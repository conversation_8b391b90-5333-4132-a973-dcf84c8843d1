package redis

type Config struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	Expiry   int    `mapstructure:"expiry"` // 小时
	Admin    struct {
		DB     int    `mapstructure:"db"`
		Prefix string `mapstructure:"prefix"`
	} `mapstructure:"admin"`
	Like struct {
		DB     int    `mapstructure:"db"`
		Prefix string `mapstructure:"prefix"`
	} `mapstructure:"like"`
	Collect struct {
		DB     int    `mapstructure:"db"`
		Prefix string `mapstructure:"prefix"`
	} `mapstructure:"collect"`
	Follow struct {
		DB     int    `mapstructure:"db"`
		Prefix string `mapstructure:"prefix"`
	} `mapstructure:"follow"`
}
