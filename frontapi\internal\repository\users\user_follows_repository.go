package users

import (
	"context"
	"frontapi/internal/models"
	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
	"frontapi/pkg/types"
	"time"

	"gorm.io/gorm"
)

//用户关注数据访问接口

type UserFollowsRepository interface {
	base.ExtendedRepository[users.UserFollows]
	//添加关注
	AddFollow(userId string, followId string) bool
	//取消关注
	CancelFollow(userId string, followId string) bool
	//检查是否关注
	IsFollowing(ctx context.Context, userID string, followID string) (bool, error)

	// 根据时间区间统计用户粉丝总数
	GetFollowersCountByTimeRange(ctx context.Context, userID string, timeType string, startTime, endTime time.Time) (int64, error)

	// 根据时间区间获取粉丝增长统计数据
	GetFollowersGrowthStats(ctx context.Context, userID string, timeType string, startTime, endTime time.Time) ([]*users.FollowersGrowthStat, error)
	// 获取用户关注
	GetUserFollow(ctx context.Context, userID, followID string) (*users.User, error)
}
type userFollowsRepository struct {
	base.ExtendedRepository[users.UserFollows]
}

func NewUserFollowsRepository(db *gorm.DB) UserFollowsRepository {
	return &userFollowsRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserFollows](db),
	}
}

func (r *userFollowsRepository) AddFollow(userId string, followId string) bool {
	ctx := context.Background()
	//检查是否已经关注
	count, _ := r.Count(ctx, map[string]interface{}{
		"user_id":   userId,
		"follow_id": followId,
	})
	if count > 0 {
		return false
	}
	//获取被关注用户的信息
	var user users.User
	r.GetDBWithContext(ctx).Model(&users.User{}).Where("id =?", followId).First(&user)
	//获取关注者的信息
	var follower users.User
	r.GetDBWithContext(ctx).Model(&users.User{}).Where("id =?", userId).First(&follower)
	// 处理null类型字段
	userAvatarStr := ""
	if user.Avatar.Valid {
		userAvatarStr = user.Avatar.String
	}

	followerAvatarStr := ""
	if follower.Avatar.Valid {
		followerAvatarStr = follower.Avatar.String
	}

	followerBioStr := ""
	if follower.Bio.Valid {
		followerBioStr = follower.Bio.String
	}

	// 处理用户和关注者的昵称
	userNicknameStr := ""
	if user.Nickname.Valid {
		userNicknameStr = user.Nickname.String
	}

	followerNicknameStr := ""
	if follower.Nickname.Valid {
		followerNicknameStr = follower.Nickname.String
	}

	// 处理关注者的用户名
	followerUsernameStr := ""
	if follower.Username.Valid {
		followerUsernameStr = follower.Username.String
	}

	//添加关注记录
	follow := users.UserFollows{
		BaseModelStruct: models.BaseModelStruct{
			ID:        base.GenerateID(),
			Status:    1,
			CreatedAt: types.JSONTime(time.Now()),
			UpdatedAt: types.JSONTime(time.Now()),
		},
		UserID:           userId,
		UserNickname:     userNicknameStr,
		UserAvatar:       userAvatarStr,
		FollowedID:       followId,
		FollowedUsername: followerUsernameStr,
		FollowedNickname: followerNicknameStr,
		FollowedAvatar:   followerAvatarStr,
		FollowedBio:      followerBioStr,
	}

	return r.Create(ctx, &follow) == nil
}
func (r *userFollowsRepository) CancelFollow(userId string, followId string) bool {
	ctx := context.Background()
	//检查是否已经关注
	count, _ := r.Count(ctx, map[string]interface{}{
		"user_id":   userId,
		"follow_id": followId,
	})
	if count == 0 {
		return false
	}
	//删除关注记录
	return r.GetDBWithContext(ctx).Where("user_id =? AND follow_id =?", userId, followId).Delete(&users.UserFollows{}).Error == nil
}

// 检查是否关注
func (r *userFollowsRepository) IsFollowing(ctx context.Context, userID string, followID string) (bool, error) {
	var count int64
	err := r.GetDB().WithContext(ctx).Model(&users.UserFollows{}).
		Where("user_id = ? AND follow_id = ?", userID, followID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetFollowersCountByTimeRange 根据时间区间统计用户粉丝总数
func (r *userFollowsRepository) GetFollowersCountByTimeRange(ctx context.Context, userID string, timeType string, startTime, endTime time.Time) (int64, error) {
	var count int64

	query := r.GetDB().WithContext(ctx).Model(&users.UserFollows{}).
		Where("followed_id = ?", userID).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime)

	err := query.Count(&count).Error
	if err != nil {
		return 0, err
	}

	return count, nil
}

// GetFollowersGrowthStats 根据时间区间获取粉丝增长统计数据
func (r *userFollowsRepository) GetFollowersGrowthStats(ctx context.Context, userID string, timeType string, startTime, endTime time.Time) ([]*users.FollowersGrowthStat, error) {
	var stats []*users.FollowersGrowthStat

	var dateFormat string
	var groupBy string

	switch timeType {
	case "day":
		dateFormat = "%Y-%m-%d"
		groupBy = "DATE(created_at)"
	case "week":
		dateFormat = "%Y-%u"
		groupBy = "YEARWEEK(created_at, 1)"
	case "month":
		dateFormat = "%Y-%m"
		groupBy = "DATE_FORMAT(created_at, '%Y-%m')"
	case "quarter":
		dateFormat = "%Y-Q%q"
		groupBy = "CONCAT(YEAR(created_at), '-Q', QUARTER(created_at))"
	default:
		dateFormat = "%Y-%m-%d"
		groupBy = "DATE(created_at)"
	}

	query := r.GetDB().WithContext(ctx).Model(&users.UserFollows{}).
		Select("DATE_FORMAT(created_at, ?) as time_period, COUNT(*) as count", dateFormat).
		Where("followed_id = ?", userID).
		Where("created_at >= ? AND created_at <= ?", startTime, endTime).
		Group(groupBy).
		Order("time_period ASC")

	err := query.Scan(&stats).Error
	if err != nil {
		return nil, err
	}

	return stats, nil
}

// 获取用户信息,以及是否follow
func (r *userFollowsRepository) GetUserFollow(ctx context.Context, userID, followID string) (*users.User, error) {
	query := "select user.*,follows.user_id as is_followed from ly_users as user left join ly_user_follows as follows on follows.followed_id = user.id where user.id=? and follows.user_id=?"
	var user users.User
	err := r.GetDB().WithContext(ctx).Raw(query, followID, userID).Scan(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}
