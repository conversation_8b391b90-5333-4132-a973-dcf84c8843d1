<template>
  <div class="mock-data-generator">
    <el-form :inline="true" class="generator-form">
      <el-form-item label="生成数量">
        <el-input-number 
          v-model="mockConfig.count" 
          :min="1" 
          :max="1000" 
          style="width: 120px;"
        />
      </el-form-item>

      <el-form-item>
        <el-button 
          type="primary" 
          @click="handleGenerateMockData" 
          :loading="generating"
          :disabled="!selectedTable"
        >
          生成Mock数据
        </el-button>
        <el-button 
          type="success" 
          @click="handleInsertToDatabase" 
          :disabled="!mockData.length"
          :loading="inserting"
        >
          插入数据库
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { insertMockData as insertMockDataAPI } from '@/service/api/database/database';
import { createMockDataGenerator } from '@/utils/mockDataGenerator';
import type { TableDetailResponse } from '@/types/database';

// Props
interface Props {
  selectedTable: string;
  mockData: Record<string, any>[];
  mockRules: Record<string, string>;
  excludedFKs: string[];
  excludedColumns: string[];
  tableDetail: TableDetailResponse | null;
}

// Emits
interface Emits {
  (e: 'mock-data-generated', data: { mockData: Record<string, any>[], sql: string }): void;
  (e: 'data-inserted'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const generating = ref(false);
const inserting = ref(false);

// Mock配置
const mockConfig = reactive({
  count: 10
});

// 生成Mock数据
const handleGenerateMockData = async () => {
  if (!props.selectedTable) {
    ElMessage.warning('请先选择表');
    return;
  }

  if (!props.tableDetail) {
    ElMessage.warning('表结构信息未加载');
    return;
  }

  try {
    generating.value = true;
    
    console.log('开始生成Mock数据...');
    console.log('表详情:', props.tableDetail);
    console.log('Mock规则:', props.mockRules);
    console.log('排除的外键:', props.excludedFKs);
    console.log('排除的列:', props.excludedColumns);

    // 创建Mock数据生成器
    const generator = createMockDataGenerator(props.tableDetail, props.selectedTable);
    
    // 使用传入的排除列列表
    const excludedColumns = [...props.excludedColumns];

    // 生成数据
    const result = generator.generate({
      count: mockConfig.count,
      mockRules: props.mockRules,
      excludedColumns
    });

    console.log('生成的Mock数据:', result);

    emit('mock-data-generated', {
      mockData: result.data,
      sql: result.sql
    });
    
    ElMessage.success(`成功生成 ${result.count} 条Mock数据`);
  } catch (error) {
    console.error('生成Mock数据失败:', error);
    ElMessage.error('生成Mock数据失败: ' + (error as Error).message);
  } finally {
    generating.value = false;
  }
};

// 插入数据库
const handleInsertToDatabase = async () => {
  if (!props.mockData.length) {
    ElMessage.warning('没有可插入的数据');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要将 ${props.mockData.length} 条数据插入到表 ${props.selectedTable} 中吗？`,
      '确认插入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    inserting.value = true;
    
    await insertMockDataAPI({
      data: {
        table_name: props.selectedTable,
        data: props.mockData
      }
    });
    
    ElMessage.success('数据插入成功');
    emit('data-inserted');
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('数据插入失败:', error);
      ElMessage.error('数据插入失败');
    }
  } finally {
    inserting.value = false;
  }
};

// 暴露配置给父组件
defineExpose({
  mockConfig
});
</script>

<style scoped>
.mock-data-generator {
  margin-bottom: 20px;
}

.generator-form {
  margin: 0;
}
</style> 