package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserPictureCollectionRepository 用户图片收藏数据访问接口
type UserPictureCollectionRepository interface {
	base.ExtendedRepository[users.UserPictureCollection]
}

// userPictureCollectionRepository 用户图片收藏数据访问实现
type userPictureCollectionRepository struct {
	base.ExtendedRepository[users.UserPictureCollection]
}

// NewUserPictureCollectionRepository 创建用户图片收藏仓库实例
func NewUserPictureCollectionRepository(db *gorm.DB) UserPictureCollectionRepository {
	return &userPictureCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserPictureCollection](db),
	}
}
