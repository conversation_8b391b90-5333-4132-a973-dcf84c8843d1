/**
 * 对象工具函数
 */

/**
 * 对象路径类型
 */
export type ObjectPath = string | (string | number)[]

/**
 * 对象合并选项
 */
export interface MergeOptions {
  deep?: boolean // 是否深度合并
  arrays?: 'replace' | 'merge' | 'concat' // 数组合并策略
  customMerge?: (target: any, source: any, key: string) => any // 自定义合并函数
}

/**
 * 对象比较选项
 */
export interface CompareOptions {
  deep?: boolean // 是否深度比较
  ignoreKeys?: string[] // 忽略的键
  customCompare?: (a: any, b: any, key: string) => boolean | undefined // 自定义比较函数
}

/**
 * 对象验证规则
 */
export interface ObjectValidationRule {
  required?: boolean // 是否必填
  type?: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'function' // 类型验证
  custom?: (value: any) => boolean // 自定义验证函数
  message?: string // 错误消息
}

/**
 * 对象验证结果
 */
export interface ObjectValidationResult {
  valid: boolean
  errors: Record<string, string[]>
}

/**
 * 对象转换选项
 */
export interface TransformOptions {
  keyTransform?: (key: string) => string // 键转换函数
  valueTransform?: (value: any, key: string) => any // 值转换函数
  deep?: boolean // 是否深度转换
  includeArrays?: boolean // 是否包含数组
}

/**
 * 对象工具类
 */
export class ObjectUtils {
  /**
   * 检查是否为对象
   */
  static isObject(value: any): value is object {
    return value !== null && typeof value === 'object' && !Array.isArray(value)
  }

  /**
   * 检查是否为纯对象
   */
  static isPlainObject(value: any): value is Record<string, any> {
    if (!this.isObject(value)) {
      return false
    }
    
    // 检查是否为普通对象（不是类实例）
    const proto = Object.getPrototypeOf(value)
    return proto === null || proto === Object.prototype
  }

  /**
   * 检查是否为空对象
   */
  static isEmpty(obj: any): boolean {
    if (!this.isObject(obj)) {
      return true
    }
    
    return Object.keys(obj).length === 0
  }

  /**
   * 检查是否不为空对象
   */
  static isNotEmpty(obj: any): boolean {
    return !this.isEmpty(obj)
  }

  /**
   * 获取对象键数组
   */
  static keys(obj: any): string[] {
    if (!this.isObject(obj)) {
      return []
    }
    
    return Object.keys(obj)
  }

  /**
   * 获取对象值数组
   */
  static values(obj: any): any[] {
    if (!this.isObject(obj)) {
      return []
    }
    
    return Object.values(obj)
  }

  /**
   * 获取对象键值对数组
   */
  static entries(obj: any): [string, any][] {
    if (!this.isObject(obj)) {
      return []
    }
    
    return Object.entries(obj)
  }

  /**
   * 从键值对数组创建对象
   */
  static fromEntries(entries: [string, any][]): Record<string, any> {
    if (!Array.isArray(entries)) {
      return {}
    }
    
    return Object.fromEntries(entries)
  }

  /**
   * 获取对象大小（键的数量）
   */
  static size(obj: any): number {
    return this.keys(obj).length
  }

  /**
   * 检查对象是否包含指定键
   */
  static hasKey(obj: any, key: string): boolean {
    if (!this.isObject(obj)) {
      return false
    }
    
    return Object.prototype.hasOwnProperty.call(obj, key)
  }

  /**
   * 检查对象是否包含指定值
   */
  static hasValue(obj: any, value: any): boolean {
    if (!this.isObject(obj)) {
      return false
    }
    
    return this.values(obj).includes(value)
  }

  /**
   * 获取对象属性值（支持路径）
   */
  static get(obj: any, path: ObjectPath, defaultValue?: any): any {
    if (!this.isObject(obj)) {
      return defaultValue
    }
    
    const keys = this.parsePath(path)
    let current = obj
    
    for (const key of keys) {
      if (current === null || current === undefined || !this.hasKey(current, String(key))) {
        return defaultValue
      }
      current = current[key]
    }
    
    return current
  }

  /**
   * 设置对象属性值（支持路径）
   */
  static set(obj: any, path: ObjectPath, value: any): any {
    if (!this.isObject(obj)) {
      return obj
    }
    
    const keys = this.parsePath(path)
    let current = obj
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      
      if (!this.hasKey(current, String(key)) || !this.isObject(current[key])) {
        current[key] = {}
      }
      
      current = current[key]
    }
    
    current[keys[keys.length - 1]] = value
    return obj
  }

  /**
   * 删除对象属性（支持路径）
   */
  static unset(obj: any, path: ObjectPath): boolean {
    if (!this.isObject(obj)) {
      return false
    }
    
    const keys = this.parsePath(path)
    
    if (keys.length === 0) {
      return false
    }
    
    if (keys.length === 1) {
      if (this.hasKey(obj, String(keys[0]))) {
        delete obj[keys[0]]
        return true
      }
      return false
    }
    
    const parentPath = keys.slice(0, -1)
    const parent = this.get(obj, parentPath)
    
    if (!this.isObject(parent)) {
      return false
    }
    
    const lastKey = keys[keys.length - 1]
    
    if (this.hasKey(parent, String(lastKey))) {
      delete parent[lastKey]
      return true
    }
    
    return false
  }

  /**
   * 检查路径是否存在
   */
  static has(obj: any, path: ObjectPath): boolean {
    if (!this.isObject(obj)) {
      return false
    }
    
    const keys = this.parsePath(path)
    let current = obj
    
    for (const key of keys) {
      if (!this.hasKey(current, String(key))) {
        return false
      }
      current = current[key]
    }
    
    return true
  }

  /**
   * 解析路径
   */
  private static parsePath(path: ObjectPath): (string | number)[] {
    if (Array.isArray(path)) {
      return path
    }
    
    if (typeof path === 'string') {
      return path.split('.').map(key => {
        // 尝试转换为数字（用于数组索引）
        const num = Number(key)
        return isNaN(num) ? key : num
      })
    }
    
    return []
  }

  /**
   * 深拷贝对象
   */
  static clone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime()) as T
    }
    
    if (obj instanceof RegExp) {
      return new RegExp(obj) as T
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.clone(item)) as T
    }
    
    if (this.isPlainObject(obj)) {
      const cloned = {} as T
      
      for (const key in obj) {
        if (this.hasKey(obj, key)) {
          ;(cloned as any)[key] = this.clone((obj as any)[key])
        }
      }
      
      return cloned
    }
    
    // 对于其他类型的对象，尝试使用JSON序列化
    try {
      return JSON.parse(JSON.stringify(obj))
    } catch {
      return obj
    }
  }

  /**
   * 浅拷贝对象
   */
  static copy<T>(obj: T): T {
    if (!this.isObject(obj)) {
      return obj
    }
    
    if (Array.isArray(obj)) {
      return [...obj] as T
    }
    
    return { ...obj } as T
  }

  /**
   * 合并对象
   */
  static merge<T extends Record<string, any>>(
    target: T,
    ...sources: Partial<T>[]
  ): T
  static merge<T extends Record<string, any>>(
    target: T,
    source: Partial<T>,
    options: MergeOptions
  ): T
  static merge<T extends Record<string, any>>(
    target: T,
    ...args: any[]
  ): T {
    if (!this.isObject(target)) {
      return target
    }
    
    let sources: Partial<T>[]
    let options: MergeOptions = { deep: true, arrays: 'replace' }
    
    // 解析参数
    if (args.length === 2 && this.isPlainObject(args[1]) && !this.isPlainObject(args[0])) {
      sources = [args[0]]
      options = { ...options, ...args[1] }
    } else {
      sources = args.filter(arg => this.isObject(arg))
    }
    
    const result = this.copy(target)
    
    for (const source of sources) {
      this.mergeObject(result, source, options)
    }
    
    return result
  }

  /**
   * 内部合并函数
   */
  private static mergeObject(
    target: any,
    source: any,
    options: MergeOptions
  ): void {
    const { deep = true, arrays = 'replace', customMerge } = options
    
    for (const key in source) {
      if (!this.hasKey(source, key)) {
        continue
      }
      
      const sourceValue = source[key]
      const targetValue = target[key]
      
      // 自定义合并
      if (customMerge) {
        const customResult = customMerge(targetValue, sourceValue, key)
        if (customResult !== undefined) {
          target[key] = customResult
          continue
        }
      }
      
      // 数组处理
      if (Array.isArray(sourceValue)) {
        if (arrays === 'replace') {
          target[key] = deep ? this.clone(sourceValue) : [...sourceValue]
        } else if (arrays === 'merge' && Array.isArray(targetValue)) {
          target[key] = [...targetValue, ...sourceValue]
        } else if (arrays === 'concat' && Array.isArray(targetValue)) {
          target[key] = targetValue.concat(sourceValue)
        } else {
          target[key] = deep ? this.clone(sourceValue) : [...sourceValue]
        }
        continue
      }
      
      // 对象处理
      if (deep && this.isPlainObject(sourceValue)) {
        if (!this.isPlainObject(targetValue)) {
          target[key] = {}
        }
        this.mergeObject(target[key], sourceValue, options)
      } else {
        target[key] = deep ? this.clone(sourceValue) : sourceValue
      }
    }
  }

  /**
   * 对象比较
   */
  static equals(obj1: any, obj2: any, options: CompareOptions = {}): boolean {
    const { deep = true, ignoreKeys = [], customCompare } = options
    
    // 基本类型比较
    if (obj1 === obj2) {
      return true
    }
    
    // 类型检查
    if (typeof obj1 !== typeof obj2) {
      return false
    }
    
    // null 和 undefined 检查
    if (obj1 === null || obj2 === null || obj1 === undefined || obj2 === undefined) {
      return obj1 === obj2
    }
    
    // 数组比较
    if (Array.isArray(obj1) && Array.isArray(obj2)) {
      if (obj1.length !== obj2.length) {
        return false
      }
      
      for (let i = 0; i < obj1.length; i++) {
        if (deep) {
          if (!this.equals(obj1[i], obj2[i], options)) {
            return false
          }
        } else {
          if (obj1[i] !== obj2[i]) {
            return false
          }
        }
      }
      
      return true
    }
    
    // 对象比较
    if (this.isObject(obj1) && this.isObject(obj2)) {
      const keys1 = this.keys(obj1).filter(key => !ignoreKeys.includes(key))
      const keys2 = this.keys(obj2).filter(key => !ignoreKeys.includes(key))
      
      if (keys1.length !== keys2.length) {
        return false
      }
      
      for (const key of keys1) {
        if (!keys2.includes(key)) {
          return false
        }
        
        // 自定义比较
        if (customCompare) {
          const customResult = customCompare(obj1[key], obj2[key], key)
          if (customResult !== undefined) {
            if (!customResult) {
              return false
            }
            continue
          }
        }
        
        if (deep) {
          if (!this.equals(obj1[key], obj2[key], options)) {
            return false
          }
        } else {
          if (obj1[key] !== obj2[key]) {
            return false
          }
        }
      }
      
      return true
    }
    
    return false
  }

  /**
   * 选择对象属性
   */
  static pick<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Pick<T, K> {
    if (!this.isObject(obj)) {
      return {} as Pick<T, K>
    }
    
    const result = {} as Pick<T, K>
    
    for (const key of keys) {
      if (this.hasKey(obj, String(key))) {
        result[key] = obj[key]
      }
    }
    
    return result
  }

  /**
   * 排除对象属性
   */
  static omit<T extends Record<string, any>, K extends keyof T>(
    obj: T,
    keys: K[]
  ): Omit<T, K> {
    if (!this.isObject(obj)) {
      return {} as Omit<T, K>
    }
    
    const result = { ...obj } as Omit<T, K>
    
    for (const key of keys) {
      delete (result as any)[key]
    }
    
    return result
  }

  /**
   * 过滤对象属性
   */
  static filter<T extends Record<string, any>>(
    obj: T,
    predicate: (value: any, key: string) => boolean
  ): Partial<T> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Partial<T> = {}
    
    for (const [key, value] of this.entries(obj)) {
      if (predicate(value, key)) {
        result[key as keyof T] = value
      }
    }
    
    return result
  }

  /**
   * 映射对象属性
   */
  static map<T extends Record<string, any>, U>(
    obj: T,
    mapper: (value: any, key: string) => U
  ): Record<string, U> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, U> = {}
    
    for (const [key, value] of this.entries(obj)) {
      result[key] = mapper(value, key)
    }
    
    return result
  }

  /**
   * 对象属性映射（键值都可以改变）
   */
  static mapEntries<T extends Record<string, any>, U>(
    obj: T,
    mapper: (value: any, key: string) => [string, U]
  ): Record<string, U> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, U> = {}
    
    for (const [key, value] of this.entries(obj)) {
      const [newKey, newValue] = mapper(value, key)
      result[newKey] = newValue
    }
    
    return result
  }

  /**
   * 对象归约
   */
  static reduce<T extends Record<string, any>, U>(
    obj: T,
    reducer: (acc: U, value: any, key: string) => U,
    initialValue: U
  ): U {
    if (!this.isObject(obj)) {
      return initialValue
    }
    
    let result = initialValue
    
    for (const [key, value] of this.entries(obj)) {
      result = reducer(result, value, key)
    }
    
    return result
  }

  /**
   * 检查对象是否满足所有条件
   */
  static every<T extends Record<string, any>>(
    obj: T,
    predicate: (value: any, key: string) => boolean
  ): boolean {
    if (!this.isObject(obj)) {
      return true
    }
    
    for (const [key, value] of this.entries(obj)) {
      if (!predicate(value, key)) {
        return false
      }
    }
    
    return true
  }

  /**
   * 检查对象是否满足任一条件
   */
  static some<T extends Record<string, any>>(
    obj: T,
    predicate: (value: any, key: string) => boolean
  ): boolean {
    if (!this.isObject(obj)) {
      return false
    }
    
    for (const [key, value] of this.entries(obj)) {
      if (predicate(value, key)) {
        return true
      }
    }
    
    return false
  }

  /**
   * 查找对象属性
   */
  static find<T extends Record<string, any>>(
    obj: T,
    predicate: (value: any, key: string) => boolean
  ): [string, any] | undefined {
    if (!this.isObject(obj)) {
      return undefined
    }
    
    for (const [key, value] of this.entries(obj)) {
      if (predicate(value, key)) {
        return [key, value]
      }
    }
    
    return undefined
  }

  /**
   * 查找对象属性键
   */
  static findKey<T extends Record<string, any>>(
    obj: T,
    predicate: (value: any, key: string) => boolean
  ): string | undefined {
    const found = this.find(obj, predicate)
    return found ? found[0] : undefined
  }

  /**
   * 查找对象属性值
   */
  static findValue<T extends Record<string, any>>(
    obj: T,
    predicate: (value: any, key: string) => boolean
  ): any {
    const found = this.find(obj, predicate)
    return found ? found[1] : undefined
  }

  /**
   * 反转对象键值
   */
  static invert<T extends Record<string, string | number>>(
    obj: T
  ): Record<string, string> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, string> = {}
    
    for (const [key, value] of this.entries(obj)) {
      result[String(value)] = key
    }
    
    return result
  }

  /**
   * 扁平化对象
   */
  static flatten(
    obj: any,
    separator = '.',
    prefix = ''
  ): Record<string, any> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, any> = {}
    
    for (const [key, value] of this.entries(obj)) {
      const newKey = prefix ? `${prefix}${separator}${key}` : key
      
      if (this.isPlainObject(value)) {
        Object.assign(result, this.flatten(value, separator, newKey))
      } else {
        result[newKey] = value
      }
    }
    
    return result
  }

  /**
   * 展开扁平化对象
   */
  static unflatten(
    obj: Record<string, any>,
    separator = '.'
  ): Record<string, any> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, any> = {}
    
    for (const [key, value] of this.entries(obj)) {
      this.set(result, key.split(separator), value)
    }
    
    return result
  }

  /**
   * 对象转换
   */
  static transform<T extends Record<string, any>>(
    obj: T,
    options: TransformOptions = {}
  ): Record<string, any> {
    const {
      keyTransform,
      valueTransform,
      deep = false,
      includeArrays = false
    } = options
    
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, any> = {}
    
    for (const [key, value] of this.entries(obj)) {
      let newKey = key
      let newValue = value
      
      // 键转换
      if (keyTransform) {
        newKey = keyTransform(key)
      }
      
      // 值转换
      if (valueTransform) {
        newValue = valueTransform(value, key)
      }
      
      // 深度转换
      if (deep) {
        if (this.isPlainObject(newValue)) {
          newValue = this.transform(newValue, options)
        } else if (includeArrays && Array.isArray(newValue)) {
          newValue = newValue.map(item => 
            this.isPlainObject(item) ? this.transform(item, options) : item
          )
        }
      }
      
      result[newKey] = newValue
    }
    
    return result
  }

  /**
   * 对象验证
   */
  static validate(
    obj: any,
    rules: Record<string, ObjectValidationRule | ObjectValidationRule[]>
  ): ObjectValidationResult {
    const errors: Record<string, string[]> = {}
    
    for (const [key, rule] of Object.entries(rules)) {
      const ruleArray = Array.isArray(rule) ? rule : [rule]
      const value = this.get(obj, key)
      const keyErrors: string[] = []
      
      for (const singleRule of ruleArray) {
        // 必填验证
        if (singleRule.required && (value === undefined || value === null)) {
          keyErrors.push(singleRule.message || `${key} 是必填项`)
          continue
        }
        
        // 如果值为空且不是必填，跳过其他验证
        if ((value === undefined || value === null) && !singleRule.required) {
          continue
        }
        
        // 类型验证
        if (singleRule.type) {
          const actualType = Array.isArray(value) ? 'array' : typeof value
          if (actualType !== singleRule.type) {
            keyErrors.push(singleRule.message || `${key} 类型应为 ${singleRule.type}`)
          }
        }
        
        // 自定义验证
        if (singleRule.custom && !singleRule.custom(value)) {
          keyErrors.push(singleRule.message || `${key} 验证失败`)
        }
      }
      
      if (keyErrors.length > 0) {
        errors[key] = keyErrors
      }
    }
    
    return {
      valid: Object.keys(errors).length === 0,
      errors
    }
  }

  /**
   * 获取对象所有路径
   */
  static paths(obj: any, prefix = ''): string[] {
    if (!this.isObject(obj)) {
      return []
    }
    
    const result: string[] = []
    
    for (const [key, value] of this.entries(obj)) {
      const path = prefix ? `${prefix}.${key}` : key
      result.push(path)
      
      if (this.isPlainObject(value)) {
        result.push(...this.paths(value, path))
      }
    }
    
    return result
  }

  /**
   * 获取对象所有叶子节点路径和值
   */
  static leaves(obj: any, prefix = ''): Record<string, any> {
    if (!this.isObject(obj)) {
      return {}
    }
    
    const result: Record<string, any> = {}
    
    for (const [key, value] of this.entries(obj)) {
      const path = prefix ? `${prefix}.${key}` : key
      
      if (this.isPlainObject(value)) {
        Object.assign(result, this.leaves(value, path))
      } else {
        result[path] = value
      }
    }
    
    return result
  }

  /**
   * 对象序列化为查询字符串
   */
  static toQueryString(obj: any, prefix = ''): string {
    if (!this.isObject(obj)) {
      return ''
    }
    
    const params: string[] = []
    
    for (const [key, value] of this.entries(obj)) {
      const paramKey = prefix ? `${prefix}[${key}]` : key
      
      if (value === null || value === undefined) {
        continue
      }
      
      if (Array.isArray(value)) {
        value.forEach((item, index) => {
          if (this.isObject(item)) {
            params.push(this.toQueryString(item, `${paramKey}[${index}]`))
          } else {
            params.push(`${paramKey}[${index}]=${encodeURIComponent(String(item))}`)
          }
        })
      } else if (this.isObject(value)) {
        params.push(this.toQueryString(value, paramKey))
      } else {
        params.push(`${paramKey}=${encodeURIComponent(String(value))}`)
      }
    }
    
    return params.filter(param => param.length > 0).join('&')
  }

  /**
   * 从查询字符串解析对象
   */
  static fromQueryString(queryString: string): Record<string, any> {
    if (typeof queryString !== 'string' || !queryString.trim()) {
      return {}
    }
    
    const result: Record<string, any> = {}
    const params = queryString.replace(/^\?/, '').split('&')
    
    for (const param of params) {
      const [key, value] = param.split('=')
      
      if (!key) {
        continue
      }
      
      const decodedKey = decodeURIComponent(key)
      const decodedValue = value ? decodeURIComponent(value) : ''
      
      this.set(result, decodedKey.replace(/\[(\d+)\]/g, '.$1').split('.'), decodedValue)
    }
    
    return result
  }

  /**
   * 对象冻结（深度）
   */
  static freeze<T>(obj: T): T {
    if (!this.isObject(obj)) {
      return obj
    }
    
    Object.freeze(obj)
    
    for (const value of this.values(obj)) {
      if (this.isObject(value)) {
        this.freeze(value)
      }
    }
    
    return obj
  }

  /**
   * 检查对象是否被冻结
   */
  static isFrozen(obj: any): boolean {
    if (!this.isObject(obj)) {
      return true
    }
    
    return Object.isFrozen(obj)
  }

  /**
   * 对象密封（深度）
   */
  static seal<T>(obj: T): T {
    if (!this.isObject(obj)) {
      return obj
    }
    
    Object.seal(obj)
    
    for (const value of this.values(obj)) {
      if (this.isObject(value)) {
        this.seal(value)
      }
    }
    
    return obj
  }

  /**
   * 检查对象是否被密封
   */
  static isSealed(obj: any): boolean {
    if (!this.isObject(obj)) {
      return true
    }
    
    return Object.isSealed(obj)
  }
}

/**
 * 对象管理器类
 */
export class ObjectManager<T extends Record<string, any>> {
  private data: T

  constructor(initialData: T = {} as T) {
    this.data = ObjectUtils.clone(initialData)
  }

  /**
   * 获取数据
   */
  getData(): T {
    return ObjectUtils.clone(this.data)
  }

  /**
   * 设置数据
   */
  setData(data: T): this {
    this.data = ObjectUtils.clone(data)
    return this
  }

  /**
   * 获取属性值
   */
  get(path: ObjectPath, defaultValue?: any): any {
    return ObjectUtils.get(this.data, path, defaultValue)
  }

  /**
   * 设置属性值
   */
  set(path: ObjectPath, value: any): this {
    ObjectUtils.set(this.data, path, value)
    return this
  }

  /**
   * 删除属性
   */
  unset(path: ObjectPath): this {
    ObjectUtils.unset(this.data, path)
    return this
  }

  /**
   * 检查路径是否存在
   */
  has(path: ObjectPath): boolean {
    return ObjectUtils.has(this.data, path)
  }

  /**
   * 合并数据
   */
  merge(source: Partial<T>, options?: MergeOptions): this {
    this.data = ObjectUtils.merge(this.data, source, options)
    return this
  }

  /**
   * 选择属性
   */
  pick<K extends keyof T>(keys: K[]): Pick<T, K> {
    return ObjectUtils.pick(this.data, keys)
  }

  /**
   * 排除属性
   */
  omit<K extends keyof T>(keys: K[]): Omit<T, K> {
    return ObjectUtils.omit(this.data, keys)
  }

  /**
   * 过滤属性
   */
  filter(predicate: (value: any, key: string) => boolean): Partial<T> {
    return ObjectUtils.filter(this.data, predicate)
  }

  /**
   * 映射属性
   */
  map<U>(mapper: (value: any, key: string) => U): Record<string, U> {
    return ObjectUtils.map(this.data, mapper)
  }

  /**
   * 转换对象
   */
  transform(options: TransformOptions = {}): Record<string, any> {
    return ObjectUtils.transform(this.data, options)
  }

  /**
   * 验证数据
   */
  validate(rules: Record<string, ObjectValidationRule | ObjectValidationRule[]>): ObjectValidationResult {
    return ObjectUtils.validate(this.data, rules)
  }

  /**
   * 获取所有路径
   */
  paths(): string[] {
    return ObjectUtils.paths(this.data)
  }

  /**
   * 获取叶子节点
   */
  leaves(): Record<string, any> {
    return ObjectUtils.leaves(this.data)
  }

  /**
   * 扁平化
   */
  flatten(separator = '.'): Record<string, any> {
    return ObjectUtils.flatten(this.data, separator)
  }

  /**
   * 清空数据
   */
  clear(): this {
    this.data = {} as T
    return this
  }

  /**
   * 检查是否为空
   */
  isEmpty(): boolean {
    return ObjectUtils.isEmpty(this.data)
  }

  /**
   * 获取大小
   */
  size(): number {
    return ObjectUtils.size(this.data)
  }

  /**
   * 获取键数组
   */
  keys(): string[] {
    return ObjectUtils.keys(this.data)
  }

  /**
   * 获取值数组
   */
  values(): any[] {
    return ObjectUtils.values(this.data)
  }

  /**
   * 获取键值对数组
   */
  entries(): [string, any][] {
    return ObjectUtils.entries(this.data)
  }

  /**
   * 克隆
   */
  clone(): ObjectManager<T> {
    return new ObjectManager(this.data)
  }

  /**
   * 转换为JSON字符串
   */
  toJSON(): string {
    return JSON.stringify(this.data)
  }

  /**
   * 转换为查询字符串
   */
  toQueryString(): string {
    return ObjectUtils.toQueryString(this.data)
  }
}

/**
 * 创建对象管理器
 */
export function createObjectManager<T extends Record<string, any>>(initialData?: T): ObjectManager<T> {
  return new ObjectManager(initialData)
}

// 导出工具实例
export const objectUtils = ObjectUtils

// 导出快捷方法
export const {
  isObject,
  isPlainObject,
  isEmpty,
  isNotEmpty,
  keys,
  values,
  entries,
  fromEntries,
  size,
  hasKey,
  hasValue,
  get,
  set,
  unset,
  has,
  clone,
  copy,
  merge,
  equals,
  pick,
  omit,
  filter,
  map,
  mapEntries,
  reduce,
  every,
  some,
  find,
  findKey,
  findValue,
  invert,
  flatten,
  unflatten,
  transform,
  validate,
  paths,
  leaves,
  toQueryString,
  fromQueryString,
  freeze,
  isFrozen,
  seal,
  isSealed
} = ObjectUtils