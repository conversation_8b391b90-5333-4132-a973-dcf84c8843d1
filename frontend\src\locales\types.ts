/**
 * 国际化语言类型定义
 */

import { SUPPORTED_LOCALES } from '@/config/locales.config';

// 语言代码类型
export type LocaleType = typeof SUPPORTED_LOCALES[number]['code'];

// 定义单个语言模块的消息类型
export interface LocaleModule {
    [key: string]: string | LocaleModule;
}

// 定义整个语言包的消息类型
export interface LocaleMessages {
    [namespace: string]: LocaleModule;
}

// 语言显示名称映射类型
export type LocaleNameMap = Record<LocaleType, string>; 