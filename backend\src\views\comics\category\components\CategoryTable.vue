<template>
  <el-table
    v-loading="loading"
    :data="categoryList"
    border
    style="width: 100%"
    row-key="id"
  >
    <el-table-column type="index" width="55"></el-table-column>
    <el-table-column prop="name" label="分类名称" min-width="120" />
    <el-table-column label="封面" width="120" align="center">
      <template #default="{ row }">
        <el-image
          v-if="row.cover"
          :src="row.cover"
          :preview-src-list="[row.cover]"
          fit="cover"
          class="category-cover"
        />
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column prop="description" label="描述" min-width="150" align="center" />
    <el-table-column prop="sort_order" label="排序" width="80" align="center" />
    <el-table-column prop="status" label="状态" width="100" align="center">
      <template #default="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" width="180" align="center">
      <template #default="{ row }">
        {{ formatDate(row.created_at) }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="220" align="center">
      <template #default="{ row }">
        <el-button type="primary" link @click="$emit('edit', row)">编辑</el-button>
        <el-button type="danger" link @click="$emit('delete', row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import type { ComicsCategory } from '@/types/comics';

defineProps<{
  loading: boolean;
  categoryList: ComicsCategory[];
}>();

defineEmits(['edit', 'delete']);

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 8);
};
</script>

<style scoped>
.category-cover {
  width: 60px;
  height: 60px;
  border-radius: 4px;
}
</style>