import { request } from "../request";
import { ApiResponse } from "@/types/https";

// 文件项接口
export interface FileItem {
  name: string;
  url: string;
  path: string;
  size: number;
  type: string;
  extension: string;
  is_dir: boolean;
  modified_at: string;
  created_at: string;
}

// 文件列表响应接口
export interface FileListResponse {
  files: FileItem[];
  path: string;
  total_files: number;
  total_size: number;
  allowed_types: string[];
}

// 获取文件列表
export function getFileList(params: { 
  data: { 
    file_type: string; // 'image' | 'video' | 'all'
    path?: string;
    search?: string;
    limit?: number;
  }
}) {
  return request<ApiResponse<FileListResponse>>({
    url: '/files/list',
    method: 'post',
    data: params
  });
}

// 上传文件
export function uploadFile(params: FormData) {
  return request<ApiResponse<FileItem>>({
    url: '/files/upload',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 重命名文件
export function renameFile(params: { 
  data: { 
    file_type: string;
    old_path: string;
    new_name: string;
  }
}) {
  return request<ApiResponse<FileItem>>({
    url: '/files/rename',
    method: 'post',
    data: params
  });
}

// 删除文件
export function deleteFile(params: { 
  data: { 
    file_type: string;
    path: string;
  }
}) {
  return request<ApiResponse<any>>({
    url: '/files/delete',
    method: 'post',
    data: params
  });
}

// 创建目录
export function createDirectory(params: { 
  data: { 
    file_type: string;
    parent_path: string;
    dir_name: string;
  }
}) {
  return request<ApiResponse<FileItem>>({
    url: '/files/create-directory',
    method: 'post',
    data: params
  });
}

// 获取文件详情
export function getFileInfo(params: { 
  data: { 
    file_type: string;
    path: string;
  }
}) {
  return request<ApiResponse<FileItem>>({
    url: '/files/info',
    method: 'post',
    data: params
  });
} 