/**
 * 无限滚动指令
 * 用于实现无限滚动加载功能
 */

import type { Directive, DirectiveBinding } from 'vue'
import { throttle } from '@/core/utils'

interface InfiniteScrollOptions {
  handler: () => void | Promise<void>
  distance?: number     // 触发距离（像素）
  throttle?: number     // 节流时间（毫秒）
  disabled?: boolean    // 是否禁用
  immediate?: boolean   // 是否立即检查
  container?: string | HTMLElement // 滚动容器
  direction?: 'vertical' | 'horizontal' // 滚动方向
}

interface InfiniteScrollElement extends HTMLElement {
  _infiniteScroll?: {
    handler: () => void | Promise<void>
    options: InfiniteScrollOptions
    container: HTMLElement | Window
    throttledHandler: () => void
    isLoading: boolean
    lastScrollTop: number
    lastScrollLeft: number
  }
}

// 默认配置
const defaultOptions: Partial<InfiniteScrollOptions> = {
  distance: 100,
  throttle: 200,
  disabled: false,
  immediate: true,
  direction: 'vertical'
}

// 使用统一的节流函数

// 获取滚动容器
function getScrollContainer(el: HTMLElement, container?: string | HTMLElement): HTMLElement | Window {
  if (container) {
    if (typeof container === 'string') {
      const containerEl = document.querySelector(container) as HTMLElement
      return containerEl || window
    } else {
      return container
    }
  }
  
  // 自动查找滚动容器
  let parent = el.parentElement
  while (parent) {
    const overflow = getComputedStyle(parent).overflow
    if (overflow === 'auto' || overflow === 'scroll') {
      return parent
    }
    parent = parent.parentElement
  }
  
  return window
}

// 获取滚动信息
function getScrollInfo(container: HTMLElement | Window, direction: 'vertical' | 'horizontal') {
  if (container === window) {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
    const scrollHeight = document.documentElement.scrollHeight
    const scrollWidth = document.documentElement.scrollWidth
    const clientHeight = window.innerHeight
    const clientWidth = window.innerWidth
    
    return {
      scrollTop,
      scrollLeft,
      scrollHeight,
      scrollWidth,
      clientHeight,
      clientWidth
    }
  } else {
    const element = container as HTMLElement
    return {
      scrollTop: element.scrollTop,
      scrollLeft: element.scrollLeft,
      scrollHeight: element.scrollHeight,
      scrollWidth: element.scrollWidth,
      clientHeight: element.clientHeight,
      clientWidth: element.clientWidth
    }
  }
}

// 检查是否应该触发加载
function shouldTrigger(
  container: HTMLElement | Window,
  distance: number,
  direction: 'vertical' | 'horizontal'
): boolean {
  const scrollInfo = getScrollInfo(container, direction)
  
  if (direction === 'vertical') {
    const { scrollTop, scrollHeight, clientHeight } = scrollInfo
    return scrollTop + clientHeight >= scrollHeight - distance
  } else {
    const { scrollLeft, scrollWidth, clientWidth } = scrollInfo
    return scrollLeft + clientWidth >= scrollWidth - distance
  }
}

// 创建滚动处理函数
function createScrollHandler(el: InfiniteScrollElement): () => void {
  return async () => {
    if (!el._infiniteScroll) return
    
    const { handler, options, container, isLoading } = el._infiniteScroll
    
    // 检查是否禁用或正在加载
    if (options.disabled || isLoading) {
      return
    }
    
    // 检查是否应该触发加载
    if (!shouldTrigger(container, options.distance!, options.direction!)) {
      return
    }
    
    // 防止重复触发
    el._infiniteScroll.isLoading = true
    
    try {
      // 触发加载事件
      el.dispatchEvent(new CustomEvent('infinite-scroll:loading'))
      
      // 执行处理函数
      await handler()
      
      // 触发加载完成事件
      el.dispatchEvent(new CustomEvent('infinite-scroll:loaded'))
    } catch (error) {
      // 触发加载错误事件
      el.dispatchEvent(new CustomEvent('infinite-scroll:error', { detail: error }))
      console.error('无限滚动加载失败:', error)
    } finally {
      el._infiniteScroll.isLoading = false
    }
  }
}

// 添加滚动监听
function addScrollListener(el: InfiniteScrollElement): void {
  if (!el._infiniteScroll) return
  
  const { container, throttledHandler } = el._infiniteScroll
  
  if (container === window) {
    window.addEventListener('scroll', throttledHandler, { passive: true })
    window.addEventListener('resize', throttledHandler, { passive: true })
  } else {
    (container as HTMLElement).addEventListener('scroll', throttledHandler, { passive: true })
  }
}

// 移除滚动监听
function removeScrollListener(el: InfiniteScrollElement): void {
  if (!el._infiniteScroll) return
  
  const { container, throttledHandler } = el._infiniteScroll
  
  if (container === window) {
    window.removeEventListener('scroll', throttledHandler)
    window.removeEventListener('resize', throttledHandler)
  } else {
    (container as HTMLElement).removeEventListener('scroll', throttledHandler)
  }
}

// 无限滚动指令
export const vInfiniteScroll: Directive<InfiniteScrollElement, InfiniteScrollOptions | (() => void | Promise<void>)> = {
  mounted(el: InfiniteScrollElement, binding: DirectiveBinding<InfiniteScrollOptions | (() => void | Promise<void>)>) {
    // 解析配置
    let options: InfiniteScrollOptions
    if (typeof binding.value === 'function') {
      options = {
        ...defaultOptions,
        handler: binding.value
      } as InfiniteScrollOptions
    } else {
      options = {
        ...defaultOptions,
        ...binding.value
      }
    }
    
    // 从修饰符中获取配置
    if (binding.modifiers.disabled) {
      options.disabled = true
    }
    if (binding.modifiers.immediate === false) {
      options.immediate = false
    }
    if (binding.modifiers.horizontal) {
      options.direction = 'horizontal'
    }
    
    // 获取滚动容器
    const container = getScrollContainer(el, options.container)
    
    // 创建节流处理函数
    const scrollHandler = createScrollHandler(el)
    const throttledHandler = throttle(scrollHandler, options.throttle!)
    
    // 保存配置到元素
    el._infiniteScroll = {
      handler: options.handler,
      options,
      container,
      throttledHandler,
      isLoading: false,
      lastScrollTop: 0,
      lastScrollLeft: 0
    }
    
    // 添加滚动监听
    addScrollListener(el)
    
    // 立即检查是否需要加载
    if (options.immediate) {
      // 使用 nextTick 确保 DOM 已更新
      setTimeout(() => {
        if (shouldTrigger(container, options.distance!, options.direction!)) {
          scrollHandler()
        }
      }, 0)
    }
  },
  
  updated(el: InfiniteScrollElement, binding: DirectiveBinding<InfiniteScrollOptions | (() => void | Promise<void>)>) {
    if (!el._infiniteScroll) return
    
    // 更新配置
    let options: InfiniteScrollOptions
    if (typeof binding.value === 'function') {
      options = {
        ...el._infiniteScroll.options,
        handler: binding.value
      }
    } else {
      options = {
        ...el._infiniteScroll.options,
        ...binding.value
      }
    }
    
    // 从修饰符中获取配置
    if (binding.modifiers.disabled) {
      options.disabled = true
    }
    if (binding.modifiers.immediate === false) {
      options.immediate = false
    }
    if (binding.modifiers.horizontal) {
      options.direction = 'horizontal'
    }
    
    // 更新配置
    el._infiniteScroll.handler = options.handler
    el._infiniteScroll.options = options
    
    // 如果容器发生变化，重新绑定监听器
    const newContainer = getScrollContainer(el, options.container)
    if (newContainer !== el._infiniteScroll.container) {
      removeScrollListener(el)
      el._infiniteScroll.container = newContainer
      addScrollListener(el)
    }
  },
  
  unmounted(el: InfiniteScrollElement) {
    if (el._infiniteScroll) {
      removeScrollListener(el)
      delete el._infiniteScroll
    }
  }
}

// 工具函数
export const infiniteScrollUtils = {
  // 手动触发加载
  trigger(el: InfiniteScrollElement): void {
    if (el._infiniteScroll && !el._infiniteScroll.options.disabled) {
      el._infiniteScroll.throttledHandler()
    }
  },
  
  // 启用无限滚动
  enable(el: InfiniteScrollElement): void {
    if (el._infiniteScroll) {
      el._infiniteScroll.options.disabled = false
    }
  },
  
  // 禁用无限滚动
  disable(el: InfiniteScrollElement): void {
    if (el._infiniteScroll) {
      el._infiniteScroll.options.disabled = true
    }
  },
  
  // 检查是否正在加载
  isLoading(el: InfiniteScrollElement): boolean {
    return el._infiniteScroll?.isLoading || false
  },
  
  // 设置加载状态
  setLoading(el: InfiniteScrollElement, loading: boolean): void {
    if (el._infiniteScroll) {
      el._infiniteScroll.isLoading = loading
    }
  },
  
  // 检查是否已禁用
  isDisabled(el: InfiniteScrollElement): boolean {
    return el._infiniteScroll?.options.disabled || false
  },
  
  // 更新触发距离
  updateDistance(el: InfiniteScrollElement, distance: number): void {
    if (el._infiniteScroll) {
      el._infiniteScroll.options.distance = distance
    }
  },
  
  // 获取滚动进度（0-1）
  getScrollProgress(el: InfiniteScrollElement): number {
    if (!el._infiniteScroll) return 0
    
    const { container, options } = el._infiniteScroll
    const scrollInfo = getScrollInfo(container, options.direction!)
    
    if (options.direction === 'vertical') {
      const { scrollTop, scrollHeight, clientHeight } = scrollInfo
      return Math.min(scrollTop / (scrollHeight - clientHeight), 1)
    } else {
      const { scrollLeft, scrollWidth, clientWidth } = scrollInfo
      return Math.min(scrollLeft / (scrollWidth - clientWidth), 1)
    }
  },
  
  // 滚动到顶部
  scrollToTop(el: InfiniteScrollElement, smooth = true): void {
    if (!el._infiniteScroll) return
    
    const { container } = el._infiniteScroll
    
    if (container === window) {
      window.scrollTo({
        top: 0,
        behavior: smooth ? 'smooth' : 'auto'
      })
    } else {
      (container as HTMLElement).scrollTo({
        top: 0,
        behavior: smooth ? 'smooth' : 'auto'
      })
    }
  },
  
  // 滚动到底部
  scrollToBottom(el: InfiniteScrollElement, smooth = true): void {
    if (!el._infiniteScroll) return
    
    const { container, options } = el._infiniteScroll
    const scrollInfo = getScrollInfo(container, options.direction!)
    
    if (container === window) {
      window.scrollTo({
        top: scrollInfo.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      })
    } else {
      (container as HTMLElement).scrollTo({
        top: scrollInfo.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      })
    }
  }
}

// 导出类型
export type { InfiniteScrollOptions }