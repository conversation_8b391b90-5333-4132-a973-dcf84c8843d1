<template>
    <div class="space-layout">
        <el-container>
            <div class="header-wrapper">
                <el-header class="space-header">
                    <div class="header-top">
                        <div class="header-top-container">
                            <div class="left-section">
                                <div class="logo">
                                    <router-link to="/"><img src="@/assets/logo.svg" alt="Logo"
                                            class="logo-image" /></router-link>
                                </div>
                                <div class="region-settings">
                                  
                                </div>
                            </div>
                            <div class="search-section">
                                <el-input v-model="searchQuery" placeholder="搜索视频" class="search-input">
                                    <template #append>
                                        <el-button :icon="Search" />
                                    </template>
                                </el-input>
                            </div>
                            <div class="right-section">
                                <el-switch v-model="isDarkMode" class="theme-switch" inline-prompt :active-icon="Moon"
                                    :inactive-icon="Sunny" @change="handleThemeChange" />
                                <div class="user-actions">
                                    <el-button type="primary" size="small"
                                        @click="$router.push('/space/my')">个人中心</el-button>
                                </div>
                                <el-button class="hamburger-menu" @click="toggleMenu">
                                    <el-icon>
                                        <Menu />
                                    </el-icon>
                                </el-button>
                            </div>
                        </div>
                    </div>
                </el-header>
            </div>
            <el-main class="space-main">
                <router-view />
            </el-main>
        </el-container>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search, Moon, Sunny, Menu } from '@element-plus/icons-vue'

// 响应式数据
const searchQuery = ref('')
const isDarkMode = ref(false)

// 方法
const handleThemeChange = (value: boolean) => {
  // 主题切换逻辑
  console.log('Theme changed:', value)
}

const toggleMenu = () => {
  // 菜单切换逻辑
  console.log('Menu toggled')
}
</script>

<style scoped lang="scss">
.space-layout {
    min-height: 100vh;
    background-color: var(--el-bg-color);
    transition: background-color 0.3s ease;

    .header-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background-color: var(--el-bg-color);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .space-header {
        padding: 0;
        height: auto;

        .header-top {
            padding: 16px 0;

            .header-top-container {
                max-width: 1280px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 20px;

                .left-section {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .logo {
                        .logo-image {
                            height: 32px;
                            transition: transform 0.3s ease;

                            &:hover {
                                transform: scale(1.05);
                            }
                        }
                    }
                }

                .search-section {
                    flex: 1;
                    max-width: 500px;
                    margin: 0 40px;

                    .search-input {
                        :deep(.el-input__wrapper) {
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                            transition: all 0.3s ease;

                            &:hover {
                                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                            }
                        }
                    }
                }

                .right-section {
                    display: flex;
                    align-items: center;
                    gap: 20px;

                    .theme-switch {
                        margin-right: 10px;
                    }

                    .user-actions {
                        display: flex;
                        gap: 12px;

                        .el-button {
                            transition: transform 0.2s ease;

                            &:hover {
                                transform: translateY(-2px);
                            }
                        }
                    }

                    .hamburger-menu {
                        display: none;
                        @media (max-width: 768px) {
                            display: block;
                        }
                    }
                }
            }
        }
    }

    .space-container {
        margin-top: 80px;
        padding: 20px;
        max-width: 1280px;
        margin-left: auto;
        margin-right: auto;
    }

    .space-aside {
        background-color: var(--el-bg-color);
        border-radius: 12px;
        padding: 24px 0;
        margin-right: 24px;
        height: fit-content;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }

        .space-menu {
            border-right: none;

            .user-info {
                text-align: center;
                padding: 24px 0;
                border-bottom: 1px solid var(--el-border-color);
                margin-bottom: 24px;

                .username {
                    margin: 12px 0 0;
                    font-size: 16px;
                    color: var(--el-text-color-primary);
                }
            }

            .el-menu-item {
                margin: 4px 16px;
                border-radius: 8px;
                transition: all 0.3s ease;

                &:hover,
                &.is-active {
                    background-color: var(--el-color-primary-light-9);
                    color: var(--el-color-primary);
                }

                .el-icon {
                    margin-right: 12px;
                }
            }
        }
    }

    .space-main {
        background-color: var(--el-bg-color);
        border-radius: 12px;
        padding: 24px;
        min-height: 500px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
        }
    }
}

// 深色模式样式
:deep([data-theme='dark']) {
    .space-layout {
        .header-wrapper {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .space-aside,
        .space-main {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

            &:hover {
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
            }
        }

        .space-menu {
            .el-menu-item {
                &:hover,
                &.is-active {
                    background-color: var(--el-color-primary-light-9);
                }
            }
        }
    }
}
</style>