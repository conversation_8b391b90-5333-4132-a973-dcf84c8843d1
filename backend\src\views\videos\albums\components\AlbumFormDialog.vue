<template>
  <el-dialog
    :model-value="visible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
    @update:model-value="updateVisible"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit.prevent
    >
      <el-form-item label="专辑标题" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入专辑标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="专辑描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入专辑描述"
          :rows="4"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="专辑封面" prop="cover">
        <div class="upload-container">
          <el-upload
            ref="uploadRef"
            class="avatar-uploader"
            action="#"
            :show-file-list="false"
            :on-change="handleFileChange"
            :auto-upload="false"
            accept="image/*"
          >
            <img v-if="formData.cover" :src="formData.cover" class="avatar" />
            <div v-else class="avatar-uploader-icon">
              <el-icon><Plus /></el-icon>
              <div class="upload-text">上传封面</div>
            </div>
          </el-upload>
        </div>
      </el-form-item>

      <el-form-item label="创建者" prop="user_id">
        <el-select
          v-model="formData.user_id"
          filterable
          remote
          reserve-keyword
          placeholder="请搜索创建者"
          :remote-method="searchCreators"
          :loading="creatorsLoading"
          clearable
          style="width: 100%;"
          @change="handleCreatorChange"
        >
          <el-option
            v-for="creator in creatorsOptions"
            :key="creator.id"
            :label="creator.nickname || creator.username"
            :value="creator.id"
          >
            <div style="display: flex; align-items: center;">
              <el-avatar :size="20" :src="creator.avatar" style="margin-right: 6px;">
                {{ (creator.nickname || creator.username).charAt(0) }}
              </el-avatar>
              <span>{{ creator.nickname || creator.username }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="分类" prop="category_id">
        <el-select
          v-model="formData.category_id"
          filterable
          remote
          reserve-keyword
          placeholder="请搜索分类"
          :remote-method="searchCategories"
          :loading="categoriesLoading"
          clearable
          style="width: 100%;"
          @change="handleCategoryChange"
        >
          <el-option
            v-for="category in categoriesOptions"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请选择或输入标签"
          style="width: 100%;"
        >
          <el-option
            v-for="tag in tagOptions"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="是否推荐" prop="is_featured">
        <el-radio-group v-model="formData.is_featured">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="排序权重" prop="sort_order">
        <el-input-number
          v-model="formData.sort_order"
          :min="0"
          :max="9999"
          placeholder="排序权重"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ type === 'add' ? '添加' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { searchUsers, searchVideoCategories } from '@/service/api/videos/videos';
import type { VideoAlbum } from '@/types/videoAlbum';
import { Plus } from '@element-plus/icons-vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';

// Props
interface Props {
  visible: boolean;
  type: 'add' | 'edit';
  albumData?: VideoAlbum | null;
}

const props = withDefaults(defineProps<Props>(), {
  albumData: null
});

// Emits
interface Emits {
  'update:visible': [value: boolean];
  success: [];
}

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);
const creatorsLoading = ref(false);
const categoriesLoading = ref(false);
const creatorsOptions = ref<any[]>([]);
const categoriesOptions = ref<any[]>([]);
const tagOptions = ref(['热门', '推荐', '精选', '原创']);

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  description: '',
  user_id: '',
  user_nickname: '',
  user_avatar: '',
  cover: '',
  category_id: '',
  category_name: '',
  tags: [] as string[],
  is_featured: 0,
  sort_order: 0,
  status: 1
});

// 表单验证规则
const formRules: FormRules = {
  title: [
    { required: true, message: '请输入专辑标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  user_id: [
    { required: true, message: '请选择创建者', trigger: 'change' }
  ]
};

// 计算属性
const dialogTitle = computed(() => {
  return props.type === 'add' ? '添加专辑' : '编辑专辑';
});

// 搜索创建者
const searchCreators = async (query: string) => {
  if (query) {
    creatorsLoading.value = true;
    try {
      const result = await searchUsers({
        page: { pageNo: 1, pageSize: 20 },
        data: { keyword: query, status: 1 }
      });

      if (result?.data?.list) {
        creatorsOptions.value = result.data.list;
      } else {
        creatorsOptions.value = [];
      }
    } catch (error) {
      console.error('搜索创建者失败:', error);
      ElMessage.error('搜索创建者失败');
      creatorsOptions.value = [];
    } finally {
      creatorsLoading.value = false;
    }
  } else {
    creatorsOptions.value = [];
  }
};

// 搜索分类
const searchCategories = async (query: string) => {
  categoriesLoading.value = true;
  try {
    const params: any = {
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    };
    
    // 如果有查询关键字，添加到参数中
    if (query) {
      params.data.keyword = query;
    }
    
    const result = await searchVideoCategories(params);

    if (result?.data?.list) {
      categoriesOptions.value = result.data.list;
    } else {
      categoriesOptions.value = [];
    }
  } catch (error) {
    console.error('搜索分类失败:', error);
    ElMessage.error('搜索分类失败');
    categoriesOptions.value = [];
  } finally {
    categoriesLoading.value = false;
  }
};

// 处理创建者选择变化
const handleCreatorChange = (userId: string) => {
  const selectedCreator = creatorsOptions.value.find(creator => creator.id === userId);
  if (selectedCreator) {
    formData.user_nickname = selectedCreator.nickname || selectedCreator.username;
    formData.user_avatar = selectedCreator.avatar || '';
  } else {
    formData.user_nickname = '';
    formData.user_avatar = '';
  }
};

// 处理分类选择变化
const handleCategoryChange = (categoryId: string) => {
  const selectedCategory = categoriesOptions.value.find(category => category.id === categoryId);
  if (selectedCategory) {
    formData.category_name = selectedCategory.name;
  } else {
    formData.category_name = '';
  }
};

// 加载默认选项
const loadDefaultOptions = async () => {
  // 加载一些默认的分类选项
  try {
    const result = await searchVideoCategories({
      page: { pageNo: 1, pageSize: 10 },
      data: { status: 1 }
    });

    if (result?.data?.list) {
      categoriesOptions.value = result.data.list;
    }
  } catch (error) {
    console.error('加载默认分类失败:', error);
  }
};

// 根据现有数据加载对应的创建者和分类信息
const loadExistingData = async () => {
  if (props.type === 'edit' && props.albumData) {
    const albumData = props.albumData;
    
    // 如果有创建者ID，加载创建者信息
    if (albumData.user_id) {
      try {
        const result = await searchUsers({
          page: { pageNo: 1, pageSize: 1 },
          data: { keyword: albumData.user_nickname || '', status: 1 }
        });
        
        if (result?.data?.list && result.data.list.length > 0) {
          creatorsOptions.value = result.data.list;
        }
      } catch (error) {
        console.error('加载创建者信息失败:', error);
      }
    }

    // 如果有分类ID，确保分类选项中包含当前分类
    if (albumData.category_id && albumData.category_name) {
      const existingCategory = categoriesOptions.value.find(cat => cat.id === albumData.category_id);
      if (!existingCategory) {
        categoriesOptions.value.push({
          id: albumData.category_id,
          name: albumData.category_name
        });
      }
    }
  }
};

// 监听props变化
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 每次打开对话框都先重置表单
    resetForm();
    
    if (props.type === 'edit' && props.albumData) {
      // 编辑模式：填充数据
      await nextTick(); // 等待DOM更新
      Object.assign(formData, {
        ...props.albumData,
        tags: props.albumData.tags || []
      });
      await loadExistingData();
    }
    // 添加模式：保持默认的重置状态
  }
});

// 监听对话框类型变化，确保类型切换时重置数据
watch(() => props.type, () => {
  if (props.visible) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    title: '',
    description: '',
    user_id: '',
    user_nickname: '',
    user_avatar: '',
    cover: '',
    category_id: '',
    category_name: '',
    tags: [],
    is_featured: 0,
    sort_order: 0,
    status: 1
  });
  formRef.value?.resetFields();
  
  // 清空选项
  creatorsOptions.value = [];
  // 保留分类选项，不清空
};

// 更新可见性
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 关闭对话框
const handleClose = () => {
  resetForm();
  updateVisible(false);
};

// 文件变化处理
const handleFileChange = (file: any) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.cover = e.target?.result as string;
  };
  reader.readAsDataURL(file.raw);
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // 准备提交数据
    const submitData = { ...formData };
    
    // 调用实际的API
    if (props.type === 'add') {
      const { addVideoAlbum } = await import('@/service/api/videos/albums');
      const {response} = await addVideoAlbum({ data: submitData }) as any;
      
      if (response.status === 200&&response.data.code==2000) {
        ElMessage.success('添加专辑成功');
        emit('success');
        handleClose();
      } else {
        ElMessage.error(response?.data?.message || '添加专辑失败');
      }
    } else {
      const { updateVideoAlbum } = await import('@/service/api/videos/albums');
      const {response} = await updateVideoAlbum(submitData.id, { data: submitData }) as any;

      if (response.status === 200&&response.data.code==2000) {
        ElMessage.success('更新专辑成功');
        emit('success');
        handleClose();
      } else {
        ElMessage.error(response?.data?.message || '更新专辑失败');
      }
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error(`${props.type === 'add' ? '添加' : '更新'}专辑失败`);
  } finally {
    submitting.value = false;
  }
};

// 组件挂载时加载默认选项
onMounted(() => {
  loadDefaultOptions();
});
</script>

<style scoped lang="scss">
.upload-container {
  .avatar-uploader {
    display: flex;
    justify-content: center;

    .avatar {
      width: 100px;
      height: 100px;
      border-radius: 6px;
      object-fit: cover;
    }

    .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px;
      height: 100px;
      text-align: center;
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .upload-text {
        margin-top: 8px;
        font-size: 12px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-select-dropdown__item) {
  height: auto;
  line-height: normal;
  padding: 6px 20px;
}
</style> 