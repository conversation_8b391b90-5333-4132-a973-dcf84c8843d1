package system

import (
	"frontapi/internal/admin"
	systemModel "frontapi/internal/models/system"
	systemSrv "frontapi/internal/service/system"
	systemValidator "frontapi/internal/validation/system"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

type CountryController struct {
	admin.BaseController
	Service systemSrv.CountryService
}

func NewCountryController(service systemSrv.CountryService) *CountryController {
	return &CountryController{Service: service}
}

func (c *CountryController) Create(ctx *fiber.Ctx) error {
	var req systemValidator.CreateCountryRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	var systemModel systemModel.Country
	if err := utils.SmartCopy(&systemModel, &req); err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}

	id, err := c.Service.Create(ctx.Context(), &systemModel)
	if err != nil {
		return c.BadRequest(ctx, "创建国家失败", nil)
	}
	return c.Success(ctx, id)
}

func (c *CountryController) Get(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	item, err := c.Service.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.BadRequest(ctx, "获取国家失败", nil)
	}
	if item == nil {
		return c.NotFound(ctx, "未找到")
	}
	return c.Success(ctx, item)
}

func (c *CountryController) Update(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	var req systemValidator.UpdateCountryRequest
	if err := ctx.BodyParser(&req); err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	var systemCountry systemModel.Country
	if err := utils.SmartCopy(&systemCountry, &req); err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if err := c.Service.UpdateById(ctx.Context(), id, &systemCountry); err != nil {
		return c.BadRequest(ctx, "更新国家失败", nil)
	}
	return c.Success(ctx, nil)
}

func (c *CountryController) Delete(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	if err := c.Service.Delete(ctx.Context(), id); err != nil {
		return c.BadRequest(ctx, "删除国家失败", nil)
	}
	return c.Success(ctx, nil)
}

func (c *CountryController) List(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "id ASC"
	}
	items, total, err := c.Service.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.BadRequest(ctx, "获取国家列表失败", nil)
	}
	return c.SuccessList(ctx, items, total, page, pageSize)
}
