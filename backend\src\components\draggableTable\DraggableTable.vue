<template>
  <div class="draggable-table-container">
    <el-table
      v-draggable-columns
      v-bind="$attrs"
      @column-drop="handleColumnDrop"
      @column-drag-start="handleDragStart"
      @column-drag-over="handleDragOver"
      ref="tableRef"
    >
      <slot></slot>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, watch } from 'vue';

interface ColumnDragEvent {
  detail: {
    fromIndex: number;
    toIndex: number;
  };
}

const emit = defineEmits(['column-order-change']);
const tableRef = ref<any>(null);
const columnOrder = ref<number[]>([]);

// Track all columns in the table
onMounted(() => {
  if (tableRef.value) {
    // Initialize column order
    initColumnOrder();
  }
});

// Watch for changes in the table columns when they might be dynamically added
watch(() => tableRef.value?.columns, (newCols) => {
  if (newCols && newCols.length) {
    initColumnOrder();
  }
}, { deep: true });

// Initialize the column order array
const initColumnOrder = () => {
  if (!tableRef.value) return;
  
  const columns = tableRef.value.columns || [];
  columnOrder.value = Array.from({ length: columns.length }, (_, i) => i);
};

// Handle column drag start
const handleDragStart = (event: any) => {
  // You can add visual feedback here if needed
  console.log('Drag started', event.detail);
};

// Handle column drag over
const handleDragOver = (event: any) => {
  // You can add visual feedback here if needed
  console.log('Dragging over', event.detail);
};

// Handle column drop event
const handleColumnDrop = async (event: ColumnDragEvent) => {
  const { fromIndex, toIndex } = event.detail;
  
  // Update the column order
  const newOrder = [...columnOrder.value];
  const [removed] = newOrder.splice(fromIndex, 1);
  newOrder.splice(toIndex, 0, removed);
  columnOrder.value = newOrder;
  
  // Need to wait for the DOM to update
  await nextTick();
  
  // Find all column elements
  if (tableRef.value) {
    const tableEl = tableRef.value.$el;
    const headerRow = tableEl.querySelector('.el-table__header-wrapper tr');
    if (headerRow) {
      // Force a table re-render
      tableRef.value.doLayout();
      
      // Emit the new column order
      emit('column-order-change', {
        fromIndex,
        toIndex,
        columnOrder: newOrder
      });
    }
  }
};
</script>

<style scoped>
.draggable-table-container {
  width: 100%;
}

:deep(.column-dragging) {
  background-color: var(--el-color-primary-light-9) !important;
  border: 2px dashed var(--el-color-primary) !important;
}

:deep(.el-table th) {
  cursor: move;
}
</style> 