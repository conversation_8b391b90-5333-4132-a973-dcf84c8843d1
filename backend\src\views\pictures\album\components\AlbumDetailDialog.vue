<template>
    <el-dialog title="专辑详情" v-model="dialogVisible" width="600px" append-to-body destroy-on-close>
        <div v-if="album" class="album-detail">
            <div class="album-header">
                <div class="album-cover">
                    <el-image :src="album.cover_url" fit="cover" class="cover-image"
                        :preview-src-list="album.cover_url ? [album.cover_url] : []">
                        <template #error>
                            <div class="image-placeholder">
                                <el-icon :size="32">
                                    <Picture />
                                </el-icon>
                            </div>
                        </template>
                    </el-image>
                </div>
                <div class="album-info">
                    <h2 class="album-title">{{ album.title }}</h2>
                    <div class="detail-item">
                        <span class="label">分类:</span>
                        <span class="value">
                            <el-tag v-if="album.category_name" type="info" size="small" effect="light">
                                {{ album.category_name }}
                            </el-tag>
                            <span v-else class="placeholder-text">未分类</span>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="label">状态:</span>
                        <span class="value">
                            <AlbumStatusTag :status="album.status" />
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="label">创建时间:</span>
                        <span class="value">{{ formatDateTime(album.created_at) }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">更新时间:</span>
                        <span class="value">{{ formatDateTime(album.updated_at) }}</span>
                    </div>
                </div>
            </div>

            <el-divider />

            <div class="detail-section">
                <h3>专辑描述</h3>
                <div class="value-box">
                    {{ album.description || '暂无描述' }}
                </div>
            </div>

            <div class="detail-section">
                <h3>统计信息</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <el-icon>
                            <Picture />
                        </el-icon>
                        <span class="stat-label">图片数量</span>
                        <span class="stat-value">{{ album.picture_count || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <el-icon>
                            <View />
                        </el-icon>
                        <span class="stat-label">浏览量</span>
                        <span class="stat-value">{{ album.view_count || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <el-icon>
                            <StarFilled />
                        </el-icon>
                        <span class="stat-label">点赞数</span>
                        <span class="stat-value">{{ album.like_count || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <el-icon>
                            <ChatDotRound />
                        </el-icon>
                        <span class="stat-label">评论数</span>
                        <span class="stat-value">{{ album.comment_count || 0 }}</span>
                    </div>
                    <div class="stat-item">
                        <el-icon>
                            <Share />
                        </el-icon>
                        <span class="stat-label">分享数</span>
                        <span class="stat-value">{{ album.share_count || 0 }}</span>
                    </div>
                </div>
            </div>

            <div v-if="album.tags_json" class="detail-section">
                <h3>标签</h3>
                <div class="tags-container">
                    <el-tag v-for="tag in parsedTags" :key="tag" class="tag-item" type="info" effect="plain">
                        {{ tag }}
                    </el-tag>
                    <span v-if="parsedTags.length === 0" class="placeholder-text">暂无标签</span>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
                <el-button type="primary" @click="handleEdit">编辑</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { PictureAlbum } from '@/types/pictures';
import { ChatDotRound, Picture, Share, StarFilled, View } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { computed, defineEmits, defineProps } from 'vue';
import AlbumStatusTag from './AlbumStatusTag.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    album: {
        type: Object as () => PictureAlbum | null,
        default: null
    }
});

const emit = defineEmits(['update:visible', 'edit']);

// 计算属性：对话框可见性
const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

// 解析标签JSON数据
const parsedTags = computed(() => {
    if (!props.album?.tags_json) return [];

    try {
        const tagsData = JSON.parse(props.album.tags_json);
        return Array.isArray(tagsData) ? tagsData : [];
    } catch (error) {
        console.error('解析标签数据失败:', error);
        return [];
    }
});

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '-';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 编辑按钮点击处理
const handleEdit = () => {
    dialogVisible.value = false;
    emit('edit', props.album);
};
</script>

<style scoped lang="scss">
.album-detail {
    padding: 16px;

    .album-header {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .album-cover {
            width: 160px;
            height: 160px;
            flex-shrink: 0;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

            .cover-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
            }

            .image-placeholder {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f5f7fa;
                color: #909399;
            }
        }

        .album-info {
            flex: 1;
            min-width: 0;

            .album-title {
                margin-top: 0;
                margin-bottom: 16px;
                color: #303133;
                font-size: 20px;
                font-weight: 600;
            }
        }
    }

    .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        .label {
            width: 80px;
            color: #606266;
            font-weight: 500;
        }

        .value {
            color: #303133;
        }
    }

    .detail-section {
        margin-bottom: 20px;

        h3 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 16px;
            color: #303133;
            font-weight: 600;
        }
    }

    .value-box {
        padding: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #303133;
        min-height: 60px;
        white-space: pre-wrap;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 16px;
        margin-top: 12px;

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            background-color: #f5f7fa;
            border-radius: 4px;
            text-align: center;

            .el-icon {
                font-size: 24px;
                color: #409eff;
                margin-bottom: 8px;
            }

            .stat-label {
                font-size: 12px;
                color: #606266;
                margin-bottom: 4px;
            }

            .stat-value {
                font-size: 18px;
                color: #303133;
                font-weight: 600;
            }
        }
    }

    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 8px;
    }

    .placeholder-text {
        color: #909399;
        font-style: italic;
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>