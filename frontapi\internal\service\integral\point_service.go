package integral

import (
	"frontapi/pkg/types"

	model "frontapi/internal/models/integral"
	repo "frontapi/internal/repository/integral"
	"frontapi/internal/service/base"
)

// CreatePointRequest 创建积分记录请求
type CreatePointRequest struct {
	UserID      string         `json:"user_id"`
	Amount      int            `json:"amount"`
	Balance     int            `json:"balance"`
	Type        string         `json:"type"`
	RuleID      string         `json:"rule_id"`
	ContentType string         `json:"content_type"`
	ContentID   string         `json:"content_id"`
	Description string         `json:"description"`
	IP          string         `json:"ip"`
	Device      string         `json:"device"`
	ExpiredAt   types.JSONTime `json:"expired_at"`
	IsExpired   int8           `json:"is_expired"`
}

// UpdatePointRequest 更新积分记录请求
type UpdatePointRequest struct {
	Amount      int            `json:"amount"`
	Balance     int            `json:"balance"`
	Type        string         `json:"type"`
	RuleID      string         `json:"rule_id"`
	ContentType string         `json:"content_type"`
	ContentID   string         `json:"content_id"`
	Description string         `json:"description"`
	IP          string         `json:"ip"`
	Device      string         `json:"device"`
	ExpiredAt   types.JSONTime `json:"expired_at"`
	IsExpired   int8           `json:"is_expired"`
}

// PointService 积分服务接口
type PointService interface {
	base.IExtendedService[model.Point]
}

type pointService struct {
	*base.ExtendedService[model.Point]
	repo repo.PointRepository
}

func NewPointService(repo repo.PointRepository) PointService {
	return &pointService{
		ExtendedService: base.NewExtendedService[model.Point](repo, "point"),
		repo:            repo,
	}
}
