import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { followUser as followUserApi, unfollowUser as unfollowUserApi } from '@/api/common/userActions'
import type { RecommendedUser } from '@/types/community'

/**
 * 用户操作组合函数
 * 提供关注、取消关注用户的通用逻辑
 */
export function useUserActions() {
  const loading = ref(false)

  /**
   * 关注用户
   * @param userId 用户ID
   * @param userList 用户列表（可选，用于更新本地状态）
   * @param successCallback 成功回调
   * @param errorCallback 错误回调
   */
  const followUser = async (
    userId: string,
    userList?: Ref<RecommendedUser[]> | RecommendedUser[],
    successCallback?: () => void,
    errorCallback?: (error: any) => void
  ) => {
    if (loading.value) return
    
    loading.value = true
    try {
      await followUserApi({ data: { userId } })
      
      // 更新本地用户状态
      if (userList) {
        const users = Array.isArray(userList) ? userList : userList.value
        const user = users.find(u => u.id === userId)
        if (user) {
          user.isFollowed = true
          user.followCount = (user.followCount || 0) + 1
        }
      }
      
      ElMessage.success('关注成功')
      successCallback?.()
    } catch (error) {
      console.error('关注失败:', error)
      ElMessage.error('关注失败')
      errorCallback?.(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 取消关注用户
   * @param userId 用户ID
   * @param userList 用户列表（可选，用于更新本地状态）
   * @param successCallback 成功回调
   * @param errorCallback 错误回调
   */
  const unfollowUser = async (
    userId: string,
    userList?: Ref<RecommendedUser[]> | RecommendedUser[],
    successCallback?: () => void,
    errorCallback?: (error: any) => void
  ) => {
    if (loading.value) return
    
    loading.value = true
    try {
      await unfollowUserApi({ data: { userId } })
      
      // 更新本地用户状态
      if (userList) {
        const users = Array.isArray(userList) ? userList : userList.value
        const user = users.find(u => u.id === userId)
        if (user) {
          user.isFollowed = false
          user.followCount = Math.max((user.followCount || 0) - 1, 0)
        }
      }
      
      ElMessage.success('取消关注成功')
      successCallback?.()
    } catch (error) {
      console.error('取消关注失败:', error)
      ElMessage.error('取消关注失败')
      errorCallback?.(error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 切换关注状态
   * @param userId 用户ID
   * @param isFollowed 当前关注状态
   * @param userList 用户列表（可选，用于更新本地状态）
   * @param successCallback 成功回调
   * @param errorCallback 错误回调
   */
  const toggleFollow = async (
    userId: string,
    isFollowed: boolean,
    userList?: Ref<RecommendedUser[]> | RecommendedUser[],
    successCallback?: () => void,
    errorCallback?: (error: any) => void
  ) => {
    if (isFollowed) {
      await unfollowUser(userId, userList, successCallback, errorCallback)
    } else {
      await followUser(userId, userList, successCallback, errorCallback)
    }
  }

  return {
    loading,
    followUser,
    unfollowUser,
    toggleFollow
  }
}