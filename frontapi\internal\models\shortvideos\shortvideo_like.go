package shortvideos

import (
	"frontapi/internal/models"

	"github.com/guregu/null/v6"
)

// ShortVideoLike 短视频点赞模型
type ShortVideoLike struct {
	models.BaseModel
	UserID        string      `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	ShortID       string      `json:"short_id" gorm:"type:string;size:36;not null;comment:短视频ID"`
	ShortTitle    string      `json:"short_title" gorm:"comment:短视频标题"`
	ShortCover    string      `json:"short_cover" gorm:"comment:短视频封面"`
	CreatorID     null.String `json:"creator_id" gorm:"type:string;size:36;comment:创作者ID"`
	CreatorName   string      `json:"creator_name" gorm:"comment:创作者名称"`
	CreatorAvatar string      `json:"creator_avatar" gorm:"comment:创作者头像"`
}

// TableName 指定表名
func (ShortVideoLike) TableName() string {
	return "ly_shorts_likes"
}
