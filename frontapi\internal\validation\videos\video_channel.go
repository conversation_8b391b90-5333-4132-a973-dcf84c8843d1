package videos

import "frontapi/pkg/types"

// 新增请求结构
type CreateChannelRequest struct {
	Name            string `json:"name" validate:"required|minLen:2|maxLen:50"`
	Icon            string `json:"icon"`
	Image           string `json:"image" validate:"required"`
	Description     string `json:"description"`
	UpdateFrequency string `json:"updateFrequency"`
	Uri             string `json:"uri"`
	SortOrder       uint8  `json:"sortOrder"`
	Status          uint8  `json:"status"`
}

// 更新请求结构
type UpdateChannelRequest struct {
	Name            string `json:"name" validate:"minLen:2|maxLen:50"`
	Icon            string `json:"icon"`
	Image           string `json:"image"`
	Description     string `json:"description"`
	UpdateFrequency string `json:"updateFrequency"`
	Uri             string `json:"uri"`
	SortOrder       uint8  `json:"sortOrder"`
	Status          uint8  `json:"status"`
}

// VideoChannelResponse 响应结构
type VideoChannelResponse struct {
	ID              string         `json:"id"`
	Name            string         `json:"name"`
	Icon            string         `json:"icon"`
	Image           string         `json:"image"`
	Description     string         `json:"description"`
	UpdateFrequency string         `json:"updateFrequency"`
	Uri             string         `json:"uri"`
	SortOrder       uint8          `json:"sortOrder"`
	Status          uint8          `json:"status"`
	CreatedAt       types.JSONTime `json:"createdAt"`
	UpdatedAt       types.JSONTime `json:"updatedAt"`
}

type UpdateChannelStatusRequest struct {
	Id     string `json:"id" validate:"required|string"`
	Status int    `json:"status" validate:"int|min:-5|max:5"`
}

// BatchUpdateCategoryStatusRequest 批量更新分类状态请求
type BatchUpdateChannelStatusRequest struct {
	Ids    []string `json:"ids" validate:"required|min_len:1"`
	Status int      `json:"status" validate:"int|min:-5|max:5"`
}
