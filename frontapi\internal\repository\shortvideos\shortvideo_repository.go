package shortvideos

import (
	"context"
	"errors"
	"frontapi/config/constant"
	"frontapi/internal/models/shortvideos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ShortVideoRepository 短视频仓库接口
type ShortVideoRepository interface {
	base.ExtendedRepository[shortvideos.ShortVideo]
	//审核视频
	ReviewShorts(ctx context.Context, id string, status int, reason string) error
}

// shortVideoRepository 短视频仓库实现
type shortVideoRepository struct {
	base.ExtendedRepository[shortvideos.ShortVideo]
}

// NewShortVideoRepository 创建短视频仓库实例
func NewShortVideoRepository(db *gorm.DB) ShortVideoRepository {
	repo := &shortVideoRepository{
		ExtendedRepository: base.NewExtendedRepository[shortvideos.ShortVideo](db),
	}
	// 设置调用者实例，这样基础仓库就能识别并使用子类的ApplyConditions方法
	repo.SetCaller(repo)
	return repo
}

// ApplyConditions 实现ConditionApplier接口，自定义短视频查询条件应用逻辑
func (r *shortVideoRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	if condition == nil {
		return query
	}

	// 处理关键字搜索 - 标题和描述
	if condition["keyword"] != nil && condition["keyword"] != "" {
		keyword := condition["keyword"].(string)
		query = query.Where("title LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 处理分类过滤
	if condition["category_id"] != nil && condition["category_id"] != "" {
		query = query.Where("category_id = ?", condition["category_id"])
	}

	// 处理创作者过滤
	if condition["creator_id"] != nil && condition["creator_id"] != "" {
		query = query.Where("creator_id = ?", condition["creator_id"])
	}

	// 处理创作者名称过滤
	if condition["creator_name"] != nil && condition["creator_name"] != "" {
		creatorName := condition["creator_name"].(string)
		query = query.Where("creator_name LIKE ?", "%"+creatorName+"%")
	}

	// 处理时长范围
	if condition["duration_min"] != nil && condition["duration_min"] != "" {
		query = query.Where("duration >= ?", condition["duration_min"])
	}
	if condition["duration_max"] != nil && condition["duration_max"] != "" {
		query = query.Where("duration <= ?", condition["duration_max"])
	}

	// 处理是否推荐
	if condition["is_featured"] != nil && condition["is_featured"] != "" {
		query = query.Where("is_featured = ?", condition["is_featured"])
	}

	// 处理是否付费
	if condition["is_paid"] != nil && condition["is_paid"] != "" {
		query = query.Where("is_paid = ?", condition["is_paid"])
	}

	// 处理价格范围
	if condition["price_min"] != nil && condition["price_min"] != "" {
		query = query.Where("price >= ?", condition["price_min"])
	}
	if condition["price_max"] != nil && condition["price_max"] != "" {
		query = query.Where("price <= ?", condition["price_max"])
	}

	// 处理观看次数范围
	if condition["view_count_min"] != nil && condition["view_count_min"] != "" {
		query = query.Where("view_count >= ?", condition["view_count_min"])
	}
	if condition["view_count_max"] != nil && condition["view_count_max"] != "" {
		query = query.Where("view_count <= ?", condition["view_count_max"])
	}

	// 处理点赞数范围
	if condition["like_count_min"] != nil && condition["like_count_min"] != "" {
		query = query.Where("like_count >= ?", condition["like_count_min"])
	}
	if condition["like_count_max"] != nil && condition["like_count_max"] != "" {
		query = query.Where("like_count <= ?", condition["like_count_max"])
	}

	// 处理上传时间范围
	if condition["upload_time_start"] != nil && condition["upload_time_start"] != "" {
		query = query.Where("upload_time >= ?", condition["upload_time_start"])
	}
	if condition["upload_time_end"] != nil && condition["upload_time_end"] != "" {
		query = query.Where("upload_time <= ?", condition["upload_time_end"])
	}

	// 处理通用状态过滤
	if condition["status"] != nil && condition["status"] != "" {
		if status, ok := condition["status"].(int); ok && status > -999 {
			query = query.Where("status = ?", status)
		}
	}

	// 处理通用时间范围条件
	if condition["created_at_start"] != nil && condition["created_at_start"] != "" {
		query = query.Where("created_at >= ?", condition["created_at_start"])
	}
	if condition["created_at_end"] != nil && condition["created_at_end"] != "" {
		query = query.Where("created_at <= ?", condition["created_at_end"])
	}
	if condition["updated_at_start"] != nil && condition["updated_at_start"] != "" {
		query = query.Where("updated_at >= ?", condition["updated_at_start"])
	}
	if condition["updated_at_end"] != nil && condition["updated_at_end"] != "" {
		query = query.Where("updated_at <= ?", condition["updated_at_end"])
	}

	// 处理其他字段的等值查询
	for key, value := range condition {
		if value == nil || value == "" {
			continue
		}

		// 跳过已处理的字段
		switch key {
		case "keyword", "category_id", "creator_id", "creator_name",
			"duration_min", "duration_max", "is_featured", "is_paid",
			"price_min", "price_max", "view_count_min", "view_count_max",
			"like_count_min", "like_count_max", "upload_time_start", "upload_time_end",
			"status", "created_at_start", "created_at_end", "updated_at_start", "updated_at_end":
			continue
		default:
			// 默认等值查询
			query = query.Where(key+" = ?", value)
		}
	}

	return query
}

func (r *shortVideoRepository) ReviewShorts(ctx context.Context, id string, status int, reason string) error {
	if status != constant.VideoState.VideoStatusPending &&
		status != constant.VideoState.VideoStatusDown &&
		status != constant.VideoState.VideoStatusPublished &&
		status != constant.VideoState.VideoStatusRejected &&
		status != constant.VideoState.VideoStatusDeleted {
		return errors.New("invalid status")
	}
	if status == constant.VideoState.VideoStatusRejected && reason == "" {
		return errors.New("reason is required")
	}
	shortVideo, err := r.FindByID(ctx, id)
	if err != nil {
		return err
	}
	shortVideo.Status = int8(status)
	if status == constant.VideoState.VideoStatusRejected {
		shortVideo.Reason = reason
	}
	return r.Update(ctx, shortVideo)
}
