package videos

import (
	"frontapi/internal/models"
	"frontapi/internal/models/users"
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"

	"gorm.io/gorm"
)

// Video 视频模型
type Video struct {
	models.BaseModelStruct
	// 基本信息
	Title       string      `json:"title" gorm:"column:title" comment:"标题"`
	Description null.String `json:"description" gorm:"column:description;type:text" comment:"描述"`
	Cover       string      `json:"cover" gorm:"column:cover" comment:"封面URL"`
	URL         string      `json:"url" gorm:"column:url;not null" comment:"视频URL"`
	Thumbnail   string      `json:"thumbnail" gorm:"column:thumbnail" comment:"缩略图URL"`

	// 技术参数
	Duration   int    `json:"duration" gorm:"column:duration" comment:"时长(秒)"`
	Size       uint64 `json:"size" gorm:"column:size" comment:"文件大小(字节)"`
	Format     string `json:"format" gorm:"column:format" comment:"视频格式"`
	Resolution string `json:"resolution" gorm:"column:resolution" comment:"分辨率"`
	Quality    string `json:"quality" gorm:"column:quality" comment:"画质"`
	// Bitrate    int     `json:"bitrate" gorm:"column:bitrate" comment:"比特率"`
	// FrameRate  float64 `json:"frame_rate" gorm:"column:frame_rate" comment:"帧率"`
	// Codec string `json:"codec" gorm:"column:codec" comment:"编码格式"`

	// 分类和标签
	CreatorID     null.String       `json:"creator_id" gorm:"column:creator_id" comment:"创作者ID"`
	CreatorName   string            `json:"creator_name" gorm:"column:creator_name" comment:"创作者名称"`
	CreatorAvatar string            `json:"creator_avatar" gorm:"column:creator_avatar" comment:"创作者头像"`
	CategoryID    null.String       `json:"category_id" gorm:"column:category_id" comment:"分类ID"`
	CategoryName  string            `json:"category_name" gorm:"column:category_name" comment:"分类名称"`
	ChannelID     null.String       `json:"channel_id" gorm:"column:channel_id" comment:"频道ID"`
	ChannelName   string            `json:"channel_name" gorm:"column:channel_name" comment:"频道名称"`
	AlbumID       null.String       `json:"album_id" gorm:"column:album_id" comment:"专辑ID"`
	AlbumTitle    string            `json:"album_title" gorm:"column:album_title" comment:"专辑标题"`
	Celebrities   types.StringArray `json:"celebrities" gorm:"column:celebrities;type:json" comment:"明星"`
	Tags          types.StringArray `json:"tags" gorm:"column:tags;type:json" comment:"标签"`

	// 统计数据
	ViewCount     uint64 `json:"view_count" gorm:"column:view_count;default:0" comment:"观看次数"`
	LikeCount     uint64 `json:"like_count" gorm:"column:like_count;default:0" comment:"点赞数"`
	DislikeCount  uint64 `json:"dislike_count" gorm:"column:dislike_count;default:0" comment:"不喜欢数量"`
	CommentCount  uint64 `json:"comment_count" gorm:"column:comment_count;default:0" comment:"评论数"`
	ShareCount    uint64 `json:"share_count" gorm:"column:share_count;default:0" comment:"分享数"`
	DownloadCount uint64 `json:"download_count" gorm:"column:download_count;default:0" comment:"下载数"`
	FavoriteCount uint64 `json:"favorite_count" gorm:"column:favorite_count;default:0" comment:"收藏次数"`

	// 业务字段
	SrcType   int8    `json:"src_type" gorm:"column:src_type" comment:"1.用户上传，2系统上传，3.系统采集"`
	Src       string  `json:"src" gorm:"column:src" comment:"采集地址"`
	IsPaid    int8    `json:"is_paid" gorm:"column:is_paid;default:0" comment:"是否付费：0-免费，1-付费"`
	IsPrivate int8    `json:"is_private" gorm:"column:is_private;default:0" comment:"是否私有"`
	IsVIP     int8    `json:"is_vip" gorm:"column:is_vip;default:0" comment:"是否VIP"`
	Price     float64 `json:"price" gorm:"column:price;type:decimal(10,2);default:0.00" comment:"价格"`
	Score     float64 `json:"score" gorm:"column:score;type:decimal(3,1);default:0.0" comment:"评分"`
	VipLevel  *int    `json:"vip_level" gorm:"column:vip_level" comment:"vip等级"`
	Reason    string  `json:"reason" gorm:"column:reason" comment:"审核拒绝原因"`

	// 时间字段
	UploadTime types.JSONTime `json:"upload_time" gorm:"column:upload_time;default:CURRENT_TIMESTAMP" comment:"上传时间"`

	// 关联
	Author *users.User `json:"author,omitempty" gorm:"foreignKey:CreatorID;references:ID" comment:"作者信息"`

	// 虚拟字段
	IsLiked    bool `json:"is_liked" gorm:"-" comment:"是否点赞"`
	IsFavorite bool `json:"is_favorite" gorm:"-" comment:"是否收藏"`
}

// TableName 指定表名
func (Video) TableName() string {
	return "ly_videos"
}

func (v Video) BeforeCreate(tx *gorm.DB) error {
	return nil
}

// 实现 ContentBaseModel 的方法
func (v *Video) SetTitle(title string) {
	v.Title = title
}

func (v *Video) SetDescription(description interface{}) {
	if description != nil {
		switch val := description.(type) {
		case string:
			v.Description = null.StringFrom(val)
		case null.String:
			v.Description = val
		}
	}
}

func (v *Video) SetCreatorID(creatorID interface{}) {
	if creatorID != nil {
		switch val := creatorID.(type) {
		case string:
			v.CreatorID = null.StringFrom(val)
		case null.String:
			v.CreatorID = val
		case *string:
			if val != nil {
				v.CreatorID = null.StringFrom(*val)
			}
		}
	}
}

func (v *Video) SetCategoryID(categoryID interface{}) {
	if categoryID != nil {
		switch val := categoryID.(type) {
		case string:
			v.CategoryID = null.StringFrom(val)
		case null.String:
			v.CategoryID = val
		case *string:
			if val != nil {
				v.CategoryID = null.StringFrom(*val)
			}
		}
	}
}

func (v *Video) SetCategoryName(categoryName interface{}) {
	if categoryName != nil {
		switch val := categoryName.(type) {
		case string:
			v.CategoryName = val
		case null.String:
			v.CategoryName = val.String
		}
	}
}

// 扩展字段的Setter方法
func (v *Video) SetViewCount(count uint64) {
	v.ViewCount = count
}

func (v *Video) SetLikeCount(count uint64) {
	v.LikeCount = count
}

func (v *Video) SetCommentCount(count uint64) {
	v.CommentCount = count
}

func (v *Video) SetShareCount(count uint64) {
	v.ShareCount = count
}
