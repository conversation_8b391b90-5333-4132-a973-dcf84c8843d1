package promotion

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type InvitationCommission struct {
	models.BaseModel
	InviterID        string         `json:"inviter_id" gorm:"type:string;size:36;not null;comment:邀请人ID"`
	InvitedID        string         `json:"invited_id" gorm:"type:string;size:36;not null;comment:被邀请人ID"`
	OrderID          string         `json:"order_id" gorm:"type:string;size:36;not null;comment:订单ID"`
	OrderType        string         `json:"order_type" gorm:"type:string;size:20;comment:订单类型"`
	ContentType      string         `json:"content_type" gorm:"type:string;size:20;comment:内容类型"`
	ContentID        string         `json:"content_id" gorm:"type:string;size:36;comment:内容ID"`
	OrderAmount      float64        `json:"order_amount" gorm:"type:decimal(10,2);not null;comment:订单金额"`
	CommissionRate   float64        `json:"commission_rate" gorm:"type:decimal(5,2);not null;comment:佣金比例"`
	CommissionLevel  int            `json:"commission_level" gorm:"not null;default:1;comment:佣金层级"`
	CommissionAmount float64        `json:"commission_amount" gorm:"type:decimal(10,2);not null;comment:佣金金额"`
	TransactionID    string         `json:"transaction_id" gorm:"type:string;size:36;comment:交易ID"`
	WithdrawID       string         `json:"withdraw_id" gorm:"type:string;size:36;comment:提现ID"`
	Status           string         `json:"status" gorm:"type:string;size:20;not null;default:'pending';comment:状态：pending-待结算，settled-已结算，withdrawn-已提现"`
	SettleTime       types.JSONTime `json:"settle_time" gorm:"comment:结算时间"`
	SettleBatchNo    string         `json:"settle_batch_no" gorm:"type:string;size:50;comment:结算批次号"`
}

func (InvitationCommission) TableName() string {
	return "ly_invitation_commissions"
}
