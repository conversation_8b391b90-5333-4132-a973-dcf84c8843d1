/**
 * 主题插件
 */
import { App } from 'vue';
import { usePreferredDark } from '@vueuse/core';
import { 
  THEME_LINK_ID, 
  THEME_CDN_PATH,
  DARK_THEME_CLASS,
  APP_DARK_CLASS,
  THEME_CHANGED_EVENT,
  THEME_COLORS
} from '@/config/theme.config';

/**
 * 加载主题CSS
 */
export function loadThemeCSS(themeName: string): void {
  const linkId = THEME_LINK_ID;
  let link = document.getElementById(linkId) as HTMLLinkElement;

  if (!link) {
    link = document.createElement('link');
    link.id = linkId;
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }

  // 设置主题CSS路径
  link.href = `${THEME_CDN_PATH}${themeName}/theme.css`;
}

/**
 * 应用暗色模式类
 */
export function applyDarkModeClasses(isDark: boolean): void {
  if (isDark) {
    document.documentElement.classList.add(DARK_THEME_CLASS);
    document.documentElement.classList.add(APP_DARK_CLASS);
  } else {
    document.documentElement.classList.remove(DARK_THEME_CLASS);
    document.documentElement.classList.remove(APP_DARK_CLASS);
  }
}

/**
 * 应用header和footer样式
 */
export function applyHeaderFooterStyles(colorScheme: string, isDark: boolean): void {
  const colorConfig = THEME_COLORS[colorScheme as keyof typeof THEME_COLORS];
  
  if (!colorConfig) return;
  
  // 设置CSS变量
  const root = document.documentElement;
  
  if (isDark) {
    // 暗色模式 - 深色背景，浅色文字
    root.style.setProperty('--header-bg', colorConfig.header);
    root.style.setProperty('--header-text', colorConfig.headerText);
    
    // 设置footer样式
    root.style.setProperty('--footer-bg', colorConfig.footer);
    root.style.setProperty('--footer-text', colorConfig.footerText);
  } else {
    // 亮色模式 - 浅色背景，深色文字
    // 亮色模式下，使用更柔和的背景色，文字使用主题色
    root.style.setProperty('--header-bg', `rgba(${hexToRgb(colorConfig.light)}, 0.1)`);
    root.style.setProperty('--header-text', colorConfig.dark);
    
    // 设置footer样式 - 更浅的背景
    root.style.setProperty('--footer-bg', `rgba(${hexToRgb(colorConfig.light)}, 0.05)`);
    root.style.setProperty('--footer-text', colorConfig.dark);
  }
}

/**
 * 将十六进制颜色转换为RGB
 */
function hexToRgb(hex: string): string {
  // 移除#号
  hex = hex.replace('#', '');
  
  // 解析RGB值
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `${r}, ${g}, ${b}`;
}

/**
 * 触发主题变更事件
 */
export function dispatchThemeChangedEvent(themeName: string, isDark: boolean): void {
  window.dispatchEvent(new CustomEvent(THEME_CHANGED_EVENT, {
    detail: { theme: themeName, isDark }
  }));
}

/**
 * 系统暗色模式偏好
 */
export const prefersDark = usePreferredDark();

/**
 * 主题插件安装函数
 */
export function setupTheme(app: App): void {
  console.log('[Theme Plugin] Initialized');
}

export default {
  install(app: App) {
    setupTheme(app);
  }
}; 