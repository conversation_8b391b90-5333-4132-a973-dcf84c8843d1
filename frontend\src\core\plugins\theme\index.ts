/**
 * 主题插件
 */
import {
    APP_DARK_CLASS,
    DEFAULT_THEME_FAMILY,
    DEFAULT_THEME_MODE,
    DEFAULT_THEME_STYLE,
    THEME_CHANGED_EVENT,
    ThemeConfig,
    ThemeFamily,
    ThemeMode,
    ThemeState,
    ThemeStyle,
    themeStyles,
} from '@/config/theme.config';
import { updatePrimaryPalette, updateSurfacePalette, usePreset } from "@primeuix/themes";
import { usePreferredDark } from '@vueuse/core';
import { App } from 'vue';

export class ThemeManager {
    // 单例模式实现
    private static instance: ThemeManager;
    public themeState: ThemeState = ThemeState.getInstance();

    // 系统暗色模式偏好
    public prefersDark = usePreferredDark();

    private constructor() {
        // 私有构造函数，防止外部实例化
    }

    public static getInstance(): ThemeManager {
        if (!ThemeManager.instance) {
            ThemeManager.instance = new ThemeManager();
        }
        return ThemeManager.instance;
    }

    /**
     * 获取可用主题列表
     */
    public getAvailableThemes(): string[] {
        return Object.keys(this.themeState.themePresets);
    }
    /**
     * 获取可用主题颜色列表
     */
    public getAvailableColors(): string[] {
        return this.themeState.primaryColors[this.themeState.themeFamily].map((c: any) => c.name);
    }
    /**
     * 获取当前定义使用的颜色列表
     */
    public getThemeStyles(): ThemeConfig[] {
        var availableColors = this.getAvailableColors()
        //过滤掉不在可用颜色列表中的颜色，保留在可用颜色列表中的颜色，就是页面显示可以切换的颜色
        return themeStyles.filter((c: ThemeConfig) => availableColors.includes(c.name));
    }

    /**
     * 应用暗色模式类
     */
    public applyDarkModeClasses(isDark: boolean): void {
        if (isDark) {
            document.documentElement.classList.add(APP_DARK_CLASS);
            // document.body.classList.add(APP_DARK_CLASS);
            this.themeState.themeMode = 'dark';
        } else {
            document.documentElement.classList.remove(APP_DARK_CLASS);
            // document.body.classList.remove(APP_DARK_CLASS);
            this.themeState.themeMode = 'light';
        }

        // 更新主题名称以反映模式变化
        this.updateThemeName();
    }

    /**
     * 切换深色模式
     */
    public toggleDarkMode(): void {
        const isDark = document.documentElement.classList.contains(APP_DARK_CLASS);
        this.applyDarkModeClasses(!isDark);

        // 触发主题变更事件
        this.dispatchThemeChangedEvent(this.themeState.themeName, !isDark);
    }

    /**
     * 获取可用主题列表
     */
    public getThemeList(): ThemeConfig[] {
        return themeStyles;
    }

    /**
     * 获取当前主题
     */
    public getCurrentTheme(): ThemeConfig {
        const theme = themeStyles.find(theme => theme.name === this.themeState.themeStyle);
        if (!theme) {
            throw new Error(`Theme style ${this.themeState.themeStyle} not found`);
        }
        return theme;
    }

    /**
     * 设置当前主题
     */
    public setCurrentTheme(themeName: string): void {
        const parts = this.parseThemeName(themeName);

        if (parts) {
            const [family, style, mode] = parts;

            // 更新主题家族
            if (family !== this.themeState.themeFamily) {
                this.updateThemePresets(family);
            }

            // 更新主题颜色
            if (style !== this.themeState.themeStyle) {
                this.updateColors('primary', style);
                this.updateColors('surface', style);
            }

            // 更新主题模式
            const isDark = mode === 'dark';
            if ((isDark && this.themeState.themeMode !== 'dark') ||
                (!isDark && this.themeState.themeMode !== 'light')) {
                this.applyDarkModeClasses(isDark);
            }

            // 更新主题名称
            this.themeState.themeName = themeName;

            // 触发主题变更事件
            this.dispatchThemeChangedEvent(themeName, isDark);
        }
    }

    /**
     * 解析主题名称，返回 [family, style, mode]
     */
    private parseThemeName(themeName: string): [ThemeFamily, ThemeStyle, ThemeMode] | null {
        const parts = themeName.split('-');
        if (parts.length < 3) return null;

        const family = parts[0] as ThemeFamily;
        const style = parts[1] as ThemeStyle;
        const mode = parts[2] as ThemeMode;

        return [family, style, mode];
    }

    /**
     * 更新主题名称
     */
    private updateThemeName(): void {
        this.themeState.themeName = this.getThemeName(
            this.themeState.themeFamily,
            this.themeState.themeStyle,
            this.themeState.themeMode
        );
    }

    /**
     * 设置主题颜色
     */
    public setPrimaryColor(color: string): void {
        this.themeState.primaryColor = color;
        this.themeState.themeStyle = color;
        this.updateThemeName();
    }

    /**
     * 设置表面颜色
     */
    public setSurface(color: string): void {
        this.themeState.surface = color;
        this.updateThemeName();
    }

    /**
     * 设置CSS变量
     * @param prefix 变量前缀，如 'primary' 或 'surface'
     * @param palette 颜色调色板对象
     * @param textColor 文本颜色，默认为白色
     * @param useContrastText 是否使用对比色作为文本颜色
     */
    private setCSSVariables(
        prefix: string,
        palette: Record<string | number, string>,
        textColor: string = '#ffffff',
        useContrastText: boolean = false
    ): void {
        // 设置基础颜色变量
        const baseColor = palette[500];
        document.documentElement.style.setProperty(`--${prefix}-color`, baseColor);

        // 如果需要对比色，则根据颜色亮度选择黑色或白色文本
        if (useContrastText) {
            // 简单的亮度计算，用于确定文本颜色
            const r = parseInt(baseColor.substring(1, 3), 16);
            const g = parseInt(baseColor.substring(3, 5), 16);
            const b = parseInt(baseColor.substring(5, 7), 16);
            const brightness = (r * 299 + g * 587 + b * 114) / 1000;

            // 亮度大于128时使用黑色文本，否则使用白色文本
            const contrastTextColor = brightness > 129 ? '#000000' : '#ffffff';
            document.documentElement.style.setProperty(`--${prefix}-color-text`, contrastTextColor);
        } else {
            document.documentElement.style.setProperty(`--${prefix}-color-text`, textColor);
        }

        // 设置调色板变量
        for (const [key, value] of Object.entries(palette)) {
            document.documentElement.style.setProperty(`--${prefix}-${key}`, value as string);
        }

        console.log(`[Theme Manager] Set ${prefix} CSS variables with base color ${baseColor}`);
    }

    /**
     * 更新颜色
     * @param type 颜色类型 ('primary' | 'surface')
     * @param colorName 颜色名称 (如 'purple', 'green' 等)
     */
    public updateColors(type: 'primary' | 'surface', colorName: string): void {
        if (type === "primary") {
            this.setPrimaryColor(colorName);

            // 查找颜色配置
            const colorMapping = this.themeState.primaryColors[this.themeState.themeFamily];
            if (!colorMapping) {
                console.warn(`No primary colors defined for theme family ${this.themeState.themeFamily}`);
                return;
            }

            // 获取颜色
            const color = colorMapping[colorName];

            if (color?.palette) {
                // 更新PrimeVue调色板
                updatePrimaryPalette(color.palette);

                // 设置CSS变量，使用对比色文本
                this.setCSSVariables('primary', color.palette, '#ffffff', true);

                // 设置PrimeVue 4按钮变量
                const primaryColor = color.palette[500];
                const primaryTextColor = document.documentElement.style.getPropertyValue('--primary-color-text').trim();
                const primaryHoverColor = color.palette[600];
                const primaryActiveColor = color.palette[700];

                document.documentElement.style.setProperty('--p-button-primary-color', primaryTextColor);
                document.documentElement.style.setProperty('--p-button-primary-background', primaryColor);
                document.documentElement.style.setProperty('--p-button-primary-border-color', primaryColor);
                document.documentElement.style.setProperty('--p-button-primary-hover-background', primaryHoverColor);
                document.documentElement.style.setProperty('--p-button-primary-hover-border-color', primaryHoverColor);
                document.documentElement.style.setProperty('--p-button-primary-active-background', primaryActiveColor);
                document.documentElement.style.setProperty('--p-button-primary-active-border-color', primaryActiveColor);

                console.log(`Updated primary color to ${colorName}`, color.palette[500]);
            } else {
                console.warn(`Primary color ${colorName} not found for theme family ${this.themeState.themeFamily}`);
            }
        } else if (type === "surface") {
            this.setSurface(colorName);

            // 查找表面颜色配置
            const surfaceMapping = this.themeState.surfaces[this.themeState.themeFamily];
            if (!surfaceMapping) {
                console.warn(`No surface colors defined for theme family ${this.themeState.themeFamily}`);
                return;
            }

            // 获取颜色
            const surfaceColor = surfaceMapping[colorName];

            if (surfaceColor?.palette) {
                // 更新PrimeVue表面调色板
                updateSurfacePalette(surfaceColor.palette);

                // 设置CSS变量，表面颜色使用暗色文本
                this.setCSSVariables('surface', {
                    0: surfaceColor.palette[0] || '#ffffff',
                    50: surfaceColor.palette[50],
                    100: surfaceColor.palette[100],
                    200: surfaceColor.palette[200],
                    300: surfaceColor.palette[300],
                    400: surfaceColor.palette[400],
                    500: surfaceColor.palette[500],
                    600: surfaceColor.palette[600],
                    700: surfaceColor.palette[700],
                    800: surfaceColor.palette[800],
                    900: surfaceColor.palette[900],
                    950: surfaceColor.palette[950],
                    'ground': surfaceColor.palette[0],
                    'section': surfaceColor.palette[100],
                    'card': surfaceColor.palette[0] || '#ffffff',
                    'overlay': surfaceColor.palette[0] || '#ffffff',
                    'border': surfaceColor.palette[200],
                    'hover': surfaceColor.palette[100]
                }, '#000000', true);

                console.log(`Updated surface color to ${colorName}`);
            } else {
                console.warn(`Surface color ${colorName} not found for theme family ${this.themeState.themeFamily}`);
            }
        }
    }

    /**
     * 切换主题预设
     * @param themeName 主题家族名称 ('aura' | 'lara')
     */
    public updateThemePresets(themeName: ThemeFamily): void {
        const preset = this.themeState.themePresets[themeName];
        if (preset) {
            usePreset(preset);
            this.themeState.themeFamily = themeName;
            this.updateThemeName();
        } else {
            console.warn(`Theme preset ${themeName} not found`);
        }
    }

    /**
     * 获取主题名称
     */
    public getThemeName(themeFamily: ThemeFamily, themeStyle: ThemeStyle, themeMode: ThemeMode): string {
        return `${themeFamily}-${themeStyle}-${themeMode}`;
    }

    /**
     * 触发主题变更事件
     */
    public dispatchThemeChangedEvent(themeName: string, isDark: boolean): void {
        window.dispatchEvent(new CustomEvent(THEME_CHANGED_EVENT, {
            detail: { theme: themeName, isDark }
        }));
    }

    /**
     * 初始化主题
     * @param theme 主题名称，格式为 'family-style-mode'
     */
    public initializeTheme(theme: string = DEFAULT_THEME_FAMILY + '-' + DEFAULT_THEME_STYLE + '-' + DEFAULT_THEME_MODE): void {
        const parts = this.parseThemeName(theme);

        if (parts) {
            const [family, style, mode] = parts;

            // 先设置主题家族
            this.updateThemePresets(family);

            // 再设置主题颜色
            this.updateColors('primary', style);
            this.updateColors('surface', style);

            // 最后设置主题模式
            this.applyDarkModeClasses(mode === 'dark');

            // 更新主题名称
            this.themeState.themeName = theme;
            this.themeState.themeFamily = family;
            this.themeState.themeStyle = style;
            this.themeState.themeMode = mode;

            // 保存到本地存储，确保刷新后能恢复
            localStorage.setItem('app-theme-family', family);
            localStorage.setItem('app-theme-style', style);
            localStorage.setItem('app-theme-mode', mode);

            // 确保PrimeVue 4按钮变量也被设置
            const primaryColor = document.documentElement.style.getPropertyValue('--primary-color').trim();
            const primaryTextColor = document.documentElement.style.getPropertyValue('--primary-color-text').trim();
            const primaryHoverColor = document.documentElement.style.getPropertyValue('--primary-600').trim();
            const primaryActiveColor = document.documentElement.style.getPropertyValue('--primary-700').trim();

            document.documentElement.style.setProperty('--p-button-primary-color', primaryTextColor || '#ffffff');
            document.documentElement.style.setProperty('--p-button-primary-background', primaryColor || '#a855f7');
            document.documentElement.style.setProperty('--p-button-primary-border-color', primaryColor || '#a855f7');
            document.documentElement.style.setProperty('--p-button-primary-hover-background', primaryHoverColor || '#9333ea');
            document.documentElement.style.setProperty('--p-button-primary-hover-border-color', primaryHoverColor || '#9333ea');
            document.documentElement.style.setProperty('--p-button-primary-active-background', primaryActiveColor || '#7e22ce');
            document.documentElement.style.setProperty('--p-button-primary-active-border-color', primaryActiveColor || '#7e22ce');

            console.log(`[Theme Manager] Theme initialized to ${theme}`);
        } else {
            console.warn(`[Theme Manager] Invalid theme name format: ${theme}, using default`);
            this.initializeTheme();
        }
    }

    /**
     * 主题插件安装函数
     */
    public setupTheme(app: App): void {
        console.log('[Theme Plugin] Initialized with PrimeVue theme system');
    }
}

export default {
    install(app: App) {
        const themeManager = ThemeManager.getInstance();
        themeManager.setupTheme(app);
    }
}; 