import { post } from '@/shared/composables'
import { corePost } from '@/shared/composables'

// 创作者信息接口
export interface Creator {
  id: string
  nickname: string
  avatar: string
  cover: string
  bio: string
  description: string
  gender: number
  birthday: string
  followCount: number
  likeCount: number
  totalAlbums: number
  totalShorts: number
  totalVideos: number
  totalPosts: number
  totalViews: number
  totalComments: number
  totalLikes: number
  totalFollowing: number
  heat: number
  lastActiveTime: string
  status: number
  isFollowed: boolean
  isLiked: boolean
  createdAt: string
}

// 视频信息接口
export interface Video {
  id: string
  title: string
  description?: string
  cover?: string
  url?: string
  duration?: number
  resolution?: string
  view_count?: number
  like_count?: number
  comment_count?: number
  creator_id?: string
  creator_name?: string
  creator_avatar?: string
  category_id?: string
  category_name?: string
  tags?: string[]
  is_paid?: boolean
  price?: number
  status?: number
  created_at?: string
  updated_at?: string
}

// 短视频信息接口
export interface ShortVideo {
  id: string
  title: string
  description?: string
  cover?: string
  url?: string
  duration?: number
  width?: number
  height?: number
  view_count?: number
  like_count?: number
  comment_count?: number
  share_count?: number
  favorite_count?: number
  creator_id?: string
  creator_name?: string
  creator_avatar?: string
  category_id?: string
  category_name?: string
  tags?: string[]
  is_paid?: boolean
  price?: number
  status?: number
  created_at?: string
  updated_at?: string
}

// 帖子信息接口
export interface Post {
  id: string
  title?: string
  content: string
  images?: string[]
  view_count?: number
  like_count?: number
  comment_count?: number
  share_count?: number
  creator_id?: string
  creator_name?: string
  creator_avatar?: string
  category_id?: string
  category_name?: string
  tags?: string[]
  status?: number
  created_at?: string
  updated_at?: string
}

// 专辑接口
export interface Album {
  id: string
  title: string
  description: string
  cover: string
  creatorId: string
  creatorName: string
  creatorAvatar: string
  videoCount: number
  viewCount: number
  likeCount: number
  shareCount: number
  commentCount: number
  isLiked: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
}

// 评论接口
export interface Comment {
  id: string
  content: string
  userId: string
  userName: string
  userAvatar: string
  targetId: string // 被评论的内容ID
  targetType: 'video' | 'shortvideo' | 'post' // 被评论的内容类型
  targetTitle: string // 被评论的内容标题
  targetThumbnail?: string // 被评论的内容缩略图
  parentId?: string // 父评论ID（用于回复）
  likeCount: number
  replyCount: number
  isLiked: boolean
  status: number // 0-待审核 1-已发布 2-已删除
  createdAt: string
  updatedAt: string
}

// 请求参数接口
export interface CreatorContentParams {
  data?: {
    id?: string
    creator_id?: string
    keyword?: string
    category_id?: string
    type?: string
    status?: number
    sort?: 'latest' | 'popular' | 'liked'
  }
  page: {
    pageNo: number
    pageSize: number
  }
}

export interface CreatorDetailParams {
  data: {
    id: string
  }
}

/**
 * 获取创作者详情
 */
export function getCreatorDetail(creatorId: string) {
  return corePost('/creator/getCreatorDetail', { id: creatorId })
}

/**
 * 获取创作者视频列表
 */
export function getCreatorVideos(params: CreatorContentParams) {
  return corePost('/creator/getCreatorVideos', params.data, params.page)
}

/**
 * 获取创作者短视频列表
 */
export function getCreatorShortVideos(params: CreatorContentParams) {
  return corePost('/creator/getCreatorShortVideos', params.data, params.page)
}

/**
 * 获取创作者评论列表
 */
export function getCreatorComments(creatorId: string, params: CreatorContentParams) {
  return corePost('/creator/getCreatorComments', {
    ...params.data,
    creator_id: creatorId
  }, params.page)
}

/**
 * 获取创作者帖子列表
 */
export function getCreatorPosts(params: CreatorContentParams) {
  return corePost('/creator/getCreatorPosts', params.data, params.page)
}

/**
 * 获取创作者专辑列表
 */
export function getCreatorAlbums(params: CreatorContentParams) {
  return corePost('/creator/getCreatorAlbums', params.data, params.page)
}

/**
 * 关注/取消关注创作者
 */
export function followCreator(creatorId: string) {
  return corePost('/creator/followCreator', { creator_id: creatorId })
}

/**
 * 检查是否已关注创作者
 */
export function checkFollowStatus(creatorId: string) {
  return corePost('/creator/checkFollowStatus', { creator_id: creatorId })
}

/**
 * 获取创作者统计数据
 */
export function getCreatorStats(creatorId: string) {
  return corePost('/creator/stats', { id: creatorId })
}

/**
 * 获取专辑详情
 * @param albumId 专辑ID
 */
export function getAlbumDetail(albumId: string) {
  return corePost('/video/albums/getAlbumDetail', { id: albumId })
}

/**
 * 获取专辑中的视频列表
 * @param params 查询参数
 */
export function getAlbumVideos(params: CreatorContentParams) {
  return corePost('/video/albums/getAlbumVideoList', params.data, params.page)
}