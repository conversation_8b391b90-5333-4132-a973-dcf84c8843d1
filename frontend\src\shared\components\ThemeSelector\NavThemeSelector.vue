<template>
    <div class="nav-theme-selector">
        <Button 
            ref="themeToggleRef"
            class="nav-theme-toggle bg-gradient-to-r from-primary-400 to-primary-500 dark:from-primary-700 dark:to-primary-800 text-surface-50 rounded-full p-button-sm" 
            @click="toggleThemePanel"
            :aria-label="t('theme.selectTheme')" 
            :title="t('theme.selectTheme')">
            <div class="theme-button-content">
                <div class="theme-color-indicator" :style="{ backgroundColor: currentThemeColor }"></div>
                <span class="theme-name">{{ displayThemeName }}</span>
            </div>
        </Button>

        <!-- 使用 PrimeVue Popover 组件 -->
        <Popover ref="popoverRef" class="theme-popover bg-surface-0 dark:bg-surface-800 border border-surface-200 dark:border-surface-700 shadow-md">
            <div class="theme-panel">
                <div class="theme-panel-header bg-gradient-to-r from-surface-50 to-surface-100 dark:from-surface-700 dark:to-surface-800 text-color dark:text-surface-50">
                    <div class="theme-panel-title">
                        <i class="pi pi-palette mr-2 text-primary-500 dark:text-primary-400"></i>
                        {{ t('theme.themeSettings') }}
                    </div>
                    <Button 
                        @click="hideThemePanel" 
                        icon="pi pi-times"
                        class="p-button-rounded p-button-text p-button-sm close-btn text-surface-700 dark:text-surface-100" />
                </div>

                <div class="theme-panel-content">
                    <!-- 主题家族选择 -->
                    <div class="theme-family-selector">
                        <div class="family-title text-surface-700 dark:text-surface-200">{{ t('theme.themeFamily') }}</div>
                        <div class="family-buttons">
                            <Button v-for="family in themeFamilies" 
                                :key="family" 
                                :class="['family-button', { 'active': currentThemeFamily === family }]"
                                @click="selectThemeFamily(family)">
                                {{ family }}
                            </Button>
                        </div>
                    </div>

                    <!-- 主题颜色选择 -->
                    <div class="theme-colors">
                        <div class="colors-title text-surface-700 dark:text-surface-200">{{ t('theme.themeColor') }}</div>
                        <div class="colors-grid">
                            <div v-for="style in themeStyles" :key="style.code" class="theme-color-item hover:bg-surface-100 dark:hover:bg-surface-700"
                                :class="{ 'active bg-surface-200 dark:bg-surface-600': currentThemeStyle === style.code }" 
                                @click="selectThemeStyle(style.code)"
                                :title="style.displayName">
                                <div class="theme-color-circle" 
                                    :style="{ backgroundColor: isDark ? style.darkBgColor : style.lightBgColor }">
                                    <i v-if="currentThemeStyle === style.code" class="pi pi-check"></i>
                                </div>
                                <span class="theme-color-name text-color dark:text-surface-50">{{ style.displayName }}</span>
                            </div>
                        </div>
                    </div>
                    <!-- 主题设置选项 -->
                    <div class="theme-options">
                        <div class="option-item">
                            <Checkbox v-model="darkMode" binary inputId="nav-dark-mode" />
                           
                            <label for="nav-dark-mode" class="option-label ml-2 text-color dark:text-surface-50">
                               <i class="pi pi-moon mr-2 text-primary-500 dark:text-primary-400" v-if="!isDark"></i>
                               <i class="pi pi-sun mr-2 text-primary-500 dark:text-primary-400" v-else-if="isDark"></i>
                                {{ t('theme.darkMode') }}
                            </label>
                        </div>

                        <div class="option-item">
                            <Checkbox v-model="followSystem" binary inputId="nav-follow-system" />
                            <label for="nav-follow-system" class="option-label ml-2 text-color dark:text-surface-50">
                                <i class="pi pi-desktop mr-2 text-primary-500 dark:text-primary-400"></i>
                                {{ t('theme.followSystem') }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </Popover>
    </div>
</template>

<script setup lang="ts">

import { useThemeStore } from '@/store/modules/theme';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import Popover from 'primevue/popover';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

// i18n
const { t } = useI18n();

// 使用Pinia主题存储
const themeStore = useThemeStore();

// 组件引用
const themeToggleRef = ref();
const popoverRef = ref();

// 可用的主题家族
const themeFamilies = computed(() => themeStore.getAvailableThemes());

// 可用的主题颜色
const themeStyles = computed(() => themeStore.getThemeStyles());

// 是否为深色模式
const isDark = computed(() => themeStore.isDark);

// 当前主题家族
const currentThemeFamily = computed(() => themeStore.currentThemeFamily);

// 当前主题样式
const currentThemeStyle = computed(() => themeStore.currentThemeStyle);

// 当前主题名称
const currentThemeName = computed(() => themeStore.currentThemeName);

// 是否跟随系统主题
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 深色模式切换
const darkMode = computed({
    get: () => themeStore.isDark,
    set: () => themeStore.toggleDarkMode()
});

// 获取当前主题颜色
const currentThemeColor = computed(() => {
    const style = themeStyles.value.find(s => s.code === currentThemeStyle.value);
    return isDark.value ? style?.darkBgColor : style?.lightBgColor;
});

// 获取当前主题显示名称
const displayThemeName = computed(() => {
    const style = themeStyles.value.find(s => s.code === currentThemeStyle.value);
    return style?.displayName || currentThemeStyle.value;
});

// 切换主题面板
const toggleThemePanel = (event: Event) => {
    popoverRef.value.toggle(event);
};

// 隐藏主题面板
const hideThemePanel = () => {
    popoverRef.value.hide();
};

// 选择主题家族
const selectThemeFamily = (family: string) => {
    themeStore.setThemeFamily(family as any);
};

// 选择主题样式
const selectThemeStyle = (style: string) => {
    themeStore.setThemeStyle(style);
    hideThemePanel();
};
</script>

<style scoped lang="scss">
.nav-theme-selector {
    position: relative;
    display: inline-block;
}

.nav-theme-toggle {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
        transform: translateY(0);
    }
    
    .theme-name {
        font-size: 0.875rem;
        font-weight: 500;
        white-space: nowrap;
        color: inherit;
    }
}

.theme-button-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-color-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--surface-border);
    flex-shrink: 0;
}

.theme-popover {
    min-width: 280px;
    border-radius: 0.5rem;
    overflow: hidden;
    
    .p-popover {
        width: 320px;
        padding: 0;
    }
}

.theme-panel {
    width: 100%;
    overflow: hidden;
    
    .theme-panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--surface-border);
    }
}

.theme-panel-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.theme-panel-content {
    padding: 16px;
}

.theme-family-selector {
    margin-bottom: 16px;
}

.family-title, .colors-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.family-buttons {
    display: flex;
    gap: 8px;
}

.family-button {
    flex: 1;
    justify-content: center;
    background-color: var(--surface-card);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    
    &.active {
        background-color: var(--primary-color) !important;
        color: var(--primary-color-text) !important;
    }
    
    &:hover {
        background-color: var(--primary-200) !important;
    }
}

.colors-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.theme-color-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 8px 4px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.theme-color-circle {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--surface-border);
    
    i {
        color: white;
        font-size: 0.75rem;
        text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    }
}

.theme-color-name {
    font-size: 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    text-align: center;
}

.theme-options {
    padding-top: 16px;
    border-top: 1px solid var(--surface-border);
}

.option-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.option-label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.p-popover-content {
    min-width: 280px;
}
</style>