<template>
    <div class="nav-theme-selector">
        <!-- 主题切换按钮 -->
        <Button
            class="nav-theme-toggle"
            text
            @click="toggleThemePanel"
        >
            <div class="theme-button-content">
                <div class="theme-color-indicator" :style="{ backgroundColor: currentThemeColor }"></div>
                <span class="theme-name">{{ currentThemeName }}</span>
                <i class="pi pi-angle-down"></i>
            </div>
        </Button>
        
        <!-- 主题面板 -->
        <Teleport to="body">
            <div v-if="showThemePanel" class="theme-overlay" @click="closeThemePanel"></div>
            <div v-if="showThemePanel" class="theme-panel" :style="panelStyle" ref="themePanel">
                <div class="theme-panel-header">
                    <h3 class="theme-panel-title">
                        <i class="pi pi-palette mr-2"></i>
                        主题设置
                    </h3>
                    <Button icon="pi pi-times" text class="close-btn" @click="closeThemePanel" />
                </div>
                
                <div class="theme-panel-content">
                    <!-- 主题家族选择 -->
                    <div class="theme-family-selector">
                        <div class="family-title">主题风格</div>
                        <div class="family-buttons">
                            <Button 
                                v-for="family in themeFamilies" 
                                :key="family.value"
                                :label="family.label"
                                :class="['family-button', { active: currentThemeFamily === family.value }]"
                                text
                                @click="setThemeFamily(family.value)"
                            />
                        </div>
                    </div>
                    
                    <!-- 主题颜色选择 -->
                    <div class="theme-colors">
                        <div
                            v-for="color in availableColorSchemes"
                            :key="color"
                            :class="['theme-color-item', { active: currentColorScheme === color }]"
                            @click="setColorScheme(color)"
                        >
                            <div class="theme-color-circle" :style="{ backgroundColor: getColorForScheme(color) }">
                                <i v-if="currentColorScheme === color" class="pi pi-check"></i>
                            </div>
                            <div class="theme-color-name">{{ formatColorName(color) }}</div>
                        </div>
                    </div>
                    
                    <!-- 主题选项 -->
                    <div class="theme-options">
                        <div class="option-item">
                            <label class="option-label flex items-center">
                                <InputSwitch v-model="isDarkMode" class="mr-2" />
                                <span>暗色模式</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </Teleport>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useThemeStore } from '@/store/modules/theme';
import { getUniqueColorSchemes, themeColorGroups } from '@/shared/themes';
import { THEME_COLORS } from '@/config/theme.config';
import Button from 'primevue/button';
import InputSwitch from 'primevue/inputswitch';

// 状态
const showThemePanel = ref(false);
const themePanel = ref<HTMLElement>();
const panelStyle = ref({});
const themeStore = useThemeStore();
const isDarkMode = computed({
    get: () => themeStore.isDarkMode,
    set: (value) => themeStore.setDarkMode(value)
});

// 计算属性
const currentThemeFamily = computed(() => themeStore.currentThemeFamily);
const currentColorScheme = computed(() => themeStore.currentColorScheme);
const currentThemeName = computed(() => {
    const family = themeFamilies.find(f => f.value === currentThemeFamily.value)?.label || '';
    const color = formatColorName(currentColorScheme.value);
    return `${family} ${color}`;
});
const currentThemeColor = computed(() => {
    const colorConfig = THEME_COLORS[currentColorScheme.value as keyof typeof THEME_COLORS];
    return colorConfig ? (isDarkMode.value ? colorConfig.dark : colorConfig.light) : '#3B82F6';
});
const availableColorSchemes = computed(() => {
    return getUniqueColorSchemes(currentThemeFamily.value);
});

// 主题家族选项
const themeFamilies = [
    { label: 'Aura', value: 'aura' },
    { label: 'Lara', value: 'lara' },
    { label: 'Material', value: 'md' }
];

// 方法
const toggleThemePanel = async (event: Event) => {
    showThemePanel.value = !showThemePanel.value;
    
    if (showThemePanel.value) {
        await nextTick();
        updatePanelPosition(event.target as HTMLElement);
    }
};

const updatePanelPosition = (buttonElement: HTMLElement) => {
    const button = buttonElement.closest('.nav-theme-toggle') as HTMLElement;
    if (!button) return;
    
    const rect = button.getBoundingClientRect();
    const panelWidth = 320; // 面板宽度
    const panelHeight = 400; // 预估面板高度
    const gap = 8; // 间距
    
    let top = rect.bottom + gap;
    let left = rect.right - panelWidth;
    
    // 确保面板不会超出视窗
    if (left < 10) {
        left = 10;
    }
    if (top + panelHeight > window.innerHeight - 10) {
        top = rect.top - panelHeight - gap;
    }
    
    panelStyle.value = {
        position: 'fixed',
        top: `${top}px`,
        left: `${left}px`,
        zIndex: 1000
    };
};

const closeThemePanel = () => {
    showThemePanel.value = false;
};

const setThemeFamily = (family: string) => {
    themeStore.setThemeFamily(family);
    
    // 如果当前颜色方案在新主题家族中不可用，则选择第一个可用的颜色方案
    const availableSchemes = getUniqueColorSchemes(family);
    if (!availableSchemes.includes(currentColorScheme.value)) {
        themeStore.setColorScheme(availableSchemes[0]);
    }
};

const setColorScheme = (scheme: string) => {
    themeStore.setColorScheme(scheme);
};

const formatColorName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
};

const getColorForScheme = (scheme: string) => {
    const colorConfig = THEME_COLORS[scheme as keyof typeof THEME_COLORS];
    return colorConfig ? (isDarkMode.value ? colorConfig.dark : colorConfig.light) : '#3B82F6';
};

// 点击外部关闭面板
const handleClickOutside = (event: Event) => {
    if (!showThemePanel.value) return;
    
    const target = event.target as Node;
    const themeSelector = document.querySelector('.nav-theme-selector');
    const panel = themePanel.value;
    
    // 如果点击的是主题选择器或面板内部，则不关闭
    if ((themeSelector && themeSelector.contains(target)) || 
        (panel && panel.contains(target))) {
        return;
    }
    
    showThemePanel.value = false;
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        showThemePanel.value = false;
    }
};

// 窗口大小变化时重新定位面板
const handleResize = () => {
    if (showThemePanel.value) {
        const button = document.querySelector('.nav-theme-toggle') as HTMLElement;
        if (button) {
            updatePanelPosition(button);
        }
    }
};

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeydown);
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
});

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeydown);
    window.removeEventListener('resize', handleResize);
    window.removeEventListener('scroll', handleResize);
});

// 监听暗色模式变化
watch(isDarkMode, (newValue) => {
    if (newValue) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
});
</script>

<style scoped lang="scss">
.nav-theme-selector {
    position: relative;
    display: inline-block;
}

.nav-theme-toggle {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    border-radius: 50px;
    color: var(--header-text, var(--text-color)) !important;
    /* 添加溢出隐藏 */
    transition: background-color 0.2s ease;
    
    .theme-name {
        font-size: 0.875rem;
        font-weight: 500;
        white-space: nowrap;
        color: inherit;
    }
    
    &:hover {
        background-color: var(--surface-hover) !important;
    }

    /* 限制水波纹大小 */
    :deep(.p-ink) {
        height: 100% !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
        transform: scale(0.8);
    }
}

.theme-button-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-color-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--surface-border);
    flex-shrink: 0;
}

.theme-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.theme-panel {
    width: 320px;
    background: var(--p-panel-background, var(--surface-card));
    border: 1px solid var(--p-panel-border-color, var(--surface-border));
    border-radius: var(--p-panel-border-radius, var(--border-radius));
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    animation: slideDown 0.3s ease;
}

.theme-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid var(--p-panel-border-color, var(--surface-border));
    background: var(--p-panel-header-bg, var(--surface-section));
    .close-btn {
         color: var(--p-panel-header-text, var(--text-color));
    }
}

.theme-panel-title {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--p-panel-header-text, var(--text-color));
    display: flex;
    align-items: center;
}

.theme-panel-content {
    padding: 1rem;
    background: var(--p-panel-content-bg, var(--surface-overlay));
    color: var(--p-panel-content-text, var(--text-color));
}

.theme-family-selector {
    margin-bottom: 1rem;
    
    .family-title {
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-color);
    }
    
    .family-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        
        .family-button {
            font-size: 0.8rem;
            padding: 0.3rem 0.7rem;
            border-radius: 1rem;
            
            &.active {
                background-color: var(--primary-color);
                color: var(--primary-color-text);
            }
        }
    }
}

.theme-colors {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.theme-color-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: translateY(-2px);
    }

    &.active .theme-color-circle {
        box-shadow: 0 0 0 2px var(--primary-color);
    }
}

.theme-color-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 1px solid var(--surface-border);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.theme-color-name {
    font-size: 0.75rem;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    color: var(--text-color);
}

.theme-options {
    margin-top: 1.5rem;
    border-top: 1px solid var(--surface-border);
    padding-top: 1rem;
}

.option-item {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;

    .option-label {
        cursor: pointer;
        display: flex;
        align-items: center;
        color: var(--text-color);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>