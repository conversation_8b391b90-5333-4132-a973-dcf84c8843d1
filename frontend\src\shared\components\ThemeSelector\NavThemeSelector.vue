<template>
    <div class="nav-theme-selector">
        <Button class="nav-theme-toggle rounded p-button-sm" @click="toggleThemePanel"
            :aria-label="t('theme.selectTheme')" :title="t('theme.selectTheme')">
            <div class="theme-button-content">
                <div class="theme-color-indicator" :style="{ backgroundColor: currentThemeColor }"></div>
                <span class="theme-name">{{ currentThemeName }}</span>
            </div>
        </Button>

        <!-- 主题选择面板 -->
        <div v-show="showThemePanel" class="theme-panel" @click.stop>
            <div class="theme-panel-header">
                <h3 class="theme-panel-title">
                    <i class="pi pi-palette mr-2"></i>
                    {{ t('theme.themeSettings') }}
                </h3>
                <Button @click="showThemePanel = false" icon="pi pi-times"
                    class="p-button-rounded p-button-text p-button-sm close-btn" />
            </div>

            <div class="theme-panel-content">
                <!-- 主题颜色圆形按钮列表 -->
                <div class="theme-colors">
                    <div v-for="theme in availableThemes" :key="theme.name" class="theme-color-item"
                        :class="{ 'active': currentTheme === theme.name }" @click="selectTheme(theme.name)"
                        :title="t(`theme.${theme.code}`)">
                        <div class="theme-color-circle" :style="{ backgroundColor: theme.primary || '#4361ee' }">
                            <i v-if="currentTheme === theme.name" class="pi pi-check"></i>
                        </div>
                        <span class="theme-color-name">{{ t(`theme.${theme.code}`) }}</span>
                    </div>
                </div>

                <!-- 主题设置选项 -->
                <div class="theme-options">
                    <div class="option-item">
                        <div class="flex align-items-center">
                            <Checkbox v-model="darkMode" binary inputId="nav-dark-mode" />
                            <label for="nav-dark-mode" class="option-label ml-2">
                                {{ t('theme.darkMode') }}
                            </label>
                        </div>
                    </div>

                    <div class="option-item">
                        <div class="flex align-items-center">
                            <Checkbox v-model="followSystem" binary inputId="nav-follow-system" />
                            <label for="nav-follow-system" class="option-label ml-2">
                                <i class="pi pi-desktop mr-2"></i>
                                {{ t('theme.followSystem') }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 遮罩层 -->
        <div v-show="showThemePanel" class="theme-overlay" @click="showThemePanel = false"></div>
    </div>
</template>

<script setup lang="ts">
import type { ThemeConfig } from '@/shared/themes/theme-manager';
import { themeManager } from '@/shared/themes/theme-manager';
import { useThemeStore } from '@/store/modules/theme';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

// i18n
const { t } = useI18n();

// 使用Pinia主题存储
const themeStore = useThemeStore();

// 面板显示状态
const showThemePanel = ref(false);

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme);

// 可用主题列表
const availableThemes = computed(() => themeManager.availableThemes.value as ThemeConfig[]);

// 是否为深色模式
const darkMode = computed({
    get: () => themeStore.isDark,
    set: () => themeStore.toggleDarkMode()
});

// 是否跟随系统主题
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 获取当前主题颜色
const currentThemeColor = computed(() => {
    const theme = availableThemes.value.find((t: any) => t.name === currentTheme.value);
    return theme?.primary || '#4361ee';
});

// 获取当前主题名称
const currentThemeName = computed(() => {
    const theme = availableThemes.value.find(t => t.name === currentTheme.value);
    if (theme) {
        // 使用displayName或code
        return theme.displayName || t(`theme.${theme.code}`);
    }
    return t('theme.default');
});

// 切换主题面板
const toggleThemePanel = () => {
    showThemePanel.value = !showThemePanel.value;
};

// 选择主题
const selectTheme = (themeName: string) => {
    themeStore.setTheme(themeName as any); // 类型转换解决TypeScript问题
    showThemePanel.value = false;
};

// 点击外部关闭面板
const handleClickOutside = (event: MouseEvent) => {
    const navThemeSelector = document.querySelector('.nav-theme-selector');
    if (navThemeSelector && !navThemeSelector.contains(event.target as Node)) {
        showThemePanel.value = false;
    }
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        showThemePanel.value = false;
    }
};

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeydown);

    // 添加调试日志
    console.log('[NavThemeSelector] Mounted, current theme:', currentTheme.value);
});

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped lang="scss">
.nav-theme-selector {
    position: relative;
    display: inline-block;
}

.nav-theme-toggle {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    border-radius: 50px;
    color: var(--nav-text, var(--primary-color-text, var(--color-text-contrast, white)));
    /* 添加溢出隐藏 */
    transition: background-color 0.2s ease;
    
    .theme-name {
        font-size: 0.875rem;
        font-weight: 500;
        white-space: nowrap;
        color: inherit;
    }
    
    &:hover {
        background-color: var(--surface-hover) !important;
        color: var(--primary-color) !important;
    }

    /* 限制水波纹大小 */
    :deep(.p-ink) {
        height: 100% !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
        transform: scale(0.8);
    }
}

.theme-button-content {
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-color-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--surface-border, var(--color-border, #e2e8f0));
    flex-shrink: 0;
}



.theme-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.theme-panel {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 280px;
    background: var(--surface-card, var(--color-background, #ffffff));
    border: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    border-radius: 0.25rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    overflow: hidden;
    animation: slideDown 0.3s ease;
}

.theme-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    background: var(--surface-section, var(--surface-ground, var(--surface-100)));
    .close-btn {
         color: var(--text-primary, var(--color-primary, #111827));
    }
}

.theme-panel-title {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary, var(--color-primary, #111827));
    display: flex;
    align-items: center;
}

.theme-panel-content {
    padding: 0.75rem;
    color: var(--text-secondary, var(--color-secondary, #6b7280));
}

.theme-colors {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.theme-color-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: translateY(-2px);
    }

    &.active .theme-color-circle {
        box-shadow: 0 0 0 2px var(--color-primary, #3b82f6);
    }
}

.theme-color-circle {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.theme-color-name {
    font-size: 0.7rem;
    text-align: center;
}

.theme-options {
    margin-top: 1rem;
    border-top: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    padding-top: 0.75rem;
}

.option-item {
    padding: 0.4rem 0;

    .option-label {
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 0.8rem;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>