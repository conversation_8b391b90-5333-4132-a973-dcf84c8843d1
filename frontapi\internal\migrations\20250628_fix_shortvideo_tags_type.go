package migrations

import (
	"gorm.io/gorm"
)

// FixShortVideoTagsType 修复短视频表中tags字段的数据类型
func FixShortVideoTagsType(db *gorm.DB) error {
	// 更新ly_shorts表中的tags字段类型从string改为json
	err := db.Exec(`
		ALTER TABLE ly_shorts 
		MODIFY COLUMN tags JSON COMMENT '标签'
	`).Error

	if err != nil {
		return err
	}

	return nil
}

// RollbackFixShortVideoTagsType 回滚短视频表中tags字段的数据类型修改
func RollbackFixShortVideoTagsType(db *gorm.DB) error {
	// 回滚：将tags字段改回varchar类型
	err := db.Exec(`
		ALTER TABLE ly_shorts 
		MODIFY COLUMN tags VARCHAR(255) COMMENT '标签'
	`).Error

	if err != nil {
		return err
	}

	return nil
}
