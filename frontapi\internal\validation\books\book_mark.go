package books

// 请求和响应结构体
type CreateBookmarkRequest struct {
	UserID    string `json:"userId" validate:"required"`
	BookID    string `json:"bookId" validate:"required"`
	ChapterID string `json:"chapterId" validate:"required"`
	Position  int    `json:"position" validate:"required"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	BookCover string `json:"book_cover"`
	Author    string `json:"author"`
}

type UpdateBookmarkRequest struct {
	Title    string `json:"title"`
	Content  string `json:"content"`
	Position int    `json:"position"`
}
