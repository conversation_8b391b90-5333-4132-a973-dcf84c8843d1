<template>
  <div 
    ref="playerContainer"
    class="video-player"
    :class="{
      'video-player--fullscreen': isFullscreen,
      'video-player--loading': loading
    }"
    @mouseenter="showControls = true"
    @mouseleave="handleMouseLeave"
    @mousemove="handleMouseMove"
  >
    <!-- 视频元素 -->
    <video
      ref="videoRef"
      class="video-player__video"
      :src="src"
      :poster="poster"
      :muted="muted"
      :autoplay="autoplay"
      :loop="loop"
      :preload="preload"
      @loadstart="handleLoadStart"
      @loadeddata="handleLoadedData"
      @canplay="handleCanPlay"
      @play="handlePlay"
      @pause="handlePause"
      @ended="handleEnded"
      @timeupdate="handleTimeUpdate"
      @volumechange="handleVolumeChange"
      @error="handleError"
      @click="togglePlay"
    ></video>
    
    <!-- 加载指示器 -->
    <div v-if="loading" class="video-player__loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
    </div>
    
    <!-- 播放按钮覆盖层 -->
    <div 
      v-if="!isPlaying && !loading"
      class="video-player__play-overlay"
      @click="play"
    >
      <el-icon class="video-player__play-icon">
        <VideoPlay />
      </el-icon>
    </div>
    
    <!-- 控制栏 -->
    <div 
      v-show="showControls || !isPlaying"
      class="video-player__controls"
      :class="{ 'video-player__controls--visible': showControls }"
    >
      <!-- 进度条 -->
      <div class="video-player__progress-container">
        <div 
          ref="progressRef"
          class="video-player__progress"
          @click="handleProgressClick"
          @mousedown="handleProgressMouseDown"
        >
          <div 
            class="video-player__progress-bar"
            :style="{ width: progressPercentage + '%' }"
          ></div>
          <div 
            class="video-player__progress-thumb"
            :style="{ left: progressPercentage + '%' }"
          ></div>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="video-player__controls-bar">
        <!-- 左侧控制 -->
        <div class="video-player__controls-left">
          <!-- 播放/暂停 -->
          <button 
            class="video-player__control-btn"
            @click="togglePlay"
          >
            <el-icon>
              <VideoPlay v-if="!isPlaying" />
              <VideoPause v-else />
            </el-icon>
          </button>
          
          <!-- 音量控制 -->
          <div class="video-player__volume">
            <button 
              class="video-player__control-btn"
              @click="toggleMute"
            >
              <el-icon>
                <Mute v-if="isMuted || volume === 0" />
                <VideoCamera v-else />
              </el-icon>
            </button>
            <div 
              v-show="showVolumeSlider"
              class="video-player__volume-slider"
              @mouseenter="showVolumeSlider = true"
              @mouseleave="showVolumeSlider = false"
            >
              <input
                v-model="volume"
                type="range"
                min="0"
                max="1"
                step="0.1"
                class="video-player__volume-input"
                @input="handleVolumeInput"
              />
            </div>
          </div>
          
          <!-- 时间显示 -->
          <div class="video-player__time">
            <span>{{ formatTime(currentTime) }}</span>
            <span>/</span>
            <span>{{ formatTime(duration) }}</span>
          </div>
        </div>
        
        <!-- 右侧控制 -->
        <div class="video-player__controls-right">
          <!-- 播放速度 -->
          <div class="video-player__speed">
            <button 
              class="video-player__control-btn"
              @click="showSpeedMenu = !showSpeedMenu"
            >
              <span class="video-player__speed-text">{{ playbackRate }}x</span>
            </button>
            <div 
              v-show="showSpeedMenu"
              class="video-player__speed-menu"
            >
              <button
                v-for="rate in speedOptions"
                :key="rate"
                class="video-player__speed-option"
                :class="{ 'active': playbackRate === rate }"
                @click="setPlaybackRate(rate)"
              >
                {{ rate }}x
              </button>
            </div>
          </div>
          
          <!-- 画中画 -->
          <button 
            v-if="supportsPiP"
            class="video-player__control-btn"
            @click="togglePictureInPicture"
          >
            <el-icon>
              <Monitor />
            </el-icon>
          </button>
          
          <!-- 全屏 -->
          <button 
            class="video-player__control-btn"
            @click="toggleFullscreen"
          >
            <el-icon>
              <FullScreen v-if="!isFullscreen" />
              <Aim v-else />
            </el-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  Loading,
  VideoPlay,
  VideoPause,
  Mute,
  VideoCamera,
  Monitor,
  FullScreen,
  Aim
} from '@element-plus/icons-vue'
import { formatDuration, formatTime } from '@/core/utils'

export interface VideoPlayerProps {
  src: string
  poster?: string
  autoplay?: boolean
  loop?: boolean
  muted?: boolean
  preload?: 'none' | 'metadata' | 'auto'
  controls?: boolean
}

const props = withDefaults(defineProps<VideoPlayerProps>(), {
  autoplay: false,
  loop: false,
  muted: false,
  preload: 'metadata',
  controls: true
})

const emit = defineEmits<{
  play: []
  pause: []
  ended: []
  timeupdate: [currentTime: number]
  volumechange: [volume: number]
  error: [error: Event]
}>()

// 引用
const playerContainer = ref<HTMLElement>()
const videoRef = ref<HTMLVideoElement>()
const progressRef = ref<HTMLElement>()

// 播放状态
const isPlaying = ref(false)
const loading = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(false)
const playbackRate = ref(1)

// UI状态
const showControls = ref(false)
const showVolumeSlider = ref(false)
const showSpeedMenu = ref(false)
const isFullscreen = ref(false)
const controlsTimer = ref<NodeJS.Timeout | null>(null)

// 功能支持检测
const supportsPiP = computed(() => {
  return 'pictureInPictureEnabled' in document
})

// 进度百分比
const progressPercentage = computed(() => {
  return duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
})

// 播放速度选项
const speedOptions = [0.5, 0.75, 1, 1.25, 1.5, 2]

// 使用统一的工具函数

// 播放控制
const play = async () => {
  if (videoRef.value) {
    try {
      await videoRef.value.play()
    } catch (error) {
      console.error('播放失败:', error)
    }
  }
}

const pause = () => {
  if (videoRef.value) {
    videoRef.value.pause()
  }
}

const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

// 音量控制
const toggleMute = () => {
  if (videoRef.value) {
    videoRef.value.muted = !videoRef.value.muted
  }
}

const handleVolumeInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const newVolume = parseFloat(target.value)
  if (videoRef.value) {
    videoRef.value.volume = newVolume
  }
}

// 播放速度控制
const setPlaybackRate = (rate: number) => {
  if (videoRef.value) {
    videoRef.value.playbackRate = rate
    playbackRate.value = rate
    showSpeedMenu.value = false
  }
}

// 进度控制
const handleProgressClick = (event: MouseEvent) => {
  if (progressRef.value && videoRef.value && duration.value > 0) {
    const rect = progressRef.value.getBoundingClientRect()
    const clickX = event.clientX - rect.left
    const percentage = clickX / rect.width
    const newTime = percentage * duration.value
    videoRef.value.currentTime = newTime
  }
}

const handleProgressMouseDown = (event: MouseEvent) => {
  const handleMouseMove = (e: MouseEvent) => {
    handleProgressClick(e)
  }
  
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 全屏控制
const toggleFullscreen = async () => {
  if (!document.fullscreenElement) {
    if (playerContainer.value?.requestFullscreen) {
      await playerContainer.value.requestFullscreen()
    }
  } else {
    if (document.exitFullscreen) {
      await document.exitFullscreen()
    }
  }
}

// 画中画控制
const togglePictureInPicture = async () => {
  if (videoRef.value) {
    if (document.pictureInPictureElement) {
      await document.exitPictureInPicture()
    } else {
      await videoRef.value.requestPictureInPicture()
    }
  }
}

// 控制栏显示/隐藏
const hideControlsAfterDelay = () => {
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  controlsTimer.value = setTimeout(() => {
    if (isPlaying.value) {
      showControls.value = false
    }
  }, 3000)
}

const handleMouseMove = () => {
  showControls.value = true
  hideControlsAfterDelay()
}

const handleMouseLeave = () => {
  if (isPlaying.value) {
    hideControlsAfterDelay()
  }
}

// 视频事件处理
const handleLoadStart = () => {
  loading.value = true
}

const handleLoadedData = () => {
  loading.value = false
  if (videoRef.value) {
    duration.value = videoRef.value.duration
  }
}

const handleCanPlay = () => {
  loading.value = false
}

const handlePlay = () => {
  isPlaying.value = true
  emit('play')
  hideControlsAfterDelay()
}

const handlePause = () => {
  isPlaying.value = false
  emit('pause')
  showControls.value = true
}

const handleEnded = () => {
  isPlaying.value = false
  emit('ended')
  showControls.value = true
}

const handleTimeUpdate = () => {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime
    emit('timeupdate', currentTime.value)
  }
}

const handleVolumeChange = () => {
  if (videoRef.value) {
    volume.value = videoRef.value.volume
    isMuted.value = videoRef.value.muted
    emit('volumechange', volume.value)
  }
}

const handleError = (error: Event) => {
  loading.value = false
  emit('error', error)
}

// 全屏状态监听
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (!playerContainer.value?.contains(event.target as Node)) return
  
  switch (event.code) {
    case 'Space':
      event.preventDefault()
      togglePlay()
      break
    case 'KeyF':
      event.preventDefault()
      toggleFullscreen()
      break
    case 'KeyM':
      event.preventDefault()
      toggleMute()
      break
    case 'ArrowLeft':
      event.preventDefault()
      if (videoRef.value) {
        videoRef.value.currentTime = Math.max(0, currentTime.value - 10)
      }
      break
    case 'ArrowRight':
      event.preventDefault()
      if (videoRef.value) {
        videoRef.value.currentTime = Math.min(duration.value, currentTime.value + 10)
      }
      break
  }
}

// 监听音量变化
watch(volume, (newVolume) => {
  if (videoRef.value) {
    videoRef.value.volume = newVolume
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('keydown', handleKeydown)
  
  // 初始化音量
  if (videoRef.value) {
    volume.value = videoRef.value.volume
    isMuted.value = videoRef.value.muted
  }
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('keydown', handleKeydown)
  
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
})

// 暴露方法
defineExpose({
  play,
  pause,
  togglePlay,
  toggleFullscreen,
  togglePictureInPicture,
  setCurrentTime: (time: number) => {
    if (videoRef.value) {
      videoRef.value.currentTime = time
    }
  },
  getCurrentTime: () => currentTime.value,
  getDuration: () => duration.value,
  setVolume: (vol: number) => {
    volume.value = vol
  },
  getVolume: () => volume.value
})
</script>

<style scoped>
.video-player {
  @apply relative bg-black rounded-lg overflow-hidden;
  aspect-ratio: 16/9;
}

.video-player__video {
  @apply w-full h-full object-contain;
}

.video-player__loading {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-50;
}

.video-player__play-overlay {
  @apply absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 cursor-pointer;
}

.video-player__play-icon {
  @apply text-white text-6xl;
}

.video-player__controls {
  @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-player__controls--visible {
  opacity: 1;
}

.video-player__progress-container {
  @apply mb-4;
}

.video-player__progress {
  @apply relative h-1 bg-white bg-opacity-30 rounded cursor-pointer;
}

.video-player__progress-bar {
  @apply h-full bg-blue-500 rounded transition-all duration-100;
}

.video-player__progress-thumb {
  @apply absolute top-1/2 w-3 h-3 bg-blue-500 rounded-full transform -translate-y-1/2 -translate-x-1/2;
}

.video-player__controls-bar {
  @apply flex items-center justify-between;
}

.video-player__controls-left,
.video-player__controls-right {
  @apply flex items-center gap-2;
}

.video-player__control-btn {
  @apply text-white hover:text-blue-400 transition-colors duration-200 p-2;
}

.video-player__volume {
  @apply relative flex items-center;
}

.video-player__volume-slider {
  @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-black bg-opacity-75 rounded;
}

.video-player__volume-input {
  @apply w-20;
}

.video-player__time {
  @apply text-white text-sm flex items-center gap-1;
}

.video-player__speed {
  @apply relative;
}

.video-player__speed-text {
  @apply text-white text-sm;
}

.video-player__speed-menu {
  @apply absolute bottom-full right-0 mb-2 bg-black bg-opacity-90 rounded overflow-hidden;
}

.video-player__speed-option {
  @apply block w-full px-3 py-2 text-white text-sm hover:bg-white hover:bg-opacity-20 transition-colors;
}

.video-player__speed-option.active {
  @apply bg-blue-500;
}

.video-player--fullscreen {
  @apply fixed inset-0 z-50;
  aspect-ratio: unset;
}

.is-loading {
  @apply text-white text-4xl;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>