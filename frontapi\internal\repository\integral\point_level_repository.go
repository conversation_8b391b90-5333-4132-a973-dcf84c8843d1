package integral

import (
	"context"

	"gorm.io/gorm"

	model "frontapi/internal/models/integral"
	"frontapi/internal/repository/base"
)

// PointLevelRepository 积分等级数据访问接口
type PointLevelRepository interface {
	base.ExtendedRepository[model.PointLevel]
	FindByLevel(ctx context.Context, level int) (*model.PointLevel, error)
}

type pointLevelRepository struct {
	base.ExtendedRepository[model.PointLevel]
}

func NewPointLevelRepository(db *gorm.DB) PointLevelRepository {
	return &pointLevelRepository{
		ExtendedRepository: base.NewExtendedRepository[model.PointLevel](db),
	}
}

// FindByLevel 根据等级查找积分等级
func (r *pointLevelRepository) FindByLevel(ctx context.Context, level int) (*model.PointLevel, error) {
	condition := map[string]interface{}{
		"level": level,
	}
	return r.FindOneByCondition(ctx, condition, "")
}
