package constant

// NotificationType 通知类型
const (
	NotificationTypeSystem   = "system"   // 系统通知
	NotificationTypeFollow   = "follow"   // 关注通知
	NotificationTypeLike     = "like"     // 点赞通知
	NotificationTypeComment  = "comment"  // 评论通知
	NotificationTypeMessage  = "message"  // 消息通知
	NotificationTypeActivity = "activity" // 活动通知
)

var NotificationType = struct {
	System   string
	Follow   string
	Like     string
	Comment  string
	Message  string
	Activity string
}{
	System:   "system",
	Follow:   "follow",
	Like:     "like",
	Comment:  "comment",
	Message:  "message",
	Activity: "activity",
}
