package base

import (
	"reflect"
	"strings"

	"gorm.io/gorm"
)

// ModelFieldInfo 模型字段信息
type ModelFieldInfo struct {
	FieldName    string            // 结构体字段名
	DBColumnName string            // 数据库列名
	FieldType    reflect.Type      // 字段类型
	IsSearchable bool              // 是否可搜索
	IsTimeField  bool              // 是否是时间字段
	Tags         map[string]string // 字段标签信息
}

// ModelInfo 模型信息
type ModelInfo struct {
	Fields           map[string]*ModelFieldInfo // 字段名到字段信息的映射
	SearchableFields []string                   // 可搜索的字段列表
	ColumnToField    map[string]*ModelFieldInfo // 数据库列名到字段信息的映射
}

// ======================
// 模型信息分析方法
// ======================

// getModelInfo 获取模型结构信息
func (r *baseRepository[T]) getModelInfo() *ModelInfo {
	var entity T
	entityType := reflect.TypeOf(entity)

	// 如果是指针类型，获取指向的类型
	if entityType.Kind() == reflect.Ptr {
		entityType = entityType.Elem()
	}

	modelInfo := &ModelInfo{
		Fields:           make(map[string]*ModelFieldInfo),
		SearchableFields: make([]string, 0),
		ColumnToField:    make(map[string]*ModelFieldInfo),
	}

	// 递归处理结构体字段，包括嵌入的字段
	r.processStructFields(entityType, modelInfo, "")

	return modelInfo
}

// processStructFields 递归处理结构体字段
func (r *baseRepository[T]) processStructFields(structType reflect.Type, modelInfo *ModelInfo, prefix string) {
	for i := 0; i < structType.NumField(); i++ {
		field := structType.Field(i)

		// 跳过私有字段
		if !field.IsExported() {
			continue
		}

		// 检查是否是嵌入字段
		if field.Anonymous {
			// 递归处理嵌入的结构体
			fieldType := field.Type
			if fieldType.Kind() == reflect.Ptr {
				fieldType = fieldType.Elem()
			}
			if fieldType.Kind() == reflect.Struct {
				r.processStructFields(fieldType, modelInfo, prefix)
			}
			continue
		}

		fieldInfo := &ModelFieldInfo{
			FieldName: field.Name,
			FieldType: field.Type,
			Tags:      make(map[string]string),
		}

		// 解析gorm标签获取数据库列名
		if gormTag := field.Tag.Get("gorm"); gormTag != "" {
			fieldInfo.Tags["gorm"] = gormTag
			if columnName := r.extractColumnNameFromTag(gormTag); columnName != "" {
				fieldInfo.DBColumnName = columnName
			}
		}

		// 如果没有明确的列名，使用字段名的蛇形命名法
		if fieldInfo.DBColumnName == "" {
			fieldInfo.DBColumnName = r.toSnakeCase(field.Name)
		}

		// 添加前缀支持（如果需要）
		if prefix != "" {
			fieldInfo.DBColumnName = prefix + "_" + fieldInfo.DBColumnName
		}

		// 判断是否可搜索（字符串类型字段）
		fieldInfo.IsSearchable = r.isSearchableType(field.Type)

		// 判断是否是时间字段
		fieldInfo.IsTimeField = r.isTimeType(field.Type)

		// 跳过gorm标签明确标记为"-"的字段（数据库中不存在的字段）
		if gormTag := field.Tag.Get("gorm"); gormTag == "-" {
			continue
		}

		modelInfo.Fields[field.Name] = fieldInfo
		modelInfo.ColumnToField[fieldInfo.DBColumnName] = fieldInfo

		if fieldInfo.IsSearchable {
			modelInfo.SearchableFields = append(modelInfo.SearchableFields, fieldInfo.DBColumnName)
		}
	}
}

// ======================
// 值检查辅助方法
// ======================

// isEmptyValue 检查值是否为空
func (r *baseRepository[T]) isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}

	switch v := value.(type) {
	case string:
		return v == ""
	case int:
		return false // int 0 是有效值，不应跳过
	case int64:
		return false // int64 0 是有效值，不应跳过
	case float64:
		return false // float64 0 是有效值，不应跳过
	case bool:
		return false // bool false 是有效值，不应跳过
	case []string:
		return len(v) == 0
	case []int:
		return len(v) == 0
	default:
		// 使用反射检查其他类型
		reflectValue := reflect.ValueOf(value)
		switch reflectValue.Kind() {
		case reflect.Slice, reflect.Array:
			return reflectValue.Len() == 0
		case reflect.Map:
			return reflectValue.Len() == 0
		case reflect.Ptr:
			return reflectValue.IsNil()
		default:
			return false
		}
	}
}

// ======================
// 智能条件应用主方法
// ======================

// applySmartCondition 应用智能条件匹配
func (r *baseRepository[T]) applySmartCondition(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {

	// 1. 特殊条件处理
	switch key {
	case "keyword":
		return r.smartKeywordQuery(query, value, modelInfo)
	case "group_by", "groupby", "group by", "group":
		return r.smartGroupByQuery(query, value, modelInfo)
	case "status":
		return r.smartStatusQuery(query, value)
	case "user_id", "creator_id", "author_id": // 常见的用户ID字段
		return r.smartUserIDQuery(query, key, value, modelInfo)
	case "category_id", "channel_id": // 常见的分类ID字段
		return r.smartCategoryQuery(query, key, value, modelInfo)
	}

	// 2. 检查是否包含操作符（如 <>、>、<、>=、<=）
	if strings.Contains(key, " <>") || strings.Contains(key, " >") ||
		strings.Contains(key, " <") || strings.Contains(key, " >=") ||
		strings.Contains(key, " <=") || strings.Contains(key, " !=") {
		return r.smartOperatorQuery(query, key, value, modelInfo)
	}

	// 3. 检查是否包含 IN 或 NOT IN 操作符
	if strings.Contains(key, " IN") || strings.Contains(key, " NOT IN") {
		return r.smartInNotInQuery(query, key, value, modelInfo)
	}

	// 4. 时间范围查询
	if r.isTimeRangeKey(key) {
		return r.smartTimeRangeQuery(query, key, value, modelInfo)
	}

	// 5. 数组/列表查询
	if r.isArrayValue(value) {
		return r.smartArrayQuery(query, key, value, modelInfo)
	}

	// 6. 模糊查询（名称类字段）
	if r.isNameLikeField(key) && r.isStringValue(value) {
		return r.smartLikeQuery(query, key, value, modelInfo)
	}

	// 7. 默认精确匹配
	return r.smartExactQuery(query, key, value, modelInfo)
}

// ======================
// 智能查询方法实现
// ======================

// smartKeywordQuery 智能关键词查询
func (r *baseRepository[T]) smartKeywordQuery(query *gorm.DB, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	keyword, ok := value.(string)
	if !ok || keyword == "" {
		return query
	}

	if len(modelInfo.SearchableFields) == 0 {
		return query
	}

	// 构建OR查询条件
	var conditions []string
	var args []interface{}

	for _, field := range modelInfo.SearchableFields {
		conditions = append(conditions, field+" LIKE ?")
		args = append(args, "%"+keyword+"%")
	}

	if len(conditions) > 0 {
		query = query.Where(strings.Join(conditions, " OR "), args...)
	}

	return query
}

// smartGroupByQuery 智能分组查询
func (r *baseRepository[T]) smartGroupByQuery(query *gorm.DB, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	groupBy, ok := value.(string)
	if !ok || groupBy == "" {
		return query
	}
	query = query.Group(groupBy)
	return query
}

// smartStatusQuery 智能状态查询
func (r *baseRepository[T]) smartStatusQuery(query *gorm.DB, value interface{}) *gorm.DB {
	switch v := value.(type) {
	case int:
		if v > -999 { // -999 表示忽略状态筛选
			query = query.Where("status = ?", v)
		}
	case string:
		if v != "" && v != "-999" {
			query = query.Where("status = ?", v)
		}
	}
	return query
}

// smartUserIDQuery 智能用户ID查询
func (r *baseRepository[T]) smartUserIDQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	// 检查字段是否存在
	if fieldInfo, exists := modelInfo.ColumnToField[key]; exists {
		query = query.Where(fieldInfo.DBColumnName+" = ?", value)
	} else {
		// 尝试常见的用户ID字段映射
		commonUserFields := map[string][]string{
			"user_id":    {"user_id", "uid", "creator_id"},
			"creator_id": {"creator_id", "author_id", "user_id"},
			"author_id":  {"author_id", "creator_id", "user_id"},
		}

		if possibleFields, exists := commonUserFields[key]; exists {
			for _, possibleField := range possibleFields {
				if fieldInfo, fieldExists := modelInfo.ColumnToField[possibleField]; fieldExists {
					query = query.Where(fieldInfo.DBColumnName+" = ?", value)
					break
				}
			}
		}
	}
	return query
}

// smartCategoryQuery 智能分类查询
func (r *baseRepository[T]) smartCategoryQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	// 检查字段是否存在
	if fieldInfo, exists := modelInfo.ColumnToField[key]; exists {
		query = query.Where(fieldInfo.DBColumnName+" = ?", value)
	} else {
		// 尝试常见的分类字段映射
		commonCategoryFields := map[string][]string{
			"category_id": {"category_id", "cat_id", "type_id"},
			"channel_id":  {"channel_id", "chnl_id", "category_id"},
		}

		if possibleFields, exists := commonCategoryFields[key]; exists {
			for _, possibleField := range possibleFields {
				if fieldInfo, fieldExists := modelInfo.ColumnToField[possibleField]; fieldExists {
					query = query.Where(fieldInfo.DBColumnName+" = ?", value)
					break
				}
			}
		}
	}
	return query
}

// smartTimeRangeQuery 智能时间范围查询
func (r *baseRepository[T]) smartTimeRangeQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	var columnName string
	var operator string

	// 解析时间范围键
	if strings.HasSuffix(key, "_start") {
		columnName = strings.TrimSuffix(key, "_start")
		operator = ">="
	} else if strings.HasSuffix(key, "_end") {
		columnName = strings.TrimSuffix(key, "_end")
		operator = "<="
	} else if key == "start_date" {
		columnName = "created_at"
		operator = ">="
	} else if key == "end_date" {
		columnName = "created_at"
		operator = "<="
	} else if strings.HasSuffix(key, "_date") {
		columnName = strings.TrimSuffix(key, "_date")
		operator = "="
	}

	if columnName == "" {
		return query
	}

	// 检查字段是否存在
	if fieldInfo, exists := modelInfo.ColumnToField[columnName]; exists {
		if fieldInfo.IsTimeField {
			query = query.Where(fieldInfo.DBColumnName+" "+operator+" ?", value)
		}
	} else {
		// 尝试常见的时间字段映射
		timeFields := []string{"created_at", "updated_at", "regtime", "login_time", "upload_time"}
		for _, timeField := range timeFields {
			if fieldInfo, exists := modelInfo.ColumnToField[timeField]; exists && fieldInfo.IsTimeField {
				query = query.Where(fieldInfo.DBColumnName+" "+operator+" ?", value)
				break
			}
		}
	}

	return query
}

// smartArrayQuery 智能数组查询
func (r *baseRepository[T]) smartArrayQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	// 检查字段是否存在
	if fieldInfo, exists := modelInfo.ColumnToField[key]; exists {
		query = query.Where(fieldInfo.DBColumnName+" IN ?", value)
	} else {
		// 尝试直接使用原始键名
		query = query.Where(key+" IN ?", value)
	}
	return query
}

// smartLikeQuery 智能模糊查询
func (r *baseRepository[T]) smartLikeQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	stringValue, ok := value.(string)
	if !ok || stringValue == "" {
		return query
	}

	// 检查字段是否存在
	if fieldInfo, exists := modelInfo.ColumnToField[key]; exists {
		if fieldInfo.IsSearchable {
			query = query.Where(fieldInfo.DBColumnName+" LIKE ?", "%"+stringValue+"%")
		}
	} else {
		// 尝试直接使用原始键名（如果它看起来像数据库列名）
		query = query.Where(key+" LIKE ?", "%"+stringValue+"%")
	}
	return query
}

// smartExactQuery 智能精确查询
func (r *baseRepository[T]) smartExactQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	// 首先检查模型中是否存在该字段
	if fieldInfo, exists := modelInfo.ColumnToField[key]; exists {
		query = query.Where(fieldInfo.DBColumnName+" = ?", value)
		return query
	}

	// 尝试字段名映射
	mappedField := r.mapFieldName(key, modelInfo)
	if mappedField != "" {
		query = query.Where(mappedField+" = ?", value)
		return query
	}

	// 如果都没有找到，使用原始键名（可能是数据库列名）
	query = query.Where(key+" = ?", value)
	return query
}

// smartOperatorQuery 处理带操作符的查询条件
func (r *baseRepository[T]) smartOperatorQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	// 提取字段名和操作符
	parts := strings.SplitN(key, " ", 2)
	if len(parts) != 2 {
		// 如果格式不正确，回退到精确查询
		return r.smartExactQuery(query, key, value, modelInfo)
	}

	fieldName := parts[0]
	operator := parts[1]

	// 处理不同的操作符
	switch operator {
	case "<>", "!=":
		operator = "<>"
	case ">", "<", ">=", "<=":
		// 这些操作符保持不变
	default:
		// 未知操作符，回退到精确查询
		return r.smartExactQuery(query, key, value, modelInfo)
	}

	// 首先检查模型中是否存在该字段
	if fieldInfo, exists := modelInfo.ColumnToField[fieldName]; exists {
		query = query.Where(fieldInfo.DBColumnName+" "+operator+" ?", value)
		return query
	}

	// 尝试字段名映射
	mappedField := r.mapFieldName(fieldName, modelInfo)
	if mappedField != "" {
		query = query.Where(mappedField+" "+operator+" ?", value)
		return query
	}

	// 如果都没有找到，使用原始字段名（可能是数据库列名）
	query = query.Where(fieldName+" "+operator+" ?", value)
	return query
}

// smartInNotInQuery 处理 IN 和 NOT IN 操作符的查询条件
func (r *baseRepository[T]) smartInNotInQuery(query *gorm.DB, key string, value interface{}, modelInfo *ModelInfo) *gorm.DB {
	// 提取字段名和操作符
	parts := strings.SplitN(key, " ", 2)
	if len(parts) != 2 {
		// 如果格式不正确，回退到精确查询
		return r.smartExactQuery(query, key, value, modelInfo)
	}

	fieldName := parts[0]
	operator := parts[1]

	// 检查操作符是否为 IN 或 NOT IN
	if operator != "IN" && operator != "NOT IN" {
		// 未知操作符，回退到精确查询
		return r.smartExactQuery(query, key, value, modelInfo)
	}

	// 首先检查模型中是否存在该字段
	if fieldInfo, exists := modelInfo.ColumnToField[fieldName]; exists {
		if operator == "IN" {
			query = query.Where(fieldInfo.DBColumnName+" IN ?", value)
		} else { // NOT IN
			query = query.Where(fieldInfo.DBColumnName+" NOT IN ?", value)
		}
		return query
	}

	// 尝试字段名映射
	mappedField := r.mapFieldName(fieldName, modelInfo)
	if mappedField != "" {
		if operator == "IN" {
			query = query.Where(mappedField+" IN ?", value)
		} else { // NOT IN
			query = query.Where(mappedField+" NOT IN ?", value)
		}
		return query
	}

	// 如果都没有找到，使用原始字段名（可能是数据库列名）
	if operator == "IN" {
		query = query.Where(fieldName+" IN ?", value)
	} else { // NOT IN
		query = query.Where(fieldName+" NOT IN ?", value)
	}
	return query
}

// ======================
// 字段映射和类型检查辅助方法
// ======================

// mapFieldName 映射字段名到数据库列名
func (r *baseRepository[T]) mapFieldName(key string, modelInfo *ModelInfo) string {
	// 常见字段映射关系
	fieldMappings := map[string][]string{
		"username":     {"username", "user_name", "name"},
		"user_name":    {"user_name", "username", "name"},
		"categoryID":   {"category_id", "cat_id"},
		"channelID":    {"channel_id", "chnl_id"},
		"created_at":   {"created_at", "create_time"},
		"updated_at":   {"updated_at", "update_time"},
		"content":      {"content", "description", "body"},
		"ip_address":   {"ip_address", "ip", "client_ip"},
		"device_type":  {"device_type", "device", "user_agent"},
		"login_status": {"login_status", "status", "is_success"},
	}

	if possibleFields, exists := fieldMappings[key]; exists {
		for _, possibleField := range possibleFields {
			if fieldInfo, fieldExists := modelInfo.ColumnToField[possibleField]; fieldExists {
				return fieldInfo.DBColumnName
			}
		}
	}

	return ""
}

// extractColumnNameFromTag 从gorm标签中提取列名
func (r *baseRepository[T]) extractColumnNameFromTag(tag string) string {
	parts := strings.Split(tag, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if strings.HasPrefix(part, "column:") {
			return strings.TrimPrefix(part, "column:")
		}
	}
	return ""
}

// toSnakeCase 将驼峰命名转换为蛇形命名
func (r *baseRepository[T]) toSnakeCase(str string) string {
	var result []rune
	for i, char := range str {
		if i > 0 && 'A' <= char && char <= 'Z' {
			result = append(result, '_')
		}
		result = append(result, char)
	}
	return strings.ToLower(string(result))
}

// isSearchableType 检查字段类型是否可搜索
func (r *baseRepository[T]) isSearchableType(fieldType reflect.Type) bool {
	switch fieldType.Kind() {
	case reflect.String:
		return true
	case reflect.Ptr:
		// 检查是否是 null.String 类型
		if fieldType.String() == "null.String" {
			return true
		}
		// 检查指针指向的类型
		return r.isSearchableType(fieldType.Elem())
	default:
		return false
	}
}

// isTimeType 检查字段类型是否是时间类型
func (r *baseRepository[T]) isTimeType(fieldType reflect.Type) bool {
	return fieldType.String() == "time.Time" ||
		fieldType.String() == "types.JSONTime" ||
		(fieldType.Kind() == reflect.Ptr && fieldType.Elem().String() == "time.Time") ||
		(fieldType.Kind() == reflect.Ptr && fieldType.Elem().String() == "types.JSONTime")
}

// isTimeRangeKey 检查是否是时间范围键
func (r *baseRepository[T]) isTimeRangeKey(key string) bool {
	return strings.HasSuffix(key, "_start") ||
		strings.HasSuffix(key, "_end") ||
		strings.HasSuffix(key, "_date") ||
		key == "start_date" ||
		key == "end_date"
}

// isArrayValue 检查是否是数组值
func (r *baseRepository[T]) isArrayValue(value interface{}) bool {
	reflectValue := reflect.ValueOf(value)
	switch reflectValue.Kind() {
	case reflect.Slice, reflect.Array:
		return reflectValue.Len() > 0
	default:
		return false
	}
}

// isStringValue 检查是否是字符串值
func (r *baseRepository[T]) isStringValue(value interface{}) bool {
	_, ok := value.(string)
	return ok
}

// isNameLikeField 检查是否是名称类字段
func (r *baseRepository[T]) isNameLikeField(key string) bool {
	nameLikeFields := []string{
		"name", "title", "content", "description",
		"username", "user_name", "creator_name",
		"email", "phone", "address", "location",
		"ip_address", "device_type", "reason",
	}

	for _, field := range nameLikeFields {
		if strings.Contains(key, field) {
			return true
		}
	}
	return false
}
