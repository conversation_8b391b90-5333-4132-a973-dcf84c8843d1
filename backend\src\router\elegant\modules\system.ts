import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from "@/locales";

const routes: GeneratedRoute[] = [
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: '系统管理',
      i18nKey: 'route.system',
      icon: 'lucide:settings',
      order: 5
    },
    children: [
      {
        name: 'system_tags',
        path: '/system/tags',
        component: 'view.system_tags',
        meta: {
          title: 'system_tags',
          i18nKey: 'route.system_tags',
          icon: 'lucide:tag'
        }
      },
      {
        name: 'system_menus',
        path: '/system/menus',
        component: 'view.system_menus',
        meta: {
          title: '菜单管理',
          i18nKey: 'route.system_menus',
          icon: 'lucide:menu'
        }
      },
      {
        name: 'system_mock',
        path: '/system/mock',
        component: 'view.system_mock',
        meta: {
          title: '数据库Mock生成器',
          i18nKey: 'route.system_mock',
          icon: 'lucide:database'
        }
      }
    ]
  }
];

export default routes; 