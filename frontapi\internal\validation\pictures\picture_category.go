package pictures

// CreateCategoryRequest 创建分类请求
type PictureCategoryCreateRequest struct {
	Name        string `json:"name" validate:"required|min_len:3|max_len:100"`
	Code        string `json:"code" validate:"required|min_len:3|max_len:100"`
	SortOrder   int    `json:"sort_order"`
	Description string `json:"description"`
}

// UpdateCategoryRequest 更新分类请求
type PictureCategoryUpdateRequest struct {
	Name        *string `json:"name"`
	SortOrder   *int    `json:"sort_order"`
	Description *string `json:"description"`
	Status      *int8   `json:"status"`
}

// UpdateCategoryStatusRequest 更新分类状态请求
type UpdateCategoryStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int8   `json:"status" validate:"int|min:-5|max:5"`
}

// BatchUpdateCategoryStatusRequest 批量更新分类状态请求
type BatchUpdateCategoryStatusRequest struct {
	IDs    []string `json:"ids" validate:"required|min_len:1"`
	Status int8     `json:"status" validate:"int|min:-5|max:5"`
}

// BatchDeleteCategoryRequest 批量删除分类请求
type BatchDeleteCategoryRequest struct {
	IDs []string `json:"ids" validate:"required|min_len:1"`
}
