import { post, corePost } from '@/shared/composables'
import type { BaseRequest } from '../../utils/http/baseRequest'
import type { BaseResponse } from '../../utils/http/baseResponse'
import type { Video, VideoSearchParams, VideoListParams } from '@/shared/types/videos'

// 视频列表响应接口
export interface VideoListResponse {
  list: Video[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// 获取视频列表
export const getVideoList = (params: BaseRequest<VideoListParams>) => {
  return corePost<BaseResponse<VideoListResponse>>('/video/getVideoList', params)
}

// 搜索视频
export const searchVideos = (params: BaseRequest<VideoSearchParams & { page: number; pageSize: number }>) => {
  return corePost<BaseResponse<VideoListResponse>>('/video/searchVideos', params)
}

// 获取热门视频
export const getHotVideos = (params: BaseRequest<{ page: number; pageSize: number }>) => {
  return corePost<BaseResponse<VideoListResponse>>('/video/getHotVideos', params)
}

// 获取推荐视频
export const getRecommendedVideos = (params: BaseRequest<{ page: number; pageSize: number; userId?: string }>) => {
  return corePost<BaseResponse<VideoListResponse>>('/video/getRecommendedVideos', params)
}

// 获取分类视频
export const getCategoryVideos = (params: BaseRequest<{ categoryId: string; page: number; pageSize: number; sortBy?: string }>) => {
  return corePost<BaseResponse<VideoListResponse>>('/video/getCategoryVideos', params)
}

// 获取创作者视频
export const getCreatorVideos = (params: BaseRequest<{ creatorId: string; page: number; pageSize: number }>) => {
  return corePost<BaseResponse<VideoListResponse>>('/video/getCreatorVideos', params)
}

// 获取视频详情
export const getVideoDetail = (params: BaseRequest<{ videoId: string }>) => {
  return corePost<BaseResponse<Video>>('/video/getVideoDetail', params)
}

// 视频交互操作
export const likeVideo = (params: BaseRequest<{ videoId: string }>) => {
  return corePost<BaseResponse<{ isLiked: boolean; likeCount: number }>>('/video/like', params)
}

export const favoriteVideo = (params: BaseRequest<{ videoId: string }>) => {
  return corePost<BaseResponse<{ isFavorited: boolean; favoriteCount: number }>>('/video/favorite', params)
}

export const shareVideo = (params: BaseRequest<{ videoId: string; platform: string }>) => {
  return corePost<BaseResponse<{ shareCount: number }>>('/video/share', params)
}

// 更新观看进度
export const updateWatchProgress = (params: BaseRequest<{ videoId: string; progress: number; watchTime: number }>) => {
  return corePost<BaseResponse<boolean>>('/video/updateProgress', params)
}

// 举报视频
export const reportVideo = (params: BaseRequest<{ videoId: string; reason: string; description?: string }>) => {
  return corePost<BaseResponse<boolean>>('/video/report', params)
}

// 下载视频
export const downloadVideo = (params: BaseRequest<{ videoId: string; quality?: string }>) => {
  return corePost<BaseResponse<{ downloadUrl: string; expiresAt: string }>>('/video/download', params)
}