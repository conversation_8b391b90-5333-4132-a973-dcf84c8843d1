package v2

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

// RedisConfig Redis配置结构
type RedisConfig struct {
	Enabled       bool          `json:"enabled"`
	KeyPrefix     string        `json:"key_prefix"`
	DefaultTTL    time.Duration `json:"default_ttl"`
	CollectTTL    time.Duration `json:"collect_ttl"`
	CountTTL      time.Duration `json:"count_ttl"`
	RankingTTL    time.Duration `json:"ranking_ttl"`
	HistoryTTL    time.Duration `json:"history_ttl"`
	StatsTTL      time.Duration `json:"stats_ttl"`
	PipelineSize  int           `json:"pipeline_size"`
	EnableMetrics bool          `json:"enable_metrics"`
	MaxRetries    int           `json:"max_retries"`
	MaxHistoryLen int64         `json:"max_history_len"`
	MaxStreamLen  int64         `json:"max_stream_len"`
}

// RedisClient Redis客户端包装器
type RedisClient struct {
	client redis.UniversalClient
	config *RedisConfig
	stats  *AdapterStats
	mu     sync.RWMutex
}

// AdapterStats 适配器统计信息
type AdapterStats struct {
	HitCount    int64     `json:"hit_count"`
	MissCount   int64     `json:"miss_count"`
	ErrorCount  int64     `json:"error_count"`
	HitRate     float64   `json:"hit_rate"`
	LastUpdated time.Time `json:"last_updated"`
	mu          sync.RWMutex
}

// NewRedisClient 创建Redis客户端实例
func NewRedisClient(client redis.UniversalClient, config *RedisConfig) *RedisClient {
	if config == nil {
		config = &RedisConfig{
			Enabled:       true,
			KeyPrefix:     "collect:",
			DefaultTTL:    24 * time.Hour,
			CollectTTL:    7 * 24 * time.Hour,
			CountTTL:      1 * time.Hour,
			RankingTTL:    30 * time.Minute,
			HistoryTTL:    7 * 24 * time.Hour,
			StatsTTL:      24 * time.Hour,
			PipelineSize:  100,
			EnableMetrics: true,
			MaxRetries:    3,
			MaxHistoryLen: 1000,
			MaxStreamLen:  10000,
		}
	}

	return &RedisClient{
		client: client,
		config: config,
		stats: &AdapterStats{
			LastUpdated: time.Now(),
		},
	}
}

// Pipeline 创建管道
func (r *RedisClient) Pipeline() redis.Pipeliner {
	return r.client.TxPipeline()
}

// Client 获取原生客户端
func (r *RedisClient) Client() redis.UniversalClient {
	return r.client
}

// Config 获取配置
func (r *RedisClient) Config() *RedisConfig {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.config
}

// Stats 获取统计信息
func (r *RedisClient) Stats() *AdapterStats {
	return r.stats
}

// UpdateStats 更新统计信息
func (r *RedisClient) UpdateStats(hit, miss, error int64) {
	r.stats.mu.Lock()
	defer r.stats.mu.Unlock()

	r.stats.HitCount += hit
	r.stats.MissCount += miss
	r.stats.ErrorCount += error
	r.stats.LastUpdated = time.Now()
	r.updateHitRate()
}

// updateHitRate 更新命中率
func (r *RedisClient) updateHitRate() {
	total := r.stats.HitCount + r.stats.MissCount
	if total > 0 {
		r.stats.HitRate = float64(r.stats.HitCount) / float64(total)
	}
}

// HealthCheck 健康检查
func (r *RedisClient) HealthCheck(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭连接
func (r *RedisClient) Close() error {
	if closer, ok := r.client.(interface{ Close() error }); ok {
		return closer.Close()
	}
	return nil
}

// ==== 键生成方法 ====

func (r *RedisClient) collectKey(itemType, itemID string) string {
	return fmt.Sprintf("%scollect:%s:%s", r.config.KeyPrefix, itemType, itemID)
}

func (r *RedisClient) userCollectsKey(userID, itemType string) string {
	return fmt.Sprintf("%suser:%s:collects:%s", r.config.KeyPrefix, userID, itemType)
}

func (r *RedisClient) itemCollectorsKey(itemType, itemID string) string {
	return fmt.Sprintf("%sitem:%s:%s:collectors", r.config.KeyPrefix, itemType, itemID)
}

func (r *RedisClient) countKey(itemType, itemID string) string {
	return fmt.Sprintf("%scount:%s:%s", r.config.KeyPrefix, itemType, itemID)
}

func (r *RedisClient) hotRankKey(itemType string) string {
	return fmt.Sprintf("%shot:%s", r.config.KeyPrefix, itemType)
}

func (r *RedisClient) statsKey(itemType, itemID string) string {
	return fmt.Sprintf("%sstats:%s:%s", r.config.KeyPrefix, itemType, itemID)
}

func (r *RedisClient) historyKey(userID, itemType string) string {
	return fmt.Sprintf("%shistory:%s:%s", r.config.KeyPrefix, userID, itemType)
}

func (r *RedisClient) userStatsKey(userID string) string {
	return fmt.Sprintf("%suser:%s:stats", r.config.KeyPrefix, userID)
}

func (r *RedisClient) globalStatsKey(itemType string) string {
	return fmt.Sprintf("%sglobal:stats:%s", r.config.KeyPrefix, itemType)
}

func (r *RedisClient) trendKey(itemType string, timeRange string) string {
	return fmt.Sprintf("%strend:%s:%s", r.config.KeyPrefix, itemType, timeRange)
}
