<template>
  <el-tag 
    :type="tagType" 
    :effect="effect" 
    size="small"
    class="channel-status-tag"
  >
    {{ statusText }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  status: number;
  effect?: 'light' | 'dark' | 'plain';
}

const props = withDefaults(defineProps<Props>(), {
  effect: 'light'
});

const tagType = computed(() => {
  switch (props.status) {
    case 1:
      return 'success';
    case 0:
      return 'warning';
    case -4:
      return 'danger';
    default:
      return 'info';
  }
});

const statusText = computed(() => {
  switch (props.status) {
    case 1:
      return '正常';
    case 0:
      return '禁用';
    case -4:
      return '已删除';
    default:
      return '未知';
  }
});
</script>

<style scoped lang="scss">
.channel-status-tag {
  font-weight: 500;
}
</style> 