package comics

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// ComicReadHistory 漫画阅读历史模型
type ComicReadHistory struct {
	models.BaseModelStruct
	UserID       string         `json:"user_id" gorm:"column:user_id;not null;index:idx_user_id;uniqueIndex:uk_user_comic_chapter,priority:1" comment:"用户ID"`
	ComicID      string         `json:"comic_id" gorm:"column:comic_id;not null;index:idx_comic_id;uniqueIndex:uk_user_comic_chapter,priority:2" comment:"漫画ID"`
	ComicTitle   string         `json:"comic_title" gorm:"column:comic_title" comment:"漫画标题"`
	ComicCover   string         `json:"comic_cover" gorm:"column:comic_cover" comment:"漫画封面"`
	ChapterID    string         `json:"chapter_id" gorm:"column:chapter_id;not null;index:idx_chapter_id;uniqueIndex:uk_user_comic_chapter,priority:3" comment:"章节ID"`
	ChapterTitle string         `json:"chapter_title" gorm:"column:chapter_title" comment:"章节标题"`
	Author       string         `json:"author" gorm:"column:author" comment:"作者"`
	PageNumber   int            `json:"page_number" gorm:"column:page_number;default:1" comment:"页码"`
	ReadTime     types.JSONTime `json:"read_time" gorm:"column:read_time;default:CURRENT_TIMESTAMP" comment:"阅读时间"`
}

// TableName 指定表名
func (ComicReadHistory) TableName() string {
	return "ly_comic_read_history"
}
