<template>
    <div class="home-page">
        <h1>{{ t('common.welcome') }}</h1>

        <!-- 主题切换部分 -->
        <div class="theme-section">
            <h2>{{ t('theme.themeSettings') }}</h2>

            <div class="theme-info">
                <p>{{ t('common.currentTheme') }}: <strong>{{ t(`theme.${currentTheme}`) }}</strong></p>
                <p>{{ t('common.themeCode') }}: <strong>{{ currentTheme }}</strong></p>
                <p>{{ t('common.darkMode') }}: <strong>{{ isDarkMode ? t('common.enabled') : t('common.disabled')
                }}</strong></p>
            </div>

            <div class="theme-switcher">
                <h3>{{ t('theme.changeTheme') }}</h3>

                <div class="theme-grid">
                    <div v-for="theme in availableThemes" :key="theme.name" class="theme-card"
                        :class="{ 'active': currentTheme === theme.name, 'dark': theme.isDark }"
                        @click="setTheme(theme.name)">
                        <div class="theme-color" :style="{ backgroundColor: theme.primary }"></div>
                        <div class="theme-details">
                            <div class="theme-name">{{ t(`theme.${theme.code}`) }}</div>
                            <div class="theme-code">{{ theme.shortName }}</div>
                        </div>
                        <div v-if="currentTheme === theme.name" class="theme-selected">
                            <i class="pi pi-check"></i>
                        </div>
                    </div>
                </div>

                <div class="theme-options">
                    <div class="p-field-checkbox">
                        <Checkbox v-model="followSystem" :binary="true" inputId="follow-system" />
                        <label for="follow-system">{{ t('theme.followSystem') }}</label>
                    </div>

                    <div class="p-field-checkbox">
                        <Checkbox v-model="darkMode" :binary="true" inputId="dark-mode" />
                        <label for="dark-mode">{{ t('theme.darkMode') }}</label>
                    </div>
                </div>
            </div>
        </div>

        <div class="language-section">
            <h2>{{ t('language.languageSettings') }}</h2>

            <div class="language-info">
                <p>{{ t('common.currentLanguage') }}: <strong>{{ localeName }}</strong></p>
                <p>{{ t('common.languageCode') }}: <strong>{{ locale }}</strong></p>
            </div>

            <div class="language-selector">
                <h3>{{ t('language.changeLanguage') }}</h3>

                <div class="language-grid">
                    <div v-for="lang in locales" :key="lang.code" class="language-card"
                        :class="{ 'active': locale === lang.code, 'loading': isChangingLanguage && selectedLanguage === lang.code }"
                        @click="handleLanguageChange(lang.code)">
                        <div class="language-flag">{{ lang.flag }}</div>
                        <div class="language-details">
                            <div class="language-name">{{ lang.nativeName }}</div>
                            <div class="language-code">{{ lang.shortCode }}</div>
                        </div>
                        <div v-if="isChangingLanguage && selectedLanguage === lang.code" class="language-loading">
                            <span class="loading-spinner"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="translation-test">
                <h3>{{ t('common.translationTest') }}</h3>
                <ul>
                    <li v-for="(key, index) in translationKeys" :key="index">
                        {{ t(key) }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTranslation } from '@/core/plugins/i18n/composables';
import { themeManager } from '@/shared/themes/theme-manager';
import { useThemeStore } from '@/store/modules/theme';
import Checkbox from 'primevue/checkbox';
import { computed, ref, watch } from 'vue';

// 使用翻译助手
const { t, locale, localeName, changeLocale, locales } = useTranslation();

// 使用主题
const themeStore = useThemeStore();

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme);
const isDarkMode = computed(() => themeStore.isDark);

// 可用主题列表
const availableThemes = computed(() => {
    return themeManager.availableThemes.value;
});

// 深色模式切换
const darkMode = computed({
    get: () => isDarkMode.value,
    set: () => themeStore.toggleDarkMode()
});

// 跟随系统
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 切换主题
const setTheme = (themeName: string) => {
    themeStore.setTheme(themeName);
};

// 语言切换状态
const isChangingLanguage = ref(false);
const selectedLanguage = ref('');

// 测试翻译的键
const translationKeys = [
    'common.home',
    'common.videos',
    'common.pictures',
    'common.manga',
    'common.ebook'
];

// 处理语言切换
const handleLanguageChange = async (code: string) => {
    // 如果正在切换或已是当前语言，则不做操作
    if (isChangingLanguage.value || code === locale.value) return;

    console.log(`Home page: changing language to ${code}`);

    // 设置切换状态
    isChangingLanguage.value = true;
    selectedLanguage.value = code;

    try {
        // 执行语言切换
        await changeLocale(code);
    } catch (error) {
        console.error('Error changing language:', error);
    } finally {
        // 无论成功与否，都重置状态
        setTimeout(() => {
            isChangingLanguage.value = false;
            selectedLanguage.value = '';
        }, 300);
    }
};

// 监听语言变化
watch(locale, (newLocale) => {
    console.log(`Locale changed to: ${newLocale}`);
});
</script>

<style scoped lang="scss">
.home-page {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;

    h1 {
        margin-bottom: 2rem;
        color: var(--primary-color, var(--color-primary));
        font-size: 2.5rem;
    }
}

// 新增主题部分样式
.theme-section {
    background: var(--surface-card, var(--color-background));
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;

    h2 {
        margin-bottom: 1.5rem;
        border-bottom: 1px solid var(--surface-border, var(--color-border));
        padding-bottom: 0.75rem;
        color: var(--text-color, var(--color-text));
    }

    h3 {
        margin: 1.5rem 0 1rem;
        color: var(--text-color, var(--color-text));
    }
}

.theme-info {
    background: var(--surface-ground, var(--color-background-alt));
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;

    p {
        margin: 0.5rem 0;
        color: var(--text-color, var(--color-text));
    }
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.theme-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--surface-border, var(--color-border));
    background-color: var(--surface-card, var(--color-background));
    cursor: pointer;
    transition: all 0.2s;
    position: relative;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.active {
        border-color: var(--primary-color, var(--color-primary));
        box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 59, 130, 246), 0.25);
    }

    &.dark {
        background-color: var(--surface-900, #121212);

        .theme-name,
        .theme-code {
            color: white;
        }
    }
}

.theme-color {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 0.75rem;
    border: 2px solid white;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.theme-details {
    flex: 1;

    .theme-name {
        font-weight: 500;
        color: var(--text-color, var(--color-text));
    }

    .theme-code {
        font-size: 0.75rem;
        color: var(--text-color-secondary, var(--color-text-light));
    }
}

.theme-selected {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color, var(--color-primary));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.theme-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 1rem;

    .p-field-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        label {
            cursor: pointer;
            color: var(--text-color, var(--color-text));
        }
    }
}

// 保留原有样式
.language-section {
    background: var(--surface-card, var(--color-background));
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;

    h2 {
        margin-bottom: 1.5rem;
        border-bottom: 1px solid var(--surface-border, var(--color-border));
        padding-bottom: 0.75rem;
        color: var(--text-color, var(--color-text));
    }

    h3 {
        margin: 1.5rem 0 1rem;
        color: var(--text-color, var(--color-text));
    }
}

.language-info {
    background: var(--surface-ground, var(--color-background-alt));
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;

    p {
        margin: 0.5rem 0;
        color: var(--text-color, var(--color-text));
    }
}

.language-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.language-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 6px;
    border: 1px solid var(--surface-border, var(--color-border));
    cursor: pointer;
    transition: all 0.2s;
    position: relative;

    &:hover {
        background: var(--surface-hover, var(--color-background-hover));
        transform: translateY(-2px);
    }

    &.active {
        background: var(--primary-color, var(--color-primary));
        color: white;
        border-color: var(--primary-color, var(--color-primary));
    }

    &.loading {
        pointer-events: none;
        opacity: 0.8;
    }

    .language-flag {
        font-size: 1.75rem;
        margin-right: 1rem;
    }

    .language-details {
        flex: 1;

        .language-name {
            font-weight: 500;
        }

        .language-code {
            opacity: 0.7;
            font-size: 0.85rem;
            margin-top: 0.25rem;
        }
    }

    .language-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 6px;

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--primary-color, var(--color-primary));
            border-top-color: transparent;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
        }
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.translation-test {
    margin-top: 2rem;

    ul {
        list-style: none;
        padding: 0;

        li {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: var(--surface-ground, var(--color-background-alt));
            border-radius: 4px;
            transition: all 0.3s ease;
            color: var(--text-color, var(--color-text));
        }
    }
}

@media (max-width: 768px) {
    .home-page {
        padding: 1rem;
    }

    .language-grid,
    .theme-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}
</style>