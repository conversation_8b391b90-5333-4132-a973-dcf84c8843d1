<template>
    <div class="home-page">
        <h1>{{ t('common.welcome') }}</h1>
        
        <!-- 主题颜色测试元素 -->
        <Card class="mb-4">
            <template #title>主题颜色测试</template>
            <template #content>
                <div class="theme-color-test">
                    <div class="theme-test-element">
                        <h3>这是使用 var(--primary-color) 的元素</h3>
                        <p>当你切换主题颜色时，这个元素的背景色会改变</p>
                    </div>
                    
                    <div class="color-samples">
                        <h3 class="mb-3">主题颜色样本</h3>
                        <div class="grid">
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-50)">
                                    <span>primary-50</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-100)">
                                    <span>primary-100</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-200)">
                                    <span>primary-200</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-300)">
                                    <span>primary-300</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-400)">
                                    <span>primary-400</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-500); color: white;">
                                    <span>primary-500</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-600); color: white;">
                                    <span>primary-600</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-700); color: white;">
                                    <span>primary-700</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-800); color: white;">
                                    <span>primary-800</span>
                                </div>
                            </div>
                            <div class="col-6 md:col-4 lg:col-2">
                                <div class="color-sample" style="background-color: var(--primary-900); color: white;">
                                    <span>primary-900</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="button-samples mt-4">
                        <h3 class="mb-3">按钮样本</h3>
                        <div class="flex flex-wrap gap-2">
                            <Button label="Primary" />
                            <Button label="Secondary" severity="secondary" />
                            <Button label="Success" severity="success" />
                            <Button label="Info" severity="info" />
                            <Button label="Warning" severity="warning" />
                            <Button label="Help" severity="help" />
                            <Button label="Danger" severity="danger" />
                        </div>
                    </div>
                </div>
            </template>
        </Card>
        
        <!-- 主题演示卡片 -->
        <Card class="mb-4">
            <template #title>主题系统演示</template>
            <template #content>
                <p class="mb-3">
                    当前使用的是重构后的主题系统，支持多种颜色和深浅模式切换。
                </p>
                <div class="theme-demo-elements">
                    <h3>UI 元素展示</h3>
                    <div class="flex flex-wrap gap-2 mb-3">
                        <Button label="Primary" />
                        <Button label="Secondary" severity="secondary" />
                        <Button label="Success" severity="success" />
                        <Button label="Info" severity="info" />
                        <Button label="Warning" severity="warning" />
                        <Button label="Help" severity="help" />
                        <Button label="Danger" severity="danger" />
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mb-3">
                        <InputText placeholder="输入框" />
                        <Dropdown v-model="selectedItem" :options="dropdownItems" optionLabel="name" placeholder="下拉菜单" class="w-14rem" />
                        <Checkbox v-model="checked" :binary="true" inputId="demo-checkbox" />
                        <label for="demo-checkbox" class="ml-2 mr-4">复选框</label>
                        <RadioButton v-model="radioValue" inputId="radio1" value="选项1" />
                        <label for="radio1" class="ml-2 mr-4">单选按钮1</label>
                        <RadioButton v-model="radioValue" inputId="radio2" value="选项2" />
                        <label for="radio2" class="ml-2">单选按钮2</label>
                    </div>
                </div>
            </template>
        </Card>
        
        <!-- 主题切换部分 -->
        <Card class="mb-4">
            <template #title>主题设置</template>
            <template #content>
                <div class="theme-info mb-4">
                    <div class="grid">
                        <div class="col-12 md:col-4">
                            <div class="theme-info-item">
                                <span class="theme-info-label">当前主题名称:</span>
                                <span class="theme-info-value">{{ currentThemeName }}</span>
                            </div>
                        </div>
                        <div class="col-12 md:col-4">
                            <div class="theme-info-item">
                                <span class="theme-info-label">主题家族:</span>
                                <span class="theme-info-value">{{ currentThemeFamily }}</span>
                            </div>
                        </div>
                        <div class="col-12 md:col-4">
                            <div class="theme-info-item">
                                <span class="theme-info-label">主题样式:</span>
                                <span class="theme-info-value">{{ currentThemeStyle }}</span>
                            </div>
                        </div>
                        <div class="col-12 md:col-4">
                            <div class="theme-info-item">
                                <span class="theme-info-label">主题模式:</span>
                                <span class="theme-info-value">{{ currentThemeMode }}</span>
                            </div>
                        </div>
                        <div class="col-12 md:col-4">
                            <div class="theme-info-item">
                                <span class="theme-info-label">跟随系统:</span>
                                <span class="theme-info-value">{{ isFollowingSystem ? '是' : '否' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="theme-controls">
                    <h3 class="mb-3">主题家族</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <Button v-for="family in themeFamilies" :key="family" 
                            :class="['p-button-outlined', { 'p-button-primary': currentThemeFamily === family }]"
                            @click="setThemeFamily(family)">
                            {{ family }}
                        </Button>
                    </div>
                    
                    <h3 class="mb-3">主题颜色</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <div v-for="style in themeStyles" :key="style.code" 
                            class="theme-color-button"
                            :class="{ 'active': currentThemeStyle === style.code }"
                            @click="setThemeStyle(style.code)">
                            <div class="color-preview" :style="{ backgroundColor: isDark ? style.darkBgColor : style.lightBgColor }"></div>
                            <span>{{ style.displayName }}</span>
                        </div>
                    </div>
                    
                    <h3 class="mb-3">主题模式</h3>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <Button label="亮色模式" icon="pi pi-sun" 
                            :class="['p-button-outlined', { 'p-button-primary': !isDark }]"
                            @click="setThemeMode('light')" />
                        <Button label="暗色模式" icon="pi pi-moon" 
                            :class="['p-button-outlined', { 'p-button-primary': isDark }]"
                            @click="setThemeMode('dark')" />
                    </div>
                    
                    <h3 class="mb-3">其他设置</h3>
                    <div class="flex align-items-center mb-2">
                        <Checkbox v-model="followSystem" :binary="true" inputId="follow-system" />
                        <label for="follow-system" class="ml-2">跟随系统主题</label>
                    </div>
                </div>
            </template>
        </Card>

        <!-- 语言切换部分 -->
        <Card>
            <template #title>语言设置</template>
            <template #content>
                <div class="language-info mb-4">
                    <div class="grid">
                        <div class="col-12 md:col-6">
                            <div class="language-info-item">
                                <span class="language-info-label">当前语言:</span>
                                <span class="language-info-value">{{ localeName }}</span>
                            </div>
                        </div>
                        <div class="col-12 md:col-6">
                            <div class="language-info-item">
                                <span class="language-info-label">语言代码:</span>
                                <span class="language-info-value">{{ locale }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="language-controls">
                    <h3 class="mb-3">选择语言</h3>
                    <div class="flex flex-wrap gap-2">
                        <Button v-for="lang in locales" :key="lang.code"
                            :class="['p-button-outlined', { 'p-button-primary': locale === lang.code }]"
                            :loading="isChangingLanguage && selectedLanguage === lang.code"
                            @click="handleLanguageChange(lang.code)">
                            <span class="mr-2">{{ lang.flag }}</span>
                            <span>{{ lang.nativeName }}</span>
                        </Button>
                    </div>
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { themeStyles } from '@/config/theme.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import { useThemeStore } from '@/store/modules/theme';
import Button from 'primevue/button';
import Card from 'primevue/card';
import Checkbox from 'primevue/checkbox';
import Dropdown from 'primevue/dropdown';
import InputText from 'primevue/inputtext';
import RadioButton from 'primevue/radiobutton';
import { computed, ref, watch } from 'vue';

// 使用翻译助手
const { t, locale, localeName, changeLocale, locales } = useTranslation();

// 使用主题存储
const themeStore = useThemeStore();

// 可用的主题家族
const themeFamilies = ['aura', 'lara'];

// 语言切换状态
const isChangingLanguage = ref(false);
const selectedLanguage = ref('');

// 当前主题信息
const currentThemeName = computed(() => themeStore.currentThemeName);
const currentThemeFamily = computed(() => themeStore.currentThemeFamily);
const currentThemeStyle = computed(() => themeStore.currentThemeStyle);
const currentThemeMode = computed(() => themeStore.themeMode);
const isDark = computed(() => themeStore.isDark);
const isFollowingSystem = computed(() => themeStore.isFollowingSystem);

// 跟随系统设置
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 主题操作方法
const setThemeFamily = (family: string) => themeStore.setThemeFamily(family as any);
const setThemeStyle = (style: string) => themeStore.setThemeStyle(style);
const setThemeMode = (mode: string) => themeStore.setThemeMode(mode as any);

// 演示用的表单元素
const selectedItem = ref(null);
const dropdownItems = ref([
    { name: '选项1', code: 'option1' },
    { name: '选项2', code: 'option2' },
    { name: '选项3', code: 'option3' }
]);
const checked = ref(false);
const radioValue = ref('选项1');

// 处理语言切换
const handleLanguageChange = async (code: string) => {
    // 如果正在切换或已是当前语言，则不做操作
    if (isChangingLanguage.value || code === locale.value) return;

    console.log(`Home page: changing language to ${code}`);

    // 设置切换状态
    isChangingLanguage.value = true;
    selectedLanguage.value = code;

    try {
        // 执行语言切换
        await changeLocale(code);
    } catch (error) {
        console.error('Error changing language:', error);
    } finally {
        // 无论成功与否，都重置状态
        setTimeout(() => {
            isChangingLanguage.value = false;
            selectedLanguage.value = '';
        }, 300);
    }
};

// 监听语言变化
watch(locale, (newLocale) => {
    console.log(`Locale changed to: ${newLocale}`);
});
</script>

<style scoped lang="scss">
.home-page {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;

    h1 {
        margin-bottom: 2rem;
        color: var(--primary-color);
        font-size: 2.5rem;
    }
    
    h3 {
        color: var(--pimary-text-color);
        font-weight: 600;
    }
}

.theme-test-element {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    background-color: var(--primary-color);
    color: var(--primary-color-text);
    text-align: center;
    transition: all 0.3s ease;
    
    h3 {
        color: inherit;
        margin-top: 0;
    }
    
    p {
        margin-bottom: 0;
    }
}

.color-sample {
    height: 80px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    
    span {
        font-size: 0.8rem;
        font-weight: 600;
    }
}

.theme-info-item, .language-info-item {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: var(--surface-ground);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
}

.theme-info-label, .language-info-label {
    font-size: 0.875rem;
    color: var(--text-color-secondary);
    margin-bottom: 0.25rem;
}

.theme-info-value, .language-info-value {
    font-weight: 600;
    color: var(--pimary-text-color);
}

.theme-color-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--surface-border);
    transition: all 0.2s;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &.active {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 1px var(--primary-color);
    }
    
    .color-preview {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        margin-bottom: 0.5rem;
        border: 1px solid var(--surface-border);
    }
    
    span {
        font-size: 0.875rem;
        color: var(--pimary-text-color);
    }
}
</style>