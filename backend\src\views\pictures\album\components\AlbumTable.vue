<template>
    <div class="album-table-container">
        <!-- 批量操作工具栏 -->
        <div v-if="selectedRows.length > 0" class="batch-toolbar">
            <div class="batch-info">
                <el-icon>
                    <Check />
                </el-icon>
                <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
            </div>
            <div class="batch-actions">
                <el-button type="warning" size="small" @click="handleBatchStatus(0)">
                    批量禁用
                </el-button>
                <el-button type="success" size="small" @click="handleBatchStatus(1)">
                    批量启用
                </el-button>
                <el-button type="danger" size="small" @click="handleBatchDelete">
                    批量删除
                </el-button>
            </div>
        </div>

        <!-- 主表格 - 使用SlinkyTable -->
        <div class="table-content">
            <SlinkyTable :data="albumList" :loading="loading" show-selection :show-table-header="true" show-index
                show-actions :border="true" :stripe="true" @selection-change="handleSelectionChange" @view="handleView"
                @edit="handleEdit" @delete="handleDelete" action-width="220" view-text="查看" edit-text="编辑"
                delete-text="删除" empty-text="暂无专辑数据" class="album-data-table">
                <!-- 专辑信息列 -->
                <el-table-column prop="title" label="专辑信息" align="left" min-width="160" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="album-info">
                            <div class="album-cover-wrapper">
                                <el-image :src="row.cover_url" :alt="row.title" class="album-cover" fit="cover">
                                    <template #error>
                                        <div class="image-placeholder">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                            </div>
                            <div class="album-details">
                                <div class="album-title">{{ row.title }}</div>
                                <div class="album-description">{{ row.description || '未设置描述' }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 分类列 -->
                <el-table-column prop="category_name" label="分类" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag v-if="row.category_name" type="info" size="small" effect="light">
                            {{ row.category_name }}
                        </el-tag>
                        <span v-else class="placeholder-text">未分类</span>
                    </template>
                </el-table-column>

                <!-- 统计信息列 -->
                <el-table-column label="统计" min-width="120" align="center">
                    <template #default="{ row }">
                        <div class="stats-info">
                            <div class="stat-item">
                                <el-icon class="stat-icon">
                                    <View />
                                </el-icon>
                                <span class="stat-value">{{ row.view_count || 0 }}</span>
                            </div>
                            <div class="stat-item">
                                <el-icon class="stat-icon">
                                    <StarFilled />
                                </el-icon>
                                <span class="stat-value">{{ row.like_count || 0 }}</span>
                            </div>
                            <div class="stat-item">
                                <el-icon class="stat-icon">
                                    <Picture />
                                </el-icon>
                                <span class="stat-value">{{ row.picture_count || 0 }}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 状态列 -->
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                        <AlbumStatusTag :status="row.status" />
                    </template>
                </el-table-column>

                <!-- 创建时间列 -->
                <el-table-column prop="created_at" label="创建时间" width="160" align="center">
                    <template #default="{ row }">
                        <div class="time-info">
                            <el-icon class="time-icon">
                                <Calendar />
                            </el-icon>
                            <span class="time-text">{{ formatDateTime(row.created_at) }}</span>
                        </div>
                    </template>
                </el-table-column>

                <!-- 自定义操作列 -->
                <template #actions="{ row }">
                    <div class="action-buttons-group" style="min-width: 220px;">
                        <el-button type="primary" link size="small" @click="handleView(row)">
                            <el-icon>
                                <View />
                            </el-icon>
                            查看
                        </el-button>
                        <el-button type="primary" link size="small" @click="handleEdit(row)">
                            <el-icon>
                                <Edit />
                            </el-icon>
                            编辑
                        </el-button>
                        <el-popconfirm :title="`确定要${row.status === 1 ? '禁用' : '启用'}专辑 ${row.title} 吗？`"
                            @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)">
                            <template #reference>
                                <el-button :type="row.status === 1 ? 'warning' : 'success'" link size="small">
                                    <el-icon>
                                        <component :is="row.status === 1 ? Lock : Unlock" />
                                    </el-icon>
                                    {{ row.status === 1 ? '禁用' : '启用' }}
                                </el-button>
                            </template>
                        </el-popconfirm>
                        <el-popconfirm :title="`确定要删除专辑 ${row.title} 吗？此操作不可恢复！`" @confirm="handleDelete(row)">
                            <template #reference>
                                <el-button type="danger" link size="small">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                    删除
                                </el-button>
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </SlinkyTable>
        </div>

        <!-- 分页组件 -->
        <div class="table-footer">
            <SinglePager :total="pagination.total" :current-page="pagination.page" :page-size="pagination.pageSize"
                @current-change="handleCurrentChange" @size-change="handleSizeChange" :background="true" show-jump-info
                class="pagination-wrapper" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import type { PictureAlbum } from '@/types/pictures';
import { Calendar, Check, Delete, Edit, Lock, Picture, StarFilled, Unlock, View } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';
import AlbumStatusTag from './AlbumStatusTag.vue';

const props = defineProps({
    albumList: {
        type: Array as () => PictureAlbum[],
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    pagination: {
        type: Object,
        default: () => ({
            page: 1,
            pageSize: 10,
            total: 0
        })
    }
});

const emit = defineEmits([
    'view',
    'edit',
    'delete',
    'change-status',
    'selection-change',
    'current-change',
    'size-change',
    'batch-status',
    'batch-delete'
]);

// 选中的行
const selectedRows = ref<PictureAlbum[]>([]);
const hasSelected = computed(() => selectedRows.value.length > 0);

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '-';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 处理选择变更
const handleSelectionChange = (selection: PictureAlbum[]) => {
    selectedRows.value = selection;
    emit('selection-change', selection);
};

// 查看专辑
const handleView = (row: PictureAlbum) => {
    emit('view', row);
};

// 编辑专辑
const handleEdit = (row: PictureAlbum) => {
    emit('edit', row);
};

// 删除专辑
const handleDelete = (row: PictureAlbum) => {
    emit('delete', row);
};

// 修改专辑状态
const handleChangeStatus = (id: string, status: number) => {
    emit('change-status', id, status);
};

// 处理分页大小变化
const handleSizeChange = (size: number) => {
    emit('size-change', size);
};

// 处理当前页变化
const handleCurrentChange = (page: number) => {
    emit('current-change', page);
};

// 批量操作
const handleBatchStatus = (status: number) => {
    emit('batch-status', status, selectedRows.value);
};

// 批量删除
const handleBatchDelete = () => {
    emit('batch-delete', selectedRows.value);
};
</script>

<style scoped lang="scss">
.album-table-container {
    .batch-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f0f9ff;
        border: 1px solid #e1f5fe;
        border-radius: 4px;
        margin-bottom: 16px;

        .batch-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1976d2;
            font-weight: 500;
        }

        .batch-actions {
            display: flex;
            gap: 8px;
        }
    }

    .table-content {
        background: white;
        border-radius: 4px;
        overflow: hidden;
    }

    .album-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .album-cover-wrapper {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            overflow: hidden;
            flex-shrink: 0;

            .album-cover {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
            }

            .image-placeholder {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f5f7fa;
                color: #909399;
                font-size: 20px;
            }
        }

        .album-details {
            min-width: 0;
            flex: 1;

            .album-title {
                font-weight: 500;
                color: #333;
            }

            .album-description {
                font-size: 12px;
                color: #666;
                margin-top: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .stats-info {
        display: flex;
        gap: 16px;
        justify-content: center;

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .stat-icon {
                color: #999;
                font-size: 14px;
            }

            .stat-value {
                font-size: 12px;
                color: #666;
            }
        }
    }

    .time-info {
        display: flex;
        align-items: center;
        gap: 4px;
        justify-content: center;

        .time-icon {
            color: #999;
            font-size: 14px;
        }

        .time-text {
            font-size: 12px;
            color: #666;
        }
    }

    .action-buttons-group {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;
    }

    .placeholder-text {
        color: #999;
        font-size: 12px;
    }

    .table-footer {
        padding: 16px 24px;
        background: var(--el-bg-color-page);
        border-top: 1px solid var(--el-border-color-lighter);
    }
}
</style>
