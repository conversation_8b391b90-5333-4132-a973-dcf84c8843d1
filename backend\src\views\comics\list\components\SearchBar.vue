<template>
  <div class="search-bar">
    <el-form :inline="true" :model="formData" class="search-form">
      <el-form-item label="搜索内容">
        <el-input v-model="formData.keyword" placeholder="请输入搜索关键字" clearable></el-input>
      </el-form-item>

      <el-form-item label="分类">
        <el-select v-model="formData.category_id" placeholder="请选择分类" clearable style="width: 120px;">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name || ''"
            :value="item.id || ''"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="进度">
        <el-select v-model="formData.status" placeholder="请选择进度" clearable style="width: 120px;">
          <el-option label="连载中" value="ongoing"></el-option>
          <el-option label="已完结" value="completed"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="formData.status" placeholder="请选择状态" clearable style="width: 120px;">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleRefresh">刷新</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';

import { ElMessage } from 'element-plus';

const emit = defineEmits(['search', 'reset', 'refresh']);

const props = defineProps({
  categoryOptions: {
    type: Array as PropType<{ id: string; name: string }[]>,
    required: true
  }
});

const formData = reactive({
  keyword: '',
  category_id: '',
  status: undefined
});

// 状态选项
const statusOptions = [
  { label: '上架中', value: 2 },
  { label: '下架中', value: 1 },
  { label: '暂时不可用', value: 0 }
];





// 搜索处理
const handleSearch = () => {
  emit('search', { ...formData });
};

// 重置处理
const handleReset = () => {
  // 重置表单
  Object.keys(formData).forEach(key => {
    formData[key] = '';
  });
  formData.status = undefined;
  
  // 触发重置事件
  emit('reset');
};

// 刷新处理
const handleRefresh = () => {
  emit('refresh');
};

// 组件挂载时加载分类
onMounted(() => {
});
</script>

<style scoped>
.search-bar {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .search-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}
</style> 