<template>
    <div class="footer-language-selector">
        <Dropdown v-model="selectedLanguage" :options="languageOptions" option-label="name" option-value="code"
            @change="changeLanguage" class="w-full" :placeholder="$t('language.selectLanguage')">
            <template #value="slotProps">
                <div v-if="slotProps.value" class="flex align-items-center">
                    <span class="language-flag mr-2">{{ getOptionFlag(slotProps.value) }}</span>
                    <span>{{ getOptionLabel(slotProps.value) }}</span>
                </div>
                <span v-else>{{ $t('language.selectLanguage') }}</span>
            </template>

            <template #option="slotProps">
                <div class="flex align-items-center">
                    <span class="language-flag mr-2">{{ slotProps.option.flag }}</span>
                    <div>
                        <div>{{ slotProps.option.name }}</div>
                        <div class="text-xs text-color-secondary">{{ slotProps.option.englishName }}</div>
                    </div>
                </div>
            </template>
        </Dropdown>
    </div>
</template>

<script setup lang="ts">
import { useLocaleStore } from '@/store/modules/locale';
import Dropdown from 'primevue/dropdown';
import { computed, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const localeStore = useLocaleStore();

// 语言选项
const languageOptions = computed(() => {
    return localeStore.getAllLocales().map(locale => ({
        name: locale.nativeName,
        englishName: locale.englishName,
        code: locale.code,
        flag: locale.flag
    }));
});

// 当前选择的语言
const selectedLanguage = ref(localeStore.locale);

// 监听store中的语言变化
watch(() => localeStore.locale, (newLocale) => {
    selectedLanguage.value = newLocale;
});

// 初始化
onMounted(() => {
    selectedLanguage.value = localeStore.locale;
});

// 切换语言
const changeLanguage = async () => {
    if (!selectedLanguage.value) return;

    try {
        if (window.$setLanguage) {
            await window.$setLanguage(selectedLanguage.value);
        } else {
            localeStore.setLocale(selectedLanguage.value);
        }
    } catch (error) {
        console.error('Failed to change language:', error);
    }
};

// 获取选项的标签
const getOptionLabel = (code: string) => {
    const option = languageOptions.value.find(opt => opt.code === code);
    return option ? option.name : code;
};

// 获取选项的国旗
const getOptionFlag = (code: string) => {
    const option = languageOptions.value.find(opt => opt.code === code);
    return option ? option.flag : '🌐';
};
</script>

<style scoped lang="scss">
.footer-language-selector {
    width: 100%;

    :deep(.p-dropdown) {
        width: 100%;

        .p-dropdown-label {
            display: flex;
            align-items: center;
            color: var(--text-color, var(--text-color-primary, inherit)) !important;
            background-color: var(--surface-card, var(--surface-0)) !important;
            border-color: var(--surface-border) !important;
        }

        .p-dropdown-trigger {
            color: var(--text-color-secondary, var(--text-600)) !important;
        }

        &:hover {
            .p-dropdown-trigger {
                color: var(--primary-color) !important;
            }
        }
    }

    // 特别处理下拉面板样式，确保在深色主题下正确显示
    :deep(.p-dropdown-panel) {
        background: var(--surface-overlay, var(--surface-card, #ffffff)) !important;
        border: 1px solid var(--surface-border) !important;

        .p-dropdown-items-wrapper {
            .p-dropdown-item {
                color: var(--text-color, var(--text-color-primary, inherit)) !important;

                &:hover {
                    background: var(--surface-hover, var(--surface-200)) !important;
                    color: var(--text-color, var(--text-color-primary, inherit)) !important;
                }

                &.p-highlight {
                    background: var(--primary-50, rgba(var(--primary-color-rgb, 24, 100, 171), 0.1)) !important;
                    color: var(--primary-color) !important;
                }
            }
        }
    }

    .language-flag {
        font-size: 1.2rem;
    }
}
</style>