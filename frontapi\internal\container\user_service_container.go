package container

import (
	userRepo "frontapi/internal/repository/users"
	videoRepo "frontapi/internal/repository/videos"
	userSrv "frontapi/internal/service/users"
)

// InitUserServices 初始化用户相关服务
func InitUserServices(b *ServiceBuilder) {
	//用户相关服务
	userRepoImpl := userRepo.NewuserRepository(b.DB())
	userLoginLogImpl := userRepo.NewUserLoginLogsRepository(b.DB())
	userFollowsRepoImpl := userRepo.NewUserFollowsRepository(b.DB())
	userWatchVideoHistoryRepoImpl := userRepo.NewUserWatchVideoHistoryRepository(b.DB())
	userVideoCollectionRepoImpl := userRepo.NewUserVideoCollectionRepository(b.DB())

	// 添加视频仓库
	videoRepoImpl := videoRepo.NewVideoRepository(b.DB())

	//UserFollowsRepositor

	container := b.Services()
	container.UserService = userSrv.NewUserService(userRepoImpl, userFollowsRepoImpl)
	container.UserLoginLogsService = userSrv.NewUserLoginLogsService(userLoginLogImpl, userRepoImpl)
	container.UserFollowsService = userSrv.NewUserFollowsService(userFollowsRepoImpl, userRepoImpl)
	container.UserWatchVideoHistoryService = userSrv.NewUserWatchVideoHistoryService(userWatchVideoHistoryRepoImpl, videoRepoImpl)
	container.UserVideoCollectionService = userSrv.NewUserVideoCollectionService(userVideoCollectionRepoImpl, videoRepoImpl)
}
