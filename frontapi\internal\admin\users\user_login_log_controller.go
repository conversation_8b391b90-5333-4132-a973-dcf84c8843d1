package users

import (
	"frontapi/internal/admin"
	userSrv "frontapi/internal/service/users"

	"github.com/gofiber/fiber/v2"
)

type UserLoginLogController struct {
	admin.BaseController
	loginLogService userSrv.UserLoginLogsService
	userService     userSrv.UserService
}

func NewUserLoginLogController(loginLogService userSrv.UserLoginLogsService, userService userSrv.UserService) *UserLoginLogController {
	return &UserLoginLogController{
		loginLogService: loginLogService,
		userService:     userService,
	}
}

// ListLoginLogs 获取登录日志列表
func (c *UserLoginLogController) ListLoginLogs(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数 - 映射前端提交的字段名
	keyword := reqInfo.Get("keyword").GetString()
	userId := reqInfo.Get("user_id").GetString()
	username := reqInfo.Get("username").GetString()
	loginStatus := reqInfo.Get("login_status").GetString()
	ipAddress := reqInfo.Get("ip_address").GetString()
	deviceType := reqInfo.Get("device_type").GetString()
	location := reqInfo.Get("location").GetString()
	startDate := reqInfo.Get("start_date").GetString()
	endDate := reqInfo.Get("end_date").GetString()

	// 构建查询条件，映射到登录日志仓库中ApplyConditions期望的字段名
	condition := map[string]interface{}{
		"keyword":      keyword,
		"user_id":      userId,
		"username":     username,
		"login_status": loginStatus,
		"ip_address":   ipAddress,
		"device_type":  deviceType,
		"location":     location,
		"start_date":   startDate,
		"end_date":     endDate,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "login_time DESC"
	}

	// 查询登录日志列表
	logsList, total, err := c.loginLogService.List(ctx.Context(), condition, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取登录日志列表失败: "+err.Error())
	}

	// 为日志补充用户名信息（如果需要的话）
	if len(logsList) > 0 {
		for i := range logsList {
			if logsList[i].UserID != "" && logsList[i].Username == "" {
				user, _ := c.userService.GetByID(ctx.Context(), logsList[i].UserID, false)
				if user != nil {
					// 处理null.String类型的用户名
					if user.Username.Valid {
						logsList[i].Username = user.Username.String
					}
				}
			}
		}
	}

	// 返回登录日志列表
	return c.SuccessList(ctx, logsList, total, page, pageSize)
}

// ClearLoginLogs 清空登录日志
func (c *UserLoginLogController) ClearLoginLogs(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		UserID string `json:"user_id"`
	}

	if err := ctx.BodyParser(&req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 清空日志
	var err error
	if req.UserID != "" {
		err = c.loginLogService.DeleteByCondition(ctx.Context(), map[string]interface{}{
			"user_id": req.UserID,
		})
	} else {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	if err != nil {
		return c.InternalServerError(ctx, "清空日志失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "清空日志成功")
}
