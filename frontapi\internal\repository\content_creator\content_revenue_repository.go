package content_creator

import (
	"frontapi/internal/models/content_creator"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type ContentRevenueRepository interface {
	base.ExtendedRepository[content_creator.ContentRevenue]
}

type contentRevenueRepository struct {
	base.ExtendedRepository[content_creator.ContentRevenue]
}

func NewContentRevenueRepository(db *gorm.DB) ContentRevenueRepository {
	return &contentRevenueRepository{
		ExtendedRepository: base.NewExtendedRepository[content_creator.ContentRevenue](db),
	}
}
