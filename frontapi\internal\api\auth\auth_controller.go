package auth

import (
	"frontapi/internal/api"
	userValidate "frontapi/internal/validation/users"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"
	"log"
	"time"

	"github.com/gofiber/fiber/v2"

	"frontapi/config"
	"frontapi/pkg/redis"
)

type AuthController struct {
	api.BaseController
}

// Login 处理登录请求
func (AuthController) Login(c *fiber.Ctx) error {
	// 定义验证模型
	loginData := new(userValidate.LoginRequest)

	// 解析和验证请求数据
	if err := c.BodyParser(loginData); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 验证请求数据
	v := validator.New()
	if err := validator.RegisterCustomValidations(v); err != nil {
		return utils.InternalServerError(c, "验证器初始化失败")
	}

	errors := v.Validate(loginData)
	if len(errors) > 0 {
		errorMessages := make([]string, 0)
		for _, err := range errors {
			errorMessages = append(errorMessages, err.FailedField+": "+err.Tag)
		}

		return utils.BadRequest(c, "数据验证失败", fiber.Map{
			"errors": errors,
		})
	}

	// 简单验证（实际应用中应该从数据库验证）
	if loginData.Username == "admin" && loginData.Password == "admin123" {
		userID := "admin" // 模拟用户ID

		// 生成令牌
		tokenExpiration := time.Duration(config.AppConfig.JWT.Expiry) * time.Hour
		token, err := utils.GenerateToken(userID, loginData.Username, tokenExpiration)
		if err != nil {
			return utils.InternalServerError(c, "生成令牌失败")
		}

		// 将令牌存储到Redis
		if err := redis.SetToken(userID, token, tokenExpiration); err != nil {
			log.Printf("存储令牌到Redis失败: %v", err)
		}
		return utils.Success(c, fiber.Map{
			"token":    token,
			"userId":   userID,
			"username": loginData.Username,
		}, "登录成功")
	}
	return utils.AuthFail(c, "用户名或密码错误")
}

// Logout 处理退出登录请求
func (AuthController) Logout(c *fiber.Ctx) error {
	userID := c.Locals("userId").(string)

	// 从Redis中删除令牌
	if err := redis.DeleteToken(userID); err != nil {
		log.Printf("从Redis删除令牌失败: %v", err)
	}
	return utils.Success(c, nil, "退出登录成功")
}

// Test 测试认证状态
func (AuthController) Test(c *fiber.Ctx) error {
	userID := c.Locals("userId")
	username := c.Locals("username")
	if userID == nil || username == nil {
		return utils.AuthFail(c, "认证失败")
	}
	return utils.Success(c, fiber.Map{
		"userId":   userID,
		"username": username,
	}, "认证成功")
}
