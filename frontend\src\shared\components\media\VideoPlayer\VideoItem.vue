<template>
  <div class="video-item">
    <div class="video-cover">
      <img :src="image" :alt="title">
      <div class="video-info-overlay">
        <span class="views">{{ formatNumber(views) }} 次观看</span>
        <span class="likes">{{ formatNumber(likes) }} 点赞</span>
      </div>
    </div>
    <div class="video-info">
      <h3 class="title">{{ title }}</h3>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  image: string
  views: number
  likes: number
}

defineProps<Props>()

const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}
</script>

<style scoped lang="scss">
.video-item {
  background: #fff;
  border-radius: 5px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .video-cover {
    position: relative;
    width: 100%;
    padding-top: 56.25%; // 16:9 比例

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .video-info-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 8px;
      background: linear-gradient(transparent, rgba(0,0,0,0.8));
      color: #fff;
      display: flex;
      justify-content: space-between;
      font-size: 11px;
      font-weight: 500;
    }
  }

  .video-info {
    padding: 8px;

    .title {
      margin: 0;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.4;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      color: #333;
    }
  }
}
</style> 