package migrations

import (
	"fmt"
	"frontapi/pkg/types"
	"time"

	"gorm.io/gorm"
)

// Migration20240722CreateUsersTables 创建用户相关数据表
type Migration20240722CreateUsersTables struct{}

// Name 返回迁移名称
func (m *Migration20240722CreateUsersTables) Name() string {
	return "20240722_create_users_tables"
}

// Up 执行迁移
func (m *Migration20240722CreateUsersTables) Up(db *gorm.DB) error {
	// 创建用户表
	err := db.Exec(`
	CREATE TABLE IF NOT EXISTS users (
		id VARCHAR(36) PRIMARY KEY,
		user_id VARCHAR(36) NOT NULL UNIQUE,
		username VARCHAR(50) NOT NULL UNIQUE,
		nickname VARCHAR(50),
		password VARCHAR(100) NOT NULL,
		salt VARCHAR(20) NOT NULL,
		reset_token VARCHAR(100),
		token_expiry DATETIME,
		avatar VARCHAR(255),
		gender TINYINT DEFAULT 0,
		email VARCHAR(100),
		phone VARCHAR(20),
		country VARCHAR(50) DEFAULT '中国',
		province VARCHAR(50),
		city VARCHAR(50),
		bio VARCHAR(255),
		user_level INT DEFAULT 0,
		member_level INT DEFAULT 0,
		member_expiry DATETIME,
		score INT DEFAULT 0,
		is_content_creator TINYINT DEFAULT 0,
		creator_verified TINYINT DEFAULT 0,
		status INT DEFAULT 1,
		reg_ip VARCHAR(50),
		reg_time DATETIME,
		last_login_time DATETIME,
		last_login_ip VARCHAR(50),
		last_active_time DATETIME,
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL,
		deleted_at DATETIME,
		INDEX idx_user_id (user_id),
		INDEX idx_username (username),
		INDEX idx_status (status),
		INDEX idx_reg_time (reg_time),
		INDEX idx_last_login_time (last_login_time)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`).Error

	if err != nil {
		return fmt.Errorf("创建用户表失败: %w", err)
	}

	// 创建用户登录日志表
	err = db.Exec(`
	CREATE TABLE IF NOT EXISTS ly_user_login_logs (
		id VARCHAR(36) PRIMARY KEY,
		user_id VARCHAR(36) NOT NULL,
		login_time DATETIME NOT NULL,
		type TINYINT DEFAULT 0 COMMENT '0:登录 1:登出',
		login_type VARCHAR(20) NOT NULL COMMENT 'password, token, oauth2',
		ip_address VARCHAR(50),
		device_type VARCHAR(20) COMMENT 'pc, mobile, tablet',
		os_name VARCHAR(50),
		os_version VARCHAR(20),
		browser_name VARCHAR(50),
		browser_version VARCHAR(20),
		user_agent VARCHAR(500),
		location VARCHAR(100),
		login_status VARCHAR(20) NOT NULL COMMENT 'success, failure',
		failure_reason VARCHAR(100),
		status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
		created_at DATETIME NOT NULL,
		updated_at DATETIME NOT NULL,
		INDEX idx_user_id (user_id),
		INDEX idx_login_time (login_time),
		INDEX idx_login_status (login_status),
		INDEX idx_status (status)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
	`).Error

	if err != nil {
		return fmt.Errorf("创建用户登录日志表失败: %w", err)
	}

	// 创建管理员用户
	salt := "admin123"
	password := "f0fd210a90a9a3a4a5a2d3a6a9bb0c77" // MD5("admin123" + salt)

	// 检查是否已存在admin用户
	var count int64
	db.Raw("SELECT COUNT(*) FROM users WHERE username = 'admin'").Scan(&count)

	if count == 0 {
		// 创建初始管理员账户
		err = db.Exec(`
		INSERT INTO users 
			(id, user_id, username, nickname, password, salt, avatar, user_level, is_content_creator, status, reg_time, created_at, updated_at) 
		VALUES 
			(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
			"1", "1", "admin", "系统管理员", password, salt, "/uploads/avatar/admin.png", 100, 1, 1, types.JSONTime(time.Now()), types.JSONTime(time.Now()), types.JSONTime(time.Now()),
		).Error

		if err != nil {
			return fmt.Errorf("创建管理员用户失败: %w", err)
		}
	}

	return nil
}

// Down 回滚迁移
func (m *Migration20240722CreateUsersTables) Down(db *gorm.DB) error {
	// 删除用户登录日志表
	err := db.Exec("DROP TABLE IF EXISTS ly_user_login_logs").Error
	if err != nil {
		return fmt.Errorf("删除用户登录日志表失败: %w", err)
	}

	// 删除用户表
	err = db.Exec("DROP TABLE IF EXISTS users").Error
	if err != nil {
		return fmt.Errorf("删除用户表失败: %w", err)
	}

	return nil
}
