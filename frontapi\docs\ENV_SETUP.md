# 环境变量配置指南

## 概述

项目已升级环境变量加载系统，现在支持在不同目录下运行时自动查找和加载 `.env` 配置文件。

## 新功能特性

### 1. 智能路径查找
系统会自动在以下位置查找 `.env` 文件：
- 当前工作目录
- 可执行文件所在目录  
- 通过 `go.mod` 文件定位的项目根目录
- 相对路径（`../`, `../../` 等）

### 2. 优先级顺序
环境变量的优先级顺序为：
1. 系统环境变量（最高优先级）
2. `.env` 文件中的配置
3. 代码中的默认值（最低优先级）

### 3. 多种数据类型支持
- `GetEnv(key, defaultValue)` - 字符串类型
- `GetEnvBool(key, defaultValue)` - 布尔类型
- `GetEnvInt(key, defaultValue)` - 整数类型

## 配置文件设置

### 1. 创建 .env 文件

在项目根目录（`frontapi/`）创建 `.env` 文件：

```bash
# 服务器配置
SERVER_HOST=localhost
SERVER_PORT=8080
ADMIN_SERVER_PORT=8081

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=lyvideos

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRY=168

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_EXPIRY=168

# CORS配置 - 重要：包含前端地址9527端口
CORS_ALLOW_ORIGINS=http://localhost:9527,http://localhost:8080,http://localhost:8081,http://localhost:3000,http://127.0.0.1:9527
CORS_ALLOW_METHODS=GET, POST, PUT, DELETE, OPTIONS, PATCH
CORS_ALLOW_HEADERS=Origin, Content-Type, Accept, Authorization, X-Admin-Token, X-Requested-With,x-client-info
CORS_ALLOW_CREDENTIALS=true
```

### 2. CORS 配置说明

为了解决前端（端口9527）访问后端（端口8081）的跨域问题，请确保 `CORS_ALLOW_ORIGINS` 包含：
- `http://localhost:9527` - 前端开发服务器
- `http://127.0.0.1:9527` - 备用地址
- 其他需要的域名和端口

## 运行方式支持

### 1. 在项目根目录运行
```bash
cd frontapi
go run cmd/main.go
```

### 2. 在 cmd 目录运行
```bash
cd frontapi/cmd
go run main.go
```

### 3. 编译后运行
```bash
cd frontapi
go build -o app cmd/main.go
./app
```

所有运行方式都能正确加载 `.env` 配置文件。

## 调试工具

### 1. 环境变量调试
运行调试工具检查配置加载状态：
```bash
cd frontapi
go run cmd/debug_env.go
```

### 2. 测试功能
运行单元测试验证功能：
```bash
cd frontapi
go test ./pkg/utils/ -v
```

## 代码使用示例

### 1. 基本用法
```go
import "frontapi/pkg/utils"

// 获取字符串配置
host := utils.GetEnv("SERVER_HOST", "localhost")

// 获取布尔配置
debug := utils.GetEnvBool("DEBUG_MODE", false)

// 获取整数配置
port := utils.GetEnvInt("SERVER_PORT", 8080)
```

### 2. 检查加载状态
```go
// 检查 .env 文件是否成功加载
if utils.IsEnvLoaded() {
    fmt.Println("配置文件加载成功")
    fmt.Printf("配置文件路径: %s\n", utils.GetLoadedEnvPath())
} else {
    fmt.Println("使用默认配置和系统环境变量")
}
```

## 故障排除

### 1. 配置未生效
- 检查 `.env` 文件是否存在于正确位置
- 运行 `go run cmd/debug_env.go` 查看加载状态
- 确认配置文件格式正确（无BOM，使用UTF-8编码）

### 2. CORS 错误
- 确认 `CORS_ALLOW_ORIGINS` 包含前端地址
- 重启后端服务器以应用新配置
- 检查前端请求的确切地址和端口

### 3. 路径问题
- 系统会自动查找多个可能的路径
- 优先使用项目根目录的 `.env` 文件
- 可以通过调试工具查看所有尝试的路径

## 最佳实践

1. **版本控制**：将 `.env` 文件添加到 `.gitignore`，避免提交敏感信息
2. **示例文件**：维护 `.env.example` 文件作为配置模板
3. **环境分离**：不同环境使用不同的 `.env` 文件
4. **安全性**：敏感信息（如密码、密钥）使用系统环境变量
5. **文档化**：在代码中注释配置项的用途和默认值

## 迁移指南

如果你之前使用的是旧版本的环境变量系统：

1. 创建 `.env` 文件并添加所需配置
2. 更新代码中的 `utils.GetEnv()` 调用（如果需要）
3. 测试不同运行方式确保配置正确加载
4. 删除旧的配置加载代码（如果有）

## 支持的配置项

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| SERVER_HOST | string | localhost | 服务器主机地址 |
| SERVER_PORT | string | 8080 | API服务器端口 |
| ADMIN_SERVER_PORT | string | 8081 | 管理后台端口 |
| CORS_ALLOW_ORIGINS | string | 见上文 | CORS允许的源 |
| CORS_ALLOW_METHODS | string | GET,POST,PUT,DELETE,OPTIONS,PATCH | CORS允许的方法 |
| CORS_ALLOW_HEADERS | string | 见上文 | CORS允许的头部 |
| CORS_ALLOW_CREDENTIALS | bool | true | CORS是否允许凭证 |
| DB_HOST | string | localhost | 数据库主机 |
| DB_PORT | string | 3306 | 数据库端口 |
| DB_USER | string | root | 数据库用户名 |
| DB_PASSWORD | string | root | 数据库密码 |
| DB_NAME | string | lyvideos | 数据库名称 |
| JWT_SECRET | string | your-secret-key | JWT密钥 |
| JWT_EXPIRY | int | 168 | JWT过期时间（小时） |
| REDIS_HOST | string | localhost | Redis主机 |
| REDIS_PORT | string | 6379 | Redis端口 |
| REDIS_PASSWORD | string | "" | Redis密码 |
| REDIS_DB | int | 0 | Redis数据库编号 |
| REDIS_EXPIRY | int | 168 | Redis缓存过期时间（小时） | 