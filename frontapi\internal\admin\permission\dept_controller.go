package permission

import (
	"frontapi/internal/admin"
	"frontapi/pkg/utils"

	"github.com/gofiber/fiber/v2"
)

// DeptController 部门控制器（简化版，使用模拟数据）
type DeptController struct {
	admin.BaseController
}

func NewDeptController() *DeptController {
	return &DeptController{}
}

// DeptInfo 部门信息
type DeptInfo struct {
	ID        int64      `json:"id"`
	ParentID  int64      `json:"parentId"`
	Name      string     `json:"name"`
	OrderNum  int        `json:"orderNum"`
	Leader    string     `json:"leader"`
	Phone     string     `json:"phone"`
	Email     string     `json:"email"`
	Status    int        `json:"status"`
	CreatedAt string     `json:"createdAt"`
	UpdatedAt string     `json:"updatedAt"`
	Children  []DeptInfo `json:"children,omitempty"`
}

// GetDeptList 获取部门列表
func (d *DeptController) GetDeptList(c *fiber.Ctx) error {
	// 这里应该从数据库获取部门列表，这里使用模拟数据
	depts := []DeptInfo{
		{
			ID:        1,
			ParentID:  0,
			Name:      "总公司",
			OrderNum:  1,
			Leader:    "张三",
			Phone:     "13800138000",
			Email:     "<EMAIL>",
			Status:    1,
			CreatedAt: "2023-01-01 00:00:00",
			UpdatedAt: "2023-01-01 00:00:00",
			Children: []DeptInfo{
				{
					ID:        2,
					ParentID:  1,
					Name:      "研发部",
					OrderNum:  1,
					Leader:    "李四",
					Phone:     "13800138001",
					Email:     "<EMAIL>",
					Status:    1,
					CreatedAt: "2023-01-01 00:00:00",
					UpdatedAt: "2023-01-01 00:00:00",
					Children: []DeptInfo{
						{
							ID:        4,
							ParentID:  2,
							Name:      "前端组",
							OrderNum:  1,
							Leader:    "王二",
							Phone:     "13800138003",
							Email:     "<EMAIL>",
							Status:    1,
							CreatedAt: "2023-01-01 00:00:00",
							UpdatedAt: "2023-01-01 00:00:00",
						},
						{
							ID:        5,
							ParentID:  2,
							Name:      "后端组",
							OrderNum:  2,
							Leader:    "麻子",
							Phone:     "13800138004",
							Email:     "<EMAIL>",
							Status:    1,
							CreatedAt: "2023-01-01 00:00:00",
							UpdatedAt: "2023-01-01 00:00:00",
						},
					},
				},
				{
					ID:        3,
					ParentID:  1,
					Name:      "市场部",
					OrderNum:  2,
					Leader:    "王五",
					Phone:     "13800138002",
					Email:     "<EMAIL>",
					Status:    1,
					CreatedAt: "2023-01-01 00:00:00",
					UpdatedAt: "2023-01-01 00:00:00",
				},
			},
		},
	}

	return utils.Success(c, depts, "获取部门列表成功")
}

// CreateDept 创建部门
func (d *DeptController) CreateDept(c *fiber.Ctx) error {
	var dept DeptInfo
	if err := c.BodyParser(&dept); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 这里应该将部门信息保存到数据库，这里只是模拟
	dept.ID = 6
	dept.CreatedAt = "2023-01-04 00:00:00"
	dept.UpdatedAt = "2023-01-04 00:00:00"
	dept.Status = 1

	return utils.Success(c, dept, "创建部门成功")
}

// UpdateDept 更新部门
func (d *DeptController) UpdateDept(c *fiber.Ctx) error {
	id := c.Params("id")
	if id == "" {
		return utils.BadRequest(c, "部门ID不能为空", nil)
	}

	var dept DeptInfo
	if err := c.BodyParser(&dept); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 这里应该从数据库中更新部门信息，这里只是模拟
	dept.UpdatedAt = "2023-01-05 00:00:00"

	return utils.Success(c, dept, "更新部门成功")
}

// DeleteDept 删除部门
func (d *DeptController) DeleteDept(c *fiber.Ctx) error {
	id := c.Params("id")
	if id == "" {
		return utils.BadRequest(c, "部门ID不能为空", nil)
	}

	// 这里应该从数据库中删除部门，这里只是模拟
	return utils.Success(c, nil, "删除部门成功")
}

// GetDeptInfo 获取部门详情
func (d *DeptController) GetDeptInfo(c *fiber.Ctx) error {
	id := c.Params("id")
	if id == "" {
		return utils.BadRequest(c, "部门ID不能为空", nil)
	}

	// 这里应该从数据库中获取部门信息，这里只是模拟
	dept := DeptInfo{
		ID:        2,
		ParentID:  1,
		Name:      "研发部",
		OrderNum:  1,
		Leader:    "李四",
		Phone:     "13800138001",
		Email:     "<EMAIL>",
		Status:    1,
		CreatedAt: "2023-01-01 00:00:00",
		UpdatedAt: "2023-01-01 00:00:00",
	}

	return utils.Success(c, dept, "获取部门详情成功")
}
