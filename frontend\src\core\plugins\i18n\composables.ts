/**
 * 国际化插件组合式API
 */

import { getLocaleInfo } from '@/config/locales.config';
import { getAllLocales } from '@/locales';
import { useLocaleStore } from '@/store/modules/locale';
import { computed, inject, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import type { I18nOptions } from './types';

// 提供给应用的i18n实例键
export const I18N_INJECTION_KEY = Symbol('i18n-plugin');

// 用于强制刷新的全局计数器
const refreshCounter = ref(0);

/**
 * 使用i18n插件的自定义组合式API
 * 扩展了vue-i18n的功能，提供了更多的语言相关功能
 */
export function useI18nPlugin() {
    // 强制刷新id
    const forceUpdateKey = ref(0);
    const forceRefresh = () => {
        forceUpdateKey.value++;
        refreshCounter.value++;
    };

    // 使用vue-i18n提供的基础功能
    const { t, locale, ...i18nRest } = useI18n({ useScope: 'global' });

    // 获取语言存储
    const localeStore = useLocaleStore();

    // 获取i18n插件选项
    const options = inject<I18nOptions>(I18N_INJECTION_KEY, {});

    // 获取当前语言信息
    const currentLocale = computed(() => localeStore.locale);

    // 获取当前语言的名称
    const currentLocaleName = computed(() => {
        // 计数器变化触发重新计算
        const _refresh = refreshCounter.value;
        const info = getLocaleInfo(localeStore.getLocale());
        return info?.nativeName || localeStore.getLocale();
    });

    // 获取当前语言的短代码
    const currentLocaleShort = computed(() => {
        const _refresh = refreshCounter.value;
        return localeStore.getLanguageCode();
    });

    // 获取当前语言的国旗
    const currentLocaleFlag = computed(() => {
        const _refresh = refreshCounter.value;
        const info = getLocaleInfo(localeStore.getLocale());
        return info?.flag || '🌐';
    });

    // 检查是否为RTL语言
    const isRTL = computed(() => {
        const _refresh = refreshCounter.value;
        const rtlLocales = ['ar', 'he', 'fa', 'ur'];
        return rtlLocales.some(code => currentLocale.value.startsWith(code));
    });

    // 切换语言函数
    const changeLocale = async (newLocale: string): Promise<boolean> => {
        try {
            // 优先使用全局setLanguage方法
            if (window.$setLanguage) {
                const success = await window.$setLanguage(newLocale);
                if (success) {
                    forceRefresh();
                    return true;
                }
            }

            // 后备方案：使用store
            localeStore.setLocale(newLocale);
            forceRefresh();
            return true;
        } catch (error) {
            console.error('Failed to change locale:', error);
            return false;
        }
    };

    // 获取所有可用语言
    const availableLocales = computed(() => {
        const _refresh = refreshCounter.value;
        return localeStore.getSupportedLocales();
    });

    // 获取所有语言信息
    const allLocales = computed(() => {
        const _refresh = refreshCounter.value;
        return getAllLocales();
    });

    // 监听语言变化
    watch(locale, () => {
        forceRefresh();
    });

    // 监听全局事件
    watch(() => localeStore.locale, () => {
        forceRefresh();
    });

    return {
        // 从vue-i18n继承的基本功能
        t,
        locale,
        ...i18nRest,

        // 扩展功能
        options,
        currentLocale,
        currentLocaleName,
        currentLocaleShort,
        currentLocaleFlag,
        isRTL,
        changeLocale,
        availableLocales,
        allLocales,
        forceUpdateKey,
        forceRefresh
    };
}

/**
 * 翻译助手函数 - 简化版，供应用使用
 */
export function useTranslation() {
    const {
        t,
        locale,
        currentLocaleName,
        changeLocale,
        allLocales,
        forceUpdateKey,
        forceRefresh
    } = useI18nPlugin();

    return {
        t,
        locale,
        localeName: currentLocaleName,
        changeLocale,
        locales: allLocales,
        forceUpdateKey,
        forceRefresh
    };
} 