package users

import (
	"context"
	"errors"
	"fmt"
	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
	"frontapi/pkg/utils"

	"gorm.io/gorm"
)

// UserRepository 用户仓库接口
type UserRepository interface {
	base.ExtendedRepository[users.User]
	// 业务特定方法
	GetUsersByIds(ctx context.Context, ids []string, userId *string) ([]users.User, error)
	FindByUsername(ctx context.Context, username string) (*users.User, error)
	//登录根据用户名、邮箱、密码查询用户
	checkLoginUser(ctx context.Context, username, email, password string) (*users.User, error)

	//获取用户全部评论
	ListUserAllComments(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*users.UserComment, int64, error)
}

// userRepository 用户仓库实现
type userRepository struct {
	base.ExtendedRepository[users.User]
}

// NewuserRepository 创建用户仓库实例
func NewuserRepository(db *gorm.DB) UserRepository {
	repo := &userRepository{
		ExtendedRepository: base.NewExtendedRepository[users.User](db),
	}
	// 设置调用者实例，这样基础仓库就能识别并使用子类的ApplyConditions方法
	repo.ExtendedRepository.SetCaller(repo)
	return repo
}

// UpdateViewCount 重写基础仓库的UpdateViewCount方法
// 用户表没有view_count字段，所以直接返回nil（不做任何操作）
func (r *userRepository) UpdateViewCount(ctx context.Context, id string) error {
	// 用户表没有view_count字段，不需要更新浏览次数
	return nil
}

// 获取用户列表
func (r *userRepository) GetUsersByIds(ctx context.Context, ids []string, userId *string) ([]users.User, error) {
	var users []users.User
	//users left join ly_user_follows查询用户是否关注

	var err error
	if userId != nil {
		//SELECT ly_users.*,ly_user_follows.id from ly_users left join ly_user_follows on ly_user_follows.followed_id = ly_users.id and ly_user_follows.user_id="01fEb1d5-83Fd-55fC-7eAD-Cc142A9FB17B" where ly_users.id in ("01fEb1d5-83Fd-55fC-7eAD-Cc142A9FB17B")
		err = r.GetDBWithContext(ctx).Model(&users).Select("ly_users.*,if(ly_user_follows.id,true,false) as is_follow").
			Joins("left join ly_user_follows on ly_user_follows.followed_id = ly_users.id and ly_user_follows.user_id=?", *userId).
			Where("ly_users.id IN ?", ids).Find(&users).Error
	} else {
		err = r.GetDBWithContext(ctx).Model(&users).Where("id IN ?", ids).Find(&users).Error
	}
	if err != nil {
		return nil, err
	}
	return users, nil
}

// FindByUsername 根据用户名查找用户
func (r *userRepository) FindByUsername(ctx context.Context, username string) (*users.User, error) {
	condition := map[string]interface{}{"username": username}
	return r.FindOneByCondition(ctx, condition, "")
}

// 登录根据用户名、邮箱、密码查询用户
func (r *userRepository) checkLoginUser(ctx context.Context, username, email, password string) (*users.User, error) {
	var user users.User
	err := r.GetDBWithContext(ctx).Where("username = ? OR email = ?", username, email).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
	}
	// 处理null类型的Salt字段
	saltStr := ""
	if user.Salt.Valid {
		saltStr = user.Salt.String
	}

	if !utils.ComparePasswords(user.Password, saltStr, password) {
		return nil, nil
	}
	return &user, nil
}

// ListUserAllComments 获取用户全部评论
func (r *userRepository) ListUserAllComments(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*users.UserComment, int64, error) {
	var comments []*users.UserComment
	var total int64

	//union ly_video_comments,ly_shorts_comments,ly_post_comments
	db := r.GetDBWithContext(ctx)
	db = db.Model(&users.UserComment{})
	sql := "select user_id,user_nickname,user_avatar,content,video_id as relation_id, heat,like_count,reply_count,created_at,updated_at,status,1 as entity_type,id as entity_id from ly_video_comments union all "
	sql += "select user_id,user_nickname,user_avatar,content,short_id as relation_id,heat,like_count,reply_count,created_at,updated_at,status,2 as entity_type,id as entity_id from ly_shorts_comments union all "
	sql += "select user_id,user_nickname,user_avatar,content,post_id as relation_id,heat,like_count,reply_count,created_at,updated_at,status,3 as entity_type,id as entity_id from ly_post_comments "
	if condition != nil {
		if condition["keyword"] != nil && condition["keyword"] != "" {
			keyword := condition["keyword"].(string)
			db = db.Where("content LIKE ?", "%"+keyword+"%")
		}
		if condition["entity_type"] != nil && condition["entity_type"] != "" {
			entityType := condition["entity_type"].(int)
			db = db.Where("entity_type = ?", entityType)
		}
		if condition["relation_id"] != nil && condition["relation_id"] != "" {
			relationID := condition["relation_id"].(string)
			db = db.Where("relation_id = ?", relationID)
		}
		if condition["user_id"] != nil && condition["user_id"] != "" {
			userID := condition["user_id"].(string)
			db = db.Where("user_id = ?", userID)
		}
	}

	err := db.Raw(sql).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Raw(sql).Offset((page - 1) * pageSize).Limit(pageSize).Order(orderBy).Find(&comments).Error
	if err != nil {
		return nil, 0, err
	}

	return comments, total, nil
}

// ApplyConditions 实现ConditionApplier接口，自定义用户查询条件应用逻辑
func (r *userRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	if condition == nil {
		return query
	}

	// 调试日志：打印接收到的查询条件
	fmt.Printf("用户仓库自定义条件应用 - 接收到的条件: %+v\n", condition)

	// 处理关键字搜索
	if condition["keyword"] != nil && condition["keyword"] != "" {
		keyword := condition["keyword"].(string)
		query = query.Where("username LIKE ? OR nickname LIKE ? OR email LIKE ? OR phone LIKE ? OR bio LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 处理状态过滤
	if condition["status"] != nil && condition["status"] != "" {
		if status, ok := condition["status"].(int); ok && status > -999 {
			query = query.Where("status = ?", status)
		}
	}

	// 处理注册时间范围
	if condition["reg_time_start"] != nil && condition["reg_time_start"] != "" {
		regTimeStart := condition["reg_time_start"].(string)
		query = query.Where("reg_time >= ?", regTimeStart)
	}
	if condition["reg_time_end"] != nil && condition["reg_time_end"] != "" {
		regTimeEnd := condition["reg_time_end"].(string)
		query = query.Where("reg_time <= ?", regTimeEnd)
	}

	// 处理用户类型过滤
	if condition["user_type"] != nil && condition["user_type"] != "" {
		if userType, ok := condition["user_type"].(int); ok && userType > -999 {
			query = query.Where("user_type = ?", userType)
		}
	}

	// 处理内容创作者过滤
	if condition["is_content_creator"] != nil && condition["is_content_creator"] != "" {
		if isContentCreator, ok := condition["is_content_creator"].(int); ok && isContentCreator > -999 {
			query = query.Where("is_content_creator = ?", isContentCreator)
		}
	}

	// 处理创作者等级过滤
	if condition["creator_level"] != nil && condition["creator_level"] != "" {
		if creatorLevel, ok := condition["creator_level"].(int); ok && creatorLevel > -999 {
			query = query.Where("creator_level = ?", creatorLevel)
		}
	}

	// 处理通用时间范围条件
	if condition["created_at_start"] != nil && condition["created_at_start"] != "" {
		query = query.Where("created_at >= ?", condition["created_at_start"])
	}
	if condition["created_at_end"] != nil && condition["created_at_end"] != "" {
		query = query.Where("created_at <= ?", condition["created_at_end"])
	}
	if condition["updated_at_start"] != nil && condition["updated_at_start"] != "" {
		query = query.Where("updated_at >= ?", condition["updated_at_start"])
	}
	if condition["updated_at_end"] != nil && condition["updated_at_end"] != "" {
		query = query.Where("updated_at <= ?", condition["updated_at_end"])
	}

	// 处理其他字段的等值查询
	for key, value := range condition {
		if value == nil || value == "" {
			continue
		}

		// 跳过已处理的字段
		switch key {
		case "keyword", "status", "reg_time_start", "reg_time_end",
			"is_content_creator", "creator_level", "user_type",
			"created_at_start", "created_at_end", "updated_at_start", "updated_at_end":
			continue
		default:
			// 默认等值查询
			query = query.Where(key+" = ?", value)
		}
	}

	return query
}
