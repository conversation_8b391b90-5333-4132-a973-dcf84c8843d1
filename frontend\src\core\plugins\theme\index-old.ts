/**
 * 主题插件 - 基于PrimeVue和CSS变量
 * 支持Tailwind CSS集成
 */
import themes from '@/shared/themes';
import { themeManager } from '@/shared/themes/theme-manager';
import { PrimeVueConfiguration } from 'primevue/config';
import { App, inject } from 'vue';

/**
 * 创建主题插件
 */
export default function createThemePlugin() {
    return {
        install(app: App) {
            // 注册所有主题
            themeManager.registerThemes(themes);

            // 注册为全局属性
            app.config.globalProperties.$themeManager = themeManager;

            // 提供依赖注入
            app.provide('themeManager', themeManager);

            // 初始化主题
            themeManager.init();

            // 配置PrimeVue主题支持
            const primevue = app.config.globalProperties.$primevue as PrimeVueConfiguration;
            if (primevue) {
                // 监听主题变化，动态更新PrimeVue主题
                themeManager.currentTheme.value && updatePrimeVueTheme(primevue, themeManager.currentTheme.value);

                // 监听主题变化事件
                document.addEventListener('themechange', (e: any) => {
                    const { theme } = e.detail;
                    if (theme && theme.name) {
                        updatePrimeVueTheme(primevue, theme.name);
                    }
                });
            }
        }
    };
}

/**
 * 更新PrimeVue主题
 */
function updatePrimeVueTheme(primevue: PrimeVueConfiguration, themeName: string) {
    // PrimeVue主题匹配
    const primeTheme = getPrimeVueThemeName(themeName);
    if (primevue.config && primevue.config.theme !== primeTheme) {
        primevue.config.theme = primeTheme;
    }
}

/**
 * 获取对应的PrimeVue主题名称
 */
function getPrimeVueThemeName(themeName: string): string {
    // 主题映射表
    const themeMap: Record<string, string> = {
        'modern': 'lara-light-blue',
        'warm': 'lara-light-amber',
        'dark': 'lara-dark-purple',
        'fresh': 'lara-light-teal',
        'charm': 'lara-light-pink',
        'mysterious': 'lara-light-indigo'
    };

    return themeMap[themeName] || 'lara-light-blue';
}

/**
 * 使用主题 - 组合式API函数
 */
export function useTheme() {
    // 尝试通过依赖注入获取
    const injectedTheme = inject('themeManager');
    if (injectedTheme) {
        return injectedTheme;
    }

    // 如果依赖注入失败，返回全局主题管理器
    return themeManager;
}

// 导出主题管理器
export { themeManager };

