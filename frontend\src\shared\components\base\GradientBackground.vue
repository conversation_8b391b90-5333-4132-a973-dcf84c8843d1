<template>
    <div class="gradient-background" :class="[
        `gradient-background--${type}`,
        `gradient-background--${direction}`,
        { 'gradient-background--animated': animated }
    ]" :style="customStyle">
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    // 背景类型：primary, secondary, accent, success, warning, error, info, default
    type: {
        type: String,
        default: 'primary',
        validator: (value: string) => [
            'primary',
            'secondary',
            'accent',
            'success',
            'warning',
            'error',
            'info',
            'default'
        ].includes(value)
    },
    // 渐变方向：horizontal, vertical, diagonal, radial
    direction: {
        type: String,
        default: 'diagonal',
        validator: (value: string) => ['horizontal', 'vertical', 'diagonal', 'radial'].includes(value)
    },
    // 是否启用动画效果
    animated: {
        type: Boolean,
        default: false
    },
    // 自定义透明度 (0-100)
    opacity: {
        type: Number,
        default: 100,
        validator: (value: number) => value >= 0 && value <= 100
    },
    // 自定义样式
    customStyles: {
        type: Object,
        default: () => ({})
    }
});

// 计算自定义样式
const customStyle = computed(() => {
    const opacity = props.opacity / 100;
    return {
        ...props.customStyles,
        '--gradient-opacity': opacity
    };
});
</script>

<style lang="scss" scoped>
.gradient-background {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    // 渐变方向
    &--horizontal {
        &.gradient-background--primary {
            background: linear-gradient(to right, rgba(142, 45, 226, var(--gradient-opacity, 1)), rgba(122, 37, 201, var(--gradient-opacity, 1)), rgba(74, 0, 224, var(--gradient-opacity, 1)));
        }

        &.gradient-background--secondary {
            background: linear-gradient(to right, rgba(74, 0, 224, var(--gradient-opacity, 1)), rgba(59, 0, 179, var(--gradient-opacity, 1)), rgba(44, 0, 134, var(--gradient-opacity, 1)));
        }

        &.gradient-background--accent {
            background: linear-gradient(to right, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--success {
            background: linear-gradient(to right, rgba(76, 175, 80, var(--gradient-opacity, 1)), rgba(67, 160, 71, var(--gradient-opacity, 1)), rgba(56, 142, 60, var(--gradient-opacity, 1)));
        }

        &.gradient-background--warning {
            background: linear-gradient(to right, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--error {
            background: linear-gradient(to right, rgba(244, 67, 54, var(--gradient-opacity, 1)), rgba(229, 57, 53, var(--gradient-opacity, 1)), rgba(211, 47, 47, var(--gradient-opacity, 1)));
        }

        &.gradient-background--info {
            background: linear-gradient(to right, rgba(33, 150, 243, var(--gradient-opacity, 1)), rgba(30, 136, 229, var(--gradient-opacity, 1)), rgba(25, 118, 210, var(--gradient-opacity, 1)));
        }

        &.gradient-background--default {
            background: var(--gradient-background, linear-gradient(to right, rgba(142, 45, 226, 0.05), rgba(122, 37, 201, 0.03), rgba(74, 0, 224, 0.02)));
        }
    }

    &--vertical {
        &.gradient-background--primary {
            background: linear-gradient(to bottom, rgba(142, 45, 226, var(--gradient-opacity, 1)), rgba(122, 37, 201, var(--gradient-opacity, 1)), rgba(74, 0, 224, var(--gradient-opacity, 1)));
        }

        &.gradient-background--secondary {
            background: linear-gradient(to bottom, rgba(74, 0, 224, var(--gradient-opacity, 1)), rgba(59, 0, 179, var(--gradient-opacity, 1)), rgba(44, 0, 134, var(--gradient-opacity, 1)));
        }

        &.gradient-background--accent {
            background: linear-gradient(to bottom, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--success {
            background: linear-gradient(to bottom, rgba(76, 175, 80, var(--gradient-opacity, 1)), rgba(67, 160, 71, var(--gradient-opacity, 1)), rgba(56, 142, 60, var(--gradient-opacity, 1)));
        }

        &.gradient-background--warning {
            background: linear-gradient(to bottom, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--error {
            background: linear-gradient(to bottom, rgba(244, 67, 54, var(--gradient-opacity, 1)), rgba(229, 57, 53, var(--gradient-opacity, 1)), rgba(211, 47, 47, var(--gradient-opacity, 1)));
        }

        &.gradient-background--info {
            background: linear-gradient(to bottom, rgba(33, 150, 243, var(--gradient-opacity, 1)), rgba(30, 136, 229, var(--gradient-opacity, 1)), rgba(25, 118, 210, var(--gradient-opacity, 1)));
        }

        &.gradient-background--default {
            background: var(--gradient-background, linear-gradient(to bottom, rgba(142, 45, 226, 0.05), rgba(122, 37, 201, 0.03), rgba(74, 0, 224, 0.02)));
        }
    }

    &--diagonal {
        &.gradient-background--primary {
            background: linear-gradient(45deg, rgba(142, 45, 226, var(--gradient-opacity, 1)), rgba(122, 37, 201, var(--gradient-opacity, 1)), rgba(74, 0, 224, var(--gradient-opacity, 1)));
        }

        &.gradient-background--secondary {
            background: linear-gradient(45deg, rgba(74, 0, 224, var(--gradient-opacity, 1)), rgba(59, 0, 179, var(--gradient-opacity, 1)), rgba(44, 0, 134, var(--gradient-opacity, 1)));
        }

        &.gradient-background--accent {
            background: linear-gradient(45deg, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--success {
            background: linear-gradient(45deg, rgba(76, 175, 80, var(--gradient-opacity, 1)), rgba(67, 160, 71, var(--gradient-opacity, 1)), rgba(56, 142, 60, var(--gradient-opacity, 1)));
        }

        &.gradient-background--warning {
            background: linear-gradient(45deg, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--error {
            background: linear-gradient(45deg, rgba(244, 67, 54, var(--gradient-opacity, 1)), rgba(229, 57, 53, var(--gradient-opacity, 1)), rgba(211, 47, 47, var(--gradient-opacity, 1)));
        }

        &.gradient-background--info {
            background: linear-gradient(45deg, rgba(33, 150, 243, var(--gradient-opacity, 1)), rgba(30, 136, 229, var(--gradient-opacity, 1)), rgba(25, 118, 210, var(--gradient-opacity, 1)));
        }

        &.gradient-background--default {
            background: var(--gradient-background, linear-gradient(45deg, rgba(142, 45, 226, 0.05), rgba(122, 37, 201, 0.03), rgba(74, 0, 224, 0.02)));
        }
    }

    &--radial {
        &.gradient-background--primary {
            background: radial-gradient(circle, rgba(142, 45, 226, var(--gradient-opacity, 1)), rgba(122, 37, 201, var(--gradient-opacity, 1)), rgba(74, 0, 224, var(--gradient-opacity, 1)));
        }

        &.gradient-background--secondary {
            background: radial-gradient(circle, rgba(74, 0, 224, var(--gradient-opacity, 1)), rgba(59, 0, 179, var(--gradient-opacity, 1)), rgba(44, 0, 134, var(--gradient-opacity, 1)));
        }

        &.gradient-background--accent {
            background: radial-gradient(circle, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--success {
            background: radial-gradient(circle, rgba(76, 175, 80, var(--gradient-opacity, 1)), rgba(67, 160, 71, var(--gradient-opacity, 1)), rgba(56, 142, 60, var(--gradient-opacity, 1)));
        }

        &.gradient-background--warning {
            background: radial-gradient(circle, rgba(255, 152, 0, var(--gradient-opacity, 1)), rgba(230, 137, 0, var(--gradient-opacity, 1)), rgba(204, 122, 0, var(--gradient-opacity, 1)));
        }

        &.gradient-background--error {
            background: radial-gradient(circle, rgba(244, 67, 54, var(--gradient-opacity, 1)), rgba(229, 57, 53, var(--gradient-opacity, 1)), rgba(211, 47, 47, var(--gradient-opacity, 1)));
        }

        &.gradient-background--info {
            background: radial-gradient(circle, rgba(33, 150, 243, var(--gradient-opacity, 1)), rgba(30, 136, 229, var(--gradient-opacity, 1)), rgba(25, 118, 210, var(--gradient-opacity, 1)));
        }

        &.gradient-background--default {
            background: var(--gradient-background, radial-gradient(circle, rgba(142, 45, 226, 0.05), rgba(122, 37, 201, 0.03), rgba(74, 0, 224, 0.02)));
        }
    }

    // 动画效果
    &--animated {
        position: relative;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            opacity: 0.7;
            filter: blur(20px);
            z-index: -1;
            animation: pulse 8s ease-in-out infinite alternate;
        }
    }
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }

    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }

    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}
</style>