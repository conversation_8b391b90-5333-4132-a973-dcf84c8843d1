package comics

import (
	"frontapi/internal/api"
	comics_service "frontapi/internal/service/comics"
	comicsTypings "frontapi/internal/typings/comics"
	"strings"

	"github.com/gofiber/fiber/v2"
)

// ComicController 漫画处理器
type ComicController struct {
	api.BaseController
	comicService       comics_service.ComicService
	chapterService     comics_service.ComicChapterService
	favoriteService    comics_service.ComicFavoriteService
	commentService     comics_service.ComicCommentService
	readHistoryService comics_service.ComicReadHistoryService
}

// NewComicController 创建漫画处理器
func NewComicController(
	comicService comics_service.ComicService,
	chapterService comics_service.ComicChapterService,
	favoriteService comics_service.ComicFavoriteService,
	commentService comics_service.ComicCommentService,
	readHistoryService comics_service.ComicReadHistoryService,
) *ComicController {
	return &ComicController{
		comicService:       comicService,
		chapterService:     chapterService,
		favoriteService:    favoriteService,
		commentService:     commentService,
		readHistoryService: readHistoryService,
	}
}

// 获取漫画列表
func (c *ComicController) GetComicList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	categoryId := reqInfo.Get("categoryId").GetString()
	sortBy := reqInfo.Get("sortBy").GetString()
	//'popularity' | 'latest' | 'rating'
	sortBy = strings.ToLower(sortBy)
	switch sortBy {
	case "popularity":
		sortBy = "read_count DESC,favorite_count DESC,rating DESC"
	case "latest":
		sortBy = "updated_at DESC,read_count DESC,favorite_count DESC,rating DESC"
	case "rating":
		sortBy = "rating DESC,read_count DESC,favorite_count DESC"
	default:
		sortBy = "read_count DESC,favorite_count DESC,rating DESC"
	}
	condition := map[string]interface{}{
		"keyword":     keyword,
		"category_id": categoryId,
	}
	comics, total, err := c.comicService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := comicsTypings.ConvertComicListResponse(comics, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// 获取漫画详情
func (c *ComicController) GetComicDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	comicId := reqInfo.Get("comicId").GetString()
	comic, err := c.comicService.GetByID(ctx.Context(), comicId, false)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	if pageNo == 0 {
		pageNo = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	//获取章节
	chapters, total, err := c.chapterService.List(ctx.Context(), map[string]interface{}{"comic_id": comicId, "status": 1}, "chapter_number DESC", pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	comic.ChapterCount = int(total)
	comic.Chapters = chapters
	response := comicsTypings.ConvertComicDetail(comic)
	return c.Success(ctx, response)
}

// 获取章节列表
func (c *ComicController) GetChapterList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	comicId := reqInfo.Get("comicId").GetString()
	condition := map[string]interface{}{
		"comic_id": comicId,
	}
	chapters, total, err := c.chapterService.List(ctx.Context(), condition, "chapter_number DESC", pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := comicsTypings.ConvertComicChapterListResponse(chapters, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// 获取章节详情
func (c *ComicController) GetChapterDetail(ctx *fiber.Ctx) error {
	return c.Success(ctx, "success")
}

// 获取章节内容
func (c *ComicController) GetChapterContent(ctx *fiber.Ctx) error {
	return c.Success(ctx, "success")
}

// 获取相关漫画
func (c *ComicController) GetRelatedComics(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	comicId := reqInfo.Get("comicId").GetString()
	userId := reqInfo.Get("userId").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	if pageNo == 0 {
		pageNo = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	condition := map[string]interface{}{
		"comic_id": comicId,
		"user_id":  userId,
	}
	comics, total, err := c.comicService.GetRelatedComics(ctx.Context(), condition, "read_count DESC,favorite_count DESC,rating DESC", pageNo, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := comicsTypings.ConvertComicListResponse(comics, total, pageNo, pageSize)
	return c.Success(ctx, response)
}
