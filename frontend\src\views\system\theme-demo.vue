<template>
    <div class="theme-demo-page">
        <div class="page-header">
            <h1>{{ t('theme.themeSystemDemo') }}</h1>
            <p>{{ t('theme.themeSystemDescription') }}</p>
        </div>

        <div class="demo-section">
            <h2>{{ t('theme.themeSelector') }}</h2>
            <div class="demo-component">
                <NewThemeSelector />
            </div>
            <div class="component-description">
                <p>{{ t('theme.themeSelectorDescription') }}</p>
                <pre>import NewThemeSelector from '@/shared/components/ThemeSelector/NewThemeSelector.vue';</pre>
            </div>
        </div>

        <div class="demo-section">
            <h2>{{ t('theme.themeVariables') }}</h2>
            <div class="theme-variables">
                <div v-for="(group, groupName) in themeVariableGroups" :key="groupName" class="variable-group">
                    <h3>{{ groupName }}</h3>
                    <div class="variables-grid">
                        <div v-for="variable in group" :key="variable.name" class="variable-item">
                            <div class="variable-preview" :style="getVariableStyle(variable.name)"></div>
                            <div class="variable-info">
                                <code>{{ variable.name }}</code>
                                <span>{{ variable.value }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>{{ t('theme.componentPreview') }}</h2>
            <div class="component-grid">
                <div class="component-item">
                    <h4>{{ t('theme.buttons') }}</h4>
                    <div class="component-preview">
                        <Button label="Primary" class="mr-2" />
                        <Button label="Secondary" severity="secondary" class="mr-2" />
                        <Button label="Success" severity="success" class="mr-2" />
                        <Button label="Warning" severity="warning" class="mr-2" />
                        <Button label="Danger" severity="danger" />
                    </div>
                </div>

                <div class="component-item">
                    <h4>{{ t('theme.inputs') }}</h4>
                    <div class="component-preview">
                        <InputText v-model="inputValue" placeholder="Enter text" class="mr-2" />
                        <Dropdown v-model="selectedOption" :options="dropdownOptions" optionLabel="name"
                            placeholder="Select" />
                    </div>
                </div>

                <div class="component-item">
                    <h4>{{ t('theme.cards') }}</h4>
                    <div class="component-preview">
                        <Card class="demo-card">
                            <template #header>
                                <div class="card-header">{{ t('theme.cardTitle') }}</div>
                            </template>
                            <template #content>
                                <p>{{ t('theme.cardContent') }}</p>
                            </template>
                            <template #footer>
                                <Button label="Action" icon="pi pi-check" />
                            </template>
                        </Card>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>{{ t('theme.apiUsage') }}</h2>
            <div class="code-example">
                <pre><code>
                // 使用主题管理器
                import { useTheme } from '@/core/plugins/theme';
                import { useThemeStore } from '@/store/modules/theme';

                // 组件中使用
                const themeManager = useTheme();
                const themeStore = useThemeStore();

                // 切换主题
                themeManager.setTheme('dark');

                // 切换暗黑模式
                themeStore.toggleDarkMode();

                // 跟随系统主题
                themeManager.enableSystemTheme();

                // 检测是否为暗黑模式
                const isDark = themeStore.isDarkMode;
            </code></pre>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTheme } from '@/core/plugins/theme';
import NewThemeSelector from '@/shared/components/ThemeSelector/NewThemeSelector.vue';
import { useThemeStore } from '@/store/modules/theme';
import Button from 'primevue/button';
import Card from 'primevue/card';
import Dropdown from 'primevue/dropdown';
import InputText from 'primevue/inputtext';
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

// i18n
const { t } = useI18n();

// 主题管理
const themeManager = useTheme();
const themeStore = useThemeStore();

// 组件演示数据
const inputValue = ref('');
const selectedOption = ref(null);
const dropdownOptions = ref([
    { name: 'Option 1', value: 1 },
    { name: 'Option 2', value: 2 },
    { name: 'Option 3', value: 3 }
]);

// 主题变量分组
const themeVariableGroups = ref({
    'Primary Colors': [
        { name: '--primary-color', value: '' },
        { name: '--primary-color-text', value: '' },
        { name: '--primary-50', value: '' },
        { name: '--primary-100', value: '' },
        { name: '--primary-500', value: '' },
        { name: '--primary-900', value: '' }
    ],
    'Surface': [
        { name: '--surface-ground', value: '' },
        { name: '--surface-section', value: '' },
        { name: '--surface-card', value: '' },
        { name: '--surface-overlay', value: '' },
        { name: '--surface-border', value: '' }
    ],
    'Text': [
        { name: '--text-color', value: '' },
        { name: '--text-color-secondary', value: '' }
    ],
    'Status Colors': [
        { name: '--info-color', value: '' },
        { name: '--success-color', value: '' },
        { name: '--warning-color', value: '' },
        { name: '--error-color', value: '' }
    ]
});

// 获取CSS变量样式
const getVariableStyle = (variableName: string) => {
    return {
        backgroundColor: `var(${variableName})`,
        color: variableName.includes('text') ? `var(--surface-card)` : `var(--text-color)`,
        border: '1px solid var(--surface-border)'
    };
};

// 更新CSS变量值
const updateVariableValues = () => {
    Object.keys(themeVariableGroups.value).forEach(groupName => {
        themeVariableGroups.value[groupName].forEach(variable => {
            const value = getComputedStyle(document.documentElement).getPropertyValue(variable.name);
            variable.value = value.trim();
        });
    });
};

// 生命周期
onMounted(() => {
    updateVariableValues();

    // 监听主题变化
    window.addEventListener('theme-changed', updateVariableValues);
});
</script>

<style scoped lang="scss">
.theme-demo-page {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 2rem;

    h1 {
        color: var(--text-color);
        margin-bottom: 0.5rem;
    }

    p {
        color: var(--text-color-secondary);
        font-size: 1.1rem;
    }
}

.demo-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background-color: var(--surface-card);
    border-radius: 0.5rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

    h2 {
        margin-top: 0;
        margin-bottom: 1.5rem;
        color: var(--text-color);
        border-bottom: 1px solid var(--surface-border);
        padding-bottom: 0.5rem;
    }
}

.demo-component {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background-color: var(--surface-section);
    border-radius: 0.5rem;
}

.component-description {
    pre {
        background-color: var(--surface-ground);
        padding: 1rem;
        border-radius: 0.25rem;
        overflow-x: auto;
        color: var(--text-color);
    }
}

.theme-variables {
    .variable-group {
        margin-bottom: 2rem;

        h3 {
            margin-top: 0;
            color: var(--text-color);
            font-size: 1.2rem;
        }
    }

    .variables-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }

    .variable-item {
        display: flex;
        align-items: center;

        .variable-preview {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            margin-right: 0.5rem;
        }

        .variable-info {
            display: flex;
            flex-direction: column;

            code {
                font-size: 0.85rem;
                margin-bottom: 0.25rem;
                color: var(--primary-color);
            }

            span {
                font-size: 0.8rem;
                color: var(--text-color-secondary);
            }
        }
    }
}

.component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;

    .component-item {
        h4 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .component-preview {
            padding: 1rem;
            background-color: var(--surface-section);
            border-radius: 0.25rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
    }
}

.demo-card {
    max-width: 300px;

    .card-header {
        font-weight: 600;
        color: var(--text-color);
    }
}

.code-example {
    pre {
        background-color: var(--surface-ground);
        padding: 1rem;
        border-radius: 0.25rem;
        overflow-x: auto;

        code {
            color: var(--text-color);
            line-height: 1.5;
        }
    }
}

// 响应式调整
@media (max-width: 768px) {
    .theme-demo-page {
        padding: 1rem;
    }

    .component-grid {
        grid-template-columns: 1fr;
    }

    .variables-grid {
        grid-template-columns: 1fr 1fr;
    }
}
</style>