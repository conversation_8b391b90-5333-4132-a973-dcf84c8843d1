package promotion

import (
	"frontapi/internal/admin"
	service "frontapi/internal/service/promotion"

	"github.com/gofiber/fiber/v2"
)

type PromotionRecordController struct {
	admin.BaseController
	service service.PromotionRecordService
}

func NewPromotionRecordController(service service.PromotionRecordService) *PromotionRecordController {
	return &PromotionRecordController{service: service}
}

// ListRecords 获取记录列表
func (h *PromotionRecordController) ListRecords(c *fiber.Ctx) error {

	return h.Success(c, "")
}
