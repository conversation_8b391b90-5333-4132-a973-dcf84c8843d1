<template>
  <el-dialog
    :title="type === 'add' ? '添加分类' : '编辑分类'"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
      status-icon
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入分类名称" maxlength="50" />
      </el-form-item>

      <el-form-item label="分类编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入分类编码">
          <template #append>
            <el-button @click="generateCode">随机生成</el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="父级分类">
        <el-cascader
          v-model="formData.parent_id"
          :options="parentOptions"
          :props="{ 
            checkStrictly: true, 
            value: 'id', 
            label: 'name', 
            emitPath: false
          }"
          clearable
          placeholder="请选择父级分类"
        />
      </el-form-item>

      <el-form-item label="排序" prop="sort_order">
        <el-input-number
          v-model="formData.sort_order"
          :min="0"
          :max="999"
          controls-position="right"
        />
      </el-form-item>

      <el-form-item label="URI标识" prop="uri">
        <el-input v-model="formData.uri" placeholder="请输入URI标识" maxlength="100" />
      </el-form-item>

      <el-form-item label="图标" prop="icon">
        <el-input v-model="formData.icon" placeholder="请输入图标URL" maxlength="255" />
        <el-image
          v-if="formData.icon"
          :src="formData.icon"
          style="width: 30px; height: 30px; margin-top: 8px"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入分类描述"
          maxlength="500"
          :rows="3"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { createCategory, updateCategory } from '@/service/api/shortvideos/shortvideos';
import type { ShortVideoCategory } from '@/types/shortvideos';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { reactive, ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String as () => 'add' | 'edit',
    default: 'add'
  },
  categoryData: {
    type: Object as () => ShortVideoCategory | null,
    default: null
  },
  parentOptions: {
    type: Array as () => { id: string; name: string; children?: any[] }[],
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  code: '',
  parent_id: '',
  sort_order: 0,
  uri: '',
  icon: '',
  status: 1,
  description: ''
});

// 重置表单 - 提前定义避免"Cannot access before initialization"错误
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    name: '',
    code: '',
    parent_id: '',
    sort_order: 0,
    uri: '',
    icon: '',
    status: 1,
    description: ''
  });
  formRef.value?.clearValidate();
};

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  sort_order: [
    { required: true, message: '请输入排序序号', trigger: 'blur' }
  ],
  uri: [
    { pattern: /^[a-zA-Z0-9_\-\/]+$/, message: 'URI只能包含字母、数字、下划线、连字符和斜杠', trigger: 'blur' }
  ]
});

// 监听编辑数据变化
watch(() => props.categoryData, (newVal) => {
  if (newVal) {
    Object.assign(formData, {
      id: newVal.id || '',
      name: newVal.name || '',
      code: newVal.code || '',
      parent_id: newVal.parent_id || '',
      sort_order: newVal.sort_order || 0,
      uri: newVal.uri || '',
      icon: newVal.icon || '',
      status: newVal.status !== undefined ? newVal.status : 1,
      description: newVal.description || ''
    });
  } else {
    resetForm();
  }
}, { deep: true, immediate: true });

// 监听对话框状态
watch(() => props.visible, (val) => {
  if (!val) {
    resetForm();
  }
});

// 生成随机编码
const generateCode = () => {
  formData.code = Math.random().toString(36).substring(2, 10).toUpperCase();
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true;
      try {
        if (props.type === 'add') {
          // 创建分类
          await createCategory({ 
            data: {
              name: formData.name,
              code: formData.code,
              parent_id: formData.parent_id || undefined,
              sort_order: formData.sort_order,
              uri: formData.uri,
              icon: formData.icon,
              status: formData.status,
              description: formData.description
            } 
          });
          ElMessage.success('添加分类成功');
        } else {
          // 更新分类
          await updateCategory({ 
            data: {
              id: formData.id,
              name: formData.name,
              code: formData.code,
              parent_id: formData.parent_id || undefined,
              sort_order: formData.sort_order,
              uri: formData.uri,
              icon: formData.icon,
              status: formData.status,
              description: formData.description
            } 
          });
          ElMessage.success('更新分类成功');
        }
        // 关闭对话框并通知父组件刷新
        emit('update:visible', false);
        emit('success');
      } catch (error: any) {
        ElMessage.error(error.message || '操作失败');
      } finally {
        submitLoading.value = false;
      }
    }
  });
};
</script>

<style scoped lang="scss">
.dialog-footer {
  padding-top: 16px;
  text-align: right;
}
</style> 