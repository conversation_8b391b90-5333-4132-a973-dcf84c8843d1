# 用户管理接口文档

## 明星用户接口 (/celebrity)

### 1. 获取所有明星用户

**接口地址**: `POST /api/celebrity/getAllCelebrity`

**接口描述**: 获取所有明星用户列表

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "status": 1,
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "celebrity_id",
        "username": "明星用户名",
        "nickname": "昵称",
        "avatar": "头像URL",
        "followers_count": 1000,
        "videos_count": 50,
        "posts_count": 20
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取明星用户详情

**接口地址**: `POST /api/celebrity/getCelebrityDetail`

**接口描述**: 获取指定明星用户的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "celebrity_id"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "celebrity_id",
    "username": "明星用户名",
    "nickname": "昵称",
    "avatar": "头像URL",
    "bio": "个人简介",
    "followers_count": 1000,
    "following_count": 100,
    "videos_count": 50,
    "posts_count": 20,
    "likes_count": 5000,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 获取明星用户视频列表

**接口地址**: `POST /api/celebrity/getCelebrityVideoList`

**接口描述**: 获取指定明星用户的视频列表

**请求参数**:
```json
{
  "data": {
    "celebrity_id": "celebrity_id",
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 4. 获取明星用户图片列表

**接口地址**: `POST /api/celebrity/getCelebrityImageList`

**接口描述**: 获取指定明星用户的图片列表

**请求参数**:
```json
{
  "data": {
    "celebrity_id": "celebrity_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 5. 获取明星用户帖子列表

**接口地址**: `POST /api/celebrity/getCelebrityPostList`

**接口描述**: 获取指定明星用户的帖子列表

**请求参数**:
```json
{
  "data": {
    "celebrity_id": "celebrity_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 6. 获取明星用户短视频列表

**接口地址**: `POST /api/celebrity/getCelebrityShortVideoList`

**接口描述**: 获取指定明星用户的短视频列表

**请求参数**:
```json
{
  "data": {
    "celebrity_id": "celebrity_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 7. 获取粉丝统计

**接口地址**: `POST /api/celebrity/getFollowersStats`

**接口描述**: 获取明星用户的粉丝统计信息

**请求参数**:
```json
{
  "data": {
    "celebrity_id": "celebrity_id"
  }
}
```

### 8. 获取明星用户评论

**接口地址**: `POST /api/celebrity/getCelebrityComments`

**接口描述**: 获取明星用户相关的评论列表

**请求参数**:
```json
{
  "data": {
    "celebrity_id": "celebrity_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 创作者接口 (/creator)

### 1. 获取创作者列表

**接口地址**: `POST /api/creator/getCreatorList`

**接口描述**: 获取创作者用户列表

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 2. 获取创作者详情

**接口地址**: `POST /api/creator/getCreatorDetail`

**接口描述**: 获取指定创作者的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "creator_id"
  }
}
```

### 3. 获取创作者视频

**接口地址**: `POST /api/creator/getCreatorVideos`

**接口描述**: 获取创作者的视频列表

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 4. 获取创作者帖子

**接口地址**: `POST /api/creator/getCreatorPosts`

**接口描述**: 获取创作者的帖子列表

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 5. 获取创作者短视频

**接口地址**: `POST /api/creator/getCreatorShortVideos`

**接口描述**: 获取创作者的短视频列表

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 6. 获取创作者专辑

**接口地址**: `POST /api/creator/getCreatorAlbums`

**接口描述**: 获取创作者的专辑列表

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 7. 获取创作者评论

**接口地址**: `POST /api/creator/getCreatorComments`

**接口描述**: 获取创作者相关的评论列表

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 8. 关注创作者

**接口地址**: `POST /api/creator/followCreator`

**接口描述**: 关注指定创作者

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  }
}
```

### 9. 取消关注创作者

**接口地址**: `POST /api/creator/unfollowCreator`

**接口描述**: 取消关注指定创作者

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  }
}
```

### 10. 检查关注状态

**接口地址**: `POST /api/creator/checkFollowStatus`

**接口描述**: 检查当前用户是否关注了指定创作者

**请求参数**:
```json
{
  "data": {
    "creator_id": "creator_id"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "is_following": true
  }
}
```