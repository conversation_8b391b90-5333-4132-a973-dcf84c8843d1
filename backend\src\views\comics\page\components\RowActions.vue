<template>
  <div class="row-actions">
    <el-button 
      type="danger" 
      size="small" 
      @click="$emit('delete')" 
      :disabled="disableDelete"
    >
      删除
    </el-button>
    <el-button
      type="primary"
      size="small"
      @click="$emit('move', 'up')"
      :disabled="disableUp"
    >
      上移
    </el-button>
    <el-button
      type="primary"
      size="small"
      @click="$emit('move', 'down')"
      :disabled="disableDown"
    >
      下移
    </el-button>
    <el-button
      type="success" 
      size="small"
      @click="$emit('add')"
    >
      添加行
    </el-button>
  </div>
</template>

<script setup lang="ts">
defineProps({
  disableDelete: {
    type: Boolean,
    default: false
  },
  disableUp: {
    type: Boolean,
    default: false
  },
  disableDown: {
    type: Boolean,
    default: false
  }
});

defineEmits(['delete', 'move', 'add']);
</script>

<style scoped>
.row-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}
</style> 