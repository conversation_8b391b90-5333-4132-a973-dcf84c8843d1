package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"frontapi/config"

	"github.com/go-redis/redis/v8"
)

var (
	// AdminClient 管理后台专用Redis客户端
	AdminClient *redis.Client
	// AdminCtx 管理后台上下文
	AdminCtx = context.Background()
)

// AdminUserInfo 管理后台用户信息结构
type AdminUserInfo struct {
	UserID      int      `json:"user_id"`
	Username    string   `json:"username"`
	Nickname    string   `json:"nickname"`
	Avatar      string   `json:"avatar"`
	Email       string   `json:"email"`
	Phone       string   `json:"phone"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
	LoginTime   int64    `json:"login_time"`
	LoginIP     string   `json:"login_ip"`
}

// InitAdminRedis 初始化管理后台Redis连接
func InitAdminRedis() error {
	// 构建Redis连接配置
	redisAddr := fmt.Sprintf("%s:%d", config.AppConfig.Redis.Host, config.AppConfig.Redis.Port)

	// 初始化管理后台专用Redis客户端
	AdminClient = redis.NewClient(&redis.Options{
		Addr:         redisAddr,
		Password:     config.AppConfig.Redis.Password,
		DB:           config.AppConfig.Redis.Admin.DB, // 使用管理后台专用DB
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10, // 连接池大小
		MinIdleConns: 5,  // 最小空闲连接数
	})

	// 验证连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := AdminClient.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("管理后台Redis连接失败: %v", err)
	}

	return nil
}

// getAdminKey 生成管理后台Redis键
func getAdminKey(key string) string {
	return config.AppConfig.Redis.Admin.Prefix + key
}

// SetAdminToken 设置管理后台用户Token
func SetAdminToken(userID int, token string, expiration time.Duration) error {
	key := getAdminKey(fmt.Sprintf("token:%d", userID))
	return AdminClient.Set(AdminCtx, key, token, expiration).Err()
}

// GetAdminToken 获取管理后台用户Token
func GetAdminToken(userID int) (string, error) {
	key := getAdminKey(fmt.Sprintf("token:%d", userID))
	return AdminClient.Get(AdminCtx, key).Result()
}

// DeleteAdminToken 删除管理后台用户Token
func DeleteAdminToken(userID int) error {
	key := getAdminKey(fmt.Sprintf("token:%d", userID))
	return AdminClient.Del(AdminCtx, key).Err()
}

// SetAdminUserInfo 设置管理后台用户信息
func SetAdminUserInfo(userID int, userInfo *AdminUserInfo, expiration time.Duration) error {
	key := getAdminKey(fmt.Sprintf("user_info:%d", userID))
	data, err := json.Marshal(userInfo)
	if err != nil {
		return fmt.Errorf("用户信息序列化失败: %w", err)
	}
	return AdminClient.Set(AdminCtx, key, data, expiration).Err()
}

// GetAdminUserInfo 获取管理后台用户信息
func GetAdminUserInfo(userID int) (*AdminUserInfo, error) {
	key := getAdminKey(fmt.Sprintf("user_info:%d", userID))
	data, err := AdminClient.Get(AdminCtx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, fmt.Errorf("用户信息不存在")
		}
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	var userInfo AdminUserInfo
	if err := json.Unmarshal([]byte(data), &userInfo); err != nil {
		return nil, fmt.Errorf("用户信息反序列化失败: %w", err)
	}

	return &userInfo, nil
}

// DeleteAdminUserInfo 删除管理后台用户信息
func DeleteAdminUserInfo(userID int) error {
	key := getAdminKey(fmt.Sprintf("user_info:%d", userID))
	return AdminClient.Del(AdminCtx, key).Err()
}

// SetAdminUserSession 设置管理后台用户会话（包含token和用户信息）
func SetAdminUserSession(userID int, token string, userInfo *AdminUserInfo, expiration time.Duration) error {
	// 设置token
	if err := SetAdminToken(userID, token, expiration); err != nil {
		return fmt.Errorf("设置token失败: %w", err)
	}

	// 设置用户信息
	if err := SetAdminUserInfo(userID, userInfo, expiration); err != nil {
		return fmt.Errorf("设置用户信息失败: %w", err)
	}

	return nil
}

// DeleteAdminUserSession 删除管理后台用户会话
func DeleteAdminUserSession(userID int) error {
	// 删除token
	if err := DeleteAdminToken(userID); err != nil {
		return fmt.Errorf("删除token失败: %w", err)
	}

	// 删除用户信息
	if err := DeleteAdminUserInfo(userID); err != nil {
		return fmt.Errorf("删除用户信息失败: %w", err)
	}

	return nil
}

// ValidateAdminToken 验证管理后台Token
func ValidateAdminToken(userID int, token string) (bool, error) {
	storedToken, err := GetAdminToken(userID)
	if err != nil {
		return false, err
	}
	return storedToken == token, nil
}

// ExtendAdminSession 延长管理后台会话时间
func ExtendAdminSession(userID int, expiration time.Duration) error {
	// 延长token过期时间
	tokenKey := getAdminKey(fmt.Sprintf("token:%d", userID))
	if err := AdminClient.Expire(AdminCtx, tokenKey, expiration).Err(); err != nil {
		return fmt.Errorf("延长token过期时间失败: %w", err)
	}

	// 延长用户信息过期时间
	infoKey := getAdminKey(fmt.Sprintf("user_info:%d", userID))
	if err := AdminClient.Expire(AdminCtx, infoKey, expiration).Err(); err != nil {
		return fmt.Errorf("延长用户信息过期时间失败: %w", err)
	}

	return nil
}

// CloseAdminRedis 关闭管理后台Redis连接
func CloseAdminRedis() error {
	if AdminClient == nil {
		return nil
	}
	return AdminClient.Close()
}

// GetAdminRedisStats 获取管理后台Redis统计信息
func GetAdminRedisStats() *redis.PoolStats {
	if AdminClient == nil {
		return nil
	}
	return AdminClient.PoolStats()
}
