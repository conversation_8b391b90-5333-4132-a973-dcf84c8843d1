package posts

import (
	"frontapi/internal/models"
)

// Like 帖子点赞模型
type PostLike struct {
	models.BaseModel
	PostID    string         `json:"post_id" gorm:"type:string;size:36;not null;comment:帖子ID"`
	UserID    string         `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	Status    int8           `json:"status" gorm:"default:1;comment:状态：0-取消点赞，1-已点赞"`
}

// TableName 指定表名
func (PostLike) TableName() string {
	return "ly_post_likes"
}
