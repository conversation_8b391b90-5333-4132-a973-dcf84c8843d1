<template>
    <div class="comment-table-container">
        <!-- 批量操作工具栏 -->
        <div v-if="selectedRows.length > 0" class="batch-toolbar">
            <div class="batch-info">
                <el-icon>
                    <Check />
                </el-icon>
                <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
            </div>
            <div class="batch-actions">
                <el-button type="success" size="small" @click="handleBatchApprove">
                    批量通过
                </el-button>
                <el-button type="warning" size="small" @click="handleBatchReject">
                    批量拒绝
                </el-button>
                <el-button type="primary" size="small" @click="handleBatchShow">
                    批量显示
                </el-button>
                <el-button type="info" size="small" @click="handleBatchHide">
                    批量隐藏
                </el-button>
                <el-button type="danger" size="small" @click="handleBatchDelete">
                    批量删除
                </el-button>
            </div>
        </div>

        <!-- 主表格 - 使用SlinkyTable -->
        <div class="table-content">
            <SlinkyTable ref="tableRef" :data="commentList" :loading="loading" row-key="id" show-selection
                @selection-change="handleSelectionChange" show-index index-label="序号" :show-actions="false"
                :empty-text="'暂无评论数据'" v-bind="$attrs" :default-sort="{ prop: 'created_at', order: 'descending' }"
                class="comment-data-table">
                <!-- 评论内容列 -->
                <el-table-column prop="content" label="评论内容" align="left" min-width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="comment-content">{{ row.content }}</div>
                    </template>
                </el-table-column>

                <!-- 帖子ID列 -->
                <el-table-column prop="post_id" label="帖子ID" min-width="120" show-overflow-tooltip />

                <!-- 用户信息列 -->
                <el-table-column label="用户信息" width="150">
                    <template #default="{ row }">
                        <div class="user-info">
                            <el-avatar :size="24" :src="row.user_avatar" class="user-avatar">
                                {{ (row.user_nickname || row.user_id || '')[0] }}
                            </el-avatar>
                            <span class="username">{{ row.user_nickname || row.user_id }}</span>
                        </div>
                    </template>
                </el-table-column>

                <!-- 状态列 -->
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                        <!-- 0=待审核, 1=影藏, 2=审核通过, -2=审核拒绝, -4=已删除 -->
                        <el-tag
                            :type="row.status === 0 ? 'info' : row.status === 1 ? 'warning' : row.status === 2 ? 'success' : row.status === -2 ? 'danger' : 'info'">
                            {{ row.status === 0 ? '待审核' : row.status === 1 ? '影藏' : row.status === 2 ? '显示' :
                                row.status === -2 ? '审核拒绝' : '已删除' }}
                        </el-tag>
                    </template>
                </el-table-column>

                <!-- 点赞数列 -->
                <el-table-column prop="like_count" label="点赞数" width="80" align="center" />

                <!-- 热度列 -->
                <el-table-column prop="heat" label="热度" width="80" align="center">
                    <template #default="{ row }">
                        <el-button v-if="row.heat > 0" type="primary" link @click="handleViewReplies(row)">
                            {{ row.heat }}
                        </el-button>
                        <span v-else>{{ row.heat || 0 }}</span>
                    </template>
                </el-table-column>

                <!-- 创建时间列 -->
                <el-table-column prop="created_at" label="创建时间" width="160" align="center" sortable>
                    <template #default="{ row }">
                        {{ formatDateTime(row.created_at) }}
                    </template>
                </el-table-column>

                <!-- 操作列 -->
                <el-table-column label="操作" width="300" fixed="right" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" link size="small" @click="handleViewDetail(row)">
                            <el-icon>
                                <View />
                            </el-icon>
                            查看
                        </el-button>
                        <!-- <el-button type="success" link size="small" @click="handleViewReplies(row)">
                            <el-icon>
                                <ChatLineRound />
                            </el-icon>
                            查看回复
                        </el-button> -->
                        <el-button type="warning" v-if="row.status === 0" link size="small" @click="handleApprove(row)">
                            <el-icon>
                                <EditPen />
                            </el-icon>
                            审核
                        </el-button>
                        <el-button :type="row.status === 1 ? 'danger' : 'success'" link size="small"
                            @click="handleToggleStatus(row)"
                            v-if="row.status !== -2 && row.status !== -4 && row.status !== 0">
                            <el-icon>
                                <Hide v-if="row.status === 1" />
                                <View v-else />
                            </el-icon>
                            {{ row.status === 1 ? '隐藏' : '显示' }}
                        </el-button>
                        <el-button type="danger" link size="small" @click="handleRestore(row)"
                            v-else-if="row.status !== 0">恢复</el-button>
                        <el-popconfirm title="确定要删除该评论吗？此操作不可恢复！" @confirm="handleDelete(row)" v-if="row.status !== -4">
                            <template #reference>
                                <el-button type="danger" link size="small">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                    删除
                                </el-button>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>

                <!-- 自定义空状态插槽，覆盖el-table的默认空状态 -->
                <template #empty>
                    <div style="display: none;"></div>
                </template>
            </SlinkyTable>
        </div>

        <!-- 分页器 -->
        <div class="table-footer">
            <SinglePager :current-page="pagination.page" :page-size="pagination.pageSize" :total="pagination.total"
                @current-change="handleCurrentChange" @size-change="handleSizeChange" show-jump-info />
        </div>
    </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import { PostComment } from '@/types/posts';
import {
    Check,
    Delete,
    EditPen,
    Hide,
    View
} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElMessageBox } from 'element-plus';
import { defineEmits, defineProps, ref } from 'vue';

// Props定义
interface Props {
    loading: boolean;
    commentList: PostComment[];
    pagination: {
        page: number;
        pageSize: number;
        total: number;
    };
}

const props = defineProps<Props>();

// Emits定义
const emit = defineEmits([
    'selection-change',
    'view-detail',
    'view-replies',
    'toggle-status',
    'delete',
    'current-change',
    'size-change',
    'batch-delete',
    'batch-show',
    'batch-hide',
    'approve',
    'reject',
    'batch-approve',
    'batch-reject'
]);

// 表格引用
const tableRef = ref();

// 选中的行
const selectedRows = ref<PostComment[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: PostComment[]) => {
    selectedRows.value = selection;
    emit('selection-change', selection);
};

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 查看详情
const handleViewDetail = (row: PostComment) => {
    emit('view-detail', row);
};

// 查看回复
const handleViewReplies = (row: PostComment) => {
    emit('view-replies', row);
};

// 修改状态
const handleToggleStatus = (row: PostComment) => {
    // 状态取反：0->1, 1->0
    const newStatus = row.status === 1 ? 2 : 1;
    emit('toggle-status', row, newStatus);
};

// 删除评论
const handleDelete = (row: PostComment) => {
    emit('delete', row);
};

// 恢复评论
const handleRestore = (row: PostComment) => {
    emit('toggle-status', row, 0);
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedRows.value.length === 0) return;

    ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 条评论吗？此操作不可恢复！`,
        '批量删除',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        emit('batch-delete', selectedRows.value);
    }).catch(() => { });
};

// 批量显示
const handleBatchShow = () => {
    if (selectedRows.value.length === 0) return;

    ElMessageBox.confirm(
        `确定要显示选中的 ${selectedRows.value.length} 条评论吗？`,
        '批量显示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        }
    ).then(() => {
        emit('batch-show', selectedRows.value, 2);
    }).catch(() => { });
};

// 批量隐藏
const handleBatchHide = () => {
    if (selectedRows.value.length === 0) return;

    ElMessageBox.confirm(
        `确定要隐藏选中的 ${selectedRows.value.length} 条评论吗？`,
        '批量隐藏',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        }
    ).then(() => {
        emit('batch-hide', selectedRows.value, 1);
    }).catch(() => { });
};

// 通过评论
const handleApprove = (row: PostComment) => {
    emit('approve', row);
};

// 拒绝评论
const handleReject = (row: PostComment) => {
    emit('reject', row);
};

// 批量通过
const handleBatchApprove = () => {
    if (selectedRows.value.length === 0) return;

    ElMessageBox.confirm(
        `确定要通过选中的 ${selectedRows.value.length} 条评论吗？`,
        '批量通过',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'success'
        }
    ).then(() => {
        emit('batch-approve', selectedRows.value);
    }).catch(() => { });
};

// 批量拒绝
const handleBatchReject = () => {
    if (selectedRows.value.length === 0) return;

    emit('batch-reject', selectedRows.value);
};

// 分页大小变化
const handleSizeChange = (size: number) => {
    emit('size-change', size);
};

// 页码变化
const handleCurrentChange = (page: number) => {
    emit('current-change', page);
};
</script>

<style scoped lang="scss">
.comment-table-container {
    .batch-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f0f9ff;
        border: 1px solid #e1f5fe;
        border-radius: 4px;
        margin-bottom: 16px;

        .batch-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1976d2;
            font-weight: 500;
        }

        .batch-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
    }

    .table-content {
        background: white;
        border-radius: 4px;
        overflow: hidden;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .user-avatar {
            flex-shrink: 0;
        }

        .username {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .comment-content {
        line-height: 1.5;
        color: #333;
    }

    .table-footer {
        padding: 16px 24px;
        background: var(--el-bg-color-page);
        border-top: 1px solid var(--el-border-color-lighter);
    }
}
</style>
