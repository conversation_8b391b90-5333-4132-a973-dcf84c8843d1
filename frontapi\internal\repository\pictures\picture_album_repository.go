package pictures

import (
	"context"
	"errors"

	"frontapi/internal/models/pictures"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// PictureAlbumRepository 图片专辑数据访问接口
type PictureAlbumRepository interface {
	base.ExtendedRepository[pictures.PictureAlbum]
	
	// 扩展方法
	GetAlbumList(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureAlbum, int64, error)
}

// pictureAlbumRepository 图片专辑数据访问实现
type pictureAlbumRepository struct {
	base.ExtendedRepository[pictures.PictureAlbum]
}

// NewPictureAlbumRepository 创建图片专辑仓库实例
func NewPictureAlbumRepository(db *gorm.DB) PictureAlbumRepository {
	return &pictureAlbumRepository{
		ExtendedRepository: base.NewExtendedRepository[pictures.PictureAlbum](db),
	}
}



// UpdateCount 更新图片专辑计数
func (r *pictureAlbumRepository) UpdateCount(ctx context.Context, id string, field string, count int64) error {
	if field != "picture_count" && field != "view_count" && field != "like_count" {
		return errors.New("不支持的字段")
	}
	return r.GetDBWithContext(ctx).Model(&pictures.PictureAlbum{}).Where("id = ?", id).Update(field, gorm.Expr(field+" + ?", count)).Error
}

// Delete 删除图片专辑
func (r *pictureAlbumRepository) Delete(ctx context.Context, id string) error {
	// 先检查是否有关联的图片
	var count int64
	err := r.GetDBWithContext(ctx).Model(&pictures.Picture{}).Where("album_id = ?", id).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该专辑下有图片，无法删除")
	}

	return r.GetDBWithContext(ctx).Where("id = ?", id).Delete(&pictures.PictureAlbum{}).Error
}

// GetAlbumList 根据条件获取专辑列表
func (r *pictureAlbumRepository) GetAlbumList(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureAlbum, int64, error) {
	var (
		albums []*pictures.PictureAlbum
		total  int64
	)

	query := r.GetDBWithContext(ctx).Model(&pictures.PictureAlbum{})

	if condition != nil {
		if id, ok := condition["id"].(string); ok && id != "" {
			query = query.Where("id = ?", id)
		}
		if title, ok := condition["title"].(string); ok && title != "" {
			query = query.Where("title LIKE ?", "%"+title+"%")
		}

		if categoryID, ok := condition["category_id"].(string); ok && categoryID != "" {
			query = query.Where("category_id = ?", categoryID)
		}

		if creatorID, ok := condition["creator_id"].(string); ok && creatorID != "" {
			query = query.Where("creator_id = ?", creatorID)
		}

		if status, ok := condition["status"].(int); ok && status > -999 {
			query = query.Where("status = ?", status)
		}

		if isFeatured, ok := condition["is_featured"].(bool); ok {
			query = query.Where("is_featured = ?", isFeatured)
		}

	}
	// 应用查询条件

	// 统计总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&albums).Error
	if err != nil {
		return nil, 0, err
	}

	return albums, total, nil
}
