# 扩展点赞服务 API 参考文档

本文档详细介绍扩展点赞服务 (ExtendedLikeService) 的所有API接口、参数说明、返回值和使用示例。

## 目录

- [接口概览](#接口概览)
- [基础操作](#基础操作)
- [查询操作](#查询操作)
- [批量操作](#批量操作)
- [热门排行](#热门排行)
- [历史记录](#历史记录)
- [统计分析](#统计分析)
- [排行榜](#排行榜)
- [缓存管理](#缓存管理)
- [数据同步](#数据同步)
- [策略管理](#策略管理)
- [数据结构](#数据结构)
- [错误码](#错误码)
- [使用示例](#使用示例)

## 接口概览

扩展点赞服务提供以下几类接口：

| 分类 | 接口数量 | 主要功能 |
|------|----------|----------|
| 基础操作 | 4 | 点赞、取消点赞、查询状态、获取数量 |
| 查询操作 | 2 | 获取用户点赞列表、获取物品点赞用户 |
| 批量操作 | 3 | 批量点赞、批量查询状态、批量获取数量 |
| 热门排行 | 2 | 获取热门物品、更新热门排名 |
| 历史记录 | 3 | 用户历史、物品历史、趋势分析 |
| 统计分析 | 3 | 用户统计、物品统计、全局统计 |
| 排行榜 | 2 | 最受欢迎物品、最活跃用户 |
| 缓存管理 | 2 | 清空缓存、获取缓存统计 |
| 数据同步 | 5 | Redis/MongoDB双向同步、业务表同步 |
| 策略管理 | 2 | 获取策略、切换策略 |

## 基础操作

### 1. LikeItem - 点赞物品

为指定物品添加点赞。

**方法签名**:
```go
LikeItem(ctx context.Context, userID, itemType, itemID string) error
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `itemType`: 物品类型 (video, short_video, post, comment)
- `itemID`: 物品ID

**返回值**:
- `error`: 错误信息，成功时为nil

**错误码**:
- `ErrAlreadyLiked`: 用户已经点赞过该物品
- `ErrInvalidParams`: 参数无效
- `ErrStorageFailure`: 存储操作失败

**使用示例**:
```go
err := service.LikeItem(ctx, "user123", "video", "video456")
if err != nil {
    if errors.Is(err, extlike.ErrAlreadyLiked) {
        // 用户已经点赞过
        return fmt.Errorf("用户已点赞")
    }
    return fmt.Errorf("点赞失败: %w", err)
}
```

### 2. UnlikeItem - 取消点赞

取消对指定物品的点赞。

**方法签名**:
```go
UnlikeItem(ctx context.Context, userID, itemType, itemID string) error
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `itemType`: 物品类型
- `itemID`: 物品ID

**返回值**:
- `error`: 错误信息，成功时为nil

**错误码**:
- `ErrNotLiked`: 用户未点赞该物品
- `ErrInvalidParams`: 参数无效
- `ErrStorageFailure`: 存储操作失败

**使用示例**:
```go
err := service.UnlikeItem(ctx, "user123", "video", "video456")
if err != nil {
    if errors.Is(err, extlike.ErrNotLiked) {
        // 用户未点赞过
        return fmt.Errorf("用户未点赞")
    }
    return fmt.Errorf("取消点赞失败: %w", err)
}
```

### 3. IsLiked - 查询点赞状态

查询用户是否已点赞指定物品。

**方法签名**:
```go
IsLiked(ctx context.Context, userID, itemType, itemID string) (bool, error)
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `itemType`: 物品类型
- `itemID`: 物品ID

**返回值**:
- `bool`: 是否已点赞
- `error`: 错误信息

**使用示例**:
```go
liked, err := service.IsLiked(ctx, "user123", "video", "video456")
if err != nil {
    return fmt.Errorf("查询点赞状态失败: %w", err)
}

if liked {
    fmt.Println("用户已点赞")
} else {
    fmt.Println("用户未点赞")
}
```

### 4. GetLikeCount - 获取点赞数量

获取指定物品的总点赞数量。

**方法签名**:
```go
GetLikeCount(ctx context.Context, itemType, itemID string) (int64, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemID`: 物品ID

**返回值**:
- `int64`: 点赞数量
- `error`: 错误信息

**使用示例**:
```go
count, err := service.GetLikeCount(ctx, "video", "video456")
if err != nil {
    return fmt.Errorf("获取点赞数量失败: %w", err)
}

fmt.Printf("视频点赞数: %d\n", count)
```

## 查询操作

### 1. GetUserLikes - 获取用户点赞列表

获取用户点赞的物品列表。

**方法签名**:
```go
GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]string, error)
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `itemType`: 物品类型
- `limit`: 返回数量限制 (1-100)
- `offset`: 偏移量

**返回值**:
- `[]string`: 物品ID列表
- `error`: 错误信息

**使用示例**:
```go
itemIDs, err := service.GetUserLikes(ctx, "user123", "video", 20, 0)
if err != nil {
    return fmt.Errorf("获取用户点赞列表失败: %w", err)
}

fmt.Printf("用户点赞了 %d 个视频\n", len(itemIDs))
for _, itemID := range itemIDs {
    fmt.Printf("视频ID: %s\n", itemID)
}
```

### 2. GetItemLikedUsers - 获取物品点赞用户

获取点赞指定物品的用户列表。

**方法签名**:
```go
GetItemLikedUsers(ctx context.Context, itemType, itemID string, limit, offset int) ([]string, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemID`: 物品ID
- `limit`: 返回数量限制 (1-100)
- `offset`: 偏移量

**返回值**:
- `[]string`: 用户ID列表
- `error`: 错误信息

**使用示例**:
```go
userIDs, err := service.GetItemLikedUsers(ctx, "video", "video456", 50, 0)
if err != nil {
    return fmt.Errorf("获取点赞用户列表失败: %w", err)
}

fmt.Printf("有 %d 个用户点赞了这个视频\n", len(userIDs))
```

## 批量操作

### 1. BatchLike - 批量点赞操作

批量执行点赞或取消点赞操作。

**方法签名**:
```go
BatchLike(ctx context.Context, operations []*LikeOperation) ([]bool, error)
```

**参数**:
- `ctx`: 上下文对象
- `operations`: 操作列表

**返回值**:
- `[]bool`: 每个操作的执行结果
- `error`: 错误信息

**LikeOperation 结构**:
```go
type LikeOperation struct {
    UserID   string `json:"user_id"`
    ItemType string `json:"item_type"`
    ItemID   string `json:"item_id"`
    Action   string `json:"action"` // "like" 或 "unlike"
}
```

**使用示例**:
```go
operations := []*extlike.LikeOperation{
    {
        UserID:   "user123",
        ItemType: "video",
        ItemID:   "video456",
        Action:   "like",
    },
    {
        UserID:   "user123",
        ItemType: "video",
        ItemID:   "video789",
        Action:   "unlike",
    },
}

results, err := service.BatchLike(ctx, operations)
if err != nil {
    return fmt.Errorf("批量操作失败: %w", err)
}

for i, result := range results {
    if result {
        fmt.Printf("操作 %d 成功\n", i)
    } else {
        fmt.Printf("操作 %d 失败\n", i)
    }
}
```

### 2. BatchGetLikeStatus - 批量查询点赞状态

批量查询用户对多个物品的点赞状态。

**方法签名**:
```go
BatchGetLikeStatus(ctx context.Context, userID, itemType string, itemIDs []string) (map[string]bool, error)
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `itemType`: 物品类型
- `itemIDs`: 物品ID列表 (最多100个)

**返回值**:
- `map[string]bool`: 物品ID到点赞状态的映射
- `error`: 错误信息

**使用示例**:
```go
itemIDs := []string{"video456", "video789", "video101"}
statuses, err := service.BatchGetLikeStatus(ctx, "user123", "video", itemIDs)
if err != nil {
    return fmt.Errorf("批量查询状态失败: %w", err)
}

for itemID, liked := range statuses {
    if liked {
        fmt.Printf("视频 %s: 已点赞\n", itemID)
    } else {
        fmt.Printf("视频 %s: 未点赞\n", itemID)
    }
}
```

### 3. BatchGetLikeCounts - 批量获取点赞数量

批量获取多个物品的点赞数量。

**方法签名**:
```go
BatchGetLikeCounts(ctx context.Context, itemType string, itemIDs []string) (map[string]int64, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemIDs`: 物品ID列表 (最多100个)

**返回值**:
- `map[string]int64`: 物品ID到点赞数量的映射
- `error`: 错误信息

**使用示例**:
```go
itemIDs := []string{"video456", "video789", "video101"}
counts, err := service.BatchGetLikeCounts(ctx, "video", itemIDs)
if err != nil {
    return fmt.Errorf("批量获取数量失败: %w", err)
}

for itemID, count := range counts {
    fmt.Printf("视频 %s: %d 个点赞\n", itemID, count)
}
```

## 热门排行

### 1. GetHotItems - 获取热门物品

获取指定类型的热门物品排行榜。

**方法签名**:
```go
GetHotItems(ctx context.Context, itemType string, limit int) ([]*HotItem, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `limit`: 返回数量限制 (1-100)

**返回值**:
- `[]*HotItem`: 热门物品列表
- `error`: 错误信息

**HotItem 结构**:
```go
type HotItem struct {
    ItemID    string  `json:"item_id"`
    LikeCount int64   `json:"like_count"`
    Score     float64 `json:"score"`
    Rank      int     `json:"rank"`
}
```

**使用示例**:
```go
hotItems, err := service.GetHotItems(ctx, "video", 10)
if err != nil {
    return fmt.Errorf("获取热门视频失败: %w", err)
}

fmt.Println("热门视频排行榜:")
for _, item := range hotItems {
    fmt.Printf("第%d名: 视频%s (点赞数: %d, 热度: %.2f)\n", 
        item.Rank, item.ItemID, item.LikeCount, item.Score)
}
```

### 2. UpdateHotRank - 更新热门排名

更新指定物品的热门排名。

**方法签名**:
```go
UpdateHotRank(ctx context.Context, itemType, itemID string, likeCount int64) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemID`: 物品ID
- `likeCount`: 当前点赞数量

**返回值**:
- `error`: 错误信息

**使用示例**:
```go
err := service.UpdateHotRank(ctx, "video", "video456", 1000)
if err != nil {
    return fmt.Errorf("更新热门排名失败: %w", err)
}
```

## 历史记录

### 1. GetUserLikeHistory - 获取用户点赞历史

获取用户的点赞历史记录。

**方法签名**:
```go
GetUserLikeHistory(ctx context.Context, userID, itemType string, limit, offset int) ([]*LikeRecord, error)
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `itemType`: 物品类型
- `limit`: 返回数量限制 (1-100)
- `offset`: 偏移量

**返回值**:
- `[]*LikeRecord`: 点赞记录列表
- `error`: 错误信息

**LikeRecord 结构**:
```go
type LikeRecord struct {
    UserID    string    `json:"user_id"`
    ItemType  string    `json:"item_type"`
    ItemID    string    `json:"item_id"`
    Action    string    `json:"action"` // "like" 或 "unlike"
    Timestamp time.Time `json:"timestamp"`
}
```

**使用示例**:
```go
records, err := service.GetUserLikeHistory(ctx, "user123", "video", 20, 0)
if err != nil {
    return fmt.Errorf("获取用户历史失败: %w", err)
}

fmt.Printf("用户最近的 %d 条点赞记录:\n", len(records))
for _, record := range records {
    fmt.Printf("%s %s 视频%s (时间: %s)\n", 
        record.UserID, record.Action, record.ItemID, record.Timestamp.Format("2006-01-02 15:04:05"))
}
```

### 2. GetItemLikeHistory - 获取物品点赞历史

获取指定物品的点赞历史记录。

**方法签名**:
```go
GetItemLikeHistory(ctx context.Context, itemType, itemID string, limit, offset int) ([]*LikeRecord, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemID`: 物品ID
- `limit`: 返回数量限制 (1-100)
- `offset`: 偏移量

**返回值**:
- `[]*LikeRecord`: 点赞记录列表
- `error`: 错误信息

**使用示例**:
```go
records, err := service.GetItemLikeHistory(ctx, "video", "video456", 50, 0)
if err != nil {
    return fmt.Errorf("获取物品历史失败: %w", err)
}

fmt.Printf("视频最近的 %d 条点赞记录:\n", len(records))
```

### 3. GetLikeTrends - 获取点赞趋势

获取指定物品在时间范围内的点赞趋势。

**方法签名**:
```go
GetLikeTrends(ctx context.Context, itemType, itemID string, timeRange TimeRange) ([]*LikeTrend, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemID`: 物品ID
- `timeRange`: 时间范围

**返回值**:
- `[]*LikeTrend`: 趋势数据列表
- `error`: 错误信息

**TimeRange 结构**:
```go
type TimeRange struct {
    StartTime time.Time `json:"start_time"`
    EndTime   time.Time `json:"end_time"`
    Interval  string    `json:"interval"` // "hour", "day", "week", "month"
}
```

**LikeTrend 结构**:
```go
type LikeTrend struct {
    Timestamp time.Time `json:"timestamp"`
    LikeCount int64     `json:"like_count"`
    Delta     int64     `json:"delta"` // 相比上一时间点的变化
}
```

**使用示例**:
```go
timeRange := &extlike.TimeRange{
    StartTime: time.Now().AddDate(0, 0, -7), // 7天前
    EndTime:   time.Now(),
    Interval:  "day",
}

trends, err := service.GetLikeTrends(ctx, "video", "video456", *timeRange)
if err != nil {
    return fmt.Errorf("获取趋势数据失败: %w", err)
}

fmt.Println("过去7天的点赞趋势:")
for _, trend := range trends {
    fmt.Printf("%s: %d 点赞 (变化: %+d)\n", 
        trend.Timestamp.Format("2006-01-02"), trend.LikeCount, trend.Delta)
}
```

## 统计分析

### 1. GetUserLikeStats - 获取用户点赞统计

获取用户在指定时间范围内的点赞统计信息。

**方法签名**:
```go
GetUserLikeStats(ctx context.Context, userID string, timeRange TimeRange) (*UserLikeStats, error)
```

**参数**:
- `ctx`: 上下文对象
- `userID`: 用户ID
- `timeRange`: 时间范围

**返回值**:
- `*UserLikeStats`: 用户统计信息
- `error`: 错误信息

**UserLikeStats 结构**:
```go
type UserLikeStats struct {
    UserID           string            `json:"user_id"`
    TotalLikes       int64             `json:"total_likes"`
    TotalUnlikes     int64             `json:"total_unlikes"`
    LikesByItemType  map[string]int64  `json:"likes_by_item_type"`
    DailyLikes       []DailyLikeStat   `json:"daily_likes"`
    MostLikedItemType string           `json:"most_liked_item_type"`
    AvgLikesPerDay   float64           `json:"avg_likes_per_day"`
}

type DailyLikeStat struct {
    Date  time.Time `json:"date"`
    Count int64     `json:"count"`
}
```

**使用示例**:
```go
timeRange := extlike.TimeRange{
    StartTime: time.Now().AddDate(0, -1, 0), // 1个月前
    EndTime:   time.Now(),
    Interval:  "day",
}

stats, err := service.GetUserLikeStats(ctx, "user123", timeRange)
if err != nil {
    return fmt.Errorf("获取用户统计失败: %w", err)
}

fmt.Printf("用户统计信息:\n")
fmt.Printf("总点赞数: %d\n", stats.TotalLikes)
fmt.Printf("总取消点赞数: %d\n", stats.TotalUnlikes)
fmt.Printf("最喜欢的内容类型: %s\n", stats.MostLikedItemType)
fmt.Printf("平均每天点赞: %.2f\n", stats.AvgLikesPerDay)
```

### 2. GetItemLikeStats - 获取物品点赞统计

获取指定物品在时间范围内的点赞统计信息。

**方法签名**:
```go
GetItemLikeStats(ctx context.Context, itemType, itemID string, timeRange TimeRange) (*ItemLikeStats, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `itemID`: 物品ID
- `timeRange`: 时间范围

**返回值**:
- `*ItemLikeStats`: 物品统计信息
- `error`: 错误信息

**ItemLikeStats 结构**:
```go
type ItemLikeStats struct {
    ItemType         string          `json:"item_type"`
    ItemID           string          `json:"item_id"`
    TotalLikes       int64           `json:"total_likes"`
    TotalUnlikes     int64           `json:"total_unlikes"`
    NetLikes         int64           `json:"net_likes"`
    DailyLikes       []DailyLikeStat `json:"daily_likes"`
    PeakLikeTime     time.Time       `json:"peak_like_time"`
    AvgLikesPerDay   float64         `json:"avg_likes_per_day"`
    LikeGrowthRate   float64         `json:"like_growth_rate"`
}
```

**使用示例**:
```go
stats, err := service.GetItemLikeStats(ctx, "video", "video456", timeRange)
if err != nil {
    return fmt.Errorf("获取物品统计失败: %w", err)
}

fmt.Printf("视频统计信息:\n")
fmt.Printf("总点赞数: %d\n", stats.TotalLikes)
fmt.Printf("净点赞数: %d\n", stats.NetLikes)
fmt.Printf("点赞增长率: %.2f%%\n", stats.LikeGrowthRate*100)
```

### 3. GetGlobalLikeStats - 获取全局点赞统计

获取指定物品类型的全局统计信息。

**方法签名**:
```go
GetGlobalLikeStats(ctx context.Context, itemType string, timeRange TimeRange) (*GlobalLikeStats, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `timeRange`: 时间范围

**返回值**:
- `*GlobalLikeStats`: 全局统计信息
- `error`: 错误信息

**GlobalLikeStats 结构**:
```go
type GlobalLikeStats struct {
    ItemType         string          `json:"item_type"`
    TotalItems       int64           `json:"total_items"`
    TotalLikes       int64           `json:"total_likes"`
    TotalUsers       int64           `json:"total_users"`
    AvgLikesPerItem  float64         `json:"avg_likes_per_item"`
    AvgLikesPerUser  float64         `json:"avg_likes_per_user"`
    DailyStats       []DailyLikeStat `json:"daily_stats"`
    TopItems         []string        `json:"top_items"`
    ActiveUsers      []string        `json:"active_users"`
}
```

**使用示例**:
```go
stats, err := service.GetGlobalLikeStats(ctx, "video", timeRange)
if err != nil {
    return fmt.Errorf("获取全局统计失败: %w", err)
}

fmt.Printf("全局统计信息:\n")
fmt.Printf("总视频数: %d\n", stats.TotalItems)
fmt.Printf("总点赞数: %d\n", stats.TotalLikes)
fmt.Printf("活跃用户数: %d\n", stats.TotalUsers)
fmt.Printf("平均每个视频点赞数: %.2f\n", stats.AvgLikesPerItem)
```

## 排行榜

### 1. GetTopLikedItems - 获取最受欢迎物品

获取指定时间范围内最受欢迎的物品排行榜。

**方法签名**:
```go
GetTopLikedItems(ctx context.Context, itemType string, timeRange TimeRange, limit int) ([]*TopLikedItem, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `timeRange`: 时间范围
- `limit`: 返回数量限制 (1-100)

**返回值**:
- `[]*TopLikedItem`: 排行榜列表
- `error`: 错误信息

**TopLikedItem 结构**:
```go
type TopLikedItem struct {
    ItemID    string  `json:"item_id"`
    LikeCount int64   `json:"like_count"`
    Rank      int     `json:"rank"`
    GrowthRate float64 `json:"growth_rate"`
}
```

**使用示例**:
```go
topItems, err := service.GetTopLikedItems(ctx, "video", timeRange, 10)
if err != nil {
    return fmt.Errorf("获取排行榜失败: %w", err)
}

fmt.Println("最受欢迎视频排行榜:")
for _, item := range topItems {
    fmt.Printf("第%d名: 视频%s (点赞数: %d, 增长率: %.2f%%)\n", 
        item.Rank, item.ItemID, item.LikeCount, item.GrowthRate*100)
}
```

### 2. GetMostActiveLikers - 获取最活跃点赞用户

获取指定时间范围内最活跃的点赞用户排行榜。

**方法签名**:
```go
GetMostActiveLikers(ctx context.Context, itemType string, timeRange TimeRange, limit int) ([]*ActiveLiker, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型
- `timeRange`: 时间范围
- `limit`: 返回数量限制 (1-100)

**返回值**:
- `[]*ActiveLiker`: 活跃用户列表
- `error`: 错误信息

**ActiveLiker 结构**:
```go
type ActiveLiker struct {
    UserID    string `json:"user_id"`
    LikeCount int64  `json:"like_count"`
    Rank      int    `json:"rank"`
}
```

**使用示例**:
```go
activeLikers, err := service.GetMostActiveLikers(ctx, "video", timeRange, 10)
if err != nil {
    return fmt.Errorf("获取活跃用户失败: %w", err)
}

fmt.Println("最活跃点赞用户:")
for _, liker := range activeLikers {
    fmt.Printf("第%d名: 用户%s (点赞数: %d)\n", 
        liker.Rank, liker.UserID, liker.LikeCount)
}
```

## 缓存管理

### 1. FlushCache - 清空缓存

清空指定物品类型的所有缓存数据。

**方法签名**:
```go
FlushCache(ctx context.Context, itemType string) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型，空字符串表示清空所有类型

**返回值**:
- `error`: 错误信息

**使用示例**:
```go
// 清空视频相关缓存
err := service.FlushCache(ctx, "video")
if err != nil {
    return fmt.Errorf("清空缓存失败: %w", err)
}

// 清空所有缓存
err = service.FlushCache(ctx, "")
if err != nil {
    return fmt.Errorf("清空所有缓存失败: %w", err)
}
```

### 2. GetCacheStats - 获取缓存统计

获取指定物品类型的缓存统计信息。

**方法签名**:
```go
GetCacheStats(ctx context.Context, itemType string) (*CacheStats, error)
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型

**返回值**:
- `*CacheStats`: 缓存统计信息
- `error`: 错误信息

**CacheStats 结构**:
```go
type CacheStats struct {
    ItemType    string  `json:"item_type"`
    HitCount    int64   `json:"hit_count"`
    MissCount   int64   `json:"miss_count"`
    HitRate     float64 `json:"hit_rate"`
    TotalKeys   int64   `json:"total_keys"`
    MemoryUsage int64   `json:"memory_usage"` // 字节
}
```

**使用示例**:
```go
stats, err := service.GetCacheStats(ctx, "video")
if err != nil {
    return fmt.Errorf("获取缓存统计失败: %w", err)
}

fmt.Printf("缓存统计信息:\n")
fmt.Printf("命中次数: %d\n", stats.HitCount)
fmt.Printf("未命中次数: %d\n", stats.MissCount)
fmt.Printf("命中率: %.2f%%\n", stats.HitRate*100)
fmt.Printf("总键数: %d\n", stats.TotalKeys)
fmt.Printf("内存使用: %d 字节\n", stats.MemoryUsage)
```

## 数据同步

### 1. SyncFromRedis - 从Redis同步到MongoDB

将Redis中的数据同步到MongoDB。

**方法签名**:
```go
SyncFromRedis(ctx context.Context, itemType string) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型，空字符串表示同步所有类型

**返回值**:
- `error`: 错误信息

**使用示例**:
```go
err := service.SyncFromRedis(ctx, "video")
if err != nil {
    return fmt.Errorf("从Redis同步失败: %w", err)
}
```

### 2. SyncToRedis - 从MongoDB同步到Redis

将MongoDB中的数据同步到Redis。

**方法签名**:
```go
SyncToRedis(ctx context.Context, itemType string) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型，空字符串表示同步所有类型

**返回值**:
- `error`: 错误信息

**使用示例**:
```go
err := service.SyncToRedis(ctx, "video")
if err != nil {
    return fmt.Errorf("同步到Redis失败: %w", err)
}
```

### 3. SyncToMongoDB - 同步到MongoDB

将当前数据同步到MongoDB。

**方法签名**:
```go
SyncToMongoDB(ctx context.Context, itemType string) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型

**返回值**:
- `error`: 错误信息

### 4. SyncFromMongoDB - 从MongoDB同步

从MongoDB同步数据到当前存储。

**方法签名**:
```go
SyncFromMongoDB(ctx context.Context, itemType string) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型

**返回值**:
- `error`: 错误信息

### 5. SyncLikeCountsToDatabase - 同步点赞数到业务表

将点赞数同步到业务数据库表。

**方法签名**:
```go
SyncLikeCountsToDatabase(ctx context.Context, itemType string) error
```

**参数**:
- `ctx`: 上下文对象
- `itemType`: 物品类型

**返回值**:
- `error`: 错误信息

**使用示例**:
```go
err := service.SyncLikeCountsToDatabase(ctx, "video")
if err != nil {
    return fmt.Errorf("同步到业务表失败: %w", err)
}
```

## 策略管理

### 1. GetStrategy - 获取当前策略

获取当前使用的存储策略。

**方法签名**:
```go
GetStrategy() StorageStrategy
```

**返回值**:
- `StorageStrategy`: 当前存储策略

**StorageStrategy 枚举**:
```go
type StorageStrategy int

const (
    MongoOnly   StorageStrategy = iota // 仅MongoDB
    RedisFirst                         // Redis优先
    MongoFirst                         // MongoDB优先
    DualWrite                          // 双写模式
)
```

**使用示例**:
```go
strategy := service.GetStrategy()
switch strategy {
case extlike.MongoOnly:
    fmt.Println("当前策略: 仅MongoDB")
case extlike.RedisFirst:
    fmt.Println("当前策略: Redis优先")
case extlike.MongoFirst:
    fmt.Println("当前策略: MongoDB优先")
case extlike.DualWrite:
    fmt.Println("当前策略: 双写模式")
}
```

### 2. SwitchStrategy - 切换存储策略

切换到指定的存储策略。

**方法签名**:
```go
SwitchStrategy(strategy StorageStrategy) error
```

**参数**:
- `strategy`: 目标存储策略

**返回值**:
- `error`: 错误信息

**使用示例**:
```go
// 切换到双写模式
err := service.SwitchStrategy(extlike.DualWrite)
if err != nil {
    return fmt.Errorf("切换策略失败: %w", err)
}

fmt.Println("已切换到双写模式")
```

## 数据结构

### 配置结构

```go
// 服务配置
type LikeServiceConfig struct {
    RedisConfig *redis.Config    `json:"redis_config"`
    MongoConfig *mongodb.Config  `json:"mongo_config"`
    Strategy    StorageStrategy  `json:"strategy"`
    SyncConfig  *SyncConfig      `json:"sync_config"`
}

// 同步配置
type SyncConfig struct {
    Enabled      bool          `json:"enabled"`
    Interval     time.Duration `json:"interval"`
    BatchSize    int           `json:"batch_size"`
    RetryTimes   int           `json:"retry_times"`
    RetryDelay   time.Duration `json:"retry_delay"`
}

// Redis配置
type RedisConfig struct {
    Addr         string        `json:"addr"`
    Password     string        `json:"password"`
    DB           int           `json:"db"`
    PoolSize     int           `json:"pool_size"`
    MinIdleConns int           `json:"min_idle_conns"`
    DialTimeout  time.Duration `json:"dial_timeout"`
    ReadTimeout  time.Duration `json:"read_timeout"`
    WriteTimeout time.Duration `json:"write_timeout"`
}

// MongoDB配置
type MongoConfig struct {
    URI        string `json:"uri"`
    Database   string `json:"database"`
    Collection string `json:"collection"`
    MaxPoolSize uint64 `json:"max_pool_size"`
    MinPoolSize uint64 `json:"min_pool_size"`
}
```

### 业务数据结构

```go
// 点赞操作
type LikeOperation struct {
    UserID   string `json:"user_id"`
    ItemType string `json:"item_type"`
    ItemID   string `json:"item_id"`
    Action   string `json:"action"` // "like" 或 "unlike"
}

// 热门物品
type HotItem struct {
    ItemID    string  `json:"item_id"`
    LikeCount int64   `json:"like_count"`
    Score     float64 `json:"score"`
    Rank      int     `json:"rank"`
}

// 点赞记录
type LikeRecord struct {
    UserID    string    `json:"user_id"`
    ItemType  string    `json:"item_type"`
    ItemID    string    `json:"item_id"`
    Action    string    `json:"action"`
    Timestamp time.Time `json:"timestamp"`
}

// 时间范围
type TimeRange struct {
    StartTime time.Time `json:"start_time"`
    EndTime   time.Time `json:"end_time"`
    Interval  string    `json:"interval"` // "hour", "day", "week", "month"
}
```

## 错误码

```go
var (
    // 业务错误
    ErrAlreadyLiked     = errors.New("用户已经点赞过该物品")
    ErrNotLiked         = errors.New("用户未点赞该物品")
    ErrInvalidParams    = errors.New("参数无效")
    ErrItemNotFound     = errors.New("物品不存在")
    ErrUserNotFound     = errors.New("用户不存在")
    
    // 存储错误
    ErrStorageFailure   = errors.New("存储操作失败")
    ErrRedisFailure     = errors.New("Redis操作失败")
    ErrMongoFailure     = errors.New("MongoDB操作失败")
    ErrSyncFailure      = errors.New("数据同步失败")
    
    // 配置错误
    ErrInvalidConfig    = errors.New("配置无效")
    ErrInvalidStrategy  = errors.New("存储策略无效")
    
    // 限制错误
    ErrRateLimited      = errors.New("操作频率过高")
    ErrBatchSizeExceeded = errors.New("批量操作数量超限")
    ErrTimeoutExceeded  = errors.New("操作超时")
)
```

## 使用示例

### 完整示例：视频点赞功能

```go
package main

import (
    "context"
    "fmt"
    "log"
    "time"
    
    "your-project/internal/service/base/extlike"
    "gorm.io/gorm"
)

func main() {
    // 1. 创建服务配置
    config := &extlike.LikeServiceConfig{
        RedisConfig: &extlike.RedisConfig{
            Addr:         "localhost:6379",
            Password:     "",
            DB:           0,
            PoolSize:     20,
            MinIdleConns: 10,
            DialTimeout:  5 * time.Second,
            ReadTimeout:  3 * time.Second,
            WriteTimeout: 3 * time.Second,
        },
        MongoConfig: &extlike.MongoConfig{
            URI:         "mongodb://localhost:27017",
            Database:    "likes",
            Collection:  "like_records",
            MaxPoolSize: 100,
            MinPoolSize: 10,
        },
        Strategy: extlike.DualWrite,
        SyncConfig: &extlike.SyncConfig{
            Enabled:    true,
            Interval:   5 * time.Minute,
            BatchSize:  1000,
            RetryTimes: 3,
            RetryDelay: time.Second,
        },
    }
    
    // 2. 创建数据库连接
    var db *gorm.DB // 初始化你的数据库连接
    
    // 3. 创建扩展点赞服务
    service, err := extlike.NewExtendedLikeService(config, db)
    if err != nil {
        log.Fatalf("创建服务失败: %v", err)
    }
    
    ctx := context.Background()
    
    // 4. 基础操作示例
    fmt.Println("=== 基础操作示例 ===")
    
    // 用户点赞视频
    err = service.LikeItem(ctx, "user123", "video", "video456")
    if err != nil {
        log.Printf("点赞失败: %v", err)
    } else {
        fmt.Println("点赞成功")
    }
    
    // 查询点赞状态
    liked, err := service.IsLiked(ctx, "user123", "video", "video456")
    if err != nil {
        log.Printf("查询状态失败: %v", err)
    } else {
        fmt.Printf("点赞状态: %t\n", liked)
    }
    
    // 获取点赞数量
    count, err := service.GetLikeCount(ctx, "video", "video456")
    if err != nil {
        log.Printf("获取数量失败: %v", err)
    } else {
        fmt.Printf("点赞数量: %d\n", count)
    }
    
    // 5. 批量操作示例
    fmt.Println("\n=== 批量操作示例 ===")
    
    operations := []*extlike.LikeOperation{
        {UserID: "user123", ItemType: "video", ItemID: "video789", Action: "like"},
        {UserID: "user123", ItemType: "video", ItemID: "video101", Action: "like"},
    }
    
    results, err := service.BatchLike(ctx, operations)
    if err != nil {
        log.Printf("批量操作失败: %v", err)
    } else {
        fmt.Printf("批量操作结果: %v\n", results)
    }
    
    // 6. 热门排行示例
    fmt.Println("\n=== 热门排行示例 ===")
    
    hotItems, err := service.GetHotItems(ctx, "video", 5)
    if err != nil {
        log.Printf("获取热门视频失败: %v", err)
    } else {
        fmt.Println("热门视频:")
        for _, item := range hotItems {
            fmt.Printf("第%d名: %s (点赞数: %d)\n", item.Rank, item.ItemID, item.LikeCount)
        }
    }
    
    // 7. 统计分析示例
    fmt.Println("\n=== 统计分析示例 ===")
    
    timeRange := extlike.TimeRange{
        StartTime: time.Now().AddDate(0, 0, -7),
        EndTime:   time.Now(),
        Interval:  "day",
    }
    
    userStats, err := service.GetUserLikeStats(ctx, "user123", timeRange)
    if err != nil {
        log.Printf("获取用户统计失败: %v", err)
    } else {
        fmt.Printf("用户统计: 总点赞数=%d, 平均每天=%.2f\n", 
            userStats.TotalLikes, userStats.AvgLikesPerDay)
    }
    
    // 8. 缓存管理示例
    fmt.Println("\n=== 缓存管理示例 ===")
    
    cacheStats, err := service.GetCacheStats(ctx, "video")
    if err != nil {
        log.Printf("获取缓存统计失败: %v", err)
    } else {
        fmt.Printf("缓存统计: 命中率=%.2f%%, 总键数=%d\n", 
            cacheStats.HitRate*100, cacheStats.TotalKeys)
    }
    
    // 9. 数据同步示例
    fmt.Println("\n=== 数据同步示例 ===")
    
    err = service.SyncLikeCountsToDatabase(ctx, "video")
    if err != nil {
        log.Printf("同步到业务表失败: %v", err)
    } else {
        fmt.Println("同步到业务表成功")
    }
    
    // 10. 策略管理示例
    fmt.Println("\n=== 策略管理示例 ===")
    
    currentStrategy := service.GetStrategy()
    fmt.Printf("当前策略: %v\n", currentStrategy)
    
    // 切换到Redis优先策略
    err = service.SwitchStrategy(extlike.RedisFirst)
    if err != nil {
        log.Printf("切换策略失败: %v", err)
    } else {
        fmt.Println("策略切换成功")
    }
}
```

### HTTP API 集成示例

```go
package handler

import (
    "encoding/json"
    "net/http"
    "strconv"
    
    "github.com/gin-gonic/gin"
    "your-project/internal/service/base/extlike"
)

type LikeHandler struct {
    service extlike.ExtendedLikeService
}

func NewLikeHandler(service extlike.ExtendedLikeService) *LikeHandler {
    return &LikeHandler{service: service}
}

// POST /api/v1/like
func (h *LikeHandler) LikeItem(c *gin.Context) {
    var req struct {
        UserID   string `json:"user_id" binding:"required"`
        ItemType string `json:"item_type" binding:"required"`
        ItemID   string `json:"item_id" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    err := h.service.LikeItem(c.Request.Context(), req.UserID, req.ItemType, req.ItemID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "点赞成功"})
}

// DELETE /api/v1/like
func (h *LikeHandler) UnlikeItem(c *gin.Context) {
    var req struct {
        UserID   string `json:"user_id" binding:"required"`
        ItemType string `json:"item_type" binding:"required"`
        ItemID   string `json:"item_id" binding:"required"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    err := h.service.UnlikeItem(c.Request.Context(), req.UserID, req.ItemType, req.ItemID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "取消点赞成功"})
}

// GET /api/v1/like/status
func (h *LikeHandler) GetLikeStatus(c *gin.Context) {
    userID := c.Query("user_id")
    itemType := c.Query("item_type")
    itemID := c.Query("item_id")
    
    if userID == "" || itemType == "" || itemID == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "缺少必要参数"})
        return
    }
    
    liked, err := h.service.IsLiked(c.Request.Context(), userID, itemType, itemID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"liked": liked})
}

// GET /api/v1/like/count
func (h *LikeHandler) GetLikeCount(c *gin.Context) {
    itemType := c.Query("item_type")
    itemID := c.Query("item_id")
    
    if itemType == "" || itemID == "" {
        c.JSON(http.StatusBadRequest, gin.H{"error": "缺少必要参数"})
        return
    }
    
    count, err := h.service.GetLikeCount(c.Request.Context(), itemType, itemID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"count": count})
}

// GET /api/v1/like/hot
func (h *LikeHandler) GetHotItems(c *gin.Context) {
    itemType := c.Query("item_type")
    limitStr := c.DefaultQuery("limit", "10")
    
    limit, err := strconv.Atoi(limitStr)
    if err != nil || limit <= 0 || limit > 100 {
        c.JSON(http.StatusBadRequest, gin.H{"error": "limit参数无效"})
        return
    }
    
    hotItems, err := h.service.GetHotItems(c.Request.Context(), itemType, limit)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"hot_items": hotItems})
}
```

通过以上API参考文档，开发者可以全面了解扩展点赞服务的所有功能和使用方法，快速集成到自己的项目中。