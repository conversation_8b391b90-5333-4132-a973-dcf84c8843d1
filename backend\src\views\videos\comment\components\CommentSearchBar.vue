<template>
  <div class="comment-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item label="视频ID">
        <el-input 
          v-model="searchForm.videoId" 
          placeholder="请输入视频ID" 
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input 
          v-model="searchForm.userId" 
          placeholder="请输入用户ID" 
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item label="关键词">
        <el-input 
          v-model="searchForm.content" 
          placeholder="请输入关键词搜索" 
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      <el-form-item label="状态">
        <el-select 
          v-model="searchForm.status" 
          placeholder="请选择状态" 
          clearable 
          style="width: 180px;"
        >
          <el-option label="正常" :value="1" />
          <el-option label="隐藏" :value="0" />
          <el-option label="审核不通过" :value="-1" />
          <el-option label="用户删除" :value="-4" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="searchForm.dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 360px;"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><RefreshRight /></el-icon>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive } from 'vue';

// 定义搜索表单接口
export interface SearchForm {
  videoId: string;
  userId: string;
  content: string;
  status?: number;
  dateRange?: [string, string];
  created_at_start?: string;
  created_at_end?: string;
}

// Props
interface Props {
  modelValue: SearchForm;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: SearchForm): void;
  (e: 'search', value: SearchForm): void;
  (e: 'reset'): void;
  (e: 'refresh'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 搜索表单数据
const searchForm = reactive<SearchForm>({
  videoId: props.modelValue.videoId,
  userId: props.modelValue.userId,
  content: props.modelValue.content,
  status: props.modelValue.status,
  dateRange: props.modelValue.dateRange || undefined,
  created_at_start: props.modelValue.created_at_start,
  created_at_end: props.modelValue.created_at_end,
});

// 监听表单变化并同步到父组件
const syncFormData = () => {
  const formData = { ...searchForm };
  
  // 将日期范围拆分为开始和结束时间
  if (searchForm.dateRange && searchForm.dateRange.length === 2) {
    // 使用as类型断言，确保dateRange被识别为数组
    const [start, end] = searchForm.dateRange as [string, string];
    // 将拆分后的时间添加到表单数据中
    (formData as any).created_at_start = start;
    (formData as any).created_at_end = end;
  } else {
    // 如果日期范围为空，也清空已拆分的时间
    (formData as any).created_at_start = undefined;
    (formData as any).created_at_end = undefined;
  }
  
  emit('update:modelValue', formData);
};

// 搜索处理
const handleSearch = () => {
  syncFormData();
  emit('search', { ...searchForm });
};

// 重置处理
const handleReset = () => {
  searchForm.videoId = '';
  searchForm.userId = '';
  searchForm.content = '';
  searchForm.status = undefined;
  searchForm.dateRange = undefined;
  searchForm.created_at_start = undefined;
  searchForm.created_at_end = undefined;
  syncFormData();
  emit('reset');
};

// 刷新处理
const handleRefresh = () => {
  syncFormData();
  emit('refresh');
};
</script>

<style scoped lang="scss">
.comment-search-bar {
  margin-bottom: 16px;

  .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}
</style> 