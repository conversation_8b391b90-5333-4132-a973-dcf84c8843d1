package v2

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"

	"frontapi/internal/service/base/extcollect/types"
)

// QueryOperations 查询操作处理器
type QueryOperations struct {
	client *RedisClient
}

// NewQueryOperations 创建查询操作处理器
func NewQueryOperations(client *RedisClient) *QueryOperations {
	return &QueryOperations{
		client: client,
	}
}

// BatchGetCollectStatus 批量获取收藏状态
func (q *QueryOperations) BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	pipe := q.client.Pipeline()
	commands := make(map[string]*redis.BoolCmd)

	for itemID, itemType := range items {
		collectKey := q.client.collectKey(itemType, itemID)
		commands[itemID] = pipe.SIsMember(ctx, collectKey, userID)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		q.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("批量获取收藏状态失败: %w", err)
	}

	result := make(map[string]bool)
	for itemID, cmd := range commands {
		collected, err := cmd.Result()
		if err != nil && err != redis.Nil {
			result[itemID] = false
		} else {
			result[itemID] = collected
		}
	}

	q.client.UpdateStats(int64(len(items)), 0, 0)
	return result, nil
}

// BatchGetCollectCounts 批量获取收藏数量
func (q *QueryOperations) BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	pipe := q.client.Pipeline()
	commands := make(map[string]*redis.StringCmd)

	for itemID, itemType := range items {
		countKey := q.client.countKey(itemType, itemID)
		commands[itemID] = pipe.Get(ctx, countKey)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		q.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("批量获取收藏数量失败: %w", err)
	}

	result := make(map[string]int64)
	for itemID, cmd := range commands {
		count, err := cmd.Int64()
		if err != nil && err != redis.Nil {
			result[itemID] = 0
		} else {
			result[itemID] = count
		}
	}

	q.client.UpdateStats(int64(len(items)), 0, 0)
	return result, nil
}

// GetUserCollects 获取用户收藏列表
func (q *QueryOperations) GetUserCollects(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	userCollectsKey := q.client.userCollectsKey(userID, itemType)

	members, err := q.client.Client().SMembers(ctx, userCollectsKey).Result()
	if err != nil {
		if err == redis.Nil {
			q.client.UpdateStats(0, 1, 0)
			return []*types.CollectRecord{}, nil
		}
		q.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取用户收藏列表失败: %w", err)
	}

	// 实现分页
	start := offset
	end := offset + limit
	if start >= len(members) {
		q.client.UpdateStats(1, 0, 0)
		return []*types.CollectRecord{}, nil
	}
	if end > len(members) {
		end = len(members)
	}

	records := make([]*types.CollectRecord, 0, end-start)
	for i := start; i < end; i++ {
		records = append(records, &types.CollectRecord{
			UserID:   userID,
			ItemID:   members[i],
			ItemType: itemType,
			Status:   "collected",
		})
	}

	q.client.UpdateStats(1, 0, 0)
	return records, nil
}

// GetItemCollectors 获取项目收藏者列表
func (q *QueryOperations) GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	itemCollectorsKey := q.client.itemCollectorsKey(itemType, itemID)

	members, err := q.client.Client().SMembers(ctx, itemCollectorsKey).Result()
	if err != nil {
		if err == redis.Nil {
			q.client.UpdateStats(0, 1, 0)
			return []*types.CollectRecord{}, nil
		}
		q.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取项目收藏者列表失败: %w", err)
	}

	// 实现分页
	start := offset
	end := offset + limit
	if start >= len(members) {
		q.client.UpdateStats(1, 0, 0)
		return []*types.CollectRecord{}, nil
	}
	if end > len(members) {
		end = len(members)
	}

	records := make([]*types.CollectRecord, 0, end-start)
	for i := start; i < end; i++ {
		records = append(records, &types.CollectRecord{
			UserID:   members[i],
			ItemID:   itemID,
			ItemType: itemType,
			Status:   "collected",
		})
	}

	q.client.UpdateStats(1, 0, 0)
	return records, nil
}

// GetCollectHistory 获取收藏历史
func (q *QueryOperations) GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error) {
	historyKey := q.client.historyKey(userID, itemType)

	// 简化实现，获取最近的收藏历史
	members, err := q.client.Client().ZRevRange(ctx, historyKey, 0, 100).Result()
	if err != nil {
		if err == redis.Nil {
			q.client.UpdateStats(0, 1, 0)
			return []*types.CollectRecord{}, nil
		}
		q.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取收藏历史失败: %w", err)
	}

	records := make([]*types.CollectRecord, 0, len(members))
	for _, member := range members {
		records = append(records, &types.CollectRecord{
			UserID:   userID,
			ItemID:   member,
			ItemType: itemType,
			Status:   "collected",
		})
	}

	q.client.UpdateStats(1, 0, 0)
	return records, nil
}
