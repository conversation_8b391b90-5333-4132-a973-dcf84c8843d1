# 服务基础架构重构实施指南

## 概述

本指南详细说明如何将现有的服务基础架构从重复代码模式迁移到新的统一架构。新架构通过泛型和接口设计，大幅减少代码重复，提高维护性和扩展性。

## 新架构特点

### 1. 核心组件

- **抽象基础服务** (`AbstractBaseService`): 提供通用的CRUD操作、缓存管理、钩子系统
- **具体基础服务** (`IntBaseService`, `StringBaseService`): 针对不同ID类型的具体实现
- **扩展服务** (`IntExtendedService`, `StringExtendedService`): 在基础服务上增加软删除、计数更新等高级功能
- **钩子系统**: 支持在操作前后执行自定义逻辑
- **仓库适配器**: 将现有仓库适配为新接口

### 2. 架构优势

- **减少重复代码**: 预计减少60-70%的重复代码
- **类型安全**: 通过泛型确保编译时类型检查
- **统一接口**: 所有服务遵循相同的接口规范
- **易于扩展**: 通过钩子系统和接口组合轻松添加新功能
- **向后兼容**: 通过适配器模式保持现有代码可用

## 实施步骤

### 阶段一：基础架构部署（第1周）

#### 1.1 部署新的基础服务文件

已创建的文件：
```
internal/service/base/common/
├── abstract_service.go          # 抽象基础服务和接口定义
├── hook_manager.go             # 钩子管理器实现
├── int_service.go              # int类型基础服务
├── string_service.go           # string类型基础服务
├── extended_service.go         # 扩展服务抽象实现
├── int_extended_service.go     # int类型扩展服务
└── string_extended_service.go  # string类型扩展服务
```

#### 1.2 验证新架构编译

```bash
# 编译检查新的common包
go build ./internal/service/base/common/
```

#### 1.3 创建模型约束接口

确保所有模型实现 `ModelConstraint` 接口：

```go
// 在 models 包中添加
type IDGetter[T comparable] interface {
    GetID() T
    SetID(T)
}

// 确保现有模型实现此接口
func (p *Permission) GetID() int { return p.ID }
func (p *Permission) SetID(id int) { p.ID = id }
```

### 阶段二：渐进式迁移（第2-3周）

#### 2.1 创建新版本服务

为每个现有服务创建V2版本，如 `permission_service_v2.go`：

```go
// 示例：角色服务V2
type RoleServiceV2 struct {
    *common.IntExtendedService[models.Role]
}

func NewRoleServiceV2(repo role.IRoleRepository) *RoleServiceV2 {
    adaptedRepo := &RoleRepositoryAdapter{repo: repo}
    service := &RoleServiceV2{
        IntExtendedService: common.NewIntExtendedService[models.Role](adaptedRepo, "role"),
    }
    
    // 注册业务钩子
    service.RegisterHook(common.BeforeCreate, common.NewValidationHook(validateRole))
    service.RegisterHook(common.AfterCreate, common.NewFunctionHook(logRoleCreation))
    
    return service
}
```

#### 2.2 创建仓库适配器

为每个仓库创建适配器，将现有接口适配为新的 `ExtendedRepository` 接口：

```go
type RoleRepositoryAdapter struct {
    repo role.IRoleRepository
}

// 实现所有 ExtendedRepository 方法
// 对于不支持的方法，可以返回 "not implemented" 错误
```

#### 2.3 并行运行测试

在迁移过程中，保持原有服务和新服务并行运行，通过测试验证功能一致性。

### 阶段三：业务逻辑迁移（第3-4周）

#### 3.1 迁移控制器层

逐步将控制器从使用旧服务改为使用新服务：

```go
// 旧代码
type PermissionController struct {
    permissionService *permission.PermissionService
}

// 新代码
type PermissionController struct {
    permissionService *permission.PermissionServiceV2
}
```

#### 3.2 添加业务钩子

利用新架构的钩子系统添加业务逻辑：

```go
// 权限创建前验证
validationHook := common.NewValidationHook(func(p *models.Permission) error {
    if p.Name == "" {
        return errors.New("权限名称不能为空")
    }
    return nil
})

// 权限创建后日志记录
loggingHook := common.NewFunctionHook(func(ctx context.Context, p *models.Permission) error {
    log.Printf("权限创建成功: ID=%d, Name=%s", p.ID, p.Name)
    return nil
})

service.RegisterHook(common.BeforeCreate, validationHook)
service.RegisterHook(common.AfterCreate, loggingHook)
```

### 阶段四：清理和优化（第4周）

#### 4.1 移除旧代码

在确认新架构稳定运行后，逐步移除旧的服务文件：

```bash
# 备份旧文件
mkdir -p backup/service/base
mv internal/service/base/extint backup/service/base/
mv internal/service/base/base_service.go backup/service/base/
mv internal/service/base/extended_service.go backup/service/base/
```

#### 4.2 更新导入路径

将所有导入路径从旧的基础服务改为新的common包：

```go
// 旧导入
import "frontapi/internal/service/base/extint"

// 新导入
import "frontapi/internal/service/base/common"
```

#### 4.3 性能优化

- 调整缓存TTL设置
- 优化数据库查询
- 添加性能监控钩子

## 迁移检查清单

### 准备阶段
- [ ] 备份现有代码
- [ ] 创建新的common包
- [ ] 验证新架构编译通过
- [ ] 确保所有模型实现ModelConstraint接口

### 迁移阶段
- [ ] 为每个服务创建V2版本
- [ ] 创建仓库适配器
- [ ] 编写单元测试
- [ ] 并行运行新旧服务
- [ ] 验证功能一致性

### 部署阶段
- [ ] 更新控制器使用新服务
- [ ] 添加业务钩子
- [ ] 监控性能指标
- [ ] 收集用户反馈

### 清理阶段
- [ ] 移除旧代码
- [ ] 更新文档
- [ ] 团队培训
- [ ] 性能优化

## 风险控制

### 高风险项
1. **数据一致性**: 确保新旧服务操作相同数据时的一致性
2. **性能回归**: 监控新架构的性能表现
3. **业务逻辑遗漏**: 确保所有业务逻辑都正确迁移

### 缓解措施
1. **渐进式迁移**: 逐个服务迁移，降低风险
2. **并行运行**: 新旧服务并行运行一段时间
3. **全面测试**: 单元测试、集成测试、性能测试
4. **监控告警**: 添加详细的监控和告警
5. **回滚计划**: 准备快速回滚方案

## 最佳实践

### 1. 钩子使用
```go
// 验证钩子
service.RegisterHook(common.BeforeCreate, common.NewValidationHook(validateEntity))

// 日志钩子
service.RegisterHook(common.AfterCreate, common.NewLoggingHook(logCreation))

// 缓存清理钩子
service.RegisterHook(common.AfterUpdate, common.NewFunctionHook(clearRelatedCache))
```

### 2. 错误处理
```go
// 统一错误处理
func (s *ServiceV2) CreateWithValidation(ctx context.Context, entity *Model) (int, error) {
    if err := s.validateEntity(entity); err != nil {
        return 0, fmt.Errorf("验证失败: %w", err)
    }
    
    return s.Create(ctx, entity)
}
```

### 3. 缓存策略
```go
// 设置合适的缓存TTL
service.SetCacheTTL(time.Hour)

// 在业务方法中合理使用缓存
entity, err := service.GetByID(ctx, id, true) // 使用缓存
list, total, err := service.List(ctx, condition, orderBy, page, pageSize, false) // 不使用缓存
```

## 性能监控

### 关键指标
1. **响应时间**: 各API接口的响应时间
2. **吞吐量**: 每秒处理的请求数
3. **错误率**: 错误请求的比例
4. **缓存命中率**: 缓存的有效性
5. **数据库连接数**: 数据库资源使用情况

### 监控实现
```go
// 性能监控钩子
performanceHook := common.NewFunctionHook(func(ctx context.Context, entity *T) error {
    start := time.Now()
    defer func() {
        duration := time.Since(start)
        metrics.RecordDuration("service.operation", duration)
    }()
    return nil
})
```

## 总结

新的服务基础架构通过以下方式显著改善了代码质量：

1. **减少重复**: 通过抽象和泛型消除了大量重复代码
2. **提高维护性**: 统一的接口和实现模式
3. **增强扩展性**: 钩子系统和接口组合
4. **保证类型安全**: 泛型确保编译时类型检查
5. **向后兼容**: 适配器模式保护现有投资

按照本指南逐步实施，可以安全、高效地完成架构迁移，为后续开发奠定坚实基础。