<template>
  <div class="comment-list-container">
    <!-- 评论头部 -->
    <div class="comment-header">
      <h3 class="comment-title">
        <el-icon><ChatDotRound /></el-icon>
        评论 {{ total }}
      </h3>
      <button class="close-btn" @click="$emit('close')" v-if="showCloseButton">
        <el-icon><Close /></el-icon>
      </button>
    </div>

    <!-- 评论输入框 -->
    <div class="comment-input-section" v-if="isUserLoggedIn">
      <div class="comment-input-wrapper">
        <div class="user-avatar">
          <img :src="userAvatar || '/default-avatar.png'" alt="用户头像" />
        </div>
        <div class="input-container">
          <el-input
            v-model="newComment"
            type="textarea"
            :rows="3"
            placeholder="说点什么吧..."
            :maxlength="500"
            show-word-limit
            resize="none"
            @keydown.ctrl.enter="handleSubmitComment"
          />
          <div class="input-actions">
            <div class="emoji-actions">
              <!-- 这里可以添加表情选择器 -->
            </div>
            <el-button 
              type="primary" 
              :disabled="!newComment.trim() || submitting"
              :loading="submitting"
              @click="handleSubmitComment"
            >
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 未登录提示 -->
    <div class="login-prompt" v-else>
      <p>请先登录后再评论</p>
      <el-button type="primary" @click="$emit('login')">立即登录</el-button>
    </div>

    <!-- 评论列表 -->
    <div class="comment-list" v-loading="loading">
      <div class="comment-item" v-for="comment in comments" :key="comment.id">
        <div class="comment-avatar">
          <img :src="comment.user_avatar || '/default-avatar.png'" :alt="comment.username" />
        </div>
        <div class="comment-content">
          <div class="comment-header-info">
            <span class="username">{{ comment.username }}</span>
            <span class="comment-time">{{ formatTime(comment.created_at) }}</span>
          </div>
          <div class="comment-text">{{ comment.content }}</div>
          <div class="comment-actions">
            <button 
              class="action-btn like-btn"
              :class="{ active: comment.is_liked }"
              @click="handleLikeComment(comment)"
            >
              <el-icon><Star /></el-icon>
              <span v-if="comment.like_count > 0">{{ comment.like_count }}</span>
            </button>
            <button class="action-btn reply-btn" @click="handleReplyComment(comment)">
              <el-icon><ChatDotRound /></el-icon>
              回复
            </button>
          </div>

          <!-- 回复列表 -->
          <div class="reply-list" v-if="comment.replies && comment.replies.length > 0">
            <div class="reply-item" v-for="reply in comment.replies" :key="reply.id">
              <div class="reply-avatar">
                <img :src="reply.user_avatar || '/default-avatar.png'" :alt="reply.username" />
              </div>
              <div class="reply-content">
                <div class="reply-header">
                  <span class="username">{{ reply.username }}</span>
                  <span class="reply-time">{{ formatTime(reply.created_at) }}</span>
                </div>
                <div class="reply-text">{{ reply.content }}</div>
                <div class="reply-actions">
                  <button 
                    class="action-btn like-btn"
                    :class="{ active: reply.is_liked }"
                    @click="handleLikeComment(reply)"
                  >
                    <el-icon><Star /></el-icon>
                    <span v-if="reply.like_count > 0">{{ reply.like_count }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 回复输入框 -->
          <div class="reply-input" v-if="replyingTo === comment.id && isUserLoggedIn">
            <div class="reply-input-wrapper">
              <el-input
                v-model="replyContent"
                type="textarea"
                :rows="2"
                :placeholder="`回复 @${comment.username}:`"
                :maxlength="500"
                show-word-limit
                resize="none"
                @keydown.ctrl.enter="handleSubmitReply(comment)"
              />
              <div class="reply-actions">
                <el-button size="small" @click="cancelReply">取消</el-button>
                <el-button 
                  type="primary" 
                  size="small"
                  :disabled="!replyContent.trim() || submitting"
                  :loading="submitting"
                  @click="handleSubmitReply(comment)"
                >
                  回复
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <el-button 
          :loading="loadingMore" 
          @click="loadMoreComments"
          text
        >
          加载更多评论
        </el-button>
      </div>

      <!-- 空状态 -->
      <div class="empty-comments" v-if="!loading && comments.length === 0">
        <el-icon class="empty-icon"><ChatDotRound /></el-icon>
        <p>还没有评论，快来抢沙发吧~</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ChatDotRound, Close, Star } from '@element-plus/icons-vue'
import { getShortVideoComments, addShortVideoComment, likeShortVideoComment, cancelLikeShortVideoComment } from '@/api/shorts'
import type { ShortVideoComment } from '@/api/shorts'

// Props
interface Props {
  videoId: string
  isUserLoggedIn?: boolean
  userAvatar?: string
  userId?: string
  showCloseButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isUserLoggedIn: false,
  showCloseButton: false
})

// Emits
const emit = defineEmits<{
  close: []
  login: []
  'comment-added': [comment: ShortVideoComment]
}>()

// State
const comments = ref<ShortVideoComment[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const submitting = ref(false)
const hasMore = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 评论输入
const newComment = ref('')
const replyContent = ref('')
const replyingTo = ref<string | null>(null)

// Methods
const loadComments = async (reset = false) => {
  if (reset) {
    currentPage.value = 1
    comments.value = []
    hasMore.value = true
  }

  loading.value = reset
  loadingMore.value = !reset

  try {
    const response = await getShortVideoComments({
      data: {
        short_id: props.videoId
      },
      page: {
        pageNo: currentPage.value,
        pageSize: pageSize.value
      }
    })

    if (reset) {
      comments.value = response.list || []
    } else {
      comments.value.push(...(response.list || []))
    }

    total.value = response.total || 0
    hasMore.value = comments.value.length < total.value
  } catch (error) {
    console.error('加载评论失败:', error)
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const loadMoreComments = async () => {
  if (!hasMore.value || loadingMore.value) return
  
  currentPage.value++
  await loadComments(false)
}

const handleSubmitComment = async () => {
  if (!newComment.value.trim() || submitting.value) return

  submitting.value = true
  try {
    await addShortVideoComment(props.videoId, props.userId || '', newComment.value.trim())
    
    ElMessage.success('评论成功')
    newComment.value = ''
    
    // 重新加载评论列表
    await loadComments(true)
    
    emit('comment-added', {} as ShortVideoComment)
  } catch (error) {
    console.error('评论失败:', error)
    ElMessage.error('评论失败')
  } finally {
    submitting.value = false
  }
}

const handleReplyComment = (comment: ShortVideoComment) => {
  replyingTo.value = comment.id
  replyContent.value = ''
}

const handleSubmitReply = async (comment: ShortVideoComment) => {
  if (!replyContent.value.trim() || submitting.value) return

  submitting.value = true
  try {
    await addShortVideoComment(props.videoId, props.userId || '', replyContent.value.trim(), comment.id)
    
    ElMessage.success('回复成功')
    cancelReply()
    
    // 重新加载评论列表
    await loadComments(true)
  } catch (error) {
    console.error('回复失败:', error)
    ElMessage.error('回复失败')
  } finally {
    submitting.value = false
  }
}

const cancelReply = () => {
  replyingTo.value = null
  replyContent.value = ''
}

const handleLikeComment = async (comment: ShortVideoComment) => {
  if (!props.isUserLoggedIn) {
    emit('login')
    return
  }

  try {
    if (comment.is_liked) {
      await cancelLikeShortVideoComment(props.userId || '', comment.id)
      comment.like_count = Math.max(0, comment.like_count - 1)
      comment.is_liked = false
    } else {
      await likeShortVideoComment(props.userId || '', comment.id)
      comment.like_count += 1
      comment.is_liked = true
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const formatTime = (timeStr: string): string => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  
  return time.toLocaleDateString()
}

// Watchers
watch(() => props.videoId, () => {
  if (props.videoId) {
    loadComments(true)
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  if (props.videoId) {
    loadComments(true)
  }
})
</script>

<style scoped lang="scss">
.comment-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;

  .comment-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: #666;
    transition: all 0.2s;

    &:hover {
      background: #f0f0f0;
      color: #333;
    }
  }
}

.comment-input-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  .comment-input-wrapper {
    display: flex;
    gap: 12px;

    .user-avatar {
      flex-shrink: 0;

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .input-container {
      flex: 1;

      .input-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;

        .emoji-actions {
          // 预留表情选择器位置
        }
      }
    }
  }
}

.login-prompt {
  padding: 40px 20px;
  text-align: center;
  color: #666;

  p {
    margin-bottom: 16px;
  }
}

.comment-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;

  .comment-item {
    display: flex;
    gap: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .comment-avatar {
      flex-shrink: 0;

      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .comment-content {
      flex: 1;

      .comment-header-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;

        .username {
          font-weight: 600;
          color: #333;
        }

        .comment-time {
          font-size: 12px;
          color: #999;
        }
      }

      .comment-text {
        color: #333;
        line-height: 1.5;
        margin-bottom: 8px;
        word-break: break-word;
      }

      .comment-actions {
        display: flex;
        gap: 16px;

        .action-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          background: none;
          border: none;
          cursor: pointer;
          color: #666;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s;

          &:hover {
            background: #f5f5f5;
            color: #333;
          }

          &.active {
            color: #ff6b6b;
          }
        }
      }
    }
  }

  .reply-list {
    margin-top: 12px;
    padding-left: 20px;
    border-left: 2px solid #f0f0f0;

    .reply-item {
      display: flex;
      gap: 8px;
      padding: 8px 0;

      .reply-avatar {
        flex-shrink: 0;

        img {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .reply-content {
        flex: 1;

        .reply-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 4px;

          .username {
            font-weight: 600;
            color: #333;
            font-size: 14px;
          }

          .reply-time {
            font-size: 11px;
            color: #999;
          }
        }

        .reply-text {
          color: #333;
          line-height: 1.4;
          margin-bottom: 4px;
          font-size: 14px;
          word-break: break-word;
        }

        .reply-actions {
          .action-btn {
            font-size: 11px;
            padding: 2px 6px;
          }
        }
      }
    }
  }

  .reply-input {
    margin-top: 12px;
    padding-left: 20px;

    .reply-input-wrapper {
      .reply-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        margin-top: 8px;
      }
    }
  }

  .load-more {
    text-align: center;
    padding: 20px 0;
  }

  .empty-comments {
    text-align: center;
    padding: 60px 20px;
    color: #999;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    p {
      margin: 0;
    }
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .comment-header {
    padding: 12px 16px;

    .comment-title {
      font-size: 14px;
    }
  }

  .comment-input-section {
    padding: 12px 16px;

    .comment-input-wrapper {
      gap: 8px;

      .user-avatar img {
        width: 32px;
        height: 32px;
      }
    }
  }

  .comment-list {
    padding: 0 16px;

    .comment-item {
      gap: 8px;
      padding: 12px 0;

      .comment-avatar img {
        width: 32px;
        height: 32px;
      }

      .comment-content {
        .comment-text {
          font-size: 14px;
        }

        .comment-actions {
          gap: 12px;

          .action-btn {
            font-size: 11px;
            padding: 2px 6px;
          }
        }
      }
    }

    .reply-list {
      padding-left: 16px;

      .reply-item {
        gap: 6px;

        .reply-avatar img {
          width: 28px;
          height: 28px;
        }

        .reply-content {
          .reply-text {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style> 