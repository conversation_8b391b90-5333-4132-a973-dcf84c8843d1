package cache

import "time"

// LocalCacheConfig 本地缓存配置
type LocalCacheConfig struct {
	Size               int           `mapstructure:"size"`             // 缓存大小（MB）
	TTL                time.Duration `mapstructure:"ttl"`              // 默认过期时间
	CleanupInterval    time.Duration `mapstructure:"cleanup_interval"` // 清理间隔
	MaxEntriesInWindow int           `mapstructure:"max_entries_in_window"`
	ShardCount         int           `mapstructure:"shard_count"`
}
