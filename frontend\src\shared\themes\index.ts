/**
 * 主题数据文件
 */
import { ThemeConfig } from '@/config/theme.config';
import { auraThemes } from './aura';
import { laraThemes } from './lara';
import { materialThemes } from './material';
import presets from './presets';

// 合并所有主题
export const availableThemes: ThemeConfig[] = [
  ...auraThemes,
  ...laraThemes,
  ...materialThemes
];

// 按主题家族分组
export const themeGroups = {
  aura: auraThemes,
  lara: laraThemes,
  md: materialThemes
};

// 按主题色系分组
export const themeColorGroups = {
  aura: groupByColorScheme(auraThemes),
  lara: groupByColorScheme(laraThemes),
  md: groupByColorScheme(materialThemes)
};

// 获取主题配置（通过代码）
export function getThemeByCode(code: string, isDark: boolean = false): ThemeConfig | undefined {
  return availableThemes.find(t => t.code === code && t.isDark === isDark);
}

// 获取主题配置（通过名称）
export function getThemeByName(name: string): ThemeConfig | undefined {
  return availableThemes.find(t => t.name === name);
}

// 获取相反模式的主题
export function getOppositeTheme(theme: ThemeConfig): ThemeConfig | undefined {
  return availableThemes.find(t => 
    t.code === theme.code &&
    t.isDark !== theme.isDark
  );
}

// 按颜色方案分组
function groupByColorScheme(themes: ThemeConfig[]): Record<string, ThemeConfig[]> {
  const result: Record<string, ThemeConfig[]> = {};
  
  // 获取所有唯一的颜色方案
  const colorSchemes = [...new Set(themes.map(t => t.colorScheme))];
  
  // 按颜色方案分组
  colorSchemes.forEach(scheme => {
    // 找出该颜色方案的亮色和暗色主题
    const themesForScheme = themes.filter(t => t.colorScheme === scheme);
    
    // 按照代码分组，确保每个代码只有一个亮色和一个暗色主题
    const uniqueThemes = themesForScheme.filter((theme, index, self) => 
      index === self.findIndex(t => t.code === theme.code && t.isDark === theme.isDark)
    );
    
    result[scheme] = uniqueThemes;
  });
  
  return result;
}

// 获取主题的唯一颜色方案列表
export function getUniqueColorSchemes(themeFamily: string): string[] {
  const familyThemes = themeGroups[themeFamily as keyof typeof themeGroups] || [];
  // 获取唯一的颜色方案
  const uniqueSchemes = [...new Set(familyThemes.map(t => t.colorScheme))];
  return uniqueSchemes;
}

// 获取指定主题家族和颜色方案的主题
export function getThemeByFamilyAndScheme(family: string, scheme: string, isDark: boolean): ThemeConfig | undefined {
  return availableThemes.find(t => 
    t.themeFamily === family && 
    t.colorScheme === scheme && 
    t.isDark === isDark
  );
}

// 导出主题预设
export { presets };
