/**
 * 主题配置索引
 * 导出所有可用主题
 */
import { ThemeConfig } from './theme-manager';

// 导入所有主题的light和dark版本
import { charmLightTheme } from './charm';
import { darkLightTheme } from './dark';
import { freshLightTheme } from './fresh';
import { modernLightTheme } from './modern';
import { mysteriousLightTheme } from './mysterious';
import { warmLightTheme } from './warm';

// 旧版主题管理器 - 兼容旧代码
export const themeManager = {
    // 旧方法的空实现
    initTheme: () => { },
    setTheme: (theme: string) => { }
};

// 导出所有主题列表 - 包含light和dark版本
export const themes: ThemeConfig[] = [
    // Modern主题
    modernLightTheme,
    //  modernDarkTheme,
    // Warm主题
    warmLightTheme,
    //  warmDarkTheme,
    // Dark主题
    darkLightTheme,
    //  darkDarkTheme,
    // Fresh主题
    freshLightTheme,
    //  freshDarkTheme,
    // Charm主题
    charmLightTheme,
    //  charmDarkTheme,
    // Mysterious主题 (默认)
    mysteriousLightTheme,
    //  mysteriousDarkTheme
];

// 主题映射表 - 便于查找
export const themeMap: Record<string, ThemeConfig> = {
    'modern-light': modernLightTheme,
    // 'modern-dark': modernDarkTheme,
    'warm-light': warmLightTheme,
    //  'warm-dark': warmDarkTheme,
    'dark-light': darkLightTheme,
    //  'dark-dark': darkDarkTheme,
    'fresh-light': freshLightTheme,
    //  'fresh-dark': freshDarkTheme,
    'charm-light': charmLightTheme,
    //  'charm-dark': charmDarkTheme,
    'mysterious-light': mysteriousLightTheme,
    //  'mysterious-dark': mysteriousDarkTheme
};

// 默认导出主题列表
export default themes;