// 标签类型定义
export interface Tag {
  id: string;
  name: string;
  count: number;
  sort_order: number; // 排序号
  type: number; // 标签类型: 1视频标签，2.短视频标签，3帖子标签
  created_at: string;
  updated_at: string;
}

// 标签查询参数
export interface TagQuery {
  name?: string;
  type?: number;
}

// 分页参数
export interface PageParams {
  pageNo: number;
  pageSize: number;
}

// 请求参数
export interface TagParams {
  page: PageParams;
  data?: TagQuery;
}

// 列表响应数据
export interface TagListResponse {
  list: Tag[];
  total: number;
  page: number;
  pageSize: number;
}

// 创建标签请求
export interface CreateTagRequest {
  name: string;
  type: number;
  sort_order: number;
}

// 更新标签请求
export interface UpdateTagRequest {
  id: string;
  name: string;
  type: number;
  sort_order: number;
}
