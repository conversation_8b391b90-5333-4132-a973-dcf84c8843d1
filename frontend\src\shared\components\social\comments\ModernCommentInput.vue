<!--
  现代化评论输入组件
  提供富文本编辑和媒体上传功能
-->
<template>
  <div class="modern-comment-input">
    <div class="input-container">
      <!-- 用户头像 -->
      <div class="user-avatar">
        <el-avatar :size="40" :src="currentUser?.avatar || defaultAvatar">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <!-- 主输入框 -->
        <div class="input-box" :class="{ 'focused': isFocused, 'expanded': isExpanded }">
          <el-input
            ref="inputRef"
            v-model="commentText"
            type="textarea"
            :placeholder="placeholder"
            :autosize="{ minRows: 2, maxRows: 8 }"
            resize="none"
            @focus="handleFocus"
            @blur="handleBlur"
            @input="handleInput"
            @keydown="handleKeydown"
            class="comment-textarea"
          />
          
          <!-- 字数统计 -->
          <div v-if="commentText.length > 0" class="char-count">
            <span :class="{ 'exceed': commentText.length > maxLength }">
              {{ commentText.length }}
            </span>
            <span class="max-length">/{{ maxLength }}</span>
          </div>
        </div>

        <!-- 工具栏 -->
        <el-collapse-transition>
          <div v-if="isExpanded" class="input-toolbar">
            <div class="toolbar-left">
              <!-- 表情按钮 -->
              <el-button text class="toolbar-btn">
                <el-icon :size="18"><Promotion /></el-icon>
                <span>表情</span>
              </el-button>

              <!-- 图片上传 -->
              <el-button v-if="supportImage" text class="toolbar-btn">
                <el-icon :size="18"><Picture /></el-icon>
                <span>图片</span>
              </el-button>

              <!-- 视频上传 -->
              <el-button v-if="supportVideo" text class="toolbar-btn">
                <el-icon :size="18"><VideoPlay /></el-icon>
                <span>视频</span>
              </el-button>
            </div>

            <div class="toolbar-right">
              <!-- 取消按钮 -->
              <el-button
                v-if="!isReply"
                text
                @click="handleCancel"
                class="cancel-btn"
              >
                取消
              </el-button>

              <!-- 发布按钮 -->
              <el-button
                type="primary"
                :loading="submitting"
                :disabled="!canSubmit"
                @click="handleSubmit"
                class="submit-btn"
              >
                {{ isReply ? '回复' : '发布' }}
              </el-button>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import {
  ElInput,
  ElButton,
  ElAvatar,
  ElIcon,
  ElCollapseTransition,
  ElMessage
} from 'element-plus'
import {
  User,
  Promotion,
  Picture,
  VideoPlay
} from '@element-plus/icons-vue'
import type { CommentSubmitData } from '@/types/comment'

// 组件属性
interface Props {
  targetId: string
  targetType: 'post' | 'video' | 'image' | 'shortvideo'
  placeholder?: string
  maxLength?: number
  supportImage?: boolean
  supportVideo?: boolean
  isReply?: boolean
  replyTo?: any
  autoFocus?: boolean
}

// 组件事件
interface Emits {
  'submit': [data: CommentSubmitData]
  'cancel': []
  'focus': []
  'blur': []
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '分享你的想法...',
  maxLength: 500,
  supportImage: true,
  supportVideo: true,
  isReply: false,
  autoFocus: false
})

const emit = defineEmits<Emits>()

// 响应式状态
const commentText = ref('')
const isFocused = ref(false)
const isExpanded = ref(false)
const submitting = ref(false)
const inputRef = ref()

// 当前用户信息
const currentUser = ref({
  id: 'current-user',
  username: '当前用户',
  avatar: ''
})

const defaultAvatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=default'

// 计算属性
const canSubmit = computed(() => {
  return commentText.value.trim().length > 0 && 
         commentText.value.length <= props.maxLength &&
         !submitting.value
})

// 方法实现
const handleFocus = () => {
  isFocused.value = true
  isExpanded.value = true
  emit('focus')
}

const handleBlur = () => {
  isFocused.value = false
  emit('blur')
}

const handleInput = () => {
  // 输入处理逻辑
}

const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+Enter 快速发布
  if (event.ctrlKey && event.key === 'Enter') {
    if (canSubmit.value) {
      handleSubmit()
    }
  }
}

const handleCancel = () => {
  commentText.value = ''
  isExpanded.value = false
  emit('cancel')
}

const handleSubmit = async () => {
  if (!canSubmit.value) return

  submitting.value = true
  
  try {
    const submitData: CommentSubmitData = {
      content: commentText.value.trim(),
      targetId: props.targetId,
      targetType: props.targetType,
      replyTo: props.replyTo,
      parentId: props.replyTo?.id
    }

    emit('submit', submitData)
    
    // 清空输入
    commentText.value = ''
    isExpanded.value = false
    
  } catch (error) {
    ElMessage.error('发布失败')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  if (props.autoFocus) {
    nextTick(() => {
      inputRef.value?.focus()
    })
  }
})

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  clear: () => {
    commentText.value = ''
    isExpanded.value = false
  }
})
</script>

<style scoped lang="scss">
.modern-comment-input {
  width: 100%;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.user-avatar {
  flex-shrink: 0;
}

.input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-box {
  position: relative;
  border: 1px solid var(--el-border-color);
  border-radius: 12px;
  background: #fff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.focused {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }
  
  &.expanded {
    border-color: var(--el-color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  :deep(.comment-textarea) {
    .el-textarea__inner {
      border: none;
      box-shadow: none;
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.5;
      border-radius: 12px;
      resize: none;
      
      &:focus {
        box-shadow: none;
      }
      
      &::placeholder {
        color: var(--el-text-color-placeholder);
        font-size: 14px;
      }
    }
  }
}

.char-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  
  .exceed {
    color: var(--el-color-danger);
  }
  
  .max-length {
    color: var(--el-text-color-placeholder);
  }
}

.input-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  gap: 12px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 8px;
  border-radius: 6px;
  font-size: 13px;
  color: var(--el-text-color-regular);
  transition: all 0.2s;
  
  &:hover {
    background: var(--el-fill-color-light);
    color: var(--el-color-primary);
  }
}

.cancel-btn {
  color: var(--el-text-color-regular);
  
  &:hover {
    color: var(--el-text-color-primary);
  }
}

.submit-btn {
  border-radius: 20px;
  padding: 8px 20px;
  font-weight: 600;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-container {
    gap: 8px;
  }
  
  .input-toolbar {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .toolbar-left {
    flex-wrap: wrap;
  }
  
  .toolbar-right {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 暗色主题支持 */
.dark {
  .input-box {
    background: var(--el-bg-color-page);
    border-color: var(--el-border-color-dark);
  }
  
  .input-toolbar {
    background: var(--el-bg-color-page);
    border-color: var(--el-border-color-dark);
  }
  
  .toolbar-btn:hover {
    background: var(--el-fill-color-dark);
  }
}
</style> 