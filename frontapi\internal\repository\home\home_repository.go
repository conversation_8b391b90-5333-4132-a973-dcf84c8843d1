package home

import (
	"context"
	"frontapi/internal/models/home"
	"frontapi/internal/models/users"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// HomeRepository 首页数据访问接口
type HomeRepository interface {
	base.ExtendedRepository[home.Tag]
	GetHotVideos(ctx context.Context, limit int) ([]*videos.Video, error)
	GetRecommendedVideos(ctx context.Context, limit int) ([]*videos.Video, error)
	GetRecommendStarList(ctx context.Context, limit int) ([]*users.User, error)
	GetCategories(ctx context.Context) ([]*videos.VideoCategory, error)
}

// homeRepository 首页数据访问实现
type homeRepository struct {
	base.ExtendedRepository[home.Tag]
}

// NewHomeRepository 创建首页仓库实例
func NewHomeRepository(db *gorm.DB) HomeRepository {
	return &homeRepository{
		ExtendedRepository: base.NewExtendedRepository[home.Tag](db),
	}
}

// GetHotVideos 获取热门视频列表
func (r *homeRepository) GetHotVideos(ctx context.Context, limit int) ([]*videos.Video, error) {
	var videos []*videos.Video
	err := r.GetDB().WithContext(ctx).
		Where("status = ?", 1). // type 1 表示热门视频
		Order("view_count DESC, created_at DESC").
		Limit(limit).
		Find(&videos).Error
	if err != nil {
		return nil, err
	}
	return videos, nil
}

// GetRecommendedVideos 获取推荐视频列表
func (r *homeRepository) GetRecommendedVideos(ctx context.Context, limit int) ([]*videos.Video, error) {
	var videos []*videos.Video
	err := r.GetDB().WithContext(ctx).
		Where("status = ? AND is_featured = ?", 1, 2). // type 2 表示推荐视频
		Order("created_at DESC").
		Limit(limit).
		Find(&videos).Error
	if err != nil {
		return nil, err
	}
	return videos, nil
}

// GetRecommendStarList 获取推荐明星列表
func (r *homeRepository) GetRecommendStarList(ctx context.Context, limit int) ([]*users.User, error) {
	var stars []*users.User
	err := r.GetDB().WithContext(ctx).
		Where("status = ?", 1).
		Order("sort ASC, follow_count DESC").
		Limit(limit).
		Find(&stars).Error
	if err != nil {
		return nil, err
	}
	return stars, nil
}

// GetCategories 获取分类列表
func (r *homeRepository) GetCategories(ctx context.Context) ([]*videos.VideoCategory, error) {
	var categories []*videos.VideoCategory
	err := r.GetDB().WithContext(ctx).
		Where("status = ?", 1).
		Order("sort ASC").
		Find(&categories).Error
	if err != nil {
		return nil, err
	}
	return categories, nil
}
