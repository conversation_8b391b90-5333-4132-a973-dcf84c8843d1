package comics

import (
	"context"
	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ComicFavoriteRepository 漫画收藏数据访问接口
type ComicFavoriteRepository interface {
	base.ExtendedRepository[comics.ComicFavorite]
	CheckUserLiked(ctx context.Context, userID, comicID string) (bool, error)
}

// comicFavoriteRepository 漫画收藏数据访问实现
type comicFavoriteRepository struct {
	base.ExtendedRepository[comics.ComicFavorite]
}

func NewComicFavoriteRepository(db *gorm.DB) ComicFavoriteRepository {
	return &comicFavoriteRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.ComicFavorite](db),
	}
}

func (r *comicFavoriteRepository) CheckUserLiked(ctx context.Context, userID, comicID string) (bool, error) {
	// 检查用户是否已经收藏该漫画
	exists, err := r.ExtendedRepository.FindOne(ctx, map[string]interface{}{
		"user_id":  userID,
		"comic_id": comicID,
	})
	if err != nil {
		return false, err
	}
	return exists != nil, nil
}
