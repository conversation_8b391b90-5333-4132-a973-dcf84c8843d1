package integral

// IntegralRechargeRequest 积分充值请求验证模型
type IntegralRechargeRequest struct {
	Amount      int    `json:"amount" validate:"required,min=10"`
	Channel     string `json:"channel" validate:"required,oneof=alipay wechat paypal creditcard wallet"`
	Description string `json:"description" validate:"omitempty,max=200"`
}

// IntegralGiftRequest 积分赠送请求验证模型
type IntegralGiftRequest struct {
	ToUserID string `json:"toUserId" validate:"required"`
	Amount   int    `json:"amount" validate:"required,min=1"`
	Message  string `json:"message" validate:"omitempty,max=100"`
}

// IntegralRecordListRequest 积分记录列表请求验证模型
type IntegralRecordListRequest struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"pageSize" validate:"min=1,max=100"`
	StartDate string `json:"startDate" validate:"omitempty,datetime=2006-01-02"`
	EndDate   string `json:"endDate" validate:"omitempty,datetime=2006-01-02,gtfield=StartDate"`
	Type      string `json:"type" validate:"omitempty,oneof=all recharge exchange gift task signin"`
}