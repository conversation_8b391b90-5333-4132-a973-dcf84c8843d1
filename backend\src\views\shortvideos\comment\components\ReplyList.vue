<template>
    <el-dialog title="评论回复列表" :model-value="visible" @update:model-value="handleClose" width="800px" destroy-on-close>
        <div v-if="parentComment" class="parent-comment">
            <h4>原评论</h4>
            <div class="comment-box">
                <div class="comment-header">
                    <el-avatar :size="24" :src="parentComment.user_avatar || ''"
                        :alt="parentComment.user_name || parentComment.user_id">
                        {{ (parentComment.user_name || parentComment.user_id || '')[0] }}
                    </el-avatar>
                    <span class="username">{{ parentComment.user_name || '用户' + parentComment.user_id }}</span>
                    <span class="time">{{ formatDateTime(parentComment.created_at) }}</span>
                </div>
                <div class="comment-content">{{ parentComment.content }}</div>
            </div>
        </div>

        <div class="replies-container">
            <h4>回复列表</h4>
            <div v-loading="loading">
                <div v-if="replyList.length > 0" class="reply-list">
                    <div v-for="reply in replyList" :key="reply.id" class="reply-item">
                        <div class="reply-header">
                            <el-avatar :size="24" :src="reply.user_avatar || ''"
                                :alt="reply.user_name || reply.user_id">
                                {{ (reply.user_name || reply.user_id || '')[0] }}
                            </el-avatar>
                            <span class="username">{{ reply.user_name || '用户' + reply.user_id }}</span>
                            <span class="time">{{ formatDateTime(reply.created_at) }}</span>
                        </div>
                        <div class="reply-content">{{ reply.content }}</div>
                        <div class="reply-footer">
                            <el-popconfirm title="确定要删除该回复吗？此操作不可恢复！" @confirm="handleDelete(reply)">
                                <template #reference>
                                    <el-button type="danger" size="small" link>
                                        <el-icon>
                                            <Delete />
                                        </el-icon>
                                        删除
                                    </el-button>
                                </template>
                            </el-popconfirm>
                        </div>
                    </div>
                </div>
                <el-empty v-else description="暂无回复"></el-empty>
            </div>

            <div v-if="replyList.length > 0 && pagination.total > pagination.pageSize" class="reply-pagination">
                <el-pagination :current-page="pagination.page" :page-size="pagination.pageSize"
                    :total="pagination.total" background layout="prev, pager, next, jumper"
                    @current-change="handlePageChange" small />
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ShortVideoComment } from '@/types/shortvideos';
import { Delete } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { PropType } from 'vue';

// Props定义
interface Props {
    visible: boolean;
    loading: boolean;
    parentComment: ShortVideoComment | null;
    replyList: ShortVideoComment[];
    pagination: {
        page: number;
        pageSize: number;
        total: number;
    };
}

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    },
    parentComment: {
        type: Object as PropType<ShortVideoComment | null>,
        default: null
    },
    replyList: {
        type: Array as PropType<ShortVideoComment[]>,
        default: () => []
    },
    pagination: {
        type: Object as PropType<{
            page: number;
            pageSize: number;
            total: number;
        }>,
        default: () => ({
            page: 1,
            pageSize: 10,
            total: 0
        })
    }
});

// Emits定义
interface Emits {
    'update:visible': [value: boolean];
    'delete': [row: ShortVideoComment];
    'page-change': [page: number];
}

const emit = defineEmits<Emits>();

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
};

// 删除回复
const handleDelete = (row: ShortVideoComment) => {
    emit('delete', row);
};

// 页码变化
const handlePageChange = (page: number) => {
    emit('page-change', page);
};

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};
</script>

<style scoped lang="scss">
.parent-comment {
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;

    h4 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #303133;
        font-weight: 600;
    }
}

.comment-box,
.reply-item {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
}

.comment-header,
.reply-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.username {
    font-weight: bold;
    color: #303133;
}

.time {
    color: #909399;
    font-size: 12px;
    margin-left: auto;
}

.comment-content,
.reply-content {
    padding: 5px 0;
    white-space: pre-wrap;
    word-break: break-all;
    color: #606266;
}

.reply-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 5px;
}

.replies-container {
    max-height: 500px;
    overflow-y: auto;

    h4 {
        margin-top: 0;
        margin-bottom: 10px;
        color: #303133;
        font-weight: 600;
    }
}

.reply-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.reply-pagination {
    margin-top: 15px;
    display: flex;
    justify-content: center;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>