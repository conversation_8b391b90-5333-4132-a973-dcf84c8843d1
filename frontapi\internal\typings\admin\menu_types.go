package admin

import (
	"time"

	permissionModels "frontapi/internal/models/permission"
)

// MenuListResponse 菜单列表响应（兼容前端格式）
type MenuListResponse struct {
	ID         int                 `json:"id"`
	Parent     *int                `json:"parent"`
	Path       string              `json:"path"`
	Name       string              `json:"name"`
	Permission string              `json:"permission"`
	Type       int8                `json:"type"`
	Icon       string              `json:"icon"`
	OrderNo    int                 `json:"orderNo"` // 对应Sort字段
	Component  string              `json:"component"`
	Keepalive  int8                `json:"keepalive"` // 对应IsCache字段
	Show       int8                `json:"show"`      // IsHidden的反值
	Status     int8                `json:"status"`
	External   int8                `json:"external"` // 对应IsFrame字段
	CreatedAt  string              `json:"createdAt"`
	UpdatedAt  string              `json:"updatedAt"`
	Children   []*MenuListResponse `json:"children,omitempty"`
}

// MenuDetailResponse 菜单详情响应
type MenuDetailResponse struct {
	*MenuListResponse
	Title  string `json:"title"`
	Remark string `json:"remark"`
	Meta   string `json:"meta"`
}

// MenuTreeResponse 菜单树响应
type MenuTreeResponse struct {
	ID       int                 `json:"id"`
	Label    string              `json:"label"`
	PID      int                 `json:"pId"`
	Children []*MenuTreeResponse `json:"children,omitempty"`
}

// MenuCheckResponse 菜单检查响应
type MenuCheckResponse struct {
	Exists bool `json:"exists"`
}

// ConvertToMenuListResponse 转换为菜单列表响应格式
func ConvertToMenuListResponse(menu *permissionModels.AdminMenu) *MenuListResponse {
	var parent *int
	if menu.ParentID > 0 {
		parent = &menu.ParentID
	}

	return &MenuListResponse{
		ID:         menu.ID,
		Parent:     parent,
		Path:       menu.Path,
		Name:       menu.Name,
		Permission: menu.Permission,
		Type:       menu.Type,
		Icon:       menu.Icon,
		OrderNo:    menu.Sort,
		Component:  menu.Component,
		Keepalive:  menu.IsCache,
		Show:       1 - menu.IsHidden, // IsHidden的反值
		Status:     menu.Status,
		External:   menu.IsFrame,
		CreatedAt:  time.Time(menu.CreatedAt).Format("2006-01-02 15:04:05"),
		UpdatedAt:  time.Time(menu.UpdatedAt).Format("2006-01-02 15:04:05"),
	}
}

// ConvertToMenuDetailResponse 转换为菜单详情响应格式
func ConvertToMenuDetailResponse(menu *permissionModels.AdminMenu) *MenuDetailResponse {
	listResp := ConvertToMenuListResponse(menu)
	return &MenuDetailResponse{
		MenuListResponse: listResp,
		Title:            menu.Title,
		Remark:           menu.Remark,
		Meta:             menu.Meta,
	}
}

// ConvertMenuTreeToResponse 转换菜单树为响应格式
func ConvertMenuTreeToResponse(menus []*permissionModels.AdminMenu) []*MenuListResponse {
	result := make([]*MenuListResponse, 0, len(menus))
	for _, menu := range menus {
		resp := ConvertToMenuListResponse(menu)
		if len(menu.Children) > 0 {
			resp.Children = ConvertMenuTreeToResponse(menu.Children)
		}
		result = append(result, resp)
	}
	return result
}

// ConvertToMenuTreeResponse 转换为菜单树形结构响应
func ConvertToMenuTreeResponse(menus []*permissionModels.AdminMenu) []*MenuTreeResponse {
	result := make([]*MenuTreeResponse, 0, len(menus))
	for _, menu := range menus {
		treeResp := &MenuTreeResponse{
			ID:    menu.ID,
			Label: menu.Title,
			PID:   menu.ParentID,
		}
		if len(menu.Children) > 0 {
			treeResp.Children = ConvertToMenuTreeResponse(menu.Children)
		}
		result = append(result, treeResp)
	}
	return result
}
