package container

import (
	userRepo "frontapi/internal/repository/users"
	videoRepo "frontapi/internal/repository/videos"
	videoService "frontapi/internal/service/videos"
)

// InitVideoServices 初始化视频相关服务
func InitVideoServices(b *ServiceBuilder) {
	// 初始化视频仓库
	videoRepoTmpl := videoRepo.NewVideoRepository(b.DB())
	videoSourceRepo := videoRepo.NewVideoSourceRepository(b.DB())
	videoCategoryRepo := videoRepo.NewVideoCategoryRepository(b.DB())
	videoCommentRepo := videoRepo.NewVideoCommentRepository(b.DB())
	videoChannelRepo := videoRepo.NewVideoChannelRepository(b.DB())
	videoAlbumRepo := videoRepo.NewVideosAlbumsRepository(b.DB())
	videoLikeRepo := videoRepo.NewVideoLikeRepository(b.DB())
	userVideoCollectRepo := userRepo.NewUserVideoCollectionRepository(b.DB())
	userVideoAlbumCollectRepo := userRepo.NewUserVideoAlbumCollectionRepository(b.DB())

	userRepoTmpl := userRepo.NewuserRepository(b.DB())

	// 初始化视频服务
	container := b.Services()
	container.VideoService = videoService.NewVideoService(videoRepoTmpl, videoSourceRepo, videoCategoryRepo, videoChannelRepo, userRepoTmpl, videoLikeRepo, userVideoCollectRepo)
	container.VideoCategoryService = videoService.NewVideoCategoryService(videoCategoryRepo, videoAlbumRepo, videoRepoTmpl)
	container.VideoCommentService = videoService.NewVideoCommentService(videoCommentRepo, videoRepoTmpl)
	container.VideoChannelService = videoService.NewVideoChannelService(videoChannelRepo, videoCategoryRepo, videoAlbumRepo, videoRepoTmpl)
	container.VideoAlbumService = videoService.NewVideoAlbumService(videoAlbumRepo, videoRepoTmpl, userVideoAlbumCollectRepo)
	container.VideoLikeService = videoService.NewVideoLikeService(videoLikeRepo, videoRepoTmpl, userRepoTmpl)
}
