# 图片管理接口文档

## 图片分类接口 (/pictures/categories)

### 1. 获取图片分类列表

**接口地址**: `POST /api/pictures/categories/getCategoryList`

**接口描述**: 获取图片分类列表

**请求参数**:
```json
{
  "data": {
    "parent_id": "父分类ID",
    "status": 1,
    "include_picture_count": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "category_id",
        "name": "分类名称",
        "description": "分类描述",
        "icon_url": "图标URL",
        "cover_url": "封面图片URL",
        "parent_id": "父分类ID",
        "sort_order": 1,
        "picture_count": 500,
        "album_count": 20,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

## 图片专辑接口 (/pictures/albums)

### 1. 获取专辑列表

**接口地址**: `POST /api/pictures/albums/getAlbumList`

**接口描述**: 获取图片专辑列表，支持分页和筛选

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "creator_id": "创作者ID",
    "status": 1,
    "sort_by": "created_at",
    "sort_order": "desc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "album_id",
        "title": "专辑标题",
        "description": "专辑描述",
        "cover_url": "封面图片URL",
        "category_id": "分类ID",
        "category_name": "分类名称",
        "creator_id": "创作者ID",
        "creator_name": "创作者名称",
        "pictures_count": 50,
        "views_count": 1000,
        "likes_count": 100,
        "collections_count": 20,
        "tags": ["标签1", "标签2"],
        "is_public": true,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 200,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取专辑详情

**接口地址**: `POST /api/pictures/albums/getAlbumDetail`

**接口描述**: 获取指定图片专辑的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "album_id",
    "user_id": "当前用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "album_id",
    "title": "专辑标题",
    "description": "专辑详细描述",
    "cover_url": "封面图片URL",
    "category_id": "分类ID",
    "category_name": "分类名称",
    "creator_id": "创作者ID",
    "creator_name": "创作者名称",
    "creator_avatar": "创作者头像",
    "pictures_count": 50,
    "views_count": 1000,
    "likes_count": 100,
    "collections_count": 20,
    "is_liked": false,
    "is_collected": false,
    "is_followed": false,
    "tags": ["标签1", "标签2"],
    "is_public": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 获取专辑图片

**接口地址**: `POST /api/pictures/albums/getAlbumPictures`

**接口描述**: 获取指定专辑下的图片列表

**请求参数**:
```json
{
  "data": {
    "album_id": "album_id",
    "sort_by": "sort_order",
    "sort_order": "asc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "picture_id",
        "title": "图片标题",
        "description": "图片描述",
        "url": "图片URL",
        "thumbnail_url": "缩略图URL",
        "width": 1920,
        "height": 1080,
        "file_size": 2048576,
        "format": "jpg",
        "album_id": "专辑ID",
        "sort_order": 1,
        "views_count": 100,
        "likes_count": 10,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20
  }
}
```

### 4. 获取推荐专辑

**接口地址**: `POST /api/pictures/albums/getRecommendedAlbums`

**接口描述**: 获取推荐的图片专辑列表

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "category_id": "分类ID",
    "exclude_album_ids": ["album_id1", "album_id2"]
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 图片接口 (/pictures/images)

### 1. 获取图片列表

**接口地址**: `POST /api/pictures/images/getPictureList`

**接口描述**: 获取图片列表，支持分页和筛选

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "album_id": "专辑ID",
    "creator_id": "创作者ID",
    "status": 1,
    "sort_by": "created_at",
    "sort_order": "desc",
    "resolution_filter": "high",
    "format_filter": "jpg"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "picture_id",
        "title": "图片标题",
        "description": "图片描述",
        "url": "图片URL",
        "thumbnail_url": "缩略图URL",
        "width": 1920,
        "height": 1080,
        "file_size": 2048576,
        "format": "jpg",
        "category_id": "分类ID",
        "category_name": "分类名称",
        "album_id": "专辑ID",
        "album_title": "专辑标题",
        "creator_id": "创作者ID",
        "creator_name": "创作者名称",
        "views_count": 100,
        "likes_count": 10,
        "collections_count": 5,
        "is_liked": false,
        "is_collected": false,
        "tags": ["标签1", "标签2"],
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1000,
    "page": 1,
    "pageSize": 20
  }
}
```

## 图片通用接口 (/pictures)

### 1. 获取图片详情

**接口地址**: `POST /api/pictures/getPictureDetail`

**接口描述**: 获取指定图片的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "picture_id",
    "user_id": "当前用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "picture_id",
    "title": "图片标题",
    "description": "图片详细描述",
    "url": "图片URL",
    "thumbnail_url": "缩略图URL",
    "width": 1920,
    "height": 1080,
    "file_size": 2048576,
    "format": "jpg",
    "category_id": "分类ID",
    "category_name": "分类名称",
    "album_id": "专辑ID",
    "album_title": "专辑标题",
    "creator_id": "创作者ID",
    "creator_name": "创作者名称",
    "creator_avatar": "创作者头像",
    "views_count": 100,
    "likes_count": 10,
    "collections_count": 5,
    "is_liked": false,
    "is_collected": false,
    "is_followed": false,
    "tags": ["标签1", "标签2"],
    "exif_data": {
      "camera": "Canon EOS R5",
      "lens": "RF 24-70mm f/2.8L IS USM",
      "focal_length": "50mm",
      "aperture": "f/2.8",
      "shutter_speed": "1/125s",
      "iso": "400",
      "taken_at": "2024-01-01T12:00:00Z"
    },
    "location": {
      "name": "拍摄地点",
      "latitude": 39.9042,
      "longitude": 116.4074
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取推荐专辑

**接口地址**: `POST /api/pictures/getRecommendedAlbums`

**接口描述**: 获取推荐的图片专辑列表（通用接口）

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "category_id": "分类ID",
    "exclude_album_ids": ["album_id1", "album_id2"]
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 图片互动接口

### 3. 点赞图片

**接口地址**: `POST /api/pictures/likePicture`

**接口描述**: 为图片点赞

**请求参数**:
```json
{
  "data": {
    "picture_id": "picture_id",
    "user_id": "用户ID"
  }
}
```

### 4. 取消点赞图片

**接口地址**: `POST /api/pictures/unlikePicture`

**接口描述**: 取消图片点赞

**请求参数**:
```json
{
  "data": {
    "picture_id": "picture_id",
    "user_id": "用户ID"
  }
}
```

### 5. 收藏图片

**接口地址**: `POST /api/pictures/collectPicture`

**接口描述**: 收藏图片到个人收藏夹

**请求参数**:
```json
{
  "data": {
    "picture_id": "picture_id",
    "user_id": "用户ID",
    "collection_id": "收藏夹ID"
  }
}
```

### 6. 取消收藏图片

**接口地址**: `POST /api/pictures/uncollectPicture`

**接口描述**: 取消收藏图片

**请求参数**:
```json
{
  "data": {
    "picture_id": "picture_id",
    "user_id": "用户ID"
  }
}
```

### 7. 下载图片

**接口地址**: `POST /api/pictures/downloadPicture`

**接口描述**: 下载图片（记录下载统计）

**请求参数**:
```json
{
  "data": {
    "picture_id": "picture_id",
    "user_id": "用户ID",
    "quality": "original"
  }
}
```

### 8. 搜索图片

**接口地址**: `POST /api/pictures/searchPictures`

**接口描述**: 根据关键词搜索图片

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "resolution_filter": "high",
    "format_filter": "jpg",
    "color_filter": "colorful",
    "orientation_filter": "landscape"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```