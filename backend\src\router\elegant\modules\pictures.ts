import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from "@/locales";

const routes: GeneratedRoute[] = [
  {
    name: 'pictures',
    path: '/pictures',
    component: 'layout.base',
    meta: {
      title: 'pictures',
      i18nKey: 'route.pictures',
      icon: 'lucide:image',
      order: 6
    },
    children: [
      // 图片分类页面
      {
        name: 'pictures_category',
        path: '/pictures/category',
        component: 'view.pictures_category',
        meta: {
          title: 'pictures_category',
          i18nKey: 'route.pictures_category',
          icon: 'lucide:folder'
        }
      },
      // 图片专辑页面
      {
        name: 'pictures_album',
        path: '/pictures/album',
        component: 'view.pictures_album',
        meta: {
          title: 'pictures_album',
          i18nKey: 'route.pictures_album',
          icon: 'lucide:album'
        }
      },
      // 图片列表页面
      {
        name: 'pictures_list',
        path: '/pictures/list',
        component: 'view.pictures_list',
        meta: {
          title: 'pictures_list',
          i18nKey: 'route.pictures_list',
          icon: 'lucide:list'
        }
      }
    ]
  }
];

export default routes;
