import { h } from 'vue'

interface IconDefinition {
  name: string
  path: string
}

const iconModules = import.meta.glob('/src/assets/icons/*.svg', { eager: true })

const loadIcons = () => {
  const icons: IconDefinition[] = Object.entries(iconModules).map(([path, module]) => ({
    name: path.split('/').pop()?.replace('.svg', '') || '',
    path: (module as any).default
  }))

  const svgContainer = document.createElement('div')
  svgContainer.style.display = 'none'
  svgContainer.innerHTML = `
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      ${icons.map(icon => `
        <symbol id="icon-${icon.name}" viewBox="0 0 1024 1024">
          ${icon.path}
        </symbol>
      `).join('')}
    </svg>
  `
  document.body.appendChild(svgContainer)
}

export default loadIcons