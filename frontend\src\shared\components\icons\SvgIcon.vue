<script setup lang="ts">

interface Props {
  name: string
  color?: string
  size?: string | number
}

const props = defineProps<Props>()
</script>

<template>
  <svg class="icon" :class="props.name" aria-hidden="true"  :style="{ color: props.color, fontSize: props.size }">
    <use :xlink:href="`#icon-${props.name}`"></use>
  </svg>
</template>

<style scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
}
</style>