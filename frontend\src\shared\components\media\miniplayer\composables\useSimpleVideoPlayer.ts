import { ref, onUnmounted, type Ref } from 'vue'

export function useSimpleVideoPlayer(videoElement: Ref<HTMLVideoElement | undefined>) {
  // 播放器状态
  const isPlaying = ref(false)
  const isMuted = ref(false)
  const volume = ref(0.6)
  const currentTime = ref(0)
  const totalDuration = ref(0)
  const isFullscreen = ref(false)

  // 播放
  const play = async () => {
    if (videoElement.value && !isPlaying.value) {
      try {
        await videoElement.value.play()
        isPlaying.value = true
      } catch (error) {
        console.warn('播放失败:', error)
      }
    }
  }

  // 暂停
  const pause = () => {
    if (videoElement.value && isPlaying.value) {
      videoElement.value.pause()
      isPlaying.value = false
    }
  }

  // 切换播放/暂停
  const togglePlay = () => {
    if (isPlaying.value) {
      pause()
    } else {
      play()
    }
  }

  // 静音
  const mute = () => {
    if (videoElement.value) {
      videoElement.value.muted = true
      isMuted.value = true
    }
  }

  // 取消静音
  const unmute = () => {
    if (videoElement.value) {
      videoElement.value.muted = false
      isMuted.value = false
    }
  }

  // 切换静音
  const toggleMute = () => {
    if (isMuted.value) {
      unmute()
    } else {
      mute()
    }
  }

  // 设置音量
  const setVolume = (newVolume: number) => {
    if (videoElement.value && newVolume >= 0 && newVolume <= 1) {
      videoElement.value.volume = newVolume
      volume.value = newVolume
      
      // 如果音量大于0，自动取消静音
      if (newVolume > 0 && isMuted.value) {
        unmute()
      }
    }
  }

  // 跳转到指定时间
  const seek = (time: number) => {
    if (videoElement.value && time >= 0 && time <= totalDuration.value) {
      videoElement.value.currentTime = time
      currentTime.value = time
    }
  }

  // 切换全屏
  const toggleFullscreen = () => {
    if (!videoElement.value) return

    if (isFullscreen.value) {
      // 退出全屏
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    } else {
      // 进入全屏
      if (videoElement.value.requestFullscreen) {
        videoElement.value.requestFullscreen()
      }
    }
  }

  // 监听全屏状态变化
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement
  }

  // 绑定全屏事件监听器
  if (typeof document !== 'undefined') {
    document.addEventListener('fullscreenchange', handleFullscreenChange)
  }

  // 组件卸载时清理
  onUnmounted(() => {
    if (typeof document !== 'undefined') {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  })

  return {
    isPlaying,
    isMuted,
    volume,
    currentTime,
    totalDuration,
    isFullscreen,
    play,
    pause,
    togglePlay,
    mute,
    unmute,
    toggleMute,
    setVolume,
    seek,
    toggleFullscreen
  }
} 