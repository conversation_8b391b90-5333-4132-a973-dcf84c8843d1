# 控制器架构迁移指南

本文档提供了从传统控制器架构迁移到轻量级控制器架构的详细步骤和最佳实践。

## 1. 迁移概述

迁移过程主要包括以下几个步骤：

1. 更新项目依赖和配置
2. 修改控制器代码
3. 更新路由注册
4. 修改服务访问方式
5. 测试和验证

整个迁移过程可以渐进式进行，不需要一次性完成所有改动。

## 2. 详细迁移步骤

### 2.1 更新项目依赖和配置

1. 确保 `main.go` 使用轻量级初始化方式：

```go
// 旧代码
services := bootstrap.InitServices(database.DB)
bootstrap.InitControllerSystem(services)
app.Use(bootstrap.ControllerMiddleware())
routes.RegisterRoutesWithContainer(app, services)

// 新代码
services := bootstrap.InitServices(database.DB)
bootstrap.InitApp(app, services)
routes.RegisterLiteRoutes(app)
```

### 2.2 创建轻量级控制器

对于每个需要迁移的控制器，创建对应的轻量级版本：

```go
// 旧代码 - PictureAlbumController
type PictureAlbumController struct {
    api.BaseController
    albumService   pictures.PictureAlbumService
    pictureService pictures.PictureService
}

func NewPictureAlbumController(
    albumService pictures.PictureAlbumService,
    pictureService pictures.PictureService,
) *PictureAlbumController {
    return &PictureAlbumController{
        BaseController: api.NewBaseController(),
        albumService:   albumService,
        pictureService: pictureService,
    }
}

// 新代码 - LitePictureAlbumController
type LitePictureAlbumController struct {
    api.LiteBaseController
}

func NewLitePictureAlbumController() *LitePictureAlbumController {
    return &LitePictureAlbumController{
        LiteBaseController: api.NewLiteBaseController(),
    }
}
```

### 2.3 修改服务访问方式

将直接字段访问改为通过服务注册表访问：

```go
// 旧代码
albums, total, err := c.albumService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)

// 新代码
albumService := c.GetPictureAlbumService().(pictures.PictureAlbumService)
albums, total, err := albumService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)
```

### 2.4 更新响应构建方式

使用对象池化的响应构建器：

```go
// 旧代码
return bootstrap.NewResponseBuilder().
    WithData(data).
    Success(ctx)

// 新代码
return bootstrap.NewLiteResponseBuilder().
    WithData(data).
    Success(ctx)
```

### 2.5 更新路由注册

创建轻量级路由注册：

```go
// 旧代码
func RegisterPictureRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
    albums := apiGroup.Group("/pictures/albums")
    {
        pictureAlbumController := pictures.NewPictureAlbumController(
            services.PictureAlbumService,
            services.PictureService,
        )
        albums.Post("/getAlbumList", pictureAlbumController.GetAlbumList)
        // ...
    }
}

// 新代码
func registerLitePictureRoutes(apiGroup fiber.Router) {
    pictureAlbumController := pictures.NewLitePictureAlbumController()
    albums := apiGroup.Group("/pictures/albums")
    {
        albums.Post("/getAlbumList", pictureAlbumController.GetAlbumList)
        // ...
    }
}
```

## 3. 渐进式迁移策略

### 3.1 按模块迁移

1. 从较小的模块开始迁移
2. 每次迁移一个完整的模块
3. 迁移后进行充分测试

### 3.2 并行运行两种架构

在迁移期间，可以同时运行两种架构：

```go
// main.go
// 初始化传统控制器系统（用于未迁移的模块）
bootstrap.InitControllerSystem(services)
app.Use(bootstrap.ControllerMiddleware())

// 初始化轻量级控制器系统（用于已迁移的模块）
bootstrap.InitControllerLite(services)
app.Use(bootstrap.LiteControllerMiddleware())

// 注册传统路由
routes.RegisterRoutesWithContainer(app, services)

// 注册轻量级路由
routes.RegisterLiteRoutes(app)
```

### 3.3 迁移顺序建议

1. 先迁移高频访问的API
2. 再迁移复杂度较低的API
3. 最后迁移复杂度高的API

## 4. 常见问题与解决方案

### 4.1 类型断言错误

**问题**：服务类型断言失败导致panic

**解决方案**：使用安全的类型断言

```go
// 不安全的类型断言
albumService := c.GetPictureAlbumService().(pictures.PictureAlbumService)

// 安全的类型断言
if service, ok := c.GetPictureAlbumService().(pictures.PictureAlbumService); ok {
    albumService = service
} else {
    return c.InternalServerError(ctx, "服务类型错误")
}
```

### 4.2 服务未注册

**问题**：尝试访问未注册的服务

**解决方案**：确保所有需要的服务都已注册

```go
// controller_lite.go 中添加所需服务
serviceRegistry.Store("NewService", services.NewService)
```

### 4.3 性能退化

**问题**：某些API迁移后性能反而下降

**解决方案**：
1. 使用pprof分析性能瓶颈
2. 检查是否有过多的类型断言
3. 确保正确使用对象池

## 5. 性能监控与优化

### 5.1 添加性能监控

```go
// 添加性能监控中间件
app.Use(func(c *fiber.Ctx) error {
    start := time.Now()
    err := c.Next()
    duration := time.Since(start)
    
    // 记录请求处理时间
    log.Printf("路径: %s, 方法: %s, 耗时: %v", c.Path(), c.Method(), duration)
    
    return err
})
```

### 5.2 进一步优化建议

1. **请求上下文对象池化**：减少每个请求的内存分配
2. **减少JSON序列化开销**：使用预编译的JSON结构
3. **使用更高效的路由匹配**：优化路由树结构
4. **减少中间件数量**：只使用必要的中间件

## 6. 迁移检查清单

- [ ] 更新main.go中的初始化代码
- [ ] 创建轻量级控制器版本
- [ ] 更新服务访问方式
- [ ] 使用对象池化的响应构建器
- [ ] 更新路由注册
- [ ] 添加性能监控
- [ ] 进行性能测试和对比
- [ ] 修复发现的问题

## 7. 结论

通过按照本指南进行迁移，您可以平稳地将项目从传统控制器架构迁移到轻量级控制器架构，从而获得显著的性能提升和资源使用优化。迁移过程可以渐进式进行，不会对现有功能造成中断。