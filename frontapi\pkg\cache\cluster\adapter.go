package cluster

import (
	"context"
	"fmt"
	"frontapi/pkg/cache/types"
	"hash/fnv"
	"sync"
	"time"
)

// Config 集群配置
type Config struct {
	// 节点列表
	Nodes []types.CacheAdapter
	// 一致性哈希的虚拟节点数量
	VirtualNodes int
	// 哈希函数类型: fnv, md5, sha1
	HashFunction string
}

// Adapter 集群适配器
type ClusterAdapter struct {
	nodes       []types.CacheAdapter
	nodeMap     map[string]types.CacheAdapter
	virtualRing map[uint32]string // 虚拟节点哈希环
	hashKeys    []uint32          // 已排序的哈希键
	mu          sync.RWMutex
	stats       types.CacheStats
}

// New 创建集群适配器
func NewAdapter(config *Config) (*ClusterAdapter, error) {
	if config == nil || len(config.Nodes) == 0 {
		return nil, fmt.Errorf("集群配置不能为空且至少需要一个节点")
	}

	adapter := &ClusterAdapter{
		nodes:       config.Nodes,
		nodeMap:     make(map[string]types.CacheAdapter),
		virtualRing: make(map[uint32]string),
		hashKeys:    make([]uint32, 0),
		stats: types.CacheStats{
			StartTime: time.Now(),
		},
	}

	// 初始化节点映射
	for i, node := range config.Nodes {
		nodeName := fmt.Sprintf("node-%d", i)
		adapter.nodeMap[nodeName] = node
	}

	// 设置虚拟节点数量
	virtualNodes := config.VirtualNodes
	if virtualNodes <= 0 {
		virtualNodes = 100 // 默认每个物理节点有100个虚拟节点
	}

	// 构建哈希环
	for i := range config.Nodes {
		nodeName := fmt.Sprintf("node-%d", i)
		for j := 0; j < virtualNodes; j++ {
			virtualNodeName := fmt.Sprintf("%s-%d", nodeName, j)
			hash := hashKey(virtualNodeName)
			adapter.virtualRing[hash] = nodeName
			adapter.hashKeys = append(adapter.hashKeys, hash)
		}
	}

	// 对哈希键进行排序
	sortUint32Slice(adapter.hashKeys)

	return adapter, nil
}

// 对uint32切片进行排序
func sortUint32Slice(s []uint32) {
	for i := 0; i < len(s); i++ {
		for j := i + 1; j < len(s); j++ {
			if s[i] > s[j] {
				s[i], s[j] = s[j], s[i]
			}
		}
	}
}

// 计算键的哈希值
func hashKey(key string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(key))
	return h.Sum32()
}

// 根据键获取节点
func (a *ClusterAdapter) getNodeByKey(key string) (types.CacheAdapter, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	if len(a.nodes) == 0 {
		return nil, fmt.Errorf("集群中没有可用节点")
	}

	if len(a.nodes) == 1 {
		return a.nodes[0], nil
	}

	// 计算键的哈希值
	hash := hashKey(key)

	// 在哈希环上查找对应的节点
	idx := searchUint32(a.hashKeys, hash)
	if idx >= len(a.hashKeys) {
		idx = 0
	}

	// 获取虚拟节点对应的物理节点
	nodeName := a.virtualRing[a.hashKeys[idx]]
	node, exists := a.nodeMap[nodeName]
	if !exists {
		return nil, fmt.Errorf("找不到节点: %s", nodeName)
	}

	return node, nil
}

// 二分查找大于等于目标值的最小索引
func searchUint32(arr []uint32, target uint32) int {
	left, right := 0, len(arr)-1
	result := len(arr) // 如果所有元素都小于目标值，则返回数组长度

	for left <= right {
		mid := left + (right-left)/2
		if arr[mid] >= target {
			result = mid
			right = mid - 1
		} else {
			left = mid + 1
		}
	}

	return result
}

// Get 获取缓存值
func (a *ClusterAdapter) Get(ctx context.Context, key string) ([]byte, error) {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return nil, err
	}

	value, err := node.Get(ctx, key)
	if err != nil {
		if err == types.ErrNotFound {
			a.stats.Misses++
		}
		return nil, err
	}

	a.stats.Hits++
	return value, nil
}

// Set 设置缓存值
func (a *ClusterAdapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return err
	}

	err = node.Set(ctx, key, value, expiration)
	if err == nil {
		a.stats.Sets++
	}
	return err
}

// Delete 删除缓存值
func (a *ClusterAdapter) Delete(ctx context.Context, key string) error {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return err
	}

	err = node.Delete(ctx, key)
	if err == nil {
		a.stats.Deletes++
	}
	return err
}

// Exists 检查键是否存在
func (a *ClusterAdapter) Exists(ctx context.Context, key string) (bool, error) {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return false, err
	}

	return node.Exists(ctx, key)
}

// Clear 清空所有节点的缓存
func (a *ClusterAdapter) Clear(ctx context.Context) error {
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, node := range a.nodes {
		if err := node.Clear(ctx); err != nil {
			return err
		}
	}

	a.stats.Clears++
	return nil
}

// Close 关闭所有节点的连接
func (a *ClusterAdapter) Close() error {
	a.mu.Lock()
	defer a.mu.Unlock()

	var lastErr error
	for _, node := range a.nodes {
		if err := node.Close(); err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// Stats 获取统计信息
func (a *ClusterAdapter) Stats() *types.CacheStats {
	a.stats.Uptime = time.Since(a.stats.StartTime)

	// 汇总所有节点的统计信息
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, node := range a.nodes {
		nodeStats := node.Stats()
		a.stats.Hits += nodeStats.Hits
		a.stats.Misses += nodeStats.Misses
		a.stats.Sets += nodeStats.Sets
		a.stats.Deletes += nodeStats.Deletes
		a.stats.Clears += nodeStats.Clears
		a.stats.BytesRead += nodeStats.BytesRead
		a.stats.BytesWritten += nodeStats.BytesWritten
	}

	return &a.stats
}

// Name 获取适配器名称
func (a *ClusterAdapter) Name() string {
	return "cluster"
}

// Type 获取适配器类型
func (a *ClusterAdapter) Type() string {
	return "cluster"
}

// MGet 批量获取缓存值
func (a *ClusterAdapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	// 按节点对键进行分组
	nodeKeys := make(map[types.CacheAdapter][]string)
	for _, key := range keys {
		node, err := a.getNodeByKey(key)
		if err != nil {
			continue
		}
		nodeKeys[node] = append(nodeKeys[node], key)
	}

	// 从各节点获取数据
	result := make(map[string][]byte)
	for node, nodeKeyList := range nodeKeys {
		nodeResult, err := node.MGet(ctx, nodeKeyList)
		if err != nil {
			return result, err
		}
		// 合并结果
		for k, v := range nodeResult {
			result[k] = v
		}
	}

	return result, nil
}

// MSet 批量设置缓存值
func (a *ClusterAdapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	if len(items) == 0 {
		return nil
	}

	// 按节点对键值对进行分组
	nodeItems := make(map[types.CacheAdapter]map[string][]byte)
	for key, value := range items {
		node, err := a.getNodeByKey(key)
		if err != nil {
			return err
		}
		if _, exists := nodeItems[node]; !exists {
			nodeItems[node] = make(map[string][]byte)
		}
		nodeItems[node][key] = value
	}

	// 向各节点设置数据
	for node, nodeItemMap := range nodeItems {
		if err := node.MSet(ctx, nodeItemMap, expiration); err != nil {
			return err
		}
	}

	a.stats.Sets += int64(len(items))
	return nil
}

// Increment 增加计数器值
func (a *ClusterAdapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return 0, err
	}

	return node.Increment(ctx, key, delta)
}

// Decrement 减少计数器值
func (a *ClusterAdapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return 0, err
	}

	return node.Decrement(ctx, key, delta)
}

// Expire 设置过期时间
func (a *ClusterAdapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return err
	}

	return node.Expire(ctx, key, expiration)
}

// TTL 获取剩余过期时间
func (a *ClusterAdapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	node, err := a.getNodeByKey(key)
	if err != nil {
		return 0, err
	}

	return node.TTL(ctx, key)
}

// Ping 测试所有节点连接
func (a *ClusterAdapter) Ping(ctx context.Context) error {
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, node := range a.nodes {
		if err := node.Ping(ctx); err != nil {
			return err
		}
	}

	return nil
}

// GetNodes 获取所有节点
func (a *ClusterAdapter) GetNodes() []types.CacheAdapter {
	a.mu.RLock()
	defer a.mu.RUnlock()

	nodes := make([]types.CacheAdapter, len(a.nodes))
	copy(nodes, a.nodes)
	return nodes
}

// GetNodeByKey 根据键获取节点
func (a *ClusterAdapter) GetNodeByKey(key string) (types.CacheAdapter, error) {
	return a.getNodeByKey(key)
}

// AddNode 添加节点
func (a *ClusterAdapter) AddNode(node types.CacheAdapter) error {
	if node == nil {
		return fmt.Errorf("节点不能为空")
	}

	a.mu.Lock()
	defer a.mu.Unlock()

	// 生成唯一的节点名称
	nodeName := fmt.Sprintf("node-%d", len(a.nodes))
	if _, exists := a.nodeMap[nodeName]; exists {
		return fmt.Errorf("节点已存在: %s", nodeName)
	}

	// 添加到节点列表和映射
	a.nodes = append(a.nodes, node)
	a.nodeMap[nodeName] = node

	// 添加虚拟节点
	virtualNodes := 100 // 默认每个物理节点有100个虚拟节点
	for i := 0; i < virtualNodes; i++ {
		virtualNodeName := fmt.Sprintf("%s-%d", nodeName, i)
		hash := hashKey(virtualNodeName)
		a.virtualRing[hash] = nodeName
		a.hashKeys = append(a.hashKeys, hash)
	}

	// 重新排序哈希键
	sortUint32Slice(a.hashKeys)

	return nil
}

// RemoveNode 移除节点
func (a *ClusterAdapter) RemoveNode(nodeName string) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	node, exists := a.nodeMap[nodeName]
	if !exists {
		return fmt.Errorf("节点不存在: %s", nodeName)
	}

	// 关闭节点连接
	if err := node.Close(); err != nil {
		return err
	}

	// 从节点列表中移除
	for i, n := range a.nodes {
		if n.Name() == nodeName {
			a.nodes = append(a.nodes[:i], a.nodes[i+1:]...)
			break
		}
	}

	// 从节点映射中移除
	delete(a.nodeMap, nodeName)

	// 重建虚拟节点哈希环
	a.virtualRing = make(map[uint32]string)
	a.hashKeys = make([]uint32, 0)
	virtualNodes := 100 // 默认每个物理节点有100个虚拟节点
	for _, n := range a.nodes {
		name := n.Name()
		for i := 0; i < virtualNodes; i++ {
			virtualNodeName := fmt.Sprintf("%s-%d", name, i)
			hash := hashKey(virtualNodeName)
			a.virtualRing[hash] = name
			a.hashKeys = append(a.hashKeys, hash)
		}
	}

	// 重新排序哈希键
	sortUint32Slice(a.hashKeys)

	return nil
}
