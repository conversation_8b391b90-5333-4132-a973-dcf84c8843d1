<template>
    <div class="app-container">
        <el-card>
            <template #header>
                <div class="filter-container">
                    <div class="title-container">
                        <h2>短视频评论管理</h2>
                        <div class="buttons">
                            <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
                            <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 搜索表单组件 -->
            <CommentSearchBar v-model="searchForm" @search="handleSearch" @reset="handleReset" @refresh="refreshList"
                class="mb-4" />

            <!-- 评论表格组件 -->
            <CommentTable :loading="loading" :comment-list="commentList" :pagination="pagination"
                @view-detail="handleViewDetail" @toggle-status="handleToggleStatus" @delete="handleDelete"
                @batch-delete="handleBatchDelete" @view-replies="handleViewReplies" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />

            <!-- 评论详情对话框 -->
            <CommentDetail v-model:visible="detailDialogVisible" :comment="currentComment" />

            <!-- 回复列表对话框 -->
            <ReplyList v-model:visible="repliesDialogVisible" :loading="repliesLoading" :parent-comment="parentComment"
                :reply-list="replyList" :pagination="replyPagination" @delete="handleDeleteReply"
                @page-change="handleReplyPageChange" />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import {
    batchDeleteComment,
    deleteComment,
    getCommentList,
    getCommentReplies,
    updateCommentStatus
} from '@/service/api/shortvideos/shortvideos';
import type {
    CommentParams,
    ShortVideoComment
} from '@/types/shortvideos';
import { Download, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入组件
import CommentDetail from './components/CommentDetail.vue';
import CommentSearchBar from './components/CommentSearchBar.vue';
import CommentTable from './components/CommentTable.vue';
import ReplyList from './components/ReplyList.vue';

// 搜索表单
const searchForm = reactive({
    short_id: '',
    user_id: '',
    keyword: '',
    status: undefined as number | undefined,
});

// 分页参数
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
});

// 回复分页参数
const replyPagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
});

// 数据列表
const commentList = ref<ShortVideoComment[]>([]);
const replyList = ref<ShortVideoComment[]>([]);
const selectedRows = ref<ShortVideoComment[]>([]);

// 当前选中的评论
const currentComment = ref<ShortVideoComment | null>(null);
const parentComment = ref<ShortVideoComment | null>(null);

// 加载状态
const loading = ref(false);
const repliesLoading = ref(false);

// 对话框状态
const detailDialogVisible = ref(false);
const repliesDialogVisible = ref(false);

// 初始化加载
onMounted(() => {
    fetchCommentList();
});

// 获取评论列表
const fetchCommentList = async () => {
    loading.value = true;
    try {
        const params: CommentParams = {
            page: {
                pageNo: pagination.page,
                pageSize: pagination.pageSize
            },
            data: {
                short_id: searchForm.short_id || undefined,
                user_id: searchForm.user_id || undefined,
                keyword: searchForm.keyword || undefined,
                status: searchForm.status,
            }
        };

        const { data, err } = await getCommentList(params) as any;

        if (!err && data) {
            commentList.value = data.list || [];
            pagination.total = data.total || 0;
        } else {
            ElMessage.error('获取评论列表失败');
            commentList.value = [];
            pagination.total = 0;
        }
    } catch (error) {
        console.error('获取评论列表出错:', error);
        ElMessage.error('获取评论列表失败');
        commentList.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 获取评论详情
const handleViewDetail = (row: ShortVideoComment) => {
    currentComment.value = row;
    detailDialogVisible.value = true;
};

// 修改状态
const handleToggleStatus = async (row: ShortVideoComment) => {
    try {
        const newStatus = row.status === 0 ? 1 : 0;
        const statusText = newStatus === 0 ? '显示' : '隐藏';

        const { err } = await updateCommentStatus({
            id: row.id,
            status: newStatus
        }) as any;

        if (!err) {
            ElMessage.success(`评论${statusText}成功`);
            row.status = newStatus;
        } else {
            ElMessage.error(`评论${statusText}失败`);
        }
    } catch (error) {
        console.error('更新评论状态出错:', error);
        ElMessage.error('更新评论状态失败');
    }
};

// 删除评论
const handleDelete = async (row: ShortVideoComment) => {
    try {
        const { err } = await deleteComment(row.id) as any;

        if (!err) {
            ElMessage.success('删除评论成功');
            fetchCommentList();
        } else {
            ElMessage.error('删除评论失败');
        }
    } catch (error) {
        console.error('删除评论出错:', error);
        ElMessage.error('删除评论失败');
    }
};

// 批量删除评论
const handleBatchDelete = async (rows: ShortVideoComment[]) => {
    try {
        const ids = rows.map(row => row.id);

        const { err } = await batchDeleteComment({ ids }) as any;

        if (!err) {
            ElMessage.success('批量删除评论成功');
            fetchCommentList();
        } else {
            ElMessage.error('批量删除评论失败');
        }
    } catch (error) {
        console.error('批量删除评论出错:', error);
        ElMessage.error('批量删除评论失败');
    }
};

// 查看回复
const handleViewReplies = async (row: ShortVideoComment) => {
    if (row.reply_count <= 0) {
        ElMessage.info('该评论暂无回复');
        return;
    }

    parentComment.value = row;
    replyPagination.page = 1;
    repliesDialogVisible.value = true;

    await fetchReplies(row.id);
};

// 获取回复列表
const fetchReplies = async (commentId: string) => {
    repliesLoading.value = true;
    try {
        const { data, err } = await getCommentReplies(
            commentId,
            replyPagination.page,
            replyPagination.pageSize
        ) as any;

        if (!err && data) {
            replyList.value = data.list || [];
            replyPagination.total = data.total || 0;
        } else {
            ElMessage.error('获取回复列表失败');
            replyList.value = [];
            replyPagination.total = 0;
        }
    } catch (error) {
        console.error('获取回复列表出错:', error);
        ElMessage.error('获取回复列表失败');
        replyList.value = [];
        replyPagination.total = 0;
    } finally {
        repliesLoading.value = false;
    }
};

// 删除回复
const handleDeleteReply = async (reply: ShortVideoComment) => {
    try {
        const { err } = await deleteComment(reply.id) as any;

        if (!err) {
            ElMessage.success('删除回复成功');

            // 更新父评论的回复数
            if (parentComment.value) {
                parentComment.value.reply_count--;
            }

            // 重新获取回复列表
            if (parentComment.value) {
                await fetchReplies(parentComment.value.id);
            }

            // 刷新评论列表
            fetchCommentList();
        } else {
            ElMessage.error('删除回复失败');
        }
    } catch (error) {
        console.error('删除回复出错:', error);
        ElMessage.error('删除回复失败');
    }
};

// 回复分页变化
const handleReplyPageChange = (page: number) => {
    replyPagination.page = page;
    if (parentComment.value) {
        fetchReplies(parentComment.value.id);
    }
};

// 分页大小变化
const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.page = 1;
    fetchCommentList();
};

// 页码变化
const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchCommentList();
};

// 搜索
const handleSearch = () => {
    pagination.page = 1;
    fetchCommentList();
};

// 重置搜索
const handleReset = () => {
    searchForm.short_id = '';
    searchForm.user_id = '';
    searchForm.keyword = '';
    searchForm.status = undefined;
    pagination.page = 1;
    fetchCommentList();
};

// 刷新列表
const refreshList = () => {
    fetchCommentList();
};

// 导出评论
const handleExport = () => {
    ElMessage.info('导出功能开发中...');
};
</script>

<style scoped lang="scss">
.app-container {
    padding: 20px;
}

.filter-container {
    margin-bottom: 20px;
}

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.title-container h2 {
    margin: 0;
    color: #303133;
    font-weight: 600;
}

.buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }

    .title-container {
        flex-direction: column;
        align-items: stretch;
    }

    .buttons {
        justify-content: center;
    }
}
</style>
