<template>
  <div class="menu-management">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <div>
            <span class="text-lg font-medium">菜单管理</span>
          </div>
          <el-button type="primary" @click="handleAdd">添加菜单</el-button>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="search-bar mb-4">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
          <el-form-item label="菜单名称">
            <el-input v-model="searchForm.name" placeholder="请输入菜单名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="菜单类型">
            <el-select v-model="searchForm.type" placeholder="请选择菜单类型" clearable style="width: 180px;">
              <el-option label="目录" :value="0"></el-option>
              <el-option label="菜单" :value="1"></el-option>
              <el-option label="按钮" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
              <el-option label="启用" :value="1"></el-option>
              <el-option label="禁用" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button @click="fetchMenuList">刷新</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 菜单表格 -->
      <el-table
        v-loading="loading"
        :data="menuList"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children' }"
        style="width: 100%"
        border
        stripe
      >
        <el-table-column prop="name" label="菜单名称" width="180"></el-table-column>
        <el-table-column prop="icon" label="图标" width="100" align="center">
          <template #default="scope">
            <i v-if="scope.row.icon" :class="scope.row.icon"></i>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由地址" min-width="180"></el-table-column>
        <el-table-column prop="component" label="组件路径" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.component">{{ scope.row.component }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="permission" label="权限标识" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.permission">{{ scope.row.permission }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="菜单类型" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 0" type="primary">目录</el-tag>
            <el-tag v-else-if="scope.row.type === 1" type="success">菜单</el-tag>
            <el-tag v-else-if="scope.row.type === 2" type="warning">按钮</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="orderNo" label="排序" width="80" align="center"></el-table-column>
        <el-table-column label="显示" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.show === 1" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 1" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" link @click="handleAddChild(scope.row)" v-if="scope.row.type !== 2">添加子菜单</el-button>
            <el-button v-if="scope.row.status === 0" type="success" link @click="handleChangeStatus(scope.row, 1)">启用</el-button>
            <el-button v-else type="warning" link @click="handleChangeStatus(scope.row, 0)">禁用</el-button>
            <el-popconfirm title="确定删除此菜单吗？此操作会同时删除其子菜单！" @confirm="handleDelete(scope.row)">
              <template #reference>
                <el-button type="danger" link>删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container mt-4 flex justify-end">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 菜单表单对话框 -->
    <menu-form-dialog
      v-if="formDialogVisible"
      :visible="formDialogVisible"
      :type="dialogType"
      :menu-data="currentMenu"
      :parent-menu="parentMenu"
      @close="formDialogVisible=false"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getMenuList, getMenuDetail, deleteMenu, updateMenuStatus } from '@/service/api/permission/menus';
import type { Menu } from '@/types/menus';
import MenuFormDialog from './components/MenuFormDialog.vue';

// 状态变量
const loading = ref(false);
const menuList = ref<Menu[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchForm = reactive({
  name: '',
  type: undefined as number | undefined,
  status: undefined as number | undefined,
});

// 弹窗相关
const formDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit' | 'addChild'>('add');
const currentMenu = ref<Menu | null>(null);
const parentMenu = ref<Menu | null>(null);

// 生命周期钩子
onMounted(() => {
  fetchMenuList();
});

// 加载菜单列表
const fetchMenuList = async () => {
  loading.value = true;
  try {
    const params = {
      page: {
        pageNo: currentPage.value,
        pageSize: pageSize.value,
      },
      data: {} as Record<string, any>
    };

    // 添加搜索条件
    if (searchForm.name) params.data.name = searchForm.name;
    if (searchForm.type !== undefined) params.data.type = searchForm.type;
    if (searchForm.status !== undefined) params.data.status = searchForm.status;

    const res = await getMenuList(params);
    console.log(res);
    if (res.code === 2000 && res.data) {
      menuList.value = res.data.list || [];
      total.value = res.data.total || 0;
    } else {
      ElMessage.error(res.message || '获取菜单列表失败');
    }
  } catch (error) {
    console.error('获取菜单列表出错:', error);
    ElMessage.error('获取菜单列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchMenuList();
};

// 重置搜索
const handleReset = () => {
  searchForm.name = '';
  searchForm.type = undefined;
  searchForm.status = undefined;
  handleSearch();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  fetchMenuList();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchMenuList();
};

// 添加菜单
const handleAdd = () => {
  dialogType.value = 'add';
  currentMenu.value = null;
  parentMenu.value = null;
  formDialogVisible.value = true;
};

// 添加子菜单
const handleAddChild = (row: Menu) => {
  dialogType.value = 'addChild';
  currentMenu.value = null;
  parentMenu.value = row;
  formDialogVisible.value = true;
};

// 编辑菜单
const handleEdit = (row: Menu) => {
  dialogType.value = 'edit';
  currentMenu.value = row;
  parentMenu.value = null;
  formDialogVisible.value = true;
};

// 更改菜单状态
const handleChangeStatus = async (row: Menu, status: number) => {
  try {
    const res = await updateMenuStatus(row.id, status);
    if (res && res.code === 2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}菜单成功`);
      fetchMenuList();
    } else {
      ElMessage.error((res && res.message) || `${status === 1 ? '启用' : '禁用'}菜单失败`);
    }
  } catch (error) {
    console.error(`${status === 1 ? '启用' : '禁用'}菜单出错:`, error);
    ElMessage.error(`${status === 1 ? '启用' : '禁用'}菜单失败`);
  }
};

// 删除菜单
const handleDelete = async (row: Menu) => {
  try {
    const res = await deleteMenu(row.id);
    if (res && res.code === 2000) {
      ElMessage.success('删除菜单成功');
      fetchMenuList();
    } else {
      ElMessage.error((res && res.message) || '删除菜单失败');
    }
  } catch (error) {
    console.error('删除菜单出错:', error);
    ElMessage.error('删除菜单失败');
  }
};

// 表单提交成功处理
const handleFormSuccess = () => {
  formDialogVisible.value = false;
  fetchMenuList();
};
</script>

<style scoped>
.menu-management {
  padding: 16px;
}
</style> 