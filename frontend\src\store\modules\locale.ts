/**
 * 国际化语言状态管理
 */

import { DEFAULT_LOCALE, SUPPORTED_LOCALES, getLocaleInfo } from '@/config/locales.config';
import { detectLocale, saveLocaleToStorage } from '@/core/plugins/i18n/utils';
import { isLocaleSupported, type LocaleType } from '@/locales';
import { defineStore } from 'pinia';
import { ref, watch } from 'vue';

/**
 * 国际化语言存储
 * 负责管理当前应用的语言设置和持久化
 */
export const useLocaleStore = defineStore('locale', () => {
    // 当前语言
    const locale = ref<LocaleType>(DEFAULT_LOCALE as LocaleType);

    // 初始化语言设置
    function initializeLocale() {
        // 检测最佳匹配语言
        const bestLocale = detectLocale(true);
        setLocale(bestLocale);
    }

    // 设置语言
    function setLocale(newLocale: string) {
        // 验证语言是否受支持
        if (!isLocaleSupported(newLocale)) {
            console.warn(`Locale '${newLocale}' is not supported, using default ${DEFAULT_LOCALE}`);
            newLocale = DEFAULT_LOCALE;
        }

        // 更新当前语言
        locale.value = newLocale as LocaleType;

        // 持久化语言设置
        saveLocaleToStorage(locale.value);

        // 更新HTML lang属性
        document.documentElement.setAttribute('lang', newLocale);

        // 检查是否为RTL语言
        const isRTL = ['ar', 'he', 'fa', 'ur'].some(code => newLocale.startsWith(code));
        document.documentElement.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
    }

    // 获取当前语言
    function getLocale(): LocaleType {
        return locale.value;
    }

    // 获取支持的语言代码列表
    function getSupportedLocales(): string[] {
        return SUPPORTED_LOCALES
            .filter(locale => locale.enabled)
            .map(locale => locale.code);
    }

    // 获取语言代码（用于显示在界面上）
    function getLanguageCode(): string {
        const localeInfo = getLocaleInfo(locale.value);
        return localeInfo?.shortCode || locale.value;
    }

    // 获取当前语言的本地名称
    function getLocaleName(): string {
        const localeInfo = getLocaleInfo(locale.value);
        return localeInfo?.nativeName || locale.value;
    }

    // 获取当前语言的英文名称
    function getEnglishName(): string {
        const localeInfo = getLocaleInfo(locale.value);
        return localeInfo?.englishName || locale.value;
    }

    // 获取当前语言的国旗
    function getLocaleFlag(): string {
        const localeInfo = getLocaleInfo(locale.value);
        return localeInfo?.flag || '🌐';
    }

    // 获取所有可用语言的信息
    function getAllLocales() {
        return SUPPORTED_LOCALES.filter(locale => locale.enabled);
    }

    // 当语言变化时自动更新HTML属性
    watch(locale, (newLocale) => {
        document.documentElement.setAttribute('lang', newLocale);
        const isRTL = ['ar', 'he', 'fa', 'ur'].some(code => newLocale.startsWith(code));
        document.documentElement.setAttribute('dir', isRTL ? 'rtl' : 'ltr');
    });

    return {
        locale,
        initializeLocale,
        setLocale,
        getLocale,
        getSupportedLocales,
        getLanguageCode,
        getLocaleName,
        getEnglishName,
        getLocaleFlag,
        getAllLocales
    };
}); 