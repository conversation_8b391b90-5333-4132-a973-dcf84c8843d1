package content_creator

import (
	"frontapi/internal/models/content_creator"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type RevenueRuleRepository interface {
	base.ExtendedRepository[content_creator.RevenueRule]
}
type revenueRuleRepositoryTmpl struct {
	base.ExtendedRepository[content_creator.RevenueRule]
}

func NewRevenueRuleRepository(db *gorm.DB) RevenueRuleRepository {
	return &revenueRuleRepositoryTmpl{
		ExtendedRepository: base.NewExtendedRepository[content_creator.RevenueRule](db),
	}
}
