package redis

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	cacheConfig "frontapi/config/cache"

	goredis "github.com/redis/go-redis/v9"

	"frontapi/pkg/cache/types"
)

// Adapter Redis缓存适配器实现
type Adapter struct {
	client        *goredis.Client
	clusterClient *goredis.ClusterClient
	prefix        string
	stats         *types.CacheStats
	name          string
	defaultTTL    time.Duration
	statsMutex    sync.RWMutex
	isCluster     bool
	startTime     time.Time
}

// // Config Redis配置
// type Config struct {
// 	Host         string
// 	Port         int
// 	Password     string
// 	DB           int
// 	Prefix       string
// 	Name         string
// 	DefaultTTL   time.Duration
// 	PoolSize     int
// 	MinIdleConns int
// 	MaxRetries   int
// 	IdleTimeout  time.Duration
// 	DialTimeout  time.Duration
// 	ReadTimeout  time.Duration
// 	WriteTimeout time.Duration
// 	// 集群配置
// 	Cluster   bool
// 	Addresses []string
// }

// NewAdapter 创建Redis缓存适配器
func NewAdapter(config *cacheConfig.RedisConfig) (*Adapter, error) {
	if config == nil {
		return nil, types.ErrInvalidConfig
	}

	adapter := &Adapter{
		name:       config.Name,
		prefix:     config.Prefix,
		stats:      &types.CacheStats{},
		defaultTTL: config.DefaultTTL,
		isCluster:  config.Cluster.Enable,
		startTime:  time.Now(),
	}

	// 如果未设置默认TTL，使用1小时
	if adapter.defaultTTL <= 0 {
		adapter.defaultTTL = time.Hour
	}

	if adapter.name == "" {
		adapter.name = "redis"
	}

	// 初始化Redis客户端
	if config.Cluster.Enable {
		clusterOpts := &goredis.ClusterOptions{
			Addrs:        config.Addrs,
			Password:     config.Password,
			PoolSize:     config.PoolSize,
			MinIdleConns: config.MinIdleConns,
			MaxRetries:   config.MaxRetries,
			DialTimeout:  time.Duration(config.DialTimeout) * time.Second,
			ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
			WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
			// 连接池优化
			PoolFIFO: true, // 使用FIFO而不是LIFO，减少连接波动
		}

		if clusterOpts.PoolSize <= 0 {
			clusterOpts.PoolSize = 10 * 4 // 默认为CPU核心数的10倍
		}

		if clusterOpts.MinIdleConns <= 0 {
			clusterOpts.MinIdleConns = clusterOpts.PoolSize / 4
		}

		if len(clusterOpts.Addrs) == 0 {
			return nil, fmt.Errorf("集群地址列表不能为空")
		}

		adapter.clusterClient = goredis.NewClusterClient(clusterOpts)

		// 验证连接
		if err := adapter.clusterClient.Ping(context.Background()).Err(); err != nil {
			return nil, fmt.Errorf("连接Redis集群失败: %w", err)
		}
	} else {
		clientOpts := &goredis.Options{
			Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
			Password:     config.Password,
			DB:           config.DB,
			PoolSize:     config.PoolSize,
			MinIdleConns: config.MinIdleConns,
			MaxRetries:   config.MaxRetries,
			DialTimeout:  time.Duration(config.DialTimeout) * time.Second,
			ReadTimeout:  time.Duration(config.ReadTimeout) * time.Second,
			WriteTimeout: time.Duration(config.WriteTimeout) * time.Second,
			// 连接池优化
			PoolFIFO: true, // 使用FIFO而不是LIFO，减少连接波动
		}

		// 设置默认值
		if clientOpts.PoolSize <= 0 {
			clientOpts.PoolSize = 10 * 4 // 默认为CPU核心数的10倍
		}

		if clientOpts.MinIdleConns <= 0 {
			clientOpts.MinIdleConns = clientOpts.PoolSize / 4
		}

		if config.Host == "" {
			clientOpts.Addr = "localhost:6379"
		}

		adapter.client = goredis.NewClient(clientOpts)

		// 验证连接
		if err := adapter.client.Ping(context.Background()).Err(); err != nil {
			return nil, fmt.Errorf("连接Redis失败: %w", err)
		}
	}

	return adapter, nil
}

// Get 获取缓存值
func (a *Adapter) Get(ctx context.Context, key string) ([]byte, error) {
	prefixedKey := a.KeyWithPrefix(key)

	var val string
	var err error

	if a.isCluster {
		val, err = a.clusterClient.Get(ctx, prefixedKey).Result()
	} else {
		val, err = a.client.Get(ctx, prefixedKey).Result()
	}

	if err != nil {
		if err == goredis.Nil {
			atomic.AddInt64(&a.stats.Misses, 1)
			return nil, types.ErrNotFound
		}
		return nil, err
	}

	atomic.AddInt64(&a.stats.Hits, 1)
	atomic.AddInt64(&a.stats.BytesRead, int64(len(val)))

	return []byte(val), nil
}

// Set 设置缓存值
func (a *Adapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	prefixedKey := a.KeyWithPrefix(key)

	if expiration <= 0 {
		expiration = a.defaultTTL
	}

	var err error
	if a.isCluster {
		err = a.clusterClient.Set(ctx, prefixedKey, value, expiration).Err()
	} else {
		err = a.client.Set(ctx, prefixedKey, value, expiration).Err()
	}

	if err != nil {
		return err
	}

	atomic.AddInt64(&a.stats.Sets, 1)
	atomic.AddInt64(&a.stats.BytesWritten, int64(len(value)))

	return nil
}

// Delete 删除缓存键
func (a *Adapter) Delete(ctx context.Context, key string) error {
	prefixedKey := a.KeyWithPrefix(key)

	var err error
	if a.isCluster {
		err = a.clusterClient.Del(ctx, prefixedKey).Err()
	} else {
		err = a.client.Del(ctx, prefixedKey).Err()
	}

	if err != nil && err != goredis.Nil {
		return err
	}

	atomic.AddInt64(&a.stats.Deletes, 1)

	return nil
}

// Clear 清除所有带指定前缀的缓存键
func (a *Adapter) Clear(ctx context.Context) error {
	var cursor uint64
	var keys []string
	var err error
	pattern := a.KeyWithPrefix("*")

	// 批量删除键，使用SCAN命令防止阻塞
	if a.isCluster {
		err = a.clearCluster(ctx, pattern)
	} else {
		for {
			keys, cursor, err = a.client.Scan(ctx, cursor, pattern, 100).Result()
			if err != nil {
				return err
			}

			if len(keys) > 0 {
				if err := a.client.Del(ctx, keys...).Err(); err != nil && err != goredis.Nil {
					return err
				}
			}

			if cursor == 0 {
				break
			}
		}
	}

	atomic.AddInt64(&a.stats.Clears, 1)

	return nil
}

// 清除集群中所有带指定前缀的缓存键
func (a *Adapter) clearCluster(ctx context.Context, pattern string) error {
	// 对集群中的每个主节点执行清理操作
	err := a.clusterClient.ForEachMaster(ctx, func(ctx context.Context, client *goredis.Client) error {
		var cursor uint64
		var keys []string
		var scanErr error

		for {
			keys, cursor, scanErr = client.Scan(ctx, cursor, pattern, 100).Result()
			if scanErr != nil {
				return scanErr
			}

			if len(keys) > 0 {
				if delErr := client.Del(ctx, keys...).Err(); delErr != nil && delErr != goredis.Nil {
					return delErr
				}
			}

			if cursor == 0 {
				break
			}
		}

		return nil
	})

	return err
}

// Close 关闭Redis连接
func (a *Adapter) Close() error {
	var err error
	if a.isCluster {
		err = a.clusterClient.Close()
	} else {
		err = a.client.Close()
	}
	return err
}

// Stats 获取缓存统计信息
func (a *Adapter) Stats() *types.CacheStats {
	a.statsMutex.RLock()
	defer a.statsMutex.RUnlock()

	stats := &types.CacheStats{
		Hits:         atomic.LoadInt64(&a.stats.Hits),
		Misses:       atomic.LoadInt64(&a.stats.Misses),
		Sets:         atomic.LoadInt64(&a.stats.Sets),
		Deletes:      atomic.LoadInt64(&a.stats.Deletes),
		Clears:       atomic.LoadInt64(&a.stats.Clears),
		StartTime:    a.startTime,
		Uptime:       time.Since(a.startTime),
		BytesRead:    atomic.LoadInt64(&a.stats.BytesRead),
		BytesWritten: atomic.LoadInt64(&a.stats.BytesWritten),
	}

	// 计算命中率
	totalOps := stats.Hits + stats.Misses
	if totalOps > 0 {
		stats.HitRate = float64(stats.Hits) / float64(totalOps)
	}

	// 获取Redis服务器统计信息
	if !a.isCluster {
		if info, err := a.client.Info(context.Background()).Result(); err == nil {
			// 解析INFO命令结果（简单处理）
			// 这里可以添加更详细的统计信息解析
			_ = info
		}
	}

	return stats
}

// Name 获取适配器名称
func (a *Adapter) Name() string {
	return a.name
}

// Type 获取适配器类型
func (a *Adapter) Type() string {
	return "redis"
}

// KeyWithPrefix 为缓存键添加前缀
func (a *Adapter) KeyWithPrefix(key string) string {
	if a.prefix == "" {
		return key
	}
	return a.prefix + ":" + key
}

// Exists 检查键是否存在
func (a *Adapter) Exists(ctx context.Context, key string) (bool, error) {
	prefixedKey := a.KeyWithPrefix(key)

	var result int64
	var err error

	if a.isCluster {
		result, err = a.clusterClient.Exists(ctx, prefixedKey).Result()
	} else {
		result, err = a.client.Exists(ctx, prefixedKey).Result()
	}

	if err != nil {
		return false, err
	}

	return result > 0, nil
}

// MGet 批量获取多个键的值
func (a *Adapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	// 为所有键添加前缀
	prefixedKeys := make([]string, len(keys))
	for i, key := range keys {
		prefixedKeys[i] = a.KeyWithPrefix(key)
	}

	// 执行批量获取
	var values []interface{}
	var err error

	if a.isCluster {
		values, err = a.clusterClient.MGet(ctx, prefixedKeys...).Result()
	} else {
		values, err = a.client.MGet(ctx, prefixedKeys...).Result()
	}

	if err != nil {
		return nil, err
	}

	// 构建结果映射
	result := make(map[string][]byte, len(keys))
	for i, value := range values {
		if value == nil {
			continue
		}

		// 类型断言
		if strValue, ok := value.(string); ok {
			result[keys[i]] = []byte(strValue)
			atomic.AddInt64(&a.stats.Hits, 1)
			atomic.AddInt64(&a.stats.BytesRead, int64(len(strValue)))
		}
	}

	// 更新未命中计数
	atomic.AddInt64(&a.stats.Misses, int64(len(keys)-len(result)))

	return result, nil
}

// MSet 批量设置多个键值对
func (a *Adapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	if len(items) == 0 {
		return nil
	}

	// 如果未指定过期时间，使用默认值
	if expiration <= 0 {
		expiration = a.defaultTTL
	}

	// 准备批量操作管道
	var pipe goredis.Pipeliner
	if a.isCluster {
		pipe = a.clusterClient.Pipeline()
	} else {
		pipe = a.client.Pipeline()
	}

	// 添加所有SET命令到管道
	bytesWritten := int64(0)
	for key, value := range items {
		prefixedKey := a.KeyWithPrefix(key)
		pipe.Set(ctx, prefixedKey, value, expiration)
		bytesWritten += int64(len(value))
	}

	// 执行管道
	_, err := pipe.Exec(ctx)
	if err != nil {
		return err
	}

	atomic.AddInt64(&a.stats.Sets, int64(len(items)))
	atomic.AddInt64(&a.stats.BytesWritten, bytesWritten)

	return nil
}

// Increment 递增计数器
func (a *Adapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	prefixedKey := a.KeyWithPrefix(key)

	var result int64
	var err error

	if a.isCluster {
		result, err = a.clusterClient.IncrBy(ctx, prefixedKey, delta).Result()
	} else {
		result, err = a.client.IncrBy(ctx, prefixedKey, delta).Result()
	}

	if err != nil {
		return 0, err
	}

	// 如果是新创建的键，设置过期时间
	if result == delta {
		if a.isCluster {
			a.clusterClient.Expire(ctx, prefixedKey, a.defaultTTL)
		} else {
			a.client.Expire(ctx, prefixedKey, a.defaultTTL)
		}
	}

	atomic.AddInt64(&a.stats.Sets, 1)

	return result, nil
}

// Decrement 递减计数器
func (a *Adapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return a.Increment(ctx, key, -delta)
}

// Expire 设置键的过期时间
func (a *Adapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	prefixedKey := a.KeyWithPrefix(key)

	if expiration <= 0 {
		expiration = a.defaultTTL
	}

	var result bool
	var err error

	if a.isCluster {
		result, err = a.clusterClient.Expire(ctx, prefixedKey, expiration).Result()
	} else {
		result, err = a.client.Expire(ctx, prefixedKey, expiration).Result()
	}

	if err != nil {
		return err
	}

	if !result {
		return types.ErrNotFound
	}

	return nil
}

// TTL 获取键的剩余生存时间
func (a *Adapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	prefixedKey := a.KeyWithPrefix(key)

	var result time.Duration
	var err error

	if a.isCluster {
		result, err = a.clusterClient.TTL(ctx, prefixedKey).Result()
	} else {
		result, err = a.client.TTL(ctx, prefixedKey).Result()
	}

	if err != nil {
		return 0, err
	}

	return result, nil
}

// Ping 测试与Redis的连接
func (a *Adapter) Ping(ctx context.Context) error {
	if a.isCluster {
		return a.clusterClient.Ping(ctx).Err()
	}
	return a.client.Ping(ctx).Err()
}
