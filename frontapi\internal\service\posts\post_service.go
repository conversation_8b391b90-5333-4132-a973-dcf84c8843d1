package posts

import (
	"context"

	"frontapi/internal/models/posts"
	repo "frontapi/internal/repository/posts"
	"frontapi/internal/service/base"
)

// PostService 帖子服务接口
type PostService interface {
	base.IExtendedService[posts.Post]
	CheckUserLiked(ctx context.Context, postID, userID string) (bool, error)
	GetRecommendPosts(ctx context.Context, condition map[string]interface{}, userID string, orderBy string, pageNo, pageSize int) ([]*posts.Post, int64, error)
}

// postService 帖子服务实现
type postService struct {
	*base.ExtendedService[posts.Post]
	postRepo     repo.PostRepository
	postLikeRepo repo.PostLikeRepository
}

// NewPostService 创建帖子服务实例
func NewPostService(
	postRepo repo.PostRepository,
	postLikeRepo repo.PostLikeRepository,
) PostService {
	return &postService{
		ExtendedService: base.NewExtendedService[posts.Post](postRepo, "post"),
		postRepo:        postRepo,
		postLikeRepo:    postLikeRepo,
	}
}

// CheckUserLiked 检查用户是否点赞
func (s *postService) CheckUserLiked(ctx context.Context, postID, userID string) (bool, error) {
	// 检查用户是否已点赞
	likeCount, err := s.postLikeRepo.Count(ctx, map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	})
	if err != nil {
		return false, err
	}
	return likeCount > 0, nil
}

// GetRecommendPosts 获取推荐帖子
func (s *postService) GetRecommendPosts(ctx context.Context, condition map[string]interface{}, userID string, orderBy string, pageNo, pageSize int) ([]*posts.Post, int64, error) {
	return s.postRepo.GetRecommendPosts(ctx, condition, userID, orderBy, pageNo, pageSize)
}
