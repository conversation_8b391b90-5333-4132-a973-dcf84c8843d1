<template>
  <div 
    ref="playerContainer" 
    class="mini-player"
    :class="{ 'is-playing': isPlaying, 'is-loading': isLoading }"
  >
    <!-- 播放器容器 -->
    <div 
      ref="videoContainer" 
      class="video-container"
      @click="handleVideoClick"
    >
      <!-- 封面图 -->
      <div 
        v-if="!playerInitialized" 
        class="video-poster"
        :style="{ backgroundImage: `url(${poster})` }"
      >
        <div class="play-overlay">
          <div class="play-button" @click.stop="initializePlayer">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div v-if="duration" class="duration-badge">
            {{ formatDuration(duration) }}
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="error-overlay">
        <el-icon class="error-icon"><Warning /></el-icon>
        <span class="error-text">视频加载失败</span>
        <el-button size="small" @click="retryLoad">重试</el-button>
      </div>
    </div>

    <!-- 自定义控制栏 -->
    <VideoControls
      v-if="playerInitialized && !hasError"
      :is-playing="isPlaying"
      :is-muted="isMuted"
      :volume="volume"
      :current-time="currentTime"
      :duration="totalDuration"
      :is-fullscreen="isFullscreen"
      @play="handlePlay"
      @pause="handlePause"
      @mute="handleMute"
      @volume-change="handleVolumeChange"
      @seek="handleSeek"
      @fullscreen="handleFullscreen"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { VideoPlay, Loading, Warning } from '@element-plus/icons-vue'
import VideoControls from './VideoControls.vue'
import { useVideoPlayer } from '../composables/useVideoPlayer'
import { useIntersectionObserver } from '../composables/useIntersectionObserver'

// 定义属性
interface Props {
  src: string
  poster?: string
  duration?: number
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  width?: number | string
  height?: number | string
}

const props = withDefaults(defineProps<Props>(), {
  poster: '',
  duration: 0,
  autoplay: false,
  muted: false,
  loop: false,
  width: '100%',
  height: 240
})

// 定义事件
const emit = defineEmits<{
  'play': []
  'pause': []
  'ended': []
  'error': [error: Error]
  'timeupdate': [currentTime: number]
  'loadedmetadata': [duration: number]
}>()

// 模板引用
const playerContainer = ref<HTMLElement>()
const videoContainer = ref<HTMLElement>()

// 播放器状态
const playerInitialized = ref(false)
const isLoading = ref(false)
const hasError = ref(false)

// 使用播放器Hook
const {
  player,
  isPlaying,
  isMuted,
  volume,
  currentTime,
  totalDuration,
  isFullscreen,
  initPlayer,
  destroyPlayer,
  play,
  pause,
  mute,
  unmute,
  setVolume,
  seek,
  toggleFullscreen
} = useVideoPlayer()

// 使用滚动监听Hook
const { isIntersecting } = useIntersectionObserver(playerContainer, {
  threshold: 0.3, // 当播放器30%可见时才继续播放
  rootMargin: '50px'
})

// 监听可见性变化，自动暂停/恢复播放
watch(isIntersecting, (visible) => {
  if (!playerInitialized.value) return
  
  if (!visible && isPlaying.value) {
    pause()
  }
})

// 监听播放状态变化
watch(isPlaying, (playing) => {
  if (playing) {
    emit('play')
  } else {
    emit('pause')
  }
})

// 监听时间更新
watch(currentTime, (time) => {
  emit('timeupdate', time)
})

// 监听总时长变化
watch(totalDuration, (duration) => {
  if (duration > 0) {
    emit('loadedmetadata', duration)
  }
})

// 初始化播放器
const initializePlayer = async () => {
  if (playerInitialized.value || !videoContainer.value) return

  try {
    isLoading.value = true
    hasError.value = false

    await nextTick()

    const playerConfig = {
      el: videoContainer.value,
      url: props.src,
      poster: props.poster,
      width: props.width,
      height: props.height,
      autoplay: props.autoplay,
      muted: props.muted,
      loop: props.loop,
      playsinline: true,
      controls: false, // 禁用默认控制栏
      fluid: false,
      fitVideoSize: 'fixWidth',
      videoFillMode: 'cover',
      // 禁用一些不需要的插件
      ignores: [
        'controls',
        'progress',
        'time',
        'volume',
        'fullscreen',
        'playbackRate',
        'screenShot',
        'pip',
        'mini'
      ],
      // 事件监听
      onPlay: () => {
        isLoading.value = false
      },
      onPause: () => {
        // 暂停事件处理
      },
      onEnded: () => {
        emit('ended')
      },
      onError: (error: any) => {
        hasError.value = true
        isLoading.value = false
        emit('error', new Error(error.message || '视频播放错误'))
      },
      onLoadedMetadata: () => {
        isLoading.value = false
      }
    }

    await initPlayer(playerConfig)
    playerInitialized.value = true

  } catch (error) {
    console.error('播放器初始化失败:', error)
    hasError.value = true
    isLoading.value = false
    emit('error', error as Error)
  }
}

// 处理视频点击
const handleVideoClick = () => {
  if (!playerInitialized.value) {
    initializePlayer()
  } else if (isPlaying.value) {
    handlePause()
  } else {
    handlePlay()
  }
}

// 播放控制方法
const handlePlay = () => {
  if (!playerInitialized.value) {
    initializePlayer()
  } else {
    play()
  }
}

const handlePause = () => {
  pause()
}

const handleMute = () => {
  if (isMuted.value) {
    unmute()
  } else {
    mute()
  }
}

const handleVolumeChange = (newVolume: number) => {
  setVolume(newVolume)
}

const handleSeek = (time: number) => {
  seek(time)
}

const handleFullscreen = () => {
  toggleFullscreen()
}

// 重试加载
const retryLoad = () => {
  hasError.value = false
  playerInitialized.value = false
  initializePlayer()
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 组件卸载时清理
onUnmounted(() => {
  destroyPlayer()
})

// 暴露方法给父组件
defineExpose({
  play: handlePlay,
  pause: handlePause,
  seek: handleSeek,
  destroy: destroyPlayer,
  isPlaying,
  currentTime,
  totalDuration
})
</script>

<style scoped lang="scss">
.mini-player {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background: #000;
  transition: all 0.3s ease;

  &.is-loading {
    .video-container {
      cursor: wait;
    }
  }

  .video-container {
    position: relative;
    width: 100%;
    height: 240px;
    background: #000;
    cursor: pointer;
    overflow: hidden;

    // sxplayer容器样式
    :deep(.xgplayer) {
      width: 100% !important;
      height: 100% !important;
      border-radius: 0;
      
      .xgplayer-video {
        object-fit: cover;
      }
    }
  }

  .video-poster {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;

    .play-overlay {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
      transition: background 0.3s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }

      .play-button {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #333;
        font-size: 24px;
        transition: all 0.3s ease;
        cursor: pointer;
        backdrop-filter: blur(10px);
      }

      .duration-badge {
        position: absolute;
        bottom: 12px;
        right: 12px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 500;
        backdrop-filter: blur(10px);
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 10;

    .loading-icon {
      font-size: 32px;
      margin-bottom: 8px;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 14px;
      opacity: 0.8;
    }
  }

  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    padding: 20px;
    z-index: 10;

    .error-icon {
      font-size: 32px;
      margin-bottom: 8px;
      color: #f56565;
    }

    .error-text {
      font-size: 14px;
      margin-bottom: 12px;
      opacity: 0.8;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .video-container {
      height: 200px;
    }

    .video-poster {
      .play-overlay {
        .play-button {
          width: 50px;
          height: 50px;
          font-size: 20px;
        }
      }
    }
  }
}
</style> 