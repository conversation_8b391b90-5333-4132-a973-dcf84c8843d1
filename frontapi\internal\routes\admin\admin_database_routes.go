package admin

import (
	"frontapi/internal/admin/sys"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterDatabaseRoutes 注册数据库相关路由
func RegisterDatabaseRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建数据库控制器
	controller := sys.NewDatabaseController(services.DatabaseService)

	// 数据库管理路由组
	databaseGroup := apiGroup.Group("/database", middleware.AuthRequired())

	// 获取数据库表列表
	databaseGroup.Post("/tables", controller.GetTableList)

	// 获取表详情（GET方式）
	databaseGroup.Post("/table/:tableName", controller.GetTableDetail)

	// 获取表详情（POST方式）
	databaseGroup.Post("/table/detail", controller.GetTableDetailByPost)

	// 生成Mock数据
	databaseGroup.Post("/mock/generate", controller.GenerateMockData)

	// 插入Mock数据到数据库
	databaseGroup.Post("/mock/insert", controller.InsertMockData)

	// 获取外键关联数据
	databaseGroup.Post("/foreign-key/data", controller.GetForeignKeyData)
}
