package promotion

import (
	"context"
	model "frontapi/internal/models/promotion"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// InvitationCommissionRepository 邀请佣金记录数据访问接口
type InvitationCommissionRepository interface {
	base.ExtendedRepository[model.InvitationCommission]
	ListByInviterID(ctx context.Context, inviterID string, page, pageSize int) ([]*model.InvitationCommission, int64, error)
	ListByInvitedID(ctx context.Context, invitedID string, page, pageSize int) ([]*model.InvitationCommission, int64, error)
}

type invitationCommissionRepository struct {
	base.ExtendedRepository[model.InvitationCommission]
}

func NewInvitationCommissionRepository(db *gorm.DB) InvitationCommissionRepository {
	return &invitationCommissionRepository{
		ExtendedRepository: base.NewExtendedRepository[model.InvitationCommission](db),
	}
}

func (r *invitationCommissionRepository) ListByInviterID(ctx context.Context, inviterID string, page, pageSize int) ([]*model.InvitationCommission, int64, error) {
	condition := map[string]interface{}{
		"inviter_id": inviterID,
	}
	return r.List(ctx, condition, "created_at DESC", page, pageSize)
}

func (r *invitationCommissionRepository) ListByInvitedID(ctx context.Context, invitedID string, page, pageSize int) ([]*model.InvitationCommission, int64, error) {
	condition := map[string]interface{}{
		"invited_id": invitedID,
	}
	return r.List(ctx, condition, "created_at DESC", page, pageSize)
}
