<!--
  现代化评论项组件
  展示单条评论的所有信息和交互功能
-->
<template>
  <div class="modern-comment-item" :class="{ 'is-reply': isReply }">
    <div class="comment-main">
      <!-- 用户头像 - 使用UserHoverCard -->
      <div class="user-avatar">
        <UserHoverCard
          :user-info="authorUserInfo"
          :avatar-size="isReply ? 28 : 36"
          @click="handleUserClick"
          @follow="handleFollowUser"
          @unfollow="handleUnfollowUser"
        />
      </div>

      <!-- 评论内容区域 -->
      <div class="comment-content">
        <!-- 用户信息行 -->
        <div class="user-info">
          <span class="username">{{ comment.author?.name || comment.userNickname || '匿名用户' }}</span>
          <el-icon v-if="comment.author?.userType === 2" class="verified-badge">
            <CircleCheck />
          </el-icon>
          <span v-if="comment.author?.level" class="user-level">Lv.{{ comment.author.level }}</span>
          <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
        </div>

        <!-- 评论文本内容 - 左对齐 -->
        <div v-if="comment.content" class="comment-text">
          <p v-html="formatContent(comment.content)"></p>
        </div>

        <!-- 媒体内容 -->
        <CommentMediaDisplay
          :images="comment.images"
          :video="comment.video"
          :auto-play="false"
          :max-display="9"
        />

        <!-- 互动按钮行 -->
        <div class="comment-actions">
          <el-button 
            text 
            size="small"
            :class="{ 'active': comment.isLiked }"
            @click="handleLike"
            class="action-btn like-btn"
          >
            <el-icon class="action-icon">
              <Star v-if="!comment.isLiked" />
              <StarFilled v-else />
            </el-icon>
            <span class="action-text">{{ formatNumber(comment.likeCount || 0) || '点赞' }}</span>
          </el-button>

          <el-button 
            text 
            size="small"
            @click="handleReply"
            class="action-btn reply-btn"
          >
            <el-icon class="action-icon"><ChatDotRound /></el-icon>
            <span class="action-text">回复</span>
          </el-button>

          <!-- 更多操作 -->
          <el-dropdown trigger="click" @command="handleCommand" class="more-dropdown">
            <el-button text size="small" class="more-btn">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="report">
                  <el-icon><Warning /></el-icon>
                  举报评论
                </el-dropdown-item>
                <el-dropdown-item command="copy">
                  <el-icon><CopyDocument /></el-icon>
                  复制内容
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>

        <!-- 回复列表 -->
        <div v-if="comment.replies && comment.replies.length > 0" class="replies-section">
          <div class="replies-list">
            <ModernCommentItem
              v-for="reply in displayReplies"
              :key="reply.id"
              :comment="reply"
              :is-reply="true"
              @like="handleReplyLike"
              @reply="handleReplyToReply"
            />
          </div>
          
          <!-- 展开更多回复 -->
          <div v-if="comment.replies.length > maxReplies" class="show-more-replies">
            <el-button text size="small" @click="toggleReplies">
              {{ showAllReplies ? '收起回复' : `展开更多回复 (${comment.replies.length - maxReplies})` }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 回复输入框 -->
    <div v-if="showReplyInput" class="reply-input-section">
      <div class="reply-input-wrapper">
                  <el-input
            v-model="replyContent"
            type="textarea"
            :rows="3"
            :placeholder="`回复 @${comment.author?.name || comment.userNickname}:`"
            class="reply-textarea"
          />
        <div class="reply-actions">
          <el-button size="small" @click="cancelReply">取消</el-button>
          <el-button type="primary" size="small" @click="submitReply" :loading="replyLoading">
            发送回复
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  CircleCheck, 
  Star, 
  StarFilled, 
  ChatDotRound, 
  MoreFilled,
  Warning,
  CopyDocument
} from '@element-plus/icons-vue'
import CommentMediaDisplay from './CommentMediaDisplay.vue'
import { UserHoverCard } from '@/shared/components/UserHoverCard'
import type { Comment } from '@/types/comment'
import type { User } from '@/types/base'
import { formatTime, formatNumber } from '@/utils/format'

interface Props {
  comment: Comment
  isReply?: boolean
  maxReplies?: number
}

const props = withDefaults(defineProps<Props>(), {
  isReply: false,
  maxReplies: 3
})

const emit = defineEmits<{
  like: [commentId: string]
  reply: [commentId: string, content: string]
}>()

// 响应式数据
const showReplyInput = ref(false)
const replyContent = ref('')
const replyLoading = ref(false)
const showAllReplies = ref(false)

// 计算属性
const authorUserInfo = computed(() => {
  const author = props.comment.author
  if (!author) {
    return {
      id: props.comment.userId || '',
      username: props.comment.userNickname || '匿名用户',
      nickname: props.comment.userNickname || '匿名用户',
      avatar: props.comment.userAvatar || '',
      userType: 1,
      isFollowed: false,
      followCount: 0,
      totalVideos: 0,
      totalPosts: 0,
      totalShorts: 0
    }
  }
  
  return {
    id: author.id || props.comment.userId || '',
    username: author.username || props.comment.userNickname || '',
    nickname: author.nickname || props.comment.userNickname || '',
    avatar: author.avatar || props.comment.userAvatar || '',
    bio: author.bio || '',
    userType: author.userType || 1,
    level: author.level || 1,
    isFollowed: author.isFollowed || false,
    followCount: author.followCount || 0,
    totalVideos: author.totalVideos || 0,
    totalPosts: author.totalPosts || 0,
    totalShorts: author.totalShorts || 0
  }
})

const displayReplies = computed(() => {
  if (!props.comment.replies) return []
  if (showAllReplies.value) return props.comment.replies
  return props.comment.replies.slice(0, props.maxReplies)
})

// 事件处理方法
const handleUserClick = (user: User) => {
  // 跳转到用户主页
  console.log('Navigate to user profile:', user.id)
}

const handleFollowUser = (user: User) => {
  // 关注用户
  console.log('Follow user:', user.id)
}

const handleUnfollowUser = (user: User) => {
  // 取消关注用户
  console.log('Unfollow user:', user.id)
}

const handleLike = () => {
  emit('like', props.comment.id)
}

const handleReply = () => {
  showReplyInput.value = !showReplyInput.value
  if (showReplyInput.value) {
    replyContent.value = ''
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'report':
      ElMessage.info('举报功能开发中...')
      break
    case 'copy':
      if (props.comment.content) {
        navigator.clipboard.writeText(props.comment.content)
        ElMessage.success('内容已复制到剪贴板')
      }
      break
  }
}

const cancelReply = () => {
  showReplyInput.value = false
  replyContent.value = ''
}

const submitReply = async () => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  replyLoading.value = true
  try {
    emit('reply', props.comment.id, replyContent.value.trim())
    replyContent.value = ''
    showReplyInput.value = false
    ElMessage.success('回复成功')
  } catch (error) {
    ElMessage.error('回复失败，请重试')
  } finally {
    replyLoading.value = false
  }
}

const handleReplyLike = (commentId: string) => {
  emit('like', commentId)
}

const handleReplyToReply = (commentId: string, content: string) => {
  emit('reply', commentId, content)
}

const toggleReplies = () => {
  showAllReplies.value = !showAllReplies.value
}

// 格式化评论内容（处理@用户、链接等）
const formatContent = (content: string) => {
  if (!content) return ''
  
  // 处理@用户
  let formatted = content.replace(/@(\w+)/g, '<span class="mention">@$1</span>')
  
  // 处理链接
  formatted = formatted.replace(
    /(https?:\/\/[^\s]+)/g,
    '<a href="$1" target="_blank" class="link">$1</a>'
  )
  
  // 处理换行
  formatted = formatted.replace(/\n/g, '<br>')
  
  return formatted
}
</script>

<style scoped lang="scss">
.modern-comment-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .comment-main {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .user-avatar {
      flex-shrink: 0;
    }

    .comment-content {
      flex: 1;
      min-width: 0;

      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        flex-wrap: wrap;

        .username {
          font-weight: 600;
          color: #333;
          font-size: 14px;
        }

        .verified-badge {
          color: #1890ff;
          font-size: 14px;
        }

        .user-level {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 2px 6px;
          border-radius: 8px;
          font-size: 10px;
          font-weight: 500;
        }

        .comment-time {
          color: #999;
          font-size: 12px;
          margin-left: auto;
        }
      }

      .comment-text {
        margin-bottom: 12px;
        text-align: left; // 确保文本左对齐

        p {
          margin: 0;
          line-height: 1.6;
          color: #333;
          font-size: 14px;
          word-wrap: break-word;
          white-space: pre-wrap;

          :deep(.mention) {
            color: #1890ff;
            font-weight: 500;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }

          :deep(.link) {
            color: #1890ff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .comment-actions {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-top: 12px;

        .action-btn {
          padding: 4px 8px;
          border-radius: 6px;
          transition: all 0.2s ease;
          color: #666;

          .action-icon {
            font-size: 14px;
          }

          .action-text {
            font-size: 13px;
            margin-left: 4px;
          }

          &:hover {
            background: #f5f5f5;
            color: #333;
          }

          &.like-btn {
            &.active {
              color: #ff6b6b;

              &:hover {
                background: #fff5f5;
              }
            }
          }

          &.reply-btn:hover {
            color: #1890ff;
            background: #f0f8ff;
          }
        }

        .more-dropdown {
          margin-left: auto;

          .more-btn {
            padding: 4px;
            border-radius: 4px;
            color: #999;

            &:hover {
              background: #f5f5f5;
              color: #666;
            }
          }
        }
      }

      .replies-section {
        margin-top: 16px;
        padding-left: 0;

        .replies-list {
          .modern-comment-item {
            padding: 12px 0;
            border-bottom: 1px solid #f8f8f8;

            &:last-child {
              border-bottom: none;
            }

            .comment-main {
              .user-avatar {
                :deep(.user-hover-trigger) {
                  .user-avatar {
                    width: 28px;
                    height: 28px;
                  }
                }
              }

              .comment-content {
                .user-info {
                  .username {
                    font-size: 13px;
                  }

                  .comment-time {
                    font-size: 11px;
                  }
                }

                .comment-text p {
                  font-size: 13px;
                }
              }
            }
          }
        }

        .show-more-replies {
          margin-top: 8px;
          text-align: left;

          .el-button {
            color: #1890ff;
            font-size: 13px;
          }
        }
      }
    }
  }

  .reply-input-section {
    margin-top: 12px;
    padding-left: 48px;

    .reply-input-wrapper {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 12px;

      .reply-textarea {
        margin-bottom: 12px;

        :deep(.el-textarea__inner) {
          border: none;
          background: white;
          border-radius: 6px;
          resize: none;
        }
      }

      .reply-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-comment-item {
    padding: 12px 0;

    .comment-main {
      gap: 8px;

      .comment-content {
        .user-info {
          .comment-time {
            margin-left: 0;
            order: 10;
            width: 100%;
          }
        }

        .comment-actions {
          gap: 12px;

          .action-btn {
            .action-text {
              display: none;
            }
          }
        }
      }
    }

    .reply-input-section {
      padding-left: 36px;
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .modern-comment-item {
    border-bottom-color: #333;

    .comment-content {
      .user-info {
        .username {
          color: #fff;
        }
      }

      .comment-text p {
        color: #e0e0e0;
      }

      .comment-actions {
        .action-btn {
          color: #ccc;

          &:hover {
            background: #333;
            color: #fff;
          }

          &.like-btn.active {
            color: #ff6b6b;

            &:hover {
              background: #2a1a1a;
            }
          }

          &.reply-btn:hover {
            color: #1890ff;
            background: #1a2332;
          }
        }
      }
    }

    .reply-input-section {
      .reply-input-wrapper {
        background: #2c2c2c;

        :deep(.el-textarea__inner) {
          background: #333;
          color: #fff;
          border-color: #444;
        }
      }
    }
  }
}
</style>