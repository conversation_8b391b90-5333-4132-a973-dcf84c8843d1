<template>
    <div class="nav-language-selector">
        <LanguageSelector custom-class="nav-language" :use-rounded="true" direction="down" panel-position="default"
            :show-text="false" />
    </div>
</template>

<script setup lang="ts">
import LanguageSelector from './index.vue';
</script>

<style scoped lang="scss">
.nav-language-selector {
    position: relative;

    :deep(.nav-language) {
        .language-toggle-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            border: none;
            padding: 0.3rem 0.8rem;
            background-color: transparent;
            color: var(--text-color, var(--text-color-primary, inherit)) !important;
            transition: background-color 0.2s ease;
            position: relative;
            overflow: hidden;
            /* 添加溢出隐藏 */

            /* 限制水波纹大小 */
            :deep(.p-ink) {
                height: 100% !important;
                width: 100% !important;
                top: 0 !important;
                left: 0 !important;
                transform: scale(0.8);
            }

            &:hover {
                background-color: var(--surface-hover, rgba(255, 255, 255, 0.1)) !important;
                color: var(--primary-color) !important;

                .current-language-flag {
                    color: var(--primary-color) !important;
                }
            }
        }

        .language-panel {
            right: 0;
            left: auto;

            // 确保导航栏下拉面板在深色模式下的可见性
            background: var(--surface-overlay, var(--surface-card, #ffffff)) !important;
            border-color: var(--surface-border) !important;
            box-shadow: var(--shadow-lg) !important;

            .language-panel-title {
                color: var(--text-primary, var(--color-primary, #111827)) !important;
            }

            .language-item {
                .language-name {
                    color: var(--text-color, var(--text-color-primary, inherit)) !important;
                }

                .language-english {
                    color: var(--text-color-secondary, var(--text-600)) !important;
                }

                &:hover {
                    background-color: var(--surface-hover, var(--surface-200)) !important;
                }

                &.active {
                    background-color: var(--primary-50, rgba(var(--primary-color-rgb, 24, 100, 171), 0.1)) !important;
                    color: var(--primary-color) !important;
                }
            }
        }
    }
}
</style>