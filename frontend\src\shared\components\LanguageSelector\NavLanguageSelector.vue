<template>
    <div class="nav-language-selector">
        <LanguageSelector custom-class="nav-language" :use-rounded="true" direction="down" panel-position="default"
            :show-text="false" />
    </div>
</template>

<script setup lang="ts">
import LanguageSelector from './index.vue';
</script>

<style scoped lang="scss">
.nav-language-selector {
    position: relative;

    :deep(.nav-language) {
        .language-toggle-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0.3rem 0.8rem;
            border: 1px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            &:active {
                transform: translateY(0);
            }
            
            .language-flag {
                color: white;
            }
        }
    }
}
</style>