<template>
  <div v-if="hasMedia" class="comment-media-display">
    <!-- 媒体内容展示 -->
    <MediaGrid
      :images="images || undefined"
      :video="video || undefined"
      :enable-auto-play="autoPlay"
      :max-display="maxDisplay"
      @media-click="handleMediaClick"
      @video-play="handleVideoPlay"
      @video-pause="handleVideoPause"
      @video-error="handleVideoError"
      @video-click="handleVideoClick"
    />

    <!-- 图片预览组件 -->
    <ImagePreview
      v-model="showImagePreview"
      :images="images || []"
      :initial-index="currentImageIndex"
      @close="closeImagePreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import MediaGrid from '@/shared/components/MediaGrid.vue'
import ImagePreview from '@/shared/components/ImagePreview.vue'
import type { MediaItem } from '@/shared/components/MediaGrid.vue'

interface Props {
  images?: string[] | null
  video?: string | null
  maxDisplay?: number
  showControls?: boolean
  autoPlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxDisplay: 9,
  showControls: false,
  autoPlay: false
})

// 响应式数据
const showImagePreview = ref(false)
const currentImageIndex = ref(0)

// 计算属性
const hasMedia = computed(() => {
  return (props.images && props.images.length > 0) || !!props.video
})

// 事件处理方法
const handleMediaClick = (media: MediaItem, index: number) => {
  if (media.type === 'image') {
    currentImageIndex.value = index
    showImagePreview.value = true
  }
}

const handleVideoPlay = (media: MediaItem) => {
  // 视频播放事件
  console.log('Video playing:', media.url)
}

const handleVideoPause = (media: MediaItem) => {
  // 视频暂停事件
  console.log('Video paused:', media.url)
}

const handleVideoError = (error: Error) => {
  // 视频错误处理
  console.error('Video error:', error)
}

const handleVideoClick = (media: MediaItem) => {
  // 视频点击事件
  console.log('Video clicked:', media.url)
}

const closeImagePreview = () => {
  showImagePreview.value = false
}
</script>

<style scoped lang="scss">
.comment-media-display {
  margin-top: 12px;
  
  // 针对评论中的媒体样式调整
  :deep(.media-grid) {
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .comment-media-display {
   
  }
}
</style>