import type { GeneratedRoute } from '@elegant-router/types';

const routes: GeneratedRoute[] = [
    {
        name: 'videos',
        path: '/videos',
        component: 'layout.base',
        meta: {
            title: 'videos_title',
            i18nKey: 'route.videos_manage',
            icon: 'fluent:globe-video-28-regular',
            order: 3
        },
        children: [
            {
                name: 'videos_category',
                path: '/videos/category',
                component: 'view.videos_category',
                meta: {
                    title: 'videos_category',
                    i18nKey: 'route.videos_category',
                    icon: 'grommet-icons:channel'
                }
            },
            {
                name: 'videos_channel',
                path: '/videos/channel',
                component: 'view.videos_channel',
                meta: {
                    title: 'videos_channel',
                    i18nKey: 'route.videos_channel',
                    icon: 'raphael:video'
                }
            },
            // 视频专辑页面
            {
                name: 'videos_albums',
                path: '/videos/albums',
                component: 'view.videos_albums',
                meta: {
                    title: 'videos_albums',
                    i18nKey: 'route.videos_albums',
                    icon: 'lucide:video-off'
                }
            },
            // 视频列表页面
            {
                name: 'videos_list',
                path: '/videos/list',
                component: 'view.videos_list',
                meta: {
                    title: 'videos_list',
                    i18nKey: 'route.videos_list',
                    icon: 'lucide:list'
                }
            },

            // 视频评论页面
            {
                name: 'videos_comment',
                path: '/videos/comment',
                component: 'view.videos_comment',
                meta: {
                    title: 'videos_comment',
                    i18nKey: 'route.videos_comment',
                    icon: 'lucide:message-circle'
                }
            }
        ]
    }
];

export default routes;
