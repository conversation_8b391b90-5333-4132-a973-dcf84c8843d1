<template>
  <el-dialog
    :model-value="visible"
    :title="form.id ? '编辑分类' : '新增分类'"
    width="500px"
    @close="$emit('close')"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称"></el-input>
      </el-form-item>

      <el-form-item label="编码" prop="code">
        <el-input v-model="form.code" placeholder="请输入分类编码">
          <template #append>
            <el-button @click="form.code=generateCode()">随机生成</el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="分类描述" prop="description">
        <el-input v-model="form.description" placeholder="请输入分类描述" type="textarea" :rows="2"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="sort_order">
        <el-input-number v-model="form.sort_order" :min="maxOrder+1" :max="999"></el-input-number>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';

const props = defineProps<{
  visible: boolean;
  category?: any;
  maxOrder: number;
}>();

const emit = defineEmits(['submit', 'close']);
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 表单数据
const form = ref({
  id: '',
  name: '',
  code: '',
  description: '',
  sort_order: 0,
  status: 1
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入分类编码', trigger: 'blur' }],
  sort_order: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
});

// 监听category变化，更新表单数据
watch(() => props.category, (val) => {
  if (val) {
    form.value = { ...val };
  } else {
    resetForm();
  }
}, { immediate: true });

// 重置表单
function resetForm() {
  form.value = {
    id: '',
    name: '',
    code: '',
    description: '',
    sort_order: 0,
    status: 1
  };
}

// 生成随机编码
function generateCode() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `BC${timestamp}${random}`;
}

// 提交表单
async function submitForm() {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      try {
        emit('submit', { ...form.value });
      } finally {
        submitLoading.value = false;
      }
    }
  });
}
</script>