<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>视频频道管理</h2>
            <div class="buttons">
              <el-button type="primary" :icon="Plus" @click="handleAdd">添加频道</el-button>
              <el-button type="success" :icon="Refresh" @click="fetchChannelList">刷新</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <ChannelSearchBar
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
        @refresh="fetchChannelList"
        class="mb-4"
      />

      <!-- 频道表格 -->
      <ChannelTable
        :channel-list="channelList"
        :loading="loading"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @batch-status="handleBatchStatus"
        @batch-delete="handleBatchDelete"
      />

      <!-- 表单弹窗 -->
      <ChannelFormDialog
        v-model:visible="formDialogVisible"
        :type="formDialogType"
        :channel-data="currentChannelData"
        @submit="handleFormSubmit"
      />

      <!-- 详情弹窗 -->
      <ChannelDetailDialog
        v-model:visible="detailDialogVisible"
        :channel-data="currentChannelData"
        @edit="handleEditFromDetail"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
    addChannel,
    batchDeleteChannel,
    batchUpdateChannelStatus,
    deleteChannel,
    getChannelDetail,
    getChannelList,
    updateChannel,
    updateChannelStatus
} from '@/service/api/videos/videos';
import type { VideoChannelItem, VideoChannelSearchForm } from '@/types/videos';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { nextTick, onMounted, reactive, ref } from 'vue';
import ChannelDetailDialog from './components/ChannelDetailDialog.vue';
import ChannelFormDialog from './components/ChannelFormDialog.vue';
import ChannelSearchBar from './components/ChannelSearchBar.vue';
import ChannelTable from './components/ChannelTable.vue';

// 响应式数据
const loading = ref(false);
const channelList = ref<VideoChannelItem[]>([]);
const selectedChannels = ref<VideoChannelItem[]>([]);

// 搜索表单
const searchForm = ref<VideoChannelSearchForm>({
  keyword: '',
  status: undefined,
  creator_id: undefined,
  is_featured: undefined,
  update_frequency: undefined,
  start_date: '',
  end_date: '',
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
});

// 弹窗控制
const formDialogVisible = ref(false);
const formDialogType = ref<'add' | 'edit'>('add');
const detailDialogVisible = ref(false);
const currentChannelData = ref<VideoChannelItem | null>(null);

// ============ 数据获取 ============

/**
 * 获取频道列表
 */
const fetchChannelList = async () => {
  try {
    loading.value = true;
    
    const params = {
      data: {
        ...searchForm.value,
        // 过滤空值
        keyword: searchForm.value.keyword || undefined,
        status: searchForm.value.status !== undefined ? searchForm.value.status : undefined,
        is_featured: searchForm.value.is_featured !== undefined ? searchForm.value.is_featured : undefined,
        update_frequency: searchForm.value.update_frequency || undefined,
        start_date: searchForm.value.start_date || undefined,
        end_date: searchForm.value.end_date || undefined,
      },
      page: {
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
      },
    };

    const { response, data } = await getChannelList(params) as any;
    console.log(response,data);
    if (response.status === 200&&response.data.code==2000) {
      channelList.value = data.list || [];
      pagination.total = data.total || 0;
    } else {
      ElMessage.error(response?.message || '获取频道列表失败');
    }
  } catch (error) {
    console.error('获取频道列表失败:', error);
    ElMessage.error('获取频道列表失败');
  } finally {
    loading.value = false;
  }
};

/**
 * 获取频道详情
 */
const fetchChannelDetail = async (id: string) => {
  try {
    const { response, data } = await getChannelDetail(id) as any;
    
    if (response.status === 200&&response.data.code==2000) {
      return data;
    } else {
      ElMessage.error(response?.message || '获取频道详情失败');
      return null;
    }
  } catch (error) {
    console.error('获取频道详情失败:', error);
    ElMessage.error('获取频道详情失败');
    return null;
  }
};

// ============ 搜索操作 ============

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.page = 1;
  fetchChannelList();
};

/**
 * 处理重置
 */
const handleReset = () => {
  pagination.page = 1;
  nextTick(() => {
    fetchChannelList();
  });
};

// ============ 表格操作 ============

/**
 * 处理选择变化
 */
const handleSelectionChange = (selection: VideoChannelItem[]) => {
  selectedChannels.value = selection;
};

/**
 * 查看频道详情
 */
const handleView = async (row: VideoChannelItem) => {
  const detailData = await fetchChannelDetail(row.id);
  if (detailData) {
    currentChannelData.value = detailData;
    detailDialogVisible.value = true;
  }
};

/**
 * 编辑频道
 */
const handleEdit = async (row: VideoChannelItem) => {
  const detailData = await fetchChannelDetail(row.id);
  if (detailData) {
    currentChannelData.value = detailData;
    formDialogType.value = 'edit';
    formDialogVisible.value = true;
  }
};

/**
 * 从详情弹窗编辑
 */
const handleEditFromDetail = (data: VideoChannelItem) => {
  currentChannelData.value = data;
  formDialogType.value = 'edit';
  formDialogVisible.value = true;
};

/**
 * 删除频道
 */
const handleDelete = async (row: VideoChannelItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除频道 "${row.name}" 吗？此操作不可恢复！`,
      '删除确认',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
      }
    );

    const { response } = await deleteChannel(row.id) as any;
    
    if (response.status === 200&&response.data.code==2000) {
      ElMessage.success('删除频道成功');
      fetchChannelList();
    } else {
      ElMessage.error(response?.message || '删除频道失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除频道失败:', error);
      ElMessage.error('删除频道失败');
    }
  }
};

/**
 * 更改状态
 */
const handleChangeStatus = async (id: string, status: number) => {
  try {
    const { response } = await updateChannelStatus({ id, status }) as any;
    
    if (response.status === 200&&response.data.code==2000) {
      ElMessage.success('更新频道状态成功');
      fetchChannelList();
    } else {
      ElMessage.error(response?.message || '更新频道状态失败');
    }
  } catch (error) {
    console.error('更新频道状态失败:', error);
    ElMessage.error('更新频道状态失败');
  }
};

// ============ 批量操作 ============

/**
 * 批量更新状态
 */
const handleBatchStatus = async (status: number, channels: VideoChannelItem[]) => {
  try {
    const ids = channels.map(item => item.id);
    const statusText = status === 1 ? '启用' : '禁用';
    
    await ElMessageBox.confirm(
      `确定要批量${statusText} ${channels.length} 个频道吗？`,
      '批量操作确认',
      {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }
    );

    const { response } = await batchUpdateChannelStatus({ ids, status }) as any;
    
    if (response.status === 200&&response.data.code==2000) {
      ElMessage.success(`批量${statusText}频道成功`);
      fetchChannelList();
    } else {
      ElMessage.error(response?.message || `批量${statusText}频道失败`);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新状态失败:', error);
      ElMessage.error('批量更新状态失败');
    }
  }
};

/**
 * 批量删除
 */
const handleBatchDelete = async (channels: VideoChannelItem[]) => {
  try {
    const ids = channels.map(item => item.id);
    
    await ElMessageBox.confirm(
      `确定要批量删除 ${channels.length} 个频道吗？此操作不可恢复！`,
      '批量删除确认',
      {
        type: 'error',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
      }
    );

    const { response } = await batchDeleteChannel({ ids }) as any;
    
    if (response.status === 200&&response.data.code==2000) {
      ElMessage.success('批量删除频道成功');
      fetchChannelList();
    } else {
      ElMessage.error(response?.message || '批量删除频道失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
};

// ============ 分页操作 ============

/**
 * 页码改变
 */
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  fetchChannelList();
};

/**
 * 页大小改变
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchChannelList();
};

// ============ 表单操作 ============

/**
 * 添加频道
 */
const handleAdd = () => {
  currentChannelData.value = null;
  formDialogType.value = 'add';
  formDialogVisible.value = true;
};

/**
 * 表单提交
 */
const handleFormSubmit = async (data: any, type: 'add' | 'edit') => {
  try {
    let response: any;
    
    if (type === 'add') {
      const result = await addChannel(data) as any;
      response = result.response;
    } else {
      const result = await updateChannel(data) as any;
      response = result.response;
    }
    
    if (response.status === 200&&response.data.code==2000) {
      ElMessage.success(type === 'add' ? '添加频道成功' : '更新频道成功');
      formDialogVisible.value = false;
      fetchChannelList();
    } else {
      ElMessage.error(response?.message || (type === 'add' ? '添加频道失败' : '更新频道失败'));
    }
  } catch (error) {
    console.error('表单提交失败:', error);
    ElMessage.error(type === 'add' ? '添加频道失败' : '更新频道失败');
  }
};

// ============ 生命周期 ============

onMounted(() => {
  fetchChannelList();
});
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;

  :deep(.el-card) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .el-card__header {
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafafa;

      .filter-container {
        .title-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #333;
          }

          .buttons {
            display: flex;
            gap: 12px;
          }
        }
      }
    }

    .el-card__body {
      padding: 20px;
    }
  }

  .mb-4 {
    margin-bottom: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 16px;

    :deep(.el-card__header) {
      .filter-container {
        .title-container {
          flex-direction: column;
          gap: 16px;
          align-items: stretch;

          .buttons {
            justify-content: center;
          }
        }
      }
    }
  }
}
</style> 