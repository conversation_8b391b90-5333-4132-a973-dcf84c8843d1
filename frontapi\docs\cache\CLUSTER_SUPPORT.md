# 缓存系统集群支持

本文档描述了缓存系统的集群和分片支持功能。

## 架构概述

缓存系统采用了模块化的设计，通过适配器模式支持多种缓存实现，并提供了集群和分片的能力。系统主要包含以下组件：

1. **缓存适配器**：提供统一的缓存操作接口
2. **集群适配器**：支持多节点集群和一致性哈希
3. **分片适配器**：支持数据分片和负载均衡
4. **分片管理器**：管理分片的创建、删除和重新平衡

## 集群支持

集群适配器 (`cluster.Adapter`) 实现了以下功能：

- **一致性哈希**：使用一致性哈希算法将数据分布到多个节点上，减少节点变更时的数据迁移
- **虚拟节点**：每个物理节点对应多个虚拟节点，提高数据分布的均匀性
- **节点管理**：支持动态添加和移除节点
- **故障转移**：当节点故障时，可以将请求转发到其他节点

### 集群配置示例

```go
// 创建多个缓存适配器作为集群节点
redisAdapter1, _ := redis.New(&redis.Config{
    Host: "redis1.example.com",
    Port: 6379,
})

redisAdapter2, _ := redis.New(&redis.Config{
    Host: "redis2.example.com",
    Port: 6379,
})

// 创建集群配置
clusterConfig := &cluster.Config{
    Nodes:         []cache.CacheAdapter{redisAdapter1, redisAdapter2},
    VirtualNodes:  100,
    HashFunction:  "fnv",
}

// 创建集群适配器
clusterAdapter, _ := cluster.New(clusterConfig)

// 使用集群适配器
clusterAdapter.Set(ctx, "key", []byte("value"), time.Minute)
```

## 分片支持

分片适配器 (`sharding.Adapter`) 和分片管理器 (`sharding.ShardManager`) 实现了以下功能：

- **数据分片**：将数据分散到多个分片中，提高系统容量和性能
- **分片策略**：支持哈希分片、范围分片和一致性哈希分片
- **读写策略**：支持从主分片读取、从任意副本读取、写入所有副本或写入法定数量副本
- **分片管理**：支持动态添加和移除分片，以及分片的重新平衡

### 分片配置示例

```go
// 创建多个缓存适配器作为分片
memoryAdapter1, _ := memory.New(&memory.Config{
    CleanupInterval: time.Minute,
})

memoryAdapter2, _ := memory.New(&memory.Config{
    CleanupInterval: time.Minute,
})

// 创建分片配置
shardConfig := &sharding.Config{
    Shards:           []cache.CacheAdapter{memoryAdapter1, memoryAdapter2},
    ShardingStrategy: "hash",
}

// 创建分片适配器
shardAdapter, _ := sharding.New(shardConfig)

// 使用分片适配器
shardAdapter.Set(ctx, "key", []byte("value"), time.Minute)
```

## 高级分片管理

分片管理器提供了更高级的分片管理功能：

```go
// 创建分片管理器配置
shardManagerConfig := &sharding.ShardConfig{
    ShardCount:       4,
    ReplicaFactor:    2,
    ShardingStrategy: "consistent_hash",
    ReadStrategy:     "any",
    WriteStrategy:    "quorum",
    FailoverEnabled:  true,
}

// 创建分片管理器
shardManager, _ := sharding.NewShardManager(shardManagerConfig)

// 注册缓存适配器到分片
redisAdapter1, _ := redis.New(&redis.Config{Host: "redis1.example.com", Port: 6379})
redisAdapter2, _ := redis.New(&redis.Config{Host: "redis2.example.com", Port: 6379})

shardManager.RegisterAdapter(0, "node1", redisAdapter1, true)  // 主节点
shardManager.RegisterAdapter(0, "node2", redisAdapter2, false) // 副本节点

// 使用分片管理器
shardManager.Set(ctx, "key", []byte("value"), time.Minute)
```

## 扩展性和可靠性

缓存系统的集群和分片支持提供了以下优势：

1. **水平扩展**：可以通过添加更多节点来增加系统容量
2. **高可用性**：通过副本和故障转移机制提高系统可用性
3. **负载均衡**：通过一致性哈希和分片策略实现负载均衡
4. **灵活配置**：可以根据需求配置不同的分片策略和读写策略

## 未来改进

未来可以考虑以下改进：

1. **自动分片重新平衡**：当节点变更时，自动重新平衡分片
2. **监控和指标**：提供更详细的监控和性能指标
3. **智能分片策略**：基于访问模式和数据大小的智能分片策略
4. **跨数据中心复制**：支持跨数据中心的数据复制和同步 