<template>
  <el-dialog
    title="分类详情"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    width="600px"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="ID">{{ categoryData?.id }}</el-descriptions-item>
      <el-descriptions-item label="分类名称">{{ categoryData?.name }}</el-descriptions-item>
      <el-descriptions-item label="分类编码">{{ categoryData?.code }}</el-descriptions-item>
      <el-descriptions-item label="父级分类">
        {{ categoryData?.parent_id ? getParentName(categoryData.parent_id) : '无' }}
      </el-descriptions-item>
      <el-descriptions-item label="排序">{{ categoryData?.sort_order }}</el-descriptions-item>
      <el-descriptions-item label="URI标识">{{ categoryData?.uri || '-' }}</el-descriptions-item>
      <el-descriptions-item label="图标">
        <div class="image-container">
          <span v-if="!categoryData?.icon">-</span>
          <div v-else>
            <el-image
              :src="categoryData.icon"
              style="width: 30px; height: 30px;"
              :preview-src-list="[categoryData.icon]"
            />
            <div class="image-url">{{ categoryData.icon }}</div>
          </div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <CategoryStatusTag :status="categoryData?.status || 0" />
      </el-descriptions-item>
      <el-descriptions-item label="描述">{{ categoryData?.description || '-' }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ formatDateTime(categoryData?.created_at) }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{ formatDateTime(categoryData?.updated_at) }}</el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { ShortVideoCategory } from '@/types/shortvideos';
import dayjs from 'dayjs';
import { defineEmits, defineProps } from 'vue';
import CategoryStatusTag from './CategoryStatusTag.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  categoryData: {
    type: Object as () => ShortVideoCategory | null,
    default: null
  },
  parentCategories: {
    type: Map as () => Map<string, string>,
    default: () => new Map()
  }
});

const emit = defineEmits(['update:visible', 'edit']);

// 获取父分类名称
const getParentName = (parentId: string) => {
  if (props.parentCategories && props.parentCategories.has(parentId)) {
    return props.parentCategories.get(parentId);
  }
  return parentId;
};

// 格式化时间
const formatDateTime = (datetime?: string) => {
  if (!datetime) return '-';
  return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 编辑处理
const handleEdit = () => {
  if (props.categoryData) {
    emit('edit', props.categoryData);
    emit('update:visible', false);
  }
};
</script>

<style scoped lang="scss">
.dialog-footer {
  padding-top: 16px;
  text-align: right;
}

.image-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.image-url {
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}
</style> 