import { ref } from 'vue';
import type { TableDetailResponse } from '@/types/database';

export function useMockRules() {
  const mockRules = ref<Record<string, string>>({});
  const excludedFKs = ref<string[]>([]);
  const excludedColumns = ref<string[]>([]);

  // 自动设置Mock规则
  const autoSetMockRules = (tableDetail: TableDetailResponse | null) => {
    if (!tableDetail?.columns || !Array.isArray(tableDetail.columns)) {
      return;
    }

    const newRules: Record<string, string> = {};

    tableDetail.columns.forEach((column: any) => {
      if (!column?.column_name) return;
      
      const columnName = column.column_name.toLowerCase();
      
      // 根据字段名自动设置Mock规则
      if (columnName.includes('email')) {
        newRules[column.column_name] = '@email';
      } else if (columnName.includes('phone')) {
        newRules[column.column_name] = '@phone';
      } else if (columnName.includes('name') && !columnName.includes('_name')) {
        newRules[column.column_name] = '@name';
      } else if (columnName.includes('url')) {
        newRules[column.column_name] = '@url';
      } else if (columnName.includes('ip')) {
        newRules[column.column_name] = '@ip';
      } else if (column.data_type === 'datetime' || column.data_type === 'timestamp') {
        newRules[column.column_name] = '@datetime';
      } else if (column.data_type === 'date') {
        newRules[column.column_name] = '@date';
      } else if (column.data_type === 'time') {
        newRules[column.column_name] = '@time';
      } else if (columnName.includes('uuid') || columnName.includes('guid')) {
        newRules[column.column_name] = '@uuid';
      } else if (columnName.includes('status') && column.data_type === 'tinyint') {
        newRules[column.column_name] = '@enum(0,1)';
      } else if (columnName.includes('gender') || columnName.includes('sex')) {
        newRules[column.column_name] = '@enum(0,1,2)';
      }
    });

    mockRules.value = newRules;
    console.log('自动设置的Mock规则:', newRules);
  };

  // 更新Mock规则
  const updateMockRules = (newRules: Record<string, string>) => {
    mockRules.value = { ...newRules };
  };

  // 切换外键包含/排除状态
  const toggleForeignKey = (constraintName: string) => {
    const index = excludedFKs.value.indexOf(constraintName);
    if (index > -1) {
      excludedFKs.value.splice(index, 1);
    } else {
      excludedFKs.value.push(constraintName);
    }
    console.log('外键排除列表:', excludedFKs.value);
  };

  // 切换列排除状态
  const toggleColumnExclusion = (columnName: string) => {
    const index = excludedColumns.value.indexOf(columnName);
    if (index > -1) {
      excludedColumns.value.splice(index, 1);
    } else {
      excludedColumns.value.push(columnName);
    }
    console.log('排除的列:', excludedColumns.value);
  };

  // 重置规则
  const resetRules = () => {
    mockRules.value = {};
    excludedFKs.value = [];
    excludedColumns.value = [];
  };

  return {
    mockRules,
    excludedFKs,
    excludedColumns,
    autoSetMockRules,
    updateMockRules,
    toggleForeignKey,
    toggleColumnExclusion,
    resetRules
  };
} 