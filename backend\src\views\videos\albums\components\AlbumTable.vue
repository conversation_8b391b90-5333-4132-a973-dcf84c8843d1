<template>
  <div class="album-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量禁用
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量启用
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 - 使用SlinkyTable -->
    <div class="table-content">
      <SlinkyTable
        :data="albumList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="编辑"
        delete-text="删除"
        empty-text="暂无专辑数据"
        class="album-data-table"
      >
        <!-- 专辑信息列 -->
        <el-table-column prop="title" label="专辑信息" align="left" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="album-info">
              <el-avatar
                :size="36"
                :src="row.cover"
                :alt="row.title"
                class="album-avatar"
              >
                <el-icon><Collection /></el-icon>
              </el-avatar>
              <div class="album-details">
                <div class="album-name">{{ row.title }}</div>
                <div class="album-description">{{ row.description || '未设置描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 创建者信息列 -->
        <el-table-column prop="user_nickname" label="创建者" align="left" min-width="140">
          <template #default="{ row }">
            <div class="creator-info">
              <el-avatar
                :size="24"
                :src="row.user_avatar"
                :alt="row.user_nickname"
                class="creator-avatar"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="creator-name">{{ row.user_nickname || '未知用户' }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 分类列 -->
        <el-table-column prop="category_name" label="分类" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.category_name" type="info" size="small" effect="light">
              {{ row.category_name }}
            </el-tag>
            <span v-else class="placeholder-text">未分类</span>
          </template>
        </el-table-column>

        <!-- 统计信息列 -->
        <el-table-column label="统计" min-width="140" align="center">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon class="stat-icon"><VideoCamera /></el-icon>
                <span class="stat-value">{{ row.video_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><View /></el-icon>
                <span class="stat-value">{{ formatNumber(row.view_count || 0) }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><StarFilled /></el-icon>
                <span class="stat-value">{{ row.heat || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 推荐状态列 -->
        <el-table-column prop="is_featured" label="推荐" width="80" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.is_featured === 1 ? 'success' : 'info'" 
              size="small" 
              effect="light"
            >
              {{ row.is_featured === 1 ? '推荐' : '普通' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <AlbumStatusTag :status="row.status" />
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ formatDate(row.created_at) }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-popconfirm
              :title="`确定要${row.status === 1 ? '禁用' : '启用'}专辑 ${row.title} 吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  link 
                  size="small"
                >
                  <el-icon>
                    <component :is="row.status === 1 ? Lock : Unlock" />
                  </el-icon>
                  {{ row.status === 1 ? '禁用' : row.status === 0 ? '启用' : '已删除' }}
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              :title="`确定要删除专辑 ${row.title} 吗？此操作不可恢复！`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页组件 -->
    <div class="table-footer">
      <SinglePager
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SinglePager from '@/components/themes/slinky/pager/SinglePager.vue';
import SlinkyTable from '@/components/themes/slinky/tables/SlinkyTable.vue';
import type { VideoAlbum } from '@/types/videoAlbum';
import {
    Calendar,
    Check,
    Collection,
    Delete, Edit, Lock,
    StarFilled,
    Unlock,
    User,
    VideoCamera,
    View
} from '@element-plus/icons-vue';
import { ref } from 'vue';
import AlbumStatusTag from '../../../../components/common/AlbumStatusTag.vue';
// 格式化函数
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

// Props定义
interface Props {
  albumList: VideoAlbum[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'selection-change': [selection: VideoAlbum[]];
  'view': [row: VideoAlbum];
  'edit': [row: VideoAlbum];
  'delete': [row: VideoAlbum];
  'change-status': [id: string, status: number];
  'current-change': [page: number];
  'size-change': [size: number];
  'batch-status': [status: number, albums: VideoAlbum[]];
  'batch-delete': [albums: VideoAlbum[]];
}

const emit = defineEmits<Emits>();

// 选中的行
const selectedRows = ref<VideoAlbum[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: VideoAlbum[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

// 查看
const handleView = (row: VideoAlbum) => {
  emit('view', row);
};

// 编辑
const handleEdit = (row: VideoAlbum) => {
  emit('edit', row);
};

// 删除
const handleDelete = (row: VideoAlbum) => {
  emit('delete', row);
};

// 状态变化
const handleChangeStatus = (id: string, status: number) => {
  emit('change-status', id, status);
};

// 分页变化
const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 批量状态更新
const handleBatchStatus = (status: number) => {
  emit('batch-status', status, selectedRows.value);
};

// 批量删除
const handleBatchDelete = () => {
  emit('batch-delete', selectedRows.value);
};
</script>

<style scoped lang="scss">
.album-table-container {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-weight: 500;

      strong {
        color: #1565c0;
      }
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-content {
    background: white;
    border-radius: 4px;
    overflow: hidden;
  }

  .album-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 4px 0;

    .album-avatar {
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .album-details {
      min-width: 0;
      flex: 1;

      .album-name {
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        margin-bottom: 2px;
      }

      .album-description {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .creator-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .creator-avatar {
      flex-shrink: 0;
    }

    .creator-name {
      font-size: 12px;
      color: #666;
    }
  }

  .stats-info {
    display: flex;
    gap: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .stat-icon {
        color: #999;
        font-size: 14px;
      }

      .stat-value {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .time-icon {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }

    .time-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }

  .action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      
      .el-icon {
        margin-right: 2px;
      }
    }
  }

  .placeholder-text {
    color: #999;
    font-size: 12px;
    font-style: italic;
  }

  .table-footer {
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .album-table-container {
    .action-buttons-group {
      flex-direction: column;
      gap: 4px;
      align-items: stretch;
    }
  }
}

@media (max-width: 768px) {
  .album-table-container {
    .batch-toolbar {
      flex-direction: column;
      gap: 12px;
      text-align: center;

      .batch-actions {
        justify-content: center;
      }
    }

    .table-footer {
      justify-content: center;
    }
  }
}
</style> 