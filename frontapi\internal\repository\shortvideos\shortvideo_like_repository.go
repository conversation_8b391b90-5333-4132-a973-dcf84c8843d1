package shortvideos

import (
	"context"
	"frontapi/internal/models/shortvideos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ShortVideoLikeRepository 短视频点赞数据访问接口
type ShortVideoLikeRepository interface {
	base.ExtendedRepository[shortvideos.ShortVideoLike]
	CheckUserLiked(ctx context.Context, userID string, shortVideoID string) (bool, error)
}

// shortVideoLikeRepository 短视频点赞数据访问实现
type shortVideoLikeRepository struct {
	base.ExtendedRepository[shortvideos.ShortVideoLike]
}

// NewShortVideoLikeRepository 创建短视频点赞仓库实例
func NewShortVideoLikeRepository(db *gorm.DB) ShortVideoLikeRepository {
	return &shortVideoLikeRepository{
		ExtendedRepository: base.NewExtendedRepository[shortvideos.ShortVideoLike](db),
	}
}

// CheckUserLiked 检查用户是否点赞了短视频
func (r *shortVideoLikeRepository) CheckUserLiked(ctx context.Context, userID string, shortVideoID string) (bool, error) {
	var count int64
	err := r.GetDBWithContext(ctx).Model(&shortvideos.ShortVideoLike{}).
		Where("user_id = ? AND short_id = ?", userID, shortVideoID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
