package pictures

import (
	"fmt"

	"gorm.io/gorm"

	"frontapi/internal/models/pictures"
	"frontapi/internal/repository/base"
)

// PictureRepository 图片数据访问接口
type PictureRepository interface {
	base.ExtendedRepository[pictures.Picture]
}

// pictureRepository 图片数据访问实现
type pictureRepository struct {
	base.ExtendedRepository[pictures.Picture]
}

// NewPictureRepository 创建图片仓库实例
func NewPictureRepository(db *gorm.DB) PictureRepository {
	return &pictureRepository{
		ExtendedRepository: base.NewExtendedRepository[pictures.Picture](db),
	}
}

// 重写applyConditions以支持图片特定的搜索条件
func (r *pictureRepository) applyPictureConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	if condition == nil {
		return query
	}

	for key, value := range condition {
		if value == nil || value == "" {
			continue
		}

		switch key {
		case "title":
			if title, ok := value.(string); ok {
				query = query.Where("title LIKE ?", "%"+title+"%")
			}
		case "category_id":
			query = query.Where("category_id = ?", value)
		case "category_name":
			if categoryName, ok := value.(string); ok {
				query = query.Where("category_name LIKE ?", "%"+categoryName+"%")
			}
		case "album_id":
			query = query.Where("album_id = ?", value)
		case "creator_id":
			query = query.Where("creator_id = ?", value)
		case "status":
			if status, ok := value.(int); ok && status > -999 {
				query = query.Where("status = ?", status)
			}
		case "keyword":
			// 关键字搜索：标题、描述、标签
			if keyword, ok := value.(string); ok {
				query = query.Where(
					"title LIKE ? OR description LIKE ? OR JSON_CONTAINS(tags_json, JSON_QUOTE(?))",
					"%"+keyword+"%", "%"+keyword+"%", keyword,
				)
			}
		case "search_fields":
			// 自定义搜索字段，配合keyword使用
			if fields, ok := value.([]string); ok {
				if keywordValue, exists := condition["keyword"]; exists {
					if keyword, ok := keywordValue.(string); ok {
						conditions := make([]string, len(fields))
						values := make([]interface{}, len(fields))
						for i, field := range fields {
							conditions[i] = fmt.Sprintf("%s LIKE ?", field)
							values[i] = "%" + keyword + "%"
						}
						if len(conditions) > 0 {
							query = query.Where(fmt.Sprintf("(%s)", conditions[0]), values[0])
							for i := 1; i < len(conditions); i++ {
								query = query.Or(conditions[i], values[i])
							}
						}
					}
				}
			}
		case "created_at_start":
			query = query.Where("created_at >= ?", value)
		case "created_at_end":
			query = query.Where("created_at <= ?", value)
		case "updated_at_start":
			query = query.Where("updated_at >= ?", value)
		case "updated_at_end":
			query = query.Where("updated_at <= ?", value)
		case "view_count_min":
			query = query.Where("view_count >= ?", value)
		case "view_count_max":
			query = query.Where("view_count <= ?", value)
		case "like_count_min":
			query = query.Where("like_count >= ?", value)
		case "like_count_max":
			query = query.Where("like_count <= ?", value)
		}
	}

	return query
}
