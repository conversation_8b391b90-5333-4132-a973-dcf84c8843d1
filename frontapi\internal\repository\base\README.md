# 通用仓库基础接口使用指南

## 概述

本包提供了三个层次的通用仓库接口，用于减少CRUD操作的重复代码：

1. **BaseRepository** - 基础CRUD操作
2. **ExtendedRepository** - 扩展业务操作
3. **SearchRepository** - 搜索和推荐功能

## 接口层次结构

```
BaseRepository[T]
    ↓ 继承
ExtendedRepository[T]
    ↓ 继承
SearchRepository[T]
```

## 使用方式

### 1. 基础使用 - BaseRepository

适用于简单的CRUD操作：

```go
package users

import (
    "frontapi/internal/models/users"
    "frontapi/internal/repository/base"
    "gorm.io/gorm"
)

type UserRepository interface {
    base.BaseRepository[users.User]
    // 添加业务特定方法
    FindByEmail(ctx context.Context, email string) (*users.User, error)
}

type userRepository struct {
    base.BaseRepository[users.User]
}

func NewUserRepository(db *gorm.DB) UserRepository {
    return &userRepository{
        BaseRepository: base.NewBaseRepository[users.User](db),
    }
}

func (r *userRepository) FindByEmail(ctx context.Context, email string) (*users.User, error) {
    condition := map[string]interface{}{"email": email}
    return r.FindOneByCondition(ctx, condition)
}
```

### 2. 扩展使用 - ExtendedRepository

适用于需要计数更新、状态管理等操作：

```go
package posts

import (
    "frontapi/internal/models/posts"
    "frontapi/internal/repository/base"
    "gorm.io/gorm"
)

type PostRepository interface {
    base.ExtendedRepository[posts.Post]
    // 业务特定方法
    GetUserPosts(ctx context.Context, userID string, page, pageSize int) ([]*posts.Post, int64, error)
}

type postRepository struct {
    base.ExtendedRepository[posts.Post]
}

func NewPostRepository(db *gorm.DB) PostRepository {
    return &postRepository{
        ExtendedRepository: base.NewExtendedRepository[posts.Post](db),
    }
}

func (r *postRepository) GetUserPosts(ctx context.Context, userID string, page, pageSize int) ([]*posts.Post, int64, error) {
    condition := map[string]interface{}{
        "author_id": userID,
        "status": 1,
    }
    return r.List(ctx, condition, "created_at DESC", page, pageSize)
}
```

### 3. 完整使用 - SearchRepository

适用于需要搜索、推荐等复杂功能：

```go
package videos

import (
    "frontapi/internal/models/videos"
    "frontapi/internal/repository/base"
    "gorm.io/gorm"
)

type VideoRepository interface {
    base.SearchRepository[videos.Video]
    // 业务特定方法
    GetVideosByCelebrity(ctx context.Context, celebrityID string, page, pageSize int) ([]*videos.Video, int64, error)
}

type videoRepository struct {
    base.SearchRepository[videos.Video]
}

func NewVideoRepository(db *gorm.DB) VideoRepository {
    return &videoRepository{
        SearchRepository: base.NewSearchRepository[videos.Video](db),
    }
}

func (r *videoRepository) GetVideosByCelebrity(ctx context.Context, celebrityID string, page, pageSize int) ([]*videos.Video, int64, error) {
    condition := map[string]interface{}{
        "celebrity_id": celebrityID,
        "status": 1,
    }
    return r.List(ctx, condition, "created_at DESC", page, pageSize)
}
```

## 可用方法

### BaseRepository 方法

```go
// 基础CRUD
Create(ctx context.Context, entity *T) error
FindByID(ctx context.Context, id string) (*T, error)
Update(ctx context.Context, entity *T) error
Delete(ctx context.Context, id string) error
BatchDelete(ctx context.Context, ids []string) error

// 列表查询
List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)
FindAll(ctx context.Context) ([]*T, error)

// 条件查询
FindByCondition(ctx context.Context, condition map[string]interface{}) ([]*T, error)
FindOneByCondition(ctx context.Context, condition map[string]interface{}) (*T, error)

// 计数操作
Count(ctx context.Context, condition map[string]interface{}) (int64, error)
Exists(ctx context.Context, condition map[string]interface{}) (bool, error)

// 批量操作
BatchCreate(ctx context.Context, entities []*T) error
BatchUpdate(ctx context.Context, entities []*T) error

// 数据库访问
GetDB() *gorm.DB
GetDBWithContext(ctx context.Context) *gorm.DB
```

### ExtendedRepository 额外方法

```go
// 计数更新
UpdateCount(ctx context.Context, id string, field string, increment int64) error
UpdateViewCount(ctx context.Context, id string) error
UpdateLikeCount(ctx context.Context, id string, increment int) error
UpdateCommentCount(ctx context.Context, id string, increment int) error

// 状态管理
UpdateStatus(ctx context.Context, id string, status int) error
BatchUpdateStatus(ctx context.Context, ids []string, status int) error
SoftDelete(ctx context.Context, id string) error
BatchSoftDelete(ctx context.Context, ids []string) error
Restore(ctx context.Context, id string) error

// 其他操作
UpdateSortOrder(ctx context.Context, id string, sortOrder int) error
SetFeatured(ctx context.Context, id string, featured bool) error
BatchSetFeatured(ctx context.Context, ids []string, featured bool) error

// 预加载查询
ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)

// 原生SQL
ExecuteRaw(ctx context.Context, sql string, values ...interface{}) error
QueryRaw(ctx context.Context, dest interface{}, sql string, values ...interface{}) error
```

### SearchRepository 额外方法

```go
// 搜索功能
FullTextSearch(ctx context.Context, keyword string, fields []string, page, pageSize int) ([]*T, int64, error)
FuzzySearch(ctx context.Context, keyword string, fields []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)
SearchByTags(ctx context.Context, tags []string, page, pageSize int) ([]*T, int64, error)
SearchByCategory(ctx context.Context, categoryID string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)

// 推荐和热门
GetRecommended(ctx context.Context, userID string, page, pageSize int) ([]*T, int64, error)
GetPopular(ctx context.Context, timeRange string, page, pageSize int) ([]*T, int64, error)
```

## 查询条件支持

基础接口支持以下通用查询条件：

```go
condition := map[string]interface{}{
    "status": 1,                    // 状态筛选
    "created_at_start": "2024-01-01", // 创建时间开始
    "created_at_end": "2024-12-31",   // 创建时间结束
    "updated_at_start": "2024-01-01", // 更新时间开始
    "updated_at_end": "2024-12-31",   // 更新时间结束
    // 其他字段会自动进行等值查询
    "category_id": "123",
    "user_id": "456",
}
```

## 自定义查询条件

如果需要支持特定的查询条件（如模糊搜索、范围查询等），可以重写 `List` 方法：

```go
func (r *pictureRepository) List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.Picture, int64, error) {
    var entities []*pictures.Picture
    var total int64
    
    query := r.GetDBWithContext(ctx).Model(&pictures.Picture{})
    
    // 应用自定义条件
    query = r.applyCustomConditions(query, condition)
    
    // 获取总数
    err := query.Count(&total).Error
    if err != nil {
        return nil, 0, err
    }
    
    // 应用排序和分页
    if orderBy != "" {
        query = query.Order(orderBy)
    }
    
    if page > 0 && pageSize > 0 {
        offset := (page - 1) * pageSize
        query = query.Offset(offset).Limit(pageSize)
    }
    
    err = query.Find(&entities).Error
    return entities, total, err
}

func (r *pictureRepository) applyCustomConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    if condition == nil {
        return query
    }
    
    for key, value := range condition {
        switch key {
        case "title":
            if title, ok := value.(string); ok {
                query = query.Where("title LIKE ?", "%"+title+"%")
            }
        case "keyword":
            if keyword, ok := value.(string); ok {
                query = query.Where("title LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
            }
        // 其他自定义条件...
        default:
            // 默认等值查询
            query = query.Where(key+" = ?", value)
        }
    }
    
    return query
}
```

## 迁移现有代码

### 步骤1：创建新的Repository接口

```go
// 原有接口
type OldPictureRepository interface {
    Create(ctx context.Context, picture *pictures.Picture) error
    FindByID(ctx context.Context, id string) (*pictures.Picture, error)
    Update(ctx context.Context, picture *pictures.Picture) error
    Delete(ctx context.Context, id string) error
    ListPictures(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.Picture, int64, error)
}

// 新接口（继承基础功能）
type PictureRepository interface {
    base.ExtendedRepository[pictures.Picture]
    // 只需要定义业务特定方法
    BatchCreate(ctx context.Context, pictures []*pictures.Picture) (int, error)
}
```

### 步骤2：更新实现

```go
type pictureRepository struct {
    base.ExtendedRepository[pictures.Picture]
}

func NewPictureRepository(db *gorm.DB) PictureRepository {
    return &pictureRepository{
        ExtendedRepository: base.NewExtendedRepository[pictures.Picture](db),
    }
}

// 只需要实现业务特定方法
func (r *pictureRepository) BatchCreate(ctx context.Context, pictures []*pictures.Picture) (int, error) {
    err := r.ExtendedRepository.BatchCreate(ctx, pictures)
    if err != nil {
        return 0, err
    }
    return len(pictures), nil
}
```

### 步骤3：更新Service层调用

```go
// 原有调用
pictures, total, err := r.pictureRepo.ListPictures(ctx, condition, orderBy, page, pageSize)

// 新调用（方法名更简洁）
pictures, total, err := r.pictureRepo.List(ctx, condition, orderBy, page, pageSize)
```

## 优势

1. **减少重复代码**：基础CRUD操作无需重复实现
2. **统一接口**：所有Repository都有一致的方法签名
3. **类型安全**：使用泛型确保类型安全
4. **易于扩展**：可以轻松添加新的通用方法
5. **向后兼容**：可以逐步迁移现有代码
6. **数据库访问**：提供GetDB()方法用于复杂查询

## 注意事项

1. **Go版本要求**：需要Go 1.18+支持泛型
2. **模型要求**：模型需要有`id`字段作为主键
3. **条件查询**：复杂查询条件需要重写相应方法
4. **性能考虑**：对于高性能要求的查询，建议使用原生SQL
5. **事务支持**：可以通过GetDB()方法获取数据库实例进行事务操作

## 示例项目结构

```
frontapi/internal/repository/
├── base/
│   ├── base_repository.go      # 基础仓库接口
│   ├── extended_repository.go  # 扩展仓库接口
│   └── README.md              # 使用指南
├── pictures/
│   ├── picture_repository.go           # 原有实现
│   └── picture_repository_refactored.go # 重构后实现
├── posts/
│   └── post_repository_v2.go   # 使用基础接口的新实现
└── users/
    └── user_repository_v2.go    # 使用基础接口的新实现
```

通过使用这些基础接口，可以大大减少Repository层的重复代码，提高开发效率和代码质量。