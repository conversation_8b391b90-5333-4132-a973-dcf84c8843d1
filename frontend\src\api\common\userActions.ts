import { post } from '@/shared/composables'
import { corePost } from '@/shared/composables'

// ==================== 用户操作相关API ====================

/**
 * 关注用户
 * @param params 关注参数
 * @param params.data.userId 要关注的用户ID
 */
export const followUser = (params: { data: { userId: string } }) => {
  return corePost('/users/follow', params)
}

/**
 * 取消关注用户
 * @param params 取消关注参数
 * @param params.data.userId 要取消关注的用户ID
 */
export const unfollowUser = (params: { data: { userId: string } }) => {
  return corePost('/users/unfollow', params)
}

/**
 * 检查是否已关注用户
 * @param params 检查参数
 * @param params.data.userId 要检查的用户ID
 */
export const checkFollowStatus = (params: { data: { userId: string } }) => {
  return corePost('/users/checkFollowStatus', params)
}

/**
 * 批量检查关注状态
 * @param params 批量检查参数
 * @param params.data.userIds 要检查的用户ID列表
 */
export const batchCheckFollowStatus = (params: { data: { userIds: string[] } }) => {
  return corePost('/users/batchCheckFollowStatus', params)
}