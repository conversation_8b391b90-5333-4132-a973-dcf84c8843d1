<template>
    <div class="slinky-table-container">
        <!-- 表格头部 -->
        <div v-if="showHeader" class="table-header">
            <div class="header-left">
                <h3 v-if="title" class="table-title">{{ title }}</h3>
                <slot name="header-left"></slot>
            </div>
            <div class="header-right">
                <slot name="header-right">
                    <el-button v-if="showAdd" type="primary" :icon="Plus" @click="handleAdd">
                        {{ addText }}
                    </el-button>
                    <el-button v-if="showRefresh" :icon="Refresh" @click="handleRefresh">
                        刷新
                    </el-button>
                </slot>
            </div>
        </div>

        <!-- 搜索区域 -->
        <div v-if="showSearch" class="table-search">
            <slot name="search"></slot>
        </div>
        <!-- 批量操作工具栏 -->
        <div v-if="showBatchActions && selectedRows.length > 0" class="batch-toolbar">
            <div class="batch-info">
                已选择 <span class="selected-count">{{ selectedRows.length }}</span> 项
                <el-button type="primary" link @click="clearSelection">取消选择</el-button>
            </div>
            <div class="batch-actions">
                <slot name="batch-actions" :selectedRows="selectedRows" :selectedCount="selectedRows.length">
                    <el-button v-if="showBatchDelete" type="danger" :icon="Delete" @click="handleBatchDelete">
                        批量删除
                    </el-button>
                </slot>
            </div>
        </div>

        <!-- 表格主体 -->
        <el-table ref="tableRef" v-bind="$attrs" :data="data" :loading="loading" :border="border" :stripe="stripe"
            :size="size" :height="height" :max-height="maxHeight" :show-header="showTableHeader" :row-key="rowKey"
            :tree-props="treeProps" :row-class-name="getRowClassName" :cell-class-name="getCellClassName"
            :header-row-class-name="getHeaderRowClassName" :header-cell-class-name="getHeaderCellClassName"
            :span-method="spanMethod" :summary-method="summaryMethod" :show-summary="showSummary" :sum-text="sumText"
            :select-on-indeterminate="selectOnIndeterminate" :indent="indent" :lazy="lazy" :load="load"
            @selection-change="handleSelectionChange" @select="handleSelect" @select-all="handleSelectAll"
            @row-click="handleRowClick" @row-contextmenu="handleRowContextmenu" @row-dblclick="handleRowDblclick"
            @cell-click="handleCellClick" @cell-dblclick="handleCellDblclick" @sort-change="handleSortChange"
            @filter-change="handleFilterChange" @current-change="handleCurrentChange" @header-click="handleHeaderClick"
            @header-contextmenu="handleHeaderContextmenu" @expand-change="handleExpandChange" class="slinky-table">
            <!-- 选择列 -->
            <el-table-column v-if="showSelection" type="selection" width="50" align="center"
                :selectable="(selectable as any)" :reserve-selection="reserveSelection" />

            <!-- 序号列 -->
            <el-table-column v-if="showIndex" type="index" :label="indexLabel" :width="indexWidth" align="center"
                :index="getIndex" />

            <!-- 展开列 -->
            <el-table-column v-if="showExpand" type="expand" :width="expandWidth" align="center">
                <template #default="props">
                    <slot name="expand" :row="props.row" :index="props.$index"></slot>
                </template>
            </el-table-column>

            <!-- 动态插槽内容 - 支持el-table-column -->
            <slot></slot>

            <!-- 操作列 -->
            <el-table-column v-if="showActions" :label="actionLabel" :width="actionWidth" :min-width="actionMinWidth"
                :fixed="actionFixed" :align="actionAlign" :header-align="actionHeaderAlign" class-name="action-column">
                <template #default="scope">
                    <div class="slinky-actions">
                        <!-- 预定义操作按钮 -->
                        <template v-if="!$slots.actions">
                            <el-button v-if="showView" type="primary" link size="small"
                                @click="handleView(scope.row, scope.$index)"
                                :disabled="isActionDisabled('view', scope.row)">
                                {{ viewText }}
                            </el-button>
                            <el-button v-if="showEdit" type="primary" link size="small"
                                @click="handleEdit(scope.row, scope.$index)"
                                :disabled="isActionDisabled('edit', scope.row)">
                                {{ editText }}
                            </el-button>
                            <el-popconfirm v-if="showDelete" :title="getDeleteTitle(scope.row)"
                                :confirm-button-text="deleteConfirmText" :cancel-button-text="deleteCancelText"
                                confirm-button-type="danger" @confirm="handleDelete(scope.row, scope.$index)">
                                <template #reference>
                                    <el-button type="danger" link size="small"
                                        :disabled="isActionDisabled('delete', scope.row)">
                                        {{ deleteText }}
                                    </el-button>
                                </template>
                            </el-popconfirm>
                        </template>

                        <!-- 自定义操作插槽 -->
                        <slot name="actions" :row="scope.row" :index="scope.$index"
                            :view="() => handleView(scope.row, scope.$index)"
                            :edit="() => handleEdit(scope.row, scope.$index)"
                            :delete="() => handleDelete(scope.row, scope.$index)"></slot>
                    </div>
                </template>
            </el-table-column>
        </el-table>


        <!-- 空状态 -->
        <div v-if="!loading && (!data || data.length === 0)" class="table-empty">
            <slot name="empty">
                <el-empty :description="emptyText" />
            </slot>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Delete, Plus, Refresh } from '@element-plus/icons-vue'
import type { TableInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'

// Props 接口定义
interface Props {
    // 数据相关
    data: any[]
    loading?: boolean

    // 表格基本配置
    border?: boolean
    stripe?: boolean
    size?: 'large' | 'default' | 'small'
    height?: string | number
    maxHeight?: string | number
    showTableHeader?: boolean

    // 行配置
    rowKey?: string | ((row: any) => string)
    treeProps?: { children?: string; hasChildren?: string }
    rowClassName?: string | ((data: { row: any; rowIndex: number }) => string)
    cellClassName?: string | ((data: { row: any; column: any; rowIndex: number; columnIndex: number }) => string)
    headerRowClassName?: string | ((data: { rowIndex: number }) => string)
    headerCellClassName?: string | ((data: { row: any; column: any; rowIndex: number; columnIndex: number }) => string)
    spanMethod?: (data: { row: any; column: any; rowIndex: number; columnIndex: number }) => number[] | { rowspan: number; colspan: number }
    summaryMethod?: (data: { columns: any[]; data: any[] }) => string[]
    showSummary?: boolean
    sumText?: string
    selectOnIndeterminate?: boolean
    indent?: number
    lazy?: boolean
    load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void

    // 表格头部
    title?: string
    showHeader?: boolean
    showAdd?: boolean
    addText?: string
    showRefresh?: boolean

    // 搜索配置
    showSearch?: boolean

    // 选择配置
    showSelection?: boolean
    selectable?: (row: any, index: number) => boolean
    reserveSelection?: boolean

    // 序号配置
    showIndex?: boolean
    indexLabel?: string
    indexWidth?: string | number
    indexMethod?: (index: number) => number

    // 展开配置
    showExpand?: boolean
    expandWidth?: string | number

    // 批量操作配置
    showBatchActions?: boolean
    showBatchDelete?: boolean

    // 操作列配置
    showActions?: boolean
    actionLabel?: string
    actionWidth?: string | number
    actionMinWidth?: string | number
    actionFixed?: boolean | 'left' | 'right'
    actionAlign?: 'left' | 'center' | 'right'
    actionHeaderAlign?: 'left' | 'center' | 'right'

    // 预定义操作按钮
    showView?: boolean
    viewText?: string
    showEdit?: boolean
    editText?: string
    showDelete?: boolean
    deleteText?: string
    deleteConfirmText?: string
    deleteCancelText?: string

    // 操作权限控制
    actionPermissions?: {
        view?: (row: any) => boolean
        edit?: (row: any) => boolean
        delete?: (row: any) => boolean
    }

    // 分页配置
    showPagination?: boolean
    total?: number
    currentPage?: number
    pageSize?: number
    pageSizes?: number[]
    paginationLayout?: string
    paginationBackground?: boolean
    paginationSmall?: boolean
    paginationDisabled?: boolean
    hideOnSinglePage?: boolean

    // 空状态
    emptyText?: string

    // 自定义标题获取函数
    titleField?: string | ((row: any) => string)
}

const props = withDefaults(defineProps<Props>(), {
    data: () => [],
    loading: false,
    border: true,
    stripe: true,
    size: 'default',
    showTableHeader: true,
    showHeader: true,
    showAdd: false,
    addText: '新增',
    showRefresh: false,
    showSearch: false,
    showSelection: false,
    reserveSelection: false,
    showIndex: false,
    indexLabel: '序号',
    indexWidth: 60,
    showExpand: false,
    expandWidth: 50,
    showBatchActions: false,
    showBatchDelete: false,
    showActions: true,
    actionLabel: '操作',
    actionWidth: 180,
    actionFixed: 'right',
    actionAlign: 'center',
    actionHeaderAlign: 'center',
    showView: false,
    viewText: '查看',
    showEdit: true,
    editText: '编辑',
    showDelete: true,
    deleteText: '删除',
    deleteConfirmText: '确定',
    deleteCancelText: '取消',
    showPagination: true,
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: () => [10, 20, 50, 100],
    paginationLayout: 'total, sizes, prev, pager, next, jumper',
    paginationBackground: true,
    paginationSmall: false,
    paginationDisabled: false,
    hideOnSinglePage: false,
    emptyText: '暂无数据',
    titleField: 'title'
})

// Emits 定义
const emit = defineEmits<{
    // 表格事件
    'selection-change': [selection: any[]]
    'select': [selection: any[], row: any]
    'select-all': [selection: any[]]
    'row-click': [row: any, column: any, event: Event]
    'row-contextmenu': [row: any, column: any, event: Event]
    'row-dblclick': [row: any, column: any, event: Event]
    'cell-click': [row: any, column: any, cell: any, event: Event]
    'cell-dblclick': [row: any, column: any, cell: any, event: Event]
    'sort-change': [data: { column: any; prop: string; order: string | null }]
    'filter-change': [filters: Record<string, any[]>]
    'current-change': [currentRow: any, oldCurrentRow: any]
    'header-click': [column: any, event: Event]
    'header-contextmenu': [column: any, event: Event]
    'expand-change': [row: any, expandedRows: any[]]

    // 操作事件
    'add': []
    'refresh': []
    'view': [row: any, index: number]
    'edit': [row: any, index: number]
    'delete': [row: any, index: number]
    'batch-delete': [rows: any[]]

    // 分页事件
    'page-change': [page: number]
    'size-change': [size: number]
    'prev-click': [page: number]
    'next-click': [page: number]
}>()

// 响应式数据
const tableRef = ref<TableInstance>()
const selectedRows = ref<any[]>([])

// 计算属性
const currentPage = computed({
    get: () => props.currentPage,
    set: (value) => emit('page-change', value)
})

const pageSize = computed({
    get: () => props.pageSize,
    set: (value) => emit('size-change', value)
})

// 方法定义
const getRowClassName = (data: { row: any; rowIndex: number }) => {
    let className = 'slinky-table-row'
    if (typeof props.rowClassName === 'string') {
        className += ` ${props.rowClassName}`
    } else if (typeof props.rowClassName === 'function') {
        className += ` ${props.rowClassName(data)}`
    }
    return className
}

const getCellClassName = (data: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    let className = 'slinky-table-cell'
    if (typeof props.cellClassName === 'string') {
        className += ` ${props.cellClassName}`
    } else if (typeof props.cellClassName === 'function') {
        className += ` ${props.cellClassName(data)}`
    }
    return className
}

const getHeaderRowClassName = (data: { rowIndex: number }) => {
    let className = 'slinky-table-header-row'
    if (typeof props.headerRowClassName === 'string') {
        className += ` ${props.headerRowClassName}`
    } else if (typeof props.headerRowClassName === 'function') {
        className += ` ${props.headerRowClassName(data)}`
    }
    return className
}

const getHeaderCellClassName = (data: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
    let className = 'slinky-table-header-cell'
    if (typeof props.headerCellClassName === 'string') {
        className += ` ${props.headerCellClassName}`
    } else if (typeof props.headerCellClassName === 'function') {
        className += ` ${props.headerCellClassName(data)}`
    }
    return className
}

const getIndex = (index: number) => {
    if (props.indexMethod) {
        return props.indexMethod(index)
    }
    return (props.currentPage - 1) * props.pageSize + index + 1
}

const getDeleteTitle = (row: any) => {
    const title = typeof props.titleField === 'function'
        ? props.titleField(row)
        : row[props.titleField as string] || '此项目'
    return `确定要删除"${title}"吗？`
}

const isActionDisabled = (action: 'view' | 'edit' | 'delete', row: any) => {
    return props.actionPermissions?.[action]?.(row) === false
}

// 事件处理
const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
    emit('selection-change', selection)
}

const handleSelect = (selection: any[], row: any) => {
    emit('select', selection, row)
}

const handleSelectAll = (selection: any[]) => {
    emit('select-all', selection)
}

const handleRowClick = (row: any, column: any, event: Event) => {
    emit('row-click', row, column, event)
}

const handleRowContextmenu = (row: any, column: any, event: Event) => {
    emit('row-contextmenu', row, column, event)
}

const handleRowDblclick = (row: any, column: any, event: Event) => {
    emit('row-dblclick', row, column, event)
}

const handleCellClick = (row: any, column: any, cell: any, event: Event) => {
    emit('cell-click', row, column, cell, event)
}

const handleCellDblclick = (row: any, column: any, cell: any, event: Event) => {
    emit('cell-dblclick', row, column, cell, event)
}

const handleSortChange = (data: { column: any; prop: string; order: string | null }) => {
    emit('sort-change', data)
}

const handleFilterChange = (filters: Record<string, any[]>) => {
    emit('filter-change', filters)
}

const handleCurrentChange = (currentRow: any, oldCurrentRow: any) => {
    emit('current-change', currentRow, oldCurrentRow)
}

const handleHeaderClick = (column: any, event: Event) => {
    emit('header-click', column, event)
}

const handleHeaderContextmenu = (column: any, event: Event) => {
    emit('header-contextmenu', column, event)
}

const handleExpandChange = (row: any, expandedRows: any[]) => {
    emit('expand-change', row, expandedRows)
}

const handleAdd = () => {
    emit('add')
}

const handleRefresh = () => {
    emit('refresh')
}

const handleView = (row: any, index: number) => {
    emit('view', row, index)
}

const handleEdit = (row: any, index: number) => {
    emit('edit', row, index)
}

const handleDelete = (row: any, index: number) => {
    emit('delete', row, index)
}

const handleBatchDelete = async () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的项目')
        return
    }

    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${selectedRows.value.length} 个项目吗？`,
            '批量删除确认',
            {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }
        )

        emit('batch-delete', [...selectedRows.value])
    } catch {
        // 用户取消删除
    }
}

const handleSizeChange = (size: number) => {
    pageSize.value = size
    emit('size-change', size)
}

const handlePageCurrentChange = (page: number) => {
    currentPage.value = page
    emit('page-change', page)
}

const handlePrevClick = (page: number) => {
    emit('prev-click', page)
}

const handleNextClick = (page: number) => {
    emit('next-click', page)
}

// 公共方法
const clearSelection = () => {
    tableRef.value?.clearSelection()
    selectedRows.value = []
}

const toggleRowSelection = (row: any, selected?: boolean) => {
    tableRef.value?.toggleRowSelection(row, selected)
}

const toggleAllSelection = () => {
    tableRef.value?.toggleAllSelection()
}

const setCurrentRow = (row: any) => {
    tableRef.value?.setCurrentRow(row)
}

const clearSort = () => {
    tableRef.value?.clearSort()
}

const clearFilter = (columnKeys?: string[]) => {
    tableRef.value?.clearFilter(columnKeys)
}

const doLayout = () => {
    tableRef.value?.doLayout()
}

const sort = (prop: string, order: string) => {
    tableRef.value?.sort(prop, order)
}

const scrollTo = (options: ScrollToOptions | number, yCoord?: number) => {
    tableRef.value?.scrollTo(options, yCoord)
}

const setScrollTop = (top: number) => {
    tableRef.value?.setScrollTop(top)
}

const setScrollLeft = (left: number) => {
    tableRef.value?.setScrollLeft(left)
}

// 暴露方法和属性
defineExpose({
    // 表格实例
    tableRef,

    // 选择相关
    selectedRows,
    clearSelection,
    toggleRowSelection,
    toggleAllSelection,

    // 当前行
    setCurrentRow,

    // 排序和过滤
    clearSort,
    clearFilter,
    sort,

    // 布局
    doLayout,

    // 滚动
    scrollTo,
    setScrollTop,
    setScrollLeft
})
</script>

<style lang="scss" scoped>
.slinky-table-container {
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;

            .table-title {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: var(--el-text-color-primary);
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .table-search {
        margin-bottom: 16px;
    }

    .batch-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 16px;
        background-color: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary-light-7);
        border-radius: var(--el-card-border-radius);

        .batch-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--el-text-color-primary);

            .selected-count {
                color: var(--el-color-primary);
                font-weight: 600;
            }
        }

        .batch-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .slinky-table {
        margin-bottom: 16px;

        :deep(.slinky-table-row) {
            &:hover {
                background-color: var(--el-table-row-hover-bg-color);
            }
        }

        :deep(.action-column) {
            .slinky-actions {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2px;
                flex-wrap: wrap;

                .el-button {
                    margin: 0;
                    padding: 4px 4px;
                    min-height: auto;

                    &.is-link {
                        padding: 4px 4px;
                    }
                }
            }
        }
    }

    .table-pagination {
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;
    }

    .table-empty {
        margin: 40px 0;
    }
}

// 主题样式
.slinky-table-container {
    :deep(.el-table) {
        border-radius: var(--el-card-border-radius);
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .el-table__header {
            th {
                background: #ffffff !important;
                color: #303133 !important;
                font-weight: bold !important;
                font-size: 14px;
                height: 60px !important;
                padding: 18px 12px !important;
                border-right: var(--el-table-border) !important;

                .el-table__cell {
                    background: transparent !important;
                    color: inherit !important;
                    height: inherit !important;

                    .cell {
                        color: #303133 !important;
                        font-weight: bold !important;
                        font-size: 14px;
                        line-height: 1.5;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }

                // 排序图标样式
                .caret-wrapper {
                    .sort-caret {


                        &.ascending {
                            border-bottom-color: #409eff;
                        }

                        &.descending {
                            border-top-color: #409eff;
                        }
                    }
                }
            }
        }

        .el-table__body {
            tr {
                transition: all 0.3s ease;

                &:nth-child(even) {
                    background-color: var(--el-table-tr-bg-color);
                }

                &:hover {
                    background-color: var(--el-table-row-hover-bg-color);
                }

                td {
                    border-color: var(--el-table-border-color);
                    padding: 6px 10px !important;
                    text-align: left;

                    &.el-table-column--selection {
                        text-align: center;
                    }

                    &.is-center {
                        text-align: center;
                    }

                    &.action-column {
                        background-color: rgba(255, 255, 255, 0.8);
                    }
                }
            }
        }
    }

    :deep(.el-pagination) {
        .el-pager {
            .number {
                &.is-active {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-color: transparent;
                    color: white;
                }
            }
        }

        .btn-prev,
        .btn-next {
            &:not(.is-disabled):hover {
                color: var(--el-color-primary);
            }
        }
    }

    // 深色模式支持
    &.dark {
        :deep(.el-table) {
            .el-table__header {
                th {
                    background: #1d1e1f !important;
                    color: #e2e8f0 !important;
                    border-right: var(--el-table-border) !important;

                    .el-table__cell .cell {
                        color: #e2e8f0 !important;
                    }
                }
            }
        }
    }
}
</style>