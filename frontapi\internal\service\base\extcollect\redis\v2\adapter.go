package v2

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"

	"frontapi/internal/service/base/extcollect/types"
)

// Config Redis配置
type Config struct {
	Prefix       string        `json:"prefix"`
	BatchSize    int           `json:"batch_size"`
	TTL          time.Duration `json:"ttl"`
	PipelineSize int           `json:"pipeline_size"`
	MaxRetries   int           `json:"max_retries"`
	RetryDelay   time.Duration `json:"retry_delay"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Prefix:       "collect",
		BatchSize:    100,
		TTL:          24 * time.Hour,
		PipelineSize: 1000,
		MaxRetries:   3,
		RetryDelay:   100 * time.Millisecond,
	}
}

// RedisAdapter Redis适配器主结构
type RedisAdapter struct {
	client      *redis.Client
	cacheKey    *types.CacheKey
	config      *Config
	redisClient *RedisClient

	// 操作处理器 - 模块化设计
	collectOps *CollectOperations // 收藏操作处理器
	queryOps   *QueryOperations   // 查询操作处理器
	rankingOps *RankingOperations // 排名操作处理器
	statsOps   *StatsOperations   // 统计操作处理器
}

// NewRedisAdapter 创建Redis适配器
func NewRedisAdapter(client *redis.Client, config *Config) *RedisAdapter {
	if config == nil {
		config = DefaultConfig()
	}

	cacheKey := types.NewCacheKey(config.Prefix)

	// 创建RedisClient包装器
	redisConfig := &RedisConfig{
		Enabled:    true,
		KeyPrefix:  config.Prefix,
		DefaultTTL: config.TTL,
		CollectTTL: config.TTL,
		CountTTL:   config.TTL,
		RankingTTL: config.TTL,
	}
	redisClient := NewRedisClient(client, redisConfig)

	adapter := &RedisAdapter{
		client:      client,
		cacheKey:    cacheKey,
		config:      config,
		redisClient: redisClient,
	}

	// 初始化操作处理器，使用正确的函数签名
	adapter.collectOps = NewCollectOperations(redisClient)
	adapter.queryOps = NewQueryOperations(redisClient)
	adapter.rankingOps = NewRankingOperations(redisClient)
	adapter.statsOps = NewStatsOperations(redisClient)

	return adapter
}

// 基础操作 - 委托给收藏操作处理器
func (r *RedisAdapter) Collect(ctx context.Context, userID, itemID, itemType string) error {
	return r.collectOps.Collect(ctx, userID, itemID, itemType)
}

func (r *RedisAdapter) Uncollect(ctx context.Context, userID, itemID, itemType string) error {
	return r.collectOps.Uncollect(ctx, userID, itemID, itemType)
}

func (r *RedisAdapter) IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	return r.collectOps.IsCollected(ctx, userID, itemID, itemType)
}

func (r *RedisAdapter) GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error) {
	return r.collectOps.GetCollectCount(ctx, itemID, itemType)
}

// 批量操作 - 简化实现，因为operations文件中没有这些方法
func (r *RedisAdapter) BatchCollect(ctx context.Context, operations []*types.CollectOperation) error {
	// 循环调用单个收藏操作
	for _, op := range operations {
		if op.Action == "collect" {
			if err := r.Collect(ctx, op.UserID, op.ItemID, op.ItemType); err != nil {
				return err
			}
		}
	}
	return nil
}

func (r *RedisAdapter) BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error {
	// 循环调用单个取消收藏操作
	for _, op := range operations {
		if op.Action == "uncollect" {
			if err := r.Uncollect(ctx, op.UserID, op.ItemID, op.ItemType); err != nil {
				return err
			}
		}
	}
	return nil
}

func (r *RedisAdapter) BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	result := make(map[string]bool)
	for itemID, itemType := range items {
		isCollected, err := r.IsCollected(ctx, userID, itemID, itemType)
		if err != nil {
			return nil, err
		}
		key := itemType + ":" + itemID
		result[key] = isCollected
	}
	return result, nil
}

func (r *RedisAdapter) BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	result := make(map[string]int64)
	for itemID, itemType := range items {
		count, err := r.GetCollectCount(ctx, itemID, itemType)
		if err != nil {
			return nil, err
		}
		key := itemType + ":" + itemID
		result[key] = count
	}
	return result, nil
}

// 查询操作 - 委托给查询操作处理器
func (r *RedisAdapter) GetUserCollections(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	return r.queryOps.GetUserCollects(ctx, userID, itemType, limit, offset)
}

func (r *RedisAdapter) GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	return r.queryOps.GetItemCollectors(ctx, itemID, itemType, limit, offset)
}

func (r *RedisAdapter) GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error) {
	return r.queryOps.GetCollectHistory(ctx, userID, itemType, timeRange)
}

// 热度排名 - 委托给排名操作处理器
func (r *RedisAdapter) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	return r.rankingOps.UpdateHotRank(ctx, itemID, itemType, score)
}

func (r *RedisAdapter) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	return r.rankingOps.GetHotRanking(ctx, itemType, limit)
}

func (r *RedisAdapter) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	return r.rankingOps.GetHotRankingWithScores(ctx, itemType, limit)
}

// 统计操作 - 委托给统计操作处理器
func (r *RedisAdapter) GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error) {
	return r.statsOps.GetUserCollectStats(ctx, userID)
}

func (r *RedisAdapter) GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error) {
	return r.statsOps.GetItemCollectStats(ctx, itemID, itemType)
}

func (r *RedisAdapter) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error) {
	return r.statsOps.GetTrendingItems(ctx, itemType, timeRange, limit)
}

// 缓存管理 - 简化实现
func (r *RedisAdapter) InvalidateCache(ctx context.Context, keys ...string) error {
	for _, key := range keys {
		r.client.Del(ctx, key)
	}
	return nil
}

func (r *RedisAdapter) WarmupCache(ctx context.Context, itemType string, itemIDs []string) error {
	// 简化实现，预热热门项目的收藏数
	for _, itemID := range itemIDs {
		_, _ = r.GetCollectCount(ctx, itemID, itemType)
	}
	return nil
}

func (r *RedisAdapter) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return r.statsOps.GetCacheStats(ctx)
}

// 数据管理 - 简化实现
func (r *RedisAdapter) CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error {
	// 简化实现
	return nil
}

func (r *RedisAdapter) ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error) {
	// 简化实现
	return []byte("{}"), nil
}

func (r *RedisAdapter) ImportData(ctx context.Context, data []byte, itemType, format string) error {
	// 简化实现
	return nil
}

// 健康检查和关闭
func (r *RedisAdapter) HealthCheck(ctx context.Context) error {
	return r.redisClient.HealthCheck(ctx)
}

func (r *RedisAdapter) Close() error {
	return r.client.Close()
}
