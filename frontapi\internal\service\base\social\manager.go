package social

import (
	"context"
	"sync"
	"time"

	"frontapi/internal/service/base/extcollect"
	"frontapi/internal/service/base/extfollow"
	"frontapi/internal/service/base/extlike"
)

// SocialServiceManager 统一的社交服务管理器
// 整合点赞、收藏、关注三大核心社交功能
type SocialServiceManager struct {
	// 核心服务
	LikeService    extlike.ExtendedLikeService
	CollectService extcollect.ExtendedCollectService
	FollowService  extfollow.ExtendedFollowService

	// 管理组件
	configManager  *ConfigManager
	metricsManager *MetricsManager
	eventBus       *EventBus
	syncManager    *SyncManager

	// 运行时状态
	isRunning bool
	stopCh    chan struct{}
	wg        sync.WaitGroup
	mu        sync.RWMutex
}

// Config 社交服务管理器配置
type Config struct {
	// 服务配置
	LikeConfig    *extlike.Config
	CollectConfig *extcollect.Config
	FollowConfig  *extfollow.Config

	// 管理器配置
	MetricsEnabled    bool          `json:"metrics_enabled"`
	SyncEnabled       bool          `json:"sync_enabled"`
	EventBusEnabled   bool          `json:"event_bus_enabled"`
	HealthCheckPeriod time.Duration `json:"health_check_period"`
	SyncPeriod        time.Duration `json:"sync_period"`

	// 性能配置
	ConcurrencyLevel int           `json:"concurrency_level"`
	BatchSize        int           `json:"batch_size"`
	CacheSync        bool          `json:"cache_sync"`
	SyncTimeout      time.Duration `json:"sync_timeout"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		MetricsEnabled:    true,
		SyncEnabled:       true,
		EventBusEnabled:   true,
		HealthCheckPeriod: 30 * time.Second,
		SyncPeriod:        5 * time.Minute,
		ConcurrencyLevel:  10,
		BatchSize:         100,
		CacheSync:         true,
		SyncTimeout:       30 * time.Second,
	}
}

// NewSocialServiceManager 创建社交服务管理器
func NewSocialServiceManager(config *Config) (*SocialServiceManager, error) {
	if config == nil {
		config = DefaultConfig()
	}

	manager := &SocialServiceManager{
		configManager: NewConfigManager(config),
		stopCh:        make(chan struct{}),
	}

	// 初始化核心服务
	if err := manager.initServices(config); err != nil {
		return nil, err
	}

	// 初始化管理组件
	if err := manager.initManagers(config); err != nil {
		return nil, err
	}

	return manager, nil
}

// initServices 初始化核心服务
func (m *SocialServiceManager) initServices(config *Config) error {
	var err error

	// 初始化点赞服务
	if config.LikeConfig != nil {
		// 使用扩展点赞服务
		m.LikeService, err = extlike.NewExtendedService(config.LikeConfig)
		if err != nil {
			return err
		}
	}

	// 初始化收藏服务
	if config.CollectConfig != nil {
		// 使用扩展收藏服务
		m.CollectService, err = extcollect.NewExtendedService(config.CollectConfig)
		if err != nil {
			return err
		}
	}

	// 初始化关注服务
	if config.FollowConfig != nil {
		// 使用扩展关注服务
		m.FollowService, err = extfollow.NewExtendedService(config.FollowConfig)
		if err != nil {
			return err
		}
	}

	return nil
}

// initManagers 初始化管理组件
func (m *SocialServiceManager) initManagers(config *Config) error {
	// 初始化指标管理器
	if config.MetricsEnabled {
		m.metricsManager = NewMetricsManager(
			m.LikeService,
			m.CollectService,
			m.FollowService,
		)
	}

	// 初始化事件总线
	if config.EventBusEnabled {
		m.eventBus = NewEventBus()
		m.setupEventHandlers()
	}

	// 初始化同步管理器
	if config.SyncEnabled {
		m.syncManager = NewSyncManager(
			m.LikeService,
			m.CollectService,
			m.FollowService,
			config.SyncPeriod,
		)
	}

	return nil
}

// Start 启动社交服务管理器
func (m *SocialServiceManager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.isRunning {
		return ErrAlreadyRunning
	}

	// 启动管理组件
	if m.metricsManager != nil {
		m.wg.Add(1)
		go m.runMetricsManager(ctx)
	}

	if m.syncManager != nil {
		m.wg.Add(1)
		go m.runSyncManager(ctx)
	}

	if m.eventBus != nil {
		if err := m.eventBus.Start(ctx); err != nil {
			return err
		}
	}

	m.isRunning = true
	return nil
}

// Stop 停止社交服务管理器
func (m *SocialServiceManager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.isRunning {
		return nil
	}

	// 发送停止信号
	close(m.stopCh)

	// 等待所有goroutine结束
	done := make(chan struct{})
	go func() {
		m.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
	case <-ctx.Done():
		return ctx.Err()
	}

	// 关闭核心服务
	if m.LikeService != nil {
		if err := m.LikeService.Shutdown(ctx); err != nil {
			return err
		}
	}
	if m.CollectService != nil {
		if err := m.CollectService.Shutdown(ctx); err != nil {
			return err
		}
	}
	if m.FollowService != nil {
		if err := m.FollowService.Shutdown(ctx); err != nil {
			return err
		}
	}

	// 关闭事件总线
	if m.eventBus != nil {
		if err := m.eventBus.Stop(ctx); err != nil {
			return err
		}
	}

	m.isRunning = false
	return nil
}

// GetLikeService 获取点赞服务
func (m *SocialServiceManager) GetLikeService() extlike.ExtendedLikeService {
	return m.LikeService
}

// GetCollectService 获取收藏服务
func (m *SocialServiceManager) GetCollectService() extcollect.ExtendedCollectService {
	return m.CollectService
}

// GetFollowService 获取关注服务
func (m *SocialServiceManager) GetFollowService() extfollow.ExtendedFollowService {
	return m.FollowService
}

// GetMetrics 获取总体指标
func (m *SocialServiceManager) GetMetrics(ctx context.Context) (*SocialMetrics, error) {
	if m.metricsManager == nil {
		return nil, ErrMetricsDisabled
	}
	return m.metricsManager.GetMetrics(ctx)
}

// GetHealthStatus 获取健康状态
func (m *SocialServiceManager) GetHealthStatus(ctx context.Context) (*HealthStatus, error) {
	status := &HealthStatus{
		Timestamp: time.Now(),
		Services:  make(map[string]ServiceHealth),
	}

	// 检查点赞服务
	if m.LikeService != nil {
		if err := m.LikeService.HealthCheck(ctx); err != nil {
			status.Services["like"] = ServiceHealth{
				Status: "unhealthy",
				Error:  err.Error(),
			}
		} else {
			status.Services["like"] = ServiceHealth{
				Status: "healthy",
			}
		}
	}

	// 检查收藏服务
	if m.CollectService != nil {
		if err := m.CollectService.HealthCheck(ctx); err != nil {
			status.Services["collect"] = ServiceHealth{
				Status: "unhealthy",
				Error:  err.Error(),
			}
		} else {
			status.Services["collect"] = ServiceHealth{
				Status: "healthy",
			}
		}
	}

	// 检查关注服务
	if m.FollowService != nil {
		if err := m.FollowService.HealthCheck(ctx); err != nil {
			status.Services["follow"] = ServiceHealth{
				Status: "unhealthy",
				Error:  err.Error(),
			}
		} else {
			status.Services["follow"] = ServiceHealth{
				Status: "healthy",
			}
		}
	}

	// 计算总体状态
	status.OverallStatus = m.calculateOverallHealth(status.Services)

	return status, nil
}

// setupEventHandlers 设置事件处理器
func (m *SocialServiceManager) setupEventHandlers() {
	// 点赞事件处理
	m.eventBus.Subscribe("like", func(event interface{}) {
		if m.metricsManager != nil {
			m.metricsManager.RecordLikeEvent(event)
		}
	})

	// 收藏事件处理
	m.eventBus.Subscribe("collect", func(event interface{}) {
		if m.metricsManager != nil {
			m.metricsManager.RecordCollectEvent(event)
		}
	})

	// 关注事件处理
	m.eventBus.Subscribe("follow", func(event interface{}) {
		if m.metricsManager != nil {
			m.metricsManager.RecordFollowEvent(event)
		}
	})
}

// runMetricsManager 运行指标管理器
func (m *SocialServiceManager) runMetricsManager(ctx context.Context) {
	defer m.wg.Done()

	ticker := time.NewTicker(m.configManager.GetHealthCheckPeriod())
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := m.metricsManager.CollectMetrics(ctx); err != nil {
				// 记录错误但继续运行
				m.logError("metrics collection failed", err)
			}
		case <-m.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// runSyncManager 运行同步管理器
func (m *SocialServiceManager) runSyncManager(ctx context.Context) {
	defer m.wg.Done()

	ticker := time.NewTicker(m.configManager.GetSyncPeriod())
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := m.syncManager.SyncAll(ctx); err != nil {
				// 记录错误但继续运行
				m.logError("sync failed", err)
			}
		case <-m.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// calculateOverallHealth 计算总体健康状态
func (m *SocialServiceManager) calculateOverallHealth(services map[string]ServiceHealth) string {
	healthyCount := 0
	totalCount := len(services)

	for _, service := range services {
		if service.Status == "healthy" {
			healthyCount++
		}
	}

	if healthyCount == totalCount {
		return "healthy"
	} else if healthyCount == 0 {
		return "unhealthy"
	} else {
		return "degraded"
	}
}

// logError 记录错误日志
func (m *SocialServiceManager) logError(message string, err error) {
	// TODO: 实现日志记录
	// logger.Error(message, "error", err)
}

// IsRunning 检查管理器是否正在运行
func (m *SocialServiceManager) IsRunning() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isRunning
}
