/* =================================
   Element Plus 表格统一样式
   ================================= */

/* 基础表格样式 */
.el-table {
    --el-table-header-bg-color: #f5f7fa;
    --el-table-row-hover-bg-color: #f5f7fa;
    --el-table-current-row-bg-color: #ecf5ff;
    --el-table-header-text-color: #909399;
    --el-table-text-color: #606266;
    --el-table-border-color: #ebeef5;

    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    /* 表头样式 */
    .el-table__header-wrapper {
        .el-table__header {
            th {
                background-color: var(--el-table-header-bg-color);
                font-weight: 600;
                color: var(--el-table-header-text-color);
                font-size: 14px;
                border-bottom: 2px solid var(--el-table-border-color);

                .cell {
                    padding: 12px 8px;
                    line-height: 1.4;
                    text-align: center;
                }
            }
        }
    }

    /* 表格主体样式 */
    .el-table__body-wrapper {
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 #f1f1f1;

        &::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;

            &:hover {
                background: #a8a8a8;
            }
        }

        .el-table__body {
            tr {
                transition: background-color 0.25s ease;

                &:hover {
                    background-color: var(--el-table-row-hover-bg-color) !important;
                }

                &.current-row {
                    background-color: var(--el-table-current-row-bg-color) !important;
                }

                td {
                    border-bottom: 1px solid #f0f0f0;

                    .cell {
                        padding: 12px 8px;
                        line-height: 1.5;
                        word-break: break-word;
                        font-size: 14px;
                        color: var(--el-table-text-color);
                    }
                }
            }
        }
    }

    /* 固定列样式 */
    .el-table__fixed-right {
        box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
    }

    .el-table__fixed {
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    }

    /* 空数据样式 */
    .el-table__empty-block {
        background-color: #fafafa;

        .el-table__empty-text {
            color: #999;
            font-size: 14px;
        }
    }
}

/* 特殊行样式 */
.el-table {

    /* 拖拽行样式 */
    .row-dragging {
        background-color: #e3f2fd !important;
        opacity: 0.8;
        transform: rotate(2deg);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);

        td {
            background-color: transparent !important;
        }
    }

    /* 外键行样式 */
    .foreign-key-row {
        background-color: #fff7e6 !important;

        &:hover {
            background-color: #fff3cd !important;
        }
    }

    /* 排除行样式 */
    .excluded-row {
        background-color: #f5f5f5 !important;
        opacity: 0.6;

        &:hover {
            background-color: #eeeeee !important;
        }
    }

    /* 选中行样式 */
    .el-table__row--selected {
        background-color: #ecf5ff !important;

        &:hover {
            background-color: #d9ecff !important;
        }
    }
}

/* 表格操作按钮样式 */
.table-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;

    .el-button {
        padding: 4px 8px;
        font-size: 12px;

        &.el-button--text,
        &.is-link {
            padding: 4px 6px;
            min-height: auto;
        }
    }
}

/* 表格内的标签样式 */
.table-tag {
    margin: 2px;
    font-size: 12px;

    &.status-tag {
        font-weight: 500;
    }
}

/* 表格内的头像样式 */
.table-avatar {
    margin-right: 8px;

    &.el-avatar {
        --el-avatar-size: 24px;
    }
}

/* 表格内的用户信息样式 */
.user-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .user-name {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
    }

    .user-id {
        font-size: 12px;
        color: #909399;
    }
}

/* 表格内容样式 */
.table-content {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
    max-height: 2.8em;

    &.single-line {
        -webkit-line-clamp: 1;
        max-height: 1.4em;
    }
}

/* 分页容器样式 */
.pagination-container {
    margin-top: 16px;
    padding: 16px 0;
    display: flex;
    justify-content: flex-end;

    .el-pagination {
        --el-pagination-button-color: #606266;
        --el-pagination-hover-color: #409eff;
    }
}

/* 响应式表格样式 */
@media (max-width: 768px) {
    .el-table {

        .el-table__header-wrapper,
        .el-table__body-wrapper {
            .cell {
                padding: 8px 4px;
                font-size: 12px;
            }
        }

        .table-actions {
            flex-direction: column;
            gap: 4px;

            .el-button {
                font-size: 11px;
                padding: 2px 6px;
            }
        }
    }

    .pagination-container {
        justify-content: center;

        .el-pagination {

            .el-pagination__sizes,
            .el-pagination__jump {
                display: none;
            }
        }
    }
}

/* 表格加载状态样式 */
.el-table .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
}

/* 表格排序样式 */
.el-table {
    .caret-wrapper {
        .sort-caret {
            &.ascending {
                border-bottom-color: #409eff;
            }

            &.descending {
                border-top-color: #409eff;
            }
        }
    }
}

/* 表格选择框样式 */
.el-table {
    .el-checkbox {
        --el-checkbox-checked-bg-color: #409eff;
        --el-checkbox-checked-border-color: #409eff;
    }
}

/* 表格展开行样式 */
.el-table {
    .el-table__expand-icon {
        color: #409eff;

        &:hover {
            color: #66b1ff;
        }
    }

    .el-table__expanded-cell {
        background-color: #fafafa;
        padding: 20px;

        .expanded-content {
            border-radius: 4px;
            background-color: #fff;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
    }
}

:deep(.el-table td.cell) {
    padding: 5px !important;
    text-align: left !important;
}

:deep(.el-table td.el-table__cell div) {
    display: flex;
    align-items: center;
    justify-content: center;
}