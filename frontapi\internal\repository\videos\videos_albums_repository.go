package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type VideosAlbumsRepository interface {
	base.ExtendedRepository[videos.VideoAlbum]
	UpdateVideosAlbumsStatus(ctx context.Context, id string, status int8) error
}

type videosAlbumsRepository struct {
	base.ExtendedRepository[videos.VideoAlbum]
}

func NewVideosAlbumsRepository(db *gorm.DB) VideosAlbumsRepository {
	repo := &videosAlbumsRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoAlbum](db),
	}
	// 设置调用者实例，确保能够使用自定义的条件应用逻辑
	repo.ExtendedRepository.SetCaller(repo)
	return repo
}

// ApplyConditions 实现ConditionApplier接口，提供自定义的条件应用逻辑
func (r *videosAlbumsRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	if condition == nil {
		return query
	}

	for key, value := range condition {
		// 跳过空值
		if r.isEmptyValue(value) {
			continue
		}

		switch key {
		case "keyword":
			// 关键词搜索：在标题和描述中搜索
			if str, ok := value.(string); ok && str != "" {
				query = query.Where("title LIKE ? OR description LIKE ?", "%"+str+"%", "%"+str+"%")
			}
		case "user_id":
			// 用户ID精确匹配
			query = query.Where("user_id = ?", value)
		case "category_id":
			// 分类ID精确匹配
			query = query.Where("category_id = ?", value)
		case "status":
			// 状态匹配
			if status, ok := value.(int); ok && status > -999 {
				query = query.Where("status = ?", status)
			}
		case "is_paid":
			// 付费类型匹配
			if isPaid, ok := value.(int); ok && isPaid >= 0 {
				query = query.Where("is_paid = ?", isPaid)
			}
		case "start_date":
			// 创建时间开始日期
			query = query.Where("created_at >= ?", value)
		case "end_date":
			// 创建时间结束日期
			query = query.Where("created_at <= ?", value)
		default:
			// 对于其他条件，使用精确匹配
			query = query.Where(key+" = ?", value)
		}
	}

	return query
}

// isEmptyValue 检查值是否为空
func (r *videosAlbumsRepository) isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}

	switch v := value.(type) {
	case string:
		return v == ""
	case int:
		return false // int 0 是有效值
	case int64:
		return false // int64 0 是有效值
	case float64:
		return false // float64 0 是有效值
	case bool:
		return false // bool false 是有效值
	default:
		return false
	}
}

func (r *videosAlbumsRepository) UpdateVideosAlbumsStatus(ctx context.Context, id string, status int8) error {
	if err := r.GetDBWithContext(ctx).Model(&videos.VideoAlbum{}).Where("id = ?", id).Update("status", status).Error; err != nil {
		return err
	}
	return nil
}
