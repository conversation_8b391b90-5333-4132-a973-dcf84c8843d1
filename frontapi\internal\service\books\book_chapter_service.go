package books

import (
	"context"
	"errors"
	"strings"
	"unicode/utf8"

	"frontapi/internal/models/books"
	bookRepo "frontapi/internal/repository/books"
	"frontapi/internal/service/base"
	bookValidator "frontapi/internal/validation/books"
	"frontapi/pkg/database"

	"gorm.io/gorm"
)

// BookChapterService 电子书章节服务接口
type BookChapterService interface {
	base.IExtendedService[books.BookChapter]
	CreateChapter(ctx context.Context, req *bookValidator.CreateChapterRequest) (*books.BookChapter, error)
	UpdateChapter(ctx context.Context, id string, req *bookValidator.UpdateChapterRequest) error
	GetChapter(ctx context.Context, id string) (*books.BookChapter, error)
	DeleteChapter(ctx context.Context, id string) error
	ListChaptersByBook(ctx context.Context, bookID string, page, pageSize int) ([]*books.BookChapter, int64, error)
	GetChapterByBookAndNumber(ctx context.Context, bookID string, chapterNumber int) (*books.BookChapter, error)
	ViewChapter(ctx context.Context, id string) error
	BatchCreateChapters(ctx context.Context, b *bookValidator.BatchUploadChaptersRequest) (int64, error)
	ReorderChapter(ctx context.Context, b *bookValidator.ReorderChapterRequest) error
	GetChapterList(ctx context.Context, condition map[string]interface{}, page int, size int) ([]*books.BookChapter, int64, error)
	BatchUpdateChapterOrder(ctx context.Context, bookID string, chapters []*bookValidator.ChapterOrderItem) error
}

type chapterService struct {
	*base.ExtendedService[books.BookChapter]
	chapterRepo bookRepo.ChapterRepository
	bookRepo    bookRepo.BookRepository
}

// NewChapterService 创建电子书章节服务实例
func NewChapterService(
	chapterRepo bookRepo.ChapterRepository,
	bookRepo bookRepo.BookRepository,
) BookChapterService {
	return &chapterService{
		ExtendedService: base.NewExtendedService[books.BookChapter](chapterRepo, "book_chapter"),
		chapterRepo:     chapterRepo,
		bookRepo:        bookRepo,
	}
}

// CreateChapter 创建电子书章节
func (s *chapterService) CreateChapter(ctx context.Context, req *bookValidator.CreateChapterRequest) (*books.BookChapter, error) {
	// 检查电子书是否存在
	book, err := s.bookRepo.FindByID(ctx, req.BookID)
	if err != nil {
		return nil, err
	}
	if book == nil {
		return nil, errors.New("电子书不存在")
	}

	// 如果未指定章节号，则获取最大章节号+1
	if req.ChapterNumber <= 0 {
		maxChapterNumber, err := s.chapterRepo.GetMaxChapterNumber(ctx, req.BookID)
		if err != nil {
			return nil, err
		}
		req.ChapterNumber = maxChapterNumber + 1
	} else {
		// 检查指定的章节号是否已存在
		existingChapter, err := s.chapterRepo.FindByBookAndChapterNumber(ctx, req.BookID, req.ChapterNumber)
		if err != nil {
			return nil, err
		}
		if existingChapter != nil {
			return nil, errors.New("指定的章节序号已存在")
		}
	}

	// 计算内容的字数
	wordCount := countWords(req.Content)

	chapter := &books.BookChapter{
		BookID:        req.BookID,
		BookName:      req.BookName,
		Title:         req.Title,
		ChapterNumber: req.ChapterNumber,
		Content:       req.Content,
		WordCount:     wordCount,
		IsLocked:      int(req.IsLocked),
		Price:         req.Price,
		Status:        1, // 默认状态为正常
	}

	err = s.chapterRepo.Create(ctx, chapter)
	if err != nil {
		return nil, err
	}

	// 更新电子书的章节数
	err = s.bookRepo.IncrementChapterCount(ctx, req.BookID)
	if err != nil {
		return nil, err
	}

	// 更新电子书的总字数
	totalWordCount, err := s.chapterRepo.GetTotalWordCount(ctx, req.BookID)
	if err != nil {
		return nil, err
	}
	err = s.bookRepo.UpdateWordCount(ctx, req.BookID, totalWordCount)
	if err != nil {
		return nil, err
	}

	return chapter, nil
}

// UpdateChapter 更新电子书章节
func (s *chapterService) UpdateChapter(ctx context.Context, id string, req *bookValidator.UpdateChapterRequest) error {
	chapter, err := s.chapterRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if chapter == nil {
		return errors.New("章节不存在")
	}

	// 更新字段
	if req.Title != "" {
		chapter.Title = req.Title
	}
	if req.BookName != "" {
		chapter.BookName = req.BookName
	}

	// 如果内容更新，重新计算字数
	if req.Content != "" {
		chapter.Content = req.Content
		chapter.WordCount = countWords(req.Content)
	}

	if req.IsLocked != nil {
		chapter.IsLocked = int(*req.IsLocked)
	}

	if req.Price > 0 {
		chapter.Price = req.Price
	}

	if req.Status != nil {
		chapter.Status = int(*req.Status)
	}

	err = s.chapterRepo.Update(ctx, chapter)
	if err != nil {
		return err
	}

	// 更新电子书的总字数
	totalWordCount, err := s.chapterRepo.GetTotalWordCount(ctx, chapter.BookID)
	if err != nil {
		return err
	}
	return s.bookRepo.UpdateWordCount(ctx, chapter.BookID, totalWordCount)
}

// GetChapter 获取电子书章节详情
func (s *chapterService) GetChapter(ctx context.Context, id string) (*books.BookChapter, error) {
	return s.GetByID(ctx, id, true)
}

// DeleteChapter 删除电子书章节
func (s *chapterService) DeleteChapter(ctx context.Context, id string) error {
	chapter, err := s.chapterRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if chapter == nil {
		return errors.New("章节不存在")
	}

	bookID := chapter.BookID

	err = s.chapterRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	// 更新章节数
	chapterCount, err := s.chapterRepo.GetChapterCount(ctx, bookID)
	if err != nil {
		return err
	}

	// 更新电子书的总字数
	totalWordCount, err := s.chapterRepo.GetTotalWordCount(ctx, bookID)
	if err != nil {
		return err
	}

	err = s.bookRepo.UpdateWordCount(ctx, bookID, totalWordCount)
	if err != nil {
		return err
	}

	// 更新电子书的章节数量
	// 此处需要手动维护章节数，因为已经删除了一个章节
	book, err := s.bookRepo.FindByID(ctx, bookID)
	if err != nil {
		return err
	}

	book.ChapterCount = int(chapterCount)
	return s.bookRepo.Update(ctx, book)
}

// ListChaptersByBook 获取电子书章节列表
func (s *chapterService) ListChaptersByBook(ctx context.Context, bookID string, page, pageSize int) ([]*books.BookChapter, int64, error) {
	return s.chapterRepo.ListByBook(ctx, bookID, page, pageSize)
}

// GetChapterByBookAndNumber 根据电子书ID和章节序号获取章节
func (s *chapterService) GetChapterByBookAndNumber(ctx context.Context, bookID string, chapterNumber int) (*books.BookChapter, error) {
	chapter, err := s.chapterRepo.FindByBookAndChapterNumber(ctx, bookID, chapterNumber)
	if err != nil {
		return nil, err
	}
	if chapter == nil {
		return nil, errors.New("章节不存在")
	}
	return chapter, nil
}

// ViewChapter 记录查看章节
func (s *chapterService) ViewChapter(ctx context.Context, id string) error {
	chapter, err := s.GetByID(ctx, id, false)
	if err != nil {
		return err
	}

	// 增加章节阅读数
	err = s.UpdateCount(ctx, id, "read_count", 1)
	if err != nil {
		return err
	}

	// 增加电子书阅读数
	return s.bookRepo.IncrementReadCount(ctx, chapter.BookID)
}

// countWords 计算字数
func countWords(content string) int {
	// 简单的字数统计，按UTF-8字符计算
	content = strings.ReplaceAll(content, "\n", "") // 去除换行符
	content = strings.ReplaceAll(content, "\r", "") // 去除回车符
	content = strings.ReplaceAll(content, "\t", "") // 去除制表符
	content = strings.ReplaceAll(content, " ", "")  // 去除空格
	return utf8.RuneCountInString(content)
}

// 批量更新章节
func (s *chapterService) BatchCreateChapters(ctx context.Context, b *bookValidator.BatchUploadChaptersRequest) (int64, error) {
	// 使用事务
	var totalWordCount int64
	err := database.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 遍历所有章节
		for _, chapter := range b.Chapters {
			// 检查指定的章节号是否已存在
			// 设置章节的电子书ID
			chapter.BookID = b.BookID

			// 创建章节
			nChapter, err := s.CreateChapter(ctx, chapter)
			if err != nil {
				return err
			}

			// 更新电子书章节数
			if err := s.bookRepo.IncrementChapterCount(ctx, b.BookID); err != nil {
				return err
			}
			// 累加字数
			totalWordCount += int64(nChapter.WordCount)
		}

		// 更新电子书总字数
		if err := s.bookRepo.UpdateWordCount(ctx, b.BookID, uint64(totalWordCount)); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	// 返回总字数
	return totalWordCount, nil
}

// 重新排序章节
func (s *chapterService) ReorderChapter(ctx context.Context, b *bookValidator.ReorderChapterRequest) error {
	// 使用事务处理
	return database.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 检查电子书是否存在
		book, err := s.bookRepo.FindByID(ctx, b.BookID)
		if err != nil {
			return err
		}
		if book == nil {
			return errors.New("电子书不存在")
		}

		// 获取旧章节信息
		oldChapter, err := s.chapterRepo.FindByID(ctx, b.OldChapterID)
		if err != nil {
			return err
		}
		if oldChapter == nil {
			return errors.New("旧章节不存在")
		}

		// 获取新章节信息
		newChapter, err := s.chapterRepo.FindByID(ctx, b.NewChapterID)
		if err != nil {
			return err
		}
		if newChapter == nil {
			return errors.New("新章节不存在")
		}

		// 检查章节是否属于同一本书
		if oldChapter.BookID != b.BookID || newChapter.BookID != b.BookID {
			return errors.New("章节不属于指定的电子书")
		}

		// 获取所有需要调整的章节
		chapters, _, err := s.chapterRepo.ListByBook(ctx, b.BookID, 1, 1000) // 假设一本书最多1000章
		if err != nil {
			return err
		}

		// 调整章节序号
		oldNum := oldChapter.ChapterNumber
		newNum := b.NewChapterNum

		// 更新其他章节的序号
		for _, chapter := range chapters {
			if oldNum < newNum { // 向后移动
				if chapter.ChapterNumber > oldNum && chapter.ChapterNumber <= newNum {
					chapter.ChapterNumber--
					if err := s.chapterRepo.Update(ctx, chapter); err != nil {
						return err
					}
				}
			} else if oldNum > newNum { // 向前移动
				if chapter.ChapterNumber >= newNum && chapter.ChapterNumber < oldNum {
					chapter.ChapterNumber++
					if err := s.chapterRepo.Update(ctx, chapter); err != nil {
						return err
					}
				}
			}
		}

		// 更新目标章节的序号
		oldChapter.ChapterNumber = newNum
		return s.chapterRepo.Update(ctx, oldChapter)
	})
}

// GetChapterList 获取章节列表
func (s *chapterService) GetChapterList(ctx context.Context, condition map[string]interface{}, page int, size int) ([]*books.BookChapter, int64, error) {
	return s.List(ctx, condition, "", page, size, true)
}

// BatchUpdateChapterOrder 批量更新章节排序
func (s *chapterService) BatchUpdateChapterOrder(ctx context.Context, bookID string, chapters []*bookValidator.ChapterOrderItem) error {
	// 检查电子书是否存在
	book, err := s.bookRepo.FindByID(ctx, bookID)
	if err != nil {
		return err
	}
	if book == nil {
		return errors.New("电子书不存在")
	}

	// 转换为repository层所需的格式
	updates := make([]map[string]interface{}, 0, len(chapters))
	for _, chapter := range chapters {
		update := map[string]interface{}{
			"id":             chapter.ID,
			"chapter_number": chapter.ChapterNumber,
		}
		updates = append(updates, update)
	}

	// 调用repository层方法批量更新
	err = s.chapterRepo.BatchUpdateChapterOrder(ctx, bookID, updates)
	if err != nil {
		return err
	}

	return nil
}
