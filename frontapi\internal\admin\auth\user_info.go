package auth

import (
	"frontapi/pkg/utils"

	"github.com/gofiber/fiber/v2"
)

// UserInfoController 用户信息控制器（简化版，使用模拟数据）
type UserInfoController struct {
}

// NewUserInfoController 创建用户信息控制器实例
func NewUserInfoController() *UserInfoController {
	return &UserInfoController{}
}

// GetUserInfo 获取用户信息
func (c *UserInfoController) GetUserInfo(ctx *fiber.Ctx) error {
	// 从上下文中获取用户ID
	userID := ctx.Locals("userId")
	if userID == nil {
		return utils.BadRequest(ctx, "未登录或登录已过期", 401)
	}

	// 模拟用户信息
	user := fiber.Map{
		"ID":       1,
		"Username": "admin",
		"Nickname": "管理员",
		"Avatar":   "/avatar/admin.png",
		"Email":    "<EMAIL>",
		"Phone":    "13800138000",
	}

	// 模拟用户角色代码
	roleCodes := []string{"admin", "user"}

	// 模拟用户权限
	permissions := []string{
		"user:view",
		"user:create",
		"user:edit",
		"user:delete",
		"role:view",
		"role:create",
		"role:edit",
		"role:delete",
	}

	// 模拟用户菜单
	menus := []fiber.Map{
		{
			"id":    1,
			"name":  "Dashboard",
			"path":  "/dashboard",
			"icon":  "DashboardOutlined",
			"title": "仪表盘",
		},
		{
			"id":    2,
			"name":  "System",
			"path":  "/system",
			"icon":  "SettingOutlined",
			"title": "系统管理",
		},
	}

	// 构建响应数据
	response := fiber.Map{
		"id":          user["ID"],
		"username":    user["Username"],
		"nickname":    user["Nickname"],
		"avatar":      user["Avatar"],
		"email":       user["Email"],
		"phone":       user["Phone"],
		"roles":       roleCodes,
		"permissions": permissions,
		"menus":       menus,
		"homePath":    "/workspace",
	}

	return utils.SendOk(ctx, response, 0, "获取用户信息成功")
}
