import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from "@/locales";

const routes: GeneratedRoute[] = [
  {
    name: 'users',
    path: '/users',
    component: 'layout.base',
    meta: {
      title: 'users',
      i18nKey: 'route.users',
      icon: 'lucide:users',
      order: 2
    },
    children: [
      {
        name: 'users_list',
        path: '/users/list',
        component: 'view.users_list',
        meta: {
          title: 'users_list',
          i18nKey: 'route.users_list',
          icon: 'lucide:list'
        }
      },
      // 用户登录日志
      {
        name: 'users_login-logs',
        path: '/users/login-logs',
        component: 'view.users_login-logs',
        meta: {
          title: 'users_login-logs',
          i18nKey: 'route.users_login-logs',
          icon: 'lucide:history'
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  }
];

export default routes;
