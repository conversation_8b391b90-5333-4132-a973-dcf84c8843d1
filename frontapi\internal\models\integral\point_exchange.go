package integral

import (
	"frontapi/internal/models"
)

type PointExchange struct {
	models.BaseModelStruct
	UserID        string `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	Points        int    `json:"points" gorm:"not null;comment:兑换积分数量"`
	ExchangeType  string `json:"exchange_type" gorm:"type:string;size:20;not null;comment:兑换类型：coin-平台币，vip-会员"`
	ExchangeValue int    `json:"exchange_value" gorm:"not null;comment:兑换获得的数量"`
	Status        string `json:"status" gorm:"type:string;size:20;not null;default:'success';comment:状态：pending-处理中，success-成功，failed-失败"`
}

func (PointExchange) TableName() string {
	return "ly_point_exchanges"
}
