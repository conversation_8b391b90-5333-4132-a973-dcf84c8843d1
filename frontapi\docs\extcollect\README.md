# 收藏服务 (ExtCollect) - 高性能收藏功能

## 📋 项目概述

ExtCollect是一个高性能、可扩展的收藏服务基础库，支持多种存储策略和缓存方案。该服务专为视频、短视频、帖子、图片、电子书、漫画等内容收藏场景设计，可以轻松集成到各种业务系统中。

## 🏗️ 架构设计

### 核心模块

```
frontapi/internal/service/base/extcollect/
├── 📁 types/              # 类型定义
│   └── types.go          # 完整的数据类型和接口定义
├── 📁 redis/v2/          # Redis适配器
│   └── adapter.go        # Redis适配器实现
├── 📁 mongodb/           # MongoDB适配器
│   └── adapter.go        # MongoDB适配器实现
├── interfaces.go         # 接口定义
├── config.go             # 配置定义
├── service.go            # 主服务实现
└── factory.go            # 工厂模式创建器
```

## 🚀 主要特性

### 1. **多存储策略支持**
- ✅ `RedisOnly`: 仅使用Redis，高性能缓存方案
- ✅ `MongoOnly`: 仅使用MongoDB，持久化存储方案  
- ✅ `RedisFirst`: Redis优先，MongoDB作为备份存储
- ✅ `MongoFirst`: MongoDB优先，Redis作为缓存层
- ✅ `DualWrite`: 双写模式，同时写入Redis和MongoDB

### 2. **系统配置集成**
- ✅ 自动检测系统Redis/MongoDB配置
- ✅ 支持使用系统现有连接池
- ✅ 支持独立配置，不影响系统其他模块
- ✅ 配置优先级：自定义 > 系统 > 默认

### 3. **高性能Redis实现**
- ✅ Pipeline批量操作，减少网络往返
- ✅ Hash存储：用户收藏状态映射
- ✅ Set存储：项目收藏用户集合
- ✅ ZSet存储：热门排行榜和收藏历史
- ✅ 智能TTL管理，自动过期清理

### 4. **MongoDB持久化存储**
- ✅ BulkWrite批量操作优化
- ✅ 聚合管道高效统计查询
- ✅ 版本控制和冲突解决
- ✅ 索引优化查询性能

### 5. **完整的功能覆盖**
- ✅ 基础操作：收藏、取消收藏、检查状态、获取数量
- ✅ 批量操作：支持高并发场景的批量处理
- ✅ 查询功能：用户收藏列表、项目收藏者、收藏历史
- ✅ 排行榜：热门收藏排名，趋势分析
- ✅ 统计分析：用户统计、项目统计、全局统计

## 📖 快速开始

### 1. 基础使用

#### 创建服务实例

```go
package main

import (
    "context"
    "log"
    
    "frontapi/internal/service/base/extcollect"
)

func main() {
    // 使用默认配置创建服务
    service, err := extcollect.CreateDefaultService()
    if err != nil {
        log.Fatal("创建收藏服务失败:", err)
    }
    defer service.Close()
    
    ctx := context.Background()
    
    // 基础操作示例
    userID := "user123"
    itemID := "video456"
    itemType := "video"
    
    // 收藏
    err = service.Collect(ctx, userID, itemID, itemType)
    if err != nil {
        log.Printf("收藏失败: %v", err)
    }
    
    // 检查收藏状态
    isCollected, err := service.IsCollected(ctx, userID, itemID, itemType)
    if err != nil {
        log.Printf("检查收藏状态失败: %v", err)
    }
    log.Printf("收藏状态: %t", isCollected)
    
    // 获取收藏数量
    count, err := service.GetCollectCount(ctx, itemID, itemType)
    if err != nil {
        log.Printf("获取收藏数量失败: %v", err)
    }
    log.Printf("收藏数量: %d", count)
}
```

#### 自定义配置使用

```go
// 创建自定义配置
config := &extcollect.Config{
    ServiceName: "my-collect-service",
    Strategy:    extcollect.RedisFirst,
    Debug:       true,
}

// 使用系统Redis和MongoDB
config.Redis.UseSystem = true
config.MongoDB.UseSystem = true

// 创建服务
service, err := extcollect.NewCollectService(config)
if err != nil {
    log.Fatal("创建服务失败:", err)
}
```

### 2. 高级功能

#### 批量操作

```go
// 批量收藏操作
operations := []*extcollect.CollectOperation{
    {
        UserID:    "user123",
        ItemID:    "video1",
        ItemType:  "video",
        Action:    "collect",
        Timestamp: time.Now(),
    },
    {
        UserID:    "user123", 
        ItemID:    "video2",
        ItemType:  "video",
        Action:    "collect",
        Timestamp: time.Now(),
    },
}

err := service.BatchCollect(ctx, operations)
if err != nil {
    log.Printf("批量收藏失败: %v", err)
}

// 批量获取收藏状态
items := map[string]string{
    "video1": "video",
    "video2": "video", 
    "video3": "video",
}

statuses, err := service.BatchGetCollectStatus(ctx, "user123", items)
if err != nil {
    log.Printf("批量获取状态失败: %v", err)
}

for itemID, isCollected := range statuses {
    log.Printf("项目 %s 收藏状态: %t", itemID, isCollected)
}
```

#### 查询功能

```go
// 获取用户收藏列表
userCollects, err := service.GetUserCollects(ctx, "user123", "video", 10, 0)
if err != nil {
    log.Printf("获取用户收藏失败: %v", err)
}

// 获取项目收藏者列表  
itemCollectors, err := service.GetItemCollectors(ctx, "video456", "video", 10, 0)
if err != nil {
    log.Printf("获取收藏者失败: %v", err)
}

// 获取收藏历史
timeRange := &extcollect.TimeRange{
    Start: time.Now().AddDate(0, -1, 0), // 一个月前
    End:   time.Now(),
}
history, err := service.GetCollectHistory(ctx, "user123", "video", timeRange)
if err != nil {
    log.Printf("获取收藏历史失败: %v", err)
}
```

#### 热门排行和统计

```go
// 更新热度分数
err = service.UpdateHotRank(ctx, "video456", "video", 85.6)
if err != nil {
    log.Printf("更新热度失败: %v", err)
}

// 获取热门排行
hotRanking, err := service.GetHotRanking(ctx, "video", 10)
if err != nil {
    log.Printf("获取排行失败: %v", err)
}

// 获取用户收藏统计
userStats, err := service.GetUserCollectStats(ctx, "user123")
if err != nil {
    log.Printf("获取用户统计失败: %v", err)
}
log.Printf("用户总收藏数: %d", userStats.TotalCollects)

// 获取项目收藏统计
itemStats, err := service.GetItemCollectStats(ctx, "video456", "video")
if err != nil {
    log.Printf("获取项目统计失败: %v", err)
}
log.Printf("项目总收藏数: %d", itemStats.TotalCollects)
```

## ⚙️ 配置说明

### 存储策略选择

#### RedisOnly - 仅Redis
- **适用场景**: 高并发、低延迟场景，数据可丢失
- **优势**: 性能极高，延迟极低
- **劣势**: 数据不持久，重启丢失

```go
config := &extcollect.Config{
    Strategy: extcollect.RedisOnly,
}
```

#### MongoOnly - 仅MongoDB  
- **适用场景**: 数据持久化要求高，并发量适中
- **优势**: 数据持久化，支持复杂查询
- **劣势**: 性能相对较低

```go
config := &extcollect.Config{
    Strategy: extcollect.MongoOnly,
}
```

#### RedisFirst - Redis优先
- **适用场景**: 平衡性能和持久化，推荐方案
- **优势**: 读写高性能，数据有备份
- **劣势**: 配置复杂，需要同步机制

```go
config := &extcollect.Config{
    Strategy: extcollect.RedisFirst,
}
```

#### DualWrite - 双写模式
- **适用场景**: 高可用要求，数据一致性要求高
- **优势**: 数据强一致，故障转移快
- **劣势**: 写入性能降低，资源消耗大

```go
config := &extcollect.Config{
    Strategy: extcollect.DualWrite,
}
```

### 详细配置选项

```go
config := &extcollect.Config{
    ServiceName: "collect-service",
    Strategy:    extcollect.RedisFirst,
    Debug:       false,
    
    // Redis配置
    Redis: struct {
        Enabled   bool
        UseSystem bool
        Client    goredis.UniversalClient
        Config    *RedisConfig
    }{
        Enabled:   true,
        UseSystem: true, // 使用系统Redis配置
        Config: &extcollect.RedisConfig{
            Host:        "localhost",
            Port:        6379,
            Password:    "",
            DB:          0,
            KeyPrefix:   "collect:",
            DefaultTTL:  24 * time.Hour,
            CollectTTL:  7 * 24 * time.Hour,
            CountTTL:    1 * time.Hour,
            RankingTTL:  30 * time.Minute,
        },
    },
    
    // MongoDB配置
    MongoDB: struct {
        Enabled   bool
        UseSystem bool
        Client    *mongo.Client
        Database  *mongo.Database
        Config    *mongoConfig.Config
    }{
        Enabled:   true,
        UseSystem: true, // 使用系统MongoDB配置
        Config: &mongoConfig.Config{
            URI:      "mongodb://localhost:27017",
            Database: "frontapi_collect",
        },
    },
    
    // 性能配置
    Performance: struct {
        BatchSize       int
        SyncInterval    time.Duration
        CacheExpiration time.Duration
        MaxRetries      int
        RetryDelay      time.Duration
        MaxConcurrency  int
        TimeoutDuration time.Duration
    }{
        BatchSize:       1000,
        SyncInterval:    5 * time.Minute,
        CacheExpiration: 1 * time.Hour,
        MaxRetries:      3,
        RetryDelay:      1 * time.Second,
        MaxConcurrency:  100,
        TimeoutDuration: 10 * time.Second,
    },
    
    // 监控配置
    Monitoring: struct {
        Enabled            bool
        MetricsInterval    time.Duration
        HealthCheckPeriod  time.Duration
        LogSlowQueries     bool
        SlowQueryThreshold time.Duration
    }{
        Enabled:            true,
        MetricsInterval:    1 * time.Minute,
        HealthCheckPeriod:  30 * time.Second,
        LogSlowQueries:     true,
        SlowQueryThreshold: 100 * time.Millisecond,
    },
}
```

## 🔧 性能优化

### Redis优化策略

#### 1. Pipeline批量操作
```go
// 自动使用Pipeline优化批量操作
operations := make([]*extcollect.CollectOperation, 1000)
// ... 填充操作
err := service.BatchCollect(ctx, operations) // 内部自动使用Pipeline
```

#### 2. 键名设计优化
```go
// 键名层次结构
collect:user:{userID}:{itemType}        // 用户收藏Set
collect:item:{itemType}:{itemID}:collectors  // 项目收藏者Set
collect:count:{itemType}:{itemID}       // 收藏计数
collect:ranking:{itemType}              // 热门排行ZSet
collect:history:{userID}:{itemType}     // 收藏历史ZSet
```

#### 3. TTL策略
```go
// 不同数据的TTL设置
CollectTTL:  7 * 24 * time.Hour,  // 收藏关系7天
CountTTL:    1 * time.Hour,       // 计数缓存1小时  
RankingTTL:  30 * time.Minute,    // 排行榜30分钟
```

### MongoDB优化策略

#### 1. 索引优化
```javascript
// 建议的MongoDB索引
db.collects.createIndex({user_id: 1, item_type: 1, timestamp: -1})
db.collects.createIndex({item_id: 1, item_type: 1, status: 1})
db.collects.createIndex({item_type: 1, timestamp: -1})
db.collects.createIndex({timestamp: -1})
```

#### 2. 聚合管道优化
```go
// 使用聚合管道进行高效统计
pipeline := []bson.M{
    {"$match": bson.M{"item_type": itemType, "status": "collected"}},
    {"$group": bson.M{
        "_id": "$item_id",
        "collect_count": bson.M{"$sum": 1},
        "unique_users": bson.M{"$addToSet": "$user_id"},
    }},
    {"$sort": bson.M{"collect_count": -1}},
    {"$limit": limit},
}
```

### 缓存策略优化

#### 1. 缓存预热
```go
// 预热热门内容缓存
hotItems := []string{"item1", "item2", "item3"}
err := service.WarmupCache(ctx, "video", hotItems)
```

#### 2. 缓存失效
```go
// 主动失效相关缓存
keys := []string{
    "collect:count:video:123",
    "collect:ranking:video",
}
err := service.InvalidateCache(ctx, keys...)
```

## 📊 监控与运维

### 健康检查

```go
// 基础健康检查
err := service.HealthCheck(ctx)
if err != nil {
    log.Printf("服务不健康: %v", err)
}

// 获取详细指标
metrics, err := service.GetMetrics(ctx)
if err != nil {
    log.Printf("获取指标失败: %v", err)
}

// 指标包含以下信息:
// - 服务基础信息(名称、版本、运行时间)
// - 请求统计(总请求数、成功率、错误率)
// - 性能指标(平均响应时间、QPS)
// - 资源使用(内存、CPU、协程数)
// - 存储统计(Redis、MongoDB状态)
```

### 缓存统计

```go
// 获取缓存统计信息
cacheStats, err := service.GetCacheStats(ctx)
if err != nil {
    log.Printf("获取缓存统计失败: %v", err)
}

// 缓存统计包含:
// - 命中率统计
// - 内存使用情况  
// - 键数量统计
// - 驱逐计数
```

### 数据同步状态

```go
// 获取数据同步状态(双写模式)
syncStatus, err := service.GetSyncStatus(ctx)
if err != nil {
    log.Printf("获取同步状态失败: %v", err)
}

// 同步状态信息:
// - 最后同步时间
// - 同步进度
// - 错误统计
// - 预计剩余时间
```

## 🛠️ 最佳实践

### 1. 服务集成

#### 在视频服务中集成收藏功能

```go
type VideoService struct {
    // ... 原有字段
    collectSvc extcollect.ExtendedCollectService
}

func NewVideoService() *VideoService {
    collectSvc, err := extcollect.CreateDefaultService()
    if err != nil {
        log.Fatal("创建收藏服务失败:", err)
    }
    
    return &VideoService{
        // ... 原有初始化
        collectSvc: collectSvc,
    }
}

// 获取带收藏信息的视频
func (s *VideoService) GetVideoWithCollectInfo(ctx context.Context, videoID, userID string) (*VideoInfo, error) {
    video, err := s.GetVideo(ctx, videoID)
    if err != nil {
        return nil, err
    }
    
    // 并发获取收藏信息
    var isCollected bool
    var collectCount int64
    var wg sync.WaitGroup
    var mu sync.Mutex
    
    wg.Add(2)
    
    // 获取收藏状态
    go func() {
        defer wg.Done()
        collected, err := s.collectSvc.IsCollected(ctx, userID, videoID, "video")
        if err == nil {
            mu.Lock()
            isCollected = collected
            mu.Unlock()
        }
    }()
    
    // 获取收藏数量
    go func() {
        defer wg.Done()
        count, err := s.collectSvc.GetCollectCount(ctx, videoID, "video")
        if err == nil {
            mu.Lock()
            collectCount = count
            mu.Unlock()
        }
    }()
    
    wg.Wait()
    
    return &VideoInfo{
        Video:        video,
        IsCollected:  isCollected,
        CollectCount: collectCount,
    }, nil
}
```

### 2. 错误处理

```go
// 优雅的错误处理
func handleCollectError(err error) {
    switch {
    case errors.Is(err, context.Canceled):
        log.Println("操作被取消")
    case errors.Is(err, context.DeadlineExceeded):
        log.Println("操作超时")
    case strings.Contains(err.Error(), "connection"):
        log.Println("连接错误，可能需要重试")
    default:
        log.Printf("未知错误: %v", err)
    }
}
```

### 3. 性能监控

```go
// 添加性能监控中间件
func withMetrics(service extcollect.ExtendedCollectService) extcollect.ExtendedCollectService {
    return &metricsWrapper{service: service}
}

type metricsWrapper struct {
    service extcollect.ExtendedCollectService
}

func (m *metricsWrapper) Collect(ctx context.Context, userID, itemID, itemType string) error {
    start := time.Now()
    err := m.service.Collect(ctx, userID, itemID, itemType)
    
    // 记录指标
    duration := time.Since(start)
    if duration > 100*time.Millisecond {
        log.Printf("慢查询: Collect took %v", duration)
    }
    
    return err
}
```

## 🔍 故障排查

### 常见问题

#### 1. Redis连接问题
```go
// 检查Redis连接
err := service.HealthCheck(ctx)
if err != nil {
    // 检查Redis配置
    // 检查网络连接
    // 检查认证信息
}
```

#### 2. MongoDB连接问题
```go
// 检查MongoDB状态
metrics, err := service.GetMetrics(ctx)
if err == nil {
    mongoStats := metrics.Storage["mongodb"]
    // 检查连接状态
    // 检查性能指标
}
```

#### 3. 缓存命中率低
```go
// 分析缓存统计
cacheStats, err := service.GetCacheStats(ctx)
if err == nil {
    hitRate := cacheStats["hit_rate"].(float64)
    if hitRate < 0.8 {
        // 考虑调整TTL
        // 考虑预热策略
        // 考虑缓存键设计
    }
}
```

#### 4. 性能问题调优
```go
// 监控关键指标
metrics, err := service.GetMetrics(ctx)
if err == nil {
    avgResponseTime := metrics.AvgResponseTime
    if avgResponseTime > 100*time.Millisecond {
        // 检查批量操作大小
        // 检查并发设置
        // 检查网络延迟
    }
}
```

## 📈 扩展开发

### 自定义适配器

```go
// 实现自定义存储适配器
type CustomAdapter struct {
    // 自定义存储实现
}

func (c *CustomAdapter) Collect(ctx context.Context, userID, itemID, itemType string) error {
    // 自定义收藏逻辑
    return nil
}

// 实现所有 CollectAdapter 接口方法...
```

### 中间件扩展

```go
// 自定义中间件
func WithRateLimit(service extcollect.ExtendedCollectService, rps int) extcollect.ExtendedCollectService {
    return &rateLimitWrapper{
        service: service,
        limiter: rate.NewLimiter(rate.Limit(rps), rps),
    }
}
```

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 基础收藏功能实现
- ✅ 多存储策略支持
- ✅ Redis和MongoDB适配器
- ✅ 批量操作优化
- ✅ 完整的统计功能
- ✅ 配置系统集成
- ✅ 健康检查和监控

### 后续规划
- 🔄 数据同步调度器
- 🔄 业务集成混入模式
- 🔄 Prometheus指标集成
- 🔄 分布式锁支持
- 🔄 更多存储后端支持

---

**注意**: 本文档基于ExtCollect v1.0.0版本编写，使用前请确保版本兼容性。 