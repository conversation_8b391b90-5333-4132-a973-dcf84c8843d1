import { ref, onUnmounted } from 'vue'
import Player from 'xgplayer'

export interface PlayerConfig {
  el: HTMLElement
  url: string
  poster?: string
  width?: number | string
  height?: number | string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  playsinline?: boolean
  controls?: boolean
  fluid?: boolean
  onPlay?: () => void
  onPause?: () => void
  onEnded?: () => void
  onError?: (error: any) => void
  onLoadedMetadata?: () => void
  onTimeUpdate?: (currentTime: number) => void
}

export function useVideoPlayer() {
  // 播放器实例
  const player = ref<Player | null>(null)
  
  // 播放器状态
  const isPlaying = ref(false)
  const isMuted = ref(false)
  const volume = ref(0.6)
  const currentTime = ref(0)
  const totalDuration = ref(0)
  const isFullscreen = ref(false)

  // 初始化播放器
  const initPlayer = async (config: PlayerConfig): Promise<void> => {
    try {
      // 销毁现有播放器
      if (player.value) {
        destroyPlayer()
      }

      // 创建新播放器实例
      player.value = new Player({
        el: config.el,
        url: config.url,
        poster: config.poster,
        width: config.width,
        height: config.height,
        autoplay: config.autoplay,
        muted: config.muted,
        loop: config.loop,
        playsinline: config.playsinline,
        controls: config.controls,
        fluid: config.fluid
      })

      // 绑定事件
      if (player.value) {
        player.value.on('play', () => {
          isPlaying.value = true
          config.onPlay?.()
        })

        player.value.on('pause', () => {
          isPlaying.value = false
          config.onPause?.()
        })

        player.value.on('ended', () => {
          isPlaying.value = false
          config.onEnded?.()
        })

        player.value.on('error', (error: any) => {
          isPlaying.value = false
          config.onError?.(error)
        })

        player.value.on('loadedmetadata', () => {
          if (player.value) {
            totalDuration.value = player.value.duration || 0
            volume.value = player.value.volume || 0.6
            isMuted.value = player.value.muted || false
          }
          config.onLoadedMetadata?.()
        })

        player.value.on('timeupdate', () => {
          if (player.value) {
            currentTime.value = player.value.currentTime || 0
            config.onTimeUpdate?.(currentTime.value)
          }
        })
      }

    } catch (error) {
      console.error('播放器初始化失败:', error)
      throw error
    }
  }

  // 销毁播放器
  const destroyPlayer = () => {
    if (player.value) {
      try {
        player.value.destroy()
      } catch (error) {
        console.warn('播放器销毁时出错:', error)
      }
      player.value = null
    }
    
    // 重置状态
    isPlaying.value = false
    isMuted.value = false
    currentTime.value = 0
    totalDuration.value = 0
    isFullscreen.value = false
  }

  // 播放
  const play = () => {
    if (player.value && !isPlaying.value) {
      player.value.play()
    }
  }

  // 暂停
  const pause = () => {
    if (player.value && isPlaying.value) {
      player.value.pause()
    }
  }

  // 静音
  const mute = () => {
    if (player.value) {
      player.value.muted = true
      isMuted.value = true
    }
  }

  // 取消静音
  const unmute = () => {
    if (player.value) {
      player.value.muted = false
      isMuted.value = false
    }
  }

  // 设置音量
  const setVolume = (newVolume: number) => {
    if (player.value && newVolume >= 0 && newVolume <= 1) {
      player.value.volume = newVolume
      volume.value = newVolume
      
      // 如果音量大于0，自动取消静音
      if (newVolume > 0 && isMuted.value) {
        unmute()
      }
    }
  }

  // 跳转到指定时间
  const seek = (time: number) => {
    if (player.value && time >= 0 && time <= totalDuration.value) {
      player.value.currentTime = time
      currentTime.value = time
    }
  }

  // 切换全屏
  const toggleFullscreen = () => {
    if (player.value) {
      if (isFullscreen.value) {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen()
        }
      } else {
        // 进入全屏
        const element = player.value.video || player.value.el
        if (element && element.requestFullscreen) {
          element.requestFullscreen()
        }
      }
    }
  }

  // 获取播放器实例
  const getPlayer = () => player.value

  // 组件卸载时自动清理
  onUnmounted(() => {
    destroyPlayer()
  })

  return {
    // 播放器实例
    player,
    
    // 状态
    isPlaying,
    isMuted,
    volume,
    currentTime,
    totalDuration,
    isFullscreen,
    
    // 方法
    initPlayer,
    destroyPlayer,
    play,
    pause,
    mute,
    unmute,
    setVolume,
    seek,
    toggleFullscreen,
    getPlayer
  }
} 