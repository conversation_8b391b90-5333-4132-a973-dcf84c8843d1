package middleware

import (
	"frontapi/config"
	"frontapi/pkg/utils"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
)

// Cors 返回增强版的CORS中间件，能正确处理预检请求
func Cors() fiber.Handler {
	// 从环境变量获取CORS配置，添加前端地址9527端口
	// allowOrigins := utils.GetEnv("CORS_ALLOW_ORIGINS", "http://localhost:9527,http://localhost:8080,http://localhost:8081,http://localhost:3000,http://127.0.0.1:9527")
	// allowMethods := utils.GetEnv("CORS_ALLOW_METHODS", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
	// allowHeaders := utils.GetEnv("CORS_ALLOW_HEADERS", "Origin, Content-Type, Accept, Authorization, X-Admin-Token, X-Requested-With,x-client-info")
	// allowCredentialsStr := utils.GetEnv("CORS_ALLOW_CREDENTIALS", "true")

	// 处理允许证书配置
	// allowCredentials := strings.ToLower(allowCredentialsStr) == "true"

	return cors.New(cors.Config{
		AllowOrigins:     strings.Join(config.AppConfig.CORS.AllowOrigins, ","),
		AllowMethods:     strings.Join(config.AppConfig.CORS.AllowMethods, ","),
		AllowHeaders:     strings.Join(config.AppConfig.CORS.AllowHeaders, ","),
		AllowCredentials: config.AppConfig.CORS.AllowCredentials,
		ExposeHeaders:    "Content-Length, Content-Type",
		MaxAge:           86400, // 预检请求结果缓存24小时
	})
}

// CorsLegacy 旧版CORS中间件，仅供参考
func CorsLegacy() fiber.Handler {
	return func(ctx *fiber.Ctx) error {
		// 获取环境变量中的配置，添加前端地址9527端口
		allowOrigins := utils.GetEnv("CORS_ALLOW_ORIGINS", "http://localhost:9527,http://localhost:8080,http://localhost:8081,http://localhost:3000,http://127.0.0.1:9527")
		allowMethods := utils.GetEnv("CORS_ALLOW_METHODS", "GET, POST, PUT, DELETE, OPTIONS")
		allowHeaders := utils.GetEnv("CORS_ALLOW_HEADERS", "Origin, Content-Type, Accept, Authorization, X-Admin-Token, X-Requested-With")

		ctx.Set("Access-Control-Allow-Origin", allowOrigins)
		ctx.Set("Access-Control-Allow-Headers", allowHeaders)
		ctx.Set("Access-Control-Allow-Credentials", "true")
		ctx.Set("Access-Control-Allow-Methods", allowMethods)

		// 处理OPTIONS预检请求
		if ctx.Method() == "OPTIONS" {
			return ctx.SendStatus(fiber.StatusOK)
		}

		return ctx.Next()
	}
}
