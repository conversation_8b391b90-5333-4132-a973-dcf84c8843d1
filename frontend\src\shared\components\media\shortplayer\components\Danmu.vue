<template>
  <div class="danmu-container" v-if="isOpen">
    <!-- 弹幕显示区域 -->
    <div class="danmu-display" ref="danmuDisplay">
      <div
        v-for="danmu in displayDanmus"
        :key="danmu.id"
        class="danmu-item"
        :style="{
          top: danmu.top + 'px',
          left: danmu.left + 'px',
          animationDuration: danmu.duration + 's',
          color: danmu.color,
          fontSize: danmu.fontSize + 'px'
        }"
      >
        {{ danmu.text }}
      </div>
    </div>

    <!-- 弹幕输入区域 -->
    <div class="danmu-input-container" v-if="showInput">
      <div class="danmu-input-box">
        <div class="input-header">
          <span class="input-title">发送弹幕</span>
          <button class="close-btn" @click="closeInput">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
        
        <div class="input-content">
          <div class="input-row">
            <input
              ref="danmuInput"
              v-model="inputText"
              type="text"
              placeholder="输入弹幕内容..."
              maxlength="50"
              @keyup.enter="sendDanmu"
              @input="handleInput"
              class="danmu-text-input"
            />
            <button 
              class="emoji-btn" 
              @click="toggleEmojiPanel"
              title="表情"
            >
              😊
            </button>
          </div>
          
          <!-- 表情面板 -->
          <div class="emoji-panel" v-if="showEmojiPanel">
            <div class="emoji-grid">
              <span
                v-for="emoji in emojiList"
                :key="emoji"
                class="emoji-item"
                @click="insertEmoji(emoji)"
              >
                {{ emoji }}
              </span>
            </div>
          </div>
          
          <div class="input-footer">
            <div class="input-options">
              <div class="color-picker">
                <span class="option-label">颜色:</span>
                <div class="color-options">
                  <div
                    v-for="color in colorOptions"
                    :key="color.value"
                    class="color-item"
                    :class="{ active: selectedColor === color.value }"
                    :style="{ backgroundColor: color.value }"
                    @click="selectedColor = color.value"
                    :title="color.name"
                  ></div>
                </div>
              </div>
              
              <div class="size-picker">
                <span class="option-label">大小:</span>
                <select v-model="selectedSize" class="size-select">
                  <option value="14">小</option>
                  <option value="16">中</option>
                  <option value="18">大</option>
                </select>
              </div>
            </div>
            
            <div class="input-actions">
              <span class="char-count">{{ inputText.length }}/50</span>
              <button 
                class="send-btn" 
                @click="sendDanmu"
                :disabled="!inputText.trim()"
              >
                发送
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { sendDanmuMessage } from '@/api/shorts'

// Props
interface Props {
  isOpen: boolean
  showInput: boolean
  speed?: number
  videoId?: string
}

const props = withDefaults(defineProps<Props>(), {
  speed: 1,
  videoId: ''
})

// Emits
const emit = defineEmits<{
  'close-input': []
  'danmu-sent': [danmu: any]
}>()

// Refs
const danmuDisplay = ref<HTMLElement>()
const danmuInput = ref<HTMLInputElement>()

// 弹幕数据
interface DanmuItem {
  id: string
  text: string
  top: number
  left: number
  duration: number
  color: string
  fontSize: number
  timestamp: number
}

const displayDanmus = ref<DanmuItem[]>([])
const inputText = ref('')
const showEmojiPanel = ref(false)
const selectedColor = ref('#ffffff')
const selectedSize = ref(16)

// 表情列表
const emojiList = [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
  '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
  '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
  '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
  '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬'
]

// 颜色选项
const colorOptions = [
  { name: '白色', value: '#ffffff' },
  { name: '红色', value: '#ff6b6b' },
  { name: '橙色', value: '#ffa726' },
  { name: '黄色', value: '#ffeb3b' },
  { name: '绿色', value: '#4caf50' },
  { name: '蓝色', value: '#2196f3' },
  { name: '紫色', value: '#9c27b0' },
  { name: '粉色', value: '#e91e63' }
]

// 弹幕轨道管理
const tracks = reactive<{ [key: number]: number }>({})
const trackHeight = 30
const maxTracks = 10

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 获取可用轨道
const getAvailableTrack = (): number => {
  for (let i = 0; i < maxTracks; i++) {
    if (!tracks[i] || Date.now() - tracks[i] > 3000) {
      tracks[i] = Date.now()
      return i * trackHeight + 10
    }
  }
  // 如果没有可用轨道，随机选择一个
  const randomTrack = Math.floor(Math.random() * maxTracks)
  tracks[randomTrack] = Date.now()
  return randomTrack * trackHeight + 10
}

// 添加弹幕到显示
const addDanmu = (text: string, color: string = '#ffffff', fontSize: number = 16) => {
  if (!props.isOpen || !danmuDisplay.value) return

  const danmu: DanmuItem = {
    id: generateId(),
    text,
    top: getAvailableTrack(),
    left: danmuDisplay.value.offsetWidth,
    duration: 8 / props.speed,
    color,
    fontSize,
    timestamp: Date.now()
  }

  displayDanmus.value.push(danmu)

  // 8秒后移除弹幕
  setTimeout(() => {
    const index = displayDanmus.value.findIndex(d => d.id === danmu.id)
    if (index > -1) {
      displayDanmus.value.splice(index, 1)
    }
  }, danmu.duration * 1000)
}

// 发送弹幕
const sendDanmu = async () => {
  if (!inputText.value.trim() || !props.videoId) return

  try {
    const danmuData = {
      text: inputText.value.trim(),
      color: selectedColor.value,
      fontSize: selectedSize.value,
      videoId: props.videoId,
      timestamp: Date.now()
    }

    // 发送到服务器
    await sendDanmuMessage(danmuData)

    // 立即显示在本地
    addDanmu(danmuData.text, danmuData.color, danmuData.fontSize)

    // 触发发送成功事件
    emit('danmu-sent', danmuData)

    // 清空输入框
    inputText.value = ''
    
    // 关闭输入面板
    closeInput()
  } catch (error) {
    console.error('发送弹幕失败:', error)
  }
}

// 插入表情
const insertEmoji = (emoji: string) => {
  if (inputText.value.length < 50) {
    inputText.value += emoji
  }
  showEmojiPanel.value = false
}

// 切换表情面板
const toggleEmojiPanel = () => {
  showEmojiPanel.value = !showEmojiPanel.value
}

// 处理输入
const handleInput = () => {
  // 限制输入长度
  if (inputText.value.length > 50) {
    inputText.value = inputText.value.slice(0, 50)
  }
}

// 关闭输入框
const closeInput = () => {
  showEmojiPanel.value = false
  emit('close-input')
}

// 监听输入框显示状态
watch(() => props.showInput, (newValue) => {
  if (newValue) {
    nextTick(() => {
      if (danmuInput.value) {
        danmuInput.value.focus()
      }
    })
  }
}, { immediate: true })

// 监听弹幕开关状态
watch(() => props.isOpen, (newValue) => {
  if (!newValue) {
    // 如果弹幕关闭，清空显示的弹幕
    displayDanmus.value = []
  }
})

// 点击外部关闭表情面板
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.emoji-panel') && !target.closest('.emoji-btn')) {
    showEmojiPanel.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  addDanmu
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  // 添加测试弹幕
  setTimeout(() => {
    addDanmu('欢迎观看！', '#ffffff', 16)
  }, 1000)
  
  setTimeout(() => {
    addDanmu('这是一条测试弹幕', '#ff6b6b', 16)
  }, 3000)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
.danmu-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 40;

  .danmu-display {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .danmu-item {
      position: absolute;
      white-space: nowrap;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
      animation: danmu-move linear forwards;
      pointer-events: none;
      z-index: 16;
      user-select: none;
    }
  }

  .danmu-input-container {
    position: absolute;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto;
    z-index: 25;

    .danmu-input-box {
      background: rgba(0, 0, 0, 0.9);
      border-radius: 12px;
      padding: 0;
      min-width: 400px;
      max-width: 500px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .input-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .input-title {
          color: white;
          font-size: 14px;
          font-weight: 500;
        }

        .close-btn {
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
          }

          svg {
            width: 16px;
            height: 16px;
          }
        }
      }

      .input-content {
        padding: 16px;

        .input-row {
          display: flex;
          gap: 8px;
          margin-bottom: 12px;

          .danmu-text-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 14px;
            outline: none;
            transition: all 0.2s ease;

            &::placeholder {
              color: rgba(255, 255, 255, 0.5);
            }

            &:focus {
              border-color: #ff6b6b;
              background: rgba(255, 255, 255, 0.15);
            }
          }

          .emoji-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
            }
          }
        }

        .emoji-panel {
          background: rgba(0, 0, 0, 0.95);
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 12px;
          border: 1px solid rgba(255, 255, 255, 0.1);

          .emoji-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 8px;

            .emoji-item {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 28px;
              height: 28px;
              cursor: pointer;
              border-radius: 4px;
              font-size: 16px;
              transition: background-color 0.2s ease;

              &:hover {
                background: rgba(255, 255, 255, 0.1);
              }
            }
          }
        }

        .input-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 16px;

          .input-options {
            display: flex;
            align-items: center;
            gap: 16px;

            .option-label {
              color: rgba(255, 255, 255, 0.7);
              font-size: 12px;
            }

            .color-picker {
              display: flex;
              align-items: center;
              gap: 8px;

              .color-options {
                display: flex;
                gap: 4px;

                .color-item {
                  width: 20px;
                  height: 20px;
                  border-radius: 50%;
                  cursor: pointer;
                  border: 2px solid transparent;
                  transition: all 0.2s ease;

                  &:hover {
                    transform: scale(1.1);
                  }

                  &.active {
                    border-color: white;
                    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
                  }
                }
              }
            }

            .size-picker {
              display: flex;
              align-items: center;
              gap: 8px;

              .size-select {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                padding: 4px 8px;
                color: white;
                font-size: 12px;
                outline: none;

                option {
                  background: #333;
                  color: white;
                }
              }
            }
          }

          .input-actions {
            display: flex;
            align-items: center;
            gap: 12px;

            .char-count {
              color: rgba(255, 255, 255, 0.5);
              font-size: 12px;
            }

            .send-btn {
              background: #ff6b6b;
              border: none;
              border-radius: 6px;
              padding: 6px 16px;
              color: white;
              font-size: 14px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover:not(:disabled) {
                background: #ff5252;
              }

              &:disabled {
                background: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.3);
                cursor: not-allowed;
              }
            }
          }
        }
      }
    }
  }
}

@keyframes danmu-move {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-120%);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .danmu-container {
    .danmu-input-container {
      left: 16px;
      right: 16px;
      transform: none;
      bottom: 60px;

      .danmu-input-box {
        min-width: auto;
        max-width: none;

        .input-content {
          padding: 12px;

          .input-footer {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;

            .input-options {
              justify-content: space-between;
            }

            .input-actions {
              justify-content: space-between;
            }
          }
        }
      }
    }

    .danmu-item {
      font-size: 14px !important;
    }
  }
}
</style> 