<template>
    <div class="mobile-nav" :class="{ 'is-open': isOpen }">
        <div class="mobile-nav-backdrop" @click="close"></div>
        <div class="mobile-nav-content">
            <!-- 用户信息区域 -->
            <div class="user-area">
                <template v-if="userStore.isLoggedIn">
                    <div class="user-info">
                        <el-avatar :size="60" :src="userStore.userAvatar" />
                        <div class="user-details">
                            <h3 class="username">{{ userStore.userDisplayName }}</h3>
                            <div class="user-stats">
                                <div class="stat">
                                    <span class="label">积分</span>
                                    <span class="value">{{ userPoints }}</span>
                                </div>
                                <div class="stat">
                                    <span class="label">关注</span>
                                    <span class="value">{{ followingCount }}</span>
                                </div>
                                <div class="stat">
                                    <span class="label">粉丝</span>
                                    <span class="value">{{ followerCount }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="user-actions">
                        <el-button type="primary" @click="goToProfile">个人中心</el-button>
                        <el-button @click="showUploadDialog">上传内容</el-button>
                    </div>
                </template>
                <template v-else>
                    <div class="guest-info">
                        <el-avatar :size="60" :src="defaultAvatar" />
                        <p>登录账号，体验更多功能</p>
                    </div>
                    <div class="auth-buttons">
                        <el-button type="primary" @click="userStore.showAuthDialog('login')">登录</el-button>
                        <el-button @click="userStore.showAuthDialog('register')">注册</el-button>
                    </div>
                </template>
            </div>

            <!-- 主导航菜单 -->
            <nav class="mobile-menu">
                <div class="menu-section">
                    <h3 class="section-title">主导航</h3>
                    <ul class="menu-list">
                        <li class="menu-item" v-for="(item, index) in mainNavItems" :key="index">
                            <router-link :to="item.path" class="menu-link" active-class="active" @click="close">
                                <el-icon>
                                    <component :is="item.icon" />
                                </el-icon>
                                <span>{{ item.title }}</span>
                            </router-link>
                        </li>
                    </ul>
                </div>

                <div class="menu-section" v-if="userStore.isLoggedIn">
                    <h3 class="section-title">用户中心</h3>
                    <ul class="menu-list">
                        <li class="menu-item" v-for="(item, index) in userNavItems" :key="index">
                            <router-link :to="item.path" class="menu-link" active-class="active" @click="close">
                                <el-icon>
                                    <component :is="item.icon" />
                                </el-icon>
                                <span>{{ item.title }}</span>
                                <el-badge v-if="item.badge" :value="item.badge" :max="99" class="menu-badge" />
                            </router-link>
                        </li>
                    </ul>
                </div>

                <div class="menu-section">
                    <h3 class="section-title">更多</h3>
                    <ul class="menu-list">
                        <li class="menu-item" v-for="(item, index) in moreNavItems" :key="index">
                            <router-link :to="item.path" class="menu-link" active-class="active" @click="close">
                                <el-icon>
                                    <component :is="item.icon" />
                                </el-icon>
                                <span>{{ item.title }}</span>
                            </router-link>
                        </li>
                    </ul>
                </div>

                <!-- 主题切换 -->
                <div class="theme-toggle">
                    <span class="theme-label">深色模式</span>
                    <el-switch v-model="isDarkMode" inline-prompt :active-icon="Moon" :inactive-icon="Sunny"
                        @change="toggleTheme" />
                </div>

                <!-- 退出登录按钮 -->
                <div class="logout-button" v-if="userStore.isLoggedIn">
                    <el-button type="danger" plain @click="logout">退出登录</el-button>
                </div>
            </nav>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import {
    Bell,
    ChatLineSquare,
    Collection,
    HomeFilled,
    Moon,
    Picture,
    QuestionFilled,
    Reading,
    Service,
    Setting,
    Star,
    Sunny,
    User,
    VideoCamera,
    VideoPlay
} from '@element-plus/icons-vue'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'

// 属性
defineProps({
    isOpen: {
        type: Boolean,
        required: true
    }
})

// 事件
const emit = defineEmits(['close'])

// 路由和用户状态
const router = useRouter()
const userStore = useUserStore()
const defaultAvatar = '/default-avatar.png'

// 用户数据
const userPoints = ref(0)
const followingCount = ref(0)
const followerCount = ref(0)

// 主题切换
const isDarkMode = ref(false)
const toggleTheme = (value: boolean) => {
    document.documentElement.setAttribute('data-theme', value ? 'dark' : 'light')
    localStorage.setItem('theme', value ? 'dark' : 'light')
}

// 导航菜单项
const mainNavItems = [
    { title: '首页', path: '/', icon: HomeFilled },
    { title: '视频', path: '/videos', icon: VideoPlay },
    { title: '短视频', path: '/shorts', icon: VideoCamera },
    { title: '社区', path: '/community', icon: ChatLineSquare },
    { title: '图片', path: '/pictures', icon: Picture },
    { title: '漫画', path: '/manga', icon: Reading },
    { title: '电子书', path: '/ebook', icon: Collection }
]

const userNavItems = computed(() => [
    { title: '个人中心', path: '/user/profile', icon: User },
    { title: '消息通知', path: '/user/messages', icon: Bell, badge: 5 },
    { title: '我的收藏', path: '/user/favorites', icon: Star },
    { title: '账户设置', path: '/user/settings', icon: Setting }
])

const moreNavItems = [
    { title: '帮助中心', path: '/help', icon: QuestionFilled },
    { title: '客户服务', path: '/service', icon: Service }
]

// 方法
const close = () => {
    emit('close')
}

const goToProfile = () => {
    router.push('/user/profile')
    close()
}

const showUploadDialog = () => {
    close()
    // 实现上传对话框逻辑
}

const logout = () => {
    userStore.logout()
    close()
}

// 初始化
const initData = () => {
    if (userStore.isLoggedIn) {
        // 获取用户数据
        userPoints.value = 1280 // 示例数据
        followingCount.value = 42 // 示例数据
        followerCount.value = 128 // 示例数据
    }

    // 检查并设置当前主题
    const savedTheme = localStorage.getItem('theme')
    isDarkMode.value = savedTheme === 'dark'
}

initData()
</script>

<style scoped lang="scss">
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    pointer-events: none;

    &.is-open {
        pointer-events: auto;

        .mobile-nav-backdrop {
            opacity: 1;
        }

        .mobile-nav-content {
            transform: translateX(0);
        }
    }

    .mobile-nav-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .mobile-nav-content {
        position: absolute;
        top: 0;
        right: 0;
        width: 80%;
        max-width: 360px;
        height: 100%;
        background-color: var(--el-bg-color);
        box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        overflow-y: auto;
        display: flex;
        flex-direction: column;

        .user-area {
            padding: 24px 20px;
            background-color: var(--el-color-primary-light-9);
            border-bottom: 1px solid var(--el-border-color-light);

            .user-info {
                display: flex;
                align-items: center;
                margin-bottom: 16px;

                .user-details {
                    margin-left: 16px;
                    flex: 1;

                    .username {
                        margin: 0 0 8px;
                        font-size: 18px;
                        font-weight: 600;
                        color: var(--el-text-color-primary);
                    }

                    .user-stats {
                        display: flex;
                        gap: 12px;

                        .stat {
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            .label {
                                font-size: 12px;
                                color: var(--el-text-color-secondary);
                            }

                            .value {
                                font-size: 14px;
                                font-weight: 600;
                                color: var(--el-text-color-primary);
                            }
                        }
                    }
                }
            }

            .user-actions {
                display: flex;
                gap: 12px;
            }

            .guest-info {
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 16px;

                p {
                    margin: 12px 0 0;
                    font-size: 16px;
                    color: var(--el-text-color-secondary);
                }
            }

            .auth-buttons {
                display: flex;
                gap: 12px;
                justify-content: center;
            }
        }

        .mobile-menu {
            flex: 1;
            padding: 16px 0;

            .menu-section {
                margin-bottom: 24px;

                .section-title {
                    padding: 0 20px;
                    margin: 0 0 8px;
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--el-text-color-secondary);
                }

                .menu-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    .menu-item {
                        .menu-link {
                            display: flex;
                            align-items: center;
                            padding: 12px 20px;
                            text-decoration: none;
                            color: var(--el-text-color-primary);
                            transition: all 0.2s;
                            position: relative;

                            .el-icon {
                                margin-right: 12px;
                                font-size: 18px;
                            }

                            &:hover {
                                background-color: var(--el-color-primary-light-9);
                            }

                            &.active {
                                color: var(--el-color-primary);
                                background-color: var(--el-color-primary-light-9);
                                font-weight: 500;
                            }

                            .menu-badge {
                                position: absolute;
                                right: 20px;
                            }
                        }
                    }
                }
            }

            .theme-toggle {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                border-top: 1px solid var(--el-border-color-light);
                margin-top: auto;

                .theme-label {
                    font-size: 16px;
                    color: var(--el-text-color-primary);
                }
            }

            .logout-button {
                padding: 16px 20px;
                text-align: center;
            }
        }
    }
}
</style>