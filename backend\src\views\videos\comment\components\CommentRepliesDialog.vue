<template>
  <el-dialog
    v-model="visible"
    title="评论回复列表"
    width="700px"
    @close="handleClose"
  >
    <div v-loading="loading" class="replies-dialog-content">
      <!-- 父评论信息 -->
      <div v-if="parentComment" class="parent-comment mb-4">
        <div class="flex items-start">
          <el-avatar 
            :size="36" 
            :src="parentComment.user_avatar" 
            class="mr-2 mt-1"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="flex-1">
            <div class="parent-user">
              <span class="font-bold">{{ parentComment.user_nickname || parentComment.user_id }}</span>
              <el-tag size="small" class="ml-2">父评论</el-tag>
            </div>
            <div class="comment-content mt-1">{{ parentComment.content }}</div>
            <div class="parent-info">
              <span class="time-info">
                <el-icon class="time-icon"><Calendar /></el-icon>
                {{ formatDate(parentComment.created_at) }}
              </span>
              <CommentStatusTag :status="parentComment.status" />
            </div>
          </div>
        </div>
      </div>

      <el-divider>回复列表</el-divider>

      <!-- 回复列表 -->
      <div v-if="replies.length > 0" class="replies-list">
        <div v-for="reply in replies" :key="reply.id" class="reply-item mb-4">
          <div class="flex items-start">
            <el-avatar 
              :size="28" 
              :src="reply.user_avatar" 
              class="mr-2 mt-1"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="flex-1">
              <div class="flex items-center">
                <span class="font-bold">{{ reply.user_nickname || reply.user_id }}</span>
                <span v-if="reply.reply_to_user" class="mx-1 text-gray-500">回复</span>
                <span v-if="reply.reply_to_user" class="font-bold">{{ reply.reply_to_user }}</span>
              </div>
              <div class="comment-content mt-1">{{ reply.content }}</div>
              <div class="flex justify-between items-center mt-1">
                <div class="flex items-center gap-2">
                  <span class="time-info">
                    <el-icon class="time-icon"><Calendar /></el-icon>
                    {{ formatDate(reply.created_at) }}
                  </span>
                  <CommentStatusTag :status="reply.status" />
                </div>
                <div class="reply-actions">
                  <el-popconfirm 
                    :title="`确定要${reply.status === 1 ? '隐藏' : '显示'}此回复吗？`"
                    @confirm="handleChangeStatus(reply.id, reply.status === 1 ? 0 : 1)"
                  >
                    <template #reference>
                      <el-button type="warning" link size="small">
                        {{ reply.status === 1 ? '隐藏' : '显示' }}
                      </el-button>
                    </template>
                  </el-popconfirm>
                  <el-popconfirm 
                    title="确定删除此回复吗？" 
                    @confirm="handleDeleteReply(reply)"
                  >
                    <template #reference>
                      <el-button type="danger" link size="small">删除</el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无回复" />

      <!-- 回复分页器 -->
      <div v-if="total > 0" class="pagination-container">
        <SinglePager
          :current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          :background="true"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { SinglePager } from '@/components/themes';
import type { CommentItem } from '@/service/api/videos/comments';
import { formatDate } from '@/utils/date';
import { Calendar, User } from '@element-plus/icons-vue';
import { computed } from 'vue';
import CommentStatusTag from './CommentStatusTag.vue';

// Props
interface Props {
  modelValue: boolean;
  loading: boolean;
  parentComment: CommentItem | null;
  replies: CommentItem[];
  currentPage: number;
  pageSize: number;
  total: number;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'deleteReply', reply: CommentItem): void;
  (e: 'pageChange', page: number): void;
  (e: 'changeStatus', id: string, status: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性：对话框可见性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
});

// 关闭对话框
const handleClose = () => {
  visible.value = false;
};

// 删除回复
const handleDeleteReply = (reply: CommentItem) => {
  emit('deleteReply', reply);
};

// 处理状态变化
const handleChangeStatus = (id: string, status: number) => {
  emit('changeStatus', id, status);
};

// 处理分页变化
const handlePageChange = (page: number) => {
  emit('pageChange', page);
};
</script>

<style scoped lang="scss">
.replies-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 8px;
}

.parent-comment {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #409eff;
  
  .parent-user {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .parent-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
  }
}

.reply-item {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.comment-content {
  padding: 8px 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-top: 4px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

.parent-comment .comment-content {
  background-color: #e6f3ff;
  border-left: 2px solid #409eff;
}

.pagination-container {
  margin-top: 16px;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: flex-end;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
  
  .time-icon {
    font-size: 12px;
  }
}

.reply-actions {
  display: flex;
  gap: 8px;
}

// Utility classes
.font-bold {
  font-weight: 600;
  color: #303133;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.flex {
  display: flex;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.gap-2 {
  gap: 0.5rem;
}
</style> 