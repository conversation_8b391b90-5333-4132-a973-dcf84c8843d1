package users

import (
	"frontapi/internal/api"
	postSrv "frontapi/internal/service/posts"
	shortVideoSrv "frontapi/internal/service/shortvideos"
	userSrv "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
	postTypings "frontapi/internal/typings/posts"
	shortVideoTypings "frontapi/internal/typings/shortvideos"
	userTypings "frontapi/internal/typings/users"
	videoTypings "frontapi/internal/typings/videos"
	"slices"

	"github.com/gofiber/fiber/v2"
)

type CreatorController struct {
	UserService              userSrv.UserService
	VideoService             videoSrv.VideoService
	PostService              postSrv.PostService
	ShortVideoService        shortVideoSrv.ShortVideoService
	VideoAlbumService        videoSrv.VideoAlbumService
	VideoCommentService      videoSrv.VideoCommentService
	PostCommentService       postSrv.PostCommentService
	ShortVideoCommentService shortVideoSrv.ShortVideoCommentService
	api.BaseController
}

func NewCreatorController(
	userService userSrv.UserService,
	videoService videoSrv.VideoService,
	postService postSrv.PostService,
	shortVideoService shortVideoSrv.ShortVideoService,
	videoAlbumService videoSrv.VideoAlbumService,
	videoCommentService videoSrv.VideoCommentService,
	postCommentService postSrv.PostCommentService,
	shortVideoCommentService shortVideoSrv.ShortVideoCommentService,
) *CreatorController {
	return &CreatorController{
		UserService:              userService,
		VideoService:             videoService,
		PostService:              postService,
		ShortVideoService:        shortVideoService,
		VideoAlbumService:        videoAlbumService,
		VideoCommentService:      videoCommentService,
		PostCommentService:       postCommentService,
		ShortVideoCommentService: shortVideoCommentService,
	}
}

func (c *CreatorController) GetCreatorList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	keyword := reqInfo.Get("keyword").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"keyword":            keyword,
		"is_content_creator": 1,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "heat DESC,reg_time DESC"
	}
	creatorList, total, err := c.UserService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取创作者列表失败: "+err.Error())
	}

	creatorListResponse := userTypings.ConvertCreatorListResponse(creatorList, total, pageNo, pageSize)
	return c.Success(ctx, creatorListResponse)
}

// GetCreatorDetail 获取创作者详情
func (c *CreatorController) GetCreatorDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	id := reqInfo.Get("id").GetString()

	creator, err := c.UserService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取创作者详情失败: "+err.Error())
	}
	if err != nil {
		return c.InternalServerError(ctx, "获取创作者统计数据失败: "+err.Error())
	}

	creatorDetailResponse := userTypings.ConvertCreatorDetailInfo(creator)
	return c.Success(ctx, creatorDetailResponse)
}

// GetCreatorVideos 获取创作者的视频列表
func (c *CreatorController) GetCreatorVideos(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	if creatorID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}
	sortBy := reqInfo.Get("sort").GetString()
	if sortBy == "" {
		sortBy = "upload_time DESC"
	}
	switch sortBy {
	case "popular":
		sortBy = "view_count DESC"
	case "liked":
		sortBy = "like_count DESC"
	default:
		sortBy = "upload_time DESC"
	}

	condition := map[string]interface{}{
		"creator_id": creatorID,
		"status":     1, // 只获取正常状态的视频
	}

	videos, total, err := c.VideoService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取创作者视频列表失败: "+err.Error())
	}
	videoListResponse := videoTypings.ConvertVideoListResponse(videos, total, pageNo, pageSize)
	return c.Success(ctx, videoListResponse)
}

// GetCreatorAlbums 获取创作者的专辑列表
func (c *CreatorController) GetCreatorAlbums(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	sort := reqInfo.Get("sort").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	if creatorID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}

	condition := map[string]interface{}{
		"user_id": creatorID,
		"status":  1, // 只获取正常状态的专辑
	}
	sortBy := "created_at DESC"
	switch sort {
	case "popular":
		sortBy = "view_count DESC"
	case "liked":
		sortBy = "like_count DESC"
	default:
		sortBy = "created_at DESC"
	}

	albums, total, err := c.VideoAlbumService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取创作者专辑列表失败: "+err.Error())
	}
	albumListResponse := videoTypings.ConvertVideoAlbumListResponse(albums, total, pageNo, pageSize)
	return c.Success(ctx, albumListResponse)
}

// GetCreatorComments 获取创作者的评论列表
func (c *CreatorController) GetCreatorComments(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	commentType := reqInfo.Get("type").GetString()
	sort := reqInfo.Get("sort").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	if creatorID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}

	condition := map[string]interface{}{
		"user_id": creatorID,
		"status":  1, // 只获取正常状态的评论
	}
	if commentType == "" {
		commentType = "all"
	}
	commentTypes := []string{"video", "post", "shortvideo", "all"}
	if !slices.Contains(commentTypes, commentType) {
		return c.BadRequest(ctx, "评论类型不正确", nil)
	}
	sortBy := "created_at DESC"
	switch sort {
	case "popular":
		sortBy = "heat DESC"
	case "liked":
		sortBy = "like_count DESC"
	default:
		sortBy = "created_at DESC"
	}
	//获取全部评论
	if commentType == "all" {
		comments, total, err := c.UserService.ListUserAllComments(ctx.Context(), condition, sortBy, pageNo, pageSize)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := userTypings.ConvertCreatorCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	} else if commentType == "post" {
		comments, total, err := c.PostCommentService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := postTypings.ConvertPostCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	} else if commentType == "shortvideo" {
		comments, total, err := c.ShortVideoCommentService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := shortVideoTypings.ConvertShortVideoCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	} else if commentType == "video" {
		comments, total, err := c.VideoCommentService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := videoTypings.ConvertVideoCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	}
	return c.Success(ctx, nil)
}

// GetCreatorPosts 获取创作者的帖子列表
func (c *CreatorController) GetCreatorPosts(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	if creatorID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}

	condition := map[string]interface{}{
		"author_id": creatorID,
		"status":    1, // 只获取正常状态的帖子
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	posts, total, err := c.PostService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取创作者帖子列表失败: "+err.Error())
	}
	response := postTypings.ConvertPostListResponse(posts, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// GetCreatorShortVideos 获取创作者的短视频列表
func (c *CreatorController) GetCreatorShortVideos(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	if creatorID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}

	// 暂时返回空列表，因为短视频服务方法需要进一步确认
	return c.SuccessList(ctx, []interface{}{}, 0, pageNo, pageSize)
}

// FollowCreator 关注创作者
func (c *CreatorController) FollowCreator(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	userID := reqInfo.Get("user_id").GetString()

	if creatorID == "" || userID == "" {
		return c.BadRequest(ctx, "创作者ID和用户ID不能为空", nil)
	}

	// 暂时返回成功，关注功能需要单独实现
	return c.SuccessWithMessage(ctx, "关注成功")
}

// UnfollowCreator 取消关注创作者
func (c *CreatorController) UnfollowCreator(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	userID := reqInfo.Get("user_id").GetString()

	if creatorID == "" || userID == "" {
		return c.BadRequest(ctx, "创作者ID和用户ID不能为空", nil)
	}

	// 暂时返回成功，取消关注功能需要单独实现
	return c.SuccessWithMessage(ctx, "取消关注成功")
}

// CheckFollowStatus 检查关注状态
func (c *CreatorController) CheckFollowStatus(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	creatorID := reqInfo.Get("creator_id").GetString()
	userID := reqInfo.Get("user_id").GetString()

	if creatorID == "" || userID == "" {
		return c.BadRequest(ctx, "创作者ID和用户ID不能为空", nil)
	}

	// 暂时返回未关注状态，关注状态检查功能需要单独实现
	return c.Success(ctx, fiber.Map{
		"is_following": false,
	})
}
