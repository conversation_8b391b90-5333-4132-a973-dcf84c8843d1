package utils

import (
	"github.com/gofiber/fiber/v2"
	"math"
	"strconv"
)

// CalculateTotalPages 计算总页数
func CalculateTotalPages(total int64, pageSize int) int {
	if pageSize <= 0 {
		return 0
	}

	return int(math.Ceil(float64(total) / float64(pageSize)))
}

// CalculateOffset 计算数据库查询的偏移量
func CalculateOffset(page, pageSize int) int {
	if page <= 0 {
		page = 1
	}

	return (page - 1) * pageSize
}

// GetPageInfo 获取分页信息
func GetPageInfo(currentPage, pageSize, total int64) map[string]interface{} {
	totalPages := CalculateTotalPages(total, int(pageSize))

	return map[string]interface{}{
		"currentPage": currentPage,
		"pageSize":    pageSize,
		"total":       total,
		"totalPages":  totalPages,
	}
}
func GetPageParams(ctx *fiber.Ctx) (int, int) {
	pageNo, _ := strconv.Atoi(ctx.Query("pageNo", "1"))
	pageSize, _ := strconv.Atoi(ctx.Query("pageSize", "20"))
	return pageNo, pageSize
}
