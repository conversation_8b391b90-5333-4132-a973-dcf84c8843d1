package pictures

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// UserPictureCollection 用户图片收藏模型
type UserPictureCollection struct {
	models.BaseModel
	UserID          string         `json:"user_id" gorm:"column:user_id;not null" comment:"用户ID"`
	PictureID       string         `json:"picture_id" gorm:"column:picture_id;not null" comment:"图片ID"`
	PictureURL      string         `json:"picture_url" gorm:"column:picture_url" comment:"图片URL"`
	PictureTitle    string         `json:"picture_title" gorm:"column:picture_title" comment:"图片标题"`
	Width           int            `json:"width" gorm:"column:width" comment:"宽度"`
	Height          int            `json:"height" gorm:"column:height" comment:"高度"`
	AlbumID         string         `json:"album_id" gorm:"column:album_id" comment:"专辑ID"`
	AlbumTitle      string         `json:"album_title" gorm:"column:album_title" comment:"专辑标题"`
	CreatorID       string         `json:"creator_id" gorm:"column:creator_id" comment:"创作者ID"`
	CreatorName     string         `json:"creator_name" gorm:"column:creator_name" comment:"创作者名称"`
	CreatorAvatar   string         `json:"creator_avatar" gorm:"column:creator_avatar" comment:"创作者头像"`
	CategoryID      string         `json:"category_id" gorm:"column:category_id" comment:"分类ID"`
	CategoryName    string         `json:"category_name" gorm:"column:category_name" comment:"分类名称"`
	CollectionTime  types.JSONTime `json:"collection_time" gorm:"column:collection_time;default:CURRENT_TIMESTAMP" comment:"收藏时间"`
	CollectionGroup string         `json:"collection_group" gorm:"column:collection_group;default:'默认收藏夹'" comment:"收藏分组"`
	Note            string         `json:"note" gorm:"column:note" comment:"收藏备注"`
}

// TableName 指定表名
func (UserPictureCollection) TableName() string {
	return "ly_user_picture_collections"
}
