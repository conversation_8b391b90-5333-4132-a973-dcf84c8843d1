package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// VideoCommentRepository 视频评论数据访问接口
type VideoCommentRepository interface {
	base.ExtendedRepository[videos.VideoComment]
	// 业务特定方法
	ListByVideoID(ctx context.Context, videoID string, page, pageSize int) ([]*videos.VideoComment, int64, error)
	ListReplyByParentID(ctx context.Context, parentID string, page, pageSize int) ([]*videos.VideoComment, int64, error)
	ListByUserID(ctx context.Context, userID string, page, pageSize int) ([]*videos.VideoComment, int64, error)
	ListComments(ctx context.Context, condition map[string]interface{}, sortBy string, page int, size int) ([]*videos.VideoComment, int64, error)
}

// videoCommentRepository 视频评论数据访问实现
type videoCommentRepository struct {
	base.ExtendedRepository[videos.VideoComment]
}

// NewVideoCommentRepository 创建视频评论仓库实例
func NewVideoCommentRepository(db *gorm.DB) VideoCommentRepository {
	return &videoCommentRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoComment](db),
	}
}



// ListByVideoID 获取视频的评论列表
func (r *videoCommentRepository) ListByVideoID(ctx context.Context, videoID string, page, pageSize int) ([]*videos.VideoComment, int64, error) {
	// 使用原生查询处理复杂条件
	var (
		comments []*videos.VideoComment
		total    int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&videos.VideoComment{}).
		Where("video_id = ? AND parent_id IS NULL", videoID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("video_id = ? AND parent_id IS NULL", videoID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&comments).Error
	if err != nil {
		return nil, 0, err
	}

	return comments, total, nil
}
func (r *videoCommentRepository) ListComments(ctx context.Context, condition map[string]interface{}, sortBy string, page int, size int) ([]*videos.VideoComment, int64, error) {
	var (
		comments []*videos.VideoComment
		total    int64
	)
	db := r.GetDBWithContext(ctx).Model(&videos.VideoComment{})
	if condition != nil {
		if condition["user_id"] != nil && condition["user_id"] != "" {
			userId := condition["user_id"].(string)
			db = db.Where("user_id = ?", userId)
		}
		if condition["status"] != nil && condition["status"] != "" {
			status := condition["status"].(int)
			if status > -999 {
				db = db.Where("status = ?", status)
			}
		}
		if condition["video_id"] != nil && condition["video_id"] != "" {
			videoId := condition["video_id"].(string)
			db = db.Where("video_id = ?", videoId)
		}
		if condition["content"] != nil && condition["content"] != "" {
			keyword := condition["content"].(string)
			db = db.Where("content LIKE ?", "%"+keyword+"%")
		}
		if condition["user_name"] != nil && condition["user_name"] != "" {
			userName := condition["user_name"].(string)
			db = db.Where("user_nickname LIKE ?", "%"+userName+"%")
		}
	}
	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	if page < 1 {
		page = 1
	}
	if size < 1 {
		size = 10
	}
	err = db.Offset((page - 1) * size).Limit(size).Order(sortBy).Find(&comments).Error
	return comments, total, err
}

// ListReplyByParentID 获取评论的回复列表
func (r *videoCommentRepository) ListReplyByParentID(ctx context.Context, parentID string, page, pageSize int) ([]*videos.VideoComment, int64, error) {
	var (
		replies []*videos.VideoComment
		total   int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&videos.VideoComment{}).
		Where("parent_id = ?", parentID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("parent_id = ?", parentID).
		Order("created_at ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&replies).Error
	if err != nil {
		return nil, 0, err
	}

	return replies, total, nil
}

// UpdateLikeCount 更新评论点赞次数
func (r *videoCommentRepository) UpdateLikeCount(ctx context.Context, id string, increment int) error {
	return r.GetDBWithContext(ctx).
		Model(&videos.VideoComment{}).
		Where("id = ?", id).
		UpdateColumn("like_count", gorm.Expr("like_count + ?", increment)).
		Error
}

// ListByUserID 获取用户的评论列表
func (r *videoCommentRepository) ListByUserID(ctx context.Context, userID string, page, pageSize int) ([]*videos.VideoComment, int64, error) {
	var (
		comments []*videos.VideoComment
		total    int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&videos.VideoComment{}).
		Where("user_id = ?", userID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&comments).Error
	if err != nil {
		return nil, 0, err
	}

	return comments, total, nil
}
