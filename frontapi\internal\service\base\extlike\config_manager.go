package extlike

import (
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"

	"frontapi/pkg/mongodb"
	redisClient "frontapi/pkg/redis"
)

// ConfigManager 配置管理器
type ConfigManager struct {
	systemRedisClient   *redis.Client
	systemMongoClient   *mongo.Client
	systemMongoDatabase *mongo.Database
	likeConfig          *LikeServiceConfig
}

// LikeServiceConfig 点赞服务配置
type LikeServiceConfig struct {
	// Redis配置
	RedisConfig *RedisConfig `json:"redis_config,omitempty" yaml:"redis_config,omitempty"`

	// MongoDB配置
	MongoConfig *MongoConfig `json:"mongo_config,omitempty" yaml:"mongo_config,omitempty"`

	// 服务配置
	ServiceConfig *ServiceConfig `json:"service_config,omitempty" yaml:"service_config,omitempty"`

	// 性能配置
	PerformanceConfig *PerformanceConfig `json:"performance_config,omitempty" yaml:"performance_config,omitempty"`
}

// NewConfigManager 创建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		systemRedisClient: redisClient.Client,
		likeConfig:        DefaultLikeServiceConfig(),
	}
}

// DefaultLikeServiceConfig 默认点赞服务配置
func DefaultLikeServiceConfig() *LikeServiceConfig {
	return &LikeServiceConfig{
		RedisConfig:       DefaultRedisConfig(),
		MongoConfig:       DefaultMongoConfig(),
		ServiceConfig:     DefaultServiceConfig(),
		PerformanceConfig: DefaultPerformanceConfig(),
	}
}

// GetRedisClient 获取Redis客户端（优先使用自定义配置，否则使用系统配置）
func (cm *ConfigManager) GetRedisClient() (redis.UniversalClient, *RedisConfig, error) {
	// 如果有自定义Redis配置，创建新的客户端
	if cm.likeConfig.RedisConfig != nil && cm.likeConfig.RedisConfig.Enabled {
		if cm.likeConfig.RedisConfig.UseCustom {
			client, err := cm.createCustomRedisClient(cm.likeConfig.RedisConfig)
			if err != nil {
				return nil, nil, fmt.Errorf("创建自定义Redis客户端失败: %w", err)
			}
			return client, cm.likeConfig.RedisConfig, nil
		}
	}

	// 使用系统Redis客户端
	if cm.systemRedisClient == nil {
		return nil, nil, fmt.Errorf("系统Redis客户端未初始化")
	}

	// 将系统Redis配置适配为我们的配置格式
	systemConfig := cm.adaptSystemRedisConfig()
	return cm.systemRedisClient, systemConfig, nil
}

// GetMongoClient 获取MongoDB客户端（优先使用自定义配置，否则使用系统配置）
func (cm *ConfigManager) GetMongoClient() (*mongo.Client, *mongo.Database, *MongoConfig, error) {
	// 如果有自定义MongoDB配置，创建新的客户端
	if cm.likeConfig.MongoConfig != nil && cm.likeConfig.MongoConfig.Enabled {
		if cm.likeConfig.MongoConfig.UseCustom {
			client, database, err := cm.createCustomMongoClient(cm.likeConfig.MongoConfig)
			if err != nil {
				return nil, nil, nil, fmt.Errorf("创建自定义MongoDB客户端失败: %w", err)
			}
			return client, database, cm.likeConfig.MongoConfig, nil
		}
	}

	// 使用系统MongoDB客户端
	if cm.systemMongoClient == nil {
		// 如果系统MongoDB客户端未初始化，尝试使用默认配置创建
		defaultConfig := mongodb.DefaultConfig()
		client, err := mongodb.NewClient(defaultConfig)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("创建默认MongoDB客户端失败: %w", err)
		}
		cm.systemMongoClient = client
		cm.systemMongoDatabase = mongodb.NewDatabase(client, defaultConfig.Database)
	}

	// 将系统MongoDB配置适配为我们的配置格式
	systemConfig := cm.adaptSystemMongoConfig()
	return cm.systemMongoClient, cm.systemMongoDatabase, systemConfig, nil
}

// SetLikeConfig 设置点赞服务配置
func (cm *ConfigManager) SetLikeConfig(config *LikeServiceConfig) {
	if config != nil {
		cm.likeConfig = config
	}
}

// GetLikeConfig 获取点赞服务配置
func (cm *ConfigManager) GetLikeConfig() *LikeServiceConfig {
	return cm.likeConfig
}

// createCustomRedisClient 创建自定义Redis客户端
func (cm *ConfigManager) createCustomRedisClient(config *RedisConfig) (redis.UniversalClient, error) {
	if config.Cluster {
		// 集群模式
		return redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:        config.ClusterAddrs,
			Password:     config.Password,
			DialTimeout:  config.DialTimeout,
			ReadTimeout:  config.ReadTimeout,
			WriteTimeout: config.WriteTimeout,
			PoolSize:     config.PoolSize,
			MinIdleConns: config.MinIdleConns,
		}), nil
	} else {
		// 单机模式
		return redis.NewClient(&redis.Options{
			Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
			Password:     config.Password,
			DB:           config.DB,
			DialTimeout:  config.DialTimeout,
			ReadTimeout:  config.ReadTimeout,
			WriteTimeout: config.WriteTimeout,
			PoolSize:     config.PoolSize,
			MinIdleConns: config.MinIdleConns,
		}), nil
	}
}

// createCustomMongoClient 创建自定义MongoDB客户端
func (cm *ConfigManager) createCustomMongoClient(config *MongoConfig) (*mongo.Client, *mongo.Database, error) {
	mongoConfig := &mongodb.Config{
		URI:            config.URI,
		Database:       config.Database,
		ConnectTimeout: config.ConnectTimeout,
		MaxPoolSize:    config.MaxPoolSize,
		MinPoolSize:    config.MinPoolSize,
		MaxIdleTime:    config.MaxIdleTime,
	}

	client, err := mongodb.NewClient(mongoConfig)
	if err != nil {
		return nil, nil, err
	}

	database := mongodb.NewDatabase(client, config.Database)
	return client, database, nil
}

// adaptSystemRedisConfig 将系统Redis配置适配为点赞服务配置
func (cm *ConfigManager) adaptSystemRedisConfig() *RedisConfig {
	return &RedisConfig{
		Enabled:      true,
		UseCustom:    false,
		Host:         "localhost", // 从系统配置中获取，这里简化处理
		Port:         6379,
		Password:     "",
		DB:           0,
		KeyPrefix:    "like:",
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 5,
		LikeTTL:      24 * time.Hour,
		CountTTL:     24 * time.Hour,
		RankingTTL:   1 * time.Hour,
		DefaultTTL:   1 * time.Hour,
	}
}

// adaptSystemMongoConfig 将系统MongoDB配置适配为点赞服务配置
func (cm *ConfigManager) adaptSystemMongoConfig() *MongoConfig {
	return &MongoConfig{
		Enabled:        true,
		UseCustom:      false,
		URI:            "mongodb://localhost:27017",
		Database:       "frontapi",
		Collection:     "likes",
		ConnectTimeout: 10 * time.Second,
		MaxPoolSize:    100,
		MinPoolSize:    5,
		MaxIdleTime:    5 * time.Minute,
	}
}

// ValidateConfig 验证配置
func (cm *ConfigManager) ValidateConfig() error {
	if cm.likeConfig == nil {
		return fmt.Errorf("点赞服务配置为空")
	}

	// 验证Redis配置
	if cm.likeConfig.RedisConfig != nil && cm.likeConfig.RedisConfig.Enabled {
		if err := cm.validateRedisConfig(cm.likeConfig.RedisConfig); err != nil {
			return fmt.Errorf("Redis配置验证失败: %w", err)
		}
	}

	// 验证MongoDB配置
	if cm.likeConfig.MongoConfig != nil && cm.likeConfig.MongoConfig.Enabled {
		if err := cm.validateMongoConfig(cm.likeConfig.MongoConfig); err != nil {
			return fmt.Errorf("MongoDB配置验证失败: %w", err)
		}
	}

	return nil
}

// validateRedisConfig 验证Redis配置
func (cm *ConfigManager) validateRedisConfig(config *RedisConfig) error {
	if config.UseCustom {
		if config.Cluster {
			if len(config.ClusterAddrs) == 0 {
				return fmt.Errorf("集群模式下必须提供集群地址")
			}
		} else {
			if config.Host == "" {
				return fmt.Errorf("必须提供Redis主机地址")
			}
			if config.Port <= 0 || config.Port > 65535 {
				return fmt.Errorf("Redis端口号必须在1-65535之间")
			}
		}
	}

	if config.PoolSize <= 0 {
		return fmt.Errorf("连接池大小必须大于0")
	}

	return nil
}

// validateMongoConfig 验证MongoDB配置
func (cm *ConfigManager) validateMongoConfig(config *MongoConfig) error {
	if config.UseCustom {
		if config.URI == "" {
			return fmt.Errorf("必须提供MongoDB连接URI")
		}
		if config.Database == "" {
			return fmt.Errorf("必须提供数据库名称")
		}
	}

	if config.MaxPoolSize <= 0 {
		return fmt.Errorf("最大连接池大小必须大于0")
	}

	return nil
}

// Close 关闭配置管理器
func (cm *ConfigManager) Close() error {
	// 如果有自定义客户端，需要关闭它们
	// 系统客户端由系统管理，不在这里关闭

	if cm.systemMongoClient != nil {
		if err := mongodb.Close(cm.systemMongoClient); err != nil {
			return fmt.Errorf("关闭MongoDB客户端失败: %w", err)
		}
	}

	return nil
}
