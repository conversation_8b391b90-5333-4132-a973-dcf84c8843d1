// 漫画基本信息类型
export interface ComicsItem {
    id: string;//漫画ID
    title: string;//标题
    cover: string;//封面图URL
    rating: number;//评分
    description: string;//描述
    author: string;//作者
    category_id: string;//分类ID
    category_name: string;//分类名称
    progress: string;//状态：ongoing-连载中，completed-已完结
    tags_json: string;//标签JSON数据
    popularity: number;//人气值
    chapter_count: number;//章节数
    read_count: number;//阅读数
    like_count: number;//点赞数
    score: number;//评分
    favorite_count: number;//收藏数
    share_count: number;//分享次数
    comment_count: number;//评论数量
    is_adult: number;//是否成人内容：0-否，1-是
    is_paid: number;//是否付费：0-免费，1-付费
    is_featured: number;//是否推荐
    tags: string;//标签
    price: number;//价格
    creator_id: string;//创作者ID
    creator_name: string;//创作者名称
    creator_avatar: string;//创作者头像
    publish_date: string;//发布日期
    update_time: string;//更新时间
    status: number;//状态：0-禁用，1.下架， 2-上架中
    created_at: string;//创建时间
    updated_at: string;//更新时间

}

// 漫画分类类型
export interface ComicsCategory {
    id: string;
    name: string;
    code: string;
    parent_id: string;
    sort_order: number;
    description: string;
    status: number;
    created_at: string;
    updated_at: string;
}

// 漫画章节类型
export interface ComicsChapter {
    id: string // 章节ID
    comic_id: string // 漫画ID
    title: string // 章节标题
    chapter_number: number // 章节序号
    read_count: number // 阅读次数
    is_locked: number // 是否锁定：0-免费，1-付费
    price: number // 单章价格
    page_count: number // 页数
    create_time: string // 创建时间
    update_time: string // 更新时间
    status: number // 状态：0-禁用，1-正常
}

// 漫画页面类型
export interface ComicsPage {
    id: string; //页面ID
    chapter_id: string; //章节ID
    page_number: number; //页码
    image_url: string; //图片URL
    width: number; //宽度
    height: number; //高度
    created_at: string; //创建时间
    updated_at: string; //更新时间
}

// 创建漫画请求类型
export interface CreateComicsRequest {
    title: string;
    description: string;
    cover: string;
    category_id: string;
    author: string;
    status: number;
    is_featured: number;
    is_paid: number;
    price: number;
    tags: string[];
}

// 更新漫画请求类型
export interface UpdateComicsRequest {
    id: string;
    title?: string;
    description?: string;
    cover?: string;
    category_id?: string;
    author?: string;
    status?: number;
    is_featured?: number;
    is_paid?: number;
    price?: number;
    tags?: string[];
}

// 创建漫画分类请求类型
export interface CreateComicsCategoryRequest {
    name: string;
    code: string;
    parent_id?: string;
    sort_order: number;
    description: string;
    status: number;
}

// 更新漫画分类请求类型
export interface UpdateComicsCategoryRequest {
    id: string;
    name?: string;
    code?: string;
    parent_id?: string;
    sort_order?: number;
    description?: string;
    status?: number;
}

// 创建漫画章节请求类型
export interface CreateComicsChapterRequest {
    comic_id: string;
    title: string;
    chapter_number: number;
    status: number;
    is_free: number;
    price: number;
}

// 更新漫画章节请求类型
export interface UpdateComicsChapterRequest {
    id: string;
    comic_id?: string;
    title?: string;
    chapter_number?: number;
    status?: number;
    is_free?: number;
    price?: number;
}

// 创建漫画页面请求类型
export interface CreateComicsPageRequest {
    comics_id: string;
    chapter_id: string;
    image_url: string;
    page_number: number;
}

// 更新漫画页面请求类型
export interface UpdateComicsPageRequest {
    id: string;
    comics_id?: string;
    chapter_id?: string;
    image_url?: string;
    page_number?: number;
}

// 查询参数
export interface ComicsQuery {
    keyword?: string;
    category_id?: string;
    status?: number;
}

// 分页参数
export interface PageParams {
    pageNo: number;
    pageSize: number;
}

// 分页请求参数
export interface ComicsParams {
    page: PageParams;
    data?: ComicsQuery;
}

// 漫画列表响应
export interface ComicsListResponse {
    list: ComicsItem[];
    total: number;
    page: number;
    pageSize: number;
} 