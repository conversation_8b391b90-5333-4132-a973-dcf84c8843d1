import { request } from '../request';

/**
 * Login
 *
 * @param userName User name
 * @param password Password
 */
export function fetchLogin(userName: string, password: string) {
  return request<any>({
    url: '/auth/login',
    method: 'post',
    data: {
      data: {
        username: userN<PERSON>,
        password: password,
        rememberMe: true
      }
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<any>({ 
    url: '/auth/getUserInfo',
    method: 'post',
    data: {
      data: {}
    }
  });
}

/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<any>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      data: {
        refreshToken
      }
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
