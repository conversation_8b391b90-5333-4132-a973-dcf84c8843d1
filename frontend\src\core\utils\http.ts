import axios, { AxiosError, type AxiosResponse, type InternalAxiosRequestConfig, type AxiosInstance,type AxiosRequestHeaders } from 'axios'
import { ElMessage } from 'element-plus'

// 请求配置接口
export interface RequestConfig extends Omit<InternalAxiosRequestConfig, 'headers'> {
  headers?: AxiosRequestHeaders
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
  successMessage?: string
  errorMessage?: string
  retry?: number
  retryDelay?: number
  cache?: boolean
  cacheTime?: number
  timeout?: number
}

// 响应数据接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
  traceId?: string
}

// 上传进度回调
export interface UploadProgressCallback {
  (progress: number, loaded: number, total: number): void
}

// 下载进度回调
export interface DownloadProgressCallback {
  (progress: number, loaded: number, total: number): void
}

// 请求拦截器类型
export interface RequestInterceptor {
  onFulfilled?: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>
  onRejected?: (error: any) => any
}

// 响应拦截器类型
export interface ResponseInterceptor {
  onFulfilled?: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>
  onRejected?: (error: AxiosError) => any
}

// 缓存管理
class RequestCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  
  set(key: string, data: any, ttl = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }
  
  get(key: string) {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  delete(key: string) {
    this.cache.delete(key)
  }
  
  clear() {
    this.cache.clear()
  }
  
  size() {
    return this.cache.size
  }
  
  keys() {
    return Array.from(this.cache.keys())
  }
}

// 请求队列管理
class RequestQueue {
  private queue: Array<() => Promise<any>> = []
  private running = 0
  private maxConcurrent = 6
  
  add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          this.running++
          const result = await request()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.running--
          this.process()
        }
      })
      
      this.process()
    })
  }
  
  private process() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return
    }
    
    const request = this.queue.shift()
    if (request) {
      request()
    }
  }
  
  setMaxConcurrent(max: number) {
    this.maxConcurrent = max
  }
  
  clear() {
    this.queue.length = 0
  }
  
  size() {
    return this.queue.length
  }
  
  getRunning() {
    return this.running
  }
}

// HTTP客户端类
export class HttpClient {
  private instance: AxiosInstance
  private cache = new RequestCache()
  private queue = new RequestQueue()
  private requestInterceptors: RequestInterceptor[] = []
  private responseInterceptors: ResponseInterceptor[] = []
  private loadingCount = 0
  
  constructor(config?: InternalAxiosRequestConfig) {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || '',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      },
      ...config
    })
    
    this.setupInterceptors()
  }
  
  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = this.getToken()
        if (token) {
          config.headers = config.headers || {}
          config.headers.Authorization = `Bearer ${token}`
        }
        
        // 添加请求ID
        config.headers = config.headers || {}
        config.headers['X-Request-ID'] = this.generateRequestId()
        
        // 添加时间戳
        config.headers['X-Timestamp'] = Date.now().toString()
        
        // 处理loading
        const requestConfig = config as RequestConfig
        if (requestConfig.showLoading !== false) {
          this.showLoading()
        }
        
        // 执行自定义拦截器
        let processedConfig = config
        for (const interceptor of this.requestInterceptors) {
          if (interceptor.onFulfilled) {
            processedConfig = interceptor.onFulfilled(processedConfig) as InternalAxiosRequestConfig
          }
        }
        
        return processedConfig
      },
      (error) => {
        this.hideLoading()
        
        // 执行自定义拦截器
        for (const interceptor of this.requestInterceptors) {
          if (interceptor.onRejected) {
            interceptor.onRejected(error)
          }
        }
        
        return Promise.reject(error)
      }
    )
    
    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        this.hideLoading()
        
        const requestConfig = response.config as RequestConfig
        
        // 显示成功消息
        if (requestConfig.showSuccess && requestConfig.successMessage) {
          ElMessage.success(requestConfig.successMessage)
        }
        
        // 执行自定义拦截器
        let processedResponse = response
        for (const interceptor of this.responseInterceptors) {
          if (interceptor.onFulfilled) {
            processedResponse = interceptor.onFulfilled(processedResponse) as AxiosResponse
          }
        }
        
        return processedResponse
      },
      (error: AxiosError) => {
        this.hideLoading()
        
        const requestConfig = error.config as RequestConfig
        
        // 处理错误
        this.handleError(error, requestConfig)
        
        // 执行自定义拦截器
        for (const interceptor of this.responseInterceptors) {
          if (interceptor.onRejected) {
            interceptor.onRejected(error)
          }
        }
        
        return Promise.reject(error)
      }
    )
  }
  
  private getToken(): string | null {
    // 从localStorage或其他地方获取token
    return localStorage.getItem('token')
  }
  
  private generateRequestId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
  
  private showLoading() {
    this.loadingCount++
    // 这里可以显示全局loading
  }
  
  private hideLoading() {
    this.loadingCount--
    if (this.loadingCount <= 0) {
      this.loadingCount = 0
      // 这里可以隐藏全局loading
    }
  }
  
  private handleError(error: AxiosError, config?: RequestConfig) {
    const response = error.response
    const status = response?.status
    
    let message = config?.errorMessage || '请求失败'
    
    switch (status) {
      case 400:
        message = '请求参数错误'
        break
      case 401:
        message = '未授权，请重新登录'
        this.handleUnauthorized()
        break
      case 403:
        message = '拒绝访问'
        break
      case 404:
        message = '请求地址不存在'
        break
      case 408:
        message = '请求超时'
        break
      case 500:
        message = '服务器内部错误'
        break
      case 502:
        message = '网关错误'
        break
      case 503:
        message = '服务不可用'
        break
      case 504:
        message = '网关超时'
        break
      default:
        if (error.code === 'ECONNABORTED') {
          message = '请求超时'
        } else if (error.message.includes('Network Error')) {
          message = '网络连接错误'
        }
    }
    
    if (config?.showError !== false) {
      ElMessage.error(message)
    }
  }
  
  private handleUnauthorized() {
    // 清除token
    localStorage.removeItem('token')
    
    // 跳转到登录页面或显示登录弹窗
    // 这里可以使用路由跳转或者触发全局事件
    window.dispatchEvent(new CustomEvent('unauthorized'))
  }
  
  private getCacheKey(config: RequestConfig): string {
    const { method, url, params, data } = config
    return `${method}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
  }
  
  private async retryRequest(config: RequestConfig, retryCount = 0): Promise<AxiosResponse> {
    const maxRetries = config.retry || 0
    const retryDelay = config.retryDelay || 1000
    
    try {
      return await this.instance.request(config)
    } catch (error) {
      if (retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retryCount)))
        return this.retryRequest(config, retryCount + 1)
      }
      throw error
    }
  }
  
  // 添加请求拦截器
  addRequestInterceptor(interceptor: RequestInterceptor) {
    this.requestInterceptors.push(interceptor)
  }
  
  // 添加响应拦截器
  addResponseInterceptor(interceptor: ResponseInterceptor) {
    this.responseInterceptors.push(interceptor)
  }
  
  // 移除拦截器
  removeRequestInterceptor(interceptor: RequestInterceptor) {
    const index = this.requestInterceptors.indexOf(interceptor)
    if (index > -1) {
      this.requestInterceptors.splice(index, 1)
    }
  }
  
  removeResponseInterceptor(interceptor: ResponseInterceptor) {
    const index = this.responseInterceptors.indexOf(interceptor)
    if (index > -1) {
      this.responseInterceptors.splice(index, 1)
    }
  }
  
  // 通用请求方法
  async request<T = any>(config: RequestConfig): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(config)
    
    // 检查缓存
    if (config.cache && config.method?.toLowerCase() === 'get') {
      const cached = this.cache.get(cacheKey)
      if (cached) {
        return cached
      }
    }
    
    // 添加到请求队列
    const response = await this.queue.add(() => this.retryRequest(config))
    
    // 缓存响应
    if (config.cache && config.method?.toLowerCase() === 'get') {
      this.cache.set(cacheKey, response.data, config.cacheTime)
    }
    
    return response.data
  }
  
  // GET请求
  async get<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
     return this.request<T>({ ...config, method: 'GET', url })
  }
  
  // POST请求
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }
  
  // PUT请求
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }
  
  // DELETE请求
  async delete<T = any>(url: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }
  
  // PATCH请求
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, method: 'PATCH', url, data })
  }
  
  // 文件上传
  async upload<T = any>(
    url: string,
    file: File | FormData,
    config?: RequestConfig & {
      onUploadProgress?: UploadProgressCallback
    }
  ): Promise<ApiResponse<T>> {
    const formData = file instanceof FormData ? file : new FormData()
    if (file instanceof File) {
      formData.append('file', file)
    }
    
    return this.request<T>({
      ...config,
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      } as AxiosRequestHeaders,
      onUploadProgress: (progressEvent) => {
        if (config?.onUploadProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          config.onUploadProgress(progress, progressEvent.loaded, progressEvent.total)
        }
      }
    })
  }
  
  // 文件下载
  async download(
    url: string,
    filename?: string,
    config?: RequestConfig & {
      onDownloadProgress?: DownloadProgressCallback
    }
  ): Promise<void> {
    const response = await this.instance.request({
      ...config,
      method: 'GET',
      url,
      responseType: 'blob',
      onDownloadProgress: (progressEvent) => {
        if (config?.onDownloadProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          config.onDownloadProgress(progress, progressEvent.loaded, progressEvent.total)
        }
      }
    })
    
    // 创建下载链接
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
  
  // 并发请求
  async all<T = any>(requests: Array<Promise<ApiResponse<T>>>): Promise<ApiResponse<T>[]> {
    return Promise.all(requests)
  }
  
  // 竞速请求
  async race<T = any>(requests: Array<Promise<ApiResponse<T>>>): Promise<ApiResponse<T>> {
    return Promise.race(requests)
  }
  
  // 取消请求
  createCancelToken() {
    return axios.CancelToken.source()
  }
  
  // 清除缓存
  clearCache(pattern?: string) {
    if (pattern) {
      const keys = this.cache.keys()
      keys.forEach(key => {
        if (key.includes(pattern)) {
          this.cache.delete(key)
        }
      })
    } else {
      this.cache.clear()
    }
  }
  
  // 获取缓存统计
  getCacheStats() {
    return {
      size: this.cache.size(),
      keys: this.cache.keys()
    }
  }
  
  // 获取队列统计
  getQueueStats() {
    return {
      pending: this.queue.size(),
      running: this.queue.getRunning()
    }
  }
  
  // 设置最大并发数
  setMaxConcurrent(max: number) {
    this.queue.setMaxConcurrent(max)
  }
  
  // 获取实例
  getInstance() {
    return this.instance
  }
}

// 创建默认实例
export const http = new HttpClient()

// 导出工具函数
export const createHttpClient = (config?: InternalAxiosRequestConfig) => {
  return new HttpClient(config)
}

// 请求工具函数
export const request = {
  get: http.get.bind(http),
  post: http.post.bind(http),
  put: http.put.bind(http),
  delete: http.delete.bind(http),
  patch: http.patch.bind(http),
  upload: http.upload.bind(http),
  download: http.download.bind(http),
  all: http.all.bind(http),
  race: http.race.bind(http)
}

// 默认导出
export default http