/**
 * Dark 主题配置
 * 基于深色调的主题
 */
import { ThemeConfig } from '../theme-manager';
import darkVariables from './variables';
import darkLightVariables from './variables-light';

// Dark 亮色主题
export const darkLightTheme: ThemeConfig = {
    name: 'dark-light',
    displayName: 'Dark Light',
    shortName: 'Dark Light',
    code: 'dark',
    primary: '#616161',
    isDark: false,
    variables: darkLightVariables
};

// Dark 暗色主题
export const darkDarkTheme: ThemeConfig = {
    name: 'dark-dark',
    displayName: 'Dark Dark',
    shortName: 'Dark Dark',
    code: 'dark',
    primary: '#424242',
    isDark: true,
    variables: darkVariables
};

// 默认导出暗色主题（保持向后兼容）
export default darkDarkTheme;