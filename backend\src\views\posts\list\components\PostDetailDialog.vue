<template>
    <el-dialog title="帖子详情" :model-value="visible" @update:model-value="handleClose" width="700px" destroy-on-close>
        <div v-if="postData" class="post-detail">
            <el-descriptions :column="1" border>
                <el-descriptions-item label="帖子ID">{{ postData.id }}</el-descriptions-item>
                <el-descriptions-item label="标题">{{ postData.title }}</el-descriptions-item>
                <el-descriptions-item label="作者">
                    <div class="user-info">
                        <el-avatar :size="24" :src="postData.user_avatar || ''" class="mr-2">
                            <el-icon>
                                <User />
                            </el-icon>
                        </el-avatar>
                        <span>{{ postData.user_name || postData.author_id }}</span>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="分类">{{ postData.category_name || '未分类' }}</el-descriptions-item>
                <el-descriptions-item label="标签">
                    <div class="tags-container">
                        <el-tag v-for="tag in postData.tags" :key="tag" size="small" class="mr-1 mb-1">
                            {{ tag }}
                        </el-tag>
                        <span v-if="!postData.tags || postData.tags.length === 0" class="text-gray-400">无标签</span>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                    <el-tag v-if="postData.status === 0" type="warning">审核中</el-tag>
                    <el-tag v-else-if="postData.status === 1" type="success">已发布</el-tag>
                    <el-tag v-else-if="postData.status === 2" type="danger">已拒绝</el-tag>
                    <el-tag v-else-if="postData.status === 3" type="info">已删除</el-tag>
                    <el-tag v-else type="info">未知</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="拒绝原因" v-if="postData.status === 2">
                    {{ postData.reject_reason || '无' }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ formatDate(postData.created_at) }}</el-descriptions-item>
                <el-descriptions-item label="更新时间">{{ formatDate(postData.updated_at) }}</el-descriptions-item>
                <el-descriptions-item label="数据统计">
                    <div class="stats-container">
                        <div class="stat-item">
                            <el-icon>
                                <View />
                            </el-icon>
                            <span>浏览: {{ postData.view_count || 0 }}</span>
                        </div>
                        <div class="stat-item">
                            <el-icon>
                                <StarFilled />
                            </el-icon>
                            <span>点赞: {{ postData.like_count || 0 }}</span>
                        </div>
                        <div class="stat-item">
                            <el-icon>
                                <ChatDotRound />
                            </el-icon>
                            <span>评论: {{ postData.comment_count || 0 }}</span>
                        </div>
                        <div class="stat-item">
                            <el-icon>
                                <Share />
                            </el-icon>
                            <span>分享: {{ postData.share_count || 0 }}</span>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>

            <div class="content-section">
                <div class="section-title">内容</div>
                <div class="content-box">{{ postData.content || '无内容' }}</div>
            </div>

            <div class="images-section" v-if="postData.images && postData.images.length > 0">
                <div class="section-title">图片</div>
                <div class="images-container">
                    <el-image v-for="(image, index) in postData.images" :key="index" :src="image" fit="cover"
                        :preview-src-list="postData.images" class="post-image" />
                </div>
            </div>

            <div class="video-section" v-if="postData.video">
                <div class="section-title">视频</div>
                <div class="video-container">
                    <video :src="postData.video" controls class="post-video"></video>
                </div>
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
                <el-button v-if="postData && postData.status === 0" type="success" @click="handleReview(1)">
                    通过
                </el-button>
                <el-button v-if="postData && postData.status === 0" type="warning" @click="handleReview(2)">
                    拒绝
                </el-button>
            </span>
        </template>

        <!-- 拒绝对话框 -->
        <el-dialog v-model="rejectDialogVisible" title="拒绝原因" width="500px" append-to-body>
            <el-form :model="rejectForm" label-width="80px">
                <el-form-item label="拒绝原因" required>
                    <el-input v-model="rejectForm.reason" type="textarea" :rows="4" placeholder="请输入拒绝原因" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="rejectDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmReject">确认</el-button>
                </span>
            </template>
        </el-dialog>
    </el-dialog>
</template>

<script setup lang="ts">
import { PostItem } from '@/types/posts';
import { formatDate } from '@/utils/date';
import { ChatDotRound, Share, StarFilled, User, View } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

// Props
interface Props {
    visible: boolean;
    postData: PostItem | null;
}

const props = defineProps<Props>();

// Emits
interface Emits {
    'update:visible': [value: boolean];
    'review': [id: string, status: number, rejectReason?: string];
}

const emit = defineEmits<Emits>();

// 拒绝对话框
const rejectDialogVisible = ref(false);
const rejectForm = ref({
    reason: '',
});

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
};

// 审核
const handleReview = (status: number) => {
    if (!props.postData) return;

    if (status === 2) {
        // 拒绝需要填写原因
        rejectForm.value.reason = '';
        rejectDialogVisible.value = true;
    } else {
        // 通过直接提交
        emit('review', props.postData.id, status);
        handleClose();
    }
};

// 确认拒绝
const confirmReject = () => {
    if (!props.postData) return;

    if (!rejectForm.value.reason) {
        ElMessage.warning('请输入拒绝原因');
        return;
    }

    emit('review', props.postData.id, 2, rejectForm.value.reason);
    rejectDialogVisible.value = false;
    handleClose();
};
</script>

<style scoped lang="scss">
.post-detail {
    max-height: 60vh;
    overflow-y: auto;
    padding-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    margin: 16px 0 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
}

.content-box {
    padding: 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    min-height: 100px;
    white-space: pre-wrap;
    word-break: break-all;
}

.images-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .post-image {
        width: 120px;
        height: 120px;
        border-radius: 4px;
        object-fit: cover;
    }
}

.video-container {
    .post-video {
        width: 100%;
        max-height: 300px;
        border-radius: 4px;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .post-detail {
        padding-right: 0;
    }

    .stats-container {
        flex-direction: column;
        gap: 8px;
    }

    .images-container {
        .post-image {
            width: 100px;
            height: 100px;
        }
    }
}
</style>