<template>
    <div class="picture-search-bar">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
            <el-form-item label="标题">
                <el-input v-model="searchForm.title" placeholder="请输入图片标题" clearable style="width: 200px"
                    @keyup.enter="handleSearch"></el-input>
            </el-form-item>

            <el-form-item label="分类">
                <el-select v-model="searchForm.category_id" filterable remote reserve-keyword placeholder="请选择分类"
                    :remote-method="handleSearchCategories" :loading="categoriesLoading" clearable
                    style="width: 200px;">
                    <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="专辑">
                <el-select v-model="searchForm.album_id" filterable placeholder="请选择专辑"
                    :filter-method="handleFilterAlbums" clearable style="width: 200px;">
                    <el-option v-for="item in filteredAlbumOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width:200px">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="正常" :value="1"></el-option>
                    <el-option label="禁用" :value="0"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="日期范围">
                <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" style="width: 260px"
                    @change="handleDateRangeChange" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon>
                        <Search />
                    </el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    重置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { getAllPictureCategories } from '@/service/api/pictures/pictures';
import { Refresh, Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';

const props = defineProps<{
    albumOptions: Array<{ value: string, label: string }>;
}>();
const emit = defineEmits(['search', 'reset', 'refresh']);

// 搜索表单数据
const searchForm = reactive({
    title: '',
    category_id: '',
    album_id: '',
    status: '',
    start_date: '',
    end_date: ''
});

// 日期范围
const dateRange = ref<string[]>([]);

// 下拉选项
const categoryOptions = ref<Array<{ value: string, label: string }>>([]);
const albumFilterKeyword = ref('');
const filteredAlbumOptions = computed(() => {
    if (!albumFilterKeyword.value) {
        return props.albumOptions;
    }
    const keyword = albumFilterKeyword.value.toLowerCase();
    return props.albumOptions.filter(item =>
        item.label.toLowerCase().includes(keyword)
    );
});

// 加载状态
const categoriesLoading = ref(false);

// 生命周期钩子
onMounted(() => {
    loadDefaultCategories();
});

// 加载默认分类
const loadDefaultCategories = async () => {
    // 加载默认分类选项
    try {
        const response = await getAllPictureCategories() as any;

        if (response.code === 2000) {
            const data = response.data;
            if (Array.isArray(data)) {
                categoryOptions.value = data.map((item: any) => ({
                    value: item.id,
                    label: item.name
                }));
            }
        }
    } catch (error) {
        console.error('加载默认分类失败:', error);
    }
};

// 搜索分类
const handleSearchCategories = async (query: string) => {
    categoriesLoading.value = true;
    try {
        const response = await getAllPictureCategories() as any;

        if (response.code === 2000) {
            const data = response.data;
            if (Array.isArray(data)) {
                const categories = data;
                if (query) {
                    categoryOptions.value = categories
                        .filter((item: any) => item.name.includes(query))
                        .map((item: any) => ({
                            value: item.id,
                            label: item.name
                        }));
                } else {
                    categoryOptions.value = categories.map((item: any) => ({
                        value: item.id,
                        label: item.name
                    }));
                }
            } else {
                categoryOptions.value = [];
            }
        } else {
            categoryOptions.value = [];
        }
    } catch (error) {
        console.error('搜索分类失败:', error);
        ElMessage.error('搜索分类失败');
        categoryOptions.value = [];
    } finally {
        categoriesLoading.value = false;
    }
};

// 过滤专辑选项
const handleFilterAlbums = (query: string) => {
    albumFilterKeyword.value = query;
};

// 处理日期范围变化
const handleDateRangeChange = (val: string[]) => {
    if (val && val.length === 2) {
        searchForm.start_date = val[0];
        searchForm.end_date = val[1];
    } else {
        searchForm.start_date = '';
        searchForm.end_date = '';
    }
};

// 处理搜索
const handleSearch = () => {
    emit('search', { ...searchForm });
};

// 处理重置
const handleReset = () => {
    // 重置表单数据
    Object.keys(searchForm).forEach(key => {
        searchForm[key as keyof typeof searchForm] = '';
    });
    dateRange.value = [];
    albumFilterKeyword.value = '';
    emit('reset');
};

// 处理刷新
const handleRefresh = () => {
    emit('refresh');
};
</script>

<style scoped lang="scss">
.picture-search-bar {
    margin-bottom: 16px;

    .el-form {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
    }

    .el-form-item {
        margin-bottom: 8px;
    }
}
</style>