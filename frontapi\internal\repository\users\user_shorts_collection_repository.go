package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserShortsCollectionRepository 用户短视频收藏数据访问接口
type UserShortsCollectionRepository interface {
	base.ExtendedRepository[users.UserShortsCollection]
}

// userShortsCollectionRepository 用户短视频收藏数据访问实现
type userShortsCollectionRepository struct {
	base.ExtendedRepository[users.UserShortsCollection]
}

// NewUserShortsCollectionRepository 创建用户短视频收藏仓库实例
func NewUserShortsCollectionRepository(db *gorm.DB) UserShortsCollectionRepository {
	return &userShortsCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserShortsCollection](db),
	}
}
