<template>
  <el-dialog
    :title="$t('books.info.title')"
    :model-value="visible"
    width="800px"
    @close="handleClose"
  >
    <el-descriptions
      v-if="book"
      :column="2"
      border
      direction="vertical"
      class="book-info"
    >
      <template #extra>
        <el-button type="primary" link @click="handleEdit">{{ $t('books.info.edit') }}</el-button>
      </template>

      <el-descriptions-item :label="$t('books.info.cover')" :span="1">
        <el-image
          :src="book.cover"
          fit="cover"
          style="width: 120px; height: 180px"
          :preview-src-list="[book.cover]"
        >
          <template #error>
            <div class="image-placeholder">
              <el-icon><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </el-descriptions-item>

      <el-descriptions-item :label="$t('books.info.basicinfo')" :span="1">
        <div class="book-meta">
          <div class="meta-item">
            <span class="label">{{ $t('books.info.title') }}:</span>
            <span class="value">{{ book.title }}</span>
            <el-tag v-if="book.is_featured === 1" type="warning" size="small" effect="dark">{{ $t('books.info.featured') }}</el-tag>
          </div>

          <div class="meta-item">
            <span class="label">{{ $t('books.info.author') }}:</span>
            <span class="value">{{ book.author }}</span>
          </div>

          <div class="meta-item">
            <span class="label">{{ $t('books.info.category') }}:</span>
            <span class="value">{{ book.category_name }}</span>
          </div>

          <div class="meta-item">
            <span class="label">{{ $t('books.info.status') }}:</span>
            <el-tag :type="book.status === 'completed' ? 'success' : 'info'">
              {{ book.status === 'completed' ? '已完结' : '连载中' }}
            </el-tag>
          </div>

          <div class="meta-item">
            <span class="label">{{ $t('books.info.wordCount') }}:</span>
            <span class="value">{{ formatWordCount(book.word_count) }}</span>
          </div>

          <div class="meta-item">
            <span class="label">{{ $t('books.info.chapterCount') }}:</span>
            <span class="value">{{ book.chapter_count }}</span>
          </div>
        </div>
      </el-descriptions-item>

      <el-descriptions-item :label="$t('books.info.description')" :span="2">
        {{ book.description }}
      </el-descriptions-item>

      <el-descriptions-item :label="$t('books.info.statistics')" :span="2">
        <div class="statistics">
          <div class="stat-item">
            <el-icon><View /></el-icon>
            <span>{{ $t('books.info.readCount') }}: {{ formatNumber(book.read_count) }}</span>
          </div>

          <div class="stat-item">
            <el-icon><Star /></el-icon>
            <span>{{ $t('books.info.favoriteCount') }}: {{ formatNumber(book.favorite_count) }}</span>
          </div>

          <div class="stat-item">
            <el-icon><ChatDotRound /></el-icon>
            <span>{{ $t('books.info.commentCount') }}: {{ formatNumber(book.comment_count) }}</span>
          </div>

          <div class="stat-item">
            <el-icon><Share /></el-icon>
            <span>{{ $t('books.info.shareCount') }}: {{ formatNumber(book.share_count) }}</span>
          </div>
        </div>
      </el-descriptions-item>

      <el-descriptions-item :label="$t('books.info.tags')" :span="2">
        <div class="tags-container">
          <el-tag
            v-for="(tag, index) in parsedTags"
            :key="index"
            class="book-tag"
            size="small"
          >
            {{ tag }}
          </el-tag>
          <span v-if="!parsedTags.length">{{ $t('books.info.noTags') }}</span>
        </div>
      </el-descriptions-item>

      <el-descriptions-item :label="$t('books.info.price')" :span="1">
        <el-tag :type="book.is_paid === 1 ? 'danger' : 'success'">
          {{ book.is_paid === 1 ? `¥${book.price}` : '免费' }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item :label="$t('books.info.publishInfo')" :span="1">
        <div class="publish-info">
          <div v-if="book.publish_date">{{ $t('books.info.publishDate') }}: {{ book.publish_date }}</div>
          <div>{{ $t('books.info.createdAt') }}: {{ book.created_at }}</div>
          <div>{{ $t('books.info.updatedAt') }}: {{ book.updated_at }}</div>
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('buttons.close') }}</el-button>
        <el-button type="primary" @click="handleChapters">{{ $t('books.info.manageChapters') }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {  computed } from 'vue';
import { Picture, View, Star, ChatDotRound, Share } from '@element-plus/icons-vue';
import type { BookItem } from '@/types/books';
import { useRouter } from 'vue-router';
import { $t } from '@/locales';

// 路由
const router = useRouter();

// 定义接收的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  book: {
    type: Object as () => BookItem,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['close', 'edit']);

// 解析标签
const parsedTags = computed(() => {
  if (!props.book) return [];
  return props.book.tags ? props.book.tags: [];
});

// 处理关闭
const handleClose = () => {
  emit('close');
};

// 处理编辑
const handleEdit = () => {
  emit('edit', props.book);
  emit('close');
};

// 处理章节管理
const handleChapters = () => {
  router.push({
    path: '/books/chapter',
    query: { bookId: props.book.id }
  });
  emit('close');
};

// 格式化字数
const formatWordCount = (wordCount: number): string => {
  if (!wordCount) return '0字';
  if (wordCount >= 10000) {
    return (wordCount / 10000).toFixed(1) + '万字';
  }
  return wordCount + '字';
};

// 格式化数字（添加千分位）
const formatNumber = (num: number): string => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  return num.toString();
};
</script>

<style scoped>
.book-info {
  margin-bottom: 20px;
}

.book-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-item .label {
  font-weight: bold;
  min-width: 70px;
}

.statistics {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.book-tag {
  margin-right: 5px;
}

.publish-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 120px;
  height: 180px;
  background-color: #f0f0f0;
  color: #909399;
  font-size: 24px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
