package system

import (
	"errors"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strings"
	"time"

	"frontapi/internal/admin"
	"frontapi/pkg/image_utils"
	"frontapi/pkg/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
)

// 文件类型常量
const (
	FileTypeImage      = "image"
	FileTypeVideo      = "video"
	FileTypeSmallImage = "small_image"
	FileTypeAll        = "all"

	// 统一的目录类型常量
	DirTypeStatic   = "static"
	DirTypePictures = "pictures"
	DirTypeVideos   = "videos"
)

// FileItem 文件信息结构
type FileItem struct {
	Name       string    `json:"name"`        // 文件名
	URL        string    `json:"url"`         // 访问URL
	Path       string    `json:"path"`        // 相对路径
	FullPath   string    `json:"full_path"`   // 完整路径，包括基础路径
	Size       int64     `json:"size"`        // 文件大小(字节)
	Type       string    `json:"type"`        // 文件类型
	Extension  string    `json:"extension"`   // 文件扩展名
	IsDir      bool      `json:"is_dir"`      // 是否为目录
	ModifiedAt time.Time `json:"modified_at"` // 修改时间
	CreatedAt  time.Time `json:"created_at"`  // 创建时间
}

// DirectoryItem 目录树项目结构
type DirectoryItem struct {
	Name     string          `json:"name"`     // 目录名称
	Path     string          `json:"path"`     // 目录路径
	Type     string          `json:"type"`     // 目录类型
	Children []DirectoryItem `json:"children"` // 子目录（仅返回第一级）
}

// FileListResponse 文件列表响应
type FileListResponse struct {
	Files         []FileItem      `json:"files"`          // 文件列表
	Path          string          `json:"path"`           // 当前路径
	TotalFiles    int             `json:"total_files"`    // 文件总数
	TotalSize     int64           `json:"total_size"`     // 总大小
	AllowedTypes  []string        `json:"allowed_types"`  // 允许的文件类型
	DirectoryTree []DirectoryItem `json:"directory_tree"` // 目录树结构（仅在根目录请求时返回）
}

// FileController 文件管理控制器
type FileController struct {
	admin.BaseController
	// 路径配置
	basePaths map[string]string
	// URL前缀配置
	urlPrefixes map[string]string
	// 默认限制返回的文件数
	defaultLimit int
}

// NewFileController 创建文件管理控制器
func NewFileController() *FileController {
	controller := &FileController{
		basePaths: map[string]string{
			FileTypeImage:      utils.GetEnv("UPLOAD_PICTURE_PATH", "../storage/pictures"),
			FileTypeVideo:      utils.GetEnv("UPLOAD_VIDEO_PATH", "../storage/videos"),
			FileTypeSmallImage: utils.GetEnv("UPLOAD_SMALL_IMAGE_PATH", "../storage/static/images"),
		},
		urlPrefixes: map[string]string{
			FileTypeImage:      utils.GetEnv("UPLOAD_PICTURE_URL", "/pictures"),
			FileTypeVideo:      utils.GetEnv("UPLOAD_VIDEO_URL", "/videos"),
			FileTypeSmallImage: utils.GetEnv("UPLOAD_SMALL_IMAGE_URL", "/static/images"),
		},
		defaultLimit: 100,
	}

	return controller
}

// GetFileList 获取文件列表
func (c *FileController) GetFileList(ctx *fiber.Ctx) error {
	// 获取当前请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 获取查询参数
	fileType := reqInfo.Get("file_type").GetString() // 文件类型：image, video, small_image, all
	dirPath := reqInfo.Get("path").GetString()       // 相对路径
	searchTerm := reqInfo.Get("search").GetString()  // 搜索关键词
	limit := reqInfo.Get("limit").GetInt()           // 限制数量
	if limit <= 0 {
		limit = c.defaultLimit
	}

	// 安全检查：确保路径不包含 '..'，防止路径遍历攻击
	if strings.Contains(dirPath, "..") {
		return c.BadRequest(ctx, "无效的路径参数", nil)
	}

	// 收集所有文件
	var allFiles []FileItem
	var totalSize int64
	var directoryTree []DirectoryItem

	// 如果是根目录，返回所有配置的目录作为虚拟目录
	if dirPath == "" {
		// 添加所有配置的目录作为顶级目录
		for pathType := range c.basePaths {
			// 获取目录名称和类型
			dirName := pathType
			dirType := getDirType(pathType)

			// 可读性友好的名称
			switch pathType {
			case FileTypeSmallImage:
				dirName = "小图片"
				dirType = DirTypeStatic
			case FileTypeImage:
				dirName = "图片"
				dirType = DirTypePictures
			case FileTypeVideo:
				dirName = "视频"
				dirType = DirTypeVideos
			}

			// 根据前端传递的文件类型过滤
			if fileType != FileTypeAll {
				switch fileType {
				case "image":
					if pathType != FileTypeImage {
						continue
					}
				case "video":
					if pathType != FileTypeVideo {
						continue
					}
				case "static":
					if pathType != FileTypeSmallImage {
						continue
					}
				default:
					// 对于旧版本兼容，直接使用pathType比较
					if fileType != pathType {
						continue
					}
				}
			}

			// 创建虚拟目录项
			dirItem := FileItem{
				Name:       dirName,
				URL:        c.urlPrefixes[pathType],
				Path:       dirType, // 使用规范化的目录类型作为路径标识符
				FullPath:   c.basePaths[pathType],
				Size:       0,
				Type:       dirType,
				IsDir:      true,
				ModifiedAt: time.Now(),
				CreatedAt:  time.Now(),
			}

			allFiles = append(allFiles, dirItem)

			// 同时添加到目录树结构中
			treeItem := DirectoryItem{
				Name:     dirName,
				Path:     dirType,
				Type:     dirType,
				Children: []DirectoryItem{}, // 初始化为空数组
			}

			// 获取该类型目录的第一级子目录
			children, err := c.getFirstLevelDirectories(pathType)
			if err == nil {
				treeItem.Children = children
			}

			directoryTree = append(directoryTree, treeItem)
		}
	} else {
		// 检查是否是顶级虚拟目录
		var selectedBasePath string
		var selectedUrlPrefix string
		var selectedType string
		var realDirPath string

		// 获取路径的第一个部分，作为目录类型
		parts := strings.SplitN(dirPath, "/", 2)
		dirType := parts[0]

		// 根据目录类型确定使用哪个基础路径
		switch dirType {
		case DirTypeStatic:
			selectedBasePath = c.basePaths[FileTypeSmallImage]
			selectedUrlPrefix = c.urlPrefixes[FileTypeSmallImage]
			selectedType = DirTypeStatic
		case DirTypePictures:
			selectedBasePath = c.basePaths[FileTypeImage]
			selectedUrlPrefix = c.urlPrefixes[FileTypeImage]
			selectedType = DirTypePictures
		case DirTypeVideos:
			selectedBasePath = c.basePaths[FileTypeVideo]
			selectedUrlPrefix = c.urlPrefixes[FileTypeVideo]
			selectedType = DirTypeVideos
		default:
			// 兼容旧的方式，尝试使用fileType作为键
			switch fileType {
			case "static":
				selectedBasePath = c.basePaths[FileTypeSmallImage]
				selectedUrlPrefix = c.urlPrefixes[FileTypeSmallImage]
				selectedType = DirTypeStatic
			case "image":
				selectedBasePath = c.basePaths[FileTypeImage]
				selectedUrlPrefix = c.urlPrefixes[FileTypeImage]
				selectedType = DirTypePictures
			case "video":
				selectedBasePath = c.basePaths[FileTypeVideo]
				selectedUrlPrefix = c.urlPrefixes[FileTypeVideo]
				selectedType = DirTypeVideos
			default:
				// 如果都没有匹配，可能是无效的路径
				return c.BadRequest(ctx, "无效的目录类型: "+dirType, nil)
			}
		}

		// 获取真实的目录路径（移除目录类型前缀）
		if len(parts) > 1 {
			realDirPath = parts[1]
		} else {
			realDirPath = ""
		}

		// 构建完整路径
		fullPath := filepath.Join(selectedBasePath, realDirPath)

		// 检查路径是否存在
		if _, err := os.Stat(fullPath); os.IsNotExist(err) {
			// 尝试一种替代路径解析方式
			modifiedPath := dirPath
			for _, prefix := range []string{fileType, dirType, "image", "video", "videos", "static", "pictures", DirTypeStatic, DirTypePictures, DirTypeVideos} {
				if strings.HasPrefix(modifiedPath, prefix+"/") {
					modifiedPath = strings.TrimPrefix(modifiedPath, prefix+"/")
					break
				}
			}

			// 重新构建完整路径
			altFullPath := filepath.Join(selectedBasePath, modifiedPath)
			if _, altErr := os.Stat(altFullPath); os.IsNotExist(altErr) {
				return c.NotFound(ctx, fmt.Sprintf("目录不存在，尝试路径: 1.%s 2.%s", fullPath, altFullPath))
			}

			// 如果找到了目录，更新路径
			fullPath = altFullPath
			realDirPath = modifiedPath
		}

		// 遍历目录
		err := filepath.WalkDir(fullPath, func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return nil // 忽略错误，继续遍历
			}

			// 跳过根目录本身
			if path == fullPath {
				return nil
			}

			// 只处理当前目录下的文件，不递归子目录
			relPath, err := filepath.Rel(fullPath, path)
			if err != nil || strings.Contains(relPath, string(os.PathSeparator)) {
				return fs.SkipDir
			}

			// 获取文件信息
			info, err := d.Info()
			if err != nil {
				return nil
			}

			// 如果有搜索词，检查文件名是否匹配
			if searchTerm != "" && !strings.Contains(strings.ToLower(info.Name()), strings.ToLower(searchTerm)) {
				return nil
			}

			// 构建URL和路径
			var filePath string
			if realDirPath == "" {
				filePath = info.Name()
			} else {
				filePath = filepath.Join(realDirPath, info.Name())
			}

			// 构建完整的路径，包括虚拟目录前缀
			virtualPath := filepath.Join(selectedType, filePath)
			fileURL := fmt.Sprintf("%s/%s", selectedUrlPrefix, filepath.ToSlash(filePath))

			// 获取文件扩展名
			ext := strings.ToLower(filepath.Ext(info.Name()))
			if ext != "" {
				ext = ext[1:] // 移除前导'.'
			}

			// 创建文件项
			fileItem := FileItem{
				Name:       info.Name(),
				URL:        fileURL,
				Path:       filepath.ToSlash(virtualPath),
				FullPath:   filepath.ToSlash(path),
				Size:       info.Size(),
				Type:       selectedType,
				Extension:  ext,
				IsDir:      info.IsDir(),
				ModifiedAt: info.ModTime(),
				CreatedAt:  info.ModTime(),
			}

			allFiles = append(allFiles, fileItem)
			totalSize += info.Size()

			return nil
		})

		if err != nil {
			return c.InternalServerError(ctx, "读取文件列表失败: "+err.Error())
		}
	}

	// 排序文件：目录优先，然后按名称排序
	sort.Slice(allFiles, func(i, j int) bool {
		// 如果一个是目录而另一个不是，目录优先
		if allFiles[i].IsDir != allFiles[j].IsDir {
			return allFiles[i].IsDir
		}
		// 否则按名称排序
		return allFiles[i].Name < allFiles[j].Name
	})

	// 应用限制
	if len(allFiles) > limit {
		allFiles = allFiles[:limit]
	}

	// 构建响应
	response := FileListResponse{
		Files:      allFiles,
		Path:       dirPath,
		TotalFiles: len(allFiles),
		TotalSize:  totalSize,
		AllowedTypes: []string{
			"jpg", "jpeg", "png", "gif", "webp", // 图片格式
			"mp4", "avi", "mov", "mkv", // 视频格式
		},
		DirectoryTree: directoryTree, // 仅在根目录请求时返回目录树
	}

	return c.Success(ctx, response)
}

// getFirstLevelDirectories 获取指定类型目录下的第一级子目录
func (c *FileController) getFirstLevelDirectories(pathType string) ([]DirectoryItem, error) {
	var result []DirectoryItem
	var basePath, normalizedDirType string

	// 确定基础路径和规范化的目录类型
	switch pathType {
	case FileTypeSmallImage:
		basePath = c.basePaths[FileTypeSmallImage]
		normalizedDirType = DirTypeStatic
	case FileTypeImage:
		basePath = c.basePaths[FileTypeImage]
		normalizedDirType = DirTypePictures
	case FileTypeVideo:
		basePath = c.basePaths[FileTypeVideo]
		normalizedDirType = DirTypeVideos
	default:
		return nil, fmt.Errorf("未知的路径类型: %s", pathType)
	}

	// 读取目录
	entries, err := os.ReadDir(basePath)
	if err != nil {
		return nil, err
	}

	// 筛选出目录
	for _, entry := range entries {
		if entry.IsDir() {
			// 构建子目录项
			childPath := filepath.Join(normalizedDirType, entry.Name())
			item := DirectoryItem{
				Name:     entry.Name(),
				Path:     childPath,
				Type:     normalizedDirType,
				Children: []DirectoryItem{}, // 不递归获取更深层次的子目录
			}
			result = append(result, item)
		}
	}

	return result, nil
}

// UploadFile 上传文件
func (c *FileController) UploadFile(ctx *fiber.Ctx) error {
	// 获取文件类型和目标路径
	fileType := ctx.FormValue("file_type")
	dirPath := ctx.FormValue("path")

	// 安全检查：确保路径不包含 '..'，防止路径遍历攻击
	if strings.Contains(dirPath, "..") {
		return c.BadRequest(ctx, "无效的路径参数", nil)
	}

	// 检查是否尝试直接上传到根目录
	parts := strings.Split(dirPath, "/")
	if len(parts) == 1 && (dirPath == DirTypeStatic || dirPath == DirTypePictures || dirPath == DirTypeVideos) {
		return c.BadRequest(ctx, "不允许直接上传文件到根目录，请先创建一个子目录", nil)
	}

	// 根据提供的fileType确定使用哪个基础路径
	var basePath, urlPrefix, normalizedDirType string
	switch fileType {
	case DirTypeStatic:
		basePath = c.basePaths[FileTypeSmallImage]
		urlPrefix = c.urlPrefixes[FileTypeSmallImage]
		normalizedDirType = DirTypeStatic
	case DirTypePictures, "image":
		basePath = c.basePaths[FileTypeImage]
		urlPrefix = c.urlPrefixes[FileTypeImage]
		normalizedDirType = DirTypePictures
	case DirTypeVideos, "video":
		basePath = c.basePaths[FileTypeVideo]
		urlPrefix = c.urlPrefixes[FileTypeVideo]
		normalizedDirType = DirTypeVideos
	default:
		// 兼容旧的方式，直接使用fileType作为键
		if path, ok := c.basePaths[fileType]; ok {
			basePath = path
			urlPrefix = c.urlPrefixes[fileType]
			normalizedDirType = getDirType(fileType)
		} else {
			return c.BadRequest(ctx, "无效的文件类型: "+fileType, nil)
		}
	}

	// 处理dirPath，确保路径格式正确
	// 完全删除路径前缀部分，只保留相对于类型目录的路径
	processedPath := dirPath
	for _, prefix := range []string{fileType, normalizedDirType, "image", "video", "videos", "static", "pictures", DirTypeStatic, DirTypePictures, DirTypeVideos} {
		if strings.HasPrefix(processedPath, prefix+"/") {
			processedPath = strings.TrimPrefix(processedPath, prefix+"/")
			break
		}
	}

	// 进一步检查处理后的路径中是否还有目录类型前缀
	for _, dirPrefix := range []string{DirTypeStatic, DirTypePictures, DirTypeVideos} {
		if strings.HasPrefix(processedPath, dirPrefix+"/") {
			processedPath = strings.TrimPrefix(processedPath, dirPrefix+"/")
		}
	}

	// 检查处理后的路径是否为空，如果为空说明是在根目录操作
	if processedPath == "" {
		return c.BadRequest(ctx, "不允许直接上传文件到根目录，请先创建一个子目录", nil)
	}

	// 构建完整目标路径
	targetDir := filepath.Join(basePath, processedPath)
	fmt.Printf("上传目标路径: %s\n", targetDir)

	// 确保目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return c.InternalServerError(ctx, "创建目录失败: "+err.Error())
	}

	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		return c.BadRequest(ctx, "获取上传文件失败: "+err.Error(), nil)
	}

	// 生成唯一文件名
	originalName := file.Filename
	ext := filepath.Ext(originalName)
	filename := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// 构建文件保存路径
	savePath := filepath.Join(targetDir, filename)

	// 保存文件
	if err := ctx.SaveFile(file, savePath); err != nil {
		return c.InternalServerError(ctx, "保存文件失败: "+err.Error())
	}

	// 显式打开并关闭文件，确保文件句柄被正确释放
	f, err := os.Open(savePath)
	if err == nil {
		f.Close() // 立即关闭文件
	}

	// 如果上传的是图片文件，异步压缩图片
	lowerExt := strings.ToLower(ext)
	if lowerExt == ".jpg" || lowerExt == ".jpeg" || lowerExt == ".png" {
		// 使用新的图片处理包进行异步压缩
		image_utils.CompressImageAsync(savePath)
	}

	// 获取文件信息
	fileInfo, err := os.Stat(savePath)
	if err != nil {
		return c.InternalServerError(ctx, "获取文件信息失败: "+err.Error())
	}

	// 构建URL（不包含目录类型前缀，只用于访问）
	fileURL := fmt.Sprintf("%s/%s", urlPrefix, filepath.ToSlash(filepath.Join(processedPath, filename)))

	// 构建虚拟路径（带有目录类型前缀，用于前端显示和后续操作）
	virtualPath := filepath.ToSlash(filepath.Join(normalizedDirType, processedPath, filename))
	fmt.Printf("虚拟路径: %s\n", virtualPath)

	// 构建响应
	fileItem := FileItem{
		Name:       filename,
		URL:        fileURL,
		Path:       virtualPath,
		Size:       fileInfo.Size(),
		Type:       normalizedDirType,
		Extension:  strings.TrimPrefix(ext, "."),
		IsDir:      false,
		ModifiedAt: fileInfo.ModTime(),
		CreatedAt:  fileInfo.ModTime(),
	}

	return c.Success(ctx, fileItem)
}

// RenameFile 重命名文件
func (c *FileController) RenameFile(ctx *fiber.Ctx) error {
	// 获取当前请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 获取参数
	fileType := reqInfo.Get("file_type").GetString()
	oldPath := reqInfo.Get("old_path").GetString()
	newName := reqInfo.Get("new_name").GetString()

	// 安全检查
	if strings.Contains(oldPath, "..") || strings.Contains(newName, "..") {
		return c.BadRequest(ctx, "无效的路径参数", nil)
	}

	// 获取路径的第一个部分，作为目录类型
	var selectedBasePath, selectedUrlPrefix, normalizedDirType string
	parts := strings.SplitN(oldPath, "/", 2)

	if len(parts) == 0 {
		return c.BadRequest(ctx, "无效的路径", nil)
	}

	dirType := parts[0]

	// 根据目录类型确定使用哪个基础路径
	switch dirType {
	case DirTypeStatic:
		selectedBasePath = c.basePaths[FileTypeSmallImage]
		selectedUrlPrefix = c.urlPrefixes[FileTypeSmallImage]
		normalizedDirType = DirTypeStatic
	case DirTypePictures:
		selectedBasePath = c.basePaths[FileTypeImage]
		selectedUrlPrefix = c.urlPrefixes[FileTypeImage]
		normalizedDirType = DirTypePictures
	case DirTypeVideos:
		selectedBasePath = c.basePaths[FileTypeVideo]
		selectedUrlPrefix = c.urlPrefixes[FileTypeVideo]
		normalizedDirType = DirTypeVideos
	default:
		// 如果路径中未提供有效的目录类型，则尝试使用fileType
		switch fileType {
		case DirTypeStatic, FileTypeSmallImage:
			selectedBasePath = c.basePaths[FileTypeSmallImage]
			selectedUrlPrefix = c.urlPrefixes[FileTypeSmallImage]
			normalizedDirType = DirTypeStatic
		case DirTypePictures, FileTypeImage:
			selectedBasePath = c.basePaths[FileTypeImage]
			selectedUrlPrefix = c.urlPrefixes[FileTypeImage]
			normalizedDirType = DirTypePictures
		case DirTypeVideos, FileTypeVideo:
			selectedBasePath = c.basePaths[FileTypeVideo]
			selectedUrlPrefix = c.urlPrefixes[FileTypeVideo]
			normalizedDirType = DirTypeVideos
		default:
			// 最后尝试直接使用作为键
			if basePath, ok := c.basePaths[dirType]; ok {
				selectedBasePath = basePath
				selectedUrlPrefix = c.urlPrefixes[dirType]
				normalizedDirType = getDirType(dirType)
			} else if basePath, ok := c.basePaths[fileType]; ok {
				selectedBasePath = basePath
				selectedUrlPrefix = c.urlPrefixes[fileType]
				normalizedDirType = getDirType(fileType)
			} else {
				return c.BadRequest(ctx, "无效的文件类型: "+fileType, nil)
			}
		}
	}

	// 获取实际的文件路径
	var realOldPath string
	if len(parts) > 1 {
		realOldPath = parts[1]
	} else {
		realOldPath = ""
	}

	// 防止路径中包含重复的目录类型前缀
	for _, dirPrefix := range []string{DirTypeStatic, DirTypePictures, DirTypeVideos} {
		if strings.HasPrefix(realOldPath, dirPrefix+"/") {
			realOldPath = strings.TrimPrefix(realOldPath, dirPrefix+"/")
		}
	}

	// 构建完整路径
	oldFullPath := filepath.Join(selectedBasePath, realOldPath)
	dirPath := filepath.Dir(realOldPath)
	newFullPath := filepath.Join(selectedBasePath, dirPath, newName)

	fmt.Printf("准备重命名: %s -> %s\n", oldFullPath, newFullPath)

	// 检查文件是否存在
	if _, err := os.Stat(oldFullPath); os.IsNotExist(err) {
		// 尝试使用不同的路径格式处理
		modifiedPath := oldPath
		for _, prefix := range []string{fileType, dirType, "image", "video", "videos", "static", "pictures", DirTypeStatic, DirTypePictures, DirTypeVideos} {
			if strings.HasPrefix(modifiedPath, prefix+"/") {
				modifiedPath = strings.TrimPrefix(modifiedPath, prefix+"/")
				break
			}
		}

		// 检查处理后的路径中是否还有目录类型前缀
		for _, dirPrefix := range []string{DirTypeStatic, DirTypePictures, DirTypeVideos} {
			if strings.HasPrefix(modifiedPath, dirPrefix+"/") {
				modifiedPath = strings.TrimPrefix(modifiedPath, dirPrefix+"/")
			}
		}

		// 重新构建完整路径
		altOldFullPath := filepath.Join(selectedBasePath, modifiedPath)
		if _, altErr := os.Stat(altOldFullPath); os.IsNotExist(altErr) {
			return c.NotFound(ctx, fmt.Sprintf("文件不存在: 尝试路径1.%s 2.%s", oldFullPath, altOldFullPath))
		}

		// 如果找到了文件，更新路径
		oldFullPath = altOldFullPath
		dirPath = filepath.Dir(modifiedPath)
		newFullPath = filepath.Join(selectedBasePath, dirPath, newName)
	}

	// 检查新名称文件是否已存在
	if _, err := os.Stat(newFullPath); err == nil {
		return c.BadRequest(ctx, "目标文件已存在", nil)
	}

	// 获取原始文件信息，用于返回
	fileInfo, err := os.Stat(oldFullPath)
	if err != nil {
		return c.InternalServerError(ctx, "获取文件信息失败: "+err.Error())
	}

	// 检查是否为目录
	isDir := fileInfo.IsDir()

	var renameSuccess bool // 跟踪是否成功重命名

	// 尝试重命名文件
	err = os.Rename(oldFullPath, newFullPath)
	if err != nil {
		// 如果文件被占用，尝试复制文件然后删除原文件
		if strings.Contains(err.Error(), "being used by another process") || strings.Contains(err.Error(), "process cannot access") {
			fmt.Printf("文件被占用，尝试复制方法: %s\n", err.Error())

			if !isDir {
				// 使用复制文件的方法
				err = copyFile(oldFullPath, newFullPath)
				if err != nil {
					return c.InternalServerError(ctx, "重命名文件失败（无法复制）: "+err.Error())
				}

				// 复制成功，设置重命名成功标志
				renameSuccess = true

				// 尝试删除原文件，但如果删除失败，不影响重命名的整体成功
				// 先等待一点时间，让文件句柄释放
				for i := 0; i < 3; i++ { // 尝试三次
					time.Sleep(100 * time.Millisecond)
					delErr := os.Remove(oldFullPath)
					if delErr == nil {
						// 删除成功
						fmt.Printf("成功删除源文件\n")
						break
					} else if i == 2 {
						// 最后一次尝试仍然失败
						fmt.Printf("无法删除源文件，但已创建新文件: %s\n", delErr.Error())
					}
				}
			} else {
				// 这是一个目录，复制方法不适用
				return c.InternalServerError(ctx, "无法重命名目录: "+err.Error())
			}
		} else {
			return c.InternalServerError(ctx, "重命名文件失败: "+err.Error())
		}
	} else {
		// 直接重命名成功
		renameSuccess = true
	}

	if !renameSuccess {
		return c.InternalServerError(ctx, "重命名操作失败")
	}

	// 重新获取文件信息（重命名后）
	fileInfo, err = os.Stat(newFullPath)
	if err != nil {
		return c.InternalServerError(ctx, "获取新文件信息失败: "+err.Error())
	}

	// 构建新的虚拟路径
	var virtualPath string
	if dirPath == "" || dirPath == "." {
		virtualPath = filepath.Join(normalizedDirType, newName)
	} else {
		virtualPath = filepath.Join(normalizedDirType, dirPath, newName)
	}

	// 构建完整URL
	fileURL := fmt.Sprintf("%s/%s", selectedUrlPrefix, filepath.ToSlash(filepath.Join(dirPath, newName)))

	fmt.Printf("重命名完成: 虚拟路径=%s, URL=%s\n", virtualPath, fileURL)

	// 构建响应
	fileItem := FileItem{
		Name:       newName,
		URL:        fileURL,
		Path:       filepath.ToSlash(virtualPath),
		Size:       fileInfo.Size(),
		Type:       normalizedDirType,
		Extension:  strings.TrimPrefix(filepath.Ext(newName), "."),
		IsDir:      fileInfo.IsDir(),
		ModifiedAt: fileInfo.ModTime(),
		CreatedAt:  fileInfo.ModTime(),
	}

	return c.Success(ctx, fileItem)
}

// 添加文件复制函数
func copyFile(src, dst string) error {
	// 打开源文件
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("无法打开源文件: %w", err)
	}
	defer sourceFile.Close()

	// 创建目标文件
	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("无法创建目标文件: %w", err)
	}
	defer destFile.Close()

	// 获取源文件信息
	sourceInfo, err := sourceFile.Stat()
	if err != nil {
		return fmt.Errorf("无法获取源文件信息: %w", err)
	}

	// 复制文件内容
	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("复制文件内容失败: %w", err)
	}

	// 确保文件刷新到磁盘
	err = destFile.Sync()
	if err != nil {
		return fmt.Errorf("刷新文件内容失败: %w", err)
	}

	// 设置权限
	err = os.Chmod(dst, sourceInfo.Mode())
	if err != nil {
		return fmt.Errorf("设置文件权限失败: %w", err)
	}

	return nil
}

// DeleteFile 删除文件或目录
func (c *FileController) DeleteFile(ctx *fiber.Ctx) error {
	// 获取当前请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 获取文件类型和路径
	fileType := reqInfo.Get("file_type").GetString()
	filePath := reqInfo.Get("path").GetString()

	// 安全检查：确保路径不包含 '..'，防止路径遍历攻击
	if strings.Contains(filePath, "..") {
		return c.BadRequest(ctx, "无效的路径参数", nil)
	}

	// 获取路径的第一个部分，作为目录类型
	var selectedBasePath string
	parts := strings.SplitN(filePath, "/", 2)

	if len(parts) == 0 {
		return c.BadRequest(ctx, "无效的路径", nil)
	}

	dirType := parts[0]

	// 根据目录类型确定使用哪个基础路径
	switch dirType {
	case DirTypeStatic:
		selectedBasePath = c.basePaths[FileTypeSmallImage]
	case DirTypePictures:
		selectedBasePath = c.basePaths[FileTypeImage]
	case DirTypeVideos:
		selectedBasePath = c.basePaths[FileTypeVideo]
	default:
		// 如果路径中未提供有效的目录类型，则尝试使用fileType
		switch fileType {
		case DirTypeStatic, FileTypeSmallImage:
			selectedBasePath = c.basePaths[FileTypeSmallImage]
		case DirTypePictures, FileTypeImage:
			selectedBasePath = c.basePaths[FileTypeImage]
		case DirTypeVideos, FileTypeVideo:
			selectedBasePath = c.basePaths[FileTypeVideo]
		default:
			// 最后尝试直接使用作为键
			if basePath, ok := c.basePaths[dirType]; ok {
				selectedBasePath = basePath
			} else if basePath, ok := c.basePaths[fileType]; ok {
				selectedBasePath = basePath
			} else {
				return c.BadRequest(ctx, "无效的文件类型: "+fileType, nil)
			}
		}
	}

	// 获取实际的文件路径
	var realFilePath string
	if len(parts) > 1 {
		realFilePath = parts[1]
	} else {
		realFilePath = ""
	}

	// 防止路径中包含重复的目录名称（例如static/static）
	for _, dirPrefix := range []string{DirTypeStatic, DirTypePictures, DirTypeVideos} {
		if strings.HasPrefix(realFilePath, dirPrefix+"/") {
			realFilePath = strings.TrimPrefix(realFilePath, dirPrefix+"/")
		}
	}

	// 构建完整路径
	fullPath := filepath.Join(selectedBasePath, realFilePath)

	// 检查文件是否存在
	fileInfo, err := os.Stat(fullPath)
	if os.IsNotExist(err) {
		// 尝试使用不同的路径格式处理
		fmt.Printf("文件不存在，尝试其他格式: %s. 原始路径: %s, fileType: %s\n", fullPath, filePath, fileType)

		// 尝试完全重新解析路径，删除所有可能的前缀
		modifiedPath := filePath
		for _, prefix := range []string{fileType, dirType, "image", "video", "videos", "static", "pictures", DirTypeStatic, DirTypePictures, DirTypeVideos} {
			if strings.HasPrefix(modifiedPath, prefix+"/") {
				modifiedPath = strings.TrimPrefix(modifiedPath, prefix+"/")
				break
			}
		}

		// 重新构建完整路径
		altFullPath := filepath.Join(selectedBasePath, modifiedPath)
		fileInfo, err = os.Stat(altFullPath)

		if os.IsNotExist(err) {
			return c.NotFound(ctx, fmt.Sprintf("文件不存在，尝试路径: 1.%s 2.%s", fullPath, altFullPath))
		}

		// 如果找到了文件，更新路径
		fullPath = altFullPath
	}

	if err != nil {
		return c.InternalServerError(ctx, "获取文件信息失败: "+err.Error())
	}

	// 如果是目录，检查是否为空
	if fileInfo.IsDir() {
		isEmpty, err := isDirEmpty(fullPath)
		if err != nil {
			return c.InternalServerError(ctx, "检查目录是否为空失败: "+err.Error())
		}
		if !isEmpty {
			return c.BadRequest(ctx, "目录不为空，无法删除", nil)
		}
	}

	// 尝试多次删除文件
	success := false
	var lastError error

	// 如果是文件（不是目录），先尝试关闭可能的文件句柄
	if !fileInfo.IsDir() {
		// 尝试打开和关闭文件，确保任何现有的句柄被刷新
		f, openErr := os.Open(fullPath)
		if openErr == nil {
			f.Close() // 立即关闭文件
		}
	}

	// 多次尝试删除文件，每次间隔增加
	for i := 0; i < 5; i++ {
		// 尝试删除文件或目录
		err = os.Remove(fullPath)
		if err == nil {
			success = true
			break
		}

		lastError = err

		// 如果文件被占用，等待更长时间再尝试
		if strings.Contains(err.Error(), "being used by another process") || strings.Contains(err.Error(), "process cannot access") {
			// 增加等待时间
			waitTime := time.Duration(100*(i+1)) * time.Millisecond
			fmt.Printf("文件被占用，等待 %v 后再次尝试删除...\n", waitTime)
			time.Sleep(waitTime)

			// 运行垃圾回收，尝试释放可能的内存引用
			if i > 1 {
				runtime.GC()
			}

			continue
		}

		// 如果不是"文件被占用"错误，不再重试
		break
	}

	if !success {
		// 如果是文件被占用错误，给出更友好的错误信息
		if strings.Contains(lastError.Error(), "being used by another process") || strings.Contains(lastError.Error(), "process cannot access") {
			return c.BadRequest(ctx, "文件正在被使用，请关闭相关程序后重试删除。", nil)
		}

		return c.InternalServerError(ctx, "删除失败: "+lastError.Error())
	}

	return c.SuccessWithMessage(ctx, "文件已成功删除")
}

// CreateDirectory 创建目录
func (c *FileController) CreateDirectory(ctx *fiber.Ctx) error {
	// 获取当前请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 获取参数
	fileType := reqInfo.Get("file_type").GetString()
	parentPath := reqInfo.Get("parent_path").GetString()
	dirName := reqInfo.Get("dir_name").GetString()

	// 安全检查
	if strings.Contains(parentPath, "..") || strings.Contains(dirName, "..") {
		return c.BadRequest(ctx, "无效的路径参数", nil)
	}

	// 检查是否是根目录操作（static, pictures, videos）
	parts := strings.Split(parentPath, "/")
	if len(parts) == 1 && (parentPath == DirTypeStatic || parentPath == DirTypePictures || parentPath == DirTypeVideos) {
		return c.BadRequest(ctx, "不允许在根目录下直接创建目录，请先创建一个子目录", nil)
	}

	// 根据提供的fileType确定使用哪个基础路径
	var basePath, urlPrefix, normalizedDirType string
	switch fileType {
	case DirTypeStatic:
		basePath = c.basePaths[FileTypeSmallImage]
		urlPrefix = c.urlPrefixes[FileTypeSmallImage]
		normalizedDirType = DirTypeStatic
	case DirTypePictures, "image":
		basePath = c.basePaths[FileTypeImage]
		urlPrefix = c.urlPrefixes[FileTypeImage]
		normalizedDirType = DirTypePictures
	case DirTypeVideos, "video":
		basePath = c.basePaths[FileTypeVideo]
		urlPrefix = c.urlPrefixes[FileTypeVideo]
		normalizedDirType = DirTypeVideos
	default:
		// 兼容旧的方式，直接使用fileType作为键
		if path, ok := c.basePaths[fileType]; ok {
			basePath = path
			urlPrefix = c.urlPrefixes[fileType]
			normalizedDirType = getDirType(fileType)
		} else {
			return c.BadRequest(ctx, "无效的文件类型: "+fileType, nil)
		}
	}

	// 处理parentPath，移除所有可能的目录类型前缀
	processedPath := parentPath
	for _, prefix := range []string{fileType, normalizedDirType, "image", "video", "videos", "static", "pictures", DirTypeStatic, DirTypePictures, DirTypeVideos} {
		if strings.HasPrefix(processedPath, prefix+"/") {
			processedPath = strings.TrimPrefix(processedPath, prefix+"/")
			break
		}
	}

	// 进一步检查处理后的路径中是否还有目录类型前缀
	for _, dirPrefix := range []string{DirTypeStatic, DirTypePictures, DirTypeVideos} {
		if strings.HasPrefix(processedPath, dirPrefix+"/") {
			processedPath = strings.TrimPrefix(processedPath, dirPrefix+"/")
		}
	}

	// 检查处理后的路径是否为空，如果为空说明是在根目录操作
	if processedPath == "" {
		return c.BadRequest(ctx, "不允许在根目录下直接创建目录，请先创建一个子目录", nil)
	}

	// 构建完整路径
	fullPath := filepath.Join(basePath, processedPath, dirName)
	fmt.Printf("创建目录: %s\n", fullPath)

	// 检查目录是否已存在
	if _, err := os.Stat(fullPath); err == nil {
		return c.BadRequest(ctx, "目录已存在", nil)
	}

	// 创建目录
	if err := os.MkdirAll(fullPath, 0755); err != nil {
		return c.InternalServerError(ctx, "创建目录失败: "+err.Error())
	}

	// 构建完整URL路径（不含目录类型前缀，用于访问）
	relDirPath := filepath.ToSlash(filepath.Join(processedPath, dirName))
	dirURL := fmt.Sprintf("%s/%s", urlPrefix, relDirPath)

	// 构建虚拟路径（带有目录类型前缀，用于前端显示和后续操作）
	virtualPath := filepath.ToSlash(filepath.Join(normalizedDirType, processedPath, dirName))
	fmt.Printf("目录创建成功: 虚拟路径=%s, URL=%s\n", virtualPath, dirURL)

	// 获取当前时间作为创建和修改时间
	now := time.Now()

	// 构建响应
	dirItem := FileItem{
		Name:       dirName,
		URL:        dirURL,
		Path:       virtualPath,
		Size:       0,
		Type:       normalizedDirType,
		Extension:  "",
		IsDir:      true,
		ModifiedAt: now,
		CreatedAt:  now,
	}

	return c.Success(ctx, dirItem)
}

// FileInfo 获取单个文件信息
func (c *FileController) FileInfo(ctx *fiber.Ctx) error {
	// 获取当前请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 获取参数
	fileType := reqInfo.Get("file_type").GetString()
	filePath := reqInfo.Get("path").GetString()

	// 安全检查
	if strings.Contains(filePath, "..") {
		return c.BadRequest(ctx, "无效的路径参数", nil)
	}

	// 获取基础路径
	basePath, ok := c.basePaths[fileType]
	if !ok {
		return c.BadRequest(ctx, "无效的文件类型", nil)
	}

	// 构建完整路径
	fullPath := filepath.Join(basePath, filePath)

	// 获取文件信息
	fileInfo, err := os.Stat(fullPath)
	if os.IsNotExist(err) {
		return c.NotFound(ctx, "文件不存在")
	}
	if err != nil {
		return c.InternalServerError(ctx, "获取文件信息失败: "+err.Error())
	}

	// 获取URL前缀
	urlPrefix := c.urlPrefixes[fileType]

	// 获取文件扩展名
	ext := filepath.Ext(fileInfo.Name())
	if ext != "" {
		ext = ext[1:] // 移除前导'.'
	}

	// 构建完整URL
	fileURL := fmt.Sprintf("%s/%s", urlPrefix, filePath)

	// 构建响应
	fileItem := FileItem{
		Name:       fileInfo.Name(),
		URL:        fileURL,
		Path:       filePath,
		Size:       fileInfo.Size(),
		Type:       getDirType(fileType),
		Extension:  ext,
		IsDir:      fileInfo.IsDir(),
		ModifiedAt: fileInfo.ModTime(),
		CreatedAt:  fileInfo.ModTime(),
	}

	return c.Success(ctx, fileItem)
}

// 辅助函数: 判断一个目录是否为空
func isDirEmpty(dirPath string) (bool, error) {
	f, err := os.Open(dirPath)
	if err != nil {
		return false, err
	}
	defer f.Close()

	_, err = f.Readdirnames(1) // 尝试读取一个条目
	if err == nil {
		// 没有错误表示读取到了至少一个条目，目录不空
		return false, nil
	}
	if errors.Is(err, io.EOF) {
		// EOF表示没有更多条目，目录为空
		return true, nil
	}
	// 其他错误
	return false, err
}

// 辅助函数: 根据目录类型获取类型标识
func getDirType(dirType string) string {
	switch dirType {
	case FileTypeSmallImage:
		return "static"
	case FileTypeImage:
		return "pictures"
	case FileTypeVideo:
		return "videos"
	default:
		return dirType
	}
}

// 辅助函数: 根据扩展名获取文件类型
func getFileType(extension string) string {
	ext := strings.ToLower(strings.TrimPrefix(extension, "."))
	if isImageExt(ext) {
		return "image"
	}
	if isVideoExt(ext) {
		return "video"
	}
	return "other"
}

// 辅助函数: 判断是否为图片扩展名
func isImageExt(ext string) bool {
	imageExts := map[string]bool{
		"jpg":  true,
		"jpeg": true,
		"png":  true,
		"gif":  true,
		"webp": true,
		"bmp":  true,
		"svg":  true,
	}
	return imageExts[ext]
}

// 辅助函数: 判断是否为视频扩展名
func isVideoExt(ext string) bool {
	videoExts := map[string]bool{
		"mp4":  true,
		"avi":  true,
		"mov":  true,
		"wmv":  true,
		"flv":  true,
		"mkv":  true,
		"webm": true,
		"m4v":  true,
	}
	return videoExts[ext]
}
