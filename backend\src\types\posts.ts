// 帖子项目类型
export interface PostItem {
    id: string;
    title: string;
    content: string;
    cover_url?: string;
    images?: string[];
    video?: string;
    status: number;
    author_id: string;
    user_id: string;
    user_name?: string;
    user_avatar?: string;
    category_id?: string;
    category_name?: string;
    tags?: string[];
    view_count: number;
    like_count: number;
    comment_count: number;
    share_count: number;
    created_at: string;
    updated_at: string;
    reject_reason?: string;
    audit_time?: string;
    auditor_id?: number;
    author_type?: number;
}

export interface PostComment {
    id: string; //评论ID
    content: string; //评论内容
    user_id: string; //作者ID
    user_nickname: string; //用户昵称
    user_avatar: string; //用户头像
    reply_to_id: string; //回复用户ID
    reply_to_user: string; //回复的用户名字
    user_type: number; //1普通用户，2明星
    parent_id: string; //父评论ID
    video_id: string; //关联实体ID
    heat: number; //热度
    reply_count: number; //回复数
    like_count: number; //点赞数
    created_at: string; //创建时间
    updated_at: string; //更新时间
    audit_time: string; //审核时间
    auditor_id: number; //审核人ID
    reject_reason: string; //拒绝原因
    status: number; //0=草稿, 1=待审核, 2=审核通过, -2=审核拒绝, -4=已删除
}

// 帖子列表响应类型
export interface PostListResponse {
    list: PostItem[];
    total: number;
    page: number;
    pageSize: number;
}

// 帖子查询参数类型
export interface PostQuery {
    title?: string;
    content?: string;
    user_id?: string;
    category_id?: string;
    status?: number;
    created_at_start?: string;
    created_at_end?: string;
    [key: string]: any; // 允许添加其他字段
}

// 帖子查询请求参数
export interface PostParams {
    page: {
        pageNo: number;
        pageSize: number;
    };
    data?: PostQuery;
}

// 创建帖子请求类型
export interface CreatePostRequest {
    title: string;
    content: string;
    cover_url?: string;
    images?: string[];
    video?: string;
    category_id?: string;
    tags?: string[];
}

// 更新帖子请求类型
export interface UpdatePostRequest extends Partial<CreatePostRequest> {
    id: string;
}

// 审核帖子请求类型
export interface ReviewPostRequest {
    id: string;
    status: number; // 1=通过, 2=拒绝
    reason?: string;
}

// 批量审核帖子请求类型
export interface BatchReviewPostRequest {
    ids: string[];
    status: number; // 1=通过, 2=拒绝
    reason?: string;
}

// 批量删除帖子请求类型
export interface BatchDeletePostRequest {
    ids: string[];
}

// 分页参数
export interface PageParams {
    pageNo: number;
    pageSize: number;
}

// 评论查询参数
export interface CommentQuery {
    post_id?: string;
    user_id?: string;
    status?: number;
    content?: string;
}

// 评论请求参数
export interface CommentParams {
    page: PageParams;
    data?: CommentQuery;
}

// 评论列表响应
export interface CommentListResponse {
    list: PostComment[];
    total: number;
    page: number;
    pageSize: number;
}
