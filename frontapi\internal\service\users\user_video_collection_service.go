package users

import (
	"time"

	"frontapi/internal/models/users"
	userRepo "frontapi/internal/repository/users"
	videoRepo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

// CreateVideoCollectionRequest 创建视频收藏请求
type CreateVideoCollectionRequest struct {
	UserID          string    `json:"userId" validate:"required"`
	VideoID         *string   `json:"videoId" validate:"required"`
	VideoTitle      string    `json:"videoTitle"`
	VideoCover      string    `json:"videoCover"`
	VideoDuration   int       `json:"videoDuration"`
	CreatorID       *string   `json:"creatorId"`
	CreatorName     string    `json:"creatorName"`
	CreatorAvatar   string    `json:"creatorAvatar"`
	CategoryID      *string   `json:"categoryId"`
	CategoryName    string    `json:"categoryName"`
	CollectionTime  time.Time `json:"collectionTime"`
	CollectionGroup string    `json:"collectionGroup"`
	Note            string    `json:"note"`
}

// UserVideoCollectionService 用户视频收藏服务接口
type UserVideoCollectionService interface {
	base.IExtendedService[users.UserVideoCollection]
}

// userVideoCollectionService 用户视频收藏服务实现
type userVideoCollectionService struct {
	*base.ExtendedService[users.UserVideoCollection]
	collectionRepo userRepo.UserVideoCollectionRepository
	videoRepo      videoRepo.VideoRepository
}

// NewUserVideoCollectionService 创建用户视频收藏服务实例
func NewUserVideoCollectionService(
	collectionRepo userRepo.UserVideoCollectionRepository,
	videoRepo videoRepo.VideoRepository,
) UserVideoCollectionService {
	return &userVideoCollectionService{
		ExtendedService: base.NewExtendedService[users.UserVideoCollection](collectionRepo, "user_video_collection"),
		collectionRepo:  collectionRepo,
		videoRepo:       videoRepo,
	}
}
