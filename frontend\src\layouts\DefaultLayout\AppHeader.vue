<template>
    <header :class="{ 'scrolled': isScrolled,'app-header': true }">
        <div class="header-container">        
            <!-- Logo区域 -->
            
            <div class="logo-section">
                <router-link to="/" class="logo-link">
                    <div class="logo-icon-wrapper">
                        <img src="@/assets/icons/logo.svg" alt="logo" class="logo-icon">
                    </div>
                    <span class="logo-text">
                        {{ $t('common.siteName') }}
                    </span>
                </router-link>
                <div class="toggle-menu-drawer" v-if="isScrolled" @click="handleToggleDrawer">
                    <i class="pi pi-bars"></i>
                </div>
            </div>
          
            <!-- 工具栏 -->
            <div class="toolbar-section">
                <div class="search-wrapper hidden md:flex">
                    <div class="search-container">
                        <IconField>
                            <InputText v-model="searchQuery" :placeholder="$t('common.search')" />
                            <InputIcon class="pi pi-search" />
                        </IconField>
                    </div>
                </div>
                <!-- 工具按钮组 -->
                <div class="tool-buttons">
                    <!-- 主题选择器 -->
                    <NavThemeSelector class="tool-selector" />
                    <!-- 语言选择器 -->
                    <NavLanguageSelector class="tool-selector" />
                    <!-- 用户菜单 -->
                    <button
                        class="tool-btn user-btn"
                        @click="toggleUserMenu"
                        aria-label="User menu"
                    >
                        <i class="pi pi-user"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>
</template>

<script setup lang="ts">
import { useTranslation } from "@/core/plugins/i18n/composables";
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import InputText from 'primevue/inputtext';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({

});

const emit = defineEmits([
    'toggledDrawer',
]);

// i18n
const { t } = useTranslation();
const router = useRouter();

// 响应式数据
const searchQuery = ref('');
const isScrolled = ref(false);
const showMobileSearch = ref(false);

// 移动端菜单控制
const handleToggleDrawer = () => {
    emit('toggledDrawer',"left");
};

// 用户菜单控制
const toggleUserMenu = () => {
    // 实现用户菜单逻辑
    console.log('User menu toggled');
};

// 搜索功能
const performSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        showMobileSearch.value = false;
    }
};

// 聚焦搜索框
const focusSearch = () => {
    const searchInput = document.querySelector('.search-container input') as HTMLInputElement;
    searchInput?.focus();
};
const handleScroll = () => {
    isScrolled.value = window.scrollY > 147;
};



onMounted(() => {
    window.addEventListener('scroll', handleScroll);
    handleScroll();
});

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll);
});
// 暴露方法给父组件
defineExpose({
    focusSearch
});
</script>

<style scoped lang="scss">
.app-header {
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1001;
    background:linear-gradient(to bottom,var(--surface-0), var(--surface-50), var(--surface-100));
    border-bottom: 1px solid var(--surface-border);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 6px #0000001a;

    .header-container{
        max-width: 1280px;
        margin: 0 auto;
        padding: 0.2rem 1.5rem;
        height: 4.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 2rem;
        .logo-section{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.6rem;
            .logo-link{
                display: flex;
                align-items: center;
                gap: 0.75rem;
                .logo-icon-wrapper{
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 12px;
                    transition: all 0.3s ease;
                }
            }
            .toggle-menu-drawer{
                color: var(--primary-400);
                font-size: 1.2rem;
                padding: 0.5rem;
                border-radius: 4px;
                width: 2.5rem;
                transition: all 0.3s ease;
                aspect-ratio: 1.5/1;
                text-align: center;
                &:hover{
                    color: var(--primary-500);
                    background-color: white;
                }
                &:active{
                    color: var(--primary-600);
                    background-color: white;
                }
                @media (min-width: 956px) {
                    display: block;
                }
            }
        }
        .toolbar-section{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;

            .search-wrapper{
                .search-container{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    width: 100%;
                   .p-inputtext{
                        width: 20rem;
                        height: 3rem;
                        padding: 0 1rem;
                        border-radius: 50px;
                        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                        margin-right: 0.5rem;
                        &:focus{
                            width: 25rem;
                        }
                   }
                   .pi-search{
                      margin-right: 0.8rem;
                      color: var(--primary-400);
                      border-radius: 50px;
                   }
                }
            }
            .tool-buttons{
                display: flex;
                gap: 0.5rem;
                align-items: center;
                .tool-btn{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 1px solid var(--primary-400);
                    width: 2.5rem;
                    height: 2.5rem;
                    border-radius: 50px;
                    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                    &:hover{
                        background-color: var(--primary-400);
                        color: white;
                    }
                    &:active{
                        background-color: var(--primary-500);
                        color: white;
                    }
                }
            }
        }
    }
}

.sof-dark{
    .app-header {
        background: linear-gradient(135deg, var(--surface-500), var(--surface-600), var(--surface-700));
        border-bottom-color: var(--primary-600);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        color: white;
    }
    .search-container{
        .p-inputtext{
            background-color: var(--surface-100);
            border: 1px solid var(--surface-100);
            color: var(--primary-950);
            &:focus{
                background-color: var(--surface-100);
                border: 1px solid var(--surface-100);
            }
            &:hover{
                background-color: var(--surface-100);
                border: 1px solid var(--surface-100);
            }
        }
    }
}


</style>