<template>
    <div class="search-bar">
        <el-form :inline="true" :model="searchFormData" class="search-form">
            <el-form-item label="关键词">
                <el-input v-model="searchFormData.keyword" placeholder="分类名称/编码" clearable
                    @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchFormData.status" placeholder="请选择状态" clearable style="width: 120px">
                    <el-option label="启用" :value="1" />
                    <el-option label="禁用" :value="0" />
                </el-select>
            </el-form-item>

            <el-form-item label="创建时间">
                <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" value-format="YYYY-MM-DD" :shortcuts="dateRangeShortcuts" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
                <el-button :icon="RefreshRight" @click="handleReset">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { RefreshRight, Search } from '@element-plus/icons-vue';
import { defineEmits, defineProps, reactive, ref, watch } from 'vue';

const props = defineProps({
    modelValue: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update:modelValue', 'search', 'reset', 'refresh']);

// 本地搜索表单数据
const searchFormData = reactive({
    keyword: '',
    status: undefined as number | undefined,
    start_date: '',
    end_date: ''
});

// 日期范围
const dateRange = ref<string[]>([]);

// 日期快捷选项
const dateRangeShortcuts = [
    {
        text: '最近一周',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
        }
    },
    {
        text: '最近一个月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
        }
    },
    {
        text: '最近三个月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end];
        }
    }
];

// 初始化表单数据
watch(() => props.modelValue, (newVal) => {
    if (newVal) {
        searchFormData.keyword = newVal.keyword || '';
        searchFormData.status = newVal.status;

        // 设置日期范围
        if (newVal.start_date && newVal.end_date) {
            dateRange.value = [newVal.start_date, newVal.end_date];
        } else {
            dateRange.value = [];
        }
    }
}, { immediate: true, deep: true });

// 监听日期范围变化
watch(dateRange, (newRange) => {
    if (newRange && newRange.length === 2) {
        searchFormData.start_date = newRange[0];
        searchFormData.end_date = newRange[1];
    } else {
        searchFormData.start_date = '';
        searchFormData.end_date = '';
    }
});

// 搜索处理
const handleSearch = () => {
    // 更新父组件的值
    emit('update:modelValue', { ...searchFormData });
    // 触发搜索事件
    emit('search', { ...searchFormData });
};

// 重置表单
const handleReset = () => {
    searchFormData.keyword = '';
    searchFormData.status = undefined;
    dateRange.value = [];
    searchFormData.start_date = '';
    searchFormData.end_date = '';

    // 更新父组件的值
    emit('update:modelValue', { ...searchFormData });
    // 触发重置事件
    emit('reset');
};
</script>

<style scoped lang="scss">
.search-bar {
    margin-bottom: 16px;
}

.search-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
    }

    :deep(.el-form-item) {
        margin-right: 0 !important;
        margin-bottom: 12px;
        width: 100%;
    }

    :deep(.el-form-item__content) {
        width: 100%;
    }

    :deep(.el-input),
    :deep(.el-select),
    :deep(.el-date-editor) {
        width: 100% !important;
    }
}
</style>