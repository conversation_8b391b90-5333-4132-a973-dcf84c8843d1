# SlinkyTable组件函数命名冲突修复

## 问题描述

在`backend/src/components/themes/slinky/tables/SlinkyTable.vue`中出现了重复的函数声明错误：

```
[vue/compiler-sfc] Identifier 'handleCurrentChange' has already been declared. (374:6)
```

## 问题原因

SlinkyTable.vue中有两个名为`handleCurrentChange`的函数：

1. **第374行左右** - 处理表格的`current-change`事件（当前行变化）
   ```typescript
   const handleCurrentChange = (currentRow: any, oldCurrentRow: any) => {
     emit('current-change', currentRow, oldCurrentRow)
   }
   ```

2. **第605行左右** - 处理分页器的`current-change`事件（页码变化）
   ```typescript
   const handleCurrentChange = (page: number) => {
     currentPage.value = page
     emit('page-change', page)
   }
   ```

两个函数有不同的用途但使用了相同的名称，导致编译错误。

## 修复方案

将分页器的`handleCurrentChange`函数重命名为`handlePageCurrentChange`：

### 1. 修改函数定义

```typescript
// 修改前
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page)
}

// 修改后
const handlePageCurrentChange = (page: number) => {
  currentPage.value = page
  emit('page-change', page)
}
```

### 2. 修改模板中的调用

```vue
<!-- 修改前 -->
<el-pagination
  @current-change="handleCurrentChange"
  @prev-click="handlePrevClick"
  @next-click="handleNextClick"
/>

<!-- 修改后 -->
<el-pagination
  @current-change="handlePageCurrentChange"
  @prev-click="handlePrevClick"
  @next-click="handleNextClick"
/>
```

## 修复后的函数结构

修复后，两个函数分别处理不同的事件：

1. **handleCurrentChange** - 表格行选择变化
   - 参数：`(currentRow: any, oldCurrentRow: any)`
   - 触发事件：`emit('current-change', currentRow, oldCurrentRow)`
   - 使用位置：`<el-table @current-change="handleCurrentChange">`

2. **handlePageCurrentChange** - 分页器页码变化
   - 参数：`(page: number)`
   - 触发事件：`emit('page-change', page)`
   - 使用位置：`<el-pagination @current-change="handlePageCurrentChange">`

## 验证

修复后编译错误消失，函数命名清晰明确，各自处理对应的事件类型。

## 相关文件

- `backend/src/components/themes/slinky/tables/SlinkyTable.vue`

## 日期

2024年11月27日

## 结果

✅ 函数名冲突已解决
✅ 代码编译通过
✅ 事件处理逻辑正确 