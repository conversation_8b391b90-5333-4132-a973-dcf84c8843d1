package container

import (
	bookSrv "frontapi/internal/service/books"
	comicServices "frontapi/internal/service/comics"
	contentService "frontapi/internal/service/content_creator"
	homeService "frontapi/internal/service/home"
	integralSvc "frontapi/internal/service/integral"
	permSrv "frontapi/internal/service/permission"
	pictureServices "frontapi/internal/service/pictures"
	postServices "frontapi/internal/service/posts"
	promotionSrv "frontapi/internal/service/promotion"
	shortvideoServices "frontapi/internal/service/shortvideos"
	sysDatabaseService "frontapi/internal/service/sys"
	sysService "frontapi/internal/service/system"
	userSrv "frontapi/internal/service/users"
	videoService "frontapi/internal/service/videos"
	vipService "frontapi/internal/service/vips"
	walletSrv "frontapi/internal/service/wallets"

	"gorm.io/gorm"
)

// ServiceContainer 服务容器结构体
type ServiceContainer struct {
	// 缓存管理器
	CacheManager interface{}

	// 视频相关服务
	VideoService         videoService.VideoService
	VideoCategoryService videoService.VideoCategoryService
	VideoCommentService  videoService.VideoCommentService
	VideoChannelService  videoService.VideoChannelService
	VideoAlbumService    videoService.VideoAlbumService
	VideoLikeService     videoService.VideoLikeService

	// 短视频相关服务
	ShortVideoService         shortvideoServices.ShortVideoService
	ShortVideoCategoryService shortvideoServices.ShortVideoCategoryService
	ShortVideoCommentService  shortvideoServices.ShortVideoCommentService

	// 帖子相关服务
	PostService        postServices.PostService
	PostCommentService postServices.PostCommentService

	// 图片相关服务
	PictureService           pictureServices.PictureService
	PictureCategoryService   pictureServices.PictureCategoryService
	PictureAlbumService      pictureServices.PictureAlbumService
	PictureCollectionService pictureServices.PictureCollectionService

	// 漫画相关服务
	ComicService            comicServices.ComicService
	ComicCategoryService    comicServices.ComicCategoryService
	ComicPageService        comicServices.ComicPageService
	ComicChapterService     comicServices.ComicChapterService
	ComicFavoriteService    comicServices.ComicFavoriteService
	ComicCommentService     comicServices.ComicCommentService
	ComicReadHistoryService comicServices.ComicReadHistoryService

	// 内容创作相关服务
	ContentAuditService   contentService.ContentAuditService
	ContentHeatService    contentService.ContentHeatService
	ContentRatingService  contentService.ContentRatingService
	ContentRevenueService contentService.ContentRevenueService

	// 内容创作积分规则相关服务
	RevenueRuleService contentService.RevenueRuleService

	// 平台币充值套餐相关服务
	CoinPackageService vipService.CoinPackageService

	// 国家/地区相关服务
	CountryService sysService.CountryService

	// 首页相关服务
	HomeService       homeService.HomeService
	HotKeywordService homeService.HotKeywordService

	// 积分相关服务
	PointService                integralSvc.PointService
	PointExchangeService        integralSvc.PointExchangeService
	PointLevelService           integralSvc.PointLevelService
	PointRuleService            integralSvc.PointRuleService
	PromotionCampaignService    promotionSrv.PromotionCampaignService
	PromotionRecordService      promotionSrv.PromotionRecordService
	InvitationCommissionService promotionSrv.InvitationCommissionService
	InvitationRuleService       promotionSrv.InvitationRuleService

	//钱包和提现服务
	WalletService          walletSrv.WalletService
	WithdrawRequestService walletSrv.WithdrawRequestService

	//权限相关（简化后）
	AdminUserService permSrv.AdminUserService
	AdminMenuService permSrv.AdminMenuService
	CasbinService    permSrv.CasbinService

	//用户相关
	UserService                  userSrv.UserService
	UserLoginLogsService         userSrv.UserLoginLogsService
	UserFollowsService           userSrv.UserFollowsService
	UserWatchVideoHistoryService userSrv.UserWatchVideoHistoryService
	UserVideoCollectionService   userSrv.UserVideoCollectionService
	// 标签相关服务
	TagService sysService.TagService

	// 电子书相关服务
	BookService         bookSrv.BookService
	BookCategoryService bookSrv.BookCategoryService
	BookChapterService  bookSrv.BookChapterService
	BookFavoriteService bookSrv.BookFavoriteService
	BookmarkService     bookSrv.BookmarkService
	ReadHistoryService  bookSrv.BookReadHistoryService

	// 数据库相关服务
	DatabaseService sysDatabaseService.DatabaseService

	// 扩展服务
	LikeService    interface{} // 将在后面类型断言为 extlike.LikeService
	CollectService interface{} // 将在后面类型断言为 extcollect.CollectService
}

// ServiceBuilder 服务构建器
type ServiceBuilder struct {
	db       *gorm.DB
	services *ServiceContainer
}

// NewServiceBuilder 创建服务构建器
func NewServiceBuilder(db *gorm.DB) *ServiceBuilder {
	return &ServiceBuilder{
		db:       db,
		services: &ServiceContainer{},
	}
}

// DB 获取数据库连接
func (b *ServiceBuilder) DB() *gorm.DB {
	return b.db
}

// Services 获取服务容器
func (b *ServiceBuilder) Services() *ServiceContainer {
	return b.services
}

// Build 构建并返回服务容器
func (b *ServiceBuilder) Build() *ServiceContainer {
	return b.services
}

// InitServices 初始化所有服务
func InitServices(db *gorm.DB) *ServiceContainer {
	builder := NewServiceBuilder(db)

	// 初始化各模块服务
	InitExtendedServices(builder) // 先初始化扩展服务
	InitVideoServices(builder)
	InitShortVideoServices(builder)
	InitBookServices(builder)
	InitContentCreatorServices(builder)
	InitContentCreatorRevenueServices(builder)
	InitPostServices(builder)
	InitPictureServices(builder)
	InitComicServices(builder)
	InitSystemServices(builder)
	InitHomeServices(builder)
	InitPointServices(builder)
	InitPromotionServices(builder)
	InitWalletServices(builder)
	InitPermissionServices(builder)

	InitUserServices(builder)
	InitTagServices(builder)
	InitDatabaseServices(builder)

	return builder.Build()
}
