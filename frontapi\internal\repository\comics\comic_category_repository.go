package comics

import (
	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ComicCategoryRepository 漫画分类数据访问接口
type ComicCategoryRepository interface {
	base.ExtendedRepository[comics.ComicCategory]
}

// comicCategoryRepository 漫画分类数据访问实现
type comicCategoryRepository struct {
	base.ExtendedRepository[comics.ComicCategory]
}

// NewComicCategoryRepository 创建漫画分类仓库实例
func NewComicCategoryRepository(db *gorm.DB) ComicCategoryRepository {
	return &comicCategoryRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.ComicCategory](db),
	}
}
