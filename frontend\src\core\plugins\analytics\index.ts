/**
 * 分析插件 - 提供用户行为分析和数据统计功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'

// 扩展Window接口
declare global {
  interface Window {
    dataLayer?: any[]
    _hmt?: any[]
    gtag?: (...args: any[]) => void
  }
}

/**
 * 分析事件类型
 */
export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: number
  userId?: string
  sessionId?: string
}

/**
 * 分析配置
 */
export interface AnalyticsConfig {
  /** 是否启用分析 */
  enabled: boolean
  /** 分析服务提供商 */
  provider: 'google' | 'baidu' | 'custom'
  /** 跟踪ID */
  trackingId?: string
  /** 是否启用调试模式 */
  debug: boolean
  /** 是否启用自动页面跟踪 */
  autoPageTracking: boolean
  /** 是否启用自动事件跟踪 */
  autoEventTracking: boolean
  /** 采样率 */
  sampleRate: number
  /** 自定义配置 */
  customConfig?: Record<string, any>
}

/**
 * 分析管理器类
 */
export class AnalyticsManager {
  private config: AnalyticsConfig
  private isInitialized = false
  private eventQueue: AnalyticsEvent[] = []
  private sessionId: string
  private userId?: string

  constructor(config: AnalyticsConfig) {
    this.config = config
    this.sessionId = this.generateSessionId()
    this.init()
  }

  /**
   * 初始化分析管理器
   */
  private async init() {
    if (!this.config.enabled) {
      console.log('Analytics disabled')
      return
    }

    try {
      await this.initProvider()
      this.isInitialized = true
      
      // 处理队列中的事件
      this.flushEventQueue()
      
      // 启用自动跟踪
      if (this.config.autoPageTracking) {
        this.enableAutoPageTracking()
      }
      
      if (this.config.autoEventTracking) {
        this.enableAutoEventTracking()
      }
      
      console.log('Analytics initialized successfully')
    } catch (error) {
      console.error('Failed to initialize analytics:', error)
    }
  }

  /**
   * 初始化分析服务提供商
   */
  private async initProvider() {
    switch (this.config.provider) {
      // case 'google':
      //   await this.initGoogleAnalytics()
      //   break
      // case 'baidu':
      //   await this.initBaiduAnalytics()
        // break
      case 'custom':
        await this.initCustomAnalytics()
        break
      default:
        throw new Error(`Unsupported analytics provider: ${this.config.provider}`)
    }
  }

  // /**
  //  * 初始化Google Analytics
  //  */
  // private async initGoogleAnalytics() {
  //   if (!this.config.trackingId) {
  //     throw new Error('Google Analytics tracking ID is required')
  //   }

  //   // 加载gtag脚本
  //   const script = document.createElement('script')
  //   script.async = true
  //   script.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.trackingId}`
  //   document.head.appendChild(script)

  //   // 初始化gtag
  //   window.dataLayer = window.dataLayer || []
  //   function gtag(...args: any[]) {
  //     window.dataLayer.push(args)
  //   }
    
  //   gtag('js', new Date())
  //   gtag('config', this.config.trackingId, {
  //     debug_mode: this.config.debug,
  //     sample_rate: this.config.sampleRate * 100,
  //     ...this.config.customConfig
  //   })

  //   // 将gtag添加到全局
  //   ;(window as any).gtag = gtag
  // }

  // /**
  //  * 初始化百度统计
  //  */
  // private async initBaiduAnalytics() {
  //   if (!this.config.trackingId) {
  //     throw new Error('Baidu Analytics tracking ID is required')
  //   }

  //   // 加载百度统计脚本
  //   const script = document.createElement('script')
  //   script.async = true
  //   script.src = `https://hm.baidu.com/hm.js?${this.config.trackingId}`
  //   document.head.appendChild(script)

  //   // 初始化_hmt
  //   window._hmt = window._hmt || []
  // }

  /**
   * 初始化自定义分析
   */
  private async initCustomAnalytics() {
    // 自定义分析实现
    console.log('Custom analytics initialized')
  }

  /**
   * 跟踪事件
   */
  track(event: AnalyticsEvent) {
    const enrichedEvent = {
      ...event,
      timestamp: event.timestamp || Date.now(),
      sessionId: this.sessionId,
      userId: this.userId || event.userId
    }

    if (!this.isInitialized) {
      this.eventQueue.push(enrichedEvent)
      return
    }

    this.sendEvent(enrichedEvent)
  }

  /**
   * 跟踪页面浏览
   */
  trackPageView(page: string, title?: string) {
    this.track({
      name: 'page_view',
      properties: {
        page,
        title: title || document.title,
        referrer: document.referrer,
        url: window.location.href
      }
    })
  }

  /**
   * 设置用户ID
   */
  setUserId(userId: string) {
    this.userId = userId
    
    if (this.config.provider === 'google' && (window as any).gtag) {
      ;(window as any).gtag('config', this.config.trackingId, {
        user_id: userId
      })
    }
  }

  /**
   * 设置用户属性
   */
  setUserProperties(properties: Record<string, any>) {
    if (this.config.provider === 'google' && (window as any).gtag) {
      ;(window as any).gtag('config', this.config.trackingId, {
        custom_map: properties
      })
    }
  }

  /**
   * 发送事件
   */
  private sendEvent(event: AnalyticsEvent) {
    try {
      switch (this.config.provider) {
        case 'google':
          this.sendGoogleEvent(event)
          break
        case 'baidu':
          this.sendBaiduEvent(event)
          break
        case 'custom':
          this.sendCustomEvent(event)
          break
      }
      
      if (this.config.debug) {
        console.log('Analytics event sent:', event)
      }
    } catch (error) {
      console.error('Failed to send analytics event:', error)
    }
  }

  /**
   * 发送Google Analytics事件
   */
  private sendGoogleEvent(event: AnalyticsEvent) {
    if ((window as any).gtag) {
      ;(window as any).gtag('event', event.name, {
        ...event.properties,
        event_category: event.properties?.category || 'general',
        event_label: event.properties?.label,
        value: event.properties?.value
      })
    }
  }

  /**
   * 发送百度统计事件
   */
  private sendBaiduEvent(event: AnalyticsEvent) {
    if (window._hmt) {
      window._hmt.push(['_trackEvent', 
        event.properties?.category || 'general',
        event.name,
        event.properties?.label,
        event.properties?.value
      ])
    }
  }

  /**
   * 发送自定义事件
   */
  private sendCustomEvent(event: AnalyticsEvent) {
    // 自定义事件发送实现
    console.log('Custom event sent:', event)
  }

  /**
   * 启用自动页面跟踪
   */
  private enableAutoPageTracking() {
    // 监听路由变化
    window.addEventListener('popstate', () => {
      this.trackPageView(window.location.pathname)
    })

    // 监听pushState和replaceState
    const originalPushState = history.pushState
    const originalReplaceState = history.replaceState

    history.pushState = function(...args) {
      originalPushState.apply(history, args)
      setTimeout(() => {
        analyticsManager.trackPageView(window.location.pathname)
      }, 0)
    }

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args)
      setTimeout(() => {
        analyticsManager.trackPageView(window.location.pathname)
      }, 0)
    }
  }

  /**
   * 启用自动事件跟踪
   */
  private enableAutoEventTracking() {
    // 跟踪点击事件
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (target.dataset.track) {
        this.track({
          name: 'click',
          properties: {
            element: target.tagName.toLowerCase(),
            text: target.textContent?.trim(),
            track: target.dataset.track
          }
        })
      }
    })
  }

  /**
   * 处理事件队列
   */
  private flushEventQueue() {
    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift()
      if (event) {
        this.sendEvent(event)
      }
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 销毁分析管理器
   */
  destroy() {
    this.isInitialized = false
    this.eventQueue = []
  }
}

// 全局分析管理器实例
export const analyticsManager = new AnalyticsManager({
  enabled: true,
  provider: 'custom',
  debug: false,
  autoPageTracking: true,
  autoEventTracking: true,
  sampleRate: 1.0
})

/**
 * 分析插件
 */
export const analyticsPlugin: Plugin = createPlugin({
  meta: {
    name: 'analytics',
    version: '1.0.0',
    description: '用户行为分析和数据统计插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 3
  },
  install(context: PluginContext) {
    // 将分析管理器添加到全局属性
    context.app.config.globalProperties.$analytics = analyticsManager
    
    // 提供分析管理器
    context.app.provide('analytics', analyticsManager)
    
    console.log('Analytics plugin installed')
  },
  uninstall() {
    analyticsManager.destroy()
    console.log('Analytics plugin uninstalled')
  }
})

/**
 * 使用分析的组合式函数
 */
export function useAnalytics() {
  return {
    analytics: analyticsManager,
    track: analyticsManager.track.bind(analyticsManager),
    trackPageView: analyticsManager.trackPageView.bind(analyticsManager),
    setUserId: analyticsManager.setUserId.bind(analyticsManager),
    setUserProperties: analyticsManager.setUserProperties.bind(analyticsManager)
  }
}

export default analyticsPlugin