package routes

import (
	"frontapi/internal/bootstrap"
	"github.com/gofiber/fiber/v2"
)

// RegisterAppRoutes 使用服务容器注册所有前端路由
func RegisterAppRoutes(app *fiber.App, services *bootstrap.ServiceContainer) {
	// 创建视频API控制器
	//videoController := apiVideos.NewVideoController(
	//	services.VideoService,
	//	services.VideoCategoryService,
	//	services.VideoCommentService,
	//)
	//
	//// 创建短视频API控制器
	//shortVideoController := shortvideos.NewShortVideoController(
	//	services.ShortVideoService,
	//	services.ShortVideoCategoryService,
	//	services.ShortVideoCommentService,
	//)
	//
	//// 创建帖子API控制器
	//postController := posts.NewPostController()
	//
	//// 创建图片API控制器
	//pictureController := pictures.NewPictureController(
	//	services.PictureService,
	//	services.PictureCategoryService,
	//	services.PictureAlbumService,
	//	services.PictureCollectionService,
	//)
	//
	//// 创建漫画API控制器
	//comicController := comics.NewComicController(
	//	services.ComicService,
	//	services.ComicChapterService,
	//	services.ComicFavoriteService,
	//	services.ComicCommentService,
	//	services.ComicReadHistoryService,
	//)
	//
	//// 创建内容创作相关控制器
	////auditApi := content_creator.NewContentAuditApi(services.ContentAuditService)
	////heatApi := content_creator.NewContentHeatApi(services.ContentHeatService)
	////ratingApi := content_creator.NewContentRatingApi(services.ContentRatingService)
	////revenueApi := content_creator.NewContentRevenueApi(services.ContentRevenueService)
	////revenueRuleApi := content_creator.NewRevenueRuleApi(services.RevenueRuleService)
	//
	//// 平台币充值套餐API控制器
	//coinPackageApi := vips.NewCoinPackageApi(services.CoinPackageService)
	//
	//// 热搜关键词API控制器
	////hotKeywordApi := home.NewHotKeywordApi(services.HotKeywordService)
	//
	//// 钱包和提现控制器
	//walletController := wallets.NewWalletController(services.WalletService)
	//withdrawController := wallets.NewWithdrawRequestController(services.WithdrawRequestService)

	//// 注册视频相关路由
	//api.RegisterVideoRoutes(app, videoController)
	//
	//// 注册短视频相关路由
	//api.RegisterShortVideoRoutes(app, shortVideoController)
	//
	//// 注册帖子相关路由
	//api.RegisterPostRoutes(app, postController)
	//
	//// 注册图片相关路由
	//api.RegisterPictureRoutes(app, pictureController)
	//
	//// 注册漫画相关路由
	//api.RegisterComicRoutes(app, comicController)
	//
	//// 注册内容创作相关路由
	//api.RegisterContentCreatorRoutes(app, auditApi, heatApi, ratingApi, revenueApi, revenueRuleApi)
	//
	//// 平台币充值套餐路由
	//api.RegisterCoinPackageRoutes(app, coinPackageApi)
	//
	//// 热搜关键词路由
	//api.RegisterHotKeywordRoutes(app, hotKeywordApi)
	//
	//// 注册积分相关路由
	//api.RegisterIntegralRoutes(app,
	//	services.PointService,
	//	services.PointExchangeService,
	//	services.PointLevelService,
	//	services.PointRuleService,
	//)
	//
	//// 注册推广相关路由
	//api.RegisterPromotionRoutes(app,
	//	services.PromotionCampaignService,
	//	services.PromotionRecordService,
	//	services.InvitationCommissionService,
	//	services.InvitationRuleService,
	//)
	//
	//// 注册钱包和提现路由
	//api.RegisterWalletRoutes(app, walletController, withdrawController)

	// 注册基础API路由
	//RegisterAPIRoutesWithContainer(app, services)
}

// RegisterRoutesWithContainer 使用服务容器注册所有路由
// 此函数保留是为了向后兼容
func RegisterRoutesWithContainer(app *fiber.App, services *bootstrap.ServiceContainer) {
	RegisterAppRoutes(app, services)
}
