# 国际化(i18n)架构文档

## 架构概述

我们的国际化系统采用了基于Vue I18n的模块化架构，主要由以下几部分组成：

1. **核心插件模块** (`/src/core/plugins/i18n/`)：
   - 提供了底层的国际化功能
   - 实现了语言加载、切换和动态更新机制
   - 提供了组合式API (Composables)

2. **语言资源模块** (`/src/locales/`)：
   - 集中管理所有语言的翻译文本
   - 按语言代码和功能模块组织
   - 使用TypeScript保证类型安全

3. **配置模块** (`/src/config/locales.config.ts`)：
   - 配置支持的语言列表
   - 定义语言匹配规则
   - 设置默认语言和存储选项

4. **UI组件** (`/src/shared/components/LanguageSelector/`)：
   - 提供了多种预设的语言选择器组件
   - 支持导航栏和页脚等不同场景

## 支持的语言

当前支持的语言包括：

- English (en)
- 简体中文 (zh-CN)
- 繁體中文 (zh-TW)
- 한국어 (ko)

## 使用方法

### 基本使用

在Vue组件中使用翻译：

```vue
<template>
  <div>
    <h1>{{ t('common.welcome') }}</h1>
    <p>{{ t('common.description') }}</p>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
</script>
```

### 使用增强的组合式API

我们提供了增强版的组合式API，包含更多功能：

```vue
<script setup lang="ts">
import { useI18nPlugin, useTranslation } from '@/core/plugins/i18n';

// 完整功能版本
const {
  t,
  locale,
  currentLocaleName,
  currentLocaleFlag,
  isRTL,
  changeLocale,
  availableLocales,
  allLocales
} = useI18nPlugin();

// 简化版本
const { t, localeName, changeLocale, locales } = useTranslation();
</script>
```

### 在组件中切换语言

```vue
<template>
  <button @click="changeLanguage('zh-CN')">切换到中文</button>
</template>

<script setup lang="ts">
import { useI18nPlugin } from '@/core/plugins/i18n';

const { changeLocale } = useI18nPlugin();

const changeLanguage = async (locale) => {
  await changeLocale(locale);
};
</script>
```

### 使用语言选择器组件

在导航栏中使用：

```vue
<template>
  <NavLanguageSelector />
</template>

<script setup lang="ts">
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
</script>
```

在页脚中使用：

```vue
<template>
  <FooterLanguageSelector />
</template>

<script setup lang="ts">
import FooterLanguageSelector from '@/shared/components/LanguageSelector/FooterLanguageSelector.vue';
</script>
```

## 添加新语言

### 1. 更新配置

在 `/src/config/locales.config.ts` 中添加新语言配置：

```typescript
export const SUPPORTED_LOCALES: LocaleInfo[] = [
  // 现有语言...
  {
    code: 'ja',
    shortCode: 'JA',
    nativeName: '日本語',
    englishName: 'Japanese',
    flag: '🇯🇵',
    enabled: true,
    browserMatches: ['ja', 'ja-jp']
  }
];
```

### 2. 创建语言文件

在 `/src/locales/` 下创建对应的语言目录和文件：

```
/src/locales/ja/
  |- index.ts     // 主入口
  |- common.ts    // 通用翻译
  |- user.ts      // 用户相关
  |- theme.ts     // 主题相关
  |- ...
```

### 3. 更新主入口文件

在 `/src/locales/index.ts` 中导入新的语言包：

```typescript
import * as jaMessages from './ja/index';

const messagesMap: Record<string, LocaleMessages> = {
  // 现有语言...
  'ja': jaMessages.default || {},
};
```

## 架构优势

1. **高性能**：预加载语言包，减少动态加载延迟
2. **类型安全**：全面使用TypeScript，提供类型检查
3. **模块化**：清晰的职责分离，易于维护和扩展
4. **用户体验**：语言切换时无需刷新页面
5. **自动检测**：支持基于浏览器设置自动选择最佳语言
6. **持久化**：记住用户的语言偏好

## 常见问题

### 语言切换后UI未更新

如果遇到语言切换后界面未正确更新，可能需要强制刷新组件：

```vue
<script setup lang="ts">
import { useI18nPlugin } from '@/core/plugins/i18n';

const { forceRefresh } = useI18nPlugin();

// 强制刷新当前组件
forceRefresh();
</script>
```

### 添加新的翻译键

添加新的翻译键时，建议先在默认语言文件中添加，然后复制到其他语言文件中进行翻译。

### 最佳实践

1. 使用嵌套结构组织翻译键，如 `user.profile.title`
2. 避免硬编码文本，始终使用翻译函数
3. 对于复杂的翻译，使用参数和插值功能
4. 对于日期、数字和货币，使用格式化函数

## 性能优化

当前架构已经针对性能进行了多项优化：

1. 采用静态导入替代动态导入，减少网络请求
2. 使用缓存机制避免重复加载
3. 实现智能局部更新，避免整页刷新
4. 采用事件通知系统，确保组件及时响应语言变化

## 未来规划

未来可能的改进方向：

1. 支持更多语言
2. 实现语言包的懒加载机制
3. 添加自动翻译建议功能
4. 实现在线编辑翻译的管理界面
5. 引入翻译缺失检测工具 