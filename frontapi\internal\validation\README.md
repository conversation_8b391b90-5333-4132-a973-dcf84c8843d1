# 验证器模块 (Validator)

本目录包含项目中用于请求数据验证的模型和结构体定义。这些验证模型基于 `github.com/gookit/validate` 实现，用于确保API请求数据的完整性和有效性。

## 目录结构

本目录包含各个模块的验证模型:

- `user.go`: 用户模块相关验证模型
- `video.go`: 视频模块相关验证模型
- `wallet.go`: 钱包模块相关验证模型
- `integral.go`: 积分模块相关验证模型
- `content_creator.go`: 内容创作者模块相关验证模型
- `comic.go`: 漫画模块相关验证模型
- `book.go`: 书籍模块相关验证模型
- `post.go`: 帖子模块相关验证模型
- `picture.go`: 图片模块相关验证模型

## 使用方法

### 1. 在控制器中使用验证器

```go
import (
    "frontapi/internal/validation"
    "frontapi/internal/validator"
    "github.com/gofiber/fiber/v2"
)

func (h *YourController) CreateSomething(c *fiber.Ctx) error {
    // 使用验证器验证请求数据
    var req validation.YourCreateRequest
    if err := validator.ValidateRequest(c, &req); err != nil {
        return err
    }
    
    // 数据验证通过，继续处理业务逻辑
    // ...
    
    return h.Success(c, result)
}
```

### 2. 在路由中使用中间件验证

```go
import (
    "frontapi/internal/validation"
    "frontapi/internal/middleware"
    "github.com/gofiber/fiber/v2"
)

func RegisterRoutes(app *fiber.App) {
    // 使用中间件验证请求数据
    app.Post("/your-endpoint", 
        middleware.ValidateRequest(&validation.YourCreateRequest{}),
        yourController.CreateSomething)
}
```

### 3. 添加新的验证模型

当需要为新的API添加验证逻辑时，可以在现有的文件中添加新的结构体，或者为新模块创建新的文件。例如:

```go
// 在相应模块的验证文件中添加
type NewResourceCreateRequest struct {
    Name        string   `json:"name" validate:"required|minLen:2|maxLen:50"`
    Description string   `json:"description" validate:"required|minLen:10|maxLen:500"`
    Type        string   `json:"type" validate:"required|in:type1,type2,type3"`
    Tags        []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
    Status      int      `json:"status" validate:"in:0,1,2"` // 0:草稿 1:发布 2:归档
}
```

## 验证规则示例

### 常用规则

- `required`: 必填字段
- `minLen:3`: 最小长度为3
- `maxLen:50`: 最大长度为50
- `min:1`: 最小值为1
- `max:100`: 最大值为100
- `email`: 电子邮箱格式
- `url`: URL格式
- `in:option1,option2`: 枚举值限制
- `each:xxx`: 应用验证规则到数组的每个元素

### 高级规则

- `requiredIf:field,value`: 当指定字段等于指定值时必填
- `requiredWith:field1,field2`: 当任一指定字段存在时必填
- `requiredWithout:field1,field2`: 当任一指定字段不存在时必填

### 规则组合

可以使用 `|` 符号组合多个验证规则:

```go
`validate:"required|minLen:3|maxLen:20|alpha"`
```

更多验证规则请参考 `github.com/gookit/validate` 文档。 