# 扩展点赞服务配置指南

本文档详细说明如何配置和部署扩展点赞服务 (ExtendedLikeService)。

## 目录

- [基础配置](#基础配置)
- [Redis配置](#redis配置)
- [MongoDB配置](#mongodb配置)
- [存储策略](#存储策略)
- [同步服务配置](#同步服务配置)
- [性能调优](#性能调优)
- [监控配置](#监控配置)
- [部署建议](#部署建议)

## 基础配置

### 服务配置结构

```go
type LikeServiceConfig struct {
    RedisConfig      *v2.LikeConfig      // Redis配置
    MongoConfig      *mongodb.LikeConfig // MongoDB配置
    Strategy         StorageStrategy     // 存储策略
    SyncConfig       *SyncConfig         // 同步配置
    PerformanceConfig *PerformanceConfig  // 性能配置
}
```

### 环境变量配置

```bash
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5

# MongoDB配置
MONGO_URI=mongodb://localhost:27017
MONGO_DATABASE=frontapi
MONGO_TIMEOUT=10s
MONGO_POOL_SIZE=100

# 点赞服务配置
LIKE_SERVICE_STRATEGY=dual_write
LIKE_CACHE_EXPIRATION=3600
LIKE_SYNC_INTERVAL=300
```

## Redis配置

### 基本Redis配置

```go
redisConfig := &v2.LikeConfig{
    RedisClient: redis.NewClient(&redis.Options{
        Addr:         "localhost:6379",
        Password:     "",
        DB:           0,
        PoolSize:     10,
        MinIdleConns: 5,
        DialTimeout:  time.Second * 5,
        ReadTimeout:  time.Second * 3,
        WriteTimeout: time.Second * 3,
        PoolTimeout:  time.Second * 4,
        IdleTimeout:  time.Minute * 5,
    }),
    KeyPrefix:   "like",
    Expiration:  3600, // 1小时
    MaxRetries:  3,
    RetryDelay:  time.Millisecond * 100,
}
```

### Redis集群配置

```go
redisClusterConfig := &v2.LikeConfig{
    RedisClient: redis.NewClusterClient(&redis.ClusterOptions{
        Addrs: []string{
            "localhost:7000",
            "localhost:7001",
            "localhost:7002",
        },
        Password:     "",
        PoolSize:     20,
        MinIdleConns: 10,
        DialTimeout:  time.Second * 5,
        ReadTimeout:  time.Second * 3,
        WriteTimeout: time.Second * 3,
    }),
    KeyPrefix:  "like_cluster",
    Expiration: 7200, // 2小时
}
```

### Redis哨兵配置

```go
redisSentinelConfig := &v2.LikeConfig{
    RedisClient: redis.NewFailoverClient(&redis.FailoverOptions{
        MasterName:    "mymaster",
        SentinelAddrs: []string{"localhost:26379", "localhost:26380", "localhost:26381"},
        Password:      "",
        DB:            0,
        PoolSize:      15,
        MinIdleConns:  8,
    }),
    KeyPrefix:  "like_sentinel",
    Expiration: 3600,
}
```

### Redis键命名规范

```go
// 键前缀配置
const (
    VideoLikePrefix       = "video_like"        // 视频点赞
    ShortVideoLikePrefix  = "shortvideo_like"   // 短视频点赞
    CommentLikePrefix     = "comment_like"      // 评论点赞
    PostLikePrefix        = "post_like"         // 帖子点赞
)

// 键结构示例
// 用户点赞集合: {prefix}:user:{userID}:{itemType}
// 项目点赞数: {prefix}:count:{itemType}:{itemID}
// 项目点赞用户: {prefix}:users:{itemType}:{itemID}
// 热门排行: {prefix}:hot:{itemType}
```

## MongoDB配置

### 基本MongoDB配置

```go
mongoConfig := &mongodb.LikeConfig{
    DB:             db, // GORM数据库实例
    CollectionName: "likes",
    IndexConfig: &mongodb.IndexConfig{
        CreateIndexes: true,
        Indexes: []mongodb.IndexDefinition{
            {
                Keys:    bson.D{{"user_id", 1}, {"item_type", 1}, {"item_id", 1}},
                Options: options.Index().SetUnique(true),
            },
            {
                Keys:    bson.D{{"item_type", 1}, {"item_id", 1}},
                Options: options.Index(),
            },
            {
                Keys:    bson.D{{"created_at", -1}},
                Options: options.Index(),
            },
        },
    },
    TTLConfig: &mongodb.TTLConfig{
        EnableTTL:      true,
        TTLField:       "expires_at",
        TTLDuration:    time.Hour * 24 * 30, // 30天
    },
}
```

### MongoDB连接池配置

```go
// 在GORM配置中设置MongoDB连接池
config := &gorm.Config{
    // 其他配置...
}

// MongoDB连接选项
clientOptions := options.Client().ApplyURI("mongodb://localhost:27017")
clientOptions.SetMaxPoolSize(100)        // 最大连接数
clientOptions.SetMinPoolSize(10)         // 最小连接数
clientOptions.SetMaxConnIdleTime(time.Minute * 30) // 连接空闲时间
clientOptions.SetConnectTimeout(time.Second * 10)  // 连接超时
clientOptions.SetSocketTimeout(time.Second * 30)   // Socket超时
```

### 分片配置

```go
// 按项目类型分片的配置
type ShardConfig struct {
    ShardKey     string            // 分片键
    Collections  map[string]string // 项目类型到集合的映射
}

shardConfig := &ShardConfig{
    ShardKey: "item_type",
    Collections: map[string]string{
        "video":      "video_likes",
        "shortvideo": "shortvideo_likes",
        "comment":    "comment_likes",
        "post":       "post_likes",
    },
}
```

## 存储策略

### 策略类型说明

```go
type StorageStrategy int

const (
    MongoOnly   StorageStrategy = iota // 仅MongoDB
    RedisFirst                         // Redis优先
    MongoFirst                         // MongoDB优先
    DualWrite                          // 双写模式
)
```

### 策略选择指南

| 策略 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| MongoOnly | 数据一致性要求高 | 数据持久化，一致性好 | 性能较低 |
| RedisFirst | 高并发读写 | 性能最高 | 数据可能丢失 |
| MongoFirst | 平衡性能和一致性 | 数据安全，性能适中 | 复杂度较高 |
| DualWrite | 高可用性要求 | 数据冗余，高可用 | 资源消耗大 |

### 策略配置示例

```go
// 高性能场景配置
highPerformanceConfig := &LikeServiceConfig{
    Strategy: RedisFirst,
    RedisConfig: &v2.LikeConfig{
        Expiration: 1800, // 30分钟
        // 其他Redis配置...
    },
    SyncConfig: &SyncConfig{
        SyncInterval: time.Minute * 5, // 5分钟同步一次
        BatchSize:    1000,
    },
}

// 高一致性场景配置
highConsistencyConfig := &LikeServiceConfig{
    Strategy: DualWrite,
    RedisConfig: &v2.LikeConfig{
        Expiration: 7200, // 2小时
        // 其他Redis配置...
    },
    SyncConfig: &SyncConfig{
        SyncInterval: time.Minute * 1, // 1分钟同步一次
        BatchSize:    500,
    },
}
```

## 同步服务配置

### 同步配置结构

```go
type SyncConfig struct {
    SyncInterval    time.Duration            // 同步间隔
    BatchSize       int                      // 批量大小
    MaxRetries      int                      // 最大重试次数
    RetryDelay      time.Duration            // 重试延迟
    TableConfigs    map[string]*TableSyncConfig // 表配置
    EnableDeltaSync bool                     // 启用增量同步
    DeltaConfig     *DeltaSyncConfig         // 增量同步配置
}

type DeltaSyncConfig struct {
    FlushInterval   time.Duration // 刷新间隔
    MaxDeltaSize    int          // 最大增量大小
    DeltaKeyPrefix  string       // 增量键前缀
}
```

### 表同步配置

```go
// 配置不同业务表的同步
tableSyncConfigs := map[string]*TableSyncConfig{
    "video": {
        TableName:       "videos",
        IDColumn:        "id",
        LikeCountColumn: "like_count",
    },
    "shortvideo": {
        TableName:       "short_videos",
        IDColumn:        "id",
        LikeCountColumn: "like_count",
    },
    "comment": {
        TableName:       "comments",
        IDColumn:        "id",
        LikeCountColumn: "like_count",
    },
    "post": {
        TableName:       "posts",
        IDColumn:        "id",
        LikeCountColumn: "like_count",
    },
}
```

### 定时同步任务

```go
func setupSyncTasks(service ExtendedLikeService, config *SyncConfig) {
    // 定时全量同步
    go func() {
        ticker := time.NewTicker(config.SyncInterval)
        defer ticker.Stop()
        
        for range ticker.C {
            ctx := context.Background()
            for itemType := range config.TableConfigs {
                if err := service.SyncLikeCountsToDatabase(ctx, itemType); err != nil {
                    log.Printf("同步失败 %s: %v", itemType, err)
                }
            }
        }
    }()
    
    // 增量同步（如果启用）
    if config.EnableDeltaSync {
        go func() {
            ticker := time.NewTicker(config.DeltaConfig.FlushInterval)
            defer ticker.Stop()
            
            for range ticker.C {
                // 执行增量同步逻辑
                flushDeltaSync(service, config)
            }
        }()
    }
}
```

## 性能调优

### 连接池配置

```go
type PerformanceConfig struct {
    // Redis连接池
    RedisPoolSize     int           // Redis连接池大小
    RedisMinIdle      int           // Redis最小空闲连接
    RedisMaxRetries   int           // Redis最大重试次数
    
    // MongoDB连接池
    MongoPoolSize     int           // MongoDB连接池大小
    MongoMinPoolSize  int           // MongoDB最小连接池大小
    MongoMaxIdleTime  time.Duration // MongoDB连接最大空闲时间
    
    // 批量操作
    BatchSize         int           // 批量操作大小
    ConcurrentWorkers int           // 并发工作者数量
    
    // 缓存配置
    CacheExpiration   time.Duration // 缓存过期时间
    CacheCleanupInterval time.Duration // 缓存清理间隔
}
```

### 性能监控指标

```go
type PerformanceMetrics struct {
    // 操作计数
    LikeOperations    int64 // 点赞操作数
    UnlikeOperations  int64 // 取消点赞操作数
    QueryOperations   int64 // 查询操作数
    
    // 响应时间
    AvgResponseTime   time.Duration // 平均响应时间
    MaxResponseTime   time.Duration // 最大响应时间
    MinResponseTime   time.Duration // 最小响应时间
    
    // 错误统计
    ErrorCount        int64 // 错误数量
    TimeoutCount      int64 // 超时数量
    
    // 缓存统计
    CacheHitRate      float64 // 缓存命中率
    CacheMissRate     float64 // 缓存未命中率
}
```

## 监控配置

### 日志配置

```go
type LogConfig struct {
    Level       string // 日志级别: debug, info, warn, error
    Format      string // 日志格式: json, text
    Output      string // 输出目标: stdout, file
    FilePath    string // 日志文件路径
    MaxSize     int    // 最大文件大小(MB)
    MaxBackups  int    // 最大备份数量
    MaxAge      int    // 最大保存天数
    Compress    bool   // 是否压缩
}
```

### 指标收集

```go
// Prometheus指标配置
func setupMetrics() {
    // 操作计数器
    likeCounter := prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "like_operations_total",
            Help: "Total number of like operations",
        },
        []string{"operation", "item_type", "status"},
    )
    
    // 响应时间直方图
    responseTimeHistogram := prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "like_operation_duration_seconds",
            Help:    "Duration of like operations",
            Buckets: prometheus.DefBuckets,
        },
        []string{"operation", "item_type"},
    )
    
    // 缓存命中率
    cacheHitGauge := prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "like_cache_hit_rate",
            Help: "Cache hit rate for like operations",
        },
        []string{"item_type"},
    )
    
    prometheus.MustRegister(likeCounter, responseTimeHistogram, cacheHitGauge)
}
```

### 健康检查

```go
func (s *extendedLikeService) HealthCheck(ctx context.Context) error {
    // 检查Redis连接
    if err := s.checkRedisHealth(ctx); err != nil {
        return fmt.Errorf("Redis health check failed: %w", err)
    }
    
    // 检查MongoDB连接
    if err := s.checkMongoHealth(ctx); err != nil {
        return fmt.Errorf("MongoDB health check failed: %w", err)
    }
    
    // 检查同步服务
    if err := s.checkSyncServiceHealth(ctx); err != nil {
        return fmt.Errorf("Sync service health check failed: %w", err)
    }
    
    return nil
}
```

## 部署建议

### 容器化部署

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN go build -o frontapi ./cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/frontapi .
COPY --from=builder /app/config ./config

EXPOSE 8080
CMD ["./frontapi"]
```

### Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  frontapi:
    build: .
    ports:
      - "8080:8080"
    environment:
      - REDIS_HOST=redis
      - MONGO_URI=mongodb://mongo:27017
    depends_on:
      - redis
      - mongo
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    restart: unless-stopped

  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped

volumes:
  redis_data:
  mongo_data:
```

### Kubernetes部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontapi
spec:
  replicas: 3
  selector:
    matchLabels:
      app: frontapi
  template:
    metadata:
      labels:
        app: frontapi
    spec:
      containers:
      - name: frontapi
        image: frontapi:latest
        ports:
        - containerPort: 8080
        env:
        - name: REDIS_HOST
          value: "redis-service"
        - name: MONGO_URI
          value: "mongodb://mongo-service:27017"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 生产环境配置清单

- [ ] Redis集群或哨兵配置
- [ ] MongoDB副本集配置
- [ ] 负载均衡配置
- [ ] SSL/TLS证书配置
- [ ] 监控和告警配置
- [ ] 日志收集配置
- [ ] 备份策略配置
- [ ] 安全策略配置
- [ ] 性能调优配置
- [ ] 灾难恢复计划

### 安全配置

```go
// 安全配置示例
type SecurityConfig struct {
    EnableAuth      bool   // 启用认证
    JWTSecret      string // JWT密钥
    RateLimitRPS   int    // 速率限制(请求/秒)
    MaxConnections int    // 最大连接数
    IPWhitelist    []string // IP白名单
    EnableTLS      bool   // 启用TLS
    TLSCertFile    string // TLS证书文件
    TLSKeyFile     string // TLS私钥文件
}
```

通过以上配置，您可以根据具体的业务需求和环境要求来部署和优化扩展点赞服务。