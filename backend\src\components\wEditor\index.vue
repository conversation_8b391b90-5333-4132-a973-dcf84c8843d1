<template>
  <div class="wang-editor-container" :style="{ height: height + 'px' }">
    <div class="toolbar-container" ref="toolbarRef"></div>
    <div class="editor-container" ref="editorRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';

// 定义 WangEditor 全局类型
declare global {
  interface Window {
    wangEditor: any;
  }
}

// 组件属性定义
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 500
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  toolbarConfig: {
    type: Object,
    default: () => ({})
  },
  editorConfig: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'default' // default 或 simple
  },
  uploadImgServer: {
    type: String,
    default: '/api/common/upload'
  }
});

// 事件声明
const emit = defineEmits(['update:modelValue', 'change', 'ready']);

// DOM 引用
const toolbarRef = ref<HTMLElement | null>(null);
const editorRef = ref<HTMLElement | null>(null);

// 编辑器实例
let editor: any = null;
let wangEditor: any = null;

// 初始化编辑器
const initEditor = () => {
  if (!window.wangEditor || !toolbarRef.value || !editorRef.value) {
    console.error('WangEditor 初始化失败：组件或 DOM 引用不存在');
    return;
  }

  // 创建编辑器实例
  wangEditor = window.wangEditor;
  const editorConfig = {
    placeholder: props.placeholder,
    readOnly: props.readOnly,
    scroll: true,
    MENU_CONF: {
      uploadImage: {
        server: props.uploadImgServer,
        fieldName: 'file',
        headers: {
          // 如果需要可以添加自定义请求头
        },
        // 上传图片成功后的回调函数
        customInsert(res: any, insertFn: Function) {
          // 从服务器响应中获取图片 URL
          if (res.code === 2000 && res.data && res.data.url) {
            // 插入图片到编辑器
            insertFn(res.data.url, res.data.alt || '', res.data.href || '');
          } else {
            console.error('图片上传失败:', res);
          }
        }
      }
    },
    ...props.editorConfig
  };

  // 创建编辑器和工具栏
  editor = wangEditor.createEditor({
    selector: editorRef.value,
    html: props.modelValue,
    config: editorConfig
  });

  // 工具栏配置
  const toolbarDefaultConfig = props.mode === 'simple' 
    ? {
        items: [
          'bold', 'italic', 'underline',
          '|',
          'bulletedList', 'numberedList',
          '|',
          'insertImage', 'insertLink',
          '|',
          'undo', 'redo'
        ]
      }
    : {
        items: [
          'headerSelect',
          'bold', 'italic', 'underline', 'through', 'code', 'clearStyle',
          '|',
          'color', 'bgColor',
          '|',
          'fontSize', 'fontFamily', 'lineHeight',
          '|',
          'bulletedList', 'numberedList', 'todo',
          '|',
          'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyJustify',
          '|',
          'insertLink', 'insertImage', 'insertTable', 'insertVideo', 'codeBlock',
          '|',
          'undo', 'redo',
          '|',
          'fullScreen'
        ]
      };

  const toolbar = wangEditor.createToolbar({
    editor,
    selector: toolbarRef.value,
    config: {
      ...toolbarDefaultConfig,
      ...props.toolbarConfig
    }
  });

  // 监听编辑器内容变化
  editor.on('change', () => {
    const html = editor.getHtml();
    emit('update:modelValue', html);
    emit('change', html);
  });

  // 编辑器就绪，触发回调
  nextTick(() => {
    emit('ready', editor);
  });
};

// 加载 WangEditor 脚本和样式
const loadWangEditor = async () => {
  try {
    // 检查是否已经加载过
    if (window.wangEditor) {
      initEditor();
      return;
    }

    // 加载样式
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = 'https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css';
    document.head.appendChild(cssLink);

    // 加载脚本
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/@wangeditor/editor@latest/dist/index.js';
    script.async = true;
    
    // 脚本加载完成后初始化编辑器
    script.onload = () => {
      initEditor();
    };
    
    // 脚本加载失败时的处理
    script.onerror = (error) => {
      console.error('无法加载 WangEditor 脚本:', error);
    };
    
    document.head.appendChild(script);
  } catch (error) {
    console.error('加载 WangEditor 时出错:', error);
  }
};

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (editor && newValue !== editor.getHtml()) {
    editor.setHtml(newValue);
  }
});

// 监听只读状态变化
watch(() => props.readOnly, (newValue) => {
  if (editor) {
    editor.enable(!newValue);
  }
});

// 组件挂载时加载 WangEditor
onMounted(() => {
  loadWangEditor();
});

// 组件卸载前销毁编辑器实例
onBeforeUnmount(() => {
  if (editor) {
    editor.destroy();
    editor = null;
  }
});

// 对外暴露编辑器实例方法
defineExpose({
  // 获取或手动设置编辑器 HTML 内容
  getHtml: () => editor?.getHtml(),
  setHtml: (html: string) => editor?.setHtml(html),
  
  // 获取或手动设置编辑器纯文本内容
  getText: () => editor?.getText(),
  setText: (text: string) => editor?.setText(text),
  
  // 清空编辑器内容
  clear: () => editor?.clear(),
  
  // 获取编辑器实例
  getEditor: () => editor
});
</script>

<style scoped>
.wang-editor-container {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.toolbar-container {
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.editor-container {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
}

/* 编辑器内容区默认样式 */
:deep(.w-e-text-container) {
  height: calc(100% - 40px) !important;
}

:deep(.w-e-text-placeholder) {
  color: #909399;
}

:deep(.w-e-toolbar) {
  padding: 0 10px;
}
</style>

<style>
/* 全局编辑器样式覆盖 */
.w-e-full-screen-container {
  z-index: 10000 !important;
}

.w-e-menu-tooltip {
  z-index: 10001 !important;
}

.w-e-modal {
  z-index: 10002 !important;
}

.w-e-toolbar .w-e-bar-item .w-e-active {
  background-color: #ecf5ff !important;
  color: #409eff !important;
}

.w-e-toolbar .w-e-bar-item:hover {
  background-color: #f0f0f0 !important;
}

.w-e-text-container {
  background-color: #fff !important;
}

.w-e-panel-tab-title.w-e-active {
  border-bottom: 2px solid #409eff !important;
  color: #409eff !important;
}

.w-e-button-container .w-e-button.primary {
  background-color: #409eff !important;
  border-color: #409eff !important;
}
</style> 