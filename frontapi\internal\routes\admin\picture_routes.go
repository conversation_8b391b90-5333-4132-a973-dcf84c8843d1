package admin

import (
	"frontapi/internal/admin/pictures"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterPictureRoutes 注册图片相关路由
func RegisterPictureRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 图片相关路由组
	picturesApi := apiGroup.Group("/pictures", middleware.AuthRequired())
	{
		// 创建图片API控制器
		pictureController := pictures.NewPictureController(
			services.PictureService,
			services.PictureCategoryService,
			services.PictureAlbumService,
			services.PictureCollectionService,
		)
		// 公开接口
		picturesApi.Post("/list", pictureController.ListPictures)
		picturesApi.Post("/detail/:id?", pictureController.GetPicture)
		//picturesApi.Post("/search", pictureController.SearchPictures)

		//picturesApi.Post("/album/:albumId?", pictureController.ListPicturesByAlbum)
		//picturesApi.Post("/creator/:creatorId?", pictureController.ListPicturesByCreator)
		//picturesApi.Post("/featured", pictureController.ListFeaturedPictures)

		// 需要认证的接口
		picturesApi.Post("/add", middleware.AuthRequired(), pictureController.CreatePicture)
		picturesApi.Post("/batch-add", middleware.AuthRequired(), pictureController.BatchCreatePictures)
		picturesApi.Post("/update/:id?", middleware.AuthRequired(), pictureController.UpdatePicture)
		picturesApi.Post("/delete/:id?", middleware.AuthRequired(), pictureController.DeletePicture)

		// 添加状态更新和批量操作路由
		picturesApi.Post("/update-status", middleware.AuthRequired(), pictureController.UpdatePictureStatus)
		picturesApi.Post("/batch-update-status", middleware.AuthRequired(), pictureController.BatchUpdatePictureStatus)
		picturesApi.Post("/batch-delete", middleware.AuthRequired(), pictureController.BatchDeletePicture)
	}

	// 图片分类相关路由组
	categories := apiGroup.Group("/pictures/categories", middleware.AuthRequired())
	{
		// 创建图片API控制器
		pictureCategoryController := pictures.NewPictureCategoryController(
			services.PictureCategoryService,
			services.PictureService,
		)
		// 公开接口
		categories.Post("/list", pictureCategoryController.ListCategories)
		categories.Post("/all", pictureCategoryController.ListCategories)
		categories.Post("/detail/:id?", pictureCategoryController.GetCategory)
		// 需要管理员权限的接口
		categories.Post("/add", middleware.AuthRequired(), pictureCategoryController.CreateCategory)
		categories.Post("/update/:id?", middleware.AuthRequired(), pictureCategoryController.UpdateCategory)
		categories.Post("/delete/:id?", middleware.AuthRequired(), pictureCategoryController.DeleteCategory)
		// 添加状态更新和批量操作路由
		categories.Post("/update-status", middleware.AuthRequired(), pictureCategoryController.UpdateCategoryStatus)
		categories.Post("/batch-update-status", middleware.AuthRequired(), pictureCategoryController.BatchUpdateCategoryStatus)
		categories.Post("/batch-delete", middleware.AuthRequired(), pictureCategoryController.BatchDeleteCategory)
	}

	// 图片专辑相关路由组
	albums := apiGroup.Group("/pictures/albums", middleware.AuthRequired())
	{
		pictureAlbumController := pictures.NewPictureAlbumController(services.PictureAlbumService, services.PictureService)
		// 公开接口
		albums.Post("/list", pictureAlbumController.ListAlbums)
		albums.Post("/detail/:id?", pictureAlbumController.GetAlbum)

		// 需要认证的接口
		albums.Post("/add", middleware.AuthRequired(), pictureAlbumController.CreateAlbum)
		albums.Post("/update/:id?", middleware.AuthRequired(), pictureAlbumController.UpdateAlbum)
		albums.Post("/delete/:id?", middleware.AuthRequired(), pictureAlbumController.DeleteAlbum)
		albums.Post("/addToAlbum", middleware.AuthRequired(), pictureAlbumController.AddPicturesToAlbum)
		albums.Post("/removeToAlbum", middleware.AuthRequired(), pictureAlbumController.RemovePicturesFromAlbum)

		// 添加状态更新和批量操作路由
		albums.Post("/update-status", middleware.AuthRequired(), pictureAlbumController.UpdateAlbumStatus)
		albums.Post("/batch-update-status", middleware.AuthRequired(), pictureAlbumController.BatchUpdateAlbumStatus)
		albums.Post("/batch-delete", middleware.AuthRequired(), pictureAlbumController.BatchDeleteAlbum)
	}

	// 图片收藏相关路由组
	//collections := app.Group("/api/proadm/picture-collections", middleware.AuthRequired())
	//{
	//	// 需要认证的接口
	//	collections.Post("/list", middleware.AuthRequired(), pictureController.ListCollections)
	//}
}
