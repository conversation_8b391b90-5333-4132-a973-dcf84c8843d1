/**
 * 中文主题语言包
 */

export default {
    // 主题名称
    light: "浅色",
    dark: "深色",
    blue: "蓝色",
    purple: "紫色",
    green: "绿色",
    indigo: "靛蓝",
    teal: "青色",
    deeppurple: "深紫色",
    orange: "橙色",
    rose: "玫瑰色",
    fuchsia: "紫红色",
    noir: "黑色",

    // PrimeVue 主题家族
    auraFamily: "Aura",
    laraFamily: "Lara",
    mdFamily: "Material",

    // 主题设置
    themeFamily: "主题风格",
    themeSystemDemo: "主题系统演示",
    themeSystemDescription: "此页面展示了基于PrimeVue和CSS变量的新主题系统。",
    themeSelector: "主题选择器",
    themeSelectorDescription: "主题选择器组件允许用户选择不同的主题并切换暗黑模式。",
    themeVariables: "主题变量",
    componentPreview: "组件预览",
    buttons: "按钮",
    inputs: "输入框",
    cards: "卡片",
    cardTitle: "卡片标题",
    cardContent: "这是一个展示主题样式的示例卡片组件。",
    apiUsage: "API使用方法",

    // 设置界面
    title: "选择主题",
    selectTheme: "选择主题",
    themeSettings: "主题设置",
    changeTheme: "更换主题",
    followSystem: "跟随系统主题",
    darkMode: "暗黑模式",
    default: "默认主题",

    // 其他设置
    settings: "主题设置",
    customization: "个性化设置",
    apply: "应用主题",
    reset: "重置设置"
}