import enTheme from '../en/theme';

export default {
    ...enTheme,
    // 主题名称
    modern: "明快现代",
    warm: "温暖友好",
    dark: "时尚前卫",
    fresh: "清新自然",
    charm: "魅力诱惑",
    mysterious: "神秘高雅",
    light: "浅色主题",

    blue: "蓝色主题",
    "purple": "紫色主题",
    "green": "绿色主题",
    "pink": "粉色主题",
    "mysterious-light": "神秘高雅 浅色",
    "mysterious-dark": "神秘高雅 深色",
    "modern-light": "明快现代 浅色",
    "modern-dark": "明快现代 深色",
    "warm-light": "温暖友好 浅色",
    "warm-dark": "温暖友好 深色",
    "dark-light": "时尚前卫 浅色",
    "dark-dark": "时尚前卫 深色",
    "fresh-light": "清新自然 浅色",
    "fresh-dark": "清新自然 深色",
    "charm-light": "魅力诱惑 浅色",
    "charm-dark": "魅力诱惑 深色",
    "themeSystemDemo": "主题系统演示",
    "themeSystemDescription": "此页面展示了基于PrimeVue和CSS变量的新主题系统。",
    "themeSelector": "主题选择器",
    "themeSelectorDescription": "主题选择器组件允许用户选择不同的主题并切换暗黑模式。",
    "themeVariables": "主题变量",
    "componentPreview": "组件预览",
    "buttons": "按钮",
    "inputs": "输入框",
    "cards": "卡片",
    "cardTitle": "卡片标题",
    "cardContent": "这是一个展示主题样式的示例卡片组件。",
    "apiUsage": "API使用方法",
    // 设置界面
    title: "选择主题",
    selectTheme: "选择主题",
    themeSettings: "主题设置",
    changeTheme: "更换主题",
    followSystem: "跟随系统主题",
    darkMode: "暗黑模式",

    // 描述
    modernDesc: "科技感与信任，适合视频平台或社交主页",
    warmDesc: "让用户感觉亲切，适合社区互动类",
    darkDesc: "提升潮酷感，适合年轻用户群",
    freshDesc: "营造舒适体验，适合兴趣社群或生活分享平台",
    charmDesc: "从浅粉到玫红，带来温柔又活力的氛围",
    mysteriousDesc: "奢华感、成熟、时尚，温柔、梦幻，活力、前卫",

    // 其他设置
    settings: "主题设置",
    customization: "个性化设置",
    apply: "应用主题",
    reset: "重置设置"
} as const;