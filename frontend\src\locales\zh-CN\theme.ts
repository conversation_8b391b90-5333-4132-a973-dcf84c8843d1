import enTheme from '../en/theme';

export default {
    ...enTheme,
    // 主题名称
    modern: "明快现代",
    warm: "温暖友好",
    dark: "时尚前卫",
    fresh: "清新自然",
    charm: "魅力诱惑",
    mysterious: "神秘高雅",

    // 设置界面
    title: "选择主题",
    selectTheme: "选择主题",
    themeSettings: "主题设置",
    changeTheme: "更换主题",
    followSystem: "跟随系统主题",
    darkMode: "暗黑模式",

    // 描述
    modernDesc: "科技感与信任，适合视频平台或社交主页",
    warmDesc: "让用户感觉亲切，适合社区互动类",
    darkDesc: "提升潮酷感，适合年轻用户群",
    freshDesc: "营造舒适体验，适合兴趣社群或生活分享平台",
    charmDesc: "从浅粉到玫红，带来温柔又活力的氛围",
    mysteriousDesc: "奢华感、成熟、时尚，温柔、梦幻，活力、前卫",

    // 其他设置
    settings: "主题设置",
    customization: "个性化设置",
    apply: "应用主题",
    reset: "重置设置"
} as const;