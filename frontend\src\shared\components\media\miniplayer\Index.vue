<template>
  <div 
    ref="playerContainer" 
    class="mini-video-player"
    :class="{ 'is-compact': isCompact }"
    :style="containerStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div ref="playerElement" class="player-wrapper">
      <!-- 移除video-background元素，它导致视频模糊 -->
    </div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner">
        <SvgIcon name="play" :size="32" color="#667eea" />
        <span>加载中...</span>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="hasError" class="error-overlay">
      <div class="error-content">
        <SvgIcon name="play" :size="32" color="#ff6b6b" />
        <p>视频加载失败</p>
        <button @click="retryLoad" class="retry-btn">重试</button>
      </div>
    </div>

    <!-- 自定义音量控制 -->
    <div v-if="showVolumeControl" class="volume-control-overlay">
      <div class="volume-slider-container">
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          :value="currentVolume"
          @input="handleVolumeChange"
          class="volume-slider"
        />
        <div class="volume-display">{{ Math.round(currentVolume * 100) }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, markRaw, shallowRef } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'
import { useIntersectionObserver } from './composables/useIntersectionObserver'
import SvgIconComponent from '@/shared/components/common/SvgIcon.vue'

// 使用markRaw避免响应式警告
const SvgIcon = ref(markRaw(SvgIconComponent))

// 定义属性
interface Props {
  src: string
  poster?: string
  duration?: number
  quality?: string
  width?: number | string
  height?: number | string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  isCompact?: boolean
  showInfo?: boolean
  videoInfo?: {
    title: string
    views: number
    uploadDate: string
  }
  enableAutoPlay?: boolean
  aspectRatio?: string // 新增：支持宽高比
}

const props = withDefaults(defineProps<Props>(), {
  poster: '',
  duration: 0,
  quality: '',
  width: '100%',
  height: 240,
  autoplay: false,
  muted: false,
  loop: false,
  isCompact: false,
  showInfo: false,
  enableAutoPlay: true,
  aspectRatio: '16:9'
})

// 定义事件
const emit = defineEmits<{
  'play': []
  'pause': []
  'ended': []
  'error': [error: Error]
  'timeupdate': [currentTime: number]
  'loadedmetadata': [duration: number]
  'click': []
  'volume-change': [volume: number]
}>()

// 模板引用
const playerContainer = ref<HTMLElement>()
const playerElement = ref<HTMLElement>()

// 播放器状态 - 使用shallowRef避免响应式警告
const player = shallowRef<Player | null>(null)
const isLoading = ref(false)
const hasError = ref(false)
const showVolumeControl = ref(false)
const currentVolume = ref(1)
const volumeControlTimer = ref<number>()
const isPlaying = ref(false)
const isHovering = ref(false)
const controlsHideTimer = ref<number>()

// 使用Intersection Observer监听可见性
const { isIntersecting } = useIntersectionObserver(
  playerContainer,
  {
    threshold: 0.3,
    rootMargin: '50px'
  }
)

// 计算容器样式 - 新的宽高比例计算逻辑
const containerStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 优先显示高度100%，宽度等比例缩放
  style.height = '100%'
  style.width = '100%'
  style.position = 'relative'
  style.display = 'flex'
  style.alignItems = 'center'
  style.justifyContent = 'center'
  
  return style
})

// 监听可见性变化，自动暂停/恢复播放
watch(isIntersecting, (visible) => {
  if (!props.enableAutoPlay || !player.value) return
  
  if (!visible && !player.value.paused) {
    player.value.pause()
  }
})

// 处理鼠标进入事件
const handleMouseEnter = () => {
  isHovering.value = true
  showControls()
}

// 处理鼠标离开事件
const handleMouseLeave = () => {
  isHovering.value = false
  if (isPlaying.value) {
    hideControlsWithDelay()
  }
}

// 显示控制栏
const showControls = () => {
  if (controlsHideTimer.value) {
    clearTimeout(controlsHideTimer.value)
  }
  
  const controlsElement = playerElement.value?.querySelector('.xgplayer-controls') as HTMLElement
  if (controlsElement) {
    controlsElement.style.opacity = '1'
    controlsElement.style.visibility = 'visible'
    controlsElement.style.transform = 'translateY(0)'
  }
}

// 延迟隐藏控制栏
const hideControlsWithDelay = () => {
  if (controlsHideTimer.value) {
    clearTimeout(controlsHideTimer.value)
  }
  
  controlsHideTimer.value = window.setTimeout(() => {
    if (isPlaying.value && !isHovering.value) {
      hideControls()
    }
  }, 3000) // 3秒后隐藏
}

// 隐藏控制栏
const hideControls = () => {
  const controlsElement = playerElement.value?.querySelector('.xgplayer-controls') as HTMLElement
  if (controlsElement) {
    controlsElement.style.opacity = '0'
    controlsElement.style.visibility = 'hidden'
    controlsElement.style.transform = 'translateY(100%)'
  }
}

// 初始化播放器
const initPlayer = async () => {
  if (!playerElement.value) return

  try {
    isLoading.value = true
    hasError.value = false

    // 创建explayer实例
    player.value = new Player({
      el: playerElement.value,
      url: props.src,
      poster: props.poster,
      width: '100%',
      height: '100%',
      autoplay: props.autoplay,
      muted: props.muted,
      loop: props.loop,
      playsinline: true,
      pip: true, // 启用画中画
      volume: currentVolume.value,
      controls: {
        mode: 'normal',
        initShow: true, // 初始显示控制栏
        autoHide: false, // 禁用自动隐藏，我们手动控制
      },
      // 自定义控制栏配置
      controlsList: ['play', 'progress', 'time', 'volume', 'pip', 'fullscreen'],
      // 移动端优化
      isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      // 新的视频适应策略：优先高度100%，宽度等比例缩放
      fitVideoSize: 'fixHeight', // 固定高度
      videoFillMode: 'contain', // 确保视频完全显示
      // 视频居中显示
      videoInit: true,
    })

    // 绑定事件
    bindPlayerEvents()
    
    // 设置视频样式
    setupVideoStyles()

    isLoading.value = false
  } catch (error) {
    console.error('播放器初始化失败:', error)
    hasError.value = true
    isLoading.value = false
    emit('error', error as Error)
  }
}

// 设置视频样式 - 实现新的宽高比例逻辑
const setupVideoStyles = () => {
  if (!player.value) return
  
  nextTick(() => {
    const videoElement = playerElement.value?.querySelector('video') as HTMLVideoElement
    const containerElement = playerContainer.value
    
    if (videoElement && containerElement) {
      // 监听视频元数据加载完成
      videoElement.addEventListener('loadedmetadata', () => {
        adjustVideoSize(videoElement, containerElement)
      })
      
      // 如果视频已经加载，立即调整
      if (videoElement.videoWidth && videoElement.videoHeight) {
        adjustVideoSize(videoElement, containerElement)
      }
    }
  })
}

// 调整视频尺寸
const adjustVideoSize = (videoElement: HTMLVideoElement, containerElement: HTMLElement) => {
  const videoAspectRatio = videoElement.videoWidth / videoElement.videoHeight
  const containerWidth = containerElement.clientWidth
  const containerHeight = containerElement.clientHeight
  const containerAspectRatio = containerWidth / containerHeight
  
  // 第一步：优先显示视频高度100%，宽度等比例缩放
  let videoWidth = containerHeight * videoAspectRatio
  let videoHeight = containerHeight
  
  // 第二步：检测视频宽度是否超过父容器宽度
  if (videoWidth > containerWidth) {
    // 如果超过，则调整为容器宽度100%，高度等比例缩放
    videoWidth = containerWidth
    videoHeight = containerWidth / videoAspectRatio
  }
  
  // 应用样式
  videoElement.style.width = `${videoWidth}px`
  videoElement.style.height = `${videoHeight}px`
  videoElement.style.objectFit = 'contain'
  videoElement.style.position = 'absolute'
  videoElement.style.top = '50%'
  videoElement.style.left = '50%'
  videoElement.style.transform = 'translate(-50%, -50%)'
}

// 绑定播放器事件
const bindPlayerEvents = () => {
  if (!player.value) return

  // 播放事件
  player.value.on('play', () => {
    isPlaying.value = true
    emit('play')
    // 播放时延迟隐藏控制栏
    hideControlsWithDelay()
  })

  // 暂停事件
  player.value.on('pause', () => {
    isPlaying.value = false
    emit('pause')
    // 暂停时显示控制栏
    showControls()
  })

  // 结束事件
  player.value.on('ended', () => {
    isPlaying.value = false
    emit('ended')
    // 结束时显示控制栏
    showControls()
  })

  // 错误事件
  player.value.on('error', (error: any) => {
    hasError.value = true
    emit('error', error)
  })

  // 时间更新事件
  player.value.on('timeupdate', () => {
    if (player.value) {
      emit('timeupdate', player.value.currentTime)
    }
  })

  // 元数据加载完成
  player.value.on('loadedmetadata', () => {
    if (player.value) {
      emit('loadedmetadata', player.value.duration)
    }
  })

  // 音量变化事件
  player.value.on('volumechange', () => {
    if (player.value) {
      currentVolume.value = player.value.volume
      emit('volume-change', player.value.volume)
    }
  })

  // 点击事件
  player.value.on('click', () => {
    emit('click')
  })

  // 自定义音量按钮点击事件
  try {
    const controls = player.value.controls as any
    const volumeBtn = controls?.volume
    if (volumeBtn && typeof volumeBtn.on === 'function') {
      volumeBtn.on('click', () => {
        showVolumeControlPanel()
      })
    }
  } catch (error) {
    console.warn('音量控制按钮绑定失败:', error)
  }
}

// 显示音量控制面板
const showVolumeControlPanel = () => {
  showVolumeControl.value = true
  
  // 清除之前的定时器
  if (volumeControlTimer.value) {
    clearTimeout(volumeControlTimer.value)
  }
  
  // 3秒后自动隐藏
  volumeControlTimer.value = window.setTimeout(() => {
    showVolumeControl.value = false
  }, 3000)
}

// 处理音量变化
const handleVolumeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const volume = parseFloat(target.value)
  currentVolume.value = volume
  
  if (player.value) {
    player.value.volume = volume
  }
  
  emit('volume-change', volume)
}

// 重试加载
const retryLoad = () => {
  hasError.value = false
  destroyPlayer()
  nextTick(() => {
    initPlayer()
  })
}

// 销毁播放器
const destroyPlayer = () => {
  if (player.value) {
    try {
      player.value.destroy()
    } catch (error) {
      console.warn('播放器销毁时出错:', error)
    }
    player.value = null
  }
  
  if (volumeControlTimer.value) {
    clearTimeout(volumeControlTimer.value)
  }
  
  if (controlsHideTimer.value) {
    clearTimeout(controlsHideTimer.value)
  }
}

// 暴露的方法
const play = () => {
  if (player.value) {
    player.value.play()
  }
}

const pause = () => {
  if (player.value) {
    player.value.pause()
  }
}

const togglePlay = () => {
  if (player.value) {
    if (player.value.paused) {
      player.value.play()
    } else {
      player.value.pause()
    }
  }
}

const setVolume = (volume: number) => {
  currentVolume.value = volume
  if (player.value) {
    player.value.volume = volume
  }
}

const toggleMute = () => {
  if (player.value) {
    player.value.muted = !player.value.muted
  }
}

const seek = (time: number) => {
  if (player.value) {
    player.value.currentTime = time
  }
}

const enterPictureInPicture = () => {
  if (player.value && player.value.video) {
    if ('requestPictureInPicture' in player.value.video) {
      player.value.video.requestPictureInPicture()
    }
  }
}

const exitPictureInPicture = () => {
  if (document.pictureInPictureElement) {
    document.exitPictureInPicture()
  }
}

// 暴露方法给父组件
defineExpose({
  play,
  pause,
  togglePlay,
  setVolume,
  toggleMute,
  seek,
  enterPictureInPicture,
  exitPictureInPicture,
  player: computed(() => player.value)
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initPlayer()
  })
})

onUnmounted(() => {
  destroyPlayer()
})

// 监听src变化，重新初始化播放器
watch(() => props.src, () => {
  if (player.value) {
    destroyPlayer()
    nextTick(() => {
      initPlayer()
    })
  }
})
</script>

<style scoped lang="scss">
.mini-video-player {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 240px; // 确保最小高度

  &.is-compact {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    min-height: 200px;
  }

  .player-wrapper {
    position: relative;
    width: 100%;
    height: 100%; // 恢复100%高度
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  }

  // 加载状态
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.95), rgba(45, 45, 45, 0.95));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    backdrop-filter: blur(4px);

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      color: white;

      span {
        font-size: 14px;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }

  // 错误状态
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(26, 26, 26, 0.95), rgba(45, 45, 45, 0.95));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    backdrop-filter: blur(4px);

    .error-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      color: white;
      text-align: center;

      p {
        margin: 0;
        font-size: 14px;
        opacity: 0.9;
        font-weight: 500;
      }

      .retry-btn {
        padding: 10px 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }
  }

  // 音量控制面板
  .volume-control-overlay {
    position: absolute;
    bottom: 80px; // 调整位置以适应新的控制栏布局
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(12px);
    border-radius: 12px;
    padding: 16px;
    z-index: 30;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

    .volume-slider-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;

      .volume-slider {
        width: 120px;
        height: 5px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
        outline: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &::-webkit-slider-thumb {
          appearance: none;
          width: 15px;
          height: 15px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
          transition: all 0.3s ease;
        }

        &::-moz-range-thumb {
          width: 15px;
          height: 15px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          border-radius: 50%;
          border: none;
          cursor: pointer;
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }
      }

      .volume-display {
        color: white;
        font-size: 13px;
        font-weight: 500;
        opacity: 0.9;
      }
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes slideInRight {
  from {
    transform: translateY(-50%) translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateY(-50%) translateX(0);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .mini-video-player {
    border-radius: 8px;
    min-height: 200px;

    .volume-control-overlay {
      bottom: 60px;
      padding: 12px;

      .volume-slider-container {
        .volume-slider {
          width: 100px;
        }
      }
    }
  }
}

// PostItem 中的特殊样式
.post-item .mini-video-player {
  width: 100%;
  margin: 0;
  border-radius: 12px;
}

// 深度样式覆盖explayer默认样式
:deep(.xgplayer) {
  width: 100% !important;
  height: 100% !important;
  border-radius: inherit;
  position: relative;
  z-index: 20;
  display: block !important; // 改回block布局

  .xgplayer-video {
    width: 100% !important;
    height: calc(100% - 60px) !important; // 减去控制栏高度
    object-fit: contain; // 改为 contain 以确保视频完全显示
    background: transparent;
    border-radius: inherit;
    display: block !important;
    flex: none !important;
  }

  // 控制栏样式 - 智能显示/隐藏
  .xgplayer-controls {
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.9) 0%,
      rgba(0, 0, 0, 0.6) 50%,
      rgba(0, 0, 0, 0.3) 80%,
      transparent 100%
    ) !important;
    backdrop-filter: blur(8px);
    border-radius: 0 0 12px 12px;
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important; // 缓慢动画
    padding: 8px 16px !important;
    position: absolute !important; // 改回绝对定位
    bottom: 0 !important; // 固定在底部
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    height: 60px !important; // 固定控制栏高度
    z-index: 25;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    box-sizing: border-box !important;
    
    // 确保控制栏内的元素正确排列
    > * {
      flex-shrink: 0 !important;
    }
    
    // 进度条容器应该占据剩余空间
    .xgplayer-progress {
      flex: 1 !important;
      margin: 0 12px !important;
    }
  }

  // 播放按钮样式
  .xgplayer-start {
    .xgplayer-start-btn {
      width: 64px !important;
      height: 64px !important;
      background: rgba(255, 255, 255, 0.95) !important;
      border-radius: 50% !important;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 1) !important;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
      }

      &::before {
        color: #667eea !important;
        font-size: 28px !important;
      }
    }
  }

  // 进度条样式
  .xgplayer-progress {
    .xgplayer-progress-outer {
      background: rgba(255, 255, 255, 0.3) !important;
      height: 5px !important;
      border-radius: 3px !important;
    }

    .xgplayer-progress-cache {
      background: rgba(255, 255, 255, 0.5) !important;
      border-radius: 3px !important;
    }

    .xgplayer-progress-played {
      background: linear-gradient(90deg, #667eea, #764ba2) !important;
      border-radius: 3px !important;
      box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);
    }

    .xgplayer-progress-btn {
      width: 14px !important;
      height: 14px !important;
      background: #667eea !important;
      border: 3px solid white !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }
  }

  // 控制按钮通用样式
  .xgplayer-controls .xgplayer-icon {
    color: white !important;
    transition: all 0.3s ease;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }

  // 音量控制
  .xgplayer-volume {
    .xgplayer-volume-btn {
      color: white !important;
      transition: all 0.3s ease;
    }
  }

  // 全屏按钮
  .xgplayer-fullscreen {
    .xgplayer-fullscreen-btn {
      color: white !important;
      transition: all 0.3s ease;

    }
  }

  // 画中画按钮
  .xgplayer-pip {
    .xgplayer-pip-btn {
      color: white !important;
      transition: all 0.3s ease;

      &:hover {
        color: #667eea !important;
      }
    }
  }

  // 时间显示
  .xgplayer-time {
    color: white !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  // 播放按钮
  .xgplayer-play {
    color: white !important;
    
    &:hover {
      color: #667eea !important;
    }
  }
}
:deep(.xgplayer){
  video{
    height: calc(100% - 60px);
  }
}
</style>