/**
 * 国际化插件主文件
 */

import { DEFAULT_LOCALE, LOCALE_STORAGE, SUPPORTED_LOCALES } from '@/config/locales.config';
import { loadLocaleMessages, type LocaleType } from '@/locales';
import { useLocaleStore } from '@/store/modules/locale';
import type { App } from 'vue';
import { nextTick } from 'vue';
import { createI18n, type Composer, type I18n } from 'vue-i18n';
import { I18N_INJECTION_KEY as I18N_KEY } from './composables';
import type { I18nOptions } from './types';
import { detectLocale } from './utils';

// 重新导出以避免歧义
export { I18N_INJECTION_KEY as I18N_KEY } from './composables';

/**
 * 语言本地化选项
 */
export interface LocaleOptions {
    /** 自定义日期时间格式 */
    dateTimeFormats?: Record<string, any>
    /** 自定义数字格式 */
    numberFormats?: Record<string, any>
}

/**
 * 应用语言本地化选项
 */
export const appLocaleOptions: LocaleOptions = {
    // 自定义日期时间格式
    dateTimeFormats: {
        'en': {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' }
        },
        'zh': {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' }
        },
        'zh-CN': {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' }
        },
        'zh-TW': {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' }
        },
        'ko': {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' }
        }
    },

    // 自定义数字格式
    numberFormats: {
        'en': {
            currency: { style: 'currency', currency: 'USD', notation: 'standard' },
            decimal: { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 },
            percent: { style: 'percent', useGrouping: false }
        },
        'zh': {
            currency: { style: 'currency', currency: 'CNY', notation: 'standard' },
            decimal: { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 },
            percent: { style: 'percent', useGrouping: false }
        },
        'zh-CN': {
            currency: { style: 'currency', currency: 'CNY', notation: 'standard' },
            decimal: { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 },
            percent: { style: 'percent', useGrouping: false }
        },
        'zh-TW': {
            currency: { style: 'currency', currency: 'TWD', notation: 'standard' },
            decimal: { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 },
            percent: { style: 'percent', useGrouping: false }
        },
        'ko': {
            currency: { style: 'currency', currency: 'KRW', notation: 'standard' },
            decimal: { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 },
            percent: { style: 'percent', useGrouping: false }
        }
    }
}

// 默认插件配置
const DEFAULT_OPTIONS: I18nOptions = {
    debug: import.meta.env.DEV,
    defaultLocale: DEFAULT_LOCALE as LocaleType,
    fallbackLocale: DEFAULT_LOCALE as LocaleType,
    preloadAllLanguages: false,
    autoDetect: true,
    asyncLoading: true,
    cacheStrategy: LOCALE_STORAGE.STORAGE as 'localStorage' | 'sessionStorage',
    cacheKey: LOCALE_STORAGE.KEY,
    useStore: true,
    dateTimeFormats: appLocaleOptions.dateTimeFormats,
    numberFormats: appLocaleOptions.numberFormats,
    additionalLocales: SUPPORTED_LOCALES.filter(locale => locale.enabled).map(locale => locale.code) as LocaleType[]
};

// 全局i18n实例
export let i18n: Composer;
// 全局i18n对象
export let i18nInstance: I18n;

/**
 * 创建i18n插件
 */
export function createI18nPlugin(options: I18nOptions = {}) {
    // 合并配置
    const finalOptions: I18nOptions = { ...DEFAULT_OPTIONS, ...options };

    // 创建vue-i18n实例
    i18nInstance = createI18n({
        legacy: false,
        locale: finalOptions.defaultLocale as string,
        fallbackLocale: finalOptions.fallbackLocale as string,
        messages: {},
        datetimeFormats: finalOptions.dateTimeFormats || {},
        numberFormats: finalOptions.numberFormats || {},
        missingWarn: finalOptions.debug,
        fallbackWarn: finalOptions.debug,
        sync: true,
        silentFallbackWarn: !finalOptions.debug,
        silentTranslationWarn: !finalOptions.debug,
        runtimeOnly: false
    });

    // 将全局实例保存为模块变量，方便其他地方访问
    i18n = i18nInstance.global;

    // 已加载的语言包
    const loadedLanguages = new Set<string>();

    /**
     * 加载语言包
     */
    const loadLanguage = async (locale: LocaleType) => {
        // 如果已经加载过，直接返回
        if (loadedLanguages.has(locale)) {
            return true;
        }

        try {
            // 动态加载语言包
            const messages = await loadLocaleMessages(locale);

            // 设置语言包
            i18n.setLocaleMessage(locale, messages);

            // 标记为已加载
            loadedLanguages.add(locale);

            return true;
        } catch (error) {
            console.error(`Failed to load locale ${locale}:`, error);
            return false;
        }
    };

    /**
     * 切换语言
     */
    const setLanguage = async (locale: LocaleType): Promise<boolean> => {
        console.log(`Setting language to: ${locale}`);

        // 加载语言包
        const success = await loadLanguage(locale);

        if (success) {
            try {
                // 重要：设置当前语言
                i18n.locale.value = locale;
                console.log(`i18n.locale set to: ${i18n.locale.value}`);

                // 强制刷新
                await nextTick();

                // 如果使用store，同步到store
                if (finalOptions.useStore) {
                    try {
                        const localeStore = useLocaleStore();
                        localeStore.setLocale(locale);
                    } catch (error) {
                        console.warn('Failed to sync locale to store:', error);
                    }
                }

                // 设置HTML文档的语言
                document.documentElement.setAttribute('lang', locale);
                document.documentElement.setAttribute('dir',
                    ['ar', 'he', 'fa', 'ur'].some(code => locale.startsWith(code)) ? 'rtl' : 'ltr'
                );

                // 强制更新页面上所有使用i18n的组件（使用自定义事件）
                window.dispatchEvent(new CustomEvent('i18n:locale-changed', { detail: { locale } }));

                return true;
            } catch (error) {
                console.error('Error setting language:', error);
                return false;
            }
        }

        return false;
    };

    // 将setLanguage函数暴露到全局，便于组件调用
    window.$setLanguage = setLanguage;

    /**
     * 初始化语言
     */
    const initializeLanguage = async () => {
        let initialLocale: LocaleType;

        // 如果使用store
        if (finalOptions.useStore) {
            try {
                const localeStore = useLocaleStore();
                // 初始化语言存储
                localeStore.initializeLocale();
                initialLocale = localeStore.getLocale();
            } catch (error) {
                console.warn('Failed to get locale from store, using detected locale');
                initialLocale = detectLocale(finalOptions.autoDetect);
            }
        } else {
            // 直接检测语言
            initialLocale = detectLocale(finalOptions.autoDetect);
        }

        // 加载初始语言
        await setLanguage(initialLocale);

        // 如果需要预加载所有语言
        if (finalOptions.preloadAllLanguages && finalOptions.additionalLocales) {
            const loadPromises = [];
            for (const locale of finalOptions.additionalLocales) {
                if (locale !== initialLocale) {
                    loadPromises.push(loadLanguage(locale));
                }
            }
            await Promise.allSettled(loadPromises);
        }
    };

    // 返回插件对象
    return {
        install: async (app: App) => {
            // 注册vue-i18n
            app.use(i18nInstance);

            // 提供插件选项
            app.provide(I18N_KEY, finalOptions);

            // 添加全局属性
            app.config.globalProperties.$i18nOptions = finalOptions;
            app.config.globalProperties.$setLanguage = setLanguage;
            app.config.globalProperties.$loadLanguage = loadLanguage;

            // 初始化语言
            await initializeLanguage();
        },
        i18n: i18nInstance,
        options: finalOptions,
        loadLanguage,
        setLanguage
    };
}

// 创建默认的i18n插件实例
export const i18nPlugin = createI18nPlugin();

/**
 * 安装i18n插件
 */
export async function setupI18n(app: App) {
    await i18nPlugin.install(app);
}

// 导出
export * from './composables';
export * from './types';
export * from './utils';

