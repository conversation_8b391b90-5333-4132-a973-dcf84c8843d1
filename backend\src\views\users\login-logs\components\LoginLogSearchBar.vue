<template>
  <div class="login-log-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item label="关键词">
        <el-input 
          v-model="searchForm.keyword" 
          placeholder="请输入用户ID/用户名/IP地址" 
          clearable
          style="width: 200px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="用户ID">
        <el-input 
          v-model="searchForm.user_id" 
          placeholder="请输入用户ID" 
          clearable
          style="width: 150px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="用户名">
        <el-input 
          v-model="searchForm.username" 
          placeholder="请输入用户名" 
          clearable
          style="width: 150px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="登录类型">
        <el-select 
          v-model="searchForm.type" 
          placeholder="请选择登录类型" 
          clearable 
          style="width: 120px;"
        >
          <el-option label="登录" :value="0" />
          <el-option label="退出" :value="1" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="登录状态">
        <el-select 
          v-model="searchForm.login_status" 
          placeholder="请选择状态" 
          clearable 
          style="width: 120px;"
        >
          <el-option label="成功" value="success" />
          <el-option label="失败" value="failure" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="IP地址">
        <el-input 
          v-model="searchForm.ip_address" 
          placeholder="请输入IP地址" 
          clearable
          style="width: 150px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="设备类型">
        <el-select 
          v-model="searchForm.device_type" 
          placeholder="请选择设备类型" 
          clearable 
          style="width: 120px;"
        >
          <el-option label="PC" value="PC" />
          <el-option label="Mobile" value="Mobile" />
          <el-option label="Tablet" value="Tablet" />
          <el-option label="Other" value="Other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="地理位置">
        <el-input 
          v-model="searchForm.location" 
          placeholder="请输入地理位置" 
          clearable
          style="width: 150px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="searchForm.start_date"
          type="datetime"
          placeholder="开始时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 180px;"
        />
      </el-form-item>
      
      <el-form-item label="结束时间">
        <el-date-picker
          v-model="searchForm.end_date"
          type="datetime"
          placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 180px;"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><RefreshRight /></el-icon>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive, watch } from 'vue';

// 搜索表单类型定义
export interface LoginLogSearchForm {
  keyword: string;
  user_id: string;
  username: string;
  type?: number;
  login_status: string;
  ip_address: string;
  device_type: string;
  location: string;
  start_date: string;
  end_date: string;
}

// Props
interface Props {
  modelValue?: LoginLogSearchForm;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    keyword: '',
    user_id: '',
    username: '',
    type: undefined,
    login_status: '',
    ip_address: '',
    device_type: '',
    location: '',
    start_date: '',
    end_date: '',
  })
});

// Emits
interface Emits {
  search: [params: LoginLogSearchForm];
  reset: [];
  refresh: [];
  'update:modelValue': [value: LoginLogSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<LoginLogSearchForm>({ ...props.modelValue });

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    user_id: '',
    username: '',
    type: undefined,
    login_status: '',
    ip_address: '',
    device_type: '',
    location: '',
    start_date: '',
    end_date: '',
  });
  emit('reset');
};

// 刷新
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.login-log-search-bar {
  margin-bottom: 16px;
}

.login-log-search-bar .el-form {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.login-log-search-bar .el-form-item {
  margin-bottom: 8px;
}
</style> 