package shortvideos

import (
	"frontapi/internal/models/shortvideos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ShortVideoCommentLikeRepository 短视频评论点赞数据访问接口
type ShortVideoCommentLikeRepository interface {
	base.ExtendedRepository[shortvideos.ShortVideoCommentLike]
}

// shortVideoCommentLikeRepository 短视频评论点赞数据访问实现
type shortVideoCommentLikeRepository struct {
	base.ExtendedRepository[shortvideos.ShortVideoCommentLike]
}

// NewShortVideoCommentLikeRepository 创建短视频评论点赞仓库实例
func NewShortVideoCommentLikeRepository(db *gorm.DB) ShortVideoCommentLikeRepository {
	return &shortVideoCommentLikeRepository{
		ExtendedRepository: base.NewExtendedRepository[shortvideos.ShortVideoCommentLike](db),
	}
}
