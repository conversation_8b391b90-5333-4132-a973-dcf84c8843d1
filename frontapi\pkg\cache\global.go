package cache

import (
	"sync"
	"time"

	cacheConfig "frontapi/config/cache"
	"frontapi/pkg/cache/types"
)

var (
	// 全局缓存管理器实例
	globalManager *Manager
	// 确保全局管理器只初始化一次的锁
	globalManagerLock sync.RWMutex
	// 标记全局管理器是否已初始化
	initialized bool
)

// GetGlobalManager 获取全局缓存管理器实例
func GetGlobalManager() *Manager {
	globalManagerLock.RLock()
	if globalManager != nil {
		defer globalManagerLock.RUnlock()
		return globalManager
	}
	globalManagerLock.RUnlock()

	// 需要初始化
	globalManagerLock.Lock()
	defer globalManagerLock.Unlock()

	// 双重检查
	if globalManager != nil {
		return globalManager
	}

	// 创建默认配置
	config := &cacheConfig.CacheConfig{
		DefaultAdapter: "redis",
		DefaultTTL:     time.Hour,
	}

	var err error
	globalManager, err = NewManager(config)
	if err != nil {
		// 出错时返回nil，让调用方处理错误
		return nil
	}

	initialized = true
	return globalManager
}

// SetGlobalManager 设置全局缓存管理器实例
func SetGlobalManager(manager *Manager) {
	globalManagerLock.Lock()
	defer globalManagerLock.Unlock()
	globalManager = manager
	initialized = true
}

// ResetGlobalManager 重置全局缓存管理器实例
func ResetGlobalManager() {
	globalManagerLock.Lock()
	defer globalManagerLock.Unlock()

	if globalManager != nil {
		// 尝试关闭现有管理器
		_ = globalManager.Close()
	}

	globalManager = nil
	initialized = false
}

// InitGlobalCache 初始化全局缓存管理器
func InitGlobalCache(manager *Manager) error {
	if IsGlobalCacheInitialized() {
		return types.ErrAdapterAlreadyExists
	}

	globalManagerLock.Lock()
	defer globalManagerLock.Unlock()

	globalManager = manager
	initialized = true
	return nil
}

// IsGlobalCacheInitialized 检查全局缓存管理器是否已初始化
func IsGlobalCacheInitialized() bool {
	globalManagerLock.RLock()
	defer globalManagerLock.RUnlock()
	return initialized
}
