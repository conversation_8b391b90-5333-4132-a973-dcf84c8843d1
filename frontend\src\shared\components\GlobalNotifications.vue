<template>
    <div v-if="notifications.length > 0" class="global-notifications">
        <div v-for="notification in notifications" :key="notification.id" :class="[
            'notification',
            `notification--${notification.type}`
        ]" @click="$emit('remove', notification.id)">
            <div class="notification__icon">
                <svg v-if="notification.type === 'success'" viewBox="0 0 24 24">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                </svg>
                <svg v-else-if="notification.type === 'error'" viewBox="0 0 24 24">
                    <path
                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                </svg>
                <svg v-else-if="notification.type === 'warning'" viewBox="0 0 24 24">
                    <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" />
                </svg>
                <svg v-else viewBox="0 0 24 24">
                    <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
                </svg>
            </div>
            <div class="notification__content">
                <div class="notification__title">{{ notification.title }}</div>
                <div class="notification__message">{{ notification.message }}</div>
            </div>
            <button class="notification__close" @click.stop="$emit('remove', notification.id)">
                ×
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
interface Notification {
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    duration?: number
}

defineProps<{
    notifications: Notification[]
}>()

defineEmits<{
    (e: 'remove', id: string): void
}>()
</script>

<style scoped>
.global-notifications {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.notification {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    animation: slideInRight 0.3s ease;
}

.notification:hover {
    transform: translateX(-5px);
}

.notification--success {
    background: #f0f9ff;
    border-left: 4px solid #10b981;
    color: #065f46;
}

.notification--error {
    background: #fef2f2;
    border-left: 4px solid #ef4444;
    color: #991b1b;
}

.notification--warning {
    background: #fffbeb;
    border-left: 4px solid #f59e0b;
    color: #92400e;
}

.notification--info {
    background: #f0f9ff;
    border-left: 4px solid #3b82f6;
    color: #1e40af;
}

.notification__icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    flex-shrink: 0;
}

.notification__icon svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.notification__content {
    flex: 1;
}

.notification__title {
    font-weight: 600;
    margin-bottom: 4px;
}

.notification__message {
    font-size: 14px;
    opacity: 0.8;
}

.notification__close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.notification__close:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 深色主题适配 */
:global(.theme-dark) .notification--success {
    background: #064e3b;
    color: #a7f3d0;
}

:global(.theme-dark) .notification--error {
    background: #7f1d1d;
    color: #fca5a5;
}

:global(.theme-dark) .notification--warning {
    background: #78350f;
    color: #fcd34d;
}

:global(.theme-dark) .notification--info {
    background: #1e3a8a;
    color: #93c5fd;
}

@media (max-width: 768px) {
    .global-notifications {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
</style>