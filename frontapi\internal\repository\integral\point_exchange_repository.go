package integral

import (
	"gorm.io/gorm"

	model "frontapi/internal/models/integral"
	"frontapi/internal/repository/base"
)

// PointExchangeRepository 积分兑换数据访问接口
type PointExchangeRepository interface {
	base.ExtendedRepository[model.PointExchange]
}

type pointExchangeRepository struct {
	base.ExtendedRepository[model.PointExchange]
}

func NewPointExchangeRepository(db *gorm.DB) PointExchangeRepository {
	return &pointExchangeRepository{
		ExtendedRepository: base.NewExtendedRepository[model.PointExchange](db),
	}
}
