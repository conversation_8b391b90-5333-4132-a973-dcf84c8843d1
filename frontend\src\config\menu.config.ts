export interface MenuItem {
    label: string;
    icon: string;
    route?: string;
    showMobile: boolean;
    items?: MenuItem[];
    // MegaMenu 兼容属性
    command?: () => void;
    url?: string;
    target?: string;
    separator?: boolean;
    disabled?: boolean;
    visible?: boolean;
    style?: any;
    class?: string;
}

export interface MenuConfig {
    items: MenuItem[];
}

// MegaMenu 格式的菜单项
export interface MegaMenuItem {
    label: string;
    icon?: string;
    items?: MegaMenuItem[][];
    command?: () => void;
    url?: string;
    route?: string;
    target?: string;
    separator?: boolean;
    disabled?: boolean;
    visible?: boolean;
    style?: any;
    class?: string;
}

export const menuItems: MenuItem[] = [
    {
        label: 'common.home',
        icon: 'pi pi-home',
        route: '/',
        showMobile: false
    },
    {
        label: 'common.categories',
        icon: 'pi pi-th-large',
        showMobile: false,
        items: [
            {
                label: 'common.allVideos',
                icon: 'pi pi-video',
                route: '/videos',
                showMobile: false
            },
            {
                label: 'common.movies',
                icon: 'pi pi-play-circle',
                route: '/videos/movies',
                showMobile: false
            },
            {
                label: 'common.series',
                icon: 'pi pi-list',
                route: '/videos/series',
                showMobile: false
            },
            {
                label: 'common.anime',
                icon: 'pi pi-star',
                route: '/videos/anime',
                showMobile: false
            },
            {
                label: 'common.documentaries',
                icon: 'pi pi-book',
                route: '/videos/documentaries',
                showMobile: false
            }
        ]
    },
    {
        label: 'common.videos',
        icon: 'pi pi-video',
        route: '/videos',
        showMobile: true
    },
    {
        label: 'common.shorts',
        icon: 'pi pi-mobile',
        route: '/shorts',
        showMobile: true
    },
    {
        label: 'common.community',
        icon: 'pi pi-users',
        route: '/community',
        showMobile: true
    },
    {
        label: 'common.creator',
        icon: 'pi pi-star',
        route: '/creators',
        showMobile: false
    },
    {
        label: 'common.pictures',
        icon: 'pi pi-image',
        route: '/pictures',
        showMobile: true
    },
    {
        label: 'common.manga',
        icon: 'pi pi-book',
        route: '/manga',
        showMobile: true
    },
    {
        label: 'common.ebook',
        icon: 'pi pi-file-pdf',
        route: '/ebooks',
        showMobile: true
    }
]

// 转换为 MegaMenu 格式的函数 - 单列显示
export function convertToMegaMenuItems(items: MenuItem[]): MegaMenuItem[] {
    return items.map(item => {
        const megaItem: MegaMenuItem = {
            label: item.label,
            icon: item.icon,
            route: item.route,
            visible: true
        };

        // 如果有子菜单，转换为单列格式
        if (item.items && item.items.length > 0) {
            // 将所有子菜单项放在一个组中，实现单列显示
            const singleGroup = item.items.map(subItem => ({
                label: subItem.label,
                icon: subItem.icon,
                route: subItem.route,
                visible: true
            }));

            megaItem.items = [singleGroup]; // 只有一个组，实现单列显示
        }

        return megaItem;
    });
}

// 导出 MegaMenu 格式的菜单项
export const megaMenuItems = convertToMegaMenuItems(menuItems);


