export interface MenuItem {
    label: string;
    icon: string;
    route?: string;
    showMobile: boolean;
    items?: MenuItem[];
}

export interface MenuConfig {
    items: MenuItem[];
}

export const menuItems: MenuItem[] = [
    {
        label: 'common.home',
        icon: 'pi pi-home',
        route: '/',
        showMobile: false
    },
    {
        label: 'common.categories',
        icon: 'pi pi-th-large',
        showMobile: false,
        items: [
            {
                label: 'common.allVideos',
                icon: 'pi pi-video',
                route: '/videos',
                showMobile: false
            },
            {
                label: 'common.movies',
                icon: 'pi pi-play-circle',
                route: '/videos/movies',
                showMobile: false
            },
            {
                label: 'common.series',
                icon: 'pi pi-list',
                route: '/videos/series',
                showMobile: false
            },
            {
                label: 'common.anime',
                icon: 'pi pi-star',
                route: '/videos/anime',
                showMobile: false
            },
            {
                label: 'common.documentaries',
                icon: 'pi pi-book',
                route: '/videos/documentaries',
                showMobile: false
            }
        ]
    },
    {
        label: 'common.videos',
        icon: 'pi pi-video',
        route: '/videos',
        showMobile: true
    },
    {
        label: 'common.shorts',
        icon: 'pi pi-mobile',
        route: '/shorts',
        showMobile: true
    },
    {
        label: 'common.community',
        icon: 'pi pi-users',
        route: '/community',
        showMobile: true
    },
    {
        label: 'common.creator',
        icon: 'pi pi-star',
        route: '/creators',
        showMobile: false
    },
    {
        label: 'common.pictures',
        icon: 'pi pi-image',
        route: '/pictures',
        showMobile: true
    },
    {
        label: 'common.manga',
        icon: 'pi pi-book',
        route: '/manga',
        showMobile: true
    },
    {
        label: 'common.ebook',
        icon: 'pi pi-file-pdf',
        route: '/ebooks',
        showMobile: true
    }
]


