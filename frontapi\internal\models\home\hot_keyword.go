package home

import (
	"frontapi/internal/models"
)

// HotKeyword represents a hot search keyword for the home page
type HotKeyword struct {
	models.BaseModel
	Keyword     string `json:"keyword" gorm:"not null;comment:关键词"`
	SearchCount uint64 `json:"search_count" gorm:"default:0;comment:搜索次数"`
	Category    string `json:"category" gorm:"comment:分类"`
	Sort        int    `json:"sort" gorm:"default:0;comment:排序"`
	Status      int8   `json:"status" gorm:"default:1;comment:状态：0-禁用，1-正常"`
}

// TableName 指定表名
func (HotKeyword) TableName() string {
	return "ly_hot_keywords"
}
