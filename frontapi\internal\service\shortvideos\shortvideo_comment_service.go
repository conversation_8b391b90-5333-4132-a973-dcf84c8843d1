package shortvideos

import (
	"context"
	"fmt"

	"frontapi/internal/models/shortvideos"
	repo "frontapi/internal/repository/shortvideos"
	"frontapi/internal/service/base"
	"frontapi/internal/service/base/extlike"
)

// CommentWithLikeInfo 带点赞信息的评论
type CommentWithLikeInfo struct {
	*shortvideos.ShortVideoComment
	IsLiked   bool  `json:"is_liked"`
	LikeCount int64 `json:"like_count"`
}

// UserCommentStats 用户评论统计
type UserCommentStats struct {
	TotalComments int64 `json:"total_comments"`
	TotalLikes    int64 `json:"total_likes"`
}

// ShortVideoCommentService 短视频评论服务接口
type ShortVideoCommentService interface {
	base.IExtendedService[shortvideos.ShortVideoComment]
	base.ILikeMixin

	// 评论特有的方法
	GetCommentsWithBatchLikeInfo(ctx context.Context, commentIDs []string, userID string) ([]*CommentWithLikeInfo, error)
	GetHotComments(ctx context.Context, videoID string, limit int) ([]*CommentWithLikeInfo, error)
	GetUserCommentStats(ctx context.Context, userID string) (*UserCommentStats, error)
	GetReplies(ctx context.Context, parentID string, page, pageSize int) ([]*CommentWithLikeInfo, int64, error)

	// 点赞相关操作
	LikeComment(ctx context.Context, userID, commentID string) error
	UnlikeComment(ctx context.Context, userID, commentID string) error
	IsCommentLiked(ctx context.Context, userID, commentID string) (bool, error)
	GetCommentLikeCount(ctx context.Context, commentID string) (int64, error)
	BatchGetCommentLikeInfo(ctx context.Context, userID string, commentIDs []string) (map[string]bool, map[string]int64, error)
}

// shortVideoCommentService 短视频评论服务实现
type shortVideoCommentService struct {
	*base.ExtendedService[shortvideos.ShortVideoComment]
	base.ILikeMixin // 嵌入点赞功能混入

	commentRepo     repo.ShortVideoCommentRepository
	shortVideoRepo  repo.ShortVideoRepository
	commentLikeRepo repo.ShortVideoCommentLikeRepository
}

// NewShortVideoCommentService 创建短视频评论服务实例
// @param commentRepo 评论仓库
// @param shortVideoRepo 短视频仓库
// @param commentLikeRepo 评论点赞仓库
// @return ShortVideoCommentService 短视频评论服务接口
func NewShortVideoCommentService(
	commentRepo repo.ShortVideoCommentRepository,
	shortVideoRepo repo.ShortVideoRepository,
	commentLikeRepo repo.ShortVideoCommentLikeRepository,
) ShortVideoCommentService {
	// 创建点赞服务配置
	likeConfig := extlike.DefaultConfig()
	likeConfig.Strategy = extlike.RedisOnly // 使用Redis作为存储
	likeConfig.Redis.Enabled = true
	likeConfig.Redis.UseSystem = true
	likeConfig.MongoDB.Enabled = false

	// 创建点赞服务
	likeService, err := extlike.NewExtendedLikeService(likeConfig)
	if err != nil {
		// 如果创建失败，使用空的点赞服务
		// 在生产环境中应该记录错误日志
		likeService = nil
	}

	// 创建点赞混入
	var likeMixin base.ILikeMixin
	if likeService != nil {
		likeMixin = base.NewLikeMixin(likeService, "shortvideo_comment")
	}

	return &shortVideoCommentService{
		ExtendedService: base.NewExtendedService[shortvideos.ShortVideoComment](commentRepo, "short_video_comment"),
		ILikeMixin:      likeMixin,
		commentRepo:     commentRepo,
		shortVideoRepo:  shortVideoRepo,
		commentLikeRepo: commentLikeRepo,
	}
}

// LikeComment 点赞评论
func (s *shortVideoCommentService) LikeComment(ctx context.Context, userID, commentID string) error {
	if s.ILikeMixin == nil {
		return fmt.Errorf("点赞服务未初始化")
	}

	// 调用点赞混入的方法
	err := s.ILikeMixin.Like(ctx, userID, commentID)
	if err != nil {
		return fmt.Errorf("点赞评论失败: %w", err)
	}

	// 异步更新评论的点赞数量统计
	go func() {
		s.updateCommentLikeCount(context.Background(), commentID)
	}()

	return nil
}

// UnlikeComment 取消点赞评论
func (s *shortVideoCommentService) UnlikeComment(ctx context.Context, userID, commentID string) error {
	if s.ILikeMixin == nil {
		return fmt.Errorf("点赞服务未初始化")
	}

	// 调用点赞混入的方法
	err := s.ILikeMixin.Unlike(ctx, userID, commentID)
	if err != nil {
		return fmt.Errorf("取消点赞评论失败: %w", err)
	}

	// 异步更新评论的点赞数量统计
	go func() {
		s.updateCommentLikeCount(context.Background(), commentID)
	}()

	return nil
}

// IsCommentLiked 检查评论是否已被点赞
func (s *shortVideoCommentService) IsCommentLiked(ctx context.Context, userID, commentID string) (bool, error) {
	if s.ILikeMixin == nil {
		return false, fmt.Errorf("点赞服务未初始化")
	}

	return s.ILikeMixin.IsLiked(ctx, userID, commentID)
}

// GetCommentLikeCount 获取评论点赞数量
func (s *shortVideoCommentService) GetCommentLikeCount(ctx context.Context, commentID string) (int64, error) {
	if s.ILikeMixin == nil {
		return 0, fmt.Errorf("点赞服务未初始化")
	}

	return s.ILikeMixin.GetLikeCount(ctx, commentID)
}

// BatchGetCommentLikeInfo 批量获取评论点赞信息
func (s *shortVideoCommentService) BatchGetCommentLikeInfo(ctx context.Context, userID string, commentIDs []string) (map[string]bool, map[string]int64, error) {
	if s.ILikeMixin == nil {
		return nil, nil, fmt.Errorf("点赞服务未初始化")
	}

	// 批量获取点赞状态
	likeStatuses, err := s.ILikeMixin.BatchGetLikeStatus(ctx, userID, commentIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("批量获取点赞状态失败: %w", err)
	}

	// 批量获取点赞数量
	likeCounts, err := s.ILikeMixin.BatchGetLikeCounts(ctx, commentIDs)
	if err != nil {
		return nil, nil, fmt.Errorf("批量获取点赞数量失败: %w", err)
	}

	return likeStatuses, likeCounts, nil
}

// updateCommentLikeCount 更新评论点赞数量统计（异步调用）
func (s *shortVideoCommentService) updateCommentLikeCount(ctx context.Context, commentID string) {
	if s.ILikeMixin == nil {
		return
	}

	// 获取最新的点赞数量
	count, err := s.ILikeMixin.GetLikeCount(ctx, commentID)
	if err != nil {
		// 在生产环境中应该记录错误日志
		return
	}

	// 更新数据库中的点赞数量
	err = s.commentRepo.UpdateLikeCount(ctx, commentID, int(count))
	if err != nil {
		// 在生产环境中应该记录错误日志
		return
	}
}

// GetCommentsWithBatchLikeInfo 批量获取带点赞信息的评论
func (s *shortVideoCommentService) GetCommentsWithBatchLikeInfo(ctx context.Context, commentIDs []string, userID string) ([]*CommentWithLikeInfo, error) {
	if len(commentIDs) == 0 {
		return []*CommentWithLikeInfo{}, nil
	}

	// 批量获取评论基本信息
	comments, err := s.FindByIDs(ctx, commentIDs)
	if err != nil {
		return nil, fmt.Errorf("批量获取评论失败: %w", err)
	}

	// 批量获取点赞信息
	var likeStatuses map[string]bool
	var likeCounts map[string]int64

	if s.ILikeMixin != nil {
		likeStatuses, likeCounts, err = s.BatchGetCommentLikeInfo(ctx, userID, commentIDs)
		if err != nil {
			// 如果点赞服务失败，使用默认值
			likeStatuses = make(map[string]bool)
			likeCounts = make(map[string]int64)
			for _, id := range commentIDs {
				likeStatuses[id] = false
				likeCounts[id] = 0
			}
		}
	} else {
		// 如果没有点赞服务，使用默认值
		likeStatuses = make(map[string]bool)
		likeCounts = make(map[string]int64)
		for _, id := range commentIDs {
			likeStatuses[id] = false
			likeCounts[id] = 0
		}
	}

	// 组装结果
	result := make([]*CommentWithLikeInfo, len(comments))
	for i, comment := range comments {
		commentID := comment.ID
		result[i] = &CommentWithLikeInfo{
			ShortVideoComment: comment,
			IsLiked:           likeStatuses[commentID],
			LikeCount:         likeCounts[commentID],
		}
	}

	return result, nil
}

// GetHotComments 获取热门评论
func (s *shortVideoCommentService) GetHotComments(ctx context.Context, videoID string, limit int) ([]*CommentWithLikeInfo, error) {
	// 如果有点赞服务，优先从Redis获取热门排行
	if s.ILikeMixin != nil {
		hotCommentIDs, err := s.ILikeMixin.GetHotRanking(ctx, limit)
		if err == nil && len(hotCommentIDs) > 0 {
			// 过滤出属于指定视频的评论
			videoCommentIDs := make([]string, 0)
			for _, commentID := range hotCommentIDs {
				comment, err := s.ExtendedService.GetByID(ctx, commentID, false)
				if err == nil && comment.ShortID.String == videoID {
					videoCommentIDs = append(videoCommentIDs, commentID)
					if len(videoCommentIDs) >= limit {
						break
					}
				}
			}

			if len(videoCommentIDs) > 0 {
				return s.GetCommentsWithBatchLikeInfo(ctx, videoCommentIDs, "")
			}
		}
	}

	// 备用方案：从数据库获取
	return s.getHotCommentsFromDB(ctx, videoID, limit)
}

// getHotCommentsFromDB 从数据库获取热门评论（备用方案）
func (s *shortVideoCommentService) getHotCommentsFromDB(ctx context.Context, videoID string, limit int) ([]*CommentWithLikeInfo, error) {
	// 使用原生SQL查询，按点赞数排序
	sql := `
		SELECT c.*, COALESCE(like_counts.count, 0) as like_count
		FROM short_video_comments c
		LEFT JOIN (
			SELECT comment_id, COUNT(*) as count
			FROM short_video_comment_likes
			GROUP BY comment_id
		) like_counts ON c.id = like_counts.comment_id
		WHERE c.video_id = ? AND c.status = 1
		ORDER BY like_count DESC, c.created_at DESC
		LIMIT ?
	`

	var results []struct {
		shortvideos.ShortVideoComment
		LikeCount int64 `gorm:"column:like_count"`
	}

	err := s.commentRepo.GetDB().Raw(sql, videoID, limit).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("查询热门评论失败: %w", err)
	}

	// 转换结果
	comments := make([]*CommentWithLikeInfo, len(results))
	for i, result := range results {
		comments[i] = &CommentWithLikeInfo{
			ShortVideoComment: &result.ShortVideoComment,
			IsLiked:           false, // 未指定用户，默认为false
			LikeCount:         result.LikeCount,
		}
	}

	return comments, nil
}

// GetUserCommentStats 获取用户评论统计
// @param ctx 上下文
// @param userID 用户ID
// @return *UserCommentStats 用户评论统计
// @return error 错误信息
func (s *shortVideoCommentService) GetUserCommentStats(ctx context.Context, userID string) (*UserCommentStats, error) {
	// 获取用户评论总数
	totalComments, err := s.Count(ctx, map[string]interface{}{
		"user_id": userID,
	})
	if err != nil {
		return nil, fmt.Errorf("获取用户评论总数失败: %w", err)
	}

	// 计算用户评论获得的总点赞数
	var totalLikes int64
	if totalComments > 0 && s.ILikeMixin != nil {
		// 获取用户所有评论
		userComments, err := s.commentRepo.FindByCondition(ctx, map[string]interface{}{
			"user_id": userID,
		}, "created_at DESC")
		if err != nil {
			return nil, fmt.Errorf("获取用户评论失败: %w", err)
		}

		// 批量获取点赞数量
		commentIDs := make([]string, len(userComments))
		for i, comment := range userComments {
			commentIDs[i] = comment.ID
		}

		likeCounts, err := s.ILikeMixin.BatchGetLikeCounts(ctx, commentIDs)
		if err == nil {
			for _, count := range likeCounts {
				totalLikes += count
			}
		}
	}

	return &UserCommentStats{
		TotalComments: totalComments,
		TotalLikes:    totalLikes,
	}, nil
}

// GetReplies 获取评论回复列表
// @param ctx 上下文
// @param parentID 父评论ID
// @param page 页码
// @param pageSize 每页大小
// @return []*CommentWithLikeInfo 带点赞信息的评论列表
// @return int64 总数
// @return error 错误信息
func (s *shortVideoCommentService) GetReplies(ctx context.Context, parentID string, page, pageSize int) ([]*CommentWithLikeInfo, int64, error) {
	// 查询条件
	condition := map[string]interface{}{
		"parent_id": parentID,
		"status":    1, // 只查询正常状态的评论
	}

	// 获取回复总数
	total, err := s.Count(ctx, condition)
	if err != nil {
		return nil, 0, fmt.Errorf("获取回复总数失败: %w", err)
	}

	// 没有回复时直接返回空列表
	if total == 0 {
		return []*CommentWithLikeInfo{}, 0, nil
	}

	// 分页查询回复列表
	replies, _, err := s.commentRepo.List(ctx, condition, "created_at DESC", page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("分页查询回复列表失败: %w", err)
	}

	// 如果没有回复，返回空列表
	if len(replies) == 0 {
		return []*CommentWithLikeInfo{}, total, nil
	}

	// 提取回复ID列表
	replyIDs := make([]string, len(replies))
	for i, reply := range replies {
		replyIDs[i] = reply.ID
	}

	// 获取点赞信息
	var likeStatuses map[string]bool
	var likeCounts map[string]int64

	if s.ILikeMixin != nil {
		// 这里传入空的用户ID，因为后台管理不需要判断当前用户是否点赞
		likeStatuses, likeCounts, err = s.BatchGetCommentLikeInfo(ctx, "", replyIDs)
		if err != nil {
			// 如果获取点赞信息失败，使用默认值
			likeStatuses = make(map[string]bool)
			likeCounts = make(map[string]int64)
			for _, id := range replyIDs {
				likeStatuses[id] = false
				likeCounts[id] = 0
			}
		}
	} else {
		// 如果没有点赞服务，使用默认值
		likeStatuses = make(map[string]bool)
		likeCounts = make(map[string]int64)
		for _, id := range replyIDs {
			likeStatuses[id] = false
			likeCounts[id] = 0
		}
	}

	// 组装结果
	result := make([]*CommentWithLikeInfo, len(replies))
	for i, reply := range replies {
		replyID := reply.ID
		result[i] = &CommentWithLikeInfo{
			ShortVideoComment: reply,
			IsLiked:           likeStatuses[replyID],
			LikeCount:         likeCounts[replyID],
		}
	}

	return result, total, nil
}
