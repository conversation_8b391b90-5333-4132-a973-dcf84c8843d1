package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extfollow/types"
)

// MongoConfig MongoDB配置
type MongoConfig struct {
	Database   string `json:"database"`
	Collection string `json:"collection"`
}

// MongoAdapter MongoDB适配器
type MongoAdapter struct {
	client     *mongo.Client
	database   *mongo.Database
	collection *mongo.Collection
	config     *MongoConfig
}

// NewMongoAdapter 创建MongoDB适配器
func NewMongoAdapter(client *mongo.Client, database *mongo.Database, config *MongoConfig) (*MongoAdapter, error) {
	if client == nil {
		return nil, fmt.Errorf("MongoDB客户端不能为空")
	}
	if database == nil {
		return nil, fmt.Errorf("MongoDB数据库不能为空")
	}
	if config == nil {
		config = &MongoConfig{
			Database:   "frontapi_follow",
			Collection: "follows",
		}
	}

	collection := database.Collection(config.Collection)

	return &MongoAdapter{
		client:     client,
		database:   database,
		collection: collection,
		config:     config,
	}, nil
}

// Follow 关注用户
func (m *MongoAdapter) Follow(ctx context.Context, followerID, followeeID string) error {
	record := &types.FollowRecord{
		FollowerID: followerID,
		FolloweeID: followeeID,
		Timestamp:  time.Now(),
		Status:     "following",
		Version:    1,
	}

	filter := bson.M{
		"follower_id": followerID,
		"followee_id": followeeID,
	}

	update := bson.M{
		"$set": record,
		"$inc": bson.M{"version": 1},
	}

	opts := options.Update().SetUpsert(true)
	_, err := m.collection.UpdateOne(ctx, filter, update, opts)
	return err
}

// Unfollow 取消关注
func (m *MongoAdapter) Unfollow(ctx context.Context, followerID, followeeID string) error {
	filter := bson.M{
		"follower_id": followerID,
		"followee_id": followeeID,
	}

	update := bson.M{
		"$set": bson.M{
			"status":    "unfollowed",
			"timestamp": time.Now(),
		},
		"$inc": bson.M{"version": 1},
	}

	_, err := m.collection.UpdateOne(ctx, filter, update)
	return err
}

// IsFollowing 检查是否已关注
func (m *MongoAdapter) IsFollowing(ctx context.Context, followerID, followeeID string) (bool, error) {
	filter := bson.M{
		"follower_id": followerID,
		"followee_id": followeeID,
		"status":      "following",
	}

	count, err := m.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetFollowerCount 获取粉丝数量
func (m *MongoAdapter) GetFollowerCount(ctx context.Context, userID string) (int64, error) {
	filter := bson.M{
		"followee_id": userID,
		"status":      "following",
	}

	return m.collection.CountDocuments(ctx, filter)
}

// GetFollowingCount 获取关注数量
func (m *MongoAdapter) GetFollowingCount(ctx context.Context, userID string) (int64, error) {
	filter := bson.M{
		"follower_id": userID,
		"status":      "following",
	}

	return m.collection.CountDocuments(ctx, filter)
}

// BatchFollow 批量关注
func (m *MongoAdapter) BatchFollow(ctx context.Context, operations []*types.FollowOperation) error {
	var models []mongo.WriteModel

	for _, op := range operations {
		if op.Action == "follow" {
			filter := bson.M{
				"follower_id": op.FollowerID,
				"followee_id": op.FolloweeID,
			}

			update := bson.M{
				"$set": bson.M{
					"follower_id": op.FollowerID,
					"followee_id": op.FolloweeID,
					"timestamp":   op.Timestamp,
					"status":      "following",
					"metadata":    op.Metadata,
				},
				"$inc": bson.M{"version": 1},
			}

			model := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update).
				SetUpsert(true)

			models = append(models, model)
		}
	}

	if len(models) == 0 {
		return nil
	}

	_, err := m.collection.BulkWrite(ctx, models)
	return err
}

// BatchUnfollow 批量取消关注
func (m *MongoAdapter) BatchUnfollow(ctx context.Context, operations []*types.FollowOperation) error {
	var models []mongo.WriteModel

	for _, op := range operations {
		if op.Action == "unfollow" {
			filter := bson.M{
				"follower_id": op.FollowerID,
				"followee_id": op.FolloweeID,
			}

			update := bson.M{
				"$set": bson.M{
					"status":    "unfollowed",
					"timestamp": op.Timestamp,
				},
				"$inc": bson.M{"version": 1},
			}

			model := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update)

			models = append(models, model)
		}
	}

	if len(models) == 0 {
		return nil
	}

	_, err := m.collection.BulkWrite(ctx, models)
	return err
}

// BatchGetFollowStatus 批量获取关注状态
func (m *MongoAdapter) BatchGetFollowStatus(ctx context.Context, followerID string, userIDs []string) (map[string]bool, error) {
	result := make(map[string]bool)

	// 初始化所有状态为false
	for _, userID := range userIDs {
		result[userID] = false
	}

	filter := bson.M{
		"follower_id": followerID,
		"followee_id": bson.M{"$in": userIDs},
		"status":      "following",
	}

	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var record types.FollowRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		result[record.FolloweeID] = true
	}

	return result, cursor.Err()
}

// BatchGetFollowerCounts 批量获取粉丝数量
func (m *MongoAdapter) BatchGetFollowerCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	result := make(map[string]int64)

	pipeline := []bson.M{
		{
			"$match": bson.M{
				"followee_id": bson.M{"$in": userIDs},
				"status":      "following",
			},
		},
		{
			"$group": bson.M{
				"_id":   "$followee_id",
				"count": bson.M{"$sum": 1},
			},
		},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var doc struct {
			ID    string `bson:"_id"`
			Count int64  `bson:"count"`
		}
		if err := cursor.Decode(&doc); err != nil {
			continue
		}
		result[doc.ID] = doc.Count
	}

	// 确保所有请求的用户ID都有结果（默认为0）
	for _, userID := range userIDs {
		if _, exists := result[userID]; !exists {
			result[userID] = 0
		}
	}

	return result, cursor.Err()
}

// BatchGetFollowingCounts 批量获取关注数量
func (m *MongoAdapter) BatchGetFollowingCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	result := make(map[string]int64)

	pipeline := []bson.M{
		{
			"$match": bson.M{
				"follower_id": bson.M{"$in": userIDs},
				"status":      "following",
			},
		},
		{
			"$group": bson.M{
				"_id":   "$follower_id",
				"count": bson.M{"$sum": 1},
			},
		},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var doc struct {
			ID    string `bson:"_id"`
			Count int64  `bson:"count"`
		}
		if err := cursor.Decode(&doc); err != nil {
			continue
		}
		result[doc.ID] = doc.Count
	}

	// 确保所有请求的用户ID都有结果（默认为0）
	for _, userID := range userIDs {
		if _, exists := result[userID]; !exists {
			result[userID] = 0
		}
	}

	return result, cursor.Err()
}

// GetUserFollowers 获取用户粉丝列表
func (m *MongoAdapter) GetUserFollowers(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	filter := bson.M{
		"followee_id": userID,
		"status":      "following",
	}

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSkip(int64(offset)).
		SetSort(bson.M{"timestamp": -1})

	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.FollowRecord
	for cursor.Next(ctx) {
		var record types.FollowRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, cursor.Err()
}

// GetUserFollowing 获取用户关注列表
func (m *MongoAdapter) GetUserFollowing(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	filter := bson.M{
		"follower_id": userID,
		"status":      "following",
	}

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSkip(int64(offset)).
		SetSort(bson.M{"timestamp": -1})

	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.FollowRecord
	for cursor.Next(ctx) {
		var record types.FollowRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, cursor.Err()
}

// GetFollowHistory 获取关注历史
func (m *MongoAdapter) GetFollowHistory(ctx context.Context, userID string, timeRange *types.TimeRange) ([]*types.FollowRecord, error) {
	filter := bson.M{
		"follower_id": userID,
	}

	if timeRange != nil {
		filter["timestamp"] = bson.M{
			"$gte": timeRange.Start,
			"$lte": timeRange.End,
		}
	}

	opts := options.Find().SetSort(bson.M{"timestamp": -1})

	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.FollowRecord
	for cursor.Next(ctx) {
		var record types.FollowRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, cursor.Err()
}

// GetMutualFollows 获取互相关注的用户
func (m *MongoAdapter) GetMutualFollows(ctx context.Context, userID1, userID2 string) ([]*types.FollowRecord, error) {
	// 查找用户1关注的人，并且这些人也关注用户1
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"follower_id": userID1,
				"status":      "following",
			},
		},
		{
			"$lookup": bson.M{
				"from": m.config.Collection,
				"let":  bson.M{"followee": "$followee_id"},
				"pipeline": []bson.M{
					{
						"$match": bson.M{
							"$expr": bson.M{
								"$and": []bson.M{
									{"$eq": []string{"$follower_id", "$$followee"}},
									{"$eq": []string{"$followee_id", userID1}},
									{"$eq": []string{"$status", "following"}},
								},
							},
						},
					},
				},
				"as": "mutual",
			},
		},
		{
			"$match": bson.M{
				"mutual": bson.M{"$ne": []interface{}{}},
			},
		},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.FollowRecord
	for cursor.Next(ctx) {
		var record types.FollowRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, cursor.Err()
}

// UpdateInfluenceRank 更新影响力排名
func (m *MongoAdapter) UpdateInfluenceRank(ctx context.Context, userID string, score float64) error {
	// 创建影响力排名集合
	rankCollection := m.database.Collection("influence_ranks")

	filter := bson.M{"user_id": userID}
	update := bson.M{
		"$set": bson.M{
			"user_id":    userID,
			"score":      score,
			"updated_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := rankCollection.UpdateOne(ctx, filter, update, opts)
	return err
}

// GetInfluenceRanking 获取影响力排名
func (m *MongoAdapter) GetInfluenceRanking(ctx context.Context, limit int) ([]string, error) {
	rankCollection := m.database.Collection("influence_ranks")

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSort(bson.M{"score": -1})

	cursor, err := rankCollection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var userIDs []string
	for cursor.Next(ctx) {
		var doc struct {
			UserID string `bson:"user_id"`
		}
		if err := cursor.Decode(&doc); err != nil {
			continue
		}
		userIDs = append(userIDs, doc.UserID)
	}

	return userIDs, cursor.Err()
}

// GetInfluenceRankingWithScores 获取带分数的影响力排名
func (m *MongoAdapter) GetInfluenceRankingWithScores(ctx context.Context, limit int) (map[string]float64, error) {
	rankCollection := m.database.Collection("influence_ranks")

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSort(bson.M{"score": -1})

	cursor, err := rankCollection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	result := make(map[string]float64)
	for cursor.Next(ctx) {
		var doc struct {
			UserID string  `bson:"user_id"`
			Score  float64 `bson:"score"`
		}
		if err := cursor.Decode(&doc); err != nil {
			continue
		}
		result[doc.UserID] = doc.Score
	}

	return result, cursor.Err()
}

// GetUserFollowStats 获取用户关注统计
func (m *MongoAdapter) GetUserFollowStats(ctx context.Context, userID string) (*types.UserFollowStats, error) {
	// 获取关注数量
	followingCount, err := m.GetFollowingCount(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取粉丝数量
	followerCount, err := m.GetFollowerCount(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取第一次和最后一次关注时间
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"follower_id": userID,
				"status":      "following",
			},
		},
		{
			"$group": bson.M{
				"_id":           nil,
				"first_follow":  bson.M{"$min": "$timestamp"},
				"last_follow":   bson.M{"$max": "$timestamp"},
				"total_follows": bson.M{"$sum": 1},
			},
		},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	stats := &types.UserFollowStats{
		UserID:      userID,
		FollowCount: followingCount,
		FansCount:   followerCount,
		MutualCount: 0, // 需要额外计算
		Ratio:       0,
	}

	if cursor.Next(ctx) {
		var result struct {
			FirstFollow  time.Time `bson:"first_follow"`
			LastFollow   time.Time `bson:"last_follow"`
			TotalFollows int64     `bson:"total_follows"`
		}
		if err := cursor.Decode(&result); err == nil {
			stats.FirstFollowAt = &result.FirstFollow
			stats.LastFollowAt = &result.LastFollow
		}
	}

	// 计算互关比例（简化版本，实际应该更复杂）
	if followingCount > 0 {
		stats.Ratio = float64(followerCount) / float64(followingCount)
	}

	return stats, nil
}

// GetFollowTrends 获取关注趋势
func (m *MongoAdapter) GetFollowTrends(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.FollowTrend, error) {
	matchStage := bson.M{
		"status": "following",
	}

	if timeRange != nil {
		matchStage["timestamp"] = bson.M{
			"$gte": timeRange.Start,
			"$lte": timeRange.End,
		}
	}

	pipeline := []bson.M{
		{"$match": matchStage},
		{
			"$group": bson.M{
				"_id": bson.M{
					"year":  bson.M{"$year": "$timestamp"},
					"month": bson.M{"$month": "$timestamp"},
					"day":   bson.M{"$dayOfMonth": "$timestamp"},
				},
				"follow_count": bson.M{"$sum": 1},
				"date":         bson.M{"$first": "$timestamp"},
			},
		},
		{
			"$sort": bson.M{"date": -1},
		},
		{
			"$limit": limit,
		},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var trends []*types.FollowTrend
	for cursor.Next(ctx) {
		var result struct {
			Date        time.Time `bson:"date"`
			FollowCount int64     `bson:"follow_count"`
		}
		if err := cursor.Decode(&result); err != nil {
			continue
		}

		trend := &types.FollowTrend{
			Date:        result.Date,
			FollowCount: result.FollowCount,
			GrowthRate:  0, // 需要与前一天比较计算
		}
		trends = append(trends, trend)
	}

	return trends, cursor.Err()
}

// InvalidateCache 缓存失效（MongoDB不需要实现）
func (m *MongoAdapter) InvalidateCache(ctx context.Context, keys ...string) error {
	// MongoDB适配器不需要缓存失效
	return nil
}

// WarmupCache 缓存预热（MongoDB不需要实现）
func (m *MongoAdapter) WarmupCache(ctx context.Context, userIDs []string) error {
	// MongoDB适配器不需要缓存预热
	return nil
}

// GetCacheStats 获取缓存统计（MongoDB不需要实现）
func (m *MongoAdapter) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return map[string]interface{}{
		"type":          "mongodb",
		"cache_enabled": false,
		"collection":    m.config.Collection,
		"database":      m.config.Database,
	}, nil
}

// CleanupExpiredData 清理过期数据
func (m *MongoAdapter) CleanupExpiredData(ctx context.Context, before time.Time) error {
	filter := bson.M{
		"timestamp": bson.M{"$lt": before},
		"status":    "unfollowed",
	}

	_, err := m.collection.DeleteMany(ctx, filter)
	return err
}

// ExportData 导出数据
func (m *MongoAdapter) ExportData(ctx context.Context, userID, format string) ([]byte, error) {
	// 简化实现，实际应该根据format参数导出不同格式
	return []byte("export not implemented"), nil
}

// ImportData 导入数据
func (m *MongoAdapter) ImportData(ctx context.Context, data []byte, format string) error {
	// 简化实现，实际应该根据format参数导入不同格式
	return fmt.Errorf("import not implemented")
}

// HealthCheck 健康检查
func (m *MongoAdapter) HealthCheck(ctx context.Context) error {
	return m.client.Ping(ctx, nil)
}

// Close 关闭连接
func (m *MongoAdapter) Close() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	return m.client.Disconnect(ctx)
}
