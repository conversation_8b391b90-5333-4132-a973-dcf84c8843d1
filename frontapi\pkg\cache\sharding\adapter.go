package sharding

import (
	"context"
	"fmt"
	"frontapi/pkg/cache/types"
	"hash/fnv"
	"sync"
	"time"
)

// Config 分片配置
type Config struct {
	// 分片数量
	ShardCount int
	// 分片列表
	Shards []types.CacheAdapter
	// 分片策略: hash, range, mod
	ShardingStrategy string
	// 键前缀
	Prefix string
	// 基础适配器
	BaseAdapter types.CacheAdapter
}

// Adapter 分片适配器
type Adapter struct {
	shards   []types.CacheAdapter
	shardMap map[string]types.CacheAdapter
	strategy string
	mu       sync.RWMutex
	stats    types.CacheStats
	prefix   string
}

// New 创建分片适配器
func NewAdapter(config *Config) (*Adapter, error) {
	if config == nil {
		return nil, fmt.Errorf("分片配置不能为空")
	}

	// 检查是否有分片或者基础适配器
	if len(config.Shards) == 0 && config.BaseAdapter == nil {
		return nil, fmt.Errorf("至少需要一个分片或者基础适配器")
	}

	strategy := config.ShardingStrategy
	if strategy == "" {
		strategy = "hash" // 默认使用哈希分片
	}

	// 如果提供了基础适配器但没有分片，创建分片
	shards := config.Shards
	if len(shards) == 0 && config.BaseAdapter != nil {
		shardCount := config.ShardCount
		if shardCount <= 0 {
			shardCount = 4 // 默认4个分片
		}

		// 创建多个基础适配器的副本作为分片
		shards = []types.CacheAdapter{config.BaseAdapter}
		// 注意：在实际生产环境中，这里应该复制基础适配器，而不是使用同一个
	}

	adapter := &Adapter{
		shards:   shards,
		shardMap: make(map[string]types.CacheAdapter),
		strategy: strategy,
		prefix:   config.Prefix,
		stats: types.CacheStats{
			StartTime: time.Now(),
		},
	}

	// 初始化分片映射
	for i, shard := range adapter.shards {
		shardName := fmt.Sprintf("shard-%d", i)
		adapter.shardMap[shardName] = shard
	}

	return adapter, nil
}

// 计算键的哈希值
func hashKey(key string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(key))
	return h.Sum32()
}

// 获取键的分片索引
func (a *Adapter) getShardIndex(key string) int {
	if len(a.shards) == 0 {
		return 0
	}

	// 使用FNV哈希算法
	h := fnv.New32a()
	h.Write([]byte(key))
	return int(h.Sum32() % uint32(len(a.shards)))
}

// 获取键对应的适配器
func (a *Adapter) getShard(key string) types.CacheAdapter {
	index := a.getShardIndex(key)
	if index >= len(a.shards) {
		index = 0
	}
	return a.shards[index]
}

// 根据键获取分片
func (a *Adapter) getShardByKey(key string) (types.CacheAdapter, error) {
	a.mu.RLock()
	defer a.mu.RUnlock()

	if len(a.shards) == 0 {
		return nil, fmt.Errorf("没有可用的分片")
	}

	if len(a.shards) == 1 {
		return a.shards[0], nil
	}

	var shardIndex int
	switch a.strategy {
	case "hash":
		// 使用哈希分片
		hash := hashKey(key)
		shardIndex = int(hash % uint32(len(a.shards)))
	case "range":
		// 使用范围分片（简单实现，仅基于首字符）
		if len(key) > 0 {
			firstChar := key[0]
			shardIndex = int(firstChar) % len(a.shards)
		}
	case "mod":
		// 使用取模分片（简单实现，基于键长度）
		shardIndex = len(key) % len(a.shards)
	default:
		// 默认使用哈希分片
		hash := hashKey(key)
		shardIndex = int(hash % uint32(len(a.shards)))
	}

	return a.shards[shardIndex], nil
}

// KeyWithPrefix 为键添加前缀
func (a *Adapter) KeyWithPrefix(key string) string {
	if a.prefix == "" {
		return key
	}
	return a.prefix + ":" + key
}

// Get 获取缓存值
func (a *Adapter) Get(ctx context.Context, key string) ([]byte, error) {
	shard := a.getShard(key)
	val, err := shard.Get(ctx, key)
	if err != nil {
		if err == types.ErrNotFound {
			a.stats.Misses++
		}
		return nil, err
	}

	a.stats.Hits++
	return val, nil
}

// Set 设置缓存值
func (a *Adapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	shard := a.getShard(key)
	err := shard.Set(ctx, key, value, expiration)
	if err == nil {
		a.stats.Sets++
	}
	return err
}

// Delete 删除缓存值
func (a *Adapter) Delete(ctx context.Context, key string) error {
	shard := a.getShard(key)
	err := shard.Delete(ctx, key)
	if err == nil {
		a.stats.Deletes++
	}
	return err
}

// Exists 检查键是否存在
func (a *Adapter) Exists(ctx context.Context, key string) (bool, error) {
	shard := a.getShard(key)
	return shard.Exists(ctx, key)
}

// Clear 清空所有分片的缓存
func (a *Adapter) Clear(ctx context.Context) error {
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, shard := range a.shards {
		if err := shard.Clear(ctx); err != nil {
			return err
		}
	}

	a.stats.Clears++
	return nil
}

// Close 关闭所有分片的连接
func (a *Adapter) Close() error {
	a.mu.Lock()
	defer a.mu.Unlock()

	var lastErr error
	for _, shard := range a.shards {
		if err := shard.Close(); err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// Stats 获取适配器统计信息
func (a *Adapter) Stats() *types.CacheStats {
	// 汇总所有分片的统计信息
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, shard := range a.shards {
		shardStats := shard.Stats()
		a.stats.Hits += shardStats.Hits
		a.stats.Misses += shardStats.Misses
		a.stats.Sets += shardStats.Sets
		a.stats.Deletes += shardStats.Deletes
		a.stats.Clears += shardStats.Clears
		// 累加新增统计项
		a.stats.BytesRead += shardStats.BytesRead
		a.stats.BytesWritten += shardStats.BytesWritten
	}

	return &a.stats
}

// Name 获取适配器名称
func (a *Adapter) Name() string {
	return "sharding"
}

// Type 获取适配器类型
func (a *Adapter) Type() string {
	return "sharding"
}

// MGet 批量获取缓存值
func (a *Adapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	// 按分片对键进行分组
	shardKeys := make(map[types.CacheAdapter][]string)
	for _, key := range keys {
		shard, err := a.getShardByKey(key)
		if err != nil {
			continue
		}
		shardKeys[shard] = append(shardKeys[shard], key)
	}

	// 从各分片获取数据
	result := make(map[string][]byte)
	for shard, shardKeyList := range shardKeys {
		shardResult, err := shard.MGet(ctx, shardKeyList)
		if err != nil {
			return result, err
		}
		// 合并结果
		for k, v := range shardResult {
			result[k] = v
		}
	}

	return result, nil
}

// MSet 批量设置缓存值
func (a *Adapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	if len(items) == 0 {
		return nil
	}

	// 按分片对键值对进行分组
	shardItems := make(map[types.CacheAdapter]map[string][]byte)
	for key, value := range items {
		shard, err := a.getShardByKey(key)
		if err != nil {
			return err
		}
		if _, exists := shardItems[shard]; !exists {
			shardItems[shard] = make(map[string][]byte)
		}
		shardItems[shard][key] = value
	}

	// 向各分片设置数据
	for shard, shardItemMap := range shardItems {
		if err := shard.MSet(ctx, shardItemMap, expiration); err != nil {
			return err
		}
	}

	a.stats.Sets += int64(len(items))
	return nil
}

// Increment 增加计数器值
func (a *Adapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	shard, err := a.getShardByKey(key)
	if err != nil {
		return 0, err
	}

	return shard.Increment(ctx, key, delta)
}

// Decrement 减少计数器值
func (a *Adapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	shard, err := a.getShardByKey(key)
	if err != nil {
		return 0, err
	}

	return shard.Decrement(ctx, key, delta)
}

// Expire 设置过期时间
func (a *Adapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	shard, err := a.getShardByKey(key)
	if err != nil {
		return err
	}

	return shard.Expire(ctx, key, expiration)
}

// TTL 获取剩余过期时间
func (a *Adapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	shard, err := a.getShardByKey(key)
	if err != nil {
		return 0, err
	}

	return shard.TTL(ctx, key)
}

// Ping 测试所有分片连接
func (a *Adapter) Ping(ctx context.Context) error {
	a.mu.RLock()
	defer a.mu.RUnlock()

	for _, shard := range a.shards {
		if err := shard.Ping(ctx); err != nil {
			return err
		}
	}

	return nil
}

// GetShardCount 获取分片数量
func (a *Adapter) GetShardCount() int {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return len(a.shards)
}

// GetShardByKey 根据键获取分片
func (a *Adapter) GetShardByKey(key string) (types.CacheAdapter, error) {
	return a.getShardByKey(key)
}

// AddShard 添加分片
func (a *Adapter) AddShard(shard types.CacheAdapter) error {
	if shard == nil {
		return fmt.Errorf("分片不能为空")
	}

	a.mu.Lock()
	defer a.mu.Unlock()

	// 生成唯一的分片名称
	shardName := fmt.Sprintf("shard-%d", len(a.shards))
	if _, exists := a.shardMap[shardName]; exists {
		return fmt.Errorf("分片已存在: %s", shardName)
	}

	// 添加到分片列表和映射
	a.shards = append(a.shards, shard)
	a.shardMap[shardName] = shard

	return nil
}

// RemoveShard 移除分片
func (a *Adapter) RemoveShard(shardName string) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	shard, exists := a.shardMap[shardName]
	if !exists {
		return fmt.Errorf("分片不存在: %s", shardName)
	}

	// 关闭分片连接
	if err := shard.Close(); err != nil {
		return err
	}

	// 从分片列表中移除
	for i, s := range a.shards {
		if s.Name() == shardName {
			a.shards = append(a.shards[:i], a.shards[i+1:]...)
			break
		}
	}

	// 从分片映射中移除
	delete(a.shardMap, shardName)

	return nil
}
