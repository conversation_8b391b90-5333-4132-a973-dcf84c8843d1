package posts

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// CommentLike 帖子评论点赞模型
type CommentLike struct {
	models.BaseModel
	UserID    string         `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	CommentID string         `json:"comment_id" gorm:"type:string;size:36;not null;comment:评论ID"`
	Status    int8           `json:"status" gorm:"default:1;comment:状态：0-取消点赞，1-已点赞"`
}

// TableName 指定表名
func (CommentLike) TableName() string {
	return "ly_post_comment_likes"
}

// CommentLikeRequest 评论点赞请求
type CommentLikeRequest struct {
	CommentID string `json:"comment_id" validate:"required"`
}

// CommentLikeResponse 评论点赞响应
type CommentLikeResponse struct {
	ID        string         `json:"id"`
	UserID    string         `json:"user_id"`
	CommentID string         `json:"comment_id"`
	Status    int8           `json:"status"`
	CreatedAt types.JSONTime `json:"created_at"`
}
