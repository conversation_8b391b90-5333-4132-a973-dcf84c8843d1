<template>
  <!-- 这个组件不需要渲染任何内容 -->
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';

const props = defineProps<{
  css: string
}>()

// 创建一个style元素引用
const styleElement = ref<HTMLStyleElement | null>(null)

// 监听css变化，更新style元素内容
watch(() => props.css, (newCss) => {
  if (styleElement.value) {
    styleElement.value.textContent = newCss
  }
})

// 组件挂载时创建style元素
onMounted(() => {
  // 创建style元素
  styleElement.value = document.createElement('style')
  styleElement.value.setAttribute('type', 'text/css')
  styleElement.value.setAttribute('data-dynamic-style', '')
  
  // 设置初始内容
  if (props.css) {
    styleElement.value.textContent = props.css
  }
  
  // 将style元素添加到head中
  document.head.appendChild(styleElement.value)
})

// 组件卸载时移除style元素
onUnmounted(() => {
  if (styleElement.value && styleElement.value.parentNode) {
    styleElement.value.parentNode.removeChild(styleElement.value)
  }
})
</script> 