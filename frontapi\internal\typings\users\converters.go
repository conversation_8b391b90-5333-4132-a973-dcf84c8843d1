package users

import (
	"frontapi/internal/models/users"
	"frontapi/internal/typings"
	"time"
)

// ConvertCelebrityList 转换明星列表
func ConvertCelebrityList(celebrities []*users.User) []CelebrityInfo {
	result := make([]CelebrityInfo, len(celebrities))
	for i, celebrity := range celebrities {
		result[i] = ConvertCelebrityInfo(celebrity)
	}
	return result
}

// ConvertCelebrityInfo 转换明星信息
func ConvertCelebrityInfo(celebrity *users.User) CelebrityInfo {
	// 处理null类型字段
	avatarStr := ""
	if celebrity.Avatar.Valid {
		avatarStr = celebrity.Avatar.String
	}

	bioStr := ""
	if celebrity.Bio.Valid {
		bioStr = celebrity.Bio.String
	}

	// 处理时间字段
	lastLoginTimeStr := time.Time(celebrity.LastLoginTime).Format("2006-01-02 15:04:05")
	lastActiveTimeStr := time.Time(celebrity.LastActiveTime).Format("2006-01-02 15:04:05")

	// 处理用户名
	usernameStr := ""
	if celebrity.Username.Valid {
		usernameStr = celebrity.Username.String
	}

	return CelebrityInfo{
		ID:               celebrity.ID,
		Name:             celebrity.Nickname.ValueOrZero(), // 使用昵称作为名称
		Code:             usernameStr,                      // 使用用户名作为编码
		Nickname:         celebrity.Nickname.ValueOrZero(),
		Avatar:           avatarStr,
		Bio:              bioStr,
		Description:      bioStr,
		Nation:           "", // 新模型中不存在此字段
		Gender:           celebrity.Gender,
		Birthday:         "", // 用户模型中没有生日字段
		UserType:         int(celebrity.UserType),
		FollowCount:      0, // 这些字段在新模型中不存在，设为默认值
		LikeCount:        0,
		TotalAlbums:      0,
		TotalShorts:      0,
		TotalVideos:      0,
		TotalPosts:       0,
		TotalViews:       0,
		TotalComments:    0,
		TotalLikes:       0,
		TotalFollowing:   0,
		Heat:             0,
		RegTime:          time.Time(celebrity.RegTime).Format("2006-01-02 15:04:05"),
		LastLoginTime:    lastLoginTimeStr,
		LastActiveTime:   lastActiveTimeStr,
		Status:           int(celebrity.Status),
		IsVerified:       celebrity.IsVerified == 1,
		IsContentCreator: celebrity.IsContentCreator == 1,
		CreatorLevel:     int(celebrity.CreatorLevel),
		IsLiked:          celebrity.IsLiked,
		IsFollowed:       celebrity.IsFollowed,
	}
}

// ConvertCelebrityListResponse 转换明星列表响应
func ConvertCelebrityListResponse(celebrities []*users.User, total int64, pageNo int, pageSize int) CelebrityListResponse {
	return CelebrityListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertCelebrityList(celebrities),
	}
}

func ConvertCreatorList(creators []*users.User) []CreatorInfo {
	result := make([]CreatorInfo, len(creators))
	for i, creator := range creators {
		result[i] = ConvertCreatorInfo(creator)
	}
	return result
}

func ConvertCreatorInfo(creator *users.User) CreatorInfo {
	// 处理null类型字段
	avatarStr := ""
	if creator.Avatar.Valid {
		avatarStr = creator.Avatar.String
	}

	bioStr := ""
	if creator.Bio.Valid {
		bioStr = creator.Bio.String
	}

	// 处理昵称
	nicknameStr := ""
	if creator.Nickname.Valid {
		nicknameStr = creator.Nickname.String
	}

	return CreatorInfo{
		ID:           creator.ID,
		Nickname:     nicknameStr,
		Avatar:       avatarStr,
		Bio:          bioStr,
		FollowCount:  0, // 这些字段在新模型中不存在，设为默认值
		TotalVideos:  0,
		TotalPosts:   0,
		TotalShorts:  0,
		Heat:         0,
		UserType:     int(creator.UserType),
		CreatorLevel: int(creator.CreatorLevel),
		IsLiked:      creator.IsLiked,
		IsFollowed:   creator.IsFollowed,
	}
}

// ConvertCreatorDetailInfo 转换创作者详情信息
func ConvertCreatorDetailInfo(creator *users.User) CreatorDetailInfo {
	// 处理null类型字段
	avatarStr := ""
	if creator.Avatar.Valid {
		avatarStr = creator.Avatar.String
	}

	bioStr := ""
	if creator.Bio.Valid {
		bioStr = creator.Bio.String
	}

	// 处理时间字段
	lastActiveTimeStr := time.Time(creator.LastActiveTime).Format("2006-01-02 15:04:05")

	return CreatorDetailInfo{
		ID:             creator.ID,
		Nickname:       creator.Nickname.ValueOrZero(),
		Avatar:         avatarStr,
		Bio:            bioStr,
		Description:    bioStr, // 使用Bio作为描述
		Gender:         creator.Gender,
		FollowCount:    0, // 这些字段在新模型中不存在，设为默认值
		LikeCount:      0,
		TotalAlbums:    0,
		TotalShorts:    0,
		TotalVideos:    0,
		TotalPosts:     0,
		TotalViews:     0,
		TotalComments:  0,
		TotalLikes:     0,
		TotalFollowing: 0,
		Heat:           0,
		LastActiveTime: lastActiveTimeStr,
		Status:         creator.Status,
		CreatedAt:      time.Time(creator.RegTime).Format("2006-01-02 15:04:05"), // 使用RegTime作为创建时间
	}
}

func ConvertCreatorListResponse(creators []*users.User, total int64, pageNo int, pageSize int) CreatorListResponse {
	return CreatorListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertCreatorList(creators),
	}
}

func ConvertCreatorCommentListResponse(comments []*users.UserComment, total int64, pageNo int, pageSize int) CreatorCommentListResponse {
	return CreatorCommentListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertCreatorCommentList(comments),
	}
}

func ConvertCreatorCommentList(comments []*users.UserComment) []CreatorCommentInfo {
	result := make([]CreatorCommentInfo, len(comments))
	for i, comment := range comments {
		result[i] = ConvertCreatorCommentInfo(comment)
	}
	return result
}

func ConvertCreatorCommentInfo(comment *users.UserComment) CreatorCommentInfo {
	return CreatorCommentInfo{
		Content:      comment.Content,
		UserNickname: comment.UserNickname,
		UserAvatar:   comment.UserAvatar,
		ParentID:     comment.ParentID,
		EntityID:     comment.EntityID,
		EntityType:   comment.EntityType,
		RelationID:   comment.RelationID,
		Heat:         comment.Heat,
		LikeCount:    comment.LikeCount,
		ReplyCount:   comment.ReplyCount,
		CreatedAt:    time.Time(comment.CreatedAt).Format("2006-01-02 15:04:05"),
		UpdatedAt:    time.Time(comment.UpdatedAt).Format("2006-01-02 15:04:05"),
		Status:       comment.Status,
	}
}
