package integral

import (
	"frontapi/internal/models"
)

type PointRule struct {
	models.BaseModelStruct
	Name        string `json:"name" gorm:"type:string;size:50;not null;comment:规则名称"`
	Code        string `json:"code" gorm:"type:string;size:50;not null;uniqueIndex;comment:规则编码"`
	Points      int    `json:"points" gorm:"not null;comment:积分数量"`
	Action      string `json:"action" gorm:"type:string;size:100;not null;comment:触发动作"`
	Period      string `json:"period" gorm:"type:string;size:20;comment:周期：once-一次性，daily-每日，weekly-每周，monthly-每月"`
	MaxTimes    *int   `json:"max_times" gorm:"comment:最大次数"`
	Description string `json:"description" gorm:"type:string;size:255;comment:描述"`
	Status      int8   `json:"status" gorm:"not null;default:1;comment:状态：0-禁用，1-启用"`
}

func (PointRule) TableName() string {
	return "ly_point_rules"
}
