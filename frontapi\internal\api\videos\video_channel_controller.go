package videos

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/videos"

	"github.com/gofiber/fiber/v2"
)

// VideoChannelController 视频频道控制器
type VideoChannelController struct {
	admin.BaseController
	channelService videos.VideoChannelService
}

// NewVideoChannelController 创建视频频道控制器
func NewVideoChannelController(channelService videos.VideoChannelService) *VideoChannelController {
	return &VideoChannelController{
		channelService: channelService,
	}
}

// GetVideoChannelList 获取视频频道列表
func (c *VideoChannelController) GetVideoChannelList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	condition := map[string]interface{}{
		"keyword": keyword,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "sort_order ASC"
	}
	channels, total, err := c.channelService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.SuccessList(ctx, channels, total, pageNo, pageSize)
}
