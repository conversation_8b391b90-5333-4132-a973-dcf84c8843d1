<template>
    <div class="post-search-bar">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
            <el-form-item label="搜索关键词">
                <el-input v-model="searchForm.keyword" placeholder="请输入搜索关键词" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
                    <el-option label="待审核" :value="0" />
                    <el-option label="隐藏" :value="1" />
                    <el-option label="显示" :value="2" />
                    <el-option label="已拒绝" :value="-2" />
                    <el-option label="已删除" :value="-4" />
                </el-select>
            </el-form-item>

            <el-form-item label="用户ID">
                <el-input v-model="searchForm.user_id" placeholder="请输入用户ID" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="分类">
                <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable style="width: 180px;">
                    <el-option v-for="category in categoryOptions" :key="category.id" :label="category.name"
                        :value="category.id" />
                </el-select>
            </el-form-item>

            <el-form-item label="创建时间">
                <el-date-picker v-model="searchForm.date_range" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
                    style="width: 240px;" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon>
                        <Search />
                    </el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    重置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { Refresh, Search } from '@element-plus/icons-vue';
import { onMounted, reactive, ref, watch } from 'vue';

// 搜索表单类型定义
export interface PostSearchForm {
    keyword: string;
    status?: number;
    category_id?: string;
    user_id?: string;
    date_range: [string, string] | [];
}

// Props
interface Props {
    modelValue?: PostSearchForm;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
        keyword: '',
        status: undefined,
        category_id: undefined,
        user_id: '',
        date_range: [],
    })
});

// Emits
interface Emits {
    search: [params: PostSearchForm];
    reset: [];
    refresh: [];
    'update:modelValue': [value: PostSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<PostSearchForm>({ ...props.modelValue });

// 分类选项（根据实际需要获取）
const categoryOptions = ref<{ id: string; name: string }[]>([]);

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
    emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
    emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
    Object.assign(searchForm, {
        title: '',
        status: undefined,
        category_id: undefined,
        user_id: '',
        date_range: [],
    });
    emit('reset');
};

// 刷新
const handleRefresh = () => {
    emit('refresh');
};

// 获取分类选项
const getCategoryOptions = async () => {
    // 根据实际需要实现
    // const categories = await getPostCategoryList();
    // categoryOptions.value = categories.data.list || [];
};

// 初始化
onMounted(() => {
    getCategoryOptions();
});
</script>

<style scoped lang="scss">
.post-search-bar {
    margin-bottom: 16px;
}

.post-search-bar .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.post-search-bar .el-form-item {
    margin-bottom: 8px;
}
</style>