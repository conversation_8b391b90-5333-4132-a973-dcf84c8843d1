package bigcache

import (
	"context"
	"fmt"
	"time"

	"frontapi/pkg/cache/types"

	"github.com/allegro/bigcache/v3"
)

// Config BigCache配置
type Config struct {
	// 分片数量
	Shards int
	// 生命周期
	LifeWindow time.Duration
	// 清理周期
	CleanWindow time.Duration
	// 窗口内最大条目数
	MaxEntriesInWindow int
	// 最大条目大小
	MaxEntrySize int
	// 硬性缓存大小限制（MB）
	HardMaxCacheSize int
	// 键前缀
	Prefix string
}

// Adapter BigCache适配器
type Adapter struct {
	client *bigcache.BigCache
	config *Config
	stats  types.CacheStats
	prefix string
}

// NewAdapter 创建新的BigCache适配器
func NewAdapter(config *Config) (*Adapter, error) {
	if config == nil {
		config = &Config{
			Shards:             1024,
			LifeWindow:         10 * time.Minute,
			CleanWindow:        5 * time.Minute,
			MaxEntriesInWindow: 1000 * 10 * 60,
			MaxEntrySize:       500,
			HardMaxCacheSize:   8192,
		}
	}

	bigcacheConfig := bigcache.DefaultConfig(config.LifeWindow)
	bigcacheConfig.Shards = config.Shards
	bigcacheConfig.CleanWindow = config.CleanWindow
	bigcacheConfig.MaxEntriesInWindow = config.MaxEntriesInWindow
	bigcacheConfig.MaxEntrySize = config.MaxEntrySize
	bigcacheConfig.HardMaxCacheSize = config.HardMaxCacheSize

	client, err := bigcache.New(context.Background(), bigcacheConfig)
	if err != nil {
		return nil, fmt.Errorf("创建BigCache客户端失败: %w", err)
	}

	// 保存前缀
	prefix := ""
	if config.Prefix != "" {
		prefix = config.Prefix
	}

	return &Adapter{
		client: client,
		config: config,
		prefix: prefix,
		stats: types.CacheStats{
			Hits:      0,
			Misses:    0,
			StartTime: time.Now(),
		},
	}, nil
}

// Get 获取缓存值
func (a *Adapter) Get(ctx context.Context, key string) ([]byte, error) {
	prefixedKey := a.KeyWithPrefix(key)
	value, err := a.client.Get(prefixedKey)
	if err != nil {
		if err == bigcache.ErrEntryNotFound {
			a.stats.Misses++
			return nil, types.ErrNotFound
		}
		return nil, fmt.Errorf("获取缓存失败: %w", err)
	}

	a.stats.Hits++
	a.stats.BytesRead += int64(len(value))
	return value, nil
}

// Set 设置缓存值
func (a *Adapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	// BigCache不支持单独设置过期时间，使用全局配置
	prefixedKey := a.KeyWithPrefix(key)
	err := a.client.Set(prefixedKey, value)
	if err != nil {
		return fmt.Errorf("设置缓存失败: %w", err)
	}
	a.stats.Sets++
	a.stats.BytesWritten += int64(len(value))
	return nil
}

// Delete 删除缓存值
func (a *Adapter) Delete(ctx context.Context, key string) error {
	prefixedKey := a.KeyWithPrefix(key)
	err := a.client.Delete(prefixedKey)
	if err != nil && err != bigcache.ErrEntryNotFound {
		return fmt.Errorf("删除缓存失败: %w", err)
	}
	a.stats.Deletes++
	return nil
}

// Exists 检查键是否存在
func (a *Adapter) Exists(ctx context.Context, key string) (bool, error) {
	prefixedKey := a.KeyWithPrefix(key)
	_, err := a.client.Get(prefixedKey)
	if err != nil {
		if err == bigcache.ErrEntryNotFound {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// Clear 清空缓存
func (a *Adapter) Clear(ctx context.Context) error {
	err := a.client.Reset()
	if err != nil {
		return fmt.Errorf("清空缓存失败: %w", err)
	}
	return nil
}

// Close 关闭缓存连接
func (a *Adapter) Close() error {
	return a.client.Close()
}

// Stats 获取缓存统计信息
func (a *Adapter) Stats() *types.CacheStats {

	// 更新我们的统计信息
	// a.stats.Size = int64(a.client.Capacity())
	// a.stats.ItemCount = int64(a.client.Len())
	a.stats.Uptime = time.Since(a.stats.StartTime)
	if a.stats.Hits+a.stats.Misses > 0 {
		a.stats.HitRate = float64(a.stats.Hits) / float64(a.stats.Hits+a.stats.Misses)
	}

	return &a.stats
}

// Name 获取适配器名称
func (a *Adapter) Name() string {
	return "bigcache"
}

// Type 获取适配器类型
func (a *Adapter) Type() string {
	return "bigcache"
}

// MGet 批量获取缓存值
func (a *Adapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	result := make(map[string][]byte, len(keys))

	for _, key := range keys {
		value, err := a.client.Get(key)
		if err == nil {
			result[key] = value
			a.stats.Hits++
		} else {
			a.stats.Misses++
		}
	}

	return result, nil
}

// MSet 批量设置缓存值
func (a *Adapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	for key, value := range items {
		if err := a.client.Set(key, value); err != nil {
			return err
		}
		a.stats.Sets++
	}

	return nil
}

// Increment 增加计数器值
func (a *Adapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	// 获取当前值
	var currentValue int64 = 0

	data, err := a.client.Get(key)
	if err == nil {
		// 尝试将数据解析为int64
		var v int64
		_, err := fmt.Sscanf(string(data), "%d", &v)
		if err == nil {
			currentValue = v
		}
	}

	// 计算新值
	newValue := currentValue + delta

	// 保存新值
	err = a.client.Set(key, []byte(fmt.Sprintf("%d", newValue)))
	if err != nil {
		return 0, err
	}

	a.stats.Sets++
	return newValue, nil
}

// Decrement 减少计数器值
func (a *Adapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return a.Increment(ctx, key, -delta)
}

// Expire 设置过期时间
func (a *Adapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	// BigCache不支持为单个key设置过期时间
	// 此方法仅为满足接口要求
	_, err := a.client.Get(key)
	return err
}

// TTL 获取剩余过期时间
func (a *Adapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	// BigCache不支持获取单个key的过期时间
	// 检查键是否存在
	_, err := a.client.Get(key)
	if err != nil {
		if err == bigcache.ErrEntryNotFound {
			return 0, fmt.Errorf("缓存键不存在: %s", key)
		}
		return 0, err
	}

	// 返回-1表示不支持TTL查询
	return -1, nil
}

// Ping 测试连接
func (a *Adapter) Ping(ctx context.Context) error {
	// BigCache是本地内存缓存，不需要连接测试
	// 只需尝试一次简单操作
	testKey := "ping_test"
	err := a.client.Set(testKey, []byte("ping"))
	if err != nil {
		return err
	}

	_, err = a.client.Get(testKey)
	if err != nil {
		return err
	}

	return a.client.Delete(testKey)
}

// KeyWithPrefix 为键添加前缀
func (a *Adapter) KeyWithPrefix(key string) string {
	if a.prefix == "" {
		return key
	}
	return a.prefix + ":" + key
}
