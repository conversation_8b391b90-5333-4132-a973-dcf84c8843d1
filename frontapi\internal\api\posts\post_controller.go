package posts

import (
	"fmt"
	"frontapi/internal/api"
	postModel "frontapi/internal/models/posts"
	"frontapi/internal/models/users"
	service "frontapi/internal/service/posts"
	userSrv "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
	postTypings "frontapi/internal/typings/posts"
	userTypings "frontapi/internal/typings/users"
	"frontapi/pkg/utils/sliceutils"

	lcSlice "github.com/duke-git/lancet/v2/slice"

	"slices"

	"github.com/gofiber/fiber/v2"
)

// PostController 帖子控制器
type PostController struct {
	PostService                service.PostService
	PostCommentService         service.PostCommentService
	UserService                userSrv.UserService
	UserFollowsService         userSrv.UserFollowsService
	VideoService               videoSrv.VideoService
	UserWatchVideoService      userSrv.UserWatchVideoHistoryService
	UserVideoCollectionService userSrv.UserVideoCollectionService
	UserLikeVideoService       videoSrv.VideoLikeService
	api.BaseController         // 继承Response
}

// NewPostController 创建帖子控制器实例
func NewPostController(postService service.PostService,
	userService userSrv.UserService,
	userFollowsService userSrv.UserFollowsService,
	videoService videoSrv.VideoService,
	userWatchVideoService userSrv.UserWatchVideoHistoryService,
	userVideoCollectionService userSrv.UserVideoCollectionService,
	userLikeVideoService videoSrv.VideoLikeService) *PostController {
	return &PostController{
		PostService:                postService,
		UserService:                userService,
		UserFollowsService:         userFollowsService,
		VideoService:               videoService,
		UserWatchVideoService:      userWatchVideoService,
		UserVideoCollectionService: userVideoCollectionService,
		UserLikeVideoService:       userLikeVideoService,
	}
}

// GetPostRecommendedUsers 获取推荐用户列表
func (c *PostController) GetRecommendedCreatorUsers(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	//获取当前登录用户id
	userID := c.GetUserID(ctx)
	nation := reqInfo.Get("nation").GetString()
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"nation": nation,
	}
	if userID != "" {
		// 用户模型中不再有Lat和Lng字段，使用默认值
		condition["lat"] = 0.0
		condition["lng"] = 0.0
	}
	orderBy := "total_videos DESC,follow_count DESC,last_active_time DESC,last_login_time DESC,follow_count DESC,like_count DESC"
	//推荐规则，1相同国籍，2相同语言，3关注过，4点赞过，5收藏过，6评论过，7分享过，8转发过，9相同收藏,10相同收藏，11相同评论，12.关注人数，13.粉丝人数，14.点赞人数，15.收藏人数，16.评论人数，17.分享人数，18.转发人数
	//根据这个打分然后获取相关推荐用户

	users, total, err := c.UserService.GetRecommendUsers(ctx.Context(), condition, userID, orderBy, page, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := userTypings.ConvertCreatorListResponse(users, total, page, pageSize)
	return c.Success(ctx, response)
}

/*
*
推荐关注用户
*/
func (c *PostController) GetRecommendedFollowUsers(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	nation := reqInfo.Get("nation").GetString()

	orderBy := "follow_count DESC,heat DESC,last_active_time DESC,last_login_time DESC,follow_count DESC,like_count DESC"
	condition := map[string]interface{}{
		"nation": nation,
	}
	users, total, err := c.UserService.GetRecommendUsers(ctx.Context(), condition, userID, orderBy, pageNo, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := userTypings.ConvertCreatorListResponse(users, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// GetPostFollowingUsers 获取关注用户列表
func (c *PostController) GetPostFollowingUsers(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"user_id": userID,
	}
	orderBy := "follow_count DESC,heat DESC,last_active_time DESC,last_login_time DESC,follow_count DESC,like_count DESC"
	users, total, err := c.UserFollowsService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.SuccessList(ctx, users, total, page, pageSize)
}

// GetTopHotUserList 获取热门用户列表
func (c *PostController) GetTopHotUserList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"user_id": userID,
	}
	orderBy := "heat DESC,follow_count DESC,last_active_time DESC,last_login_time DESC,follow_count DESC,like_count DESC"
	users, total, err := c.UserService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.SuccessList(ctx, users, total, page, pageSize)
}

// GetPostList 获取帖子列表
func (c *PostController) GetPostList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	condition := map[string]interface{}{
		"status":  2,
		"keyword": keyword,
	}

	orderBy := reqInfo.Get("order_by").GetString()

	sortOrder := []string{"trending", "latest", "recommend", "videos", "pictures"}
	if !slices.Contains(sortOrder, orderBy) {
		orderBy = "trending"
	}
	switch orderBy {
	case "trending":
		orderBy = "heat DESC,created_at DESC,view_count DESC"
		break
	case "latest":
		orderBy = "created_at DESC,heat DESC,view_count DESC"
		break
	case "recommend":
		orderBy = "created_at DESC,comment_count DESC,heat DESC,view_count DESC"
		break
	case "videos":
		condition["video"] = true
		orderBy = "created_at DESC,heat DESC,view_count DESC"
		break
	case "pictures":
		condition["images"] = true
		orderBy = "created_at DESC,heat DESC,view_count DESC"
		break
	}
	posts, total, err := c.PostService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	userIDs := sliceutils.Pluck[*postModel.Post, string](posts, func(post *postModel.Post) *string {
		return &post.AuthorID
	})
	authorMap, err := c.getAuthorMap(ctx, userIDs, userID)
	if err != nil {
		return err
	}
	//遍历，获取发帖用户信息，并且是否给帖子点赞，是否跟随用户
	for _, post := range posts {
		post.IsLiked = false
		if userID != "" {
			post.IsLiked, err = c.PostService.CheckUserLiked(ctx.Context(), userID, post.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取帖子列表失败: "+err.Error())
			}
		}
		if user, ok := authorMap[post.AuthorID]; ok {
			post.Author = user
		}
	}
	response := postTypings.ConvertPostListResponse(posts, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// getAuthorMap 获取用户信息
func (c *PostController) getAuthorMap(ctx *fiber.Ctx, userIDs []string, userID string) (map[string]*users.User, error) {
	userIDs = lcSlice.Unique(userIDs) // 去重
	fmt.Println("userIDs", userIDs)
	authors, err := c.UserService.FindByIDs(ctx.Context(), userIDs)
	fmt.Println("authors", authors)
	if err != nil {
		return nil, c.InternalServerError(ctx, err.Error())
	}
	authorMap := sliceutils.ToMap(authors, func(userPtr *users.User, _ int, slice []*users.User) string {
		return userPtr.ID
	})
	fmt.Println("authorMap", authorMap)
	return authorMap, nil
}

// GetRecommendedPostList 获取推荐帖子
func (c *PostController) GetRecommendedPostList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	postID := reqInfo.Get("post_id").GetString()
	condition := map[string]interface{}{
		"status":  2,
		"keyword": keyword,
	}
	if postID != "" {
		//获取帖子详情
		post, err := c.PostService.GetByID(ctx.Context(), postID, true)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		//根据帖子类型获取推荐帖子
		if post.Video != "" {
			condition["video"] = true
		} else if post.Images != nil {
			condition["images"] = true
		} else {
			return c.InternalServerError(ctx, "帖子类型错误")
		}
	}

	orderBy := reqInfo.Get("order_by").GetString()

	sortOrder := []string{"trending", "latest", "recommend", "videos", "pictures"}
	if !slices.Contains(sortOrder, orderBy) {
		orderBy = "trending"
	}
	switch orderBy {
	case "trending":
		orderBy = "heat DESC,created_at DESC,view_count DESC"
		break
	case "latest":
		orderBy = "created_at DESC,heat DESC,view_count DESC"
		break
	case "recommend":
		orderBy = "created_at DESC,comment_count DESC,heat DESC,view_count DESC"
		break
	case "videos":
		condition["video"] = true
		orderBy = "created_at DESC,heat DESC,view_count DESC"
		break
	case "pictures":
		condition["images"] = true
		orderBy = "created_at DESC,heat DESC,view_count DESC"
		break
	}
	posts, total, err := c.PostService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	userIDs := sliceutils.Pluck[*postModel.Post, string](posts, func(post *postModel.Post) *string {
		return &post.AuthorID
	})
	authorMap, err := c.getAuthorMap(ctx, userIDs, userID)
	if err != nil {
		return err
	}
	//遍历，获取发帖用户信息，并且是否给帖子点赞，是否跟随用户
	for _, post := range posts {
		post.IsLiked = false
		if userID != "" {
			post.IsLiked, err = c.PostService.CheckUserLiked(ctx.Context(), userID, post.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取帖子列表失败: "+err.Error())
			}
		}
		if user, ok := authorMap[post.AuthorID]; ok {
			post.Author = user
		}
	}
	response := postTypings.ConvertPostListResponse(posts, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// 获取推荐用户
func (c *PostController) GetRecommendedUsers(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	//获取当前登录用户id
	userID := c.GetUserID(ctx)
	nation := reqInfo.Get("nation").GetString()
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"nation": nation,
	}
	if userID != "" {
		// 用户模型中不再有Lat和Lng字段，使用默认值
		condition["lat"] = 0.0
		condition["lng"] = 0.0
	}
	orderBy := "total_videos DESC,follow_count DESC,last_active_time DESC,last_login_time DESC,follow_count DESC,like_count DESC"
	//推荐规则，1相同国籍，2相同语言，3关注过，4点赞过，5收藏过，6评论过，7分享过，8转发过，9相同收藏,10相同收藏，11相同评论，12.关注人数，13.粉丝人数，14.点赞人数，15.收藏人数，16.评论人数，17.分享人数，18.转发人数
	//根据这个打分然后获取相关推荐用户

	users, total, err := c.UserService.GetRecommendUsers(ctx.Context(), condition, userID, orderBy, page, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := userTypings.ConvertCreatorListResponse(users, total, page, pageSize)
	return c.Success(ctx, response)
}

// GetPostDetail 获取帖子详情
func (c *PostController) GetPostDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	postID := reqInfo.Get("post_id").GetString()
	if postID == "" {
		return c.BadRequest(ctx, "post_id is required", nil)
	}
	post, err := c.PostService.GetByID(ctx.Context(), postID, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	//获取帖子作者信息
	authorUserInfo, err := c.UserService.GetByID(ctx.Context(), post.AuthorID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取帖子作者信息失败: "+err.Error())
	}
	post.Author = authorUserInfo
	response := postTypings.ConvertPostResponse(post)
	return c.Success(ctx, response)
}

// 获取推荐视频
func (c *PostController) GetRecommendedVideos(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	condition := map[string]interface{}{
		"status": 1,
	}
	//获取用户看过的视频或者点赞收藏的视频

	var videoIDs []string
	if userID != "" {

		watchVideos, err := c.UserWatchVideoService.FindAll(ctx.Context(), map[string]interface{}{"user_id": userID}, "created_at DESC", false)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		for _, video := range watchVideos {
			videoIDs = append(videoIDs, video.VideoID)
		}
		likeVideos, err := c.UserLikeVideoService.FindAll(ctx.Context(), map[string]interface{}{"user_id": userID}, "created_at DESC", false)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		for _, video := range likeVideos {
			videoIDs = append(videoIDs, video.VideoID)
		}
		collectionVideos, err := c.UserVideoCollectionService.FindAll(ctx.Context(), map[string]interface{}{"user_id": userID}, "created_at DESC", false)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		for _, video := range collectionVideos {
			videoIDs = append(videoIDs, *video.VideoID.Ptr())
		}
		condition["notin"] = videoIDs
	}
	orderBy := "created_at DESC,heat DESC,view_count DESC"
	videos, total, err := c.VideoService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	// 添加调试信息
	fmt.Printf("获取到的视频数量：%d, 总数：%d\n", len(videos), total)

	for _, video := range videos {

		creatorID := video.CreatorID.ValueOrZero()
		if creatorID == "" {

			continue
		}

		user, err := c.UserService.GetByID(ctx.Context(), creatorID, false)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		video.Author = user
	}
	fmt.Println("videos", videos)
	response := postTypings.ConvertPostVideoListResponse(videos, total, page, pageSize)
	return c.Success(ctx, response)
}

// getCommunitySearch 搜索用户和帖子
func (c *PostController) getCommunitySearch(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	keyword := reqInfo.Get("keyword").GetString()
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"keyword": keyword,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	posts, total, err := c.PostService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.SuccessList(ctx, posts, total, page, pageSize)
}
