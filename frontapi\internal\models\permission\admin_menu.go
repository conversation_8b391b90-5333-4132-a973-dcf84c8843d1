package permission

import (
	"frontapi/internal/models"
)

// AdminMenu 管理员菜单权限模型
type AdminMenu struct {
	*models.IntBaseModelStruct
	ParentID   int    `json:"parent_id" gorm:"column:parent_id;default:0;comment:父级菜单ID"`
	Title      string `json:"title" gorm:"column:title;type:varchar(100);not null;comment:菜单标题"`
	Name       string `json:"name" gorm:"column:name;type:varchar(100);not null;comment:菜单名称"`
	Path       string `json:"path" gorm:"column:path;type:varchar(200);comment:路由路径"`
	Meta       string `json:"meta" gorm:"column:meta;type:json;comment:菜单元数据"`
	Component  string `json:"component" gorm:"column:component;type:varchar(200);comment:组件路径"`
	Icon       string `json:"icon" gorm:"column:icon;type:varchar(100);comment:菜单图标"`
	Permission string `json:"permission" gorm:"column:permission;type:varchar(200);comment:权限标识"`
	Type       int8   `json:"type" gorm:"column:type;default:1;comment:菜单类型(1菜单,2按钮,3接口)"`
	Sort       int    `json:"sort" gorm:"column:sort;default:0;comment:排序"`
	IsHidden   int8   `json:"is_hidden" gorm:"column:is_hidden;default:0;comment:是否隐藏(0否,1是)"`
	IsCache    int8   `json:"is_cache" gorm:"column:is_cache;default:0;comment:是否缓存(0否,1是)"`
	IsFrame    int8   `json:"is_frame" gorm:"column:is_frame;default:0;comment:是否外链(0否,1是)"`
	Remark     string `json:"remark" gorm:"column:remark;type:varchar(500);comment:备注"`

	// 关联字段 (不存储到数据库)
	Children   []*AdminMenu `json:"children" gorm:"-"`    // 子菜单
	ParentName string       `json:"parent_name" gorm:"-"` // 父级菜单名称
	Level      int          `json:"level" gorm:"-"`       // 菜单层级
}

// TableName 指定表名
func (AdminMenu) TableName() string {
	return "ly_admin_sys_menu"
}

// IsActive 检查菜单是否激活
func (m *AdminMenu) IsActive() bool {
	return m.Status == 1
}

// IsHiddenMenu 检查是否为隐藏菜单
func (m *AdminMenu) IsHiddenMenu() bool {
	return m.IsHidden == 1
}

// IsCacheMenu 检查是否缓存菜单
func (m *AdminMenu) IsCacheMenu() bool {
	return m.IsCache == 1
}

// IsFrameMenu 检查是否为外链菜单
func (m *AdminMenu) IsFrameMenu() bool {
	return m.IsFrame == 1
}

// IsTopLevel 检查是否为顶级菜单
func (m *AdminMenu) IsTopLevel() bool {
	return m.ParentID == 0
}

// GetPermissionCode 获取权限标识
func (m *AdminMenu) GetPermissionCode() string {
	return m.Permission
}

// HasChildren 检查是否有子菜单
func (m *AdminMenu) HasChildren() bool {
	return len(m.Children) > 0
}

// MenuType 菜单类型常量
const (
	MenuTypeMenu      = 1 // 菜单
	MenuTypeButton    = 2 // 按钮
	MenuTypeInterface = 3 // 接口
)

// GetMenuTypeName 获取菜单类型名称
func GetMenuTypeName(menuType int8) string {
	switch menuType {
	case MenuTypeMenu:
		return "菜单"
	case MenuTypeButton:
		return "按钮"
	case MenuTypeInterface:
		return "接口"
	default:
		return "未知"
	}
}

// GetMenuTypeNames 获取所有菜单类型名称
func GetMenuTypeNames() map[int8]string {
	return map[int8]string{
		MenuTypeMenu:      "菜单",
		MenuTypeButton:    "按钮",
		MenuTypeInterface: "接口",
	}
}

// MenuMetaData 菜单元数据
type MenuMetaData struct {
	Title  string `json:"title"`
	Icon   string `json:"icon"`
	Hidden bool   `json:"hidden"`
	Cache  bool   `json:"cache"`
	Frame  bool   `json:"frame"`
	Sort   int    `json:"sort"`
	Remark string `json:"remark"`
}

// GetMetaData 获取菜单元数据
func (m *AdminMenu) GetMetaData() *MenuMetaData {
	return &MenuMetaData{
		Title:  m.Title,
		Icon:   m.Icon,
		Hidden: m.IsHiddenMenu(),
		Cache:  m.IsCacheMenu(),
		Frame:  m.IsFrameMenu(),
		Sort:   m.Sort,
		Remark: m.Remark,
	}
}

// BuildMenuTree 构建菜单树
func BuildMenuTree(menus []*AdminMenu) []*AdminMenu {
	// 创建菜单映射
	menuMap := make(map[int]*AdminMenu)
	for _, menu := range menus {
		menuMap[menu.ID] = menu
		menu.Children = make([]*AdminMenu, 0)
	}

	// 构建树结构
	var roots []*AdminMenu
	for _, menu := range menus {
		if menu.ParentID == 0 {
			roots = append(roots, menu)
		} else if parent, exists := menuMap[menu.ParentID]; exists {
			parent.Children = append(parent.Children, menu)
		}
	}

	return roots
}
