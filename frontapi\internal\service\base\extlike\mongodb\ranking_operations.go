package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extlike/types"
)

// RankingOperations MongoDB排行榜操作处理器
type RankingOperations struct {
	client *MongoClient
}

// NewRankingOperations 创建排行榜操作处理器
func NewRankingOperations(client *MongoClient) *RankingOperations {
	return &RankingOperations{
		client: client,
	}
}

// UpdateHotRank 更新热门排行榜
func (ops *RankingOperations) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	rankingCollection := ops.client.getRankingCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": bson.M{
			"score":      score,
			"updated_at": time.Now(),
		},
		"$setOnInsert": bson.M{
			"item_id":    itemID,
			"item_type":  itemType,
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := rankingCollection.UpdateOne(ctx, filter, update, opts)
	if err != nil {
		return fmt.Errorf("更新热门排行榜失败: %w", err)
	}

	return nil
}

// GetHotRanking 获取热门排行榜
func (ops *RankingOperations) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	rankingCollection := ops.client.getRankingCollection()
	filter := bson.M{
		"item_type": itemType,
	}

	findOptions := options.Find()
	findOptions.SetSort(bson.M{"score": -1})
	findOptions.SetLimit(int64(limit))
	findOptions.SetProjection(bson.M{"item_id": 1, "_id": 0})

	cursor, err := rankingCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询热门排行榜失败: %w", err)
	}
	defer cursor.Close(ctx)

	var itemIDs []string
	for cursor.Next(ctx) {
		var item struct {
			ItemID string `bson:"item_id"`
		}

		if err := cursor.Decode(&item); err != nil {
			continue
		}

		itemIDs = append(itemIDs, item.ItemID)
	}

	return itemIDs, nil
}

// GetHotRankingWithScores 获取带分数的热门排行榜
func (ops *RankingOperations) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	rankingCollection := ops.client.getRankingCollection()
	filter := bson.M{
		"item_type": itemType,
	}

	findOptions := options.Find()
	findOptions.SetSort(bson.M{"score": -1})
	findOptions.SetLimit(int64(limit))
	findOptions.SetProjection(bson.M{"item_id": 1, "score": 1, "_id": 0})

	cursor, err := rankingCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询热门排行榜失败: %w", err)
	}
	defer cursor.Close(ctx)

	result := make(map[string]float64)
	for cursor.Next(ctx) {
		var item struct {
			ItemID string  `bson:"item_id"`
			Score  float64 `bson:"score"`
		}

		if err := cursor.Decode(&item); err != nil {
			continue
		}

		result[item.ItemID] = item.Score
	}

	return result, nil
}

// GetTrendingItems 获取趋势项目
func (ops *RankingOperations) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	return ops.calculateTrendingItems(ctx, itemType, timeRange, limit)
}

// calculateTrendingItems 实时计算趋势项目
func (ops *RankingOperations) calculateTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error) {
	likeCollection := ops.client.getLikeCollection()

	matchFilter := bson.M{
		"item_type": itemType,
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		matchFilter["status"] = "liked"
	}

	if timeRange != nil {
		timeFilter := bson.M{}
		if !timeRange.Start.IsZero() {
			timeFilter["$gte"] = timeRange.Start
		}
		if !timeRange.End.IsZero() {
			timeFilter["$lte"] = timeRange.End
		}
		if len(timeFilter) > 0 {
			matchFilter["timestamp"] = timeFilter
		}
	} else {
		matchFilter["timestamp"] = bson.M{
			"$gte": time.Now().AddDate(0, 0, -7),
		}
	}

	pipeline := []bson.M{
		{"$match": matchFilter},
		{
			"$group": bson.M{
				"_id":        "$item_id",
				"like_count": bson.M{"$sum": 1},
				"user_count": bson.M{"$addToSet": "$user_id"},
				"first_like": bson.M{"$min": "$timestamp"},
				"last_like":  bson.M{"$max": "$timestamp"},
			},
		},
		{
			"$project": bson.M{
				"item_id":    "$_id",
				"like_count": 1,
				"user_count": bson.M{"$size": "$user_count"},
				"score":      bson.M{"$multiply": []interface{}{"$like_count", bson.M{"$size": "$user_count"}}},
			},
		},
		{"$sort": bson.M{"score": -1}},
		{"$limit": limit},
	}

	cursor, err := likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("聚合计算趋势项目失败: %w", err)
	}
	defer cursor.Close(ctx)

	var trends []*types.LikeTrend
	rank := 1

	for cursor.Next(ctx) {
		var result struct {
			ItemID    string  `bson:"item_id"`
			LikeCount int64   `bson:"like_count"`
			UserCount int64   `bson:"user_count"`
			Score     float64 `bson:"score"`
		}

		if err := cursor.Decode(&result); err != nil {
			continue
		}

		trend := &types.LikeTrend{
			ItemID:    result.ItemID,
			ItemType:  itemType,
			Date:      time.Now(),
			LikeCount: result.LikeCount,
			UserCount: result.UserCount,
			Score:     result.Score,
			Rank:      rank,
		}

		trends = append(trends, trend)
		rank++
	}

	return trends, nil
}
