package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extlike/types"
)

// QueryOperations MongoDB查询操作处理器
type QueryOperations struct {
	client *MongoClient
}

// NewQueryOperations 创建查询操作处理器
func NewQueryOperations(client *MongoClient) *QueryOperations {
	return &QueryOperations{
		client: client,
	}
}

// BatchGetLikeStatus 批量获取点赞状态
func (ops *QueryOperations) BatchGetLikeStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	if len(items) == 0 {
		return make(map[string]bool), nil
	}

	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	result := make(map[string]bool)

	// 构建查询条件
	var filters []bson.M
	for itemID, itemType := range items {
		filter := bson.M{
			"user_id":   userID,
			"item_id":   itemID,
			"item_type": itemType,
		}

		if ops.client.config.ConsistencyLevel == "soft" {
			filter["status"] = "liked"
		}

		filters = append(filters, filter)
		result[itemID] = false // 默认值
	}

	// 执行批量查询
	likeCollection := ops.client.getLikeCollection()
	orFilter := bson.M{"$or": filters}

	cursor, err := likeCollection.Find(ctx, orFilter)
	if err != nil {
		return nil, fmt.Errorf("批量查询点赞状态失败: %w", err)
	}
	defer cursor.Close(ctx)

	// 处理查询结果
	for cursor.Next(ctx) {
		var record struct {
			UserID   string `bson:"user_id"`
			ItemID   string `bson:"item_id"`
			ItemType string `bson:"item_type"`
		}

		if err := cursor.Decode(&record); err != nil {
			continue
		}

		result[record.ItemID] = true
	}

	return result, nil
}

// BatchGetLikeCounts 批量获取点赞数量
func (ops *QueryOperations) BatchGetLikeCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	if len(items) == 0 {
		return make(map[string]int64), nil
	}

	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	result := make(map[string]int64)

	// 优先从统计表获取
	statsCollection := ops.client.getStatsCollection()
	var statsFilters []bson.M

	for itemID, itemType := range items {
		statsFilters = append(statsFilters, bson.M{
			"item_id":   itemID,
			"item_type": itemType,
		})
		result[itemID] = 0 // 默认值
	}

	// 查询统计表
	orFilter := bson.M{"$or": statsFilters}
	cursor, err := statsCollection.Find(ctx, orFilter)
	if err != nil {
		return nil, fmt.Errorf("查询统计数据失败: %w", err)
	}
	defer cursor.Close(ctx)

	foundItems := make(map[string]bool)
	for cursor.Next(ctx) {
		var stats struct {
			ItemID    string `bson:"item_id"`
			ItemType  string `bson:"item_type"`
			LikeCount int64  `bson:"like_count"`
		}

		if err := cursor.Decode(&stats); err != nil {
			continue
		}

		result[stats.ItemID] = stats.LikeCount
		foundItems[stats.ItemID] = true
	}

	// 对于统计表中没有的数据，实时计算
	var missingItems []struct {
		ItemID   string
		ItemType string
	}

	for itemID, itemType := range items {
		if !foundItems[itemID] {
			missingItems = append(missingItems, struct {
				ItemID   string
				ItemType string
			}{itemID, itemType})
		}
	}

	if len(missingItems) > 0 {
		for _, item := range missingItems {
			count, err := ops.getRealTimeLikeCount(ctx, item.ItemID, item.ItemType)
			if err == nil {
				result[item.ItemID] = count
				// 异步更新统计表
				go ops.updateStatsAsync(item.ItemID, item.ItemType, count)
			}
		}
	}

	return result, nil
}

// GetUserLikes 获取用户的点赞记录
func (ops *QueryOperations) GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"user_id": userID,
	}

	if itemType != "" {
		filter["item_type"] = itemType
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	// 设置查询选项
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"timestamp": -1})
	findOptions.SetLimit(int64(limit))
	findOptions.SetSkip(int64(offset))

	cursor, err := likeCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询用户点赞记录失败: %w", err)
	}
	defer cursor.Close(ctx)

	var records []*types.LikeRecord
	for cursor.Next(ctx) {
		var record types.LikeRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetItemLikers 获取项目的点赞用户
func (ops *QueryOperations) GetItemLikers(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	// 设置查询选项
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"timestamp": -1})
	findOptions.SetLimit(int64(limit))
	findOptions.SetSkip(int64(offset))

	cursor, err := likeCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询项目点赞用户失败: %w", err)
	}
	defer cursor.Close(ctx)

	var records []*types.LikeRecord
	for cursor.Next(ctx) {
		var record types.LikeRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetLikeHistory 获取点赞历史记录
func (ops *QueryOperations) GetLikeHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.LikeRecord, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"user_id": userID,
	}

	if itemType != "" {
		filter["item_type"] = itemType
	}

	// 添加时间范围过滤
	if timeRange != nil {
		timeFilter := bson.M{}
		if !timeRange.Start.IsZero() {
			timeFilter["$gte"] = timeRange.Start
		}
		if !timeRange.End.IsZero() {
			timeFilter["$lte"] = timeRange.End
		}
		if len(timeFilter) > 0 {
			filter["timestamp"] = timeFilter
		}
	}

	// 设置查询选项
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"timestamp": -1})
	findOptions.SetLimit(1000) // 限制最大返回数量

	cursor, err := likeCollection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, fmt.Errorf("查询点赞历史失败: %w", err)
	}
	defer cursor.Close(ctx)

	var records []*types.LikeRecord
	for cursor.Next(ctx) {
		var record types.LikeRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetUserLikeStats 获取用户点赞统计
func (ops *QueryOperations) GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	likeCollection := ops.client.getLikeCollection()

	// 使用聚合管道计算统计信息
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"user_id": userID,
			},
		},
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		pipeline[0]["$match"].(bson.M)["status"] = "liked"
	}

	pipeline = append(pipeline, []bson.M{
		{
			"$group": bson.M{
				"_id": bson.M{
					"item_type": "$item_type",
				},
				"count":      bson.M{"$sum": 1},
				"first_like": bson.M{"$min": "$timestamp"},
				"last_like":  bson.M{"$max": "$timestamp"},
			},
		},
		{
			"$group": bson.M{
				"_id":         nil,
				"total_likes": bson.M{"$sum": "$count"},
				"total_items": bson.M{"$sum": 1},
				"likes_by_type": bson.M{
					"$push": bson.M{
						"k": "$_id.item_type",
						"v": "$count",
					},
				},
				"first_like_at": bson.M{"$min": "$first_like"},
				"last_like_at":  bson.M{"$max": "$last_like"},
			},
		},
		{
			"$project": bson.M{
				"_id":           0,
				"total_likes":   1,
				"total_items":   1,
				"likes_by_type": bson.M{"$arrayToObject": "$likes_by_type"},
				"first_like_at": 1,
				"last_like_at":  1,
			},
		},
	}...)

	cursor, err := likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("聚合查询用户统计失败: %w", err)
	}
	defer cursor.Close(ctx)

	stats := &types.UserLikeStats{
		UserID:      userID,
		LikesByType: make(map[string]int64),
	}

	if cursor.Next(ctx) {
		var result struct {
			TotalLikes  int64            `bson:"total_likes"`
			TotalItems  int64            `bson:"total_items"`
			LikesByType map[string]int64 `bson:"likes_by_type"`
			FirstLikeAt *time.Time       `bson:"first_like_at"`
			LastLikeAt  *time.Time       `bson:"last_like_at"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("解析用户统计数据失败: %w", err)
		}

		stats.TotalLikes = result.TotalLikes
		stats.TotalItems = result.TotalItems
		stats.LikesByType = result.LikesByType
		stats.FirstLikeAt = result.FirstLikeAt
		stats.LastLikeAt = result.LastLikeAt

		// 计算平均每日点赞数
		if stats.FirstLikeAt != nil && stats.LastLikeAt != nil {
			days := stats.LastLikeAt.Sub(*stats.FirstLikeAt).Hours() / 24
			if days > 0 {
				stats.AvgLikesPerDay = float64(stats.TotalLikes) / days
			}
		}

		// 找出最喜欢的类型
		var maxCount int64
		for itemType, count := range stats.LikesByType {
			if count > maxCount {
				maxCount = count
				stats.MostLikedType = itemType
			}
		}

		// 计算参与度评分（简单实现）
		if stats.TotalLikes > 0 {
			stats.EngagementScore = float64(stats.TotalLikes) / float64(stats.TotalItems+1) * 100
		}
	}

	return stats, nil
}

// GetItemLikeStats 获取项目点赞统计
func (ops *QueryOperations) GetItemLikeStats(ctx context.Context, itemID, itemType string) (*types.ItemLikeStats, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	// 使用聚合管道计算统计信息
	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id":           nil,
				"total_likes":   bson.M{"$sum": 1},
				"total_users":   bson.M{"$sum": 1},
				"unique_users":  bson.M{"$addToSet": "$user_id"},
				"first_like_at": bson.M{"$min": "$timestamp"},
				"last_like_at":  bson.M{"$max": "$timestamp"},
				"likes_by_hour": bson.M{
					"$push": bson.M{
						"hour": bson.M{"$hour": "$timestamp"},
					},
				},
			},
		},
		{
			"$project": bson.M{
				"_id":           0,
				"total_likes":   1,
				"total_users":   1,
				"unique_users":  bson.M{"$size": "$unique_users"},
				"first_like_at": 1,
				"last_like_at":  1,
			},
		},
	}

	cursor, err := likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("聚合查询项目统计失败: %w", err)
	}
	defer cursor.Close(ctx)

	stats := &types.ItemLikeStats{
		ItemType:     itemType,
		ItemID:       itemID,
		LikesPerHour: make(map[int]int64),
		LikesPerDay:  make(map[string]int64),
	}

	if cursor.Next(ctx) {
		var result struct {
			TotalLikes  int64      `bson:"total_likes"`
			TotalUsers  int64      `bson:"total_users"`
			UniqueUsers int64      `bson:"unique_users"`
			FirstLikeAt *time.Time `bson:"first_like_at"`
			LastLikeAt  *time.Time `bson:"last_like_at"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("解析项目统计数据失败: %w", err)
		}

		stats.TotalLikes = result.TotalLikes
		stats.TotalUsers = result.TotalUsers
		stats.UniqueUsers = result.UniqueUsers
		stats.FirstLikeAt = result.FirstLikeAt
		stats.LastLikeAt = result.LastLikeAt

		// 计算热度评分（基于点赞数和时间）
		if stats.TotalLikes > 0 {
			timeFactor := 1.0
			if stats.LastLikeAt != nil {
				hoursSinceLastLike := time.Since(*stats.LastLikeAt).Hours()
				timeFactor = 1.0 / (1.0 + hoursSinceLastLike/24.0) // 时间衰减因子
			}
			stats.HotScore = float64(stats.TotalLikes) * timeFactor
		}

		// 计算趋势评分（最近24小时的点赞数）
		recentLikes, _ := ops.getRecentLikeCount(ctx, itemID, itemType, 24*time.Hour)
		stats.TrendingScore = float64(recentLikes)
	}

	return stats, nil
}

// getRealTimeLikeCount 实时计算点赞数量
func (ops *QueryOperations) getRealTimeLikeCount(ctx context.Context, itemID, itemType string) (int64, error) {
	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	count, err := likeCollection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("实时计算点赞数量失败: %w", err)
	}

	return count, nil
}

// getRecentLikeCount 获取最近一段时间的点赞数量
func (ops *QueryOperations) getRecentLikeCount(ctx context.Context, itemID, itemType string, duration time.Duration) (int64, error) {
	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
		"timestamp": bson.M{
			"$gte": time.Now().Add(-duration),
		},
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	count, err := likeCollection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("查询最近点赞数量失败: %w", err)
	}

	return count, nil
}

// updateStatsAsync 异步更新统计数据
func (ops *QueryOperations) updateStatsAsync(itemID, itemType string, count int64) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	statsCollection := ops.client.getStatsCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": bson.M{
			"like_count": count,
			"updated_at": time.Now(),
		},
		"$setOnInsert": bson.M{
			"item_id":    itemID,
			"item_type":  itemType,
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, _ = statsCollection.UpdateOne(ctx, filter, update, opts)
}
