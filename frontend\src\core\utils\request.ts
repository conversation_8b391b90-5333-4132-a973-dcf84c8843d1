import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { ENV_CONFIG, API_CONFIG } from '@/config/api';

// 为了TypeScript类型声明
declare global {
  interface Window {
    ElMessage?: {
      error: (message: string) => void;
      success: (message: string) => void;
    }
  }
}

/**
 * 统一的API响应格式
 */
interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
  timestamp?: number;
  traceId?: string;
}

/**
 * 分页响应数据格式
 */
interface PageResponse<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 请求参数格式 - 支持两种格式
 * 1. 普通请求: { data: {...} }
 * 2. 分页请求: { data: {...}, page: { pageNo: 1, pageSize: 10 } }
 */
interface RequestParams<T = any> {
  data?: T;
  page?: {
    pageNo: number;
    pageSize: number;
  };
}

/**
 * 请求配置选项
 */
export interface RequestOptions {
  /** 是否显示loading */
  showLoading?: boolean;
  /** 是否显示错误提示 */
  showError?: boolean;
  /** 是否显示成功提示 */
  showSuccess?: boolean;
  /** 自定义成功消息 */
  successMessage?: string;
  /** 自定义错误消息 */
  errorMessage?: string;
  /** 是否启用缓存 */
  cache?: boolean;
  /** 缓存时间(ms) */
  cacheTime?: number;
  /** 重试次数 */
  retry?: number;
  /** 重试延迟(ms) */
  retryDelay?: number;
  /** 超时时间(ms) */
  timeout?: number;
}

/**
 * 加密/解密接口（预留）
 */
interface CryptoInterface {
  encrypt: (data: any) => any;
  decrypt: (data: any) => any;
}

/**
 * 默认的加密实现（开发阶段不加密）
 */
const defaultCrypto: CryptoInterface = {
  encrypt: (data: any) => data,
  decrypt: (data: any) => data,
};

/**
 * 当前使用的加密实例（可在生产环境替换）
 */
let cryptoInstance: CryptoInterface = defaultCrypto;

/**
 * 设置加密实例（预留接口）
 */
export const setCryptoInstance = (crypto: CryptoInterface) => {
  cryptoInstance = crypto;
  console.log('🔐 已设置加密实例');
};

/**
 * 创建axios实例
 * 在开发环境使用Vite代理，生产环境使用完整的API地址
 */
const createAxiosInstance = (): AxiosInstance => {
  const isDevelopment = ENV_CONFIG.APP_ENV === 'development';
  
  // 开发环境使用API前缀（通过Vite代理），生产环境使用完整API地址
  const baseURL = isDevelopment 
    ? API_CONFIG.FULL_PREFIX  // 开发环境: /api/v1
    : `${ENV_CONFIG.API_BASE_URL}${API_CONFIG.FULL_PREFIX}`; // 生产环境: http://127.0.0.1:8080/api/v1
  
  console.log(`🔧 Request配置 - 环境: ${ENV_CONFIG.APP_ENV}, BaseURL: ${baseURL}`);
  
  return axios.create({
    baseURL,
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  });
};

const service: AxiosInstance = createAxiosInstance();

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 加密请求数据（预留）
    if (config.data) {
      config.data = cryptoInstance.encrypt(config.data);
    }
    
    // 开发环境下打印请求信息
    if (ENV_CONFIG.APP_ENV === 'development') {
      console.log(`🚀 发起请求: ${config.method?.toUpperCase()} ${config.url}`, {
        原始数据: config.data,
        params: config.params
      });
    }
    
    return config;
  },
  (error) => {
    console.error('❌ 请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;
    
    // 开发环境下打印响应信息
    if (ENV_CONFIG.APP_ENV === 'development') {
      console.log(`📥 收到响应: ${response.config.url}`, {
        status: response.status,
        code: data.code,
        message: data.message,
        data: data.data
      });
    }
    
    // 解密响应数据（预留）
    const decryptedData = cryptoInstance.decrypt(data);
    
    // 添加时间戳
    if (!decryptedData.timestamp) {
      decryptedData.timestamp = Date.now();
    }
    
    // 直接返回完整的响应数据，让调用方处理业务逻辑
    return decryptedData;
  },
  (error) => {
    const { response } = error;
    
    // 开发环境下打印错误信息
    if (ENV_CONFIG.APP_ENV === 'development') {
      console.error('❌ 响应错误:', {
        url: error.config?.url,
        status: response?.status,
        message: error.message,
        data: response?.data
      });
    }
    
    // 处理特殊状态码
    if (response?.status === 401 || response?.status === 403) {
      // 清除可能已过期的token
      localStorage.removeItem('token');
      console.warn('🔒 权限验证失败，已清除token');
    }
    
    // 构造标准错误响应格式
    const errorResponse: ApiResponse = {
      code: response?.status || -1,
      message: response?.data?.message || response?.data?.msg || error.message || '请求失败',
      data: null,
      timestamp: Date.now()
    };
    
    // 返回标准格式的错误响应，而不是抛出异常
    return errorResponse;
  }
);

/**
 * 包装请求参数
 */
const wrapRequestData = <T = any>(data?: T, page?: { pageNo: number; pageSize: number }): RequestParams<T> => {
  const params: RequestParams<T> = {};
  
  if (data !== undefined) {
    params.data = data;
  }
  
  if (page) {
    params.page = page;
  }
  
  return params;
};

/**
 * 通用请求方法
 * @param config 请求配置
 * @returns Promise<ApiResponse>
 */
export const request = <T = any>(config: InternalAxiosRequestConfig): Promise<ApiResponse<T>> => {
  return service.request(config);
};

/**
 * GET请求
 * @param url 请求地址
 * @param params 查询参数
 * @param options 请求选项
 * @returns Promise<ApiResponse>
 */
export const get = <T = any>(url: string, params?: any, options?: RequestOptions): Promise<ApiResponse<T>> => {
  return service.get(url, { params, ...options });
};

/**
 * POST请求（自动包装data格式）
 * @param url 请求地址
 * @param data 请求数据
 * @param page 分页参数（可选）
 * @param options 请求选项
 * @returns Promise<ApiResponse>
 */
export const post = <T = any>(
  url: string, 
  data?: any, 
  page?: { pageNo: number; pageSize: number },
  options?: RequestOptions
): Promise<ApiResponse<T>> => {
  const requestData = wrapRequestData(data, page);
  return service.post(url, requestData, options);
};

/**
 * PUT请求（自动包装data格式）
 * @param url 请求地址
 * @param data 请求数据
 * @param options 请求选项
 * @returns Promise<ApiResponse>
 */
export const put = <T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> => {
  const requestData = wrapRequestData(data);
  return service.put(url, requestData, options);
};

/**
 * DELETE请求（自动包装data格式）
 * @param url 请求地址
 * @param data 请求数据（可选）
 * @param options 请求选项
 * @returns Promise<ApiResponse>
 */
export const del = <T = any>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>> => {
  const requestData = wrapRequestData(data);
  return service.delete(url, { data: requestData, ...options });
};

/**
 * 分页请求的便捷方法
 * @param url 请求地址
 * @param pageNo 页码
 * @param pageSize 每页大小
 * @param data 查询条件（可选）
 * @param options 请求选项
 * @returns Promise<ApiResponse<PageResponse>>
 */
export const getPage = <T = any>(
  url: string,
  pageNo: number = 1,
  pageSize: number = 10,
  data?: any,
  options?: RequestOptions
): Promise<ApiResponse<PageResponse<T>>> => {
  return post(url, data, { pageNo, pageSize }, options);
};

/**
 * POST分页列表请求
 * @param url 请求地址
 * @param params 请求参数
 * @param options 请求选项
 * @returns Promise<ApiResponse<PageResponse>>
 */
export const postPageList = <T = any>(
  url: string, 
  params?: RequestParams, 
  options?: RequestOptions
): Promise<ApiResponse<PageResponse<T>>> => {
  return post(url, params?.data, params?.page, options);
};

/**
 * 简单的POST分页请求（兼容现有代码）
 * @param url 请求URL
 * @param params 请求参数
 * @param headers 请求头选项
 * @returns Promise<ApiResponse<PageResponse<T>>>
 */
export const postPageListSimple = <T = any>(
  url: string,
  params?: any,
  headers?: Record<string, string>
): Promise<ApiResponse<PageResponse<T>>> => {
  return request<PageResponse<T>>({
    url,
    method: 'POST',
    data: params,
    headers
  });
};

/**
 * 业务成功判断工具函数
 * @param response API响应
 * @returns 是否业务成功
 */
export const isSuccess = (response: ApiResponse): boolean => {
  return response.code === 2000;
};

/**
 * 获取响应数据的便捷方法
 * @param response API响应
 * @returns 响应数据或null
 */
export const getData = <T = any>(response: ApiResponse<T>): T | null => {
  return isSuccess(response) ? response.data : null;
};

/**
 * 获取错误信息的便捷方法
 * @param response API响应
 * @returns 错误信息
 */
export const getErrorMessage = (response: ApiResponse): string => {
  return response.message || '未知错误';
};


// 导出类型定义
export type { ApiResponse, PageResponse, RequestParams, CryptoInterface };

// 导出默认实例（保持向后兼容）
export default service;