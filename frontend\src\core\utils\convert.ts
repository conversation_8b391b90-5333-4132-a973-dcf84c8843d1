/**
 * 转换工具类
 * 提供各种数据类型转换功能
 */

export class ConvertUtils {
  /**
   * 将字符串转换为数字
   */
  static toNumber(value: any, defaultValue: number = 0): number {
    if (typeof value === 'number') return value
    if (typeof value === 'string') {
      const num = parseFloat(value)
      return isNaN(num) ? defaultValue : num
    }
    return defaultValue
  }

  /**
   * 将值转换为字符串
   */
  static toString(value: any, defaultValue: string = ''): string {
    if (value === null || value === undefined) return defaultValue
    return String(value)
  }

  /**
   * 将值转换为布尔值
   */
  static toBoolean(value: any): boolean {
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') {
      return ['true', '1', 'yes', 'on'].includes(value.toLowerCase())
    }
    if (typeof value === 'number') {
      return value !== 0
    }
    return Boolean(value)
  }

  /**
   * 将对象转换为数组
   */
  static objectToArray<T>(obj: Record<string, T>): Array<{ key: string; value: T }> {
    return Object.entries(obj).map(([key, value]) => ({ key, value }))
  }

  /**
   * 将数组转换为对象
   */
  static arrayToObject<T>(
    arr: T[],
    keyField: keyof T,
    valueField?: keyof T
  ): Record<string, any> {
    const result: Record<string, any> = {}
    arr.forEach(item => {
      const key = String(item[keyField])
      result[key] = valueField ? item[valueField] : item
    })
    return result
  }

  /**
   * 将字节转换为可读格式
   */
  static bytesToSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * 将秒数转换为时间格式
   */
  static secondsToTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = Math.floor(seconds % 60)

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  /**
   * 将时间格式转换为秒数
   */
  static timeToSeconds(time: string): number {
    const parts = time.split(':').map(Number)
    if (parts.length === 2) {
      return parts[0] * 60 + parts[1]
    }
    if (parts.length === 3) {
      return parts[0] * 3600 + parts[1] * 60 + parts[2]
    }
    return 0
  }

  /**
   * 将RGB转换为HEX
   */
  static rgbToHex(r: number, g: number, b: number): string {
    return '#' + [r, g, b].map(x => {
      const hex = x.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }).join('')
  }

  /**
   * 将HEX转换为RGB
   */
  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  /**
   * 将驼峰命名转换为短横线命名
   */
  static camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }

  /**
   * 将短横线命名转换为驼峰命名
   */
  static kebabToCamel(str: string): string {
    return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  /**
   * 将下划线命名转换为驼峰命名
   */
  static snakeToCamel(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
  }

  /**
   * 将驼峰命名转换为下划线命名
   */
  static camelToSnake(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1_$2').toLowerCase()
  }

  /**
   * 将查询字符串转换为对象
   */
  static queryStringToObject(queryString: string): Record<string, string> {
    const params = new URLSearchParams(queryString)
    const result: Record<string, string> = {}
    params.forEach((value, key) => {
      result[key] = value
    })
    return result
  }

  /**
   * 将对象转换为查询字符串
   */
  static objectToQueryString(obj: Record<string, any>): string {
    const params = new URLSearchParams()
    Object.entries(obj).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, String(value))
      }
    })
    return params.toString()
  }

  /**
   * 将Base64转换为Blob
   */
  static base64ToBlob(base64: string, mimeType: string = 'application/octet-stream'): Blob {
    const byteCharacters = atob(base64)
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    return new Blob([byteArray], { type: mimeType })
  }

  /**
   * 将Blob转换为Base64
   */
  static blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result.split(',')[1])
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 将文件大小转换为字节
   */
  static sizeToBytes(size: string): number {
    const units: Record<string, number> = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024,
      'TB': 1024 * 1024 * 1024 * 1024
    }
    
    const match = size.match(/^([\d.]+)\s*(\w+)$/i)
    if (!match) return 0
    
    const [, value, unit] = match
    const multiplier = units[unit.toUpperCase()] || 1
    return parseFloat(value) * multiplier
  }
}

export default ConvertUtils