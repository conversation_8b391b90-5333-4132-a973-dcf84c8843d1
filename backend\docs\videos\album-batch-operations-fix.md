# 视频专辑批量操作修复文档

## 问题描述

用户反馈视频专辑管理页面中的以下功能无效：
1. **状态修改**：单个专辑的状态修改按钮点击无效果
2. **批量删除**：选中多个专辑后，批量删除按钮点击无效果
3. **批量状态修改**：选中多个专辑后，批量启用/禁用按钮点击无效果

## 问题分析

### 前端问题
1. **事件绑定不完整**：主页面中表格组件的事件绑定缺少关键事件
2. **API函数缺失**：批量删除API函数未定义
3. **事件处理函数缺失**：缺少状态修改和批量操作的处理函数

### 后端问题
1. **路由缺失**：批量删除路由未注册
2. **方法返回值处理错误**：批量删除方法的返回值处理不正确

## 修复方案

### 前端修复

#### 1. 添加批量删除API函数 (albums.ts)
```typescript
// 批量删除接口
interface BatchDeleteData {
    ids: string[];
}

/**
 * 批量删除视频专辑
 */
export function batchDeleteVideoAlbum(data: { data: BatchDeleteData }) {
    return request({
        url: '/video-albums/batch-delete',
        method: 'post',
        data: data
    });
}
```

#### 2. 修复表格组件事件绑定 (index.vue)
```vue
<!-- 表格 -->
<AlbumTable
  :loading="loading"
  :albumList="albumList"
  :pagination="pagination"
  @view="handleDetail"
  @edit="handleEdit"
  @delete="handleDelete"
  @change-status="handleChangeStatus"
  @current-change="handleCurrentChange"
  @size-change="handleSizeChange"
  @batch-status="handleBatchStatus"
  @batch-delete="handleBatchDelete"
/>
```

#### 3. 添加事件处理函数 (index.vue)
```typescript
// 状态修改（表格组件使用的事件名）
const handleChangeStatus = async (id: string, status: number) => {
    try {
        const {response} = await updateVideoAlbumStatus(id, status) as any;
        
        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('状态更新成功');
            getList();
        } else {
            ElMessage.error(response.data.message || '状态更新失败');
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        ElMessage.error('状态更新失败');
    }
};

// 批量状态更新
const handleBatchStatus = async (status: number, albums: VideoAlbum[]) => {
    if (!albums || albums.length === 0) {
        ElMessage.warning('请先选择要操作的专辑');
        return;
    }

    try {
        await ElMessageBox.confirm(
            `确定要${status === 1 ? '启用' : '禁用'}选中的 ${albums.length} 个专辑吗？`,
            '批量操作确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        const ids = albums.map(album => album.id);
        const {response} = await batchUpdateVideoAlbumStatus({ 
            data: { ids, status } 
        }) as any;
        
        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success(`批量${status === 1 ? '启用' : '禁用'}成功`);
            getList();
        } else {
            ElMessage.error(response.data.message || '批量操作失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量状态更新失败:', error);
            ElMessage.error('批量操作失败');
        }
    }
};

// 批量删除
const handleBatchDelete = async (albums: VideoAlbum[]) => {
    if (!albums || albums.length === 0) {
        ElMessage.warning('请先选择要删除的专辑');
        return;
    }

    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${albums.length} 个专辑吗？此操作不可恢复！`,
            '批量删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        const ids = albums.map(album => album.id);
        const {response} = await batchDeleteVideoAlbum({ 
            data: { ids } 
        }) as any;
        
        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('批量删除成功');
            getList();
        } else {
            ElMessage.error(response.data.message || '批量删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除失败:', error);
            ElMessage.error('批量删除失败');
        }
    }
};
```

### 后端修复

#### 1. 添加批量删除控制器方法 (video_album_controller.go)
```go
// BatchDeleteVideoAlbum 批量删除视频专辑
func (h *VideoAlbumController) BatchDeleteVideoAlbum(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 定义请求结构
	type BatchDeleteRequest struct {
		IDs []string `json:"ids" validate:"required"`
	}

	var req BatchDeleteRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 批量删除
	deletedCount, err := h.videoAlbumService.BatchDelete(c.Context(), req.IDs)
	if err != nil {
		return h.InternalServerError(c, "批量删除视频专辑失败: "+err.Error())
	}

	// 返回成功响应
	return h.Success(c, fiber.Map{
		"message":      "批量删除视频专辑成功",
		"deletedCount": deletedCount,
	})
}
```

#### 2. 添加批量删除路由 (video_routes.go)
```go
// 专辑管理接口
albums.Post("/list", videoAlbumController.ListVideoAlbums)
albums.Post("/detail/:id?", videoAlbumController.GetVideoAlbum)
albums.Post("/add", videoAlbumController.CreateVideoAlbum)
albums.Post("/update/:id?", videoAlbumController.UpdateVideoAlbum)
albums.Post("/delete/:id?", videoAlbumController.DeleteVideoAlbum)
albums.Post("/update-status/:id?", videoAlbumController.UpdateVideoAlbumStatus)
albums.Post("/batch-update-status", videoAlbumController.BatchUpdateVideoAlbumStatus)
albums.Post("/batch-delete", videoAlbumController.BatchDeleteVideoAlbum)  // 新增
```

## 修复结果

### ✅ 已修复的功能

1. **状态修改**
   - 单个专辑的状态修改按钮现在可以正常工作
   - 点击启用/禁用按钮会弹出确认对话框
   - 操作成功后会显示成功提示并刷新列表

2. **批量删除**
   - 选中多个专辑后，批量删除按钮可以正常工作
   - 会显示确认对话框，显示要删除的专辑数量
   - 操作成功后会显示删除的专辑数量

3. **批量状态修改**
   - 选中多个专辑后，批量启用/禁用按钮可以正常工作
   - 会显示确认对话框，显示要操作的专辑数量
   - 操作成功后会显示成功提示并刷新列表

### 🔧 技术改进

1. **事件绑定完整性**：确保所有表格组件事件都正确绑定到主页面
2. **API完整性**：添加了缺失的批量删除API函数
3. **错误处理**：完善了所有操作的错误处理和用户反馈
4. **用户体验**：添加了确认对话框和操作反馈

### 📋 测试建议

1. **单个状态修改**：选择一个专辑，点击启用/禁用按钮，确认操作成功
2. **批量状态修改**：选择多个专辑，点击批量启用/禁用，确认操作成功
3. **批量删除**：选择多个专辑，点击批量删除，确认操作成功
4. **边界条件**：测试未选择专辑时点击批量操作按钮的提示
5. **错误处理**：测试网络错误或服务器错误时的错误提示

现在视频专辑管理页面的所有批量操作和状态修改功能都应该可以正常工作了。 