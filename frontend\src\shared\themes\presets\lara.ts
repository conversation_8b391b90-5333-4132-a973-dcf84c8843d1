/**
 * Lara主题预设
 */
import { definePreset } from '@primeuix/themes';
import <PERSON> from '@primeuix/themes/lara';

// 定义Lara预设
export const LaraPreset = definePreset(<PERSON>, {
    name: 'lara',
    options: {
        darkMode: true,
        cssLayer: false
    },
    // global: {
    //     css: `
    //   :root {
    //     --font-family: 'Inter var', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    //   }

    //   * {
    //     box-sizing: border-box;
    //   }

    //   body {
    //     font-family: var(--font-family);
    //     -webkit-font-smoothing: antialiased;
    //     -moz-osx-font-smoothing: grayscale;
    //   }

    //   /* 确保PrimeVue组件样式正确应用 */
    //   .p-component {
    //     font-family: var(--font-family);
    //   }

    //   /* 确保暗色模式正确应用 */
    //   .app-dark {
    //     color-scheme: dark;
    //   }
    // `
    // },
    semantic: {
        colorScheme: {
            light: {
                primary: {
                    color: '{primary.500}',
                    inverseColor: '#ffffff',
                    hoverColor: '{primary.600}',
                    activeColor: '{primary.700}'
                },
                highlight: {
                    background: '{primary.500}',
                    focusBackground: '{primary.700}',
                    color: '#ffffff',
                    focusColor: '#ffffff'
                },
                surface: {
                    0: '#ffffff',
                    50: '{surface.50}',
                    100: '{surface.100}',
                    200: '{surface.200}',
                    300: '{surface.300}',
                    400: '{surface.400}',
                    500: '{surface.500}',
                    600: '{surface.600}',
                    700: '{surface.700}',
                    800: '{surface.800}',
                    900: '{surface.900}',
                    950: '{surface.950}'
                },
                formField: {
                    hoverBorderColor: '{primary.color}'
                }
            },
            dark: {
                primary: {
                    color: '{primary.400}',
                    inverseColor: '#1e1e1e',
                    hoverColor: '{primary.300}',
                    activeColor: '{primary.200}'
                },
                highlight: {
                    background: 'rgba(130, 246, 59, .16)',
                    focusBackground: 'rgba(130, 246, 59, .24)',
                    color: 'rgba(255,255,255,.87)',
                    focusColor: 'rgba(255,255,255,.87)'
                },
                surface: {
                    0: '#121212',
                    50: '{surface.950}',
                    100: '{surface.900}',
                    200: '{surface.800}',
                    300: '{surface.700}',
                    400: '{surface.600}',
                    500: '{surface.500}',
                    600: '{surface.400}',
                    700: '{surface.300}',
                    800: '{surface.200}',
                    900: '{surface.100}',
                    950: '{surface.50}'
                },
                formField: {
                    hoverBorderColor: '{primary.color}'
                }
            }
        },
        primary: {
            50: '{primary.50}',
            100: '{primary.100}',
            200: '{primary.200}',
            300: '{primary.300}',
            400: '{primary.400}',
            500: '{primary.500}',
            600: '{primary.600}',
            700: '{primary.700}',
            800: '{primary.800}',
            900: '{primary.900}',
            950: '{primary.950}'
        },
        slate: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
            950: '#020617'
        },
        blue: {
            50: "#eff6ff",
            100: "#dbeafe",
            200: "#bfdbfe",
            300: "#93c5fd",
            400: "#60a5fa",
            500: "#3b82f6",
            600: "#2563eb",
            700: "#1d4ed8",
            800: "#1e40af",
            900: "#1e3a8a",
            950: "#172554"
        }
    }
});