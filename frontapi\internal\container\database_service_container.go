package container

import (
	sysRepo "frontapi/internal/repository/sys"
	sysService "frontapi/internal/service/sys"
)

// InitDatabaseServices 初始化数据库相关服务
func InitDatabaseServices(builder *ServiceBuilder) {
	// 初始化数据库仓库
	databaseRepo := sysRepo.NewDatabaseRepository(builder.DB())

	// 初始化数据库服务
	databaseSvc := sysService.NewDatabaseService(databaseRepo)

	// 注册到服务容器
	builder.Services().DatabaseService = databaseSvc
}
