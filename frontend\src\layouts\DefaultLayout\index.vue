<template>
  <div class="main-layout">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
    </div>
    
    <!-- 主要布局容器 -->
    <div class="layout-container">
      <!-- 桌面端布局 -->
      <div v-if="!isMobileView" class="desktop-layout">
        <!-- 顶部导航栏 -->
        <AppHeader 
          ref="headerRef"
          class="app-header-wrapper"
          @toggledDrawer="handleToggledDrawer"
        />
        
        <!-- 导航菜单栏 -->
        <AppNavigation 
          ref="navigationRef"
          class="app-navigation-wrapper"
        />
      </div>
      
      <!-- 移动端布局 -->
      <div v-else class="mobile-layout">
        <MobileHeader 
          ref="mobileHeaderRef"
          @toggleDrawer="handleToggledDrawer"
        />
      </div>
      
      <!-- 滚动后显示的菜单按钮 -->
      <!-- <div 
        v-if="isScrolled && !isMobileView" 
        class="toggle-menu-drawer"
        @click="handleToggledDrawer('left')"
      >
        <i class="pi pi-bars"></i>
      </div> -->
      
      <!-- 主内容区域 -->
      <main class="main-content" :class="{ 'mobile-view': isMobileView }">
        <div class="content-wrapper">
          <RouterView v-slot="{ Component, route }">
            <Transition 
              :name="getTransitionName(route)" 
              mode="out-in"
              @enter="onPageEnter"
              @leave="onPageLeave"
            >
              <KeepAlive :include="keepAliveComponents">
                <component 
                  :is="Component" 
                  :key="route.fullPath"
                  class="page-component"
                />
              </KeepAlive>
            </Transition>
          </RouterView>
        </div>
      </main>
      
      <!-- 页脚 -->
      <AppFooter 
        class="app-footer-wrapper"
      />
    </div>
    
    <!-- 全局加载指示器 -->
    <Transition name="loading">
      <div v-if="isLoading" class="global-loading">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p class="loading-text">{{ t('common.loading') }}</p>
        </div>
      </div>
    </Transition>
    
    <!-- 侧边抽屉 -->
    <Drawer 
      ref="drawerRef" 
      :visible="drawerVisible" 
      :position="drawerPosition"
      @update:visible="drawerVisible = $event"
      @close="drawerVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { useTranslation } from '@/core/plugins/i18n/composables'
import { useThemeStore } from '@/store/modules/theme'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { RouterView, useRoute } from 'vue-router'
import AppFooter from './AppFooter.vue'
import AppHeader from './AppHeader.vue'
import AppNavigation from './AppNavigation.vue'
import Drawer from './Drawer.vue'
import MobileHeader from './MobileHeader.vue'

// 组合式API
const route = useRoute()
const drawerRef = ref()
const drawerVisible = ref(false)
const drawerPosition = ref('left')
const themeStore = useThemeStore()
const { t } = useTranslation()
// 组件引用
const headerRef = ref()
const navigationRef = ref()
const mobileHeaderRef = ref()

// 响应式数据
const isPageTransitioning = ref(false)
const isLoading = ref(false)

const isMobileView = ref(false)

// 需要缓存的组件列表
const keepAliveComponents = computed(() => [
  'HomePage',
  'VideoList',
  'CommunityList'
])

// 方法
const getTransitionName = (currentRoute: any) => {
  // 根据路由层级决定过渡动画
  const depth = currentRoute.path.split('/').length
  
  if (currentRoute.meta?.transition) {
    return currentRoute.meta.transition
  }
  
  // 默认过渡动画
  return depth > 2 ? 'slide-left' : 'fade'
}

const onPageEnter = (el: Element) => {
  isPageTransitioning.value = true
  // 页面进入时的处理
  nextTick(() => {
    // 滚动到顶部
    if (route.meta?.scrollToTop !== false) {
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  })
}

const onPageLeave = (el: Element) => {
  // 页面离开时的处理
  isPageTransitioning.value = false
}

const handleToggledDrawer = (position: string = 'left') => {
  // 处理菜单切换
  drawerPosition.value = position
  drawerVisible.value = !drawerVisible.value
  drawerRef.value?.toggleDrawer(position)
}


// 窗口大小变化处理
const handleResize = () => {
  // 更新视口信息
  const width = window.innerWidth
  const height = window.innerHeight
  document.documentElement.style.setProperty('--viewport-width', `${width}px`)
  document.documentElement.style.setProperty('--viewport-height', `${height}px`)
  
  // 检测是否为移动视图
  isMobileView.value = width < 768
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    headerRef.value?.focusSearch?.()
  }
  
  // ESC 关闭移动端菜单
  if (event.key === 'Escape') {
    navigationRef.value?.closeMobileMenu?.()
    drawerVisible.value = false
  }
}

// 路由变化监听
watch(
  () => route.path,
  (newPath, oldPath) => {
    // 路由变化时的处理
    document.documentElement.setAttribute('data-current-route', newPath)
    
    // 关闭移动端菜单
    navigationRef.value?.closeMobileMenu?.()
    drawerVisible.value = false
  },
  { immediate: true }
)

// 主题变化监听
watch(
  () => themeStore.currentThemeName,
  (newTheme) => {
    // 主题变化时更新CSS变量
    document.documentElement.setAttribute('data-theme', newTheme)
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  // 添加事件监听
  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleKeydown)

  
  // 初始化视口信息
  handleResize()

  
  // 设置初始路由
  document.documentElement.setAttribute('data-current-route', route.path)
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleKeydown)

})
</script>

<style scoped lang="scss">
// 主布局
.main-layout {
  position: relative;
  min-height: 100vh;
  background: var(--surface-ground);
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

// 背景装饰
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  opacity: 0.6;
  
  .gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    opacity: 0.4;
    animation: float 8s ease-in-out infinite;
    
    &.orb-1 {
      width: 400px;
      height: 400px;
      background: linear-gradient(135deg, var(--primary-200), var(--primary-400));
      top: 10%;
      left: -10%;
      animation-delay: 0s;
    }
    
    &.orb-2 {
      width: 300px;
      height: 300px;
      background: linear-gradient(135deg, var(--pink-200), var(--purple-300));
      top: 60%;
      right: -5%;
      animation-delay: 3s;
    }
    
    &.orb-3 {
      width: 350px;
      height: 350px;
      background: linear-gradient(135deg, var(--blue-200), var(--indigo-300));
      bottom: -10%;
      left: 30%;
      animation-delay: 1.5s;
    }
  }
}

// 布局容器
.layout-container {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
}

// 主内容区域
.main-content {
  flex: 1;
  padding-top: 147px; // 头部高度 + 导航高度
  transition: padding-top 0.3s ease;
  
  &.mobile-view {
    padding-top: 120px; // 移动端头部高度
  }
  
  .content-wrapper {
    max-width: 1280px;
    margin: 0 auto;
    padding: 1.5rem;
    width: 100%;
    
    @media (max-width: 768px) {
      padding: 1rem;
    }
  }
}

// 滚动后显示的菜单按钮
.toggle-menu-drawer {
  position: fixed;
  top: 5rem;
  left: 1.5rem;
  z-index: 49;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--primary-color);
  
  &:hover {
    transform: scale(1.05);
    background: var(--primary-color);
    color: white;
  }
  
  &:active {
    transform: scale(0.95);
  }
}

// 全局加载指示器
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-content {
    background: var(--surface-card);
    border-radius: 12px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    
    .loading-spinner {
      width: 48px;
      height: 48px;
      border: 4px solid var(--surface-300);
      border-top-color: var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      color: var(--text-color);
      font-weight: 500;
    }
  }
}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-enter-active,
.loading-leave-active {
  transition: opacity 0.3s ease;
}

.loading-enter-from,
.loading-leave-to {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

// 移动端样式
@media (max-width: 768px) {
  .desktop-layout {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-layout {
    display: none;
  }
}
</style>