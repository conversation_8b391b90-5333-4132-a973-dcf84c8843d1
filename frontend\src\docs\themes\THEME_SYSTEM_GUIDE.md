# 主题系统使用指南

## 概述

我们的主题系统采用了基于PrimeVue和CSS变量的架构，支持Tailwind CSS集成，实现了以下功能：

- 多主题切换（包括浅色和深色模式）
- 基于CSS变量的主题定制
- 响应系统主题偏好
- 持久化用户主题选择
- 与国际化(i18n)系统无缝整合
- 适配PrimeVue组件和自定义组件

## 架构设计

主题系统由以下部分组成：

1. **主题管理器** (`/src/shared/themes/theme-manager.ts`)：
   - 核心逻辑和状态管理
   - 主题切换和应用
   - 系统主题检测

2. **Pinia状态存储** (`/src/store/modules/theme.ts`)：
   - 全局状态管理
   - 持久化设置
   - 组件间通信

3. **Vue插件** (`/src/core/plugins/theme/index.ts`)：
   - 将主题系统与Vue应用集成
   - 提供全局属性和依赖注入

4. **CSS变量系统** (`/src/shared/themes/tailwind-dark.css`)：
   - 定义主题变量
   - 暗黑模式支持
   - Tailwind集成

## 使用方法

### 基本使用

在Vue组件中访问和控制主题：

```vue
<template>
  <div>
    <h1>当前主题：{{ themeStore.currentTheme }}</h1>
    <button @click="themeStore.toggleDarkMode()">
      切换暗黑模式
    </button>
  </div>
</template>

<script setup>
import { useThemeStore } from '@/store/modules/theme';

const themeStore = useThemeStore();
</script>
```

### 检测暗黑模式

```vue
<template>
  <div :class="{ 'dark-content': themeStore.isDarkMode }">
    适配暗黑模式的内容
  </div>
</template>

<script setup>
import { useThemeStore } from '@/store/modules/theme';

const themeStore = useThemeStore();
</script>
```

### 跟随系统主题

```vue
<template>
  <div>
    <Checkbox v-model="followSystem" binary />
    <label>跟随系统主题</label>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useThemeStore } from '@/store/modules/theme';
import Checkbox from 'primevue/checkbox';

const themeStore = useThemeStore();

const followSystem = computed({
  get: () => themeStore.isFollowingSystem,
  set: (value) => themeStore.toggleFollowSystem(value)
});
</script>
```

### 主题选择器组件

我们提供了预设的主题选择器组件：

```vue
<template>
  <ThemeSelector />
</template>

<script setup>
import ThemeSelector from '@/shared/components/ThemeSelector/index.vue';
</script>
```

## CSS变量使用

### 在组件中使用主题变量

```css
.my-component {
  /* 使用主题颜色 */
  color: var(--text-color);
  background-color: var(--surface-card);
  border-color: var(--surface-border);
  
  /* 主题色变体 */
  --primary-light: var(--primary-100);
  --primary-dark: var(--primary-800);
  
  /* 兼容旧主题系统 */
  color: var(--text-color, var(--color-text, #212121));
  background-color: var(--surface-card, var(--color-surface, #ffffff));
}

/* 深色模式特定样式 */
.dark .my-component {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);
}
```

### 主要CSS变量

以下是主要的主题CSS变量：

```css
:root {
  /* 主色 */
  --primary-color: #1976D2;
  --primary-color-text: #ffffff;
  
  /* 表面 */
  --surface-ground: #f8f9fa;
  --surface-section: #ffffff;
  --surface-card: #ffffff;
  --surface-overlay: #ffffff;
  --surface-border: #dfe7ef;
  --surface-hover: #f6f9fc;
  
  /* 文本 */
  --text-color: #495057;
  --text-color-secondary: #6c757d;
  
  /* 兼容旧变量 */
  --color-primary: #1976D2;
  --color-background: #f8f9fa;
  --color-surface: #ffffff;
  --color-text: #495057;
  --color-border: #dfe7ef;
}

.dark {
  /* 暗色模式变量 */
  --primary-color: #42A5F5;
  --surface-ground: #121212;
  --surface-card: #262626;
  --text-color: #f8f9fa;
  /* ...其他变量... */
}
```

## 添加新主题

要添加新主题，请按以下步骤操作：

1. 在 `frontend/src/shared/themes/theme-manager.ts` 中的 `createDefaultThemeOptions` 函数中添加新主题配置：

```typescript
{
  name: 'purple',
  label: '紫色主题',
  primevueTheme: 'lara-light-purple',
  isDark: false,
  variables: {
    '--primary-color': '#9c27b0',
    '--primary-color-text': '#ffffff',
    // ...其他变量
  }
}
```

2. 如果需要，导入相应的PrimeVue主题CSS：

```typescript
// frontend/src/core/plugins/theme/index.ts
import 'primevue/resources/themes/lara-light-purple/theme.css';
```

3. 更新类型定义：

```typescript
// frontend/src/store/modules/theme.ts
export type ThemeType = 'light' | 'dark' | 'purple' | /* 其他主题 */;
```

## 主题切换时的视觉反馈

为了提供更好的用户体验，可以在主题切换时添加过渡效果：

```css
/* 全局CSS文件中 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
```

## 与国际化系统集成

主题系统与国际化系统无缝集成，可以在i18n文件中定义主题名称：

```json
{
  "theme": {
    "light": "浅色主题",
    "dark": "深色主题",
    "purple": "紫色主题",
    "selectTheme": "选择主题",
    "followSystem": "跟随系统主题"
  }
}
```

然后在组件中使用：

```vue
<template>
  <div>
    <h2>{{ t('theme.selectTheme') }}</h2>
    <div v-for="theme in themes" :key="theme">
      <button @click="setTheme(theme)">
        {{ t(`theme.${theme}`) }}
      </button>
    </div>
  </div>
</template>
```

## 调试和疑难解答

如果遇到主题问题，请检查：

1. CSS变量是否正确应用（使用浏览器开发者工具）
2. 是否存在CSS选择器优先级问题
3. 组件是否正确使用了主题变量
4. 在暗黑模式下是否有特定组件需要额外样式

## 兼容性说明

新主题系统兼容旧系统，但推荐使用新API：

旧API：
```typescript
import { themeManager } from '@/shared/themes';
themeManager.setTheme('blue');
```

新API：
```typescript
import { useThemeStore } from '@/store/modules/theme';
const themeStore = useThemeStore();
themeStore.setTheme('light');
```

## 最佳实践

1. 始终使用CSS变量而非硬编码颜色
2. 为暗黑模式提供专门的样式
3. 测试所有组件在不同主题下的显示效果
4. 在复杂组件中提供暗黑模式特定的样式
5. 避免直接使用Tailwind的颜色类，优先使用主题变量 