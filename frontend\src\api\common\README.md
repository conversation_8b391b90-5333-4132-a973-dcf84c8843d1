# 公共API模块

本目录包含了可在多个组件中复用的公共API接口。

## 文件结构

```
common/
├── userActions.ts        # 用户操作相关API
├── postCommentActions.ts # 帖子评论操作相关API
└── README.md            # 说明文档
```

## userActions.ts - 用户操作API

提供用户关注、取消关注等操作的API接口。

### 接口列表

#### followUser(params)
关注用户
- **参数**: `{ data: { userId: string } }`
- **返回**: Promise
- **用途**: 关注指定用户

#### unfollowUser(params)
取消关注用户
- **参数**: `{ data: { userId: string } }`
- **返回**: Promise
- **用途**: 取消关注指定用户

#### checkFollowStatus(params)
检查关注状态
- **参数**: `{ data: { userId: string } }`
- **返回**: Promise
- **用途**: 检查是否已关注某用户

#### batchCheckFollowStatus(params)
批量检查关注状态
- **参数**: `{ data: { userIds: string[] } }`
- **返回**: Promise
- **用途**: 批量检查多个用户的关注状态

### 使用示例

```typescript
import { followUser, unfollowUser } from '@/api/common/userActions'

// 关注用户
const handleFollow = async (userId: string) => {
  try {
    await followUser({ data: { userId } })
    console.log('关注成功')
  } catch (error) {
    console.error('关注失败:', error)
  }
}

// 取消关注用户
const handleUnfollow = async (userId: string) => {
  try {
    await unfollowUser({ data: { userId } })
    console.log('取消关注成功')
  } catch (error) {
    console.error('取消关注失败:', error)
  }
}
```

## postCommentActions.ts - 帖子评论操作API

提供帖子评论相关的API接口。

> **注意**: 此API专用于帖子评论，视频评论和短视频评论请使用对应的专用API。

### 接口列表

#### getComments(params)
获取评论列表
- **参数**: `{ data: { postId: string }, page: { pageNo: number, pageSize: number } }`
- **返回**: Promise
- **用途**: 获取帖子的评论列表

#### createComment(params)
创建评论
- **参数**: `{ data: { postId: string, content: string, parentId?: string } }`
- **返回**: Promise
- **用途**: 创建新评论或回复

#### deleteComment(params)
删除评论
- **参数**: `{ data: { commentId: string } }`
- **返回**: Promise
- **用途**: 删除指定评论

#### toggleCommentLike(params)
点赞/取消点赞评论
- **参数**: `{ data: { commentId: string } }`
- **返回**: Promise
- **用途**: 切换评论的点赞状态

#### replyComment(params)
回复评论
- **参数**: `{ data: { parentId: string, content: string, postId: string } }`
- **返回**: Promise
- **用途**: 回复指定评论

### 使用示例

```typescript
import { getComments, createComment, toggleCommentLike } from '@/api/common/postCommentActions'

// 获取评论
const loadComments = async (postId: string) => {
  try {
    const response = await getComments({
      data: { postId },
      page: { pageNo: 1, pageSize: 20 }
    })
    console.log('评论列表:', response.list)
  } catch (error) {
    console.error('获取评论失败:', error)
  }
}

// 创建评论
const addComment = async (postId: string, content: string) => {
  try {
    await createComment({
      data: { postId, content }
    })
    console.log('评论成功')
  } catch (error) {
    console.error('评论失败:', error)
  }
}

// 点赞评论
const likeComment = async (commentId: string) => {
  try {
    await toggleCommentLike({ data: { commentId } })
    console.log('点赞成功')
  } catch (error) {
    console.error('点赞失败:', error)
  }
}
```

## 注意事项

1. **错误处理**: 所有API调用都应该包含适当的错误处理
2. **参数验证**: 确保传递正确的参数格式
3. **状态更新**: API调用成功后，记得更新本地状态
4. **用户反馈**: 提供适当的用户反馈（成功/失败消息）

## 相关组合函数

推荐使用对应的组合函数来简化API调用：
- `useUserActions` - 用户操作组合函数
- `usePostCommentActions` - 帖子评论操作组合函数

详见 `@/composables/` 目录下的相关文件。