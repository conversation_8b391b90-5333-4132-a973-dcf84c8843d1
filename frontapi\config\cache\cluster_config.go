package cache

import "time"

// ClusterNode 集群节点配置
type ClusterNode struct {
	// 节点地址
	Host string `mapstructure:"host"`
	// 节点端口
	Port int `mapstructure:"port"`
	// 节点权重（用于一致性哈希等）
	Weight int `mapstructure:"weight"`
	// 节点ID
	ID string `mapstructure:"id"`
	// 是否是主节点
	IsMaster bool `mapstructure:"is_master"`
}

// ClusterConfig 集群配置
type ClusterConfig struct {
	// 节点列表
	Nodes []ClusterNode `mapstructure:"nodes"`
	// 集群名称
	Name string `mapstructure:"name"`
	// 故障转移策略
	FailoverStrategy string `mapstructure:"failover_strategy"`
	// 连接超时
	ConnectTimeout time.Duration `mapstructure:"connect_timeout"`
	// 一致性哈希副本数
	ConsistentHashReplicas int `mapstructure:"consistent_hash_replicas"`
	// 写一致性
	WriteConsistency string `mapstructure:"write_consistency"`
	// 读一致性
	ReadConsistency string `mapstructure:"read_consistency"`
	// 健康检查
	HealthCheck bool `mapstructure:"health_check"`
	// 健康检查间隔
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval"`
	// 健康检查超时
	HealthCheckTimeout time.Duration `mapstructure:"health_check_timeout"`
	// 健康检查重试次数
	HealthCheckRetries int `mapstructure:"health_check_retries"`
	// 健康检查重试延迟
	HealthCheckRetryDelay time.Duration `mapstructure:"health_check_retry_delay"`
	// 健康检查重试最大延迟
	HealthCheckRetryMaxDelay time.Duration `mapstructure:"health_check_retry_max_delay"`
	// 健康检查重试最大次数
	HealthCheckRetryMax int `mapstructure:"health_check_retry_max"`
	// 健康检查重试退避
	HealthCheckRetryBackoff time.Duration `mapstructure:"health_check_retry_backoff"`
	//重新平衡分区
	PartitionCount int `mapstructure:"partition_count"`
}
