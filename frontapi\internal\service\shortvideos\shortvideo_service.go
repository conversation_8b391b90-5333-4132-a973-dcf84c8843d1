package shortvideos

import (
	"context"
	"fmt"

	"frontapi/internal/models/shortvideos"
	repo "frontapi/internal/repository/shortvideos"
	userRepo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
	"frontapi/internal/service/base/extcollect"
	"frontapi/internal/service/base/extlike"
)

const (
	// ItemType 短视频项目类型
	ItemTypeShortVideo = "shortvideo"
)

// ShortVideoService 短视频服务接口
type ShortVideoService interface {
	base.IExtendedService[shortvideos.ShortVideo]

	// 点赞相关
	CheckUserLiked(ctx context.Context, userID string, shortVideoID string) (bool, error)
	LikeShortVideo(ctx context.Context, userID string, shortVideoID string) error
	UnlikeShortVideo(ctx context.Context, userID string, shortVideoID string) error
	GetShortVideoLikeCount(ctx context.Context, shortVideoID string) (int64, error)

	// 收藏相关
	CheckUserCollected(ctx context.Context, userID string, shortVideoID string) (bool, error)
	CollectShortVideo(ctx context.Context, userID string, shortVideoID string) error
	UncollectShortVideo(ctx context.Context, userID string, shortVideoID string) error
	GetShortVideoCollectCount(ctx context.Context, shortVideoID string) (int64, error)

	// 批量操作
	BatchCheckUserLiked(ctx context.Context, userID string, shortVideoIDs []string) (map[string]bool, error)
	BatchCheckUserCollected(ctx context.Context, userID string, shortVideoIDs []string) (map[string]bool, error)
	BatchGetLikeCounts(ctx context.Context, shortVideoIDs []string) (map[string]int64, error)
	BatchGetCollectCounts(ctx context.Context, shortVideoIDs []string) (map[string]int64, error)

	// 审核视频
	ReviewShorts(ctx context.Context, id string, status int, reason string) error
}

// shortVideoService 短视频服务实现
type shortVideoService struct {
	*base.ExtendedService[shortvideos.ShortVideo]
	shortVideoRepo          repo.ShortVideoRepository
	likeRepo                repo.ShortVideoLikeRepository
	userShortCollectionRepo userRepo.UserShortsCollectionRepository

	// 新增的扩展服务
	likeService    extlike.LikeService
	collectService extcollect.CollectService
}

// NewShortVideoService 创建短视频服务实例
func NewShortVideoService(
	shortVideoRepo repo.ShortVideoRepository,
	likeRepo repo.ShortVideoLikeRepository,
	userShortCollectRepo userRepo.UserShortsCollectionRepository,
	likeService extlike.LikeService,
	collectService extcollect.CollectService,
) ShortVideoService {
	return &shortVideoService{
		ExtendedService:         base.NewExtendedService[shortvideos.ShortVideo](shortVideoRepo, "short_video"),
		shortVideoRepo:          shortVideoRepo,
		likeRepo:                likeRepo,
		userShortCollectionRepo: userShortCollectRepo,
		likeService:             likeService,
		collectService:          collectService,
	}
}

// ReviewShorts 审核短视频
func (s *shortVideoService) ReviewShorts(ctx context.Context, id string, status int, reason string) error {
	return s.shortVideoRepo.ReviewShorts(ctx, id, status, reason)
}

// ============ 点赞相关方法 ============

// CheckUserLiked 检查用户是否点赞了短视频
func (s *shortVideoService) CheckUserLiked(ctx context.Context, userID string, shortVideoID string) (bool, error) {
	if userID == "" || shortVideoID == "" {
		return false, fmt.Errorf("用户ID和短视频ID不能为空")
	}

	// 优先使用扩展点赞服务
	if s.likeService != nil {
		return s.likeService.IsLiked(ctx, userID, shortVideoID, ItemTypeShortVideo)
	}

	// 回退到原始仓库方法
	return s.likeRepo.CheckUserLiked(ctx, userID, shortVideoID)
}

// LikeShortVideo 点赞短视频
func (s *shortVideoService) LikeShortVideo(ctx context.Context, userID string, shortVideoID string) error {
	if userID == "" || shortVideoID == "" {
		return fmt.Errorf("用户ID和短视频ID不能为空")
	}

	// 使用扩展点赞服务
	if s.likeService != nil {
		return s.likeService.Like(ctx, userID, shortVideoID, ItemTypeShortVideo)
	}

	// 如果没有扩展服务，返回错误
	return fmt.Errorf("点赞服务不可用")
}

// UnlikeShortVideo 取消点赞短视频
func (s *shortVideoService) UnlikeShortVideo(ctx context.Context, userID string, shortVideoID string) error {
	if userID == "" || shortVideoID == "" {
		return fmt.Errorf("用户ID和短视频ID不能为空")
	}

	// 使用扩展点赞服务
	if s.likeService != nil {
		return s.likeService.Unlike(ctx, userID, shortVideoID, ItemTypeShortVideo)
	}

	// 如果没有扩展服务，返回错误
	return fmt.Errorf("点赞服务不可用")
}

// GetShortVideoLikeCount 获取短视频点赞数
func (s *shortVideoService) GetShortVideoLikeCount(ctx context.Context, shortVideoID string) (int64, error) {
	if shortVideoID == "" {
		return 0, fmt.Errorf("短视频ID不能为空")
	}

	// 使用扩展点赞服务
	if s.likeService != nil {
		return s.likeService.GetLikeCount(ctx, shortVideoID, ItemTypeShortVideo)
	}

	// 如果没有扩展服务，返回0
	return 0, fmt.Errorf("点赞服务不可用")
}

// ============ 收藏相关方法 ============

// CheckUserCollected 检查用户是否收藏了短视频
func (s *shortVideoService) CheckUserCollected(ctx context.Context, userID string, shortVideoID string) (bool, error) {
	if userID == "" || shortVideoID == "" {
		return false, fmt.Errorf("用户ID和短视频ID不能为空")
	}

	// 优先使用扩展收藏服务
	if s.collectService != nil {
		return s.collectService.IsCollected(ctx, userID, shortVideoID, ItemTypeShortVideo)
	}

	// 回退到原始仓库方法
	return s.userShortCollectionRepo.Exists(ctx, map[string]interface{}{
		"user_id":        userID,
		"short_video_id": shortVideoID,
	})
}

// CollectShortVideo 收藏短视频
func (s *shortVideoService) CollectShortVideo(ctx context.Context, userID string, shortVideoID string) error {
	if userID == "" || shortVideoID == "" {
		return fmt.Errorf("用户ID和短视频ID不能为空")
	}

	// 使用扩展收藏服务
	if s.collectService != nil {
		return s.collectService.Collect(ctx, userID, shortVideoID, ItemTypeShortVideo)
	}

	// 如果没有扩展服务，返回错误
	return fmt.Errorf("收藏服务不可用")
}

// UncollectShortVideo 取消收藏短视频
func (s *shortVideoService) UncollectShortVideo(ctx context.Context, userID string, shortVideoID string) error {
	if userID == "" || shortVideoID == "" {
		return fmt.Errorf("用户ID和短视频ID不能为空")
	}

	// 使用扩展收藏服务
	if s.collectService != nil {
		return s.collectService.Uncollect(ctx, userID, shortVideoID, ItemTypeShortVideo)
	}

	// 如果没有扩展服务，返回错误
	return fmt.Errorf("收藏服务不可用")
}

// GetShortVideoCollectCount 获取短视频收藏数
func (s *shortVideoService) GetShortVideoCollectCount(ctx context.Context, shortVideoID string) (int64, error) {
	if shortVideoID == "" {
		return 0, fmt.Errorf("短视频ID不能为空")
	}

	// 使用扩展收藏服务
	if s.collectService != nil {
		return s.collectService.GetCollectCount(ctx, shortVideoID, ItemTypeShortVideo)
	}

	// 如果没有扩展服务，返回0
	return 0, fmt.Errorf("收藏服务不可用")
}

// ============ 批量操作方法 ============

// BatchCheckUserLiked 批量检查用户点赞状态
func (s *shortVideoService) BatchCheckUserLiked(ctx context.Context, userID string, shortVideoIDs []string) (map[string]bool, error) {
	if userID == "" || len(shortVideoIDs) == 0 {
		return map[string]bool{}, fmt.Errorf("用户ID和短视频ID列表不能为空")
	}

	// 使用扩展点赞服务
	if s.likeService != nil {
		// 构建items map
		items := make(map[string]string)
		for _, id := range shortVideoIDs {
			items[id] = ItemTypeShortVideo
		}
		return s.likeService.BatchGetLikeStatus(ctx, userID, items)
	}

	// 回退到逐个检查
	result := make(map[string]bool)
	for _, id := range shortVideoIDs {
		liked, err := s.likeRepo.CheckUserLiked(ctx, userID, id)
		if err != nil {
			// 出错时设为false，继续处理其他
			result[id] = false
		} else {
			result[id] = liked
		}
	}
	return result, nil
}

// BatchCheckUserCollected 批量检查用户收藏状态
func (s *shortVideoService) BatchCheckUserCollected(ctx context.Context, userID string, shortVideoIDs []string) (map[string]bool, error) {
	if userID == "" || len(shortVideoIDs) == 0 {
		return map[string]bool{}, fmt.Errorf("用户ID和短视频ID列表不能为空")
	}

	// 使用扩展收藏服务
	if s.collectService != nil {
		// 构建items map
		items := make(map[string]string)
		for _, id := range shortVideoIDs {
			items[id] = ItemTypeShortVideo
		}
		return s.collectService.BatchGetCollectStatus(ctx, userID, items)
	}

	// 回退到逐个检查
	result := make(map[string]bool)
	for _, id := range shortVideoIDs {
		collected, err := s.userShortCollectionRepo.Exists(ctx, map[string]interface{}{
			"user_id":        userID,
			"short_video_id": id,
		})
		if err != nil {
			// 出错时设为false，继续处理其他
			result[id] = false
		} else {
			result[id] = collected
		}
	}
	return result, nil
}

// BatchGetLikeCounts 批量获取点赞数
func (s *shortVideoService) BatchGetLikeCounts(ctx context.Context, shortVideoIDs []string) (map[string]int64, error) {
	if len(shortVideoIDs) == 0 {
		return map[string]int64{}, nil
	}

	// 使用扩展点赞服务
	if s.likeService != nil {
		// 构建items map
		items := make(map[string]string)
		for _, id := range shortVideoIDs {
			items[id] = ItemTypeShortVideo
		}
		return s.likeService.BatchGetLikeCounts(ctx, items)
	}

	// 如果没有扩展服务，返回空结果
	result := make(map[string]int64)
	for _, id := range shortVideoIDs {
		result[id] = 0
	}
	return result, nil
}

// BatchGetCollectCounts 批量获取收藏数
func (s *shortVideoService) BatchGetCollectCounts(ctx context.Context, shortVideoIDs []string) (map[string]int64, error) {
	if len(shortVideoIDs) == 0 {
		return map[string]int64{}, nil
	}

	// 使用扩展收藏服务
	if s.collectService != nil {
		// 构建items map
		items := make(map[string]string)
		for _, id := range shortVideoIDs {
			items[id] = ItemTypeShortVideo
		}
		return s.collectService.BatchGetCollectCounts(ctx, items)
	}

	// 如果没有扩展服务，返回空结果
	result := make(map[string]int64)
	for _, id := range shortVideoIDs {
		result[id] = 0
	}
	return result, nil
}
