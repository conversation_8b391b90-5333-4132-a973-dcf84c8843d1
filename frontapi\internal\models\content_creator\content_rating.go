package content_creator

import (
	"frontapi/internal/models"
)

type ContentRating struct {
	models.BaseModel
	UserID      string  `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                         // 用户ID
	ContentType string  `gorm:"column:content_type;type:string;not null;comment:内容类型" json:"content_type"`               // 内容类型
	ContentID   string  `gorm:"column:content_id;type:string;not null;comment:内容ID" json:"content_id"`                   // 内容ID
	Rating      float64 `gorm:"column:rating;type:decimal(3,1);not null;comment:评分(1-5分)" json:"rating"`                 // 评分(1-5分)
	Comment     string  `gorm:"column:comment;type:text;comment:评价内容" json:"comment"`                                    // 评价内容
	IsAnonymous bool    `gorm:"column:is_anonymous;type:tinyint(1);not null;default:0;comment:是否匿名" json:"is_anonymous"` // 是否匿名
	IsHidden    bool    `gorm:"column:is_hidden;type:tinyint(1);not null;default:0;comment:是否隐藏" json:"is_hidden"`       // 是否隐藏
}

func (ContentRating) TableName() string {
	return "ly_content_ratings"
}
