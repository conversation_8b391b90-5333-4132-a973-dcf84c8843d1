/**
 * orange 主题配置
 * 基于温暖色调的主题
 */
import { ThemeConfig } from '../theme-manager';
import orangeVariables from './variables';
import orangeDarkVariables from './variables-dark';

// orange 亮色主题
export const orangeLightTheme: ThemeConfig = {
    name: 'orangeLight',
    displayName: 'Orange Light',
    shortName: 'orange',
    extendName: 'aura-light-orange',
    extendThemeStyle: 'primevue/resources/themes/aura-light-orange/theme.css',
    code: 'orangelight',
    primary: '#FF7043',
    isDark: false,
    variables: orangeVariables
};

// orange 暗色主题
export const orangeDarkTheme: ThemeConfig = {
    name: 'orangeDark',
    displayName: 'Orange Dark',
    shortName: 'orange dark',
    extendName: 'aura-dark-orange',
    extendThemeStyle: 'primevue/resources/themes/aura-dark-orange/theme.css',
    code: 'orangedark',
    primary: '#FF8A65',
    isDark: true,
    variables: orangeDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default orangeLightTheme;