<template>
  <el-dialog 
    :model-value="visible" 
    :title="title || '内容审核'" 
    width="400px" 
    @close="handleClose"
    destroy-on-close
  >
    <div class="approval-form">
      <el-radio-group v-model="auditStatus">
        <el-radio :value="2">通过</el-radio>
        <el-radio :value="-2">不通过</el-radio>
      </el-radio-group>
    </div>
    <div v-if="auditStatus === -2" class="mt-4">
      <el-form :model="form">
        <el-form-item 
          label="原因" 
          :rules="[{ required: true, message: '请填写不通过原因', trigger: 'blur' }]"
        >
          <el-input 
            v-model="form.reason" 
            type="textarea" 
            placeholder="请输入不通过原因" 
            :rows="3"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { ref, watch } from 'vue';

interface Props {
  visible: boolean;
  title?: string;
  itemId?: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '内容审核',
  itemId: '',
  loading: false
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [data: { status: number; reason: string; itemId?: string }];
}>();

const auditStatus = ref(2); // 2: 通过, -2: 不通过
const form = ref({ reason: '' });

watch(() => props.visible, (val) => {
  if (val) {
    auditStatus.value = 2;
    form.value.reason = '';
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = () => {
  if (auditStatus.value === -2 && !form.value.reason) {
    ElMessage.error('请填写不通过原因');
    return;
  }
  
  emit('confirm', { 
    status: auditStatus.value, 
    reason: form.value.reason,
    itemId: props.itemId
  });
};
</script>

<style scoped lang="scss">
.approval-form {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}
</style> 