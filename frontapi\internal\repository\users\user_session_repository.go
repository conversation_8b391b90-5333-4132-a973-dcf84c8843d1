package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserSessionRepository 用户会话数据访问接口
type UserSessionRepository interface {
	base.ExtendedRepository[users.UserSession]
}

// userSessionRepository 用户会话数据访问实现
type userSessionRepository struct {
	base.ExtendedRepository[users.UserSession]
}

// NewUserSessionRepository 创建用户会话仓库实例
func NewUserSessionRepository(db *gorm.DB) UserSessionRepository {
	return &userSessionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserSession](db),
	}
}
