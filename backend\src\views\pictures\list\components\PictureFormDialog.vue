<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="650px" :close-on-click-modal="false"
        :close-on-press-escape="true" destroy-on-close>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right" status-icon>
            <el-form-item label="图片标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入图片标题" />
            </el-form-item>

            <el-form-item label="图片URL" prop="url">
                <url-or-file-input v-model="form.url" file-type="pictures" sub-dir="images" placeholder="请上传或选择图片"
                    @change="handleImageChange" />
            </el-form-item>

            <el-form-item label="图片描述" prop="description">
                <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入图片描述" />
            </el-form-item>

            <el-form-item label="所属专辑" prop="album_id">
                <el-select v-model="form.album_id" placeholder="请选择专辑" filterable clearable style="width: 100%">
                    <el-option v-for="item in filteredAlbumOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="图片状态" prop="status">
                <el-radio-group v-model="form.status">
                    <el-radio :label="1">正常</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="loading">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import { createPicture, updatePicture } from '@/service/api/pictures/pictures';
import type { CreatePictureRequest, Picture, UpdatePictureRequest } from '@/types/pictures';
import { ElMessage } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';

const props = defineProps<{
    visible: boolean;
    picture: Picture | null;
    albumOptions: Array<{ value: string, label: string }>;
}>();

const emit = defineEmits<{
    'update:visible': [value: boolean];
    'success': [];
}>();

// 对话框可见性
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 对话框标题
const dialogTitle = computed(() => props.picture ? '编辑图片' : '添加图片');

// 表单引用
const formRef = ref();

// 加载状态
const loading = ref(false);

// 专辑过滤
const albumFilterKeyword = ref('');
const filteredAlbumOptions = computed(() => {
    if (!albumFilterKeyword.value) {
        return props.albumOptions;
    }
    const keyword = albumFilterKeyword.value.toLowerCase();
    return props.albumOptions.filter(album =>
        album.label.toLowerCase().includes(keyword)
    );
});

// 监听专辑选择器的搜索输入
watch(() => dialogVisible.value, (newVal) => {
    if (newVal) {
        // 对话框打开时重置过滤关键词
        albumFilterKeyword.value = '';
    }
});

// 表单数据
const form = reactive<{
    title: string;
    url: string;
    description: string;
    album_id: string;
    width: number;
    height: number;
    size: number;
    status: number;
}>({
    title: '',
    url: '',
    description: '',
    album_id: '',
    width: 0,
    height: 0,
    size: 0,
    status: 1
});

// 表单校验规则
const rules = {
    title: [
        { required: true, message: '请输入图片标题', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
    ],
    url: [
        { required: true, message: '请上传或选择图片', trigger: 'change' }
    ]
};

// 重置表单
const resetForm = () => {
    Object.assign(form, {
        title: '',
        url: '',
        description: '',
        album_id: '',
        width: 0,
        height: 0,
        size: 0,
        status: 1
    });
    if (formRef.value) {
        formRef.value.resetFields();
    }
};

// 监听图片数据变化
watch(() => props.picture, (newVal) => {
    if (newVal) {
        // 编辑模式，填充表单
        Object.keys(form).forEach(key => {
            if (key in newVal) {
                const value = newVal[key as keyof Picture];
                if (value !== undefined) {
                    (form as any)[key] = value;
                }
            }
        });
    } else {
        // 新增模式，重置表单
        resetForm();
    }
}, { immediate: true });

// 处理图片变更
const handleImageChange = (url: string, fileInfo?: any) => {
    form.url = url;
    if (fileInfo) {
        form.width = fileInfo.width || 0;
        form.height = fileInfo.height || 0;
        form.size = fileInfo.size || 0;
    }
};

// 处理表单提交
const handleSubmit = async () => {
    if (!formRef.value) return;

    try {
        await formRef.value.validate();

        loading.value = true;

        if (props.picture) {
            // 编辑模式
            const params = {
                data: {
                    id: props.picture.id,
                    title: form.title,
                    description: form.description,
                    album_id: form.album_id,
                    status: form.status
                } as UpdatePictureRequest
            };

            await updatePicture(props.picture.id, params);
            ElMessage.success('更新图片成功');
        } else {
            // 新增模式
            const params = {
                data: {
                    url: form.url,
                    title: form.title,
                    description: form.description,
                    album_id: form.album_id,
                    width: form.width,
                    height: form.height,
                    size: form.size,
                    status: form.status
                } as CreatePictureRequest
            };

            await createPicture(params);
            ElMessage.success('添加图片成功');
        }

        dialogVisible.value = false;
        emit('success');
    } catch (error) {
        console.error('提交表单失败:', error);
        ElMessage.error('提交失败，请检查表单');
    } finally {
        loading.value = false;
    }
};
</script>

<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
}
</style>