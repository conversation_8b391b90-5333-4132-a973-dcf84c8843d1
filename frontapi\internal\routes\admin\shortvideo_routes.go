package admin

import (
	apiShortVideos "frontapi/internal/admin/shortvideos"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterShortVideoRoutes 注册短视频相关路由
func RegisterShortVideoRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 创建短视频API控制器
	controller := apiShortVideos.NewShortVideoController(
		services.ShortVideoService,
		services.ShortVideoCategoryService,
		services.ShortVideoCommentService,
		services.UserService,
	)
	// 短视频管理路由组
	shortVideoGroup := apiGroup.Group("/shortvideos", middleware.AuthRequired())
	{
		// 短视频管理
		shortVideoGroup.Post("/list", controller.ListShortVideos)
		shortVideoGroup.Post("/detail/:id?", controller.GetShortVideo)
		shortVideoGroup.Post("/add", controller.CreateShortVideo)
		shortVideoGroup.Post("/update", controller.UpdateShortVideo)
		shortVideoGroup.Post("/change-status", controller.UpdateShortVideoStatus)
		shortVideoGroup.Post("/delete/:id?", controller.DeleteShortVideo)
		// 添加批量操作路由
		shortVideoGroup.Post("/batch-update-status", controller.BatchUpdateShortVideoStatus)
		shortVideoGroup.Post("/batch-delete", controller.BatchDeleteShortVideo)
	}

	// 短视频分类管理路由组
	categoryGroup := apiGroup.Group("/shortvideos/categories", middleware.AuthRequired())
	{
		categoryController := apiShortVideos.NewShortVideoCategoryController(services.ShortVideoCategoryService, services.ShortVideoService)
		// 短视频分类管理
		categoryGroup.Post("/list", categoryController.ListShortVideoCategories)
		categoryGroup.Post("/detail/:id?", categoryController.GetShortVideoCategory)
		categoryGroup.Post("/add", categoryController.CreateShortVideoCategory)
		categoryGroup.Post("/update", categoryController.UpdateShortVideoCategory)
		categoryGroup.Post("/update-status", categoryController.UpdateShortVideoCategoryStatus)
		categoryGroup.Post("/delete/:id?", categoryController.DeleteShortVideoCategory)
		// 添加批量操作路由
		categoryGroup.Post("/batch-update-status", categoryController.BatchUpdateShortVideoCategoryStatus)
		categoryGroup.Post("/batch-delete", categoryController.BatchDeleteShortVideoCategory)
	}

	// 短视频评论管理路由组
	commentGroup := apiGroup.Group("/shortvideos/comments", middleware.AuthRequired())
	{
		commentController := apiShortVideos.NewShortVideoCommentController(services.ShortVideoCommentService, services.ShortVideoService)
		// 短视频评论管理
		commentGroup.Post("/list", commentController.ListShortVideoComments)
		commentGroup.Post("/detail/:id?", commentController.GetShortVideoComment)
		commentGroup.Post("/update-status", commentController.UpdateShortVideoCommentStatus)
		commentGroup.Post("/delete/:id?", commentController.DeleteShortVideoComment)
		// 添加批量操作路由
		commentGroup.Post("/batch-update-status", commentController.BatchUpdateShortVideoCommentStatus)
		commentGroup.Post("/batch-delete", commentController.BatchDeleteShortVideoComment)
		// 添加获取评论回复的路由
		commentGroup.Post("/replies/:id?", controller.GetCommentReplies)
	}

}
