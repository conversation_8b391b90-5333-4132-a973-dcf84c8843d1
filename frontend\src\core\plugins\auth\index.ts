/**
 * 认证插件 - 提供用户认证和授权功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'
import { storage } from '../../utils/storage'

/**
 * 用户信息接口
 */
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  roles: string[]
  permissions: string[]
  profile?: Record<string, any>
}

/**
 * 认证状态
 */
export interface AuthState {
  isAuthenticated: boolean
  user: User | null
  token: string | null
  refreshToken: string | null
  expiresAt: number | null
}

/**
 * 登录凭据
 */
export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
}

/**
 * 认证配置
 */
export interface AuthConfig {
  /** API基础URL */
  apiBaseUrl: string
  /** 登录端点 */
  loginEndpoint: string
  /** 刷新令牌端点 */
  refreshEndpoint: string
  /** 用户信息端点 */
  userInfoEndpoint: string
  /** 登出端点 */
  logoutEndpoint: string
  /** 令牌存储键 */
  tokenStorageKey: string
  /** 刷新令牌存储键 */
  refreshTokenStorageKey: string
  /** 用户信息存储键 */
  userStorageKey: string
  /** 自动刷新令牌 */
  autoRefreshToken: boolean
  /** 令牌刷新阈值（分钟） */
  refreshThreshold: number
  /** 是否启用记住我功能 */
  enableRememberMe: boolean
  /** 记住我过期时间（天） */
  rememberMeDays: number
}

/**
 * 认证管理器类
 */
export class AuthManager {
  private config: AuthConfig
  private state: AuthState
  private refreshTimer?: number
  private listeners: Array<(state: AuthState) => void> = []

  constructor(config: AuthConfig) {
    this.config = config
    this.state = {
      isAuthenticated: false,
      user: null,
      token: null,
      refreshToken: null,
      expiresAt: null
    }
    this.init()
  }

  /**
   * 初始化认证管理器
   */
  private init() {
    this.loadFromStorage()
    
    if (this.state.token && this.config.autoRefreshToken) {
      this.scheduleTokenRefresh()
    }
  }

  /**
   * 从存储中加载认证信息
   */
  private loadFromStorage() {
    try {
      const token = storage.get(this.config.tokenStorageKey)
      const refreshToken = storage.get(this.config.refreshTokenStorageKey)
      const userStr = storage.get(this.config.userStorageKey)
      
      if (token && userStr) {
        const user = JSON.parse(userStr)
        const expiresAt = this.parseTokenExpiration(token)
        
        this.state = {
          isAuthenticated: true,
          user,
          token,
          refreshToken,
          expiresAt
        }
        
        // 检查令牌是否过期
        if (expiresAt && Date.now() > expiresAt) {
          this.logout()
        }
      }
    } catch (error) {
      console.error('Failed to load auth state from storage:', error)
      this.clearStorage()
    }
  }

  /**
   * 保存到存储
   */
  private saveToStorage() {
    try {
      if (this.state.token) {
        storage.set(this.config.tokenStorageKey, this.state.token)
      }
      
      if (this.state.refreshToken) {
        storage.set(this.config.refreshTokenStorageKey, this.state.refreshToken)
      }
      
      if (this.state.user) {
        storage.set(this.config.userStorageKey, JSON.stringify(this.state.user))
      }
    } catch (error) {
      console.error('Failed to save auth state to storage:', error)
    }
  }

  /**
   * 清除存储
   */
  private clearStorage() {
    storage.remove(this.config.tokenStorageKey)
    storage.remove(this.config.refreshTokenStorageKey)
    storage.remove(this.config.userStorageKey)
  }

  /**
   * 登录
   */
  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const response = await fetch(`${this.config.apiBaseUrl}${this.config.loginEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      })

      if (!response.ok) {
        throw new Error('Login failed')
      }

      const data = await response.json()
      const { token, refreshToken, user } = data
      
      const expiresAt = this.parseTokenExpiration(token)
      
      this.state = {
        isAuthenticated: true,
        user,
        token,
        refreshToken,
        expiresAt
      }
      
      this.saveToStorage()
      this.notifyListeners()
      
      if (this.config.autoRefreshToken) {
        this.scheduleTokenRefresh()
      }
      
      return user
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    try {
      if (this.state.token) {
        await fetch(`${this.config.apiBaseUrl}${this.config.logoutEndpoint}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.state.token}`
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.state = {
        isAuthenticated: false,
        user: null,
        token: null,
        refreshToken: null,
        expiresAt: null
      }
      
      this.clearStorage()
      this.clearRefreshTimer()
      this.notifyListeners()
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<string> {
    if (!this.state.refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await fetch(`${this.config.apiBaseUrl}${this.config.refreshEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refreshToken: this.state.refreshToken
        })
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const data = await response.json()
      const { token, refreshToken } = data
      
      const expiresAt = this.parseTokenExpiration(token)
      
      this.state.token = token
      this.state.refreshToken = refreshToken || this.state.refreshToken
      this.state.expiresAt = expiresAt
      
      this.saveToStorage()
      this.notifyListeners()
      
      if (this.config.autoRefreshToken) {
        this.scheduleTokenRefresh()
      }
      
      return token
    } catch (error) {
      console.error('Token refresh error:', error)
      await this.logout()
      throw error
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(): Promise<User> {
    if (!this.state.token) {
      throw new Error('No token available')
    }

    try {
      const response = await fetch(`${this.config.apiBaseUrl}${this.config.userInfoEndpoint}`, {
        headers: {
          'Authorization': `Bearer ${this.state.token}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to get user info')
      }

      const user = await response.json()
      
      this.state.user = user
      this.saveToStorage()
      this.notifyListeners()
      
      return user
    } catch (error) {
      console.error('Get user info error:', error)
      throw error
    }
  }

  /**
   * 检查权限
   */
  hasPermission(permission: string): boolean {
    return this.state.user?.permissions.includes(permission) || false
  }

  /**
   * 检查角色
   */
  hasRole(role: string): boolean {
    return this.state.user?.roles.includes(role) || false
  }

  /**
   * 检查多个权限（AND逻辑）
   */
  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(permission))
  }

  /**
   * 检查多个权限（OR逻辑）
   */
  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission))
  }

  /**
   * 解析令牌过期时间
   */
  private parseTokenExpiration(token: string): number | null {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return payload.exp ? payload.exp * 1000 : null
    } catch (error) {
      console.error('Failed to parse token expiration:', error)
      return null
    }
  }

  /**
   * 安排令牌刷新
   */
  private scheduleTokenRefresh() {
    this.clearRefreshTimer()
    
    if (!this.state.expiresAt) {
      return
    }
    
    const now = Date.now()
    const expiresAt = this.state.expiresAt
    const refreshTime = expiresAt - (this.config.refreshThreshold * 60 * 1000)
    
    if (refreshTime > now) {
      this.refreshTimer = window.setTimeout(() => {
        this.refreshToken().catch(error => {
          console.error('Scheduled token refresh failed:', error)
        })
      }, refreshTime - now)
    }
  }

  /**
   * 清除刷新定时器
   */
  private clearRefreshTimer() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = undefined
    }
  }

  /**
   * 添加状态监听器
   */
  addListener(listener: (state: AuthState) => void) {
    this.listeners.push(listener)
  }

  /**
   * 移除状态监听器
   */
  removeListener(listener: (state: AuthState) => void) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.state)
      } catch (error) {
        console.error('Auth listener error:', error)
      }
    })
  }

  /**
   * 获取当前状态
   */
  getState(): AuthState {
    return { ...this.state }
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): User | null {
    return this.state.user
  }

  /**
   * 获取当前令牌
   */
  getToken(): string | null {
    return this.state.token
  }

  /**
   * 是否已认证
   */
  isAuthenticated(): boolean {
    return this.state.isAuthenticated
  }

  /**
   * 销毁认证管理器
   */
  destroy() {
    this.clearRefreshTimer()
    this.listeners = []
  }
}

// 默认配置
const defaultConfig: AuthConfig = {
  apiBaseUrl: '/api',
  loginEndpoint: '/auth/login',
  refreshEndpoint: '/auth/refresh',
  userInfoEndpoint: '/auth/user',
  logoutEndpoint: '/auth/logout',
  tokenStorageKey: 'auth_token',
  refreshTokenStorageKey: 'auth_refresh_token',
  userStorageKey: 'auth_user',
  autoRefreshToken: true,
  refreshThreshold: 5,
  enableRememberMe: true,
  rememberMeDays: 30
}

// 全局认证管理器实例
export const authManager = new AuthManager(defaultConfig)

/**
 * 认证插件
 */
export const authPlugin: Plugin = createPlugin({
  meta: {
    name: 'auth',
    version: '1.0.0',
    description: '用户认证和授权插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 1
  },
  install(context: PluginContext) {
    // 将认证管理器添加到全局属性
    context.app.config.globalProperties.$auth = authManager
    
    // 提供认证管理器
    context.app.provide('auth', authManager)
    
    console.log('Auth plugin installed')
  },
  uninstall() {
    authManager.destroy()
    console.log('Auth plugin uninstalled')
  }
})

/**
 * 使用认证的组合式函数
 */
export function useAuth() {
  return {
    auth: authManager,
    login: authManager.login.bind(authManager),
    logout: authManager.logout.bind(authManager),
    refreshToken: authManager.refreshToken.bind(authManager),
    getUserInfo: authManager.getUserInfo.bind(authManager),
    hasPermission: authManager.hasPermission.bind(authManager),
    hasRole: authManager.hasRole.bind(authManager),
    hasAllPermissions: authManager.hasAllPermissions.bind(authManager),
    hasAnyPermission: authManager.hasAnyPermission.bind(authManager),
    getState: authManager.getState.bind(authManager),
    getCurrentUser: authManager.getCurrentUser.bind(authManager),
    getToken: authManager.getToken.bind(authManager),
    isAuthenticated: authManager.isAuthenticated.bind(authManager),
    addListener: authManager.addListener.bind(authManager),
    removeListener: authManager.removeListener.bind(authManager)
  }
}

export default authPlugin