/* 全局样式 */
@import './css/normalize.css';

/* PrimeVue 水波纹效果控制 */
.p-button {
    .p-ink {
        height: 100% !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
        transform: scale(0.8);
    }
}

/* 深色主题样式修复 */
.dark-theme {

    /* 页脚样式 */
    footer {
        color: var(--footer-text, var(--text-color, #f1f5f9)) !important;
        background: var(--footer-gradient) !important;

        .text-footer-title {
            color: var(--footer-title, var(--text-color, #f1f5f9)) !important;
        }

        .text-footer-link {
            color: var(--footer-link, var(--text-color, #f1f5f9)) !important;

            &:hover {
                color: var(--primary-color, #6366f1) !important;
            }
        }

        .footer-theme-label {
            color: var(--footer-title, var(--text-color, #f1f5f9)) !important;
        }

        .option-label {
            color: var(--text-color, #f1f5f9) !important;
        }
    }

    /* 导航栏样式 */
    header {
        background: var(--header-gradient) !important;
        color: var(--nav-text, white) !important;

        .nav-link {
            color: var(--nav-text, white) !important;
        }

        .theme-name {
            color: var(--nav-text, white) !important;
        }

        .nav-theme-toggle {
            color: var(--nav-text, white) !important;

            &:hover {
                background-color: rgba(255, 255, 255, 0.1) !important;
                color: var(--primary-color, #6366f1) !important;
            }
        }
    }

    /* 主题选择器样式 */
    .theme-panel-title {
        color: var(--text-color, #f1f5f9) !important;
    }

    /* 语言选择器样式 */
    .language-panel-title {
        color: var(--text-color, #f1f5f9) !important;
    }

    .language-name {
        color: var(--text-color, #f1f5f9) !important;
    }

    .language-english {
        color: var(--text-color-secondary, #d1d5db) !important;
    }
}

/* 亮色主题样式修复 */
body:not(.dark-theme) {

    /* 页脚样式 */
    footer {
        color: var(--text-color, #212121) !important;
        background: var(--footer-gradient) !important;

        .text-footer-title {
            color: var(--text-color, #212121) !important;
        }

        .text-footer-link {
            color: var(--text-color, #212121) !important;
        }
    }

    /* 导航栏样式 */
    header {
        background: var(--header-gradient) !important;
        color: white !important;
    }
}