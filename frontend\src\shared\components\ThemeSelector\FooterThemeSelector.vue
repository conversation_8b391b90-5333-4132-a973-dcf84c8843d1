<template>
    <div class="footer-theme-selector">
        <div class="footer-theme-label">{{ t('theme.selectTheme') }}</div>

        <!-- 主题家族选择 -->
        <div class="theme-family-selector">
            <div class="family-buttons">
                <Button v-for="(family, key) in themeGroups" 
                    :key="key" 
                    :class="['family-button p-button-text', { 'active': currentThemeFamily === key }]"
                    @click="selectThemeFamily(key)">
                    {{ t(`theme.${key}Family`) }}
                </Button>
            </div>
        </div>

        <!-- 主题选择器 -->
        <div class="footer-theme-buttons">
            <div v-for="colorScheme in uniqueColorSchemes" :key="colorScheme" class="theme-button"
                :class="{ 'active': currentColorScheme === colorScheme }" 
                :title="t(`theme.${colorScheme}`)"
                @click="selectColorScheme(colorScheme)">
                <div class="theme-color-circle" 
                    :style="{ backgroundColor: getColorForScheme(colorScheme) }">
                    <i v-if="currentColorScheme === colorScheme" class="pi pi-check"></i>
                </div>
            </div>
        </div>

        <!-- 深色模式切换 -->
        <div class="footer-theme-options">
            <div class="option-item">
                <Checkbox v-model="darkMode" binary inputId="footer-dark-mode" />
                <label for="footer-dark-mode" class="option-label ml-2">
                    {{ t('theme.darkMode') }}
                </label>
            </div>

            <div class="option-item">
                <Checkbox v-model="followSystem" binary inputId="footer-follow-system" />
                <label for="footer-follow-system" class="option-label ml-2">
                    {{ t('theme.followSystem') }}
                </label>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { THEME_COLORS } from '@/config/theme.config';
import { useThemeStore } from '@/store/modules/theme';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

// i18n
const { t } = useI18n();

// 使用Pinia主题存储
const themeStore = useThemeStore();

// 当前选择的主题家族
const currentThemeFamily = ref(themeStore.currentThemeFamily);

// 当前主题颜色方案
const currentColorScheme = computed(() => themeStore.currentColorScheme);

// 获取当前主题家族的唯一颜色方案
const uniqueColorSchemes = computed(() => {
    return themeStore.currentFamilyColorSchemes;
});

// 主题分组
const themeGroups = computed(() => themeStore.themeGroups);

// 是否为深色模式
const darkMode = computed({
    get: () => themeStore.isDark,
    set: () => themeStore.toggleDarkMode()
});

// 是否跟随系统主题
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 获取颜色方案的颜色
function getColorForScheme(colorScheme: string): string {
    const colorConfig = THEME_COLORS[colorScheme as keyof typeof THEME_COLORS];
    return colorConfig ? (darkMode.value ? colorConfig.dark : colorConfig.light) : '#cccccc';
}

// 选择主题家族
const selectThemeFamily = (family: string) => {
    currentThemeFamily.value = family as 'aura' | 'lara' | 'md';
};

// 选择颜色方案
const selectColorScheme = (colorScheme: string) => {
    const code = `${currentThemeFamily.value}${colorScheme.charAt(0).toUpperCase() + colorScheme.slice(1)}`;
    themeStore.setTheme(code);
};
</script>

<style scoped lang="scss">
.footer-theme-selector {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-theme-label {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--footer-text, var(--text-color));
}

.theme-family-selector {
    margin-bottom: 0.5rem;
    
    .family-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        
        .family-button {
            font-size: 0.75rem;
            padding: 0.25rem 0.6rem;
            border-radius: 1rem;
            
            &.active {
                background-color: var(--primary-color);
                color: var(--primary-color-text);
            }
        }
    }
}

.footer-theme-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.theme-button {
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: scale(1.1);
    }

    &.active .theme-color-circle {
        box-shadow: 0 0 0 2px var(--primary-color);
    }
}

.theme-color-circle {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 1px solid var(--surface-border);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.footer-theme-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.option-item {
    padding: 0.5rem 0;
    display: flex;
    align-items: center;

    .option-label {
        cursor: pointer;
        font-size: 0.8rem;
        color: var(--footer-text, var(--text-color));
        font-weight: 500;
        display: flex;
        align-items: center;
    }
}
</style>