<template>
    <div class="footer-theme-selector">
        <div class="footer-theme-label">{{ t('theme.selectTheme') }}</div>

        <!-- 主题选择器 -->
        <div class="footer-theme-buttons">
            <div v-for="theme in availableThemes" :key="theme.name" class="theme-button"
                :class="{ 'active': currentTheme === theme.name }" :title="t(`theme.${theme.code}`)"
                @click="selectTheme(theme.name)">
                <div class="theme-color-circle" :style="{ backgroundColor: theme.primary || '#4361ee' }">
                    <i v-if="currentTheme === theme.name" class="pi pi-check"></i>
                </div>
            </div>
        </div>

        <!-- 深色模式切换 -->
        <div class="footer-theme-options">
            <div class="option-item">
                <div class="flex align-items-center">
                    <Checkbox v-model="darkMode" binary inputId="footer-dark-mode" />
                    <label for="footer-dark-mode" class="option-label ml-2">
                        {{ t('theme.darkMode') }}
                    </label>
                </div>
            </div>

            <div class="option-item">
                <div class="flex align-items-center">
                    <Checkbox v-model="followSystem" binary inputId="footer-follow-system" />
                    <label for="footer-follow-system" class="option-label ml-2">
                        {{ t('theme.followSystem') }}
                    </label>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { ThemeConfig } from '@/shared/themes/theme-manager';
import { themeManager } from '@/shared/themes/theme-manager';
import { useThemeStore } from '@/store/modules/theme';
import Checkbox from 'primevue/checkbox';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

// i18n
const { t } = useI18n();

// 使用Pinia主题存储
const themeStore = useThemeStore();

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme);

// 可用主题列表
const availableThemes = computed(() => {
    return themeManager.availableThemes.value as ThemeConfig[];
});

// 是否为深色模式
const darkMode = computed({
    get: () => themeStore.isDark,
    set: () => themeStore.toggleDarkMode()
});

// 是否跟随系统主题
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 选择主题
const selectTheme = (themeName: string) => {
    themeStore.setTheme(themeName as any); // 类型转换解决TypeScript问题
};

// 添加调试日志
import { onMounted } from 'vue';
onMounted(() => {
    console.log('[FooterThemeSelector] Mounted, current theme:', currentTheme.value);
});
</script>

<style scoped lang="scss">
.footer-theme-selector {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-theme-label {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--text-color, var(--color-text, #212121));
}

.footer-theme-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.theme-button {
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: scale(1.1);
    }

    &.active .theme-color-circle {
        box-shadow: 0 0 0 2px var(--primary-color, var(--color-primary, #3b82f6));
    }
}

.theme-color-circle {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    border: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.footer-theme-options {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.option-item {
    .option-label {
        cursor: pointer;
        font-size: 0.8rem;
        color: var(--text-color, var(--color-text, #212121));
        font-weight: 500;
    }
}
</style>