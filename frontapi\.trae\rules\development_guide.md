# 多媒体内容管理平台开发指南

## 🎯 项目概述

这是一个基于Go + Fiber的多媒体内容管理平台，支持视频、小说、图片、短视频等多种内容类型的管理。

### 核心技术栈
- **后端框架**: Fiber v2 (高性能HTTP框架)
- **数据库**: MySQL 8.0+ (主数据库) + MongoDB (文档存储)
- **缓存**: Redis 6.0+ (缓存 + 会话存储)
- **认证**: JWT + Casbin (权限控制)
- **ORM**: GORM v2 (数据库操作)
- **验证**: Go Playground Validator (数据验证)

## 📁 项目结构规范

```
frontapi/
├── cmd/                    # 应用程序入口
│   ├── admin/             # 管理后台服务
│   └── api/               # 前台API服务
├── config/                # 配置管理
├── internal/              # 内部业务逻辑
│   ├── admin/            # 管理后台模块
│   ├── api/              # 前台API模块
│   ├── container/        # 依赖注入容器
│   ├── handlers/         # HTTP处理器
│   ├── hooks/            # 钩子系统
│   ├── middleware/       # 中间件
│   ├── migrations/       # 数据库迁移
│   ├── models/           # 数据模型
│   ├── repository/       # 数据访问层
│   ├── routes/           # 路由定义
│   ├── service/          # 业务逻辑层
│   └── validation/       # 数据验证
├── pkg/                   # 公共包
│   ├── auth/             # 认证相关
│   ├── database/         # 数据库连接
│   ├── redis/            # Redis连接
│   ├── utils/            # 工具函数
│   └── validator/        # 验证器
├── storage/              # 文件存储
├── docs/                 # 项目文档
└── scripts/              # 脚本文件
```

## 🔧 开发环境配置

### 1. 环境变量配置 (.env)
```env
# 服务器配置
SERVER_PORT=8080
ADMIN_PORT=8081
SERVER_HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=frontapi

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 管理后台Redis
ADMIN_REDIS_HOST=localhost
ADMIN_REDIS_PORT=6379
ADMIN_REDIS_PASSWORD=
ADMIN_REDIS_DB=1

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE_HOURS=24

# MongoDB配置
MONGO_URI=mongodb://localhost:27017
MONGO_DATABASE=frontapi

# 文件上传配置
UPLOAD_PATH=./storage/uploads
MAX_FILE_SIZE=10485760  # 10MB
```

### 2. 开发工具配置

#### VS Code 推荐插件
```json
{
    "recommendations": [
        "golang.go",
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss"
    ]
}
```

#### Go 工具安装
```bash
# 安装必要的Go工具
go install github.com/cosmtrek/air@latest          # 热重载
go install github.com/swaggo/swag/cmd/swag@latest # API文档生成
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest # 代码检查
```

## 🏗️ 代码架构规范

### 1. 分层架构

```go
// Controller层 - 处理HTTP请求
type UserController struct {
    userService service.UserServiceInterface // 依赖服务层接口
}

// Service层 - 业务逻辑处理
type UserService struct {
    userRepo   repository.UserRepositoryInterface // 依赖仓库层接口
    validator  validator.ValidatorInterface       // 依赖验证器接口
    redis      redis.RedisInterface               // 依赖Redis接口
}

// Repository层 - 数据访问
type UserRepository struct {
    db *gorm.DB // 数据库连接
}
```

### 2. 依赖注入容器

```go
// container/container.go
type Container struct {
    // 数据库连接
    DB    *gorm.DB
    Redis redis.RedisInterface
    Mongo *mongo.Database
    
    // 仓库层
    UserRepo     repository.UserRepositoryInterface
    VideoRepo    repository.VideoRepositoryInterface
    
    // 服务层
    UserService  service.UserServiceInterface
    VideoService service.VideoServiceInterface
    
    // 控制器
    UserController  *handlers.UserController
    VideoController *handlers.VideoController
}

// 初始化容器
func NewContainer(cfg *config.Config) *Container {
    container := &Container{}
    
    // 初始化数据库连接
    container.DB = database.NewConnection(cfg.Database)
    container.Redis = redis.NewConnection(cfg.Redis)
    container.Mongo = mongodb.NewConnection(cfg.MongoDB)
    
    // 初始化仓库层
    container.UserRepo = repository.NewUserRepository(container.DB)
    container.VideoRepo = repository.NewVideoRepository(container.DB)
    
    // 初始化服务层
    container.UserService = service.NewUserService(container.UserRepo, container.Redis)
    container.VideoService = service.NewVideoService(container.VideoRepo, container.Redis)
    
    // 初始化控制器
    container.UserController = handlers.NewUserController(container.UserService)
    container.VideoController = handlers.NewVideoController(container.VideoService)
    
    return container
}
```

### 3. 钩子系统

```go
// hooks/user_hooks.go
type UserHooks struct {
    redis redis.RedisInterface
}

// 用户创建前钩子
func (h *UserHooks) BeforeCreate(user *models.User) error {
    // 检查用户名是否重复
    if exists := h.checkUsernameExists(user.Username); exists {
        return errors.New("username already exists") // 用户名已存在
    }
    
    // 密码加密
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
    if err != nil {
        return fmt.Errorf("failed to hash password: %w", err) // 密码加密失败
    }
    user.Password = string(hashedPassword)
    
    return nil
}

// 用户创建后钩子
func (h *UserHooks) AfterCreate(user *models.User) error {
    // 清除相关缓存
    cacheKey := fmt.Sprintf("user_list:*")
    h.redis.DelPattern(cacheKey)
    
    // 发送欢迎邮件（异步）
    go h.sendWelcomeEmail(user.Email)
    
    return nil
}
```

## 📊 数据模型规范

### 1. 基础模型

```go
// models/base.go
type BaseModel struct {
    ID        uint           `json:"id" gorm:"primarykey"`                    // 主键ID
    CreatedAt time.Time      `json:"created_at"`                              // 创建时间
    UpdatedAt time.Time      `json:"updated_at"`                              // 更新时间
    DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`                 // 软删除时间
}

// 分页模型
type Pagination struct {
    Page     int   `json:"page" form:"page" validate:"min=1"`           // 当前页码
    PageSize int   `json:"page_size" form:"page_size" validate:"min=1,max=100"` // 每页数量
    Total    int64 `json:"total"`                                      // 总记录数
    Pages    int   `json:"pages"`                                      // 总页数
}

// 计算总页数
func (p *Pagination) CalculatePages() {
    if p.PageSize > 0 {
        p.Pages = int(math.Ceil(float64(p.Total) / float64(p.PageSize)))
    }
}

// 计算偏移量
func (p *Pagination) GetOffset() int {
    return (p.Page - 1) * p.PageSize
}
```

### 2. 用户模型示例

```go
// models/user.go
type User struct {
    BaseModel
    Username    string     `json:"username" gorm:"uniqueIndex;size:50;not null" validate:"required,min=3,max=50"`     // 用户名
    Email       string     `json:"email" gorm:"uniqueIndex;size:100;not null" validate:"required,email"`               // 邮箱
    Password    string     `json:"-" gorm:"size:255;not null" validate:"required,min=6"`                              // 密码
    Nickname    string     `json:"nickname" gorm:"size:50" validate:"max=50"`                                         // 昵称
    Avatar      *string    `json:"avatar" gorm:"size:255"`                                                           // 头像URL
    Phone       *string    `json:"phone" gorm:"size:20;uniqueIndex" validate:"omitempty,len=11"`                     // 手机号
    Gender      int        `json:"gender" gorm:"default:0;comment:性别 0:未知 1:男 2:女"`                                 // 性别
    Birthday    *time.Time `json:"birthday"`                                                                         // 生日
    Status      int        `json:"status" gorm:"default:1;comment:状态 1:正常 2:禁用"`                                   // 状态
    LastLoginAt *time.Time `json:"last_login_at"`                                                                    // 最后登录时间
    LastLoginIP *string    `json:"last_login_ip" gorm:"size:45"`                                                     // 最后登录IP
    
    // 关联关系
    Videos    []Video    `json:"videos,omitempty" gorm:"foreignKey:UserID"`    // 用户上传的视频
    Novels    []Novel    `json:"novels,omitempty" gorm:"foreignKey:UserID"`    // 用户创作的小说
    Comments  []Comment  `json:"comments,omitempty" gorm:"foreignKey:UserID"`  // 用户的评论
    Favorites []Favorite `json:"favorites,omitempty" gorm:"foreignKey:UserID"` // 用户的收藏
}

// 表名
func (User) TableName() string {
    return "users"
}

// 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
    // 设置默认昵称
    if u.Nickname == "" {
        u.Nickname = u.Username
    }
    return nil
}
```

## 🌐 API设计规范

### 1. 路由设计

```go
// routes/api_routes.go
func SetupAPIRoutes(app *fiber.App, container *container.Container) {
    // API版本分组
    v1 := app.Group("/api/v1")
    
    // 公开接口（无需认证）
    public := v1.Group("/public")
    public.Post("/auth/login", container.AuthController.Login)       // 用户登录
    public.Post("/auth/register", container.AuthController.Register) // 用户注册
    public.Get("/videos", container.VideoController.GetPublicVideos) // 获取公开视频列表
    
    // 需要认证的接口
    auth := v1.Group("/auth")
    auth.Use(middleware.JWTAuth()) // JWT认证中间件
    
    // 用户相关接口
    users := auth.Group("/users")
    users.Get("/profile", container.UserController.GetProfile)      // 获取用户资料
    users.Put("/profile", container.UserController.UpdateProfile)   // 更新用户资料
    users.Post("/avatar", container.UserController.UploadAvatar)    // 上传头像
    
    // 视频相关接口
    videos := auth.Group("/videos")
    videos.Get("/", container.VideoController.GetUserVideos)        // 获取用户视频列表
    videos.Post("/", container.VideoController.CreateVideo)         // 创建视频
    videos.Get("/:id", container.VideoController.GetVideo)          // 获取视频详情
    videos.Put("/:id", container.VideoController.UpdateVideo)       // 更新视频
    videos.Delete("/:id", container.VideoController.DeleteVideo)    // 删除视频
}
```

### 2. 响应格式标准

```go
// pkg/response/response.go
type Response struct {
    Code      int         `json:"code"`      // 业务状态码
    Message   string      `json:"message"`   // 响应消息
    Data      interface{} `json:"data"`      // 响应数据
    Timestamp string      `json:"timestamp"` // 响应时间戳
}

type PaginationResponse struct {
    Code      int                    `json:"code"`      // 业务状态码
    Message   string                 `json:"message"`   // 响应消息
    Data      PaginationData         `json:"data"`      // 分页数据
    Timestamp string                 `json:"timestamp"` // 响应时间戳
}

type PaginationData struct {
    Items      interface{}            `json:"items"`      // 数据列表
    Pagination *models.Pagination     `json:"pagination"` // 分页信息
}

// 成功响应
func Success(c *fiber.Ctx, data interface{}) error {
    return c.JSON(Response{
        Code:      2000,
        Message:   "success", // 操作成功
        Data:      data,
        Timestamp: time.Now().Format(time.RFC3339),
    })
}

// 分页响应
func SuccessWithPagination(c *fiber.Ctx, items interface{}, pagination *models.Pagination) error {
    return c.JSON(PaginationResponse{
        Code:    2000,
        Message: "success", // 获取成功
        Data: PaginationData{
            Items:      items,
            Pagination: pagination,
        },
        Timestamp: time.Now().Format(time.RFC3339),
    })
}

// 错误响应
func Error(c *fiber.Ctx, code int, message string) error {
    httpStatus := getHTTPStatusFromCode(code)
    return c.Status(httpStatus).JSON(Response{
        Code:      code,
        Message:   message,
        Data:      nil,
        Timestamp: time.Now().Format(time.RFC3339),
    })
}

// 业务状态码到HTTP状态码的映射
func getHTTPStatusFromCode(code int) int {
    switch {
    case code >= 2000 && code < 3000:
        return fiber.StatusOK
    case code >= 4000 && code < 4100:
        return fiber.StatusBadRequest
    case code >= 4100 && code < 4200:
        return fiber.StatusUnauthorized
    case code >= 4300 && code < 4400:
        return fiber.StatusForbidden
    case code >= 4400 && code < 4500:
        return fiber.StatusNotFound
    case code >= 5000:
        return fiber.StatusInternalServerError
    default:
        return fiber.StatusInternalServerError
    }
}
```

### 3. 业务状态码规范

```go
// pkg/codes/codes.go
const (
    // 成功状态码 2xxx
    CodeSuccess = 2000 // 操作成功
    
    // 客户端错误 4xxx
    CodeBadRequest     = 4000 // 请求参数错误
    CodeUnauthorized   = 4001 // 未授权
    CodeForbidden      = 4003 // 禁止访问
    CodeNotFound       = 4004 // 资源不存在
    CodeConflict       = 4009 // 资源冲突
    CodeValidationFail = 4022 // 数据验证失败
    CodeTooManyRequest = 4029 // 请求过于频繁
    
    // 业务错误 4xxx
    CodeUserNotFound      = 4104 // 用户不存在
    CodeUserAlreadyExists = 4109 // 用户已存在
    CodeInvalidPassword   = 4101 // 密码错误
    CodeAccountDisabled   = 4103 // 账户已禁用
    
    CodeVideoNotFound     = 4204 // 视频不存在
    CodeVideoNotOwner     = 4203 // 非视频所有者
    
    // 服务器错误 5xxx
    CodeInternalError = 5000 // 服务器内部错误
    CodeDatabaseError = 5001 // 数据库错误
    CodeRedisError    = 5002 // Redis错误
    CodeFileError     = 5003 // 文件操作错误
)

// 状态码消息映射
var CodeMessages = map[int]string{
    CodeSuccess:           "success",
    CodeBadRequest:        "bad request",
    CodeUnauthorized:      "unauthorized",
    CodeForbidden:         "forbidden",
    CodeNotFound:          "not found",
    CodeConflict:          "conflict",
    CodeValidationFail:    "validation failed",
    CodeTooManyRequest:    "too many requests",
    CodeUserNotFound:      "user not found",
    CodeUserAlreadyExists: "user already exists",
    CodeInvalidPassword:   "invalid password",
    CodeAccountDisabled:   "account disabled",
    CodeVideoNotFound:     "video not found",
    CodeVideoNotOwner:     "not video owner",
    CodeInternalError:     "internal server error",
    CodeDatabaseError:     "database error",
    CodeRedisError:        "redis error",
    CodeFileError:         "file operation error",
}

// 获取状态码对应的消息
func GetMessage(code int) string {
    if msg, exists := CodeMessages[code]; exists {
        return msg
    }
    return "unknown error"
}
```

## 🔐 安全最佳实践

### 1. JWT认证中间件

```go
// middleware/auth.go
func JWTAuth() fiber.Handler {
    return jwtware.New(jwtware.Config{
        SigningKey:   []byte(config.Get().JWT.Secret),
        ErrorHandler: jwtError,
        SuccessHandler: func(c *fiber.Ctx) error {
            // 从token中提取用户信息
            token := c.Locals("user").(*jwt.Token)
            claims := token.Claims.(jwt.MapClaims)
            userID := uint(claims["user_id"].(float64))
            
            // 将用户ID存储到上下文中
            c.Locals("user_id", userID)
            return c.Next()
        },
    })
}

func jwtError(c *fiber.Ctx, err error) error {
    if err.Error() == "Missing or malformed JWT" {
        return response.Error(c, codes.CodeUnauthorized, "missing or malformed token") // 缺少或格式错误的token
    }
    return response.Error(c, codes.CodeUnauthorized, "invalid or expired token") // 无效或过期的token
}
```

### 2. 权限控制中间件

```go
// middleware/permission.go
func RequirePermission(permission string) fiber.Handler {
    return func(c *fiber.Ctx) error {
        userID := c.Locals("user_id").(uint)
        
        // 使用Casbin检查权限
        enforcer := casbin.GetEnforcer()
        allowed, err := enforcer.Enforce(fmt.Sprintf("user:%d", userID), permission, "read")
        if err != nil {
            return response.Error(c, codes.CodeInternalError, "permission check failed") // 权限检查失败
        }
        
        if !allowed {
            return response.Error(c, codes.CodeForbidden, "insufficient permissions") // 权限不足
        }
        
        return c.Next()
    }
}
```

### 3. 请求限流中间件

```go
// middleware/rate_limit.go
func RateLimit(max int, duration time.Duration) fiber.Handler {
    return limiter.New(limiter.Config{
        Max:        max,
        Expiration: duration,
        KeyGenerator: func(c *fiber.Ctx) string {
            // 基于IP地址限流
            return c.IP()
        },
        LimitReached: func(c *fiber.Ctx) error {
            return response.Error(c, codes.CodeTooManyRequest, "rate limit exceeded") // 请求频率超限
        },
    })
}
```

## 📝 日志规范

### 1. 日志配置

```go
// pkg/logger/logger.go
type Logger struct {
    *logrus.Logger
}

func NewLogger() *Logger {
    log := logrus.New()
    
    // 设置日志格式
    log.SetFormatter(&logrus.JSONFormatter{
        TimestampFormat: time.RFC3339,
        FieldMap: logrus.FieldMap{
            logrus.FieldKeyTime:  "timestamp",
            logrus.FieldKeyLevel: "level",
            logrus.FieldKeyMsg:   "message",
        },
    })
    
    // 设置日志级别
    log.SetLevel(logrus.InfoLevel)
    
    // 设置输出
    log.SetOutput(os.Stdout)
    
    return &Logger{log}
}

// 记录请求日志
func (l *Logger) LogRequest(c *fiber.Ctx, duration time.Duration) {
    l.WithFields(logrus.Fields{
        "method":     c.Method(),
        "path":       c.Path(),
        "status":     c.Response().StatusCode(),
        "duration":   duration.Milliseconds(),
        "ip":         c.IP(),
        "user_agent": c.Get("User-Agent"),
    }).Info("HTTP Request") // HTTP请求日志
}

// 记录错误日志
func (l *Logger) LogError(err error, context map[string]interface{}) {
    l.WithFields(context).Error(err.Error())
}
```

### 2. 日志中间件

```go
// middleware/logger.go
func Logger(logger *logger.Logger) fiber.Handler {
    return func(c *fiber.Ctx) error {
        start := time.Now()
        
        // 处理请求
        err := c.Next()
        
        // 记录请求日志
        duration := time.Since(start)
        logger.LogRequest(c, duration)
        
        return err
    }
}
```

## 🧪 测试规范

### 1. 测试文件组织

```
internal/
├── handlers/
│   ├── user_handler.go
│   └── user_handler_test.go    # 控制器测试
├── service/
│   ├── user_service.go
│   └── user_service_test.go    # 服务层测试
└── repository/
    ├── user_repository.go
    └── user_repository_test.go # 仓库层测试
```

### 2. 测试工具配置

```go
// test/setup.go
func SetupTestDB() *gorm.DB {
    // 使用SQLite内存数据库进行测试
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
    if err != nil {
        panic("failed to connect test database") // 连接测试数据库失败
    }
    
    // 自动迁移测试表
    db.AutoMigrate(&models.User{}, &models.Video{})
    
    return db
}

func SetupTestRedis() redis.RedisInterface {
    // 使用mock Redis进行测试
    return &MockRedis{}
}
```

### 3. 集成测试示例

```go
// test/integration/user_test.go
func TestUserAPI(t *testing.T) {
    // 设置测试环境
    app := fiber.New()
    db := SetupTestDB()
    redis := SetupTestRedis()
    
    // 初始化容器
    container := container.NewTestContainer(db, redis)
    
    // 设置路由
    routes.SetupAPIRoutes(app, container)
    
    // 测试用例
    tests := []struct {
        name           string
        method         string
        url            string
        body           string
        expectedStatus int
        expectedCode   int
    }{
        {
            name:           "create user success", // 创建用户成功
            method:         "POST",
            url:            "/api/v1/public/auth/register",
            body:           `{"username":"testuser","email":"<EMAIL>","password":"password123"}`,
            expectedStatus: 200,
            expectedCode:   2000,
        },
        {
            name:           "create user with existing username", // 用户名已存在
            method:         "POST",
            url:            "/api/v1/public/auth/register",
            body:           `{"username":"testuser","email":"<EMAIL>","password":"password123"}`,
            expectedStatus: 400,
            expectedCode:   4109,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            req := httptest.NewRequest(tt.method, tt.url, strings.NewReader(tt.body))
            req.Header.Set("Content-Type", "application/json")
            
            resp, err := app.Test(req)
            assert.NoError(t, err)
            assert.Equal(t, tt.expectedStatus, resp.StatusCode)
            
            var response map[string]interface{}
            json.NewDecoder(resp.Body).Decode(&response)
            assert.Equal(t, float64(tt.expectedCode), response["code"])
        })
    }
}
```

## 🚀 部署规范

### 1. Docker配置

```dockerfile
# Dockerfile
FROM golang:1.23-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o api ./cmd/api
RUN CGO_ENABLED=0 GOOS=linux go build -o admin ./cmd/admin

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/api .
COPY --from=builder /app/admin .
COPY --from=builder /app/storage ./storage

EXPOSE 8080 8081

CMD ["./api"]
```

### 2. Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  api:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - MONGO_URI=mongodb://mongo:27017
    depends_on:
      - mysql
      - redis
      - mongo
    volumes:
      - ./storage:/root/storage
  
  admin:
    build: .
    command: ["./admin"]
    ports:
      - "8081:8081"
    environment:
      - DB_HOST=mysql
      - ADMIN_REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: frontapi
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
  
  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mysql_data:
  redis_data:
  mongo_data:
```

## 📚 开发工作流

### 1. 功能开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/user-management
   ```

2. **编写数据模型**
   - 在 `internal/models/` 中定义数据模型
   - 添加数据库迁移文件

3. **实现数据访问层**
   - 在 `internal/repository/` 中实现仓库接口
   - 编写仓库层单元测试

4. **实现业务逻辑层**
   - 在 `internal/service/` 中实现服务接口
   - 编写服务层单元测试

5. **实现控制器层**
   - 在 `internal/handlers/` 中实现HTTP处理器
   - 编写集成测试

6. **配置路由**
   - 在 `internal/routes/` 中添加路由配置

7. **更新依赖注入**
   - 在 `internal/container/` 中注册新的依赖

### 2. 代码提交规范

```bash
# 提交格式
git commit -m "<type>(<scope>): <description>"

# 示例
git commit -m "feat(user): add user registration API"
git commit -m "fix(auth): fix JWT token validation"
git commit -m "docs(api): update API documentation"
```

### 3. 代码审查清单

- [ ] 代码遵循项目编码规范
- [ ] 所有公共函数都有注释
- [ ] 错误处理得当
- [ ] 添加了相应的单元测试
- [ ] 测试覆盖率达到要求
- [ ] 没有硬编码的配置
- [ ] 安全性考虑充分
- [ ] 性能影响可接受

---

**记住**: 这个开发指南是活文档，随着项目的发展会持续更新。请确保团队成员都熟悉并遵循这些规范。