# 多媒体内容管理平台 API

这是一个基于Go和Fiber框架开发的企业级多媒体内容管理平台，提供完整的前台用户API和管理后台API，支持视频、图片、小说、漫画、短视频等多种内容类型的管理。

## 🚀 核心特性

### 多媒体内容管理
- **视频管理**: 完整的视频上传、转码、分类、播放统计
- **图片管理**: 图片上传、相册管理、图片处理
- **小说管理**: 章节管理、书签、阅读历史
- **漫画管理**: 分页管理、收藏、阅读进度
- **短视频**: 短视频发布、评论、点赞系统

### 用户系统
- **用户认证**: 基于JWT的双端认证系统
- **权限管理**: 基于Casbin的RBAC权限控制
- **用户等级**: 积分系统、VIP会员管理
- **社交功能**: 关注、点赞、评论、收藏

### 内容创作者系统
- **创作者认证**: 内容创作者身份验证
- **收益管理**: 创作者收益计算和分成
- **内容审核**: 自动化内容审核流程
- **数据统计**: 创作者数据分析和热度统计

### 运营管理
- **推广系统**: 邀请码、推广活动管理
- **积分系统**: 积分规则、兑换商城
- **钱包系统**: 用户钱包、提现管理
- **广告系统**: 广告位管理、投放统计

## 🛠️ 技术栈

### 后端框架
- **[Go 1.23+](https://golang.org/)** - 高性能编程语言
- **[Fiber v2](https://gofiber.io/)** - 高性能Web框架
- **[GORM](https://gorm.io/)** - 强大的ORM库
- **[Casbin](https://casbin.org/)** - 权限管理框架

### 数据存储
- **[MySQL](https://www.mysql.com/)** - 主数据库
- **[Redis](https://redis.io/)** - 缓存和会话存储
- **[MongoDB](https://www.mongodb.com/)** - 扩展数据存储

### 认证与安全
- **[JWT](https://jwt.io/)** - 用户认证
- **[bcrypt](https://pkg.go.dev/golang.org/x/crypto/bcrypt)** - 密码加密
- **CORS** - 跨域资源共享

### 工具库
- **[Validator](https://github.com/gookit/validate)** - 数据验证
- **[UUID](https://github.com/google/uuid)** - 唯一标识符
- **[Lancet](https://github.com/duke-git/lancet)** - 工具函数库

## 项目重构说明

最近完成了项目结构的重构，主要改进包括：

### 1. 服务容器和初始化逻辑重构

将服务容器和初始化逻辑从bootstrap包移至container包：
- 创建了`ServiceContainer`和`ServiceBuilder`类型，位于`internal/container/service_builder.go`
- 将各个服务的初始化函数分离到不同文件中（如`video_service_container.go`, `book_services.go`等）
- 将函数名从`initXXXServices`改为`InitXXXServices`以导出

### 2. 保持向后兼容性

在bootstrap包中提供向后兼容接口：
- 定义了`ServiceContainer`类型别名，指向`container.ServiceContainer`
- 提供了`InitServices`函数，内部调用`container.InitServices`

### 3. 路由配置优化

优化了路由配置，使所有路由都保持一致的组织方式：
- 将`routes.go`文件重构为`app_routes.go`
- 创建了`routers.go`作为主要入口点
- 重构了`admin_routes.go`以匹配新的风格
- 创建了`api_routes.go`来分离基础API路由

### 重构优势

这种重构方式的优势在于：
1. 避免了循环依赖问题
2. 代码更加模块化
3. 每个服务的初始化逻辑现在都是独立的
4. 路由注册更加清晰和一致
5. 保持了向后兼容性，不需要大量修改其他文件

## 🏗️ 项目架构

### 整体架构

本项目采用**分层架构**和**领域驱动设计(DDD)**，实现了前台用户API和管理后台API的完全分离，支持独立部署和扩展。

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway Layer                        │
│  ┌─────────────────┐              ┌─────────────────┐      │
│  │   Frontend API  │              │   Admin API     │      │
│  │   (Port: 8080)  │              │   (Port: 8081)  │      │
│  └─────────────────┘              └─────────────────┘      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Business Logic Layer                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   Service   │  │   Service   │  │   Service   │        │
│  │   Layer     │  │   Layer     │  │   Layer     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Data Access Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   MySQL     │  │    Redis    │  │   MongoDB   │        │
│  │  (Primary)  │  │   (Cache)   │  │ (Extended)  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构

```
frontapi/
├── cmd/                          # 🚀 应用入口点
│   ├── admin/main.go             # 管理后台API服务入口
│   ├── api/main.go               # 前台用户API服务入口
│   ├── mediaserver/main.go       # 媒体服务器入口
│   └── main.go                   # 统一启动入口
├── config/                       # ⚙️ 配置管理
│   ├── config.go                 # 配置加载和管理
│   ├── rbac_model.conf           # Casbin权限模型
│   └── rbac_policy.csv           # 权限策略配置
├── internal/                     # 🔒 内部业务逻辑
│   ├── admin/                    # 🛠️ 管理后台控制器
│   │   ├── auth/                 # 管理员认证
│   │   ├── users/                # 用户管理
│   │   ├── videos/               # 视频管理
│   │   ├── books/                # 小说管理
│   │   ├── comics/               # 漫画管理
│   │   ├── pictures/             # 图片管理
│   │   ├── shortvideos/          # 短视频管理
│   │   ├── content_creator/      # 创作者管理
│   │   ├── permission/           # 权限管理
│   │   ├── system/               # 系统管理
│   │   └── ...
│   ├── api/                      # 📱 前台用户控制器
│   │   ├── auth/                 # 用户认证
│   │   ├── videos/               # 视频接口
│   │   ├── books/                # 小说接口
│   │   ├── comics/               # 漫画接口
│   │   ├── pictures/             # 图片接口
│   │   ├── shortvideos/          # 短视频接口
│   │   ├── users/                # 用户接口
│   │   ├── wallets/              # 钱包接口
│   │   └── ...
│   ├── models/                   # 📊 数据模型
│   │   ├── base_model.go         # 基础模型
│   │   ├── users/                # 用户相关模型
│   │   ├── videos/               # 视频相关模型
│   │   ├── books/                # 小说相关模型
│   │   ├── comics/               # 漫画相关模型
│   │   ├── pictures/             # 图片相关模型
│   │   ├── shortvideos/          # 短视频相关模型
│   │   ├── content_creator/      # 创作者相关模型
│   │   ├── wallets/              # 钱包相关模型
│   │   └── ...
│   ├── service/                  # 🔧 业务服务层
│   │   └── [按模块组织的服务]
│   ├── repository/               # 🗄️ 数据访问层
│   │   └── [按模块组织的仓储]
│   ├── container/                # 📦 依赖注入容器
│   │   ├── service_builder.go    # 服务构建器
│   │   ├── video_service_container.go
│   │   ├── user_service_container.go
│   │   └── ...
│   ├── middleware/               # 🛡️ 中间件
│   │   ├── auth_middleware.go    # 认证中间件
│   │   ├── casbin_middleware.go  # 权限中间件
│   │   ├── cors_middleware.go    # 跨域中间件
│   │   └── ...
│   ├── hooks/                    # 🪝 钩子系统
│   │   ├── manager.go            # 钩子管理器
│   │   ├── service_hooks.go      # 服务钩子
│   │   └── common/               # 通用钩子
│   ├── routes/                   # 🛣️ 路由定义
│   │   ├── admin_routes.go       # 管理后台路由
│   │   ├── api_routes.go         # 前台API路由
│   │   └── routers.go            # 路由注册
│   ├── migrations/               # 📈 数据库迁移
│   └── validation/               # ✅ 数据验证
├── pkg/                          # 📚 公共包
│   ├── auth/                     # JWT认证
│   ├── database/                 # 数据库连接
│   ├── redis/                    # Redis操作
│   ├── mongodb/                  # MongoDB操作
│   ├── utils/                    # 工具函数
│   ├── types/                    # 自定义类型
│   └── validator/                # 验证器
├── docs/                         # 📖 项目文档
├── scripts/                      # 📜 脚本文件
├── storage/                      # 💾 文件存储
├── test/                         # 🧪 测试文件
└── migrations/                   # 🔄 数据库迁移脚本
```

## 🚀 快速开始

### 环境要求

- **Go 1.23+** - 编程语言环境
- **MySQL 8.0+** - 主数据库
- **Redis 6.0+** - 缓存数据库
- **MongoDB 4.4+** - 扩展数据库（可选）

### 安装步骤

#### 1. 克隆项目
```bash
git clone [仓库URL]
cd frontapi
```

#### 2. 安装依赖
```bash
go mod download
```

#### 3. 环境配置

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接：
```env
# 服务器配置
SERVER_HOST=localhost
SERVER_PORT=8080
ADMIN_SERVER_PORT=8081

# MySQL数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=frontapi
DB_MAX_IDLE_CONNS=10
DB_MAX_OPEN_CONNS=100

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_ADMIN_DB=1
REDIS_EXPIRY=3600

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRY=86400

# MongoDB配置（可选）
MONGO_URI=mongodb://localhost:27017
MONGO_DATABASE=frontapi_ext
```

#### 4. 数据库初始化

创建MySQL数据库：
```sql
CREATE DATABASE frontapi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

运行数据库迁移：
```bash
go run cmd/main.go migrate
```

#### 5. 启动服务

**方式一：统一启动（推荐）**
```bash
go run cmd/main.go
```

**方式二：分别启动**
```bash
# 启动前台API服务 (端口: 8080)
go run cmd/api/main.go

# 启动管理后台API服务 (端口: 8081)
go run cmd/admin/main.go

# 启动媒体服务器 (可选)
go run cmd/mediaserver/main.go
```

**方式三：使用脚本启动**
```bash
# Windows
.\start_admin.bat

# 或使用PowerShell
.\start_with_cors.ps1
```

#### 6. 验证安装

访问以下地址验证服务是否正常运行：

- **前台API**: http://localhost:8080/api/health
- **管理后台API**: http://localhost:8081/admin/health

### 🐳 Docker部署（推荐）

```bash
# 构建镜像
docker build -t frontapi .

# 使用docker-compose启动
docker-compose up -d
```

## 📚 API文档

### API概览

本项目提供两套完整的API接口：

- **前台用户API** (端口: 8080) - 面向终端用户的接口
- **管理后台API** (端口: 8081) - 面向管理员的接口

### 🔐 认证系统

#### 前台用户认证
```http
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
POST /api/auth/logout         # 用户登出
GET  /api/auth/profile        # 获取用户信息
PUT  /api/auth/profile        # 更新用户信息
POST /api/auth/refresh        # 刷新Token
POST /api/auth/forgot-password # 忘记密码
POST /api/auth/reset-password  # 重置密码
```

#### 管理后台认证
```http
POST /admin/auth/login        # 管理员登录
GET  /admin/auth/profile      # 获取管理员信息
POST /admin/auth/logout       # 管理员登出
```

### 📹 视频管理

#### 前台视频接口
```http
GET    /api/videos            # 获取视频列表
GET    /api/videos/:id        # 获取视频详情
POST   /api/videos/:id/view   # 记录观看
POST   /api/videos/:id/like   # 点赞视频
DELETE /api/videos/:id/like   # 取消点赞
GET    /api/videos/search     # 搜索视频
GET    /api/videos/category/:code # 分类视频
GET    /api/videos/trending   # 热门视频
GET    /api/videos/recommended # 推荐视频
```

#### 管理后台视频接口
```http
GET    /admin/videos          # 视频管理列表
POST   /admin/videos          # 创建视频
PUT    /admin/videos/:id      # 更新视频
DELETE /admin/videos/:id      # 删除视频
POST   /admin/videos/:id/audit # 审核视频
GET    /admin/videos/statistics # 视频统计
```

### 📚 小说管理

#### 前台小说接口
```http
GET    /api/books             # 获取小说列表
GET    /api/books/:id         # 获取小说详情
GET    /api/books/:id/chapters # 获取章节列表
GET    /api/books/chapters/:id # 获取章节内容
POST   /api/books/:id/favorite # 收藏小说
POST   /api/books/:id/bookmark # 添加书签
GET    /api/books/favorites   # 我的收藏
GET    /api/books/history     # 阅读历史
```

#### 管理后台小说接口
```http
GET    /admin/books           # 小说管理列表
POST   /admin/books           # 创建小说
PUT    /admin/books/:id       # 更新小说
DELETE /admin/books/:id       # 删除小说
POST   /admin/books/:id/chapters # 添加章节
PUT    /admin/books/chapters/:id # 更新章节
```

### 🎨 图片管理

#### 前台图片接口
```http
GET    /api/pictures          # 获取图片列表
GET    /api/pictures/:id      # 获取图片详情
GET    /api/pictures/albums   # 获取相册列表
GET    /api/pictures/albums/:id # 获取相册详情
POST   /api/pictures/:id/like # 点赞图片
POST   /api/pictures/:id/collect # 收藏图片
```

### 📱 短视频管理

#### 前台短视频接口
```http
GET    /api/shortvideos       # 获取短视频列表
GET    /api/shortvideos/:id   # 获取短视频详情
POST   /api/shortvideos       # 发布短视频
POST   /api/shortvideos/:id/like # 点赞短视频
POST   /api/shortvideos/:id/comment # 评论短视频
GET    /api/shortvideos/feed  # 推荐流
```

### 👥 用户管理

#### 前台用户接口
```http
GET    /api/users/profile     # 获取个人信息
PUT    /api/users/profile     # 更新个人信息
GET    /api/users/:id         # 获取用户信息
POST   /api/users/:id/follow  # 关注用户
DELETE /api/users/:id/follow  # 取消关注
GET    /api/users/followers   # 我的粉丝
GET    /api/users/following   # 我的关注
```

#### 管理后台用户接口
```http
GET    /admin/users           # 用户管理列表
GET    /admin/users/:id       # 获取用户详情
PUT    /admin/users/:id       # 更新用户信息
POST   /admin/users/:id/ban   # 封禁用户
POST   /admin/users/:id/unban # 解封用户
GET    /admin/users/statistics # 用户统计
```

### 💰 钱包系统

#### 前台钱包接口
```http
GET    /api/wallet            # 获取钱包信息
GET    /api/wallet/transactions # 交易记录
POST   /api/wallet/recharge   # 充值
POST   /api/wallet/withdraw   # 提现申请
GET    /api/wallet/withdraw/records # 提现记录
```

### 🎯 积分系统

#### 前台积分接口
```http
GET    /api/points            # 获取积分信息
GET    /api/points/history    # 积分历史
POST   /api/points/exchange   # 积分兑换
GET    /api/points/rules      # 积分规则
```

### 🏆 创作者系统

#### 前台创作者接口
```http
POST   /api/creator/apply     # 申请成为创作者
GET    /api/creator/profile   # 创作者信息
GET    /api/creator/revenue   # 收益统计
GET    /api/creator/content   # 我的作品
```

### 🔧 系统接口

#### 通用系统接口
```http
GET    /api/system/config     # 系统配置
GET    /api/system/countries  # 国家列表
POST   /api/system/upload     # 文件上传
GET    /api/system/tags       # 标签列表
```

## 📋 接口规范

### 🌐 基础信息

#### 服务端口
- **前台用户API**: `http://localhost:8080`
- **管理后台API**: `http://localhost:8081`

#### 请求格式
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
User-Agent: YourApp/1.0
```

#### 认证方式
- **JWT Token**: 用于用户身份验证
- **API Key**: 用于第三方服务集成（可选）
- **Session**: 管理后台会话管理

### 📤 请求规范

#### 标准请求结构
```json
{
  "data": {
    "field1": "value1",
    "field2": "value2"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

#### 分页参数
```json
{
  "page": 1,
  "limit": 20,
  "sort": "created_at",
  "order": "desc"
}
```

#### 搜索参数
```json
{
  "keyword": "搜索关键词",
  "category": "分类ID",
  "tags": ["标签1", "标签2"],
  "date_from": "2024-01-01",
  "date_to": "2024-12-31"
}
```

### 📥 响应格式

#### 标准响应结构
```json
{
  "code": 2000,
  "message": "成功",
  "data": {
    "id": 1,
    "title": "示例数据"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 分页响应结构
```json
{
  "code": 2000,
  "message": "成功",
  "data": {
    "list": [],
    "total": 100,
    "curPage": 1,
    "pageSize": 10,
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

#### 错误响应
```json
{
  "code": 4000,
  "message": "请求参数错误",
  "error": {
    "field": "email",
    "reason": "邮箱格式不正确"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 🚨 状态码说明

#### 成功状态码 (2xxx)
- `2000` - 请求成功
- `2001` - 创建成功
- `2004` - 删除成功（无内容返回）

#### 客户端错误 (4xxx)
- `4000` - 请求参数错误
- `4001` - 未授权（Token无效或过期）
- `4003` - 禁止访问（权限不足）
- `4004` - 资源不存在
- `4009` - 资源冲突（如重复创建）
- `4022` - 请求格式正确但语义错误
- `4029` - 请求频率限制

#### 服务器错误 (5xxx)
- `5000` - 服务器内部错误
- `5002` - 网关错误
- `5003` - 服务不可用
- `5004` - 网关超时

### 🔒 安全规范

#### 请求限制
- **频率限制**: 每分钟最多100次请求
- **文件上传**: 单文件最大50MB
- **请求超时**: 30秒

#### 数据验证
- 所有输入数据进行严格验证
- SQL注入防护
- XSS攻击防护
- CSRF保护

### 📊 数据格式

#### 时间格式
- **ISO 8601**: `2024-01-01T12:00:00Z`
- **时区**: UTC

#### 文件上传
```json
{
  "file": "base64编码的文件内容",
  "filename": "example.jpg",
  "content_type": "image/jpeg"
}
```

#### 多语言支持
```http
Accept-Language: zh-CN,en-US;q=0.9
```

### 🔄 版本控制

- **API版本**: 通过URL路径指定 `/api/v1/`
- **向后兼容**: 保持至少2个版本的兼容性
- **废弃通知**: 提前30天通知API废弃

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

### 📋 贡献流程

1. **Fork 项目**
   ```bash
   git clone https://github.com/yourusername/frontapi.git
   cd frontapi
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **开发和测试**
   ```bash
   # 安装依赖
   go mod tidy
   
   # 运行测试
   go test ./...
   
   # 代码格式化
   go fmt ./...
   
   # 代码检查
   golangci-lint run
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   ```

5. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **创建 Pull Request**
   - 详细描述你的更改
   - 确保所有测试通过
   - 等待代码审查

### 📝 提交规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式化
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

### 🔍 代码规范

- 遵循 Go 官方代码规范
- 使用有意义的变量和函数名
- 添加必要的注释
- 保持函数简洁（建议不超过50行）
- 编写单元测试

### 🐛 问题报告

发现bug？请创建 Issue 并包含：

- 详细的问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息（Go版本、操作系统等）

### 💡 功能建议

有新想法？欢迎创建 Feature Request：

- 清晰描述功能需求
- 说明使用场景
- 提供可能的实现方案

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

```
MIT License

Copyright (c) 2024 多媒体内容管理平台

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

### 核心贡献者
- [@your-username](https://github.com/your-username) - 项目创建者和维护者

### 技术支持
- [Go](https://golang.org/) - 编程语言
- [Fiber](https://gofiber.io/) - Web框架
- [GORM](https://gorm.io/) - ORM框架
- [Redis](https://redis.io/) - 缓存数据库
- [MongoDB](https://www.mongodb.com/) - 文档数据库

---

<div align="center">
  <p>如果这个项目对你有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by the 多媒体内容管理平台 team</p>
</div>

# FrontAPI 系统架构文档

## 系统概述

FrontAPI 是一个基于 Go 语言和 Fiber 框架构建的后端 API 服务，主要提供视频、短视频、帖子、图片等内容的管理和访问功能。系统采用了清晰的分层架构，遵循依赖注入和关注点分离的设计原则。

## 系统架构

### 架构分层

FrontAPI 采用经典的分层架构设计，主要包含以下几层：

1. **API/Handler 层**：处理 HTTP 请求、参数验证、返回响应
2. **Service 层**：实现业务逻辑
3. **Repository 层**：负责数据访问和持久化
4. **Model 层**：定义数据模型

### 目录结构

```
frontapi/
├── cmd/                # 应用入口
│   ├── admin/          # 管理后台API入口
│   ├── api/            # 前端用户API入口
│   └── main.go         # 主入口文件
├── config/             # 配置文件
├── internal/           # 内部包
│   ├── admin/          # 管理后台控制器
│   ├── api/            # 前端用户控制器
│   ├── bootstrap/      # 应用初始化（向后兼容）
│   ├── container/      # 服务容器和初始化
│   ├── middleware/     # 中间件
│   ├── models/         # 数据模型
│   ├── repository/     # 数据访问层
│   ├── routes/         # 路由定义
│   │   ├── admin/      # 管理后台路由
│   │   ├── api/        # 前端用户路由
│   │   ├── admin_routes.go # 管理后台主路由
│   │   ├── api_routes.go   # 前端API基础路由
│   │   ├── app_routes.go   # 前端应用路由
│   │   └── routers.go      # 路由注册入口
│   ├── service/        # 业务逻辑层
│   └── validator/      # 请求验证
├── migrations/         # 数据库迁移
├── pkg/                # 公共包
└── README.md           # 项目说明
```

## 核心组件

### 1. API/Handler 层

位于 `internal/api` 目录，包含所有的 API 处理器实现，负责：

- 请求参数解析和验证
- 调用相应的 Service 层处理业务逻辑
- 格式化并返回响应

主要组件：
- `BaseController`：基础控制器，提供公共方法
- `PostHandler`：帖子相关 API
- `VideoHandler`：视频相关 API
- `ShortVideoHandler`：短视频相关 API
- `PictureHandler`：图片相关 API
- 等其他功能模块

### 2. Service 层

位于 `internal/service` 目录，实现业务逻辑，包括：

- 处理复杂的业务规则
- 调用 Repository 层进行数据操作
- 处理事务和异常

### 3. Repository 层

位于 `internal/repository` 目录，负责：

- 数据访问和持久化
- 封装数据库操作
- 提供数据查询和操作接口

### 4. Model 层

位于 `internal/models` 目录，定义：

- 数据结构
- 表关系
- 验证规则

### 5. 路由管理

位于 `internal/routes` 目录，负责：

- 注册 API 路由
- 配置路由组
- 应用路由中间件

### 6. 中间件

位于 `internal/middleware` 目录，提供：

- 认证与授权
- 请求日志
- 错误处理
- CORS 支持

### 7. 引导流程

位于 `internal/bootstrap` 目录，负责：

- 服务初始化和依赖注入
- 服务容器管理

## 依赖注入与管理

系统使用简洁的依赖注入模式，通过 `ServiceContainer` 管理所有服务实例，便于：

- 集中管理服务依赖
- 便于单元测试（依赖可被模拟）
- 清晰的依赖关系

## handlers 目录分析

经过分析，`internal/handlers` 目录目前只包含一个 `example_handler.go` 文件，该文件提供了处理标准化请求的示例代码。与 `internal/api` 目录相比，handlers 目录的功能有所重复。

### 存在问题

1. **职责重叠**：handlers 与 api 目录都实现了处理 HTTP 请求的功能
2. **不一致的模式**：两个目录采用了不同的编程风格和返回格式
3. **未充分利用**：当前系统主要使用 api 目录，handlers 目录使用很少

### 优化建议

1. **合并代码**：将 handlers 目录中的有用模式迁移到 api 目录或 pkg/utils 中
2. **删除 handlers 目录**：移除冗余的目录，简化项目结构
3. **统一编程风格**：确保所有 API 处理器遵循一致的模式和返回格式

## 事件与钩子系统评估

目前系统未实现事件和钩子机制。引入这些机制可能带来以下好处：

### 事件系统的潜在优势

1. **解耦系统组件**：通过事件发布/订阅模式，减少组件间的直接依赖
2. **支持异步处理**：某些操作（如日志记录、通知发送）可异步执行，提高响应速度
3. **扩展性增强**：新功能可以通过订阅现有事件来实现，无需修改原有代码

### 钩子系统的潜在优势

1. **自定义行为**：允许在关键点注入自定义代码，而无需修改核心逻辑
2. **可插拔功能**：某些功能可以通过钩子实现，便于启用或禁用

### 建议实现方案

如果决定引入事件/钩子系统，建议：

1. **创建事件包**：在 `pkg/events` 目录下实现简单的事件总线
2. **定义标准事件**：为主要操作（创建、更新、删除等）定义标准事件
3. **添加钩子接口**：在 Service 层提供钩子接口，允许注册前/后处理函数
4. **渐进式采用**：优先在新功能中使用，逐步重构现有代码

## 结论

FrontAPI 系统架构整体设计良好，采用了清晰的分层结构和依赖注入模式。但 handlers 目录存在冗余，建议移除或合并到 api 目录。

引入事件和钩子系统可以提高系统的灵活性和可扩展性，特别是对于需要处理异步任务和自定义行为的场景。建议根据实际业务需求，考虑在下一阶段的开发中逐步引入这些机制。

建议操作：
1. 删除 handlers 目录，将有用的工具函数移至 pkg/utils
2. 创建简单的事件系统，优先用于通知和日志记录等异步场景
3. 在关键服务中逐步引入钩子机制，增强系统的可扩展性

## 数据包装器验证器

为了支持以下格式的API请求：

```json
{
  "data": {
    "name": "视频名称",
    "code": "video_code"
  }
}
```

或者带有分页信息的格式：

```json
{
  "data": {
    "name": "视频名称",
    "code": "video_code"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

我们添加了`ValidateDataWrapper`函数，使用原始JSON解析的方式直接处理请求数据，避免了常规解析产生的字段名称问题：

1. 接收带有data字段的请求
2. 将data字段内容直接解析到目标模型
3. 对模型执行验证规则
4. 将page字段信息存储到Fiber的本地上下文中，以便后续使用

### 使用示例

```go
// 定义要验证的数据结构
var req videos.CreateCategoryRequest

// 使用ValidateDataWrapper验证请求
if err := validator.ValidateDataWrapper(c, &req); err != nil {
    return err // 验证器已经返回了错误响应
}

// 使用验证后的数据进行后续操作
categoryID, err := categoryService.CreateCategory(c.Context(), &req)
```

### 获取分页信息

```go
// 获取默认分页信息
pageNo := 1
pageSize := 10

// 尝试从上下文中获取分页信息
if pageData := c.Locals("page"); pageData != nil {
    if pageMap, ok := pageData.(map[string]interface{}); ok {
        if pn, ok := pageMap["pageNo"].(float64); ok {
            pageNo = int(pn)
        }
        if ps, ok := pageMap["pageSize"].(float64); ok {
            pageSize = int(ps)
        }
    }
}
```

## 媒体服务器

项目包含一个专用的媒体服务器，用于提供图片和视频的静态文件服务。媒体服务器支持视频流播放，自动处理Range请求，并为不同类型的媒体文件配置适当的缓存策略。

### 特性

- 从.env文件读取配置
- 支持小图片、大图片和视频的分离存储
- 对视频文件提供流媒体服务，支持Range请求
- 自动设置适当的内容类型和缓存策略
- 支持CORS
- 提供健康检查接口

### 配置

媒体服务器从.env文件中读取以下配置：

```
# 媒体服务器端口
MEDIA_SERVER_PORT=8082

# CORS配置
CORS_ALLOW_ORIGINS=*

# 小图片配置（分类图标、头像等）
UPLOAD_SMALL_IMAGE_PATH=../storage/static/images
UPLOAD_SMALL_IMAGE_URL=http://localhost:8082/static/images

# 图片模块的图片
UPLOAD_PICTURE_PATH=../storage/pictures
UPLOAD_PICTURE_URL=http://localhost:8082/pictures

# 视频
UPLOAD_VIDEO_PATH=../storage/videos
UPLOAD_VIDEO_URL=http://localhost:8082/videos
```

### 运行

```bash
# 进入媒体服务器目录
cd cmd/mediaserver

# 直接运行
make run

# 或者构建后运行
make build
../../bin/mediaserver
```

### 访问

媒体服务器启动后，可以通过以下URL访问文件：

- 小图片： `http://localhost:8082/static/images/{filename}`
- 图片： `http://localhost:8082/pictures/{filename}`
- 视频： `http://localhost:8082/videos/{filename}`

### 健康检查

可以通过以下URL检查媒体服务器的运行状态：

```
GET http://localhost:8082/health
```

响应示例：

```json
{
  "status": "ok",
  "paths": {
    "/static/images": "../storage/static/images",
    "/pictures": "../storage/pictures",
    "/videos": "../storage/videos"
  },
  "urls": {
    "/static/images": "http://localhost:8082/static/images",
    "/pictures": "http://localhost:8082/pictures",
    "/videos": "http://localhost:8082/videos"
  }
}
```

## 运行服务

项目提供了一个单一的入口文件来启动API、管理后台和媒体服务器，可以通过命令行参数选择启用哪些服务。

### 启动所有服务

```bash
# 启动所有服务(API、Admin、Media)
go run cmd/main.go
```

### 选择性启动服务

可以通过命令行参数选择启用哪些服务：

```bash
# 仅启动API服务器
go run cmd/main.go -admin=false -media=false

# 仅启动管理后台服务器
go run cmd/main.go -api=false -media=false

# 仅启动媒体服务器
go run cmd/main.go -api=false -admin=false

# 启动API和媒体服务器，不启动管理后台
go run cmd/main.go -admin=false
```

### 服务端口配置

各服务器使用的默认端口：

- API服务器: 8080 (配置项: SERVER_PORT)
- 管理后台服务器: 8081 (配置项: ADMIN_SERVER_PORT)
- 媒体服务器: 8082 (配置项: MEDIA_SERVER_PORT)

可以在.env文件中修改这些端口设置。

### 日志输出

各服务器的日志输出使用不同的前缀标识：

- API服务器: `[API]`
- 管理后台服务器: `[Admin]`
- 媒体服务器: `[Media]`

这使您可以轻松区分不同服务器的日志信息。