package comics

import (
	"frontapi/internal/admin"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"

	"frontapi/internal/models/comics"
	comicSrv "frontapi/internal/service/comics"
	comicsValidator "frontapi/internal/validation/comics"
)

// ComicCategoryController 漫画分类控制器
type ComicCategoryController struct {
	admin.BaseController
	ComicCategoryService comicSrv.ComicCategoryService
}

// NewComicCategoryController 创建漫画分类控制器实例
func NewComicCategoryController(service comicSrv.ComicCategoryService) *ComicCategoryController {
	return &ComicCategoryController{
		ComicCategoryService: service,
	}
}

// List 获取漫画分类列表
func (c *ComicCategoryController) List(ctx *fiber.Ctx) error {
	// 获取分页参数
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	status := -999
	err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	if err != nil {
		return c.InternalServerError(ctx, "参数解析失败")
	}
	condition := map[string]interface{}{
		"keyword": keyword,
		"status":  status,
	}

	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "sort_order DESC"
	}
	// 获取分类列表
	categories, total, err := c.ComicCategoryService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取分类列表失败: "+err.Error())
	}

	return c.SuccessList(ctx, categories, total, pageNo, pageSize)
}

// Create 创建漫画分类
func (c *ComicCategoryController) Create(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req comicsValidator.ComicCategoryCreateRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	var category comics.ComicCategory
	if err := utils.SmartCopy(req, &category); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 创建分类
	if _, err := c.ComicCategoryService.Create(ctx.Context(), &category); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.Success(ctx, category)
}

// Update 更新漫画分类
func (c *ComicCategoryController) Update(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "分类ID不能为空", nil)
	}

	// 解析请求参数
	var req comicsValidator.ComicCategoryUpdateRequest
	err = c.ParseRequestData(ctx, &req)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}

	var category comics.ComicCategory
	if err := utils.SmartCopy(req, &category); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 更新分类
	if err := c.ComicCategoryService.UpdateById(ctx.Context(), id, &category); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.Success(ctx, category)
}

// Delete 删除漫画分类
func (c *ComicCategoryController) Delete(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "分类ID不能为空", nil)
	}

	// 删除分类
	if err := c.ComicCategoryService.Delete(ctx.Context(), id); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除分类成功")
}

// GetByID 获取漫画分类详情
func (c *ComicCategoryController) GetByID(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "分类ID不能为空", nil)
	}
	// 获取分类详情
	category, err := c.ComicCategoryService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取分类详情失败: "+err.Error())
	}

	return c.Success(ctx, category)
}
