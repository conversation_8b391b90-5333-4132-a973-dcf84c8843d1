<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :modal-class="modalClass"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :class="['slinky-dialog', 'detail-dialog', dialogClass]"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <div class="dialog-content">
      <!-- 详情信息头部 -->
      <div v-if="showDetailInfo" class="detail-info">
        <el-alert
          :title="`查看详情: ${getDetailTitle()}`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 详情内容 -->
      <div class="detail-content">
        <!-- 基础信息卡片 -->
        <el-card v-if="basicFields.length > 0" class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>基础信息</span>
            </div>
          </template>
          <div class="detail-grid">
            <div
              v-for="field in basicFields"
              :key="field.prop"
              class="detail-item"
              :class="field.class"
            >
              <div class="label">{{ field.label }}:</div>
              <div class="value">
                <!-- 文本类型 -->
                <span v-if="!field.type || field.type === 'text'">
                  {{ formatValue(data[field.prop], field) }}
                </span>
                
                <!-- 标签类型 -->
                <el-tag
                  v-else-if="field.type === 'tag'"
                  :type="(getTagType(data[field.prop], field) as any)"
                  :effect="field.tagEffect || 'light'"
                  :size="field.size || 'default'"
                >
                  {{ getTagLabel(data[field.prop], field) }}
                </el-tag>
                
                <!-- 图片类型 -->
                <div v-else-if="field.type === 'image'" class="image-container">
                  <el-image
                    v-if="data[field.prop]"
                    :src="data[field.prop]"
                    :preview-src-list="[data[field.prop]]"
                    :initial-index="0"
                    fit="cover"
                    class="detail-image"
                    :style="{ width: field.imageWidth || '80px', height: field.imageHeight || '80px' }"
                  />
                  <span v-else class="empty-text">无图片</span>
                </div>
                
                <!-- 链接类型 -->
                <el-link
                  v-else-if="field.type === 'link'"
                  :href="data[field.prop]"
                  :type="(field.linkType as any) || 'primary'"
                  :underline="field.underline !== false"
                  target="_blank"
                >
                  {{ field.linkText || data[field.prop] }}
                </el-link>
                
                <!-- 日期类型 -->
                <span v-else-if="field.type === 'date'">
                  {{ formatDate(data[field.prop], field.dateFormat) }}
                </span>
                
                <!-- 数字类型 -->
                <span v-else-if="field.type === 'number'">
                  {{ formatNumber(data[field.prop], field) }}
                </span>
                
                <!-- 布尔类型 -->
                <el-tag
                  v-else-if="field.type === 'boolean'"
                  :type="data[field.prop] ? 'success' : 'danger'"
                  size="small"
                >
                  {{ data[field.prop] ? (field.trueText || '是') : (field.falseText || '否') }}
                </el-tag>
                
                <!-- 数组类型 -->
                <div v-else-if="field.type === 'array'" class="array-value">
                  <el-tag
                    v-for="(item, index) in (data[field.prop] || [])"
                    :key="index"
                    size="small"
                    class="array-tag"
                  >
                    {{ field.arrayFormatter ? field.arrayFormatter(item) : item }}
                  </el-tag>
                  <span v-if="!(data[field.prop] && data[field.prop].length)" class="empty-text">
                    无数据
                  </span>
                </div>
                
                <!-- JSON类型 -->
                <div v-else-if="field.type === 'json'" class="json-value">
                  <el-button
                    type="primary"
                    link
                    @click="showJsonDialog(data[field.prop], field.label)"
                  >
                    查看JSON
                  </el-button>
                </div>
                
                <!-- 自定义插槽 -->
                <template v-else-if="field.type === 'slot'">
                  <slot 
                    :name="field.slotName || field.prop"
                    :field="field"
                    :value="data[field.prop]"
                    :data="data"
                  />
                </template>
                
                <!-- 自定义渲染 -->
                <component
                  v-else-if="field.type === 'component'"
                  :is="field.component"
                  :value="data[field.prop]"
                  :data="data"
                  :field="field"
                  v-bind="field.componentProps"
                />
                
                <!-- 默认文本显示 -->
                <span v-else>
                  {{ formatValue(data[field.prop], field) }}
                </span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 扩展信息卡片 -->
        <template v-for="group in fieldGroups" :key="group.title">
          <el-card v-if="group.fields.length > 0" class="detail-card" shadow="never">
            <template #header>
              <div class="card-header">
                <el-icon v-if="group.icon">
                  <component :is="group.icon" />
                </el-icon>
                <span>{{ group.title }}</span>
              </div>
            </template>
            <div class="detail-grid">
              <div
                v-for="field in group.fields"
                :key="field.prop"
                class="detail-item"
                :class="field.class"
              >
                <div class="label">{{ field.label }}:</div>
                <div class="value">
                  <!-- 复用上面的渲染逻辑 -->
                  <span v-if="!field.type || field.type === 'text'">
                    {{ formatValue(data[field.prop], field) }}
                  </span>
                  
                  <el-tag
                    v-else-if="field.type === 'tag'"
                    :type="(getTagType(data[field.prop], field) as any)"
                    :effect="field.tagEffect || 'light'"
                    :size="field.size || 'default'"
                  >
                    {{ getTagLabel(data[field.prop], field) }}
                  </el-tag>
                  
                  <div v-else-if="field.type === 'image'" class="image-container">
                    <el-image
                      v-if="data[field.prop]"
                      :src="data[field.prop]"
                      :preview-src-list="[data[field.prop]]"
                      fit="cover"
                      class="detail-image"
                      :style="{ width: field.imageWidth || '80px', height: field.imageHeight || '80px' }"
                    />
                    <span v-else class="empty-text">无图片</span>
                  </div>
                  
                  <el-link
                    v-else-if="field.type === 'link'"
                    :href="data[field.prop]"
                    :type="(field.linkType as any) || 'primary'"
                    target="_blank"
                  >
                    {{ field.linkText || data[field.prop] }}
                  </el-link>
                  
                  <span v-else-if="field.type === 'date'">
                    {{ formatDate(data[field.prop], field.dateFormat) }}
                  </span>
                  
                  <span v-else-if="field.type === 'number'">
                    {{ formatNumber(data[field.prop], field) }}
                  </span>
                  
                  <el-tag
                    v-else-if="field.type === 'boolean'"
                    :type="data[field.prop] ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ data[field.prop] ? (field.trueText || '是') : (field.falseText || '否') }}
                  </el-tag>
                  
                  <div v-else-if="field.type === 'array'" class="array-value">
                    <el-tag
                      v-for="(item, index) in (data[field.prop] || [])"
                      :key="index"
                      size="small"
                      class="array-tag"
                    >
                      {{ field.arrayFormatter ? field.arrayFormatter(item) : item }}
                    </el-tag>
                    <span v-if="!(data[field.prop] && data[field.prop].length)" class="empty-text">
                      无数据
                    </span>
                  </div>
                  
                  <div v-else-if="field.type === 'json'" class="json-value">
                    <el-button
                      type="primary"
                      link
                      @click="showJsonDialog(data[field.prop], field.label)"
                    >
                      查看JSON
                    </el-button>
                  </div>
                  
                  <template v-else-if="field.type === 'slot'">
                    <slot 
                      :name="field.slotName || field.prop"
                      :field="field"
                      :value="data[field.prop]"
                      :data="data"
                    />
                  </template>
                  
                  <component
                    v-else-if="field.type === 'component'"
                    :is="field.component"
                    :value="data[field.prop]"
                    :data="data"
                    :field="field"
                    v-bind="field.componentProps"
                  />
                  
                  <span v-else>
                    {{ formatValue(data[field.prop], field) }}
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </template>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!-- 自定义操作按钮 -->
        <template v-for="action in actions" :key="action.name">
          <el-button
            :type="(action.type as any) || 'default'"
            :icon="action.icon"
            :loading="action.loading"
            :disabled="action.disabled"
            @click="handleAction(action)"
          >
            {{ action.label }}
          </el-button>
        </template>
        
        <!-- 关闭按钮 -->
        <el-button @click="handleClose">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- JSON查看对话框 -->
  <el-dialog
    v-model="jsonDialogVisible"
    :title="`查看${jsonDialogTitle}`"
    width="600px"
    append-to-body
  >
    <el-input
      v-model="jsonContent"
      type="textarea"
      :rows="15"
      readonly
      class="json-viewer"
    />
    <template #footer>
      <el-button @click="jsonDialogVisible = false">关闭</el-button>
      <el-button type="primary" @click="copyJson">复制</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { computed, ref } from 'vue'

// 接口定义
interface DetailField {
  prop: string
  label: string
  type?: 'text' | 'tag' | 'image' | 'link' | 'date' | 'number' | 'boolean' | 'array' | 'json' | 'slot' | 'component'
  class?: string
  group?: string
  
  // 标签类型属性
  tagOptions?: { value: any; label: string; type?: string }[]
  tagEffect?: 'dark' | 'light' | 'plain'
  
  // 图片类型属性
  imageWidth?: string
  imageHeight?: string
  
  // 链接类型属性
  linkType?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  linkText?: string
  underline?: boolean
  
  // 日期类型属性
  dateFormat?: string
  
  // 数字类型属性
  numberFormat?: string
  precision?: number
  unit?: string
  
  // 布尔类型属性
  trueText?: string
  falseText?: string
  
  // 数组类型属性
  arrayFormatter?: (item: any) => string
  
  // 通用属性
  formatter?: (value: any) => string
  size?: 'large' | 'default' | 'small'
  
  // 插槽属性
  slotName?: string
  
  // 组件属性
  component?: any
  componentProps?: Record<string, any>
}

interface FieldGroup {
  title: string
  icon?: any
  fields: DetailField[]
}

interface DetailAction {
  name: string
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: any
  loading?: boolean
  disabled?: boolean
  handler?: (data: Record<string, any>) => void
}

interface Props {
  // 基础属性
  modelValue?: boolean
  title?: string
  width?: string | number
  fullscreen?: boolean
  top?: string
  modal?: boolean
  modalClass?: string
  appendToBody?: boolean
  lockScroll?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  destroyOnClose?: boolean
  dialogClass?: string
  
  // 数据属性
  data: Record<string, any>
  fields: DetailField[]
  
  // 显示配置
  showDetailInfo?: boolean
  
  // 操作按钮
  actions?: DetailAction[]
  
  // 事件回调
  onBeforeClose?: (done: () => void) => void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '详情',
  width: '800px',
  fullscreen: false,
  top: '15vh',
  modal: true,
  appendToBody: true,
  lockScroll: true,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  destroyOnClose: false,
  showDetailInfo: true,
  actions: () => []
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'open': []
  'opened': []
  'close': []
  'closed': []
  'action': [action: string, data: Record<string, any>]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const jsonDialogVisible = ref(false)
const jsonDialogTitle = ref('')
const jsonContent = ref('')

// 计算属性
const basicFields = computed(() => {
  return props.fields.filter(field => !field.group)
})

const fieldGroups = computed((): FieldGroup[] => {
  const groups: Record<string, DetailField[]> = {}
  
  props.fields.forEach(field => {
    if (field.group) {
      if (!groups[field.group]) {
        groups[field.group] = []
      }
      groups[field.group].push(field)
    }
  })
  
  return Object.entries(groups).map(([title, fields]) => ({
    title,
    fields
  }))
})

// 辅助方法
const getDetailTitle = (): string => {
  const titleField = props.fields.find(f => f.prop === 'title' || f.prop === 'name')
  if (titleField && props.data[titleField.prop]) {
    return props.data[titleField.prop]
  }
  return '当前记录'
}

const formatValue = (value: any, field: DetailField): string => {
  if (value == null || value === '') return '暂无'
  
  if (field.formatter) {
    return field.formatter(value)
  }
  
  return String(value)
}

const formatDate = (value: any, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!value) return '暂无'
  return dayjs(value).format(format)
}

const formatNumber = (value: any, field: DetailField): string => {
  if (value == null || value === '') return '暂无'
  
  let num = Number(value)
  if (isNaN(num)) return String(value)
  
  if (field.precision !== undefined) {
    num = Number(num.toFixed(field.precision))
  }
  
  let result = num.toLocaleString()
  
  if (field.unit) {
    result += ' ' + field.unit
  }
  
  return result
}

const getTagType = (value: any, field: DetailField): string => {
  if (!field.tagOptions) return 'info'
  
  const option = field.tagOptions.find(opt => opt.value === value)
  return option?.type || 'info'
}

const getTagLabel = (value: any, field: DetailField): string => {
  if (!field.tagOptions) return String(value || '')
  
  const option = field.tagOptions.find(opt => opt.value === value)
  return option?.label || String(value || '未知')
}

const showJsonDialog = (value: any, title: string) => {
  jsonDialogTitle.value = title
  jsonContent.value = JSON.stringify(value, null, 2)
  jsonDialogVisible.value = true
}

const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(jsonContent.value)
    ElMessage.success('JSON内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 事件处理
const handleAction = (action: DetailAction) => {
  emit('action', action.name, props.data)
  action.handler?.(props.data)
}

const handleBeforeClose = (done: () => void) => {
  if (props.onBeforeClose) {
    props.onBeforeClose(done)
  } else {
    done()
  }
}

const handleOpen = () => {
  emit('open')
}

const handleOpened = () => {
  emit('opened')
}

const handleClose = () => {
  visible.value = false
  emit('close')
}

const handleClosed = () => {
  emit('closed')
}

// 暴露方法
defineExpose({
  showJsonDialog,
  copyJson
})
</script>

<style lang="scss" scoped>
.slinky-dialog.detail-dialog {
  .dialog-content {
    .detail-info {
      margin-bottom: 20px;
    }
    
    .detail-content {
      .detail-card {
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 500;
          color: var(--el-color-primary);
          
          .el-icon {
            font-size: 18px;
          }
        }
        
        .detail-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 16px;
          
          .detail-item {
            display: flex;
            
            .label {
              font-weight: 500;
              color: var(--el-text-color-regular);
              min-width: 120px;
              flex-shrink: 0;
            }
            
            .value {
              flex: 1;
              color: var(--el-text-color-primary);
              
              .empty-text {
                color: var(--el-text-color-placeholder);
                font-style: italic;
              }
              
              .image-container {
                .detail-image {
                  border-radius: var(--el-card-border-radius);
                  border: 1px solid var(--el-border-color-light);
                }
              }
              
              .array-value {
                .array-tag {
                  margin: 2px 4px 2px 0;
                }
              }
              
              .json-value {
                .el-button {
                  padding: 0;
                }
              }
            }
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

.json-viewer {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  
  :deep(.el-textarea__inner) {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .slinky-dialog.detail-dialog {
    .dialog-content {
      .detail-content {
        .detail-card {
          .detail-grid {
            grid-template-columns: 1fr;
            
            .detail-item {
              flex-direction: column;
              
              .label {
                min-width: auto;
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }
  }
}
</style> 