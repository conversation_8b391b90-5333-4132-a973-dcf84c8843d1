package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"frontapi/internal/service/base/extfollow/types"

	goredis "github.com/go-redis/redis/v8"
)

// QueryOperations 查询操作处理器
type QueryOperations struct {
	client   *goredis.Client
	cacheKey *types.CacheKey
	config   *Config
}

// NewQueryOperations 创建查询操作处理器
func NewQueryOperations(client *goredis.Client, cacheKey *types.CacheKey, config *Config) *QueryOperations {
	return &QueryOperations{
		client:   client,
		cacheKey: cacheKey,
		config:   config,
	}
}

// GetUserFollowers 获取用户粉丝列表
func (q *QueryOperations) GetUserFollowers(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	followersKey := q.cacheKey.FollowersKey(userID)

	// 使用 SSCAN 遍历集合
	iter := q.client.SScan(ctx, followersKey, 0, "", int64(limit)).Iterator()

	var records []*types.FollowRecord
	count := 0
	skip := offset

	for iter.Next(ctx) && count < limit {
		if skip > 0 {
			skip--
			continue
		}

		followerID := iter.Val()
		record := &types.FollowRecord{
			FollowerID: followerID,
			FolloweeID: userID,
			Status:     "following",
			Timestamp:  time.Now(),
		}
		records = append(records, record)
		count++
	}

	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("获取粉丝列表失败: %w", err)
	}

	return records, nil
}

// GetUserFollowing 获取用户关注列表
func (q *QueryOperations) GetUserFollowing(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	followingKey := q.cacheKey.FollowingKey(userID)

	// 使用 SSCAN 遍历集合
	iter := q.client.SScan(ctx, followingKey, 0, "", int64(limit)).Iterator()

	var records []*types.FollowRecord
	count := 0
	skip := offset

	for iter.Next(ctx) && count < limit {
		if skip > 0 {
			skip--
			continue
		}

		followeeID := iter.Val()
		record := &types.FollowRecord{
			FollowerID: userID,
			FolloweeID: followeeID,
			Status:     "following",
			Timestamp:  time.Now(),
		}
		records = append(records, record)
		count++
	}

	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("获取关注列表失败: %w", err)
	}

	return records, nil
}

// GetFollowHistory 获取关注历史
func (q *QueryOperations) GetFollowHistory(ctx context.Context, userID string, timeRange *types.TimeRange) ([]*types.FollowRecord, error) {
	historyKey := "follow_history:" + userID

	var minScore, maxScore string
	if timeRange != nil {
		minScore = strconv.FormatInt(timeRange.Start.Unix(), 10)
		maxScore = strconv.FormatInt(timeRange.End.Unix(), 10)
	} else {
		minScore = "-inf"
		maxScore = "+inf"
	}

	// 按时间范围查询历史记录
	results, err := q.client.ZRangeByScore(ctx, historyKey, &goredis.ZRangeBy{
		Min:    minScore,
		Max:    maxScore,
		Offset: 0,
		Count:  100, // 限制数量
	}).Result()

	if err != nil {
		if err == goredis.Nil {
			return []*types.FollowRecord{}, nil
		}
		return nil, fmt.Errorf("获取关注历史失败: %w", err)
	}

	var records []*types.FollowRecord
	for _, result := range results {
		var record types.FollowRecord
		if err := json.Unmarshal([]byte(result), &record); err == nil {
			records = append(records, &record)
		}
	}

	return records, nil
}

// GetMutualFollows 获取互相关注
func (q *QueryOperations) GetMutualFollows(ctx context.Context, userID1, userID2 string) ([]*types.FollowRecord, error) {
	following1Key := q.cacheKey.FollowingKey(userID1)
	following2Key := q.cacheKey.FollowingKey(userID2)

	// 使用临时key计算交集
	tempKey := "temp_mutual:" + userID1 + ":" + userID2 + ":" + strconv.FormatInt(time.Now().Unix(), 10)

	// 计算交集
	pipe := q.client.Pipeline()
	pipe.SInterStore(ctx, tempKey, following1Key, following2Key)
	pipe.Expire(ctx, tempKey, 5*time.Minute)
	pipe.SMembers(ctx, tempKey)

	results, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("计算互关用户失败: %w", err)
	}

	// 获取交集结果
	membersCmd := results[2].(*goredis.StringSliceCmd)
	mutualUserIDs, err := membersCmd.Result()
	if err != nil {
		return nil, fmt.Errorf("获取互关结果失败: %w", err)
	}

	// 清理临时key
	q.client.Del(ctx, tempKey)

	var records []*types.FollowRecord
	for _, mutualUserID := range mutualUserIDs {
		record := &types.FollowRecord{
			FollowerID: userID1,
			FolloweeID: mutualUserID,
			Status:     "mutual",
			Timestamp:  time.Now(),
		}
		records = append(records, record)
	}

	return records, nil
}
