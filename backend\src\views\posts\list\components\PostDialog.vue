<template>
  <el-dialog
    :title="type === 'add' ? '添加帖子' : '编辑帖子'"
    v-model="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    @closed="resetForm"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入帖子标题"></el-input>
      </el-form-item>

      <el-form-item label="内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入帖子内容"
        ></el-input>
      </el-form-item>

      <el-form-item label="封面" prop="cover_url">
        <div class="upload-container">
          <el-upload
            class="upload-cover"
            :action="uploadAction"
            :headers="uploadHeaders"
            :show-file-list="false"
            :before-upload="beforeCoverUpload"
            :on-success="handleCoverSuccess"
            :on-error="handleUploadError"
          >
            <div v-if="form.cover_url" class="image-preview">
              <img :src="form.cover_url" class="preview-image" />
              <div class="image-actions">
                <el-button type="primary" size="small" @click.stop="handleChangeCover">更换</el-button>
              </div>
            </div>
            <el-button v-else type="primary">上传封面图</el-button>
          </el-upload>
        </div>
      </el-form-item>

      <el-form-item label="分类" prop="category_id">
        <el-select v-model="form.category_id" placeholder="请选择分类" style="width: 100%">
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入标签，回车确认"
          style="width: 100%"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
          <el-option label="审核中" :value="0"></el-option>
          <el-option label="已发布" :value="1"></el-option>
          <el-option label="已拒绝" :value="2"></el-option>
          <el-option label="已删除" :value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="是否推荐" prop="is_featured">
        <el-switch
          v-model="form.is_featured"
          :active-value="1"
          :inactive-value="0"
        ></el-switch>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch} from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { createPost, updatePost } from '@/service/api/posts/post';
import type { PostItem, CreatePostRequest, UpdatePostRequest } from '@/types/posts';
import { ApiResponse } from '@/types/https';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String as () => 'add' | 'edit',
    default: 'add'
  },
  postData: {
    type: Object as () => PostItem | null,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'success', 'close']);

// 表单引用
const formRef = ref<FormInstance>();

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 监听postData变化
watch(() => props.postData, (val) => {
  if (val) {
    initFormData(val);
  }
}, { immediate: true });

// 监听visible变化
watch(() => props.visible, (val) => {
  if (val && props.type === 'add') {
    resetForm();
  }
});

// 表单数据
const form = reactive({
  id: '',
  title: '',
  content: '',
  cover_url: '',
  category_id: '',
  tags: [] as string[],
  status: 0,
  is_featured: 0
});

// 验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' },
    { min: 10, max: 5000, message: '内容长度在 10 到 5000 个字符', trigger: 'blur' }
  ],
  cover_url: [
    { required: true, message: '请上传封面图', trigger: 'change' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 上传相关
const uploadAction = '/api/proadm/upload';
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('token') || ''}`
}));
const submitting = ref(false);

// 分类和标签选项
const categoryOptions = ref<{ label: string, value: string }[]>([
  { label: '分类1', value: '1' },
  { label: '分类2', value: '2' }
]);

const tagOptions = ref<{ label: string, value: string }[]>([
  { label: '热门', value: '热门' },
  { label: '推荐', value: '推荐' },
  { label: '新品', value: '新品' }
]);

// 初始化表单数据
const initFormData = (data: PostItem) => {
  form.id = data.id || '';
  form.title = data.title || '';
  form.content = data.content || '';
  form.cover_url = data.cover_url || '';
  form.category_id = data.category_id || '';
  form.tags = data.tags || [];
  form.status = data.status;
  form.is_featured = data.is_featured;
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }

  if (props.type === 'add') {
    form.id = '';
    form.title = '';
    form.content = '';
    form.cover_url = '';
    form.category_id = '';
    form.tags = [];
    form.status = 0;
    form.is_featured = 0;
  }
};

// 上传前验证
const beforeCoverUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('上传封面只能是图片格式!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('上传封面大小不能超过 2MB!');
    return false;
  }
  return true;
};

// 封面上传成功
const handleCoverSuccess = (response: ApiResponse<{url: string}>, file: any) => {
  if (response.code === 2000 && response.data.url) {
    form.cover_url = response.data.url;
    ElMessage.success('封面上传成功');
  } else {
    ElMessage.error(response.message || '封面上传失败');
  }
};

// 上传失败
const handleUploadError = () => {
  ElMessage.error('上传失败，请重试');
};

// 更换封面
const handleChangeCover = () => {
  // 触发点击上传按钮
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitting.value = true;
      try {
        let res: ApiResponse<null>;

        if (props.type === 'add') {
          const postData: CreatePostRequest = {
            title: form.title,
            content: form.content,
            cover_url: form.cover_url,
            category_id: form.category_id,
            tags: form.tags,
            status: form.status,
            is_featured: form.is_featured
          };

          res = await createPost(postData) as ApiResponse<null>;
        } else {
          const postData: UpdatePostRequest = {
            id: form.id,
            title: form.title,
            content: form.content,
            cover_url: form.cover_url,
            category_id: form.category_id,
            tags: form.tags,
            status: form.status,
            is_featured: form.is_featured
          };

          res = await updatePost(postData) as ApiResponse<null>;
        }

        if (res.code === 2000) {
          ElMessage.success(props.type === 'add' ? '添加成功' : '更新成功');
          dialogVisible.value = false;
          emit('success');
          resetForm();
        } else {
          ElMessage.error(res.message || (props.type === 'add' ? '添加失败' : '更新失败'));
        }
      } catch (error) {
        console.error(error);
        ElMessage.error(props.type === 'add' ? '添加失败' : '更新失败');
      } finally {
        submitting.value = false;
      }
    } else {
      console.log('表单验证失败', fields);
    }
  });
};
</script>

<style scoped>
.upload-container {
  display: flex;
  align-items: center;
}

.upload-cover {
  display: flex;
  flex-direction: column;
}

.image-preview {
  position: relative;
  width: 200px;
  height: 120px;
  margin-bottom: 10px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.image-actions {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
}
</style>
