package posts

import (
	"frontapi/internal/typings"
)

type PostInfo struct {
	ID           string   `json:"id"`           // 帖子ID
	AuthorID     string   `json:"authorId"`     // 作者ID
	AuthorName   string   `json:"authorName"`   // 作者昵称
	AuthorAvatar string   `json:"authorAvatar"` // 作者头像
	Content      string   `json:"content"`      // 内容
	Images       []string `json:"images"`       // 帖子图片,与视频只能存在一个
	Video        string   `json:"video"`        // 帖子视频,与图片只能存在一个
	AuthorType   int8     `json:"authorType"`   // 用户类型，1普通用户，2明星
	// CategoryID   string   `json:"categoryId"`   // 分类ID
	LikeCount    int64 `json:"likeCount"`    // 点赞数
	CommentCount int64 `json:"commentCount"` // 评论数
	ViewCount    int64 `json:"viewCount"`    // 浏览次数
	Heat         int64 `json:"heat"`         // 热度
	// ShareCount   int64  `json:"shareCount"`   // 分享次数
	CreatedAt string `json:"createdAt"` // 创建时间
	// UpdatedAt    string `json:"updatedAt"`    // 更新时间
	// AuditTime    string `json:"auditTime"`    // 审核时间
	// AuditorID    string `json:"auditorId"`    // 审核人ID
	// RejectReason string `json:"rejectReason"` // 拒绝原因
	Status  int8               `json:"status"`  // 状态：0-草稿，1-待审核，2-审核通过，-2-审核拒绝，-4-已删除
	IsLiked bool               `json:"isLiked"` // 是否点赞
	Author  typings.BaseAuthor `json:"author"`  // 作者
}
type PostListResponse struct {
	typings.BaseListResponse
	List []PostInfo `json:"list"`
}
type PostResponse struct {
	typings.BaseDetailResponse
	Data PostInfo `json:"data"`
}

type PostCommentInfo struct {
	UserID       string             `json:"userId"`       // 用户ID
	Content      string             `json:"content"`      // 评论内容
	Images       []string           `json:"images"`       // 图片
	Video        string             `json:"video"`        // 视频
	UserNickname string             `json:"userNickname"` // 用户昵称
	UserAvatar   string             `json:"userAvatar"`   // 用户头像
	ParentID     string             `json:"parentId"`     // 父级ID
	EntityID     string             `json:"entityId"`     // 关联实体ID
	EntityType   int8               `json:"entityType"`   // 关联实体类型
	RelationID   string             `json:"relationId"`   // 关联ID
	Heat         int64              `json:"heat"`         // 热度
	LikeCount    int64              `json:"likeCount"`    // 点赞数
	ReplyCount   int64              `json:"replyCount"`   // 回复数
	CreatedAt    string             `json:"createdAt"`    // 创建时间
	UpdatedAt    string             `json:"updatedAt"`    // 更新时间
	Status       int8               `json:"status"`       // 状态：0-禁用，1-正常
	IsLiked      bool               `json:"isLiked"`      // 是否点赞
	Author       typings.BaseAuthor `json:"author"`       // 作者
}
type PostVideoInfo struct {
	ID          string             `json:"id"`          // 帖子ID
	Title       string             `json:"title"`       // 视频标题
	Description string             `json:"description"` // 视频描述
	Cover       string             `json:"cover"`       // 封面URL
	Duration    int                `json:"duration"`    // 视频时长(秒)
	Resolution  string             `json:"resolution"`  // 分辨率
	Quality     string             `json:"quality"`     // 画质
	Size        int64              `json:"size"`        // 文件大小(字节)
	ViewCount   int64              `json:"viewCount"`   // 观看次数
	LikeCount   int64              `json:"likeCount"`   // 点赞数
	Heat        int64              `json:"heat"`        // 热度
	CreatedAt   string             `json:"createdAt"`   // 创建时间
	IsLiked     bool               `json:"isLiked"`     // 是否点赞
	Author      typings.BaseAuthor `json:"author"`      // 作者
}

// type PostListResponse struct {
// 	typings.BaseListResponse
// 	List []PostInfo `json:"list"`
// }

type PostCommentListResponse struct {
	typings.BaseListResponse
	List []PostCommentInfo `json:"list"`
}

type PostVideoListResponse struct {
	typings.BaseListResponse
	List []PostVideoInfo `json:"list"`
}
