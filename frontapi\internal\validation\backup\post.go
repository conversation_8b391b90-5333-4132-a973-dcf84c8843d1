package validation

// PostCreateRequest 帖子创建请求验证模型
type PostCreateRequest struct {
	Title       string   `json:"title" validate:"required|minLen:2|maxLen:100"`
	Content     string   `json:"content" validate:"required|minLen:10"`
	CategoryID  uint     `json:"categoryId" validate:"required|min:1"`
	Tags        []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
	Images      []string `json:"images" validate:"each:url"`
	IsTop       bool     `json:"isTop"`
	IsEssence   bool     `json:"isEssence"`
	IsAnonymous bool     `json:"isAnonymous"`
}

// PostUpdateRequest 帖子更新请求验证模型
type PostUpdateRequest struct {
	Title      string   `json:"title" validate:"minLen:2|maxLen:100"`
	Content    string   `json:"content" validate:"minLen:10"`
	CategoryID uint     `json:"categoryId" validate:"min:1"`
	Tags       []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
	Images     []string `json:"images" validate:"each:url"`
	Status     int      `json:"status" validate:"in:0,1,2"` // 0:正常 1:待审核 2:已删除
}

// PostListRequest 帖子列表请求验证模型
type PostListRequest struct {
	Page       int      `json:"page" validate:"min:1"`
	PageSize   int      `json:"pageSize" validate:"min:1|max:100"`
	CategoryID uint     `json:"categoryId" validate:"min:1"`
	UserID     uint     `json:"userId" validate:"min:1"`
	Tags       []string `json:"tags"`
	SortBy     string   `json:"sortBy" validate:"in:latest,popular,comment,hot"`
	IsTop      bool     `json:"isTop"`
	IsEssence  bool     `json:"isEssence"`
	Keyword    string   `json:"keyword" validate:"maxLen:50"`
}

// PostCommentRequest 帖子评论请求验证模型
type PostCommentRequest struct {
	PostID      uint     `json:"postId" validate:"required|min:1"`
	Content     string   `json:"content" validate:"required|minLen:1|maxLen:500"`
	Images      []string `json:"images" validate:"each:url"`
	ParentID    uint     `json:"parentId"` // 父评论ID，0表示顶级评论
	IsAnonymous bool     `json:"isAnonymous"`
}

// PostCategoryCreateRequest 帖子分类创建请求验证模型
type PostCategoryCreateRequest struct {
	Name        string `json:"name" validate:"required|minLen:2|maxLen:50"`
	Description string `json:"description" validate:"required|minLen:5|maxLen:200"`
	Icon        string `json:"icon" validate:"url"`
	SortOrder   int    `json:"sortOrder" validate:"min:0"`
	ParentID    uint   `json:"parentId" validate:"min:0"`
}

// PostCategoryUpdateRequest 帖子分类更新请求验证模型
type PostCategoryUpdateRequest struct {
	Name        string `json:"name" validate:"minLen:2|maxLen:50"`
	Description string `json:"description" validate:"minLen:5|maxLen:200"`
	Icon        string `json:"icon" validate:"url"`
	SortOrder   int    `json:"sortOrder" validate:"min:0"`
	Status      int    `json:"status" validate:"in:0,1"` // 0:正常 1:禁用
}
