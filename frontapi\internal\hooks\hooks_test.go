package hooks

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"frontapi/internal/hooks/common"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockEntity 测试用的模拟实体
type MockEntity struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name"`
	Email       string    `json:"email"`
	Description string    `json:"description"`
	Status      int       `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// MockDB 创建测试用的内存数据库
func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// 创建测试表
	db.AutoMigrate(&MockEntity{})

	// 创建审计日志表
	db.Exec(`CREATE TABLE audit_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id TEXT,
		action TEXT,
		table_name TEXT,
		data TEXT,
		created_at DATETIME
	)`)

	return db
}

// TestLayeredHookManager 测试分层钩子管理器
func TestLayeredHookManager(t *testing.T) {
	manager := NewLayeredHookManager()

	// 测试注册钩子
	hook := Hook{
		Name:        "test_hook",
		Description: "测试钩子",
		Priority:    10,
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			return nil
		},
	}

	manager.RegisterHook("test_entity", BeforeCreate, hook)

	// 测试获取钩子
	hooks := manager.GetHooks("test_entity", BeforeCreate)
	assert.Len(t, hooks, 1)
	assert.Equal(t, "test_hook", hooks[0].Name)

	// 测试执行钩子
	err := manager.ExecuteHooks(context.Background(), "test_entity", BeforeCreate, &MockEntity{})
	assert.NoError(t, err)
}

// TestDuplicateCheckHook 测试重复检查钩子
func TestDuplicateCheckHook(t *testing.T) {
	db := setupTestDB()

	// 先插入一条记录
	entity1 := &MockEntity{
		ID:    "1",
		Name:  "test",
		Email: "<EMAIL>",
	}
	db.Create(entity1)

	// 测试重复检查钩子
	hook := &common.DuplicateCheckHook{
		DB:        db,
		TableName: "mock_entities",
		Fields:    []string{"Name"},
		Message:   "名称已存在",
	}

	// 测试重复数据
	entity2 := &MockEntity{
		ID:    "2",
		Name:  "test", // 重复的名称
		Email: "<EMAIL>",
	}

	err := hook.Execute(context.Background(), entity2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "名称已存在")

	// 测试非重复数据
	entity3 := &MockEntity{
		ID:    "3",
		Name:  "test3", // 不重复的名称
		Email: "<EMAIL>",
	}

	err = hook.Execute(context.Background(), entity3)
	assert.NoError(t, err)
}

// TestDataCleaningHook 测试数据清洗钩子
func TestDataCleaningHook(t *testing.T) {
	hook := &common.DataCleaningHook{
		TrimFields:  []string{"Name", "Description"},
		LowerFields: []string{"Email"},
		DefaultValues: map[string]interface{}{
			"Status": 1,
		},
	}

	entity := &MockEntity{
		Name:        "  test name  ",    // 包含空格
		Email:       "<EMAIL>", // 大写
		Description: "  test description  ",
		Status:      0, // 零值，应该被设置为默认值
	}

	err := hook.Execute(context.Background(), entity)
	assert.NoError(t, err)

	// 验证数据清洗结果
	assert.Equal(t, "test name", entity.Name)               // 空格被去除
	assert.Equal(t, "<EMAIL>", entity.Email)       // 转换为小写
	assert.Equal(t, "test description", entity.Description) // 空格被去除
	assert.Equal(t, 1, entity.Status)                       // 设置了默认值
}

// TestValidationHook 测试数据验证钩子
func TestValidationHook(t *testing.T) {
	hook := &common.ValidationHook{
		Rules: map[string][]common.ValidationRule{
			"Name": {
				{Type: "required", Message: "名称不能为空"},
				{Type: "min_length", Value: 2, Message: "名称至少2个字符"},
				{Type: "max_length", Value: 10, Message: "名称不能超过10个字符"},
			},
			"Email": {
				{Type: "regex", Value: `^[^@]+@[^@]+\.[^@]+$`, Message: "邮箱格式不正确"},
			},
		},
	}

	// 测试有效数据
	validEntity := &MockEntity{
		Name:  "test",
		Email: "<EMAIL>",
	}
	err := hook.Execute(context.Background(), validEntity)
	assert.NoError(t, err)

	// 测试无效数据 - 名称为空
	invalidEntity1 := &MockEntity{
		Name:  "",
		Email: "<EMAIL>",
	}
	err = hook.Execute(context.Background(), invalidEntity1)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "名称不能为空")

	// 测试无效数据 - 名称太短
	invalidEntity2 := &MockEntity{
		Name:  "a",
		Email: "<EMAIL>",
	}
	err = hook.Execute(context.Background(), invalidEntity2)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "名称至少2个字符")

	// 测试无效数据 - 邮箱格式错误
	invalidEntity3 := &MockEntity{
		Name:  "test",
		Email: "invalid-email",
	}
	err = hook.Execute(context.Background(), invalidEntity3)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "邮箱格式不正确")
}

// TestServiceHookManager 测试服务钩子管理器
func TestServiceHookManager(t *testing.T) {
	db := setupTestDB()
	hookManager := NewServiceHookManager(db, "test_entity")

	// 注册钩子
	hookManager.RegisterDataCleaning(
		[]string{"Name"},
		[]string{"Email"},
		[]string{},
		map[string]interface{}{"Status": 1},
	)

	hookManager.RegisterValidation(map[string][]common.ValidationRule{
		"Name": {
			{Type: "required", Message: "名称不能为空"},
		},
	})

	// 测试执行钩子
	entity := &MockEntity{
		Name:  "  test  ",
		Email: "<EMAIL>",
	}

	err := hookManager.ExecuteHooks(context.Background(), BeforeCreate, entity)
	assert.NoError(t, err)

	// 验证数据清洗效果
	assert.Equal(t, "test", entity.Name)
	assert.Equal(t, "<EMAIL>", entity.Email)
	assert.Equal(t, 1, entity.Status)
}

// TestHookBuilder 测试钩子构建器
func TestHookBuilder(t *testing.T) {
	db := setupTestDB()
	hookManager := NewServiceHookManager(db, "test_entity")

	// 使用构建器设置钩子
	builder := NewCreateHookBuilder(hookManager)
	builder.WithDataCleaning(
		[]string{"Name"},
		[]string{"Email"},
		[]string{},
		map[string]interface{}{"Status": 1},
	).WithValidation(map[string][]common.ValidationRule{
		"Name": {
			{Type: "required", Message: "名称不能为空"},
		},
	}).WithCustomHook(
		BeforeCreate,
		"test_custom_hook",
		"测试自定义钩子",
		50,
		func(ctx context.Context, data interface{}) error {
			if entity, ok := data.(*MockEntity); ok {
				entity.Description = "processed by custom hook"
			}
			return nil
		},
	).Build()

	// 测试执行钩子
	entity := &MockEntity{
		Name:  "  test  ",
		Email: "<EMAIL>",
	}

	err := hookManager.ExecuteHooks(context.Background(), BeforeCreate, entity)
	assert.NoError(t, err)

	// 验证所有钩子都执行了
	assert.Equal(t, "test", entity.Name)
	assert.Equal(t, "<EMAIL>", entity.Email)
	assert.Equal(t, 1, entity.Status)
	assert.Equal(t, "processed by custom hook", entity.Description)
}

// TestHookPriority 测试钩子优先级
func TestHookPriority(t *testing.T) {
	manager := NewLayeredHookManager()

	var executionOrder []string

	// 注册不同优先级的钩子
	hook1 := Hook{
		Name:     "hook1",
		Priority: 20,
		Enabled:  true,
		Func: func(ctx context.Context, data interface{}) error {
			executionOrder = append(executionOrder, "hook1")
			return nil
		},
	}

	hook2 := Hook{
		Name:     "hook2",
		Priority: 10, // 更高优先级
		Enabled:  true,
		Func: func(ctx context.Context, data interface{}) error {
			executionOrder = append(executionOrder, "hook2")
			return nil
		},
	}

	hook3 := Hook{
		Name:     "hook3",
		Priority: 30,
		Enabled:  true,
		Func: func(ctx context.Context, data interface{}) error {
			executionOrder = append(executionOrder, "hook3")
			return nil
		},
	}

	manager.RegisterHook("test", BeforeCreate, hook1)
	manager.RegisterHook("test", BeforeCreate, hook2)
	manager.RegisterHook("test", BeforeCreate, hook3)

	// 执行钩子
	err := manager.ExecuteHooks(context.Background(), "test", BeforeCreate, &MockEntity{})
	assert.NoError(t, err)

	// 验证执行顺序（按优先级从小到大）
	assert.Equal(t, []string{"hook2", "hook1", "hook3"}, executionOrder)
}

// TestHookError 测试钩子错误处理
func TestHookError(t *testing.T) {
	manager := NewLayeredHookManager()

	// 注册一个会失败的钩子
	errorHook := Hook{
		Name:     "error_hook",
		Priority: 10,
		Enabled:  true,
		Func: func(ctx context.Context, data interface{}) error {
			return errors.New("hook execution failed")
		},
	}

	manager.RegisterHook("test", BeforeCreate, errorHook)

	// 执行钩子应该返回错误
	err := manager.ExecuteHooks(context.Background(), "test", BeforeCreate, &MockEntity{})
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "hook execution failed")
}

// TestHookDisableEnable 测试钩子禁用和启用
func TestHookDisableEnable(t *testing.T) {
	manager := NewLayeredHookManager()

	var executed bool
	hook := Hook{
		Name:     "test_hook",
		Priority: 10,
		Enabled:  true,
		Func: func(ctx context.Context, data interface{}) error {
			executed = true
			return nil
		},
	}

	manager.RegisterHook("test", BeforeCreate, hook)

	// 执行钩子
	err := manager.ExecuteHooks(context.Background(), "test", BeforeCreate, &MockEntity{})
	assert.NoError(t, err)
	assert.True(t, executed)

	// 禁用钩子
	executed = false
	manager.DisableHook("test", BeforeCreate, "test_hook")

	// 再次执行钩子
	err = manager.ExecuteHooks(context.Background(), "test", BeforeCreate, &MockEntity{})
	assert.NoError(t, err)
	assert.False(t, executed) // 钩子被禁用，不应该执行

	// 重新启用钩子
	manager.EnableHook("test", BeforeCreate, "test_hook")

	// 再次执行钩子
	err = manager.ExecuteHooks(context.Background(), "test", BeforeCreate, &MockEntity{})
	assert.NoError(t, err)
	assert.True(t, executed) // 钩子被重新启用，应该执行
}

// BenchmarkHookExecution 钩子执行性能测试
func BenchmarkHookExecution(b *testing.B) {
	manager := NewLayeredHookManager()

	// 注册多个钩子
	for i := 0; i < 10; i++ {
		hook := Hook{
			Name:     fmt.Sprintf("hook_%d", i),
			Priority: i * 10,
			Enabled:  true,
			Func: func(ctx context.Context, data interface{}) error {
				// 模拟一些处理
				return nil
			},
		}
		manager.RegisterHook("test", BeforeCreate, hook)
	}

	entity := &MockEntity{Name: "test"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		manager.ExecuteHooks(context.Background(), "test", BeforeCreate, entity)
	}
}
