package middleware

import (
	"fmt"
	"frontapi/pkg/auth"
	"frontapi/pkg/redis"
	"frontapi/pkg/utils"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
)

var jwtSecret []byte

// SetJWTSecret 是为了向后兼容，现在使用auth.SetJWTSecret替代
func SetJWTSecret(secret []byte) {
	auth.SetJWTSecret(string(secret))
}

// AuthMiddleware JWT认证中间件
func AuthRequired() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 添加调试信息
		fmt.Printf("AuthRequired Debug - Request Headers: %+v\n", c.GetReqHeaders())
		fmt.Printf("AuthRequired Debug - Authorization header: '%s'\n", c.Get("Authorization"))
		fmt.Printf("AuthRequired Debug - Request URL: %s\n", c.OriginalURL())
		fmt.Printf("AuthRequired Debug - Request Method: %s\n", c.Method())

		// 尝试从多个位置获取token
		var tokenString string

		// 1. 从Authorization头获取
		authHeader := c.Get("Authorization")
		if authHeader != "" {
			parts := strings.Split(authHeader, " ")
			if len(parts) == 2 && parts[0] == "Bearer" {
				tokenString = parts[1]
			}
		}

		// 2. 从请求头的token字段获取
		if tokenString == "" {
			tokenString = c.Get("token")
		}

		// 3. 从查询参数获取
		if tokenString == "" {
			tokenString = c.Query("token")
		}

		fmt.Printf("AuthRequired Debug - Final tokenString: '%s'\n", tokenString)

		// 检查是否获取到token
		if tokenString == "" {
			return utils.Error(c, fiber.StatusUnauthorized, "未提供认证信息")
		}

		// 解析JWT令牌
		claims, err := auth.ParseToken(tokenString)
		fmt.Println("令牌：", claims)
		if err != nil {
			return utils.Error(c, fiber.StatusUnauthorized, "无效的令牌: "+err.Error())
		}

		// 设置用户信息到上下文中
		c.Locals("userId", claims.UserID)
		c.Locals("user_id", claims.UserID)
		c.Locals("username", claims.Username)

		return c.Next()
	}
}

// Auth 返回认证中间件
func Auth() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 添加调试信息
		fmt.Printf("Debug - Request Headers: %+v\n", c.GetReqHeaders())
		fmt.Printf("Debug - Authorization header: '%s'\n", c.Get("Authorization"))
		fmt.Printf("Debug - Request URL: %s\n", c.OriginalURL())
		fmt.Printf("Debug - Request Method: %s\n", c.Method())

		// 尝试从多个位置获取token
		var tokenString string

		// 1. 从Authorization头获取
		authHeader := c.Get("Authorization")
		if authHeader != "" {
			parts := strings.Split(authHeader, " ")
			if len(parts) == 2 && parts[0] == "Bearer" {
				tokenString = parts[1]
			}
		}

		// 2. 从请求头的token字段获取
		if tokenString == "" {
			tokenString = c.Get("token")
		}

		// 3. 从查询参数获取
		if tokenString == "" {
			tokenString = c.Query("token")
		}

		fmt.Printf("Debug - Final tokenString: '%s'\n", tokenString)

		// 检查是否获取到token
		if tokenString == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    fiber.StatusUnauthorized,
				"message": "未提供认证信息",
				"data":    nil,
			})
		}

		// 解析JWT令牌
		claims, err := auth.ParseToken(tokenString)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    fiber.StatusUnauthorized,
				"message": "无效的令牌: " + err.Error(),
				"data":    nil,
			})
		}

		// 设置用户信息到上下文
		c.Locals("userId", claims.UserID)
		c.Locals("username", claims.Username)

		return c.Next()
	}
}

// OptionalAuth 返回可选的认证中间件，不阻止未认证请求 // OptionalAuthMiddleware 可选的认证中间件，不阻止未认证请求
func OptionalAuth() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 尝试从多个位置获取token
		var tokenString string

		// 1. 从Authorization头获取
		authHeader := c.Get("Authorization")
		if authHeader != "" {
			parts := strings.Split(authHeader, " ")
			if len(parts) == 2 && parts[0] == "Bearer" {
				tokenString = parts[1]
			}
		}

		// 2. 从请求头的token字段获取
		if tokenString == "" {
			tokenString = c.Get("token")
		}

		// 3. 从查询参数获取
		if tokenString == "" {
			tokenString = c.Query("token")
		}

		// 如果没有提供token，允许请求继续
		if tokenString == "" {
			return c.Next()
		}

		// 解析JWT令牌
		claims, err := auth.ParseToken(tokenString)
		if err != nil {
			// 令牌无效，但仍然允许请求继续
			return c.Next()
		}

		// 解析成功，设置用户信息到上下文中
		c.Locals("userId", claims.UserID)
		c.Locals("user_id", claims.UserID)
		c.Locals("username", claims.Username)

		return c.Next()
	}
}

func RequireRole(role string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 从上下文中获取用户角色
		userRole := c.Locals("role")
		// 检查用户角色是否匹配
		if userRole != role {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code":    fiber.StatusForbidden,
				"message": "权限不足",
				"data":    nil,
			})
		}
		return c.Next()
	}
}

func AdminRequired() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 从上下文中获取用户角色
		userRole := c.Locals("role")
		// 检查用户角色是否匹配
		if userRole != "admin" {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code":    fiber.StatusForbidden,
				"message": "权限不足",
				"data":    nil,
			})
		}
		return c.Next()
	}
}

// LenientAuth 宽松的认证中间件，支持多种token传递方式
func LenientAuth() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 添加调试信息
		fmt.Printf("LenientAuth Debug - Request Headers: %+v\n", c.GetReqHeaders())

		var tokenString string

		// 1. 从Authorization头获取 (Bearer token)
		authHeader := c.Get("Authorization")
		if authHeader != "" {
			if strings.HasPrefix(authHeader, "Bearer ") {
				tokenString = strings.TrimPrefix(authHeader, "Bearer ")
			} else {
				// 也支持没有Bearer前缀的token
				tokenString = authHeader
			}
		}

		// 2. 从X-Admin-Token头获取
		if tokenString == "" {
			tokenString = c.Get("X-Admin-Token")
		}

		// 3. 从token头获取
		if tokenString == "" {
			tokenString = c.Get("token")
		}

		// 4. 从查询参数获取
		if tokenString == "" {
			tokenString = c.Query("token")
		}

		// 5. 从access_token查询参数获取
		if tokenString == "" {
			tokenString = c.Query("access_token")
		}

		fmt.Printf("LenientAuth Debug - Final tokenString: '%s'\n", tokenString)

		// 检查是否获取到token
		if tokenString == "" {
			return utils.Error(c, fiber.StatusUnauthorized, "未提供认证信息")
		}

		// 解析JWT token
		claims, err := auth.ParseToken(tokenString)
		if err != nil {
			fmt.Printf("LenientAuth Debug - Parse token error: %v\n", err)
			return utils.Error(c, fiber.StatusUnauthorized, "认证令牌无效")
		}

		// 从claims中获取用户ID
		userIDInt, err := strconv.Atoi(claims.UserID)
		if err != nil {
			fmt.Printf("LenientAuth Debug - UserID conversion error: %v\n", err)
			return utils.Error(c, fiber.StatusUnauthorized, "用户ID格式错误")
		}

		// 验证token是否在Redis中存在且匹配
		valid, err := redis.ValidateAdminToken(userIDInt, tokenString)
		if err != nil {
			fmt.Printf("LenientAuth Debug - Redis token validation error: %v\n", err)
			return utils.Error(c, fiber.StatusUnauthorized, "认证令牌验证失败")
		}

		if !valid {
			fmt.Printf("LenientAuth Debug - Token validation failed for user %d\n", userIDInt)
			return utils.Error(c, fiber.StatusUnauthorized, "认证令牌已失效")
		}

		// 从Redis获取用户信息
		userInfo, err := redis.GetAdminUserInfo(userIDInt)
		if err != nil {
			fmt.Printf("LenientAuth Debug - Get user info error: %v\n", err)
			return utils.Error(c, fiber.StatusUnauthorized, "用户信息获取失败")
		}

		// 设置用户信息到上下文
		c.Locals("userId", strconv.Itoa(userInfo.UserID))
		c.Locals("user_id", strconv.Itoa(userInfo.UserID))
		c.Locals("username", userInfo.Username)
		c.Locals("nickname", userInfo.Nickname)
		c.Locals("avatar", userInfo.Avatar)
		c.Locals("roles", userInfo.Roles)
		c.Locals("permissions", userInfo.Permissions)

		fmt.Printf("LenientAuth Debug - User authenticated successfully: ID=%d, Username=%s\n",
			userInfo.UserID, userInfo.Username)

		return c.Next()
	}
}
