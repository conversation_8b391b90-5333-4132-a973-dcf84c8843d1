# 工具函数文档

本文档详细介绍了 `src/shared/utils` 目录下所有可用的工具函数及其使用方法。

## 目录结构

```
src/shared/utils/
├── index.ts              # 统一导出入口
├── format.ts             # 格式化工具
├── common.ts             # 通用工具函数
├── date.ts               # 日期时间工具
├── string.ts             # 字符串工具
├── number.ts             # 数字工具
├── validate.ts           # 验证工具
├── array.ts              # 数组工具
├── object.ts             # 对象工具
├── auth.ts               # 认证工具
├── avatar.ts             # 头像工具
├── animation.ts          # 动画工具
├── chart.ts              # 图表工具
├── convert.ts            # 转换工具
├── crypto.ts             # 加密工具
├── device.ts             # 设备检测
├── dom.ts                # DOM 工具
├── event.ts              # 事件工具
├── file.ts               # 文件工具
├── form.ts               # 表单工具
├── http.ts               # HTTP 工具
├── math.ts               # 数学工具
├── request.ts            # 请求工具
├── storage.ts            # 存储工具
├── url.ts                # URL 工具
├── websocket.ts          # WebSocket 工具
├── worker.ts             # Worker 工具
├── usage-examples.md     # 使用示例
├── ANALYSIS_REPORT.md    # 分析报告
└── UTILS_DOCUMENTATION.md # 本文档
```

## 核心工具函数

### 1. 格式化工具 (format.ts)

#### formatNumber(num, options?)
格式化数字显示
```typescript
formatNumber(1234) // "1.2K"
formatNumber(12345) // "1.2万"
formatNumber(123456789) // "1.2亿"
```

#### formatDuration(seconds)
格式化时长显示
```typescript
formatDuration(65) // "1:05"
formatDuration(3665) // "1:01:05"
```

#### formatFileSize(bytes)
格式化文件大小
```typescript
formatFileSize(1024) // "1 KB"
formatFileSize(1048576) // "1 MB"
```

#### formatTimeAgo(date)
格式化相对时间
```typescript
formatTimeAgo(new Date()) // "刚刚"
formatTimeAgo('2023-01-01') // "1年前"
```

### 2. 通用工具函数 (common.ts)

#### debounce(func, wait, options?)
防抖函数
```typescript
const debouncedFn = debounce(() => console.log('执行'), 300)
```

#### throttle(func, wait, options?)
节流函数
```typescript
const throttledFn = throttle(() => console.log('执行'), 300)
```

#### deepClone(obj)
深拷贝对象
```typescript
const cloned = deepClone({ a: { b: 1 } })
```

#### generateId(length?)
生成随机ID
```typescript
generateId() // "aBc12DeF"
generateId(12) // "aBc12DeFgHiJ"
```

#### generateUUID()
生成UUID
```typescript
generateUUID() // "550e8400-e29b-41d4-a716-************"
```

### 3. 验证工具 (validate.ts)

#### BaseValidator
基础验证器
```typescript
BaseValidator.isEmail('<EMAIL>') // true
BaseValidator.isPhone('13800138000') // true
BaseValidator.isIdCard('110101199003077777') // true
```

#### Validator
高级验证器
```typescript
const validator = new Validator()
validator.required('必填字段').email('邮箱格式').validate('<EMAIL>')
```

### 4. 字符串工具 (string.ts)

#### StringUtils
字符串工具类
```typescript
StringUtils.isEmpty('') // true
StringUtils.capitalize('hello') // "Hello"
StringUtils.toCamelCase('hello-world') // "helloWorld"
```

### 5. 数组工具 (array.ts)

#### ArrayUtils
数组工具类
```typescript
ArrayUtils.unique([1, 2, 2, 3]) // [1, 2, 3]
ArrayUtils.flatten([[1, 2], [3, 4]]) // [1, 2, 3, 4]
```

### 6. 对象工具 (object.ts)

#### ObjectUtils
对象工具类
```typescript
ObjectUtils.isEmpty({}) // true
ObjectUtils.pick({ a: 1, b: 2 }, ['a']) // { a: 1 }
```

## 使用方式

### 1. 统一导入
```typescript
import { formatNumber, debounce, StringUtils } from '@/shared/utils'
```

### 2. 按需导入
```typescript
import { formatNumber } from '@/shared/utils/format'
import { debounce } from '@/shared/utils/common'
```

### 3. 全量导入
```typescript
import * as Utils from '@/shared/utils'
Utils.formatNumber(1234)
```

## 最佳实践

### 1. 命名规范
- 函数名使用驼峰命名法
- 工具类使用 PascalCase
- 常量使用 UPPER_SNAKE_CASE

### 2. 类型安全
- 所有函数都提供完整的 TypeScript 类型定义
- 使用泛型确保类型推导正确
- 提供详细的 JSDoc 注释

### 3. 性能优化
- 防抖和节流函数避免频繁调用
- 使用记忆化缓存计算结果
- 避免不必要的深拷贝操作

### 4. 错误处理
- 提供合理的默认值
- 对异常输入进行校验
- 返回有意义的错误信息

## 版本信息

当前版本：v2.0.0

### 更新日志

#### v2.0.0 (2024-01-XX)
- 重构整个工具函数库
- 移除重复函数定义
- 统一代码风格和命名规范
- 完善类型定义和文档
- 优化性能和错误处理

#### v1.x.x
- 初始版本，包含基础工具函数

## 贡献指南

### 添加新工具函数
1. 在对应的分类文件中添加函数
2. 更新 `index.ts` 导出
3. 添加完整的类型定义
4. 编写单元测试
5. 更新文档

### 代码规范
- 遵循 ESLint 和 Prettier 配置
- 提供完整的 JSDoc 注释
- 确保 100% 的类型覆盖
- 编写充分的单元测试

## 常见问题

### Q: 如何选择合适的工具函数？
A: 根据功能分类选择，优先使用经过优化的工具函数而不是自己实现。

### Q: 工具函数的性能如何？
A: 所有工具函数都经过性能优化，包括防抖节流、记忆化缓存等技术。

### Q: 如何处理兼容性问题？
A: 工具函数支持现代浏览器，对于旧版本浏览器需要相应的 polyfill。

### Q: 可以修改工具函数吗？
A: 建议通过配置参数自定义行为，避免直接修改源码。如需新功能，请提交 PR。

## 相关链接

- [使用示例](./usage-examples.md)
- [分析报告](./ANALYSIS_REPORT.md)
- [项目文档](../../../README.md)