package comics

import "frontapi/internal/typings"

type ComicCategoryListResponse struct {
	typings.BaseListResponse
	List []ComicCategoryInfo `json:"list"`
}

type ComicCategoryInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	SortOrder   int    `json:"sortOrder"`
	Status      int    `json:"status"`
}

type ComicListResponse struct {
	typings.BaseListResponse
	List []ComicInfo `json:"list"`
}

type ComicInfo struct {
	ID            string  `json:"id"`
	Title         string  `json:"title"`
	Cover         string  `json:"cover"`
	Rating        float64 `json:"rating"`
	Description   string  `json:"description"`
	Author        string  `json:"author"`
	CategoryID    string  `json:"categoryId"`
	CategoryName  string  `json:"categoryName"`
	Progress      string  `json:"progress"`
	Popularity    int64   `json:"popularity"`
	ChapterCount  int     `json:"chapterCount"`
	ReadCount     int64   `json:"readCount"`
	LikeCount     int64   `json:"likeCount"`
	Score         float64 `json:"score"`
	FavoriteCount int64   `json:"favoriteCount"`
	ShareCount    int64   `json:"shareCount"`
	CommentCount  int64   `json:"commentCount"`
	IsPaid        bool    `json:"isPaid"`
	IsFeatured    bool    `json:"isFeatured"`
	Tags          string  `json:"tags"`
	Price         float64 `json:"price"`
	Status        int     `json:"status"`
}
type ComicDetail struct {
	ComicInfo
	Chapters []ComicChapterInfo `json:"chapters"`
}

// ComicChapterListResponse 漫画章节列表响应
type ComicChapterListResponse struct {
	typings.BaseListResponse
	List []ComicChapterInfo `json:"list"`
}

type ComicChapterInfo struct {
	ID            string  `json:"id"`
	ComicID       string  `json:"comicId"`
	Title         string  `json:"title"`
	ChapterNumber int     `json:"chapterNumber"`
	ReadCount     int64   `json:"readCount"`
	IsLocked      bool    `json:"isLocked"`
	Price         float64 `json:"price"`
	PageCount     int     `json:"pageCount"`
	Status        int     `json:"status"`
}
