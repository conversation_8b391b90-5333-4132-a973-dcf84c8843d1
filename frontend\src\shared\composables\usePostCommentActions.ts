import {
    createComment,
    deleteComment,
    getComments,
    replyComment,
    toggleCommentLike
} from '@/api/common/postCommentActions';
import { ElMessage } from 'element-plus';
import { ref, type Ref } from 'vue';

/**
 * 评论类型简化定义
 */
interface Comment {
    id: string;
    content?: string;
    isLiked?: boolean;
    likeCount?: number;
}

/**
 * 帖子评论操作组合函数
 * 提供帖子评论相关的操作逻辑封装
 */
export function usePostCommentActions() {
    const loading = ref(false)
    const submitting = ref(false)

    /**
     * 获取评论列表
     * @param postId 帖子ID
     * @param page 页码
     * @param pageSize 每页数量
     */
    const fetchComments = async (postId: string, page: number = 1, pageSize: number = 20) => {
        loading.value = true
        try {
            const response = await getComments({
                data: { postId },
                page: { pageNo: page, pageSize }
            })
            return response
        } catch (error) {
            console.error('获取评论失败:', error)
            // 不显示弹窗，让页面自然显示加载失败状态
            throw error
        } finally {
            loading.value = false
        }
    }

    /**
     * 创建评论
     * @param postId 帖子ID
     * @param content 评论内容
     * @param parentId 父评论ID（可选）
     * @param successCallback 成功回调
     * @param errorCallback 错误回调
     */
    const addComment = async (
        postId: string,
        content: string,
        parentId?: string,
        successCallback?: (comment: any) => void,
        errorCallback?: (error: any) => void
    ) => {
        if (submitting.value) return

        submitting.value = true
        try {
            const response = await createComment({
                data: { postId, content, parentId }
            })

            ElMessage.success(parentId ? '回复成功' : '评论成功')
            successCallback?.(response)
            return response
        } catch (error) {
            console.error('评论失败:', error)
            ElMessage.error('评论失败')
            errorCallback?.(error)
            throw error
        } finally {
            submitting.value = false
        }
    }

    /**
     * 删除评论
     * @param commentId 评论ID
     * @param successCallback 成功回调
     * @param errorCallback 错误回调
     */
    const removeComment = async (
        commentId: string,
        successCallback?: () => void,
        errorCallback?: (error: any) => void
    ) => {
        try {
            await deleteComment({ data: { commentId } })
            ElMessage.success('删除成功')
            successCallback?.()
        } catch (error) {
            console.error('删除评论失败:', error)
            ElMessage.error('删除失败')
            errorCallback?.(error)
            throw error
        }
    }

    /**
     * 点赞/取消点赞评论
     * @param commentId 评论ID
     * @param commentList 评论列表（可选，用于更新本地状态）
     * @param successCallback 成功回调
     * @param errorCallback 错误回调
     */
    const likeComment = async (
        commentId: string,
        commentList?: Comment[] | Ref<Comment[]>,
        successCallback?: () => void,
        errorCallback?: (error: any) => void
    ) => {
        try {
            await toggleCommentLike({ data: { commentId } })

            // 更新本地评论状态
            if (commentList) {
                const comments = Array.isArray(commentList) ? commentList : commentList.value
                const comment = comments.find((c: Comment) => c.id === commentId)
                if (comment) {
                    comment.isLiked = !comment.isLiked
                    comment.likeCount = comment.isLiked
                        ? (comment.likeCount || 0) + 1
                        : Math.max((comment.likeCount || 0) - 1, 0)
                }
            }

            successCallback?.()
        } catch (error) {
            console.error('点赞评论失败:', error)
            ElMessage.error('操作失败')
            errorCallback?.(error)
            throw error
        }
    }

    /**
     * 回复评论
     * @param parentId 父评论ID
     * @param content 回复内容
     * @param postId 帖子ID
     * @param successCallback 成功回调
     * @param errorCallback 错误回调
     */
    const replyToComment = async (
        parentId: string,
        content: string,
        postId: string,
        successCallback?: (reply: any) => void,
        errorCallback?: (error: any) => void
    ) => {
        if (submitting.value) return

        submitting.value = true
        try {
            const response = await replyComment({
                data: { parentId, content, postId }
            })

            ElMessage.success('回复成功')
            successCallback?.(response)
            return response
        } catch (error) {
            console.error('回复失败:', error)
            ElMessage.error('回复失败')
            errorCallback?.(error)
            throw error
        } finally {
            submitting.value = false
        }
    }

    return {
        loading,
        submitting,
        fetchComments,
        addComment,
        removeComment,
        likeComment,
        replyToComment
    }
}