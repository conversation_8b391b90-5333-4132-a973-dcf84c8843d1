package videos

import (
	"context"
	"frontapi/internal/models/videos"
	userRepo "frontapi/internal/repository/users"
	repo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

type VideoAlbumService interface {
	base.IExtendedService[videos.VideoAlbum]
	CheckCollection(ctx context.Context, userID string, albumID string) (bool, error)
}

type videoAlbumService struct {
	*base.ExtendedService[videos.VideoAlbum]
	videoAlbumRepo           repo.VideosAlbumsRepository
	videoRepo                repo.VideoRepository
	videoAlbumCollectionRepo userRepo.UserVideoAlbumCollectionRepository
}

func NewVideoAlbumService(videoAlbumRepo repo.VideosAlbumsRepository,
	videoRepo repo.VideoRepository,
	userVideoAlbumCollectionRepo userRepo.UserVideoAlbumCollectionRepository) VideoAlbumService {
	return &videoAlbumService{
		ExtendedService:          base.NewExtendedService[videos.VideoAlbum](videoAlbumRepo, "video_album"),
		videoAlbumRepo:           videoAlbumRepo,
		videoRepo:                videoRepo,
		videoAlbumCollectionRepo: userVideoAlbumCollectionRepo,
	}
}

func (s *videoAlbumService) CheckCollection(ctx context.Context, userID string, albumID string) (bool, error) {
	return s.videoAlbumCollectionRepo.Exists(ctx, map[string]interface{}{
		"user_id":  userID,
		"album_id": albumID,
	})
}
