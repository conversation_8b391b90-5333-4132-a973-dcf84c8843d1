<template>
    <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item label="专辑标题">
                <el-input v-model="searchForm.keyword" placeholder="请输入专辑标题" clearable style="width: 200px" />
            </el-form-item>

            <el-form-item label="创建者">
                <el-select v-model="searchForm.user_id" filterable remote reserve-keyword placeholder="请搜索创建者"
                    :remote-method="searchCreators" :loading="creatorsLoading" clearable style="width: 200px">
                    <el-option v-for="creator in creatorsOptions" :key="creator.id"
                        :label="creator.nickname || creator.username" :value="creator.id">
                        <div style="display: flex; align-items: center;">
                            <el-avatar :size="20" :src="creator.avatar" style="margin-right: 6px;">
                                {{ (creator.nickname || creator.username).charAt(0) }}
                            </el-avatar>
                            <span>{{ creator.nickname || creator.username }}</span>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="分类">
                <el-select v-model="searchForm.category_id" filterable remote reserve-keyword placeholder="请搜索分类"
                    :remote-method="searchCategories" :loading="categoriesLoading" clearable style="width: 200px">
                    <el-option v-for="category in categoriesOptions" :key="category.id" :label="category.name"
                        :value="category.id" />
                </el-select>
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
                    <el-option label="启用" :value="1" />
                    <el-option label="禁用" :value="0" />
                </el-select>
            </el-form-item>

            <el-form-item label="排序">
                <el-select v-model="searchForm.sort_by" placeholder="请选择排序方式" style="width: 150px">
                    <el-option label="创建时间降序" value="created_at DESC" />
                    <el-option label="创建时间升序" value="created_at ASC" />
                    <el-option label="更新时间降序" value="updated_at DESC" />
                    <el-option label="排序值升序" value="sort_order ASC" />
                    <el-option label="标题A-Z" value="title ASC" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="handleReset">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { searchUsers, searchVideoCategories } from '@/service/api/videos/videos'
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

interface SearchParams {
    keyword?: string
    user_id?: string
    category_id?: string
    status?: number
    sort_by?: string
}

interface Emits {
    (e: 'search', params: SearchParams): void
    (e: 'reset'): void
}

const emit = defineEmits<Emits>()

const creatorsLoading = ref(false)
const categoriesLoading = ref(false)
const creatorsOptions = ref<any[]>([])
const categoriesOptions = ref<any[]>([])

// 搜索表单
const searchForm = reactive<SearchParams>({
    keyword: '',
    user_id: '',
    category_id: '',
    status: undefined,
    sort_by: 'created_at DESC'
})

// 搜索创建者
const searchCreators = async (query: string) => {
    if (query) {
        creatorsLoading.value = true
        try {
            const result = await searchUsers({
                page: { pageNo: 1, pageSize: 20 },
                data: { keyword: query, status: 1 }
            })

            if (result?.data?.list) {
                creatorsOptions.value = result.data.list
            } else {
                creatorsOptions.value = []
            }
        } catch (error) {
            console.error('搜索创建者失败:', error)
            ElMessage.error('搜索创建者失败')
            creatorsOptions.value = []
        } finally {
            creatorsLoading.value = false
        }
    } else {
        creatorsOptions.value = []
    }
}

// 搜索分类
const searchCategories = async (query: string) => {
    categoriesLoading.value = true
    try {
        const params: any = {
            page: { pageNo: 1, pageSize: 20 },
            data: { status: 1 }
        }
        
        // 如果有查询关键字，添加到参数中
        if (query) {
            params.data.keyword = query
        }
        
        const result = await searchVideoCategories(params)

        if (result?.data?.list) {
            categoriesOptions.value = result.data.list
        } else {
            categoriesOptions.value = []
        }
    } catch (error) {
        console.error('搜索分类失败:', error)
        ElMessage.error('搜索分类失败')
        categoriesOptions.value = []
    } finally {
        categoriesLoading.value = false
    }
}

// 处理搜索
const handleSearch = () => {
    emit('search', { ...searchForm })
}

// 处理重置
const handleReset = () => {
    Object.keys(searchForm).forEach(key => {
        if (key === 'sort_by') {
            searchForm[key] = 'created_at DESC'
        } else {
            searchForm[key as keyof SearchParams] = undefined as any
        }
    })
    searchForm.keyword = ''
    searchForm.user_id = ''
    searchForm.category_id = ''

    // 清空选项
    creatorsOptions.value = []
    categoriesOptions.value = []

    // 重新加载默认分类选项
    loadDefaultOptions()

    emit('reset')
}

// 加载默认选项
const loadDefaultOptions = async () => {
    // 加载一些默认的分类选项
    try {
        const result = await searchVideoCategories({
            page: { pageNo: 1, pageSize: 10 },
            data: { status: 1 }
        })

        if (result?.data?.list) {
            categoriesOptions.value = result.data.list
        }
    } catch (error) {
        console.error('加载默认分类失败:', error)
    }
}

// 组件挂载时加载默认选项
onMounted(() => {
    loadDefaultOptions()
})
</script>

<style scoped>
.search-bar {
    margin-bottom: 20px;
}

.search-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

:deep(.el-select-dropdown__item) {
    height: auto;
    line-height: normal;
    padding: 6px 20px;
}
</style>