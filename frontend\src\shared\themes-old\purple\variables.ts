/**
 * 神秘高雅主题 - CSS变量
 * 紫色主调 + 薰衣草紫 + 李子紫
 * 设计参考: PrimeVue Indigo主题 + Tailwind紫色系
 */

// CSS变量配置
const variables = {
    // 主要颜色
    'color-primary': '#6d28d9',
    'color-primary-light': '#7c3aed',
    'color-primary-dark': '#5b21b6',
    'color-primary-contrast': '#ffffff',
    'color-primary-secondary': '#a78bfa', // 第二配色 - 比主色浅的相同色系


    // 强调色
    'color-accent': '#9333ea',
    'color-accent-light': '#a855f7',
    'color-accent-dark': '#7e22ce',
    'color-accent-contrast': '#ffffff',

    // 中性色调 - 优雅紫色系
    'color-neutral-50': '#f5f3ff',
    'color-neutral-100': '#ede9fe',
    'color-neutral-200': '#ddd6fe',
    'color-neutral-300': '#c4b5fd',
    'color-neutral-400': '#a78bfa',
    'color-neutral-500': '#8b5cf6',
    'color-neutral-600': '#7c3aed',
    'color-neutral-700': '#6d28d9',
    'color-neutral-800': '#5b21b6',
    'color-neutral-900': '#4c1d95',

    // 成功/错误/警告/信息色
    'color-success': '#10b981',
    'color-error': '#e11d48',
    'color-warning': '#f59e0b',
    'color-info': '#3b82f6',

    // 背景颜色
    'color-background': '#ffffff',
    'color-background-alt': '#f5f3ff',
    'color-background-hover': '#ede9fe',
    'color-background-card': 'linear-gradient(145deg, #ffffff, #f5f3ff)',
    // 主页面背景和文字色
    'color-page-background': '#faf9ff',
    'color-page-text': '#4c1d95',

    // 文本颜色
    'color-text': '#4c1d95',
    'color-text-light': '#5b21b6',
    'color-text-lighter': '#6d28d9',
    'color-text-contrast': '#ffffff',

    // 边框颜色
    'color-border': '#ddd6fe',
    'color-border-light': '#ede9fe',
    'color-border-dark': '#c4b5fd',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(109, 40, 217, 0.05)',
    'shadow': '0 1px 3px 0 rgba(109, 40, 217, 0.1), 0 1px 2px 0 rgba(109, 40, 217, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(109, 40, 217, 0.1), 0 2px 4px -1px rgba(109, 40, 217, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(109, 40, 217, 0.1), 0 4px 6px -2px rgba(109, 40, 217, 0.05)',

    // 特定区域颜色
    'header-gradient': 'linear-gradient(135deg, #6d28d9, #7c3aed)',
    'footer-gradient': 'linear-gradient(135deg, #ede9fe, #f5f3ff)',
    'color-nav-gradient': '#4c1d95',
    'color-footer-gradient': '#5b21b6',
    'color-footer-border': '#ddd6fe',
    'button-gradient': 'linear-gradient(135deg, #8b5cf6, #6d28d9)',
    'card-gradient': 'linear-gradient(145deg, #ffffff, #f5f3ff)',
    'accent-gradient': 'linear-gradient(135deg, #a855f7, #9333ea)',

    // PrimeVue集成
    'primary-color': '#6d28d9',
    'primary-color-text': '#ffffff',
    'surface-ground': '#faf9ff',
    'surface-section': '#faf9ff',
    'surface-card': 'linear-gradient(145deg, #ffffff, #f5f3ff)',
    'surface-overlay': '#ffffff',
    'surface-border': '#ddd6fe',
    'surface-hover': '#ede9fe',
    // 主页面内容区域
    'content-bg': '#faf9ff',
    'content-text': '#4c1d95',
};

export default variables;