package home

import (
	"frontapi/internal/api"
	"frontapi/internal/models/videos"
	homeSrv "frontapi/internal/service/home"
	sysSrv "frontapi/internal/service/system"
	userSrv "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
	homeTypings "frontapi/internal/typings/home"

	"github.com/gofiber/fiber/v2"
)

// HomeController 首页控制器
type HomeController struct {
	api.BaseController   // 继承BaseController
	homeService          homeSrv.HomeService
	videoService         videoSrv.VideoService
	tagService           sysSrv.TagService
	userService          userSrv.UserService
	videoCategoryService videoSrv.VideoCategoryService
	videoChannelService  videoSrv.VideoChannelService
	videoAlbumService    videoSrv.VideoAlbumService
	// hotkeywordService    system.TagService
}

// NewHomeController 创建首页控制器实例
func NewHomeController(homeService homeSrv.HomeService,
	videoService videoSrv.VideoService,
	tagService sysSrv.TagService,
	userService userSrv.UserService,
	videoCategoryService videoSrv.VideoCategoryService,
	videoChannelService videoSrv.VideoChannelService,
	videoAlbumService videoSrv.VideoAlbumService) *HomeController {
	return &HomeController{
		homeService:          homeService,
		videoService:         videoService,
		tagService:           tagService,
		userService:          userService,
		videoCategoryService: videoCategoryService,
		videoAlbumService:    videoAlbumService,
	}
}

func (c *HomeController) Index(ctx *fiber.Ctx) error {
	return c.Success(ctx, "hello world")
}

// GetTags 获取首页标签列表
func (c *HomeController) GetTags(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := 20

	// 获取标签列表
	tags, err := c.homeService.GetTagList(ctx.Context())

	if err != nil {
		return c.InternalServerError(ctx, "获取标签列表失败: "+err.Error())
	}

	// 转换为前端响应类型
	response := homeTypings.ConvertTagListResponse(tags, int64(len(tags)), page, pageSize)

	return c.Success(ctx, response)
}

// GetHotVideos 获取热门视频列表
func (c *HomeController) GetHotVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	// user := c.GetLoginUser(ctx)
	userId := c.GetUserID(ctx)
	page := reqInfo.Page.PageNo
	pageSize := 30

	// 获取热门视频列表
	videos, total, err := c.videoService.List(ctx.Context(), map[string]interface{}{
		"status": 2, // 只获取正常状态的视频
	}, "heat DESC", page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取热门视频列表失败: "+err.Error())
	}
	//视频是否点赞收藏
	if videos != nil && len(videos) > 0 {
		for _, video := range videos {
			//视频作者信息
			video, err = c.getVideoInfo(ctx, video, userId)
		}

	}
	// 转换为前端响应类型
	response := homeTypings.ConvertVideoListResponse(videos, total, page, pageSize)

	return c.Success(ctx, response)
}

// GetStars 获取推荐明星列表
func (c *HomeController) GetRecommendStarList(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	loginUser := c.GetLoginUser(ctx)
	userId := c.GetUserID(ctx)
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	pageSize = 30

	// 获取明星列表
	users, total, err := c.userService.GetAllCelebrity(ctx.Context(), map[string]interface{}{
		"user_type": 11, // 只获取明星用户
		"status":    1,  // 只获取正常状态的用户
	}, "heat DESC", page, pageSize)

	if err != nil {
		return c.InternalServerError(ctx, "获取明星列表失败: "+err.Error())
	}
	//是否关注
	if users != nil && loginUser != nil {
		for _, user := range users {
			user.IsFollowed, err = c.checkUserFollow(ctx, user.ID, userId)
			if err != nil {
				return c.InternalServerError(ctx, "获取是否关注失败: "+err.Error())
			}
		}
	}
	// 转换为前端响应类型
	response := homeTypings.ConvertStarListResponse(users, total, page, pageSize)

	return c.Success(ctx, response)
}

// 推荐视频频道
func (c *HomeController) GetRecommendChannels(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	pageSize = 60

	// 获取分类列表
	channels, total, err := c.videoChannelService.List(ctx.Context(), map[string]interface{}{
		"status": 1, // 只获取启用状态的分类
	}, "sort_order ASC", page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取分类列表失败: "+err.Error())
	}

	// 转换为前端响应类型
	response := homeTypings.ConvertChannelListResponse(channels, total, page, pageSize)

	return c.Success(ctx, response)
}

// GetCategories 获取分类列表
func (c *HomeController) GetTopCategoryNav(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	pageSize = 60

	// 获取分类列表
	categories, err := c.videoCategoryService.FindAll(ctx.Context(), map[string]interface{}{
		"status":      1, // 只获取启用状态的分类
		"is_featured": 1, // 只获取推荐分类
	}, "is_featured=1, sort_order ASC", false)

	if err != nil {
		return c.InternalServerError(ctx, "获取分类列表失败: "+err.Error())
	}

	// 转换为前端响应类型
	response := homeTypings.ConvertCategoryListResponse(categories, int64(len(categories)), page, pageSize)

	return c.Success(ctx, response)
}

// 推荐视频专辑
func (c *HomeController) GetRecommendAlbums(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	userId := c.GetUserID(ctx)
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	pageSize = 30

	// 获取分类列表
	albums, total, err := c.videoAlbumService.List(ctx.Context(), map[string]interface{}{
		"status": 1, // 只获取启用状态的分类
	}, "sort_order ASC", page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取分类列表失败: "+err.Error())
	}
	if albums != nil && len(albums) > 0 {
		for _, album := range albums {
			album, err = c.getAlbumInfo(ctx, album, userId)
			if err != nil {
				return c.InternalServerError(ctx, "获取专辑信息失败: "+err.Error())
			}
		}
	}
	// 转换为前端响应类型
	response := homeTypings.ConvertAlbumListResponse(albums, total, page, pageSize)

	return c.Success(ctx, response)
}

// GetRecommendVideos 获取推荐视频列表
func (c *HomeController) GetRecommendVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	userId := c.GetUserID(ctx)
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	categoryId := reqInfo.Get("category_id").GetString()

	// 获取推荐视频列表
	videos, total, err := c.videoService.List(ctx.Context(), map[string]interface{}{
		"category_id": categoryId,
		"status":      1, // 只获取正常状态的视频
	}, "heat DESC", page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取推荐视频列表失败: "+err.Error())
	}
	//视频是否点赞收藏
	if videos != nil && len(videos) > 0 {
		for _, video := range videos {
			video, err = c.getVideoInfo(ctx, video, userId)
			if err != nil {
				return c.InternalServerError(ctx, "获取视频信息失败: "+err.Error())
			}
		}
	}
	// 转换为前端响应类型
	response := homeTypings.ConvertVideoListResponse(videos, total, page, pageSize)

	return c.Success(ctx, response)
}

// GetLatestVideos 获取最新视频列表
func (c *HomeController) GetLatestVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	userId := c.GetUserID(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	categoryId := reqInfo.Get("category_id").GetString()

	// 获取最新视频列表
	videos, total, err := c.videoService.List(ctx.Context(), map[string]interface{}{
		"category_id": categoryId,
		"status":      1, // 只获取正常状态的视频
	}, "created_at DESC", page, pageSize, false)
	//视频是否点赞收藏
	if videos != nil && len(videos) > 0 {
		for _, video := range videos {
			//视频作者信息
			video, err = c.getVideoInfo(ctx, video, userId)
			if err != nil {
				return c.InternalServerError(ctx, "获取视频信息失败: "+err.Error())
			}
		}
	}
	if err != nil {
		return c.InternalServerError(ctx, "获取最新视频列表失败: "+err.Error())
	}
	// 转换为前端响应类型
	response := homeTypings.ConvertVideoListResponse(videos, total, page, pageSize)

	return c.Success(ctx, response)
}

// GetHotCreators 获取热门创作者列表
func (c *HomeController) GetHotCreators(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	user := c.GetLoginUser(ctx)
	userFollowService := c.GetUserFollowService()
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	orderBy := "heat DESC,(total_shorts+total_albums+total_videos+total_views+total_likes) DESC"
	// 获取热门创作者列表
	creators, total, err := c.userService.List(ctx.Context(), map[string]interface{}{
		"is_content_creator": 1, // 只获取内容创作者
		"status":             1, // 只获取正常状态的用户
	}, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取热门创作者列表失败: "+err.Error())
	}
	//是否关注
	if creators != nil && user != nil {
		for _, creator := range creators {
			follow, err := userFollowService.FindOne(ctx.Context(), map[string]interface{}{
				"user_id":   user.ID,
				"follow_id": creator.ID,
			})
			if err != nil {
				return c.InternalServerError(ctx, "获取是否关注失败: "+err.Error())
			}
			creator.IsFollowed = follow != nil
		}
	}
	// 转换为前端响应类型
	response := homeTypings.ConvertUserListResponse(creators, total, page, pageSize)

	return c.Success(ctx, response)
}

// 用户是否关注
func (c *HomeController) checkUserFollow(ctx *fiber.Ctx, followID string, userId string) (bool, error) {
	userFollowService := c.GetUserFollowService()
	follow, err := userFollowService.FindOne(ctx.Context(), map[string]interface{}{
		"user_id":   userId,
		"follow_id": followID,
	})
	if err != nil {
		return false, err
	}
	isFollowed := follow != nil
	return isFollowed, nil
}

// 获取视频信息
func (c *HomeController) getVideoInfo(ctx *fiber.Ctx, video *videos.Video, userId string) (*videos.Video, error) {
	//视频作者信息
	var err error
	if video.CreatorID.ValueOrZero() != "" {
		video.Author, err = c.userService.GetByID(ctx.Context(), video.CreatorID.ValueOrZero(), false)
		if err != nil {
			return video, err
		}
	}
	if userId != "" {
		//视频是否点赞收藏
		video.IsLiked, err = c.videoService.CheckUserLiked(ctx.Context(), userId, video.ID)
		if err != nil {
			return video, err
		}
		video.IsFavorite, err = c.videoService.CheckUserCollected(ctx.Context(), userId, video.ID)
		if err != nil {
			return video, err
		}
		if video.Author != nil && userId != "" {
			//视频作者是否关注
			video.Author.IsFollowed, err = c.checkUserFollow(ctx, video.Author.ID, userId)
			if err != nil {
				return video, err
			}
		}
	}
	return video, err
}

// 获取专辑信息
func (c *HomeController) getAlbumInfo(ctx *fiber.Ctx, album *videos.VideoAlbum, userId string) (*videos.VideoAlbum, error) {
	//专辑作者信息
	var err error
	if album.UserID.ValueOrZero() != "" {
		album.Author, err = c.userService.GetByID(ctx.Context(), album.UserID.ValueOrZero(), false)
		if err != nil {
			return album, err
		}
	}
	//专辑是否点赞收藏
	if userId != "" {
		album.IsLiked, err = c.videoAlbumService.CheckCollection(ctx.Context(), userId, album.ID)
		if err != nil {
			return album, err
		}
		if album.Author != nil && userId != "" {
			//专辑作者是否关注
			album.Author.IsFollowed, err = c.checkUserFollow(ctx, album.Author.ID, userId)
			if err != nil {
				return album, err
			}
		}
	}
	return album, err
}
