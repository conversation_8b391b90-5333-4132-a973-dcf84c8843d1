package videos

import (
	"context"
	userRepo "frontapi/internal/repository/users"

	"frontapi/internal/models/videos"
	repo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

// VideoService 视频服务接口
type VideoService interface {
	base.IExtendedService[videos.Video]
	CheckUserLiked(ctx context.Context, userID, videoID string) (bool, error)     //是否点赞
	CheckUserCollected(ctx context.Context, userID, videoID string) (bool, error) //是否收藏
}

// videoService 视频服务实现
type videoService struct {
	*base.ExtendedService[videos.Video]
	videoRepo            repo.VideoRepository
	videoSourceRepo      repo.VideoSourceRepository
	videoCateRepo        repo.VideoCategoryRepository
	videoChanelRepo      repo.VideoChannelRepository
	userRepo             userRepo.UserRepository
	videoLikeRepo        repo.VideoLikeRepository
	userVideoCollectRepo userRepo.UserVideoCollectionRepository
}

// NewVideoService 创建视频服务实例
func NewVideoService(
	videoRepo repo.VideoRepository,
	videoSourceRepo repo.VideoSourceRepository,
	videoCateRepo repo.VideoCategoryRepository,
	videoChanelRepo repo.VideoChannelRepository,
	userRepo userRepo.UserRepository,
	videoLikeRepo repo.VideoLikeRepository,
	userVideoCollectRepo userRepo.UserVideoCollectionRepository,
) VideoService {
	return &videoService{
		ExtendedService:      base.NewExtendedService[videos.Video](videoRepo, "video"),
		videoRepo:            videoRepo,
		videoSourceRepo:      videoSourceRepo,
		videoCateRepo:        videoCateRepo,
		videoChanelRepo:      videoChanelRepo,
		userRepo:             userRepo,
		videoLikeRepo:        videoLikeRepo,
		userVideoCollectRepo: userVideoCollectRepo,
	}
}
func (s *videoService) CheckUserLiked(ctx context.Context, userID, videoID string) (bool, error) {
	return s.videoLikeRepo.Exists(ctx, map[string]interface{}{
		"user_id":  userID,
		"video_id": videoID,
	})
}
func (s *videoService) CheckUserCollected(ctx context.Context, userID, videoID string) (bool, error) {
	return s.userVideoCollectRepo.Exists(ctx, map[string]interface{}{
		"user_id":  userID,
		"video_id": videoID,
	})
}
