package users

import (
	"frontapi/internal/repository/shortvideos"
	"frontapi/internal/service/base"

	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
)

// CreateShortsCollectionRequest 创建短视频收藏请求
type CreateShortsCollectionRequest struct {
	UserID   string `json:"userId" validate:"required"`
	ShortsID string `json:"shortsId" validate:"required"`
	Remark   string `json:"remark"`
}

// UserShortsCollectionService 用户短视频收藏服务接口
type UserShortsCollectionService interface {
	base.IExtendedService[users.UserShortsCollection]
}

// userShortsCollectionService 用户短视频收藏服务实现
type userShortsCollectionService struct {
	*base.ExtendedService[users.UserShortsCollection]
	shortsCollectionRepo repo.UserShortsCollectionRepository
	shortsRepo           shortvideos.ShortVideoRepository
	userRepo             repo.UserRepository
}

// NewUserShortsCollectionService 创建用户短视频收藏服务实例
func NewUserShortsCollectionService(
	shortsCollectionRepo repo.UserShortsCollectionRepository,
	shortsRepo shortvideos.ShortVideoRepository,
	userRepo repo.UserRepository,
) UserShortsCollectionService {
	return &userShortsCollectionService{
		ExtendedService:      base.NewExtendedService[users.UserShortsCollection](shortsCollectionRepo, "user_shorts_collection"),
		shortsCollectionRepo: shortsCollectionRepo,
		shortsRepo:           shortsRepo,
		userRepo:             userRepo,
	}
}
