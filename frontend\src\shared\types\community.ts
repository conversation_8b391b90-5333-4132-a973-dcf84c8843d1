// 基础媒体项目
export interface MediaItem {
  id: string
  type: 'image' | 'video' | 'audio'
  url: string
  thumbnail?: string
  width?: number
  height?: number
  duration?: number
  size?: number
  format?: string
}

// 分享视频信息
export interface SharedVideoInfo {
  id: string
  title: string
  description: string
  cover: string
  url: string
  duration: number
  creatorId: string
  creatorName: string
  creatorAvatar: string
  viewsCount: number
  likesCount: number
  createdAt: string
}

// 视频内容
export interface VideoContent {
  id: string
  title: string
  description: string
  cover: string
  url: string
  duration: number
  resolution: string
  quality: 'SD' | 'HD' | 'FHD' | '4K'
  tags: string[]
  category: string
  viewsCount: number
  likesCount: number
  sharesCount: number
  commentsCount: number
  createdAt: string
}

// 帖子评论
export interface PostComment {
  id: string
  content: string
  userId: string
  userName: string
  userAvatar: string
  userVerified: boolean
  postId: string
  parentId?: string
  replyToId?: string
  replyToUser?: {
    id: string
    name: string
    avatar: string
  }
  likesCount: number
  repliesCount: number
  isLiked?: boolean
  replies?: Comment[]
  images?: string[]
  createdAt: string
  updatedAt: string
}

// 帖子信息
export interface Post {
  id: string
  content: string
  userId: string
  userName: string
  userAvatar: string
  userVerified: boolean
  userLevel: number
  images?: string[]
  videos?: VideoContent[]
  sharedVideo?: SharedVideoInfo
  location?: string
  topics?: string[]
  viewsCount: number
  likesCount: number
  sharesCount: number
  commentsCount: number
  favoritesCount: number
  isLiked?: boolean
  isFavorited?: boolean
  isFollowing?: boolean
  visibility: 'public' | 'followers' | 'private'
  allowComments: boolean
  isPinned?: boolean
  isHot?: boolean
  createdAt: string
  updatedAt: string
}

// 推荐视频
export interface RecommendedVideo {
  id: string
  title: string
  description: string
  cover: string
  url: string
  duration: number
  creatorId: string
  creatorName: string
  creatorAvatar: string
  creatorVerified: boolean
  category: string
  tags: string[]
  viewsCount: number
  likesCount: number
  publishedAt: string
  reason?: string // 推荐理由
}

// 发帖请求
export interface PostRequest {
  content: string
  images?: string[]
  videos?: string[]
  sharedVideoId?: string
  location?: string
  topics?: string[]
  visibility?: 'public' | 'followers' | 'private'
  allowComments?: boolean
}

// 帖子列表响应
export interface PostListResponse {
  list: Post[]
  page: number
  pageSize: number
  total: number
  hasMore: boolean
}

// 热门话题
export interface HotTopic {
  id: string
  name: string
  description?: string
  cover?: string
  postsCount: number
  participantsCount: number
  isFollowing?: boolean
  trend: 'up' | 'down' | 'stable'
  trendValue: number
  createdAt: string
}

// 话题详情
export interface Topic {
  id: string
  name: string
  description: string
  cover?: string
  banner?: string
  postsCount: number
  participantsCount: number
  followersCount: number
  isFollowing?: boolean
  isOfficial: boolean
  moderators: {
    id: string
    name: string
    avatar: string
  }[]
  rules?: string[]
  relatedTopics?: {
    id: string
    name: string
  }[]
  createdAt: string
  updatedAt: string
}

// 社区统计
export interface CommunityStats {
  totalPosts: number
  totalUsers: number
  totalComments: number
  totalLikes: number
  dailyActiveUsers: number
  weeklyActiveUsers: number
  monthlyActiveUsers: number
  trendingTopics: HotTopic[]
  popularPosts: Post[]
}

// 帖子查询参数
export interface PostQueryParams {
  userId?: string
  topicId?: string
  keyword?: string
  type?: 'all' | 'text' | 'image' | 'video' | 'shared'
  sortBy?: 'latest' | 'popular' | 'trending' | 'hot'
  timeRange?: 'day' | 'week' | 'month' | 'year' | 'all'
  page?: number
  pageSize?: number
}

// 用户查询参数
export interface UserQueryParams {
  keyword?: string
  verified?: boolean
  level?: number
  location?: string
  sortBy?: 'followers' | 'posts' | 'likes' | 'latest'
  page?: number
  pageSize?: number
}

// 兼容旧版本用户类型
export interface LegacyUser {
  id: string
  username: string
  nickname: string
  avatar: string
  verified: boolean
  level: number
  followersCount: number
  followingCount: number
  isFollowing?: boolean
}

// 推荐用户
export interface RecommendedUser {
  id: string
  username: string
  nickname: string
  avatar: string
  verified: boolean
  verifiedType?: 'personal' | 'enterprise' | 'media' | 'government'
  level: number
  signature?: string
  tags: string[]
  followersCount: number
  postsCount: number
  isFollowing?: boolean
  reason?: string // 推荐理由
  mutualFollowersCount?: number // 共同关注数
  mutualFollowers?: {
    id: string
    name: string
    avatar: string
  }[]
}

// 帖子统计信息
export interface PostStats {
  viewsCount: number
  likesCount: number
  sharesCount: number
  commentsCount: number
  favoritesCount: number
  reachCount: number // 触达人数
  engagementRate: number // 互动率
  viewsGrowth: number // 浏览量增长
  likesGrowth: number // 点赞增长
  sharesGrowth: number // 分享增长
  commentsGrowth: number // 评论增长
}

// 类型守卫
export const isPost = (obj: any): obj is Post => {
  return obj && typeof obj.id === 'string' && typeof obj.content === 'string' && typeof obj.userId === 'string'
}

export const isComment = (obj: any): obj is Comment => {
  return obj && typeof obj.id === 'string' && typeof obj.content === 'string' && typeof obj.postId === 'string'
}

export const isTopic = (obj: any): obj is Topic => {
  return obj && typeof obj.id === 'string' && typeof obj.name === 'string'
}

export const isRecommendedUser = (obj: any): obj is RecommendedUser => {
  return obj && typeof obj.id === 'string' && typeof obj.username === 'string' && Array.isArray(obj.tags)
}