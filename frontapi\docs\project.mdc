---
description: 
globs: 
alwaysApply: true
---
# 前后端Web应用开发规则

## 项目架构规则

### 目录结构
- frontapi/: Go后端API服务采用fiber v2框架
  - internal/: 内部包
    - models/: 数据模型定义
     - advs/: 广告模块model
     - books/: 电子书模块model
     - comics/: 漫画模块model
     - content_creator/:创作者模块model
     - home/: 首页模块model
     - integral/: 积分模块model
     - permission/: 权限模块model
     - pictures/: 图片模块model
     - promotion/: 推广模块model
     - posts/: 帖子模块model
     - shortvideos/: 短视频模块model
     - sys/: 系统模块model
     - tags/:标签模块model
     - users/: 用户模块model
     - videos/: 视频模块model
     - vips/: vip模块model
     - wallets/: 钱包模块model
    - repository/: 数据访问层
      - advs/: 广告模块数据访问层
      - books/: 电子书模块数据访问层
      - comics/: 漫画模块数据访问层
      - content_creator/:创作者模块数据访问层
      - home/: 首页模块数据访问层
      - integral/: 积分模块数据访问层
      - permission/: 权限模块数据访问层
      - pictures/: 图片模块数据访问层
      - promotion/: 推广模块数据访问层
      - posts/: 帖子模块数据访问层
      - shortvideos/: 短视频模块数据访问层
      - sys/: 系统模块数据访问层
      - tags/:标签模块数据访问层
      - users/: 用户模块数据访问层
      - videos/: 视频模块数据访问层
      - vips/: vip模块数据访问层
      - wallets/: 钱包模块数据访问层
    - service/: 业务逻辑层
     - advs/: 广告模块业务逻辑层
     - books/: 电子书模块业务逻辑层
     - comics/: 漫画模块业务逻辑层
     - content_creator/:创作者模块业务逻辑层
     - home/: 首页模块业务逻辑层
     - integral/: 积分模块业务逻辑层
     - permission/: 权限模块业务逻辑层
     - pictures/: 图片模块业务逻辑层
     - promotion/: 推广模块业务逻辑层
     - posts/: 帖子模块业务逻辑层
     - shortvideos/: 短视频模块业务逻辑层
     - sys/: 系统模块业务逻辑层
     - tags/:标签模块业务逻辑层
     - users/: 用户模块业务逻辑层
     - videos/: 视频模块业务逻辑层
     - vips/: vip模块业务逻辑层
     - wallets/: 钱包模块业务逻辑层

    - admin/: 管理后台控制器接口
      - auth/: 管理后台认证模块controller
      - books/: 管理后台电子书模块controller
      - content_creator/:管理后台创作者模块controller
      - comics/: 管理后台漫画模块controller
      - home/: 管理后台首页模块controller
      - integral/: 管理后台积分模块controller
      - permission/:管理后台权限模块controller
      - pictures/:管理后台图片模块controller
      - posts/: 管理后台帖子模块controller
      - promotion/:管理后台推广模块controller
      - shortvideos/:管理后台短视频模块controller
      - sys/:管理后台系统模块controller
      - system/:管理后台系统模块controller
      - users/:管理后台用户模块controller
      - videos/:管理后台视频模块controller
      - vips/:管理后台会员模块controller
      - wallets/:管理后台钱包模块controller
    - api/: 前端用户端控制器接口
      - advs/: 前端用户端广告模块controller
      - auth/: 用户端认证模块controller
      - books/: 用户端电子书模块controller
      - content_creator/:用户端创作者模块controller
      - comics/: 用户端漫画模块controller
      - home/: 用户端首页模块controller
      - integral/: 用户端积分模块controller
      - permission/:用户端权限模块controller
      - pictures/:用户端图片模块controller
      - posts/: 用户端帖子模块controller
      - promotion/:用户端推广模块controller
      - shortvideos/:用户端短视频模块controller
      - sys/:用户端系统模块controller
      - system/:用户端系统模块controller
      - users/:用户端用户模块controller
      - videos/:用户端视频模块controller
      - vips/:用户端会员模块controller
      - wallets/:用户端钱包模块controller
    - routes/: 路由定义
    - middleware/: 中间件
    - bootstrap/: 应用初始化
    - validator/: 请求验证
  - cmd/: 应用入口
    - admin/: 管理后台接口入口
    - api/: 前端用户接口入口
    - main.go: 应用入口
  - config/: 配置文件
  - migrations/: 数据库迁移
  - db/: 数据库文件
  - docs/: 文档
  - pkg/: 公共包

- lyadmin/: Vue管理后台

  - locales/: 国际化和多语言
  - src/: 源代码
    - api/: API接口定义
      - advs/: 广告模块api
      - books/: 电子书模块api
      - comics/: 漫画模块api
      - posts/: 帖子模块api
      - shortvideos/: 短视频模块api
      - system/: 系统模块api
      - users/: 用户模块api
      - videos/: 视频模块api
      -  vips/: vip模块api
    - views/: 页面视图
     - advs/: 广告模块页面
     - permission/:权限模块页面
     - posts/: 帖子模块页面
     - login/: 登录页面
     - users/: 用户模块页面
     - system/: 系统模块页面
     - shortvideos/: 短视频模块页面
     - videos/: 视频模块页面
     - welcome/: 欢迎页面
     - error/: 错误页面
    - components/: 组件
    - router/: 路由
    - store/: 状态管理
    - utils/: 工具函数
    - config/: 配置文件
    - layout/: 布局
    - plugins/: 插件
    - assets/: 静态资源
    - static/: 静态资源
    - types/: 类型定义
    - directives/: 指令
    - utils/: 工具函数
    - main.js: 应用入口
    - App.vue: 应用入口

- frontend/: Vue前端应用
  - src/: 源代码
    - api/: API接口定义
     - books/: 电子书模块api
     - category/:分类模块api
     - celebrity/: 影人模块api
     - channels/:板块模块api
     - comics/: 漫画模块api
     - community/:社区(帖子)模块api
     - home/: 首页模块api
     - live/: 直播模块api
     - pictures/: 图片模块api
     - shorts/: 短视频模块api
     - space/: 空间模块api
     - users/: 用户模块api
    - views/: 页面视图
     - account/: 账户模块视图
     - books/: 电子书模块视图
     - celebrity/: 明星模块视图
     - categories/: 视频分类模块视图
     - channels/: 频道模块视图
     - comics/: 动漫模块视图
     - community/: 社区模块视图
     - error/: 错误页面
     - home/: 首页模块视图
     - live/: 直播模块视图
     - shorts/: 短视频模块视图
     - pictures/: 图片模块视图
     - space/: 空间模块视图
     - user/: 用户模块视图
     - videos/: 视频模块视图
    - components/: 组件
    - router/: 路由
    - stores/: 状态管理
    - styles/: 样式
    - plugins/: 插件
    - layouts/:  布局
    - types/: 类型定义
    - utils/: 工具函数
    - assets/: 静态资源

## 代码规范

### 后端Go代码规范
- 使用驼峰命名法，公开函数/结构体首字母大写
- 每个包应有明确的职责，避免循环依赖
- 使用依赖注入模式管理服务依赖
- 实现接口而非直接依赖具体类型
- 错误处理必须明确，避免忽略错误
- 注释应当解释"为什么"而非"是什么"

### 后端项目层级规范
- models层: 定义数据结构和关系
- repository层: 负责数据访问和持久化
- service层: 实现业务逻辑，调用repository层
- admin/api层: 处理HTTP请求，调用service层
- routes层: 定义API路由和中间件
- 严格遵循依赖方向: controller -> service -> repository -> models

### 前端Vue代码规范
- 使用组件化开发方式，每个组件有单一职责
- 使用TypeScript进行类型检查
- 视图与业务逻辑分离，使用Pinia/Vuex管理状态
- API接口统一在api目录下定义
- 使用Vue Router管理路由
- 遵循一致的CSS命名规范(如BEM)

## 开发流程

### 功能开发流程
1. 在models层定义数据模型
2. 在repository层实现数据访问方法
3. 在service层实现业务逻辑
4. 在admin/api层实现HTTP接口
5. 在routes层注册路由
6. 在前端api中定义接口
7. 在前端视图中实现界面

### 代码审查要点
- 代码是否符合项目架构规范
- 是否存在安全隐患
- 是否处理了所有可能的错误
- 是否有足够的测试覆盖
- 性能是否满足要求
- 是否遵循了既定的代码风格

## 通用开发准则
- 保持简单，避免过度设计
- 编写可测试的代码
- 关注性能和安全性
- 代码应该自文档化
- 遵循最小权限原则
- 定期进行代码重构



