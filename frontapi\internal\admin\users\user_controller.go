package users

import (
	"frontapi/internal/admin"
	userModel "frontapi/internal/models/users"
	userSrv "frontapi/internal/service/users"
	userValidator "frontapi/internal/validation/users"

	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
	"github.com/guregu/null/v6"
)

// UserController 用户控制器
type UserController struct {
	UserService          userSrv.UserService
	admin.BaseController // 继承Response

}

// NewUserController 创建用户控制器实例
func NewUserController(userService userSrv.UserService) *UserController {
	return &UserController{
		UserService: userService,
	}
}

// ListUsers 获取用户列表
func (c *UserController) ListUsers(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	keyword := reqInfo.Get("keyword").GetString()
	status := -999
	err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)

	// 获取内容创作者过滤条件
	isContentCreator := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "is_content_creator", &isContentCreator)

	// 获取用户类型过滤条件
	userType := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "user_type", &userType)

	// 获取时间范围参数 - 前端发送的是start_date和end_date
	startDate := reqInfo.Get("reg_time_start").GetString()
	endDate := reqInfo.Get("reg_time_end").GetString()

	// 构建查询条件，映射到用户仓库中ApplyConditions期望的字段名
	condition := map[string]interface{}{
		"keyword":            keyword,
		"status":             status,
		"user_type":          userType,
		"is_content_creator": isContentCreator,
		"reg_time_start":     startDate, // 映射前端的start_date到reg_time_start
		"reg_time_end":       endDate,   // 映射前端的end_date到reg_time_end
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "reg_time DESC"
	}
	// 查询用户列表
	userList, total, err := c.UserService.List(ctx.Context(), condition, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取用户列表失败: "+err.Error())
	}

	// 返回用户列表
	return c.SuccessList(ctx, userList, total, page, pageSize)
}

// GetUser 获取用户详情
func (c *UserController) GetUser(ctx *fiber.Ctx) error {
	// 获取用户ID
	id, _ := c.GetId(ctx)
	if id == "" {
		return c.BadRequest(ctx, "用户ID不能为空", nil)
	}

	// 查询用户
	user, err := c.UserService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取用户详情失败: "+err.Error())
	}

	if user == nil {
		return c.NotFound(ctx, "用户不存在")
	}

	// 返回用户详情
	return c.Success(ctx, user)
}

// CreateUser 创建用户
func (c *UserController) CreateUser(ctx *fiber.Ctx) error {
	var req userValidator.CreateUserRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 创建用户对象，手动设置null.String字段
	var user userModel.User

	// 手动设置null.String字段
	if req.Username != "" {
		user.Username = null.StringFrom(req.Username)
	}
	if req.Nickname != "" {
		user.Nickname = null.StringFrom(req.Nickname)
	}
	if req.Email != "" {
		user.Email = null.StringFrom(req.Email)
	}
	if req.Avatar != "" {
		user.Avatar = null.StringFrom(req.Avatar)
	}
	if req.Bio != "" {
		user.Bio = null.StringFrom(req.Bio)
	}
	if req.PhoneNumber != "" {
		user.Phone = null.StringFrom(req.PhoneNumber)
	}

	// 设置其他字段
	user.Password = req.Password
	user.Gender = req.Gender

	// 保存用户
	userId, err1 := c.UserService.Create(ctx.Context(), &user)
	if err1 != nil {
		return c.InternalServerError(ctx, "创建用户失败: "+err1.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      userId,
		"message": "创建用户成功",
	})
}

// UpdateUser 更新用户
func (c *UserController) UpdateUser(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req userValidator.UpdateUserRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 获取原有用户信息
	existingUser, err := c.UserService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取用户信息失败: "+err.Error())
	}
	if existingUser == nil {
		return c.NotFound(ctx, "用户不存在")
	}

	// 创建用户更新对象，只更新非空字段
	utils.SmartCopy(req, existingUser)
	existingUser.ID = id

	// 手动设置null.String字段，只有当请求中字段不为空时才更新
	// if req.Username != "" {
	// 	existingUser.Username = null.StringFrom(req.Username) // 保持原有用户名，因为用户名通常不允许修改
	// }
	if req.Nickname != "" {
		existingUser.Nickname = null.StringFrom(req.Nickname)
	}
	if req.Avatar != "" {
		existingUser.Avatar = null.StringFrom(req.Avatar)
	}
	if req.Bio != "" {
		existingUser.Bio = null.StringFrom(req.Bio)
	}

	// 设置其他字段
	existingUser.Gender = req.Gender
	existingUser.Status = int8(req.Status)

	// 保存用户
	err = c.UserService.UpdateById(ctx.Context(), id, existingUser)
	if err != nil {
		return c.InternalServerError(ctx, "更新用户失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新用户成功")
}

// 更新用户状态
func (c *UserController) UpdateUserStatus(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}
	var req userValidator.UpdateUserStatusRequest
	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	err = c.UserService.UpdateStatus(ctx.Context(), id, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新用户状态失败: "+err.Error())
	}
	return c.SuccessWithMessage(ctx, "更新用户状态成功")
}

// 批量更新用户状态
func (c *UserController) BatchUpdateUserStatus(ctx *fiber.Ctx) error {
	var req userValidator.BatchUpdateUserStatusRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	err := c.UserService.BatchUpdateStatus(ctx.Context(), req.Ids, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "批量更新用户状态失败: "+err.Error())
	}
	return c.SuccessWithMessage(ctx, "批量更新用户状态成功")
}

// DeleteUser 删除用户
func (c *UserController) DeleteUser(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}
	// 查询用户
	user, err := c.UserService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取用户详情失败: "+err.Error())
	}

	if user == nil {
		return c.NotFound(ctx, "用户不存在")
	}

	// 删除用户
	err = c.UserService.SoftDelete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除用户失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除用户成功")
}

// 批量删除用户
// BatchDeleteUser 批量删除用户
func (c *UserController) BatchDeleteUser(ctx *fiber.Ctx) error {
	var req userValidator.BatchDeleteUserRequest
	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 调用用户服务删除用户
	err := c.UserService.BatchSoftDelete(ctx.Context(), req.Ids)
	if err != nil {
		return c.InternalServerError(ctx, "批量删除用户失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量删除用户成功")
}
