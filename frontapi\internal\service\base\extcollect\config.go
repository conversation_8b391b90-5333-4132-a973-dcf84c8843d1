package extcollect

import (
	"time"

	goredis "github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"

	mongoConfig "frontapi/pkg/mongodb"
)

// StorageStrategy 存储策略枚举
type StorageStrategy string

const (
	RedisOnly  StorageStrategy = "redis_only"  // 仅使用Redis
	MongoOnly  StorageStrategy = "mongo_only"  // 仅使用MongoDB
	RedisFirst StorageStrategy = "redis_first" // Redis优先，MongoDB备份
	MongoFirst StorageStrategy = "mongo_first" // MongoDB优先，Redis缓存
	DualWrite  StorageStrategy = "dual_write"  // 双写模式
)

// Config 收藏服务配置
type Config struct {
	// 基础配置
	ServiceName string          `json:"service_name" yaml:"service_name"`
	Strategy    StorageStrategy `json:"strategy" yaml:"strategy"`
	Debug       bool            `json:"debug" yaml:"debug"`

	// Redis配置
	Redis struct {
		Enabled   bool                    `json:"enabled" yaml:"enabled"`
		UseSystem bool                    `json:"use_system" yaml:"use_system"` // 是否使用系统Redis配置
		Client    goredis.UniversalClient `json:"-" yaml:"-"`                   // 系统Redis客户端
		Config    *RedisConfig            `json:"config,omitempty" yaml:"config,omitempty"`
	} `json:"redis" yaml:"redis"`

	// MongoDB配置
	MongoDB struct {
		Enabled   bool                `json:"enabled" yaml:"enabled"`
		UseSystem bool                `json:"use_system" yaml:"use_system"` // 是否使用系统MongoDB配置
		Client    *mongo.Client       `json:"-" yaml:"-"`                   // 系统MongoDB客户端
		Database  *mongo.Database     `json:"-" yaml:"-"`                   // 系统MongoDB数据库
		Config    *mongoConfig.Config `json:"config,omitempty" yaml:"config,omitempty"`
	} `json:"mongodb" yaml:"mongodb"`

	// 性能配置
	Performance struct {
		BatchSize       int           `json:"batch_size" yaml:"batch_size"`
		SyncInterval    time.Duration `json:"sync_interval" yaml:"sync_interval"`
		CacheExpiration time.Duration `json:"cache_expiration" yaml:"cache_expiration"`
		MaxRetries      int           `json:"max_retries" yaml:"max_retries"`
		RetryDelay      time.Duration `json:"retry_delay" yaml:"retry_delay"`
		MaxConcurrency  int           `json:"max_concurrency" yaml:"max_concurrency"`
		TimeoutDuration time.Duration `json:"timeout_duration" yaml:"timeout_duration"`
	} `json:"performance" yaml:"performance"`

	// 监控配置
	Monitoring struct {
		Enabled            bool          `json:"enabled" yaml:"enabled"`
		MetricsInterval    time.Duration `json:"metrics_interval" yaml:"metrics_interval"`
		HealthCheckPeriod  time.Duration `json:"health_check_period" yaml:"health_check_period"`
		LogSlowQueries     bool          `json:"log_slow_queries" yaml:"log_slow_queries"`
		SlowQueryThreshold time.Duration `json:"slow_query_threshold" yaml:"slow_query_threshold"`
	} `json:"monitoring" yaml:"monitoring"`
}

// RedisConfig Redis配置结构体
type RedisConfig struct {
	// 启用状态
	Enabled   bool `json:"enabled" yaml:"enabled"`
	UseCustom bool `json:"use_custom" yaml:"use_custom"`

	// 基础连接配置
	Host         string   `json:"host" yaml:"host"`
	Port         int      `json:"port" yaml:"port"`
	Addrs        []string `json:"addrs" yaml:"addrs"`
	Password     string   `json:"password" yaml:"password"`
	DB           int      `json:"db" yaml:"db"`
	Username     string   `json:"username" yaml:"username"`
	Cluster      bool     `json:"cluster" yaml:"cluster"`
	ClusterAddrs []string `json:"cluster_addrs" yaml:"cluster_addrs"`

	// 连接池配置
	PoolSize     int           `json:"pool_size" yaml:"pool_size"`
	MinIdleConns int           `json:"min_idle_conns" yaml:"min_idle_conns"`
	MaxConnAge   time.Duration `json:"max_conn_age" yaml:"max_conn_age"`
	PoolTimeout  time.Duration `json:"pool_timeout" yaml:"pool_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout" yaml:"idle_timeout"`

	// 网络配置
	DialTimeout  time.Duration `json:"dial_timeout" yaml:"dial_timeout"`
	ReadTimeout  time.Duration `json:"read_timeout" yaml:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout" yaml:"write_timeout"`

	// 重试配置
	MaxRetries      int           `json:"max_retries" yaml:"max_retries"`
	MinRetryBackoff time.Duration `json:"min_retry_backoff" yaml:"min_retry_backoff"`
	MaxRetryBackoff time.Duration `json:"max_retry_backoff" yaml:"max_retry_backoff"`

	// 缓存配置
	KeyPrefix       string        `json:"key_prefix" yaml:"key_prefix"`
	DefaultTTL      time.Duration `json:"default_ttl" yaml:"default_ttl"`
	CollectTTL      time.Duration `json:"collect_ttl" yaml:"collect_ttl"`
	CountTTL        time.Duration `json:"count_ttl" yaml:"count_ttl"`
	RankingTTL      time.Duration `json:"ranking_ttl" yaml:"ranking_ttl"`
	CleanupInterval time.Duration `json:"cleanup_interval" yaml:"cleanup_interval"`

	// 集群配置
	MasterName       string   `json:"master_name" yaml:"master_name"`
	SentinelAddrs    []string `json:"sentinel_addrs" yaml:"sentinel_addrs"`
	SentinelPassword string   `json:"sentinel_password" yaml:"sentinel_password"`

	// TLS配置
	TLSConfig struct {
		Enabled    bool   `json:"enabled" yaml:"enabled"`
		CertFile   string `json:"cert_file" yaml:"cert_file"`
		KeyFile    string `json:"key_file" yaml:"key_file"`
		CAFile     string `json:"ca_file" yaml:"ca_file"`
		SkipVerify bool   `json:"skip_verify" yaml:"skip_verify"`
	} `json:"tls_config" yaml:"tls_config"`
}

// MongoConfig MongoDB配置结构体
type MongoConfig struct {
	// 启用状态
	Enabled   bool `json:"enabled" yaml:"enabled"`
	UseCustom bool `json:"use_custom" yaml:"use_custom"`

	// 连接配置
	URI      string `json:"uri" yaml:"uri"`
	Database string `json:"database" yaml:"database"`
	Username string `json:"username" yaml:"username"`
	Password string `json:"password" yaml:"password"`

	// 集合配置
	Collection string `json:"collection" yaml:"collection"`

	// 连接池配置
	MaxPoolSize    uint64        `json:"max_pool_size" yaml:"max_pool_size"`
	MinPoolSize    uint64        `json:"min_pool_size" yaml:"min_pool_size"`
	MaxIdleTime    time.Duration `json:"max_idle_time" yaml:"max_idle_time"`
	ConnectTimeout time.Duration `json:"connect_timeout" yaml:"connect_timeout"`
	SocketTimeout  time.Duration `json:"socket_timeout" yaml:"socket_timeout"`

	// 写入配置
	WriteConcern struct {
		W        int           `json:"w" yaml:"w"`
		J        bool          `json:"j" yaml:"j"`
		WTimeout time.Duration `json:"w_timeout" yaml:"w_timeout"`
	} `json:"write_concern" yaml:"write_concern"`

	// 读取配置
	ReadPreference string `json:"read_preference" yaml:"read_preference"`
	ReadConcern    string `json:"read_concern" yaml:"read_concern"`
}

// ServiceConfig 服务配置结构体
type ServiceConfig struct {
	ServiceName   string        `json:"service_name" yaml:"service_name"`
	Version       string        `json:"version" yaml:"version"`
	Debug         bool          `json:"debug" yaml:"debug"`
	LogLevel      string        `json:"log_level" yaml:"log_level"`
	EnableMetrics bool          `json:"enable_metrics" yaml:"enable_metrics"`
	MetricsPort   int           `json:"metrics_port" yaml:"metrics_port"`
	HealthCheck   bool          `json:"health_check" yaml:"health_check"`
	ShutdownGrace time.Duration `json:"shutdown_grace" yaml:"shutdown_grace"`
}

// PerformanceConfig 性能配置结构体
type PerformanceConfig struct {
	BatchSize        int           `json:"batch_size" yaml:"batch_size"`
	SyncInterval     time.Duration `json:"sync_interval" yaml:"sync_interval"`
	CacheExpiration  time.Duration `json:"cache_expiration" yaml:"cache_expiration"`
	MaxRetries       int           `json:"max_retries" yaml:"max_retries"`
	RetryDelay       time.Duration `json:"retry_delay" yaml:"retry_delay"`
	MaxConcurrency   int           `json:"max_concurrency" yaml:"max_concurrency"`
	TimeoutDuration  time.Duration `json:"timeout_duration" yaml:"timeout_duration"`
	EnablePipelining bool          `json:"enable_pipelining" yaml:"enable_pipelining"`
	PipelineSize     int           `json:"pipeline_size" yaml:"pipeline_size"`
	CircuitBreaker   bool          `json:"circuit_breaker" yaml:"circuit_breaker"`
	RateLimiting     bool          `json:"rate_limiting" yaml:"rate_limiting"`
	RateLimit        int           `json:"rate_limit" yaml:"rate_limit"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		ServiceName: "extcollect-service",
		Strategy:    RedisFirst,
		Debug:       false,
		Redis: struct {
			Enabled   bool                    `json:"enabled" yaml:"enabled"`
			UseSystem bool                    `json:"use_system" yaml:"use_system"`
			Client    goredis.UniversalClient `json:"-" yaml:"-"`
			Config    *RedisConfig            `json:"config,omitempty" yaml:"config,omitempty"`
		}{
			Enabled:   true,
			UseSystem: true,
			Config:    DefaultRedisConfig(),
		},
		MongoDB: struct {
			Enabled   bool                `json:"enabled" yaml:"enabled"`
			UseSystem bool                `json:"use_system" yaml:"use_system"`
			Client    *mongo.Client       `json:"-" yaml:"-"`
			Database  *mongo.Database     `json:"-" yaml:"-"`
			Config    *mongoConfig.Config `json:"config,omitempty" yaml:"config,omitempty"`
		}{
			Enabled:   false,
			UseSystem: true,
			Config:    DefaultMongoConfig(),
		},
		Performance: struct {
			BatchSize       int           `json:"batch_size" yaml:"batch_size"`
			SyncInterval    time.Duration `json:"sync_interval" yaml:"sync_interval"`
			CacheExpiration time.Duration `json:"cache_expiration" yaml:"cache_expiration"`
			MaxRetries      int           `json:"max_retries" yaml:"max_retries"`
			RetryDelay      time.Duration `json:"retry_delay" yaml:"retry_delay"`
			MaxConcurrency  int           `json:"max_concurrency" yaml:"max_concurrency"`
			TimeoutDuration time.Duration `json:"timeout_duration" yaml:"timeout_duration"`
		}{
			BatchSize:       100,
			SyncInterval:    5 * time.Minute,
			CacheExpiration: 1 * time.Hour,
			MaxRetries:      3,
			RetryDelay:      1 * time.Second,
			MaxConcurrency:  10,
			TimeoutDuration: 30 * time.Second,
		},
		Monitoring: struct {
			Enabled            bool          `json:"enabled" yaml:"enabled"`
			MetricsInterval    time.Duration `json:"metrics_interval" yaml:"metrics_interval"`
			HealthCheckPeriod  time.Duration `json:"health_check_period" yaml:"health_check_period"`
			LogSlowQueries     bool          `json:"log_slow_queries" yaml:"log_slow_queries"`
			SlowQueryThreshold time.Duration `json:"slow_query_threshold" yaml:"slow_query_threshold"`
		}{
			Enabled:            true,
			MetricsInterval:    30 * time.Second,
			HealthCheckPeriod:  1 * time.Minute,
			LogSlowQueries:     true,
			SlowQueryThreshold: 100 * time.Millisecond,
		},
	}
}

// DefaultRedisConfig 返回默认Redis配置
func DefaultRedisConfig() *RedisConfig {
	return &RedisConfig{
		Enabled:   true,
		UseCustom: false,
		Host:      "localhost",
		Port:      6379,
		Password:  "",
		DB:        1, // 使用DB1避免与其他服务冲突
		Username:  "",
		Cluster:   false,

		// 连接池配置
		PoolSize:     10,
		MinIdleConns: 5,
		MaxConnAge:   30 * time.Minute,
		PoolTimeout:  5 * time.Second,
		IdleTimeout:  10 * time.Minute,

		// 网络配置
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,

		// 重试配置
		MaxRetries:      3,
		MinRetryBackoff: 8 * time.Millisecond,
		MaxRetryBackoff: 512 * time.Millisecond,

		// 缓存配置
		KeyPrefix:       "collect:",
		DefaultTTL:      24 * time.Hour,
		CollectTTL:      7 * 24 * time.Hour, // 收藏记录保存7天
		CountTTL:        1 * time.Hour,      // 计数缓存1小时
		RankingTTL:      30 * time.Minute,   // 排行榜缓存30分钟
		CleanupInterval: 10 * time.Minute,   // 清理间隔10分钟

		// 集群配置
		MasterName:       "",
		SentinelAddrs:    []string{},
		SentinelPassword: "",

		// TLS配置
		TLSConfig: struct {
			Enabled    bool   `json:"enabled" yaml:"enabled"`
			CertFile   string `json:"cert_file" yaml:"cert_file"`
			KeyFile    string `json:"key_file" yaml:"key_file"`
			CAFile     string `json:"ca_file" yaml:"ca_file"`
			SkipVerify bool   `json:"skip_verify" yaml:"skip_verify"`
		}{
			Enabled:    false,
			CertFile:   "",
			KeyFile:    "",
			CAFile:     "",
			SkipVerify: false,
		},
	}
}

// DefaultMongoConfig 返回默认MongoDB配置
func DefaultMongoConfig() *mongoConfig.Config {
	return &mongoConfig.Config{
		URI:            "mongodb://localhost:27017",
		Database:       "frontapi_collect",
		ConnectTimeout: 10 * time.Second,
		MaxPoolSize:    100,
		MinPoolSize:    5,
		MaxIdleTime:    5 * time.Minute,
	}
}

// DefaultServiceConfig 返回默认服务配置
func DefaultServiceConfig() *ServiceConfig {
	return &ServiceConfig{
		ServiceName:   "extcollect-service",
		Version:       "1.0.0",
		Debug:         false,
		LogLevel:      "info",
		EnableMetrics: true,
		MetricsPort:   9091,
		HealthCheck:   true,
		ShutdownGrace: 30 * time.Second,
	}
}

// DefaultPerformanceConfig 返回默认性能配置
func DefaultPerformanceConfig() *PerformanceConfig {
	return &PerformanceConfig{
		BatchSize:        100,
		SyncInterval:     5 * time.Minute,
		CacheExpiration:  1 * time.Hour,
		MaxRetries:       3,
		RetryDelay:       1 * time.Second,
		MaxConcurrency:   10,
		TimeoutDuration:  30 * time.Second,
		EnablePipelining: true,
		PipelineSize:     50,
		CircuitBreaker:   true,
		RateLimiting:     true,
		RateLimit:        1000, // 每秒1000次操作
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.ServiceName == "" {
		c.ServiceName = "extcollect-service"
	}

	if c.Strategy == "" {
		c.Strategy = RedisFirst
	}

	// 验证性能配置
	if c.Performance.BatchSize <= 0 {
		c.Performance.BatchSize = 100
	}
	if c.Performance.MaxConcurrency <= 0 {
		c.Performance.MaxConcurrency = 10
	}
	if c.Performance.MaxRetries < 0 {
		c.Performance.MaxRetries = 3
	}

	return nil
}

// Clone 克隆配置
func (c *Config) Clone() *Config {
	clone := *c
	return &clone
}
