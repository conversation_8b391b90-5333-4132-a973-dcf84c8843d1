/* Slinky主题 - 表格样式 */

.slinky-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    background: #fff;

    .el-table {
        border: none;

        &::before {
            display: none; // 移除element-plus默认边框线
        }
    }

    // 表头样式
    .el-table__header-wrapper {
        .el-table__header {
            th {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                font-weight: 600;
                padding: 16px 12px;

                .cell {
                    font-size: 14px;
                    letter-spacing: 0.5px;
                }
            }
        }
    }

    // 表格主体
    .el-table__body-wrapper {
        .el-table__body {
            tr {
                transition: all 0.3s ease;

                &:hover {
                    background: linear-gradient(90deg, #f8f9ff 0%, #fff 100%);
                    transform: translateX(2px);
                    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
                }

                td {
                    border-bottom: 1px solid #f0f2f5;
                    padding: 16px 12px;

                    .cell {
                        color: #333;
                        font-size: 14px;
                        line-height: 1.6;
                    }
                }
            }
        }
    }

    // 固定列阴影
    .el-table__fixed-right {
        box-shadow: -4px 0 12px rgba(0, 0, 0, 0.08);
    }

    .el-table__fixed {
        box-shadow: 4px 0 12px rgba(0, 0, 0, 0.08);
    }

    // 空数据状态
    .el-table__empty-block {
        background: #fafbfc;
        min-height: 200px;

        .el-table__empty-text {
            color: #999;
            font-size: 16px;
        }
    }
}

// 表格操作按钮样式
.slinky-actions {
    display: flex;
    gap: 4px;
    padding: 2px 4px;
    justify-content: center;
    align-items: center;

    .action-buttons-group {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 4px;
        padding: 2px 4px;
    }

    .el-button {
        border-radius: 6px;
        font-size: 12px;
        padding: 4px 8px;
        transition: all 0.3s ease;

        &.el-button--primary {
            border: none;
        }

        &.el-button--danger {
            border: none;
        }

        &.el-button--success {
            border: none;
        }

        &.is-link {
            color: #667eea;
            padding: 4px 8px;
        }
    }
}

// 状态标签样式
.slinky-tag {
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 12px;
    border: none;

    &.el-tag--success {
        background: linear-gradient(45deg, #51cf66, #40c057);
        color: #fff;
    }

    &.el-tag--danger {
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: #fff;
    }

    &.el-tag--warning {
        background: linear-gradient(45deg, #ffd43b, #fab005);
        color: #fff;
    }

    &.el-tag--info {
        background: linear-gradient(45deg, #74c0fc, #339af0);
        color: #fff;
    }
}