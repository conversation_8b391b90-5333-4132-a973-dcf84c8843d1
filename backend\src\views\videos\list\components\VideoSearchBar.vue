<template>
  <div class="video-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item :label="$t('videos.info.name')">
        <el-input 
          v-model="searchForm.title" 
          :placeholder="$t('videos.info.name')" 
          clearable 
          style="width: 200px"
          @keyup.enter="handleSearch"
        ></el-input>
      </el-form-item>
      
      <el-form-item :label="$t('videos.info.category')">
        <el-select 
          v-model="searchForm.categoryId" 
          filterable 
          remote 
          reserve-keyword 
          :placeholder="$t('videos.info.category')" 
          :remote-method="handleSearchCategories" 
          :loading="categoriesLoading" 
          clearable 
          style="width: 200px;"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item :label="$t('videos.info.channel')">
        <el-select 
          v-model="searchForm.channelId" 
          filterable 
          remote 
          reserve-keyword 
          :placeholder="$t('videos.info.channel')" 
          :remote-method="handleSearchChannels" 
          :loading="channelsLoading" 
          clearable 
          style="width: 200px"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="创建者">
        <el-select 
          v-model="searchForm.creatorId" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请搜索创建者" 
          :remote-method="handleSearchCreators" 
          :loading="creatorsLoading" 
          clearable 
          style="width: 200px"
        >
          <el-option 
            v-for="creator in creatorsOptions" 
            :key="creator.id"
            :label="creator.nickname || creator.username" 
            :value="creator.id"
          >
            <div style="display: flex; align-items: center;">
              <el-avatar :size="20" :src="creator.avatar" style="margin-right: 6px;">
                {{ (creator.nickname || creator.username).charAt(0) }}
              </el-avatar>
              <span>{{ creator.nickname || creator.username }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item :label="$t('common.status.label')">
        <el-select v-model="searchForm.status" :placeholder="$t('common.status.all')" clearable style="width:200px">
            <!-- 状态：0-待审核,1-已下架,2-已发布,-2-已拒绝,-4-已删除 -->
            <el-option :label="$t('common.status.all')" value=""></el-option>
          <el-option :label="$t('common.status.pending')" value="0"></el-option>
          <el-option :label="$t('common.status.unpublished')" value="1"></el-option>
          <el-option :label="$t('common.status.published')" value="2"></el-option>
          <el-option :label="$t('common.status.rejected')" value="-2"></el-option>
          <el-option :label="$t('common.status.deleted')" value="-4"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          {{ $t('buttons.search') }}
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          {{ $t('buttons.reset') }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { searchChannels as searchChannelsAPI, searchUsers, searchVideoCategories } from '@/service/api/videos/videos';
import { Refresh, Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const emit = defineEmits(['search', 'reset', 'refresh']);

// 搜索表单数据
const searchForm = reactive({
  title: '',
  categoryId: '',
  channelId: '',
  creatorId: '',
  status: ''
});

// 下拉选项
const categoryOptions = ref<Array<{value: string, label: string}>>([]);
const channelOptions = ref<Array<{value: string, label: string}>>([]);
const creatorsOptions = ref<any[]>([]);

// 加载状态
const categoriesLoading = ref(false);
const channelsLoading = ref(false);
const creatorsLoading = ref(false);

// 生命周期钩子
onMounted(() => {
  loadDefaultOptions();
});

// 加载默认选项
const loadDefaultOptions = async () => {
  // 加载默认分类选项
  try {
    const categoryResult = await searchVideoCategories({
      page: { pageNo: 1, pageSize: 10 },
      data: { status: 1 }
    });
    if (categoryResult?.data?.list) {
      categoryOptions.value = categoryResult.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }
  } catch (error) {
    console.error('加载默认分类失败:', error);
  }

  // 加载默认频道选项
  try {
    const channelResult = await searchChannelsAPI({
      page: { pageNo: 1, pageSize: 10 },
      data: { status: 1 }
    });
    if (channelResult?.data?.list) {
      channelOptions.value = channelResult.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }
  } catch (error) {
    console.error('加载默认频道失败:', error);
  }
};

// 搜索分类
const handleSearchCategories = async (query: string) => {
  categoriesLoading.value = true;
  try {
    const params: any = {
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    };
    
    if (query) {
      params.data.keyword = query;
    }
    
    const result = await searchVideoCategories(params);
    if (result?.data?.list) {
      categoryOptions.value = result.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    } else {
      categoryOptions.value = [];
    }
  } catch (error) {
    console.error('搜索分类失败:', error);
    ElMessage.error('搜索分类失败');
    categoryOptions.value = [];
  } finally {
    categoriesLoading.value = false;
  }
};

// 搜索频道
const handleSearchChannels = async (query: string) => {
  channelsLoading.value = true;
  try {
    const params: any = {
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    };
    
    if (query) {
      params.data.keyword = query;
    }
    
    const result = await searchChannelsAPI(params);
    if (result?.data?.list) {
      channelOptions.value = result.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    } else {
      channelOptions.value = [];
    }
  } catch (error) {
    console.error('搜索频道失败:', error);
    ElMessage.error('搜索频道失败');
    channelOptions.value = [];
  } finally {
    channelsLoading.value = false;
  }
};

// 搜索创建者
const handleSearchCreators = async (query: string) => {
  if (query) {
    creatorsLoading.value = true;
    try {
      const result = await searchUsers({
        page: { pageNo: 1, pageSize: 20 },
        data: { keyword: query, status: 1 }
      });

      if (result?.data?.list) {
        creatorsOptions.value = result.data.list;
      } else {
        creatorsOptions.value = [];
      }
    } catch (error) {
      console.error('搜索创建者失败:', error);
      ElMessage.error('搜索创建者失败');
      creatorsOptions.value = [];
    } finally {
      creatorsLoading.value = false;
    }
  } else {
    creatorsOptions.value = [];
  }
};

// 处理搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 处理重置
const handleReset = () => {
  // 重置表单数据
  Object.keys(searchForm).forEach(key => {
    searchForm[key as keyof typeof searchForm] = '';
  });
  emit('reset');
};

// 处理刷新
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped lang="scss">
.video-search-bar {
  margin-bottom: 16px;
  
  .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}
</style> 