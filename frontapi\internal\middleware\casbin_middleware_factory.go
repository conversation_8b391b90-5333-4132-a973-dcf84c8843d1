package middleware

import (
	"github.com/gofiber/fiber/v2"
	"frontapi/internal/service/permission"
)

// CasbinMiddlewareFactory Casbin中间件工厂
type CasbinMiddlewareFactory struct {
	casbinService permission.CasbinService
}

// NewCasbinMiddlewareFactory 创建Casbin中间件工厂
func NewCasbinMiddlewareFactory(casbinService permission.CasbinService) *CasbinMiddlewareFactory {
	return &CasbinMiddlewareFactory{
		casbinService: casbinService,
	}
}

// CasbinAuth 返回Casbin权限验证中间件
func (f *CasbinMiddlewareFactory) CasbinAuth() fiber.Handler {
	return CasbinAuth(f.casbinService)
}

// AdminAuth 返回管理员权限验证中间件
func (f *CasbinMiddlewareFactory) AdminAuth() fiber.Handler {
	return AdminAuth(f.casbinService)
}

// CasbinAuthWithRole 返回基于角色的Casbin权限验证中间件
func (f *CasbinMiddlewareFactory) CasbinAuthWithRole(requiredRole string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 先执行基本的Casbin权限验证
		if err := CasbinAuth(f.casbinService)(c); err != nil {
			return err
		}

		// 获取用户ID
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    4001,
				"message": "未登录",
				"data":    nil,
			})
		}

		// 检查用户是否具有指定角色
		roles, err := f.casbinService.GetRolesForUser(c.Context(), userID.(string))
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    5000,
				"message": "权限验证失败",
				"data":    nil,
			})
		}

		hasRole := false
		for _, role := range roles {
			if role == requiredRole {
				hasRole = true
				break
			}
		}

		if !hasRole {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code":    4003,
				"message": "需要 " + requiredRole + " 角色权限",
				"data":    nil,
			})
		}

		return c.Next()
	}
}