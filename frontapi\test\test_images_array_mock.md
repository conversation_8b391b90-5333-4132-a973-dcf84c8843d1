# Images字段Mock数据生成测试

## 测试目的
验证修复后的Mock系统能否正确生成images字段的图片数组数据。

## 测试场景

### 场景1：自动识别images字段
**表结构**：
```sql
CREATE TABLE test_posts (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    images JSON COMMENT '帖子图片'
);
```

**预期结果**：
- images字段自动识别为图片数组类型
- 生成的Mock规则为：`GENERATE_IMAGE_ARRAY`
- 生成的数据格式：`["https://picsum.photos/800/600?random=123", "https://picsum.photos/400/300?random=456"]`

### 场景2：手动选择图片数组规则
**操作步骤**：
1. 在Mock规则配置中选择images字段
2. 在下拉菜单中选择"网络相关" > "图片数组 (JSON)"
3. 生成Mock数据

**预期结果**：
- 规则设置为：`GENERATE_IMAGE_ARRAY`
- 生成实际的图片URL数组而非函数字符串

### 场景3：向后兼容测试
**操作步骤**：
1. 手动输入旧版本的规则：`() => generateImageArray()`
2. 生成Mock数据

**预期结果**：
- 系统能识别并正确处理旧格式
- 生成正确的图片数组数据

## 修复前后对比

### 修复前（错误）
```json
{
  "id": "123",
  "title": "测试标题",
  "images": "() => generateImageArray()"
}
```

### 修复后（正确）
```json
{
  "id": "123",
  "title": "测试标题", 
  "images": "[\"https://picsum.photos/800/600?random=234\", \"https://picsum.photos/1200/400?random=567\"]"
}
```

## 测试步骤

1. **创建测试表**：使用包含images字段的表结构
2. **智能识别测试**：检查系统是否自动为images字段应用正确规则
3. **数据生成测试**：生成Mock数据并验证images字段内容
4. **数据库插入测试**：确认生成的SQL语句正确且可执行
5. **JSON解析测试**：验证生成的JSON字符串可以被正确解析为数组

## 验证要点

- [ ] images字段自动识别为图片数组类型
- [ ] 生成的数据为JSON字符串格式的数组
- [ ] 数组包含1-5个图片URL
- [ ] 图片URL格式正确且可访问
- [ ] 生成的SQL语句可以正常插入数据库
- [ ] 向后兼容旧版本规则格式
- [ ] 手动选择图片数组规则正常工作

## 回归测试

确保修复不影响其他功能：
- [ ] 其他字段的Mock规则正常工作
- [ ] @image规则（单图片）仍正常工作
- [ ] 自定义函数规则正常工作
- [ ] Mock.js原生规则正常工作 