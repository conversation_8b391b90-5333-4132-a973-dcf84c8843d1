package comics

import (
	"frontapi/internal/models/comics"
	repo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
)

// ComicCommentService 漫画评论服务接口
type ComicCommentService interface {
	base.IExtendedService[comics.ComicComment]
}

// comicCommentService 漫画评论服务实现
type comicCommentService struct {
	*base.ExtendedService[comics.ComicComment]
	commentRepo repo.ComicCommentRepository
	comicRepo   repo.ComicRepository
}

// NewComicCommentService 创建漫画评论服务实例
func NewComicCommentService(
	commentRepo repo.ComicCommentRepository,
	comicRepo repo.ComicRepository,
) ComicCommentService {
	return &comicCommentService{
		ExtendedService: base.NewExtendedService[comics.ComicComment](commentRepo, "comic_comment"),
		commentRepo:     commentRepo,
		comicRepo:       comicRepo,
	}
}
