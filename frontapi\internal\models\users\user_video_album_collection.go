package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// UserVideoAlbumCollection 用户视频专辑收藏表
type UserVideoAlbumCollection struct {
	models.BaseModel
	UserID          string         `json:"userId" gorm:"type:string;index;not null;comment:用户ID"`  //用户ID
	AlbumID         string         `json:"albumId" gorm:"type:string;index;not null;comment:视频ID"` //视频ID
	AlbumTitle      string         `json:"albumTitle" gorm:"type:string;comment:视频标题"`             //视频标题
	AlbumCover      string         `json:"albumCover" gorm:"type:string;comment:视频封面"`             //视频封面
	CreatorID       string         `json:"creatorId" gorm:"type:string;comment:创作者ID"`             //创作者ID
	CreatorName     string         `json:"creatorName" gorm:"type:string;comment:创作者名称"`           //创作者名称
	CreatorAvatar   string         `json:"creatorAvatar" gorm:"type:string;comment:创作者头像"`         //创作者头像
	CollectionTime  types.JSONTime `json:"collectionTime" gorm:"type:datetime;comment:收藏时间"`       //收藏时间
	CollectionGroup string         `json:"collectionGroup" gorm:"type:string;comment:收藏分组"`        //收藏分组
	Note            string         `json:"note" gorm:"type:string;comment:收藏备注"`                   //收藏备注
	Status          int            `json:"status" gorm:"type:int;default:1;comment:状态"`            //状态
}

// TableName 表名
func (UserVideoAlbumCollection) TableName() string {
	return "ly_user_video_album_collections"
}
