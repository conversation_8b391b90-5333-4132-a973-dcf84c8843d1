import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from "@/locales";

const routes: GeneratedRoute[] = [
  {
    name: 'books',
    path: '/books',
    component: 'layout.base',
    meta: {
      title: 'books',
      i18nKey: 'route.books',
      icon: 'lucide:book-open',
      order: 8
    },
    children: [
      // 电子书分类管理
      {
        name: 'books_category',
        path: '/books/category',
        component: 'view.books_category',
        meta: {
          title: 'books_category',
          i18nKey: 'route.books_category',
          icon: 'lucide:folder',
          order:1
        }
      },
      // 电子书列表页面
      {
        name: 'books_list',
        path: '/books/list',
        component: 'view.books_list',
        meta: {
          title: 'books_list',
          i18nKey: 'route.books_list',
          icon: 'lucide:list',
          order:2
        }
      },
      // 电子书章节管理
      {
        name: 'books_chapter',
        path: '/books/chapter',
        component: 'view.books_chapter',
        meta: {
          title: 'books_chapter',
          i18nKey: 'route.books_chapter',
          icon: 'lucide:file-text',
          hideInMenu: true,
          order:3,
          query: {
            bookId: {
              type: 'string',
              required: true
            },
            bookName: {
              type: 'string',
              required: true
            }
          }
        }
      
      }
    ]
  }
];

export default routes;
