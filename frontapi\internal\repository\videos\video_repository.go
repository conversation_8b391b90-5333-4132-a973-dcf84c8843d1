package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// VideoRepository 视频仓库接口
type VideoRepository interface {
	base.ExtendedRepository[videos.Video]
	// 业务特定方法
	SearchVideos(ctx context.Context, keyword string, page, pageSize int) ([]*videos.Video, int64, error)
}

// videoRepository 视频仓库实现
type videoRepository struct {
	base.ExtendedRepository[videos.Video]
}

// NewVideoRepository 创建视频仓库实例
func NewVideoRepository(db *gorm.DB) VideoRepository {
	return &videoRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.Video](db),
	}
}



// SearchVideos 搜索视频
func (r *videoRepository) SearchVideos(ctx context.Context, keyword string, page, pageSize int) ([]*videos.Video, int64, error) {
	if keyword == "" {
		condition := map[string]interface{}{}
		return r.List(ctx, condition, "created_at DESC", page, pageSize)
	}
	
	// 使用模糊搜索
	query := "%" + keyword + "%"
	condition := map[string]interface{}{
		"title LIKE ? OR description LIKE ?": []interface{}{query, query},
	}
	return r.List(ctx, condition, "created_at DESC", page, pageSize)
}
