package types

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// ImagesJSON 用于存储JSON格式的图片数据
type StringArray []string

// Value 实现 driver.Valuer 接口
func (t StringArray) Value() (driver.Value, error) {
	// 即使是空数组也要序列化为JSON，而不是返回nil
	// 这样可以确保空数组也会被写入数据库
	b, err := json.Marshal(t)
	return string(b), err
}

// Scan 实现sql.Scanner接口，将数据库类型转换为Go类型
func (t *StringArray) Scan(value interface{}) error {
	if value == nil {
		*t = StringArray{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("无效的标签数据类型")
	}

	return json.Unmarshal(bytes, t)
}
