package books

import (
	"context"

	"gorm.io/gorm"

	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"
)

// LikeRepository 电子书点赞数据访问接口
type LikeRepository interface {
	base.ExtendedRepository[books.BookLike]
	CheckUserLiked(ctx context.Context, userID, bookID string) (bool, error)
	FindByUserAndBook(ctx context.Context, userID, bookID string) (*books.BookLike, error)
}

// likeRepository 电子书点赞数据访问实现
type likeRepository struct {
	base.ExtendedRepository[books.BookLike]
}

// NewLikeRepository 创建电子书点赞仓库实例
func NewLikeRepository(db *gorm.DB) LikeRepository {
	return &likeRepository{
		ExtendedRepository: base.NewExtendedRepository[books.BookLike](db),
	}
}

// CheckUserLiked 检查用户是否点赞电子书
func (r likeRepository) CheckUserLiked(ctx context.Context, userID, bookID string) (bool, error) {
	var count int64
	err := r.GetDB().WithContext(ctx).
		Model(&books.BookLike{}).
		Where("user_id = ? AND book_id = ? AND status = 1", userID, bookID).
		Count(&count).Error
	return count > 0, err
}

// FindByUserAndBook 根据用户ID和电子书ID查找点赞记录
func (r likeRepository) FindByUserAndBook(ctx context.Context, userID, bookID string) (*books.BookLike, error) {
	var like books.BookLike
	err := r.GetDB().WithContext(ctx).
		Where("user_id = ? AND book_id = ?", userID, bookID).
		First(&like).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &like, nil
}
