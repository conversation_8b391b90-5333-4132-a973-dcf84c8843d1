package mongodb

import (
	"context"
	"fmt"
	"sync"

	"frontapi/internal/service/base/extlike/types"
)

// MongoAdapter MongoDB点赞适配器
type MongoAdapter struct {
	client    *MongoClient
	likeOps   *LikeOperations
	queryOps  *QueryOperations
	rankOps   *RankingOperations
	stats     *Stats
	mu        sync.RWMutex
	connected bool
}

// ====================
// 基础点赞操作
// ====================

// Like 点赞操作
func (a *MongoAdapter) Like(ctx context.Context, userID, itemType, itemID string) error {
	if !a.isConnected() {
		return fmt.Errorf("MongoDB连接已断开")
	}
	return a.likeOps.Like(ctx, userID, itemType, itemID)
}

// Unlike 取消点赞操作
func (a *MongoAdapter) Unlike(ctx context.Context, userID, itemType, itemID string) error {
	if !a.isConnected() {
		return fmt.Errorf("MongoDB连接已断开")
	}
	return a.likeOps.Unlike(ctx, userID, itemType, itemID)
}

// IsLiked 检查是否已点赞
func (a *MongoAdapter) IsLiked(ctx context.Context, userID, itemType, itemID string) (bool, error) {
	if !a.isConnected() {
		return false, fmt.Errorf("MongoDB连接已断开")
	}
	return a.likeOps.IsLiked(ctx, userID, itemType, itemID)
}

// GetLikeCount 获取点赞数
func (a *MongoAdapter) GetLikeCount(ctx context.Context, itemType, itemID string) (int64, error) {
	if !a.isConnected() {
		return 0, fmt.Errorf("MongoDB连接已断开")
	}
	return a.likeOps.GetLikeCount(ctx, itemType, itemID)
}

// ====================
// 批量操作
// ====================

// BatchLike 批量点赞
func (a *MongoAdapter) BatchLike(ctx context.Context, operations []*types.LikeOperation) error {
	if !a.isConnected() {
		return fmt.Errorf("MongoDB连接已断开")
	}
	return a.likeOps.BatchLike(ctx, operations)
}

// BatchUnlike 批量取消点赞
func (a *MongoAdapter) BatchUnlike(ctx context.Context, operations []*types.LikeOperation) error {
	if !a.isConnected() {
		return fmt.Errorf("MongoDB连接已断开")
	}
	return a.likeOps.BatchUnlike(ctx, operations)
}

// ====================
// 查询操作
// ====================

// BatchGetLikeStatus 批量获取点赞状态
func (a *MongoAdapter) BatchGetLikeStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.BatchGetLikeStatus(ctx, userID, items)
}

// BatchGetLikeCounts 批量获取点赞数
func (a *MongoAdapter) BatchGetLikeCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.BatchGetLikeCounts(ctx, items)
}

// GetUserLikes 获取用户点赞列表
func (a *MongoAdapter) GetUserLikes(ctx context.Context, userID string, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.GetUserLikes(ctx, userID, itemType, limit, offset)
}

// GetItemLikers 获取项目点赞用户列表
func (a *MongoAdapter) GetItemLikers(ctx context.Context, itemType, itemID string, limit, offset int) ([]*types.LikeRecord, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.GetItemLikers(ctx, itemType, itemID, limit, offset)
}

// GetLikeHistory 获取点赞历史
func (a *MongoAdapter) GetLikeHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.LikeRecord, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.GetLikeHistory(ctx, userID, itemType, timeRange)
}

// ====================
// 统计查询
// ====================

// GetUserLikeStats 获取用户点赞统计
func (a *MongoAdapter) GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.GetUserLikeStats(ctx, userID)
}

// GetItemLikeStats 获取项目点赞统计
func (a *MongoAdapter) GetItemLikeStats(ctx context.Context, itemType, itemID string) (*types.ItemLikeStats, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.queryOps.GetItemLikeStats(ctx, itemType, itemID)
}

// ====================
// 排行榜操作
// ====================

// UpdateHotRank 更新热门排行
func (a *MongoAdapter) UpdateHotRank(ctx context.Context, itemType, algorithm string, threshold float64) error {
	if !a.isConnected() {
		return fmt.Errorf("MongoDB连接已断开")
	}
	return a.rankOps.UpdateHotRank(ctx, itemType, algorithm, threshold)
}

// GetHotRanking 获取热门排行
func (a *MongoAdapter) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.rankOps.GetHotRanking(ctx, itemType, limit)
}

// GetHotRankingWithScores 获取带分数的热门排行
func (a *MongoAdapter) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	if !a.isConnected() {
		return nil, fmt.Errorf("MongoDB连接已断开")
	}
	return a.rankOps.GetHotRankingWithScores(ctx, itemType, limit)
}

// ====================
// 连接管理
// ====================

// Ping 测试连接
func (a *MongoAdapter) Ping(ctx context.Context) error {
	if !a.isConnected() {
		return fmt.Errorf("MongoDB连接已断开")
	}
	return a.client.Ping(ctx)
}

// GetStatus 获取连接状态
func (a *MongoAdapter) GetStatus() string {
	a.mu.RLock()
	defer a.mu.RUnlock()

	if a.connected {
		return "connected"
	}
	return "disconnected"
}

// ====================
// 私有方法
// ====================

// isConnected 检查连接状态
func (a *MongoAdapter) isConnected() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.connected
}

// GetClient 获取底层客户端（用于高级操作）
func (a *MongoAdapter) GetClient() *MongoClient {
	return a.client
}

// GetConfig 获取配置信息
func (a *MongoAdapter) GetConfig() *MongoConfig {
	return a.client.config
}

// Cleanup 清理资源
func (a *MongoAdapter) Cleanup(ctx context.Context) error {
	return a.client.Close(ctx)
}
