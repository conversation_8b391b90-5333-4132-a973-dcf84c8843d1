package books

import (
	"time"

	"frontapi/internal/models"
)

// ReadHistory 阅读历史模型
type BookReadHistory struct {
	models.BaseModel
	UserID       string    `json:"user_id" gorm:"type:varchar(36);not null;index;comment:用户ID"`
	BookID       string    `json:"book_id" gorm:"type:varchar(36);not null;index;comment:书籍ID"`
	BookTitle    string    `json:"book_title" gorm:"type:varchar(255);not null;comment:书籍标题"`
	BookCover    string    `json:"book_cover" gorm:"type:varchar(500);comment:书籍封面"`
	ChapterID    string    `json:"chapter_id" gorm:"type:varchar(36);comment:章节ID"`
	ChapterTitle string    `json:"chapter_title" gorm:"type:varchar(255);comment:章节标题"`
	Author       string    `json:"author" gorm:"type:varchar(255);comment:作者"`
	Position     int       `json:"position" gorm:"default:0;comment:阅读位置"`
	ReadTime     time.Time `json:"read_time" gorm:"comment:阅读时间"`
}

// TableName 设置表名
func (BookReadHistory) TableName() string {
	return "ly_book_read_history"
}
