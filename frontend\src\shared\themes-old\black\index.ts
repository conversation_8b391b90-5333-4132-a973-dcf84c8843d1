/**
 * Dark 主题配置
 * 基于深色调的主题
 */
import { ThemeConfig } from '../theme-manager';
import blackVariables from './variables';
import blackLightVariables from './variables-light';

// Dark 亮色主题
export const blackLightTheme: ThemeConfig = {
    name: 'blackLight',
    displayName: 'Black Light',
    shortName: 'black',
    code: 'blacklight',
    primary: '#616161',
    isDark: false,
    extendTheme: 'aura',
    extendName: 'aura-light-black',
    extendThemeStyle: 'primevue/resources/themes/aura-light-noir/theme.css',
    variables: blackLightVariables
};

// Dark 暗色主题
export const blackDarkTheme: ThemeConfig = {
    name: 'blackDark',
    displayName: 'Black Dark',
    shortName: 'black dark',
    code: 'blackdark',
    primary: '#424242',
    isDark: true,
    extendTheme: 'aura',
    extendName: 'aura-dark-black',
    extendThemeStyle: 'primevue/resources/themes/aura-dark-noir/theme.css',
    variables: blackVariables
};

// 默认导出暗色主题（保持向后兼容）
export default blackDarkTheme;