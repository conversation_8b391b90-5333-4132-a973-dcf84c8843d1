package users

import (
	"frontapi/pkg/types"

	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreateSessionRequest 创建会话请求
type CreateSessionRequest struct {
	UserID     string `json:"userId" validate:"required"`
	Token      string `json:"token" validate:"required"`
	DeviceType string `json:"deviceType"`
	DeviceInfo string `json:"deviceInfo"`
	IPAddress  string `json:"ipAddress"`
	Location   string `json:"location"`
	ExpireTime types.JSONTime
}

// UpdateSessionRequest 更新会话请求
type UpdateSessionRequest struct {
	IsActive       bool           `json:"isActive"`
	LastActiveTime types.JSONTime `json:"lastActiveTime"`
	ExpireTime     types.JSONTime `json:"expireTime"`
}

// UserSessionService 用户会话服务接口
type UserSessionService interface {
	base.IExtendedService[users.UserSession]
}

// userSessionService 用户会话服务实现
type userSessionService struct {
	*base.ExtendedService[users.UserSession]
	sessionRepo repo.UserSessionRepository
	userRepo    repo.UserRepository
}

// NewUserSessionService 创建用户会话服务实例
func NewUserSessionService(
	sessionRepo repo.UserSessionRepository,
	userRepo repo.UserRepository,
) UserSessionService {
	return &userSessionService{
		ExtendedService: base.NewExtendedService[users.UserSession](sessionRepo, "user_session"),
		sessionRepo:     sessionRepo,
		userRepo:        userRepo,
	}
}
