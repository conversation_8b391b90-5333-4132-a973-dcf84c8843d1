package books

import (
	"frontapi/internal/models"
)

// Favorite 收藏模型
type BookFavorite struct {
	models.BaseModel
	UserID       string `json:"user_id" gorm:"type:varchar(36);not null;index;comment:用户ID"`
	BookID       string `json:"book_id" gorm:"type:varchar(36);not null;index;comment:书籍ID"`
	BookTitle    string `json:"book_title" gorm:"type:varchar(255);not null;comment:书籍标题"`
	BookCover    string `json:"book_cover" gorm:"type:varchar(500);comment:书籍封面"`
	Author       string `json:"author" gorm:"type:varchar(255);comment:作者"`
	CategoryName string `json:"category_name" gorm:"type:varchar(100);comment:分类名称"`
	Status       int    `json:"status" gorm:"default:1;comment:状态 0-取消收藏 1-已收藏"`
}

// TableName 设置表名
func (BookFavorite) TableName() string {
	return "ly_book_favorites"
}
