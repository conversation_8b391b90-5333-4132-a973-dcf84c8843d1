package books

import (
	"context"
	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// BookRepository 书籍仓库接口
type BookRepository interface {
	base.ExtendedRepository[books.Book]
	// 业务特定方法
	ListByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*books.Book, int64, error)
	ListFeatured(ctx context.Context, page, pageSize int) ([]*books.Book, int64, error)
	Search(ctx context.Context, keyword string, page, pageSize int) ([]*books.Book, int64, error)
	IncrementReadCount(ctx context.Context, id string) error
	IncrementChapterCount(ctx context.Context, id string) error
	UpdateWordCount(ctx context.Context, id string, wordCount uint64) error
	ListByCreator(ctx context.Context, creatorID string, page, pageSize int) ([]*books.Book, int64, error)
}

// bookRepository 书籍仓库实现
type bookRepository struct {
	base.ExtendedRepository[books.Book]
}

// NewBookRepository 创建书籍仓库实例
func NewBookRepository(db *gorm.DB) BookRepository {
	return &bookRepository{
		ExtendedRepository: base.NewExtendedRepository[books.Book](db),
	}
}

// ListByCategory 根据分类获取电子书列表
func (r *bookRepository) ListByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*books.Book, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "created_at DESC", page, pageSize, "category_id = ? AND status = ?", categoryID, 1)
}

// ListFeatured 获取推荐电子书列表
func (r *bookRepository) ListFeatured(ctx context.Context, page, pageSize int) ([]*books.Book, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "created_at DESC", page, pageSize, "is_featured = ?", 1)
}

// Search 搜索电子书
func (r *bookRepository) Search(ctx context.Context, keyword string, page, pageSize int) ([]*books.Book, int64, error) {
	var bookList []*books.Book
	var total int64

	offset := (page - 1) * pageSize

	db := r.GetDBWithContext(ctx).Model(&books.Book{}).
		Where("title LIKE ? OR author LIKE ? OR tags LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")

	err := db.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = db.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&bookList).Error
	if err != nil {
		return nil, 0, err
	}

	return bookList, total, nil
}

// IncrementReadCount 增加阅读数
func (r *bookRepository) IncrementReadCount(ctx context.Context, id string) error {
	return r.GetDBWithContext(ctx).Model(&books.Book{}).
		Where("id = ?", id).
		UpdateColumn("read_count", gorm.Expr("read_count + 1")).Error
}

// IncrementChapterCount 增加章节数
func (r *bookRepository) IncrementChapterCount(ctx context.Context, id string) error {
	return r.GetDBWithContext(ctx).Model(&books.Book{}).
		Where("id = ?", id).
		UpdateColumn("chapter_count", gorm.Expr("chapter_count + 1")).Error
}

// UpdateWordCount 更新字数
func (r *bookRepository) UpdateWordCount(ctx context.Context, id string, wordCount uint64) error {
	return r.GetDBWithContext(ctx).Model(&books.Book{}).
		Where("id = ?", id).
		UpdateColumn("word_count", wordCount).Error
}

// ListByCreator 获取创建者的电子书列表
func (r *bookRepository) ListByCreator(ctx context.Context, creatorID string, page, pageSize int) ([]*books.Book, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "created_at DESC", page, pageSize, "creator_id = ?", creatorID)
}
