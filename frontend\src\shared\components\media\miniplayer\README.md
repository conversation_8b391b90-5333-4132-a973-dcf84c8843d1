# 视频播放器组件 (MiniPlayer)

## 概述

这是一个基于 xgplayer 的现代化视频播放器组件，专为嵌入到帖子页面和帖子详情页面而设计。组件支持多种视频格式，具有智能的宽高比例计算和控制栏显示/隐藏功能。

## 最新更新 (2024)

### 🎯 核心功能改进

1. **智能宽高比例计算**
   - 优先显示视频高度100%，宽度等比例缩放
   - 当视频宽度超过父容器时，自动调整为容器宽度100%，高度等比例缩放
   - 视频始终居中显示，确保最佳观看体验

2. **智能控制栏显示/隐藏**
   - 播放时：控制栏3秒后缓慢隐藏（使用缓动动画）
   - 暂停/结束时：控制栏立即显示
   - 鼠标悬浮时：控制栏显示
   - 鼠标离开时：如果正在播放，延迟隐藏控制栏

3. **命名规范更新**
   - 在代码注释中使用 "explayer" 命名
   - 保持 xgplayer 原始CSS类名以确保兼容性
   - 变量和函数名使用更清晰的命名约定

## 目录结构

```
frontend/src/components/miniplayer/
├── index.vue                    # 主要播放器组件 (供community使用)
├── README.md                    # 文档说明
├── composables/                 # 组合式函数
│   ├── useSimpleVideoPlayer.ts  # 简化的视频播放器Hook
│   ├── useVideoPlayer.ts        # 基于xgplayer的Hook
│   └── useIntersectionObserver.ts # 可见性检测Hook
└── components/                  # 子组件目录
    ├── PostVideoPlayer.vue      # 帖子视频播放器
    ├── FullscreenVideoPlayer.vue # 全屏视频播放器
    ├── MiniPlayer.vue           # 迷你播放器
    ├── VideoControls.vue        # 视频控制组件
    └── VideoProgress.vue        # 进度条组件
```

## 主要组件

### index.vue (主要组件)
- 基于 xgplayer 的完整功能播放器
- 支持智能宽高比例计算
- 智能控制栏显示/隐藏
- 自动可见性检测和播放控制
- 完整的事件系统和API

### components/PostVideoPlayer.vue
- 专为帖子设计的轻量级播放器
- 基于原生 HTML5 Video
- 现代化UI设计
- 响应式布局

### components/FullscreenVideoPlayer.vue
- 专为全屏显示设计
- 支持不同宽高比视频适配
- 优化的移动端体验

## 使用方法

### 基础用法

```vue
<template>
  <MiniPlayer
    src="http://localhost:8082/videos/movies/a.mp4"
    :autoplay="false"
    :muted="true"
    :enable-auto-play="true"
    aspect-ratio="16:9"
    @play="handlePlay"
    @pause="handlePause"
    @ended="handleEnded"
  />
</template>

<script setup>
import MiniPlayer from '@/shared/components/media/miniplayer/Index.vue'

const handlePlay = () => {
  console.log('视频开始播放 - 控制栏将在3秒后隐藏')
}

const handlePause = () => {
  console.log('视频暂停 - 控制栏显示')
}

const handleEnded = () => {
  console.log('视频播放结束')
}
</script>
```

### 在社区帖子中使用

```vue
<template>
  <div class="post-item">
    <!-- 视频内容区域 -->
    <div v-if="post.videoContent" class="video-content">
      <MiniPlayer
        :src="post.videoContent.url"
        :poster="post.videoContent.thumbnail"
        :autoplay="false"
        :muted="true"
        :enable-auto-play="true"
        is-compact
        @play="handleVideoPlay"
        @pause="handleVideoPause"
      />
    </div>
  </div>
</template>
```

## Props 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| src | string | - | 视频源URL（必需） |
| poster | string | '' | 视频封面图片URL |
| duration | number | 0 | 视频时长（秒） |
| quality | string | '' | 视频质量标识 |
| width | number/string | '100%' | 播放器宽度 |
| height | number/string | 240 | 播放器高度 |
| autoplay | boolean | false | 是否自动播放 |
| muted | boolean | false | 是否静音 |
| loop | boolean | false | 是否循环播放 |
| isCompact | boolean | false | 是否紧凑模式 |
| showInfo | boolean | false | 是否显示视频信息 |
| enableAutoPlay | boolean | true | 是否启用自动播放控制 |
| aspectRatio | string | '16:9' | 视频宽高比 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| play | - | 视频开始播放 |
| pause | - | 视频暂停 |
| ended | - | 视频播放结束 |
| error | Error | 播放错误 |
| timeupdate | number | 播放时间更新 |
| loadedmetadata | number | 元数据加载完成 |
| click | - | 播放器点击 |
| volume-change | number | 音量变化 |

## 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| play | - | void | 开始播放 |
| pause | - | void | 暂停播放 |
| togglePlay | - | void | 切换播放/暂停 |
| setVolume | number | void | 设置音量 (0-1) |
| toggleMute | - | void | 切换静音 |
| seek | number | void | 跳转到指定时间 |
| enterPictureInPicture | - | void | 进入画中画模式 |
| exitPictureInPicture | - | void | 退出画中画模式 |

## 新功能特性

### 1. 智能宽高比例计算

播放器会根据视频的实际尺寸和容器尺寸智能调整显示方式：

```javascript
// 第一步：优先显示视频高度100%，宽度等比例缩放
let videoWidth = containerHeight * videoAspectRatio
let videoHeight = containerHeight

// 第二步：检测视频宽度是否超过父容器宽度
if (videoWidth > containerWidth) {
  // 如果超过，则调整为容器宽度100%，高度等比例缩放
  videoWidth = containerWidth
  videoHeight = containerWidth / videoAspectRatio
}
```

### 2. 智能控制栏管理

控制栏的显示/隐藏逻辑：

- **播放状态**：3秒后缓慢隐藏（cubic-bezier缓动动画）
- **暂停/结束状态**：立即显示
- **鼠标交互**：悬浮时显示，离开时根据播放状态决定是否隐藏

### 3. 响应式设计

- 支持桌面端和移动端
- 自适应容器尺寸
- 优化的触摸交互

## 测试页面

访问 `/videos/test-player` 可以查看完整的功能测试页面，包括：

- 16:9 横屏视频测试
- 9:16 竖屏视频测试
- 小尺寸容器测试
- 超宽容器测试
- 实时播放日志

## 技术栈

- **Vue 3** - Composition API
- **TypeScript** - 类型安全
- **xgplayer** - 视频播放引擎
- **SCSS** - 样式预处理
- **@vueuse/core** - 工具函数库

## 浏览器兼容性

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 性能优化

- 使用 `shallowRef` 避免深度响应式
- 使用 `markRaw` 避免组件响应式警告
- 智能的事件监听器管理
- 自动内存清理

## 注意事项

1. 确保视频服务器支持 CORS
2. 移动端需要用户交互才能自动播放
3. 某些浏览器可能限制自动播放功能
4. 建议为视频提供封面图片以提升用户体验

## 更新日志

### v2.0.0 (2024-12-19)
- ✨ 新增智能宽高比例计算逻辑
- ✨ 新增智能控制栏显示/隐藏功能
- 🔧 优化视频适配算法
- 🔧 改进事件处理机制
- 📝 更新命名规范（explayer）
- 🐛 修复Vue响应式对象警告
- 🎨 优化UI动画效果

### v1.0.0 (2024-12-18)
- 🎉 初始版本发布
- ✨ 基础视频播放功能
- ✨ 多种播放器组件
- ✨ 响应式设计
- ✨ 事件系统

## 技术细节

### 架构设计

```