# 自定义条件应用指南

## 概述

本指南说明如何在仓库(Repository)中实现自定义的查询条件应用逻辑，覆盖基础仓库的默认条件处理。

## 功能特性

- **子类覆盖**: 允许具体的仓库实现自定义的条件应用逻辑
- **自动检测**: 基础仓库会自动检测子类是否实现了`ConditionApplier`接口
- **回退机制**: 如果子类没有实现自定义逻辑，会回退到默认的条件处理
- **类型安全**: 通过Go泛型确保类型安全

## 实现步骤

### 1. 实现ConditionApplier接口

在你的具体仓库中实现`ConditionApplier`接口：

```go
// ApplyConditions 实现ConditionApplier接口，自定义查询条件应用逻辑
func (r *yourRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    if condition == nil {
        return query
    }

    // 处理关键字搜索
    if condition["keyword"] != nil && condition["keyword"] != "" {
        keyword := condition["keyword"].(string)
        query = query.Where("title LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
    }

    // 处理状态过滤
    if condition["status"] != nil && condition["status"] != "" {
        if status, ok := condition["status"].(int); ok && status > -999 {
            query = query.Where("status = ?", status)
        }
    }

    // 处理其他自定义条件...

    return query
}
```

### 2. 在构造函数中设置Caller

#### 对于使用ExtendedRepository的仓库：

在仓库的构造函数中调用`SetCaller`方法：

```go
func NewYourRepository(db *gorm.DB) YourRepositoryInterface {
    repo := &yourRepository{
        ExtendedRepository: base.NewExtendedRepository[YourModel](db),
    }
    // 设置调用者实例，这样基础仓库就能识别并使用子类的ApplyConditions方法
    repo.SetCaller(repo)
    return repo
}
```

#### 对于直接使用BaseRepository的仓库：

有两种方式来设置caller：

**方式一：使用NewBaseRepositoryWithCaller辅助函数**
```go
func NewYourRepository(db *gorm.DB) YourRepositoryInterface {
    repo := &yourRepository{}
    // 直接创建带有caller设置的基础仓库
    repo.BaseRepository = base.NewBaseRepositoryWithCaller[YourModel](db, repo)
    return repo
}
```

**方式二：先创建再设置**
```go
func NewYourRepository(db *gorm.DB) YourRepositoryInterface {
    repo := &yourRepository{
        BaseRepository: base.NewBaseRepository[YourModel](db),
    }
    // 设置调用者实例
    repo.SetCaller(repo)
    return repo
}
```

## 完整示例

### 用户仓库示例

```go
package users

import (
    "context"
    "frontapi/internal/models/users"
    "frontapi/internal/repository/base"
    "gorm.io/gorm"
)

// UserRepository 用户仓库接口
type UserRepository interface {
    base.ExtendedRepository[users.User]
    // 业务特定方法...
}

// userRepository 用户仓库实现
type userRepository struct {
    base.ExtendedRepository[users.User]
}

// NewUserRepository 创建用户仓库实例
func NewUserRepository(db *gorm.DB) UserRepository {
    repo := &userRepository{
        ExtendedRepository: base.NewExtendedRepository[users.User](db),
    }
    // 设置调用者实例
    repo.SetCaller(repo)
    return repo
}

// ApplyConditions 实现ConditionApplier接口，自定义用户查询条件应用逻辑
func (r *userRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    if condition == nil {
        return query
    }

    // 处理关键字搜索 - 支持用户名、昵称、邮箱、手机号、简介搜索
    if condition["keyword"] != nil && condition["keyword"] != "" {
        keyword := condition["keyword"].(string)
        query = query.Where("username LIKE ? OR nickname LIKE ? OR email LIKE ? OR phone LIKE ? OR bio LIKE ?", 
            "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
    }

    // 处理状态过滤
    if condition["status"] != nil && condition["status"] != "" {
        if status, ok := condition["status"].(int); ok && status > -999 {
            query = query.Where("status = ?", status)
        }
    }

    // 处理注册时间范围
    if condition["reg_time_start"] != nil && condition["reg_time_start"] != "" {
        query = query.Where("reg_time >= ?", condition["reg_time_start"])
    }
    if condition["reg_time_end"] != nil && condition["reg_time_end"] != "" {
        query = query.Where("reg_time <= ?", condition["reg_time_end"])
    }

    // 处理内容创作者过滤
    if condition["is_content_creator"] != nil && condition["is_content_creator"] != "" {
        isContentCreator := condition["is_content_creator"].(int)
        query = query.Where("is_content_creator = ?", isContentCreator)
    }

    // 处理创作者等级过滤
    if condition["creator_level"] != nil && condition["creator_level"] != "" {
        creatorLevel := condition["creator_level"].(int)
        query = query.Where("creator_level = ?", creatorLevel)
    }

    // 处理通用时间范围条件
    if condition["created_at_start"] != nil && condition["created_at_start"] != "" {
        query = query.Where("created_at >= ?", condition["created_at_start"])
    }
    if condition["created_at_end"] != nil && condition["created_at_end"] != "" {
        query = query.Where("created_at <= ?", condition["created_at_end"])
    }

    // 处理其他字段的等值查询
    for key, value := range condition {
        if value == nil || value == "" {
            continue
        }
        
        // 跳过已处理的字段
        switch key {
        case "keyword", "status", "reg_time_start", "reg_time_end", 
             "is_content_creator", "creator_level", 
             "created_at_start", "created_at_end", "updated_at_start", "updated_at_end":
            continue
        default:
            // 默认等值查询
            query = query.Where(key+" = ?", value)
        }
    }

    return query
}
```

## 短视频仓库示例

```go
package shortvideos

import (
    "frontapi/internal/models/shortvideos"
    "frontapi/internal/repository/base"
    "gorm.io/gorm"
)

type shortVideoRepository struct {
    base.ExtendedRepository[shortvideos.ShortVideo]
}

func NewShortVideoRepository(db *gorm.DB) ShortVideoRepository {
    repo := &shortVideoRepository{
        ExtendedRepository: base.NewExtendedRepository[shortvideos.ShortVideo](db),
    }
    repo.SetCaller(repo)
    return repo
}

// ApplyConditions 自定义短视频查询条件
func (r *shortVideoRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    if condition == nil {
        return query
    }

    // 处理关键字搜索 - 标题和描述
    if condition["keyword"] != nil && condition["keyword"] != "" {
        keyword := condition["keyword"].(string)
        query = query.Where("title LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
    }

    // 处理分类过滤
    if condition["category_id"] != nil && condition["category_id"] != "" {
        query = query.Where("category_id = ?", condition["category_id"])
    }

    // 处理创作者过滤
    if condition["creator_id"] != nil && condition["creator_id"] != "" {
        query = query.Where("creator_id = ?", condition["creator_id"])
    }

    // 处理时长范围
    if condition["duration_min"] != nil && condition["duration_min"] != "" {
        query = query.Where("duration >= ?", condition["duration_min"])
    }
    if condition["duration_max"] != nil && condition["duration_max"] != "" {
        query = query.Where("duration <= ?", condition["duration_max"])
    }

    // 处理是否推荐
    if condition["is_featured"] != nil && condition["is_featured"] != "" {
        query = query.Where("is_featured = ?", condition["is_featured"])
    }

    // 处理通用条件
    if condition["status"] != nil && condition["status"] != "" {
        if status, ok := condition["status"].(int); ok && status > -999 {
            query = query.Where("status = ?", status)
        }
    }

    return query
}
```

## 核心概念

### ConditionApplier接口

```go
type ConditionApplier interface {
    ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB
}
```

### 工作流程

1. **检测阶段**: 基础仓库检查是否有caller设置，并且caller是否实现了`ConditionApplier`接口
2. **应用阶段**: 如果检测到自定义实现，调用子类的`ApplyConditions`方法；否则使用默认逻辑
3. **回退机制**: 确保在没有自定义实现时，系统仍能正常工作

### 最佳实践

1. **完整性**: 在自定义`ApplyConditions`中处理所有可能的条件，包括通用条件
2. **性能**: 对于复杂查询，考虑使用索引优化
3. **安全**: 始终验证输入参数，避免SQL注入
4. **可维护性**: 将复杂的条件逻辑分解为独立的方法

## 注意事项

- 确保在构造函数中调用`SetCaller(repo)`
- `ApplyConditions`方法必须处理`condition`为`nil`的情况
- 建议处理通用条件（如时间范围、状态）以保持一致性
- 对于复杂的查询逻辑，可以在`ApplyConditions`中调用其他辅助方法

## 调试技巧

如果自定义条件没有生效，检查以下几点：

1. 是否调用了`SetCaller(repo)`
2. 方法名是否正确（`ApplyConditions`，注意大写A）
3. 方法签名是否正确
4. 是否正确处理了所有条件参数 