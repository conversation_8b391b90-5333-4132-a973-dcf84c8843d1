<template>
    <div class="simple-language-selector">
        <select v-model="currentLanguage" @change="onLanguageChange" class="language-select">
            <option value="en">English</option>
            <option value="zh-CN">简体中文</option>
            <option value="zh-TW">繁體中文</option>
            <option value="ko">한국어</option>
        </select>
    </div>
</template>

<script setup lang="ts">
import { useTranslation } from '@/core/plugins/i18n/composables'
import type { LocaleType } from '@/locales'
import { onMounted, ref } from 'vue'

// 使用翻译助手
const { locale, changeLocale } = useTranslation()

// 当前语言
const currentLanguage = ref<LocaleType>('en')

// 语言切换处理
const onLanguageChange = async () => {
    try {
        await changeLocale(currentLanguage.value)
        console.log('Language changed to:', currentLanguage.value)
    } catch (error) {
        console.error('Failed to change language:', error)
        // 回退到默认语言
        currentLanguage.value = 'en'
    }
}

// 组件挂载时初始化
onMounted(() => {
    currentLanguage.value = locale.value as LocaleType
})
</script>

<style scoped>
.simple-language-selector {
    display: inline-block;
}

.language-select {
    padding: 0.5rem;
    border: 1px solid var(--surface-border, #ccc);
    border-radius: 4px;
    background: var(--surface-ground, white);
    color: var(--text-color, black);
    font-size: 0.875rem;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.language-select:hover {
    border-color: var(--primary-color, #007bff);
}

.language-select:focus {
    outline: none;
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}
</style>