package admin

import (
	"frontapi/internal/admin/comics"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterComicRoutes 注册漫画相关路由
func RegisterComicRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	comicController := comics.NewComicController(
		services.ComicService,
	)
	// 漫画相关路由组
	comicsApp := apiGroup.Group("/comics", middleware.AuthRequired())
	{
		// 公开接口
		comicsApp.Post("/all", comicController.List)
		comicsApp.Post("/detail", comicController.GetByID)
		comicsApp.Post("/add", comicController.Create)
		comicsApp.Post("/update/:id", comicController.Update)
		comicsApp.Post("/delete/:id", comicController.Delete)
	}

	//comicsApp.Post("/category/:categoryId", comicController.ListComicsByCategory)
	//comicsApp.Post("/featured", comicController.ListFeaturedComics)
	//comicsApp.Post("/:comicId/chapters", comicController.GetComicChapters)
	//漫画分类相关路由
	category := apiGroup.Group("/comics/category", middleware.AuthRequired())
	{
		categoryController := comics.NewComicCategoryController(services.ComicCategoryService)
		category.Post("/all", categoryController.List)
		category.Post("/add", categoryController.Create)
		category.Post("/update/:id", categoryController.Update)
		category.Post("/delete/:id", categoryController.Delete)
		category.Post("/get", categoryController.GetByID)
	}

	//漫画章节路由
	chapter := apiGroup.Group("/comics/chapter", middleware.AuthRequired())
	{
		chapterController := comics.NewComicChapterController(services.ComicChapterService)
		chapter.Post("/list", chapterController.List)
		chapter.Post("/add", chapterController.Create)
		chapter.Post("/update/:id", chapterController.Update)
		chapter.Post("/delete/:id", chapterController.Delete)
		chapter.Post("/get", chapterController.GetByID)
		chapter.Post("/batch-update-order", chapterController.BatchUpdateOrder)
	}

	//漫画页面相关路由
	page := apiGroup.Group("/comics/page", middleware.AuthRequired())
	{
		pageController := comics.NewComicPageController(services.ComicPageService)
		page.Post("/list", pageController.List)
		page.Post("/add", pageController.Create)
		page.Post("/batch-add", pageController.BatchCreate)
		page.Post("/update/:id", pageController.Update)
		page.Post("/delete/:id", pageController.Delete)
		page.Post("/get", pageController.GetByID)
		page.Post("/batch-update-order", pageController.BatchUpdateOrder)
	}

	// 漫画收藏相关路由
	//下面是预留接口
	// 漫画评论相关路由组
	//comments := app.Group("/api/proadm/comic-comments")
	//{
	//	// 公开接口
	//	//comments.Post("/list", comicController.ListComments)
	//	//comments.Get("/replies", comicController.ListReplies)
	//}
	//
	//// 漫画阅读历史相关路由
	//history := app.Group("/api/proadm/comic-history")
	//{
	//	//history.Get("/", middleware.AuthRequired(), comicController.ListReadHistories)
	//}
}
