package cache

import (
	"context"
	"fmt"
	"sync"
	"time"

	cacheConfig "frontapi/config/cache"
	"frontapi/pkg/cache/types"
)

// Manager 缓存管理器实现，提供对多个缓存适配器的管理
type Manager struct {
	adapters       map[string]types.CacheAdapter
	defaultAdapter string
	mu             sync.RWMutex
	config         *cacheConfig.CacheConfig
	localCache     types.CacheAdapter // 本地缓存适配器，用作主缓存的二级缓存
}

// NewManager 创建新的缓存管理器
func NewManager(config *cacheConfig.CacheConfig) (*Manager, error) {
	manager := &Manager{
		adapters: make(map[string]types.CacheAdapter),
		config:   config,
	}

	// 设置默认适配器
	if config != nil && config.DefaultAdapter != "" {
		manager.defaultAdapter = config.DefaultAdapter
	}

	return manager, nil
}

// GetAdapter 获取特定的缓存适配器
func (m *Manager) GetAdapter(adapterType string) (types.CacheAdapter, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	adapter, ok := m.adapters[adapterType]
	if !ok {
		return nil, types.ErrAdapterNotFound
	}

	return adapter, nil
}

// AddAdapter 添加缓存适配器
func (m *Manager) AddAdapter(name string, adapter types.CacheAdapter) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.adapters[name]; exists {
		return types.ErrAdapterAlreadyExists
	}

	m.adapters[name] = adapter

	// 如果是第一个添加的适配器且未设置默认适配器，将其设为默认
	if len(m.adapters) == 1 && m.defaultAdapter == "" {
		m.defaultAdapter = name
	}

	return nil
}

// GetAdapterNames 获取所有适配器名称
func (m *Manager) GetAdapterNames() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	names := make([]string, 0, len(m.adapters))
	for name := range m.adapters {
		names = append(names, name)
	}

	return names
}

// GetDefaultAdapter 获取默认适配器
func (m *Manager) GetDefaultAdapter() (types.CacheAdapter, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.defaultAdapter == "" {
		return nil, types.ErrNoDefaultAdapter
	}

	adapter, ok := m.adapters[m.defaultAdapter]
	if !ok {
		return nil, types.ErrAdapterNotFound
	}

	// 如果启用本地缓存且本地缓存适配器已设置，使用多级缓存
	if m.config != nil && m.config.EnableLocalCache && m.localCache != nil {
		return NewMultiLevelAdapter(adapter, m.localCache), nil
	}

	return adapter, nil
}

// SetDefaultAdapter 设置默认适配器
func (m *Manager) SetDefaultAdapter(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, ok := m.adapters[name]; !ok {
		return types.ErrAdapterNotFound
	}

	m.defaultAdapter = name
	return nil
}

// SetLocalCache 设置本地缓存适配器
func (m *Manager) SetLocalCache(adapter types.CacheAdapter) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.localCache = adapter
}

// Close 关闭所有适配器
func (m *Manager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	var lastErr error
	for _, adapter := range m.adapters {
		if err := adapter.Close(); err != nil {
			lastErr = err
		}
	}

	// 关闭本地缓存
	if m.localCache != nil {
		if err := m.localCache.Close(); err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// MultiLevelAdapter 多级缓存适配器，组合主缓存和本地缓存
type MultiLevelAdapter struct {
	primary    types.CacheAdapter // 主缓存（如Redis）
	secondary  types.CacheAdapter // 次级缓存（如本地内存）
	name       string
	localStats *types.CacheStats
	statsMutex sync.RWMutex
}

// NewMultiLevelAdapter 创建多级缓存适配器
func NewMultiLevelAdapter(primary, secondary types.CacheAdapter) *MultiLevelAdapter {
	return &MultiLevelAdapter{
		primary:    primary,
		secondary:  secondary,
		name:       "multi:" + primary.Name() + "+" + secondary.Name(),
		localStats: &types.CacheStats{StartTime: time.Now()},
	}
}

// Get 获取缓存值，先从本地缓存获取，未命中再从主缓存获取
func (m *MultiLevelAdapter) Get(ctx context.Context, key string) ([]byte, error) {
	// 先尝试从本地缓存获取
	value, err := m.secondary.Get(ctx, key)
	if err == nil {
		// 本地缓存命中
		return value, nil
	}

	// 本地缓存未命中，从主缓存获取
	value, err = m.primary.Get(ctx, key)
	if err != nil {
		return nil, err
	}

	// 获取成功后，同时写入本地缓存（异步）
	go func(k string, v []byte) {
		// 使用较短的TTL，避免本地缓存数据过期
		localTTL := 5 * time.Minute
		_ = m.secondary.Set(context.Background(), k, v, localTTL)
	}(key, value)

	return value, nil
}

// Set 设置缓存值，同时写入主缓存和本地缓存
func (m *MultiLevelAdapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	// 先写入主缓存
	err := m.primary.Set(ctx, key, value, expiration)
	if err != nil {
		return err
	}

	// 写入本地缓存（使用较短的TTL）
	localTTL := expiration
	if localTTL > 30*time.Minute {
		localTTL = 30 * time.Minute
	}

	// 异步写入本地缓存
	go func(k string, v []byte, ttl time.Duration) {
		_ = m.secondary.Set(context.Background(), k, v, ttl)
	}(key, value, localTTL)

	return nil
}

// Delete 删除缓存值，同时从主缓存和本地缓存删除
func (m *MultiLevelAdapter) Delete(ctx context.Context, key string) error {
	// 从主缓存删除
	err := m.primary.Delete(ctx, key)

	// 异步从本地缓存删除
	go func(k string) {
		_ = m.secondary.Delete(context.Background(), k)
	}(key)

	return err
}

// Clear 清空缓存
func (m *MultiLevelAdapter) Clear(ctx context.Context) error {
	// 先清空主缓存
	err := m.primary.Clear(ctx)

	// 异步清空本地缓存
	go func() {
		_ = m.secondary.Clear(context.Background())
	}()

	return err
}

// Close 关闭缓存连接
func (m *MultiLevelAdapter) Close() error {
	// 关闭主缓存
	err1 := m.primary.Close()

	// 关闭本地缓存
	err2 := m.secondary.Close()

	if err1 != nil {
		return err1
	}
	return err2
}

// Stats 获取缓存统计信息
func (m *MultiLevelAdapter) Stats() *types.CacheStats {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	// 从主缓存和本地缓存获取统计信息
	primaryStats := m.primary.Stats()
	secondaryStats := m.secondary.Stats()

	// 合并统计信息
	m.localStats.Hits = primaryStats.Hits + secondaryStats.Hits
	m.localStats.Misses = primaryStats.Misses
	m.localStats.Sets = primaryStats.Sets + secondaryStats.Sets
	m.localStats.Deletes = primaryStats.Deletes + secondaryStats.Deletes
	m.localStats.Clears = primaryStats.Clears + secondaryStats.Clears
	m.localStats.BytesRead = primaryStats.BytesRead + secondaryStats.BytesRead
	m.localStats.BytesWritten = primaryStats.BytesWritten + secondaryStats.BytesWritten

	// 计算命中率
	totalRequests := m.localStats.Hits + m.localStats.Misses
	if totalRequests > 0 {
		m.localStats.HitRate = float64(m.localStats.Hits) / float64(totalRequests)
	} else {
		m.localStats.HitRate = 0
	}

	return m.localStats
}

// Name 获取适配器名称
func (m *MultiLevelAdapter) Name() string {
	return m.name
}

// Type 获取适配器类型
func (m *MultiLevelAdapter) Type() string {
	return "multi-level"
}

// KeyWithPrefix 为缓存键添加前缀
func (m *MultiLevelAdapter) KeyWithPrefix(key string) string {
	return m.primary.KeyWithPrefix(key)
}

// Exists 检查键是否存在
func (m *MultiLevelAdapter) Exists(ctx context.Context, key string) (bool, error) {
	// 先检查本地缓存
	exists, err := m.secondary.Exists(ctx, key)
	if err == nil && exists {
		return true, nil
	}

	// 再检查主缓存
	return m.primary.Exists(ctx, key)
}

// MGet 批量获取多个键的值
func (m *MultiLevelAdapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	// 先从本地缓存获取
	localResults, _ := m.secondary.MGet(ctx, keys)

	// 找出本地缓存未命中的键
	missedKeys := make([]string, 0)
	for _, key := range keys {
		if _, found := localResults[key]; !found {
			missedKeys = append(missedKeys, key)
		}
	}

	// 如果所有键都在本地缓存命中，直接返回
	if len(missedKeys) == 0 {
		return localResults, nil
	}

	// 从主缓存获取未命中的键
	primaryResults, err := m.primary.MGet(ctx, missedKeys)
	if err != nil {
		return localResults, err
	}

	// 合并结果
	results := make(map[string][]byte)
	for k, v := range localResults {
		results[k] = v
	}

	// 将主缓存结果添加到结果集，并异步更新本地缓存
	for k, v := range primaryResults {
		results[k] = v

		// 异步更新本地缓存
		go func(key string, value []byte) {
			localTTL := 5 * time.Minute
			_ = m.secondary.Set(context.Background(), key, value, localTTL)
		}(k, v)
	}

	return results, nil
}

// MSet 批量设置多个键值对
func (m *MultiLevelAdapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	// 先写入主缓存
	err := m.primary.MSet(ctx, items, expiration)
	if err != nil {
		return err
	}

	// 确定本地缓存TTL
	localTTL := expiration
	if localTTL > 30*time.Minute {
		localTTL = 30 * time.Minute
	}

	// 异步写入本地缓存
	go func(data map[string][]byte, ttl time.Duration) {
		_ = m.secondary.MSet(context.Background(), data, ttl)
	}(items, localTTL)

	return nil
}

// Increment 递增计数器
func (m *MultiLevelAdapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	// 递增操作必须在主缓存执行以确保准确性
	result, err := m.primary.Increment(ctx, key, delta)
	if err != nil {
		return 0, err
	}

	// 异步更新本地缓存
	go func(k string, val int64) {
		// 直接设置最新值
		data := []byte(fmt.Sprintf("%d", val))
		localTTL := 5 * time.Minute
		_ = m.secondary.Set(context.Background(), k, data, localTTL)
	}(key, result)

	return result, nil
}

// Decrement 递减计数器
func (m *MultiLevelAdapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return m.Increment(ctx, key, -delta)
}

// Expire 设置过期时间
func (m *MultiLevelAdapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	// 在主缓存中设置过期时间
	err := m.primary.Expire(ctx, key, expiration)

	// 确定本地缓存TTL
	localTTL := expiration
	if localTTL > 30*time.Minute {
		localTTL = 30 * time.Minute
	}

	// 异步在本地缓存中设置过期时间
	go func(k string, ttl time.Duration) {
		_ = m.secondary.Expire(context.Background(), k, ttl)
	}(key, localTTL)

	return err
}

// TTL 获取键的剩余生存时间
func (m *MultiLevelAdapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	// 使用主缓存的TTL
	return m.primary.TTL(ctx, key)
}

// Ping 测试缓存连接
func (m *MultiLevelAdapter) Ping(ctx context.Context) error {
	// 主缓存必须可用
	if err := m.primary.Ping(ctx); err != nil {
		return err
	}

	// 本地缓存即使不可用也不影响整体功能
	_ = m.secondary.Ping(ctx)

	return nil
}
