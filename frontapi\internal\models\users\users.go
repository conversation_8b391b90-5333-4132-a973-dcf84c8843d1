package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"
)

// User 用户会员表
type User struct {
	models.BaseModelStruct
	Username            null.String    `json:"username" gorm:"<-:create;column:username;type:varchar(50);not null;comment:用户名;unique"`
	Nickname            null.String    `json:"nickname" gorm:"column:nickname;type:varchar(50);not null;comment:昵称"`
	Password            string         `json:"password" gorm:"column:password;type:varchar(128);not null;comment:密码（加密存储）"`
	Salt                null.String    `json:"salt" gorm:"column:salt;type:varchar(32);comment:盐值"`
	ResetToken          null.String    `json:"reset_token" gorm:"column:reset_token;type:varchar(128);comment:重置密码token"`
	Avatar              null.String    `json:"avatar" gorm:"column:avatar;type:varchar(255);comment:头像URL"`
	Gender              int8           `json:"gender" gorm:"column:gender;type:tinyint(1);default:0;comment:性别"`
	Email               null.String    `json:"email" gorm:"column:email;type:varchar(100);comment:邮箱"`
	Phone               null.String    `json:"phone" gorm:"column:phone;type:varchar(20);comment:手机号"`
	PhoneVerified       int8           `json:"phone_verified" gorm:"column:phone_verified;type:tinyint(1);default:0;comment:手机是否已验证"`
	EmailVerified       int8           `json:"email_verified" gorm:"column:email_verified;type:tinyint(1);default:0;comment:邮箱是否已验证"`
	Bio                 null.String    `json:"bio" gorm:"column:bio;type:text;comment:个人简介"`
	Nation              null.String    `json:"nation" gorm:"column:nation;type:varchar(20);comment:国家"`
	Address             null.String    `json:"address" gorm:"column:address;type:varchar(200);comment:地址"`
	Lat                 float64        `json:"lat" gorm:"column:lat;type:decimal(10,6);comment:纬度"`
	Lng                 float64        `json:"lng" gorm:"column:lng;type:decimal(11,6);comment:经度"`
	RecommendCode       null.String    `json:"recommend_code" gorm:"column:recommend_code;type:varchar(15);comment:我的推荐码"`
	RecommendID         null.String    `json:"recommend_id" gorm:"column:recommend_id;type:varchar(36);comment:推荐人ID"`
	Heat                int64          `json:"heat" gorm:"column:heat;type:bigint(20);comment:热度"`
	UserType            int8           `json:"user_type" gorm:"column:user_type;type:tinyint(4);default:1;comment:用户类型"`
	FollowCount         int64          `json:"follow_count" gorm:"column:follow_count;type:int(11);default:0;comment:跟随者数量"`
	LikeCount           int64          `json:"like_count" gorm:"column:like_count;type:int(11);default:0;comment:点赞数量"`
	TotalVideos         int64          `json:"total_videos" gorm:"column:total_videos;type:int(11);default:0;comment:发表总视频数"`
	TotalAlbums         int64          `json:"total_albums" gorm:"column:total_albums;type:int(11);default:0;comment:发表总专辑数"`
	TotalShorts         int64          `json:"total_shorts" gorm:"column:total_shorts;type:int(11);default:0;comment:发表总短视频数"`
	TotalPosts          int64          `json:"total_posts" gorm:"column:total_posts;type:int(11);default:0;comment:发表总帖子数"`
	TotalViews          int64          `json:"total_views" gorm:"column:total_views;type:int(11);default:0;comment:总浏览量"`
	TotalComments       int64          `json:"total_comments" gorm:"column:total_comments;type:int(11);default:0;comment:发表总评论数"`
	TotalLikes          int64          `json:"total_likes" gorm:"column:total_likes;type:int(11);default:0;comment:给别人点赞总次数"`
	TotalFollowing      int64          `json:"total_following" gorm:"column:total_following;type:int(11);default:0;comment:关注别人人数"`
	RegTime             types.JSONTime `json:"reg_time" gorm:"column:reg_time;type:datetime;comment:注册时间"`
	LastLoginTime       types.JSONTime `json:"last_login_time" gorm:"column:last_login_time;type:datetime;comment:最后登录时间"`
	LastActiveTime      types.JSONTime `json:"last_active_time" gorm:"column:last_active_time;type:datetime;comment:最后活跃时间"`
	FirstLoginIP        null.String    `json:"first_login_ip" gorm:"column:first_login_ip;type:varchar(50);comment:首次登录IP"`
	FirstLoginDevice    null.String    `json:"first_login_device" gorm:"column:first_login_device;type:varchar(255);comment:首次登录设备"`
	Score               int64          `json:"score" gorm:"column:score;type:int(11);default:0;comment:会员积分"`
	IsVerified          int8           `json:"is_verified" gorm:"column:is_verified;type:tinyint(1);default:0;comment:是否认证：0-未认证，1-已认证"`
	IsContentCreator    int8           `json:"is_content_creator" gorm:"column:is_content_creator;type:tinyint(1);default:0;comment:是否为内容创作者:0-否,1-是"`
	CreatorLevel        int64          `json:"creator_level" gorm:"column:creator_level;type:int(11);default:0;comment:创作者等级"`
	TotalCoinConsumed   int64          `json:"total_coin_consumed" gorm:"column:total_coin_consumed;type:int(11);default:0;comment:总消费平台币"`
	TotalPointsEarned   int64          `json:"total_points_earned" gorm:"column:total_points_earned;type:int(11);default:0;comment:总获得积分"`
	CreatorVerifiedTime types.JSONTime `json:"creator_verified_time" gorm:"column:creator_verified_time;type:datetime;comment:创作者认证时间"`
	Status              int8           `json:"status" gorm:"column:status;type:tinyint(1);default:1;comment:状态：0-禁用，1-正常"`
	Online              int8           `json:"online" gorm:"column:online;type:tinyint(1);default:0;comment:1.在线，0.离线"`
	IsLiked             bool           `json:"is_liked" gorm:"-" comment:"是否点赞"`
	IsFollowed          bool           `json:"is_followed" gorm:"-" comment:"是否关注"`
}

func (User) TableName() string {
	return "ly_users"
}

// GetCreatedAt 获取创建时间 - 用户表使用reg_time作为创建时间
func (u User) GetCreatedAt() types.JSONTime {
	return u.RegTime
}

// SetCreatedAt 设置创建时间 - 用户表使用reg_time作为创建时间
func (u *User) SetCreatedAt(time types.JSONTime) {
	u.RegTime = time
}

// GetUpdatedAt 重写获取更新时间方法
func (u User) GetUpdatedAt() types.JSONTime {
	return u.BaseModelStruct.UpdatedAt
}

// SetUpdatedAt 重写设置更新时间方法
func (u *User) SetUpdatedAt(time types.JSONTime) {
	u.BaseModelStruct.UpdatedAt = time
}

type UserComment struct {
	UserID       string         `json:"user_id"`       // 用户ID
	Content      string         `json:"content"`       // 评论内容
	UserNickname string         `json:"user_nickname"` // 用户昵称
	UserAvatar   string         `json:"user_avatar"`   // 用户头像
	ParentID     string         `json:"parent_id"`     // 父级ID
	EntityID     string         `json:"entity_id"`     // 关联实体ID
	EntityType   int8           `json:"entity_type"`   // 关联实体类型
	RelationID   string         `json:"relation_id"`   // 关联ID
	Heat         int64          `json:"heat"`          // 热度
	LikeCount    int64          `json:"like_count"`    // 点赞数
	ReplyCount   int64          `json:"reply_count"`   // 回复数
	CreatedAt    types.JSONTime `json:"created_at"`    // 创建时间
	UpdatedAt    types.JSONTime `json:"updated_at"`    // 更新时间
	Status       int8           `json:"status"`        // 状态：0-禁用，1-正常
}
