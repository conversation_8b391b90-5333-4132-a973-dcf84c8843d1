<!--
  现代化评论组件
  通过props接收数据，不直接调用API
  提供优雅的设计和流畅的用户体验
-->
<template>
  <div class="modern-comment-wrapper" :class="{ 'theme-dark': isDarkMode }">
    <!-- 评论概览区域 -->
    <div class="comment-overview">
      <div class="comment-header" @click="handleHeaderClick">
        <div class="comment-meta">
          <div class="comment-icon">
            <el-icon :size="20"><ChatDotRound /></el-icon>
          </div>
          <div class="comment-info">
            <span class="comment-count">{{ formatCount(totalComments || 0) }}</span>
            <span class="comment-label">条评论</span>
          </div>
        </div>
        
        <div class="comment-actions">
          <el-button 
            v-if="!isExpanded" 
            type="primary" 
            :icon="ArrowDown"
            @click="handleToggleExpand"
            class="expand-btn"
            :loading="loading"
          >
            展开评论
          </el-button>
          
          <el-button 
            v-if="isExpanded" 
            text 
            :icon="ArrowUp"
            @click="handleToggleExpand"
            class="collapse-btn"
          >
            收起评论
          </el-button>
          
          <el-button 
            v-if="!isExpanded && !disableDialog"
            text 
            :icon="Edit"
            @click="openCommentDialog"
            class="write-btn"
          >
            写评论
          </el-button>
        </div>
      </div>
    </div>

    <!-- 展开的评论区域 -->
    <el-collapse-transition>
      <div v-if="isExpanded" class="comment-expanded-area">
        <!-- 评论输入区（始终显示给已登录用户） -->
        <div v-if="userStore.isLoggedIn" class="comment-input-section">
          <ModernCommentInput
            :target-id="targetId"
            :target-type="targetType"
            :placeholder="placeholder"
            :support-image="supportImage"
            :support-video="supportVideo"
            @submit="handleCommentSubmit"
          />
        </div>
        
        <!-- 未登录用户的评论输入提示（仅在有评论时显示） -->
        <div v-if="!userStore.isLoggedIn && displayComments.length > 0" class="login-prompt-input">
          <div class="input-mock" @click="handleNonLoggedUserComment">
            <div class="input-placeholder">
              <el-icon><Edit /></el-icon>
              <span>{{ placeholder }}</span>
            </div>
            <div class="login-hint">点击登录后发表评论</div>
          </div>
        </div>
        
        <!-- 评论列表区 -->
        <div v-if="displayComments.length > 0" class="comment-list-section">
          <ModernCommentList
            :comments="displayComments"
            :loading="loading"
            :has-more="hasMore"
            @like="handleCommentLike"
            @reply="handleCommentReply"
            @load-more="handleLoadMore"
          />
          
          <!-- 已登录用户在有评论时的底部输入框 -->
          <div v-if="userStore.isLoggedIn" class="bottom-comment-input">
                       <ModernCommentInput
             :target-id="targetId"
             :target-type="targetType"
             :placeholder="'加入讨论...'"
             :support-image="supportImage"
             :support-video="supportVideo"
             @submit="handleCommentSubmit"
           />
          </div>
          
          <!-- 未登录用户在有评论时的登录提示 -->
          <div v-if="!userStore.isLoggedIn" class="login-prompt-bottom">
            <div class="login-prompt-content-simple">
              <span class="login-text">想要参与讨论？</span>
              <el-button type="primary" size="small" @click="userStore.showAuthDialog('login')">
                立即登录
              </el-button>
              <el-button size="small" @click="userStore.showAuthDialog('register')">
                注册账号
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 空状态提示（没有评论时） -->
        <div v-if="displayComments.length === 0" class="empty-comments-hint">
          <div class="empty-icon">
            <el-icon :size="48"><ChatDotRound /></el-icon>
          </div>
          <p class="empty-text">{{ userStore.isLoggedIn ? '还没有评论，来说点什么吧~' : '还没有评论' }}</p>
          
          <!-- 未登录用户的登录提示 -->
          <div v-if="!userStore.isLoggedIn" class="empty-login-actions">
            <el-button type="primary" @click="userStore.showAuthDialog('login')">
              登录后发表评论
            </el-button>
            <el-button @click="userStore.showAuthDialog('register')">
              注册账号
            </el-button>
          </div>
        </div>
      </div>
    </el-collapse-transition>

    <!-- 弹窗评论模式 -->
    <el-dialog
      v-if="!disableDialog"
      v-model="dialogVisible"
      :title="`评论 (${formatCount(totalComments || 0)})`"
      width="800px"
      :before-close="handleDialogClose"
      class="modern-comment-dialog"
      :close-on-click-modal="false"
      append-to-body
      destroy-on-close
    >
      <div class="dialog-comment-content">
        <div v-if="userStore.isLoggedIn" class="dialog-comment-input">
          <ModernCommentInput
            :target-id="targetId"
            :target-type="targetType"
            :placeholder="placeholder"
            :support-image="supportImage"
            :support-video="supportVideo"
            @submit="handleCommentSubmit"
          />
        </div>
        
        <!-- 弹窗中的未登录提示 -->
        <div v-else class="dialog-login-prompt">
          <div class="login-prompt-content">
            <el-icon :size="48" class="login-icon"><User /></el-icon>
            <p class="login-text">请先登录后再发表评论</p>
            <div class="login-actions">
              <el-button type="primary" @click="userStore.showAuthDialog('login'); dialogVisible = false">
                立即登录
              </el-button>
              <el-button @click="userStore.showAuthDialog('register'); dialogVisible = false">
                注册账号
              </el-button>
            </div>
          </div>
        </div>
        
        <div class="dialog-comment-list">
          <ModernCommentList
            :comments="displayComments"
            :loading="loading"
            :has-more="hasMore"
            @like="handleCommentLike"
            @reply="handleCommentReply"
            @load-more="handleLoadMore"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { 
  ElButton, 
  ElDialog, 
  ElIcon, 
  ElCollapseTransition,
  ElMessage 
} from 'element-plus'
import { 
  ChatDotRound, 
  ArrowDown, 
  ArrowUp, 
  Edit,
  User,
  ChatDotSquare
} from '@element-plus/icons-vue'
import ModernCommentInput from './ModernCommentInput.vue'
import ModernCommentList from './ModernCommentList.vue'
import { useUserStore } from '@/stores/user'
import type { Comment, CommentSubmitData, CommentBoxProps, CommentBoxEmits } from '@/types/comment'

// 组件属性
const props = withDefaults(defineProps<CommentBoxProps>(), {
  mode: 'inline',
  autoExpand: false,
  maxHeight: '600px',
  disableDialog: false,
  supportHeaderCollapse: true,
  supportImage: true,
  supportVideo: true,
  placeholder: '分享你的想法...',
  maxComments: 100,
  theme: 'auto',
  allowAnonymous: false,
  requireLogin: true,
  comments: () => [],
  totalComments: 0,
  loading: false,
  hasMore: false
})

// 组件事件
const emit = defineEmits<CommentBoxEmits>()

// 响应式状态
const isExpanded = ref(props.autoExpand)
const dialogVisible = ref(false)
const hasLoadedOnce = ref(false) // 标记是否已经加载过数据

// 用户状态
const userStore = useUserStore()

// 计算属性
const isDarkMode = computed(() => {
  if (props.theme === 'dark') return true
  if (props.theme === 'light') return false
  // 这里可以添加系统主题检测逻辑
  return false
})

// 显示的评论列表
const displayComments = computed(() => {
  return props.comments || []
})

// 工具函数
const formatCount = (count: number): string => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`
  }
  if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k`
  }
  return count.toString()
}

// 处理头部点击
const handleHeaderClick = (event: Event) => {
  if (!props.supportHeaderCollapse) {
    return
  }
  event.stopPropagation()
  // 不自动展开，需要用户明确点击展开按钮
  isExpanded.value = !isExpanded.value
  emit('expanded-changed', isExpanded.value)
}

// 处理展开/收起切换
const handleToggleExpand = (event: Event) => {
  event.stopPropagation()
  
  isExpanded.value = !isExpanded.value
  emit('expanded-changed', isExpanded.value)
  
  // 如果是首次展开且没有加载过数据，触发加载数据事件
  if (isExpanded.value && !hasLoadedOnce.value) {
    hasLoadedOnce.value = true
    emit('load-comments', 1)
  }
}

// 打开评论弹窗或展开评论区域
const openCommentDialog = (event: Event) => {
  event.stopPropagation()
  
  // 如果禁用弹窗模式，直接展开评论区域
  if (props.disableDialog) {
    isExpanded.value = true
    emit('expanded-changed', isExpanded.value)
    
    // 如果是首次展开且没有加载过数据，触发加载数据事件
    if (!hasLoadedOnce.value) {
      hasLoadedOnce.value = true
      emit('load-comments', 1)
    }
    return
  }
  
  // 默认行为：打开弹窗
  dialogVisible.value = true
  emit('dialog-opened')
  
  // 如果没有加载过数据，触发加载数据事件
  if (!hasLoadedOnce.value) {
    hasLoadedOnce.value = true
    emit('load-comments', 1)
  }
}

// 关闭评论弹窗
const handleDialogClose = () => {
  dialogVisible.value = false
  emit('dialog-closed')
}

// 处理评论提交
const handleCommentSubmit = (data: CommentSubmitData) => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    userStore.showAuthDialog('login')
    return
  }
  
  // 不直接处理，而是发出事件让父组件处理
  emit('comment-added', {
    content: data.content,
    targetId: props.targetId,
    targetType: props.targetType,
    parentId: data.parentId,
    media: data.media
  })
}

// 处理未登录用户点击评论输入区
const handleNonLoggedUserComment = () => {
  userStore.showAuthDialog('login')
}

// 处理评论点赞
const handleCommentLike = (commentId: string) => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    userStore.showAuthDialog('login')
    return
  }
  
  const comment = findCommentById(commentId)
  if (!comment) return
  
  // 发出事件让父组件处理
  emit('comment-liked', commentId, !comment.isLiked)
}

// 处理评论回复
const handleCommentReply = (parentId: string, replyData: CommentSubmitData) => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    userStore.showAuthDialog('login')
    return
  }
  
  // 发出事件让父组件处理
  emit('comment-replied', parentId, {
    content: replyData.content,
    targetId: props.targetId,
    targetType: props.targetType,
    parentId: parentId,
    media: replyData.media
  })
}

// 处理加载更多
const handleLoadMore = () => {
  emit('load-more')
}

// 查找评论
const findCommentById = (id: string): Comment | undefined => {
  for (const comment of displayComments.value) {
    if (comment.id === id) return comment
    if (comment.replies) {
      const reply = comment.replies.find(r => r.id === id)
      if (reply) return reply
    }
  }
  return undefined
}

// 监听器
watch(() => props.targetId, () => {
  // 当目标ID变化时，重置状态
  hasLoadedOnce.value = false
  isExpanded.value = props.autoExpand
}, { immediate: false })

// 生命周期
onMounted(() => {
  if (props.mode === 'dialog') {
    dialogVisible.value = true
  }
  
  // 如果设置了自动展开且有评论数据，标记为已加载
  if (props.autoExpand && props.comments && props.comments.length > 0) {
    hasLoadedOnce.value = true
  }
})

// 暴露方法
defineExpose({
  openDialog: openCommentDialog,
  refresh: () => {
    hasLoadedOnce.value = false
    emit('refresh')
  },
  expand: () => {
    if (!isExpanded.value) {
      handleToggleExpand(new Event('click'))
    }
  },
  collapse: () => {
    isExpanded.value = false
    emit('expanded-changed', false)
  },
  // 重置状态
  reset: () => {
    hasLoadedOnce.value = false
    isExpanded.value = props.autoExpand
    dialogVisible.value = false
  }
})
</script>

<style scoped lang="scss">
@use '@/styles/theme.scss' as theme;

.modern-comment-wrapper {
  --comment-bg: #ffffff;
  --comment-border: #e4e7ed;
  --comment-text: #303133;
  --comment-text-light: #606266;
  --comment-text-lighter: #909399;
  --comment-hover: #f5f7fa;
  --comment-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --comment-radius: 8px;
  --comment-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.theme-dark {
    --comment-bg: #1a1a1a;
    --comment-border: #303133;
    --comment-text: #e4e7ed;
    --comment-text-light: #c0c4cc;
    --comment-text-lighter: #909399;
    --comment-hover: #262727;
    --comment-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  }

  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.comment-overview {
  background: var(--comment-bg);
  border: 1px solid var(--comment-border);
  border-top-left-radius: var(--comment-radius);
  border-top-right-radius: var(--comment-radius);
  padding: 0;
  overflow: hidden;
  transition: var(--comment-transition);
  
  &:hover {
    border-color: var(--el-color-primary);
    box-shadow: var(--comment-shadow);
  }
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f5f7fa 100%);
  cursor: pointer;
  transition: var(--comment-transition);
  
  .theme-dark & {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }

  &:hover {
    background: linear-gradient(135deg, #f0f2ff 0%, #e8ebf0 100%);
    transform: translateY(-1px);
    
    .theme-dark & {
      background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    }
  }
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--el-color-primary) 0%, #667eea 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.comment-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2px;
}

.comment-count {
  font-size: 18px;
  font-weight: 700;
  color: var(--comment-text);
  line-height: 1;
}

.comment-label {
  font-size: 12px;
  color: var(--comment-text-lighter);
  font-weight: 500;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .expand-btn, .collapse-btn, .write-btn {
    border-radius: 20px;
    font-weight: 500;
    transition: var(--comment-transition);
    
    &:hover {
      transform: translateY(-1px);
    }
  }
  
  .expand-btn {
    background: linear-gradient(135deg, var(--el-color-primary) 0%, #667eea 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    
    &:hover {
      box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
    }
  }
  
  .collapse-btn {
    color: var(--comment-text-light);
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
  
  .write-btn {
    color: var(--comment-text-light);
    
    &:hover {
      color: var(--el-color-primary);
      background: rgba(64, 158, 255, 0.1);
    }
  }
}

.comment-expanded-area {
  padding: 20px;
  background: var(--comment-bg);
  border-top: 1px solid var(--comment-border);
}

.comment-input-section {
  margin-bottom: 20px;
}

.login-prompt-input {
  margin-bottom: 20px;

  .input-mock {
    border: 1px solid var(--comment-border);
    border-radius: 8px;
    padding: 12px 16px;
    background: var(--comment-hover);
    cursor: pointer;
    transition: var(--comment-transition);

    &:hover {
      border-color: var(--el-color-primary);
      background: var(--comment-bg);
    }

    .input-placeholder {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--comment-text-lighter);
      font-size: 14px;

      .el-icon {
        font-size: 16px;
      }
    }

    .login-hint {
      margin-top: 8px;
      font-size: 12px;
      color: var(--el-color-primary);
    }
  }
}

.comment-list-section {
  // 评论列表样式
  
  .bottom-comment-input {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--comment-border);
  }

  .login-prompt-bottom {
    margin-top: 16px;
    padding: 12px;
    background: linear-gradient(135deg, #f8f9ff 0%, #f5f7fa 100%);
    border-radius: 8px;
    text-align: center;

    .theme-dark & {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .login-prompt-content-simple {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      flex-wrap: wrap;

      .login-text {
        font-size: 14px;
        color: var(--comment-text-light);
      }
    }
  }
}

.modern-comment-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #f8f9ff 0%, #f5f7fa 100%);
    border-bottom: 1px solid var(--comment-border);
    border-radius: 8px 8px 0 0;
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.dialog-comment-content {
  max-height: 600px;
  overflow-y: auto;
}

.dialog-comment-input {
  padding: 20px;
  border-bottom: 1px solid var(--comment-border);
  background: var(--comment-bg);
}

.dialog-comment-list {
  padding: 20px;
  background: var(--comment-bg);
}

// 登录提示样式
.login-prompt {
  padding: 40px 20px;
  text-align: center;
  background: var(--comment-bg);
}

.login-prompt-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 300px;
  margin: 0 auto;
}

.login-icon {
  color: var(--comment-text-lighter);
  opacity: 0.6;
}

.login-text {
  color: var(--comment-text-light);
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.login-actions {
  display: flex;
  gap: 12px;
  
  .el-button {
    border-radius: 20px;
    font-weight: 500;
    transition: var(--comment-transition);
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

.dialog-login-prompt {
    padding: 60px 20px;
    text-align: center;
    background: var(--comment-bg);
    
    .login-prompt-content {
      gap: 20px;
    }
    
    .login-icon {
      color: var(--comment-text-lighter);
      opacity: 0.5;
    }
    
    .login-text {
      font-size: 16px;
      color: var(--comment-text);
    }
  }

  // 空评论状态样式
  .empty-comments-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    margin-top: 20px;
  }

  .empty-icon {
    margin-bottom: 16px;
    color: var(--comment-text-lighter);
    opacity: 0.6;
  }

  .empty-text {
    color: var(--comment-text-light);
    font-size: 14px;
    margin: 0 0 16px 0;
    line-height: 1.5;
  }

  .empty-login-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
    
    .el-button {
      border-radius: 20px;
      font-weight: 500;
      transition: var(--comment-transition);
      
      &:hover {
        transform: translateY(-1px);
      }
    }
  }

// 响应式设计
@media (max-width: 768px) {
  .comment-header {
    padding: 12px 16px;
    
    .comment-actions {
      gap: 4px;
      
      .expand-btn, .collapse-btn, .write-btn {
        font-size: 12px;
        padding: 6px 12px;
      }
    }
  }
  
  .comment-expanded-area {
    padding: 16px;
  }
  
  .modern-comment-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 20px auto;
    }
  }
}
</style>