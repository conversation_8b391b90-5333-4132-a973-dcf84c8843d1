<template>
  <div class="pagination-container">
    <el-pagination
      :current-page="current"
      :page-size="size"
      :total="total" background
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// 定义属性
const props = defineProps<{
  total: number;
  current: number;
  size: number;
}>();

// 定义事件
const emit = defineEmits<{
  pagination: [params: { page: number; pageSize: number }];
}>();

// 处理页码变化
const handleCurrentChange = (page: number) => {
  emit('pagination', { page, pageSize: props.size });
};

// 处理页面大小变化
const handleSizeChange = (pageSize: number) => {
  emit('pagination', { page: 1, pageSize });
};
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}
</style> 