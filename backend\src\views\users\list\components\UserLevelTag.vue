<template>
  <el-tag 
    :type="tagType" 
    :effect="effect"
    size="small"
    class="user-level-tag"
  >
    <el-icon v-if="levelIcon"><component :is="levelIcon" /></el-icon>
    {{ levelText }}
  </el-tag>
</template>

<script setup lang="ts">
import { Star, Trophy, User } from '@element-plus/icons-vue';
import { computed } from 'vue';

// Props
interface Props {
  level?: number | null | undefined;
  effect?: 'dark' | 'light' | 'plain';
}

const props = withDefaults(defineProps<Props>(), {
  level: 1,
  effect: 'light'
});

// 计算属性
const levelConfig = computed(() => {
  const actualLevel = props.level || 1;
  switch (actualLevel) {
    case 1:
      return {
        text: '普通用户',
        type: 'info' as const,
        icon: User
      };
    case 2:
      return {
        text: 'VIP用户',
        type: 'success' as const,
        icon: Trophy
      };
    case 3:
      return {
        text: '蓝标用户',
        type: 'primary' as const,
        icon: Star
      };
    default:
      return {
        text: '普通用户',
        type: 'info' as const,
        icon: User
      };
  }
});

const levelText = computed(() => levelConfig.value.text);
const tagType = computed(() => levelConfig.value.type);
const levelIcon = computed(() => levelConfig.value.icon);
</script>

<style scoped lang="scss">
.user-level-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;

  .el-icon {
    font-size: 12px;
  }
}
</style> 