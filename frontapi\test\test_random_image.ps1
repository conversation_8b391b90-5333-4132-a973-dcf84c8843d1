# 随机图片生成功能测试脚本

Write-Host "=== 随机图片生成功能测试 ===" -ForegroundColor Green

# 测试健康检查
Write-Host "`n1. 测试健康检查端点..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:8082/health" -UseBasicParsing
    Write-Host "健康检查状态码: $($healthResponse.StatusCode)" -ForegroundColor Green
    Write-Host "响应内容: $($healthResponse.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试随机图片生成
Write-Host "`n2. 测试随机图片生成..." -ForegroundColor Yellow

$testUrls = @(
    "http://localhost:8082/image/random/800/600/test1.jpg",
    "http://localhost:8082/image/random/400/300/test2.png",
    "http://localhost:8082/image/random/500/500/test3.jpeg"
)

foreach ($url in $testUrls) {
    Write-Host "`n测试URL: $url" -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "内容类型: $($response.Headers['Content-Type'])" -ForegroundColor Green
        Write-Host "内容长度: $($response.Headers['Content-Length']) bytes" -ForegroundColor Green
        
        # 保存图片到临时文件进行验证
        $filename = Split-Path $url -Leaf
        $tempPath = "temp_$filename"
        [System.IO.File]::WriteAllBytes($tempPath, $response.Content)
        $fileInfo = Get-Item $tempPath
        Write-Host "保存文件: $tempPath (大小: $($fileInfo.Length) bytes)" -ForegroundColor Green
        
        # 清理临时文件
        Remove-Item $tempPath -Force
        
    } catch {
        Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试错误情况
Write-Host "`n3. 测试错误处理..." -ForegroundColor Yellow

$errorTests = @(
    @{ url = "http://localhost:8082/image/random/3000/600/test.jpg"; desc = "宽度超出范围" },
    @{ url = "http://localhost:8082/image/random/800/3000/test.jpg"; desc = "高度超出范围" },
    @{ url = "http://localhost:8082/image/random/800/600/test.txt"; desc = "不支持的格式" },
    @{ url = "http://localhost:8082/image/random/abc/600/test.jpg"; desc = "无效宽度" }
)

foreach ($test in $errorTests) {
    Write-Host "`n测试错误情况: $($test.desc)" -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $test.url -UseBasicParsing
        Write-Host "意外成功: 状态码 $($response.StatusCode)" -ForegroundColor Yellow
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        Write-Host "预期错误: 状态码 $statusCode" -ForegroundColor Green
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green 