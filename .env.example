# 应用配置
APP_ENV=development
APP_DEBUG=true
APP_PORT=8080

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=my_app
DB_USERNAME=root
DB_PASSWORD=password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=86400

# 文件上传配置
# 小图片（分类图标、头像等）
UPLOAD_SMALL_IMAGE_PATH=./storage/static/images
UPLOAD_SMALL_IMAGE_URL=/static/images

# 图片模块的图片
UPLOAD_PICTURE_PATH=./storage/pictures
UPLOAD_PICTURE_URL=/pictures

# 视频
UPLOAD_VIDEO_PATH=./storage/videos
UPLOAD_VIDEO_URL=/videos

# 也可以配置为对象存储的路径
# UPLOAD_SMALL_IMAGE_PATH=/path/to/oss/bucket/images
# UPLOAD_SMALL_IMAGE_URL=https://cdn.example.com/images

# UPLOAD_PICTURE_PATH=/path/to/oss/bucket/pictures
# UPLOAD_PICTURE_URL=https://cdn.example.com/pictures

# UPLOAD_VIDEO_PATH=/path/to/oss/bucket/videos
# UPLOAD_VIDEO_URL=https://cdn.example.com/videos 