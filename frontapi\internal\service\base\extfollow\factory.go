package extfollow

import (
	"context"
	"frontapi/internal/service/base/extfollow/mongodb"
	"frontapi/internal/service/base/extfollow/redis"
	"frontapi/internal/service/base/extfollow/types"
	"time"

	goredis "github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"
)

// NewExtendedService 创建扩展关注服务实例
func NewExtendedService(config *Config) (ExtendedFollowService, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// 如果没有提供Redis客户端，使用配置创建一个
	var redisClient *goredis.Client
	if config.Redis.Client != nil {
		redisClient = config.Redis.Client
	} else {
		// 创建一个新的Redis客户端
		redisClient = goredis.NewClient(&goredis.Options{
			Addr:     config.RedisAddr,
			Password: config.RedisPassword,
			DB:       config.RedisDB,
		})
	}

	// 使用现有的FollowService创建函数
	service := NewFollowService(redisClient, &ServiceConfig{
		StorageType:   config.StorageType,
		DefaultTTL:    config.DefaultTTL,
		BatchSize:     config.BatchSize,
		EnableMetrics: config.EnableMetrics,
		EnableSync:    config.EnableSync,
	})

	return service, nil
}

// CreateFollowService 创建关注服务工厂函数
func CreateFollowService(redisClient *goredis.Client, storageType string) FollowService {
	var adapter FollowAdapter

	switch storageType {
	case "redis":
		config := redis.DefaultConfig()
		adapter = redis.NewRedisAdapter(redisClient, config)
	default:
		config := redis.DefaultConfig()
		adapter = redis.NewRedisAdapter(redisClient, config)
	}

	return &followServiceImpl{
		adapter: adapter,
	}
}

// CreateFollowServiceWithMongo 创建带MongoDB支持的关注服务
func CreateFollowServiceWithMongo(redisClient *goredis.Client, mongoClient *mongo.Client, mongoDatabase *mongo.Database, storageType string) FollowService {
	var adapter FollowAdapter

	switch storageType {
	case "redis":
		config := redis.DefaultConfig()
		adapter = redis.NewRedisAdapter(redisClient, config)
	case "mongodb":
		config := &mongodb.MongoConfig{
			Database:   "frontapi_follow",
			Collection: "follows",
		}
		mongoAdapter, err := mongodb.NewMongoAdapter(mongoClient, mongoDatabase, config)
		if err != nil {
			// 如果MongoDB创建失败，回退到Redis
			redisConfig := redis.DefaultConfig()
			adapter = redis.NewRedisAdapter(redisClient, redisConfig)
		} else {
			adapter = mongoAdapter
		}
	default:
		config := redis.DefaultConfig()
		adapter = redis.NewRedisAdapter(redisClient, config)
	}

	return &followServiceImpl{
		adapter: adapter,
	}
}

// followServiceImpl 简化的服务实现
type followServiceImpl struct {
	adapter FollowAdapter
}

// Follow 关注用户
func (s *followServiceImpl) Follow(ctx context.Context, followerID, followeeID string) error {
	if followerID == "" {
		return types.ErrInvalidFollowerID
	}
	if followeeID == "" {
		return types.ErrInvalidFolloweeID
	}
	if followerID == followeeID {
		return types.ErrSelfFollow
	}

	return s.adapter.Follow(ctx, followerID, followeeID)
}

// Unfollow 取消关注
func (s *followServiceImpl) Unfollow(ctx context.Context, followerID, followeeID string) error {
	if followerID == "" {
		return types.ErrInvalidFollowerID
	}
	if followeeID == "" {
		return types.ErrInvalidFolloweeID
	}
	if followerID == followeeID {
		return types.ErrSelfFollow
	}

	return s.adapter.Unfollow(ctx, followerID, followeeID)
}

// IsFollowing 检查是否关注
func (s *followServiceImpl) IsFollowing(ctx context.Context, followerID, followeeID string) (bool, error) {
	if followerID == "" || followeeID == "" {
		return false, nil
	}
	if followerID == followeeID {
		return false, nil
	}

	return s.adapter.IsFollowing(ctx, followerID, followeeID)
}

// GetFollowerCount 获取粉丝数
func (s *followServiceImpl) GetFollowerCount(ctx context.Context, userID string) (int64, error) {
	if userID == "" {
		return 0, types.ErrInvalidFolloweeID
	}

	return s.adapter.GetFollowerCount(ctx, userID)
}

// GetFollowingCount 获取关注数
func (s *followServiceImpl) GetFollowingCount(ctx context.Context, userID string) (int64, error) {
	if userID == "" {
		return 0, types.ErrInvalidFollowerID
	}

	return s.adapter.GetFollowingCount(ctx, userID)
}

// BatchFollow 批量关注
func (s *followServiceImpl) BatchFollow(ctx context.Context, operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	// 验证操作
	if err := validateBatchOperations(operations); err != nil {
		return err
	}

	return s.adapter.BatchFollow(ctx, operations)
}

// BatchUnfollow 批量取消关注
func (s *followServiceImpl) BatchUnfollow(ctx context.Context, operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	// 验证操作
	if err := validateBatchOperations(operations); err != nil {
		return err
	}

	return s.adapter.BatchUnfollow(ctx, operations)
}

// validateBatchOperations 验证批量操作（本地版本）
func validateBatchOperations(operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	for i, op := range operations {
		if op.FollowerID == "" {
			return types.ErrInvalidFollowerID
		}
		if op.FolloweeID == "" {
			return types.ErrInvalidFolloweeID
		}
		if op.FollowerID == op.FolloweeID {
			return types.ErrSelfFollow
		}
		// 设置默认操作时间
		if op.Timestamp.IsZero() {
			operations[i].Timestamp = time.Now()
		}
	}

	return nil
}

// 其他方法的简化实现...
func (s *followServiceImpl) BatchGetFollowStatus(ctx context.Context, followerID string, userIDs []string) (map[string]bool, error) {
	return s.adapter.BatchGetFollowStatus(ctx, followerID, userIDs)
}

func (s *followServiceImpl) BatchGetFollowerCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	return s.adapter.BatchGetFollowerCounts(ctx, userIDs)
}

func (s *followServiceImpl) BatchGetFollowingCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	return s.adapter.BatchGetFollowingCounts(ctx, userIDs)
}

func (s *followServiceImpl) GetUserFollowers(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	return s.adapter.GetUserFollowers(ctx, userID, limit, offset)
}

func (s *followServiceImpl) GetUserFollowing(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	return s.adapter.GetUserFollowing(ctx, userID, limit, offset)
}

func (s *followServiceImpl) GetFollowHistory(ctx context.Context, userID string, timeRange *types.TimeRange) ([]*types.FollowRecord, error) {
	return s.adapter.GetFollowHistory(ctx, userID, timeRange)
}

func (s *followServiceImpl) GetMutualFollows(ctx context.Context, userID1, userID2 string) ([]*types.FollowRecord, error) {
	return s.adapter.GetMutualFollows(ctx, userID1, userID2)
}

func (s *followServiceImpl) UpdateInfluenceRank(ctx context.Context, userID string, score float64) error {
	return s.adapter.UpdateInfluenceRank(ctx, userID, score)
}

func (s *followServiceImpl) GetInfluenceRanking(ctx context.Context, limit int) ([]string, error) {
	return s.adapter.GetInfluenceRanking(ctx, limit)
}

func (s *followServiceImpl) GetInfluenceRankingWithScores(ctx context.Context, limit int) (map[string]float64, error) {
	return s.adapter.GetInfluenceRankingWithScores(ctx, limit)
}

func (s *followServiceImpl) GetUserFollowStats(ctx context.Context, userID string) (*types.UserFollowStats, error) {
	return s.adapter.GetUserFollowStats(ctx, userID)
}

func (s *followServiceImpl) GetFollowTrends(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.FollowTrend, error) {
	return s.adapter.GetFollowTrends(ctx, timeRange, limit)
}

func (s *followServiceImpl) HealthCheck(ctx context.Context) error {
	return s.adapter.HealthCheck(ctx)
}

func (s *followServiceImpl) Close() error {
	return s.adapter.Close()
}
