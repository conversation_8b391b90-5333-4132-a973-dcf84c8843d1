# 缓存系统使用指南

本文档提供了对缓存系统的详细介绍和使用方法。经过性能优化的缓存系统提供了高效、可靠和易用的接口，支持多种缓存策略和适配器。

## 主要特性

1. **多层缓存架构**：支持本地内存缓存和远程缓存的组合，提供最佳性能
2. **多种适配器**：支持Redis、文件和BigCache三种缓存存储
3. **分片和集群**：支持数据分片和集群模式，提高并发性能
4. **键前缀管理**：自动管理键前缀，避免命名冲突
5. **高效序列化**：使用高性能JSON库，减少序列化开销
6. **数据压缩**：支持大型数据压缩，节省存储空间和网络带宽
7. **过期时间控制**：灵活的缓存项过期时间控制
8. **统计信息**：提供详细的缓存使用统计

## 快速开始

### 初始化缓存

在应用启动时初始化缓存系统：

```go
import (
    "frontapi/pkg/config"
    "frontapi/pkg/cache"
)

// 从配置文件加载
err := config.InitCacheFromFile("config/cache.yaml")
if err != nil {
    // 处理错误
}

// 或使用默认配置
err := config.InitDefaultCache()
if err != nil {
    // 处理错误
}
```

### 基本使用

获取和设置缓存值：

```go
import (
    "context"
    "time"
    "frontapi/pkg/cache"
)

// 获取默认适配器
adapter, err := cache.GetDefaultAdapter()
if err != nil {
    // 处理错误
}

// 设置缓存
ctx := context.Background()
err = adapter.Set(ctx, "user:123", []byte(`{"name":"张三","age":30}`), time.Hour)
if err != nil {
    // 处理错误
}

// 获取缓存
data, err := adapter.Get(ctx, "user:123")
if err != nil {
    if err == types.ErrNotFound {
        // 缓存未命中
    } else {
        // 处理其他错误
    }
}

// 使用数据
// ...
```

### 使用 JSON 工具

缓存系统提供了高效的 JSON 序列化工具：

```go
import "frontapi/pkg/cache"

// 序列化结构体
user := User{
    ID:   123,
    Name: "张三",
    Age:  30,
}

data, err := cache.Marshal(user)
if err != nil {
    // 处理错误
}

// 设置缓存
adapter.Set(ctx, "user:123", data, time.Hour)

// 获取并反序列化
data, err = adapter.Get(ctx, "user:123")
if err != nil {
    // 处理错误
}

var retrievedUser User
err = cache.Unmarshal(data, &retrievedUser)
if err != nil {
    // 处理错误
}
```

### 批量操作

```go
// 批量获取
keys := []string{"user:123", "user:456", "user:789"}
results, err := adapter.MGet(ctx, keys)
if err != nil {
    // 处理错误
}

for key, data := range results {
    // 处理每个结果
}

// 批量设置
items := map[string][]byte{
    "user:123": []byte(`{"name":"张三","age":30}`),
    "user:456": []byte(`{"name":"李四","age":25}`),
}
err = adapter.MSet(ctx, items, time.Hour)
if err != nil {
    // 处理错误
}
```

### 计数器操作

```go
// 递增计数器
newValue, err := adapter.Increment(ctx, "visits:page1", 1)
if err != nil {
    // 处理错误
}
fmt.Printf("页面访问次数: %d\n", newValue)

// 递减计数器
remaining, err := adapter.Decrement(ctx, "stock:item123", 1)
if err != nil {
    // 处理错误
}
fmt.Printf("剩余库存: %d\n", remaining)
```

## 配置说明

### 配置文件示例

完整的配置文件示例位于 `frontapi/pkg/cache/cache_config.yaml.example`。
您可以将其复制到 `config/cache.yaml` 并根据需要修改。

主要配置选项：

```yaml
cache:
  # 默认适配器: redis, file, bigcache
  default: "redis"
  
  # 默认缓存过期时间(秒)
  default_ttl: 3600
  
  # 是否启用本地内存缓存(作为二级缓存)
  enable_local_cache: true
  
  # 本地缓存大小(MB)
  local_cache_size: 100
  
  # 是否启用压缩
  enable_compression: true
  
  # 是否启用分片(提高性能和并发)
  enable_sharding: true
  
  # Redis配置
  redis:
    host: "localhost"
    port: 6379
    # ...其他 Redis 选项...
```

## 性能优化建议

1. **启用本地缓存**：对于频繁读取的数据，启用本地内存缓存可以显著提高性能
2. **合理设置连接池**：根据负载调整 Redis 连接池大小
3. **使用适当的分片数量**：分片数量通常设置为 CPU 核心数的 4-8 倍
4. **启用压缩**：对于大型数据，启用压缩可以减少网络和存储开销
5. **设置键前缀**：使用有意义的键前缀，便于管理和避免冲突

## 最佳实践

1. **合理设置过期时间**：避免使用永久缓存，为不同类型的数据设置合适的过期时间
2. **使用键前缀**：始终使用有意义的键前缀，如 `user:`, `product:` 等
3. **处理缓存未命中**：优雅地处理缓存未命中的情况，并重新填充缓存
4. **监控缓存统计**：定期检查缓存命中率和使用情况
5. **批量操作**：尽可能使用批量获取和设置操作，减少网络往返

## 故障排除

常见问题及解决方法：

1. **连接失败**：检查 Redis 服务器是否运行，以及网络设置是否正确
2. **内存使用过高**：调整本地缓存大小或 Redis 最大内存设置
3. **性能下降**：检查命中率统计，可能需要调整缓存策略或增加本地缓存
4. **键冲突**：使用更具体的键前缀避免命中其他应用的数据

## 高级功能

### 自定义适配器

您可以实现自己的缓存适配器：

```go
import "frontapi/pkg/cache/types"

// 实现 CacheAdapter 接口
type MyAdapter struct {
    // ...
}

// 实现必要的方法
func (a *MyAdapter) Get(ctx context.Context, key string) ([]byte, error) {
    // 实现逻辑
}

// ...其他方法实现...

// 添加到缓存管理器
func init() {
    adapter := &MyAdapter{}
    cache.GetGlobalManager().AddAdapter("my-adapter", adapter)
}
```

### 使用分片和集群

分片和集群配置可以提高性能和可用性：

```yaml
cache:
  enable_sharding: true
  shard_count: 32
  
  redis:
    cluster: true
    addrs:
      - "redis-1:6379"
      - "redis-2:6379"
      - "redis-3:6379"
```

## 接口更新说明

我们在优化缓存系统的过程中对接口进行了一些更新：

1. **上下文参数统一**：所有缓存操作方法现在统一使用`context.Context`而非`interface{}`作为上下文参数，提高了类型安全性。
   ```go
   // 旧版
   Get(ctx interface{}, key string) ([]byte, error)
   
   // 新版
   Get(ctx context.Context, key string) ([]byte, error)
   ```

2. **键前缀管理**：所有适配器现在都实现了`KeyWithPrefix`方法，用于自动添加前缀。
   ```go
   // 使用键前缀
   prefixedKey := adapter.KeyWithPrefix("user:profile")
   ```

3. **统计数据扩展**：CacheStats结构体增加了更多统计字段，包括`Size`、`ItemCount`和`EvictionCount`等。
   ```go
   stats := adapter.Stats()
   fmt.Printf("缓存大小: %d 字节\n", stats.Size)
   fmt.Printf("缓存条目数: %d\n", stats.ItemCount)
   ```

4. **分片和集群类型**：添加了`ClusterNode`和`ClusterConfig`等类型，支持分布式缓存配置。

## API 参考

完整的 API 文档请参考代码中的注释和接口定义。
主要接口和类型定义位于 `frontapi/pkg/cache/interfaces.go` 和 `frontapi/pkg/cache/types/cache_types.go`。 