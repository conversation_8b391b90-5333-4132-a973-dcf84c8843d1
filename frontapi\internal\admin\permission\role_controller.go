package permission

import (
	"frontapi/internal/admin"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

// RoleController 角色控制器（简化版，使用模拟数据）
type RoleController struct {
	admin.BaseController
}

func NewRoleController() *RoleController {
	return &RoleController{}
}

// RoleInfo 角色信息结构体
type RoleInfo struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Code   string `json:"code"`
	Value  string `json:"value"`
	Remark string `json:"remark"`
	Status int8   `json:"status"`
}

// CreateRole 创建角色
func (r *RoleController) CreateRole(c *fiber.Ctx) error {
	// 解析请求数据
	var req struct {
		Name   string `json:"name" validate:"required"`
		Code   string `json:"code" validate:"required"`
		Value  string `json:"value" validate:"required"`
		Remark string `json:"remark"`
		Status int8   `json:"status"`
	}

	err := validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return r.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 模拟创建角色
	role := RoleInfo{
		ID:     100, // 模拟生成的ID
		Name:   req.Name,
		Code:   req.Code,
		Value:  req.Value,
		Remark: req.Remark,
		Status: req.Status,
	}

	return utils.Success(c, fiber.Map{
		"id": role.ID,
	}, "创建角色成功")
}

// UpdateRole 更新角色
func (r *RoleController) UpdateRole(c *fiber.Ctx) error {
	// 获取角色ID
	roleId, err := r.GetId(c)
	if err != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}
	_, err2 := strconv.Atoi(roleId)
	if err2 != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}

	// 解析请求数据
	var req struct {
		Name   string `json:"name"`
		Code   string `json:"code"`
		Value  string `json:"value"`
		Remark string `json:"remark"`
		Status int8   `json:"status"`
	}

	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 模拟更新成功
	return r.SuccessWithMessage(c, "更新角色成功")
}

// DeleteRole 删除角色
func (r *RoleController) DeleteRole(c *fiber.Ctx) error {
	// 获取角色ID
	roleId, err := r.GetId(c)
	if err != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}
	_, err2 := strconv.Atoi(roleId)
	if err2 != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}

	// 模拟删除成功
	return utils.Success(c, nil, "删除角色成功")
}

// GetRole 获取角色详情
func (r *RoleController) GetRole(c *fiber.Ctx) error {
	// 获取角色ID
	roleId, err := r.GetId(c)
	if err != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}
	_, err2 := strconv.Atoi(roleId)
	if err2 != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}

	// 模拟角色数据
	role := RoleInfo{
		ID:     1,
		Name:   "管理员",
		Code:   "admin",
		Value:  "admin",
		Remark: "系统管理员",
		Status: 1,
	}

	// 模拟菜单ID列表
	menuIDs := []int{1, 2, 3, 4, 5}

	// 模拟权限ID列表
	permIDs := []string{"1", "2", "3", "4", "5"}

	return utils.Success(c, fiber.Map{
		"role":        role,
		"menuIds":     menuIDs,
		"permissions": permIDs,
	}, "获取角色成功")
}

// ListRoles 获取角色列表
func (r *RoleController) ListRoles(c *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Name   string `json:"name"`
		Code   string `json:"code"`
		Status int    `json:"status"`
		Page   int    `json:"page" default:"1"`
		Size   int    `json:"size" default:"10"`
	}

	if err := c.BodyParser(&req); err != nil {
		req.Page = 1
		req.Size = 10
	}

	// 模拟角色列表数据
	roles := []RoleInfo{
		{ID: 1, Name: "管理员", Code: "admin", Value: "admin", Remark: "系统管理员", Status: 1},
		{ID: 2, Name: "编辑", Code: "editor", Value: "editor", Remark: "内容编辑", Status: 1},
		{ID: 3, Name: "查看者", Code: "viewer", Value: "viewer", Remark: "只读权限", Status: 1},
	}

	return utils.Success(c, fiber.Map{
		"list":  roles,
		"total": len(roles),
		"page":  req.Page,
		"size":  req.Size,
	}, "获取角色列表成功")
}

// UpdateRoleMenus 更新角色菜单权限
func (r *RoleController) UpdateRoleMenus(c *fiber.Ctx) error {
	// 获取角色ID
	_, err := r.GetId(c)
	if err != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}

	// 解析请求参数
	var req struct {
		MenuIDs []int `json:"menuIds"`
	}

	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 模拟更新成功
	return utils.Success(c, nil, "更新角色菜单权限成功")
}

// UpdateRolePermissions 更新角色操作权限
func (r *RoleController) UpdateRolePermissions(c *fiber.Ctx) error {
	// 获取角色ID
	_, err := r.GetId(c)
	if err != nil {
		return r.BadRequest(c, "无效的角色ID", nil)
	}

	// 解析请求参数
	var req struct {
		PermissionIDs []string `json:"permissionIds"`
	}

	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 模拟更新成功
	return utils.Success(c, nil, "更新角色操作权限成功")
}
