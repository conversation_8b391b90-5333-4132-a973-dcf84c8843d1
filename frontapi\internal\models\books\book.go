package books

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// Book 电子书模型
type Book struct {
	*models.ContentBaseModel
	Cover         string            `json:"cover" gorm:"type:string;not null"`
	Rating        float64           `json:"rating" gorm:"type:decimal(3,1);default:0.0"`
	Author        string            `json:"author" gorm:"type:string;not null"`
	WordCount     uint64            `json:"word_count" gorm:"type:bigint unsigned;default:0"`
	ChapterCount  int               `json:"chapter_count" gorm:"type:int;default:0"`
	ReadCount     uint64            `json:"read_count" gorm:"type:bigint unsigned;default:0"`
	Score         float64           `json:"score" gorm:"type:decimal(3,1);default:0.0"`
	FavoriteCount uint64            `json:"favorite_count" gorm:"type:bigint unsigned;default:0"`
	Progress      string            `json:"progress" gorm:"type:string;default:'completed'"`
	IsPaid        uint8             `json:"is_paid" gorm:"type:tinyint(1);default:0"`
	IsFeatured    uint8             `json:"is_featured" gorm:"type:tinyint(1);default:0"`
	Tags          types.StringArray `json:"tags" gorm:"type:json"`
	Price         float64           `json:"price" gorm:"type:decimal(10,2);default:0.00"`
	PublishDate   types.JSONTime    `json:"publish_date" gorm:"type:datetime"`
}

// TableName 设置表名
func (Book) TableName() string {
	return "ly_books"
}
