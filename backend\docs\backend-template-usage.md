# Backend 模块开发模板使用指南

## 概述
`backend-module-template.mdc` 是一个通用的backend模块开发规则模板，适用于所有backend模块的开发。本文档详细说明如何使用这个模板快速创建新的模块。

## 模板特点

### ✅ 完整性
- 涵盖了模块开发的所有必要组件
- 包含完整的CRUD操作
- 支持搜索、分页、批量操作等常用功能

### ✅ 标准化
- 统一的代码结构和命名规范
- 标准化的API接口格式
- 一致的错误处理机制

### ✅ 可扩展性
- 支持分类管理
- 支持评论系统
- 易于添加新功能

### ✅ 响应式设计
- 适配不同屏幕尺寸
- 良好的用户体验

## 使用步骤

### 1. 确定模块信息
在开始之前，需要确定以下信息：

```typescript
// 示例：创建文章管理模块
const moduleInfo = {
  moduleName: 'articles',        // 模块名称（英文，小写）
  entityName: 'Article',         // 实体名称（英文，首字母大写）
  entityNameLower: 'article',    // 实体名称（英文，小写）
  chineseEntity: '文章',         // 中文实体名称
  chineseModule: '文章管理'      // 中文模块名称
};
```

### 2. 占位符替换表

| 占位符 | 说明 | 示例 |
|--------|------|------|
| `{module-name}` | 模块名称（英文，小写） | `articles` |
| `{Entity}` | 实体名称（英文，首字母大写） | `Article` |
| `{entity}` | 实体名称（英文，小写） | `article` |
| `{实体}` | 中文实体名称 | `文章` |
| `{模块名称}` | 中文模块名称 | `文章管理` |

### 3. 创建目录结构

```bash
# 创建模块目录
mkdir -p backend/src/views/articles/list/components
mkdir -p backend/src/service/api/articles
mkdir -p backend/src/types

# 创建文件
touch backend/src/views/articles/list/index.vue
touch backend/src/views/articles/list/components/ArticleSearchBar.vue
touch backend/src/views/articles/list/components/ArticleTable.vue
touch backend/src/views/articles/list/components/ArticleFormDialog.vue
touch backend/src/views/articles/list/components/ArticleDetailDialog.vue
touch backend/src/service/api/articles/articles.ts
touch backend/src/types/articles.ts
```

### 4. 复制并替换模板内容

#### 4.1 类型定义文件 (backend/src/types/articles.ts)
```typescript
export interface ArticleItem {
    id: string;                    // 文章ID
    name: string;                  // 文章名称
    title?: string;                // 标题
    description?: string;          // 描述
    cover?: string;                // 封面图URL
    
    // 状态相关字段
    status: number;                // 状态：0-禁用，1-正常，-4-已删除
    is_featured?: number;          // 是否推荐：0-否，1-是
    is_published?: number;         // 是否发布：0-否，1-是
    
    // 分类和标签
    category_id?: string;          // 分类ID
    category_name?: string;        // 分类名称
    tags?: string[];               // 标签数组
    
    // 统计字段
    view_count?: number;           // 浏览次数
    like_count?: number;           // 点赞数
    comment_count?: number;        // 评论数
    share_count?: number;          // 分享数
    
    // 创建者信息
    creator_id?: string;           // 创建者ID
    creator_name?: string;         // 创建者名称
    creator_avatar?: string;       // 创建者头像
    
    // 时间字段
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
    published_at?: string;         // 发布时间
    
    // 排序字段
    sort_order?: number;           // 排序权重
    
    // 临时字段，不在数据库中存储
    is_liked?: boolean;            // 是否点赞
    is_collected?: boolean;        // 是否收藏
}

export interface ArticleSearchForm {
    keyword: string;               // 关键词搜索（名称/标题）
    status?: number;               // 状态筛选
    category_id?: string;          // 分类筛选
    creator_id?: string;           // 创建者筛选
    is_featured?: number;          // 是否推荐筛选
    is_published?: number;         // 是否发布筛选
    start_date: string;            // 开始时间
    end_date: string;              // 结束时间
}
```

#### 4.2 API接口文件 (backend/src/service/api/articles/articles.ts)
```typescript
import type { ArticleItem } from "@/types/articles";
import { request } from "../../request";

// 文章查询参数接口
interface ArticleParams {
    page?: {
        pageNo?: number;           // 页码
        pageSize?: number;         // 每页数量
    };
    data?: {
        keyword?: string;          // 关键词
        status?: number;           // 状态
        category_id?: string;      // 分类ID
        creator_id?: string;       // 创建者ID
        is_featured?: number;      // 是否推荐
        is_published?: number;     // 是否发布
        start_date?: string;       // 开始时间
        end_date?: string;         // 结束时间
    }
}

// ============ 文章管理API ============

/**
 * 获取文章列表
 * @param params 查询参数
 */
export function getArticleList(params?: ArticleParams) {
    return request({
        url: "/articles/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取文章详情
 * @param id 文章ID
 */
export function getArticleDetail(id: string) {
    return request({
        url: `/articles/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 新增文章
 * @param data 文章数据
 */
export function addArticle(data: Partial<ArticleItem>) {
    return request({
        url: "/articles/add",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新文章
 * @param data 文章数据
 */
export function updateArticle(data: Partial<ArticleItem>) {
    return request({
        url: "/articles/update",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新文章状态
 * @param data 状态数据 {id: string, status: number}
 */
export function updateArticleStatus(data: any) {
    return request({
        url: "/articles/update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 批量更新文章状态
 * @param data 批量状态数据 {ids: string[], status: number}
 */
export function batchUpdateArticleStatus(data: any) {
    return request({
        url: "/articles/batch-update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 删除文章
 * @param id 文章ID
 */
export function deleteArticle(id: string) {
    return request({
        url: `/articles/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 批量删除文章
 * @param data 批量删除数据 {ids: string[]}
 */
export function batchDeleteArticle(data: any) {
    return request({
        url: "/articles/batch-delete",
        method: "post",
        data: { data: data }
    })
}
```

### 5. 自动化替换脚本

为了简化替换过程，可以创建一个脚本来自动替换占位符：

```javascript
// replace-template.js
const fs = require('fs');
const path = require('path');

function replaceTemplate(templateContent, replacements) {
    let result = templateContent;
    
    for (const [placeholder, value] of Object.entries(replacements)) {
        const regex = new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g');
        result = result.replace(regex, value);
    }
    
    return result;
}

function createModule(moduleInfo) {
    const replacements = {
        '{module-name}': moduleInfo.moduleName,
        '{Entity}': moduleInfo.entityName,
        '{entity}': moduleInfo.entityNameLower,
        '{实体}': moduleInfo.chineseEntity,
        '{模块名称}': moduleInfo.chineseModule
    };

    // 读取模板文件
    const templatePath = '.cursor/rules/backend-module-template.mdc';
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    
    // 替换占位符
    const result = replaceTemplate(templateContent, replacements);
    
    // 输出结果到新文件
    const outputPath = `.cursor/rules/backend-${moduleInfo.moduleName}.mdc`;
    fs.writeFileSync(outputPath, result);
    
    console.log(`模块规则文件已生成：${outputPath}`);
}

// 使用示例
const moduleInfo = {
    moduleName: 'articles',
    entityName: 'Article',
    entityNameLower: 'article',
    chineseEntity: '文章',
    chineseModule: '文章管理'
};

createModule(moduleInfo);
```

### 6. 常见模块示例

#### 6.1 视频管理模块
```javascript
const videoModule = {
    moduleName: 'videos',
    entityName: 'Video',
    entityNameLower: 'video',
    chineseEntity: '视频',
    chineseModule: '视频管理'
};
```

#### 6.2 帖子管理模块
```javascript
const postModule = {
    moduleName: 'posts',
    entityName: 'Post',
    entityNameLower: 'post',
    chineseEntity: '帖子',
    chineseModule: '帖子管理'
};
```

#### 6.3 图片管理模块
```javascript
const pictureModule = {
    moduleName: 'pictures',
    entityName: 'Picture',
    entityNameLower: 'picture',
    chineseEntity: '图片',
    chineseModule: '图片管理'
};
```

### 7. 模板定制化

根据具体业务需求，可能需要对模板进行定制化：

#### 7.1 添加特定字段
```typescript
// 如果是视频模块，可能需要添加视频特有字段
export interface VideoItem extends BaseItem {
    duration: number;              // 视频时长
    width: number;                 // 视频宽度
    height: number;                // 视频高度
    file_size: number;             // 文件大小
    format: string;                // 视频格式
    quality: string;               // 视频质量
}
```

#### 7.2 修改搜索条件
```typescript
// 视频模块特有的搜索条件
export interface VideoSearchForm extends BaseSearchForm {
    min_duration?: number;         // 最小时长
    max_duration?: number;         // 最大时长
    quality?: string;              // 视频质量
    format?: string;               // 视频格式
}
```

#### 7.3 添加特定API
```typescript
// 视频模块特有的API
export function getVideoStatistics(id: string) {
    return request({
        url: `/videos/statistics/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}
```

### 8. 最佳实践

#### 8.1 命名规范
- 模块名称使用复数形式：`articles`, `videos`, `posts`
- 实体名称使用单数形式：`Article`, `Video`, `Post`
- 文件名使用 kebab-case：`article-search-bar.vue`
- 组件名使用 PascalCase：`ArticleSearchBar`

#### 8.2 代码组织
- 保持组件的单一职责
- 使用组合式API
- 合理抽取公共逻辑
- 添加必要的类型注解

#### 8.3 错误处理
- 统一使用 `handleApiError` 处理错误
- 为用户提供友好的错误提示
- 记录详细的错误日志

#### 8.4 性能优化
- 合理设置分页大小
- 使用防抖处理搜索
- 缓存常用数据
- 懒加载大型组件

### 9. 验证清单

创建新模块后，使用以下清单验证：

#### 9.1 文件结构
- [ ] 目录结构正确
- [ ] 文件命名规范
- [ ] 导入路径正确

#### 9.2 功能完整性
- [ ] 列表展示正常
- [ ] 搜索功能工作
- [ ] 分页功能正常
- [ ] CRUD操作成功
- [ ] 批量操作正确
- [ ] 状态更新正常

#### 9.3 用户体验
- [ ] 加载状态显示
- [ ] 操作反馈及时
- [ ] 错误提示友好
- [ ] 响应式布局正常

#### 9.4 代码质量
- [ ] TypeScript类型正确
- [ ] ESLint检查通过
- [ ] 代码注释完整
- [ ] 变量命名规范

## 总结

使用这个通用模板可以大大提高backend模块开发的效率和一致性。通过标准化的结构和规范，确保所有模块都遵循相同的开发模式，便于维护和扩展。

关键要点：
1. **标准化结构** - 统一的目录和文件组织
2. **类型安全** - 完整的TypeScript类型定义
3. **功能完整** - 涵盖常用的CRUD和管理功能
4. **易于扩展** - 支持自定义字段和功能
5. **用户友好** - 良好的交互体验和错误处理

遵循这个模板和指南，可以快速创建高质量、可维护的backend管理模块。 