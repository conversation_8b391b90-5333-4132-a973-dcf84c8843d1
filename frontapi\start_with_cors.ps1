# 设置CORS环境变量
$env:CORS_ALLOW_ORIGINS = "http://localhost:9527,http://localhost:8080,http://localhost:8081,http://localhost:3000,http://127.0.0.1:9527,http://127.0.0.1:8080,http://127.0.0.1:8081"
$env:CORS_ALLOW_METHODS = "GET,POST,PUT,DELETE,OPTIONS,PATCH"
$env:CORS_ALLOW_HEADERS = "Origin,Content-Type,Accept,Authorization,X-Admin-Token,X-Requested-With,x-client-info"
$env:CORS_ALLOW_CREDENTIALS = "true"

# 设置其他环境变量
$env:SERVER_PORT = "8080"
$env:ADMIN_SERVER_PORT = "8081"
$env:MEDIA_SERVER_PORT = "8082"
$env:STORAGE_PATH = "./storage"

Write-Host "🚀 正在启动管理后台服务..." -ForegroundColor Green
Write-Host "CORS配置:" -ForegroundColor Yellow
Write-Host "  允许的源: $env:CORS_ALLOW_ORIGINS" -ForegroundColor Gray
Write-Host "  允许的方法: $env:CORS_ALLOW_METHODS" -ForegroundColor Gray
Write-Host "  允许的头部: $env:CORS_ALLOW_HEADERS" -ForegroundColor Gray

# 启动管理后台服务
go run .\cmd\main.go -admin 