/**
 * 清新自然主题 - CSS变量
 * 青绿色主调 + 白色 + 淡绿
 * 设计参考: PrimeVue Teal主题 + Tailwind绿色系
 */

// CSS变量配置
const variables = {
    // 主要颜色
    'color-primary': '#059669',
    'color-primary-light': '#10b981',
    'color-primary-dark': '#047857',
    'color-primary-contrast': '#ffffff',
    'color-primary-secondary': '#34d399', // 第二配色 - 比主色浅的相同色系

    // 强调色
    'color-accent': '#0d9488',
    'color-accent-light': '#14b8a6',
    'color-accent-dark': '#0f766e',
    'color-accent-contrast': '#ffffff',

    // 中性色调 - 绿色系
    'color-neutral-50': '#ecfdf5',
    'color-neutral-100': '#d1fae5',
    'color-neutral-200': '#a7f3d0',
    'color-neutral-300': '#6ee7b7',
    'color-neutral-400': '#34d399',
    'color-neutral-500': '#10b981',
    'color-neutral-600': '#059669',
    'color-neutral-700': '#047857',
    'color-neutral-800': '#065f46',
    'color-neutral-900': '#064e3b',

    // 成功/错误/警告/信息色
    'color-success': '#16a34a',
    'color-error': '#dc2626',
    'color-warning': '#d97706',
    'color-info': '#0891b2',

    // 背景颜色
    'color-background': '#ffffff',
    'color-background-alt': '#ecfdf5',
    'color-background-hover': '#d1fae5',
    'color-background-card': 'linear-gradient(145deg, #ffffff, #ecfdf5)',
    // 主页面背景和文字色
    'color-page-background': '#f0fdf4',
    'color-page-text': '#064e3b',

    // 文本颜色
    'color-text': '#064e3b',
    'color-text-light': '#065f46',
    'color-text-lighter': '#047857',
    'color-text-contrast': '#ffffff',

    // 边框颜色
    'color-border': '#a7f3d0',
    'color-border-light': '#d1fae5',
    'color-border-dark': '#6ee7b7',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(5, 150, 105, 0.05)',
    'shadow': '0 1px 3px 0 rgba(5, 150, 105, 0.1), 0 1px 2px 0 rgba(5, 150, 105, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(5, 150, 105, 0.1), 0 2px 4px -1px rgba(5, 150, 105, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(5, 150, 105, 0.1), 0 4px 6px -2px rgba(5, 150, 105, 0.05)',

    // 特定区域颜色
    'header-gradient': 'linear-gradient(135deg, #059669, #10b981)',
    'footer-gradient': 'linear-gradient(135deg, #d1fae5, #ecfdf5)',
    'color-nav-gradient': '#064e3b',
    'color-footer-gradient': '#065f46',
    'color-footer-border': '#a7f3d0',
    'button-gradient': 'linear-gradient(135deg, #10b981, #059669)',
    'card-gradient': 'linear-gradient(145deg, #ffffff, #ecfdf5)',
    'accent-gradient': 'linear-gradient(135deg, #14b8a6, #0d9488)',

    // PrimeVue集成
    'primary-color': '#059669',
    'primary-color-text': '#ffffff',
    'surface-ground': '#f0fdf4',
    'surface-section': '#f0fdf4',
    'surface-card': 'linear-gradient(145deg, #ffffff, #ecfdf5)',
    'surface-overlay': '#ffffff',
    'surface-border': '#a7f3d0',
    'surface-hover': '#d1fae5',
    // 主页面内容区域
    'content-bg': '#f0fdf4',
    'content-text': '#064e3b',
};

export default variables;