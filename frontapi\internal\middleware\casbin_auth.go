package middleware

import (
	"log"
	"strings"

	"github.com/gofiber/fiber/v2"

	"frontapi/internal/service/permission"
	"frontapi/pkg/utils"
)

// CasbinAuth Casbin权限验证中间件
func CasbinAuth(casbinService permission.CasbinService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取请求路径和方法
		path := c.Path()
		method := c.Method()

		// 跳过某些路径的权限检查
		if shouldSkipAuth(path) {
			return c.Next()
		}

		// 从请求头获取Token
		authHeader := c.Get("Authorization")
		if authHeader == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    4001,
				"message": "未提供访问令牌",
				"data":    nil,
			})
		}

		// 验证Token格式
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    4001,
				"message": "令牌格式错误",
				"data":    nil,
			})
		}

		// 解析Token
		claims, err := utils.ParseToken(tokenString)
		if err != nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    4001,
				"message": "令牌无效或已过期",
				"data":    nil,
			})
		}

		// 获取用户ID
		userID := claims.UserID
		if userID == "" {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    4001,
				"message": "令牌中缺少用户信息",
				"data":    nil,
			})
		}

		// 权限检查
		allowed, err := casbinService.Enforce(c.Context(), userID, path, getActionFromMethod(method))
		if err != nil {
			log.Printf("权限检查错误: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    5000,
				"message": "权限验证失败",
				"data":    nil,
			})
		}

		if !allowed {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code":    4003,
				"message": "权限不足",
				"data":    nil,
			})
		}

		// 将用户信息存储到上下文中
		c.Locals("user_id", userID)
		c.Locals("user_claims", claims)

		return c.Next()
	}
}

// shouldSkipAuth 判断是否跳过权限验证
func shouldSkipAuth(path string) bool {
	skipPaths := []string{
		"/api/admin/auth/login",
		"/api/admin/auth/register",
		"/api/admin/auth/refresh",
		"/api/admin/health",
		"/api/proadm/auth/login",
		"/api/proadm/auth/register",
		"/api/proadm/auth/refresh",
		"/api/proadm/auth/logout",
		"/api/proadm/system/captcha",
		// 静态资源
		"/static/",
		"/uploads/",
		"/assets/",
		// Swagger文档
		"/swagger/",
		"/api/docs/",
	}

	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}

	return false
}

// getActionFromMethod 从HTTP方法获取动作
func getActionFromMethod(method string) string {
	switch strings.ToUpper(method) {
	case "GET":
		return "read"
	case "POST":
		return "create"
	case "PUT", "PATCH":
		return "update"
	case "DELETE":
		return "delete"
	default:
		return "read"
	}
}

// AdminAuth 管理员权限验证中间件
func AdminAuth(casbinService permission.CasbinService) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 获取用户信息
		userID := c.Locals("user_id")
		if userID == nil {
			return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
				"code":    4001,
				"message": "未登录",
				"data":    nil,
			})
		}

		// 检查是否是管理员
		roles, err := casbinService.GetRolesForUser(c.Context(), userID.(string))
		if err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"code":    5000,
				"message": "权限验证失败",
				"data":    nil,
			})
		}

		isAdmin := false
		for _, role := range roles {
			if role == "admin" || role == "super_admin" {
				isAdmin = true
				break
			}
		}

		if !isAdmin {
			return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
				"code":    4003,
				"message": "需要管理员权限",
				"data":    nil,
			})
		}

		return c.Next()
	}
}
