package migrations

import (
	"log"

	"gorm.io/gorm"
)

// Migration 数据库迁移接口
type Migration interface {
	// Name 返回迁移名称
	Name() string
	// Up 向上迁移（创建表）
	Up(db *gorm.DB) error
	// Down 向下迁移（删除表）
	Down(db *gorm.DB) error
}

// RegisterMigrations 注册所有迁移
func RegisterMigrations() []Migration {
	return []Migration{
		// 加入所有迁移实例
		&Migration20240722CreateUsersTables{},
		&Migration20241201FixUserLoginLogsTable{},
	}
}

// RunMigrations 执行所有迁移
func RunMigrations(db *gorm.DB) error {
	// 创建迁移记录表
	err := createMigrationTable(db)
	if err != nil {
		return err
	}

	// 获取所有迁移
	migrations := RegisterMigrations()

	// 获取已执行的迁移记录
	executed := make(map[string]bool)
	var records []MigrationRecord
	db.Find(&records)

	for _, record := range records {
		executed[record.Name] = true
	}

	// 执行未执行过的迁移
	for _, migration := range migrations {
		name := migration.Name()
		if !executed[name] {
			log.Printf("正在执行迁移: %s", name)
			err := migration.Up(db)
			if err != nil {
				log.Printf("迁移失败: %s, 错误: %v", name, err)
				return err
			}

			// 记录迁移已执行
			db.Create(&MigrationRecord{Name: name})
			log.Printf("迁移成功: %s", name)
		}
	}

	return nil
}

// MigrationRecord 迁移记录表
type MigrationRecord struct {
	ID   uint   `gorm:"primaryKey"`
	Name string `gorm:"size:255;not null;unique"`
}

// TableName 设置迁移记录表名
func (MigrationRecord) TableName() string {
	return "migrations"
}

// createMigrationTable 创建迁移记录表
func createMigrationTable(db *gorm.DB) error {
	return db.AutoMigrate(&MigrationRecord{})
}
