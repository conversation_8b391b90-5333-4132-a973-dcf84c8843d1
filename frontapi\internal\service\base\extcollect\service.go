package extcollect

import (
	"context"
	"fmt"
	"sync"
	"time"

	"frontapi/internal/service/base/extcollect/types"
)

// collectService 收藏服务实现
type collectService struct {
	config  *Config
	adapter CollectAdapter
	mu      sync.RWMutex
}

// 确保实现了接口
var _ ExtendedCollectService = (*collectService)(nil)

// Collect 收藏项目
func (s *collectService) Collect(ctx context.Context, userID, itemID, itemType string) error {
	return s.adapter.Collect(ctx, userID, itemID, itemType)
}

// Uncollect 取消收藏
func (s *collectService) Uncollect(ctx context.Context, userID, itemID, itemType string) error {
	return s.adapter.Uncollect(ctx, userID, itemID, itemType)
}

// IsCollected 检查是否已收藏
func (s *collectService) IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	return s.adapter.IsCollected(ctx, userID, itemID, itemType)
}

// GetCollectCount 获取收藏数量
func (s *collectService) GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error) {
	return s.adapter.GetCollectCount(ctx, itemID, itemType)
}

// BatchCollect 批量收藏
func (s *collectService) BatchCollect(ctx context.Context, operations []*types.CollectOperation) error {
	return s.adapter.BatchCollect(ctx, operations)
}

// BatchUncollect 批量取消收藏
func (s *collectService) BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error {
	return s.adapter.BatchUncollect(ctx, operations)
}

// BatchGetCollectStatus 批量获取收藏状态
func (s *collectService) BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	return s.adapter.BatchGetCollectStatus(ctx, userID, items)
}

// BatchGetCollectCounts 批量获取收藏数量
func (s *collectService) BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	return s.adapter.BatchGetCollectCounts(ctx, items)
}

// GetUserCollections 获取用户收藏列表
func (s *collectService) GetUserCollections(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	return s.adapter.GetUserCollections(ctx, userID, itemType, limit, offset)
}

// GetItemCollectors 获取项目收藏者列表
func (s *collectService) GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	return s.adapter.GetItemCollectors(ctx, itemID, itemType, limit, offset)
}

// GetCollectHistory 获取收藏历史
func (s *collectService) GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error) {
	return s.adapter.GetCollectHistory(ctx, userID, itemType, timeRange)
}

// UpdateHotRank 更新热度排名
func (s *collectService) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	return s.adapter.UpdateHotRank(ctx, itemID, itemType, score)
}

// GetHotRanking 获取热门排行
func (s *collectService) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	return s.adapter.GetHotRanking(ctx, itemType, limit)
}

// GetHotRankingWithScores 获取带分数的热门排行
func (s *collectService) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	return s.adapter.GetHotRankingWithScores(ctx, itemType, limit)
}

// GetUserCollectStats 获取用户收藏统计
func (s *collectService) GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error) {
	return s.adapter.GetUserCollectStats(ctx, userID)
}

// GetItemCollectStats 获取项目收藏统计
func (s *collectService) GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error) {
	return s.adapter.GetItemCollectStats(ctx, itemID, itemType)
}

// GetTrendingItems 获取趋势项目
func (s *collectService) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error) {
	return s.adapter.GetTrendingItems(ctx, itemType, timeRange, limit)
}

// InvalidateCache 清除缓存
func (s *collectService) InvalidateCache(ctx context.Context, keys ...string) error {
	return s.adapter.InvalidateCache(ctx, keys...)
}

// WarmupCache 预热缓存
func (s *collectService) WarmupCache(ctx context.Context, itemType string, itemIDs []string) error {
	return s.adapter.WarmupCache(ctx, itemType, itemIDs)
}

// GetCacheStats 获取缓存统计
func (s *collectService) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return s.adapter.GetCacheStats(ctx)
}

// CleanupExpiredData 清理过期数据
func (s *collectService) CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error {
	return s.adapter.CleanupExpiredData(ctx, itemType, before)
}

// ExportData 导出数据
func (s *collectService) ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error) {
	return s.adapter.ExportData(ctx, userID, itemType, format)
}

// ImportData 导入数据
func (s *collectService) ImportData(ctx context.Context, data []byte, itemType, format string) error {
	return s.adapter.ImportData(ctx, data, itemType, format)
}

// SyncData 同步数据
func (s *collectService) SyncData(ctx context.Context, itemType string, startTime, endTime time.Time) error {
	// 这里可以实现具体的同步逻辑
	return nil
}

// GetSyncStatus 获取同步状态
func (s *collectService) GetSyncStatus(ctx context.Context) (*types.SyncStatus, error) {
	return &types.SyncStatus{
		LastSyncTime:   time.Now(),
		SyncEnabled:    true,
		SyncInProgress: false,
	}, nil
}

// GetMetrics 获取服务指标
func (s *collectService) GetMetrics(ctx context.Context) (*types.ServiceMetrics, error) {
	return &types.ServiceMetrics{
		ServiceName: s.config.ServiceName,
		Version:     "1.0.0",
		Timestamp:   time.Now(),
		Uptime:      time.Since(time.Now()),
		Components:  make(map[string]interface{}),
		Metadata:    make(map[string]interface{}),
	}, nil
}

// GetErrorStats 获取错误统计
func (s *collectService) GetErrorStats(ctx context.Context) (map[string]interface{}, error) {
	return map[string]interface{}{
		"total_errors": 0,
		"error_rate":   0.0,
	}, nil
}

// HealthCheck 健康检查
func (s *collectService) HealthCheck(ctx context.Context) error {
	return s.adapter.HealthCheck(ctx)
}

// Close 关闭服务
func (s *collectService) Close() error {
	return s.adapter.Close()
}

// Shutdown 关闭服务
func (s *collectService) Shutdown(ctx context.Context) error {
	return s.Close()
}

// dualAdapter 双写适配器
type dualAdapter struct {
	primary   CollectAdapter
	secondary CollectAdapter
	strategy  StorageStrategy
	mu        sync.RWMutex
}

// 确保实现了接口
var _ CollectAdapter = (*dualAdapter)(nil)

// Collect 收藏项目
func (d *dualAdapter) Collect(ctx context.Context, userID, itemID, itemType string) error {
	err := d.primary.Collect(ctx, userID, itemID, itemType)
	if err != nil {
		return err
	}

	if d.secondary != nil {
		// 异步写入次要存储，失败不影响主要操作
		go func() {
			if syncErr := d.secondary.Collect(context.Background(), userID, itemID, itemType); syncErr != nil {
				// 可以记录日志或发送监控
				fmt.Printf("Secondary storage collect failed: %v\n", syncErr)
			}
		}()
	}

	return nil
}

// Uncollect 取消收藏
func (d *dualAdapter) Uncollect(ctx context.Context, userID, itemID, itemType string) error {
	err := d.primary.Uncollect(ctx, userID, itemID, itemType)
	if err != nil {
		return err
	}

	if d.secondary != nil {
		// 异步删除次要存储
		go func() {
			if syncErr := d.secondary.Uncollect(context.Background(), userID, itemID, itemType); syncErr != nil {
				fmt.Printf("Secondary storage uncollect failed: %v\n", syncErr)
			}
		}()
	}

	return nil
}

// IsCollected 检查是否已收藏
func (d *dualAdapter) IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	return d.primary.IsCollected(ctx, userID, itemID, itemType)
}

// GetCollectCount 获取收藏数量
func (d *dualAdapter) GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error) {
	return d.primary.GetCollectCount(ctx, itemID, itemType)
}

// 其他方法的实现...
func (d *dualAdapter) BatchCollect(ctx context.Context, operations []*types.CollectOperation) error {
	return d.primary.BatchCollect(ctx, operations)
}

func (d *dualAdapter) BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error {
	return d.primary.BatchUncollect(ctx, operations)
}

func (d *dualAdapter) BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	return d.primary.BatchGetCollectStatus(ctx, userID, items)
}

func (d *dualAdapter) BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	return d.primary.BatchGetCollectCounts(ctx, items)
}

func (d *dualAdapter) GetUserCollections(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	return d.primary.GetUserCollections(ctx, userID, itemType, limit, offset)
}

func (d *dualAdapter) GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	return d.primary.GetItemCollectors(ctx, itemID, itemType, limit, offset)
}

func (d *dualAdapter) GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error) {
	return d.primary.GetCollectHistory(ctx, userID, itemType, timeRange)
}

func (d *dualAdapter) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	return d.primary.UpdateHotRank(ctx, itemID, itemType, score)
}

func (d *dualAdapter) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	return d.primary.GetHotRanking(ctx, itemType, limit)
}

func (d *dualAdapter) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	return d.primary.GetHotRankingWithScores(ctx, itemType, limit)
}

func (d *dualAdapter) GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error) {
	return d.primary.GetUserCollectStats(ctx, userID)
}

func (d *dualAdapter) GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error) {
	return d.primary.GetItemCollectStats(ctx, itemID, itemType)
}

func (d *dualAdapter) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error) {
	return d.primary.GetTrendingItems(ctx, itemType, timeRange, limit)
}

func (d *dualAdapter) InvalidateCache(ctx context.Context, keys ...string) error {
	return d.primary.InvalidateCache(ctx, keys...)
}

func (d *dualAdapter) WarmupCache(ctx context.Context, itemType string, itemIDs []string) error {
	return d.primary.WarmupCache(ctx, itemType, itemIDs)
}

func (d *dualAdapter) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return d.primary.GetCacheStats(ctx)
}

func (d *dualAdapter) CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error {
	return d.primary.CleanupExpiredData(ctx, itemType, before)
}

func (d *dualAdapter) ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error) {
	return d.primary.ExportData(ctx, userID, itemType, format)
}

func (d *dualAdapter) ImportData(ctx context.Context, data []byte, itemType, format string) error {
	return d.primary.ImportData(ctx, data, itemType, format)
}

func (d *dualAdapter) HealthCheck(ctx context.Context) error {
	return d.primary.HealthCheck(ctx)
}

func (d *dualAdapter) Close() error {
	if err := d.primary.Close(); err != nil {
		return err
	}
	if d.secondary != nil {
		return d.secondary.Close()
	}
	return nil
}
