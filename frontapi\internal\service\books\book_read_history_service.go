package books

import (
	"context"
	"errors"
	"time"

	"frontapi/internal/models/books"
	bookRepo "frontapi/internal/repository/books"
	"frontapi/internal/service/base"
)

// 请求和响应结构体
type CreateReadHistoryRequest struct {
	UserID       string `json:"userId" validate:"required"`
	BookID       string `json:"bookId" validate:"required"`
	ChapterID    string `json:"chapterId" validate:"required"`
	Position     int    `json:"position"`
	BookTitle    string `json:"bookTitle"`
	BookCover    string `json:"bookCover"`
	ChapterTitle string `json:"chapterTitle"`
	Author       string `json:"author"`
}

// BookReadHistoryService 电子书阅读历史服务接口
type BookReadHistoryService interface {
	// 继承BaseService的所有方法
	base.IExtendedService[books.BookReadHistory]
	CreateOrUpdateReadHistory(ctx context.Context, req *CreateReadHistoryRequest) (string, error)
	GetReadHistoryByID(ctx context.Context, id string) (*books.BookReadHistory, error)
	DeleteReadHistory(ctx context.Context, id string, userId string) error
	ListUserReadHistory(ctx context.Context, userID string, page, pageSize int) ([]*books.BookReadHistory, int64, error)
	UpdateReadPosition(ctx context.Context, userID, bookID, chapterID string, position int) error
}

type readHistoryService struct {
	*base.ExtendedService[books.BookReadHistory]
	readHistoryRepo bookRepo.ReadHistoryRepository
	bookRepo        bookRepo.BookRepository
	chapterRepo     bookRepo.ChapterRepository
}

// NewReadHistoryService 创建电子书阅读历史服务实例
func NewReadHistoryService(
	readHistoryRepo bookRepo.ReadHistoryRepository,
	bookRepo bookRepo.BookRepository,
	chapterRepo bookRepo.ChapterRepository,
) BookReadHistoryService {
	return &readHistoryService{
		ExtendedService: base.NewExtendedService[books.BookReadHistory](readHistoryRepo, "book_read_histories"),
		readHistoryRepo: readHistoryRepo,
		bookRepo:        bookRepo,
		chapterRepo:     chapterRepo,
	}
}

// CreateOrUpdateReadHistory 创建或更新电子书阅读历史
func (s *readHistoryService) CreateOrUpdateReadHistory(ctx context.Context, req *CreateReadHistoryRequest) (string, error) {
	// 先查询是否存在阅读记录
	existingHistory, err := s.readHistoryRepo.FindByUserAndBookAndChapter(ctx, req.UserID, req.BookID, req.ChapterID)
	if err != nil {
		return "", err
	}

	if existingHistory != nil {
		// 更新阅读位置和时间
		existingHistory.Position = req.Position
		existingHistory.ReadTime = time.Now()

		err = s.readHistoryRepo.Update(ctx, existingHistory)
		if err != nil {
			return "", err
		}

		return existingHistory.GetID(), nil
	}

	// 如果没有提供电子书和章节详情，则获取相关信息
	if req.BookTitle == "" || req.BookCover == "" || req.ChapterTitle == "" || req.Author == "" {
		// 获取电子书信息
		book, err := s.bookRepo.FindByID(ctx, req.BookID)
		if err != nil {
			return "", err
		}
		if book == nil {
			return "", errors.New("电子书不存在")
		}

		// 获取章节信息
		chapter, err := s.chapterRepo.FindByID(ctx, req.ChapterID)
		if err != nil {
			return "", err
		}
		if chapter == nil {
			return "", errors.New("章节不存在")
		}

		req.BookTitle = book.Title
		req.BookCover = book.Cover
		req.ChapterTitle = chapter.Title
		req.Author = book.Author
	}

	// 创建新的阅读历史记录
	readHistory := &books.BookReadHistory{
		UserID:       req.UserID,
		BookID:       req.BookID,
		BookTitle:    req.BookTitle,
		BookCover:    req.BookCover,
		ChapterID:    req.ChapterID,
		ChapterTitle: req.ChapterTitle,
		Author:       req.Author,
		Position:     req.Position,
		ReadTime:     time.Now(),
	}

	err = s.readHistoryRepo.Create(ctx, readHistory)
	if err != nil {
		return "", err
	}

	// 增加电子书阅读数量，可选
	err = s.bookRepo.IncrementReadCount(ctx, req.BookID)
	if err != nil {
		return "", err
	}

	// 增加章节阅读数量，可选
	err = s.chapterRepo.IncrementReadCount(ctx, req.ChapterID)
	if err != nil {
		return "", err
	}

	return readHistory.GetID(), nil
}

// GetReadHistoryByID 根据ID获取阅读历史
func (s *readHistoryService) GetReadHistoryByID(ctx context.Context, id string) (*books.BookReadHistory, error) {
	return s.GetByID(ctx, id, true)
}

// DeleteReadHistory 删除阅读历史
func (s *readHistoryService) DeleteReadHistory(ctx context.Context, id string, userId string) error {
	return s.readHistoryRepo.Delete(ctx, id)
}

// ListUserReadHistory 获取用户阅读历史列表
func (s *readHistoryService) ListUserReadHistory(ctx context.Context, userID string, page, pageSize int) ([]*books.BookReadHistory, int64, error) {
	return s.readHistoryRepo.ListByUser(ctx, userID, page, pageSize)
}

// UpdateReadPosition 更新阅读位置
func (s *readHistoryService) UpdateReadPosition(ctx context.Context, userID, bookID, chapterID string, position int) error {
	history, err := s.readHistoryRepo.FindByUserAndBookAndChapter(ctx, userID, bookID, chapterID)
	if err != nil {
		return err
	}

	if history == nil {
		// 如果不存在，创建一个新的阅读记录
		req := &CreateReadHistoryRequest{
			UserID:    userID,
			BookID:    bookID,
			ChapterID: chapterID,
			Position:  position,
		}
		_, err = s.CreateOrUpdateReadHistory(ctx, req)
		return err
	}

	// 更新阅读位置
	return s.readHistoryRepo.UpdatePosition(ctx, userID, bookID, chapterID, position)
}
