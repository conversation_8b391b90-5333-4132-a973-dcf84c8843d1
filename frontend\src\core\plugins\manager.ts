/**
 * 插件管理器实现
 */

import type {
  Plugin,
  PluginInstance,
  PluginManager,
  PluginContext,
  PluginStatus,
  PluginEvent,
  PluginManagerConfig,
  ExtendedPluginInstance
} from './types'

/**
 * 默认配置
 */
const DEFAULT_CONFIG: PluginManagerConfig = {
  autoInstall: true,
  strictMode: false,
  maxConcurrency: 5,
  timeout: 30000,
  retries: 3,
  logger: {
    debug: false,
    level: 'info'
  }
}

/**
 * 插件管理器实现类
 */
export class PluginManagerImpl implements PluginManager {
  private plugins: Map<string, ExtendedPluginInstance> = new Map()
  private context: PluginContext | null = null
  private config: PluginManagerConfig
  private eventHandlers: Map<string, Set<(event: PluginEvent) => void>> = new Map()
  private installQueue: Set<string> = new Set()
  private installing: Set<string> = new Set()

  constructor(config: Partial<PluginManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * 初始化插件管理器
   */
  async init(context: PluginContext): Promise<void> {
    this.context = context
    this.log('info', 'Plugin manager initialized')
    
    // 如果启用自动安装，安装所有已注册的插件
    if (this.config.autoInstall) {
      await this.installAll()
    }
  }

  /**
   * 销毁插件管理器
   */
  async destroy(): Promise<void> {
    // 卸载所有插件
    const installedPlugins = this.getInstalled()
    for (const instance of installedPlugins) {
      await this.uninstall(instance.plugin.meta.name)
    }
    
    // 清理资源
    this.plugins.clear()
    this.eventHandlers.clear()
    this.installQueue.clear()
    this.installing.clear()
    this.context = null
    
    this.log('info', 'Plugin manager destroyed')
  }

  /**
   * 注册插件
   */
  async register(plugin: Plugin): Promise<void> {
    const name = plugin.meta.name
    
    if (this.plugins.has(name)) {
      throw new Error(`Plugin '${name}' is already registered`)
    }

    // 验证插件
    await this.validatePlugin(plugin)

    // 创建插件实例
    const instance: ExtendedPluginInstance = {
      plugin,
      status: 'pending',
      installedAt: undefined,
      updatedAt: undefined
    }

    this.plugins.set(name, instance)
    this.log('info', `Plugin '${name}' registered`)
    
    this.emit('register', { plugin: name })
  }

  /**
   * 注销插件
   */
  async unregister(name: string): Promise<void> {
    const instance = this.plugins.get(name)
    if (!instance) {
      throw new Error(`Plugin '${name}' is not registered`)
    }

    // 如果插件已安装，先卸载
    if (instance.status === 'installed') {
      await this.uninstall(name)
    }

    this.plugins.delete(name)
    this.log('info', `Plugin '${name}' unregistered`)
    
    this.emit('unregister', { plugin: name })
  }

  /**
   * 安装插件
   */
  async install(name: string): Promise<void> {
    const instance = this.plugins.get(name)
    if (!instance) {
      throw new Error(`Plugin '${name}' is not registered`)
    }

    if (instance.status === 'installed') {
      this.log('warn', `Plugin '${name}' is already installed`)
      return
    }

    if (this.installing.has(name)) {
      this.log('warn', `Plugin '${name}' is already being installed`)
      return
    }

    if (!this.context) {
      throw new Error('Plugin manager is not initialized')
    }

    this.installing.add(name)
    this.setStatus(name, 'installing')

    try {
      // 检查依赖
      await this.checkDependencies(instance.plugin)

      // 执行安装前钩子
      if (instance.plugin.hooks?.beforeInstall) {
        await instance.plugin.hooks.beforeInstall(this.context)
      }

      // 执行插件安装
      await this.executeWithTimeout(
        () => instance.plugin.install(this.context!),
        this.config.timeout!
      )

      // 更新状态
      instance.installedAt = new Date()
      this.setStatus(name, 'installed')

      // 执行安装后钩子
      if (instance.plugin.hooks?.afterInstall) {
        await instance.plugin.hooks.afterInstall(this.context)
      }

      this.log('info', `Plugin '${name}' installed successfully`)
      this.emit('install', { plugin: name })

    } catch (error) {
      instance.error = error as Error
      this.setStatus(name, 'error')
      this.log('error', `Failed to install plugin '${name}':`, error)
      
      // 执行错误钩子
      if (instance.plugin.hooks?.onError) {
        instance.plugin.hooks.onError(error as Error, this.context)
      }
      
      this.emit('error', { plugin: name, error })
      throw error
    } finally {
      this.installing.delete(name)
    }
  }

  /**
   * 卸载插件
   */
  async uninstall(name: string): Promise<void> {
    const instance = this.plugins.get(name)
    if (!instance) {
      throw new Error(`Plugin '${name}' is not registered`)
    }

    if (instance.status !== 'installed') {
      this.log('warn', `Plugin '${name}' is not installed`)
      return
    }

    if (!this.context) {
      throw new Error('Plugin manager is not initialized')
    }

    this.setStatus(name, 'uninstalling')

    try {
      // 执行卸载前钩子
      if (instance.plugin.hooks?.beforeUninstall) {
        await instance.plugin.hooks.beforeUninstall(this.context)
      }

      // 执行插件卸载
      if (instance.plugin.uninstall) {
        await this.executeWithTimeout(
          () => instance.plugin.uninstall!(this.context!),
          this.config.timeout!
        )
      }

      // 更新状态
      this.setStatus(name, 'uninstalled')
      instance.installedAt = undefined

      // 执行卸载后钩子
      if (instance.plugin.hooks?.afterUninstall) {
        await instance.plugin.hooks.afterUninstall(this.context)
      }

      this.log('info', `Plugin '${name}' uninstalled successfully`)
      this.emit('uninstall', { plugin: name })

    } catch (error) {
      instance.error = error as Error
      this.setStatus(name, 'error')
      this.log('error', `Failed to uninstall plugin '${name}':`, error)
      
      if (instance.plugin.hooks?.onError) {
        instance.plugin.hooks.onError(error as Error, this.context)
      }
      
      this.emit('error', { plugin: name, error })
      throw error
    }
  }

  /**
   * 更新插件
   */
  async update(name: string): Promise<void> {
    const instance = this.plugins.get(name)
    if (!instance) {
      throw new Error(`Plugin '${name}' is not registered`)
    }

    if (!this.context) {
      throw new Error('Plugin manager is not initialized')
    }

    const oldVersion = instance.plugin.meta.version

    try {
      if (instance.plugin.update) {
        await instance.plugin.update(this.context, oldVersion)
      }

      instance.updatedAt = new Date()
      this.log('info', `Plugin '${name}' updated successfully`)
      this.emit('update', { plugin: name, oldVersion })

    } catch (error) {
      instance.error = error as Error
      this.log('error', `Failed to update plugin '${name}':`, error)
      
      if (instance.plugin.hooks?.onError) {
        instance.plugin.hooks.onError(error as Error, this.context)
      }
      
      this.emit('error', { plugin: name, error })
      throw error
    }
  }

  /**
   * 获取插件实例
   */
  get(name: string): PluginInstance | undefined {
    return this.plugins.get(name)
  }

  /**
   * 获取所有插件实例
   */
  getAll(): PluginInstance[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取已安装的插件
   */
  getInstalled(): PluginInstance[] {
    return this.getAll().filter(instance => instance.status === 'installed')
  }

  /**
   * 检查插件是否已安装
   */
  isInstalled(name: string): boolean {
    const instance = this.plugins.get(name)
    return instance?.status === 'installed'
  }

  /**
   * 获取插件状态
   */
  getStatus(name: string): PluginStatus | undefined {
    return this.plugins.get(name)?.status
  }

  /**
   * 设置插件状态
   */
  setStatus(name: string, status: PluginStatus): void {
    const instance = this.plugins.get(name)
    if (instance) {
      const oldStatus = instance.status
      instance.status = status
      
      if (oldStatus !== status) {
        this.emit('statusChange', { plugin: name, oldStatus, newStatus: status })
      }
    }
  }

  /**
   * 事件监听
   */
  on(event: string, handler: (event: PluginEvent) => void): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set())
    }
    this.eventHandlers.get(event)!.add(handler)
  }

  /**
   * 移除事件监听
   */
  off(event: string, handler: (event: PluginEvent) => void): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.eventHandlers.delete(event)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event: string, data?: any): void {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const eventObj: PluginEvent = {
        type: event as any,
        plugin: data?.plugin || '',
        data,
        timestamp: new Date()
      }
      
      handlers.forEach(handler => {
        try {
          handler(eventObj)
        } catch (error) {
          this.log('error', `Error in event handler for '${event}':`, error)
        }
      })
    }
  }

  /**
   * 安装所有已注册的插件
   */
  private async installAll(): Promise<void> {
    const plugins = Array.from(this.plugins.keys())
    const batches = this.createInstallBatches(plugins)
    
    for (const batch of batches) {
      await Promise.all(
        batch.map(name => this.install(name).catch(error => {
          this.log('error', `Failed to auto-install plugin '${name}':`, error)
        }))
      )
    }
  }

  /**
   * 创建安装批次（处理依赖关系）
   */
  private createInstallBatches(plugins: string[]): string[][] {
    const batches: string[][] = []
    const remaining = new Set(plugins)
    const installed = new Set<string>()

    while (remaining.size > 0) {
      const batch: string[] = []
      
      for (const name of remaining) {
        const instance = this.plugins.get(name)!
        const dependencies = instance.plugin.meta.dependencies || []
        
        // 检查依赖是否都已安装
        const canInstall = dependencies.every(dep => installed.has(dep) || !remaining.has(dep))
        
        if (canInstall) {
          batch.push(name)
        }
      }
      
      if (batch.length === 0) {
        // 循环依赖或无法解决的依赖
        this.log('warn', 'Circular dependency detected or unresolvable dependencies:', Array.from(remaining))
        break
      }
      
      batch.forEach(name => {
        remaining.delete(name)
        installed.add(name)
      })
      
      batches.push(batch)
    }

    return batches
  }

  /**
   * 检查插件依赖
   */
  private async checkDependencies(plugin: Plugin): Promise<void> {
    const dependencies = plugin.meta.dependencies || []
    
    for (const dep of dependencies) {
      if (!this.isInstalled(dep)) {
        if (this.config.strictMode) {
          throw new Error(`Dependency '${dep}' is not installed`)
        } else {
          this.log('warn', `Dependency '${dep}' is not installed, attempting to install`)
          await this.install(dep)
        }
      }
    }
  }

  /**
   * 验证插件
   */
  private async validatePlugin(plugin: Plugin): Promise<void> {
    if (!plugin.meta?.name) {
      throw new Error('Plugin must have a name')
    }
    
    if (!plugin.meta?.version) {
      throw new Error('Plugin must have a version')
    }
    
    if (typeof plugin.install !== 'function') {
      throw new Error('Plugin must have an install function')
    }
  }

  /**
   * 带超时的执行
   */
  private async executeWithTimeout<T>(
    fn: () => Promise<T> | T,
    timeout: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`))
      }, timeout)

      Promise.resolve(fn())
        .then(result => {
          clearTimeout(timer)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timer)
          reject(error)
        })
    })
  }

  /**
   * 日志记录
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, ...args: any[]): void {
    if (!this.config.logger?.debug && level === 'debug') {
      return
    }

    const levels = ['error', 'warn', 'info', 'debug']
    const currentLevel = levels.indexOf(this.config.logger?.level || 'info')
    const messageLevel = levels.indexOf(level)

    if (messageLevel <= currentLevel) {
      console[level](`[PluginManager] ${message}`, ...args)
    }
  }
}

/**
 * 创建插件管理器
 */
export function createPluginManager(config?: Partial<PluginManagerConfig>): PluginManager {
  return new PluginManagerImpl(config)
}

/**
 * 默认插件管理器实例
 */
export const pluginManager = createPluginManager()

/**
 * 导出默认实例
 */
export default pluginManager