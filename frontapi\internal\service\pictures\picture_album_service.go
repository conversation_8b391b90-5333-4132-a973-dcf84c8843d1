package pictures

import (
	"context"
	"errors"
	"fmt"
	"frontapi/pkg/types"
	"time"

	"github.com/google/uuid"

	"frontapi/internal/models/pictures"
	repo "frontapi/internal/repository/pictures"
	"frontapi/internal/service/base"
	pictureValidator "frontapi/internal/validation/pictures"
)

// PictureAlbumService 图片专辑服务接口
type PictureAlbumService interface {
	base.IExtendedService[pictures.PictureAlbum]
	CreateAlbum(ctx context.Context, req *pictureValidator.PictureAlbumCreateRequest) (string, error)
	UpdateAlbum(ctx context.Context, id string, req *pictureValidator.PictureAlbumUpdateRequest) error
	GetRecommendedAlbums(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureAlbum, int64, error)
}

// pictureAlbumService 图片专辑服务实现
type pictureAlbumService struct {
	*base.ExtendedService[pictures.PictureAlbum]
	albumRepo    repo.PictureAlbumRepository
	categoryRepo repo.PictureCategoryRepository
	pictureRepo  repo.PictureRepository
}

// NewPictureAlbumService 创建图片专辑服务实例
func NewPictureAlbumService(
	albumRepo repo.PictureAlbumRepository,
	categoryRepo repo.PictureCategoryRepository,
	pictureRepo repo.PictureRepository,
) PictureAlbumService {
	return &pictureAlbumService{
		ExtendedService: base.NewExtendedService[pictures.PictureAlbum](albumRepo, "picture_album"),
		albumRepo:       albumRepo,
		categoryRepo:    categoryRepo,
		pictureRepo:     pictureRepo,
	}
}

// CreateAlbum 创建图片专辑
func (s *pictureAlbumService) CreateAlbum(ctx context.Context, req *pictureValidator.PictureAlbumCreateRequest) (string, error) {
	// 生成唯一ID
	albumID := uuid.New().String()

	var categoryName string
	// 如果指定了分类，检查分类是否存在并获取分类名称
	if req.CategoryID != "" {
		category, err := s.categoryRepo.FindByID(ctx, req.CategoryID)
		if err != nil {
			return "", errors.New("指定的分类不存在")
		}
		categoryName = category.Name.String
	}

	// 创建专辑记录
	album := &pictures.PictureAlbum{
		CoverURL:     req.CoverURL,
		PictureCount: 0,
		IsPaid:       req.IsPaid,
		Price:        req.Price,
		UploadTime:   types.JSONTime(time.Now()),
		Tags:         req.Tags,
	}

	// 设置继承的字段
	album.SetID(albumID)
	album.Title = req.Title
	album.SetDescription(req.Description)
	album.SetCategoryID(req.CategoryID)
	album.SetCategoryName(categoryName)
	album.SetCreatorID(req.CreatorID)
	album.SetStatus(1)
	album.SetCreatedAt(types.JSONTime(time.Now()))
	album.SetUpdatedAt(types.JSONTime(time.Now()))

	// 保存专辑
	if err := s.albumRepo.Create(ctx, album); err != nil {
		return "", err
	}

	return albumID, nil
}

// UpdateAlbum 更新图片专辑
func (s *pictureAlbumService) UpdateAlbum(ctx context.Context, id string, req *pictureValidator.PictureAlbumUpdateRequest) error {
	// 获取现有专辑
	album, err := s.GetByID(ctx, id, true)
	if err != nil {
		return err
	}

	// 如果要更改分类，检查分类是否存在并更新分类名称
	if req.CategoryID != "" && req.CategoryID != album.CategoryID.ValueOrZero() {
		category, err := s.categoryRepo.FindByID(ctx, req.CategoryID)
		if err != nil {
			return errors.New("指定的分类不存在")
		}
		album.SetCategoryID(req.CategoryID)
		album.SetCategoryName(category.Name)
	}

	// 更新其他字段
	if req.Title != "" {
		album.SetTitle(req.Title)
	}
	if req.Description != "" {
		album.SetDescription(req.Description)
	}
	if req.CoverURL != "" {
		album.CoverURL = req.CoverURL
	}
	if req.CategoryName != "" {
		album.SetCategoryName(req.CategoryName)
	}
	if req.IsPaid != nil {
		album.IsPaid = *req.IsPaid
	}
	if req.Price != nil {
		album.Price = *req.Price
	}
	if req.Status != nil {
		album.Status = *req.Status
	}
	if req.Tags != nil {
		album.Tags = req.Tags
	}

	album.UpdatedAt = types.JSONTime(time.Now())

	// 保存更新
	return s.BaseService.Update(ctx, album)
}

// GetRecommendedAlbums 获取推荐专辑
func (s *pictureAlbumService) GetRecommendedAlbums(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureAlbum, int64, error) {
	// 设置推荐条件
	if condition == nil {
		condition = make(map[string]interface{})
	}
	condition["status"] = 1

	// 如果没有指定排序，使用默认排序
	if orderBy == "" {
		orderBy = "view_count DESC,like_count DESC,picture_count DESC,sort_order ASC"
	}
	fmt.Println("condition", condition)
	// 查询推荐专辑
	albums, total, err := s.albumRepo.List(ctx, condition, orderBy, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	return albums, total, nil
}
