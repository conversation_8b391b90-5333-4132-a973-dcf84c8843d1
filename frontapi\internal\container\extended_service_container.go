package container

import (
	"log"

	"frontapi/internal/service/base/extcollect"
	"frontapi/internal/service/base/extlike"
)

// InitExtendedServices 初始化扩展服务（点赞和收藏）
func InitExtendedServices(b *ServiceBuilder) {
	// 初始化点赞服务
	likeConfig := extlike.DefaultConfig()
	likeConfig.Strategy = extlike.RedisOnly // 使用Redis作为存储
	likeService, err := extlike.NewExtendedLikeService(likeConfig)
	if err != nil {
		log.Printf("警告: 无法创建点赞服务: %v，将使用nil", err)
		likeService = nil
	}

	// 初始化收藏服务
	collectConfig := extcollect.DefaultConfig()
	collectConfig.Strategy = extcollect.RedisOnly // 使用Redis作为存储
	collectService, err := extcollect.NewCollectService(collectConfig)
	if err != nil {
		log.Printf("警告: 无法创建收藏服务: %v，将使用nil", err)
		collectService = nil
	}

	// 注册到容器
	container := b.Services()
	container.LikeService = likeService
	container.CollectService = collectService
}
