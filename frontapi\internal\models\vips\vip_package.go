package vips

import (
	"frontapi/internal/models"
)

type VipPackage struct {
	models.BaseModel
	Name          string         `gorm:"column:name;type:string;not null;comment:套餐名称" json:"name"`                                // 套餐名称
	Code          string         `gorm:"column:code;type:string;not null;comment:套餐编码" json:"code"`                                // 套餐编码
	Price         float64        `gorm:"column:price;type:decimal(10,2);not null;comment:价格" json:"price"`                         // 价格
	OriginalPrice float64        `gorm:"column:original_price;type:decimal(10,2);not null;comment:原价" json:"original_price"`       // 原价
	Duration      int            `gorm:"column:duration;type:int;not null;comment:有效期(天)" json:"duration"`                         // 有效期(天)
	Description   string         `gorm:"column:description;type:text;comment:描述" json:"description"`                               // 描述
	Benefits      string         `gorm:"column:benefits;type:text;comment:权益说明" json:"benefits"`                                   // 权益说明
	Icon          string         `gorm:"column:icon;type:string;comment:图标" json:"icon"`                                           // 图标
	SortOrder     int            `gorm:"column:sort_order;type:int;default:0;comment:排序序号" json:"sort_order"`                      // 排序序号
	Status        int8           `gorm:"column:status;type:tinyint;not null;default:1;comment:状态:0-禁用,1-启用" json:"status"`         // 状态：0-禁用，1-启用
}

func (VipPackage) TableName() string {
	return "ly_vip_packages"
}
