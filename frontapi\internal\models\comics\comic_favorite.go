package comics

import (
	"frontapi/internal/models"
)

// ComicFavorite 漫画收藏模型
type ComicFavorite struct {
	models.BaseModelStruct
	UserID       string `json:"user_id" gorm:"column:user_id;not null;index:idx_user_id;uniqueIndex:uk_user_comic,priority:1" comment:"用户ID"`
	ComicID      string `json:"comic_id" gorm:"column:comic_id;not null;index:idx_comic_id;uniqueIndex:uk_user_comic,priority:2" comment:"漫画ID"`
	ComicTitle   string `json:"comic_title" gorm:"column:comic_title" comment:"漫画标题"`
	ComicCover   string `json:"comic_cover" gorm:"column:comic_cover" comment:"漫画封面"`
	Author       string `json:"author" gorm:"column:author" comment:"作者"`
	CategoryName string `json:"category_name" gorm:"column:category_name" comment:"分类名称"`
	Status       string `json:"status" gorm:"column:status" comment:"状态"`
}

// TableName 指定表名
func (ComicFavorite) TableName() string {
	return "ly_comic_favorites"
}
