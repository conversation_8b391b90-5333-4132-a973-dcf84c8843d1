package advs

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type AdvPosition struct {
	*models.BaseModelStruct
	Name        string `gorm:"column:name;type:varchar(50);not null;comment:广告位名称" json:"name"`         // 广告位名称
	Code        string `gorm:"column:code;type:varchar(50);not null;comment:广告位编码" json:"code"`         // 广告位编码
	Description string `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`      // 描述
	Width       int    `gorm:"column:width;type:int;comment:宽度" json:"width"`                           // 宽度
	Height      int    `gorm:"column:height;type:int;comment:高度" json:"height"`                         // 高度
}

func (AdvPosition) TableName() string {
	return "ly_adv_positions"
}

// 实现BaseModel接口的方法，确保AdvPosition满足BaseModel约束
func (a *AdvPosition) SetCreatedAt(time types.JSONTime) {
	if a.BaseModelStruct != nil {
		a.BaseModelStruct.SetCreatedAt(time)
	}
}

func (a *AdvPosition) SetUpdatedAt(time types.JSONTime) {
	if a.BaseModelStruct != nil {
		a.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (a *AdvPosition) SetID(id string) {
	if a.BaseModelStruct != nil {
		a.BaseModelStruct.SetID(id)
	}
}

func (a *AdvPosition) SetStatus(status int8) {
	if a.BaseModelStruct != nil {
		a.BaseModelStruct.SetStatus(status)
	}
}
