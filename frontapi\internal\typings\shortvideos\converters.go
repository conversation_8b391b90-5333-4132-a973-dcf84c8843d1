package shortvideos

import (
	"frontapi/internal/models/shortvideos"
	"frontapi/internal/typings"
	"time"
)

// formatTime 格式化时间
func formatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func ConvertShortVideoInfo(video *shortvideos.ShortVideo) ShortVideoInfo {
	return ShortVideoInfo{
		ID:            video.ID,
		Title:         video.Title,
		Description:   video.Description.ValueOrZero(),
		Cover:         video.Cover,
		URL:           video.URL,
		Duration:      video.Duration,
		Resolution:    video.Resolution,
		ViewCount:     video.ViewCount,
		LikeCount:     video.LikeCount,
		CommentCount:  video.CommentCount,
		ShareCount:    video.ShareCount,
		FavoriteCount: video.FavoriteCount,
		CreatorID:     video.CreatorID.ValueOrZero(),
		CreatorName:   "", // CreatorName字段不存在，设置为空
		CreatorAvatar: "", // CreatorAvatar字段不存在，设置为空
		CategoryID:    video.CategoryID.ValueOrZero(),
		CategoryName:  video.CategoryName.ValueOrZero(),
		Tags:          video.Tags,
		IsPaid:        video.IsPaid,
		IsFeatured:    video.IsFeatured,
		Price:         video.Price,
		Status:        video.Status,
		IsLiked:       video.IsLiked,
	}
}

func ConvertShortVideoList(videos []*shortvideos.ShortVideo) []ShortVideoInfo {
	result := make([]ShortVideoInfo, len(videos))
	for i, video := range videos {
		result[i] = ConvertShortVideoInfo(video)
	}
	return result
}
func ConvertShortVideoListResponse(videos []*shortvideos.ShortVideo, total int64, pageNo int, pageSize int) ShortVideoListResponse {
	return ShortVideoListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertShortVideoList(videos),
	}
}

func ConvertShortVideoCommentInfo(comment *shortvideos.ShortVideoComment) ShortVideoCommentInfo {
	return ShortVideoCommentInfo{
		Content:      comment.Content,
		UserNickname: comment.UserNickname.ValueOrZero(),
		UserAvatar:   comment.UserAvatar.ValueOrZero(),
		ParentID:     comment.ParentID.ValueOrZero(),
		EntityID:     comment.GetID(),
		EntityType:   2,
		RelationID:   comment.ShortID.ValueOrZero(),
		Heat:         int64(comment.Heat),
		LikeCount:    int64(comment.LikeCount),
		ReplyCount:   int64(comment.ReplyCount),
		CreatedAt:    formatTime(time.Time(comment.GetCreatedAt())),
		UpdatedAt:    formatTime(time.Time(comment.GetUpdatedAt())),
		Status:       comment.Status,
	}
}

func ConvertShortVideoCommentList(comments []*shortvideos.ShortVideoComment) []ShortVideoCommentInfo {
	result := make([]ShortVideoCommentInfo, len(comments))
	for i, comment := range comments {
		result[i] = ConvertShortVideoCommentInfo(comment)
	}
	return result
}

func ConvertShortVideoCommentListResponse(comments []*shortvideos.ShortVideoComment, total int64, pageNo int, pageSize int) ShortVideoCommentListResponse {
	return ShortVideoCommentListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertShortVideoCommentList(comments),
	}
}
