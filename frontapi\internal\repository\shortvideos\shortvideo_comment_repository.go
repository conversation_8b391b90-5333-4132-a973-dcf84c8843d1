package shortvideos

import (
	"frontapi/internal/models/shortvideos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ShortVideoCommentRepository 短视频评论数据访问接口
type ShortVideoCommentRepository interface {
	base.ExtendedRepository[shortvideos.ShortVideoComment]
}

// shortVideoCommentRepository 短视频评论数据访问实现
type shortVideoCommentRepository struct {
	base.ExtendedRepository[shortvideos.ShortVideoComment]
}

// NewShortVideoCommentRepository 创建短视频评论仓库实例
func NewShortVideoCommentRepository(db *gorm.DB) ShortVideoCommentRepository {
	return &shortVideoCommentRepository{
		ExtendedRepository: base.NewExtendedRepository[shortvideos.ShortVideoComment](db),
	}
}
