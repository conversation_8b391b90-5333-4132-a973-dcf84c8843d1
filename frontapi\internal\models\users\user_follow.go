package users

import (
	"frontapi/internal/models"
)

type UserFollow struct {
	models.BaseModel
	UserID           string         `gorm:"column:user_id;type:string;not null;index;comment:关注者ID" json:"user_id"`
	UserNickname     string         `gorm:"column:user_nickname;type:string;size:50;comment:用户昵称" json:"user_nickname"`
	UserAvatar       string         `gorm:"column:user_avatar;type:string;size:1024;comment:用户头像" json:"user_avatar"`
	FollowedID       string         `gorm:"column:followed_id;type:string;not null;index;comment:被关注者ID" json:"followed_id"`
	FollowedUsername string         `gorm:"column:followed_username;type:string;size:50;comment:被关注用户名" json:"followed_username"`
	FollowedNickname string         `gorm:"column:followed_nickname;type:string;size:50;comment:被关注用户昵称" json:"followed_nickname"`
	FollowedAvatar   string         `gorm:"column:followed_avatar;type:string;size:255;comment:被关注用户头像" json:"followed_avatar"`
	FollowedBio      string         `gorm:"column:followed_bio;type:text;comment:被关注用户简介" json:"followed_bio"`
	IsMutual         bool           `gorm:"column:is_mutual;type:bool;default:false;comment:是否互相关注" json:"is_mutual"`
	GroupName        string         `gorm:"column:group_name;type:string;size:50;comment:关注分组" json:"group_name"`
	RemarkName       string         `gorm:"column:remark_name;type:string;size:50;comment:备注名称" json:"remark_name"`
}

func (UserFollow) TableName() string {
	return "ly_user_follow"
}
