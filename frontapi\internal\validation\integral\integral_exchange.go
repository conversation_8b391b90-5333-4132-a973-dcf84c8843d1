package integral

// IntegralExchangeRequest 积分兑换请求验证模型
type IntegralExchangeRequest struct {
	GoodID       string `json:"goodId" validate:"required"`
	Quantity     int    `json:"quantity" validate:"required,min=1,max=100"`
	Address      string `json:"address" validate:"omitempty"`
	ContactName  string `json:"contactName" validate:"omitempty"`
	ContactPhone string `json:"contactPhone" validate:"omitempty"`
	Remark       string `json:"remark" validate:"omitempty,max=200"`
}

// IntegralGoodListRequest 积分商品列表请求验证模型
type IntegralGoodListRequest struct {
	Page        int    `json:"page" validate:"min=1"`
	PageSize    int    `json:"pageSize" validate:"min=1,max=100"`
	Category    string `json:"category" validate:"omitempty"`
	SortBy      string `json:"sortBy" validate:"omitempty,oneof=newest popular price"`
	MinIntegral int    `json:"minIntegral" validate:"omitempty,min=0"`
	MaxIntegral int    `json:"maxIntegral" validate:"omitempty,gtfield=MinIntegral"`
}