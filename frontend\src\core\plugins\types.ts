/**
 * 插件系统类型定义
 */

import type { App } from 'vue'
import type { Router } from 'vue-router'
import type { Pinia } from 'pinia'

/**
 * 插件上下文
 */
export interface PluginContext {
  app: App
  router: Router
  store: Pinia
  config: Record<string, any>
}

/**
 * 插件元数据
 */
export interface PluginMeta {
  name: string
  version: string
  description?: string
  author?: string
  dependencies?: string[]
  peerDependencies?: string[]
  keywords?: string[]
  homepage?: string
  repository?: string
  license?: string
}

/**
 * 插件配置
 */
export interface PluginConfig {
  enabled?: boolean
  priority?: number
  options?: Record<string, any>
  environment?: 'development' | 'production' | 'test' | 'all'
}

/**
 * 插件生命周期钩子
 */
export interface PluginHooks {
  beforeInstall?: (context: PluginContext) => void | Promise<void>
  afterInstall?: (context: PluginContext) => void | Promise<void>
  beforeUninstall?: (context: PluginContext) => void | Promise<void>
  afterUninstall?: (context: PluginContext) => void | Promise<void>
  onError?: (error: Error, context: PluginContext) => void
}

/**
 * 插件接口
 */
export interface Plugin {
  meta: PluginMeta
  config?: PluginConfig
  hooks?: PluginHooks
  install: (context: PluginContext) => void | Promise<void>
  uninstall?: (context: PluginContext) => void | Promise<void>
  update?: (context: PluginContext, oldVersion: string) => void | Promise<void>
}

/**
 * 插件状态
 */
export type PluginStatus = 'pending' | 'installing' | 'installed' | 'uninstalling' | 'uninstalled' | 'error'

/**
 * 插件实例
 */
export interface PluginInstance {
  plugin: Plugin
  status: PluginStatus
  error?: Error
  installedAt?: Date
  updatedAt?: Date
}

/**
 * 插件管理器配置
 */
export interface PluginManagerConfig {
  autoInstall?: boolean
  strictMode?: boolean
  maxConcurrency?: number
  timeout?: number
  retries?: number
  logger?: {
    debug?: boolean
    level?: 'error' | 'warn' | 'info' | 'debug'
  }
}

/**
 * 插件事件
 */
export interface PluginEvent {
  type: 'install' | 'uninstall' | 'update' | 'error' | 'statusChange'
  plugin: string
  data?: any
  timestamp: Date
}

/**
 * 插件管理器接口
 */
export interface PluginManager {
  // 插件注册
  register(plugin: Plugin): Promise<void>
  unregister(name: string): Promise<void>
  
  // 插件安装
  install(name: string): Promise<void>
  uninstall(name: string): Promise<void>
  update(name: string): Promise<void>
  
  // 插件查询
  get(name: string): PluginInstance | undefined
  getAll(): PluginInstance[]
  getInstalled(): PluginInstance[]
  isInstalled(name: string): boolean
  
  // 插件状态
  getStatus(name: string): PluginStatus | undefined
  setStatus(name: string, status: PluginStatus): void
  
  // 事件系统
  on(event: string, handler: (event: PluginEvent) => void): void
  off(event: string, handler: (event: PluginEvent) => void): void
  emit(event: string, data?: any): void
  
  // 生命周期
  init(context: PluginContext): Promise<void>
  destroy(): Promise<void>
}

/**
 * 插件工厂函数
 */
export type PluginFactory = (options?: Record<string, any>) => Plugin

/**
 * 插件加载器
 */
export interface PluginLoader {
  load(name: string): Promise<Plugin>
  loadFromUrl(url: string): Promise<Plugin>
  loadFromFile(path: string): Promise<Plugin>
}

/**
 * 插件仓库
 */
export interface PluginRepository {
  search(query: string): Promise<PluginMeta[]>
  get(name: string): Promise<Plugin>
  publish(plugin: Plugin): Promise<void>
  unpublish(name: string, version: string): Promise<void>
}

/**
 * 插件依赖解析器
 */
export interface DependencyResolver {
  resolve(dependencies: string[]): Promise<Plugin[]>
  checkCompatibility(plugin: Plugin, context: PluginContext): boolean
}

/**
 * 插件验证器
 */
export interface PluginValidator {
  validate(plugin: Plugin): Promise<ValidationResult>
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * 插件缓存
 */
export interface PluginCache {
  get(key: string): any
  set(key: string, value: any, ttl?: number): void
  delete(key: string): void
  clear(): void
  has(key: string): boolean
}

/**
 * 插件日志记录器
 */
export interface PluginLogger {
  debug(message: string, ...args: any[]): void
  info(message: string, ...args: any[]): void
  warn(message: string, ...args: any[]): void
  error(message: string, ...args: any[]): void
}

/**
 * 插件权限
 */
export interface PluginPermissions {
  api?: string[]
  routes?: string[]
  components?: string[]
  stores?: string[]
  files?: string[]
}

/**
 * 插件安全策略
 */
export interface PluginSecurityPolicy {
  allowedDomains?: string[]
  blockedDomains?: string[]
  maxMemoryUsage?: number
  maxExecutionTime?: number
  sandboxed?: boolean
}

/**
 * 插件性能监控
 */
export interface PluginPerformance {
  installTime?: number
  memoryUsage?: number
  cpuUsage?: number
  networkRequests?: number
}

/**
 * 扩展的插件实例
 */
export interface ExtendedPluginInstance extends PluginInstance {
  permissions?: PluginPermissions
  security?: PluginSecurityPolicy
  performance?: PluginPerformance
}