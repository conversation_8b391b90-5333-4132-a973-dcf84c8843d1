<template>
    <div>
       <button
            class="tool-btn user-btn"
            @click="toggleUserMenu"
            aria-label="User menu"
        >
            <i class="pi pi-user"></i>
        </button>

    </div>
</template>

<script setup lang="ts">
import { useTranslation } from "@/core/plugins/i18n/composables";
import { ref } from "vue";

const { t } = useTranslation();

const showUserMenu = ref(false);

const toggleUserMenu = () => {
    showUserMenu.value = !showUserMenu.value;
};


</script>

<style scoped lang="scss">
</style>