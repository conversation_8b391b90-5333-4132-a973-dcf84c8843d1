# UserHoverCard 用户悬浮卡片组件

一个美观的用户信息悬浮卡片组件，支持鼠标悬停显示用户详细信息。

## 功能特性

- 🎨 美观的卡片设计，支持渐变背景和阴影效果
- 🖱️ 鼠标悬停触发，支持延迟显示和智能隐藏
- 📊 用户统计信息展示（关注者、视频、动态、短视频数量）
- 👤 用户头像、昵称、简介展示
- ✅ 认证标识和在线状态指示
- 🔘 关注/取消关注按钮
- 📱 响应式设计，适配不同屏幕尺寸
- 🧩 模块化设计，拆分为多个独立小组件

## 组件结构

```
UserHoverCard/
├── UserHoverCard.vue     # 主组件
├── UserAvatar.vue        # 用户头像组件
├── UserStats.vue         # 用户统计组件
├── FollowButton.vue      # 关注按钮组件
├── index.ts             # 导出文件
└── README.md            # 说明文档
```

## 使用方法

### 基础用法

```vue
<template>
  <div>
    <UserHoverCard
      :user-info="userInfo"
      @click="handleUserClick"
      @follow="handleFollow"
      @unfollow="handleUnfollow"
    />
  </div>
</template>

<script setup lang="ts">
import { UserHoverCard } from '@/shared/components/UserHoverCard'
import type { Author } from '@/types/community'

const userInfo: Author = {
  id: '1',
  username: 'johndoe',
  nickname: '约翰·多伊',
  avatar: 'https://example.com/avatar.jpg',
  bio: '这是一个简短的个人简介...',
  followCount: 1234,
  totalVideos: 56,
  totalPosts: 89,
  totalShorts: 123,
  userType: 2, // 2表示创作者
  isFollowed: false,
  // ... 其他字段
}

const handleUserClick = (user: User) => {
  console.log('用户点击:', user)
  // 跳转到用户详情页
}

const handleFollow = (user: User) => {
  console.log('关注用户:', user)
  // 调用关注API
}

const handleUnfollow = (user: User) => {
  console.log('取消关注用户:', user)
  // 调用取消关注API
}
</script>
```

### 自定义配置

```vue
<template>
  <UserHoverCard
    :user-info="userInfo"
    :avatar-size="64"
    :show-avatar-border="false"
    :hover-delay="500"
    @click="handleUserClick"
    @follow="handleFollow"
    @unfollow="handleUnfollow"
  />
</template>
```

## Props

### UserHoverCard Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| userInfo | User | - | 用户信息对象（必填） |
| avatarSize | number | 48 | 触发器头像大小 |
| showAvatarBorder | boolean | true | 是否显示头像边框 |
| hoverDelay | number | 300 | 悬停延迟时间（毫秒） |

### User 类型定义

```typescript
interface User {
  id: string
  username: string
  nickname?: string
  avatar: string
  bio?: string
  followCount: number
  totalVideos: number
  totalPosts: number
  totalShorts: number
  heat: number
  userType: number // 1-普通用户 2-创作者
  creatorLevel: number
  isLiked: boolean
  isFollowed: boolean
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | user: User | 点击用户头像时触发 |
| follow | user: User | 点击关注按钮时触发 |
| unfollow | user: User | 点击取消关注按钮时触发 |

## 独立组件使用

你也可以单独使用各个子组件：

### UserAvatar

```vue
<template>
  <UserAvatar
    :avatar="user.avatar"
    :username="user.username"
    :nickname="user.nickname"
    :size="48"
    :is-online="user.isFollowed"
    :is-verified="user.userType === 2"
  />
</template>
```

### UserStats

```vue
<template>
  <UserStats
    :follow-count="user.followCount"
    :total-videos="user.totalVideos"
    :total-posts="user.totalPosts"
    :total-shorts="user.totalShorts"
  />
</template>
```

### FollowButton

```vue
<template>
  <FollowButton
    :is-followed="user.isFollowed"
    :loading="followLoading"
    @follow="handleFollow"
    @unfollow="handleUnfollow"
  />
</template>
```

## 样式定制

组件使用了 SCSS 变量，你可以通过覆盖这些变量来自定义样式：

```scss
// 自定义主题色
.user-hover-card {
  --primary-color: #667eea;
  --primary-hover-color: #5a6fd8;
  --creator-badge-bg: linear-gradient(135deg, #fbbf24, #f59e0b);
}
```

## 注意事项

1. 确保传入的 `userInfo` 包含所有必要的字段
2. 组件依赖 Element Plus，请确保已正确安装和配置
3. 需要 `@/utils/format` 中的 `formatCount` 函数来格式化数字
4. 悬浮卡片会自动处理边界检测，避免超出屏幕范围