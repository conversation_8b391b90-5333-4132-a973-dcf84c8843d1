package redis

import (
	"context"
	"fmt"

	"frontapi/internal/service/base/extfollow/types"

	goredis "github.com/go-redis/redis/v8"
)

// RankingOperations 排名操作处理器
type RankingOperations struct {
	client   *goredis.Client
	cacheKey *types.CacheKey
	config   *Config
}

// NewRankingOperations 创建排名操作处理器
func NewRankingOperations(client *goredis.Client, cacheKey *types.CacheKey, config *Config) *RankingOperations {
	return &RankingOperations{
		client:   client,
		cacheKey: cacheKey,
		config:   config,
	}
}

// UpdateInfluenceRank 更新影响力排名
func (r *RankingOperations) UpdateInfluenceRank(ctx context.Context, userID string, score float64) error {
	rankingKey := r.cacheKey.InfluenceRankingKey()

	err := r.client.ZAdd(ctx, rankingKey, &goredis.Z{
		Score:  score,
		Member: userID,
	}).Err()

	if err != nil {
		return fmt.Errorf("更新影响力排名失败: %w", err)
	}

	// 设置过期时间
	r.client.Expire(ctx, rankingKey, r.config.DefaultTTL)

	return nil
}

// GetInfluenceRanking 获取影响力排行榜
func (r *RankingOperations) GetInfluenceRanking(ctx context.Context, limit int) ([]string, error) {
	rankingKey := r.cacheKey.InfluenceRankingKey()

	// 按分数降序获取排行榜
	results, err := r.client.ZRevRange(ctx, rankingKey, 0, int64(limit-1)).Result()
	if err != nil {
		if err == goredis.Nil {
			return []string{}, nil
		}
		return nil, fmt.Errorf("获取影响力排行榜失败: %w", err)
	}

	return results, nil
}

// GetInfluenceRankingWithScores 获取带分数的影响力排行榜
func (r *RankingOperations) GetInfluenceRankingWithScores(ctx context.Context, limit int) (map[string]float64, error) {
	rankingKey := r.cacheKey.InfluenceRankingKey()

	// 按分数降序获取排行榜和分数
	results, err := r.client.ZRevRangeWithScores(ctx, rankingKey, 0, int64(limit-1)).Result()
	if err != nil {
		if err == goredis.Nil {
			return make(map[string]float64), nil
		}
		return nil, fmt.Errorf("获取影响力排行榜失败: %w", err)
	}

	ranking := make(map[string]float64)
	for _, result := range results {
		userID := result.Member.(string)
		score := result.Score
		ranking[userID] = score
	}

	return ranking, nil
}
