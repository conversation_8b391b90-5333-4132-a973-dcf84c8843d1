package base

import (
	"crypto/rand"
	"fmt"
	"strings"
	"time"
)

// GenerateID 生成唯一ID
func GenerateID() string {
	// 生成时间戳部分
	timestamp := time.Now().UnixNano()
	
	// 生成随机部分
	bytes := make([]byte, 8)
	_, err := rand.Read(bytes)
	if err != nil {
		// 如果随机数生成失败，使用时间戳作为后备
		return fmt.Sprintf("%d", timestamp)
	}
	
	// 组合时间戳和随机数
	return fmt.Sprintf("%d%x", timestamp, bytes)
}

// GenerateShortID 生成短ID（适用于不需要全局唯一的场景）
func GenerateShortID() string {
	bytes := make([]byte, 6)
	_, err := rand.Read(bytes)
	if err != nil {
		return fmt.Sprintf("%d", time.Now().UnixNano()%1000000)
	}
	return fmt.Sprintf("%x", bytes)
}

// GenerateUUID 生成UUID格式的ID
func GenerateUUID() string {
	bytes := make([]byte, 16)
	_, err := rand.Read(bytes)
	if err != nil {
		// 后备方案
		now := time.Now().UnixNano()
		return fmt.Sprintf("%08x-%04x-%04x-%04x-%012x", 
			now&0xffffffff, 
			(now>>32)&0xffff, 
			(now>>48)&0xffff, 
			(now>>16)&0xffff, 
			now&0xffffffffffff)
	}
	
	// 设置版本号和变体
	bytes[6] = (bytes[6] & 0x0f) | 0x40 // Version 4
	bytes[8] = (bytes[8] & 0x3f) | 0x80 // Variant 10
	
	return fmt.Sprintf("%08x-%04x-%04x-%04x-%012x",
		bytes[0:4], bytes[4:6], bytes[6:8], bytes[8:10], bytes[10:16])
}

// BuildOrderBy 构建排序字符串
func BuildOrderBy(orderFields []string, direction string) string {
	if len(orderFields) == 0 {
		return ""
	}
	
	if direction == "" {
		direction = "ASC"
	}
	
	direction = strings.ToUpper(direction)
	if direction != "ASC" && direction != "DESC" {
		direction = "ASC"
	}
	
	orderParts := make([]string, len(orderFields))
	for i, field := range orderFields {
		orderParts[i] = fmt.Sprintf("%s %s", field, direction)
	}
	
	return strings.Join(orderParts, ", ")
}

// BuildMultiOrderBy 构建多字段排序字符串
func BuildMultiOrderBy(orders map[string]string) string {
	if len(orders) == 0 {
		return ""
	}
	
	orderParts := make([]string, 0, len(orders))
	for field, direction := range orders {
		direction = strings.ToUpper(direction)
		if direction != "ASC" && direction != "DESC" {
			direction = "ASC"
		}
		orderParts = append(orderParts, fmt.Sprintf("%s %s", field, direction))
	}
	
	return strings.Join(orderParts, ", ")
}

// ValidatePageParams 验证分页参数
func ValidatePageParams(page, pageSize int) (int, int) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}
	return page, pageSize
}

// CalculateOffset 计算偏移量
func CalculateOffset(page, pageSize int) int {
	page, pageSize = ValidatePageParams(page, pageSize)
	return (page - 1) * pageSize
}

// SanitizeString 清理字符串（防止SQL注入）
func SanitizeString(input string) string {
	// 移除危险字符
	dangerous := []string{"'", "\"", ";", "--", "/*", "*/", "xp_", "sp_"}
	result := input
	for _, char := range dangerous {
		result = strings.ReplaceAll(result, char, "")
	}
	return strings.TrimSpace(result)
}

// BuildLikePattern 构建LIKE查询模式
func BuildLikePattern(keyword string, matchType string) string {
	if keyword == "" {
		return ""
	}
	
	// 清理关键字
	keyword = SanitizeString(keyword)
	
	switch matchType {
	case "start":
		return keyword + "%"
	case "end":
		return "%" + keyword
	case "exact":
		return keyword
	default: // "contains"
		return "%" + keyword + "%"
	}
}

// InSlice 检查元素是否在切片中
func InSlice[T comparable](slice []T, item T) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// RemoveFromSlice 从切片中移除元素
func RemoveFromSlice[T comparable](slice []T, item T) []T {
	result := make([]T, 0, len(slice))
	for _, s := range slice {
		if s != item {
			result = append(result, s)
		}
	}
	return result
}

// UniqueSlice 去重切片
func UniqueSlice[T comparable](slice []T) []T {
	seen := make(map[T]bool)
	result := make([]T, 0, len(slice))
	
	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// ChunkSlice 将切片分块
func ChunkSlice[T any](slice []T, chunkSize int) [][]T {
	if chunkSize <= 0 {
		return nil
	}
	
	var chunks [][]T
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	
	return chunks
}

// MapSlice 映射切片
func MapSlice[T, R any](slice []T, fn func(T) R) []R {
	result := make([]R, len(slice))
	for i, item := range slice {
		result[i] = fn(item)
	}
	return result
}

// FilterSlice 过滤切片
func FilterSlice[T any](slice []T, fn func(T) bool) []T {
	result := make([]T, 0, len(slice))
	for _, item := range slice {
		if fn(item) {
			result = append(result, item)
		}
	}
	return result
}

// GetTimeRangeCondition 获取时间范围条件
func GetTimeRangeCondition(timeRange string) (map[string]interface{}, error) {
	condition := make(map[string]interface{})
	now := time.Now()
	
	switch timeRange {
	case "today":
		startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		condition["created_at_start"] = startOfDay
	case "yesterday":
		yesterday := now.AddDate(0, 0, -1)
		startOfYesterday := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
		endOfYesterday := startOfYesterday.Add(24*time.Hour - time.Nanosecond)
		condition["created_at_start"] = startOfYesterday
		condition["created_at_end"] = endOfYesterday
	case "week":
		weekAgo := now.AddDate(0, 0, -7)
		condition["created_at_start"] = weekAgo
	case "month":
		monthAgo := now.AddDate(0, -1, 0)
		condition["created_at_start"] = monthAgo
	case "quarter":
		quarterAgo := now.AddDate(0, -3, 0)
		condition["created_at_start"] = quarterAgo
	case "year":
		yearAgo := now.AddDate(-1, 0, 0)
		condition["created_at_start"] = yearAgo
	default:
		return nil, fmt.Errorf("unsupported time range: %s", timeRange)
	}
	
	return condition, nil
}

// MergeConditions 合并查询条件
func MergeConditions(conditions ...map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	
	for _, condition := range conditions {
		for key, value := range condition {
			result[key] = value
		}
	}
	
	return result
}

// ValidateStatus 验证状态值
func ValidateStatus(status int) bool {
	// 通常状态值：-1=删除, 0=禁用, 1=正常, 2=审核中
	return status >= -1 && status <= 2
}

// GetDefaultOrderBy 获取默认排序
func GetDefaultOrderBy(modelType string) string {
	defaultOrders := map[string]string{
		"post":       "created_at DESC",
		"user":       "created_at DESC",
		"video":      "created_at DESC",
		"picture":    "created_at DESC",
		"shortvideo": "created_at DESC",
		"comment":    "created_at ASC",
		"category":   "sort_order ASC, created_at DESC",
	}
	
	if order, exists := defaultOrders[modelType]; exists {
		return order
	}
	
	return "created_at DESC"
}