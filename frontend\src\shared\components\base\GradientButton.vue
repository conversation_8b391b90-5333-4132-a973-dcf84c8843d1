<template>
    <button class="gradient-button" :class="[
        `gradient-button--${type}`,
        { 'gradient-button--block': block },
        { 'gradient-button--disabled': disabled },
        sizeClass
    ]" :disabled="disabled" @click="handleClick">
        <slot name="prefix"></slot>
        <span class="gradient-button__content">
            <slot></slot>
        </span>
        <slot name="suffix"></slot>
    </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    // 按钮类型：primary, secondary, accent, success, warning, error, info
    type: {
        type: String,
        default: 'primary',
        validator: (value: string) => [
            'primary',
            'secondary',
            'accent',
            'success',
            'warning',
            'error',
            'info'
        ].includes(value)
    },
    // 按钮尺寸：small, medium, large
    size: {
        type: String,
        default: 'medium',
        validator: (value: string) => ['small', 'medium', 'large'].includes(value)
    },
    // 是否禁用
    disabled: {
        type: Boolean,
        default: false
    },
    // 是否为块级按钮（宽度100%）
    block: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['click']);

// 计算尺寸类名
const sizeClass = computed(() => {
    return `gradient-button--${props.size}`;
});

// 处理点击事件
const handleClick = (event: MouseEvent) => {
    if (!props.disabled) {
        emit('click', event);
    }
};
</script>

<style lang="scss" scoped>
.gradient-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    // 尺寸变体
    &--small {
        padding: 6px 12px;
        font-size: 0.875rem;
    }

    &--medium {
        padding: 8px 16px;
        font-size: 1rem;
    }

    &--large {
        padding: 10px 20px;
        font-size: 1.125rem;
    }

    // 块级按钮
    &--block {
        width: 100%;
        display: flex;
    }

    // 禁用状态
    &--disabled {
        opacity: 0.6;
        cursor: not-allowed;
        box-shadow: none;

        &:hover {
            transform: none;
            box-shadow: none;
        }
    }

    // 按钮内容
    &__content {
        position: relative;
        z-index: 1;
    }

    // 悬停效果
    &:hover:not(.gradient-button--disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &:active:not(.gradient-button--disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    // 颜色变体
    &--primary {
        background: var(--gradient-primary, linear-gradient(45deg, #8e2de2, #7a25c9, #4a00e0));

        &:hover:not(.gradient-button--disabled) {
            background: var(--gradient-button-hover, linear-gradient(45deg, #9d3cf1, #8e2de2, #5500ff));
        }
    }

    &--secondary {
        background: var(--gradient-secondary, linear-gradient(45deg, #4a00e0, #3b00b3, #2c0086));

        &:hover:not(.gradient-button--disabled) {
            background: linear-gradient(45deg, #5500ff, #4a00e0, #3b00b3);
        }
    }

    &--accent {
        background: var(--gradient-accent, linear-gradient(45deg, #ff9800, #e68900, #cc7a00));

        &:hover:not(.gradient-button--disabled) {
            background: linear-gradient(45deg, #ffa726, #ff9800, #e68900);
        }
    }

    &--success {
        background: var(--gradient-success, linear-gradient(45deg, #4caf50, #43a047, #388e3c));

        &:hover:not(.gradient-button--disabled) {
            background: linear-gradient(45deg, #66bb6a, #4caf50, #43a047);
        }
    }

    &--warning {
        background: var(--gradient-warning, linear-gradient(45deg, #ff9800, #e68900, #cc7a00));

        &:hover:not(.gradient-button--disabled) {
            background: linear-gradient(45deg, #ffa726, #ff9800, #e68900);
        }
    }

    &--error {
        background: var(--gradient-error, linear-gradient(45deg, #f44336, #e53935, #d32f2f));

        &:hover:not(.gradient-button--disabled) {
            background: linear-gradient(45deg, #ef5350, #f44336, #e53935);
        }
    }

    &--info {
        background: var(--gradient-info, linear-gradient(45deg, #2196f3, #1e88e5, #1976d2));

        &:hover:not(.gradient-button--disabled) {
            background: linear-gradient(45deg, #42a5f5, #2196f3, #1e88e5);
        }
    }
}
</style>