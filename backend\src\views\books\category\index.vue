<template>
  <div class="category-list">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="card-title">分类列表</span>
          <el-button type="primary" @click="onAdd">添加分类</el-button>
        </div>
      </template>

      <!-- 搜索栏组件 -->
      <SearchBar @search="onSearch" @reset="onReset" />

      <!-- 分类表格组件 -->
      <CategoryTable
        :loading="loading"
        :category-list="categoryList"
        @edit="onEdit"
        @delete="onDelete"
      />

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onPageChange"
        />
      </div>

      <!-- 分类表单对话框组件 -->
      <CategoryDialog
        :visible="dialogVisible"
        :category="currentCategory"
        @submit="submitForm"
        :maxOrder="maxOrder"
        @close="dialogVisible = false"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getBookCategoryList, createBookCategory, updateBookCategory, deleteBookCategory } from '@/service/api/books/category';
import type { BookCategory, CreateBookCategoryRequest, UpdateBookCategoryRequest, BookParams } from '@/types/books';
import SearchBar from './components/SearchBar.vue';
import CategoryTable from './components/CategoryTable.vue';
import CategoryDialog from './components/CategoryDialog.vue';

const loading = ref(false);
const categoryList = ref<BookCategory[]>([]);
const pagination = reactive({ page: 1, pageSize: 10, total: 0 });
const dialogVisible = ref(false);
const currentCategory = ref<BookCategory>();
const maxOrder = ref(1);

const fetchList = async (params?: any) => {
  loading.value = true;
  try {
    const queryParams: BookParams = {
      page: { pageNo: pagination.page, pageSize: pagination.pageSize },
      data: { ...params }
    };
    const {response,data} = await getBookCategoryList(queryParams) as any;
    if (response.data.code === 2000) {
      categoryList.value = data.list;
      pagination.total = data.total;
      getMaxOrder();
    }
  } finally {
    loading.value = false;
  }
};

const onSearch = (params: any) => {
  pagination.page = 1;
  fetchList(params);
};

const onReset = () => {
  pagination.page = 1;
  fetchList();
};

const onPageChange = (page: number) => {
  pagination.page = page;
  fetchList();
};

const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchList();
};

const onAdd = () => {
  currentCategory.value = undefined;
  dialogVisible.value = true;
};

const onEdit = (row: BookCategory) => {
  currentCategory.value = row;
  dialogVisible.value = true;
};
const getMaxOrder=()=>{
  if(categoryList.value.length>0){
    maxOrder.value = Math.max(...categoryList.value.map(item => item.sort_order));
    console.log('maxOrder',maxOrder.value);
  }
}

const onDelete = (row: BookCategory) => {
  ElMessageBox.confirm('确认删除该分类吗？', '提示', {
    type: 'warning',
  }).then(async () => {
    try {
      const res = await deleteBookCategory(row.id);
      if (res.code === 2000) {
        ElMessage.success('删除成功');
        fetchList();
      }
    } catch (error) {
      console.error('删除分类失败:', error);
      ElMessage.error('删除失败');
    }
  });
};

const submitForm = async (formData: any) => {
  try {
    const isEdit = formData.id;
    const res = isEdit
      ? await updateBookCategory({ data: formData })
      : await createBookCategory({ data: formData });
    const {response,data} = res as any;
    if (response.data.code === 2000) {
      ElMessage.success(isEdit ? '更新成功' : '创建成功');
      dialogVisible.value = false;
      fetchList();
    }
  } catch (error) {
    console.error('提交分类表单失败:', error);
    ElMessage.error('操作失败');
  }
};

onMounted(() => {
  fetchList();
});
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}
</style>
