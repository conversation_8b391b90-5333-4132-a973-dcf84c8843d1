package videos

import (
	"frontapi/internal/models/videos"
	userRepo "frontapi/internal/repository/users"
	repo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

type VideoLikeService interface {
	base.IExtendedService[videos.VideoLike]
}
type videoLikeService struct {
	*base.ExtendedService[videos.VideoLike]
	videoLikeRepo repo.VideoLikeRepository
	videoRepo     repo.VideoRepository
	userRepo      userRepo.UserRepository
}

func NewVideoLikeService(videoLikeRepo repo.VideoLikeRepository, videoRepo repo.VideoRepository, userRepo userRepo.UserRepository) VideoLikeService {
	return &videoLikeService{
		ExtendedService: base.NewExtendedService[videos.VideoLike](videoLikeRepo, "video_like"),
		videoLikeRepo:   videoLikeRepo,
		videoRepo:       videoRepo,
		userRepo:        userRepo,
	}
}
