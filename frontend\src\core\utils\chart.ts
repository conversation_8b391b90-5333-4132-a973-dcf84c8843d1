/**
 * 图表工具函数
 */

/**
 * 图表类型
 */
export type ChartType = 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'bubble' | 'radar' | 'polar'

/**
 * 图表数据点
 */
export interface ChartDataPoint {
  x?: number | string | Date
  y?: number
  label?: string
  value?: number
  color?: string
}

/**
 * 图表数据集
 */
export interface ChartDataset {
  label: string
  data: ChartDataPoint[] | number[]
  backgroundColor?: string | string[]
  borderColor?: string | string[]
  borderWidth?: number
  fill?: boolean
  tension?: number
  pointRadius?: number
  pointHoverRadius?: number
}

/**
 * 图表配置
 */
export interface ChartConfig {
  type: ChartType
  data: {
    labels?: string[]
    datasets: ChartDataset[]
  }
  options?: ChartOptions
}

/**
 * 图表选项
 */
export interface ChartOptions {
  responsive?: boolean
  maintainAspectRatio?: boolean
  title?: {
    display?: boolean
    text?: string
    fontSize?: number
    fontColor?: string
  }
  legend?: {
    display?: boolean
    position?: 'top' | 'bottom' | 'left' | 'right'
  }
  scales?: {
    x?: AxisOptions
    y?: AxisOptions
  }
  plugins?: {
    tooltip?: TooltipOptions
    legend?: LegendOptions
  }
  animation?: AnimationOptions
}

/**
 * 坐标轴选项
 */
export interface AxisOptions {
  display?: boolean
  title?: {
    display?: boolean
    text?: string
  }
  min?: number
  max?: number
  grid?: {
    display?: boolean
    color?: string
  }
  ticks?: {
    display?: boolean
    fontSize?: number
    fontColor?: string
    callback?: (value: any) => string
  }
}

/**
 * 提示框选项
 */
export interface TooltipOptions {
  enabled?: boolean
  backgroundColor?: string
  titleColor?: string
  bodyColor?: string
  borderColor?: string
  borderWidth?: number
  callbacks?: {
    title?: (context: any[]) => string
    label?: (context: any) => string
  }
}

/**
 * 图例选项
 */
export interface LegendOptions {
  display?: boolean
  position?: 'top' | 'bottom' | 'left' | 'right'
  labels?: {
    color?: string
    fontSize?: number
    usePointStyle?: boolean
  }
}

/**
 * 动画选项
 */
export interface AnimationOptions {
  duration?: number
  easing?: 'linear' | 'easeInQuad' | 'easeOutQuad' | 'easeInOutQuad'
  onComplete?: () => void
  onProgress?: (animation: any) => void
}

/**
 * 颜色主题
 */
export interface ColorTheme {
  primary: string[]
  secondary: string[]
  success: string[]
  warning: string[]
  danger: string[]
  info: string[]
  light: string[]
  dark: string[]
}

/**
 * 默认颜色主题
 */
export const defaultColorTheme: ColorTheme = {
  primary: ['#007bff', '#0056b3', '#004085', '#002752'],
  secondary: ['#6c757d', '#545b62', '#3d4449', '#2c3034'],
  success: ['#28a745', '#1e7e34', '#155724', '#0a3622'],
  warning: ['#ffc107', '#d39e00', '#b08800', '#7a5f00'],
  danger: ['#dc3545', '#bd2130', '#a71e2a', '#721c24'],
  info: ['#17a2b8', '#117a8b', '#0c5460', '#062c33'],
  light: ['#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da'],
  dark: ['#343a40', '#23272b', '#1d2124', '#171a1d']
}

/**
 * 生成颜色调色板
 * @param baseColor 基础颜色
 * @param count 颜色数量
 * @returns 颜色数组
 */
export function generateColorPalette(baseColor: string, count: number): string[] {
  const colors: string[] = []
  const hsl = hexToHsl(baseColor)
  
  for (let i = 0; i < count; i++) {
    const hue = (hsl.h + (i * 360 / count)) % 360
    const saturation = Math.max(0.3, hsl.s - (i * 0.1))
    const lightness = Math.max(0.2, Math.min(0.8, hsl.l + (i % 2 === 0 ? 0.1 : -0.1)))
    
    colors.push(hslToHex(hue, saturation, lightness))
  }
  
  return colors
}

/**
 * 十六进制颜色转HSL
 * @param hex 十六进制颜色
 * @returns HSL对象
 */
export function hexToHsl(hex: string): { h: number; s: number; l: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255
  const g = parseInt(hex.slice(3, 5), 16) / 255
  const b = parseInt(hex.slice(5, 7), 16) / 255
  
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  const l = (max + min) / 2
  
  if (max !== min) {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
    }
    
    h /= 6
  }
  
  return { h: h * 360, s, l }
}

/**
 * HSL转十六进制颜色
 * @param h 色相
 * @param s 饱和度
 * @param l 亮度
 * @returns 十六进制颜色
 */
export function hslToHex(h: number, s: number, l: number): string {
  h /= 360
  
  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1/6) return p + (q - p) * 6 * t
    if (t < 1/2) return q
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
    return p
  }
  
  let r: number, g: number, b: number
  
  if (s === 0) {
    r = g = b = l
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }
  
  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/**
 * 生成渐变色
 * @param startColor 起始颜色
 * @param endColor 结束颜色
 * @param steps 步数
 * @returns 渐变色数组
 */
export function generateGradient(startColor: string, endColor: string, steps: number): string[] {
  const start = hexToRgb(startColor)
  const end = hexToRgb(endColor)
  const colors: string[] = []
  
  for (let i = 0; i < steps; i++) {
    const ratio = i / (steps - 1)
    const r = Math.round(start.r + (end.r - start.r) * ratio)
    const g = Math.round(start.g + (end.g - start.g) * ratio)
    const b = Math.round(start.b + (end.b - start.b) * ratio)
    
    colors.push(rgbToHex(r, g, b))
  }
  
  return colors
}

/**
 * 十六进制颜色转RGB
 * @param hex 十六进制颜色
 * @returns RGB对象
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } {
  const r = parseInt(hex.slice(1, 3), 16)
  const g = parseInt(hex.slice(3, 5), 16)
  const b = parseInt(hex.slice(5, 7), 16)
  
  return { r, g, b }
}

/**
 * RGB转十六进制颜色
 * @param r 红色值
 * @param g 绿色值
 * @param b 蓝色值
 * @returns 十六进制颜色
 */
export function rgbToHex(r: number, g: number, b: number): string {
  const toHex = (c: number) => {
    const hex = c.toString(16)
    return hex.length === 1 ? '0' + hex : hex
  }
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`
}

/**
 * 创建线性图表配置
 * @param labels 标签数组
 * @param datasets 数据集数组
 * @param options 选项
 * @returns 图表配置
 */
export function createLineChart(
  labels: string[],
  datasets: ChartDataset[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'line',
    data: {
      labels,
      datasets: datasets.map((dataset, index) => ({
        ...dataset,
        borderColor: dataset.borderColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        backgroundColor: dataset.backgroundColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length] + '20',
        fill: dataset.fill !== undefined ? dataset.fill : false,
        tension: dataset.tension !== undefined ? dataset.tension : 0.4
      }))
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...options
    }
  }
}

/**
 * 创建柱状图表配置
 * @param labels 标签数组
 * @param datasets 数据集数组
 * @param options 选项
 * @returns 图表配置
 */
export function createBarChart(
  labels: string[],
  datasets: ChartDataset[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'bar',
    data: {
      labels,
      datasets: datasets.map((dataset, index) => ({
        ...dataset,
        backgroundColor: dataset.backgroundColor || generateColorPalette(defaultColorTheme.primary[0], labels.length),
        borderColor: dataset.borderColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        borderWidth: dataset.borderWidth || 1
      }))
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...options
    }
  }
}

/**
 * 创建饼图配置
 * @param labels 标签数组
 * @param data 数据数组
 * @param options 选项
 * @returns 图表配置
 */
export function createPieChart(
  labels: string[],
  data: number[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'pie',
    data: {
      labels,
      datasets: [{
        label: 'Data',
        data,
        backgroundColor: generateColorPalette(defaultColorTheme.primary[0], labels.length),
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...options
    }
  }
}

/**
 * 创建环形图配置
 * @param labels 标签数组
 * @param data 数据数组
 * @param options 选项
 * @returns 图表配置
 */
export function createDoughnutChart(
  labels: string[],
  data: number[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'doughnut',
    data: {
      labels,
      datasets: [{
        label: 'Data',
        data,
        backgroundColor: generateColorPalette(defaultColorTheme.primary[0], labels.length),
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...options
    }
  }
}

/**
 * 创建面积图配置
 * @param labels 标签数组
 * @param datasets 数据集数组
 * @param options 选项
 * @returns 图表配置
 */
export function createAreaChart(
  labels: string[],
  datasets: ChartDataset[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'line',
    data: {
      labels,
      datasets: datasets.map((dataset, index) => ({
        ...dataset,
        borderColor: dataset.borderColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        backgroundColor: dataset.backgroundColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length] + '40',
        fill: true,
        tension: dataset.tension !== undefined ? dataset.tension : 0.4
      }))
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...options
    }
  }
}

/**
 * 创建散点图配置
 * @param datasets 数据集数组
 * @param options 选项
 * @returns 图表配置
 */
export function createScatterChart(
  datasets: ChartDataset[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'scatter',
    data: {
      datasets: datasets.map((dataset, index) => ({
        ...dataset,
        backgroundColor: dataset.backgroundColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        borderColor: dataset.borderColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        pointRadius: dataset.pointRadius || 5
      }))
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          type: 'linear',
          position: 'bottom'
        }
      },
      ...options
    }
  }
}

/**
 * 创建雷达图配置
 * @param labels 标签数组
 * @param datasets 数据集数组
 * @param options 选项
 * @returns 图表配置
 */
export function createRadarChart(
  labels: string[],
  datasets: ChartDataset[],
  options: ChartOptions = {}
): ChartConfig {
  return {
    type: 'radar',
    data: {
      labels,
      datasets: datasets.map((dataset, index) => ({
        ...dataset,
        borderColor: dataset.borderColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        backgroundColor: dataset.backgroundColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length] + '20',
        pointBackgroundColor: dataset.borderColor || defaultColorTheme.primary[index % defaultColorTheme.primary.length],
        pointBorderColor: '#ffffff',
        pointRadius: dataset.pointRadius || 3
      }))
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      ...options
    }
  }
}

/**
 * 数据处理工具
 */
export class ChartDataProcessor {
  /**
   * 数据分组
   * @param data 原始数据
   * @param groupBy 分组字段
   * @returns 分组后的数据
   */
  static groupBy<T>(data: T[], groupBy: keyof T): Record<string, T[]> {
    return data.reduce((groups, item) => {
      const key = String(item[groupBy])
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(item)
      return groups
    }, {} as Record<string, T[]>)
  }
  
  /**
   * 数据聚合
   * @param data 数据数组
   * @param aggregateBy 聚合字段
   * @param aggregateFunc 聚合函数
   * @returns 聚合结果
   */
  static aggregate<T>(
    data: T[],
    aggregateBy: keyof T,
    aggregateFunc: 'sum' | 'avg' | 'count' | 'min' | 'max' = 'sum'
  ): number {
    const values = data.map(item => Number(item[aggregateBy])).filter(val => !isNaN(val))
    
    switch (aggregateFunc) {
      case 'sum':
        return values.reduce((sum, val) => sum + val, 0)
      case 'avg':
        return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0
      case 'count':
        return values.length
      case 'min':
        return values.length > 0 ? Math.min(...values) : 0
      case 'max':
        return values.length > 0 ? Math.max(...values) : 0
      default:
        return 0
    }
  }
  
  /**
   * 数据排序
   * @param data 数据数组
   * @param sortBy 排序字段
   * @param order 排序顺序
   * @returns 排序后的数据
   */
  static sort<T>(
    data: T[],
    sortBy: keyof T,
    order: 'asc' | 'desc' = 'asc'
  ): T[] {
    return [...data].sort((a, b) => {
      const aVal = a[sortBy]
      const bVal = b[sortBy]
      
      if (aVal < bVal) return order === 'asc' ? -1 : 1
      if (aVal > bVal) return order === 'asc' ? 1 : -1
      return 0
    })
  }
  
  /**
   * 数据过滤
   * @param data 数据数组
   * @param filterFunc 过滤函数
   * @returns 过滤后的数据
   */
  static filter<T>(data: T[], filterFunc: (item: T) => boolean): T[] {
    return data.filter(filterFunc)
  }
  
  /**
   * 数据转换为图表格式
   * @param data 原始数据
   * @param labelField 标签字段
   * @param valueField 值字段
   * @returns 图表数据
   */
  static toChartData<T>(
    data: T[],
    labelField: keyof T,
    valueField: keyof T
  ): { labels: string[]; data: number[] } {
    const labels = data.map(item => String(item[labelField]))
    const values = data.map(item => Number(item[valueField]))
    
    return {
      labels,
      data: values
    }
  }
  
  /**
   * 时间序列数据处理
   * @param data 时间序列数据
   * @param dateField 日期字段
   * @param valueField 值字段
   * @param interval 时间间隔
   * @returns 处理后的数据
   */
  static processTimeSeries<T>(
    data: T[],
    dateField: keyof T,
    valueField: keyof T,
    interval: 'day' | 'week' | 'month' | 'year' = 'day'
  ): { labels: string[]; data: number[] } {
    // 按时间间隔分组数据
    const grouped = this.groupBy(data, dateField)
    const sortedKeys = Object.keys(grouped).sort()
    
    const labels: string[] = []
    const values: number[] = []
    
    sortedKeys.forEach(key => {
      labels.push(key)
      const groupData = grouped[key]
      const aggregatedValue = this.aggregate(groupData, valueField, 'sum')
      values.push(aggregatedValue)
    })
    
    return {
      labels,
      data: values
    }
  }
}

/**
 * 图表工具类
 */
export class ChartUtils {
  /**
   * 导出图表为图片
   * @param canvas Canvas元素
   * @param filename 文件名
   * @param format 图片格式
   */
  static exportAsImage(
    canvas: HTMLCanvasElement,
    filename = 'chart',
    format: 'png' | 'jpeg' | 'webp' = 'png'
  ): void {
    const link = document.createElement('a')
    link.download = `${filename}.${format}`
    link.href = canvas.toDataURL(`image/${format}`)
    link.click()
  }
  
  /**
   * 获取图表数据URL
   * @param canvas Canvas元素
   * @param format 图片格式
   * @returns 数据URL
   */
  static getDataURL(
    canvas: HTMLCanvasElement,
    format: 'png' | 'jpeg' | 'webp' = 'png'
  ): string {
    return canvas.toDataURL(`image/${format}`)
  }
  
  /**
   * 计算图表尺寸
   * @param container 容器元素
   * @param aspectRatio 宽高比
   * @returns 尺寸对象
   */
  static calculateSize(
    container: HTMLElement,
    aspectRatio = 2
  ): { width: number; height: number } {
    const containerWidth = container.clientWidth
    const width = containerWidth
    const height = containerWidth / aspectRatio
    
    return { width, height }
  }
  
  /**
   * 格式化数值
   * @param value 数值
   * @param type 格式类型
   * @returns 格式化后的字符串
   */
  static formatValue(
    value: number,
    type: 'number' | 'currency' | 'percentage' = 'number'
  ): string {
    switch (type) {
      case 'currency':
        return new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY'
        }).format(value)
      case 'percentage':
        return new Intl.NumberFormat('zh-CN', {
          style: 'percent',
          minimumFractionDigits: 1
        }).format(value / 100)
      default:
        return new Intl.NumberFormat('zh-CN').format(value)
    }
  }
  
  /**
   * 生成图表主题
   * @param primaryColor 主色调
   * @returns 主题配置
   */
  static generateTheme(primaryColor: string): Partial<ChartOptions> {
    const colors = generateColorPalette(primaryColor, 8)
    
    return {
      plugins: {
        legend: {
          labels: {
            color: '#666666'
          }
        }
      },
      scales: {
        x: {
          grid: {
            color: '#e0e0e0'
          },
          ticks: {
            fontColor: '#666666'
          }
        },
        y: {
          grid: {
            color: '#e0e0e0'
          },
          ticks: {
            fontColor: '#666666'
          }
        }
      }
    }
  }
}

// 导出默认实例
export const chartDataProcessor = ChartDataProcessor
export const chartUtils = ChartUtils

// 导出快捷方法
export {
  createLineChart as line,
  createBarChart as bar,
  createPieChart as pie,
  createDoughnutChart as doughnut,
  createAreaChart as area,
  createScatterChart as scatter,
  createRadarChart as radar,
  generateColorPalette as colors,
  generateGradient as gradient
}