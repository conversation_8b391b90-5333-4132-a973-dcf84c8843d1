<template>
  <el-form :model="forgetForm" :rules="rules" ref="forgetFormRef" class="forget-form">
    <el-form-item prop="email">
      <el-input
        v-model="forgetForm.email"
        placeholder="请输入注册邮箱"
        :prefix-icon="Message"
        @focus="activeInput = 'email'"
        @blur="activeInput = ''"
        :class="{ 'is-active': activeInput === 'email' }"
      />
    </el-form-item>
    <div class="captcha-container">
      <el-form-item prop="captcha">
        <el-input
          v-model="forgetForm.captcha"
          placeholder="请输入验证码"
          :prefix-icon="Key"
          @focus="activeInput = 'captcha'"
          @blur="activeInput = ''"
          :class="{ 'is-active': activeInput === 'captcha' }"
        />
      </el-form-item>
      <div class="captcha-image" @click="refreshCaptcha">
        <img :src="captchaUrl" alt="验证码" />
      </div>
    </div>
    <el-button type="primary" class="submit-button" @click="handleSubmit" :loading="loading">
      发送重置邮件
    </el-button>
    <div class="login-link">
      想起密码了？<el-link type="primary" underline="never" @click="$emit('switch-component', 'login')">返回登录</el-link>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message, Key } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const emit = defineEmits<{
  (e: 'switch-component', component: string): void
}>()

const forgetFormRef = ref()
const loading = ref(false)
const activeInput = ref('')
const captchaUrl = ref('/api/captcha')

const forgetForm = reactive({
  email: '',
  captcha: ''
})

const rules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 4, message: '验证码长度为4位', trigger: 'blur' }
  ]
}

const refreshCaptcha = () => {
  captchaUrl.value = `/api/captcha?t=${new Date().getTime()}`
}

const handleSubmit = async () => {
  if (!forgetFormRef.value) return
  
  await forgetFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        // TODO: 调用忘记密码API
        // const res = await api.forgotPassword(forgetForm)
        ElMessage.success('重置邮件已发送，请查收')
        emit('switch-component', 'login')
      } catch (error: any) {
        ElMessage.error(error.message || '发送失败')
        refreshCaptcha()
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.forget-form {
  :deep(.el-input) {
    .el-input__wrapper {
      background: #f5f7fa;
      border-radius: 8px;
      padding: 8px 15px;
      box-shadow: none;
      transition: all 0.3s ease;

      &.is-focus,
      &:hover {
        background: #fff;
        box-shadow: 0 0 0 1px var(--el-color-primary) !important;
      }

      .el-input__inner {
        height: 36px;
        font-size: 14px;
      }
    }
  }

  .captcha-container {
    display: flex;
    gap: 12px;

    .el-form-item {
      flex: 1;
      margin-bottom: 0;
    }

    .captcha-image {
      width: 100px;
      height: 36px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .submit-button {
    width: 100%;
    padding: 12px 20px;
    margin-top: 20px;
    font-size: 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border: none;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .login-link {
    text-align: center;
    margin-top: 16px;
  }
}

:deep([data-theme='dark']) {
  .forget-form {
    :deep(.el-input) {
      .el-input__wrapper {
        background: #2c2c2c;
        
        &.is-focus,
        &:hover {
          background: #363636;
        }
      }
    }
  }
}
</style>