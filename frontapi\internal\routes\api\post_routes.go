package api

import (
	"frontapi/internal/api/posts"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterPostRoutes 注册帖子管理相关路由
func RegisterPostRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 创建帖子控制器
	postController := posts.NewPostController(
		services.PostService,
		services.UserService,
		services.UserFollowsService,
		services.VideoService,
		services.UserWatchVideoHistoryService,
		services.UserVideoCollectionService,
		services.VideoLikeService,
	)

	// 帖子管理路由组
	postRoutes := apiGroup.Group("/community/posts")

	{ // 帖子管理接口
		postRoutes.Post("/getPostList", postController.GetPostList)
		postRoutes.Post("/getRecommendedCreatorUsers", postController.GetRecommendedCreatorUsers)
		postRoutes.Post("/getRecommendedFollowUsers", postController.GetRecommendedFollowUsers)
		postRoutes.Post("/getPostFollowingUsers", postController.GetPostFollowingUsers)

		postRoutes.Post("/getTopHotUserList", postController.GetTopHotUserList)
		postRoutes.Post("/getPostDetail", postController.GetPostDetail)
		postRoutes.Post("/getRecommendedVideos", postController.GetRecommendedVideos)
		postRoutes.Post("/getRecommendedPostList", postController.GetRecommendedPostList)
		postRoutes.Post("/getRecommendedUsers", postController.GetRecommendedUsers)

	}

	// 评论管理控制器
	commentController := posts.NewPostCommentController(services.PostCommentService, services.UserService)
	{
		// 评论管理路由组
		commentRoutes := apiGroup.Group("/community/post/comments")
		commentRoutes.Post("/getPostCommentList", commentController.GetPostCommentList)
		commentRoutes.Post("/getCommentDetail", commentController.GetCommentDetail)
		commentRoutes.Post("/addComment", commentController.AddComment)
		commentRoutes.Post("/deleteComment", commentController.DeleteComment)

	}

}
