/**
 * 懒加载指令
 * 用于图片、视频等媒体资源的懒加载
 */

import type { Directive, DirectiveBinding } from 'vue'

interface LazyOptions {
  loading?: string // 加载中的占位图
  error?: string   // 加载失败的占位图
  threshold?: number // 触发加载的阈值
  rootMargin?: string // 根边距
  once?: boolean   // 是否只加载一次
  background?: boolean // 是否作为背景图片
}

interface LazyElement extends HTMLElement {
  _lazy?: {
    observer: IntersectionObserver
    options: LazyOptions
    loaded: boolean
    loading: boolean
    error: boolean
  }
}

// 默认配置
const defaultOptions: LazyOptions = {
  loading: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+',
  error: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjVmNWY1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkVycm9yPC90ZXh0Pjwvc3ZnPg==',
  threshold: 0.1,
  rootMargin: '50px',
  once: true,
  background: false
}

// 创建 IntersectionObserver
function createObserver(options: LazyOptions): IntersectionObserver {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const el = entry.target as LazyElement
          loadResource(el)
        }
      })
    },
    {
      threshold: options.threshold,
      rootMargin: options.rootMargin
    }
  )
}

// 加载资源
function loadResource(el: LazyElement): void {
  if (!el._lazy || el._lazy.loading || el._lazy.loaded) {
    return
  }

  const { options } = el._lazy
  const src = el.dataset.src

  if (!src) {
    console.warn('懒加载指令: 未找到 data-src 属性')
    return
  }

  el._lazy.loading = true

  // 触发加载开始事件
  el.dispatchEvent(new CustomEvent('lazy:loading', { detail: { src } }))

  if (el.tagName === 'IMG') {
    loadImage(el as HTMLImageElement, src, options)
  } else if (el.tagName === 'VIDEO') {
    loadVideo(el as HTMLVideoElement, src, options)
  } else if (options.background) {
    loadBackgroundImage(el, src, options)
  } else {
    console.warn('懒加载指令: 不支持的元素类型')
  }
}

// 加载图片
function loadImage(img: HTMLImageElement, src: string, options: LazyOptions): void {
  const image = new Image()

  image.onload = () => {
    img.src = src
    img.classList.add('lazy-loaded')
    img.classList.remove('lazy-loading', 'lazy-error')
    
    if (img._lazy) {
      img._lazy.loaded = true
      img._lazy.loading = false
      img._lazy.error = false

      // 如果只加载一次，移除观察者
      if (options.once) {
        img._lazy.observer.unobserve(img)
      }
    }

    // 触发加载完成事件
    img.dispatchEvent(new CustomEvent('lazy:loaded', { detail: { src } }))
  }

  image.onerror = () => {
    if (options.error) {
      img.src = options.error
    }
    img.classList.add('lazy-error')
    img.classList.remove('lazy-loading')
    
    if (img._lazy) {
      img._lazy.loading = false
      img._lazy.error = true
    }

    // 触发加载错误事件
    img.dispatchEvent(new CustomEvent('lazy:error', { detail: { src } }))
  }

  // 设置加载中状态
  if (options.loading && !img.src) {
    img.src = options.loading
  }
  img.classList.add('lazy-loading')

  // 开始加载
  image.src = src
}

// 加载视频
function loadVideo(video: HTMLVideoElement, src: string, options: LazyOptions): void {
  video.addEventListener('loadeddata', () => {
    video.classList.add('lazy-loaded')
    video.classList.remove('lazy-loading', 'lazy-error')
    
    if (video._lazy) {
      video._lazy.loaded = true
      video._lazy.loading = false
      video._lazy.error = false

      if (options.once) {
        video._lazy.observer.unobserve(video)
      }
    }

    video.dispatchEvent(new CustomEvent('lazy:loaded', { detail: { src } }))
  }, { once: true })

  video.addEventListener('error', () => {
    video.classList.add('lazy-error')
    video.classList.remove('lazy-loading')
    
    if (video._lazy) {
      video._lazy.loading = false
      video._lazy.error = true
    }

    video.dispatchEvent(new CustomEvent('lazy:error', { detail: { src } }))
  }, { once: true })

  video.classList.add('lazy-loading')
  video.src = src
}

// 加载背景图片
function loadBackgroundImage(el: HTMLElement, src: string, options: LazyOptions): void {
  const image = new Image()

  image.onload = () => {
    el.style.backgroundImage = `url(${src})`
    el.classList.add('lazy-loaded')
    el.classList.remove('lazy-loading', 'lazy-error')
    
    if (el._lazy) {
      el._lazy.loaded = true
      el._lazy.loading = false
      el._lazy.error = false

      if (options.once) {
        el._lazy.observer.unobserve(el)
      }
    }

    el.dispatchEvent(new CustomEvent('lazy:loaded', { detail: { src } }))
  }

  image.onerror = () => {
    if (options.error) {
      el.style.backgroundImage = `url(${options.error})`
    }
    el.classList.add('lazy-error')
    el.classList.remove('lazy-loading')
    
    if (el._lazy) {
      el._lazy.loading = false
      el._lazy.error = true
    }

    el.dispatchEvent(new CustomEvent('lazy:error', { detail: { src } }))
  }

  // 设置加载中状态
  if (options.loading) {
    el.style.backgroundImage = `url(${options.loading})`
  }
  el.classList.add('lazy-loading')

  // 开始加载
  image.src = src
}

// 懒加载指令
export const vLazy: Directive<LazyElement, string | LazyOptions> = {
  mounted(el: LazyElement, binding: DirectiveBinding<string | LazyOptions>) {
    // 检查浏览器支持
    if (!('IntersectionObserver' in window)) {
      console.warn('懒加载指令: 浏览器不支持 IntersectionObserver，将立即加载资源')
      loadResource(el)
      return
    }

    // 解析配置
    let options: LazyOptions
    if (typeof binding.value === 'string') {
      options = { ...defaultOptions }
      el.dataset.src = binding.value
    } else {
      options = { ...defaultOptions, ...binding.value }
    }

    // 如果没有设置 data-src，尝试从 binding.arg 获取
    if (!el.dataset.src && binding.arg) {
      el.dataset.src = binding.arg
    }

    // 创建观察者
    const observer = createObserver(options)

    // 保存配置到元素
    el._lazy = {
      observer,
      options,
      loaded: false,
      loading: false,
      error: false
    }

    // 开始观察
    observer.observe(el)

    // 添加初始样式类
    el.classList.add('lazy-element')
  },

  updated(el: LazyElement, binding: DirectiveBinding<string | LazyOptions>) {
    if (!el._lazy) return

    // 更新 data-src
    if (typeof binding.value === 'string') {
      el.dataset.src = binding.value
    }

    // 如果已经加载过且配置为只加载一次，不重新加载
    if (el._lazy.loaded && el._lazy.options.once) {
      return
    }

    // 重置状态
    el._lazy.loaded = false
    el._lazy.loading = false
    el._lazy.error = false
    el.classList.remove('lazy-loaded', 'lazy-loading', 'lazy-error')

    // 重新开始观察
    el._lazy.observer.observe(el)
  },

  unmounted(el: LazyElement) {
    if (el._lazy) {
      el._lazy.observer.unobserve(el)
      el._lazy.observer.disconnect()
      delete el._lazy
    }
  }
}

// 导出类型
export type { LazyOptions }

// 导出工具函数
export const lazyUtils = {
  // 手动触发加载
  load(el: LazyElement): void {
    loadResource(el)
  },

  // 检查是否已加载
  isLoaded(el: LazyElement): boolean {
    return el._lazy?.loaded || false
  },

  // 检查是否正在加载
  isLoading(el: LazyElement): boolean {
    return el._lazy?.loading || false
  },

  // 检查是否加载错误
  hasError(el: LazyElement): boolean {
    return el._lazy?.error || false
  },

  // 重新加载
  reload(el: LazyElement): void {
    if (el._lazy) {
      el._lazy.loaded = false
      el._lazy.loading = false
      el._lazy.error = false
      el.classList.remove('lazy-loaded', 'lazy-loading', 'lazy-error')
      loadResource(el)
    }
  }
}