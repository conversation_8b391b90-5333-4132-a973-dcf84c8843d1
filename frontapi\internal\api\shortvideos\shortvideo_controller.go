package shortvideos

import (
	"frontapi/internal/api"
	shortvideoModels "frontapi/internal/models/shortvideos"
	"frontapi/internal/service/shortvideos"
	shortTypings "frontapi/internal/typings/shortvideos"

	"github.com/gofiber/fiber/v2"
)

// ShortVideoController 短视频控制器
type ShortVideoController struct {
	ShortVideoService         shortvideos.ShortVideoService
	ShortVideoCategoryService shortvideos.ShortVideoCategoryService
	ShortVideoCommentService  shortvideos.ShortVideoCommentService
	api.BaseController        // 继承BaseController
}

// NewShortVideoController 创建短视频控制器实例
func NewShortVideoController(
	shortVideoService shortvideos.ShortVideoService,
	shortVideoCategoryService shortvideos.ShortVideoCategoryService,
	shortVideoCommentService shortvideos.ShortVideoCommentService,
) *ShortVideoController {
	return &ShortVideoController{
		ShortVideoService:         shortVideoService,
		ShortVideoCategoryService: shortVideoCategoryService,
		ShortVideoCommentService:  shortVideoCommentService,
	}
}

// GetShortVideoList 获取短视频列表
func (c *ShortVideoController) GetShortVideoList(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	keyword := reqInfo.Get("keyword").GetString()
	categoryId := reqInfo.Get("category_id").GetString()
	status := reqInfo.Get("status").GetInt()
	if status == 0 {
		status = 1 // 默认只获取正常状态的短视频
	}

	// 查询短视频列表
	shortVideoList, total, err := c.ShortVideoService.List(ctx.Context(), map[string]interface{}{
		"keyword":     keyword,
		"category_id": categoryId,
		"status":      status,
	}, "created_at DESC", page, pageSize, true)

	if err != nil {
		return c.InternalServerError(ctx, "获取短视频列表失败: "+err.Error())
	}
	//查看用户是否点了like
	userID := c.GetUserID(ctx)
	if userID != "" {
		for _, video := range shortVideoList {
			video.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "检查用户点赞状态失败: "+err.Error())
			}

		}
	}
	shortResponseList := shortTypings.ConvertShortVideoListResponse(shortVideoList, total, page, pageSize)

	// 返回短视频列表
	return c.Success(ctx, shortResponseList)
}

// SearchShortVideos 高级搜索短视频
func (c *ShortVideoController) SearchShortVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 基本搜索参数
	keyword := reqInfo.Get("keyword").GetString()
	categoryId := reqInfo.Get("category_id").GetString()
	creatorId := reqInfo.Get("creator_id").GetString()
	creatorName := reqInfo.Get("creator_name").GetString()

	// 高级过滤参数
	durationMin := reqInfo.Get("duration_min").GetString()
	durationMax := reqInfo.Get("duration_max").GetString()
	viewCountMin := reqInfo.Get("view_count_min").GetString()
	viewCountMax := reqInfo.Get("view_count_max").GetString()
	likeCountMin := reqInfo.Get("like_count_min").GetString()
	likeCountMax := reqInfo.Get("like_count_max").GetString()
	uploadTimeStart := reqInfo.Get("upload_time_start").GetString()
	uploadTimeEnd := reqInfo.Get("upload_time_end").GetString()
	isFeatured := reqInfo.Get("is_featured").GetString()
	isPaid := reqInfo.Get("is_paid").GetString()
	priceMin := reqInfo.Get("price_min").GetString()
	priceMax := reqInfo.Get("price_max").GetString()

	// 排序参数
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 构建查询条件
	condition := map[string]interface{}{
		"keyword":           keyword,
		"category_id":       categoryId,
		"creator_id":        creatorId,
		"creator_name":      creatorName,
		"duration_min":      durationMin,
		"duration_max":      durationMax,
		"view_count_min":    viewCountMin,
		"view_count_max":    viewCountMax,
		"like_count_min":    likeCountMin,
		"like_count_max":    likeCountMax,
		"upload_time_start": uploadTimeStart,
		"upload_time_end":   uploadTimeEnd,
		"is_featured":       isFeatured,
		"is_paid":           isPaid,
		"price_min":         priceMin,
		"price_max":         priceMax,
		"status":            1, // 默认只获取正常状态的短视频
	}

	// 查询短视频列表
	shortVideoList, total, err := c.ShortVideoService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "搜索短视频失败: "+err.Error())
	}

	// 查看用户是否点了like
	userID := c.GetUserID(ctx)
	if userID != "" {
		for _, video := range shortVideoList {
			video.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "检查用户点赞状态失败: "+err.Error())
			}
		}
	}

	// 转换为响应格式
	shortResponseList := shortTypings.ConvertShortVideoListResponse(shortVideoList, total, page, pageSize)

	// 返回搜索结果
	return c.Success(ctx, shortResponseList)
}

// GetShortVideoDetail 获取短视频详情
func (c *ShortVideoController) GetShortVideoDetail(ctx *fiber.Ctx) error {
	// 获取短视频ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}
	userID := c.GetUserID(ctx)

	// 查询短视频
	shortVideo, err := c.ShortVideoService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取短视频详情失败: "+err.Error())
	}

	if shortVideo == nil {
		return c.NotFound(ctx, "短视频不存在")
	}

	shortVideo.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, shortVideo.ID)
	if err != nil {
		// 不报错，设为默认值
		shortVideo.IsLiked = false
	}

	// 返回短视频详情
	return c.Success(ctx, shortVideo)
}

// ViewShortVideo 观看短视频（增加观看次数）
func (c *ShortVideoController) ViewShortVideo(ctx *fiber.Ctx) error {
	// 获取短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	id := reqInfo.Get("id").GetString()

	if id == "" {
		return c.BadRequest(ctx, "短视频ID不能为空", nil)
	}

	// 暂时注释掉观看次数功能，因为服务中没有这个方法
	// err := c.ShortVideoService.ViewShortVideo(ctx.Context(), id)
	err := error(nil)
	if err != nil {
		return c.InternalServerError(ctx, "更新观看次数失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "观看记录成功")
}

// LikeShortVideo 点赞短视频
func (c *ShortVideoController) LikeShortVideo(ctx *fiber.Ctx) error {
	// 获取用户ID和短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	shortID := reqInfo.Get("short_id").GetString()

	if userID == "" || shortID == "" {
		return c.BadRequest(ctx, "用户ID和短视频ID不能为空", nil)
	}

	// 点赞短视频
	err := c.ShortVideoService.LikeShortVideo(ctx.Context(), userID, shortID)
	if err != nil {
		return c.InternalServerError(ctx, "点赞失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "点赞成功")
}

// CancelLikeShortVideo 取消点赞短视频
func (c *ShortVideoController) CancelLikeShortVideo(ctx *fiber.Ctx) error {
	// 获取用户ID和短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	shortID := reqInfo.Get("short_id").GetString()

	if userID == "" || shortID == "" {
		return c.BadRequest(ctx, "用户ID和短视频ID不能为空", nil)
	}

	// 取消点赞
	err := c.ShortVideoService.UnlikeShortVideo(ctx.Context(), userID, shortID)
	if err != nil {
		return c.InternalServerError(ctx, "取消点赞失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "取消点赞成功")
}

// CheckUserLiked 检查用户是否点赞过短视频
func (c *ShortVideoController) CheckUserLiked(ctx *fiber.Ctx) error {
	// 获取用户ID和短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	shortID := reqInfo.Get("short_id").GetString()

	if userID == "" || shortID == "" {
		return c.BadRequest(ctx, "用户ID和短视频ID不能为空", nil)
	}

	// 检查是否点赞
	isLiked, err := c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, shortID)
	if err != nil {
		return c.InternalServerError(ctx, "检查点赞状态失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"is_liked": isLiked,
	})
}

// GetTrendingShortVideos 获取热门/推荐短视频
func (c *ShortVideoController) GetTrendingShortVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 默认获取推荐的视频
	condition := map[string]interface{}{
		"is_featured": "1", // 获取推荐的视频
		"status":      1,   // 正常状态
	}

	// 设置排序方式，默认按热度（点赞数+评论数）排序
	sortType := reqInfo.Get("sort_type").GetString()
	var orderBy string

	switch sortType {
	case "newest":
		orderBy = "created_at DESC"
	case "most_liked":
		orderBy = "like_count DESC"
	case "most_viewed":
		orderBy = "view_count DESC"
	case "most_commented":
		orderBy = "comment_count DESC"
	default:
		// 默认按热度排序（可以自定义热度算法）
		orderBy = "like_count DESC, comment_count DESC, view_count DESC"
	}

	// 查询短视频列表
	shortVideoList, total, err := c.ShortVideoService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取热门短视频失败: "+err.Error())
	}

	// 查看用户是否点赞
	userID := c.GetUserID(ctx)
	if userID != "" {
		for _, video := range shortVideoList {
			video.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "检查用户点赞状态失败: "+err.Error())
			}
		}
	}

	// 转换为响应格式
	shortResponseList := shortTypings.ConvertShortVideoListResponse(shortVideoList, total, page, pageSize)

	// 返回热门短视频列表
	return c.Success(ctx, shortResponseList)
}

// GetRelatedShortVideos 获取相关短视频推荐
func (c *ShortVideoController) GetRelatedShortVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 获取当前视频ID和分类ID
	shortID := reqInfo.Get("short_id").GetString()
	if shortID == "" {
		return c.BadRequest(ctx, "短视频ID不能为空", nil)
	}

	// 获取当前视频信息，用于获取分类和标签信息
	currentVideo, err := c.ShortVideoService.GetByID(ctx.Context(), shortID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取短视频信息失败: "+err.Error())
	}

	if currentVideo == nil {
		return c.NotFound(ctx, "短视频不存在")
	}

	// 构建查询条件
	condition := map[string]interface{}{
		"status": 1, // 正常状态
	}

	// 使用同样分类的视频
	if currentVideo.CategoryID.Valid {
		condition["category_id"] = currentVideo.CategoryID.String
	}

	// 排除当前视频
	orderBy := "RAND()" // 随机排序，增加推荐多样性

	// 查询相关短视频列表
	relatedVideos, total, err := c.ShortVideoService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取相关短视频失败: "+err.Error())
	}

	// 过滤掉当前视频
	filteredVideos := make([]*shortvideoModels.ShortVideo, 0)
	for _, video := range relatedVideos {
		if video.ID != shortID {
			filteredVideos = append(filteredVideos, video)
		}
	}

	// 如果过滤后的结果不足，补充一些随机推荐
	if len(filteredVideos) < pageSize/2 {
		// 获取一些随机推荐的视频
		randomCondition := map[string]interface{}{
			"status":      1,
			"is_featured": "1", // 获取推荐的视频
		}
		randomVideos, _, err := c.ShortVideoService.List(ctx.Context(), randomCondition, "RAND()", 1, pageSize-len(filteredVideos), true)
		if err == nil && len(randomVideos) > 0 {
			// 添加不重复的随机视频
			for _, video := range randomVideos {
				if video.ID != shortID {
					isDuplicate := false
					for _, existing := range filteredVideos {
						if existing.ID == video.ID {
							isDuplicate = true
							break
						}
					}
					if !isDuplicate {
						filteredVideos = append(filteredVideos, video)
					}
				}
			}
		}
	}

	// 查看用户是否点赞
	userID := c.GetUserID(ctx)
	if userID != "" {
		for _, video := range filteredVideos {
			video.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
			if err != nil {
				// 不报错，设为默认值
				video.IsLiked = false
			}
		}
	}

	// 转换为响应格式
	shortResponseList := shortTypings.ConvertShortVideoListResponse(filteredVideos, total, page, pageSize)

	// 返回相关短视频列表
	return c.Success(ctx, shortResponseList)
}

// CollectShortVideo 收藏短视频
func (c *ShortVideoController) CollectShortVideo(ctx *fiber.Ctx) error {
	// 获取用户ID和短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	shortID := reqInfo.Get("short_id").GetString()

	if userID == "" || shortID == "" {
		return c.BadRequest(ctx, "用户ID和短视频ID不能为空", nil)
	}

	// 收藏短视频
	err := c.ShortVideoService.CollectShortVideo(ctx.Context(), userID, shortID)
	if err != nil {
		return c.InternalServerError(ctx, "收藏失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "收藏成功")
}

// CancelCollectShortVideo 取消收藏短视频
func (c *ShortVideoController) CancelCollectShortVideo(ctx *fiber.Ctx) error {
	// 获取用户ID和短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	shortID := reqInfo.Get("short_id").GetString()

	if userID == "" || shortID == "" {
		return c.BadRequest(ctx, "用户ID和短视频ID不能为空", nil)
	}

	// 取消收藏
	err := c.ShortVideoService.UncollectShortVideo(ctx.Context(), userID, shortID)
	if err != nil {
		return c.InternalServerError(ctx, "取消收藏失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "取消收藏成功")
}

// CheckUserCollected 检查用户是否收藏过短视频
func (c *ShortVideoController) CheckUserCollected(ctx *fiber.Ctx) error {
	// 获取用户ID和短视频ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	shortID := reqInfo.Get("short_id").GetString()

	if userID == "" || shortID == "" {
		return c.BadRequest(ctx, "用户ID和短视频ID不能为空", nil)
	}

	// 检查是否收藏
	isCollected, err := c.ShortVideoService.CheckUserCollected(ctx.Context(), userID, shortID)
	if err != nil {
		return c.InternalServerError(ctx, "检查收藏状态失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"is_collected": isCollected,
	})
}

// GetUserCollections 获取用户收藏的短视频列表
func (c *ShortVideoController) GetUserCollections(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 获取用户ID
	userID := c.GetUserID(ctx)
	if userID == "" {
		return c.BadRequest(ctx, "用户未登录或ID无效", nil)
	}

	// 这里需要在服务层实现获取用户收藏的短视频列表方法
	// 目前使用基本的列表查询接口代替，实际使用时需要实现专门的方法
	condition := map[string]interface{}{
		"user_collected": userID, // 假设有这个条件字段，实际可能需要专门实现
		"status":         1,      // 正常状态
	}

	orderBy := "created_at DESC" // 默认按收藏时间排序

	// 查询用户收藏的短视频列表
	// 注意：这里实际应该调用专门的获取用户收藏列表方法，现在使用List代替
	shortVideoList, total, err := c.ShortVideoService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取收藏列表失败: "+err.Error())
	}

	// 设置收藏状态
	for _, video := range shortVideoList {
		video.IsCollected = true // 既然是收藏列表，肯定是已收藏的

		// 检查是否点赞
		video.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
		if err != nil {
			// 不报错，设为默认值
			video.IsLiked = false
		}
	}

	// 转换为响应格式
	shortResponseList := shortTypings.ConvertShortVideoListResponse(shortVideoList, total, page, pageSize)

	// 返回收藏的短视频列表
	return c.Success(ctx, shortResponseList)
}
