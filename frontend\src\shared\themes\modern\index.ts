/**
 * Modern 主题配置
 * 基于现代设计理念的主题
 */
import { ThemeConfig } from '../theme-manager';
import modernVariables from './variables';
import modernDarkVariables from './variables-dark';

// Modern 亮色主题
export const modernLightTheme: ThemeConfig = {
    name: 'modern-light',
    displayName: 'Modern Light',
    shortName: 'Modern Light',
    code: 'modern',
    primary: '#4A90E2',
    isDark: false,
    variables: modernVariables
};

// Modern 暗色主题
export const modernDarkTheme: ThemeConfig = {
    name: 'modern-dark',
    displayName: 'Modern Dark',
    shortName: 'Modern Dark',
    code: 'modern',
    primary: '#5BA0F2',
    isDark: true,
    variables: modernDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default modernLightTheme;