package vips

import (
	"frontapi/internal/models/vips"
	"frontapi/internal/service/base"
)

type CreateCoinPackageRequest struct {
	Name        string  `json:"name" validate:"required"`
	Code        string  `json:"code" validate:"required"`
	CoinAmount  int     `json:"coin_amount" validate:"required"`
	Price       float64 `json:"price" validate:"required"`
	BonusCoin   int     `json:"bonus_coin"`
	Description string  `json:"description"`
	Icon        string  `json:"icon"`
	SortOrder   int     `json:"sort_order"`
	Status      int8    `json:"status"`
}

type UpdateCoinPackageRequest struct {
	Name        string  `json:"name"`
	Code        string  `json:"code"`
	CoinAmount  int     `json:"coin_amount"`
	Price       float64 `json:"price"`
	BonusCoin   int     `json:"bonus_coin"`
	Description string  `json:"description"`
	Icon        string  `json:"icon"`
	SortOrder   int     `json:"sort_order"`
	Status      int8    `json:"status"`
}

type CoinPackageService interface {
	base.IExtendedService[vips.CoinPackage]
}
