<template>
  <el-dialog
    :model-value="visible"
    title="清空登录日志"
    width="500px"
    :before-close="handleClose"
    @update:model-value="updateVisible"
  >
    <div class="clear-dialog-content">
      <div class="warning-info">
        <el-alert
          title="注意"
          type="warning"
          show-icon
          :closable="false"
          class="mb-4"
        >
          <template #default>
            <p>此操作将永久删除登录日志，无法恢复，请谨慎操作！</p>
          </template>
        </el-alert>
      </div>

      <el-form :model="clearForm" label-width="100px" class="clear-form">
        <el-form-item label="清空范围">
          <el-radio-group v-model="clearForm.clearType">
            <el-radio label="all">清空所有日志</el-radio>
            <el-radio label="user">清空指定用户日志</el-radio>
            <el-radio label="date">清空指定时间段日志</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="clearForm.clearType === 'user'" 
          label="用户ID"
          :rules="[{ required: true, message: '请输入用户ID', trigger: 'blur' }]"
        >
          <el-input 
            v-model="clearForm.userId" 
            placeholder="请输入要清空日志的用户ID"
            clearable
          />
        </el-form-item>

        <el-form-item 
          v-if="clearForm.clearType === 'date'" 
          label="时间范围"
          :rules="[{ required: true, message: '请选择时间范围', trigger: 'change' }]"
        >
          <el-date-picker
            v-model="clearForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>

        <el-form-item label="确认密码">
          <el-input 
            v-model="clearForm.confirmPassword" 
            type="password" 
            placeholder="请输入管理员密码确认操作"
            show-password
            clearable
          />
          <div class="form-tip">
            <small>为了安全起见，请输入当前登录用户的密码进行确认</small>
          </div>
        </el-form-item>
      </el-form>

      <div class="statistics-info">
        <el-card shadow="never" class="stats-card">
          <template #header>
            <span class="stats-title">预计影响</span>
          </template>
          <div class="stats-content">
            <div class="stats-item">
              <span class="stats-label">预计删除记录数：</span>
              <span class="stats-value text-danger">{{ estimatedCount || '计算中...' }}</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">操作类型：</span>
              <span class="stats-value">
                <el-tag type="danger">永久删除</el-tag>
              </span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="danger" 
          :loading="loading"
          @click="handleConfirm"
        >
          确认清空
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { clearLoginLogs } from '@/service/api/users/users';
import { handleApiError } from '@/utils/errorHandler';
import { ElMessage } from 'element-plus';
import { reactive, ref, watch } from 'vue';

// Props类型定义
interface Props {
  visible: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
});

// Emits类型定义
interface Emits {
  'update:visible': [value: boolean];
  'success': [];
}

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const estimatedCount = ref(0);

// 清空表单
const clearForm = reactive({
  clearType: 'all', // all: 全部, user: 指定用户, date: 指定时间
  userId: '',
  dateRange: [] as string[],
  confirmPassword: ''
});

// 监听清空类型变化，重置相关字段
watch(() => clearForm.clearType, (newType) => {
  if (newType === 'all') {
    clearForm.userId = '';
    clearForm.dateRange = [];
  } else if (newType === 'user') {
    clearForm.dateRange = [];
  } else if (newType === 'date') {
    clearForm.userId = '';
  }
  // 重新计算预估数量
  calculateEstimatedCount();
});

// 监听用户ID和时间范围变化
watch([() => clearForm.userId, () => clearForm.dateRange], () => {
  calculateEstimatedCount();
}, { deep: true });

// 计算预估删除数量
const calculateEstimatedCount = () => {
  // 这里应该调用后端API获取预估数量
  // 暂时使用模拟数据
  if (clearForm.clearType === 'all') {
    estimatedCount.value = 10000; // 示例：所有记录
  } else if (clearForm.clearType === 'user' && clearForm.userId) {
    estimatedCount.value = 150; // 示例：指定用户记录
  } else if (clearForm.clearType === 'date' && clearForm.dateRange.length === 2) {
    estimatedCount.value = 500; // 示例：指定时间段记录
  } else {
    estimatedCount.value = 0;
  }
};

// 更新可见性
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 关闭对话框
const handleClose = () => {
  // 重置表单
  Object.assign(clearForm, {
    clearType: 'all',
    userId: '',
    dateRange: [],
    confirmPassword: ''
  });
  estimatedCount.value = 0;
  emit('update:visible', false);
};

// 确认清空
const handleConfirm = async () => {
  // 基本验证
  if (clearForm.clearType === 'user' && !clearForm.userId) {
    ElMessage.error('请输入用户ID');
    return;
  }
  
  if (clearForm.clearType === 'date' && clearForm.dateRange.length !== 2) {
    ElMessage.error('请选择时间范围');
    return;
  }

  if (!clearForm.confirmPassword) {
    ElMessage.error('请输入确认密码');
    return;
  }

  loading.value = true;
  try {
    let params: any = {
      confirmPassword: clearForm.confirmPassword
    };

    if (clearForm.clearType === 'user') {
      params.userId = clearForm.userId;
    } else if (clearForm.clearType === 'date') {
      params.startDate = clearForm.dateRange[0];
      params.endDate = clearForm.dateRange[1];
    }

    const {response} = await clearLoginLogs(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('清空登录日志成功');
      emit('success');
      handleClose();
    } else {
      handleApiError(response.message || '清空登录日志失败');
    }
  } catch (error) {
    handleApiError(error, '清空登录日志失败');
  } finally {
    loading.value = false;
  }
};

// 初始化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    calculateEstimatedCount();
  }
});
</script>

<style scoped>
.clear-dialog-content {
  padding: 10px 0;
}

.clear-form {
  margin: 20px 0;
}

.form-tip {
  margin-top: 5px;
  color: #909399;
}

.statistics-info {
  margin-top: 20px;
}

.stats-card {
  border: 1px solid #f56c6c;
}

.stats-title {
  font-weight: 600;
  color: #f56c6c;
}

.stats-content {
  padding: 0;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stats-item:last-child {
  margin-bottom: 0;
}

.stats-label {
  color: #606266;
  font-size: 14px;
}

.stats-value {
  font-size: 14px;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-item {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
  }
  
  .stats-label {
    margin-bottom: 4px;
  }
}
</style> 