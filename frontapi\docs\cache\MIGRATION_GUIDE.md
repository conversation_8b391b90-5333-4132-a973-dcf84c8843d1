# 缓存系统迁移指南：从 v1 到 v2

本指南帮助您将应用程序从缓存系统 v1 迁移到 v2 版本。v2 版本提供了更简洁、更强大的 API，同时保持向后兼容性。

## 主要改进

v2 版本相比 v1 版本有以下主要改进：

1. **简化的 API**：更直观、更一致的 API 设计
2. **完整的泛型支持**：改进的类型安全适配器
3. **多级缓存支持**：更灵活的缓存策略配置
4. **更多适配器**：增加了 Memcached 和 BigCache 适配器
5. **统一的错误处理**：标准化的错误类型和消息
6. **增强的配置选项**：更灵活的配置方式

## 快速迁移

### 导入路径变更

将导入路径从 `frontapi/pkg/cache` 更改为 `frontapi/pkg/cache/v2`：

```go
// 旧版本
import "frontapi/pkg/cache"

// 新版本
import cache "frontapi/pkg/cache/v2"
```

### 基本用法对比

#### 创建缓存实例

**v1 版本**：
```go
// Redis 缓存
redisConfig := &cache.RedisConfig{
    Host:     "localhost",
    Port:     6379,
    Password: "",
    DB:       0,
}
redisAdapter, err := cache.NewRedisAdapter(redisConfig)
if err != nil {
    log.Fatal(err)
}
```

**v2 版本**：
```go
// Redis 缓存
redisCache, err := cache.NewRedis("localhost", 6379, "", 0)
if err != nil {
    log.Fatal(err)
}
```

#### 设置和获取缓存

**v1 版本**：
```go
// 设置缓存
ctx := context.Background()
value := []byte("value")
err = redisAdapter.Set(ctx, "key", value, 10*time.Minute)

// 获取缓存
data, err := redisAdapter.Get(ctx, "key")
```

**v2 版本**：
```go
// 设置缓存
err = redisCache.Set("key", "value", 10*time.Minute)

// 获取缓存
value, err := redisCache.Get("key")
```

### 类型安全缓存

**v1 版本**：
```go
type User struct {
    ID   int    `json:"id"`
    Name string `json:"name"`
}

// 设置缓存
user := User{ID: 1, Name: "张三"}
data, err := json.Marshal(user)
if err != nil {
    log.Fatal(err)
}
err = redisAdapter.Set(ctx, "user:1", data, 10*time.Minute)

// 获取缓存
data, err = redisAdapter.Get(ctx, "user:1")
if err != nil {
    log.Fatal(err)
}
var user User
err = json.Unmarshal(data, &user)
if err != nil {
    log.Fatal(err)
}
```

**v2 版本**：
```go
type User struct {
    ID   int    `json:"id"`
    Name string `json:"name"`
}

// 创建类型安全的缓存适配器
userCache, err := cache.GetTyped[User](redisCache)
if err != nil {
    log.Fatal(err)
}

// 设置缓存
user := User{ID: 1, Name: "张三"}
err = userCache.Set("user:1", user, 10*time.Minute)

// 获取缓存
user, err = userCache.Get("user:1")
```

### 多级缓存

**v1 版本**：
```go
// 创建 Redis 适配器
redisAdapter, err := cache.NewRedisAdapter(redisConfig)
if err != nil {
    log.Fatal(err)
}

// 创建文件适配器
fileAdapter, err := cache.NewFileAdapter(&cache.FileConfig{
    Path: "storage/cache",
})
if err != nil {
    log.Fatal(err)
}

// 手动管理多个适配器
// ...
```

**v2 版本**：
```go
// 创建多级缓存（内存 + Redis + 文件）
multiCache, err := cache.NewMultiLevel(
    "localhost", 6379, "", 0,
    "storage/cache",
)
if err != nil {
    log.Fatal(err)
}
```

## 详细迁移指南

### 1. 缓存配置

**v1 版本**：
```go
redisConfig := &cache.RedisConfig{
    Host:     "localhost",
    Port:     6379,
    Password: "",
    DB:       0,
}
```

**v2 版本**：
```go
// 方式一：使用工厂函数
redisCache, err := cache.NewRedis("localhost", 6379, "", 0)

// 方式二：使用配置对象
config := &cache.CacheConfig{
    Redis: &cache.RedisConfig{
        Host:     "localhost",
        Port:     6379,
        Password: "",
        DB:       0,
    },
}
cacheManager, err := cache.New(config)
```

### 2. 错误处理

**v1 版本**：
```go
data, err := redisAdapter.Get(ctx, "key")
if err != nil {
    if err == redis.Nil {
        // 键不存在
    } else {
        // 其他错误
    }
}
```

**v2 版本**：
```go
value, err := redisCache.Get("key")
if err != nil {
    if err == cache.ErrNotFound {
        // 键不存在
    } else {
        // 其他错误
    }
}
```

### 3. 批量操作

**v1 版本**：
```go
keys := []string{"key1", "key2", "key3"}
results, err := redisAdapter.MGet(ctx, keys)
```

**v2 版本**：
```go
// 使用批量适配器
batchAdapter, ok := redisCache.GetAdapter("redis").(cache.BatchCacheAdapter)
if ok {
    keys := []string{"key1", "key2", "key3"}
    results, err := batchAdapter.MGet(context.Background(), keys)
}

// 或者使用类型安全的批量操作
typedCache, err := cache.GetTyped[string](redisCache)
if err != nil {
    log.Fatal(err)
}
keys := []string{"key1", "key2", "key3"}
results, err := typedCache.GetMulti(keys)
```

### 4. 计数器操作

**v1 版本**：
```go
newValue, err := redisAdapter.Increment(ctx, "counter", 1)
```

**v2 版本**：
```go
// 使用计数器适配器
counterAdapter, ok := redisCache.GetAdapter("redis").(cache.CounterCacheAdapter)
if ok {
    newValue, err := counterAdapter.Increment(context.Background(), "counter", 1)
}
```

### 5. 从环境变量加载配置

**v1 版本**：
```go
// 手动从环境变量读取配置
host := os.Getenv("REDIS_HOST")
port, _ := strconv.Atoi(os.Getenv("REDIS_PORT"))
// ...
```

**v2 版本**：
```go
// 自动从环境变量加载配置
config, err := cache.LoadConfigFromEnv("APP_")
if err != nil {
    log.Fatal(err)
}
cacheManager, err := cache.New(config)
```

## 新特性使用指南

### 1. 多适配器管理

v2 版本支持注册和获取多个适配器：

```go
// 创建缓存管理器
cacheManager, err := cache.NewRedis("localhost", 6379, "", 0)
if err != nil {
    log.Fatal(err)
}

// 注册自定义适配器
customAdapter := &MyCustomAdapter{}
cacheManager.RegisterAdapter("custom", customAdapter)

// 获取适配器
redisAdapter, ok := cacheManager.GetAdapter("redis")
if ok {
    // 使用 Redis 适配器
}

customAdapter, ok := cacheManager.GetAdapter("custom")
if ok {
    // 使用自定义适配器
}
```

### 2. 使用 Memcached 适配器

v2 版本新增了 Memcached 适配器：

```go
// 创建 Memcached 缓存
memcachedCache, err := cache.NewMemcached([]string{"localhost:11211"})
if err != nil {
    log.Fatal(err)
}

// 使用 Memcached 缓存
err = memcachedCache.Set("key", "value", 10*time.Minute)
value, err := memcachedCache.Get("key")
```

### 3. 使用 BigCache 适配器

v2 版本新增了 BigCache 适配器：

```go
// 创建 BigCache 缓存
bigcacheCache, err := cache.NewBigCache(10 * time.Minute)
if err != nil {
    log.Fatal(err)
}

// 使用 BigCache 缓存
err = bigcacheCache.Set("key", "value", 10*time.Minute)
value, err := bigcacheCache.Get("key")
```

### 4. GetOrSet 模式

v2 版本支持 GetOrSet 模式，简化缓存逻辑：

```go
typedCache, err := cache.GetTyped[string](redisCache)
if err != nil {
    log.Fatal(err)
}

// 获取缓存，如果不存在则设置
value, err := typedCache.GetOrSet("key", func() (string, error) {
    // 从数据库或其他地方获取数据
    return "value from database", nil
}, 10*time.Minute)
```

## 常见问题

### 1. v1 和 v2 版本可以同时使用吗？

是的，v1 和 v2 版本可以同时使用。v2 版本位于 `frontapi/pkg/cache/v2` 包中，而 v1 版本位于 `frontapi/pkg/cache` 包中。

### 2. 如何逐步迁移到 v2 版本？

建议按以下步骤逐步迁移：

1. 首先在新功能中使用 v2 版本
2. 逐步将现有代码迁移到 v2 版本
3. 最后完全替换 v1 版本

### 3. v2 版本是否支持所有 v1 版本的功能？

是的，v2 版本支持所有 v1 版本的功能，并且添加了许多新功能。

### 4. 迁移到 v2 版本需要修改哪些代码？

主要需要修改以下几个方面：

1. 导入路径
2. 创建缓存实例的方式
3. 错误处理
4. 类型安全操作

## 结论

v2 版本提供了更简洁、更强大的 API，同时保持向后兼容性。通过本指南，您可以轻松地将应用程序从 v1 版本迁移到 v2 版本，并利用 v2 版本提供的新功能。 