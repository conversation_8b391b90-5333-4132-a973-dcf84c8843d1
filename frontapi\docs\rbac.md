# RBAC权限管理系统

## 介绍

本项目实现了基于RBAC（Role-Based Access Control，基于角色的访问控制）模型的权限管理系统，为前端框架Pure Admin提供完整的后端支持。

RBAC权限模型主要包含以下几个核心概念：
1. 用户（User）：系统的使用者
2. 角色（Role）：用户的分组，具有一定的权限集合
3. 权限（Permission）：对资源的操作权限
4. 菜单（Menu）：系统的功能模块，组织成树状结构

## 功能特性

- 用户管理：用户的增删改查、分配角色
- 角色管理：角色的增删改查、分配菜单和权限
- 权限管理：权限的增删改查
- 菜单管理：菜单的增删改查、构建菜单树
- 动态路由：根据用户角色返回动态路由
- 权限码：根据用户角色返回权限码，用于前端按钮级权限控制

## 数据库设计

系统包含以下数据表：

1. `ly_admin_user`：用户表
2. `ly_admin_sys_role`：角色表
3. `ly_admin_sys_menu`：菜单表
4. `ly_admin_permission`：权限表
5. `ly_admin_sys_role_menus`：角色菜单关联表
6. `ly_admin_role_permission`：角色权限关联表
7. `ly_admin_user_role`：用户角色关联表

详细的表结构参见 `/db/rbac.sql` 文件。

## API接口设计

### 用户管理相关接口

- `POST /api/proadm/permission/users/list`：获取用户列表
- `POST /api/proadm/permission/users/add`：创建用户
- `POST /api/proadm/permission/users/update/:id`：更新用户
- `POST /api/proadm/permission/users/delete/:id`：删除用户
- `POST /api/proadm/permission/users/detail/:id`：获取用户详情
- `POST /api/proadm/permission/users/assign-roles/:id`：分配用户角色
- `POST /api/proadm/permission/users/reset-password/:id`：重置用户密码
- `POST /api/proadm/permission/users/update-password/:id`：修改用户密码

### 角色管理相关接口

- `POST /api/proadm/permission/roles/list`：获取角色列表
- `POST /api/proadm/permission/roles/add`：创建角色
- `POST /api/proadm/permission/roles/update/:id`：更新角色
- `POST /api/proadm/permission/roles/delete/:id`：删除角色
- `POST /api/proadm/permission/roles/detail/:id`：获取角色详情
- `POST /api/proadm/permission/roles/assign-menus/:id`：分配角色菜单
- `POST /api/proadm/permission/roles/assign-permissions/:id`：分配角色权限

### 权限管理相关接口

- `POST /api/proadm/permission/permissions/list`：获取权限列表
- `POST /api/proadm/permission/permissions/add`：创建权限
- `POST /api/proadm/permission/permissions/update/:id`：更新权限
- `POST /api/proadm/permission/permissions/delete/:id`：删除权限
- `POST /api/proadm/permission/permissions/detail/:id`：获取权限详情

### 特殊接口

- `POST /api/proadm/permission/get-user-codes`：获取当前用户的权限码
- `POST /api/proadm/permission/get-async-routes`：获取用户动态路由

## 如何使用

1. 初始化数据库：执行 `/db/rbac.sql` 脚本创建数据库表和初始数据
2. 启动服务：运行 `go run cmd/api/main.go`
3. 默认管理员账号：用户名 `admin`，密码 `123456`

## 与Pure Admin前端框架对接

本系统与Pure Admin前端框架完美对接，支持：

1. 菜单权限：根据用户角色返回可访问的菜单
2. 按钮权限：通过权限码控制前端按钮的显示/隐藏
3. 动态路由：根据用户角色动态生成前端路由

具体对接方式，请参考Pure Admin的[RBAC权限文档](https://pure-admin.cn/pages/RBAC/)。

## 示例代码

### 在控制器中获取当前用户

```go
// 从上下文中获取用户ID
userID := c.Locals("userId")
if userID == nil {
    return utils.Unauthorized(c)
}

// 获取用户详情
user, err := permission.GetUserByID(utils.ToString(userID))
if err != nil {
    return utils.InternalServerError(c, fmt.Sprintf("获取用户失败: %v", err))
}
```

### 获取用户的角色和权限

```go
// 获取用户角色
userRoles, err := permission.GetUserRoles(utils.ToString(userID))
if err != nil {
    return utils.InternalServerError(c, fmt.Sprintf("获取用户角色失败: %v", err))
}

// 获取角色权限
var roleIDs []int
for _, role := range userRoles {
    roleIDs = append(roleIDs, role.ID)
}

authCodes, err := permission.GetRolePermissionCodes(roleIDs)
if err != nil {
    return utils.InternalServerError(c, fmt.Sprintf("获取权限码失败: %v", err))
}
``` 