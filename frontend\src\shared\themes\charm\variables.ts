/**
 * 魅力诱惑主题 - CSS变量
 * 粉色主调 + 浅粉 + 玫红
 * 设计参考: PrimeVue Pink主题 + Tailwind粉色系
 */

// CSS变量配置
const variables = {
    // 主要颜色
    'color-primary': '#db2777',
    'color-primary-light': '#ec4899',
    'color-primary-dark': '#be185d',
    'color-primary-contrast': '#ffffff',
    'color-primary-secondary': '#f472b6', // 第二配色 - 比主色浅的相同色系

    // 强调色
    'color-accent': '#d946ef',
    'color-accent-light': '#e879f9',
    'color-accent-dark': '#c026d3',
    'color-accent-contrast': '#ffffff',

    // 中性色调 - 粉色系
    'color-neutral-50': '#fdf2f8',
    'color-neutral-100': '#fce7f3',
    'color-neutral-200': '#fbcfe8',
    'color-neutral-300': '#f9a8d4',
    'color-neutral-400': '#f472b6',
    'color-neutral-500': '#ec4899',
    'color-neutral-600': '#db2777',
    'color-neutral-700': '#be185d',
    'color-neutral-800': '#9d174d',
    'color-neutral-900': '#831843',

    // 成功/错误/警告/信息色
    'color-success': '#10b981',
    'color-error': '#e11d48',
    'color-warning': '#f59e0b',
    'color-info': '#0284c7',

    // 背景颜色
    'color-background': '#ffffff',
    'color-background-alt': '#fdf2f8',
    'color-background-hover': '#fce7f3',
    'color-background-card': 'linear-gradient(145deg, #ffffff, #fdf2f8)',
    // 主页面背景和文字色
    'color-page-background': '#fef7f7',
    'color-page-text': '#831843',

    // 文本颜色
    'color-text': '#831843',
    'color-text-light': '#9d174d',
    'color-text-lighter': '#be185d',
    'color-text-contrast': '#ffffff',

    // 边框颜色
    'color-border': '#fbcfe8',
    'color-border-light': '#fce7f3',
    'color-border-dark': '#f9a8d4',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(219, 39, 119, 0.05)',
    'shadow': '0 1px 3px 0 rgba(219, 39, 119, 0.1), 0 1px 2px 0 rgba(219, 39, 119, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(219, 39, 119, 0.1), 0 2px 4px -1px rgba(219, 39, 119, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(219, 39, 119, 0.1), 0 4px 6px -2px rgba(219, 39, 119, 0.05)',

    // 特定区域颜色
    'header-gradient': 'linear-gradient(135deg, #db2777, #ec4899)',
    'footer-gradient': 'linear-gradient(135deg, #fce7f3, #fdf2f8)',
    'color-nav-gradient': '#831843',
    'color-footer-gradient': '#9d174d',
    'color-footer-border': '#fbcfe8',
    'button-gradient': 'linear-gradient(135deg, #ec4899, #db2777)',
    'card-gradient': 'linear-gradient(145deg, #ffffff, #fdf2f8)',
    'accent-gradient': 'linear-gradient(135deg, #e879f9, #d946ef)',

    // PrimeVue集成
    'primary-color': '#db2777',
    'primary-color-text': '#ffffff',
    'surface-ground': '#fef7f7',
    'surface-section': '#fef7f7',
    'surface-card': 'linear-gradient(145deg, #ffffff, #fdf2f8)',
    'surface-overlay': '#ffffff',
    'surface-border': '#fbcfe8',
    'surface-hover': '#fce7f3',
    // 主页面内容区域
    'content-bg': '#fef7f7',
    'content-text': '#831843',
};

export default variables;