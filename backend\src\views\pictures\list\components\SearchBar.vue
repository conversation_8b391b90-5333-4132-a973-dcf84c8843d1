<template>
  <div class="search-container">
    <el-form :model="searchForm" ref="searchFormRef" :inline="true">
      <el-form-item label="标题" prop="title">
        <el-input v-model="searchForm.title" placeholder="请输入图片标题" clearable />
      </el-form-item>

      <el-form-item label="分类" prop="category_id">
        <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable>
          <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="专辑" prop="album_id">
        <el-select v-model="searchForm.album_id" placeholder="请选择专辑" clearable>
          <el-option v-for="item in albumOptions" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button @click="handleRefresh">刷新</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { getAllPictureCategories, getPictureAlbumList } from '@/service/api/pictures/pictures';
import type { PictureCategory, PictureAlbum } from '@/types/pictures';

const emit = defineEmits(['search', 'reset', 'refresh']);

// 搜索表单
const searchForm = reactive({
  title: '',
  category_id: '',
  album_id: '',
  status: undefined
});

const searchFormRef = ref();

// 状态选项
const statusOptions = [
  { label: '正常', value: 1 },
  { label: '禁用', value: 0 }
];

// 分类选项列表
const categoryOptions = ref<any[]>([]);
// 专辑选项列表
const albumOptions = ref<any[]>([]);

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await getAllPictureCategories();
    const data = res as any;
    if (data?.data) {
      categoryOptions.value = data.data;
    }
  } catch (error) {
    console.error('获取分类列表失败', error);
  }
};

// 获取专辑列表
const fetchAlbums = async () => {
  try {
    const res = await getPictureAlbumList({ page: { pageNo: 1, pageSize: 100 } });
    const data = res as any;
    if (data?.data?.list) {
      albumOptions.value = data.data.list;
    }
  } catch (error) {
    console.error('获取专辑列表失败', error);
  }
};

// 初始化
onMounted(async () => {
  await fetchCategories();
  await fetchAlbums();
});

// 搜索事件
const handleSearch = () => {
  emit('search', {
    title: searchForm.title,
    category_id: searchForm.category_id,
    album_id: searchForm.album_id,
    status: searchForm.status
  });
};

// 重置事件
const handleReset = () => {
  searchForm.title = '';
  searchForm.category_id = '';
  searchForm.album_id = '';
  searchForm.status = undefined;
  emit('reset');
};

// 刷新事件
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped>
.search-container {
  margin-bottom: 20px;
}
</style>
