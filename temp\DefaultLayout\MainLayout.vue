<template>
    <div class="min-h-screen flex flex-col bg-surface-50 dark:bg-surface-300 text-surface-900 dark:text-surface-0 transition-colors duration-300">
        <!-- 顶部导航栏 -->
        <TopHeader />
        
        <!-- 主导航菜单 -->
        <NavBar />
        
        <!-- 主内容区域 -->
        <main class="flex-1 bg-surface-100 dark:bg-surface-800 pt-6 pb-12">
            <!-- 内容容器 -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- 面包屑导航 -->
                <div v-if="showBreadcrumb" class="mb-6">
                    <Breadcrumb :model="breadcrumbItems" class="p-0 bg-transparent border-none" />
                </div>
                
                <!-- 页面内容插槽 -->
                <div class="bg-surface-0 dark:bg-surface-900 rounded-xl shadow-md border border-surface-200 dark:border-surface-700 overflow-hidden">
                    <slot></slot>
                </div>
            </div>
        </main>
        
        <!-- 底部 -->
        <AppFooter />
        
        <!-- 回到顶部按钮 -->
        <ScrollTop 
            target="parent" 
            :threshold="600" 
            class="bg-primary hover:bg-primary-600 text-white shadow-lg" 
            icon="pi pi-arrow-up" />
    </div>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/store/modules/theme';
import Breadcrumb from 'primevue/breadcrumb';
import ScrollTop from 'primevue/scrolltop';
import { computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import AppFooter from './AppFooter.vue';
import NavBar from './NavBar.vue';
import TopHeader from './TopHeader.vue';

// 路由和主题状态
const route = useRoute();
const themeStore = useThemeStore();
const isDarkMode = computed(() => themeStore.isDark);

// 面包屑配置
const showBreadcrumb = computed(() => {
    // 根据路由决定是否显示面包屑
    return route.path !== '/' && route.meta?.showBreadcrumb !== false;
});

const breadcrumbItems = computed(() => {
    const items = [];
    const pathSegments = route.path.split('/').filter(segment => segment);
    
    // 添加首页
    items.push({ 
        label: '首页', 
        icon: 'pi pi-home', 
        to: '/',
        class: 'text-surface-600 dark:text-surface-300'
    });
    
    // 根据路径构建面包屑
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
        currentPath += `/${segment}`;
        const isLast = index === pathSegments.length - 1;
        
        items.push({
            label: getSegmentLabel(segment),
            to: isLast ? undefined : currentPath,
            class: isLast ? 'text-primary font-medium' : 'text-surface-600 dark:text-surface-300'
        });
    });
    
    return items;
});

// 获取路径段的显示标签
function getSegmentLabel(segment: string): string {
    const labelMap: Record<string, string> = {
        'videos': '视频',
        'movies': '电影',
        'shorts': '短视频',
        'community': '社区',
        'pictures': '图片',
        'comics': '漫画',
        'ebooks': '电子书',
        'category': '分类',
        'action': '动作',
        'comedy': '喜剧',
        'drama': '剧情',
        'horror': '恐怖',
        'animation': '动画',
        'documentary': '纪录片',
        'scifi': '科幻',
        'romance': '爱情'
    };
    
    return labelMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
}

// 监听主题变化，确保正确应用类
watch(isDarkMode, (newValue) => {
    applyThemeClasses(newValue);
});

// 应用主题类到根元素
function applyThemeClasses(isDark: boolean) {
    const rootElement = document.documentElement;
    if (isDark) {
        rootElement.classList.add('dark');
        rootElement.classList.add('sof-dark');
    } else {
        rootElement.classList.remove('dark');
        rootElement.classList.remove('sof-dark');
    }
}

onMounted(() => {
    // 初始化时应用主题类
    applyThemeClasses(isDarkMode.value);
});
</script>

<style scoped lang="scss">
:deep(.p-breadcrumb) {
    .p-breadcrumb-list {
        li {
            .p-menuitem-link {
                &:hover {
                    background: transparent;
                    text-decoration: underline;
                }
            }
            
            &:last-child {
                .p-menuitem-text {
                    font-weight: 500;
                }
            }
        }
        
        .p-breadcrumb-chevron {
            margin: 0 0.5rem;
            color: var(--surface-400);
        }
    }
}

:deep(.p-scrolltop) {
    border-radius: 50%;
    width: 3rem;
    height: 3rem;
    
    &.p-link {
        &:hover {
            transform: scale(1.05);
        }
    }
    
    .p-scrolltop-icon {
        font-size: 1.25rem;
    }
}
</style>