<template>
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-6">{{ $t('personal.profile.title') }}</h1>

        <div class="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">{{ $t('personal.profile.basicInfo') }}</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">{{ $t('personal.profile.username') }}</label>
                    <p class="p-2 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                        用户123456
                    </p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">{{ $t('personal.profile.email') }}</label>
                    <p class="p-2 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                        <EMAIL>
                    </p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">{{ $t('personal.profile.registrationDate') }}</label>
                    <p class="p-2 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                        2023-05-15
                    </p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">{{ $t('personal.profile.lastLogin') }}</label>
                    <p class="p-2 bg-gray-50 dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                        2023-10-20 15:30:45
                    </p>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button
                    class="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50">
                    {{ $t('personal.profile.editProfile') }}
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 可以在这里添加个人资料相关的逻辑
</script>

<style scoped lang="scss">
// 个人资料页面的特定样式</style>