package content_creator

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type ContentRevenue struct {
	models.BaseModel
	ContentType    string         `gorm:"column:content_type;type:string;not null;comment:内容类型" json:"content_type"`                            // 内容类型
	ContentID      string         `gorm:"column:content_id;type:string;not null;comment:内容ID" json:"content_id"`                                // 内容ID
	UserID         string         `gorm:"column:user_id;type:string;not null;comment:创作者ID" json:"user_id"`                                     // 创作者ID
	OrderID        string         `gorm:"column:order_id;type:string;comment:订单ID" json:"order_id"`                                             // 订单ID
	Amount         float64        `gorm:"column:amount;type:decimal(10,2);not null;comment:收益金额" json:"amount"`                                 // 收益金额
	PlatformAmount float64        `gorm:"column:platform_amount;type:decimal(10,2);not null;comment:平台分成" json:"platform_amount"`               // 平台分成
	ReferrerAmount float64        `gorm:"column:referrer_amount;type:decimal(10,2);not null;default:0.00;comment:推荐人分成" json:"referrer_amount"` // 推荐人分成
	Status         string         `gorm:"column:status;type:string;not null;default:'pending';comment:状态" json:"status"`                        // 状态
	SettleTime     types.JSONTime `gorm:"column:settle_time;type:datetime;comment:结算时间" json:"settle_time"`                                     // 结算时间
}

func (ContentRevenue) TableName() string {
	return "ly_content_revenues"
}
