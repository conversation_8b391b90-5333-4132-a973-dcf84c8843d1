package image_utils

import (
	"bytes"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"os"
	"path/filepath"
	"strings"

	"frontapi/pkg/utils"
)

// CompressImageAsync processes image compression in a separate goroutine
// It doesn't block the calling function and handles all errors internally
func CompressImageAsync(originalPath string) {
	go func() {
		// Recover from any panics to prevent crashes
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Recovered from panic in image compression: %v\n", r)
			}
		}()

		err := CompressImage(originalPath)
		if err != nil {
			fmt.Printf("Error compressing image %s: %v\n", originalPath, err)
		} else {
			fmt.Printf("Successfully compressed image: %s\n", originalPath)
		}
	}()
}

// CompressImage compresses an image file based on its format
func CompressImage(originalPath string) error {
	// Get temp directory from env
	tempBaseDir := utils.GetEnv("RUNTEMP", os.TempDir())
	tempDir := filepath.Join(tempBaseDir, "images")

	// Create temp directory if it doesn't exist
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		if err := os.MkdirAll(tempDir, 0755); err != nil {
			return fmt.Errorf("failed to create temp directory: %w", err)
		}
	}

	// Get filename and create temp file path
	fileName := filepath.Base(originalPath)
	tempFilePath := filepath.Join(tempDir, fileName)

	// Read original file
	originalData, err := os.ReadFile(originalPath)
	if err != nil {
		return fmt.Errorf("failed to read original file: %w", err)
	}

	// Detect image format and compress
	fileExt := strings.ToLower(filepath.Ext(originalPath))
	var compressedData []byte

	switch fileExt {
	case ".jpg", ".jpeg":
		compressedData, err = compressJpeg(originalData, 85) // 85% quality
	case ".png":
		compressedData, err = compressPng(originalData)
	default:
		return fmt.Errorf("unsupported image format: %s", fileExt)
	}

	if err != nil {
		return fmt.Errorf("compression failed: %w", err)
	}

	// Check if compression actually reduced file size
	if len(compressedData) >= len(originalData) {
		return fmt.Errorf("compression did not reduce file size")
	}

	// Write to temp file
	if err := os.WriteFile(tempFilePath, compressedData, 0644); err != nil {
		return fmt.Errorf("failed to write temp file: %w", err)
	}

	// Replace original with compressed version
	if err := os.WriteFile(originalPath, compressedData, 0644); err != nil {
		// Clean up temp file
		os.Remove(tempFilePath)
		return fmt.Errorf("failed to replace original file: %w", err)
	}

	// Clean up temp file
	os.Remove(tempFilePath)
	return nil
}

// compressJpeg compresses a JPEG image with specified quality (1-100)
func compressJpeg(data []byte, quality int) ([]byte, error) {
	// Decode image
	img, err := jpeg.Decode(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to decode JPEG: %w", err)
	}

	// Create buffer for compressed image
	var buf bytes.Buffer

	// Encode with specified quality
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
	if err != nil {
		return nil, fmt.Errorf("failed to encode JPEG: %w", err)
	}

	return buf.Bytes(), nil
}

// compressPng compresses a PNG image
func compressPng(data []byte) ([]byte, error) {
	// Decode image
	img, _, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to decode PNG: %w", err)
	}

	// Create buffer for compressed image
	var buf bytes.Buffer

	// Use best compression
	encoder := png.Encoder{
		CompressionLevel: png.BestCompression,
	}
	err = encoder.Encode(&buf, img)
	if err != nil {
		return nil, fmt.Errorf("failed to encode PNG: %w", err)
	}

	return buf.Bytes(), nil
}
