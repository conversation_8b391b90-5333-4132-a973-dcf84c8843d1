export interface AbstractUser{
  id: string
  username: string
  nickname: string
  avatar: string
}
//基础Author
export interface BaseAuthor extends AbstractUser{
  likeCount: number
  isContentCreator: number
  viewCount: number
  followCount: number
  totalVideos: number
  totalShorts: number
  totalPosts: number
  isFollowed: boolean

}
// 基础用户信息
export interface BaseUser extends AbstractUser{
  gender?: 'male' | 'female' | 'unknown'
  birthday?: string
  location?: string
  signature?: string
  verified: boolean
  verifiedType?: 'personal' | 'enterprise' | 'media' | 'government'
  level: number
  exp: number
  createdAt: string
  updatedAt: string
}

// 用户资料（完整信息）
export interface UserProfile extends BaseUser {
  followingCount: number
  followersCount: number
  likesCount: number
  videosCount: number
  articlesCount: number
  isFollowing?: boolean
  isBlocked?: boolean
  privacy: {
    showEmail: boolean
    showPhone: boolean
    showBirthday: boolean
    showLocation: boolean
    allowMessage: boolean
    allowComment: boolean
  }
  preferences: {
    language: string
    theme: 'light' | 'dark' | 'auto'
    notifications: {
      like: boolean
      comment: boolean
      follow: boolean
      system: boolean
    }
  }
}

// 用户更新数据
export interface UserUpdateData {
  nickname?: string
  avatar?: string
  gender?: 'male' | 'female' | 'unknown'
  birthday?: string
  location?: string
  signature?: string
  privacy?: Partial<UserProfile['privacy']>
  preferences?: Partial<UserProfile['preferences']>
}

// 创作者信息
export interface Creator extends BaseUser {
  description: string
  tags: string[]
  categories: string[]
  totalViews: number
  totalLikes: number
  totalShares: number
  totalComments: number
  monthlyViews: number
  isOfficial: boolean
  socialLinks: {
    weibo?: string
    wechat?: string
    douyin?: string
    bilibili?: string
    youtube?: string
    twitter?: string
    instagram?: string
  }
  achievements: {
    id: string
    name: string
    icon: string
    description: string
    unlockedAt: string
  }[]
}

// 视频创作者
export interface VideoCreator extends Creator {
  specialties: string[]
  equipment: string[]
  collaborationInfo: {
    available: boolean
    types: string[]
    contactEmail?: string
    priceRange?: string
  }
}

// 明星详情
export interface CelebrityDetail extends BaseUser {
  realName: string
  profession: string[]
  nationality: string
  birthPlace: string
  height?: number
  weight?: number
  bloodType?: string
  constellation?: string
  agency?: string
  debutDate?: string
  biography: string
  works: {
    id: string
    title: string
    type: 'movie' | 'tv' | 'music' | 'variety' | 'other'
    year: number
    role?: string
    poster?: string
  }[]
  awards: {
    id: string
    name: string
    year: number
    category: string
    work?: string
  }[]
  socialMedia: {
    platform: string
    url: string
    followers?: number
  }[]
}

// 视频信息
export interface Video {
  id: string
  title: string
  description: string
  cover: string
  url: string
  duration: number
  size: number
  resolution: string
  format: string
  quality: 'SD' | 'HD' | 'FHD' | '4K'
  tags: string[]
  category: string
  creatorId: string
  creator: BaseUser
  viewsCount: number
  likesCount: number
  sharesCount: number
  commentsCount: number
  favoritesCount: number
  isLiked?: boolean
  isFavorited?: boolean
  status: 'draft' | 'published' | 'private' | 'deleted'
  publishedAt?: string
  createdAt: string
  updatedAt: string
}

// 动态信息
export interface Activity {
  id: string
  type: 'video' | 'article' | 'like' | 'follow' | 'comment' | 'share'
  userId: string
  user: BaseUser
  content?: string
  targetId?: string
  targetType?: string
  targetTitle?: string
  targetCover?: string
  createdAt: string
}

// 评论信息
export interface Comment {
  id: string
  content: string
  userId: string
  user: BaseUser
  targetId: string
  targetType: 'video' | 'article' | 'post'
  parentId?: string
  replyToId?: string
  replyToUser?: BaseUser
  likesCount: number
  repliesCount: number
  isLiked?: boolean
  replies?: Comment[]
  createdAt: string
  updatedAt: string
}
// 兼容性类型别名（保持向后兼容）
export type User = UserProfile
export type Author = Creator

// 类型守卫
export const isUser = (obj: any): obj is UserProfile => {
  return obj && typeof obj.id === 'string' && typeof obj.username === 'string'
}