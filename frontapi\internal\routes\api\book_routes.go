package api

import (
	adminBooks "frontapi/internal/admin/books"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterBookRoutes 注册电子书相关路由
func RegisterBookRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建电子书API控制器
	controller := adminBooks.NewBookController(
		services.BookService,
		services.BookCategoryService,
		services.BookChapterService,
	)

	// 电子书管理路由组
	bookGroup := apiGroup.Group("/books", middleware.AuthRequired())

	// 电子书管理
	bookGroup.Post("/list", controller.ListBooks)
	bookGroup.Post("/detail/:id?", controller.GetBook)
	bookGroup.Post("/add", controller.CreateBook)
	bookGroup.Post("/update", controller.UpdateBook)
	bookGroup.Post("/update-status", controller.UpdateBookStatus)
	bookGroup.Post("/delete/:id?", controller.DeleteBook)

	// 电子书分类管理路由组
	categoryGroup := apiGroup.Group("/books/categories", middleware.AuthRequired())
	{
		bookCategoryController := adminBooks.NewBookCategoryController(services.BookCategoryService, services.BookService)
		// 电子书分类管理
		categoryGroup.Post("/list", bookCategoryController.ListBookCategories)
		categoryGroup.Post("/detail/:id?", bookCategoryController.GetBookCategory)
		categoryGroup.Post("/add", bookCategoryController.CreateBookCategory)
		categoryGroup.Post("/update", bookCategoryController.UpdateBookCategory)
		categoryGroup.Post("/update-status", bookCategoryController.UpdateBookCategoryStatus)
		categoryGroup.Post("/delete/:id?", bookCategoryController.DeleteBookCategory)
	}

	// 电子书章节管理路由组
	chapterGroup := apiGroup.Group("/books/chapters", middleware.AuthRequired())
	{
		chapterController := adminBooks.NewBookChapterController(services.BookChapterService, services.BookService, services.BookCategoryService)
		// 电子书章节管理
		chapterGroup.Post("/list", chapterController.ListBookChapters)
		chapterGroup.Post("/detail/:id?", chapterController.GetBookChapter)
		chapterGroup.Post("/add", chapterController.CreateBookChapter)
		chapterGroup.Post("/update", chapterController.UpdateBookChapter)
		chapterGroup.Post("/delete/:id?", chapterController.DeleteBookChapter)
		chapterGroup.Post("/batch-upload", chapterController.BatchUploadChapters)
		chapterGroup.Post("/reorder", chapterController.ReorderBookChapter)
		chapterGroup.Post("/batch-update-order", chapterController.BatchUpdateChapterOrder)
	}

}
