<template>
    <el-dialog :title="isBatch ? '批量审核评论' : '审核评论'" :model-value="visible" @update:model-value="handleClose"
        width="500px" destroy-on-close>
        <div class="review-dialog-content">
            <div v-if="isBatch" class="batch-info">
                <el-alert type="info" :closable="false">
                    <div class="batch-info-text">
                        <p>您正在批量审核 <strong>{{ commentIds.length }}</strong> 条评论</p>
                    </div>
                </el-alert>
            </div>

            <div v-else class="comment-info">
                <el-descriptions :column="1" border>
                    <el-descriptions-item label="评论ID">{{ commentId }}</el-descriptions-item>
                    <el-descriptions-item label="评论内容">
                        <div class="comment-text">{{ commentContent }}</div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <div class="review-form">
                <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
                    <el-form-item label="审核结果" prop="status">
                        <el-radio-group v-model="form.status">
                            <el-radio :label="2">通过</el-radio>
                            <el-radio :label="-2">拒绝</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="拒绝原因" prop="reason" v-if="form.status === -2">
                        <el-input v-model="form.reason" type="textarea" :rows="4" placeholder="请输入拒绝原因"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :loading="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';

// Props定义
interface Props {
    visible: boolean;
    loading?: boolean;
    isBatch?: boolean;
    commentId?: string;
    commentContent?: string;
    commentIds?: string[];
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    loading: false,
    isBatch: false,
    commentId: '',
    commentContent: '',
    commentIds: () => []
});

// Emits定义
interface Emits {
    'update:visible': [value: boolean];
    'submit': [status: number, reason: string, id?: string, ids?: string[]];
}

const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive({
    status: 2, // 默认通过
    reason: ''
});

// 表单校验规则
const rules = reactive<FormRules>({
    status: [
        { required: true, message: '请选择审核结果', trigger: 'change' }
    ],
    reason: [
        { required: form.status === -2, message: '请输入拒绝原因', trigger: 'blur' },
        { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
    ]
});

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
    // 重置表单
    form.status = 2;
    form.reason = '';
};

// 提交表单
const handleSubmit = async () => {
    if (form.status === -2 && form.reason == '') {
        // 如果是拒绝，需要验证表单
        ElMessage.warning('请输入拒绝原因');
        return;
    }
    // 如果是通过，直接提交
    submitReview();
};

// 提交审核结果
const submitReview = () => {
    if (props.isBatch) {
        // 批量审核
        if (props.commentIds.length === 0) {
            ElMessage.warning('没有选择要审核的评论');
            return;
        }
        emit('submit', form.status, form.reason, undefined, props.commentIds);
    } else {
        // 单个审核
        if (!props.commentId) {
            ElMessage.warning('评论ID不能为空');
            return;
        }
        emit('submit', form.status, form.reason, props.commentId);
    }

    // 关闭对话框
    handleClose();
};
</script>

<style scoped lang="scss">
.review-dialog-content {
    padding: 10px;

    .batch-info {
        margin-bottom: 20px;

        .batch-info-text {
            p {
                margin: 0;
                line-height: 1.5;
            }
        }
    }

    .comment-info {
        margin-bottom: 20px;

        .comment-text {
            white-space: pre-wrap;
            word-break: break-all;
            padding: 8px;
            background-color: #f5f7fa;
            border-radius: 4px;
            min-height: 40px;
        }
    }

    .review-form {
        margin-top: 20px;
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>