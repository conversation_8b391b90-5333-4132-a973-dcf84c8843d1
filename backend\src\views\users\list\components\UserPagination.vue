<template>
  <div class="user-pagination">
    <div class="pagination-info">
      <el-text class="info-text">
        共 <el-text type="primary" class="font-medium">{{ total }}</el-text> 条记录，
        当前第 <el-text type="primary" class="font-medium">{{ currentPage }}</el-text> 页，
        共 <el-text type="primary" class="font-medium">{{ totalPages }}</el-text> 页
      </el-text>
    </div>
    
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :hide-on-single-page="hideOnSinglePage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="pagination-component"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
interface Props {
  currentPage: number;
  pageSize: number;
  total: number;
  pageSizes?: number[];
  layout?: string;
  background?: boolean;
  hideOnSinglePage?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  pageSizes: () => [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  hideOnSinglePage: false
});

// Emits
interface Emits {
  'update:currentPage': [value: number];
  'update:pageSize': [value: number];
  sizeChange: [size: number];
  currentChange: [current: number];
}

const emit = defineEmits<Emits>();

// 计算属性
const totalPages = computed(() => Math.ceil(props.total / props.pageSize));

// 事件处理
const handleSizeChange = (size: number) => {
  emit('update:pageSize', size);
  emit('sizeChange', size);
};

const handleCurrentChange = (current: number) => {
  emit('update:currentPage', current);
  emit('currentChange', current);
};
</script>

<style scoped lang="scss">
.user-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;

  .pagination-info {
    display: flex;
    align-items: center;

    .info-text {
      font-size: 14px;
      color: #606266;
    }
  }

  .pagination-component {
    flex-shrink: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-pagination {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    
    .pagination-info {
      justify-content: center;
      margin-bottom: 8px;
    }
    
    .pagination-component {
      align-self: center;
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .user-pagination {
    border-top-color: #4c4d4f;
  }
}
</style> 