export interface BookItem {
    id: string; //电子书ID
    title: string; //标题
    cover: string; //封面图URL
    rating: number; //评分
    description: string; //描述
    author: string; //作者
    category_id: string; //分类ID
    category_name: string; //分类名称
    word_count: number; //字数
    chapter_count: number; //章节数
    read_count: number; //阅读数
    like_count: number; //点赞数
    score: number; //评分
    favorite_count: number; //收藏数
    share_count: number; //分享次数
    comment_count: number; //评论数量
    status: string; //状态：completed-已完结，serializing-连载中
    tags_json: string; //标签JSON数据
    is_paid: number; //是否付费：0-免费，1-付费
    is_featured: number; //是否推荐
    tags: string; //标签
    price: number; //价格
    creator_id: string; //上传者ID
    creator_name: string; //创作者名称
    creator_avatar: string; //创作者头像
    publish_date: string; //发布日期
    created_at: string; //创建时间
    updated_at: string; //更新时间
}
export interface BookCategory {
    id: string;//分类ID
    name: string;//分类名称
    description: string;//分类描述
    status: number;//状态：0-禁用，1-正常
    created_at: string;//创建时间
    updated_at: string;//更新时间

}

export interface BookChapter {
    id: string;//章节ID
    book_id: string;//电子书ID
    book_title?: string;//书籍标题
    title: string;//章节标题
    chapter_number: number;//章节序号
    sort_order?: number;//排序序号
    content: string;//章节内容
    word_count: number;//字数
    read_count: number;//阅读次数
    is_locked: number;//是否锁定：0-免费，1-付费
    price: number;//单章价格
    create_time?: string;//创建时间
    update_time?: string;//更新时间
    created_at?: string;//创建时间
    updated_at?: string;//更新时间
    status: number;//状态：0-禁用，1-正常
}
export interface CreateBookChapterRequest{
    book_id: string;
    title: string;
    chapter_number: number;
    content: string;
    is_locked: number;
    price: number;
}
export interface UpdateBookChapterRequest{
    id: string;
    title: string;
    chapter_number: number;
    content: string;
    is_locked: number;
    price: number;
}
export interface BookParams{
    page:{
      pageNo:number,
      pageSize:number
    },
  data?:{
      title?: string,
      author?: string,
      category_id?: string,
      status?: string,
      is_paid?: number,
      is_featured?: number,
      book_id?: string
  }
}
