package home

import (
	"frontapi/internal/models"
)

// Tag represents a tag for the home page
type Tag struct {
	models.BaseModel
	Name   string `json:"name" gorm:"not null;comment:标签名称"`
	Icon   string `json:"icon" gorm:"comment:标签图标"`
	Sort   int    `json:"sort" gorm:"default:0;comment:排序"`
	Status int8   `json:"status" gorm:"default:1;comment:状态：0-禁用，1-正常"`
}

// TableName 指定表名
func (Tag) TableName() string {
	return "ly_home_tags"
}

// HomeVideo represents a video item for home page display
type HomeVideo struct {
	models.BaseModel
	Title         string `json:"title" gorm:"not null;comment:视频标题"`
	Description   string `json:"description" gorm:"comment:视频描述"`
	Cover         string `json:"cover" gorm:"not null;comment:封面图URL"`
	URL           string `json:"url" gorm:"not null;comment:视频URL"`
	Duration      int    `json:"duration" gorm:"default:0;comment:时长(秒)"`
	ViewCount     uint64 `json:"view_count" gorm:"default:0;comment:观看次数"`
	LikeCount     uint64 `json:"like_count" gorm:"default:0;comment:点赞数"`
	CommentCount  uint64 `json:"comment_count" gorm:"default:0;comment:评论数"`
	CategoryID    string `json:"category_id" gorm:"type:string;size:36;comment:分类ID"`
	CategoryName  string `json:"category_name" gorm:"comment:分类名称"`
	CreatorID     string `json:"creator_id" gorm:"type:string;size:36;not null;comment:创作者ID"`
	CreatorName   string `json:"creator_name" gorm:"comment:创作者名称"`
	CreatorAvatar string `json:"creator_avatar" gorm:"comment:创作者头像"`
	Type          int8   `json:"type" gorm:"comment:视频类型: 1-热门 2-推荐"`
	Status        int8   `json:"status" gorm:"default:1;comment:状态：0-禁用，1-正常"`
}

// TableName 指定表名
func (HomeVideo) TableName() string {
	return "ly_home_videos"
}

// Star represents a celebrity/star for home page recommendations
type Star struct {
	models.BaseModel
	Name        string `json:"name" gorm:"not null;comment:明星名称"`
	Avatar      string `json:"avatar" gorm:"comment:明星头像"`
	Description string `json:"description" gorm:"comment:明星描述"`
	FollowCount uint64 `json:"follow_count" gorm:"default:0;comment:关注数"`
	Sort        int    `json:"sort" gorm:"default:0;comment:排序"`
	Status      int8   `json:"status" gorm:"default:1;comment:状态：0-禁用，1-正常"`
}

// TableName 指定表名
func (Star) TableName() string {
	return "ly_stars"
}

// Category represents a content category
type Category struct {
	models.BaseModel
	Name        string `json:"name" gorm:"not null;comment:分类名称"`
	Icon        string `json:"icon" gorm:"comment:分类图标"`
	Description string `json:"description" gorm:"comment:分类描述"`
	ParentID    string `json:"parent_id" gorm:"type:string;size:36;comment:父分类ID"`
	Path        string `json:"path" gorm:"comment:分类路径"`
	Sort        int    `json:"sort" gorm:"default:0;comment:排序"`
	Status      int8   `json:"status" gorm:"default:1;comment:状态：0-禁用，1-正常"`
}

// TableName 指定表名
func (Category) TableName() string {
	return "ly_categories"
}
