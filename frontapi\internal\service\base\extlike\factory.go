package extlike

import (
	"fmt"

	goredis "github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"

	redisClient "frontapi/pkg/redis"
)

// ServiceBuilder 服务构建器
type ServiceBuilder struct {
	config        *Config
	redisClient   goredis.UniversalClient
	mongoClient   *mongo.Client
	mongoDatabase *mongo.Database
}

// NewServiceBuilder 创建服务构建器
func NewServiceBuilder() *ServiceBuilder {
	return &ServiceBuilder{
		config: DefaultConfig(),
	}
}

// WithConfig 设置配置
func (b *ServiceBuilder) WithConfig(config *Config) *ServiceBuilder {
	b.config = config
	return b
}

// WithStrategy 设置存储策略
func (b *ServiceBuilder) WithStrategy(strategy StorageStrategy) *ServiceBuilder {
	b.config.Strategy = strategy
	return b
}

// WithSystemRedis 使用系统Redis客户端
func (b *ServiceBuilder) WithSystemRedis() *ServiceBuilder {
	b.config.Redis.Enabled = true
	b.config.Redis.UseSystem = true
	b.config.Redis.Client = redisClient.Client // 使用系统Redis客户端
	return b
}

// WithRedisClient 设置自定义Redis客户端
func (b *ServiceBuilder) WithRedisClient(client goredis.UniversalClient) *ServiceBuilder {
	b.config.Redis.Enabled = true
	b.config.Redis.UseSystem = false
	b.redisClient = client
	return b
}

// WithMongoClient 设置MongoDB客户端
func (b *ServiceBuilder) WithMongoClient(client *mongo.Client, database *mongo.Database) *ServiceBuilder {
	b.config.MongoDB.Enabled = true
	b.config.MongoDB.UseSystem = false
	b.mongoClient = client
	b.mongoDatabase = database
	return b
}

// WithSystemMongo 使用系统MongoDB客户端（如果有的话）
func (b *ServiceBuilder) WithSystemMongo(client *mongo.Client, database *mongo.Database) *ServiceBuilder {
	b.config.MongoDB.Enabled = true
	b.config.MongoDB.UseSystem = true
	b.config.MongoDB.Client = client
	b.config.MongoDB.Database = database
	return b
}

// EnableRedisOnly 仅启用Redis
func (b *ServiceBuilder) EnableRedisOnly() *ServiceBuilder {
	b.config.Strategy = RedisOnly
	b.config.Redis.Enabled = true
	b.config.MongoDB.Enabled = false
	return b
}

// EnableMongoOnly 仅启用MongoDB
func (b *ServiceBuilder) EnableMongoOnly() *ServiceBuilder {
	b.config.Strategy = MongoOnly
	b.config.MongoDB.Enabled = true
	b.config.Redis.Enabled = false
	return b
}

// EnableDualWrite 启用双写模式
func (b *ServiceBuilder) EnableDualWrite() *ServiceBuilder {
	b.config.Strategy = DualWrite
	b.config.Redis.Enabled = true
	b.config.MongoDB.Enabled = true
	return b
}

// Build 构建服务
func (b *ServiceBuilder) Build() (ExtendedLikeService, error) {
	// 验证配置
	if err := b.config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 如果有自定义客户端，设置到配置中
	if b.redisClient != nil {
		b.config.Redis.Client = b.redisClient
	}

	if b.mongoClient != nil {
		b.config.MongoDB.Client = b.mongoClient
		b.config.MongoDB.Database = b.mongoDatabase
	}

	// 创建服务
	return NewExtendedLikeService(b.config)
}

// QuickCreateRedisService 快速创建Redis点赞服务
func QuickCreateRedisService() (ExtendedLikeService, error) {
	return NewServiceBuilder().
		EnableRedisOnly().
		WithSystemRedis().
		Build()
}

// QuickCreateMongoService 快速创建MongoDB点赞服务
func QuickCreateMongoService(client *mongo.Client, database *mongo.Database) (ExtendedLikeService, error) {
	return NewServiceBuilder().
		EnableMongoOnly().
		WithMongoClient(client, database).
		Build()
}

// QuickCreateDualWriteService 快速创建双写点赞服务
func QuickCreateDualWriteService(mongoClient *mongo.Client, mongoDatabase *mongo.Database) (ExtendedLikeService, error) {
	return NewServiceBuilder().
		EnableDualWrite().
		WithSystemRedis().
		WithMongoClient(mongoClient, mongoDatabase).
		Build()
}

// CreateService 创建点赞服务（通用方法）
// 参数：
//   - strategy: 存储策略
//   - redisClient: Redis客户端（可选）
//   - mongoClient: MongoDB客户端（可选）
//   - mongoDatabase: MongoDB数据库（可选）
func CreateService(strategy StorageStrategy, redisClient goredis.UniversalClient, mongoClient *mongo.Client, mongoDatabase *mongo.Database) (ExtendedLikeService, error) {
	builder := NewServiceBuilder().WithStrategy(strategy)

	switch strategy {
	case RedisOnly:
		if redisClient != nil {
			builder.WithRedisClient(redisClient)
		} else {
			builder.WithSystemRedis()
		}

	case MongoOnly:
		if mongoClient == nil || mongoDatabase == nil {
			return nil, fmt.Errorf("MongoDB策略需要提供有效的MongoDB客户端和数据库")
		}
		builder.WithMongoClient(mongoClient, mongoDatabase)

	case RedisFirst, MongoFirst, DualWrite:
		if redisClient != nil {
			builder.WithRedisClient(redisClient)
		} else {
			builder.WithSystemRedis()
		}

		if mongoClient == nil || mongoDatabase == nil {
			return nil, fmt.Errorf("双存储策略需要提供有效的MongoDB客户端和数据库")
		}
		builder.WithMongoClient(mongoClient, mongoDatabase)

	default:
		return nil, fmt.Errorf("不支持的存储策略: %v", strategy)
	}

	return builder.Build()
}

// MustCreateService 创建点赞服务，失败时panic
func MustCreateService(strategy StorageStrategy, redisClient goredis.UniversalClient, mongoClient *mongo.Client, mongoDatabase *mongo.Database) ExtendedLikeService {
	service, err := CreateService(strategy, redisClient, mongoClient, mongoDatabase)
	if err != nil {
		panic(fmt.Sprintf("创建点赞服务失败: %v", err))
	}
	return service
}

// NewExtendedService 创建扩展点赞服务实例
func NewExtendedService(config *Config) (ExtendedLikeService, error) {
	return NewExtendedLikeService(config)
}
