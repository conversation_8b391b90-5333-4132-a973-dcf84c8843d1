import { request } from "../../request";

// 视频专辑参数接口
interface VideoAlbumParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        user_id?: string;
        category_id?: string;
        status?: number;
        is_featured?: number;
        sort_by?: string;
    }
}

// 视频专辑数据接口
interface VideoAlbumData {
    id?: string;
    title: string;
    description?: string;
    user_id: string;
    user_nickname?: string;
    user_avatar?: string;
    cover?: string;
    category_id?: string;
    category_name?: string;
    tags?: string[];
    is_featured?: number;
    sort_order?: number;
    status?: number;
    [key: string]: any;
}

// 批量更新状态接口
interface BatchUpdateStatusData {
    ids: string[];
    status: number;
}

// 批量删除接口
interface BatchDeleteData {
    ids: string[];
}

/**
 * 获取视频专辑列表
 */
export function getVideoAlbumList(params?: VideoAlbumParams) {
    return request({
        url: '/video-albums/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取视频专辑详情
 */
export function getVideoAlbumDetail(id: string) {
    return request({
        url: `/video-albums/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    });
}

/**
 * 添加视频专辑
 */
export function addVideoAlbum(data: { data: VideoAlbumData }) {
    return request({
        url: '/video-albums/add',
        method: 'post',
        data: data
    });
}

/**
 * 更新视频专辑
 */
export function updateVideoAlbum(id: string, data: { data: VideoAlbumData }) {
    return request({
        url: `/video-albums/update/${id}`,
        method: 'post',
        data: data
    });
}

/**
 * 删除视频专辑
 */
export function deleteVideoAlbum(id: string) {
    return request({
        url: `/video-albums/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    });
}

/**
 * 更新视频专辑状态
 */
export function updateVideoAlbumStatus(id: string, status: number) {
    return request({
        url: `/video-albums/update-status/${id}`,
        method: 'post',
        data: { data: { status: status, id: id } }
    });
}

/**
 * 批量更新视频专辑状态
 */
export function batchUpdateVideoAlbumStatus(data: { data: BatchUpdateStatusData }) {
    return request({
        url: '/video-albums/batch-update-status',
        method: 'post',
        data: data
    });
}

/**
 * 批量删除视频专辑
 */
export function batchDeleteVideoAlbum(data: { data: BatchDeleteData }) {
    return request({
        url: '/video-albums/batch-delete',
        method: 'post',
        data: data
    });
} 