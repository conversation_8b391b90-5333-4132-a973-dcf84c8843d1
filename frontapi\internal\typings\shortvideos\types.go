package shortvideos

import (
	"frontapi/internal/typings"
)

type ShortVideoInfo struct {
	ID            string             `json:"id"`
	Title         string             `json:"title"`
	Description   string             `json:"description"`
	Cover         string             `json:"cover"`
	URL           string             `json:"url"`
	Duration      int                `json:"duration"`
	Resolution    string             `json:"resolution"`
	ViewCount     uint64             `json:"viewCount"`
	LikeCount     uint64             `json:"likeCount"`
	CommentCount  uint64             `json:"commentCount"`
	ShareCount    uint64             `json:"shareCount"`
	FavoriteCount uint64             `json:"favoriteCount"`
	CreatorID     string             `json:"creatorId"`
	CreatorName   string             `json:"creatorName"`
	CreatorAvatar string             `json:"creatorAvatar"`
	CategoryID    string             `json:"categoryId"`
	CategoryName  string             `json:"categoryName"`
	Tags          []string           `json:"tags"`
	IsPaid        int8               `json:"isPaid"`
	IsFeatured    int8               `json:"isFeatured"`
	Price         float64            `json:"price"`
	Status        int8               `json:"status"`
	IsLiked       bool               `json:"isLiked"`
	Author        typings.BaseAuthor `json:"author"`
}

type ShortVideoCommentInfo struct {
	UserID       string             `json:"userId"`       // 用户ID
	Content      string             `json:"content"`      // 评论内容
	UserNickname string             `json:"userNickname"` // 用户昵称
	UserAvatar   string             `json:"userAvatar"`   // 用户头像
	ParentID     string             `json:"parentId"`     // 父级ID
	EntityID     string             `json:"entityId"`     // 关联实体ID
	EntityType   int8               `json:"entityType"`   // 关联实体类型
	RelationID   string             `json:"relationId"`   // 关联ID
	Heat         int64              `json:"heat"`         // 热度
	LikeCount    int64              `json:"likeCount"`    // 点赞数
	ReplyCount   int64              `json:"replyCount"`   // 回复数
	CreatedAt    string             `json:"createdAt"`    // 创建时间
	UpdatedAt    string             `json:"updatedAt"`    // 更新时间
	Status       int8               `json:"status"`       // 状态：0-禁用，1-正常
	Author       typings.BaseAuthor `json:"author"`       // 作者

}
type ShortVideoListResponse struct {
	typings.BaseListResponse
	List []ShortVideoInfo `json:"list"`
}

type ShortVideoCommentListResponse struct {
	typings.BaseListResponse
	List []ShortVideoCommentInfo `json:"list"`
}
