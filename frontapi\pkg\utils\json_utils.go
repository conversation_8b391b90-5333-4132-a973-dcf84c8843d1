package utils

import (
	"bytes"
	"sync"

	jsoniter "github.com/json-iterator/go"
)

var (
	// 使用更快的JSON库
	myJson = jsoniter.ConfigCompatibleWithStandardLibrary
	// 编码器和解码器的对象池
	encoderPool = sync.Pool{
		New: func() interface{} {
			return myJson.NewEncoder(nil)
		},
	}
	decoderPool = sync.Pool{
		New: func() interface{} {
			return myJson.NewDecoder(nil)
		},
	}
)

// Marshal 高性能JSON序列化，使用对象池减少GC压力
func Marshal(v interface{}) ([]byte, error) {
	// 直接使用jsoniter内置的序列化方法
	return myJson.Marshal(v)
}

// Unmarshal 高性能JSON反序列化，使用对象池减少GC压力
func Unmarshal(data []byte, v interface{}) error {
	// 直接使用jsoniter内置的反序列化方法
	return myJson.Unmarshal(data, v)
}

// MarshalString 将对象序列化为JSON字符串
func MarshalString(v interface{}) (string, error) {
	data, err := Marshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// UnmarshalString 将JSON字符串反序列化为对象
func UnmarshalString(data string, v interface{}) error {
	return Unmarshal([]byte(data), v)
}

// MarshalToBytes 将对象序列化为字节数组，带预分配优化
func MarshalToBytes(v interface{}, estimatedSize int) ([]byte, error) {
	// 预分配缓冲区，减少内存分配
	buf := bytes.NewBuffer(make([]byte, 0, estimatedSize))

	// 获取一个新的编码器
	encoder := myJson.NewEncoder(buf)

	// 编码对象
	err := encoder.Encode(v)
	if err != nil {
		return nil, err
	}

	// 复制缓冲区数据以避免后续操作影响结果
	result := buf.Bytes()

	// 移除encoder添加的换行符
	if len(result) > 0 && result[len(result)-1] == '\n' {
		result = result[:len(result)-1]
	}

	return result, nil
}
