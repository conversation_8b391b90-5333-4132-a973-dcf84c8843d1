/**
 * 数学计算工具函数
 */

/**
 * 数学常量
 */
export const MATH_CONSTANTS = {
  PI: Math.PI,
  E: Math.E,
  LN2: Math.LN2,
  LN10: Math.LN10,
  LOG2E: Math.LOG2E,
  LOG10E: Math.LOG10E,
  SQRT1_2: Math.SQRT1_2,
  SQRT2: Math.SQRT2,
  GOLDEN_RATIO: (1 + Math.sqrt(5)) / 2, // 黄金比例
  EULER_GAMMA: 0.5772156649015329, // 欧拉常数
  CATALAN: 0.9159655941772190, // 卡塔兰常数
  KHINCHIN: 2.6854520010653064 // 欣钦常数
} as const

/**
 * 角度单位
 */
export type AngleUnit = 'degree' | 'radian' | 'gradian'

/**
 * 统计结果接口
 */
export interface Statistics {
  count: number
  sum: number
  mean: number
  median: number
  mode: number[]
  min: number
  max: number
  range: number
  variance: number
  standardDeviation: number
  skewness: number
  kurtosis: number
  quartiles: {
    q1: number
    q2: number
    q3: number
    iqr: number
  }
}

/**
 * 回归结果接口
 */
export interface RegressionResult {
  slope: number
  intercept: number
  rSquared: number
  equation: string
  predict: (x: number) => number
}

/**
 * 矩阵接口
 */
export interface Matrix {
  rows: number
  cols: number
  data: number[][]
}

/**
 * 复数接口
 */
export interface Complex {
  real: number
  imaginary: number
}

/**
 * 向量接口
 */
export interface Vector {
  components: number[]
  dimension: number
}

/**
 * 数学工具类
 */
export class MathUtils {
  /**
   * 精度常量
   */
  static readonly EPSILON = Number.EPSILON
  static readonly MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER
  static readonly MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER

  /**
   * 检查是否为数字
   */
  static isNumber(value: any): value is number {
    return typeof value === 'number' && !isNaN(value) && isFinite(value)
  }

  /**
   * 检查是否为整数
   */
  static isInteger(value: any): value is number {
    return Number.isInteger(value)
  }

  /**
   * 检查是否为正数
   */
  static isPositive(value: number): boolean {
    return this.isNumber(value) && value > 0
  }

  /**
   * 检查是否为负数
   */
  static isNegative(value: number): boolean {
    return this.isNumber(value) && value < 0
  }

  /**
   * 检查是否为零
   */
  static isZero(value: number, epsilon = this.EPSILON): boolean {
    return this.isNumber(value) && Math.abs(value) < epsilon
  }

  /**
   * 检查是否为偶数
   */
  static isEven(value: number): boolean {
    return this.isInteger(value) && value % 2 === 0
  }

  /**
   * 检查是否为奇数
   */
  static isOdd(value: number): boolean {
    return this.isInteger(value) && value % 2 !== 0
  }

  /**
   * 检查是否为质数
   */
  static isPrime(value: number): boolean {
    if (!this.isInteger(value) || value < 2) {
      return false
    }
    
    if (value === 2) {
      return true
    }
    
    if (value % 2 === 0) {
      return false
    }
    
    const sqrt = Math.sqrt(value)
    for (let i = 3; i <= sqrt; i += 2) {
      if (value % i === 0) {
        return false
      }
    }
    
    return true
  }

  /**
   * 数字精度处理
   */
  static precision(value: number, digits: number): number {
    if (!this.isNumber(value)) {
      return NaN
    }
    
    const factor = Math.pow(10, digits)
    return Math.round(value * factor) / factor
  }

  /**
   * 四舍五入到指定小数位
   */
  static round(value: number, digits = 0): number {
    return this.precision(value, digits)
  }

  /**
   * 向上取整到指定小数位
   */
  static ceil(value: number, digits = 0): number {
    if (!this.isNumber(value)) {
      return NaN
    }
    
    const factor = Math.pow(10, digits)
    return Math.ceil(value * factor) / factor
  }

  /**
   * 向下取整到指定小数位
   */
  static floor(value: number, digits = 0): number {
    if (!this.isNumber(value)) {
      return NaN
    }
    
    const factor = Math.pow(10, digits)
    return Math.floor(value * factor) / factor
  }

  /**
   * 截断到指定小数位
   */
  static trunc(value: number, digits = 0): number {
    if (!this.isNumber(value)) {
      return NaN
    }
    
    const factor = Math.pow(10, digits)
    return Math.trunc(value * factor) / factor
  }

  /**
   * 限制数值在指定范围内
   */
  static clamp(value: number, min: number, max: number): number {
    if (!this.isNumber(value) || !this.isNumber(min) || !this.isNumber(max)) {
      return NaN
    }
    
    return Math.min(Math.max(value, min), max)
  }

  /**
   * 线性插值
   */
  static lerp(start: number, end: number, t: number): number {
    if (!this.isNumber(start) || !this.isNumber(end) || !this.isNumber(t)) {
      return NaN
    }
    
    return start + (end - start) * t
  }

  /**
   * 反向线性插值
   */
  static inverseLerp(start: number, end: number, value: number): number {
    if (!this.isNumber(start) || !this.isNumber(end) || !this.isNumber(value)) {
      return NaN
    }
    
    if (start === end) {
      return 0
    }
    
    return (value - start) / (end - start)
  }

  /**
   * 数值映射
   */
  static map(value: number, fromMin: number, fromMax: number, toMin: number, toMax: number): number {
    const t = this.inverseLerp(fromMin, fromMax, value)
    return this.lerp(toMin, toMax, t)
  }

  /**
   * 计算百分比
   */
  static percentage(value: number, total: number, digits = 2): number {
    if (!this.isNumber(value) || !this.isNumber(total) || total === 0) {
      return NaN
    }
    
    return this.round((value / total) * 100, digits)
  }

  /**
   * 计算增长率
   */
  static growthRate(oldValue: number, newValue: number, digits = 2): number {
    if (!this.isNumber(oldValue) || !this.isNumber(newValue) || oldValue === 0) {
      return NaN
    }
    
    return this.round(((newValue - oldValue) / oldValue) * 100, digits)
  }

  /**
   * 计算平均值
   */
  static average(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return NaN
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return NaN
    }
    
    return validNumbers.reduce((sum, num) => sum + num, 0) / validNumbers.length
  }

  /**
   * 计算加权平均值
   */
  static weightedAverage(values: number[], weights: number[]): number {
    if (!Array.isArray(values) || !Array.isArray(weights) || values.length !== weights.length) {
      return NaN
    }
    
    let weightedSum = 0
    let totalWeight = 0
    
    for (let i = 0; i < values.length; i++) {
      if (this.isNumber(values[i]) && this.isNumber(weights[i])) {
        weightedSum += values[i] * weights[i]
        totalWeight += weights[i]
      }
    }
    
    return totalWeight === 0 ? NaN : weightedSum / totalWeight
  }

  /**
   * 计算中位数
   */
  static median(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return NaN
    }
    
    const validNumbers = numbers.filter(this.isNumber).sort((a, b) => a - b)
    if (validNumbers.length === 0) {
      return NaN
    }
    
    const mid = Math.floor(validNumbers.length / 2)
    
    if (validNumbers.length % 2 === 0) {
      return (validNumbers[mid - 1] + validNumbers[mid]) / 2
    } else {
      return validNumbers[mid]
    }
  }

  /**
   * 计算众数
   */
  static mode(numbers: number[]): number[] {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return []
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return []
    }
    
    const frequency: Record<number, number> = {}
    let maxFreq = 0
    
    for (const num of validNumbers) {
      frequency[num] = (frequency[num] || 0) + 1
      maxFreq = Math.max(maxFreq, frequency[num])
    }
    
    return Object.keys(frequency)
      .filter(key => frequency[Number(key)] === maxFreq)
      .map(Number)
  }

  /**
   * 计算方差
   */
  static variance(numbers: number[], sample = false): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return NaN
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return NaN
    }
    
    const mean = this.average(validNumbers)
    const squaredDiffs = validNumbers.map(num => Math.pow(num - mean, 2))
    const divisor = sample ? validNumbers.length - 1 : validNumbers.length
    
    return divisor === 0 ? NaN : squaredDiffs.reduce((sum, diff) => sum + diff, 0) / divisor
  }

  /**
   * 计算标准差
   */
  static standardDeviation(numbers: number[], sample = false): number {
    const variance = this.variance(numbers, sample)
    return isNaN(variance) ? NaN : Math.sqrt(variance)
  }

  /**
   * 计算偏度
   */
  static skewness(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length < 3) {
      return NaN
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length < 3) {
      return NaN
    }
    
    const mean = this.average(validNumbers)
    const std = this.standardDeviation(validNumbers)
    
    if (std === 0) {
      return NaN
    }
    
    const n = validNumbers.length
    const sum = validNumbers.reduce((acc, num) => acc + Math.pow((num - mean) / std, 3), 0)
    
    return (n / ((n - 1) * (n - 2))) * sum
  }

  /**
   * 计算峰度
   */
  static kurtosis(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length < 4) {
      return NaN
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length < 4) {
      return NaN
    }
    
    const mean = this.average(validNumbers)
    const std = this.standardDeviation(validNumbers)
    
    if (std === 0) {
      return NaN
    }
    
    const n = validNumbers.length
    const sum = validNumbers.reduce((acc, num) => acc + Math.pow((num - mean) / std, 4), 0)
    
    return ((n * (n + 1)) / ((n - 1) * (n - 2) * (n - 3))) * sum - (3 * Math.pow(n - 1, 2)) / ((n - 2) * (n - 3))
  }

  /**
   * 计算四分位数
   */
  static quartiles(numbers: number[]): { q1: number; q2: number; q3: number; iqr: number } {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return { q1: NaN, q2: NaN, q3: NaN, iqr: NaN }
    }
    
    const validNumbers = numbers.filter(this.isNumber).sort((a, b) => a - b)
    if (validNumbers.length === 0) {
      return { q1: NaN, q2: NaN, q3: NaN, iqr: NaN }
    }
    
    const q2 = this.median(validNumbers)
    const mid = Math.floor(validNumbers.length / 2)
    
    const lowerHalf = validNumbers.slice(0, mid)
    const upperHalf = validNumbers.slice(validNumbers.length % 2 === 0 ? mid : mid + 1)
    
    const q1 = this.median(lowerHalf)
    const q3 = this.median(upperHalf)
    const iqr = q3 - q1
    
    return { q1, q2, q3, iqr }
  }

  /**
   * 计算完整统计信息
   */
  static statistics(numbers: number[]): Statistics {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      const nan = NaN
      return {
        count: 0,
        sum: nan,
        mean: nan,
        median: nan,
        mode: [],
        min: nan,
        max: nan,
        range: nan,
        variance: nan,
        standardDeviation: nan,
        skewness: nan,
        kurtosis: nan,
        quartiles: { q1: nan, q2: nan, q3: nan, iqr: nan }
      }
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    const count = validNumbers.length
    
    if (count === 0) {
      const nan = NaN
      return {
        count: 0,
        sum: nan,
        mean: nan,
        median: nan,
        mode: [],
        min: nan,
        max: nan,
        range: nan,
        variance: nan,
        standardDeviation: nan,
        skewness: nan,
        kurtosis: nan,
        quartiles: { q1: nan, q2: nan, q3: nan, iqr: nan }
      }
    }
    
    const sum = validNumbers.reduce((acc, num) => acc + num, 0)
    const mean = this.average(validNumbers)
    const median = this.median(validNumbers)
    const mode = this.mode(validNumbers)
    const min = Math.min(...validNumbers)
    const max = Math.max(...validNumbers)
    const range = max - min
    const variance = this.variance(validNumbers)
    const standardDeviation = this.standardDeviation(validNumbers)
    const skewness = this.skewness(validNumbers)
    const kurtosis = this.kurtosis(validNumbers)
    const quartiles = this.quartiles(validNumbers)
    
    return {
      count,
      sum,
      mean,
      median,
      mode,
      min,
      max,
      range,
      variance,
      standardDeviation,
      skewness,
      kurtosis,
      quartiles
    }
  }

  /**
   * 线性回归
   */
  static linearRegression(xValues: number[], yValues: number[]): RegressionResult {
    if (!Array.isArray(xValues) || !Array.isArray(yValues) || xValues.length !== yValues.length || xValues.length < 2) {
      throw new Error('Invalid input for linear regression')
    }
    
    const n = xValues.length
    const sumX = xValues.reduce((sum, x) => sum + x, 0)
    const sumY = yValues.reduce((sum, y) => sum + y, 0)
    const sumXY = xValues.reduce((sum, x, i) => sum + x * yValues[i], 0)
    const sumXX = xValues.reduce((sum, x) => sum + x * x, 0)
    const sumYY = yValues.reduce((sum, y) => sum + y * y, 0)
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
    const intercept = (sumY - slope * sumX) / n
    
    // 计算R²
    const meanY = sumY / n
    const ssTotal = yValues.reduce((sum, y) => sum + Math.pow(y - meanY, 2), 0)
    const ssResidual = yValues.reduce((sum, y, i) => {
      const predicted = slope * xValues[i] + intercept
      return sum + Math.pow(y - predicted, 2)
    }, 0)
    
    const rSquared = 1 - (ssResidual / ssTotal)
    
    return {
      slope,
      intercept,
      rSquared,
      equation: `y = ${this.round(slope, 4)}x + ${this.round(intercept, 4)}`,
      predict: (x: number) => slope * x + intercept
    }
  }

  /**
   * 角度转换：度转弧度
   */
  static degreeToRadian(degree: number): number {
    return (degree * Math.PI) / 180
  }

  /**
   * 角度转换：弧度转度
   */
  static radianToDegree(radian: number): number {
    return (radian * 180) / Math.PI
  }

  /**
   * 角度转换：度转梯度
   */
  static degreeToGradian(degree: number): number {
    return (degree * 400) / 360
  }

  /**
   * 角度转换：梯度转度
   */
  static gradianToDegree(gradian: number): number {
    return (gradian * 360) / 400
  }

  /**
   * 角度转换：弧度转梯度
   */
  static radianToGradian(radian: number): number {
    return (radian * 200) / Math.PI
  }

  /**
   * 角度转换：梯度转弧度
   */
  static gradianToRadian(gradian: number): number {
    return (gradian * Math.PI) / 200
  }

  /**
   * 通用角度转换
   */
  static convertAngle(value: number, from: AngleUnit, to: AngleUnit): number {
    if (from === to) {
      return value
    }
    
    // 先转换为弧度
    let radian: number
    switch (from) {
      case 'degree':
        radian = this.degreeToRadian(value)
        break
      case 'gradian':
        radian = this.gradianToRadian(value)
        break
      default:
        radian = value
    }
    
    // 再转换为目标单位
    switch (to) {
      case 'degree':
        return this.radianToDegree(radian)
      case 'gradian':
        return this.radianToGradian(radian)
      default:
        return radian
    }
  }

  /**
   * 计算最大公约数
   */
  static gcd(a: number, b: number): number {
    if (!this.isInteger(a) || !this.isInteger(b)) {
      throw new Error('GCD requires integers')
    }
    
    a = Math.abs(a)
    b = Math.abs(b)
    
    while (b !== 0) {
      const temp = b
      b = a % b
      a = temp
    }
    
    return a
  }

  /**
   * 计算最小公倍数
   */
  static lcm(a: number, b: number): number {
    if (!this.isInteger(a) || !this.isInteger(b)) {
      throw new Error('LCM requires integers')
    }
    
    return Math.abs(a * b) / this.gcd(a, b)
  }

  /**
   * 计算阶乘
   */
  static factorial(n: number): number {
    if (!this.isInteger(n) || n < 0) {
      throw new Error('Factorial requires non-negative integer')
    }
    
    if (n === 0 || n === 1) {
      return 1
    }
    
    let result = 1
    for (let i = 2; i <= n; i++) {
      result *= i
    }
    
    return result
  }

  /**
   * 计算组合数 C(n, k)
   */
  static combination(n: number, k: number): number {
    if (!this.isInteger(n) || !this.isInteger(k) || n < 0 || k < 0 || k > n) {
      throw new Error('Invalid parameters for combination')
    }
    
    if (k === 0 || k === n) {
      return 1
    }
    
    // 优化：C(n, k) = C(n, n-k)
    k = Math.min(k, n - k)
    
    let result = 1
    for (let i = 0; i < k; i++) {
      result = result * (n - i) / (i + 1)
    }
    
    return Math.round(result)
  }

  /**
   * 计算排列数 P(n, k)
   */
  static permutation(n: number, k: number): number {
    if (!this.isInteger(n) || !this.isInteger(k) || n < 0 || k < 0 || k > n) {
      throw new Error('Invalid parameters for permutation')
    }
    
    let result = 1
    for (let i = 0; i < k; i++) {
      result *= (n - i)
    }
    
    return result
  }

  /**
   * 计算斐波那契数列第n项
   */
  static fibonacci(n: number): number {
    if (!this.isInteger(n) || n < 0) {
      throw new Error('Fibonacci requires non-negative integer')
    }
    
    if (n <= 1) {
      return n
    }
    
    let a = 0
    let b = 1
    
    for (let i = 2; i <= n; i++) {
      const temp = a + b
      a = b
      b = temp
    }
    
    return b
  }

  /**
   * 生成斐波那契数列
   */
  static fibonacciSequence(count: number): number[] {
    if (!this.isInteger(count) || count < 0) {
      throw new Error('Count must be non-negative integer')
    }
    
    const sequence: number[] = []
    
    for (let i = 0; i < count; i++) {
      sequence.push(this.fibonacci(i))
    }
    
    return sequence
  }

  /**
   * 生成质数列表
   */
  static generatePrimes(limit: number): number[] {
    if (!this.isInteger(limit) || limit < 2) {
      return []
    }
    
    const primes: number[] = []
    const isPrime = new Array(limit + 1).fill(true)
    isPrime[0] = isPrime[1] = false
    
    for (let i = 2; i <= limit; i++) {
      if (isPrime[i]) {
        primes.push(i)
        
        // 标记所有倍数为非质数
        for (let j = i * i; j <= limit; j += i) {
          isPrime[j] = false
        }
      }
    }
    
    return primes
  }

  /**
   * 计算数字的各位数字之和
   */
  static digitalRoot(n: number): number {
    if (!this.isInteger(n) || n < 0) {
      throw new Error('Digital root requires non-negative integer')
    }
    
    while (n >= 10) {
      let sum = 0
      while (n > 0) {
        sum += n % 10
        n = Math.floor(n / 10)
      }
      n = sum
    }
    
    return n
  }

  /**
   * 检查是否为完全数
   */
  static isPerfectNumber(n: number): boolean {
    if (!this.isInteger(n) || n <= 1) {
      return false
    }
    
    let sum = 1
    const sqrt = Math.sqrt(n)
    
    for (let i = 2; i <= sqrt; i++) {
      if (n % i === 0) {
        sum += i
        if (i !== sqrt) {
          sum += n / i
        }
      }
    }
    
    return sum === n
  }

  /**
   * 计算数字的因数
   */
  static getFactors(n: number): number[] {
    if (!this.isInteger(n) || n <= 0) {
      return []
    }
    
    const factors: number[] = []
    const sqrt = Math.sqrt(n)
    
    for (let i = 1; i <= sqrt; i++) {
      if (n % i === 0) {
        factors.push(i)
        if (i !== sqrt) {
          factors.push(n / i)
        }
      }
    }
    
    return factors.sort((a, b) => a - b)
  }

  /**
   * 生成随机数
   */
  static random(min = 0, max = 1): number {
    return Math.random() * (max - min) + min
  }

  /**
   * 生成随机整数
   */
  static randomInt(min: number, max: number): number {
    return Math.floor(this.random(min, max + 1))
  }

  /**
   * 生成随机数组
   */
  static randomArray(length: number, min = 0, max = 1): number[] {
    return Array.from({ length }, () => this.random(min, max))
  }

  /**
   * 洗牌算法
   */
  static shuffle<T>(array: T[]): T[] {
    const result = [...array]
    
    for (let i = result.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[result[i], result[j]] = [result[j], result[i]]
    }
    
    return result
  }

  /**
   * 计算两点间距离
   */
  static distance(x1: number, y1: number, x2: number, y2: number): number {
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
  }

  /**
   * 计算多维空间中两点间距离
   */
  static euclideanDistance(point1: number[], point2: number[]): number {
    if (point1.length !== point2.length) {
      throw new Error('Points must have the same dimension')
    }
    
    let sum = 0
    for (let i = 0; i < point1.length; i++) {
      sum += Math.pow(point2[i] - point1[i], 2)
    }
    
    return Math.sqrt(sum)
  }

  /**
   * 计算曼哈顿距离
   */
  static manhattanDistance(point1: number[], point2: number[]): number {
    if (point1.length !== point2.length) {
      throw new Error('Points must have the same dimension')
    }
    
    let sum = 0
    for (let i = 0; i < point1.length; i++) {
      sum += Math.abs(point2[i] - point1[i])
    }
    
    return sum
  }

  /**
   * 计算余弦相似度
   */
  static cosineSimilarity(vector1: number[], vector2: number[]): number {
    if (vector1.length !== vector2.length) {
      throw new Error('Vectors must have the same dimension')
    }
    
    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0
    
    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i]
      norm1 += vector1[i] * vector1[i]
      norm2 += vector2[i] * vector2[i]
    }
    
    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }
}

/**
 * 复数运算类
 */
export class ComplexNumber {
  constructor(public real: number, public imaginary: number) {}

  /**
   * 加法
   */
  add(other: ComplexNumber): ComplexNumber {
    return new ComplexNumber(
      this.real + other.real,
      this.imaginary + other.imaginary
    )
  }

  /**
   * 减法
   */
  subtract(other: ComplexNumber): ComplexNumber {
    return new ComplexNumber(
      this.real - other.real,
      this.imaginary - other.imaginary
    )
  }

  /**
   * 乘法
   */
  multiply(other: ComplexNumber): ComplexNumber {
    return new ComplexNumber(
      this.real * other.real - this.imaginary * other.imaginary,
      this.real * other.imaginary + this.imaginary * other.real
    )
  }

  /**
   * 除法
   */
  divide(other: ComplexNumber): ComplexNumber {
    const denominator = other.real * other.real + other.imaginary * other.imaginary
    
    if (denominator === 0) {
      throw new Error('Division by zero')
    }
    
    return new ComplexNumber(
      (this.real * other.real + this.imaginary * other.imaginary) / denominator,
      (this.imaginary * other.real - this.real * other.imaginary) / denominator
    )
  }

  /**
   * 共轭
   */
  conjugate(): ComplexNumber {
    return new ComplexNumber(this.real, -this.imaginary)
  }

  /**
   * 模长
   */
  magnitude(): number {
    return Math.sqrt(this.real * this.real + this.imaginary * this.imaginary)
  }

  /**
   * 幅角
   */
  argument(): number {
    return Math.atan2(this.imaginary, this.real)
  }

  /**
   * 转换为字符串
   */
  toString(): string {
    if (this.imaginary === 0) {
      return this.real.toString()
    }
    
    if (this.real === 0) {
      return `${this.imaginary}i`
    }
    
    const sign = this.imaginary >= 0 ? '+' : '-'
    const imagPart = Math.abs(this.imaginary)
    
    return `${this.real} ${sign} ${imagPart}i`
  }

  /**
   * 从极坐标创建复数
   */
  static fromPolar(magnitude: number, argument: number): ComplexNumber {
    return new ComplexNumber(
      magnitude * Math.cos(argument),
      magnitude * Math.sin(argument)
    )
  }
}

/**
 * 矩阵运算类
 */
export class MatrixUtils {
  /**
   * 创建矩阵
   */
  static create(rows: number, cols: number, fill = 0): Matrix {
    const data: number[][] = []
    
    for (let i = 0; i < rows; i++) {
      data[i] = new Array(cols).fill(fill)
    }
    
    return { rows, cols, data }
  }

  /**
   * 创建单位矩阵
   */
  static identity(size: number): Matrix {
    const matrix = this.create(size, size, 0)
    
    for (let i = 0; i < size; i++) {
      matrix.data[i][i] = 1
    }
    
    return matrix
  }

  /**
   * 矩阵加法
   */
  static add(a: Matrix, b: Matrix): Matrix {
    if (a.rows !== b.rows || a.cols !== b.cols) {
      throw new Error('Matrices must have the same dimensions')
    }
    
    const result = this.create(a.rows, a.cols)
    
    for (let i = 0; i < a.rows; i++) {
      for (let j = 0; j < a.cols; j++) {
        result.data[i][j] = a.data[i][j] + b.data[i][j]
      }
    }
    
    return result
  }

  /**
   * 矩阵减法
   */
  static subtract(a: Matrix, b: Matrix): Matrix {
    if (a.rows !== b.rows || a.cols !== b.cols) {
      throw new Error('Matrices must have the same dimensions')
    }
    
    const result = this.create(a.rows, a.cols)
    
    for (let i = 0; i < a.rows; i++) {
      for (let j = 0; j < a.cols; j++) {
        result.data[i][j] = a.data[i][j] - b.data[i][j]
      }
    }
    
    return result
  }

  /**
   * 矩阵乘法
   */
  static multiply(a: Matrix, b: Matrix): Matrix {
    if (a.cols !== b.rows) {
      throw new Error('Number of columns in first matrix must equal number of rows in second matrix')
    }
    
    const result = this.create(a.rows, b.cols)
    
    for (let i = 0; i < a.rows; i++) {
      for (let j = 0; j < b.cols; j++) {
        let sum = 0
        for (let k = 0; k < a.cols; k++) {
          sum += a.data[i][k] * b.data[k][j]
        }
        result.data[i][j] = sum
      }
    }
    
    return result
  }

  /**
   * 矩阵转置
   */
  static transpose(matrix: Matrix): Matrix {
    const result = this.create(matrix.cols, matrix.rows)
    
    for (let i = 0; i < matrix.rows; i++) {
      for (let j = 0; j < matrix.cols; j++) {
        result.data[j][i] = matrix.data[i][j]
      }
    }
    
    return result
  }

  /**
   * 计算行列式（仅适用于方阵）
   */
  static determinant(matrix: Matrix): number {
    if (matrix.rows !== matrix.cols) {
      throw new Error('Determinant can only be calculated for square matrices')
    }
    
    const n = matrix.rows
    
    if (n === 1) {
      return matrix.data[0][0]
    }
    
    if (n === 2) {
      return matrix.data[0][0] * matrix.data[1][1] - matrix.data[0][1] * matrix.data[1][0]
    }
    
    let det = 0
    
    for (let j = 0; j < n; j++) {
      const subMatrix = this.create(n - 1, n - 1)
      
      for (let i = 1; i < n; i++) {
        let colIndex = 0
        for (let k = 0; k < n; k++) {
          if (k !== j) {
            subMatrix.data[i - 1][colIndex] = matrix.data[i][k]
            colIndex++
          }
        }
      }
      
      det += Math.pow(-1, j) * matrix.data[0][j] * this.determinant(subMatrix)
    }
    
    return det
  }
}

// 导出工具实例
export const mathUtils = MathUtils
export const matrixUtils = MatrixUtils

// 导出快捷方法
export const {
  isNumber,
  isInteger,
  isPositive,
  isNegative,
  isZero,
  isEven,
  isOdd,
  isPrime,
  precision,
  round,
  ceil,
  floor,
  trunc,
  clamp,
  lerp,
  inverseLerp,
  map,
  percentage,
  growthRate,
  average,
  weightedAverage,
  median,
  mode,
  variance,
  standardDeviation,
  skewness,
  kurtosis,
  quartiles,
  statistics,
  linearRegression,
  degreeToRadian,
  radianToDegree,
  convertAngle,
  gcd,
  lcm,
  factorial,
  combination,
  permutation,
  fibonacci,
  fibonacciSequence,
  generatePrimes,
  digitalRoot,
  isPerfectNumber,
  getFactors,
  random,
  randomInt,
  randomArray,
  shuffle,
  distance,
  euclideanDistance,
  manhattanDistance,
  cosineSimilarity
} = MathUtils