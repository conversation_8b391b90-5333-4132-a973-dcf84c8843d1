package shortvideos

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/shortvideos"

	"github.com/gofiber/fiber/v2"
)

// ShortVideoCategoryController 短视频控制器
type ShortVideoCategoryController struct {
	ShortVideoService         shortvideos.ShortVideoService
	ShortVideoCategoryService shortvideos.ShortVideoCategoryService
	admin.BaseController      // 继承BaseController
}

// NewShortVideoCategoryController 创建短视频控制器实例
func NewShortVideoCategoryController(
	shortVideoCategoryService shortvideos.ShortVideoCategoryService,
	shortVideoService shortvideos.ShortVideoService,
) *ShortVideoCategoryController {
	return &ShortVideoCategoryController{
		ShortVideoService:         shortVideoService,
		ShortVideoCategoryService: shortVideoCategoryService,
	}
}

// GetShortVideoCategories 获取短视频分类列表
func (c *ShortVideoCategoryController) GetShortVideoCategories(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	keyword := reqInfo.Get("keyword").GetString()
	status := reqInfo.Get("status").GetInt()
	condition := map[string]interface{}{
		"keyword": keyword,
		"status":  status,
	}
	// 查询短视频分类列表
	categoryList, total, err := c.ShortVideoCategoryService.List(ctx.Context(), condition, "created_at DESC", page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取短视频分类列表失败: "+err.Error())
	}

	// 返回短视频分类列表
	return c.SuccessList(ctx, categoryList, total, page, pageSize)
}
