<template>
  <el-tag :type="tagType" :effect="effect">
    {{ statusText }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: Number,
    required: true
  },
  effect: {
    type: String,
    default: 'light'
  }
});

// 根据状态计算标签类型
const tagType = computed(() => {
  switch (props.status) {
    case 1: return 'success';
    case 0: return 'danger';
    default: return 'info';
  }
});

// 根据状态计算显示文本
const statusText = computed(() => {
  switch (props.status) {
    case 1: return '启用';
    case 0: return '禁用';
    default: return '未知';
  }
});
</script> 