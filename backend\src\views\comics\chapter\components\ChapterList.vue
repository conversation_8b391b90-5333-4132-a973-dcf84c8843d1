<template>
  <div class="chapter-list-container">
    <div v-if="isSortMode" class="sort-mode-tip">
      <el-alert
        title="章节排序模式"
        type="info"
        description="您可以通过拖动章节序号列中的拖动手柄图标来调整章节顺序。完成后请点击'保存排序'按钮。"
        show-icon
        :closable="false"
      />
    </div>

    <DraggableSortTable
      ref="sortTableRef"
      v-if="isSortMode"
      :table-data="sortableChapterList"
      :draggable="true"
      @row-sorted="onRowSorted"
      :loading="loading"
      :show-pagination="false"
    >
      <el-table-column type="index" width="55" label="#" />
      <el-table-column prop="title" label="章节标题" min-width="180" />
      <DraggableSortColumn prop="chapter_number" label="章节序号" width="80" align="center">
        <template #default="{ row }">
          <div class="chapter-drag-handle">
            <el-icon class="drag-icon"><Rank /></el-icon>
            <span>{{ row.chapter_number }}</span>
          </div>
        </template>
      </DraggableSortColumn>
      <el-table-column prop="read_count" label="阅读数" width="100" align="center" />
      <el-table-column prop="is_locked" label="付费" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_locked === 1 ? 'danger' : 'success'">
            {{ row.is_locked === 1 ? '付费' : '免费' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" width="100" align="center">
        <template #default="{ row }">
          {{ row.is_locked === 1 ? `¥${row.price}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="page_count" label="页数" width="80" align="center" />
      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
    </DraggableSortTable>

    <!-- 常规模式下的表格 -->
    <el-table
      v-else
      v-loading="loading"
      :data="chapterList"
      border
      :highlight-current-row="false"
      style="width: 100%"
      row-key="id"
    >
      <el-table-column type="index" width="55" label="#"></el-table-column>
      <el-table-column prop="title" label="章节标题" min-width="180" />
      <el-table-column prop="chapter_number" label="章节序号" width="80" align="center" />
      <el-table-column prop="read_count" label="阅读数" width="100" align="center" />
      <el-table-column prop="is_locked" label="付费" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_locked === 1 ? 'danger' : 'success'">
            {{ row.is_locked === 1 ? '付费' : '免费' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" width="100" align="center">
        <template #default="{ row }">
          {{ row.is_locked === 1 ? `¥${row.price}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="page_count" label="页数" width="80" align="center" />
      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="170" align="center">
        <template #default="{ row }">
          {{ formatDate(row.create_time) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="onEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="managePage(row)">管理页面</el-button>
          <el-button type="danger" link @click="onDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="!isSortMode" class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, onMounted, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Rank } from '@element-plus/icons-vue';
import { DraggableSortTable, DraggableSortColumn } from '@/components/DraggableSortTable';
import { deleteComicsChapter, updateComicsChapter } from '@/service/api/comics/comics';
import type { ComicsChapter } from '@/types/comics';

interface Props {
  loading: boolean;
  chapterList: ComicsChapter[];
  sortableChapterList: ComicsChapter[];
  comicsId: string;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
  isSortMode: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits([
  'update:pagination',
  'update:sortableChapterList',
  'edit',
  'delete',
  'refresh',
  'size-change',
  'page-change',
  'order-change'
]);

const router = useRouter();
const isOrderChanged = ref(false);

// 添加表格引用
const sortTableRef = ref<any>(null);

// 监听排序模式变化
watch(() => props.isSortMode, async (newVal) => {
  if (newVal) {
    // 在DOM更新后刷新排序表格
    await nextTick();
    if (sortTableRef.value) {
      console.log('刷新排序表格');
      sortTableRef.value.refreshSortable();
    }
  }
});

// 拖拽排序处理
const onRowSorted = (evt: any) => {
  const { data } = evt;
  
  // 更新序号
  const newList = data.map((item: any, index: number) => ({
    ...item,
    chapter_number: index + 1
  }));
  
  // 触发父组件更新
  emit('update:sortableChapterList', newList);
  emit('order-change', true);
  isOrderChanged.value = true;
};

// 管理章节页面
const managePage = (row: ComicsChapter) => {
  router.push(`/comics/${row.comic_id}/chapter/${row.id}/page`);
};

// 编辑章节
const onEdit = (row: ComicsChapter) => {
  emit('edit', row);
};

// 删除章节
const onDelete = async (row: ComicsChapter) => {
  try {
    await ElMessageBox.confirm('确定要删除该章节吗？', '提示', { type: 'warning' });
    const res = await deleteComicsChapter(row.id) as any;
    const {response, data} = res as any;
    if (response.data.code === 2000) {
      ElMessage.success('删除成功');
      emit('refresh');
    } else {
      ElMessage.error(response?.data?.message || '删除失败');
    }
  } catch (e) {}
};

// 分页处理
const onSizeChange = (size: number) => {
  emit('size-change', size);
};

const onPageChange = (page: number) => {
  emit('page-change', page);
};

// 日期格式化
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 8);
};
</script>

<style scoped lang="scss">
.chapter-list-container {
  width: 100%;
}

.sort-mode-tip {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.chapter-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  background-color: #f0f9ff;
  border-radius: 4px;
  padding: 4px 8px;
  
  .drag-icon {
    color: #409eff;
    margin-right: 5px;
    font-size: 16px;
  }
  
  &:hover {
    background-color: #ecf5ff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    cursor: grabbing;
  }
}

:deep(.el-table .hover-row) {
  background-color: var(--el-color-primary-light-9);
}
</style> 