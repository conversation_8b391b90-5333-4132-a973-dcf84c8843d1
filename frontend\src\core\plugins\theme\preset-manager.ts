/**
 * PrimeVue 4 主题预设管理器
 */
import { App } from 'vue';
import { usePrimeVue } from 'primevue/config';
import { getPresetByThemeCode } from '@/shared/themes/presets/definePresets';
import { THEME_COLORS } from '@/config/theme.config';

// 主题预设管理器类
export class ThemePresetManager {
    private app: App | null = null;
    private primevue: any = null;

    constructor(app?: App) {
        if (app) {
            this.initialize(app);
        }
    }

    /**
     * 初始化主题预设管理器
     */
    initialize(app: App) {
        this.app = app;
        // 在组件挂载后获取PrimeVue实例
        setTimeout(() => {
            try {
                this.primevue = usePrimeVue();
                console.log('[Theme Preset Manager] Initialized successfully');
            } catch (error) {
                console.warn('[Theme Preset Manager] Failed to get PrimeVue instance:', error);
            }
        }, 100);
    }

    /**
     * 切换主题预设
     */
    switchTheme(themeCode: string, isDark: boolean = false) {
        if (!this.primevue) {
            console.warn('[Theme Preset Manager] PrimeVue instance not available');
            return false;
        }

        try {
            // 获取对应的预设
            const preset = getPresetByThemeCode(themeCode);
            
            if (!preset) {
                console.warn(`[Theme Preset Manager] Preset not found for theme: ${themeCode}`);
                return false;
            }

            // 切换预设
            this.primevue.changeTheme(this.primevue.config.theme?.preset, preset, 'theme-link', () => {
                console.log(`[Theme Preset Manager] Theme switched to: ${themeCode}`);
                
                // 应用暗色模式类
                this.applyDarkModeClasses(isDark);
                
                // 应用自定义样式
                this.applyCustomStyles(themeCode, isDark);
                
                // 触发主题变更事件
                this.dispatchThemeChangedEvent(themeCode, isDark);
            });

            return true;
        } catch (error) {
            console.error('[Theme Preset Manager] Error switching theme:', error);
            return false;
        }
    }

    /**
     * 应用暗色模式类
     */
    private applyDarkModeClasses(isDark: boolean) {
        const root = document.documentElement;
        
        if (isDark) {
            root.classList.add('app-dark');
            root.classList.add('dark-theme');
        } else {
            root.classList.remove('app-dark');
            root.classList.remove('dark-theme');
        }
    }

    /**
     * 应用自定义样式（header、footer等）
     */
    private applyCustomStyles(themeCode: string, isDark: boolean) {
        // 从主题代码中提取颜色方案
        const colorScheme = this.extractColorSchemeFromThemeCode(themeCode);
        const colorConfig = THEME_COLORS[colorScheme as keyof typeof THEME_COLORS];
        
        if (!colorConfig) return;
        
        const root = document.documentElement;
        
        if (isDark) {
            // 暗色模式样式
            root.style.setProperty('--header-bg', colorConfig.header);
            root.style.setProperty('--header-text', colorConfig.headerText);
            root.style.setProperty('--footer-bg', colorConfig.footer);
            root.style.setProperty('--footer-text', colorConfig.footerText);
        } else {
            // 亮色模式样式
            root.style.setProperty('--header-bg', `rgba(${this.hexToRgb(colorConfig.light)}, 0.1)`);
            root.style.setProperty('--header-text', colorConfig.dark);
            root.style.setProperty('--footer-bg', `rgba(${this.hexToRgb(colorConfig.light)}, 0.05)`);
            root.style.setProperty('--footer-text', colorConfig.dark);
        }
    }

    /**
     * 从主题代码中提取颜色方案
     */
    private extractColorSchemeFromThemeCode(themeCode: string): string {
        // 移除主题家族和明暗模式前缀，获取颜色方案
        const match = themeCode.match(/(Light|Dark)(.+)$/);
        if (match && match[2]) {
            return match[2].toLowerCase();
        }
        return 'indigo'; // 默认颜色方案
    }

    /**
     * 将十六进制颜色转换为RGB
     */
    private hexToRgb(hex: string): string {
        hex = hex.replace('#', '');
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        return `${r}, ${g}, ${b}`;
    }

    /**
     * 触发主题变更事件
     */
    private dispatchThemeChangedEvent(themeCode: string, isDark: boolean) {
        window.dispatchEvent(new CustomEvent('theme-changed', {
            detail: { theme: themeCode, isDark }
        }));
    }

    /**
     * 获取当前主题预设
     */
    getCurrentPreset() {
        return this.primevue?.config?.theme?.preset || null;
    }

    /**
     * 检查是否已初始化
     */
    isInitialized(): boolean {
        return this.primevue !== null;
    }
}

// 创建全局实例
export const themePresetManager = new ThemePresetManager();

// 导出便捷函数
export function switchThemePreset(themeCode: string, isDark: boolean = false): boolean {
    return themePresetManager.switchTheme(themeCode, isDark);
}

export function initializeThemePresetManager(app: App) {
    themePresetManager.initialize(app);
}

export default themePresetManager;