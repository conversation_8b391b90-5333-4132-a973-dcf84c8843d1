/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

// 引入各个模块路由
import booksRoutes from './modules/books';
import comicsRoutes from './modules/comics';
import postsRoutes from './modules/posts';
import picturesRoutes from './modules/pictures';
import tagsRoutes from './modules/system';
import shortvideoRoutes from './modules/shortvideos';
import usersRoutes from './modules/users';
import videosRoutes from './modules/videos';
import homeRoutes from './modules/home';
import { routes as examplesRoutes } from './modules/examples';

// 定义生成的所有路由
export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true,
      order: 15
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true,
      order: 16
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true,
      order: 16
    }
  },
  {
    name: 'about',
    path: '/about',
    component: 'layout.base$view.about',
    meta: {
      title: 'about',
      i18nKey: 'route.about',
      icon: 'fluent:book-information-24-regular',
      order: 17
    }
  },
  {
    name: 'alova',
    path: '/alova',
    component: 'layout.base',
    meta: {
      title: 'alova',
      i18nKey: 'route.alova',
      icon: 'carbon:http',
      order: 12
    },
    children: [
      {
        name: 'alova_request',
        path: '/alova/request',
        component: 'view.alova_request',
        meta: {
          title: 'alova_request',
          i18nKey: 'route.alova_request',
          order: 1
        }
      },
      {
        name: 'alova_scenes',
        path: '/alova/scenes',
        component: 'view.alova_scenes',
        meta: {
          title: 'alova_scenes',
          i18nKey: 'route.alova_scenes',
          icon: 'cbi:scene-dynamic',
          order: 3
        }
      },
      {
        name: 'alova_user',
        path: '/alova/user',
        component: 'view.alova_user',
        meta: {
          title: 'alova_user',
          i18nKey: 'route.alova_user',
          icon: 'carbon:user-multiple',
          order: 2
        }
      }
    ]
  },
  {
    name: 'books',
    path: '/books',
    component: 'layout.base',
    meta: {
      title: 'books',
      i18nKey: 'route.books'
    },
    children: [
      {
        name: 'books_category',
        path: '/books/category',
        component: 'view.books_category',
        meta: {
          title: 'books_category',
          i18nKey: 'route.books_category'
        }
      },
      {
        name: 'books_chapter',
        path: '/books/chapter',
        component: 'view.books_chapter',
        meta: {
          title: 'books_chapter',
          i18nKey: 'route.books_chapter'
        }
      },
      {
        name: 'books_list',
        path: '/books/list',
        component: 'view.books_list',
        meta: {
          title: 'books_list',
          i18nKey: 'route.books_list'
        }
      }
    ]
  },
  {
    name: 'comics',
    path: '/comics',
    component: 'layout.base',
    meta: {
      title: 'comics',
      i18nKey: 'route.comics'
    },
    children: [
      {
        name: 'comics_category',
        path: '/comics/category',
        component: 'view.comics_category',
        meta: {
          title: 'comics_category',
          i18nKey: 'route.comics_category'
        }
      },
      {
        name: 'comics_chapter',
        path: '/comics/chapter',
        component: 'view.comics_chapter',
        meta: {
          title: 'comics_chapter',
          i18nKey: 'route.comics_chapter'
        }
      },
      {
        name: 'comics_list',
        path: '/comics/list',
        component: 'view.comics_list',
        meta: {
          title: 'comics_list',
          i18nKey: 'route.comics_list'
        }
      },
      {
        name: 'comics_page',
        path: '/comics/page',
        component: 'view.comics_page',
        meta: {
          title: 'comics_page',
          i18nKey: 'route.comics_page'
        }
      }
    ]
  },
  {
    name: 'function',
    path: '/function',
    component: 'layout.base',
    meta: {
      title: 'function',
      i18nKey: 'route.function',
      icon: 'icon-park-outline:all-application',
      order: 15
    },
    children: [
      {
        name: 'function_hide-child',
        path: '/function/hide-child',
        meta: {
          title: 'function_hide-child',
          i18nKey: 'route.function_hide-child',
          icon: 'material-symbols:filter-list-off',
          order: 2
        },
        redirect: '/function/hide-child/one',
        children: [
          {
            name: 'function_hide-child_one',
            path: '/function/hide-child/one',
            component: 'view.function_hide-child_one',
            meta: {
              title: 'function_hide-child_one',
              i18nKey: 'route.function_hide-child_one',
              icon: 'material-symbols:filter-list-off',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          },
          {
            name: 'function_hide-child_three',
            path: '/function/hide-child/three',
            component: 'view.function_hide-child_three',
            meta: {
              title: 'function_hide-child_three',
              i18nKey: 'route.function_hide-child_three',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          },
          {
            name: 'function_hide-child_two',
            path: '/function/hide-child/two',
            component: 'view.function_hide-child_two',
            meta: {
              title: 'function_hide-child_two',
              i18nKey: 'route.function_hide-child_two',
              hideInMenu: true,
              activeMenu: 'function_hide-child'
            }
          }
        ]
      },
      {
        name: 'function_multi-tab',
        path: '/function/multi-tab',
        component: 'view.function_multi-tab',
        meta: {
          title: 'function_multi-tab',
          i18nKey: 'route.function_multi-tab',
          icon: 'ic:round-tab',
          multiTab: true,
          hideInMenu: true,
          activeMenu: 'function_tab'
        }
      },
      {
        name: 'function_request',
        path: '/function/request',
        component: 'view.function_request',
        meta: {
          title: 'function_request',
          i18nKey: 'route.function_request',
          icon: 'carbon:network-overlay',
          order: 3
        }
      },
      {
        name: 'function_super-page',
        path: '/function/super-page',
        component: 'view.function_super-page',
        meta: {
          title: 'function_super-page',
          i18nKey: 'route.function_super-page',
          icon: 'ic:round-supervisor-account',
          order: 5,
          roles: ['R_SUPER']
        }
      },
      {
        name: 'function_tab',
        path: '/function/tab',
        component: 'view.function_tab',
        meta: {
          title: 'function_tab',
          i18nKey: 'route.function_tab',
          icon: 'ic:round-tab',
          order: 1
        }
      },
      {
        name: 'function_toggle-auth',
        path: '/function/toggle-auth',
        component: 'view.function_toggle-auth',
        meta: {
          title: 'function_toggle-auth',
          i18nKey: 'route.function_toggle-auth',
          icon: 'ic:round-construction',
          order: 4
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home'
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'integral',
    path: '/integral',
    component: 'layout.base',
    meta: {
      title: 'integral',
      i18nKey: 'route.integral',
      order: 10
    },
    children: [
      {
        name: 'integral_list',
        path: '/integral/list',
        component: 'view.integral_list',
        meta: {
          title: 'integral_list',
          i18nKey: 'route.integral_list'
        }
      }
    ]
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 9,
      roles: ['R_ADMIN']
    },
    children: [
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu',
          icon: 'material-symbols:route',
          order: 3,
          roles: ['R_ADMIN'],
          keepAlive: true
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role',
          icon: 'carbon:user-role',
          order: 2,
          roles: ['R_SUPER']
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user',
          icon: 'ic:round-manage-accounts',
          order: 1,
          roles: ['R_ADMIN']
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideInMenu: true,
          roles: ['R_ADMIN'],
          activeMenu: 'manage_user'
        }
      }
    ]
  },
  {
    name: 'multi-menu',
    path: '/multi-menu',
    component: 'layout.base',
    meta: {
      title: 'multi-menu',
      i18nKey: 'route.multi-menu',
      order: 14
    },
    children: [
      {
        name: 'multi-menu_first',
        path: '/multi-menu/first',
        meta: {
          title: 'multi-menu_first',
          i18nKey: 'route.multi-menu_first',
          order: 1
        },
        children: [
          {
            name: 'multi-menu_first_child',
            path: '/multi-menu/first/child',
            component: 'view.multi-menu_first_child',
            meta: {
              title: 'multi-menu_first_child',
              i18nKey: 'route.multi-menu_first_child'
            }
          }
        ]
      },
      {
        name: 'multi-menu_second',
        path: '/multi-menu/second',
        meta: {
          title: 'multi-menu_second',
          i18nKey: 'route.multi-menu_second',
          order: 2
        },
        children: [
          {
            name: 'multi-menu_second_child',
            path: '/multi-menu/second/child',
            meta: {
              title: 'multi-menu_second_child',
              i18nKey: 'route.multi-menu_second_child'
            },
            children: [
              {
                name: 'multi-menu_second_child_home',
                path: '/multi-menu/second/child/home',
                component: 'view.multi-menu_second_child_home',
                meta: {
                  title: 'multi-menu_second_child_home',
                  i18nKey: 'route.multi-menu_second_child_home'
                }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    name: 'pictures',
    path: '/pictures',
    component: 'layout.base',
    meta: {
      title: 'pictures',
      i18nKey: 'route.pictures'
    },
    children: [
      {
        name: 'pictures_album',
        path: '/pictures/album',
        component: 'view.pictures_album',
        meta: {
          title: 'pictures_album',
          i18nKey: 'route.pictures_album'
        }
      },
      {
        name: 'pictures_category',
        path: '/pictures/category',
        component: 'view.pictures_category',
        meta: {
          title: 'pictures_category',
          i18nKey: 'route.pictures_category'
        }
      },
      {
        name: 'pictures_list',
        path: '/pictures/list',
        component: 'view.pictures_list',
        meta: {
          title: 'pictures_list',
          i18nKey: 'route.pictures_list'
        }
      }
    ]
  },
  {
    name: 'plugin',
    path: '/plugin',
    component: 'layout.base',
    meta: {
      title: '插件示例',
      i18nKey: 'route.plugin',
      order: 11,
      icon: 'fluent:puzzle-piece-24-regular'
    },
    children: [
      {
        name: 'plugin_barcode',
        path: '/plugin/barcode',
        component: 'view.plugin_barcode',
        meta: {
          title: 'plugin_barcode',
          i18nKey: 'route.plugin_barcode'
        }
      },
      {
        name: 'plugin_charts',
        path: '/plugin/charts',
        component: 'view.plugin_charts',
        meta: {
          title: '图表',
          i18nKey: 'route.plugin_charts',
          icon: 'material-symbols:analytics-outline-rounded'
        },
        children: [
          {
            name: 'plugin_charts_antv',
            path: '/plugin/charts/antv',
            component: 'view.plugin_charts_antv',
            meta: {
              title: 'plugin_charts_antv',
              i18nKey: 'route.plugin_charts_antv'
            }
          },
          {
            name: 'plugin_charts_echarts',
            path: '/plugin/charts/echarts',
            component: 'view.plugin_charts_echarts',
            meta: {
              title: 'plugin_charts_echarts',
              i18nKey: 'route.plugin_charts_echarts'
            }
          },
          {
            name: 'plugin_charts_vchart',
            path: '/plugin/charts/vchart',
            component: 'view.plugin_charts_vchart',
            meta: {
              title: 'plugin_charts_vchart',
              i18nKey: 'route.plugin_charts_vchart'
            }
          }
        ]
      },
      {
        name: 'plugin_copy',
        path: '/plugin/copy',
        component: 'view.plugin_copy',
        meta: {
          title: '剪贴板',
          i18nKey: 'route.plugin_copy',
          icon: 'material-symbols:content-copy-outline-rounded'
        }
      },
      {
        name: 'plugin_editor',
        path: '/plugin/editor',
        component: 'view.plugin_editor',
        meta: {
          title: '编辑器',
          i18nKey: 'route.plugin_editor',
          icon: 'material-symbols:edit-document-outline-rounded'
        },
        children: [
          {
            name: 'plugin_editor_markdown',
            path: '/plugin/editor/markdown',
            component: 'view.plugin_editor_markdown',
            meta: {
              title: 'plugin_editor_markdown',
              i18nKey: 'route.plugin_editor_markdown'
            }
          },
          {
            name: 'plugin_editor_quill',
            path: '/plugin/editor/quill',
            component: 'view.plugin_editor_quill',
            meta: {
              title: 'plugin_editor_quill',
              i18nKey: 'route.plugin_editor_quill'
            }
          }
        ]
      },
      {
        name: 'plugin_excel',
        path: '/plugin/excel',
        component: 'view.plugin_excel',
        meta: {
          title: 'plugin_excel',
          i18nKey: 'route.plugin_excel'
        }
      },
      {
        name: 'plugin_gantt',
        path: '/plugin/gantt',
        meta: {
          title: 'plugin_gantt',
          i18nKey: 'route.plugin_gantt'
        },
        children: [
          {
            name: 'plugin_gantt_dhtmlx',
            path: '/plugin/gantt/dhtmlx',
            component: 'view.plugin_gantt_dhtmlx',
            meta: {
              title: 'plugin_gantt_dhtmlx',
              i18nKey: 'route.plugin_gantt_dhtmlx'
            }
          },
          {
            name: 'plugin_gantt_vtable',
            path: '/plugin/gantt/vtable',
            component: 'view.plugin_gantt_vtable',
            meta: {
              title: 'plugin_gantt_vtable',
              i18nKey: 'route.plugin_gantt_vtable'
            }
          }
        ]
      },
      {
        name: 'plugin_icon',
        path: '/plugin/icon',
        component: 'view.plugin_icon',
        meta: {
          title: '图标',
          i18nKey: 'route.plugin_icon',
          icon: 'material-symbols:sentiment-satisfied-outline-rounded'
        }
      },
      {
        name: 'plugin_map',
        path: '/plugin/map',
        component: 'view.plugin_map',
        meta: {
          title: '地图',
          i18nKey: 'route.plugin_map',
          icon: 'material-symbols:location-on-outline-rounded'
        }
      },
      {
        name: 'plugin_pdf',
        path: '/plugin/pdf',
        component: 'view.plugin_pdf',
        meta: {
          title: 'plugin_pdf',
          i18nKey: 'route.plugin_pdf'
        }
      },
      {
        name: 'plugin_pinyin',
        path: '/plugin/pinyin',
        component: 'view.plugin_pinyin',
        meta: {
          title: 'plugin_pinyin',
          i18nKey: 'route.plugin_pinyin'
        }
      },
      {
        name: 'plugin_print',
        path: '/plugin/print',
        component: 'view.plugin_print',
        meta: {
          title: '打印',
          i18nKey: 'route.plugin_print',
          icon: 'material-symbols:print-outline'
        }
      },
      {
        name: 'plugin_swiper',
        path: '/plugin/swiper',
        component: 'view.plugin_swiper',
        meta: {
          title: '轮播图',
          i18nKey: 'route.plugin_swiper',
          icon: 'material-symbols:view-carousel-outline-rounded'
        }
      },
      {
        name: 'plugin_tables',
        path: '/plugin/tables',
        meta: {
          title: 'plugin_tables',
          i18nKey: 'route.plugin_tables'
        },
        children: [
          {
            name: 'plugin_tables_vtable',
            path: '/plugin/tables/vtable',
            component: 'view.plugin_tables_vtable',
            meta: {
              title: 'plugin_tables_vtable',
              i18nKey: 'route.plugin_tables_vtable'
            }
          }
        ]
      },
      {
        name: 'plugin_typeit',
        path: '/plugin/typeit',
        component: 'view.plugin_typeit',
        meta: {
          title: 'plugin_typeit',
          i18nKey: 'route.plugin_typeit'
        }
      },
      {
        name: 'plugin_video',
        path: '/plugin/video',
        component: 'view.plugin_video',
        meta: {
          title: '视频',
          i18nKey: 'route.plugin_video',
          icon: 'material-symbols:play-circle-outline-rounded'
        }
      }
    ]
  },
  {
    name: 'posts',
    path: '/posts',
    component: 'layout.base',
    meta: {
      title: 'posts',
      i18nKey: 'route.posts'
    },
    children: [
      {
        name: 'posts_comment',
        path: '/posts/comment',
        component: 'view.posts_comment',
        meta: {
          title: 'posts_comment',
          i18nKey: 'route.posts_comment'
        }
      },
      {
        name: 'posts_list',
        path: '/posts/list',
        component: 'view.posts_list',
        meta: {
          title: 'posts_list',
          i18nKey: 'route.posts_list'
        }
      }
    ]
  },
  {
    name: 'shortvideos',
    path: '/shortvideos',
    component: 'layout.base',
    meta: {
      title: 'shortvideos',
      i18nKey: 'route.shortvideos'
    },
    children: [
      {
        name: 'shortvideos_category',
        path: '/shortvideos/category',
        component: 'view.shortvideos_category',
        meta: {
          title: 'shortvideos_category',
          i18nKey: 'route.shortvideos_category',
          order: 1
        }
      },
      {
        name: 'shortvideos_comment',
        path: '/shortvideos/comment',
        component: 'view.shortvideos_comment',
        meta: {
          title: 'shortvideos_comment',
          i18nKey: 'route.shortvideos_comment',
          order: 3
        }
      },
      {
        name: 'shortvideos_list',
        path: '/shortvideos/list',
        component: 'view.shortvideos_list',
        meta: {
          title: 'shortvideos_list',
          i18nKey: 'route.shortvideos_list',
          order: 2
        }
      }
    ]
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system'
    },
    children: [
      {
        name: 'system_menus',
        path: '/system/menus',
        component: 'view.system_menus',
        meta: {
          title: 'system_menus',
          i18nKey: 'route.system_menus'
        }
      },
      {
        name: 'system_mock',
        path: '/system/mock',
        component: 'view.system_mock',
        meta: {
          title: 'system_mock',
          i18nKey: 'route.system_mock'
        }
      },
      {
        name: 'system_tags',
        path: '/system/tags',
        component: 'view.system_tags',
        meta: {
          title: 'system_tags',
          i18nKey: 'route.system_tags'
        }
      }
    ]
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center'
    }
  },
  {
    name: 'users',
    path: '/users',
    component: 'layout.base',
    meta: {
      title: 'users',
      i18nKey: 'route.users'
    },
    children: [
      {
        name: 'users_list',
        path: '/users/list',
        component: 'view.users_list',
        meta: {
          title: 'users_list',
          i18nKey: 'route.users_list'
        }
      },
      {
        name: 'users_login-logs',
        path: '/users/login-logs',
        component: 'view.users_login-logs',
        meta: {
          title: 'users_login-logs',
          i18nKey: 'route.users_login-logs'
        }
      }
    ]
  },
  {
    name: 'videos',
    path: '/videos',
    component: 'layout.base',
    meta: {
      title: 'videos',
      i18nKey: 'route.videos'
    },
    children: [
      {
        name: 'videos_albums',
        path: '/videos/albums',
        component: 'view.videos_albums',
        meta: {
          title: 'videos_albums',
          i18nKey: 'route.videos_albums'
        }
      },
      {
        name: 'videos_category',
        path: '/videos/category',
        component: 'view.videos_category',
        meta: {
          title: 'videos_category',
          i18nKey: 'route.videos_category'
        }
      },
      {
        name: 'videos_channel',
        path: '/videos/channel',
        component: 'view.videos_channel',
        meta: {
          title: 'videos_channel',
          i18nKey: 'route.videos_channel'
        }
      },
      {
        name: 'videos_comment',
        path: '/videos/comment',
        component: 'view.videos_comment',
        meta: {
          title: 'videos_comment',
          i18nKey: 'route.videos_comment'
        }
      },
      {
        name: 'videos_list',
        path: '/videos/list',
        component: 'view.videos_list',
        meta: {
          title: 'videos_list',
          i18nKey: 'route.videos_list'
        }
      }
    ]
  }
];

// 添加模块路由到generatedRoutes
// 先处理单路由模块
if (Array.isArray(homeRoutes) && homeRoutes.length > 0) {
  homeRoutes.forEach(route => generatedRoutes.push(route));
} else if (homeRoutes) {
  generatedRoutes.push(homeRoutes);
}

// 处理其他模块
[
  usersRoutes,
  videosRoutes,
  shortvideoRoutes,
  postsRoutes,
  picturesRoutes,
  booksRoutes,
  comicsRoutes,
  tagsRoutes
].forEach(moduleRoute => {
  if (Array.isArray(moduleRoute) && moduleRoute.length > 0) {
    moduleRoute.forEach(route => generatedRoutes.push(route));
  } else if (moduleRoute) {
    generatedRoutes.push(moduleRoute);
  }
});
