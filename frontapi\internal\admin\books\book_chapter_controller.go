package books

import (
	"frontapi/internal/admin"
	bookModel "frontapi/internal/models/books"
	bookSrv "frontapi/internal/service/books"
	bookValidator "frontapi/internal/validation/books"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

type BookChapterController struct {
	admin.BaseController
	BookCategoryService bookSrv.BookCategoryService
	BookChapterService  bookSrv.BookChapterService
	BookService         bookSrv.BookService
}

func NewBookChapterController(
	bookChapterService bookSrv.BookChapterService,
	bookService bookSrv.BookService,
	bookCategoryService bookSrv.BookCategoryService,
) *BookChapterController {
	return &BookChapterController{
		BaseController:      admin.BaseController{},
		BookService:         bookService,
		BookCategoryService: bookCategoryService,
		BookChapterService:  bookChapterService,
	}
}

// ListBookChapters 获取电子书章节列表
func (c *BookChapterController) ListBookChapters(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	bookID := reqInfo.Get("book_id").GetString()
	title := reqInfo.Get("title").GetString()
	chapterNumber := reqInfo.Get("chapter_number").GetInt()
	status := -999 // 默认值-1表示不过滤
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	condition := map[string]interface{}{
		"book_id":        bookID,
		"chapter_number": chapterNumber,
		"title":          title,
		"status":         1,
	}

	// 查询电子书章节列表
	chapterList, total, err := c.BookChapterService.GetChapterList(ctx.Context(), condition, page, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, "获取电子书章节列表失败: "+err.Error())
	}

	// 返回电子书章节列表
	return c.SuccessList(ctx, chapterList, total, page, pageSize)
}

// GetBookChapter 获取电子书章节详情
func (c *BookChapterController) GetBookChapter(ctx *fiber.Ctx) error {
	// 获取章节ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询章节
	chapter, err := c.BookChapterService.GetChapter(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "获取电子书章节详情失败: "+err.Error())
	}

	if chapter == nil {
		return c.NotFound(ctx, "电子书章节不存在")
	}

	// 返回章节详情
	return c.Success(ctx, chapter)
}

// CreateBookChapter 创建电子书章节
func (c *BookChapterController) CreateBookChapter(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.CreateChapterRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	var BookChapter bookModel.BookChapter
	if err := utils.SmartCopy(req, &BookChapter); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}
	BookChapter.BookID = req.BookID
	// 创建章节
	chapter, err := c.BookChapterService.Create(ctx.Context(), &BookChapter)
	if err != nil {
		return c.InternalServerError(ctx, "创建电子书章节失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      chapter,
		"message": "创建电子书章节成功",
	})
}

// UpdateBookChapter 更新电子书章节
func (c *BookChapterController) UpdateBookChapter(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.UpdateChapterRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	id, _ := c.GetId(ctx)
	var bookChapter bookModel.BookChapter
	if err := utils.SmartCopy(req, &bookChapter); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 更新章节
	err := c.BookChapterService.UpdateById(ctx.Context(), id, &bookChapter)
	if err != nil {
		return c.InternalServerError(ctx, "更新电子书章节失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新电子书章节成功")
}

// DeleteBookChapter 删除电子书章节
func (c *BookChapterController) DeleteBookChapter(ctx *fiber.Ctx) error {
	// 获取章节ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除章节
	err = c.BookChapterService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除电子书章节失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除电子书章节成功")
}

// BatchUploadChapters 批量上传章节
func (c *BookChapterController) BatchUploadChapters(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.BatchUploadChaptersRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量上传章节
	count, err := c.BookChapterService.BatchCreateChapters(ctx.Context(), &req)
	if err != nil {
		return c.InternalServerError(ctx, "批量上传章节失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"count":   count,
		"message": "批量上传章节成功",
	})
}

// ReorderBookChapter 调整章节顺序
func (c *BookChapterController) ReorderBookChapter(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.ReorderChapterRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 调整章节顺序
	err := c.BookChapterService.ReorderChapter(ctx.Context(), &req)
	if err != nil {
		return c.InternalServerError(ctx, "调整章节顺序失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "调整章节顺序成功")
}

// BatchUpdateChapterOrder 批量更新章节排序
func (c *BookChapterController) BatchUpdateChapterOrder(ctx *fiber.Ctx) error {
	// 解析请求参数
	request := struct {
		BookID   string                            `json:"book_id"`
		Chapters []*bookValidator.ChapterOrderItem `json:"chapters"`
	}{}

	if err := validator.ValidateDataWrapper(ctx, &request); err != nil {
		return c.BadRequest(ctx, "无效的请求参数: "+err.Error(), nil)
	}

	// 验证必要参数
	if request.BookID == "" {
		return c.BadRequest(ctx, "电子书ID不能为空", nil)
	}

	if len(request.Chapters) == 0 {
		return c.BadRequest(ctx, "章节排序数据不能为空", nil)
	}

	// 调用服务层方法批量更新章节排序
	err := c.BookChapterService.BatchUpdateChapterOrder(ctx.Context(), request.BookID, request.Chapters)
	if err != nil {
		return c.InternalServerError(ctx, "更新章节排序失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新章节排序成功")
}
