package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(localhost:3306)/lyvideos?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatal(err)
	}
	defer db.Close()

	// 查询前10条记录的tags_json字段
	rows, err := db.Query("SELECT id, title, tags_json FROM ly_videos LIMIT 10")
	if err != nil {
		log.Fatal(err)
	}
	defer rows.Close()

	fmt.Println("检查ly_videos表中的tags_json字段数据:")
	fmt.Println("ID | Title | Tags JSON")
	fmt.Println("---|-------|----------")

	for rows.Next() {
		var id, title string
		var tagsJSON sql.NullString

		err := rows.Scan(&id, &title, &tagsJSON)
		if err != nil {
			fmt.Printf("扫描错误: %v\n", err)
			continue
		}

		if tagsJSON.Valid {
			fmt.Printf("%s | %s | %s\n", id, title, tagsJSON.String)
		} else {
			fmt.Printf("%s | %s | NULL\n", id, title)
		}
	}

	if err = rows.Err(); err != nil {
		log.Fatal(err)
	}
}
