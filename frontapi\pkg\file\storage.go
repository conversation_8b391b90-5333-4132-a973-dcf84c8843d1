package file

import (
	"os"
	"path/filepath"

	"frontapi/pkg/utils"
)

// StorageManager 存储管理器
type StorageManager struct {
	primaryPath  string
	fallbackPath string
	initialized  bool
}

var defaultManager *StorageManager

// GetStorageManager 获取默认存储管理器实例
func GetStorageManager() *StorageManager {
	if defaultManager == nil {
		defaultManager = &StorageManager{
			initialized: false,
		}
	}
	return defaultManager
}

// Initialize 初始化存储管理器
func (sm *StorageManager) Initialize() error {
	if sm.initialized {
		return nil
	}

	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		return err
	}

	// 检查是否在frontapi子目录中，如果是则回到上级目录
	if filepath.Base(currentDir) == "frontapi" {
		currentDir = filepath.Dir(currentDir)
	}

	// 构造根目录storage路径
	rootStoragePath := filepath.Join(currentDir, "storage")

	// 安全地检查根目录storage是否存在
	if info, err := os.Stat(rootStoragePath); err == nil && info.IsDir() {
		sm.primaryPath = rootStoragePath
	} else {
		// 备用路径：使用MEDIA_STORAGE_PATH环境变量，默认为当前目录下的storage
		sm.primaryPath = utils.GetEnv("MEDIA_STORAGE_PATH", filepath.Join(currentDir, "frontapi", "storage"))
	}

	// 设置初始化标志，避免递归调用
	sm.initialized = true

	// 确保所有必要的目录存在
	if err := sm.ensureDirectories(); err != nil {
		return err
	}

	return nil
}

// GetStoragePath 获取存储根路径
func (sm *StorageManager) GetStoragePath() string {
	if !sm.initialized {
		sm.Initialize()
	}
	return sm.primaryPath
}

// GetPicturesPath 获取图片存储路径
func (sm *StorageManager) GetPicturesPath() string {
	return filepath.Join(sm.GetStoragePath(), "pictures")
}

// GetVideosPath 获取视频存储路径
func (sm *StorageManager) GetVideosPath() string {
	return filepath.Join(sm.GetStoragePath(), "videos")
}

// GetStaticPath 获取静态文件存储路径
func (sm *StorageManager) GetStaticPath() string {
	return filepath.Join(sm.GetStoragePath(), "static")
}

// ensureDirectories 确保所有必要的目录存在
func (sm *StorageManager) ensureDirectories() error {
	// 直接使用 primaryPath 构建路径，避免递归调用
	directories := []string{
		filepath.Join(sm.primaryPath, "pictures"),
		filepath.Join(sm.primaryPath, "videos"),
		filepath.Join(sm.primaryPath, "static"),
	}

	for _, dir := range directories {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}

	return nil
}

// GetFileTypeDirectory 根据文件类型获取对应目录
func (sm *StorageManager) GetFileTypeDirectory(fileType string) string {
	switch fileType {
	case "pictures":
		return sm.GetPicturesPath()
	case "videos":
		return sm.GetVideosPath()
	case "static":
		return sm.GetStaticPath()
	default:
		return sm.GetStoragePath()
	}
}

// FileExists 检查文件是否存在
func (sm *StorageManager) FileExists(fileType, filename string) bool {
	fullPath := filepath.Join(sm.GetFileTypeDirectory(fileType), filename)
	_, err := os.Stat(fullPath)
	return err == nil
}

// GetFullPath 获取文件的完整路径
func (sm *StorageManager) GetFullPath(fileType, filename string) string {
	return filepath.Join(sm.GetFileTypeDirectory(fileType), filename)
}

// 便捷函数
func GetStoragePath() string {
	return GetStorageManager().GetStoragePath()
}

func GetPicturesPath() string {
	return GetStorageManager().GetPicturesPath()
}

func GetVideosPath() string {
	return GetStorageManager().GetVideosPath()
}

func GetStaticPath() string {
	return GetStorageManager().GetStaticPath()
}

func InitializeStorage() error {
	return GetStorageManager().Initialize()
}
