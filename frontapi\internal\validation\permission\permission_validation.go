package permission

// CheckPermissionRequest 检查权限请求
type CheckPermissionRequest struct {
	UserID   string `json:"user_id" validate:"required"`
	Resource string `json:"resource" validate:"required"`
	Action   string `json:"action" validate:"required"`
}

// AssignUserRolesRequest 分配用户角色请求
type AssignUserRolesRequest struct {
	UserID    int      `json:"user_id" validate:"required"`
	RoleCodes []string `json:"role_codes"`
}

// BatchPermissionRequest 批量权限操作请求
type BatchPermissionRequest struct {
	Rules []PermissionRule `json:"rules" validate:"required"`
}

// PermissionRule 权限规则
type PermissionRule struct {
	Sub string `json:"sub"` // 主体（用户ID或角色）
	Obj string `json:"obj"` // 对象（资源）
	Act string `json:"act"` // 操作
}
