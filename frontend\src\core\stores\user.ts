/**
 * 用户状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  role: string
  permissions: string[]
  profile?: {
    nickname?: string
    bio?: string
    location?: string
    website?: string
  }
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const currentUser = ref<User | null>(null)
  const isLoggedIn = ref<boolean>(false)
  const loading = ref<boolean>(false)

  // 计算属性
  const userInfo = computed(() => currentUser.value)
  const hasPermission = computed(() => (permission: string) => {
    return currentUser.value?.permissions?.includes(permission) || false
  })
  const isAdmin = computed(() => currentUser.value?.role === 'admin')

  // 方法
  const setUser = (user: User | null) => {
    currentUser.value = user
    isLoggedIn.value = !!user
  }

  const updateUser = (updates: Partial<User>) => {
    if (currentUser.value) {
      currentUser.value = { ...currentUser.value, ...updates }
    }
  }

  const logout = () => {
    currentUser.value = null
    isLoggedIn.value = false
  }

  const setLoading = (state: boolean) => {
    loading.value = state
  }

  return {
    // 状态
    currentUser,
    isLoggedIn,
    loading,
    
    // 计算属性
    userInfo,
    hasPermission,
    isAdmin,
    
    // 方法
    setUser,
    updateUser,
    logout,
    setLoading
  }
})

export default useUserStore