/**
 * 视频服务
 * 处理视频相关的业务逻辑
 */

import { ref, computed } from 'vue'
import type { Video, VideoCategory, VideoStats, VideoComment, VideoPlaylist } from '@/shared/types'
import { apiRequest } from '@/core/utils/request'
import { storageUtils } from '@/core/utils'

export interface VideoService {
  // 视频列表状态
  videos: Ref<Video[]>
  loading: Ref<boolean>
  hasMore: Ref<boolean>
  currentPage: Ref<number>
  
  // 视频操作
  getVideos(params?: VideoListParams): Promise<Video[]>
  getVideoById(id: string): Promise<Video>
  uploadVideo(videoData: VideoUploadData): Promise<Video>
  updateVideo(id: string, data: Partial<Video>): Promise<Video>
  deleteVideo(id: string): Promise<void>
  
  // 视频交互
  likeVideo(videoId: string): Promise<void>
  unlikeVideo(videoId: string): Promise<void>
  favoriteVideo(videoId: string): Promise<void>
  unfavoriteVideo(videoId: string): Promise<void>
  shareVideo(videoId: string, platform: string): Promise<void>
  
  // 视频播放
  recordView(videoId: string): Promise<void>
  updateWatchTime(videoId: string, watchTime: number): Promise<void>
  getVideoStats(videoId: string): Promise<VideoStats>
  
  // 视频评论
  getComments(videoId: string, page?: number): Promise<VideoComment[]>
  addComment(videoId: string, content: string, parentId?: string): Promise<VideoComment>
  updateComment(commentId: string, content: string): Promise<VideoComment>
  deleteComment(commentId: string): Promise<void>
  likeComment(commentId: string): Promise<void>
  
  // 视频分类
  getCategories(): Promise<VideoCategory[]>
  getVideosByCategory(categoryId: string, params?: VideoListParams): Promise<Video[]>
  
  // 播放列表
  createPlaylist(name: string, description?: string): Promise<VideoPlaylist>
  addToPlaylist(playlistId: string, videoId: string): Promise<void>
  removeFromPlaylist(playlistId: string, videoId: string): Promise<void>
  getUserPlaylists(userId: string): Promise<VideoPlaylist[]>
  
  // 搜索和推荐
  searchVideos(query: string, filters?: VideoSearchFilters): Promise<Video[]>
  getRecommendedVideos(userId?: string): Promise<Video[]>
  getTrendingVideos(): Promise<Video[]>
  getRelatedVideos(videoId: string): Promise<Video[]>
}

interface VideoListParams {
  page?: number
  limit?: number
  sortBy?: 'latest' | 'popular' | 'trending' | 'views'
  categoryId?: string
  duration?: 'short' | 'medium' | 'long'
  quality?: '720p' | '1080p' | '4k'
}

interface VideoUploadData {
  title: string
  description: string
  categoryId: string
  tags: string[]
  thumbnail: File
  video: File
  privacy: 'public' | 'private' | 'unlisted'
  allowComments: boolean
  allowDownload: boolean
}

interface VideoSearchFilters {
  categoryId?: string
  duration?: string
  uploadDate?: 'today' | 'week' | 'month' | 'year'
  sortBy?: string
  quality?: string
}

class VideoServiceImpl implements VideoService {
  videos = ref<Video[]>([])
  loading = ref(false)
  hasMore = ref(true)
  currentPage = ref(1)
  
  private watchHistory = new Map<string, number>()
  
  async getVideos(params: VideoListParams = {}): Promise<Video[]> {
    try {
      this.loading.value = true
      
      const response = await apiRequest.get('/videos', {
        params: {
          page: params.page || this.currentPage.value,
          limit: params.limit || 20,
          sortBy: params.sortBy || 'latest',
          categoryId: params.categoryId,
          duration: params.duration,
          quality: params.quality
        }
      })
      
      const { data, pagination } = response.data
      
      if (params.page === 1 || !params.page) {
        this.videos.value = data
      } else {
        this.videos.value.push(...data)
      }
      
      this.hasMore.value = pagination.hasMore
      this.currentPage.value = pagination.currentPage
      
      return data
    } catch (error) {
      throw new Error('获取视频列表失败')
    } finally {
      this.loading.value = false
    }
  }
  
  async getVideoById(id: string): Promise<Video> {
    try {
      const response = await apiRequest.get(`/videos/${id}`)
      return response.data
    } catch (error) {
      throw new Error('获取视频详情失败')
    }
  }
  
  async uploadVideo(videoData: VideoUploadData): Promise<Video> {
    try {
      const formData = new FormData()
      
      // 添加视频文件
      formData.append('video', videoData.video)
      formData.append('thumbnail', videoData.thumbnail)
      
      // 添加元数据
      formData.append('title', videoData.title)
      formData.append('description', videoData.description)
      formData.append('categoryId', videoData.categoryId)
      formData.append('tags', JSON.stringify(videoData.tags))
      formData.append('privacy', videoData.privacy)
      formData.append('allowComments', String(videoData.allowComments))
      formData.append('allowDownload', String(videoData.allowDownload))
      
      const response = await apiRequest.post('/videos/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round(
            (progressEvent.loaded * 100) / (progressEvent.total || 1)
          )
          // 可以通过事件系统通知上传进度
          console.log(`上传进度: ${progress}%`)
        }
      })
      
      return response.data
    } catch (error) {
      throw new Error('视频上传失败')
    }
  }
  
  async updateVideo(id: string, data: Partial<Video>): Promise<Video> {
    try {
      const response = await apiRequest.put(`/videos/${id}`, data)
      
      // 更新本地视频列表
      const index = this.videos.value.findIndex(v => v.id === id)
      if (index !== -1) {
        this.videos.value[index] = { ...this.videos.value[index], ...response.data }
      }
      
      return response.data
    } catch (error) {
      throw new Error('更新视频失败')
    }
  }
  
  async deleteVideo(id: string): Promise<void> {
    try {
      await apiRequest.delete(`/videos/${id}`)
      
      // 从本地列表中移除
      this.videos.value = this.videos.value.filter(v => v.id !== id)
    } catch (error) {
      throw new Error('删除视频失败')
    }
  }
  
  async likeVideo(videoId: string): Promise<void> {
    try {
      await apiRequest.post(`/videos/${videoId}/like`)
      
      // 更新本地状态
      this.updateVideoInteraction(videoId, 'liked', true)
    } catch (error) {
      throw new Error('点赞失败')
    }
  }
  
  async unlikeVideo(videoId: string): Promise<void> {
    try {
      await apiRequest.delete(`/videos/${videoId}/like`)
      
      // 更新本地状态
      this.updateVideoInteraction(videoId, 'liked', false)
    } catch (error) {
      throw new Error('取消点赞失败')
    }
  }
  
  async favoriteVideo(videoId: string): Promise<void> {
    try {
      await apiRequest.post(`/videos/${videoId}/favorite`)
      
      // 更新本地状态
      this.updateVideoInteraction(videoId, 'favorited', true)
    } catch (error) {
      throw new Error('收藏失败')
    }
  }
  
  async unfavoriteVideo(videoId: string): Promise<void> {
    try {
      await apiRequest.delete(`/videos/${videoId}/favorite`)
      
      // 更新本地状态
      this.updateVideoInteraction(videoId, 'favorited', false)
    } catch (error) {
      throw new Error('取消收藏失败')
    }
  }
  
  async shareVideo(videoId: string, platform: string): Promise<void> {
    try {
      await apiRequest.post(`/videos/${videoId}/share`, { platform })
      
      // 记录分享统计
      this.updateVideoStats(videoId, 'shareCount', 1)
    } catch (error) {
      throw new Error('分享失败')
    }
  }
  
  async recordView(videoId: string): Promise<void> {
    try {
      // 防止重复记录观看
      const lastView = storageUtils.get(`last_view_${videoId}`)
      const now = Date.now()
      
      if (!lastView || now - lastView > 30000) { // 30秒内不重复记录
        await apiRequest.post(`/videos/${videoId}/view`)
        storageUtils.set(`last_view_${videoId}`, now)
        
        // 更新本地统计
        this.updateVideoStats(videoId, 'viewCount', 1)
      }
    } catch (error) {
      console.error('记录观看失败:', error)
    }
  }
  
  async updateWatchTime(videoId: string, watchTime: number): Promise<void> {
    try {
      // 累积观看时间
      const currentTime = this.watchHistory.get(videoId) || 0
      const newTime = currentTime + watchTime
      this.watchHistory.set(videoId, newTime)
      
      // 每30秒同步一次观看时间
      if (newTime % 30 < watchTime) {
        await apiRequest.post(`/videos/${videoId}/watch-time`, {
          watchTime: newTime
        })
      }
    } catch (error) {
      console.error('更新观看时间失败:', error)
    }
  }
  
  async getVideoStats(videoId: string): Promise<VideoStats> {
    try {
      const response = await apiRequest.get(`/videos/${videoId}/stats`)
      return response.data
    } catch (error) {
      throw new Error('获取视频统计失败')
    }
  }
  
  async getComments(videoId: string, page = 1): Promise<VideoComment[]> {
    try {
      const response = await apiRequest.get(`/videos/${videoId}/comments`, {
        params: { page, limit: 20 }
      })
      return response.data
    } catch (error) {
      throw new Error('获取评论失败')
    }
  }
  
  async addComment(videoId: string, content: string, parentId?: string): Promise<VideoComment> {
    try {
      const response = await apiRequest.post(`/videos/${videoId}/comments`, {
        content,
        parentId
      })
      
      // 更新评论数
      this.updateVideoStats(videoId, 'commentCount', 1)
      
      return response.data
    } catch (error) {
      throw new Error('添加评论失败')
    }
  }
  
  async updateComment(commentId: string, content: string): Promise<VideoComment> {
    try {
      const response = await apiRequest.put(`/comments/${commentId}`, { content })
      return response.data
    } catch (error) {
      throw new Error('更新评论失败')
    }
  }
  
  async deleteComment(commentId: string): Promise<void> {
    try {
      await apiRequest.delete(`/comments/${commentId}`)
    } catch (error) {
      throw new Error('删除评论失败')
    }
  }
  
  async likeComment(commentId: string): Promise<void> {
    try {
      await apiRequest.post(`/comments/${commentId}/like`)
    } catch (error) {
      throw new Error('点赞评论失败')
    }
  }
  
  async getCategories(): Promise<VideoCategory[]> {
    try {
      const response = await apiRequest.get('/videos/categories')
      return response.data
    } catch (error) {
      throw new Error('获取分类失败')
    }
  }
  
  async getVideosByCategory(categoryId: string, params: VideoListParams = {}): Promise<Video[]> {
    return this.getVideos({ ...params, categoryId })
  }
  
  async createPlaylist(name: string, description = ''): Promise<VideoPlaylist> {
    try {
      const response = await apiRequest.post('/playlists', {
        name,
        description
      })
      return response.data
    } catch (error) {
      throw new Error('创建播放列表失败')
    }
  }
  
  async addToPlaylist(playlistId: string, videoId: string): Promise<void> {
    try {
      await apiRequest.post(`/playlists/${playlistId}/videos`, { videoId })
    } catch (error) {
      throw new Error('添加到播放列表失败')
    }
  }
  
  async removeFromPlaylist(playlistId: string, videoId: string): Promise<void> {
    try {
      await apiRequest.delete(`/playlists/${playlistId}/videos/${videoId}`)
    } catch (error) {
      throw new Error('从播放列表移除失败')
    }
  }
  
  async getUserPlaylists(userId: string): Promise<VideoPlaylist[]> {
    try {
      const response = await apiRequest.get(`/users/${userId}/playlists`)
      return response.data
    } catch (error) {
      throw new Error('获取播放列表失败')
    }
  }
  
  async searchVideos(query: string, filters: VideoSearchFilters = {}): Promise<Video[]> {
    try {
      const response = await apiRequest.get('/videos/search', {
        params: {
          q: query,
          ...filters
        }
      })
      return response.data
    } catch (error) {
      throw new Error('搜索视频失败')
    }
  }
  
  async getRecommendedVideos(userId?: string): Promise<Video[]> {
    try {
      const response = await apiRequest.get('/videos/recommended', {
        params: { userId }
      })
      return response.data
    } catch (error) {
      throw new Error('获取推荐视频失败')
    }
  }
  
  async getTrendingVideos(): Promise<Video[]> {
    try {
      const response = await apiRequest.get('/videos/trending')
      return response.data
    } catch (error) {
      throw new Error('获取热门视频失败')
    }
  }
  
  async getRelatedVideos(videoId: string): Promise<Video[]> {
    try {
      const response = await apiRequest.get(`/videos/${videoId}/related`)
      return response.data
    } catch (error) {
      throw new Error('获取相关视频失败')
    }
  }
  
  private updateVideoInteraction(videoId: string, field: string, value: boolean) {
    const video = this.videos.value.find(v => v.id === videoId)
    if (video && video.userInteraction) {
      video.userInteraction[field] = value
    }
  }
  
  private updateVideoStats(videoId: string, field: string, increment: number) {
    const video = this.videos.value.find(v => v.id === videoId)
    if (video && video.stats) {
      video.stats[field] = (video.stats[field] || 0) + increment
    }
  }
}

// 导出单例实例
export const videoService = new VideoServiceImpl()

// 导出类型
export type { VideoListParams, VideoUploadData, VideoSearchFilters }