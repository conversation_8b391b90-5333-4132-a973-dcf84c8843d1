import { ref, computed, watch, nextTick } from 'vue'
import { useStorage, useSessionStorage } from '@vueuse/core'
import { useRouter, useRoute } from 'vue-router'
import { generateId } from '@/core/utils'
import { useCache } from './useCache'
import { useErrorHandler } from './useErrorHandler'
import { usePerformance } from './usePerformance'

// 事件类型
export enum EventType {
  // 页面事件
  PAGE_VIEW = 'page_view',
  PAGE_LEAVE = 'page_leave',
  PAGE_SCROLL = 'page_scroll',
  
  // 用户交互
  CLICK = 'click',
  HOVER = 'hover',
  FOCUS = 'focus',
  BLUR = 'blur',
  SUBMIT = 'submit',
  
  // 内容交互
  VIDEO_PLAY = 'video_play',
  VIDEO_PAUSE = 'video_pause',
  VIDEO_END = 'video_end',
  VIDEO_SEEK = 'video_seek',
  
  // 社交互动
  LIKE = 'like',
  UNLIKE = 'unlike',
  SHARE = 'share',
  COMMENT = 'comment',
  FOLLOW = 'follow',
  UNFOLLOW = 'unfollow',
  
  // 搜索和发现
  SEARCH = 'search',
  FILTER = 'filter',
  SORT = 'sort',
  
  // 转化事件
  SIGNUP = 'signup',
  LOGIN = 'login',
  LOGOUT = 'logout',
  PURCHASE = 'purchase',
  SUBSCRIPTION = 'subscription',
  
  // 错误事件
  ERROR = 'error',
  CRASH = 'crash',
  
  // 性能事件
  PERFORMANCE = 'performance',
  
  // 自定义事件
  CUSTOM = 'custom'
}

// 事件属性
export interface EventProperties {
  [key: string]: string | number | boolean | null | undefined
}

// 分析事件
export interface AnalyticsEvent {
  id: string
  type: EventType
  name: string
  properties: EventProperties
  timestamp: number
  sessionId: string
  userId?: string
  deviceId: string
  url: string
  referrer: string
  userAgent: string
  viewport: {
    width: number
    height: number
  }
  screen: {
    width: number
    height: number
  }
  location: {
    country?: string
    region?: string
    city?: string
    timezone: string
  }
  performance?: {
    loadTime: number
    renderTime: number
    interactionTime: number
  }
}

// 用户属性
export interface UserProperties {
  userId?: string
  email?: string
  username?: string
  role?: string
  plan?: string
  signupDate?: string
  lastLoginDate?: string
  totalSessions?: number
  totalEvents?: number
  [key: string]: string | number | boolean | null | undefined
}

// 会话信息
export interface SessionInfo {
  sessionId: string
  startTime: number
  endTime?: number
  duration?: number
  pageViews: number
  events: number
  bounced: boolean
  converted: boolean
  referrer: string
  landingPage: string
  exitPage?: string
  device: {
    type: 'desktop' | 'tablet' | 'mobile'
    os: string
    browser: string
  }
  location: {
    country?: string
    region?: string
    city?: string
    timezone: string
  }
}

// 页面视图信息
export interface PageView {
  id: string
  url: string
  title: string
  referrer: string
  timestamp: number
  duration?: number
  scrollDepth: number
  interactions: number
  exitType?: 'navigation' | 'close' | 'refresh'
}

// 转化目标
export interface ConversionGoal {
  id: string
  name: string
  description: string
  type: 'event' | 'page' | 'funnel'
  conditions: {
    eventType?: EventType
    eventName?: string
    url?: string
    properties?: EventProperties
  }[]
  value?: number
  currency?: string
}

// 漏斗分析
export interface FunnelStep {
  id: string
  name: string
  eventType: EventType
  eventName?: string
  url?: string
  properties?: EventProperties
}

export interface Funnel {
  id: string
  name: string
  description: string
  steps: FunnelStep[]
  timeWindow: number // 完成漏斗的时间窗口（毫秒）
}

// A/B测试
export interface ABTest {
  id: string
  name: string
  description: string
  status: 'draft' | 'running' | 'paused' | 'completed'
  variants: {
    id: string
    name: string
    weight: number
    properties: EventProperties
  }[]
  targetAudience?: {
    userProperties?: UserProperties
    sessionProperties?: Partial<SessionInfo>
  }
  conversionGoal: string // ConversionGoal ID
  startDate: string
  endDate?: string
}

// 分析配置
export interface AnalyticsConfig {
  apiEndpoint: string
  apiKey: string
  userId?: string
  sessionTimeout: number
  batchSize: number
  batchTimeout: number
  enableAutoTracking: boolean
  enablePerformanceTracking: boolean
  enableErrorTracking: boolean
  enableScrollTracking: boolean
  enableClickTracking: boolean
  enableFormTracking: boolean
  enableVideoTracking: boolean
  trackingDomains: string[]
  excludeUrls: string[]
  excludeElements: string[]
  customDimensions: Record<string, string>
  debug: boolean
}

// 默认配置
const defaultConfig: AnalyticsConfig = {
  apiEndpoint: '/api/analytics',
  apiKey: '',
  sessionTimeout: 30 * 60 * 1000, // 30分钟
  batchSize: 50,
  batchTimeout: 10 * 1000, // 10秒
  enableAutoTracking: true,
  enablePerformanceTracking: true,
  enableErrorTracking: true,
  enableScrollTracking: true,
  enableClickTracking: true,
  enableFormTracking: true,
  enableVideoTracking: true,
  trackingDomains: [],
  excludeUrls: [],
  excludeElements: ['.no-track', '[data-no-track]'],
  customDimensions: {},
  debug: false
}

/**
 * 分析系统 Composable
 * 提供完整的用户行为分析和数据收集功能
 */
export function useAnalytics(config: Partial<AnalyticsConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }
  const { logError, logInfo } = useErrorHandler()
  const { getMetrics } = usePerformance()
  const router = useRouter()
  const route = useRoute()
  
  const eventCache = useCache<AnalyticsEvent>({
    maxSize: 1000,
    defaultTTL: 60 * 60 * 1000, // 1小时
    storageKey: 'analytics_cache'
  })

  // 响应式状态
  const isInitialized = ref(false)
  const isTracking = ref(false)
  const sessionId = useSessionStorage('analytics_session_id', generateId())
  const deviceId = useStorage('analytics_device_id', generateId())
  const userId = ref<string | undefined>(finalConfig.userId)
  const userProperties = ref<UserProperties>({})
  const sessionInfo = ref<SessionInfo | null>(null)
  const currentPageView = ref<PageView | null>(null)
  const eventQueue = ref<AnalyticsEvent[]>([])
  const batchTimer = ref<NodeJS.Timeout | null>(null)
  const abTests = ref<ABTest[]>([])
  const activeVariants = useStorage<Record<string, string>>('ab_test_variants', {})

  // 计算属性
  const isSessionActive = computed(() => {
    if (!sessionInfo.value) return false
    const now = Date.now()
    const lastActivity = sessionInfo.value.endTime || sessionInfo.value.startTime
    return (now - lastActivity) < finalConfig.sessionTimeout
  })

  const sessionDuration = computed(() => {
    if (!sessionInfo.value) return 0
    const endTime = sessionInfo.value.endTime || Date.now()
    return endTime - sessionInfo.value.startTime
  })

  // 使用统一的工具函数

  // 获取设备信息
  const getDeviceInfo = () => {
    const ua = navigator.userAgent
    let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop'
    
    if (/tablet|ipad|playbook|silk/i.test(ua)) {
      deviceType = 'tablet'
    } else if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(ua)) {
      deviceType = 'mobile'
    }
    
    const getOS = () => {
      if (ua.includes('Windows')) return 'Windows'
      if (ua.includes('Mac')) return 'macOS'
      if (ua.includes('Linux')) return 'Linux'
      if (ua.includes('Android')) return 'Android'
      if (ua.includes('iOS')) return 'iOS'
      return 'Unknown'
    }
    
    const getBrowser = () => {
      if (ua.includes('Chrome')) return 'Chrome'
      if (ua.includes('Firefox')) return 'Firefox'
      if (ua.includes('Safari')) return 'Safari'
      if (ua.includes('Edge')) return 'Edge'
      if (ua.includes('Opera')) return 'Opera'
      return 'Unknown'
    }
    
    return {
      type: deviceType,
      os: getOS(),
      browser: getBrowser()
    }
  }

  // 获取位置信息
  const getLocationInfo = () => {
    return {
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }

  // 创建基础事件数据
  const createBaseEvent = (): Omit<AnalyticsEvent, 'id' | 'type' | 'name' | 'properties'> => {
    return {
      timestamp: Date.now(),
      sessionId: sessionId.value,
      userId: userId.value,
      deviceId: deviceId.value,
      url: window.location.href,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: screen.width,
        height: screen.height
      },
      location: getLocationInfo()
    }
  }

  // 发送事件到服务器
  const sendEvents = async (events: AnalyticsEvent[]): Promise<void> => {
    if (!events.length) return
    
    try {
      const response = await fetch(finalConfig.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${finalConfig.apiKey}`
        },
        body: JSON.stringify({
          events,
          userProperties: userProperties.value,
          sessionInfo: sessionInfo.value
        })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      if (finalConfig.debug) {
        logInfo('Analytics events sent successfully', { count: events.length })
      }
      
    } catch (error) {
      logError(error as Error, {
        context: 'Analytics',
        action: 'sendEvents',
        eventCount: events.length
      })
      
      // 将失败的事件重新加入队列
      eventQueue.value.unshift(...events)
    }
  }

  // 批量发送事件
  const flushEvents = async (): Promise<void> => {
    if (!eventQueue.value.length) return
    
    const eventsToSend = eventQueue.value.splice(0, finalConfig.batchSize)
    await sendEvents(eventsToSend)
  }

  // 启动批量发送定时器
  const startBatchTimer = (): void => {
    if (batchTimer.value) {
      clearTimeout(batchTimer.value)
    }
    
    batchTimer.value = setTimeout(async () => {
      await flushEvents()
      if (eventQueue.value.length > 0) {
        startBatchTimer()
      }
    }, finalConfig.batchTimeout)
  }

  // 跟踪事件
  const track = (type: EventType, name: string, properties: EventProperties = {}): void => {
    if (!isTracking.value) return
    
    const event: AnalyticsEvent = {
      id: generateId(),
      type,
      name,
      properties: {
        ...finalConfig.customDimensions,
        ...properties
      },
      ...createBaseEvent()
    }
    
    // 添加性能数据
    if (finalConfig.enablePerformanceTracking && type === EventType.PAGE_VIEW) {
      const metrics = getMetrics()
      event.performance = {
        loadTime: metrics.pageLoadTime || 0,
        renderTime: metrics.domContentLoadedTime || 0,
        interactionTime: metrics.firstInputDelay || 0
      }
    }
    
    // 缓存事件
    eventCache.set(event.id, event)
    
    // 添加到队列
    eventQueue.value.push(event)
    
    // 更新会话信息
    if (sessionInfo.value) {
      sessionInfo.value.events++
      sessionInfo.value.endTime = Date.now()
    }
    
    if (finalConfig.debug) {
      console.log('Analytics event tracked:', event)
    }
    
    // 检查是否需要立即发送
    if (eventQueue.value.length >= finalConfig.batchSize) {
      flushEvents()
    } else {
      startBatchTimer()
    }
  }

  // 跟踪页面视图
  const trackPageView = (url?: string, title?: string): void => {
    const pageUrl = url || window.location.href
    const pageTitle = title || document.title
    
    // 结束当前页面视图
    if (currentPageView.value) {
      currentPageView.value.duration = Date.now() - currentPageView.value.timestamp
      currentPageView.value.exitType = 'navigation'
    }
    
    // 创建新的页面视图
    const pageView: PageView = {
      id: generateId(),
      url: pageUrl,
      title: pageTitle,
      referrer: document.referrer,
      timestamp: Date.now(),
      scrollDepth: 0,
      interactions: 0
    }
    
    currentPageView.value = pageView
    
    // 更新会话信息
    if (sessionInfo.value) {
      sessionInfo.value.pageViews++
      sessionInfo.value.endTime = Date.now()
      if (!sessionInfo.value.landingPage) {
        sessionInfo.value.landingPage = pageUrl
      }
      sessionInfo.value.exitPage = pageUrl
    }
    
    // 跟踪页面视图事件
    track(EventType.PAGE_VIEW, 'page_view', {
      url: pageUrl,
      title: pageTitle,
      referrer: document.referrer
    })
  }

  // 跟踪用户交互
  const trackInteraction = (element: Element, type: EventType): void => {
    if (!isTracking.value) return
    
    // 检查是否应该排除此元素
    const shouldExclude = finalConfig.excludeElements.some(selector => {
      try {
        return element.matches(selector)
      } catch {
        return false
      }
    })
    
    if (shouldExclude) return
    
    const properties: EventProperties = {
      tagName: element.tagName.toLowerCase(),
      className: element.className,
      id: element.id,
      text: element.textContent?.slice(0, 100) || '',
      href: (element as HTMLAnchorElement).href || undefined
    }
    
    // 获取元素位置
    const rect = element.getBoundingClientRect()
    properties.elementX = rect.left
    properties.elementY = rect.top
    properties.elementWidth = rect.width
    properties.elementHeight = rect.height
    
    track(type, `${type}_${element.tagName.toLowerCase()}`, properties)
    
    // 更新当前页面视图的交互次数
    if (currentPageView.value) {
      currentPageView.value.interactions++
    }
  }

  // 跟踪滚动深度
  const trackScrollDepth = (): void => {
    if (!isTracking.value || !currentPageView.value) return
    
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.scrollHeight
    
    const scrollDepth = Math.round((scrollTop + windowHeight) / documentHeight * 100)
    
    if (scrollDepth > currentPageView.value.scrollDepth) {
      currentPageView.value.scrollDepth = scrollDepth
      
      // 跟踪滚动里程碑
      const milestones = [25, 50, 75, 90, 100]
      const milestone = milestones.find(m => 
        scrollDepth >= m && currentPageView.value!.scrollDepth < m
      )
      
      if (milestone) {
        track(EventType.PAGE_SCROLL, 'scroll_depth', {
          depth: milestone,
          url: currentPageView.value.url
        })
      }
    }
  }

  // 设置用户属性
  const setUserProperties = (properties: UserProperties): void => {
    userProperties.value = { ...userProperties.value, ...properties }
    
    if (properties.userId) {
      userId.value = properties.userId
    }
  }

  // 设置用户ID
  const setUserId = (id: string): void => {
    userId.value = id
    userProperties.value.userId = id
  }

  // 开始新会话
  const startSession = (): void => {
    const now = Date.now()
    
    sessionInfo.value = {
      sessionId: sessionId.value,
      startTime: now,
      pageViews: 0,
      events: 0,
      bounced: true,
      converted: false,
      referrer: document.referrer,
      landingPage: window.location.href,
      device: getDeviceInfo(),
      location: getLocationInfo()
    }
    
    if (finalConfig.debug) {
      logInfo('Analytics session started', { sessionId: sessionId.value })
    }
  }

  // 结束会话
  const endSession = (): void => {
    if (!sessionInfo.value) return
    
    sessionInfo.value.endTime = Date.now()
    sessionInfo.value.duration = sessionDuration.value
    
    // 判断是否为跳出
    sessionInfo.value.bounced = sessionInfo.value.pageViews <= 1 && sessionInfo.value.duration < 30000
    
    // 发送会话结束事件
    track(EventType.PAGE_LEAVE, 'session_end', {
      duration: sessionInfo.value.duration,
      pageViews: sessionInfo.value.pageViews,
      events: sessionInfo.value.events,
      bounced: sessionInfo.value.bounced
    })
    
    // 立即发送所有待发送的事件
    flushEvents()
    
    if (finalConfig.debug) {
      logInfo('Analytics session ended', {
        sessionId: sessionId.value,
        duration: sessionInfo.value.duration
      })
    }
  }

  // A/B测试相关
  const getVariant = (testId: string): string | null => {
    return activeVariants.value[testId] || null
  }

  const setVariant = (testId: string, variantId: string): void => {
    activeVariants.value[testId] = variantId
    
    track(EventType.CUSTOM, 'ab_test_variant', {
      testId,
      variantId
    })
  }

  const trackConversion = (goalId: string, value?: number): void => {
    track(EventType.CUSTOM, 'conversion', {
      goalId,
      value
    })
    
    if (sessionInfo.value) {
      sessionInfo.value.converted = true
    }
  }

  // 设置自动跟踪
  const setupAutoTracking = (): void => {
    if (!finalConfig.enableAutoTracking) return
    
    // 点击跟踪
    if (finalConfig.enableClickTracking) {
      document.addEventListener('click', (event) => {
        if (event.target instanceof Element) {
          trackInteraction(event.target, EventType.CLICK)
        }
      }, true)
    }
    
    // 滚动跟踪
    if (finalConfig.enableScrollTracking) {
      let scrollTimer: NodeJS.Timeout | null = null
      window.addEventListener('scroll', () => {
        if (scrollTimer) clearTimeout(scrollTimer)
        scrollTimer = setTimeout(trackScrollDepth, 100)
      })
    }
    
    // 表单跟踪
    if (finalConfig.enableFormTracking) {
      document.addEventListener('submit', (event) => {
        if (event.target instanceof HTMLFormElement) {
          trackInteraction(event.target, EventType.SUBMIT)
        }
      }, true)
    }
    
    // 视频跟踪
    if (finalConfig.enableVideoTracking) {
      document.addEventListener('play', (event) => {
        if (event.target instanceof HTMLVideoElement) {
          track(EventType.VIDEO_PLAY, 'video_play', {
            src: event.target.src,
            currentTime: event.target.currentTime,
            duration: event.target.duration
          })
        }
      }, true)
      
      document.addEventListener('pause', (event) => {
        if (event.target instanceof HTMLVideoElement) {
          track(EventType.VIDEO_PAUSE, 'video_pause', {
            src: event.target.src,
            currentTime: event.target.currentTime,
            duration: event.target.duration
          })
        }
      }, true)
      
      document.addEventListener('ended', (event) => {
        if (event.target instanceof HTMLVideoElement) {
          track(EventType.VIDEO_END, 'video_end', {
            src: event.target.src,
            duration: event.target.duration
          })
        }
      }, true)
    }
    
    // 错误跟踪
    if (finalConfig.enableErrorTracking) {
      window.addEventListener('error', (event) => {
        track(EventType.ERROR, 'javascript_error', {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        })
      })
      
      window.addEventListener('unhandledrejection', (event) => {
        track(EventType.ERROR, 'unhandled_promise_rejection', {
          reason: String(event.reason)
        })
      })
    }
    
    // 页面可见性变化
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        track(EventType.PAGE_LEAVE, 'page_hidden')
      } else {
        track(EventType.PAGE_VIEW, 'page_visible')
      }
    })
    
    // 页面卸载
    window.addEventListener('beforeunload', () => {
      endSession()
    })
  }

  // 开始跟踪
  const startTracking = (): void => {
    if (isTracking.value) return
    
    isTracking.value = true
    
    // 检查会话是否有效
    if (!isSessionActive.value) {
      sessionId.value = generateId()
      startSession()
    }
    
    // 跟踪当前页面
    trackPageView()
    
    if (finalConfig.debug) {
      logInfo('Analytics tracking started')
    }
  }

  // 停止跟踪
  const stopTracking = (): void => {
    if (!isTracking.value) return
    
    isTracking.value = false
    endSession()
    
    if (batchTimer.value) {
      clearTimeout(batchTimer.value)
      batchTimer.value = null
    }
    
    if (finalConfig.debug) {
      logInfo('Analytics tracking stopped')
    }
  }

  // 初始化分析系统
  const init = async (): Promise<void> => {
    if (isInitialized.value) return
    
    try {
      // 设置自动跟踪
      setupAutoTracking()
      
      // 监听路由变化
      if (router) {
        router.afterEach((to, from) => {
          if (to.path !== from.path) {
            trackPageView(to.fullPath, to.meta.title as string)
          }
        })
      }
      
      // 开始跟踪
      startTracking()
      
      isInitialized.value = true
      
      if (finalConfig.debug) {
        logInfo('Analytics system initialized')
      }
      
    } catch (error) {
      logError(error as Error, {
        context: 'Analytics',
        action: 'init'
      })
    }
  }

  return {
    // 响应式状态
    isInitialized: readonly(isInitialized),
    isTracking: readonly(isTracking),
    sessionId: readonly(sessionId),
    userId: readonly(userId),
    sessionInfo: readonly(sessionInfo),
    currentPageView: readonly(currentPageView),
    
    // 计算属性
    isSessionActive,
    sessionDuration,
    
    // 跟踪方法
    track,
    trackPageView,
    trackInteraction,
    trackScrollDepth,
    
    // 用户管理
    setUserId,
    setUserProperties,
    
    // 会话管理
    startSession,
    endSession,
    startTracking,
    stopTracking,
    
    // A/B测试
    getVariant,
    setVariant,
    trackConversion,
    
    // 批量发送
    flushEvents,
    
    // 初始化
    init
  }
}

/**
 * 页面分析 Hook
 * 简化的页面跟踪接口
 */
export function usePageAnalytics() {
  const { trackPageView, currentPageView, sessionInfo } = useAnalytics()
  
  return {
    trackPageView,
    currentPageView,
    sessionInfo
  }
}

/**
 * 事件分析 Hook
 * 简化的事件跟踪接口
 */
export function useEventAnalytics() {
  const { track, trackInteraction } = useAnalytics()
  
  const trackClick = (element: Element) => trackInteraction(element, EventType.CLICK)
  const trackSubmit = (element: Element) => trackInteraction(element, EventType.SUBMIT)
  const trackCustom = (name: string, properties?: EventProperties) => 
    track(EventType.CUSTOM, name, properties)
  
  return {
    track,
    trackClick,
    trackSubmit,
    trackCustom
  }
}

/**
 * 转化分析 Hook
 * 转化跟踪接口
 */
export function useConversionAnalytics() {
  const { trackConversion, setVariant, getVariant } = useAnalytics()
  
  return {
    trackConversion,
    setVariant,
    getVariant
  }
}