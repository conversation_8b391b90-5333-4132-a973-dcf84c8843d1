package content_creator

import (
	"frontapi/internal/admin"
	contentCreatorModel "frontapi/internal/models/content_creator"
	contentCreatorSrv "frontapi/internal/service/content_creator"
	contentCreatorValidator "frontapi/internal/validation/content_creator"

	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ContentRevenueController 内容收益控制器
type ContentRevenueController struct {
	ContentRevenueService contentCreatorSrv.ContentRevenueService
	admin.BaseController  // 继承BaseController
}

// NewContentRevenueController 创建内容收益控制器实例
func NewContentRevenueController(contentRevenueService contentCreatorSrv.ContentRevenueService) *ContentRevenueController {
	return &ContentRevenueController{
		ContentRevenueService: contentRevenueService,
	}
}

// ListContentRevenues 获取内容收益列表
func (c *ContentRevenueController) ListContentRevenues(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()
	userID := reqInfo.Get("user_id").GetString()
	condition := map[string]interface{}{
		"content_type": contentType,
		"content_id":   contentID,
		"user_id":      userID,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询内容收益列表
	list, total, err := c.ContentRevenueService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容收益列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// GetContentRevenue 获取内容收益详情
func (c *ContentRevenueController) GetContentRevenue(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询内容收益
	contentRevenue, err := c.ContentRevenueService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容收益详情失败: "+err.Error())
	}

	if contentRevenue == nil {
		return c.NotFound(ctx, "内容收益不存在")
	}

	// 返回内容收益详情
	return c.Success(ctx, contentRevenue)
}

// CreateContentRevenue 创建内容收益
func (c *ContentRevenueController) CreateContentRevenue(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.CreateContentRevenueRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 创建内容收益
	contentRevenue := &contentCreatorModel.ContentRevenue{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentRevenue); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	id, err := c.ContentRevenueService.Create(ctx.Context(), contentRevenue)
	if err != nil {
		return c.InternalServerError(ctx, "创建内容收益失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建内容收益成功",
	})
}

// UpdateContentRevenue 更新内容收益
func (c *ContentRevenueController) UpdateContentRevenue(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.UpdateContentRevenueRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新内容收益
	contentRevenue := &contentCreatorModel.ContentRevenue{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentRevenue); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	err := c.ContentRevenueService.Update(ctx.Context(), contentRevenue)
	if err != nil {
		return c.InternalServerError(ctx, "更新内容收益失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新内容收益成功")
}

// DeleteContentRevenue 删除内容收益
func (c *ContentRevenueController) DeleteContentRevenue(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除内容收益
	err = c.ContentRevenueService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除内容收益失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除内容收益成功")
}

// ListContentRevenuesByContent 根据内容获取收益列表
func (c *ContentRevenueController) ListContentRevenuesByContent(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()
	userID := reqInfo.Get("user_id").GetString()
	condition := map[string]interface{}{
		"content_type": contentType,
		"content_id":   contentID,
		"user_id":      userID,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询内容收益列表
	list, total, err := c.ContentRevenueService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容收益列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// ListContentRevenuesByUser 根据用户获取收益列表
func (c *ContentRevenueController) ListContentRevenuesByUser(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	userID := reqInfo.Get("user_id").GetString()
	condition := map[string]interface{}{
		"user_id": userID,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询内容收益列表
	list, total, err := c.ContentRevenueService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取用户收益列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}
