/**
 * 缓存插件 - 提供多层缓存管理功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'
import { storage } from '../../utils/storage';

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  key: string
  value: T
  expiresAt?: number
  createdAt: number
  accessedAt: number
  accessCount: number
  tags?: string[]
  metadata?: Record<string, any>
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 默认过期时间（毫秒） */
  defaultTTL: number
  /** 最大缓存项数量 */
  maxItems: number
  /** 清理策略 */
  evictionPolicy: 'lru' | 'lfu' | 'fifo' | 'ttl'
  /** 是否启用持久化 */
  persistent: boolean
  /** 持久化存储键前缀 */
  storagePrefix: string
  /** 是否启用压缩 */
  compression: boolean
  /** 是否启用统计 */
  enableStats: boolean
  /** 清理间隔（毫秒） */
  cleanupInterval: number
}

/**
 * 缓存统计
 */
export interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  evictions: number
  hitRate: number
  totalItems: number
  totalSize: number
}

/**
 * 缓存管理器类
 */
export class CacheManager {
  private config: CacheConfig
  private cache = new Map<string, CacheItem>()
  private stats: CacheStats
  private cleanupTimer?: number
  private accessOrder: string[] = [] // 用于LRU
  private accessFrequency = new Map<string, number>() // 用于LFU

  constructor(config: CacheConfig) {
    this.config = config
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
      totalItems: 0,
      totalSize: 0
    }
    this.init()
  }

  /**
   * 初始化缓存管理器
   */
  private init() {
    if (this.config.persistent) {
      this.loadFromStorage()
    }
    
    if (this.config.cleanupInterval > 0) {
      this.startCleanupTimer()
    }
  }

  /**
   * 从存储中加载缓存
   */
  private loadFromStorage() {
    try {
      const keys = storage.keys().filter(key => 
        key.startsWith(this.config.storagePrefix)
      )
      
      for (const storageKey of keys) {
        const data = storage.get(storageKey)
        if (data) {
          const item: CacheItem = JSON.parse(data)
          const key = storageKey.replace(this.config.storagePrefix, '')
          
          // 检查是否过期
          if (!this.isExpired(item)) {
            this.cache.set(key, item)
            this.updateAccessOrder(key)
          } else {
            storage.remove(storageKey)
          }
        }
      }
      
      this.updateStats()
    } catch (error) {
      console.error('Failed to load cache from storage:', error)
    }
  }

  /**
   * 保存到存储
   */
  private saveToStorage(key: string, item: CacheItem) {
    if (!this.config.persistent) {
      return
    }
    
    try {
      const storageKey = this.config.storagePrefix + key
      const data = JSON.stringify(item)
      storage.set(storageKey, data)
    } catch (error) {
      console.error('Failed to save cache to storage:', error)
    }
  }

  /**
   * 从存储中删除
   */
  private removeFromStorage(key: string) {
    if (!this.config.persistent) {
      return
    }
    
    try {
      const storageKey = this.config.storagePrefix + key
      storage.remove(storageKey)
    } catch (error) {
      console.error('Failed to remove cache from storage:', error)
    }
  }

  /**
   * 设置缓存项
   */
  set<T>(key: string, value: T, ttl?: number, tags?: string[]): void {
    const now = Date.now()
    const expiresAt = ttl ? now + ttl : 
      (this.config.defaultTTL > 0 ? now + this.config.defaultTTL : undefined)
    
    const item: CacheItem<T> = {
      key,
      value,
      expiresAt,
      createdAt: now,
      accessedAt: now,
      accessCount: 0,
      tags,
      metadata: {}
    }
    
    // 检查是否需要清理空间
    if (this.cache.size >= this.config.maxItems && !this.cache.has(key)) {
      this.evict()
    }
    
    this.cache.set(key, item)
    this.updateAccessOrder(key)
    this.saveToStorage(key, item)
    
    if (this.config.enableStats) {
      this.stats.sets++
      this.updateStats()
    }
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key)
    
    if (!item) {
      if (this.config.enableStats) {
        this.stats.misses++
        this.updateStats()
      }
      return undefined
    }
    
    // 检查是否过期
    if (this.isExpired(item)) {
      this.delete(key)
      if (this.config.enableStats) {
        this.stats.misses++
        this.updateStats()
      }
      return undefined
    }
    
    // 更新访问信息
    item.accessedAt = Date.now()
    item.accessCount++
    this.updateAccessOrder(key)
    this.updateAccessFrequency(key)
    
    if (this.config.enableStats) {
      this.stats.hits++
      this.updateStats()
    }
    
    return item.value as T
  }

  /**
   * 检查缓存项是否存在
   */
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) {
      return false
    }
    
    if (this.isExpired(item)) {
      this.delete(key)
      return false
    }
    
    return true
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const existed = this.cache.delete(key)
    
    if (existed) {
      this.removeFromStorage(key)
      this.removeFromAccessOrder(key)
      this.accessFrequency.delete(key)
      
      if (this.config.enableStats) {
        this.stats.deletes++
        this.updateStats()
      }
    }
    
    return existed
  }

  /**
   * 清空缓存
   */
  clear(): void {
    const keys = Array.from(this.cache.keys())
    
    this.cache.clear()
    this.accessOrder = []
    this.accessFrequency.clear()
    
    if (this.config.persistent) {
      keys.forEach(key => this.removeFromStorage(key))
    }
    
    this.updateStats()
  }

  /**
   * 根据标签删除缓存项
   */
  deleteByTag(tag: string): number {
    let deletedCount = 0
    
    for (const [key, item] of this.cache.entries()) {
      if (item.tags?.includes(tag)) {
        this.delete(key)
        deletedCount++
      }
    }
    
    return deletedCount
  }

  /**
   * 根据标签获取缓存项
   */
  getByTag<T>(tag: string): Array<{ key: string; value: T }> {
    const results: Array<{ key: string; value: T }> = []
    
    for (const [key, item] of this.cache.entries()) {
      if (item.tags?.includes(tag) && !this.isExpired(item)) {
        results.push({ key, value: item.value as T })
      }
    }
    
    return results
  }

  /**
   * 获取所有键
   */
  keys(): string[] {
    const validKeys: string[] = []
    
    for (const [key, item] of this.cache.entries()) {
      if (!this.isExpired(item)) {
        validKeys.push(key)
      } else {
        this.delete(key)
      }
    }
    
    return validKeys
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    this.cleanup() // 清理过期项
    return this.cache.size
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return item.expiresAt ? Date.now() > item.expiresAt : false
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    let cleanedCount = 0
    
    for (const [key, item] of this.cache.entries()) {
      if (this.isExpired(item)) {
        this.delete(key)
        cleanedCount++
      }
    }
    
    return cleanedCount
  }

  /**
   * 驱逐缓存项
   */
  private evict(): void {
    if (this.cache.size === 0) {
      return
    }
    
    let keyToEvict: string | undefined
    
    switch (this.config.evictionPolicy) {
      case 'lru':
        keyToEvict = this.accessOrder[0]
        break
      case 'lfu':
        keyToEvict = this.getLFUKey()
        break
      case 'fifo':
        keyToEvict = this.getFIFOKey()
        break
      case 'ttl':
        keyToEvict = this.getTTLKey()
        break
    }
    
    if (keyToEvict) {
      this.delete(keyToEvict)
      
      if (this.config.enableStats) {
        this.stats.evictions++
      }
    }
  }

  /**
   * 获取LFU键
   */
  private getLFUKey(): string | undefined {
    let minFrequency = Infinity
    let keyToEvict: string | undefined
    
    for (const [key] of this.cache.entries()) {
      const frequency = this.accessFrequency.get(key) || 0
      if (frequency < minFrequency) {
        minFrequency = frequency
        keyToEvict = key
      }
    }
    
    return keyToEvict
  }

  /**
   * 获取FIFO键
   */
  private getFIFOKey(): string | undefined {
    let oldestTime = Infinity
    let keyToEvict: string | undefined
    
    for (const [key, item] of this.cache.entries()) {
      if (item.createdAt < oldestTime) {
        oldestTime = item.createdAt
        keyToEvict = key
      }
    }
    
    return keyToEvict
  }

  /**
   * 获取TTL键（最快过期的）
   */
  private getTTLKey(): string | undefined {
    let earliestExpiry = Infinity
    let keyToEvict: string | undefined
    
    for (const [key, item] of this.cache.entries()) {
      const expiresAt = item.expiresAt || Infinity
      if (expiresAt < earliestExpiry) {
        earliestExpiry = expiresAt
        keyToEvict = key
      }
    }
    
    return keyToEvict
  }

  /**
   * 更新访问顺序
   */
  private updateAccessOrder(key: string): void {
    this.removeFromAccessOrder(key)
    this.accessOrder.push(key)
  }

  /**
   * 从访问顺序中移除
   */
  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }
  }

  /**
   * 更新访问频率
   */
  private updateAccessFrequency(key: string): void {
    const current = this.accessFrequency.get(key) || 0
    this.accessFrequency.set(key, current + 1)
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    this.stats.totalItems = this.cache.size
    this.stats.hitRate = this.stats.hits + this.stats.misses > 0 ? 
      this.stats.hits / (this.stats.hits + this.stats.misses) : 0
    
    // 计算总大小（估算）
    let totalSize = 0
    for (const item of this.cache.values()) {
      totalSize += JSON.stringify(item).length
    }
    this.stats.totalSize = totalSize
  }

  /**
   * 获取统计信息
   */
  getStats(): CacheStats {
    this.updateStats()
    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
      totalItems: this.cache.size,
      totalSize: 0
    }
    this.updateStats()
  }

  /**
   * 获取缓存项信息
   */
  getItemInfo(key: string): Omit<CacheItem, 'value'> | undefined {
    const item = this.cache.get(key)
    if (!item || this.isExpired(item)) {
      return undefined
    }
    
    const { value, ...info } = item
    return info
  }

  /**
   * 设置缓存项元数据
   */
  setMetadata(key: string, metadata: Record<string, any>): boolean {
    const item = this.cache.get(key)
    if (!item || this.isExpired(item)) {
      return false
    }
    
    item.metadata = { ...item.metadata, ...metadata }
    this.saveToStorage(key, item)
    return true
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    this.stopCleanupTimer()
    this.clear()
  }
}

// 默认配置
const defaultConfig: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxItems: 1000,
  evictionPolicy: 'lru',
  persistent: false,
  storagePrefix: 'cache_',
  compression: false,
  enableStats: true,
  cleanupInterval: 60 * 1000 // 1分钟
}

// 全局缓存管理器实例
export const cacheManager = new CacheManager(defaultConfig)

/**
 * 缓存插件
 */
export const cachePlugin: Plugin = createPlugin({
  meta: {
    name: 'cache',
    version: '1.0.0',
    description: '多层缓存管理插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 2
  },
  install(context: PluginContext) {
    // 将缓存管理器添加到全局属性
    context.app.config.globalProperties.$cache = cacheManager
    
    // 提供缓存管理器
    context.app.provide('cache', cacheManager)
    
    console.log('Cache plugin installed')
  },
  uninstall() {
    cacheManager.destroy()
    console.log('Cache plugin uninstalled')
  }
})

/**
 * 使用缓存的组合式函数
 */
export function useCache() {
  return {
    cache: cacheManager,
    set: cacheManager.set.bind(cacheManager),
    get: cacheManager.get.bind(cacheManager),
    has: cacheManager.has.bind(cacheManager),
    delete: cacheManager.delete.bind(cacheManager),
    clear: cacheManager.clear.bind(cacheManager),
    deleteByTag: cacheManager.deleteByTag.bind(cacheManager),
    getByTag: cacheManager.getByTag.bind(cacheManager),
    keys: cacheManager.keys.bind(cacheManager),
    size: cacheManager.size.bind(cacheManager),
    cleanup: cacheManager.cleanup.bind(cacheManager),
    getStats: cacheManager.getStats.bind(cacheManager),
    resetStats: cacheManager.resetStats.bind(cacheManager),
    getItemInfo: cacheManager.getItemInfo.bind(cacheManager),
    setMetadata: cacheManager.setMetadata.bind(cacheManager)
  }
}

export default cachePlugin