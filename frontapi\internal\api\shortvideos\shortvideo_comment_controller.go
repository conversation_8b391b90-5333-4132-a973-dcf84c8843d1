package shortvideos

import (
	"frontapi/internal/admin"
	shortModel "frontapi/internal/models/shortvideos"
	"frontapi/internal/service/shortvideos"
	shortTypings "frontapi/internal/typings/shortvideos"
	shortvideoValidator "frontapi/internal/validation/shortvideos"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ShortVideoCommentController 短视频控制器
type ShortVideoCommentController struct {
	ShortVideoCommentService shortvideos.ShortVideoCommentService
	ShortVideoService        shortvideos.ShortVideoService
	admin.BaseController     // 继承BaseController
}

// NewShortVideoCommentController 创建短视频控制器实例
func NewShortVideoCommentController(
	shortVideoCommentService shortvideos.ShortVideoCommentService,
	shortVideoService shortvideos.ShortVideoService,
) *ShortVideoCommentController {
	return &ShortVideoCommentController{
		ShortVideoCommentService: shortVideoCommentService,
		ShortVideoService:        shortVideoService,
	}
}

// GetShortVideoComments 获取短视频评论列表
func (c *ShortVideoCommentController) GetShortVideoComments(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	shortVideoId := reqInfo.Get("short_id").GetString()
	if shortVideoId == "" {
		return c.BadRequest(ctx, "短视频ID不能为空", nil)
	}
	orderBy := "created_at DESC"
	// 查询短视频评论列表
	commentList, total, err := c.ShortVideoCommentService.List(ctx.Context(), map[string]interface{}{
		"short_id": shortVideoId,
		"status":   1, // 只获取正常状态的评论
	}, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取短视频评论列表失败: "+err.Error())
	}
	//判断是否like评论
	userID := c.GetUserID(ctx)
	for _, comment := range commentList {
		comment.IsLiked, err = c.ShortVideoCommentService.IsLiked(ctx.Context(), userID, comment.ID)
		if err != nil {
			return c.InternalServerError(ctx, "检查用户点赞状态失败: "+err.Error())
		}
	}
	commentResponseList := shortTypings.ConvertShortVideoCommentListResponse(commentList, total, page, pageSize)

	// 返回短视频评论列表
	return c.Success(ctx, commentResponseList)
}

// AddShortVideoComment 添加短视频评论
func (c *ShortVideoCommentController) AddShortVideoComment(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req shortvideoValidator.CreateCommentRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	var shortComment shortModel.ShortVideoComment
	utils.SmartCopy(req, &shortComment)
	// 创建评论
	id, err := c.ShortVideoCommentService.Create(ctx.Context(), &shortComment)
	if err != nil {
		return c.InternalServerError(ctx, "添加评论失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "添加评论成功",
	})
}

// LikeShortVideoComment 点赞短视频评论
func (c *ShortVideoCommentController) LikeShortVideoComment(ctx *fiber.Ctx) error {
	// 获取用户ID和评论ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	commentID := reqInfo.Get("comment_id").GetString()

	if userID == "" || commentID == "" {
		return c.BadRequest(ctx, "用户ID和评论ID不能为空", nil)
	}

	// 点赞评论
	err := c.ShortVideoCommentService.Like(ctx.Context(), userID, commentID)
	if err != nil {
		return c.InternalServerError(ctx, "点赞失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "点赞成功")
}

// CancelLikeShortVideoComment 取消点赞短视频评论
func (c *ShortVideoCommentController) CancelLikeShortVideoComment(ctx *fiber.Ctx) error {
	// 获取用户ID和评论ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	commentID := reqInfo.Get("comment_id").GetString()

	if userID == "" || commentID == "" {
		return c.BadRequest(ctx, "用户ID和评论ID不能为空", nil)
	}

	// 取消点赞评论
	err := c.ShortVideoCommentService.Unlike(ctx.Context(), userID, commentID)
	if err != nil {
		return c.InternalServerError(ctx, "取消点赞失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "取消点赞成功")
}

// CheckUserLikedShortVideoComment 检查用户是否点赞过短视频评论
func (c *ShortVideoCommentController) CheckUserLikedShortVideoComment(ctx *fiber.Ctx) error {
	// 获取用户ID和评论ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	commentID := reqInfo.Get("comment_id").GetString()

	if userID == "" || commentID == "" {
		return c.BadRequest(ctx, "用户ID和评论ID不能为空", nil)
	}

	// 这里需要实现检查用户是否点赞过评论的逻辑
	// 暂时返回false，实际应该查询数据库
	isLiked := false

	return c.Success(ctx, fiber.Map{
		"is_liked": isLiked,
	})
}
