package bootstrap

import (
	"frontapi/internal/container"
	"sync"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

// InitApp 一站式应用初始化函数
// 此函数可以在main.go中一行代码完成所有初始化
func InitApp(app *fiber.App, services *container.ServiceContainer) {
	// 初始化轻量级控制器系统
}

// ServiceContainer 是对container.ServiceContainer的别名，用于向后兼容
var (
	serviceRegistry sync.Map
	initialized     bool
	initMutex       sync.Mutex
)

type ServiceContainer = container.ServiceContainer

// InitServices 初始化所有服务并返回服务容器
// 这个函数是为了保持向后兼容性而存在的，内部直接调用container包的初始化方法
func InitServices(db *gorm.DB) *ServiceContainer {
	return container.InitServices(db)
}

// InitControllerSystem 一站式初始化控制器系统
// 在应用启动时调用此函数，完成所有控制器相关的初始化工作
func InitControllerSystem(container *container.ServiceContainer) {
	// 初始化服务注册表
	InitServiceRegistry(container)
}

// InitServiceRegistry 初始化服务注册表
// 使用单例模式确保只初始化一次
func InitServiceRegistry(container *ServiceContainer) {
	initMutex.Lock()
	defer initMutex.Unlock()

	if initialized {
		return
	}

	// 注册核心服务
	registerCoreServices(container)

	initialized = true
}

// registerCoreServices 注册核心服务到全局注册表
func registerCoreServices(container *ServiceContainer) {
	// 用户相关服务
	serviceRegistry.Store("UserService", container.UserService)
	serviceRegistry.Store("UserFollowsService", container.UserFollowsService)
	// 图片相关服务
	// serviceRegistry.Store("PictureService", container.PictureService)
	// serviceRegistry.Store("PictureAlbumService", container.PictureAlbumService)
	// serviceRegistry.Store("PictureCategoryService", container.PictureCategoryService)

	// // 视频相关服务
	// serviceRegistry.Store("VideoService", container.VideoService)
	// serviceRegistry.Store("VideoCategoryService", container.VideoCategoryService)
	// serviceRegistry.Store("VideoCommentService", container.VideoCommentService)

	// // 短视频相关服务
	// serviceRegistry.Store("ShortVideoService", container.ShortVideoService)
	// serviceRegistry.Store("ShortVideoCategoryService", container.ShortVideoCategoryService)
	// serviceRegistry.Store("ShortVideoCommentService", container.ShortVideoCommentService)

	// // 帖子相关服务
	// serviceRegistry.Store("PostService", container.PostService)
	// serviceRegistry.Store("PostCommentService", container.PostCommentService)
}

// GetService 获取服务实例
// 使用泛型风格的接口设计，允许直接获取具体类型
func GetService(name string) interface{} {
	if value, ok := serviceRegistry.Load(name); ok {
		return value
	}
	return nil
}
