package content_creator

import (
	"frontapi/internal/models/content_creator"
	repo "frontapi/internal/repository/content_creator"
	"frontapi/internal/service/base"
)

type ContentAuditService interface {
	base.IExtendedService[content_creator.ContentAudit]
}

type contentAuditService struct {
	*base.ExtendedService[content_creator.ContentAudit]
	repo repo.ContentAuditRepository
}

func NewContentAuditService(repo repo.ContentAuditRepository) ContentAuditService {
	baseService := base.NewExtendedService[content_creator.ContentAudit](repo, "content_audit")
	return &contentAuditService{
		ExtendedService: baseService,
		repo:            repo,
	}
}
