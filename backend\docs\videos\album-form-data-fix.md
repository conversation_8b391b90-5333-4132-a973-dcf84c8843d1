# 视频专辑表单数据修复文档

## 问题描述

### 问题1：添加专辑时保留之前修改的数据
- **现象**：点击添加专辑按钮时，如果之前打开过编辑对话框，表单会保留之前编辑的数据
- **原因**：前端表单数据没有在对话框打开时正确重置

### 问题2：修改专辑时创建者和分类没有正确保存
- **现象**：编辑专辑保存后，创建者和分类信息没有更新到数据库
- **原因**：后端更新逻辑不支持清空操作，只在有值时才更新

## 修复方案

### 前端修复 (AlbumFormDialog.vue)

#### 1. 修复表单数据重置逻辑
```typescript
// 监听props变化
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 每次打开对话框都先重置表单
    resetForm();
    
    if (props.type === 'edit' && props.albumData) {
      // 编辑模式：填充数据
      await nextTick(); // 等待DOM更新
      Object.assign(formData, {
        ...props.albumData,
        tags: props.albumData.tags || []
      });
      await loadExistingData();
    }
    // 添加模式：保持默认的重置状态
  }
});

// 监听对话框类型变化，确保类型切换时重置数据
watch(() => props.type, () => {
  if (props.visible) {
    resetForm();
  }
});
```

#### 2. 修复API调用
```typescript
// 修复API导入路径
const { addVideoAlbum } = await import('@/service/api/videos/albums');
const { updateVideoAlbum } = await import('@/service/api/videos/albums');
```

### 后端修复 (video_album_controller.go)

#### 1. 修复更新逻辑，支持清空操作
```go
// 处理UserID、CategoryID和Cover（因为类型不匹配）
// 支持清空操作：如果传入空字符串，则清空对应字段
videoAlbum.UserID = null.StringFromPtr(&req.UserID)
videoAlbum.CategoryID = null.StringFromPtr(&req.CategoryID)
if req.Cover != "" {
    videoAlbum.Cover = null.StringFrom(req.Cover)
} else {
    videoAlbum.Cover = null.String{} // 清空封面
}

// 处理创建者信息
if req.UserID != "" {
    // 查询用户信息并填充
    user, err := h.userService.GetByID(c.Context(), req.UserID, false)
    if err != nil {
        return h.BadRequest(c, "指定的创建者不存在", nil)
    }
    if user != nil {
        if user.Nickname.Valid {
            videoAlbum.UserNickname = null.StringFrom(user.Nickname.String)
        } else {
            videoAlbum.UserNickname = null.String{} // 清空昵称
        }
        if user.Avatar.Valid {
            videoAlbum.UserAvatar = null.StringFrom(user.Avatar.String)
        } else {
            videoAlbum.UserAvatar = null.String{} // 清空头像
        }
    }
} else {
    // 如果用户ID为空，清空所有相关字段
    videoAlbum.UserNickname = null.String{}
    videoAlbum.UserAvatar = null.String{}
}

// 处理分类信息
if req.CategoryID != "" {
    // 查询分类信息并填充
    category, err := h.videoCategoryService.GetByID(c.Context(), req.CategoryID, false)
    if err != nil {
        return h.BadRequest(c, "指定的分类不存在", nil)
    }
    if category != nil {
        if category.Name.Valid {
            videoAlbum.CategoryName = null.StringFrom(category.Name.String)
        } else {
            videoAlbum.CategoryName = null.String{} // 清空分类名称
        }
    }
} else {
    // 如果分类ID为空，清空分类名称
    videoAlbum.CategoryName = null.String{}
}
```

### API修复 (albums.ts)

#### 修复添加专辑API的URL
```typescript
export function addVideoAlbum(data: { data: VideoAlbumData }) {
    return request({
        url: '/video-albums/add', // 修复：原来是 '/videos-albums/add'
        method: 'post',
        data: data
    });
}
```

## 修复效果

### 问题1解决
- ✅ 每次打开添加对话框时，表单数据都会被重置为默认值
- ✅ 不会保留之前编辑时的数据
- ✅ 编辑模式下正确加载现有数据

### 问题2解决
- ✅ 支持清空创建者：传入空字符串时清空创建者相关字段
- ✅ 支持清空分类：传入空字符串时清空分类相关字段
- ✅ 更新时正确保存创建者和分类信息
- ✅ 自动查询并填充创建者昵称、头像和分类名称

## 技术要点

1. **前端数据重置**：使用 `watch` 监听器在对话框打开时强制重置表单数据
2. **后端清空支持**：使用 `null.StringFromPtr` 支持空字符串转换为null值
3. **关联数据处理**：自动查询用户和分类信息，填充相关字段
4. **API路径修复**：统一使用正确的API端点

## 测试建议

1. **添加专辑测试**：
   - 先编辑一个专辑，然后点击添加，确认表单为空
   - 添加专辑时选择创建者和分类，确认保存成功

2. **编辑专辑测试**：
   - 编辑专辑时清空创建者，确认保存后创建者字段为空
   - 编辑专辑时清空分类，确认保存后分类字段为空
   - 编辑专辑时更改创建者和分类，确认保存后正确更新

3. **数据一致性测试**：
   - 确认创建者信息（昵称、头像）与用户表数据一致
   - 确认分类名称与分类表数据一致 