# 金币套餐管理接口文档

## 金币套餐接口 (/coin_packages)

**注意**: 以下接口为预留接口，当前代码中已注释，具体实现需要根据业务需求进行开发。

### 1. 添加金币套餐

**接口地址**: `POST /api/coin_packages/add`

**接口描述**: 创建新的金币套餐

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "name": "套餐名称",
    "description": "套餐描述",
    "coin_amount": 1000,
    "price": 9.99,
    "original_price": 12.99,
    "discount_percentage": 23,
    "bonus_coins": 100,
    "icon_url": "套餐图标URL",
    "is_popular": false,
    "is_limited_time": false,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "sort_order": 1,
    "status": 1
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "套餐创建成功",
  "data": {
    "id": "package_id",
    "name": "套餐名称",
    "coin_amount": 1000,
    "price": 9.99,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 获取金币套餐详情

**接口地址**: `POST /api/coin_packages/detail/:id`

**接口描述**: 获取指定金币套餐的详细信息

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "include_purchase_history": false
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "package_id",
    "name": "套餐名称",
    "description": "套餐详细描述",
    "coin_amount": 1000,
    "price": 9.99,
    "original_price": 12.99,
    "discount_percentage": 23,
    "bonus_coins": 100,
    "icon_url": "套餐图标URL",
    "is_popular": true,
    "is_limited_time": false,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "sort_order": 1,
    "purchase_count": 500,
    "status": 1,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 更新金币套餐

**接口地址**: `POST /api/coin_packages/update`

**接口描述**: 更新金币套餐信息

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "id": "package_id",
    "name": "更新后的套餐名称",
    "description": "更新后的描述",
    "price": 8.99,
    "bonus_coins": 150,
    "is_popular": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "套餐更新成功",
  "data": {
    "id": "package_id",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 删除金币套餐

**接口地址**: `POST /api/coin_packages/delete/:id`

**接口描述**: 删除指定的金币套餐

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "confirm": true,
    "reason": "删除原因"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "套餐删除成功",
  "data": {
    "id": "package_id",
    "deleted_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. 获取金币套餐列表

**接口地址**: `POST /api/coin_packages/list`

**接口描述**: 获取金币套餐列表，支持分页和筛选

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "status": 1,
    "is_popular": null,
    "is_limited_time": null,
    "price_min": 0,
    "price_max": 100,
    "sort_by": "sort_order",
    "sort_order": "asc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "package_id",
        "name": "套餐名称",
        "description": "套餐描述",
        "coin_amount": 1000,
        "price": 9.99,
        "original_price": 12.99,
        "discount_percentage": 23,
        "bonus_coins": 100,
        "icon_url": "套餐图标URL",
        "is_popular": true,
        "is_limited_time": false,
        "sort_order": 1,
        "purchase_count": 500,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20
  }
}
```

## 金币套餐购买接口（扩展）

### 6. 购买金币套餐

**接口地址**: `POST /api/coin_packages/purchase`

**接口描述**: 购买指定的金币套餐

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "package_id": "package_id",
    "payment_method": "alipay",
    "user_id": "用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "购买成功",
  "data": {
    "order_id": "order_id",
    "package_id": "package_id",
    "coins_received": 1100,
    "amount_paid": 9.99,
    "payment_status": "completed",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 7. 获取购买历史

**接口地址**: `POST /api/coin_packages/purchase-history`

**接口描述**: 获取用户的金币套餐购买历史

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "status": "completed"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "purchase_id",
        "order_id": "order_id",
        "package_id": "package_id",
        "package_name": "套餐名称",
        "coins_received": 1100,
        "amount_paid": 9.99,
        "payment_method": "alipay",
        "payment_status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10
  }
}
```

## 状态码说明

- `2000`: 请求成功
- `4000`: 请求参数错误
- `4001`: 套餐不存在
- `4002`: 套餐已下架
- `4003`: 用户余额不足
- `4004`: 支付失败
- `4005`: 套餐已过期
- `5000`: 服务器内部错误

## 注意事项

1. 所有接口都使用 POST 方法
2. 请求和响应数据格式为 JSON
3. 价格单位为人民币（元）
4. 金币数量为整数
5. 时间格式统一使用 ISO 8601 格式
6. 当前所有接口为预留接口，需要根据实际业务需求进行实现
7. 购买相关接口需要集成支付系统
8. 建议添加购买限制和风控机制