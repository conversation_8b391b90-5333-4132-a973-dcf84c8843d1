@echo off
echo 运行缓存性能测试

:: 切换到当前目录
cd /d %~dp0

:: 设置 GOPATH
set GOPATH=%~dp0..\..

:: 运行测试
echo 测试Redis缓存性能（带本地缓存）...
go run test_cache_performance.go --adapter=redis --local=true --items=10000 --size=1024 --time=30
echo.

echo 测试Redis缓存性能（不带本地缓存）...
go run test_cache_performance.go --adapter=redis --local=false --items=10000 --size=1024 --time=30
echo.

echo 测试文件缓存性能...
go run test_cache_performance.go --adapter=file --items=10000 --size=1024 --time=30
echo.

echo 测试大数据缓存性能...
go run test_cache_performance.go --adapter=redis --items=1000 --size=102400 --time=30
echo.

echo 测试高并发缓存性能...
go run test_cache_performance.go --adapter=redis --concurrent=32 --items=10000 --size=1024 --time=30
echo.

echo 测试完成!
pause 