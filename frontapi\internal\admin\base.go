package admin

import (
	"frontapi/internal/bootstrap"
	"frontapi/internal/bootstrap/controller"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// BaseController 基础控制器，提供公共函数
type BaseController struct {
	*bootstrap.ControllerCore
	controller.ResponseTrait
	controller.RequestTrait
}

// NewBaseController 创建Admin基础控制器
func NewBaseController() BaseController {
	return BaseController{
		ControllerCore: bootstrap.NewControllerCore(),
		ResponseTrait:  controller.ResponseTrait{},
		RequestTrait:   controller.RequestTrait{},
	}
}

func (b BaseController) GetIdWithDataWrapper(c *fiber.Ctx) (string, error) {
	reqInfo := utils.GetRequestInfo(c)
	id := reqInfo.Get("id").GetString()
	if id == "" {
		id = c.Params("id")
		if id == "" {
			return "", utils.BadRequest(c, "参数ID必传", nil)
		}
	}
	return id, nil
}

func (b BaseController) ParseRequestData(c *fiber.Ctx, req interface{}) error {
	return utils.GetRequestInfo(c).ParseObject(req)
}

// IsAdmin 检查当前用户是否是管理员
func (b BaseController) IsAdmin(c *fiber.Ctx) bool {
	role := c.Locals("user_role")
	if role == nil {
		return false
	}

	return role.(string) == "admin"
}

// revertRequestToEntity 将ctx请求数据绑定到T entity中，支持post:form-data/x-www-form-urlencoded/json,get:query表单提交
func RevertRequestToEntity[T any](ctx *fiber.Ctx) (*T, error) {
	// 使用new()函数创建T类型的零值指针
	entity := new(T)

	if err := validator.ValidateDataWrapper(ctx, entity); err != nil {
		return nil, err
	}
	return entity, nil
}
