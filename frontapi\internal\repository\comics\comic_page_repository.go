package comics

import (
	"context"

	"gorm.io/gorm"

	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"
)

// ComicPageRepository 漫画页面数据访问接口
type ComicPageRepository interface {
	base.ExtendedRepository[comics.ComicPage]
	UpdateOrder(ctx context.Context, pageID string, newOrder int) error
	GetMaxPageNumber(ctx context.Context, chapterID string, comicId string) (int, error)
	BatchUpdateOrder(ctx context.Context, chapterID string, pages map[string]int) error
}

// comicPageRepository 漫画页面数据访问实现
type comicPageRepository struct {
	base.ExtendedRepository[comics.ComicPage]
}

// NewComicPageRepository 创建漫画页面仓库实例
func NewComicPageRepository(db *gorm.DB) ComicPageRepository {
	return &comicPageRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.ComicPage](db),
	}
}

// 获取章节最大页码
func (r *comicPageRepository) GetMaxPageNumber(ctx context.Context, chapterID string, comicId string) (int, error) {
	var maxPageNumber int
	err := r.GetDB().WithContext(ctx).Model(&comics.ComicPage{}).
		Where("comic_id = ? AND chapter_id = ?", comicId, chapterID).
		Select("COALESCE(MAX(page_number), 0)").
		Scan(&maxPageNumber).Error
	return maxPageNumber, err
}

// UpdateOrder 更新页面顺序
func (r *comicPageRepository) UpdateOrder(ctx context.Context, pageID string, newOrder int) error {
	// 先获取页面信息
	var page comics.ComicPage
	if err := r.GetDB().WithContext(ctx).Where("id = ?", pageID).First(&page).Error; err != nil {
		return err
	}

	// 如果新顺序与当前顺序相同，直接返回
	if page.PageNumber == newOrder {
		return nil
	}

	// 开始事务
	return r.GetDB().WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 更新其他页面的顺序
		if page.PageNumber < newOrder {
			// 当前页面顺序变大，中间页面顺序减1
			if err := tx.Model(&comics.ComicPage{}).
				Where("chapter_id = ? AND page_number > ? AND page_number <= ?", page.ChapterID, page.PageNumber, newOrder).
				UpdateColumn("page_number", gorm.Expr("page_number - 1")).
				Error; err != nil {
				return err
			}
		} else {
			// 当前页面顺序变小，中间页面顺序加1
			if err := tx.Model(&comics.ComicPage{}).
				Where("chapter_id = ? AND page_number >= ? AND page_number < ?", page.ChapterID, newOrder, page.PageNumber).
				UpdateColumn("page_number", gorm.Expr("page_number + 1")).
				Error; err != nil {
				return err
			}
		}

		// 更新当前页面顺序
		return tx.Model(&page).UpdateColumn("page_number", newOrder).Error
	})
}

// BatchUpdateOrder 批量更新页面顺序
func (r *comicPageRepository) BatchUpdateOrder(ctx context.Context, chapterID string, pages map[string]int) error {
	// 开启事务
	tx := r.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 遍历更新每个页面
	for pageID, pageNumber := range pages {
		// 更新页面序号
		err := tx.WithContext(ctx).
			Model(&comics.ComicPage{}).
			Where("id = ? AND chapter_id = ?", pageID, chapterID).
			Update("page_number", pageNumber).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}
