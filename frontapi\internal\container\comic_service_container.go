package container

import (
	comicRepo "frontapi/internal/repository/comics"
	comicServices "frontapi/internal/service/comics"
)

// InitComicServices 初始化漫画相关服务
func InitComicServices(b *ServiceBuilder) {
	// 初始化漫画仓库
	comicRepoImpl := comicRepo.NewComicRepository(b.DB())
	comicCategoryRepo := comicRepo.NewComicCategoryRepository(b.DB())
	comicChapterRepo := comicRepo.NewComicChapterRepository(b.DB())
	comicPageRepo := comicRepo.NewComicPageRepository(b.DB())
	comicFavoriteRepo := comicRepo.NewComicFavoriteRepository(b.DB())
	comicCommentRepo := comicRepo.NewComicCommentRepository(b.DB())
	comicReadHistoryRepo := comicRepo.NewComicReadHistoryRepository(b.DB())

	// 初始化漫画服务
	container := b.Services()
	container.ComicService = comicServices.NewComicService(
		comicRepoImpl,
		comicCategoryRepo,
		comicChapterRepo,
		comicFavoriteRepo,
		comicReadHistoryRepo,
	)
	container.ComicChapterService = comicServices.NewComicChapterService(
		comicChapterRepo,
		comicRepoImpl,
		comicPageRepo,
	)
	container.ComicFavoriteService = comicServices.NewComicFavoriteService(comicFavoriteRepo, comicRepoImpl)
	container.ComicPageService = comicServices.NewComicPageService(comicPageRepo, comicChapterRepo)
	container.ComicCategoryService = comicServices.NewComicCategoryService(comicCategoryRepo)
	container.ComicCommentService = comicServices.NewComicCommentService(comicCommentRepo, comicRepoImpl)
	container.ComicReadHistoryService = comicServices.NewComicReadHistoryService(
		comicReadHistoryRepo,
		comicRepoImpl,
		comicChapterRepo,
	)
}
