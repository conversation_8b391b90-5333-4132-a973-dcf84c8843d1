package v2

import (
	"context"
	"fmt"
	"strconv"

	"frontapi/internal/service/base/extcollect/types"
)

// StatsOperations 统计操作处理器
type StatsOperations struct {
	client *RedisClient
}

// NewStatsOperations 创建统计操作处理器
func NewStatsOperations(client *RedisClient) *StatsOperations {
	return &StatsOperations{
		client: client,
	}
}

// GetUserCollectStats 获取用户收藏统计
func (s *StatsOperations) GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error) {
	userStatsKey := s.client.userStatsKey(userID)

	statsMap, err := s.client.Client().HGetAll(ctx, userStatsKey).Result()
	if err != nil {
		s.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取用户收藏统计失败: %w", err)
	}

	stats := &types.UserCollectStats{
		UserID: userID,
	}

	if totalStr, ok := statsMap["total_collects"]; ok {
		if total, err := strconv.ParseInt(totalStr, 10, 64); err == nil {
			stats.TotalCollects = total
		}
	}

	s.client.UpdateStats(1, 0, 0)
	return stats, nil
}

// GetItemCollectStats 获取项目收藏统计
func (s *StatsOperations) GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error) {
	countKey := s.client.countKey(itemType, itemID)

	// 获取当前收藏数
	count, err := s.client.Client().Get(ctx, countKey).Int64()
	if err != nil {
		count = 0 // 如果没有找到，设为0
	}

	stats := &types.ItemCollectStats{
		ItemID:        itemID,
		ItemType:      itemType,
		TotalCollects: count,
	}

	s.client.UpdateStats(1, 0, 0)
	return stats, nil
}

// GetCacheStats 获取缓存统计
func (s *StatsOperations) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	adapterStats := s.client.Stats()

	stats := map[string]interface{}{
		"hit_count":    adapterStats.HitCount,
		"miss_count":   adapterStats.MissCount,
		"error_count":  adapterStats.ErrorCount,
		"hit_rate":     adapterStats.HitRate,
		"last_updated": adapterStats.LastUpdated,
	}

	return stats, nil
}

// GetTrendingItems 获取趋势项目
func (s *StatsOperations) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error) {
	trendKey := s.client.trendKey(itemType, "daily")

	// 简化实现，获取热门项目排序
	results, err := s.client.Client().ZRevRangeWithScores(ctx, trendKey, 0, int64(limit-1)).Result()
	if err != nil {
		s.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取趋势项目失败: %w", err)
	}

	trends := make([]*types.CollectTrend, 0, len(results))
	for _, result := range results {
		trend := &types.CollectTrend{
			ItemID:       result.Member.(string),
			ItemType:     itemType,
			Score:        result.Score,
			CollectCount: int64(result.Score),
		}
		trends = append(trends, trend)
	}

	s.client.UpdateStats(1, 0, 0)
	return trends, nil
}
