# 智能查询功能使用指南

本文档介绍如何使用 frontapi 中的智能查询功能，特别是如何使用各种操作符进行复杂条件查询。

## 基本用法

智能查询功能允许你使用 `map[string]interface{}` 类型的条件参数来构建复杂的查询条件。基本用法如下：

```go
condition := map[string]interface{}{
    "status": 1,
    "keyword": "搜索关键词",
}

// 执行查询
items, total, err := repository.List(ctx, condition, "created_at DESC", 1, 10)
```

## 支持的操作符

### 1. 不等于操作符 (`<>`, `!=`)

用于查询不等于指定值的记录。

```go
// 查询ID不等于指定值的记录
condition := map[string]interface{}{
    "id <>": "some-id",
    "status": 1,
}
```

示例：获取推荐相关的专辑列表（不包含当前专辑）

```go
condition := map[string]interface{}{
    "id <>": albumId,
    "status": 1,
}
albums, total, err := albumRepo.List(ctx, condition, "created_at DESC", 1, 10)
```

### 2. 大于、小于操作符 (`>`, `<`, `>=`, `<=`)

用于数值比较查询。

```go
// 查询图片数量大于0的专辑
condition := map[string]interface{}{
    "picture_count >": 0,
    "status": 1,
}

// 查询创建时间在指定日期之后的记录
condition := map[string]interface{}{
    "created_at >": someDate,
    "status": 1,
}
```

### 3. IN 操作符

用于查询字段值在指定列表中的记录。

```go
// 查询ID在指定列表中的记录
ids := []string{"id1", "id2", "id3"}
condition := map[string]interface{}{
    "id IN": ids,
    "status": 1,
}
```

示例：获取指定多个分类下的专辑

```go
categoryIDs := []string{"cat1", "cat2", "cat3"}
condition := map[string]interface{}{
    "category_id IN": categoryIDs,
    "status": 1,
}
albums, total, err := albumRepo.List(ctx, condition, "created_at DESC", 1, 10)
```

### 4. NOT IN 操作符

用于查询字段值不在指定列表中的记录。

```go
// 查询ID不在指定列表中的记录
excludeIDs := []string{"id1", "id2", "id3"}
condition := map[string]interface{}{
    "id NOT IN": excludeIDs,
    "status": 1,
}
```

示例：获取除了指定分类外的所有专辑

```go
excludeCategoryIDs := []string{"cat1", "cat2"}
condition := map[string]interface{}{
    "category_id NOT IN": excludeCategoryIDs,
    "status": 1,
}
albums, total, err := albumRepo.List(ctx, condition, "created_at DESC", 1, 10)
```

## 组合使用多个条件

可以在同一个查询中组合使用多种操作符：

```go
// 复杂查询示例
condition := map[string]interface{}{
    "id NOT IN": excludeIDs,
    "category_id IN": categoryIDs,
    "picture_count >": 0,
    "view_count >": 100,
    "status": 1,
    "keyword": "风景",
}
```

这个查询将会：
1. 排除指定ID的记录
2. 只包含指定分类的记录
3. 只包含图片数量大于0的记录
4. 只包含浏览次数大于100的记录
5. 只包含状态为1的记录
6. 标题或描述中包含"风景"关键词的记录

## 注意事项

1. 操作符前后需要有空格，例如 `"id <>"` 而不是 `"id<>"`
2. 对于 `IN` 和 `NOT IN` 操作符，值必须是切片或数组类型
3. 对于比较操作符，确保值的类型与数据库字段类型兼容
4. 空值或nil值的条件会被自动忽略 