# Themes 组件样式统一优化

## 修改概述
统一优化 `backend/src/components/themes` 目录下所有组件的样式，实现border-radius统一和去除悬浮效果。

## 修改原则

### 1. Border-radius 统一
- 所有组件的 `border-radius` 统一使用 CSS 变量 `var(--el-card-border-radius)`
- 避免硬编码数值，确保与 Element Plus 设计系统保持一致

### 2. 边框颜色统一
- 表格标题栏边框使用 `var(--el-table-border)` 变量
- 确保与 Element Plus 表格组件风格统一

### 3. 去除悬浮效果
- 移除不必要的 `transform: translateY()` 悬浮动画
- 移除 `box-shadow` 悬浮阴影效果
- 保留基本的 `:hover` 状态变化（如颜色变化）

## 修改文件清单

### 1. SlinkyTable.vue
**位置**: `backend/src/components/themes/slinky/tables/SlinkyTable.vue`
**修改内容**:
- 统一使用 `var(--el-card-border-radius)`
- 表格标题栏边框改为 `var(--el-table-border)`
- 去除行悬浮时的 `transform` 和 `box-shadow` 效果
- 深色模式下的边框也使用统一变量

### 2. ActionTable.vue
**位置**: `backend/src/components/themes/slinky/tables/ActionTable.vue`
**修改内容**:
- 搜索区域的 `border-radius` 改为 `var(--el-card-border-radius)`

### 3. SearchBar.vue
**位置**: `backend/src/components/themes/slinky/searchbox/SearchBar.vue`
**修改内容**:
- 主容器 `border-radius` 改为 `var(--el-card-border-radius)`
- 输入框包装器 `border-radius` 改为 `var(--el-card-border-radius)`
- 按钮 `border-radius` 改为 `var(--el-card-border-radius)`
- 去除主按钮的悬浮 `transform` 和 `box-shadow` 效果

### 4. SinglePager.vue
**位置**: `backend/src/components/themes/slinky/pager/SinglePager.vue`
**修改内容**:
- 分页器各元素 `border-radius` 改为 `var(--el-card-border-radius)`
- 去除分页按钮和输入框的悬浮效果
- 保留激活状态的样式

### 5. EditDialog.vue
**位置**: `backend/src/components/themes/slinky/dialog/EditDialog.vue`
**修改内容**:
- 变更项容器 `border-radius` 改为 `var(--el-card-border-radius)`
- 变更指示器的 `border-radius` 改为 `var(--el-card-border-radius)`

### 6. DetailDialog.vue
**位置**: `backend/src/components/themes/slinky/dialog/DetailDialog.vue`
**修改内容**:
- 图片容器 `border-radius` 改为 `var(--el-card-border-radius)`
- 卡片阴影效果从 `shadow="hover"` 改为 `shadow="never"`

### 7. BatchActionDialog.vue
**位置**: `backend/src/components/themes/slinky/dialog/BatchActionDialog.vue`
**修改内容**:
- 项目列表容器 `border-radius` 改为 `var(--el-card-border-radius)`
- 警告文本容器 `border-radius` 改为 `var(--el-card-border-radius)`
- 滚动条 `border-radius` 改为 `var(--el-card-border-radius)`
- 去除列表项悬浮效果和滚动条悬浮效果

## 技术细节

### CSS 变量使用
```scss
// 推荐使用
border-radius: var(--el-card-border-radius);

// 不推荐的硬编码
border-radius: 4px;
border-radius: 6px;
border-radius: 8px;
```

### 表格边框变量
```scss
// 表格标题栏边框
border: 1px solid var(--el-table-border);
border-bottom: 2px solid var(--el-table-border);
```

### 悬浮效果简化
```scss
// 修改前 - 复杂悬浮效果
&:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

// 修改后 - 简化悬浮效果
&:hover {
  background-color: var(--el-table-row-hover-bg-color);
}
```

## 设计理念

### 1. 一致性优先
- 与 Element Plus 设计系统保持高度一致
- 所有组件使用统一的设计语言

### 2. 性能优化
- 减少不必要的动画效果
- 降低浏览器重绘和重排的性能消耗

### 3. 用户体验
- 保持简洁专业的视觉效果
- 避免过度炫酷的动画干扰用户操作

## 兼容性说明
- 保持深色模式完全兼容
- 保持响应式设计不变
- Element Plus 组件功能完全保留

## 验证方法
1. 检查所有组件的圆角是否统一
2. 验证表格标题栏边框颜色是否与表格主体一致
3. 确认悬浮效果简化但交互反馈正常
4. 测试深色模式下的样式表现

修改时间：2024年12月19日 