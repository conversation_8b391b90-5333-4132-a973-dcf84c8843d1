package home

import (
	"context"
	"frontapi/internal/models/home"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// HotKeywordRepository 热搜关键词数据访问接口
type HotKeywordRepository interface {
	base.ExtendedRepository[home.HotKeyword]
	GetHotKeywords(ctx context.Context, limit int) ([]*home.HotKeyword, error)
	GetHotKeywordsByCategory(ctx context.Context, category string, limit int) ([]*home.HotKeyword, error)
}

// hotKeywordRepository 热搜关键词数据访问实现
type hotKeywordRepository struct {
	base.ExtendedRepository[home.HotKeyword]
}

// NewHotKeywordRepository 创建热搜关键词仓库实例
func NewHotKeywordRepository(db *gorm.DB) HotKeywordRepository {
	return &hotKeywordRepository{
		ExtendedRepository: base.NewExtendedRepository[home.HotKeyword](db),
	}
}

// GetHotKeywords 获取热搜关键词列表
func (r *hotKeywordRepository) GetHotKeywords(ctx context.Context, limit int) ([]*home.HotKeyword, error) {
	condition := map[string]interface{}{
		"status": 1,
	}
	return r.FindByCondition(ctx, condition, "sort ASC, search_count DESC")
}

// GetHotKeywordsByCategory 获取指定分类的热搜关键词列表
func (r *hotKeywordRepository) GetHotKeywordsByCategory(ctx context.Context, category string, limit int) ([]*home.HotKeyword, error) {
	condition := map[string]interface{}{
		"status":   1,
		"category": category,
	}
	return r.FindByCondition(ctx, condition, "sort ASC, search_count DESC")
}
