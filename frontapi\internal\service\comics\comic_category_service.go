package comics

import (
	"frontapi/internal/models/comics"
	comicRepo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
)

// ComicCategoryService 漫画分类服务接口
type ComicCategoryService interface {
	base.IExtendedService[comics.ComicCategory]
}

// comicCategoryService 漫画分类服务实现
type comicCategoryService struct {
	*base.ExtendedService[comics.ComicCategory]
	repo comicRepo.ComicCategoryRepository // 保留repo用于特殊查询方法
}

// NewComicCategoryService 创建漫画分类服务实例
func NewComicCategoryService(repo comicRepo.ComicCategoryRepository) ComicCategoryService {
	return &comicCategoryService{
		ExtendedService: base.NewExtendedService[comics.ComicCategory](repo, "comic_category"),
		repo:            repo,
	}
}
