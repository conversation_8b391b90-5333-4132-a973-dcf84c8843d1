package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoClient MongoDB客户端包装器
type MongoClient struct {
	client    *mongo.Client
	database  *mongo.Database
	config    *MongoConfig
	keyPrefix string
}

// MongoConfig MongoDB配置
type MongoConfig struct {
	// 基础连接配置
	Enabled    bool   `json:"enabled" yaml:"enabled"`
	UseSystem  bool   `json:"use_system" yaml:"use_system"`
	UseCustom  bool   `json:"use_custom" yaml:"use_custom"`
	URI        string `json:"uri" yaml:"uri"`
	Database   string `json:"database" yaml:"database"`
	Username   string `json:"username" yaml:"username"`
	Password   string `json:"password" yaml:"password"`
	AuthSource string `json:"auth_source" yaml:"auth_source"`

	// 连接池配置
	MaxPoolSize    uint64        `json:"max_pool_size" yaml:"max_pool_size"`
	MinPoolSize    uint64        `json:"min_pool_size" yaml:"min_pool_size"`
	MaxIdleTime    time.Duration `json:"max_idle_time" yaml:"max_idle_time"`
	ConnectTimeout time.Duration `json:"connect_timeout" yaml:"connect_timeout"`

	// 操作配置
	Timeout        time.Duration `json:"timeout" yaml:"timeout"`
	ReadPreference string        `json:"read_preference" yaml:"read_preference"`
	WriteConcern   string        `json:"write_concern" yaml:"write_concern"`
	ReadConcern    string        `json:"read_concern" yaml:"read_concern"`

	// 集合配置
	LikeCollection      string `json:"like_collection" yaml:"like_collection"`
	StatsCollection     string `json:"stats_collection" yaml:"stats_collection"`
	TrendCollection     string `json:"trend_collection" yaml:"trend_collection"`
	RankingCollection   string `json:"ranking_collection" yaml:"ranking_collection"`
	OperationCollection string `json:"operation_collection" yaml:"operation_collection"`

	// 索引配置
	EnableIndexes bool `json:"enable_indexes" yaml:"enable_indexes"`
	CreateIndexes bool `json:"create_indexes" yaml:"create_indexes"`

	// 性能配置
	BatchSize        int           `json:"batch_size" yaml:"batch_size"`
	BulkWriteTimeout time.Duration `json:"bulk_write_timeout" yaml:"bulk_write_timeout"`
	EnablePipeline   bool          `json:"enable_pipeline" yaml:"enable_pipeline"`

	// 数据保留配置
	DataRetention time.Duration `json:"data_retention" yaml:"data_retention"`
	EnableTTL     bool          `json:"enable_ttl" yaml:"enable_ttl"`
	TTLField      string        `json:"ttl_field" yaml:"ttl_field"`

	// 一致性配置
	ConsistencyLevel string `json:"consistency_level" yaml:"consistency_level"`
	RetryWrites      bool   `json:"retry_writes" yaml:"retry_writes"`
	RetryReads       bool   `json:"retry_reads" yaml:"retry_reads"`
}

// DefaultMongoConfig 默认MongoDB配置
func DefaultMongoConfig() *MongoConfig {
	return &MongoConfig{
		Enabled:             true,
		UseSystem:           true,
		UseCustom:           false,
		URI:                 "mongodb://localhost:27017",
		Database:            "like_service",
		AuthSource:          "admin",
		MaxPoolSize:         100,
		MinPoolSize:         5,
		MaxIdleTime:         30 * time.Minute,
		ConnectTimeout:      10 * time.Second,
		Timeout:             5 * time.Second,
		ReadPreference:      "primary",
		WriteConcern:        "majority",
		ReadConcern:         "majority",
		LikeCollection:      "likes",
		StatsCollection:     "like_stats",
		TrendCollection:     "like_trends",
		RankingCollection:   "like_rankings",
		OperationCollection: "like_operations",
		EnableIndexes:       true,
		CreateIndexes:       true,
		BatchSize:           1000,
		BulkWriteTimeout:    30 * time.Second,
		EnablePipeline:      true,
		DataRetention:       365 * 24 * time.Hour, // 1年
		EnableTTL:           false,
		TTLField:            "created_at",
		ConsistencyLevel:    "strong",
		RetryWrites:         true,
		RetryReads:          true,
	}
}

// NewMongoClient 创建MongoDB客户端
func NewMongoClient(client *mongo.Client, database *mongo.Database, config *MongoConfig, itemType string) (*MongoClient, error) {
	if config == nil {
		config = DefaultMongoConfig()
	}

	if client == nil || database == nil {
		return nil, fmt.Errorf("MongoDB客户端或数据库不能为空")
	}

	mc := &MongoClient{
		client:    client,
		database:  database,
		config:    config,
		keyPrefix: fmt.Sprintf("%s:", itemType),
	}

	// 创建索引
	if config.CreateIndexes {
		if err := mc.createIndexes(context.Background()); err != nil {
			return nil, fmt.Errorf("创建索引失败: %w", err)
		}
	}

	return mc, nil
}

// createIndexes 创建必要的索引
func (mc *MongoClient) createIndexes(ctx context.Context) error {
	if !mc.config.EnableIndexes {
		return nil
	}

	// 点赞记录索引
	likeCollection := mc.getLikeCollection()
	likeIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"user_id":   1,
				"item_id":   1,
				"item_type": 1,
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: map[string]interface{}{
				"item_id":   1,
				"item_type": 1,
			},
		},
		{
			Keys: map[string]interface{}{
				"user_id":   1,
				"timestamp": -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"timestamp": -1,
			},
		},
	}

	if mc.config.EnableTTL && mc.config.TTLField != "" {
		likeIndexes = append(likeIndexes, mongo.IndexModel{
			Keys: map[string]interface{}{
				mc.config.TTLField: 1,
			},
			Options: options.Index().SetExpireAfterSeconds(int32(mc.config.DataRetention.Seconds())),
		})
	}

	_, err := likeCollection.Indexes().CreateMany(ctx, likeIndexes)
	if err != nil {
		return fmt.Errorf("创建点赞记录索引失败: %w", err)
	}

	// 统计数据索引
	statsCollection := mc.getStatsCollection()
	statsIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"item_id":   1,
				"item_type": 1,
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: map[string]interface{}{
				"like_count": -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"updated_at": -1,
			},
		},
	}

	_, err = statsCollection.Indexes().CreateMany(ctx, statsIndexes)
	if err != nil {
		return fmt.Errorf("创建统计数据索引失败: %w", err)
	}

	// 排行榜索引
	rankingCollection := mc.getRankingCollection()
	rankingIndexes := []mongo.IndexModel{
		{
			Keys: map[string]interface{}{
				"item_type": 1,
				"score":     -1,
			},
		},
		{
			Keys: map[string]interface{}{
				"item_type": 1,
				"rank":      1,
			},
		},
	}

	_, err = rankingCollection.Indexes().CreateMany(ctx, rankingIndexes)
	if err != nil {
		return fmt.Errorf("创建排行榜索引失败: %w", err)
	}

	return nil
}

// 获取集合的辅助方法
func (mc *MongoClient) getLikeCollection() *mongo.Collection {
	return mc.database.Collection(mc.config.LikeCollection)
}

func (mc *MongoClient) getStatsCollection() *mongo.Collection {
	return mc.database.Collection(mc.config.StatsCollection)
}

func (mc *MongoClient) getTrendCollection() *mongo.Collection {
	return mc.database.Collection(mc.config.TrendCollection)
}

func (mc *MongoClient) getRankingCollection() *mongo.Collection {
	return mc.database.Collection(mc.config.RankingCollection)
}

func (mc *MongoClient) getOperationCollection() *mongo.Collection {
	return mc.database.Collection(mc.config.OperationCollection)
}

// generateCollectionName 生成集合名称
func (mc *MongoClient) generateCollectionName(baseCollection string) string {
	if mc.keyPrefix == "" {
		return baseCollection
	}
	return fmt.Sprintf("%s%s", mc.keyPrefix, baseCollection)
}

// GetClient 获取MongoDB客户端
func (mc *MongoClient) GetClient() *mongo.Client {
	return mc.client
}

// GetDatabase 获取数据库
func (mc *MongoClient) GetDatabase() *mongo.Database {
	return mc.database
}

// GetConfig 获取配置
func (mc *MongoClient) GetConfig() *MongoConfig {
	return mc.config
}

// CreateContext 创建带超时的上下文
func (mc *MongoClient) CreateContext(parent context.Context) (context.Context, context.CancelFunc) {
	if mc.config.Timeout > 0 {
		return context.WithTimeout(parent, mc.config.Timeout)
	}
	return parent, func() {}
}

// Ping 检查连接
func (mc *MongoClient) Ping(ctx context.Context) error {
	ctx, cancel := mc.CreateContext(ctx)
	defer cancel()

	return mc.client.Ping(ctx, nil)
}

// Close 关闭连接
func (mc *MongoClient) Close(ctx context.Context) error {
	return mc.client.Disconnect(ctx)
}
