package cache

import (
	"time"

	"frontapi/config"
	cacheConfig "frontapi/config/cache"
	"frontapi/pkg/cache/bigcache"
	"frontapi/pkg/cache/file"
	"frontapi/pkg/cache/redis"
	"frontapi/pkg/cache/types"
)

// createRedisAdapter 创建Redis缓存适配器
func CreateRedisAdapter(appConfig *config.Config) (types.CacheAdapter, error) {
	// 创建Redis配置
	redisConfig := &cacheConfig.RedisConfig{
		Host:         appConfig.Cache.Redis.Host,
		Port:         appConfig.Cache.Redis.Port,
		Password:     appConfig.Cache.Redis.Password,
		DB:           appConfig.Cache.Redis.DB,
		PoolSize:     appConfig.Cache.Redis.PoolSize,
		MinIdleConns: appConfig.Cache.Redis.MinIdleConns,
		MaxRetries:   appConfig.Cache.Redis.MaxRetries,
		Prefix:       "cache:", // 添加前缀防止与其他Redis键冲突
	}

	// 创建Redis适配器
	adapter, err := redis.NewAdapter(redisConfig)
	if err != nil {
		return nil, err
	}

	return adapter, nil
}

// createFileAdapter 创建文件缓存适配器
func CreateFileAdapter(appConfig *config.Config) (types.CacheAdapter, error) {
	// 创建文件缓存配置
	cleanupInterval := 10 * time.Minute
	if appConfig.Cache.File.CleanupInterval > 0 {
		cleanupInterval = time.Duration(appConfig.Cache.File.CleanupInterval) * time.Second
	}

	fileConfig := &file.Config{
		BasePath:        appConfig.Cache.File.Path,
		CleanupInterval: cleanupInterval,
		FileMode:        0644,
		DirMode:         0755,
	}

	// 创建文件缓存适配器
	adapter, err := file.NewAdapter(fileConfig)
	if err != nil {
		return nil, err
	}

	return adapter, nil
}

// createBigCacheAdapter 创建BigCache缓存适配器
func CreateBigCacheAdapter(appConfig *config.Config) (types.CacheAdapter, error) {
	// 创建BigCache配置
	lifeWindow := 10 * time.Minute
	cleanWindow := 5 * time.Minute
	if appConfig.Cache.BigCache.LifeWindow > 0 {
		lifeWindow = time.Duration(appConfig.Cache.BigCache.LifeWindow) * time.Second
	}
	if appConfig.Cache.BigCache.CleanWindow > 0 {
		cleanWindow = time.Duration(appConfig.Cache.BigCache.CleanWindow) * time.Second
	}

	shards := 1024
	if appConfig.Cache.BigCache.Shards > 0 {
		shards = appConfig.Cache.BigCache.Shards
	}

	maxEntriesInWindow := 1000 * 10 * 60
	if appConfig.Cache.BigCache.MaxEntriesInWindow > 0 {
		maxEntriesInWindow = appConfig.Cache.BigCache.MaxEntriesInWindow
	}

	maxEntrySize := 500
	if appConfig.Cache.BigCache.MaxEntrySize > 0 {
		maxEntrySize = appConfig.Cache.BigCache.MaxEntrySize
	}

	hardMaxCacheSize := 8192
	if appConfig.Cache.BigCache.HardMaxCacheSize > 0 {
		hardMaxCacheSize = appConfig.Cache.BigCache.HardMaxCacheSize
	}

	bigcacheConfig := &bigcache.Config{
		Shards:             shards,
		LifeWindow:         lifeWindow,
		CleanWindow:        cleanWindow,
		MaxEntriesInWindow: maxEntriesInWindow,
		MaxEntrySize:       maxEntrySize,
		HardMaxCacheSize:   hardMaxCacheSize,
		Prefix:             "cache:", // 添加前缀
	}

	// 创建BigCache适配器
	adapter, err := bigcache.NewAdapter(bigcacheConfig)
	if err != nil {
		return nil, err
	}

	return adapter, nil
}
