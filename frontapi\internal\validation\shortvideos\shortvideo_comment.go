package shortvideos

import (
	"github.com/guregu/null/v6"
)

// CreateCommentRequest 创建评论请求
type CreateCommentRequest struct {
	ShortID     null.String `json:"shortID" validate:"required"`
	Content     string      `json:"content" validate:"required"`
	UserID      null.String `json:"userID"`
	ReplyToID   null.String `json:"replyToID"`
	ReplyToUser string      `json:"replyToUser"`
	ParentID    null.String `json:"parentID"`
}

// UpdateCommentRequest 更新评论请求
type UpdateCommentRequest struct {
	Content string `json:"content" validate:"required"`
}
