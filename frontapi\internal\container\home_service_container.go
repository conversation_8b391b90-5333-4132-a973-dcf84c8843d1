package container

import (
	homeRepo "frontapi/internal/repository/home"
	tagRepo "frontapi/internal/repository/tags"
	homeService "frontapi/internal/service/home"
)

// InitHomeServices 初始化首页相关服务
func InitHomeServices(b *ServiceBuilder) {
	// 初始化首页仓库
	homeRepoImpl := homeRepo.NewHomeRepository(b.DB())
	tagRepoImpl := tagRepo.NewTagRepository(b.DB())

	hotKeywordRepo := homeRepo.NewHotKeywordRepository(b.DB())

	// 初始化首页服务
	container := b.Services()
	container.HomeService = homeService.NewHomeService(homeRepoImpl, tagRepoImpl)
	container.HotKeywordService = homeService.NewHotKeywordService(hotKeywordRepo)
}
