<template>
  <el-tag 
    :type="tagType" 
    :effect="effect"
    size="small"
    class="user-status-tag"
  >
    <el-icon v-if="statusIcon"><component :is="statusIcon" /></el-icon>
    {{ statusText }}
  </el-tag>
</template>

<script setup lang="ts">
import { Check, Close, QuestionFilled } from '@element-plus/icons-vue';
import { computed } from 'vue';

// Props
interface Props {
  status: number;
  effect?: 'dark' | 'light' | 'plain';
}

const props = withDefaults(defineProps<Props>(), {
  effect: 'light'
});

// 计算属性
const statusConfig = computed(() => {
  switch (props.status) {
    case 1:
      return {
        text: '正常',
        type: 'success' as const,
        icon: Check
      };
    case 0:
      return {
        text: '禁用',
        type: 'danger' as const,
        icon: Close
      };
    default:
      return {
        text: '未知',
        type: 'info' as const,
        icon: QuestionFilled
      };
  }
});

const statusText = computed(() => statusConfig.value.text);
const tagType = computed(() => statusConfig.value.type);
const statusIcon = computed(() => statusConfig.value.icon);
</script>

<style scoped lang="scss">
.user-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;

  .el-icon {
    font-size: 12px;
  }
}
</style> 