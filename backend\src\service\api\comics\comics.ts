import { request } from "../../request";
import type {
  ComicsParams,
  ComicsItem,
  CreateComicsRequest,
  UpdateComicsRequest,
  ComicsChapter,
  CreateComicsChapterRequest,
  UpdateComicsChapterRequest,
  ComicsPage,
  CreateComicsPageRequest,
  UpdateComicsPageRequest
} from '@/types/comics';
import type { ApiResponse } from '@/types/https';

/**
 * 获取漫画列表
 * @param params 查询参数
 */
export function getComicsList(params: ComicsParams) {
  return request<ApiResponse<any>>({
    url: '/comics/all',
    method: 'post',
    data: params
  });
}

/**
 * 获取漫画详情
 * @param id 漫画ID
 */
export function getComicsDetail(id: string) {
  return request<ApiResponse<ComicsItem>>({
    url: `/comics/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 创建漫画
 * @param data 漫画数据
 */
export function createComics(params: { data: CreateComicsRequest }) {
  return request<ApiResponse<any>>({
    url: '/comics/add',
    method: 'post',
    data: params
  });
}

/**
 * 更新漫画
 * @param id 漫画ID
 * @param data 漫画数据
 */
export function updateComics(id: string, params: { data: UpdateComicsRequest }) {
  return request<ApiResponse<any>>({
    url: `/comics/update/${id}`,
    method: 'post',
    data: params
  });
}

/**
 * 删除漫画
 * @param id 漫画ID
 */
export function deleteComics(id: string) {
  return request<ApiResponse<any>>({
    url: `/comics/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 更新漫画状态
 * @param id 漫画ID
 * @param status 状态
 */
export function updateComicsStatus(id: string, status: number) {
  return request<ApiResponse<any>>({
    url: `/comics/update-status/${id}`,
    method: 'post',
    data: { data: { "id": id, "status": status } }
  });
}

/**
 * 获取漫画章节列表
 * @param comicsId 漫画ID
 * @param params 分页参数
 */
export function getComicsChapterList(comicsId: string, params: ComicsParams) {
  return request<ApiResponse<any>>({
    url: '/comics/chapter/list',
    method: 'post',
    data: {
      ...params,
      data: {
        ...params.data,
        comics_id: comicsId
      }
    }
  });
}

/**
 * 获取漫画章节详情
 * @param id 章节ID
 */
export function getComicsChapterDetail(id: string) {
  return request<ApiResponse<ComicsChapter>>({
    url: `/comics/chapter/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 创建漫画章节
 * @param data 章节数据
 */
export function createComicsChapter(params: { data: CreateComicsChapterRequest }) {
  return request<ApiResponse<any>>({
    url: '/comics/chapter/add',
    method: 'post',
    data: params
  })
}

/**
 * 更新漫画章节
 * @param id 章节ID
 * @param data 章节数据
 */
export function updateComicsChapter(id: string, params: { data: UpdateComicsChapterRequest }) {
  return request<ApiResponse<any>>({
    url: `/comics/chapter/update/${id}`,
    method: 'post',
    data: params
  });
}

/**
 * 删除漫画章节
 * @param id 章节ID
 */
export function deleteComicsChapter(id: string) {
  return request<ApiResponse<any>>({
    url: `/comics/chapter/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 获取漫画页面列表
 * @param chapterId 章节ID
 * @param params 分页参数
 */
export function getComicsPageList(chapterId: string,comicId:string, params: ComicsParams) {
  return request<ApiResponse<any>>({
    url: '/comics/page/list',
    method: 'post',
    data: {
     ...params,
      data: {
       ...params.data,
        chapter_id: chapterId,
        comic_id:comicId
      }
    }
  });
}

/**
 * 获取漫画页面详情
 * @param id 页面ID
 */
export function getComicsPageDetail(id: string) {
  return request<ApiResponse<ComicsPage>>({
    url: `/comics/page/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 创建漫画页面
 * @param data 页面数据
 */
export function createComicsPage(params: { data: CreateComicsPageRequest }) {
  return request<ApiResponse<any>>({
    url: '/comics/page/add',
    method: 'post',
    data: params
  })
}

/**
 * 更新漫画页面
 * @param id 页面ID
 * @param data 页面数据
 */
export function updateComicsPage(id: string, params: { data: UpdateComicsPageRequest }) {
  return request<ApiResponse<any>>({
    url: `/comics/page/update/${id}`,
    method: 'post',
    data: params
  });
}

/**
 * 删除漫画页面
 * @param id 页面ID
 */
export function deleteComicsPage(id: string) {
  return request<ApiResponse<any>>({
    url: `/comics/page/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 批量上传漫画页面
 * @param chapterId 章节ID
 * @param comicsId 漫画ID
 * @param imageUrls 图片URL数组
 */
export function batchUploadComicsPages(chapterId: string, comicsId: string, imageUrls: string[]) {
  return request<ApiResponse<any>>({
    url: '/comics/page/batch-upload',
    method: 'post',
    data: {
      data: {
        chapter_id: chapterId,
        comics_id: comicsId,
        image_urls: imageUrls
      }
    }
  });
}

/**
 * 调整漫画页面顺序
 * @param pageId 页面ID
 * @param newOrder 新顺序
 */
export function reorderComicsPage(pageId: string, newOrder: number) {
  return request<ApiResponse<any>>({
    url: '/comics/page/reorder',
    method: 'post',
    data: { data: { "id": pageId, "page_number": newOrder } }
  });
}

/**
 * 批量创建漫画页面
 */
export function batchCreateComicsPages(chapterId: string, comicsId: string, pages: any[]) {
  return request<ApiResponse<any>>({
    url: '/comics/page/batch-add',
    method: 'post',
    data: {
      data: {
        comic_id: comicsId,
        chapter_id: chapterId,
        pages: pages
      }
    }
  });
}

/**
 * 批量更新章节顺序
 * @param comicId 漫画ID
 * @param chapters 章节数组
 */
export function batchUpdateChaptersOrder(comicId: string, chapters: { id: string, chapter_number: number }[]) {
  return request<ApiResponse<any>>({
    url: '/comics/chapter/batch-update-order',
    method: 'post',
    data: {
      data: {
        comic_id: comicId,
        chapters: chapters
      }
    }
  });
}

/**
 * 批量更新页面顺序
 * @param chapterId 章节ID
 * @param comicId 漫画ID
 * @param pages 页面数组
 */
export function batchUpdatePagesOrder(chapterId: string, comicId: string, pages: { id: string, page_number: number }[]) {
  return request<ApiResponse<any>>({
    url: '/comics/page/batch-update-order',
    method: 'post',
    data: {
      data: {
        chapter_id: chapterId,
        comic_id: comicId,
        pages: pages
      }
    }
  });
} 