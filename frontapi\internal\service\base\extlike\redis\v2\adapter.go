package v2

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"

	"frontapi/internal/service/base/extlike/types"
)

// Config Redis配置
type Config struct {
	Prefix       string        `json:"prefix"`
	BatchSize    int           `json:"batch_size"`
	TTL          time.Duration `json:"ttl"`
	PipelineSize int           `json:"pipeline_size"`
	MaxRetries   int           `json:"max_retries"`
	RetryDelay   time.Duration `json:"retry_delay"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Prefix:       "like",
		BatchSize:    100,
		TTL:          24 * time.Hour,
		PipelineSize: 1000,
		MaxRetries:   3,
		RetryDelay:   100 * time.Millisecond,
	}
}

// RedisConfig 兼容性配置结构（为了向后兼容service.go中的调用）
type RedisConfig struct {
	Enabled    bool          `json:"enabled"`
	KeyPrefix  string        `json:"key_prefix"`
	DefaultTTL time.Duration `json:"default_ttl"`
	LikeTTL    time.Duration `json:"like_ttl"`
	CountTTL   time.Duration `json:"count_ttl"`
	RankingTTL time.Duration `json:"ranking_ttl"`
}

// DefaultRedisConfig 返回默认RedisConfig（为了向后兼容）
func DefaultRedisConfig() *RedisConfig {
	return &RedisConfig{
		Enabled:    true,
		KeyPrefix:  "like:",
		DefaultTTL: 24 * time.Hour,
		LikeTTL:    24 * time.Hour,
		CountTTL:   24 * time.Hour,
		RankingTTL: 24 * time.Hour,
	}
}

// RedisAdapter Redis适配器主结构
type RedisAdapter struct {
	client      redis.UniversalClient
	config      *Config
	redisClient *RedisClient
	stats       *AdapterStats

	// 操作处理器
	likeOps    *LikeOperations
	queryOps   *QueryOperations
	rankingOps *RankingOperations
	statsOps   *StatsOperations
}

// AdapterStats 适配器统计
type AdapterStats struct {
	HitCount    int64     `json:"hit_count"`
	MissCount   int64     `json:"miss_count"`
	ErrorCount  int64     `json:"error_count"`
	HitRate     float64   `json:"hit_rate"`
	LastUpdated time.Time `json:"last_updated"`
}

// updateHitRate 更新命中率
func (s *AdapterStats) updateHitRate() {
	total := s.HitCount + s.MissCount
	if total > 0 {
		s.HitRate = float64(s.HitCount) / float64(total)
	}
	s.LastUpdated = time.Now()
}

// NewAdapterStats 创建适配器统计
func NewAdapterStats() *AdapterStats {
	return &AdapterStats{}
}

// NewRedisAdapter 创建Redis适配器
func NewRedisAdapter(client redis.UniversalClient, config interface{}) *RedisAdapter {
	var internalConfig *Config

	// 支持多种配置类型以兼容不同的调用方式
	switch c := config.(type) {
	case *Config:
		internalConfig = c
	case *RedisConfig:
		// 转换RedisConfig到内部Config
		internalConfig = &Config{
			Prefix:       c.KeyPrefix,
			BatchSize:    100,
			TTL:          c.DefaultTTL,
			PipelineSize: 1000,
			MaxRetries:   3,
			RetryDelay:   100 * time.Millisecond,
		}
	case nil:
		internalConfig = DefaultConfig()
	default:
		internalConfig = DefaultConfig()
	}

	stats := NewAdapterStats()

	// 创建RedisClient包装器
	redisConfig := &RedisConfig{
		Enabled:    true,
		KeyPrefix:  internalConfig.Prefix,
		DefaultTTL: internalConfig.TTL,
		LikeTTL:    internalConfig.TTL,
		CountTTL:   internalConfig.TTL,
		RankingTTL: internalConfig.TTL,
	}
	redisClient := NewRedisClient(client, redisConfig)

	adapter := &RedisAdapter{
		client:      client,
		config:      internalConfig,
		redisClient: redisClient,
		stats:       stats,
	}

	// 初始化操作处理器，使用现有的函数签名
	adapter.likeOps = NewLikeOperations(redisClient, stats)
	adapter.queryOps = NewQueryOperations(redisClient, stats)
	adapter.rankingOps = NewRankingOperations(redisClient, stats)
	adapter.statsOps = NewStatsOperations(redisClient, stats)

	return adapter
}

// 基础操作
func (r *RedisAdapter) Like(ctx context.Context, userID, itemID, itemType string) error {
	return r.likeOps.Like(ctx, userID, itemID, itemType)
}

func (r *RedisAdapter) Unlike(ctx context.Context, userID, itemID, itemType string) error {
	return r.likeOps.Unlike(ctx, userID, itemID, itemType)
}

func (r *RedisAdapter) IsLiked(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	return r.likeOps.IsLiked(ctx, userID, itemID, itemType)
}

func (r *RedisAdapter) GetLikeCount(ctx context.Context, itemID, itemType string) (int64, error) {
	return r.likeOps.GetLikeCount(ctx, itemID, itemType)
}

// 批量操作
func (r *RedisAdapter) BatchLike(ctx context.Context, operations []*types.LikeOperation) error {
	return r.likeOps.BatchLike(ctx, operations)
}

func (r *RedisAdapter) BatchUnlike(ctx context.Context, operations []*types.LikeOperation) error {
	return r.likeOps.BatchUnlike(ctx, operations)
}

func (r *RedisAdapter) BatchGetLikeStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	// 这个方法在like_operations.go中不存在，我们需要在adapter层实现
	result := make(map[string]bool)
	for itemID, itemType := range items {
		isLiked, err := r.IsLiked(ctx, userID, itemID, itemType)
		if err != nil {
			return nil, err
		}
		key := itemType + ":" + itemID
		result[key] = isLiked
	}
	return result, nil
}

func (r *RedisAdapter) BatchGetLikeCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	// 这个方法在like_operations.go中不存在，我们需要在adapter层实现
	result := make(map[string]int64)
	for itemID, itemType := range items {
		count, err := r.GetLikeCount(ctx, itemID, itemType)
		if err != nil {
			return nil, err
		}
		key := itemType + ":" + itemID
		result[key] = count
	}
	return result, nil
}

// 查询操作
func (r *RedisAdapter) GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	return r.queryOps.GetUserLikes(ctx, userID, itemType, limit, offset)
}

func (r *RedisAdapter) GetItemLikers(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	return r.queryOps.GetItemLikers(ctx, itemID, itemType, limit, offset)
}

func (r *RedisAdapter) GetLikeHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.LikeRecord, error) {
	return r.queryOps.GetLikeHistory(ctx, userID, itemType, timeRange)
}

// 热度排名
func (r *RedisAdapter) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	return r.rankingOps.UpdateHotRank(ctx, itemID, itemType, score)
}

func (r *RedisAdapter) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	return r.rankingOps.GetHotRanking(ctx, itemType, limit)
}

func (r *RedisAdapter) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	return r.rankingOps.GetHotRankingWithScores(ctx, itemType, limit)
}

// 统计操作 - 使用简化的方法，因为stats operations没有这些方法
func (r *RedisAdapter) GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error) {
	// 简化实现，返回基本统计
	return &types.UserLikeStats{
		UserID:     userID,
		TotalLikes: 0, // 可以通过查询用户点赞列表获取
	}, nil
}

func (r *RedisAdapter) GetItemLikeStats(ctx context.Context, itemID, itemType string) (*types.ItemLikeStats, error) {
	// 获取点赞数
	count, err := r.GetLikeCount(ctx, itemID, itemType)
	if err != nil {
		return nil, err
	}

	return &types.ItemLikeStats{
		ItemID:     itemID,
		ItemType:   itemType,
		TotalLikes: count,
	}, nil
}

func (r *RedisAdapter) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error) {
	// 简化实现，返回空列表
	return []*types.LikeTrend{}, nil
}

// 缓存管理和其他方法，简化实现
func (r *RedisAdapter) InvalidateCache(ctx context.Context, keys ...string) error {
	// 简化实现
	for _, key := range keys {
		r.client.Del(ctx, key)
	}
	return nil
}

func (r *RedisAdapter) WarmupCache(ctx context.Context, itemType string, itemIDs []string) error {
	// 简化实现，预热热门项目的点赞数
	for _, itemID := range itemIDs {
		_, _ = r.GetLikeCount(ctx, itemID, itemType)
	}
	return nil
}

func (r *RedisAdapter) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return r.statsOps.GetCacheStats(ctx)
}

func (r *RedisAdapter) CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error {
	// 简化实现
	return nil
}

func (r *RedisAdapter) ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error) {
	// 简化实现
	return []byte("{}"), nil
}

func (r *RedisAdapter) ImportData(ctx context.Context, data []byte, itemType, format string) error {
	// 简化实现
	return nil
}

func (r *RedisAdapter) HealthCheck(ctx context.Context) error {
	return r.redisClient.HealthCheck(ctx)
}

func (r *RedisAdapter) Close() error {
	return r.redisClient.Close()
}
