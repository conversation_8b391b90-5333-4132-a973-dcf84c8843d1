package container

import (
	picRepo "frontapi/internal/repository/pictures"
	pictureServices "frontapi/internal/service/pictures"
)

// InitPictureServices 初始化图片相关服务
func InitPictureServices(b *ServiceBuilder) {
	// 初始化图片仓库
	pictureRepoImpl := picRepo.NewPictureRepository(b.DB())
	pictureCategoryRepo := picRepo.NewPictureCategoryRepository(b.DB())
	pictureAlbumRepo := picRepo.NewPictureAlbumRepository(b.DB())
	pictureLikeRepo := picRepo.NewPictureLikeRepository(b.DB())
	pictureCollectionRepo := picRepo.NewUserPictureCollectionRepository(b.DB())

	// 初始化图片服务
	container := b.Services()
	container.PictureService = pictureServices.NewPictureService(pictureRepoImpl, pictureAlbumRepo, pictureCategoryRepo, pictureLikeRepo)
	container.PictureCategoryService = pictureServices.NewPictureCategoryService(pictureCategoryRepo)
	container.PictureAlbumService = pictureServices.NewPictureAlbumService(pictureAlbumRepo, pictureCategoryRepo, pictureRepoImpl)
	container.PictureCollectionService = pictureServices.NewPictureCollectionService(pictureCollectionRepo, pictureRepoImpl, pictureAlbumRepo)
}
