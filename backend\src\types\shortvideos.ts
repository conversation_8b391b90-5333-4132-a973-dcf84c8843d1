// 短视频类型定义
export interface ShortVideo {
    id: string;
    title: string;
    description: string;
    cover: string;
    url: string;
    user_id?: string;
    category_id?: string;
    category_name?: string;
    creator_id?: string;
    creator_name?: string;
    creator_avatar?: string;
    duration: number;
    width: number;
    height: number;
    tags: string[];
    status: number; // 0-待审核,1-已下架,2-已发布,-2-已拒绝,-4-已删除
    views: number;
    likes: number;
    comments: number;
    shares: number;
    created_at: string;
    updated_at: string;
}

// 短视频查询参数
export interface ShortVideoQuery {
    title?: string;
    category_id?: string;
    status?: number;
    is_featured?: number;
    user_id?: string;
}

// 分页参数
export interface PageParams {
    pageNo: number;
    pageSize: number;
}

// 请求参数
export interface ShortVideoParams {
    page: PageParams;
    data?: ShortVideoQuery;
}

// 列表响应数据
export interface ShortVideoListResponse {
    list: ShortVideo[];
    total: number;
    page: number;
    pageSize: number;
}

// 创建短视频请求
export interface CreateShortVideoRequest {
    title: string;
    description: string;
    url: string;
    category_id: string;
    category_name: string;
    creator_id: string;
    creator_name: string;
    creator_avatar: string;
    duration: number;
    width: number;
    height: number;
    tags: string[];
    status: number;
    is_featured: number;
}

// 更新短视频请求
export interface UpdateShortVideoRequest {
    id: string;
    title: string;
    description: string;
    url: string;
    category_id: string;
    category_name: string;
    creator_id: string;
    creator_name: string;
    creator_avatar: string;
    duration: number;
    width: number;
    height: number;
    tags: string[];
    status: number;
    is_featured: number;
}

// 短视频分类
export interface ShortVideoCategory {
    id: string;
    name: string;
    code: string;
    description?: string;
    parent_id: string;
    sort_order: number;
    status: number; // 0-禁用 1-启用
    uri?: string;
    icon?: string;
    created_at: string;
    updated_at: string;
}

// 分类查询参数
export interface CategoryQuery {
    keyword?: string;
    status?: number;
    parent_id?: string;
    name?: string;
}

// 分类请求参数
export interface CategoryParams {
    page: PageParams;
    data?: CategoryQuery;
}

// 分类列表响应
export interface CategoryListResponse {
    list: ShortVideoCategory[];
    total: number;
    page: number;
    pageSize: number;
}

// 创建分类请求
export interface CreateCategoryRequest {
    name: string;
    code: string;
    description: string;
    parent_id?: string;
    sort_order: number;
    status: number;
    uri: string;
    icon: string;
}

// 更新分类请求
export interface UpdateCategoryRequest {
    id: string;
    name: string;
    code: string;
    description: string;
    parent_id?: string;
    sort_order: number;
    status: number;
    uri?: string;
    icon?: string;
}

// 短视频评论
export interface ShortVideoComment {
    id: string;
    video_id: string;
    user_id: string;
    parent_id: string;
    content: string;
    likes: number;
    reply_count: number;
    status: number; // 0-正常 1-隐藏
    created_at: string;
    updated_at: string;
    user_name?: string;
    user_avatar?: string;
}

// 评论查询参数
export interface CommentQuery {
    short_id?: string;
    user_id?: string;
    status?: number;
    keyword?: string;
}

// 评论请求参数
export interface CommentParams {
    page: PageParams;
    data?: CommentQuery;
}

// 评论列表响应
export interface CommentListResponse {
    list: ShortVideoComment[];
    total: number;
    page: number;
    pageSize: number;
}

// 创建评论请求
export interface CreateCommentRequest {
    short_id: string;
    content: string;
    parent_id?: string;
}

// 更新评论请求
export interface UpdateCommentRequest {
    id: string;
    content: string;
    status: number;
}
