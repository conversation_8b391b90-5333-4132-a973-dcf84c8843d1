/* Slinky主题 - 对话框样式 */

.slinky-dialog {
    .el-dialog {
        border-radius: 16px;
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
        background: #fff;
        overflow: hidden;

        .el-dialog__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #fff;
            padding: 20px 24px;
            border-bottom: none;

            .el-dialog__title {
                color: #fff;
                font-size: 18px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }

            .el-dialog__headerbtn {
                top: 20px;
                right: 24px;

                .el-dialog__close {
                    color: #fff;
                    font-size: 18px;

                    &:hover {
                        color: #f0f0f0;
                    }
                }
            }
        }

        .el-dialog__body {
            padding: 24px;
            background: #fafbfc;

            .el-form {
                .el-form-item {
                    margin-bottom: 20px;

                    .el-form-item__label {
                        color: #333;
                        font-weight: 500;
                        font-size: 14px;
                    }

                    .el-form-item__content {
                        .el-input {
                            .el-input__wrapper {
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                                border: 1px solid #e0e6ed;
                                transition: all 0.3s ease;

                                &:hover,
                                &.is-focus {
                                    border-color: #667eea;
                                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
                                }
                            }
                        }

                        .el-textarea {
                            .el-textarea__inner {
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                                border: 1px solid #e0e6ed;
                                transition: all 0.3s ease;

                                &:hover,
                                &:focus {
                                    border-color: #667eea;
                                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
                                }
                            }
                        }

                        .el-select {
                            .el-select__wrapper {
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                                border: 1px solid #e0e6ed;
                                transition: all 0.3s ease;

                                &:hover,
                                &.is-focus {
                                    border-color: #667eea;
                                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
                                }
                            }
                        }

                        .el-date-picker {
                            .el-input__wrapper {
                                border-radius: 8px;
                                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                                border: 1px solid #e0e6ed;
                                transition: all 0.3s ease;

                                &:hover,
                                &.is-focus {
                                    border-color: #667eea;
                                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
                                }
                            }
                        }
                    }
                }
            }
        }

        .el-dialog__footer {
            padding: 20px 24px;
            background: #fff;
            border-top: 1px solid #f0f2f5;
            text-align: right;

            .el-button {
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s ease;

                &.el-button--primary {
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    border: none;

                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
                    }
                }

                &.el-button--default {
                    background: #f8f9fa;
                    border: 1px solid #e0e6ed;
                    color: #666;

                    &:hover {
                        background: #e9ecef;
                        border-color: #ced4da;
                        transform: translateY(-1px);
                    }
                }
            }
        }
    }
}

// 确认对话框样式
.slinky-confirm {
    .el-message-box {
        border-radius: 12px;
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);

        .el-message-box__header {
            .el-message-box__title {
                color: #333;
                font-weight: 600;
            }
        }

        .el-message-box__content {
            .el-message-box__message {
                color: #666;
                font-size: 14px;
            }
        }

        .el-message-box__btns {
            .el-button {
                border-radius: 6px;

                &.el-button--primary {
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    border: none;
                }
            }
        }
    }
}

// 详情对话框特殊样式
.slinky-detail-dialog {
    .detail-section {
        margin-bottom: 24px;

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #667eea;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                left: 0;
                bottom: -2px;
                width: 40px;
                height: 2px;
                background: linear-gradient(45deg, #667eea, #764ba2);
            }
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;

            .detail-item {
                background: #fff;
                padding: 16px;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

                .label {
                    font-size: 12px;
                    color: #999;
                    margin-bottom: 4px;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                .value {
                    font-size: 14px;
                    color: #333;
                    font-weight: 500;
                }
            }
        }
    }
}

// 图片/视频预览样式
.slinky-media-preview {
    .media-container {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

        img,
        video {
            width: 100%;
            height: auto;
            display: block;
        }
    }

    .media-info {
        margin-top: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;

            .label {
                color: #666;
                font-weight: 500;
            }

            .value {
                color: #333;
            }
        }
    }
}