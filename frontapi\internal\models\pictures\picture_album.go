package pictures

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// PictureAlbum 图片专辑模型
type PictureAlbum struct {
	*models.ContentBaseModel
	CoverURL     string            `json:"cover_url" gorm:"column:cover_url;not null" comment:"封面图URL"`
	PictureCount int               `json:"picture_count" gorm:"column:picture_count;default:0" comment:"图片数量"`
	IsPaid       bool              `json:"is_paid" gorm:"column:is_paid;default:0" comment:"是否付费：0-免费，1-付费"`
	SortOrder    int               `json:"sort_order" gorm:"column:sort_order;default:0" comment:"排序"`
	Price        float64           `json:"price" gorm:"column:price;default:0.00" comment:"价格"`
	UploadTime   types.JSONTime    `json:"upload_time" gorm:"column:upload_time;default:CURRENT_TIMESTAMP" comment:"上传时间"`
	Tags         types.StringArray `json:"tags" gorm:"column:tags;type:json" comment:"标签JSON数据"`
}

// TableName 指定表名
func (PictureAlbum) TableName() string {
	return "ly_picture_albums"
}
