/**
 * 设备检测工具
 */

/**
 * 设备类型
 */
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

/**
 * 操作系统类型
 */
export type OSType = 'windows' | 'macos' | 'linux' | 'ios' | 'android' | 'unknown'

/**
 * 浏览器类型
 */
export type BrowserType = 'chrome' | 'firefox' | 'safari' | 'edge' | 'ie' | 'opera' | 'unknown'

/**
 * 网络类型
 */
export type NetworkType = 'wifi' | '4g' | '3g' | '2g' | 'slow-2g' | 'unknown'

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  type: DeviceType
  os: OSType
  browser: BrowserType
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouchDevice: boolean
  isRetina: boolean
  userAgent: string
  platform: string
  language: string
  languages: readonly string[]
  timezone: string
  screen: {
    width: number
    height: number
    availWidth: number
    availHeight: number
    colorDepth: number
    pixelDepth: number
    orientation?: string
  }
  viewport: {
    width: number
    height: number
  }
}

/**
 * 网络信息接口
 */
export interface NetworkInfo {
  online: boolean
  type: NetworkType
  effectiveType?: string
  downlink?: number
  rtt?: number
  saveData?: boolean
}

/**
 * 检测设备类型
 */
export function detectDeviceType(): DeviceType {
  const userAgent = navigator.userAgent.toLowerCase()
  const width = window.innerWidth
  
  // 移动设备检测
  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
  if (mobileRegex.test(userAgent)) {
    // 区分手机和平板
    if (width >= 768 || /ipad|tablet/i.test(userAgent)) {
      return 'tablet'
    }
    return 'mobile'
  }
  
  // 基于屏幕尺寸的检测
  if (width < 768) {
    return 'mobile'
  } else if (width < 1024) {
    return 'tablet'
  }
  
  return 'desktop'
}

/**
 * 检测操作系统
 */
export function detectOS(): OSType {
  const userAgent = navigator.userAgent.toLowerCase()
  const platform = navigator.platform.toLowerCase()
  
  if (/iphone|ipad|ipod/.test(userAgent)) {
    return 'ios'
  }
  
  if (/android/.test(userAgent)) {
    return 'android'
  }
  
  if (/win/.test(platform)) {
    return 'windows'
  }
  
  if (/mac/.test(platform)) {
    return 'macos'
  }
  
  if (/linux/.test(platform)) {
    return 'linux'
  }
  
  return 'unknown'
}

/**
 * 检测浏览器
 */
export function detectBrowser(): BrowserType {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (/edg/.test(userAgent)) {
    return 'edge'
  }
  
  if (/chrome/.test(userAgent) && !/edg/.test(userAgent)) {
    return 'chrome'
  }
  
  if (/firefox/.test(userAgent)) {
    return 'firefox'
  }
  
  if (/safari/.test(userAgent) && !/chrome/.test(userAgent)) {
    return 'safari'
  }
  
  if (/opera|opr/.test(userAgent)) {
    return 'opera'
  }
  
  if (/trident|msie/.test(userAgent)) {
    return 'ie'
  }
  
  return 'unknown'
}

/**
 * 检测是否为触摸设备
 */
export function isTouchDevice(): boolean {
  return (
    'ontouchstart' in window ||
    navigator.maxTouchPoints > 0 ||
    (navigator as any).msMaxTouchPoints > 0
  )
}

/**
 * 检测是否为Retina屏幕
 */
export function isRetinaDisplay(): boolean {
  return (
    window.devicePixelRatio > 1 ||
    (window.matchMedia && window.matchMedia('(-webkit-min-device-pixel-ratio: 1.5)').matches)
  )
}

/**
 * 获取屏幕方向
 */
export function getScreenOrientation(): string {
  if (screen.orientation) {
    return screen.orientation.type
  }
  
  // 兼容性处理
  const orientation = (screen as any).mozOrientation || (screen as any).msOrientation
  if (orientation) {
    return orientation
  }
  
  // 基于尺寸判断
  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
}

/**
 * 获取时区
 */
export function getTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  } catch (error) {
    return 'UTC'
  }
}

/**
 * 检测完整设备信息
 */
export function detectDevice(): DeviceInfo {
  const type = detectDeviceType()
  const os = detectOS()
  const browser = detectBrowser()
  
  return {
    type,
    os,
    browser,
    isMobile: type === 'mobile',
    isTablet: type === 'tablet',
    isDesktop: type === 'desktop',
    isTouchDevice: isTouchDevice(),
    isRetina: isRetinaDisplay(),
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    languages: navigator.languages,
    timezone: getTimezone(),
    screen: {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth,
      orientation: getScreenOrientation()
    },
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    }
  }
}

/**
 * 检测网络信息
 */
export function detectNetwork(): NetworkInfo {
  const info: NetworkInfo = {
    online: navigator.onLine,
    type: 'unknown'
  }
  
  // 检测网络连接信息（如果支持）
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    
    if (connection) {
      info.effectiveType = connection.effectiveType
      info.downlink = connection.downlink
      info.rtt = connection.rtt
      info.saveData = connection.saveData
      
      // 映射网络类型
      switch (connection.effectiveType) {
        case 'slow-2g':
          info.type = 'slow-2g'
          break
        case '2g':
          info.type = '2g'
          break
        case '3g':
          info.type = '3g'
          break
        case '4g':
          info.type = '4g'
          break
        default:
          info.type = 'unknown'
      }
    }
  }
  
  return info
}

/**
 * 设备能力检测
 */
export const DeviceCapabilities = {
  /**
   * 检测是否支持WebGL
   */
  hasWebGL(): boolean {
    try {
      const canvas = document.createElement('canvas')
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    } catch (error) {
      return false
    }
  },
  
  /**
   * 检测是否支持WebRTC
   */
  hasWebRTC(): boolean {
    return !!(window.RTCPeerConnection || (window as any).webkitRTCPeerConnection || (window as any).mozRTCPeerConnection)
  },
  
  /**
   * 检测是否支持Service Worker
   */
  hasServiceWorker(): boolean {
    return 'serviceWorker' in navigator
  },
  
  /**
   * 检测是否支持Web Workers
   */
  hasWebWorkers(): boolean {
    return typeof Worker !== 'undefined'
  },
  
  /**
   * 检测是否支持本地存储
   */
  hasLocalStorage(): boolean {
    try {
      const test = '__localStorage_test__'
      localStorage.setItem(test, 'test')
      localStorage.removeItem(test)
      return true
    } catch (error) {
      return false
    }
  },
  
  /**
   * 检测是否支持IndexedDB
   */
  hasIndexedDB(): boolean {
    return 'indexedDB' in window
  },
  
  /**
   * 检测是否支持Geolocation
   */
  hasGeolocation(): boolean {
    return 'geolocation' in navigator
  },
  
  /**
   * 检测是否支持摄像头
   */
  hasCamera(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
  },
  
  /**
   * 检测是否支持通知
   */
  hasNotifications(): boolean {
    return 'Notification' in window
  },
  
  /**
   * 检测是否支持全屏
   */
  hasFullscreen(): boolean {
    const element = document.documentElement
    return !!(element.requestFullscreen || (element as any).webkitRequestFullscreen || (element as any).msRequestFullscreen)
  },
  
  /**
   * 检测是否支持剪贴板
   */
  hasClipboard(): boolean {
    return !!(navigator.clipboard && navigator.clipboard.writeText)
  },
  
  /**
   * 检测是否支持分享
   */
  hasShare(): boolean {
    return !!(navigator.share)
  },
  
  /**
   * 检测是否支持震动
   */
  hasVibration(): boolean {
    return !!(navigator.vibrate)
  },
  
  /**
   * 检测是否支持电池API
   */
  hasBattery(): boolean {
    return !!((navigator as any).getBattery)
  },
  
  /**
   * 检测是否支持网络信息API
   */
  hasNetworkInformation(): boolean {
    return 'connection' in navigator
  }
}

/**
 * 性能检测
 */
export const PerformanceDetector = {
  /**
   * 检测设备性能等级
   */
  getPerformanceLevel(): 'low' | 'medium' | 'high' {
    const cores = navigator.hardwareConcurrency || 1
    const memory = (navigator as any).deviceMemory || 1
    
    // 基于CPU核心数和内存判断
    if (cores >= 8 && memory >= 8) {
      return 'high'
    } else if (cores >= 4 && memory >= 4) {
      return 'medium'
    } else {
      return 'low'
    }
  },
  
  /**
   * 测试渲染性能
   */
  async testRenderPerformance(): Promise<number> {
    return new Promise((resolve) => {
      const start = performance.now()
      let frames = 0
      
      function frame() {
        frames++
        if (frames < 60) {
          requestAnimationFrame(frame)
        } else {
          const end = performance.now()
          const fps = 1000 / ((end - start) / frames)
          resolve(fps)
        }
      }
      
      requestAnimationFrame(frame)
    })
  },
  
  /**
   * 获取内存信息
   */
  getMemoryInfo(): any {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }
}

/**
 * 设备工具函数
 */
export const DeviceUtils = {
  /**
   * 检测是否为移动设备
   */
  isMobile(): boolean {
    return detectDeviceType() === 'mobile'
  },
  
  /**
   * 检测是否为平板设备
   */
  isTablet(): boolean {
    return detectDeviceType() === 'tablet'
  },
  
  /**
   * 检测是否为桌面设备
   */
  isDesktop(): boolean {
    return detectDeviceType() === 'desktop'
  },
  
  /**
   * 检测是否为iOS设备
   */
  isIOS(): boolean {
    return detectOS() === 'ios'
  },
  
  /**
   * 检测是否为Android设备
   */
  isAndroid(): boolean {
    return detectOS() === 'android'
  },
  
  /**
   * 检测是否为Safari浏览器
   */
  isSafari(): boolean {
    return detectBrowser() === 'safari'
  },
  
  /**
   * 检测是否为Chrome浏览器
   */
  isChrome(): boolean {
    return detectBrowser() === 'chrome'
  },
  
  /**
   * 检测是否为微信浏览器
   */
  isWechat(): boolean {
    return /micromessenger/i.test(navigator.userAgent)
  },
  
  /**
   * 检测是否为QQ浏览器
   */
  isQQ(): boolean {
    return /qq/i.test(navigator.userAgent)
  },
  
  /**
   * 检测是否在PWA模式下运行
   */
  isPWA(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches || (window.navigator as any).standalone
  },
  
  /**
   * 获取设备像素比
   */
  getPixelRatio(): number {
    return window.devicePixelRatio || 1
  },
  
  /**
   * 获取视口尺寸
   */
  getViewportSize(): { width: number; height: number } {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    }
  },
  
  /**
   * 获取屏幕尺寸
   */
  getScreenSize(): { width: number; height: number } {
    return {
      width: screen.width,
      height: screen.height
    }
  }
}

/**
 * 默认导出
 */
export default {
  detectDevice,
  detectNetwork,
  DeviceCapabilities,
  PerformanceDetector,
  DeviceUtils
}