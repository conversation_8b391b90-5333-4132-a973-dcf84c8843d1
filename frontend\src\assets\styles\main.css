/* TailwindCSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局CSS变量定义 */
:root {
    /* 颜色系统 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --primary-active: #1d4ed8;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;

    --secondary-color: #6b7280;
    --secondary-hover: #4b5563;
    --secondary-light: #f3f4f6;
    --secondary-dark: #374151;

    --success-color: #10b981;
    --success-hover: #059669;
    --success-light: #d1fae5;
    --success-dark: #047857;

    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --warning-light: #fef3c7;
    --warning-dark: #b45309;

    --error-color: #ef4444;
    --error-hover: #dc2626;
    --error-light: #fee2e2;
    --error-dark: #b91c1c;

    --info-color: #3b82f6;
    --info-hover: #2563eb;
    --info-light: #dbeafe;
    --info-dark: #1d4ed8;

    /* 文本颜色 */
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-tertiary: #9ca3af;
    --text-disabled: #d1d5db;
    --text-inverse: #ffffff;

    /* 背景颜色 */
    --background-primary: #ffffff;
    --background-secondary: #f9fafb;
    --background-tertiary: #f3f4f6;
    --background-overlay: rgba(0, 0, 0, 0.5);
    --background-modal: rgba(0, 0, 0, 0.75);

    /* 边框颜色 */
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --border-dark: #d1d5db;
    --border-focus: var(--primary-color);

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 圆角 */
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 9999px;

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;

    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* 过渡动画 */
    --transition-fast: 150ms ease;
    --transition-normal: 300ms ease;
    --transition-slow: 500ms ease;

    /* Z-index层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* 布局 */
    --container-max-width: 1200px;
    --sidebar-width: 280px;
    --header-height: 64px;
    --footer-height: 80px;
}

/* 深色主题 */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-tertiary: #9ca3af;
    --text-disabled: #6b7280;
    --text-inverse: #111827;

    --background-primary: #111827;
    --background-secondary: #1f2937;
    --background-tertiary: #374151;
    --background-overlay: rgba(0, 0, 0, 0.75);
    --background-modal: rgba(0, 0, 0, 0.9);

    --border-color: #374151;
    --border-light: #4b5563;
    --border-dark: #6b7280;
}

/* 蓝色主题 */
[data-theme="blue"] {
    --primary-color: #1e40af;
    --primary-hover: #1d4ed8;
    --primary-active: #2563eb;
    --primary-light: #dbeafe;
    --primary-dark: #1e3a8a;

    --background-primary: #f0f9ff;
    --background-secondary: #e0f2fe;
    --background-tertiary: #bae6fd;
}

/* 基础样式重置 */
*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    margin: 0;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 标题样式 */
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 var(--spacing-md) 0;
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
}

h1 {
    font-size: var(--font-size-4xl);
}

h2 {
    font-size: var(--font-size-3xl);
}

h3 {
    font-size: var(--font-size-2xl);
}

h4 {
    font-size: var(--font-size-xl);
}

h5 {
    font-size: var(--font-size-lg);
}

h6 {
    font-size: var(--font-size-base);
}

/* 段落样式 */
p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-secondary);
}

/* 链接样式 */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 按钮基础样式 */
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* 表单元素样式 */
input,
textarea,
select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--background-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input:disabled,
textarea:disabled,
select:disabled {
    background-color: var(--background-tertiary);
    color: var(--text-disabled);
    cursor: not-allowed;
}

/* 列表样式 */
ul,
ol {
    margin: 0 0 var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

/* 图片样式 */
img {
    max-width: 100%;
    height: auto;
    border-style: none;
}

/* 表格样式 */
table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

th,
td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    background-color: var(--background-secondary);
}

/* 代码样式 */
code {
    font-family: var(--font-family-mono);
    font-size: 0.875em;
    padding: 0.125em 0.25em;
    background-color: var(--background-tertiary);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
}

pre {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    margin: 0 0 var(--spacing-md) 0;
    padding: var(--spacing-md);
    background-color: var(--background-tertiary);
    border-radius: var(--border-radius);
    overflow-x: auto;
}

pre code {
    padding: 0;
    background: none;
    border-radius: 0;
}

/* 分割线样式 */
hr {
    margin: var(--spacing-xl) 0;
    border: none;
    border-top: 1px solid var(--border-color);
}

/* 引用样式 */
blockquote {
    margin: 0 0 var(--spacing-md) 0;
    padding: var(--spacing-md) var(--spacing-lg);
    border-left: 4px solid var(--primary-color);
    background-color: var(--background-secondary);
    font-style: italic;
}

/* 工具类 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-light {
    font-weight: var(--font-weight-light);
}

.font-normal {
    font-weight: var(--font-weight-normal);
}

.font-medium {
    font-weight: var(--font-weight-medium);
}

.font-semibold {
    font-weight: var(--font-weight-semibold);
}

.font-bold {
    font-weight: var(--font-weight-bold);
}

.text-xs {
    font-size: var(--font-size-xs);
}

.text-sm {
    font-size: var(--font-size-sm);
}

.text-base {
    font-size: var(--font-size-base);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.text-xl {
    font-size: var(--font-size-xl);
}

.text-2xl {
    font-size: var(--font-size-2xl);
}

.text-3xl {
    font-size: var(--font-size-3xl);
}

.text-4xl {
    font-size: var(--font-size-4xl);
}

.text-primary {
    color: var(--text-primary);
}

.text-secondary {
    color: var(--text-secondary);
}

.text-tertiary {
    color: var(--text-tertiary);
}

.text-disabled {
    color: var(--text-disabled);
}

.text-inverse {
    color: var(--text-inverse);
}

.bg-primary {
    background: var(--background-primary);
}

.bg-secondary {
    background: var(--background-secondary);
}

.bg-tertiary {
    background: var(--background-tertiary);
}

.border {
    border: 1px solid var(--border-color);
}

.border-light {
    border: 1px solid var(--border-light);
}

.border-dark {
    border: 1px solid var(--border-dark);
}

.rounded {
    border-radius: var(--border-radius);
}

.rounded-sm {
    border-radius: var(--border-radius-sm);
}

.rounded-lg {
    border-radius: var(--border-radius-lg);
}

.rounded-xl {
    border-radius: var(--border-radius-xl);
}

.rounded-full {
    border-radius: var(--border-radius-full);
}

.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.shadow-xl {
    box-shadow: var(--shadow-xl);
}

.transition {
    transition: all var(--transition-normal);
}

.transition-fast {
    transition: all var(--transition-fast);
}

.transition-slow {
    transition: all var(--transition-slow);
}

/* 响应式工具类 */
@media (max-width: 640px) {
    .sm\:hidden {
        display: none;
    }

    .sm\:block {
        display: block;
    }

    .sm\:flex {
        display: flex;
    }

    .sm\:grid {
        display: grid;
    }
}

@media (max-width: 768px) {
    .md\:hidden {
        display: none;
    }

    .md\:block {
        display: block;
    }

    .md\:flex {
        display: flex;
    }

    .md\:grid {
        display: grid;
    }
}

@media (max-width: 1024px) {
    .lg\:hidden {
        display: none;
    }

    .lg\:block {
        display: block;
    }

    .lg\:flex {
        display: flex;
    }

    .lg\:grid {
        display: grid;
    }
}

@media (max-width: 1280px) {
    .xl\:hidden {
        display: none;
    }

    .xl\:block {
        display: block;
    }

    .xl\:flex {
        display: flex;
    }

    .xl\:grid {
        display: grid;
    }
}

/* 打印样式 */
@media print {

    *,
    *::before,
    *::after {
        background: transparent !important;
        color: #000 !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    a[href]::after {
        content: " (" attr(href) ")";
    }

    abbr[title]::after {
        content: " (" attr(title) ")";
    }

    pre {
        white-space: pre-wrap !important;
    }

    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }

    thead {
        display: table-header-group;
    }

    tr,
    img {
        page-break-inside: avoid;
    }

    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--border-radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}

/* Firefox 滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--background-secondary);
}

/* 选择文本样式 */
::selection {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

::-moz-selection {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

/* 焦点可见性 */
.js-focus-visible :focus:not(.focus-visible) {
    outline: none;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}