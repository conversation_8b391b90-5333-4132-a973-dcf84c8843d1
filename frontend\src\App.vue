<template>
    <MainLayout>
        <router-view />
    </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/layouts/DefaultLayout/MainLayout.vue';
import { themeManager } from '@/shared/themes/theme-manager';
import { useLocaleStore } from '@/store/modules/locale';
import 'primeicons/primeicons.css';
import { onMounted } from 'vue';

// 初始化主题
const localeStore = useLocaleStore();

onMounted(() => {
    try {
        // 主题已在插件中初始化，这里只需要检查状态
        console.log('App initialized with locale:', localeStore.locale);
        console.log('App initialized with theme:', themeManager.currentTheme.value);
    } catch (error) {
        console.error('Failed to initialize app:', error);
    }
});
</script>

<style>
/* 全局样式可以放在这里 */
</style>