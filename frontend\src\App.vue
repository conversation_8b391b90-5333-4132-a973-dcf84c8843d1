<template>
    <MainLayout>
        <router-view />
    </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/layouts/DefaultLayout/MainLayout.vue';
import { themeManager } from '@/shared/themes/theme-manager';
import { useLocaleStore } from '@/store/modules/locale';
import 'primeicons/primeicons.css';
import { onMounted } from 'vue';

// 初始化主题
const localeStore = useLocaleStore();

onMounted(() => {
    try {
        // 确保主题已正确加载
        themeManager.init();

        // 初始化时检查是否为深色主题并添加对应的类
        const isDark = themeManager.isDark.value;
        if (isDark) {
            document.documentElement.classList.add('dark-theme');
            document.body.classList.add('dark-theme');
        } else {
            document.documentElement.classList.remove('dark-theme');
            document.body.classList.remove('dark-theme');
        }

        // 注意：语言初始化在localeStore中已自动处理，不需要再次设置
        console.log('App initialized with locale:', localeStore.locale);
        console.log('App initialized with theme:', themeManager.currentTheme.value);
    } catch (error) {
        console.error('Failed to initialize app:', error);
    }
});
</script>

<style>
/* 全局样式可以放在这里 */
</style>