<template>
    <MainLayout>
        <router-view />
    </MainLayout>
</template>

<script setup lang="ts">
import MainLayout from '@/layouts/DefaultLayout/MainLayout.vue';
import { useLocaleStore } from '@/store/modules/locale';
import { useThemeStore } from '@/store/modules/theme';
import 'primeicons/primeicons.css';
import { onMounted } from 'vue';

// 初始化
const localeStore = useLocaleStore();
const themeStore = useThemeStore();

onMounted(() => {
    console.log('App initialized with locale:', localeStore.locale);
    console.log('App initialized with theme:', themeStore.currentThemeCode);
    
    // 主题现在由预设管理器自动处理
});
</script>

<style>
/* 全局样式可以放在这里 */
</style>