package content_creator

import "frontapi/pkg/types"

type CreateContentAuditRequest struct {
	ContentType string         `json:"content_type"`
	ContentID   string         `json:"content_id"`
	UserID      string         `json:"user_id"`
	AdminID     string         `json:"admin_id"`
	Status      string         `json:"status"`
	Reason      string         `json:"reason"`
	AuditTime   types.JSONTime `json:"audit_time"`
}

type UpdateContentAuditRequest struct {
	Status    string         `json:"status"`
	Reason    string         `json:"reason"`
	AdminID   string         `json:"admin_id"`
	AuditTime types.JSONTime `json:"audit_time"`
}
