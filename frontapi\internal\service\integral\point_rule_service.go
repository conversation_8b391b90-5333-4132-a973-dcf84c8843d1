package integral

import (
	model "frontapi/internal/models/integral"
	repo "frontapi/internal/repository/integral"
	"frontapi/internal/service/base"
)

// CreatePointRuleRequest 创建规则请求
type CreatePointRuleRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	Points      int    `json:"points"`
	Action      string `json:"action"`
	Period      string `json:"period"`
	MaxTimes    *int   `json:"max_times"`
	Description string `json:"description"`
	Status      int8   `json:"status"`
}

// UpdatePointRuleRequest 更新规则请求
type UpdatePointRuleRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	Points      int    `json:"points"`
	Action      string `json:"action"`
	Period      string `json:"period"`
	MaxTimes    *int   `json:"max_times"`
	Description string `json:"description"`
	Status      int8   `json:"status"`
}

// PointRuleService 积分规则服务接口
type PointRuleService interface {
	base.IExtendedService[model.PointRule]
}

type pointRuleService struct {
	*base.ExtendedService[model.PointRule]
	repo repo.PointRuleRepository
}

func NewPointRuleService(repo repo.PointRuleRepository) PointRuleService {
	return &pointRuleService{
		ExtendedService: base.NewExtendedService[model.PointRule](repo, "point_rule"),
		repo:            repo,
	}
}
