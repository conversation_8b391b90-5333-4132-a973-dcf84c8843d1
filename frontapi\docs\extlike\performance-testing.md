# 扩展点赞服务性能测试指南

本文档详细介绍如何对扩展点赞服务进行性能测试、基准测试和性能优化。

## 目录

- [测试环境准备](#测试环境准备)
- [基准测试](#基准测试)
- [压力测试](#压力测试)
- [并发测试](#并发测试)
- [内存测试](#内存测试)
- [缓存性能测试](#缓存性能测试)
- [数据库性能测试](#数据库性能测试)
- [网络延迟测试](#网络延迟测试)
- [性能监控](#性能监控)
- [性能优化建议](#性能优化建议)
- [测试报告模板](#测试报告模板)

## 测试环境准备

### 1. 硬件环境

**推荐配置**:
```yaml
# 测试服务器
CPU: 8核心 2.4GHz+
RAM: 16GB+
Disk: SSD 500GB+
Network: 1Gbps+

# Redis服务器
CPU: 4核心 2.4GHz+
RAM: 8GB+
Disk: SSD 100GB+

# MongoDB服务器
CPU: 8核心 2.4GHz+
RAM: 16GB+
Disk: SSD 500GB+
```

### 2. 软件环境

```bash
# 安装测试工具
go install github.com/rakyll/hey@latest
go install github.com/tsenart/vegeta@latest
go install github.com/codesenberg/bombardier@latest

# 安装监控工具
docker run -d --name redis-exporter \
  -p 9121:9121 \
  oliver006/redis_exporter

docker run -d --name mongodb-exporter \
  -p 9216:9216 \
  percona/mongodb_exporter
```

### 3. 测试数据准备

```go
package main

import (
    "context"
    "fmt"
    "math/rand"
    "time"
    
    "your-project/internal/service/base/extlike"
)

// 生成测试数据
func generateTestData(service extlike.ExtendedLikeService, userCount, itemCount int) error {
    ctx := context.Background()
    
    // 生成用户点赞数据
    for i := 0; i < userCount; i++ {
        userID := fmt.Sprintf("user_%d", i)
        
        // 每个用户随机点赞一些物品
        likeCount := rand.Intn(50) + 10 // 10-60个点赞
        for j := 0; j < likeCount; j++ {
            itemID := fmt.Sprintf("video_%d", rand.Intn(itemCount))
            service.LikeItem(ctx, userID, "video", itemID)
        }
        
        if i%1000 == 0 {
            fmt.Printf("已生成 %d 个用户的数据\n", i)
        }
    }
    
    return nil
}

// 清理测试数据
func cleanTestData(service extlike.ExtendedLikeService) error {
    ctx := context.Background()
    return service.FlushCache(ctx, "")
}
```

## 基准测试

### 1. 基础操作基准测试

```go
package extlike_test

import (
    "context"
    "fmt"
    "testing"
    "time"
    
    "your-project/internal/service/base/extlike"
)

var (
    testService extlike.ExtendedLikeService
    testCtx     = context.Background()
)

func BenchmarkLikeItem(b *testing.B) {
    userID := "bench_user"
    itemType := "video"
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        i := 0
        for pb.Next() {
            itemID := fmt.Sprintf("video_%d", i)
            testService.LikeItem(testCtx, userID, itemType, itemID)
            i++
        }
    })
}

func BenchmarkUnlikeItem(b *testing.B) {
    userID := "bench_user"
    itemType := "video"
    
    // 预先创建点赞数据
    for i := 0; i < b.N; i++ {
        itemID := fmt.Sprintf("video_%d", i)
        testService.LikeItem(testCtx, userID, itemType, itemID)
    }
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        i := 0
        for pb.Next() {
            itemID := fmt.Sprintf("video_%d", i)
            testService.UnlikeItem(testCtx, userID, itemType, itemID)
            i++
        }
    })
}

func BenchmarkIsLiked(b *testing.B) {
    userID := "bench_user"
    itemType := "video"
    itemID := "video_1"
    
    // 预先创建点赞
    testService.LikeItem(testCtx, userID, itemType, itemID)
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            testService.IsLiked(testCtx, userID, itemType, itemID)
        }
    })
}

func BenchmarkGetLikeCount(b *testing.B) {
    itemType := "video"
    itemID := "video_1"
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            testService.GetLikeCount(testCtx, itemType, itemID)
        }
    })
}

func BenchmarkBatchLike(b *testing.B) {
    userID := "bench_user"
    itemType := "video"
    batchSize := 10
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        operations := make([]*extlike.LikeOperation, batchSize)
        for j := 0; j < batchSize; j++ {
            operations[j] = &extlike.LikeOperation{
                UserID:   userID,
                ItemType: itemType,
                ItemID:   fmt.Sprintf("video_%d_%d", i, j),
                Action:   "like",
            }
        }
        testService.BatchLike(testCtx, operations)
    }
}

func BenchmarkBatchGetLikeStatus(b *testing.B) {
    userID := "bench_user"
    itemType := "video"
    batchSize := 10
    
    // 预先创建数据
    for i := 0; i < batchSize; i++ {
        itemID := fmt.Sprintf("video_%d", i)
        testService.LikeItem(testCtx, userID, itemType, itemID)
    }
    
    itemIDs := make([]string, batchSize)
    for i := 0; i < batchSize; i++ {
        itemIDs[i] = fmt.Sprintf("video_%d", i)
    }
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            testService.BatchGetLikeStatus(testCtx, userID, itemType, itemIDs)
        }
    })
}

func BenchmarkGetHotItems(b *testing.B) {
    itemType := "video"
    limit := 10
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            testService.GetHotItems(testCtx, itemType, limit)
        }
    })
}
```

### 2. 运行基准测试

```bash
# 运行所有基准测试
go test -bench=. -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof

# 运行特定基准测试
go test -bench=BenchmarkLikeItem -benchmem -count=5

# 生成性能报告
go tool pprof cpu.prof
go tool pprof mem.prof

# 比较不同版本性能
benchcmp old.txt new.txt
```

### 3. 基准测试结果分析

```
# 示例输出
BenchmarkLikeItem-8                    50000    25000 ns/op    1024 B/op    12 allocs/op
BenchmarkUnlikeItem-8                  45000    27000 ns/op    1152 B/op    14 allocs/op
BenchmarkIsLiked-8                    100000    12000 ns/op     512 B/op     6 allocs/op
BenchmarkGetLikeCount-8               120000    10000 ns/op     256 B/op     3 allocs/op
BenchmarkBatchLike-8                   10000   150000 ns/op    8192 B/op    45 allocs/op
BenchmarkBatchGetLikeStatus-8          20000    75000 ns/op    4096 B/op    25 allocs/op
BenchmarkGetHotItems-8                 30000    45000 ns/op    2048 B/op    18 allocs/op
```

**性能指标说明**:
- `ns/op`: 每次操作的纳秒数
- `B/op`: 每次操作分配的字节数
- `allocs/op`: 每次操作的内存分配次数

## 压力测试

### 1. HTTP API 压力测试

```bash
# 使用 hey 进行压力测试

# 点赞接口压力测试
hey -n 10000 -c 100 -m POST \
  -H "Content-Type: application/json" \
  -d '{"user_id":"user123","item_type":"video","item_id":"video456"}' \
  http://localhost:8080/api/v1/like

# 查询接口压力测试
hey -n 10000 -c 100 \
  "http://localhost:8080/api/v1/like/count?item_type=video&item_id=video456"

# 热门排行压力测试
hey -n 5000 -c 50 \
  "http://localhost:8080/api/v1/like/hot?item_type=video&limit=10"
```

### 2. 使用 Vegeta 进行压力测试

```bash
# 创建测试目标文件
cat > targets.txt << EOF
POST http://localhost:8080/api/v1/like
Content-Type: application/json
@like_payload.json

GET http://localhost:8080/api/v1/like/count?item_type=video&item_id=video456

GET http://localhost:8080/api/v1/like/hot?item_type=video&limit=10
EOF

# 创建请求负载
cat > like_payload.json << EOF
{
  "user_id": "user123",
  "item_type": "video",
  "item_id": "video456"
}
EOF

# 执行压力测试
vegeta attack -targets=targets.txt -rate=100 -duration=60s | vegeta report
vegeta attack -targets=targets.txt -rate=100 -duration=60s | vegeta plot > plot.html
```

### 3. 自定义压力测试脚本

```go
package main

import (
    "context"
    "fmt"
    "sync"
    "sync/atomic"
    "time"
    
    "your-project/internal/service/base/extlike"
)

type StressTestResult struct {
    TotalRequests   int64
    SuccessRequests int64
    FailedRequests  int64
    AvgLatency      time.Duration
    MaxLatency      time.Duration
    MinLatency      time.Duration
    ThroughputQPS   float64
}

func StressTestLikeItem(service extlike.ExtendedLikeService, 
    concurrency int, duration time.Duration) *StressTestResult {
    
    ctx := context.Background()
    var (
        totalRequests   int64
        successRequests int64
        failedRequests  int64
        totalLatency    int64
        maxLatency      int64
        minLatency      int64 = int64(time.Hour)
    )
    
    startTime := time.Now()
    endTime := startTime.Add(duration)
    
    var wg sync.WaitGroup
    
    // 启动并发goroutine
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            
            requestCount := 0
            for time.Now().Before(endTime) {
                requestStart := time.Now()
                
                userID := fmt.Sprintf("user_%d", workerID)
                itemID := fmt.Sprintf("video_%d_%d", workerID, requestCount)
                
                err := service.LikeItem(ctx, userID, "video", itemID)
                
                latency := time.Since(requestStart).Nanoseconds()
                atomic.AddInt64(&totalRequests, 1)
                atomic.AddInt64(&totalLatency, latency)
                
                if err != nil {
                    atomic.AddInt64(&failedRequests, 1)
                } else {
                    atomic.AddInt64(&successRequests, 1)
                }
                
                // 更新最大最小延迟
                for {
                    current := atomic.LoadInt64(&maxLatency)
                    if latency <= current || atomic.CompareAndSwapInt64(&maxLatency, current, latency) {
                        break
                    }
                }
                
                for {
                    current := atomic.LoadInt64(&minLatency)
                    if latency >= current || atomic.CompareAndSwapInt64(&minLatency, current, latency) {
                        break
                    }
                }
                
                requestCount++
            }
        }(i)
    }
    
    wg.Wait()
    
    actualDuration := time.Since(startTime)
    
    return &StressTestResult{
        TotalRequests:   totalRequests,
        SuccessRequests: successRequests,
        FailedRequests:  failedRequests,
        AvgLatency:      time.Duration(totalLatency / totalRequests),
        MaxLatency:      time.Duration(maxLatency),
        MinLatency:      time.Duration(minLatency),
        ThroughputQPS:   float64(totalRequests) / actualDuration.Seconds(),
    }
}

func main() {
    // 初始化服务
    service := initializeService()
    
    // 执行压力测试
    result := StressTestLikeItem(service, 100, 60*time.Second)
    
    // 输出结果
    fmt.Printf("压力测试结果:\n")
    fmt.Printf("总请求数: %d\n", result.TotalRequests)
    fmt.Printf("成功请求数: %d\n", result.SuccessRequests)
    fmt.Printf("失败请求数: %d\n", result.FailedRequests)
    fmt.Printf("成功率: %.2f%%\n", float64(result.SuccessRequests)/float64(result.TotalRequests)*100)
    fmt.Printf("平均延迟: %v\n", result.AvgLatency)
    fmt.Printf("最大延迟: %v\n", result.MaxLatency)
    fmt.Printf("最小延迟: %v\n", result.MinLatency)
    fmt.Printf("吞吐量: %.2f QPS\n", result.ThroughputQPS)
}
```

## 并发测试

### 1. 并发安全性测试

```go
func TestConcurrentLikeUnlike(t *testing.T) {
    service := initializeService()
    ctx := context.Background()
    
    userID := "concurrent_user"
    itemType := "video"
    itemID := "video_concurrent"
    
    concurrency := 100
    iterations := 1000
    
    var wg sync.WaitGroup
    var likeCount, unlikeCount int64
    
    // 并发点赞和取消点赞
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            
            for j := 0; j < iterations; j++ {
                if j%2 == 0 {
                    err := service.LikeItem(ctx, userID, itemType, itemID)
                    if err == nil {
                        atomic.AddInt64(&likeCount, 1)
                    }
                } else {
                    err := service.UnlikeItem(ctx, userID, itemType, itemID)
                    if err == nil {
                        atomic.AddInt64(&unlikeCount, 1)
                    }
                }
            }
        }()
    }
    
    wg.Wait()
    
    // 验证最终状态
    finalCount, err := service.GetLikeCount(ctx, itemType, itemID)
    if err != nil {
        t.Fatalf("获取最终计数失败: %v", err)
    }
    
    expectedCount := likeCount - unlikeCount
    if finalCount != expectedCount {
        t.Errorf("并发测试失败: 期望计数=%d, 实际计数=%d", expectedCount, finalCount)
    }
    
    t.Logf("并发测试完成: 点赞=%d, 取消点赞=%d, 最终计数=%d", 
        likeCount, unlikeCount, finalCount)
}
```

### 2. 数据一致性测试

```go
func TestDataConsistency(t *testing.T) {
    service := initializeService()
    ctx := context.Background()
    
    userCount := 100
    itemCount := 50
    
    var wg sync.WaitGroup
    
    // 并发创建点赞数据
    for i := 0; i < userCount; i++ {
        wg.Add(1)
        go func(userIndex int) {
            defer wg.Done()
            
            userID := fmt.Sprintf("user_%d", userIndex)
            for j := 0; j < itemCount; j++ {
                itemID := fmt.Sprintf("video_%d", j)
                service.LikeItem(ctx, userID, "video", itemID)
            }
        }(i)
    }
    
    wg.Wait()
    
    // 验证数据一致性
    for i := 0; i < itemCount; i++ {
        itemID := fmt.Sprintf("video_%d", i)
        
        // 从不同存储获取计数
        count, err := service.GetLikeCount(ctx, "video", itemID)
        if err != nil {
            t.Errorf("获取计数失败: %v", err)
            continue
        }
        
        // 验证计数是否正确
        expectedCount := int64(userCount)
        if count != expectedCount {
            t.Errorf("数据不一致: 物品%s 期望计数=%d, 实际计数=%d", 
                itemID, expectedCount, count)
        }
    }
}
```

## 内存测试

### 1. 内存泄漏测试

```go
func TestMemoryLeak(t *testing.T) {
    service := initializeService()
    ctx := context.Background()
    
    // 获取初始内存使用
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)
    
    // 执行大量操作
    for i := 0; i < 100000; i++ {
        userID := fmt.Sprintf("user_%d", i%1000)
        itemID := fmt.Sprintf("video_%d", i%5000)
        
        service.LikeItem(ctx, userID, "video", itemID)
        service.IsLiked(ctx, userID, "video", itemID)
        service.GetLikeCount(ctx, "video", itemID)
        
        if i%10000 == 0 {
            runtime.GC()
        }
    }
    
    // 强制垃圾回收
    runtime.GC()
    runtime.GC()
    runtime.ReadMemStats(&m2)
    
    // 检查内存增长
    memoryGrowth := m2.Alloc - m1.Alloc
    if memoryGrowth > 50*1024*1024 { // 50MB
        t.Errorf("可能存在内存泄漏: 内存增长 %d 字节", memoryGrowth)
    }
    
    t.Logf("内存使用: 初始=%d, 最终=%d, 增长=%d", 
        m1.Alloc, m2.Alloc, memoryGrowth)
}
```

### 2. 内存使用监控

```go
func MonitorMemoryUsage(service extlike.ExtendedLikeService, duration time.Duration) {
    ctx := context.Background()
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    endTime := time.Now().Add(duration)
    
    fmt.Println("时间\t\t内存使用(MB)\tGoroutines\tGC次数")
    
    for time.Now().Before(endTime) {
        select {
        case <-ticker.C:
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            
            fmt.Printf("%s\t%.2f\t\t%d\t\t%d\n",
                time.Now().Format("15:04:05"),
                float64(m.Alloc)/1024/1024,
                runtime.NumGoroutine(),
                m.NumGC)
                
            // 执行一些操作
            userID := fmt.Sprintf("user_%d", time.Now().Unix()%1000)
            itemID := fmt.Sprintf("video_%d", time.Now().Unix()%5000)
            service.LikeItem(ctx, userID, "video", itemID)
        }
    }
}
```

## 缓存性能测试

### 1. 缓存命中率测试

```go
func TestCacheHitRate(t *testing.T) {
    service := initializeService()
    ctx := context.Background()
    
    // 预热缓存
    for i := 0; i < 1000; i++ {
        itemID := fmt.Sprintf("video_%d", i)
        service.GetLikeCount(ctx, "video", itemID)
    }
    
    // 获取初始缓存统计
    initialStats, err := service.GetCacheStats(ctx, "video")
    if err != nil {
        t.Fatalf("获取缓存统计失败: %v", err)
    }
    
    // 执行查询操作
    queryCount := 10000
    for i := 0; i < queryCount; i++ {
        itemID := fmt.Sprintf("video_%d", i%1000) // 重复查询提高命中率
        service.GetLikeCount(ctx, "video", itemID)
    }
    
    // 获取最终缓存统计
    finalStats, err := service.GetCacheStats(ctx, "video")
    if err != nil {
        t.Fatalf("获取缓存统计失败: %v", err)
    }
    
    // 计算命中率
    hitCount := finalStats.HitCount - initialStats.HitCount
    missCount := finalStats.MissCount - initialStats.MissCount
    totalQueries := hitCount + missCount
    hitRate := float64(hitCount) / float64(totalQueries)
    
    t.Logf("缓存性能测试结果:")
    t.Logf("总查询数: %d", totalQueries)
    t.Logf("命中次数: %d", hitCount)
    t.Logf("未命中次数: %d", missCount)
    t.Logf("命中率: %.2f%%", hitRate*100)
    
    if hitRate < 0.8 { // 期望命中率80%以上
        t.Errorf("缓存命中率过低: %.2f%%", hitRate*100)
    }
}
```

### 2. 缓存性能基准测试

```go
func BenchmarkCachePerformance(b *testing.B) {
    service := initializeService()
    ctx := context.Background()
    
    // 预热缓存
    for i := 0; i < 1000; i++ {
        itemID := fmt.Sprintf("video_%d", i)
        service.LikeItem(ctx, "user_1", "video", itemID)
    }
    
    b.ResetTimer()
    
    b.Run("CacheHit", func(b *testing.B) {
        b.RunParallel(func(pb *testing.PB) {
            i := 0
            for pb.Next() {
                itemID := fmt.Sprintf("video_%d", i%1000)
                service.GetLikeCount(ctx, "video", itemID)
                i++
            }
        })
    })
    
    b.Run("CacheMiss", func(b *testing.B) {
        b.RunParallel(func(pb *testing.PB) {
            i := 0
            for pb.Next() {
                itemID := fmt.Sprintf("video_new_%d", i)
                service.GetLikeCount(ctx, "video", itemID)
                i++
            }
        })
    })
}
```

## 数据库性能测试

### 1. 数据库连接池测试

```go
func TestDatabaseConnectionPool(t *testing.T) {
    service := initializeService()
    ctx := context.Background()
    
    concurrency := 200
    duration := 30 * time.Second
    
    var wg sync.WaitGroup
    var successCount, errorCount int64
    
    startTime := time.Now()
    endTime := startTime.Add(duration)
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            
            requestCount := 0
            for time.Now().Before(endTime) {
                userID := fmt.Sprintf("user_%d", workerID)
                itemID := fmt.Sprintf("video_%d_%d", workerID, requestCount)
                
                err := service.LikeItem(ctx, userID, "video", itemID)
                if err != nil {
                    atomic.AddInt64(&errorCount, 1)
                } else {
                    atomic.AddInt64(&successCount, 1)
                }
                
                requestCount++
                time.Sleep(10 * time.Millisecond) // 模拟真实请求间隔
            }
        }(i)
    }
    
    wg.Wait()
    
    totalRequests := successCount + errorCount
    errorRate := float64(errorCount) / float64(totalRequests)
    
    t.Logf("数据库连接池测试结果:")
    t.Logf("并发数: %d", concurrency)
    t.Logf("测试时长: %v", duration)
    t.Logf("总请求数: %d", totalRequests)
    t.Logf("成功请求数: %d", successCount)
    t.Logf("失败请求数: %d", errorCount)
    t.Logf("错误率: %.2f%%", errorRate*100)
    
    if errorRate > 0.01 { // 错误率不应超过1%
        t.Errorf("数据库连接池错误率过高: %.2f%%", errorRate*100)
    }
}
```

### 2. 数据库查询性能测试

```go
func BenchmarkDatabaseQueries(b *testing.B) {
    service := initializeService()
    ctx := context.Background()
    
    // 准备测试数据
    for i := 0; i < 10000; i++ {
        userID := fmt.Sprintf("user_%d", i%1000)
        itemID := fmt.Sprintf("video_%d", i)
        service.LikeItem(ctx, userID, "video", itemID)
    }
    
    b.ResetTimer()
    
    b.Run("SimpleQuery", func(b *testing.B) {
        b.RunParallel(func(pb *testing.PB) {
            i := 0
            for pb.Next() {
                itemID := fmt.Sprintf("video_%d", i%10000)
                service.GetLikeCount(ctx, "video", itemID)
                i++
            }
        })
    })
    
    b.Run("ComplexQuery", func(b *testing.B) {
        b.RunParallel(func(pb *testing.PB) {
            i := 0
            for pb.Next() {
                userID := fmt.Sprintf("user_%d", i%1000)
                service.GetUserLikes(ctx, userID, "video", 20, 0)
                i++
            }
        })
    })
    
    b.Run("BatchQuery", func(b *testing.B) {
        itemIDs := make([]string, 10)
        for i := 0; i < 10; i++ {
            itemIDs[i] = fmt.Sprintf("video_%d", i)
        }
        
        b.RunParallel(func(pb *testing.PB) {
            for pb.Next() {
                service.BatchGetLikeCounts(ctx, "video", itemIDs)
            }
        })
    })
}
```

## 网络延迟测试

### 1. 网络延迟模拟

```bash
# 使用 tc 命令模拟网络延迟

# 添加延迟
sudo tc qdisc add dev eth0 root netem delay 100ms

# 添加延迟和丢包
sudo tc qdisc add dev eth0 root netem delay 100ms loss 1%

# 添加延迟抖动
sudo tc qdisc add dev eth0 root netem delay 100ms 20ms

# 清除设置
sudo tc qdisc del dev eth0 root
```

### 2. 网络延迟测试代码

```go
func TestNetworkLatency(t *testing.T) {
    service := initializeService()
    ctx := context.Background()
    
    latencies := []time.Duration{}
    requestCount := 1000
    
    for i := 0; i < requestCount; i++ {
        start := time.Now()
        
        userID := fmt.Sprintf("user_%d", i)
        itemID := fmt.Sprintf("video_%d", i)
        
        err := service.LikeItem(ctx, userID, "video", itemID)
        if err != nil {
            t.Errorf("请求失败: %v", err)
            continue
        }
        
        latency := time.Since(start)
        latencies = append(latencies, latency)
    }
    
    // 计算统计信息
    var totalLatency time.Duration
    minLatency := latencies[0]
    maxLatency := latencies[0]
    
    for _, latency := range latencies {
        totalLatency += latency
        if latency < minLatency {
            minLatency = latency
        }
        if latency > maxLatency {
            maxLatency = latency
        }
    }
    
    avgLatency := totalLatency / time.Duration(len(latencies))
    
    // 计算P95延迟
    sort.Slice(latencies, func(i, j int) bool {
        return latencies[i] < latencies[j]
    })
    p95Index := int(float64(len(latencies)) * 0.95)
    p95Latency := latencies[p95Index]
    
    t.Logf("网络延迟测试结果:")
    t.Logf("请求数量: %d", requestCount)
    t.Logf("平均延迟: %v", avgLatency)
    t.Logf("最小延迟: %v", minLatency)
    t.Logf("最大延迟: %v", maxLatency)
    t.Logf("P95延迟: %v", p95Latency)
    
    if avgLatency > 100*time.Millisecond {
        t.Errorf("平均延迟过高: %v", avgLatency)
    }
}
```

## 性能监控

### 1. 实时性能监控

```go
package monitoring

import (
    "context"
    "fmt"
    "runtime"
    "sync/atomic"
    "time"
    
    "your-project/internal/service/base/extlike"
)

type PerformanceMonitor struct {
    service         extlike.ExtendedLikeService
    requestCount    int64
    errorCount      int64
    totalLatency    int64
    maxLatency      int64
    minLatency      int64
    startTime       time.Time
}

func NewPerformanceMonitor(service extlike.ExtendedLikeService) *PerformanceMonitor {
    return &PerformanceMonitor{
        service:    service,
        startTime:  time.Now(),
        minLatency: int64(time.Hour),
    }
}

func (pm *PerformanceMonitor) RecordRequest(latency time.Duration, err error) {
    atomic.AddInt64(&pm.requestCount, 1)
    atomic.AddInt64(&pm.totalLatency, int64(latency))
    
    if err != nil {
        atomic.AddInt64(&pm.errorCount, 1)
    }
    
    // 更新最大最小延迟
    latencyNs := int64(latency)
    for {
        current := atomic.LoadInt64(&pm.maxLatency)
        if latencyNs <= current || atomic.CompareAndSwapInt64(&pm.maxLatency, current, latencyNs) {
            break
        }
    }
    
    for {
        current := atomic.LoadInt64(&pm.minLatency)
        if latencyNs >= current || atomic.CompareAndSwapInt64(&pm.minLatency, current, latencyNs) {
            break
        }
    }
}

func (pm *PerformanceMonitor) GetStats() map[string]interface{} {
    requestCount := atomic.LoadInt64(&pm.requestCount)
    errorCount := atomic.LoadInt64(&pm.errorCount)
    totalLatency := atomic.LoadInt64(&pm.totalLatency)
    maxLatency := atomic.LoadInt64(&pm.maxLatency)
    minLatency := atomic.LoadInt64(&pm.minLatency)
    
    duration := time.Since(pm.startTime)
    
    var avgLatency time.Duration
    if requestCount > 0 {
        avgLatency = time.Duration(totalLatency / requestCount)
    }
    
    var m runtime.MemStats
    runtime.ReadMemStats(&m)
    
    return map[string]interface{}{
        "request_count":    requestCount,
        "error_count":      errorCount,
        "error_rate":       float64(errorCount) / float64(requestCount),
        "avg_latency":      avgLatency,
        "max_latency":      time.Duration(maxLatency),
        "min_latency":      time.Duration(minLatency),
        "qps":              float64(requestCount) / duration.Seconds(),
        "uptime":           duration,
        "memory_usage":     m.Alloc,
        "goroutines":       runtime.NumGoroutine(),
        "gc_count":         m.NumGC,
    }
}

func (pm *PerformanceMonitor) StartReporting(interval time.Duration) {
    ticker := time.NewTicker(interval)
    go func() {
        for range ticker.C {
            stats := pm.GetStats()
            fmt.Printf("[%s] 性能统计: %+v\n", time.Now().Format("2006-01-02 15:04:05"), stats)
        }
    }()
}
```

### 2. Prometheus 指标导出

```go
package metrics

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    requestsTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "extlike_requests_total",
            Help: "Total number of requests",
        },
        []string{"method", "status"},
    )
    
    requestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name:    "extlike_request_duration_seconds",
            Help:    "Request duration in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method"},
    )
    
    cacheHitRate = promauto.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "extlike_cache_hit_rate",
            Help: "Cache hit rate",
        },
        []string{"item_type"},
    )
    
    activeConnections = promauto.NewGauge(
        prometheus.GaugeOpts{
            Name: "extlike_active_connections",
            Help: "Number of active database connections",
        },
    )
)

func RecordRequest(method, status string, duration float64) {
    requestsTotal.WithLabelValues(method, status).Inc()
    requestDuration.WithLabelValues(method).Observe(duration)
}

func UpdateCacheHitRate(itemType string, hitRate float64) {
    cacheHitRate.WithLabelValues(itemType).Set(hitRate)
}

func UpdateActiveConnections(count float64) {
    activeConnections.Set(count)
}
```

## 性能优化建议

### 1. 代码层面优化

```go
// 1. 使用对象池减少内存分配
var operationPool = sync.Pool{
    New: func() interface{} {
        return &LikeOperation{}
    },
}

func GetOperation() *LikeOperation {
    return operationPool.Get().(*LikeOperation)
}

func PutOperation(op *LikeOperation) {
    op.UserID = ""
    op.ItemType = ""
    op.ItemID = ""
    op.Action = ""
    operationPool.Put(op)
}

// 2. 批量操作优化
func (s *extendedLikeService) OptimizedBatchLike(ctx context.Context, operations []*LikeOperation) ([]bool, error) {
    // 按存储类型分组
    groups := make(map[string][]*LikeOperation)
    for _, op := range operations {
        key := op.ItemType
        groups[key] = append(groups[key], op)
    }
    
    // 并行处理不同类型
    var wg sync.WaitGroup
    results := make([]bool, len(operations))
    
    for itemType, ops := range groups {
        wg.Add(1)
        go func(itemType string, ops []*LikeOperation) {
            defer wg.Done()
            // 处理该类型的操作
            s.processBatchOperations(ctx, itemType, ops, results)
        }(itemType, ops)
    }
    
    wg.Wait()
    return results, nil
}

// 3. 连接池优化
func OptimizeConnectionPool() {
    // Redis连接池优化
    redisClient := redis.NewClient(&redis.Options{
        PoolSize:        runtime.NumCPU() * 4,
        MinIdleConns:    runtime.NumCPU(),
        MaxRetries:      3,
        DialTimeout:     5 * time.Second,
        ReadTimeout:     3 * time.Second,
        WriteTimeout:    3 * time.Second,
        PoolTimeout:     4 * time.Second,
        IdleTimeout:     5 * time.Minute,
        IdleCheckFrequency: time.Minute,
    })
    
    // MongoDB连接池优化
    clientOptions := options.Client().ApplyURI(uri)
    clientOptions.SetMaxPoolSize(uint64(runtime.NumCPU() * 10))
    clientOptions.SetMinPoolSize(uint64(runtime.NumCPU()))
    clientOptions.SetMaxConnIdleTime(10 * time.Minute)
    clientOptions.SetConnectTimeout(10 * time.Second)
    clientOptions.SetSocketTimeout(5 * time.Second)
}
```

### 2. 缓存优化策略

```go
// 1. 多级缓存
type MultiLevelCache struct {
    l1Cache *sync.Map          // 本地缓存
    l2Cache redis.Cmdable      // Redis缓存
    l3Store mongodb.LikeService // MongoDB存储
}

func (c *MultiLevelCache) Get(key string) (interface{}, error) {
    // L1缓存查询
    if value, ok := c.l1Cache.Load(key); ok {
        return value, nil
    }
    
    // L2缓存查询
    value, err := c.l2Cache.Get(context.Background(), key).Result()
    if err == nil {
        c.l1Cache.Store(key, value)
        return value, nil
    }
    
    // L3存储查询
    // ...
    return nil, err
}

// 2. 缓存预热
func (s *extendedLikeService) WarmupCache(ctx context.Context) error {
    // 预加载热门物品
    hotItems, err := s.GetHotItems(ctx, "video", 1000)
    if err != nil {
        return err
    }
    
    // 批量预加载到缓存
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, 10) // 限制并发数
    
    for _, item := range hotItems {
        wg.Add(1)
        go func(itemID string) {
            defer wg.Done()
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            s.GetLikeCount(ctx, "video", itemID)
        }(item.ItemID)
    }
    
    wg.Wait()
    return nil
}
```

### 3. 数据库优化

```sql
-- 1. 索引优化
CREATE INDEX idx_user_item ON like_records(user_id, item_type, item_id);
CREATE INDEX idx_item_time ON like_records(item_type, item_id, created_at);
CREATE INDEX idx_time_type ON like_records(created_at, item_type);

-- 2. 分区表
CREATE TABLE like_records_2024_01 PARTITION OF like_records
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 3. 物化视图
CREATE MATERIALIZED VIEW like_stats AS
SELECT 
    item_type,
    item_id,
    COUNT(*) as like_count,
    COUNT(DISTINCT user_id) as unique_users
FROM like_records 
WHERE action = 'like'
GROUP BY item_type, item_id;

CREATE UNIQUE INDEX ON like_stats(item_type, item_id);
```

## 测试报告模板

### 性能测试报告

```markdown
# 扩展点赞服务性能测试报告

## 测试概述

**测试时间**: 2024-01-15 10:00:00 - 2024-01-15 18:00:00
**测试环境**: 生产环境模拟
**测试工具**: Go Benchmark, hey, vegeta
**测试数据量**: 100万用户, 500万物品, 1亿点赞记录

## 测试结果汇总

### 基准测试结果

| 操作 | QPS | 平均延迟 | P95延迟 | 内存使用 | CPU使用率 |
|------|-----|----------|---------|----------|----------|
| LikeItem | 15,000 | 2.5ms | 8ms | 512MB | 45% |
| UnlikeItem | 14,500 | 2.8ms | 9ms | 510MB | 43% |
| IsLiked | 25,000 | 1.2ms | 3ms | 256MB | 25% |
| GetLikeCount | 30,000 | 1.0ms | 2.5ms | 128MB | 20% |
| BatchLike | 8,000 | 12ms | 25ms | 1GB | 60% |
| GetHotItems | 5,000 | 8ms | 15ms | 512MB | 35% |

### 压力测试结果

**测试场景**: 1000并发用户, 持续60分钟

- **总请求数**: 54,000,000
- **成功请求数**: 53,946,000 (99.9%)
- **失败请求数**: 54,000 (0.1%)
- **平均QPS**: 15,000
- **峰值QPS**: 18,500
- **平均响应时间**: 3.2ms
- **P95响应时间**: 12ms
- **P99响应时间**: 25ms

### 缓存性能

- **Redis命中率**: 95.2%
- **本地缓存命中率**: 78.5%
- **缓存更新延迟**: 平均1.5ms
- **缓存内存使用**: 2.5GB

### 数据库性能

- **MongoDB连接池使用率**: 65%
- **平均查询时间**: 5.8ms
- **慢查询数量**: 23 (>100ms)
- **索引命中率**: 99.8%

## 性能瓶颈分析

### 1. CPU瓶颈
- **问题**: 批量操作时CPU使用率过高
- **原因**: JSON序列化/反序列化开销大
- **建议**: 使用更高效的序列化方案

### 2. 内存瓶颈
- **问题**: 长时间运行后内存使用持续增长
- **原因**: 缓存数据过多，GC压力大
- **建议**: 实现缓存LRU淘汰策略

### 3. 网络瓶颈
- **问题**: 高并发时网络延迟增加
- **原因**: 连接池配置不当
- **建议**: 优化连接池参数

## 优化建议

### 短期优化 (1-2周)
1. 调整Redis连接池大小
2. 优化批量操作的批次大小
3. 增加本地缓存容量

### 中期优化 (1-2月)
1. 实现读写分离
2. 添加数据库分片
3. 优化数据库索引

### 长期优化 (3-6月)
1. 微服务拆分
2. 引入消息队列
3. 实现多级缓存架构

## 结论

扩展点赞服务在当前配置下能够满足业务需求，但在高并发场景下存在一些性能瓶颈。建议按照优化建议逐步改进，预期可以将性能提升30-50%。
```

通过以上全面的性能测试指南，开发者可以系统地评估和优化扩展点赞服务的性能，确保服务在生产环境中的稳定运行。