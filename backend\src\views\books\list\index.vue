<template>
  <el-scrollbar>
    <div class="book-list">
      <el-card>
        <template #header>
          <div class="flex justify-between items-center">
            <span class="card-title">{{ t('books.info.manage') }}</span>
            <el-button type="primary" @click="onAdd">{{ $t('books.info.addNew') }}</el-button>
          </div>
        </template>

        <!-- 搜索栏组件 -->
        <SearchBar
          :categoryOptions="categoryOptions"
          @search="onSearch"
          @reset="onReset"
          @refresh="fetchBookList"
        />

        <!-- 电子书表格组件 -->
        <BookTable
          :loading="loading"
          :bookList="bookList"
          @edit="onEdit"
          @detail="onDetail"
          @delete="onDelete"
          @manage-chapters="onManageChapters"
        />

        <!-- 分页组件 -->
        <Pagination
          :total="total"
          :current="currentPage"
          :size="pageSize"
          @pagination="onPageChange"
        />
      </el-card>

      <!-- 电子书表单对话框 -->
      <BookDialog
        ref="bookDialogRef"
        :visible="dialogVisible"
        :book="currentBook"
        :categoryOptions="categoryOptions"
        @submit="onDialogSubmit"
        @close="dialogVisible = false"
      />

      <!-- 电子书详情弹窗 -->
      <BookDetailDialog
        :visible="detailDialogVisible"
        :book="currentBook"
        @edit="onEdit"
        @close="detailDialogVisible = false"
      />
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getBookList, deleteBook, createBook, updateBook } from '@/service/api/books/books';
import { useI18n } from 'vue-i18n';
import { getAllBookCategories } from '@/service/api/books/category';
import SearchBar from './components/SearchBar.vue';
import BookTable from './components/BookTable.vue';
import Pagination from './components/Pagination.vue';
import BookDialog from './components/BookDialog.vue';
import BookDetailDialog from './components/BookDetailDialog.vue';
import { BookItem, BookCategory } from '@/types/books';

const { t } = useI18n();
const router = useRouter();


// 初始化标签页
onMounted(() => {
  // 添加当前页面到标签栏
  // 初始化数据
  fetchBookList();
  fetchCategories();
});

// 数据状态
const loading = ref(false);
const bookList = ref<BookItem[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref<Record<string, any>>({});
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const currentBook = ref<BookItem>({} as BookItem);
const categoryOptions = ref<BookCategory[]>([]);

// 获取电子书列表
const fetchBookList = async () => {
  loading.value = true;
  try {
    const params = {
      page: {
        pageNo: currentPage.value,
        pageSize: pageSize.value,
      },
      data: {
        title: searchParams.value.title || '',
        category_id: searchParams.value.categoryId || '',
        author: searchParams.value.author || '',
        progress: searchParams.value.progress || '',
        is_featured: searchParams.value.isFeatured !== undefined ? parseInt(searchParams.value.isFeatured) : undefined,
      }
    };

    const {response,data} = await getBookList(params) as any;
    if (response.data.code === 2000) {
      bookList.value = data.list;
      total.value = data.total || 0;
    } else {
      console.error(response?.data.msg || t('books.info.dataLoadFailed'));
    }
  } catch (error) {
    console.error('获取电子书列表出错:', error);
    console.error(t('books.info.dataLoadFailed'));
  } finally {
    loading.value = false;
  }
};


// 获取所有可用分类
const fetchCategories = async () => {
  try {
    const {response,data} = await getAllBookCategories() as any;
    if (response.data.code === 2000) {
      categoryOptions.value = data || [];
      console.log(categoryOptions.value);
    }
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
};

// 处理搜索
const onSearch = (formData: Record<string, any>) => {
  searchParams.value = formData;
  currentPage.value = 1;
  fetchBookList();

};

// 处理重置
const onReset = () => {
  searchParams.value = {};
  currentPage.value = 1;
  fetchBookList();
};

// 处理分页
const onPageChange = (params: { page: number; pageSize: number }) => {
  currentPage.value = params.page;
  pageSize.value = params.pageSize;
  fetchBookList();
};

// 处理添加电子书
const onAdd = () => {
  currentBook.value = {} as BookItem;
  dialogVisible.value = true;
};

// 处理编辑电子书
const onEdit = (row: BookItem) => {
  currentBook.value = {...row};
  console.log("编辑电子书",currentBook.value);
  dialogVisible.value = true;
};

// 处理查看详情
const onDetail = (row: BookItem) => {
  currentBook.value = {...row};
  detailDialogVisible.value = true;
};

// 处理删除电子书
const onDelete = async (row: BookItem) => {
  try {
    await ElMessageBox.confirm(
      t('books.info.deleteConfirmContent'),
      t('books.info.deleteConfirm'),
      {
        confirmButtonText: t('buttons.confirm'),
        cancelButtonText: t('buttons.cancel'),
        type: 'warning'
      }
    );

    const {response,data} = await deleteBook(row.id) as any;
    if (response.data.code === 2000) {
      ElMessage.success(t('books.info.deleteSuccess'));
      fetchBookList();
    } else {
      ElMessage.error(response?.data?.msg || t('books.info.deleteFailed'));
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除电子书出错:', error);
      ElMessage.error(t('books.info.deleteFailed'));
    }
  }
};

// 处理管理章节
const onManageChapters = (row: BookItem) => {
  router.push({
    path: '/books/chapter',
    query: { bookId: row.id,bookName:row.title }
  });
};

// 处理对话框提交
const onDialogSubmit = async (subData: BookItem) => {
  console.log(subData);
  try {
    let res;
    if (subData.id) {
      // 更新电子书
      res = await updateBook({data:subData}) as any;
    } else {
      // 添加电子书
      res = await createBook({data:subData}) as any;
    }
    const {response,data} = res;
    if (response.data.code === 2000) {
      ElMessage.success(subData.id ? t('books.info.updateSuccess') : t('books.info.addSuccess'));
      dialogVisible.value = false;
      fetchBookList();
    } else {
      ElMessage.error(response.data.message || (subData.id ? t('books.info.updateFailed') : t('books.info.addFailed')));
    }
  } catch (error) {
    console.error('提交电子书数据出错:', error);
    ElMessage.error(subData.id ? t('books.info.updateFailed') : t('books.info.addFailed'));
  }
};
</script>

<style scoped>
.book-list {
  padding: 20px;
}
</style>
