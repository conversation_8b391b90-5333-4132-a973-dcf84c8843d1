package repository

import (
	"context"
	"fmt"
	pictureRepo "frontapi/internal/repository/pictures"
	"testing"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 测试 IN 和 NOT IN 操作符查询
func TestInNotInOperator(t *testing.T) {
	// 连接数据库
	dsn := "root:password@tcp(127.0.0.1:3306)/lydb?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		t.Fatalf("连接数据库失败: %v", err)
	}

	// 创建图片专辑仓库
	albumRepo := pictureRepo.NewPictureAlbumRepository(db)

	// 测试场景1: 使用 IN 操作符查询
	testIDs := []string{"test-album-id-1", "test-album-id-2", "test-album-id-3"} // 替换为实际存在的ID
	condition := map[string]interface{}{
		"id IN":  testIDs,
		"status": 1,
	}

	// 执行查询
	albums, total, err := albumRepo.List(context.Background(), condition, "created_at DESC", 1, 10)
	if err != nil {
		t.Fatalf("查询失败: %v", err)
	}

	// 验证结果
	fmt.Printf("IN 查询到 %d 条记录，总数: %d\n", len(albums), total)
	for i, album := range albums {
		// 检查是否在指定的ID列表中
		found := false
		for _, id := range testIDs {
			if album.ID == id {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("查询结果中包含不在指定ID列表中的记录: %s", album.ID)
		}
		fmt.Printf("专辑 %d: ID=%s, 标题=%s\n", i+1, album.ID, album.Title)
	}

	// 测试场景2: 使用 NOT IN 操作符查询
	condition2 := map[string]interface{}{
		"id NOT IN": testIDs,
		"status":    1,
	}

	// 执行查询
	albums2, total2, err := albumRepo.List(context.Background(), condition2, "created_at DESC", 1, 10)
	if err != nil {
		t.Fatalf("查询失败: %v", err)
	}

	// 验证结果
	fmt.Printf("NOT IN 查询到 %d 条记录，总数: %d\n", len(albums2), total2)
	for i, album := range albums2 {
		// 检查是否不在指定的ID列表中
		for _, id := range testIDs {
			if album.ID == id {
				t.Errorf("查询结果中不应该包含ID为 %s 的记录", id)
				break
			}
		}
		fmt.Printf("专辑 %d: ID=%s, 标题=%s\n", i+1, album.ID, album.Title)
	}

	// 测试场景3: 组合使用 IN 和其他条件
	condition3 := map[string]interface{}{
		"id IN":           testIDs,
		"status":          1,
		"picture_count >": 0,
	}

	// 执行查询
	albums3, total3, err := albumRepo.List(context.Background(), condition3, "created_at DESC", 1, 10)
	if err != nil {
		t.Fatalf("查询失败: %v", err)
	}

	// 验证结果
	fmt.Printf("IN + 其他条件查询到 %d 条记录，总数: %d\n", len(albums3), total3)
	for i, album := range albums3 {
		// 检查是否在指定的ID列表中
		found := false
		for _, id := range testIDs {
			if album.ID == id {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("查询结果中包含不在指定ID列表中的记录: %s", album.ID)
		}
		if album.PictureCount <= 0 {
			t.Errorf("查询结果中的图片数量应该大于0，但实际为 %d", album.PictureCount)
		}
		fmt.Printf("专辑 %d: ID=%s, 标题=%s, 图片数量=%d\n", i+1, album.ID, album.Title, album.PictureCount)
	}
}
