<template>
    <div
        class="video-list-item cursor-pointer bg-surface rounded-lg overflow-hidden shadow hover:shadow-md transition-shadow">
        <!-- 视频缩略图 -->
        <div class="thumbnail-container relative">
            <img :src="video.thumbnail" :alt="video.title" class="w-full h-[180px] object-cover" />

            <!-- 视频时长 -->
            <div
                class="duration absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                {{ formatDuration(video.duration) }}
            </div>

            <!-- 付费标识 -->
            <div v-if="video.isPremium"
                class="premium-badge absolute top-2 left-2 bg-primary text-white text-xs px-2 py-1 rounded-full">
                VIP
            </div>
        </div>

        <!-- 视频信息 -->
        <div class="video-info p-3">
            <h3 class="video-title font-medium text-base line-clamp-2" :title="video.title">
                {{ video.title }}
            </h3>

            <div class="video-meta flex justify-between items-center mt-2 text-sm text-text-light">
                <span class="video-author">{{ video.author }}</span>
                <div class="video-stats flex gap-2 items-center">
                    <span class="video-views flex items-center">
                        <i class="pi pi-eye mr-1"></i>
                        {{ formatNumber(video.views) }}
                    </span>
                    <span class="video-date">
                        {{ formatDate(video.uploadDate) }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

// 视频接口
interface Video {
    id: number;
    title: string;
    thumbnail: string;
    duration: number;
    views: number;
    uploadDate: Date;
    author: string;
    isPremium: boolean;
}

// 组件属性
const props = defineProps<{
    video: Video;
}>();

// 格式化视频时长 (秒 -> HH:MM:SS)
const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
};

// 格式化数字 (例如: 1.2k, 1.3M)
const formatNumber = (num: number) => {
    if (num >= 1000000) {
        return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
        return `${(num / 1000).toFixed(1)}k`;
    } else {
        return num.toString();
    }
};

// 格式化日期 (例如: 1天前, 3周前)
const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
        return '今天';
    } else if (diffDays < 7) {
        return `${diffDays}天前`;
    } else if (diffDays < 30) {
        return `${Math.floor(diffDays / 7)}周前`;
    } else if (diffDays < 365) {
        return `${Math.floor(diffDays / 30)}月前`;
    } else {
        return `${Math.floor(diffDays / 365)}年前`;
    }
};
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-list-item {
    transition: all 0.3s ease;
}

.premium-badge {
    font-weight: 600;
}
</style>