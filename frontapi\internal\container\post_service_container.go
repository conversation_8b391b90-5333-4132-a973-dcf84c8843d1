package container

import (
	postRepo "frontapi/internal/repository/posts"
	postServices "frontapi/internal/service/posts"
)

// InitPostServices 初始化帖子相关服务
func InitPostServices(b *ServiceBuilder) {
	// 初始化帖子仓库
	postRepoImpl := postRepo.NewPostRepository(b.DB())
	postLikeRepoImpl := postRepo.NewPostLikeRepository(b.DB())
	postCommentRepoImpl := postRepo.NewCommentRepository(b.DB())
	postCommentLikeRepoImpl := postRepo.NewPostCommentLikeRepository(b.DB())

	// 初始化帖子服务
	container := b.Services()
	container.PostService = postServices.NewPostService(postRepoImpl, postLikeRepoImpl)
	container.PostCommentService = postServices.NewPostCommentService(postCommentRepoImpl, postRepoImpl, postCommentLikeRepoImpl)
}
