package api

import (
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

func RegisterCoinPackageRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// group := apiGroup.Group("/coin_packages")
	// group.Post("/add", middleware.AuthRequired(), api.Create)
	// group.Post("/detail/:id?", api.Get)
	// group.Post("/udpate", middleware.AuthRequired(), api.Update)
	// group.Post("/delete/:id?", middleware.AuthRequired(), api.Delete)
	// group.Post("/list", api.List)
}
