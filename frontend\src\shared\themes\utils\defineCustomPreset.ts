/**
 * 定义自定义主题预设
 */
interface CustomPresetOptions {
    name: string;
    variables: Record<string, any>;
    components?: Record<string, any>;
}

/**
 * 定义自定义主题预设
 */
export function defineCustomPreset(options: CustomPresetOptions) {
    const { name, variables, components = {} } = options;

    return {
        name,
        variables,
        components,

        // 应用自定义变量
        apply() {
            // 将变量应用到CSS变量
            Object.entries(variables).forEach(([key, value]) => {
                document.documentElement.style.setProperty(`--pv-custom-${key}`, value);
            });

            console.log(`[Theme] Applied custom preset: ${name}`);
            return true;
        }
    };
}

// 确保导出
export default defineCustomPreset; 