package admin

import (
	adminVideos "frontapi/internal/admin/videos"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterVideoRoutes 注册视频相关路由
func RegisterVideoRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	//videoController *videos.VideoController, videoChannelController *videos.VideoChannelController
	// 初始化视频服务和控制器
	videoService := services.VideoService
	videoCategoryService := services.VideoCategoryService
	videoCommentService := services.VideoCommentService
	videoChannelService := services.VideoChannelService
	videoAlbumService := services.VideoAlbumService

	// 注册视频相关路由组
	videoApi := apiGroup.Group("/videos", middleware.AuthRequired())
	{
		videoController := adminVideos.NewVideoController(
			videoService,
			videoCategoryService,
			videoCommentService,
			videoChannelService,
			services.UserService,
		)
		// 公开接口
		videoApi.Post("/list", videoController.ListVideos)
		videoApi.Post("/detail/:id?", videoController.GetVideo)
		videoApi.Post("/search", videoController.ListVideos)
		videoApi.Post("/category/:categoryId?", videoController.ListVideos)
		//videoApi.Get("/featured", videoController.ListFeaturedVideos)

		// 管理接口 - 视频CRUD操作
		videoApi.Post("/add", videoController.CreateVideo)
		videoApi.Post("/update/:id?", videoController.UpdateVideo)
		videoApi.Post("/delete/:id?", videoController.DeleteVideo)

		// 添加审核相关路由
		videoApi.Post("/review/:id?", videoController.ReviewVideo)
		videoApi.Post("/batch-review", videoController.BatchReviewVideo)
		videoApi.Post("/update-status/:id?", videoController.UpdateVideoStatus)
		videoApi.Post("/batch-update-status", videoController.BatchUpdateVideoStatus)
		videoApi.Post("/batch-delete", videoController.BatchDeleteVideo)
	}

	videoCategoryController := adminVideos.NewVideoCategoryController(videoService, videoCategoryService)
	// 视频分类相关路由组
	categories := apiGroup.Group("/video-categories", middleware.AuthRequired())
	{
		// 公开接口
		categories.Post("/list", videoCategoryController.ListCategories)
		categories.Post("/detail/:id?", videoCategoryController.GetCategory)
		categories.Post("/add", videoCategoryController.CreateVideoCategory)
		categories.Post("/update", videoCategoryController.UpdateVideoCategory)
		categories.Post("/update-status/:id?", videoCategoryController.UpdateVideoCategoryStatus)
		categories.Post("/batch-update-status", videoCategoryController.BatchUpdateVideoCategoryStatus)
		categories.Post("/delete/:id?", videoCategoryController.DeleteVideoCategory)
	}

	// 视频频道相关路由组
	channels := apiGroup.Group("/video-channels", middleware.AuthRequired())
	{
		videoChannelController := adminVideos.NewVideoChannelController(videoChannelService)
		// 公开接口
		channels.Post("/list", videoChannelController.ListChannels)
		channels.Post("/detail/:id?", videoChannelController.GetChannel)
		channels.Post("/add", videoChannelController.CreateChannel)
		channels.Post("/update/:id?", videoChannelController.UpdateChannel)
		channels.Post("/delete/:id?", videoChannelController.DeleteChannel)
		channels.Post("/update-status/:id?", videoChannelController.UpdateChannelStatus)
		channels.Post("/batch-update-status", videoChannelController.BatchUpdateChannelStatus)
	}

	// 视频专辑相关路由组
	albums := apiGroup.Group("/video-albums", middleware.AuthRequired())
	{
		videoAlbumController := adminVideos.NewVideoAlbumController(
			videoAlbumService,
			services.UserService,
			videoCategoryService,
		)
		// 专辑管理接口
		albums.Post("/list", videoAlbumController.ListVideoAlbums)
		albums.Post("/detail/:id?", videoAlbumController.GetVideoAlbum)
		albums.Post("/add", videoAlbumController.CreateVideoAlbum)
		albums.Post("/update/:id?", videoAlbumController.UpdateVideoAlbum)
		albums.Post("/delete/:id?", videoAlbumController.DeleteVideoAlbum)
		albums.Post("/update-status/:id?", videoAlbumController.UpdateVideoAlbumStatus)
		albums.Post("/batch-update-status", videoAlbumController.BatchUpdateVideoAlbumStatus)
		albums.Post("/batch-delete", videoAlbumController.BatchDeleteVideoAlbum)
	}

	// 视频评论相关路由组
	comments := apiGroup.Group("/video-comments", middleware.AuthRequired())
	{
		videoCommentController := adminVideos.NewVideoCommentController(videoCommentService)
		// 公开接口
		comments.Post("/list", videoCommentController.ListComments)
		comments.Post("/detail/:id?", videoCommentController.GetComment)
		comments.Post("/delete/:id?", videoCommentController.DeleteComment)
		comments.Post("/replies", videoCommentController.ListReplies)
		comments.Post("/update-status", videoCommentController.UpdateCommentStatus)
		comments.Post("/batch-update-status", videoCommentController.BatchUpdateCommentStatus)
		comments.Post("/batch-delete", videoCommentController.BatchDeleteComment)
	}
}
