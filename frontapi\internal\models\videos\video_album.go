package videos

import (
	"frontapi/internal/models"
	"frontapi/internal/models/users"
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"
)

type VideoAlbum struct {
	models.BaseModelStruct
	Title        string            `json:"title" gorm:"comment:专辑标题"`                       // 专辑标题
	Description  string            `json:"description" gorm:"comment:描述"`                   // 描述
	UserID       null.String       `json:"user_id" gorm:"comment:创建人"`                      // 创建人
	UserNickname null.String       `json:"user_nickname" gorm:"comment:创建人昵称"`              // 创建人昵称
	UserAvatar   null.String       `json:"user_avatar" gorm:"comment:创建人头像"`                // 创建人头像
	Cover        null.String       `json:"cover" gorm:"comment:封面"`                         // 封面
	Heat         uint64            `json:"heat" gorm:"comment:热度"`                          // 热度
	ViewCount    uint64            `json:"view_count" gorm:"comment:观看次数"`                  // 观看次数
	VideoCount   uint64            `json:"video_count" gorm:"comment:视频数量"`                 // 视频数量
	CategoryID   null.String       `json:"category_id" gorm:"comment:分类"`                   // 分类
	CategoryName null.String       `json:"category_name" gorm:"comment:分类名称"`               // 分类名称
	Tags         types.StringArray `json:"tags" gorm:"type:json;comment:标签"`                // 标签
	IsPaid       int8              `json:"is_paid" gorm:"comment:是否付费:0-免费,1-付费"`           // 是否付费
	Price        float64           `json:"price" gorm:"comment:价格"`                         // 价格
	Status       int8              `json:"status" gorm:"default:1;comment:状态:0-禁用,1-正常"`    // 状态
	Author       *users.User       `json:"author" gorm:"-;foreignKey:UserID;references:ID"` // 专辑作者
	Videos       []*Video          `json:"videos" gorm:"-"`                                 // 专辑视频列表
	IsLiked      bool              `json:"is_liked" gorm:"-"`                               // 是否点赞
	IsFavorite   bool              `json:"is_favorite" gorm:"-"`                            // 是否收藏
}

func (VideoAlbum) TableName() string {
	return "ly_video_albums"
}

// 实现BaseModel接口的方法
func (v *VideoAlbum) SetCreatedAt(time types.JSONTime) {
	v.BaseModelStruct.SetCreatedAt(time)
}

func (v *VideoAlbum) SetUpdatedAt(time types.JSONTime) {
	v.BaseModelStruct.SetUpdatedAt(time)
}

func (v *VideoAlbum) SetID(id string) {
	v.BaseModelStruct.SetID(id)
}

func (v *VideoAlbum) SetStatus(status int8) {
	v.BaseModelStruct.SetStatus(status)
}
