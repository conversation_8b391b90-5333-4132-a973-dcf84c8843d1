<template>
    <el-tag :type="tagType" :effect="tagEffect" size="small">
        <el-icon class="mr-1" v-if="icon">
            <component :is="icon" />
        </el-icon>
        {{ statusText }}
    </el-tag>
</template>

<script setup lang="ts">
import { Check, CircleClose } from '@element-plus/icons-vue';
import type { TagProps } from 'element-plus';
import { computed } from 'vue';

const props = defineProps({
    status: {
        type: Number,
        required: true
    }
});

// 状态文本
const statusText = computed(() => {
    switch (props.status) {
        case 1:
            return '正常';
        case 0:
            return '禁用';
        case -4:
            return '已删除';
        default:
            return '未知';
    }
});

// 标签类型
const tagType = computed(() => {
    switch (props.status) {
        case 1:
            return 'success';
        case 0:
            return 'warning';
        case -4:
            return 'danger';
        default:
            return 'info';
    }
});

// 标签效果
const tagEffect = computed<TagProps['effect']>(() => {
    return 'light';
});

// 图标
const icon = computed(() => {
    switch (props.status) {
        case 1:
            return Check;
        case 0:
        case -4:
            return CircleClose;
        default:
            return '';
    }
});
</script>

<style scoped lang="scss">
.el-tag {
    display: inline-flex;
    align-items: center;
}
</style>