package main

import (
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"frontapi/config"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"
	"frontapi/internal/routes"
	"frontapi/pkg/cache"
	"frontapi/pkg/database"
	fileStorage "frontapi/pkg/file"
	redisClient "frontapi/pkg/redis"
	"frontapi/pkg/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

// 命令行参数
var (
	enableAPI   = flag.Bool("api", true, "启用API服务")
	enableAdmin = flag.Bool("admin", true, "启用Admin服务")
	enableMedia = flag.Bool("media", true, "启用Media媒体服务器")
	enableCron  = flag.Bool("cron", true, "启用定时任务")
)

func init() {
	flag.Parse()
	// 这里可以添加环境变量加载逻辑
}

func main() {
	// 初始化配置
	config.LoadConfig()

	// 初始化数据库连接
	err := database.InitDB()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 初始化Redis连接
	if err := redisClient.Init(); err != nil {
		log.Printf("警告: Redis初始化失败: %v, 某些功能可能不可用", err)
	} else {
		log.Println("Redis初始化成功")
	}

	// 初始化服务
	services := bootstrap.InitServices(database.DB)

	// 初始化缓存管理器

	if err := cache.InitCacheWithConfig(&config.AppConfig.Cache); err != nil {
		// 如果缓存初始化失败，使用简单缓存作为回退
		log.Printf("警告: 缓存系统初始化失败: %v, 将使用简单内存缓存", err)
	}
	// 根据启用的服务创建goroutine启动对应服务器
	if *enableAPI {
		go startAPIServer(services)
	}

	if *enableAdmin {
		go startAdminServer(services)
	}

	if *enableMedia {
		go startMediaServer()
	}

	if *enableCron {
		go startCronJobs()
	}

	// 检查是否至少启用了一个服务器
	if !(*enableAPI || *enableAdmin || *enableMedia || *enableCron) {
		log.Fatal("错误: 至少需要启用一个服务器(API、Admin、Media或Cron)")
	}

	// 设置优雅关闭
	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)

	log.Println("所有服务器已启动，按 Ctrl+C 退出...")
	<-sigterm

	log.Println("正在关闭所有服务器...")

	// 关闭Redis连接
	if err := redisClient.Close(); err != nil {
		log.Printf("关闭Redis连接失败: %v", err)
	}

	// 关闭缓存
	if err := cache.GetGlobalManager().Close(); err != nil {
		log.Printf("关闭缓存失败: %v", err)
	}
}

// startAPIServer 启动API服务器
func startAPIServer(services *bootstrap.ServiceContainer) {
	// API服务器启动代码
	app := fiber.New(fiber.Config{
		AppName:               "FrontAPI - API服务器",
		DisableStartupMessage: false,
	})

	// 添加中间件
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[API] ${time} | ${status} | ${latency} | ${method} | ${path}\n",
	}))

	// CORS配置
	app.Use(middleware.Cors())
	// 注册API路由
	routes.RegisterAPIRoutesWithContainer(app, services)

	// 获取API服务器端口
	port := utils.GetEnv("SERVER_PORT", "8080")

	// 启动API服务器
	log.Printf("🚀 API服务器开始运行在端口 %s\n", port)
	if err := app.Listen(":" + port); err != nil && err != http.ErrServerClosed {
		log.Fatalf("API服务器启动失败: %v", err)
	}
}

// startAdminServer 启动Admin服务器
func startAdminServer(services *bootstrap.ServiceContainer) {
	// Admin服务器启动代码
	app := fiber.New(fiber.Config{
		AppName:               "FrontAPI - Admin管理后台服务器",
		DisableStartupMessage: false,
		BodyLimit:             2048 * 1024 * 1024, // 2048 MB
	})

	// 添加中间件
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[Admin] ${time} | ${status} | ${latency} | ${method} | ${path}\n",
	}))

	// CORS配置
	app.Use(middleware.Cors())

	// 添加OPTIONS请求的特殊处理
	app.Options("*", func(c *fiber.Ctx) error {
		return c.SendStatus(fiber.StatusOK)
	})

	// 注册Admin路由
	routes.RegisterAdminRoutesWithContainer(app, services)

	// 获取Admin服务器端口
	port := utils.GetEnv("ADMIN_SERVER_PORT", "8081")

	// 启动Admin服务器
	log.Printf("🚀 Admin管理后台服务器开始运行在端口 %s\n", port)
	if err := app.Listen(":" + port); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Admin服务器启动失败: %v", err)
	}
}

// startMediaServer 启动媒体文件服务器
func startMediaServer() {
	// 创建媒体服务器
	app := fiber.New(fiber.Config{
		AppName:               "FrontAPI - Media媒体服务器",
		DisableStartupMessage: false,
		BodyLimit:             1024 * 1024 * 1024, // 1GB
	})

	// 添加中间件
	app.Use(recover.New())
	app.Use(logger.New(logger.Config{
		Format: "[Media] ${time} | ${status} | ${latency} | ${method} | ${path}\n",
	}))

	// CORS配置
	app.Use(middleware.Cors())

	// 注册媒体服务路由
	registerMediaRoutes(app)

	// 获取媒体服务器端口
	port := fmt.Sprintf("%d", config.AppConfig.Server.MediaPort)

	// 启动媒体服务器
	log.Printf("🚀 Media媒体服务器开始运行在端口 %s (存储路径: %s)\n", port, fileStorage.GetStoragePath())
	if err := app.Listen(":" + port); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Media服务器启动失败: %v", err)
	}
}

// registerMediaRoutes 注册媒体服务路由
func registerMediaRoutes(app *fiber.App) {
	// 使用fileStorage包获取存储路径
	storageManager := fileStorage.GetStorageManager()

	// 静态文件服务 - 支持子目录访问
	app.Static("/videos", storageManager.GetVideosPath(), fiber.Static{
		Compress:      true,
		ByteRange:     true,
		Browse:        false,
		CacheDuration: 24 * time.Hour,
		MaxAge:        86400,
	})

	app.Static("/pictures", storageManager.GetPicturesPath(), fiber.Static{
		Compress:      true,
		ByteRange:     true,
		Browse:        false,
		CacheDuration: 24 * time.Hour,
		MaxAge:        86400,
	})

	app.Static("/static", storageManager.GetStaticPath(), fiber.Static{
		Compress:      true,
		Browse:        false,
		CacheDuration: 24 * time.Hour,
		MaxAge:        86400,
	})

	// 健康检查接口
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status":       "ok",
			"service":      "media-server",
			"timestamp":    time.Now().Unix(),
			"storage_path": storageManager.GetStoragePath(),
		})
	})

	// 文件上传接口
	app.Post("/upload/:type", handleFileUpload)

	// 文件信息接口
	app.Get("/info/:type/:filename", handleFileInfo)

	// 文件删除接口
	app.Delete("/delete/:type/:filename", handleFileDelete)
}

// handleFileUpload 处理文件上传
func handleFileUpload(c *fiber.Ctx) error {
	fileType := c.Params("type")

	// 验证文件类型
	if fileType != "videos" && fileType != "pictures" && fileType != "static" {
		return c.Status(400).JSON(fiber.Map{
			"error": "无效的文件类型",
		})
	}

	// 获取上传的文件
	uploadedFile, err := c.FormFile("file")
	if err != nil {
		return c.Status(400).JSON(fiber.Map{
			"error": "获取文件失败: " + err.Error(),
		})
	}

	// 生成文件名
	filename := fmt.Sprintf("%d_%s", time.Now().Unix(), uploadedFile.Filename)

	// 使用存储管理器获取完整路径
	storageManager := fileStorage.GetStorageManager()
	fullPath := storageManager.GetFullPath(fileType, filename)

	// 保存文件
	if err := c.SaveFile(uploadedFile, fullPath); err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "保存文件失败: " + err.Error(),
		})
	}

	// 返回文件信息
	return c.JSON(fiber.Map{
		"success":  true,
		"filename": filename,
		"path":     fmt.Sprintf("/%s/%s", fileType, filename),
		"size":     uploadedFile.Size,
		"type":     fileType,
	})
}

// handleFileInfo 处理文件信息查询
func handleFileInfo(c *fiber.Ctx) error {
	fileType := c.Params("type")
	filename := c.Params("filename")

	// 验证文件类型
	if fileType != "videos" && fileType != "pictures" && fileType != "static" {
		return c.Status(400).JSON(fiber.Map{
			"error": "无效的文件类型",
		})
	}

	// 使用存储管理器获取完整路径
	storageManager := fileStorage.GetStorageManager()
	fullPath := storageManager.GetFullPath(fileType, filename)

	// 检查文件是否存在
	info, err := os.Stat(fullPath)
	if os.IsNotExist(err) {
		return c.Status(404).JSON(fiber.Map{
			"error": "文件不存在",
		})
	}
	if err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "获取文件信息失败: " + err.Error(),
		})
	}

	// 返回文件信息
	return c.JSON(fiber.Map{
		"filename":    filename,
		"size":        info.Size(),
		"type":        fileType,
		"modified_at": info.ModTime().Unix(),
		"url":         fmt.Sprintf("/%s/%s", fileType, filename),
	})
}

// handleFileDelete 处理文件删除
func handleFileDelete(c *fiber.Ctx) error {
	fileType := c.Params("type")
	filename := c.Params("filename")

	// 验证文件类型
	if fileType != "videos" && fileType != "pictures" && fileType != "static" {
		return c.Status(400).JSON(fiber.Map{
			"error": "无效的文件类型",
		})
	}

	// 使用存储管理器获取完整路径
	storageManager := fileStorage.GetStorageManager()
	fullPath := storageManager.GetFullPath(fileType, filename)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return c.Status(404).JSON(fiber.Map{
			"error": "文件不存在",
		})
	}

	// 删除文件
	if err := os.Remove(fullPath); err != nil {
		return c.Status(500).JSON(fiber.Map{
			"error": "删除文件失败: " + err.Error(),
		})
	}

	// 返回成功信息
	return c.JSON(fiber.Map{
		"success": true,
		"message": "文件删除成功",
	})
}

// startCronJobs 启动定时任务
func startCronJobs() {
	// 定时任务启动代码
	log.Println("定时任务已启动")
}
