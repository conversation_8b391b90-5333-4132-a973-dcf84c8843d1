# 评论组件系统

现代化的评论组件系统，提供完整的评论展示、互动和媒体预览功能。

## 🎯 最新更新

### v2.0 重大更新 (2024-12-19)

**核心改进：**
- ✅ **统一媒体展示**：使用通用的 `MediaGrid` 和 `ImagePreview` 组件
- ✅ **视频播放优化**：集成 `MiniVideoPlayer` 组件，提供专业的视频播放体验
- ✅ **用户交互升级**：使用 `UserHoverCard` 组件，支持用户悬浮卡片和关注功能
- ✅ **内容左对齐**：评论文字内容采用左对齐布局，提升可读性
- ✅ **代码优化**：简化组件结构，提高可维护性

**技术栈：**
- Vue 3 + TypeScript
- Element Plus UI 框架
- 响应式设计
- 深色主题支持

## 📁 组件结构

```
frontend/src/components/comments/
├── Index.vue                    # 主评论容器组件
├── ModernCommentItem.vue        # 评论项组件 (已更新)
├── ModernCommentList.vue        # 评论列表组件 (已更新)
├── CommentMediaDisplay.vue      # 媒体展示组件 (已重构)
├── ModernCommentInput.vue       # 评论输入组件
└── README.md                    # 文档说明

frontend/src/components/
├── MediaGrid.vue                # 通用媒体网格组件 (新增)
├── ImagePreview.vue             # 图片预览组件
├── UserHoverCard/               # 用户悬浮卡片组件
└── miniplayer/                  # 视频播放器组件
```

## 🎨 主要功能

### 1. 媒体展示功能

**图片展示：**
- 智能布局：单图、双图、三图、九宫格自适应
- 图片预览：点击放大查看，支持左右切换
- 懒加载优化：提升页面性能
- 响应式设计：移动端友好

**视频播放：**
- 专业播放器：使用 `MiniVideoPlayer` 组件
- 自动播放控制：支持自动播放开关
- 播放状态管理：播放、暂停、错误处理
- 悬停控制：鼠标悬停显示控制栏

### 2. 用户交互功能

**用户头像：**
- 悬浮卡片：鼠标悬停显示用户详情
- 用户信息：头像、昵称、认证状态、等级
- 关注功能：支持关注/取消关注操作
- 点击跳转：点击头像跳转用户主页

**评论互动：**
- 点赞功能：支持点赞/取消点赞
- 回复功能：支持回复评论和嵌套回复
- 更多操作：举报、复制等功能

### 3. 内容展示优化

**文本内容：**
- 左对齐布局：提升可读性
- 富文本支持：@用户、链接识别
- 换行处理：保持原始格式
- 内容格式化：自动处理特殊字符

**布局设计：**
- 现代化UI：圆角、阴影、渐变效果
- 响应式布局：适配各种屏幕尺寸
- 深色主题：自动适配系统主题
- 动画效果：悬停、点击动画

## 🔧 使用示例

### 基础使用

```vue
<template>
  <CommentBox
    :target-id="postId"
    target-type="post"
    :comments="comments"
    :total-comments="totalComments"
    :loading="loading"
    :has-more="hasMore"
    @comment-added="handleCommentAdded"
    @comment-liked="handleCommentLiked"
    @load-comments="handleLoadComments"
  />
</template>

<script setup>
import CommentBox from '@/shared/components/social/comments/Index.vue'

// 评论数据和事件处理
</script>
```

### 媒体展示

```vue
<template>
  <!-- 图片展示 -->
  <MediaGrid
    :images="imageList"
    @media-click="handleImageClick"
  />
  
  <!-- 视频展示 -->
  <MediaGrid
    :video="videoUrl"
    :enable-auto-play="true"
    @video-play="handleVideoPlay"
  />
  
  <!-- 图片预览 -->
  <ImagePreview
    v-model="showPreview"
    :images="previewImages"
    :initial-index="currentIndex"
  />
</template>
```

### 用户交互

```vue
<template>
  <UserHoverCard
    :user-info="userInfo"
    :avatar-size="36"
    @click="handleUserClick"
    @follow="handleFollow"
    @unfollow="handleUnfollow"
  />
</template>
```

## 📱 响应式设计

**桌面端 (>768px)：**
- 完整功能展示
- 大尺寸头像和媒体
- 详细的用户信息

**移动端 (≤768px)：**
- 紧凑布局
- 小尺寸头像
- 简化的操作按钮
- 触摸友好的交互

## 🎨 主题支持

**浅色主题：**
- 白色背景
- 深色文字
- 蓝色强调色

**深色主题：**
- 深色背景
- 浅色文字
- 蓝色强调色
- 自动适配系统设置

## 🔄 API 接口

### 事件系统

```typescript
// 评论相关事件
interface CommentEvents {
  'comment-added': [comment: CreateCommentData]
  'comment-liked': [commentId: string, liked: boolean]
  'comment-replied': [parentId: string, reply: CreateCommentData]
  'load-comments': [page: number]
  'load-more': []
  'refresh': []
}

// 媒体相关事件
interface MediaEvents {
  'media-click': [media: MediaItem, index: number]
  'video-play': [media: MediaItem]
  'video-pause': [media: MediaItem]
  'video-error': [error: Error]
}

// 用户相关事件
interface UserEvents {
  'click': [user: User]
  'follow': [user: User]
  'unfollow': [user: User]
}
```

### 数据类型

```typescript
// 评论数据结构
interface Comment {
  id: string
  content: string
  userId: string
  userNickname: string
  userAvatar: string
  images?: string[]
  video?: string
  likeCount: number
  isLiked: boolean
  createdAt: string
  author?: CompactUser
  replies?: Comment[]
}

// 媒体项结构
interface MediaItem {
  type: 'image' | 'video' | 'audio'
  url: string
  thumbnail?: string
  duration?: number
}

// 用户信息结构
interface User {
  id: string
  username: string
  nickname: string
  avatar: string
  userType: number
  level?: number
  isFollowed: boolean
  // ... 其他字段
}
```

## 🚀 性能优化

**图片优化：**
- 懒加载：只加载可见区域的图片
- 缩略图：列表中使用缩略图
- 预加载：预览时预加载相邻图片

**组件优化：**
- 虚拟滚动：大量评论时的性能优化
- 组件懒加载：按需加载组件
- 缓存机制：缓存用户信息和媒体数据

**交互优化：**
- 防抖处理：避免频繁的API调用
- 乐观更新：先更新UI，再同步服务器
- 错误重试：网络错误时自动重试

## 🔧 开发指南

### 添加新功能

1. **扩展媒体类型**：在 `MediaGrid` 组件中添加新的媒体类型支持
2. **自定义主题**：修改 SCSS 变量定义自定义主题
3. **添加交互**：在相应组件中添加新的事件和处理逻辑

### 最佳实践

1. **类型安全**：使用 TypeScript 确保类型安全
2. **组件复用**：优先使用通用组件，避免重复代码
3. **性能考虑**：大量数据时考虑虚拟滚动和懒加载
4. **用户体验**：提供加载状态、错误处理和友好的交互反馈

## 📝 更新日志

### v2.0.0 (2024-12-19)
- 🎉 重构媒体展示系统，使用统一的 `MediaGrid` 组件
- 🎥 集成专业视频播放器 `MiniVideoPlayer`
- 👤 升级用户交互，使用 `UserHoverCard` 组件
- 📝 优化文本布局，采用左对齐设计
- 🧹 代码重构，提高可维护性和性能

### v1.0.0 (2024-12-18)
- 🎉 初始版本发布
- 💬 基础评论功能
- 🖼️ 图片展示功能
- 🎬 视频播放功能
- 📱 响应式设计
- 🌙 深色主题支持

---

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进评论组件系统！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## �� 许可证

MIT License