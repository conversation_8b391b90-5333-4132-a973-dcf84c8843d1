# 缓存系统使用示例

本文档提供了缓存系统的各种使用示例，帮助开发者快速上手。

## 基本用法

### 创建缓存实例

```go
package main

import (
    "context"
    "fmt"
    "time"

    "frontapi/pkg/cache"
    "frontapi/pkg/cache/memory"
)

func main() {
    // 创建内存缓存适配器
    memoryAdapter, err := memory.New(&memory.Config{
        CleanupInterval: time.Minute,
        MaxEntries: 1000,
    })
    if err != nil {
        panic(err)
    }
    defer memoryAdapter.Close()

    ctx := context.Background()

    // 设置缓存
    err = memoryAdapter.Set(ctx, "user:123", []byte(`{"name":"张三","age":30}`), time.Minute*10)
    if err != nil {
        panic(err)
    }

    // 获取缓存
    value, err := memoryAdapter.Get(ctx, "user:123")
    if err != nil {
        if err == cache.ErrNotFound {
            fmt.Println("缓存未找到")
        } else {
            panic(err)
        }
    } else {
        fmt.Printf("缓存值: %s\n", value)
    }

    // 删除缓存
    err = memoryAdapter.Delete(ctx, "user:123")
    if err != nil {
        panic(err)
    }
}
```

### 使用 Redis 缓存

```go
package main

import (
    "context"
    "time"

    "frontapi/pkg/cache/redis"
)

func main() {
    // 创建 Redis 缓存适配器
    redisAdapter, err := redis.New(&redis.Config{
        Host:     "localhost",
        Port:     6379,
        Password: "",
        DB:       0,
    })
    if err != nil {
        panic(err)
    }
    defer redisAdapter.Close()

    ctx := context.Background()

    // 设置缓存
    err = redisAdapter.Set(ctx, "session:abc123", []byte("user_id=456"), time.Hour)
    if err != nil {
        panic(err)
    }

    // 检查键是否存在
    exists, err := redisAdapter.Exists(ctx, "session:abc123")
    if err != nil {
        panic(err)
    }
    if exists {
        // 获取过期时间
        ttl, err := redisAdapter.TTL(ctx, "session:abc123")
        if err != nil {
            panic(err)
        }
        fmt.Printf("键将在 %v 后过期\n", ttl)
    }
}
```

### 使用文件缓存

```go
package main

import (
    "context"
    "time"

    "frontapi/pkg/cache/file"
)

func main() {
    // 创建文件缓存适配器
    fileAdapter, err := file.New(&file.Config{
        Path:            "./cache",
        CleanupInterval: time.Hour,
    })
    if err != nil {
        panic(err)
    }
    defer fileAdapter.Close()

    ctx := context.Background()

    // 批量设置缓存
    items := map[string][]byte{
        "product:1": []byte(`{"name":"手机","price":1999}`),
        "product:2": []byte(`{"name":"电脑","price":5999}`),
        "product:3": []byte(`{"name":"耳机","price":299}`),
    }
    err = fileAdapter.MSet(ctx, items, time.Hour*24)
    if err != nil {
        panic(err)
    }

    // 批量获取缓存
    keys := []string{"product:1", "product:2", "product:3"}
    results, err := fileAdapter.MGet(ctx, keys)
    if err != nil {
        panic(err)
    }

    for k, v := range results {
        fmt.Printf("%s: %s\n", k, v)
    }
}
```

## 高级用法

### 使用集群缓存

```go
package main

import (
    "context"
    "time"

    "frontapi/pkg/cache"
    "frontapi/pkg/cache/cluster"
    "frontapi/pkg/cache/redis"
)

func main() {
    // 创建多个 Redis 实例作为集群节点
    redis1, _ := redis.New(&redis.Config{Host: "redis1.example.com", Port: 6379})
    redis2, _ := redis.New(&redis.Config{Host: "redis2.example.com", Port: 6379})
    redis3, _ := redis.New(&redis.Config{Host: "redis3.example.com", Port: 6379})

    // 创建集群配置
    clusterConfig := &cluster.Config{
        Nodes:        []cache.CacheAdapter{redis1, redis2, redis3},
        VirtualNodes: 100,
    }

    // 创建集群适配器
    clusterAdapter, err := cluster.New(clusterConfig)
    if err != nil {
        panic(err)
    }
    defer clusterAdapter.Close()

    ctx := context.Background()

    // 使用集群缓存
    // 数据会根据键的哈希值分布到不同的节点上
    for i := 0; i < 10; i++ {
        key := fmt.Sprintf("key:%d", i)
        value := []byte(fmt.Sprintf("value:%d", i))
        err = clusterAdapter.Set(ctx, key, value, time.Minute)
        if err != nil {
            panic(err)
        }
    }

    // 动态添加节点
    redis4, _ := redis.New(&redis.Config{Host: "redis4.example.com", Port: 6379})
    err = clusterAdapter.AddNode(redis4)
    if err != nil {
        panic(err)
    }

    // 获取节点列表
    nodes := clusterAdapter.GetNodes()
    fmt.Printf("集群节点数量: %d\n", len(nodes))
}
```

### 使用分片缓存

```go
package main

import (
    "context"
    "time"

    "frontapi/pkg/cache"
    "frontapi/pkg/cache/memory"
    "frontapi/pkg/cache/sharding"
)

func main() {
    // 创建多个内存缓存实例作为分片
    memory1, _ := memory.New(&memory.Config{CleanupInterval: time.Minute})
    memory2, _ := memory.New(&memory.Config{CleanupInterval: time.Minute})
    memory3, _ := memory.New(&memory.Config{CleanupInterval: time.Minute})

    // 创建分片配置
    shardConfig := &sharding.Config{
        Shards:           []cache.CacheAdapter{memory1, memory2, memory3},
        ShardingStrategy: "hash",
    }

    // 创建分片适配器
    shardAdapter, err := sharding.New(shardConfig)
    if err != nil {
        panic(err)
    }
    defer shardAdapter.Close()

    ctx := context.Background()

    // 使用分片缓存
    // 数据会根据键的哈希值分布到不同的分片上
    for i := 0; i < 1000; i++ {
        key := fmt.Sprintf("user:%d", i)
        value := []byte(fmt.Sprintf(`{"id":%d,"name":"用户%d"}`, i, i))
        err = shardAdapter.Set(ctx, key, value, time.Hour)
        if err != nil {
            panic(err)
        }
    }

    // 获取分片数量
    shardCount := shardAdapter.GetShardCount()
    fmt.Printf("分片数量: %d\n", shardCount)

    // 添加新分片
    memory4, _ := memory.New(&memory.Config{CleanupInterval: time.Minute})
    err = shardAdapter.AddShard(memory4)
    if err != nil {
        panic(err)
    }
}
```

### 使用高级分片管理

```go
package main

import (
    "context"
    "time"

    "frontapi/pkg/cache/redis"
    "frontapi/pkg/cache/sharding"
)

func main() {
    // 创建分片管理器配置
    shardManagerConfig := &sharding.ShardConfig{
        ShardCount:       4,
        ReplicaFactor:    2,
        ShardingStrategy: "consistent_hash",
        ReadStrategy:     "any",
        WriteStrategy:    "quorum",
        FailoverEnabled:  true,
    }

    // 创建分片管理器
    shardManager, err := sharding.NewShardManager(shardManagerConfig)
    if err != nil {
        panic(err)
    }
    defer shardManager.Close()

    // 创建 Redis 实例
    redis1, _ := redis.New(&redis.Config{Host: "redis1.example.com", Port: 6379})
    redis2, _ := redis.New(&redis.Config{Host: "redis2.example.com", Port: 6379})
    redis3, _ := redis.New(&redis.Config{Host: "redis3.example.com", Port: 6379})
    redis4, _ := redis.New(&redis.Config{Host: "redis4.example.com", Port: 6379})

    // 注册适配器到分片
    shardManager.RegisterAdapter(0, "node1", redis1, true)  // 分片0的主节点
    shardManager.RegisterAdapter(0, "node2", redis2, false) // 分片0的副本节点
    shardManager.RegisterAdapter(1, "node3", redis3, true)  // 分片1的主节点
    shardManager.RegisterAdapter(1, "node4", redis4, false) // 分片1的副本节点

    ctx := context.Background()

    // 使用分片管理器
    shardManager.Set(ctx, "key1", []byte("value1"), time.Minute)
    shardManager.Set(ctx, "key2", []byte("value2"), time.Minute)

    // 获取分片统计信息
    stats := shardManager.GetShardStats()
    for shardID, shardStats := range stats {
        fmt.Printf("分片 %d: %+v\n", shardID, shardStats)
    }

    // 添加新分片
    newShardID, err := shardManager.AddShard()
    if err != nil {
        panic(err)
    }
    fmt.Printf("添加了新分片，ID: %d\n", newShardID)

    // 重新平衡分片
    err = shardManager.Rebalance()
    if err != nil {
        panic(err)
    }
}
```

## 计数器操作

```go
package main

import (
    "context"
    "fmt"
    "time"

    "frontapi/pkg/cache/redis"
)

func main() {
    redisAdapter, err := redis.New(&redis.Config{
        Host: "localhost",
        Port: 6379,
    })
    if err != nil {
        panic(err)
    }
    defer redisAdapter.Close()

    ctx := context.Background()

    // 增加计数器
    count, err := redisAdapter.Increment(ctx, "page_views", 1)
    if err != nil {
        panic(err)
    }
    fmt.Printf("页面浏览次数: %d\n", count)

    // 减少计数器
    remaining, err := redisAdapter.Decrement(ctx, "stock:product123", 1)
    if err != nil {
        panic(err)
    }
    fmt.Printf("剩余库存: %d\n", remaining)
}
```

## 缓存统计

```go
package main

import (
    "fmt"
    "time"

    "frontapi/pkg/cache/memory"
)

func main() {
    memoryAdapter, err := memory.New(&memory.Config{
        CleanupInterval: time.Minute,
    })
    if err != nil {
        panic(err)
    }
    defer memoryAdapter.Close()

    // 获取缓存统计信息
    stats := memoryAdapter.Stats()
    fmt.Printf("缓存统计:\n")
    fmt.Printf("  命中次数: %d\n", stats.Hits)
    fmt.Printf("  未命中次数: %d\n", stats.Misses)
    fmt.Printf("  设置次数: %d\n", stats.Sets)
    fmt.Printf("  删除次数: %d\n", stats.Deletes)
    fmt.Printf("  清空次数: %d\n", stats.Clears)
    fmt.Printf("  键数量: %d\n", stats.Keys)
    fmt.Printf("  大小: %d 字节\n", stats.Size)
    fmt.Printf("  运行时间: %v\n", stats.Uptime)
}
```

## 最佳实践

1. **选择合适的缓存适配器**：
   - 内存缓存适合小型数据集和临时数据
   - Redis 缓存适合需要持久化和分布式的场景
   - 文件缓存适合大型数据和不频繁访问的数据

2. **设置合理的过期时间**：
   - 根据数据的更新频率和重要性设置过期时间
   - 对于频繁变化的数据，设置较短的过期时间
   - 对于相对稳定的数据，设置较长的过期时间

3. **使用批量操作**：
   - 尽量使用 MGet 和 MSet 等批量操作，减少网络往返

4. **处理缓存未命中**：
   - 始终检查缓存错误，特别是 ErrNotFound
   - 实现缓存回填策略，当缓存未命中时从数据源加载数据

5. **监控缓存性能**：
   - 定期检查缓存统计信息，如命中率、未命中率等
   - 根据统计信息调整缓存策略和配置

6. **集群和分片配置**：
   - 对于高可用性要求高的场景，使用集群配置
   - 对于大数据量场景，使用分片配置
   - 根据数据访问模式选择合适的分片策略 