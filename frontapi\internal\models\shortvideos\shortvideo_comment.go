package shortvideos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"
)

// ShortVideoComment 短视频评论模型
type ShortVideoComment struct {
	*models.BaseModelStruct
	Content      string      `json:"content" gorm:"not null;comment:评论内容"`
	Images       null.String `json:"images" gorm:"type:text;comment:图片"`
	Video        null.String `json:"video" gorm:"type:text;comment:视频"`
	UserID       null.String `json:"user_id" gorm:"type:string;size:36;comment:作者ID"`
	UserNickname null.String `json:"user_nickname" gorm:"comment:用户昵称"`
	UserAvatar   null.String `json:"user_avatar" gorm:"comment:用户头像"`
	ReplyToID    null.String `json:"reply_to_id" gorm:"type:string;size:36;comment:回复用户ID"`
	ReplyToUser  string      `json:"reply_to_user" gorm:"comment:回复的用户名字"`
	UserType     int8        `json:"user_type" gorm:"comment:1普通用户，2明星"`
	ParentID     null.String `json:"parent_id" gorm:"type:string;size:36;comment:父评论ID"`
	ShortID      null.String `json:"short_id" gorm:"type:string;size:36;comment:关联短视频ID"`
	Heat         int64       `json:"heat" gorm:"comment:热度"`
	LikeCount    uint64      `json:"like_count" gorm:"default:0;comment:点赞数"`
	ReplyCount   uint64      `json:"reply_count" gorm:"default:0;comment:回复数"`
	Status       int8        `json:"status" gorm:"default:1;comment:状态：0-禁用，1-正常"`
	IsLiked      bool        `json:"is_liked" gorm:"-"`
	IsReply      bool        `json:"is_reply" gorm:"-"`
}

// TableName 指定表名
func (ShortVideoComment) TableName() string {
	return "ly_shorts_comments"
}

// GetID 获取ID
func (s ShortVideoComment) GetID() string {
	if s.BaseModelStruct != nil {
		return s.BaseModelStruct.GetID()
	}
	return ""
}

// GetStatus 获取状态
func (s ShortVideoComment) GetStatus() int8 {
	if s.BaseModelStruct != nil {
		return s.BaseModelStruct.GetStatus()
	}
	return 0
}

// GetCreatedAt 获取创建时间
func (s ShortVideoComment) GetCreatedAt() types.JSONTime {
	if s.BaseModelStruct != nil {
		return s.BaseModelStruct.GetCreatedAt()
	}
	return types.JSONTime{}
}

// GetUpdatedAt 获取更新时间
func (s ShortVideoComment) GetUpdatedAt() types.JSONTime {
	if s.BaseModelStruct != nil {
		return s.BaseModelStruct.GetUpdatedAt()
	}
	return types.JSONTime{}
}
