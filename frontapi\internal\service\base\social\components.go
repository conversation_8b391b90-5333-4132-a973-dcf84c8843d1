package social

import (
	"context"
	"errors"
	"sync"
	"time"

	"frontapi/internal/service/base/extcollect"
	"frontapi/internal/service/base/extfollow"
	"frontapi/internal/service/base/extlike"
)

// ConfigManager 配置管理器
type ConfigManager struct {
	config *Config
	mu     sync.RWMutex
}

// NewConfigManager 创建配置管理器
func NewConfigManager(config *Config) *ConfigManager {
	return &ConfigManager{
		config: config,
	}
}

// GetConfig 获取配置
func (cm *ConfigManager) GetConfig() *Config {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.config
}

// UpdateConfig 更新配置
func (cm *ConfigManager) UpdateConfig(config *Config) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.config = config
}

// GetHealthCheckPeriod 获取健康检查周期
func (cm *ConfigManager) GetHealthCheckPeriod() time.Duration {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.config.HealthCheckPeriod
}

// GetSyncPeriod 获取同步周期
func (cm *ConfigManager) GetSyncPeriod() time.Duration {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	return cm.config.SyncPeriod
}

// MetricsManager 指标管理器
type MetricsManager struct {
	likeService    extlike.ExtendedLikeService
	collectService extcollect.ExtendedCollectService
	followService  extfollow.ExtendedFollowService

	metrics *SocialMetrics
	mu      sync.RWMutex
}

// NewMetricsManager 创建指标管理器
func NewMetricsManager(
	likeService extlike.ExtendedLikeService,
	collectService extcollect.ExtendedCollectService,
	followService extfollow.ExtendedFollowService,
) *MetricsManager {
	return &MetricsManager{
		likeService:    likeService,
		collectService: collectService,
		followService:  followService,
		metrics: &SocialMetrics{
			Timestamp: time.Now(),
		},
	}
}

// GetMetrics 获取指标
func (mm *MetricsManager) GetMetrics(ctx context.Context) (*SocialMetrics, error) {
	mm.mu.RLock()
	defer mm.mu.RUnlock()

	// 复制当前指标
	metrics := *mm.metrics
	return &metrics, nil
}

// CollectMetrics 收集指标
func (mm *MetricsManager) CollectMetrics(ctx context.Context) error {
	mm.mu.Lock()
	defer mm.mu.Unlock()

	// 收集各服务指标
	var err error

	if mm.likeService != nil {
		mm.metrics.LikeMetrics, err = mm.likeService.GetMetrics(ctx)
		if err != nil {
			return err
		}
	}

	if mm.collectService != nil {
		mm.metrics.CollectMetrics, err = mm.collectService.GetMetrics(ctx)
		if err != nil {
			return err
		}
	}

	if mm.followService != nil {
		mm.metrics.FollowMetrics, err = mm.followService.GetMetrics(ctx)
		if err != nil {
			return err
		}
	}

	// 计算总体指标
	mm.calculateOverallMetrics()
	mm.metrics.Timestamp = time.Now()

	return nil
}

// calculateOverallMetrics 计算总体指标
func (mm *MetricsManager) calculateOverallMetrics() {
	var totalRequests, totalErrors int64

	if mm.metrics.LikeMetrics != nil {
		totalRequests += mm.metrics.LikeMetrics.TotalRequests
		totalErrors += mm.metrics.LikeMetrics.ErrorRequests
	}

	if mm.metrics.CollectMetrics != nil {
		totalRequests += mm.metrics.CollectMetrics.TotalRequests
		totalErrors += mm.metrics.CollectMetrics.ErrorRequests
	}

	if mm.metrics.FollowMetrics != nil {
		totalRequests += mm.metrics.FollowMetrics.TotalRequests
		totalErrors += mm.metrics.FollowMetrics.ErrorRequests
	}

	mm.metrics.TotalOperations = totalRequests
	mm.metrics.TotalErrors = totalErrors

	if totalRequests > 0 {
		mm.metrics.ErrorRate = float64(totalErrors) / float64(totalRequests) * 100
	}
}

// RecordLikeEvent 记录点赞事件
func (mm *MetricsManager) RecordLikeEvent(event interface{}) {
	// 实现点赞事件记录逻辑
}

// RecordCollectEvent 记录收藏事件
func (mm *MetricsManager) RecordCollectEvent(event interface{}) {
	// 实现收藏事件记录逻辑
}

// RecordFollowEvent 记录关注事件
func (mm *MetricsManager) RecordFollowEvent(event interface{}) {
	// 实现关注事件记录逻辑
}

// EventBus 事件总线
type EventBus struct {
	subscribers map[string][]func(interface{})
	mu          sync.RWMutex
	isRunning   bool
}

// NewEventBus 创建事件总线
func NewEventBus() *EventBus {
	return &EventBus{
		subscribers: make(map[string][]func(interface{})),
	}
}

// Start 启动事件总线
func (eb *EventBus) Start(ctx context.Context) error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if eb.isRunning {
		return ErrAlreadyRunning
	}

	eb.isRunning = true
	return nil
}

// Stop 停止事件总线
func (eb *EventBus) Stop(ctx context.Context) error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if !eb.isRunning {
		return ErrNotRunning
	}

	eb.isRunning = false
	return nil
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(topic string, handler func(interface{})) error {
	eb.mu.Lock()
	defer eb.mu.Unlock()

	if eb.subscribers[topic] == nil {
		eb.subscribers[topic] = make([]func(interface{}), 0)
	}

	eb.subscribers[topic] = append(eb.subscribers[topic], handler)
	return nil
}

// Publish 发布事件
func (eb *EventBus) Publish(topic string, event interface{}) error {
	eb.mu.RLock()
	defer eb.mu.RUnlock()

	if !eb.isRunning {
		return ErrNotRunning
	}

	handlers := eb.subscribers[topic]
	for _, handler := range handlers {
		go handler(event) // 异步处理
	}

	return nil
}

// SyncManager 同步管理器
type SyncManager struct {
	likeService    extlike.ExtendedLikeService
	collectService extcollect.ExtendedCollectService
	followService  extfollow.ExtendedFollowService

	syncPeriod   time.Duration
	lastSyncTime time.Time
	isRunning    bool
	mu           sync.RWMutex
}

// NewSyncManager 创建同步管理器
func NewSyncManager(
	likeService extlike.ExtendedLikeService,
	collectService extcollect.ExtendedCollectService,
	followService extfollow.ExtendedFollowService,
	syncPeriod time.Duration,
) *SyncManager {
	return &SyncManager{
		likeService:    likeService,
		collectService: collectService,
		followService:  followService,
		syncPeriod:     syncPeriod,
	}
}

// SyncAll 同步所有服务数据
func (sm *SyncManager) SyncAll(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if sm.isRunning {
		return errors.New("sync is already running")
	}

	sm.isRunning = true
	defer func() {
		sm.isRunning = false
		sm.lastSyncTime = time.Now()
	}()

	// 同步点赞服务
	if sm.likeService != nil {
		if err := sm.likeService.SyncData(ctx, "", time.Now().Add(-sm.syncPeriod), time.Now()); err != nil {
			return err
		}
	}

	// 同步收藏服务
	if sm.collectService != nil {
		if err := sm.collectService.SyncData(ctx, "", time.Now().Add(-sm.syncPeriod), time.Now()); err != nil {
			return err
		}
	}

	// 同步关注服务
	if sm.followService != nil {
		if err := sm.followService.SyncData(ctx, time.Now().Add(-sm.syncPeriod), time.Now()); err != nil {
			return err
		}
	}

	return nil
}

// GetSyncStatus 获取同步状态
func (sm *SyncManager) GetSyncStatus(ctx context.Context) (*SyncStatus, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	return &SyncStatus{
		LastSyncTime: sm.lastSyncTime,
		IsRunning:    sm.isRunning,
		NextSyncTime: sm.lastSyncTime.Add(sm.syncPeriod),
		Enabled:      true,
	}, nil
}
