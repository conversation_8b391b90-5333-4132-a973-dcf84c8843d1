/**
 * 英文主题语言包
 */

export default {
    // 主题名称
    light: "Light",
    dark: "Dark",
    blue: "Blue",
    purple: "Purple",
    green: "Green",
    indigo: "Indigo",
    teal: "Teal",
    deeppurple: "Deep Purple",
    orange: "Orange",
    rose: "<PERSON>",
    fuchsia: "<PERSON><PERSON><PERSON>",
    noir: "Noir",

    // PrimeVue 主题家族
    auraFamily: "Aura",
    laraFamily: "Lara",
    mdFamily: "Material",

    // 主题设置
    themeFamily: "Theme Style",
    themeSystemDemo: "Theme System Demo",
    themeSystemDescription: "This page demonstrates the new theme system based on PrimeVue and CSS variables.",
    themeSelector: "Theme Selector",
    themeSelectorDescription: "The theme selector component allows users to choose between different themes and toggle dark mode.",
    themeVariables: "Theme Variables",
    componentPreview: "Component Preview",
    buttons: "Buttons",
    inputs: "Input Fields",
    cards: "Cards",
    cardTitle: "Card Title",
    cardContent: "This is a sample card component showing theme styling.",
    apiUsage: "API Usage",
    default: "Default Theme",

    // 设置界面
    title: "Select Theme",
    selectTheme: 'Select Theme',
    themeSettings: 'Theme Settings',
    changeTheme: 'Change Theme',
    followSystem: 'Follow System',
    darkMode: 'Dark Mode',

    // 其他设置
    settings: "Theme Settings",
    customization: "Customization",
    apply: "Apply Theme",
    reset: "Reset Settings"
}