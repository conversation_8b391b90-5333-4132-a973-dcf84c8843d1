import { ref, onMounted, onUnmounted } from 'vue'

// 性能指标接口
export interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
  
  // 自定义指标
  domContentLoaded?: number
  loadComplete?: number
  memoryUsage?: MemoryInfo
  connectionType?: string
  
  // 页面指标
  pageLoadTime?: number
  resourceLoadTime?: number
  apiResponseTime?: number
}

// 内存信息接口
export interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

// 性能观察器配置
export interface PerformanceConfig {
  enableCoreWebVitals?: boolean
  enableResourceTiming?: boolean
  enableUserTiming?: boolean
  enableMemoryMonitoring?: boolean
  reportInterval?: number
  maxEntries?: number
}

/**
 * 性能监控 Composable
 * 监控 Core Web Vitals 和其他性能指标
 */
export function usePerformance(config: PerformanceConfig = {}) {
  const {
    enableCoreWebVitals = true,
    enableResourceTiming = true,
    enableUserTiming = true,
    enableMemoryMonitoring = true,
    reportInterval = 30000, // 30秒
    maxEntries = 100
  } = config

  const metrics = ref<PerformanceMetrics>({})
  const isSupported = ref(false)
  const observers = ref<PerformanceObserver[]>([])
  const reportTimer = ref<number | null>(null)

  // 检查浏览器支持
  const checkSupport = () => {
    isSupported.value = typeof window !== 'undefined' && 
                       'performance' in window && 
                       'PerformanceObserver' in window
  }

  // 获取 Core Web Vitals
  const measureCoreWebVitals = () => {
    if (!enableCoreWebVitals || !isSupported.value) return

    // First Contentful Paint (FCP)
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        metrics.value.fcp = fcpEntry.startTime
      }
    })
    fcpObserver.observe({ entryTypes: ['paint'] })
    observers.value.push(fcpObserver)

    // Largest Contentful Paint (LCP)
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      if (lastEntry) {
        metrics.value.lcp = lastEntry.startTime
      }
    })
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    observers.value.push(lcpObserver)

    // First Input Delay (FID)
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (entry.processingStart && entry.startTime) {
          metrics.value.fid = entry.processingStart - entry.startTime
        }
      })
    })
    fidObserver.observe({ entryTypes: ['first-input'] })
    observers.value.push(fidObserver)

    // Cumulative Layout Shift (CLS)
    let clsValue = 0
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
          metrics.value.cls = clsValue
        }
      })
    })
    clsObserver.observe({ entryTypes: ['layout-shift'] })
    observers.value.push(clsObserver)
  }

  // 测量资源加载时间
  const measureResourceTiming = () => {
    if (!enableResourceTiming || !isSupported.value) return

    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      let totalResourceTime = 0
      let resourceCount = 0

      entries.forEach((entry: any) => {
        if (entry.duration > 0) {
          totalResourceTime += entry.duration
          resourceCount++
        }
      })

      if (resourceCount > 0) {
        metrics.value.resourceLoadTime = totalResourceTime / resourceCount
      }
    })
    resourceObserver.observe({ entryTypes: ['resource'] })
    observers.value.push(resourceObserver)
  }

  // 测量用户自定义时间
  const measureUserTiming = () => {
    if (!enableUserTiming || !isSupported.value) return

    const userTimingObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry) => {
        // 可以根据需要处理用户自定义的性能标记
        console.log(`User Timing: ${entry.name} - ${entry.duration}ms`)
      })
    })
    userTimingObserver.observe({ entryTypes: ['measure'] })
    observers.value.push(userTimingObserver)
  }

  // 获取内存使用情况
  const getMemoryUsage = (): MemoryInfo | null => {
    if (!enableMemoryMonitoring) return null
    
    const memory = (performance as any).memory
    if (memory) {
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }
    return null
  }

  // 获取连接信息
  const getConnectionInfo = (): string => {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection
    
    if (connection) {
      return connection.effectiveType || connection.type || 'unknown'
    }
    return 'unknown'
  }

  // 测量页面加载时间
  const measurePageLoadTime = () => {
    if (!isSupported.value) return

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      metrics.value.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart
      metrics.value.loadComplete = navigation.loadEventEnd - navigation.navigationStart
      metrics.value.ttfb = navigation.responseStart - navigation.navigationStart
      metrics.value.pageLoadTime = navigation.loadEventEnd - navigation.navigationStart
    }
  }

  // 测量 API 响应时间
  const measureApiResponseTime = (url: string, startTime: number, endTime: number) => {
    const responseTime = endTime - startTime
    
    // 更新平均响应时间
    if (metrics.value.apiResponseTime) {
      metrics.value.apiResponseTime = (metrics.value.apiResponseTime + responseTime) / 2
    } else {
      metrics.value.apiResponseTime = responseTime
    }
  }

  // 创建性能标记
  const mark = (name: string) => {
    if (isSupported.value) {
      performance.mark(name)
    }
  }

  // 测量两个标记之间的时间
  const measure = (name: string, startMark: string, endMark?: string) => {
    if (isSupported.value) {
      performance.measure(name, startMark, endMark)
    }
  }

  // 获取所有性能指标
  const getAllMetrics = (): PerformanceMetrics => {
    const memoryUsage = getMemoryUsage()
    const connectionType = getConnectionInfo()

    return {
      ...metrics.value,
      memoryUsage: memoryUsage || undefined,
      connectionType
    }
  }

  // 报告性能数据
  const reportMetrics = (callback?: (metrics: PerformanceMetrics) => void) => {
    const allMetrics = getAllMetrics()
    
    if (callback) {
      callback(allMetrics)
    } else {
      console.log('Performance Metrics:', allMetrics)
    }
  }

  // 开始性能监控
  const startMonitoring = (reportCallback?: (metrics: PerformanceMetrics) => void) => {
    checkSupport()
    
    if (!isSupported.value) {
      console.warn('Performance monitoring is not supported in this browser')
      return
    }

    measureCoreWebVitals()
    measureResourceTiming()
    measureUserTiming()
    measurePageLoadTime()

    // 定期报告性能数据
    if (reportInterval > 0) {
      reportTimer.value = window.setInterval(() => {
        reportMetrics(reportCallback)
      }, reportInterval)
    }
  }

  // 停止性能监控
  const stopMonitoring = () => {
    // 断开所有观察器
    observers.value.forEach(observer => {
      observer.disconnect()
    })
    observers.value = []

    // 清除定时器
    if (reportTimer.value) {
      clearInterval(reportTimer.value)
      reportTimer.value = null
    }
  }

  // 清除性能数据
  const clearMetrics = () => {
    metrics.value = {}
    
    if (isSupported.value) {
      performance.clearMarks()
      performance.clearMeasures()
    }
  }

  onMounted(() => {
    startMonitoring()
  })

  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    // 响应式数据
    metrics: readonly(metrics),
    isSupported: readonly(isSupported),
    
    // 方法
    startMonitoring,
    stopMonitoring,
    measureApiResponseTime,
    mark,
    measure,
    getAllMetrics,
    reportMetrics,
    clearMetrics,
    getMemoryUsage,
    getConnectionInfo
  }
}

/**
 * 页面性能监控 Composable
 * 简化版本，专注于页面加载性能
 */
export function usePagePerformance() {
  const loadTime = ref<number>(0)
  const domReady = ref<number>(0)
  const firstPaint = ref<number>(0)
  const firstContentfulPaint = ref<number>(0)

  const measurePagePerformance = () => {
    if (typeof window === 'undefined' || !window.performance) return

    // 页面加载时间
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        loadTime.value = navigation.loadEventEnd - navigation.navigationStart
        domReady.value = navigation.domContentLoadedEventEnd - navigation.navigationStart
      }
    })

    // First Paint 和 First Contentful Paint
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.name === 'first-paint') {
            firstPaint.value = entry.startTime
          } else if (entry.name === 'first-contentful-paint') {
            firstContentfulPaint.value = entry.startTime
          }
        })
      })
      observer.observe({ entryTypes: ['paint'] })
    }
  }

  onMounted(() => {
    measurePagePerformance()
  })

  return {
    loadTime: readonly(loadTime),
    domReady: readonly(domReady),
    firstPaint: readonly(firstPaint),
    firstContentfulPaint: readonly(firstContentfulPaint)
  }
}

/**
 * API 性能监控 Composable
 * 监控 API 请求的性能
 */
export function useApiPerformance() {
  const apiMetrics = ref<Map<string, number[]>>(new Map())

  const recordApiCall = (url: string, duration: number) => {
    const current = apiMetrics.value.get(url) || []
    current.push(duration)
    
    // 只保留最近的 50 次记录
    if (current.length > 50) {
      current.shift()
    }
    
    apiMetrics.value.set(url, current)
  }

  const getApiStats = (url: string) => {
    const durations = apiMetrics.value.get(url) || []
    if (durations.length === 0) return null

    const sum = durations.reduce((a, b) => a + b, 0)
    const avg = sum / durations.length
    const min = Math.min(...durations)
    const max = Math.max(...durations)

    return { avg, min, max, count: durations.length }
  }

  const getAllApiStats = () => {
    const stats: Record<string, any> = {}
    apiMetrics.value.forEach((durations, url) => {
      stats[url] = getApiStats(url)
    })
    return stats
  }

  const clearApiMetrics = () => {
    apiMetrics.value.clear()
  }

  return {
    recordApiCall,
    getApiStats,
    getAllApiStats,
    clearApiMetrics,
    apiMetrics: readonly(apiMetrics)
  }
}

// 性能工具函数
export const performanceUtils = {
  // 格式化时间
  formatTime: (ms: number): string => {
    if (ms < 1000) {
      return `${Math.round(ms)}ms`
    }
    return `${(ms / 1000).toFixed(2)}s`
  },

  // 格式化内存大小
  formatMemory: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`
  },

  // 获取性能等级
  getPerformanceGrade: (metrics: PerformanceMetrics): string => {
    const { fcp, lcp, fid, cls } = metrics
    
    let score = 0
    let count = 0

    if (fcp !== undefined) {
      score += fcp < 1800 ? 100 : fcp < 3000 ? 50 : 0
      count++
    }

    if (lcp !== undefined) {
      score += lcp < 2500 ? 100 : lcp < 4000 ? 50 : 0
      count++
    }

    if (fid !== undefined) {
      score += fid < 100 ? 100 : fid < 300 ? 50 : 0
      count++
    }

    if (cls !== undefined) {
      score += cls < 0.1 ? 100 : cls < 0.25 ? 50 : 0
      count++
    }

    if (count === 0) return 'Unknown'

    const average = score / count
    if (average >= 90) return 'Excellent'
    if (average >= 70) return 'Good'
    if (average >= 50) return 'Fair'
    return 'Poor'
  }
}