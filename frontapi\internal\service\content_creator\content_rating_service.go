package content_creator

import (
	"frontapi/internal/models/content_creator"
	repo "frontapi/internal/repository/content_creator"
	"frontapi/internal/service/base"
)

type ContentRatingService interface {
	base.IExtendedService[content_creator.ContentRating]
}

type contentRatingService struct {
	*base.ExtendedService[content_creator.ContentRating]
	repo repo.ContentRatingRepository
}

func NewContentRatingService(repo repo.ContentRatingRepository) ContentRatingService {
	return &contentRatingService{
		ExtendedService: base.NewExtendedService[content_creator.ContentRating](repo, "content_rating"),
		repo:            repo,
	}
}
