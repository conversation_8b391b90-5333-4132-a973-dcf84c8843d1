package types

import (
	"encoding/json"
	"errors"
	"time"
)

// 错误定义
var (
	ErrInvalidFollowerID   = errors.New("无效的关注者ID")
	ErrInvalidFolloweeID   = errors.New("无效的被关注者ID")
	ErrSelfFollow          = errors.New("不能关注自己")
	ErrAlreadyFollowing    = errors.New("已经关注了该用户")
	ErrNotFollowing        = errors.New("没有关注该用户")
	ErrFollowLimitExceeded = errors.New("关注数量超过限制")
)

// FollowOperation 关注操作
type FollowOperation struct {
	FollowerID string    `json:"follower_id"`
	FollowedID string    `json:"followed_id"`
	Action     string    `json:"action"` // "follow" 或 "unfollow"
	Created    time.Time `json:"created"`
	Metadata   string    `json:"metadata,omitempty"`
}

// FollowRecord 关注记录
type FollowRecord struct {
	ID         string    `json:"id" bson:"_id,omitempty"`
	FollowerID string    `json:"follower_id" bson:"follower_id"`
	FollowedID string    `json:"followed_id" bson:"followed_id"`
	Created    time.Time `json:"created" bson:"created"`
	Metadata   string    `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       string `json:"id"`       // 用户ID
	Username string `json:"username"` // 用户名
	Nickname string `json:"nickname"` // 昵称
	Avatar   string `json:"avatar"`   // 头像
	IsVip    bool   `json:"is_vip"`   // 是否VIP
	Level    int    `json:"level"`    // 用户等级
}

// UserFollowStats 用户关注统计
type UserFollowStats struct {
	UserID        string            `json:"user_id"`
	FollowCount   int64             `json:"follow_count"`   // 关注数
	FansCount     int64             `json:"fans_count"`     // 粉丝数
	MutualCount   int64             `json:"mutual_count"`   // 互关数
	InfluenceRank float64           `json:"influence_rank"` // 影响力排名
	LastFollow    time.Time         `json:"last_follow"`
	Metadata      map[string]string `json:"metadata,omitempty"`
}

// FollowTrend 关注趋势
type FollowTrend struct {
	UserID        string    `json:"user_id"`
	TrendScore    float64   `json:"trend_score"`
	RecentFans    int64     `json:"recent_fans"`
	TotalFans     int64     `json:"total_fans"`
	InfluenceRate float64   `json:"influence_rate"`
	LastActivity  time.Time `json:"last_activity"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// SyncStatus 同步状态
type SyncStatus struct {
	LastSync    time.Time         `json:"last_sync"`
	Status      string            `json:"status"`
	RecordCount int64             `json:"record_count"`
	ErrorCount  int64             `json:"error_count"`
	Details     map[string]string `json:"details,omitempty"`
}

// SyncConflict 同步冲突
type SyncConflict struct {
	FollowerID   string            `json:"follower_id"`
	FollowedID   string            `json:"followed_id"`
	ConflictType string            `json:"conflict_type"`
	LocalData    interface{}       `json:"local_data"`
	RemoteData   interface{}       `json:"remote_data"`
	Timestamp    time.Time         `json:"timestamp"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

// ServiceMetrics 服务指标
type ServiceMetrics struct {
	Timestamp           time.Time         `json:"timestamp"`
	TotalRequests       int64             `json:"total_requests"` // 兼容字段
	TotalOperations     int64             `json:"total_operations"`
	SuccessRequests     int64             `json:"success_requests"` // 兼容字段
	SuccessOperations   int64             `json:"success_operations"`
	ErrorRequests       int64             `json:"error_requests"` // 兼容字段
	ErrorOperations     int64             `json:"error_operations"`
	AverageLatency      time.Duration     `json:"average_latency"`
	OperationsPerSecond float64           `json:"operations_per_second"`
	CacheHitRate        float64           `json:"cache_hit_rate"`
	Details             map[string]int64  `json:"details,omitempty"`
	Metadata            map[string]string `json:"metadata,omitempty"`
}

// OperationStats 操作统计
type OperationStats struct {
	Operation   string        `json:"operation"`
	Count       int64         `json:"count"`
	SuccessRate float64       `json:"success_rate"`
	AvgLatency  time.Duration `json:"avg_latency"`
	MinLatency  time.Duration `json:"min_latency"`
	MaxLatency  time.Duration `json:"max_latency"`
	LastExec    time.Time     `json:"last_exec"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Timestamp  time.Time         `json:"timestamp"`
	Operation  string            `json:"operation"`
	Latency    time.Duration     `json:"latency"`
	Throughput int64             `json:"throughput"`
	ErrorRate  float64           `json:"error_rate"`
	CacheHit   bool              `json:"cache_hit"`
	Metadata   map[string]string `json:"metadata,omitempty"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Timestamp    time.Time         `json:"timestamp"`
	Status       string            `json:"status"` // "healthy", "degraded", "unhealthy"
	Components   map[string]string `json:"components"`
	Dependencies map[string]string `json:"dependencies"`
	Uptime       time.Duration     `json:"uptime"`
	Version      string            `json:"version"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

// FollowEvent 关注事件
type FollowEvent struct {
	FollowerID string            `json:"follower_id"`
	FollowedID string            `json:"followed_id"`
	Timestamp  time.Time         `json:"timestamp"`
	IP         string            `json:"ip,omitempty"`
	UserAgent  string            `json:"user_agent,omitempty"`
	Metadata   map[string]string `json:"metadata,omitempty"`
}

// ToJSON 将关注事件转换为JSON
func (e *FollowEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// FromJSON 从JSON解析关注事件
func (e *FollowEvent) FromJSON(data []byte) error {
	return json.Unmarshal(data, e)
}

// UnfollowEvent 取消关注事件
type UnfollowEvent struct {
	FollowerID string            `json:"follower_id"`
	FollowedID string            `json:"followed_id"`
	Timestamp  time.Time         `json:"timestamp"`
	IP         string            `json:"ip,omitempty"`
	UserAgent  string            `json:"user_agent,omitempty"`
	Metadata   map[string]string `json:"metadata,omitempty"`
}

// ToJSON 将取消关注事件转换为JSON
func (e *UnfollowEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// FromJSON 从JSON解析取消关注事件
func (e *UnfollowEvent) FromJSON(data []byte) error {
	return json.Unmarshal(data, e)
}

// BatchEvent 批量操作事件
type BatchEvent struct {
	Operations   []*FollowOperation `json:"operations"`
	Timestamp    time.Time          `json:"timestamp"`
	SuccessCount int64              `json:"success_count"`
	ErrorCount   int64              `json:"error_count"`
	TotalCount   int64              `json:"total_count"`
	Duration     time.Duration      `json:"duration"`
	Errors       []string           `json:"errors,omitempty"`
	Metadata     map[string]string  `json:"metadata,omitempty"`
}

// ToJSON 将批量事件转换为JSON
func (e *BatchEvent) ToJSON() ([]byte, error) {
	return json.Marshal(e)
}

// FromJSON 从JSON解析批量事件
func (e *BatchEvent) FromJSON(data []byte) error {
	return json.Unmarshal(data, e)
}

// CacheKey 缓存键构建器
type CacheKey struct {
	Prefix string
}

// NewCacheKey 创建缓存键构建器
func NewCacheKey(prefix string) *CacheKey {
	if prefix == "" {
		prefix = "follow"
	}
	return &CacheKey{Prefix: prefix}
}

// FollowKey 关注关系键
func (c *CacheKey) FollowKey(followerID, followedID string) string {
	return c.Prefix + ":follow:" + followerID + ":" + followedID
}

// FollowCountKey 关注数键
func (c *CacheKey) FollowCountKey(userID string) string {
	return c.Prefix + ":follow_count:" + userID
}

// FansCountKey 粉丝数键
func (c *CacheKey) FansCountKey(userID string) string {
	return c.Prefix + ":fans_count:" + userID
}

// FollowingListKey 关注列表键
func (c *CacheKey) FollowingListKey(userID string) string {
	return c.Prefix + ":following:" + userID
}

// FansListKey 粉丝列表键
func (c *CacheKey) FansListKey(userID string) string {
	return c.Prefix + ":fans:" + userID
}

// InfluenceRankKey 影响力排名键
func (c *CacheKey) InfluenceRankKey() string {
	return c.Prefix + ":influence_rank"
}

// UserStatsKey 用户统计键
func (c *CacheKey) UserStatsKey(userID string) string {
	return c.Prefix + ":stats:user:" + userID
}

// TrendingKey 趋势排名键
func (c *CacheKey) TrendingKey(timeRange string) string {
	return c.Prefix + ":trending:" + timeRange
}

// RecommendationKey 推荐键
func (c *CacheKey) RecommendationKey(userID string) string {
	return c.Prefix + ":recommend:" + userID
}

// PopularUsersKey 热门用户键
func (c *CacheKey) PopularUsersKey() string {
	return c.Prefix + ":popular"
}

// ActiveUsersKey 活跃用户键
func (c *CacheKey) ActiveUsersKey(timeRange string) string {
	return c.Prefix + ":active:" + timeRange
}

// SyncStatusKey 同步状态键
func (c *CacheKey) SyncStatusKey() string {
	return c.Prefix + ":sync_status"
}

// MetricsKey 指标键
func (c *CacheKey) MetricsKey(operation string) string {
	return c.Prefix + ":metrics:" + operation
}

// HealthKey 健康检查键
func (c *CacheKey) HealthKey(component string) string {
	return c.Prefix + ":health:" + component
}

// EventKey 事件键
func (c *CacheKey) EventKey(eventType, timestamp string) string {
	return c.Prefix + ":event:" + eventType + ":" + timestamp
}

// DailyStatsKey 日统计键
func (c *CacheKey) DailyStatsKey(date string) string {
	return c.Prefix + ":daily:" + date
}

// WeeklyStatsKey 周统计键
func (c *CacheKey) WeeklyStatsKey(week string) string {
	return c.Prefix + ":weekly:" + week
}

// MonthlyStatsKey 月统计键
func (c *CacheKey) MonthlyStatsKey(month string) string {
	return c.Prefix + ":monthly:" + month
}
