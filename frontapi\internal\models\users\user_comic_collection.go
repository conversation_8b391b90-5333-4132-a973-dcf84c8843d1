package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// ComicCollection 用户漫画收藏表
type UserComicCollection struct {
	models.BaseModel
	UserID               string         `gorm:"column:user_id;type:string;not null;index;comment:用户ID" json:"user_id"`                               //用户ID
	ComicID              string         `gorm:"column:comic_id;type:string;not null;index;comment:漫画ID" json:"comic_id"`                             //漫画ID
	ComicTitle           string         `gorm:"column:comic_title;type:string;size:255;comment:漫画标题" json:"comic_title"`                             //漫画标题
	ComicCover           string         `gorm:"column:comic_cover;type:string;size:255;comment:漫画封面" json:"comic_cover"`                             //漫画封面
	Author               string         `gorm:"column:author;type:string;size:100;comment:作者" json:"author"`                                         //作者
	ChapterCount         int            `gorm:"column:chapter_count;type:int;comment:章节数" json:"chapter_count"`                                      //章节数
	LastReadChapterID    string         `gorm:"column:last_read_chapter_id;type:string;comment:最后阅读章节ID" json:"last_read_chapter_id"`                //最后阅读章节ID
	LastReadChapterTitle string         `gorm:"column:last_read_chapter_title;type:string;size:255;comment:最后阅读章节标题" json:"last_read_chapter_title"` //最后阅读章节标题
	LastReadTime         types.JSONTime `gorm:"column:last_read_time;type:time;comment:最后阅读时间" json:"last_read_time"`                                //最后阅读时间
	CategoryID           string         `gorm:"column:category_id;type:string;comment:分类ID" json:"category_id"`                                      //分类ID
	CategoryName         string         `gorm:"column:category_name;type:string;size:50;comment:分类名称" json:"category_name"`                          //分类名称
	Status               string         `gorm:"column:status;type:string;size:20;comment:连载状态" json:"status"`                                        //连载状态
	CollectionTime       types.JSONTime `gorm:"column:collection_time;type:time;default:current_timestamp;comment:收藏时间" json:"collection_time"`      //收藏时间
	CollectionGroup      string         `gorm:"column:collection_group;type:string;size:50;default:'默认收藏夹';comment:收藏分组" json:"collection_group"`    //收藏分组
	Note                 string         `gorm:"column:note;type:string;size:255;comment:收藏备注" json:"note"`                                           //收藏备注
}

// TableName 表名
func (UserComicCollection) TableName() string {
	return "ly_user_comic_collections"
}
