# ExtLike 扩展点赞服务使用指南

## 概述

ExtLike 是一个高性能的点赞服务，支持多种存储策略（Redis、MongoDB、双写模式），用于缓存点赞操作并定时同步到数据库。

## 设计模式

- **策略模式**: 支持不同的存储策略（Redis、MongoDB、双写等）
- **适配器模式**: 统一不同存储引擎的接口
- **工厂模式**: 简化服务实例的创建
- **建造者模式**: 提供灵活的配置方式

## 快速开始

### 1. 创建Redis点赞服务

```go
package main

import (
    "context"
    "log"
    
    "frontapi/internal/service/base/extlike"
)

func main() {
    // 快速创建Redis点赞服务
    service, err := extlike.QuickCreateRedisService()
    if err != nil {
        log.Fatal("创建Redis点赞服务失败:", err)
    }
    defer service.Close()

    ctx := context.Background()
    
    // 用户点赞
    err = service.Like(ctx, "user123", "video456", "video")
    if err != nil {
        log.Printf("点赞失败: %v", err)
        return
    }
    
    // 检查点赞状态
    liked, err := service.IsLiked(ctx, "user123", "video456", "video")
    if err != nil {
        log.Printf("检查点赞状态失败: %v", err)
        return
    }
    
    log.Printf("用户是否点赞: %v", liked)
    
    // 获取点赞数
    count, err := service.GetLikeCount(ctx, "video456", "video")
    if err != nil {
        log.Printf("获取点赞数失败: %v", err)
        return
    }
    
    log.Printf("视频点赞数: %d", count)
}
```

### 2. 使用建造者模式创建服务

```go
// 创建双写模式的点赞服务
service, err := extlike.NewServiceBuilder().
    EnableDualWrite().
    WithSystemRedis().
    WithMongoClient(mongoClient, mongoDB).
    Build()

if err != nil {
    log.Fatal("创建点赞服务失败:", err)
}
defer service.Close()
```

### 3. 批量操作

```go
// 批量点赞
operations := []*types.LikeOperation{
    {
        UserID:   "user1",
        ItemID:   "video1",
        ItemType: "video",
        Action:   "like",
    },
    {
        UserID:   "user2", 
        ItemID:   "video1",
        ItemType: "video",
        Action:   "like",
    },
}

err := service.BatchLike(ctx, operations)
if err != nil {
    log.Printf("批量点赞失败: %v", err)
}

// 批量获取点赞状态
items := map[string]string{
    "video1": "video",
    "video2": "video",
    "post1":  "post",
}

statuses, err := service.BatchGetLikeStatus(ctx, "user1", items)
if err != nil {
    log.Printf("批量获取点赞状态失败: %v", err)
} else {
    for itemID, liked := range statuses {
        log.Printf("物品 %s 点赞状态: %v", itemID, liked)
    }
}
```

## 存储策略

### 1. Redis Only (RedisOnly)

仅使用Redis存储，适合对性能要求极高的场景。

```go
service, err := extlike.CreateService(
    extlike.RedisOnly,
    redisClient, // 可选
    nil,         // MongoDB客户端
    nil,         // MongoDB数据库
)
```

**优点:**
- 极高的读写性能
- 内存存储，延迟极低

**缺点:**
- 数据可能丢失
- 需要额外的持久化机制

### 2. MongoDB Only (MongoOnly)

仅使用MongoDB存储，适合数据持久性要求高的场景。

```go
service, err := extlike.CreateService(
    extlike.MongoOnly,
    nil,           // Redis客户端
    mongoClient,   // MongoDB客户端
    mongoDatabase, // MongoDB数据库
)
```

**优点:**
- 数据持久化保证
- 支持复杂查询

**缺点:**
- 性能相对较低
- 磁盘I/O开销

### 3. Redis First (RedisFirst)

优先使用Redis，Redis故障时自动切换到MongoDB。

```go
service, err := extlike.CreateService(
    extlike.RedisFirst,
    redisClient,   // Redis客户端
    mongoClient,   // MongoDB客户端
    mongoDatabase, // MongoDB数据库
)
```

**适用场景:**
- 高性能要求 + 故障容错
- 数据最终一致性可接受

### 4. Dual Write (DualWrite)

同时写入Redis和MongoDB，保证数据一致性。

```go
service, err := extlike.CreateService(
    extlike.DualWrite,
    redisClient,   // Redis客户端
    mongoClient,   // MongoDB客户端
    mongoDatabase, // MongoDB数据库
)
```

**适用场景:**
- 数据一致性要求高
- 可以接受写入性能损失

## 高级功能

### 1. 热门排行

```go
// 更新热门分数
err := service.UpdateHotRank(ctx, "video123", "video", 95.5)

// 获取热门排行
ranking, err := service.GetHotRanking(ctx, "video", 10)
if err == nil {
    for i, itemID := range ranking {
        log.Printf("排名 %d: %s", i+1, itemID)
    }
}

// 获取带分数的热门排行
rankingWithScores, err := service.GetHotRankingWithScores(ctx, "video", 10)
if err == nil {
    for itemID, score := range rankingWithScores {
        log.Printf("物品 %s 分数: %.2f", itemID, score)
    }
}
```

### 2. 统计信息

```go
// 获取用户点赞统计
userStats, err := service.GetUserLikeStats(ctx, "user123")
if err == nil {
    log.Printf("用户总点赞数: %d", userStats.TotalLikes)
    log.Printf("点赞物品数: %d", userStats.TotalItems)
}

// 获取物品点赞统计
itemStats, err := service.GetItemLikeStats(ctx, "video123", "video")
if err == nil {
    log.Printf("物品总点赞数: %d", itemStats.TotalLikes)
    log.Printf("点赞用户数: %d", itemStats.TotalUsers)
}
```

### 3. 点赞历史

```go
// 获取用户点赞历史
timeRange := &types.TimeRange{
    Start: time.Now().AddDate(0, 0, -7), // 7天前
    End:   time.Now(),                   // 现在
}

history, err := service.GetLikeHistory(ctx, "user123", "video", timeRange)
if err == nil {
    for _, record := range history {
        log.Printf("时间: %v, 物品: %s, 状态: %s", 
            record.Timestamp, record.ItemID, record.Status)
    }
}
```

### 4. 缓存管理

```go
// 预热缓存
itemIDs := []string{"video1", "video2", "video3"}
err := service.WarmupCache(ctx, "video", itemIDs)

// 失效缓存
keys := []string{"like:video:video1", "count:video:video1"}
err := service.InvalidateCache(ctx, keys...)

// 获取缓存统计
stats, err := service.GetCacheStats(ctx)
if err == nil {
    log.Printf("缓存命中率: %.2f%%", stats["hit_rate"].(float64)*100)
}
```

## 监控和指标

### 1. 服务指标

```go
// 获取服务指标
metrics, err := service.GetMetrics(ctx)
if err == nil {
    log.Printf("总请求数: %d", metrics.TotalRequests)
    log.Printf("成功请求数: %d", metrics.SuccessRequests)
    log.Printf("平均响应时间: %v", metrics.AvgResponseTime)
    log.Printf("每秒请求数: %.2f", metrics.RequestsPerSecond)
}

// 获取错误统计
errorStats, err := service.GetErrorStats(ctx)
if err == nil {
    log.Printf("错误率: %.2f%%", errorStats["error_rate"].(float64)*100)
}
```

### 2. 健康检查

```go
// 健康检查
err := service.HealthCheck(ctx)
if err != nil {
    log.Printf("服务不健康: %v", err)
} else {
    log.Println("服务健康")
}
```

## 集成示例

### 在视频服务中集成

```go
package videos

import (
    "context"
    "frontapi/internal/service/base/extlike"
    "frontapi/internal/service/base/extlike/types"
)

type VideoService struct {
    likeService extlike.ExtendedLikeService
    // 其他依赖...
}

func NewVideoService() *VideoService {
    // 创建点赞服务
    likeService, err := extlike.QuickCreateRedisService()
    if err != nil {
        panic("创建点赞服务失败: " + err.Error())
    }
    
    return &VideoService{
        likeService: likeService,
    }
}

func (s *VideoService) LikeVideo(ctx context.Context, userID, videoID string) error {
    // 点赞视频
    err := s.likeService.Like(ctx, userID, videoID, "video")
    if err != nil {
        return err
    }
    
    // 更新热门排行（可选）
    go func() {
        // 计算热门分数（可以基于点赞数、观看数等）
        count, _ := s.likeService.GetLikeCount(context.Background(), videoID, "video")
        score := float64(count) // 简化的分数计算
        
        s.likeService.UpdateHotRank(context.Background(), videoID, "video", score)
    }()
    
    return nil
}

func (s *VideoService) GetVideoLikeInfo(ctx context.Context, userID, videoID string) (*VideoLikeInfo, error) {
    // 批量获取点赞信息
    items := map[string]string{videoID: "video"}
    
    // 获取用户点赞状态
    statuses, err := s.likeService.BatchGetLikeStatus(ctx, userID, items)
    if err != nil {
        return nil, err
    }
    
    // 获取点赞数
    counts, err := s.likeService.BatchGetLikeCounts(ctx, items)
    if err != nil {
        return nil, err
    }
    
    return &VideoLikeInfo{
        VideoID:   videoID,
        IsLiked:   statuses[videoID],
        LikeCount: counts[videoID],
    }, nil
}

type VideoLikeInfo struct {
    VideoID   string `json:"video_id"`
    IsLiked   bool   `json:"is_liked"`
    LikeCount int64  `json:"like_count"`
}
```

## 最佳实践

### 1. 错误处理

```go
// 使用上下文超时
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

err := service.Like(ctx, userID, itemID, itemType)
if err != nil {
    switch {
    case errors.Is(err, context.DeadlineExceeded):
        log.Printf("操作超时: %v", err)
    case errors.Is(err, types.ErrAlreadyExists):
        log.Printf("已经点赞过: %v", err)
    default:
        log.Printf("点赞失败: %v", err)
    }
}
```

### 2. 批量操作优化

```go
// 批量处理而不是单个操作
const batchSize = 100

func processLikes(service extlike.ExtendedLikeService, operations []*types.LikeOperation) error {
    for i := 0; i < len(operations); i += batchSize {
        end := i + batchSize
        if end > len(operations) {
            end = len(operations)
        }
        
        batch := operations[i:end]
        if err := service.BatchLike(context.Background(), batch); err != nil {
            return err
        }
    }
    return nil
}
```

### 3. 连接池管理

```go
// 配置合适的连接池参数
config := extlike.DefaultConfig()
config.Redis.Config.PoolSize = 100
config.Redis.Config.MinIdleConns = 10
config.Redis.Config.MaxConnAge = time.Hour

service, err := extlike.NewServiceBuilder().
    WithConfig(config).
    EnableRedisOnly().
    WithSystemRedis().
    Build()
```

## 注意事项

1. **内存使用**: Redis模式下注意设置合适的TTL，避免内存溢出
2. **数据一致性**: 双写模式可能存在短暂的数据不一致
3. **故障恢复**: 配置适当的重试策略和超时时间
4. **监控**: 定期检查服务健康状态和性能指标
5. **备份**: MongoDB数据需要定期备份

## 性能调优

### Redis 优化

```go
// 设置合适的TTL
config.Redis.Config.LikeTTL = time.Hour * 24      // 点赞关系24小时
config.Redis.Config.CountTTL = time.Hour * 12     // 计数12小时  
config.Redis.Config.RankingTTL = time.Hour * 6    // 排行6小时

// 配置连接池
config.Redis.Config.PoolSize = 200
config.Redis.Config.MinIdleConns = 20
```

### 批量操作

```go
// 使用批量接口而不是循环调用单个接口
items := map[string]string{
    "video1": "video",
    "video2": "video", 
    "post1":  "post",
}

// 好的做法
statuses, err := service.BatchGetLikeStatus(ctx, userID, items)

// 避免的做法
// for itemID, itemType := range items {
//     status, err := service.IsLiked(ctx, userID, itemID, itemType)
//     // ...
// }
```

这个点赞服务提供了完整的点赞功能，支持多种存储策略，可以很好地集成到视频、短视频、帖子等业务模块中。 