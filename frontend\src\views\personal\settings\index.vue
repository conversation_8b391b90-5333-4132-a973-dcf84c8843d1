<template>
    <div class="p-6">
        <h1 class="text-2xl font-bold mb-6">{{ $t('personal.settings.title') }}</h1>

        <div class="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">{{ $t('personal.settings.accountSettings') }}</h2>

            <div class="mb-4">
                <label class="block text-sm font-medium mb-1" for="language">{{ $t('personal.settings.language')
                }}</label>
                <select id="language"
                    class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800">
                    <option value="zh-CN">简体中文</option>
                    <option value="zh-TW">繁體中文</option>
                    <option value="en">English</option>
                </select>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium mb-1" for="theme">{{ $t('personal.settings.theme') }}</label>
                <select id="theme"
                    class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800">
                    <option value="light">{{ $t('personal.settings.lightTheme') }}</option>
                    <option value="dark">{{ $t('personal.settings.darkTheme') }}</option>
                    <option value="system">{{ $t('personal.settings.systemTheme') }}</option>
                </select>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium mb-2">{{ $t('personal.settings.notifications') }}</label>
                <div class="flex items-center">
                    <input type="checkbox" id="email-notifications" class="mr-2" />
                    <label for="email-notifications">{{ $t('personal.settings.emailNotifications') }}</label>
                </div>
                <div class="flex items-center mt-2">
                    <input type="checkbox" id="push-notifications" class="mr-2" />
                    <label for="push-notifications">{{ $t('personal.settings.pushNotifications') }}</label>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button
                    class="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50">
                    {{ $t('personal.settings.saveChanges') }}
                </button>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4 text-red-600">{{ $t('personal.settings.dangerZone') }}</h2>

            <div class="mb-4">
                <button
                    class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
                    {{ $t('personal.settings.deleteAccount') }}
                </button>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    {{ $t('personal.settings.deleteAccountWarning') }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// 可以在这里添加设置相关的逻辑
</script>

<style scoped lang="scss">
// 设置页面的特定样式</style>