/**
 * 点击外部指令
 * 用于检测点击元素外部的事件，常用于关闭弹窗、下拉菜单等
 */

import type { Directive, DirectiveBinding } from 'vue'

interface ClickOutsideOptions {
  handler: (event: Event) => void
  exclude?: string[] | HTMLElement[] // 排除的元素选择器或元素
  include?: string[] | HTMLElement[] // 包含的元素选择器或元素
  capture?: boolean // 是否在捕获阶段处理
  once?: boolean    // 是否只触发一次
  disabled?: boolean // 是否禁用
}

interface ClickOutsideElement extends HTMLElement {
  _clickOutside?: {
    handler: (event: Event) => void
    options: ClickOutsideOptions
    listener: (event: Event) => void
    touchStartListener: (event: TouchEvent) => void
    touchEndListener: (event: TouchEvent) => void
    isTouchDevice: boolean
    touchStartTarget: EventTarget | null
  }
}

// 检查是否为触摸设备
function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// 检查元素是否在排除列表中
function isExcluded(target: EventTarget | null, exclude: string[] | HTMLElement[] = []): boolean {
  if (!target || !(target instanceof Element)) {
    return false
  }

  return exclude.some(item => {
    if (typeof item === 'string') {
      // 选择器字符串
      return target.closest(item) !== null
    } else if (item instanceof HTMLElement) {
      // HTML 元素
      return item.contains(target as Node) || item === target
    }
    return false
  })
}

// 检查元素是否在包含列表中
function isIncluded(target: EventTarget | null, include: string[] | HTMLElement[] = []): boolean {
  if (!target || !(target instanceof Element)) {
    return false
  }

  if (include.length === 0) {
    return true // 如果没有指定包含列表，默认包含所有
  }

  return include.some(item => {
    if (typeof item === 'string') {
      // 选择器字符串
      return target.closest(item) !== null
    } else if (item instanceof HTMLElement) {
      // HTML 元素
      return item.contains(target as Node) || item === target
    }
    return false
  })
}

// 创建事件监听器
function createListener(el: ClickOutsideElement): (event: Event) => void {
  return (event: Event) => {
    if (!el._clickOutside) return

    const { handler, options } = el._clickOutside
    const target = event.target

    // 检查是否禁用
    if (options.disabled) {
      return
    }

    // 检查点击是否在元素内部
    if (el.contains(target as Node) || el === target) {
      return
    }

    // 检查是否在排除列表中
    if (isExcluded(target, options.exclude)) {
      return
    }

    // 检查是否在包含列表中
    if (!isIncluded(target, options.include)) {
      return
    }

    // 触发处理函数
    handler(event)

    // 如果只触发一次，移除监听器
    if (options.once) {
      removeListeners(el)
    }
  }
}

// 创建触摸开始监听器
function createTouchStartListener(el: ClickOutsideElement): (event: TouchEvent) => void {
  return (event: TouchEvent) => {
    if (el._clickOutside) {
      el._clickOutside.touchStartTarget = event.target
    }
  }
}

// 创建触摸结束监听器
function createTouchEndListener(el: ClickOutsideElement): (event: TouchEvent) => void {
  return (event: TouchEvent) => {
    if (!el._clickOutside) return

    const { handler, options, touchStartTarget } = el._clickOutside

    // 检查是否禁用
    if (options.disabled) {
      return
    }

    // 检查触摸开始和结束是否在同一个目标上（避免滑动误触）
    if (touchStartTarget !== event.target) {
      return
    }

    const target = event.target

    // 检查触摸是否在元素内部
    if (el.contains(target as Node) || el === target) {
      return
    }

    // 检查是否在排除列表中
    if (isExcluded(target, options.exclude)) {
      return
    }

    // 检查是否在包含列表中
    if (!isIncluded(target, options.include)) {
      return
    }

    // 触发处理函数
    handler(event)

    // 如果只触发一次，移除监听器
    if (options.once) {
      removeListeners(el)
    }
  }
}

// 添加事件监听器
function addListeners(el: ClickOutsideElement): void {
  if (!el._clickOutside) return

  const { options, listener, touchStartListener, touchEndListener, isTouchDevice: isTouch } = el._clickOutside

  if (isTouch) {
    // 触摸设备使用 touchstart 和 touchend
    document.addEventListener('touchstart', touchStartListener, options.capture)
    document.addEventListener('touchend', touchEndListener, options.capture)
  } else {
    // 非触摸设备使用 click
    document.addEventListener('click', listener, options.capture)
  }

  // 同时监听 mousedown 以处理某些特殊情况
  document.addEventListener('mousedown', listener, options.capture)
}

// 移除事件监听器
function removeListeners(el: ClickOutsideElement): void {
  if (!el._clickOutside) return

  const { options, listener, touchStartListener, touchEndListener, isTouchDevice: isTouch } = el._clickOutside

  if (isTouch) {
    document.removeEventListener('touchstart', touchStartListener, options.capture)
    document.removeEventListener('touchend', touchEndListener, options.capture)
  } else {
    document.removeEventListener('click', listener, options.capture)
  }

  document.removeEventListener('mousedown', listener, options.capture)
}

// 点击外部指令
export const vClickOutside: Directive<ClickOutsideElement, ClickOutsideOptions | ((event: Event) => void)> = {
  mounted(el: ClickOutsideElement, binding: DirectiveBinding<ClickOutsideOptions | ((event: Event) => void)>) {
    // 解析配置
    let options: ClickOutsideOptions
    if (typeof binding.value === 'function') {
      options = {
        handler: binding.value,
        exclude: [],
        include: [],
        capture: false,
        once: false,
        disabled: false
      }
    } else {
      options = {
        exclude: [],
        include: [],
        capture: false,
        once: false,
        disabled: false,
        ...binding.value
      }
    }

    // 从修饰符中获取配置
    if (binding.modifiers.capture) {
      options.capture = true
    }
    if (binding.modifiers.once) {
      options.once = true
    }
    if (binding.modifiers.disabled) {
      options.disabled = true
    }

    const isTouch = isTouchDevice()
    const listener = createListener(el)
    const touchStartListener = createTouchStartListener(el)
    const touchEndListener = createTouchEndListener(el)

    // 保存配置到元素
    el._clickOutside = {
      handler: options.handler,
      options,
      listener,
      touchStartListener,
      touchEndListener,
      isTouchDevice: isTouch,
      touchStartTarget: null
    }

    // 添加事件监听器
    addListeners(el)
  },

  updated(el: ClickOutsideElement, binding: DirectiveBinding<ClickOutsideOptions | ((event: Event) => void)>) {
    if (!el._clickOutside) return

    // 移除旧的监听器
    removeListeners(el)

    // 更新配置
    let options: ClickOutsideOptions
    if (typeof binding.value === 'function') {
      options = {
        ...el._clickOutside.options,
        handler: binding.value
      }
    } else {
      options = {
        ...el._clickOutside.options,
        ...binding.value
      }
    }

    // 从修饰符中获取配置
    if (binding.modifiers.capture) {
      options.capture = true
    }
    if (binding.modifiers.once) {
      options.once = true
    }
    if (binding.modifiers.disabled) {
      options.disabled = true
    }

    // 更新配置
    el._clickOutside.handler = options.handler
    el._clickOutside.options = options

    // 重新添加监听器
    addListeners(el)
  },

  unmounted(el: ClickOutsideElement) {
    if (el._clickOutside) {
      removeListeners(el)
      delete el._clickOutside
    }
  }
}

// 工具函数
export const clickOutsideUtils = {
  // 手动触发点击外部检查
  trigger(el: ClickOutsideElement, event: Event): void {
    if (el._clickOutside && !el._clickOutside.options.disabled) {
      el._clickOutside.listener(event)
    }
  },

  // 启用点击外部检查
  enable(el: ClickOutsideElement): void {
    if (el._clickOutside) {
      el._clickOutside.options.disabled = false
    }
  },

  // 禁用点击外部检查
  disable(el: ClickOutsideElement): void {
    if (el._clickOutside) {
      el._clickOutside.options.disabled = true
    }
  },

  // 检查是否已禁用
  isDisabled(el: ClickOutsideElement): boolean {
    return el._clickOutside?.options.disabled || false
  },

  // 更新排除列表
  updateExclude(el: ClickOutsideElement, exclude: string[] | HTMLElement[]): void {
    if (el._clickOutside) {
      el._clickOutside.options.exclude = exclude
    }
  },

  // 更新包含列表
  updateInclude(el: ClickOutsideElement, include: string[] | HTMLElement[]): void {
    if (el._clickOutside) {
      el._clickOutside.options.include = include
    }
  }
}

// 导出类型
export type { ClickOutsideOptions }