<template>
  <div 
    class="svg-icon" 
    :class="[`svg-icon-${name}`, className]"
    :style="iconStyle"
    v-html="svgContent"
  />
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'

interface Props {
  name: string
  size?: string | number
  color?: string
  gradient?: {
    from: string
    to: string
    direction?: string
  }
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: '1em',
  color: 'currentColor',
  className: ''
})

const svgContent = ref('')

// 计算样式
const iconStyle = computed(() => {
  const style: Record<string, string> = {
    width: typeof props.size === 'number' ? `${props.size}px` : props.size,
    height: typeof props.size === 'number' ? `${props.size}px` : props.size,
    display: 'inline-block',
    verticalAlign: 'middle'
  }

  if (props.gradient) {
    // 如果有渐变色，创建渐变ID
    const gradientId = `gradient-${props.name}-${Date.now()}`
    style.fill = `url(#${gradientId})`
  } else if (props.color) {
    style.fill = props.color
    style.color = props.color
  }

  return style
})

// 预定义的SVG图标内容
const iconMap: Record<string, string> = {
  play: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"/></svg>',
  pause: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/></svg>',
  volume: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/></svg>',
  mute: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/></svg>',
  fullscreen: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/></svg>',
  loading: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"><animateTransform attributeName="transform" type="rotate" from="0 12 12" to="360 12 12" dur="1s" repeatCount="indefinite"/></path></svg>'
}

// 动态导入SVG内容
const loadSvgContent = async () => {
  try {
    // 首先尝试使用预定义的图标
    if (iconMap[props.name]) {
      let content = iconMap[props.name]
      
      // 如果有渐变色，添加渐变定义
      if (props.gradient) {
        const gradientId = `gradient-${props.name}-${Date.now()}`
        
        const gradientDef = `
          <defs>
            <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:${props.gradient.from};stop-opacity:1" />
              <stop offset="100%" style="stop-color:${props.gradient.to};stop-opacity:1" />
            </linearGradient>
          </defs>
        `
        
        // 在SVG标签后插入渐变定义并更新fill
        content = content.replace('<svg', `<svg${gradientDef}<svg`.slice(4))
        content = content.replace('fill="currentColor"', `fill="url(#${gradientId})"`)
      } else if (props.color && props.color !== 'currentColor') {
        // 替换颜色
        content = content.replace('fill="currentColor"', `fill="${props.color}"`)
      }
      
      svgContent.value = content
      return
    }

    // 如果预定义图标不存在，尝试动态导入
    const svgModule = await import(`../../assets/icons/${props.name}.svg?raw`)
    let content = svgModule.default

    // 处理渐变色和颜色
    if (props.gradient) {
      const gradientId = `gradient-${props.name}-${Date.now()}`
      
      const gradientDef = `
        <defs>
          <linearGradient id="${gradientId}" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:${props.gradient.from};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${props.gradient.to};stop-opacity:1" />
          </linearGradient>
        </defs>
      `
      
      content = content.replace('<svg', `<svg${gradientDef}<svg`.slice(4))
      content = content.replace(/fill="[^"]*"/g, `fill="url(#${gradientId})"`)
    } else if (props.color && props.color !== 'currentColor') {
      content = content.replace(/fill="[^"]*"/g, `fill="${props.color}"`)
    }

    svgContent.value = content
  } catch (error) {
    console.warn(`Failed to load SVG icon: ${props.name}`, error)
    svgContent.value = `<svg viewBox="0 0 24 24" fill="currentColor"><text x="12" y="12" text-anchor="middle" font-size="8">${props.name}</text></svg>`
  }
}

onMounted(() => {
  loadSvgContent()
})
</script>

<style scoped>
.svg-icon {
  display: inline-block;
  vertical-align: middle;
  fill: currentColor;
}

.svg-icon :deep(svg) {
  width: 100%;
  height: 100%;
  display: block;
}
</style> 