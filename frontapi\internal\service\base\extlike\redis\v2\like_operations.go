package v2

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"

	"frontapi/internal/service/base/extlike/types"
)

// LikeOperations 点赞操作处理器
type LikeOperations struct {
	client *RedisClient
	stats  *AdapterStats
}

// NewLikeOperations 创建点赞操作处理器
func NewLikeOperations(client *RedisClient, stats *AdapterStats) *LikeOperations {
	return &LikeOperations{
		client: client,
		stats:  stats,
	}
}

// Like 点赞操作
// 使用 Redis 事务确保数据一致性
func (l *LikeOperations) Like(ctx context.Context, userID, itemID, itemType string) error {
	pipe := l.client.Pipeline()

	// 检查是否已经点赞
	likeKey := l.client.likeKey(itemType, itemID)
	userLikesKey := l.client.userLikesKey(userID, itemType)
	itemLikersKey := l.client.itemLikersKey(itemType, itemID)
	countKey := l.client.countKey(itemType, itemID)
	statsKey := l.client.statsKey(itemType, itemID)
	historyKey := l.client.historyKey(userID, itemType)

	// 使用 Set 存储点赞关系
	pipe.SAdd(ctx, likeKey, userID)
	pipe.Expire(ctx, likeKey, l.client.config.LikeTTL)

	// 用户点赞列表（使用Set）
	pipe.SAdd(ctx, userLikesKey, itemID)
	pipe.Expire(ctx, userLikesKey, l.client.config.LikeTTL)

	// 物品点赞用户列表（使用Set）
	pipe.SAdd(ctx, itemLikersKey, userID)
	pipe.Expire(ctx, itemLikersKey, l.client.config.LikeTTL)

	// 点赞计数（使用String）
	pipe.Incr(ctx, countKey)
	pipe.Expire(ctx, countKey, l.client.config.CountTTL)

	// 统计信息（使用Hash）
	now := time.Now()
	pipe.HSet(ctx, statsKey, map[string]interface{}{
		"last_like_time": now.Unix(),
	})
	pipe.HIncrBy(ctx, statsKey, "total_likes", 1)
	pipe.Expire(ctx, statsKey, l.client.config.DefaultTTL)

	// 历史记录（使用Stream）
	likeRecord := map[string]interface{}{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
		"action":    "like",
		"timestamp": now.Unix(),
	}
	pipe.XAdd(ctx, &redis.XAddArgs{
		Stream: historyKey,
		Values: likeRecord,
		MaxLen: 1000, // 保留最近1000条记录
		Approx: true,
	})
	pipe.Expire(ctx, historyKey, l.client.config.LikeTTL)

	_, err := pipe.Exec(ctx)
	if err != nil {
		l.stats.ErrorCount++
		return fmt.Errorf("点赞操作失败: %w", err)
	}

	l.stats.HitCount++
	l.stats.updateHitRate()
	return nil
}

// Unlike 取消点赞操作
func (l *LikeOperations) Unlike(ctx context.Context, userID, itemID, itemType string) error {
	pipe := l.client.Pipeline()

	likeKey := l.client.likeKey(itemType, itemID)
	userLikesKey := l.client.userLikesKey(userID, itemType)
	itemLikersKey := l.client.itemLikersKey(itemType, itemID)
	countKey := l.client.countKey(itemType, itemID)
	statsKey := l.client.statsKey(itemType, itemID)
	historyKey := l.client.historyKey(userID, itemType)

	// 移除点赞关系
	pipe.SRem(ctx, likeKey, userID)
	pipe.SRem(ctx, userLikesKey, itemID)
	pipe.SRem(ctx, itemLikersKey, userID)

	// 减少计数
	pipe.Decr(ctx, countKey)

	// 更新统计信息
	now := time.Now()
	pipe.HSet(ctx, statsKey, "last_unlike_time", now.Unix())
	pipe.HIncrBy(ctx, statsKey, "total_unlikes", 1)

	// 记录历史
	unlikeRecord := map[string]interface{}{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
		"action":    "unlike",
		"timestamp": now.Unix(),
	}
	pipe.XAdd(ctx, &redis.XAddArgs{
		Stream: historyKey,
		Values: unlikeRecord,
		MaxLen: 1000,
		Approx: true,
	})

	_, err := pipe.Exec(ctx)
	if err != nil {
		l.stats.ErrorCount++
		return fmt.Errorf("取消点赞操作失败: %w", err)
	}

	l.stats.HitCount++
	l.stats.updateHitRate()
	return nil
}

// IsLiked 检查是否已点赞
func (l *LikeOperations) IsLiked(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	likeKey := l.client.likeKey(itemType, itemID)

	result, err := l.client.client.SIsMember(ctx, likeKey, userID).Result()
	if err != nil {
		if err == redis.Nil {
			l.stats.MissCount++
			l.stats.updateHitRate()
			return false, nil
		}
		l.stats.ErrorCount++
		return false, fmt.Errorf("检查点赞状态失败: %w", err)
	}

	if result {
		l.stats.HitCount++
	} else {
		l.stats.MissCount++
	}
	l.stats.updateHitRate()
	return result, nil
}

// GetLikeCount 获取点赞数量
func (l *LikeOperations) GetLikeCount(ctx context.Context, itemID, itemType string) (int64, error) {
	countKey := l.client.countKey(itemType, itemID)

	result, err := l.client.client.Get(ctx, countKey).Int64()
	if err != nil {
		if err == redis.Nil {
			l.stats.MissCount++
			l.stats.updateHitRate()
			return 0, nil
		}
		l.stats.ErrorCount++
		return 0, fmt.Errorf("获取点赞数量失败: %w", err)
	}

	l.stats.HitCount++
	l.stats.updateHitRate()
	return result, nil
}

// BatchLike 批量点赞操作
func (l *LikeOperations) BatchLike(ctx context.Context, operations []*types.LikeOperation) error {
	if len(operations) == 0 {
		return nil
	}

	pipe := l.client.Pipeline()

	for _, op := range operations {
		if op.Action != "like" {
			continue
		}

		// 为每个操作添加相同的Redis命令
		likeKey := l.client.likeKey(op.ItemType, op.ItemID)
		userLikesKey := l.client.userLikesKey(op.UserID, op.ItemType)
		itemLikersKey := l.client.itemLikersKey(op.ItemType, op.ItemID)
		countKey := l.client.countKey(op.ItemType, op.ItemID)

		pipe.SAdd(ctx, likeKey, op.UserID)
		pipe.Expire(ctx, likeKey, l.client.config.LikeTTL)
		pipe.SAdd(ctx, userLikesKey, op.ItemID)
		pipe.Expire(ctx, userLikesKey, l.client.config.LikeTTL)
		pipe.SAdd(ctx, itemLikersKey, op.UserID)
		pipe.Expire(ctx, itemLikersKey, l.client.config.LikeTTL)
		pipe.Incr(ctx, countKey)
		pipe.Expire(ctx, countKey, l.client.config.CountTTL)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		l.stats.ErrorCount++
		return fmt.Errorf("批量点赞操作失败: %w", err)
	}

	l.stats.HitCount++
	l.stats.updateHitRate()
	return nil
}

// BatchUnlike 批量取消点赞操作
func (l *LikeOperations) BatchUnlike(ctx context.Context, operations []*types.LikeOperation) error {
	if len(operations) == 0 {
		return nil
	}

	pipe := l.client.Pipeline()

	for _, op := range operations {
		if op.Action != "unlike" {
			continue
		}

		likeKey := l.client.likeKey(op.ItemType, op.ItemID)
		userLikesKey := l.client.userLikesKey(op.UserID, op.ItemType)
		itemLikersKey := l.client.itemLikersKey(op.ItemType, op.ItemID)
		countKey := l.client.countKey(op.ItemType, op.ItemID)

		pipe.SRem(ctx, likeKey, op.UserID)
		pipe.SRem(ctx, userLikesKey, op.ItemID)
		pipe.SRem(ctx, itemLikersKey, op.UserID)
		pipe.Decr(ctx, countKey)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		l.stats.ErrorCount++
		return fmt.Errorf("批量取消点赞操作失败: %w", err)
	}

	l.stats.HitCount++
	l.stats.updateHitRate()
	return nil
}
