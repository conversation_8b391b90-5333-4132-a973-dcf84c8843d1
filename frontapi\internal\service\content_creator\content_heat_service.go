package content_creator

import (
	"frontapi/internal/models/content_creator"
	repo "frontapi/internal/repository/content_creator"
	"frontapi/internal/service/base"
)

type ContentHeatService interface {
	base.IExtendedService[content_creator.ContentHeat]
}

type contentHeatService struct {
	*base.ExtendedService[content_creator.ContentHeat]
	repo repo.ContentHeatRepository
}

func NewContentHeatService(repo repo.ContentHeatRepository) ContentHeatService {
	return &contentHeatService{
		ExtendedService: base.NewExtendedService[content_creator.ContentHeat](repo, "content_heat"),
		repo:            repo,
	}
}
