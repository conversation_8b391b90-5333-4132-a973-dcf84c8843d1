# 缓存系统安装指南

本文档介绍如何在 frontapi 项目中安装和配置缓存系统。

## 系统要求

- Go 1.18+ (支持泛型)
- Redis 6.0+ (推荐，但不强制)
- 足够的磁盘空间用于文件缓存（如果启用）

## 安装步骤

### 1. 添加依赖

确保项目的 `go.mod` 文件中包含以下依赖：

```go
require (
    github.com/go-redis/redis/v8 v8.11.5
    // ... 其他依赖
)
```

如果没有，请运行以下命令添加：

```bash
go get github.com/go-redis/redis/v8
```

### 2. 配置环境变量

在项目的环境变量或 `.env` 文件中设置以下配置：

```
# 缓存基本配置
CACHE_DEFAULT_TTL=3600  # 默认缓存过期时间（秒）

# Redis缓存配置
CACHE_REDIS_HOST=localhost
CACHE_REDIS_PORT=6379
CACHE_REDIS_PASSWORD=
CACHE_REDIS_DB=1  # 使用专用的Redis数据库，避免与其他数据混合

# 文件缓存配置（可选）
CACHE_FILE_PATH=storage/cache
```

### 3. 在 main.go 中初始化缓存

在项目的 `main.go` 文件中添加缓存初始化代码：

```go
import (
    // ... 其他导入
    "frontapi/pkg/cache/v2"
    "frontapi/pkg/utils"
)

// initCache 初始化缓存系统
func initCache() v2.CacheManager {
    // 获取缓存配置
    defaultTTL := utils.GetEnvAsInt("CACHE_DEFAULT_TTL", 3600)

    // 创建缓存配置
    config := &v2.CacheConfig{
        DefaultTTL: time.Duration(defaultTTL) * time.Second,
        Redis: &v2.RedisConfig{
            Host:     utils.GetEnv("CACHE_REDIS_HOST", "localhost"),
            Port:     utils.GetEnvAsInt("CACHE_REDIS_PORT", 6379),
            Password: utils.GetEnv("CACHE_REDIS_PASSWORD", ""),
            DB:       utils.GetEnvAsInt("CACHE_REDIS_DB", 1),
        },
    }

    // 创建缓存管理器
    cacheManager, err := v2.New(config)
    if err != nil {
        log.Fatalf("缓存系统初始化失败: %v", err)
    }

    return cacheManager
}

func main() {
    // ... 其他初始化代码
    
    // 初始化缓存
    cacheManager := initCache()
    defer cacheManager.Close()
    
    // 将缓存管理器添加到服务容器
    services := bootstrap.InitServices(db)
    services.CacheManager = cacheManager
    
    // ... 其他代码
}
```

### 4. 创建缓存目录（如果使用文件缓存）

如果使用文件缓存，确保缓存目录存在：

```bash
mkdir -p storage/cache
chmod 755 storage/cache
```

## 验证安装

可以通过运行以下示例代码验证缓存系统是否正常工作：

```go
package main

import (
    "fmt"
    "frontapi/pkg/cache/v2"
    "log"
    "time"
)

func main() {
    // 创建Redis缓存实例
    redisCache, err := v2.NewRedis("localhost", 6379, "", 1)
    if err != nil {
        log.Fatalf("创建Redis缓存失败: %v", err)
    }
    defer redisCache.Close()

    // 设置缓存
    err = redisCache.Set("test_key", "test_value", 1*time.Minute)
    if err != nil {
        log.Fatalf("设置缓存失败: %v", err)
    }
    fmt.Println("缓存设置成功")

    // 获取缓存
    value, err := redisCache.Get("test_key")
    if err != nil {
        log.Fatalf("获取缓存失败: %v", err)
    }
    fmt.Printf("获取缓存成功: %v\n", value)
}
```

## 故障排除

### 无法连接到Redis

如果出现 "Redis连接失败" 错误，请检查：

1. Redis服务是否正在运行
2. 环境变量中的主机名和端口是否正确
3. 如果设置了密码，密码是否正确
4. 防火墙是否允许连接到Redis端口

### 文件缓存权限问题

如果使用文件缓存时出现权限错误，请检查：

1. 应用程序是否有权限读写缓存目录
2. 缓存目录的所有者和权限设置是否正确

### 缓存命中率低

如果发现缓存命中率低，可以考虑：

1. 增加默认TTL时间
2. 检查键的命名是否一致
3. 使用混合缓存模式（Redis + 文件）提高可靠性

## 进一步配置

### 启用混合缓存

如果要同时使用Redis和文件缓存，可以修改配置：

```go
config := &v2.CacheConfig{
    DefaultTTL: time.Duration(defaultTTL) * time.Second,
    Redis: &v2.RedisConfig{
        Host:     utils.GetEnv("CACHE_REDIS_HOST", "localhost"),
        Port:     utils.GetEnvAsInt("CACHE_REDIS_PORT", 6379),
        Password: utils.GetEnv("CACHE_REDIS_PASSWORD", ""),
        DB:       utils.GetEnvAsInt("CACHE_REDIS_DB", 1),
    },
    File: &v2.FileConfig{
        Path: utils.GetEnv("CACHE_FILE_PATH", "storage/cache"),
    },
}
```

### 使用类型安全的缓存

对于需要类型安全的场景，可以使用泛型适配器：

```go
// 创建字符串类型的缓存适配器
stringCache, err := v2.GetTyped[string](cacheManager)
if err != nil {
    log.Fatalf("创建类型安全缓存失败: %v", err)
}

// 类型安全的设置和获取
err = stringCache.Set("typed_key", "typed_value", 1*time.Minute)
if err != nil {
    log.Fatalf("设置类型安全缓存失败: %v", err)
}

stringValue, err := stringCache.Get("typed_key")
if err != nil {
    log.Fatalf("获取类型安全缓存失败: %v", err)
}
fmt.Printf("获取类型安全缓存成功: %s (类型: %T)\n", stringValue, stringValue)
``` 