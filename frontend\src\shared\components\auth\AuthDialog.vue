<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="400px"
    :show-close="true"
    :close-on-click-modal="false"
    class="auth-dialog"
    :append-to-body="true"
    :destroy-on-close="true"
    transition="dialog-fade"
    @close="handleClose"
  >
    <component
      :is="currentComponent"
      @login-success="handleLoginSuccess"
      @switch-component="switchComponent"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, shallowRef, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  visible: boolean
  initialComponent?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'login-success'): void
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const LoginForm = defineAsyncComponent(() => import('@/shared/components/auth/LoginForm.vue'))
const RegisterForm = defineAsyncComponent(() => import('@/shared/components/auth/RegisterForm.vue'))
const ForgetForm = defineAsyncComponent(() => import('@/shared/components/auth/ForgetForm.vue'))

const currentComponentName = ref(props.initialComponent || 'login')
const currentComponent = shallowRef(LoginForm)

const dialogTitle = computed(() => {
  switch (currentComponentName.value) {
    case 'register':
      return '用户注册'
    case 'forget':
      return '忘记密码'
    default:
      return '登录'
  }
})

const switchComponent = (component: string) => {
  currentComponentName.value = component
  switch (component) {
    case 'register':
      currentComponent.value = RegisterForm
      break
    case 'forget':
      currentComponent.value = ForgetForm
      break
    default:
      currentComponent.value = LoginForm
  }
}

const handleClose = () => {
  currentComponentName.value = 'login'
  currentComponent.value = LoginForm
}

const handleLoginSuccess = () => {
  emit('login-success')
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.auth-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .el-dialog__header {
      margin: 0;
      padding: 20px 20px 10px;
      text-align: center;

      .el-dialog__title {
        font-size: 24px;
        font-weight: 600;
        color: #333;
      }
    }

    .el-dialog__body {
      padding: 20px 30px 30px;
    }
  }
}

:deep([data-theme='dark']) {
  .auth-dialog {
    :deep(.el-dialog) {
      background: rgba(26, 26, 26, 0.95);

      .el-dialog__title {
        color: #fff;
      }
    }
  }
}
</style>