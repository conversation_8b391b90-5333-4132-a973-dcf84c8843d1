package users

import (
	"frontapi/internal/models/users"
	userRepo "frontapi/internal/repository/users"
	videoRepo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

// CreateWatchHistoryRequest 创建观看历史请求
type CreateWatchHistoryRequest struct {
	UserID       string `json:"userId" validate:"required"`
	VideoID      string `json:"videoId" validate:"required"`
	WatchTime    int    `json:"watchTime"`
	LastPosition int    `json:"lastPosition"`
	IsFinished   uint8  `json:"isFinished"`
}

// UpdateWatchPositionRequest 更新观看位置请求
type UpdateWatchPositionRequest struct {
	WatchTime    int   `json:"watchTime"`
	LastPosition int   `json:"lastPosition"`
	IsFinished   uint8 `json:"isFinished"`
}

// UserWatchVideoHistoryService 用户视频观看历史服务接口
type UserWatchVideoHistoryService interface {
	base.IExtendedService[users.UserWatchVideoHistory]
}

// userWatchVideoHistoryService 用户视频观看历史服务实现
type userWatchVideoHistoryService struct {
	*base.ExtendedService[users.UserWatchVideoHistory]
	watchHistoryRepo userRepo.UserWatchVideoHistoryRepository
	videoRepo        videoRepo.VideoRepository
}

// NewUserWatchVideoHistoryService 创建用户视频观看历史服务实例
func NewUserWatchVideoHistoryService(
	watchHistoryRepo userRepo.UserWatchVideoHistoryRepository,
	videoRepo videoRepo.VideoRepository,
) UserWatchVideoHistoryService {
	return &userWatchVideoHistoryService{
		ExtendedService:  base.NewExtendedService[users.UserWatchVideoHistory](watchHistoryRepo, "user_watch_video_history"),
		watchHistoryRepo: watchHistoryRepo,
		videoRepo:        videoRepo,
	}
}
