package permission

import (
	"frontapi/internal/admin"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

// UserController 用户控制器（简化版，使用模拟数据）
type UserController struct {
	admin.BaseController
}

func NewAdminUserController() *UserController {
	return &UserController{}
}

// UserInfo 用户信息结构体
type UserInfo struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	Status   int8   `json:"status"`
	DeptID   int    `json:"deptId"`
}

// CreateUser 创建用户
func (u *UserController) CreateUser(c *fiber.Ctx) error {
	// 解析请求数据
	var req struct {
		Username string `json:"username" validate:"required"`
		Password string `json:"password" validate:"required,min=6"`
		Nickname string `json:"nickname"`
		Avatar   string `json:"avatar"`
		Email    string `json:"email" validate:"omitempty,email"`
		Phone    string `json:"phone"`
		Status   int8   `json:"status"`
		DeptID   int    `json:"deptId"`
		RoleIDs  []int  `json:"roleIds"`
	}

	err := validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return u.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 模拟检查用户名是否已存在
	if req.Username == "admin" || req.Username == "test" {
		return utils.BadRequest(c, "用户名已存在", nil)
	}

	// 模拟创建用户
	user := UserInfo{
		ID:       100, // 模拟生成的ID
		Username: req.Username,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Email:    req.Email,
		Phone:    req.Phone,
		Status:   req.Status,
		DeptID:   req.DeptID,
	}

	return utils.Success(c, fiber.Map{
		"id": user.ID,
	}, "创建用户成功")
}

// UpdateUser 更新用户
func (u *UserController) UpdateUser(c *fiber.Ctx) error {
	// 获取用户ID
	id, err := u.GetId(c)
	if err != nil {
		return utils.BadRequest(c, "无效的用户ID", nil)
	}
	_, err2 := strconv.Atoi(id)
	if err2 != nil {
		return utils.BadRequest(c, "无效的用户ID", nil)
	}

	// 解析请求数据
	var req struct {
		Username string `json:"username"`
		Password string `json:"password" validate:"omitempty,min=6"`
		RealName string `json:"realName"`
		Avatar   string `json:"avatar"`
		Email    string `json:"email" validate:"omitempty,email"`
		Phone    string `json:"phone"`
		Status   int8   `json:"status"`
		DeptID   int    `json:"deptId"`
	}

	err = validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return u.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 模拟更新成功
	return utils.Success(c, nil, "更新用户成功")
}

// DeleteUser 删除用户
func (u *UserController) DeleteUser(c *fiber.Ctx) error {
	// 获取用户ID
	id := c.Params("id")
	if id == "" {
		return utils.BadRequest(c, "无效的用户ID", nil)
	}

	// 模拟删除成功
	return utils.Success(c, nil, "删除用户成功")
}

// GetUser 获取用户详情
func (u *UserController) GetUser(c *fiber.Ctx) error {
	id, err := u.GetId(c)
	if id == "" || err != nil {
		return u.BadRequest(c, "无效的用户ID", nil)
	}

	// 模拟用户数据
	user := UserInfo{
		ID:       1,
		Username: "admin",
		Nickname: "管理员",
		Avatar:   "/avatar/admin.png",
		Email:    "<EMAIL>",
		Phone:    "13800138000",
		Status:   1,
		DeptID:   1,
	}

	// 模拟角色ID列表
	roleIDs := []int{1, 2}

	return utils.Success(c, fiber.Map{
		"user":    user,
		"roleIds": roleIDs,
	}, "获取用户成功")
}

// ListUsers 获取用户列表
func (u *UserController) ListUsers(c *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Username string `json:"username"`
		Nickname string `json:"nickname"`
		Email    string `json:"email"`
		Phone    string `json:"phone"`
		Status   int    `json:"status"`
		DeptID   int    `json:"deptId"`
		Page     int    `json:"page" default:"1"`
		Size     int    `json:"size" default:"10"`
	}

	if err := c.BodyParser(&req); err != nil {
		req.Page = 1
		req.Size = 10
	}

	// 模拟用户列表数据
	users := []UserInfo{
		{ID: 1, Username: "admin", Nickname: "管理员", Email: "<EMAIL>", Phone: "13800138000", Status: 1, DeptID: 1},
		{ID: 2, Username: "editor", Nickname: "编辑", Email: "<EMAIL>", Phone: "13800138001", Status: 1, DeptID: 2},
		{ID: 3, Username: "viewer", Nickname: "查看者", Email: "<EMAIL>", Phone: "13800138002", Status: 1, DeptID: 2},
	}

	return utils.Success(c, fiber.Map{
		"list":  users,
		"total": len(users),
		"page":  req.Page,
		"size":  req.Size,
	}, "获取用户列表成功")
}

// UpdateUserRoles 更新用户角色
func (u *UserController) UpdateUserRoles(c *fiber.Ctx) error {
	// 获取用户ID
	_, err := u.GetId(c)
	if err != nil {
		return u.BadRequest(c, "无效的用户ID", nil)
	}

	// 解析请求参数
	var req struct {
		RoleIDs []int `json:"roleIds"`
	}

	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 模拟更新成功
	return utils.Success(c, nil, "更新用户角色成功")
}

// UpdatePassword 更新密码
func (u *UserController) UpdatePassword(c *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		OldPassword string `json:"oldPassword" validate:"required"`
		NewPassword string `json:"newPassword" validate:"required,min=6"`
	}

	err := validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return u.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 模拟密码验证（简单示例）
	if req.OldPassword != "123456" {
		return utils.BadRequest(c, "原密码错误", nil)
	}

	// 模拟更新成功
	return utils.Success(c, nil, "密码更新成功")
}

// ResetPassword 重置密码
func (u *UserController) ResetPassword(c *fiber.Ctx) error {
	// 获取用户ID
	id, err := u.GetId(c)
	if err != nil {
		return u.BadRequest(c, "无效的用户ID", nil)
	}

	// 解析请求参数
	var req struct {
		NewPassword string `json:"newPassword" validate:"required,min=6"`
	}

	err = validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return u.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 模拟重置成功
	return utils.Success(c, fiber.Map{
		"userId":      id,
		"newPassword": req.NewPassword,
	}, "重置密码成功")
}

// GetUserCodes 获取用户权限码
func (u *UserController) GetUserCodes(c *fiber.Ctx) error {
	// 模拟用户权限码
	codes := []string{
		"user:view",
		"user:create",
		"user:edit",
		"user:delete",
		"role:view",
		"role:create",
		"role:edit",
		"role:delete",
	}

	return utils.Success(c, codes, "获取用户权限码成功")
}

// GetAsyncRoutes 获取异步路由
func (u *UserController) GetAsyncRoutes(c *fiber.Ctx) error {
	// 模拟路由数据
	routes := []fiber.Map{
		{
			"path":      "/dashboard",
			"name":      "Dashboard",
			"component": "Dashboard",
			"meta": fiber.Map{
				"title": "仪表盘",
				"icon":  "DashboardOutlined",
			},
		},
		{
			"path":      "/system",
			"name":      "System",
			"component": "Layout",
			"meta": fiber.Map{
				"title": "系统管理",
				"icon":  "SettingOutlined",
			},
			"children": []fiber.Map{
				{
					"path":      "user",
					"name":      "User",
					"component": "system/user/index",
					"meta": fiber.Map{
						"title": "用户管理",
						"icon":  "UserOutlined",
					},
				},
			},
		},
	}

	return utils.Success(c, routes, "获取异步路由成功")
}
