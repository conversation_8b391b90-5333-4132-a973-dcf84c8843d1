/**
 * UI相关组合式函数
 */

import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import type {
  UseThemeReturn,
  UseFullscreenReturn,
  UseClipboardReturn,
  UseDragReturn,
  UseTimerReturn,
  UseCountdownReturn,
  UseUploadReturn
} from './types'

/**
 * 主题组合式函数
 */
export function useTheme(): UseThemeReturn {
  const theme = ref<'light' | 'dark' | 'auto'>('auto')
  const isDark = ref(false)
  const primaryColor = ref('#1890ff')

  // 检测系统主题
  const detectSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return 'light'
  }

  // 更新主题
  const updateTheme = () => {
    const systemTheme = detectSystemTheme()
    const actualTheme = theme.value === 'auto' ? systemTheme : theme.value
    isDark.value = actualTheme === 'dark'
    
    // 更新HTML类名
    if (typeof document !== 'undefined') {
      document.documentElement.classList.toggle('dark', isDark.value)
      document.documentElement.setAttribute('data-theme', actualTheme)
    }
  }

  // 设置主题
  const setTheme = (newTheme: 'light' | 'dark' | 'auto') => {
    theme.value = newTheme
    updateTheme()
  }

  // 切换主题
  const toggleTheme = () => {
    if (theme.value === 'light') {
      setTheme('dark')
    } else if (theme.value === 'dark') {
      setTheme('light')
    } else {
      // auto模式下切换到相反的主题
      const systemTheme = detectSystemTheme()
      setTheme(systemTheme === 'dark' ? 'light' : 'dark')
    }
  }

  // 设置主色调
  const setPrimaryColor = (color: string) => {
    primaryColor.value = color
    if (typeof document !== 'undefined') {
      document.documentElement.style.setProperty('--primary-color', color)
    }
  }

  // 监听系统主题变化
  onMounted(() => {
    updateTheme()
    
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => {
        if (theme.value === 'auto') {
          updateTheme()
        }
      }
      
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', handleChange)
      } else {
        mediaQuery.addListener(handleChange)
      }
      
      onUnmounted(() => {
        if (mediaQuery.removeEventListener) {
          mediaQuery.removeEventListener('change', handleChange)
        } else {
          mediaQuery.removeListener(handleChange)
        }
      })
    }
  })

  // 监听主题变化
  watch(theme, updateTheme)

  return {
    theme,
    isDark,
    primaryColor,
    setTheme,
    toggleTheme,
    setPrimaryColor
  }
}

/**
 * 全屏组合式函数
 */
export function useFullscreen(target?: Ref<HTMLElement | null>): UseFullscreenReturn {
  const isFullscreen = ref(false)
  const isSupported = ref(false)

  // 检查是否支持全屏
  const checkSupport = () => {
    if (typeof document !== 'undefined') {
      isSupported.value = !!(document.fullscreenEnabled ||
        (document as any).webkitFullscreenEnabled ||
        (document as any).mozFullScreenEnabled ||
        (document as any).msFullscreenEnabled)
    }
  }

  // 更新全屏状态
  const updateFullscreenState = () => {
    if (typeof document !== 'undefined') {
      isFullscreen.value = !!(document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement)
    }
  }

  // 进入全屏
  const enter = async (): Promise<void> => {
    if (!isSupported.value) {
      throw new Error('Fullscreen is not supported')
    }

    const element = target?.value || document.documentElement

    try {
      if (element.requestFullscreen) {
        await element.requestFullscreen()
      } else if ((element as any).webkitRequestFullscreen) {
        await (element as any).webkitRequestFullscreen()
      } else if ((element as any).mozRequestFullScreen) {
        await (element as any).mozRequestFullScreen()
      } else if ((element as any).msRequestFullscreen) {
        await (element as any).msRequestFullscreen()
      }
    } catch (error) {
      throw new Error(`Failed to enter fullscreen: ${error}`)
    }
  }

  // 退出全屏
  const exit = async (): Promise<void> => {
    if (!isSupported.value) {
      throw new Error('Fullscreen is not supported')
    }

    try {
      if (document.exitFullscreen) {
        await document.exitFullscreen()
      } else if ((document as any).webkitExitFullscreen) {
        await (document as any).webkitExitFullscreen()
      } else if ((document as any).mozCancelFullScreen) {
        await (document as any).mozCancelFullScreen()
      } else if ((document as any).msExitFullscreen) {
        await (document as any).msExitFullscreen()
      }
    } catch (error) {
      throw new Error(`Failed to exit fullscreen: ${error}`)
    }
  }

  // 切换全屏
  const toggle = async (): Promise<void> => {
    if (isFullscreen.value) {
      await exit()
    } else {
      await enter()
    }
  }

  onMounted(() => {
    checkSupport()
    updateFullscreenState()

    // 监听全屏状态变化
    const events = [
      'fullscreenchange',
      'webkitfullscreenchange',
      'mozfullscreenchange',
      'msfullscreenchange'
    ]

    events.forEach(event => {
      document.addEventListener(event, updateFullscreenState)
    })

    onUnmounted(() => {
      events.forEach(event => {
        document.removeEventListener(event, updateFullscreenState)
      })
    })
  })

  return {
    isFullscreen,
    isSupported,
    enter,
    exit,
    toggle
  }
}

/**
 * 剪贴板组合式函数
 */
export function useClipboard(): UseClipboardReturn {
  const text = ref('')
  const isSupported = ref(false)
  const copied = ref(false)

  // 检查是否支持剪贴板API
  const checkSupport = () => {
    isSupported.value = typeof navigator !== 'undefined' && 'clipboard' in navigator
  }

  // 复制文本
  const copy = async (value: string): Promise<void> => {
    if (isSupported.value && navigator.clipboard) {
      try {
        await navigator.clipboard.writeText(value)
        text.value = value
        copied.value = true
        
        // 2秒后重置copied状态
        setTimeout(() => {
          copied.value = false
        }, 2000)
      } catch (error) {
        throw new Error(`Failed to copy text: ${error}`)
      }
    } else {
      // 降级方案：使用execCommand
      try {
        const textArea = document.createElement('textarea')
        textArea.value = value
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        
        if (successful) {
          text.value = value
          copied.value = true
          setTimeout(() => {
            copied.value = false
          }, 2000)
        } else {
          throw new Error('Copy command failed')
        }
      } catch (error) {
        throw new Error(`Failed to copy text: ${error}`)
      }
    }
  }

  // 读取剪贴板
  const read = async (): Promise<string> => {
    if (isSupported.value && navigator.clipboard) {
      try {
        const value = await navigator.clipboard.readText()
        text.value = value
        return value
      } catch (error) {
        throw new Error(`Failed to read clipboard: ${error}`)
      }
    } else {
      throw new Error('Clipboard read is not supported')
    }
  }

  onMounted(() => {
    checkSupport()
  })

  return {
    text,
    isSupported,
    copied,
    copy,
    read
  }
}

/**
 * 拖拽组合式函数
 */
export function useDrag(target?: Ref<HTMLElement | null>): UseDragReturn {
  const isDragging = ref(false)
  const position = reactive({ x: 0, y: 0 })
  const startPosition = reactive({ x: 0, y: 0 })
  const delta = reactive({ x: 0, y: 0 })

  let initialPosition = { x: 0, y: 0 }

  const onMouseDown = (event: MouseEvent) => {
    isDragging.value = true
    initialPosition = { x: event.clientX, y: event.clientY }
    startPosition.x = position.x
    startPosition.y = position.y
    
    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
    event.preventDefault()
  }

  const onMouseMove = (event: MouseEvent) => {
    if (!isDragging.value) return
    
    delta.x = event.clientX - initialPosition.x
    delta.y = event.clientY - initialPosition.y
    position.x = startPosition.x + delta.x
    position.y = startPosition.y + delta.y
  }

  const onMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  const onTouchStart = (event: TouchEvent) => {
    if (event.touches.length !== 1) return
    
    const touch = event.touches[0]
    isDragging.value = true
    initialPosition = { x: touch.clientX, y: touch.clientY }
    startPosition.x = position.x
    startPosition.y = position.y
    
    document.addEventListener('touchmove', onTouchMove, { passive: false })
    document.addEventListener('touchend', onTouchEnd)
  }

  const onTouchMove = (event: TouchEvent) => {
    if (!isDragging.value || event.touches.length !== 1) return
    
    const touch = event.touches[0]
    delta.x = touch.clientX - initialPosition.x
    delta.y = touch.clientY - initialPosition.y
    position.x = startPosition.x + delta.x
    position.y = startPosition.y + delta.y
    
    event.preventDefault()
  }

  const onTouchEnd = () => {
    isDragging.value = false
    document.removeEventListener('touchmove', onTouchMove)
    document.removeEventListener('touchend', onTouchEnd)
  }

  const reset = () => {
    position.x = 0
    position.y = 0
    delta.x = 0
    delta.y = 0
  }

  onMounted(() => {
    const element = target?.value
    if (element) {
      element.addEventListener('mousedown', onMouseDown)
      element.addEventListener('touchstart', onTouchStart, { passive: true })
    }
  })

  onUnmounted(() => {
    const element = target?.value
    if (element) {
      element.removeEventListener('mousedown', onMouseDown)
      element.removeEventListener('touchstart', onTouchStart)
    }
    
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
    document.removeEventListener('touchmove', onTouchMove)
    document.removeEventListener('touchend', onTouchEnd)
  })

  return {
    isDragging,
    position,
    delta,
    reset
  }
}

/**
 * 定时器组合式函数
 */
export function useTimer(options: TimerOptions = {}): UseTimerReturn {
  const {
    interval = 1000,
    immediate = false,
    callback
  } = options

  const isActive = ref(false)
  const counter = ref(0)
  let timerId: NodeJS.Timeout | null = null

  const start = () => {
    if (isActive.value) return
    
    isActive.value = true
    timerId = setInterval(() => {
      counter.value++
      callback?.(counter.value)
    }, interval)
  }

  const pause = () => {
    isActive.value = false
    if (timerId) {
      clearInterval(timerId)
      timerId = null
    }
  }

  const reset = () => {
    pause()
    counter.value = 0
  }

  const restart = () => {
    reset()
    start()
  }

  onMounted(() => {
    if (immediate) {
      start()
    }
  })

  onUnmounted(() => {
    pause()
  })

  return {
    isActive,
    counter,
    start,
    pause,
    reset,
    restart
  }
}

/**
 * 倒计时组合式函数
 */
export function useCountdown(options: CountdownOptions): UseCountdownReturn {
  const {
    time,
    interval = 1000,
    onFinish,
    onUpdate
  } = options

  const timeLeft = ref(time)
  const isActive = ref(false)
  const isFinished = ref(false)
  let timerId: NodeJS.Timeout | null = null

  const start = () => {
    if (isActive.value || isFinished.value) return
    
    isActive.value = true
    timerId = setInterval(() => {
      timeLeft.value -= interval
      onUpdate?.(timeLeft.value)
      
      if (timeLeft.value <= 0) {
        timeLeft.value = 0
        isActive.value = false
        isFinished.value = true
        
        if (timerId) {
          clearInterval(timerId)
          timerId = null
        }
        
        onFinish?.()
      }
    }, interval)
  }

  const pause = () => {
    isActive.value = false
    if (timerId) {
      clearInterval(timerId)
      timerId = null
    }
  }

  const reset = () => {
    pause()
    timeLeft.value = time
    isFinished.value = false
  }

  const restart = () => {
    reset()
    start()
  }

  onUnmounted(() => {
    pause()
  })

  return {
    timeLeft,
    isActive,
    isFinished,
    start,
    pause,
    reset,
    restart
  }
}

/**
 * 文件上传组合式函数
 */
export function useUpload(): UseUploadReturn {
  const files = ref<File[]>([])
  const uploading = ref(false)
  const progress = ref(0)
  const error = ref<string | null>(null)

  const selectFiles = (accept?: string, multiple = false): Promise<File[]> => {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = accept || '*'
      input.multiple = multiple
      
      input.onchange = (event) => {
        const target = event.target as HTMLInputElement
        const selectedFiles = Array.from(target.files || [])
        files.value = selectedFiles
        resolve(selectedFiles)
      }
      
      input.onerror = () => {
        reject(new Error('File selection failed'))
      }
      
      input.click()
    })
  }

  const upload = async (
    url: string,
    uploadFiles?: File[],
    options: {
      headers?: Record<string, string>
      data?: Record<string, any>
      onProgress?: (progress: number) => void
    } = {}
  ): Promise<any> => {
    const filesToUpload = uploadFiles || files.value
    if (filesToUpload.length === 0) {
      throw new Error('No files to upload')
    }

    uploading.value = true
    progress.value = 0
    error.value = null

    try {
      const formData = new FormData()
      
      filesToUpload.forEach((file, index) => {
        formData.append(`file${index}`, file)
      })
      
      if (options.data) {
        Object.entries(options.data).forEach(([key, value]) => {
          formData.append(key, value)
        })
      }

      const xhr = new XMLHttpRequest()
      
      return new Promise((resolve, reject) => {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progressValue = Math.round((event.loaded / event.total) * 100)
            progress.value = progressValue
            options.onProgress?.(progressValue)
          }
        }
        
        xhr.onload = () => {
          uploading.value = false
          
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText)
              resolve(response)
            } catch {
              resolve(xhr.responseText)
            }
          } else {
            const errorMsg = `Upload failed: ${xhr.status} ${xhr.statusText}`
            error.value = errorMsg
            reject(new Error(errorMsg))
          }
        }
        
        xhr.onerror = () => {
          uploading.value = false
          const errorMsg = 'Upload failed: Network error'
          error.value = errorMsg
          reject(new Error(errorMsg))
        }
        
        xhr.open('POST', url)
        
        if (options.headers) {
          Object.entries(options.headers).forEach(([key, value]) => {
            xhr.setRequestHeader(key, value)
          })
        }
        
        xhr.send(formData)
      })
    } catch (err) {
      uploading.value = false
      const errorMsg = err instanceof Error ? err.message : 'Upload failed'
      error.value = errorMsg
      throw err
    }
  }

  const reset = () => {
    files.value = []
    uploading.value = false
    progress.value = 0
    error.value = null
  }

  const removeFile = (index: number) => {
    files.value.splice(index, 1)
  }

  return {
    files,
    uploading,
    progress,
    error,
    selectFiles,
    upload,
    reset,
    removeFile
  }
}