package comics

// // ComicCreateRequest 漫画创建请求验证模型
// type ComicCreateRequest struct {
// 	Title       string   `json:"title" validate:"required,min=2,max=100"`
// 	Description string   `json:"description" validate:"required,min=10,max=2000"`
// 	Cover       string   `json:"cover" validate:"required,url"`
// 	Author      string   `json:"author" validate:"required"`
// 	CategoryID  uint     `json:"categoryId" validate:"required,min=1"`
// 	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
// 	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
// 	IsVIP       bool     `json:"isVip"`
// 	IsAdult     bool     `json:"isAdult"`
// }

// // ComicUpdateRequest 漫画更新请求验证模型
// type ComicUpdateRequest struct {
// 	Title       string   `json:"title" validate:"omitempty,min=2,max=100"`
// 	Description string   `json:"description" validate:"omitempty,min=10,max=2000"`
// 	Cover       string   `json:"cover" validate:"omitempty,url"`
// 	Author      string   `json:"author" validate:"omitempty"`
// 	CategoryID  uint     `json:"categoryId" validate:"omitempty,min=1"`
// 	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
// 	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
// 	IsVIP       bool     `json:"isVip"`
// 	IsAdult     bool     `json:"isAdult"`
// }

// CreateComicRequest 创建漫画请求
type CreateComicRequest struct {
	Title         string   `json:"title" validate:"required"`
	Cover         string   `json:"cover" validate:"required"`
	Description   string   `json:"description"`
	Author        string   `json:"author"`
	CategoryID    string   `json:"category_id"`
	Progress      string   `json:"progress"`
	Tags          []string `json:"tags"`
	IsAdult       int8     `json:"is_adult" validate:"bool"`
	IsPaid        int8     `json:"is_paid" validate:"bool"`
	IsFeatured    int8     `json:"is_featured" validate:"bool"`
	Price         float64  `json:"price"`
	CreatorID     string   `json:"creator_id"`
	CreatorName   string   `json:"creator_name"`
	CreatorAvatar string   `json:"creator_avatar"`
	PublishDate   string   `json:"publish_date"`
	Status        int8     `json:"status"`
}

// UpdateComicRequest 更新漫画请求
type UpdateComicRequest struct {
	Title         string   `json:"title"`
	Cover         string   `json:"cover"`
	Description   string   `json:"description"`
	Author        string   `json:"author"`
	CategoryID    string   `json:"category_id"`
	Progress      string   `json:"progress"`
	Tags          []string `json:"tags"`
	IsAdult       int8     `json:"is_adult"`
	IsPaid        int8     `json:"is_paid"`
	IsFeatured    int8     `json:"is_featured"`
	Price         *float64 `json:"price"`
	CreatorName   string   `json:"creator_name"`
	CreatorAvatar string   `json:"creator_avatar"`
	Status        int8     `json:"status"`
}
