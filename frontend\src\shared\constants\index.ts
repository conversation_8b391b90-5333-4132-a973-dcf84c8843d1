// 导出所有常量模块
export * from './api'
export * from './app'
export * from './auth'
export * from './video'
export * from './user'
export * from './upload'
export * from './comment'
export * from './search'
export * from './live'
export * from './notification'
export * from './storage'
export * from './route'
export * from './theme'
export * from './language'
export * from './device'
export * from './error'
export * from './status'
export * from './permission'
export * from './cache'
export * from './event'
export * from './regex'
export * from './format'
export * from './validation'
export * from './media'
export * from './social'
export * from './analytics'
export * from './seo'
export * from './pwa'
export * from './performance'
export * from './security'
export * from './accessibility'
export * from './internationalization'
export * from './monetization'
export * from './gamification'
export * from './recommendation'
export * from './moderation'
export * from './compliance'
export * from './integration'
export * from './deployment'
export * from './monitoring'
export * from './testing'
export * from './documentation'
export * from './development'

// 应用基础信息
export const APP_INFO = {
  name: 'VideoSocial',
  version: '1.0.0',
  description: 'Vue3视频社交网站',
  author: 'Development Team',
  homepage: 'https://videosocial.com',
  repository: 'https://github.com/videosocial/frontend',
  license: 'MIT',
  keywords: ['vue3', 'video', 'social', 'typescript', 'vite'],
  buildTime: new Date().toISOString(),
  commitHash: process.env.VITE_COMMIT_HASH || 'unknown',
  buildNumber: process.env.VITE_BUILD_NUMBER || '0'
} as const

// 环境配置
export const ENV_CONFIG = {
  development: {
    apiUrl: 'http://localhost:3000/api',
    wsUrl: 'ws://localhost:3000',
    cdnUrl: 'http://localhost:3000/static',
    debug: true,
    mock: true,
    analytics: false,
    errorReporting: false
  },
  staging: {
    apiUrl: 'https://api-staging.videosocial.com',
    wsUrl: 'wss://ws-staging.videosocial.com',
    cdnUrl: 'https://cdn-staging.videosocial.com',
    debug: true,
    mock: false,
    analytics: true,
    errorReporting: true
  },
  production: {
    apiUrl: 'https://api.videosocial.com',
    wsUrl: 'wss://ws.videosocial.com',
    cdnUrl: 'https://cdn.videosocial.com',
    debug: false,
    mock: false,
    analytics: true,
    errorReporting: true
  }
} as const

// 获取当前环境配置
export const getCurrentEnvConfig = () => {
  const env = import.meta.env.MODE as keyof typeof ENV_CONFIG
  return ENV_CONFIG[env] || ENV_CONFIG.development
}

// 浏览器支持
export const BROWSER_SUPPORT = {
  chrome: 88,
  firefox: 85,
  safari: 14,
  edge: 88,
  opera: 74,
  samsung: 15,
  ios: 14,
  android: 88
} as const

// 设备类型
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile',
  TV: 'tv',
  WATCH: 'watch',
  VR: 'vr',
  AR: 'ar'
} as const

// 操作系统
export const OS_TYPES = {
  WINDOWS: 'windows',
  MACOS: 'macos',
  LINUX: 'linux',
  IOS: 'ios',
  ANDROID: 'android',
  CHROME_OS: 'chromeos',
  UNKNOWN: 'unknown'
} as const

// 网络类型
export const NETWORK_TYPES = {
  WIFI: 'wifi',
  CELLULAR: 'cellular',
  ETHERNET: 'ethernet',
  BLUETOOTH: 'bluetooth',
  WIMAX: 'wimax',
  OTHER: 'other',
  UNKNOWN: 'unknown',
  NONE: 'none'
} as const

// 连接速度
export const CONNECTION_SPEEDS = {
  SLOW_2G: 'slow-2g',
  TWO_G: '2g',
  THREE_G: '3g',
  FOUR_G: '4g',
  FIVE_G: '5g',
  UNKNOWN: 'unknown'
} as const

// 时区
export const TIMEZONES = {
  UTC: 'UTC',
  BEIJING: 'Asia/Shanghai',
  TOKYO: 'Asia/Tokyo',
  NEW_YORK: 'America/New_York',
  LONDON: 'Europe/London',
  PARIS: 'Europe/Paris',
  SYDNEY: 'Australia/Sydney',
  DUBAI: 'Asia/Dubai',
  MOSCOW: 'Europe/Moscow',
  SAO_PAULO: 'America/Sao_Paulo'
} as const

// 货币
export const CURRENCIES = {
  USD: { code: 'USD', symbol: '$', name: 'US Dollar' },
  EUR: { code: 'EUR', symbol: '€', name: 'Euro' },
  GBP: { code: 'GBP', symbol: '£', name: 'British Pound' },
  JPY: { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  CNY: { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' },
  KRW: { code: 'KRW', symbol: '₩', name: 'South Korean Won' },
  AUD: { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  CAD: { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  CHF: { code: 'CHF', symbol: 'Fr', name: 'Swiss Franc' },
  SEK: { code: 'SEK', symbol: 'kr', name: 'Swedish Krona' }
} as const

// 文件类型
export const FILE_TYPES = {
  IMAGE: {
    JPEG: 'image/jpeg',
    PNG: 'image/png',
    GIF: 'image/gif',
    WEBP: 'image/webp',
    SVG: 'image/svg+xml',
    BMP: 'image/bmp',
    ICO: 'image/x-icon',
    TIFF: 'image/tiff'
  },
  VIDEO: {
    MP4: 'video/mp4',
    WEBM: 'video/webm',
    OGV: 'video/ogg',
    AVI: 'video/avi',
    MOV: 'video/quicktime',
    WMV: 'video/x-ms-wmv',
    FLV: 'video/x-flv',
    MKV: 'video/x-matroska'
  },
  AUDIO: {
    MP3: 'audio/mpeg',
    WAV: 'audio/wav',
    OGG: 'audio/ogg',
    AAC: 'audio/aac',
    FLAC: 'audio/flac',
    M4A: 'audio/mp4',
    WMA: 'audio/x-ms-wma'
  },
  DOCUMENT: {
    PDF: 'application/pdf',
    DOC: 'application/msword',
    DOCX: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    XLS: 'application/vnd.ms-excel',
    XLSX: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    PPT: 'application/vnd.ms-powerpoint',
    PPTX: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    TXT: 'text/plain',
    RTF: 'application/rtf'
  },
  ARCHIVE: {
    ZIP: 'application/zip',
    RAR: 'application/x-rar-compressed',
    TAR: 'application/x-tar',
    GZ: 'application/gzip',
    SEVEN_Z: 'application/x-7z-compressed'
  }
} as const

// 文件大小限制
export const FILE_SIZE_LIMITS = {
  AVATAR: 5 * 1024 * 1024, // 5MB
  COVER: 10 * 1024 * 1024, // 10MB
  VIDEO: 500 * 1024 * 1024, // 500MB
  AUDIO: 50 * 1024 * 1024, // 50MB
  IMAGE: 20 * 1024 * 1024, // 20MB
  DOCUMENT: 100 * 1024 * 1024, // 100MB
  SUBTITLE: 1 * 1024 * 1024, // 1MB
  THUMBNAIL: 2 * 1024 * 1024 // 2MB
} as const

// 颜色主题
export const COLOR_THEMES = {
  LIGHT: {
    primary: '#1976d2',
    secondary: '#424242',
    accent: '#82b1ff',
    error: '#ff5252',
    info: '#2196f3',
    success: '#4caf50',
    warning: '#ffc107',
    background: '#ffffff',
    surface: '#ffffff',
    text: '#212121'
  },
  DARK: {
    primary: '#2196f3',
    secondary: '#424242',
    accent: '#ff4081',
    error: '#ff5252',
    info: '#2196f3',
    success: '#4caf50',
    warning: '#ffc107',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#ffffff'
  },
  AUTO: 'auto'
} as const

// 动画持续时间
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 1000
} as const

// 缓动函数
export const EASING_FUNCTIONS = {
  LINEAR: 'linear',
  EASE: 'ease',
  EASE_IN: 'ease-in',
  EASE_OUT: 'ease-out',
  EASE_IN_OUT: 'ease-in-out',
  CUBIC_BEZIER: 'cubic-bezier(0.4, 0, 0.2, 1)'
} as const

// 断点
export const BREAKPOINTS = {
  XS: 0,
  SM: 600,
  MD: 960,
  LG: 1280,
  XL: 1920
} as const

// Z-index层级
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
  LOADING: 1090,
  OVERLAY: 9999
} as const

// 键盘按键
export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  ESCAPE: 'Escape',
  SPACE: ' ',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown',
  DELETE: 'Delete',
  BACKSPACE: 'Backspace'
} as const

// 鼠标按键
export const MOUSE_BUTTONS = {
  LEFT: 0,
  MIDDLE: 1,
  RIGHT: 2
} as const

// 触摸手势
export const TOUCH_GESTURES = {
  TAP: 'tap',
  DOUBLE_TAP: 'doubletap',
  LONG_PRESS: 'longpress',
  SWIPE_LEFT: 'swipeleft',
  SWIPE_RIGHT: 'swiperight',
  SWIPE_UP: 'swipeup',
  SWIPE_DOWN: 'swipedown',
  PINCH_IN: 'pinchin',
  PINCH_OUT: 'pinchout',
  ROTATE: 'rotate'
} as const

// 方向
export const DIRECTIONS = {
  UP: 'up',
  DOWN: 'down',
  LEFT: 'left',
  RIGHT: 'right',
  TOP: 'top',
  BOTTOM: 'bottom',
  CENTER: 'center',
  HORIZONTAL: 'horizontal',
  VERTICAL: 'vertical'
} as const

// 对齐方式
export const ALIGNMENTS = {
  START: 'start',
  END: 'end',
  CENTER: 'center',
  STRETCH: 'stretch',
  BASELINE: 'baseline',
  SPACE_BETWEEN: 'space-between',
  SPACE_AROUND: 'space-around',
  SPACE_EVENLY: 'space-evenly'
} as const

// 位置
export const POSITIONS = {
  STATIC: 'static',
  RELATIVE: 'relative',
  ABSOLUTE: 'absolute',
  FIXED: 'fixed',
  STICKY: 'sticky'
} as const

// 显示类型
export const DISPLAY_TYPES = {
  NONE: 'none',
  BLOCK: 'block',
  INLINE: 'inline',
  INLINE_BLOCK: 'inline-block',
  FLEX: 'flex',
  INLINE_FLEX: 'inline-flex',
  GRID: 'grid',
  INLINE_GRID: 'inline-grid',
  TABLE: 'table',
  TABLE_ROW: 'table-row',
  TABLE_CELL: 'table-cell'
} as const

// 溢出处理
export const OVERFLOW_TYPES = {
  VISIBLE: 'visible',
  HIDDEN: 'hidden',
  SCROLL: 'scroll',
  AUTO: 'auto',
  CLIP: 'clip'
} as const

// 文本对齐
export const TEXT_ALIGNMENTS = {
  LEFT: 'left',
  RIGHT: 'right',
  CENTER: 'center',
  JUSTIFY: 'justify',
  START: 'start',
  END: 'end'
} as const

// 字体粗细
export const FONT_WEIGHTS = {
  THIN: 100,
  EXTRA_LIGHT: 200,
  LIGHT: 300,
  NORMAL: 400,
  MEDIUM: 500,
  SEMI_BOLD: 600,
  BOLD: 700,
  EXTRA_BOLD: 800,
  BLACK: 900
} as const

// 字体样式
export const FONT_STYLES = {
  NORMAL: 'normal',
  ITALIC: 'italic',
  OBLIQUE: 'oblique'
} as const

// 文本装饰
export const TEXT_DECORATIONS = {
  NONE: 'none',
  UNDERLINE: 'underline',
  OVERLINE: 'overline',
  LINE_THROUGH: 'line-through'
} as const

// 文本转换
export const TEXT_TRANSFORMS = {
  NONE: 'none',
  CAPITALIZE: 'capitalize',
  UPPERCASE: 'uppercase',
  LOWERCASE: 'lowercase'
} as const

// 边框样式
export const BORDER_STYLES = {
  NONE: 'none',
  SOLID: 'solid',
  DASHED: 'dashed',
  DOTTED: 'dotted',
  DOUBLE: 'double',
  GROOVE: 'groove',
  RIDGE: 'ridge',
  INSET: 'inset',
  OUTSET: 'outset'
} as const

// 阴影类型
export const SHADOW_TYPES = {
  NONE: 'none',
  SMALL: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
  MEDIUM: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
  LARGE: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
  EXTRA_LARGE: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)'
} as const

// 过渡类型
export const TRANSITION_TYPES = {
  FADE: 'fade',
  SLIDE: 'slide',
  SCALE: 'scale',
  ROTATE: 'rotate',
  FLIP: 'flip',
  BOUNCE: 'bounce',
  ELASTIC: 'elastic'
} as const

// 加载状态
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
} as const

// 数据状态
export const DATA_STATES = {
  EMPTY: 'empty',
  LOADING: 'loading',
  LOADED: 'loaded',
  ERROR: 'error',
  REFRESHING: 'refreshing'
} as const

// 连接状态
export const CONNECTION_STATES = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
} as const

// 默认导出
export default {
  APP_INFO,
  ENV_CONFIG,
  getCurrentEnvConfig,
  BROWSER_SUPPORT,
  DEVICE_TYPES,
  OS_TYPES,
  NETWORK_TYPES,
  CONNECTION_SPEEDS,
  TIMEZONES,
  CURRENCIES,
  FILE_TYPES,
  FILE_SIZE_LIMITS,
  COLOR_THEMES,
  ANIMATION_DURATIONS,
  EASING_FUNCTIONS,
  BREAKPOINTS,
  Z_INDEX,
  KEYBOARD_KEYS,
  MOUSE_BUTTONS,
  TOUCH_GESTURES,
  DIRECTIONS,
  ALIGNMENTS,
  POSITIONS,
  DISPLAY_TYPES,
  OVERFLOW_TYPES,
  TEXT_ALIGNMENTS,
  FONT_WEIGHTS,
  FONT_STYLES,
  TEXT_DECORATIONS,
  TEXT_TRANSFORMS,
  BORDER_STYLES,
  SHADOW_TYPES,
  TRANSITION_TYPES,
  LOADING_STATES,
  DATA_STATES,
  CONNECTION_STATES
}