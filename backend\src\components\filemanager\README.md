# File Manager Component

一个用于浏览、上传、管理文件和文件夹的可重用组件，适用于图片管理、视频管理等场景。

## 功能特点

- 左侧目录树 + 右侧文件浏览器的经典布局
- 支持文件上传、浏览、删除、重命名等操作
- 支持创建目录、删除目录
- 支持文件预览（图片和视频）
- 支持按文件类型过滤（图片、视频、全部）
- 支持文件搜索
- 提供普通模式和文件选择器模式
- 支持上下文菜单（右键菜单）
- 响应式设计，适应不同屏幕大小

## 组件结构

```
filemanager/
├── FileManager.vue          # 主组件
├── UploadDialog.vue         # 文件上传对话框
├── CreateFolderDialog.vue   # 创建文件夹对话框
├── RenameDialog.vue         # 重命名对话框
├── PreviewDialog.vue        # 文件预览对话框
├── UrlOrFileInput.vue       # URL或文件输入框
└── README.md                # 文档
```

## 使用方法

### 基本使用

```vue
<template>
  <div style="height: 600px">
    <FileManager />
  </div>
</template>

<script setup>
import FileManager from '@/components/filemanager/FileManager.vue';
</script>
```

### 作为文件选择器使用

```vue
<template>
  <div>
    <el-button @click="showSelector = true">选择文件</el-button>
    
    <el-dialog v-model="showSelector" title="选择文件" width="80%">
      <div style="height: 600px">
        <FileManager
          mode="selector"
          initial-file-type="image"
          :multiple="false"
          @select="handleFileSelected"
          @cancel="showSelector = false"
        />
      </div>
    </el-dialog>
    
    <div v-if="selectedFile">
      选中的文件: {{ selectedFile.name }}
      <img v-if="selectedFile.type === 'image'" :src="selectedFile.url" style="max-width: 200px" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FileManager from '@/components/filemanager/FileManager.vue';

const showSelector = ref(false);
const selectedFile = ref(null);

const handleFileSelected = (files) => {
  selectedFile.value = files[0];
  showSelector.value = false;
};
</script>
```

### 使用URL或文件选择输入框

```vue
<template>
  <div>
    <UrlOrFileInput
      v-model="imageUrl"
      fileType="image"
      subDir="products"
      placeholder="请输入图片URL或选择图片"
    />
    
    <div v-if="imageUrl">
      <img :src="imageUrl" style="max-width: 300px; margin-top: 10px;" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';

const imageUrl = ref('');
</script>
```

## Props

### FileManager 组件

| 属性名 | 类型 | 默认值 | 说明 |
|-----|-----|-----|-----|
| mode | String | 'manager' | 组件模式，'manager' 为管理模式，'selector' 为选择器模式 |
| initialFileType | String | 'all' | 初始文件类型，可选值：'image'、'video'、'all' |
| multiple | Boolean | false | 是否允许多选（仅在选择器模式下有效） |

### UrlOrFileInput 组件

| 属性名 | 类型 | 默认值 | 说明 |
|-----|-----|-----|-----|
| modelValue | String | '' | 输入框的值，支持v-model绑定 |
| placeholder | String | '请输入URL或选择文件' | 输入框占位文本 |
| fileType | String | 'image' | 文件类型，可选值：'image'、'video' |
| subDir | String | '' | 上传子目录 |
| disabled | Boolean | false | 是否禁用输入框 |
| clearable | Boolean | true | 是否显示清除按钮 |
| showPreview | Boolean | true | 是否显示预览图标 |

## 事件

### FileManager 组件

| 事件名 | 参数 | 说明 |
|-----|-----|-----|
| select | files: Array | 选择文件时触发（选择器模式下） |
| cancel | - | 取消选择时触发（选择器模式下） |

### UrlOrFileInput 组件

| 事件名 | 参数 | 说明 |
|-----|-----|-----|
| update:modelValue | value: String | 值更新时触发 |
| change | value: String | 值变更时触发 |

## 后端API要求

此组件需要以下后端API支持：

1. `/api/files/list` - 获取文件列表
2. `/api/files/upload` - 上传文件
3. `/api/files/create-directory` - 创建目录
4. `/api/files/rename` - 重命名文件或目录
5. `/api/files/delete` - 删除文件或目录

请确保后端实现了这些API并符合组件预期的请求和响应格式。

## 权限和安全

- 文件管理器基于环境变量中配置的上传路径（`UPLOAD_SMALL_IMAGE_PATH`、`UPLOAD_PICTURE_PATH`、`UPLOAD_VIDEO_PATH`）进行文件访问
- 不允许删除系统根目录
- 不允许删除非空目录
- 实现了路径遍历防护

## 示例

### 图片管理模块

```vue
<template>
  <div class="image-manager">
    <h2>图片管理</h2>
    <div class="file-manager-container">
      <FileManager
        initialFileType="image"
      />
    </div>
  </div>
</template>

<script setup>
import FileManager from '@/components/filemanager/FileManager.vue';
</script>

<style scoped>
.image-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.file-manager-container {
  flex: 1;
  min-height: 500px;
}
</style>
```

### 视频管理模块

```vue
<template>
  <div class="video-manager">
    <h2>视频管理</h2>
    <div class="file-manager-container">
      <FileManager
        initialFileType="video"
      />
    </div>
  </div>
</template>

<script setup>
import FileManager from '@/components/filemanager/FileManager.vue';
</script>

<style scoped>
.video-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.file-manager-container {
  flex: 1;
  min-height: 500px;
}
</style>
``` 