<template>
  <div class="comment-item" :class="{ 'is-reply': isReply }">
    <el-avatar :size="40" :src="comment.user.avatar" class="comment-avatar" />
    <div class="comment-content">
      <div class="comment-header">
        <span class="author-name">{{ comment.user.name }}</span>
        <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
      </div>
      
      <div class="comment-text">
        <template v-if="comment.replyTo">
          <span class="reply-to">回复 @{{ comment.replyTo.user.name }}：</span>
        </template>
        {{ comment.content }}
      </div>

      <!-- 评论图片 -->
      <div v-if="comment.images && comment.images.length > 0" class="comment-images">
        <el-image
          v-for="(image, index) in comment.images"
          :key="index"
          :src="image"
          :preview-src-list="comment.images"
          fit="cover"
          class="comment-image"
        />
      </div>

      <div class="comment-actions">
        <div class="action-item" @click="$emit('like', comment)">
          <SvgIcon :name="comment.isLiked ? 'like' : 'like'" :color="comment.isLiked ? '#ff4757' : '#8a8a8a'" :size="16" />
          <span>{{ formatNumber(comment.likes) }}</span>
        </div>
        <div class="action-item" @click="$emit('reply', comment)">
          <SvgIcon name="reply" color="#8a8a8a" :size="16" />
          <span>回复</span>
        </div>
      </div>

      <!-- 回复列表 -->
      <div v-if="comment.replies && comment.replies.length > 0" class="replies-list">
        <CommentItem
          v-for="reply in comment.replies"
          :key="reply.id"
          :comment="reply"
          :current-user-avatar="currentUserAvatar"
          :is-reply="true"
          @like="$emit('like', reply)"
          @reply="(replyComment) => $emit('reply', replyComment, comment)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from '../icons/SvgIcon.vue'
import type { Comment } from '@/types/comment'
import { formatTime } from '@/core/utils'

defineProps<{
  comment: Comment
  currentUserAvatar: string
  isReply?: boolean
}>()

// 使用统一的工具函数

const formatNumber = (num: number) => {
  if (num < 1000) return num
  if (num < 10000) return `${(num / 1000).toFixed(1)}k`
  return `${(num / 10000).toFixed(1)}w`
}
</script>

<style scoped lang="scss">
.comment-item {
  display: flex;
  margin-bottom: 20px;
  text-align: left;
  &.is-reply {
    margin-left: 48px;
    margin-bottom: 16px;
  }

  .comment-avatar {
    margin-right: 12px;
    flex-shrink: 0;
  }

  .comment-content {
    flex: 1;
    min-width: 0;

    .comment-header {
      margin-bottom: 4px;

      .author-name {
        font-weight: 500;
        margin-right: 8px;
      }

      .comment-time {
        color: var(--el-text-color-secondary);
        font-size: 12px;
      }
    }

    .comment-text {
      margin-bottom: 8px;
      word-break: break-word;

      .reply-to {
        color: var(--el-color-primary);
      }
    }

    .comment-images {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 8px;

      .comment-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        object-fit: cover;
      }
    }

    .comment-actions {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;

      .action-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--el-text-color-secondary);
        cursor: pointer;
        transition: color 0.2s;

        &:hover {
          color: var(--el-color-primary);
        }

        span {
          font-size: 12px;
        }
      }
    }

    .replies-list {
      margin-top: 16px;
    }
  }
}
</style>