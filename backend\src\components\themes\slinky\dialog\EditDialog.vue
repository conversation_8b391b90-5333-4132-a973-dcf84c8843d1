<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :modal-class="modalClass"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :class="['slinky-dialog', dialogClass]"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <div class="dialog-content">
      <!-- 编辑信息展示 -->
      <div v-if="showEditInfo" class="edit-info">
        <el-alert
          :title="`正在编辑: ${getEditTitle()}`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 变更对比 -->
      <div v-if="showChanges && hasChanges" class="changes-section">
        <el-collapse v-model="changesPanelOpen">
          <el-collapse-item title="查看变更内容" name="changes">
            <div class="changes-list">
              <div
                v-for="change in changedFields"
                :key="change.field"
                class="change-item"
              >
                <div class="field-name">{{ change.label }}:</div>
                <div class="change-values">
                  <span class="old-value">{{ formatValue(change.oldValue) }}</span>
                  <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                  <span class="new-value">{{ formatValue(change.newValue) }}</span>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        :size="size"
        :disabled="submitting"
        @submit.prevent="handleSubmit"
      >
        <template v-for="field in fields" :key="field.prop">
          <el-form-item
            :label="field.label"
            :prop="field.prop"
            :rules="field.rules"
            :required="field.required"
            :class="[field.class, { 'field-changed': isFieldChanged(field.prop) }]"
          >
            <!-- 文本输入框 -->
            <el-input
              v-if="field.type === 'input'"
              v-model="formData[field.prop]"
              :type="field.inputType || 'text'"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled || field.readonly"
              :readonly="field.readonly"
              :maxlength="field.maxlength"
              :minlength="field.minlength"
              :show-word-limit="field.showWordLimit"
              :prefix-icon="field.prefixIcon"
              :suffix-icon="field.suffixIcon"
              @change="handleFieldChange(field.prop, $event)"
              @blur="field.onBlur && field.onBlur($event)"
              @focus="field.onFocus && field.onFocus($event)"
            />
            
            <!-- 文本域 -->
            <el-input
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.prop]"
              type="textarea"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled || field.readonly"
              :readonly="field.readonly"
              :maxlength="field.maxlength"
              :minlength="field.minlength"
              :show-word-limit="field.showWordLimit"
              :rows="field.rows || 3"
              :autosize="field.autosize"
              :resize="field.resize"
              @change="handleFieldChange(field.prop, $event)"
            />
            
            <!-- 下拉选择 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled || field.readonly"
              :multiple="field.multiple"
              :filterable="field.filterable"
              :allow-create="field.allowCreate"
              :remote="field.remote"
              :remote-method="field.remoteMethod"
              :loading="field.loading"
              :multiple-limit="field.multipleLimit"
              @change="handleFieldChange(field.prop, $event)"
              @visible-change="field.onVisibleChange && field.onVisibleChange($event)"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
            
            <!-- 级联选择 -->
            <el-cascader
              v-else-if="field.type === 'cascader'"
              v-model="formData[field.prop]"
              :options="field.options"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled || field.readonly"
              :show-all-levels="field.showAllLevels !== false"
              :collapse-tags="field.collapseTags"
              :separator="field.separator || '/'"
              :props="field.cascaderProps"
              @change="handleFieldChange(field.prop, $event)"
            />
            
            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="formData[field.prop]"
              :type="field.dateType || 'date'"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled || field.readonly"
              :readonly="field.readonly"
              :editable="field.editable !== false"
              :format="field.format"
              :value-format="field.valueFormat"
              :start-placeholder="field.startPlaceholder"
              :end-placeholder="field.endPlaceholder"
              :range-separator="field.rangeSeparator || '至'"
              :shortcuts="field.shortcuts"
              :disabled-date="field.disabledDate"
              @change="handleFieldChange(field.prop, $event)"
            />
            
            <!-- 数字输入 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder"
              :disabled="field.disabled || field.readonly"
              :min="field.min"
              :max="field.max"
              :step="field.step"
              :step-strictly="field.stepStrictly"
              :precision="field.precision"
              :controls="field.controls !== false"
              :controls-position="field.controlsPosition"
              @change="handleFieldChange(field.prop, $event)"
            />
            
            <!-- 开关 -->
            <el-switch
              v-else-if="field.type === 'switch'"
              v-model="formData[field.prop]"
              :disabled="field.disabled || field.readonly"
              :width="field.width"
              :active-text="field.activeText"
              :inactive-text="field.inactiveText"
              :active-value="field.activeValue"
              :inactive-value="field.inactiveValue"
              :active-color="field.activeColor"
              :inactive-color="field.inactiveColor"
              @change="handleFieldChange(field.prop, $event)"
            />
            
            <!-- 单选框组 -->
            <el-radio-group
              v-else-if="field.type === 'radio'"
              v-model="formData[field.prop]"
              :disabled="field.disabled || field.readonly"
              :size="field.size"
              @change="handleFieldChange(field.prop, $event)"
            >
              <el-radio
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            
            <!-- 复选框组 -->
            <el-checkbox-group
              v-else-if="field.type === 'checkbox'"
              v-model="formData[field.prop]"
              :disabled="field.disabled || field.readonly"
              :min="field.min"
              :max="field.max"
              @change="handleFieldChange(field.prop, $event)"
            >
              <el-checkbox
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
            
            <!-- 文件上传 -->
            <el-upload
              v-else-if="field.type === 'upload'"
              ref="uploadRef"
              :action="field.action || uploadAction"
              :headers="field.headers || uploadHeaders"
              :data="field.data"
              :name="field.name || 'file'"
              :multiple="field.multiple"
              :accept="field.accept"
              :file-list="formData[field.prop] || []"
              :auto-upload="field.autoUpload !== false"
              :disabled="field.disabled || field.readonly"
              :limit="field.limit"
              :list-type="field.listType || 'text'"
              :before-upload="field.beforeUpload"
              :on-success="(response, file, fileList) => handleUploadSuccess(field.prop, response, file, fileList)"
              :on-error="(error, file, fileList) => handleUploadError(field.prop, error, file, fileList)"
              :on-remove="(file, fileList) => handleUploadRemove(field.prop, file, fileList)"
              :on-exceed="field.onExceed"
            >
              <template v-if="field.listType === 'picture-card'">
                <el-icon><Plus /></el-icon>
              </template>
              <template v-else>
                <el-button type="primary" :disabled="field.disabled || field.readonly">
                  <el-icon><Upload /></el-icon>
                  {{ field.uploadText || '选择文件' }}
                </el-button>
              </template>
              <template #tip v-if="field.tip">
                <div class="el-upload__tip">{{ field.tip }}</div>
              </template>
            </el-upload>
            
            <!-- 富文本编辑器 -->
            <div v-else-if="field.type === 'editor'" class="editor-container">
              <slot 
                :name="`editor-${field.prop}`" 
                :value="formData[field.prop]"
                :onChange="(value: any) => handleFieldChange(field.prop, value)"
                :disabled="field.disabled || field.readonly"
              >
                <el-input
                  v-model="formData[field.prop]"
                  type="textarea"
                  :rows="field.rows || 6"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="field.disabled || field.readonly"
                  @change="handleFieldChange(field.prop, $event)"
                />
              </slot>
            </div>
            
            <!-- 自定义插槽 -->
            <template v-else-if="field.type === 'slot'">
              <slot 
                :name="field.slotName || field.prop"
                :field="field"
                :value="formData[field.prop]"
                :formData="formData"
                :onChange="(value: any) => handleFieldChange(field.prop, value)"
                :disabled="field.disabled || field.readonly"
              />
            </template>
            
            <!-- 自定义渲染 -->
            <component
              v-else-if="field.type === 'component'"
              :is="field.component"
              v-model="formData[field.prop]"
              v-bind="field.componentProps"
              :disabled="field.disabled || field.readonly"
              @change="handleFieldChange(field.prop, $event)"
            />
            
            <!-- 提示信息 -->
            <div v-if="field.tip" class="field-tip">
              {{ field.tip }}
            </div>
            
            <!-- 变更标识 -->
            <div v-if="isFieldChanged(field.prop)" class="field-change-indicator">
              <el-icon class="change-icon"><Edit /></el-icon>
              <span class="change-text">已修改</span>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="submitting">
          {{ cancelText }}
        </el-button>
        <el-button
          v-if="showReset"
          @click="handleReset"
          :disabled="submitting || !hasChanges"
        >
          <el-icon><RefreshLeft /></el-icon>
          重置
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          :disabled="!hasChanges"
        >
          <template v-if="!submitting">
            <el-icon><Check /></el-icon>
            {{ submitText }}
          </template>
          <template v-else>
            {{ submitLoadingText }}
          </template>
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
    ArrowRight,
    Check,
    Edit,
    Plus,
    RefreshLeft,
    Upload
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadUserFile } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, nextTick, reactive, ref, watch } from 'vue'

// 接口定义
interface EditFormField {
  prop: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'cascader' | 'date' | 'number' | 'switch' | 'radio' | 'checkbox' | 'upload' | 'editor' | 'slot' | 'component'
  required?: boolean
  rules?: any[]
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  class?: string
  
  // input/textarea 特有属性
  inputType?: string
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  prefixIcon?: string
  suffixIcon?: string
  rows?: number
  autosize?: boolean | { minRows?: number; maxRows?: number }
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'
  
  // select 特有属性
  options?: { label: string; value: any; disabled?: boolean }[]
  multiple?: boolean
  filterable?: boolean
  allowCreate?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => void
  loading?: boolean
  multipleLimit?: number
  onVisibleChange?: (visible: boolean) => void
  
  // cascader 特有属性
  cascaderProps?: any
  showAllLevels?: boolean
  collapseTags?: boolean
  separator?: string
  
  // date 特有属性
  dateType?: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange'
  format?: string
  valueFormat?: string
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  shortcuts?: any[]
  disabledDate?: (date: Date) => boolean
  editable?: boolean
  
  // number 特有属性
  min?: number
  max?: number
  step?: number
  stepStrictly?: boolean
  precision?: number
  controls?: boolean
  controlsPosition?: 'right' | ''
  
  // switch 特有属性
  width?: number
  activeText?: string
  inactiveText?: string
  activeValue?: any
  inactiveValue?: any
  activeColor?: string
  inactiveColor?: string
  
  // radio/checkbox 特有属性
  size?: 'large' | 'default' | 'small'
  
  // upload 特有属性
  action?: string
  headers?: Record<string, any>
  data?: Record<string, any>
  name?: string
  accept?: string
  limit?: number
  listType?: 'text' | 'picture' | 'picture-card'
  autoUpload?: boolean
  beforeUpload?: (file: File) => boolean | Promise<any>
  onExceed?: (files: File[], fileList: UploadUserFile[]) => void
  uploadText?: string
  tip?: string
  
  // editor 特有属性
  // 通过插槽实现
  
  // slot 特有属性
  slotName?: string
  
  // component 特有属性
  component?: any
  componentProps?: Record<string, any>
  
  // 事件回调
  onBlur?: (event: Event) => void
  onFocus?: (event: Event) => void
  onChange?: (value: any) => void
}

interface ChangeRecord {
  field: string
  label: string
  oldValue: any
  newValue: any
}

// Props 定义
interface Props {
  // 基础属性
  modelValue?: boolean
  title?: string
  width?: string | number
  fullscreen?: boolean
  top?: string
  modal?: boolean
  modalClass?: string
  appendToBody?: boolean
  lockScroll?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  destroyOnClose?: boolean
  dialogClass?: string
  
  // 表单属性
  fields: EditFormField[]
  data?: Record<string, any>
  rules?: FormRules
  labelWidth?: string
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'default' | 'small'
  
  // 编辑特有属性
  originalData?: Record<string, any>
  showEditInfo?: boolean
  showChanges?: boolean
  showReset?: boolean
  
  // 文本定制
  submitText?: string
  submitLoadingText?: string
  cancelText?: string
  
  // 上传配置
  uploadAction?: string
  uploadHeaders?: Record<string, any>
  
  // 事件回调
  onSubmit?: (data: Record<string, any>, changes: ChangeRecord[]) => Promise<void>
  onCancel?: () => void
  onReset?: () => void
  onBeforeClose?: (done: () => void) => void
  onFieldChange?: (field: string, value: any, oldValue: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '编辑',
  width: '600px',
  fullscreen: false,
  top: '15vh',
  modal: true,
  appendToBody: true,
  lockScroll: true,
  closeOnClickModal: false,
  closeOnPressEscape: true,
  showClose: true,
  destroyOnClose: false,
  labelWidth: '100px',
  labelPosition: 'left',
  size: 'default',
  showEditInfo: true,
  showChanges: true,
  showReset: true,
  submitText: '保存',
  submitLoadingText: '保存中...',
  cancelText: '取消',
  uploadAction: '/api/upload',
  uploadHeaders: () => ({})
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'open': []
  'opened': []
  'close': []
  'closed': []
  'submit': [data: Record<string, any>, changes: ChangeRecord[]]
  'cancel': []
  'reset': []
  'field-change': [field: string, value: any, oldValue: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const formData = reactive<Record<string, any>>({})
const originalFormData = ref<Record<string, any>>({})
const submitting = ref(false)
const changesPanelOpen = ref(['changes'])

// 计算属性
const hasChanges = computed(() => {
  return changedFields.value.length > 0
})

const changedFields = computed((): ChangeRecord[] => {
  const changes: ChangeRecord[] = []
  
  for (const field of props.fields) {
    const currentValue = formData[field.prop]
    const originalValue = originalFormData.value[field.prop]
    
    if (!isEqual(currentValue, originalValue)) {
      changes.push({
        field: field.prop,
        label: field.label,
        oldValue: originalValue,
        newValue: currentValue
      })
    }
  }
  
  return changes
})

// 辅助方法
const isEqual = (a: any, b: any): boolean => {
  if (a === b) return true
  if (a == null || b == null) return false
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    return a.every((item, index) => isEqual(item, b[index]))
  }
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a)
    const keysB = Object.keys(b)
    if (keysA.length !== keysB.length) return false
    return keysA.every(key => isEqual(a[key], b[key]))
  }
  return false
}

const isFieldChanged = (fieldProp: string): boolean => {
  return changedFields.value.some(change => change.field === fieldProp)
}

const getEditTitle = (): string => {
  const titleField = props.fields.find(f => f.prop === 'title' || f.prop === 'name')
  if (titleField && formData[titleField.prop]) {
    return formData[titleField.prop]
  }
  return '当前记录'
}

const formatValue = (value: any): string => {
  if (value == null || value === '') return '(空)'
  if (Array.isArray(value)) return value.join(', ')
  if (typeof value === 'object') return JSON.stringify(value)
  if (typeof value === 'boolean') return value ? '是' : '否'
  return String(value)
}

// 事件处理
const handleFieldChange = (field: string, value: any) => {
  const oldValue = formData[field]
  formData[field] = value
  
  emit('field-change', field, value, oldValue)
  props.onFieldChange?.(field, value, oldValue)
}

const handleUploadSuccess = (field: string, response: any, file: any, fileList: any[]) => {
  formData[field] = fileList
  handleFieldChange(field, fileList)
}

const handleUploadError = (field: string, error: any, file: any, fileList: any[]) => {
  console.error('Upload error:', error)
  ElMessage.error('文件上传失败')
}

const handleUploadRemove = (field: string, file: any, fileList: any[]) => {
  formData[field] = fileList
  handleFieldChange(field, fileList)
}

const handleReset = () => {
  // 重置为原始数据
  Object.keys(formData).forEach(key => {
    formData[key] = originalFormData.value[key]
  })
  
  // 清除表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
  
  emit('reset')
  props.onReset?.()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const changes = changedFields.value
    
    if (props.onSubmit) {
      await props.onSubmit(formData, changes)
    }
    
    emit('submit', { ...formData }, changes)
    
    // 更新原始数据
    originalFormData.value = { ...formData }
    
    visible.value = false
  } catch (error) {
    console.error('Form validation failed:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  if (hasChanges.value) {
    ElMessageBox.confirm(
      '有未保存的修改，确认要取消吗？',
      '确认取消',
      {
        type: 'warning',
        confirmButtonText: '确认取消',
        cancelButtonText: '继续编辑'
      }
    ).then(() => {
      emit('cancel')
      props.onCancel?.()
      visible.value = false
    }).catch(() => {
      // 用户选择继续编辑
    })
  } else {
    emit('cancel')
    props.onCancel?.()
    visible.value = false
  }
}

const handleBeforeClose = (done: () => void) => {
  if (props.onBeforeClose) {
    props.onBeforeClose(done)
  } else if (hasChanges.value) {
    ElMessageBox.confirm(
      '有未保存的修改，确认要关闭吗？',
      '确认关闭',
      {
        type: 'warning',
        confirmButtonText: '确认关闭',
        cancelButtonText: '继续编辑'
      }
    ).then(() => {
      done()
    }).catch(() => {
      // 用户选择继续编辑
    })
  } else {
    done()
  }
}

const handleOpen = () => {
  emit('open')
}

const handleOpened = () => {
  emit('opened')
}

const handleClose = () => {
  emit('close')
}

const handleClosed = () => {
  emit('closed')
}

// 初始化数据
const initFormData = () => {
  const data = props.data || props.originalData || {}
  
  // 初始化表单数据
  props.fields.forEach(field => {
    formData[field.prop] = data[field.prop] ?? getDefaultValue(field.type)
  })
  
  // 保存原始数据
  originalFormData.value = { ...formData }
}

const getDefaultValue = (type: string) => {
  switch (type) {
    case 'checkbox':
      return []
    case 'switch':
      return false
    case 'number':
      return 0
    case 'upload':
      return []
    default:
      return ''
  }
}

// 监听数据变化
watch(() => props.data, () => {
  if (props.data) {
    initFormData()
  }
}, { immediate: true })

watch(() => props.originalData, () => {
  if (props.originalData) {
    initFormData()
  }
}, { immediate: true })

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  clearValidate: () => formRef.value?.clearValidate(),
  resetFields: () => formRef.value?.resetFields(),
  reset: handleReset,
  getChanges: () => changedFields.value,
  hasChanges: () => hasChanges.value
})
</script>

<style lang="scss" scoped>
.slinky-dialog {
  .dialog-content {
    .edit-info {
      margin-bottom: 16px;
    }
    
    .changes-section {
      margin-bottom: 20px;
      
      .changes-list {
        .change-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          padding: 8px;
          background-color: var(--el-fill-color-lighter);
          border-radius: var(--el-card-border-radius);
          
          .field-name {
            font-weight: 500;
            margin-right: 12px;
            min-width: 80px;
          }
          
          .change-values {
            display: flex;
            align-items: center;
            flex: 1;
            
            .old-value {
              color: var(--el-color-info);
              text-decoration: line-through;
              margin-right: 8px;
            }
            
            .arrow-icon {
              margin: 0 8px;
              color: var(--el-color-primary);
            }
            
            .new-value {
              color: var(--el-color-success);
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .el-form-item {
      position: relative;
      
      &.field-changed {
        .el-form-item__label {
          color: var(--el-color-primary);
          font-weight: 500;
        }
        
        .el-form-item__content {
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--el-color-primary);
            border-radius: var(--el-card-border-radius);
          }
        }
      }
      
      .field-change-indicator {
        position: absolute;
        right: 0;
        top: 0;
        display: flex;
        align-items: center;
        padding: 2px 6px;
        background-color: var(--el-color-primary);
        color: white;
        font-size: 12px;
        border-radius: 0 0 0 8px;
        z-index: 10;
        
        .change-icon {
          margin-right: 4px;
          font-size: 12px;
        }
        
        .change-text {
          font-size: 12px;
        }
      }
    }
    
    .editor-container {
      border: 1px solid var(--el-border-color);
      border-radius: var(--el-card-border-radius);
      overflow: hidden;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    .el-button {
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}
</style> 