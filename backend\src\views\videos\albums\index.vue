<template>
    <div class="app-container">
        <el-card>
            <template #header>
                <div class="filter-container">
                    <div class="title-container">
                        <h2>视频专辑管理</h2>
                        <div class="buttons">
                            <el-button type="primary" :icon="Plus" @click="handleAdd">添加专辑</el-button>
                            <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
                            <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 搜索栏 -->
            <AlbumSearchBar
                @search="handleSearch"
                @reset="handleReset"
            />

                  <!-- 表格 -->
      <AlbumTable
        :loading="loading"
        :albumList="albumList"
        :pagination="pagination"
        @view="handleDetail"
        @edit="handleEdit"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @batch-status="handleBatchStatus"
        @batch-delete="handleBatchDelete"
      />
              <!-- 详情对话框 -->
      <AlbumDetailDialog
        :visible="detailDialogVisible"
        :albumData="currentAlbum"
        @update:visible="(val: boolean) => detailDialogVisible = val"
      />

                  <!-- 编辑对话框 -->
      <AlbumFormDialog
        :visible="dialogVisible"
        :type="currentAlbum ? 'edit' : 'add'"
        :album-data="currentAlbum"
        @update:visible="(val: boolean) => dialogVisible = val"
        @success="handleDialogSuccess"
      />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入组件
import AlbumDetailDialog from './components/AlbumDetailDialog.vue';
import AlbumFormDialog from './components/AlbumFormDialog.vue';
import AlbumSearchBar from './components/AlbumSearchBar.vue';
import AlbumTable from './components/AlbumTable.vue';

// 导入API和类型
import {
    batchDeleteVideoAlbum,
    batchUpdateVideoAlbumStatus,
    deleteVideoAlbum,
    getVideoAlbumList,
    updateVideoAlbumStatus
} from '@/service/api/videos/albums';
import type { VideoAlbum } from '@/types/videoAlbum';

// 页面状态
const loading = ref(false);
const albumList = ref<VideoAlbum[]>([]);
const detailDialogVisible = ref(false);
const dialogVisible = ref(false);
const currentAlbum = ref<VideoAlbum | null>(null);

// 分页信息
const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
});

// 搜索表单
const searchParams = reactive({
    keyword: '',
    user_id: '',
    category_id: '',
    status: undefined as number | undefined,
    sort_by: 'created_at DESC'
});

// 获取专辑列表
const getList = async () => {
    loading.value = true;
    try {
        const params = {
            page: {
                pageNo: pagination.page,
                pageSize: pagination.pageSize
            },
            data: { ...searchParams }
        };

        const response = await getVideoAlbumList(params);
        if (response?.data?.list) {
            albumList.value = response.data.list;
            pagination.total = response.data.total || 0;
        } else {
            albumList.value = [];
            pagination.total = 0;
        }
    } catch (error) {
        console.error('获取专辑列表失败:', error);
        ElMessage.error('获取专辑列表失败');
        albumList.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 搜索处理
const handleSearch = (params: any) => {
    Object.assign(searchParams, params);
    pagination.page = 1;
    getList();
};

// 重置搜索
const handleReset = () => {
    Object.keys(searchParams).forEach(key => {
        if (key === 'sort_by') {
            searchParams[key as keyof typeof searchParams] = 'created_at DESC' as never;
        } else {
            searchParams[key as keyof typeof searchParams] = undefined as never;
        }
    });
    searchParams.keyword = '';
    searchParams.user_id = '';
    searchParams.category_id = '';
    
    pagination.page = 1;
    getList();
};

// 刷新列表
const refreshList = () => {
    getList();
};

// 添加专辑
const handleAdd = () => {
    currentAlbum.value = null;
    dialogVisible.value = true;
};

// 编辑专辑
const handleEdit = (album: VideoAlbum) => {
    currentAlbum.value = album;
    dialogVisible.value = true;
};

// 查看详情
const handleDetail = (album: VideoAlbum) => {
    currentAlbum.value = album;
    detailDialogVisible.value = true;
};

// 删除专辑
const handleDelete = async (album: VideoAlbum) => {
    try {
        await ElMessageBox.confirm(
            `确定要删除专辑"${album.title}"吗？此操作不可恢复！`,
            '删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        const {response} = await deleteVideoAlbum(album.id) as any;
        if (response.status === 200&&response.data.code==2000) {
            ElMessage.success('删除成功');
            getList();
        } else {
            ElMessage.error(response.data.message || '删除失败');
        }
    } catch (error) {
        console.error('删除专辑失败:', error);
    }
};

// 更新状态
const handleUpdateStatus = async (album: VideoAlbum, status: number) => {
    try {
        const {response} = await updateVideoAlbumStatus(album.id, status) as any;
        
        if (response.status === 200&&response.data.code==2000) {
            ElMessage.success('状态更新成功');
            getList();
        } else {
            ElMessage.error(response.data.message || '状态更新失败');
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        ElMessage.error('状态更新失败');
    }
};

// 状态修改（表格组件使用的事件名）
const handleChangeStatus = async (id: string, status: number) => {
    try {
        const {response} = await updateVideoAlbumStatus(id, status) as any;
        
        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('状态更新成功');
            getList();
        } else {
            ElMessage.error(response.data.message || '状态更新失败');
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        ElMessage.error('状态更新失败');
    }
};

// 批量状态更新
const handleBatchStatus = async (status: number, albums: VideoAlbum[]) => {
    if (!albums || albums.length === 0) {
        ElMessage.warning('请先选择要操作的专辑');
        return;
    }

    try {
        await ElMessageBox.confirm(
            `确定要${status === 1 ? '启用' : '禁用'}选中的 ${albums.length} 个专辑吗？`,
            '批量操作确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        const ids = albums.map(album => album.id);
        const {response} = await batchUpdateVideoAlbumStatus({ 
            data: { ids, status } 
        }) as any;
        
        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success(`批量${status === 1 ? '启用' : '禁用'}成功`);
            getList();
        } else {
            ElMessage.error(response.data.message || '批量操作失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量状态更新失败:', error);
            ElMessage.error('批量操作失败');
        }
    }
};

// 批量删除
const handleBatchDelete = async (albums: VideoAlbum[]) => {
    if (!albums || albums.length === 0) {
        ElMessage.warning('请先选择要删除的专辑');
        return;
    }

    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${albums.length} 个专辑吗？此操作不可恢复！`,
            '批量删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        const ids = albums.map(album => album.id);
        const {response} = await batchDeleteVideoAlbum({ 
            data: { ids } 
        }) as any;
        
        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('批量删除成功');
            getList();
        } else {
            ElMessage.error(response.data.message || '批量删除失败');
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('批量删除失败:', error);
            ElMessage.error('批量删除失败');
        }
    }
};

// 分页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getList();
};

// 分页组件的统一回调
const handlePaginationChange = (params: { page: number; pageSize: number }) => {
  pagination.page = params.page;
  pagination.pageSize = params.pageSize;
  getList();
};

// 对话框成功回调
const handleDialogSuccess = () => {
  dialogVisible.value = false;
  getList();
};

// 导出功能
const handleExport = () => {
    ElMessage.info('导出功能开发中...');
};

// 初始化
onMounted(() => {
    getList();
});
</script>

<style scoped lang="scss">
.app-container {
    padding: 20px;

    .filter-container {
        .title-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            h2 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }
            
            .buttons {
                display: flex;
                gap: 12px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
        
        .filter-container {
            .title-container {
                flex-direction: column;
                gap: 16px;
                
                .buttons {
                    justify-content: center;
                }
            }
        }
    }
}
</style>