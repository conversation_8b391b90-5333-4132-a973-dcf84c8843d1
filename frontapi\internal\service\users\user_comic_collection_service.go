package users

import (
	"frontapi/internal/models/users"
	"frontapi/internal/service/base"

	comicRepo "frontapi/internal/repository/comics"
	repo "frontapi/internal/repository/users"
)

// CreateComicCollectionRequest 创建漫画收藏请求
type CreateComicCollectionRequest struct {
	UserID     string `json:"userId" validate:"required"`  // 用户ID
	ComicID    string `json:"comicId" validate:"required"` // 漫画ID
	ComicTitle string `json:"comicTitle"`                  // 漫画标题
	ComicCover string `json:"comicCover"`                  // 漫画封面
	Author     string `json:"author"`                      // 作者
	Remark     string `json:"remark"`                      // 收藏备注
}

// UserComicCollectionService 用户漫画收藏服务接口
type UserComicCollectionService interface {
	base.IExtendedService[users.UserComicCollection]
}

// userComicCollectionService 用户漫画收藏服务实现
type userComicCollectionService struct {
	*base.ExtendedService[users.UserComicCollection]
	collectionRepo repo.UserComicCollectionRepository
	userRepo       repo.UserRepository
	comicRepo      comicRepo.ComicRepository
}

// NewUserComicCollectionService 创建用户漫画收藏服务实例
func NewUserComicCollectionService(
	collectionRepo repo.UserComicCollectionRepository,
	userRepo repo.UserRepository,
	comicRepo comicRepo.ComicRepository,
) UserComicCollectionService {
	return &userComicCollectionService{
		ExtendedService: base.NewExtendedService[users.UserComicCollection](collectionRepo, "user_comic_collection"),
		collectionRepo:  collectionRepo,
		userRepo:        userRepo,
		comicRepo:       comicRepo,
	}
}
