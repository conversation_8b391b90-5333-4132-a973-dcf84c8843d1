export interface VideoAlbum {
    id: string;
    title: string;
    description: string;
    user_id: string;
    user_nickname: string;
    user_avatar: string;
    cover: string;
    heat: number;
    view_count: number;
    video_count: number;
    category_id: string;
    category_name: string;
    tags?: string[];
    is_featured: number;
    sort_order: number;
    created_at: string;
    updated_at: string;
    status: number;
}

export interface VideoAlbumFormData {
    id?: string;
    title: string;
    description?: string;
    user_id: string;
    user_nickname?: string;
    user_avatar?: string;
    cover?: string;
    category_id?: string;
    category_name?: string;
    tags?: string[];
    is_featured?: number;
    sort_order?: number;
    status?: number;
}

export interface VideoAlbumSearchParams {
    keyword?: string;
    user_id?: string;
    category_id?: string;
    status?: number;
    is_featured?: number;
    sort_by?: string;
}

export interface VideoAlbumListParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: VideoAlbumSearchParams;
}

export interface BatchUpdateStatusParams {
    ids: string[];
    status: number;
} 