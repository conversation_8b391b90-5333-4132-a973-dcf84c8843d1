<template>
  <el-dialog
    title="编辑页面"
    v-model="dialogVisible"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="edit-form">
      <el-form label-width="80px">
        <el-form-item label="页面图片">
          <upload-image
            v-model="formData.image_url"
            :allowMultiple="false"
            @upload-success="handleUploadSuccess"
          />
        </el-form-item>
        <el-form-item label="页码">
          <el-input-number
            v-model="formData.page_number"
            :min="1"
            :max="9999"
            style="width: 120px;"
          />
        </el-form-item>
        <el-form-item label="尺寸" v-if="formData.width && formData.height">
          <span>{{ formData.width }} x {{ formData.height }}</span>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect } from 'vue';
import { ElMessage } from 'element-plus';
import { updateComicsPage } from '@/service/api/comics/comics';
import type { ComicsPage } from '@/types/comics';
import UploadImage from './UploadImage.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  },
  pageMax: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update:visible', 'success']);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

const submitLoading = ref(false);
const formData = ref<any>({});

// 初始化表单数据
watchEffect(() => {
  if (props.editData && Object.keys(props.editData).length > 0) {
    formData.value = { ...props.editData };
  }
});

// 处理上传成功
const handleUploadSuccess = (result: any) => {
  formData.value.width = result.width;
  formData.value.height = result.height;
};

// 提交表单
const handleSubmit = async () => {
  if (!formData.value.image_url) {
    ElMessage.error('请上传图片');
    return;
  }

  try {
    submitLoading.value = true;
    const img = new Image();
    img.src = formData.value.image_url;
    await new Promise(resolve => {
      img.onload = resolve;
      img.onerror = resolve;
    });
    const updateData: any = {
      id: formData.value.id,
      image_url: formData.value.image_url,
      page_number: formData.value.page_number,
      width: img.naturalWidth || 0,
      height: img.naturalHeight || 0,
    };

    const { response, message } = await updateComicsPage(formData.value.id, {
      data: updateData
    }) as any;

    if (response && response.data.code === 2000) {
      ElMessage.success('修改成功');
      emit('success');
      dialogVisible.value = false;
      emit('update:visible', false);
    } else {
      ElMessage.error(response.data.message || '修改失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败');
  } finally {
    submitLoading.value = false;
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
  emit('update:visible', false);
};
</script>

<style scoped>
.edit-form {
  padding: 10px;
}
</style>
