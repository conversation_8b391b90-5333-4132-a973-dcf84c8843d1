package system

import (
	"frontapi/internal/models/system"
	"frontapi/internal/service/base"

	repo "frontapi/internal/repository/tags"
)

// Tag 标签实体

// TagService 标签服务接口
type TagService interface {
	base.IExtendedService[system.Tag]
}

// tagService 标签服务实现
type tagService struct {
	*base.ExtendedService[system.Tag]
	tagRepo repo.TagRepository
}

// NewTagService 创建标签服务实例
func NewTagService(tagRepo repo.TagRepository) TagService {
	return &tagService{
		ExtendedService: base.NewExtendedService[system.Tag](tagRepo, "tag"),
		tagRepo:         tagRepo,
	}
}
