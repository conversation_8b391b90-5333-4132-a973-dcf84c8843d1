package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"frontapi/internal/service/base/extlike/types"
)

// Stats MongoDB统计功能扩展
type Stats struct {
	client *MongoClient
}

// NewStats 创建统计功能实例
func NewStats(client *MongoClient) *Stats {
	return &Stats{
		client: client,
	}
}

// GetServiceMetrics 获取服务指标
func (s *Stats) GetServiceMetrics(ctx context.Context) (*types.ServiceMetrics, error) {
	ctx, cancel := s.client.CreateContext(ctx)
	defer cancel()

	metrics := &types.ServiceMetrics{
		Timestamp: time.Now(),
		Storage:   make(map[string]interface{}),
	}

	// 获取点赞记录总数
	likeCollection := s.client.getLikeCollection()
	totalLikes, err := likeCollection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("获取点赞总数失败: %w", err)
	}
	metrics.TotalLikes = totalLikes

	// 获取活跃点赞数（最近24小时）
	activeFilter := bson.M{
		"timestamp": bson.M{
			"$gte": time.Now().Add(-24 * time.Hour),
		},
	}
	if s.client.config.ConsistencyLevel == "soft" {
		activeFilter["status"] = "liked"
	}

	activeLikes, err := likeCollection.CountDocuments(ctx, activeFilter)
	if err != nil {
		return nil, fmt.Errorf("获取活跃点赞数失败: %w", err)
	}
	metrics.ActiveLikes = activeLikes

	// 获取唯一用户数
	pipeline := []bson.M{
		{
			"$group": bson.M{
				"_id": "$user_id",
			},
		},
		{
			"$count": "unique_users",
		},
	}

	cursor, err := likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("获取唯一用户数失败: %w", err)
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result struct {
			UniqueUsers int64 `bson:"unique_users"`
		}
		if err := cursor.Decode(&result); err == nil {
			metrics.UniqueUsers = result.UniqueUsers
		}
	}

	// 获取唯一项目数
	pipeline = []bson.M{
		{
			"$group": bson.M{
				"_id": bson.M{
					"item_id":   "$item_id",
					"item_type": "$item_type",
				},
			},
		},
		{
			"$count": "unique_items",
		},
	}

	cursor, err = likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("获取唯一项目数失败: %w", err)
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result struct {
			UniqueItems int64 `bson:"unique_items"`
		}
		if err := cursor.Decode(&result); err == nil {
			metrics.UniqueItems = result.UniqueItems
		}
	}

	// 计算平均点赞率
	if metrics.UniqueItems > 0 {
		metrics.AvgLikesPerItem = float64(metrics.TotalLikes) / float64(metrics.UniqueItems)
	}

	if metrics.UniqueUsers > 0 {
		metrics.AvgLikesPerUser = float64(metrics.TotalLikes) / float64(metrics.UniqueUsers)
	}

	// 获取存储信息
	metrics.Storage = s.getStorageStats(ctx)

	return metrics, nil
}

// GetCacheStats 获取缓存统计
func (s *Stats) GetCacheStats(ctx context.Context) (*types.CacheStats, error) {
	// MongoDB没有传统意义上的缓存，这里返回集合的统计信息
	stats := &types.CacheStats{
		Timestamp: time.Now(),
	}

	// 获取各集合的文档数量
	collections := map[string]*types.CollectionStats{
		"likes":      {Name: s.client.config.LikeCollection},
		"stats":      {Name: s.client.config.StatsCollection},
		"trends":     {Name: s.client.config.TrendCollection},
		"rankings":   {Name: s.client.config.RankingCollection},
		"operations": {Name: s.client.config.OperationCollection},
	}

	for key, collStats := range collections {
		collection := s.client.database.Collection(collStats.Name)
		count, err := collection.CountDocuments(ctx, bson.M{})
		if err == nil {
			collStats.DocumentCount = count
		}
		collections[key] = collStats
	}

	stats.Collections = collections
	return stats, nil
}

// GetPerformanceMetrics 获取性能指标
func (s *Stats) GetPerformanceMetrics(ctx context.Context, timeRange *types.TimeRange) (*types.PerformanceMetrics, error) {
	metrics := &types.PerformanceMetrics{
		Timestamp: time.Now(),
	}

	// 如果没有操作记录集合，返回基本指标
	if !s.client.config.EnablePipeline {
		return metrics, nil
	}

	operationCollection := s.client.getOperationCollection()

	// 构建时间过滤器
	filter := bson.M{}
	if timeRange != nil {
		timeFilter := bson.M{}
		if !timeRange.Start.IsZero() {
			timeFilter["$gte"] = timeRange.Start
		}
		if !timeRange.End.IsZero() {
			timeFilter["$lte"] = timeRange.End
		}
		if len(timeFilter) > 0 {
			filter["timestamp"] = timeFilter
		}
	} else {
		// 默认最近1小时
		filter["timestamp"] = bson.M{
			"$gte": time.Now().Add(-time.Hour),
		}
	}

	// 计算操作统计
	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id":               "$action",
				"count":             bson.M{"$sum": 1},
				"avg_response_time": bson.M{"$avg": "$response_time"},
			},
		},
	}

	cursor, err := operationCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("获取性能指标失败: %w", err)
	}
	defer cursor.Close(ctx)

	operationStats := make(map[string]*types.OperationStats)
	for cursor.Next(ctx) {
		var result struct {
			Action          string  `bson:"_id"`
			Count           int64   `bson:"count"`
			AvgResponseTime float64 `bson:"avg_response_time"`
		}

		if err := cursor.Decode(&result); err != nil {
			continue
		}

		operationStats[result.Action] = &types.OperationStats{
			Operation:       result.Action,
			Count:           result.Count,
			AvgResponseTime: time.Duration(result.AvgResponseTime * float64(time.Millisecond)),
		}

		// 累计总操作数
		metrics.TotalOperations += result.Count
	}

	metrics.OperationStats = operationStats

	// 计算错误率（这里简化处理，实际需要记录错误信息）
	if metrics.TotalOperations > 0 {
		metrics.ErrorRate = 0.0 // MongoDB事务保证了数据一致性，错误率较低
	}

	return metrics, nil
}

// GetHourlyStats 获取小时统计
func (s *Stats) GetHourlyStats(ctx context.Context, itemType string, date time.Time) (*types.HourlyStats, error) {
	ctx, cancel := s.client.CreateContext(ctx)
	defer cancel()

	likeCollection := s.client.getLikeCollection()

	// 构建日期过滤器
	startOfDay := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	endOfDay := startOfDay.Add(24 * time.Hour)

	filter := bson.M{
		"timestamp": bson.M{
			"$gte": startOfDay,
			"$lt":  endOfDay,
		},
	}

	if itemType != "" {
		filter["item_type"] = itemType
	}

	if s.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	// 按小时聚合
	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id": bson.M{
					"hour": bson.M{"$hour": "$timestamp"},
				},
				"like_count":   bson.M{"$sum": 1},
				"unique_users": bson.M{"$addToSet": "$user_id"},
				"unique_items": bson.M{"$addToSet": "$item_id"},
			},
		},
		{
			"$project": bson.M{
				"hour":         "$_id.hour",
				"like_count":   1,
				"unique_users": bson.M{"$size": "$unique_users"},
				"unique_items": bson.M{"$size": "$unique_items"},
			},
		},
		{"$sort": bson.M{"hour": 1}},
	}

	cursor, err := likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("获取小时统计失败: %w", err)
	}
	defer cursor.Close(ctx)

	stats := &types.HourlyStats{
		Date:      date,
		ItemType:  itemType,
		HourStats: make([]types.HourStats, 24),
	}

	// 初始化24小时的数据
	for i := 0; i < 24; i++ {
		stats.HourStats[i] = types.HourStats{
			Hour: i,
		}
	}

	// 填充实际数据
	for cursor.Next(ctx) {
		var result struct {
			Hour        int   `bson:"hour"`
			LikeCount   int64 `bson:"like_count"`
			UniqueUsers int64 `bson:"unique_users"`
			UniqueItems int64 `bson:"unique_items"`
		}

		if err := cursor.Decode(&result); err != nil {
			continue
		}

		if result.Hour >= 0 && result.Hour < 24 {
			stats.HourStats[result.Hour].LikeCount = result.LikeCount
			stats.HourStats[result.Hour].UserCount = result.UniqueUsers
			stats.HourStats[result.Hour].ItemCount = result.UniqueItems
		}

		stats.TotalLikes += result.LikeCount
	}

	return stats, nil
}

// GetTopItemsByType 获取按类型分组的热门项目
func (s *Stats) GetTopItemsByType(ctx context.Context, limit int) (map[string][]string, error) {
	ctx, cancel := s.client.CreateContext(ctx)
	defer cancel()

	likeCollection := s.client.getLikeCollection()

	filter := bson.M{}
	if s.client.config.ConsistencyLevel == "soft" {
		filter["status"] = "liked"
	}

	// 按类型和项目聚合
	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id": bson.M{
					"item_type": "$item_type",
					"item_id":   "$item_id",
				},
				"like_count": bson.M{"$sum": 1},
			},
		},
		{
			"$sort": bson.M{
				"_id.item_type": 1,
				"like_count":    -1,
			},
		},
		{
			"$group": bson.M{
				"_id": "$_id.item_type",
				"items": bson.M{
					"$push": bson.M{
						"item_id":    "$_id.item_id",
						"like_count": "$like_count",
					},
				},
			},
		},
		{
			"$project": bson.M{
				"item_type": "$_id",
				"top_items": bson.M{
					"$slice": []interface{}{"$items", limit},
				},
			},
		},
	}

	cursor, err := likeCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("获取热门项目失败: %w", err)
	}
	defer cursor.Close(ctx)

	result := make(map[string][]string)
	for cursor.Next(ctx) {
		var typeResult struct {
			ItemType string `bson:"item_type"`
			TopItems []struct {
				ItemID    string `bson:"item_id"`
				LikeCount int64  `bson:"like_count"`
			} `bson:"top_items"`
		}

		if err := cursor.Decode(&typeResult); err != nil {
			continue
		}

		var itemIDs []string
		for _, item := range typeResult.TopItems {
			itemIDs = append(itemIDs, item.ItemID)
		}

		result[typeResult.ItemType] = itemIDs
	}

	return result, nil
}

// getStorageStats 获取存储统计信息
func (s *Stats) getStorageStats(ctx context.Context) map[string]interface{} {
	stats := make(map[string]interface{})

	// 获取数据库统计
	result := s.client.database.RunCommand(ctx, bson.M{"dbStats": 1})
	var dbStats bson.M
	if err := result.Decode(&dbStats); err == nil {
		stats["database"] = dbStats
	}

	// 获取各集合统计
	collectionNames := []string{
		s.client.config.LikeCollection,
		s.client.config.StatsCollection,
		s.client.config.TrendCollection,
		s.client.config.RankingCollection,
		s.client.config.OperationCollection,
	}

	collections := make(map[string]interface{})
	for _, collName := range collectionNames {
		result := s.client.database.RunCommand(ctx, bson.M{
			"collStats": collName,
		})
		var collStats bson.M
		if err := result.Decode(&collStats); err == nil {
			collections[collName] = collStats
		}
	}
	stats["collections"] = collections

	return stats
}
