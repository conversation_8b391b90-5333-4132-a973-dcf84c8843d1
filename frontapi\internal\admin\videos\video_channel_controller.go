package videos

import (
	"fmt"
	"frontapi/internal/admin"
	videoModel "frontapi/internal/models/videos"
	videoSrv "frontapi/internal/service/videos"
	"frontapi/internal/validation/videos"
	videoValidator "frontapi/internal/validation/videos"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// VideoChannelController 视频频道控制器
type VideoChannelController struct {
	admin.BaseController
	channelService videoSrv.VideoChannelService
}

// NewVideoChannelController 创建视频频道控制器
func NewVideoChannelController(channelService videoSrv.VideoChannelService) *VideoChannelController {
	return &VideoChannelController{
		channelService: channelService,
	}
}

// CreateChannel 创建频道
func (h *VideoChannelController) CreateChannel(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 创建请求结构
	var req videoValidator.CreateChannelRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 创建频道对象并使用SmartCopy进行智能拷贝
	channel := videoModel.VideoChannel{}

	// 使用SmartCopy进行基础字段拷贝
	if err := utils.SmartCopy(&req, &channel); err != nil {
		return h.InternalServerError(c, "数据拷贝失败: "+err.Error())
	}

	// 创建频道
	channelID, err := h.channelService.Create(c.Context(), &channel)
	if err != nil {
		return h.InternalServerError(c, "创建频道失败: "+err.Error())
	}

	// 返回成功响应
	return h.Success(c, fiber.Map{
		"id":      channelID,
		"message": "创建频道成功",
	})
}

// GetChannel 获取频道详情
func (h *VideoChannelController) GetChannel(c *fiber.Ctx) error {
	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "频道ID不能为空", nil)
	}

	// 获取频道详情
	channel, err := h.channelService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "频道不存在")
	}

	return h.Success(c, channel)
}

// ListChannels 获取频道列表
func (h *VideoChannelController) ListChannels(c *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 获取过滤条件
	keyword := reqInfo.Get("keyword").GetString()
	status := -9999 // 默认值，表示获取所有状态

	// 尝试获取状态过滤条件
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return err
	}

	// 获取时间区间查询条件
	createdAtStart := reqInfo.Get("created_at_start").GetString()
	createdAtEnd := reqInfo.Get("created_at_end").GetString()

	condition := map[string]interface{}{
		"keyword": keyword,
		"status":  status,
	}

	// 添加时间区间查询条件
	if createdAtStart != "" {
		condition["created_at_start"] = createdAtStart
	}
	if createdAtEnd != "" {
		condition["created_at_end"] = createdAtEnd
	}

	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "sort_order ASC"
	}
	// 获取频道列表
	channels, total, err := h.channelService.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取频道列表失败")
	}

	// 返回带分页的响应
	return h.SuccessList(c, channels, total, page, pageSize)
}

// UpdateChannel 更新频道
func (h *VideoChannelController) UpdateChannel(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "频道ID不能为空", nil)
	}

	// 创建请求结构
	var req videos.UpdateChannelRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 创建频道对象并使用SmartCopy进行智能拷贝
	channel := videoModel.VideoChannel{}

	// 使用SmartCopy进行基础字段拷贝
	if err := utils.SmartCopy(&req, &channel); err != nil {
		return h.InternalServerError(c, "数据拷贝失败: "+err.Error())
	}

	// 更新频道
	err := h.channelService.UpdateById(c.Context(), id, &channel)
	if err != nil {
		return h.InternalServerError(c, fmt.Sprintf("更新频道失败: %v", err))
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "更新频道成功")
}

// DeleteChannel 删除频道
func (h *VideoChannelController) DeleteChannel(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "频道ID不能为空", nil)
	}

	// 删除频道
	err := h.channelService.Delete(c.Context(), id)
	if err != nil {
		return h.InternalServerError(c, "删除频道失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "删除频道成功")
}

func (h *VideoChannelController) UpdateChannelStatus(c *fiber.Ctx) error {
	// 权限检查
	var req videoValidator.UpdateChannelStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数", nil)
	}
	err := h.channelService.UpdateStatus(c.Context(), req.Id, req.Status)
	if err != nil {
		return h.InternalServerError(c, "更新频道状态失败: "+err.Error())
	}
	return h.SuccessWithMessage(c, "更新频道状态成功")
}

func (h *VideoChannelController) BatchUpdateChannelStatus(c *fiber.Ctx) error {
	var req videoValidator.BatchUpdateChannelStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数", nil)
	}
	err := h.channelService.BatchUpdateStatus(c.Context(), req.Ids, req.Status)
	if err != nil {
		return h.InternalServerError(c, "批量更新频道状态失败")
	}
	return h.SuccessWithMessage(c, "批量更新频道状态成功")
}
