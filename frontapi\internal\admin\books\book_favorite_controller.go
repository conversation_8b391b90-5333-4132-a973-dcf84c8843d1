package books

import (
	"frontapi/internal/admin"
	bookService "frontapi/internal/service/books"
)

// BookFavoriteController 电子书收藏控制器
type BookFavoriteController struct {
	admin.BaseController
	favoriteService bookService.BookFavoriteService
}

// NewBookFavoriteController 创建电子书收藏控制器
func NewBookFavoriteController(favoriteService bookService.BookFavoriteService) *BookFavoriteController {
	return &BookFavoriteController{
		favoriteService: favoriteService,
	}
}
