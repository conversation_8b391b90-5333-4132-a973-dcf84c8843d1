package comics

import (
	"frontapi/internal/admin"
	"frontapi/pkg/validator"

	comicsValidator "frontapi/internal/validation/comics"

	"github.com/gofiber/fiber/v2"

	comicSrv "frontapi/internal/service/comics"
)

// ComicPageController 漫画页面控制器
type ComicPageController struct {
	admin.BaseController
	comicPageService comicSrv.ComicPageService
}

// NewComicPageController 创建漫画页面控制器实例
func NewComicPageController(service comicSrv.ComicPageService) *ComicPageController {
	return &ComicPageController{
		comicPageService: service,
	}
}

// List 获取漫画页面列表
func (c *ComicPageController) List(ctx *fiber.Ctx) error {
	// 获取章节ID
	reqInfo := c.GetRequestInfo(ctx)
	chapterID := reqInfo.Get("chapter_id").GetString()
	comicId := reqInfo.Get("comic_id").GetString()
	//if chapterID == "" {
	//	return c.BadRequest(ctx, "章节ID不能为空", nil)
	//}
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"chapter_id": chapterID,
		"comic_id":   comicId,
	}
	// 获取页面列表
	pages, total, err := c.comicPageService.List(ctx.Context(), condition, "page_number ASC", pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取页面列表失败: "+err.Error())
	}

	return c.SuccessList(ctx, pages, total, pageNo, pageSize)
}

// Create 创建漫画页面
func (c *ComicPageController) Create(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req comicsValidator.CreatePageRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "参数验证失败"+err.Error(), nil)
	}

	// 创建页面
	pageID, err := c.comicPageService.CreatePage(ctx.Context(), &req)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.Success(ctx, map[string]interface{}{"id": pageID})
}

// BatchCreate 批量创建漫画页面
func (c *ComicPageController) BatchCreate(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req comicsValidator.BatchCreatePagesRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "参数验证失败"+err.Error(), nil)
	}
	// 批量创建页面
	if err := c.comicPageService.BatchCreatePages(ctx.Context(), &req); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量创建页面成功")
}

// Update 更新漫画页面
func (c *ComicPageController) Update(ctx *fiber.Ctx) error {
	// 获取页面ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "页面ID不能为空", nil)
	}

	// 解析请求参数
	var req comicsValidator.UpdatePageRequest
	err = c.ParseRequestData(ctx, &req)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}

	// 更新页面
	if err := c.comicPageService.UpdatePage(ctx.Context(), id, &req); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新页面成功")
}

// Delete 删除漫画页面
func (c *ComicPageController) Delete(ctx *fiber.Ctx) error {
	// 获取页面ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "页面ID不能为空", nil)
	}

	// 删除页面
	if err := c.comicPageService.DeletePage(ctx.Context(), id); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除页面成功")
}

// GetByID 获取漫画页面详情
func (c *ComicPageController) GetByID(ctx *fiber.Ctx) error {
	// 获取页面ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "页面ID不能为空", nil)
	}

	// 获取页面详情
	page, err := c.comicPageService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取页面详情失败: "+err.Error())
	}

	return c.Success(ctx, page)
}

// BatchUpdateOrder 批量更新页面顺序
func (c *ComicPageController) BatchUpdateOrder(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req comicsValidator.BatchUpdatePageOrderRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "参数解析失败: "+err.Error(), nil)
	}

	// 批量更新页面顺序
	if err := c.comicPageService.BatchUpdatePageOrder(ctx.Context(), req.ChapterID, req.ComicID, req.Pages); err != nil {
		return c.InternalServerError(ctx, "更新页面顺序失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "页面顺序更新成功")
}
