package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserSearchHistoryRepository 用户搜索历史数据访问接口
type UserSearchHistoryRepository interface {
	base.ExtendedRepository[users.UserSearchHistory]
}

// userSearchHistoryRepository 用户搜索历史数据访问实现
type userSearchHistoryRepository struct {
	base.ExtendedRepository[users.UserSearchHistory]
}

// NewUserSearchHistoryRepository 创建用户搜索历史仓库实例
func NewUserSearchHistoryRepository(db *gorm.DB) UserSearchHistoryRepository {
	return &userSearchHistoryRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserSearchHistory](db),
	}
}
