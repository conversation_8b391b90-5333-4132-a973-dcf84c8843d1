package sharding

import (
	"context"
	"errors"
	"fmt"
	"hash/fnv"
	"sync"
	"time"

	"frontapi/pkg/cache/types"
)

// ShardConfig 分片配置
type ShardConfig struct {
	ShardCount       int           // 分片数量
	ReplicaFactor    int           // 每个分片的副本数
	ShardingStrategy string        // "hash", "range", "consistent_hash"
	ReadStrategy     string        // "primary", "any", "nearest"
	WriteStrategy    string        // "all", "quorum", "primary"
	FailoverEnabled  bool          // 是否启用故障转移
	FailoverDelay    time.Duration // 故障转移延迟
}

// ShardManager 分片管理器
type ShardManager struct {
	config      *ShardConfig
	shards      []Shard
	shardMap    map[int][]types.CacheAdapter // 分片ID -> 适配器列表
	mu          sync.RWMutex
	failoverMap map[string]string // 原始节点ID -> 故障转移节点ID
	stats       *types.CacheStats
}

// Shard 表示一个分片
type Shard struct {
	ID       int
	Primary  string
	Replicas []string
	Status   string // "active", "rebalancing", "recovering"
}

// NewShardManager 创建分片管理器
func NewShardManager(config *ShardConfig) (*ShardManager, error) {
	if config == nil {
		return nil, errors.New("config cannot be nil")
	}

	if config.ShardCount <= 0 {
		return nil, errors.New("shard count must be positive")
	}

	sm := &ShardManager{
		config:      config,
		shards:      make([]Shard, config.ShardCount),
		shardMap:    make(map[int][]types.CacheAdapter),
		failoverMap: make(map[string]string),
		stats: &types.CacheStats{
			StartTime: time.Now(),
		},
	}

	// 初始化分片
	for i := 0; i < config.ShardCount; i++ {
		sm.shards[i] = Shard{
			ID:       i,
			Status:   "active",
			Replicas: make([]string, 0),
		}
		sm.shardMap[i] = make([]types.CacheAdapter, 0)
	}

	return sm, nil
}

// RegisterAdapter 注册缓存适配器到分片
func (sm *ShardManager) RegisterAdapter(shardID int, nodeID string, adapter types.CacheAdapter, isPrimary bool) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if shardID < 0 || shardID >= sm.config.ShardCount {
		return fmt.Errorf("invalid shard ID: %d", shardID)
	}

	// 添加适配器到分片映射
	sm.shardMap[shardID] = append(sm.shardMap[shardID], adapter)

	// 更新分片信息
	if isPrimary {
		sm.shards[shardID].Primary = nodeID
	} else {
		sm.shards[shardID].Replicas = append(sm.shards[shardID].Replicas, nodeID)
	}

	return nil
}

// GetShardForKey 根据键获取分片ID
func (sm *ShardManager) GetShardForKey(key string) int {
	switch sm.config.ShardingStrategy {
	case "range":
		// 范围分片
		return sm.rangeSharding(key)
	case "consistent_hash":
		// 一致性哈希分片
		return sm.consistentSharding(key)
	default:
		// 默认哈希分片
		return sm.hashSharding(key)
	}
}

// hashSharding 哈希分片
func (sm *ShardManager) hashSharding(key string) int {
	h := fnv.New32a()
	h.Write([]byte(key))
	return int(h.Sum32()) % sm.config.ShardCount
}

// rangeSharding 范围分片
func (sm *ShardManager) rangeSharding(key string) int {
	// 简单实现，可以根据需要扩展
	if len(key) == 0 {
		return 0
	}

	// 使用第一个字符作为范围分片的依据
	firstChar := key[0]
	return int(firstChar) % sm.config.ShardCount
}

// consistentSharding 一致性哈希分片
func (sm *ShardManager) consistentSharding(key string) int {
	// 简单实现，实际应用中可以使用一致性哈希算法
	h := fnv.New32a()
	h.Write([]byte(key))
	return int(h.Sum32()) % sm.config.ShardCount
}

// GetAdaptersForKey 根据键获取适配器列表
func (sm *ShardManager) GetAdaptersForKey(key string) ([]types.CacheAdapter, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	shardID := sm.GetShardForKey(key)
	adapters, exists := sm.shardMap[shardID]

	if !exists || len(adapters) == 0 {
		return nil, fmt.Errorf("no adapters available for shard %d", shardID)
	}

	return adapters, nil
}

// GetPrimaryAdapterForKey 获取键的主要适配器
func (sm *ShardManager) GetPrimaryAdapterForKey(key string) (types.CacheAdapter, error) {
	adapters, err := sm.GetAdaptersForKey(key)
	if err != nil {
		return nil, err
	}

	// 主要适配器通常是第一个
	if len(adapters) > 0 {
		return adapters[0], nil
	}

	return nil, errors.New("no primary adapter available")
}

// Set 设置缓存值（分片）
func (sm *ShardManager) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	switch sm.config.WriteStrategy {
	case "all":
		return sm.setToAll(ctx, key, value, expiration)
	case "quorum":
		return sm.setToQuorum(ctx, key, value, expiration)
	default:
		// 默认写入主分片
		adapter, err := sm.GetPrimaryAdapterForKey(key)
		if err != nil {
			return err
		}
		err = adapter.Set(ctx, key, value, expiration)
		if err == nil {
			sm.stats.Sets++
		}
		return err
	}
}

// setToAll 写入所有副本
func (sm *ShardManager) setToAll(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	adapters, err := sm.GetAdaptersForKey(key)
	if err != nil {
		return err
	}

	var firstError error

	// 写入所有适配器
	for _, adapter := range adapters {
		if err := adapter.Set(ctx, key, value, expiration); err != nil {
			if firstError == nil {
				firstError = err
			}
		}
	}

	if firstError == nil {
		sm.stats.Sets++
	}
	return firstError
}

// setToQuorum 写入大多数副本
func (sm *ShardManager) setToQuorum(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	adapters, err := sm.GetAdaptersForKey(key)
	if err != nil {
		return err
	}

	successCount := 0
	quorum := (len(adapters) / 2) + 1
	var lastError error

	// 写入适配器，直到达到法定数量
	for _, adapter := range adapters {
		if err := adapter.Set(ctx, key, value, expiration); err != nil {
			lastError = err
		} else {
			successCount++
			if successCount >= quorum {
				sm.stats.Sets++
				return nil
			}
		}
	}

	if successCount < quorum {
		if lastError != nil {
			return fmt.Errorf("failed to achieve quorum write: %w", lastError)
		}
		return fmt.Errorf("failed to achieve quorum write")
	}

	return nil
}

// Get 获取缓存值（分片）
func (sm *ShardManager) Get(ctx context.Context, key string) ([]byte, error) {
	switch sm.config.ReadStrategy {
	case "any":
		return sm.getFromAny(ctx, key)
	case "nearest":
		return sm.getFromNearest(ctx, key)
	default:
		// 默认从主分片读取
		adapter, err := sm.GetPrimaryAdapterForKey(key)
		if err != nil {
			return nil, err
		}
		value, err := adapter.Get(ctx, key)
		if err != nil {
			if err == types.ErrNotFound {
				sm.stats.Misses++
			}
			return nil, err
		}
		sm.stats.Hits++
		return value, nil
	}
}

// getFromAny 从任意可用副本读取
func (sm *ShardManager) getFromAny(ctx context.Context, key string) ([]byte, error) {
	adapters, err := sm.GetAdaptersForKey(key)
	if err != nil {
		return nil, err
	}

	var lastError error
	for _, adapter := range adapters {
		value, err := adapter.Get(ctx, key)
		if err == nil {
			sm.stats.Hits++
			return value, nil
		}
		if err != types.ErrNotFound {
			lastError = err
		} else {
			sm.stats.Misses++
		}
	}

	if lastError != nil {
		return nil, lastError
	}
	return nil, types.ErrNotFound
}

// getFromNearest 从最近的副本读取（简单实现）
func (sm *ShardManager) getFromNearest(ctx context.Context, key string) ([]byte, error) {
	// 简单实现，直接使用getFromAny
	return sm.getFromAny(ctx, key)
}

// Delete 删除缓存值
func (sm *ShardManager) Delete(ctx context.Context, key string) error {
	adapters, err := sm.GetAdaptersForKey(key)
	if err != nil {
		return err
	}

	var lastError error
	for _, adapter := range adapters {
		if err := adapter.Delete(ctx, key); err != nil {
			lastError = err
		}
	}

	if lastError == nil {
		sm.stats.Deletes++
	}
	return lastError
}

// Exists 检查键是否存在
func (sm *ShardManager) Exists(ctx context.Context, key string) (bool, error) {
	adapter, err := sm.GetPrimaryAdapterForKey(key)
	if err != nil {
		return false, err
	}
	return adapter.Exists(ctx, key)
}

// Rebalance 重新平衡分片
func (sm *ShardManager) Rebalance() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// 这里应该实现分片重新平衡的逻辑
	// 简单实现，仅标记分片状态为重新平衡
	for i := range sm.shards {
		sm.shards[i].Status = "rebalancing"
	}

	return nil
}

// AddShard 添加分片
func (sm *ShardManager) AddShard() (int, error) {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	// 创建新分片
	newShardID := len(sm.shards)
	newShard := Shard{
		ID:       newShardID,
		Status:   "active",
		Replicas: make([]string, 0),
	}

	// 添加到分片列表
	sm.shards = append(sm.shards, newShard)
	sm.shardMap[newShardID] = make([]types.CacheAdapter, 0)

	// 更新分片数量
	sm.config.ShardCount++

	return newShardID, nil
}

// RemoveShard 移除分片
func (sm *ShardManager) RemoveShard(shardID int) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	if shardID < 0 || shardID >= len(sm.shards) {
		return fmt.Errorf("invalid shard ID: %d", shardID)
	}

	// 关闭分片中的所有适配器
	for _, adapter := range sm.shardMap[shardID] {
		if err := adapter.Close(); err != nil {
			return err
		}
	}

	// 移除分片
	// 注意：这里简单实现，实际应用中需要处理数据迁移
	sm.shards = append(sm.shards[:shardID], sm.shards[shardID+1:]...)
	delete(sm.shardMap, shardID)

	// 更新分片ID
	for i := shardID; i < len(sm.shards); i++ {
		sm.shards[i].ID = i
		sm.shardMap[i] = sm.shardMap[i+1]
		delete(sm.shardMap, i+1)
	}

	// 更新分片数量
	sm.config.ShardCount--

	return nil
}

// GetShardStats 获取分片统计信息
func (sm *ShardManager) GetShardStats() map[int]map[string]interface{} {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	stats := make(map[int]map[string]interface{})

	for i, shard := range sm.shards {
		shardStats := make(map[string]interface{})
		shardStats["id"] = shard.ID
		shardStats["status"] = shard.Status
		shardStats["primary"] = shard.Primary
		shardStats["replicas"] = shard.Replicas

		// 计算适配器统计信息
		if adapters, exists := sm.shardMap[i]; exists {
			var hits, misses, sets, deletes int64
			for _, adapter := range adapters {
				adapterStats := adapter.Stats()
				hits += adapterStats.Hits
				misses += adapterStats.Misses
				sets += adapterStats.Sets
				deletes += adapterStats.Deletes
			}
			shardStats["hits"] = hits
			shardStats["misses"] = misses
			shardStats["sets"] = sets
			shardStats["deletes"] = deletes
		}

		stats[i] = shardStats
	}

	return stats
}

// Close 关闭所有分片
func (sm *ShardManager) Close() error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	var lastError error
	for _, adapters := range sm.shardMap {
		for _, adapter := range adapters {
			if err := adapter.Close(); err != nil {
				lastError = err
			}
		}
	}

	return lastError
}

// Stats 获取统计信息
func (sm *ShardManager) Stats() *types.CacheStats {
	sm.stats.Uptime = time.Since(sm.stats.StartTime)
	return sm.stats
}

// MGet 批量获取缓存值
func (sm *ShardManager) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	if len(keys) == 0 {
		return make(map[string][]byte), nil
	}

	// 按分片对键进行分组
	shardKeys := make(map[int][]string)
	for _, key := range keys {
		shardID := sm.GetShardForKey(key)
		shardKeys[shardID] = append(shardKeys[shardID], key)
	}

	// 从各分片获取数据
	result := make(map[string][]byte)
	for _, keyList := range shardKeys {
		// 使用第一个键获取适配器
		adapters, err := sm.GetAdaptersForKey(keyList[0])
		if err != nil {
			continue
		}

		// 使用主适配器获取数据
		if len(adapters) > 0 {
			shardResult, err := adapters[0].MGet(ctx, keyList)
			if err != nil {
				continue
			}
			// 合并结果
			for k, v := range shardResult {
				result[k] = v
			}
		}
	}

	return result, nil
}

// MSet 批量设置缓存值
func (sm *ShardManager) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	if len(items) == 0 {
		return nil
	}

	// 按分片对键值对进行分组
	shardItems := make(map[int]map[string][]byte)
	for key, value := range items {
		shardID := sm.GetShardForKey(key)
		if _, exists := shardItems[shardID]; !exists {
			shardItems[shardID] = make(map[string][]byte)
		}
		shardItems[shardID][key] = value
	}

	// 向各分片设置数据
	for _, itemMap := range shardItems {
		// 使用第一个键获取适配器
		firstKey := getFirstKey(itemMap)
		adapters, err := sm.GetAdaptersForKey(firstKey)
		if err != nil {
			return err
		}

		// 根据写入策略设置数据
		switch sm.config.WriteStrategy {
		case "all":
			for _, adapter := range adapters {
				if err := adapter.MSet(ctx, itemMap, expiration); err != nil {
					return err
				}
			}
		case "quorum":
			successCount := 0
			quorum := (len(adapters) / 2) + 1
			var lastError error

			for _, adapter := range adapters {
				if err := adapter.MSet(ctx, itemMap, expiration); err != nil {
					lastError = err
				} else {
					successCount++
					if successCount >= quorum {
						break
					}
				}
			}

			if successCount < quorum {
				if lastError != nil {
					return fmt.Errorf("failed to achieve quorum write: %w", lastError)
				}
				return fmt.Errorf("failed to achieve quorum write")
			}
		default:
			// 默认写入主适配器
			if len(adapters) > 0 {
				if err := adapters[0].MSet(ctx, itemMap, expiration); err != nil {
					return err
				}
			}
		}
	}

	sm.stats.Sets += int64(len(items))
	return nil
}

// 获取map中的第一个键
func getFirstKey(m map[string][]byte) string {
	for k := range m {
		return k
	}
	return ""
}

// Increment 增加计数器值
func (sm *ShardManager) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	adapter, err := sm.GetPrimaryAdapterForKey(key)
	if err != nil {
		return 0, err
	}
	return adapter.Increment(ctx, key, delta)
}

// Decrement 减少计数器值
func (sm *ShardManager) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	adapter, err := sm.GetPrimaryAdapterForKey(key)
	if err != nil {
		return 0, err
	}
	return adapter.Decrement(ctx, key, delta)
}

// Expire 设置过期时间
func (sm *ShardManager) Expire(ctx context.Context, key string, expiration time.Duration) error {
	adapter, err := sm.GetPrimaryAdapterForKey(key)
	if err != nil {
		return err
	}
	return adapter.Expire(ctx, key, expiration)
}

// TTL 获取剩余过期时间
func (sm *ShardManager) TTL(ctx context.Context, key string) (time.Duration, error) {
	adapter, err := sm.GetPrimaryAdapterForKey(key)
	if err != nil {
		return 0, err
	}
	return adapter.TTL(ctx, key)
}

// Clear 清空所有分片
func (sm *ShardManager) Clear(ctx context.Context) error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	for _, adapters := range sm.shardMap {
		for _, adapter := range adapters {
			if err := adapter.Clear(ctx); err != nil {
				return err
			}
		}
	}

	sm.stats.Clears++
	return nil
}

// Ping 测试所有分片连接
func (sm *ShardManager) Ping(ctx context.Context) error {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	for _, adapters := range sm.shardMap {
		for _, adapter := range adapters {
			if err := adapter.Ping(ctx); err != nil {
				return err
			}
		}
	}

	return nil
}

// Name 获取适配器名称
func (sm *ShardManager) Name() string {
	return "shard_manager"
}

// Type 获取适配器类型
func (sm *ShardManager) Type() string {
	return "shard_manager"
}
