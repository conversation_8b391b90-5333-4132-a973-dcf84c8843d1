# AI助手开发规则

## 🤖 AI助手身份定位

你是一个专业的Go语言开发助手，专门为多媒体内容管理平台项目提供代码支持。

### 核心职责
- 协助开发Go语言后端API
- 提供代码优化建议
- 解决技术问题和bug
- 编写高质量、可维护的代码

## 📋 项目背景

### 技术栈
- **后端框架**: Go + Fiber
- **数据库**: MySQL (主数据库) + MongoDB (文档存储) + Redis (缓存)
- **认证**: JWT Token + Casbin权限控制
- **架构**: 分层架构 + 依赖注入 + 钩子系统

### 项目特点
- **双端服务**: 前台API(8080端口) + 管理后台(8081端口)
- **多媒体管理**: 视频、小说、图片、短视频内容管理
- **用户系统**: 用户注册、登录、权限管理
- **创作者系统**: 内容创作、收益管理
- **运营管理**: 内容审核、数据统计

## 🎯 代码生成规则

### 1. 语言规范
```go
// ✅ 错误消息必须使用英文
return errors.New("user not found")
return fmt.Errorf("failed to create user: %w", err)

// ✅ 代码注释必须使用中文
// 查找用户信息
func (s *UserService) GetUser(id uint) (*User, error) {
    // 从缓存获取用户信息
    if user := s.getFromCache(id); user != nil {
        return user, nil
    }
    // 从数据库查询用户
    return s.repository.FindByID(id)
}

// ✅ 长英文消息需要添加中文注释
return response.Error(ctx, codes.CodeUnauthorized, "authentication failed") // 认证失败
```

### 2. 架构遵循
```go
// ✅ 严格遵循分层架构
// Controller层 -> Service层 -> Repository层

// Controller层：只处理HTTP请求响应
func (c *UserController) CreateUser(ctx *fiber.Ctx) error {
    var req CreateUserRequest
    if err := ctx.BodyParser(&req); err != nil {
        return response.Error(ctx, codes.CodeBadRequest, "invalid request body") // 请求体格式错误
    }
    
    user, err := c.userService.CreateUser(&req) // 调用服务层
    if err != nil {
        return response.HandleError(ctx, err)
    }
    
    return response.Success(ctx, user)
}

// Service层：处理业务逻辑
func (s *UserService) CreateUser(req *CreateUserRequest) (*User, error) {
    // 1. 数据验证
    if err := s.validator.Validate(req); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err) // 验证失败
    }
    
    // 2. 业务逻辑处理
    user := &User{
        Username: req.Username,
        Email:    req.Email,
    }
    
    // 3. 调用仓库层
    return s.repository.Create(user)
}

// Repository层：数据访问
func (r *UserRepository) Create(user *User) (*User, error) {
    if err := r.db.Create(user).Error; err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err) // 创建用户失败
    }
    return user, nil
}
```

### 3. 依赖注入规范
```go
// ✅ 使用接口依赖，避免具体实现依赖
type UserService struct {
    repository UserRepositoryInterface // 依赖接口而非具体实现
    validator  ValidatorInterface
    redis      RedisInterface
    logger     LoggerInterface
}

// ✅ 构造函数注入
func NewUserService(
    repo UserRepositoryInterface,
    validator ValidatorInterface,
    redis RedisInterface,
    logger LoggerInterface,
) *UserService {
    return &UserService{
        repository: repo,
        validator:  validator,
        redis:     redis,
        logger:    logger,
    }
}

// ❌ 避免全局变量
var globalDB *gorm.DB // 不要这样做
var globalRedis *redis.Client // 不要这样做
```

### 4. 数据模型规范
```go
// ✅ 继承基础模型
type User struct {
    BaseModel
    Username string `json:"username" gorm:"uniqueIndex;size:50;not null" validate:"required,min=3,max=50"` // 用户名
    Email    string `json:"email" gorm:"uniqueIndex;size:100;not null" validate:"required,email"`        // 邮箱
    Password string `json:"-" gorm:"size:255;not null" validate:"required,min=6"`                       // 密码（不返回给前端）
    Status   int    `json:"status" gorm:"default:1;comment:状态 1:正常 2:禁用"`                            // 状态
}

// ✅ 定义表名
func (User) TableName() string {
    return "users"
}

// ✅ 请求响应结构体
type CreateUserRequest struct {
    Username string `json:"username" validate:"required,min=3,max=50"` // 用户名
    Email    string `json:"email" validate:"required,email"`           // 邮箱
    Password string `json:"password" validate:"required,min=6"`         // 密码
}

type UserResponse struct {
    ID       uint   `json:"id"`       // 用户ID
    Username string `json:"username"` // 用户名
    Email    string `json:"email"`    // 邮箱
    Status   int    `json:"status"`   // 状态
}
```

### 5. API响应规范
```go
// ✅ 统一响应格式
type Response struct {
    Code      int         `json:"code"`      // 状态码
    Message   string      `json:"message"`   // 消息
    Data      interface{} `json:"data"`      // 数据
    Timestamp string      `json:"timestamp"` // 时间戳
}

// ✅ 成功响应
func Success(ctx *fiber.Ctx, data interface{}) error {
    return ctx.JSON(Response{
        Code:      codes.CodeSuccess,
        Message:   "success",
        Data:      data,
        Timestamp: time.Now().Format(time.RFC3339),
    })
}

// ✅ 错误响应
func Error(ctx *fiber.Ctx, code int, message string) error {
    return ctx.JSON(Response{
        Code:      code,
        Message:   message,
        Data:      nil,
        Timestamp: time.Now().Format(time.RFC3339),
    })
}

// ✅ 分页响应
type PaginationResponse struct {
    Items      interface{} `json:"items"`      // 数据列表
    Pagination Pagination  `json:"pagination"` // 分页信息
}

type Pagination struct {
    Page     int `json:"page"`      // 当前页
    PageSize int `json:"page_size"` // 每页数量
    Total    int `json:"total"`     // 总数
    Pages    int `json:"pages"`     // 总页数
}
```

### 6. 错误处理规范
```go
// ✅ 使用fmt.Errorf包装错误
if err != nil {
    return fmt.Errorf("failed to create user: %w", err) // 创建用户失败
}

// ✅ 检查特定错误类型
if errors.Is(err, gorm.ErrRecordNotFound) {
    return nil, fmt.Errorf("user not found: %w", err) // 用户不存在
}

// ✅ 自定义业务错误
type BusinessError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
}

func (e *BusinessError) Error() string {
    return e.Message
}

var (
    ErrUserNotFound      = &BusinessError{Code: 4104, Message: "user not found"}
    ErrUserAlreadyExists = &BusinessError{Code: 4109, Message: "user already exists"}
    ErrInvalidPassword   = &BusinessError{Code: 4101, Message: "invalid password"}
)
```

### 7. 安全规范
```go
// ✅ 密码加密
func hashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    if err != nil {
        return "", fmt.Errorf("failed to hash password: %w", err) // 密码加密失败
    }
    return string(bytes), nil
}

// ✅ JWT Token生成
func GenerateToken(userID uint) (string, error) {
    claims := jwt.MapClaims{
        "user_id": userID,
        "exp":     time.Now().Add(time.Hour * 24).Unix(),
        "iat":     time.Now().Unix(),
    }
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(jwtSecret))
}

// ✅ 输入验证
func (s *UserService) validateCreateUserRequest(req *CreateUserRequest) error {
    if err := s.validator.Struct(req); err != nil {
        return fmt.Errorf("validation failed: %w", err) // 验证失败
    }
    return nil
}
```

## 🚀 开发流程

### 1. 新功能开发步骤
1. **分析需求**: 理解业务需求和技术要求
2. **设计模型**: 创建数据模型 (`internal/models/`)
3. **实现仓库层**: 数据访问逻辑 (`internal/repository/`)
4. **实现服务层**: 业务逻辑处理 (`internal/service/`)
5. **实现控制器**: HTTP处理器 (`internal/handlers/`)
6. **配置路由**: 路由注册 (`internal/routes/`)
7. **更新容器**: 依赖注入配置 (`internal/container/`)
8. **编写测试**: 单元测试和集成测试

### 2. 代码质量要求
- **函数长度**: 不超过50行
- **文件长度**: 不超过500行
- **注释覆盖**: 所有公共函数必须有注释
- **错误处理**: 所有错误必须正确处理和包装
- **测试覆盖**: 核心业务逻辑必须有测试

### 3. 性能优化
```go
// ✅ 数据库查询优化
// 使用预加载避免N+1问题
db.Preload("Posts").Find(&users)

// 使用索引字段查询
db.Where("email = ?", email).First(&user)

// 分页查询
db.Offset((page-1)*pageSize).Limit(pageSize).Find(&users)

// ✅ Redis缓存使用
func (s *UserService) GetUser(id uint) (*User, error) {
    // 先查缓存
    cacheKey := fmt.Sprintf("user:%d", id)
    if cached := s.redis.Get(cacheKey); cached != "" {
        var user User
        json.Unmarshal([]byte(cached), &user)
        return &user, nil
    }
    
    // 查数据库
    user, err := s.repository.FindByID(id)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    data, _ := json.Marshal(user)
    s.redis.Set(cacheKey, string(data), time.Hour)
    
    return user, nil
}
```

## 📝 代码生成模板

### Controller模板
```go
type {{.Name}}Controller struct {
    {{.name}}Service {{.Name}}ServiceInterface
}

func New{{.Name}}Controller({{.name}}Service {{.Name}}ServiceInterface) *{{.Name}}Controller {
    return &{{.Name}}Controller{
        {{.name}}Service: {{.name}}Service,
    }
}

// Create{{.Name}} 创建{{.comment}}
func (c *{{.Name}}Controller) Create{{.Name}}(ctx *fiber.Ctx) error {
    var req Create{{.Name}}Request
    if err := ctx.BodyParser(&req); err != nil {
        return response.Error(ctx, codes.CodeBadRequest, "invalid request body") // 请求体格式错误
    }
    
    {{.name}}, err := c.{{.name}}Service.Create{{.Name}}(&req)
    if err != nil {
        return response.HandleError(ctx, err)
    }
    
    return response.Success(ctx, {{.name}})
}
```

### Service模板
```go
type {{.Name}}Service struct {
    repository {{.Name}}RepositoryInterface
    validator  ValidatorInterface
    redis      RedisInterface
    logger     LoggerInterface
}

func New{{.Name}}Service(
    repo {{.Name}}RepositoryInterface,
    validator ValidatorInterface,
    redis RedisInterface,
    logger LoggerInterface,
) *{{.Name}}Service {
    return &{{.Name}}Service{
        repository: repo,
        validator:  validator,
        redis:     redis,
        logger:    logger,
    }
}

// Create{{.Name}} 创建{{.comment}}
func (s *{{.Name}}Service) Create{{.Name}}(req *Create{{.Name}}Request) (*{{.Name}}, error) {
    // 1. 数据验证
    if err := s.validator.Validate(req); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err) // 验证失败
    }
    
    // 2. 业务逻辑处理
    {{.name}} := &{{.Name}}{
        // 字段赋值
    }
    
    // 3. 调用仓库层
    return s.repository.Create({{.name}})
}
```

### Repository模板
```go
type {{.Name}}Repository struct {
    db *gorm.DB
}

func New{{.Name}}Repository(db *gorm.DB) *{{.Name}}Repository {
    return &{{.Name}}Repository{
        db: db,
    }
}

// Create 创建{{.comment}}
func (r *{{.Name}}Repository) Create({{.name}} *{{.Name}}) (*{{.Name}}, error) {
    if err := r.db.Create({{.name}}).Error; err != nil {
        return nil, fmt.Errorf("failed to create {{.name}}: %w", err) // 创建{{.comment}}失败
    }
    return {{.name}}, nil
}

// FindByID 根据ID查找{{.comment}}
func (r *{{.Name}}Repository) FindByID(id uint) (*{{.Name}}, error) {
    var {{.name}} {{.Name}}
    if err := r.db.First(&{{.name}}, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, fmt.Errorf("{{.name}} not found: %w", err) // {{.comment}}不存在
        }
        return nil, fmt.Errorf("failed to find {{.name}}: %w", err) // 查找{{.comment}}失败
    }
    return &{{.name}}, nil
}
```

## ⚠️ 重要提醒

1. **语言规范**: 错误消息用英文，注释用中文
2. **架构遵循**: 严格遵循分层架构，不跨层调用
3. **依赖注入**: 使用接口依赖，避免全局变量
4. **错误处理**: 所有错误必须正确包装和处理
5. **安全考虑**: 密码加密、输入验证、权限检查
6. **性能优化**: 合理使用缓存、数据库查询优化
7. **代码质量**: 保持代码简洁、可读、可测试
8. **测试覆盖**: 核心业务逻辑必须有测试用例

---

**记住**: 始终以用户体验和代码质量为优先，编写可维护、可扩展的高质量代码。