package system

// Tag 标签实体

// CreateTagRequest 创建标签请求
type CreateTagRequest struct {
	Name      string `json:"name" validate:"required|minLen:2|maxLen:50" message:"required:{field} is required" label:"用户名称"`
	Type      int8   `json:"type" validate:"required|int|min:0|max:3"`
	SortOrder int    `json:"sort_order" validate:"int|min:0"`
}

// UpdateTagRequest 更新标签请求
type UpdateTagRequest struct {
	ID        string `json:"id" validate:"required"`
	Name      string `json:"name" validate:"required|minLen:2|maxLen:50" message:"required:{field} is required" label:"用户名称"`
	Type      int8   `json:"type" validate:"required|int|min:0|max:3"`
	SortOrder int    `json:"sort_order" validate:"int|min:0"`
}

// UpdateTagStatusRequest 更新标签状态请求
type UpdateTagStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status" validate:"required,oneof=0 1"`
}
