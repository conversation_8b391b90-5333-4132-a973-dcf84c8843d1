<template>
  <el-form :model="registerForm" :rules="rules" ref="registerFormRef" class="register-form">
    <el-form-item prop="username">
      <el-input
        v-model="registerForm.username"
        placeholder="请输入用户名"
        :prefix-icon="User"
        @focus="activeInput = 'username'"
        @blur="activeInput = ''"
        :class="{ 'is-active': activeInput === 'username' }"
      />
    </el-form-item>
    <el-form-item prop="email">
      <el-input
        v-model="registerForm.email"
        placeholder="请输入邮箱"
        :prefix-icon="Message"
        @focus="activeInput = 'email'"
        @blur="activeInput = ''"
        :class="{ 'is-active': activeInput === 'email' }"
      />
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        v-model="registerForm.password"
        type="password"
        placeholder="请输入密码"
        :prefix-icon="Lock"
        show-password
        @focus="activeInput = 'password'"
        @blur="activeInput = ''"
        :class="{ 'is-active': activeInput === 'password' }"
      />
    </el-form-item>
    <el-form-item prop="confirmPassword">
      <el-input
        v-model="registerForm.confirmPassword"
        type="password"
        placeholder="请确认密码"
        :prefix-icon="Lock"
        show-password
        @focus="activeInput = 'confirmPassword'"
        @blur="activeInput = ''"
        :class="{ 'is-active': activeInput === 'confirmPassword' }"
      />
    </el-form-item>
    <div class="captcha-container">
      <el-form-item prop="captcha">
        <el-input
          v-model="registerForm.captcha"
          placeholder="请输入验证码"
          :prefix-icon="Key"
          @focus="activeInput = 'captcha'"
          @blur="activeInput = ''"
          :class="{ 'is-active': activeInput === 'captcha' }"
        />
      </el-form-item>
      <div class="captcha-image" @click="refreshCaptcha">
        <img :src="captchaUrl" alt="验证码" />
      </div>
    </div>
    <el-form-item prop="agreement">
      <el-checkbox v-model="registerForm.agreement">我已阅读并同意<el-link type="primary" underline="never">用户协议</el-link></el-checkbox>
    </el-form-item>
    <el-button type="primary" class="submit-button" @click="handleSubmit" :loading="loading">
      立即注册
    </el-button>
    <div class="login-link">
      已有账号？<el-link type="primary" underline="never" @click="$emit('switch-component', 'login')">立即登录</el-link>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { User, Message, Lock, Key } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const emit = defineEmits<{
  (e: 'switch-component', component: string): void
}>()

const registerFormRef = ref()
const loading = ref(false)
const activeInput = ref('')
const captchaUrl = ref('/api/captcha')

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  captcha: '',
  agreement: false
})

const validatePass = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (registerForm.confirmPassword !== '') {
      registerFormRef.value?.validateField('confirmPassword')
    }
    callback()
  }
}

const validatePass2 = (rule: any, value: string, callback: Function) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, validator: validatePass, trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validatePass2, trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 4, message: '验证码长度为4位', trigger: 'blur' }
  ],
  agreement: [
    { type: 'boolean', message: '请同意用户协议', trigger: 'change' },
    { validator: (rule: any, value: boolean, callback: Function) => {
      if (!value) {
        callback(new Error('请同意用户协议'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ]
}

const refreshCaptcha = () => {
  captchaUrl.value = `/api/captcha?t=${new Date().getTime()}`
}

const handleSubmit = async () => {
  if (!registerFormRef.value) return
  
  await registerFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        // TODO: 调用注册API
        // const res = await api.register(registerForm)
        ElMessage.success('注册成功')
        emit('switch-component', 'login')
      } catch (error: any) {
        ElMessage.error(error.message || '注册失败')
        refreshCaptcha()
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.register-form {
  :deep(.el-input) {
    .el-input__wrapper {
      background: #f5f7fa;
      border-radius: 8px;
      padding: 8px 15px;
      box-shadow: none;
      transition: all 0.3s ease;

      &.is-focus,
      &:hover {
        background: #fff;
        box-shadow: 0 0 0 1px var(--el-color-primary) !important;
      }

      .el-input__inner {
        height: 36px;
        font-size: 14px;
      }
    }
  }

  .captcha-container {
    display: flex;
    gap: 12px;

    .el-form-item {
      flex: 1;
      margin-bottom: 0;
    }

    .captcha-image {
      width: 100px;
      height: 36px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .submit-button {
    width: 100%;
    padding: 12px 20px;
    margin-top: 20px;
    font-size: 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border: none;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .login-link {
    text-align: center;
    margin-top: 16px;
  }
}

:deep([data-theme='dark']) {
  .register-form {
    :deep(.el-input) {
      .el-input__wrapper {
        background: #2c2c2c;
        
        &.is-focus,
        &:hover {
          background: #363636;
        }
      }
    }
  }
}
</style>