import { corePost, corePostPageList } from '@/shared/composables';

// ==================== 帖子评论操作相关API ====================

/**
 * 帖子评论操作相关API
 * 提供帖子评论的增删改查、点赞等功能
 * 注意：此API专用于帖子评论，视频评论和短视频评论请使用对应的API
 */

/**
 * 获取评论列表
 * @param params 查询参数
 * @param params.data.postId 帖子ID
 * @param params.page 分页参数
 */
export const getComments = (params: any) => {
    return corePostPageList('/community/post/comments/getPostCommentList', params)
}

/**
 * 创建评论
 * @param params 评论参数
 * @param params.data.postId 帖子ID
 * @param params.data.content 评论内容
 * @param params.data.parentId 父评论ID（可选，用于回复）
 */
export const createComment = (params: { data: { postId: string; content: string; parentId?: string } }) => {
    return corePost('/community/post/comments/addComment', params)
}

/**
 * 删除评论
 * @param params 删除参数
 * @param params.data.commentId 评论ID
 */
export const deleteComment = (params: { data: { commentId: string } }) => {
    return corePost('/community/post/comments/deleteComment', params)
}

/**
 * 点赞/取消点赞评论
 * @param params 点赞参数
 * @param params.data.commentId 评论ID
 */
export const toggleCommentLike = (params: { data: { commentId: string } }) => {
    return corePost('/community/post/comments/toggleLike', params)
}

/**
 * 回复评论
 * @param params 回复参数
 * @param params.data.parentId 父评论ID
 * @param params.data.content 回复内容
 * @param params.data.postId 帖子ID
 */
export const replyComment = (params: { data: { parentId: string; content: string; postId: string } }) => {
    return corePost('/community/post/comments/replyComment', params)
}

/**
 * 获取评论详情
 * @param params 查询参数
 * @param params.data.commentId 评论ID
 */
export const getCommentDetail = (params: { data: { commentId: string } }) => {
    return corePost('/community/post/comments/getCommentDetail', params)
}

/**
 * 获取评论回复列表
 * @param params 查询参数
 * @param params.data.commentId 评论ID
 * @param params.page 分页参数
 */
export const getCommentReplies = (params: any) => {
    return corePostPageList('/community/post/comments/getReplies', params)
}