package common

import (
	"context"
	"time"

	"gorm.io/gorm"
)

// AuditHook 审计钩子
type AuditHook struct {
	DB        *gorm.DB
	TableName string
	UserID    string // 当前用户ID
	Action    string // 操作类型
}

// Execute 执行审计记录
func (h *AuditHook) Execute(ctx context.Context, data interface{}) error {
	if h.DB == nil {
		return nil // 审计失败不影响主操作
	}

	// 创建审计记录
	auditRecord := map[string]interface{}{
		"user_id":    h.UserID,
		"action":     h.Action,
		"table_name": h.TableName,
		"data":       data,
		"created_at": time.Now(),
	}

	// 插入审计记录（忽略错误，不影响主操作）
	h.DB.WithContext(ctx).Table("audit_logs").Create(auditRecord)
	return nil
}

// NewAuditHook 创建审计钩子
func NewAuditHook(db *gorm.DB, tableName, userID, action string) *AuditHook {
	return &AuditHook{
		DB:        db,
		TableName: tableName,
		UserID:    userID,
		Action:    action,
	}
}