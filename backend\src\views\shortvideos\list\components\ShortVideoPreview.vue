<template>
  <el-dialog
    v-model="dialogVisible"
    title="短视频预览"
    width="640px"
    :destroy-on-close="true"
  >
    <div v-if="shortvideo" class="video-preview">
      <video
        controls
        :src="shortvideo.url"
        style="width: 100%; max-height: 70vh;"
      ></video>
      <div class="mt-4">
        <h3 class="text-lg font-medium">{{ shortvideo.title }}</h3>
        <el-divider></el-divider>
        <p class="mt-2 text-sm text-gray-600">描述:&nbsp; &nbsp;{{ shortvideo.description }}</p>
        <div class="mt-2 flex flex-wrap gap-1">
          <el-tag size="small" v-if="shortvideo.category_name">{{ shortvideo.category_name }}</el-tag>
          <el-tag v-for="(tag, index) in shortvideo.tags" :key="index" size="small" type="info" v-if="shortvideo.tags&&shortvideo.tags.length>0">
            <label>标签:</label>{{ tag }}
          </el-tag>
        </div>
        <div class="mt-3 grid grid-cols-2 gap-2 text-sm text-gray-500">
          <div>观看: {{ formatNumber(shortvideo.views) }}</div>
          <div>点赞: {{ formatNumber(shortvideo.likes) }}</div>
          <div>评论: {{ formatNumber(shortvideo.comments) }}</div>
          <div>分享: {{ formatNumber(shortvideo.shares) }}</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ShortVideo } from '@/types/shortvideos';

const props = defineProps<{
  visible: boolean;
  shortvideo: ShortVideo | null;
}>();

const emit = defineEmits(['update:visible']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 格式化数字（添加千位分隔符）
const formatNumber = (num: number | undefined): string => {
  return num ? num.toLocaleString() : '0';
};
</script>

<style scoped>
.video-preview {
  display: flex;
  flex-direction: column;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.font-medium {
  font-weight: 500;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}
</style>
