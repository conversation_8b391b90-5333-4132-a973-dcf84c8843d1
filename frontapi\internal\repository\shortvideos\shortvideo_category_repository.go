package shortvideos

import (
	"frontapi/internal/models/shortvideos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ShortVideoCategoryRepository 短视频分类仓库接口
type ShortVideoCategoryRepository interface {
	base.ExtendedRepository[shortvideos.ShortVideoCategory]
}

// shortVideoCategoryRepository 短视频分类仓库实现
type shortVideoCategoryRepository struct {
	base.ExtendedRepository[shortvideos.ShortVideoCategory]
}

// NewShortVideoCategoryRepository 创建短视频分类仓库实例
func NewShortVideoCategoryRepository(db *gorm.DB) ShortVideoCategoryRepository {
	return &shortVideoCategoryRepository{
		ExtendedRepository: base.NewExtendedRepository[shortvideos.ShortVideoCategory](db),
	}
}
