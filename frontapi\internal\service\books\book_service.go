package books

import (
	"context"
	"errors"
	"frontapi/internal/models/books"
	bookRepo "frontapi/internal/repository/books"
	"frontapi/internal/service/base"
)

// BookService 电子书服务接口
type BookService interface {
	// 继承BaseService的所有方法
	base.IExtendedService[books.Book]

	// 电子书特有的业务方法
	GetBooksByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*books.Book, int64, error)
	SearchBooks(ctx context.Context, keyword string, page, pageSize int) ([]*books.Book, int64, error)
	GetPopularBooks(ctx context.Context, page, pageSize int) ([]*books.Book, int64, error)
	GetRecentBooks(ctx context.Context, page, pageSize int) ([]*books.Book, int64, error)
}

type bookService struct {
	*base.ExtendedService[books.Book]                         // 嵌入BaseService，自动获得所有方法
	bookRepo                          bookRepo.BookRepository // 直接使用BookRepository
	categoryRepo                      bookRepo.CategoryRepository
	readHistoryRepo                   bookRepo.ReadHistoryRepository
}

// NewBookService 创建电子书服务
// 注意：点赞功能已移至 FavoriteService，此服务不再处理点赞相关操作
func NewBookService(
	bookRepo bookRepo.BookRepository,
	categoryRepo bookRepo.CategoryRepository,
	readHistoryRepo bookRepo.ReadHistoryRepository,
) BookService {
	return &bookService{
		ExtendedService: base.NewExtendedService[books.Book](bookRepo, "books"),
		bookRepo:        bookRepo,
		categoryRepo:    categoryRepo,
		readHistoryRepo: readHistoryRepo,
	}
}

// 通过嵌入BaseService，所有BaseService的方法都自动可用，无需手动委托

// 电子书特有的业务方法

// GetBooksByCategory 根据分类获取电子书列表
func (s *bookService) GetBooksByCategory(ctx context.Context, categoryID string, page, pageSize int) ([]*books.Book, int64, error) {
	if categoryID == "" {
		return nil, 0, errors.New("分类ID不能为空")
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 使用BaseService的List方法
	condition := map[string]interface{}{
		"category_id": categoryID,
		"status":      1, // 只获取正常状态的书籍
	}
	return s.BaseService.List(ctx, condition, "created_at DESC", page, pageSize, true)
}

// SearchBooks 搜索电子书
func (s *bookService) SearchBooks(ctx context.Context, keyword string, page, pageSize int) ([]*books.Book, int64, error) {
	if keyword == "" {
		return nil, 0, errors.New("搜索关键词不能为空")
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 使用BaseService的List方法进行搜索
	condition := map[string]interface{}{
		"keyword": keyword,
		"status":  1, // 只搜索正常状态的书籍
	}
	return s.BaseService.List(ctx, condition, "created_at DESC", page, pageSize, true)
}

// GetPopularBooks 获取热门电子书
func (s *bookService) GetPopularBooks(ctx context.Context, page, pageSize int) ([]*books.Book, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 使用BaseService的List方法，按照is_featured和status筛选
	condition := map[string]interface{}{
		"is_featured": true,
		"status":      1,
	}
	return s.BaseService.List(ctx, condition, "view_count DESC, created_at DESC", page, pageSize, true)
}

// GetRecentBooks 获取最新电子书
func (s *bookService) GetRecentBooks(ctx context.Context, page, pageSize int) ([]*books.Book, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 使用BaseService的List方法，按照创建时间倒序排列
	condition := map[string]interface{}{
		"status": 1, // 只获取正常状态的书籍
	}
	return s.BaseService.List(ctx, condition, "created_at DESC", page, pageSize, true)
}
