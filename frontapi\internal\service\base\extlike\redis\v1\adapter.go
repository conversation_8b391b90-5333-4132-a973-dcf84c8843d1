package v1

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// NewRedisAdapter 创建Redis适配器实例
// @param redisClient Redis客户端
// @param cacheTTL 缓存过期时间
// @return *RedisAdapter Redis适配器实例
func NewRedisAdapter(redisClient *redis.Client, cacheTTL time.Duration) *RedisAdapter {
	return &RedisAdapter{
		redisClient: redisClient,
		cacheTTL:    cacheTTL,
		stats: &CacheStats{
			HitCount:  0,
			MissCount: 0,
			HitRate:   0.0,
		},
	}
}

// generateUserLikeKey 生成用户点赞缓存键
// @param userID 用户ID
// @param itemType 项目类型
// @return string 缓存键
func (r *RedisAdapter) generateUserLikeKey(userID, itemType string) string {
	return fmt.Sprintf("like:user:%s:%s", userID, itemType)
}

// generateItemLikeKey 生成项目点赞缓存键
// @param itemType 项目类型
// @param itemID 项目ID
// @return string 缓存键
func (r *RedisAdapter) generateItemLikeKey(itemType, itemID string) string {
	return fmt.Sprintf("like:item:%s:%s", itemType, itemID)
}

// generateHotRankKey 生成热门排行缓存键
// @param itemType 项目类型
// @return string 缓存键
func (r *RedisAdapter) generateHotRankKey(itemType string) string {
	return fmt.Sprintf("like:hot:%s", itemType)
}

// LikeItem Redis点赞操作
// @param ctx 上下文
// @param userID 用户ID
// @param itemType 项目类型
// @param itemID 项目ID
// @return error 错误信息
func (r *RedisAdapter) LikeItem(ctx context.Context, userID, itemType, itemID string) error {
	userKey := r.generateUserLikeKey(userID, itemType)
	itemKey := r.generateItemLikeKey(itemType, itemID)

	// 使用Redis事务确保原子性
	pipe := r.redisClient.TxPipeline()

	// 添加到用户点赞集合
	pipe.SAdd(ctx, userKey, itemID)
	pipe.Expire(ctx, userKey, r.cacheTTL)

	// 增加项目点赞计数
	pipe.Incr(ctx, itemKey)
	pipe.Expire(ctx, itemKey, r.cacheTTL)

	// 更新热门排行（使用有序集合）
	hotKey := r.generateHotRankKey(itemType)
	pipe.ZIncrBy(ctx, hotKey, 1, itemID)
	pipe.Expire(ctx, hotKey, r.cacheTTL)

	_, err := pipe.Exec(ctx)
	return err
}

// UnlikeItem Redis取消点赞操作
// @param ctx 上下文
// @param userID 用户ID
// @param itemType 项目类型
// @param itemID 项目ID
// @return error 错误信息
func (r *RedisAdapter) UnlikeItem(ctx context.Context, userID, itemType, itemID string) error {
	userKey := r.generateUserLikeKey(userID, itemType)
	itemKey := r.generateItemLikeKey(itemType, itemID)

	// 使用Redis事务确保原子性
	pipe := r.redisClient.TxPipeline()

	// 从用户点赞集合中移除
	pipe.SRem(ctx, userKey, itemID)

	// 减少项目点赞计数
	pipe.Decr(ctx, itemKey)

	// 更新热门排行
	hotKey := r.generateHotRankKey(itemType)
	pipe.ZIncrBy(ctx, hotKey, -1, itemID)

	_, err := pipe.Exec(ctx)
	return err
}
