<template>
    <el-dialog v-model="dialogVisible" title="分类详情" width="500px" :close-on-click-modal="false"
        :close-on-press-escape="true" destroy-on-close>
        <el-descriptions :column="1" border>
            <el-descriptions-item label="分类ID">
                {{ categoryData?.id || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="分类名称">
                {{ categoryData?.name || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="分类编码">
                {{ categoryData?.code || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="排序">
                {{ categoryData?.sort_order || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
                <CategoryStatusTag :status="categoryData?.status || 0" />
            </el-descriptions-item>
            <el-descriptions-item label="描述">
                {{ categoryData?.description || '无描述' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
                {{ formatDateTime(categoryData?.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
                {{ formatDateTime(categoryData?.updated_at) }}
            </el-descriptions-item>
        </el-descriptions>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
                <el-button type="primary" @click="handleEdit">编辑</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { PictureCategory } from '@/types/pictures';
import dayjs from 'dayjs';
import { computed } from 'vue';
import CategoryStatusTag from './CategoryStatusTag.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    categoryData: {
        type: Object as () => PictureCategory | null,
        default: null
    }
});

const emit = defineEmits(['update:visible', 'edit']);

// 计算属性：对话框是否可见
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '-';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false;
};

// 编辑分类
const handleEdit = () => {
    dialogVisible.value = false;
    emit('edit', props.categoryData);
};
</script>

<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    gap: 12px;
}

:deep(.el-descriptions__label) {
    width: 100px;
    text-align: right;
}
</style>