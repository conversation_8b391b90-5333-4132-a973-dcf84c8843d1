<template>
  <div 
    class="video-card"
    :class="{ 'video-card--loading': loading }"
    @click="handleClick"
  >
    <!-- 视频缩略图 -->
    <div class="video-card__thumbnail">
      <img 
        v-if="!loading"
        :src="video?.thumbnail || '/placeholder-video.jpg'"
        :alt="video?.title"
        class="video-card__image"
        loading="lazy"
      />
      <div v-else class="video-card__skeleton-image"></div>
      
      <!-- 视频时长 -->
      <div v-if="video?.duration && !loading" class="video-card__duration">
        {{ formatDuration(video.duration) }}
      </div>
      
      <!-- 播放按钮覆盖层 -->
      <div class="video-card__overlay">
        <el-icon class="video-card__play-icon">
          <VideoPlay />
        </el-icon>
      </div>
    </div>
    
    <!-- 视频信息 -->
    <div class="video-card__content">
      <!-- 标题 -->
      <h3 v-if="!loading" class="video-card__title" :title="video?.title">
        {{ video?.title }}
      </h3>
      <div v-else class="video-card__skeleton-title"></div>
      
      <!-- 作者信息 -->
      <div v-if="showAuthor && !loading" class="video-card__author">
        <img 
          :src="video?.author?.avatar || '/placeholder-avatar.jpg'"
          :alt="video?.author?.name"
          class="video-card__avatar"
        />
        <span class="video-card__author-name">{{ video?.author?.name }}</span>
        <el-icon v-if="video?.author?.verified" class="video-card__verified">
          <Check />
        </el-icon>
      </div>
      <div v-else-if="showAuthor && loading" class="video-card__skeleton-author"></div>
      
      <!-- 统计信息 -->
      <div v-if="showStats && !loading" class="video-card__stats">
        <span class="video-card__stat">
          <el-icon><View /></el-icon>
          {{ formatNumber(video?.viewCount || 0) }}
        </span>
        <span class="video-card__stat">
          <el-icon><Timer /></el-icon>
          {{ formatTimeAgo(video?.publishedAt) }}
        </span>
      </div>
      <div v-else-if="showStats && loading" class="video-card__skeleton-stats"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { VideoPlay, Check, View, Timer } from '@element-plus/icons-vue'
import { formatDuration, formatNumber, formatTimeAgo } from '@/core/utils'

export interface Video {
  id: string
  title: string
  thumbnail: string
  duration: number
  viewCount: number
  publishedAt: string
  author: {
    id: string
    name: string
    avatar: string
    verified: boolean
  }
}

export interface VideoCardProps {
  video?: Video
  showAuthor?: boolean
  showStats?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<VideoCardProps>(), {
  showAuthor: true,
  showStats: true,
  loading: false
})

const emit = defineEmits<{
  click: [video: Video]
}>()

const handleClick = () => {
  if (props.video && !props.loading) {
    emit('click', props.video)
  }
}
</script>

<style scoped>
.video-card {
  @apply bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer;
}

.video-card:hover {
  @apply transform -translate-y-1;
}

.video-card--loading {
  @apply cursor-default;
}

.video-card__thumbnail {
  @apply relative aspect-video bg-gray-200 overflow-hidden;
}

.video-card__image {
  @apply w-full h-full object-cover;
}

.video-card__skeleton-image {
  @apply w-full h-full bg-gray-300 animate-pulse;
}

.video-card__duration {
  @apply absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded;
}

.video-card__overlay {
  @apply absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center;
}

.video-card__play-icon {
  @apply text-white text-4xl opacity-0 hover:opacity-100 transition-opacity duration-200;
}

.video-card__content {
  @apply p-4;
}

.video-card__title {
  @apply text-lg font-semibold text-gray-900 mb-2 line-clamp-2;
}

.video-card__skeleton-title {
  @apply h-6 bg-gray-300 rounded animate-pulse mb-2;
}

.video-card__author {
  @apply flex items-center gap-2 mb-2;
}

.video-card__avatar {
  @apply w-8 h-8 rounded-full object-cover;
}

.video-card__author-name {
  @apply text-sm text-gray-700 font-medium;
}

.video-card__verified {
  @apply text-blue-500 text-sm;
}

.video-card__skeleton-author {
  @apply h-8 bg-gray-300 rounded animate-pulse mb-2;
}

.video-card__stats {
  @apply flex items-center gap-4 text-sm text-gray-500;
}

.video-card__stat {
  @apply flex items-center gap-1;
}

.video-card__skeleton-stats {
  @apply h-4 bg-gray-300 rounded animate-pulse;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-card__content {
    @apply p-3;
  }
  
  .video-card__title {
    @apply text-base;
  }
}

/* 工具类 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>