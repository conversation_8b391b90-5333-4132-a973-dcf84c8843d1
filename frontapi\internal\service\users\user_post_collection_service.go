package users

import (
	"frontapi/internal/models/users"
	"frontapi/internal/repository/posts"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreatePostCollectionRequest 创建帖子收藏请求
type CreatePostCollectionRequest struct {
	UserID string `json:"userId" validate:"required"`
	PostID string `json:"postId" validate:"required"`
	// 以下字段从帖子中获取
	PostContent  string   `json:"postContent"`
	PostImages   []string `json:"postImages"`
	AuthorID     string   `json:"authorId"`
	AuthorName   string   `json:"authorName"`
	AuthorAvatar string   `json:"authorAvatar"`
	Remark       string   `json:"remark"`
	Title        string   `json:"title"`
	Cover        string   `json:"cover"`
	Author       string   `json:"author"`
	Summary      string   `json:"summary"`
}

// UserPostCollectionService 用户帖子收藏服务接口
type UserPostCollectionService interface {
	base.IExtendedService[users.UserPostCollection]
}

// userPostCollectionService 用户帖子收藏服务实现
type userPostCollectionService struct {
	*base.ExtendedService[users.UserPostCollection]
	postCollectionRepo repo.UserPostCollectionRepository
	postRepo           posts.PostRepository
	userRepo           repo.UserRepository
}

// NewUserPostCollectionService 创建用户帖子收藏服务实例
func NewUserPostCollectionService(
	postCollectionRepo repo.UserPostCollectionRepository,
	postRepo posts.PostRepository,
	userRepo repo.UserRepository,
) UserPostCollectionService {
	return &userPostCollectionService{
		ExtendedService:    base.NewExtendedService[users.UserPostCollection](postCollectionRepo, "user_post_collection"),
		postCollectionRepo: postCollectionRepo,
		postRepo:           postRepo,
		userRepo:           userRepo,
	}
}
