# 视频频道Null值支持修复

## 问题描述

用户希望在视频编辑时能够将频道ID设置为null，以实现删除频道的功能。需要使用 `github.com/guregu/null/v6` 包来正确处理null值。

## 技术背景

### 使用的null包
- `github.com/guregu/null/v6`
- 提供了对数据库NULL值的完整支持
- `null.String` 类型可以表示有效的字符串值或NULL

### 数据库字段定义
```go
// 在视频模型中
ChannelID null.String `json:"channel_id" gorm:"column:channel_id" comment:"频道ID"`
```

## 修复方案

### 1. 后端Controller修复

#### CreateVideo方法
```go
// 设置分类和创作者信息
video.SetCategoryID(req.CategoryID)
video.SetCategoryName(req.CategoryName)

// 处理频道ID和频道名称，支持null值
video.ChannelID = null.StringFromPtr(req.ChannelID)
// 当频道ID为nil或空时，清空频道名称；否则设置频道名称
if req.ChannelID == nil || (req.ChannelID != nil && *req.ChannelID == "") {
    video.ChannelName = "" // 清空频道名称
} else {
    video.ChannelName = req.ChannelName
}
```

#### UpdateVideo方法
```go
// 处理频道ID和频道名称，支持清空操作
video.ChannelID = null.StringFromPtr(req.ChannelID)
// 当频道ID为nil或空时，清空频道名称；否则设置频道名称
if req.ChannelID == nil || (req.ChannelID != nil && *req.ChannelID == "") {
    video.ChannelName = "" // 清空频道名称
} else {
    video.ChannelName = req.ChannelName
}
```

### 2. 核心改进

#### 2.1 移除条件判断
**修改前：**
```go
if req.ChannelID != nil {
    video.ChannelID = null.StringFromPtr(req.ChannelID)
}
```

**修改后：**
```go
// 直接赋值，支持nil值
video.ChannelID = null.StringFromPtr(req.ChannelID)
```

#### 2.2 添加名称清空逻辑
当频道ID为nil时，自动清空频道名称，确保数据一致性。

### 3. 前端配合

前端已经正确配置：
```typescript
// 前端提交数据时
channel_id: form.channel_id || null, // 如果为空，传null  
channel_name: channelName,
```

## 功能特性

### 1. 完整的Null值支持
- **设置频道**: `channel_id` 有值时，正常设置频道ID和名称
- **清空频道**: `channel_id` 为null或空字符串时，清空频道信息
- **数据库存储**: 使用NULL值正确存储到数据库

### 2. 数据一致性保证
- 频道ID为null时，频道名称自动清空
- 避免出现ID为空但名称不为空的不一致状态

### 3. 向后兼容
- 对现有数据完全兼容
- 不影响已有的视频记录

## 使用场景

### 1. 创建视频
```json
{
  "channel_id": null,        // 不设置频道
  "channel_name": ""
}
```

### 2. 编辑视频 - 设置频道
```json
{
  "channel_id": "channel-123",
  "channel_name": "音乐频道"
}
```

### 3. 编辑视频 - 删除频道
```json
{
  "channel_id": null,        // 删除频道关联
  "channel_name": ""
}
```

## 数据库行为

### 存储行为
- `null.StringFromPtr(nil)` → 数据库存储为 `NULL`
- `null.StringFromPtr(&"channel-123")` → 数据库存储为 `"channel-123"`

### 查询行为
- 可以使用 `WHERE channel_id IS NULL` 查询没有频道的视频
- 可以使用 `WHERE channel_id = 'channel-123'` 查询特定频道的视频

## 测试用例

### 1. 清空频道测试
1. 编辑一个有频道的视频
2. 清空频道选择
3. 保存后验证：
   - `channel_id` 在数据库中为 `NULL`
   - `channel_name` 为空字符串

### 2. 设置频道测试
1. 编辑一个没有频道的视频
2. 选择一个频道
3. 保存后验证：
   - `channel_id` 在数据库中为正确的频道ID
   - `channel_name` 为正确的频道名称

### 3. 切换频道测试
1. 编辑一个有频道A的视频
2. 切换到频道B
3. 保存后验证：
   - `channel_id` 更新为频道B的ID
   - `channel_name` 更新为频道B的名称

## 技术细节

### null.StringFromPtr函数
```go
// 函数签名
func StringFromPtr(s *string) String

// 行为
- 输入nil → 返回null.String{Valid: false}
- 输入&"value" → 返回null.String{String: "value", Valid: true}
```

### GORM行为
```go
// null.String{Valid: false} → 数据库存储为NULL
// null.String{String: "value", Valid: true} → 数据库存储为"value"
```

## 相关文件

- `frontapi/internal/admin/videos/video_controller.go` - Controller逻辑
- `frontapi/internal/models/videos/video.go` - 模型定义
- `frontapi/internal/validation/videos/video.go` - 验证规则
- `backend/src/views/videos/list/VideoDialog.vue` - 前端组件

## 注意事项

1. **导入包**: 确保使用正确的null包版本 `github.com/guregu/null/v6`
2. **数据类型**: 确保字段类型为 `null.String` 而不是 `*string`
3. **验证规则**: 验证结构体中使用 `*string` 类型以支持nil值传递
4. **前端处理**: 前端需要正确处理空值的传递（传递null而不是空字符串）
5. **运行时安全**: 在解引用指针前必须进行nil检查，避免panic
   ```go
   // 安全的检查方式
   if req.ChannelID == nil || (req.ChannelID != nil && *req.ChannelID == "") {
       // 处理nil或空值情况
   }
   ``` 