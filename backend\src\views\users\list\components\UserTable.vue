<template>
  <div class="user-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量禁用
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量启用
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 -->
    <div class="table-content">
      <SlinkyTable
        :data="userList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="编辑"
        delete-text="删除"
        empty-text="暂无用户数据"
        class="user-data-table"
      >
        <!-- 用户信息列 -->
        <el-table-column prop="username" label="用户信息" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar
                :size="36"
                :src="row.avatar"
                :alt="row.username"
                class="user-avatar"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.username }}</div>
                <div class="nickname">{{ row.nickname || '未设置昵称' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 联系方式列 -->
        <el-table-column label="联系方式" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="contact-info">
              <div class="contact-item" v-if="row.email">
                <el-icon class="contact-icon"><Message /></el-icon>
                <span class="contact-text">{{ row.email }}</span>
              </div>
              <div class="contact-item" v-if="row.phone">
                <el-icon class="contact-icon"><Phone /></el-icon>
                <span class="contact-text">{{ row.phone }}</span>
              </div>
              <div v-if="!row.email && !row.phone" class="no-contact">
                <span class="placeholder-text">暂无联系方式</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 创作者等级列 -->
        <el-table-column prop="creator_level" label="创作者等级" width="110" align="center">
          <template #default="{ row }">
            <UserLevelTag :level="row.creator_level" />
          </template>
        </el-table-column>

        <!-- 创作者状态列 -->
        <el-table-column prop="is_content_creator" label="创作者" width="80" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.is_content_creator === 1 ? 'success' : 'info'" 
              size="small"
              effect="light"
            >
              <el-icon>
                <component :is="row.is_content_creator === 1 ? VideoCameraFilled : User" />
              </el-icon>
              {{ row.is_content_creator === 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 用户类型列 -->
        <el-table-column prop="user_type" label="用户类型" width="80" align="center">
                <template #default="{ row }">
            <el-tag 
              :type="row.user_type === 1 ? 'success' : 'info'" 
              size="small"
              effect="light"
            >
              {{ row.user_type === 1 ? '普通用户' : 'VIP用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 积分余额列 -->
        <el-table-column prop="score" label="积分" min-width="80" align="center">
          <template #default="{ row }">
            <div class="points-info">
              <el-icon class="points-icon"><Coin /></el-icon>
              <span class="points-value">{{ row.score || 0 }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- bio列 -->
        <el-table-column prop="bio" label="简介" min-width="160" align="center" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="bio-info">
              <span class="bio-text">{{ row.bio?.slice(0, 200) || '暂无简介' }}</span>
            </div>
          </template>
        </el-table-column>
        <!-- 地理位置列 -->
        <el-table-column prop="location" label="地理位置" min-width="50" align="center">
          <template #default="{ row }">
            <div class="location-info">
              <span class="location-text">{{ row.nation }}</span>
              <span class="location-text">{{ row.lat }}</span> | <span class="location-text">{{ row.lng }}</span>   
            </div>
          </template>
        </el-table-column>
        <!-- 推荐人 -->
        <el-table-column prop="referrer" label="推荐人ID" min-width="80" align="center">
          <template #default="{ row }">
            <div class="referrer-info">
              <span class="referrer-text">{{ row.recommend_id }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <UserStatusTag :status="row.status" />
          </template>
        </el-table-column>

        <!-- 注册时间列 -->
        <el-table-column prop="created_at" label="注册时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ formatDate(row.created_at) }}</span>
            </div>
          </template>
        </el-table-column>
       

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-popconfirm
              :title="`确定要${row.status === 1 ? '禁用' : '启用'}用户 ${row.username} 吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  link 
                  size="small"
                >
                  <el-icon>
                    <component :is="row.status === 1 ? Lock : Unlock" />
                  </el-icon>
                  {{ row.status === 1 ? '禁用' : row.status === 0 ? '启用' : '已删除' }}
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              :title="`确定要删除用户 ${row.username} 吗？此操作不可恢复！`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页器 -->
    <div class="table-footer">
      <SinglePager
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UserItem } from '@/types/users';
import { formatDate } from '@/utils/date';
import {
    Calendar,
    Check,
    Coin,
    Delete,
    Edit,
    Lock,
    Message,
    Phone,
    Unlock,
    User,
    VideoCameraFilled,
    View
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref } from 'vue';

// 导入主题组件
import {SinglePager,SlinkyTable} from '@/components/themes';

// 导入子组件
import UserLevelTag from './UserLevelTag.vue';
import UserStatusTag from './UserStatusTag.vue';

// Props
interface Props {
  userList: UserItem[];
  loading: boolean;
  pagination: {
    total: number;
    page: number;
    pageSize: number;
  };
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'selection-change': [selection: UserItem[]];
  'view': [user: UserItem];
  'edit': [user: UserItem];
  'delete': [user: UserItem];
  'change-status': [id: string, status: number];
  'current-change': [page: number];
  'size-change': [size: number];
  'add': [];
  'refresh': [];
  'export': [];
  'batch-status': [status: number, users: UserItem[]];
  'batch-delete': [users: UserItem[]];
}>();

// 响应式数据
const selectedRows = ref<UserItem[]>([]);

// 事件处理
const handleSelectionChange = (selection: UserItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

const handleView = (user: UserItem) => {
  emit('view', user);
};

const handleEdit = (user: UserItem) => {
  emit('edit', user);
};

const handleDelete = (user: UserItem) => {
  emit('delete', user);
};

const handleChangeStatus = (id: string, status: number) => {
  emit('change-status', id, status);
};

const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 批量操作
const handleBatchStatus = (status: number) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户');
    return;
  }
  
  const action = status === 1 ? '启用' : '禁用';
  ElMessageBox.confirm(
    `确定要${action}选中的 ${selectedRows.value.length} 个用户吗？`,
    '批量操作确认',
    {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    }
  ).then(() => {
    emit('batch-status', status, [...selectedRows.value]);
  }).catch(() => {
    // 用户取消操作
  });
};

const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的用户');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedRows.value.length} 个用户吗？此操作不可恢复！`,
    '批量删除确认',
    {
      type: 'error',
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    }
  ).then(() => {
    emit('batch-delete', [...selectedRows.value]);
  }).catch(() => {
    // 用户取消操作
  });
};
</script>

<style scoped lang="scss">
.user-table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* 批量操作工具栏 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: var(--el-color-primary-light-9);
  border-bottom: 1px solid var(--el-color-primary-light-7);
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-color-primary);
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

/* 表格内容样式 */
.table-content {
  padding: 0;
}

.user-data-table {
  border-radius: 0;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.user-avatar {
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.nickname {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 联系方式样式 */
.contact-info {
  padding: 4px 0;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 2px;
  font-size: 13px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  flex-shrink: 0;
}

.contact-text {
  color: var(--el-text-color-regular);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-contact {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
}

.placeholder-text {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

/* 积分样式 */
.points-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.points-icon {
  font-size: 14px;
  color: #f39c12;
}

.points-value {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 时间样式 */
.time-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.time-icon {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.time-text {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 操作按钮样式 */
.action-buttons-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons-group .el-button {
  margin: 0;
  padding: 4px 8px;
}

/* 分页器样式 */
.table-footer {
  padding: 16px 24px;
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color-lighter);
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .pagination-wrapper {
    justify-content: center;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .table-header {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  }
  
  .title-icon {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .count-tag {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}
</style> 