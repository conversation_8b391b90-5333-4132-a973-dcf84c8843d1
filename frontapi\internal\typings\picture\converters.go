package picture

import (
	"frontapi/internal/models/pictures"
	"frontapi/internal/typings"
	"time"
)

func formatTime(time time.Time) string {
	return time.Format("2006-01-02 15:04:05")
}
func ConvertPictureCateory(category *pictures.PictureCategory) *PictureCategoryInfo {
	return &PictureCategoryInfo{
		ID:        category.ID,
		Name:      category.Name.ValueOrZero(),
		Code:      category.Code,
		SortOrder: int64(category.SortOrder),
	}
}
func ConvertPictureCategoryList(categories []*pictures.PictureCategory) []PictureCategoryInfo {
	result := make([]PictureCategoryInfo, len(categories))
	for i, category := range categories {
		result[i] = *ConvertPictureCateory(category)
	}
	return result
}
func ConvertPictureCategoryListResponse(categories []*pictures.PictureCategory, total int64, pageNo int, pageSize int) PictureCategoryListResponse {
	result := PictureCategoryListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertPictureCategoryList(categories),
	}
	return result
}

func ConvertPictureAlbum(album *pictures.PictureAlbum) *PictureAlbumDetail {
	return &PictureAlbumDetail{
		ID:           album.ID,
		Title:        album.Title,
		Description:  album.Description.ValueOrZero(),
		CoverURL:     album.CoverURL,
		PictureCount: int64(album.PictureCount),
		ViewCount:    int64(album.ViewCount),
		LikeCount:    int64(album.LikeCount),
		CommentCount: int64(album.CommentCount),
		CategoryID:   album.CategoryID.ValueOrZero(),
		CategoryName: album.CategoryName.ValueOrZero(),
		Tags:         album.Tags,
	}
}

// ConvertPictureAlbumDetail 转换单个专辑详情
func ConvertPictureAlbumDetail(album *pictures.PictureAlbum) *PictureAlbumDetail {
	return ConvertPictureAlbum(album)
}

func ConvertPictureAlbumList(albums []*pictures.PictureAlbum) []PictureAlbumDetail {
	result := make([]PictureAlbumDetail, len(albums))
	for i, album := range albums {
		result[i] = *ConvertPictureAlbum(album)
	}
	return result
}
func ConvertPictureAlbumListResponse(albums []*pictures.PictureAlbum, total int64, pageNo int, pageSize int) PictureAlbumListResponse {
	result := PictureAlbumListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertPictureAlbumList(albums),
	}
	return result
}

func ConvertPictureInfo(picture *pictures.Picture) *PictureInfo {
	return &PictureInfo{
		ID:           picture.ID,
		URL:          picture.URL,
		Title:        picture.Title,
		Description:  picture.Description.ValueOrZero(),
		Width:        int64(picture.Width),
		Height:       int64(picture.Height),
		Size:         int64(picture.Size),
		CategoryID:   picture.CategoryID.ValueOrZero(),
		CategoryName: picture.CategoryName.ValueOrZero(),
		AlbumID:      picture.AlbumID.ValueOrZero(),
		AlbumTitle:   picture.AlbumTitle,
		CreatorID:    picture.CreatorID.ValueOrZero(),
		ViewCount:    int64(picture.ViewCount),
		LikeCount:    int64(picture.LikeCount),
		UploadTime:   formatTime(time.Time(picture.UploadTime)),
		Status:       picture.Status,
		CreatedAt:    formatTime(time.Time(picture.CreatedAt)),
		UpdatedAt:    formatTime(time.Time(picture.UpdatedAt)),
		IsLiked:      picture.IsLiked,
		IsFavorite:   picture.IsFavorite,
	}
}

func ConvertPictureList(pictures []*pictures.Picture) []PictureInfo {
	result := make([]PictureInfo, len(pictures))
	for i, picture := range pictures {
		result[i] = *ConvertPictureInfo(picture)
	}
	return result
}

func ConvertPictureListResponse(pictures []*pictures.Picture, total int64, pageNo int, pageSize int) PictureListResponse {
	return PictureListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertPictureList(pictures),
	}
}
