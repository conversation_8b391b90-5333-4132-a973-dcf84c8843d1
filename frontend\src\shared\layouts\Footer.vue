<template>
  <footer class="modern-footer">
    <!-- 主要内容区域 -->
    <div class="footer-main">
      <div class="footer-container">
        <!-- 品牌区域 -->
        <div class="footer-brand">
          <div class="brand-logo">
            <div class="logo-icon">
              <div class="logo-gradient"></div>
            </div>
            <span class="brand-name">MyFirm</span>
          </div>
          <p class="brand-description">
            打造最优质的视频内容平台，为用户提供丰富多彩的娱乐体验。
          </p>
          <div class="social-links">
            <a href="#" class="social-link" title="微博">
              <el-icon><Platform /></el-icon>
            </a>
            <a href="#" class="social-link" title="微信">
              <el-icon><ChatDotRound /></el-icon>
            </a>
            <a href="#" class="social-link" title="抖音">
              <el-icon><VideoPlay /></el-icon>
            </a>
            <a href="#" class="social-link" title="B站">
              <el-icon><Film /></el-icon>
            </a>
          </div>
        </div>
        
        <!-- 链接区域 -->
        <div class="footer-links">
          <div class="link-group">
            <h4 class="group-title">产品服务</h4>
            <ul class="link-list">
              <li><router-link to="/">首页</router-link></li>
              <li><router-link to="/channels">频道</router-link></li>
              <li><router-link to="/shorts">短视频</router-link></li>
              <li><router-link to="/community">社区</router-link></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="group-title">帮助支持</h4>
            <ul class="link-list">
              <li><a href="#">常见问题</a></li>
              <li><a href="#">使用指南</a></li>
              <li><a href="#">意见反馈</a></li>
              <li><a href="#">联系客服</a></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="group-title">关于我们</h4>
            <ul class="link-list">
              <li><a href="#">公司介绍</a></li>
              <li><a href="#">加入我们</a></li>
              <li><a href="#">合作伙伴</a></li>
              <li><a href="#">媒体报道</a></li>
            </ul>
          </div>
          
          <div class="link-group">
            <h4 class="group-title">法律条款</h4>
            <ul class="link-list">
              <li><a href="#">用户协议</a></li>
              <li><a href="#">隐私政策</a></li>
              <li><a href="#">版权声明</a></li>
              <li><a href="#">免责声明</a></li>
            </ul>
          </div>
        </div>
        
        <!-- 订阅区域 -->
        <div class="footer-subscribe">
          <h4 class="subscribe-title">订阅我们</h4>
          <p class="subscribe-description">获取最新资讯和优质内容推荐</p>
          <div class="subscribe-form">
            <el-input
              v-model="email"
              placeholder="请输入您的邮箱"
              class="subscribe-input"
            >
              <template #suffix>
                <el-button 
                  type="primary" 
                  class="subscribe-btn"
                  @click="handleSubscribe"
                >
                  订阅
                </el-button>
              </template>
            </el-input>
          </div>
          <div class="download-apps">
            <h5>下载APP</h5>
            <div class="app-links">
              <a href="#" class="app-link">
                <el-icon><Iphone /></el-icon>
                <span>iOS</span>
              </a>
              <a href="#" class="app-link">
                <el-icon><Platform /></el-icon>
                <span>Android</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部版权区域 -->
    <div class="footer-bottom">
      <div class="footer-container">
        <div class="bottom-content">
          <div class="copyright">
            <p>&copy; 2024 MyFirm. All rights reserved.</p>
            <p class="icp">京ICP备12345678号-1</p>
          </div>
          <div class="bottom-links">
            <a href="#">网站地图</a>
            <span class="divider">|</span>
            <a href="#">友情链接</a>
            <span class="divider">|</span>
            <a href="#">广告合作</a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  Platform, 
  ChatDotRound, 
  VideoPlay, 
  Film, 
  Iphone 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const email = ref('')

const handleSubscribe = () => {
  if (!email.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }
  
  // 简单的邮箱验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email.value)) {
    ElMessage.error('请输入有效的邮箱地址')
    return
  }
  
  // TODO: 实现订阅功能
  ElMessage.success('订阅成功！感谢您的关注')
  email.value = ''
}
</script>

<style scoped lang="scss">
.modern-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  margin-top: auto;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 157, 0.3), transparent);
  }
}

.footer-main {
  padding: var(--spacing-2xl) 0;
  
  .footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-2xl);
    
    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-xl);
      text-align: center;
    }
  }
}

// 品牌区域
.footer-brand {
  .brand-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    
    @media (max-width: 1024px) {
      justify-content: center;
    }
    
    .logo-icon {
      .logo-gradient {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        border-radius: var(--radius-lg);
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          inset: 3px;
          background: white;
          border-radius: var(--radius-md);
        }
        
        &::after {
          content: 'M';
          position: absolute;
          inset: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 24px;
          background: linear-gradient(135deg, #FF6B9D, #9C27B0);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          z-index: 1;
        }
      }
    }
    
    .brand-name {
      font-size: 28px;
      font-weight: 700;
      background: linear-gradient(135deg, #FF6B9D, #9C27B0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      letter-spacing: -0.5px;
    }
  }
  
  .brand-description {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    max-width: 280px;
    
    @media (max-width: 1024px) {
      max-width: none;
    }
  }
  
  .social-links {
    display: flex;
    gap: var(--spacing-sm);
    
    @media (max-width: 1024px) {
      justify-content: center;
    }
    
    .social-link {
      width: 44px;
      height: 44px;
      background: rgba(255, 107, 157, 0.1);
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-primary);
      text-decoration: none;
      transition: all var(--transition-normal);
      
      &:hover {
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
      }
      
      .el-icon {
        font-size: 20px;
      }
    }
  }
}

// 链接区域
.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
  
  .link-group {
    .group-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-text);
      margin-bottom: var(--spacing-md);
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 30px;
        height: 2px;
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        border-radius: var(--radius-sm);
        
        @media (max-width: 1024px) {
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
    
    .link-list {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        margin-bottom: var(--spacing-sm);
        
        a {
          color: var(--color-text-secondary);
          text-decoration: none;
          font-size: 14px;
          transition: all var(--transition-fast);
          display: inline-block;
          
          &:hover {
            color: var(--color-primary);
            transform: translateX(4px);
          }
        }
      }
    }
  }
}

// 订阅区域
.footer-subscribe {
  .subscribe-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text);
    margin-bottom: var(--spacing-sm);
  }
  
  .subscribe-description {
    color: var(--color-text-secondary);
    font-size: 14px;
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
  }
  
  .subscribe-form {
    margin-bottom: var(--spacing-lg);
    
    .subscribe-input {
      :deep(.el-input__wrapper) {
        border-radius: var(--radius-full);
        border: 2px solid rgba(255, 107, 157, 0.2);
        transition: all var(--transition-normal);
        
        &:hover {
          border-color: rgba(255, 107, 157, 0.4);
        }
        
        &.is-focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 2px rgba(255, 107, 157, 0.1);
        }
      }
      
      :deep(.el-input__inner) {
        padding-right: 80px;
      }
      
      .subscribe-btn {
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        border: none;
        border-radius: var(--radius-full);
        padding: var(--spacing-xs) var(--spacing-md);
        font-weight: 500;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }
      }
    }
  }
  
  .download-apps {
    h5 {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text);
      margin-bottom: var(--spacing-sm);
    }
    
    .app-links {
      display: flex;
      gap: var(--spacing-sm);
      
      @media (max-width: 1024px) {
        justify-content: center;
      }
      
      .app-link {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        background: rgba(255, 107, 157, 0.1);
        border-radius: var(--radius-lg);
        color: var(--color-primary);
        text-decoration: none;
        font-weight: 500;
        font-size: 14px;
        transition: all var(--transition-normal);
        
        &:hover {
          background: linear-gradient(135deg, #FF6B9D, #9C27B0);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }
        
        .el-icon {
          font-size: 18px;
        }
      }
    }
  }
}

// 底部版权区域
.footer-bottom {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 107, 157, 0.1);
  padding: var(--spacing-lg) 0;
  
  .footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
  }
  
  .bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      text-align: center;
    }
    
    .copyright {
      p {
        color: var(--color-text-secondary);
        font-size: 14px;
        margin: 0;
        
        &.icp {
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
    
    .bottom-links {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      
      a {
        color: var(--color-text-secondary);
        text-decoration: none;
        font-size: 14px;
        transition: color var(--transition-fast);
        
        &:hover {
          color: var(--color-primary);
        }
      }
      
      .divider {
        color: var(--color-border);
        font-size: 12px;
      }
    }
  }
}
</style>
  