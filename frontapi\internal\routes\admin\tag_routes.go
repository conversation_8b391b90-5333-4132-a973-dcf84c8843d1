package admin

import (
	"frontapi/internal/admin/system"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterTagRoutes 注册标签相关路由
func RegisterTagRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建标签API路由组
	tagApi := apiGroup.Group("/tags", middleware.AuthRequired())

	// 创建标签控制器
	tagController := system.NewTagController(services.TagService)
	// 设置JWT认证中间件
	{
		// 注册标签管理相关路由
		tagApi.Post("/list", tagController.ListTags)                 // 获取标签列表
		tagApi.Post("/detail/:id?", tagController.GetTag)            // 获取标签详情
		tagApi.Post("/add", tagController.CreateTag)                 // 创建标签
		tagApi.Post("/update", tagController.UpdateTag)              // 更新标签
		tagApi.Post("/update/status", tagController.UpdateTagStatus) // 更新标签状态
		tagApi.Post("/delete/:id?", tagController.DeleteTag)         // 删除标签
	}

}
