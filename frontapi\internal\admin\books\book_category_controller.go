package books

import (
	"frontapi/internal/admin"
	bookModel "frontapi/internal/models/books"
	bookSrv "frontapi/internal/service/books"
	bookValidator "frontapi/internal/validation/books"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

type BookCategoryController struct {
	admin.BaseController
	BookCategoryService          bookSrv.BookCategoryService
	BookCategoryServiceWithHooks *bookSrv.BookCategoryService
	BookChapterService           bookSrv.BookChapterService
	BookService                  bookSrv.BookService
}

func NewBookCategoryController(
	bookCategoryService bookSrv.BookCategoryService,
	bookService bookSrv.BookService,
) *BookCategoryController {
	return &BookCategoryController{
		BookService:         bookService,
		BookCategoryService: bookCategoryService,
	}
}

// ListBookCategories 获取电子书分类列表
func (c *BookCategoryController) ListBookCategories(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	name := reqInfo.Get("name").GetString()
	status := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询电子书分类列表
	categoryList, total, err := c.BookCategoryService.List(ctx.Context(), map[string]interface{}{
		"name":   name,
		"status": status,
	}, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取电子书分类列表失败: "+err.Error())
	}

	// 返回电子书分类列表
	return c.SuccessList(ctx, categoryList, total, page, pageSize)
}

// GetBookCategory 获取电子书分类详情
func (c *BookCategoryController) GetBookCategory(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询分类
	category, err := c.BookCategoryService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取电子书分类详情失败: "+err.Error())
	}

	if category == nil {
		return c.NotFound(ctx, "电子书分类不存在")
	}

	// 返回分类详情
	return c.Success(ctx, category)
}

// CreateBookCategory 创建电子书分类
func (c *BookCategoryController) CreateBookCategory(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.CreateCategoryRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	var category bookModel.BookCategory
	if err := utils.SmartCopy(req, &category); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 创建分类（带名称唯一性验证）
	id, err := c.BookCategoryService.Create(ctx.Context(), &category)
	if err != nil {
		return c.InternalServerError(ctx, "创建电子书分类失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建电子书分类成功",
	})
}

// UpdateBookCategory 更新电子书分类
func (c *BookCategoryController) UpdateBookCategory(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.UpdateCategoryRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	id, _ := c.GetId(ctx)

	var category bookModel.BookCategory
	if err := utils.SmartCopy(req, &category); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 更新分类（带名称唯一性验证）
	err := c.BookCategoryService.UpdateById(ctx.Context(), id, &category)
	if err != nil {
		return c.InternalServerError(ctx, "更新电子书分类失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新电子书分类成功")
}

// UpdateBookCategoryStatus 更新电子书分类状态
func (c *BookCategoryController) UpdateBookCategoryStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.UpdateCategoryStatusRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 更新分类状态
	id, _ := c.GetId(ctx)

	err := c.BookCategoryService.UpdateStatus(ctx.Context(), id, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新电子书分类状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新电子书分类状态成功")
}

// DeleteBookCategory 删除电子书分类
func (c *BookCategoryController) DeleteBookCategory(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除分类
	err = c.BookCategoryService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除电子书分类失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除电子书分类成功")
}
