package system

import (
	"frontapi/internal/models/system"
	repo "frontapi/internal/repository/system"
	"frontapi/internal/service/base"
)

type CountryService interface {
	base.IExtendedService[system.Country]
}

type countryService struct {
	*base.ExtendedService[system.Country]
	repo repo.CountryRepository
}

func NewCountryService(repo repo.CountryRepository) CountryService {
	return &countryService{
		ExtendedService: base.NewExtendedService[system.Country](repo, "country"),
		repo:            repo,
	}
}
