/**
 * Mysterious 主题配置
 * 基于神秘紫色的主题
 */
import { ThemeConfig } from '../theme-manager';
import mysteriousVariables from './variables';
import mysteriousDarkVariables from './variables-dark';

// Mysterious 亮色主题
export const mysteriousLightTheme: ThemeConfig = {
    name: 'mysterious-light',
    displayName: 'Mysterious Light',
    shortName: 'Mysterious Light',
    code: 'mysterious',
    primary: '#673AB7',
    isDark: false,
    variables: mysteriousVariables
};

// Mysterious 暗色主题
export const mysteriousDarkTheme: ThemeConfig = {
    name: 'mysterious-dark',
    displayName: 'Mysterious Dark',
    shortName: 'Mysterious Dark',
    code: 'mysterious',
    primary: '#9575CD',
    isDark: true,
    variables: mysteriousDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default mysteriousLightTheme;