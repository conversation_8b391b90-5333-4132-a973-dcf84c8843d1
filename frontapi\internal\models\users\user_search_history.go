package users

import (
	"frontapi/internal/models"
)

// SearchHistory 搜索历史表
type UserSearchHistory struct {
	models.BaseModel
	UserID           string `gorm:"column:user_id;type:string;comment:用户ID(可为空表示未登录用户)" json:"user_id"`                                                                           //用户ID(可为空表示未登录用户)
	Keyword          string `gorm:"column:keyword;type:string;size:100;not null;comment:搜索关键词" json:"keyword"`                                                                    //搜索关键词
	SearchType       string `gorm:"column:search_type;type:string;size:20;default:'all';comment:搜索类型:all-全部,video-视频,book-电子书,comic-漫画,picture-图片,shorts-短视频" json:"search_type"` //搜索类型:all-全部,video-视频,book-电子书,comic-漫画,picture-图片,shorts-短视频
	ResultCount      int    `gorm:"column:result_count;type:int;default:0;comment:结果数量" json:"result_count"`                                                                      //结果数量
	HasClick         bool   `gorm:"column:has_click;type:bool;default:false;comment:是否有点击" json:"has_click"`                                                                      //是否有点击
	ClickContentType string `gorm:"column:click_content_type;type:string;size:20;comment:点击内容类型" json:"click_content_type"`                                                       //点击内容类型
	ClickContentID   string `gorm:"column:click_content_id;type:string;comment:点击内容ID" json:"click_content_id"`                                                                   //点击内容ID
	ClickPosition    int    `gorm:"column:click_position;type:int;comment:点击位置" json:"click_position"`                                                                            //点击位置
	IP               string `gorm:"column:ip;type:string;size:50;comment:IP地址" json:"ip"`                                                                                         //IP地址
	Device           string `gorm:"column:device;type:string;size:255;comment:设备信息" json:"device"`                                                                                //设备信息

}

// TableName 表名
func (UserSearchHistory) TableName() string {
	return "ly_user_search_history"
}
