import enTheme from '../en/theme';

export default {
    ...enTheme,

    modern: '현대적인',
    warm: '따뜻한',
    dark: '어두운',
    fresh: '새로운',
    charm: '매력적인',
    mysterious: '신비한',
    light: "밝은",
    blue: "파란색",
    purple: "보라색",
    green: "초록색",
    pink: "분홍색",

    mysteriousLight: "신비한 밝은",
    mysteriousDark: "신비한 어두운",
    modernLight: "현대적인 밝은",
    modernDark: "현대적인 어두운",
    warmLight: "따뜻한 밝은",
    warmDark: "따뜻한 어두운",
    darkLight: "어두운 밝은",
    darkDark: "어두운 어두운",
    freshLight: "새로운 밝은",
    freshDark: "새로운 어두운",
    charmLight: "매력적인 밝은",
    charmDark: "매력적인 어두운",
    themeVariables: "테마 변수",
    componentPreview: "컴포넌트 미리보기",
    buttons: "버튼",
    inputs: "입력 필드",
    cards: "카드",
    cardTitle: "카드 제목",
    cardContent: "이것은 테마 스타일링을 보여주는 샘플 카드 컴포넌트입니다.",
    apiUsage: "API 사용법",

    // 设置界面
    title: "테마 선택",
    selectTheme: "테마 선택",
    themeSettings: "테마 설정",
    changeTheme: "테마 변경",
    followSystem: "시스템 테마 따르기",
    darkMode: "어두운 모드",

    // 描述
    modernDesc: "깔끔하고 현대적인 디자인",
    warmDesc: "따뜻하고 친근한 색상",
    darkDesc: "깔끔한 어두운 인터페이스",
    freshDesc: "새로운 자연스러운 느낌",
    charmDesc: "매력적이고 매력적인",
    mysteriousDesc: "우아하고 신비한",
} as const;