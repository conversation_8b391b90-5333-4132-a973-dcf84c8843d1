package extcollect

import (
	"context"
	"time"

	"frontapi/internal/service/base/extcollect/types"
)

// CollectService 收藏服务核心接口
// 定义所有收藏相关的基础操作
type CollectService interface {
	// 基础收藏操作
	Collect(ctx context.Context, userID, itemID, itemType string) error
	Uncollect(ctx context.Context, userID, itemID, itemType string) error
	IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error)
	GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error)

	// 批量操作
	BatchCollect(ctx context.Context, operations []*types.CollectOperation) error
	BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error
	BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error)
	BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error)

	// 查询操作
	GetUserCollections(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error)
	GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error)
	GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error)

	// 热度排名
	UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error
	GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error)
	GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error)

	// 统计操作
	GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error)
	GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error)
	GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error)

	// 健康检查和关闭
	HealthCheck(ctx context.Context) error
	Close() error
}

// ExtendedCollectService 扩展收藏服务接口
// 提供完整的收藏功能，包括缓存管理、数据同步等高级功能
type ExtendedCollectService interface {
	CollectService

	// 缓存管理
	InvalidateCache(ctx context.Context, keys ...string) error
	WarmupCache(ctx context.Context, itemType string, itemIDs []string) error
	GetCacheStats(ctx context.Context) (map[string]interface{}, error)

	// 数据管理
	CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error
	ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error)
	ImportData(ctx context.Context, data []byte, itemType, format string) error

	// 数据同步
	SyncData(ctx context.Context, itemType string, startTime, endTime time.Time) error
	GetSyncStatus(ctx context.Context) (*types.SyncStatus, error)

	// 服务管理
	GetMetrics(ctx context.Context) (*types.ServiceMetrics, error)
	GetErrorStats(ctx context.Context) (map[string]interface{}, error)
	Shutdown(ctx context.Context) error
}

// CollectAdapter 存储适配器接口
// 定义不同存储后端（Redis/MongoDB）的统一接口
type CollectAdapter interface {
	// 基础操作
	Collect(ctx context.Context, userID, itemID, itemType string) error
	Uncollect(ctx context.Context, userID, itemID, itemType string) error
	IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error)
	GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error)

	// 批量操作
	BatchCollect(ctx context.Context, operations []*types.CollectOperation) error
	BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error
	BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error)
	BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error)

	// 查询操作
	GetUserCollections(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error)
	GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error)
	GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error)

	// 热度排名
	UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error
	GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error)
	GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error)

	// 统计操作
	GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error)
	GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error)
	GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error)

	// 缓存管理
	InvalidateCache(ctx context.Context, keys ...string) error
	WarmupCache(ctx context.Context, itemType string, itemIDs []string) error
	GetCacheStats(ctx context.Context) (map[string]interface{}, error)

	// 数据管理
	CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error
	ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error)
	ImportData(ctx context.Context, data []byte, itemType, format string) error

	// 健康检查
	HealthCheck(ctx context.Context) error
	Close() error
}

// SyncService 数据同步服务接口
type SyncService interface {
	// 同步操作
	Sync(ctx context.Context, itemType string) error
	SyncBatch(ctx context.Context, itemType string, batchSize int) error
	SyncDelta(ctx context.Context, itemType string, since time.Time) error

	// 冲突解决
	ResolveConflicts(ctx context.Context, itemType string) error
	GetConflicts(ctx context.Context, itemType string) ([]types.SyncConflict, error)

	// 状态管理
	GetSyncStatus(ctx context.Context, itemType string) (map[string]interface{}, error)
	SetSyncEnabled(ctx context.Context, itemType string, enabled bool) error
	IsSyncEnabled(ctx context.Context, itemType string) (bool, error)

	// 监控
	GetSyncMetrics(ctx context.Context, itemType string) (map[string]interface{}, error)
	GetLastSyncTime(ctx context.Context, itemType string) (time.Time, error)
	SetLastSyncTime(ctx context.Context, itemType string, t time.Time) error
}

// CacheManager 缓存管理器接口
type CacheManager interface {
	// 缓存操作
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
	Get(ctx context.Context, key string, dest interface{}) error
	Delete(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, key string) (bool, error)

	// 批量操作
	MSet(ctx context.Context, pairs map[string]interface{}, ttl time.Duration) error
	MGet(ctx context.Context, keys []string) (map[string]interface{}, error)
	MDelete(ctx context.Context, keys []string) error

	// 集合操作
	SAdd(ctx context.Context, key string, members ...interface{}) error
	SMembers(ctx context.Context, key string) ([]string, error)
	SIsMember(ctx context.Context, key, member string) (bool, error)
	SRemove(ctx context.Context, key string, members ...interface{}) error

	// 有序集合操作
	ZAdd(ctx context.Context, key string, score float64, member string) error
	ZRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	ZRangeWithScores(ctx context.Context, key string, start, stop int64) (map[string]float64, error)
	ZRemove(ctx context.Context, key string, members ...string) error

	// 计数器操作
	Incr(ctx context.Context, key string) (int64, error)
	Decr(ctx context.Context, key string) (int64, error)
	IncrBy(ctx context.Context, key string, value int64) (int64, error)

	// 统计信息
	GetStats(ctx context.Context) (map[string]interface{}, error)
	Flush(ctx context.Context) error
}

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	// 操作计数
	IncrementCounter(operation string, labels map[string]string)
	DecrementCounter(operation string, labels map[string]string)
	SetGauge(metric string, value float64, labels map[string]string)

	// 性能指标
	RecordLatency(operation string, duration time.Duration, labels map[string]string)
	RecordThroughput(operation string, count int64, labels map[string]string)
	RecordError(operation string, err error, labels map[string]string)

	// 业务指标
	RecordCollectOperation(userID, itemID, itemType, operation string, success bool)
	RecordCacheHit(operation string, hit bool)
	RecordSyncOperation(itemType string, operation string, count int64, success bool)

	// 获取指标
	GetMetrics(ctx context.Context) (*types.ServiceMetrics, error)
	GetOperationStats(ctx context.Context, operation string) (*types.OperationStats, error)
	GetPerformanceMetrics(ctx context.Context, timeRange *types.TimeRange) ([]*types.PerformanceMetrics, error)
}

// HealthChecker 健康检查器接口
type HealthChecker interface {
	// 健康检查
	CheckHealth(ctx context.Context) (*types.HealthStatus, error)
	CheckComponent(ctx context.Context, component string) (string, error)
	CheckDependency(ctx context.Context, dependency string) (string, error)

	// 组件注册
	RegisterComponent(name string, checker func(ctx context.Context) error)
	RegisterDependency(name string, checker func(ctx context.Context) error)
	UnregisterComponent(name string)
	UnregisterDependency(name string)

	// 监控
	GetHealthHistory(ctx context.Context, timeRange *types.TimeRange) ([]*types.HealthStatus, error)
	SetHealthThreshold(component string, threshold float64)
	GetHealthThreshold(component string) float64
}

// StorageBackend 存储后端枚举
type StorageBackend string

const (
	RedisBackend     StorageBackend = "redis"
	MongoBackend     StorageBackend = "mongodb"
	HybridBackend    StorageBackend = "hybrid" // 混合模式
	DualWriteBackend StorageBackend = "dual"   // 双写模式
)

// GenericAdapter 泛型适配器接口
type GenericAdapter[T any] interface {
	// 基础CRUD操作
	Create(ctx context.Context, data *T) error
	Read(ctx context.Context, id string) (*T, error)
	Update(ctx context.Context, id string, data *T) error
	Delete(ctx context.Context, id string) error

	// 批量操作
	BatchCreate(ctx context.Context, data []*T) error
	BatchRead(ctx context.Context, ids []string) ([]*T, error)
	BatchUpdate(ctx context.Context, updates map[string]*T) error
	BatchDelete(ctx context.Context, ids []string) error

	// 查询操作
	Find(ctx context.Context, filter map[string]interface{}, limit, offset int) ([]*T, error)
	Count(ctx context.Context, filter map[string]interface{}) (int64, error)
	Exists(ctx context.Context, filter map[string]interface{}) (bool, error)

	// 聚合操作
	Aggregate(ctx context.Context, pipeline []map[string]interface{}) ([]map[string]interface{}, error)
	Group(ctx context.Context, field string, filter map[string]interface{}) (map[string]int64, error)

	// 索引操作
	CreateIndex(ctx context.Context, fields []string, unique bool) error
	DropIndex(ctx context.Context, name string) error
	ListIndexes(ctx context.Context) ([]string, error)

	// 统计操作
	GetStats(ctx context.Context) (map[string]interface{}, error)
	GetCollectionStats(ctx context.Context) (map[string]interface{}, error)
}

// Observer 观察者接口
type Observer interface {
	OnCollect(ctx context.Context, userID, itemID, itemType string)
	OnUncollect(ctx context.Context, userID, itemID, itemType string)
	OnError(ctx context.Context, operation string, err error)
	OnMetrics(ctx context.Context, metrics *types.ServiceMetrics)
}

// EventPublisher 事件发布器接口
type EventPublisher interface {
	// 事件发布
	PublishCollectEvent(ctx context.Context, event *types.CollectEvent) error
	PublishUncollectEvent(ctx context.Context, event *types.UncollectEvent) error
	PublishBatchEvent(ctx context.Context, event *types.BatchEvent) error

	// 订阅管理
	Subscribe(topic string, handler func(event interface{})) error
	Unsubscribe(topic string, handler func(event interface{})) error

	// 事件历史
	GetEventHistory(ctx context.Context, topic string, limit int) ([]interface{}, error)
	GetEventStats(ctx context.Context) (map[string]interface{}, error)
}
