# 管理后台认证系统设置指南

## 概述

本文档说明如何设置和测试管理后台的认证系统，该系统现在使用MySQL真实数据、Redis存储会话，并支持Casbin权限管理。

## 系统架构

### 认证流程
1. **登录**: 用户通过用户名/密码登录
2. **JWT生成**: 成功登录后生成JWT令牌
3. **Redis存储**: 用户会话信息存储在Redis中
4. **权限验证**: 每个请求通过中间件验证令牌和权限

### 技术栈
- **后端**: Go + Fiber v2
- **数据库**: MySQL (用户数据)
- **缓存**: Redis (会话存储)
- **权限**: <PERSON><PERSON>bin (RBAC权限控制)
- **认证**: JWT令牌

## 配置要求

### 1. 环境变量设置

确保以下配置项存在于`.env`文件中：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=lyvideos

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 管理后台Redis配置
REDIS_ADMIN_DB=1
REDIS_ADMIN_PREFIX=admin:

# JWT配置
JWT_SECRET=your-secret-key
JWT_EXPIRY=168

# 服务器配置
ADMIN_SERVER_PORT=8081
```

### 2. 数据库要求

确保MySQL中存在以下表：
- `ly_admin_users` (管理员用户表)
- 其他相关权限表

### 3. Redis要求

确保Redis服务运行在指定端口，管理后台将使用独立的数据库(DB=1)。

## 启动服务

### 方法1: 使用启动脚本
```bash
# Windows
start_admin.bat

# Linux/Mac
chmod +x start_admin.sh
./start_admin.sh
```

### 方法2: 直接运行
```bash
cd frontapi
go run cmd/admin/main.go
```

服务将启动在 `http://localhost:8081`

## API接口

### 认证相关接口

#### 1. 用户登录
```http
POST http://localhost:8081/api/proadm/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456",
    "rememberMe": false
}
```

**响应示例:**
```json
{
    "code": 2000,
    "message": "登录成功",
    "data": {
        "id": 1,
        "username": "admin",
        "nickname": "管理员",
        "avatar": "",
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
}
```

#### 2. 获取用户信息
```http
POST http://localhost:8081/api/proadm/auth/getUserInfo
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**响应示例:**
```json
{
    "code": 2000,
    "message": "获取用户信息成功",
    "data": {
        "userId": 1,
        "userName": "admin",
        "nickname": "管理员",
        "avatar": "",
        "email": "<EMAIL>",
        "phone": "",
        "roles": ["admin"],
        "permissions": ["user:view", "user:create"]
    }
}
```

#### 3. 刷新令牌
```http
POST http://localhost:8081/api/proadm/auth/refreshToken
Content-Type: application/json

{
    "refreshToken": "YOUR_REFRESH_TOKEN"
}
```

#### 4. 退出登录
```http
POST http://localhost:8081/api/proadm/auth/logout
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 故障排除

### 1. 401 Unauthorized 错误

**可能原因:**
- Token过期或无效
- Redis中会话不存在
- 用户不存在

**解决方法:**
1. 检查token是否正确传递
2. 验证Redis连接和数据
3. 重新登录获取新token

### 2. Redis连接失败

**检查项:**
- Redis服务是否运行
- 端口是否正确
- 数据库索引是否配置正确

### 3. 数据库连接失败

**检查项:**
- MySQL服务是否运行
- 数据库配置是否正确
- 用户表是否存在

## 测试步骤

### 1. 基础功能测试

```bash
# 1. 启动服务
go run cmd/admin/main.go

# 2. 测试登录
curl -X POST http://localhost:8081/api/proadm/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'

# 3. 使用返回的token测试获取用户信息
curl -X POST http://localhost:8081/api/proadm/auth/getUserInfo \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. 前端集成测试

确保前端管理系统：
1. 正确发送Authorization头部
2. 处理401错误并重定向到登录页
3. 正确存储和使用token

## 开发注意事项

### 1. 安全考虑
- JWT密钥应该足够复杂
- 令牌过期时间应合理设置
- 敏感操作应要求重新验证

### 2. 性能优化
- Redis连接池配置
- 令牌过期时间平衡
- 数据库查询优化

### 3. 监控和日志
- 认证失败次数监控
- 会话创建/销毁日志
- 异常访问检测

## 下一步计划

1. 集成Casbin权限管理
2. 添加多角色支持
3. 实现细粒度权限控制
4. 添加审计日志功能 