package database

import (
	"fmt"
	"log"
	"time"

	"gorm.io/gorm/schema"

	"frontapi/config"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB 初始化数据库
func InitDB() error {
	// 调用MySQL初始化
	InitMySQL()
	return nil
}

// InitMySQL 初始化MySQL数据库连接
func InitMySQL() {
	// 使用配置系统获取DSN
	dsn := config.GetDSN()

	// 配置GORM日志
	gormLogger := logger.New(
		log.New(log.Writer(), "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)
	fmt.Println("数据库连接DSN:", dsn)

	// 打开数据库连接
	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		SkipDefaultTransaction: true, // 禁用默认事务
		Logger:                 gormLogger,
	})
	if err != nil {
		log.Fatalf("无法连接到数据库: %v", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatalf("无法获取数据库连接池: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(config.AppConfig.Database.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.AppConfig.Database.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("数据库连接成功")
}

// Close 关闭数据库连接
func Close() {
	if DB == nil {
		return
	}

	sqlDB, err := DB.DB()
	if err != nil {
		log.Printf("获取数据库连接失败: %v", err)
		return
	}

	log.Println("正在关闭数据库连接...")
	err = sqlDB.Close()
	if err != nil {
		log.Printf("关闭数据库连接失败: %v", err)
		return
	}

	log.Println("数据库连接已关闭")
}
