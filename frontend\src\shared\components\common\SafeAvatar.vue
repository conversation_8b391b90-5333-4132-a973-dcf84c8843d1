<template>
  <div 
    :class="[
      'safe-avatar',
      `safe-avatar--${shape}`,
      `safe-avatar--${size}`,
      { 'safe-avatar--loading': isLoading }
    ]"
    :style="customStyle"
  >
    <!-- 头像图片 -->
    <img
      v-if="!isError && currentSrc"
      :src="currentSrc"
      :alt="alt"
      class="avatar-img"
      @load="handleLoad"
      @error="handleError"
    />
    
    <!-- 加载中状态 -->
    <div v-else-if="isLoading" class="avatar-loading">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 错误状态或默认状态 -->
    <div v-else class="avatar-placeholder">
      <slot name="placeholder">
        <svg viewBox="0 0 24 24" fill="currentColor" class="placeholder-icon">
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      </slot>
    </div>
    
    <!-- 认证徽章 -->
    <div v-if="showVerified && verified" class="verified-badge">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 6.5V4.5C15 3.4 14.6 2.4 13.9 1.7L12 0L10.1 1.7C9.4 2.4 9 3.4 9 4.5V6.5L3 7V9L9 9.5V11.5C9 12.6 9.4 13.6 10.1 14.3L12 16L13.9 14.3C14.6 13.6 15 12.6 15 11.5V9.5L21 9ZM12 8L14 10L10 14L6 10L8 8L10 10L12 8Z"/>
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { getSafeAvatarUrl, getModuloAvatarUrl } from '@/utils/avatar'

interface Props {
  src?: string
  userId?: string
  alt?: string
  size?: 'small' | 'medium' | 'large' | number
  shape?: 'circle' | 'square'
  verified?: boolean
  showVerified?: boolean
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'
  lazy?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  alt: '用户头像',
  size: 'medium',
  shape: 'circle',
  verified: false,
  showVerified: true,
  fit: 'cover',
  lazy: false
})

const emit = defineEmits<{
  load: [event: Event]
  error: [event: Event]
}>()

// 响应式状态
const currentSrc = ref('')
const isLoading = ref(false)
const isError = ref(false)
const hasTriedFallback = ref(false)

// 计算自定义样式
const customStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (typeof props.size === 'number') {
    style.width = `${props.size}px`
    style.height = `${props.size}px`
  }
  
  return style
})

// 尺寸类名映射
const sizeMap = {
  small: '32px',
  medium: '40px', 
  large: '64px'
}

// 加载头像
const loadAvatar = async () => {
  if (!props.src && !props.userId) {
    currentSrc.value = ''
    isLoading.value = false
    return
  }
  
  isLoading.value = true
  isError.value = false
  hasTriedFallback.value = false
  
  try {
    const safeUrl = await getSafeAvatarUrl(props.src, props.userId)
    currentSrc.value = safeUrl
  } catch (error) {
    handleError()
  }
}

// 处理图片加载成功
const handleLoad = (event: Event) => {
  isLoading.value = false
  isError.value = false
  emit('load', event)
}

// 处理图片加载失败
const handleError = (event?: Event) => {
  if (event) {
    emit('error', event)
  }
  
  // 如果还没有尝试过回退头像，则尝试使用取模头像
  if (!hasTriedFallback.value && props.userId) {
    hasTriedFallback.value = true
    currentSrc.value = getModuloAvatarUrl(props.userId)
    return
  }
  
  // 如果回退也失败了，显示占位符
  isLoading.value = false
  isError.value = true
  currentSrc.value = ''
}

// 监听属性变化
watch([() => props.src, () => props.userId], () => {
  loadAvatar()
}, { immediate: false })

// 组件挂载时加载头像
onMounted(() => {
  loadAvatar()
})
</script>

<style scoped lang="scss">
.safe-avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  color: #c0c4cc;
  background: #f5f7fa;
  
  // 尺寸
  &--small {
    width: 32px;
    height: 32px;
  }
  
  &--medium {
    width: 40px;
    height: 40px;
  }
  
  &--large {
    width: 64px;
    height: 64px;
  }
  
  // 形状
  &--circle {
    border-radius: 50%;
  }
  
  &--square {
    border-radius: 6px;
  }
  
  // 加载状态
  &--loading {
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(242, 242, 242, 0.5) 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: loading 1.4s ease infinite;
  }
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.avatar-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  
  .placeholder-icon {
    width: 50%;
    height: 50%;
  }
}

.verified-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  background: #409eff;
  border-radius: 50%;
  border: 2px solid #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  
  svg {
    width: 10px;
    height: 10px;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Element Plus风格的hover效果
.safe-avatar:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.3s ease;
}
</style> 