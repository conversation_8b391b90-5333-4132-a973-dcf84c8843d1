package videos

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/videos"

	"github.com/gofiber/fiber/v2"
)

// ==============================
// 视频评论相关方法
// ==============================
type VideoCommentController struct {
	admin.BaseController
	videoCommentService videos.VideoCommentService
}

func NewVideoCommentController(
	commentService videos.VideoCommentService,
) *VideoCommentController {
	return &VideoCommentController{
		videoCommentService: commentService,
	}
}

// GetVideoCommentList 获取视频评论列表
func (c *VideoCommentController) GetVideoCommentList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	videoID := reqInfo.Get("video_id").GetString()
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	sortBy := reqInfo.Get("sort_by").GetString()
	condition := map[string]interface{}{
		"video_id": videoID,
	}
	comments, total, err := c.videoCommentService.List(ctx.Context(), condition, sortBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.SuccessList(ctx, comments, total, page, pageSize)

}
