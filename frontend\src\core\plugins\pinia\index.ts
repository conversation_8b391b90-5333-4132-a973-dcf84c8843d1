/**
 * Pinia状态管理插件 - 提供完整的状态管理功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'
import { createPinia, type Pinia, type PiniaPluginContext } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useStorage } from '@vueuse/core'

/**
 * Pinia插件配置
 */
export interface PiniaPluginConfig {
  enableDevtools?: boolean
  enablePersistence?: boolean
  persistenceKey?: string
  enableLogging?: boolean
  enableTimeTravel?: boolean
  maxHistorySize?: number
}

/**
 * 状态持久化配置
 */
export interface PersistenceConfig {
  key: string
  storage?: Storage
  paths?: string[]
  beforeRestore?: (context: PiniaPluginContext) => void
  afterRestore?: (context: PiniaPluginContext) => void
}

/**
 * 状态历史记录
 */
export interface StateHistory {
  timestamp: number
  storeId: string
  action: string
  state: any
  payload?: any
}

/**
 * Pinia管理器类
 */
export class PiniaManager {
  private pinia: Pinia | null = null
  private config: PiniaPluginConfig
  private stateHistory = ref<StateHistory[]>([])
  private maxHistorySize: number
  private persistedStores = new Set<string>()

  constructor(config: PiniaPluginConfig = {}) {
    this.config = {
      enableDevtools: true,
      enablePersistence: true,
      persistenceKey: 'pinia-state',
      enableLogging: false,
      enableTimeTravel: false,
      maxHistorySize: 50,
      ...config
    }
    this.maxHistorySize = this.config.maxHistorySize || 50
  }

  /**
   * 创建Pinia实例
   */
  createPinia(): Pinia {
    this.pinia = createPinia()

    // 添加插件
    if (this.config.enablePersistence) {
      this.pinia.use(this.createPersistencePlugin())
    }

    if (this.config.enableLogging) {
      this.pinia.use(this.createLoggingPlugin())
    }

    if (this.config.enableTimeTravel) {
      this.pinia.use(this.createTimeTravelPlugin())
    }

    return this.pinia
  }

  /**
   * 创建状态持久化插件
   */
  private createPersistencePlugin() {
    return (context: PiniaPluginContext) => {
      const { store, options } = context
      
      // 检查是否需要持久化
      const persistConfig = options.persist as PersistenceConfig | boolean | undefined
      
      if (!persistConfig) return

      const config: PersistenceConfig = typeof persistConfig === 'boolean' 
        ? { key: `${this.config.persistenceKey}-${store.$id}` }
        : { key: `${this.config.persistenceKey}-${store.$id}`, ...persistConfig }

      const storage = config.storage || localStorage
      const storageKey = config.key

      // 恢复状态
      try {
        const savedState = storage.getItem(storageKey)
        if (savedState) {
          const parsedState = JSON.parse(savedState)
          
          // 执行恢复前回调
          config.beforeRestore?.(context)
          
          // 恢复指定路径的状态或全部状态
          if (config.paths) {
            config.paths.forEach(path => {
              if (parsedState[path] !== undefined) {
                store.$patch({ [path]: parsedState[path] })
              }
            })
          } else {
            store.$patch(parsedState)
          }
          
          // 执行恢复后回调
          config.afterRestore?.(context)
          
          this.persistedStores.add(store.$id)
        }
      } catch (error) {
        console.error(`Failed to restore state for store ${store.$id}:`, error)
      }

      // 监听状态变化并保存
      store.$subscribe((mutation, state) => {
        try {
          const stateToSave = config.paths 
            ? config.paths.reduce((acc, path) => {
                acc[path] = state[path]
                return acc
              }, {} as any)
            : state
            
          storage.setItem(storageKey, JSON.stringify(stateToSave))
        } catch (error) {
          console.error(`Failed to persist state for store ${store.$id}:`, error)
        }
      })
    }
  }

  /**
   * 创建日志插件
   */
  private createLoggingPlugin() {
    return (context: PiniaPluginContext) => {
      const { store } = context

      store.$onAction(({ name, args, after, onError }) => {
        const startTime = Date.now()
        console.group(`🏪 ${store.$id} - ${name}`)
        console.log('📥 Arguments:', args)
        console.log('📊 State before:', { ...store.$state })

        after((result) => {
          const duration = Date.now() - startTime
          console.log('📤 Result:', result)
          console.log('📊 State after:', { ...store.$state })
          console.log(`⏱️ Duration: ${duration}ms`)
          console.groupEnd()
        })

        onError((error) => {
          const duration = Date.now() - startTime
          console.error('❌ Error:', error)
          console.log(`⏱️ Duration: ${duration}ms`)
          console.groupEnd()
        })
      })
    }
  }

  /**
   * 创建时间旅行插件
   */
  private createTimeTravelPlugin() {
    return (context: PiniaPluginContext) => {
      const { store } = context

      // 记录初始状态
      this.addToHistory({
        timestamp: Date.now(),
        storeId: store.$id,
        action: 'INIT',
        state: { ...store.$state }
      })

      // 监听动作
      store.$onAction(({ name, args, after }) => {
        after(() => {
          this.addToHistory({
            timestamp: Date.now(),
            storeId: store.$id,
            action: name,
            state: { ...store.$state },
            payload: args
          })
        })
      })

      // 监听状态变化
      store.$subscribe((mutation, state) => {
        if (mutation.type === 'direct') {
          this.addToHistory({
            timestamp: Date.now(),
            storeId: store.$id,
            action: 'MUTATION',
            state: { ...state }
          })
        }
      })
    }
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(entry: StateHistory) {
    this.stateHistory.value.push(entry)
    
    // 限制历史记录大小
    if (this.stateHistory.value.length > this.maxHistorySize) {
      this.stateHistory.value.shift()
    }
  }

  /**
   * 获取状态历史
   */
  get history() {
    return computed(() => this.stateHistory.value)
  }

  /**
   * 获取指定store的历史
   */
  getStoreHistory(storeId: string) {
    return computed(() => 
      this.stateHistory.value.filter(entry => entry.storeId === storeId)
    )
  }

  /**
   * 时间旅行到指定状态
   */
  travelTo(timestamp: number, storeId?: string) {
    if (!this.pinia) return

    const targetEntry = this.stateHistory.value.find(entry => 
      entry.timestamp === timestamp && (!storeId || entry.storeId === storeId)
    )

    if (targetEntry) {
      const store = this.pinia._s.get(targetEntry.storeId)
      if (store) {
        store.$patch(targetEntry.state)
        console.log(`🕰️ Traveled to ${new Date(timestamp).toISOString()} for store ${targetEntry.storeId}`)
      }
    }
  }

  /**
   * 清除历史记录
   */
  clearHistory(storeId?: string) {
    if (storeId) {
      this.stateHistory.value = this.stateHistory.value.filter(
        entry => entry.storeId !== storeId
      )
    } else {
      this.stateHistory.value = []
    }
  }

  /**
   * 导出状态
   */
  exportState(storeId?: string) {
    if (!this.pinia) return null

    if (storeId) {
      const store = this.pinia._s.get(storeId)
      return store ? { [storeId]: store.$state } : null
    }

    const allStates: Record<string, any> = {}
    this.pinia._s.forEach((store, id) => {
      allStates[id] = store.$state
    })
    return allStates
  }

  /**
   * 导入状态
   */
  importState(states: Record<string, any>) {
    if (!this.pinia) return

    Object.entries(states).forEach(([storeId, state]) => {
      const store = this.pinia!._s.get(storeId)
      if (store) {
        store.$patch(state)
      }
    })
  }

  /**
   * 重置store状态
   */
  resetStore(storeId: string) {
    if (!this.pinia) return

    const store = this.pinia._s.get(storeId)
    if (store) {
      store.$reset()
      
      // 清除持久化数据
      if (this.persistedStores.has(storeId)) {
        try {
          localStorage.removeItem(`${this.config.persistenceKey}-${storeId}`)
        } catch (error) {
          console.error(`Failed to clear persisted state for store ${storeId}:`, error)
        }
      }
    }
  }

  /**
   * 重置所有store
   */
  resetAllStores() {
    if (!this.pinia) return

    this.pinia._s.forEach((store, storeId) => {
      store.$reset()
      
      // 清除持久化数据
      if (this.persistedStores.has(storeId)) {
        try {
          localStorage.removeItem(`${this.config.persistenceKey}-${storeId}`)
        } catch (error) {
          console.error(`Failed to clear persisted state for store ${storeId}:`, error)
        }
      }
    })

    // 清除历史记录
    this.clearHistory()
  }

  /**
   * 获取所有store信息
   */
  getStoresInfo() {
    if (!this.pinia) return []

    const stores: Array<{
      id: string
      state: any
      actions: string[]
      getters: string[]
      isPersisted: boolean
    }> = []

    this.pinia._s.forEach((store, id) => {
      const storeInfo = {
        id,
        state: store.$state,
        actions: Object.getOwnPropertyNames(store).filter(
          key => typeof store[key] === 'function' && !key.startsWith('$')
        ),
        getters: Object.getOwnPropertyNames(store).filter(
          key => typeof store[key] !== 'function' && !key.startsWith('$') && key !== 'state'
        ),
        isPersisted: this.persistedStores.has(id)
      }
      stores.push(storeInfo)
    })

    return stores
  }

  /**
   * 启用/禁用功能
   */
  toggleFeature(feature: keyof PiniaPluginConfig, enabled: boolean) {
    this.config[feature] = enabled as any
  }

  /**
   * 获取配置
   */
  get configuration() {
    return { ...this.config }
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.pinia = null
    this.stateHistory.value = []
    this.persistedStores.clear()
  }
}

// 全局Pinia管理器实例
export const piniaManager = new PiniaManager()

/**
 * Pinia插件
 */
export const piniaPlugin: Plugin = createPlugin({
  meta: {
    name: 'pinia',
    version: '1.0.0',
    description: 'Pinia状态管理插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 10 // 高优先级，确保在其他插件之前安装
  },
  install(context: PluginContext) {
    // 创建Pinia实例
    const pinia = piniaManager.createPinia()
    
    // 安装到Vue应用
    context.app.use(pinia)
    
    // 将Pinia管理器添加到全局属性
    context.app.config.globalProperties.$piniaManager = piniaManager
    
    // 提供Pinia管理器
    context.app.provide('piniaManager', piniaManager)
    
    console.log('Pinia plugin installed')
  },
  uninstall() {
    piniaManager.destroy()
    console.log('Pinia plugin uninstalled')
  }
})

/**
 * 使用Pinia管理器的组合式函数
 */
export function usePiniaManager() {
  return {
    piniaManager,
    history: piniaManager.history,
    getStoreHistory: piniaManager.getStoreHistory.bind(piniaManager),
    travelTo: piniaManager.travelTo.bind(piniaManager),
    clearHistory: piniaManager.clearHistory.bind(piniaManager),
    exportState: piniaManager.exportState.bind(piniaManager),
    importState: piniaManager.importState.bind(piniaManager),
    resetStore: piniaManager.resetStore.bind(piniaManager),
    resetAllStores: piniaManager.resetAllStores.bind(piniaManager),
    getStoresInfo: piniaManager.getStoresInfo.bind(piniaManager),
    toggleFeature: piniaManager.toggleFeature.bind(piniaManager),
    configuration: piniaManager.configuration
  }
}

/**
 * 创建持久化store的辅助函数
 */
export function createPersistedStore<T extends Record<string, any>>(
  storeDefinition: any,
  persistConfig?: PersistenceConfig | boolean
) {
  return {
    ...storeDefinition,
    persist: persistConfig || true
  }
}

/**
 * 状态管理工具函数
 */
export const storeUtils = {
  /**
   * 深度克隆状态
   */
  cloneState<T>(state: T): T {
    return JSON.parse(JSON.stringify(state))
  },

  /**
   * 比较两个状态是否相等
   */
  isStateEqual<T>(state1: T, state2: T): boolean {
    return JSON.stringify(state1) === JSON.stringify(state2)
  },

  /**
   * 合并状态
   */
  mergeState<T extends Record<string, any>>(target: T, source: Partial<T>): T {
    return { ...target, ...source }
  },

  /**
   * 获取状态差异
   */
  getStateDiff<T extends Record<string, any>>(oldState: T, newState: T): Partial<T> {
    const diff: Partial<T> = {}
    
    Object.keys(newState).forEach(key => {
      if (oldState[key] !== newState[key]) {
        diff[key] = newState[key]
      }
    })
    
    return diff
  },

  /**
   * 验证状态结构
   */
  validateState<T>(state: any, schema: Record<keyof T, string>): boolean {
    return Object.keys(schema).every(key => {
      const expectedType = schema[key]
      const actualType = typeof state[key]
      return actualType === expectedType
    })
  }
}

export default piniaPlugin