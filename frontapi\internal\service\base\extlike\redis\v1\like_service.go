package v1

import (
	"context"
	"fmt"
	"frontapi/internal/service/base/extlike/types"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
)

// LikeService Redis v1 点赞服务接口
type LikeService interface {
	// 基础操作
	LikeItem(ctx context.Context, userID, itemType, itemID string) error
	UnlikeItem(ctx context.Context, userID, itemType, itemID string) error
	IsLiked(ctx context.Context, userID, itemType, itemID string) (bool, error)

	// 统计操作
	GetLikeCount(ctx context.Context, itemType, itemID string) (int64, error)
	GetUserLikes(ctx context.Context, userID, itemType string) ([]string, error)
	GetItemLikedUsers(ctx context.Context, itemType, itemID string) ([]string, error)

	// 批量操作
	BatchLike(ctx context.Context, itemType string, operations []LikeOperation) error
	BatchGetLikeStatus(ctx context.Context, itemType, userID string, itemIDs []string) (map[string]bool, error)
	BatchGetLikeCounts(ctx context.Context, itemType string, itemIDs []string) (map[string]int64, error)

	// 热门排行
	GetHotItems(ctx context.Context, itemType string, limit int) ([]types.HotItem, error)
	UpdateHotRank(ctx context.Context, itemType, itemID string, score float64) error

	// 复杂查询
	GetUserLikeHistory(ctx context.Context, userID string, itemType string, limit, offset int) ([]*LikeRecord, error)
	GetItemLikeHistory(ctx context.Context, itemType, itemID string, limit, offset int) ([]*LikeRecord, error)
	GetLikeTrends(ctx context.Context, itemType, itemID string, timeRange TimeRange) ([]*LikeTrend, error)

	// 统计分析
	GetUserLikeStats(ctx context.Context, userID string, timeRange TimeRange) (*UserLikeStats, error)
	GetItemLikeStats(ctx context.Context, itemType, itemID string, timeRange TimeRange) (*ItemLikeStats, error)
	GetGlobalLikeStats(ctx context.Context, itemType string, timeRange TimeRange) (*GlobalMongoStats, error)

	// 排行榜
	GetTopLikedItems(ctx context.Context, itemType string, timeRange TimeRange, limit int) ([]*TopLikedItem, error)
	GetMostActiveLikers(ctx context.Context, itemType string, timeRange TimeRange, limit int) ([]*ActiveLiker, error)

	// 缓存管理
	FlushCache(ctx context.Context, itemType string) error
	GetCacheStats(ctx context.Context) (*CacheStats, error)

	// 数据同步
	SyncFromRedis(ctx context.Context, itemType string) error
	SyncToRedis(ctx context.Context, itemType string) error
}

// likeService Redis v1 点赞服务实现
type likeService struct {
	adapter *RedisAdapter
}

// NewLikeService 创建Redis v1点赞服务实例
// @param redisClient Redis客户端
// @param cacheTTL 缓存过期时间
// @return LikeService 点赞服务接口
func NewLikeService(redisClient *redis.Client, cacheTTL time.Duration) LikeService {
	return &likeService{
		adapter: NewRedisAdapter(redisClient, cacheTTL),
	}
}

// LikeItem 点赞
// @param ctx 上下文
// @param userID 用户ID
// @param itemType 项目类型
// @param itemID 项目ID
// @return error 错误信息
func (s *likeService) LikeItem(ctx context.Context, userID, itemType, itemID string) error {
	return s.adapter.LikeItem(ctx, userID, itemType, itemID)
}

// UnlikeItem 取消点赞
// @param ctx 上下文
// @param userID 用户ID
// @param itemType 项目类型
// @param itemID 项目ID
// @return error 错误信息
func (s *likeService) UnlikeItem(ctx context.Context, userID, itemType, itemID string) error {
	return s.adapter.UnlikeItem(ctx, userID, itemType, itemID)
}

// IsLiked 检查是否已点赞
// @param ctx 上下文
// @param userID 用户ID
// @param itemType 项目类型
// @param itemID 项目ID
// @return bool 是否已点赞
// @return error 错误信息
func (s *likeService) IsLiked(ctx context.Context, userID, itemType, itemID string) (bool, error) {
	userKey := s.adapter.generateUserLikeKey(userID, itemType)
	result, err := s.adapter.redisClient.SIsMember(ctx, userKey, itemID).Result()
	if err != nil {
		s.adapter.stats.MissCount++
		return false, err
	}
	s.adapter.stats.HitCount++
	s.updateHitRate()
	return result, nil
}

// GetLikeCount 获取点赞数
// @param ctx 上下文
// @param itemType 项目类型
// @param itemID 项目ID
// @return int64 点赞数
// @return error 错误信息
func (s *likeService) GetLikeCount(ctx context.Context, itemType, itemID string) (int64, error) {
	itemKey := s.adapter.generateItemLikeKey(itemType, itemID)
	result, err := s.adapter.redisClient.Get(ctx, itemKey).Result()
	if err != nil {
		if err == redis.Nil {
			s.adapter.stats.MissCount++
			s.updateHitRate()
			return 0, nil
		}
		s.adapter.stats.MissCount++
		s.updateHitRate()
		return 0, err
	}
	s.adapter.stats.HitCount++
	s.updateHitRate()
	count, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// GetUserLikes 获取用户点赞列表
// @param ctx 上下文
// @param userID 用户ID
// @param itemType 项目类型
// @return []string 点赞项目ID列表
// @return error 错误信息
func (s *likeService) GetUserLikes(ctx context.Context, userID, itemType string) ([]string, error) {
	userKey := s.adapter.generateUserLikeKey(userID, itemType)
	result, err := s.adapter.redisClient.SMembers(ctx, userKey).Result()
	if err != nil {
		s.adapter.stats.MissCount++
		s.updateHitRate()
		return []string{}, err
	}
	s.adapter.stats.HitCount++
	s.updateHitRate()
	return result, nil
}

// GetItemLikedUsers 获取项目点赞用户列表
// @param ctx 上下文
// @param itemType 项目类型
// @param itemID 项目ID
// @return []string 点赞用户ID列表
// @return error 错误信息
func (s *likeService) GetItemLikedUsers(ctx context.Context, itemType, itemID string) ([]string, error) {
	// TODO: 实现获取项目点赞用户列表逻辑
	// 这需要额外的数据结构来存储反向索引
	return []string{}, nil
}

// GetUserLikeStats 获取用户点赞统计
// @param ctx 上下文
// @param userID 用户ID
// @param timeRange 时间范围
// @return *UserLikeStats 用户点赞统计
// @return error 错误信息
func (s *likeService) GetUserLikeStats(ctx context.Context, userID string, timeRange *TimeRange) (*UserLikeStats, error) {
	// TODO: 实现用户点赞统计逻辑
	return &UserLikeStats{
		UserID:             userID,
		TotalLikes:         0,
		TotalUnlikes:       0,
		MostLikedType:      "",
		AverageLikesPerDay: 0,
		FirstLikeTime:      time.Now(),
		LastLikeTime:       time.Now(),
	}, nil
}

// GetItemLikeStats 获取项目点赞统计
// @param ctx 上下文
// @param itemType 项目类型
// @param itemID 项目ID
// @param timeRange 时间范围
// @return *ItemLikeStats 项目点赞统计
// @return error 错误信息
func (s *likeService) GetItemLikeStats(ctx context.Context, itemType, itemID string, timeRange *TimeRange) (*ItemLikeStats, error) {
	// TODO: 实现项目点赞统计逻辑
	return &ItemLikeStats{
		ItemType:           itemType,
		ItemID:             itemID,
		TotalLikes:         0,
		TotalUnlikes:       0,
		UniqueUsers:        0,
		AverageLikesPerDay: 0,
		PeakLikeTime:       time.Now(),
		FirstLikeTime:      time.Now(),
		LastLikeTime:       time.Now(),
	}, nil
}

// GetGlobalLikeStats 获取全局点赞统计
// @param ctx 上下文
// @param itemType 项目类型
// @param timeRange 时间范围
// @return *GlobalLikeStats 全局点赞统计
// @return error 错误信息
func (s *likeService) GetGlobalLikeStats(ctx context.Context, itemType string, timeRange *TimeRange) (*GlobalLikeStats, error) {
	// TODO: 实现全局点赞统计逻辑
	return &GlobalLikeStats{
		ItemType:            itemType,
		TotalLikes:          0,
		TotalUnlikes:        0,
		TotalItems:          0,
		TotalUsers:          0,
		AverageLikesPerItem: 0,
		AverageLikesPerUser: 0,
		MostLikedItem:       "",
		MostActiveUser:      "",
		MostActiveHour:      0,
		MostActiveDay:       "",
		PeakLikeTime:        time.Now(),
	}, nil
}

// BatchLike 批量点赞
// @param ctx 上下文
// @param operations 批量操作列表
// @return error 错误信息
func (s *likeService) BatchLike(ctx context.Context, operations []LikeOperation) error {
	pipe := s.adapter.redisClient.TxPipeline()

	for _, op := range operations {
		if op.Action == "like" {
			userKey := s.adapter.generateUserLikeKey(op.UserID, "default")
			itemKey := s.adapter.generateItemLikeKey("default", op.ItemID)
			hotKey := s.adapter.generateHotRankKey("default")

			pipe.SAdd(ctx, userKey, op.ItemID)
			pipe.Incr(ctx, itemKey)
			pipe.ZIncrBy(ctx, hotKey, 1, op.ItemID)
		} else if op.Action == "unlike" {
			userKey := s.adapter.generateUserLikeKey(op.UserID, "default")
			itemKey := s.adapter.generateItemLikeKey("default", op.ItemID)
			hotKey := s.adapter.generateHotRankKey("default")

			pipe.SRem(ctx, userKey, op.ItemID)
			pipe.Decr(ctx, itemKey)
			pipe.ZIncrBy(ctx, hotKey, -1, op.ItemID)
		}
	}

	_, err := pipe.Exec(ctx)
	return err
}

// BatchGetLikeCounts 批量获取点赞数
// @param ctx 上下文
// @param itemType 项目类型
// @param itemIDs 项目ID列表
// @return map[string]int64 项目ID到点赞数的映射
// @return error 错误信息
func (s *likeService) BatchGetLikeCounts(ctx context.Context, itemType string, itemIDs []string) (map[string]int64, error) {
	result := make(map[string]int64)
	pipe := s.adapter.redisClient.Pipeline()

	for _, itemID := range itemIDs {
		itemKey := s.adapter.generateItemLikeKey(itemType, itemID)
		pipe.Get(ctx, itemKey)
	}

	cmds, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return result, err
	}

	for i, cmd := range cmds {
		if i < len(itemIDs) {
			val, err := cmd.(*redis.StringCmd).Result()
			if err == redis.Nil {
				result[itemIDs[i]] = 0
			} else if err != nil {
				result[itemIDs[i]] = 0
			} else {
				count, parseErr := strconv.ParseInt(val, 10, 64)
				if parseErr != nil {
					result[itemIDs[i]] = 0
				} else {
					result[itemIDs[i]] = count
				}
			}
		}
	}

	return result, nil
}

// GetHotItems 获取热门项目
// @param ctx 上下文
// @param itemType 项目类型
// @param limit 限制数量
// @return []HotItem 热门项目列表
// @return error 错误信息
func (s *likeService) GetHotItems(ctx context.Context, itemType string, limit int) ([]HotItem, error) {
	hotKey := s.adapter.generateHotRankKey(itemType)
	result, err := s.adapter.redisClient.ZRevRangeWithScores(ctx, hotKey, 0, int64(limit-1)).Result()
	if err != nil {
		s.adapter.stats.MissCount++
		s.updateHitRate()
		return []HotItem{}, err
	}

	s.adapter.stats.HitCount++
	s.updateHitRate()

	hotItems := make([]HotItem, 0, len(result))
	for i, z := range result {
		hotItems = append(hotItems, HotItem{
			ItemID:    z.Member.(string),
			ItemType:  itemType,
			LikeCount: int64(z.Score),
			Score:     z.Score,
			Rank:      i + 1,
			UpdatedAt: time.Now(),
		})
	}

	return hotItems, nil
}

// UpdateHotRank 更新热门排行
// @param ctx 上下文
// @param itemType 项目类型
// @param itemID 项目ID
// @param score 分数
// @return error 错误信息
func (s *likeService) UpdateHotRank(ctx context.Context, itemType, itemID string, score float64) error {
	hotKey := s.adapter.generateHotRankKey(itemType)
	return s.adapter.redisClient.ZAdd(ctx, hotKey, &redis.Z{
		Score:  score,
		Member: itemID,
	}).Err()
}

// FlushCache 清空缓存
// @param ctx 上下文
// @param itemType 项目类型
// @return error 错误信息
func (s *likeService) FlushCache(ctx context.Context, itemType string) error {
	// 清空指定类型的所有缓存键
	patterns := []string{
		fmt.Sprintf("like:user:*:%s", itemType),
		fmt.Sprintf("like:item:%s:*", itemType),
		fmt.Sprintf("like:hot:%s", itemType),
	}

	for _, pattern := range patterns {
		keys, err := s.adapter.redisClient.Keys(ctx, pattern).Result()
		if err != nil {
			return err
		}
		if len(keys) > 0 {
			err = s.adapter.redisClient.Del(ctx, keys...).Err()
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// GetCacheStats 获取缓存统计
// @param ctx 上下文
// @return *CacheStats 缓存统计信息
// @return error 错误信息
func (s *likeService) GetCacheStats(ctx context.Context) (*CacheStats, error) {
	return s.adapter.stats, nil
}

// updateHitRate 更新缓存命中率
func (s *likeService) updateHitRate() {
	total := s.adapter.stats.HitCount + s.adapter.stats.MissCount
	if total > 0 {
		s.adapter.stats.HitRate = float64(s.adapter.stats.HitCount) / float64(total)
	}
}
