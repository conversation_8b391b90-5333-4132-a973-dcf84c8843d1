package videos

import (
	"frontapi/internal/admin"
	videoModel "frontapi/internal/models/videos"
	userSrv "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
	videoValidator "frontapi/internal/validation/videos"

	"frontapi/pkg/utils"
	"frontapi/pkg/validator"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/guregu/null/v6"
)

// VideoAlbumController 视频专辑控制器结构体
type VideoAlbumController struct {
	admin.BaseController
	videoAlbumService    videoSrv.VideoAlbumService
	userService          userSrv.UserService
	videoCategoryService videoSrv.VideoCategoryService
}

// NewVideoAlbumController 创建视频专辑控制器
func NewVideoAlbumController(
	videoAlbumService videoSrv.VideoAlbumService,
	userService userSrv.UserService,
	videoCategoryService videoSrv.VideoCategoryService,
) *VideoAlbumController {
	return &VideoAlbumController{
		BaseController:       admin.BaseController{},
		videoAlbumService:    videoAlbumService,
		userService:          userService,
		videoCategoryService: videoCategoryService,
	}
}

// ListVideoAlbums 获取视频专辑列表
func (h *VideoAlbumController) ListVideoAlbums(c *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 获取查询参数
	keyword := reqInfo.Get("keyword").GetString()
	creatorID := reqInfo.Get("user_id").GetString()
	categoryID := reqInfo.Get("category_id").GetString()
	sortBy := reqInfo.Get("sort_by").GetString()

	// 获取状态参数
	status := -999
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return err
	}

	// 获取付费类型参数
	isPaid := -1
	_ = h.GetIntegerValueWithDataWrapper(c, "is_paid", &isPaid)

	// 构建查询条件
	condition := map[string]interface{}{
		"keyword":     keyword,
		"user_id":     creatorID,
		"category_id": categoryID,
		"status":      status,
	}

	if isPaid >= 0 {
		condition["is_paid"] = isPaid
	}

	// 设置默认排序
	if sortBy == "" {
		sortBy = "created_at DESC"
	}

	// 获取专辑列表
	albums, total, err := h.videoAlbumService.List(c.Context(), condition, sortBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取视频专辑列表失败: "+err.Error())
	}

	return h.SuccessList(c, albums, total, page, pageSize)
}

// GetVideoAlbum 获取视频专辑详情
func (h *VideoAlbumController) GetVideoAlbum(c *fiber.Ctx) error {
	// 获取专辑ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 获取专辑详情
	album, err := h.videoAlbumService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "视频专辑不存在")
	}

	return h.Success(c, album)
}

// CreateVideoAlbum 创建视频专辑
func (h *VideoAlbumController) CreateVideoAlbum(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 创建请求结构
	var req videoValidator.CreateVideoAlbumRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	var videoAlbum videoModel.VideoAlbum
	// 使用SmartCopy进行基础拷贝
	utils.SmartCopy(req, &videoAlbum)

	// 处理UserID、CategoryID和Cover（因为类型不匹配）
	videoAlbum.UserID = null.StringFromPtr(&req.UserID)
	if req.Cover != "" {
		videoAlbum.Cover = null.StringFrom(req.Cover)
	}

	// 处理创建者信息
	if req.UserID != "" {
		// 如果提供了用户ID，查询用户信息并填充
		user, err := h.userService.GetByID(c.Context(), req.UserID, false)
		if err != nil {
			return h.BadRequest(c, "指定的创建者不存在", nil)
		}
		if user != nil {
			if user.Nickname.Valid {
				videoAlbum.UserNickname = null.StringFrom(user.Nickname.String)
			}
			if user.Avatar.Valid {
				videoAlbum.UserAvatar = null.StringFrom(user.Avatar.String)
			}
		}
	}

	// 处理分类信息（先验证，再设置）
	if req.CategoryID != "" {
		// 如果提供了分类ID，先查询分类信息验证是否存在
		category, err := h.videoCategoryService.GetByID(c.Context(), req.CategoryID, false)
		if err != nil || category == nil {
			return h.BadRequest(c, "指定的分类不存在", nil)
		}
		// 验证通过后，设置分类ID和名称
		videoAlbum.CategoryID = null.StringFrom(req.CategoryID)
		if category.Name.Valid {
			videoAlbum.CategoryName = null.StringFrom(category.Name.String)
		}
	} else {
		// 如果分类ID为空，保持为null
		videoAlbum.CategoryID = null.String{}
		videoAlbum.CategoryName = null.String{}
	}

	// 创建专辑
	albumID, err := h.videoAlbumService.Create(c.Context(), &videoAlbum)
	if err != nil {
		return h.InternalServerError(c, "创建视频专辑失败: "+err.Error())
	}

	// 返回成功响应
	return h.Success(c, fiber.Map{
		"id":      albumID,
		"message": "创建视频专辑成功",
	})
}

// UpdateVideoAlbum 更新视频专辑
func (h *VideoAlbumController) UpdateVideoAlbum(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 创建请求结构
	var req videoValidator.UpdateVideoAlbumRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	videoAlbum, err := h.videoAlbumService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "视频专辑不存在")
	}
	//是否有相同名称的专辑
	sameNameAlbum, err := h.videoAlbumService.FindOne(c.Context(), "id != ? AND title = ?", id, req.Title)
	//判断是否存在相同名称的专辑
	if err != nil {
		return h.InternalServerError(c, "获取视频专辑失败: "+err.Error())
	}

	if sameNameAlbum != nil {
		return h.BadRequest(c, "已有相同名称的专辑", nil)
	}

	// 使用SmartCopy进行基础拷贝
	utils.SmartCopy(req, videoAlbum)

	// 处理UserID和Cover（因为类型不匹配）
	// 支持清空操作：如果传入空字符串，则清空对应字段
	videoAlbum.UserID = null.StringFromPtr(&req.UserID)
	if req.Cover != "" {
		videoAlbum.Cover = null.StringFrom(req.Cover)
	} else {
		videoAlbum.Cover = null.String{} // 清空封面
	}

	// 处理创建者信息
	if req.UserID != "" {
		// 如果提供了用户ID，查询用户信息并填充
		user, err := h.userService.GetByID(c.Context(), req.UserID, false)
		if err != nil {
			return h.BadRequest(c, "指定的创建者不存在", nil)
		}
		if user != nil {
			if user.Nickname.Valid {
				videoAlbum.UserNickname = null.StringFrom(user.Nickname.String)
			} else {
				videoAlbum.UserNickname = null.String{} // 清空昵称
			}
			if user.Avatar.Valid {
				videoAlbum.UserAvatar = null.StringFrom(user.Avatar.String)
			} else {
				videoAlbum.UserAvatar = null.String{} // 清空头像
			}
		}
	} else {
		// 如果用户ID为空，清空所有相关字段
		videoAlbum.UserNickname = null.String{}
		videoAlbum.UserAvatar = null.String{}
	}

	// 处理分类信息（先验证，再设置）
	if req.CategoryID != "" {
		// 如果提供了分类ID，先查询分类信息验证是否存在
		category, err := h.videoCategoryService.GetByID(c.Context(), req.CategoryID, false)
		if err != nil || category == nil {
			return h.BadRequest(c, "指定的分类不存在", nil)
		}
		// 验证通过后，设置分类ID和名称
		videoAlbum.CategoryID = null.StringFrom(req.CategoryID)
		if category.Name.Valid {
			videoAlbum.CategoryName = null.StringFrom(category.Name.String)
		} else {
			videoAlbum.CategoryName = null.String{} // 清空分类名称
		}
	} else {
		// 如果分类ID为空，清空分类相关字段
		videoAlbum.CategoryID = null.String{}
		videoAlbum.CategoryName = null.String{}
	}

	// 更新专辑
	err = h.videoAlbumService.UpdateById(c.Context(), id, videoAlbum)
	if err != nil {
		return h.InternalServerError(c, "更新视频专辑失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "更新视频专辑成功")
}

// DeleteVideoAlbum 删除视频专辑
func (h *VideoAlbumController) DeleteVideoAlbum(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 删除专辑
	err = h.videoAlbumService.Delete(c.Context(), id)
	if err != nil {
		return h.InternalServerError(c, "删除视频专辑失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "删除视频专辑成功")
}

// UpdateVideoAlbumStatus 更新视频专辑状态
func (h *VideoAlbumController) UpdateVideoAlbumStatus(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 获取状态参数
	reqInfo := h.GetRequestInfo(c)
	statusStr := reqInfo.Get("status").GetString()
	if statusStr == "" {
		return h.BadRequest(c, "状态参数不能为空", nil)
	}

	status, err := strconv.ParseInt(statusStr, 10, 8)
	if err != nil {
		return h.BadRequest(c, "状态参数格式错误", nil)
	}

	// 更新状态
	err = h.videoAlbumService.UpdateStatus(c.Context(), id, int(status))
	if err != nil {
		return h.InternalServerError(c, "更新视频专辑状态失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "更新视频专辑状态成功")
}

// BatchUpdateVideoAlbumStatus 批量更新视频专辑状态
func (h *VideoAlbumController) BatchUpdateVideoAlbumStatus(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 定义请求结构
	type BatchUpdateRequest struct {
		IDs    []string `json:"ids" validate:"required"`
		Status int8     `json:"status" validate:"required"`
	}

	var req BatchUpdateRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 批量更新状态
	err := h.videoAlbumService.BatchUpdateStatus(c.Context(), req.IDs, int(req.Status))
	if err != nil {
		return h.InternalServerError(c, "批量更新视频专辑状态失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "批量更新视频专辑状态成功")
}

// BatchDeleteVideoAlbum 批量删除视频专辑
func (h *VideoAlbumController) BatchDeleteVideoAlbum(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 定义请求结构
	type BatchDeleteRequest struct {
		IDs []string `json:"ids" validate:"required"`
	}

	var req BatchDeleteRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 批量删除
	deletedCount, err := h.videoAlbumService.BatchDelete(c.Context(), req.IDs)
	if err != nil {
		return h.InternalServerError(c, "批量删除视频专辑失败: "+err.Error())
	}

	// 返回成功响应
	return h.Success(c, fiber.Map{
		"message":      "批量删除视频专辑成功",
		"deletedCount": deletedCount,
	})
}
