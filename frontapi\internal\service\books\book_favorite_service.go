package books

import (
	"frontapi/internal/models/books"
	bookRepo "frontapi/internal/repository/books"
	"frontapi/internal/service/base"
)

// BookFavoriteService 电子书收藏服务接口
type BookFavoriteService interface {
	base.IBaseService[books.BookFavorite]
}

type favoriteService struct {
	*base.ExtendedService[books.BookFavorite]
	favoriteRepo bookRepo.FavoriteRepository
	bookRepo     bookRepo.BookRepository
}

// NewFavoriteService 创建电子书收藏服务实例
func NewFavoriteService(favoriteRepo bookRepo.FavoriteRepository, bookRepo bookRepo.BookRepository) BookFavoriteService {
	return &favoriteService{
		ExtendedService: base.NewExtendedService[books.BookFavorite](favoriteRepo, "book_favorites"),
		favoriteRepo:    favoriteRepo,
		bookRepo:        bookRepo,
	}
}
