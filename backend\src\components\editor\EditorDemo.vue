<template>
  <div class="editor-demo">
    <h2>UmoEditor 富文本编辑器演示</h2>
    
    <div class="editor-controls">
      <el-switch
        v-model="simpleMode"
        active-text="简洁模式"
        inactive-text="完整模式"
      />
      <el-button type="primary" @click="getContent">获取内容</el-button>
      <el-button @click="setContent">设置内容</el-button>
    </div>
    
    <div class="editor-container">
      <UmoRichEditor
        v-model="content"
        :simple-mode="simpleMode"
        :height="500"
        :upload-url="uploadUrl"
        @change="handleEditorChange"
      />
    </div>
    
    <div v-if="showContent" class="content-preview">
      <h3>编辑器内容预览：</h3>
      <div v-html="content"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import UmoRichEditor from './UmoRichEditor.vue';

const content = ref('<h1>UmoEditor 富文本编辑器</h1><p>这是一个功能强大的编辑器，支持多种格式。</p><ul><li>支持文本格式化</li><li>支持图片上传</li><li>支持表格</li><li>支持导出为Word/PDF</li></ul>');
const simpleMode = ref(false);
const showContent = ref(false);
const uploadUrl = '/api/common/upload'; // 替换为实际的上传接口

// 编辑器内容变更事件
const handleEditorChange = (val: string) => {
  console.log('Editor content changed:', val.substring(0, 100) + '...');
};

// 获取编辑器内容
const getContent = () => {
  showContent.value = true;
  ElMessage.success('内容已更新到预览区域');
};

// 设置编辑器内容
const setContent = () => {
  content.value = `<h2>这是新设置的内容 - ${new Date().toLocaleString()}</h2>
<p>UmoEditor 是一个基于 Vue3 和 Tiptap 的本土化开源文档编辑器，专为国人用户设计。</p>
<p>它提供了强大的文档编辑能力和 AI 创作功能，支持分页模式、Markdown 语法、富文本编辑、多种格式的节点插入、页面样式设置、文档导出与打印等功能。</p>`;
  
  ElMessage.success('内容已重置');
};
</script>

<style scoped>
.editor-demo {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.editor-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.editor-container {
  margin-bottom: 30px;
}

.content-preview {
  border-top: 1px solid #dcdfe6;
  padding-top: 20px;
  margin-top: 30px;
}

.content-preview h3 {
  margin-bottom: 16px;
  color: #409eff;
}

.content-preview :deep(img) {
  max-width: 100%;
  height: auto;
}
</style> 