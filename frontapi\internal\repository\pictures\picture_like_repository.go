package pictures

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"frontapi/internal/models/pictures"
	"frontapi/internal/repository/base"
)

// PictureLikeRepository 图片点赞数据访问接口
type PictureLikeRepository interface {
	base.ExtendedRepository[pictures.PictureLike]
	FindByUserAndPicture(ctx context.Context, userID, pictureID string) (*pictures.PictureLike, error)
	ListByPicture(ctx context.Context, pictureID string, page, pageSize int) ([]*pictures.PictureLike, int64, error)
	ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*pictures.PictureLike, int64, error)
	CountByPicture(ctx context.Context, pictureID string) (int, error)
}

// pictureLikeRepository 图片点赞数据访问实现
type pictureLikeRepository struct {
	base.ExtendedRepository[pictures.PictureLike]
}

// NewPictureLikeRepository 创建图片点赞仓库实例
func NewPictureLikeRepository(db *gorm.DB) PictureLikeRepository {
	return &pictureLikeRepository{
		ExtendedRepository: base.NewExtendedRepository[pictures.PictureLike](db),
	}
}

// FindByUserAndPicture 根据用户ID和图片ID查找点赞记录
func (r *pictureLikeRepository) FindByUserAndPicture(ctx context.Context, userID, pictureID string) (*pictures.PictureLike, error) {
	var like pictures.PictureLike
	err := r.GetDBWithContext(ctx).
		Where("user_id = ? AND picture_id = ?", userID, pictureID).
		First(&like).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &like, nil
}

// ListByPicture 根据图片ID获取点赞列表
func (r *pictureLikeRepository) ListByPicture(ctx context.Context, pictureID string, page, pageSize int) ([]*pictures.PictureLike, int64, error) {
	var (
		likes []*pictures.PictureLike
		total int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&pictures.PictureLike{}).
		Where("picture_id = ?", pictureID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("picture_id = ?", pictureID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&likes).Error
	if err != nil {
		return nil, 0, err
	}

	return likes, total, nil
}

// ListByUser 根据用户ID获取点赞列表
func (r *pictureLikeRepository) ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*pictures.PictureLike, int64, error) {
	var (
		likes []*pictures.PictureLike
		total int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&pictures.PictureLike{}).
		Where("user_id = ?", userID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&likes).Error
	if err != nil {
		return nil, 0, err
	}

	return likes, total, nil
}

// CountByPicture 统计图片点赞数
func (r *pictureLikeRepository) CountByPicture(ctx context.Context, pictureID string) (int, error) {
	var count int64
	err := r.GetDBWithContext(ctx).Model(&pictures.PictureLike{}).
		Where("picture_id = ?", pictureID).
		Count(&count).Error
	return int(count), err
}
