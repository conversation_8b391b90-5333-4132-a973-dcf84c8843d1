package validation

// PictureCreateRequest 图片创建请求验证模型
type PictureCreateRequest struct {
	Title       string   `json:"title" validate:"required,min=2,max=100"`
	Description string   `json:"description" validate:"omitempty,max=2000"`
	URL         string   `json:"url" validate:"required,url"`
	Thumbnail   string   `json:"thumbnail" validate:"omitempty,url"`
	CategoryID  uint     `json:"categoryId" validate:"required,min=1"`
	AlbumID     uint     `json:"albumId" validate:"omitempty,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Width       int      `json:"width" validate:"omitempty,min=0"`
	Height      int      `json:"height" validate:"omitempty,min=0"`
	Size        int64    `json:"size" validate:"omitempty,min=0"`
	IsPrivate   bool     `json:"isPrivate"`
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
}

// PictureUpdateRequest 图片更新请求验证模型
type PictureUpdateRequest struct {
	Title       string   `json:"title" validate:"omitempty,min=2,max=100"`
	Description string   `json:"description" validate:"omitempty,max=2000"`
	Thumbnail   string   `json:"thumbnail" validate:"omitempty,url"`
	CategoryID  uint     `json:"categoryId" validate:"omitempty,min=1"`
	AlbumID     uint     `json:"albumId" validate:"omitempty,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	IsPrivate   bool     `json:"isPrivate"`
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:正常 1:待审核 2:已删除
}

// PictureListRequest 图片列表请求验证模型
type PictureListRequest struct {
	Page       int      `json:"page" validate:"min=1"`
	PageSize   int      `json:"pageSize" validate:"min=1,max=100"`
	CategoryID uint     `json:"categoryId" validate:"omitempty,min=1"`
	AlbumID    uint     `json:"albumId" validate:"omitempty,min=1"`
	UserID     uint     `json:"userId" validate:"omitempty,min=1"`
	Tags       []string `json:"tags" validate:"omitempty"`
	SortBy     string   `json:"sortBy" validate:"omitempty,oneof=latest popular download favorite"`
	IsVIP      bool     `json:"isVip" validate:"omitempty"`
	IsAdult    bool     `json:"isAdult" validate:"omitempty"`
	Keyword    string   `json:"keyword" validate:"omitempty,max=50"`
}

// PictureAlbumCreateRequest 图片相册创建请求验证模型
type PictureAlbumCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"omitempty,max=200"`
	Cover       string `json:"cover" validate:"omitempty,url"`
	IsPrivate   bool   `json:"isPrivate"`
}

// PictureAlbumUpdateRequest 图片相册更新请求验证模型
type PictureAlbumUpdateRequest struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=50"`
	Description string `json:"description" validate:"omitempty,max=200"`
	Cover       string `json:"cover" validate:"omitempty,url"`
	IsPrivate   bool   `json:"isPrivate"`
	Status      int    `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:正常 1:待审核 2:已删除
}

// PictureCategoryCreateRequest 图片分类创建请求验证模型
type PictureCategoryCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"required,min=5,max=200"`
	Icon        string `json:"icon" validate:"omitempty,url"`
	SortOrder   int    `json:"sortOrder" validate:"omitempty,min=0"`
	ParentID    uint   `json:"parentId" validate:"omitempty,min=0"`
}

// PictureCategoryUpdateRequest 图片分类更新请求验证模型
type PictureCategoryUpdateRequest struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=50"`
	Description string `json:"description" validate:"omitempty,min=5,max=200"`
	Icon        string `json:"icon" validate:"omitempty,url"`
	SortOrder   int    `json:"sortOrder" validate:"omitempty,min=0"`
	Status      int    `json:"status" validate:"omitempty,oneof=0 1"` // 0:正常 1:禁用
}
