<template>
    <div class="container">
      <el-tabs style="width:300px" v-model="activeName">
          <el-tab-pane label="微信" name="first">
             <div class="content">
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in wx_list" :key="i">
                          <span class="item-icon" :style="{'background-position':item.position}" @click="$emit('select', {i: item.name, type: 'wx'})"></span>
                      </li>
                  </ul>
              </div>
          </el-tab-pane>
          <el-tab-pane label="emoji" name="second">
              <div class="content">
                  <div class="content-title" v-if="use_list.length">常用</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in use_list" :key="'use'+i">
                          <span class="item-icon-list" @click="$emit('select', {i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">人类和身体</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in men_list" :key="'men'+i">
                          <span class="item-icon-list" @click="$emit('select', {i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">动物与自然</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in animal_list" :key="'animal_list'+i">
                          <span class="item-icon-list" @click="$emit('select', {i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">食物和饮料</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in food_list" :key="'food_list'+i">
                          <span class="item-icon-list" @click="$emit('select', {i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">活动</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in active_list" :key="'active_list'+i">
                          <span class="item-icon-list" @click="$emit('select', {i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">旅行和地点</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in pot_list" :key="'pot_list'+i">
                          <span class="item-icon-list" @click="$emit('select', {i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">物品</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in obj_list" :key="'obj_list'+i">
                          <span class="item-icon-list" @click="icon_select({i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">符号</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in signal_list" :key="'signal_list'+i">
                          <span class="item-icon-list" @click="icon_select({i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
                  <div class="content-title">旗帜</div>
                  <ul class="emoji-wrap">
                      <li class="item" v-for="(item,i) in flag_list" :key="'flag_list'+i">
                          <span class="item-icon-list" @click="icon_select({i: item, type: 'emoji'})">{{item}}</span>
                      </li>
                  </ul>
              </div>
          </el-tab-pane>
      </el-tabs>
    </div>
  </template>
  
<script setup lang="ts">
import { reactive, ref,onMounted,onUnmounted } from 'vue'

// 定义发射事件
const emit = defineEmits<{
  (e: 'select', value: { i: string, type: 'wx' | 'emoji' }): void
}>()

// 响应式状态
const activeName = ref('first')

// 表情数据
const { wx_list, use_list, men_list, animal_list, food_list, active_list, pot_list, obj_list, signal_list, flag_list } = reactive({
    wx_list: [
        {name: "[微笑]", position: "0% 0%"},
        {name: "[色]", position: "0% 11.1111%"},
        {name: "[发呆]", position: "10% 0%"},
        {name: "[得意]", position: "10% 11.1111%"},
        {name: "[流泪]", position: "20% 0%"},
        {name: "[害羞]", position: "20% 11.1111%"},
        {name: "[闭嘴]", position: "20% 22.2222%"},
        {name: "[睡]", position: "0% 22.2222%"},
        {name: "[大哭]", position: "10% 22.2222%"},
        {name: "[尴尬]", position: "30% 0%"},
        {name: "[发怒]", position: "30% 11.1111%"},
        {name: "[调皮]", position: "30% 22.2222%"},
        {name: "[呲牙]", position: "0% 33.3333%"},
        {name: "[惊讶]", position: "10% 33.3333%"},
        {name: "[难过]", position: "30% 33.3333%"},
        {name: "[囧]", position: "20% 33.3333%"},
        {name: "[抓狂]", position: "40% 0%"},
        {name: "[吐]", position: "40% 11.1111%"},
        {name: "[偷笑]", position: "0% 44.4444%"},
        {name: "[愉快]", position: "40% 22.2222%"},
        {name: "[白眼]", position: "40% 33.3333%"},
        {name: "[傲慢]", position: "10% 44.4444%"},
        {name: "[困]", position: "20% 44.4444%"},
        {name: "[惊恐]", position: "30% 44.4444%"},
        {name: "[憨笑]", position: "40% 44.4444%"},
        {name: "[悠闲]", position: "50% 0%"},
        {name: "[咒骂]", position: "50% 22.2222%"},
        {name: "[疑问]", position: "50% 11.1111%"},
        {name: "[嘘]", position: "50% 33.3333%"},
        {name: "[晕]", position: "50% 44.4444%"},
        {name: "[衰]", position: "0% 55.5556%"},
        {name: "[骷髅]", position: "10% 55.5556%"},
        {name: "[敲打]", position: "20% 55.5556%"},
        {name: "[再见]", position: "30% 55.5556%"},
        {name: "[擦汗]", position: "40% 55.5556%"},
        {name: "[抠鼻]", position: "50% 55.5556%"},
        {name: "[鼓掌]", position: "60% 0%"},
        {name: "[坏笑]", position: "60% 11.1111%"},
        {name: "[右哼哼]", position: "60% 22.2222%"},
        {name: "[鄙视]", position: "60% 33.3333%"},
        {name: "[委屈]", position: "60% 44.4444%"},
        {name: "[快哭了]", position: "60% 55.5556%"},
        {name: "[阴险]", position: "10% 66.6667%"},
        {name: "[亲亲]", position: "0% 66.6667%"},
        {name: "[可怜]", position: "20% 66.6667%"},
        {name: "[笑脸]", position: "30% 66.6667%"},
        {name: "[生病]", position: "40% 66.6667%"},
        {name: "[脸红]", position: "60% 66.6667%"},
        {name: "[破涕为笑]", position: "50% 66.6667%"},
        {name: "[恐惧]", position: "70% 0%"},
        {name: "[失望]", position: "70% 22.2222%"},
        {name: "[无语]", position: "70% 11.1111%"},
        {name: "[嘿哈]", position: "70% 33.3333%"},
        {name: "[捂脸]", position: "70% 44.4444%"},
        {name: "[奸笑]", position: "70% 55.5556%"},
        {name: "[机智]", position: "70% 66.6667%"},
        {name: "[皱眉]", position: "0% 77.7778%"},
        {name: "[耶]", position: "20% 77.7778%"},
        {name: "[吃瓜]", position: "10% 77.7778%"},
        {name: "[加油]", position: "30% 88.8889%"},
        {name: "[汗]", position: "40% 77.7778%"},
        {name: "[天啊]", position: "30% 77.7778%"},
        {name: "[Emm]", position: "50% 77.7778%"},
        {name: "[社会社会]", position: "80% 11.1111%"},
        {name: "[旺柴]", position: "60% 77.7778%"},
        {name: "[好的]", position: "70% 77.7778%"},
        {name: "[打脸]", position: "80% 44.4444%"},
        {name: "[哇]", position: "80% 0%"},
        {name: "[翻白眼]", position: "80% 22.2222%"},
        {name: "[666]", position: "80% 33.3333%"},
        {name: "[让我看看]", position: "80% 66.6667%"},
        {name: "[叹气]", position: "80% 55.5556%"},
        {name: "[苦涩]", position: "80% 77.7778%"},
        {name: "[裂开]", position: "0% 88.8889%"},
        {name: "[嘴唇]", position: "10% 88.8889%"},
        {name: "[爱心]", position: "20% 88.8889%"},
        {name: "[心碎]", position: "40% 88.8889%"},
        {name: "[拥抱]", position: "50% 88.8889%"},
        {name: "[强]", position: "60% 88.8889%"},
        {name: "[弱]", position: "70% 88.8889%"},
        {name: "[握手]", position: "80% 88.8889%"},
        {name: "[胜利]", position: "90% 0%"},
        {name: "[抱拳]", position: "90% 22.2222%"},
        {name: "[勾引]", position: "90% 11.1111%"},
        {name: "[拳头]", position: "90% 44.4444%"},
        {name: "[OK]", position: "90% 33.3333%"},
        {name: "[合十]", position: "40% 100%"},
        {name: "[啤酒]", position: "90% 55.5556%"},
        {name: "[咖啡]", position: "90% 88.8889%"},
        {name: "[蛋糕]", position: "90% 66.6667%"},
        {name: "[玫瑰]", position: "90% 77.7778%"},
        {name: "[凋谢]", position: "0% 100%"},
        {name: "[菜刀]", position: "10% 100%"},
        {name: "[炸弹]", position: "20% 100%"},
        {name: "[便便]", position: "50% 100%"},
        {name: "[月亮]", position: "30% 100%"},
        {name: "[太阳]", position: "60% 100%"},
        {name: "[庆祝]", position: "90% 100%"},
        {name: "[礼物]", position: "70% 100%"},
        {name: "[红包]", position: "80% 100%"},
        {name: "[福]", position: "100% 0%"},
        {name: "[烟花]", position: "100% 11.1111%"},
        {name: "[爆竹]", position: "100% 22.2222%"},
        {name: "[猪头]", position: "100% 33.3333%"},
        {name: "[跳跳]", position: "100% 44.4444%"}
    ],
    use_list: [],
    men_list: ['😀','😃','😄','😁','😆','😅','🤣','😂','🙂','🙃','😉','😊','😇','🥰','😍','🤩','😘','😗','☺️','😚','😙','😋','😛','😜','🤪','😝','🤑','🤗','🤭','🤫','🤔','🤐','🤨','😐','😑','😶','😏','😒','🙄','😬','🤥','😌','😔','😪','🤤','😴','😷','🤒','🤕','🤢','🤮','🤧','🥵','🥶','🥴','😵','🤯','🤠','🥳','😎','🤓','🧐','😕','😟','🙁','☹️','😮','😯','😲','😳','🥺','😦','😧','😨','😰','😥','😢','😭','😱','😖','😣','😞','😓','😩','😫','🥱','😤','😡','😠','🤬','😈','👿','💀','☠️','💩','🤡','👹','👺','👻','👽','👾','🤖','😺','😸','😹','😻','😼','😽','🙀','😿','😾','🙈','🙉','🙊','👋','🤚','🖐️','✋','🖖','👌','🤏','✌️','🤞','🤟','🤘','🤙','👈','👉','👆','🖕','👇','☝️','👍','👎','✊','👊','🤛','🤜','👏','🙌','👐','🤲','🤝','🙏','✍️','💅','🤳','💪','🦾','🦿','🦵','🦶','👂','🦻','👃','🧠','🦷','🦴','👀','👁️','👅','👄','👶','🧒','👦','👧','🧑','👱','👨','🧔','👨‍🦰','👨‍🦱','👨‍🦳','👨‍🦲','👩','👩‍🦰','🧑‍🦰','👩‍🦱','🧑‍🦱','👩‍🦳','🧑‍🦳','👩‍🦲','🧑‍🦲','👱‍♀️','👱‍♂️','🧓','👴','👵','🙍','🙍‍♂️','🙍‍♀️','🙎','🙎‍♂️','🙎‍♀️','🙅','🙅‍♂️','🙅‍♀️','🙆','🙆‍♂️','🙆‍♀️','💁','💁‍♂️','💁‍♀️','🙋','🙋‍♂️','🙋‍♀️','🧏','🧏‍♂️','🧏‍♀️','🙇','🙇‍♂️','🙇‍♀️','🤦','🤦‍♂️','🤦‍♀️','🤷','🤷‍♂️','🤷‍♀️','🧑‍⚕️','👨‍⚕️','👩‍⚕️','🧑‍🎓','👨‍🎓','👩‍🎓','🧑‍🏫','👨‍🏫','👩‍🏫','🧑‍⚖️','👨‍⚖️','👩‍⚖️','🧑‍🌾','👨‍🌾','👩‍🌾','🧑‍🍳','👨‍🍳','👩‍🍳','🧑‍🔧','👨‍🔧','👩‍🔧','🧑‍🏭','👨‍🏭','👩‍🏭','🧑‍💼','👨‍💼','👩‍💼','🧑‍🔬','👨‍🔬','👩‍🔬','🧑‍💻','👨‍💻','👩‍💻','🧑‍🎤','👨‍🎤','👩‍🎤','🧑‍🎨','👨‍🎨','👩‍🎨','🧑‍✈️','👨‍✈️','👩‍✈️','🧑‍🚀','👨‍🚀','👩‍🚀','🧑‍🚒','👨‍🚒','👩‍🚒','👮','👮‍♂️','👮‍♀️','🕵️','🕵️‍♂️','🕵️‍♀️','💂','💂‍♂️','💂‍♀️','👷','👷‍♂️','👷‍♀️','🤴','👸','👳','👳‍♂️','👳‍♀️','👲','🧕','🤵','👰','🤰','🤱','👼','🎅','🤶','🦸','🦸‍♂️','🦸‍♀️','🦹','🦹‍♂️','🦹‍♀️','🧙','🧙‍♂️','🧙‍♀️','🧚','🧚‍♂️','🧚‍♀️','🧛','🧛‍♂️','🧛‍♀️','🧜','🧜‍♂️','🧜‍♀️','🧝','🧝‍♂️','🧝‍♀️','🧞','🧞‍♂️','🧞‍♀️','🧟','🧟‍♂️','🧟‍♀️','💆','💆‍♂️','💆‍♀️','💇','💇‍♂️','💇‍♀️','🚶','🚶‍♂️','🚶‍♀️','🧍','🧍‍♂️','🧍‍♀️','🧎','🧎‍♂️','🧎‍♀️','🧑‍🦯','👨‍🦯','👩‍🦯','🧑‍🦼','👨‍🦼','👩‍🦼','🧑‍🦽','👨‍🦽','👩‍🦽','🏃','🏃‍♂️','🏃‍♀️','💃','🕺','🕴️','👯','👯‍♂️','👯‍♀️','🧖','🧖‍♂️','🧖‍♀️','🧗','🧗‍♂️','🧗‍♀️','🤺','🏇','⛷️','🏂','🏌️','🏌️‍♂️','🏌️‍♀️','🏄','🏄‍♂️','🏄‍♀️','🚣','🚣‍♂️','🚣‍♀️','🏊','🏊‍♂️','🏊‍♀️','⛹️','⛹️‍♂️','⛹️‍♀️','🏋️','🏋️‍♂️','🏋️‍♀️','🚴','🚴‍♂️','🚴‍♀️','🚵','🚵‍♂️','🚵‍♀️','🤸','🤸‍♂️','🤸‍♀️','🤼','🤼‍♂️','🤼‍♀️','🤽','🤽‍♂️','🤽‍♀️','🤾','🤾‍♂️','🤾‍♀️','🤹','🤹‍♂️','🤹‍♀️','🧘','🧘‍♂️','🧘‍♀️','🛀','🛌','🧑‍🤝‍🧑','👭','👫','👬','💏','👩‍❤️‍💋‍👨','👨‍❤️‍💋‍👨','👩‍❤️‍💋‍👩','💑','👩‍❤️‍👨','👨‍❤️‍👨','👩‍❤️‍👩','👨‍👩‍👦','👪','👨‍👩‍👧','👨‍👩‍👧‍👦','👨‍👩‍👦‍👦','👨‍👩‍👧‍👧','👨‍👨‍👦','👨‍👨‍👧','👨‍👨‍👧‍👦','👨‍👨‍👦‍👦','👨‍👨‍👧‍👧','👩‍👩‍👦','👩‍👩‍👧','👩‍👩‍👧‍👦','👩‍👩‍👦‍👦','👩‍👩‍👧‍👧','👨‍👦','👨‍👦‍👦','👨‍👧','👨‍👧‍👦','👨‍👧‍👧','👩‍👦','👩‍👦‍👦','👩‍👧','👩‍👧‍👦','👩‍👧‍👧','🗣️','👤','👥','👣','💋','💌','💘','💝','💖','💗','💓','💞','💕','💟','❣️','💔','❤️','🧡','💛','💚','💙','💜','🤎','🖤','🤍','💯','💢','💥','💫','💦','💨','🕳️','💣','💬','👁️‍🗨️','🗨️','🗯️','💭','💤'],
    animal_list: ['🐵','🐒','🦍','🦧','🐶','🐕','🦮','🐕‍🦺','🐩','🐺','🦊','🦝','🐱','🐈','🦁','🐯','🐅','🐆','🐴','🐎','🦄','🦓','🦌','🐮','🐂','🐃','🐄','🐷','🐖','🐗','🐽','🐏','🐑','🐐','🐪','🐫','🦙','🦒','🐘','🦏','🦛','🐭','🐁','🐀','🐹','🐰','🐇','🐿️','🦔','🦇','🐻','🐨','🐼','🦥','🦦','🦨','🦘','🦡','🐾','🦃','🐔','🐓','🐣','🐤','🐥','🐦','🐧','🕊️','🦅','🦆','🦢','🦉','🦩','🦚','🦜','🐸','🐊','🐢','🦎','🐍','🐲','🐉','🦕','🦖','🐳','🐋','🐬','🐟','🐠','🐡','🦈','🐙','🐚','🐌','🦋','🐛','🐜','🐝','🐞','🦗','🕷️','🕸️','🦂','🦟','🦠','💐','🌸','💮','🏵️','🌹','🥀','🌺','🌻','🌼','🌷','🌱','🌲','🌳','🌴','🌵','🌾','🌿','☘️','🍀','🍁','🍂','🍃'],
    food_list: ['🍇','🍈','🍉','🍊','🍋','🍌','🍍','🥭','🍎','🍏','🍐','🍑','🍒','🍓','🥝','🍅','🥥','🥑','🍆','🥔','🥕','🌽','🌶️','🥒','🥬','🥦','🧄','🧅','🍄','🥜','🌰','🍞','🥐','🥖','🥨','🥯','🥞','🧇','🧀','🍖','🍗','🥩','🥓','🍔','🍟','🍕','🌭','🥪','🌮','🌯','🥙','🧆','🥚','🍳','🥘','🍲','🥣','🥗','🍿','🧈','🧂','🥫','🍱','🍘','🍙','🍚','🍛','🍜','🍝','🍠','🍢','🍣','🍤','🍥','🥮','🍡','🥟','🥠','🥡','🦀','🦞','🦐','🦑','🦪','🍦','🍧','🍨','🍩','🍪','🎂','🍰','🧁','🥧','🍫','🍬','🍭','🍮','🍯','🍼','🥛','☕','🍵','🍶','🍾','🍷','🍸','🍹','🍺','🍻','🥂','���3','���4','🧃','🧉','🧊','🥢','🍽️','🍴','🥄','🔪','🏺'],
    active_list: ['🎃','🎄','🎆','🎇','🧨','✨','🎈','🎉','🎊','🎋','🎍','🎎','🎏','🎐','🎑','🧧','🎀','🎁','🎗️','🎟️','🎫','🎖️','🏆','🏅','🥇','🥈','🥉','⚽','⚾','🥎','🏀','🏐','🏈','🏉','🎾','🥏','🎳','🏏','🏑','🏒','🥍','🏓','🏸','🥊','🥋','🥅','⛳','⛸️','🎣','🤿','🎽','🎿','🛷','🥌','🎯','🪀','🪁','🎱','🔮','🧿','🎮','🕹️','🎰','🎲','🧩','🧸','♠️','♥️','♦️','♣️','♟️','🃏','🀄','🎴','🎭','🖼️','🎨','🧵','🧶'],
    pot_list: ['🌍','🌎','🌏','🌐','🗺️','🗾','🧭','🏔️','⛰️','🌋','🗻','🏕️','🏖️','🏜️','🏝️','🏞️','🏟️','🏛️','🏗️','🧱','🏘️','🏚️','🏠','🏡','🏢','🏣','🏤','🏥','🏦','🏨','🏩','🏪','🏫','🏬','🏭','🏯','🏰','💒','🗼','🗽','⛪','🕌','🛕','🕍','⛩️','🕋','⛲','⛺','🌁','🌃','🏙️','🌄','🌅','🌆','🌇','🌉','♨️','🎠','🎡','🎢','💈','🎪','���2','���3','���4','���5','���6','���7','���8','���9','���A','���B','���C','���D','���E','���F','���G','���H','���I','���J','���K','���L','���M','���N','���O','���P','���Q','���R','���S','���T','���U','���V','���W','���X','���Y','���Z','🚁','🚀','🛸','🛎️','🧳','⌛','⏳','⌚','⏰','⏱️','⏲️','🕰️','🕛','🕧','🕐','🕜','🕑','🕝','🕒','🕞','🕓','🕟','🕔','🕠','🕕','🕡','🕖','🕢','🕗','🕣','🕘','🕤','🕙','🕥','🕚','🕦','🌑','🌒','🌓','🌔','🌕','🌖','🌗','🌘','🌙','🌚','🌛','🌜','🌡️','☀️','🌝','🌞','🪐','⭐','🌟','🌠','🌌','☁️','⛅','⛈️','🌤️','🌥️','🌦️','🌧️','🌨️','🌩️','🌪️','🌫️','🌬️','🌀','🌈','🌂','☂️','☔','⛱️','⚡','❄️','☃️','⛄','☄️','🔥','💧','🌊'],
    obj_list: ['👓','🕶️','🥽','🥼','🦺','👔','👕','👖','🧣','🧤','🧥','🧦','👗','👘','🥻','🩱','🩲','🩳','👙','👚','👛','👜','👝','🛍️','🎒','👞','👟','🥾','🥿','👠','👡','🩰','👢','👑','👒','🎩','🎓','🧢','⛑️','📿','💄','💍','💎','🔇','🔈','🔉','🔊','📢','📣','📯','🔔','🔕','🎼','🎵','🎶','🎙️','🎚️','🎛️','🎤','🎧','📻','🎷','🎸','🎹','🎺','🎻','🪕','🥁','📱','📲','☎️','📞','📟','📠','🔋','🔌','💻','🖥️','🖨️','⌨️','🖱️','🖲️','💽','💾','💿','📀','🧮','🎥','🎞️','📽️','🎬','📺','📷','📸','📹','📼','🔍','🔎','🕯️','💡','🔦','🏮','🪔','📔','📕','📖','📗','📘','📙','📚','📓','📒','📃','📜','📄','📰','🗞️','📑','🔖','🏷️','💰','💴','💵','💶','💷','💸','💳','🧾','💹','💱','💲','✉️','📧','📨','📩','📤','📥','📦','📫','📪','📬','📭','📮','🗳️','✏️','✒️','🖋️','🖊️','🖌️','🖍️','📝','💼','📁','📂','🗂️','📅','📆','🗒️','🗓️','📇','📈','📉','📊','📋','📌','📍','📎','🖇️','📏','📐','✂️','🗃️','🗄️','🗑️','🔒','🔓','🔏','🔐','🔑','🗝️','🔨','🪓','⛏️','⚒️','🛠️','🗡️','⚔️','🔫','🏹','🛡️','🔧','🔩','⚙️','🗜️','⚖️','🦯','🔗','⛓️','🧰','🧲','⚗️','🧪','🧫','🧬','🔬','🔭','📡','💉','🩸','💊','🩹','🩺','🚪','🛏️','🛋️','🪑','🚽','🚿','🛁','🪒','🧴','🧷','🧹','🧺','🧻','🧼','🧽','🧯','🛒','🚬','⚰️','⚱️','🗿'],
    signal_list: ['🏧','🚮','🚰','♿','🚹','🚺','🚻','🚼','🚾','🛂','🛃','🛄','🛅','⚠️','🚸','⛔','🚫','🚳','🚭','🚯','🚱','🚷','📵','🔞','☢️','☣️','⬆️','↗️','➡️','↘️','⬇️','↙️','⬅️','↖️','↕️','↔️','↩️','↪️','⤴️','⤵️','🔃','🔄','🔙','🔚','🔛','🔜','🔝','🛐','⚛️','🕉️','✡️','☸️','☯️','✝️','☦️','☪️','☮️','🕎','🔯','♈','♉','♊','♋','♌','♍','♎','♏','♐','♑','♒','♓','⛎','🔀','🔁','🔂','▶️','⏩','⏭️','⏯️','◀️','⏪','⏮️','🔼','⏫','🔽','⏬','⏸️','⏹️','⏺️','⏏️','🎦','🔅','🔆','📶','📳','📴','♀️','♂️','⚕️','♾️','♻️','⚜️','🔱','📛','🔰','⭕','✅','☑️','✔️','✖️','❌','❎','➕','➖','➗','➰','➿','〽️','✳️','✴️','❇️','‼️','⁉️','❓','❔','❕','❗','〰️','©️','®️','™️','#️⃣','*️⃣','0️⃣','1️⃣','2️⃣','3️⃣','4️⃣','5️⃣','6️⃣','7️⃣','8️⃣','9️⃣','🔟','🔠','🔡','🔢','🔣','🔤','🅰️','🆎','🅱️','🆑','🆒','🆓','ℹ️','🆔','Ⓜ️','🆕','🆖','🅾️','🆗','🅿️','🆘','🆙','🆚','🈁','🈂️','🈷️','🈶','🈯','🉐','🈹','🈚','🈲','🉑','🈸','🈴','🈳','㊗️','㊙️','🈺','🈵','🔴','🟠','🟡','🟢','🔵','🟣','🟤','⚫','⚪','🟥','🟧','🟨','🟩','🟦','🟪','🟫','⬛','⬜','◼️','◻️','◾','◽','▪️','▫️','🔶','🔷','🔸','🔹','🔺','🔻','💠','🔘','🔳','🔲'],
    flag_list: ['🏁','🇨🇳','🎌','🇩🇪','🇪🇸','🇦🇨','🇦🇩','🇦🇪','🇦🇫','🇦🇬','🇦🇮','🇦🇱','🇦🇲','🇦🇴','🇦🇶','🇦🇷','🇦🇸','🇦🇹','🇦🇺','🇦🇼','🇦🇽','🇦🇿','🇧🇦','🇧🇧','🇧🇩','🇧🇪','🇧🇫','🇧🇬','🇧🇭','🇧🇮','🇧🇯','🇧🇱','🇧🇲','🇧🇳','🇧🇴','🇧🇶','🇧🇷','🇧🇸','🇧🇹','🇧🇻','🇧🇼','🇧🇾','🇧🇿','🇨🇦','🇨🇨','🇨🇩','🇨🇫','🇨🇬','🇨🇭','🇨🇮','🇨🇰','🇨🇱','🇨🇲','🇨🇴','🇨🇵','🇨🇷','🇨🇺','🇨🇻','🇨🇼','🇨🇽','🇨🇾','🇨🇿','🇩🇬','🇩🇯','🇩🇰','🇩🇲','🇩🇴','🇩🇿','🇪🇦','🇪🇨','🇪🇪','🇪🇬','🇪🇭','🏴󠁧󠁢󠁥󠁮󠁧󠁿','🇪🇷','🇪🇹','🇪🇺','🇫🇮','🇫🇯','🇫🇰','🇫🇲','🇫🇴','🇬🇦','🇬🇩','🇬🇪','🇬🇫','🇬🇬','🇬🇭','🇬🇮','🇬🇱','🇬🇲','🇬🇳','🇬🇵','🇬🇶','🇬🇷','🇬🇸','🇬🇹','🇬🇺','🇬🇼','🇬🇾','🇭🇰','🇭🇲','🇭🇳','🇭🇷','🇭🇹','🇭🇺','🇮🇨','🇮🇩','🇮🇪','🇮🇱','🇮🇲','🇮🇳','🇮🇴','🇮🇶','🇮🇷','🇮🇸','🇯🇪','🇯🇲','🇯🇴','🇰🇪','🇰🇬','🇰🇭','🇰🇮','🇰🇲','🇰🇳','🇰🇵','🇰🇼','🇰🇾','🇰🇿','🇱🇦','🇱🇧','🇱🇨','🇱🇮','🇱🇰','🇱🇷','🇱🇸','🇱🇹','🇱🇺','🇱🇻','🇱🇾','🇲🇦','🇲🇨','🇲🇩','🇲🇪','🇲🇫','🇲🇬','🇲🇭','🇲🇰','🇲🇱','🇲🇲','🇲🇳','🇲🇴','🇲🇵','🇲🇶','🇲🇷','🇲🇸','🇲🇹','🇲🇺','🇲🇻','🇲🇼','🇲🇽','🇲🇾','🇲🇿','🇳🇦','🇳🇨','🇳🇪','🇳🇫','🇳🇬','🇳🇮','🇳🇱','🇳🇴','🇳🇵','🇳🇷','🇳🇺','🇳🇿','🇴🇲','🇵🇦','🇵🇪','🇵🇫','🇵🇬','🇵🇭','🇵🇰','🇵🇱','🇵🇲','🇵🇳','🇵🇷','🇵🇸','🇵🇹','🇵🇼','🇵🇾','🇶🇦','🇷🇪','🇷🇴','🇷🇸','🇷🇼','🇸🇦','🇸🇧','🇸🇨','🏴󠁧󠁢󠁳󠁣󠁴󠁿','🇸🇩','🇸🇪','🇸🇬','🇸🇭','🇸🇮','🇸🇯','🇸🇰','🇸🇱','🇸🇲','🇸🇳','🇸🇴','🇸🇷','🇸🇸','🇸🇹','🇸🇻','🇸🇽','🇸🇾','🇸🇿','🇹🇦','🇹🇨','🇹🇩','🇹🇫','🇹🇬','🇹🇭','🇹🇯','🇹🇰','🇹🇱','🇹🇲','🇹🇳','🇹🇴','🇹🇷','🇹🇹','🇹🇻','🇹🇼','🇹🇿','🇺🇦','🇺🇬','🇺🇲','🇺🇾','🇺🇿','🇻🇦','🇻🇨','🇻🇪','🇻🇬','🇻🇮','🇻🇳','🇻🇺','🏴󠁧󠁢󠁷󠁬󠁳󠁿','🇼🇫','🇼🇸','🇽🇰','🇾🇪','🇾🇹','🇿🇦','🇿🇲','🇿🇼','🇫🇷','🇬🇧','🇮🇹','🇯🇵','🇰🇷','🏴‍☠️','🏳️‍🌈','🇷🇺','🚩','🇺���8','🏴','🏳️']
})

// 方法定义
const icon_select = (icon) => {
    emit('select', icon)
    if(activeName.value == 'second'){
        let list = JSON.parse(localStorage.getItem('icon_list'))
        if(list == null){
            list = [icon]
        }else{
            list.unshift(icon)
            if(list.length>15){
                list.pop()
            }
        }
        localStorage.setItem('icon_list',JSON.stringify(list))
    }
}

const init = () => {
    let list = JSON.parse(localStorage.getItem('icon_list'))
    if(list == null){
        list = []
        localStorage.setItem('icon_list',JSON.stringify(list))
    }
}

// 生命周期
onMounted(() => {
    init()
})
</script>

<style lang="scss" scoped>
  .container{
      width: 100%;
      .content{
          width: 290px;
          height:270px;
          overflow-x: hidden;
          overflow-y: scroll;
          background: #fff;
          .content-title{
              position: sticky;
              top: 0;
              padding: 6px;
              font-size: 12px;
              color: #999;
              font-weight: 400;
              background-color: hsla(0,0%,100%,.95);
              z-index: 1;
          }
          .emoji-wrap{
              width: 300px;
              .item{
                  position: relative;
                  width: 36px;
                  height: 36px;
                  display: inline-block;
                  box-sizing: border-box;
                  padding: 6px;
                  .item-icon{
                      width: 24px;
                      height: 24px;
                      display: inline-block;
                      background-image: url('../../assets/emoji/emoji.png');
                      background-size: 1100% 1000%;
                      background-position: 0% 0%;
                      cursor: pointer;
                  }
                  .item-icon-list{
                      font-size: 18px;
                      width: 24px;
                      height: 24px;
                      overflow: hidden;
                      display: inline-block;
                      cursor: pointer;
                  }
              }
              .item:hover{
                  background: #f0f0f0;
                  border-radius: 50%;
              }
          }
      }
      .content::-webkit-scrollbar {
          width: 0px;
      }
  }
  </style>