import { ref, computed, watch, nextTick } from 'vue'
import { useStorage, useSessionStorage } from '@vueuse/core'
import { useRouter, useRoute } from 'vue-router'
import { useCache } from './useCache'
import { useErrorHand<PERSON> } from './useErrorHandler'
import { useRouteGuard } from './useRouteGuard'

// 用户角色
export enum UserRole {
  GUEST = 'guest',
  USER = 'user',
  CREATOR = 'creator',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// 权限类型
export enum Permission {
  // 基础权限
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  
  // 内容权限
  CREATE_POST = 'create_post',
  EDIT_POST = 'edit_post',
  DELETE_POST = 'delete_post',
  PUBLISH_POST = 'publish_post',
  
  // 用户权限
  VIEW_USERS = 'view_users',
  EDIT_USERS = 'edit_users',
  DELETE_USERS = 'delete_users',
  BAN_USERS = 'ban_users',
  
  // 管理权限
  MANAGE_CONTENT = 'manage_content',
  MANAGE_USERS = 'manage_users',
  MANAGE_SYSTEM = 'manage_system',
  VIEW_ANALYTICS = 'view_analytics',
  
  // 高级权限
  SUPER_ADMIN = 'super_admin'
}

// 用户信息
export interface UserInfo {
  id: string
  username: string
  email: string
  avatar?: string
  displayName?: string
  bio?: string
  role: UserRole
  permissions: Permission[]
  isVerified: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  profile?: {
    firstName?: string
    lastName?: string
    phone?: string
    birthday?: string
    gender?: 'male' | 'female' | 'other'
    location?: string
    website?: string
    socialLinks?: {
      twitter?: string
      instagram?: string
      youtube?: string
      tiktok?: string
    }
  }
  preferences?: {
    language: string
    timezone: string
    theme: string
    notifications: {
      email: boolean
      push: boolean
      sms: boolean
    }
    privacy: {
      profileVisibility: 'public' | 'private' | 'friends'
      showEmail: boolean
      showPhone: boolean
    }
  }
  stats?: {
    postsCount: number
    followersCount: number
    followingCount: number
    likesCount: number
    viewsCount: number
  }
}

// 认证令牌
export interface AuthTokens {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
  expiresAt: number
}

// 登录凭据
export interface LoginCredentials {
  email?: string
  username?: string
  password: string
  rememberMe?: boolean
  captcha?: string
}

// 注册信息
export interface RegisterInfo {
  username: string
  email: string
  password: string
  confirmPassword: string
  firstName?: string
  lastName?: string
  agreeToTerms: boolean
  captcha?: string
}

// 密码重置信息
export interface PasswordResetInfo {
  email: string
  captcha?: string
}

// 密码更新信息
export interface PasswordUpdateInfo {
  token: string
  newPassword: string
  confirmPassword: string
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: UserInfo | null
  tokens: AuthTokens | null
  lastActivity: number
  sessionExpiry: number
}

// 认证配置
export interface AuthConfig {
  apiBaseUrl: string
  tokenStorageKey: string
  userStorageKey: string
  sessionTimeout: number
  refreshThreshold: number
  maxRetries: number
  redirectAfterLogin: string
  redirectAfterLogout: string
  enableAutoRefresh: boolean
  enableSessionTracking: boolean
}

// 认证事件
export interface AuthEvents {
  login: (user: UserInfo) => void
  logout: () => void
  tokenRefresh: (tokens: AuthTokens) => void
  sessionExpired: () => void
  userUpdated: (user: UserInfo) => void
  permissionChanged: (permissions: Permission[]) => void
}

// 默认配置
const defaultConfig: AuthConfig = {
  apiBaseUrl: '/api/auth',
  tokenStorageKey: 'auth_tokens',
  userStorageKey: 'auth_user',
  sessionTimeout: 30 * 60 * 1000, // 30分钟
  refreshThreshold: 5 * 60 * 1000, // 5分钟
  maxRetries: 3,
  redirectAfterLogin: '/dashboard',
  redirectAfterLogout: '/login',
  enableAutoRefresh: true,
  enableSessionTracking: true
}

/**
 * 认证系统 Composable
 * 提供完整的用户认证和授权功能
 */
export function useAuth(config: Partial<AuthConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }
  const { logError, logInfo } = useErrorHandler()
  const router = useRouter()
  const route = useRoute()
  
  const userCache = useCache<UserInfo>({
    maxSize: 100,
    defaultTTL: 60 * 60 * 1000, // 1小时
    storageKey: 'user_cache'
  })

  // 响应式状态
  const tokens = useStorage<AuthTokens | null>(finalConfig.tokenStorageKey, null)
  const user = useStorage<UserInfo | null>(finalConfig.userStorageKey, null)
  const isLoading = ref(false)
  const lastActivity = useSessionStorage('last_activity', Date.now())
  const sessionExpiry = useSessionStorage('session_expiry', 0)
  const eventListeners = ref<Partial<AuthEvents>>({})

  // 计算属性
  const isAuthenticated = computed(() => {
    return !!(tokens.value?.accessToken && user.value && !isTokenExpired.value)
  })

  const isTokenExpired = computed(() => {
    if (!tokens.value) return true
    return Date.now() >= tokens.value.expiresAt
  })

  const shouldRefreshToken = computed(() => {
    if (!tokens.value || !finalConfig.enableAutoRefresh) return false
    return Date.now() >= (tokens.value.expiresAt - finalConfig.refreshThreshold)
  })

  const isSessionExpired = computed(() => {
    if (!finalConfig.enableSessionTracking) return false
    return Date.now() >= sessionExpiry.value
  })

  const userRole = computed(() => user.value?.role || UserRole.GUEST)

  const userPermissions = computed(() => user.value?.permissions || [])

  const authState = computed<AuthState>(() => ({
    isAuthenticated: isAuthenticated.value,
    isLoading: isLoading.value,
    user: user.value,
    tokens: tokens.value,
    lastActivity: lastActivity.value,
    sessionExpiry: sessionExpiry.value
  }))

  // API 请求函数
  const apiRequest = async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const url = `${finalConfig.apiBaseUrl}${endpoint}`
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (tokens.value?.accessToken) {
      headers.Authorization = `${tokens.value.tokenType} ${tokens.value.accessToken}`
    }

    const response = await fetch(url, {
      ...options,
      headers
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Network error' }))
      throw new Error(error.message || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // 更新活动时间
  const updateActivity = (): void => {
    lastActivity.value = Date.now()
    if (finalConfig.enableSessionTracking) {
      sessionExpiry.value = Date.now() + finalConfig.sessionTimeout
    }
  }

  // 设置令牌
  const setTokens = (newTokens: AuthTokens): void => {
    tokens.value = {
      ...newTokens,
      expiresAt: Date.now() + (newTokens.expiresIn * 1000)
    }
    
    updateActivity()
    eventListeners.value.tokenRefresh?.(tokens.value)
  }

  // 清除认证信息
  const clearAuth = (): void => {
    tokens.value = null
    user.value = null
    lastActivity.value = 0
    sessionExpiry.value = 0
    userCache.clear()
  }

  // 登录
  const login = async (credentials: LoginCredentials): Promise<UserInfo> => {
    try {
      isLoading.value = true

      const response = await apiRequest<{
        user: UserInfo
        tokens: AuthTokens
      }>('/login', {
        method: 'POST',
        body: JSON.stringify(credentials)
      })

      // 设置用户信息和令牌
      user.value = response.user
      setTokens(response.tokens)

      // 缓存用户信息
      userCache.set(response.user.id, response.user)

      // 触发登录事件
      eventListeners.value.login?.(response.user)

      logInfo('User logged in successfully', {
        userId: response.user.id,
        username: response.user.username
      })

      return response.user

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'login',
        credentials: { ...credentials, password: '[REDACTED]' }
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (registerInfo: RegisterInfo): Promise<UserInfo> => {
    try {
      isLoading.value = true

      const response = await apiRequest<{
        user: UserInfo
        tokens: AuthTokens
      }>('/register', {
        method: 'POST',
        body: JSON.stringify(registerInfo)
      })

      // 设置用户信息和令牌
      user.value = response.user
      setTokens(response.tokens)

      // 缓存用户信息
      userCache.set(response.user.id, response.user)

      // 触发登录事件
      eventListeners.value.login?.(response.user)

      logInfo('User registered successfully', {
        userId: response.user.id,
        username: response.user.username
      })

      return response.user

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'register',
        registerInfo: { ...registerInfo, password: '[REDACTED]', confirmPassword: '[REDACTED]' }
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async (everywhere = false): Promise<void> => {
    try {
      isLoading.value = true

      if (tokens.value?.accessToken) {
        await apiRequest('/logout', {
          method: 'POST',
          body: JSON.stringify({ everywhere })
        })
      }

      // 清除认证信息
      clearAuth()

      // 触发登出事件
      eventListeners.value.logout?.()

      logInfo('User logged out successfully')

      // 重定向到登录页
      if (router && route.path !== finalConfig.redirectAfterLogout) {
        await router.push(finalConfig.redirectAfterLogout)
      }

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'logout'
      })
      
      // 即使API调用失败，也要清除本地认证信息
      clearAuth()
      eventListeners.value.logout?.()
      
    } finally {
      isLoading.value = false
    }
  }

  // 刷新令牌
  const refreshToken = async (): Promise<AuthTokens> => {
    try {
      if (!tokens.value?.refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await apiRequest<{ tokens: AuthTokens }>('/refresh', {
        method: 'POST',
        body: JSON.stringify({
          refreshToken: tokens.value.refreshToken
        })
      })

      setTokens(response.tokens)
      return response.tokens

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'refreshToken'
      })
      
      // 刷新失败，清除认证信息
      await logout()
      throw error
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<UserInfo> => {
    try {
      const response = await apiRequest<{ user: UserInfo }>('/me')
      
      user.value = response.user
      userCache.set(response.user.id, response.user)
      
      eventListeners.value.userUpdated?.(response.user)
      
      return response.user

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'getCurrentUser'
      })
      throw error
    }
  }

  // 更新用户信息
  const updateUser = async (updates: Partial<UserInfo>): Promise<UserInfo> => {
    try {
      isLoading.value = true

      const response = await apiRequest<{ user: UserInfo }>('/me', {
        method: 'PATCH',
        body: JSON.stringify(updates)
      })

      user.value = response.user
      userCache.set(response.user.id, response.user)
      
      eventListeners.value.userUpdated?.(response.user)

      return response.user

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'updateUser',
        updates
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 更改密码
  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      isLoading.value = true

      await apiRequest('/change-password', {
        method: 'POST',
        body: JSON.stringify({
          currentPassword,
          newPassword
        })
      })

      logInfo('Password changed successfully')

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'changePassword'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 请求密码重置
  const requestPasswordReset = async (resetInfo: PasswordResetInfo): Promise<void> => {
    try {
      isLoading.value = true

      await apiRequest('/forgot-password', {
        method: 'POST',
        body: JSON.stringify(resetInfo)
      })

      logInfo('Password reset email sent')

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'requestPasswordReset',
        email: resetInfo.email
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 重置密码
  const resetPassword = async (updateInfo: PasswordUpdateInfo): Promise<void> => {
    try {
      isLoading.value = true

      await apiRequest('/reset-password', {
        method: 'POST',
        body: JSON.stringify(updateInfo)
      })

      logInfo('Password reset successfully')

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'resetPassword'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 权限检查
  const hasPermission = (permission: Permission): boolean => {
    if (!user.value) return false
    return userPermissions.value.includes(permission) || userPermissions.value.includes(Permission.SUPER_ADMIN)
  }

  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  // 角色检查
  const hasRole = (role: UserRole): boolean => {
    if (!user.value) return false
    return user.value.role === role
  }

  const hasAnyRole = (roles: UserRole[]): boolean => {
    return roles.some(role => hasRole(role))
  }

  const isAdmin = computed(() => {
    return hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN])
  })

  const isModerator = computed(() => {
    return hasAnyRole([UserRole.MODERATOR, UserRole.ADMIN, UserRole.SUPER_ADMIN])
  })

  const isCreator = computed(() => {
    return hasAnyRole([UserRole.CREATOR, UserRole.MODERATOR, UserRole.ADMIN, UserRole.SUPER_ADMIN])
  })

  // 事件监听
  const on = <K extends keyof AuthEvents>(event: K, listener: AuthEvents[K]): void => {
    eventListeners.value[event] = listener
  }

  const off = <K extends keyof AuthEvents>(event: K): void => {
    delete eventListeners.value[event]
  }

  // 会话管理
  const extendSession = (): void => {
    updateActivity()
  }

  const checkSession = (): boolean => {
    if (isSessionExpired.value) {
      eventListeners.value.sessionExpired?.()
      logout()
      return false
    }
    return true
  }

  // 自动刷新令牌
  const setupAutoRefresh = (): void => {
    if (!finalConfig.enableAutoRefresh) return

    const checkAndRefresh = async () => {
      if (shouldRefreshToken.value && isAuthenticated.value) {
        try {
          await refreshToken()
        } catch (error) {
          // 刷新失败会自动登出
        }
      }
    }

    // 每分钟检查一次
    setInterval(checkAndRefresh, 60 * 1000)
  }

  // 初始化认证系统
  const init = async (): Promise<void> => {
    try {
      // 检查现有令牌
      if (tokens.value && !isTokenExpired.value) {
        try {
          // 验证令牌并获取用户信息
          await getCurrentUser()
          updateActivity()
        } catch (error) {
          // 令牌无效，清除认证信息
          clearAuth()
        }
      } else if (tokens.value && isTokenExpired.value) {
        // 令牌过期，尝试刷新
        try {
          await refreshToken()
          await getCurrentUser()
        } catch (error) {
          // 刷新失败，清除认证信息
          clearAuth()
        }
      }

      // 设置自动刷新
      setupAutoRefresh()

      // 监听路由变化，更新活动时间
      if (router) {
        router.afterEach(() => {
          if (isAuthenticated.value) {
            updateActivity()
          }
        })
      }

      // 监听页面可见性变化
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden && isAuthenticated.value) {
          checkSession()
        }
      })

      // 监听用户活动
      const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
      const throttledUpdateActivity = throttle(updateActivity, 30000) // 30秒节流

      activityEvents.forEach(event => {
        document.addEventListener(event, throttledUpdateActivity, true)
      })

    } catch (error) {
      logError(error as Error, {
        context: 'Auth',
        action: 'init'
      })
    }
  }

  // 节流函数
  function throttle<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout | null = null
    let lastExecTime = 0
    
    return ((...args: any[]) => {
      const currentTime = Date.now()
      
      if (currentTime - lastExecTime > delay) {
        func(...args)
        lastExecTime = currentTime
      } else {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        timeoutId = setTimeout(() => {
          func(...args)
          lastExecTime = Date.now()
        }, delay - (currentTime - lastExecTime))
      }
    }) as T
  }

  return {
    // 响应式状态
    isAuthenticated,
    isLoading,
    user: readonly(user),
    tokens: readonly(tokens),
    authState,
    
    // 计算属性
    userRole,
    userPermissions,
    isAdmin,
    isModerator,
    isCreator,
    isTokenExpired,
    shouldRefreshToken,
    isSessionExpired,
    
    // 认证方法
    login,
    register,
    logout,
    refreshToken,
    
    // 用户管理
    getCurrentUser,
    updateUser,
    changePassword,
    requestPasswordReset,
    resetPassword,
    
    // 权限检查
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    hasAnyRole,
    
    // 会话管理
    extendSession,
    checkSession,
    updateActivity,
    
    // 事件监听
    on,
    off,
    
    // 初始化
    init
  }
}

/**
 * 权限检查 Hook
 * 简化的权限检查接口
 */
export function usePermissions() {
  const { hasPermission, hasAnyPermission, hasAllPermissions, userPermissions } = useAuth()
  
  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissions: userPermissions
  }
}

/**
 * 角色检查 Hook
 * 简化的角色检查接口
 */
export function useRoles() {
  const { hasRole, hasAnyRole, userRole, isAdmin, isModerator, isCreator } = useAuth()
  
  return {
    hasRole,
    hasAnyRole,
    role: userRole,
    isAdmin,
    isModerator,
    isCreator
  }
}

/**
 * 认证守卫 Hook
 * 用于路由守卫
 */
export function useAuthGuard() {
  const { isAuthenticated, hasPermission, hasRole } = useAuth()
  const { registerGuard } = useRouteGuard()
  
  // 注册认证守卫
  const requireAuth = () => {
    registerGuard({
      name: 'auth',
      type: 'authentication',
      enabled: true,
      execute: async () => {
        if (!isAuthenticated.value) {
          return {
            allowed: false,
            redirect: '/login',
            message: '请先登录'
          }
        }
        return { allowed: true }
      }
    })
  }
  
  // 注册权限守卫
  const requirePermission = (permission: Permission) => {
    registerGuard({
      name: `permission-${permission}`,
      type: 'permission',
      enabled: true,
      execute: async () => {
        if (!hasPermission(permission)) {
          return {
            allowed: false,
            redirect: '/403',
            message: '权限不足'
          }
        }
        return { allowed: true }
      }
    })
  }
  
  // 注册角色守卫
  const requireRole = (role: UserRole) => {
    registerGuard({
      name: `role-${role}`,
      type: 'role',
      enabled: true,
      execute: async () => {
        if (!hasRole(role)) {
          return {
            allowed: false,
            redirect: '/403',
            message: '角色权限不足'
          }
        }
        return { allowed: true }
      }
    })
  }
  
  return {
    requireAuth,
    requirePermission,
    requireRole
  }
}