package wallets

import (
	walletSrv "frontapi/internal/service/wallets"
)

// WithdrawRequestController 提现申请控制器
type WithdrawRequestController struct {
	withdrawRequestService walletSrv.WithdrawRequestService
}

// NewWithdrawRequestController 创建提现申请控制器实例
func NewWithdrawRequestController(withdrawRequestService walletSrv.WithdrawRequestService) *WithdrawRequestController {
	return &WithdrawRequestController{withdrawRequestService: withdrawRequestService}
}
