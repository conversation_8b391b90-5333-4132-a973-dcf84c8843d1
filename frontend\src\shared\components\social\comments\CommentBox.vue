<template>
  <div class="comment-box-wrapper" :data-theme="theme">
    <!-- 内嵌模式 -->
    <div v-if="!isDialog" class="comment-box-inline" :class="{ 'compact': compact }">
      <!-- 评论统计和展开按钮 -->
      <div class="comment-header" @click="toggleExpanded">
        <div class="comment-stats">
          <i class="icon-comment"></i>
          <span class="comment-count">{{ totalComments }}</span>
          <span class="comment-text">条评论</span>
        </div>
        <div class="expand-actions">
          <button 
            v-if="!expanded && !compact" 
            class="btn-expand"
            @click.stop="openDialog"
          >
            <i class="icon-expand"></i>
            展开评论
          </button>
          <button 
            v-if="compact" 
            class="btn-popup"
            @click.stop="openDialog"
          >
            <i class="icon-popup"></i>
            评论
          </button>
          <button 
            v-if="expanded" 
            class="btn-collapse"
            @click.stop="toggleExpanded"
          >
            <i class="icon-collapse"></i>
            收起
          </button>
        </div>
      </div>

      <!-- 展开的评论区域 -->
      <div v-if="expanded" class="comment-content">
        <EnhancedCommentInput
          :support-video="supportVideo"
          :support-image="supportImage"
          :placeholder="placeholder"
          @submit="handleSubmit"
        />
        <EnhancedCommentList
          :comments="comments"
          :loading="loading"
          @like="handleLike"
          @reply="handleReply"
          @load-more="loadMore"
        />
      </div>
    </div>

    <!-- 弹出对话框模式 -->
    <el-dialog
      v-model="dialogVisible"
      :title="`评论 (${totalComments})`"
      width="680px"
      :before-close="handleDialogClose"
      class="comment-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      append-to-body
    >
      <div class="comment-dialog-content">
        <EnhancedCommentInput
          :support-video="supportVideo"
          :support-image="supportImage"
          :placeholder="placeholder"
          @submit="handleSubmit"
        />
        <EnhancedCommentList
          :comments="comments"
          :loading="loading"
          @like="handleLike"
          @reply="handleReply"
          @load-more="loadMore"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import EnhancedCommentInput from './EnhancedCommentInput.vue'
import EnhancedCommentList from './EnhancedCommentList.vue'
import type { Comment, CommentSubmitData } from '../../types/comment'

// 组件属性
interface Props {
  // 基础配置
  targetId: string // 评论目标ID（帖子ID、视频ID等）
  targetType: 'post' | 'video' | 'image' | 'shortvideo' // 评论目标类型
  
  // 显示模式
  mode?: 'inline' | 'dialog' | 'compact' // 显示模式：内嵌、仅对话框、紧凑
  compact?: boolean // 紧凑模式
  
  // 媒体支持
  supportMedia?: boolean // 是否支持媒体上传
  supportVideo?: boolean // 是否支持视频
  supportImage?: boolean // 是否支持图片
  
  // 自定义配置
  placeholder?: string // 输入框占位符
  maxComments?: number // 最大显示评论数
  autoLoad?: boolean // 是否自动加载评论
  
  // 样式配置
  theme?: 'light' | 'dark' // 主题
  borderRadius?: string // 圆角大小
  
  // 权限配置
  allowAnonymous?: boolean // 是否允许匿名评论
  requireLogin?: boolean // 是否需要登录
}

// 组件事件
interface Emits {
  'comment-added': [comment: Comment]
  'comment-liked': [commentId: string, liked: boolean]
  'comment-replied': [parentId: string, reply: Comment]
  'dialog-opened': []
  'dialog-closed': []
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'inline',
  compact: false,
  supportMedia: true,
  supportVideo: true,
  supportImage: true,
  placeholder: '写下你的评论...',
  maxComments: 50,
  autoLoad: true,
  theme: 'light',
  borderRadius: '12px',
  allowAnonymous: false,
  requireLogin: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const expanded = ref(false)
const dialogVisible = ref(false)
const loading = ref(false)
const comments = ref<Comment[]>([])
const totalComments = ref(0)
const currentPage = ref(1)
const hasMore = ref(true)

// 计算属性
const isDialog = computed(() => props.mode === 'dialog')

// 方法
const loadComments = async (page = 1) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    // TODO: 调用API获取评论
    // const response = await getComments({
    //   targetId: props.targetId,
    //   targetType: props.targetType,
    //   page,
    //   pageSize: 20
    // })
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const mockComments: Comment[] = [
      {
        id: `comment-${Date.now()}`,
        content: '这个内容很棒！期待更多精彩内容。',
        author: {
          id: 'user1',
          username: '用户A',
          nickname: '用户A',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user1',
          isVerified: true
        },
        targetId: props.targetId,
        targetType: props.targetType,
        likes: 12,
        isLiked: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        replies: [
          {
            id: `reply-${Date.now()}`,
            content: '同意！非常有用的分享',
            author: {
              id: 'user2',
              username: '用户B',
              nickname: '用户B',
              avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2'
            },
            targetId: props.targetId,
            targetType: props.targetType,
            parentId: `comment-${Date.now()}`,
            likes: 3,
            isLiked: true,
            createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString()
          }
        ]
      }
    ]
    
    if (page === 1) {
      comments.value = mockComments
    } else {
      comments.value.push(...mockComments)
    }
    
    totalComments.value = 25 // 模拟总数
    currentPage.value = page
    hasMore.value = page < 3 // 模拟分页
    
  } catch (error) {
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async (data: CommentSubmitData) => {
  try {
    // TODO: 调用API提交评论
    // const response = await createComment({
    //   targetId: props.targetId,
    //   targetType: props.targetType,
    //   ...data
    // })
    
    // 模拟新评论
    const newComment: Comment = {
      id: `comment-${Date.now()}`,
      content: data.content,
      author: {
        id: 'current-user',
        username: '当前用户',
        nickname: '当前用户',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=current'
      },
      targetId: props.targetId,
      targetType: props.targetType,
      likes: 0,
      isLiked: false,
      createdAt: new Date().toISOString(),
      media: data.media
    }
    
    comments.value.unshift(newComment)
    totalComments.value++
    emit('comment-added', newComment)
    ElMessage.success('评论发布成功')
    
  } catch (error) {
    ElMessage.error('评论发布失败')
  }
}

const handleLike = async (commentId: string) => {
  const comment = comments.value.find(c => c.id === commentId)
  if (!comment) return
  
  try {
    // TODO: 调用API切换点赞
    // await toggleCommentLike(commentId)
    
    comment.isLiked = !comment.isLiked
    comment.likes += comment.isLiked ? 1 : -1
    emit('comment-liked', commentId, comment.isLiked)
    
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleReply = async (parentId: string, replyData: CommentSubmitData) => {
  try {
    // TODO: 调用API提交回复
    // const response = await createReply({
    //   parentId,
    //   targetId: props.targetId,
    //   targetType: props.targetType,
    //   ...replyData
    // })
    
    const reply: Comment = {
      id: `reply-${Date.now()}`,
      content: replyData.content,
      author: {
        id: 'current-user',
        username: '当前用户',
        nickname: '当前用户',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=current'
      },
      targetId: props.targetId,
      targetType: props.targetType,
      parentId,
      likes: 0,
      isLiked: false,
      createdAt: new Date().toISOString(),
      media: replyData.media
    }
    
    const parentComment = comments.value.find(c => c.id === parentId)
    if (parentComment) {
      if (!parentComment.replies) {
        parentComment.replies = []
      }
      parentComment.replies.push(reply)
      totalComments.value++
      emit('comment-replied', parentId, reply)
      ElMessage.success('回复发布成功')
    }
    
  } catch (error) {
    ElMessage.error('回复发布失败')
  }
}

const toggleExpanded = () => {
  expanded.value = !expanded.value
  if (expanded.value && comments.value.length === 0) {
    loadComments()
  }
}

const openDialog = () => {
  dialogVisible.value = true
  emit('dialog-opened')
  if (comments.value.length === 0) {
    loadComments()
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  emit('dialog-closed')
}

const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadComments(currentPage.value + 1)
  }
}

// 监听器
watch(() => props.targetId, () => {
  if (props.autoLoad) {
    loadComments()
  }
}, { immediate: false })

// 生命周期
onMounted(() => {
  if (props.mode === 'dialog') {
    dialogVisible.value = true
  }
  
  // 自动加载评论
  if (props.autoLoad && props.targetId) {
    loadComments()
  }
})

// 暴露方法
defineExpose({
  loadComments,
  openDialog,
  refresh: () => loadComments(1)
})
</script>

<style scoped lang="scss">
.comment-box-wrapper {
  --comment-border-radius: v-bind(borderRadius);
  --comment-theme: v-bind(theme);
}

.comment-box-inline {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: var(--comment-border-radius);
  overflow: hidden;
  transition: all 0.3s ease;

  &.compact {
    border-radius: 8px;
    
    .comment-header {
      padding: 8px 12px;
      
      .comment-stats {
        font-size: 13px;
      }
      
      .btn-popup {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f3f4f6;
  }

  .comment-stats {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 14px;

    .icon-comment {
      width: 16px;
      height: 16px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'/%3E%3C/svg%3E") no-repeat center;
      background-size: contain;
    }

    .comment-count {
      font-weight: 600;
      color: #374151;
    }
  }

  .expand-actions {
    display: flex;
    gap: 8px;

    button {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &.btn-expand, &.btn-popup {
        background: #3b82f6;
        color: white;

        &:hover {
          background: #2563eb;
          transform: translateY(-1px);
        }
      }

      &.btn-collapse {
        background: #e5e7eb;
        color: #6b7280;

        &:hover {
          background: #d1d5db;
          color: #374151;
        }
      }

      i {
        width: 14px;
        height: 14px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;

        &.icon-expand {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 14l-7 7m0 0l-7-7m7 7V3'/%3E%3C/svg%3E");
        }

        &.icon-popup {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14'/%3E%3C/svg%3E");
        }

        &.icon-collapse {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 10l7-7m0 0l7 7m-7-7v18'/%3E%3C/svg%3E");
        }
      }
    }
  }
}

.comment-content {
  padding: 16px;
  background: #ffffff;
}

// 对话框样式
:deep(.comment-dialog) {
  .el-dialog__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__headerbtn {
    top: 16px;
    right: 20px;
    width: 32px;
    height: 32px;
    background: #f3f4f6;
    border-radius: 50%;
    transition: all 0.2s ease;

    &:hover {
      background: #e5e7eb;
    }

    .el-dialog__close {
      color: #6b7280;
      font-size: 16px;
    }
  }
}

.comment-dialog-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;

    &:hover {
      background: #9ca3af;
    }
  }
}

// 暗色主题
.comment-box-wrapper[data-theme="dark"] {
  .comment-box-inline {
    background: #1f2937;
    border-color: #374151;

    &:hover {
      border-color: #4b5563;
    }
  }

  .comment-header {
    background: #111827;
    border-bottom-color: #374151;

    &:hover {
      background: #0f172a;
    }

    .comment-stats {
      color: #9ca3af;

      .comment-count {
        color: #f3f4f6;
      }
    }
  }

  .comment-content {
    background: #1f2937;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .comment-header {
    padding: 10px 12px;

    .comment-stats {
      font-size: 13px;
    }

    .expand-actions button {
      padding: 5px 8px;
      font-size: 12px;
    }
  }

  .comment-content {
    padding: 12px;
  }

  :deep(.comment-dialog) {
    width: 95vw !important;
    margin: 0 auto;
  }

  .comment-dialog-content {
    padding: 16px;
    max-height: 60vh;
  }
}
</style> 