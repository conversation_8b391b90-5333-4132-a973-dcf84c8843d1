package users

import (
	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreateLoginLogRequest 创建登录日志请求
type CreateLoginLogRequest struct {
	UserID         string `json:"userId" validate:"required"`
	IP             string `json:"ip"`
	Location       string `json:"location"`
	UserAgent      string `json:"userAgent"`
	DeviceType     string `json:"deviceType"`
	OsName         string `json:"os_name"`
	OsVersion      string `json:"os_version"`
	BrowserName    string `json:"browser_name"`
	BrowserVersion string `json:"browser_version"`
	LoginType      string `json:"loginType"` // 登录类型：password, token, oauth2, etc.
	Status         string `json:"status"`    // 登录状态：成功/失败
	Remark         string `json:"remark"`    // 备注信息，如失败原因
}

// UserLoginLogsService 用户登录日志服务接口
type UserLoginLogsService interface {
	base.IExtendedService[users.UserLoginLogs]
}

// userLoginLogsService 用户登录日志服务实现
type userLoginLogsService struct {
	*base.ExtendedService[users.UserLoginLogs]
	loginLogsRepo repo.UserLoginLogsRepository
	userRepo      repo.UserRepository
}

// NewUserLoginLogsService 创建用户登录日志服务实例
func NewUserLoginLogsService(
	loginLogsRepo repo.UserLoginLogsRepository,
	userRepo repo.UserRepository,
) UserLoginLogsService {
	return &userLoginLogsService{
		ExtendedService: base.NewExtendedService[users.UserLoginLogs](loginLogsRepo, "user_login_logs"),
		loginLogsRepo:   loginLogsRepo,
		userRepo:        userRepo,
	}
}
