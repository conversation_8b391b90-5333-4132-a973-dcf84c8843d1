package sys

import (
	"context"
	"fmt"
	"frontapi/config"
	"frontapi/internal/models/sys"
	sysRepo "frontapi/internal/repository/sys"
	"math/rand"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// DatabaseService 数据库服务接口
type DatabaseService interface {
	// GetTableList 获取数据库表列表
	GetTableList(ctx context.Context) ([]sys.TableInfo, error)

	// GetTableDetail 获取表详情（包含字段、外键、建表语句）
	GetTableDetail(ctx context.Context, tableName string) (*sys.TableDetailResponse, error)

	// GenerateMockData 生成Mock数据
	GenerateMockData(ctx context.Context, req *sys.MockDataRequest) (*sys.MockDataResponse, error)

	// InsertMockDataToDB 将Mock数据插入数据库
	InsertMockDataToDB(ctx context.Context, tableName string, data []map[string]interface{}) error

	// GetForeignKeyData 获取外键关联表的数据
	GetForeignKeyData(ctx context.Context, tableName, columnName string, limit int) ([]string, error)
}

// databaseService 数据库服务实现
type databaseService struct {
	databaseRepo sysRepo.DatabaseRepository
}

// NewDatabaseService 创建数据库服务实例
func NewDatabaseService(databaseRepo sysRepo.DatabaseRepository) DatabaseService {
	return &databaseService{
		databaseRepo: databaseRepo,
	}
}

// GetTableList 获取数据库表列表
func (s *databaseService) GetTableList(ctx context.Context) ([]sys.TableInfo, error) {
	return s.databaseRepo.GetTableList(ctx, config.AppConfig.Database.Name)
}

// GetTableDetail 获取表详情
func (s *databaseService) GetTableDetail(ctx context.Context, tableName string) (*sys.TableDetailResponse, error) {
	// 验证表名不能为空
	if tableName == "" {
		return nil, fmt.Errorf("表名不能为空")
	}

	// 获取表基本信息
	tableInfo, err := s.databaseRepo.GetTableInfo(ctx, config.AppConfig.Database.Name, tableName)
	if err != nil {
		return nil, fmt.Errorf("获取表信息失败: %v", err)
	}

	// 检查表是否存在
	if tableInfo == nil {
		return nil, fmt.Errorf("表 '%s' 不存在", tableName)
	}

	// 获取字段信息
	columns, err := s.databaseRepo.GetTableColumns(ctx, config.AppConfig.Database.Name, tableName)
	if err != nil {
		return nil, fmt.Errorf("获取字段信息失败: %v", err)
	}

	// 获取外键信息
	foreignKeys, err := s.databaseRepo.GetTableForeignKeys(ctx, config.AppConfig.Database.Name, tableName)
	if err != nil {
		return nil, fmt.Errorf("获取外键信息失败: %v", err)
	}

	// 获取建表语句
	createSQL, err := s.databaseRepo.GetCreateTableSQL(ctx, tableName)
	if err != nil {
		return nil, fmt.Errorf("获取建表语句失败: %v", err)
	}

	return &sys.TableDetailResponse{
		TableInfo:   *tableInfo,
		Columns:     columns,
		ForeignKeys: foreignKeys,
		CreateSQL:   createSQL,
	}, nil
}

// GenerateMockData 生成Mock数据
func (s *databaseService) GenerateMockData(ctx context.Context, req *sys.MockDataRequest) (*sys.MockDataResponse, error) {
	// 获取表字段信息
	columns, err := s.databaseRepo.GetTableColumns(ctx, config.AppConfig.Database.Name, req.TableName)
	if err != nil {
		return nil, fmt.Errorf("获取表字段信息失败: %v", err)
	}

	// 获取外键信息
	foreignKeys, err := s.databaseRepo.GetTableForeignKeys(ctx, config.AppConfig.Database.Name, req.TableName)
	if err != nil {
		return nil, fmt.Errorf("获取外键信息失败: %v", err)
	}

	// 创建外键数据缓存
	fkDataCache := make(map[string][]interface{})

	// 预加载外键数据（排除被删除的外键）
	for _, fk := range foreignKeys {
		isExcluded := false
		for _, excludedFK := range req.ExcludedFKs {
			if fk.ConstraintName == excludedFK {
				isExcluded = true
				break
			}
		}

		if !isExcluded {
			fkData, err := s.databaseRepo.GetForeignKeyData(ctx, fk.ReferencedTableName, fk.ReferencedColumnName, 100)
			if err == nil && len(fkData) > 0 {
				fkDataCache[fk.ColumnName] = fkData
			}
		}
	}

	// 生成Mock数据
	var mockData []map[string]interface{}

	for i := 0; i < req.Count; i++ {
		row := make(map[string]interface{})

		for _, column := range columns {
			// 跳过自增字段
			if strings.Contains(column.Extra, "auto_increment") {
				continue
			}

			value := s.generateColumnValue(column, fkDataCache, req.MockRules)
			row[column.ColumnName] = value
		}

		mockData = append(mockData, row)
	}

	// 生成SQL语句
	sql := s.generateInsertSQL(req.TableName, mockData)

	response := &sys.MockDataResponse{
		TableName:    req.TableName,
		Count:        req.Count,
		Data:         mockData,
		SQL:          sql,
		InsertedToDB: false,
	}

	// 如果需要插入数据库
	if req.InsertToDB {
		err := s.databaseRepo.InsertMockData(ctx, req.TableName, mockData)
		if err != nil {
			return nil, fmt.Errorf("插入数据库失败: %v", err)
		}
		response.InsertedToDB = true
	}

	return response, nil
}

// InsertMockDataToDB 将Mock数据插入数据库
func (s *databaseService) InsertMockDataToDB(ctx context.Context, tableName string, data []map[string]interface{}) error {
	return s.databaseRepo.InsertMockData(ctx, tableName, data)
}

// GetForeignKeyData 获取外键关联表的数据
func (s *databaseService) GetForeignKeyData(ctx context.Context, tableName, columnName string, limit int) ([]string, error) {
	// 调用repository层获取数据
	data, err := s.databaseRepo.GetForeignKeyData(ctx, tableName, columnName, limit)
	if err != nil {
		return nil, fmt.Errorf("获取外键关联数据失败: %v", err)
	}

	// 将interface{}转换为string
	var result []string
	for _, item := range data {
		if item != nil {
			result = append(result, fmt.Sprintf("%v", item))
		}
	}

	return result, nil
}

// generateColumnValue 生成字段值
func (s *databaseService) generateColumnValue(column sys.ColumnInfo, fkDataCache map[string][]interface{}, mockRules map[string]string) interface{} {
	// 优先使用自定义Mock规则
	if rule, exists := mockRules[column.ColumnName]; exists && rule != "" {
		return s.applyMockRule(rule, column)
	}

	// 检查是否为外键字段
	if fkData, exists := fkDataCache[column.ColumnName]; exists && len(fkData) > 0 {
		return fkData[rand.Intn(len(fkData))]
	}

	// 检查是否为可能的外键字段（通过字段名判断）
	if s.isForeignKeyField(column.ColumnName) {
		// 如果是外键字段但没有缓存数据，可能需要生成UUID或返回null
		if column.IsNullable == "YES" {
			// 30%的概率返回null
			if rand.Intn(100) < 30 {
				return nil
			}
		}
		// 生成UUID作为外键值
		return uuid.New().String()
	}

	// 根据字段类型和名称生成值
	return s.generateValueByType(column)
}

// isForeignKeyField 判断是否为外键字段
func (s *databaseService) isForeignKeyField(columnName string) bool {
	columnName = strings.ToLower(columnName)

	// 常见的外键字段命名模式
	fkPatterns := []string{
		"_id$",        // 以_id结尾
		"^.*_id$",     // 任何以_id结尾的字段
		"user_id",     // 用户ID
		"category_id", // 分类ID
		"parent_id",   // 父级ID
		"creator_id",  // 创建者ID
		"author_id",   // 作者ID
		"owner_id",    // 所有者ID
	}

	for _, pattern := range fkPatterns {
		if matched, _ := regexp.MatchString(pattern, columnName); matched {
			return true
		}
	}

	return false
}

// generateValueByType 根据字段类型生成值
func (s *databaseService) generateValueByType(column sys.ColumnInfo) interface{} {
	dataType := strings.ToLower(column.DataType)
	columnName := strings.ToLower(column.ColumnName)
	columnType := strings.ToLower(column.ColumnType)

	// 处理tinyint类型
	if dataType == "tinyint" {
		// 解析注释中的枚举值
		if enumValues := s.parseEnumFromComment(column.ColumnComment); len(enumValues) > 0 {
			return enumValues[rand.Intn(len(enumValues))]
		}

		// 根据字段名判断类型
		if strings.Contains(columnName, "verified") || strings.Contains(columnName, "enabled") ||
			strings.Contains(columnName, "active") || strings.Contains(columnName, "status") {
			return rand.Intn(2) // 0 或 1
		}

		// 性别字段
		if strings.Contains(columnName, "gender") || strings.Contains(columnName, "sex") {
			return rand.Intn(3) // 0-未知，1-男，2-女
		}

		return rand.Intn(128) // 默认tinyint范围
	}

	// 处理其他整数类型
	if strings.Contains(dataType, "int") {
		switch dataType {
		case "smallint":
			return rand.Intn(32768)
		case "mediumint":
			return rand.Intn(8388608)
		case "bigint":
			return rand.Int63n(9223372036854775807)
		default: // int
			return rand.Intn(2147483647)
		}
	}

	// 处理字符串类型
	if dataType == "varchar" || dataType == "char" || dataType == "text" {
		// 获取字段长度限制
		maxLength := s.extractFieldLength(columnType)

		// 根据字段名生成特定格式的数据
		if strings.Contains(columnName, "email") {
			email := fmt.Sprintf("<EMAIL>", rand.Intn(10000))
			return s.truncateString(email, maxLength)
		}
		if strings.Contains(columnName, "phone") {
			phone := fmt.Sprintf("1%010d", rand.Intn(10000000000))
			return s.truncateString(phone, maxLength)
		}
		if strings.Contains(columnName, "url") {
			url := fmt.Sprintf("https://example.com/path%d", rand.Intn(1000))
			return s.truncateString(url, maxLength)
		}
		if strings.Contains(columnName, "username") || strings.Contains(columnName, "password") ||
			strings.Contains(columnName, "salt") || strings.Contains(columnName, "token") ||
			strings.Contains(columnName, "code") {
			// 生成英文字符串
			englishStr := s.generateEnglishString(maxLength)
			return englishStr
		}
		if strings.Contains(columnName, "location") || strings.Contains(columnName, "address") {
			addresses := []string{"北京市朝阳区", "上海市浦东新区", "广州市天河区", "深圳市南山区", "杭州市西湖区", "成都市锦江区"}
			address := addresses[rand.Intn(len(addresses))]
			return s.truncateString(address, maxLength)
		}
		if strings.Contains(columnName, "ip") {
			return fmt.Sprintf("%d.%d.%d.%d", rand.Intn(256), rand.Intn(256), rand.Intn(256), rand.Intn(256))
		}
		if strings.Contains(columnName, "device") {
			devices := []string{"PC Web", "Android", "iPhone", "iPad", "Mac", "Windows"}
			return devices[rand.Intn(len(devices))]
		}
		if strings.Contains(columnName, "name") || strings.Contains(columnName, "title") {
			names := []string{"张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"}
			name := names[rand.Intn(len(names))]
			return s.truncateString(name, maxLength)
		}
		if strings.Contains(columnName, "id") && !strings.Contains(columnName, "user_id") {
			return uuid.New().String()
		}

		// 默认字符串
		defaultStr := fmt.Sprintf("测试数据%d", rand.Intn(10000))
		return s.truncateString(defaultStr, maxLength)
	}

	// 处理浮点数类型
	if dataType == "decimal" || dataType == "float" || dataType == "double" {
		return rand.Float64() * 1000
	}

	// 处理日期时间类型
	if dataType == "datetime" || dataType == "timestamp" {
		return time.Now().Add(-time.Duration(rand.Intn(365*24)) * time.Hour).Format("2006-01-02 15:04:05")
	}

	if dataType == "date" {
		return time.Now().Add(-time.Duration(rand.Intn(365*24)) * time.Hour).Format("2006-01-02")
	}

	if dataType == "time" {
		return fmt.Sprintf("%02d:%02d:%02d", rand.Intn(24), rand.Intn(60), rand.Intn(60))
	}

	// 处理JSON类型
	if dataType == "json" {
		return `{"key": "value"}`
	}

	// 默认返回字符串
	defaultStr := fmt.Sprintf("默认值%d", rand.Intn(1000))
	return s.truncateString(defaultStr, 255)
}

// parseEnumFromComment 从注释中解析枚举值
func (s *databaseService) parseEnumFromComment(comment string) []int {
	if comment == "" {
		return nil
	}

	var values []int

	// 匹配模式1：0-未知，1-男，2-女
	re1 := regexp.MustCompile(`(\d+)[-]([^，,]+)[，,]?`)
	matches1 := re1.FindAllStringSubmatch(comment, -1)
	for _, match := range matches1 {
		if len(match) > 1 {
			if val, err := strconv.Atoi(match[1]); err == nil {
				values = append(values, val)
			}
		}
	}

	// 如果第一种模式没有匹配到，尝试第二种模式
	if len(values) == 0 {
		// 匹配模式2：1普通用户，2,Vip用户,3蓝标用户
		re2 := regexp.MustCompile(`(\d+)[^，,\d]*[，,]?`)
		matches2 := re2.FindAllStringSubmatch(comment, -1)
		for _, match := range matches2 {
			if len(match) > 1 {
				if val, err := strconv.Atoi(match[1]); err == nil {
					values = append(values, val)
				}
			}
		}
	}

	// 如果还是没有匹配到，尝试第三种模式
	if len(values) == 0 {
		// 匹配模式3：0:禁用 1:启用
		re3 := regexp.MustCompile(`(\d+)[:：]([^0-9]+)`)
		matches3 := re3.FindAllStringSubmatch(comment, -1)
		for _, match := range matches3 {
			if len(match) > 1 {
				if val, err := strconv.Atoi(match[1]); err == nil {
					values = append(values, val)
				}
			}
		}
	}

	return values
}

// applyMockRule 应用Mock规则
func (s *databaseService) applyMockRule(rule string, column sys.ColumnInfo) interface{} {
	// 处理Mock.js风格的规则
	switch {
	case strings.HasPrefix(rule, "@name"):
		names := []string{"张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"}
		return names[rand.Intn(len(names))]

	case strings.HasPrefix(rule, "@email"):
		return fmt.Sprintf("<EMAIL>", rand.Intn(10000))

	case strings.HasPrefix(rule, "@phone"):
		return fmt.Sprintf("1%010d", rand.Intn(10000000000))

	case strings.HasPrefix(rule, "@url"):
		return fmt.Sprintf("https://example.com/path%d", rand.Intn(1000))

	case strings.HasPrefix(rule, "@ip"):
		return fmt.Sprintf("%d.%d.%d.%d", rand.Intn(256), rand.Intn(256), rand.Intn(256), rand.Intn(256))

	case strings.HasPrefix(rule, "@datetime"):
		return time.Now().Add(-time.Duration(rand.Intn(365*24)) * time.Hour).Format("2006-01-02 15:04:05")

	case strings.HasPrefix(rule, "@date"):
		return time.Now().Add(-time.Duration(rand.Intn(365*24)) * time.Hour).Format("2006-01-02")

	case strings.HasPrefix(rule, "@time"):
		return fmt.Sprintf("%02d:%02d:%02d", rand.Intn(24), rand.Intn(60), rand.Intn(60))

	case strings.HasPrefix(rule, "@integer"):
		// 解析范围，例如 @integer(1,100)
		if matches := regexp.MustCompile(`@integer\((\d+),(\d+)\)`).FindStringSubmatch(rule); len(matches) == 3 {
			min, _ := strconv.Atoi(matches[1])
			max, _ := strconv.Atoi(matches[2])
			return rand.Intn(max-min+1) + min
		}
		return rand.Intn(1000)

	case strings.HasPrefix(rule, "@float"):
		// 解析范围，例如 @float(1,100,2,2)
		return rand.Float64() * 1000

	case strings.HasPrefix(rule, "@boolean"):
		return rand.Intn(2) == 1

	case strings.HasPrefix(rule, "@string"):
		// 解析长度，例如 @string(5,10)
		if matches := regexp.MustCompile(`@string\((\d+),(\d+)\)`).FindStringSubmatch(rule); len(matches) == 3 {
			min, _ := strconv.Atoi(matches[1])
			max, _ := strconv.Atoi(matches[2])
			length := rand.Intn(max-min+1) + min
			return s.generateEnglishString(length)
		}
		return s.generateEnglishString(10)

	case strings.HasPrefix(rule, "@uuid"):
		return uuid.New().String()

	case strings.HasPrefix(rule, "@enum"):
		// 解析枚举值，例如 @enum(0,1,2)
		if matches := regexp.MustCompile(`@enum\(([^)]+)\)`).FindStringSubmatch(rule); len(matches) == 2 {
			values := strings.Split(matches[1], ",")
			if len(values) > 0 {
				value := strings.TrimSpace(values[rand.Intn(len(values))])
				// 尝试转换为数字
				if intVal, err := strconv.Atoi(value); err == nil {
					return intVal
				}
				return value
			}
		}
		return 0

	default:
		// 如果不是Mock.js规则，直接返回规则字符串
		return rule
	}
}

// generateInsertSQL 生成插入SQL语句
func (s *databaseService) generateInsertSQL(tableName string, data []map[string]interface{}) string {
	if len(data) == 0 {
		return ""
	}

	// 获取字段名
	var columns []string
	for column := range data[0] {
		columns = append(columns, fmt.Sprintf("`%s`", column))
	}

	// 构建SQL
	sql := fmt.Sprintf("INSERT INTO `%s` (%s) VALUES\n", tableName, strings.Join(columns, ", "))

	var values []string
	for _, row := range data {
		var rowValues []string
		for _, column := range columns {
			columnName := strings.Trim(column, "`")
			value := row[columnName]

			// 格式化值
			switch v := value.(type) {
			case string:
				rowValues = append(rowValues, fmt.Sprintf("'%s'", strings.ReplaceAll(v, "'", "''")))
			case nil:
				rowValues = append(rowValues, "NULL")
			default:
				rowValues = append(rowValues, fmt.Sprintf("%v", v))
			}
		}
		values = append(values, fmt.Sprintf("(%s)", strings.Join(rowValues, ", ")))
	}

	sql += strings.Join(values, ",\n") + ";"

	return sql
}

// extractFieldLength 提取字段长度限制
func (s *databaseService) extractFieldLength(columnType string) int {
	// 使用正则表达式提取长度，例如 varchar(50) -> 50
	re := regexp.MustCompile(`\((\d+)\)`)
	matches := re.FindStringSubmatch(columnType)
	if len(matches) > 1 {
		if length, err := strconv.Atoi(matches[1]); err == nil {
			return length
		}
	}
	return 255 // 默认返回255
}

// truncateString 截断字符串到指定长度
func (s *databaseService) truncateString(str string, maxLength int) string {
	if maxLength <= 0 {
		return str
	}

	// 处理UTF-8字符串，确保不会截断多字节字符
	runes := []rune(str)
	if len(runes) <= maxLength {
		return str
	}
	return string(runes[:maxLength])
}

// generateEnglishString 生成指定长度的英文字符串
func (s *databaseService) generateEnglishString(maxLength int) string {
	if maxLength <= 0 {
		maxLength = 10
	}

	// 限制最大长度，避免生成过长的字符串
	if maxLength > 50 {
		maxLength = 50
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	result := make([]byte, maxLength)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}
	return string(result)
}
