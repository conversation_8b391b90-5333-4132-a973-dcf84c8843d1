package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoLike 用户视频点赞表
type VideoLike struct {
	*models.BaseModelStruct
	UserID        string `gorm:"column:user_id;index" json:"user_id"`                   // 用户ID
	VideoID       string `gorm:"column:video_id;index" json:"video_id"`                 // 视频ID
	VideoTitle    string `gorm:"column:video_title" json:"video_title"`                 // 视频标题
	VideoCover    string `gorm:"column:video_cover" json:"video_cover"`                 // 视频封面
	VideoDuration uint   `gorm:"column:video_duration;default:0" json:"video_duration"` // 视频时长(秒)
	CreatorName   string `gorm:"column:creator_name" json:"creator_name"`               // 创作者名称
	CreatorAvatar string `gorm:"column:creator_avatar" json:"creator_avatar"`           // 创作者头像
}

// TableName 设置表名
func (VideoLike) TableName() string {
	return "ly_video_likes"
}

// 实现BaseModel接口的方法
func (v *VideoLike) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoLike) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoLike) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoLike) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
