package wallet

// WalletRechargeRequest 钱包充值请求验证模型
type WalletRechargeRequest struct {
	Amount      float64 `json:"amount" validate:"required,gt=0"`
	Channel     string  `json:"channel" validate:"required,oneof=alipay wechat paypal creditcard"`
	ReferenceID string  `json:"referenceId" validate:"omitempty"`
	Description string  `json:"description" validate:"omitempty,max=200"`
}

// WalletWithdrawRequest 钱包提现请求验证模型
type WalletWithdrawRequest struct {
	Amount      float64 `json:"amount" validate:"required,gt=0"`
	BankAccount string  `json:"bankAccount" validate:"required_if=Channel bank"`
	BankName    string  `json:"bankName" validate:"required_if=Channel bank"`
	AccountName string  `json:"accountName" validate:"required"`
	Channel     string  `json:"channel" validate:"required,oneof=alipay wechat bank paypal"`
	Description string  `json:"description" validate:"omitempty,max=200"`
}

// WalletTransferRequest 钱包转账请求验证模型
type WalletTransferRequest struct {
	ToUserID    string  `json:"toUserId" validate:"required"`
	Amount      float64 `json:"amount" validate:"required,gt=0"`
	Description string  `json:"description" validate:"omitempty,max=200"`
}

// WalletTransactionListRequest 钱包交易记录列表请求验证模型
type WalletTransactionListRequest struct {
	Page      int     `json:"page" validate:"min=1"`
	PageSize  int     `json:"pageSize" validate:"min=1,max=100"`
	StartDate string  `json:"startDate" validate:"omitempty,datetime=2006-01-02"`
	EndDate   string  `json:"endDate" validate:"omitempty,datetime=2006-01-02,gtfield=StartDate"`
	Type      string  `json:"type" validate:"omitempty,oneof=all recharge withdraw transfer income expense"`
	MinAmount float64 `json:"minAmount" validate:"omitempty,min=0"`
	MaxAmount float64 `json:"maxAmount" validate:"omitempty,gtfield=MinAmount"`
}