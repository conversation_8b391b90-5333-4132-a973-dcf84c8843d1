package types

import (
	"context"
	"fmt"
	"time"
)

// CacheAdapter 缓存适配器接口
type CacheAdapter interface {
	Get(ctx context.Context, key string) ([]byte, error)
	Set(ctx context.Context, key string, value []byte, expiration time.Duration) error
	Delete(ctx context.Context, key string) error
	Clear(ctx context.Context) error
	Close() error
	Stats() *CacheStats
	Name() string
	Type() string
	KeyWithPrefix(key string) string
	Exists(ctx context.Context, key string) (bool, error)
	MGet(ctx context.Context, keys []string) (map[string][]byte, error)
	MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error
	Increment(ctx context.Context, key string, delta int64) (int64, error)
	Decrement(ctx context.Context, key string, delta int64) (int64, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	TTL(ctx context.Context, key string) (time.Duration, error)
	Ping(ctx context.Context) error
}

// CacheStats 缓存统计信息
type CacheStats struct {
	// 命中次数
	Hits int64
	// 未命中次数
	Misses int64
	// 写入次数
	Sets int64
	// 删除次数
	Deletes int64
	// 清空次数
	Clears int64
	// 命中率
	HitRate float64
	// 启动时间
	StartTime time.Time
	// 运行时间
	Uptime time.Duration
	// 读取字节数
	BytesRead int64
	// 写入字节数
	BytesWritten int64
	// 缓存大小（字节）
	Size int64
	// 条目数量
	ItemCount int64
	// 驱逐次数
	EvictionCount int64
}

// 预定义错误
var (
	ErrNotFound              = fmt.Errorf("缓存键不存在")
	ErrInvalidValue          = fmt.Errorf("无效的缓存值")
	ErrInvalidKey            = fmt.Errorf("无效的缓存键")
	ErrInvalidConfig         = fmt.Errorf("无效的缓存配置")
	ErrAdapterNotFound       = fmt.Errorf("缓存适配器不存在")
	ErrNoDefaultAdapter      = fmt.Errorf("未设置默认缓存适配器")
	ErrAdapterAlreadyExists  = fmt.Errorf("缓存适配器已存在")
	ErrAdapterNotInitialized = fmt.Errorf("缓存适配器未初始化")
)
