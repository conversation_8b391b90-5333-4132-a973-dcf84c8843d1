package users

import (
	"frontapi/pkg/types"

	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreateVipRequest 创建VIP请求
type CreateVipRequest struct {
	UserID      string         `json:"userId" validate:"required"`
	PackageID   string         `json:"packageId" validate:"required"`
	OrderID     string         `json:"orderId"`
	StartTime   types.JSONTime `json:"startTime" validate:"required"`
	EndTime     types.JSONTime `json:"endTime" validate:"required"`
	IsAutoRenew bool           `json:"isAutoRenew"`
}

// UpdateVipRequest 更新VIP请求
type UpdateVipRequest struct {
	EndTime     types.JSONTime `json:"endTime"`
	Status      bool           `json:"status"`
	IsAutoRenew bool           `json:"isAutoRenew"`
}

// UserVipService 用户VIP服务接口
type UserVipService interface {
	base.IExtendedService[users.UserVip]
}

// userVipService 用户VIP服务实现
type userVipService struct {
	*base.ExtendedService[users.UserVip]
	vipRepo  repo.UserVipRepository
	userRepo repo.UserRepository
}

// NewUserVipService 创建用户VIP服务实例
func NewUserVipService(
	vipRepo repo.UserVipRepository,
	userRepo repo.UserRepository,
) UserVipService {
	return &userVipService{
		ExtendedService: base.NewExtendedService[users.UserVip](vipRepo, "user_vip"),
		vipRepo:         vipRepo,
		userRepo:        userRepo,
	}
}
