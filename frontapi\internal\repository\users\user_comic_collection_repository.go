package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserComicCollectionRepository 用户漫画收藏数据访问接口
type UserComicCollectionRepository interface {
	base.ExtendedRepository[users.UserComicCollection]
}

// userComicCollectionRepository 用户漫画收藏数据访问实现
type userComicCollectionRepository struct {
	base.ExtendedRepository[users.UserComicCollection]
}

// NewUserComicCollectionRepository 创建用户漫画收藏仓库实例
func NewUserComicCollectionRepository(db *gorm.DB) UserComicCollectionRepository {
	return &userComicCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserComicCollection](db),
	}
}
