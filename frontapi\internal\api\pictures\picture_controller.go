package pictures

import (
	"frontapi/internal/api"
	"frontapi/internal/service/pictures"
	pictureTypings "frontapi/internal/typings/picture"

	"github.com/gofiber/fiber/v2"
)

// PictureController 图片控制器结构体
type PictureController struct {
	api.BaseController
	pictureService    pictures.PictureService
	categoryService   pictures.PictureCategoryService
	albumService      pictures.PictureAlbumService
	collectionService pictures.PictureCollectionService
}

// NewPictureController 创建图片控制器
func NewPictureController(
	pictureService pictures.PictureService,
	categoryService pictures.PictureCategoryService,
	albumService pictures.PictureAlbumService,
	collectionService pictures.PictureCollectionService,
) *PictureController {
	return &PictureController{
		pictureService:    pictureService,
		categoryService:   categoryService,
		albumService:      albumService,
		collectionService: collectionService,
	}
}
func (c *PictureController) GetPictureList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	albumID := reqInfo.Get("album_id").GetString()

	condition := map[string]interface{}{
		"status":   1,
		"keyword":  keyword,
		"album_id": albumID,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC,heat DESC,view_count DESC"
	}
	pictures, total, err := c.pictureService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := pictureTypings.ConvertPictureListResponse(pictures, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// GetPictureDetail 获取图片详情
func (c *PictureController) GetPictureDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pictureID := reqInfo.Get("pictureId").GetString()
	if pictureID == "" {
		return c.BadRequest(ctx, "picture ID is required", nil) // 图片ID必填
	}

	picture, err := c.pictureService.GetByID(ctx.Context(), pictureID, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	if picture == nil {
		return c.NotFound(ctx, "picture not found") // 图片不存在
	}

	response := pictureTypings.ConvertPictureInfo(picture)
	return c.Success(ctx, response)
}

// GetRecommendedAlbums 获取推荐专辑
func (c *PictureController) GetRecommendedAlbums(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	albumID := reqInfo.Get("albumId").GetString()
	limit := reqInfo.Get("limit").GetInt()
	if limit <= 0 {
		limit = 6 // 默认推荐6个专辑
	}

	condition := map[string]interface{}{
		"status": 1,
	}

	// 如果指定了专辑ID，排除当前专辑
	if albumID != "" {
		condition["id_not"] = albumID
	}

	orderBy := "heat DESC,view_count DESC,created_at DESC"
	albums, _, err := c.albumService.List(ctx.Context(), condition, orderBy, 1, limit, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	response := pictureTypings.ConvertPictureAlbumListResponse(albums, int64(len(albums)), 1, limit)
	return c.Success(ctx, response)
}
