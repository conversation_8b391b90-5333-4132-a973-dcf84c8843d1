package comics

// CreatePageRequest 创建页面请求
type CreatePageRequest struct {
	ChapterID  string `json:"chapter_id" validate:"required"`
	ComicId    string `json:"comic_id" validate:"required"`
	PageNumber int    `json:"page_number" validate:"required"`
	ImageURL   string `json:"image_url" validate:"required"`
	Width      int    `json:"width"`
	Height     int    `json:"height"`
}

// UpdatePageRequest 更新页面请求
type UpdatePageRequest struct {
	PageNumber *int   `json:"page_number"`
	ImageURL   string `json:"image_url"`
	Width      *int   `json:"width"`
	Height     *int   `json:"height"`
}

// BatchCreatePagesRequest 批量创建页面请求
type BatchCreatePagesRequest struct {
	ChapterID string             `json:"chapter_id" validate:"required"`
	ComicId   string             `json:"comic_id" validate:"required"`
	Width     *int               `json:"width"`
	Height    *int               `json:"height"`
	Pages     []CreatePageDetail `json:"pages" validate:"required"`
}

// CreatePageDetail 创建页面详情
type CreatePageDetail struct {
	PageNumber int    `json:"page_number" validate:"required"`
	ImageURL   string `json:"image_url" validate:"required"`
	Width      int    `json:"width"`
	Height     int    `json:"height"`
}

// PageOrderItem 页面顺序项
type PageOrderItem struct {
	ID         string `json:"id"`
	PageNumber int    `json:"page_number"`
}

// 批量更新页面顺序请求结构
type BatchUpdatePageOrderRequest struct {
	ChapterID string          `json:"chapter_id" validate:"required"`
	ComicID   string          `json:"comic_id" validate:"required"`
	Pages     []PageOrderItem `json:"pages" validate:"required|minLen:1"`
}
