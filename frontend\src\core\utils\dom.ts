/**
 * DOM操作工具函数
 */

/**
 * 元素位置信息接口
 */
export interface ElementPosition {
  top: number
  left: number
  right: number
  bottom: number
  width: number
  height: number
  x: number
  y: number
}

/**
 * 滚动选项接口
 */
export interface ScrollOptions {
  behavior?: 'auto' | 'smooth'
  block?: 'start' | 'center' | 'end' | 'nearest'
  inline?: 'start' | 'center' | 'end' | 'nearest'
}

/**
 * 事件监听器选项
 */
export interface EventListenerOptions {
  once?: boolean
  passive?: boolean
  capture?: boolean
}

/**
 * 获取元素
 * @param selector 选择器
 * @param context 上下文元素
 * @returns 元素或null
 */
export function $(selector: string, context: Document | Element = document): Element | null {
  return context.querySelector(selector)
}

/**
 * 获取多个元素
 * @param selector 选择器
 * @param context 上下文元素
 * @returns 元素数组
 */
export function $$(selector: string, context: Document | Element = document): Element[] {
  return Array.from(context.querySelectorAll(selector))
}

/**
 * 根据ID获取元素
 * @param id 元素ID
 * @returns 元素或null
 */
export function getElementById(id: string): HTMLElement | null {
  return document.getElementById(id)
}

/**
 * 根据类名获取元素
 * @param className 类名
 * @param context 上下文元素
 * @returns 元素数组
 */
export function getElementsByClassName(className: string, context: Document | Element = document): Element[] {
  return Array.from(context.getElementsByClassName(className))
}

/**
 * 根据标签名获取元素
 * @param tagName 标签名
 * @param context 上下文元素
 * @returns 元素数组
 */
export function getElementsByTagName(tagName: string, context: Document | Element = document): Element[] {
  return Array.from(context.getElementsByTagName(tagName))
}

/**
 * 创建元素
 * @param tagName 标签名
 * @param attributes 属性对象
 * @param children 子元素或文本
 * @returns 创建的元素
 */
export function createElement(
  tagName: string,
  attributes: Record<string, any> = {},
  children: (Element | string)[] = []
): HTMLElement {
  const element = document.createElement(tagName)
  
  // 设置属性
  Object.entries(attributes).forEach(([key, value]) => {
    if (key === 'className' || key === 'class') {
      element.className = value
    } else if (key === 'style' && typeof value === 'object') {
      Object.assign(element.style, value)
    } else if (key.startsWith('data-')) {
      element.setAttribute(key, value)
    } else if (key in element) {
      ;(element as any)[key] = value
    } else {
      element.setAttribute(key, value)
    }
  })
  
  // 添加子元素
  children.forEach(child => {
    if (typeof child === 'string') {
      element.appendChild(document.createTextNode(child))
    } else {
      element.appendChild(child)
    }
  })
  
  return element
}

/**
 * 检查元素是否存在
 * @param element 元素
 * @returns 是否存在
 */
export function isElement(element: any): element is Element {
  return element instanceof Element
}

/**
 * 检查元素是否在DOM中
 * @param element 元素
 * @returns 是否在DOM中
 */
export function isInDOM(element: Element): boolean {
  return document.contains(element)
}

/**
 * 检查元素是否可见
 * @param element 元素
 * @returns 是否可见
 */
export function isVisible(element: Element): boolean {
  const style = window.getComputedStyle(element)
  return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0'
}

/**
 * 检查元素是否在视口中
 * @param element 元素
 * @param threshold 阈值（0-1）
 * @returns 是否在视口中
 */
export function isInViewport(element: Element, threshold = 0): boolean {
  const rect = element.getBoundingClientRect()
  const windowHeight = window.innerHeight || document.documentElement.clientHeight
  const windowWidth = window.innerWidth || document.documentElement.clientWidth
  
  const vertInView = (rect.top + rect.height * threshold) <= windowHeight && (rect.top + rect.height * (1 - threshold)) >= 0
  const horInView = (rect.left + rect.width * threshold) <= windowWidth && (rect.left + rect.width * (1 - threshold)) >= 0
  
  return vertInView && horInView
}

/**
 * 获取元素位置信息
 * @param element 元素
 * @returns 位置信息
 */
export function getElementPosition(element: Element): ElementPosition {
  const rect = element.getBoundingClientRect()
  return {
    top: rect.top,
    left: rect.left,
    right: rect.right,
    bottom: rect.bottom,
    width: rect.width,
    height: rect.height,
    x: rect.x,
    y: rect.y
  }
}

/**
 * 获取元素相对于文档的位置
 * @param element 元素
 * @returns 位置信息
 */
export function getElementOffset(element: Element): { top: number; left: number } {
  const rect = element.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
  
  return {
    top: rect.top + scrollTop,
    left: rect.left + scrollLeft
  }
}

/**
 * 获取元素的计算样式
 * @param element 元素
 * @param property 样式属性
 * @returns 样式值
 */
export function getComputedStyle(element: Element, property?: string): string | CSSStyleDeclaration {
  const styles = window.getComputedStyle(element)
  return property ? styles.getPropertyValue(property) : styles
}

/**
 * 设置元素样式
 * @param element 元素
 * @param styles 样式对象
 */
export function setStyle(element: HTMLElement, styles: Partial<CSSStyleDeclaration>): void {
  Object.assign(element.style, styles)
}

/**
 * 添加类名
 * @param element 元素
 * @param classNames 类名
 */
export function addClass(element: Element, ...classNames: string[]): void {
  element.classList.add(...classNames)
}

/**
 * 移除类名
 * @param element 元素
 * @param classNames 类名
 */
export function removeClass(element: Element, ...classNames: string[]): void {
  element.classList.remove(...classNames)
}

/**
 * 切换类名
 * @param element 元素
 * @param className 类名
 * @param force 强制添加或移除
 * @returns 是否包含该类名
 */
export function toggleClass(element: Element, className: string, force?: boolean): boolean {
  return element.classList.toggle(className, force)
}

/**
 * 检查是否包含类名
 * @param element 元素
 * @param className 类名
 * @returns 是否包含
 */
export function hasClass(element: Element, className: string): boolean {
  return element.classList.contains(className)
}

/**
 * 获取或设置元素属性
 * @param element 元素
 * @param name 属性名
 * @param value 属性值
 * @returns 属性值或undefined
 */
export function attr(element: Element, name: string, value?: string): string | null | undefined {
  if (value === undefined) {
    return element.getAttribute(name)
  }
  element.setAttribute(name, value)
}

/**
 * 移除元素属性
 * @param element 元素
 * @param name 属性名
 */
export function removeAttr(element: Element, name: string): void {
  element.removeAttribute(name)
}

/**
 * 获取或设置元素数据属性
 * @param element 元素
 * @param key 数据键
 * @param value 数据值
 * @returns 数据值或undefined
 */
export function data(element: HTMLElement, key: string, value?: any): any {
  if (value === undefined) {
    return element.dataset[key]
  }
  element.dataset[key] = value
}

/**
 * 获取或设置元素文本内容
 * @param element 元素
 * @param text 文本内容
 * @returns 文本内容或undefined
 */
export function text(element: Element, text?: string): string | undefined {
  if (text === undefined) {
    return element.textContent || ''
  }
  element.textContent = text
}

/**
 * 获取或设置元素HTML内容
 * @param element 元素
 * @param html HTML内容
 * @returns HTML内容或undefined
 */
export function html(element: Element, html?: string): string | undefined {
  if (html === undefined) {
    return element.innerHTML
  }
  element.innerHTML = html
}

/**
 * 在元素后插入内容
 * @param element 目标元素
 * @param content 要插入的内容
 */
export function after(element: Element, content: Element | string): void {
  if (typeof content === 'string') {
    element.insertAdjacentHTML('afterend', content)
  } else {
    element.parentNode?.insertBefore(content, element.nextSibling)
  }
}

/**
 * 在元素前插入内容
 * @param element 目标元素
 * @param content 要插入的内容
 */
export function before(element: Element, content: Element | string): void {
  if (typeof content === 'string') {
    element.insertAdjacentHTML('beforebegin', content)
  } else {
    element.parentNode?.insertBefore(content, element)
  }
}

/**
 * 在元素内部开头插入内容
 * @param element 目标元素
 * @param content 要插入的内容
 */
export function prepend(element: Element, content: Element | string): void {
  if (typeof content === 'string') {
    element.insertAdjacentHTML('afterbegin', content)
  } else {
    element.insertBefore(content, element.firstChild)
  }
}

/**
 * 在元素内部末尾插入内容
 * @param element 目标元素
 * @param content 要插入的内容
 */
export function append(element: Element, content: Element | string): void {
  if (typeof content === 'string') {
    element.insertAdjacentHTML('beforeend', content)
  } else {
    element.appendChild(content)
  }
}

/**
 * 移除元素
 * @param element 要移除的元素
 */
export function remove(element: Element): void {
  element.parentNode?.removeChild(element)
}

/**
 * 清空元素内容
 * @param element 要清空的元素
 */
export function empty(element: Element): void {
  element.innerHTML = ''
}

/**
 * 克隆元素
 * @param element 要克隆的元素
 * @param deep 是否深度克隆
 * @returns 克隆的元素
 */
export function clone(element: Element, deep = true): Element {
  return element.cloneNode(deep) as Element
}

/**
 * 查找最近的匹配祖先元素
 * @param element 起始元素
 * @param selector 选择器
 * @returns 匹配的祖先元素或null
 */
export function closest(element: Element, selector: string): Element | null {
  return element.closest(selector)
}

/**
 * 查找父元素
 * @param element 子元素
 * @param selector 选择器（可选）
 * @returns 父元素或null
 */
export function parent(element: Element, selector?: string): Element | null {
  const parentElement = element.parentElement
  if (!parentElement) return null
  
  if (selector) {
    return parentElement.matches(selector) ? parentElement : null
  }
  
  return parentElement
}

/**
 * 查找所有祖先元素
 * @param element 起始元素
 * @param selector 选择器（可选）
 * @returns 祖先元素数组
 */
export function parents(element: Element, selector?: string): Element[] {
  const parents: Element[] = []
  let current = element.parentElement
  
  while (current) {
    if (!selector || current.matches(selector)) {
      parents.push(current)
    }
    current = current.parentElement
  }
  
  return parents
}

/**
 * 查找兄弟元素
 * @param element 元素
 * @param selector 选择器（可选）
 * @returns 兄弟元素数组
 */
export function siblings(element: Element, selector?: string): Element[] {
  const siblings: Element[] = []
  const parent = element.parentElement
  
  if (!parent) return siblings
  
  Array.from(parent.children).forEach(child => {
    if (child !== element && (!selector || child.matches(selector))) {
      siblings.push(child)
    }
  })
  
  return siblings
}

/**
 * 查找下一个兄弟元素
 * @param element 元素
 * @param selector 选择器（可选）
 * @returns 下一个兄弟元素或null
 */
export function next(element: Element, selector?: string): Element | null {
  let nextElement = element.nextElementSibling
  
  while (nextElement) {
    if (!selector || nextElement.matches(selector)) {
      return nextElement
    }
    nextElement = nextElement.nextElementSibling
  }
  
  return null
}

/**
 * 查找上一个兄弟元素
 * @param element 元素
 * @param selector 选择器（可选）
 * @returns 上一个兄弟元素或null
 */
export function prev(element: Element, selector?: string): Element | null {
  let prevElement = element.previousElementSibling
  
  while (prevElement) {
    if (!selector || prevElement.matches(selector)) {
      return prevElement
    }
    prevElement = prevElement.previousElementSibling
  }
  
  return null
}

/**
 * 滚动到元素
 * @param element 目标元素
 * @param options 滚动选项
 */
export function scrollToElement(element: Element, options: ScrollOptions = {}): void {
  element.scrollIntoView({
    behavior: options.behavior || 'smooth',
    block: options.block || 'start',
    inline: options.inline || 'nearest'
  })
}

/**
 * 滚动到顶部
 * @param element 容器元素（可选）
 * @param behavior 滚动行为
 */
export function scrollToTop(element?: Element, behavior: 'auto' | 'smooth' = 'smooth'): void {
  if (element) {
    element.scrollTo({ top: 0, behavior })
  } else {
    window.scrollTo({ top: 0, behavior })
  }
}

/**
 * 滚动到底部
 * @param element 容器元素（可选）
 * @param behavior 滚动行为
 */
export function scrollToBottom(element?: Element, behavior: 'auto' | 'smooth' = 'smooth'): void {
  if (element) {
    element.scrollTo({ top: element.scrollHeight, behavior })
  } else {
    window.scrollTo({ top: document.documentElement.scrollHeight, behavior })
  }
}

/**
 * 获取滚动位置
 * @param element 容器元素（可选）
 * @returns 滚动位置
 */
export function getScrollPosition(element?: Element): { x: number; y: number } {
  if (element) {
    return { x: element.scrollLeft, y: element.scrollTop }
  }
  return {
    x: window.pageXOffset || document.documentElement.scrollLeft,
    y: window.pageYOffset || document.documentElement.scrollTop
  }
}

/**
 * 添加事件监听器
 * @param element 元素
 * @param event 事件名
 * @param handler 事件处理器
 * @param options 选项
 */
export function on(
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options?: EventListenerOptions
): void {
  element.addEventListener(event, handler, options)
}

/**
 * 移除事件监听器
 * @param element 元素
 * @param event 事件名
 * @param handler 事件处理器
 * @param options 选项
 */
export function off(
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options?: EventListenerOptions
): void {
  element.removeEventListener(event, handler, options)
}

/**
 * 添加一次性事件监听器
 * @param element 元素
 * @param event 事件名
 * @param handler 事件处理器
 * @param options 选项
 */
export function once(
  element: Element | Window | Document,
  event: string,
  handler: EventListener,
  options?: Omit<EventListenerOptions, 'once'>
): void {
  element.addEventListener(event, handler, { ...options, once: true })
}

/**
 * 触发事件
 * @param element 元素
 * @param event 事件名或事件对象
 * @param detail 事件详情
 */
export function trigger(element: Element, event: string | Event, detail?: any): void {
  let eventObj: Event
  
  if (typeof event === 'string') {
    eventObj = new CustomEvent(event, { detail, bubbles: true, cancelable: true })
  } else {
    eventObj = event
  }
  
  element.dispatchEvent(eventObj)
}

/**
 * 事件委托
 * @param container 容器元素
 * @param selector 目标选择器
 * @param event 事件名
 * @param handler 事件处理器
 * @param options 选项
 */
export function delegate(
  container: Element,
  selector: string,
  event: string,
  handler: (event: Event, target: Element) => void,
  options?: EventListenerOptions
): void {
  container.addEventListener(
    event,
    (e) => {
      const target = (e.target as Element).closest(selector)
      if (target && container.contains(target)) {
        handler(e, target)
      }
    },
    options
  )
}

/**
 * 获取视口尺寸
 * @returns 视口尺寸
 */
export function getViewportSize(): { width: number; height: number } {
  return {
    width: window.innerWidth || document.documentElement.clientWidth,
    height: window.innerHeight || document.documentElement.clientHeight
  }
}

/**
 * 获取文档尺寸
 * @returns 文档尺寸
 */
export function getDocumentSize(): { width: number; height: number } {
  const body = document.body
  const html = document.documentElement
  
  return {
    width: Math.max(body.scrollWidth, body.offsetWidth, html.clientWidth, html.scrollWidth, html.offsetWidth),
    height: Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight)
  }
}

/**
 * 检查是否支持某个CSS属性
 * @param property CSS属性名
 * @returns 是否支持
 */
export function supportsCSSProperty(property: string): boolean {
  return property in document.documentElement.style
}

/**
 * 检查是否支持某个CSS值
 * @param property CSS属性名
 * @param value CSS值
 * @returns 是否支持
 */
export function supportsCSSValue(property: string, value: string): boolean {
  const element = document.createElement('div')
  element.style.setProperty(property, value)
  return element.style.getPropertyValue(property) === value
}

/**
 * 等待DOM加载完成
 * @returns Promise
 */
export function ready(): Promise<void> {
  return new Promise((resolve) => {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => resolve(), { once: true })
    } else {
      resolve()
    }
  })
}

/**
 * 等待图片加载完成
 * @param img 图片元素或图片URL
 * @returns Promise
 */
export function loadImage(img: HTMLImageElement | string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const image = typeof img === 'string' ? new Image() : img
    
    image.onload = () => resolve(image)
    image.onerror = () => reject(new Error('Image load failed'))
    
    if (typeof img === 'string') {
      image.src = img
    } else if (image.complete) {
      resolve(image)
    }
  })
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise
 */
export async function copyToClipboard(text: string): Promise<void> {
  if (navigator.clipboard) {
    await navigator.clipboard.writeText(text)
  } else {
    // 降级方案
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'
    textarea.style.opacity = '0'
    document.body.appendChild(textarea)
    textarea.select()
    document.execCommand('copy')
    document.body.removeChild(textarea)
  }
}

/**
 * 从剪贴板读取文本
 * @returns Promise<string>
 */
export async function readFromClipboard(): Promise<string> {
  if (navigator.clipboard) {
    return await navigator.clipboard.readText()
  }
  throw new Error('Clipboard API not supported')
}

/**
 * 全屏显示元素
 * @param element 要全屏的元素
 * @returns Promise
 */
export async function requestFullscreen(element: Element = document.documentElement): Promise<void> {
  if (element.requestFullscreen) {
    await element.requestFullscreen()
  } else if ((element as any).webkitRequestFullscreen) {
    await (element as any).webkitRequestFullscreen()
  } else if ((element as any).msRequestFullscreen) {
    await (element as any).msRequestFullscreen()
  }
}

/**
 * 退出全屏
 * @returns Promise
 */
export async function exitFullscreen(): Promise<void> {
  if (document.exitFullscreen) {
    await document.exitFullscreen()
  } else if ((document as any).webkitExitFullscreen) {
    await (document as any).webkitExitFullscreen()
  } else if ((document as any).msExitFullscreen) {
    await (document as any).msExitFullscreen()
  }
}

/**
 * 检查是否处于全屏状态
 * @returns 是否全屏
 */
export function isFullscreen(): boolean {
  return !!(document.fullscreenElement || (document as any).webkitFullscreenElement || (document as any).msFullscreenElement)
}