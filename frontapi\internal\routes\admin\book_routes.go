package admin

import (
	adminBooks "frontapi/internal/admin/books"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterBookRoutes 注册电子书相关路由
func RegisterBookRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建电子书API控制器
	controller := adminBooks.NewBookController(
		services.BookService,
		services.BookCategoryService,
		services.BookChapterService,
	)

	// 电子书管理路由组
	bookGroup := apiGroup.Group("/books", middleware.AuthRequired())

	// 电子书管理
	bookGroup.Post("/list", controller.ListBooks)
	bookGroup.Post("/detail/:id?", controller.GetBook)
	bookGroup.Post("/add", controller.CreateBook)
	bookGroup.Post("/update", controller.UpdateBook)
	bookGroup.Post("/update-status", controller.UpdateBookStatus)
	bookGroup.Post("/delete/:id?", controller.DeleteBook)

	// 电子书分类管理路由组
	categoryGroup := apiGroup.Group("/books/categories", middleware.AuthRequired())
	{
		bookCategoryController := adminBooks.NewBookCategoryController(services.BookCategoryService, services.BookService)
		// 电子书分类管理
		categoryGroup.Post("/list", bookCategoryController.ListBookCategories)

		categoryGroup.Post("/detail/:id?", bookCategoryController.GetBookCategory)
		categoryGroup.Post("/add", bookCategoryController.CreateBookCategory)
		categoryGroup.Post("/update", bookCategoryController.UpdateBookCategory)
		categoryGroup.Post("/update-status", bookCategoryController.UpdateBookCategoryStatus)
		categoryGroup.Post("/delete/:id?", bookCategoryController.DeleteBookCategory)
	}

	// 电子书章节管理路由组
	chapterApi := apiGroup.Group("/books/chapters", middleware.AuthRequired())
	{
		chapterController := adminBooks.NewBookChapterController(services.BookChapterService, services.BookService, services.BookCategoryService)
		// 电子书章节管理
		chapterApi.Post("/list", chapterController.ListBookChapters)
		chapterApi.Post("/detail/:id?", chapterController.GetBookChapter)
		chapterApi.Post("/add", chapterController.CreateBookChapter)
		chapterApi.Post("/update", chapterController.UpdateBookChapter)
		chapterApi.Post("/delete/:id?", chapterController.DeleteBookChapter)
		chapterApi.Post("/batch-upload", chapterController.BatchUploadChapters)
		chapterApi.Post("/reorder", chapterController.ReorderBookChapter)
		chapterApi.Post("/batch-update-order", chapterController.BatchUpdateChapterOrder)
	}

}
