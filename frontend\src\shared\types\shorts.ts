// 短视频基础信息
export interface ShortsVideo {
  id: string
  title: string
  description: string
  cover: string
  url: string
  duration: number
  width: number
  height: number
  size: number
  format: string
  quality: 'SD' | 'HD' | 'FHD' | '4K'
  fps: number
  bitrate: number
  tags: string[]
  categoryId: string
  categoryName: string
  creatorId: string
  creatorName: string
  creatorAvatar: string
  creatorVerified: boolean
  creatorLevel: number
  viewsCount: number
  likesCount: number
  sharesCount: number
  commentsCount: number
  favoritesCount: number
  downloadsCount: number
  isLiked?: boolean
  isFavorited?: boolean
  isFollowing?: boolean
  allowComments: boolean
  allowDownload: boolean
  visibility: 'public' | 'followers' | 'private'
  status: 'processing' | 'published' | 'failed' | 'deleted'
  location?: string
  music?: {
    id: string
    name: string
    artist: string
    cover: string
    url: string
    duration: number
  }
  effects?: {
    id: string
    name: string
    type: 'filter' | 'sticker' | 'text' | 'transition'
    params: Record<string, any>
  }[]
  publishedAt?: string
  createdAt: string
  updatedAt: string
}

// 短视频分类
export interface ShortsCategory {
  id: string
  name: string
  description: string
  icon: string
  cover?: string
  color: string
  videosCount: number
  isHot: boolean
  sortOrder: number
  parentId?: string
  children?: ShortsCategory[]
  createdAt: string
  updatedAt: string
}

// 短视频评论
export interface ShortsComment {
  id: string
  content: string
  videoId: string
  userId: string
  userName: string
  userAvatar: string
  userVerified: boolean
  userLevel: number
  parentId?: string
  replyToId?: string
  replyToUser?: {
    id: string
    name: string
    avatar: string
  }
  likesCount: number
  repliesCount: number
  isLiked?: boolean
  replies?: ShortsComment[]
  images?: string[]
  stickers?: {
    id: string
    url: string
    width: number
    height: number
  }[]
  createdAt: string
  updatedAt: string
}

// 短视频弹幕
export interface ShortsDanmaku {
  id: string
  content: string
  videoId: string
  userId: string
  userName: string
  userAvatar: string
  time: number // 弹幕出现时间（秒）
  color: string
  fontSize: number
  position: 'top' | 'middle' | 'bottom'
  speed: number
  opacity: number
  isBlocked?: boolean
  createdAt: string
}

// 短视频播放列表
export interface ShortsPlaylist {
  id: string
  name: string
  description: string
  cover: string
  creatorId: string
  creatorName: string
  creatorAvatar: string
  videosCount: number
  totalDuration: number
  viewsCount: number
  likesCount: number
  sharesCount: number
  isPublic: boolean
  isLiked?: boolean
  videos: ShortsVideo[]
  createdAt: string
  updatedAt: string
}

// 短视频挑战/话题
export interface ShortsChallenge {
  id: string
  name: string
  description: string
  hashtag: string
  banner: string
  icon: string
  startDate: string
  endDate?: string
  participantsCount: number
  videosCount: number
  viewsCount: number
  isParticipated?: boolean
  isOfficial: boolean
  sponsor?: {
    id: string
    name: string
    logo: string
  }
  prizes?: {
    rank: number
    title: string
    description: string
    value: string
  }[]
  rules: string[]
  status: 'upcoming' | 'active' | 'ended'
  createdAt: string
}

// 短视频音乐
export interface ShortsMusic {
  id: string
  name: string
  artist: string
  album?: string
  cover: string
  url: string
  duration: number
  genre: string
  bpm?: number
  key?: string
  mood?: string[]
  usageCount: number
  isOriginal: boolean
  isTrending: boolean
  copyright: {
    owner: string
    license: string
    allowCommercial: boolean
    attribution: boolean
  }
  createdAt: string
}

// 短视频特效
export interface ShortsEffect {
  id: string
  name: string
  description: string
  type: 'filter' | 'sticker' | 'text' | 'transition' | 'beauty' | 'ar'
  category: string
  thumbnail: string
  preview?: string
  usageCount: number
  isNew: boolean
  isTrending: boolean
  isPremium: boolean
  params: {
    name: string
    type: 'number' | 'string' | 'boolean' | 'color' | 'range'
    defaultValue: any
    min?: number
    max?: number
    options?: string[]
  }[]
  createdAt: string
}

// 短视频上传参数
export interface ShortsUploadParams {
  title: string
  description?: string
  cover?: string
  categoryId: string
  tags: string[]
  musicId?: string
  effects?: {
    id: string
    params: Record<string, any>
  }[]
  location?: string
  allowComments?: boolean
  allowDownload?: boolean
  visibility?: 'public' | 'followers' | 'private'
  challengeId?: string
}

// 短视频统计信息
export interface ShortsStats {
  videoId: string
  viewsCount: number
  likesCount: number
  sharesCount: number
  commentsCount: number
  favoritesCount: number
  downloadsCount: number
  completionRate: number // 完播率
  averageWatchTime: number // 平均观看时长
  replayRate: number // 重播率
  engagementRate: number // 互动率
  shareRate: number // 分享率
  demographics: {
    ageGroups: Record<string, number>
    genders: Record<string, number>
    locations: Record<string, number>
  }
  hourlyViews: Record<string, number>
  dailyViews: Record<string, number>
  sources: Record<string, number> // 流量来源
}

// 短视频搜索参数
export interface ShortsSearchParams {
  keyword?: string
  categoryId?: string
  tags?: string[]
  creatorId?: string
  musicId?: string
  challengeId?: string
  duration?: {
    min?: number
    max?: number
  }
  quality?: ('SD' | 'HD' | 'FHD' | '4K')[]
  sortBy?: 'latest' | 'popular' | 'trending' | 'views' | 'likes'
  timeRange?: 'day' | 'week' | 'month' | 'year' | 'all'
  page?: number
  pageSize?: number
}

// 短视频推荐参数
export interface ShortsRecommendParams {
  userId?: string
  categoryIds?: string[]
  excludeVideoIds?: string[]
  maxDuration?: number
  minQuality?: 'SD' | 'HD' | 'FHD' | '4K'
  includeFollowing?: boolean
  page?: number
  pageSize?: number
}

// 类型守卫
export const isShortsVideo = (obj: any): obj is ShortsVideo => {
  return obj && typeof obj.id === 'string' && typeof obj.url === 'string' && typeof obj.duration === 'number'
}

export const isShortsComment = (obj: any): obj is ShortsComment => {
  return obj && typeof obj.id === 'string' && typeof obj.content === 'string' && typeof obj.videoId === 'string'
}

export const isShortsCategory = (obj: any): obj is ShortsCategory => {
  return obj && typeof obj.id === 'string' && typeof obj.name === 'string'
}

export const isShortsDanmaku = (obj: any): obj is ShortsDanmaku => {
  return obj && typeof obj.id === 'string' && typeof obj.content === 'string' && typeof obj.time === 'number'
}