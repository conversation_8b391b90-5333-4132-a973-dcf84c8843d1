package comics

import (
	comicModel "frontapi/internal/models/comics"
	repo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
)

// ComicFavoriteService 漫画收藏服务接口
type ComicFavoriteService interface {
	base.IExtendedService[comicModel.ComicFavorite]
}

// comicFavoriteService 漫画收藏服务实现
type comicFavoriteService struct {
	*base.ExtendedService[comicModel.ComicFavorite]
	favoriteRepo repo.ComicFavoriteRepository
	comicRepo    repo.ComicRepository
}

// NewComicFavoriteService 创建漫画收藏服务实例
func NewComicFavoriteService(
	favoriteRepo repo.ComicFavoriteRepository,
	comicRepo repo.ComicRepository,
) ComicFavoriteService {
	return &comicFavoriteService{
		ExtendedService: base.NewExtendedService[comicModel.ComicFavorite](favoriteRepo, "comic_favorite"),
		favoriteRepo:    favoriteRepo,
		comicRepo:       comicRepo,
	}
}
