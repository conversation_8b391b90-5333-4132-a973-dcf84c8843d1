<template>
  <div class="share-modal-overlay" @click="handleOverlayClick">
    <div class="share-modal" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <h3 class="modal-title">分享视频</h3>
        <button class="close-btn" @click="$emit('close')" title="关闭">
          <span class="close-icon">×</span>
        </button>
      </div>

      <!-- 视频预览 -->
      <div class="video-preview" v-if="video">
        <div class="video-thumbnail">
          <img v-if="video.cover" :src="video.cover" :alt="video.title" />
          <div v-else class="thumbnail-placeholder">
            <span class="play-icon">▶</span>
          </div>
        </div>
        <div class="video-info">
          <h4 class="video-title">{{ video.title }}</h4>
          <p class="video-creator">@{{ video.creator_name }}</p>
          <div class="video-stats">
            <span class="stat-item">
              <span class="stat-icon">👁️</span>
              {{ formatCount(video.view_count || 0) }}
            </span>
            <span class="stat-item">
              <span class="stat-icon">❤️</span>
              {{ formatCount(video.like_count || 0) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 分享选项 -->
      <div class="share-options">
        <div class="share-grid">
          <!-- 复制链接 -->
          <div class="share-item" @click="copyLink" title="复制链接">
            <div class="share-icon-wrapper copy">
              <span class="share-icon">🔗</span>
            </div>
            <span class="share-label">复制链接</span>
          </div>

          <!-- 微信 -->
          <div class="share-item" @click="shareToWeChat" title="分享到微信">
            <div class="share-icon-wrapper wechat">
              <span class="share-icon">💬</span>
            </div>
            <span class="share-label">微信</span>
          </div>

          <!-- 朋友圈 -->
          <div class="share-item" @click="shareToMoments" title="分享到朋友圈">
            <div class="share-icon-wrapper moments">
              <span class="share-icon">🌟</span>
            </div>
            <span class="share-label">朋友圈</span>
          </div>

          <!-- QQ -->
          <div class="share-item" @click="shareToQQ" title="分享到QQ">
            <div class="share-icon-wrapper qq">
              <span class="share-icon">🐧</span>
            </div>
            <span class="share-label">QQ</span>
          </div>

          <!-- 微博 -->
          <div class="share-item" @click="shareToWeibo" title="分享到微博">
            <div class="share-icon-wrapper weibo">
              <span class="share-icon">📢</span>
            </div>
            <span class="share-label">微博</span>
          </div>

          <!-- 下载 -->
          <div class="share-item" @click="downloadVideo" title="下载视频">
            <div class="share-icon-wrapper download">
              <span class="share-icon">📥</span>
            </div>
            <span class="share-label">下载</span>
          </div>

          <!-- 二维码 -->
          <div class="share-item" @click="showQRCode" title="生成二维码">
            <div class="share-icon-wrapper qrcode">
              <span class="share-icon">📱</span>
            </div>
            <span class="share-label">二维码</span>
          </div>

          <!-- 更多 -->
          <div class="share-item" @click="showMoreOptions" title="更多选项">
            <div class="share-icon-wrapper more">
              <span class="share-icon">⋯</span>
            </div>
            <span class="share-label">更多</span>
          </div>
        </div>
      </div>

      <!-- 二维码显示 -->
      <div v-if="showQR" class="qr-section">
        <div class="qr-header">
          <h4>扫码分享</h4>
          <button class="qr-close" @click="showQR = false">×</button>
        </div>
        <div class="qr-code">
          <div class="qr-placeholder">
            <span class="qr-icon">📱</span>
            <p>二维码</p>
          </div>
        </div>
        <p class="qr-tip">使用手机扫描二维码观看视频</p>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <button class="action-btn secondary" @click="$emit('close')">
          取消
        </button>
        <button class="action-btn primary" @click="copyLink">
          <span class="btn-icon">🔗</span>
          复制链接
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Types
interface VideoData {
  id: string
  title: string
  description?: string
  cover?: string
  like_count?: number
  comment_count?: number
  view_count?: number
  creator_name?: string
  creator_avatar?: string
}

interface Props {
  video?: VideoData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  shared: [platform: string]
}>()

// State
const showQR = ref(false)

// Methods
const handleOverlayClick = () => {
  emit('close')
}

const formatCount = (count: number) => {
  if (count < 1000) return count.toString()
  if (count < 1000000) return (count / 1000).toFixed(1) + 'K'
  return (count / 1000000).toFixed(1) + 'M'
}

const copyLink = async () => {
  try {
    const url = window.location.href
    await navigator.clipboard.writeText(url)
    showToast('链接已复制到剪贴板')
    emit('shared', 'copy')
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败，请重试')
  }
}

const shareToWeChat = () => {
  // 实际项目中应该调用微信SDK
  showToast('正在打开微信分享...')
  emit('shared', 'wechat')
}

const shareToMoments = () => {
  // 实际项目中应该调用微信SDK
  showToast('正在分享到朋友圈...')
  emit('shared', 'moments')
}

const shareToQQ = () => {
  // 实际项目中应该调用QQ SDK
  showToast('正在打开QQ分享...')
  emit('shared', 'qq')
}

const shareToWeibo = () => {
  // 实际项目中应该调用微博SDK
  showToast('正在分享到微博...')
  emit('shared', 'weibo')
}

const downloadVideo = () => {
  // 实际项目中应该提供下载功能
  showToast('开始下载视频...')
  emit('shared', 'download')
}

const showQRCode = () => {
  showQR.value = true
}

const showMoreOptions = () => {
  showToast('更多分享选项开发中...')
}

const showToast = (message: string) => {
  // 简单的提示实现，实际项目中可以使用更完善的提示组件
  console.log(message)
}
</script>

<style scoped>
.share-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.share-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e0e0e0;
  transform: scale(1.05);
}

.close-icon {
  font-size: 20px;
  color: #666;
  line-height: 1;
}

.video-preview {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
  position: relative;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
}

.play-icon {
  font-size: 20px;
}

.video-info {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-creator {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
}

.video-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #999;
}

.stat-icon {
  font-size: 12px;
}

.share-options {
  padding: 20px 24px;
}

.share-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.share-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 12px 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.share-item:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
}

.share-item:active {
  transform: translateY(0);
}

.share-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  position: relative;
  overflow: hidden;
}

.share-icon-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  transition: opacity 0.2s ease;
}

.share-item:hover .share-icon-wrapper::before {
  opacity: 0.2;
}

.share-icon-wrapper.copy {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.share-icon-wrapper.wechat {
  background: linear-gradient(135deg, #07C160, #06AD56);
}

.share-icon-wrapper.moments {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.share-icon-wrapper.qq {
  background: linear-gradient(135deg, #12B7F5, #0E9FE0);
}

.share-icon-wrapper.weibo {
  background: linear-gradient(135deg, #E6162D, #C91326);
}

.share-icon-wrapper.download {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.share-icon-wrapper.qrcode {
  background: linear-gradient(135deg, #607D8B, #455A64);
}

.share-icon-wrapper.more {
  background: linear-gradient(135deg, #795548, #5D4037);
}

.share-label {
  font-size: 12px;
  color: #666;
  text-align: center;
  font-weight: 500;
}

.qr-section {
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.qr-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.qr-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.qr-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 4px;
}

.qr-code {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.qr-placeholder {
  width: 120px;
  height: 120px;
  background: white;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.qr-icon {
  font-size: 32px;
  color: #999;
}

.qr-placeholder p {
  margin: 0;
  font-size: 14px;
  color: #999;
}

.qr-tip {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin: 0;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  height: 44px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn.secondary:hover {
  background: #e0e0e0;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #ff5252, #ff7979);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.btn-icon {
  font-size: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .share-modal-overlay {
    padding: 0;
    align-items: flex-end;
  }
  
  .share-modal {
    border-radius: 16px 16px 0 0;
    max-height: 80vh;
  }
  
  .share-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
  
  .share-icon-wrapper {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
  
  .share-label {
    font-size: 11px;
  }
  
  .modal-actions {
    padding: 16px 20px 20px;
  }
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .share-modal {
    border: 2px solid #333;
  }
  
  .share-item {
    border: 1px solid #ddd;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .share-modal-overlay,
  .share-modal,
  .share-item,
  .action-btn {
    animation: none !important;
    transition: none !important;
  }
}
</style> 