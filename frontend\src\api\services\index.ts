// API 服务模块
import { get, post, put, del } from '@/shared/composables'
import { coreGet, corePost, corePut, coreDel } from '@/shared/composables'
import type { Video, VideoListParams, VideoUploadData } from '@/shared/types'

// 视频服务
export const videoService = {
  // 获取视频列表
  getVideoList(params: VideoListParams) {
    return coreGet('/videos', params)
  },

  // 获取视频详情
  getVideoDetail(id: string) {
    return coreGet(`/videos/${id}`, {})
  },

  // 上传视频
  uploadVideo(data: VideoUploadData) {
    return corePost('/videos', data)
  },

  // 更新视频信息
  updateVideo(id: string, data: Partial<Video>) {
    return corePut(`/videos/${id}`, data)
  },

  // 删除视频
  deleteVideo(id: string) {
    return coreDel(`/videos/${id}`, {})
  },

  // 点赞视频
  likeVideo(id: string) {
    return corePost(`/videos/${id}/like`, {})
  },

  // 取消点赞
  unlikeVideo(id: string) {
    return coreDel(`/videos/${id}/like`, {})
  },

  // 收藏视频
  favoriteVideo(id: string) {
    return corePost(`/videos/${id}/favorite`, {})
  },

  // 取消收藏
  unfavoriteVideo(id: string) {
    return coreDel(`/videos/${id}/favorite`, {})
  },

  // 获取视频评论
  getVideoComments(id: string, params?: any) {
    return coreGet(`/videos/${id}/comments`, params || {})
  },

  // 添加视频评论
  addVideoComment(id: string, content: string) {
    return corePost(`/videos/${id}/comments`, { content })
  }
}

// 用户服务
export const userService = {
  // 获取用户信息
  getUserInfo(id?: string) {
    return coreGet(id ? `/users/${id}` : '/user/profile', {})
  },

  // 更新用户信息
  updateUserInfo(data: any) {
    return corePut('/user/profile', data)
  },

  // 关注用户
  followUser(id: string) {
    return corePost(`/users/${id}/follow`, {})
  },

  // 取消关注
  unfollowUser(id: string) {
    return coreDel(`/users/${id}/follow`, {})
  }
}

// 认证服务
export const authService = {
  // 登录
  login(credentials: any) {
    return corePost('/auth/login', credentials)
  },

  // 注册
  register(data: any) {
    return corePost('/auth/register', data)
  },

  // 退出登录
  logout() {
    return corePost('/auth/logout', {})
  },

  // 刷新令牌
  refreshToken() {
    return corePost('/auth/refresh', {})
  }
}

export default {
  videoService,
  userService,
  authService
}