/**
 * 主题系统的Vue Composable
 * 
 * 提供在Vue组件中使用主题的便捷方法
 */

import { computed } from 'vue'
import { themeManager } from '../themes'

/**
 * 主题系统的Vue Composable
 */
export function useTheme() {
    // 从主题管理器获取当前状态
    const { currentTheme, currentThemeName, themes, setTheme, toggleDarkMode, initTheme } = themeManager

    // 是否为深色模式
    const isDark = computed(() => currentTheme.value?.isDark || false)

    // 获取CSS变量值
    const getThemeVar = (name: string): string => {
        return getComputedStyle(document.documentElement).getPropertyValue(`--color-${name}`).trim()
    }

    // 设置CSS变量值
    const setThemeVar = (name: string, value: string): void => {
        document.documentElement.style.setProperty(`--color-${name}`, value)
    }

    // 获取所有主题颜色
    const themeColors = computed(() => {
        const colors: Record<string, string> = {}

        if (currentTheme.value) {
            Object.entries(currentTheme.value.colors).forEach(([key, value]) => {
                colors[key] = value
            })
        }

        return colors
    })

    // 获取主题列表
    const themeList = computed(() => {
        return Object.entries(themes).map(([key, theme]) => ({
            name: key,
            displayName: theme.displayName,
            isDark: theme.isDark || false
        }))
    })

    // 应用主题色到元素
    const applyThemeColorTo = (element: HTMLElement, colorName: string = 'primary'): void => {
        if (currentTheme.value?.colors[colorName]) {
            element.style.color = currentTheme.value.colors[colorName]
        }
    }

    // 应用主题背景色到元素
    const applyThemeBackgroundTo = (element: HTMLElement, colorName: string = 'primary'): void => {
        if (currentTheme.value?.colors[colorName]) {
            element.style.backgroundColor = currentTheme.value.colors[colorName]
        }
    }

    return {
        // 状态
        currentTheme,
        currentThemeName,
        isDark,
        themes,
        themeColors,
        themeList,

        // 方法
        setTheme,
        toggleDarkMode,
        initTheme,
        getThemeVar,
        setThemeVar,
        applyThemeColorTo,
        applyThemeBackgroundTo
    }
}

export default useTheme 