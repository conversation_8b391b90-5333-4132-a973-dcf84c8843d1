package comics

import (
	"frontapi/internal/admin"
	"frontapi/internal/models"
	comicModel "frontapi/internal/models/comics"
	comicSrv "frontapi/internal/service/comics"
	comicsValidator "frontapi/internal/validation/comics"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ComicController 漫画控制器
type ComicController struct {
	admin.BaseController
	comicService comicSrv.ComicService
}

// NewComicController 创建漫画控制器实例
func NewComicController(service comicSrv.ComicService) *ComicController {
	return &ComicController{
		comicService: service,
	}
}

// List 获取漫画列表
func (c *ComicController) List(ctx *fiber.Ctx) error {
	// 获取分页参数
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	author := reqInfo.Get("author").GetString()
	keyword := reqInfo.Get("keyword").GetString()
	categoryId := reqInfo.Get("keyword").GetString()
	categoryName := reqInfo.Get("category_name").GetString()
	status := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	condition := map[string]interface{}{
		"author":        author,
		"keyword":       keyword,
		"category_id":   categoryId,
		"category_name": categoryName,
		"status":        status,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "updated_at desc"
	}
	// 获取漫画列表
	comics, total, err := c.comicService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取漫画列表失败: "+err.Error())
	}

	return c.SuccessList(ctx, comics, total, pageNo, pageSize)
}

// Create 创建漫画
func (c *ComicController) Create(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req comicsValidator.CreateComicRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "参数验证失败"+err.Error(), nil)
	}
	myComic := &comicModel.Comic{
		ContentBaseModel: models.ContentBaseModel{},
	}
	if err := utils.SmartCopy(req, myComic); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 创建漫画
	comicID, err := c.comicService.Create(ctx.Context(), myComic)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	// 获取创建的漫画信息
	comic, err := c.comicService.GetByID(ctx.Context(), comicID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取漫画信息失败: "+err.Error())
	}

	return c.Success(ctx, comic)
}

// Update 更新漫画
func (c *ComicController) Update(ctx *fiber.Ctx) error {
	// 获取漫画ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "漫画ID不能为空", nil)
	}

	// 解析请求参数
	var req comicsValidator.UpdateComicRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "参数验证失败"+err.Error(), nil)
	}
	myComic := &comicModel.Comic{
		ContentBaseModel: models.ContentBaseModel{},
	}
	if err := utils.SmartCopy(req, myComic); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 更新漫画
	if err := c.comicService.UpdateById(ctx.Context(), id, myComic); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	// 获取更新后的漫画信息
	comic, err := c.comicService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取漫画信息失败: "+err.Error())
	}
	return c.Success(ctx, comic)
}

// Delete 删除漫画
func (c *ComicController) Delete(ctx *fiber.Ctx) error {
	// 获取漫画ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "漫画ID不能为空", nil)
	}

	// 删除漫画
	if err := c.comicService.Delete(ctx.Context(), id); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除漫画成功")
}

// GetByID 获取漫画详情
func (c *ComicController) GetByID(ctx *fiber.Ctx) error {
	// 获取漫画ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "漫画ID不能为空", nil)
	}

	// 获取漫画详情
	comic, err := c.comicService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取漫画详情失败: "+err.Error())
	}

	return c.Success(ctx, comic)
}
