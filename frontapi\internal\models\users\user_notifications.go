package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type UserNotification struct {
	models.BaseModel
	UserID      string         `gorm:"column:user_id;type:string;not null;index;comment:用户ID" json:"user_id"`                                       // 用户ID
	Title       string         `gorm:"column:title;type:string;comment:通知标题" json:"title"`                                                          // 通知标题
	Content     string         `gorm:"column:content;type:text;comment:通知内容" json:"content"`                                                        // 通知内容
	Type        string         `gorm:"column:type;type:string;comment:通知类型：system-系统，order-订单，payment-支付，comment-评论，like-点赞，follow-关注" json:"type"` // 通知类型：system-系统，order-订单，payment-支付，comment-评论，like-点赞，follow-关注
	RelatedType string         `gorm:"column:related_type;type:string;comment:关联类型" json:"related_type"`                                            // 关联类型
	RelatedID   string         `gorm:"column:related_id;type:string;comment:关联ID" json:"related_id"`                                                // 关联ID
	SenderID    string         `gorm:"column:sender_id;type:string;comment:发送者ID" json:"sender_id"`                                                 // 发送者ID
	IsRead      bool           `gorm:"column:is_read;type:tinyint;default:0;comment:是否已读：0-未读，1-已读" json:"is_read"`                                 // 是否已读：0-未读，1-已读
	ReadTime    types.JSONTime `gorm:"column:read_time;type:datetime;comment:阅读时间" json:"read_time"`                                                // 阅读时间
}

func (UserNotification) TableName() string {
	return "ly_user_notifications"
}
