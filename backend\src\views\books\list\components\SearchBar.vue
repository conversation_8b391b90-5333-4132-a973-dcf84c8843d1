<template>
  <div class="search-bar">
    <el-form :model="formData" label-width="100px" :inline="true">
      <el-form-item :label="$t('books.info.title')">
        <el-input
          v-model="formData.title"
          :placeholder="$t('books.info.enterTitle')"
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>

      <el-form-item :label="$t('books.info.author')" >
        <el-input
          v-model="formData.author"
          :placeholder="$t('books.info.enterAuthor')"
          clearable style="width: 180px;"
          @keyup.enter="handleSearch"
        />
      </el-form-item>

      <el-form-item :label="$t('books.info.category')">
        <el-select
          v-model="formData.categoryId"
          :placeholder="$t('books.info.selectCategory')"
          clearable style="width: 180px;"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('books.info.progress')">
        <el-select
          v-model="formData.progress"
          :placeholder="$t('books.info.progress')"
          clearable style="width: 180px;"
        >
          <el-option label="连载中" value="serializing" />
          <el-option label="已完结" value="completed" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('books.info.featured')">
        <el-select
          v-model="formData.isFeatured"
          :placeholder="$t('books.info.selectFeatured')"
          clearable style="width: 180px;"
        >
          <el-option :label="$t('books.info.yes')" :value="1" />
          <el-option :label="$t('books.info.no')" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <div class="button-group">
          <el-button type="primary" @click="handleSearch">{{ $t('books.info.search') }}</el-button>
          <el-button @click="handleReset">{{ $t('books.info.reset') }}</el-button>
          <el-button type="success" @click="handleRefresh">{{ $t('books.info.refresh') }}</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';

import type { BookCategory } from '@/types/books';
defineProps<{
  categoryOptions: BookCategory[]
}>();
// 定义事件
const emit = defineEmits(['search', 'reset', 'refresh']);

// 表单数据
const formData = reactive({
  title: '',
  author: '',
  categoryId: '',
  progress: '',
  status: '',
  isFeatured: undefined
});

// 分类选项
// const categoryOptions = ref<BookCategory[]>([]);


// 处理搜索
const handleSearch = () => {
  emit('search', { ...formData });
};

// 处理重置
const handleReset = () => {
  // 重置表单
  formData.title = '';
  formData.author = '';
  formData.categoryId = '';
  formData.progress = '';
  formData.status = '';
  formData.isFeatured = undefined;

  // 触发重置事件
  emit('reset');
};

// 处理刷新
const handleRefresh = () => {
  emit('refresh');
};

// 组件挂载后获取分类列表
onMounted(() => {
});
</script>

<style scoped>
.search-bar {
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  gap: 8px;
}
</style>
