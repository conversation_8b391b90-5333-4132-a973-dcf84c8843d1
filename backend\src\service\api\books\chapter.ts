import { request} from '../../request';
import { ApiRequest,ApiResponse } from '../../../types/https';
import type { BookChapter, CreateBookChapterRequest, UpdateBookChapterRequest, BookParams } from '@/types/books';

/**
 * 获取电子书章节列表
 * @param params 查询参数
 */
export function getBookChapterList(params: BookParams) {
  return request<ApiResponse<BookChapter[]>>({
    url: '/books/chapters/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取电子书章节详情
 * @param id 章节ID
 */
export function getBookChapterDetail(id: string) {
  return request<ApiResponse<BookChapter>>({
    url: `/books/chapters/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 创建电子书章节
 * @param data 章节数据
 */
export function createBookChapter(params: { data: CreateBookChapterRequest }) {
  return request<ApiResponse<any>>({
    url: '/books/chapters/add',
    method: 'post',
    data: params
  });
}

/**
 * 更新电子书章节
 * @param data 章节数据
 */
export function updateBookChapter(params: { data: UpdateBookChapterRequest }) {
  return request<ApiResponse<any>>({
    url: '/books/chapters/update',
    method: 'post',
    data: params
  });
}

/**
 * 删除电子书章节
 * @param id 章节ID
 */
export function deleteBookChapter(id: string) {
  return request<ApiResponse<any>>({
    url: `/books/chapters/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  })
}

/**
 * 更新电子书章节排序
 * @param params 包含章节ID和排序号的数组
 */
export function updateChapterOrder(params: { data: { book_id: string, chapters: { id: string, sort_order: number, chapter_number: number }[] } }) {
  return request<ApiResponse<any>>({
    url: '/books/chapters/batch-update-order',
    method: 'post',
    data: params
  });
}