package shortvideos

import (
	"context"
	"frontapi/internal/models/shortvideos"
	repo "frontapi/internal/repository/shortvideos"
	"frontapi/internal/service/base"
)

type UpdateCategoryRequestStatus struct {
	ID     string `json:"id" validate:"required"`
	Status *int8  `json:"status" validate:"required|int|min:-2|max:3"`
}

// ShortVideoCategoryService 短视频分类服务接口
type ShortVideoCategoryService interface {
	base.IExtendedService[shortvideos.ShortVideoCategory]
	UpdateStatus(ctx context.Context, id string, status int) error
	BatchUpdateStatus(ctx context.Context, ids []string, status int) error
}

// shortVideoCategoryService 短视频分类服务实现
type shortVideoCategoryService struct {
	*base.ExtendedService[shortvideos.ShortVideoCategory]
	categoryRepo repo.ShortVideoCategoryRepository
}

// NewShortVideoCategoryService 创建短视频分类服务实例
func NewShortVideoCategoryService(categoryRepo repo.ShortVideoCategoryRepository) ShortVideoCategoryService {
	return &shortVideoCategoryService{
		ExtendedService: base.NewExtendedService[shortvideos.ShortVideoCategory](categoryRepo, "short_video_category"),
		categoryRepo:    categoryRepo,
	}
}

// UpdateStatus 更新分类状态
func (s *shortVideoCategoryService) UpdateStatus(ctx context.Context, id string, status int) error {
	category, err := s.GetByID(ctx, id, false)
	if err != nil {
		return err
	}

	category.Status = int8(status)
	return s.UpdateById(ctx, id, category)
}

// BatchUpdateStatus 批量更新分类状态
func (s *shortVideoCategoryService) BatchUpdateStatus(ctx context.Context, ids []string, status int) error {
	for _, id := range ids {
		if err := s.UpdateStatus(ctx, id, status); err != nil {
			return err
		}
	}
	return nil
}
