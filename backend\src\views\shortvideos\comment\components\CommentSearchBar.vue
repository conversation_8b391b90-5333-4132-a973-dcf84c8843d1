<template>
    <div class="comment-search-bar">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
            <el-form-item label="评论内容">
                <el-input v-model="searchForm.keyword" placeholder="请输入评论内容" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="视频ID">
                <el-input v-model="searchForm.short_id" placeholder="请输入视频ID" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="用户ID">
                <el-input v-model="searchForm.user_id" placeholder="请输入用户ID" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="所有状态" clearable style="width: 120px;">
                    <el-option label="正常" :value="0" />
                    <el-option label="隐藏" :value="1" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon>
                        <Search />
                    </el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    重置
                </el-button>
                <el-button @click="handleRefresh">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>
                    刷新
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive, watch } from 'vue';

// 搜索表单类型定义
export interface CommentSearchForm {
    keyword: string;
    short_id: string;
    user_id: string;
    status?: number;
}

// Props
interface Props {
    modelValue?: CommentSearchForm;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
        keyword: '',
        short_id: '',
        user_id: '',
        status: undefined,
    })
});

// Emits
interface Emits {
    search: [params: CommentSearchForm];
    reset: [];
    refresh: [];
    'update:modelValue': [value: CommentSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<CommentSearchForm>({ ...props.modelValue });

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
    emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
    emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
    Object.assign(searchForm, {
        keyword: '',
        short_id: '',
        user_id: '',
        status: undefined,
    });
    emit('reset');
};

// 刷新
const handleRefresh = () => {
    emit('refresh');
};
</script>

<style scoped lang="scss">
.comment-search-bar {
    margin-bottom: 16px;

    .el-form {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
    }

    .el-form-item {
        margin-bottom: 8px;
    }
}
</style>