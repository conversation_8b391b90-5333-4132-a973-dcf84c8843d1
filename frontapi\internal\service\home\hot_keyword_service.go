package home

import (
	"context"
	"encoding/json"
	"frontapi/internal/models/home"
	homeRepo "frontapi/internal/repository/home"
	"frontapi/pkg/redis"
	"time"
)

// HotKeywordService 热搜关键词服务接口
type HotKeywordService interface {
	GetHotKeywords(ctx context.Context) ([]*home.HotKeyword, error)
	GetHotKeywordsByCategory(ctx context.Context, category string) ([]*home.HotKeyword, error)
}

// hotKeywordService 热搜关键词服务实现
type hotKeywordService struct {
	hotKeywordRepo homeRepo.HotKeywordRepository
}

// NewHotKeywordService 创建热搜关键词服务实例
func NewHotKeywordService(hotKeywordRepo homeRepo.HotKeywordRepository) HotKeywordService {
	return &hotKeywordService{
		hotKeywordRepo: hotKeywordRepo,
	}
}

// GetHotKeywords 获取热搜关键词列表
func (s *hotKeywordService) GetHotKeywords(ctx context.Context) ([]*home.HotKeyword, error) {
	// 尝试从缓存获取
	cacheKey := "home:hot_keywords"
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var keywords []*home.HotKeyword
		if err := json.Unmarshal([]byte(cachedData), &keywords); err == nil {
			return keywords, nil
		}
	}

	// 从数据库获取，默认限制10个
	keywords, err := s.hotKeywordRepo.GetHotKeywords(ctx, 10)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if keywordsData, err := json.Marshal(keywords); err == nil {
		redis.Set(cacheKey, string(keywordsData), 1*time.Hour)
	}

	return keywords, nil
}

// GetHotKeywordsByCategory 获取指定分类的热搜关键词列表
func (s *hotKeywordService) GetHotKeywordsByCategory(ctx context.Context, category string) ([]*home.HotKeyword, error) {
	// 尝试从缓存获取
	cacheKey := "home:hot_keywords:" + category
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var keywords []*home.HotKeyword
		if err := json.Unmarshal([]byte(cachedData), &keywords); err == nil {
			return keywords, nil
		}
	}

	// 从数据库获取，默认限制10个
	keywords, err := s.hotKeywordRepo.GetHotKeywordsByCategory(ctx, category, 10)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if keywordsData, err := json.Marshal(keywords); err == nil {
		redis.Set(cacheKey, string(keywordsData), 1*time.Hour)
	}

	return keywords, nil
}
