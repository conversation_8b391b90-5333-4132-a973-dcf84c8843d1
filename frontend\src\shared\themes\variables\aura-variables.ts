/**
 * Aura主题自定义变量
 */
export default {
    // 基础变量
    'border-radius': '6px',
    'font-family': '"Inter var", sans-serif',

    // 自定义颜色
    'custom-primary': 'var(--primary-color)',
    'custom-secondary': '#64748B',
    'custom-success': '#22C55E',
    'custom-info': '#3B82F6',
    'custom-warning': '#F97316',
    'custom-danger': '#EF4444',

    // 自定义尺寸
    'spacing-xs': '0.25rem',
    'spacing-sm': '0.5rem',
    'spacing-md': '1rem',
    'spacing-lg': '1.5rem',
    'spacing-xl': '2rem',

    // 自定义阴影
    'shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    'shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    'shadow-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',

    // 自定义过渡
    'transition-duration': '0.2s',
    'transition-timing-function': 'ease-in-out'
}; 