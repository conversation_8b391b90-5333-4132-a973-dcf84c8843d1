package users

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserInvitationRepository 用户邀请数据访问接口
type UserInvitationRepository interface {
	base.ExtendedRepository[users.UserInvitation]

	FindByInviterAndInvitee(ctx context.Context, id string, id2 string) (*users.UserInvitation, error)
}

// userInvitationRepository 用户邀请数据访问实现
type userInvitationRepository struct {
	base.ExtendedRepository[users.UserInvitation]
}

// NewUserInvitationRepository 创建用户邀请仓库实例
func NewUserInvitationRepository(db *gorm.DB) UserInvitationRepository {
	return &userInvitationRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserInvitation](db),
	}
}

// FindByInviterAndInvitee 根据邀请人和被邀请人查找邀请记录
func (r *userInvitationRepository) FindByInviterAndInvitee(ctx context.Context, inviterID, invitedID string) (*users.UserInvitation, error) {
	invitation, err := r.FindOneByCondition(ctx, map[string]interface{}{
		"inviter_id": inviterID,
		"invited_id": invitedID,
	}, "")
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return invitation, nil
}
