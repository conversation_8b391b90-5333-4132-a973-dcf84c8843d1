<template>
  <div class="enhanced-comment-input">
    <!-- 回复信息 -->
    <div v-if="replyTo" class="reply-info">
      <div class="reply-content">
        <i class="icon-reply"></i>
        <span>回复 @{{ replyTo.author.username }}：</span>
        <span class="reply-text">{{ replyTo.content.slice(0, 50) }}{{ replyTo.content.length > 50 ? '...' : '' }}</span>
      </div>
      <button class="btn-cancel-reply" @click="cancelReply">
        <i class="icon-close"></i>
      </button>
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <div class="user-avatar">
        <img :src="currentUserAvatar || defaultAvatar" :alt="currentUserName" />
      </div>
      
      <div class="input-content">
        <!-- 文本输入框 -->
        <div class="text-input-wrapper">
          <textarea
            ref="textareaRef"
            v-model="inputText"
            :placeholder="placeholder"
            class="text-input"
            :rows="minRows"
            :maxlength="maxLength"
            @input="handleInput"
            @keydown="handleKeydown"
            @focus="handleFocus"
            @blur="handleBlur"
          ></textarea>
          <div class="char-count" :class="{ 'warning': inputText.length > maxLength * 0.8 }">
            {{ inputText.length }}/{{ maxLength }}
          </div>
        </div>

        <!-- 媒体预览区域 -->
        <div v-if="mediaFiles.length > 0" class="media-preview">
          <div
            v-for="(file, index) in mediaFiles"
            :key="file.id"
            class="media-item"
            :class="{ 'video': file.type === 'video' }"
          >
            <div class="media-content">
              <img v-if="file.type === 'image'" :src="file.url" :alt="`图片 ${index + 1}`" />
              <div v-else class="video-preview">
                <video :src="file.url" :poster="file.thumbnail" muted></video>
                <div class="video-overlay">
                  <i class="icon-play"></i>
                  <span v-if="file.duration" class="duration">{{ formatDuration(file.duration) }}</span>
                </div>
              </div>
            </div>
            <button class="btn-remove" @click="removeMedia(index)">
              <i class="icon-close"></i>
            </button>
            <div v-if="file.uploading" class="upload-progress">
              <div class="progress-bar" :style="{ width: `${file.progress || 0}%` }"></div>
            </div>
          </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <!-- 表情选择器 -->
            <div class="emoji-picker-wrapper">
              <button
                class="toolbar-btn"
                :class="{ 'active': showEmojiPicker }"
                @click="toggleEmojiPicker"
                title="添加表情"
              >
                <i class="icon-emoji"></i>
              </button>
              <div v-if="showEmojiPicker" class="emoji-picker-panel">
                <EmojiPicker @select="insertEmoji" />
              </div>
            </div>

            <!-- 图片上传 -->
            <button
              v-if="supportImage"
              class="toolbar-btn"
              @click="triggerImageUpload"
              :disabled="!canAddMedia"
              title="添加图片"
            >
              <i class="icon-image"></i>
            </button>

            <!-- 视频上传 -->
            <button
              v-if="supportVideo"
              class="toolbar-btn"
              @click="triggerVideoUpload"
              :disabled="!canAddMedia"
              title="添加视频"
            >
              <i class="icon-video"></i>
            </button>

            <!-- @提及 -->
            <button
              class="toolbar-btn"
              @click="triggerMention"
              title="@提及用户"
            >
              <i class="icon-mention"></i>
            </button>
          </div>

          <div class="toolbar-right">
            <button
              class="btn-submit"
              :class="{ 'disabled': !canSubmit }"
              :disabled="!canSubmit"
              @click="handleSubmit"
            >
              {{ submitText }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件上传输入 -->
    <input
      ref="imageInputRef"
      type="file"
      accept="image/*"
      multiple
      style="display: none"
      @change="handleImageUpload"
    />
    <input
      ref="videoInputRef"
      type="file"
      accept="video/*"
      style="display: none"
      @change="handleVideoUpload"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import EmojiPicker from 'vue3-emoji-picker'
import type { Comment, MediaFile, CommentSubmitData } from '../../types/comment'

// 组件属性
interface Props {
  // 基础配置
  placeholder?: string
  maxLength?: number
  minRows?: number
  submitText?: string
  
  // 用户信息
  currentUserAvatar?: string
  currentUserName?: string
  
  // 回复配置
  replyTo?: Comment | null
  
  // 媒体支持
  supportImage?: boolean
  supportVideo?: boolean
  maxMediaCount?: number
  maxImageSize?: number // MB
  maxVideoSize?: number // MB
  
  // 功能开关
  enableEmoji?: boolean
  enableMention?: boolean
  autoFocus?: boolean
}

// 组件事件
interface Emits {
  'submit': [data: CommentSubmitData]
  'cancel-reply': []
  'focus': []
  'blur': []
  'input': [text: string]
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '写下你的评论...',
  maxLength: 500,
  minRows: 3,
  submitText: '发布',
  supportImage: true,
  supportVideo: true,
  maxMediaCount: 9,
  maxImageSize: 10,
  maxVideoSize: 100,
  enableEmoji: true,
  enableMention: true,
  autoFocus: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const textareaRef = ref<HTMLTextAreaElement>()
const imageInputRef = ref<HTMLInputElement>()
const videoInputRef = ref<HTMLInputElement>()

const inputText = ref('')
const mediaFiles = ref<MediaFile[]>([])
const showEmojiPicker = ref(false)
const focused = ref(false)

// 默认头像
const defaultAvatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=default'

// 计算属性
const canSubmit = computed(() => {
  return (inputText.value.trim().length > 0 || mediaFiles.value.length > 0) &&
         !mediaFiles.value.some(file => file.uploading)
})

const canAddMedia = computed(() => {
  return mediaFiles.value.length < props.maxMediaCount
})

// 监听器
watch(() => props.autoFocus, (autoFocus) => {
  if (autoFocus) {
    nextTick(() => {
      textareaRef.value?.focus()
    })
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
const handleInput = () => {
  emit('input', inputText.value)
  autoResize()
}

const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + Enter 提交
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    if (canSubmit.value) {
      handleSubmit()
    }
  }
}

const handleFocus = () => {
  focused.value = true
  emit('focus')
}

const handleBlur = () => {
  focused.value = false
  emit('blur')
}

const autoResize = () => {
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = `${Math.max(textareaRef.value.scrollHeight, props.minRows * 24)}px`
  }
}

const handleSubmit = () => {
  if (!canSubmit.value) return

  const data: CommentSubmitData = {
    content: inputText.value.trim(),
    media: mediaFiles.value.length > 0 ? mediaFiles.value : undefined,
    parentId: props.replyTo?.id
  }

  emit('submit', data)
  
  // 清空输入
  inputText.value = ''
  mediaFiles.value = []
  autoResize()
}

const cancelReply = () => {
  emit('cancel-reply')
}

// 表情相关
const toggleEmojiPicker = () => {
  showEmojiPicker.value = !showEmojiPicker.value
}

const insertEmoji = (emoji: any) => {
  const textarea = textareaRef.value
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const text = inputText.value
  
  inputText.value = text.slice(0, start) + emoji.i + text.slice(end)
  
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start + emoji.i.length, start + emoji.i.length)
  })
}

const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.emoji-picker-wrapper')) {
    showEmojiPicker.value = false
  }
}

// 媒体上传相关
const triggerImageUpload = () => {
  imageInputRef.value?.click()
}

const triggerVideoUpload = () => {
  videoInputRef.value?.click()
}

const handleImageUpload = (event: Event) => {
  const files = (event.target as HTMLInputElement).files
  if (!files) return

  Array.from(files).forEach(file => {
    if (file.size > props.maxImageSize * 1024 * 1024) {
      ElMessage.error(`图片大小不能超过 ${props.maxImageSize}MB`)
      return
    }
    
    uploadFile(file, 'image')
  })
}

const handleVideoUpload = (event: Event) => {
  const files = (event.target as HTMLInputElement).files
  if (!files) return

  Array.from(files).forEach(file => {
    if (file.size > props.maxVideoSize * 1024 * 1024) {
      ElMessage.error(`视频大小不能超过 ${props.maxVideoSize}MB`)
      return
    }
    
    uploadFile(file, 'video')
  })
}

const uploadFile = async (file: File, type: 'image' | 'video') => {
  const mediaFile: MediaFile = {
    id: `${Date.now()}-${Math.random()}`,
    type,
    url: URL.createObjectURL(file),
    size: file.size,
    uploading: true,
    progress: 0
  }

  mediaFiles.value.push(mediaFile)

  try {
    // TODO: 实际上传逻辑
    // const response = await uploadMedia(file, {
    //   onProgress: (progress) => {
    //     mediaFile.progress = progress
    //   }
    // })
    
    // 模拟上传进度
    const interval = setInterval(() => {
      if (mediaFile.progress! < 90) {
        mediaFile.progress = (mediaFile.progress || 0) + Math.random() * 20
      }
    }, 200)

    // 模拟上传完成
    setTimeout(() => {
      clearInterval(interval)
      mediaFile.progress = 100
      mediaFile.uploading = false
      // mediaFile.url = response.url // 使用服务器返回的URL
    }, 2000)

  } catch (error) {
    ElMessage.error('上传失败')
    removeMedia(mediaFiles.value.indexOf(mediaFile))
  }
}

const removeMedia = (index: number) => {
  const file = mediaFiles.value[index]
  if (file.url.startsWith('blob:')) {
    URL.revokeObjectURL(file.url)
  }
  mediaFiles.value.splice(index, 1)
}

// @提及功能
const triggerMention = () => {
  // TODO: 实现@提及功能
  console.log('Trigger mention')
}

// 工具函数
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
</script>

<style scoped lang="scss">
.enhanced-comment-input {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s ease;

  &:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

.reply-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;

  .reply-content {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;

    .icon-reply {
      width: 16px;
      height: 16px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6'/%3E%3C/svg%3E") no-repeat center;
      background-size: contain;
    }

    .reply-text {
      color: #9ca3af;
      font-style: italic;
    }
  }

  .btn-cancel-reply {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    background: #e5e7eb;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: #d1d5db;
    }

    .icon-close {
      width: 12px;
      height: 12px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'/%3E%3C/svg%3E") no-repeat center;
      background-size: contain;
    }
  }
}

.input-container {
  display: flex;
  gap: 12px;
  padding: 16px;
}

.user-avatar {
  flex-shrink: 0;

  img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.input-content {
  flex: 1;
  min-width: 0;
}

.text-input-wrapper {
  position: relative;
  margin-bottom: 12px;

  .text-input {
    width: 100%;
    min-height: 72px;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #3b82f6;
    }

    &::placeholder {
      color: #9ca3af;
    }
  }

  .char-count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 12px;
    color: #9ca3af;

    &.warning {
      color: #f59e0b;
    }
  }
}

.media-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  margin-bottom: 12px;

  .media-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    background: #f3f4f6;

    .media-content {
      width: 100%;
      height: 100%;

      img, video {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .video-preview {
      position: relative;
      width: 100%;
      height: 100%;

      .video-overlay {
        position: absolute;
        inset: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.3);
        color: white;

        .icon-play {
          width: 24px;
          height: 24px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E") no-repeat center;
          background-size: contain;
          margin-bottom: 4px;
        }

        .duration {
          font-size: 12px;
          background: rgba(0, 0, 0, 0.6);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }

    .btn-remove {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 20px;
      height: 20px;
      border: none;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-close {
        width: 12px;
        height: 12px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M6 18L18 6M6 6l12 12' stroke='white' stroke-width='2' stroke-linecap='round'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
      }
    }

    .upload-progress {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: rgba(0, 0, 0, 0.1);

      .progress-bar {
        height: 100%;
        background: #3b82f6;
        transition: width 0.3s ease;
      }
    }
  }
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: #f3f4f6;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.active {
      background: #3b82f6;
      color: white;
    }

    i {
      width: 18px;
      height: 18px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;

      &.icon-emoji {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E");
      }

      &.icon-image {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z'/%3E%3C/svg%3E");
      }

      &.icon-video {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z'/%3E%3C/svg%3E");
      }

      &.icon-mention {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207'/%3E%3C/svg%3E");
      }
    }
  }

  .emoji-picker-wrapper {
    position: relative;

    .emoji-picker-panel {
      position: absolute;
      bottom: 100%;
      left: 0;
      z-index: 1000;
      margin-bottom: 8px;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      overflow: hidden;
    }
  }

  .btn-submit {
    padding: 8px 16px;
    border: none;
    background: #3b82f6;
    color: white;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(.disabled) {
      background: #2563eb;
      transform: translateY(-1px);
    }

    &.disabled {
      background: #d1d5db;
      color: #9ca3af;
      cursor: not-allowed;
      transform: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .input-container {
    padding: 12px;
    gap: 8px;
  }

  .user-avatar img {
    width: 32px;
    height: 32px;
  }

  .text-input-wrapper .text-input {
    min-height: 60px;
    padding: 10px;
  }

  .media-preview {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }

  .toolbar {
    flex-wrap: wrap;
    gap: 8px;

    .toolbar-left {
      gap: 2px;
    }

    .toolbar-btn {
      width: 28px;
      height: 28px;

      i {
        width: 16px;
        height: 16px;
      }
    }

    .btn-submit {
      padding: 6px 12px;
      font-size: 13px;
    }
  }
}
</style> 