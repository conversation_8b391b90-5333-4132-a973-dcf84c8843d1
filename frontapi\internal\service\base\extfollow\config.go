package extfollow

import (
	"time"

	goredis "github.com/go-redis/redis/v8"
)

// Config 关注服务配置
type Config struct {
	// 服务配置
	ServiceName   string        `json:"service_name"`
	StorageType   string        `json:"storage_type"`   // redis, mongodb, hybrid
	DefaultTTL    time.Duration `json:"default_ttl"`    // 默认缓存时间
	BatchSize     int           `json:"batch_size"`     // 批量操作大小
	EnableMetrics bool          `json:"enable_metrics"` // 是否启用指标收集
	EnableSync    bool          `json:"enable_sync"`    // 是否启用数据同步

	// Redis配置
	RedisAddr     string `json:"redis_addr"`
	RedisPassword string `json:"redis_password"`
	RedisDB       int    `json:"redis_db"`
	CachePrefix   string `json:"cache_prefix"`
	Redis         struct {
		Client *goredis.Client `json:"-"` // Redis客户端实例
	}

	// 性能配置
	ConcurrencyLevel int           `json:"concurrency_level"`
	SyncTimeout      time.Duration `json:"sync_timeout"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		ServiceName:      "extfollow",
		StorageType:      "redis",
		DefaultTTL:       24 * time.Hour,
		BatchSize:        100,
		EnableMetrics:    true,
		EnableSync:       false,
		RedisAddr:        "localhost:6379",
		RedisPassword:    "",
		RedisDB:          0,
		CachePrefix:      "follow:",
		ConcurrencyLevel: 10,
		SyncTimeout:      30 * time.Second,
	}
}
