package common

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"regexp"
)

// ValidationHook 数据验证钩子
type ValidationHook struct {
	Rules map[string][]ValidationRule // 字段验证规则
}

// ValidationRule 验证规则
type ValidationRule struct {
	Type    string      // 规则类型: required, min_length, max_length, regex, custom
	Value   interface{} // 规则值
	Message string      // 错误消息
}

// Execute 执行数据验证
func (h *ValidationHook) Execute(ctx context.Context, data interface{}) error {
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return errors.New("数据必须是结构体类型")
	}

	for field, rules := range h.Rules {
		fieldValue := v.FieldByName(field)
		if !fieldValue.IsValid() {
			return fmt.Errorf("字段 %s 不存在", field)
		}

		for _, rule := range rules {
			if err := h.validateField(fieldValue, rule); err != nil {
				if rule.Message != "" {
					return errors.New(rule.Message)
				}
				return fmt.Errorf("字段 %s 验证失败: %w", field, err)
			}
		}
	}

	return nil
}

// validateField 验证单个字段
func (h *ValidationHook) validateField(fieldValue reflect.Value, rule ValidationRule) error {
	switch rule.Type {
	case "required":
		if fieldValue.IsZero() {
			return errors.New("字段不能为空")
		}
	case "min_length":
		if fieldValue.Kind() == reflect.String {
			minLen, ok := rule.Value.(int)
			if !ok {
				return errors.New("min_length 规则值必须是整数")
			}
			if len(fieldValue.String()) < minLen {
				return fmt.Errorf("字段长度不能少于 %d 个字符", minLen)
			}
		}
	case "max_length":
		if fieldValue.Kind() == reflect.String {
			maxLen, ok := rule.Value.(int)
			if !ok {
				return errors.New("max_length 规则值必须是整数")
			}
			if len(fieldValue.String()) > maxLen {
				return fmt.Errorf("字段长度不能超过 %d 个字符", maxLen)
			}
		}
	case "regex":
		if fieldValue.Kind() == reflect.String {
			pattern, ok := rule.Value.(string)
			if !ok {
				return errors.New("regex 规则值必须是字符串")
			}
			matched, err := regexp.MatchString(pattern, fieldValue.String())
			if err != nil {
				return fmt.Errorf("正则表达式错误: %w", err)
			}
			if !matched {
				return errors.New("字段格式不正确")
			}
		}
	case "custom":
		if customFunc, ok := rule.Value.(func(reflect.Value) error); ok {
			return customFunc(fieldValue)
		}
		return errors.New("custom 规则值必须是函数")
	}
	return nil
}

// NewValidationHook 创建验证钩子
func NewValidationHook() *ValidationHook {
	return &ValidationHook{
		Rules: make(map[string][]ValidationRule),
	}
}

// AddRule 添加验证规则
func (h *ValidationHook) AddRule(field string, rule ValidationRule) *ValidationHook {
	if h.Rules[field] == nil {
		h.Rules[field] = []ValidationRule{}
	}
	h.Rules[field] = append(h.Rules[field], rule)
	return h
}

// AddRequiredRule 添加必填规则
func (h *ValidationHook) AddRequiredRule(field string, message ...string) *ValidationHook {
	msg := "字段不能为空"
	if len(message) > 0 {
		msg = message[0]
	}
	return h.AddRule(field, ValidationRule{
		Type:    "required",
		Message: msg,
	})
}

// AddMinLengthRule 添加最小长度规则
func (h *ValidationHook) AddMinLengthRule(field string, minLen int, message ...string) *ValidationHook {
	msg := fmt.Sprintf("字段长度不能少于 %d 个字符", minLen)
	if len(message) > 0 {
		msg = message[0]
	}
	return h.AddRule(field, ValidationRule{
		Type:    "min_length",
		Value:   minLen,
		Message: msg,
	})
}

// AddMaxLengthRule 添加最大长度规则
func (h *ValidationHook) AddMaxLengthRule(field string, maxLen int, message ...string) *ValidationHook {
	msg := fmt.Sprintf("字段长度不能超过 %d 个字符", maxLen)
	if len(message) > 0 {
		msg = message[0]
	}
	return h.AddRule(field, ValidationRule{
		Type:    "max_length",
		Value:   maxLen,
		Message: msg,
	})
}

// AddRegexRule 添加正则表达式规则
func (h *ValidationHook) AddRegexRule(field string, pattern string, message ...string) *ValidationHook {
	msg := "字段格式不正确"
	if len(message) > 0 {
		msg = message[0]
	}
	return h.AddRule(field, ValidationRule{
		Type:    "regex",
		Value:   pattern,
		Message: msg,
	})
}