<template>
    <el-dialog :model-value="visible" :title="isEdit ? '编辑专辑' : '创建专辑'" width="600px" :before-close="handleClose">
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
            <el-form-item label="专辑名称" prop="title">
                <el-input v-model="formData.title" placeholder="请输入专辑名称" maxlength="100" show-word-limit />
            </el-form-item>

            <el-form-item label="专辑描述" prop="description">
                <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入专辑描述" maxlength="500"
                    show-word-limit />
            </el-form-item>

            <el-form-item label="封面图片" prop="cover">
                <UrlOrFileInput v-model="formData.cover" fileType="image" subDir="covers"
                    placeholder="请选择封面图片或输入图片URL" />
            </el-form-item>

            <el-form-item label="创建者">
                <el-select v-model="formData.user_id" filterable remote reserve-keyword placeholder="请搜索并选择创建者"
                    :remote-method="searchCreators" :loading="creatorsLoading" clearable style="width: 100%">
                    <el-option v-for="creator in creatorsOptions" :key="creator.id"
                        :label="creator.nickname || creator.username" :value="creator.id">
                        <div style="display: flex; align-items: center;">
                            <el-avatar :size="24" :src="creator.avatar" style="margin-right: 8px;">
                                {{ (creator.nickname || creator.username).charAt(0) }}
                            </el-avatar>
                            <span>{{ creator.nickname || creator.username }}</span>
                            <span v-if="creator.username && creator.nickname" style="color: #999; margin-left: 8px;">
                                ({{ creator.username }})
                            </span>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="分类">
                <el-select v-model="formData.category_id" filterable remote reserve-keyword placeholder="请搜索并选择分类"
                    :remote-method="searchCategories" :loading="categoriesLoading" clearable style="width: 100%">
                    <el-option v-for="category in categoriesOptions" :key="category.id" :label="category.name"
                        :value="category.id">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span>{{ category.name }}</span>
                            <span v-if="category.description" style="color: #999; font-size: 12px;">
                                {{ category.description }}
                            </span>
                        </div>
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="排序" prop="sort_order">
                <el-input-number v-model="formData.sort_order" :min="0" :max="999" controls-position="right"
                    style="width: 200px;" />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleSubmit">
                    {{ isEdit ? '更新' : '创建' }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElForm } from 'element-plus'
import { searchUsers, searchVideoCategories } from '@/service/api/videos/videos'
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue'

interface Props {
    visible: boolean
    album?: any  // 改为album以匹配父组件传递的prop名称
}

interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'submit', data: any): void  // 改为submit事件以匹配父组件监听的事件
    (e: 'close'): void  // 添加close事件
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<InstanceType<typeof ElForm>>()
const loading = ref(false)
const creatorsLoading = ref(false)
const categoriesLoading = ref(false)

// 选项数据
const creatorsOptions = ref<any[]>([])
const categoriesOptions = ref<any[]>([])

const isEdit = computed(() => !!props.album?.id)

// 表单数据（移除is_featured字段）
const formData = reactive({
    id: '',
    title: '',
    description: '',
    cover: '',
    user_id: '',
    category_id: '',
    status: 1,
    sort_order: 0
})

// 表单验证规则
const rules = {
    title: [
        { required: true, message: '请输入专辑名称', trigger: 'blur' },
        { min: 2, max: 100, message: '专辑名称长度在 2 到 100 个字符', trigger: 'blur' }
    ],
    status: [
        { required: true, message: '请选择状态', trigger: 'change' }
    ]
}

// 搜索创建者
const searchCreators = async (query: string) => {
    if (query) {
        creatorsLoading.value = true
        try {
            const result = await searchUsers({
                page: { pageNo: 1, pageSize: 20 },
                data: { keyword: query, status: 1 }
            })

            if (result?.data?.list) {
                creatorsOptions.value = result.data.list
            } else {
                creatorsOptions.value = []
            }
        } catch (error) {
            console.error('搜索创建者失败:', error)
            ElMessage.error('搜索创建者失败')
            creatorsOptions.value = []
        } finally {
            creatorsLoading.value = false
        }
    } else {
        creatorsOptions.value = []
    }
}

// 搜索分类
const searchCategories = async (query: string) => {
    if (query) {
        categoriesLoading.value = true
        try {
            const result = await searchVideoCategories({
                page: { pageNo: 1, pageSize: 20 },
                data: { keyword: query, status: 1 }
            })

            if (result?.data?.list) {
                categoriesOptions.value = result.data.list
            } else {
                categoriesOptions.value = []
            }
        } catch (error) {
            console.error('搜索分类失败:', error)
            ElMessage.error('搜索分类失败')
            categoriesOptions.value = []
        } finally {
            categoriesLoading.value = false
        }
    } else {
        categoriesOptions.value = []
    }
}

// 重置表单（移除is_featured字段）
const resetForm = () => {
    Object.assign(formData, {
        id: '',
        title: '',
        description: '',
        cover: '',
        user_id: '',
        category_id: '',
        status: 1,
        sort_order: 0
    })
    creatorsOptions.value = []
    categoriesOptions.value = []
    formRef.value?.resetFields()
}

// 初始化表单数据（移除is_featured字段）
const initFormData = () => {
    if (props.album) {
        // 使用props.album而不是props.albumData
        Object.assign(formData, {
            id: props.album.id || '',
            title: props.album.title || '',
            description: props.album.description || '',
            cover: props.album.cover || '',
            user_id: props.album.user_id || '', // 使用user_id而不是creator_id
            category_id: props.album.category_id || '',
            status: props.album.status !== undefined ? props.album.status : 1,
            sort_order: props.album.sort_order || 0
        })

        // 如果是编辑模式，需要加载当前选中的创建者和分类信息
        if (props.album.user_id) {
            // 如果有用户昵称，直接添加到选项中
            if (props.album.user_nickname) {
                creatorsOptions.value = [{
                    id: props.album.user_id,
                    nickname: props.album.user_nickname,
                    username: props.album.user_nickname,
                    avatar: props.album.user_avatar || ''
                }]
            }
        }

        if (props.album.category_id) {
            // 如果有分类名称，直接添加到选项中
            if (props.album.category_name) {
                categoriesOptions.value = [{
                    id: props.album.category_id,
                    name: props.album.category_name,
                    description: ''
                }]
            }
        }
    } else {
        resetForm()
    }
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return

    try {
        await formRef.value.validate()

        // 触发submit事件，将数据传递给父组件
        emit('submit', { ...formData })
    } catch (error) {
        console.error('表单验证失败:', error)
    }
}

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false)
    emit('close')
    // 不在这里重置表单，让父组件控制
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
    if (newVal) {
        nextTick(() => {
            initFormData()
        })
    }
})

// 监听album数据变化，用于编辑模式
watch(() => props.album, () => {
    if (props.visible) {
        nextTick(() => {
            initFormData()
        })
    }
}, { deep: true })

// 页面加载时获取一些默认选项
const loadDefaultOptions = async () => {
    // 加载一些默认的分类选项
    try {
        const result = await searchVideoCategories({
            page: { pageNo: 1, pageSize: 10 },
            data: { status: 1 }
        })

        if (result?.data?.list && !props.album?.category_id) {
            categoriesOptions.value = result.data.list
        }
    } catch (error) {
        console.error('加载默认分类失败:', error)
    }
}

// 组件挂载时加载默认选项
loadDefaultOptions()
</script>

<style scoped>
.dialog-footer {
    text-align: right;
}

:deep(.el-select-dropdown__item) {
    height: auto;
    line-height: normal;
    padding: 8px 20px;
}
</style>