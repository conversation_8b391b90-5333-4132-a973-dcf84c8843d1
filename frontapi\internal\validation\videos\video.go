package videos

// VideoCreateRequest 视频创建请求验证模型
type VideoCreateRequest struct {
	Title       string   `json:"title" validate:"required|minLen:2|maxLen:100"`
	Description string   `json:"description" validate:"required|minLen:10|maxLen:2000"`
	CategoryID  uint     `json:"categoryId" validate:"required|min:1"`
	CoverURL    string   `json:"coverUrl" validate:"required|url"`
	VideoURL    string   `json:"videoUrl" validate:"required|url"`
	Duration    int      `json:"duration" validate:"required|min:1"`
	Tags        []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
	IsPrivate   bool     `json:"isPrivate"`
}

// VideoUpdateRequest 视频更新请求验证模型
type VideoUpdateRequest struct {
	Title       string   `json:"title" validate:"minLen:2|maxLen:100"`
	Description string   `json:"description" validate:"minLen:10|maxLen:2000"`
	CategoryID  uint     `json:"categoryId" validate:"min:1"`
	CoverURL    string   `json:"coverUrl" validate:"url"`
	Tags        []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
	IsPrivate   bool     `json:"isPrivate"`
}

// VideoListRequest 视频列表请求验证模型
type VideoListRequest struct {
	Page     int    `json:"page" validate:"min:1"`
	PageSize int    `json:"pageSize" validate:"min:1|max:100"`
	Category uint   `json:"category" validate:"min:1"`
	Keyword  string `json:"keyword" validate:"maxLen:50"`
	SortBy   string `json:"sortBy" validate:"in:latest,popular,favorite"`
}

// VideoSearchRequest 视频搜索请求验证模型
type VideoSearchRequest struct {
	Keyword  string `json:"keyword" validate:"required|minLen:1|maxLen:50"`
	Page     int    `json:"page" validate:"min:1"`
	PageSize int    `json:"pageSize" validate:"min:1|max:100"`
	SortBy   string `json:"sortBy" validate:"in:latest,popular,relevant"`
}

// CreateVideoRequest 创建视频请求
type CreateVideoRequest struct {
	Title         string               `json:"title" validate:"required"`
	Description   string               `json:"description"`
	Cover         string               `json:"cover" validate:"required"`
	URL           string               `json:"url" validate:"required"`
	Duration      int                  `json:"duration"`
	Resolution    string               `json:"resolution"`
	Quality       string               `json:"quality"`
	Format        string               `json:"format"`
	Celebrities   []string             `json:"celebrities"`
	CategoryID    *string              `json:"category_id" validate:"required_without:channel_id"`
	CategoryName  string               `json:"category_name"`
	ChannelID     *string              `json:"channel_id" validate:"required_without:category_id"`
	ChannelName   string               `json:"channel_name"`
	AlbumID       *string              `json:"album_id"`
	CreatorID     *string              `json:"creator_id"`
	CreatorName   string               `json:"creator_name"`
	CreatorAvatar string               `json:"creator_avatar"`
	Tags          []string             `json:"tags"`
	IsPaid        int8                 `json:"is_paid"`
	IsPrivate     int8                 `json:"is_private"`
	IsVIP         int8                 `json:"is_vip"`
	Price         float64              `json:"price"`
	Sources       []VideoSourceRequest `json:"sources"`
}

// VideoSourceRequest 视频源请求
type VideoSourceRequest struct {
	Quality string `json:"quality"`
	URL     string `json:"url"`
	Size    int64  `json:"size"`
	Format  string `json:"format"`
}

// UpdateVideoRequest 更新视频请求
type UpdateVideoRequest struct {
	ID           string   `json:"id"`
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	Cover        *string  `json:"cover"`
	URL          string   `json:"url"`
	CategoryID   *string  `json:"category_id"`
	CategoryName string   `json:"category_name"`
	ChannelID    *string  `json:"channel_id"`
	ChannelName  string   `json:"channel_name"`
	AlbumID      *string  `json:"album_id"` // 所属专辑ID
	Celebrities  []string `json:"celebrities"`
	CreatorID    *string  `json:"creator_id"`
	Tags         []string `json:"tags"`
	IsPrivate    int8     `json:"is_private"`
	IsVIP        int8     `json:"is_vip"`
	Price        float64  `json:"price"`
	Status       int8     `json:"status"`
}
