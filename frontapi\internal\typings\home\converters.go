package home

import (
	"fmt"
	sysModel "frontapi/internal/models/system"
	"frontapi/internal/models/users"
	"frontapi/internal/models/videos"
	"time"
)

// ConvertTag 转换标签信息
func ConvertTag(tag *sysModel.Tag) HomeTag {
	return HomeTag{
		ID:     tag.GetID(),
		Name:   tag.Name,
		Icon:   "", // 标签模型中没有Icon字段，设为空字符串
		Sort:   tag.SortOrder,
		Status: 1, // 标签模型中没有Status字段，默认设为1（启用）
	}
}

// ConvertTagList 转换标签列表
func ConvertTagList(tags []*sysModel.Tag) []HomeTag {
	result := make([]HomeTag, len(tags))
	for i, tag := range tags {
		result[i] = ConvertTag(tag)
	}
	return result
}

// ConvertTagListResponse 转换标签列表响应
func ConvertTagListResponse(tags []*sysModel.Tag, total int64, page, size int) HomeTagListResponse {
	convertedTags := ConvertTagList(tags)
	return HomeTagListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedTags,
	}
}

// formatDuration 格式化时长（秒转换为 MM:SS 格式）
func formatDuration(seconds int) string {
	if seconds <= 0 {
		return "00:00"
	}
	minutes := seconds / 60
	secs := seconds % 60
	return fmt.Sprintf("%02d:%02d", minutes, secs)
}

// formatTime 格式化时间
func formatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

// ConvertVideo 转换视频信息
func ConvertVideo(video *videos.Video) HomeVideo {
	var categoryID, creatorID string
	if video.CategoryID.Valid {
		categoryID = video.CategoryID.String
	}
	if video.CreatorID.Valid {
		creatorID = video.CreatorID.String
	}

	var description string
	if video.Description.Valid {
		description = video.Description.String
	}

	homeVideo := HomeVideo{
		ID:            video.ID,
		Title:         video.Title,
		Description:   description,
		Cover:         video.Cover,
		Image:         video.Cover,     // 兼容前端字段
		Thumbnail:     video.Thumbnail, // 兼容前端字段
		URL:           video.URL,
		Duration:      formatDuration(video.Duration),
		ViewCount:     video.ViewCount,
		LikeCount:     video.LikeCount,
		Hot:           0, // 兼容前端字段
		Heat:          0,
		CommentCount:  video.CommentCount,
		ShareCount:    video.ShareCount,
		CategoryID:    categoryID,
		CategoryName:  video.CategoryName,
		CreatorID:     creatorID,
		CreatorName:   getCreatorName(video.Author),
		CreatorAvatar: getCreatorAvatar(video.Author),
		Status:        video.Status,
		CreatedAt:     formatTime(time.Time(video.CreatedAt)),
		UpdatedAt:     formatTime(time.Time(video.UpdatedAt)),
	}
	if video.Author != nil {
		homeVideo.Author = &HomeBaseUser{
			ID:               video.Author.ID,
			Username:         video.Author.Username.ValueOrZero(),
			Nickname:         video.Author.Nickname.ValueOrZero(),
			Avatar:           getCreatorAvatar(video.Author),
			LikeCount:        uint64(video.Author.LikeCount),
			IsContentCreator: int8(video.Author.IsContentCreator),
			IsFollowed:       false,
			ViewCount:        uint64(video.Author.TotalViews),
			FollowCount:      uint64(video.Author.FollowCount),
			TotalVideos:      uint64(video.Author.TotalVideos),
			TotalShorts:      uint64(video.Author.TotalShorts),
			TotalPosts:       uint64(video.Author.TotalPosts),
		}
	}
	return homeVideo
}

// ConvertVideoList 转换视频列表
func ConvertVideoList(videos []*videos.Video) []HomeVideo {
	result := make([]HomeVideo, len(videos))
	for i, video := range videos {
		result[i] = ConvertVideo(video)
	}
	return result
}

// ConvertVideoListResponse 转换视频列表响应
func ConvertVideoListResponse(videos []*videos.Video, total int64, page, size int) HomeVideoListResponse {
	convertedVideos := ConvertVideoList(videos)
	return HomeVideoListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedVideos,
	}
}

// ConvertStar 转换明星信息
func ConvertStar(user *users.User) HomeStar {
	// 处理null类型字段
	avatarStr := ""
	if user.Avatar.Valid {
		avatarStr = user.Avatar.String
	}

	return HomeStar{
		HomeBaseUser: HomeBaseUser{
			ID:               user.ID,
			Username:         user.Username.ValueOrZero(),
			Nickname:         user.Nickname.ValueOrZero(),
			Avatar:           avatarStr,
			LikeCount:        uint64(user.LikeCount),
			IsContentCreator: int8(user.IsContentCreator),
			IsFollowed:       false,
			ViewCount:        uint64(user.TotalViews),
			FollowCount:      uint64(user.FollowCount),
			TotalVideos:      uint64(user.TotalVideos),
			TotalShorts:      uint64(user.TotalShorts),
			TotalPosts:       uint64(user.TotalPosts),
		},
	}
}

// ConvertStarList 转换明星列表
func ConvertStarList(users []*users.User) []HomeStar {
	result := make([]HomeStar, len(users))
	for i, user := range users {
		result[i] = ConvertStar(user)
	}
	return result
}

// ConvertStarListResponse 转换明星列表响应
func ConvertStarListResponse(users []*users.User, total int64, page, size int) HomeStarListResponse {
	convertedStars := ConvertStarList(users)
	return HomeStarListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedStars,
	}
}

// ConvertUser 转换用户信息
func ConvertUser(user *users.User) HomeUser {
	return HomeUser{
		HomeBaseUser: HomeBaseUser{
			ID:               user.ID,
			Username:         user.Username.ValueOrZero(),
			Nickname:         user.Nickname.ValueOrZero(),
			Avatar:           getCreatorAvatar(user),
			LikeCount:        uint64(user.LikeCount),
			IsContentCreator: int8(user.IsContentCreator),
			IsFollowed:       false,
			ViewCount:        uint64(user.TotalViews),
			FollowCount:      uint64(user.FollowCount),
			TotalVideos:      uint64(user.TotalVideos),
			TotalShorts:      uint64(user.TotalShorts + user.TotalAlbums),
			TotalPosts:       uint64(user.TotalPosts),
		},
	}
}

// ConvertUserList 转换用户列表
func ConvertUserList(users []*users.User) []HomeUser {
	result := make([]HomeUser, len(users))
	for i, user := range users {
		result[i] = ConvertUser(user)
	}
	return result
}

// ConvertUserListResponse 转换用户列表响应
func ConvertUserListResponse(users []*users.User, total int64, page, size int) HomeUserListResponse {
	convertedUsers := ConvertUserList(users)
	return HomeUserListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedUsers,
	}
}

// ConvertCreator 转换创作者信息
func ConvertCreator(user *users.User) HomeCreator {
	return HomeCreator{
		HomeBaseUser: HomeBaseUser{
			ID:               user.ID,
			Username:         user.Username.ValueOrZero(),
			Nickname:         user.Nickname.ValueOrZero(),
			Avatar:           getCreatorAvatar(user),
			LikeCount:        uint64(user.LikeCount),
			IsContentCreator: int8(user.IsContentCreator),
			IsFollowed:       false,
			ViewCount:        uint64(user.TotalViews),
			FollowCount:      uint64(user.FollowCount),
			TotalVideos:      uint64(user.TotalVideos),
			TotalShorts:      uint64(user.TotalShorts + user.TotalAlbums),
			TotalPosts:       uint64(user.TotalPosts),
		},
	}
}

// ConvertCreatorList 转换创作者列表
func ConvertCreatorList(users []*users.User) []HomeCreator {
	result := make([]HomeCreator, len(users))
	for i, user := range users {
		result[i] = ConvertCreator(user)
	}
	return result
}

// ConvertCreatorListResponse 转换创作者列表响应
func ConvertCreatorListResponse(users []*users.User, total int64, page, size int) HomeCreatorListResponse {
	convertedCreators := ConvertCreatorList(users)
	return HomeCreatorListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedCreators,
	}
}

// ConvertCategory 转换分类信息
func ConvertCategory(category *videos.VideoCategory) HomeCategory {
	return HomeCategory{
		ID:         category.ID,
		Name:       category.Name.ValueOrZero(),
		Code:       category.Code,
		Icon:       category.Icon,
		Cover:      category.Image,
		Sort:       category.SortOrder,
		ViewCount:  0, // 视频分类模型中没有这个字段，设为0或从其他地方获取
		VideoCount: 0, // 视频分类模型中没有这个字段，设为0或从其他地方获取
		IsFeatured: int8(category.IsFeatured),
	}
}

// ConvertCategoryList 转换分类列表
func ConvertCategoryList(categories []*videos.VideoCategory) []HomeCategory {
	result := make([]HomeCategory, len(categories))
	for i, category := range categories {
		result[i] = ConvertCategory(category)
	}
	return result
}

// ConvertCategoryListResponse 转换分类列表响应
func ConvertCategoryListResponse(categories []*videos.VideoCategory, total int64, page, size int) HomeCategoryListResponse {
	convertedCategories := ConvertCategoryList(categories)
	return HomeCategoryListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedCategories,
	}
}

// ConvertAlbum 转换频道信息
func ConvertChannel(channel *videos.VideoChannel) HomeChannel {
	return HomeChannel{
		ID:         channel.ID,
		Name:       channel.Name,
		Icon:       channel.Icon,
		Image:      channel.Image,
		Sort:       int(channel.SortOrder),
		ViewCount:  0, // 视频分类模型中没有这个字段，设为0或从其他地方获取
		VideoCount: 0, // 视频分类模型中没有这个字段，设为0或从其他地方获取
	}
}

// ConvertAlbumList 转换专辑列表
func ConvertChannelList(albums []*videos.VideoChannel) []HomeChannel {
	result := make([]HomeChannel, len(albums))
	for i, album := range albums {
		result[i] = ConvertChannel(album)
	}
	return result
}
func ConvertChannelListResponse(albums []*videos.VideoChannel, total int64, page, size int) HomeChannelListResponse {
	convertedAlbums := ConvertChannelList(albums)
	return HomeChannelListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedAlbums,
	}
}

// 专辑相关操作--------------------
func ConvertAlbum(album *videos.VideoAlbum) HomeAlbum {
	HomeAlbum := HomeAlbum{
		ID:           album.ID,
		Title:        album.Title,
		Description:  album.Description,
		CategoryID:   album.CategoryID.ValueOrZero(),
		CategoryName: album.CategoryName.ValueOrZero(),
		Tags:         album.Tags,
		Cover:        album.Cover.ValueOrZero(),
		Heat:         int(album.Heat),
		ViewCount:    int(album.ViewCount),
		VideoCount:   int(album.VideoCount),
		IsPaid:       int8(album.IsPaid),
		Price:        album.Price,
		CreatedAt:    formatTime(time.Time(album.CreatedAt)),
		UpdatedAt:    formatTime(time.Time(album.UpdatedAt)),
		Status:       int8(album.Status),
	}
	if album.Author != nil {
		HomeAlbum.Author = &HomeBaseUser{
			ID:               album.Author.ID,
			Username:         album.Author.Username.ValueOrZero(),
			Nickname:         album.Author.Nickname.ValueOrZero(),
			Avatar:           getCreatorAvatar(album.Author),
			LikeCount:        uint64(album.Author.LikeCount),
			IsContentCreator: int8(album.Author.IsContentCreator),
			IsFollowed:       false,
			ViewCount:        uint64(album.Author.TotalViews),
			FollowCount:      uint64(album.Author.FollowCount),
			TotalVideos:      uint64(album.Author.TotalVideos),
			TotalShorts:      uint64(album.Author.TotalShorts),
			TotalPosts:       uint64(album.Author.TotalPosts),
		}
	}
	return HomeAlbum
}

// ConvertAlbumList 转换专辑列表
func ConvertAlbumList(albums []*videos.VideoAlbum) []HomeAlbum {
	result := make([]HomeAlbum, len(albums))
	for i, album := range albums {
		result[i] = ConvertAlbum(album)
	}
	return result
}

// ConvertAlbumListResponse 转换专辑列表响应
func ConvertAlbumListResponse(albums []*videos.VideoAlbum, total int64, page, size int) HomeAlbumListResponse {
	convertedAlbums := ConvertAlbumList(albums)
	return HomeAlbumListResponse{
		BaseListResponse: BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: convertedAlbums,
	}
}

//专辑相关操作--------------------

// getCreatorName 获取创作者名称
func getCreatorName(author *users.User) string {
	if author != nil && author.Nickname.Valid {
		return author.Nickname.String
	}
	return ""
}

// getCreatorAvatar 获取创作者头像
func getCreatorAvatar(author *users.User) string {
	if author != nil && author.Avatar.Valid {
		return author.Avatar.String
	}
	return ""
}
