package permission

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username     string `json:"username" validate:"required,min=3,max=50"`
	Password     string `json:"password" validate:"required,min=6,max=32"`
	Nickname     string `json:"nickname" validate:"required,min=2,max=100"`
	Email        string `json:"email" validate:"email"`
	Phone        string `json:"phone" validate:"phone"`
	Remark       string `json:"remark" validate:"max=500"`
	DeptID       int    `json:"dept_id"`
	IsSuperAdmin bool   `json:"is_super_admin"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Nickname     string `json:"nickname" validate:"min=2,max=100"`
	Email        string `json:"email" validate:"email"`
	Phone        string `json:"phone" validate:"phone"`
	Remark       string `json:"remark" validate:"max=500"`
	DeptID       int    `json:"dept_id"`
	IsSuperAdmin bool   `json:"is_super_admin"`
	Status       int8   `json:"status"`
}
