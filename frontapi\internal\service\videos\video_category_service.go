package videos

import (
	"frontapi/internal/models/videos"
	repo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

// VideoCategoryService 视频分类服务接口
type VideoCategoryService interface {
	base.IExtendedService[videos.VideoCategory]
}

// videoCategoryService 视频分类服务实现
type videoCategoryService struct {
	*base.ExtendedService[videos.VideoCategory]
	categoryRepo   repo.VideoCategoryRepository
	videoAlbumRepo repo.VideosAlbumsRepository
	videoRepo      repo.VideoRepository
}

// NewVideoCategoryService 创建视频分类服务实例
func NewVideoCategoryService(categoryRepo repo.VideoCategoryRepository, videoAlbumRepo repo.VideosAlbumsRepository, videoRepo repo.VideoRepository) VideoCategoryService {
	return &videoCategoryService{
		ExtendedService: base.NewExtendedService[videos.VideoCategory](categoryRepo, "video_category"),
		categoryRepo:    categoryRepo,
		videoAlbumRepo:  videoAlbumRepo,
		videoRepo:       videoRepo,
	}
}
