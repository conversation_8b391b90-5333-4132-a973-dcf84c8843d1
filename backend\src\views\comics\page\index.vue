<template>
  <div class="page-list">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <div>
            <span class="text-lg font-medium">页面管理</span>
          </div>
          <div>
            <div class="flex">
              <el-button v-if="!isSortMode" type="primary" @click="toggleSortMode">排序模式</el-button>
              <el-button v-if="isSortMode && orderChanged" type="success" @click="savePageOrder">保存排序</el-button>
              <el-button v-if="isSortMode" type="info" @click="cancelSortMode">取消排序</el-button>
              <el-button type="danger" @click="$router.push(`/comics/list`)">返回漫画列表</el-button>
              <el-button type="danger" @click="$router.push(`/comics/chapter/${comicsId}`)">返回章节列表</el-button>&nbsp;&nbsp;
              <el-button v-if="!isSortMode" type="primary" @click="onAdd">添加页面</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <search-form
        v-if="!isSortMode"
        v-model="searchForm"
        @search="onSearch"
        @reset="onReset"
      />

      <!-- 页面表格 -->
      <page-table
        v-model="pagination"
        :page-list="pageList"
        :sortable-page-list="sortablePageList"
        :loading="loading"
        :total="pagination.total"
        :chapterId.layz="chapterId"
        :comicId.layz="comicsId"
        :is-sort-mode="isSortMode"
        :show-pagination="true"
        @edit="onEdit"
        @delete="onDelete"
        @refresh="fetchList"
        @order-change="handleOrderChange"
        @update:sortable-page-list="updateSortablePageList"
        ref="pageTableRef"
      />
    </el-card>

    <!-- 添加页面对话框 -->
    <page-form
      :visible="addDialogVisible"
      :comics-id="comicsId" @update:visible="addDialogVisible=false"
      :chapter-id="chapterId"
      :page-max="pageMax"
      @success="onSuccess"
    />

    <!-- 编辑页面对话框 -->
    <page-edit-form
      :visible="editDialogVisible"
      :edit-data="editData"
      @update:visible="editDialogVisible=false"
      :page-max="pageMax"
      @success="onSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import { getComicsPageList, deleteComicsPage } from '@/service/api/comics/comics';
import type { ComicsPage, ComicsParams } from '@/types/comics';

// 导入子组件
import SearchForm from './components/SearchForm.vue';
import PageTable from './components/PageTable.vue';
import PageForm from './components/PageForm.vue';
import PageEditForm from './components/PageEditForm.vue';

const route = useRoute();
const comicsId = route.params.comic_id as string;
const chapterId = route.params.chapter_id as string;
const loading = ref(false);
const pageList = ref<ComicsPage[]>([]);
const pagination = reactive({ page: 1, pageSize: 10, total: 0 });
const searchForm = reactive({ page_number: '' });
const orderChanged = ref(false);
const pageTableRef = ref<InstanceType<typeof PageTable> | null>(null);
const pageMax=ref(1)

// 对话框相关
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
const editData = ref<ComicsPage>({} as ComicsPage);

// 排序相关
const isSortMode = ref(false);
const sortablePageList = ref<ComicsPage[]>([]);

// 获取列表数据
const fetchList = async () => {
  loading.value = true;
  try {
    const params: ComicsParams = {
      page: { pageNo: pagination.page, pageSize: pagination.pageSize },
      data: {
        ...searchForm.page_number ? { page_number: searchForm.page_number } : {}
      } as any
    };

    const {response,data} = await getComicsPageList(chapterId, comicsId, params) as any;
    console.log("数据:",response,data)
    if (response.data.code === 2000) {
      pageList.value = data.list;
      pagination.total = data.total;
      getPageNumMax()
    }
  } finally {
    loading.value = false;
  }
};

const getPageNumMax=()=>{
  const maxPageNum = pageList.value.reduce((max, page) => Math.max(max, page.page_number), 0);
  pageMax.value = maxPageNum;
}

// 搜索和重置
const onSearch = () => {
  pagination.page = 1;
  fetchList();
};

const onReset = () => {
  pagination.page = 1;
  fetchList();
};

// 添加和编辑
const onAdd = () => {
  addDialogVisible.value = true;
};

const onEdit = (row: ComicsPage) => {
  editData.value = row;
  editDialogVisible.value = true;
};

// 删除
const onDelete = async (row: ComicsPage) => {
  try {
    await ElMessageBox.confirm('确定要删除该页面吗？', '提示', { type: 'warning' });
    const {response,data} = await deleteComicsPage(row.id) as any;
    if (response.data.code === 2000) {
      ElMessage.success('删除成功');
      fetchList();
    } else {
      ElMessage.error(response.data?.message || '删除失败');
    }
  } catch (e) {}
};

// 处理排序变化
const handleOrderChange = (changed: boolean) => {
  orderChanged.value = changed;
};

// 保存页面排序
const savePageOrder = () => {
  if (pageTableRef.value) {
    pageTableRef.value.saveOrder();
  }
};

// 表单提交成功
const onSuccess = () => {
  fetchList();
};

// 排序相关 - 拖拽
const onRowDrop = async (params: any) => {
  // This is now handled by the PageTable component
  // Just used for logging or additional processing if needed
  console.log('Row drop event received from PageTable:', params);
};

// 切换排序模式
const toggleSortMode = () => {
  isSortMode.value = true;
  sortablePageList.value = [...pageList.value];
};

// 取消排序模式
const cancelSortMode = () => {
  isSortMode.value = false;
  sortablePageList.value = [];
};

// 更新可排序页面列表
const updateSortablePageList = (newList: ComicsPage[]) => {
  sortablePageList.value = newList;
};

onMounted(() => {
  fetchList();
});
</script>

<style scoped lang="scss">
.page-list {
  .search-form {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
