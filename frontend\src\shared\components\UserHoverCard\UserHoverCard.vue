<template>
  <el-popover
    :visible="visible"
    :width="320"
    placement="bottom-start"
    :show-arrow="false"
    :offset="8"
    popper-class="user-hover-card-popper"
    trigger="manual"
  >
    <template #reference>
      <div
        class="user-hover-trigger"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
        @click="handleClick"
      >
        <UserAvatar
          :avatar="userInfo?.avatar"
          :username="userInfo?.username"
          :nickname="userInfo?.nickname"
          :size="avatarSize"
          :is-online="userInfo.isFollowed"
          :is-verified="userInfo.userType === 2"
          :show-border="showAvatarBorder"
        />
      </div>
    </template>

    <div class="user-hover-card" @mouseenter="handleCardEnter" @mouseleave="handleCardLeave">
      <!-- 用户基本信息 -->
      <div class="user-header">
        <UserAvatar
          :avatar="userInfo.avatar || `/assets/images/avatar_${userInfo.userType%5}.png`"
          :username="userInfo.username"
          :nickname="userInfo.nickname"
          :size="64"
          :is-online="userInfo.isFollowed"
          :is-verified="userInfo.userType === 2"
        />
        
        <div class="user-info">
          <div class="user-name">
            <h3>{{ userInfo.nickname || userInfo.username }}</h3>
            <div v-if="userInfo.userType === 2" class="creator-badge">
              <el-icon><Star /></el-icon>
              <span>创作者</span>
            </div>
          </div>
          
          <div v-if="userInfo.bio" class="user-bio">
            {{ userInfo.bio }}
          </div>
        </div>
      </div>

      <!-- 用户统计 -->
      <UserStats
        :follow-count="userInfo.followCount"
        :total-videos="userInfo.totalVideos"
        :total-posts="userInfo.totalPosts"
        :total-shorts="userInfo.totalShorts"
      />

      <!-- 关注按钮 -->
      <div class="user-actions">
        <FollowButton
          :is-followed="userInfo.isFollowed || false"
          :loading="followLoading"
          @follow="handleFollow"
          @unfollow="handleUnfollow"
        />
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Star } from '@element-plus/icons-vue'
import UserAvatar from './UserAvatar.vue'
import UserStats from './UserStats.vue'
import FollowButton from './FollowButton.vue'
import type { User } from '@/types/user'

interface Props {
  userInfo: User
  avatarSize?: number
  showAvatarBorder?: boolean
  hoverDelay?: number
}

interface Emits {
  (e: 'click', user: User): void
  (e: 'follow', user: User): void
  (e: 'unfollow', user: User): void
}

const props = withDefaults(defineProps<Props>(), {
  avatarSize: 48,
  showAvatarBorder: true,
  hoverDelay: 300
})

const emit = defineEmits<Emits>()

const visible = ref(false)
const followLoading = ref(false)
let hoverTimer: NodeJS.Timeout | null = null
let leaveTimer: NodeJS.Timeout | null = null

const handleMouseEnter = () => {
  if (leaveTimer) {
    clearTimeout(leaveTimer)
    leaveTimer = null
  }
  
  hoverTimer = setTimeout(() => {
    visible.value = true
  }, props.hoverDelay)
}

const handleMouseLeave = () => {
  if (hoverTimer) {
    clearTimeout(hoverTimer)
    hoverTimer = null
  }
  
  leaveTimer = setTimeout(() => {
    visible.value = false
  }, 200)
}

const handleCardEnter = () => {
  if (leaveTimer) {
    clearTimeout(leaveTimer)
    leaveTimer = null
  }
}

const handleCardLeave = () => {
  leaveTimer = setTimeout(() => {
    visible.value = false
  }, 200)
}

const handleClick = () => {
  emit('click', props.userInfo)
}

const handleFollow = async () => {
  followLoading.value = true
  try {
    emit('follow', props.userInfo)
  } finally {
    followLoading.value = false
  }
}

const handleUnfollow = async () => {
  followLoading.value = true
  try {
    emit('unfollow', props.userInfo)
  } finally {
    followLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.user-hover-trigger {
  display: inline-block;
  cursor: pointer;
}

.user-hover-card {
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #f0f0f0;

  .user-header {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .user-info {
      flex: 1;
      min-width: 0;

      .user-name {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .creator-badge {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 2px 8px;
          background: linear-gradient(135deg, #fbbf24, #f59e0b);
          color: #fff;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
          flex-shrink: 0;

          .el-icon {
            font-size: 12px;
          }
        }
      }

      .user-bio {
        font-size: 14px;
        color: #6b7280;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .user-actions {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f3f4f6;
  }
}
</style>

<style lang="scss">
.user-hover-card-popper {
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}
</style> 