package pictures

// CreateAlbumRequest 创建专辑请求
type CreateAlbumRequest = PictureAlbumCreateRequest
type PictureAlbumCreateRequest struct {
	Title         string   `json:"title" validate:"required"`
	Description   string   `json:"description"`
	CoverURL      string   `json:"cover_url" validate:"required"`
	CategoryID    string   `json:"category_id"`
	CreatorID     string   `json:"creator_id" validate:"required"`
	CreatorName   string   `json:"creator_name"`
	CreatorAvatar string   `json:"creator_avatar"`
	IsPaid        bool     `json:"is_paid"`
	SortOrder     int      `json:"sort_order"`
	Price         float64  `json:"price"`
	Tags          []string `json:"tags"`
}

// UpdateAlbumRequest 更新专辑请求
type UpdateAlbumRequest = PictureAlbumUpdateRequest
type PictureAlbumUpdateRequest struct {
	ID           string   `json:"id" validate:"required"`
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	CoverURL     string   `json:"cover_url"`
	CategoryID   string   `json:"category_id"`
	CategoryName string   `json:"category_name"`
	IsPaid       *bool    `json:"is_paid"`
	SortOrder    *int     `json:"sort_order"`
	Price        *float64 `json:"price"`
	Status       *int8    `json:"status"`
	Tags         []string `json:"tags"`
}

// UpdateAlbumStatusRequest 更新专辑状态请求
type UpdateAlbumStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status" validate:"int|min:-5|max:5"`
}

// BatchUpdateAlbumStatusRequest 批量更新专辑状态请求
type BatchUpdateAlbumStatusRequest struct {
	IDs    []string `json:"ids" validate:"required"`
	Status int      `json:"status" validate:"int|min:-5|max:5"`
}

// BatchDeleteAlbumRequest 批量删除专辑请求
type BatchDeleteAlbumRequest struct {
	IDs []string `json:"ids" validate:"required"`
}
