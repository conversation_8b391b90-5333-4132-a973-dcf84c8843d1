/**
 * 多主题管理系统 - 基于PrimeVue和CSS变量
 * 支持Tailwind CSS集成
 */
import { computed, ref, watch } from 'vue';

// 默认主题名称
const DEFAULT_THEME = 'mysterious-light';

// 主题配置接口
export interface ThemeConfig {
    code: string;          // 用于i18n翻译的key
    name: string;          // 主题英文名
    displayName: string;   // 主题显示名
    shortName: string;     // 主题简写
    isDark?: boolean;      // 是否暗色主题
    primary?: string;      // 主色
    variables?: Record<string, string>; // CSS变量
}

// 主题管理器类
export class ThemeManager {
    // 可用主题列表
    private _themes: ThemeConfig[] = [];

    // 当前主题
    private _currentTheme = ref<string>(DEFAULT_THEME);

    // 是否跟随系统主题
    private _followSystem = ref<boolean>(false);

    // 是否为暗色模式
    private _isDark = ref<boolean>(false);

    // 系统是否为暗色模式
    private _systemIsDark = ref<boolean>(false);

    // 构造函数
    constructor() {
        // 监听系统主题变化
        this.initSystemThemeDetection();

        // 加载保存的主题设置
        this.loadSavedTheme();
    }

    // 加载保存的主题设置
    private loadSavedTheme(): void {
        // 读取本地存储的主题
        const savedTheme = localStorage.getItem('app-theme');
        if (savedTheme) {
            this._currentTheme.value = savedTheme;
        } else {
            // 如果没有保存主题，使用默认主题
            this._currentTheme.value = DEFAULT_THEME;
        }

        // 读取本地存储的跟随系统设置
        const followSystem = localStorage.getItem('app-theme-follow-system');
        if (followSystem !== null) {
            this._followSystem.value = followSystem === 'true';
        }

        console.log(`[Theme Manager] Loaded theme: ${this._currentTheme.value}, followSystem: ${this._followSystem.value}`);
    }

    // 初始化主题
    public init(): void {
        // 应用主题
        if (this._followSystem.value) {
            this.applySystemTheme();
        } else {
            this.applyTheme();
        }

        // 监听主题变化
        watch(() => this._currentTheme.value, () => {
            this.applyTheme();
            this.saveThemePreference();
        });

        // 监听系统主题跟随设置变化
        watch(() => this._followSystem.value, () => {
            if (this._followSystem.value) {
                this.applySystemTheme();
            }
            this.saveThemePreference();
        });

        // 监听系统主题变化
        watch(() => this._systemIsDark.value, () => {
            if (this._followSystem.value) {
                this.applySystemTheme();
            }
        });
    }

    // 注册主题
    public registerTheme(theme: ThemeConfig): void {
        const existingTheme = this._themes.find(t => t.name === theme.name);
        if (existingTheme) {
            // 如果主题已存在，更新主题
            const index = this._themes.indexOf(existingTheme);
            this._themes[index] = { ...existingTheme, ...theme };
        } else {
            // 添加新主题
            this._themes.push(theme);
        }
    }

    // 注册多个主题
    public registerThemes(themes: ThemeConfig[]): void {
        themes.forEach(theme => this.registerTheme(theme));
    }

    // 设置主题
    public setTheme(themeName: string): void {
        // 检查主题是否存在
        const theme = this._themes.find(t => t.name === themeName);
        if (!theme) {
            console.warn(`[Theme Manager] Theme "${themeName}" not found`);
            return;
        }

        // 设置主题
        this._currentTheme.value = themeName;
        this._isDark.value = theme.isDark || false;

        // 如果是跟随系统，关闭跟随系统
        if (this._followSystem.value) {
            this._followSystem.value = false;
        }

        console.log(`[Theme Manager] Theme set to: ${themeName}`);
    }

    // 应用主题
    private applyTheme(): void {
        // 查找当前主题
        let theme = this._themes.find(t => t.name === this._currentTheme.value);

        // 如果找不到当前主题，使用默认主题
        if (!theme && this._themes.length > 0) {
            theme = this._themes.find(t => t.name === DEFAULT_THEME) || this._themes[0];
            this._currentTheme.value = theme.name;
            console.warn(`[Theme Manager] Theme not found, using default: ${theme.name}`);
        }

        if (!theme) {
            console.error('[Theme Manager] No themes available');
            return;
        }

        // 更新CSS变量
        if (theme.variables) {
            Object.entries(theme.variables).forEach(([key, value]) => {
                document.documentElement.style.setProperty(`--${key}`, value);
            });
        }

        // 更新主题类
        document.body.classList.forEach(cls => {
            if (cls.startsWith('theme-')) {
                document.body.classList.remove(cls);
            }
        });
        document.body.classList.add(`theme-${theme.name}`);

        // 更新暗色/亮色模式
        if (theme.isDark) {
            document.documentElement.classList.add('dark');
            document.documentElement.classList.add('dark-theme');
            document.body.classList.add('dark-theme');
            document.documentElement.setAttribute('data-theme', 'dark');
            document.documentElement.setAttribute('data-theme-name', theme.name);
            this._isDark.value = true;
        } else {
            document.documentElement.classList.remove('dark');
            document.documentElement.classList.remove('dark-theme');
            document.body.classList.remove('dark-theme');
            document.documentElement.setAttribute('data-theme', 'light');
            document.documentElement.setAttribute('data-theme-name', theme.name);
            this._isDark.value = false;
        }

        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('themechange', { detail: { theme } }));
        window.dispatchEvent(new CustomEvent('theme-changed', { detail: { theme: theme.name, isDark: theme.isDark } }));

        console.log(`[Theme Manager] Applied theme: ${theme.name}, isDark: ${theme.isDark}`);
    }

    // 切换暗黑模式
    public toggleDarkMode(): void {
        // 查找当前主题的亮/暗对应版本
        const currentTheme = this._themes.find(t => t.name === this._currentTheme.value);
        if (!currentTheme) return;

        // 查找对应的亮/暗主题
        const targetTheme = this._themes.find(t =>
            t.code === currentTheme.code && t.isDark !== currentTheme.isDark
        );

        if (targetTheme) {
            this.setTheme(targetTheme.name);
        } else {
            // 如果没有找到对应的亮/暗主题，则切换到默认主题的对应版本
            const defaultTheme = this._isDark.value ? 'mysterious-light' : 'mysterious-dark';
            this.setTheme(defaultTheme);
        }
    }

    // 启用系统主题
    public enableSystemTheme(): void {
        this._followSystem.value = true;
        this.applySystemTheme();
        this.saveThemePreference();
    }

    // 禁用系统主题
    public disableSystemTheme(): void {
        this._followSystem.value = false;
        this.saveThemePreference();
    }

    // 应用系统主题
    private applySystemTheme(): void {
        const prefersDark = this._systemIsDark.value;

        // 查找适合的亮/暗主题
        let theme: ThemeConfig | undefined;

        // 尝试保持当前主题的同类型
        const currentTheme = this._themes.find(t => t.name === this._currentTheme.value);

        if (currentTheme) {
            // 查找相同代码但暗/亮不同的主题
            theme = this._themes.find(t =>
                t.code === currentTheme.code && t.isDark === prefersDark
            );
        }

        // 如果没有找到对应主题，使用默认主题
        if (!theme) {
            theme = this._themes.find(t => t.isDark === prefersDark);
        }

        // 如果找到了合适的主题，应用它
        if (theme) {
            this._currentTheme.value = theme.name;
        }
    }

    // 初始化系统主题检测
    private initSystemThemeDetection(): void {
        // 检查系统是否支持暗色模式
        const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

        // 设置初始值
        this._systemIsDark.value = darkModeMediaQuery.matches;

        // 监听系统主题变化
        darkModeMediaQuery.addEventListener('change', e => {
            this._systemIsDark.value = e.matches;
            console.log(`[Theme Manager] System theme changed to ${e.matches ? 'dark' : 'light'}`);
        });
    }

    // 保存主题偏好
    private saveThemePreference(): void {
        localStorage.setItem('app-theme', this._currentTheme.value);
        localStorage.setItem('app-theme-follow-system', String(this._followSystem.value));
        console.log(`[Theme Manager] Saved preferences - theme: ${this._currentTheme.value}, followSystem: ${this._followSystem.value}`);
    }

    // 获取主题显示名称
    public getThemeDisplayName(themeName: string): string {
        const theme = this._themes.find(t => t.name === themeName);
        return theme ? `theme.${theme.code}` : themeName;
    }

    // 导出属性
    get currentTheme() {
        return computed(() => this._currentTheme.value);
    }

    get followSystem() {
        return computed(() => this._followSystem.value);
    }

    get isDark() {
        return computed(() => this._isDark.value);
    }

    get systemIsDark() {
        return computed(() => this._systemIsDark.value);
    }

    get availableThemes() {
        return computed(() => this._themes);
    }
}

// 创建主题管理器实例
export const themeManager = new ThemeManager();

// 导出默认主题管理器
export default themeManager;