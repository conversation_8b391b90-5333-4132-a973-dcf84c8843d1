<template>
  <el-dialog
    v-if="videoData"
    :model-value="visible"
    :title="'审核视频: ' + (videoData?.title || '')"
    width="650px"
    @update:model-value="handleVisibleChange"
    destroy-on-close
    class="video-approval-dialog"
  >
    <div class="approval-content">
      <!-- 视频预览区 -->
      <div class="video-preview">
        <el-card shadow="never" class="preview-card">
          <div class="video-thumbnail">
            <el-image 
              :src="videoData?.cover || ''" 
              fit="cover" 
              class="thumbnail-image"
              :preview-src-list="videoData?.cover ? [videoData.cover] : []"
            />
            <div class="video-info-overlay">
              <div class="video-duration">{{ formatDuration(videoData?.duration || 0) }}</div>
            </div>
          </div>
          <div class="video-basic-info">
            <h3 class="video-title">{{ videoData?.title }}</h3>
            <div class="video-meta">
              <span class="video-category" v-if="videoData?.category_name">
                <el-tag size="small" effect="plain">{{ videoData.category_name }}</el-tag>
              </span>
              <span class="video-uploader" v-if="videoData?.creator_name">
                <el-icon><User /></el-icon> {{ videoData.creator_name }}
              </span>
              <span class="video-time">
                <el-icon><Calendar /></el-icon> {{ formatDate(videoData?.created_at || '') }}
              </span>
            </div>
          </div>
        </el-card>
        
        <el-tabs class="video-tabs" v-model="activeTab">
          <el-tab-pane label="视频信息" name="info">
            <el-descriptions :column="1" border size="small">
              <el-descriptions-item label="视频ID">{{ videoData.id }}</el-descriptions-item>
              <el-descriptions-item label="视频描述">{{ videoData.description || '无描述' }}</el-descriptions-item>
              <el-descriptions-item label="标签">
                <div class="tags-wrapper">
                  <el-tag 
                    v-for="tag in videoData.tags" 
                    :key="tag" 
                    size="small" 
                    effect="plain"
                    class="tag-item"
                  >{{ tag }}</el-tag>
                  <span v-if="!videoData.tags || videoData.tags.length === 0" class="no-data">无标签</span>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="视频预览" name="preview" v-if="videoData.url">
            <div class="video-player-container">
              <video 
                :src="videoData.url" 
                controls 
                class="video-player"
                @loadedmetadata="onVideoLoaded"
                ref="videoRef"
              ></video>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 审核表单区 -->
      <div class="approval-form">
        <h3 class="form-title">审核结果</h3>
        
        <el-form :model="form" label-width="80px" class="approval-form-content">
          <el-form-item label="审核结果">
            <el-radio-group v-model="form.status">
              <el-radio :value="2">
                <el-icon class="status-icon approve"><CircleCheckFilled /></el-icon>
                通过
              </el-radio>
              <el-radio :value="-2">
                <el-icon class="status-icon reject"><CircleCloseFilled /></el-icon>
                不通过
              </el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            v-if="form.status === -2"
            label="拒绝原因" 
            :rules="[{ required: true, message: '请填写拒绝原因', trigger: 'blur' }]"
          >
            <el-input 
              v-model="form.reason" 
              type="textarea" 
              placeholder="请输入拒绝理由，将通知给内容提供者" 
              :rows="4"
            />
            <div class="reason-templates" v-if="reasonTemplates.length > 0">
              <span class="reason-label">快速选择:</span>
              <el-button 
                v-for="(template, index) in reasonTemplates" 
                :key="index"
                type="primary"
                size="small"
                link
                @click="applyReasonTemplate(template)"
              >{{ template.label }}</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleVisibleChange(false)" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ form.status === 2 ? '通过' : '拒绝' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reviewVideo } from '@/service/api/videos/videos';
import { VideoItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import { handleApiError } from '@/utils/errorHandler';
import { Calendar, CircleCheckFilled, CircleCloseFilled, User } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref, watch } from 'vue';

interface Props {
  visible: boolean;
  videoData?: VideoItem;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  videoData: undefined
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

const loading = ref(false);
const activeTab = ref('info');
const videoRef = ref<HTMLVideoElement | null>(null);
const videoWidth = ref(0);
const videoHeight = ref(0);

const form = ref({
  status: 2,  // 默认为通过
  reason: ''
});

// 拒绝原因模板
const reasonTemplates = [
  { label: '违规内容', value: '视频包含违规内容，无法通过审核。' },
  { label: '低质量', value: '视频质量不达标，建议提高分辨率或清晰度后重新提交。' },
  { label: '音质问题', value: '视频音质存在问题，请修复后重新提交。' },
  { label: '封面不当', value: '视频封面不符合平台规范，请更换后重新提交。' },
  { label: '标题不规范', value: '视频标题不符合平台规范，请修改后重新提交。' }
];

// 应用拒绝原因模板
const applyReasonTemplate = (template: { label: string; value: string }) => {
  form.value.reason = template.value;
};

// 处理可见性变更
const handleVisibleChange = (val: boolean) => {
  emit('update:visible', val);
};

// 视频加载完成后获取尺寸
const onVideoLoaded = (e: Event) => {
  const video = e.target as HTMLVideoElement;
  if (video) {
    videoWidth.value = video.videoWidth;
    videoHeight.value = video.videoHeight;
  }
};

// 格式化视频时长
const formatDuration = (seconds: number = 0) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;

  return [
    h > 0 ? h.toString().padStart(2, '0') : '',
    m.toString().padStart(2, '0'),
    s.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
};

// 重置表单
watch(() => props.visible, (val) => {
  if (val) {
    form.value = {
      status: 2,
      reason: ''
    };
    activeTab.value = 'info';
  }
});

// 处理提交
const handleConfirm = async () => {
  if (!props.videoData) return;
  
  if (form.value.status === -2 && !form.value.reason) {
    ElMessage.error('请填写拒绝原因');
    return;
  }
  
  loading.value = true;
  try {
    const result = await reviewVideo(props.videoData.id, {
      status: form.value.status,
      reason: form.value.reason
    });
    
    if (result) {
      ElMessage.success(form.value.status === 2 ? '审核通过成功' : '审核拒绝成功');
      emit('update:visible', false);
      emit('success');
    }
  } catch (error) {
    handleApiError(error, '审核视频');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped lang="scss">
.video-approval-dialog {
  .approval-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    .video-preview {
      .preview-card {
        margin-bottom: 12px;
        
        .video-thumbnail {
          position: relative;
          height: 180px;
          overflow: hidden;
          border-radius: 4px;
          
          .thumbnail-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .video-info-overlay {
            position: absolute;
            bottom: 8px;
            right: 8px;
            
            .video-duration {
              background-color: rgba(0, 0, 0, 0.7);
              color: white;
              padding: 2px 6px;
              border-radius: 2px;
              font-size: 12px;
            }
          }
        }
        
        .video-basic-info {
          margin-top: 8px;
          
          .video-title {
            margin: 0 0 4px 0;
            font-size: 16px;
            line-height: 1.4;
            font-weight: 500;
          }
          
          .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            font-size: 13px;
            color: #606266;
            
            .video-uploader,
            .video-time {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }
      }
      
      .video-tabs {
        .video-player-container {
          background: #000;
          border-radius: 4px;
          overflow: hidden;
          text-align: center;
          
          .video-player {
            max-width: 100%;
            max-height: 300px;
          }
        }
        
        .tags-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
          
          .tag-item {
            margin-bottom: 4px;
          }
          
          .no-data {
            color: #909399;
            font-style: italic;
          }
        }
      }
    }
    
    .approval-form {
      background-color: #f8f9fa;
      border-radius: 4px;
      padding: 12px;
      
      .form-title {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 500;
        color: #303133;
      }
      
      .approval-form-content {
        .status-icon {
          margin-right: 4px;
          
          &.approve {
            color: #67c23a;
          }
          
          &.reject {
            color: #f56c6c;
          }
        }
        
        .reason-templates {
          margin-top: 8px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 8px;
          
          .reason-label {
            color: #606266;
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style> 