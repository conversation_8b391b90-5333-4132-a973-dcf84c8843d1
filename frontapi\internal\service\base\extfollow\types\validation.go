package types

import (
	"time"
)

// ValidateBatchOperations 验证批量操作
func ValidateBatchOperations(operations []*FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	for i, op := range operations {
		if op.FollowerID == "" {
			return ErrInvalidFollowerID
		}
		if op.FollowedID == "" {
			return ErrInvalidFolloweeID
		}
		if op.FollowerID == op.FollowedID {
			return ErrSelfFollow
		}
		// 设置默认操作时间
		if op.Created.IsZero() {
			operations[i].Created = time.Now()
		}
	}

	return nil
}
