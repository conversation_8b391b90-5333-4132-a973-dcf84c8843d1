<template>
  <nav class="app-navigation">
    <div class="nav-container">
      <!-- 桌面端导航 -->
      <div class="desktop-nav">
        <Menubar :model="menuItems" class="main-menubar">
          <template #item="{ item, props, hasSubmenu }">
            <router-link 
              v-if="item.route" 
              :to="item.route" 
              class="nav-link"
              :class="{ 'active': isActiveRoute(item.route) }"
            >
              <span class="nav-icon">
                <i :class="item.icon"></i>
              </span>
              <span class="nav-label">{{ t(item.label as string) }}</span>
            </router-link>
            <a 
              v-else 
              v-bind="props.action" 
              class="nav-link"
              :class="{ 'has-submenu': hasSubmenu }"
            >
              <span class="nav-icon">
                <i :class="item.icon"></i>
              </span>
              <span class="nav-label">{{ t(item.label as string) }}</span>
              <i v-if="hasSubmenu" class="pi pi-angle-down submenu-arrow"></i>
            </a>
          </template>
        </Menubar>
      </div>

    </div>
  </nav>
</template>

<script setup lang="ts">
import { MenuItem, menuItems } from '@/config/menu.config'
import { useTranslation } from '@/core/plugins/i18n/composables'
import Menubar from 'primevue/menubar'
import { defineEmits, defineProps, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// Props
interface Props {
  isMobile?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false
})

// Emits
const emit = defineEmits<{
  itemClick: [item: MenuItem]
}>()

// 组合式API
const router = useRouter()
const route = useRoute()
const { t } = useTranslation()
// 响应式数据
const isMobile = ref(false)
const showMobileMenu = ref(false)
const searchQuery = ref('')
const expandedMobileItem = ref<string | null>(null)

// 导航菜单配置


// 方法
const isActiveRoute = (routePath: string) => {
  return route.path === routePath || route.path.startsWith(routePath + '/')
}

const handleMobileMenuClick = (item: MenuItem) => {
  if (item.items) {
    // 切换子菜单展开状态
    expandedMobileItem.value = expandedMobileItem.value === item.label ? null : item.label
  } else if (item.route) {
    // 导航到路由
    router.push(item.route)
    showMobileMenu.value = false
    emit('itemClick', item)
  }
}

const handleMobileSubMenuClick = (subItem: MenuItem) => {
  if (subItem.route) {
    router.push(subItem.route)
    showMobileMenu.value = false
    emit('itemClick', subItem)    
  }
}

const handleMobileSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      name: 'search',
      query: { q: searchQuery.value.trim() }
    })
    showMobileMenu.value = false
  }
}

// 响应式检测
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    showMobileMenu.value = false
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 暴露给父组件
defineExpose({
  toggleMobileMenu: () => {
    showMobileMenu.value = !showMobileMenu.value
  }
})
</script>

<style scoped lang="scss">
.app-navigation {
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  border-top: 1px solid var(--surface-border);
  position: relative;
  top: 84px;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  @media (max-width: 960px) {
    display: none;
  }
  
  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    
    @media (max-width: 768px) {
      padding: 0;
    }
  }
}

.desktop-nav {
  @media (max-width: 768px) {
    display: none;
  }
  
  :deep(.main-menubar) {
    background: transparent;
    border: none;
    padding: 0;
    
    .p-menubar-root-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1rem;
      height: 4.5rem;
      margin: 0;
      padding: 0;
      list-style: none;
      overflow-x: auto;
      scrollbar-width: none;
      
      &::-webkit-scrollbar {
        display: none;
      }
      .p-menubar-item{
        height: 100%;
        .p-menubar-item-content{
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0;
            padding: 0.2rem 0.8rem;
            .nav-link{
                display: flex;
                gap: 0.5rem;
                align-items: center;
                .nav-icon{
                    font-size: 1.2rem;
                }
                .nav-label{
                    font-size: 1.2rem;
                }
            }
        }
      }
    }
    
    .p-submenu-list {
      background: var(--surface-card);
      border: 1px solid var(--surface-border);
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
      padding: 0.5rem;
      min-width: 220px;
      margin-top: 0.5rem;
      z-index: 1000;
      
      .p-menuitem {
        .p-menuitem-link {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1rem;
          text-decoration: none;
          color: var(--text-color);
          transition: all 0.2s ease;
          border-radius: 8px;
          margin: 0.25rem 0;
          
          &:hover {
            background: var(--surface-hover);
            color: var(--primary-color);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            font-weight: 500;
          }
          
          .submenu-icon {
            font-size: 0.9rem;
            width: 16px;
            text-align: center;
            
            i {
              color: inherit;
            }
          }
          
          .submenu-label {
            font-weight: 400;
            font-size: 0.875rem;
          }
        }
      }
    }
  }
}


/* 深色主题适配 */
:global(.sof-dark) {
  .app-navigation {
    background: var(--surface-800);
    border-bottom-color: var(--surface-700);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .desktop-nav :deep(.main-menubar) {
    .p-menubar-root-list > .p-menuitem .nav-link {
      &:hover {
        background: var(--surface-700);
      }
      
      &.active {
        background: rgba(var(--primary-700-rgb), 0.2);
      }
    }
    
    .p-submenu-list {
      background: var(--surface-800);
      border-color: var(--surface-700);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
      
      .p-menuitem .p-menuitem-link:hover {
        background: var(--surface-700);
      }
    }
  }
}

/* 动画效果 */
.mobile-submenu {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .desktop-nav :deep(.main-menubar) {
    .p-menubar-root-list > .p-menuitem .nav-link {
      padding: 0.75rem 0.6rem;
      
      .nav-label {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .mobile-nav :deep(.mobile-sidebar) {
    width: 100vw;
  }
}
</style>