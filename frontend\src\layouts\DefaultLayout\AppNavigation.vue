<template>
  <div class="app-navigation bg-gradient-to-b from-surface-0 to-surface-50 to-surface-100 dark:bg-gradient-to-b dark:from-surface-600 dark:to-surface-700 dark:to-surface-800 border-t border-surface-200 dark:border-surface-700 text-surface-900 dark:text-surface-50">
    <div class="nav-container">
      <!-- 桌面端导航 -->
      <div class="desktop-nav">
        <ul class="menu-list">
            <li v-for="item in menuItems" :key="item.label" class="menu-item-wrapper">
            <template v-if="item.items">
              <n-popover placement="bottom" trigger="click" class="menu-item" to="body">
                <template #trigger>
                  <div class="menu-item">
                    <i :class="item.icon" class="menu-icon"></i>
                    <span class="menu-label">{{ t(item.label) }}</span>
                    <i class="pi pi-angle-down submenu-arrow" v-if="item.items"></i>
                  </div>
                </template>
                <div class="submenu-container">
                  <ul class="menu-sub-list">
                    <li v-for="subItem in item.items" :key="subItem.label">
                      <router-link
                        :to="subItem.route!"
                        class="menu-item"
                      >
                        <i :class="subItem.icon" class="menu-icon"></i>
                        <span class="menu-label">{{ t(subItem.label) }}</span>
                      </router-link>
                    </li>
                  </ul>
                </div>
              </n-popover>
            </template>
            <template v-else>
              <router-link
                :to="item.route!"
                class="menu-item"
              >
                <i :class="item.icon" class="menu-icon"></i>
                <span class="menu-label">{{ t(item.label) }}</span>
              </router-link>
            </template>
          </li>
        </ul>
      </div>

    </div>
</div>
</template>

<script setup lang="ts">
import { MenuItem, menuItems } from '@/config/menu.config'
import { useTranslation } from '@/core/plugins/i18n/composables'
import { defineEmits, defineProps, onMounted, onUnmounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// Props
interface Props {
  isMobile?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isMobile: false
})

// Emits
const emit = defineEmits<{
  itemClick: [item: MenuItem]
}>()

// 组合式API
const router = useRouter()
const route = useRoute()
const { t } = useTranslation()
// 响应式数据
const isMobile = ref(false)
const showMobileMenu = ref(false)
const searchQuery = ref('')
const expandedMobileItem = ref<string | null>(null)

// 导航菜单配置


// 方法
const isActiveRoute = (routePath: string) => {
  return route.path === routePath || route.path.startsWith(routePath + '/')
}

const handleMobileMenuClick = (item: MenuItem) => {
  if (item.items) {
    // 切换子菜单展开状态
    expandedMobileItem.value = expandedMobileItem.value === item.label ? null : item.label
  } else if (item.route) {
    // 导航到路由
    router.push(item.route)
    showMobileMenu.value = false
    emit('itemClick', item)
  }
}

const handleMobileSubMenuClick = (subItem: MenuItem) => {
  if (subItem.route) {
    router.push(subItem.route)
    showMobileMenu.value = false
    emit('itemClick', subItem)    
  }
}

const handleMobileSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      name: 'search',
      query: { q: searchQuery.value.trim() }
    })
    showMobileMenu.value = false
  }
}

// 响应式检测
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    showMobileMenu.value = false
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

// 暴露给父组件
defineExpose({
  toggleMobileMenu: () => {
    showMobileMenu.value = !showMobileMenu.value
  }
})
</script>

<style scoped lang="scss">
.app-navigation {
  border-bottom: 1px solid var(--surface-border);
  border-top: 1px solid var(--surface-border);
  position: relative;
  top: 84px;
  z-index: 999;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  @media (max-width: 960px) {
    display: none;
  }
  
  .nav-container {
    max-width: var(--main-width);
    margin: 0 auto;
    padding: 0 1rem;
    
    @media (max-width: 768px) {
      padding: 0;
    }
    .desktop-nav {
        @media (max-width: 768px) {
            display: none;
        }
        .menu-list{
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            gap: 1rem;
            list-style: none;
            padding: 0;
            margin: 0;
            .menu-item-wrapper{
                height: 100%;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                align-items: center;
                list-style: none;
                gap: 0.3rem;
                margin: 0;
                .menu-item{
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 1.2rem 0.5rem;
                    cursor: pointer;
                    &:hover{
                        background-color: var(--surface-100);
                        color: var(--primary-color);
                    }
                }
            }
        }
      
    }
  }
}




/* 深色主题适配 */


/* 动画效果 */
.mobile-submenu {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .desktop-nav :deep(.main-menubar) {
    .p-menubar-root-list > .p-menuitem .nav-link {
      padding: 0.75rem 0.6rem;
      
      .nav-label {
        font-size: 0.9rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .mobile-nav :deep(.mobile-sidebar) {
    width: 100vw;
  }
}
</style>