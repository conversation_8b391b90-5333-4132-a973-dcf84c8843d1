package posts

import (
	"frontapi/internal/models"
	users "frontapi/internal/models/users"
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"
)

// Comment 帖子评论模型
type PostComment struct {
	models.BaseModelStruct
	Content      string            `json:"content" gorm:"type:text;comment:评论内容"`
	Images       types.StringArray `json:"images" gorm:"type:json;comment:图片"`
	Video        null.String       `json:"Video" gorm:"type:text;comment:视频"`
	UserID       null.String       `json:"user_id" gorm:"type:string;size:36;comment:作者ID"`
	UserNickname null.String       `json:"user_nickname" gorm:"type:varchar;size:50;comment:用户昵称"`
	UserAvatar   null.String       `json:"user_avatar" gorm:"type:varchar;size:1024;comment:用户头像"`
	ReplyToID    null.String       `json:"reply_to_id" gorm:"type:string;size:36;comment:回复用户ID"`
	ReplyToUser  null.String       `json:"reply_to_user" gorm:"type:varchar;size:50;comment:回复的用户名字"`
	UserType     int               `json:"user_type" gorm:"type:tinyint;comment:1普通用户，2明星"`
	ParentID     null.String       `json:"parent_id" gorm:"type:string;size:36;comment:父评论ID"`
	PostID       null.String       `json:"post_id" gorm:"type:string;size:36;comment:关联实体ID"`
	Heat         int64             `json:"heat" gorm:"type:bigint;comment:热度"`
	LikeCount    int64             `json:"like_count" gorm:"type:bigint;comment:点赞数"`
	ReplyCount   int64             `json:"reply_count" gorm:"type:bigint;comment:回复数"`
	AuditTime    types.JSONTime    `json:"audit_time" gorm:"type:datetime;comment:审核时间"`
	AuditorID    int               `json:"auditor_id" gorm:"type:int;comment:审核人ID"`
	Reason       string            `json:"reason" gorm:"type:varchar;size:255;comment:拒绝原因"`
	Status       int               `json:"status" gorm:"type:tinyint;comment:0=草稿, 1=待审核, 2=审核通过/显示, -2=审核拒绝, -4=已删除"`
	IsLiked      bool              `json:"is_liked" gorm:"-"`
	Author       *users.User       `json:"author" gorm:"-"`
}

// TableName 指定表名
func (PostComment) TableName() string {
	return "ly_post_comments"
}
