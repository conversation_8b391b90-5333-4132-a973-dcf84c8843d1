/**
 * 日期时间工具函数
 */

/**
 * 日期格式化选项
 */
export interface DateFormatOptions {
  year?: 'numeric' | '2-digit'
  month?: 'numeric' | '2-digit' | 'long' | 'short' | 'narrow'
  day?: 'numeric' | '2-digit'
  hour?: 'numeric' | '2-digit'
  minute?: 'numeric' | '2-digit'
  second?: 'numeric' | '2-digit'
  weekday?: 'long' | 'short' | 'narrow'
  timeZone?: string
  locale?: string
}

/**
 * 时间单位
 */
export type TimeUnit = 'year' | 'month' | 'week' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond'

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式字符串
 * @returns 格式化后的字符串
 */
export function formatDate(date: Date | string | number, format = 'YYYY-MM-DD HH:mm:ss'): string {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return 'Invalid Date'
  }
  
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hour = d.getHours()
  const minute = d.getMinutes()
  const second = d.getSeconds()
  const millisecond = d.getMilliseconds()
  
  const formatMap: Record<string, string> = {
    'YYYY': year.toString(),
    'YY': year.toString().slice(-2),
    'MM': month.toString().padStart(2, '0'),
    'M': month.toString(),
    'DD': day.toString().padStart(2, '0'),
    'D': day.toString(),
    'HH': hour.toString().padStart(2, '0'),
    'H': hour.toString(),
    'mm': minute.toString().padStart(2, '0'),
    'm': minute.toString(),
    'ss': second.toString().padStart(2, '0'),
    's': second.toString(),
    'SSS': millisecond.toString().padStart(3, '0'),
    'S': millisecond.toString()
  }
  
  let result = format
  Object.keys(formatMap).forEach(key => {
    result = result.replace(new RegExp(key, 'g'), formatMap[key])
  })
  
  return result
}

/**
 * 解析日期字符串
 * @param dateString 日期字符串
 * @param format 格式字符串
 * @returns 日期对象
 */
export function parseDate(dateString: string, format = 'YYYY-MM-DD HH:mm:ss'): Date {
  // 简单的解析实现，可以根据需要扩展
  const date = new Date(dateString)
  return isNaN(date.getTime()) ? new Date() : date
}

/**
 * 获取相对时间
 * @param date 日期
 * @param baseDate 基准日期
 * @param locale 语言环境
 * @returns 相对时间字符串
 */
export function getRelativeTime(
  date: Date | string | number,
  baseDate: Date | string | number = new Date(),
  locale = 'zh-CN'
): string {
  const d = new Date(date)
  const base = new Date(baseDate)
  
  if (isNaN(d.getTime()) || isNaN(base.getTime())) {
    return 'Invalid Date'
  }
  
  const diff = base.getTime() - d.getTime()
  const absDiff = Math.abs(diff)
  const isPast = diff > 0
  
  const units = [
    { unit: 'year', ms: 365 * 24 * 60 * 60 * 1000 },
    { unit: 'month', ms: 30 * 24 * 60 * 60 * 1000 },
    { unit: 'week', ms: 7 * 24 * 60 * 60 * 1000 },
    { unit: 'day', ms: 24 * 60 * 60 * 1000 },
    { unit: 'hour', ms: 60 * 60 * 1000 },
    { unit: 'minute', ms: 60 * 1000 },
    { unit: 'second', ms: 1000 }
  ]
  
  for (const { unit, ms } of units) {
    const value = Math.floor(absDiff / ms)
    if (value >= 1) {
      const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
      return rtf.format(isPast ? -value : value, unit as Intl.RelativeTimeFormatUnit)
    }
  }
  
  return locale.startsWith('zh') ? '刚刚' : 'just now'
}

/**
 * 获取时间差
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param unit 单位
 * @returns 时间差
 */
export function getDiff(
  startDate: Date | string | number,
  endDate: Date | string | number,
  unit: TimeUnit = 'millisecond'
): number {
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 0
  }
  
  const diff = end.getTime() - start.getTime()
  
  switch (unit) {
    case 'year':
      return diff / (365 * 24 * 60 * 60 * 1000)
    case 'month':
      return diff / (30 * 24 * 60 * 60 * 1000)
    case 'week':
      return diff / (7 * 24 * 60 * 60 * 1000)
    case 'day':
      return diff / (24 * 60 * 60 * 1000)
    case 'hour':
      return diff / (60 * 60 * 1000)
    case 'minute':
      return diff / (60 * 1000)
    case 'second':
      return diff / 1000
    default:
      return diff
  }
}

/**
 * 添加时间
 * @param date 日期
 * @param amount 数量
 * @param unit 单位
 * @returns 新的日期
 */
export function addTime(
  date: Date | string | number,
  amount: number,
  unit: TimeUnit
): Date {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return new Date()
  }
  
  switch (unit) {
    case 'year':
      d.setFullYear(d.getFullYear() + amount)
      break
    case 'month':
      d.setMonth(d.getMonth() + amount)
      break
    case 'week':
      d.setDate(d.getDate() + amount * 7)
      break
    case 'day':
      d.setDate(d.getDate() + amount)
      break
    case 'hour':
      d.setHours(d.getHours() + amount)
      break
    case 'minute':
      d.setMinutes(d.getMinutes() + amount)
      break
    case 'second':
      d.setSeconds(d.getSeconds() + amount)
      break
    case 'millisecond':
      d.setMilliseconds(d.getMilliseconds() + amount)
      break
  }
  
  return d
}

/**
 * 减去时间
 * @param date 日期
 * @param amount 数量
 * @param unit 单位
 * @returns 新的日期
 */
export function subtractTime(
  date: Date | string | number,
  amount: number,
  unit: TimeUnit
): Date {
  return addTime(date, -amount, unit)
}

/**
 * 获取时间的开始
 * @param date 日期
 * @param unit 单位
 * @returns 开始时间
 */
export function startOf(date: Date | string | number, unit: TimeUnit): Date {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return new Date()
  }
  
  switch (unit) {
    case 'year':
      d.setMonth(0, 1)
      d.setHours(0, 0, 0, 0)
      break
    case 'month':
      d.setDate(1)
      d.setHours(0, 0, 0, 0)
      break
    case 'week':
      const day = d.getDay()
      d.setDate(d.getDate() - day)
      d.setHours(0, 0, 0, 0)
      break
    case 'day':
      d.setHours(0, 0, 0, 0)
      break
    case 'hour':
      d.setMinutes(0, 0, 0)
      break
    case 'minute':
      d.setSeconds(0, 0)
      break
    case 'second':
      d.setMilliseconds(0)
      break
  }
  
  return d
}

/**
 * 获取时间的结束
 * @param date 日期
 * @param unit 单位
 * @returns 结束时间
 */
export function endOf(date: Date | string | number, unit: TimeUnit): Date {
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return new Date()
  }
  
  switch (unit) {
    case 'year':
      d.setMonth(11, 31)
      d.setHours(23, 59, 59, 999)
      break
    case 'month':
      d.setMonth(d.getMonth() + 1, 0)
      d.setHours(23, 59, 59, 999)
      break
    case 'week':
      const day = d.getDay()
      d.setDate(d.getDate() + (6 - day))
      d.setHours(23, 59, 59, 999)
      break
    case 'day':
      d.setHours(23, 59, 59, 999)
      break
    case 'hour':
      d.setMinutes(59, 59, 999)
      break
    case 'minute':
      d.setSeconds(59, 999)
      break
    case 'second':
      d.setMilliseconds(999)
      break
  }
  
  return d
}

/**
 * 检查是否为同一时间
 * @param date1 日期1
 * @param date2 日期2
 * @param unit 单位
 * @returns 是否相同
 */
export function isSame(
  date1: Date | string | number,
  date2: Date | string | number,
  unit: TimeUnit = 'millisecond'
): boolean {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return false
  }
  
  const start1 = startOf(d1, unit)
  const start2 = startOf(d2, unit)
  
  return start1.getTime() === start2.getTime()
}

/**
 * 检查是否在之前
 * @param date1 日期1
 * @param date2 日期2
 * @param unit 单位
 * @returns 是否在之前
 */
export function isBefore(
  date1: Date | string | number,
  date2: Date | string | number,
  unit: TimeUnit = 'millisecond'
): boolean {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return false
  }
  
  if (unit === 'millisecond') {
    return d1.getTime() < d2.getTime()
  }
  
  const start1 = startOf(d1, unit)
  const start2 = startOf(d2, unit)
  
  return start1.getTime() < start2.getTime()
}

/**
 * 检查是否在之后
 * @param date1 日期1
 * @param date2 日期2
 * @param unit 单位
 * @returns 是否在之后
 */
export function isAfter(
  date1: Date | string | number,
  date2: Date | string | number,
  unit: TimeUnit = 'millisecond'
): boolean {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return false
  }
  
  if (unit === 'millisecond') {
    return d1.getTime() > d2.getTime()
  }
  
  const start1 = startOf(d1, unit)
  const start2 = startOf(d2, unit)
  
  return start1.getTime() > start2.getTime()
}

/**
 * 检查是否在范围内
 * @param date 日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param unit 单位
 * @param inclusivity 包含性
 * @returns 是否在范围内
 */
export function isBetween(
  date: Date | string | number,
  startDate: Date | string | number,
  endDate: Date | string | number,
  unit: TimeUnit = 'millisecond',
  inclusivity = '()'
): boolean {
  const d = new Date(date)
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (isNaN(d.getTime()) || isNaN(start.getTime()) || isNaN(end.getTime())) {
    return false
  }
  
  const includeStart = inclusivity.includes('[')
  const includeEnd = inclusivity.includes(']')
  
  const afterStart = includeStart
    ? !isBefore(d, start, unit)
    : isAfter(d, start, unit)
  
  const beforeEnd = includeEnd
    ? !isAfter(d, end, unit)
    : isBefore(d, end, unit)
  
  return afterStart && beforeEnd
}

/**
 * 获取时区偏移
 * @param date 日期
 * @returns 时区偏移（分钟）
 */
export function getTimezoneOffset(date: Date | string | number = new Date()): number {
  const d = new Date(date)
  return isNaN(d.getTime()) ? 0 : d.getTimezoneOffset()
}

/**
 * 转换为UTC时间
 * @param date 日期
 * @returns UTC时间
 */
export function toUTC(date: Date | string | number): Date {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return new Date()
  }
  
  return new Date(d.getTime() + d.getTimezoneOffset() * 60000)
}

/**
 * 从UTC时间转换
 * @param date 日期
 * @returns 本地时间
 */
export function fromUTC(date: Date | string | number): Date {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return new Date()
  }
  
  return new Date(d.getTime() - d.getTimezoneOffset() * 60000)
}

/**
 * 获取月份的天数
 * @param year 年份
 * @param month 月份（0-11）
 * @returns 天数
 */
export function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month + 1, 0).getDate()
}

/**
 * 获取年份的天数
 * @param year 年份
 * @returns 天数
 */
export function getDaysInYear(year: number): number {
  return isLeapYear(year) ? 366 : 365
}

/**
 * 检查是否为闰年
 * @param year 年份
 * @returns 是否为闰年
 */
export function isLeapYear(year: number): boolean {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0
}

/**
 * 获取星期几
 * @param date 日期
 * @param locale 语言环境
 * @returns 星期几
 */
export function getWeekday(date: Date | string | number, locale = 'zh-CN'): string {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return ''
  }
  
  return d.toLocaleDateString(locale, { weekday: 'long' })
}

/**
 * 获取月份名称
 * @param date 日期
 * @param locale 语言环境
 * @returns 月份名称
 */
export function getMonthName(date: Date | string | number, locale = 'zh-CN'): string {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return ''
  }
  
  return d.toLocaleDateString(locale, { month: 'long' })
}

/**
 * 获取季度
 * @param date 日期
 * @returns 季度（1-4）
 */
export function getQuarter(date: Date | string | number): number {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return 1
  }
  
  return Math.ceil((d.getMonth() + 1) / 3)
}

/**
 * 获取周数
 * @param date 日期
 * @returns 周数
 */
export function getWeekOfYear(date: Date | string | number): number {
  const d = new Date(date)
  if (isNaN(d.getTime())) {
    return 1
  }
  
  const start = startOf(d, 'year')
  const diff = getDiff(start, d, 'day')
  return Math.ceil((diff + start.getDay() + 1) / 7)
}

/**
 * 格式化持续时间
 * @param milliseconds 毫秒数
 * @param format 格式
 * @returns 格式化后的字符串
 */
export function formatDuration(
  milliseconds: number,
  format: 'short' | 'long' | 'precise' = 'short'
): string {
  if (milliseconds < 0) {
    return '0'
  }
  
  const units = [
    { name: 'day', ms: 24 * 60 * 60 * 1000, short: 'd', long: '天' },
    { name: 'hour', ms: 60 * 60 * 1000, short: 'h', long: '小时' },
    { name: 'minute', ms: 60 * 1000, short: 'm', long: '分钟' },
    { name: 'second', ms: 1000, short: 's', long: '秒' }
  ]
  
  const parts: string[] = []
  let remaining = milliseconds
  
  for (const unit of units) {
    const value = Math.floor(remaining / unit.ms)
    if (value > 0) {
      const suffix = format === 'short' ? unit.short : unit.long
      parts.push(`${value}${suffix}`)
      remaining -= value * unit.ms
    }
    
    if (format !== 'precise' && parts.length >= 2) {
      break
    }
  }
  
  return parts.length > 0 ? parts.join(' ') : '0s'
}

/**
 * 解析持续时间
 * @param duration 持续时间字符串
 * @returns 毫秒数
 */
export function parseDuration(duration: string): number {
  const regex = /(\d+)([dhms])/g
  const units: Record<string, number> = {
    d: 24 * 60 * 60 * 1000,
    h: 60 * 60 * 1000,
    m: 60 * 1000,
    s: 1000
  }
  
  let total = 0
  let match
  
  while ((match = regex.exec(duration)) !== null) {
    const value = parseInt(match[1], 10)
    const unit = match[2]
    total += value * (units[unit] || 0)
  }
  
  return total
}