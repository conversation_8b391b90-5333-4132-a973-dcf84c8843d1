package base

import (
	"context"
	"time"

	"frontapi/internal/models"
	base "frontapi/internal/repository/base"
)

// ServiceTemplate 通用服务模板
// 这个模板展示了如何使用嵌入模式来避免重复的委托方法
// 任何具体的服务都可以基于这个模板来实现
type ServiceTemplate[T models.BaseModelConstraint] struct {
	*BaseService[T]                        // 嵌入BaseService，自动获得所有方法
	repo            base.BaseRepository[T] // 具体的仓库实现
	entityType      string                 // 实体类型，用于日志和缓存
}

// NewServiceTemplate 创建通用服务模板实例
func NewServiceTemplate[T models.BaseModelConstraint](
	repo base.BaseRepository[T],
	entityType string,
) *ServiceTemplate[T] {
	return &ServiceTemplate[T]{
		BaseService: NewBaseService[T](repo, "base"),
		repo:        repo,
		entityType:  entityType,
	}
}

// 使用示例：
// 1. 嵌入后，可以直接调用所有BaseService的方法，无需委托：
//    service.Create(ctx, entity)
//    service.GetByID(ctx, id, useCache)
//    service.Update(ctx, entity)
//    service.Delete(ctx, id)
//    等等...
//
// 2. 如果需要扩展特定的业务逻辑，只需要添加新方法：
//    func (s *ServiceTemplate[T]) CustomMethod(ctx context.Context, param string) error {
//        // 自定义业务逻辑
//        return nil
//    }
//
// 3. 如果需要重写某个BaseService的方法，直接定义同名方法即可：
//    func (s *ServiceTemplate[T]) Create(ctx context.Context, entity *T) (string, error) {
//        // 自定义创建逻辑
//        // 可以调用原方法：return s.BaseService.Create(ctx, entity)
//        // 或者完全自定义实现
//    }

// GetEntityType 获取实体类型
func (s *ServiceTemplate[T]) GetEntityType() string {
	return s.entityType
}

// SetCustomCacheTTL 设置自定义缓存TTL（示例扩展方法）
func (s *ServiceTemplate[T]) SetCustomCacheTTL(ttl time.Duration) {
	// 可以直接调用嵌入的BaseService方法
	s.SetCacheTTL(ttl)
	// 或者添加额外的逻辑
}

// BatchCreateWithValidation 带验证的批量创建（示例扩展方法）
func (s *ServiceTemplate[T]) BatchCreateWithValidation(ctx context.Context, entities []*T) (int, error) {
	// 自定义验证逻辑
	for _, entity := range entities {
		s.SetEntityDefaults(entity) // 直接调用嵌入的方法
	}

	// 调用原始的批量创建方法
	return s.BatchCreate(ctx, entities)
}
