package pictures

import (
	"context"
	"errors"
	"frontapi/internal/models/pictures"
	repo "frontapi/internal/repository/pictures"
	"frontapi/internal/service/base"
	pictureValidator "frontapi/internal/validation/pictures"
	"frontapi/pkg/types"
	"time"

	"github.com/guregu/null/v6"
)

// PictureService 图片服务接口
type PictureService interface {
	base.IExtendedService[pictures.Picture]
	BatchCreatePictures(ctx context.Context, req *pictureValidator.BatchCreatePicturesRequest) (int, error)
}

// pictureService 图片服务实现
type pictureService struct {
	*base.ExtendedService[pictures.Picture]
	pictureRepo     repo.PictureRepository
	albumRepo       repo.PictureAlbumRepository
	categoryRepo    repo.PictureCategoryRepository
	pictureLikeRepo repo.PictureLikeRepository
}

// NewPictureService 创建图片服务实例
func NewPictureService(
	pictureRepo repo.PictureRepository,
	albumRepo repo.PictureAlbumRepository,
	categoryRepo repo.PictureCategoryRepository,
	pictureLikeRepo repo.PictureLikeRepository,
) PictureService {
	return &pictureService{
		ExtendedService: base.NewExtendedService[pictures.Picture](pictureRepo, "picture"),
		pictureRepo:     pictureRepo,
		albumRepo:       albumRepo,
		categoryRepo:    categoryRepo,
		pictureLikeRepo: pictureLikeRepo,
	}
}

// BatchCreatePictures 批量创建图片
func (s *pictureService) BatchCreatePictures(ctx context.Context, req *pictureValidator.BatchCreatePicturesRequest) (int, error) {
	if len(req.Pictures) == 0 {
		return 0, errors.New("没有要创建的图片")
	}

	// 用于存储准备创建的图片记录
	pictureRecords := make([]*pictures.Picture, 0, len(req.Pictures))
	pictureIDs := make([]string, 0, len(req.Pictures))

	// 逐个处理图片请求
	for _, p := range req.Pictures {
		// 获取相册信息
		var albumTitle string
		if p.AlbumID != "" {
			album, err := s.albumRepo.FindByID(ctx, p.AlbumID)
			if err == nil && album != nil {
				albumTitle = album.Title
			}
		}

		// 获取分类信息
		var categoryName string
		if p.CategoryID != "" {
			category, err := s.categoryRepo.FindByID(ctx, p.CategoryID)
			if err == nil && category != nil {
				categoryName = category.Name.String
			}
		}
		// 创建图片记录
		picture := &pictures.Picture{
			URL:        p.URL,
			Width:      p.Width,
			Height:     p.Height,
			Size:       uint64(p.Size),
			AlbumID:    null.StringFrom(p.AlbumID),
			AlbumTitle: albumTitle,
			UploadTime: types.JSONTime(time.Now()),
		}
		picture.SetID(s.GenerateID())
		picture.SetTitle(p.Title)
		picture.SetDescription(p.Description)
		picture.SetCategoryID(p.CategoryID)
		picture.SetCategoryName(categoryName)
		// picture.SetCreatorID(p.userID)
		picture.SetStatus(int8(p.Status))
		picture.SetCreatedAt(types.JSONTime(time.Now()))
		picture.SetUpdatedAt(types.JSONTime(time.Now()))

		pictureIDs = append(pictureIDs, picture.ID)

		pictureRecords = append(pictureRecords, picture)
	}

	// 批量创建图片记录
	return s.BatchCreate(ctx, pictureRecords)
}
