package pictures

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/pictures"
	pictureValidation "frontapi/internal/validation/pictures"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ==============================
// 图片分类操作
// ==============================
type PictureCategoryController struct {
	admin.BaseController
	categoryService pictures.PictureCategoryService
	pictureService  pictures.PictureService
}

func NewPictureCategoryController(
	categoryService pictures.PictureCategoryService,
	pictureService pictures.PictureService,
) *PictureCategoryController {
	return &PictureCategoryController{
		BaseController:  admin.BaseController{},
		categoryService: categoryService,
		pictureService:  pictureService,
	}
}

// GetCategory 获取图片分类详情
func (h *PictureCategoryController) GetCategory(c *fiber.Ctx) error {
	// 获取分类ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	category, err := h.categoryService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "分类不存在: "+err.Error())
	}

	return h.Success(c, category)
}

// CreateCategory 创建图片分类
func (h *PictureCategoryController) CreateCategory(c *fiber.Ctx) error {
	// 检查用户是否已登录
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 解析请求参数
	var req pictureValidation.PictureCategoryCreateRequest
	err := validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 检查是否有相同名称的分类
	if req.Name != "" {
		existingCategory, err := h.categoryService.FindOne(c.Context(), "name = ?", req.Name)
		if err != nil {
			return h.InternalServerError(c, "检查名称重复失败: "+err.Error())
		}
		if existingCategory != nil {
			return h.BadRequest(c, "已有相同名称的分类", nil)
		}
	}

	// 创建分类
	categoryID, err := h.categoryService.CreateCategory(c.Context(), &req)
	if err != nil {
		return h.InternalServerError(c, "创建分类失败: "+err.Error())
	}

	return h.Success(c, fiber.Map{"id": categoryID, "message": "创建分类成功"})
}

// UpdateCategory 更新图片分类
func (h *PictureCategoryController) UpdateCategory(c *fiber.Ctx) error {
	// 获取分类ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req pictureValidation.PictureCategoryUpdateRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 先查询现有的分类信息
	category, err := h.categoryService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "图片分类不存在")
	}
	if err := utils.SmartCopy(req, category); err != nil {
		return h.InternalServerError(c, "更新分类失败: "+err.Error())
	}
	// 检查是否有相同名称的分类（排除当前分类）
	if req.Name != nil && *req.Name != "" && *req.Name != *category.Name.Ptr() {
		existingCategory, err := h.categoryService.FindOne(c.Context(), "id != ? AND name = ?", id, *req.Name)
		if err != nil {
			return h.InternalServerError(c, "检查名称重复失败: "+err.Error())
		}
		if existingCategory != nil {
			return h.BadRequest(c, "已有相同名称的分类", nil)
		}
	}

	// 更新分类
	if err := h.categoryService.UpdateCategory(c.Context(), id, &req); err != nil {
		return h.InternalServerError(c, "更新分类失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新分类成功")
}

// DeleteCategory 删除图片分类
func (h *PictureCategoryController) DeleteCategory(c *fiber.Ctx) error {
	// 获取分类ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 删除分类
	if err := h.categoryService.Delete(c.Context(), id); err != nil {
		return h.InternalServerError(c, "删除分类失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "删除分类成功")
}

// ListCategories 获取图片分类列表
func (h *PictureCategoryController) ListCategories(c *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 提取查询参数
	keyword := reqInfo.Get("keyword").GetString()
	status := -999
	_ = h.GetIntegerValueWithDataWrapper(c, "status", &status)
	createdAtStart := reqInfo.Get("created_at_start").GetString()
	createdAtEnd := reqInfo.Get("created_at_end").GetString()

	// 构建查询条件
	condition := map[string]interface{}{
		"keyword":          keyword,
		"status":           status,
		"created_at_start": createdAtStart,
		"created_at_end":   createdAtEnd,
	}
	// 查询分类列表
	categories, total, err := h.categoryService.List(c.Context(), condition, "created_at DESC", page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取图片分类列表失败: "+err.Error())
	}

	return h.SuccessList(c, categories, total, page, pageSize)
}

// UpdateCategoryStatus 更新图片分类状态
func (h *PictureCategoryController) UpdateCategoryStatus(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidation.UpdateCategoryStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数"+err.Error(), req)
	}

	// 更新分类状态
	err := h.categoryService.UpdateStatus(c.Context(), req.ID, int(req.Status))
	if err != nil {
		return h.InternalServerError(c, "更新图片分类状态失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新图片分类状态成功")
}

// BatchUpdateCategoryStatus 批量更新图片分类状态
func (h *PictureCategoryController) BatchUpdateCategoryStatus(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidation.BatchUpdateCategoryStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 批量更新分类状态
	err := h.categoryService.BatchUpdateStatus(c.Context(), req.IDs, int(req.Status))
	if err != nil {
		return h.InternalServerError(c, "批量更新图片分类状态失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "批量更新图片分类状态成功")
}

// BatchDeleteCategory 批量删除图片分类
func (h *PictureCategoryController) BatchDeleteCategory(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidation.BatchDeleteCategoryRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 批量删除分类
	err := h.categoryService.BatchSoftDelete(c.Context(), req.IDs)
	if err != nil {
		return h.InternalServerError(c, "批量删除图片分类失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "批量删除图片分类成功")
}
