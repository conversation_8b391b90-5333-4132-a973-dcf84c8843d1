/**
 * 头像工具函数
 */

import { readonly, ref } from 'vue';

// 默认头像数量
const DEFAULT_AVATAR_COUNT = 5;

/**
 * 头像URL处理工具
 */

// 默认头像
const DEFAULT_AVATAR = '/images/avatars/default-avatar.png'

/**
 * 计算字符串的简单hash值
 * @param str 字符串
 * @returns hash值
 */
function hashString(str: string): number {
    let hash = 0;
    if (str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash);
}

/**
 * 获取默认头像URL（根据索引）
 * @param index 头像索引（1-5）
 * @returns 默认头像URL
 */
export function getDefaultAvatarUrl(index: number): string {
    const avatarIndex = ((index - 1) % DEFAULT_AVATAR_COUNT) + 1;
    return new URL(`../assets/image/avatar_${avatarIndex}.png`, import.meta.url).href;
}

/**
 * 根据用户ID获取取模头像
 * @param userId 用户ID
 * @returns 取模头像URL
 */
export function getModuloAvatarUrl(userId: string): string {
    const hash = hashString(userId);
    const avatarIndex = (hash % DEFAULT_AVATAR_COUNT) + 1;
    return getDefaultAvatarUrl(avatarIndex);
}

/**
 * 获取头像URL
 * @param avatar 头像路径或URL
 * @param userId 用户ID（用于生成默认头像）
 * @returns 完整的头像URL
 */
export function getAvatarUrl(avatar?: string, userId?: string): string {
    // 如果有头像路径
    if (avatar) {
        // 如果是完整URL，直接返回
        if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
            return avatar
        }

        // 如果是相对路径，加上基础URL
        if (avatar.startsWith('/')) {
            return avatar
        }

        // 否则拼接完整路径
        return `/uploads/avatars/${avatar}`
    }

    // 如果没有头像但有用户ID，返回取模头像
    if (userId) {
        return getModuloAvatarUrl(userId);
    }

    // 默认头像
    return DEFAULT_AVATAR
}

/**
 * 安全获取头像URL（支持异步验证）
 * @param avatarUrl 头像URL
 * @param userId 用户ID
 * @returns Promise<头像URL>
 */
export async function getSafeAvatarUrl(avatarUrl?: string, userId?: string): Promise<string> {
    // 如果有头像URL，先尝试验证
    if (avatarUrl && avatarUrl.trim()) {
        try {
            // 创建一个Promise来验证图片是否能加载
            await new Promise<void>((resolve, reject) => {
                const img = new Image()
                img.onload = () => resolve()
                img.onerror = () => reject(new Error('Image load failed'))
                img.src = avatarUrl

                // 设置超时，避免无限等待
                setTimeout(() => reject(new Error('Image load timeout')), 5000)
            })

            return avatarUrl
        } catch {
            // 头像加载失败，继续使用取模头像
        }
    }

    // 如果有用户ID，返回取模头像
    if (userId) {
        return getModuloAvatarUrl(userId)
    }

    // 都没有，返回默认头像
    return getDefaultAvatarUrl(1)
}

/**
 * 预加载头像
 * @param url 头像URL
 * @returns Promise<boolean> 是否加载成功
 */
export function preloadAvatar(url: string): Promise<boolean> {
    return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => resolve(true)
        img.onerror = () => resolve(false)
        img.src = url

        // 设置超时，避免长时间等待
        setTimeout(() => resolve(false), 5000);
    })
}

/**
 * 获取缩略图头像URL
 * @param avatar 原始头像路径
 * @param size 缩略图尺寸
 * @param userId 用户ID（用于错误回退）
 * @returns 缩略图URL
 */
export function getThumbnailAvatarUrl(avatar?: string, size: number = 100, userId?: string): string {
    const baseUrl = getAvatarUrl(avatar, userId);

    // 如果是取模头像，直接返回
    if (baseUrl.includes('avatar_')) {
        return baseUrl;
    }

    // 如果是外部URL，直接返回
    if (baseUrl.startsWith('http://') || baseUrl.startsWith('https://')) {
        return baseUrl
    }

    // 添加缩略图参数
    return `${baseUrl}?size=${size}`
}

/**
 * 创建头像组件的props
 * @param avatar 头像路径
 * @param userId 用户ID
 * @param size 头像尺寸
 * @returns 头像组件所需的props
 */
export function createAvatarProps(avatar?: string, userId?: string, size?: number) {
    const primaryUrl = getAvatarUrl(avatar, userId);
    const fallbackUrl = userId ? getModuloAvatarUrl(userId) : getDefaultAvatarUrl(1);

    return {
        src: primaryUrl,
        fallback: fallbackUrl,
        size: size || 40,
        onError: () => fallbackUrl
    };
}

/**
 * Vue组合式函数：使用头像
 * @param avatar 头像路径
 * @param userId 用户ID
 * @returns 响应式头像数据
 */
export function useAvatar(avatar?: string, userId?: string) {
    const avatarUrl = ref(getAvatarUrl(avatar, userId));
    const isLoading = ref(false);
    const hasError = ref(false);

    const loadAvatar = async () => {
        if (!avatar) {
            avatarUrl.value = userId ? getModuloAvatarUrl(userId) : getDefaultAvatarUrl(1);
            return;
        }

        isLoading.value = true;
        hasError.value = false;

        try {
            const safeUrl = await getSafeAvatarUrl(avatar, userId);
            avatarUrl.value = safeUrl;
        } catch (error) {
            hasError.value = true;
            avatarUrl.value = userId ? getModuloAvatarUrl(userId) : getDefaultAvatarUrl(1);
        } finally {
            isLoading.value = false;
        }
    };

    const retry = () => {
        loadAvatar();
    };

    // 初始加载
    loadAvatar();

    return {
        avatarUrl: readonly(avatarUrl),
        isLoading: readonly(isLoading),
        hasError: readonly(hasError),
        retry
    };
}

/**
 * 预加载所有默认头像
 * @returns Promise数组
 */
export function preloadDefaultAvatars(): Promise<void>[] {
    const promises: Promise<void>[] = [];

    for (let i = 1; i <= DEFAULT_AVATAR_COUNT; i++) {
        const promise = new Promise<void>((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => reject(new Error(`Failed to load avatar_${i}.png`));
            img.src = getDefaultAvatarUrl(i);
        });
        promises.push(promise);
    }

    return promises;
} 