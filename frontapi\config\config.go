package config

import (
	"fmt"
	"log"

	adminConfig "frontapi/config/admin"
	cacheConfig "frontapi/config/cache"
	corsConfig "frontapi/config/cors"
	databaseConfig "frontapi/config/database"
	redisDbConfig "frontapi/config/redis"
	securityConfig "frontapi/config/security"
	serverConfig "frontapi/config/server"
	staticConfig "frontapi/config/static"

	"github.com/spf13/viper"
)

// Config 应用配置结构体
type Config struct {
	// 服务器配置
	Server serverConfig.ServerConfig `mapstructure:"server"`

	// 数据库配置
	Database databaseConfig.DatabaseConfig `mapstructure:"database"`

	// JWT配置
	JWT securityConfig.SecureConfig `mapstructure:"jwt"`

	// 缓存配置
	Cache cacheConfig.CacheConfig `mapstructure:"cache"`

	// Redis配置
	Redis redisDbConfig.Config `mapstructure:"redis"`

	// 静态文件配置
	Static staticConfig.StaticConfig `mapstructure:"static"`

	// 运行时临时目录
	Runtime struct {
		TempDir string `mapstructure:"temp_dir"`
	} `mapstructure:"runtime"`

	// CORS配置
	CORS corsConfig.CorsConfig `mapstructure:"cors"`

	// 媒体存储配置
	Media staticConfig.MediaConfig `mapstructure:"media"`

	// 上传配置
	Upload staticConfig.UploadConfig `mapstructure:"upload"`

	// Casbin权限配置
	Casbin adminConfig.CasbinConfig `mapstructure:"casbin"`
}

// AppConfig 全局配置实例
var AppConfig Config

// LoadConfig 加载配置
func LoadConfig() {
	viper.SetConfigName("config")    // 配置文件名称
	viper.SetConfigType("yaml")      // 配置文件类型
	viper.AddConfigPath(".")         // 查找配置文件的路径
	viper.AddConfigPath("../")       // 查找配置文件的路径
	viper.AddConfigPath("../../")    // 查找配置文件的路径
	viper.AddConfigPath("../../../") // 查找配置文件的路径

	// 设置默认值，确保即使配置文件中没有这些项也能正常工作
	setDefaultValues()

	// 读取配置文件
	err := viper.ReadInConfig()
	if err != nil {
		log.Fatalf("无法读取配置文件: %v", err)
	}

	// 将配置解析到结构体
	err = viper.Unmarshal(&AppConfig)
	if err != nil {
		log.Fatalf("无法解析配置: %v", err)
	}

	// 设置数据库连接池默认值
	if AppConfig.Database.MaxIdleConns == 0 {
		AppConfig.Database.MaxIdleConns = 10
	}
	if AppConfig.Database.MaxOpenConns == 0 {
		AppConfig.Database.MaxOpenConns = 100
	}

	// 输出配置加载日志
	log.Println("配置文件加载成功:", viper.ConfigFileUsed())
}

// 设置默认值
func setDefaultValues() {
	// 服务器配置
	viper.SetDefault("server.host", "0.0.0.0")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.admin_port", 8081)
	viper.SetDefault("server.media_port", 8082)

	// 数据库配置
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 3306)
	viper.SetDefault("database.user", "root")
	viper.SetDefault("database.password", "root")
	viper.SetDefault("database.name", "lyvideos")
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.max_open_conns", 100)

	// JWT配置
	viper.SetDefault("jwt.expiry", 168) // 7天

	// 缓存配置
	viper.SetDefault("cache.default", "memory")
	viper.SetDefault("cache.redis.host", "localhost")
	viper.SetDefault("cache.redis.port", 6379)
	viper.SetDefault("cache.redis.db", 0)
	viper.SetDefault("cache.redis.ttl", 3600)
	viper.SetDefault("cache.redis.cluster.enable", false)

	viper.SetDefault("cache.file.path", "./cache")
	viper.SetDefault("cache.file.ttl", 3600)

	viper.SetDefault("cache.memory.size", 10000)
	viper.SetDefault("cache.memory.ttl", 3600)

	viper.SetDefault("cache.bigcache.size", 10000)
	viper.SetDefault("cache.bigcache.ttl", 3600)

	// Redis配置
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.expiry", 168)
	viper.SetDefault("redis.admin.db", 1)
	viper.SetDefault("redis.admin.prefix", "admin:")
	viper.SetDefault("redis.like.db", 1)
	viper.SetDefault("redis.like.prefix", "ly_")
	viper.SetDefault("redis.collect.db", 1)
	viper.SetDefault("redis.collect.prefix", "ly_")
	viper.SetDefault("redis.follow.db", 1)
	viper.SetDefault("redis.follow.prefix", "ly_")

	// CORS配置
	viper.SetDefault("cors.allow_credentials", true)
	viper.SetDefault("cors.allow_origins", []string{"http://localhost:8080", "http://localhost:8081"})
	viper.SetDefault("cors.allow_methods", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
	viper.SetDefault("cors.allow_headers", []string{"Origin", "Content-Type", "Accept"})
}

// 辅助函数
func GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		AppConfig.Database.User,
		AppConfig.Database.Password,
		AppConfig.Database.Host,
		AppConfig.Database.Port,
		AppConfig.Database.Name,
	)
}
