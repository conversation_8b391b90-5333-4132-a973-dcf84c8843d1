<template>
  <el-dialog
    title="添加漫画页面"
    v-model="dialogVisible"
    width="950px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    :destroy-on-close="true"
    class="comic-page-form-dialog"
    :append-to-body="true"
  >
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" class="page-add-tabs">
      <!-- 批量上传选项卡 (更改默认顺序，把批量放在前面) -->
      <el-tab-pane label="批量上传页面" name="batch">
        <div class="batch-upload-form">
          <el-alert
            title="批量上传说明"
            type="info"
            description="您可以选择多个图片文件一次性上传为漫画页面。系统会按照文件选择顺序自动编排页码，您也可以在上传前拖拽调整顺序。"
            show-icon
            :closable="false"
            class="mb-4"
          />

          <el-form ref="batchFormRef" :model="batchFormData" :rules="batchRules" label-width="100px">
            <el-form-item label="页码起始值" prop="startPageNumber">
              <el-input-number
                v-model="batchFormData.startPageNumber"
                :min="pageMax + 1"
                :max="9999"
                style="width: 150px;"
                controls-position="right"
              />
              <span class="help-text ml-2">批量上传的页面将从此页码开始依次编号</span>
            </el-form-item>

            <el-form-item label="图片文件" prop="files" class="multi-select-container">
              <MultiFileSelector
                v-model="batchFormData.files"
                :limit="100"
                :sortable="true"
                @change="handleBatchFilesChange"
                file-type="pictures"
              />

              <div class="file-summary" v-if="batchFormData.files.length > 0">
                <el-tag type="success" size="large" class="mb-2">已选择 {{ batchFormData.files.length }} 个文件</el-tag>
                <div class="preview-list">
                  <div
                    v-for="(file, index) in batchFormData.files.slice(0, 5)"
                    :key="index"
                    class="preview-item"
                  >
                    <span class="page-number">{{ batchFormData.startPageNumber + index }}</span>
                    <el-image
                      :src="file.url"
                      fit="cover"
                      class="preview-image"
                      :preview-src-list="[file.url]"
                    />
                    <div class="file-info">
                      <div class="file-name">{{ file.name }}</div>
                      <div class="file-size" v-if="file.size">{{ formatFileSize(file.size) }}</div>
                      <div class="file-dimensions" v-if="file.width && file.height">
                        {{ file.width }}×{{ file.height }}
                      </div>
                    </div>
                  </div>
                  <div v-if="batchFormData.files.length > 5" class="more-files">
                    +{{ batchFormData.files.length - 5 }} 个文件
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 手动添加选项卡 -->
      <el-tab-pane label="手动添加页面" name="manual">
        <div class="manual-add-form">
          <el-alert
            title="手动添加说明"
            type="info"
            description="您可以逐个添加页面，并手动控制每个页面的页码和顺序。适合对页面有精确控制需求的情况。"
            show-icon
            :closable="false"
            class="mb-4"
          />

          <div class="operation-bar mb-3">
            <el-button type="primary" @click="addNewItem" size="small">
              <el-icon><Plus /></el-icon> 添加新页面
            </el-button>
            <el-button type="warning" @click="sortPagesByNumber" size="small" :disabled="formList.length <= 1">
              <el-icon><Sort /></el-icon> 按页码排序
            </el-button>
            <el-button type="danger" @click="clearAllItems" size="small" :disabled="formList.length <= 1">
              <el-icon><Delete /></el-icon> 清空所有
            </el-button>
          </div>

          <el-scrollbar height="450px">
            <el-table :data="formList" style="width: 100%" size="small" border>
              <el-table-column label="#" width="60" align="center">
                <template #default="{ $index }">
                  <div class="drag-handle">
                    <el-icon><Rank /></el-icon>
                    <span>{{ $index + 1 }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="页面图片" width="280" align="center">
                <template #default="{ row, $index }">
                  <UrlOrFileInput
                    v-model="row.image_url"
                    fileType="image"
                    subDir="comic/pages"
                    placeholder="请输入封面图URL或选择图片"
                  />
                </template>
              </el-table-column>

              <el-table-column label="页码" width="130" align="center">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.page_number"
                    :min="pageMax + 1"
                    :max="9999"
                    size="small"
                    style="width: 100px;"
                    controls-position="right"
                  />
                </template>
              </el-table-column>

              <el-table-column label="尺寸" width="120" align="center">
                <template #default="{ row }">
                  <div v-if="row.width && row.height">
                    {{ row.width }}×{{ row.height }}
                  </div>
                  <div v-else>--</div>
                </template>
              </el-table-column>

              <el-table-column label="操作" align="center">
                <template #default="{ $index }">
                  <div class="action-buttons">
                    <el-button-group>
                      <el-button
                        type="primary"
                        size="small"
                        :disabled="$index === 0"
                        @click="moveItem($index, 'up')"
                        title="上移"
                      >
                        <el-icon><ArrowUp /></el-icon>
                      </el-button>
                      <el-button
                        type="primary"
                        size="small"
                        :disabled="$index === formList.length - 1"
                        @click="moveItem($index, 'down')"
                        title="下移"
                      >
                        <el-icon><ArrowDown /></el-icon>
                      </el-button>
                    </el-button-group>

                    <el-button-group class="ml-1">
                      <el-button
                        type="success"
                        size="small"
                        @click="addItemAfter($index)"
                        title="在此行后添加"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="removeItem($index)"
                        :disabled="formList.length === 1"
                        title="删除此行"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </el-button-group>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-scrollbar>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <div class="warning-message" v-if="hasDataToSave">
          <el-icon><Warning /></el-icon> 您有未保存的数据
        </div>
        <div class="buttons">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading" :disabled="!hasDataToSave">
            <el-icon v-if="!submitLoading"><Upload /></el-icon>
            <span>{{ activeTab === 'batch' ? '批量上传' : '保存页面' }}</span>
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watchEffect, reactive, nextTick, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { batchCreateComicsPages } from '@/service/api/comics/comics';
import UploadImage from './UploadImage.vue';
import MultiFileSelector from "@/components/filemanager/MultiFileSelector.vue";
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import {
  Plus, Delete, Sort, Rank, ArrowUp, ArrowDown,
  Warning, Upload
} from '@element-plus/icons-vue';

// 接口定义
interface FileItem {
  name: string;
  url: string;
  path: string;
  size?: number;
  width?: number;
  height?: number;
  type?: string;
  extension?: string;
  is_dir?: boolean;
  [key: string]: any;

}

interface PageItem {
  image_url: string;
  page_number: number;
  width: number;
  height: number;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  comicsId: {
    type: String,
    required: true
  },
  chapterId: {
    type: String,
    required: true
  },
  pageMax: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update:visible', 'success']);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

const submitLoading = ref(false);
const activeTab = ref('batch'); // 默认选择批量上传
const formList = ref<PageItem[]>([{ image_url: '', page_number: 1, width: 0, height: 0 }]);

// 批量上传表单数据
const batchFormRef = ref();
const batchFormData = reactive({
  files: [] as FileItem[],
  startPageNumber: 1
});

// 批量上传表单验证规则
const batchRules = {
  files: [{ required: true, message: '请选择至少一个图片文件', trigger: 'change' }],
  startPageNumber: [{ required: true, message: '请设置起始页码', trigger: 'change' }]
};

// 判断是否有未保存的数据
const hasDataToSave = computed(() => {
  if (activeTab.value === 'manual') {
    return formList.value.some(item => item.image_url);
  } else {
    return batchFormData.files.length > 0;
  }
});

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
};

// 初始化表单数据
watchEffect(() => {
  if (!props.visible) {
    // 重置表单
    setTimeout(() => {
      formList.value = [{ image_url: '', page_number: 1, width: 0, height: 0 }];
      batchFormData.files = [];
      batchFormData.startPageNumber = 1;
      activeTab.value = 'batch';
    }, 300);
  }
});

// 处理上传成功 - 手动模式
const handleUploadSuccess = (result: any, index: number) => {
  console.log('Image uploaded or URL entered:', result, index);

  // Handle direct URL input from UrlOrFileInput
  if (result && typeof result === 'string') {
    // It's a direct URL string
    formList.value[index].image_url = result;

    // Load the image to get dimensions
    const img = new Image();
    img.onload = () => {
      formList.value[index].width = img.width;
      formList.value[index].height = img.height;
    };
    img.src = result;
  }

  // For backward compatibility with other component types
  else if (result && result.url) {
    formList.value[index].image_url = result.url;
    formList.value[index].width = result.width || 0;
    formList.value[index].height = result.height || 0;
  }
};

// 处理批量文件变更
const handleBatchFilesChange = (files: FileItem[]) => {
  console.log('Batch files changed:', files.length);
  batchFormData.files = files;
};

// 添加新行
const addNewItem = () => {
  const lastPageNumber = formList.value[formList.value.length - 1]?.page_number || 0;
  formList.value.push({
    image_url: '',
    page_number: lastPageNumber + 1,
    width: 0,
    height: 0
  });
};

// 在指定位置后添加新行
const addItemAfter = (index: number) => {
  const newPageNumber = formList.value[index].page_number + 1;

  // 更新后续页码
  for (let i = index + 1; i < formList.value.length; i++) {
    formList.value[i].page_number += 1;
  }

  formList.value.splice(index + 1, 0, {
    image_url: '',
    page_number: newPageNumber,
    width: 0,
    height: 0
  });
};

// 删除行
const removeItem = (index: number) => {
  if (formList.value.length > 1) {
    formList.value.splice(index, 1);
  }
};

// 清空所有行
const clearAllItems = () => {
  ElMessageBox.confirm('确定要清空所有页面吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formList.value = [{ image_url: '', page_number: 1, width: 0, height: 0 }];
  }).catch(() => {});
};

// 按页码排序
const sortPagesByNumber = () => {
  formList.value.sort((a, b) => a.page_number - b.page_number);
  ElMessage.success('已按页码排序');
};

// 移动行
const moveItem = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1;

  // 交换页码
  const tempPageNumber = formList.value[index].page_number;
  formList.value[index].page_number = formList.value[newIndex].page_number;
  formList.value[newIndex].page_number = tempPageNumber;

  // 交换位置
  const temp = formList.value[index];
  formList.value[index] = formList.value[newIndex];
  formList.value[newIndex] = temp;
};

// 处理关闭
const handleClose = () => {
  // 判断有没有保存数据，提示保存
  if (hasDataToSave.value) {
    ElMessageBox.confirm('确定关闭吗？未保存的数据将丢失', '提示', {
      confirmButtonText: '确定关闭',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      dialogVisible.value = false;
    }).catch(() => {
      // 用户取消关闭
    });
  } else {
    dialogVisible.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (activeTab.value === 'manual') {
    await submitManualForm();
  } else {
    await submitBatchForm();
  }
};

// 提交手动表单
const submitManualForm = async () => {
  // 验证表单
  const invalidItems = formList.value.filter(item => !item.image_url);
  if (invalidItems.length > 0) {
    ElMessage.error(`请上传所有行的图片，有 ${invalidItems.length} 个页面未上传图片`);
    return;
  }

  try {
    submitLoading.value = true;
    
    // 批量添加
    const data = formList.value.map(async item =>{
      if(!item.width||!item.height){
         const img=new Image();
         img.src=item.image_url;
         await new Promise(resolve => {
          img.onload = resolve;
          img.onerror = resolve;
        });
         item.width=img.naturalWidth;
         item.height=img.naturalHeight;
      }
      return {
        comic_id: props.comicsId,
        chapter_id: props.chapterId,
        image_url: item.image_url,
        page_number: item.page_number,
        width: item.width,
        height: item.height
      };
    });
    const result = await Promise.all(data);

    const {response} = await batchCreateComicsPages(props.chapterId, props.comicsId, result) as any;
    if (response.data.code === 2000) {
      ElMessage.success(`添加成功！共添加了 ${result.length} 个页面`);
      emit('success');
      dialogVisible.value = false;
    } else {
      ElMessage.error(response.data?.message || '添加失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败');
  } finally {
    submitLoading.value = false;
  }
};

// 提交批量表单
const submitBatchForm = async () => {
  if (batchFormRef.value) {
    await batchFormRef.value.validate(async (valid: boolean) => {
      if (valid) {
        if (batchFormData.files.length === 0) {
          ElMessage.warning('请至少选择一个图片文件');
          return;
        }

        try {
          submitLoading.value = true;

          // 准备数据
          const pages = batchFormData.files.map((file, index) => ({
            comic_id: props.comicsId,
            chapter_id: props.chapterId,
            image_url: file.url,
            page_number: batchFormData.startPageNumber + index,
            width: file.width || 0,
            height: file.height || 0
          }));

          // 批量创建
          const {response,data} = await batchCreateComicsPages(props.chapterId, props.comicsId, pages) as any;
          if (response.data.code === 2000) {
            ElMessage.success(`批量上传成功！共添加了 ${pages.length} 个页面`);
            emit('success');
            dialogVisible.value = false;
          } else {
            ElMessage.error(response.data.message || '批量添加页面失败');
          }
        } catch (error: any) {
          ElMessage.error(error.message || '操作失败');
        } finally {
          submitLoading.value = false;
        }
      }
    });
  }
};

// 取消
const handleCancel = () => {
  handleClose();
};

// Add an onMounted hook to apply fixes for z-index
onMounted(() => {
  // Add a global CSS rule to fix z-index for nested dialogs
  const styleEl = document.createElement('style');
  styleEl.textContent = `
    .file-manager-dialog {
      z-index: 3000 !important;
    }
    .el-dialog__wrapper.file-manager-dialog {
      z-index: 3000 !important;
    }
    .file-manager-dialog .el-dialog__body{
      padding:0!important;
    }
    .preview-dialog{
      z-index: 3000 !important;
    }
    .el-overlay:nth-of-type(2) {
      z-index: 3000 !important;
    }
  `;
  document.head.appendChild(styleEl);
});
</script>

<style scoped lang="scss">
// Remove the duplicated global z-index fixes since they're now in a dedicated file
.comic-page-form-dialog {
  :deep(.el-dialog__body) {
    padding-top: 10px;
  }
}

.page-add-tabs {
  margin-bottom: 15px;
}

.manual-add-form,
.batch-upload-form {
  margin-bottom: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.ml-1 {
  margin-left: 4px;
}

.multi-select-container {
  margin-bottom: 0;
}

.operation-bar {
  display: flex;
  gap: 10px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.help-text {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .warning-message {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #e6a23c;
    font-size: 14px;
  }

  .buttons {
    display: flex;
    gap: 10px;
  }
}

.file-summary {
  margin-top: 15px;

  .preview-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;

    .preview-item {
      position: relative;
      width: 120px;

      .page-number {
        position: absolute;
        top: 5px;
        left: 5px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1;
      }

      .preview-image {
        width: 120px;
        height: 160px;
        object-fit: cover;
        border-radius: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }

      .file-info {
        margin-top: 5px;
        font-size: 12px;

        .file-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 120px;
        }

        .file-size,
        .file-dimensions {
          color: #909399;
        }
      }
    }

    .more-files {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 160px;
      background: #f5f7fa;
      border-radius: 4px;
      color: #909399;
      font-size: 14px;
    }
  }
}

.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: move;
}

</style>
