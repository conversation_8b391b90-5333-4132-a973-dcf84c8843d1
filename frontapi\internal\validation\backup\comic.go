package validation

// ComicCreateRequest 漫画创建请求验证模型
type ComicCreateRequest struct {
	Title       string   `json:"title" validate:"required,min=2,max=100"`
	Description string   `json:"description" validate:"required,min=10,max=2000"`
	Cover       string   `json:"cover" validate:"required,url"`
	Author      string   `json:"author" validate:"required"`
	CategoryID  uint     `json:"categoryId" validate:"required,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
}

// ComicUpdateRequest 漫画更新请求验证模型
type ComicUpdateRequest struct {
	Title       string   `json:"title" validate:"omitempty,min=2,max=100"`
	Description string   `json:"description" validate:"omitempty,min=10,max=2000"`
	Cover       string   `json:"cover" validate:"omitempty,url"`
	Author      string   `json:"author" validate:"omitempty"`
	CategoryID  uint     `json:"categoryId" validate:"omitempty,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
}

// ComicChapterCreateRequest 漫画章节创建请求验证模型
type ComicChapterCreateRequest struct {
	ComicID   uint     `json:"comicId" validate:"required,min=1"`
	Title     string   `json:"title" validate:"required,min=2,max=100"`
	ChapterNo float64  `json:"chapterNo" validate:"required"` // 支持小数，如 1.5 表示番外
	Images    []string `json:"images" validate:"required,dive,url"`
	IsVIP     bool     `json:"isVip"`
	Price     float64  `json:"price" validate:"omitempty,min=0"`
}

// ComicChapterUpdateRequest 漫画章节更新请求验证模型
type ComicChapterUpdateRequest struct {
	Title     string   `json:"title" validate:"omitempty,min=2,max=100"`
	ChapterNo float64  `json:"chapterNo" validate:"omitempty"`
	Images    []string `json:"images" validate:"omitempty,dive,url"`
	IsVIP     bool     `json:"isVip"`
	Price     float64  `json:"price" validate:"omitempty,min=0"`
}

// ComicListRequest 漫画列表请求验证模型
type ComicListRequest struct {
	Page       int      `json:"page" validate:"min=1"`
	PageSize   int      `json:"pageSize" validate:"min=1,max=100"`
	CategoryID uint     `json:"categoryId" validate:"omitempty,min=1"`
	Status     int      `json:"status" validate:"omitempty,oneof=0 1 2"`
	Tags       []string `json:"tags" validate:"omitempty"`
	SortBy     string   `json:"sortBy" validate:"omitempty,oneof=latest popular favorite update"`
	IsVIP      bool     `json:"isVip" validate:"omitempty"`
	IsAdult    bool     `json:"isAdult" validate:"omitempty"`
}

// ComicCommentRequest 漫画评论请求验证模型
type ComicCommentRequest struct {
	ComicID     uint   `json:"comicId" validate:"required,min=1"`
	ChapterID   uint   `json:"chapterId" validate:"omitempty,min=1"`
	Content     string `json:"content" validate:"required,min=1,max=500"`
	ParentID    uint   `json:"parentId"`    // 父评论ID，0表示顶级评论
	SpoilerFlag bool   `json:"spoilerFlag"` // 是否包含剧透
}
