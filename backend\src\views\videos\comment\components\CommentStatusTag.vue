<template>
  <div class="comment-status-tag">
    <el-tag
      v-if="status === 1"
      type="success"
      size="small"
      effect="light"
    >
      <el-icon><CircleCheck /></el-icon> 正常
    </el-tag>
    <el-tag
      v-else-if="status === 0"
      type="warning"
      size="small"
      effect="light"
    >
      <el-icon><Hide /></el-icon> 隐藏
    </el-tag>
    <el-tag
      v-else-if="status === -1"
      type="danger"
      size="small"
      effect="light"
    >
      <el-icon><CloseBold /></el-icon> 审核不通过
    </el-tag>
    <el-tag
      v-else-if="status === -4"
      type="info"
      size="small"
      effect="light"
    >
      <el-icon><Delete /></el-icon> 用户删除
    </el-tag>
    <el-tag
      v-else
      type="info"
      size="small"
      effect="plain"
    >
      未知状态
    </el-tag>
  </div>
</template>

<script setup lang="ts">
import { CircleChe<PERSON>, CloseBold, Delete, Hide } from '@element-plus/icons-vue';

// 定义组件属性
interface Props {
  status: number;
}

defineProps<Props>();
</script>

<style scoped lang="scss">
.comment-status-tag {
  display: inline-flex;
  align-items: center;
  
  .el-tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      font-size: 12px;
    }
  }
}
</style> 