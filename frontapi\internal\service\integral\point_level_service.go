package integral

import (
	model "frontapi/internal/models/integral"
	repo "frontapi/internal/repository/integral"
	"frontapi/internal/service/base"
)

// CreatePointLevelRequest 创建等级请求
type CreatePointLevelRequest struct {
	Level     int    `json:"level"`
	Name      string `json:"name"`
	MinPoints int    `json:"min_points"`
	MaxPoints *int   `json:"max_points"`
	Icon      string `json:"icon"`
	Benefits  string `json:"benefits"`
	Color     string `json:"color"`
}

// UpdatePointLevelRequest 更新等级请求
type UpdatePointLevelRequest struct {
	Level     int    `json:"level"`
	Name      string `json:"name"`
	MinPoints int    `json:"min_points"`
	MaxPoints *int   `json:"max_points"`
	Icon      string `json:"icon"`
	Benefits  string `json:"benefits"`
	Color     string `json:"color"`
}

// PointLevelService 积分等级服务接口
type PointLevelService interface {
	base.IExtendedService[model.PointLevel]
}

type pointLevelService struct {
	*base.ExtendedService[model.PointLevel]
	repo repo.PointLevelRepository
}

func NewPointLevelService(repo repo.PointLevelRepository) PointLevelService {
	return &pointLevelService{
		ExtendedService: base.NewExtendedService[model.PointLevel](repo, "point_level"),
		repo:            repo,
	}
}
