import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getTableDetailByPost } from '@/service/api/database/database';
import type { TableDetailResponse } from '@/types/database';

export function useTableDetail() {
  const tableDetail = ref<TableDetailResponse | null>(null);
  const loading = ref(false);

  // 获取表详情
  const getTableDetail = async (tableName: string) => {
    if (!tableName || typeof tableName !== 'string' || tableName.trim() === '') {
      console.error('无效的表名:', tableName);
      ElMessage.error('无效的表名');
      return null;
    }

    try {
      loading.value = true;
      console.log('请求表详情，表名:', tableName);
      
      const response = await getTableDetailByPost({
        data: { table_name: tableName }
      });
      
      console.log('表详情响应:', response);
      
      if (response) {
        // 检查响应是否有data字段
        let detail;
        if (response.data) {
          detail = response.data as unknown as TableDetailResponse;
          console.log('使用response.data:', detail);
        } else {
          detail = response as unknown as TableDetailResponse;
          console.log('直接使用response:', detail);
        }
        
        // 验证响应数据结构
        if (detail && typeof detail === 'object') {
          // 确保必要的字段存在
          const processedDetail: TableDetailResponse = {
            table_info: detail.table_info || {},
            columns: Array.isArray(detail.columns) ? detail.columns : [],
            foreign_keys: Array.isArray(detail.foreign_keys) ? detail.foreign_keys : [],
            create_sql: detail.create_sql || ''
          };
          
          console.log('处理后的表详情:', processedDetail);
          console.log('columns数据:', processedDetail.columns);
          console.log('foreign_keys数据:', processedDetail.foreign_keys);
          
          tableDetail.value = processedDetail;
          return processedDetail;
        } else {
          console.error('表详情响应数据格式不正确:', detail);
          ElMessage.error('表详情数据格式不正确');
          return null;
        }
      } else {
        console.error('表详情响应为空');
        ElMessage.error('获取表详情失败：响应为空');
        return null;
      }
    } catch (error: any) {
      console.error('获取表详情失败:', error);
      console.error('错误详情:', {
        message: error?.message,
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        data: error?.response?.data
      });
      
      if (error?.response?.status === 404) {
        ElMessage.error(`表 "${tableName}" 不存在`);
      } else if (error?.response?.status === 401) {
        ElMessage.error('请先登录后再使用此功能');
      } else if (error?.response?.status === 403) {
        ElMessage.error('没有权限访问此功能');
      } else {
        ElMessage.error('获取表详情失败: ' + (error?.message || '未知错误'));
      }
      return null;
    } finally {
      loading.value = false;
    }
  };

  // 清空表详情
  const clearTableDetail = () => {
    tableDetail.value = null;
  };

  return {
    tableDetail,
    loading,
    getTableDetail,
    clearTableDetail
  };
} 