package extint

import (
	"context"
	"errors"

	"frontapi/internal/models"

	"gorm.io/gorm"
)

// IntExtendedRepository 针对int类型ID的扩展仓库接口，包含更多业务方法
type IntExtendedRepository[T models.IntBaseModelConstraint] interface {
	IntBaseRepository[T]

	// 计数更新操作
	UpdateCount(ctx context.Context, id int, field string, increment int64) error
	UpdateViewCount(ctx context.Context, id int) error
	UpdateLikeCount(ctx context.Context, id int, increment int) error
	UpdateCommentCount(ctx context.Context, id int, increment int) error

	// 状态操作
	UpdateStatus(ctx context.Context, id int, status int) error
	BatchUpdateStatus(ctx context.Context, ids []int, status int) error

	// 软删除操作
	SoftDelete(ctx context.Context, id int) error
	BatchSoftDelete(ctx context.Context, ids []int) error
	Restore(ctx context.Context, id int) error

	// 排序操作
	UpdateSortOrder(ctx context.Context, id int, sortOrder int) error

	// 特色/推荐操作
	SetFeatured(ctx context.Context, id int, featured bool) error
	BatchSetFeatured(ctx context.Context, ids []int, featured bool) error

	// 分页查询增强
	ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)

	// 条件查询方法
	ListWithCondition(ctx context.Context, orderBy string, whereClause string, args ...interface{}) ([]*T, error)
	ListWithConditionAndPagination(ctx context.Context, orderBy string, page, pageSize int, whereClause string, args ...interface{}) ([]*T, int64, error)

	// 原生SQL支持
	ExecuteRaw(ctx context.Context, sql string, values ...interface{}) error
	QueryRaw(ctx context.Context, dest interface{}, sql string, values ...interface{}) error
}

// intExtendedRepository 针对int类型ID的扩展仓库实现
type intExtendedRepository[T models.IntBaseModelConstraint] struct {
	IntBaseRepository[T]
}

// NewIntExtendedRepository 创建针对int类型ID的扩展仓库实例
func NewIntExtendedRepository[T models.IntBaseModelConstraint](db *gorm.DB) IntExtendedRepository[T] {
	return &intExtendedRepository[T]{
		IntBaseRepository: NewIntBaseRepository[T](db),
	}
}

// UpdateCount 更新计数字段
func (r *intExtendedRepository[T]) UpdateCount(ctx context.Context, id int, field string, increment int64) error {
	if field == "" {
		return errors.New("field name cannot be empty")
	}

	return r.GetDBWithContext(ctx).Model(new(T)).Where("id = ?", id).UpdateColumn(field, gorm.Expr(field+" + ?", increment)).Error
}

// UpdateViewCount 更新查看次数
func (r *intExtendedRepository[T]) UpdateViewCount(ctx context.Context, id int) error {
	return r.UpdateCount(ctx, id, "view_count", 1)
}

// UpdateLikeCount 更新点赞数
func (r *intExtendedRepository[T]) UpdateLikeCount(ctx context.Context, id int, increment int) error {
	return r.UpdateCount(ctx, id, "like_count", int64(increment))
}

// UpdateCommentCount 更新评论数
func (r *intExtendedRepository[T]) UpdateCommentCount(ctx context.Context, id int, increment int) error {
	return r.UpdateCount(ctx, id, "comment_count", int64(increment))
}

// UpdateStatus 更新状态
func (r *intExtendedRepository[T]) UpdateStatus(ctx context.Context, id int, status int) error {
	return r.GetDBWithContext(ctx).Model(new(T)).Where("id = ?", id).Update("status", status).Error
}

// BatchUpdateStatus 批量更新状态
func (r *intExtendedRepository[T]) BatchUpdateStatus(ctx context.Context, ids []int, status int) error {
	if len(ids) == 0 {
		return nil
	}

	return r.GetDBWithContext(ctx).Model(new(T)).Where("id IN ?", ids).Update("status", status).Error
}

// SoftDelete 软删除
func (r *intExtendedRepository[T]) SoftDelete(ctx context.Context, id int) error {
	return r.UpdateStatus(ctx, id, 0) // 假设0表示已删除状态
}

// BatchSoftDelete 批量软删除
func (r *intExtendedRepository[T]) BatchSoftDelete(ctx context.Context, ids []int) error {
	return r.BatchUpdateStatus(ctx, ids, 0)
}

// Restore 恢复软删除的记录
func (r *intExtendedRepository[T]) Restore(ctx context.Context, id int) error {
	return r.UpdateStatus(ctx, id, 1) // 假设1表示正常状态
}

// UpdateSortOrder 更新排序
func (r *intExtendedRepository[T]) UpdateSortOrder(ctx context.Context, id int, sortOrder int) error {
	return r.GetDBWithContext(ctx).Model(new(T)).Where("id = ?", id).Update("sort_order", sortOrder).Error
}

// SetFeatured 设置推荐状态
func (r *intExtendedRepository[T]) SetFeatured(ctx context.Context, id int, featured bool) error {
	featuredValue := 0
	if featured {
		featuredValue = 1
	}
	return r.GetDBWithContext(ctx).Model(new(T)).Where("id = ?", id).Update("is_featured", featuredValue).Error
}

// BatchSetFeatured 批量设置推荐状态
func (r *intExtendedRepository[T]) BatchSetFeatured(ctx context.Context, ids []int, featured bool) error {
	if len(ids) == 0 {
		return nil
	}

	featuredValue := 0
	if featured {
		featuredValue = 1
	}
	return r.GetDBWithContext(ctx).Model(new(T)).Where("id IN ?", ids).Update("is_featured", featuredValue).Error
}

// ListWithPreload 带预加载的分页查询
func (r *intExtendedRepository[T]) ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.GetDBWithContext(ctx).Model(new(T))

	// 应用条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用预加载
	for _, preload := range preloads {
		query = query.Preload(preload)
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Find(&entities).Error
	return entities, total, err
}

// ListWithCondition 根据条件查询列表
func (r *intExtendedRepository[T]) ListWithCondition(ctx context.Context, orderBy string, whereClause string, args ...interface{}) ([]*T, error) {
	var entities []*T

	query := r.GetDBWithContext(ctx).Model(new(T))

	// 应用条件
	if whereClause != "" {
		query = query.Where(whereClause, args...)
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	err := query.Find(&entities).Error
	return entities, err
}

// ListWithConditionAndPagination 根据条件分页查询列表
func (r *intExtendedRepository[T]) ListWithConditionAndPagination(ctx context.Context, orderBy string, page, pageSize int, whereClause string, args ...interface{}) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.GetDBWithContext(ctx).Model(new(T))

	// 应用条件
	if whereClause != "" {
		query = query.Where(whereClause, args...)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Find(&entities).Error
	return entities, total, err
}

// ExecuteRaw 执行原生SQL
func (r *intExtendedRepository[T]) ExecuteRaw(ctx context.Context, sql string, values ...interface{}) error {
	return r.GetDBWithContext(ctx).Exec(sql, values...).Error
}

// QueryRaw 执行原生查询SQL
func (r *intExtendedRepository[T]) QueryRaw(ctx context.Context, dest interface{}, sql string, values ...interface{}) error {
	return r.GetDBWithContext(ctx).Raw(sql, values...).Scan(dest).Error
}
