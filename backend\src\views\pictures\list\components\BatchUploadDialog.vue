<template>
    <el-dialog title="批量上传图片到专辑" v-model="dialogVisible" :close-on-press-escape="false" width="800px"
        :close-on-click-modal="false" append-to-body>
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
            <el-form-item label="专辑" prop="album_id">
                <el-select v-model="formData.album_id" placeholder="请选择专辑" clearable filterable>
                    <el-option v-for="item in filteredAlbumOptions" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="图片" prop="files">
                <MultiFileSelector v-model="formData.files" :limit="20" @change="handleFilesChange" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-radio-group v-model="formData.status">
                    <el-radio :label="1">正常</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="submitForm" :disabled="isUploading || formData.files.length === 0">确
                    定</el-button>
                <el-button @click="closeDialog" :disabled="isUploading">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import MultiFileSelector from "@/components/filemanager/MultiFileSelector.vue";
import { batchCreatePictures } from '@/service/api/pictures/pictures';
import { ElMessage } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';

// 定义 FileItem 接口
interface Picture {
    name: string;
    url: string;
    path: string;
    size?: number;
    width?: number;
    height?: number;
    type?: string;
    extension?: string;
    is_dir?: boolean;
    [key: string]: any;
}

const props = defineProps<{
    modelValue: boolean;
    albumOptions: Array<{ value: string, label: string }>;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    'success': [data: any];
}>();

// 对话框可见性
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
};

// 表单引用
const formRef = ref();
const isUploading = ref(false);

// 专辑过滤
const albumFilterKeyword = ref('');
const filteredAlbumOptions = computed(() => {
    if (!albumFilterKeyword.value) {
        return props.albumOptions;
    }
    const keyword = albumFilterKeyword.value.toLowerCase();
    return props.albumOptions.filter(album =>
        album.label.toLowerCase().includes(keyword)
    );
});

// 表单数据
const formData = reactive({
    album_id: '',
    status: 1,
    category_id: '',
    category_name: '',
    album_title: '',
    files: [] as Picture[]
});

// 表单校验规则
const rules = {
    album_id: [{ required: true, message: '请选择专辑', trigger: 'change' }],
    files: [{ required: true, message: '请选择至少一张图片', trigger: 'change' }]
};

// 处理文件变更
const handleFilesChange = (files: Picture[]) => {
    formData.files = files;
};

// 组件挂载时加载数据
watch(
    () => props.modelValue,
    (newVal) => {
        if (newVal) {
            // 重置表单
            formData.album_id = '';
            formData.status = 1;
            formData.files = [];
            albumFilterKeyword.value = '';
        }
    },
    { immediate: true }
);

// 提交表单
const submitForm = async () => {
    if (formRef.value) {
        await formRef.value.validate(async (valid: boolean) => {
            if (valid) {
                if (formData.files.length === 0) {
                    ElMessage.warning('请至少选择一张图片');
                    return;
                }

                try {
                    isUploading.value = true;

                    // 查找选中的专辑信息
                    const selectedAlbum = props.albumOptions.find(item => item.value === formData.album_id);

                    // 准备批量请求数据
                    const picturesData = formData.files.map((file, index) => ({
                        title: file.name || `图片${index + 1}`, // 使用文件名作为标题
                        url: file.url,
                        album_id: formData.album_id,
                        status: formData.status,
                        description: '',
                        width: file.width || 0,
                        height: file.height || 0,
                        size: file.size || 0,
                        path: file.path || '',
                        sort_order: index + 1
                    }));

                    // 批量创建图片
                    const result = await batchCreatePictures({
                        data: {
                            pictures: picturesData
                        }
                    });

                    ElMessage.success(`已成功添加 ${picturesData.length} 张图片`);
                    dialogVisible.value = false;
                    emit('success', result);
                } catch (error: any) {
                    console.error('批量添加图片失败', error);
                    ElMessage.error(error.message || '批量添加图片失败');
                } finally {
                    isUploading.value = false;
                }
            }
        });
    }
};
</script>

<style lang="scss" scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
