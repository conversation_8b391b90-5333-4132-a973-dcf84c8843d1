package advs

import (
	"context"
	"frontapi/internal/models/advs"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type AdvPositionRepository interface {
	base.ExtendedRepository[advs.AdvPosition]
	FindByCode(ctx context.Context, code string) (*advs.AdvPosition, error)
}

type advPositionRepository struct {
	base.ExtendedRepository[advs.AdvPosition]
}

func NewAdvPositionRepository(db *gorm.DB) AdvPositionRepository {
	return &advPositionRepository{
		ExtendedRepository: base.NewExtendedRepository[advs.AdvPosition](db),
	}
}

func (r *advPositionRepository) FindByCode(ctx context.Context, code string) (*advs.AdvPosition, error) {
	condition := map[string]interface{}{
		"code": code,
	}
	return r.FindOneByCondition(ctx, condition, "")
}
