package videos

import (
	"frontapi/internal/api"
	"frontapi/internal/service/videos"

	"github.com/gofiber/fiber/v2"
)

// VideoController 视频控制器结构体
type VideoController struct {
	api.BaseController
	videoService    videos.VideoService
	categoryService videos.VideoCategoryService
	commentService  videos.VideoCommentService
}

// NewVideoController 创建视频控制器
func NewVideoController(
	videoService videos.VideoService,
	categoryService videos.VideoCategoryService,
	commentService videos.VideoCommentService,
) *VideoController {
	return &VideoController{
		videoService:    videoService,
		categoryService: categoryService,
		commentService:  commentService,
	}
}

// 筛选视频
func (v *VideoController) SearchVideoList(ctx *fiber.Ctx) error {
	// var req types.VideoFilter
	// if err := ctx.BodyParser(&req); err != nil {
	// 	return ctx.Status(fiber.StatusBadRequest).JSON(fiber.Map{
	// 		"message": "请求参数错误",
	// 		"error":   err.Error(),
	// 	})
	// }
	// v.videoService.SearchVideos(ctx.Context())
	return nil
}

// 获取视频列表
func (v *VideoController) GetVideoList(ctx *fiber.Ctx) error {
	reqInfo := v.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	categoryId := reqInfo.Get("categoryId").GetString()
	channelId := reqInfo.Get("channelId").GetString()
	sortBy := reqInfo.Get("sortBy").GetString()

	condition := map[string]interface{}{
		"keyword":    keyword,
		"categoryId": categoryId,
		"channelId":  channelId,
		"status":     1,
	}
	videoList, total, err := v.videoService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
	if err != nil {
		return v.InternalServerError(ctx, "获取视频列表失败: "+err.Error())
	}
	return v.SuccessList(ctx, videoList, total, pageNo, pageSize)
}
