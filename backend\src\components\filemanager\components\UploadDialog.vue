<template>
  <el-dialog
    v-model="dialogVisible"
    title="上传文件"
    width="500px"
    destroy-on-close
    @closed="handleClosed"
  >
    <div class="upload-dialog">
      <!-- Custom upload area -->
      <div
        class="custom-upload-dragger"
        :class="{
          'is-dragging': isDragging,
          'is-uploading': isUploading,
          'has-file': fileList.length > 0 && !isUploading
        }"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        @click="selectFile"
      >
        <input
          ref="fileInputRef"
          type="file"
          :accept="acceptTypes"
          class="file-input"
          @change="handleFileChange"
        />

        <template v-if="fileList.length === 0">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <span v-if="isDragging"><em>释放鼠标上传文件</em></span>
            <span v-else>拖拽文件到此处或 <em>点击上传</em></span>
          </div>
          <div class="el-upload__tip">
            {{ uploadTips.formats }}<br>
            {{ uploadTips.size }}
          </div>
        </template>

        <!-- Selected file preview -->
        <div v-else-if="!isUploading" class="selected-file-preview">
          <div class="file-icon">
            <el-icon v-if="fileType === 'image' || fileType === 'small_image'"><picture /></el-icon>
            <el-icon v-else-if="fileType === 'video'"><video-play /></el-icon>
            <el-icon v-else><document /></el-icon>
          </div>
          <div class="file-details">
            <h3 class="file-name">{{ fileList[0].name }}</h3>
            <p class="file-meta">{{ formatFileSize(fileList[0].size) }}</p>
          </div>
          <el-button
            type="danger"
            class="remove-btn"
            circle
            @click.stop="removeFile"
          >
            <el-icon><delete /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- Upload progress -->
      <div class="upload-progress" v-if="isUploading">
        <div class="progress-header">
          <div class="file-info">
            <el-icon v-if="fileType === 'image' || fileType === 'small_image'"><picture /></el-icon>
            <el-icon v-else-if="fileType === 'video'"><video-play /></el-icon>
            <el-icon v-else><document /></el-icon>
            <span class="file-name">{{ fileList[0]?.name }}</span>
          </div>
          <el-button
            v-if="cancelUpload"
            type="danger"
            size="small"
            @click="cancelAndClose"
          >
            取消上传
          </el-button>
        </div>

        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
          :stroke-width="15"
        ></el-progress>

        <div class="progress-info">
          <div class="progress-detail">
            <span class="progress-value">{{ uploadProgress }}%</span>
            <span class="progress-speed">{{ uploadSpeed }}</span>
          </div>
          <span class="remaining-time">{{ remainingTime }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelAndClose" :disabled="isUploading && !cancelUpload">取消</el-button>
        <el-button
          type="primary"
          @click="submitUpload"
          :loading="isUploading"
          :disabled="fileList.length === 0 || isUploading"
        >
          {{ isUploading ? '上传中...' : '开始上传' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue';
import { UploadFilled, Document, Delete, Picture, VideoPlay } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { uploadFileWithProgress } from '@/service/api/system/files';
import axios from 'axios';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  fileType: {
    type: String,
    default: 'all' // 'image', 'video', 'pictures', 'videos', 'static', or 'all'
  },
  currentPath: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'upload-success','uploaded']);

// Refs
const fileInputRef = ref<HTMLInputElement | null>(null);
const fileList = ref<any[]>([]);
const isDragging = ref(false);

// State
const dialogVisible = ref(props.modelValue);
const isUploading = ref(false);
const uploadProgress = ref(0);
const uploadSpeed = ref('0 KB/s');
const remainingTime = ref('计算中...');
const cancelUpload = ref<Function | null>(null);
const uploadError = ref(false);

// Computed properties
const allowedFileTypes = computed(() => {
  // Log for debugging
  console.log('UploadDialog fileType prop:', props.fileType);

  // 根据父文件夹类型确定允许上传的文件类型
  switch (props.fileType) {
    case 'video':
    case 'videos':
      // 视频目录允许上传视频和图片
      console.log('Allowing video and image uploads');
      return ['video', 'image'];
    case 'image':
    case 'pictures':
      // 图片目录只允许上传图片
      console.log('Allowing only image uploads (大图片)');
      return ['image'];
    case 'small_image':
    case 'static':
      // 小图片目录只允许上传图片
      console.log('Allowing only image uploads (小图片)');
      return ['image'];
    default:
      // 默认允许所有类型
      console.log('Allowing all file types');
      return ['all'];
  }
});

const acceptTypes = computed(() => {
  if (allowedFileTypes.value.includes('image') && !allowedFileTypes.value.includes('video')) {
    // 只允许图片
    return '.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg';
  } else if (allowedFileTypes.value.includes('video') && allowedFileTypes.value.includes('image')) {
    // 允许视频和图片
    return '.jpg,.jpeg,.png,.gif,.webp,.bmp,.svg,.mp4,.avi,.mov,.mkv,.webm,.flv,.wmv';
  } else if (allowedFileTypes.value.includes('all')) {
    // 允许所有类型
    return '*';
  }
  return '';
});

const uploadTips = computed(() => {
  if (allowedFileTypes.value.includes('image') && !allowedFileTypes.value.includes('video')) {
    // 只允许图片
    return {
      formats: '支持的图片格式：JPG/JPEG、PNG、GIF、WebP',
      size: props.fileType === 'small_image' ? '图片大小不超过2MB' : '图片大小不超过10MB'
    };
  } else if (allowedFileTypes.value.includes('video') && allowedFileTypes.value.includes('image')) {
    // 允许视频和图片
    return {
      formats: '支持的格式：视频(MP4、AVI、MOV、WebM)、图片(JPG、PNG、GIF)',
      size: '视频不超过2GB，图片不超过10MB'
    };
  } else {
    // 允许所有类型
    return {
      formats: '支持所有文件类型',
      size: '请注意文件大小限制'
    };
  }
});

const uploadStatus = computed(() => {
  if (uploadError.value) return 'exception';
  if (uploadProgress.value < 100) return '';
  return 'success';
});

// Watch for prop changes
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
  if (val) {
    console.log('UploadDialog opened with fileType:', props.fileType);
    console.log('Allowed file types:', allowedFileTypes.value);
  }
});

// Watch for dialog visibility changes
watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val);
  if (!val) {
    resetUpload();
  }
});

// Methods
const selectFile = () => {
  if (isUploading.value) return;
  fileInputRef.value?.click();
};

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const files = input.files;

  if (files && files.length > 0) {
    const file = files[0];

    // Validate file
    if (!validateFile(file)) {
      input.value = ''; // Reset input
      return;
    }

    // Add to file list
    fileList.value = [{
      name: file.name,
      size: file.size,
      raw: file
    }];
  }

  // Reset input so the same file can be selected again
  input.value = '';
};

const handleDragOver = (event: DragEvent) => {
  isDragging.value = true;
  event.dataTransfer!.dropEffect = 'copy';
};

const handleDragLeave = () => {
  isDragging.value = false;
};

const handleDrop = (event: DragEvent) => {
  isDragging.value = false;

  if (isUploading.value) return;

  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    // Take only the first file
    const file = files[0];

    // Validate file
    if (!validateFile(file)) {
      return;
    }

    // Add to file list
    fileList.value = [{
      name: file.name,
      size: file.size,
      raw: file
    }];
  }
};

const validateFile = (file: File): boolean => {
  // Check if file is empty
  if (file.size === 0) {
    ElMessage.error('文件不能为空');
    return false;
  }

  // Check file size based on file type and parent folder type
  let sizeLimit = Infinity;
  let sizeErrorMessage = '';

  // 根据文件类型和父文件夹类型确定大小限制
  const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
  const isImageFile = /^(jpg|jpeg|png|gif|webp|bmp|svg)$/.test(fileExtension);
  const isVideoFile = /^(mp4|avi|mov|mkv|webm|flv|wmv)$/.test(fileExtension);

  if (isImageFile) {
    // 图片文件大小限制
    sizeLimit = props.fileType === 'small_image' ? 2 * 1024 * 1024 : 10 * 1024 * 1024;
    sizeErrorMessage = props.fileType === 'small_image' ? '图片大小不能超过2MB' : '图片大小不能超过10MB';
  } else if (isVideoFile) {
    // 视频文件大小限制
    sizeLimit = 2 * 1024 * 1024 * 1024; // 2GB
    sizeErrorMessage = '视频大小不能超过2GB';
  }

  // 检查文件大小
  if (file.size > sizeLimit) {
    ElMessage.error(sizeErrorMessage || '文件大小超出限制');
    return false;
  }

  // 检查文件类型是否符合父文件夹的限制
  if (!isFileTypeAllowed(file)) {
    return false;
  }

  return true;
};

// Check if the file type is allowed in the current directory
const isFileTypeAllowed = (file: File): boolean => {
  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.split('.').pop() || '';

  const isImageFile = /^(jpg|jpeg|png|gif|webp|bmp|svg)$/.test(fileExtension);
  const isVideoFile = /^(mp4|avi|mov|mkv|webm|flv|wmv)$/.test(fileExtension);

  // 根据父文件夹类型检查文件类型是否允许
  switch (props.fileType) {
    case 'image':
    case 'pictures':
    case 'small_image':
    case 'static':
      // 图片和小图片目录只允许上传图片
      if (!isImageFile) {
        ElMessage.error('在图片目录中只能上传图片文件');
        return false;
      }
      break;
    case 'video':
    case 'videos':
      // 视频目录允许上传视频和图片
      if (!isVideoFile && !isImageFile) {
        ElMessage.error('在视频目录中只能上传视频或图片文件');
        return false;
      }
      break;
  }

  return true;
};

const handleProgress = (progress: number, speed: string, remaining: string) => {
  uploadProgress.value = progress;
  if (speed) uploadSpeed.value = speed;
  if (remaining) remainingTime.value = remaining;
};

const handleSuccess = ({response,data,err}: any) => {
  isUploading.value = false;
  uploadProgress.value = 100;
  console.log(response)
  if (response.data && response.data.code === 2000) {
    ElMessage.success('文件上传成功');
    emit('upload-success', response.data.data);

    // Add a small delay before closing to show the 100% completion
    setTimeout(() => {
      dialogVisible.value = false;
    }, 500);
  } else {
    const message = response.data?.message || '上传失败';
    ElMessage.error(message);
    uploadError.value = true;
  }
};

const handleError = (error: any) => {
  isUploading.value = false;
  uploadError.value = true;

  if (axios.isCancel(error)) {
    ElMessage.warning('上传已取消');
    uploadProgress.value = 0;
  } else {
    console.error('上传失败:', error);
    ElMessage.error('文件上传失败，请重试');
  }
};

const submitUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件');
    return;
  }

  const file = fileList.value[0].raw;
  if (!file) {
    ElMessage.warning('上传文件不存在');
    return;
  }

  isUploading.value = true;
  uploadProgress.value = 0;
  uploadError.value = false;

  // Cancel any existing upload
  if (cancelUpload.value) {
    cancelUpload.value();
    cancelUpload.value = null;
  }

  // Log the fileType and path being used for upload
  console.log('Uploading with fileType:', props.fileType, 'to path:', props.currentPath);

  // Start new upload with progress tracking
  const cancelFn = uploadFileWithProgress({
    file,
    fileType: props.fileType,
    path: props.currentPath,
    callbacks: {
      onProgress: (progress) => {
        uploadProgress.value = progress;
      },
      onSuccess: ({response,data,err}:any) => {
        isUploading.value = false;
        ElMessage.success('文件上传成功');
        emit('uploaded',data);
        emit('update:modelValue', false);
      },
      onError: (error) => {
        isUploading.value = false;
        uploadError.value = true;
        console.error('上传失败:', error);
        ElMessage.error('文件上传失败');
      }
    }
  });

  // Store cancel function
  cancelUpload.value = cancelFn;
};

const removeFile = () => {
  fileList.value = [];
};

const cancelAndClose = () => {
  if (isUploading.value && cancelUpload.value) {
    cancelUpload.value();
  }
  dialogVisible.value = false;
};

const resetUpload = () => {
  fileList.value = [];
  uploadProgress.value = 0;
  isUploading.value = false;
  uploadSpeed.value = '0 KB/s';
  remainingTime.value = '计算中...';
  uploadError.value = false;

  // Cancel any ongoing upload
  if (cancelUpload.value) {
    cancelUpload.value();
    cancelUpload.value = null;
  }
};

const handleClosed = () => {
  resetUpload();
};

// Format file size for display
const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(2)} KB`;
  } else if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  } else {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  }
};

// Clean up on component unmount
onBeforeUnmount(() => {
  if (cancelUpload.value) {
    cancelUpload.value();
    cancelUpload.value = null;
  }
});
</script>

<style scoped lang="scss">
.upload-dialog {
  .custom-upload-dragger {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    padding: 30px 20px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    min-height: 180px;

    &:hover {
      border-color: #409EFF;
    }

    &.is-dragging {
      border-color: #409EFF;
      background-color: rgba(64, 158, 255, 0.1);
    }

    &.is-uploading {
      cursor: not-allowed;
      opacity: 0.7;
      pointer-events: none;
    }

    &.has-file {
      border-color: #67C23A;
      background-color: rgba(103, 194, 58, 0.05);
    }
  }

  .file-input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
  }

  .el-upload__text {
    font-size: 14px;
    color: #606266;
    margin: 7px 0;

    em {
      color: #409EFF;
      font-style: normal;
    }
  }

  .el-upload__tip {
    margin-top: 10px;
    font-size: 12px;
    line-height: 18px;
    color: #909399;
    text-align: center;
  }

  .el-icon--upload {
    font-size: 48px;
    color: #409EFF;
    margin-bottom: 16px;
  }

  .selected-file-preview {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 8px;
    position: relative;

    .file-icon {
      font-size: 36px;
      color: #409EFF;
      margin-right: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .file-details {
      flex: 1;
      overflow: hidden;

      .file-name {
        margin: 0 0 5px;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .file-meta {
        margin: 0;
        font-size: 12px;
        color: #909399;
      }
    }

    .remove-btn {
      margin-left: 10px;
    }
  }

  .upload-progress {
    margin-top: 20px;
    padding: 16px;
    border-radius: 6px;
    background-color: #f8f9fa;

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .file-info {
        display: flex;
        align-items: center;

        .el-icon {
          font-size: 18px;
          margin-right: 8px;
          color: #409EFF;
        }

        .file-name {
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 300px;
        }
      }
    }

    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;

      .progress-detail {
        display: flex;
        align-items: center;

        .progress-value {
          font-weight: bold;
          margin-right: 10px;
        }

        .progress-speed {
          font-size: 12px;
          color: #606266;
        }
      }

      .remaining-time {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
