import { request } from '../../request';
import type { ApiResponse, ApiRequest } from '@/types/https';
import type { BookCategory } from '@/types/books';

/**
 * 电子书分类参数接口
 */
interface BookCategoryParams {
  page: {
    pageNo: number;
    pageSize: number;
  };
  data?: {
    name?: string;
    status?: number;
    [key: string]: any;
  };
}

/**
 * 获取电子书分类列表
 * @param params 查询参数
 */
export function getBookCategoryList(params: BookCategoryParams) {
  return request<ApiResponse<any>>({
    url: '/books/categories/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取所有可用电子书分类（不分页）
 */
export function getAllBookCategories() {
  return request<ApiResponse<BookCategory[]>>({
    url: '/books/categories/all',
    method: 'post',
    data: { data: { status: 1 } } // 假设 status 为 1 表示可用
  });
}

/**
 * 获取电子书分类详情
 * @param id 分类ID
 */
export function getBookCategoryDetail(id: string) {
  return request<ApiResponse<BookCategory>>({
    url: `/books/categories/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 创建电子书分类
 * @param data 分类数据
 */
export function createBookCategory(params: { data: any }) {
  return request<ApiResponse<any>>({
    url: '/books/categories/add',
    method: 'post',
    data: params
  });
}

/**
 * 更新电子书分类
 * @param data 分类数据
 */
export function updateBookCategory(params: { data: any }) {
  return request<ApiResponse<any>>({
    url: '/books/categories/update',
    method: 'post',
    data: params
  });
}

/**
 * 删除电子书分类
 * @param id 分类ID
 */
export function deleteBookCategory(id: string) {
  return request<ApiResponse<any>>({
    url: `/books/categories/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  })
}

/**
 * 更新电子书分类状态
 * @param id 分类ID
 * @param status 状态 (0: 禁用, 1: 正常)
 */
export function updateBookCategoryStatus(id: string, status: number) {
  return request<ApiResponse<any>>({
    url: `/books/categories/update-status`,
    method: 'post',
    data: { data: { "id": id, "status": status } }
  })
} 