package posts

import (
	"frontapi/internal/models"
	"frontapi/internal/models/users"
	"frontapi/pkg/types"
)

// Post 帖子模型
type Post struct {
	*models.ContentBaseModel
	Content      string            `json:"content" gorm:"not null;comment:内容"`
	Images       types.StringArray `json:"images" gorm:"type:json;comment:帖子图片,与视频只能存在一个"`
	ImagesJSON   string            `json:"images_json" gorm:"-"`
	Video        string            `json:"video" gorm:"comment:帖子视频,与图片只能存在一个"`
	AuthorID     string            `json:"author_id" gorm:"type:string;size:36;not null;comment:作者ID"`
	AuthorName   string            `json:"author_name" gorm:"type:string;size:255;not null;comment:作者名称"`
	AuthorAvatar string            `json:"author_avatar" gorm:"type:string;size:255;not null;comment:作者头像"`
	AuthorType   int               `json:"author_type" gorm:"default:1;comment:用户类型，1普通用户，2明星"`
	AuthorLevel  int               `json:"author_level" gorm:"-;comment:作者等级"`
	CommentCount uint64            `json:"comment_count" gorm:"default:0;comment:评论数"`
	ShareCount   uint64            `json:"share_count" gorm:"default:0;comment:分享次数"`
	Heat         uint64            `json:"heat" gorm:"default:0;comment:热度"`
	Author       *users.User       `json:"author" gorm:"-"`
	IsLiked      bool              `json:"is_liked" gorm:"-"`
}

// TableName 指定表名
func (Post) TableName() string {
	return "ly_posts"
}
