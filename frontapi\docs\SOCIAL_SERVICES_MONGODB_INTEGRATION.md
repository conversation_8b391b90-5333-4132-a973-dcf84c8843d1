# 社交服务 MongoDB 集成指南

本文档介绍如何为ExtLike、ExtCollect、ExtFollow三个社交服务统一配置MongoDB支持。

## 概述

现在三个社交服务都支持MongoDB存储方案：

- **ExtLike**: 点赞/取消点赞服务
- **ExtCollect**: 收藏/取消收藏服务  
- **ExtFollow**: 关注/取消关注服务

每个服务都可以独立选择存储方案（Redis或MongoDB），也可以统一配置。

## 统一配置示例

### 1. 环境配置

```go
package main

import (
    "context"
    "log"
    "os"
    "time"

    "frontapi/internal/service/base/extlike"
    "frontapi/internal/service/base/extcollect"
    "frontapi/internal/service/base/extfollow"
    "frontapi/internal/service/base/social"

    "github.com/go-redis/redis/v8"
    "go.mongodb.org/mongo-driver/mongo"
    "go.mongodb.org/mongo-driver/mongo/options"
)

// SocialServicesConfig 统一配置
type SocialServicesConfig struct {
    StorageType    string // "redis" 或 "mongodb"
    RedisAddr      string
    RedisPassword  string
    MongoURI       string
    MongoDatabase  string
}

func setupSocialServices() {
    config := &SocialServicesConfig{
        StorageType:   os.Getenv("SOCIAL_STORAGE_TYPE"),   // "redis" 或 "mongodb"
        RedisAddr:     os.Getenv("REDIS_ADDR"),
        RedisPassword: os.Getenv("REDIS_PASSWORD"),
        MongoURI:      os.Getenv("MONGODB_URI"),
        MongoDatabase: os.Getenv("MONGODB_DATABASE"),
    }

    // 默认值
    if config.StorageType == "" {
        config.StorageType = "redis"
    }
    if config.RedisAddr == "" {
        config.RedisAddr = "localhost:6379"
    }
    if config.MongoURI == "" {
        config.MongoURI = "mongodb://localhost:27017"
    }
    if config.MongoDatabase == "" {
        config.MongoDatabase = "frontapi_social"
    }

    // 初始化客户端
    redisClient := redis.NewClient(&redis.Options{
        Addr:     config.RedisAddr,
        Password: config.RedisPassword,
        DB:       0,
    })

    var mongoClient *mongo.Client
    var mongoDatabase *mongo.Database

    if config.StorageType == "mongodb" {
        ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
        defer cancel()

        var err error
        mongoClient, err = mongo.Connect(ctx, options.Client().ApplyURI(config.MongoURI))
        if err != nil {
            log.Printf("MongoDB连接失败，回退到Redis: %v", err)
            config.StorageType = "redis"
        } else {
            mongoDatabase = mongoClient.Database(config.MongoDatabase)
        }
    }

    // 创建服务
    likeService := createLikeService(redisClient, mongoClient, mongoDatabase, config.StorageType)
    collectService := createCollectService(redisClient, mongoClient, mongoDatabase, config.StorageType)
    followService := createFollowService(redisClient, mongoClient, mongoDatabase, config.StorageType)

    // 使用服务
    useSocialServices(likeService, collectService, followService)
}

func createLikeService(redisClient *redis.Client, mongoClient *mongo.Client, mongoDatabase *mongo.Database, storageType string) extlike.LikeService {
    if storageType == "mongodb" && mongoClient != nil {
        return extlike.CreateLikeServiceWithMongo(redisClient, mongoClient, mongoDatabase, "mongodb")
    }
    return extlike.CreateLikeService(redisClient, "redis")
}

func createCollectService(redisClient *redis.Client, mongoClient *mongo.Client, mongoDatabase *mongo.Database, storageType string) extcollect.CollectService {
    if storageType == "mongodb" && mongoClient != nil {
        return extcollect.CreateCollectServiceWithMongo(redisClient, mongoClient, mongoDatabase, "mongodb")
    }
    return extcollect.CreateCollectService(redisClient, "redis")
}

func createFollowService(redisClient *redis.Client, mongoClient *mongo.Client, mongoDatabase *mongo.Database, storageType string) extfollow.FollowService {
    if storageType == "mongodb" && mongoClient != nil {
        return extfollow.CreateFollowServiceWithMongo(redisClient, mongoClient, mongoDatabase, "mongodb")
    }
    return extfollow.CreateFollowService(redisClient, "redis")
}
```

### 2. 使用示例

```go
func useSocialServices(
    likeService extlike.LikeService,
    collectService extcollect.CollectService,
    followService extfollow.FollowService,
) {
    ctx := context.Background()

    // 点赞操作
    err := likeService.Like(ctx, "user1", "post1", "post")
    if err != nil {
        log.Printf("点赞失败: %v", err)
    }

    // 收藏操作
    err = collectService.Collect(ctx, "user1", "article1", "article")
    if err != nil {
        log.Printf("收藏失败: %v", err)
    }

    // 关注操作
    err = followService.Follow(ctx, "user1", "user2")
    if err != nil {
        log.Printf("关注失败: %v", err)
    }

    // 批量查询状态
    checkSocialStatus(ctx, likeService, collectService, followService, "user1")
}

func checkSocialStatus(
    ctx context.Context,
    likeService extlike.LikeService,
    collectService extcollect.CollectService,
    followService extfollow.FollowService,
    userID string,
) {
    // 检查点赞状态
    likeStatus, err := likeService.BatchGetLikeStatus(ctx, userID, []string{"post1", "post2"}, "post")
    if err != nil {
        log.Printf("获取点赞状态失败: %v", err)
    } else {
        log.Printf("用户%s的点赞状态: %+v", userID, likeStatus)
    }

    // 检查收藏状态
    collectStatus, err := collectService.BatchGetCollectStatus(ctx, userID, map[string]string{
        "article1": "article",
        "video1":   "video",
    })
    if err != nil {
        log.Printf("获取收藏状态失败: %v", err)
    } else {
        log.Printf("用户%s的收藏状态: %+v", userID, collectStatus)
    }

    // 检查关注状态
    followStatus, err := followService.BatchGetFollowStatus(ctx, userID, []string{"user2", "user3"})
    if err != nil {
        log.Printf("获取关注状态失败: %v", err)
    } else {
        log.Printf("用户%s的关注状态: %+v", userID, followStatus)
    }
}
```

## Docker Compose 部署

### 完整部署配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 本公司环境 - Redis方案
  app-redis:
    build: .
    environment:
      - SOCIAL_STORAGE_TYPE=redis
      - REDIS_ADDR=redis:6379
      - REDIS_PASSWORD=
    depends_on:
      - redis
    ports:
      - "8080:8080"

  # 其他公司环境 - MongoDB方案
  app-mongodb:
    build: .
    environment:
      - SOCIAL_STORAGE_TYPE=mongodb
      - REDIS_ADDR=redis:6379       # 回退方案
      - MONGODB_URI=mongodb://mongo:27017
      - MONGODB_DATABASE=frontapi_social
    depends_on:
      - redis
      - mongo
    ports:
      - "8081:8080"

  # Redis服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # MongoDB服务
  mongo:
    image: mongo:6
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=frontapi_social
    volumes:
      - mongo_data:/data/db

volumes:
  redis_data:
  mongo_data:
```

## MongoDB 数据结构设计

### 数据库结构

```
frontapi_social/
├── likes/              # 点赞记录
├── like_counts/        # 点赞计数
├── hot_ranks/          # 热度排名
├── collects/           # 收藏记录
├── collect_counts/     # 收藏计数
├── collect_ranks/      # 收藏排名
├── follows/            # 关注记录
├── influence_ranks/    # 影响力排名
└── social_stats/       # 统计数据
```

### 索引优化

```javascript
// 点赞相关索引
db.likes.createIndex({ "user_id": 1, "item_id": 1, "item_type": 1 }, { unique: true })
db.likes.createIndex({ "item_id": 1, "item_type": 1, "status": 1 })
db.likes.createIndex({ "timestamp": -1 })

// 收藏相关索引
db.collects.createIndex({ "user_id": 1, "item_id": 1, "item_type": 1 }, { unique: true })
db.collects.createIndex({ "item_id": 1, "item_type": 1, "status": 1 })
db.collects.createIndex({ "timestamp": -1 })

// 关注相关索引
db.follows.createIndex({ "follower_id": 1, "followee_id": 1 }, { unique: true })
db.follows.createIndex({ "follower_id": 1, "status": 1 })
db.follows.createIndex({ "followee_id": 1, "status": 1 })
db.follows.createIndex({ "timestamp": -1 })

// 排名相关索引
db.hot_ranks.createIndex({ "item_type": 1, "score": -1 })
db.influence_ranks.createIndex({ "score": -1 })
```

## 性能监控

### 监控指标

```go
type SocialServicesMetrics struct {
    // 操作指标
    LikeOps      int64 `json:"like_ops"`
    CollectOps   int64 `json:"collect_ops"`
    FollowOps    int64 `json:"follow_ops"`
    
    // 延迟指标
    AvgLatency   time.Duration `json:"avg_latency"`
    MaxLatency   time.Duration `json:"max_latency"`
    
    // 错误指标
    ErrorRate    float64 `json:"error_rate"`
    
    // 存储指标
    StorageType  string `json:"storage_type"`
    DBConnected  bool   `json:"db_connected"`
}

func monitorSocialServices() {
    // 实现监控逻辑
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()

    for range ticker.C {
        metrics := collectMetrics()
        log.Printf("社交服务指标: %+v", metrics)
        
        // 发送到监控系统
        sendToMonitoring(metrics)
    }
}
```

## 迁移工具

### 数据迁移脚本

```go
package main

import (
    "context"
    "log"
    "time"
)

type MigrationTool struct {
    sourceStorage  string // "redis" 或 "mongodb"
    targetStorage  string // "redis" 或 "mongodb"
    batchSize      int
    progressChan   chan float64
}

func (m *MigrationTool) MigrateSocialData(ctx context.Context) error {
    // 1. 迁移点赞数据
    if err := m.migrateLikeData(ctx); err != nil {
        return err
    }

    // 2. 迁移收藏数据
    if err := m.migrateCollectData(ctx); err != nil {
        return err
    }

    // 3. 迁移关注数据
    if err := m.migrateFollowData(ctx); err != nil {
        return err
    }

    return nil
}

func (m *MigrationTool) migrateLikeData(ctx context.Context) error {
    // 实现点赞数据迁移
    log.Println("开始迁移点赞数据...")
    
    // 分批迁移逻辑
    for {
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
            // 迁移一批数据
            batch, err := m.fetchLikeBatch()
            if err != nil {
                return err
            }
            
            if len(batch) == 0 {
                break // 迁移完成
            }
            
            if err := m.writeLikeBatch(batch); err != nil {
                return err
            }
            
            // 更新进度
            m.updateProgress(0.33)
        }
    }
    
    log.Println("点赞数据迁移完成")
    return nil
}
```

## 最佳实践总结

### 选择建议

| 场景 | 推荐方案 | 原因 |
|------|---------|------|
| 本公司生产环境 | Redis | 高性能，运维简单 |
| 其他公司部署 | MongoDB | 数据持久化，查询灵活 |
| 开发测试环境 | Redis | 快速启动，内存占用小 |
| 大数据分析需求 | MongoDB | 支持复杂查询 |
| 高并发场景 | Redis | 极致性能 |

### 配置原则

1. **统一配置**: 三个服务使用相同的存储方案
2. **回退机制**: MongoDB失败时自动回退到Redis
3. **环境隔离**: 通过环境变量控制不同环境的配置
4. **监控告警**: 实时监控服务状态和性能
5. **数据备份**: 定期备份重要数据

### 故障处理

1. **MongoDB故障**: 自动回退到Redis
2. **Redis故障**: 需要重启或恢复Redis服务
3. **网络故障**: 使用连接池和重试机制
4. **数据不一致**: 提供数据修复工具

这样的设计确保了系统在不同环境下都能稳定运行，同时提供了灵活的配置选项。 