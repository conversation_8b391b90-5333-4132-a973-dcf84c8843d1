package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// UserVip 用户会员表
type UserVip struct {
	models.BaseModel
	UserID      string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                      // 用户ID
	PackageID   string         `gorm:"column:package_id;type:string;not null;comment:套餐ID" json:"package_id"`                // 套餐ID
	OrderID     string         `gorm:"column:order_id;type:string;comment:订单ID" json:"order_id"`                             // 订单ID
	StartTime   types.JSONTime `gorm:"column:start_time;type:time;not null;comment:开始时间" json:"start_time"`                  // 开始时间
	EndTime     types.JSONTime `gorm:"column:end_time;type:time;not null;comment:结束时间" json:"end_time"`                      // 结束时间
	Status      bool           `gorm:"column:status;type:bool;default:true;comment:状态:false-已过期,true-生效中" json:"status"`     // 状态：false-已过期，true-生效中
	IsAutoRenew bool           `gorm:"column:is_auto_renew;type:bool;default:false;comment:是否自动续费" json:"is_auto_renew"`     // 是否自动续费

}

// TableName 表名
func (UserVip) TableName() string {
	return "ly_user_vips"
}
