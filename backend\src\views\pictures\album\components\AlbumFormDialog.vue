<template>
    <el-dialog v-model="dialogVisible" :title="title" width="600px" :close-on-click-modal="false"
        :close-on-press-escape="true" destroy-on-close>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right" status-icon>
            <el-form-item label="专辑名称" prop="title">
                <el-input v-model="form.title" placeholder="请输入专辑名称" />
            </el-form-item>

            <el-form-item label="封面图片" prop="cover_url">
                <url-or-file-input v-model="form.cover_url" file-type="pictures" sub-dir="albums"
                    placeholder="请上传或选择封面图片" @change="handleCoverChange" />
                <div class="form-tip">建议尺寸: 400x400px, 格式: JPG/PNG</div>
            </el-form-item>

            <el-form-item label="所属分类" prop="category_id">
                <el-select v-model="form.category_id" placeholder="请选择分类" clearable style="width: 100%">
                    <el-option v-for="category in categoryOptions" :key="category.id" :label="category.name"
                        :value="category.id" />
                </el-select>
            </el-form-item>

            <el-form-item label="排序" prop="sort_order">
                <el-input-number v-model="form.sort_order" :min="0" :max="9999" :step="1" />
                <div class="form-tip">数值越大越靠前</div>
            </el-form-item>

            <el-form-item label="状态" prop="status" v-if="isEdit">
                <el-radio-group v-model="form.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="标签" prop="tags">
                <el-tag v-for="tag in form.tags" :key="tag" class="tag-item" closable @close="removeTag(tag)">
                    {{ tag }}
                </el-tag>
                <el-input v-if="inputTagVisible" ref="tagInputRef" v-model="inputTagValue" class="tag-input"
                    size="small" @keyup.enter="handleTagInputConfirm" @blur="handleTagInputConfirm" />
                <el-button v-else class="tag-button" size="small" @click="showTagInput">
                    <el-icon>
                        <Plus />
                    </el-icon> 添加标签
                </el-button>
                <div class="form-tip">按回车确认添加标签</div>
            </el-form-item>

            <el-form-item label="描述" prop="description">
                <el-input v-model="form.description" type="textarea" :rows="4" placeholder="请输入专辑描述" />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" :loading="loading" @click="submitForm">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import type { PictureAlbum, PictureCategory } from '@/types/pictures';
import { Plus } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed, nextTick, reactive, ref, watch } from 'vue';

// Props
interface Props {
    visible: boolean;
    title: string;
    initialData?: Partial<PictureAlbum>;
    categoryOptions: PictureCategory[];
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    title: '添加专辑',
    initialData: () => ({}),
    categoryOptions: () => []
});

// Emits
const emit = defineEmits(['update:visible', 'submit']);

// 表单引用
const formRef = ref<FormInstance>();

// 对话框可见性
const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

// 是否是编辑模式
const isEdit = computed(() => !!props.initialData?.id);

// 表单数据
const form = reactive({
    id: '',
    title: '',
    cover_url: '',
    description: '',
    category_id: '',
    sort_order: 0,
    status: 1,
    tags: [] as string[]
});

// 表单验证规则
const rules = reactive<FormRules>({
    title: [
        { required: true, message: '请输入专辑名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    cover_url: [
        { required: true, message: '请上传封面图片', trigger: 'change' }
    ],
    description: [
        { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
    ]
});

// 加载状态
const loading = ref(false);

// 标签输入相关
const inputTagVisible = ref(false);
const inputTagValue = ref('');
const tagInputRef = ref<HTMLInputElement>();

// 监听初始数据变化
watch(() => props.initialData, (newVal) => {
    if (newVal) {
        // 重置表单
        form.id = newVal.id || '';
        form.title = newVal.title || '';
        form.cover_url = newVal.cover_url || '';
        form.description = newVal.description || '';
        form.category_id = newVal.category_id || '';
        form.sort_order = newVal.sort_order || 0;
        form.status = newVal.status ?? 1;

        // 处理标签
        form.tags = [];
        if (newVal.tags_json) {
            try {
                const tags = JSON.parse(newVal.tags_json);
                if (Array.isArray(tags)) {
                    form.tags = tags;
                }
            } catch (error) {
                console.error('解析标签失败:', error);
            }
        }
    }
}, { immediate: true, deep: true });

// 处理封面变更
const handleCoverChange = (url: string) => {
    form.cover_url = url;
};

// 显示标签输入框
const showTagInput = () => {
    inputTagVisible.value = true;
    nextTick(() => {
        tagInputRef.value?.focus();
    });
};

// 处理标签输入确认
const handleTagInputConfirm = () => {
    const value = inputTagValue.value.trim();
    if (value) {
        if (!form.tags.includes(value)) {
            form.tags.push(value);
        } else {
            ElMessage.warning('标签已存在');
        }
    }
    inputTagVisible.value = false;
    inputTagValue.value = '';
};

// 移除标签
const removeTag = (tag: string) => {
    const index = form.tags.indexOf(tag);
    if (index !== -1) {
        form.tags.splice(index, 1);
    }
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    try {
        const valid = await formRef.value.validate();
        if (valid) {
            loading.value = true;

            // 构建提交数据
            const submitData: Partial<PictureAlbum> = {
                ...form,
                tags_json: JSON.stringify(form.tags)
            };

            // 提交表单
            emit('submit', submitData);

            // 关闭对话框
            setTimeout(() => {
                loading.value = false;
                dialogVisible.value = false;
            }, 300);
        }
    } catch (error) {
        ElMessage.error('请填写必填项');
    }
};
</script>

<style scoped lang="scss">
.form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
}

.tag-item {
    margin-right: 8px;
    margin-bottom: 8px;
}

.tag-input {
    width: 120px;
    margin-right: 8px;
    vertical-align: bottom;
}

.tag-button {
    margin-bottom: 8px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
}
</style>