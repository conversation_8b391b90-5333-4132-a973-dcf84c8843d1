<template>
    <div class="album-search-bar">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
            <el-form-item label="专辑名称">
                <el-input v-model="searchForm.title" placeholder="请输入专辑名称" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
                    <el-option label="正常" :value="1" />
                    <el-option label="禁用" :value="0" />
                    <el-option label="已删除" :value="-4" />
                </el-select>
            </el-form-item>

            <el-form-item label="分类">
                <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable style="width: 180px;">
                    <el-option v-for="category in categoryOptions" :key="category.id" :label="category.name"
                        :value="category.id" />
                </el-select>
            </el-form-item>

            <el-form-item label="创建时间">
                <el-date-picker
                    v-model="dateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    :shortcuts="dateRangeShortcuts"
                    style="width: 300px;"
                />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon>
                        <Search />
                    </el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    重置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import type { PictureCategory } from '@/types/pictures';
import { Refresh, Search } from '@element-plus/icons-vue';
import { reactive, ref, watch } from 'vue';

// 搜索表单类型定义
export interface AlbumSearchForm {
    title: string;
    status?: number;
    category_id?: string;
    start_date: string;
    end_date: string;
}

// Props
interface Props {
    modelValue?: AlbumSearchForm;
    categoryOptions: PictureCategory[];
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
        title: '',
        status: undefined,
        category_id: undefined,
        start_date: '',
        end_date: '',
    }),
    categoryOptions: () => []
});

// Emits
interface Emits {
    search: [params: AlbumSearchForm];
    reset: [];
    'update:modelValue': [value: AlbumSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<AlbumSearchForm>({ ...props.modelValue });

// 日期范围
const dateRange = ref<string[]>([]);

// 日期快捷选项
const dateRangeShortcuts = [
    {
        text: '最近一周',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
        }
    },
    {
        text: '最近一个月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
        }
    },
    {
        text: '最近三个月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end];
        }
    }
];

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
    emit('update:modelValue', { ...newValue });
}, { deep: true });

// 监听props变化，更新本地表单
watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        Object.assign(searchForm, newValue);
        // 设置日期范围
        if (newValue.start_date && newValue.end_date) {
            dateRange.value = [newValue.start_date, newValue.end_date];
        } else {
            dateRange.value = [];
        }
    }
}, { deep: true, immediate: true });

// 监听日期范围变化
watch(dateRange, (newRange) => {
    if (newRange && newRange.length === 2) {
        searchForm.start_date = newRange[0];
        searchForm.end_date = newRange[1];
    } else {
        searchForm.start_date = '';
        searchForm.end_date = '';
    }
});

// 搜索
const handleSearch = () => {
    emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
    Object.assign(searchForm, {
        title: '',
        status: undefined,
        category_id: undefined,
        start_date: '',
        end_date: '',
    });
    dateRange.value = [];
    emit('reset');
};


</script>

<style scoped lang="scss">
.album-search-bar {
    margin-bottom: 16px;

    .el-form {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
    }

    .el-form-item {
        margin-bottom: 8px;
    }
}
</style>