package books

import (
	"frontapi/internal/api"
	bookSrv "frontapi/internal/service/books"
)

// BookmarkController 电子书书签控制器
type BookmarkController struct {
	api.BaseController
	bookmarkService bookSrv.BookmarkService
}

// NewBookmarkController 创建电子书书签控制器
func NewBookmarkController(bookmarkService bookSrv.BookmarkService) *BookmarkController {
	return &BookmarkController{
		bookmarkService: bookmarkService,
	}
}
