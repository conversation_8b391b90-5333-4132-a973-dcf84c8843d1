<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户详情"
    width="700px"
  >
    <div v-if="currentUser" class="user-detail">
      <div class="user-header mb-4 flex items-center">
        <el-avatar :size="80" :src="currentUser.avatar || defaultAvatar" class="mr-4"></el-avatar>
        <div>
          <h3 class="text-xl font-bold">{{ currentUser.nickname || '未设置昵称' }}</h3>
          <p class="text-gray-500">{{ currentUser.username }}</p>
          <div class="mt-2">
            <el-tag v-if="currentUser.user_level === 1" type="info">普通用户</el-tag>
            <el-tag v-else-if="currentUser.user_level === 2" type="success">VIP用户</el-tag>
            <el-tag v-else-if="currentUser.user_level === 3" type="primary">蓝标用户</el-tag>
          </div>
        </div>
      </div>

      <el-divider>基本信息</el-divider>

      <div class="grid grid-cols-2 gap-4">
        <div class="detail-item">
          <span class="label">用户ID:</span>
          <span class="value">{{ currentUser.id }}</span>
        </div>
        <div class="detail-item">
          <span class="label">用户名:</span>
          <span class="value">{{ currentUser.username }}</span>
        </div>
        <div class="detail-item">
          <span class="label">昵称:</span>
          <span class="value">{{ currentUser.nickname || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">性别:</span>
          <span class="value">
            {{ currentUser.gender === 0 ? '未知' : currentUser.gender === 1 ? '男' : '女' }}
          </span>
        </div>
        <div class="detail-item">
          <span class="label">手机号:</span>
          <span class="value">{{ currentUser.phone || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">邮箱:</span>
          <span class="value">{{ currentUser.email || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">位置:</span>
          <span class="value">{{ currentUser.location || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">推荐码:</span>
          <span class="value">{{ currentUser.recommend_code || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">注册时间:</span>
          <span class="value">{{ currentUser.reg_time }}</span>
        </div>
        <div class="detail-item">
          <span class="label">最后登录:</span>
          <span class="value">{{ currentUser.last_login_time }}</span>
        </div>
        <div class="detail-item">
          <span class="label">最后活跃:</span>
          <span class="value">{{ currentUser.last_active_time }}</span>
        </div>
        <div class="detail-item">
          <span class="label">首次登录IP:</span>
          <span class="value">{{ currentUser.first_login_ip || '未记录' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">首次登录设备:</span>
          <span class="value">{{ currentUser.first_login_device || '未记录' }}</span>
        </div>
        <div class="detail-item">
          <span class="label">状态:</span>
          <span class="value">
            <el-tag v-if="currentUser.status === 1" type="success">正常</el-tag>
            <el-tag v-else-if="currentUser.status === 0" type="danger">禁用</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </span>
        </div>
      </div>

      <el-divider>会员信息</el-divider>

      <div class="grid grid-cols-2 gap-4">
        <div class="detail-item">
          <span class="label">用户等级:</span>
          <span class="value">
            {{ currentUser.user_level === 1 ? '普通用户' :
               currentUser.user_level === 2 ? 'VIP用户' :
               currentUser.user_level === 3 ? '蓝标用户' : '未知' }}
          </span>
        </div>
        <div class="detail-item">
          <span class="label">会员等级:</span>
          <span class="value">
            {{ currentUser.member_level === 0 ? '非会员' : '会员' }}
          </span>
        </div>
        <div class="detail-item">
          <span class="label">会员积分:</span>
          <span class="value">{{ currentUser.score }}</span>
        </div>
        <div class="detail-item">
          <span class="label">是否认证:</span>
          <span class="value">
            <el-tag v-if="currentUser.is_verified === 1" type="success">已认证</el-tag>
            <el-tag v-else type="info">未认证</el-tag>
          </span>
        </div>
      </div>

      <el-divider>创作者信息</el-divider>

      <div class="grid grid-cols-2 gap-4">
        <div class="detail-item">
          <span class="label">是否创作者:</span>
          <span class="value">
            <el-tag v-if="currentUser.is_content_creator === 1" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </span>
        </div>
        <div class="detail-item">
          <span class="label">创作者等级:</span>
          <span class="value">{{ currentUser.creator_level }}</span>
        </div>
        <div class="detail-item">
          <span class="label">认证时间:</span>
          <span class="value">{{ currentUser.creator_verified_time || '未认证' }}</span>
        </div>
      </div>

      <el-divider>消费信息</el-divider>

      <div class="grid grid-cols-2 gap-4">
        <div class="detail-item">
          <span class="label">总消费平台币:</span>
          <span class="value">{{ currentUser.total_coin_consumed }}</span>
        </div>
        <div class="detail-item">
          <span class="label">总获得积分:</span>
          <span class="value">{{ currentUser.total_points_earned }}</span>
        </div>
      </div>

      <el-divider>其他信息</el-divider>

      <div class="mb-4">
        <div class="label">个人简介:</div>
        <div class="value-box">{{ currentUser.bio || '暂无简介' }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { UserItem } from '@/types/users';
import { ref, watch } from 'vue';

const props = defineProps<{
  visible: boolean;
  userData: UserItem | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
}>();

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

// 弹窗相关
const dialogVisible = ref(props.visible);
const currentUser = ref(props.userData);

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
  }
);

// 监听userData变化
watch(
  () => props.userData,
  (newVal) => {
    currentUser.value = newVal;
  }
);

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (newVal) => {
    emit('update:visible', newVal);
  }
);
</script>

<style scoped lang="scss">
.detail-item {
  display: flex;
  align-items: center;
  padding: 8px 0;

  .label {
    color: #606266;
    font-weight: 500;
    margin-right: 12px;
    min-width: 80px;
  }

  .value {
    color: #303133;
  }
}

.value-box {
  margin-top: 8px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #303133;
  min-height: 60px;
  white-space: pre-wrap;
}
</style>
