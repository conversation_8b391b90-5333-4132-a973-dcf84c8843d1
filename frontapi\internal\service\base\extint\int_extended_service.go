package base

import (
	"context"
	"errors"
	"fmt"
	"frontapi/pkg/redis"
	"reflect"
	"time"

	"frontapi/internal/hooks"
	"frontapi/internal/models"
	"frontapi/internal/repository/base/extint"
	baseService "frontapi/internal/service/base"
	"frontapi/pkg/types"
)

// IntExtendedService 针对int类型ID的扩展服务结构
type IntExtendedService[T models.IntBaseModelConstraint] struct {
	*IntBaseService[T]
	extendedRepo extint.IntExtendedRepository[T]
	entityType   string
}

// IIntExtendedService 针对int类型ID的扩展服务接口
type IIntExtendedService[T models.IntBaseModelConstraint] interface {
	IIntBaseService[T]

	// 扩展的业务方法
	UpdateViewCount(ctx context.Context, id int, increment int64) error
	UpdateLikeCount(ctx context.Context, id int, increment int64) error
	UpdateCommentCount(ctx context.Context, id int, increment int64) error
	UpdateShareCount(ctx context.Context, id int, increment int64) error

	// 软删除相关
	SoftDelete(ctx context.Context, id int) error
	BatchSoftDelete(ctx context.Context, ids []int) error
	Restore(ctx context.Context, id int) error
	BatchRestore(ctx context.Context, ids []int) error

	// 排序相关
	UpdateSortOrder(ctx context.Context, id int, sortOrder int) error
	BatchUpdateSortOrder(ctx context.Context, updates map[int]int) error

	// 特色/推荐相关
	SetFeatured(ctx context.Context, id int, featured bool) error
	BatchSetFeatured(ctx context.Context, ids []int, featured bool) error

	// 增强的查询方法
	ListWithPreload(ctx context.Context, condition map[string]interface{}, preloads []string, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error)
	ListWithCondition(ctx context.Context, condition map[string]interface{}, orderBy string, limit int, useCache bool) ([]*T, error)
	ListDeleted(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)

	// 原生SQL执行
	ExecuteRawSQL(ctx context.Context, sql string, args ...interface{}) error
	QueryRawSQL(ctx context.Context, dest interface{}, sql string, args ...interface{}) error

	// 获取扩展仓库
	GetExtendedRepo() extint.IntExtendedRepository[T]
}

// NewIntExtendedService 创建针对int类型ID的扩展服务实例
// NewIntExtendedService 创建整型ID扩展服务实例
// 参数:
//   - repo: 整型ID扩展仓储接口实例
//   - entityType: 实体类型标识
//
// 返回:
//   - *IntExtendedService[T]: 整型ID扩展服务实例
func NewIntExtendedService[T models.IntBaseModelConstraint](
	repo extint.IntExtendedRepository[T],
	entityType string,
) *IntExtendedService[T] {
	return &IntExtendedService[T]{
		IntBaseService: NewIntBaseService[T](repo, entityType),
		extendedRepo:   repo,
		entityType:     entityType,
	}
}

// UpdateViewCount 更新浏览次数
func (s *IntExtendedService[T]) UpdateViewCount(ctx context.Context, id int, increment int64) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.UpdateCount(ctx, id, "view_count", increment)
	if err != nil {
		return fmt.Errorf("更新浏览次数失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// UpdateLikeCount 更新点赞次数
func (s *IntExtendedService[T]) UpdateLikeCount(ctx context.Context, id int, increment int64) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.UpdateCount(ctx, id, "like_count", increment)
	if err != nil {
		return fmt.Errorf("更新点赞次数失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// UpdateCommentCount 更新评论次数
func (s *IntExtendedService[T]) UpdateCommentCount(ctx context.Context, id int, increment int64) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.UpdateCount(ctx, id, "comment_count", increment)
	if err != nil {
		return fmt.Errorf("更新评论次数失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// UpdateShareCount 更新分享次数
func (s *IntExtendedService[T]) UpdateShareCount(ctx context.Context, id int, increment int64) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.UpdateCount(ctx, id, "share_count", increment)
	if err != nil {
		return fmt.Errorf("更新分享次数失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// SoftDelete 软删除
func (s *IntExtendedService[T]) SoftDelete(ctx context.Context, id int) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	// 先获取实体用于钩子
	entity, err := s.extendedRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取实体失败: %w", err)
	}

	// 执行删除前钩子
	if err := s.IntBaseService.hookManager.ExecuteHooks(ctx, hooks.BeforeDelete, entity); err != nil {
		return fmt.Errorf("软删除前钩子执行失败: %w", err)
	}

	// 设置删除时间和删除状态
	now := types.JSONTime(time.Now())
	if ptr, ok := any(entity).(interface{ SetDeletedAt(*types.JSONTime) }); ok {
		ptr.SetDeletedAt(&now)
	}

	// 更新实体
	err = s.extendedRepo.Update(ctx, entity)
	if err != nil {
		return fmt.Errorf("软删除失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)

	// 执行删除后钩子
	if err := s.IntBaseService.hookManager.ExecuteHooks(ctx, hooks.AfterDelete, entity); err != nil {
		// 删除后钩子失败只记录日志，不影响删除结果
		// 这里可以添加日志记录
	}

	return nil
}

// BatchSoftDelete 批量软删除
func (s *IntExtendedService[T]) BatchSoftDelete(ctx context.Context, ids []int) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	repo := s.GetExtendedRepo()
	err := repo.BatchSoftDelete(ctx, ids)
	if err != nil {
		return fmt.Errorf("批量软删除失败: %w", err)
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return nil
}

// Restore 恢复软删除的记录
func (s *IntExtendedService[T]) Restore(ctx context.Context, id int) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.Restore(ctx, id)
	if err != nil {
		return fmt.Errorf("恢复记录失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// BatchRestore 批量恢复软删除的记录
func (s *IntExtendedService[T]) BatchRestore(ctx context.Context, ids []int) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	// 逐个恢复，因为仓库层没有批量恢复方法
	repo := s.GetExtendedRepo()
	for _, id := range ids {
		err := repo.Restore(ctx, id)
		if err != nil {
			return fmt.Errorf("恢复记录ID %d失败: %w", id, err)
		}
		s.deleteCacheByID(id)
	}

	return nil
}

// UpdateSortOrder 更新排序
func (s *IntExtendedService[T]) UpdateSortOrder(ctx context.Context, id int, sortOrder int) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.UpdateSortOrder(ctx, id, sortOrder)
	if err != nil {
		return fmt.Errorf("更新排序失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// BatchUpdateSortOrder 批量更新排序
func (s *IntExtendedService[T]) BatchUpdateSortOrder(ctx context.Context, updates map[int]int) error {
	if len(updates) == 0 {
		return errors.New("更新数据不能为空")
	}

	// 逐个更新，因为仓库层没有批量更新排序方法
	repo := s.GetExtendedRepo()
	for id, sortOrder := range updates {
		err := repo.UpdateSortOrder(ctx, id, sortOrder)
		if err != nil {
			return fmt.Errorf("更新排序ID %d失败: %w", id, err)
		}
		s.deleteCacheByID(id)
	}

	return nil
}

// SetFeatured 设置特色/推荐状态
func (s *IntExtendedService[T]) SetFeatured(ctx context.Context, id int, featured bool) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	repo := s.GetExtendedRepo()
	err := repo.SetFeatured(ctx, id, featured)
	if err != nil {
		return fmt.Errorf("设置特色状态失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)
	return nil
}

// BatchSetFeatured 批量设置特色/推荐状态
func (s *IntExtendedService[T]) BatchSetFeatured(ctx context.Context, ids []int, featured bool) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	repo := s.GetExtendedRepo()
	err := repo.BatchSetFeatured(ctx, ids, featured)
	if err != nil {
		return fmt.Errorf("批量设置特色状态失败: %w", err)
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return nil
}

// ListWithPreload 带预加载的列表查询
func (s *IntExtendedService[T]) ListWithPreload(ctx context.Context, condition map[string]interface{}, preloads []string, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := baseService.BuildListCacheKey("entity_preload", condition, page, pageSize)
		var result struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}
		if redis.GetJSON(cacheKey, &result) == nil {
			return result.Items, result.Total, nil
		}
	}

	// 从数据库获取 - 调整参数顺序以匹配仓库接口
	repo := s.GetExtendedRepo()
	items, total, err := repo.ListWithPreload(ctx, preloads, condition, orderBy, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取预加载列表失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := baseService.BuildListCacheKey("entity_preload", condition, page, pageSize)
		result := struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}{
			Items: items,
			Total: total,
		}
		redis.SetJSON(cacheKey, result, s.cacheTTL)
	}

	return items, total, nil
}

// ListWithCondition 带条件的列表查询（不分页）
func (s *IntExtendedService[T]) ListWithCondition(ctx context.Context, condition map[string]interface{}, orderBy string, limit int, useCache bool) ([]*T, error) {
	if limit <= 0 || limit > 1000 {
		limit = 100
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := baseService.BuildListCacheKey("entity_condition", condition, 0, limit)
		var items []*T
		if redis.GetJSON(cacheKey, &items) == nil {
			return items, nil
		}
	}

	// 从数据库获取 - 使用仓库的FindByCondition方法
	repo := s.GetExtendedRepo()
	items, err := repo.FindByCondition(ctx, condition, orderBy)
	if err != nil {
		return nil, fmt.Errorf("获取条件列表失败: %w", err)
	}

	// 限制返回数量
	if len(items) > limit {
		items = items[:limit]
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := baseService.BuildListCacheKey("entity_condition", condition, 0, limit)
		redis.SetJSON(cacheKey, items, s.cacheTTL)
	}

	return items, nil
}

// ListDeleted 获取已删除的记录列表
func (s *IntExtendedService[T]) ListDeleted(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 添加删除条件
	if condition == nil {
		condition = make(map[string]interface{})
	}
	condition["deleted_at IS NOT NULL"] = true

	repo := s.GetExtendedRepo()
	return repo.List(ctx, condition, orderBy, page, pageSize)
}

// ExecuteRawSQL 执行原生SQL（无返回结果）
func (s *IntExtendedService[T]) ExecuteRawSQL(ctx context.Context, sql string, args ...interface{}) error {
	repo := s.GetExtendedRepo()
	err := repo.ExecuteRaw(ctx, sql, args...)
	if err != nil {
		return fmt.Errorf("执行SQL失败: %w", err)
	}
	return nil
}

// QueryRawSQL 查询原生SQL（有返回结果）
func (s *IntExtendedService[T]) QueryRawSQL(ctx context.Context, dest interface{}, sql string, args ...interface{}) error {
	repo := s.GetExtendedRepo()
	err := repo.QueryRaw(ctx, dest, sql, args...)
	if err != nil {
		return fmt.Errorf("查询SQL失败: %w", err)
	}
	return nil
}

// GetExtendedRepo 获取扩展仓库实例
func (s *IntExtendedService[T]) GetExtendedRepo() extint.IntExtendedRepository[T] {
	return s.extendedRepo
}

// deleteCacheByID 根据ID删除缓存（重写父类方法以访问私有字段）
func (s *IntExtendedService[T]) deleteCacheByID(id int) {
	entityType := reflect.TypeOf((*T)(nil)).Elem().Name()
	cacheKey := fmt.Sprintf("%s:%d", entityType, id)
	redis.Del(cacheKey)
}

// GetHookManager 获取钩子管理器
func (s *IntExtendedService[T]) GetHookManager() *hooks.ServiceHookManager {
	return s.hookManager
}

// RegisterHook 注册钩子
func (s *IntExtendedService[T]) RegisterHook(hookType hooks.HookType, hookFunc hooks.HookFunc) {
	s.hookManager.RegisterCustomHook(hookType, "custom", "custom hook", 50, hookFunc)
}

// Create 重写创建方法以支持扩展服务层面的钩子
func (s *IntExtendedService[T]) Create(ctx context.Context, entity *T) (int, error) {
	// 执行扩展服务的创建前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, entity); err != nil {
		return 0, fmt.Errorf("扩展服务创建前钩子执行失败: %w", err)
	}

	// 调用基础服务的创建方法
	id, err := s.IntBaseService.Create(ctx, entity)
	if err != nil {
		return 0, err
	}

	// 执行扩展服务的创建后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterCreate, entity); err != nil {
		// 创建后钩子失败只记录日志，不影响创建结果
		// 这里可以添加日志记录
	}

	return id, nil
}

// Update 重写更新方法以支持扩展服务层面的钩子
func (s *IntExtendedService[T]) Update(ctx context.Context, entity *T) error {
	// 执行扩展服务的更新前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, entity); err != nil {
		return fmt.Errorf("扩展服务更新前钩子执行失败: %w", err)
	}

	// 调用基础服务的更新方法
	err := s.IntBaseService.Update(ctx, entity)
	if err != nil {
		return err
	}

	// 执行扩展服务的更新后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, entity); err != nil {
		// 更新后钩子失败只记录日志，不影响更新结果
		// 这里可以添加日志记录
	}

	return nil
}

// Delete 重写删除方法以支持扩展服务层面的钩子
func (s *IntExtendedService[T]) Delete(ctx context.Context, id int) error {
	// 获取实体用于钩子执行
	entity, err := s.GetByID(ctx, id, false)
	if err != nil {
		return fmt.Errorf("获取实体失败: %w", err)
	}

	// 执行扩展服务的删除前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeDelete, entity); err != nil {
		return fmt.Errorf("扩展服务删除前钩子执行失败: %w", err)
	}

	// 调用基础服务的删除方法
	err = s.IntBaseService.Delete(ctx, id)
	if err != nil {
		return err
	}

	// 执行扩展服务的删除后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterDelete, entity); err != nil {
		// 删除后钩子失败只记录日志，不影响删除结果
		// 这里可以添加日志记录
	}

	return nil
}
