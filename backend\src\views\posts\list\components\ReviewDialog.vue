<template>
    <el-dialog v-model="dialogVisible" width="500px" style="padding: 2px;" :close-on-click-modal="false" :title="title"
        @closed="resetForm" @update:model-value="$emit('update:visible', $event)" destroy-on-close>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right">
            <el-form-item label="审核状态" prop="status" style="margin-bottom: 20px;">
                <el-radio-group v-model="form.status">
                    <el-radio :value="2">同意</el-radio>
                    <el-radio :value="-2">拒绝</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item v-if="form.status === -2" label="拒绝原因" prop="rejectReason">
                <el-input v-model="form.rejectReason" type="textarea" :rows="4" placeholder="请输入拒绝原因"></el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';

// Props
interface Props {
    visible: boolean;
    postIds: string[];
    status: number; // 2=通过, -2=拒绝
    isBatch: boolean; // 是否批量操作
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    postIds: () => [],
    status: 2,
    isBatch: false
});

// Emits
interface Emits {
    'update:visible': [value: boolean];
    'confirm': [status: number, ids: string[], rejectReason?: string];
}

const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<FormInstance>();

// 对话框可见状态
const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

// 对话框标题
const title = computed(() => {
    const action = props.status === 2 ? '通过' : '拒绝';
    return props.isBatch ? `批量${action}帖子` : `${action}帖子`;
});

// 表单数据
const form = reactive({
    status: props.status,
    rejectReason: '',
    remark: ''
});

// 监听status变化
watch(() => props.status, (val) => {
    form.status = val;
}, { immediate: true });

// 验证规则
const rules = {
    rejectReason: [
        { required: props.status === -2, message: '请输入拒绝原因', trigger: 'blur' },
        { min: 2, max: 200, message: '拒绝原因长度在 2 到 200 个字符', trigger: 'blur' }
    ]
};

// 提交状态
const submitting = ref(false);

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.rejectReason = '';
    form.remark = '';
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    // 如果是拒绝状态，需要验证表单
    if (form.status === 2) {
        await formRef.value.validate((valid, fields) => {
            if (valid) {
                confirmSubmit();
            } else {
                console.log('表单验证失败', fields);
            }
        });
    } else {
        // 通过状态直接提交
        confirmSubmit();
    }
};

// 确认提交
const confirmSubmit = () => {
    if (form.status === -2 && form.rejectReason === '') {
        ElMessage.warning('请输入拒绝原因');
        return;
    }
    submitting.value = true;
    try {
        emit('confirm', form.status, props.postIds, form.status === -2 ? form.rejectReason : "");
        dialogVisible.value = false;
        resetForm();
    } catch (error) {
        console.error('提交失败', error);
        ElMessage.error('提交失败');
    } finally {
        submitting.value = false;
    }
};
</script>

<style scoped lang="scss">
.el-dialog {
    padding: 2px;

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }

    .el-form {
        min-height: 150px;

        .el-form-item {
            margin-bottom: 20px;
        }

        .el-textarea {
            width: 100%;
        }
    }


}
</style>