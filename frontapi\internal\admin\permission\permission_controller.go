package permission

import (
	"frontapi/internal/admin"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// PermissionController 权限控制器（简化版，使用模拟数据）
type PermissionController struct {
	admin.BaseController
}

func NewPermissionController() *PermissionController {
	return &PermissionController{}
}

// PermissionInfo 权限信息结构体
type PermissionInfo struct {
	ID     int    `json:"id"`
	MenuID int    `json:"menu_id"`
	Name   string `json:"name"`
	Code   string `json:"code"`
	Status int    `json:"status"`
}

// CreatePermission 创建权限
func (p *PermissionController) CreatePermission(c *fiber.Ctx) error {
	// 解析请求数据
	var req struct {
		MenuID int    `json:"menu_id" validate:"required"`
		Name   string `json:"name" validate:"required"`
		Code   string `json:"code" validate:"required"`
		Status int    `json:"status"`
	}
	err := validator.ValidateDataWrapper(c, &req)
	if err != nil {
		return p.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 模拟创建权限
	permission := PermissionInfo{
		ID:     100, // 模拟生成的ID
		MenuID: req.MenuID,
		Name:   req.Name,
		Code:   req.Code,
		Status: req.Status,
	}

	return utils.Success(c, fiber.Map{
		"id": permission.ID,
	}, "创建权限成功")
}

// UpdatePermission 更新权限
func (p *PermissionController) UpdatePermission(c *fiber.Ctx) error {
	// 获取权限ID
	id := c.Params("id")
	if id == "" {
		return utils.BadRequest(c, "无效的权限ID", nil)
	}

	// 解析请求数据
	var req struct {
		MenuID int    `json:"menu_id"`
		Name   string `json:"name"`
		Code   string `json:"code"`
		Status int    `json:"status"`
	}

	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数错误", nil)
	}

	// 模拟更新成功
	return utils.Success(c, nil, "更新权限成功")
}

// DeletePermission 删除权限
func (p *PermissionController) DeletePermission(c *fiber.Ctx) error {
	// 获取权限ID
	id := c.Params("id")
	if id == "" {
		return utils.BadRequest(c, "无效的权限ID", nil)
	}

	// 模拟删除成功
	return utils.Success(c, nil, "删除权限成功")
}

// GetPermission 获取权限详情
func (p *PermissionController) GetPermission(c *fiber.Ctx) error {
	// 获取权限ID
	id, err := p.GetId(c)
	if id == "" || err != nil {
		return p.BadRequest(c, "无效的权限ID", nil)
	}

	// 模拟权限数据
	permission := PermissionInfo{
		ID:     1,
		MenuID: 1,
		Name:   "查看用户",
		Code:   "user:view",
		Status: 1,
	}

	return utils.Success(c, permission, "获取权限成功")
}

// ListPermissions 获取权限列表
func (p *PermissionController) ListPermissions(c *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		MenuID int    `json:"menu_id"`
		Name   string `json:"name"`
		Code   string `json:"code"`
		Status int    `json:"status"`
		Page   int    `json:"page" default:"1"`
		Size   int    `json:"size" default:"10"`
	}

	if err := c.BodyParser(&req); err != nil {
		req.Page = 1
		req.Size = 10
	}

	// 模拟权限列表数据
	permissions := []PermissionInfo{
		{ID: 1, MenuID: 1, Name: "查看用户", Code: "user:view", Status: 1},
		{ID: 2, MenuID: 1, Name: "创建用户", Code: "user:create", Status: 1},
		{ID: 3, MenuID: 1, Name: "编辑用户", Code: "user:edit", Status: 1},
		{ID: 4, MenuID: 1, Name: "删除用户", Code: "user:delete", Status: 1},
	}

	return utils.Success(c, fiber.Map{
		"list":  permissions,
		"total": len(permissions),
		"page":  req.Page,
		"size":  req.Size,
	}, "获取权限列表成功")
}
