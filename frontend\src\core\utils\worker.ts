/**
 * Worker工具函数
 */

/**
 * Worker消息类型
 */
export interface WorkerMessage<T = any> {
  id: string
  type: string
  data: T
  timestamp: number
}

/**
 * Worker响应类型
 */
export interface WorkerResponse<T = any> {
  id: string
  success: boolean
  data?: T
  error?: string
  timestamp: number
}

/**
 * Worker任务类型
 */
export interface WorkerTask<T = any, R = any> {
  id: string
  type: string
  data: T
  resolve: (result: R) => void
  reject: (error: Error) => void
  timeout?: number
  startTime: number
}

/**
 * Worker配置选项
 */
export interface WorkerOptions {
  name?: string
  type?: 'classic' | 'module'
  credentials?: 'omit' | 'same-origin' | 'include'
  timeout?: number
  maxRetries?: number
  debug?: boolean
}

/**
 * Worker池配置
 */
export interface WorkerPoolOptions extends WorkerOptions {
  maxWorkers?: number
  minWorkers?: number
  idleTimeout?: number
  taskTimeout?: number
  queueLimit?: number
}

/**
 * Worker统计信息
 */
export interface WorkerStats {
  totalTasks: number
  completedTasks: number
  failedTasks: number
  averageExecutionTime: number
  activeWorkers: number
  idleWorkers: number
  queuedTasks: number
}

/**
 * 增强的Worker类
 */
export class EnhancedWorker {
  private worker: Worker | null = null
  private tasks = new Map<string, WorkerTask>()
  private options: Required<WorkerOptions>
  private isTerminated = false
  private stats = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    totalExecutionTime: 0
  }

  constructor(scriptURL: string | URL, options: WorkerOptions = {}) {
    this.options = {
      name: 'EnhancedWorker',
      type: 'classic',
      credentials: 'same-origin',
      timeout: 30000,
      maxRetries: 3,
      debug: false,
      ...options
    }

    this.createWorker(scriptURL)
  }

  /**
   * 创建Worker
   * @param scriptURL 脚本URL
   */
  private createWorker(scriptURL: string | URL): void {
    try {
      this.worker = new Worker(scriptURL, {
        name: this.options.name,
        type: this.options.type,
        credentials: this.options.credentials
      })

      this.worker.onmessage = (event) => {
        this.handleMessage(event.data)
      }

      this.worker.onerror = (event) => {
        this.log('Worker error:', event)
        this.rejectAllTasks(new Error(`Worker error: ${event.message}`))
      }

      this.worker.onmessageerror = (event) => {
        this.log('Worker message error:', event)
        this.rejectAllTasks(new Error('Worker message error'))
      }
    } catch (error) {
      throw new Error(`Failed to create worker: ${error}`)
    }
  }

  /**
   * 处理Worker消息
   * @param data 消息数据
   */
  private handleMessage(data: WorkerResponse): void {
    const task = this.tasks.get(data.id)
    if (!task) {
      this.log('Received response for unknown task:', data.id)
      return
    }

    this.tasks.delete(data.id)
    const executionTime = Date.now() - task.startTime
    this.stats.totalExecutionTime += executionTime

    if (data.success) {
      this.stats.completedTasks++
      task.resolve(data.data)
      this.log(`Task ${data.id} completed in ${executionTime}ms`)
    } else {
      this.stats.failedTasks++
      task.reject(new Error(data.error || 'Unknown error'))
      this.log(`Task ${data.id} failed:`, data.error)
    }
  }

  /**
   * 执行任务
   * @param type 任务类型
   * @param data 任务数据
   * @param timeout 超时时间
   * @returns Promise
   */
  execute<T, R>(type: string, data: T, timeout?: number): Promise<R> {
    return new Promise((resolve, reject) => {
      if (this.isTerminated || !this.worker) {
        reject(new Error('Worker is terminated'))
        return
      }

      const taskId = this.generateId()
      const taskTimeout = timeout || this.options.timeout
      const startTime = Date.now()

      const task: WorkerTask<T, R> = {
        id: taskId,
        type,
        data,
        resolve,
        reject,
        timeout: taskTimeout,
        startTime
      }

      this.tasks.set(taskId, task)
      this.stats.totalTasks++

      // 设置超时
      if (taskTimeout > 0) {
        setTimeout(() => {
          if (this.tasks.has(taskId)) {
            this.tasks.delete(taskId)
            this.stats.failedTasks++
            reject(new Error(`Task timeout after ${taskTimeout}ms`))
          }
        }, taskTimeout)
      }

      // 发送消息到Worker
      const message: WorkerMessage<T> = {
        id: taskId,
        type,
        data,
        timestamp: Date.now()
      }

      try {
        this.worker.postMessage(message)
        this.log(`Task ${taskId} sent to worker`)
      } catch (error) {
        this.tasks.delete(taskId)
        this.stats.failedTasks++
        reject(error)
      }
    })
  }

  /**
   * 终止Worker
   */
  terminate(): void {
    if (this.worker) {
      this.worker.terminate()
      this.worker = null
    }
    
    this.isTerminated = true
    this.rejectAllTasks(new Error('Worker terminated'))
  }

  /**
   * 检查Worker是否可用
   * @returns 是否可用
   */
  isAvailable(): boolean {
    return !this.isTerminated && this.worker !== null
  }

  /**
   * 获取统计信息
   * @returns 统计信息
   */
  getStats(): WorkerStats {
    const averageExecutionTime = this.stats.completedTasks > 0 
      ? this.stats.totalExecutionTime / this.stats.completedTasks 
      : 0

    return {
      totalTasks: this.stats.totalTasks,
      completedTasks: this.stats.completedTasks,
      failedTasks: this.stats.failedTasks,
      averageExecutionTime,
      activeWorkers: this.isAvailable() ? 1 : 0,
      idleWorkers: this.isAvailable() && this.tasks.size === 0 ? 1 : 0,
      queuedTasks: this.tasks.size
    }
  }

  /**
   * 拒绝所有待处理任务
   * @param error 错误信息
   */
  private rejectAllTasks(error: Error): void {
    this.tasks.forEach(task => {
      task.reject(error)
      this.stats.failedTasks++
    })
    this.tasks.clear()
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 日志输出
   * @param args 参数
   */
  private log(...args: any[]): void {
    if (this.options.debug) {
      console.log('[EnhancedWorker]', ...args)
    }
  }
}

/**
 * Worker池类
 */
export class WorkerPool {
  private workers: EnhancedWorker[] = []
  private idleWorkers: EnhancedWorker[] = []
  private taskQueue: Array<{
    type: string
    data: any
    resolve: (result: any) => void
    reject: (error: Error) => void
    timeout?: number
  }> = []
  private options: Required<WorkerPoolOptions>
  private isDestroyed = false
  private stats = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    totalExecutionTime: 0
  }

  constructor(scriptURL: string | URL, options: WorkerPoolOptions = {}) {
    this.options = {
      name: 'WorkerPool',
      type: 'classic',
      credentials: 'same-origin',
      timeout: 30000,
      maxRetries: 3,
      debug: false,
      maxWorkers: navigator.hardwareConcurrency || 4,
      minWorkers: 1,
      idleTimeout: 60000,
      taskTimeout: 30000,
      queueLimit: 100,
      ...options
    }

    // 创建最小数量的Worker
    for (let i = 0; i < this.options.minWorkers; i++) {
      this.createWorker(scriptURL)
    }
  }

  /**
   * 创建Worker
   * @param scriptURL 脚本URL
   */
  private createWorker(scriptURL: string | URL): EnhancedWorker {
    const worker = new EnhancedWorker(scriptURL, {
      name: `${this.options.name}-${this.workers.length}`,
      type: this.options.type,
      credentials: this.options.credentials,
      timeout: this.options.taskTimeout,
      maxRetries: this.options.maxRetries,
      debug: this.options.debug
    })

    this.workers.push(worker)
    this.idleWorkers.push(worker)

    return worker
  }

  /**
   * 获取可用的Worker
   * @param scriptURL 脚本URL（用于创建新Worker）
   * @returns Worker实例
   */
  private getAvailableWorker(scriptURL?: string | URL): EnhancedWorker | null {
    // 优先使用空闲Worker
    if (this.idleWorkers.length > 0) {
      return this.idleWorkers.shift()!
    }

    // 如果没有空闲Worker且未达到最大数量，创建新Worker
    if (this.workers.length < this.options.maxWorkers && scriptURL) {
      return this.createWorker(scriptURL)
    }

    return null
  }

  /**
   * 释放Worker到空闲池
   * @param worker Worker实例
   */
  private releaseWorker(worker: EnhancedWorker): void {
    if (worker.isAvailable() && !this.idleWorkers.includes(worker)) {
      this.idleWorkers.push(worker)

      // 设置空闲超时
      if (this.options.idleTimeout > 0 && this.workers.length > this.options.minWorkers) {
        setTimeout(() => {
          const index = this.idleWorkers.indexOf(worker)
          if (index > -1 && this.workers.length > this.options.minWorkers) {
            this.idleWorkers.splice(index, 1)
            const workerIndex = this.workers.indexOf(worker)
            if (workerIndex > -1) {
              this.workers.splice(workerIndex, 1)
            }
            worker.terminate()
          }
        }, this.options.idleTimeout)
      }
    }
  }

  /**
   * 处理队列中的任务
   * @param scriptURL 脚本URL
   */
  private processQueue(scriptURL?: string | URL): void {
    while (this.taskQueue.length > 0) {
      const worker = this.getAvailableWorker(scriptURL)
      if (!worker) break

      const task = this.taskQueue.shift()!
      this.executeTask(worker, task)
    }
  }

  /**
   * 执行任务
   * @param worker Worker实例
   * @param task 任务
   */
  private async executeTask(
    worker: EnhancedWorker,
    task: {
      type: string
      data: any
      resolve: (result: any) => void
      reject: (error: Error) => void
      timeout?: number
    }
  ): Promise<void> {
    const startTime = Date.now()
    this.stats.totalTasks++

    try {
      const result = await worker.execute(task.type, task.data, task.timeout)
      const executionTime = Date.now() - startTime
      this.stats.totalExecutionTime += executionTime
      this.stats.completedTasks++
      task.resolve(result)
    } catch (error) {
      this.stats.failedTasks++
      task.reject(error as Error)
    } finally {
      this.releaseWorker(worker)
    }
  }

  /**
   * 执行任务
   * @param type 任务类型
   * @param data 任务数据
   * @param timeout 超时时间
   * @returns Promise
   */
  execute<T, R>(type: string, data: T, timeout?: number): Promise<R> {
    return new Promise((resolve, reject) => {
      if (this.isDestroyed) {
        reject(new Error('Worker pool is destroyed'))
        return
      }

      if (this.taskQueue.length >= this.options.queueLimit) {
        reject(new Error('Task queue is full'))
        return
      }

      const task = {
        type,
        data,
        resolve,
        reject,
        timeout: timeout || this.options.taskTimeout
      }

      // 尝试立即执行
      const worker = this.getAvailableWorker()
      if (worker) {
        this.executeTask(worker, task)
      } else {
        // 加入队列
        this.taskQueue.push(task)
      }
    })
  }

  /**
   * 批量执行任务
   * @param tasks 任务数组
   * @returns Promise数组
   */
  executeBatch<T, R>(tasks: Array<{ type: string; data: T; timeout?: number }>): Promise<R[]> {
    return Promise.all(
      tasks.map(task => this.execute<T, R>(task.type, task.data, task.timeout))
    )
  }

  /**
   * 获取统计信息
   * @returns 统计信息
   */
  getStats(): WorkerStats {
    const averageExecutionTime = this.stats.completedTasks > 0 
      ? this.stats.totalExecutionTime / this.stats.completedTasks 
      : 0

    return {
      totalTasks: this.stats.totalTasks,
      completedTasks: this.stats.completedTasks,
      failedTasks: this.stats.failedTasks,
      averageExecutionTime,
      activeWorkers: this.workers.length - this.idleWorkers.length,
      idleWorkers: this.idleWorkers.length,
      queuedTasks: this.taskQueue.length
    }
  }

  /**
   * 销毁Worker池
   */
  destroy(): void {
    this.isDestroyed = true
    
    // 拒绝所有队列中的任务
    this.taskQueue.forEach(task => {
      task.reject(new Error('Worker pool destroyed'))
    })
    this.taskQueue = []

    // 终止所有Worker
    this.workers.forEach(worker => worker.terminate())
    this.workers = []
    this.idleWorkers = []
  }

  /**
   * 获取Worker数量
   * @returns Worker数量
   */
  size(): number {
    return this.workers.length
  }

  /**
   * 获取空闲Worker数量
   * @returns 空闲Worker数量
   */
  idleSize(): number {
    return this.idleWorkers.length
  }

  /**
   * 获取队列长度
   * @returns 队列长度
   */
  queueSize(): number {
    return this.taskQueue.length
  }
}

/**
 * 创建Worker
 * @param scriptURL 脚本URL
 * @param options 配置选项
 * @returns Worker实例
 */
export function createWorker(scriptURL: string | URL, options?: WorkerOptions): EnhancedWorker {
  return new EnhancedWorker(scriptURL, options)
}

/**
 * 创建Worker池
 * @param scriptURL 脚本URL
 * @param options 配置选项
 * @returns Worker池实例
 */
export function createWorkerPool(scriptURL: string | URL, options?: WorkerPoolOptions): WorkerPool {
  return new WorkerPool(scriptURL, options)
}

/**
 * 从函数创建Worker
 * @param fn 函数
 * @param options 配置选项
 * @returns Worker实例
 */
export function createWorkerFromFunction(fn: Function, options?: WorkerOptions): EnhancedWorker {
  const blob = new Blob([
    `
    self.onmessage = function(e) {
      const { id, type, data } = e.data;
      try {
        const result = (${fn.toString()})(data);
        if (result instanceof Promise) {
          result
            .then(data => {
              self.postMessage({ id, success: true, data, timestamp: Date.now() });
            })
            .catch(error => {
              self.postMessage({ id, success: false, error: error.message, timestamp: Date.now() });
            });
        } else {
          self.postMessage({ id, success: true, data: result, timestamp: Date.now() });
        }
      } catch (error) {
        self.postMessage({ id, success: false, error: error.message, timestamp: Date.now() });
      }
    };
    `
  ], { type: 'application/javascript' })

  const url = URL.createObjectURL(blob)
  const worker = new EnhancedWorker(url, options)

  // 清理URL对象
  const originalTerminate = worker.terminate.bind(worker)
  worker.terminate = () => {
    URL.revokeObjectURL(url)
    originalTerminate()
  }

  return worker
}

/**
 * 从函数创建Worker池
 * @param fn 函数
 * @param options 配置选项
 * @returns Worker池实例
 */
export function createWorkerPoolFromFunction(fn: Function, options?: WorkerPoolOptions): WorkerPool {
  const blob = new Blob([
    `
    self.onmessage = function(e) {
      const { id, type, data } = e.data;
      try {
        const result = (${fn.toString()})(data);
        if (result instanceof Promise) {
          result
            .then(data => {
              self.postMessage({ id, success: true, data, timestamp: Date.now() });
            })
            .catch(error => {
              self.postMessage({ id, success: false, error: error.message, timestamp: Date.now() });
            });
        } else {
          self.postMessage({ id, success: true, data: result, timestamp: Date.now() });
        }
      } catch (error) {
        self.postMessage({ id, success: false, error: error.message, timestamp: Date.now() });
      }
    };
    `
  ], { type: 'application/javascript' })

  const url = URL.createObjectURL(blob)
  const pool = new WorkerPool(url, options)

  // 清理URL对象
  const originalDestroy = pool.destroy.bind(pool)
  pool.destroy = () => {
    URL.revokeObjectURL(url)
    originalDestroy()
  }

  return pool
}

/**
 * Worker工具函数
 */
export const WorkerUtils = {
  /**
   * 检查Worker是否支持
   * @returns 是否支持
   */
  isSupported(): boolean {
    return typeof Worker !== 'undefined'
  },

  /**
   * 检查SharedArrayBuffer是否支持
   * @returns 是否支持
   */
  isSharedArrayBufferSupported(): boolean {
    return typeof SharedArrayBuffer !== 'undefined'
  },

  /**
   * 检查Atomics是否支持
   * @returns 是否支持
   */
  isAtomicsSupported(): boolean {
    return typeof Atomics !== 'undefined'
  },

  /**
   * 获取硬件并发数
   * @returns 并发数
   */
  getHardwareConcurrency(): number {
    return navigator.hardwareConcurrency || 4
  },

  /**
   * 创建数据URL
   * @param code 代码字符串
   * @returns 数据URL
   */
  createDataURL(code: string): string {
    const blob = new Blob([code], { type: 'application/javascript' })
    return URL.createObjectURL(blob)
  },

  /**
   * 释放数据URL
   * @param url 数据URL
   */
  revokeDataURL(url: string): void {
    URL.revokeObjectURL(url)
  },

  /**
   * 创建简单的计算Worker
   * @param fn 计算函数
   * @returns Worker实例
   */
  createComputeWorker(fn: (data: any) => any): EnhancedWorker {
    return createWorkerFromFunction(fn)
  },

  /**
   * 并行执行计算任务
   * @param data 数据数组
   * @param fn 计算函数
   * @param options 选项
   * @returns Promise
   */
  async parallelCompute<T, R>(
    data: T[],
    fn: (item: T) => R,
    options: { chunkSize?: number; maxWorkers?: number } = {}
  ): Promise<R[]> {
    const { chunkSize = 1, maxWorkers = WorkerUtils.getHardwareConcurrency() } = options
    
    if (data.length === 0) return []
    
    const pool = createWorkerPoolFromFunction(fn, { maxWorkers })
    
    try {
      const chunks: T[][] = []
      for (let i = 0; i < data.length; i += chunkSize) {
        chunks.push(data.slice(i, i + chunkSize))
      }
      
      const results = await Promise.all(
        chunks.map(chunk => 
          pool.execute<T[], R[]>('compute', chunk)
        )
      )
      
      return results.flat()
    } finally {
      pool.destroy()
    }
  }
}

/**
 * Worker装饰器（用于类方法）
 */
export function workerMethod(options: WorkerOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function (...args: any[]) {
      const worker = createWorkerFromFunction(originalMethod, options)
      
      return worker.execute('compute', args).finally(() => {
        worker.terminate()
      })
    }
    
    return descriptor
  }
}

/**
 * Worker缓存
 */
export class WorkerCache {
  private cache = new Map<string, EnhancedWorker>()
  private options: WorkerOptions

  constructor(options: WorkerOptions = {}) {
    this.options = options
  }

  /**
   * 获取或创建Worker
   * @param key 缓存键
   * @param scriptURL 脚本URL或函数
   * @returns Worker实例
   */
  get(key: string, scriptURL: string | URL | Function): EnhancedWorker {
    if (this.cache.has(key)) {
      const worker = this.cache.get(key)!
      if (worker.isAvailable()) {
        return worker
      } else {
        this.cache.delete(key)
      }
    }

    let worker: EnhancedWorker
    if (typeof scriptURL === 'function') {
      worker = createWorkerFromFunction(scriptURL, this.options)
    } else {
      worker = new EnhancedWorker(scriptURL, this.options)
    }

    this.cache.set(key, worker)
    return worker
  }

  /**
   * 移除Worker
   * @param key 缓存键
   */
  remove(key: string): void {
    const worker = this.cache.get(key)
    if (worker) {
      worker.terminate()
      this.cache.delete(key)
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.forEach(worker => worker.terminate())
    this.cache.clear()
  }

  /**
   * 获取缓存大小
   * @returns 缓存大小
   */
  size(): number {
    return this.cache.size
  }
}

/**
 * 创建Worker缓存
 * @param options 配置选项
 * @returns Worker缓存实例
 */
export function createWorkerCache(options?: WorkerOptions): WorkerCache {
  return new WorkerCache(options)
}