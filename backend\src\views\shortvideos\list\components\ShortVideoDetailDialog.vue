<template>
  <el-dialog
    title="短视频详情"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
  >
    <div v-if="shortVideoData" class="shortvideo-detail">
      <div class="detail-header">
        <div class="video-preview">
          <!-- 直接嵌入视频播放器 -->
          <video
            :src="shortVideoData.url"
            controls
            class="video-player"
            preload="metadata"
            :poster="shortVideoData.cover || ''"
          ></video>
        </div>
        <div class="video-info">
          <h2 class="video-title">{{ shortVideoData.title }}</h2>
          <div class="video-meta">
            <div class="meta-item">
              <span class="label">时长：</span>
              <span class="value">{{ formatDuration(shortVideoData.duration) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">分辨率：</span>
              <span class="value">{{ shortVideoData.width }}x{{ shortVideoData.height }}</span>
            </div>
            <div class="meta-item">
              <span class="label">状态：</span>
              <el-tag :type="getStatusType(shortVideoData.status)" size="small">
                {{ getStatusText(shortVideoData.status) }}
              </el-tag>
            </div>
            <div class="meta-item">
              <span class="label">创建时间：</span>
              <span class="value">{{ formatDate(shortVideoData.created_at) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">更新时间：</span>
              <span class="value">{{ formatDate(shortVideoData.updated_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <el-divider />

      <div class="detail-body">
        <el-descriptions :column="2" border>
            <el-descriptions-item label="ID">
            {{ shortVideoData.id }}
            </el-descriptions-item>
          <el-descriptions-item label="分类">
            <el-tag size="small" type="info" effect="plain">
              {{ shortVideoData.category_name || '未分类' }}
            </el-tag>
            </el-descriptions-item>
          <el-descriptions-item label="创作者">
            <div class="creator-info" v-if="shortVideoData?.creator_name">
              <el-avatar :size="24" :src="shortVideoData?.creator_avatar || ''">
                {{ shortVideoData?.creator_name?.charAt(0) || '?' }}
              </el-avatar>
              <span class="creator-name">{{ shortVideoData?.creator_name }}</span>
            </div>
            <span v-else>未知</span>
            </el-descriptions-item>
            <el-descriptions-item label="标签">
            <div class="tags-list">
                  <el-tag
                v-for="(tag, index) in shortVideoData.tags"
                    :key="index"
                size="small"
                effect="plain"
                    class="tag-item"
              >
                {{ tag }}
              </el-tag>
              <span v-if="!shortVideoData.tags || shortVideoData.tags.length === 0">无标签</span>
            </div>
            </el-descriptions-item>
          <el-descriptions-item label="视频URL" :span="2">
            <el-link :href="shortVideoData.url" target="_blank" type="primary" :underline="false">
              {{ shortVideoData.url }}
            </el-link>
            </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <div class="description-content">
              {{ shortVideoData.description || '暂无描述' }}
            </div>
            </el-descriptions-item>
          </el-descriptions>
      </div>

      <el-divider />

      <div class="detail-stats">
        <h3>数据统计</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(shortVideoData.views) }}</div>
              <div class="stat-label">播放量</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(shortVideoData.likes) }}</div>
              <div class="stat-label">点赞数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(shortVideoData.comments) }}</div>
              <div class="stat-label">评论数</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Share /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(shortVideoData.shares) }}</div>
              <div class="stat-label">分享数</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { ShortVideo } from '@/types/shortvideos';
import { ChatDotRound, Share, Star, View } from '@element-plus/icons-vue';
import { computed } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  shortVideoData: {
    type: Object as () => ShortVideo | null,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'edit']);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 处理编辑
const handleEdit = () => {
  emit('edit');
};

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '00:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化数字
const formatNumber = (num: number) => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '待审核';
    case 1: return '已下架';
    case 2: return '已发布';
    case -2: return '已拒绝';
    case -4: return '已删除';
    default: return '未知状态';
  }
};

// 获取状态标签类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'info';
    case 1: return 'warning';
    case 2: return 'success';
    case -2: return 'danger';
    case -4: return 'danger';
    default: return 'info';
  }
};
</script>

<style scoped lang="scss">
.shortvideo-detail {
  .detail-header {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;

    .video-preview {
      flex: 1;
      max-width: 400px;
      
      .video-player {
        width: 100%;
        aspect-ratio: 16/9;
        border-radius: 8px;
        background-color: #000;
      }
      
      .video-cover {
        width: 100%;
        aspect-ratio: 16/9;
        border-radius: 8px;
        object-fit: cover;
      }
      
      .image-placeholder {
        width: 100%;
        aspect-ratio: 16/9;
        border-radius: 8px;
        background-color: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #909399;
        
        .el-icon {
          font-size: 48px;
        }
      }
      
      .video-actions {
        display: flex;
        justify-content: center;
        margin-top: 12px;
      }
    }
    
    .video-info {
      flex: 1;
      
      .video-title {
        margin-top: 0;
        margin-bottom: 16px;
        font-size: 20px;
        line-height: 1.4;
      }
      
      .video-meta {
        display: flex;
        flex-direction: column;
        gap: 8px;
        
        .meta-item {
          display: flex;
          align-items: center;
          
          .label {
            width: 80px;
            color: #606266;
          }
          
          .value {
            color: #303133;
          }
        }
      }
    }
  }
  
  .detail-body {
    margin-bottom: 24px;
    
    .description-content {
      white-space: pre-wrap;
      line-height: 1.6;
    }
    
    .tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .tag-item {
        margin-right: 0;
      }
    }
    
    .creator-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .detail-stats {
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      
      .stat-card {
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        
        .stat-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background-color: #ecf5ff;
          color: #409eff;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .el-icon {
            font-size: 20px;
          }
        }
        
        .stat-content {
          .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
            line-height: 1.2;
          }
          
          .stat-label {
            font-size: 12px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
