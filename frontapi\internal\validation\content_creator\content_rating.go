package content_creator

type CreateContentRatingRequest struct {
	UserID      string  `json:"user_id"`
	ContentType string  `json:"content_type"`
	ContentID   string  `json:"content_id"`
	Rating      float64 `json:"rating"`
	Comment     string  `json:"comment"`
	IsAnonymous bool    `json:"is_anonymous"`
	IsHidden    bool    `json:"is_hidden"`
}

type UpdateContentRatingRequest struct {
	Rating      float64 `json:"rating"`
	Comment     string  `json:"comment"`
	IsAnonymous bool    `json:"is_anonymous"`
	IsHidden    bool    `json:"is_hidden"`
}
