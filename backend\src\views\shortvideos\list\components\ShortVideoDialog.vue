<template>
  <el-dialog
    :title="type === 'add' ? '添加短视频' : '编辑短视频'"
    v-model="dialogVisible" style="padding-right: 25px;"
    :close-on-click-modal="!isUploading"
    :close-on-press-escape="!isUploading"
    :show-close="!isUploading"
    width="700px"   align-center
    @closed="handleDialogClosed"
    @close="handleDialogClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入标题" maxlength="255" show-word-limit></el-input>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
          maxlength="1024"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="创作者" prop="creator_id">
        <el-select
          v-model="form.creator_id"
          filterable
          remote
          :remote-method="remoteSearchUsers"
          :loading="isLoadingUsers"
          placeholder="请选择或搜索用户"
          style="width: 100%"
          @change="handleCreatorChange"
          clearable
        >
          <el-option
            v-for="item in userOptions"
            :key="item.id"
            :label="item.username || item.nickname"
            :value="item.id"
          >
            <div style="display: flex; align-items: center;">
              <img v-if="item.avatar" :src="item.avatar" style="width: 20px; height: 20px; border-radius: 50%; margin-right: 8px;" />
              <span>{{ item.username || item.nickname }}</span>
            </div>
          </el-option>
        </el-select>
        <div class="form-tip">选择视频创作者，不选则为当前管理员</div>
      </el-form-item>

      <el-form-item label="视频" prop="url">
        <UrlOrFileInput
          v-model="form.url"
          fileType="video"
          subDir="videos"
          placeholder="请输入视频地址，支持MP4、WebM格式，最大100MB"
        />
      </el-form-item>

      <el-form-item label="分类" prop="category_id">
        <el-select v-model="form.category_id" placeholder="请选择分类" @change="handleCategoryChange">
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          remote
          :remote-method="remoteSearchTags"
          :loading="isLoadingTags"
          default-first-option
          placeholder="请选择或搜索标签"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          ></el-option>
        </el-select>
        <div class="form-tip">支持多选，可输入关键词搜索</div>
      </el-form-item>

      <el-form-item label="推荐" prop="is_featured">
        <el-switch
          v-model="form.is_featured"
          :active-value="1"
          :inactive-value="0"
        ></el-switch>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleDialogClose" :disabled="isUploading">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting || isUploading">确定</el-button>
      </span>
    </template>

    <!-- 图片预览 -->
    <el-dialog v-model="previewVisible" width="50%">
      <img :src="previewUrl" style="width: 100%" />
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import { createShortVideo, getCategoryList, updateShortVideo } from '@/service/api/shortvideos/shortvideos';
import { getTagList } from '@/service/api/system/tags';
import { deleteUploadedFile, uploadVideoWithProgress } from '@/service/api/upload';
import { getUserList } from '@/service/api/users/users';
import type { CreateShortVideoRequest, ShortVideo, UpdateShortVideoRequest } from '@/types/shortvideos';
import type { Tag } from '@/types/tags';
import type { UserItem } from '@/types/users';
import { ElMessage, FormInstance } from 'element-plus';
import { computed, nextTick, reactive, ref, watch } from 'vue';

const props = defineProps<{
  visible: boolean;
  type: 'add' | 'edit';
  shortvideoData?: ShortVideo | null;
}>();

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    title: '',
    description: '',
    url: '',
    category_id: '',
    creator_id: '',
    duration: 0,
    width: 0,
    height: 0,
    tags: [],
    status: 0,
    is_featured: 0,
  });
  videoFileList.value = [];
  videoUploadProgress.value = 0;
};

const emit = defineEmits(['update:visible', 'success','close']);

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const form = reactive<{
  id: string;
  title: string;
  description: string;
  cover_url: string;
  url: string;
  category_id: string;
  category_name: string;
  creator_id: string;
  creator_name: string;
  creator_avatar: string;
  duration: number;
  width: number;
  height: number;
  tags: string[];
  status: number;
  is_featured: number;
}>({
  id: '',
  title: '',
  description: '',
  cover_url: '',
  url: '',
  category_id: '',
  category_name: '',
  creator_id: '',
  creator_name: '',
  creator_avatar: '',
  duration: 0,
  width: 0,
  height: 0,
  tags: [],
  status: 0,
  is_featured: 0,
});

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  url: [{ required: true, message: '请上传视频', trigger: 'change' }],
  category_id: [{ required: true, message: '请选择分类', trigger: 'change' }],
};

// 上传相关
const videoFileList = ref<any[]>([]);
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('token') || ''}`,
}));

// 上传状态管理
const isUploading = ref(false);
const videoUploadProgress = ref(0);

// 预览相关
const previewVisible = ref(false);
const previewUrl = ref('');

// 分类和标签选项
const categoryOptions = ref<{ label: string; value: string }[]>([]);
const tagOptions = ref<Tag[]>([]);
const tagSearchKeyword = ref('');
const isLoadingTags = ref(false);

// 用户选项
const userOptions = ref<UserItem[]>([]);
const isLoadingUsers = ref(false);

// 提交状态
const submitting = ref(false);

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
});

// 监听Props变化
watch(() => props.shortvideoData, (val) => {
  if (val) {
    Object.assign(form, {
      id: val.id || '',
      title: val.title || '',
      description: val.description || '',
      cover: val.cover || '',
      url: val.url || '',
      category_id: val.category_id || '',
      category_name: val.category_name || '',
      creator_id: val.creator_id || '',
      creator_name: val.creator_name || '',
      creator_avatar: val.creator_avatar || '',
      duration: val.duration || 0,
      width: val.width || 0,
      height: val.height || 0,
      tags: Array.isArray(val.tags) ? val.tags : (val.tags ? [val.tags] : []),
      status: val.status || 0,
      is_featured: val.is_featured || 0,
    });
  } else {
    resetForm();
  }
}, { immediate: true, deep: true });

// 监听对话框可见性
watch(() => props.visible, async (val) => {
  if (val) {
    await Promise.all([fetchCategories(), fetchTags()]);
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }
});

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await getCategoryList({
      page: { pageNo: 1, pageSize: 100 },
      data: { status: 1 }
    }) as any;

    if (res.code === 2000 && res.data.list) {
      categoryOptions.value = res.data.list.map((item: any) => ({
        label: item.name,
        value: item.id
      }));
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取分类列表失败');
  }
};

// 获取标签列表
const fetchTags = async (keyword?: string) => {
  try {
    isLoadingTags.value = true;
    const params = {
      page: { pageNo: 1, pageSize: 100 },
      data: {
        type: 2,  // 短视频标签类型
        name: keyword || undefined
      }
    };

    const res = await getTagList(params) as any;
    if (res.code === 2000 || res.code === 200) {
      tagOptions.value = res.data?.list || [];
    }
  } catch (error) {
    console.error('获取标签列表失败:', error);
    ElMessage.error('获取标签列表失败');
  } finally {
    isLoadingTags.value = false;
  }
};

// 远程搜索标签
const remoteSearchTags = (keyword: string) => {
  tagSearchKeyword.value = keyword;
  if (keyword) {
    fetchTags(keyword);
  } else {
    fetchTags();
  }
};

// 创作者选择变化
const handleCreatorChange = (value: string) => {
  const selectedUser = userOptions.value.find(user => user.id === value);
  if (selectedUser) {
    form.creator_name = selectedUser.username || selectedUser.nickname;
    form.creator_avatar = selectedUser.avatar;
  }
};
const handleCategoryChange = (value: string) => {
  const selectedCategory = categoryOptions.value.find(category => category.value === value);
  if (selectedCategory) {
    form.category_name = selectedCategory.label;
  }
};

// 获取用户列表
const fetchUsers = async (keyword?: string) => {
  try {
    isLoadingUsers.value = true;
    const params = {
      page: { pageNo: 1, pageSize: 20 },
      data: {
        keyword: keyword || undefined
      }
    };

    const res = await getUserList(params) as any;
    if (res.code === 2000 || res.code === 200) {
      userOptions.value = res.data?.list || [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    ElMessage.error('获取用户列表失败');
  } finally {
    isLoadingUsers.value = false;
  }
};

// 远程搜索用户
const remoteSearchUsers = (keyword: string) => {
  if (keyword) {
    fetchUsers(keyword);
  } else {
    fetchUsers();
  }
};

// 视频上传前的校验
const beforeVideoUpload = (file: File) => {
  const isMP4 = file.type === 'video/mp4' || file.type === 'video/webm';
  const isLt100M = file.size / 1024 / 1024 < 100;

  if (!isMP4) {
    ElMessage.error('视频只能是MP4/WebM格式!');
    return false;
  }

  if (!isLt100M) {
    ElMessage.error('视频大小不能超过100MB!');
    return false;
  }

  return true;
};

// 视频自定义上传
const uploadVideoHandler = (options: any) => {
  // 设置上传状态
  isUploading.value = true;
  videoUploadProgress.value = 0;

  // 使用带进度的上传函数
  return uploadVideoWithProgress({
    file: options.file,
    subDir: 'shortvideos',
    timeout: 60 * 60 * 1000, // 60分钟超时
    callbacks: {
      onProgress: (percent) => {
        videoUploadProgress.value = percent;
      },
      onSuccess: (res: any) => {
        if (res.code === 2000 || res.success) {
          form.url = res.data?.url || (typeof res.data === 'string' ? res.data : '');
          form.duration = res.data?.duration || 0;
          form.width = res.data?.width || 0;
          form.height = res.data?.height || 0;
          ElMessage.success('视频上传成功');
          options.onSuccess && options.onSuccess(res);
        } else {
          const errMsg = res.message || '视频上传失败';
          ElMessage.error(errMsg);
          options.onError && options.onError(new Error(errMsg));
        }
      },
      onError: (err) => {
        ElMessage.error(err.message || '视频上传失败');
        options.onError && options.onError(err);
      },
      onComplete: () => {
        videoUploadProgress.value = 0;
        isUploading.value = false;
      }
    }
  });
};

// 格式化视频时长
const formatDuration = (seconds: number) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;

  return [
    h > 0 ? h.toString().padStart(2, '0') : '',
    m.toString().padStart(2, '0'),
    s.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
};

// 从URL中提取文件名
const extractFileName = (url: string): string => {
  if (!url) return '';
  return url.split('/').pop() || url;
};

// 从URL中提取文件路径
const extractFilePathFromUrl = (url: string): string | null => {
  if (!url) return null;

  try {
    // 去除域名和协议，只保留路径部分
    const urlObj = new URL(url, window.location.origin);
    return urlObj.pathname;
  } catch (e) {
    // 如果URL不是完整URL，假设它已经是路径
    return url.startsWith('/') ? url : `/${url}`;
  }
};

// 预览图片
const handlePreview = (file: any) => {
  previewUrl.value = file.url;
  previewVisible.value = true;
};

// 处理对话框关闭
const handleDialogClose = () => {
  if (isUploading.value) {
    ElMessage.warning('文件正在上传中，请等待上传完成');
    return;
  }
  dialogVisible.value = false;
  emit('close');
};

// 移除文件
const handleRemove = (file: any, type: 'cover' | 'video') => {
  if (type === 'video') {
    videoFileList.value = [];
    handleRemoveVideo();
  }
};

// 移除视频
const handleRemoveVideo = async () => {
  if (form.url) {
    try {
      const filePath = extractFilePathFromUrl(form.url);
      if (filePath) {
        await deleteUploadedFile(filePath);
      }
      form.url = '';
      form.duration = 0;
      form.width = 0;
      form.height = 0;
      ElMessage.success('视频已删除');
    } catch (error) {
      console.error(error);
      ElMessage.error('删除视频失败');
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  // 检查是否有上传正在进行
  if (isUploading.value) {
    ElMessage.warning('请等待文件上传完成');
    return;
  }

  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true;

      try {
        if (props.type === 'add') {
          // 创建短视频
          const formData: CreateShortVideoRequest = {
            title: form.title,
            description: form.description,
            url: form.url,
            category_id: form.category_id,
            category_name: form.category_name,
            creator_id: form.creator_id,
            creator_name: form.creator_name,
            creator_avatar: form.creator_avatar,
            duration: form.duration,
            width: form.width,
            height: form.height,
            tags: form.tags,
            status: form.status,
            is_featured: form.is_featured,
          };

          // 如果选择了用户，添加creator_id字段
          if (form.creator_id) {
            formData.creator_id = form.creator_id;
            formData.creator_name = form.creator_name;
            formData.creator_avatar = form.creator_avatar;
          }

          const {response} = await createShortVideo({data:formData}) as any;
          if (response.data.code === 2000) {
            ElMessage.success('创建短视频成功');
            dialogVisible.value = false;
            emit('success');
          } else {
            ElMessage.error(response.data.message || '创建短视频失败');
          }
        } else {
          // 更新短视频
          const formData: UpdateShortVideoRequest = {
            id: form.id,
            title: form.title,
            description: form.description,
            url: form.url,
            category_id: form.category_id,
            category_name: form.category_name,
            creator_id: form.creator_id,
            creator_name: form.creator_name,
            creator_avatar: form.creator_avatar,
            duration: form.duration,
            width: form.width,
            height: form.height,
            tags: form.tags,
            status: form.status,
            is_featured: form.is_featured,
          };

          // 如果选择了用户，添加creator_id字段
          if (form.creator_id) {
            formData.creator_id = form.creator_id;

          }

          const {response} = await updateShortVideo({data:formData}) as any;
          console.log(response)
          if (response.data.code === 2000) {
            ElMessage.success('更新短视频成功');
            dialogVisible.value = false;
            emit('success');
          } else {
            ElMessage.error(response.data.message || '更新短视频失败');
          }
        }
      } catch (error) {
        console.error(error);
        ElMessage.error('操作失败，请重试');
      } finally {
        submitting.value = false;
      }
    }
  });
};

// 对话框关闭
const handleDialogClosed = () => {
  emit('close')
  resetForm();
};
</script>

<style scoped>
.upload-container {
  width: 100%;
}

.upload-box, .upload-cover {
  width: 100%;
}

.upload-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-actions {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  padding: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 0 4px;
}

.upload-actions .el-icon {
  margin: 2px;
  cursor: pointer;
}

.upload-tip, .form-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}

.video-preview {
  width: 100%;
  height: auto;
  max-height: 400px;
  border-radius: 4px;
}

.preview-container {
  position: relative;
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 10px;
}

.preview-container:hover .action-buttons {
  opacity: 1;
}

.action-buttons {
  position: absolute;
  top: 10px;
  right: 10px;
  opacity: 0.7;
  transition: opacity 0.3s;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
}

.video-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  margin-top: 10px;
}

.video-actions {
  display: flex;
  gap: 10px;
}

.progress-container {
  margin-top: 10px;
  width: 100%;
}

.video-info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.nested-form-item {
  margin-bottom: 0 !important;
}

:deep(.el-form-item__content) {
  justify-content: flex-start;
}
</style>
