import { corePostPageList } from '@/shared/composables'
import type { VideoItem } from '@/types/video'

export interface SearchParams {
    keyword?: string
    filter?: 'recommended' | 'latest' | 'popular'
    page?: number
    pageSize?: number
}

export interface SearchResult {
    list: VideoItem[]
    total: number
    currentPage: number
    pageSize: number
}

export const searchVideos = (params: SearchParams): Promise<SearchResult> => {
    return corePostPageList('/home/<USER>', {
        data: params,
        page: { pageNo: 1, pageSize: 10 }
    }) as any
}