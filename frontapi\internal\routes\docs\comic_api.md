# 漫画管理接口文档

## 漫画分类接口 (/comics/category)

### 1. 获取漫画分类列表

**接口地址**: `POST /api/comics/category/getComicCategoryList`

**接口描述**: 获取漫画分类列表

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "parent_id": "父分类ID",
    "status": 1,
    "include_comic_count": true
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "category_id",
        "name": "分类名称",
        "description": "分类描述",
        "icon_url": "图标URL",
        "cover_url": "封面图片URL",
        "parent_id": "父分类ID",
        "sort_order": 1,
        "comic_count": 100,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 获取漫画分类详情

**接口地址**: `POST /api/comics/category/getComicCategoryDetail`

**接口描述**: 获取指定漫画分类的详细信息

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "category_id": "category_id",
    "include_comics": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "category_id",
    "name": "分类名称",
    "description": "分类详细描述",
    "icon_url": "图标URL",
    "cover_url": "封面图片URL",
    "parent_id": "父分类ID",
    "sort_order": 1,
    "comic_count": 100,
    "status": 1,
    "comics": [
      {
        "id": "comic_id",
        "title": "漫画标题",
        "cover_url": "封面URL",
        "author": "作者",
        "status": "连载中"
      }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 漫画基础接口 (/comics)

### 1. 获取漫画列表

**接口地址**: `POST /api/comics/getComicList`

**接口描述**: 获取漫画列表，支持分页和筛选

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "author": "作者名称",
    "status": "连载中",
    "sort_by": "created_at",
    "sort_order": "desc",
    "tags": ["热血", "冒险"]
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "comic_id",
        "title": "漫画标题",
        "subtitle": "副标题",
        "author": "作者名称",
        "description": "漫画描述",
        "cover_url": "封面图片URL",
        "category_id": "分类ID",
        "category_name": "分类名称",
        "tags": ["热血", "冒险"],
        "status": "连载中",
        "chapters_count": 100,
        "views_count": 50000,
        "favorites_count": 1000,
        "rating": 4.5,
        "reviews_count": 200,
        "last_update": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 500,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 获取漫画详情

**接口地址**: `POST /api/comics/getComicDetail`

**接口描述**: 获取指定漫画的详细信息

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "comic_id": "comic_id",
    "include_chapters": true,
    "include_comments": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "comic_id",
    "title": "漫画标题",
    "subtitle": "副标题",
    "author": "作者名称",
    "description": "漫画详细描述",
    "cover_url": "封面图片URL",
    "category_id": "分类ID",
    "category_name": "分类名称",
    "tags": ["热血", "冒险"],
    "status": "连载中",
    "chapters_count": 100,
    "views_count": 50000,
    "favorites_count": 1000,
    "rating": 4.5,
    "reviews_count": 200,
    "publisher": "出版社",
    "publish_date": "2024-01-01",
    "language": "zh-CN",
    "age_rating": "全年龄",
    "last_update": "2024-01-01T00:00:00Z",
    "chapters": [
      {
        "id": "chapter_id",
        "title": "第1话 开始",
        "order": 1,
        "pages_count": 20,
        "is_free": true,
        "publish_date": "2024-01-01T00:00:00Z"
      }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 获取漫画章节列表

**接口地址**: `POST /api/comics/getComicChapterList`

**接口描述**: 获取指定漫画的章节列表

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "comic_id": "comic_id",
    "sort_by": "order",
    "sort_order": "asc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 50
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "chapter_id",
        "comic_id": "comic_id",
        "title": "第1话 开始",
        "order": 1,
        "pages_count": 20,
        "views_count": 1000,
        "is_free": true,
        "price": 0,
        "cover_url": "章节封面URL",
        "summary": "章节简介",
        "publish_date": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 50
  }
}
```

### 4. 获取漫画章节详情

**接口地址**: `POST /api/comics/getComicChapterDetail`

**接口描述**: 获取指定章节的详细信息

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "chapter_id": "chapter_id",
    "include_pages": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "chapter_id",
    "comic_id": "comic_id",
    "comic_title": "漫画标题",
    "title": "第1话 开始",
    "order": 1,
    "pages_count": 20,
    "views_count": 1000,
    "is_free": true,
    "price": 0,
    "cover_url": "章节封面URL",
    "summary": "章节详细简介",
    "pages": [
      {
        "id": "page_id",
        "order": 1,
        "image_url": "页面图片URL",
        "width": 800,
        "height": 1200
      }
    ],
    "publish_date": "2024-01-01T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5. 获取章节内容

**接口地址**: `POST /api/comics/getChapterContent`

**接口描述**: 获取章节的阅读内容（页面图片列表）

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "chapter_id": "chapter_id",
    "user_id": "用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "chapter_id": "chapter_id",
    "title": "第1话 开始",
    "pages": [
      {
        "id": "page_id",
        "order": 1,
        "image_url": "页面图片URL",
        "thumbnail_url": "缩略图URL",
        "width": 800,
        "height": 1200,
        "file_size": 204800
      }
    ],
    "prev_chapter": {
      "id": "prev_chapter_id",
      "title": "上一话"
    },
    "next_chapter": {
      "id": "next_chapter_id",
      "title": "下一话"
    }
  }
}
```

### 6. 获取相关漫画

**接口地址**: `POST /api/comics/getRelatedComics`

**接口描述**: 获取与指定漫画相关的推荐漫画

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "comic_id": "comic_id",
    "limit": 10,
    "type": "similar"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "related_comic_id",
        "title": "相关漫画标题",
        "cover_url": "封面URL",
        "author": "作者",
        "rating": 4.3,
        "status": "连载中",
        "chapters_count": 50
      }
    ]
  }
}
```

## 漫画页面接口 (/comics/page)

### 1. 获取漫画页面列表

**接口地址**: `POST /api/comics/page/getComicPageList`

**接口描述**: 获取漫画章节的页面列表

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "chapter_id": "chapter_id",
    "sort_by": "order",
    "sort_order": "asc"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "page_id",
        "chapter_id": "chapter_id",
        "order": 1,
        "image_url": "页面图片URL",
        "thumbnail_url": "缩略图URL",
        "width": 800,
        "height": 1200,
        "file_size": 204800,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 20
  }
}
```

### 2. 获取漫画页面详情

**接口地址**: `POST /api/comics/page/getComicPageDetail`

**接口描述**: 获取指定页面的详细信息

**认证要求**: 无需认证

**请求参数**:
```json
{
  "data": {
    "page_id": "page_id"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "page_id",
    "chapter_id": "chapter_id",
    "chapter_title": "第1话 开始",
    "comic_id": "comic_id",
    "comic_title": "漫画标题",
    "order": 1,
    "image_url": "页面图片URL",
    "thumbnail_url": "缩略图URL",
    "width": 800,
    "height": 1200,
    "file_size": 204800,
    "alt_text": "图片描述",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 漫画互动接口（预留）

以下接口为预留接口，暂未实现：

### 漫画收藏接口
- 收藏漫画
- 取消收藏
- 获取收藏列表
- 检查收藏状态

### 漫画评论接口
- 获取评论列表
- 添加评论
- 删除评论
- 评论点赞
- 获取评论回复

### 漫画阅读历史接口
- 记录阅读历史
- 获取阅读历史
- 更新阅读进度
- 删除阅读历史

## 状态码说明

- `2000`: 请求成功
- `4000`: 请求参数错误
- `4001`: 漫画不存在
- `4002`: 章节不存在
- `4003`: 页面不存在
- `4004`: 分类不存在
- `5000`: 服务器内部错误

## 注意事项

1. 所有接口都使用 POST 方法
2. 请求和响应数据格式为 JSON
3. 分页参数中 pageNo 从 1 开始
4. 图片 URL 为完整的可访问地址
5. 时间格式统一使用 ISO 8601 格式
6. 部分互动功能接口为预留接口，具体实现需要根据业务需求进行开发