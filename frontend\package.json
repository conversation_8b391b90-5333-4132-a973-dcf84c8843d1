{"name": "myfirm-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.2", "element-plus": "^2.7.4", "pinia": "^2.1.7", "primeicons": "^7.0.0", "primevue": "^3.52.0", "vue": "^3.4.27", "vue-i18n": "^9.13.1", "vue-router": "^4.3.2"}, "devDependencies": {"@types/node": "^20.12.12", "@vitejs/plugin-vue": "^5.0.4", "@vueuse/core": "^13.5.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "sass": "^1.77.4", "tailwindcss": "^3.4.3", "typescript": "^5.2.2", "vite": "^5.2.0", "vue-tsc": "^2.0.19"}, "packageManager": "pnpm@8.15.5+sha512.b051a32c7e695833b84926d3b29b8cca57254b589f0649d899c6e9d0edb670b91ec7e2a43459bae73759bb5ce619c3266f116bf931ce22d1ef1759a7e45aa96f"}