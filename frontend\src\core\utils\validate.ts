/**
 * 验证工具函数
 */

/**
 * 验证规则接口
 */
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean
  message?: string
  errors?: string[]
}

/**
 * 表单验证规则
 */
export interface FormValidationRules {
  [field: string]: ValidationRule | ValidationRule[]
}

/**
 * 表单验证结果
 */
export interface FormValidationResult {
  valid: boolean
  errors: Record<string, string[]>
  firstError?: string
}

/**
 * 基础验证器
 */
export const BaseValidator = {
  /**
   * 验证是否为空
   */
  isEmpty(value: any): boolean {
    if (value === null || value === undefined) {
      return true
    }
    if (typeof value === 'string') {
      return value.trim().length === 0
    }
    if (Array.isArray(value)) {
      return value.length === 0
    }
    if (typeof value === 'object') {
      return Object.keys(value).length === 0
    }
    return false
  },
  
  /**
   * 验证是否为数字
   */
  isNumber(value: any): boolean {
    return !isNaN(Number(value)) && isFinite(Number(value))
  },
  
  /**
   * 验证是否为整数
   */
  isInteger(value: any): boolean {
    return Number.isInteger(Number(value))
  },
  
  /**
   * 验证是否为正数
   */
  isPositive(value: any): boolean {
    return this.isNumber(value) && Number(value) > 0
  },
  
  /**
   * 验证是否为负数
   */
  isNegative(value: any): boolean {
    return this.isNumber(value) && Number(value) < 0
  },
  
  /**
   * 验证数值范围
   */
  inRange(value: any, min: number, max: number): boolean {
    if (!this.isNumber(value)) {
      return false
    }
    const num = Number(value)
    return num >= min && num <= max
  },
  
  /**
   * 验证字符串长度
   */
  lengthInRange(value: string, min: number, max: number): boolean {
    if (typeof value !== 'string') {
      return false
    }
    return value.length >= min && value.length <= max
  },
  
  /**
   * 验证正则表达式
   */
  matchPattern(value: string, pattern: RegExp): boolean {
    if (typeof value !== 'string') {
      return false
    }
    return pattern.test(value)
  }
}

/**
 * 常用正则表达式
 */
export const RegexPatterns = {
  // 邮箱
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // 手机号（中国）
  phone: /^1[3-9]\d{9}$/,
  
  // 固定电话
  landline: /^0\d{2,3}-?\d{7,8}$/,
  
  // 身份证号
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  
  // 护照号
  passport: /^[a-zA-Z0-9]{5,17}$/,
  
  // 银行卡号
  bankCard: /^\d{16,19}$/,
  
  // 邮政编码
  zipCode: /^\d{6}$/,
  
  // URL
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  
  // IP地址
  ip: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  
  // MAC地址
  mac: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
  
  // 中文字符
  chinese: /^[\u4e00-\u9fa5]+$/,
  
  // 英文字符
  english: /^[a-zA-Z]+$/,
  
  // 数字和字母
  alphanumeric: /^[a-zA-Z0-9]+$/,
  
  // 用户名（字母、数字、下划线，3-16位）
  username: /^[a-zA-Z0-9_]{3,16}$/,
  
  // 密码（至少包含字母和数字，6-20位）
  password: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/,
  
  // 强密码（包含大小写字母、数字、特殊字符，8-20位）
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/,
  
  // 十六进制颜色
  hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  
  // 版本号
  version: /^\d+\.\d+\.\d+$/,
  
  // 车牌号
  licensePlate: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{4}[A-Z0-9挂学警港澳]$/
}

/**
 * 字符串验证器
 */
export const StringValidator = {
  /**
   * 验证邮箱
   */
  email(email: string): ValidationResult {
    if (BaseValidator.isEmpty(email)) {
      return { valid: false, message: '邮箱不能为空' }
    }
    if (!BaseValidator.matchPattern(email, RegexPatterns.email)) {
      return { valid: false, message: '邮箱格式不正确' }
    }
    return { valid: true }
  },
  
  /**
   * 验证手机号
   */
  phone(phone: string): ValidationResult {
    if (BaseValidator.isEmpty(phone)) {
      return { valid: false, message: '手机号不能为空' }
    }
    if (!BaseValidator.matchPattern(phone, RegexPatterns.phone)) {
      return { valid: false, message: '手机号格式不正确' }
    }
    return { valid: true }
  },
  
  /**
   * 验证身份证号
   */
  idCard(idCard: string): ValidationResult {
    if (BaseValidator.isEmpty(idCard)) {
      return { valid: false, message: '身份证号不能为空' }
    }
    if (!BaseValidator.matchPattern(idCard, RegexPatterns.idCard)) {
      return { valid: false, message: '身份证号格式不正确' }
    }
    
    // 验证校验码
    if (!this.validateIdCardChecksum(idCard)) {
      return { valid: false, message: '身份证号校验码不正确' }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证身份证校验码
   */
  validateIdCardChecksum(idCard: string): boolean {
    if (idCard.length !== 18) {
      return false
    }
    
    const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    let sum = 0
    for (let i = 0; i < 17; i++) {
      sum += parseInt(idCard[i]) * weights[i]
    }
    
    const checkCode = checkCodes[sum % 11]
    return idCard[17].toUpperCase() === checkCode
  },
  
  /**
   * 验证银行卡号
   */
  bankCard(cardNumber: string): ValidationResult {
    if (BaseValidator.isEmpty(cardNumber)) {
      return { valid: false, message: '银行卡号不能为空' }
    }
    
    const cleaned = cardNumber.replace(/\s/g, '')
    if (!BaseValidator.matchPattern(cleaned, RegexPatterns.bankCard)) {
      return { valid: false, message: '银行卡号格式不正确' }
    }
    
    // Luhn算法验证
    if (!this.validateLuhn(cleaned)) {
      return { valid: false, message: '银行卡号校验失败' }
    }
    
    return { valid: true }
  },
  
  /**
   * Luhn算法验证
   */
  validateLuhn(cardNumber: string): boolean {
    let sum = 0
    let alternate = false
    
    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let n = parseInt(cardNumber[i])
      
      if (alternate) {
        n *= 2
        if (n > 9) {
          n = (n % 10) + 1
        }
      }
      
      sum += n
      alternate = !alternate
    }
    
    return sum % 10 === 0
  },
  
  /**
   * 验证URL
   */
  url(url: string): ValidationResult {
    if (BaseValidator.isEmpty(url)) {
      return { valid: false, message: 'URL不能为空' }
    }
    if (!BaseValidator.matchPattern(url, RegexPatterns.url)) {
      return { valid: false, message: 'URL格式不正确' }
    }
    return { valid: true }
  },
  
  /**
   * 验证密码强度
   */
  password(password: string, level: 'weak' | 'medium' | 'strong' = 'medium'): ValidationResult {
    if (BaseValidator.isEmpty(password)) {
      return { valid: false, message: '密码不能为空' }
    }
    
    if (password.length < 6) {
      return { valid: false, message: '密码长度不能少于6位' }
    }
    
    if (password.length > 20) {
      return { valid: false, message: '密码长度不能超过20位' }
    }
    
    switch (level) {
      case 'weak':
        // 只要求长度
        break
      case 'medium':
        if (!BaseValidator.matchPattern(password, RegexPatterns.password)) {
          return { valid: false, message: '密码必须包含字母和数字' }
        }
        break
      case 'strong':
        if (!BaseValidator.matchPattern(password, RegexPatterns.strongPassword)) {
          return { valid: false, message: '密码必须包含大小写字母、数字和特殊字符' }
        }
        break
    }
    
    return { valid: true }
  },
  
  /**
   * 验证用户名
   */
  username(username: string): ValidationResult {
    if (BaseValidator.isEmpty(username)) {
      return { valid: false, message: '用户名不能为空' }
    }
    if (!BaseValidator.matchPattern(username, RegexPatterns.username)) {
      return { valid: false, message: '用户名只能包含字母、数字和下划线，长度3-16位' }
    }
    return { valid: true }
  }
}

/**
 * 数字验证器
 */
export const NumberValidator = {
  /**
   * 验证整数
   */
  integer(value: any, min?: number, max?: number): ValidationResult {
    if (BaseValidator.isEmpty(value)) {
      return { valid: false, message: '数值不能为空' }
    }
    
    if (!BaseValidator.isInteger(value)) {
      return { valid: false, message: '必须是整数' }
    }
    
    const num = Number(value)
    if (min !== undefined && num < min) {
      return { valid: false, message: `数值不能小于${min}` }
    }
    
    if (max !== undefined && num > max) {
      return { valid: false, message: `数值不能大于${max}` }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证浮点数
   */
  float(value: any, min?: number, max?: number, decimals?: number): ValidationResult {
    if (BaseValidator.isEmpty(value)) {
      return { valid: false, message: '数值不能为空' }
    }
    
    if (!BaseValidator.isNumber(value)) {
      return { valid: false, message: '必须是数字' }
    }
    
    const num = Number(value)
    if (min !== undefined && num < min) {
      return { valid: false, message: `数值不能小于${min}` }
    }
    
    if (max !== undefined && num > max) {
      return { valid: false, message: `数值不能大于${max}` }
    }
    
    if (decimals !== undefined) {
      const decimalPart = String(value).split('.')[1]
      if (decimalPart && decimalPart.length > decimals) {
        return { valid: false, message: `小数位数不能超过${decimals}位` }
      }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证正数
   */
  positive(value: any): ValidationResult {
    if (BaseValidator.isEmpty(value)) {
      return { valid: false, message: '数值不能为空' }
    }
    
    if (!BaseValidator.isPositive(value)) {
      return { valid: false, message: '必须是正数' }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证年龄
   */
  age(value: any): ValidationResult {
    const result = this.integer(value, 0, 150)
    if (!result.valid) {
      return { valid: false, message: '年龄必须是0-150之间的整数' }
    }
    return { valid: true }
  }
}

/**
 * 日期验证器
 */
export const DateValidator = {
  /**
   * 验证日期格式
   */
  date(value: any): ValidationResult {
    if (BaseValidator.isEmpty(value)) {
      return { valid: false, message: '日期不能为空' }
    }
    
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      return { valid: false, message: '日期格式不正确' }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证日期范围
   */
  dateRange(value: any, min?: Date, max?: Date): ValidationResult {
    const dateResult = this.date(value)
    if (!dateResult.valid) {
      return dateResult
    }
    
    const date = new Date(value)
    
    if (min && date < min) {
      return { valid: false, message: `日期不能早于${min.toLocaleDateString()}` }
    }
    
    if (max && date > max) {
      return { valid: false, message: `日期不能晚于${max.toLocaleDateString()}` }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证生日
   */
  birthday(value: any): ValidationResult {
    const today = new Date()
    const minDate = new Date(today.getFullYear() - 150, 0, 1)
    
    return this.dateRange(value, minDate, today)
  },
  
  /**
   * 验证未来日期
   */
  future(value: any): ValidationResult {
    const dateResult = this.date(value)
    if (!dateResult.valid) {
      return dateResult
    }
    
    const date = new Date(value)
    const now = new Date()
    
    if (date <= now) {
      return { valid: false, message: '日期必须是未来时间' }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证过去日期
   */
  past(value: any): ValidationResult {
    const dateResult = this.date(value)
    if (!dateResult.valid) {
      return dateResult
    }
    
    const date = new Date(value)
    const now = new Date()
    
    if (date >= now) {
      return { valid: false, message: '日期必须是过去时间' }
    }
    
    return { valid: true }
  }
}

/**
 * 文件验证器
 */
export const FileValidator = {
  /**
   * 验证文件大小
   */
  size(file: File, maxSize: number): ValidationResult {
    if (file.size > maxSize) {
      return { valid: false, message: `文件大小不能超过${this.formatFileSize(maxSize)}` }
    }
    return { valid: true }
  },
  
  /**
   * 验证文件类型
   */
  type(file: File, allowedTypes: string[]): ValidationResult {
    const fileType = file.type.toLowerCase()
    const fileName = file.name.toLowerCase()
    
    const isAllowed = allowedTypes.some(type => {
      if (type.includes('/')) {
        // MIME类型
        return fileType === type || fileType.startsWith(type.replace('*', ''))
      } else {
        // 文件扩展名
        return fileName.endsWith(type.toLowerCase())
      }
    })
    
    if (!isAllowed) {
      return { valid: false, message: `文件类型不支持，只支持：${allowedTypes.join(', ')}` }
    }
    
    return { valid: true }
  },
  
  /**
   * 验证图片文件
   */
  image(file: File, maxSize = 5 * 1024 * 1024): ValidationResult {
    const typeResult = this.type(file, ['image/*', '.jpg', '.jpeg', '.png', '.gif', '.webp'])
    if (!typeResult.valid) {
      return typeResult
    }
    
    return this.size(file, maxSize)
  },
  
  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }
}

/**
 * 表单验证器
 */
export class FormValidator {
  private rules: FormValidationRules
  
  constructor(rules: FormValidationRules) {
    this.rules = rules
  }
  
  /**
   * 验证单个字段
   */
  validateField(field: string, value: any): ValidationResult {
    const fieldRules = this.rules[field]
    if (!fieldRules) {
      return { valid: true }
    }
    
    const rules = Array.isArray(fieldRules) ? fieldRules : [fieldRules]
    const errors: string[] = []
    
    for (const rule of rules) {
      const result = this.validateRule(value, rule)
      if (!result.valid && result.message) {
        errors.push(result.message)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      message: errors[0]
    }
  }
  
  /**
   * 验证整个表单
   */
  validate(data: Record<string, any>): FormValidationResult {
    const errors: Record<string, string[]> = {}
    let firstError: string | undefined
    
    for (const field in this.rules) {
      const result = this.validateField(field, data[field])
      if (!result.valid && result.errors) {
        errors[field] = result.errors
        if (!firstError) {
          firstError = result.errors[0]
        }
      }
    }
    
    return {
      valid: Object.keys(errors).length === 0,
      errors,
      firstError
    }
  }
  
  /**
   * 验证单个规则
   */
  private validateRule(value: any, rule: ValidationRule): ValidationResult {
    // 必填验证
    if (rule.required && BaseValidator.isEmpty(value)) {
      return { valid: false, message: rule.message || '此字段为必填项' }
    }
    
    // 如果值为空且非必填，跳过其他验证
    if (BaseValidator.isEmpty(value) && !rule.required) {
      return { valid: true }
    }
    
    // 最小值验证
    if (rule.min !== undefined && BaseValidator.isNumber(value) && Number(value) < rule.min) {
      return { valid: false, message: rule.message || `值不能小于${rule.min}` }
    }
    
    // 最大值验证
    if (rule.max !== undefined && BaseValidator.isNumber(value) && Number(value) > rule.max) {
      return { valid: false, message: rule.message || `值不能大于${rule.max}` }
    }
    
    // 最小长度验证
    if (rule.minLength !== undefined && typeof value === 'string' && value.length < rule.minLength) {
      return { valid: false, message: rule.message || `长度不能少于${rule.minLength}位` }
    }
    
    // 最大长度验证
    if (rule.maxLength !== undefined && typeof value === 'string' && value.length > rule.maxLength) {
      return { valid: false, message: rule.message || `长度不能超过${rule.maxLength}位` }
    }
    
    // 正则验证
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      return { valid: false, message: rule.message || '格式不正确' }
    }
    
    // 自定义验证器
    if (rule.validator) {
      const result = rule.validator(value)
      if (result === false) {
        return { valid: false, message: rule.message || '验证失败' }
      }
      if (typeof result === 'string') {
        return { valid: false, message: result }
      }
    }
    
    return { valid: true }
  }
}

/**
 * 验证工具函数
 */
export const ValidateUtils = {
  /**
   * 创建表单验证器
   */
  createValidator(rules: FormValidationRules): FormValidator {
    return new FormValidator(rules)
  },
  
  /**
   * 快速验证
   */
  quick(value: any, rules: ValidationRule | ValidationRule[]): ValidationResult {
    const validator = new FormValidator({ field: rules })
    return validator.validateField('field', value)
  },
  
  /**
   * 验证多个值
   */
  multiple(data: Record<string, any>, rules: FormValidationRules): FormValidationResult {
    const validator = new FormValidator(rules)
    return validator.validate(data)
  },
  
  /**
   * 异步验证
   */
  async validateAsync(value: any, validator: (value: any) => Promise<boolean | string>): Promise<ValidationResult> {
    try {
      const result = await validator(value)
      if (result === true) {
        return { valid: true }
      }
      return { valid: false, message: typeof result === 'string' ? result : '验证失败' }
    } catch (error) {
      return { valid: false, message: '验证过程中发生错误' }
    }
  }
}

/**
 * 默认导出
 */
export default {
  BaseValidator,
  RegexPatterns,
  StringValidator,
  NumberValidator,
  DateValidator,
  FileValidator,
  FormValidator,
  ValidateUtils
}