import type { ComicChapter, ComicPage } from '@/types/index'
import { post, page, postPage } from '@/shared/composables'
import { corePost, coreGetPage, corePostPageList } from '@/shared/composables'
import type { PageResponse, RequestParams, PageRequestParams } from '@/shared/composables'

// 通用响应接口
interface ListResponse<T> {
    list: T[]
    total: number
    currentPage: number
    pageSize: number
}

// 获取漫画分类
export const getComicCategories = () => {
    return corePostPageList('/comics/category/getComicCategoryList', {
        data: {},
        page: { page: 1, pageSize: 100 }
    })
}

// 获取漫画列表
export interface ComicListParams {
    page: {
        pageNo: number
        pageSize: number
    }
    data: {
        categoryId?: string
        keyword?: string
        sortBy?: 'popularity' | 'latest' | 'rating'
    }
}

export const getComicList = (params: ComicListParams) => {
    return corePostPageList('/comics/getComicList', {
        data: params.data,
        page: { page: params.page.pageNo, pageSize: params.page.pageSize }
    })
}

// 获取漫画详情
export interface ComicDetailParams {
    data: {
        comicId: string
    }
    page: {
        pageNo: number
        pageSize: number
    }
}

export const getComicDetail = (params: ComicDetailParams) => {
    return corePost('/comics/getComicDetail', params.data)
}

// 获取章节内容
export interface ChapterParams {
    chapterId: string
}

export interface ChapterResponse {
    chapter: ComicChapter
    pages: ComicPage[]
}

export const getChapterContent = (params: ChapterParams) => {
    return corePost('/comics/getChapterContent', params)
}

// 获取相关推荐
export const getRelatedComics = (params: ComicDetailParams) => {
    return corePost('/comics/getRelatedComics', params.data)
}
