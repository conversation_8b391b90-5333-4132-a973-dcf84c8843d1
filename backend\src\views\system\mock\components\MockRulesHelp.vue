<template>
  <el-dialog
    v-model="visible"
    title="Mock规则说明"
    width="800px"
    :before-close="handleClose"
  >
    <div class="mock-rules-help">
      <el-alert
        title="Mock.js 规则说明"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;"
      >
        <template #default>
          <p>Mock.js 是一个强大的前端数据模拟库，支持丰富的数据生成规则。</p>
        </template>
      </el-alert>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 智能匹配规则 -->
        <el-tab-pane label="智能匹配" name="smart">
          <el-alert
            title="智能类型匹配说明"
            type="success"
            :closable="false"
            style="margin-bottom: 15px;"
          >
            <template #default>
              <p>系统会根据数据库字段类型和长度自动生成最合适的Mock规则，无需手动设置。</p>
            </template>
          </el-alert>
          
          <h4>字符串类型匹配规则</h4>
          <el-table :data="stringTypeRules" border style="width: 100%; margin-bottom: 20px;">
            <el-table-column prop="type" label="字段类型" width="150" />
            <el-table-column prop="rule" label="生成规则" width="200" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="150" />
          </el-table>

          <h4>数字类型匹配规则</h4>
          <el-table :data="numberTypeRules" border style="width: 100%; margin-bottom: 20px;">
            <el-table-column prop="type" label="字段类型" width="150" />
            <el-table-column prop="rule" label="生成规则" width="200" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="150" />
          </el-table>

          <h4>特殊类型匹配规则</h4>
          <el-table :data="specialTypeRules" border style="width: 100%;">
            <el-table-column prop="type" label="字段类型" width="150" />
            <el-table-column prop="rule" label="生成规则" width="200" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="150" />
          </el-table>

          <h4>tinyint类型智能判断</h4>
          <p>系统会根据字段注释和长度智能判断tinyint类型的用途：</p>
          <ul>
            <li><strong>布尔值判断</strong>：注释包含"是否"、"启用"、"禁用"等关键词时，生成0或1</li>
            <li><strong>枚举值判断</strong>：注释包含"0,1,2"等数字序列时，从中随机选择</li>
            <li><strong>长度判断</strong>：
              <ul>
                <li>tinyint(1) → 布尔值 (0或1)</li>
                <li>tinyint(2) → 0-99范围</li>
                <li>tinyint(3) → 0-127范围</li>
              </ul>
            </li>
          </ul>
          
          <h4>外键字段处理</h4>
          <p>外键字段会以橙色背景高亮显示，并提供以下功能：</p>
          <ul>
            <li><strong>字段标识</strong>：外键字段在注释列显示关联表信息</li>
            <li><strong>排除功能</strong>：点击删除按钮可排除该外键字段</li>
            <li><strong>数据关联</strong>：未排除的外键会从关联表获取真实数据</li>
            <li><strong>视觉区分</strong>：排除的字段显示为灰色</li>
          </ul>
        </el-tab-pane>

        <!-- 基础规则 -->
        <el-tab-pane label="基础规则" name="basic">
          <el-table :data="basicRules" border style="width: 100%">
            <el-table-column prop="rule" label="规则" width="150" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="200" />
          </el-table>
        </el-tab-pane>

        <!-- 文本规则 -->
        <el-tab-pane label="文本规则" name="text">
          <el-table :data="textRules" border style="width: 100%">
            <el-table-column prop="rule" label="规则" width="200" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="200" />
          </el-table>
        </el-tab-pane>

        <!-- 数字规则 -->
        <el-tab-pane label="数字规则" name="number">
          <el-table :data="numberRules" border style="width: 100%">
            <el-table-column prop="rule" label="规则" width="200" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="200" />
          </el-table>
        </el-tab-pane>

        <!-- 日期时间 -->
        <el-tab-pane label="日期时间" name="datetime">
          <el-table :data="datetimeRules" border style="width: 100%">
            <el-table-column prop="rule" label="规则" width="250" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="200" />
          </el-table>
        </el-tab-pane>

        <!-- 地址信息 -->
        <el-tab-pane label="地址信息" name="address">
          <el-table :data="addressRules" border style="width: 100%">
            <el-table-column prop="rule" label="规则" width="150" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="200" />
          </el-table>
        </el-tab-pane>

        <!-- 网络相关 -->
        <el-tab-pane label="网络相关" name="network">
          <el-table :data="networkRules" border style="width: 100%">
            <el-table-column prop="rule" label="规则" width="200" />
            <el-table-column prop="description" label="说明" />
            <el-table-column prop="example" label="示例" width="200" />
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <el-alert
        title="提示"
        type="warning"
        :closable="false"
        style="margin-top: 20px;"
      >
        <template #default>
          <ul style="margin: 0; padding-left: 20px;">
            <li><strong>智能匹配优先：</strong>系统会根据字段类型和长度自动生成最合适的规则</li>
            <li><strong>字段名称推断：</strong>根据字段名称（如email、phone、title等）智能推断数据类型</li>
            <li><strong>长度精确控制：</strong>varchar(20)生成1-20字符，tinyint(2)生成0-99范围数字</li>
            <li><strong>手动规则覆盖：</strong>手动设置的规则会覆盖自动生成的规则</li>
            <li><strong>规则格式：</strong>所有规则都以 @ 符号开头，支持参数传递</li>
            <li><strong>组合使用：</strong>可以组合使用多个规则，留空将自动生成</li>
          </ul>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('smart');

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
});

// 智能匹配规则 - 字符串类型
const stringTypeRules = [
  { type: 'varchar(10)', rule: '@string(1, 10)', description: '短字符串，长度1-10', example: 'aBcDe' },
  { type: 'varchar(50)', rule: '@cword(2, 20)', description: '中等长度词汇，2-20字符', example: '测试词汇' },
  { type: 'varchar(255)', rule: '@ctitle(3, 20)', description: '长字符串标题，3-20词', example: '这是一个测试标题' },
  { type: 'varchar(500+)', rule: '@cparagraph(1, 2)', description: '超长字符串段落', example: '这是一个测试段落...' },
  { type: 'text', rule: '@cparagraph(1, 3)', description: '文本段落，1-3句', example: '这是文本内容...' },
  { type: 'char(n)', rule: '@string(1, n)', description: '固定长度字符串', example: 'ABC' }
];

// 智能匹配规则 - 数字类型
const numberTypeRules = [
  { type: 'tinyint(1)', rule: '@pick([0, 1])', description: '布尔值，0或1', example: '1' },
  { type: 'tinyint(2)', rule: '@integer(0, 99)', description: '0-99范围整数', example: '42' },
  { type: 'tinyint(3)', rule: '@integer(0, 127)', description: '0-127范围整数', example: '85' },
  { type: 'int(5)', rule: '@integer(1, 99999)', description: '1-99999范围整数', example: '12345' },
  { type: 'int(10)', rule: '@integer(1, 999999)', description: '1-999999范围整数', example: '567890' },
  { type: 'decimal(10,2)', rule: '@float(0, 99999999, 2, 2)', description: '浮点数，2位小数', example: '1234.56' },
  { type: 'decimal(8,2)', rule: '@float(0, 999999, 2, 2)', description: '价格类型，2位小数', example: '999.99' }
];

// 智能匹配规则 - 特殊类型
const specialTypeRules = [
  { type: 'enum(...)', rule: '@pick([值1, 值2])', description: '从枚举值中随机选择', example: 'active' },
  { type: 'set(...)', rule: '@pick([值1, 值2])', description: '从集合值中随机选择', example: 'tag1' },
  { type: 'json', rule: '{"key": "@string"}', description: 'JSON格式数据', example: '{"name": "test"}' },
  { type: 'datetime', rule: '@datetime("yyyy-MM-dd HH:mm:ss")', description: '日期时间格式', example: '2023-12-25 14:30:45' },
  { type: 'date', rule: '@date("yyyy-MM-dd")', description: '日期格式', example: '2023-12-25' },
  { type: 'time', rule: '@time("HH:mm:ss")', description: '时间格式', example: '14:30:45' },
  { type: 'year', rule: '@integer(1970, 2030)', description: '年份范围', example: '2023' },
  { type: 'binary(n)', rule: '@string("lower", n)', description: '二进制数据', example: 'abc123' }
];

// 基础规则
const basicRules = [
  { rule: '@cname', description: '中文姓名', example: '张三' },
  { rule: '@name', description: '英文姓名', example: 'John Smith' },
  { rule: '@email', description: '邮箱地址', example: '<EMAIL>' },
  { rule: '@phone', description: '手机号码', example: '13812345678' },
  { rule: '@id', description: '身份证号', example: '110101199001011234' },
  { rule: '@uuid', description: 'UUID', example: '550e8400-e29b-41d4-a716-************' },
  { rule: '@guid', description: 'GUID', example: '550e8400-e29b-41d4-a716-************' }
];

// 文本规则
const textRules = [
  { rule: '@ctitle(5, 20)', description: '中文标题，5-20个字符', example: '这是一个中文标题' },
  { rule: '@cparagraph(1, 3)', description: '中文段落，1-3句话', example: '这是一个中文段落。包含多个句子。' },
  { rule: '@cword(2, 6)', description: '中文词汇，2-6个字符', example: '测试词汇' },
  { rule: '@title(3, 7)', description: '英文标题，3-7个单词', example: 'This Is A Title' },
  { rule: '@sentence(3, 18)', description: '英文句子，3-18个单词', example: 'This is a sample sentence.' },
  { rule: '@string(5, 20)', description: '随机字符串，5-20个字符', example: 'aBcDeFgHiJ' }
];

// 数字规则
const numberRules = [
  { rule: '@integer(1, 100)', description: '1-100之间的整数', example: '42' },
  { rule: '@integer(1, 1000)', description: '1-1000之间的整数', example: '567' },
  { rule: '@float(0, 100, 2, 2)', description: '0-100之间的浮点数，2位小数', example: '45.67' },
  { rule: '@float(0, 10000, 2, 2)', description: '价格，0-10000，2位小数', example: '1234.56' },
  { rule: '@natural(1, 1000)', description: '1-1000之间的自然数', example: '123' },
  { rule: '@pick([0, 1])', description: '布尔值（0或1）', example: '1' },
  { rule: '@pick([0, 1, 2])', description: '状态值（0、1或2）', example: '2' }
];

// 日期规则
const datetimeRules = [
  { rule: '@datetime', description: '随机日期时间', example: '2023-12-25 14:30:45' },
  { rule: '@date', description: '随机日期', example: '2023-12-25' },
  { rule: '@time', description: '随机时间', example: '14:30:45' },
  { rule: '@datetime("yyyy-MM-dd HH:mm:ss")', description: '格式化日期时间', example: '2023-12-25 14:30:45' },
  { rule: '@date("yyyy-MM-dd")', description: '格式化日期', example: '2023-12-25' },
  { rule: '@time("HH:mm:ss")', description: '格式化时间', example: '14:30:45' }
];

// 地址规则
const addressRules = [
  { rule: '@county(true)', description: '省市区完整地址', example: '北京市 北京市 朝阳区' },
  { rule: '@province', description: '省份', example: '北京市' },
  { rule: '@city', description: '城市', example: '北京市' },
  { rule: '@county', description: '区县', example: '朝阳区' },
  { rule: '@region', description: '地区', example: '华北' }
];

// 网络规则
const networkRules = [
  { rule: '@url', description: '网址', example: 'http://example.com' },
  { rule: '@domain', description: '域名', example: 'example.com' },
  { rule: '@ip', description: 'IP地址', example: '***********' },
  { rule: '@image("200x200")', description: '图片URL，200x200尺寸', example: 'https://picsum.photos/200' }
];

// 处理关闭
const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.mock-rules-help {
  max-height: 600px;
  overflow-y: auto;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-tabs__content) {
  padding: 15px;
}
</style> 