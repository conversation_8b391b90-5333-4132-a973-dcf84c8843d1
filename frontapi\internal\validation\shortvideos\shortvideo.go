package shortvideos

// CreateShortVideoRequest 创建短视频请求
type CreateShortVideoRequest struct {
	Title         string   `json:"title" validate:"required|minLen:3|maxLen:50"`
	Description   string   `json:"description"`
	URL           string   `json:"url" validate:"required"`
	Duration      int      `json:"duration"`
	Resolution    string   `json:"resolution"`
	CategoryID    *string  `json:"category_id"`
	CategoryName  string   `json:"categoryName"`
	CreatorID     *string  `json:"creator_id"`
	CreatorName   string   `json:"creator_name"`
	CreatorAvatar string   `json:"creator_avatar"`
	Tags          []string `json:"tags"`
	IsPaid        bool     `json:"is_paid"`
	IsFeatured    bool     `json:"is_featured"`
	Price         float64  `json:"price"`
	SrcType       int8     `json:"src_type"`
	Src           string   `json:"src"`
}

// UpdateShortVideoRequest 更新短视频请求
type UpdateShortVideoRequest struct {
	Id          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	// Cover         string   `json:"cover"`
	CategoryID    *string  `json:"category_id"`
	CategoryName  string   `json:"category_name"`
	CreatorName   string   `json:"creator_name"`
	CreatorAvatar string   `json:"creator_avatar"`
	URL           *string  `json:"url"`
	CreatorID     *string  `json:"creator_id"`
	Tags          []string `json:"tags"`
	IsPaid        *int8    `json:"is_paid" validate:"int|min:0|max:1"`
	Price         *float64 `json:"price"`
	Status        *int8    `json:"status"`
}
