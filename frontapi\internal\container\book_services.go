package container

import (
	bookRepo "frontapi/internal/repository/books"
	bookSrv "frontapi/internal/service/books"
)

// InitBookServices 初始化电子书相关服务
func InitBookServices(b *ServiceBuilder) {
	// 电子书相关仓库
	bookRepoImpl := bookRepo.NewBookRepository(b.DB())
	bookCategoryRepoImpl := bookRepo.NewCategoryRepository(b.DB())
	bookChapterRepoImpl := bookRepo.NewChapterRepository(b.DB())
	bookFavoriteRepoImpl := bookRepo.NewFavoriteRepository(b.DB())
	bookmarkRepoImpl := bookRepo.NewBookmarkRepository(b.DB())
	readHistoryRepoImpl := bookRepo.NewReadHistoryRepository(b.DB())

	// 电子书相关服务
	container := b.Services()
	container.BookService = bookSrv.NewBookService(bookRepoImpl, bookCategoryRepoImpl, readHistoryRepoImpl)
	container.BookCategoryService = bookSrv.NewBookCategoryService(bookCategoryRepoImpl)
	container.BookChapterService = bookSrv.NewChapterService(bookChapterRepoImpl, bookRepoImpl)
	container.BookFavoriteService = bookSrv.NewFavoriteService(bookFavoriteRepoImpl, bookRepoImpl)
	container.BookmarkService = bookSrv.NewBookmarkService(bookmarkRepoImpl, bookRepoImpl, bookChapterRepoImpl)
	container.ReadHistoryService = bookSrv.NewReadHistoryService(readHistoryRepoImpl, bookRepoImpl, bookChapterRepoImpl)
}
