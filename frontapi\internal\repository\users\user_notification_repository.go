package users

import (
	"context"
	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// UserNotificationRepository 用户通知数据访问接口
type UserNotificationRepository interface {
	base.ExtendedRepository[users.UserNotification]
	MarkAllAsRead(ctx context.Context, userID string) error
	CountUnreadByUserID(ctx context.Context, userID string) (int64, error)
}

// userNotificationRepository 用户通知数据访问实现
type userNotificationRepository struct {
	base.ExtendedRepository[users.UserNotification]
}

// NewUserNotificationRepository 创建用户通知仓库实例
func NewUserNotificationRepository(db *gorm.DB) UserNotificationRepository {
	return &userNotificationRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserNotification](db),
	}
}

// MarkAllAsRead 将用户的所有通知标记为已读
func (r *userNotificationRepository) MarkAllAsRead(ctx context.Context, userID string) error {
	return r.GetDBWithContext(ctx).Model(&users.UserNotification{}).Where("user_id = ?", userID).Update("is_read", true).Error
}

// CountUnreadByUserID 根据用户ID统计未读用户通知数量
func (r *userNotificationRepository) CountUnreadByUserID(ctx context.Context, userID string) (int64, error) {
	return r.Count(ctx, map[string]interface{}{
		"user_id": userID,
		"is_read": false,
	})
}
