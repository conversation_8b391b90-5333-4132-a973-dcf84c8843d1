package users

import (
	"frontapi/internal/api"
	service "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
)

// UserController 用户控制器
type UserController struct {
	UserService        service.UserService
	VideoService       videoSrv.VideoService
	api.BaseController // 继承Response
}

// NewUserController 创建用户控制器实例
func NewUserController(userService service.UserService, videoService videoSrv.VideoService) *UserController {
	return &UserController{
		UserService:  userService,
		VideoService: videoService,
	}
}
