package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// ShortsCollection 用户短视频收藏表
type UserShortsCollection struct {
	models.BaseModel
	UserID          string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                  //用户ID
	ShortsID        string         `gorm:"column:shorts_id;type:string;not null;comment:短视频ID" json:"shorts_id"`                             //短视频ID
	ShortsTitle     string         `gorm:"column:shorts_title;type:string;size:255;comment:短视频标题" json:"shorts_title"`                       //短视频标题
	ShortsCover     string         `gorm:"column:shorts_cover;type:string;size:255;comment:短视频封面" json:"shorts_cover"`                       //短视频封面
	ShortsDuration  int            `gorm:"column:shorts_duration;type:int;comment:短视频时长(秒)" json:"shorts_duration"`                          //短视频时长(秒)
	CreatorID       string         `gorm:"column:creator_id;type:string;comment:创作者ID" json:"creator_id"`                                    //创作者ID
	CreatorName     string         `gorm:"column:creator_name;type:string;size:50;comment:创作者名称" json:"creator_name"`                        //创作者名称
	CreatorAvatar   string         `gorm:"column:creator_avatar;type:string;size:255;comment:创作者头像" json:"creator_avatar"`                   //创作者头像
	CategoryID      string         `gorm:"column:category_id;type:string;comment:分类ID" json:"category_id"`                                   //分类ID
	CategoryName    string         `gorm:"column:category_name;type:string;size:50;comment:分类名称" json:"category_name"`                       //分类名称
	CollectionTime  types.JSONTime `gorm:"column:collection_time;type:time;default:current_timestamp;comment:收藏时间" json:"collection_time"`   //收藏时间
	CollectionGroup string         `gorm:"column:collection_group;type:string;size:50;default:'默认收藏夹';comment:收藏分组" json:"collection_group"` //收藏分组
	Note            string         `gorm:"column:note;type:string;size:255;comment:收藏备注" json:"note"`                                        //收藏备注

}

// TableName 表名
func (UserShortsCollection) TableName() string {
	return "ly_user_shorts_collections"
}
