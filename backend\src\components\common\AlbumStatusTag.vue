<template>
  <el-tag 
    :type="tagType" 
    size="small" 
    effect="light"
    class="album-status-tag"
  >
    <el-icon v-if="iconComponent" class="mr-1">
      <component :is="iconComponent" />
    </el-icon>
    {{ statusText }}
  </el-tag>
</template>

<script setup lang="ts">
import { CircleCheck, Delete, Lock } from '@element-plus/icons-vue';
import { computed } from 'vue';

interface Props {
  status: number;
}

const props = defineProps<Props>();

// 状态文本映射
const statusText = computed(() => {
  switch (props.status) {
    case 1:
      return '正常';
    case 0:
      return '禁用';
    case -4:
      return '已删除';
    default:
      return '未知';
  }
});

// 标签类型映射
const tagType = computed(() => {
  switch (props.status) {
    case 1:
      return 'success';
    case 0:
      return 'warning';
    case -4:
      return 'danger';
    default:
      return 'info';
  }
});

// 图标组件映射
const iconComponent = computed(() => {
  switch (props.status) {
    case 1:
      return CircleCheck;
    case 0:
      return Lock;
    case -4:
      return Delete;
    default:
      return null;
  }
});
</script>

<style scoped lang="scss">
.album-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;

  .el-icon {
    font-size: 12px;
  }
}
</style> 