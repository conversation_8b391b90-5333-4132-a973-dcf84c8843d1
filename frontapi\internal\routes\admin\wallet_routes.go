package admin

import (
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterWalletRoutes 注册钱包相关路由
func RegisterWalletRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	//walletController *wallets.WalletController, withdrawController *wallets.WithdrawRequestController

	// 钱包和提现控制器
	// walletController := wallets.NewWalletController(services.WalletService)
	// withdrawController := wallets.NewWithdrawRequestController(services.WithdrawRequestService)

	// // 钱包相关路由
	// wallet := apiGroup.Group("/wallets")
	// {

	// }

	// // 提现相关路由
	// withdraw := apiGroup.Group("/withdraws")
	// {

	// }

}
