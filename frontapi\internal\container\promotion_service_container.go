package container

import (
	promotionRepo "frontapi/internal/repository/promotion"
	promotionSrv "frontapi/internal/service/promotion"
)

// InitPromotionServices 初始化推广相关服务
func InitPromotionServices(b *ServiceBuilder) {
	//注册Promotion相关的仓库和服务
	promotionCompaign := promotionRepo.NewPromotionCampaignRepository(b.DB())
	promotionRecord := promotionRepo.NewPromotionRecordRepository(b.DB())
	invitationCommission := promotionRepo.NewInvitationCommissionRepository(b.DB())
	invitationRule := promotionRepo.NewInvitationRuleRepository(b.DB())

	container := b.Services()
	container.PromotionCampaignService = promotionSrv.NewPromotionCampaignService(promotionCompaign)
	container.PromotionRecordService = promotionSrv.NewPromotionRecordService(promotionRecord)
	container.InvitationCommissionService = promotionSrv.NewInvitationCommissionService(invitationCommission)
	container.InvitationRuleService = promotionSrv.NewInvitationRuleService(invitationRule)
}
