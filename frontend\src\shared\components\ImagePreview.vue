<template>
  <el-dialog
    v-model="visible"
    :width="dialogWidth"
    :style="{ height: dialogHeight }"
    class="image-preview-dialog"
    :show-close="false"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    @close="handleClose"
    append-to-body
  >
    <div class="image-container">
      <img
        :src="currentImage"
        :alt="`预览图片 ${currentIndex + 1}`"
        class="preview-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />
      
      <!-- 左右切换按钮 (仅多张图片时显示) -->
      <button
        v-if="images.length > 1"
        class="nav-btn prev-btn"
        @click="prevImage"
        :disabled="currentIndex === 0 && !infinite"
      >
        <el-icon><ArrowLeft /></el-icon>
      </button>
      
      <button
        v-if="images.length > 1"
        class="nav-btn next-btn"
        @click="nextImage"
        :disabled="currentIndex === images.length - 1 && !infinite"
      >
        <el-icon><ArrowRight /></el-icon>
      </button>
      
      <!-- 关闭按钮 -->
      <button class="nav-btn close-btn" @click="handleClose">
        <el-icon><Close /></el-icon>
      </button>
      
      <!-- 图片指示器 (仅多张图片时显示) -->
      <div v-if="images.length > 1" class="image-indicator">
        {{ currentIndex + 1 }} / {{ images.length }}
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElDialog, ElIcon } from 'element-plus'
import { ArrowLeft, ArrowRight, Close } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
  images: string | string[]
  initialIndex?: number
  infinite?: boolean
  maxWidth?: string
  maxHeight?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
  (e: 'switch', index: number): void
}

const props = withDefaults(defineProps<Props>(), {
  initialIndex: 0,
  infinite: true,
  maxWidth: '80vw',
  maxHeight: '80vh'
})

const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentIndex = ref(props.initialIndex)
const dialogWidth = ref('80%')
const dialogHeight = ref('80vh')
const imageLoaded = ref(false)

// 计算属性
const imageList = computed(() => {
  if (typeof props.images === 'string') {
    return [props.images]
  }
  return props.images || []
})

const currentImage = computed(() => {
  return imageList.value[currentIndex.value] || ''
})

// 监听器
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    currentIndex.value = props.initialIndex
    imageLoaded.value = false
  }
})

watch(() => props.initialIndex, (newVal) => {
  currentIndex.value = newVal
})

// 方法
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img && currentIndex.value === props.initialIndex) {
    // 根据第一张图片的尺寸设定弹窗大小
    const aspectRatio = img.naturalWidth / img.naturalHeight
    const maxWidth = window.innerWidth * 0.8
    const maxHeight = window.innerHeight * 0.8
    
    let width = img.naturalWidth
    let height = img.naturalHeight
    
    // 如果图片超出最大尺寸，按比例缩放
    if (width > maxWidth) {
      width = maxWidth
      height = width / aspectRatio
    }
    
    if (height > maxHeight) {
      height = maxHeight
      width = height * aspectRatio
    }
    
    dialogWidth.value = `${Math.min(width + 40, maxWidth)}px`
    dialogHeight.value = `${Math.min(height + 40, maxHeight)}px`
  }
  imageLoaded.value = true
}

const handleImageError = () => {
  console.error('图片加载失败:', currentImage.value)
}

const prevImage = () => {
  if (imageList.value.length <= 1) return
  
  if (currentIndex.value > 0) {
    currentIndex.value--
  } else if (props.infinite) {
    currentIndex.value = imageList.value.length - 1
  }
  
  emit('switch', currentIndex.value)
}

const nextImage = () => {
  if (imageList.value.length <= 1) return
  
  if (currentIndex.value < imageList.value.length - 1) {
    currentIndex.value++
  } else if (props.infinite) {
    currentIndex.value = 0
  }
  
  emit('switch', currentIndex.value)
}

const handleClose = () => {
  visible.value = false
  emit('close')
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!visible.value) return
  
  switch (event.key) {
    case 'ArrowLeft':
      prevImage()
      break
    case 'ArrowRight':
      nextImage()
      break
    case 'Escape':
      handleClose()
      break
  }
}

// 生命周期
watch(visible, (newVal) => {
  if (newVal) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style lang="scss" scoped>
.image-preview-dialog {
  :deep(.el-dialog__header) {
    display: none;
  }
  
  :deep(.el-dialog__body) {
    padding: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000;
    border-radius: 12px;
    overflow: hidden;
  }
  
  :deep(.el-dialog) {
    background-color: rgba(0, 0, 0, 0.95);
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .preview-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 8px;
      transition: transform 0.3s ease;
    }
    
    .nav-btn {
      position: absolute;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      z-index: 10;
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: none;
          box-shadow: none;
        }
      }
      
      .el-icon {
        font-size: 20px;
      }
    }
    
    .prev-btn {
      left: 24px;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .next-btn {
      right: 24px;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .close-btn {
      top: 20px;
      right: 20px;
    }
    
    .image-indicator {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-preview-dialog {
    .image-container {
      .nav-btn {
        width: 40px;
        height: 40px;
        
        .el-icon {
          font-size: 16px;
        }
      }
      
      .prev-btn {
        left: 16px;
      }
      
      .next-btn {
        right: 16px;
      }
      
      .close-btn {
        top: 16px;
        right: 16px;
      }
      
      .image-indicator {
        bottom: 16px;
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}
</style>

<style lang="scss">
.image-preview-dialog {
  .el-dialog__header {
    display: none !important;
  }
  
  .el-dialog__body {
    padding: 0 !important;
  }
  
  .el-dialog {
    width: 80% !important;
    max-width: 1200px !important;
    background-color: rgba(0, 0, 0, 0.95) !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}
</style>