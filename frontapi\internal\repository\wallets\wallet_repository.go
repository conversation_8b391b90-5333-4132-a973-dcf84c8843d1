package wallets

import (
	"context"
	"frontapi/internal/models/wallets"
	"frontapi/internal/repository/base"
	"time"

	"gorm.io/gorm"
)

// WalletRepository 钱包数据访问接口
type WalletRepository interface {
	base.ExtendedRepository[wallets.Wallet]
	// 业务特定方法
	FindByUserID(ctx context.Context, userID string) (*wallets.Wallet, error)
	UpdateBalance(ctx context.Context, id string, amount float64) error
	UpdateFrozenBalance(ctx context.Context, id string, amount float64) error
	AuditWallet(ctx context.Context, id string, status string, reason string) error
}

// walletRepository 钱包数据访问实现
type walletRepository struct {
	base.ExtendedRepository[wallets.Wallet]
}

// NewWalletRepository 创建钱包仓库实例
func NewWalletRepository(db *gorm.DB) WalletRepository {
	return &walletRepository{
		ExtendedRepository: base.NewExtendedRepository[wallets.Wallet](db),
	}
}

// FindByUserID 根据用户ID查找钱包
func (r *walletRepository) FindByUserID(ctx context.Context, userID string) (*wallets.Wallet, error) {
	condition := map[string]interface{}{"user_id": userID}
	return r.FindOneByCondition(ctx, condition, "")
}

// UpdateBalance 更新钱包余额
func (r *walletRepository) UpdateBalance(ctx context.Context, id string, amount float64) error {
	return r.GetDBWithContext(ctx).
		Model(&wallets.Wallet{}).
		Where("id = ?", id).
		Update("balance", gorm.Expr("balance + ?", amount)).Error
}

// UpdateFrozenBalance 更新冻结余额
func (r *walletRepository) UpdateFrozenBalance(ctx context.Context, id string, amount float64) error {
	return r.GetDBWithContext(ctx).
		Model(&wallets.Wallet{}).
		Where("id = ?", id).
		Update("frozen_balance", gorm.Expr("frozen_balance + ?", amount)).Error
}

// UpdateStatus 更新钱包状态
func (r *walletRepository) AuditWallet(ctx context.Context, id string, status string, reason string) error {
	return r.GetDBWithContext(ctx).
		Model(&wallets.Wallet{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":               status,
			"status_change_reason": reason,
			"status_change_time":   time.Now(),
		}).Error
}
