export interface UserItem {
    id: string; // 用户ID
    username: string; // 用户名
    nickname: string; // 昵称
    password?: string; // 密码（加密存储）
    salt?: string; // 盐值
    reset_token?: string; // 重置密码token
    avatar: string; // 头像URL
    gender: number; // 0-未知，1-男，2-女
    email: string; // 邮箱
    phone: string; // 手机号
    phone_verified: number; // 手机是否已验证
    email_verified: number; // 邮箱是否已验证
    bio: string; // 个人简介
    nation: string; // 国家
    address: string; // 地址
    lat: number; // 纬度
    lng: number; // 经度
    recommend_code: string; // 我的推荐码
    recommend_id: string; // 推荐人ID
    heat: number; // 热度
    user_type: number; // 用户类型
    follow_count: number; // 关注数量
    like_count: number; // 点赞数量
    total_albums: number; // 发表总专辑数
    total_shorts: number; // 发表总短视频数
    total_posts: number; // 发表总帖子数
    total_views: number; // 总浏览量
    total_comments: number; // 发表总评论数
    total_likes: number; // 给别人点赞总次数
    total_following: number; // 关注别人人数
    reg_time: string; // 注册时间
    last_login_time: string; // 最后登录时间
    last_active_time: string; // 最后活跃时间
    first_login_ip: string; // 首次登录IP
    first_login_device: string; // 首次登录设备
    score: number; // 会员积分
    is_verified: number; // 是否认证：0-未认证，1-已认证
    is_content_creator: number; // 是否为内容创作者:0-否,1-是
    creator_level: number; // 创作者等级
    total_coin_consumed: number; // 总消费平台币
    total_points_earned: number; // 总获得积分
    creator_verified_time: string; // 创作者认证时间
    status: number; // 状态：0-禁用，1-正常
    online: number; // 1.在线，0.离线
    created_at?: string; // 创建时间
    updated_at?: string; // 更新时间
    // 临时字段，不在数据库中存储
    is_liked?: boolean; // 是否点赞
    is_followed?: boolean; // 是否关注
}

// 用户登录日志类型
export interface UserLoginLog {
    id: string; // 记录ID
    user_id: string; // 用户ID
    username?: string; // 用户名（前端展示用）
    login_time: string; // 登录时间
    type: number; // 登录类型，0登录，1退出
    login_type: string; // 登录类型：password, token, oauth2, etc.
    ip_address: string; // 登录IP地址
    device_type: string; // 设备类型
    os_name: string; // 操作系统名称
    os_version: string; // 操作系统版本号
    browser_name: string; // 浏览器名称
    browser_version: string; // 浏览器版本号
    user_agent: string; // 完整的User-Agent字符串
    location: string; // 地理位置
    login_status: string; // 登录状态：成功或失败
    failure_reason: string; // 失败原因，若登录失败时记录
}