# 视频分类状态更新API修复

## 问题描述

调用 `http://localhost:8081/api/proadm/video-categories/update-status` 接口修改分类状态时报错：
```
字段 [status]: status is required to not be empty
```

## 问题分析

### 1. 路由定义问题
路由定义为：
```go
categories.Post("/update-status/:id?", videoCategoryController.UpdateVideoCategoryStatus)
```

这表明 ID 应该作为 URL 路径参数传递，但控制器实现期望从请求体获取 ID。

### 2. 控制器实现问题
原始控制器代码：
```go
func (h *VideoCategoryController) UpdateVideoCategoryStatus(c *fiber.Ctx) error {
    var req videoValidator.UpdateCategoryStatusRequest
    if err := validator.ValidateDataWrapper(c, &req); err != nil {
        return err
    }
    
    err := h.categoryService.UpdateStatus(c.Context(), req.Id, req.Status)
    // ...
}
```

这种实现期望从请求体的 `data` 字段中获取 `id` 和 `status`，但路由定义了 URL 参数 `:id?`。

### 3. 前端调用问题
前端调用：
```javascript
// 前端传递参数
const params = { id, status };
const {response, data} = await updateVideoCategoryStatus(params) as any;

// API函数实现
export function updateVideoCategoryStatus(data: any) {
    return request({
        url: "/video-categories/update-status",
        method: "post",
        data: { data: data }
    })
}
```

前端没有将 ID 作为 URL 参数传递，而是放在请求体中。

## 解决方案

### 1. 修改控制器实现
将控制器改为从 URL 路径获取 ID，从请求体获取 status：

```go
func (h *VideoCategoryController) UpdateVideoCategoryStatus(c *fiber.Ctx) error {
    // 从URL路径获取ID
    id, err := h.GetId(c)
    if err != nil {
        return h.BadRequest(c, "分类ID不能为空", nil)
    }

    // 获取状态参数
    status := -999
    err = h.GetIntegerValueWithDataWrapper(c, "status", &status)
    if err != nil {
        return h.BadRequest(c, "status参数无效", nil)
    }
    
    if status == -999 {
        return h.BadRequest(c, "status is required to not be empty", nil)
    }

    err = h.categoryService.UpdateStatus(c.Context(), id, status)
    if err != nil {
        return h.InternalServerError(c, "更新分类状态失败")
    }
    return h.SuccessWithMessage(c, "更新分类状态成功")
}
```

### 2. 修改前端API调用
更新前端API函数，将 ID 作为 URL 参数传递：

```javascript
export function updateVideoCategoryStatus(data: any) {
    return request({
        url: `/video-categories/update-status/${data.id}`,
        method: "post",
        data: { data: { status: data.status } }
    })
}
```

## 修改的文件

1. **后端控制器**: `frontapi/internal/admin/videos/video_category_controller.go`
   - 修改了 `UpdateVideoCategoryStatus` 方法
   - 从 URL 路径获取 ID
   - 从请求体获取 status 参数
   - 添加了参数验证

2. **前端API接口**: `backend/src/service/api/videos/videos.ts`
   - 修改了 `updateVideoCategoryStatus` 函数
   - 将 ID 作为 URL 参数传递
   - 只在请求体中传递 status 参数

## 测试验证

修复后的调用方式：
- **URL**: `POST /api/proadm/video-categories/update-status/{id}`
- **请求体**: `{ "data": { "status": 1 } }`

其中：
- `{id}`: 分类ID，作为URL路径参数
- `status`: 状态值（0-禁用，1-启用），在请求体data字段中

## 注意事项

1. 此修复保持了与其他模块的一致性，按照RESTful规范使用URL参数传递资源ID
2. 前端调用方式保持原有格式，只是调整了参数传递方式
3. 错误提示更加明确，便于调试和问题定位
4. 修复后需要重新编译和重启后端服务才能生效

## 相关文件路径

- 后端控制器: `frontapi/internal/admin/videos/video_category_controller.go`
- 前端API: `backend/src/service/api/videos/videos.ts`
- 路由定义: `frontapi/internal/routes/admin/video_routes.go`
- 验证器: `frontapi/internal/validation/videos/video_category.go` 