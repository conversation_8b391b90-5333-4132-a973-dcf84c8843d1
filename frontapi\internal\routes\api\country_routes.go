package api

import (
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

func RegisterCountryRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 国家/地区API控制器
	// countryController := system.NewCountryController(services.CountryService)
	{
		// group := app.Group("/api/proadm/countries", middleware.AuthRequired())
		// group.Post("/add", countryController.Create)
		// group.Get("/detail/:id?", countryController.Get)
		// group.Put("/update/:id?", middleware.AuthRequired(), countryController.Update)
		// group.Delete("/delete/:id?", middleware.AuthRequired(), countryController.Delete)
		// group.Get("/list", countryController.List)
	}

}
