/**
 * 服务模块统一导出
 * 提供所有业务服务的统一入口
 */

// 用户服务
export { userService } from './user'
export type { UserService } from './user'

// 视频服务
export { videoService } from './video'
export type { VideoService, VideoListParams, VideoUploadData, VideoSearchFilters } from './video'

// 社交服务
export { socialService } from './social'
export type { SocialService, Conversation, Group } from './social'

// 通知服务
export { notificationService } from './notification'
export type { 
  NotificationService, 
  AppNotification, 
  NotificationSettings, 
  ToastType, 
  AlertType 
} from './notification'

// 媒体服务
export { mediaService } from './media'
export type {
  MediaService,
  UploadOptions,
  ImageUploadOptions,
  AudioUploadOptions,
  UploadResult,
  VideoQuality,
  PlayerOptions,
  VideoPlayer,
  LiveStreamOptions,
  LiveStream,
  VideoAnalysis,
  ContentDetection
} from './media'

// 服务管理器
class ServiceManager {
  private services = new Map<string, any>()
  
  constructor() {
    // 注册所有服务
    this.registerService('user', userService)
    this.registerService('video', videoService)
    this.registerService('social', socialService)
    this.registerService('notification', notificationService)
    this.registerService('media', mediaService)
  }
  
  registerService(name: string, service: any): void {
    this.services.set(name, service)
  }
  
  getService<T>(name: string): T {
    const service = this.services.get(name)
    if (!service) {
      throw new Error(`服务 '${name}' 未找到`)
    }
    return service as T
  }
  
  getAllServices(): Map<string, any> {
    return new Map(this.services)
  }
  
  // 初始化所有服务
  async initialize(): Promise<void> {
    console.log('正在初始化服务...')
    
    try {
      // 这里可以添加服务初始化逻辑
      // 比如检查用户登录状态、初始化WebSocket连接等
      
      console.log('服务初始化完成')
    } catch (error) {
      console.error('服务初始化失败:', error)
      throw error
    }
  }
  
  // 清理所有服务
  destroy(): void {
    console.log('正在清理服务...')
    
    // 清理社交服务的WebSocket连接
    if (socialService && typeof socialService.destroy === 'function') {
      socialService.destroy()
    }
    
    // 清理其他需要清理的资源
    this.services.clear()
    
    console.log('服务清理完成')
  }
}

// 导出服务管理器实例
export const serviceManager = new ServiceManager()

// 导出便捷的服务访问方法
export const services = {
  user: userService,
  video: videoService,
  social: socialService,
  notification: notificationService,
  media: mediaService
} as const

// 导出服务管理器类型
export type { ServiceManager }