import { post, corePost } from '@/shared/composables'

// 创作者相关接口

/**
 * 获取创作者列表
 */
export function getCreatorList(params: {
  data?: {
    keyword?: string
    category?: string
    sortBy?: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return corePost('/creator/getCreatorList', params)
}

/**
 * 获取创作者详情
 */
export function getCreatorDetail(creatorId: string) {
  return corePost('/creator/getCreatorDetail', {
    data: {
      creatorId
    }
  })
}

/**
 * 获取创作者的视频列表
 */
export function getCreatorVideoList(params: {
  data: {
    creatorId: string
    category?: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return corePost('/creator/getCreatorVideoList', params)
}

/**
 * 获取创作者的帖子列表
 */
export function getCreatorPostList(params: {
  data: {
    creatorId: string
    type?: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return corePost('/creator/getCreatorPostList', params)
}

/**
 * 获取创作者的短视频列表
 */
export function getCreatorShortVideoList(params: {
  data: {
    creatorId: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return corePost('/creator/getCreatorShortVideoList', params)
}

/**
 * 关注创作者
 */
export function followCreator(creatorId: string) {
  return corePost('/creator/follow', {
    data: {
      creatorId
    }
  })
}

/**
 * 取消关注创作者
 */
export function unfollowCreator(creatorId: string) {
  return corePost('/creator/unfollow', {
    data: {
      creatorId
    }
  })
}

/**
 * 检查关注状态
 */
export function checkCreatorFollowStatus(creatorId: string) {
  return corePost('/creator/checkFollowStatus', {
    data: {
      creatorId
    }
  })
}