package permission

import (
	"context"
	"encoding/json"
	"errors"

	permissionModels "frontapi/internal/models/permission"
	permRepo "frontapi/internal/repository/permission"
	base "frontapi/internal/service/base/extint"
	validationPerm "frontapi/internal/validation/permission"
)

// AdminMenuService 管理员菜单服务接口
type AdminMenuService interface {
	base.IIntBaseService[permissionModels.AdminMenu] // 继承基础服务接口

	// 特有的业务逻辑方法
	CreateMenu(ctx context.Context, req *validationPerm.CreateAdminMenuRequest) (int, error)
	UpdateMenu(ctx context.Context, id int, req *validationPerm.UpdateAdminMenuRequest) error
	GetMenuTree(ctx context.Context, condition map[string]interface{}) ([]*permissionModels.AdminMenu, error)
	GetMenusByParent(ctx context.Context, parentID int) ([]*permissionModels.AdminMenu, error)
	GetMenuByPath(ctx context.Context, path string) (*permissionModels.AdminMenu, error)
	GetMenuByName(ctx context.Context, name string) (*permissionModels.AdminMenu, error)
	GetMenuByPermission(ctx context.Context, permission string) (*permissionModels.AdminMenu, error)

	// 检查方法
	CheckMenuNameExists(ctx context.Context, name string, excludeID ...int) (bool, error)
	CheckMenuPathExists(ctx context.Context, path string, excludeID ...int) (bool, error)
	CheckMenuPermissionExists(ctx context.Context, permission string, excludeID ...int) (bool, error)

	// 批量操作
	BatchUpdateMenuSort(ctx context.Context, sortData []map[string]interface{}) error
	GetUserMenus(ctx context.Context, userID int) ([]*permissionModels.AdminMenu, error)
	GetRoleMenus(ctx context.Context, roleID int) ([]*permissionModels.AdminMenu, error)
}

// adminMenuService 管理员菜单服务实现
type adminMenuService struct {
	base.IIntBaseService[permissionModels.AdminMenu] // 嵌入基础服务
	menuRepo                                         permRepo.AdminMenuRepository
}

// NewAdminMenuService 创建管理员菜单服务实例
func NewAdminMenuService(menuRepo permRepo.AdminMenuRepository) AdminMenuService {
	baseService := base.NewIntBaseService[permissionModels.AdminMenu](menuRepo, "admin_menu")
	return &adminMenuService{
		IIntBaseService: baseService,
		menuRepo:        menuRepo,
	}
}

// CreateMenu 创建菜单
func (s *adminMenuService) CreateMenu(ctx context.Context, req *validationPerm.CreateAdminMenuRequest) (int, error) {
	// 检查名称是否重复
	if exists, err := s.CheckMenuNameExists(ctx, req.Name); err != nil {
		return 0, err
	} else if exists {
		return 0, errors.New("菜单名称已存在")
	}

	// 检查路径是否重复（如果有路径）
	if req.Path != "" {
		if exists, err := s.CheckMenuPathExists(ctx, req.Path); err != nil {
			return 0, err
		} else if exists {
			return 0, errors.New("菜单路径已存在")
		}
	}

	// 检查权限标识是否重复（如果有权限标识）
	if req.Permission != "" {
		if exists, err := s.CheckMenuPermissionExists(ctx, req.Permission); err != nil {
			return 0, err
		} else if exists {
			return 0, errors.New("权限标识已存在")
		}
	}

	// 构建Meta字段
	metaData := map[string]interface{}{
		"title":    req.Title,
		"icon":     req.Icon,
		"isHidden": req.IsHidden == 1,
		"isCache":  req.IsCache == 1,
		"isFrame":  req.IsFrame == 1,
	}
	metaJSON, _ := json.Marshal(metaData)

	// 创建菜单实例
	menu := &permissionModels.AdminMenu{
		ParentID:   req.ParentID,
		Title:      req.Title,
		Name:       req.Name,
		Path:       req.Path,
		Component:  req.Component,
		Icon:       req.Icon,
		Permission: req.Permission,
		Type:       req.Type,
		Sort:       req.Sort,
		IsHidden:   req.IsHidden,
		IsCache:    req.IsCache,
		IsFrame:    req.IsFrame,
		Remark:     req.Remark,
		Meta:       string(metaJSON),
	}

	// 使用基础服务创建
	return s.IIntBaseService.Create(ctx, menu)
}

// UpdateMenu 更新菜单
func (s *adminMenuService) UpdateMenu(ctx context.Context, id int, req *validationPerm.UpdateAdminMenuRequest) error {
	// 获取现有菜单
	menu, err := s.IIntBaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}
	if menu == nil {
		return errors.New("菜单不存在")
	}

	// 检查名称是否重复（排除当前菜单）
	if req.Name != "" && req.Name != menu.Name {
		if exists, err := s.CheckMenuNameExists(ctx, req.Name, id); err != nil {
			return err
		} else if exists {
			return errors.New("菜单名称已存在")
		}
		menu.Name = req.Name
	}

	// 检查路径是否重复（排除当前菜单）
	if req.Path != "" && req.Path != menu.Path {
		if exists, err := s.CheckMenuPathExists(ctx, req.Path, id); err != nil {
			return err
		} else if exists {
			return errors.New("菜单路径已存在")
		}
		menu.Path = req.Path
	}

	// 检查权限标识是否重复（排除当前菜单）
	if req.Permission != "" && req.Permission != menu.Permission {
		if exists, err := s.CheckMenuPermissionExists(ctx, req.Permission, id); err != nil {
			return err
		} else if exists {
			return errors.New("权限标识已存在")
		}
		menu.Permission = req.Permission
	}

	// 更新其他字段
	if req.Title != "" {
		menu.Title = req.Title
	}
	if req.Component != "" {
		menu.Component = req.Component
	}
	if req.Icon != "" {
		menu.Icon = req.Icon
	}
	if req.Type > 0 {
		menu.Type = req.Type
	}
	if req.Sort >= 0 {
		menu.Sort = req.Sort
	}
	if req.ParentID >= 0 {
		menu.ParentID = req.ParentID
	}

	menu.IsHidden = req.IsHidden
	menu.IsCache = req.IsCache
	menu.IsFrame = req.IsFrame

	if req.Remark != "" {
		menu.Remark = req.Remark
	}

	// 重新构建Meta字段
	metaData := map[string]interface{}{
		"title":    menu.Title,
		"icon":     menu.Icon,
		"isHidden": menu.IsHidden == 1,
		"isCache":  menu.IsCache == 1,
		"isFrame":  menu.IsFrame == 1,
	}
	metaJSON, _ := json.Marshal(metaData)
	menu.Meta = string(metaJSON)

	// 使用基础服务更新
	return s.IIntBaseService.Update(ctx, menu)
}

// GetMenuTree 获取菜单树
func (s *adminMenuService) GetMenuTree(ctx context.Context, condition map[string]interface{}) ([]*permissionModels.AdminMenu, error) {
	// 获取所有菜单（不分页）
	menus, _, err := s.IIntBaseService.List(ctx, condition, "sort ASC, id ASC", 1, 10000, false)
	if err != nil {
		return nil, err
	}

	// 构建菜单树
	return s.buildMenuTree(menus, 0), nil
}

// GetMenusByParent 获取指定父级的菜单
func (s *adminMenuService) GetMenusByParent(ctx context.Context, parentID int) ([]*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"parent_id": parentID,
	}
	return s.IIntBaseService.FindByCondition(ctx, condition, "sort ASC, id ASC")
}

// GetMenuByPath 根据路径获取菜单
func (s *adminMenuService) GetMenuByPath(ctx context.Context, path string) (*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"path": path,
	}
	return s.IIntBaseService.FindOneByCondition(ctx, condition, "")
}

// GetMenuByName 根据名称获取菜单
func (s *adminMenuService) GetMenuByName(ctx context.Context, name string) (*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"name": name,
	}
	return s.IIntBaseService.FindOneByCondition(ctx, condition, "")
}

// GetMenuByPermission 根据权限标识获取菜单
func (s *adminMenuService) GetMenuByPermission(ctx context.Context, permission string) (*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"permission": permission,
	}
	return s.IIntBaseService.FindOneByCondition(ctx, condition, "")
}

// CheckMenuNameExists 检查菜单名称是否存在
func (s *adminMenuService) CheckMenuNameExists(ctx context.Context, name string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"name": name,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	count, err := s.IIntBaseService.Count(ctx, condition)
	return count > 0, err
}

// CheckMenuPathExists 检查菜单路径是否存在
func (s *adminMenuService) CheckMenuPathExists(ctx context.Context, path string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"path": path,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	count, err := s.IIntBaseService.Count(ctx, condition)
	return count > 0, err
}

// CheckMenuPermissionExists 检查权限标识是否存在
func (s *adminMenuService) CheckMenuPermissionExists(ctx context.Context, permission string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"permission": permission,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	count, err := s.IIntBaseService.Count(ctx, condition)
	return count > 0, err
}

// BatchUpdateMenuSort 批量更新菜单排序
func (s *adminMenuService) BatchUpdateMenuSort(ctx context.Context, sortData []map[string]interface{}) error {
	for _, data := range sortData {
		id, ok := data["id"].(int)
		if !ok {
			continue
		}

		sort, ok := data["sort"].(int)
		if !ok {
			continue
		}

		// 获取菜单
		menu, err := s.IIntBaseService.GetByID(ctx, id, false)
		if err != nil {
			return err
		}
		if menu == nil {
			continue
		}

		// 更新排序
		menu.Sort = sort
		if err := s.IIntBaseService.Update(ctx, menu); err != nil {
			return err
		}
	}

	return nil
}

// GetUserMenus 获取用户可访问的菜单
func (s *adminMenuService) GetUserMenus(ctx context.Context, userID int) ([]*permissionModels.AdminMenu, error) {
	// 这里需要根据用户权限获取菜单，暂时返回所有启用的菜单
	condition := map[string]interface{}{
		"status": 1,
	}
	return s.IIntBaseService.FindByCondition(ctx, condition, "sort ASC, id ASC")
}

// GetRoleMenus 获取角色可访问的菜单
func (s *adminMenuService) GetRoleMenus(ctx context.Context, roleID int) ([]*permissionModels.AdminMenu, error) {
	// 这里需要根据角色权限获取菜单，暂时返回所有启用的菜单
	condition := map[string]interface{}{
		"status": 1,
	}
	return s.IIntBaseService.FindByCondition(ctx, condition, "sort ASC, id ASC")
}

// buildMenuTree 构建菜单树
func (s *adminMenuService) buildMenuTree(menus []*permissionModels.AdminMenu, parentID int) []*permissionModels.AdminMenu {
	var tree []*permissionModels.AdminMenu

	for _, menu := range menus {
		if menu.ParentID == parentID {
			children := s.buildMenuTree(menus, menu.ID)
			menu.Children = children
			tree = append(tree, menu)
		}
	}

	return tree
}
