package cache

import "time"

// CacheConfig 缓存配置结构体
type CacheConfig struct {
	// DefaultTTL 默认缓存时间
	DefaultTTL time.Duration `mapstructure:"default_ttl"`
	// Redis配置
	Redis *RedisConfig `mapstructure:"redis"`
	// File配置
	File *FileConfig `mapstructure:"file"`
	// Memory配置
	Memory *MemoryConfig `mapstructure:"memory"`
	// BigCache配置
	BigCache *BigCacheConfig `mapstructure:"bigcache"`
	// Cluster配置
	Cluster *ClusterConfig `mapstructure:"cluster"`
	// Sharding配置
	Sharding *ShardingConfig `mapstructure:"sharding"`
	// 默认适配器
	DefaultAdapter string `mapstructure:"default"`
	// 是否启用集群
	EnableCluster bool `mapstructure:"enable_cluster"`
	// 是否启用分片
	EnableSharding bool `mapstructure:"enable_sharding"`
	// 分片数量
	ShardCount int `mapstructure:"shard_count"`
	// 本地缓存配置
	LocalCache *LocalCacheConfig `mapstructure:"local_cache"`
	// 是否启用本地缓存
	EnableLocalCache bool `mapstructure:"enable_local_cache"`
	// 本地缓存大小
	LocalCacheSize int `mapstructure:"local_cache_size"`
	// 本地缓存过期时间
	LocalCacheTTL time.Duration `mapstructure:"local_cache_ttl"`
}
