/**
 * 国际化插件类型定义
 */

import type { LocaleType } from '@/locales/types';

/**
 * 国际化插件选项
 */
export interface I18nOptions {
    /** 是否启用调试 */
    debug?: boolean;
    /** 默认语言 */
    defaultLocale?: LocaleType;
    /** 回退语言 */
    fallbackLocale?: LocaleType;
    /** 是否预加载所有语言 */
    preloadAllLanguages?: boolean;
    /** 自动检测语言 */
    autoDetect?: boolean;
    /** 是否异步加载语言包 */
    asyncLoading?: boolean;
    /** 缓存策略 */
    cacheStrategy?: 'localStorage' | 'sessionStorage';
    /** 缓存键名 */
    cacheKey?: string;
    /** 是否使用store */
    useStore?: boolean;
    /** 日期时间格式 */
    dateTimeFormats?: Record<string, any>;
    /** 数字格式 */
    numberFormats?: Record<string, any>;
    /** 额外需要加载的语言列表 */
    additionalLocales?: LocaleType[];
}

/**
 * 国际化插件注入键
 */
export const I18N_INJECTION_KEY = Symbol('i18n-options'); 