package content_creator

import "frontapi/pkg/types"

type CreateContentHeatRequest struct {
	ContentType     string         `json:"content_type"`
	ContentID       string         `json:"content_id"`
	PeriodType      string         `json:"period_type"`
	PeriodDate      types.JSONTime `json:"period_date"`
	HeatScore       float64        `json:"heat_score"`
	ViewWeight      float64        `json:"view_weight"`
	LikeWeight      float64        `json:"like_weight"`
	CommentWeight   float64        `json:"comment_weight"`
	ShareWeight     float64        `json:"share_weight"`
	FavoriteWeight  float64        `json:"favorite_weight"`
	WatchTimeWeight float64        `json:"watch_time_weight"`
	Rank            *int           `json:"rank"`
}

type UpdateContentHeatRequest struct {
	HeatScore       float64 `json:"heat_score"`
	ViewWeight      float64 `json:"view_weight"`
	LikeWeight      float64 `json:"like_weight"`
	CommentWeight   float64 `json:"comment_weight"`
	ShareWeight     float64 `json:"share_weight"`
	FavoriteWeight  float64 `json:"favorite_weight"`
	WatchTimeWeight float64 `json:"watch_time_weight"`
	Rank            *int    `json:"rank"`
}
