<template>
    <header class="app-header">
        <div class="header-container">        
            <!-- Logo区域 -->
            <div class="logo-section">
                <router-link to="/" class="logo-link">
                    <div class="logo-icon-wrapper">
                        <svg class="logo-icon" viewBox="0 0 32 32" fill="none">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#667eea" />
                                    <stop offset="100%" stop-color="#764ba2" />
                                </linearGradient>
                            </defs>
                            <circle cx="16" cy="16" r="14" fill="url(#logoGradient)" />
                            <path d="M12 10l8 6-8 6V10z" fill="white" />
                        </svg>
                    </div>
                    <span class="logo-text">
                        {{ $t('common.siteName') }}
                    </span>
                </router-link>
            </div>

            <!-- 中央导航菜单 -->
            <nav class="main-navigation hidden lg:flex">
                <div class="nav-menu">
                    <template v-for="item in menuItems" :key="item.label">
                        <div class="nav-item-wrapper">
                            <router-link
                                v-if="item.route"
                                :to="item.route"
                                class="nav-item"
                                :class="{ 'active': $route.path === item.route }"
                            >
                                <i :class="item.icon" class="nav-icon"></i>
                                <span class="nav-label">{{ item.label }}</span>
                            </router-link>
                            <div v-else class="nav-item dropdown-trigger">
                                <i :class="item.icon" class="nav-icon"></i>
                                <span class="nav-label">{{ item.label }}</span>
                                <i class="pi pi-angle-down dropdown-arrow"></i>
                                <div v-if="item.items" class="dropdown-menu">
                                    <router-link
                                        v-for="subItem in item.items"
                                        :key="subItem.label"
                                        :to="subItem.route"
                                        class="dropdown-item"
                                    >
                                        <i :class="subItem.icon" class="dropdown-icon"></i>
                                        <span>{{ subItem.label }}</span>
                                    </router-link>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </nav>

            <!-- 右侧工具栏 -->
            <div class="toolbar-section">
                <!-- 搜索框 -->
                <div class="search-wrapper hidden md:flex">
                    <div class="search-container">
                        <i class="pi pi-search search-icon"></i>
                        <input
                            v-model="searchQuery"
                            type="text"
                            :placeholder="$t('common.search')"
                            class="search-input"
                            @keyup.enter="performSearch"
                        />
                    </div>
                </div>

                <!-- 工具按钮组 -->
                <div class="tool-buttons">
                    <!-- 移动端搜索按钮 -->
                    <button
                        class="tool-btn search-btn md:hidden"
                        @click="toggleMobileSearch"
                        aria-label="Search"
                    >
                        <i class="pi pi-search"></i>
                    </button>

                    <!-- 通知按钮 -->
                    <button
                        class="tool-btn notification-btn"
                        @click="toggleNotifications"
                        aria-label="Notifications"
                    >
                        <i class="pi pi-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>

                    <!-- 语言选择器 -->
                    <NavLanguageSelector class="tool-selector" />

                    <!-- 主题选择器 -->
                    <NavThemeSelector class="tool-selector" />

                    <!-- 用户菜单 -->
                    <button
                        class="tool-btn user-btn"
                        @click="toggleUserMenu"
                        aria-label="User menu"
                    >
                        <i class="pi pi-user"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端搜索栏 -->
        <div v-if="showMobileSearch" class="mobile-search-bar bg-surface-50 dark:bg-surface-900 border-t border-surface-200 dark:border-surface-700 p-4 lg:hidden">
            <IconField iconPosition="left" class="w-full">
                <InputIcon class="pi pi-search text-surface-400" />
                <InputText 
                    v-model="searchQuery" 
                    :placeholder="t('common.search')"
                    class="w-full pl-10 pr-4 py-3 bg-surface-0 dark:bg-surface-950 border border-surface-200 dark:border-surface-700 rounded-lg"
                    @keyup.enter="performSearch"
                />
            </IconField>
        </div>
        
        <!-- 移动端导航菜单 -->
        <div v-if="showMobileMenu" class="mobile-nav bg-surface-0 dark:bg-surface-950 border-t border-surface-200 dark:border-surface-700 lg:hidden">
            <div class="mobile-nav-content p-4">
                <div v-for="item in menuItems" :key="item.label" class="mobile-nav-item">
                    <router-link 
                        v-if="item.route" 
                        :to="item.route" 
                        class="mobile-nav-link"
                        @click="closeMobileMenu">
                        <i :class="item.icon" class="mr-3"></i>
                        <span>{{ item.label }}</span>
                    </router-link>
                    <div v-else class="mobile-nav-group">
                        <div class="mobile-nav-group-title">
                            <i :class="item.icon" class="mr-3"></i>
                            <span>{{ item.label }}</span>
                        </div>
                        <div class="mobile-nav-submenu ml-6 mt-2">
                            <template v-for="group in item.items" :key="group.label">
                                <div v-for="subItem in group.items" :key="subItem.label" class="mobile-nav-subitem">
                                    <router-link 
                                        :to="subItem.route" 
                                        class="mobile-nav-sublink"
                                        @click="closeMobileMenu">
                                        <i :class="subItem.icon" class="mr-2 text-sm"></i>
                                        <span>{{ subItem.label }}</span>
                                    </router-link>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
</template>

<script setup lang="ts">
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import InputText from 'primevue/inputtext';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

// i18n
const { t } = useI18n();
const router = useRouter();

// 响应式数据
const searchQuery = ref('');
const showMobileMenu = ref(false);
const showMobileSearch = ref(false);

// 移动端菜单控制
const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
    showMobileSearch.value = false;
};

const closeMobileMenu = () => {
    showMobileMenu.value = false;
};

// 移动端搜索控制
const toggleMobileSearch = () => {
    showMobileSearch.value = !showMobileSearch.value;
    showMobileMenu.value = false;
};

// 搜索功能
const performSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        showMobileSearch.value = false;
    }
};

// 通知和用户菜单
const toggleNotifications = () => {
    // TODO: 实现通知功能
    console.log('Toggle notifications');
};

const toggleUserMenu = () => {
    // TODO: 实现用户菜单
    console.log('Toggle user menu');
};

// 主菜单配置
const menuItems = computed(() => [
    {
        label: t('common.home'),
        icon: 'pi pi-home',
        route: '/'
    },
    {
        label: t('common.categories'),
        icon: 'pi pi-list',
        items: [
            {
                label: t('category.movies'),
                items: [
                    {
                        label: t('category.action'),
                        icon: 'pi pi-bolt',
                        route: '/category/action'
                    },
                    {
                        label: t('category.comedy'),
                        icon: 'pi pi-smile',
                        route: '/category/comedy'
                    },
                    {
                        label: t('category.drama'),
                        icon: 'pi pi-heart',
                        route: '/category/drama'
                    },
                    {
                        label: t('category.horror'),
                        icon: 'pi pi-exclamation-triangle',
                        route: '/category/horror'
                    },
                    {
                        label: t('category.animation'),
                        icon: 'pi pi-palette',
                        route: '/category/animation'
                    },
                    {
                        label: t('category.documentary'),
                        icon: 'pi pi-file-video',
                        route: '/category/documentary'
                    },
                    {
                        label: t('category.scifi'),
                        icon: 'pi pi-star',
                        route: '/category/sci-fi'
                    },
                    {
                        label: t('category.romance'),
                        icon: 'pi pi-heart-fill',
                        route: '/category/romance'
                    }
                ]
            }
        ]
    },
    {
        label: t('common.videos'),
        icon: 'pi pi-video',
        route: '/videos'
    },
    {
        label: t('common.shorts'),
        icon: 'pi pi-mobile',
        route: '/shorts'
    },
    {
        label: t('common.community'),
        icon: 'pi pi-users',
        route: '/community'
    },
    {
        label: t('common.pictures'),
        icon: 'pi pi-images',
        route: '/pictures'
    },
    {
        label: t('common.comics'),
        icon: 'pi pi-book',
        route: '/comics'
    },
    {
        label: t('common.ebooks'),
        icon: 'pi pi-file-pdf',
        route: '/ebooks'
    }
]);
</script>

<style scoped>
.app-header {
    position: sticky;
    top: 0;
    z-index: 50;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark .app-header {
    background: rgba(15, 23, 42, 0.85);
    border-bottom-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.header-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1.5rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

/* Logo 区域 */
.logo-section {
    flex-shrink: 0;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.logo-link:hover {
    transform: translateY(-1px);
}

.logo-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.logo-link:hover .logo-icon-wrapper {
    transform: scale(1.05) rotate(5deg);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.logo-icon {
    width: 2rem;
    height: 2rem;
    transition: all 0.3s ease;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.dark .logo-text {
    background: linear-gradient(135deg, #818cf8 0%, #c084fc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 中央导航 */
.main-navigation {
    flex: 1;
    justify-content: center;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-item-wrapper {
    position: relative;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    color: #64748b;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.nav-item:hover {
    color: #1e293b;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.nav-item:hover::before {
    opacity: 0.1;
}

.nav-item.active {
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.nav-item.active::before {
    opacity: 0;
}

.dark .nav-item {
    color: #94a3b8;
}

.dark .nav-item:hover {
    color: #f1f5f9;
}

.nav-icon {
    font-size: 0.875rem;
}

.dropdown-trigger {
    cursor: pointer;
}

.dropdown-arrow {
    font-size: 0.75rem;
    transition: transform 0.3s ease;
}

.dropdown-trigger:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 100;
}

.nav-item-wrapper:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dark .dropdown-menu {
    background: rgba(15, 23, 42, 0.95);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #64748b;
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 0.25rem;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.dark .dropdown-item {
    color: #94a3b8;
}

.dark .dropdown-item:hover {
    background: rgba(129, 140, 248, 0.1);
    color: #818cf8;
}

/* 右侧工具栏 */
.toolbar-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-shrink: 0;
}

.search-wrapper {
    position: relative;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 1rem;
    color: #94a3b8;
    font-size: 0.875rem;
    z-index: 1;
}

.search-input {
    width: 280px;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 24px;
    font-size: 0.875rem;
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
}

.search-input:focus {
    width: 320px;
    background: rgba(255, 255, 255, 0.95);
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.15);
}

.search-input::placeholder {
    color: #94a3b8;
}

.dark .search-input {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(71, 85, 105, 0.6);
    color: #f1f5f9;
}

.dark .search-input:focus {
    background: rgba(30, 41, 59, 0.95);
    border-color: #818cf8;
    box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1), 0 8px 25px rgba(129, 140, 248, 0.15);
}

.dark .search-input::placeholder {
    color: #64748b;
}

.tool-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tool-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.tool-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.dark .tool-btn {
    color: #94a3b8;
}

.dark .tool-btn:hover {
    background: rgba(129, 140, 248, 0.1);
    color: #818cf8;
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    min-width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.dark .mobile-menu-btn {
    color: #94a3b8;
}

.dark .mobile-menu-btn:hover {
    background: rgba(129, 140, 248, 0.1);
    color: #818cf8;
}

/* 移动端样式 */
.mobile-search-bar {
    animation: slideDown 0.2s ease-out;
}

.mobile-nav {
    animation: slideDown 0.2s ease-out;
    max-height: 70vh;
    overflow-y: auto;
}

.mobile-nav-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-nav-item {
    border-bottom: 1px solid #e2e8f0;
}

.mobile-nav-item:last-child {
    border-bottom: none;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.2s ease;
}

.mobile-nav-link:hover {
    color: #667eea;
}

.mobile-nav-link.router-link-active {
    color: #667eea;
}

.dark .mobile-nav-item {
    border-bottom-color: #475569;
}

.dark .mobile-nav-link {
    color: #94a3b8;
}

.dark .mobile-nav-link:hover,
.dark .mobile-nav-link.router-link-active {
    color: #818cf8;
}

.mobile-nav-group-title {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    font-weight: 600;
    color: #1e293b;
}

.dark .mobile-nav-group-title {
    color: #f1f5f9;
}

.mobile-nav-submenu {
    padding-bottom: 0.5rem;
}

.mobile-nav-subitem {
    margin-bottom: 0.25rem;
}

.mobile-nav-sublink {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    text-decoration: none;
    color: #94a3b8;
    transition: color 0.2s ease;
}

.mobile-nav-sublink:hover {
    color: #667eea;
}

.mobile-nav-sublink.router-link-active {
    color: #667eea;
    font-weight: 500;
}

.dark .mobile-nav-sublink {
    color: #64748b;
}

.dark .mobile-nav-sublink:hover,
.dark .mobile-nav-sublink.router-link-active {
    color: #818cf8;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-navigation {
        display: none;
    }
    
    .header-container {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .header-container {
        padding: 0 1rem;
        height: 3.5rem;
    }
    
    .logo-text {
        display: none;
    }
    
    .logo-icon-wrapper {
        width: 2rem;
        height: 2rem;
    }
    
    .logo-icon {
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .search-wrapper {
        display: none !important;
    }
    
    .tool-buttons {
        gap: 0.25rem;
    }
    
    .tool-btn {
        width: 2.25rem;
        height: 2.25rem;
    }
}

@media (max-width: 640px) {
    .header-container {
        padding: 0 0.75rem;
    }
    
    .toolbar-section {
        gap: 0.5rem;
    }
}
</style>