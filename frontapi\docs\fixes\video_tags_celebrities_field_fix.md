# 视频Tags和Celebrities字段解析问题修复

## 问题描述

在调试视频更新功能时，发现 `types.StringArray len: 0, cap: 0, nil` 问题，即使前端发送了tags和celebrities数据，后端Controller中获取到的数据仍然是nil切片。

## 问题分析

通过断点调试和代码分析，发现问题的根本原因是**前后端字段名不匹配**：

### 前端发送的JSON字段名：
- `"tags"` - 来自前端 VideoDialog.vue 第499行：`tags: form.tags`
- `"celebrities"` - 来自前端 VideoDialog.vue 第498行：`celebrities: form.celebrities`

### 后端验证结构体期望的JSON字段名：
- `"tags_json"` - 在 `CreateVideoRequest.Tags` 和 `UpdateVideoRequest.Tags` 中
- `"celebrities"` - 这个是正确的

### 结果：
由于字段名不匹配，JSON解析器无法将前端发送的 `"tags"` 字段映射到后端的 `Tags` 字段，导致该字段保持零值（nil切片）。

## 修复方案

### 1. 修复验证结构体中的JSON标签

**文件**: `frontapi/internal/validation/videos/video.go`

```go
// 修复前
type CreateVideoRequest struct {
    // ...
    Tags []string `json:"tags_json"`
    // ...
}

type UpdateVideoRequest struct {
    // ...
    Tags []string `json:"tags_json"`
    // ...
}

// 修复后
type CreateVideoRequest struct {
    // ...
    Tags []string `json:"tags"`
    // ...
}

type UpdateVideoRequest struct {
    // ...
    Tags []string `json:"tags"`
    // ...
}
```

### 2. 移除Controller中的长度判断

**文件**: `frontapi/internal/admin/videos/video_controller.go`

#### CreateVideo方法修复：
```go
// 修复前
if len(req.Tags) > 0 {
    video.Tags = req.Tags
}
if len(req.Celebrities) > 0 {
    video.Celebrities = req.Celebrities
}

// 修复后
// 设置标签 (包括空数组)
video.Tags = req.Tags

// 设置明星/影人信息 (包括空数组)
video.Celebrities = req.Celebrities
```

#### UpdateVideo方法修复：
```go
// 修复前
if len(req.Tags) > 0 {
    video.Tags = req.Tags
}
if len(req.Celebrities) > 0 {
    video.Celebrities = req.Celebrities
}

// 修复后
// 设置标签 (包括空数组)
video.Tags = req.Tags

// 设置明星/影人信息 (包括空数组)
video.Celebrities = req.Celebrities
```

## 修复原理

### 1. 字段名匹配
通过修改JSON标签，确保前端发送的字段名和后端期望的字段名一致，让JSON解析器能够正确映射数据。

### 2. 处理空数组
移除长度判断，确保即使是空数组也会被正确设置到模型中。这样：
- 如果前端发送 `"tags": []`，后端会接收到空的切片而不是nil
- 如果前端发送 `"tags": ["tag1", "tag2"]`，后端会接收到包含数据的切片

### 3. 数据库保存
之前修复的 `StringArray.Value()` 方法确保空数组也会被序列化为 `"[]"` 并保存到数据库中。

## 验证方法

### 1. 调试验证
在Controller中设置断点，检查：
- `req.Tags` 不再是 `nil`，而是有效的切片（可能为空）
- `req.Celebrities` 不再是 `nil`，而是有效的切片（可能为空）

### 2. 功能验证
- 创建视频时添加tags和celebrities，检查是否正确保存
- 更新视频时修改tags和celebrities，检查是否正确更新
- 清空tags和celebrities，检查是否正确保存为空数组

## 涉及文件

1. `frontapi/internal/validation/videos/video.go` - 修复JSON字段标签
2. `frontapi/internal/admin/videos/video_controller.go` - 移除长度判断
3. `frontapi/pkg/types/string_array.go` - 之前已修复空数组序列化

## 注意事项

1. **向后兼容性**: 这个修复保持了与前端的兼容性，不需要修改前端代码
2. **数据一致性**: 修复后，空数组会被正确保存为JSON字符串`"[]"`，而不是NULL
3. **其他模块**: 如果其他模块也有类似的字段名不匹配问题，需要类似的修复

## 最佳实践

1. **字段命名一致性**: 前后端字段名应该保持一致，避免不必要的映射问题
2. **JSON标签规范**: JSON标签应该与前端API文档保持一致
3. **调试方法**: 遇到类似问题时，首先检查字段名是否匹配，然后检查数据类型和处理逻辑 