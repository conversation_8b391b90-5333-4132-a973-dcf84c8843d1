package posts

import (
	"context"
	"frontapi/internal/models/posts"
	repo "frontapi/internal/repository/posts"
	"frontapi/internal/service/base"
)

// CommentService 帖子评论服务接口
type PostCommentService interface {
	base.IExtendedService[posts.PostComment]
	// 业务特定方法
	IsLikeComment(ctx context.Context, commentID, userID string) (bool, error)
}

// commentService 帖子评论服务实现
type postCommentService struct {
	*base.ExtendedService[posts.PostComment]
	commentRepo     repo.CommentRepository
	postRepo        repo.PostRepository
	commentLikeRepo repo.PostCommentLikeRepository
}

// NewPostCommentService 创建帖子评论服务实例
func NewPostCommentService(commentRepo repo.CommentRepository, postRepo repo.PostRepository, commentLikeRepo repo.PostCommentLikeRepository) PostCommentService {
	return &postCommentService{
		ExtendedService: base.NewExtendedService[posts.PostComment](commentRepo, "post_comment"),
		commentRepo:     commentRepo,
		postRepo:        postRepo,
		commentLikeRepo: commentLikeRepo,
	}
}

func (s *postCommentService) IsLikeComment(ctx context.Context, commentID, userID string) (bool, error) {
	// 检查用户是否已点赞
	likeCount, err := s.commentLikeRepo.Count(ctx, map[string]interface{}{
		"comment_id": commentID,
		"user_id":    userID,
	})
	if err != nil {
		return false, err
	}
	return likeCount > 0, nil
}
