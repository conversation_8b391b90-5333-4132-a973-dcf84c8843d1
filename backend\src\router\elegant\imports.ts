/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  about: () => import("@/views/about/index.vue"),
  alova_request: () => import("@/views/alova/request/index.vue"),
  alova_scenes: () => import("@/views/alova/scenes/index.vue"),
  alova_user: () => import("@/views/alova/user/index.vue"),
  books_category: () => import("@/views/books/category/index.vue"),
  books_chapter: () => import("@/views/books/chapter/index.vue"),
  books_list: () => import("@/views/books/list/index.vue"),
  comics_category: () => import("@/views/comics/category/index.vue"),
  comics_chapter: () => import("@/views/comics/chapter/index.vue"),
  comics_list: () => import("@/views/comics/list/index.vue"),
  comics_page: () => import("@/views/comics/page/index.vue"),
  "function_hide-child_one": () => import("@/views/function/hide-child/one/index.vue"),
  "function_hide-child_three": () => import("@/views/function/hide-child/three/index.vue"),
  "function_hide-child_two": () => import("@/views/function/hide-child/two/index.vue"),
  "function_multi-tab": () => import("@/views/function/multi-tab/index.vue"),
  function_request: () => import("@/views/function/request/index.vue"),
  "function_super-page": () => import("@/views/function/super-page/index.vue"),
  function_tab: () => import("@/views/function/tab/index.vue"),
  "function_toggle-auth": () => import("@/views/function/toggle-auth/index.vue"),
  home: () => import("@/views/home/<USER>"),
  integral_list: () => import("@/views/integral/list/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  "multi-menu_first_child": () => import("@/views/multi-menu/first_child/index.vue"),
  "multi-menu_second_child_home": () => import("@/views/multi-menu/second_child_home/index.vue"),
  pictures_album: () => import("@/views/pictures/album/index.vue"),
  pictures_category: () => import("@/views/pictures/category/index.vue"),
  pictures_list: () => import("@/views/pictures/list/index.vue"),
  plugin_barcode: () => import("@/views/plugin/barcode/index.vue"),
  plugin_charts_antv: () => import("@/views/plugin/charts/antv/index.vue"),
  plugin_charts_echarts: () => import("@/views/plugin/charts/echarts/index.vue"),
  plugin_charts_vchart: () => import("@/views/plugin/charts/vchart/index.vue"),
  plugin_copy: () => import("@/views/plugin/copy/index.vue"),
  plugin_editor_markdown: () => import("@/views/plugin/editor/markdown/index.vue"),
  plugin_editor_quill: () => import("@/views/plugin/editor/quill/index.vue"),
  plugin_excel: () => import("@/views/plugin/excel/index.vue"),
  plugin_gantt_dhtmlx: () => import("@/views/plugin/gantt/dhtmlx/index.vue"),
  plugin_gantt_vtable: () => import("@/views/plugin/gantt/vtable/index.vue"),
  plugin_icon: () => import("@/views/plugin/icon/index.vue"),
  plugin_map: () => import("@/views/plugin/map/index.vue"),
  plugin_pdf: () => import("@/views/plugin/pdf/index.vue"),
  plugin_pinyin: () => import("@/views/plugin/pinyin/index.vue"),
  plugin_print: () => import("@/views/plugin/print/index.vue"),
  plugin_swiper: () => import("@/views/plugin/swiper/index.vue"),
  plugin_tables_vtable: () => import("@/views/plugin/tables/vtable/index.vue"),
  plugin_typeit: () => import("@/views/plugin/typeit/index.vue"),
  plugin_video: () => import("@/views/plugin/video/index.vue"),
  posts_comment: () => import("@/views/posts/comment/index.vue"),
  posts_list: () => import("@/views/posts/list/index.vue"),
  shortvideos_category: () => import("@/views/shortvideos/category/index.vue"),
  shortvideos_comment: () => import("@/views/shortvideos/comment/index.vue"),
  shortvideos_list: () => import("@/views/shortvideos/list/index.vue"),
  system_menus: () => import("@/views/system/menus/index.vue"),
  system_mock: () => import("@/views/system/mock/index.vue"),
  system_tags: () => import("@/views/system/tags/index.vue"),
  "user-center": () => import("@/views/user-center/index.vue"),
  users_list: () => import("@/views/users/list/index.vue"),
  "users_login-logs": () => import("@/views/users/login-logs/index.vue"),
  videos_albums: () => import("@/views/videos/albums/index.vue"),
  videos_category: () => import("@/views/videos/category/index.vue"),
  videos_channel: () => import("@/views/videos/channel/index.vue"),
  videos_comment: () => import("@/views/videos/comment/index.vue"),
  videos_list: () => import("@/views/videos/list/index.vue"),
};
