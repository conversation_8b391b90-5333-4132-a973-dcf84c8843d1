/**
 * 通知服务
 * 处理各种类型的通知，包括浏览器通知、应用内通知、推送通知等
 */

import { ref, computed } from 'vue'
import { eventBus } from '@/core/utils/eventBus'
import { storageUtils } from '@/core/utils'

export interface NotificationService {
  // 通知权限
  requestPermission(): Promise<NotificationPermission>
  hasPermission(): boolean
  
  // 浏览器通知
  showBrowserNotification(title: string, options?: NotificationOptions): Promise<Notification | null>
  
  // 应用内通知
  showToast(message: string, type?: ToastType, duration?: number): void
  showAlert(title: string, message: string, type?: AlertType): Promise<boolean>
  showConfirm(title: string, message: string): Promise<boolean>
  
  // 推送通知
  subscribeToPush(): Promise<PushSubscription | null>
  unsubscribeFromPush(): Promise<void>
  
  // 通知管理
  addNotification(notification: AppNotification): void
  removeNotification(id: string): void
  clearAllNotifications(): void
  markAsRead(id: string): void
  markAllAsRead(): void
  
  // 通知设置
  updateSettings(settings: NotificationSettings): void
  getSettings(): NotificationSettings
}

type ToastType = 'success' | 'error' | 'warning' | 'info'
type AlertType = 'success' | 'error' | 'warning' | 'info'

interface AppNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: number
  isRead: boolean
  action?: {
    label: string
    handler: () => void
  }
  avatar?: string
  link?: string
}

interface NotificationSettings {
  browserNotifications: boolean
  pushNotifications: boolean
  soundEnabled: boolean
  vibrationEnabled: boolean
  emailNotifications: boolean
  
  // 具体通知类型设置
  videoLikes: boolean
  videoComments: boolean
  newFollowers: boolean
  mentions: boolean
  directMessages: boolean
  liveStreams: boolean
  systemUpdates: boolean
}

class NotificationServiceImpl implements NotificationService {
  private notifications = ref<AppNotification[]>([])
  private settings = ref<NotificationSettings>(this.getDefaultSettings())
  private toasts = ref<Array<{ id: string; message: string; type: ToastType; duration: number }>>([])
  
  // 计算属性
  unreadCount = computed(() => this.notifications.value.filter(n => !n.isRead).length)
  
  constructor() {
    this.loadSettings()
    this.initServiceWorker()
  }
  
  private getDefaultSettings(): NotificationSettings {
    return {
      browserNotifications: true,
      pushNotifications: false,
      soundEnabled: true,
      vibrationEnabled: true,
      emailNotifications: true,
      videoLikes: true,
      videoComments: true,
      newFollowers: true,
      mentions: true,
      directMessages: true,
      liveStreams: true,
      systemUpdates: true
    }
  }
  
  private loadSettings() {
    const saved = storageUtils.get('notification_settings')
    if (saved) {
      this.settings.value = { ...this.getDefaultSettings(), ...saved }
    }
  }
  
  private saveSettings() {
    storageUtils.set('notification_settings', this.settings.value)
  }
  
  private async initServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js')
        console.log('Service Worker 注册成功:', registration)
      } catch (error) {
        console.error('Service Worker 注册失败:', error)
      }
    }
  }
  
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      throw new Error('此浏览器不支持通知功能')
    }
    
    if (Notification.permission === 'granted') {
      return 'granted'
    }
    
    if (Notification.permission === 'denied') {
      return 'denied'
    }
    
    const permission = await Notification.requestPermission()
    
    if (permission === 'granted') {
      this.settings.value.browserNotifications = true
      this.saveSettings()
    }
    
    return permission
  }
  
  hasPermission(): boolean {
    return 'Notification' in window && Notification.permission === 'granted'
  }
  
  async showBrowserNotification(title: string, options: NotificationOptions = {}): Promise<Notification | null> {
    if (!this.settings.value.browserNotifications || !this.hasPermission()) {
      return null
    }
    
    try {
      const notification = new Notification(title, {
        icon: '/favicon.ico',
        badge: '/badge.png',
        tag: 'app-notification',
        renotify: true,
        requireInteraction: false,
        ...options
      })
      
      // 播放声音
      if (this.settings.value.soundEnabled) {
        this.playNotificationSound()
      }
      
      // 振动
      if (this.settings.value.vibrationEnabled && 'vibrate' in navigator) {
        navigator.vibrate([200, 100, 200])
      }
      
      // 自动关闭
      setTimeout(() => {
        notification.close()
      }, 5000)
      
      return notification
    } catch (error) {
      console.error('显示浏览器通知失败:', error)
      return null
    }
  }
  
  showToast(message: string, type: ToastType = 'info', duration = 3000): void {
    const id = Date.now().toString()
    const toast = { id, message, type, duration }
    
    this.toasts.value.push(toast)
    
    // 发送事件给 UI 组件
    eventBus.emit('showToast', toast)
    
    // 自动移除
    setTimeout(() => {
      this.removeToast(id)
    }, duration)
  }
  
  private removeToast(id: string) {
    const index = this.toasts.value.findIndex(t => t.id === id)
    if (index !== -1) {
      this.toasts.value.splice(index, 1)
      eventBus.emit('removeToast', id)
    }
  }
  
  async showAlert(title: string, message: string, type: AlertType = 'info'): Promise<boolean> {
    return new Promise((resolve) => {
      const alertData = {
        id: Date.now().toString(),
        title,
        message,
        type,
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      }
      
      eventBus.emit('showAlert', alertData)
    })
  }
  
  async showConfirm(title: string, message: string): Promise<boolean> {
    return new Promise((resolve) => {
      const confirmData = {
        id: Date.now().toString(),
        title,
        message,
        onConfirm: () => resolve(true),
        onCancel: () => resolve(false)
      }
      
      eventBus.emit('showConfirm', confirmData)
    })
  }
  
  async subscribeToPush(): Promise<PushSubscription | null> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      throw new Error('此浏览器不支持推送通知')
    }
    
    try {
      const registration = await navigator.serviceWorker.ready
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(process.env.VITE_VAPID_PUBLIC_KEY || '')
      })
      
      // 将订阅信息发送到服务器
      await this.sendSubscriptionToServer(subscription)
      
      this.settings.value.pushNotifications = true
      this.saveSettings()
      
      return subscription
    } catch (error) {
      console.error('订阅推送通知失败:', error)
      throw error
    }
  }
  
  async unsubscribeFromPush(): Promise<void> {
    if (!('serviceWorker' in navigator)) {
      return
    }
    
    try {
      const registration = await navigator.serviceWorker.ready
      const subscription = await registration.pushManager.getSubscription()
      
      if (subscription) {
        await subscription.unsubscribe()
        await this.removeSubscriptionFromServer(subscription)
      }
      
      this.settings.value.pushNotifications = false
      this.saveSettings()
    } catch (error) {
      console.error('取消推送通知订阅失败:', error)
      throw error
    }
  }
  
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    // 这里应该调用 API 将订阅信息发送到服务器
    console.log('发送订阅信息到服务器:', subscription)
  }
  
  private async removeSubscriptionFromServer(subscription: PushSubscription): Promise<void> {
    // 这里应该调用 API 从服务器移除订阅信息
    console.log('从服务器移除订阅信息:', subscription)
  }
  
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')
    
    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)
    
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    
    return outputArray
  }
  
  addNotification(notification: AppNotification): void {
    this.notifications.value.unshift(notification)
    
    // 限制通知数量
    if (this.notifications.value.length > 100) {
      this.notifications.value = this.notifications.value.slice(0, 100)
    }
    
    // 发送事件
    eventBus.emit('newNotification', notification)
    
    // 显示浏览器通知
    if (this.settings.value.browserNotifications) {
      this.showBrowserNotification(notification.title, {
        body: notification.message,
        icon: notification.avatar,
        data: { link: notification.link }
      })
    }
  }
  
  removeNotification(id: string): void {
    const index = this.notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      this.notifications.value.splice(index, 1)
      eventBus.emit('notificationRemoved', id)
    }
  }
  
  clearAllNotifications(): void {
    this.notifications.value = []
    eventBus.emit('notificationsCleared')
  }
  
  markAsRead(id: string): void {
    const notification = this.notifications.value.find(n => n.id === id)
    if (notification && !notification.isRead) {
      notification.isRead = true
      eventBus.emit('notificationRead', id)
    }
  }
  
  markAllAsRead(): void {
    this.notifications.value.forEach(n => n.isRead = true)
    eventBus.emit('allNotificationsRead')
  }
  
  updateSettings(settings: NotificationSettings): void {
    this.settings.value = { ...this.settings.value, ...settings }
    this.saveSettings()
    eventBus.emit('notificationSettingsUpdated', this.settings.value)
  }
  
  getSettings(): NotificationSettings {
    return { ...this.settings.value }
  }
  
  private playNotificationSound(): void {
    try {
      const audio = new Audio('/sounds/notification.mp3')
      audio.volume = 0.5
      audio.play().catch(error => {
        console.warn('播放通知声音失败:', error)
      })
    } catch (error) {
      console.warn('创建音频对象失败:', error)
    }
  }
  
  // 预定义的通知创建方法
  createVideoLikeNotification(videoTitle: string, userName: string, userAvatar: string): AppNotification {
    return {
      id: Date.now().toString(),
      title: '新的点赞',
      message: `${userName} 点赞了你的视频「${videoTitle}」`,
      type: 'info',
      timestamp: Date.now(),
      isRead: false,
      avatar: userAvatar
    }
  }
  
  createCommentNotification(videoTitle: string, userName: string, userAvatar: string, comment: string): AppNotification {
    return {
      id: Date.now().toString(),
      title: '新的评论',
      message: `${userName} 评论了你的视频「${videoTitle}」：${comment}`,
      type: 'info',
      timestamp: Date.now(),
      isRead: false,
      avatar: userAvatar
    }
  }
  
  createFollowNotification(userName: string, userAvatar: string): AppNotification {
    return {
      id: Date.now().toString(),
      title: '新的关注者',
      message: `${userName} 开始关注你了`,
      type: 'success',
      timestamp: Date.now(),
      isRead: false,
      avatar: userAvatar
    }
  }
  
  createMessageNotification(userName: string, userAvatar: string, message: string): AppNotification {
    return {
      id: Date.now().toString(),
      title: '新消息',
      message: `${userName}：${message}`,
      type: 'info',
      timestamp: Date.now(),
      isRead: false,
      avatar: userAvatar
    }
  }
  
  createSystemNotification(title: string, message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): AppNotification {
    return {
      id: Date.now().toString(),
      title,
      message,
      type,
      timestamp: Date.now(),
      isRead: false
    }
  }
}

// 导出单例实例
export const notificationService = new NotificationServiceImpl()

// 导出类型
export type { AppNotification, NotificationSettings, ToastType, AlertType }