package mongodb

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extcollect/types"
)

// QueryOperations MongoDB查询操作处理器
type QueryOperations struct {
	collection *mongo.Collection
}

// NewQueryOperations 创建查询操作处理器
func NewQueryOperations(collection *mongo.Collection) *QueryOperations {
	return &QueryOperations{
		collection: collection,
	}
}

// BatchGetCollectStatus 批量获取收藏状态
func (q *QueryOperations) BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	result := make(map[string]bool)

	// 构建查询条件
	var filters []bson.M
	for itemID, itemType := range items {
		filter := bson.M{
			"user_id":   userID,
			"item_id":   itemID,
			"item_type": itemType,
			"status":    "collected",
		}
		filters = append(filters, filter)
	}

	if len(filters) == 0 {
		return result, nil
	}

	cursor, err := q.collection.Find(ctx, bson.M{"$or": filters})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 初始化所有项目为false
	for itemID := range items {
		result[itemID] = false
	}

	// 处理查询结果
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		result[record.ItemID] = true
	}

	return result, nil
}

// BatchGetCollectCounts 批量获取收藏数量
func (q *QueryOperations) BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	result := make(map[string]int64)

	for itemID, itemType := range items {
		filter := bson.M{
			"item_id":   itemID,
			"item_type": itemType,
			"status":    "collected",
		}

		count, err := q.collection.CountDocuments(ctx, filter)
		if err != nil {
			result[itemID] = 0
		} else {
			result[itemID] = count
		}
	}

	return result, nil
}

// GetUserCollects 获取用户收藏列表
func (q *QueryOperations) GetUserCollects(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	filter := bson.M{
		"user_id":   userID,
		"item_type": itemType,
		"status":    "collected",
	}

	opts := options.Find().
		SetSkip(int64(offset)).
		SetLimit(int64(limit)).
		SetSort(bson.M{"timestamp": -1})

	cursor, err := q.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.CollectRecord
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetItemCollectors 获取项目收藏者列表
func (q *QueryOperations) GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
		"status":    "collected",
	}

	opts := options.Find().
		SetSkip(int64(offset)).
		SetLimit(int64(limit)).
		SetSort(bson.M{"timestamp": -1})

	cursor, err := q.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.CollectRecord
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}

// GetCollectHistory 获取收藏历史
func (q *QueryOperations) GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error) {
	filter := bson.M{
		"user_id":   userID,
		"item_type": itemType,
	}

	// 添加时间范围过滤
	if timeRange != nil {
		filter["timestamp"] = bson.M{
			"$gte": timeRange.Start,
			"$lte": timeRange.End,
		}
	}

	opts := options.Find().
		SetSort(bson.M{"timestamp": -1}).
		SetLimit(100) // 限制返回数量

	cursor, err := q.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.CollectRecord
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err != nil {
			continue
		}
		records = append(records, &record)
	}

	return records, nil
}
