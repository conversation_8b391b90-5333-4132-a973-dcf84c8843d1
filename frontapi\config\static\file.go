package static

// 静态文件配置
type StaticConfig struct {
	URL string `mapstructure:"url"`
}

type MediaConfig struct { // 媒体存储配置
	StoragePath string `mapstructure:"storage_path"`
	StorageURL  string `mapstructure:"storage_url"`
}

type UploadConfig struct {
	SmallImage struct {
		Path string `mapstructure:"path"`
		URL  string `mapstructure:"url"`
	} `mapstructure:"small_image"`
	Picture struct {
		Path string `mapstructure:"path"`
		URL  string `mapstructure:"url"`
	} `mapstructure:"picture"`
	Video struct {
		Path string `mapstructure:"path"`
		URL  string `mapstructure:"url"`
	} `mapstructure:"video"`
}
