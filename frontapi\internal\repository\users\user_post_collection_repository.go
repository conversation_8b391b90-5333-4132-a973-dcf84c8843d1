package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserPostCollectionRepository 用户帖子收藏数据访问接口
type UserPostCollectionRepository interface {
	base.ExtendedRepository[users.UserPostCollection]
}

// userPostCollectionRepository 用户帖子收藏数据访问实现
type userPostCollectionRepository struct {
	base.ExtendedRepository[users.UserPostCollection]
}

// NewUserPostCollectionRepository 创建用户帖子收藏仓库实例
func NewUserPostCollectionRepository(db *gorm.DB) UserPostCollectionRepository {
	return &userPostCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserPostCollection](db),
	}
}
