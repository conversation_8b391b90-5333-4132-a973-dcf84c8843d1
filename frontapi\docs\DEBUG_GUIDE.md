# FrontAPI 调试指南

## 概述

本指南介绍如何在 Cursor/VS Code 中调试 FrontAPI Golang 项目。

## 前提条件

1. 安装 Go 扩展：`golang.go`
2. 安装 Delve 调试器：`go install github.com/go-delve/delve/cmd/dlv@latest`
3. 确保 Go 环境正确配置

## 调试配置

我们已经为项目配置了以下调试选项：

### 1. Debug FrontAPI - All Services
启动所有服务（API、Admin、Media）进行调试
- **端口**: API(8080), Admin(8081), Media(8082)
- **用途**: 完整系统调试

### 2. Debug FrontAPI - Admin Only  
仅启动Admin管理后台服务进行调试
- **端口**: 8081
- **用途**: 专门调试管理后台功能
- **推荐**: 调试视频管理、用户管理等后台功能

### 3. Debug FrontAPI - API Only
仅启动API服务进行调试
- **端口**: 8080
- **用途**: 专门调试前端API功能

### 4. Debug FrontAPI - Custom Config
自定义配置调试（当前配置为仅启动Admin）
- **可配置**: 通过修改args参数来控制启动的服务

### 5. Launch Current File
调试当前打开的Go文件

### 6. Launch Package
调试当前包

## 使用方法

### 方法一：使用调试面板

1. 打开Cursor
2. 按 `Ctrl+Shift+D` (Windows/Linux) 或 `Cmd+Shift+D` (Mac) 打开调试面板
3. 在调试配置下拉菜单中选择需要的配置
4. 点击绿色播放按钮开始调试

### 方法二：使用快捷键

1. 在代码中设置断点（点击行号左侧）
2. 按 `F5` 开始调试
3. 选择调试配置

### 方法三：使用命令面板

1. 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
2. 输入 "Debug: Start Debugging"
3. 选择调试配置

## 设置断点

1. **行断点**: 点击代码行号左侧的空白区域
2. **条件断点**: 右键点击断点，选择"编辑断点"，设置条件
3. **日志断点**: 右键点击断点，选择"编辑断点"，设置日志消息

## 调试控制

- **F5**: 继续执行
- **F10**: 单步跳过（Step Over）
- **F11**: 单步进入（Step Into）
- **Shift+F11**: 单步跳出（Step Out）
- **Ctrl+Shift+F5**: 重启调试
- **Shift+F5**: 停止调试

## 常用调试场景

### 调试视频控制器

1. 在 `frontapi/internal/admin/videos/video_controller.go` 中设置断点
2. 选择 "Debug FrontAPI - Admin Only" 配置
3. 启动调试
4. 使用前端或API工具调用相关接口

### 调试服务层

1. 在 `frontapi/internal/service/videos/video_service.go` 中设置断点
2. 启动对应的调试配置
3. 通过接口调用触发断点

### 调试数据层

1. 在 `frontapi/internal/repository/videos/video_repository.go` 中设置断点
2. 启动调试配置
3. 执行数据库操作触发断点

## 环境变量

调试时会自动设置以下环境变量：
- `GO_ENV=development`: 开发环境标识
- `SERVER_PORT=8080`: API服务端口
- `ADMIN_SERVER_PORT=8081`: Admin服务端口
- `MEDIA_SERVER_PORT=8082`: Media服务端口

## 任务运行器

我们还配置了任务运行器，可以通过以下方式使用：

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择需要执行的任务：
   - **Build FrontAPI**: 构建主程序
   - **Build FrontAPI Admin**: 构建Admin程序
   - **Build FrontAPI API**: 构建API程序
   - **Run FrontAPI Admin**: 运行Admin服务（非调试模式）
   - **Run FrontAPI API**: 运行API服务（非调试模式）
   - **Run FrontAPI All Services**: 运行所有服务（非调试模式）
   - **Go: Test Package**: 运行测试
   - **Go: Mod Tidy**: 整理依赖

## 常见问题

### 1. 调试器无法启动

**解决方案**:
- 确保已安装 Delve: `go install github.com/go-delve/delve/cmd/dlv@latest`
- 检查 Go 环境变量配置
- 重启 Cursor

### 2. 断点无法命中

**解决方案**:
- 确保代码已保存
- 检查断点位置是否正确
- 确认调试的是正确的服务

### 3. 端口被占用

**解决方案**:
- 检查端口占用: `netstat -an | findstr :8081`
- 终止占用进程或修改配置中的端口

### 4. 无法连接数据库

**解决方案**:
- 检查数据库连接配置
- 确保数据库服务正在运行
- 检查配置文件中的数据库参数

## 最佳实践

1. **按需调试**: 根据需要选择对应的调试配置，避免启动不必要的服务
2. **日志配合**: 结合日志输出进行调试，便于理解程序执行流程
3. **断点管理**: 及时清理不需要的断点，避免影响调试效率
4. **变量监视**: 使用变量监视窗口观察关键变量的变化
5. **调用堆栈**: 利用调用堆栈了解函数调用关系

## 相关文件

- `.vscode/launch.json`: 调试配置文件
- `.vscode/tasks.json`: 任务配置文件  
- `.vscode/settings.json`: 项目设置文件
- `frontapi/cmd/main.go`: 主程序入口
- `frontapi/cmd/admin/main.go`: Admin服务入口
- `frontapi/cmd/api/main.go`: API服务入口