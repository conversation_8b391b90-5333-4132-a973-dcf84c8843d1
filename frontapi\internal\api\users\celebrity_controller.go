package users

import (
	"errors"
	"frontapi/internal/api"
	userModel "frontapi/internal/models/users"
	pictureSrv "frontapi/internal/service/pictures"
	postSrv "frontapi/internal/service/posts"
	shortVideoSrv "frontapi/internal/service/shortvideos"
	userSrv "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
	postTypings "frontapi/internal/typings/posts"
	shortVideoTypings "frontapi/internal/typings/shortvideos"
	userTypings "frontapi/internal/typings/users"
	videoTypings "frontapi/internal/typings/videos"

	pictureTypings "frontapi/internal/typings/picture"
	"slices"
	"time"

	"github.com/gofiber/fiber/v2"
)

type CelebrityController struct {
	api.BaseController
	UserService              userSrv.UserService
	UserFollowService        userSrv.UserFollowsService
	VideoService             videoSrv.VideoService
	ShortVideoService        shortVideoSrv.ShortVideoService
	PostService              postSrv.PostService
	VideoAlbumService        videoSrv.VideoAlbumService
	VideoCommentService      videoSrv.VideoCommentService
	PostCommentService       postSrv.PostCommentService
	ShortVideoCommentService shortVideoSrv.ShortVideoCommentService
	PictureService           pictureSrv.PictureService
}

func NewCelebrityController(
	userService userSrv.UserService,
	userFollowService userSrv.UserFollowsService,
	videoService videoSrv.VideoService,
	shortVideoService shortVideoSrv.ShortVideoService,
	postService postSrv.PostService,
	videoAlbumService videoSrv.VideoAlbumService,
	videoCommentService videoSrv.VideoCommentService,
	postCommentService postSrv.PostCommentService,
	shortVideoCommentService shortVideoSrv.ShortVideoCommentService,
	pictureService pictureSrv.PictureService,
) *CelebrityController {
	return &CelebrityController{
		UserService: userService, UserFollowService: userFollowService,
		VideoService: videoService, ShortVideoService: shortVideoService, PostService: postService,
		PictureService: pictureService,
	}
}

// 获取所有明星
func (c *CelebrityController) GetAllCelebrity(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	userID := c.GetUserID(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	nationality := reqInfo.Get("nation").GetString()
	region := reqInfo.Get("region").GetString()
	sortBy := reqInfo.Get("sortBy").GetString()
	sortByList := []string{"created_at", "view_count", "heat", "totalVideos", "followCount", "favorite_count"}
	if !slices.Contains(sortByList, sortBy) {
		sortBy = "created_at"
	}
	orderby := "created_at"
	switch sortBy {

	case "view_count":
		orderby = "view_count"
	case "heat":
		orderby = "heat"
	case "totalVideos":
		orderby = "total_videos"
	case "followCount":
		orderby = "follow_count"
	case "favorite_count":
		orderby = "like_count"
	default:
		orderby = "created_at"
	}
	condition := map[string]interface{}{
		"keyword":     keyword,
		"nationality": nationality,
		"region":      region,
	}

	celebrityList, total, err := c.UserService.GetAllCelebrity(ctx.Context(), condition, orderby, page, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星列表失败: "+err.Error())
	}
	if userID != "" {
		//是否给明星点赞和关注
		for _, celebrity := range celebrityList {
			celebrity.IsFollowed, err = c.UserFollowService.IsFollowing(ctx.Context(), userID, celebrity.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星列表失败: "+err.Error())
			}
		}
	}

	// 转换为前端响应格式
	response := userTypings.ConvertCelebrityListResponse(celebrityList, total, page, pageSize)
	return c.Success(ctx, response)
}

// 获取明星列表
func (c *CelebrityController) GetCelebrity(ctx *fiber.Ctx) (*userModel.User, error) {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	id := reqInfo.Get("id").GetString()
	code := reqInfo.Get("code").GetString()

	var celebrity *userModel.User
	var err error

	if id != "" {
		celebrity, err = c.UserService.GetByID(ctx.Context(), id, true)
	} else if code != "" {
		celebrity, err = c.UserService.FindOne(ctx.Context(), "code = ?", code)
	} else {
		return nil, errors.New("缺少id或code参数")
	}

	if err != nil {
		return nil, err
	}
	if celebrity == nil {
		return nil, errors.New("明星不存在")
	}
	if celebrity.UserType != 11 { // 明星类型为11
		return nil, errors.New("不是明星")
	}
	return celebrity, nil
}

// 获取明星详情
func (c *CelebrityController) GetCelebrityDetail(ctx *fiber.Ctx) error {
	// 获取请求参数
	celebrity, err := c.GetCelebrity(ctx)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星详情失败: "+err.Error())
	}

	// 转换为前端响应格式
	response := userTypings.ConvertCelebrityInfo(celebrity)
	return c.Success(ctx, response)
}

// 获取明星作品列表
func (c *CelebrityController) GetCelebrityVideoList(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	celebrity, err := c.GetCelebrity(ctx)
	userID := c.GetUserID(ctx)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星详情失败: "+err.Error())
	}
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	sortBy := reqInfo.Get("sortBy").GetString()
	condition := map[string]interface{}{
		"user_id":  celebrity.ID,
		"username": celebrity.Username,
		"keyword":  keyword,
	}
	videoList, total, err := c.VideoService.List(ctx.Context(), condition, sortBy, page, pageSize, true)
	if userID != "" {
		for _, video := range videoList {
			video.IsLiked, err = c.VideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星作品列表失败: "+err.Error())
			}
			video.IsFavorite, err = c.VideoService.CheckUserCollected(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星作品列表失败: "+err.Error())
			}
		}
	}
	if err != nil {
		return c.InternalServerError(ctx, "获取明星作品列表失败: "+err.Error())
	}
	response := videoTypings.ConvertVideoListResponse(videoList, total, page, pageSize)
	return c.Success(ctx, response)
}

// 获取明星短视频列表
func (c *CelebrityController) GetCelebrityShortVideoList(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	celebrity, err := c.GetCelebrity(ctx)
	userID := c.GetUserID(ctx)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星详情失败: "+err.Error())
	}
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	sortBy := reqInfo.Get("sortBy").GetString()
	condition := map[string]interface{}{
		"creator_id": celebrity.ID,
		"username":   celebrity.Username,
		"keyword":    keyword,
	}
	videoList, total, err := c.ShortVideoService.List(ctx.Context(), condition, sortBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星短视频列表失败: "+err.Error())
	}
	if userID != "" {
		for _, video := range videoList {
			video.IsLiked, err = c.ShortVideoService.CheckUserLiked(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星短视频列表失败: "+err.Error())
			}
			video.IsCollected, err = c.ShortVideoService.CheckUserCollected(ctx.Context(), userID, video.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星短视频列表失败: "+err.Error())
			}
		}
	}
	response := shortVideoTypings.ConvertShortVideoListResponse(videoList, total, page, pageSize)
	return c.Success(ctx, response)
}

// 明星帖子列表
func (c *CelebrityController) GetCelebrityPostList(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	celebrity, err := c.GetCelebrity(ctx)
	userID := c.GetUserID(ctx)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星详情失败: "+err.Error())
	}
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	condition := map[string]interface{}{
		"creator_id": celebrity.ID,
		"username":   celebrity.Username,
		"keyword":    keyword,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	postList, total, err := c.PostService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星帖子列表失败: "+err.Error())
	}
	if userID != "" {
		for _, post := range postList {
			user, err := c.UserFollowService.GetUserFollow(ctx.Context(), userID, post.AuthorID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星帖子列表失败: "+err.Error())
			}
			post.Author = user
			post.IsLiked, err = c.PostService.CheckUserLiked(ctx.Context(), userID, post.ID)
			if err != nil {
				return c.InternalServerError(ctx, "获取明星帖子列表失败: "+err.Error())
			}

		}
	}
	response := postTypings.ConvertPostListResponse(postList, total, page, pageSize)
	return c.Success(ctx, response)
}

// 明星帖子评论列表
func (c *CelebrityController) GetCelebrityComments(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	celebrityID := reqInfo.Get("celebrity_id").GetString()
	commentType := reqInfo.Get("type").GetString()
	sort := reqInfo.Get("sort").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	if celebrityID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}

	condition := map[string]interface{}{
		"user_id": celebrityID,
		"status":  1, // 只获取正常状态的评论
	}
	if commentType == "" {
		commentType = "all"
	}
	commentTypes := []string{"video", "post", "shortvideo", "all"}
	if !slices.Contains(commentTypes, commentType) {
		return c.BadRequest(ctx, "评论类型不正确", nil)
	}
	sortBy := "created_at DESC"
	switch sort {
	case "popular":
		sortBy = "heat DESC"
	case "liked":
		sortBy = "like_count DESC"
	default:
		sortBy = "created_at DESC"
	}
	//获取全部评论
	if commentType == "all" {
		comments, total, err := c.UserService.ListUserAllComments(ctx.Context(), condition, sortBy, pageNo, pageSize)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := userTypings.ConvertCreatorCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	} else if commentType == "post" {
		comments, total, err := c.PostCommentService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := postTypings.ConvertPostCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	} else if commentType == "shortvideo" {
		comments, total, err := c.ShortVideoCommentService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := shortVideoTypings.ConvertShortVideoCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	} else if commentType == "video" {
		comments, total, err := c.VideoCommentService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, true)
		if err != nil {
			return c.InternalServerError(ctx, "获取创作者评论列表失败: "+err.Error())
		}
		commentListResponse := videoTypings.ConvertVideoCommentListResponse(comments, total, pageNo, pageSize)
		return c.Success(ctx, commentListResponse)
	}
	return c.Success(ctx, nil)
}

// 明星图片列表
func (c *CelebrityController) GetCelebrityImageList(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	celebrityID := reqInfo.Get("celebrity_id").GetString()
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	condition := map[string]interface{}{
		"creator_id": celebrityID,
		"keyword":    keyword,
	}
	imageList, total, err := c.PictureService.List(ctx.Context(), condition, "created_at DESC", page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取明星图片列表失败: "+err.Error())
	}
	response := pictureTypings.ConvertPictureListResponse(imageList, total, page, pageSize)
	return c.Success(ctx, response)
}

// 统计粉丝增长数据(日,周，月，季度)
func (c *CelebrityController) GetFollowersStats(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)
	celebrityID := reqInfo.Get("celebrity_id").GetString()
	timeRange := reqInfo.Get("time_range").GetString()
	startTime := reqInfo.Get("start_time").GetString()
	endTime := reqInfo.Get("end_time").GetString()
	if celebrityID == "" {
		return c.BadRequest(ctx, "创作者ID不能为空", nil)
	}
	if timeRange == "" {
		timeRange = "day"
	}
	timeRanges := []string{"day", "week", "month", "quarter"}
	if !slices.Contains(timeRanges, timeRange) {
		return c.BadRequest(ctx, "时间范围不正确", nil)
	}
	if startTime == "" {
		startTime = time.Now().AddDate(0, -1, 0).Format("2006-01-02")
	}
	if endTime == "" {
		endTime = time.Now().Format("2006-01-02")
	}
	growth, err := c.UserFollowService.GetFollowersGrowthStats(ctx.Context(), celebrityID, timeRange, startTime, endTime)
	if err != nil {
		return c.InternalServerError(ctx, "获取粉丝增长速度失败: "+err.Error())
	}
	return c.Success(ctx, growth)

}
