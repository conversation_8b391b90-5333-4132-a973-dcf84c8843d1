import { ref, onMounted } from 'vue';
import { getPictureAlbumList } from '@/service/api/pictures/pictures';
import type {PictureAlbum, PictureAlbumParams} from '@/types/pictures';

export function useAlbums() {
  const albumOptions = ref<PictureAlbum[]>([]);
  const loading = ref(false);

  const fetchAlbums = async () => {
    loading.value = true;
    try {
      const {response,data,err} = await getPictureAlbumList({} as PictureAlbumParams) as any;
      if(!err&&response.data.code==2000){
        albumOptions.value = data.list;
      }

    } catch (error) {
      console.error('获取专辑列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchAlbums();
  });

  return {
    albumOptions,
    loading,
    fetchAlbums
  };
}
