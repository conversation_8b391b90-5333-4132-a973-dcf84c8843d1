import type { PageResponse } from '@/shared/composables'
import { corePost, corePostPageList } from '@/shared/composables'

/**
 * 获取首页标签列表
 */
export const getHomeTagList = (pageNo: number = 1, pageSize: number = 20) => {
    return corePostPageList('/home/<USER>', {
        data: {},
        page: { pageNo: pageNo, pageSize: pageSize }
    })
}

/**
 * 获取热门视频列表
 */
export const getHotVideos = (pageNo: number = 1, pageSize: number = 12) => {
    return corePostPageList('/home/<USER>', {
        data: {},
        page: { pageNo: pageNo, pageSize: pageSize }
    })
}

/**
 * 获取推荐视频列表
 */
export const getRecommends = (pageNo: number = 1, pageSize: number = 10) => {
    return corePostPageList('/home/<USER>', {
        data: {},
        page: { pageNo: pageNo, pageSize: pageSize }
    })
}

/**
 * 获取推荐明星列表
 */
export const getHomeRecommendStarList = (pageNo: number = 1, pageSize: number = 30) => {
    return corePostPageList('/home/<USER>', {
        data: {},
        page: { pageNo: pageNo, pageSize: pageSize }
    })
}

/**
 * 获取分类列表
 */
export const getCategories = (pageNo: number = 1, pageSize: number = 12) => {
    return corePostPageList('/categories', {
        data: {},
        page: { pageNo: pageNo, pageSize: pageSize }
    })
}

/**
 * 获取视频列表（带分页）
 * @param pageNo 页码
 * @param pageSize 每页大小
 * @param filters 筛选条件
 */
export const getVideoList = (
    pageNo: number = 1,
    pageSize: number = 10,
    filters?: {
        keyword?: string;
        categoryId?: string;
        status?: number;
    }
): Promise<PageResponse<any>> => {
    return corePostPageList('/videos/list', {
        data: filters || {},
        page: { pageNo: pageNo, pageSize: pageSize }
    }) as any
}

/**
 * 搜索视频（带分页和查询条件）
 * @param keyword 搜索关键词
 * @param pageNo 页码
 * @param pageSize 每页大小
 */
export const searchVideos = (
    keyword: string,
    pageNo: number = 1,
    pageSize: number = 10
): Promise<PageResponse<any>> => {
    return corePostPageList('/videos/search', {
        data: {
            keyword
        },
        page: { pageNo: pageNo, pageSize: pageSize }
    }) as any
}

/**
 * 获取视频详情（带参数）
 * @param id 视频ID
 */
export const getVideoDetail = (id: string) => {
    return corePost('/videos/detail', { id })
}

// 示例：如果需要调用v2版本的API
export const getHomeTagListV2 = () => {
    return corePost('/home/<USER>', {})
}
