<template>
  <div class="video-info">
    <!-- 视频信息区域 -->
    <div class="video-details">
      <div class="creator-info">
        <img 
          :src="video.creator_avatar || '/default-avatar.png'" 
          :alt="video.creator_name"
          class="creator-avatar"
        >
        <div class="creator-text">
          <h3 class="creator-name">{{ video.creator_name }}</h3>
        </div>
      </div>
      
      <div class="video-title">
        <p>{{ video.title }}</p>
        <p v-if="video.description" class="video-description">
          {{ video.description }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ShortVideo } from '@/types/shorts'

// Props
interface Props {
  video: ShortVideo
}

defineProps<Props>()
</script>

<style scoped>
.video-info {
  pointer-events: auto;
}

.video-details {
  color: white;
  padding: 0 18px;
  width: calc(100% - 36px);
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.creator-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.creator-text {
  display: flex;
  align-items: center;
  gap: 12px;
}

.creator-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.video-title {
  max-width: 100%;
  text-align: left;
}

.video-title p {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.4;
  color: #fefefefe;
}

.video-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .creator-avatar {
    width: 36px;
    height: 36px;
  }

  .creator-name {
    font-size: 15px;
  }

  .video-title p {
    font-size: 13px;
  }

  .video-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .creator-info {
    gap: 10px;
    margin-bottom: 10px;
  }

  .creator-avatar {
    width: 32px;
    height: 32px;
  }

  .creator-name {
    font-size: 14px;
  }

  .video-title p {
    font-size: 12px;
  }

  .video-description {
    font-size: 12px;
  }
}
</style> 