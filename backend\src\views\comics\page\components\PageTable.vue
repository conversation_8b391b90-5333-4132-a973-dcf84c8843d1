<template>
  <div class="page-table-container">
    <div v-if="isSortMode" class="sort-mode-tip">
      <el-alert
        title="页面排序模式"
        type="info"
        description="您可以通过拖动页码列中的拖动手柄图标来调整页面顺序。完成后请点击'保存排序'按钮。"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 排序模式下的表格 -->
    <DraggableSortTable
      ref="sortTableRef"
      v-if="isSortMode"
      :table-data="sortablePageList"
      :draggable="true"
      @row-sorted="onRowSorted"
      :loading="loading"
      :show-pagination="false"
    >
      <el-table-column type="index" width="55" label="#" />
      <el-table-column prop="image_url" label="图片" align="center">
        <template #default="{ row }">
          <el-image
            :src="row.image_url"
            style="height: 120px; object-fit: cover;"
            :preview-src-list="[row.image_url]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="width" label="宽度" width="80" align="center" />
      <el-table-column prop="height" label="高度" width="80" align="center" />
      <DraggableSortColumn prop="page_number" label="页码" width="100" align="center">
        <template #default="{ row }">
          <div class="page-drag-handle">
            <el-icon class="drag-icon"><Rank /></el-icon>
            <span>{{ row.page_number }}</span>
          </div>
        </template>
      </DraggableSortColumn>
    </DraggableSortTable>

    <!-- 常规模式下的表格 -->
    <el-table
      v-else
      v-loading="loading"
      :data="pageList"
      border
      :highlight-current-row="false"
      style="width: 100%"
      row-key="id"
    >
      <el-table-column type="index" width="55" label="#"></el-table-column>
      <el-table-column prop="image_url" label="图片" align="center">
        <template #default="{ row }">
          <el-image
            :src="row.image_url"
            style="height: 120px; object-fit: cover;"
            :preview-src-list="[row.image_url]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="width" label="宽度" width="80" align="center" />
      <el-table-column prop="height" label="高度" width="80" align="center" />
      <el-table-column prop="page_number" label="页码" width="80" align="center" />
      <el-table-column prop="created_at" label="上传时间" width="170" align="center">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="$emit('edit', row)">编辑</el-button>
          <el-button type="danger" link @click="$emit('delete', row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="!isSortMode && showPagination" class="pagination-container">
      <el-pagination
        :current-page="modelValue.page"
        :page-size="modelValue.pageSize"
        :total="total"
        background
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onSizeChange"
        @current-change="onCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, computed, watch, nextTick } from 'vue';
import { Rank } from '@element-plus/icons-vue';
import type { ComicsPage } from '@/types/comics';
import { ElMessage } from 'element-plus';
import { batchUpdatePagesOrder } from '@/service/api/comics/comics';
import { DraggableSortTable, DraggableSortColumn } from '@/components/DraggableSortTable';

interface Props {
  modelValue: {
    page: number;
    pageSize: number;
  };
  pageList: ComicsPage[];
  sortablePageList: ComicsPage[];
  loading: boolean;
  total: number;
  chapterId:string;
  comicId: string;
  showPagination: boolean;
  isSortMode: boolean;
}

const props = defineProps<Props>();

const emit = defineEmits([
  'update:modelValue',
  'update:sortablePageList',
  'edit',
  'delete',
  'refresh',
  'order-change'
]);

// 表格引用
const sortTableRef = ref<any>(null);
const isOrderChanged = ref(false);

// 计算属性 - 用于传递分页信息给父组件
const pagination = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 监听排序模式变化
watch(() => props.isSortMode, async (newVal) => {
  if (newVal) {
    // 在DOM更新后刷新排序表格
    await nextTick();
    if (sortTableRef.value) {
      console.log('刷新排序表格');
      sortTableRef.value.refreshSortable();
    }
  }
});

// 拖拽排序处理
const onRowSorted = (evt: any) => {
  const { data } = evt;

  // 更新序号
  const newList = data.map((item: any, index: number) => ({
    ...item,
    page_number: index + 1
  }));

  // 触发父组件更新
  emit('update:sortablePageList', newList);
  emit('order-change', true);
  isOrderChanged.value = true;

  console.log('Row sorted:', newList);
};

// 保存页面排序
const saveOrder = async () => {
  if (!props.chapterId || !props.comicId) {
    ElMessage.error('章节ID或漫画ID缺失，无法保存顺序');
    return;
  }

  try {
    // 格式化数据用于API请求
    const updatedPages = props.sortablePageList.map((page) => ({
      id: page.id,
      page_number: page.page_number
    }));

    // 发送更新请求到服务器
    await batchUpdatePagesOrder(props.chapterId, props.comicId, updatedPages);
    ElMessage.success('页面顺序更新成功');

    // 重置更改跟踪
    isOrderChanged.value = false;
    emit('order-change', false);

    // 刷新数据
    emit('refresh');
  } catch (error) {
    console.error('更新页面顺序失败', error);
    ElMessage.error('更新页面顺序失败，请重试');
  }
};

// 分页处理
const onSizeChange = (size: number) => {
  emit('update:modelValue', { ...props.modelValue, pageSize: size });
};

const onCurrentChange = (page: number) => {
  emit('update:modelValue', { ...props.modelValue, page });
};

// 日期格式化
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 8);
};

// 暴露方法给父组件
defineExpose({
  saveOrder,
  isOrderChanged
});
</script>

<style scoped lang="scss">
.page-table-container {
  width: 100%;
}

.sort-mode-tip {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.page-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  background-color: #f0f9ff;
  border-radius: 4px;
  padding: 4px 8px;

  .drag-icon {
    color: #409eff;
    margin-right: 5px;
    font-size: 16px;
  }

  &:hover {
    background-color: #ecf5ff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    cursor: grabbing;
  }
}

:deep(.el-table .hover-row) {
  background-color: var(--el-color-primary-light-9);
}
</style>
