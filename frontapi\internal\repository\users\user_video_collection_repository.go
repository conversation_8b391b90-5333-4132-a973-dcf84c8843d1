package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserVideoCollectionRepository 用户视频收藏数据访问接口
type UserVideoCollectionRepository interface {
	base.ExtendedRepository[users.UserVideoCollection]
}

// userVideoCollectionRepository 用户视频收藏数据访问实现
type userVideoCollectionRepository struct {
	base.ExtendedRepository[users.UserVideoCollection]
}

// NewUserVideoCollectionRepository 创建用户视频收藏仓库实例
func NewUserVideoCollectionRepository(db *gorm.DB) UserVideoCollectionRepository {
	return &userVideoCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserVideoCollection](db),
	}
}
