<template>
  <el-dialog
    :model-value="visible"
    :title="form.id ? '编辑分类' : '新增分类'"
    width="500px"
    @close="$emit('close')"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称"></el-input>
      </el-form-item>

      <el-form-item label="封面" prop="cover">
        <UrlOrFileInput
          v-model="form.cover"
          fileType="image"
          subDir="category"
          placeholder="请输入封面图URL或选择图片"
        />
      </el-form-item>

      <el-form-item label="分类描述" prop="description">
        <el-input v-model="form.description" placeholder="请输入分类描述" type="textarea" :rows="2"></el-input>
      </el-form-item>

      <el-form-item label="排序" prop="sort_order">
        <el-input-number v-model="form.sort_order" :min="0" :max="999"></el-input-number>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive,watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { uploadImage } from '@/service/api/upload';
import UrlOrFileInput from "@/components/filemanager/UrlOrFileInput.vue";

const props = defineProps<{
  visible: boolean;
  category?: any;
}>();

const emit = defineEmits(['submit', 'close']);
const formRef = ref<FormInstance>();
const submitLoading = ref(false);

// 表单数据
const form = ref({
  id: '',
  name: '',
  cover: '',
  description: '',
  sort_order: 0,
  status: 1
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
  sort_order: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
});

// 监听category变化，更新表单数据
watch(() => props.category, (val) => {
  if (val) {
    form.value = { ...val };
  } else {
    resetForm();
  }
}, { immediate: true });

// 重置表单
function resetForm() {
  form.value = {
    id: '',
    name: '',
    cover: '',
    description: '',
    sort_order: 0,
    status: 1
  };
}

// 上传前验证
function beforeCoverUpload(file: File) {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('封面必须是图片格式!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('封面大小不能超过 5MB!');
    return false;
  }
  return true;
}

// 上传处理
async function uploadCoverHandler({ file }: { file: File }) {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'comics-category');
    const res = await uploadImage(formData);
    if (res.code === 2000) {
      form.value.cover = res.data.url;
      ElMessage.success('上传成功');
    } else {
      ElMessage.error(res.message || '上传失败');
    }
  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('上传失败');
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate();
  emit('submit', { ...form.value });
};
</script>

<style scoped>
.cover-uploader {
  text-align: center;
}
.cover {
  width: 200px;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
}
</style>
