<template>
    <el-dialog title="评论详情" :model-value="visible" @update:model-value="handleClose" width="600px" destroy-on-close>
        <div v-if="comment" class="comment-detail">
            <el-descriptions :column="1" border>
                <el-descriptions-item label="评论ID">{{ comment.id }}</el-descriptions-item>
                <el-descriptions-item label="视频ID">{{ comment.video_id }}</el-descriptions-item>
                <el-descriptions-item label="用户ID">{{ comment.user_id }}</el-descriptions-item>
                <el-descriptions-item label="用户名">{{ comment.user_name || '无名称' }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                    <el-tag :type="comment.status === 0 ? 'success' : 'danger'">
                        {{ comment.status === 0 ? '正常' : '隐藏' }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="评论内容">
                    <div class="comment-text">{{ comment.content }}</div>
                </el-descriptions-item>
                <el-descriptions-item label="点赞数">{{ comment.likes || 0 }}</el-descriptions-item>
                <el-descriptions-item label="回复数">{{ comment.reply_count || 0 }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">
                    {{ formatDateTime(comment.created_at) }}
                </el-descriptions-item>
                <el-descriptions-item label="更新时间">
                    {{ formatDateTime(comment.updated_at) }}
                </el-descriptions-item>
            </el-descriptions>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ShortVideoComment } from '@/types/shortvideos';
import dayjs from 'dayjs';
import { PropType } from 'vue';

// Props定义
interface Props {
    visible: boolean;
    comment: ShortVideoComment | null;
}

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    comment: {
        type: Object as PropType<ShortVideoComment | null>,
        default: null
    }
});

// Emits定义
interface Emits {
    'update:visible': [value: boolean];
}

const emit = defineEmits<Emits>();

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
};

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};
</script>

<style scoped lang="scss">
.comment-detail {
    padding: 10px;

    .comment-text {
        white-space: pre-wrap;
        word-break: break-all;
        padding: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
        min-height: 60px;
    }
}

.el-descriptions {
    margin-bottom: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>