package content_creator

// CreatorIncomeWithdrawRequest 创作者收益提现请求验证模型
type CreatorIncomeWithdrawRequest struct {
	Amount      float64 `json:"amount" validate:"required|gt:0"`
	Account     string  `json:"account" validate:"required"`
	AccountType string  `json:"accountType" validate:"required|in:alipay,wechat,bank"`
	RealName    string  `json:"realName" validate:"requiredIf:AccountType,bank"`
	BankName    string  `json:"bankName" validate:"requiredIf:AccountType,bank"`
}

// CreatorIncomeListRequest 创作者收益列表请求验证模型
type CreatorIncomeListRequest struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"pageSize" validate:"min=1,max=100"`
	StartDate string `json:"startDate" validate:"omitempty,datetime=2006-01-02"`
	EndDate   string `json:"endDate" validate:"omitempty,datetime=2006-01-02,gtfield=StartDate"`
	Type      string `json:"type" validate:"omitempty,oneof=all video music picture article comic ad"`
}