<template>
  <div 
    ref="playerContainer" 
    class="fullscreen-video-player"
    :class="{ 
      'is-playing': isPlaying, 
      'is-loading': isLoading,
      'has-error': hasError,
      'is-muted': isMuted
    }"
  >
    <!-- 视频容器 -->
    <div 
      ref="videoWrapper" 
      class="video-wrapper"
      @click="handleVideoClick"
      @mouseenter="showControls = true"
      @mouseleave="hideControlsDelayed"
    >
      <!-- 原生视频元素 -->
      <video
        ref="videoElement"
        class="video-element"
        :poster="poster"
        :muted="isMuted"
        :loop="loop"
        playsinline
        preload="metadata"
        @loadedmetadata="handleLoadedMetadata"
        @timeupdate="handleTimeUpdate"
        @play="handlePlay"
        @pause="handlePause"
        @ended="handleEnded"
        @error="handleError"
        @waiting="isLoading = true"
        @canplay="isLoading = false"
      >
        <source :src="src" type="video/mp4">
        您的浏览器不支持视频播放
      </video>

      <!-- 播放按钮覆盖层 -->
      <div 
        v-if="!isPlaying && !isLoading" 
        class="play-overlay"
        @click.stop="togglePlay"
      >
        <div class="play-button">
          <el-icon class="play-icon"><VideoPlay /></el-icon>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner">
          <el-icon class="loading-icon"><Loading /></el-icon>
        </div>
        <span class="loading-text">加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="error-overlay">
        <div class="error-content">
          <el-icon class="error-icon"><Warning /></el-icon>
          <span class="error-text">视频加载失败</span>
          <el-button size="small" type="primary" @click="retryLoad">重试</el-button>
        </div>
      </div>

      <!-- 控制栏 -->
      <div 
        v-show="showControls || !isPlaying" 
        class="video-controls"
        @click.stop
      >
        <!-- 进度条 -->
        <div class="progress-container">
          <div 
            ref="progressBar"
            class="progress-bar"
            @click="handleProgressClick"
          >
            <div 
              class="progress-filled"
              :style="{ width: progressPercentage + '%' }"
            ></div>
            <div 
              class="progress-thumb"
              :style="{ left: progressPercentage + '%' }"
            ></div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="controls-row">
          <div class="left-controls">
            <!-- 播放/暂停按钮 -->
            <button class="control-btn play-pause-btn" @click="togglePlay">
              <el-icon>
                <VideoPlay v-if="!isPlaying" />
                <VideoPause v-else />
              </el-icon>
            </button>

            <!-- 音量控制 -->
            <div class="volume-control" @mouseenter="showVolumeSlider = true" @mouseleave="showVolumeSlider = false">
              <button class="control-btn volume-btn" @click="toggleMute">
                <el-icon>
                  <Mute v-if="isMuted || volume === 0" />
                  <Mic v-else-if="volume < 0.7" />
                  <Microphone v-else />
                </el-icon>
              </button>
              <div class="volume-slider" v-show="showVolumeSlider">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  v-model="volume"
                  @input="handleVolumeChange"
                  class="volume-range"
                />
              </div>
            </div>

            <!-- 时间显示 -->
            <div class="time-display">
              <span class="current-time">{{ formatTime(currentTime) }}</span>
              <span class="time-separator">/</span>
              <span class="total-time">{{ formatTime(totalDuration) }}</span>
            </div>
          </div>

          <div class="right-controls">
            <!-- 画质选择 -->
            <div v-if="quality" class="quality-indicator">
              <span class="quality-text">{{ quality }}</span>
            </div>

            <!-- 全屏按钮 -->
            <button class="control-btn fullscreen-btn" @click="toggleFullscreen">
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <Aim v-else />
              </el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { 
  VideoPlay, 
  VideoPause, 
  Loading, 
  Warning, 
  Mute, 
  FullScreen,
  Aim,
  Microphone,
  Mic
} from '@element-plus/icons-vue'
import { useSimpleVideoPlayer } from '../composables/useSimpleVideoPlayer'

// 定义属性
interface Props {
  src: string
  poster?: string
  duration?: number
  quality?: string
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  poster: '',
  duration: 0,
  quality: '',
  autoplay: false,
  muted: false,
  loop: false
})

// 定义事件
const emit = defineEmits<{
  'play': []
  'pause': []
  'ended': []
  'error': [error: Error]
  'timeupdate': [currentTime: number]
  'loadedmetadata': [duration: number]
  'click': []
}>()

// 模板引用
const playerContainer = ref<HTMLElement>()
const videoWrapper = ref<HTMLElement>()
const videoElement = ref<HTMLVideoElement>()
const progressBar = ref<HTMLElement>()

// 播放器状态
const isLoading = ref(false)
const hasError = ref(false)
const showControls = ref(false)
const showVolumeSlider = ref(false)
const controlsTimer = ref<number>()

// 使用简化的视频播放器Hook
const {
  isPlaying,
  isMuted,
  volume,
  currentTime,
  totalDuration,
  isFullscreen,
  play,
  pause,
  togglePlay,
  toggleMute,
  setVolume,
  seek,
  toggleFullscreen
} = useSimpleVideoPlayer(videoElement)

// 计算进度百分比
const progressPercentage = computed(() => {
  if (totalDuration.value === 0) return 0
  return (currentTime.value / totalDuration.value) * 100
})

// 监听音量变化
watch(volume, (newVolume) => {
  if (videoElement.value) {
    videoElement.value.volume = newVolume
  }
})

// 事件处理函数
const handleVideoClick = () => {
  togglePlay()
  emit('click')
}

const handlePlay = () => {
  emit('play')
}

const handlePause = () => {
  emit('pause')
}

const handleEnded = () => {
  emit('ended')
}

const handleError = (event: Event) => {
  hasError.value = true
  isLoading.value = false
  const error = new Error('视频加载失败')
  emit('error', error)
}

const handleLoadedMetadata = () => {
  if (videoElement.value) {
    totalDuration.value = videoElement.value.duration
    emit('loadedmetadata', totalDuration.value)
  }
}

const handleTimeUpdate = () => {
  if (videoElement.value) {
    currentTime.value = videoElement.value.currentTime
    emit('timeupdate', currentTime.value)
  }
}

const handleProgressClick = (event: MouseEvent) => {
  if (!progressBar.value || totalDuration.value === 0) return
  
  const rect = progressBar.value.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percentage = clickX / rect.width
  const newTime = percentage * totalDuration.value
  
  seek(newTime)
}

const handleVolumeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const newVolume = parseFloat(target.value)
  setVolume(newVolume)
}

const hideControlsDelayed = () => {
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
  controlsTimer.value = window.setTimeout(() => {
    showControls.value = false
    showVolumeSlider.value = false
  }, 3000)
}

const retryLoad = () => {
  hasError.value = false
  isLoading.value = true
  if (videoElement.value) {
    videoElement.value.load()
  }
}

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 组件挂载时的初始化
onMounted(() => {
  if (videoElement.value) {
    // 设置初始音量
    videoElement.value.volume = volume.value
    videoElement.value.muted = props.muted
    
    // 如果需要自动播放
    if (props.autoplay) {
      nextTick(() => {
        play()
      })
    }
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
  }
})
</script>

<style scoped lang="scss">
.fullscreen-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  overflow: hidden;

  .video-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;

    .video-element {
      max-width: 100%;
      max-height: 100%;
      width: auto;
      height: auto;
      object-fit: contain;
      display: block;
      background: #000;
    }

    .play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(4px);
      transition: all 0.3s ease;

      .play-button {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);


        .play-icon {
          font-size: 40px;
          margin-left: 4px;
          color: #764ba2;
        }
      }
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(4px);

      .loading-spinner {
        margin-bottom: 16px;

        .loading-icon {
          font-size: 48px;
          color: #fff;
          animation: spin 1s linear infinite;
        }
      }

      .loading-text {
        color: #fff;
        font-size: 18px;
      }
    }

    .error-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(4px);

      .error-content {
        text-align: center;
        color: #fff;

        .error-icon {
          font-size: 48px;
          color: #f56565;
          margin-bottom: 16px;
        }

        .error-text {
          display: block;
          margin-bottom: 20px;
          font-size: 18px;
        }
      }
    }

    .video-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
      backdrop-filter: blur(10px);
      padding: 24px;
      transition: all 0.3s ease;

      .progress-container {
        margin-bottom: 16px;

        .progress-bar {
          position: relative;
          height: 6px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 3px;
          cursor: pointer;
          transition: height 0.2s ease;

          &:hover {
            height: 8px;
          }

          .progress-filled {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.1s ease;
          }

          .progress-thumb {
            position: absolute;
            top: 50%;
            width: 16px;
            height: 16px;
            background: #fff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          }

          &:hover .progress-thumb {
            opacity: 1;
          }
        }
      }

      .controls-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .left-controls,
        .right-controls {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .control-btn {
          background: none;
          border: none;
          color: #fff;
          cursor: pointer;
          padding: 12px;
          border-radius: 8px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          .el-icon {
            font-size: 24px;
          }
        }

        .volume-control {
          position: relative;
          display: flex;
          align-items: center;

          .volume-slider {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 12px;

            .volume-range {
              writing-mode: bt-lr;
              -webkit-appearance: slider-vertical;
              width: 6px;
              height: 80px;
              background: rgba(255, 255, 255, 0.3);
              outline: none;
            }
          }
        }

        .time-display {
          color: #fff;
          font-size: 16px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 8px;

          .time-separator {
            opacity: 0.6;
          }
        }

        .quality-indicator {
          .quality-text {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
          }
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .fullscreen-video-player {
    .video-wrapper {
      .play-overlay .play-button {
        width: 80px;
        height: 80px;

        .play-icon {
          font-size: 32px;
        }
      }

      .video-controls {
        padding: 16px;

        .controls-row {
          gap: 12px;

          .control-btn {
            padding: 8px;

            .el-icon {
              font-size: 20px;
            }
          }

          .time-display {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style> 