import dayjs from 'dayjs';

/**
 * Format date to specified format
 * @param date Date to format
 * @param format Format pattern (default: YYYY-MM-DD HH:mm:ss)
 * @returns Formatted date string
 */
export function formatDate(date: string | number | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return '';
  return dayjs(date).format(format);
}

/**
 * Format date to date only format (YYYY-MM-DD)
 * @param date Date to format
 * @returns Formatted date string
 */
export function formatDateOnly(date: string | number | Date): string {
  return formatDate(date, 'YYYY-MM-DD');
}

/**
 * Format date to time only format (HH:mm:ss)
 * @param date Date to format
 * @returns Formatted time string
 */
export function formatTimeOnly(date: string | number | Date): string {
  return formatDate(date, 'HH:mm:ss');
}

/**
 * Get relative time (e.g., 2 hours ago, 3 days ago)
 * @param date Date to compare
 * @returns Relative time string
 */
export function getRelativeTime(date: string | number | Date): string {
  return dayjs(date).fromNow();
}

/**
 * Check if a date is valid
 * @param date Date to check
 * @returns boolean
 */
export function isValidDate(date: string | number | Date): boolean {
  return dayjs(date).isValid();
}