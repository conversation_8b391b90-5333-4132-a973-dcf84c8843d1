{"light": "浅色主题", "dark": "深色主题", "blue": "蓝色主题", "purple": "紫色主题", "green": "绿色主题", "pink": "粉色主题", "modern": "明快现代", "warm": "温暖友好", "fresh": "清新自然", "charm": "魅力诱惑", "mysterious": "神秘高雅", "mysterious-light": "神秘高雅 浅色", "mysterious-dark": "神秘高雅 深色", "modern-light": "明快现代 浅色", "modern-dark": "明快现代 深色", "warm-light": "温暖友好 浅色", "warm-dark": "温暖友好 深色", "dark-light": "时尚前卫 浅色", "dark-dark": "时尚前卫 深色", "fresh-light": "清新自然 浅色", "fresh-dark": "清新自然 深色", "charm-light": "魅力诱惑 浅色", "charm-dark": "魅力诱惑 深色", "selectTheme": "选择主题", "followSystem": "跟随系统主题", "themeSettings": "主题设置", "darkMode": "暗黑模式", "changeTheme": "更换主题", "themeSystemDemo": "主题系统演示", "themeSystemDescription": "此页面展示了基于PrimeVue和CSS变量的新主题系统。", "themeSelector": "主题选择器", "themeSelectorDescription": "主题选择器组件允许用户选择不同的主题并切换暗黑模式。", "themeVariables": "主题变量", "componentPreview": "组件预览", "buttons": "按钮", "inputs": "输入框", "cards": "卡片", "cardTitle": "卡片标题", "cardContent": "这是一个展示主题样式的示例卡片组件。", "apiUsage": "API使用方法"}