package pictures

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/pictures"
)

type PictureCollectionController struct {
	admin.BaseController
	collectionService pictures.PictureCollectionService
	pictureService    pictures.PictureService
}

func NewPictureCollectionController(
	collectionService pictures.PictureCollectionService,
	pictureService pictures.PictureService,
) *PictureCollectionController {
	return &PictureCollectionController{
		BaseController:    admin.BaseController{},
		collectionService: collectionService,
		pictureService:    pictureService,
	}
}
