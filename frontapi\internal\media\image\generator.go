package image

import (
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
)

// 随机图片API服务列表
var imageAPIs = []struct {
	name string
	url  string
}{
	{"Picsum", "https://picsum.photos"},
	{"Lorem Flickr", "https://loremflickr.com"},
	{"Placeholder", "https://via.placeholder.com"},
}

// RandomImageHandler 处理随机图片生成请求
// 路由格式: /image/random/:width/:height/:filename
func RandomImageHandler(c *fiber.Ctx) error {
	// 获取路径参数
	widthStr := c.Params("width")
	heightStr := c.Params("height")
	filename := c.Params("filename")

	// 验证参数
	if widthStr == "" || heightStr == "" || filename == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "invalid_params",
			"message": "缺少必要参数：width、height、filename",
		})
	}

	// 解析宽度和高度
	width, err := strconv.Atoi(widthStr)
	if err != nil || width <= 0 || width > 2000 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "invalid_width",
			"message": "宽度必须是1-2000之间的数字",
		})
	}

	height, err := strconv.Atoi(heightStr)
	if err != nil || height <= 0 || height > 2000 {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "invalid_height",
			"message": "高度必须是1-2000之间的数字",
		})
	}

	// 验证文件名格式（支持常见图片格式）
	filename = strings.ToLower(filename)
	if !strings.HasSuffix(filename, ".png") &&
		!strings.HasSuffix(filename, ".jpg") &&
		!strings.HasSuffix(filename, ".jpeg") {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error":   "invalid_format",
			"message": "支持的格式：.png, .jpg, .jpeg",
		})
	}

	// 获取随机图片
	imageData, contentType, err := fetchRandomImage(width, height, filename)
	if err != nil {
		log.Printf("获取随机图片失败: %v", err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error":   "fetch_failed",
			"message": "获取随机图片失败",
		})
	}

	// 设置响应头
	c.Set("Content-Type", contentType)
	c.Set("Cache-Control", "public, max-age=3600") // 缓存1小时
	c.Set("Content-Disposition", fmt.Sprintf("inline; filename=\"%s\"", filename))
	c.Set("Content-Length", strconv.Itoa(len(imageData)))

	// 返回图片数据
	return c.Send(imageData)
}

// fetchRandomImage 从随机API获取图片
func fetchRandomImage(width, height int, filename string) ([]byte, string, error) {
	// 使用文件名作为随机种子，但每小时变化一次
	rand.Seed(int64(hashString(filename)) + time.Now().Unix()/3600)

	// 随机选择一个API服务
	api := imageAPIs[rand.Intn(len(imageAPIs))]

	// 构建图片URL
	imageURL := buildImageURL(api, width, height)

	log.Printf("正在从 %s 获取图片: %s", api.name, imageURL)

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 15 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 允许重定向，但限制次数
			if len(via) >= 10 {
				return fmt.Errorf("重定向次数过多")
			}
			return nil
		},
	}

	// 发送请求
	resp, err := client.Get(imageURL)
	if err != nil {
		// 如果第一个API失败，尝试其他API
		return fetchRandomImageFallback(width, height, filename, api.name)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		log.Printf("API %s 返回状态码: %d", api.name, resp.StatusCode)
		return fetchRandomImageFallback(width, height, filename, api.name)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("读取图片数据失败: %v", err)
	}

	// 获取内容类型
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		// 根据文件扩展名设置默认内容类型
		if strings.HasSuffix(filename, ".png") {
			contentType = "image/png"
		} else {
			contentType = "image/jpeg"
		}
	}

	log.Printf("成功从 %s 获取图片，大小: %d bytes", api.name, len(imageData))
	return imageData, contentType, nil
}

// fetchRandomImageFallback 备用获取图片方法
func fetchRandomImageFallback(width, height int, filename, excludeAPI string) ([]byte, string, error) {
	// 尝试其他API服务
	for _, api := range imageAPIs {
		if api.name == excludeAPI {
			continue // 跳过已经失败的API
		}

		imageURL := buildImageURL(api, width, height)
		log.Printf("备用方案：尝试从 %s 获取图片: %s", api.name, imageURL)

		client := &http.Client{Timeout: 10 * time.Second}
		resp, err := client.Get(imageURL)
		if err != nil {
			log.Printf("备用API %s 失败: %v", api.name, err)
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			imageData, err := io.ReadAll(resp.Body)
			if err != nil {
				log.Printf("读取备用API %s 数据失败: %v", api.name, err)
				continue
			}

			contentType := resp.Header.Get("Content-Type")
			if contentType == "" {
				if strings.HasSuffix(filename, ".png") {
					contentType = "image/png"
				} else {
					contentType = "image/jpeg"
				}
			}

			log.Printf("备用方案成功：从 %s 获取图片，大小: %d bytes", api.name, len(imageData))
			return imageData, contentType, nil
		}
	}

	return nil, "", fmt.Errorf("所有图片API都无法访问")
}

// buildImageURL 根据不同API构建图片URL
func buildImageURL(api struct{ name, url string }, width, height int) string {
	switch api.name {
	case "Picsum":
		// Picsum Photos: https://picsum.photos/800/600?random=123
		return fmt.Sprintf("%s/%d/%d?random=%d", api.url, width, height, rand.Intn(10000))

	case "Lorem Flickr":
		// Lorem Flickr: https://loremflickr.com/800/600/nature
		topics := []string{"nature", "city", "technology", "food", "animals", "landscape", "architecture", "abstract", "people", "travel"}
		topic := topics[rand.Intn(len(topics))]
		return fmt.Sprintf("%s/%d/%d/%s", api.url, width, height, topic)

	case "Placeholder":
		// Via Placeholder: https://via.placeholder.com/800x600/0066cc/ffffff?text=Sample
		colors := []string{"0066cc", "ff6b6b", "4ecdc4", "45b7d1", "96ceb4", "feca57", "ff9ff3", "54a0ff"}
		bgColor := colors[rand.Intn(len(colors))]
		textColor := "ffffff"
		return fmt.Sprintf("%s/%dx%d/%s/%s?text=%dx%d", api.url, width, height, bgColor, textColor, width, height)

	default:
		return fmt.Sprintf("%s/%d/%d", api.url, width, height)
	}
}

// hashString 计算字符串哈希值
func hashString(s string) int {
	hash := 0
	for _, char := range s {
		hash = hash*31 + int(char)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}
