<template>
  <div class="player-controls">
    <!-- 进度条 -->
    <div class="progress-container">
      <div 
        class="progress-bar" 
        @click="handleProgressClick" 
        @mousedown="handleProgressMouseDown"
        ref="progressBar"
      >
        <div class="progress-track">
          <div 
            class="progress-fill" 
            :style="{ width: progressPercent + '%' }"
          ></div>
          <div 
            class="progress-thumb" 
            :style="{ left: progressPercent + '%' }"
            :class="{ dragging: isDragging }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 控制栏 -->
    <div class="controls-bar">
      <!-- 左侧：播放/暂停 + 时间显示 -->
      <div class="left-controls">
        <button class="control-btn play-pause-btn" @click="$emit('play-pause')">
          <img 
            :src="isPlaying ? pauseIcon : playIcon" 
            :alt="isPlaying ? '暂停' : '播放'"
          />
        </button>
        
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="separator">/</span>
          <span class="total-time">{{ formatTime(duration) }}</span>
        </div>
      </div>

      <!-- 中间：弹幕开关 -->
      <div class="center-controls">
        <slot name="danmu-control">
          <!-- 弹幕控制 -->
          
        </slot>
      </div>

      <!-- 右侧：音量、倍速、清晰度、全屏 -->
      <div class="right-controls">
        <!-- 音量控制 -->
        <div class="volume-control">
          <button 
            class="control-btn volume-btn" 
            @click="handleVolumeClick" 
            @dblclick="toggleVolumeSlider"
            title="音量"
          >
            <img 
              :src="isMuted ? muteIcon : voiceIcon" 
              :alt="isMuted ? '取消静音' : '静音'"
            />
          </button>
          <div class="volume-slider" v-show="showVolumeSlider">
            <input 
              type="range" 
              min="0" 
              max="100" 
              :value="volume"
              @input="handleVolumeChange"
              class="volume-range"
            />
          </div>
        </div>
        
        <!-- 播放速度 -->
        <div class="speed-control">
          <button 
            class="control-btn speed-btn" 
            @click="toggleSpeedMenu" 
            title="播放速度"
          >
            <span class="speed-text">{{ currentSpeed }}x</span>
          </button>
          <div class="speed-menu" v-show="showSpeedMenu">
            <div 
              v-for="speed in speedOptions" 
              :key="speed"
              class="speed-option"
              :class="{ active: currentSpeed === speed }"
              @click="selectSpeed(speed)"
            >
              {{ speed }}x
            </div>
          </div>
        </div>
        
        <!-- 清晰度 -->
        <div class="quality-control">
          <button 
            class="control-btn quality-btn" 
            @click="toggleQualityMenu" 
            title="清晰度"
          >
            <span class="quality-text">{{ currentQuality }}</span>
          </button>
          <div class="quality-menu" v-show="showQualityMenu">
            <div 
              v-for="quality in qualityOptions" 
              :key="quality.value"
              class="quality-option"
              :class="{ active: currentQuality === quality.label }"
              @click="selectQuality(quality)"
            >
              {{ quality.label }}
            </div>
          </div>
        </div>
        
        <!-- 网页全屏 -->
        <button 
          class="control-btn webpage-fullscreen-btn" 
          @click="$emit('webpage-fullscreen')" 
          title="网页全屏"
        >
         <img :src="pageFullscreenIcon" alt="网页全屏" />
        </button>
        
        <!-- 全屏 -->
        <button 
          class="control-btn fullscreen-btn" 
          @click="$emit('fullscreen')" 
          title="全屏"
        >
          <img :src="fullscreen" alt="全屏" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ShortVideo } from '../../../types/shorts'
import { computed, ref, onMounted, onUnmounted } from 'vue'

// 导入图标
import playIcon from '@/assets/icons/play.svg'
import pauseIcon from '@/assets/icons/pause.svg'
import voiceIcon from '@/assets/icons/voice.svg'
import muteIcon from '@/assets/icons/mute.svg'
import fullscreen from '@/assets/icons/fullscreen.svg'
import danmuOpenIcon from '@/assets/icons/danmu_open.svg'
import danmuCloseIcon from '@/assets/icons/danmu_close.svg'
import pageFullscreenIcon from '@/assets/icons/page_fullscreen.svg'

// Props
interface Props {
  video?: ShortVideo
  isPlaying: boolean
  isMuted: boolean
  canGoPrevious: boolean
  canGoNext: boolean
  currentTime?: number
  duration?: number
  volume?: number
  isDanmuOpen?: boolean
  qualityOptions?: Array<{ label: string; value: string }>
  currentQuality?: string
  isUserLoggedIn: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentTime: 0,
  duration: 0,
  volume: 100,
  isDanmuOpen: false,
  qualityOptions: () => [
    { label: '标清', value: '480p' },
    { label: '高清', value: '720p' },
    { label: '超清', value: '1080p' }
  ],
  currentQuality: '标清',
  isUserLoggedIn: false
})

// Emits
const emit = defineEmits<{
  'play-pause': []
  'mute-toggle': []
  'fullscreen': []
  'webpage-fullscreen': []
  'danmu-toggle': []
  'quality-change': [quality: { label: string; value: string }]
  'speed-change': [speed: number]
  'volume-change': [volume: number]
  'progress-change': [time: number]
  'danmu-input': []
  'danmu-input-click': []
  'danmu-input-focus': []
}>()

// Refs
const progressBar = ref<HTMLElement>()
const showVolumeSlider = ref(false)
const showSpeedMenu = ref(false)
const showQualityMenu = ref(false)
const isDragging = ref(false)
const dragStartX = ref(0)
const dragStartPercent = ref(0)
const previousVolume = ref(100)

// 播放速度选项
const speedOptions = [0.5, 0.75, 1, 1.25, 1.5, 2]
const currentSpeed = ref(1)

// 计算进度百分比
const progressPercent = computed(() => {
  if (!props.duration || props.duration === 0) return 0
  return (props.currentTime / props.duration) * 100
})

// 格式化时间
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '00:00'
  
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 处理进度条点击
const handleProgressClick = (event: MouseEvent) => {
  if (!progressBar.value || !props.duration || isDragging.value) return
  
  const rect = progressBar.value.getBoundingClientRect()
  const clickX = event.clientX - rect.left
  const percent = Math.max(0, Math.min(100, (clickX / rect.width) * 100))
  const newTime = (percent / 100) * props.duration
  
  emit('progress-change', newTime)
}

// 处理进度条拖拽开始
const handleProgressMouseDown = (event: MouseEvent) => {
  if (!progressBar.value || !props.duration) return
  
  isDragging.value = true
  dragStartX.value = event.clientX
  dragStartPercent.value = progressPercent.value
  
  document.addEventListener('mousemove', handleProgressMouseMove)
  document.addEventListener('mouseup', handleProgressMouseUp)
  
  // 阻止默认行为
  event.preventDefault()
}

// 处理进度条拖拽移动
const handleProgressMouseMove = (event: MouseEvent) => {
  if (!isDragging.value || !progressBar.value || !props.duration) return
  
  const rect = progressBar.value.getBoundingClientRect()
  const deltaX = event.clientX - dragStartX.value
  const deltaPercent = (deltaX / rect.width) * 100
  const newPercent = Math.max(0, Math.min(100, dragStartPercent.value + deltaPercent))
  const newTime = (newPercent / 100) * props.duration
  
  emit('progress-change', newTime)
}

// 处理进度条拖拽结束
const handleProgressMouseUp = () => {
  isDragging.value = false
  
  document.removeEventListener('mousemove', handleProgressMouseMove)
  document.removeEventListener('mouseup', handleProgressMouseUp)
}

// 音量控制
const handleVolumeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const volume = parseInt(target.value)
  emit('volume-change', volume)
  
  // 如果音量大于0，保存为之前的音量
  if (volume > 0) {
    previousVolume.value = volume
  }
}

// 音量按钮点击处理 - 修复逻辑
const handleVolumeClick = (event: Event) => {
  event.stopPropagation()
  
  // 如果有音量控制条显示
  if (showVolumeSlider.value) {
    // 判断是否静音
    if (props.isMuted || props.volume === 0) {
      // 如果是静音，开启声音并设置到之前的音量
      emit('volume-change', previousVolume.value)
    } else {
      // 如果不是静音，则调整成静音
      emit('mute-toggle')
    }
  } else {
    // 如果没有音量控制条，则打开音量控制条
    showVolumeSlider.value = true
  }
}

// 切换音量滑块显示
const toggleVolumeSlider = () => {
  showVolumeSlider.value = !showVolumeSlider.value
}

// 播放速度控制
const toggleSpeedMenu = () => {
  showSpeedMenu.value = !showSpeedMenu.value
  showQualityMenu.value = false
}

const selectSpeed = (speed: number) => {
  currentSpeed.value = speed
  showSpeedMenu.value = false
  emit('speed-change', speed)
}

// 清晰度控制
const toggleQualityMenu = () => {
  showQualityMenu.value = !showQualityMenu.value
  showSpeedMenu.value = false
}

const selectQuality = (quality: { label: string; value: string }) => {
  showQualityMenu.value = false
  emit('quality-change', quality)
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.speed-control')) {
    showSpeedMenu.value = false
  }
  if (!target.closest('.quality-control')) {
    showQualityMenu.value = false
  }
  if (!target.closest('.volume-control')) {
    showVolumeSlider.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  // 初始化之前的音量值
  if (props.volume && props.volume > 0) {
    previousVolume.value = props.volume
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 弹幕控制
const toggleDanmu = () => {
  emit('danmu-toggle')
}

// 弹幕输入
const handleDanmuInputClick = () => {
  if (props.isDanmuOpen && props.isUserLoggedIn) {
    emit('danmu-input-click')
  }
}

const handleDanmuInputFocus = () => {
  if (props.isDanmuOpen && props.isUserLoggedIn) {
    emit('danmu-input-focus')
  }
}


</script>

<style scoped lang="scss">
.player-controls {
  width: calc(100% - 40px);
  z-index: 30;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
  padding: 10px 20px 10px;
  backdrop-filter: blur(10px);
  pointer-events: auto;

  /* 进度条 */
  .progress-container {
    .progress-bar {
      cursor: pointer;
      padding: 8px 0;
      user-select: none;

      &:hover .progress-track {
        height: 6px;
      }

      &:hover .progress-thumb,
      .progress-thumb.dragging {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.2);
      }

      .progress-track {
        position: relative;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        transition: height 0.2s ease;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
          border-radius: 2px;
          transition: width 0.1s ease;
        }

        .progress-thumb {
          position: absolute;
          top: 50%;
          width: 14px;
          height: 14px;
          background: #fff;
          border-radius: 50%;
          transform: translate(-50%, -50%);
          opacity: 0;
          transition: all 0.2s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
          cursor: grab;

          &.dragging {
            cursor: grabbing;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
          }
        }
      }
    }
  }

  /* 控制栏 */
  .controls-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    .left-controls,
    .center-controls,
    .right-controls {
      display: flex;
      align-items: center;
    }

    .left-controls {
      flex: 0 0 auto;
      min-width: 200px;
      gap: 16px;
    }

    .center-controls {
      flex: 0 0 auto;
      gap: 16px;
    }

    .right-controls {
      flex: 0 0 auto;
      gap: 8px;
    }
  }

  /* 控制按钮基础样式 */
  .control-btn {
    background: transparent;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    min-width: 56px;
    min-height: 56px;

    &:hover {
      background: transparent;
    }

    &:active {
      background: transparent;
    }

    img {
      width: 40px;
      height: 40px;
      filter: brightness(0) invert(1);
      pointer-events: none;
      transition: filter 0.2s ease;
      display: block;
      flex-shrink: 0;
    }

    &:hover img {
      filter: brightness(0) invert(1) brightness(1.3);
    }

    &:active img {
      filter: brightness(0) invert(1) brightness(0.8);
    }
  }

  /* 左侧控制按钮 - 保持原样式 */
  .left-controls {
    .play-pause-btn {
      width: 64px;
      height: 64px;
      background: transparent;
      border-radius: 50%;
      padding: 0;

      &:hover {
        background: transparent;
      }

      &:active {
        background: transparent;
      }

      img {
        width: 48px;
        height: 48px;
      }
    }

    .time-display {
      display: flex;
      align-items: center;
      gap: 6px;
      color: white;
      font-size: 16px;
      font-weight: 500;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      background: rgba(0, 0, 0, 0.3);
      padding: 8px 12px;
      border-radius: 20px;
      backdrop-filter: blur(10px);

      .current-time,
      .total-time {
        min-width: 40px;
        text-align: center;
      }

      .separator {
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  /* 中间弹幕按钮 - 保留背景 */
  .center-controls {

  }

  /* 右侧控制按钮 - 添加背景和边框，调小尺寸 */
  .right-controls {
    .control-btn {
      min-width: 40px;
      min-height: 40px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }

      &:active {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.25);
      }

      img {
        width: 24px;
        height: 24px;
      }
    }

    /* 音量控制 */
    .volume-control {
      position: relative;
      display: flex;
      align-items: center;

      .volume-slider {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 10px;
        background: rgba(0, 0, 0, 0.8);
        padding: 12px 8px;
        border-radius: 8px;
        backdrop-filter: blur(10px);

        .volume-range {
          writing-mode: bt-lr;
          -webkit-appearance: slider-vertical;
          width: 6px;
          height: 80px;
          background: rgba(255, 255, 255, 0.3);
          outline: none;
          border-radius: 3px;

          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background: #ff6b6b;
            cursor: pointer;
          }
        }
      }
    }

    /* 速度控制 */
    .speed-control {
      position: relative;

      .speed-btn {
        height: 40px;
        padding: 0 12px;
        border-radius: 20px;
        min-width: 56px;

        .speed-text {
          font-size: 14px;
          font-weight: 600;
          color: white;
        }

        &:hover .speed-text {
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .speed-menu {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 10px;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 8px;
        padding: 8px 0;
        min-width: 80px;
        backdrop-filter: blur(10px);

        .speed-option {
          padding: 8px 16px;
          color: white;
          cursor: pointer;
          font-size: 13px;
          text-align: center;
          transition: background-color 0.2s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }

          &.active {
            background: rgba(255, 107, 107, 0.8);
            color: white;
          }
        }
      }
    }

    /* 清晰度控制 */
    .quality-control {
      position: relative;

      .quality-btn {
        height: 40px;
        padding: 0 12px;
        border-radius: 20px;
        min-width: 64px;

        .quality-text {
          font-size: 14px;
          font-weight: 600;
          color: white;
        }

        &:hover .quality-text {
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .quality-menu {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 10px;
        background: rgba(0, 0, 0, 0.9);
        border-radius: 8px;
        padding: 8px 0;
        min-width: 100px;
        backdrop-filter: blur(10px);

        .quality-option {
          padding: 8px 16px;
          color: white;
          cursor: pointer;
          font-size: 13px;
          text-align: center;
          transition: background-color 0.2s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }

          &.active {
            background: rgba(255, 107, 107, 0.8);
            color: white;
          }
        }
      }
    }

    /* 全屏按钮 */
    .webpage-fullscreen-btn,
    .fullscreen-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      padding: 0;

      img {
        width: 24px;
        height: 24px;
      }
    }
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    padding: 15px 16px 12px;

    .controls-bar {
      gap: 12px;

      .left-controls {
        min-width: 160px;
        gap: 12px;

        .play-pause-btn {
          width: 56px;
          height: 56px;
          padding: 0;

          img {
            width: 40px;
            height: 40px;
          }
        }

        .time-display {
          font-size: 14px;
          padding: 6px 10px;

          .current-time,
          .total-time {
            min-width: 35px;
          }
        }
      }

      .center-controls {
        .danmu-controls {
          button{
            border-radius: none;
          }
          .danmu-toggle-btn {
            height: 40px;
            padding: 0 16px;
            border-radius: none;
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
            img {
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .right-controls {
        gap: 8px;

        .control-btn {
          min-width: 36px;
          min-height: 36px;

          img {
            width: 20px;
            height: 20px;
          }
        }

        .speed-btn,
        .quality-btn {
          height: 36px;
          min-width: 48px;
          padding: 0 10px;

          .speed-text,
          .quality-text {
            font-size: 12px;
          }
        }

        .webpage-fullscreen-btn,
        .fullscreen-btn {
          width: 36px;
          height: 36px;

          img {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .controls-bar {
      gap: 8px;

      .left-controls {
        min-width: 140px;
        gap: 8px;

        .play-pause-btn {
          width: 48px;
          height: 48px;
          padding: 0;

          img {
            width: 32px;
            height: 32px;
          }
        }

        .time-display {
          font-size: 12px;
          padding: 4px 8px;
        }
      }

      .center-controls {
        .danmu-controls {
          button{
            border-radius: none;
          }
          .danmu-toggle-btn {
            height: 36px;
            padding: 0 12px;
            border-radius: none;
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
            img {
              width: 20px;
              height: 20px;
            }
          }
        }
      }

      .right-controls {
        gap: 6px;

        .control-btn {
          min-width: 32px;
          min-height: 32px;

          img {
            width: 18px;
            height: 18px;
          }
        }

        .speed-btn,
        .quality-btn {
          height: 32px;
          min-width: 40px;
          padding: 0 8px;

          .speed-text,
          .quality-text {
            font-size: 11px;
          }
        }

        .webpage-fullscreen-btn,
        .fullscreen-btn {
          width: 32px;
          height: 32px;

          img {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
  }
}
</style> 