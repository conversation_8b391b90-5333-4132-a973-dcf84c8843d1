package tags

import (
	"frontapi/internal/models/system"
	"frontapi/internal/repository/tags"
	"frontapi/internal/service/base"
)

// TagService 标签服务接口
type TagService interface {
	base.IExtendedService[system.Tag]
}

// tagService 标签服务实现
type tagService struct {
	*base.ExtendedService[system.Tag]
	tagRepo tags.TagRepository
}

// NewTagService 创建标签服务实例
func NewTagService(tagRepo tags.TagRepository) TagService {
	return &tagService{
		ExtendedService: base.NewExtendedService[system.Tag](tagRepo, "tag"),
		tagRepo:         tagRepo,
	}
}
