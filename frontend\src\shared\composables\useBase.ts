/**
 * 基础组合式函数
 */

import { ref, computed, reactive, toRefs, watch, nextTick } from 'vue'
import type {
  UsePaginationReturn,
  PaginationOptions,
  UseRequestReturn,
  RequestOptions,
  UseFormReturn,
  FormConfig,
  FormValidationRule,
  UseModalReturn,
  UseStorageReturn,
  StorageOptions
} from './types'

/**
 * 分页组合式函数
 */
export function usePagination(options: PaginationOptions = {}): UsePaginationReturn {
  const {
    current = 1,
    pageSize = 10,
    showSizeChanger = true,
    showQuickJumper = true,
    showTotal = true
  } = options

  const pagination = reactive({
    current,
    pageSize,
    total: 0,
    showSizeChanger,
    showQuickJumper,
    showTotal
  })

  const totalPages = computed(() => {
    return Math.ceil(pagination.total / pagination.pageSize)
  })

  const hasNext = computed(() => {
    return pagination.current < totalPages.value
  })

  const hasPrev = computed(() => {
    return pagination.current > 1
  })

  const setPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      pagination.current = page
    }
  }

  const setPageSize = (size: number) => {
    pagination.pageSize = size
    // 重新计算当前页
    const maxPage = Math.ceil(pagination.total / size)
    if (pagination.current > maxPage) {
      pagination.current = Math.max(1, maxPage)
    }
  }

  const setTotal = (total: number) => {
    pagination.total = total
    // 检查当前页是否超出范围
    const maxPage = Math.ceil(total / pagination.pageSize)
    if (pagination.current > maxPage) {
      pagination.current = Math.max(1, maxPage)
    }
  }

  const nextPage = () => {
    if (hasNext.value) {
      pagination.current++
    }
  }

  const prevPage = () => {
    if (hasPrev.value) {
      pagination.current--
    }
  }

  const reset = () => {
    pagination.current = current
    pagination.pageSize = pageSize
    pagination.total = 0
  }

  return {
    pagination: ref(pagination),
    current: toRefs(pagination).current,
    pageSize: toRefs(pagination).pageSize,
    total: toRefs(pagination).total,
    totalPages,
    hasNext,
    hasPrev,
    setPage,
    setPageSize,
    setTotal,
    nextPage,
    prevPage,
    reset
  }
}

/**
 * 请求组合式函数
 */
export function useRequest<T = any>(
  requestFn: (...args: any[]) => Promise<T>,
  options: RequestOptions = {}
): UseRequestReturn<T> {
  const {
    immediate = false,
    resetOnExecute = true,
    shallow = false,
    throwOnFailed = false
  } = options

  const data = ref<T | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const isFinished = ref(false)

  const execute = async (...args: any[]): Promise<T> => {
    if (resetOnExecute) {
      data.value = null
      error.value = null
    }

    loading.value = true
    isFinished.value = false

    try {
      const result = await requestFn(...args)
      data.value = result
      isFinished.value = true
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err)
      error.value = errorMessage
      isFinished.value = true
      
      if (throwOnFailed) {
        throw err
      }
      
      throw err
    } finally {
      loading.value = false
    }
  }

  const refresh = () => execute()

  const reset = () => {
    data.value = null
    loading.value = false
    error.value = null
    isFinished.value = false
  }

  if (immediate) {
    execute()
  }

  return {
    data: shallow ? data : ref(data.value),
    loading,
    error,
    execute,
    refresh,
    reset,
    isFinished
  }
}

/**
 * 表单组合式函数
 */
export function useForm<T extends Record<string, any> = Record<string, any>>(
  config: FormConfig<T> = {}
): UseFormReturn<T> {
  const {
    initialValues = {} as T,
    validationRules = {},
    validateOnChange = true,
    validateOnBlur = true,
    resetOnSubmit = false
  } = config

  const values = ref({ ...initialValues } as T)
  const errors = ref({} as Record<keyof T, string[]>)
  const touched = ref({} as Record<keyof T, boolean>)
  const dirty = ref({} as Record<keyof T, boolean>)
  const submitting = ref(false)

  const valid = computed(() => {
    return Object.keys(errors.value).length === 0
  })

  const validateRule = async (value: any, rule: FormValidationRule): Promise<string | null> => {
    // 必填验证
    if (rule.required && (value === null || value === undefined || value === '')) {
      return rule.message || '此字段为必填项'
    }

    // 如果值为空且非必填，跳过其他验证
    if ((value === null || value === undefined || value === '') && !rule.required) {
      return null
    }

    // 最小值验证
    if (rule.min !== undefined && typeof value === 'number' && value < rule.min) {
      return rule.message || `值不能小于${rule.min}`
    }

    // 最大值验证
    if (rule.max !== undefined && typeof value === 'number' && value > rule.max) {
      return rule.message || `值不能大于${rule.max}`
    }

    // 最小长度验证
    if (rule.minLength !== undefined && typeof value === 'string' && value.length < rule.minLength) {
      return rule.message || `长度不能少于${rule.minLength}位`
    }

    // 最大长度验证
    if (rule.maxLength !== undefined && typeof value === 'string' && value.length > rule.maxLength) {
      return rule.message || `长度不能超过${rule.maxLength}位`
    }

    // 正则验证
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      return rule.message || '格式不正确'
    }

    // 自定义验证器
    if (rule.validator) {
      const result = await rule.validator(value)
      if (result === false) {
        return rule.message || '验证失败'
      }
      if (typeof result === 'string') {
        return result
      }
    }

    return null
  }

  const validateField = async (field: keyof T): Promise<boolean> => {
    const fieldRules = validationRules[field]
    if (!fieldRules) {
      return true
    }

    const rules = Array.isArray(fieldRules) ? fieldRules : [fieldRules]
    const fieldErrors: string[] = []

    for (const rule of rules) {
      const error = await validateRule(values.value[field], rule)
      if (error) {
        fieldErrors.push(error)
      }
    }

    if (fieldErrors.length > 0) {
      errors.value[field] = fieldErrors
      return false
    } else {
      delete errors.value[field]
      return true
    }
  }

  const validate = async (field?: keyof T): Promise<boolean> => {
    if (field) {
      return await validateField(field)
    }

    const fieldsToValidate = Object.keys(validationRules) as (keyof T)[]
    const results = await Promise.all(
      fieldsToValidate.map(f => validateField(f))
    )

    return results.every(result => result)
  }

  const setValue = (field: keyof T, value: any) => {
    values.value[field] = value
    dirty.value[field] = true

    if (validateOnChange) {
      nextTick(() => {
        validateField(field)
      })
    }
  }

  const setValues = (newValues: Partial<T>) => {
    Object.assign(values.value, newValues)
    Object.keys(newValues).forEach(key => {
      dirty.value[key as keyof T] = true
    })

    if (validateOnChange) {
      nextTick(() => {
        Object.keys(newValues).forEach(key => {
          validateField(key as keyof T)
        })
      })
    }
  }

  const setError = (field: keyof T, error: string | string[]) => {
    errors.value[field] = Array.isArray(error) ? error : [error]
  }

  const setErrors = (newErrors: Partial<Record<keyof T, string | string[]>>) => {
    Object.entries(newErrors).forEach(([key, error]) => {
      if (error) {
        setError(key as keyof T, error)
      }
    })
  }

  const clearError = (field: keyof T) => {
    delete errors.value[field]
  }

  const clearErrors = () => {
    errors.value = {} as Record<keyof T, string[]>
  }

  const setTouched = (field: keyof T, isTouched = true) => {
    touched.value[field] = isTouched

    if (validateOnBlur && isTouched) {
      nextTick(() => {
        validateField(field)
      })
    }
  }

  const setFieldTouched = (fields: Partial<Record<keyof T, boolean>>) => {
    Object.assign(touched.value, fields)

    if (validateOnBlur) {
      nextTick(() => {
        Object.keys(fields).forEach(key => {
          if (fields[key as keyof T]) {
            validateField(key as keyof T)
          }
        })
      })
    }
  }

  const submit = async (onSubmit: (values: T) => void | Promise<void>) => {
    submitting.value = true

    try {
      const isValid = await validate()
      if (isValid) {
        await onSubmit(values.value)
        if (resetOnSubmit) {
          reset()
        }
      }
    } finally {
      submitting.value = false
    }
  }

  const reset = () => {
    values.value = { ...initialValues } as T
    errors.value = {} as Record<keyof T, string[]>
    touched.value = {} as Record<keyof T, boolean>
    dirty.value = {} as Record<keyof T, boolean>
    submitting.value = false
  }

  const resetField = (field: keyof T) => {
    values.value[field] = initialValues[field]
    delete errors.value[field]
    delete touched.value[field]
    delete dirty.value[field]
  }

  return {
    values,
    errors,
    touched,
    dirty,
    valid,
    submitting,
    setValue,
    setValues,
    setError,
    setErrors,
    clearError,
    clearErrors,
    setTouched,
    setFieldTouched,
    validate,
    validateField,
    submit,
    reset,
    resetField
  }
}

/**
 * 模态框组合式函数
 */
export function useModal(): UseModalReturn {
  const visible = ref(false)
  const loading = ref(false)
  const data = ref<any>(null)

  const open = (modalData?: any) => {
    visible.value = true
    if (modalData !== undefined) {
      data.value = modalData
    }
  }

  const close = () => {
    visible.value = false
    loading.value = false
    data.value = null
  }

  const toggle = () => {
    if (visible.value) {
      close()
    } else {
      open()
    }
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setData = (modalData: any) => {
    data.value = modalData
  }

  return {
    visible,
    loading,
    data,
    open,
    close,
    toggle,
    setLoading,
    setData
  }
}

/**
 * 本地存储组合式函数
 */
export function useStorage<T>(
  key: string,
  defaultValue: T,
  options: StorageOptions = {}
): UseStorageReturn<T> {
  const {
    serializer = {
      read: JSON.parse,
      write: JSON.stringify
    },
    storage = localStorage,
    shallow = false
  } = options

  const storedValue = ref<T>(defaultValue)

  // 读取存储的值
  const read = (): T => {
    try {
      const item = storage.getItem(key)
      if (item === null) {
        return defaultValue
      }
      return serializer.read(item)
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return defaultValue
    }
  }

  // 写入存储的值
  const write = (value: T) => {
    try {
      storage.setItem(key, serializer.write(value))
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }

  // 初始化值
  storedValue.value = read()

  // 监听值的变化
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: !shallow }
  )

  // 监听存储事件（其他标签页的变化）
  if (typeof window !== 'undefined') {
    window.addEventListener('storage', (e) => {
      if (e.key === key && e.newValue !== null) {
        try {
          storedValue.value = serializer.read(e.newValue)
        } catch (error) {
          console.warn(`Error parsing localStorage key "${key}":`, error)
        }
      }
    })
  }

  const remove = () => {
    try {
      storage.removeItem(key)
      storedValue.value = defaultValue
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  }

  const clear = () => {
    try {
      storage.clear()
      storedValue.value = defaultValue
    } catch (error) {
      console.warn('Error clearing localStorage:', error)
    }
  }

  return {
    value: storedValue,
    remove,
    clear
  }
}

/**
 * 防抖组合式函数
 */
export function useDebounce<T>(value: Ref<T>, delay = 300) {
  const debouncedValue = ref<T>(value.value)
  let timeoutId: NodeJS.Timeout | null = null

  watch(
    value,
    (newValue) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        debouncedValue.value = newValue
      }, delay)
    },
    { immediate: true }
  )

  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  const flush = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      debouncedValue.value = value.value
      timeoutId = null
    }
  }

  return {
    debouncedValue,
    cancel,
    flush
  }
}

/**
 * 节流组合式函数
 */
export function useThrottle<T>(value: Ref<T>, delay = 300) {
  const throttledValue = ref<T>(value.value)
  let lastUpdate = 0
  let timeoutId: NodeJS.Timeout | null = null

  watch(
    value,
    (newValue) => {
      const now = Date.now()
      
      if (now - lastUpdate >= delay) {
        throttledValue.value = newValue
        lastUpdate = now
      } else {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        timeoutId = setTimeout(() => {
          throttledValue.value = newValue
          lastUpdate = Date.now()
        }, delay - (now - lastUpdate))
      }
    },
    { immediate: true }
  )

  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }

  const flush = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      throttledValue.value = value.value
      lastUpdate = Date.now()
      timeoutId = null
    }
  }

  return {
    throttledValue,
    cancel,
    flush
  }
}