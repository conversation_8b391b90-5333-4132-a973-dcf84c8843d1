/**
 * 视频应用类型定义
 */
import { type BaseAuthor } from "./user"
// 视频基础信息
export interface Video {
  id: string
  title: string
  description?: string
  thumbnail: string
  image: string
  cover: string
  url: string
  duration: number
  resolution?: string
  fileSize?: number
  format?: string
  hot: number
  heat: number
  
  // 统计信息
  viewCount: number
  likeCount: number
  commentCount: number
  shareCount: number
  favoriteCount: number
  
  // 创作者信息
  creatorId: string
  creatorName: string
  creatorAvatar: string
  creatorVerified?: boolean
  
  // 分类和标签
  categoryId?: string
  categoryName?: string
  tags: string[]
  
  // 状态信息
  status: VideoStatus
  isPublic: boolean
  isPaid: boolean
  price?: number
  isFeatured: boolean
  
  // 时间信息
  uploadTime: string
  publishTime?: string
  createdAt: string
  updatedAt: string
  
  // 用户交互状态
  isLiked?: boolean
  isFavorited?: boolean
  isFollowing?: boolean
  watchProgress?: number
  author?: BaseAuthor
}

// 视频状态枚举
export enum VideoStatus {
  DRAFT = 0,
  PUBLISHED = 1,
  PRIVATE = 2,
  DELETED = 3,
  REVIEWING = 4,
  REJECTED = 5
}

// 视频分类
export interface VideoCategory {
  id: string
  name: string
  code: string
  parentId?: string
  description?: string
  cover?: string
  sortOrder: number
  status: number
  createdAt: string
  updatedAt: string
}

// 视频播放器配置
export interface VideoPlayerConfig {
  autoplay?: boolean
  muted?: boolean
  loop?: boolean
  controls?: boolean
  preload?: 'none' | 'metadata' | 'auto'
  poster?: string
  playbackRates?: number[]
  volume?: number
  currentTime?: number
  
  // 高级配置
  hls?: boolean
  dash?: boolean
  subtitles?: VideoSubtitle[]
  chapters?: VideoChapter[]
  
  // 样式配置
  width?: number | string
  height?: number | string
  aspectRatio?: string
  responsive?: boolean
  
  // 功能配置
  allowFullscreen?: boolean
  allowPictureInPicture?: boolean
  allowDownload?: boolean
  allowShare?: boolean
  showProgress?: boolean
  showDuration?: boolean
  showVolume?: boolean
  showPlaybackRate?: boolean
}

// 视频字幕
export interface VideoSubtitle {
  id: string
  language: string
  label: string
  src: string
  default?: boolean
}

// 视频章节
export interface VideoChapter {
  id: string
  title: string
  startTime: number
  endTime?: number
  thumbnail?: string
}

// 视频播放状态
export interface VideoPlayState {
  isPlaying: boolean
  isPaused: boolean
  isLoading: boolean
  isBuffering: boolean
  isEnded: boolean
  isMuted: boolean
  isFullscreen: boolean
  isPictureInPicture: boolean
  
  // 播放进度
  currentTime: number
  duration: number
  buffered: TimeRanges | null
  seekable: TimeRanges | null
  
  // 音量和播放速度
  volume: number
  playbackRate: number
  
  // 视频质量
  videoWidth: number
  videoHeight: number
  
  // 网络状态
  networkState: number
  readyState: number
  
  // 错误信息
  error: MediaError | null
}

// 视频评论
export interface VideoComment {
  id: string
  videoId: string
  userId: string
  username: string
  userAvatar: string
  content: string
  parentId?: string
  likeCount: number
  replyCount: number
  status: number
  createdAt: string
  updatedAt: string
  
  // 扩展信息
  isLiked?: boolean
  replies?: VideoComment[]
  timestamp?: number // 视频时间点评论
}

// 视频推荐
export interface VideoRecommendation {
  id: string
  title: string
  reason: string
  score: number
  videos: Video[]
}

// 视频搜索参数
export interface VideoSearchParams {
  keyword?: string
  categoryId?: string
  creatorId?: string
  tags?: string[]
  duration?: {
    min?: number
    max?: number
  }
  uploadTime?: {
    start?: string
    end?: string
  }
  sortBy?: 'latest' | 'popular' | 'trending' | 'rating'
  sortOrder?: 'asc' | 'desc'
}

// 视频列表参数
export interface VideoListParams {
  page: number
  pageSize: number
  filters?: VideoSearchParams
}

// 视频统计信息
export interface VideoStats {
  totalViews: number
  totalLikes: number
  totalComments: number
  totalShares: number
  totalFavorites: number
  averageRating: number
  watchTime: number
  completionRate: number
}

// 视频上传信息
export interface VideoUpload {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  duration?: number
  resolution?: string
  status: UploadStatus
  progress: number
  uploadedAt: string
  
  // 处理信息
  processStatus?: ProcessStatus
  processProgress?: number
  thumbnails?: string[]
  qualities?: VideoQuality[]
}

// 上传状态
export enum UploadStatus {
  PENDING = 0,
  UPLOADING = 1,
  COMPLETED = 2,
  FAILED = 3,
  CANCELLED = 4
}

// 处理状态
export enum ProcessStatus {
  PENDING = 0,
  PROCESSING = 1,
  COMPLETED = 2,
  FAILED = 3
}

// 视频质量
export interface VideoQuality {
  label: string
  resolution: string
  bitrate: number
  url: string
  size?: number
}

// 视频播放历史
export interface VideoHistory {
  id: string
  videoId: string
  userId: string
  watchTime: number
  progress: number
  lastWatchAt: string
  device?: string
  ip?: string
}

// 视频收藏
export interface VideoFavorite {
  id: string
  videoId: string
  userId: string
  folderId?: string
  folderName?: string
  createdAt: string
}

// 视频分享
export interface VideoShare {
  id: string
  videoId: string
  userId: string
  platform: SharePlatform
  sharedAt: string
}

// 分享平台
export enum SharePlatform {
  WECHAT = 'wechat',
  WEIBO = 'weibo',
  QQ = 'qq',
  FACEBOOK = 'facebook',
  TWITTER = 'twitter',
  LINK = 'link'
}