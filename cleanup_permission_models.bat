@echo off
echo 正在清理权限模型文件...

rem 删除Models目录中的过时文件
del /q internal\models\permission\admin_sys_menu.go 2>nul
del /q internal\models\permission\admin_sys_role.go 2>nul
del /q internal\models\permission\admin_sys_role_menus.go 2>nul
del /q internal\models\permission\admin_sys_user.go 2>nul
del /q internal\models\permission\admin_sys_user_role.go 2>nul
del /q internal\models\permission\admin_user_access_tokens.go 2>nul
del /q internal\models\permission\admin_user_refresh_tokens.go 2>nul
del /q internal\models\permission\base_admin_model.go 2>nul

rem 删除Repository目录中的过时文件
del /q internal\repository\permission\admin_permission_repository.go 2>nul
del /q internal\repository\permission\dept_repository.go 2>nul
del /q internal\repository\permission\login_log_repository.go 2>nul
del /q internal\repository\permission\menu_repository.go 2>nul
del /q internal\repository\permission\role_menus_repository.go 2>nul
del /q internal\repository\permission\sys_config_repository.go 2>nul
del /q internal\repository\permission\sys_menu_repository.go 2>nul
del /q internal\repository\permission\sys_role_repository.go 2>nul
del /q internal\repository\permission\sys_user_repository.go 2>nul

rem 删除Service目录中的过时文件  
del /q internal\service\permission\admin_permission_service.go 2>nul
del /q internal\service\permission\cache_types.go 2>nul
del /q internal\service\permission\dept_service.go 2>nul
del /q internal\service\permission\gorm_adapter.go 2>nul
del /q internal\service\permission\login_log_service.go 2>nul
del /q internal\service\permission\menu_service.go 2>nul
del /q internal\service\permission\README_MIGRATION.md 2>nul
del /q internal\service\permission\role_menus_service.go 2>nul
del /q internal\service\permission\role_service.go 2>nul
del /q internal\service\permission\user_service.go 2>nul

echo 权限模块清理完成！ 