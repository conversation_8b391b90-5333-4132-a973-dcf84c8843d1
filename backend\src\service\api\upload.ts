import { request } from '../request';
import { getToken } from "@/store/modules/auth/shared";
import {ApiResponse} from "@/types/https";
var BaseAPIURL=import.meta.env.VITE_SERVICE_BASE_URL

type Result = {
  code: number;
  data: any;
  message: string;
  success?: boolean;
};

// 进度回调接口
export interface UploadProgressCallback {
  onProgress?: (progress: number) => void;
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
  onComplete?: () => void;
}

// 上传配置接口
export interface UploadOptions {
  file: File;
  subDir?: string;
  timeout?: number;
  callbacks?: UploadProgressCallback;
  cancelToken?: { cancel: () => void };
}

export const getAsyncRoutes = () => {

  return request<Result>({
    method:"get",
    url:"/get-async-routes"
  });
};

export const uploadImage = (formData: FormData) => {
  return request<Result>({method: "post", url:"/upload/image",params:{
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    transformRequest: [function () {
      // 不做任何处理，让浏览器自动设置正确的Content-Type边界
      return formData;
    }]
  }});
};

export const uploadVideo = (formData: FormData) => {
  return request<Result>({method: "post", url:"/upload/video",params:{
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    transformRequest: [function () {
      // 不做任何处理，让浏览器自动设置正确的Content-Type边界
      return formData;
    }]
  }})
};

export const uploadPicture = (formData: FormData) => {
  return request<Result>({method: "post", url:"/upload/picture",params:{
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    transformRequest: [function () {
      // 不做任何处理，让浏览器自动设置正确的Content-Type边界
      return formData;
    }]
  }})
};
export const uploadFile = (formData: FormData) => {
  return request<Result>({method: "post", url:"/upload/file",params:{
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    transformRequest: [function () {
      // 不做任何处理，让浏览器自动设置正确的Content-Type边界
      return formData;
    }]
  }
})
  };

/**
 * 带进度回调的视频上传函数
 * @param options 上传配置项
 * @returns 可控制的上传Promise
 */
export const uploadVideoWithProgress = (options: UploadOptions): Promise<Result> => {
  const { file, subDir = 'videos', timeout = 30 * 60 * 1000, callbacks, cancelToken } = options;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sub_dir', subDir);

  // 创建可取消的控制器对象
  const abortController:{ abort: (() => void) | null} = { abort: null };
  if (cancelToken) {
    cancelToken.cancel = () => {
      if (abortController.abort) {
        abortController.abort();
      }
    };
  }

  // 调试信息
  console.log('开始上传视频', {
    fileName: file.name,
    fileSize: file.size,
    fileType: file.type,
    subDir
  });

  return new Promise((resolve, reject) => {
    // 大文件检查
    if (file.size > 500 * 1024 * 1024) { // 500MB
      console.error('文件过大:', file.size);
      const error = new Error(`文件大小超过500MB限制: ${Math.round(file.size / (1024 * 1024))}MB`);
      callbacks?.onError?.(error);
      callbacks?.onComplete?.();
      reject(error);
      return;
    }

    const xhr = new XMLHttpRequest();

    // 绑定abort方法
    abortController.abort = () => xhr.abort();

    // 设置超时
    xhr.timeout = timeout;

    // 监听进度
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        const percent = Math.round((e.loaded / e.total) * 100);
        console.log(`上传进度: ${percent}%`);
        callbacks?.onProgress?.(percent);
      }
    };

    // 请求完成
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          console.log('视频上传成功:', response);
          callbacks?.onSuccess?.(response);
          resolve(response);
        } catch (err) {
          console.error('解析响应失败:', err, xhr.responseText);
          callbacks?.onError?.(new Error('解析响应失败'));
          reject(new Error('解析响应失败'));
        }
      } else {
        const errorMsg = `上传失败，状态码: ${xhr.status}, 响应: ${xhr.responseText}`;
        console.error(errorMsg);
        const error = new Error(errorMsg);
        callbacks?.onError?.(error);
        reject(error);
      }
      callbacks?.onComplete?.();
    };

    // 请求错误
    xhr.onerror = (e) => {
      // 更详细的错误信息
      console.error('上传网络错误:', e);
      const errorDetails = {
        type: 'network_error',
        url: import.meta.env.VITE_SERVICE_BASE_URL+'/upload/video-progress',
        readyState: xhr.readyState,
        status: xhr.status,
        statusText: xhr.statusText
      };
      console.error('错误详情:', errorDetails);

      // 尝试使用普通上传作为备选方案
      console.log('尝试使用普通上传方式作为备选方案...');
      tryFallbackUpload(file, subDir, callbacks, resolve, reject);
    };

    // 请求超时
    xhr.ontimeout = () => {
      console.error('上传超时');
      const error = new Error('上传超时');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 请求中止
    xhr.onabort = () => {
      console.log('上传已取消');
      const error = new Error('上传已取消');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 完整的API路径
    const uploadUrl = BaseAPIURL+'/upload/video-progress';
    console.log('视频上传URL:', uploadUrl);

    // 打开连接
    xhr.open('POST', uploadUrl, true);

    // 设置认证头
    const token = getToken();
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    }

    // 可选：添加额外请求头帮助调试
    xhr.setRequestHeader('X-Client-Info', 'lyadmin-ui');

    // 发送请求
    try {
      xhr.send(formData);
    } catch (error) {
      console.error('发送请求失败:', error);
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    }
  });
};

// 备选上传方法：使用标准上传接口
function tryFallbackUpload(
  file: File,
  subDir: string,
  callbacks?: UploadProgressCallback,
  resolve?: (value: any) => void,
  reject?: (reason: any) => void
) {
  console.log('使用标准上传接口作为备选:', file.name);

  const formData = new FormData();
  formData.append('file', file);
  formData.append('sub_dir', subDir);

  callbacks?.onProgress?.(0); // 重置进度

  uploadVideo(formData)
    .then(response => {
      console.log('备选上传成功:', response);
      callbacks?.onSuccess?.(response);
      if (resolve) resolve(response);
    })
    .catch(error => {
      console.error('备选上传失败:', error);
      callbacks?.onError?.(error);
      if (reject) reject(error);
    })
    .finally(() => {
      callbacks?.onComplete?.();
    });
}

/**
 * 带进度回调的图片上传函数
 * @param options 上传配置项
 * @returns 可控制的上传Promise
 */
export const uploadImageWithProgress = (options: UploadOptions): Promise<Result> => {
  const { file, subDir = 'image', timeout = 60 * 1000, callbacks, cancelToken } = options;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sub_dir', subDir);

  // 创建可取消的控制器对象
  const abortController:{ abort: (() => void) | null} = { abort: null };
  if (cancelToken) {
    cancelToken.cancel = () => {
      if (abortController.abort) {
        abortController.abort();
      }
    };
  }

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // 绑定abort方法
    abortController.abort = () => xhr.abort();

    // 设置超时
    xhr.timeout = timeout;

    // 监听进度
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        const percent = Math.round((e.loaded / e.total) * 100);
        callbacks?.onProgress?.(percent);
      }
    };

    // 请求完成
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          callbacks?.onSuccess?.(response);
          resolve(response);
        } catch (err) {
          callbacks?.onError?.(new Error('解析响应失败'));
          reject(new Error('解析响应失败'));
        }
      } else {
        const error = new Error(`上传失败，状态码: ${xhr.status}`);
        callbacks?.onError?.(error);
        reject(error);
      }
      callbacks?.onComplete?.();
    };

    // 请求错误
    xhr.onerror = (e) => {
      console.error('图片上传网络错误:', e);
      const error = new Error('网络连接错误');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 请求超时
    xhr.ontimeout = () => {
      const error = new Error('上传超时');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 请求中止
    xhr.onabort = () => {
      const error = new Error('上传已取消');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 完整的API路径
    const uploadUrl = BaseAPIURL+'/upload/image';

    // 打开连接
    xhr.open('POST', uploadUrl, true);

    // 设置认证头
    const token = getToken();
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    }

    // 发送请求
    xhr.send(formData);
  });
};
/**
 * 带进度回调的图片上传函数
 * @param options 上传配置项
 * @returns 可控制的上传Promise
 */
export const uploadPictureWithProgress = (options: UploadOptions): Promise<Result> => {
  const { file, subDir = 'pictures', timeout = 60 * 1000, callbacks, cancelToken } = options;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sub_dir', subDir);

  // 创建可取消的控制器对象
  const abortController:{ abort: (() => void) | null} = { abort: null };
  if (cancelToken) {
    cancelToken.cancel = () => {
      if (abortController.abort) {
        abortController.abort();
      }
    };
  }

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // 绑定abort方法
    abortController.abort = () => xhr.abort();

    // 设置超时
    xhr.timeout = timeout;

    // 监听进度
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        const percent = Math.round((e.loaded / e.total) * 100);
        callbacks?.onProgress?.(percent);
      }
    };

    // 请求完成
    xhr.onload = () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          callbacks?.onSuccess?.(response);
          resolve(response);
        } catch (err) {
          callbacks?.onError?.(new Error('解析响应失败'));
          reject(new Error('解析响应失败'));
        }
      } else {
        const error = new Error(`上传失败，状态码: ${xhr.status}`);
        callbacks?.onError?.(error);
        reject(error);
      }
      callbacks?.onComplete?.();
    };

    // 请求错误
    xhr.onerror = (e) => {
      console.error('图片上传网络错误:', e);
      const error = new Error('网络连接错误');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 请求超时
    xhr.ontimeout = () => {
      const error = new Error('上传超时');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 请求中止
    xhr.onabort = () => {
      const error = new Error('上传已取消');
      callbacks?.onError?.(error);
      reject(error);
      callbacks?.onComplete?.();
    };

    // 完整的API路径
    const uploadUrl = BaseAPIURL+'/upload/picture-progress';

    // 打开连接
    xhr.open('POST', uploadUrl, true);

    // 设置认证头
    const token = getToken();
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    }

    // 发送请求
    xhr.send(formData);
  });
};

/**
 * 删除已上传的文件
 * @param filePath 文件路径，必须以/开头，例如/videos/filename.mp4
 * @returns 删除结果
 */
export const deleteUploadedFile = (filePath: string) => {
  if (!filePath) {
    return Promise.reject(new Error('文件路径不能为空'));
  }

  // 确保路径以/开头
  // const path = filePath.startsWith('/') ? filePath : `/${filePath}`;
   return request<ApiResponse<any>>({
    url: "/delete-file",
    method: "post",
    data: {
      path: filePath,
    },
  })
};
