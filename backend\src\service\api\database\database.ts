import { request } from "@/service/request";
import type {
  TableInfo,
  TableDetailResponse,
  MockDataRequest,
  MockDataResponse
} from '@/types/database';

/**
 * 获取数据库表列表
 */
export function getTableList() {
  return request<TableInfo[]>({ 
    url:   '/database/tables',
    method: 'post'
  });
}

/**
 * 获取表详情
 * @param tableName 表名
 */
export function getTableDetail(tableName: string) {
  return request<TableDetailResponse>({ 
    url: `/database/table/${tableName}`,
    method: 'post'
  });
}

/**
 * 通过POST方式获取表详情
 * @param params 请求参数
 */
export function getTableDetailByPost(params: { data: { table_name: string } }) {
  return request<TableDetailResponse>({
    url: '/database/table/detail',
    method: 'post',
    data: params
  });
}

/**
 * 生成Mock数据
 * @param params Mock数据生成参数
 */
export function generateMockData(params: { data: MockDataRequest }) {
  return request<MockDataResponse>({
    url: '/database/mock/generate',
    method: 'post',
    data: params
  });
}

/**
 * 插入Mock数据到数据库
 * @param params 插入参数
 */
export function insertMockData(params: { 
  data: { 
    table_name: string; 
    data: Record<string, any>[] 
  } 
}) {
  return request({
    url: '/database/mock/insert',
    method: 'post',
    data: params
  });
}

/**
 * 获取外键关联表的数据
 * @param params 外键查询参数
 */
export function getForeignKeyData(params: { 
  data: { 
    table_name: string; 
    column_name: string; 
    limit?: number;
  } 
}) {
  return request<{ values: string[] }>({
    url: '/database/foreign-key/data',
    method: 'post',
    data: params
  });
} 