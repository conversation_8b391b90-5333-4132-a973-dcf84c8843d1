/**
 * Material Design主题配置
 */
import { THEME_COLORS, ThemeConfig } from '@/config/theme.config';

// Material Design主题列表
export const materialThemes: ThemeConfig[] = [
    // Indigo
    {
        name: 'md-light-indigo',
        code: 'mdIndigo',
        displayName: 'Indigo',
        isDark: false,
        primary: THEME_COLORS.indigo.light,
        themeFamily: 'md',
        colorScheme: 'indigo'
    },
    {
        name: 'md-dark-indigo',
        code: 'mdIndigo',
        displayName: 'Indigo',
        isDark: true,
        primary: THEME_COLORS.indigo.dark,
        themeFamily: 'md',
        colorScheme: 'indigo'
    },

    // Deep Purple
    {
        name: 'md-light-deeppurple',
        code: 'mdDeeppurple',
        displayName: 'Deep Purple',
        isDark: false,
        primary: THEME_COLORS.deeppurple.light,
        themeFamily: 'md',
        colorScheme: 'deeppurple'
    },
    {
        name: 'md-dark-deeppurple',
        code: 'mdDeeppurple',
        displayName: 'Deep Purple',
        isDark: true,
        primary: THEME_COLORS.deeppurple.dark,
        themeFamily: 'md',
        colorScheme: 'deeppurple'
    },

    // Orange
    {
        name: 'md-light-orange',
        code: 'mdOrange',
        displayName: 'Orange',
        isDark: false,
        primary: THEME_COLORS.orange.light,
        themeFamily: 'md',
        colorScheme: 'orange'
    },
    {
        name: 'md-dark-orange',
        code: 'mdOrange',
        displayName: 'Orange',
        isDark: true,
        primary: THEME_COLORS.orange.dark,
        themeFamily: 'md',
        colorScheme: 'orange'
    },

    // Rose
    {
        name: 'md-light-rose',
        code: 'mdRose',
        displayName: 'Rose',
        isDark: false,
        primary: THEME_COLORS.rose.light,
        themeFamily: 'md',
        colorScheme: 'rose'
    },
    {
        name: 'md-dark-rose',
        code: 'mdRose',
        displayName: 'Rose',
        isDark: true,
        primary: THEME_COLORS.rose.dark,
        themeFamily: 'md',
        colorScheme: 'rose'
    },

    // Noir
    {
        name: 'md-light-noir',
        code: 'mdNoir',
        displayName: 'Noir',
        isDark: false,
        primary: THEME_COLORS.noir.light,
        themeFamily: 'md',
        colorScheme: 'noir'
    },
    {
        name: 'md-dark-noir',
        code: 'mdNoir',
        displayName: 'Noir',
        isDark: true,
        primary: THEME_COLORS.noir.dark,
        themeFamily: 'md',
        colorScheme: 'noir'
    }
]; 