# 用户登录日志控制器修复文档

## 问题描述

用户登录日志前端提交的数据格式与后端控制器不匹配，需要修改控制器和repository使其能够正确处理前端提交的查询参数。

## 前端提交的数据格式

```json
{
  "data": {
    "keyword": "",
    "user_id": "",
    "username": "",
    "login_status": "",
    "ip_address": "",
    "device_type": "",
    "location": "",
    "start_date": "",
    "end_date": ""
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 修改内容

### 1. 控制器修改 (`frontapi/internal/admin/users/user_login_log_controller.go`)

#### 修改前的问题：
- 使用`utils.GetRequestInfo(ctx)`而不是`c.GetRequestInfo(ctx)`
- 字段名不匹配：`userId`、`loginStatus`、`ip`、`deviceType`等
- 查询条件构建方式不符合新的架构

#### 修改后：
- 使用正确的`c.GetRequestInfo(ctx)`方法
- 映射前端字段名到正确的查询条件：
  - `user_id` → `user_id`
  - `login_status` → `login_status`
  - `ip_address` → `ip_address`
  - `device_type` → `device_type`
  - `start_date` → `start_date`
  - `end_date` → `end_date`
- 构建统一的查询条件map传递给service层

### 2. Repository修改 (`frontapi/internal/repository/users/user_login_logs_repository.go`)

#### 新增功能：
- 实现`ApplyConditions`方法，自定义查询条件应用逻辑
- 设置调用者实例，使基础仓库能够识别并使用子类的ApplyConditions方法

#### 支持的查询条件：
- `keyword`：关键字搜索（用户名、IP地址、位置、用户代理）
- `user_id`：用户ID精确匹配
- `username`：用户名模糊搜索（通过关联查询）
- `login_status`：登录状态精确匹配
- `ip_address`：IP地址模糊搜索
- `device_type`：设备类型模糊搜索
- `location`：地理位置模糊搜索
- `start_date`/`end_date`：登录时间范围查询
- `type`：登录类型（0登录，1退出）
- `login_type`：登录方式（password, token, oauth2等）

## 技术要点

### 查询条件映射
控制器将前端提交的字段名映射到repository期望的条件名：

```go
condition := map[string]interface{}{
    "keyword":      keyword,
    "user_id":      userId,
    "username":     username,
    "login_status": loginStatus,
    "ip_address":   ipAddress,
    "device_type":  deviceType,
    "location":     location,
    "start_date":   startDate,
    "end_date":     endDate,
}
```

### Repository条件应用
在repository的ApplyConditions方法中处理各种查询条件：

```go
// 关键字搜索
if condition["keyword"] != nil && condition["keyword"] != "" {
    keyword := condition["keyword"].(string)
    query = query.Where("user_id IN (SELECT id FROM ly_users WHERE username LIKE ? OR nickname LIKE ?) OR ip_address LIKE ? OR location LIKE ? OR user_agent LIKE ?",
        "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
}

// 时间范围查询
if condition["start_date"] != nil && condition["start_date"] != "" {
    startDate := condition["start_date"].(string)
    query = query.Where("login_time >= ?", startDate)
}
```

## 修改后的效果

1. 前端提交的查询参数能够正确被识别和处理
2. 支持多种查询条件的组合使用
3. 查询逻辑统一，符合现有架构规范
4. 关键字搜索支持多字段联合搜索
5. 时间范围查询支持灵活的日期筛选

## 测试建议

1. 测试空条件查询（获取全部登录日志）
2. 测试单一条件查询（如按用户ID查询）
3. 测试多条件组合查询
4. 测试关键字搜索功能
5. 测试时间范围查询
6. 测试分页功能

## 注意事项

1. 关键字搜索使用了子查询，对于大数据量可能需要优化
2. 时间格式需要确保前端传递的格式与数据库兼容
3. 所有字符串字段都支持模糊搜索（LIKE查询）
4. 调试日志已添加，便于排查查询条件问题 