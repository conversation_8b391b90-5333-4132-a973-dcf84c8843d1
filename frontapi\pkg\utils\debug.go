package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
)

// PrintJSON 美化打印JSON对象
func PrintJSON(prefix string, v interface{}) {
	if v == nil {
		fmt.Printf("%s: <nil>\n", prefix)
		return
	}

	// 获取值的类型信息
	t := reflect.TypeOf(v)
	kind := "<unknown>"
	if t != nil {
		kind = t.Kind().String()
	}

	jsonData, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		fmt.Printf("%s: [%s] Error: %v\n", prefix, kind, err)
		return
	}

	fmt.Printf("%s: [%s]\n%s\n", prefix, kind, string(jsonData))
}

// DebugStruct 输出结构体的详细信息
func DebugStruct(prefix string, v interface{}) {
	if v == nil {
		fmt.Printf("%s: <nil>\n", prefix)
		return
	}

	val := reflect.ValueOf(v)
	if val.Kind() == reflect.Ptr {
		if val.IsNil() {
			fmt.Printf("%s: <nil pointer>\n", prefix)
			return
		}
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		fmt.Printf("%s: Not a struct, but %s\n", prefix, val.Kind())
		return
	}

	typ := val.Type()
	fmt.Printf("%s: [%s]\n", prefix, typ.Name())

	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// 跳过未导出字段
		if !fieldType.IsExported() {
			continue
		}

		fieldName := fieldType.Name
		fieldKind := field.Kind()
		var fieldValue interface{}

		if field.CanInterface() {
			fieldValue = field.Interface()
		} else {
			fieldValue = "<cannot access>"
		}

		fmt.Printf("  %-20s %-12s = ", fieldName, fieldKind)

		switch fieldKind {
		case reflect.Struct:
			fmt.Printf("<struct>\n")
			DebugStruct(prefix+"  "+fieldName, fieldValue)
		case reflect.Map, reflect.Slice, reflect.Array:
			jsonData, err := json.MarshalIndent(fieldValue, "", "  ")
			if err != nil {
				fmt.Printf("<error: %v>\n", err)
			} else {
				fmt.Printf("%s\n", string(jsonData))
			}
		default:
			fmt.Printf("%v\n", fieldValue)
		}
	}
}
