<template>
  <div class="pagination">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  total: {
    type: Number,
    default: 0
  },
  current: {
    type: Number,
    default: 1
  },
  size: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['pagination']);

const currentPage = ref(props.current);
const pageSize = ref(props.size);

// 监听props变化
watch(() => props.current, (val) => {
  currentPage.value = val;
});

watch(() => props.size, (val) => {
  pageSize.value = val;
});

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  emitPagination();
};

// 处理每页数量变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  // 页码大小变化时，页码重置为1
  currentPage.value = 1;
  emitPagination();
};

// 触发分页事件
const emitPagination = () => {
  emit('pagination', {
    page: currentPage.value,
    pageSize: pageSize.value
  });
};
</script>

<style scoped>
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
