package videos

import (
	"frontapi/internal/api"
	videoSrv "frontapi/internal/service/videos"
	videoTypings "frontapi/internal/typings/videos"

	"github.com/gofiber/fiber/v2"
)

type VideoAlbumController struct {
	VideoAlbumService videoSrv.VideoAlbumService
	VideoService      videoSrv.VideoService
	api.BaseController
}

func NewVideoAlbumController(videoAlbumService videoSrv.VideoAlbumService, videoService videoSrv.VideoService) *VideoAlbumController {
	return &VideoAlbumController{
		VideoAlbumService: videoAlbumService,
		VideoService:      videoService,
	}
}

func (c *VideoAlbumController) GetAlbumList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	keyword := reqInfo.Get("keyword").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"keyword": keyword,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	//获取专辑列表
	albumList, total, err := c.VideoAlbumService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取专辑列表失败: "+err.Error())
	}
	albumListResponse := videoTypings.ConvertVideoAlbumListResponse(albumList, total, pageNo, pageSize)
	return c.Success(ctx, albumListResponse)
}

func (c *VideoAlbumController) GetAlbumDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	id := reqInfo.Get("id").GetString()
	album, err := c.VideoAlbumService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取专辑详情失败: "+err.Error())
	}
	albumDetailResponse := videoTypings.ConvertVideoAlbumInfo(album)
	return c.Success(ctx, albumDetailResponse)
}

func (c *VideoAlbumController) GetAlbumVideoList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	id := reqInfo.Get("id").GetString()
	keyword := reqInfo.Get("keyword").GetString()
	orderBy := reqInfo.Get("order_by").GetString()
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"album_id": id,
		"keyword":  keyword,
		"status":   1,
	}
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	videoList, total, err := c.VideoService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取专辑视频列表失败: "+err.Error())
	}
	videoListResponse := videoTypings.ConvertVideoListResponse(videoList, total, pageNo, pageSize)
	return c.Success(ctx, videoListResponse)
}
