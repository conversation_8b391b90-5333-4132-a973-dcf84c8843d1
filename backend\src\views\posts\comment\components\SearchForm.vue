<template>
    <div class="search-bar mb-4">
        <el-form :inline="true" :model="modelValue" class="flex flex-wrap gap-2">
            <el-form-item label="评论内容">
                <el-input v-model="modelValue.content" placeholder="请输入评论内容" clearable></el-input>
            </el-form-item>
            <el-form-item label="帖子ID">
                <el-input v-model="modelValue.post_id" placeholder="请输入帖子ID" clearable></el-input>
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="modelValue.status" placeholder="请选择状态" clearable style="width: 180px;">
                    <el-option label="待审核" :value="0"></el-option>
                    <el-option label="影藏" :value="1"></el-option>
                    <el-option label="显示" :value="2"></el-option>
                    <el-option label="审核拒绝" :value="-2"></el-option>
                    <el-option label="已删除" :value="-4"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="$emit('search')">搜索</el-button>
                <el-button @click="$emit('reset')">重置</el-button>
                <el-button @click="$emit('refresh')">刷新</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
defineProps({
    modelValue: {
        type: Object,
        required: true
    }
});

defineEmits(['search', 'reset', 'refresh']);
</script>

<style scoped>
.search-bar {
    margin-bottom: 16px;
}

.mb-4 {
    margin-bottom: 16px;
}
</style>
