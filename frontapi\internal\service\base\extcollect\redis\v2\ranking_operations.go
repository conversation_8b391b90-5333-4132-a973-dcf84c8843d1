package v2

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
)

// RankingOperations 排名操作处理器
type RankingOperations struct {
	client *RedisClient
}

// NewRankingOperations 创建排名操作处理器
func NewRankingOperations(client *RedisClient) *RankingOperations {
	return &RankingOperations{
		client: client,
	}
}

// UpdateHotRank 更新热门排行榜
func (r *RankingOperations) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	hotRankKey := r.client.hotRankKey(itemType)
	config := r.client.Config()

	_, err := r.client.Client().ZAdd(ctx, hotRankKey, &redis.Z{
		Score:  score,
		Member: itemID,
	}).Result()
	if err != nil {
		r.client.UpdateStats(0, 0, 1)
		return fmt.Errorf("更新热门排行榜失败: %w", err)
	}

	// 设置过期时间
	r.client.Client().Expire(ctx, hotRankKey, config.RankingTTL)

	r.client.UpdateStats(1, 0, 0)
	return nil
}

// GetHotRanking 获取热门排行榜
func (r *RankingOperations) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	result, err := r.client.Client().ZRevRange(ctx, hotRankKey, 0, int64(limit-1)).Result()
	if err != nil {
		if err == redis.Nil {
			r.client.UpdateStats(0, 1, 0)
			return []string{}, nil
		}
		r.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取热门排行榜失败: %w", err)
	}

	r.client.UpdateStats(1, 0, 0)
	return result, nil
}

// GetHotRankingWithScores 获取带分数的热门排行榜
func (r *RankingOperations) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	result, err := r.client.Client().ZRevRangeWithScores(ctx, hotRankKey, 0, int64(limit-1)).Result()
	if err != nil {
		if err == redis.Nil {
			r.client.UpdateStats(0, 1, 0)
			return map[string]float64{}, nil
		}
		r.client.UpdateStats(0, 0, 1)
		return nil, fmt.Errorf("获取带分数的热门排行榜失败: %w", err)
	}

	rankings := make(map[string]float64)
	for _, z := range result {
		if itemID, ok := z.Member.(string); ok {
			rankings[itemID] = z.Score
		}
	}

	r.client.UpdateStats(1, 0, 0)
	return rankings, nil
}

// GetItemRank 获取项目在排行榜中的排名
func (r *RankingOperations) GetItemRank(ctx context.Context, itemID, itemType string) (int64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	rank, err := r.client.Client().ZRevRank(ctx, hotRankKey, itemID).Result()
	if err != nil {
		if err == redis.Nil {
			r.client.UpdateStats(0, 1, 0)
			return -1, nil // 未找到排名
		}
		r.client.UpdateStats(0, 0, 1)
		return -1, fmt.Errorf("获取项目排名失败: %w", err)
	}

	r.client.UpdateStats(1, 0, 0)
	return rank + 1, nil // Redis的rank从0开始，所以+1
}

// GetItemScore 获取项目的热度分数
func (r *RankingOperations) GetItemScore(ctx context.Context, itemID, itemType string) (float64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	score, err := r.client.Client().ZScore(ctx, hotRankKey, itemID).Result()
	if err != nil {
		if err == redis.Nil {
			r.client.UpdateStats(0, 1, 0)
			return 0, nil
		}
		r.client.UpdateStats(0, 0, 1)
		return 0, fmt.Errorf("获取项目分数失败: %w", err)
	}

	r.client.UpdateStats(1, 0, 0)
	return score, nil
}

// RemoveFromRank 从排行榜中移除项目
func (r *RankingOperations) RemoveFromRank(ctx context.Context, itemID, itemType string) error {
	hotRankKey := r.client.hotRankKey(itemType)

	_, err := r.client.Client().ZRem(ctx, hotRankKey, itemID).Result()
	if err != nil {
		r.client.UpdateStats(0, 0, 1)
		return fmt.Errorf("从排行榜移除项目失败: %w", err)
	}

	r.client.UpdateStats(1, 0, 0)
	return nil
}

// GetRankingSize 获取排行榜大小
func (r *RankingOperations) GetRankingSize(ctx context.Context, itemType string) (int64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	size, err := r.client.Client().ZCard(ctx, hotRankKey).Result()
	if err != nil {
		r.client.UpdateStats(0, 0, 1)
		return 0, fmt.Errorf("获取排行榜大小失败: %w", err)
	}

	r.client.UpdateStats(1, 0, 0)
	return size, nil
}

// CleanupRanking 清理排行榜（保留前N名）
func (r *RankingOperations) CleanupRanking(ctx context.Context, itemType string, keepTop int) error {
	hotRankKey := r.client.hotRankKey(itemType)

	// 移除排名在keepTop之后的所有项目
	_, err := r.client.Client().ZRemRangeByRank(ctx, hotRankKey, 0, -(int64(keepTop) + 1)).Result()
	if err != nil {
		r.client.UpdateStats(0, 0, 1)
		return fmt.Errorf("清理排行榜失败: %w", err)
	}

	r.client.UpdateStats(1, 0, 0)
	return nil
}
