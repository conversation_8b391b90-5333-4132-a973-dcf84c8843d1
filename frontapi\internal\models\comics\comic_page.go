package comics

import (
	"frontapi/internal/models"
)

// ComicPage 漫画页面模型
type ComicPage struct {
	models.BaseModelStruct
	ChapterID  string `json:"chapter_id" gorm:"column:chapter_id;not null;index:idx_chapter_id;uniqueIndex:uk_chapter_page,priority:1" comment:"章节ID"`
	ComicId    string `json:"comic_id" gorm:"column:comic_id;not null;" comment:"漫画ID"`
	PageNumber int    `json:"page_number" gorm:"column:page_number;not null;uniqueIndex:uk_chapter_page,priority:2" comment:"页码"`
	ImageURL   string `json:"image_url" gorm:"column:image_url;not null" comment:"图片URL"`
	Width      int    `json:"width" gorm:"column:width" comment:"宽度"`
	Height     int    `json:"height" gorm:"column:height" comment:"高度"`
}

// TableName 指定表名
func (ComicPage) TableName() string {
	return "ly_comic_pages"
}
