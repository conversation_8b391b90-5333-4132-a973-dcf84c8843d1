<template>
  <div class="vertical-menu" :class="{ 'menu-collapsed': collapsed }">
    <div class="menu-items">
      <div
        v-for="item in menuItems"
        :key="item.label"
        class="menu-item"
        :class="{ 
          'has-submenu': item.items,
          'active': isActiveRoute(item.route),
          'submenu-open': openSubmenu === item.label
        }"
      >
        <!-- 主菜单项 -->
        <div
          class="menu-item-content"
          @click="(event) => handleMenuItemClick(item, event)"
          @mouseenter="(event) => handleMenuItemHover(item, event)"
          @mouseleave="() => handleMenuItemLeave(item)"
        >
          <router-link
            v-if="item.route && !item.items"
            :to="item.route"
            class="menu-link"
            :class="{ 'active': isActiveRoute(item.route) }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span v-if="!collapsed" class="menu-label">{{ t(item.label) }}</span>
          </router-link>
          <div
            v-else
            class="menu-link"
            :class="{ 'has-submenu': item.items }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span v-if="!collapsed" class="menu-label">{{ t(item.label) }}</span>
            <i 
              v-if="item.items && !collapsed" 
              class="pi pi-angle-right submenu-indicator"
              :class="{ 'rotated': openSubmenu === item.label }"
            ></i>
          </div>
        </div>

        <!-- 子菜单 CustomPopover (用于悬停模式) -->
        <CustomPopover
          v-if="item.items && (collapsed || hoverMode)"
          :ref="el => setPopoverRef(item.label, el)"
          placement="right-start"
          trigger="manual"
          :show-arrow="true"
          :offset="8"
          class="submenu-popover"
        >
          <template #trigger>
            <!-- 空的触发器，因为我们手动控制 -->
            <div style="display: none;"></div>
          </template>

          <div class="submenu-content">
            <div class="submenu-header">
              <span class="submenu-title">{{ t(item.label) }}</span>
            </div>
            <div class="submenu-items">
              <router-link
                v-for="subItem in item.items"
                :key="subItem.label"
                :to="subItem.route!"
                class="submenu-item-link"
                :class="{ 'active': isActiveRoute(subItem.route) }"
                @click="$emit('item-click', subItem)"
              >
                <span v-if="subItem.icon" class="submenu-icon">
                  <i :class="subItem.icon"></i>
                </span>
                <span class="submenu-label">{{ t(subItem.label) }}</span>
              </router-link>
            </div>
          </div>
        </CustomPopover>

        <!-- 子菜单展开 (用于展开模式) -->
        <div
          v-if="item.items && !collapsed && !hoverMode && openSubmenu === item.label"
          class="submenu-expanded"
        >
          <router-link
            v-for="subItem in item.items"
            :key="subItem.label"
            :to="subItem.route!"
            class="submenu-item-link"
            :class="{ 'active': isActiveRoute(subItem.route) }"
            @click="$emit('item-click', subItem)"
          >
            <span v-if="subItem.icon" class="submenu-icon">
              <i :class="subItem.icon"></i>
            </span>
            <span class="submenu-label">{{ t(subItem.label) }}</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import CustomPopover from './CustomPopover.vue';

// Props
interface Props {
  collapsed?: boolean;
  hoverMode?: boolean; // 悬停模式：true=悬停显示子菜单，false=点击展开子菜单
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  hoverMode: true
});

// Emits
interface Emits {
  'item-click': [item: any];
}

const emit = defineEmits<Emits>();

// Composables
const { t } = useTranslation();
const route = useRoute();

// State
const openSubmenu = ref('');
const hoverTimer = ref<NodeJS.Timeout | null>(null);
const popoverRefs = ref<Record<string, any>>({});

// Methods
const setPopoverRef = (label: string, el: any) => {
  if (el) {
    popoverRefs.value[label] = el;
  }
};

const isActiveRoute = (routePath?: string) => {
  if (!routePath) return false;
  return route.path === routePath || route.path.startsWith(routePath + '/');
};

const handleMenuItemClick = (item: any, event?: Event) => {
  if (item.items) {
    if (props.collapsed || props.hoverMode) {
      // 折叠模式或悬停模式：显示 Popover
      const popover = popoverRefs.value[item.label];
      if (popover && event) {
        popover.toggle(event);
      }
    } else {
      // 展开模式：切换子菜单展开状态
      openSubmenu.value = openSubmenu.value === item.label ? '' : item.label;
    }
  } else if (item.route) {
    emit('item-click', item);
  }
};

const handleMenuItemHover = (item: any, event?: Event) => {
  if (item.items && (props.collapsed || props.hoverMode)) {
    // 清除之前的定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }
    
    // 显示 Popover
    const popover = popoverRefs.value[item.label];
    if (popover && event) {
      popover.show(event);
    }
  }
};

const handleMenuItemLeave = (item: any) => {
  if (item.items && (props.collapsed || props.hoverMode)) {
    // 延迟隐藏 Popover
    hoverTimer.value = setTimeout(() => {
      const popover = popoverRefs.value[item.label];
      if (popover) {
        popover.hide();
      }
    }, 300);
  }
};
</script>

<style lang="scss" scoped>
.vertical-menu {
  width: 280px;
  background: var(--surface-card);
  border-right: 1px solid var(--surface-border);
  transition: width 0.3s ease;
  
  &.menu-collapsed {
    width: 60px;
  }
  
  .menu-items {
    .menu-item {
      position: relative;
      border-bottom: 1px solid var(--surface-border);
      
      &:last-child {
        border-bottom: none;
      }
      
      .menu-item-content {
        .menu-link {
          display: flex;
          align-items: center;
          padding: 1rem;
          color: var(--text-color);
          text-decoration: none;
          transition: all 0.2s ease;
          cursor: pointer;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            font-weight: 500;
            
            .menu-icon i {
              color: var(--primary-color);
            }
          }
          
          .menu-icon {
            width: 24px;
            margin-right: 0.75rem;
            color: var(--text-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            
            i {
              font-size: 1.1rem;
            }
          }
          
          .menu-label {
            flex: 1;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .submenu-indicator {
            color: var(--text-color-secondary);
            font-size: 0.8rem;
            transition: transform 0.2s ease;
            
            &.rotated {
              transform: rotate(90deg);
            }
          }
        }
      }
      
      // 展开的子菜单
      .submenu-expanded {
        background: var(--surface-ground);
        
        .submenu-item-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem 0.75rem 3rem;
          color: var(--text-color);
          text-decoration: none;
          transition: background 0.2s ease;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            
            .submenu-icon i {
              color: var(--primary-color);
            }
          }
          
          .submenu-icon {
            width: 20px;
            margin-right: 0.75rem;
            color: var(--text-color-secondary);
            
            i {
              font-size: 0.9rem;
            }
          }
          
          .submenu-label {
            font-size: 0.9rem;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Popover 子菜单样式
:deep(.submenu-popover) {
  .p-popover-content {
    padding: 0;
    min-width: 280px;
    
    .submenu-content {
      .submenu-header {
        padding: 0.75rem 1rem 0.5rem;
        border-bottom: 1px solid var(--surface-border);
        margin-bottom: 0.25rem;
        
        .submenu-title {
          font-size: 0.85rem;
          font-weight: 600;
          color: var(--text-color);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
      
      .submenu-items {
        padding: 0.25rem 0;
        
        .submenu-item-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem;
          color: var(--text-color);
          text-decoration: none;
          transition: background 0.2s ease;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            
            .submenu-icon i {
              color: var(--primary-color);
            }
          }
          
          .submenu-icon {
            width: 20px;
            margin-right: 0.75rem;
            color: var(--text-color-secondary);
            
            i {
              font-size: 0.9rem;
            }
          }
          
          .submenu-label {
            font-size: 0.9rem;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
