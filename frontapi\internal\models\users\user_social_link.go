package users

import (
	"frontapi/internal/models"
)

// UserSocialLink 用户社交链接表
type UserSocialLink struct {
	models.BaseModel
	UserID     string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                                // 用户ID
	Platform   string         `gorm:"column:platform;type:string;not null;comment:平台名称" json:"platform"`                                              // 平台名称
	URL        string         `gorm:"column:url;type:string;not null;comment:链接URL" json:"url"`                                                       // 链接URL
	IsVerified bool           `gorm:"column:is_verified;type:bool;default:false;comment:是否已验证" json:"is_verified"`                                    // 是否已验证
	Visibility string         `gorm:"column:visibility;type:string;default:'public';comment:可见性:public-公开,private-私密,friends-好友可见" json:"visibility"` // 可见性:public-公开,private-私密,friends-好友可见

}

// TableName 表名
func (UserSocialLink) TableName() string {
	return "ly_user_social_links"
}
