package vips

import (
	"context"
	"frontapi/internal/models/vips"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// VipPackageRepository VIP套餐仓库接口
type VipPackageRepository interface {
	base.ExtendedRepository[vips.VipPackage]
	// 业务特定方法
	FindByCode(ctx context.Context, code string) (*vips.VipPackage, error)
}

// vipPackageRepository VIP套餐仓库实现
type vipPackageRepository struct {
	base.ExtendedRepository[vips.VipPackage]
}

// NewVipPackageRepository 创建VIP套餐仓库实例
func NewVipPackageRepository(db *gorm.DB) VipPackageRepository {
	return &vipPackageRepository{
		ExtendedRepository: base.NewExtendedRepository[vips.VipPackage](db),
	}
}

// FindByCode 根据编码查找VIP套餐
func (r *vipPackageRepository) FindByCode(ctx context.Context, code string) (*vips.VipPackage, error) {
	condition := map[string]interface{}{"code": code}
	return r.FindOneByCondition(ctx, condition, "")
}
