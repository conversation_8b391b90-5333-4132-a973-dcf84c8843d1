<template>
    <footer class="bg-[linear-gradient(135deg,var(--surface-100),var(--surface-200))] dark:bg-[linear-gradient(135deg,var(--surface-800),var(--surface-900))] border-t border-surface-200 dark:border-surface-700 pt-12 pb-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                <!-- 关于我们 -->
                <div class="footer-section">
                    <h3 class="text-lg font-semibold mb-4 text-surface-950 dark:text-surface-50">{{ t('common.aboutUs') }}</h3>
                    <ul class="space-y-2">
                        <li><router-link to="/about" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.company') }}</router-link></li>
                        <li><router-link to="/contact" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.contactUs') }}</router-link></li>
                        <li><router-link to="/jobs" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.joinUs') }}</router-link></li>
                    </ul>
                </div>

                <!-- 帮助中心 -->
                <div class="footer-section">
                    <h3 class="text-lg font-semibold mb-4  dark:text-surface-50">{{ t('common.helpCenter') }}</h3>
                    <ul class="space-y-2">
                        <li><router-link to="/faq" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.faq') }}</router-link></li>
                        <li><router-link to="/feedback" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.feedback') }}</router-link></li>
                        <li><router-link to="/report" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.report') }}</router-link></li>
                    </ul>
                </div>

                <!-- 商务合作 -->
                <div class="footer-section">
                    <h3 class="text-lg font-semibold mb-4 text-surface-950 dark:text-surface-50">{{ t('common.business') }}</h3>
                    <ul class="space-y-2">
                        <li><router-link to="/business" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.advertising') }}</router-link></li>
                        <li><router-link to="/cooperation" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.cooperation') }}</router-link></li>
                        <li><router-link to="/creator" class="text-surface-850 dark:text-surface-100 hover:text-primary dark:hover:text-primary transition-colors">{{ t('common.creator') }}</router-link></li>
                    </ul>
                </div>

                <!-- 关注我们 -->
                <div class="footer-section">
                    <h3 class="text-lg font-semibold mb-4 text-surface-950 dark:text-surface-50">{{ t('common.followUs') }}</h3>
                    <div class="flex space-x-3">
                        <a href="#" class="w-10 h-10 rounded-full bg-surface-200 dark:bg-surface-800 flex items-center justify-center text-surface-850 dark:text-surface-100 hover:bg-primary hover:text-white transition-colors">
                            <i class="pi pi-facebook"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-surface-200 dark:bg-surface-800 flex items-center justify-center text-surface-850 dark:text-surface-100 hover:bg-primary hover:text-white transition-colors">
                            <i class="pi pi-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-surface-200 dark:bg-surface-800 flex items-center justify-center text-surface-850 dark:text-surface-100 hover:bg-primary hover:text-white transition-colors">
                            <i class="pi pi-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-surface-200 dark:bg-surface-800 flex items-center justify-center text-surface-850 dark:text-surface-100 hover:bg-primary hover:text-white transition-colors">
                            <i class="pi pi-github"></i>
                        </a>
                    </div>
                    
                    <!-- 订阅通讯 -->
                    <div class="mt-6">
                        <h4 class="text-sm font-medium mb-2 text-surface-700 dark:text-surface-200">{{ t('common.newsletter') }}</h4>
                        <div class="flex">
                            <InputText 
                                placeholder="Email" 
                                class="flex-1 rounded-r-none" 
                            />
                            <Button 
                                icon="pi pi-send" 
                                class="p-button-primary rounded-l-none"
                            />
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 设置区域 -->
            <div class="flex flex-wrap justify-center gap-6 py-6 border-t border-b border-surface-200 dark:border-surface-700">
                <div class="flex items-center">
                    <i class="pi pi-palette mr-2 text-surface-850 dark:text-surface-400"></i>
                    <FooterThemeSelector />
                </div>
                
                <div class="flex items-center">
                    <i class="pi pi-globe mr-2 text-surface-850 dark:text-surface-400"></i>
                    <FooterLanguageSelector />
                </div>
            </div>
            
            <!-- 版权信息 -->
            <div class="mt-6 text-center text-sm text-surface-500 dark:text-surface-400">
                <p>&copy; {{ new Date().getFullYear() }} MyFirm. {{ t('common.allRightsReserved') }}</p>
                <div class="mt-2 flex justify-center space-x-4">
                    <router-link to="/privacy" class="hover:text-primary transition-colors">{{ t('common.privacy') }}</router-link>
                    <router-link to="/terms" class="hover:text-primary transition-colors">{{ t('common.terms') }}</router-link>
                    <router-link to="/cookies" class="hover:text-primary transition-colors">{{ t('common.cookies') }}</router-link>
                </div>
            </div>
        </div>
    </footer>
</template>

<script setup lang="ts">
import { useTranslation } from '@/core/plugins/i18n/composables';
import FooterLanguageSelector from '@/shared/components/LanguageSelector/FooterLanguageSelector.vue';
import FooterThemeSelector from '@/shared/components/ThemeSelector/FooterThemeSelector.vue';
import { useLocaleStore } from '@/store/modules/locale';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import { computed, ref } from 'vue';

const { t } = useTranslation();

// 语言设置
const currentLanguage = computed(() => useLocaleStore().getLocale());
const selectedLanguage = ref(currentLanguage.value);

console.log('localeStore.getAllLocales()',useLocaleStore().getAllLocales());
;
</script>

<style scoped lang="scss">
.app-footer {
    background: var(--footer-bg, var(--surface-section));
    color: var(--footer-text, var(--text-color-secondary));
    padding: 2rem 1rem;
    margin-top: 2rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--footer-text, var(--pimary-text-color));
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-link {
    display: block;
    padding: 0.5rem 0;
    color: var(--footer-text, var(--text-color-secondary));
    text-decoration: none;
    transition: color 0.2s;
    
    &:hover {
        color: var(--primary-color);
    }
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--surface-card);
    color: var(--footer-text, var(--text-color-secondary));
    text-decoration: none;
    transition: all 0.2s;
    
    &:hover {
        background-color: var(--primary-color);
        color: var(--primary-color-text);
    }
    
    i {
        font-size: 1rem;
    }
}

.footer-settings-section {
    padding: 1.5rem 0;
    border-top: 1px solid var(--surface-border);
    border-bottom: 1px solid var(--surface-border);
    margin-bottom: 1.5rem;
}

.footer-section-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--footer-text, var(--pimary-text-color));
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.settings-content {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 2rem;
}

.setting-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.language-selector-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.language-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--footer-text, var(--text-color-secondary));
}

.language-icon {
    font-size: 1rem;
}

.language-title {
    font-weight: 500;
}

.language-dropdown {
    min-width: 150px;
}

.language-option-display,
.language-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.language-flag {
    font-size: 1.2rem;
}

.language-name {
    font-size: 0.9rem;
}

.footer-bottom {
    padding-top: 1rem;
    text-align: center;
}

.copyright {
    font-size: 0.9rem;
    color: var(--footer-text, var(--text-color-secondary));
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-section {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .settings-content {
        flex-direction: column;
        gap: 1.5rem;
    }
}
</style>