package books

import (
	"time"

	"frontapi/internal/models"
)

// Chapter 章节模型
type BookChapter struct {
	models.BaseModel
	BookID        string    `json:"book_id" gorm:"type:varchar(36);not null;index;comment:书籍ID"`
	BookName      string    `json:"book_name" gorm:"type:varchar(255);not null;comment:书籍名称"`
	Title         string    `json:"title" gorm:"type:varchar(255);not null;comment:章节标题"`
	ChapterNumber int       `json:"chapter_number" gorm:"not null;comment:章节序号"`
	SortOrder     int       `json:"sort_order" gorm:"not null;default:0;comment:排序"`
	Content       string    `json:"content" gorm:"type:longtext;comment:章节内容"`
	WordCount     int       `json:"word_count" gorm:"default:0;comment:字数"`
	ReadCount     int       `json:"read_count" gorm:"default:0;comment:阅读次数"`
	IsLocked      int       `json:"is_locked" gorm:"default:0;comment:是否锁定 0-否 1-是"`
	Price         float64   `json:"price" gorm:"type:decimal(10,2);default:0.00;comment:价格"`
	CreateTime    time.Time `json:"create_time" gorm:"autoCreateTime;comment:创建时间"`
	UpdateTime    time.Time `json:"update_time" gorm:"autoUpdateTime;comment:更新时间"`
	Status        int       `json:"status" gorm:"default:1;comment:状态 0-禁用 1-启用"`
}

// TableName 设置表名
func (BookChapter) TableName() string {
	return "ly_book_chapters"
}
