package integral

import (
	"frontapi/internal/models"
)

type PointLevel struct {
	models.BaseModel
	Level     int    `json:"level" gorm:"not null;comment:等级"`
	Name      string `json:"name" gorm:"type:string;size:50;not null;comment:等级名称"`
	MinPoints int    `json:"min_points" gorm:"not null;comment:最低积分要求"`
	MaxPoints *int   `json:"max_points" gorm:"comment:最高积分限制"`
	Icon      string `json:"icon" gorm:"type:string;size:255;comment:等级图标"`
	Benefits  string `json:"benefits" gorm:"type:string;comment:等级权益"`
	Color     string `json:"color" gorm:"type:string;size:20;comment:等级颜色"`
}

func (PointLevel) TableName() string {
	return "ly_point_levels"
}
