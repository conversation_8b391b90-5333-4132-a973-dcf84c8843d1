package comics

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// Comic 漫画模型
type Comic struct {
	models.ContentBaseModel
	Cover         string            `json:"cover" gorm:"column:cover;not null" comment:"封面图URL"`
	Rating        float64           `json:"rating" gorm:"column:rating;default:0.0" comment:"评分"`
	Author        string            `json:"author" gorm:"column:author" comment:"作者"`
	Progress      string            `json:"progress" gorm:"column:progress;default:ongoing" comment:"状态：ongoing-连载中，completed-已完结"`
	Tags          types.StringArray `json:"tags" gorm:"column:tags;type:longtext" comment:"标签JSON数据"`
	Popularity    uint64            `json:"popularity" gorm:"column:popularity;default:0" comment:"人气值"`
	ChapterCount  int               `json:"chapter_count" gorm:"column:chapter_count;default:0" comment:"章节数"`
	ReadCount     uint64            `json:"read_count" gorm:"column:read_count;default:0" comment:"阅读数"`
	Score         float64           `json:"score" gorm:"column:score;default:0.0" comment:"评分"`
	FavoriteCount uint64            `json:"favorite_count" gorm:"column:favorite_count;default:0" comment:"收藏数"`
	IsAdult       int8              `json:"is_adult" gorm:"column:is_adult;default:0" comment:"是否成人内容：0-否，1-是"`
	IsPaid        int8              `json:"is_paid" gorm:"column:is_paid;default:0" comment:"是否付费：0-免费，1-付费"`
	IsFeatured    int8              `json:"is_featured" gorm:"column:is_featured;default:0" comment:"是否推荐"`
	Price         float64           `json:"price" gorm:"column:price;default:0.00" comment:"价格"`
	PublishDate   types.JSONTime    `json:"publish_date" gorm:"column:publish_date" comment:"发布日期"`
	UpdateTime    types.JSONTime    `json:"update_time" gorm:"column:update_time;default:CURRENT_TIMESTAMP" comment:"更新时间"`
	Chapters      []*ComicChapter   `json:"chapters" gorm:"-"`
}

// TableName 指定表名
func (Comic) TableName() string {
	return "ly_comics"
}
