package cache

import "time"

// RedisConfig Redis配置
type RedisConfig struct {
	Name         string        `mapstructure:"name"`           // 适配器名称
	Host         string        `mapstructure:"host"`           // 主机地址
	Port         int           `mapstructure:"port"`           // 端口
	Password     string        `mapstructure:"password"`       // 密码
	DefaultTTL   time.Duration `mapstructure:"default_ttl"`    // 默认过期时间
	DB           int           `mapstructure:"db"`             // 数据库索引
	Prefix       string        `mapstructure:"prefix"`         // 键前缀
	PoolSize     int           `mapstructure:"pool_size"`      // 连接池大小
	MinIdleConns int           `mapstructure:"min_idle_conns"` // 最小空闲连接数
	IdleTimeout  int           `mapstructure:"idle_timeout"`   // 空闲连接超时时间(秒)
	MaxRetries   int           `mapstructure:"max_retries"`    // 最大重试次数
	DialTimeout  int           `mapstructure:"dial_timeout"`   // 拨号超时时间(秒)
	ReadTimeout  int           `mapstructure:"read_timeout"`   // 读取超时时间(秒)
	WriteTimeout int           `mapstructure:"write_timeout"`  // 写入超时时间(秒)
	Cluster      struct {
		Enable bool          `mapstructure:"enable"` // 是否使用集群模式
		Nodes  []ClusterNode `mapstructure:"nodes"`  // 集群节点列表
	} `mapstructure:"cluster"` // 是否使用集群模式
	Addrs []string `mapstructure:"addrs"` // 集群地址列表
}
