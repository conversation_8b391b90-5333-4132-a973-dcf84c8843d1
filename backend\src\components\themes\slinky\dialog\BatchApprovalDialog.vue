<template>
  <el-dialog 
    :model-value="visible" 
    :title="title || '批量内容审核'" 
    width="500px" 
    @close="handleClose"
    destroy-on-close
  >
    <div class="batch-approval-form">
      <div class="selected-info">
        <el-alert 
          type="info" 
          :closable="false"
        >
          <div class="selected-count">
            <el-icon><InfoFilled /></el-icon>
            <span>已选择 <strong>{{ itemCount }}</strong> 个项目进行审核</span>
          </div>
        </el-alert>
      </div>

      <div class="approval-options mt-4">
        <el-form :model="form" label-width="80px">
          <el-form-item label="审核结果">
            <el-radio-group v-model="form.status">
              <el-radio :value="2">批量通过</el-radio>
              <el-radio :value="-2">批量拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            v-if="form.status === -2"
            label="拒绝原因" 
            :rules="[{ required: true, message: '请填写拒绝原因', trigger: 'blur' }]"
          >
            <el-input 
              v-model="form.reason" 
              type="textarea" 
              placeholder="请输入拒绝原因，将应用于所有选中项目" 
              :rows="3"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref, watch } from 'vue';

interface Props {
  visible: boolean;
  title?: string;
  itemCount: number;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '批量内容审核',
  itemCount: 0,
  loading: false
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'confirm': [data: { status: number; reason: string }];
}>();

const form = ref({
  status: 2, // 2: 通过, -2: 拒绝
  reason: ''
});

watch(() => props.visible, (val) => {
  if (val) {
    form.value.status = 2;
    form.value.reason = '';
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleConfirm = () => {
  if (form.value.status === -2 && !form.value.reason) {
    ElMessage.error('请填写拒绝原因');
    return;
  }
  
  emit('confirm', { 
    status: form.value.status, 
    reason: form.value.reason
  });
};
</script>

<style scoped lang="scss">
.batch-approval-form {
  .selected-info {
    margin-bottom: 20px;
    
    .selected-count {
      display: flex;
      align-items: center;
      gap: 8px;
      
      strong {
        font-weight: 600;
      }
    }
  }
  
  .approval-options {
    padding: 0 10px;
  }
}

.mt-4 {
  margin-top: 16px;
}
</style> 