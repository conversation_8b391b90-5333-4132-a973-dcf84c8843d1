/* Add this to your global CSS */
.column-dragging {
    background-color: var(--el-color-primary-light-9) !important;
    border-left: 2px solid var(--el-color-primary) !important;
    border-right: 2px solid var(--el-color-primary) !important;
}

/* For Element Plus tables, we need to style both th and related td cells */
.el-table th.column-dragging,
.el-table td.column-dragging {
    background-color: var(--el-color-primary-light-9) !important;
}

/* Drag and drop styles */
.row-dragging {
    opacity: 0.5;
    background-color: #f0f9eb !important;
}

.row-over {
    border-top: 2px solid #67c23a !important;
}

.column-dragging {
    opacity: 0.5;
    background-color: #f0f9eb !important;
}

.column-over {
    border-left: 2px solid #67c23a !important;
}

.drag-handle {
    cursor: move;
    color: #909399;
    display: inline-flex;
    align-items: center;
    margin-right: 5px;
}

/* Make sure all draggable rows have the move cursor */
[draggable="true"] {
    cursor: move;
}

/* Add styles for sort mode */
.sort-mode-tip {
    margin-bottom: 16px;
}

// Import dialog z-index fixes
@import './fixed-dialogs.scss';

// Import SCSS partials
@import './scss/global.scss';
@import './scss/scrollbar.scss';
@import './scss/element-plus.scss';
@import './scss/table.scss';