package v2

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"

	"frontapi/internal/service/base/extlike/types"
)

// RankingOperations 排行榜操作处理器
type RankingOperations struct {
	client *RedisClient
	stats  *AdapterStats
}

// NewRankingOperations 创建排行榜操作处理器
func NewRankingOperations(client *RedisClient, stats *AdapterStats) *RankingOperations {
	return &RankingOperations{
		client: client,
		stats:  stats,
	}
}

// UpdateHotRank 更新热门排行榜
func (r *RankingOperations) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	hotRankKey := r.client.hotRankKey(itemType)

	// 使用 ZSet 存储热门排行
	err := r.client.client.ZAdd(ctx, hotRankKey, &redis.Z{
		Score:  score,
		Member: itemID,
	}).Err()

	if err != nil {
		r.stats.ErrorCount++
		return fmt.Errorf("更新热门排行失败: %w", err)
	}

	// 设置过期时间
	err = r.client.client.Expire(ctx, hotRankKey, r.client.config.RankingTTL).Err()
	if err != nil {
		// 过期时间设置失败不影响主要功能
		// 记录但不返回错误
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return nil
}

// GetHotRanking 获取热门排行榜（只返回ID列表）
func (r *RankingOperations) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	// 获取分数最高的前N个成员
	result, err := r.client.client.ZRevRange(ctx, hotRankKey, 0, int64(limit-1)).Result()
	if err != nil {
		if err == redis.Nil {
			r.stats.MissCount++
			r.stats.updateHitRate()
			return []string{}, nil
		}
		r.stats.ErrorCount++
		return nil, fmt.Errorf("获取热门排行失败: %w", err)
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return result, nil
}

// GetHotRankingWithScores 获取带分数的热门排行榜
func (r *RankingOperations) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	// 获取分数最高的前N个成员及其分数
	result, err := r.client.client.ZRevRangeWithScores(ctx, hotRankKey, 0, int64(limit-1)).Result()
	if err != nil {
		if err == redis.Nil {
			r.stats.MissCount++
			r.stats.updateHitRate()
			return make(map[string]float64), nil
		}
		r.stats.ErrorCount++
		return nil, fmt.Errorf("获取热门排行（带分数）失败: %w", err)
	}

	// 转换结果
	ranking := make(map[string]float64)
	for _, z := range result {
		if itemID, ok := z.Member.(string); ok {
			ranking[itemID] = z.Score
		}
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return ranking, nil
}

// GetTrendingItems 获取趋势项目
func (r *RankingOperations) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error) {
	trendKey := r.client.trendKey(itemType)

	// 如果有时间范围，使用时间范围查询
	var result []redis.Z
	var err error

	if timeRange != nil {
		// 使用分数范围查询（基于时间戳）
		min := float64(timeRange.Start.Unix())
		max := float64(timeRange.End.Unix())

		result, err = r.client.client.ZRangeByScoreWithScores(ctx, trendKey, &redis.ZRangeBy{
			Min:   fmt.Sprintf("%f", min),
			Max:   fmt.Sprintf("%f", max),
			Count: int64(limit),
		}).Result()
	} else {
		// 获取最新的趋势项目
		result, err = r.client.client.ZRevRangeWithScores(ctx, trendKey, 0, int64(limit-1)).Result()
	}

	if err != nil {
		if err == redis.Nil {
			r.stats.MissCount++
			r.stats.updateHitRate()
			return []*types.LikeTrend{}, nil
		}
		r.stats.ErrorCount++
		return nil, fmt.Errorf("获取趋势项目失败: %w", err)
	}

	// 转换结果
	var trends []*types.LikeTrend
	for i, z := range result {
		if itemID, ok := z.Member.(string); ok {
			trend := &types.LikeTrend{
				ItemID:   itemID,
				ItemType: itemType,
				Score:    z.Score,
				Rank:     i + 1,
				Date:     time.Unix(int64(z.Score), 0),
			}

			// 获取点赞数量
			if count, err := r.GetLikeCountForTrend(ctx, itemID, itemType); err == nil {
				trend.LikeCount = count
			}

			trends = append(trends, trend)
		}
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return trends, nil
}

// GetLikeCountForTrend 为趋势分析获取点赞数量
func (r *RankingOperations) GetLikeCountForTrend(ctx context.Context, itemID, itemType string) (int64, error) {
	countKey := r.client.countKey(itemType, itemID)

	count, err := r.client.client.Get(ctx, countKey).Int64()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		return 0, err
	}

	return count, nil
}

// RemoveFromHotRank 从热门排行榜中移除项目
func (r *RankingOperations) RemoveFromHotRank(ctx context.Context, itemID, itemType string) error {
	hotRankKey := r.client.hotRankKey(itemType)

	err := r.client.client.ZRem(ctx, hotRankKey, itemID).Err()
	if err != nil {
		r.stats.ErrorCount++
		return fmt.Errorf("从热门排行榜移除项目失败: %w", err)
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return nil
}

// GetItemRank 获取项目在排行榜中的排名
func (r *RankingOperations) GetItemRank(ctx context.Context, itemID, itemType string) (int64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	// 获取排名（从高到低）
	rank, err := r.client.client.ZRevRank(ctx, hotRankKey, itemID).Result()
	if err != nil {
		if err == redis.Nil {
			r.stats.MissCount++
			r.stats.updateHitRate()
			return -1, nil // 不在排行榜中
		}
		r.stats.ErrorCount++
		return -1, fmt.Errorf("获取项目排名失败: %w", err)
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return rank + 1, nil // Redis排名从0开始，这里转换为从1开始
}

// GetItemScore 获取项目在排行榜中的分数
func (r *RankingOperations) GetItemScore(ctx context.Context, itemID, itemType string) (float64, error) {
	hotRankKey := r.client.hotRankKey(itemType)

	score, err := r.client.client.ZScore(ctx, hotRankKey, itemID).Result()
	if err != nil {
		if err == redis.Nil {
			r.stats.MissCount++
			r.stats.updateHitRate()
			return 0, nil
		}
		r.stats.ErrorCount++
		return 0, fmt.Errorf("获取项目分数失败: %w", err)
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return score, nil
}

// BatchUpdateHotRank 批量更新热门排行榜
func (r *RankingOperations) BatchUpdateHotRank(ctx context.Context, itemType string, items map[string]float64) error {
	if len(items) == 0 {
		return nil
	}

	hotRankKey := r.client.hotRankKey(itemType)
	pipe := r.client.Pipeline()

	// 批量添加到排行榜
	for itemID, score := range items {
		pipe.ZAdd(ctx, hotRankKey, &redis.Z{
			Score:  score,
			Member: itemID,
		})
	}

	// 设置过期时间
	pipe.Expire(ctx, hotRankKey, r.client.config.RankingTTL)

	_, err := pipe.Exec(ctx)
	if err != nil {
		r.stats.ErrorCount++
		return fmt.Errorf("批量更新热门排行失败: %w", err)
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return nil
}

// CleanupOldRankings 清理过期的排行榜数据
func (r *RankingOperations) CleanupOldRankings(ctx context.Context, itemType string, before time.Time) error {
	hotRankKey := r.client.hotRankKey(itemType)

	// 移除分数小于指定时间戳的成员
	maxScore := float64(before.Unix())

	err := r.client.client.ZRemRangeByScore(ctx, hotRankKey, "-inf", fmt.Sprintf("(%f", maxScore)).Err()
	if err != nil {
		r.stats.ErrorCount++
		return fmt.Errorf("清理过期排行榜数据失败: %w", err)
	}

	r.stats.HitCount++
	r.stats.updateHitRate()
	return nil
}
