<template>
    <div class="app-container">
        <el-card>
            <template v-slot:header>
                <div class="filter-container">
                    <div class="title-container">
                        <h2>图片分类管理</h2>
                        <div class="buttons">
                            <el-button type="primary" :icon="Plus" @click="handleAdd">添加分类</el-button>
                            <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
                            <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 使用分类搜索组件 -->
            <CategorySearchBar v-model="searchForm" @search="handleSearch" @reset="handleReset" @refresh="refreshList"
                class="mb-4" />

            <!-- 使用分类表格组件 -->
            <CategoryTable :category-list="categoryList" :loading="loading" :pagination="pagination"
                @selection-change="handleSelectionChange" @view="handleView" @edit="handleEdit" @delete="handleDelete"
                @change-status="handleChangeStatus" @current-change="handleCurrentChange"
                @size-change="handleSizeChange" @batch-status="handleBatchStatus" @batch-delete="handleBatchDelete" />

            <!-- 对话框组件 -->
            <CategoryFormDialog :visible="dialogVisible" :type="dialogType" :category-data="currentCategory"
                @update:visible="(val: boolean) => dialogVisible = val" @success="handleDialogSuccess" />

            <CategoryDetailDialog :visible="detailDialogVisible" :category-data="currentCategory"
                @update:visible="(val: boolean) => detailDialogVisible = val" @edit="handleEdit" />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import {
    batchDeletePictureCategory,
    batchUpdatePictureCategoryStatus,
    deletePictureCategory,
    getPictureCategoryList,
    updatePictureCategoryStatus
} from '@/service/api/pictures/pictures';
import type { PictureCategory } from '@/types/pictures';
import { handleApiError } from '@/utils/errorHandler';
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onBeforeMount, reactive, ref } from 'vue';

// 导入分类管理组件
import CategoryDetailDialog from './components/CategoryDetailDialog.vue';
import CategoryFormDialog from './components/CategoryFormDialog.vue';
import CategorySearchBar from './components/CategorySearchBar.vue';
import CategoryTable from './components/CategoryTable.vue';

// 响应式数据
const loading = ref(false);
const categoryList = ref<PictureCategory[]>([]);
const selectedRows = ref<PictureCategory[]>([]);
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const currentCategory = ref<PictureCategory | null>(null);

// 搜索表单
const searchForm = reactive({
    keyword: '',
    status: undefined as number | undefined,
    start_date: '',
    end_date: ''
});

// 分页数据
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
});

// 在组件挂载前初始化
onBeforeMount(() => {
    // 确保在组件加载之前获取数据
    getList();
});

// 获取分类列表
const getList = async () => {
    loading.value = true;
    try {
        const params = {
            data: {
                keyword: searchForm.keyword,
                status: searchForm.status,
                created_at_start: searchForm.start_date,
                created_at_end: searchForm.end_date
            },
            page: {
                pageNo: pagination.page,
                pageSize: pagination.pageSize
            }
        };

        const { response, data } = await getPictureCategoryList(params) as any;

        if (response.status === 200 && response.data.code === 2000) {
            categoryList.value = data.list || [];
            pagination.total = data.total || 0;
        } else {
            handleApiError(response.message || '获取分类列表失败');
        }
    } catch (error) {
        handleApiError(error, '获取分类列表失败');
    } finally {
        loading.value = false;
    }
};

// 搜索处理
const handleSearch = (searchParams?: any) => {
    if (searchParams) {
        Object.assign(searchForm, searchParams);
    }
    pagination.page = 1;
    getList();
};

// 重置搜索
const handleReset = () => {
    Object.assign(searchForm, {
        keyword: '',
        status: undefined,
        start_date: '',
        end_date: ''
    });
    pagination.page = 1;
    getList();
};

// 刷新列表
const refreshList = () => {
    getList();
};

// 表格选择变化
const handleSelectionChange = (selection: PictureCategory[]) => {
    selectedRows.value = selection;
};

// 分页处理
const handleCurrentChange = (page: number) => {
    pagination.page = page;
    getList();
};

const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.page = 1;
    getList();
};

// 添加分类
const handleAdd = () => {
    dialogType.value = 'add';
    currentCategory.value = null;
    dialogVisible.value = true;
};

// 查看分类
const handleView = (row: PictureCategory) => {
    currentCategory.value = row;
    detailDialogVisible.value = true;
};

// 编辑分类
const handleEdit = (row: PictureCategory) => {
    dialogType.value = 'edit';
    currentCategory.value = { ...row };
    dialogVisible.value = true;
};

// 删除分类
const handleDelete = (row: PictureCategory) => {
    ElMessageBox.confirm('确认删除此分类吗？此操作不可恢复！', '删除确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await deletePictureCategory(row.id);
            ElMessage.success('删除分类成功');
            getList();
        } catch (error) {
            handleApiError(error, '删除分类失败');
        }
    }).catch(() => {
        // 用户取消，不做任何处理
    });
};

// 更改分类状态
const handleChangeStatus = async (id: string, status: number) => {
    try {
        await updatePictureCategoryStatus({
            id,
            status
        });
        ElMessage.success(`${status === 1 ? '启用' : '禁用'}分类成功`);
        getList();
    } catch (error) {
        handleApiError(error, '更新分类状态失败');
    }
};

// 批量处理状态
const handleBatchStatus = async (status: number) => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要操作的分类');
        return;
    }

    const ids = selectedRows.value.map(item => item.id);
    try {
        await batchUpdatePictureCategoryStatus({
            ids,
            status
        });
        ElMessage.success(`批量${status === 1 ? '启用' : '禁用'}分类成功`);
        getList();
    } catch (error) {
        handleApiError(error, '批量更新分类状态失败');
    }
};

// 批量删除
const handleBatchDelete = async () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的分类');
        return;
    }

    ElMessageBox.confirm(`确认批量删除选中的 ${selectedRows.value.length} 个分类吗？此操作不可恢复！`, '批量删除确认', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const ids = selectedRows.value.map(item => item.id);
        try {
            await batchDeletePictureCategory({
                data: { ids }
            });
            ElMessage.success('批量删除分类成功');
            getList();
        } catch (error) {
            handleApiError(error, '批量删除分类失败');
        }
    }).catch(() => {
        // 用户取消，不做任何处理
    });
};

// 导出分类数据
const handleExport = () => {
    ElMessage.info('导出功能开发中...');
};

// 对话框操作成功
const handleDialogSuccess = () => {
    getList();
};
</script>

<style scoped lang="scss">
.app-container {
    padding: 20px;
}

.filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    h2 {
        margin: 0;
        color: #303133;
        font-weight: 600;
    }

    .buttons {
        display: flex;
        gap: 8px;
    }
}

.mb-4 {
    margin-bottom: 16px;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .title-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .buttons {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>
