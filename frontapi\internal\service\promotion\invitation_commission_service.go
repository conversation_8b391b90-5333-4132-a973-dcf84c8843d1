package promotion

import (
	"frontapi/pkg/types"

	model "frontapi/internal/models/promotion"
	repo "frontapi/internal/repository/promotion"
	"frontapi/internal/service/base"
)

// CreateInvitationCommissionRequest 创建佣金记录请求
type CreateInvitationCommissionRequest struct {
	InviterID        string         `json:"inviter_id"`
	InvitedID        string         `json:"invited_id"`
	OrderID          string         `json:"order_id"`
	OrderType        string         `json:"order_type"`
	ContentType      string         `json:"content_type"`
	ContentID        string         `json:"content_id"`
	OrderAmount      float64        `json:"order_amount"`
	CommissionRate   float64        `json:"commission_rate"`
	CommissionLevel  int            `json:"commission_level"`
	CommissionAmount float64        `json:"commission_amount"`
	TransactionID    string         `json:"transaction_id"`
	WithdrawID       string         `json:"withdraw_id"`
	Status           string         `json:"status"`
	SettleTime       types.JSONTime `json:"settle_time"`
	SettleBatchNo    string         `json:"settle_batch_no"`
}

type UpdateInvitationCommissionRequest struct {
	OrderType        string         `json:"order_type"`
	ContentType      string         `json:"content_type"`
	ContentID        string         `json:"content_id"`
	OrderAmount      float64        `json:"order_amount"`
	CommissionRate   float64        `json:"commission_rate"`
	CommissionLevel  int            `json:"commission_level"`
	CommissionAmount float64        `json:"commission_amount"`
	TransactionID    string         `json:"transaction_id"`
	WithdrawID       string         `json:"withdraw_id"`
	Status           string         `json:"status"`
	SettleTime       types.JSONTime `json:"settle_time"`
	SettleBatchNo    string         `json:"settle_batch_no"`
}

// InvitationCommissionService 邀请佣金服务接口
type InvitationCommissionService interface {
	base.IExtendedService[model.InvitationCommission]
}

type invitationCommissionService struct {
	*base.ExtendedService[model.InvitationCommission]
	repo repo.InvitationCommissionRepository
}

func NewInvitationCommissionService(repo repo.InvitationCommissionRepository) InvitationCommissionService {
	return &invitationCommissionService{
		ExtendedService: base.NewExtendedService[model.InvitationCommission](repo, "invitation_commission"),
		repo:            repo,
	}
}
