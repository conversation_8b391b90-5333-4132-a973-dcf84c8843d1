<template>
  <div class="theme-switcher">
    <!-- 主题切换按钮 -->
    <el-dropdown 
      trigger="click" 
      placement="bottom-end"
      @command="handleThemeChange"
    >
      <el-button 
        class="theme-button"
        :class="getThemeClasses()"
        circle
        size="large"
      >
        <el-icon><Palette /></el-icon>
      </el-button>
      
      <template #dropdown>
        <el-dropdown-menu class="theme-dropdown">
          <div class="theme-header">
            <h4 class="text-gradient">选择主题</h4>
            <p class="theme-subtitle">个性化你的体验</p>
          </div>
          
          <div class="theme-options">
            <!-- 渐变潮流主题 -->
            <el-dropdown-item 
              command="gradient"
              :class="{ 'active': currentThemeType === 'gradient' }"
              class="theme-option"
            >
              <div class="theme-preview gradient-preview">
                <div class="preview-colors">
                  <div class="color-dot" style="background: linear-gradient(135deg, #FF6B9D 0%, #9C27B0 50%, #BA68C8 100%)"></div>
                  <div class="color-dot" style="background: #FF8A80"></div>
                  <div class="color-dot" style="background: #FF4081"></div>
                </div>
                <div class="theme-info">
                  <span class="theme-name">渐变潮流</span>
                  <span class="theme-desc">炫彩渐变，潮流时尚</span>
                </div>
              </div>
              <el-icon v-if="currentThemeType === 'gradient'" class="check-icon">
                <Check />
              </el-icon>
            </el-dropdown-item>
            
            <!-- 粉色主题 -->
            <el-dropdown-item 
              command="pink"
              :class="{ 'active': currentThemeType === 'pink' }"
              class="theme-option"
            >
              <div class="theme-preview pink-preview">
                <div class="preview-colors">
                  <div class="color-dot" style="background: #FF6B9D"></div>
                  <div class="color-dot" style="background: #FFB3D1"></div>
                  <div class="color-dot" style="background: #FF8A80"></div>
                </div>
                <div class="theme-info">
                  <span class="theme-name">甜美粉色</span>
                  <span class="theme-desc">温柔浪漫，甜美可爱</span>
                </div>
              </div>
              <el-icon v-if="currentThemeType === 'pink'" class="check-icon">
                <Check />
              </el-icon>
            </el-dropdown-item>
            
            <!-- 紫色主题 -->
            <el-dropdown-item 
              command="purple"
              :class="{ 'active': currentThemeType === 'purple' }"
              class="theme-option"
            >
              <div class="theme-preview purple-preview">
                <div class="preview-colors">
                  <div class="color-dot" style="background: #9C27B0"></div>
                  <div class="color-dot" style="background: #CE93D8"></div>
                  <div class="color-dot" style="background: #BA68C8"></div>
                </div>
                <div class="theme-info">
                  <span class="theme-name">神秘紫色</span>
                  <span class="theme-desc">高贵典雅，神秘魅力</span>
                </div>
              </div>
              <el-icon v-if="currentThemeType === 'purple'" class="check-icon">
                <Check />
              </el-icon>
            </el-dropdown-item>
          </div>
          
          <div class="theme-footer">
            <el-button 
              size="small" 
              text 
              @click="resetTheme"
            >
              重置默认
            </el-button>
          </div>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- 主题预览模态框 -->
    <el-dialog
      v-model="showPreview"
      title="主题预览"
      width="600px"
      :before-close="closePreview"
      class="theme-preview-dialog"
    >
      <div class="preview-content" :class="`theme-${previewTheme}`">
        <div class="preview-header">
          <h3 class="text-gradient">{{ getThemeName(previewTheme) }}</h3>
          <p>预览主题效果</p>
        </div>
        
        <div class="preview-components">
          <!-- 按钮预览 -->
          <div class="component-group">
            <h4>按钮</h4>
            <div class="button-group">
              <el-button type="primary">主要按钮</el-button>
              <el-button>默认按钮</el-button>
              <el-button type="success">成功按钮</el-button>
            </div>
          </div>
          
          <!-- 输入框预览 -->
          <div class="component-group">
            <h4>输入框</h4>
            <el-input placeholder="请输入内容" class="preview-input" />
          </div>
          
          <!-- 卡片预览 -->
          <div class="component-group">
            <h4>卡片</h4>
            <el-card class="preview-card">
              <template #header>
                <span class="text-gradient">卡片标题</span>
              </template>
              <p>这是一个示例卡片内容，展示当前主题的效果。</p>
            </el-card>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="closePreview">取消</el-button>
        <el-button type="primary" @click="applyPreviewTheme">
          应用主题
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem, ElButton, ElIcon, ElDialog, ElInput, ElCard } from 'element-plus'
import { Palette, Check } from '@element-plus/icons-vue'
import { useTrendyTheme } from '@/core/plugins/theme'

// 主题类型定义
type ThemeType = 'gradient' | 'pink' | 'purple'

// 主题管理
const { 
  currentThemeType, 
  setTheme, 
  getThemeClasses, 
  getThemePreview,
  isTransitioning 
} = useTrendyTheme()

// 预览相关
const showPreview = ref(false)
const previewTheme = ref<ThemeType>('gradient')

// 主题切换处理
const handleThemeChange = async (themeType: ThemeType) => {
  if (themeType === currentThemeType.value) return
  
  try {
    await setTheme(themeType)
    
    // 显示切换成功提示
    ElMessage({
      message: `已切换到${getThemeName(themeType)}主题`,
      type: 'success',
      duration: 2000
    })
  } catch (error) {
    console.error('主题切换失败:', error)
    ElMessage({
      message: '主题切换失败，请重试',
      type: 'error'
    })
  }
}

// 获取主题名称
const getThemeName = (themeType: ThemeType): string => {
  const names = {
    gradient: '渐变潮流',
    pink: '甜美粉色',
    purple: '神秘紫色'
  }
  return names[themeType]
}

// 重置主题
const resetTheme = async () => {
  await setTheme('gradient')
  ElMessage({
    message: '已重置为默认主题',
    type: 'info'
  })
}

// 预览主题
const previewThemeHandler = (themeType: ThemeType) => {
  previewTheme.value = themeType
  showPreview.value = true
}

// 关闭预览
const closePreview = () => {
  showPreview.value = false
}

// 应用预览主题
const applyPreviewTheme = async () => {
  await handleThemeChange(previewTheme.value)
  closePreview()
}

// 当前主题按钮样式
const themeButtonStyle = computed(() => {
  const colors = getThemePreview(currentThemeType.value)
  return {
    background: colors.gradient,
    borderColor: colors.primary
  }
})
</script>

<style scoped>
.theme-switcher {
  position: relative;
}

.theme-button {
  @apply trendy-button;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.theme-button:hover {
  transform: scale(1.1);
  box-shadow: var(--neon-glow);
}

.theme-dropdown {
  @apply trendy-card;
  min-width: 320px;
  padding: 0;
  border: none;
  box-shadow: 0 12px 40px var(--shadow-color);
}

.theme-header {
  padding: 20px 20px 16px;
  border-bottom: 1px solid var(--color-border);
  text-align: center;
}

.theme-header h4 {
  margin: 0 0 4px;
  font-size: 18px;
  font-weight: 600;
}

.theme-subtitle {
  margin: 0;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.theme-options {
  padding: 12px 8px;
}

.theme-option {
  padding: 12px 16px;
  margin: 4px 0;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.theme-option:hover {
  background: rgba(255, 107, 157, 0.05);
  transform: translateX(4px);
}

.theme-option.active {
  background: var(--gradient-primary);
  color: white;
}

.theme-option.active .theme-name,
.theme-option.active .theme-desc {
  color: white;
}

.theme-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.preview-colors {
  display: flex;
  gap: 4px;
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.theme-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
}

.theme-desc {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.check-icon {
  color: #67C23A;
  font-size: 16px;
}

.theme-option.active .check-icon {
  color: white;
}

.theme-footer {
  padding: 12px 20px 16px;
  border-top: 1px solid var(--color-border);
  text-align: center;
}

/* 预览对话框样式 */
.theme-preview-dialog {
  @apply trendy-card;
}

.preview-content {
  padding: 20px;
}

.preview-header {
  text-align: center;
  margin-bottom: 24px;
}

.preview-header h3 {
  margin: 0 0 8px;
  font-size: 20px;
  font-weight: 600;
}

.preview-header p {
  margin: 0;
  color: var(--color-text-secondary);
}

.preview-components {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.component-group h4 {
  margin: 0 0 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text);
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.preview-input {
  max-width: 300px;
}

.preview-card {
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .theme-dropdown {
    min-width: 280px;
  }
  
  .theme-option {
    padding: 10px 12px;
  }
  
  .theme-name {
    font-size: 13px;
  }
  
  .theme-desc {
    font-size: 11px;
  }
}
</style>