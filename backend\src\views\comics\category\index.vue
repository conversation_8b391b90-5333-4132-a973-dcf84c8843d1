<template>
  <div class="category-list">
    <el-card>
      <template #header>
        <div class="title-container flex justify-between items-center card-header">
          <div class="card-title">分类列表</div>
          <div class="buttons">
            <el-button type="primary" @click="onAdd">添加分类</el-button>
          </div>
        </div>
      </template>
      <!-- 搜索栏组件 -->
      <SearchBar @search="onSearch" @reset="onReset" />

      <!-- 分类表格组件 -->
      <CategoryTable
        :loading="loading"
        :category-list="categoryList"
        @edit="onEdit"
        @delete="onDelete"
      />

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total" background
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onPageChange"
        />
      </div>

      <!-- 分类表单对话框组件 -->
      <CategoryDialog
        :visible="dialogVisible"
        :category="currentCategory"
        @submit="submitForm"
        @close="dialogVisible = false"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive,onMounted,watch  } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getComicsCategoryList, createComicsCategory, updateComicsCategory, deleteComicsCategory } from '@/service/api/comics/category';
import type { ComicsCategory, CreateComicsCategoryRequest, UpdateComicsCategoryRequest, ComicsParams } from '@/types/comics';
import SearchBar from './components/SearchBar.vue';
import CategoryTable from './components/CategoryTable.vue';
import CategoryDialog from './components/CategoryDialog.vue';

const loading = ref(false);
const categoryList = ref<ComicsCategory[]>([]);
const pagination = reactive({ page: 1, pageSize: 10, total: 0 });
const dialogVisible = ref(false);
const currentCategory = ref<ComicsCategory>();

const fetchList = async (params?: any) => {
  loading.value = true;
  try {
    const queryParams: ComicsParams = {
      page: { pageNo: pagination.page, pageSize: pagination.pageSize },
      data: { ...params }
    };
    const {response,data} = await getComicsCategoryList(queryParams) as any;
    if (response.data.code === 2000) {
      categoryList.value = data.list;
      pagination.total = data.total;
    }
  } finally {
    loading.value = false;
  }
};

const onSearch = (params: any) => {
  pagination.page = 1;
  fetchList(params);
};

const onReset = () => {
  pagination.page = 1;
  fetchList();
};

const onPageChange = (page: number) => {
  pagination.page = page;
  fetchList();
};

const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchList();
};

const onAdd = () => {
  currentCategory.value = undefined;
  dialogVisible.value = true;
};

const onEdit = (row: ComicsCategory) => {
  currentCategory.value = row;
  dialogVisible.value = true;
};

const onDelete = async (row: ComicsCategory) => {
  try {
    await ElMessageBox.confirm('确定要删除该分类吗？', '提示', { type: 'warning' });
    const res = await deleteComicsCategory(row.id) as any;
    if (res && res.code === 2000) {
      ElMessage.success('删除成功');
      fetchList();
    } else {
      ElMessage.error(res?.message || '删除失败');
    }
  } catch (e) {}
};

const submitForm = async (formData: any) => {
  try {
    let res;
    if (formData.id) {
      const params: { data: UpdateComicsCategoryRequest } = { data: formData };
      res = await updateComicsCategory(formData.id, params) as any;
    } else {
      const params: { data: CreateComicsCategoryRequest } = { data: formData };
      res = await createComicsCategory(params) as any;
    }
    const {response,data}=res as any;
    if (response.data.code === 2000) {
      ElMessage.success(formData.id ? '编辑成功' : '添加成功');
      dialogVisible.value = false;
      fetchList();
    } else {
      ElMessage.error(response?.data?.message || '操作失败');
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('操作失败');
  }
};

onMounted(() => { fetchList(); });
</script>

<style scoped lang="scss">
.category-list {
  .pagination-container {
    margin-top: 20px;
  }
}
</style>
