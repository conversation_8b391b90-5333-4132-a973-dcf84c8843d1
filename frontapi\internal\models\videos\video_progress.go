package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoProgress 用户播放进度表
type VideoProgress struct {
	*models.BaseModelStruct
	UserID     string         `gorm:"column:user_id;index" json:"user_id"`                                   // 用户ID
	VideoID    string         `gorm:"column:video_id;index" json:"video_id"`                                 // 视频ID
	Progress   uint           `gorm:"column:progress;default:0" json:"progress"`                             // 进度(秒)
	Duration   uint           `gorm:"column:duration;default:0" json:"duration"`                             // 视频总时长(秒)
	IsFinished uint8          `gorm:"column:is_finished;default:0" json:"is_finished"`                       // 是否看完：0-未看完，1-已看完
	LastView   types.JSONTime `gorm:"column:last_view_time;default:CURRENT_TIMESTAMP" json:"last_view_time"` // 最后观看时间
}

// TableName 设置表名
func (VideoProgress) TableName() string {
	return "ly_video_progress"
}

// 实现BaseModel接口的方法
func (v *VideoProgress) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoProgress) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoProgress) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoProgress) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
