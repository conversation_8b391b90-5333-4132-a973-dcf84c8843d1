<template>
    <div class="comment-search-bar">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
            <el-form-item label="评论内容">
                <el-input v-model="searchForm.content" placeholder="请输入评论内容" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="帖子ID">
                <el-input v-model="searchForm.post_id" placeholder="请输入帖子ID" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="用户ID">
                <el-input v-model="searchForm.user_id" placeholder="请输入用户ID" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="所有状态" clearable style="width: 120px;">
                    <el-option label="待审核" :value="0"></el-option>
                    <el-option label="影藏" :value="1"></el-option>
                    <el-option label="显示" :value="2"></el-option>
                    <el-option label="审核拒绝" :value="-2"></el-option>
                    <el-option label="已删除" :value="-4"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon>
                        <Search />
                    </el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    重置
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { Refresh, Search } from '@element-plus/icons-vue';
import { reactive, watch } from 'vue';

// 搜索表单类型定义
export interface CommentSearchForm {
    content: string;
    post_id: string;
    user_id: string;
    status?: number;
}

// Props
interface Props {
    modelValue?: CommentSearchForm;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
        content: '',
        post_id: '',
        user_id: '',
        status: undefined,
    })
});

// Emits
interface Emits {
    search: [params: CommentSearchForm];
    reset: [];
    refresh: [];
    'update:modelValue': [value: CommentSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<CommentSearchForm>({ ...props.modelValue });

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
    emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
    emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
    Object.assign(searchForm, {
        content: '',
        post_id: '',
        user_id: '',
        status: undefined,
    });
    emit('reset');
};

// 刷新
const handleRefresh = () => {
    emit('refresh');
};
</script>

<style scoped lang="scss">
.comment-search-bar {
    margin-bottom: 16px;

    .el-form {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e4e7ed;
    }

    .el-form-item {
        margin-bottom: 8px;
    }
}
</style>