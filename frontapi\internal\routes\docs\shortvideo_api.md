# 短视频接口文档

## 短视频基础接口 (/shorts)

### 1. 获取短视频列表

**接口地址**: `POST /api/shorts/getShortVideoList`

**接口描述**: 获取短视频列表，支持分页和筛选

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "user_id": "用户ID",
    "status": 1,
    "sort_by": "created_at",
    "sort_order": "desc",
    "duration_filter": "short"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "shortvideo_id",
        "title": "短视频标题",
        "description": "短视频描述",
        "cover_url": "封面图片URL",
        "video_url": "视频文件URL",
        "duration": 30,
        "width": 720,
        "height": 1280,
        "file_size": 5242880,
        "views_count": 1000,
        "likes_count": 50,
        "comments_count": 10,
        "shares_count": 5,
        "category_id": "分类ID",
        "user_id": "用户ID",
        "username": "用户名",
        "avatar": "用户头像",
        "is_liked": false,
        "is_collected": false,
        "is_followed": false,
        "tags": ["标签1", "标签2"],
        "location": "发布地点",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 500,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取短视频详情

**接口地址**: `POST /api/shorts/getShortVideoDetail`

**接口描述**: 获取指定短视频的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "shortvideo_id",
    "user_id": "当前用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "shortvideo_id",
    "title": "短视频标题",
    "description": "短视频描述",
    "cover_url": "封面图片URL",
    "video_url": "视频文件URL",
    "duration": 30,
    "width": 720,
    "height": 1280,
    "file_size": 5242880,
    "views_count": 1000,
    "likes_count": 50,
    "comments_count": 10,
    "shares_count": 5,
    "category_id": "分类ID",
    "category_name": "分类名称",
    "user_id": "用户ID",
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "用户头像",
    "bio": "用户简介",
    "is_liked": false,
    "is_collected": false,
    "is_followed": false,
    "tags": ["标签1", "标签2"],
    "location": "发布地点",
    "music_info": {
      "id": "music_id",
      "name": "背景音乐名称",
      "artist": "艺术家",
      "url": "音乐URL"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 观看短视频

**接口地址**: `POST /api/shorts/viewShortVideo`

**接口描述**: 记录用户观看短视频的行为，增加播放量

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID",
    "watch_duration": 25,
    "watch_percentage": 83.33
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "观看记录成功",
  "data": {
    "view_recorded": true,
    "new_views_count": 1001
  }
}
```

### 4. 搜索短视频

**接口地址**: `POST /api/shorts/searchShortVideos`

**接口描述**: 根据关键词搜索短视频

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "duration_filter": "all",
    "upload_time": "week",
    "sort_by": "relevance"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 5. 获取热门短视频

**接口地址**: `POST /api/shorts/getTrendingShortVideos`

**接口描述**: 获取当前热门的短视频列表

**请求参数**:
```json
{
  "data": {
    "time_range": "day",
    "category_id": "分类ID",
    "region": "地区代码"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 6. 获取相关短视频

**接口地址**: `POST /api/shorts/getRelatedShortVideos`

**接口描述**: 获取与指定短视频相关的推荐视频

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID",
    "exclude_ids": ["id1", "id2"]
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 短视频互动接口

### 7. 点赞短视频

**接口地址**: `POST /api/shorts/likeShortVideo`

**接口描述**: 为短视频点赞

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "点赞成功",
  "data": {
    "liked": true,
    "new_likes_count": 51
  }
}
```

### 8. 取消点赞短视频

**接口地址**: `POST /api/shorts/cancelLikeShortVideo`

**接口描述**: 取消短视频点赞

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID"
  }
}
```

### 9. 检查用户点赞状态

**接口地址**: `POST /api/shorts/checkUserLiked`

**接口描述**: 检查用户是否已点赞指定短视频

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "is_liked": true,
    "liked_at": "2024-01-01T00:00:00Z"
  }
}
```

## 短视频收藏接口

### 10. 收藏短视频

**接口地址**: `POST /api/shorts/collectShortVideo`

**接口描述**: 收藏短视频到个人收藏夹

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID",
    "collection_id": "收藏夹ID"
  }
}
```

### 11. 取消收藏短视频

**接口地址**: `POST /api/shorts/cancelCollectShortVideo`

**接口描述**: 取消收藏短视频

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID"
  }
}
```

### 12. 检查用户收藏状态

**接口地址**: `POST /api/shorts/checkUserCollected`

**接口描述**: 检查用户是否已收藏指定短视频

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "user_id": "用户ID"
  }
}
```

### 13. 获取用户收藏列表

**接口地址**: `POST /api/shorts/getUserCollections`

**接口描述**: 获取用户的短视频收藏列表

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "collection_id": "收藏夹ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 短视频评论接口

### 14. 获取短视频评论

**接口地址**: `POST /api/shorts/getShortVideoComments`

**接口描述**: 获取指定短视频的评论列表

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "parent_id": "父评论ID",
    "sort_by": "created_at",
    "sort_order": "desc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "comment_id",
        "content": "评论内容",
        "user_id": "用户ID",
        "username": "用户名",
        "avatar": "用户头像",
        "shortvideo_id": "短视频ID",
        "parent_id": "父评论ID",
        "likes_count": 10,
        "replies_count": 5,
        "is_liked": false,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 15. 添加短视频评论

**接口地址**: `POST /api/shorts/addShortVideoComment`

**接口描述**: 为短视频添加评论

**请求参数**:
```json
{
  "data": {
    "shortvideo_id": "shortvideo_id",
    "content": "评论内容",
    "parent_id": "父评论ID",
    "mentioned_users": ["@用户名1", "@用户名2"]
  }
}
```

### 16. 点赞短视频评论

**接口地址**: `POST /api/shorts/likeShortVideoComment`

**接口描述**: 为短视频评论点赞

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "user_id": "用户ID"
  }
}
```

### 17. 取消点赞短视频评论

**接口地址**: `POST /api/shorts/cancelLikeShortVideoComment`

**接口描述**: 取消短视频评论点赞

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "user_id": "用户ID"
  }
}
```

### 18. 检查用户评论点赞状态

**接口地址**: `POST /api/shorts/checkUserLikedShortVideoComment`

**接口描述**: 检查用户是否已点赞指定评论

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "user_id": "用户ID"
  }
}
```

## 短视频分类接口

### 19. 获取短视频分类

**接口地址**: `POST /api/shorts/getShortVideoCategories`

**接口描述**: 获取短视频分类列表

**请求参数**:
```json
{
  "data": {
    "parent_id": "父分类ID",
    "status": 1,
    "include_video_count": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "category_id",
        "name": "分类名称",
        "description": "分类描述",
        "icon_url": "图标URL",
        "cover_url": "封面图片URL",
        "parent_id": "父分类ID",
        "sort_order": 1,
        "video_count": 1000,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```