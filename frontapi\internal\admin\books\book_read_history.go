package books

import (
	"frontapi/internal/admin"
	bookService "frontapi/internal/service/books"
)

// BookReadHistoryController 电子书阅读历史控制器
type BookReadHistoryController struct {
	admin.BaseController
	readHistoryService bookService.BookReadHistoryService
}

// NewBookReadHistoryController 创建电子书阅读历史控制器
func NewBookReadHistoryController(readHistoryService bookService.BookReadHistoryService) *BookReadHistoryController {
	return &BookReadHistoryController{
		readHistoryService: readHistoryService,
	}
}
