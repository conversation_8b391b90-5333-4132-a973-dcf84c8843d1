package extfollow

import (
	"context"
	"fmt"
	"time"

	"frontapi/internal/service/base/extfollow/redis"
	"frontapi/internal/service/base/extfollow/types"

	goredis "github.com/go-redis/redis/v8"
)

// FollowServiceImpl 关注服务实现
type FollowServiceImpl struct {
	adapter FollowAdapter
	config  *ServiceConfig
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	StorageType   string        `json:"storage_type"`   // redis, mongodb, hybrid
	DefaultTTL    time.Duration `json:"default_ttl"`    // 默认缓存时间
	BatchSize     int           `json:"batch_size"`     // 批量操作大小
	EnableMetrics bool          `json:"enable_metrics"` // 是否启用指标收集
	EnableSync    bool          `json:"enable_sync"`    // 是否启用数据同步

	// Redis配置
	RedisConfig *redis.Config `json:"redis_config"`
}

// DefaultServiceConfig 默认服务配置
func DefaultServiceConfig() *ServiceConfig {
	return &ServiceConfig{
		StorageType:   "redis",
		DefaultTTL:    24 * time.Hour,
		BatchSize:     100,
		EnableMetrics: true,
		EnableSync:    false,
		RedisConfig:   redis.DefaultConfig(),
	}
}

// NewFollowService 创建关注服务
func NewFollowService(redisClient *goredis.Client, config *ServiceConfig) ExtendedFollowService {
	if config == nil {
		config = DefaultServiceConfig()
	}

	var adapter FollowAdapter

	switch config.StorageType {
	case "redis":
		adapter = redis.NewRedisAdapter(redisClient, config.RedisConfig)
	default:
		adapter = redis.NewRedisAdapter(redisClient, config.RedisConfig)
	}

	return &FollowServiceImpl{
		adapter: adapter,
		config:  config,
	}
}

// ============ 基础关注操作 ============

// Follow 关注用户
func (s *FollowServiceImpl) Follow(ctx context.Context, followerID, followeeID string) error {
	if followerID == "" {
		return types.ErrInvalidFollowerID
	}
	if followeeID == "" {
		return types.ErrInvalidFolloweeID
	}
	if followerID == followeeID {
		return types.ErrSelfFollow
	}

	return s.adapter.Follow(ctx, followerID, followeeID)
}

// Unfollow 取消关注
func (s *FollowServiceImpl) Unfollow(ctx context.Context, followerID, followeeID string) error {
	if followerID == "" {
		return types.ErrInvalidFollowerID
	}
	if followeeID == "" {
		return types.ErrInvalidFolloweeID
	}
	if followerID == followeeID {
		return types.ErrSelfFollow
	}

	return s.adapter.Unfollow(ctx, followerID, followeeID)
}

// IsFollowing 检查是否关注
func (s *FollowServiceImpl) IsFollowing(ctx context.Context, followerID, followeeID string) (bool, error) {
	if followerID == "" || followeeID == "" {
		return false, nil
	}
	if followerID == followeeID {
		return false, nil
	}

	return s.adapter.IsFollowing(ctx, followerID, followeeID)
}

// GetFollowerCount 获取粉丝数
func (s *FollowServiceImpl) GetFollowerCount(ctx context.Context, userID string) (int64, error) {
	if userID == "" {
		return 0, types.ErrInvalidFolloweeID
	}

	return s.adapter.GetFollowerCount(ctx, userID)
}

// GetFollowingCount 获取关注数
func (s *FollowServiceImpl) GetFollowingCount(ctx context.Context, userID string) (int64, error) {
	if userID == "" {
		return 0, types.ErrInvalidFollowerID
	}

	return s.adapter.GetFollowingCount(ctx, userID)
}

// ============ 批量操作 ============

// BatchFollow 批量关注
func (s *FollowServiceImpl) BatchFollow(ctx context.Context, operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	// 验证操作
	if err := types.ValidateBatchOperations(operations); err != nil {
		return err
	}

	return s.adapter.BatchFollow(ctx, operations)
}

// BatchUnfollow 批量取消关注
func (s *FollowServiceImpl) BatchUnfollow(ctx context.Context, operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	// 验证操作
	if err := types.ValidateBatchOperations(operations); err != nil {
		return err
	}

	return s.adapter.BatchUnfollow(ctx, operations)
}

// BatchGetFollowStatus 批量获取关注状态
func (s *FollowServiceImpl) BatchGetFollowStatus(ctx context.Context, followerID string, userIDs []string) (map[string]bool, error) {
	if followerID == "" || len(userIDs) == 0 {
		return make(map[string]bool), nil
	}

	return s.adapter.BatchGetFollowStatus(ctx, followerID, userIDs)
}

// BatchGetFollowerCounts 批量获取粉丝数
func (s *FollowServiceImpl) BatchGetFollowerCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	if len(userIDs) == 0 {
		return make(map[string]int64), nil
	}

	return s.adapter.BatchGetFollowerCounts(ctx, userIDs)
}

// BatchGetFollowingCounts 批量获取关注数
func (s *FollowServiceImpl) BatchGetFollowingCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	if len(userIDs) == 0 {
		return make(map[string]int64), nil
	}

	return s.adapter.BatchGetFollowingCounts(ctx, userIDs)
}

// ============ 查询操作 ============

// GetUserFollowers 获取用户粉丝列表
func (s *FollowServiceImpl) GetUserFollowers(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	if userID == "" {
		return []*types.FollowRecord{}, nil
	}
	if limit <= 0 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	return s.adapter.GetUserFollowers(ctx, userID, limit, offset)
}

// GetUserFollowing 获取用户关注列表
func (s *FollowServiceImpl) GetUserFollowing(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	if userID == "" {
		return []*types.FollowRecord{}, nil
	}
	if limit <= 0 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	return s.adapter.GetUserFollowing(ctx, userID, limit, offset)
}

// GetFollowHistory 获取关注历史
func (s *FollowServiceImpl) GetFollowHistory(ctx context.Context, userID string, timeRange *types.TimeRange) ([]*types.FollowRecord, error) {
	if userID == "" {
		return []*types.FollowRecord{}, nil
	}

	return s.adapter.GetFollowHistory(ctx, userID, timeRange)
}

// GetMutualFollows 获取互相关注
func (s *FollowServiceImpl) GetMutualFollows(ctx context.Context, userID1, userID2 string) ([]*types.FollowRecord, error) {
	if userID1 == "" || userID2 == "" || userID1 == userID2 {
		return []*types.FollowRecord{}, nil
	}

	return s.adapter.GetMutualFollows(ctx, userID1, userID2)
}

// ============ 热门排名 ============

// UpdateInfluenceRank 更新影响力排名
func (s *FollowServiceImpl) UpdateInfluenceRank(ctx context.Context, userID string, score float64) error {
	if userID == "" {
		return types.ErrInvalidFolloweeID
	}

	return s.adapter.UpdateInfluenceRank(ctx, userID, score)
}

// GetInfluenceRanking 获取影响力排行榜
func (s *FollowServiceImpl) GetInfluenceRanking(ctx context.Context, limit int) ([]string, error) {
	if limit <= 0 {
		limit = 10
	}

	return s.adapter.GetInfluenceRanking(ctx, limit)
}

// GetInfluenceRankingWithScores 获取带分数的影响力排行榜
func (s *FollowServiceImpl) GetInfluenceRankingWithScores(ctx context.Context, limit int) (map[string]float64, error) {
	if limit <= 0 {
		limit = 10
	}

	return s.adapter.GetInfluenceRankingWithScores(ctx, limit)
}

// ============ 统计操作 ============

// GetUserFollowStats 获取用户关注统计
func (s *FollowServiceImpl) GetUserFollowStats(ctx context.Context, userID string) (*types.UserFollowStats, error) {
	if userID == "" {
		return nil, types.ErrInvalidFollowerID
	}

	return s.adapter.GetUserFollowStats(ctx, userID)
}

// GetFollowTrends 获取关注趋势
func (s *FollowServiceImpl) GetFollowTrends(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.FollowTrend, error) {
	if limit <= 0 {
		limit = 10
	}

	return s.adapter.GetFollowTrends(ctx, timeRange, limit)
}

// ============ 扩展功能 ============

// InvalidateCache 清除缓存
func (s *FollowServiceImpl) InvalidateCache(ctx context.Context, keys ...string) error {
	return s.adapter.InvalidateCache(ctx, keys...)
}

// WarmupCache 预热缓存
func (s *FollowServiceImpl) WarmupCache(ctx context.Context, userIDs []string) error {
	return s.adapter.WarmupCache(ctx, userIDs)
}

// GetCacheStats 获取缓存统计
func (s *FollowServiceImpl) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return s.adapter.GetCacheStats(ctx)
}

// CleanupExpiredData 清理过期数据
func (s *FollowServiceImpl) CleanupExpiredData(ctx context.Context, before time.Time) error {
	return s.adapter.CleanupExpiredData(ctx, before)
}

// ExportData 导出数据
func (s *FollowServiceImpl) ExportData(ctx context.Context, userID, format string) ([]byte, error) {
	return s.adapter.ExportData(ctx, userID, format)
}

// ImportData 导入数据
func (s *FollowServiceImpl) ImportData(ctx context.Context, data []byte, format string) error {
	return s.adapter.ImportData(ctx, data, format)
}

// SyncData 数据同步
func (s *FollowServiceImpl) SyncData(ctx context.Context, startTime, endTime time.Time) error {
	// 简化实现
	return fmt.Errorf("数据同步功能暂未实现")
}

// GetSyncStatus 获取同步状态
func (s *FollowServiceImpl) GetSyncStatus(ctx context.Context) (*types.SyncStatus, error) {
	return &types.SyncStatus{
		LastSyncTime: time.Now(),
		IsRunning:    false,
		RecordsCount: 0,
		ErrorCount:   0,
	}, nil
}

// GetMetrics 获取服务指标
func (s *FollowServiceImpl) GetMetrics(ctx context.Context) (*types.ServiceMetrics, error) {
	return &types.ServiceMetrics{
		TotalRequests:      0,
		SuccessfulRequests: 0,
		FailedRequests:     0,
		SuccessRate:        1.0,
		LastUpdated:        time.Now(),
	}, nil
}

// GetErrorStats 获取错误统计
func (s *FollowServiceImpl) GetErrorStats(ctx context.Context) (map[string]interface{}, error) {
	return map[string]interface{}{
		"error_count": 0,
		"last_error":  "",
	}, nil
}

// HealthCheck 健康检查
func (s *FollowServiceImpl) HealthCheck(ctx context.Context) error {
	return s.adapter.HealthCheck(ctx)
}

// Close 关闭服务
func (s *FollowServiceImpl) Close() error {
	return s.adapter.Close()
}

// Shutdown 优雅关闭
func (s *FollowServiceImpl) Shutdown(ctx context.Context) error {
	// 清理资源，保存状态等
	return s.Close()
}
