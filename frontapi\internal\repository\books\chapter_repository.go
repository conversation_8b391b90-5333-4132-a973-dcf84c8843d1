package books

import (
	"context"
	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ChapterRepository 电子书章节仓库接口
type ChapterRepository interface {
	base.ExtendedRepository[books.BookChapter]
	ListByBook(ctx context.Context, bookID string, page, pageSize int) ([]*books.BookChapter, int64, error)
	FindByBookAndChapterNumber(ctx context.Context, bookID string, chapterNumber int) (*books.BookChapter, error)
	IncrementReadCount(ctx context.Context, id string) error
	GetMaxChapterNumber(ctx context.Context, bookID string) (int, error)
	GetChapterCount(ctx context.Context, bookID string) (int64, error)
	GetTotalWordCount(ctx context.Context, bookID string) (uint64, error)
	BatchUpdateChapterOrder(ctx context.Context, bookID string, updates []map[string]interface{}) error
}

type chapterRepository struct {
	base.ExtendedRepository[books.BookChapter]
}

// NewChapterRepository 创建电子书章节仓库实例
func NewChapterRepository(db *gorm.DB) ChapterRepository {
	return &chapterRepository{
		ExtendedRepository: base.NewExtendedRepository[books.BookChapter](db),
	}
}

// ListByBook 获取电子书章节列表
func (r *chapterRepository) ListByBook(ctx context.Context, bookID string, page, pageSize int) ([]*books.BookChapter, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "chapter_number ASC", page, pageSize, "book_id = ? AND status = ?", bookID, 1)
}

// FindByBookAndChapterNumber 根据电子书ID和章节序号查找电子书章节
func (r *chapterRepository) FindByBookAndChapterNumber(ctx context.Context, bookID string, chapterNumber int) (*books.BookChapter, error) {
	conditions := map[string]interface{}{
		"book_id":        bookID,
		"chapter_number": chapterNumber,
	}
	return r.FindOneByCondition(ctx, conditions, "")
}

// IncrementReadCount 增加阅读数
func (r *chapterRepository) IncrementReadCount(ctx context.Context, id string) error {
	return r.GetDBWithContext(ctx).Model(&books.BookChapter{}).
		Where("id = ?", id).
		UpdateColumn("read_count", gorm.Expr("read_count + 1")).Error
}

// GetMaxChapterNumber 获取最大章节序号
func (r *chapterRepository) GetMaxChapterNumber(ctx context.Context, bookID string) (int, error) {
	var maxChapterNumber int
	err := r.GetDBWithContext(ctx).Model(&books.BookChapter{}).
		Where("book_id = ?", bookID).
		Select("COALESCE(MAX(chapter_number), 0)").
		Scan(&maxChapterNumber).Error
	if err != nil {
		return 0, err
	}
	return maxChapterNumber, nil
}

// GetChapterCount 获取章节数量
func (r *chapterRepository) GetChapterCount(ctx context.Context, bookID string) (int64, error) {
	var count int64
	err := r.GetDBWithContext(ctx).Model(&books.BookChapter{}).
		Where("book_id = ?", bookID).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// GetTotalWordCount 获取总字数
func (r *chapterRepository) GetTotalWordCount(ctx context.Context, bookID string) (uint64, error) {
	var totalWordCount uint64
	err := r.GetDBWithContext(ctx).Model(&books.BookChapter{}).
		Where("book_id = ?", bookID).
		Select("COALESCE(SUM(word_count), 0)").
		Scan(&totalWordCount).Error
	if err != nil {
		return 0, err
	}
	return totalWordCount, nil
}

// BatchUpdateChapterOrder 批量更新章节排序
func (r *chapterRepository) BatchUpdateChapterOrder(ctx context.Context, bookID string, updates []map[string]interface{}) error {
	// 开启事务
	return r.GetDBWithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			chapterID, ok := update["id"].(string)
			if !ok || chapterID == "" {
				continue
			}

			// 构建更新数据
			updateData := make(map[string]interface{})

			if sortOrder, ok := update["sort_order"].(int); ok && sortOrder > 0 {
				updateData["sort_order"] = sortOrder
			}

			if chapterNumber, ok := update["chapter_number"].(int); ok && chapterNumber > 0 {
				updateData["chapter_number"] = chapterNumber
			}

			if len(updateData) == 0 {
				continue
			}

			// 执行更新
			err := tx.Model(&books.BookChapter{}).
				Where("id = ? AND book_id = ?", chapterID, bookID).
				Updates(updateData).Error

			if err != nil {
				return err
			}
		}

		return nil
	})
}
