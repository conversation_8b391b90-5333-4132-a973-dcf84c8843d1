<template>
    <div>
        <div v-if="showDebugPanel" class="debug-panel">
            <div class="debug-panel__header">
                <h3>调试面板</h3>
                <button @click="showDebugPanel = false">×</button>
            </div>
            <div class="debug-panel__content">
                <div class="debug-section">
                    <h4>当前主题</h4>
                    <p>{{ themeName }}</p>
                </div>
                <div class="debug-section">
                    <h4>当前语言</h4>
                    <p>{{ currentLanguage?.code || 'Unknown' }}</p>
                </div>
                <div class="debug-section">
                    <h4>路由信息</h4>
                    <p>{{ route.name }} ({{ route.path }})</p>
                </div>
                <div class="debug-section">
                    <h4>插件状态</h4>
                    <ul>
                        <li v-for="plugin in pluginStatus" :key="plugin.name">
                            {{ plugin.name }}: {{ plugin.status }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 调试面板切换按钮 -->
        <button v-if="!showDebugPanel" class="debug-toggle" @click="showDebugPanel = true" title="显示调试面板">
            🐛
        </button>
    </div>
</template>

<script setup lang="ts">
import { getPluginStatus } from '@/core/plugins'
import { useI18nManager } from '@/core/plugins/i18n/composables'
import { useTheme } from '@/core/plugins/theme'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const showDebugPanel = ref(false)
const route = useRoute()
const { currentTheme, getTheme } = useTheme()
const i18nManager = useI18nManager()
const currentLanguage = computed(() => 'zh-CN')

const themeName = computed(() => {
    return currentTheme.value || 'default'
})

const pluginStatus = computed(() => {
    try {
        return getPluginStatus() || []
    } catch (error) {
        console.error('获取插件状态失败:', error)
        return []
    }
})

// 全局事件监听
const handleKeydown = (event: KeyboardEvent) => {
    // Ctrl/Cmd + Shift + D 切换调试面板
    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault()
        showDebugPanel.value = !showDebugPanel.value
    }
}

onMounted(() => {
    // 添加全局事件监听
    document.addEventListener('keydown', handleKeydown)

        // 暴露全局方法到 window
        ; (window as any).__DEBUG_PANEL__ = {
            toggle: () => {
                showDebugPanel.value = !showDebugPanel.value
            }
        }
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.debug-panel {
    position: fixed;
    top: 20px;
    left: 20px;
    width: 300px;
    max-height: 80vh;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    border-radius: 8px;
    overflow: hidden;
    z-index: 10001;
    font-size: 12px;
}

.debug-panel__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.debug-panel__header h3 {
    margin: 0;
    font-size: 14px;
}

.debug-panel__header button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.debug-panel__content {
    padding: 16px;
    max-height: calc(80vh - 60px);
    overflow-y: auto;
}

.debug-section {
    margin-bottom: 16px;
}

.debug-section h4 {
    margin-bottom: 8px;
    font-size: 12px;
    color: #fbbf24;
}

.debug-section p,
.debug-section li {
    font-size: 11px;
    line-height: 1.4;
    color: #d1d5db;
}

.debug-section ul {
    list-style: none;
    padding-left: 0;
}

.debug-section li {
    padding: 2px 0;
}

.debug-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    font-size: 20px;
    cursor: pointer;
    z-index: 10000;
    transition: all 0.3s ease;
}

.debug-toggle:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .debug-panel {
        top: 10px;
        left: 10px;
        right: 10px;
        width: auto;
    }

    .debug-toggle {
        bottom: 10px;
        left: 10px;
    }
}
</style>