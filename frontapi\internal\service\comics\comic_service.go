package comics

import (
	"context"
	"frontapi/internal/models/comics"
	repo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
)

// ComicService 漫画服务接口
type ComicService interface {
	base.IExtendedService[comics.Comic]
	GetRelatedComics(ctx context.Context, condition map[string]interface{}, orderBy string, pageNo int, pageSize int) ([]*comics.Comic, int64, error)
}

// comicService 漫画服务实现
type comicService struct {
	*base.ExtendedService[comics.Comic]
	comicRepo    repo.ComicRepository
	categoryRepo repo.ComicCategoryRepository
	chapterRepo  repo.ComicChapterRepository
	favoriteRepo repo.ComicFavoriteRepository
	historyRepo  repo.ComicReadHistoryRepository
}

// NewComicService 创建漫画服务实例
func NewComicService(
	comicRepo repo.ComicRepository,
	categoryRepo repo.ComicCategoryRepository,
	chapterRepo repo.ComicChapterRepository,
	favoriteRepo repo.ComicFavoriteRepository,
	historyRepo repo.ComicReadHistoryRepository,
) ComicService {
	return &comicService{
		ExtendedService: base.NewExtendedService[comics.Comic](comicRepo, "comic"),
		comicRepo:       comicRepo,
		categoryRepo:    categoryRepo,
		chapterRepo:     chapterRepo,
		favoriteRepo:    favoriteRepo,
		historyRepo:     historyRepo,
	}
}

// 获取相关漫画推荐
func (s *comicService) GetRelatedComics(ctx context.Context, condition map[string]interface{}, orderBy string, pageNo int, pageSize int) ([]*comics.Comic, int64, error) {
	return s.comicRepo.GetRelatedComics(ctx, condition, orderBy, pageNo, pageSize)
}
