package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"frontapi/pkg/cache"
	"frontapi/pkg/cache/types"
)

// 测试参数
var (
	numItems       = flag.Int("items", 10000, "要测试的缓存项数量")
	numConcurrent  = flag.Int("concurrent", runtime.NumCPU(), "并发协程数量")
	itemSize       = flag.Int("size", 1024, "每个缓存项的大小(字节)")
	runTime        = flag.Int("time", 60, "测试运行时间(秒)")
	readWriteRatio = flag.Int("ratio", 80, "读/写比例(0-100之间，表示读取百分比)")
	useLocalCache  = flag.Bool("local", true, "是否使用本地缓存")
	adapter        = flag.String("adapter", "redis", "要测试的缓存适配器(redis, file, bigcache)")
)

// 统计信息
var (
	totalReads     int64
	totalWrites    int64
	totalReadTime  int64
	totalWriteTime int64
	totalErrors    int64
)

func main() {
	flag.Parse()

	log.Printf("开始缓存性能测试")
	log.Printf("参数: 项数量=%d, 并发数=%d, 项大小=%d字节, 测试时间=%d秒, 读/写比=%d/%d",
		*numItems, *numConcurrent, *itemSize, *runTime, *readWriteRatio, 100-*readWriteRatio)

	// 初始化缓存系统
	cacheConfig := &types.CacheConfig{
		EnableLocalCache: *useLocalCache,
		LocalCacheSize:   100, // 100MB
		LocalCacheTTL:    300, // 5分钟
		DefaultAdapter:   *adapter,
		DefaultTTL:       time.Hour,
	}

	if err := cache.InitCacheWithConfig(cacheConfig); err != nil {
		log.Fatalf("缓存系统初始化失败: %v", err)
	}

	// 生成随机测试数据
	log.Printf("生成随机测试数据...")
	testData := make([][]byte, *numItems)
	for i := 0; i < *numItems; i++ {
		testData[i] = make([]byte, *itemSize)
		rand.Read(testData[i])
	}

	// 获取缓存适配器
	manager := cache.GetGlobalManager()
	if manager == nil {
		log.Fatalf("获取缓存管理器失败")
	}

	cacheAdapter, err := manager.GetDefaultAdapter()
	if err != nil {
		log.Fatalf("获取缓存适配器失败: %v", err)
	}

	// 预热缓存
	log.Printf("预热缓存...")
	ctx := context.Background()
	for i := 0; i < *numItems; i++ {
		key := fmt.Sprintf("test:item:%d", i)
		err := cacheAdapter.Set(ctx, key, testData[i], time.Hour)
		if err != nil {
			log.Printf("预热缓存失败: %v", err)
		}
	}

	// 开始测试
	log.Printf("开始压力测试...")
	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	// 启动工作协程
	for i := 0; i < *numConcurrent; i++ {
		wg.Add(1)
		go worker(i, testData, stopChan, &wg, cacheAdapter)
	}

	// 运行指定时间
	time.Sleep(time.Duration(*runTime) * time.Second)
	close(stopChan)
	wg.Wait()

	// 打印统计结果
	printStats()
}

// worker 执行读写操作的工作协程
func worker(id int, testData [][]byte, stopChan <-chan struct{}, wg *sync.WaitGroup, cacheAdapter types.CacheAdapter) {
	defer wg.Done()

	ctx := context.Background()
	localRand := rand.New(rand.NewSource(time.Now().UnixNano() + int64(id)))

	for {
		select {
		case <-stopChan:
			return
		default:
			// 决定是读还是写
			op := localRand.Intn(100)
			if op < *readWriteRatio {
				// 读操作
				itemIndex := localRand.Intn(*numItems)
				key := fmt.Sprintf("test:item:%d", itemIndex)

				start := time.Now()
				data, err := cacheAdapter.Get(ctx, key)
				elapsed := time.Since(start).Nanoseconds()

				atomic.AddInt64(&totalReadTime, elapsed)
				atomic.AddInt64(&totalReads, 1)

				if err != nil {
					atomic.AddInt64(&totalErrors, 1)
				}

				// 验证数据完整性
				if err == nil && len(data) != *itemSize {
					atomic.AddInt64(&totalErrors, 1)
				}
			} else {
				// 写操作
				itemIndex := localRand.Intn(*numItems)
				key := fmt.Sprintf("test:item:%d", itemIndex)

				start := time.Now()
				err := cacheAdapter.Set(ctx, key, testData[itemIndex], time.Hour)
				elapsed := time.Since(start).Nanoseconds()

				atomic.AddInt64(&totalWriteTime, elapsed)
				atomic.AddInt64(&totalWrites, 1)

				if err != nil {
					atomic.AddInt64(&totalErrors, 1)
				}
			}
		}
	}
}

// 打印统计信息
func printStats() {
	totalOps := totalReads + totalWrites
	avgReadTime := float64(0)
	if totalReads > 0 {
		avgReadTime = float64(totalReadTime) / float64(totalReads) / 1_000_000 // 转为毫秒
	}

	avgWriteTime := float64(0)
	if totalWrites > 0 {
		avgWriteTime = float64(totalWriteTime) / float64(totalWrites) / 1_000_000 // 转为毫秒
	}

	readThroughput := float64(totalReads) / float64(*runTime)
	writeThroughput := float64(totalWrites) / float64(*runTime)
	totalThroughput := float64(totalOps) / float64(*runTime)

	fmt.Println("\n===== 缓存性能测试结果 =====")
	fmt.Printf("测试时间: %d 秒\n", *runTime)
	fmt.Printf("并发协程: %d\n", *numConcurrent)
	fmt.Printf("缓存项数量: %d\n", *numItems)
	fmt.Printf("缓存项大小: %d 字节\n", *itemSize)
	fmt.Printf("使用本地缓存: %v\n", *useLocalCache)
	fmt.Printf("适配器类型: %s\n\n", *adapter)

	fmt.Printf("总读取次数: %d\n", totalReads)
	fmt.Printf("总写入次数: %d\n", totalWrites)
	fmt.Printf("总操作次数: %d\n", totalOps)
	fmt.Printf("错误次数: %d (%.2f%%)\n", totalErrors, float64(totalErrors)*100/float64(totalOps))

	fmt.Printf("平均读取时间: %.3f ms\n", avgReadTime)
	fmt.Printf("平均写入时间: %.3f ms\n", avgWriteTime)
	fmt.Printf("读取吞吐量: %.2f ops/sec\n", readThroughput)
	fmt.Printf("写入吞吐量: %.2f ops/sec\n", writeThroughput)
	fmt.Printf("总吞吐量: %.2f ops/sec\n", totalThroughput)

	fmt.Printf("读取数据量: %.2f MB\n", float64(totalReads*int64(*itemSize))/(1024*1024))
	fmt.Printf("写入数据量: %.2f MB\n", float64(totalWrites*int64(*itemSize))/(1024*1024))
	fmt.Printf("总数据量: %.2f MB\n", float64((totalReads+totalWrites)*int64(*itemSize))/(1024*1024))
}
