/**
 * green 主题配置
 * 基于清新自然的主题
 */
import { ThemeConfig } from '../theme-manager';
import greenVariables from './variables';
import greenDarkVariables from './variables-dark';

// green 亮色主题
export const greenLightTheme: ThemeConfig = {
    name: 'greenLight',
    displayName: 'Green Light',
    shortName: 'green',
    extendName: 'aura-light-green',
    extendThemeStyle: 'primevue/resources/themes/aura-light-green/theme.css',
    code: 'greenlight',
    primary: '#4CAF50',
    isDark: false,
    variables: greenVariables
};

// green 暗色主题
export const greenDarkTheme: ThemeConfig = {
    name: 'greenDark',
    displayName: 'Green Dark',
    shortName: 'green dark',
    extendName: 'aura-dark-green',
    extendThemeStyle: 'primevue/resources/themes/aura-dark-green/theme.css',
    code: 'greendark',
    primary: '#66BB6A',
    isDark: true,
    variables: greenDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default greenLightTheme;