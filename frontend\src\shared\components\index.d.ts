/**
 * 全局组件类型声明
 */

declare module '@/shared/components/GlobalLoading.vue' {
    import { DefineComponent } from 'vue'
    const component: DefineComponent<{
        text: string
    }, {}, any>
    export default component
}

declare module '@/shared/components/GlobalNotifications.vue' {
    import { DefineComponent } from 'vue'

    interface Notification {
        id: string
        type: 'success' | 'error' | 'warning' | 'info'
        title: string
        message: string
        duration?: number
    }

    const component: DefineComponent<{
        notifications: Notification[]
    }, {}, any>
    export default component
}

declare module '@/shared/components/DebugPanel.vue' {
    import { DefineComponent } from 'vue'
    const component: DefineComponent<{}, {}, any>
    export default component
}

declare module '@/shared/components/DynamicStyle.vue' {
    import { DefineComponent } from 'vue'
    const component: DefineComponent<{
        css: string
    }, {}, any>
    export default component
} 