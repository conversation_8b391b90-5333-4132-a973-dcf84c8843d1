<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-bold">标签管理</span>
          <el-button type="primary" @click="handleAdd">新增标签</el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <div class="search-form mb-4">
        <el-form :inline="true" :model="queryParams" @keyup.enter="handleSearch">
          <el-form-item label="标签名称">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入标签名称"
              clearable
              @clear="handleSearch"
            />
          </el-form-item>
          <el-form-item label="标签类型">
            <el-select v-model="queryParams.type" placeholder="请选择标签类型" clearable @clear="handleSearch" style="width: 200px;">
              <el-option label="视频标签" :value="1" />
              <el-option label="短视频标签" :value="2" />
              <el-option label="帖子标签" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
            <el-button @click="fetchTagList">刷新</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tagList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column label="标签名称" min-width="120">
          <template #default="{ row }">
            {{ row.name }}
          </template>
        </el-table-column>
        <el-table-column label="标签类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.type === 1" type="success">视频标签</el-tag>
            <el-tag v-if="row.type === 2" type="warning">短视频标签</el-tag>
            <el-tag v-if="row.type === 3" type="danger">帖子标签</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="使用次数" width="100" align="center" />
        <el-table-column prop="sort_order" label="排序" width="80" align="center" />
        <el-table-column prop="created_at" label="创建时间" width="180" show-overflow-tooltip />
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pageParams.pageNo"
          :page-size="pageParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 标签表单对话框 -->
    <tag-dialog
      :visible="dialogVisible"
      :tag="currentTag"
      @close="dialogVisible = false"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import TagDialog from './components/TagDialog.vue';
import { getTagList, getTagDetail, createTag, updateTag, deleteTag } from '@/service/api/system/tags';
import type { Tag, TagParams, CreateTagRequest, UpdateTagRequest, TagListResponse } from '@/types/tags';

// API响应类型定义
interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// 加载标志
const loading = ref(false);
const dialogVisible = ref(false);
const currentTag = ref<Tag | null>(null);

// 查询参数
const queryParams = reactive({
  name: '',
  type: undefined as number | undefined
});

// 分页参数
const pageParams = reactive({
  pageNo: 1,
  pageSize: 10
});

// 总数
const total = ref(0);

// 标签列表
const tagList = ref<Tag[]>([]);

// 生命周期钩子
onMounted(() => {
  fetchTagList();
});

// 获取标签列表
const fetchTagList = async () => {
  loading.value = true;
  try {
    const params: TagParams = {
      page: pageParams,
      data: queryParams
    };

    const res = await getTagList(params) as ApiResponse<TagListResponse>;
    if (res.code === 200 || res.code === 2000) {
      tagList.value = res.data.list || [];
      total.value = res.data.total || 0;
    } else {
      ElMessage.error(res.message || '获取标签列表失败');
    }
  } catch (error) {
    console.error('获取标签列表失败:', error);
    ElMessage.error('获取标签列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  pageParams.pageNo = 1;
  fetchTagList();
};

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    name: '',
    type: undefined
  });
  handleSearch();
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pageParams.pageSize = val;
  fetchTagList();
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  pageParams.pageNo = val;
  fetchTagList();
};

// 新增标签
const handleAdd = () => {
  currentTag.value = null;
  dialogVisible.value = true;
};

// 编辑标签
const handleEdit = async (row: Tag) => {
  try {
    const res = await getTagDetail(row.id) as ApiResponse<Tag>;
    if (res.code === 200 || res.code === 2000) {
      currentTag.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res.message || '获取标签详情失败');
    }
  } catch (error) {
    console.error('获取标签详情失败:', error);
    ElMessage.error('获取标签详情失败');
  }
};

// 删除标签
const handleDelete = (row: Tag) => {
  ElMessageBox.confirm(
    `确定要删除标签"${row.name}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        const res = await deleteTag(row.id) as ApiResponse<any>;
        if (res.code === 200 || res.code === 2000) {
          ElMessage.success('删除成功');
          fetchTagList();
        } else {
          ElMessage.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除标签失败:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除');
    });
};

// 对话框操作成功回调
const handleDialogSuccess = async (formData: CreateTagRequest | UpdateTagRequest) => {
  try {
    let res;
    if ('id' in formData && formData.id && formData.id.length > 0) {
      // 更新
      res = await updateTag({data: formData}) as ApiResponse<any>;
    } else {
      // 新增
      res = await createTag({data: formData}) as ApiResponse<any>;
    }

    if (res.code === 200 || res.code === 2000) {
      ElMessage.success(`${'id' in formData ? '更新' : '新增'}成功`);
      dialogVisible.value = false;
      fetchTagList();
    } else {
      ElMessage.error(res.message || `${'id' in formData ? '更新' : '新增'}失败`);
    }
  } catch (error) {
    console.error(`${'id' in formData ? '更新' : '新增'}标签失败:`, error);
    ElMessage.error(`${'id' in formData ? '更新' : '新增'}失败`);
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
