package comics

import (
	"frontapi/internal/models/comics"
	repo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
)

// UpdateReadHistoryRequest 更新阅读历史请求
type UpdateReadHistoryRequest struct {
	PageNumber int `json:"page_number" validate:"required"`
}

// ComicReadHistoryService 漫画阅读历史服务接口
type ComicReadHistoryService interface {
	base.IExtendedService[comics.ComicReadHistory]
}

// comicReadHistoryService 漫画阅读历史服务实现
type comicReadHistoryService struct {
	*base.ExtendedService[comics.ComicReadHistory]
	historyRepo repo.ComicReadHistoryRepository
	comicRepo   repo.ComicRepository
	chapterRepo repo.ComicChapterRepository
}

// NewComicReadHistoryService 创建漫画阅读历史服务实例
func NewComicReadHistoryService(
	historyRepo repo.ComicReadHistoryRepository,
	comicRepo repo.ComicRepository,
	chapterRepo repo.ComicChapterRepository,
) ComicReadHistoryService {
	return &comicReadHistoryService{
		ExtendedService: base.NewExtendedService[comics.ComicReadHistory](historyRepo, "comic_read_history"),
		historyRepo:     historyRepo,
		comicRepo:       comicRepo,
		chapterRepo:     chapterRepo,
	}
}
