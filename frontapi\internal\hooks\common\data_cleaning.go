package common

import (
	"context"
	"errors"
	"reflect"
	"strings"
)

// DataCleaningHook 数据清洗钩子
type DataCleaningHook struct {
	TrimFields    []string // 需要去除空格的字段
	LowerFields   []string // 需要转换为小写的字段
	UpperFields   []string // 需要转换为大写的字段
	DefaultValues map[string]interface{} // 默认值设置
}

// Execute 执行数据清洗
func (h *DataCleaningHook) Execute(ctx context.Context, data interface{}) error {
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return errors.New("数据必须是结构体类型")
	}

	// 去除空格
	for _, field := range h.TrimFields {
		if fieldValue := v.FieldByName(field); fieldValue.IsValid() && fieldValue.CanSet() {
			if fieldValue.Kind() == reflect.String {
				fieldValue.SetString(strings.TrimSpace(fieldValue.String()))
			}
		}
	}

	// 转换为小写
	for _, field := range h.LowerFields {
		if fieldValue := v.FieldByName(field); fieldValue.IsValid() && fieldValue.CanSet() {
			if fieldValue.Kind() == reflect.String {
				fieldValue.SetString(strings.ToLower(fieldValue.String()))
			}
		}
	}

	// 转换为大写
	for _, field := range h.UpperFields {
		if fieldValue := v.FieldByName(field); fieldValue.IsValid() && fieldValue.CanSet() {
			if fieldValue.Kind() == reflect.String {
				fieldValue.SetString(strings.ToUpper(fieldValue.String()))
			}
		}
	}

	// 设置默认值
	for field, defaultValue := range h.DefaultValues {
		if fieldValue := v.FieldByName(field); fieldValue.IsValid() && fieldValue.CanSet() {
			if fieldValue.IsZero() {
				fieldValue.Set(reflect.ValueOf(defaultValue))
			}
		}
	}

	return nil
}

// NewDataCleaningHook 创建数据清洗钩子
func NewDataCleaningHook() *DataCleaningHook {
	return &DataCleaningHook{
		TrimFields:    []string{},
		LowerFields:   []string{},
		UpperFields:   []string{},
		DefaultValues: make(map[string]interface{}),
	}
}

// WithTrimFields 设置需要去除空格的字段
func (h *DataCleaningHook) WithTrimFields(fields ...string) *DataCleaningHook {
	h.TrimFields = append(h.TrimFields, fields...)
	return h
}

// WithLowerFields 设置需要转换为小写的字段
func (h *DataCleaningHook) WithLowerFields(fields ...string) *DataCleaningHook {
	h.LowerFields = append(h.LowerFields, fields...)
	return h
}

// WithUpperFields 设置需要转换为大写的字段
func (h *DataCleaningHook) WithUpperFields(fields ...string) *DataCleaningHook {
	h.UpperFields = append(h.UpperFields, fields...)
	return h
}

// WithDefaultValue 设置字段默认值
func (h *DataCleaningHook) WithDefaultValue(field string, value interface{}) *DataCleaningHook {
	h.DefaultValues[field] = value
	return h
}