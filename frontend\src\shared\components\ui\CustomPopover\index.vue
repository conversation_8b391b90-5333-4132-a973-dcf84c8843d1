<template>
  <div class="custom-popover-wrapper">
    <!-- 触发器插槽 -->
    <div
      ref="triggerRef"
      class="popover-trigger"
      @click="handleTriggerClick"
      @mouseenter="handleTriggerMouseEnter"
      @mouseleave="handleTriggerMouseLeave"
    >
      <slot name="trigger"></slot>
    </div>

    <!-- Popover 内容 -->
    <Teleport to="body">
      <Transition name="popover-fade">
        <div
          v-if="visible"
          ref="popoverRef"
          class="custom-popover"
          :class="[
            `popover-${placement}`,
            { 'popover-arrow': showArrow }
          ]"
          :style="popoverStyle"
          @mouseenter="handlePopoverMouseEnter"
          @mouseleave="handlePopoverMouseLeave"
        >
          <!-- 箭头 -->
          <div v-if="showArrow" class="popover-arrow-element" :class="`arrow-${placement}`"></div>
          
          <!-- 内容 -->
          <div class="popover-content">
            <!-- Header 插槽 -->
            <div v-if="$slots.header" class="popover-header">
              <slot name="header"></slot>
            </div>

            <!-- Body 插槽 (默认插槽) -->
            <div class="popover-body">
              <slot name="body">
                <slot></slot>
              </slot>
            </div>

            <!-- Footer 插槽 -->
            <div v-if="$slots.footer" class="popover-footer">
              <slot name="footer"></slot>
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';

// Props
interface Props {
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end' | 'right-start' | 'right-end';
  trigger?: 'click' | 'hover' | 'manual';
  showArrow?: boolean;
  offset?: number;
  disabled?: boolean;
  hideDelay?: number;
  showDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  placement: 'bottom',
  trigger: 'click',
  showArrow: true,
  offset: 8,
  disabled: false,
  hideDelay: 200,
  showDelay: 0
});

// Emits
interface Emits {
  'show': [];
  'hide': [];
  'before-show': [];
  'before-hide': [];
}

const emit = defineEmits<Emits>();

// State
const visible = ref(false);
const triggerRef = ref<HTMLElement>();
const popoverRef = ref<HTMLElement>();
const popoverStyle = ref({});
const showTimer = ref<NodeJS.Timeout | null>(null);
const hideTimer = ref<NodeJS.Timeout | null>(null);

// Methods
const clearTimers = () => {
  if (showTimer.value) {
    clearTimeout(showTimer.value);
    showTimer.value = null;
  }
  if (hideTimer.value) {
    clearTimeout(hideTimer.value);
    hideTimer.value = null;
  }
};

const calculatePosition = (customTrigger?: HTMLElement) => {
  const trigger = customTrigger || triggerRef.value;
  if (!trigger || !popoverRef.value) return;

  const triggerRect = trigger.getBoundingClientRect();
  const popoverRect = popoverRef.value.getBoundingClientRect();
  const { innerWidth, innerHeight } = window;
  
  let top = 0;
  let left = 0;

  // 根据 placement 计算位置
  switch (props.placement) {
    case 'top':
      top = triggerRect.top - popoverRect.height - props.offset;
      left = triggerRect.left + (triggerRect.width - popoverRect.width) / 2;
      break;
    case 'top-start':
      top = triggerRect.top - popoverRect.height - props.offset;
      left = triggerRect.left;
      break;
    case 'top-end':
      top = triggerRect.top - popoverRect.height - props.offset;
      left = triggerRect.right - popoverRect.width;
      break;
    case 'bottom':
      top = triggerRect.bottom + props.offset;
      left = triggerRect.left + (triggerRect.width - popoverRect.width) / 2;
      break;
    case 'bottom-start':
      top = triggerRect.bottom + props.offset;
      left = triggerRect.left;
      break;
    case 'bottom-end':
      top = triggerRect.bottom + props.offset;
      left = triggerRect.right - popoverRect.width;
      break;
    case 'left':
      top = triggerRect.top + (triggerRect.height - popoverRect.height) / 2;
      left = triggerRect.left - popoverRect.width - props.offset;
      break;
    case 'left-start':
      top = triggerRect.top;
      left = triggerRect.left - popoverRect.width - props.offset;
      break;
    case 'left-end':
      top = triggerRect.bottom - popoverRect.height;
      left = triggerRect.left - popoverRect.width - props.offset;
      break;
    case 'right':
      top = triggerRect.top + (triggerRect.height - popoverRect.height) / 2;
      left = triggerRect.right + props.offset;
      break;
    case 'right-start':
      top = triggerRect.top;
      left = triggerRect.right + props.offset;
      break;
    case 'right-end':
      top = triggerRect.bottom - popoverRect.height;
      left = triggerRect.right + props.offset;
      break;
  }

  // 边界检测和调整
  if (left < 0) left = 8;
  if (left + popoverRect.width > innerWidth) left = innerWidth - popoverRect.width - 8;
  if (top < 0) top = 8;
  if (top + popoverRect.height > innerHeight) top = innerHeight - popoverRect.height - 8;

  popoverStyle.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    zIndex: 9999
  };
};

const show = () => {
  if (props.disabled || visible.value) return;
  
  clearTimers();
  
  if (props.showDelay > 0) {
    showTimer.value = setTimeout(() => {
      doShow();
    }, props.showDelay);
  } else {
    doShow();
  }
};

const hide = () => {
  if (!visible.value) return;
  
  clearTimers();
  
  if (props.hideDelay > 0) {
    hideTimer.value = setTimeout(() => {
      doHide();
    }, props.hideDelay);
  } else {
    doHide();
  }
};

const doShow = (customTrigger?: HTMLElement) => {
  emit('before-show');
  visible.value = true;
  nextTick(() => {
    calculatePosition(customTrigger);
    emit('show');
  });
};

const doHide = () => {
  emit('before-hide');
  visible.value = false;
  emit('hide');
};

const toggle = () => {
  if (visible.value) {
    hide();
  } else {
    show();
  }
};

// 使用自定义触发器显示popover
const showWithTrigger = (customTrigger: HTMLElement) => {
  if (props.disabled || visible.value) return;
  
  clearTimers();
  emit('before-show');
  visible.value = true;
  nextTick(() => {
    calculatePosition(customTrigger);
    emit('show');
  });
};

// 包装函数用于事件监听器
const handleResize = () => calculatePosition();
const handleScroll = () => calculatePosition();

// Event handlers
const handleTriggerClick = () => {
  if (props.trigger === 'click') {
    toggle();
  }
};

const handleTriggerMouseEnter = () => {
  if (props.trigger === 'hover') {
    show();
  }
};

const handleTriggerMouseLeave = () => {
  if (props.trigger === 'hover') {
    hide();
  }
};

const handlePopoverMouseEnter = () => {
  if (props.trigger === 'hover') {
    clearTimers();
  }
};

const handlePopoverMouseLeave = () => {
  if (props.trigger === 'hover') {
    hide();
  }
};

const handleClickOutside = (event: Event) => {
  if (
    visible.value &&
    triggerRef.value &&
    popoverRef.value &&
    !triggerRef.value.contains(event.target as Node) &&
    !popoverRef.value.contains(event.target as Node)
  ) {
    hide();
  }
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleScroll);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', handleResize);
  window.removeEventListener('scroll', handleScroll);
  clearTimers();
});

// Watch for position updates
watch(visible, (newVisible) => {
  if (newVisible) {
    nextTick(calculatePosition);
  }
});

// Expose methods
defineExpose({
  show,
  hide,
  toggle,
  showWithTrigger
});
</script>

<style lang="scss" scoped>
.custom-popover-wrapper {
  display: inline-block;
}

.popover-trigger {
  display: inline-block;
}

.custom-popover {
  background: var(--surface-overlay);
  border: 1px solid var(--surface-border);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  position: fixed;
  z-index: 9999;
  
  .popover-content {
    padding: 0;

    .popover-header {
      padding: 1rem 1rem 0.5rem;
      border-bottom: 1px solid var(--surface-border);
      background: var(--surface-50);
      border-radius: 8px 8px 0 0;

      // 如果有标题样式
      h1, h2, h3, h4, h5, h6 {
        margin: 0;
        color: var(--text-color);
        font-weight: 600;
      }
    }

    .popover-body {
      // 默认body样式，子组件可以覆盖
    }

    .popover-footer {
      padding: 0.5rem 1rem 1rem;
      border-top: 1px solid var(--surface-border);
      background: var(--surface-50);
      border-radius: 0 0 8px 8px;
      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;
    }
    
    // 子菜单标题样式
    .submenu-title {
      font-size: 0.85rem;
      font-weight: 600;
      color: var(--text-color);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
      
    // 单列子菜单
    .submenu-single-column {
      min-width: 280px;

      .submenu-items {
        padding: 0.25rem 0;

        .submenu-item-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem;
          color: var(--text-color);
          text-decoration: none;
          transition: background 0.2s ease;

          &:hover {
            background: var(--surface-hover);
          }

          &.active {
            background: var(--primary-50);
            color: var(--primary-color);

            .submenu-icon i {
              color: var(--primary-color);
            }
          }

          .submenu-icon {
            width: 20px;
            margin-right: 0.75rem;
            color: var(--text-color-secondary);

            i {
              font-size: 0.9rem;
            }
          }

          .submenu-label {
            font-size: 0.9rem;
            font-weight: 500;
          }
        }
      }
    }

    // 通用子菜单项样式
    .submenu-items {
      padding: 0.25rem 0;

      .submenu-item-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: var(--text-color);
        text-decoration: none;
        transition: background 0.2s ease;

        &:hover {
          background: var(--surface-hover);
        }

        &.active {
          background: var(--primary-50);
          color: var(--primary-color);

          .submenu-icon i {
            color: var(--primary-color);
          }
        }

        .submenu-icon {
          width: 20px;
          margin-right: 0.75rem;
          color: var(--text-color-secondary);

          i {
            font-size: 0.9rem;
          }
        }

        .submenu-label {
          font-size: 0.9rem;
          font-weight: 500;
        }
      }
    }

    // 多列子菜单 (MegaMenu 风格)
      .submenu-mega {
        min-width: 600px;
        
        .submenu-columns {
          display: flex;
          padding: 0.5rem 1rem 1rem;
          gap: 2rem;
          
          .submenu-column {
            flex: 1;
            
            .submenu-item-link {
              display: flex;
              align-items: center;
              padding: 0.75rem 1rem;
              color: var(--text-color);
              text-decoration: none;
              transition: background 0.2s ease;
              border-radius: 6px;
              margin-bottom: 0.25rem;
              
              &:hover {
                background: var(--surface-hover);
              }
              
              &.active {
                background: var(--primary-50);
                color: var(--primary-color);
                
                .submenu-icon i {
                  color: var(--primary-color);
                }
              }
              
              .submenu-icon {
                width: 24px;
                margin-right: 0.75rem;
                color: var(--text-color-secondary);
                
                i {
                  font-size: 1rem;
                }
              }
              
              .submenu-label {
                font-size: 0.9rem;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
  
  // 箭头样式
  .popover-arrow {
    .popover-arrow-element {
      position: absolute;
      width: 0;
      height: 0;
      border: 6px solid transparent;
      
      &.arrow-top {
        bottom: -12px;
        left: 50%;
        transform: translateX(-50%);
        border-top-color: var(--surface-overlay);
        border-bottom: none;
      }
      
      &.arrow-bottom {
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        border-bottom-color: var(--surface-overlay);
        border-top: none;
      }
      
      &.arrow-left {
        right: -12px;
        top: 50%;
        transform: translateY(-50%);
        border-left-color: var(--surface-overlay);
        border-right: none;
      }
      
      &.arrow-right {
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        border-right-color: var(--surface-overlay);
        border-left: none;
      }
    }
  }

/* 过渡动画 */
.popover-fade-enter-active,
.popover-fade-leave-active {
  transition: all 0.2s ease;
}

.popover-fade-enter-from,
.popover-fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

</style>
