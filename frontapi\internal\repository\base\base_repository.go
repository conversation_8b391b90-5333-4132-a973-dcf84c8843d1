package base

import (
	"context"
	"errors"
	"reflect"

	"frontapi/internal/models"

	"gorm.io/gorm"
)

// ConditionApplier 条件应用接口
// 实现此接口的仓库可以自定义条件应用逻辑
type ConditionApplier interface {
	ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB
}

// BaseRepository 通用仓库接口
// T 表示模型类型，必须继承自BaseModel
type BaseRepository[T models.BaseModelConstraint] interface {
	// 基础CRUD操作
	Create(ctx context.Context, entity *T) error
	FindByID(ctx context.Context, id string) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id string) error
	BatchDelete(ctx context.Context, ids []string) error
	UpdateColumn(ctx context.Context, condition map[string]interface{}, column string, value interface{}) error
	DeleteByCondition(ctx context.Context, condition map[string]interface{}) error

	UpdateById(ctx context.Context, id string, entity *T) error

	// 列表查询
	List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)
	FindAll(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error)

	FindOne(ctx context.Context, query interface{}, args ...interface{}) (*T, error)
	// 条件查询
	FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error)
	FindOneByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) (*T, error)

	// 计数操作
	Count(ctx context.Context, condition map[string]interface{}) (int64, error)
	Exists(ctx context.Context, condition map[string]interface{}) (bool, error)
	FindByIDs(ctx context.Context, ids []string) ([]*T, error)
	// 批量操作
	BatchCreate(ctx context.Context, entities []*T) (int, error)
	BatchUpdate(ctx context.Context, entities []*T) error
	UpdateCountByIDs(ctx context.Context, ids []string, field string, count int64) error
	BatchUpdateStatus(ctx context.Context, ids []string, status int) error
	// 状态操作
	UpdateStatus(ctx context.Context, id string, status int) error

	// 获取数据库实例
	GetDB() *gorm.DB
	GetDBWithContext(ctx context.Context) *gorm.DB
	GetTableName() string

	// 设置调用者实例，用于支持条件应用的自定义
	SetCaller(caller interface{})
}

// baseRepository 通用仓库实现
type baseRepository[T models.BaseModelConstraint] struct {
	db     *gorm.DB
	caller interface{} // 用于存储具体的仓库实例，支持条件应用的自定义
}

// NewBaseRepository 创建通用仓库实例
func NewBaseRepository[T models.BaseModelConstraint](db *gorm.DB) BaseRepository[T] {
	return &baseRepository[T]{
		db:     db,
		caller: nil,
	}
}

// SetCaller 设置调用者实例，用于支持条件应用的自定义
func (r *baseRepository[T]) SetCaller(caller interface{}) {
	r.caller = caller
}

// NewBaseRepositoryWithCaller 创建带有调用者设置的基础仓库实例
// 这是一个辅助函数，用于简化仓库的创建和配置过程
func NewBaseRepositoryWithCaller[T models.BaseModelConstraint](db *gorm.DB, caller interface{}) BaseRepository[T] {
	repo := &baseRepository[T]{
		db:     db,
		caller: caller,
	}
	return repo
}

// GetDB 获取数据库实例
func (r *baseRepository[T]) GetDB() *gorm.DB {
	return r.db
}

// GetDBWithContext 获取带上下文的数据库实例
func (r *baseRepository[T]) GetDBWithContext(ctx context.Context) *gorm.DB {
	return r.db.WithContext(ctx)
}

// Create 创建实体
func (r *baseRepository[T]) Create(ctx context.Context, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}
	return r.db.WithContext(ctx).Create(entity).Error
}

// FindByID 根据ID查找实体
func (r *baseRepository[T]) FindByID(ctx context.Context, id string) (*T, error) {
	if id == "" {
		return nil, errors.New("id cannot be empty")
	}

	var entity T
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &entity, nil
}

// Update 更新实体
func (r *baseRepository[T]) Update(ctx context.Context, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}
	return r.db.WithContext(ctx).Save(entity).Error
}

// UpdateById 根据ID更新实体
func (r *baseRepository[T]) UpdateById(ctx context.Context, id string, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}
	// 使用Save方法确保所有字段都被更新，包括null.String的NULL值
	// 注意：需要设置ID以确保正确更新
	if setter, ok := any(entity).(interface{ SetID(string) }); ok {
		setter.SetID(id)
	}
	return r.db.WithContext(ctx).Save(entity).Error
}

// UpdateColumn 更新实体的指定列
func (r *baseRepository[T]) UpdateColumn(ctx context.Context, condition map[string]interface{}, column string, value interface{}) error {
	if len(condition) == 0 {
		return errors.New("condition cannot be empty")
	}
	return r.db.WithContext(ctx).Where(condition).Update(column, value).Error
}

// DeleteByCondition 根据条件删除实体
func (r *baseRepository[T]) DeleteByCondition(ctx context.Context, condition map[string]interface{}) error {
	if len(condition) == 0 {
		return errors.New("condition cannot be empty")
	}
	var entity T
	return r.db.WithContext(ctx).Where(condition).Delete(&entity).Error
}

// Delete 删除实体
func (r *baseRepository[T]) Delete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("id cannot be empty")
	}
	var entity T
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&entity).Error
}

// BatchDelete 批量删除
func (r *baseRepository[T]) BatchDelete(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return errors.New("ids cannot be empty")
	}

	// 使用事务确保原子性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var entity T
		return tx.Where("id IN ?", ids).Delete(&entity).Error
	})
}

// List 获取列表
func (r *baseRepository[T]) List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error) {
	var entities []*T
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	query = r.applyConditions(query, condition)

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	err = query.Find(&entities).Error
	if err != nil {
		return nil, 0, err
	}

	return entities, total, nil
}

// FindAll 查找所有实体
func (r *baseRepository[T]) FindAll(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error) {
	var entities []*T
	query := r.db.WithContext(ctx).Model(new(T))
	if orderBy != "" {
		query = query.Order(orderBy)
	}
	query = r.applyConditions(query, condition)
	err := query.Find(&entities).Error
	return entities, err
}

// FindOne 根据条件查找单个实体
func (r *baseRepository[T]) FindOne(ctx context.Context, field interface{}, args ...interface{}) (*T, error) {
	var entity T
	query := r.db.WithContext(ctx).Model(new(T))
	query = query.Where(field, args...)
	err := query.First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &entity, nil
}

// FindByCondition 根据条件查找实体列表
func (r *baseRepository[T]) FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error) {
	var entities []*T
	query := r.db.WithContext(ctx).Model(new(T))
	query = r.applyConditions(query, condition)
	if orderBy != "" {
		query = query.Order(orderBy)
	}
	err := query.Find(&entities).Error
	return entities, err
}

// FindOneByCondition 根据条件查找单个实体
func (r *baseRepository[T]) FindOneByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) (*T, error) {
	var entity T
	query := r.db.WithContext(ctx).Model(new(T))
	query = r.applyConditions(query, condition)
	if orderBy != "" {
		query = query.Order(orderBy)
	}
	err := query.First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &entity, nil
}

// Count 计数
func (r *baseRepository[T]) Count(ctx context.Context, condition map[string]interface{}) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(new(T))
	query = r.applyConditions(query, condition)
	err := query.Count(&count).Error
	return count, err
}

// Exists 检查是否存在
func (r *baseRepository[T]) Exists(ctx context.Context, condition map[string]interface{}) (bool, error) {
	count, err := r.Count(ctx, condition)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// BatchCreate 批量创建
func (r *baseRepository[T]) BatchCreate(ctx context.Context, entities []*T) (int, error) {
	if len(entities) == 0 {
		return 0, errors.New("entities cannot be empty")
	}

	// 使用事务确保原子性
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return tx.Create(entities).Error
	})
	if err != nil {
		return 0, err
	}
	return len(entities), nil
}

// BatchUpdate 批量更新
func (r *baseRepository[T]) BatchUpdate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return errors.New("entities cannot be empty")
	}

	// 使用事务进行批量更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, entity := range entities {
			if err := tx.Save(entity).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BatchUpdateStatus 批量更新状态
func (r *baseRepository[T]) BatchUpdateStatus(ctx context.Context, ids []string, status int) error {
	if len(ids) == 0 {
		return errors.New("ids cannot be empty")
	}
	//通过new(T)获取状态字段是status
	entity := new(T)
	ref := reflect.ValueOf(entity).Elem()
	method := ref.MethodByName("GetStatusField")
	field := method.Call(nil)[0].String()
	if field == "" {
		field = "status"
	}
	return r.db.WithContext(ctx).Model(entity).Where("id IN ?", ids).Update(field, status).Error
}

// UpdateStatus 更新状态
func (r *baseRepository[T]) UpdateStatus(ctx context.Context, id string, status int) error {
	entity := new(T)
	ref := reflect.ValueOf(entity).Elem()
	method := ref.MethodByName("GetStatusField")
	field := method.Call(nil)[0].String()
	if field == "" {
		field = "status"
	}
	return r.db.WithContext(ctx).Model(entity).Where("id = ?", id).Update(field, status).Error
}

func (r *baseRepository[T]) FindByIDs(ctx context.Context, ids []string) ([]*T, error) {
	var entities []*T
	err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&entities).Error
	return entities, err
}

// applyConditions 应用查询条件
// 如果调用者实现了ConditionApplier接口，则使用自定义的条件应用逻辑
func (r *baseRepository[T]) applyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	return r.applyConditionsWithCaller(query, condition, r.caller)
}

// applyConditionsWithCaller 带调用者的条件应用方法
// caller参数用于传递具体的仓库实例，以便检查是否实现了ConditionApplier接口
func (r *baseRepository[T]) applyConditionsWithCaller(query *gorm.DB, condition map[string]interface{}, caller interface{}) *gorm.DB {
	// 首先检查调用者是否实现了ConditionApplier接口
	if caller != nil {
		if applier, ok := caller.(ConditionApplier); ok {
			return applier.ApplyConditions(query, condition)
		}
	}

	// 如果没有传入caller，或者caller没有实现ConditionApplier接口，则使用默认逻辑
	return r.defaultApplyConditions(query, condition)
}

// defaultApplyConditions 默认的条件应用逻辑
func (r *baseRepository[T]) defaultApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	if condition == nil || len(condition) == 0 {
		return query
	}

	// 获取模型结构信息用于字段映射
	modelInfo := r.getModelInfo()

	for key, value := range condition {
		// 跳过空值和无效值
		if r.isEmptyValue(value) {
			continue
		}

		// 应用智能条件匹配
		query = r.applySmartCondition(query, key, value, modelInfo)
	}

	return query
}

// 通过反射，获取所有字段类型是string,null.string,则使用like查询,先获取字段名用 like or 拼接,然后使用Where拼接
func (r *baseRepository[T]) likeQuery(query *gorm.DB, value interface{}) *gorm.DB {
	if value == nil || value == "" {
		return query
	}
	ref := reflect.ValueOf(new(T)).Elem()
	fields := ref.NumField()
	likeQuery := ""
	for i := 0; i < fields; i++ {
		field := ref.Field(i)
		if field.Kind() == reflect.String || field.Kind() == reflect.Ptr { //类型是string,null.String
			likeQuery += field.Type().Name() + " LIKE ? OR "
		}
	}
	likeQuery = likeQuery[:len(likeQuery)-3]
	query = query.Where(likeQuery, "%"+value.(string)+"%")
	return query
}

// 保留原有的简单时间查询方法作为兼容
//
//key:created_at_start created_at_end updated_at_start updated_at_end,regtime_start regtime_end或者其他{column}_start {column}_end
func (r *baseRepository[T]) timeRangeQuery(query *gorm.DB, key string, value interface{}) *gorm.DB {
	// 使用智能时间范围查询
	modelInfo := r.getModelInfo()
	return r.smartTimeRangeQuery(query, key, value, modelInfo)
}

// UpdateCountByIDs 批量更新计数字段
func (r *baseRepository[T]) UpdateCountByIDs(ctx context.Context, ids []string, field string, count int64) error {
	if len(ids) == 0 {
		return errors.New("ids cannot be empty")
	}
	if field == "" {
		return errors.New("field cannot be empty")
	}

	// 使用事务确保原子性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var entity T
		return tx.Model(&entity).Where("id IN ?", ids).Update(field, gorm.Expr(field+" + ?", count)).Error
	})
}

// GetModelName 获取模型名称（用于日志等）
func (r *baseRepository[T]) GetModelName() string {
	var entity T
	t := reflect.TypeOf(entity)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.Name()
}

// 获取表名
func (r *baseRepository[T]) GetTableName() string {
	var entity T
	t := reflect.TypeOf(entity)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.Name()
}
