package videos

import "frontapi/internal/typings"

// VideoInfo 视频信息 - 前端响应类型
type VideoInfo struct {
	ID            string `json:"id"`
	Title         string `json:"title"`
	Description   string `json:"description"`
	Cover         string `json:"cover"`
	URL           string `json:"url"`
	Duration      int64  `json:"duration"` // 格式化后的时长
	ViewCount     uint64 `json:"viewCount"`
	LikeCount     uint64 `json:"likeCount"`
	CommentCount  uint64 `json:"commentCount"`
	ShareCount    uint64 `json:"shareCount"`
	Heat          uint64 `json:"heat"`
	CategoryID    string `json:"categoryID"`
	CategoryName  string `json:"categoryName"`
	CreatorID     string `json:"creatorID"`
	CreatorName   string `json:"creatorName"`
	CreatorAvatar string `json:"creatorAvatar"`
	IsFeatured    int8   `json:"isFeatured"`
	Status        int8   `json:"status"`
	CreatedAt     string `json:"createdAt"`
	UpdatedAt     string `json:"updatedAt"`
	IsOwner       bool   `json:"isOwner"`
	IsLiked       bool   `json:"isLiked"`
	IsFavorite    bool   `json:"isFavorite"`
}

// VideoCategoryInfo 视频分类信息 - 前端响应类型
type VideoCategoryInfo struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Icon        string `json:"icon"`
	Cover       string `json:"cover"`
	Description string `json:"description"`
	Sort        int    `json:"sort"`
	ViewCount   uint64 `json:"viewCount"`
	VideoCount  uint64 `json:"videoCount"`
	IsFeatured  int8   `json:"isFeatured"`
	Status      int8   `json:"status"`
	CreatedAt   string `json:"createdAt"`
}

// VideoListResponse 视频列表响应 - 前端响应类型
type VideoListResponse struct {
	typings.BaseListResponse
	List []VideoInfo `json:"list"`
}

// VideoCategoryListResponse 视频分类列表响应 - 前端响应类型
type VideoCategoryListResponse struct {
	typings.BaseListResponse
	List []VideoCategoryInfo `json:"list"`
}

type VideoAlbumInfo struct {
	ID            string `json:"id"`
	Title         string `json:"title"`
	Description   string `json:"description"`
	CreatorID     string `json:"creatorID"`
	CreatorName   string `json:"creatorName"`
	Cover         string `json:"cover"`
	CreatorAvatar string `json:"creatorAvatar"`
	ViewCount     uint64 `json:"viewCount"`
	VideoCount    uint64 `json:"videoCount"`
	Heat          uint64 `json:"heat"`
}

type VideoAlbumListResponse struct {
	typings.BaseListResponse
	List []VideoAlbumInfo `json:"list"`
}

type VideoCommentInfo struct {
	UserID       string `json:"userId"`       // 用户ID
	Content      string `json:"content"`      // 评论内容
	UserNickname string `json:"userNickname"` // 用户昵称
	UserAvatar   string `json:"userAvatar"`   // 用户头像
	ParentID     string `json:"parentId"`     // 父级ID
	EntityID     string `json:"entityId"`     // 关联实体ID
	EntityType   int8   `json:"entityType"`   // 关联实体类型
	RelationID   string `json:"relationId"`   // 关联ID
	Heat         int64  `json:"heat"`         // 热度
	LikeCount    int64  `json:"likeCount"`    // 点赞数
	ReplyCount   int64  `json:"replyCount"`   // 回复数
	CreatedAt    string `json:"createdAt"`    // 创建时间
	UpdatedAt    string `json:"updatedAt"`    // 更新时间
	Status       int8   `json:"status"`       // 状态：0-禁用，1-正常
}
type VideoCommentListResponse struct {
	typings.BaseListResponse
	List []VideoCommentInfo `json:"list"`
}
