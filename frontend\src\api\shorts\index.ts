import { post } from '@/shared/composables'
import { corePost } from '@/shared/composables'

// 短视频数据类型定义
export interface ShortVideo {
  id: string
  title: string
  description?: string
  cover: string
  url: string
  duration: number
  resolution?: string
  view_count: number
  like_count: number
  comment_count: number
  share_count: number
  favorite_count: number
  creator_id: string
  creator_name: string
  creator_avatar: string
  category_id?: string
  category_name?: string
  tags: string[]
  tags_json: string
  src_type: number
  src?: string
  is_paid: number
  is_featured: number
  price: number
  upload_time: string
  status: number
  reason?: string
  created_at: string
  updated_at: string
  // 为ShortPlayer组件添加的兼容字段
  videoUrl?: string
  // 用户交互状态字段
  isLiked?: boolean
  isFavorited?: boolean
  isFollowing?: boolean
}

// 短视频分类类型定义
export interface ShortVideoCategory {
  id: string
  name: string
  code: string
  parent_id?: string
  description?: string
  sort_order: number
  status: number
  created_at: string
  updated_at: string
}

// 短视频评论类型定义
export interface ShortVideoComment {
  id: string
  short_id: string
  user_id: string
  username: string
  user_avatar: string
  content: string
  parent_id?: string
  like_count: number
  reply_count: number
  status: number
  created_at: string
  updated_at: string
  // 新增属性
  is_liked?: boolean
  replies?: ShortVideoComment[]
}

// 弹幕数据类型定义
export interface DanmuMessage {
  id?: string
  videoId: string
  text: string
  color: string
  fontSize: number
  timestamp: number
  userId?: string
  username?: string
  created_at?: string
}

// API响应格式
interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 分页响应数据格式
interface PageResponse<T = any> {
  list: T[]
  page: number
  pageSize: number
  total: number
}

/**
 * 获取短视频列表
 */
export function getShortVideoList(params: {
  data?: {
    keyword?: string
    category_id?: string
    status?: number
  }
  page: {
    pageNo: number
    pageSize: number
  }
}): Promise<PageResponse<ShortVideo>> {
  return corePost('/shorts/getShortVideoList', params.data, params.page)
}

/**
 * 获取短视频详情
 */
export function getShortVideoDetail(id: string): Promise<ShortVideo> {
  return corePost('/shorts/getShortVideoDetail', { id })
}

/**
 * 观看短视频（增加观看次数）
 */
export function viewShortVideo(id: string): Promise<any> {
  return corePost('/shorts/viewShortVideo', { id })
}

/**
 * 点赞短视频
 */
export function likeShortVideo(user_id: string, short_id: string): Promise<any> {
  return corePost('/shorts/likeShortVideo', { user_id, short_id })
}

/**
 * 取消点赞短视频
 */
export function cancelLikeShortVideo(user_id: string, short_id: string): Promise<any> {
  return corePost('/shorts/cancelLikeShortVideo', { user_id, short_id })
}

/**
 * 检查用户是否点赞过短视频
 */
export function checkUserLiked(user_id: string, short_id: string): Promise<boolean> {
  return corePost('/shorts/checkUserLiked', { user_id, short_id })
}

/**
 * 获取短视频评论列表
 */
export function getShortVideoComments(params: {
  data: {
    short_id: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}): Promise<PageResponse<ShortVideoComment>> {
  return corePost('/shorts/getShortVideoComments', params.data, params.page)
}

/**
 * 添加短视频评论
 */
export function addShortVideoComment(short_id: string, user_id: string, content: string, parent_id?: string): Promise<any> {
  return corePost('/shorts/addShortVideoComment', { short_id, user_id, content, parent_id })
}

/**
 * 获取短视频分类列表
 */
export function getShortVideoCategories(params: {
  data?: {
    keyword?: string
    status?: number
  }
  page: {
    pageNo: number
    pageSize: number
  }
}): Promise<PageResponse<ShortVideoCategory>> {
  return corePost('/shorts/getShortVideoCategories', params.data, params.page)
}

export function cancelLikeShortVideoComment(user_id: string, comment_id: string): Promise<any> {
  return corePost('/shorts/cancelLikeShortVideoComment', { user_id, comment_id })
}

export function checkUserLikedShortVideoComment(user_id: string, comment_id: string): Promise<boolean> {
  return corePost('/shorts/checkUserLikedShortVideoComment', { user_id, comment_id })
}

export function likeShortVideoComment(user_id: string, comment_id: string): Promise<any> {
  return corePost('/shorts/likeShortVideoComment', { user_id, comment_id })
}

/**
 * 发送弹幕消息
 */
export function sendDanmuMessage(danmuData: DanmuMessage): Promise<any> {
  return corePost('/shorts/sendDanmuMessage', danmuData)
}

/**
 * 获取视频弹幕列表
 */
export function getDanmuMessages(videoId: string, startTime?: number, endTime?: number): Promise<DanmuMessage[]> {
  return corePost('/shorts/getDanmuMessages', { videoId, startTime, endTime })
}

/**
 * 删除弹幕消息
 */
export function deleteDanmuMessage(danmuId: string, userId: string): Promise<any> {
  return corePost('/shorts/deleteDanmuMessage', { danmuId, userId })
}
