# 优化后的服务架构使用指南

## 概述

经过优化后的服务架构已经深度集成了 `internal/hooks` 系统，提供了更简洁、高效和易读的代码结构。

## 主要改进

### 1. 统一的钩子系统
- 移除了旧的 `HookManager` 和 `IntHookManager`
- 集成了新的 `hooks.ServiceHookManager`
- 提供了更丰富的钩子功能

### 2. 简化的构造函数
- 所有服务构造函数现在需要 `db *gorm.DB` 和 `entityType string` 参数
- 自动初始化钩子管理器

### 3. 增强的钩子注册方法
- `RegisterDuplicateCheck()` - 重复检查钩子
- `RegisterDataCleaning()` - 数据清洗钩子
- `RegisterAuditHook()` - 审计钩子
- `RegisterTimestampHook()` - 时间戳钩子
- `RegisterValidationHook()` - 验证钩子
- `SetupCommonHooks()` - 一键设置通用钩子

## 使用示例

### 1. 基础服务使用

```go
package main

import (
    "gorm.io/gorm"
    "frontapi/internal/service/base"
    "frontapi/internal/repository/base"
    "frontapi/internal/hooks"
    "frontapi/internal/models"
)

// 创建基础服务
func NewBookService(repo base.BaseRepository[models.Book]) *base.BaseService[models.Book] {
    service := base.NewBaseService(repo, "book")
    
    // 设置通用钩子
    setup := &hooks.CommonHooksSetup{
        TableName:       "books",
        DuplicateFields: []string{"isbn"},
        TrimFields:      []string{"title", "author"},
        LowerFields:     []string{"isbn"},
        DefaultValues: map[string]interface{}{
            "status": 1,
        },
        EnableAudit:     true,
        EnableTimestamp: true,
        UserID:          "system",
    }
    service.SetupCommonHooks(setup)
    
    return service
}
```

### 2. 扩展服务使用

```go
// 创建扩展服务
func NewPostService(repo base.ExtendedRepository[models.Post]) *base.ExtendedService[models.Post] {
    service := base.NewExtendedService(repo, "post")
    
    // 单独注册钩子
    service.RegisterDuplicateCheck("posts", []string{"title", "author_id"}, "标题重复")
    service.RegisterDataCleaning(
        []string{"title", "content"}, // trim fields
        []string{"slug"},             // lower fields
        []string{},                   // upper fields
        map[string]interface{}{
            "view_count": 0,
            "like_count": 0,
        },
    )
    service.RegisterAuditHook("admin")
    service.RegisterTimestampHook("created_at", "updated_at")
    
    return service
}
```

### 3. 整型ID服务使用

```go
import (
    "frontapi/internal/service/base/extint"
    "frontapi/internal/repository/base/extint"
)

// 创建整型ID基础服务
func NewCategoryService(repo extint.IntBaseRepository[models.Category]) *extint.IntBaseService[models.Category] {
    service := extint.NewIntBaseService(repo, "category")
    
    // 使用通用钩子设置
    setup := &hooks.CommonHooksSetup{
        TableName:       "categories",
        DuplicateFields: []string{"name", "parent_id"},
        TrimFields:      []string{"name", "description"},
        EnableTimestamp: true,
    }
    service.SetupCommonHooks(setup)
    
    return service
}

// 创建整型ID扩展服务
func NewTagService(repo extint.IntExtendedRepository[models.Tag]) *extint.IntExtendedService[models.Tag] {
    service := extint.NewIntExtendedService(repo, "tag")
    
    // 注册验证钩子
    validationRules := map[string]interface{}{
        "name": []interface{}{
            map[string]interface{}{"required": true},
            map[string]interface{}{"min_length": 2},
            map[string]interface{}{"max_length": 50},
        },
    }
    service.RegisterValidationHook(validationRules)
    
    return service
}
```

### 4. 自定义钩子使用

```go
// 获取钩子管理器并注册自定义钩子
func setupCustomHooks(service *base.BaseService[models.User]) {
    hookManager := service.GetHookManager()
    
    // 注册自定义钩子
    hookManager.RegisterHook("user", hooks.BeforeCreate, hooks.Hook{
        Name:        "password_hash",
        Description: "密码加密",
        Priority:    5,
        Func: func(ctx context.Context, data interface{}) error {
            if user, ok := data.(*models.User); ok {
                // 加密密码逻辑
                hashedPassword, err := hashPassword(user.Password)
                if err != nil {
                    return err
                }
                user.Password = hashedPassword
            }
            return nil
        },
    })
}
```

## 迁移指南

### 从旧版本迁移

1. **更新构造函数调用**：
   ```go
   // 旧版本
   service := base.NewBaseService(repo, db, "entityType")
   
   // 新版本（优化后）
   service := base.NewBaseService(repo, "entityType")
   ```

2. **更新钩子注册**：
   ```go
   // 旧版本
   service.RegisterDuplicateCheck("fieldName", "error message")
   
   // 新版本
   service.RegisterDuplicateCheck("tableName", []string{"field1", "field2"}, "error message")
   ```

3. **使用新的钩子方法**：
   ```go
   // 推荐使用通用钩子设置
   setup := &hooks.CommonHooksSetup{
       TableName:       "table_name",
       DuplicateFields: []string{"field1"},
       TrimFields:      []string{"field2"},
       EnableAudit:     true,
       EnableTimestamp: true,
   }
   service.SetupCommonHooks(setup)
   ```

## 性能优化

### 1. 钩子优先级
- 数据清洗：优先级 1-10
- 验证：优先级 11-20
- 重复检查：优先级 21-30
- 审计：优先级 31-40
- 时间戳：优先级 41-50

### 2. 缓存策略
- 自动缓存管理
- 支持自定义缓存TTL
- 智能缓存失效

### 3. 批量操作优化
- 批量钩子执行
- 事务支持
- 性能监控

## 最佳实践

1. **统一使用 `SetupCommonHooks()`** 来配置标准钩子
2. **合理设置钩子优先级** 确保执行顺序
3. **使用类型安全的钩子** 避免运行时错误
4. **适当的错误处理** 区分致命错误和警告
5. **性能监控** 监控钩子执行时间

## 总结

优化后的服务架构提供了：
- 更简洁的API
- 更强大的钩子系统
- 更好的类型安全
- 更高的性能
- 更易的维护性

通过集成 `internal/hooks` 系统，代码变得更加模块化和可重用，同时保持了高度的灵活性和扩展性。