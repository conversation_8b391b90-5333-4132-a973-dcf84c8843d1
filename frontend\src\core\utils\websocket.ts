/**
 * WebSocket工具函数
 */

/**
 * WebSocket连接状态枚举
 */
export enum WebSocketState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3
}

/**
 * WebSocket消息类型
 */
export interface WebSocketMessage<T = any> {
  id?: string
  type: string
  data: T
  timestamp?: number
}

/**
 * WebSocket配置选项
 */
export interface WebSocketOptions {
  protocols?: string | string[]
  reconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeat?: boolean
  heartbeatInterval?: number
  heartbeatMessage?: string | object
  timeout?: number
  binaryType?: 'blob' | 'arraybuffer'
  debug?: boolean
}

/**
 * WebSocket事件类型
 */
export interface WebSocketEvents {
  open: (event: Event) => void
  close: (event: CloseEvent) => void
  error: (event: Event) => void
  message: (data: any, event: MessageEvent) => void
  reconnect: (attempt: number) => void
  reconnectFailed: () => void
  heartbeat: (message: string | object) => void
}

/**
 * WebSocket连接信息
 */
export interface WebSocketInfo {
  url: string
  state: WebSocketState
  protocol: string
  extensions: string
  bufferedAmount: number
  readyState: number
  isConnected: boolean
  reconnectAttempts: number
  lastConnectTime: number
  lastDisconnectTime: number
}

/**
 * 增强的WebSocket类
 */
export class EnhancedWebSocket {
  private ws: WebSocket | null = null
  private url: string
  private options: Required<WebSocketOptions>
  private listeners: Partial<WebSocketEvents> = {}
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private timeoutTimer: number | null = null
  private reconnectAttempts = 0
  private lastConnectTime = 0
  private lastDisconnectTime = 0
  private messageQueue: Array<string | ArrayBuffer | Blob> = []
  private isDestroyed = false

  constructor(url: string, options: WebSocketOptions = {}) {
    this.url = url
    this.options = {
      protocols: undefined,
      reconnect: true,
      reconnectInterval: 3000,
      maxReconnectAttempts: 5,
      heartbeat: false,
      heartbeatInterval: 30000,
      heartbeatMessage: 'ping',
      timeout: 10000,
      binaryType: 'blob',
      debug: false,
      ...options
    }
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isDestroyed) {
        reject(new Error('WebSocket has been destroyed'))
        return
      }

      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      this.cleanup()
      this.log('Connecting to:', this.url)

      try {
        this.ws = new WebSocket(this.url, this.options.protocols)
        this.ws.binaryType = this.options.binaryType

        // 设置连接超时
        if (this.options.timeout > 0) {
          this.timeoutTimer = window.setTimeout(() => {
            if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
              this.ws.close()
              reject(new Error('Connection timeout'))
            }
          }, this.options.timeout)
        }

        this.ws.onopen = (event) => {
          this.clearTimeout()
          this.lastConnectTime = Date.now()
          this.reconnectAttempts = 0
          this.log('Connected')
          
          // 发送队列中的消息
          this.flushMessageQueue()
          
          // 启动心跳
          if (this.options.heartbeat) {
            this.startHeartbeat()
          }
          
          this.listeners.open?.(event)
          resolve()
        }

        this.ws.onclose = (event) => {
          this.clearTimeout()
          this.stopHeartbeat()
          this.lastDisconnectTime = Date.now()
          this.log('Disconnected:', event.code, event.reason)
          
          this.listeners.close?.(event)
          
          // 自动重连
          if (this.options.reconnect && !this.isDestroyed && !event.wasClean) {
            this.scheduleReconnect()
          }
        }

        this.ws.onerror = (event) => {
          this.clearTimeout()
          this.log('Error:', event)
          this.listeners.error?.(event)
          reject(event)
        }

        this.ws.onmessage = (event) => {
          this.log('Received:', event.data)
          
          let data = event.data
          
          // 尝试解析JSON
          if (typeof data === 'string') {
            try {
              data = JSON.parse(data)
            } catch {
              // 保持原始字符串
            }
          }
          
          this.listeners.message?.(data, event)
        }
      } catch (error) {
        this.clearTimeout()
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   * @param code 关闭代码
   * @param reason 关闭原因
   */
  disconnect(code = 1000, reason = 'Normal closure'): void {
    this.options.reconnect = false
    this.cleanup()
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close(code, reason)
    }
  }

  /**
   * 发送消息
   * @param data 消息数据
   */
  send(data: string | ArrayBuffer | Blob | object): boolean {
    if (this.isDestroyed) {
      this.log('Cannot send message: WebSocket destroyed')
      return false
    }

    let message: string | ArrayBuffer | Blob
    
    if (typeof data === 'object' && !(data instanceof ArrayBuffer) && !(data instanceof Blob)) {
      message = JSON.stringify(data)
    } else {
      message = data as string | ArrayBuffer | Blob
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(message)
        this.log('Sent:', message)
        return true
      } catch (error) {
        this.log('Send error:', error)
        return false
      }
    } else {
      // 连接未建立时，将消息加入队列
      this.messageQueue.push(message)
      this.log('Message queued:', message)
      return false
    }
  }

  /**
   * 发送结构化消息
   * @param type 消息类型
   * @param data 消息数据
   * @param id 消息ID
   */
  sendMessage<T>(type: string, data: T, id?: string): boolean {
    const message: WebSocketMessage<T> = {
      id: id || this.generateId(),
      type,
      data,
      timestamp: Date.now()
    }
    
    return this.send(message)
  }

  /**
   * 添加事件监听器
   * @param event 事件名
   * @param listener 监听器函数
   */
  on<K extends keyof WebSocketEvents>(event: K, listener: WebSocketEvents[K]): void {
    this.listeners[event] = listener
  }

  /**
   * 移除事件监听器
   * @param event 事件名
   */
  off<K extends keyof WebSocketEvents>(event: K): void {
    delete this.listeners[event]
  }

  /**
   * 获取连接信息
   * @returns 连接信息
   */
  getInfo(): WebSocketInfo {
    return {
      url: this.url,
      state: this.ws?.readyState ?? WebSocketState.CLOSED,
      protocol: this.ws?.protocol ?? '',
      extensions: this.ws?.extensions ?? '',
      bufferedAmount: this.ws?.bufferedAmount ?? 0,
      readyState: this.ws?.readyState ?? WebSocket.CLOSED,
      isConnected: this.ws?.readyState === WebSocket.OPEN,
      reconnectAttempts: this.reconnectAttempts,
      lastConnectTime: this.lastConnectTime,
      lastDisconnectTime: this.lastDisconnectTime
    }
  }

  /**
   * 检查是否已连接
   * @returns 是否已连接
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  /**
   * 检查是否正在连接
   * @returns 是否正在连接
   */
  isConnecting(): boolean {
    return this.ws?.readyState === WebSocket.CONNECTING
  }

  /**
   * 销毁WebSocket实例
   */
  destroy(): void {
    this.isDestroyed = true
    this.disconnect()
    this.messageQueue = []
    this.listeners = {}
  }

  /**
   * 清理定时器和连接
   */
  private cleanup(): void {
    this.clearTimeout()
    this.clearReconnectTimer()
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.onopen = null
      this.ws.onclose = null
      this.ws.onerror = null
      this.ws.onmessage = null
    }
  }

  /**
   * 清除超时定时器
   */
  private clearTimeout(): void {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer)
      this.timeoutTimer = null
    }
  }

  /**
   * 清除重连定时器
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.log('Max reconnect attempts reached')
      this.listeners.reconnectFailed?.()
      return
    }

    this.reconnectAttempts++
    this.log(`Scheduling reconnect attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts}`)
    
    this.reconnectTimer = window.setTimeout(() => {
      this.listeners.reconnect?.(this.reconnectAttempts)
      this.connect().catch(() => {
        // 重连失败，会触发下一次重连
      })
    }, this.options.reconnectInterval)
  }

  /**
   * 启动心跳
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected()) {
        this.send(this.options.heartbeatMessage)
        this.listeners.heartbeat?.(this.options.heartbeatMessage)
      }
    }, this.options.heartbeatInterval)
  }

  /**
   * 停止心跳
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 发送队列中的消息
   */
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message)
      }
    }
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 日志输出
   * @param args 参数
   */
  private log(...args: any[]): void {
    if (this.options.debug) {
      console.log('[WebSocket]', ...args)
    }
  }
}

/**
 * 创建WebSocket连接
 * @param url WebSocket URL
 * @param options 配置选项
 * @returns WebSocket实例
 */
export function createWebSocket(url: string, options?: WebSocketOptions): EnhancedWebSocket {
  return new EnhancedWebSocket(url, options)
}

/**
 * WebSocket连接池
 */
export class WebSocketPool {
  private connections = new Map<string, EnhancedWebSocket>()
  private defaultOptions: WebSocketOptions

  constructor(defaultOptions: WebSocketOptions = {}) {
    this.defaultOptions = defaultOptions
  }

  /**
   * 获取或创建连接
   * @param key 连接键
   * @param url WebSocket URL
   * @param options 配置选项
   * @returns WebSocket实例
   */
  getConnection(key: string, url: string, options?: WebSocketOptions): EnhancedWebSocket {
    if (this.connections.has(key)) {
      return this.connections.get(key)!
    }

    const mergedOptions = { ...this.defaultOptions, ...options }
    const ws = new EnhancedWebSocket(url, mergedOptions)
    this.connections.set(key, ws)

    // 监听关闭事件，自动清理
    ws.on('close', () => {
      this.connections.delete(key)
    })

    return ws
  }

  /**
   * 移除连接
   * @param key 连接键
   */
  removeConnection(key: string): void {
    const ws = this.connections.get(key)
    if (ws) {
      ws.destroy()
      this.connections.delete(key)
    }
  }

  /**
   * 获取所有连接
   * @returns 连接映射
   */
  getAllConnections(): Map<string, EnhancedWebSocket> {
    return new Map(this.connections)
  }

  /**
   * 广播消息到所有连接
   * @param data 消息数据
   */
  broadcast(data: any): void {
    this.connections.forEach(ws => {
      if (ws.isConnected()) {
        ws.send(data)
      }
    })
  }

  /**
   * 关闭所有连接
   */
  closeAll(): void {
    this.connections.forEach(ws => ws.destroy())
    this.connections.clear()
  }

  /**
   * 获取连接数量
   * @returns 连接数量
   */
  size(): number {
    return this.connections.size
  }

  /**
   * 获取活跃连接数量
   * @returns 活跃连接数量
   */
  activeSize(): number {
    let count = 0
    this.connections.forEach(ws => {
      if (ws.isConnected()) {
        count++
      }
    })
    return count
  }
}

/**
 * 创建WebSocket连接池
 * @param defaultOptions 默认配置选项
 * @returns 连接池实例
 */
export function createWebSocketPool(defaultOptions?: WebSocketOptions): WebSocketPool {
  return new WebSocketPool(defaultOptions)
}

/**
 * WebSocket消息路由器
 */
export class WebSocketRouter {
  private routes = new Map<string, (data: any, message: WebSocketMessage) => void>()
  private middlewares: Array<(message: WebSocketMessage, next: () => void) => void> = []

  /**
   * 注册路由
   * @param type 消息类型
   * @param handler 处理函数
   */
  route(type: string, handler: (data: any, message: WebSocketMessage) => void): void {
    this.routes.set(type, handler)
  }

  /**
   * 添加中间件
   * @param middleware 中间件函数
   */
  use(middleware: (message: WebSocketMessage, next: () => void) => void): void {
    this.middlewares.push(middleware)
  }

  /**
   * 处理消息
   * @param data 消息数据
   * @param event 原始事件
   */
  handle(data: any, event: MessageEvent): void {
    let message: WebSocketMessage

    // 尝试解析为结构化消息
    if (typeof data === 'object' && data.type) {
      message = data
    } else {
      // 创建默认消息结构
      message = {
        type: 'default',
        data,
        timestamp: Date.now()
      }
    }

    // 执行中间件链
    this.executeMiddlewares(message, 0, () => {
      // 查找并执行路由处理器
      const handler = this.routes.get(message.type)
      if (handler) {
        try {
          handler(message.data, message)
        } catch (error) {
          console.error(`Error handling message type '${message.type}':`, error)
        }
      } else {
        console.warn(`No handler found for message type '${message.type}'`)
      }
    })
  }

  /**
   * 执行中间件链
   * @param message 消息
   * @param index 当前中间件索引
   * @param final 最终处理函数
   */
  private executeMiddlewares(message: WebSocketMessage, index: number, final: () => void): void {
    if (index >= this.middlewares.length) {
      final()
      return
    }

    const middleware = this.middlewares[index]
    middleware(message, () => {
      this.executeMiddlewares(message, index + 1, final)
    })
  }

  /**
   * 移除路由
   * @param type 消息类型
   */
  removeRoute(type: string): void {
    this.routes.delete(type)
  }

  /**
   * 清空所有路由
   */
  clearRoutes(): void {
    this.routes.clear()
  }

  /**
   * 清空所有中间件
   */
  clearMiddlewares(): void {
    this.middlewares = []
  }
}

/**
 * 创建WebSocket路由器
 * @returns 路由器实例
 */
export function createWebSocketRouter(): WebSocketRouter {
  return new WebSocketRouter()
}

/**
 * WebSocket重连策略
 */
export interface ReconnectStrategy {
  shouldReconnect(attempt: number, error?: Event): boolean
  getDelay(attempt: number): number
}

/**
 * 指数退避重连策略
 */
export class ExponentialBackoffStrategy implements ReconnectStrategy {
  constructor(
    private maxAttempts = 5,
    private baseDelay = 1000,
    private maxDelay = 30000,
    private factor = 2
  ) {}

  shouldReconnect(attempt: number): boolean {
    return attempt <= this.maxAttempts
  }

  getDelay(attempt: number): number {
    const delay = this.baseDelay * Math.pow(this.factor, attempt - 1)
    return Math.min(delay, this.maxDelay)
  }
}

/**
 * 固定间隔重连策略
 */
export class FixedIntervalStrategy implements ReconnectStrategy {
  constructor(
    private maxAttempts = 5,
    private interval = 3000
  ) {}

  shouldReconnect(attempt: number): boolean {
    return attempt <= this.maxAttempts
  }

  getDelay(): number {
    return this.interval
  }
}

/**
 * 线性增长重连策略
 */
export class LinearBackoffStrategy implements ReconnectStrategy {
  constructor(
    private maxAttempts = 5,
    private baseDelay = 1000,
    private increment = 1000,
    private maxDelay = 30000
  ) {}

  shouldReconnect(attempt: number): boolean {
    return attempt <= this.maxAttempts
  }

  getDelay(attempt: number): number {
    const delay = this.baseDelay + (attempt - 1) * this.increment
    return Math.min(delay, this.maxDelay)
  }
}

/**
 * WebSocket工具函数
 */
export const WebSocketUtils = {
  /**
   * 检查WebSocket是否支持
   * @returns 是否支持
   */
  isSupported(): boolean {
    return typeof WebSocket !== 'undefined'
  },

  /**
   * 获取WebSocket状态名称
   * @param state 状态码
   * @returns 状态名称
   */
  getStateName(state: number): string {
    switch (state) {
      case WebSocket.CONNECTING:
        return 'CONNECTING'
      case WebSocket.OPEN:
        return 'OPEN'
      case WebSocket.CLOSING:
        return 'CLOSING'
      case WebSocket.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  },

  /**
   * 检查URL是否为有效的WebSocket URL
   * @param url URL字符串
   * @returns 是否有效
   */
  isValidURL(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return urlObj.protocol === 'ws:' || urlObj.protocol === 'wss:'
    } catch {
      return false
    }
  },

  /**
   * 将HTTP URL转换为WebSocket URL
   * @param url HTTP URL
   * @returns WebSocket URL
   */
  httpToWebSocket(url: string): string {
    return url.replace(/^https?:/, (match) => {
      return match === 'https:' ? 'wss:' : 'ws:'
    })
  },

  /**
   * 将WebSocket URL转换为HTTP URL
   * @param url WebSocket URL
   * @returns HTTP URL
   */
  webSocketToHttp(url: string): string {
    return url.replace(/^wss?:/, (match) => {
      return match === 'wss:' ? 'https:' : 'http:'
    })
  }
}