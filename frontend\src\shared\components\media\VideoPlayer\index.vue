<template>
  <div :class="showComments ? 'has-comment vc' : 'vc'">
    <div class="video-wrapper">
      <!-- 视频播放区域 -->
      <video class="video-js vjs-default-skin video-player" ref="videoPlayer" crossorigin="anonymous" data-setup='{}'
        @click="togglePlay">
      </video>
      <SvgIcon name="play" class="status-play-icon" :size="96" color="#ffffff" v-if="!isPlaying" @click="togglePlay" />
      
      <!-- 统一控制区域 -->
      <div class="control-area">
        <!-- 视频信息 -->
        <div class="v-info">
          <h2 class="v-title">{{ video.title }}</h2>
          <p class="v-description">{{ video.description }}</p>
        </div>
        <!--进度条-->
        <div class="c-progress" @click="seekToPosition">
          <div class="progress-bar">
            <div class="buffer-progress" :style="{ width: bufferProgress + '%' }"></div>
            <div class="play-progress" :style="{ width: playProgress + '%' }">
              <div class="progress-dot"></div>
            </div>
          </div>
        </div>
        <!-- 自定义控制器 -->
        <div class="custom-controls">
          <div class="left-control">
            <div class="control-item" @click="togglePlay">
              <SvgIcon :name="isPlaying ? 'pause' : 'play'" :size="24" color="#ffffff" />
            </div>
            <div class="control-item">
              <div class="time-info">
                <span>{{ formatTime(currentTime) }}</span>
                /<span>{{ formatTime(duration) }}</span>
              </div>
            </div>
            <!-- 弹幕控制区域 -->
            <div class="danmu-controls">
              <el-input v-model="danmakuText" class="danmaku-input" placeholder="输入弹幕内容" @keyup.enter="sendDanmaku">
                <template #prefix>
                  <SvgIcon :name="danmakuEnabled ? 'danmu_close' : 'danmu_open'" @click="toggleDanmaku"
                    class="danmu-toggle" size="32" color="#ffffff"></SvgIcon>
                </template>
              </el-input>
            </div>
          </div>
          <div class="right-control">
            <div class="control-item">
              <div class="speed-select" ref="speedControlRef" @mouseenter="handleSpeedMouseEnter"
                @mouseleave="handleSpeedMouseLeave" @click.stop>
                <div class="speed-button">{{ playbackRate }}x</div>
                <div class="speed-options" v-show="showSpeedOptions" @mouseleave="handleSpeedMouseLeave" @click.stop>
                  <div v-for="rate in [0.75, 1, 1.25, 1.5, 2]" :key="rate" class="speed-option"
                    :class="{ active: playbackRate === rate }" @click.stop="changePlaybackRate(rate)">
                    {{ rate }}x
                  </div>
                </div>
              </div>
            </div>
            <div class="control-item">
              <div class="volume-control" ref="volumeControlRef" @mouseenter="handleVolumeMouseEnter"
                @mouseleave="handleVolumeMouseLeave" @click.stop>
                <SvgIcon :name="isMuted ? 'mute' : 'voice'" :size="24" class="toggle-mute" color="#ffffff"
                  @click.stop="toggleMute" />
                <div class="volume-slider" v-show="showVolumeSlider" @click.stop>
                  <el-slider v-model="volume" vertical height="100px" :min="0" :max="100" @change="changeVolume"
                    @click.stop />
                </div>
              </div>
            </div>
            <div class="control-item" @click="toggleFullscreen">
              <SvgIcon name="fullscreen" :size="24" color="#ffffff" />
            </div>
          </div>
        </div>
      </div>
      <!-- 弹幕显示区域 -->
      <div class="danmaku-container">
        <canvas ref="danmakuCanvas" class="danmaku-canvas" />
      </div>
    
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import videojs from 'video.js'
import '@videojs/http-streaming'
import 'video.js/dist/video-js.css'
// 修正导入路径,使用相对路径导入SvgIcon组件
import SvgIcon from '../icons/SvgIcon.vue'

interface VideoAuthor {
  id: string
  name: string
  avatar: string
}

interface Video {
  id: string
  title: string
  description: string
  videoUrl: string
  cover: string
  likes: number
  favorites: number
  comments: number
  author: VideoAuthor
}

interface Props {
  video: Video
  isFirstVideo: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'prev'): void
  (e: 'next'): void
  (e: 'dolike'): void
  (e: 'favorite'): void
  (e: 'comment'): void
  (e: 'follow'): void
}>()

// Define types
type VideoJsPlayer = ReturnType<typeof videojs>;

const player = ref<VideoJsPlayer | null>(null)
const videoPlayer = ref<HTMLVideoElement | null>(null)
const danmakuText = ref('')
// 倍速控制相关
const showSpeedOptions = ref(false)
const playbackRate = ref(1)
const speedControlRef = ref<HTMLElement | null>(null)

// 音量控制相关
const showVolumeSlider = ref(false)
const volume = ref(100)
const isMuted = ref(false)
const volumeControlRef = ref<HTMLElement | null>(null)

// 倍速选择的事件处理
const handleSpeedMouseEnter = () => {
  showSpeedOptions.value = true
}

const handleSpeedMouseLeave = (event: MouseEvent) => {
  const speedControl = speedControlRef.value
  const relatedTarget = event.relatedTarget as HTMLElement
  console.log("speedControl", relatedTarget, speedControl);
  if (speedControl && !speedControl.contains(relatedTarget)) {
    showSpeedOptions.value = false
  }
}

const changePlaybackRate = (rate: number) => {
  if (player.value) {
    player.value.playbackRate(rate)
    playbackRate.value = rate
    showSpeedOptions.value = false
  }
}

// 音量控制的事件处理
const handleVolumeMouseEnter = () => {
  showVolumeSlider.value = true
}

const handleVolumeMouseLeave = (event: MouseEvent) => {
  const volumeControl = volumeControlRef.value
  const relatedTarget = event.relatedTarget as HTMLElement
  if (volumeControl && !volumeControl.contains(relatedTarget)) {
    showVolumeSlider.value = false
  }
}

const changeVolume = (val: number) => {
  if (player.value) {
    player.value.volume(val / 100)
    volume.value = val
    isMuted.value = val === 0
  }
}


const danmakuEnabled = ref(true)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const bufferProgress = ref(0)
const playProgress = ref(0)
const showControls = ref(false)
const showResolutionOptions = ref(false)

const videoLike = ref(0);
const videoFavorite = ref(0);
const videoComment = ref(0);

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 更新进度条
const updateProgress = () => {
  if (player.value) {
    currentTime.value = player.value.currentTime() || 0
    duration.value = player.value.duration() || 0
    playProgress.value = duration.value ? (currentTime.value / duration.value) * 100 : 0

    // 更新缓冲进度
    const buffered = player.value.buffered()
    if (buffered && buffered.length > 0) {
      bufferProgress.value = (buffered.end(buffered.length - 1) / duration.value) * 100
    }
  }
}

// 跳转到指定位置
const seekToPosition = (event: MouseEvent) => {
  if (player.value && duration.value > 0) {
    const progressBar = event.currentTarget as HTMLElement
    const rect = progressBar.getBoundingClientRect()
    const position = (event.clientX - rect.left) / rect.width
    const seekTime = Math.max(0, Math.min(duration.value, position * duration.value))
    player.value.currentTime(seekTime)
  }
}

// 视频播放控制
const togglePlay = () => {
  if (player.value) {
    if (player.value.paused()) {
      player.value.play()
      isPlaying.value = true
    } else {
      player.value.pause()
      isPlaying.value = false
    }
  }
  showControls.value = true
  resetControlsTimer()
}

// 切换全屏
const toggleFullscreen = () => {
  if (player.value) {
    if (!document.fullscreenElement) {
      player.value.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }
}

// 切换静音
const toggleMute = () => {
  if (player.value) {
    if (player.value.muted()) {
      player.value.muted(false)
      isMuted.value = false
    } else {
      player.value.muted(true)
      isMuted.value = true
      volume.value = 0
    }
  }
}



// 监听video prop的变化
watch(() => props.video, (newVideo) => {
  if (player.value) {
    player.value.src({
      src: newVideo.videoUrl,
      type: newVideo.videoUrl.includes('.m3u8') ? 'application/x-mpegURL' :
        newVideo.videoUrl.includes('.mp4') ? 'video/mp4' :
          'video/mp4'
    })
    player.value.play()
  }
}, { deep: true })

// 初始化 Video.js 播放器
const initializePlayer = () => {
  if (videoPlayer.value) {
    initPlayer();
  }
}

// 窗口resize处理函数
const handleResize = () => {
  player.value?.trigger('resize');
}

const initPlayer = () => {
  if (videoPlayer.value) {
    player.value = videojs(videoPlayer.value, {
      controls: false,
      autoplay: false,
      preload: 'auto',
      html5: {
        vhs: {
          overrideNative: true
        }
      },
      sources: [{
        src: props.video.videoUrl.startsWith('/')
          ? `${window.location.origin}${props.video.videoUrl}`
          : props.video.videoUrl,
        type: props.video.videoUrl.includes('.m3u8') ? 'application/x-mpegURL' : 'video/mp4'
      }]
    })

    // 将事件监听移到初始化内部
    player.value?.on('loadedmetadata', () => {
      duration.value = player.value?.duration() || 0
      updateProgress()
      
      // 获取视频实际宽高比并设置适配样式
      const videoElement = player.value?.tech().el()
      if (videoElement) {
        const aspectRatio = videoElement.clientWidth / videoElement.clientHeight
        if (aspectRatio > 1) {
          videoElement.classList.add('width-priority')
          videoElement.classList.remove('height-priority')
        } else {
          videoElement.classList.add('height-priority')
          videoElement.classList.remove('width-priority')
        }
      }
    })

    player.value?.on('error', (err: any) => {
      console.error('视频加载失败:', err)
      ElMessage.error('视频加载失败，请检查网络或文件路径')
    })

    // 其他事件监听...
  }
}


const animationFrameId = ref<number>()




onUnmounted(() => {
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
})

// 控制栏定时器
const controlsTimer = ref<number | null>(null);

onMounted(() => {
  animationFrameId.value = requestAnimationFrame(animate)

  videoLike.value = props.video.likes;
  videoFavorite.value = props.video.favorites;
  videoComment.value = props.video.comments

  // 初始化播放器
  initializePlayer()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
  // 添加timeupdate事件监听器
  if (player.value) {
    player.value.on('timeupdate', updateProgress)

    // 获取播放器容器元素
    const playerContainer = document.querySelector('.video-wrapper');
    if (playerContainer) {
      // 鼠标移动时显示控制栏
      playerContainer.addEventListener('mousemove', () => {
        showControls.value = true;
        resetControlsTimer();
      });

      // 鼠标离开时隐藏控制栏和清晰度选项
      playerContainer.addEventListener('mouseleave', () => {
        showControls.value = false;
        showResolutionOptions.value = false;
      });
    }
  }
})

onUnmounted(() => {
  if (player.value) {
    player.value.dispose()
  }
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
  window.removeEventListener('resize', handleResize)
})

const resetControlsTimer = () => {
  // 清除之前的定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value);
  }

  // 设置新的定时器，3秒后隐藏控制栏
  controlsTimer.value = setTimeout(() => {
    showControls.value = false;
  }, 3000);
}


const danmakuCanvas = ref<HTMLCanvasElement | null>(null)
const danmakuContext = ref<CanvasRenderingContext2D | null>(null)
const danmakus = ref<Array<{
  text: string
  x: number
  y: number
  color: string
  speed: number
  width?: number
}>>([]);

const initDanmakuCanvas = () => {
  if (!danmakuCanvas.value) return
  const canvas = danmakuCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  danmakuContext.value = ctx
  canvas.width = canvas.offsetWidth
  canvas.height = canvas.offsetHeight
}

const addDanmaku = (text: string) => {
  if (!danmakuEnabled.value) return

  const danmaku = {
    text,
    x: danmakuCanvas.value?.width || 0,
    y: Math.random() * ((danmakuCanvas.value?.height || 0) - 30),
    color: '#fff',
    width:0,
    speed: Math.random()*2+1
  }

  if (danmakuContext.value) {
    danmakuContext.value.font = '20px Arial'
    danmaku.width = danmakuContext.value.measureText(text).width
  }

  danmakus.value.push(danmaku)
}

const updateDanmakus = () => {
  if (!danmakuCanvas.value || !danmakuContext.value || !danmakuEnabled.value) return

  const ctx = danmakuContext.value
  const canvas = danmakuCanvas.value

  ctx.clearRect(0, 0, canvas.width, canvas.height)
  ctx.font = '20px Arial'
  ctx.fillStyle = '#fff'

  danmakus.value = danmakus.value.filter(danmaku => {
    danmaku.x -= danmaku.speed
    if (danmaku.x + (danmaku.width || 0) < 0) return false

    ctx.fillText(danmaku.text, danmaku.x, danmaku.y)
    return true
  })
}

const animate = () => {
  animationFrameId.value = requestAnimationFrame(animate)
  updateDanmakus()
}

const toggleDanmaku = () => {
  danmakuEnabled.value = !danmakuEnabled.value
  if (!danmakuEnabled.value) {
    danmakus.value = []
    if (danmakuCanvas.value && danmakuContext.value) {
      danmakuContext.value.clearRect(0, 0, danmakuCanvas.value.width, danmakuCanvas.value.height)
    }
  }
}

const sendDanmaku = (event: KeyboardEvent) => {
  event.preventDefault()
  if (danmakuText.value.trim()) {
    addDanmaku(danmakuText.value)
    danmakuText.value = ''
  }
}

onMounted(() => {
  initDanmakuCanvas()
  window.addEventListener('resize', initDanmakuCanvas)
  animationFrameId.value = requestAnimationFrame(animate)
})

onUnmounted(() => {
  window.removeEventListener('resize', initDanmakuCanvas)
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
})


onMounted(() => {
  initializePlayer()

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  // 添加timeupdate事件监听器
  if (player.value) {
    player.value.on('timeupdate', updateProgress)
  }

    // 获取播放器容器元素
    const playerContainer = document.querySelector('.video-wrapper');
    if (playerContainer) {
      // 鼠标移动时显示控制栏
      playerContainer.addEventListener('mousemove', () => {
        showControls.value = true;
        resetControlsTimer();
      });

      // 鼠标离开时隐藏控制栏和清晰度选项
      playerContainer.addEventListener('mouseleave', () => {
        showControls.value = false;
        showResolutionOptions.value = false;
      });
    }
  })

onUnmounted(() => {
  if (player.value) {
    player.value.dispose()
  }
  if (animationFrameId.value) {
    cancelAnimationFrame(animationFrameId.value)
  }
  window.removeEventListener('resize', handleResize)
})


</script>

<style scoped lang="scss">
.vc {
  display: flex;
  width: 100%;
  height: 100%;
  transition: flex-direction 0.3s ease;
  border-radius: 5px;

  @media screen and (min-width: 769px) {
    flex-direction: row;
    background-color: #000;
    
    &.has-comments {
      max-width: calc(100% - 360px);
      width: 100%;
    }

    .video-wrapper {
      flex: 1;
    }

    .video-comments {
      width: 360px;
      display: block;
      background-color: #000;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: var(--cover-image, none);
        background-size: cover;
        background-position: center;
        filter: blur(10px);
        opacity: 0.5;
        z-index: 0;
      }

      & > * {
        position: relative;
        z-index: 1;
      }
    }
  }

  @media screen and (max-width: 768px) {
    flex-direction: column;
    min-height: 800px;
    justify-content: flex-start;
    .video-wrapper {
      flex: 1;
      max-width: 1280px;
      width: 100%;
      height:100%;
      min-height: 720px;
      background-color: #000;
    }

    .video-comments {
      width: 100%;
      background-color: #000;
    }
  }
}

.video-wrapper{
  position: relative;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  height: 100%;
  background-color: #000;
  overflow: hidden;
  user-select: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .danmaku-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: calc(100% - 180px);
    pointer-events: none;
    z-index: 2;
  }

  .danmaku-canvas {
    width: 100%;
    height: 100%;
  }

  .danmu-controls {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }

  .danmaku-input {
    width: 200px;
    margin-right: 10px;

    :deep(.el-input__wrapper) {
      background: rgba(255, 255, 255, 0.2);
      box-shadow: none;
    }

    :deep(.el-input__inner) {
      color: #fff;
      &::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  .danmu-toggle {
    cursor: pointer;
    transition: transform 0.3s;
    &:hover {
      transform: scale(1.1);
    }
  }

  .video-player {
    width: 100%;
    max-height: calc(100% - 160px);
    height: 100%;
    object-fit: contain;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    &.video-js {
      .vjs-tech {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        &.width-priority {
          width: 100%;
          height: auto;
        }
        &.height-priority {
          width: auto;
          height: 100%;
        }
      }
    }

    &.video-js {
      .vjs-tech {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        &.width-priority {
          width: 100%;
          height: auto;
        }
        &.height-priority {
          width: auto;
          height: 100%;
        }
    }
  }
  }
  .c-progress {
    margin: 10px 0;
    width: 100%;

    .progress-bar {
      position: relative;
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
      cursor: pointer;

      .buffer-progress {
        position: absolute;
        height: 100%;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 2px;
      }

      .play-progress {
        position: absolute;
        height: 100%;
        background: #409eff;
        border-radius: 2px;

        .progress-dot {
          position: absolute;
          right: -6px;
          top: -4px;
          width: 12px;
          height: 12px;
          background: #409eff;
          border-radius: 50%;
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }

  .custom-controls {
    height: 40px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: center;
    padding: 5px 0;
    z-index: 3;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .left-control {
      display: flex;
      align-items: center;
      gap: 15px;

      .control-item {
        position: relative;
        display: flex;
        align-items: center;
        color: #fff;
        cursor: pointer;
      }
    }

    .right-control {
      display: flex;
      align-items: center;
      gap: 15px;
      padding-right: 15px;

      .control-item {
        position: relative;
        display: flex;
        align-items: center;
        color: #fff;
        cursor: pointer;
        padding: 5px;

        .speed-select {
          position: relative;

          .speed-button {
            padding: 5px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 4px;
            font-size: 16px;
            color: #fff;
            cursor: pointer;
          }
          .speed-options {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            border-radius: 4px;
            overflow: hidden;
            min-width: 80px;
            z-index: 9999;
            .speed-option {
              padding: 8px 12px;
              text-align: center;
              cursor: pointer;
              transition: background-color 0.2s;

              &:hover {
                background: rgba(255, 255, 255, 0.1);
              }

              &.active {
                background: rgba(255, 255, 255, 0.2);
              }
            }
          }
        }
          .volume-control {
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;

            .volume-slider {
              position: absolute;
              bottom: 100%;
              left: 50%;
              transform: translateX(-50%);
              padding: 12px;
              background: rgba(0, 0, 0, 0.9);
              border-radius: 4px;
              height: 120px;
              display: flex;
              flex-direction: column;
              align-items: center;

              .el-slider {
                height: 100%;
              }
            }
          }
        
      }
    }

    .danmu-controls {
      display: flex;
      background-color: rgba(200, 200, 200, 0.3);
      margin-left: 15px;
      padding: 2px 5px 2px 0;
      border-radius: 4px;

      .danmaku-input {
        margin-left: 0px;
        outline: none;
        border: none;
        min-width: 280px;

        :deep(.el-input__wrapper) {
          background-color: transparent;
          box-shadow: none;

          &.is-focus {
            box-shadow: none;
          }

          .el-input__inner {
            color: #fff;

            &::placeholder {
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
      }
    }

  
  }
  .control-area {
      position: absolute;
      left: 0;
      bottom: 0;
      width: calc(100% - 40px);
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
      padding: 20px;
      z-index: 2;
      display: flex;
      flex-direction: column;

      .v-info {
        color: #fff;
        text-align: left;

        .v-title {
          font-size: 18px;
          margin: 0 0 8px;
          font-weight: bold;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .v-description {
          font-size: 14px;
          opacity: 0.8;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
      }
    }
  .floating-user-section {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    width: 80px;

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;

      .user-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 2px solid #fff;
        margin-bottom: 10px;
      }

      .user-name {
        color: #fff;
        font-size: 14px;
        margin-bottom: 8px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }

      .follow-btn {
        padding: 4px 15px;
        border-radius: 20px;
        font-size: 14px;

        &.is-followed {
          background-color: rgba(255, 255, 255, 0.2);
          border-color: transparent;
        }
      }
    }

    .interaction-buttons {
      display: flex;
      flex-direction: column;
      gap: 15px;
      width: 100%;

      .interaction-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;

        img {
          width: 30px;
          margin-bottom: 4px;
          transition: all 0.3s ease;

          &.is-active {
            color: #f12b56;
            background-color: rgba(64, 158, 255, 0.2);
          }
        }

        span {
          font-size: 12px;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
      }
    }
  }

  .video-info {
    position: absolute;
    left: 0;
    bottom: 0;
    padding-left: 20px;
    padding-bottom: 40px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: #fff;
    z-index: 1;

    .video-title {
      font-size: 18px;
      margin-bottom: 8px;
    }

    .video-description {
      font-size: 14px;
      opacity: 0.8;
    }
  }

  .floating-user-section {
    position: absolute;
    right: 80px;
    bottom: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;
    width: 80px;

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 40px;
      width: 100%;

      .user-avatar {
        margin-bottom: 12px;
        border: 2px solid #fff;
      }

      .user-name {
        color: #fff;
        font-size: 14px;
        margin: 8px 0;
        text-align: center;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .follow-btn {
        width: 80px;

        &.is-followed {
          background-color: #909399;
        }
      }
    }

    .interaction-buttons {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .interaction-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;
        cursor: pointer;

        .el-icon {
          margin-bottom: 4px;
          transition: all 0.3s;

          &.is-active {
            color: #409EFF;
            transform: scale(1.1);
          }
        }

        span {
          font-size: 12px;
        }

        &:hover {
          .el-icon {
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .video-navigation {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 2;

    .nav-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.3);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .status-play-icon {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    cursor: pointer;
  }

  .controls-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .resolution-selector {
    position: relative;
    margin-left: auto;
  }

  .resolution-button {
    padding: 5px 10px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }

  .resolution-options {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    overflow: hidden;
    width: 120px;
  }

  .resolution-option {
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
  }

  .resolution-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .resolution-option.active {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

</style>
