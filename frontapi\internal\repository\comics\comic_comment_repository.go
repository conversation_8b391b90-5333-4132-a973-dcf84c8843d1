package comics

import (
	"gorm.io/gorm"

	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"
)

// ComicCommentRepository 漫画评论数据访问接口
type ComicCommentRepository interface {
	base.ExtendedRepository[comics.ComicComment]
}

// comicCommentRepository 漫画评论数据访问实现
type comicCommentRepository struct {
	base.ExtendedRepository[comics.ComicComment]
}

// NewComicCommentRepository 创建漫画评论仓库实例
func NewComicCommentRepository(db *gorm.DB) ComicCommentRepository {
	return &comicCommentRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.ComicComment](db),
	}
}
