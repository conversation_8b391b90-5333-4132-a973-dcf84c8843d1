package cluster

import (
	"context"
	"errors"
	"fmt"
	cacheConfig "frontapi/config/cache"
	"frontapi/pkg/cache/types"
	"sync"
	"time"
)

// 使用types包中定义的类型
type (
	CacheAdapter  = types.CacheAdapter
	CacheStats    = types.CacheStats
	ClusterNode   = cacheConfig.ClusterNode
	ClusterConfig = cacheConfig.ClusterConfig
)

// ClusterManager 分布式缓存集群管理器
type ClusterManager struct {
	config         *ClusterConfig
	nodes          map[string]*ClusterNode
	adapters       map[string]CacheAdapter
	partitionMap   map[int][]string // 分区ID -> 节点ID列表
	hashRing       *ConsistentHash
	mu             sync.RWMutex
	healthChecker  *time.Ticker
	stopChan       chan struct{}
	eventListeners []func(ClusterEvent)
}

// ClusterEvent 集群事件
type ClusterEvent struct {
	Type      string // "node_added", "node_removed", "node_down", "node_up", "failover"
	NodeID    string
	Timestamp time.Time
	Data      interface{}
}

// NewClusterManager 创建新的集群管理器
func NewClusterManager(config *ClusterConfig) (*ClusterManager, error) {
	if config == nil {
		return nil, errors.New("config cannot be nil")
	}

	if len(config.Nodes) == 0 {
		return nil, errors.New("at least one node is required")
	}

	cm := &ClusterManager{
		config:         config,
		nodes:          make(map[string]*ClusterNode),
		adapters:       make(map[string]CacheAdapter),
		partitionMap:   make(map[int][]string),
		hashRing:       NewConsistentHash(config.ConsistentHashReplicas),
		stopChan:       make(chan struct{}),
		eventListeners: make([]func(ClusterEvent), 0),
	}

	// 初始化节点
	for _, node := range config.Nodes {
		nodeCopy := node // 创建副本以避免引用循环
		cm.nodes[nodeCopy.ID] = &nodeCopy
		cm.hashRing.Add(nodeCopy.ID)
	}

	// 初始化分区映射
	if config.PartitionCount > 0 {
		cm.initializePartitions()
	}

	// 启动健康检查
	if config.HealthCheckInterval > 0 {
		cm.startHealthCheck()
	}

	return cm, nil
}

// initializePartitions 初始化分区映射
func (cm *ClusterManager) initializePartitions() {
	// 实现分区逻辑，将数据分区映射到节点
	// 考虑复制因子，确保每个分区有多个副本
}

// startHealthCheck 开始健康检查
func (cm *ClusterManager) startHealthCheck() {
	cm.healthChecker = time.NewTicker(cm.config.HealthCheckInterval)

	go func() {
		for {
			select {
			case <-cm.healthChecker.C:
				cm.checkNodesHealth()
			case <-cm.stopChan:
				cm.healthChecker.Stop()
				return
			}
		}
	}()
}

// checkNodesHealth 检查所有节点的健康状态
func (cm *ClusterManager) checkNodesHealth() {
	// 实现节点健康检查逻辑
	// 检测节点是否可用，并在必要时触发故障转移
}

// RegisterAdapter 注册缓存适配器到节点
func (cm *ClusterManager) RegisterAdapter(nodeID string, adapter CacheAdapter) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.nodes[nodeID]; !exists {
		return fmt.Errorf("node %s does not exist", nodeID)
	}

	cm.adapters[nodeID] = adapter
	return nil
}

// GetAdapter 获取指定节点的缓存适配器
func (cm *ClusterManager) GetAdapter(nodeID string) (CacheAdapter, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	adapter, exists := cm.adapters[nodeID]
	if !exists {
		return nil, fmt.Errorf("adapter for node %s not found", nodeID)
	}

	return adapter, nil
}

// GetNodeForKey 根据键获取节点
func (cm *ClusterManager) GetNodeForKey(key string) string {
	return cm.hashRing.Get(key)
}

// Set 设置缓存值（分布式）
func (cm *ClusterManager) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	nodeID := cm.GetNodeForKey(key)

	// 根据写一致性级别决定写入策略
	switch cm.config.WriteConsistency {
	case "all":
		return cm.setToAllReplicas(ctx, key, value, expiration)
	case "quorum":
		return cm.setToQuorum(ctx, key, value, expiration)
	default:
		// 默认写入主节点
		adapter, err := cm.GetAdapter(nodeID)
		if err != nil {
			return err
		}
		return adapter.Set(ctx, key, value, expiration)
	}
}

// Get 获取缓存值（分布式）
func (cm *ClusterManager) Get(ctx context.Context, key string) ([]byte, error) {
	nodeID := cm.GetNodeForKey(key)

	// 根据读一致性级别决定读取策略
	switch cm.config.ReadConsistency {
	case "all":
		return cm.getFromAllReplicas(ctx, key)
	case "quorum":
		return cm.getFromQuorum(ctx, key)
	default:
		// 默认从主节点读取
		adapter, err := cm.GetAdapter(nodeID)
		if err != nil {
			return nil, err
		}
		return adapter.Get(ctx, key)
	}
}

// setToAllReplicas 写入所有副本
func (cm *ClusterManager) setToAllReplicas(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	// 实现写入所有副本的逻辑
	return nil
}

// setToQuorum 写入大多数副本
func (cm *ClusterManager) setToQuorum(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	// 实现写入大多数副本的逻辑
	return nil
}

// getFromAllReplicas 从所有副本读取
func (cm *ClusterManager) getFromAllReplicas(ctx context.Context, key string) ([]byte, error) {
	// 实现从所有副本读取的逻辑
	return nil, nil
}

// getFromQuorum 从大多数副本读取
func (cm *ClusterManager) getFromQuorum(ctx context.Context, key string) ([]byte, error) {
	// 实现从大多数副本读取的逻辑
	return nil, nil
}

// AddNode 添加新节点
func (cm *ClusterManager) AddNode(node ClusterNode) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.nodes[node.ID]; exists {
		return fmt.Errorf("node %s already exists", node.ID)
	}

	cm.nodes[node.ID] = &node
	cm.hashRing.Add(node.ID)

	// 重新平衡分区
	if cm.config.PartitionCount > 0 {
		cm.rebalancePartitions()
	}

	// 触发节点添加事件
	cm.triggerEvent(ClusterEvent{
		Type:      "node_added",
		NodeID:    node.ID,
		Timestamp: time.Now(),
		Data:      node,
	})

	return nil
}

// RemoveNode 移除节点
func (cm *ClusterManager) RemoveNode(nodeID string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	if _, exists := cm.nodes[nodeID]; !exists {
		return fmt.Errorf("node %s does not exist", nodeID)
	}

	delete(cm.nodes, nodeID)
	// 从一致性哈希环中移除节点
	cm.hashRing.Remove(nodeID)

	// 重新平衡分区
	if cm.config.PartitionCount > 0 {
		cm.rebalancePartitions()
	}

	// 触发节点移除事件
	cm.triggerEvent(ClusterEvent{
		Type:      "node_removed",
		NodeID:    nodeID,
		Timestamp: time.Now(),
	})

	return nil
}

// rebalancePartitions 重新平衡分区
func (cm *ClusterManager) rebalancePartitions() {
	// 实现分区重新平衡逻辑
}

// AddEventListener 添加事件监听器
func (cm *ClusterManager) AddEventListener(listener func(ClusterEvent)) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	cm.eventListeners = append(cm.eventListeners, listener)
}

// triggerEvent 触发事件
func (cm *ClusterManager) triggerEvent(event ClusterEvent) {
	for _, listener := range cm.eventListeners {
		go listener(event)
	}
}

// GetNodeIDs 获取所有节点ID
func (cm *ClusterManager) GetNodeIDs() []string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	nodeIDs := make([]string, 0, len(cm.nodes))
	for nodeID := range cm.nodes {
		nodeIDs = append(nodeIDs, nodeID)
	}

	return nodeIDs
}

// GetNode 获取节点信息
func (cm *ClusterManager) GetNode(nodeID string) (*ClusterNode, error) {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	node, exists := cm.nodes[nodeID]
	if !exists {
		return nil, fmt.Errorf("node %s not found", nodeID)
	}

	return node, nil
}

// GetNodes 获取所有节点
func (cm *ClusterManager) GetNodes() map[string]*ClusterNode {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	// 创建副本以避免并发访问问题
	nodesCopy := make(map[string]*ClusterNode, len(cm.nodes))
	for id, node := range cm.nodes {
		nodeCopy := *node // 创建节点的副本
		nodesCopy[id] = &nodeCopy
	}

	return nodesCopy
}

// HealthCheck 执行健康检查
func (cm *ClusterManager) HealthCheck(ctx context.Context) map[string]error {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	results := make(map[string]error)

	for nodeID, adapter := range cm.adapters {
		results[nodeID] = adapter.Ping(ctx)
	}

	return results
}

// Close 关闭集群管理器
func (cm *ClusterManager) Close() error {
	close(cm.stopChan)

	var errs []error

	// 关闭所有适配器
	for nodeID, adapter := range cm.adapters {
		if err := adapter.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close adapter for node %s: %w", nodeID, err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors occurred while closing cluster manager: %v", errs)
	}

	return nil
}
