/**
 * Lara主题自定义变量
 */
export default {
    // 基础变量
    'border-radius': '4px',
    'font-family': '"Roboto", sans-serif',

    // 自定义颜色
    'custom-primary': 'var(--primary-color)',
    'custom-secondary': '#607D8B',
    'custom-success': '#4CAF50',
    'custom-info': '#2196F3',
    'custom-warning': '#FF9800',
    'custom-danger': '#F44336',

    // 自定义尺寸
    'spacing-xs': '0.25rem',
    'spacing-sm': '0.5rem',
    'spacing-md': '1rem',
    'spacing-lg': '1.5rem',
    'spacing-xl': '2rem',

    // 自定义阴影
    'shadow-sm': '0 1px 1px rgba(0,0,0,0.05)',
    'shadow': '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
    'shadow-md': '0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)',
    'shadow-lg': '0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)',
    'shadow-xl': '0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22)',

    // 自定义过渡
    'transition-duration': '0.3s',
    'transition-timing-function': 'cubic-bezier(0.4, 0, 0.2, 1)'
}; 