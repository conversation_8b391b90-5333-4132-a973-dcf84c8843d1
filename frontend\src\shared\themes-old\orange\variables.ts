/**
 * 温暖友好主题 - CSS变量
 * 橙色主调 + 棕色 + 米色
 * 设计参考: PrimeVue Amber主题 + Tailwind暖色系
 */

// CSS变量配置
const variables = {
    // 主要颜色
    'color-primary': '#ea580c',
    'color-primary-light': '#f97316',
    'color-primary-dark': '#c2410c',
    'color-primary-contrast': '#ffffff',
    'color-primary-secondary': '#fbbf24', // 第二配色 - 比主色浅的相同色系

    // 强调色
    'color-accent': '#d97706',
    'color-accent-light': '#f59e0b',
    'color-accent-dark': '#b45309',
    'color-accent-contrast': '#ffffff',

    // 中性色调 - 暖色系
    'color-neutral-50': '#fffbeb',
    'color-neutral-100': '#fef3c7',
    'color-neutral-200': '#fde68a',
    'color-neutral-300': '#fcd34d',
    'color-neutral-400': '#fbbf24',
    'color-neutral-500': '#f59e0b',
    'color-neutral-600': '#d97706',
    'color-neutral-700': '#b45309',
    'color-neutral-800': '#92400e',
    'color-neutral-900': '#78350f',

    // 成功/错误/警告/信息色
    'color-success': '#65a30d',
    'color-error': '#b91c1c',
    'color-warning': '#d97706',
    'color-info': '#0369a1',

    // 背景颜色
    'color-background': '#ffffff',
    'color-background-alt': '#fffbeb',
    'color-background-hover': '#fef3c7',
    'color-background-card': 'linear-gradient(145deg, #ffffff, #fffbeb)',
    // 主页面背景和文字色
    'color-page-background': '#fefcf3',
    'color-page-text': '#78350f',

    // 文本颜色
    'color-text': '#78350f',
    'color-text-light': '#92400e',
    'color-text-lighter': '#b45309',
    'color-text-contrast': '#ffffff',

    // 边框颜色
    'color-border': '#fde68a',
    'color-border-light': '#fef3c7',
    'color-border-dark': '#fcd34d',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(234, 88, 12, 0.05)',
    'shadow': '0 1px 3px 0 rgba(234, 88, 12, 0.1), 0 1px 2px 0 rgba(234, 88, 12, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(234, 88, 12, 0.1), 0 2px 4px -1px rgba(234, 88, 12, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(234, 88, 12, 0.1), 0 4px 6px -2px rgba(234, 88, 12, 0.05)',

    // 特定区域颜色
    'header-gradient': 'linear-gradient(135deg, #ea580c, #f97316)',
    'footer-gradient': 'linear-gradient(135deg, #fef3c7, #fffbeb)',
    'color-nav-gradient': '#78350f',
    'color-footer-gradient': '#92400e',
    'color-footer-border': '#fde68a',
    'button-gradient': 'linear-gradient(135deg, #f97316, #ea580c)',
    'card-gradient': 'linear-gradient(145deg, #ffffff, #fffbeb)',
    'accent-gradient': 'linear-gradient(135deg, #f59e0b, #d97706)',

    // PrimeVue集成
    'primary-color': '#ea580c',
    'primary-color-text': '#ffffff',
    'surface-ground': '#fefcf3',
    'surface-section': '#fefcf3',
    'surface-card': 'linear-gradient(145deg, #ffffff, #fffbeb)',
    'surface-overlay': '#ffffff',
    'surface-border': '#fde68a',
    'surface-hover': '#fef3c7',
    // 主页面内容区域
    'content-bg': '#fefcf3',
    'content-text': '#78350f',
};

export default variables;