<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :top="top"
    :modal="modal"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :class="['slinky-dialog', 'batch-action-dialog', dialogClass]"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <div class="dialog-content">
      <!-- 操作提示 -->
      <div class="action-info">
        <el-alert
          :title="`${actionConfig.title} (${selectedItems.length} 项)`"
          :type="actionConfig.alertType || 'warning'"
          :description="actionConfig.description"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 选中项目预览 -->
      <div v-if="showSelectedItems" class="selected-items">
        <div class="section-title">
          <el-icon><List /></el-icon>
          <span>选中项目</span>
        </div>
        <div class="items-list">
          <div
            v-for="(item, index) in selectedItems"
            :key="getItemKey(item, index)"
            class="selected-item"
          >
            <div class="item-info">
              <div class="item-title">{{ getItemTitle(item) }}</div>
              <div v-if="getItemSubtitle(item)" class="item-subtitle">
                {{ getItemSubtitle(item) }}
              </div>
            </div>
            <div v-if="actionConfig.showItemStatus" class="item-status">
              <el-tag :type="getItemStatusType(item)" size="small">
                {{ getItemStatusText(item) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作配置表单 -->
      <div v-if="actionConfig.fields && actionConfig.fields.length > 0" class="action-form">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>操作配置</span>
        </div>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          :label-width="labelWidth"
          :label-position="labelPosition"
          :size="size"
        >
          <template v-for="field in actionConfig.fields" :key="field.prop">
            <el-form-item
              :label="field.label"
              :prop="field.prop"
              :rules="field.rules"
              :required="field.required"
            >
              <!-- 文本输入 -->
              <el-input
                v-if="field.type === 'input'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
                :maxlength="field.maxlength"
                :show-word-limit="field.showWordLimit"
              />
              
              <!-- 文本域 -->
              <el-input
                v-else-if="field.type === 'textarea'"
                v-model="formData[field.prop]"
                type="textarea"
                :placeholder="field.placeholder"
                :rows="field.rows || 3"
                :maxlength="field.maxlength"
                :show-word-limit="field.showWordLimit"
              />
              
              <!-- 下拉选择 -->
              <el-select
                v-else-if="field.type === 'select'"
                v-model="formData[field.prop]"
                :placeholder="field.placeholder"
                :clearable="field.clearable !== false"
                :disabled="field.disabled"
                style="width: 100%"
              >
                <el-option
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                  :disabled="option.disabled"
                />
              </el-select>
              
              <!-- 单选框 -->
              <el-radio-group
                v-else-if="field.type === 'radio'"
                v-model="formData[field.prop]"
                :disabled="field.disabled"
              >
                <el-radio
                  v-for="option in field.options"
                  :key="option.value"
                  :label="option.value"
                  :disabled="option.disabled"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
              
              <!-- 开关 -->
              <el-switch
                v-else-if="field.type === 'switch'"
                v-model="formData[field.prop]"
                :disabled="field.disabled"
                :active-text="field.activeText"
                :inactive-text="field.inactiveText"
                :active-value="field.activeValue"
                :inactive-value="field.inactiveValue"
              />
              
              <!-- 日期选择 -->
              <el-date-picker
                v-else-if="field.type === 'date'"
                v-model="formData[field.prop]"
                :type="field.dateType || 'date'"
                :placeholder="field.placeholder"
                :format="field.format"
                :value-format="field.valueFormat"
                :clearable="field.clearable !== false"
                style="width: 100%"
              />
              
              <!-- 数字输入 -->
              <el-input-number
                v-else-if="field.type === 'number'"
                v-model="formData[field.prop]"
                :min="field.min"
                :max="field.max"
                :step="field.step"
                :precision="field.precision"
                :disabled="field.disabled"
                style="width: 100%"
              />
            </el-form-item>
          </template>
        </el-form>
      </div>

      <!-- 操作确认 -->
      <div class="action-confirm">
        <div class="section-title">
          <el-icon><WarningFilled /></el-icon>
          <span>操作确认</span>
        </div>
        <div class="confirm-content">
          <p class="confirm-text">{{ actionConfig.confirmText || `确认要对 ${selectedItems.length} 个项目执行${actionConfig.title}操作吗？` }}</p>
          <div v-if="actionConfig.warning" class="warning-text">
            <el-icon><Warning /></el-icon>
            <span>{{ actionConfig.warning }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!-- 取消按钮 -->
        <el-button 
          :disabled="submitting"
          @click="handleCancel"
        >
          取消
        </el-button>
        
        <!-- 确认按钮 -->
        <el-button 
          :type="actionConfig.buttonType || 'primary'" 
          :loading="submitting"
          :disabled="submitting"
          @click="handleSubmit"
        >
          <template v-if="!submitting">
            <el-icon v-if="actionConfig.buttonIcon">
              <component :is="actionConfig.buttonIcon" />
            </el-icon>
            {{ actionConfig.buttonText || actionConfig.title }}
          </template>
          <template v-else>
            {{ actionConfig.loadingText || '处理中...' }}
          </template>
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {
    List,
    Setting,
    Warning,
    WarningFilled
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { computed, reactive, ref } from 'vue'

// 接口定义
interface BatchFormField {
  prop: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'radio' | 'switch' | 'date' | 'number'
  required?: boolean
  rules?: any[]
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  
  // input/textarea 特有属性
  maxlength?: number
  showWordLimit?: boolean
  rows?: number
  
  // select/radio 特有属性
  options?: { label: string; value: any; disabled?: boolean }[]
  
  // switch 特有属性
  activeText?: string
  inactiveText?: string
  activeValue?: any
  inactiveValue?: any
  
  // date 特有属性
  dateType?: string
  format?: string
  valueFormat?: string
  
  // number 特有属性
  min?: number
  max?: number
  step?: number
  precision?: number
}

interface BatchActionConfig {
  name: string
  title: string
  description?: string
  alertType?: 'success' | 'info' | 'warning' | 'error'
  confirmText?: string
  warning?: string
  buttonText?: string
  buttonType?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  buttonIcon?: any
  loadingText?: string
  fields?: BatchFormField[]
  showItemStatus?: boolean
}

interface Props {
  // 基础属性
  modelValue?: boolean
  title?: string
  width?: string | number
  top?: string
  modal?: boolean
  appendToBody?: boolean
  lockScroll?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  destroyOnClose?: boolean
  dialogClass?: string
  
  // 数据属性
  selectedItems: any[]
  actionConfig: BatchActionConfig
  
  // 表单配置
  rules?: FormRules
  labelWidth?: string
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'default' | 'small'
  
  // 显示配置
  showSelectedItems?: boolean
  
  // 项目显示配置
  itemTitleField?: string
  itemSubtitleField?: string
  itemStatusField?: string
  itemStatusOptions?: { value: any; label: string; type: string }[]
  
  // 事件回调
  onSubmit?: (formData: Record<string, any>, selectedItems: any[]) => Promise<void>
  onCancel?: () => void
  onBeforeClose?: (done: () => void) => void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: '批量操作',
  width: '600px',
  top: '15vh',
  modal: true,
  appendToBody: true,
  lockScroll: true,
  closeOnClickModal: false,
  closeOnPressEscape: true,
  showClose: true,
  destroyOnClose: false,
  labelWidth: '100px',
  labelPosition: 'left',
  size: 'default',
  showSelectedItems: true,
  itemTitleField: 'name',
  itemSubtitleField: '',
  itemStatusField: 'status',
  itemStatusOptions: () => [
    { value: 1, label: '启用', type: 'success' },
    { value: 0, label: '禁用', type: 'danger' }
  ]
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'open': []
  'opened': []
  'close': []
  'closed': []
  'submit': [formData: Record<string, any>, selectedItems: any[]]
  'cancel': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const formData = reactive<Record<string, any>>({})
const submitting = ref(false)

// 辅助方法
const getItemKey = (item: any, index: number): string => {
  return item.id || item.key || index.toString()
}

const getItemTitle = (item: any): string => {
  const title = item[props.itemTitleField] || item.title || item.name
  return title || '未命名项目'
}

const getItemSubtitle = (item: any): string => {
  if (!props.itemSubtitleField) return ''
  return item[props.itemSubtitleField] || ''
}

const getItemStatusText = (item: any): string => {
  if (!props.itemStatusField) return ''
  
  const statusValue = item[props.itemStatusField]
  const statusOption = props.itemStatusOptions.find(opt => opt.value === statusValue)
  return statusOption?.label || String(statusValue)
}

const getItemStatusType = (item: any): string => {
  if (!props.itemStatusField) return 'info'
  
  const statusValue = item[props.itemStatusField]
  const statusOption = props.itemStatusOptions.find(opt => opt.value === statusValue)
  return statusOption?.type || 'info'
}

// 初始化表单数据
const initFormData = () => {
  if (!props.actionConfig.fields) return
  
  props.actionConfig.fields.forEach(field => {
    formData[field.prop] = getDefaultValue(field.type)
  })
}

const getDefaultValue = (type: string) => {
  switch (type) {
    case 'switch':
      return false
    case 'number':
      return 0
    default:
      return ''
  }
}

// 事件处理
const handleSubmit = async () => {
  if (!formRef.value) {
    await submitAction()
    return
  }
  
  try {
    await formRef.value.validate()
    await submitAction()
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const submitAction = async () => {
  submitting.value = true
  
  try {
    if (props.onSubmit) {
      await props.onSubmit(formData, props.selectedItems)
    }
    
    emit('submit', { ...formData }, props.selectedItems)
    visible.value = false
  } catch (error) {
    console.error('Batch action failed:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
  props.onCancel?.()
  visible.value = false
}

const handleBeforeClose = (done: () => void) => {
  if (props.onBeforeClose) {
    props.onBeforeClose(done)
  } else {
    done()
  }
}

const handleOpen = () => {
  initFormData()
  emit('open')
}

const handleOpened = () => {
  emit('opened')
}

const handleClose = () => {
  emit('close')
}

const handleClosed = () => {
  emit('closed')
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  clearValidate: () => formRef.value?.clearValidate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.slinky-dialog.batch-action-dialog {
  .dialog-content {
    .action-info {
      margin-bottom: 20px;
    }
    
    .selected-items {
      margin-bottom: 20px;
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-color-primary);
        margin-bottom: 12px;
        
        .el-icon {
          font-size: 16px;
        }
      }
      
      .items-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid var(--el-border-color-light);
        border-radius: var(--el-card-border-radius);
        
        .selected-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          
          &:last-child {
            border-bottom: none;
          }
          
          .item-info {
            flex: 1;
            
            .item-title {
              font-weight: 500;
              color: var(--el-text-color-primary);
              margin-bottom: 4px;
            }
            
            .item-subtitle {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
          
          .item-status {
            margin-left: 12px;
          }
        }
      }
    }
    
    .action-form {
      margin-bottom: 20px;
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-color-primary);
        margin-bottom: 16px;
        
        .el-icon {
          font-size: 16px;
        }
      }
    }
    
    .action-confirm {
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-color-warning);
        margin-bottom: 12px;
        
        .el-icon {
          font-size: 16px;
        }
      }
      
      .confirm-content {
        .confirm-text {
          margin: 0 0 12px 0;
          color: var(--el-text-color-primary);
          font-size: 14px;
        }
        
        .warning-text {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background-color: var(--el-color-warning-light-9);
          color: var(--el-color-warning);
          border-radius: var(--el-card-border-radius);
          font-size: 13px;
          
          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    .el-button {
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

// 滚动条样式
.items-list {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background-color: var(--el-fill-color-lighter);
    border-radius: var(--el-card-border-radius);
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: var(--el-card-border-radius);
  }
}
</style> 