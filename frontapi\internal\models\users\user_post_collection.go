package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// PostCollection 用户帖子收藏表
type UserPostCollection struct {
	models.BaseModel
	UserID          string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                  //用户ID
	PostID          string         `gorm:"column:post_id;type:string;not null;comment:帖子ID" json:"post_id"`                                  //帖子ID
	PostContent     string         `gorm:"column:post_content;type:string;size:255;comment:帖子内容摘要" json:"post_content"`                      //帖子内容摘要
	PostImages      string         `gorm:"column:post_images;type:string;size:255;comment:帖子图片(第一张)" json:"post_images"`                     //帖子图片(第一张)
	AuthorID        string         `gorm:"column:author_id;type:string;comment:作者ID" json:"author_id"`                                       //作者ID
	AuthorName      string         `gorm:"column:author_name;type:string;size:50;comment:作者名称" json:"author_name"`                           //作者名称
	AuthorAvatar    string         `gorm:"column:author_avatar;type:string;size:255;comment:作者头像" json:"author_avatar"`                      //作者头像
	CategoryID      string         `gorm:"column:category_id;type:string;comment:分类ID" json:"category_id"`                                   //分类ID
	CategoryName    string         `gorm:"column:category_name;type:string;size:50;comment:分类名称" json:"category_name"`                       //分类名称
	LikeCount       int            `gorm:"column:like_count;type:int;comment:点赞数" json:"like_count"`                                         //点赞数
	CommentCount    int            `gorm:"column:comment_count;type:int;comment:评论数" json:"comment_count"`                                   //评论数
	CollectionTime  types.JSONTime `gorm:"column:collection_time;type:time;default:current_timestamp;comment:收藏时间" json:"collection_time"`   //收藏时间
	CollectionGroup string         `gorm:"column:collection_group;type:string;size:50;default:'默认收藏夹';comment:收藏分组" json:"collection_group"` //收藏分组
	Note            string         `gorm:"column:note;type:string;size:255;comment:收藏备注" json:"note"`                                        //收藏备注

}

// TableName 表名
func (UserPostCollection) TableName() string {
	return "ly_user_post_collections"
}
