import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from '@/locales';

const routes: GeneratedRoute[] = [
  {
    name: 'posts',
    path: '/posts',
    component: 'layout.base',
    meta: {
      title: 'posts',
      i18nKey: 'route.posts',
      icon: 'lucide:file-text',
      order: 5
    },
    children: [
      // 帖子列表页面
      {
        name: 'posts_list',
        path: '/posts/list',
        component: 'view.posts_list',
        meta: {
          title: 'posts_list',
          i18nKey: 'route.posts_list',
          icon: 'lucide:list'
        }
      },
      // 帖子评论页面
      {
        name: 'posts_comment',
        path: '/posts/comment',
        component: 'view.posts_comment',
        meta: {
          title: 'posts_comment',
          i18nKey: 'route.posts_comment',
          icon: 'lucide:message-circle'
        }
      }
    ]
  }
];

export default routes;
