package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extcollect/types"
)

// MongoConfig MongoDB配置
type MongoConfig struct {
	Database   string `json:"database"`
	Collection string `json:"collection"`
}

// MongoAdapter MongoDB适配器
type MongoAdapter struct {
	client     *mongo.Client
	database   *mongo.Database
	collection *mongo.Collection
	config     *MongoConfig
}

// NewMongoAdapter 创建MongoDB适配器
func NewMongoAdapter(client *mongo.Client, database *mongo.Database, config *MongoConfig) (*MongoAdapter, error) {
	if client == nil {
		return nil, fmt.Errorf("MongoDB客户端不能为空")
	}
	if database == nil {
		return nil, fmt.Errorf("MongoDB数据库不能为空")
	}
	if config == nil {
		config = &MongoConfig{
			Database:   "frontapi_collect",
			Collection: "collects",
		}
	}

	collection := database.Collection(config.Collection)

	return &MongoAdapter{
		client:     client,
		database:   database,
		collection: collection,
		config:     config,
	}, nil
}

// Collect 收藏项目
func (m *MongoAdapter) Collect(ctx context.Context, userID, itemID, itemType string) error {
	record := &types.CollectRecord{
		UserID:    userID,
		ItemID:    itemID,
		ItemType:  itemType,
		Timestamp: time.Now(),
		Status:    "collected",
		Version:   1,
	}

	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": record,
		"$inc": bson.M{"version": 1},
	}

	opts := options.Update().SetUpsert(true)
	_, err := m.collection.UpdateOne(ctx, filter, update, opts)
	return err
}

// Uncollect 取消收藏
func (m *MongoAdapter) Uncollect(ctx context.Context, userID, itemID, itemType string) error {
	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": bson.M{
			"status":    "uncollected",
			"timestamp": time.Now(),
		},
		"$inc": bson.M{"version": 1},
	}

	_, err := m.collection.UpdateOne(ctx, filter, update)
	return err
}

// IsCollected 检查是否已收藏
func (m *MongoAdapter) IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
		"status":    "collected",
	}

	count, err := m.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetCollectCount 获取收藏数量
func (m *MongoAdapter) GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error) {
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
		"status":    "collected",
	}

	return m.collection.CountDocuments(ctx, filter)
}

// BatchCollect 批量收藏
func (m *MongoAdapter) BatchCollect(ctx context.Context, operations []*types.CollectOperation) error {
	var models []mongo.WriteModel

	for _, op := range operations {
		if op.Action == "collect" {
			filter := bson.M{
				"user_id":   op.UserID,
				"item_id":   op.ItemID,
				"item_type": op.ItemType,
			}

			update := bson.M{
				"$set": bson.M{
					"user_id":   op.UserID,
					"item_id":   op.ItemID,
					"item_type": op.ItemType,
					"timestamp": op.Timestamp,
					"status":    "collected",
					"metadata":  op.Metadata,
				},
				"$inc": bson.M{"version": 1},
			}

			model := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update).
				SetUpsert(true)

			models = append(models, model)
		}
	}

	if len(models) == 0 {
		return nil
	}

	_, err := m.collection.BulkWrite(ctx, models)
	return err
}

// BatchUncollect 批量取消收藏
func (m *MongoAdapter) BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error {
	var models []mongo.WriteModel

	for _, op := range operations {
		if op.Action == "uncollect" {
			filter := bson.M{
				"user_id":   op.UserID,
				"item_id":   op.ItemID,
				"item_type": op.ItemType,
			}

			update := bson.M{
				"$set": bson.M{
					"status":    "uncollected",
					"timestamp": op.Timestamp,
				},
				"$inc": bson.M{"version": 1},
			}

			model := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update)

			models = append(models, model)
		}
	}

	if len(models) == 0 {
		return nil
	}

	_, err := m.collection.BulkWrite(ctx, models)
	return err
}

// BatchGetCollectStatus 批量获取收藏状态
func (m *MongoAdapter) BatchGetCollectStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	result := make(map[string]bool)

	var orConditions []bson.M
	for itemID, itemType := range items {
		orConditions = append(orConditions, bson.M{
			"item_id":   itemID,
			"item_type": itemType,
		})
	}

	filter := bson.M{
		"user_id": userID,
		"$or":     orConditions,
		"status":  "collected",
	}

	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// 初始化所有为false
	for itemID := range items {
		result[itemID] = false
	}

	// 设置已收藏的为true
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err == nil {
			result[record.ItemID] = true
		}
	}

	return result, nil
}

// BatchGetCollectCounts 批量获取收藏数量
func (m *MongoAdapter) BatchGetCollectCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	result := make(map[string]int64)

	for itemID, itemType := range items {
		count, err := m.GetCollectCount(ctx, itemID, itemType)
		if err != nil {
			result[itemID] = 0
		} else {
			result[itemID] = count
		}
	}

	return result, nil
}

// GetUserCollections 获取用户收藏列表
func (m *MongoAdapter) GetUserCollections(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	filter := bson.M{
		"user_id":   userID,
		"item_type": itemType,
		"status":    "collected",
	}

	opts := options.Find().
		SetSort(bson.D{{"timestamp", -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.CollectRecord
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err == nil {
			records = append(records, &record)
		}
	}

	return records, nil
}

// GetItemCollectors 获取项目收藏者列表
func (m *MongoAdapter) GetItemCollectors(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.CollectRecord, error) {
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
		"status":    "collected",
	}

	opts := options.Find().
		SetSort(bson.D{{"timestamp", -1}}).
		SetLimit(int64(limit)).
		SetSkip(int64(offset))

	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.CollectRecord
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err == nil {
			records = append(records, &record)
		}
	}

	return records, nil
}

// GetCollectHistory 获取收藏历史
func (m *MongoAdapter) GetCollectHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.CollectRecord, error) {
	filter := bson.M{
		"user_id":   userID,
		"item_type": itemType,
	}

	if timeRange != nil {
		filter["timestamp"] = bson.M{
			"$gte": timeRange.Start,
			"$lte": timeRange.End,
		}
	}

	opts := options.Find().SetSort(bson.D{{"timestamp", -1}})

	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var records []*types.CollectRecord
	for cursor.Next(ctx) {
		var record types.CollectRecord
		if err := cursor.Decode(&record); err == nil {
			records = append(records, &record)
		}
	}

	return records, nil
}

// UpdateHotRank 更新热度排名
func (m *MongoAdapter) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	// MongoDB版本可以使用单独的集合存储排名信息
	rankingCollection := m.database.Collection("collect_rankings")

	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": bson.M{
			"item_id":    itemID,
			"item_type":  itemType,
			"score":      score,
			"updated_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := rankingCollection.UpdateOne(ctx, filter, update, opts)
	return err
}

// GetHotRanking 获取热门排行
func (m *MongoAdapter) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	rankingCollection := m.database.Collection("collect_rankings")

	filter := bson.M{"item_type": itemType}
	opts := options.Find().
		SetSort(bson.D{{"score", -1}}).
		SetLimit(int64(limit))

	cursor, err := rankingCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var ranking []string
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err == nil {
			if itemID, ok := doc["item_id"].(string); ok {
				ranking = append(ranking, itemID)
			}
		}
	}

	return ranking, nil
}

// GetHotRankingWithScores 获取带分数的热门排行
func (m *MongoAdapter) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	rankingCollection := m.database.Collection("collect_rankings")

	filter := bson.M{"item_type": itemType}
	opts := options.Find().
		SetSort(bson.D{{"score", -1}}).
		SetLimit(int64(limit))

	cursor, err := rankingCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	ranking := make(map[string]float64)
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err == nil {
			if itemID, ok := doc["item_id"].(string); ok {
				if score, ok := doc["score"].(float64); ok {
					ranking[itemID] = score
				}
			}
		}
	}

	return ranking, nil
}

// GetUserCollectStats 获取用户收藏统计
func (m *MongoAdapter) GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error) {
	pipeline := []bson.M{
		{"$match": bson.M{"user_id": userID, "status": "collected"}},
		{"$group": bson.M{
			"_id":            "$user_id",
			"total_collects": bson.M{"$sum": 1},
			"collects_by_type": bson.M{
				"$push": bson.M{
					"item_type": "$item_type",
				},
			},
			"first_collect": bson.M{"$min": "$timestamp"},
			"last_collect":  bson.M{"$max": "$timestamp"},
		}},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result bson.M
		if err := cursor.Decode(&result); err == nil {
			stats := &types.UserCollectStats{
				UserID:        userID,
				TotalCollects: result["total_collects"].(int64),
			}

			if firstCollect, ok := result["first_collect"].(time.Time); ok {
				stats.FirstCollectAt = &firstCollect
			}
			if lastCollect, ok := result["last_collect"].(time.Time); ok {
				stats.LastCollectAt = &lastCollect
			}

			return stats, nil
		}
	}

	return &types.UserCollectStats{UserID: userID}, nil
}

// GetItemCollectStats 获取项目收藏统计
func (m *MongoAdapter) GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error) {
	count, err := m.GetCollectCount(ctx, itemID, itemType)
	if err != nil {
		return nil, err
	}

	// 可以添加更多统计信息
	stats := &types.ItemCollectStats{
		ItemID:        itemID,
		ItemType:      itemType,
		TotalCollects: count,
	}

	return stats, nil
}

// GetTrendingItems 获取趋势项目
func (m *MongoAdapter) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.CollectTrend, error) {
	matchStage := bson.M{
		"$match": bson.M{
			"item_type": itemType,
			"status":    "collected",
		},
	}

	if timeRange != nil {
		matchStage["$match"].(bson.M)["timestamp"] = bson.M{
			"$gte": timeRange.Start,
			"$lte": timeRange.End,
		}
	}

	pipeline := []bson.M{
		matchStage,
		{"$group": bson.M{
			"_id":           "$item_id",
			"collect_count": bson.M{"$sum": 1},
			"user_count":    bson.M{"$addToSet": "$user_id"},
		}},
		{"$project": bson.M{
			"item_id":       "$_id",
			"collect_count": 1,
			"user_count":    bson.M{"$size": "$user_count"},
		}},
		{"$sort": bson.M{"collect_count": -1}},
		{"$limit": limit},
	}

	cursor, err := m.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var trends []*types.CollectTrend
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err == nil {
			trend := &types.CollectTrend{
				ItemID:       doc["item_id"].(string),
				ItemType:     itemType,
				CollectCount: doc["collect_count"].(int64),
				UserCount:    doc["user_count"].(int64),
			}
			trends = append(trends, trend)
		}
	}

	return trends, nil
}

// InvalidateCache 清除缓存（MongoDB不需要实现）
func (m *MongoAdapter) InvalidateCache(ctx context.Context, keys ...string) error {
	return nil
}

// WarmupCache 预热缓存（MongoDB不需要实现）
func (m *MongoAdapter) WarmupCache(ctx context.Context, itemType string, itemIDs []string) error {
	return nil
}

// GetCacheStats 获取缓存统计（MongoDB返回数据库统计）
func (m *MongoAdapter) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	stats := m.database.RunCommand(ctx, bson.D{{"dbStats", 1}})
	var result bson.M
	if err := stats.Decode(&result); err != nil {
		return nil, err
	}
	return result, nil
}

// CleanupExpiredData 清理过期数据
func (m *MongoAdapter) CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error {
	filter := bson.M{
		"item_type": itemType,
		"timestamp": bson.M{"$lt": before},
	}

	_, err := m.collection.DeleteMany(ctx, filter)
	return err
}

// ExportData 导出数据
func (m *MongoAdapter) ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error) {
	return nil, fmt.Errorf("MongoDB适配器暂不支持导出")
}

// ImportData 导入数据
func (m *MongoAdapter) ImportData(ctx context.Context, data []byte, itemType, format string) error {
	return fmt.Errorf("MongoDB适配器暂不支持导入")
}

// HealthCheck 健康检查
func (m *MongoAdapter) HealthCheck(ctx context.Context) error {
	return m.client.Ping(ctx, nil)
}

// Close 关闭连接
func (m *MongoAdapter) Close() error {
	return m.client.Disconnect(context.Background())
}
