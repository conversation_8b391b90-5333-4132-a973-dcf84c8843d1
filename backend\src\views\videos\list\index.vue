<template>
    <el-scrollbar>
        <div class="video-list">
            <el-card>
                <template #header>
                    <div class="flex justify-between items-center">
                        <div class="card-title">{{ $t('videos.info.title') }}</div>
                        <div class="action-buttons">
                            <el-button type="primary" @click="onAdd">
                                <el-icon><Plus /></el-icon>
                                {{ $t('videos.info.addNew') }}
                            </el-button>
                            <el-button type="success" @click="onRefresh">
                                <el-icon><Refresh /></el-icon>
                                刷新
                            </el-button>
                        </div>
                    </div>
                </template>

                <!-- 搜索栏组件 -->
                <SearchBar @search="onSearch" @reset="onReset" @refresh="fetchVideoList" />

                <!-- 视频表格组件 -->
                <VideoTable 
                    :loading="loading" 
                    :videoList="videoList" 
                    :pagination="pagination"
                    @edit="onEdit" 
                    @detail="onDetail"
                    @delete="onDelete" 
                    @audit="onAudit"
                    @change-status="onChangeStatus"
                    @selection-change="onSelectionChange"
                    @batch-status="onBatchStatus"
                    @batch-delete="onBatchDelete"
                    @batch-audit="onBatchAudit"
                    @current-change="onPageChange"
                    @size-change="onSizeChange"
                />
            </el-card>

            <!-- 视频表单对话框 -->
            <VideoDialog 
                ref="videoDialogRef" 
                :visible="dialogVisible" 
                :videoData="currentVideo"
                :type="dialogType"
                @update:visible="dialogVisible = $event"
                @success="onSuccess"
                @close="dialogVisible = false" 
            />

            <!-- 视频详情弹窗 -->
            <VideoDetailDialog 
                :visible="detailDialogVisible" 
                :videoData="currentVideo"
                @close="detailDialogVisible = false"
                @update:visible="detailDialogVisible = $event"
            />

            <!-- 视频审核弹窗 -->
            <VideoApprovalDialog
                v-model:visible="approvalDialogVisible"
                :videoData="currentVideo"
                @success="onApprovalSuccess"
            />

            <!-- 视频批量审核弹窗 -->
            <VideoBatchApprovalDialog
                v-model:visible="batchApprovalDialogVisible"
                :videos="selectedVideos"
                @success="onApprovalSuccess"
            />
        </div>
    </el-scrollbar>
</template>

<script setup lang="ts">
import { batchDeleteVideos, batchUpdateVideoStatus, deleteVideo, getVideoDetail, getVideoList, updateVideoStatus } from '@/service/api/videos/videos';
import { VideoItem } from '@/types/videos';
import { handleApiError } from '@/utils/errorHandler';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import VideoApprovalDialog from './components/VideoApprovalDialog.vue';
import VideoBatchApprovalDialog from './components/VideoBatchApprovalDialog.vue';
import VideoDetailDialog from './components/VideoDetailDialog.vue';
import VideoDialog from './components/VideoFormDialog.vue';
import SearchBar from './components/VideoSearchBar.vue';
import VideoTable from './components/VideoTable.vue';

const { t } = useI18n();
const router = useRouter();

// 扩展VideoItem接口，添加前端兼容字段
interface ExtendedVideoItem extends VideoItem {
    viewCount?: number;
    likeCount?: number;
    favoriteCount?: number;
    commentCount?: number;
    categoryName?: string;
    coverUrl?: string;
    videoUrl?: string;
    celebrities?: any[];
}

// 初始化标签页
onMounted(() => {
    // 初始化数据
    fetchVideoList();
});

// 数据状态
const loading = ref(false);
const videoList = ref<ExtendedVideoItem[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref<Record<string, any>>({});
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const approvalDialogVisible = ref(false);
const batchApprovalDialogVisible = ref(false);
const currentVideo = ref<ExtendedVideoItem>({} as ExtendedVideoItem);
const selectedVideos = ref<ExtendedVideoItem[]>([]);
const dialogType = ref<'add' | 'edit'>('add');

// 分页信息
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
});

// 获取视频列表
const fetchVideoList = async () => {
    loading.value = true;
    try {
        const params = {
            page: {
                pageNo: currentPage.value,
                pageSize: pageSize.value,
            },
            data: {
                keyword: searchParams.value.keyword || '',
                category_id: searchParams.value.categoryId || '',
                channel_id: searchParams.value.channelId || '',
                channel_name: searchParams.value.channelName || '',
                creator_id: searchParams.value.creatorId || '',
                status: searchParams.value.status ? parseInt(searchParams.value.status) : undefined,
            }
        };

        const { data, err } = await getVideoList(params) as any;
        if (!err) {
            // 直接使用后端返回的数据，不做转换
            videoList.value = data.list;
            // 兼容前端旧字段，为每个视频项添加兼容字段
            videoList.value.forEach((item: ExtendedVideoItem) => {
                item.viewCount = item.view_count;
                item.likeCount = item.like_count;
                item.favoriteCount = item.share_count;
                item.commentCount = item.comment_count;
                item.categoryName = item.category_name;
                item.coverUrl = item.cover;
                item.videoUrl = item.url;

                // 处理标签数据 - 确保是数组格式
                if (typeof item.tags === 'string') {
                    try {
                        item.tags = JSON.parse(item.tags);
                    } catch (e) {
                        item.tags = [];
                    }
                } else if (!Array.isArray(item.tags)) {
                    item.tags = [];
                }

                // 处理明星数据 - 确保是数组格式
                if (typeof item.celebrities === 'string') {
                    try {
                        item.celebrities = JSON.parse(item.celebrities);
                    } catch (e) {
                        item.celebrities = [];
                    }
                } else if (!Array.isArray(item.celebrities)) {
                    item.celebrities = [];
                }
            });

            total.value = data.total || 0;
            pagination.total = total.value;
        }
    } catch (error) {
        handleApiError(error, '获取视频列表');
    } finally {
        loading.value = false;
    }
};

// 处理搜索
const onSearch = (formData: Record<string, any>) => {
    searchParams.value = formData;
    currentPage.value = 1;
    pagination.page = 1;
    fetchVideoList();
};

// 处理重置
const onReset = () => {
    searchParams.value = {};
    currentPage.value = 1;
    pagination.page = 1;
    fetchVideoList();
};

// 刷新
const onRefresh = () => {
    fetchVideoList();
};

// 处理分页
const onPageChange = (params: { page: number; pageSize: number }) => {
    currentPage.value = params.page;
    pageSize.value = params.pageSize;
    pagination.page = params.page;
    pagination.pageSize = params.pageSize;
    fetchVideoList();
};

// 处理添加视频
const onAdd = () => {
    currentVideo.value = {} as ExtendedVideoItem;
    dialogType.value = 'add';
    dialogVisible.value = true;
};

// 处理编辑视频
const onEdit = (row: ExtendedVideoItem) => {
    currentVideo.value = { ...row };
    console.log('currentVideo',currentVideo.value);
    dialogType.value = 'edit';
    dialogVisible.value = true;
};

// 处理查看详情
const onDetail = async (row: ExtendedVideoItem) => {
    console.log('row',row);
    try {
        const { data, response } = await getVideoDetail(row.id) as any;
        if (response.status === 200 && response.data.code === 2000) {
            currentVideo.value = data;
            detailDialogVisible.value = true;
        }else{
            ElMessage.error(response.data.message);
        }
    }catch(error) {
        handleApiError(error, '获取视频详情');
    }

};

// 处理审核视频
const onAudit = (row: ExtendedVideoItem) => {
    currentVideo.value = { ...row };
    approvalDialogVisible.value = true;
};

// 处理审核成功
const onApprovalSuccess = () => {
    // 刷新视频列表
    fetchVideoList();
};

// 处理状态变更
const onChangeStatus = async (id: string, status: number) => {
    try {
        const res = await updateVideoStatus(id, status);
        if (res) {
            ElMessage.success(`视频状态已${status === 2 ? '上架' : '下架'}`);
            fetchVideoList();
        }
    } catch (error) {
        handleApiError(error, '更新视频状态');
    }
};

// 处理选择变更
const onSelectionChange = (selection: ExtendedVideoItem[]) => {
    selectedVideos.value = selection;
};

// 处理批量状态更新
const onBatchStatus = async (status: number) => {
    if (selectedVideos.value.length === 0) {
        ElMessage.warning('请先选择要操作的视频');
        return;
    }

    try {
        const ids = selectedVideos.value.map(video => video.id);
        const res = await batchUpdateVideoStatus(ids, status);
        if (res) {
            ElMessage.success(`成功${status === 2 ? '上架' : '下架'}${ids.length}个视频`);
            fetchVideoList();
        }
    } catch (error) {
        handleApiError(error, '批量更新视频状态');
    }
};

// 处理批量审核
const onBatchAudit = (videos: ExtendedVideoItem[]) => {
    if (videos.length === 0) {
        ElMessage.warning('请先选择要审核的视频');
        return;
    }
    
    selectedVideos.value = videos;
    batchApprovalDialogVisible.value = true;
};

// 处理批量删除
const onBatchDelete = async () => {
    if (selectedVideos.value.length === 0) {
        ElMessage.warning('请先选择要删除的视频');
        return;
    }

    try {
        await ElMessageBox.confirm(
            `确定要删除选中的 ${selectedVideos.value.length} 个视频吗？此操作不可恢复！`,
            '批量删除确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        );

        const ids = selectedVideos.value.map(video => video.id);
        const res = await batchDeleteVideos(ids);
        if (res) {
            ElMessage.success(`成功删除${ids.length}个视频`);
            fetchVideoList();
        }
    } catch (error) {
        if (error !== 'cancel') {
            handleApiError(error, '批量删除视频');
        }
    }
};

// 处理删除视频
const onDelete = async (row: ExtendedVideoItem) => {
    try {
        await ElMessageBox.confirm(
            t('videos.info.deleteConfirmContent'),
            t('videos.info.deleteConfirm'),
            {
                confirmButtonText: t('buttons.confirm'),
                cancelButtonText: t('buttons.cancel'),
                type: 'warning'
            }
        );

        const res = await deleteVideo(row.id) as any;
        if (res && res.code === 2000) {
            ElMessage.success(t('videos.info.deleteSuccess'));
            fetchVideoList();
        }
    } catch (error) {
        if (error !== 'cancel') {
            handleApiError(error, '删除视频');
        }
    }
};


// 处理分页大小变化
const onSizeChange = (size: number) => {
    pageSize.value = size;
    pagination.pageSize = size;
    fetchVideoList();
};

// 处理成功
const onSuccess = () => {
    fetchVideoList();
};
</script>

<style scoped lang="scss">
.video-list {
    padding: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}
</style>
