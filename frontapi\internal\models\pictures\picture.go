package pictures

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
	"time"

	"github.com/guregu/null/v6"
)

// Picture 图片模型
type Picture struct {
	*models.ContentBaseModel
	URL        string         `json:"url" gorm:"column:url;not null" comment:"图片URL"`
	Width      int            `json:"width" gorm:"column:width;not null" comment:"宽度"`
	Height     int            `json:"height" gorm:"column:height;not null" comment:"高度"`
	Size       uint64         `json:"size" gorm:"column:size" comment:"文件大小(字节)"`
	AlbumID    null.String    `json:"album_id" gorm:"column:album_id" comment:"专辑ID"`
	AlbumTitle string         `json:"album_title" gorm:"column:album_title" comment:"专辑名称"`
	UploadTime types.JSONTime `json:"upload_time" gorm:"column:upload_time;default:CURRENT_TIMESTAMP" comment:"上传时间"`
	CreatedAt  time.Time      `json:"created_at" gorm:"default:CURRENT_TIMESTAMP;->;<-:create;comment:创建时间"`
	UpdatedAt  time.Time      `json:"updated_at" gorm:"default:CURRENT_TIMESTAMP;ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`
}

// TableName 指定表名
func (Picture) TableName() string {
	return "ly_pictures"
}

// 确保Picture实现BaseModel接口
