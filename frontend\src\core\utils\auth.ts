/**
 * 认证工具函数
 */

import { storage } from './storage'
import { hashPassword, verifyPassword, generateRandomString } from './crypto'

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string | number
  username: string
  email?: string
  avatar?: string
  roles: string[]
  permissions: string[]
  profile?: Record<string, any>
  lastLoginTime?: number
  tokenExpiry?: number
}

/**
 * 登录凭据接口
 */
export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
  captcha?: string
  twoFactorCode?: string
}

/**
 * 认证令牌接口
 */
export interface AuthToken {
  accessToken: string
  refreshToken?: string
  tokenType: string
  expiresIn: number
  scope?: string
  issuedAt: number
}

/**
 * 认证状态接口
 */
export interface AuthState {
  isAuthenticated: boolean
  user: UserInfo | null
  token: AuthToken | null
  permissions: string[]
  roles: string[]
}

/**
 * 认证配置选项
 */
export interface AuthOptions {
  tokenKey?: string
  userKey?: string
  refreshTokenKey?: string
  rememberKey?: string
  autoRefresh?: boolean
  refreshThreshold?: number // 令牌过期前多少毫秒开始刷新
  maxRetries?: number
  storage?: 'local' | 'session' | 'memory'
}

/**
 * 权限检查选项
 */
export interface PermissionOptions {
  mode?: 'all' | 'any' // 检查模式：all需要所有权限，any需要任一权限
  strict?: boolean // 严格模式：区分大小写
}

/**
 * 认证事件类型
 */
export interface AuthEvents {
  login: (user: UserInfo) => void
  logout: () => void
  tokenRefresh: (token: AuthToken) => void
  tokenExpired: () => void
  permissionDenied: (permission: string) => void
  userUpdate: (user: UserInfo) => void
}

/**
 * 认证管理器类
 */
export class AuthManager {
  private options: Required<AuthOptions>
  private listeners: Partial<AuthEvents> = {}
  private refreshTimer: number | null = null
  private state: AuthState = {
    isAuthenticated: false,
    user: null,
    token: null,
    permissions: [],
    roles: []
  }

  constructor(options: AuthOptions = {}) {
    this.options = {
      tokenKey: 'auth_token',
      userKey: 'auth_user',
      refreshTokenKey: 'auth_refresh_token',
      rememberKey: 'auth_remember',
      autoRefresh: true,
      refreshThreshold: 5 * 60 * 1000, // 5分钟
      maxRetries: 3,
      storage: 'local',
      ...options
    }

    this.loadAuthState()
    this.setupAutoRefresh()
  }

  /**
   * 加载认证状态
   */
  private loadAuthState(): void {
    try {
      const storageManager = this.getStorageManager()
      const token = storageManager.get<AuthToken>(this.options.tokenKey)
      const user = storageManager.get<UserInfo>(this.options.userKey)

      if (token && user) {
        // 检查令牌是否过期
        if (this.isTokenValid(token)) {
          this.state = {
            isAuthenticated: true,
            user,
            token,
            permissions: user.permissions || [],
            roles: user.roles || []
          }
        } else {
          this.clearAuthState()
        }
      }
    } catch (error) {
      console.error('Failed to load auth state:', error)
      this.clearAuthState()
    }
  }

  /**
   * 保存认证状态
   */
  private saveAuthState(): void {
    try {
      const storageManager = this.getStorageManager()
      
      if (this.state.token) {
        storageManager.set(this.options.tokenKey, this.state.token)
      }
      
      if (this.state.user) {
        storageManager.set(this.options.userKey, this.state.user)
      }
    } catch (error) {
      console.error('Failed to save auth state:', error)
    }
  }

  /**
   * 清除认证状态
   */
  private clearAuthState(): void {
    const storageManager = this.getStorageManager()
    storageManager.remove(this.options.tokenKey)
    storageManager.remove(this.options.userKey)
    storageManager.remove(this.options.refreshTokenKey)
    
    this.state = {
      isAuthenticated: false,
      user: null,
      token: null,
      permissions: [],
      roles: []
    }
    
    this.clearRefreshTimer()
  }

  /**
   * 获取存储管理器
   */
  private getStorageManager() {
    switch (this.options.storage) {
      case 'session':
        return storage // 这里应该是sessionStorage，但为了简化使用默认storage
      case 'memory':
        return storage // 这里应该是memoryStorage，但为了简化使用默认storage
      default:
        return storage
    }
  }

  /**
   * 检查令牌是否有效
   * @param token 令牌
   * @returns 是否有效
   */
  private isTokenValid(token: AuthToken): boolean {
    if (!token.expiresIn || !token.issuedAt) return true
    
    const expiryTime = token.issuedAt + token.expiresIn * 1000
    return Date.now() < expiryTime
  }

  /**
   * 检查令牌是否需要刷新
   * @param token 令牌
   * @returns 是否需要刷新
   */
  private shouldRefreshToken(token: AuthToken): boolean {
    if (!token.expiresIn || !token.issuedAt) return false
    
    const expiryTime = token.issuedAt + token.expiresIn * 1000
    const refreshTime = expiryTime - this.options.refreshThreshold
    return Date.now() >= refreshTime
  }

  /**
   * 设置自动刷新
   */
  private setupAutoRefresh(): void {
    if (!this.options.autoRefresh || !this.state.token) return
    
    this.clearRefreshTimer()
    
    if (this.shouldRefreshToken(this.state.token)) {
      this.refreshToken()
    } else {
      const refreshTime = this.state.token.issuedAt + 
        this.state.token.expiresIn * 1000 - 
        this.options.refreshThreshold
      const delay = Math.max(0, refreshTime - Date.now())
      
      this.refreshTimer = window.setTimeout(() => {
        this.refreshToken()
      }, delay)
    }
  }

  /**
   * 清除刷新定时器
   */
  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  /**
   * 登录
   * @param credentials 登录凭据
   * @returns Promise
   */
  async login(credentials: LoginCredentials): Promise<UserInfo> {
    try {
      // 这里应该调用实际的登录API
      // 为了演示，我们模拟一个登录过程
      const response = await this.mockLogin(credentials)
      
      const { user, token } = response
      
      this.state = {
        isAuthenticated: true,
        user,
        token,
        permissions: user.permissions || [],
        roles: user.roles || []
      }
      
      this.saveAuthState()
      
      if (credentials.remember) {
        const storageManager = this.getStorageManager()
        storageManager.set(this.options.rememberKey, true)
      }
      
      this.setupAutoRefresh()
      this.listeners.login?.(user)
      
      return user
    } catch (error) {
      throw new Error(`Login failed: ${error}`)
    }
  }

  /**
   * 模拟登录（实际项目中应该调用真实API）
   * @param credentials 登录凭据
   * @returns Promise
   */
  private async mockLogin(credentials: LoginCredentials): Promise<{ user: UserInfo; token: AuthToken }> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟验证
    if (credentials.username === 'admin' && credentials.password === 'password') {
      const user: UserInfo = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        avatar: '',
        roles: ['admin'],
        permissions: ['read', 'write', 'delete'],
        lastLoginTime: Date.now()
      }
      
      const token: AuthToken = {
        accessToken: generateRandomString(32),
        refreshToken: generateRandomString(32),
        tokenType: 'Bearer',
        expiresIn: 3600, // 1小时
        issuedAt: Date.now()
      }
      
      return { user, token }
    } else {
      throw new Error('Invalid credentials')
    }
  }

  /**
   * 登出
   */
  async logout(): Promise<void> {
    try {
      // 这里应该调用实际的登出API
      await this.mockLogout()
      
      this.clearAuthState()
      this.listeners.logout?.()
    } catch (error) {
      console.error('Logout error:', error)
      // 即使API调用失败，也要清除本地状态
      this.clearAuthState()
      this.listeners.logout?.()
    }
  }

  /**
   * 模拟登出（实际项目中应该调用真实API）
   */
  private async mockLogout(): Promise<void> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  /**
   * 刷新令牌
   */
  async refreshToken(): Promise<AuthToken> {
    try {
      if (!this.state.token?.refreshToken) {
        throw new Error('No refresh token available')
      }
      
      // 这里应该调用实际的刷新令牌API
      const newToken = await this.mockRefreshToken(this.state.token.refreshToken)
      
      this.state.token = newToken
      this.saveAuthState()
      this.setupAutoRefresh()
      this.listeners.tokenRefresh?.(newToken)
      
      return newToken
    } catch (error) {
      this.listeners.tokenExpired?.()
      this.clearAuthState()
      throw new Error(`Token refresh failed: ${error}`)
    }
  }

  /**
   * 模拟刷新令牌（实际项目中应该调用真实API）
   * @param refreshToken 刷新令牌
   * @returns Promise
   */
  private async mockRefreshToken(refreshToken: string): Promise<AuthToken> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return {
      accessToken: generateRandomString(32),
      refreshToken: generateRandomString(32),
      tokenType: 'Bearer',
      expiresIn: 3600,
      issuedAt: Date.now()
    }
  }

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  getCurrentUser(): UserInfo | null {
    return this.state.user
  }

  /**
   * 获取当前令牌
   * @returns 令牌
   */
  getCurrentToken(): AuthToken | null {
    return this.state.token
  }

  /**
   * 检查是否已认证
   * @returns 是否已认证
   */
  isAuthenticated(): boolean {
    return this.state.isAuthenticated && this.state.token !== null && this.isTokenValid(this.state.token)
  }

  /**
   * 检查是否有指定权限
   * @param permission 权限名称或权限数组
   * @param options 检查选项
   * @returns 是否有权限
   */
  hasPermission(permission: string | string[], options: PermissionOptions = {}): boolean {
    if (!this.isAuthenticated()) return false
    
    const { mode = 'all', strict = false } = options
    const userPermissions = this.state.permissions
    
    if (!userPermissions || userPermissions.length === 0) return false
    
    const permissions = Array.isArray(permission) ? permission : [permission]
    const normalizedUserPermissions = strict ? userPermissions : userPermissions.map(p => p.toLowerCase())
    const normalizedPermissions = strict ? permissions : permissions.map(p => p.toLowerCase())
    
    if (mode === 'all') {
      return normalizedPermissions.every(p => normalizedUserPermissions.includes(p))
    } else {
      return normalizedPermissions.some(p => normalizedUserPermissions.includes(p))
    }
  }

  /**
   * 检查是否有指定角色
   * @param role 角色名称或角色数组
   * @param options 检查选项
   * @returns 是否有角色
   */
  hasRole(role: string | string[], options: PermissionOptions = {}): boolean {
    if (!this.isAuthenticated()) return false
    
    const { mode = 'all', strict = false } = options
    const userRoles = this.state.roles
    
    if (!userRoles || userRoles.length === 0) return false
    
    const roles = Array.isArray(role) ? role : [role]
    const normalizedUserRoles = strict ? userRoles : userRoles.map(r => r.toLowerCase())
    const normalizedRoles = strict ? roles : roles.map(r => r.toLowerCase())
    
    if (mode === 'all') {
      return normalizedRoles.every(r => normalizedUserRoles.includes(r))
    } else {
      return normalizedRoles.some(r => normalizedUserRoles.includes(r))
    }
  }

  /**
   * 检查权限并抛出错误（如果没有权限）
   * @param permission 权限名称或权限数组
   * @param options 检查选项
   */
  requirePermission(permission: string | string[], options?: PermissionOptions): void {
    if (!this.hasPermission(permission, options)) {
      const permissionStr = Array.isArray(permission) ? permission.join(', ') : permission
      this.listeners.permissionDenied?.(permissionStr)
      throw new Error(`Permission denied: ${permissionStr}`)
    }
  }

  /**
   * 检查角色并抛出错误（如果没有角色）
   * @param role 角色名称或角色数组
   * @param options 检查选项
   */
  requireRole(role: string | string[], options?: PermissionOptions): void {
    if (!this.hasRole(role, options)) {
      const roleStr = Array.isArray(role) ? role.join(', ') : role
      throw new Error(`Role required: ${roleStr}`)
    }
  }

  /**
   * 更新用户信息
   * @param user 用户信息
   */
  updateUser(user: Partial<UserInfo>): void {
    if (!this.state.user) return
    
    this.state.user = { ...this.state.user, ...user }
    this.state.permissions = this.state.user.permissions || []
    this.state.roles = this.state.user.roles || []
    
    this.saveAuthState()
    this.listeners.userUpdate?.(this.state.user)
  }

  /**
   * 获取认证状态
   * @returns 认证状态
   */
  getAuthState(): AuthState {
    return { ...this.state }
  }

  /**
   * 添加事件监听器
   * @param event 事件名
   * @param listener 监听器函数
   */
  on<K extends keyof AuthEvents>(event: K, listener: AuthEvents[K]): void {
    this.listeners[event] = listener
  }

  /**
   * 移除事件监听器
   * @param event 事件名
   */
  off<K extends keyof AuthEvents>(event: K): void {
    delete this.listeners[event]
  }

  /**
   * 销毁认证管理器
   */
  destroy(): void {
    this.clearRefreshTimer()
    this.listeners = {}
  }
}

/**
 * 默认认证管理器实例
 */
export const authManager = new AuthManager()

/**
 * 创建认证管理器
 * @param options 配置选项
 * @returns 认证管理器实例
 */
export function createAuthManager(options?: AuthOptions): AuthManager {
  return new AuthManager(options)
}

/**
 * 快捷方法：登录
 * @param credentials 登录凭据
 * @returns Promise
 */
export function login(credentials: LoginCredentials): Promise<UserInfo> {
  return authManager.login(credentials)
}

/**
 * 快捷方法：登出
 * @returns Promise
 */
export function logout(): Promise<void> {
  return authManager.logout()
}

/**
 * 快捷方法：获取当前用户
 * @returns 用户信息
 */
export function getCurrentUser(): UserInfo | null {
  return authManager.getCurrentUser()
}

/**
 * 快捷方法：获取当前令牌
 * @returns 令牌
 */
export function getCurrentToken(): AuthToken | null {
  return authManager.getCurrentToken()
}

/**
 * 快捷方法：检查是否已认证
 * @returns 是否已认证
 */
export function isAuthenticated(): boolean {
  return authManager.isAuthenticated()
}

/**
 * 快捷方法：检查权限
 * @param permission 权限名称或权限数组
 * @param options 检查选项
 * @returns 是否有权限
 */
export function hasPermission(permission: string | string[], options?: PermissionOptions): boolean {
  return authManager.hasPermission(permission, options)
}

/**
 * 快捷方法：检查角色
 * @param role 角色名称或角色数组
 * @param options 检查选项
 * @returns 是否有角色
 */
export function hasRole(role: string | string[], options?: PermissionOptions): boolean {
  return authManager.hasRole(role, options)
}

/**
 * 快捷方法：要求权限
 * @param permission 权限名称或权限数组
 * @param options 检查选项
 */
export function requirePermission(permission: string | string[], options?: PermissionOptions): void {
  authManager.requirePermission(permission, options)
}

/**
 * 快捷方法：要求角色
 * @param role 角色名称或角色数组
 * @param options 检查选项
 */
export function requireRole(role: string | string[], options?: PermissionOptions): void {
  authManager.requireRole(role, options)
}

/**
 * 认证装饰器（用于类方法）
 */
export function requireAuth(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value
  
  descriptor.value = function (...args: any[]) {
    if (!isAuthenticated()) {
      throw new Error('Authentication required')
    }
    return originalMethod.apply(this, args)
  }
  
  return descriptor
}

/**
 * 权限装饰器（用于类方法）
 */
export function requirePermissions(permissions: string | string[], options?: PermissionOptions) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function (...args: any[]) {
      requirePermission(permissions, options)
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 角色装饰器（用于类方法）
 */
export function requireRoles(roles: string | string[], options?: PermissionOptions) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = function (...args: any[]) {
      requireRole(roles, options)
      return originalMethod.apply(this, args)
    }
    
    return descriptor
  }
}

/**
 * 认证工具函数
 */
export const AuthUtils = {
  /**
   * 生成随机密码
   * @param length 密码长度
   * @param includeSymbols 是否包含符号
   * @returns 随机密码
   */
  generatePassword(length = 12, includeSymbols = true): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
    
    let chars = lowercase + uppercase + numbers
    if (includeSymbols) {
      chars += symbols
    }
    
    return generateRandomString(length, chars)
  },

  /**
   * 检查密码强度
   * @param password 密码
   * @returns 强度分数（0-100）
   */
  checkPasswordStrength(password: string): number {
    let score = 0
    
    // 长度检查
    if (password.length >= 8) score += 25
    if (password.length >= 12) score += 25
    
    // 字符类型检查
    if (/[a-z]/.test(password)) score += 10
    if (/[A-Z]/.test(password)) score += 10
    if (/[0-9]/.test(password)) score += 10
    if (/[^a-zA-Z0-9]/.test(password)) score += 20
    
    return Math.min(score, 100)
  },

  /**
   * 验证邮箱格式
   * @param email 邮箱地址
   * @returns 是否有效
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 验证用户名格式
   * @param username 用户名
   * @returns 是否有效
   */
  isValidUsername(username: string): boolean {
    // 用户名：3-20个字符，只能包含字母、数字、下划线
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    return usernameRegex.test(username)
  },

  /**
   * 格式化权限名称
   * @param permission 权限名称
   * @returns 格式化后的权限名称
   */
  formatPermission(permission: string): string {
    return permission.toLowerCase().replace(/[^a-z0-9]/g, '_')
  },

  /**
   * 格式化角色名称
   * @param role 角色名称
   * @returns 格式化后的角色名称
   */
  formatRole(role: string): string {
    return role.toLowerCase().replace(/[^a-z0-9]/g, '_')
  },

  /**
   * 解析JWT令牌（不验证签名）
   * @param token JWT令牌
   * @returns 解析结果
   */
  parseJWT(token: string): any {
    try {
      const parts = token.split('.')
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format')
      }
      
      const payload = parts[1]
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'))
      return JSON.parse(decoded)
    } catch (error) {
      throw new Error(`Failed to parse JWT: ${error}`)
    }
  },

  /**
   * 检查JWT令牌是否过期
   * @param token JWT令牌
   * @returns 是否过期
   */
  isJWTExpired(token: string): boolean {
    try {
      const payload = this.parseJWT(token)
      if (!payload.exp) return false
      
      return Date.now() >= payload.exp * 1000
    } catch {
      return true
    }
  },

  /**
   * 获取JWT令牌的过期时间
   * @param token JWT令牌
   * @returns 过期时间戳
   */
  getJWTExpiry(token: string): number | null {
    try {
      const payload = this.parseJWT(token)
      return payload.exp ? payload.exp * 1000 : null
    } catch {
      return null
    }
  }
}