package books

import (
	"frontapi/internal/api"
	bookService "frontapi/internal/service/books"
)

// BookController 电子书控制器
type BookController struct {
	api.BaseController
	bookService        bookService.BookService
	categoryService    bookService.BookCategoryService
	chapterService     bookService.BookChapterService
	bookmarkService    bookService.BookmarkService
	readHistoryService bookService.BookReadHistoryService
	favoriteService    bookService.BookFavoriteService
}

// NewBookController 创建电子书控制器
func NewBookController(
	bookService bookService.BookService,
	categoryService bookService.BookCategoryService,
	chapterService bookService.BookChapterService,
	bookmarkService bookService.BookmarkService,
	readHistoryService bookService.BookReadHistoryService,
	favoriteService bookService.BookFavoriteService,
) *BookController {
	return &BookController{
		bookService:        bookService,
		categoryService:    categoryService,
		chapterService:     chapterService,
		bookmarkService:    bookmarkService,
		readHistoryService: readHistoryService,
		favoriteService:    favoriteService,
	}
}
