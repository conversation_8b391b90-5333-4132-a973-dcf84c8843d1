package shortvideos

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name        string `json:"name" validate:"required"`
	Code        string `json:"code" validate:"required"`
	Description string `json:"description"`
	ParentID    string `json:"parent_id"`
	URI         string `json:"uri"`
	Image       string `json:"image"`
	Icon        string `json:"icon"`
	SortOrder   int    `json:"sort_order"`
	Status      int8   `json:"status"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name        *string `json:"name"`
	Code        string  `json:"code"`
	Description *string `json:"description"`
	ParentID    *string `json:"parent_id"`
	URI         *string `json:"uri"`
	Image       *string `json:"image"`
	Icon        *string `json:"icon"`
	SortOrder   *int    `json:"sort_order"`
	Status      *int8   `json:"status"`
}

type UpdateCategoryRequestStatus struct {
	ID     string `json:"id" validate:"required"`
	Status int8   `json:"status" validate:"in:-2,-1,0,1,2"`
}
