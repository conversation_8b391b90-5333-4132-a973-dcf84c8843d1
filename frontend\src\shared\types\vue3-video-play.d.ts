declare module 'vue3-video-play' {
  import { DefineComponent } from 'vue'
  
  interface VideoOptions {
    width?: string | number
    height?: string | number
    color?: string
    title?: string
    language?: string
    playbackRates?: number[]
    poster?: string
    fluid?: boolean
    responsive?: boolean
    aspectRatio?: string
  }
  
  interface VideoPlayerProps {
    src: string
    poster?: string
    autoPlay?: boolean
    loop?: boolean
    muted?: boolean
    controls?: boolean
    playsinline?: boolean
    'webkit-playsinline'?: boolean
    'x5-playsinline'?: boolean
  }
  
  const vue3VideoPlay: DefineComponent<VideoPlayerProps>
  export default vue3VideoPlay
} 