<template>
    <div class="album-container">
        <el-card style="width:100%">
            <template #header>
                <div class="flex justify-between items-center">
                    <div class="card-header">图片专辑</div>
                    <!-- 操作按钮 -->
                    <div class="button-group">
                        <el-button type="primary" @click="handleAdd">
                            <el-icon>
                                <Plus />
                            </el-icon> 新增专辑
                        </el-button>
                        <el-button type="success" @click="refreshList">
                            <el-icon>
                                <Refresh />
                            </el-icon> 刷新
                        </el-button>
                    </div>
                </div>
            </template>

            <!-- 搜索栏组件 -->
            <AlbumSearchBar v-model="queryForm" :category-options="categoryOptions" @search="handleSearch"
                @reset="handleReset" />

            <!-- 专辑表格组件 -->
            <AlbumTable :album-list="albumList" :loading="loading" :pagination="pagination" @view="handleView"
                @edit="handleEdit" @delete="handleDelete" @change-status="handleChangeStatus"
                @selection-change="handleSelectionChange" @current-change="handleCurrentChange"
                @size-change="handleSizeChange" @batch-status="handleBatchStatus" @batch-delete="handleBatchDelete" />

            <!-- 添加/编辑对话框组件 -->
            <AlbumFormDialog v-model:visible="formDialogVisible" :title="dialogTitle" :initial-data="currentAlbum"
                :category-options="categoryOptions" @submit="handleFormSubmit" />

            <!-- 详情对话框组件 -->
            <AlbumDetailDialog v-model:visible="detailDialogVisible" :album="currentAlbum" @edit="handleEdit" />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import {
    batchDeletePictureAlbum,
    batchUpdatePictureAlbumStatus,
    createPictureAlbum,
    deletePictureAlbum,
    getAllPictureCategories,
    getPictureAlbumDetail,
    getPictureAlbumList,
    updatePictureAlbum,
    updatePictureAlbumStatus
} from '@/service/api/pictures/pictures';
import type { PictureAlbum, PictureCategory } from '@/types/pictures';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入组件
import AlbumDetailDialog from './components/AlbumDetailDialog.vue';
import AlbumFormDialog from './components/AlbumFormDialog.vue';
import AlbumSearchBar, { AlbumSearchForm } from './components/AlbumSearchBar.vue';
import AlbumTable from './components/AlbumTable.vue';

// 查询表单数据
const queryForm = reactive<AlbumSearchForm>({
    title: '',
    status: undefined,
    category_id: undefined,
    start_date: '',
    end_date: ''
});

// 分页和查询参数
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
});

// 查询参数
const queryParams = reactive({
    page: {
        pageNo: 1,
        pageSize: 10
    },
    data: {
        title: '',
        category_id: '',
        status: undefined as number | undefined,
        created_at_start: '',
        created_at_end: ''
    }
});

// 对话框标题
const dialogTitle = ref('添加专辑');
// 表单对话框可见性
const formDialogVisible = ref(false);
// 详情对话框可见性
const detailDialogVisible = ref(false);
// 当前操作的专辑
const currentAlbum = ref<PictureAlbum>({
    id: '',
    title: '',
    cover_url: '',
    description: '',
    category_id: '',
    category_name: '',
    sort_order: 0,
    status: 1,
    creator_id: '',
    creator_name: '',
    creator_avatar: '',
    picture_count: 0,
    view_count: 0,
    like_count: 0,
    comment_count: 0,
    share_count: 0,
    created_at: '',
    updated_at: ''
} as PictureAlbum);
// 专辑列表数据
const albumList = ref<PictureAlbum[]>([]);
// 分类选项
const categoryOptions = ref<PictureCategory[]>([]);
// 加载状态
const loading = ref(false);
// 选中的专辑列表
const selectedAlbums = ref<PictureAlbum[]>([]);

// 获取分类列表
const fetchCategories = async () => {
    try {
        const { data, err, response } = await getAllPictureCategories() as any;

        if (!err) {
            if (Array.isArray(data)) {
                categoryOptions.value = data;
            } else if (data && Array.isArray(data.list)) {
                categoryOptions.value = data.list;
            } else {
                categoryOptions.value = [];
                console.warn('分类数据格式不正确:', data);
            }
        } else {
            ElMessage.error(response?.data?.message || '获取分类列表失败');
            categoryOptions.value = [];
        }
    } catch (error) {
        console.error('获取分类列表失败', error);
        ElMessage.error('获取分类列表失败');
        categoryOptions.value = [];
    }
};

// 获取专辑列表
const fetchAlbumList = async () => {
    loading.value = true;
    try {
        const { data, err, response } = await getPictureAlbumList(queryParams) as any;

        if (!err) {
            albumList.value = data.list || [];
            pagination.total = data.total || 0;
        } else {
            ElMessage.error(response?.data?.message || '获取专辑列表失败');
        }
    } catch (error) {
        console.error('获取专辑列表失败', error);
        ElMessage.error('获取专辑列表失败');
    } finally {
        loading.value = false;
    }
};

// 搜索处理
const handleSearch = (params: AlbumSearchForm) => {
    queryParams.page.pageNo = 1;
    queryParams.data.title = params.title;
    queryParams.data.category_id = params.category_id || '';
    queryParams.data.status = params.status;
    queryParams.data.created_at_start = params.start_date;
    queryParams.data.created_at_end = params.end_date;
    fetchAlbumList();
};

// 重置查询
const handleReset = () => {
    queryParams.page.pageNo = 1;
    queryParams.data.title = '';
    queryParams.data.category_id = '';
    queryParams.data.status = undefined;
    queryParams.data.created_at_start = '';
    queryParams.data.created_at_end = '';
    fetchAlbumList();
};

// 刷新列表
const refreshList = () => {
    fetchAlbumList();
};

// 页码改变
const handleCurrentChange = (page: number) => {
    queryParams.page.pageNo = page;
    pagination.page = page;
    fetchAlbumList();
};

// 每页大小改变
const handleSizeChange = (size: number) => {
    queryParams.page.pageSize = size;
    pagination.pageSize = size;
    queryParams.page.pageNo = 1;
    pagination.page = 1;
    fetchAlbumList();
};

// 新增按钮操作
const handleAdd = () => {
    dialogTitle.value = '添加专辑';
    currentAlbum.value = {
        id: '',
        title: '',
        cover_url: '',
        description: '',
        category_id: '',
        category_name: '',
        sort_order: 0,
        status: 1,
        creator_id: '',
        creator_name: '',
        creator_avatar: '',
        picture_count: 0,
        view_count: 0,
        like_count: 0,
        comment_count: 0,
        share_count: 0,
        created_at: '',
        updated_at: ''
    } as PictureAlbum;
    formDialogVisible.value = true;
};

// 编辑按钮操作
const handleEdit = async (album: PictureAlbum) => {
    loading.value = true;
    try {
        const { data, err, response } = await getPictureAlbumDetail(album.id) as any;

        if (!err) {
            dialogTitle.value = '编辑专辑';
            currentAlbum.value = data;
            formDialogVisible.value = true;
        } else {
            ElMessage.error(response?.data?.message || '获取专辑详情失败');
        }
    } catch (error) {
        console.error('获取专辑详情失败', error);
        ElMessage.error('获取专辑详情失败');
    } finally {
        loading.value = false;
    }
};

// 查看详情
const handleView = async (album: PictureAlbum) => {
    loading.value = true;
    try {
        const { data, err, response } = await getPictureAlbumDetail(album.id) as any;

        if (!err) {
            currentAlbum.value = data;
            detailDialogVisible.value = true;
        } else {
            ElMessage.error(response?.data?.message || '获取专辑详情失败');
        }
    } catch (error) {
        console.error('获取专辑详情失败', error);
        ElMessage.error('获取专辑详情失败');
    } finally {
        loading.value = false;
    }
};

// 删除专辑
const handleDelete = (album: PictureAlbum) => {
    ElMessageBox.confirm(`确定要删除专辑 ${album.title} 吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true;
        try {
            const { err, response } = await deletePictureAlbum(album.id) as any;

            if (!err) {
                ElMessage.success('删除专辑成功');
                fetchAlbumList();
            } else {
                ElMessage.error(response?.data?.message || '删除专辑失败');
            }
        } catch (error) {
            console.error('删除专辑失败', error);
            ElMessage.error('删除专辑失败');
        } finally {
            loading.value = false;
        }
    }).catch(() => {
        // 取消操作
    });
};

// 更新状态
const handleChangeStatus = async (id: string, status: number) => {
    loading.value = true;
    try {
        const { err, response } = await updatePictureAlbumStatus({ id, status }) as any;

        if (!err) {
            ElMessage.success(`${status === 1 ? '启用' : '禁用'}专辑成功`);
            fetchAlbumList();
        } else {
            ElMessage.error(response?.data?.message || `${status === 1 ? '启用' : '禁用'}专辑失败`);
        }
    } catch (error) {
        console.error(`${status === 1 ? '启用' : '禁用'}专辑失败`, error);
        ElMessage.error(`${status === 1 ? '启用' : '禁用'}专辑失败`);
    } finally {
        loading.value = false;
    }
};

// 表单提交
const handleFormSubmit = async (formData: any) => {
    loading.value = true;
    try {
        if (formData.id) {
            // 编辑
            const { err, response } = await updatePictureAlbum({ data: formData }) as any;

            if (!err) {
                ElMessage.success('更新专辑成功');
                fetchAlbumList();
            } else {
                ElMessage.error(response?.data?.message || '更新专辑失败');
            }
        } else {
            // 新增
            const { err, response } = await createPictureAlbum({ data: formData }) as any;

            if (!err) {
                ElMessage.success('创建专辑成功');
                fetchAlbumList();
            } else {
                ElMessage.error(response?.data?.message || '创建专辑失败');
            }
        }
    } catch (error) {
        console.error('保存专辑失败', error);
        ElMessage.error('保存专辑失败');
    } finally {
        loading.value = false;
    }
};

// 处理选择变更
const handleSelectionChange = (selection: PictureAlbum[]) => {
    selectedAlbums.value = selection;
};

// 批量更新状态
const handleBatchStatus = async (status: number, albums: PictureAlbum[]) => {
    if (albums.length === 0) {
        ElMessage.warning('请先选择专辑');
        return;
    }

    const ids = albums.map(item => item.id);
    const statusText = status === 1 ? '启用' : '禁用';

    ElMessageBox.confirm(`确定要批量${statusText}选中的 ${ids.length} 个专辑吗？`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true;
        try {
            const { err, response } = await batchUpdatePictureAlbumStatus({ ids, status }) as any;

            if (!err) {
                ElMessage.success(`批量${statusText}专辑成功`);
                fetchAlbumList();
            } else {
                ElMessage.error(response?.data?.message || `批量${statusText}专辑失败`);
            }
        } catch (error) {
            console.error(`批量${statusText}专辑失败`, error);
            ElMessage.error(`批量${statusText}专辑失败`);
        } finally {
            loading.value = false;
        }
    }).catch(() => {
        // 取消操作
    });
};

// 批量删除
const handleBatchDelete = async (albums: PictureAlbum[]) => {
    if (albums.length === 0) {
        ElMessage.warning('请先选择专辑');
        return;
    }

    const ids = albums.map(item => item.id);

    ElMessageBox.confirm(`确定要批量删除选中的 ${ids.length} 个专辑吗？此操作不可恢复！`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true;
        try {
            const { err, response } = await batchDeletePictureAlbum({ ids }) as any;

            if (!err) {
                ElMessage.success('批量删除专辑成功');
                fetchAlbumList();
            } else {
                ElMessage.error(response?.data?.message || '批量删除专辑失败');
            }
        } catch (error) {
            console.error('批量删除专辑失败', error);
            ElMessage.error('批量删除专辑失败');
        } finally {
            loading.value = false;
        }
    }).catch(() => {
        // 取消操作
    });
};

// 页面加载时获取数据
onMounted(() => {
    fetchCategories();
    fetchAlbumList();
});
</script>

<style scoped lang="scss">
.album-container {
    padding: 16px;

    .card-header {
        font-size: 18px;
        font-weight: 600;
    }

    .button-group {
        display: flex;
        gap: 12px;
    }
}
</style>
