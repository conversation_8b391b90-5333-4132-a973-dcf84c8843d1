/**
 * Aura主题预设
 */
import { definePreset } from '@primeuix/themes';
import Aura from '@primeuix/themes/aura';

// 定义Aura预设
export const AuraPreset = definePreset(Aura, {
    name: 'aura',
    options: {
        darkMode: true,
        cssLayer: false
    },
    components: {
        card: {
            colorScheme: {
                light: {
                    root: {
                        background: '{surface.0}',
                        color: '{text.color}'
                    },
                    subtitle: {
                        color: '{text.color}'
                    }
                },
                dark: {
                    root: {
                        background: '{surface.500}',
                        color: '{text.color}'
                    },
                    subtitle: {
                        color: '{text.color}'
                    }
                }
            }
        },
        checkbox: {
            colorScheme: {
                light: {
                    root: {
                        background: '{surface.0}',
                        borderColor: '{surface.100}',
                    },
                },
                dark: {
                    root: {
                        background: '{surface.500}',
                        borderColor: '{surface.100}',
                    }
                }
            }
        },

        inputtext: {
            colorScheme: {
                light: {
                    root: {
                        background: '{surface.0}',
                    }
                },
                dark: {
                    root: {
                        background: '{surface.500}',
                    }
                }
            }
        },
    },

    semantic: {
        colorScheme: {
            light: {
                primary: {
                    color: '{primary.500}',
                    inverseColor: '#ffffff',
                    hoverColor: '{primary.600}',
                    activeColor: '{primary.700}'
                },
                highlight: {
                    background: '{primary.500}',
                    focusBackground: '{primary.700}',
                    color: '#ffffff',
                    focusColor: '#ffffff'
                },

                surface: {
                    0: '#ffffff',
                    50: '{surface.50}',
                    100: '{surface.100}',
                    200: '{surface.200}',
                    300: '{surface.300}',
                    400: '{surface.400}',
                    500: '{surface.500}',
                    600: '{surface.600}',
                    700: '{surface.700}',
                    800: '{surface.800}',
                    900: '{surface.900}',
                    950: '{surface.950}'
                },
                formField: {
                    hoverBorderColor: '{primary.color}'
                }
            },
            dark: {
                primary: {
                    color: '#FFFFFF',
                    inverseColor: '#1e1e1e',
                    hoverColor: '{primary.300}',
                    activeColor: '{primary.200}'
                },
                highlight: {
                    background: 'rgba(168, 85, 247, .16)',
                    focusBackground: 'rgba(168, 85, 247, .24)',
                    color: 'rgba(255,255,255,.87)',
                    focusColor: 'rgba(255,255,255,.87)'
                },
                surface: {
                    0: '{surface.0}',  // 更浅的背景色，原来是 surface.950
                    50: '{surface.50}', // 更浅的背景色
                    100: '{surface.100}', // 更浅的背景色
                    200: '{surface.200}', // 更浅的背景色
                    300: '{surface.300}', // 中等亮度
                    400: '{surface.400}', // 更亮的前景色
                    500: '{surface.500}', // 更亮的前景色
                    600: '{surface.600}', // 更亮的前景色
                    700: '{surface.700}', // 最亮的前景色
                    800: '{surface.800}', // 几乎白色
                    900: '{surface.900}', // 白色
                    950: '{surface.950}'  // 白色
                },
                formField: {
                    hoverBorderColor: '{primary.color}'
                }
            }
        },
        primary: {
            50: '{primary.50}',
            100: '{primary.100}',
            200: '{primary.200}',
            300: '{primary.300}',
            400: '{primary.400}',
            500: '{primary.500}',
            600: '{primary.600}',
            700: '{primary.700}',
            800: '{primary.800}',
            900: '{primary.900}',
            950: '{primary.950}'
        }
    }
});