/**
 * Aura主题预设
 */
import { definePreset } from '@primeuix/themes';
import Aura from '@primeuix/themes/aura';

// 定义Aura预设
export const AuraPreset = definePreset(Aura, {
    name: 'aura',
    options: {
        darkMode: true,
        cssLayer: false
    },
    // global: {
    //     css: `
    //   :root {
    //     --font-family: 'Inter var', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
    //   }

    //   * {
    //     box-sizing: border-box;
    //   }

    //   body {
    //     font-family: var(--font-family);
    //     -webkit-font-smoothing: antialiased;
    //     -moz-osx-font-smoothing: grayscale;
    //   }

    //   /* 确保PrimeVue组件样式正确应用 */
    //   .p-component {
    //     font-family: var(--font-family);
    //   }

    //   /* 确保暗色模式正确应用 */
    //   .app-dark {
    //     color-scheme: dark;
    //   }
    // `
    // },
    semantic: {
        colorScheme: {
            light: {
                primary: {
                    color: '{primary.500}',
                    inverseColor: '#ffffff',
                    hoverColor: '{primary.600}',
                    activeColor: '{primary.700}'
                },
                highlight: {
                    background: '{primary.500}',
                    focusBackground: '{primary.700}',
                    color: '#ffffff',
                    focusColor: '#ffffff'
                },
                text: {
                    color: '#333333',
                    secondaryColor: '#666666',
                    disabledColor: '#999999',
                    linkColor: '#000000',
                    contrastColor: '#ffffff',
                    contrastSecondaryColor: '#ffffff',
                    contrastDisabledColor: '#ffffff',
                },
                surface: {
                    0: '#ffffff',
                    50: '{surface.50}',
                    100: '{surface.100}',
                    200: '{surface.200}',
                    300: '{surface.300}',
                    400: '{surface.400}',
                    500: '{surface.500}',
                    600: '{surface.600}',
                    700: '{surface.700}',
                    800: '{surface.800}',
                    900: '{surface.900}',
                    950: '{surface.950}'
                },
                formField: {
                    hoverBorderColor: '{primary.color}'
                }
            },
            dark: {
                primary: {
                    color: '#FFFFFF',
                    inverseColor: '#1e1e1e',
                    hoverColor: '{primary.300}',
                    activeColor: '{primary.200}'
                },
                highlight: {
                    background: 'rgba(168, 85, 247, .16)',
                    focusBackground: 'rgba(168, 85, 247, .24)',
                    color: 'rgba(255,255,255,.87)',
                    focusColor: 'rgba(255,255,255,.87)'
                },
                text: {
                    color: '#ffffff',
                    secondaryColor: '#ffffff',
                    disabledColor: '#ffffff',
                    linkColor: '#ffffff',
                    contrastColor: '#ffffff',
                    contrastSecondaryColor: '#ffffff',
                    contrastDisabledColor: '#ffffff',
                },
                surface: {
                    0: '{surface.0}',  // 更浅的背景色，原来是 surface.950
                    50: '{surface.50}', // 更浅的背景色
                    100: '{surface.100}', // 更浅的背景色
                    200: '{surface.200}', // 更浅的背景色
                    300: '{surface.300}', // 中等亮度
                    400: '{surface.400}', // 更亮的前景色
                    500: '{surface.500}', // 更亮的前景色
                    600: '{surface.600}', // 更亮的前景色
                    700: '{surface.700}', // 最亮的前景色
                    800: '{surface.800}', // 几乎白色
                    900: '{surface.900}', // 白色
                    950: '{surface.950}'  // 白色
                },
                formField: {
                    hoverBorderColor: '{primary.color}'
                }
            }
        },
        primary: {
            50: '{primary.50}',
            100: '{primary.100}',
            200: '{primary.200}',
            300: '{primary.300}',
            400: '{primary.400}',
            500: '{primary.500}',
            600: '{primary.600}',
            700: '{primary.700}',
            800: '{primary.800}',
            900: '{primary.900}',
            950: '{primary.950}'
        },
        slate: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
            950: '#020617'
        },
        purple: {
            50: "#faf5ff",
            100: "#f3e8ff",
            200: "#e9d5ff",
            300: "#d8b4fe",
            400: "#c084fc",
            500: "#a855f7",
            600: "#9333ea",
            700: "#7e22ce",
            800: "#6b21a8",
            900: "#581c87",
            950: "#3b0764"
        }
    }
});