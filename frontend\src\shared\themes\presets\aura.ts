/**
 * Aura主题预设
 */
import { THEME_COLORS } from '@/config/theme.config';

// PrimeVue Pass-through (PT) 属性
const auraPreset = {
  // 全局样式
  global: {
    css: `
      :root {
        /* 自定义变量 */
        --header-bg: ${THEME_COLORS.indigo.header};
        --header-text: ${THEME_COLORS.indigo.headerText};
        --footer-bg: ${THEME_COLORS.indigo.footer};
        --footer-text: ${THEME_COLORS.indigo.footerText};
        
        /* PrimeVue Panel组件变量 */
        --p-panel-header-bg: var(--surface-section);
        --p-panel-header-text: var(--surface-900);
        --p-panel-content-bg: var(--surface-overlay);
        --p-panel-content-text: var(--surface-700);
        --p-panel-border-color: var(--surface-border);
        --p-panel-border-radius: 0.5rem;
        --p-panel-background: var(--surface-card);
        --p-panel-color: var(--text-color);
      }
      
      .dark {
        --p-panel-header-bg: var(--surface-800);
        --p-panel-header-text: #ffffff;
        --p-panel-content-bg: var(--surface-800);
        --p-panel-content-text: rgba(255, 255, 255, 0.8);
        --p-panel-border-color: var(--surface-700);
      }
    `
  },
  
  // Panel组件样式
  panel: {
    header: {
      class: [
        // 基础样式
        'flex items-center justify-between',
        'border border-solid border-surface-border',
        'bg-surface-section text-surface-900',
        'rounded-tl-lg rounded-tr-lg',
        'py-3 px-5',
        
        // 状态样式
        'dark:bg-surface-800 dark:text-white dark:border-surface-700'
      ]
    },
    content: {
      class: [
        // 基础样式
        'p-5',
        'border border-solid border-surface-border',
        'bg-surface-overlay text-surface-700',
        'border-t-0',
        'rounded-bl-lg rounded-br-lg',
        
        // 状态样式
        'dark:bg-surface-800 dark:text-white/80 dark:border-surface-700'
      ]
    },
    footer: {
      class: [
        // 基础样式
        'p-5',
        'border border-solid border-surface-border',
        'bg-surface-overlay text-surface-700',
        'border-t-0',
        'rounded-bl-lg rounded-br-lg',
        
        // 状态样式
        'dark:bg-surface-800 dark:text-white/80 dark:border-surface-700'
      ]
    },
    transition: {
      enterFromClass: 'opacity-0',
      enterActiveClass: 'transition-opacity duration-300 ease-in',
      leaveActiveClass: 'transition-opacity duration-300 ease-in',
      leaveToClass: 'opacity-0'
    }
  },
  
  // Button组件样式
  button: {
    root: ({ props }) => ({
      class: [
        // 基础样式
        'relative',
        'items-center inline-flex text-center align-bottom justify-center',
        'rounded-md',
        'text-sm',
        'transition duration-200 ease-in-out',
        'cursor-pointer overflow-hidden select-none',
        
        // 尺寸
        {
          'px-2.5 py-1.5 min-w-[2rem]': props.size === 'small',
          'px-3 py-2 min-w-[2.5rem]': props.size === null || props.size === 'medium',
          'px-4 py-3 min-w-[3rem]': props.size === 'large'
        },
        
        // 图标按钮
        { 'p-0 w-8 h-8': props.label == null && props.icon !== null },
        
        // 主题样式
        {
          'text-white dark:text-surface-900 bg-primary-500 dark:bg-primary-400 border border-primary-500 dark:border-primary-400 hover:bg-primary-600 dark:hover:bg-primary-300 hover:border-primary-600 dark:hover:border-primary-300':
            !props.link && props.severity === null && !props.text && !props.outlined && !props.plain,
          'text-primary-500 hover:text-primary-600 hover:bg-primary-300/20': props.link
        },
        
        // 禁用状态
        {
          'opacity-60 pointer-events-none cursor-default': props.disabled
        }
      ]
    })
  },
  
  // 其他组件样式...
};

export default auraPreset; 