package container

import (
	contentRepo "frontapi/internal/repository/content_creator"
	contentService "frontapi/internal/service/content_creator"
)

// InitContentCreatorServices 初始化内容创作相关服务
func InitContentCreatorServices(b *ServiceBuilder) {
	// 内容创作相关仓库
	contentAuditRepoImpl := contentRepo.NewContentAuditRepository(b.DB())
	contentHeatRepo := contentRepo.NewContentHeatRepository(b.DB())
	contentRatingRepo := contentRepo.NewContentRatingRepository(b.DB())
	contentRevenueRepo := contentRepo.NewContentRevenueRepository(b.DB())

	// 内容创作相关服务
	container := b.Services()
	container.ContentAuditService = contentService.NewContentAuditService(contentAuditRepoImpl)
	container.ContentHeatService = contentService.NewContentHeatService(contentHeatRepo)
	container.ContentRatingService = contentService.NewContentRatingService(contentRatingRepo)
	container.ContentRevenueService = contentService.NewContentRevenueService(contentRevenueRepo)
}

// InitContentCreatorRevenueServices 初始化内容创作者收益规则相关服务
func InitContentCreatorRevenueServices(b *ServiceBuilder) {
	// 内容创作收益规则相关仓库
	revenueRuleRepo := contentRepo.NewRevenueRuleRepository(b.DB())

	// 内容创作收益规则相关服务
	container := b.Services()
	container.RevenueRuleService = contentService.NewRevenueRuleService(revenueRuleRepo)
}
