<template>
  <div class="comics-table">
    <el-table
      v-loading="loading"
      :data="comicsList"
      border
      style="width: 100%"
      row-key="id"
    >
      <el-table-column type="index" width="55" align="center" />

      <el-table-column prop="cover" label="封面" width="100" align="center">
        <template #default="{ row }">
          <el-image
            v-if="row.cover"
            :src="row.cover"
            :preview-src-list="[row.cover]"
            fit="cover"
            class="cover-image"
          />
          <el-icon v-else><Picture /></el-icon>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="标题" min-width="180">
        <template #default="{ row }">
          <div class="title-container">
            <div class="title-text">{{ row.title }}</div>
            <div class="title-badges">
              <el-tag size="small" v-if="row.is_featured === 1" type="success">推荐</el-tag>
              <el-tag size="small" v-if="row.is_paid === 0" type="danger">付费</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="category_name" label="分类" width="120" align="center">
        <template #default="{ row }">
          <el-tag>{{ row.category_name || '-' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="author" label="作者" width="120" align="center" />

      <el-table-column prop="price" label="价格" width="80" align="center">
        <template #default="{ row }">
          {{ row.is_paid === 0 ? '免费' : `¥${row.price}` }}
        </template>
      </el-table-column>

      <el-table-column prop="chapter_count" label="章节数" width="80" align="center" />

      <el-table-column prop="read_count" label="浏览量" width="100" align="center">
        <template #default="{ row }">
          {{ formatNumber(row.read_count) }}
        </template>
      </el-table-column>

      <el-table-column prop="progress" label="进度" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.progress === 'ongoing' ? 'success' : 'danger'">
            {{ row.progress === 'ongoing' ? '连载中' : '已完结' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="170" align="center">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="280" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="success" link @click="handleManageChapter(row)">管理章节</el-button>
          <el-button type="info" link @click="handleDetail(row)">详情</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="comicsList.length === 0 && !loading" class="empty-data">
      暂无数据
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import { ComicsItem } from '@/types/comics';

defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  comicsList: {
    type: Array as () => ComicsItem[],
    default: () => []
  }
});

const emit = defineEmits(['edit', 'detail', 'delete', 'chapter']);

// 处理编辑
const handleEdit = (row: ComicsItem) => {
  emit('edit', row);
};

// 处理查看详情
const handleDetail = (row: ComicsItem) => {
  emit('detail', row);
};

// 处理删除
const handleDelete = (row: ComicsItem) => {
  emit('delete', row);
};

// 处理管理章节
const handleManageChapter = (row: ComicsItem) => {
  emit('chapter', row);
};

// 格式化数字
const formatNumber = (num: number) => {
  if (!num && num !== 0) return '-';
  return num >= 10000 ? (num / 10000).toFixed(1) + '万' : num;
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 8);
};
</script>

<style scoped>
.comics-table {
  margin-bottom: 20px;
}

.cover-image {
  width: 60px;
  height: 80px;
  border-radius: 4px;
  object-fit: cover;
}

.title-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: left;
  padding:15px;
}

.title-text {
  font-weight: 500;
}

.title-badges {
  display: flex;
  gap: 5px;
}

.empty-data {
  text-align: center;
  padding: 30px 0;
  color: #909399;
  font-size: 14px;
}
</style>
