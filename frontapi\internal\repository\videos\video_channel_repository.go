package videos

import (
	"context"

	"gorm.io/gorm"

	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"
)

// VideoChannelRepository 视频频道数据访问接口
type VideoChannelRepository interface {
	base.ExtendedRepository[videos.VideoChannel]
	FindByName(ctx context.Context, name string) (*videos.VideoChannel, error)
}

// videoChannelRepository 视频频道数据访问实现
type videoChannelRepository struct {
	base.ExtendedRepository[videos.VideoChannel]
}

// NewVideoChannelRepository 创建视频频道仓库实例
func NewVideoChannelRepository(db *gorm.DB) VideoChannelRepository {
	return &videoChannelRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoChannel](db),
	}

}

// FindByName 根据名称查找视频频道
func (r *videoChannelRepository) FindByName(ctx context.Context, name string) (*videos.VideoChannel, error) {
	return r.FindOneByCondition(ctx, map[string]interface{}{"name": name}, "")
}
