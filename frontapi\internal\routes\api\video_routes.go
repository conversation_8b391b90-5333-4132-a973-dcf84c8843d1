package api

import (
	apiVideos "frontapi/internal/api/videos"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterVideoRoutes 注册视频相关路由
func RegisterVideoRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	//videoController *videos.VideoController, videoChannelController *videos.VideoChannelController
	// 初始化视频服务和控制器
	videoService := services.VideoService
	videoCategoryService := services.VideoCategoryService
	videoCommentService := services.VideoCommentService
	videoChannelService := services.VideoChannelService
	videoAlbumService := services.VideoAlbumService
	// 注册视频相关路由组
	videoApi := apiGroup.Group("/videos")
	{
		videoController := apiVideos.NewVideoController(
			videoService,
			videoCategoryService,
			videoCommentService,
		)
		// 公开接口
		videoApi.Post("/getVideoList", videoController.GetVideoList)
	}

	videoCategoryController := apiVideos.NewVideoCategoryController(videoService, videoCategoryService)
	// 视频分类相关路由组
	categories := apiGroup.Group("/video/categories")
	{
		// 公开接口
		categories.Post("/getCategoryList", videoCategoryController.GetVideoCategoryList)
	}

	// 视频频道相关路由组
	channels := apiGroup.Group("/video/channels")
	{
		videoChannelController := apiVideos.NewVideoChannelController(videoChannelService)
		// 公开接口
		channels.Post("/getChannelList", videoChannelController.GetVideoChannelList)
	}
	//视频专辑
	album := apiGroup.Group("/video/albums")
	{
		videoAlbumController := apiVideos.NewVideoAlbumController(videoAlbumService, videoService)
		// 公开接口
		album.Post("/getAlbumList", videoAlbumController.GetAlbumList)
		album.Post("/getAlbumDetail", videoAlbumController.GetAlbumDetail)
		album.Post("/getAlbumVideoList", videoAlbumController.GetAlbumVideoList)
	}

	// 视频评论相关路由组
	comments := apiGroup.Group("/video/comments")
	{
		videoCommentController := apiVideos.NewVideoCommentController(videoCommentService)
		// 公开接口
		comments.Post("/getCommentList", videoCommentController.GetVideoCommentList)
	}
}
