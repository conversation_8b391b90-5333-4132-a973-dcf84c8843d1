package posts

import (
	"context"

	"gorm.io/gorm"

	"frontapi/internal/models/posts"
	"frontapi/internal/repository/base"
)

// PostCommentLikeRepository 帖子评论点赞数据访问接口
type PostCommentLikeRepository interface {
	base.ExtendedRepository[posts.PostCommentLike]
	FindByUserAndComment(ctx context.Context, userID, commentID string) (*posts.PostCommentLike, error)
	ListByUserID(ctx context.Context, userID string, page, pageSize int) ([]*posts.PostCommentLike, int64, error)
	ListByCommentID(ctx context.Context, commentID string, page, pageSize int) ([]*posts.PostCommentLike, int64, error)
}

// PostCommentLikeRepository 帖子评论点赞数据访问实现
type postCommentLikeRepository struct {
	base.ExtendedRepository[posts.PostCommentLike]
}

// NewPostCommentLikeRepository 创建帖子评论点赞仓库实例
func NewPostCommentLikeRepository(db *gorm.DB) PostCommentLikeRepository {
	return &postCommentLikeRepository{
		ExtendedRepository: base.NewExtendedRepository[posts.PostCommentLike](db),
	}
}

// FindByUserAndComment 根据用户ID和评论ID查找点赞记录
func (r *postCommentLikeRepository) FindByUserAndComment(ctx context.Context, userID, commentID string) (*posts.PostCommentLike, error) {
	conditions := map[string]interface{}{
		"user_id":    userID,
		"comment_id": commentID,
	}
	return r.FindOneByCondition(ctx, conditions, "")
}

// ListByUserID 获取用户的评论点赞列表
func (r *postCommentLikeRepository) ListByUserID(ctx context.Context, userID string, page, pageSize int) ([]*posts.PostCommentLike, int64, error) {
	orderBy := "created_at DESC"
	return r.ListWithConditionAndPagination(ctx, orderBy, page, pageSize, "user_id = ?", userID)
}

// ListByCommentID 获取评论的点赞列表
func (r *postCommentLikeRepository) ListByCommentID(ctx context.Context, commentID string, page, pageSize int) ([]*posts.PostCommentLike, int64, error) {
	orderBy := "created_at DESC"
	return r.ListWithConditionAndPagination(ctx, orderBy, page, pageSize, "comment_id = ?", commentID)
}
