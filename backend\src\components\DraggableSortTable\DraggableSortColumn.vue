<template>
  <el-table-column 
    :prop="prop" 
    :label="label" 
    :width="width"
    :min-width="minWidth"
    :align="align"
    :class="`draggable-column-${prop}`"
  >
    <template #default="scope">
      <div class="draggable-cell">
        <slot :row="scope.row" :$index="scope.$index">
          {{ scope.row[prop] }}
        </slot>
      </div>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import { onMounted, inject } from 'vue';

const props = defineProps({
  prop: {
    type: String,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  width: {
    type: [String, Number],
    default: ''
  },
  minWidth: {
    type: [String, Number],
    default: ''
  },
  align: {
    type: String,
    default: 'left'
  }
});

// Register this column as draggable if registration function is provided
onMounted(() => {
  const registerDraggableColumn = inject<((column: any) => void) | null>('registerDraggableColumn', null);
  if (registerDraggableColumn) {
    registerDraggableColumn({
      prop: props.prop,
      label: props.label
    });
  }
});
</script>

<style scoped>
.draggable-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* For when a custom drag handle is provided */
:deep(.chapter-drag-handle) {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
}

:deep(.drag-icon) {
  margin-right: 5px;
  cursor: grab;
}
</style> 