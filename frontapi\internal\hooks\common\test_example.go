package common

import (
	"context"
	"fmt"
)

// TestValidationHook 测试验证钩子的基本功能
func TestValidationHook() error {
	// 创建验证钩子
	validationHook := NewValidationHook()
	
	// 添加验证规则
	validationHook.AddRequiredRule("name")
	validationHook.AddMinLengthRule("name", 2)
	validationHook.AddMaxLengthRule("name", 50)
	
	// 测试数据
	testData := map[string]interface{}{
		"name": "测试用户",
		"email": "<EMAIL>",
	}
	
	// 执行验证
	err := validationHook.Execute(context.Background(), testData)
	if err != nil {
		return fmt.Errorf("验证失败: %v", err)
	}
	
	fmt.Println("验证钩子测试通过")
	return nil
}

// TestTimestampHook 测试时间戳钩子的基本功能
func TestTimestampHook() error {
	// 创建时间戳钩子
	timestampHook := &TimestampHook{}
	
	// 测试数据
	testData := map[string]interface{}{
		"name": "测试数据",
	}
	
	// 执行时间戳设置
	err := timestampHook.Execute(context.Background(), testData)
	if err != nil {
		return fmt.Errorf("时间戳设置失败: %v", err)
	}
	
	// 检查是否添加了时间戳字段
	if _, exists := testData["created_at"]; !exists {
		return fmt.Errorf("未找到 created_at 字段")
	}
	
	if _, exists := testData["updated_at"]; !exists {
		return fmt.Errorf("未找到 updated_at 字段")
	}
	
	fmt.Println("时间戳钩子测试通过")
	return nil
}

// RunAllTests 运行所有测试
func RunAllTests() {
	fmt.Println("开始测试钩子系统...")
	
	if err := TestValidationHook(); err != nil {
		fmt.Printf("验证钩子测试失败: %v\n", err)
	}
	
	if err := TestTimestampHook(); err != nil {
		fmt.Printf("时间戳钩子测试失败: %v\n", err)
	}
	
	fmt.Println("钩子系统测试完成")
}