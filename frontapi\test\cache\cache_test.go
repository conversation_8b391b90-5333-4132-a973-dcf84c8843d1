package cache_test

import (
	"context"
	"testing"
	"time"

	"frontapi/pkg/cache"
	"frontapi/pkg/cache/bigcache"
	"frontapi/pkg/cache/file"
	"frontapi/pkg/cache/redis"
	"frontapi/pkg/cache/types"
)

// 测试用户结构体
type User struct {
	ID       int    `json:"id"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	CreateAt int64  `json:"create_at"`
}

// 测试缓存基本功能
func testBasicCache(t *testing.T, adapter types.CacheAdapter) {
	t.Logf("测试 %s 适配器基本功能", adapter.Name())

	ctx := context.Background()

	// 测试设置和获取
	key := "test_key"
	value := []byte("Hello Cache")

	err := adapter.Set(ctx, key, value, time.Minute)
	if err != nil {
		t.Fatalf("设置缓存失败: %v", err)
	}
	t.Log("设置缓存成功")

	// 获取缓存
	data, err := adapter.Get(ctx, key)
	if err != nil {
		t.Fatalf("获取缓存失败: %v", err)
	}
	t.Logf("获取缓存成功: %s", string(data))

	// 测试删除
	err = adapter.Delete(ctx, key)
	if err != nil {
		t.Fatalf("删除缓存失败: %v", err)
	}
	t.Log("删除缓存成功")

	// 验证删除是否成功
	_, err = adapter.Get(ctx, key)
	if err == nil {
		t.Fatal("删除缓存验证失败，缓存仍然存在")
	}
	t.Log("删除缓存验证成功，缓存已不存在")
}

// 测试JSON序列化和反序列化
func testJSONCache(t *testing.T, adapter types.CacheAdapter) {
	t.Logf("测试 %s 适配器JSON功能", adapter.Name())

	// 创建测试数据
	user := User{
		ID:       1,
		Name:     "张三",
		Email:    "<EMAIL>",
		CreateAt: time.Now().Unix(),
	}

	// 设置JSON缓存
	err := cache.AdapterSetJSON(adapter, "user:1", user, time.Minute)
	if err != nil {
		t.Fatalf("设置JSON缓存失败: %v", err)
	}
	t.Log("设置JSON缓存成功")

	// 获取JSON缓存
	var retrievedUser User
	err = cache.AdapterGetJSON(adapter, "user:1", &retrievedUser)
	if err != nil {
		t.Fatalf("获取JSON缓存失败: %v", err)
	}

	t.Logf("获取JSON缓存成功: %+v", retrievedUser)

	// 验证数据一致性
	if user.ID != retrievedUser.ID || user.Name != retrievedUser.Name {
		t.Fatalf("JSON缓存数据不一致: 原始=%+v, 获取=%+v", user, retrievedUser)
	}
	t.Log("JSON缓存数据一致性验证通过")
}

// 测试过期功能
func testExpiration(t *testing.T, adapter types.CacheAdapter) {
	t.Logf("测试 %s 适配器过期功能", adapter.Name())

	ctx := context.Background()

	// 设置短期过期的缓存
	key := "expire_test"
	err := adapter.Set(ctx, key, []byte("短期过期"), 2*time.Second)
	if err != nil {
		t.Fatalf("设置过期缓存失败: %v", err)
	}
	t.Log("设置短期过期缓存成功")

	// 立即获取
	_, err = adapter.Get(ctx, key)
	if err != nil {
		t.Fatalf("获取短期缓存失败: %v", err)
	}
	t.Log("短期缓存立即获取成功")

	// 等待过期
	t.Log("等待3秒让缓存过期...")
	time.Sleep(3 * time.Second)

	// 再次获取，应该已过期
	_, err = adapter.Get(ctx, key)
	if err == nil {
		t.Fatal("过期测试失败，缓存未过期")
	}
	t.Log("过期测试成功，缓存已过期")
}

// 测试批量操作
func testBatchOperations(t *testing.T, adapter types.CacheAdapter) {
	t.Logf("测试 %s 适配器批量操作", adapter.Name())

	ctx := context.Background()

	// 批量设置
	items := map[string][]byte{
		"batch:1": []byte("值1"),
		"batch:2": []byte("值2"),
		"batch:3": []byte("值3"),
	}

	err := adapter.MSet(ctx, items, time.Minute)
	if err != nil {
		t.Fatalf("批量设置失败: %v", err)
	}
	t.Log("批量设置成功")

	// 批量获取
	keys := []string{"batch:1", "batch:2", "batch:3", "batch:nonexistent"}
	results, err := adapter.MGet(ctx, keys)
	if err != nil {
		t.Fatalf("批量获取失败: %v", err)
	}

	t.Log("批量获取结果:")
	for k, v := range results {
		t.Logf("  %s: %s", k, string(v))
	}

	// 验证结果数量
	if len(results) < 3 {
		t.Fatalf("批量获取结果不完整，应至少有3个结果，实际有%d个", len(results))
	}
	t.Log("批量操作测试通过")
}

// 测试计数器功能
func testCounters(t *testing.T, adapter types.CacheAdapter) {
	t.Logf("测试 %s 适配器计数器功能", adapter.Name())

	ctx := context.Background()
	counterKey := "counter:test"

	// 设置初始值
	_, err := adapter.Increment(ctx, counterKey, 10)
	if err != nil {
		t.Fatalf("设置计数器初始值失败: %v", err)
	}
	t.Log("设置计数器初始值成功")

	// 增加计数
	newVal, err := adapter.Increment(ctx, counterKey, 5)
	if err != nil {
		t.Fatalf("增加计数器失败: %v", err)
	}
	t.Logf("增加计数器成功，新值: %d", newVal)

	// 减少计数
	newVal, err = adapter.Decrement(ctx, counterKey, 3)
	if err != nil {
		t.Fatalf("减少计数器失败: %v", err)
	}
	t.Logf("减少计数器成功，新值: %d", newVal)

	// 验证最终值
	if newVal != 12 { // 10 + 5 - 3 = 12
		t.Fatalf("计数器值不正确，期望12，实际%d", newVal)
	}
	t.Log("计数器功能测试通过")
}

// 初始化Redis适配器
func setupRedisAdapter(t *testing.T) types.CacheAdapter {
	redisConfig := &redis.Config{
		Host:     "localhost",
		Port:     6379,
		Password: "",
		DB:       0,
	}

	adapter, err := redis.NewAdapter(redisConfig)
	if err != nil {
		t.Fatalf("初始化Redis适配器失败: %v", err)
	}

	return adapter
}

// 初始化文件适配器
func setupFileAdapter(t *testing.T) types.CacheAdapter {
	fileConfig := &file.Config{
		Path:            "./storage/cache",
		CleanupInterval: 10 * time.Minute,
		FileMode:        0644,
		DirMode:         0755,
	}

	adapter, err := file.NewAdapter(fileConfig)
	if err != nil {
		t.Fatalf("初始化文件适配器失败: %v", err)
	}

	return adapter
}

// 初始化BigCache适配器
func setupBigCacheAdapter(t *testing.T) types.CacheAdapter {
	bigcacheConfig := &bigcache.Config{
		Shards:             1024,
		LifeWindow:         10 * time.Minute,
		CleanWindow:        5 * time.Minute,
		MaxEntriesInWindow: 1000 * 10 * 60,
		MaxEntrySize:       500,
		HardMaxCacheSize:   8192,
	}

	adapter, err := bigcache.NewAdapter(bigcacheConfig)
	if err != nil {
		t.Fatalf("初始化BigCache适配器失败: %v", err)
	}

	return adapter
}

// 测试缓存管理器
func testCacheManager(t *testing.T) {
	t.Log("测试缓存管理器")

	// 创建缓存管理器
	manager, err := cache.NewManager(nil)
	if err != nil {
		t.Fatalf("创建缓存管理器失败: %v", err)
	}

	// 添加适配器
	redisAdapter := setupRedisAdapter(t)
	err = manager.AddAdapter("redis", redisAdapter)
	if err != nil {
		t.Fatalf("添加Redis适配器失败: %v", err)
	}

	fileAdapter := setupFileAdapter(t)
	err = manager.AddAdapter("file", fileAdapter)
	if err != nil {
		t.Fatalf("添加文件适配器失败: %v", err)
	}

	bigcacheAdapter := setupBigCacheAdapter(t)
	err = manager.AddAdapter("bigcache", bigcacheAdapter)
	if err != nil {
		t.Fatalf("添加BigCache适配器失败: %v", err)
	}

	// 设置默认适配器
	err = manager.SetDefaultAdapter("redis")
	if err != nil {
		t.Fatalf("设置默认适配器失败: %v", err)
	}
	t.Log("设置默认适配器成功")

	// 获取默认适配器
	defaultAdapter, err := manager.GetDefaultAdapter()
	if err != nil {
		t.Fatalf("获取默认适配器失败: %v", err)
	}

	// 使用默认适配器
	err = defaultAdapter.Set(context.Background(), "manager:test", []byte("通过管理器设置"), time.Minute)
	if err != nil {
		t.Fatalf("通过管理器设置缓存失败: %v", err)
	}

	// 获取缓存
	data, err := defaultAdapter.Get(context.Background(), "manager:test")
	if err != nil {
		t.Fatalf("通过管理器获取缓存失败: %v", err)
	}
	t.Logf("通过管理器获取缓存成功: %s", string(data))

	// 切换默认适配器
	err = manager.SetDefaultAdapter("bigcache")
	if err != nil {
		t.Fatalf("切换默认适配器失败: %v", err)
	}
	t.Log("切换默认适配器成功")

	// 获取新的默认适配器
	newDefaultAdapter, err := manager.GetDefaultAdapter()
	if err != nil {
		t.Fatalf("获取新默认适配器失败: %v", err)
	}

	// 使用新默认适配器
	err = newDefaultAdapter.Set(context.Background(), "manager:test2", []byte("通过新管理器设置"), time.Minute)
	if err != nil {
		t.Fatalf("通过新管理器设置缓存失败: %v", err)
	}

	// 获取缓存
	data, err = newDefaultAdapter.Get(context.Background(), "manager:test2")
	if err != nil {
		t.Fatalf("通过新管理器获取缓存失败: %v", err)
	}
	t.Logf("通过新管理器获取缓存成功: %s", string(data))

	t.Log("缓存管理器测试通过")
}

// 测试全局缓存管理器
func testGlobalCacheManager(t *testing.T) {
	t.Log("测试全局缓存管理器")

	// 重置全局管理器
	cache.ResetGlobalManager()

	// 创建新管理器
	manager, err := cache.NewManager(nil)
	if err != nil {
		t.Fatalf("创建缓存管理器失败: %v", err)
	}

	// 添加适配器
	redisAdapter := setupRedisAdapter(t)
	err = manager.AddAdapter("redis", redisAdapter)
	if err != nil {
		t.Fatalf("添加Redis适配器失败: %v", err)
	}

	// 设置默认适配器
	err = manager.SetDefaultAdapter("redis")
	if err != nil {
		t.Fatalf("设置默认适配器失败: %v", err)
	}

	// 设置为全局管理器
	cache.SetGlobalManager(manager)
	t.Log("设置全局缓存管理器成功")

	// 使用全局管理器设置缓存
	err = cache.ManagerSetJSON("global:test", User{ID: 100, Name: "全局测试"}, time.Minute)
	if err != nil {
		t.Fatalf("使用全局管理器设置缓存失败: %v", err)
	}

	// 获取缓存
	var user User
	err = cache.ManagerGetJSON("global:test", &user)
	if err != nil {
		t.Fatalf("使用全局管理器获取缓存失败: %v", err)
	}
	t.Logf("使用全局管理器获取缓存成功: %+v", user)

	t.Log("全局缓存管理器测试通过")
}

// TestRedisCache 测试Redis缓存适配器
func TestRedisCache(t *testing.T) {
	adapter := setupRedisAdapter(t)
	testBasicCache(t, adapter)
	testJSONCache(t, adapter)
	testExpiration(t, adapter)
	testBatchOperations(t, adapter)
	testCounters(t, adapter)
}

// TestFileCache 测试文件缓存适配器
func TestFileCache(t *testing.T) {
	adapter := setupFileAdapter(t)
	testBasicCache(t, adapter)
	testJSONCache(t, adapter)
	testExpiration(t, adapter)
	testBatchOperations(t, adapter)
	testCounters(t, adapter)
}

// TestBigCache 测试BigCache缓存适配器
func TestBigCache(t *testing.T) {
	adapter := setupBigCacheAdapter(t)
	testBasicCache(t, adapter)
	testJSONCache(t, adapter)
	testExpiration(t, adapter)
	testBatchOperations(t, adapter)
	testCounters(t, adapter)
}

// TestCacheManager 测试缓存管理器
func TestCacheManager(t *testing.T) {
	testCacheManager(t)
}

// TestGlobalCacheManager 测试全局缓存管理器
func TestGlobalCacheManager(t *testing.T) {
	testGlobalCacheManager(t)
}
