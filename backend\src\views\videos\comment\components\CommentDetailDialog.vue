<template>
  <el-dialog
    v-model="visible"
    title="评论详情"
    width="600px"
    @close="handleClose"
  >
    <div v-if="comment" class="comment-detail">
      <div class="detail-item">
        <span class="label">视频ID:</span>
        <span class="value">{{ comment.video_id }}</span>
      </div>
      <div class="detail-item">
        <span class="label">用户信息:</span>
        <div class="flex items-center">
          <el-avatar 
            :size="36" 
            :src="comment.user_avatar" 
            class="mr-2"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div>
            <div>{{ comment.user_nickname || comment.user_id }}</div>
            <div class="text-gray-500 text-sm">ID: {{ comment.user_id }}</div>
          </div>
        </div>
      </div>
      <div class="detail-item">
        <span class="label">评论内容:</span>
        <div class="value-box">{{ comment.content }}</div>
      </div>
      <div class="detail-item">
        <span class="label">评论时间:</span>
        <span class="value">{{ formatDate(comment.created_at) }}</span>
      </div>
      <div class="detail-item">
        <span class="label">点赞数:</span>
        <span class="value">{{ comment.like_count }}</span>
      </div>
      <div class="detail-item">
        <span class="label">回复数:</span>
        <span class="value">{{ comment.reply_count }}</span>
      </div>
      <div class="detail-item">
        <span class="label">状态:</span>
        <span class="value">
          <CommentStatusTag :status="comment.status" />
        </span>
      </div>
      <div class="detail-item">
        <span class="label">IP地址:</span>
        <span class="value">{{ comment.ip_address || '未记录' }}</span>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-dropdown v-if="comment" split-button type="primary" @command="handleStatusChange">
          修改状态
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="1" :disabled="comment?.status === 1">设为正常</el-dropdown-item>
              <el-dropdown-item :command="0" :disabled="comment?.status === 0">设为隐藏</el-dropdown-item>
              <el-dropdown-item :command="-1" :disabled="comment?.status === -1">审核不通过</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-popconfirm 
          title="确定删除此评论吗？此操作不可恢复！" 
          @confirm="handleDelete"
        >
          <template #reference>
            <el-button type="danger">删除</el-button>
          </template>
        </el-popconfirm>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { CommentItem } from '@/service/api/videos/comments';
import { formatDate } from '@/utils/date';
import { User } from '@element-plus/icons-vue';
import { computed } from 'vue';
import CommentStatusTag from './CommentStatusTag.vue';

// Props
interface Props {
  modelValue: boolean;
  comment: CommentItem | null;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'delete', comment: CommentItem): void;
  (e: 'statusChange', id: string, status: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性：对话框可见性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
});

// 关闭对话框
const handleClose = () => {
  visible.value = false;
};

// 删除评论
const handleDelete = () => {
  if (props.comment) {
    emit('delete', props.comment);
    handleClose();
  }
};

// 状态变更
const handleStatusChange = (status: number) => {
  if (props.comment) {
    emit('statusChange', props.comment.id, status);
  }
};
</script>

<style scoped lang="scss">
.comment-detail {
  padding: 16px;
}

.detail-item {
  margin-bottom: 16px;
  
  .label {
    font-weight: bold;
    margin-right: 10px;
    color: #606266;
    display: block;
    margin-bottom: 5px;
  }
  
  .value {
    color: #303133;
  }
}

.value-box {
  margin-top: 8px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #303133;
  min-height: 60px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
  border-left: 3px solid #409eff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.text-sm {
  font-size: 12px;
}

.text-gray-500 {
  color: #909399;
}

.mr-2 {
  margin-right: 0.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style> 