package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	fmt.Println("开始运行缓存测试...")

	// 获取当前目录
	currentDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("获取当前目录失败: %v\n", err)
		os.Exit(1)
	}

	// 构建测试目录路径
	testDir := filepath.Join(currentDir, "frontapi", "test", "cache")

	// 切换到测试目录
	err = os.Chdir(testDir)
	if err != nil {
		fmt.Printf("切换到测试目录失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("当前测试目录: %s\n", testDir)

	// 运行测试
	cmd := exec.Command("go", "test", "-v")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	fmt.Println("执行测试命令: go test -v")
	err = cmd.Run()
	if err != nil {
		fmt.Printf("测试执行失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("缓存测试完成!")
}
