<template>
  <div class="comment-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量隐藏
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量显示
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 - 使用SlinkyTable -->
    <div class="table-content">
      <SlinkyTable
        :data="commentList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleViewDetail"
        @edit="handleViewReplies"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="回复"
        delete-text="删除"
        empty-text="暂无评论数据"
        class="comment-data-table"
      >
        <!-- 评论信息列 -->
        <el-table-column prop="content" label="评论信息" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="comment-info">
              <el-avatar
                :size="36"
                :src="row.user_avatar"
                :alt="row.user_nickname"
                class="comment-avatar"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="comment-details">
                <div class="comment-user">{{ row.user_nickname || row.user_id }}</div>
                <div class="comment-content-preview">{{ row.content }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 视频ID列 -->
        <el-table-column prop="video_id" label="视频ID" min-width="120" align="center" show-overflow-tooltip />

        <!-- 统计信息列 -->
        <el-table-column label="统计" min-width="120" align="center">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon class="stat-icon"><ChatDotRound /></el-icon>
                <span class="stat-value">{{ row.reply_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><StarFilled /></el-icon>
                <span class="stat-value">{{ row.like_count || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <CommentStatusTag :status="row.status" />
          </template>
        </el-table-column>

        <!-- 评论时间列 -->
        <el-table-column prop="created_at" label="评论时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ formatDate(row.created_at) }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleViewDetail(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleViewReplies(row)">
              <el-icon><ChatDotRound /></el-icon>
              回复
            </el-button>
            <el-popconfirm
              :title="`确定要${row.status === 1 ? '隐藏' : '显示'}评论吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  link 
                  size="small"
                >
                  <el-icon>
                    <component :is="row.status === 1 ? Hide : View" />
                  </el-icon>
                  {{ row.status === 1 ? '隐藏' : row.status === 0 ? '显示' : row.status === -1 ? '审核' : '恢复' }}
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              title="确定要删除此评论吗？此操作不可恢复！"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页组件 -->
    <div class="table-footer">
      <SinglePager
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import type { CommentItem } from '@/service/api/videos/comments';
import { formatDate } from '@/utils/date';
import { Calendar, ChatDotRound, Check, Delete, Hide, StarFilled, User, View } from '@element-plus/icons-vue';
import { ref } from 'vue';
import CommentStatusTag from './CommentStatusTag.vue';

// Props
interface Props {
  loading: boolean;
  commentList: CommentItem[];
  currentPage: number;
  pageSize: number;
  total: number;
}

// Emits
interface Emits {
  (e: 'viewDetail', comment: CommentItem): void;
  (e: 'viewReplies', comment: CommentItem): void;
  (e: 'delete', comment: CommentItem): void;
  (e: 'changeStatus', id: string, status: number): void;
  (e: 'sizeChange', size: number): void;
  (e: 'currentChange', page: number): void;
  (e: 'batchStatus', status: number, comments: CommentItem[]): void;
  (e: 'batchDelete', comments: CommentItem[]): void;
  (e: 'selectionChange', selection: CommentItem[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 选中行数据
const selectedRows = ref<CommentItem[]>([]);

// 选择变化
const handleSelectionChange = (selection: CommentItem[]) => {
  selectedRows.value = selection;
  emit('selectionChange', selection);
};

// 查看详情
const handleViewDetail = (comment: CommentItem) => {
  emit('viewDetail', comment);
};

// 查看回复
const handleViewReplies = (comment: CommentItem) => {
  emit('viewReplies', comment);
};

// 删除评论
const handleDelete = (comment: CommentItem) => {
  emit('delete', comment);
};

// 更改状态
const handleChangeStatus = (id: string, status: number) => {
  emit('changeStatus', id, status);
};

// 分页相关
const handleSizeChange = (size: number) => {
  emit('sizeChange', size);
};

const handleCurrentChange = (page: number) => {
  emit('currentChange', page);
};

// 批量操作
const handleBatchStatus = (status: number) => {
  emit('batchStatus', status, [...selectedRows.value]);
};

const handleBatchDelete = () => {
  emit('batchDelete', [...selectedRows.value]);
};
</script>

<style scoped lang="scss">
.comment-table-container {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-weight: 500;
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-content {
    background: white;
    border-radius: 4px;
    overflow: hidden;
  }

  .comment-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .comment-details {
      min-width: 0;
      flex: 1;

      .comment-user {
        font-weight: 500;
        color: #333;
      }

      .comment-content-preview {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .stats-info {
    display: flex;
    gap: 16px;
    justify-content: center;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .stat-icon {
        color: #999;
        font-size: 14px;
      }

      .stat-value {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .time-icon {
      color: #999;
      font-size: 14px;
    }

    .time-text {
      font-size: 12px;
      color: #666;
    }
  }

  .action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
  }

  .table-footer {
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}
</style> 