package users

import (
	"frontapi/internal/models/users"
	"frontapi/internal/typings"
)

// UserInfo 用户信息 - 前端响应类型
type UserInfo struct {
	ID string `json:"id"`
	// Username string `json:"username"`
	Nickname string `json:"nickname"`
	// Email          string `json:"email"`
	// Phone          string `json:"phone"`
	Avatar         string `json:"avatar"`
	Cover          string `json:"cover"`
	Description    string `json:"description"`
	Gender         int8   `json:"gender"`
	Birthday       string `json:"birthday"`
	FollowersCount uint64 `json:"followersCount"`
	FollowingCount uint64 `json:"followingCount"`
	VideosCount    uint64 `json:"videosCount"`
	LikesCount     uint64 `json:"likesCount"`
	Heat           uint64 `json:"heat"`
	IsCelebrity    int8   `json:"isCelebrity"`
	IsVerified     int8   `json:"isVerified"`
	LastActiveTime string `json:"lastActiveTime"`
	Status         int8   `json:"status"`
	CreatedAt      string `json:"createdAt"`
}

// UserListResponse 用户列表响应 - 前端响应类型
type UserListResponse struct {
	typings.BaseListResponse
	List []UserInfo `json:"list"`
}

// CelebrityInfo 明星信息 - 前端响应类型
type CelebrityInfo struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	Nickname string `json:"nickname"`
	// Username         string `json:"username"`
	Avatar           string `json:"avatar"`
	Bio              string `json:"bio"`
	Description      string `json:"description"`
	Nation           string `json:"nation"`
	Region           string `json:"region"`
	Location         string `json:"location"`
	Gender           int8   `json:"gender"`
	Birthday         string `json:"birthday"`
	UserType         int    `json:"userType"`
	FollowCount      uint64 `json:"followCount"`
	LikeCount        uint64 `json:"likeCount"`
	TotalAlbums      uint64 `json:"totalAlbums"`
	TotalShorts      uint64 `json:"totalShorts"`
	TotalVideos      uint64 `json:"totalVideos"`
	TotalPosts       uint64 `json:"totalPosts"`
	TotalViews       uint64 `json:"totalViews"`
	TotalComments    uint64 `json:"totalComments"`
	TotalLikes       uint64 `json:"totalLikes"`
	TotalFollowing   uint64 `json:"totalFollowing"`
	Heat             uint64 `json:"heat"`
	RegTime          string `json:"regTime"`
	LastLoginTime    string `json:"lastLoginTime"`
	LastActiveTime   string `json:"lastActiveTime"`
	Status           int    `json:"status"`
	IsVerified       bool   `json:"isVerified"`
	IsContentCreator bool   `json:"isContentCreator"`
	CreatorLevel     int    `json:"creatorLevel"`
	IsLiked          bool   `json:"isLiked"`
	IsFollowed       bool   `json:"isFollowed"`
}

// CelebrityListResponse 明星列表响应 - 前端响应类型
type CelebrityListResponse struct {
	typings.BaseListResponse
	List []CelebrityInfo `json:"list"`
}

type CreatorInfo struct {
	ID           string `json:"id"`
	Nickname     string `json:"nickname"`
	Avatar       string `json:"avatar"`
	Bio          string `json:"bio"`
	FollowCount  uint64 `json:"followCount"`
	TotalVideos  uint64 `json:"totalVideos"`
	TotalPosts   uint64 `json:"totalPosts"`
	TotalShorts  uint64 `json:"totalShorts"`
	Heat         uint64 `json:"heat"`
	UserType     int    `json:"userType"`
	CreatorLevel int    `json:"creatorLevel"`
	IsLiked      bool   `json:"isLiked"`
	IsFollowed   bool   `json:"isFollowed"`
}

// CreatorDetailInfo 创作者详情信息
type CreatorDetailInfo struct {
	ID             string `json:"id"`
	Nickname       string `json:"nickname"`
	Avatar         string `json:"avatar"`
	Cover          string `json:"cover"`
	Bio            string `json:"bio"`
	Description    string `json:"description"`
	Gender         int8   `json:"gender"`
	Birthday       string `json:"birthday"`
	FollowCount    uint64 `json:"followCount"`
	LikeCount      uint64 `json:"likeCount"`
	TotalAlbums    uint64 `json:"totalAlbums"`
	TotalShorts    uint64 `json:"totalShorts"`
	TotalVideos    uint64 `json:"totalVideos"`
	TotalPosts     uint64 `json:"totalPosts"`
	TotalViews     uint64 `json:"totalViews"`
	TotalComments  uint64 `json:"totalComments"`
	TotalLikes     uint64 `json:"totalLikes"`
	TotalFollowing uint64 `json:"totalFollowing"`
	Heat           uint64 `json:"heat"`
	LastActiveTime string `json:"lastActiveTime"`
	Status         int8   `json:"status"`
	IsFollowed     bool   `json:"isFollowed"` //是否关注
	IsLiked        bool   `json:"isLiked"`    //是否点赞
	CreatedAt      string `json:"createdAt"`
}

type CreatorListResponse struct {
	typings.BaseListResponse
	List []CreatorInfo `json:"list"`
}

type CreatorCommentListResponse struct {
	typings.BaseListResponse
	List []CreatorCommentInfo `json:"list"`
}

type CreatorCommentInfo struct {
	UserID       string `json:"userId"`       // 用户ID
	Content      string `json:"content"`      // 评论内容
	UserNickname string `json:"userNickname"` // 用户昵称
	UserAvatar   string `json:"userAvatar"`   // 用户头像
	ParentID     string `json:"parentId"`     // 父级ID
	EntityID     string `json:"entityId"`     // 关联实体ID
	EntityType   int8   `json:"entityType"`   // 关联实体类型
	RelationID   string `json:"relationId"`   // 关联ID
	Heat         int64  `json:"heat"`         // 热度
	LikeCount    int64  `json:"likeCount"`    // 点赞数
	ReplyCount   int64  `json:"replyCount"`   // 回复数
	CreatedAt    string `json:"createdAt"`    // 创建时间
	UpdatedAt    string `json:"updatedAt"`    // 更新时间
	Status       int8   `json:"status"`       // 状态：0-禁用，1-正常
}

// FollowersStatsResponse 粉丝统计响应数据
type FollowersStatsResponse struct {
	UserID      string                       `json:"user_id"`      // 用户ID
	TimeType    string                       `json:"time_type"`    // 时间类型
	StartTime   string                       `json:"start_time"`   // 开始时间
	EndTime     string                       `json:"end_time"`     // 结束时间
	TotalCount  int64                        `json:"total_count"`  // 总粉丝数
	GrowthStats []*users.FollowersGrowthStat `json:"growth_stats"` // 增长统计数据
}
