package integral

import (
	"gorm.io/gorm"

	model "frontapi/internal/models/integral"
	"frontapi/internal/repository/base"
)

// PointRepository 积分数据访问接口
type PointRepository interface {
	base.ExtendedRepository[model.Point]
}

// pointRepository 实现
type pointRepository struct {
	base.ExtendedRepository[model.Point]
}

// NewPointRepository 创建实例
func NewPointRepository(db *gorm.DB) PointRepository {
	return &pointRepository{
		ExtendedRepository: base.NewExtendedRepository[model.Point](db),
	}
}
