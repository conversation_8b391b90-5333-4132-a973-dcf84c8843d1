package utils

import (
	"math/rand"
	"net/http"
	"strings"
	"time"
)

// GetFileContentType 根据文件字节流获取文件MIME类型
func GetFileContentType(buffer []byte) string {
	// 使用http包的DetectContentType函数检测内容类型
	contentType := http.DetectContentType(buffer)

	// 只返回MIME类型主要部分，例如image/jpeg
	return strings.Split(contentType, ";")[0]
}

// RandomString 生成指定长度的随机字符串
func RandomString(length int) string {
	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 定义可用字符集
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

	// 生成随机字符串
	result := make([]byte, length)
	for i := range result {
		result[i] = charset[rand.Intn(len(charset))]
	}

	return string(result)
}
func GetFirstPathSegment(path, delimiter string) string {
	// 移除路径前后的斜杠
	trimmedPath := strings.Trim(path, delimiter)
	// 按斜杠分割路径
	segments := strings.Split(trimmedPath, delimiter)
	if len(segments) > 0 {
		return segments[0]
	}
	return ""
}
