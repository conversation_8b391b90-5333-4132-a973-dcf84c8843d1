import { post, page } from '@/shared/composables'
import { corePost, coreGetPage } from '@/shared/composables'
import type { 
  Celebrity, 
  CelebrityDetail, 
  CelebrityQueryParams, 
  CelebrityListResponse, 
  CelebrityDetailResponse,
  CelebrityVideoQueryParams 
} from '@/types/user'

// 明星详情请求参数
export interface CelebrityDetailParams {
  code: string
}

// 明星视频列表请求参数
export interface CelebrityVideoListParams {
  data: {
    code: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}

// 明星动态列表请求参数
export interface CelebrityPostListParams {
  data: {
    code: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}

// 明星图片集请求参数
export interface CelebrityGalleryParams {
  data: {
    code: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}

// 明星成就荣誉请求参数
export interface CelebrityAchievementsParams {
  data: {
    code: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}

// 关注明星请求参数
export interface FollowCelebrityParams {
  data: {
    celebrityId: string
    action: 'follow' | 'unfollow'
  }
}

/**
 * 获取明星详情
 * @param params 请求参数
 */
export function getCelebrityDetail(params: CelebrityDetailParams) {
  return corePost('/celebrity/getCelebrityDetail', params)
}

/**
 * 获取明星视频列表
 * @param params 请求参数
 */
export function getCelebrityVideoList(params: CelebrityVideoListParams) {
  return coreGetPage('/celebrity/getCelebrityVideoList', {
    data: params.data,
    page: { page: params.page.pageNo, pageSize: params.page.pageSize }
  })
}

/**
 * 获取明星动态列表
 * @param params 请求参数
 */
export function getCelebrityPostList(params: CelebrityPostListParams) {
  return coreGetPage('/celebrity/getCelebrityPostList', {
    data: params.data,
    page: { page: params.page.pageNo, pageSize: params.page.pageSize }
  })
}

/**
 * 获取明星图片集
 * @param params 请求参数
 */
export function getCelebrityGallery(params: CelebrityGalleryParams) {
  return corePost('/celebrity/getCelebrityGallery', params)
}

/**
 * 获取明星成就荣誉
 * @param params 请求参数
 */
export function getCelebrityAchievements(params: CelebrityAchievementsParams) {
  return corePost('/celebrity/getCelebrityAchievements', params)
}

/**
 * 关注/取消关注明星
 * @param params 请求参数
 */
export function followCelebrity(params: FollowCelebrityParams) {
  return corePost('/celebrity/follow', params)
}

/**
 * 点赞明星
 * @param celebrityId 明星ID
 */
export function likeCelebrity(celebrityId: string) {
  return corePost('/celebrity/like', { 
    data: { 
      celebrityId 
    } 
  })
}

/**
 * 获取明星列表
 * @param params 查询参数
 */
export function getCelebrityList(params: {
  data?: {
    keyword?: string
    category?: string
    nation?: string
    gender?: number,
    sortBy?: string
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return coreGetPage('/celebrity/getAllCelebrity', {
    data: params.data || {},
    page: { page: params.page.pageNo, pageSize: params.page.pageSize }
  })
}

/**
 * 获取推荐明星
 * @param limit 推荐数量
 */
export function getRecommendedCelebrities(limit: number = 6) {
  return corePost('/celebrity/recommended', {   
    data: { 
      limit 
    } 
  })
}

/**
 * 获取明星短视频列表
 * @param params 请求参数
 */
export function getCelebrityShortVideoList(params: CelebrityVideoListParams) {
  return coreGetPage('/celebrity/getCelebrityShortVideoList', {
    data: params.data,
    page: { page: params.page.pageNo, pageSize: params.page.pageSize }
  })
}

/**
 * 获取明星图片列表
 * @param params 请求参数
 */
export function getCelebrityImageList(params: CelebrityVideoListParams) {
  return coreGetPage('/celebrity/getCelebrityImageList', {
    data: { celebrity_id: params.data.code },
    page: { page: params.page.pageNo, pageSize: params.page.pageSize }
  })
}

/**
 * 获取明星评论列表
 * @param params 请求参数
 */
export function getCelebrityComments(params: {
  data: {
    celebrity_id: string
    type?: string // 'video' | 'post' | 'shortvideo' | 'all'
    sort?: string // 'popular' | 'liked' | 'latest'
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return coreGetPage('/celebrity/getCelebrityComments', {
    data: params.data,
    page: { page: params.page.pageNo, pageSize: params.page.pageSize }
  })
}

/**
 * 获取粉丝增长统计数据
 * @param params 请求参数
 */
export function getFollowersStats(params: {
  data: {
    celebrity_id: string
    time_range: 'day' | 'week' | 'month' | 'quarter'
    start_time: string // YYYY-MM-DD
    end_time: string   // YYYY-MM-DD
  }
  page: {
    pageNo: number
    pageSize: number
  }
}) {
  return getPage('/celebrity/getFollowersStats', params.page.pageNo, params.page.pageSize, params.data)
}