package comics

import (
	"frontapi/internal/api"
	comics_service "frontapi/internal/service/comics"
	comicsTypings "frontapi/internal/typings/comics"
	"frontapi/pkg/cache"
	"frontapi/pkg/utils"
	"time"

	"github.com/gofiber/fiber/v2"
)

// ComicController 漫画处理器
type ComicCategoryController struct {
	api.BaseController
	comicCategoryService comics_service.ComicCategoryService
	comicService         comics_service.ComicService
}

// NewComicController 创建漫画处理器
func NewComicCategoryController(
	comicCategoryService comics_service.ComicCategoryService,
	comicService comics_service.ComicService,
) *ComicCategoryController {
	return &ComicCategoryController{
		comicCategoryService: comicCategoryService,
		comicService:         comicService,
	}
}

// 获取漫画分类列表
func (c *ComicCategoryController) GetCategoryList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	sortBy := reqInfo.Get("sortBy").GetString()
	if sortBy == "" {
		sortBy = "created_at DESC"
	}

	//获取有电子书的分类
	categoryIds, err := getComicCategoryWithEbook(ctx, c)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	condition := map[string]interface{}{
		"keyword": keyword,
		"id IN":   categoryIds,
	}
	comicCategories, total, err := c.comicCategoryService.List(ctx.Context(), condition, sortBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := comicsTypings.ConvertComicCategoryListResponse(comicCategories, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// 获取有电子书的分类
func getComicCategoryWithEbook(ctx *fiber.Ctx, c *ComicCategoryController) ([]string, error) {

	cacheKey := "comic_category_list_with_ebook"
	categoryBytes, err := cache.GetAdapter("redis").Get(ctx.Context(), cacheKey)
	if err == nil && categoryBytes != nil {
		var categoryIds []string
		err = utils.Unmarshal(categoryBytes, &categoryIds)
		if err == nil && len(categoryIds) > 0 {
			condition := map[string]interface{}{
				"status":   1,
				"group_by": "category_id",
			}
			comicCategories, err := c.comicService.FindAll(ctx.Context(), condition, "created_at DESC", false)
			if err != nil {
				return nil, err
			}

			categoryIds := make([]string, 0, len(comicCategories))
			for _, comic := range comicCategories {
				categoryIds = append(categoryIds, comic.CategoryID.String)
			}
			categoryIdsBytes, err := utils.Marshal(categoryIds)
			if err != nil {
				return nil, err
			}
			if err := cache.GetAdapter("redis").Set(ctx.Context(), cacheKey, categoryIdsBytes, time.Hour*24); err != nil {
				return nil, err
			}
			return categoryIds, nil
		}
		return categoryIds, nil
	}
	return nil, nil
}

// 获取漫画分类详情
func (c *ComicCategoryController) GetCategoryDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	id := reqInfo.Get("id").GetString()
	comicCategory, err := c.comicCategoryService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := comicsTypings.ConvertComicCategoryInfo(comicCategory)
	return c.Success(ctx, response)
}
