package content_creator

import (
	"frontapi/internal/models/content_creator"
	repo "frontapi/internal/repository/content_creator"
	"frontapi/internal/service/base"
)

type ContentRevenueService interface {
	base.IExtendedService[content_creator.ContentRevenue]
}

type contentRevenueService struct {
	*base.ExtendedService[content_creator.ContentRevenue]
	repo repo.ContentRevenueRepository
}

func NewContentRevenueService(repo repo.ContentRevenueRepository) ContentRevenueService {
	return &contentRevenueService{
		ExtendedService: base.NewExtendedService[content_creator.ContentRevenue](repo, "content_revenue"),
		repo:            repo,
	}
}
