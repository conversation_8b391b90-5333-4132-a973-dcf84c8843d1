# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
ADMIN_SERVER_PORT=8081  # 管理后台API端口
MEDIA_SERVER_PORT=8082  # 媒体服务器端口

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=lyvideos

# JWT配置
JWT_SECRET=LHd5cdf2khs
JWT_EXPIRY=168

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_EXPIRY=168 # Redis缓存过期时间（小时）
# 管理后台Redis配置
REDIS_ADMIN_DB=1
REDIS_ADMIN_PREFIX=admin:
REDIS_LIKE_DB=1
REDIS_LIKE_PREFIX=ly_
REDIS_COLLECT_DB=1
REDIS_COLLECT_PREFIX=ly_
REDIS_FOLLOW_DB=1
REDIS_FOLLOW_PREFIX=ly_



# 静态文件配置
STATIC_URL=/static/

RUNTEMP=../temp

# CORS配置
CORS_ALLOW_ORIGINS=http://localhost:8080,http://localhost:8081,http://localhost:8848,http://localhost:9527
CORS_ALLOW_METHODS=GET, POST, PUT, DELETE, OPTIONS, PATCH
CORS_ALLOW_HEADERS=Origin, Content-Type, Accept, Authorization, X-Admin-Token, X-Requested-With,x-client-info, Content-Type, Authorization,x-request-id,apifoxtoken
CORS_ALLOW_CREDENTIALS=true

# 小图片配置（分类图标、头像等）
UPLOAD_SMALL_IMAGE_PATH=../storage/static/images
UPLOAD_SMALL_IMAGE_URL=http://localhost:8082/static/images

# 图片模块的图片
UPLOAD_PICTURE_PATH=../storage/pictures
UPLOAD_PICTURE_URL=http://localhost:8082/pictures

# 视频
UPLOAD_VIDEO_PATH=../storage/videos
UPLOAD_VIDEO_URL=http://localhost:8082/videos# ����������


# Casbin配置
CASBIN_MODEL_PATH=config/rbac_model.conf
CASBIN_POLICY_FILE=config/rbac_policy.csv