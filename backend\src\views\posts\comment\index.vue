<template>
    <div class="app-container">
        <el-card>
            <template #header>
                <div class="filter-container">
                    <div class="title-container">
                        <h2>帖子评论管理</h2>
                        <div class="buttons">
                            <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
                            <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 搜索表单组件 -->
            <CommentSearchBar v-model="searchForm" @search="handleSearch" @reset="handleReset" @refresh="refreshList"
                class="mb-4" />

            <!-- 评论表格组件 -->
            <CommentTable :loading="loading" :comment-list="commentList" :pagination="pagination"
                @view-detail="handleViewDetail" @toggle-status="handleToggleStatus" @delete="handleDelete"
                @batch-delete="handleBatchDelete" @view-replies="handleViewReplies" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" @approve="handleApprove" @reject="handleReject"
                @batch-approve="handleBatchApprove" @batch-reject="handleBatchReject" @batch-show="handleBatchShow"
                @batch-hide="handleBatchHide" />

            <!-- 评论详情对话框 -->
            <CommentDetail v-model:visible="detailDialogVisible" :comment="currentComment" />

            <!-- 回复列表对话框 -->
            <ReplyList v-model:visible="repliesDialogVisible" :loading="repliesLoading" :parent-comment="parentComment"
                :reply-list="replyList" :pagination="replyPagination" @delete="handleDeleteReply"
                @page-change="handleReplyPageChange" />

            <!-- 审核对话框 -->
            <ReviewDialog v-model:visible="reviewDialogVisible" :loading="reviewLoading" :is-batch="reviewIsBatch"
                :comment-id="reviewCommentId" :comment-content="reviewCommentContent" :comment-ids="reviewCommentIds"
                @submit="handleReviewSubmit" />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import {
    batchDeleteComment,
    batchUpdateCommentStatus,
    deleteComment,
    getCommentList,
    getCommentReplies,
    updateCommentStatus
} from '@/service/api/posts/comment';
import type {
    CommentParams,
    PostComment
} from '@/types/posts';
import { Download, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入组件
import CommentDetail from './components/CommentDetail.vue';
import CommentSearchBar from './components/CommentSearchBar.vue';
import CommentTable from './components/CommentTable.vue';
import ReplyList from './components/ReplyList.vue';
import ReviewDialog from './components/ReviewDialog.vue';

// 搜索表单
const searchForm = reactive({
    post_id: '',
    user_id: '',
    content: '',
    status: undefined as number | undefined,
});

// 分页参数
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
});

// 回复分页参数
const replyPagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
});

// 数据列表
const commentList = ref<PostComment[]>([]);
const replyList = ref<PostComment[]>([]);
const selectedRows = ref<PostComment[]>([]);

// 当前选中的评论
const currentComment = ref<PostComment | null>(null);
const parentComment = ref<PostComment | null>(null);

// 加载状态
const loading = ref(false);
const repliesLoading = ref(false);
const reviewLoading = ref(false);

// 对话框状态
const detailDialogVisible = ref(false);
const repliesDialogVisible = ref(false);
const reviewDialogVisible = ref(false);

// 审核参数
const reviewIsBatch = ref(false);
const reviewCommentId = ref('');
const reviewCommentContent = ref('');
const reviewCommentIds = ref<string[]>([]);

// 初始化加载
onMounted(() => {
    fetchCommentList();
});

// 获取评论列表
const fetchCommentList = async () => {
    loading.value = true;
    try {
        const params: CommentParams = {
            page: {
                pageNo: pagination.page,
                pageSize: pagination.pageSize
            },
            data: {
                content: searchForm.content || undefined,
                post_id: searchForm.post_id || undefined,
                user_id: searchForm.user_id || undefined,
                status: searchForm.status,
            }
        };

        const { data, err, response } = await getCommentList(params) as any;

        if (!err && data) {
            commentList.value = data.list || [];
            pagination.total = data.total || 0;
        } else {
            ElMessage.error(response?.data?.message || '获取评论列表失败');
            commentList.value = [];
            pagination.total = 0;
        }
    } catch (error) {
        console.error('获取评论列表出错:', error);
        ElMessage.error('获取评论列表失败');
        commentList.value = [];
        pagination.total = 0;
    } finally {
        loading.value = false;
    }
};

// 获取评论详情
const handleViewDetail = (row: PostComment) => {
    currentComment.value = row;
    detailDialogVisible.value = true;
};

// 修改状态
const handleToggleStatus = async (row: PostComment, status: number) => {
    try {

        let statusText = status === 2 ? '显示' : status == 1 ? '隐藏' : '恢复';

        const { response } = await updateCommentStatus(row.id, status) as any;

        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success(`评论${statusText}成功`);
            fetchCommentList();
        } else {
            ElMessage.error(response?.data?.message || `评论${statusText}失败`);
        }
    } catch (error) {
        console.error('更新评论状态出错:', error);
        ElMessage.error('更新评论状态失败');
    }
};

// 删除评论
const handleDelete = async (row: PostComment) => {
    try {
        const { response } = await deleteComment(row.id) as any;

        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('删除评论成功');
            fetchCommentList();
        } else {
            ElMessage.error(response?.data?.message || '删除评论失败');
        }
    } catch (error) {
        console.error('删除评论出错:', error);
        ElMessage.error('删除评论失败');
    }
};

// 批量删除评论
const handleBatchDelete = async (rows: PostComment[]) => {
    try {
        const ids = rows.map(row => row.id);

        // 这里假设后端有批量删除的接口，如果没有，需要实现
        const { response } = await batchDeleteComment({ ids }) as any;

        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('批量删除评论成功');
            fetchCommentList();
        } else {
            ElMessage.error(response?.data?.message || '批量删除评论失败');
        }
    } catch (error) {
        console.error('批量删除评论出错:', error);
        ElMessage.error('批量删除评论失败');
    }
};

// 批量显示评论
const handleBatchShow = async (rows: PostComment[], status: number) => {
    try {
        const ids = rows.map(row => row.id);

        // 这里假设后端有批量更新状态的接口，如果没有，需要实现
        // 可以使用Promise.all来并行处理多个请求
        const { response } = await batchUpdateCommentStatus({ ids, status }) as any;

        if (response.status === 200 && response.data.code == 2000) {
            ElMessage.success('批量更新状态成功');
            fetchCommentList();
        } else {
            ElMessage.error('部分评论状态更新失败');
            fetchCommentList();
        }
    } catch (error) {
        console.error('批量更新状态出错:', error);
        ElMessage.error('批量更新状态失败');
    }
};

// 批量隐藏评论
const handleBatchHide = async (rows: PostComment[], status: number) => {
    await handleBatchShow(rows, status);
};

// 通过评论
const handleApprove = (row: PostComment) => {
    reviewIsBatch.value = false;
    reviewCommentId.value = row.id;
    reviewCommentContent.value = row.content;
    reviewCommentIds.value = [];
    reviewDialogVisible.value = true;
};

// 拒绝评论
const handleReject = (row: PostComment) => {
    reviewIsBatch.value = false;
    reviewCommentId.value = row.id;
    reviewCommentContent.value = row.content;
    reviewCommentIds.value = [];
    reviewDialogVisible.value = true;
};

// 批量通过评论
const handleBatchApprove = (rows: PostComment[]) => {
    reviewIsBatch.value = true;
    reviewCommentId.value = '';
    reviewCommentContent.value = '';
    reviewCommentIds.value = rows.map(row => row.id);
    reviewDialogVisible.value = true;
};

// 批量拒绝评论
const handleBatchReject = (rows: PostComment[]) => {
    reviewIsBatch.value = true;
    reviewCommentId.value = '';
    reviewCommentContent.value = '';
    reviewCommentIds.value = rows.map(row => row.id);
    reviewDialogVisible.value = true;
};

// 提交审核结果
const handleReviewSubmit = async (status: number, reason: string, id?: string, ids?: string[]) => {
    reviewLoading.value = true;
    try {
        if (ids && ids.length > 0) {
            // 批量审核
            // 这里假设后端有批量审核的接口，如果没有，需要实现
            // 可以使用Promise.all来并行处理多个请求
            const { response } = await batchUpdateCommentStatus({ ids, status: status === 2 ? 2 : -2 }) as any;

            if (response.status === 200 && response.data.code == 2000) {
                ElMessage.success('批量审核成功');
                fetchCommentList();
            } else {
                ElMessage.error('部分评论审核失败');
                fetchCommentList();
            }
        } else if (id) {
            // 单个审核
            const { response } = await updateCommentStatus(id, status === 2 ? 2 : -2, reason) as any;

            if (response.status === 200 && response.data.code == 2000) {
                ElMessage.success('审核成功');
                fetchCommentList();
            } else {
                ElMessage.error(response?.data?.message || '审核失败');
            }
        }
    } catch (error) {
        console.error('审核评论出错:', error);
        ElMessage.error('审核评论失败');
    } finally {
        reviewLoading.value = false;
    }
};

// 查看回复
const handleViewReplies = async (row: PostComment) => {
    if (row.heat <= 0) {
        ElMessage.info('该评论暂无回复');
        return;
    }

    parentComment.value = row;
    replyPagination.page = 1;
    repliesDialogVisible.value = true;

    await fetchReplies(row.id);
};

// 获取回复列表
const fetchReplies = async (commentId: string) => {
    repliesLoading.value = true;
    try {
        const { data, err, response } = await getCommentReplies(
            commentId,
            replyPagination.page,
            replyPagination.pageSize
        ) as any;

        if (!err && data) {
            replyList.value = data.list || [];
            replyPagination.total = data.total || 0;
        } else {
            ElMessage.error(response?.data?.message || '获取回复列表失败');
            replyList.value = [];
            replyPagination.total = 0;
        }
    } catch (error) {
        console.error('获取回复列表出错:', error);
        ElMessage.error('获取回复列表失败');
        replyList.value = [];
        replyPagination.total = 0;
    } finally {
        repliesLoading.value = false;
    }
};

// 删除回复
const handleDeleteReply = async (reply: PostComment) => {
    try {
        const { err, response } = await deleteComment(reply.id) as any;

        if (!err) {
            ElMessage.success('删除回复成功');

            // 更新父评论的回复数
            if (parentComment.value) {
                parentComment.value.heat--;
            }

            // 重新获取回复列表
            if (parentComment.value) {
                await fetchReplies(parentComment.value.id);
            }

            // 刷新评论列表
            fetchCommentList();
        } else {
            ElMessage.error(response?.data?.message || '删除回复失败');
        }
    } catch (error) {
        console.error('删除回复出错:', error);
        ElMessage.error('删除回复失败');
    }
};

// 回复分页变化
const handleReplyPageChange = (page: number) => {
    replyPagination.page = page;
    if (parentComment.value) {
        fetchReplies(parentComment.value.id);
    }
};

// 分页大小变化
const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.page = 1;
    fetchCommentList();
};

// 页码变化
const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchCommentList();
};

// 搜索
const handleSearch = (params: any) => {
    Object.assign(searchForm, params);
    pagination.page = 1;
    fetchCommentList();
};

// 重置搜索
const handleReset = () => {
    searchForm.content = '';
    searchForm.post_id = '';
    searchForm.user_id = '';
    searchForm.status = undefined;
    pagination.page = 1;
    fetchCommentList();
};

// 刷新列表
const refreshList = () => {
    fetchCommentList();
};

// 导出评论
const handleExport = () => {
    ElMessage.info('导出功能开发中...');
};
</script>

<style scoped lang="scss">
.app-container {
    padding: 20px;
}

.filter-container {
    margin-bottom: 20px;
}

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.title-container h2 {
    margin: 0;
    color: #303133;
    font-weight: 600;
}

.buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }

    .title-container {
        flex-direction: column;
        align-items: stretch;
    }

    .buttons {
        justify-content: center;
    }
}
</style>
