package cache

import "time"

// FileConfig 文件缓存配置
type FileConfig struct {
	Path              string        `mapstructure:"path"`               // 缓存文件路径
	CleanupInterval   time.Duration `mapstructure:"cleanup_interval"`   // 清理间隔
	FileMode          int           `mapstructure:"file_mode"`          // 文件权限
	DirMode           int           `mapstructure:"dir_mode"`           // 目录权限
	EnableCompression bool          `mapstructure:"enable_compression"` // 是否启用压缩
	MaxFileSize       int           `mapstructure:"max_file_size"`      // 最大文件大小
	Compression       bool          `mapstructure:"compression"`        // 是否启用压缩
}
