<template>
  <div class="vertical-menu" :class="{ 'menu-collapsed': collapsed }">
    <div class="menu-items">
      <div
        v-for="item in menuItems"
        :key="item.label"
        class="menu-item"
        :class="{ 
          'has-submenu': item.items,
          'active': isActiveRoute(item.route),
          'submenu-open': openSubmenu === item.label
        }"
      >
        <!-- 主菜单项 -->
        <div
          :ref="el => setMenuItemRef(item.label, el)"
          class="menu-item-content"
          @click="() => handleMenuItemClick(item)"
          @mouseenter="() => handleMenuItemHover(item)"
          @mouseleave="() => handleMenuItemLeave(item)"
        >
          <router-link
            v-if="item.route && !item.items"
            :to="item.route"
            class="menu-link"
            :class="{ 'active': isActiveRoute(item.route) }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span v-if="!collapsed" class="menu-label">{{ t(item.label) }}</span>
          </router-link>
          <div
            v-else
            class="menu-link"
            :class="{ 'has-submenu': item.items }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span v-if="!collapsed" class="menu-label">{{ t(item.label) }}</span>
            <i 
              v-if="item.items && !collapsed" 
              class="pi pi-angle-right submenu-indicator"
              :class="{ 'rotated': openSubmenu === item.label }"
            ></i>
          </div>
        </div>

        <!-- 子菜单 CustomPopover (用于悬停模式) -->
        <div
          v-if="item.items && (collapsed || hoverMode)"
          class="submenu-popover-wrapper"
        >
          <CustomPopover
            :ref="el => setPopoverRef(item.label, el)"
            placement="right-start"
            trigger="manual"
            :show-arrow="true"
            :offset="8"
            class="submenu-popover"
          >
            <template #trigger>
              <div class="popover-trigger-placeholder"></div>
            </template>

            <template #header>
              <span class="submenu-title">{{ t(item.label) }}</span>
            </template>

            <template #body>
              <div class="submenu-items">
                <router-link
                  v-for="subItem in item.items"
                  :key="subItem.label"
                  :to="subItem.route!"
                  class="submenu-item-link"
                  :class="{ 'active': isActiveRoute(subItem.route) }"
                  @click="$emit('item-click', subItem)"
                >
                  <span v-if="subItem.icon" class="submenu-icon">
                    <i :class="subItem.icon"></i>
                  </span>
                  <span class="submenu-label">{{ t(subItem.label) }}</span>
                </router-link>
              </div>
            </template>
          </CustomPopover>
        </div>

        <!-- 子菜单展开 (用于展开模式) -->
        <div
          v-if="item.items && !collapsed && !hoverMode && openSubmenu === item.label"
          class="submenu-expanded"
        >
          <router-link
            v-for="subItem in item.items"
            :key="subItem.label"
            :to="subItem.route!"
            class="submenu-item-link"
            :class="{ 'active': isActiveRoute(subItem.route) }"
            @click="$emit('item-click', subItem)"
          >
            <span v-if="subItem.icon" class="submenu-icon">
              <i :class="subItem.icon"></i>
            </span>
            <span class="submenu-label">{{ t(subItem.label) }}</span>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import CustomPopover from '../CustomPopover/index.vue';

// Props
interface Props {
  collapsed?: boolean;
  hoverMode?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  hoverMode: true
});

// Emits
interface Emits {
  'item-click': [item: any];
}

const emit = defineEmits<Emits>();

// Composables
const { t } = useTranslation();
const route = useRoute();

// State
const openSubmenu = ref('');
const hoverTimer = ref<NodeJS.Timeout | null>(null);
const popoverRefs = ref<Record<string, any>>({});
const menuItemRefs = ref<Record<string, HTMLElement>>({});

// Methods
const setPopoverRef = (label: string, el: any) => {
  if (el) {
    popoverRefs.value[label] = el;
  }
};

const setMenuItemRef = (label: string, el: any) => {
  if (el && el instanceof HTMLElement) {
    menuItemRefs.value[label] = el;
  }
};

const isActiveRoute = (routePath?: string) => {
  if (!routePath) return false;
  return route.path === routePath || route.path.startsWith(routePath + '/');
};

const handleMenuItemClick = (item: any) => {
  if (item.items) {
    if (props.collapsed || props.hoverMode) {
      const popover = popoverRefs.value[item.label];
      const menuItem = menuItemRefs.value[item.label];
      if (popover && menuItem) {
        popover.showWithTrigger(menuItem);
      }
    } else {
      openSubmenu.value = openSubmenu.value === item.label ? '' : item.label;
    }
  } else if (item.route) {
    emit('item-click', item);
  }
};

const handleMenuItemHover = (item: any) => {
  if (item.items && (props.collapsed || props.hoverMode)) {
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }
    
    const popover = popoverRefs.value[item.label];
    const menuItem = menuItemRefs.value[item.label];
    if (popover && menuItem) {
      popover.showWithTrigger(menuItem);
    }
  }
};

const handleMenuItemLeave = (item: any) => {
  if (item.items && (props.collapsed || props.hoverMode)) {
    hoverTimer.value = setTimeout(() => {
      const popover = popoverRefs.value[item.label];
      if (popover) {
        popover.hide();
      }
    }, 300);
  }
};
</script>

<style lang="scss" scoped>
.vertical-menu {
  width: 280px;
  background: var(--surface-card);
  border-right: 1px solid var(--surface-border);
  transition: width 0.3s ease;
  
  &.menu-collapsed {
    width: 60px;
  }
  
  .menu-items {
    .menu-item {
      position: relative;
      border-bottom: 1px solid var(--surface-border);
      
      &:last-child {
        border-bottom: none;
      }
      
      .menu-item-content {
        .menu-link {
          display: flex;
          align-items: center;
          padding: 1rem;
          color: var(--text-color);
          text-decoration: none;
          transition: all 0.2s ease;
          cursor: pointer;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            font-weight: 500;
            
            .menu-icon i {
              color: var(--primary-color);
            }
          }
          
          .menu-icon {
            width: 24px;
            margin-right: 0.75rem;
            color: var(--text-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            
            i {
              font-size: 1.1rem;
            }
          }
          
          .menu-label {
            flex: 1;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .submenu-indicator {
            color: var(--text-color-secondary);
            font-size: 0.8rem;
            transition: transform 0.2s ease;
            
            &.rotated {
              transform: rotate(90deg);
            }
          }
        }
      }
      .submenu-popover-wrapper{
        display: none;
      }
      
      .submenu-expanded {
        background: var(--surface-ground);
        
        .submenu-item-link {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem 0.75rem 3rem;
          color: var(--text-color);
          text-decoration: none;
          transition: background 0.2s ease;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            
            .submenu-icon i {
              color: var(--primary-color);
            }
          }
          
          .submenu-icon {
            width: 20px;
            margin-right: 0.75rem;
            color: var(--text-color-secondary);
            
            i {
              font-size: 0.9rem;
            }
          }
          
          .submenu-label {
            font-size: 0.9rem;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
