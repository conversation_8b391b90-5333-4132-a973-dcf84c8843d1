package redis

import (
	"context"
	"fmt"
	"time"

	"frontapi/internal/service/base/extfollow/types"

	goredis "github.com/go-redis/redis/v8"
)

// CacheOperations 缓存操作处理器
type CacheOperations struct {
	client   *goredis.Client
	cacheKey *types.CacheKey
	config   *Config
}

// NewCacheOperations 创建缓存操作处理器
func NewCacheOperations(client *goredis.Client, cacheKey *types.CacheKey, config *Config) *CacheOperations {
	return &CacheOperations{
		client:   client,
		cacheKey: cacheKey,
		config:   config,
	}
}

// InvalidateCache 清除缓存
func (c *CacheOperations) InvalidateCache(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}

	err := c.client.Del(ctx, keys...).Err()
	if err != nil {
		return fmt.Errorf("清除缓存失败: %w", err)
	}

	return nil
}

// WarmupCache 预热缓存
func (c *CacheOperations) WarmupCache(ctx context.Context, userIDs []string) error {
	// 简化实现，预热用户的关注和粉丝计数
	for _, userID := range userIDs {
		// 这里可以预加载用户的关注数据
		// 实际实现中会从数据库加载数据到缓存
		followingKey := c.cacheKey.FollowingCountKey(userID)
		followerKey := c.cacheKey.FollowerCountKey(userID)

		// 设置默认值（实际应该从数据库获取）
		c.client.SetNX(ctx, followingKey, "0", c.config.DefaultTTL)
		c.client.SetNX(ctx, followerKey, "0", c.config.DefaultTTL)
	}

	return nil
}

// GetCacheStats 获取缓存统计
func (c *CacheOperations) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	info, err := c.client.Info(ctx, "memory").Result()
	if err != nil {
		return nil, fmt.Errorf("获取缓存统计失败: %w", err)
	}

	stats := map[string]interface{}{
		"redis_info": info,
		"prefix":     c.cacheKey.Prefix,
	}

	return stats, nil
}

// CleanupExpiredData 清理过期数据
func (c *CacheOperations) CleanupExpiredData(ctx context.Context, before time.Time) error {
	// Redis会自动清理过期数据，这里只是示例
	// 实际可以清理一些特定的过期数据
	pattern := c.cacheKey.Prefix + "*"

	iter := c.client.Scan(ctx, 0, pattern, 100).Iterator()
	var keysToCheck []string

	for iter.Next(ctx) {
		keysToCheck = append(keysToCheck, iter.Val())
		if len(keysToCheck) >= 100 {
			// 检查这批keys的TTL
			pipe := c.client.Pipeline()
			for _, key := range keysToCheck {
				pipe.TTL(ctx, key)
			}

			// 执行批量TTL检查
			pipe.Exec(ctx)
			keysToCheck = keysToCheck[:0]
		}
	}

	return iter.Err()
}

// ExportData 导出数据
func (c *CacheOperations) ExportData(ctx context.Context, userID, format string) ([]byte, error) {
	// 简化实现，导出用户的关注数据
	data := fmt.Sprintf("用户 %s 的关注数据导出 (格式: %s)", userID, format)
	return []byte(data), nil
}

// ImportData 导入数据
func (c *CacheOperations) ImportData(ctx context.Context, data []byte, format string) error {
	// 简化实现
	return fmt.Errorf("导入功能暂未实现")
}

// HealthCheck 健康检查
func (c *CacheOperations) HealthCheck(ctx context.Context) error {
	err := c.client.Ping(ctx).Err()
	if err != nil {
		return fmt.Errorf("Redis健康检查失败: %w", err)
	}

	return nil
}

// Close 关闭适配器
func (c *CacheOperations) Close() error {
	return c.client.Close()
}
