package container

import (
	permRepo "frontapi/internal/repository/permission"
	permSrv "frontapi/internal/service/permission"
	"log"
)

// InitPermissionServices 初始化权限相关服务
func InitPermissionServices(b *ServiceBuilder) {
	// 初始化权限仓库
	adminUserRepo := permRepo.NewAdminUserRepository(b.DB())
	adminMenuRepo := permRepo.NewAdminMenuRepository(b.DB())
	// adminRoleRepo := permRepo.NewAdminRoleRepository(b.DB()) // 暂时未使用，保留以备后续需要

	// 创建Casbin服务（需要菜单仓库）
	casbinService, err := permSrv.NewCasbinService("config/rbac_model.conf", b.DB(), adminMenuRepo)
	if err != nil {
		// 如果Casbin服务创建失败，记录错误但不中断整个初始化过程
		// 可以考虑使用日志记录错误
		log.Printf("Failed to create Casbin service: %v", err)
	}

	// 初始化权限服务
	container := b.Services()
	container.AdminUserService = permSrv.NewAdminUserService(adminUserRepo)
	container.AdminMenuService = permSrv.NewAdminMenuService(adminMenuRepo)
	if casbinService != nil {
		container.CasbinService = casbinService
	}
}
