package content_creator

import (
	"frontapi/internal/models/content_creator"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type ContentHeatRepository interface {
	base.ExtendedRepository[content_creator.ContentHeat]
}

type contentHeatRepository struct {
	base.ExtendedRepository[content_creator.ContentHeat]
}

func NewContentHeatRepository(db *gorm.DB) ContentHeatRepository {
	return &contentHeatRepository{
		ExtendedRepository: base.NewExtendedRepository[content_creator.ContentHeat](db),
	}
}
