package cache

import "time"

// BigCacheConfig BigCache配置
type BigCacheConfig struct {
	Shards             int           `mapstructure:"shards"`                // 分片数
	LifeWindow         time.Duration `mapstructure:"life_window"`           // 生命周期窗口
	CleanWindow        time.Duration `mapstructure:"clean_window"`          // 清理窗口
	MaxEntriesInWindow int           `mapstructure:"max_entries_in_window"` // 窗口中最大条目数
	MaxEntrySize       int           `mapstructure:"max_entry_size"`        // 最大条目大小
	HardMaxCacheSize   int           `mapstructure:"hard_max_cache_size"`   // 硬缓存最大大小(MB)
	Prefix             string        `mapstructure:"prefix"`                // 键前缀
}
