---
description: 
globs: backend/src/*.ts,backend/src/*.vue,backend/src/*.js
alwaysApply: false
---
# Backend 模块开发规则模板

## 设计风格核心规范

### 整体架构风格
所有模块必须严格遵循以下架构风格，确保与用户管理模块保持一致：

1. **主页面结构 (index.vue)**
   - 使用 `<div class="app-container">` 作为最外层容器
   - 使用 `<el-card>` 包装整个内容区域
   - header 区域使用 `<template #header>` 定义标题和操作按钮
   - 标题区域使用 `filter-container` 和 `title-container` 类
   - 操作按钮统一放在右侧，使用 Primary、Success、Info 类型

2. **组件导入和结构**
   - 所有子组件必须单独拆分：SearchBar、Table、FormDialog、DetailDialog
   - 使用统一的命名约定：{Entity}SearchBar、{Entity}Table、{Entity}FormDialog、{Entity}DetailDialog
   - 组件导入按功能分组排列

3. **表格组件标准**
   - 必须使用 `SlinkyTable` 组件而非原生 `el-table`
   - 导入路径：`@/components/themes/slinky/tables/SlinkyTable.vue`
   - 必须支持批量操作工具栏
   - 必须包含选择列、序号列、操作列
   - 操作列标准宽度：220px，包含查看、编辑、状态切换、删除操作

## 模块概述模板
```
backend/src/views/{module-name}/{sub-module-name} {模块名称}管理模块，包含{实体}列表展示、搜索、增删改查、状态管理等功能。

主要功能：
- ✅ {实体}列表展示与分页
- ✅ 多条件搜索（关键词、状态、类型、时间范围）
- ✅ {实体}详情查看
- ✅ {实体}新增/编辑/删除
- ✅ {实体}状态管理（启用/禁用/删除）
- ✅ 批量操作（状态更新、删除）
- ✅ 响应式设计
```

## 目录结构模板
```
backend/src/views/{module-name}/{sub-module-name}/
├── index.vue                        # 主页面组件
├── components/
│   ├── {Entity}SearchBar.vue        # {实体}搜索栏组件
│   ├── {Entity}Table.vue           # {实体}表格组件
│   ├── {Entity}FormDialog.vue      # {实体}表单对话框
│   └── {Entity}DetailDialog.vue    # {实体}详情对话框

对应的API和类型文件：
backend/src/service/api/{module-name}/{module-name}.ts    # API接口定义
backend/src/types/{module-name}.ts                       # 类型定义
```

## 类型定义规范模板 (backend/src/types/{module-name}.ts)

### 主实体类型模板
```typescript
export interface {Entity}Item {
    id: string;                    // {实体}ID
    name: string;                  // {实体}名称
    title?: string;                // 标题
    description?: string;          // 描述
    cover?: string;                // 封面图URL
    
    // 状态相关字段
    status: number;                // 状态：0-禁用，1-正常，-4-已删除
    is_featured?: number;          // 是否推荐：0-否，1-是
    is_published?: number;         // 是否发布：0-否，1-是
    
    // 分类和标签
    category_id?: string;          // 分类ID
    category_name?: string;        // 分类名称
    tags?: string[];               // 标签数组
    
    // 统计字段
    view_count?: number;           // 浏览次数
    like_count?: number;           // 点赞数
    comment_count?: number;        // 评论数
    share_count?: number;          // 分享数
    
    // 创建者信息
    creator_id?: string;           // 创建者ID
    creator_name?: string;         // 创建者名称
    creator_avatar?: string;       // 创建者头像
    
    // 时间字段
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
    published_at?: string;         // 发布时间
    
    // 排序字段
    sort_order?: number;           // 排序权重
    
    // 临时字段，不在数据库中存储
    is_liked?: boolean;            // 是否点赞
    is_collected?: boolean;        // 是否收藏
}
```
这个是示例模型，模型定义可以参考接口和，后端models定义，文件frontapi\internal\models\{module-name}\{Entity}.go


### 搜索表单类型模板
```typescript
export interface {Entity}SearchForm {
    keyword: string;               // 关键词搜索（名称/标题）
    status?: number;               // 状态筛选
    category_id?: string;          // 分类筛选
    creator_id?: string;           // 创建者筛选
    is_featured?: number;          // 是否推荐筛选
    is_published?: number;         // 是否发布筛选
    start_date: string;            // 开始时间
    end_date: string;              // 结束时间
}
```

### 子实体类型模板（如评论、分类等）
```typescript
export interface {Entity}Category {
    id: string;                    // 分类ID
    name: string;                  // 分类名称
    code: string;                  // 分类编码
    description?: string;          // 分类描述
    parent_id?: string;            // 父分类ID
    parent_name?: string;          // 父分类名称
    icon?: string;                 // 图标
    cover?: string;                // 封面图
    sort_order: number;            // 排序权重
    status: number;                // 状态：0-禁用，1-启用
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
}

export interface {Entity}Comment {
    id: string;                    // 评论ID
    {entity}_id: string;           // {实体}ID
    {entity}_title?: string;       // {实体}标题
    user_id: string;               // 用户ID
    username?: string;             // 用户名
    user_avatar?: string;          // 用户头像
    content: string;               // 评论内容
    parent_id?: string;            // 父评论ID
    reply_to_user_id?: string;     // 回复用户ID
    reply_to_username?: string;    // 回复用户名
    like_count: number;            // 点赞数
    reply_count: number;           // 回复数
    status: number;                // 状态：0-待审核，1-已通过，-1-已拒绝
    ip_address?: string;           // IP地址
    user_agent?: string;           // 用户代理
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
}
```

## API接口规范模板 (backend/src/service/api/{module-name}/{module-name}.ts)

### 请求参数类型模板
```typescript
// {实体}查询参数接口
interface {Entity}Params {
    page?: {
        pageNo?: number;           // 页码
        pageSize?: number;         // 每页数量
    };
    data?: {
        keyword?: string;          // 关键词
        status?: number;           // 状态
        category_id?: string;      // 分类ID
        creator_id?: string;       // 创建者ID
        is_featured?: number;      // 是否推荐
        is_published?: number;     // 是否发布
        start_date?: string;       // 开始时间
        end_date?: string;         // 结束时间
    }
}

// 分类查询参数
interface {Entity}CategoryParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;          // 分类名称关键词
        status?: number;           // 状态
        parent_id?: string;        // 父分类ID
    }
}

// 评论查询参数
interface {Entity}CommentParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        {entity}_id?: string;      // {实体}ID
        user_id?: string;          // 用户ID
        username?: string;         // 用户名
        keyword?: string;          // 评论内容关键词
        status?: number;           // 状态
        start_date?: string;       // 开始时间
        end_date?: string;         // 结束时间
    }
}
```

### API接口定义模板
```typescript
// ============ {实体}管理API ============

/**
 * 获取{实体}列表
 * @param params 查询参数
 */
export function get{Entity}List(params?: {Entity}Params) {
    return request({
        url: "/{module-name}/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取{实体}详情
 * @param id {实体}ID
 */
export function get{Entity}Detail(id: string) {
    return request({
        url: `/{module-name}/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 新增{实体}
 * @param data {实体}数据
 */
export function add{Entity}(data: Partial<{Entity}Item>) {
    return request({
        url: "/{module-name}/add",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新{实体}
 * @param data {实体}数据
 */
export function update{Entity}(data: Partial<{Entity}Item>) {
    return request({
        url: "/{module-name}/update",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新{实体}状态
 * @param data 状态数据 {id: string, status: number}
 */
export function update{Entity}Status(data: any) {
    return request({
        url: "/{module-name}/update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 批量更新{实体}状态
 * @param data 批量状态数据 {ids: string[], status: number}
 */
export function batchUpdate{Entity}Status(data: any) {
    return request({
        url: "/{module-name}/batch-update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 删除{实体}
 * @param id {实体}ID
 */
export function delete{Entity}(id: string) {
    return request({
        url: `/{module-name}/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 批量删除{实体}
 * @param data 批量删除数据 {ids: string[]}
 */
export function batchDelete{Entity}(data: any) {
    return request({
        url: "/{module-name}/batch-delete",
        method: "post",
        data: { data: data }
    })
}

// ============ {实体}分类管理API ============

/**
 * 获取{实体}分类列表
 */
export function get{Entity}CategoryList(params?: {Entity}CategoryParams) {
    return request({
        url: "/{module-name}/category/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取{实体}分类详情
 */
export function get{Entity}CategoryDetail(id: string) {
    return request({
        url: `/{module-name}/category/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 新增{实体}分类
 */
export function add{Entity}Category(data: Partial<{Entity}Category>) {
    return request({
        url: "/{module-name}/category/add",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新{实体}分类
 */
export function update{Entity}Category(data: Partial<{Entity}Category>) {
    return request({
        url: "/{module-name}/category/update",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新{实体}分类状态
 */
export function update{Entity}CategoryStatus(data: any) {
    return request({
        url: "/{module-name}/category/update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 删除{实体}分类
 */
export function delete{Entity}Category(id: string) {
    return request({
        url: `/{module-name}/category/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

// ============ {实体}评论管理API ============

/**
 * 获取{实体}评论列表
 */
export function get{Entity}CommentList(params?: {Entity}CommentParams) {
    return request({
        url: "/{module-name}/comment/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取{实体}评论详情
 */
export function get{Entity}CommentDetail(id: string) {
    return request({
        url: `/{module-name}/comment/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 更新{实体}评论状态
 */
export function update{Entity}CommentStatus(data: any) {
    return request({
        url: "/{module-name}/comment/update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 删除{实体}评论
 */
export function delete{Entity}Comment(id: string) {
    return request({
        url: `/{module-name}/comment/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}
```

## 请求响应格式规范

### 统一请求格式
```typescript
// 普通请求
{
    data: {
        id: "123456",
        name: "实体名称",
        status: 1
    }
}

// 带分页请求
{
    data: {
        keyword: "搜索关键词",
        status: 1,
        category_id: "category-id"
    },
    page: {
        pageNo: 1,
        pageSize: 10
    }
}
```

### 统一响应格式
```typescript
// 成功响应
{
    code: 2000,
    message: "请求成功",
    data: {
        // 单个数据
        id: "123456",
        name: "实体名称",
        // ...其他字段
    }
}

// 列表响应
{
    code: 2000,
    message: "请求成功",
    data: {
        list: [
            {
                id: "123456",
                name: "实体1",
                // ...其他字段
            }
        ],
        total: 100,
        page: 1,
        pageSize: 10
    }
}

// 错误响应
{
    code: 4001,
    message: "请求参数错误",
    data: null
}
```

## 批量操作工具栏设计标准

### 批量工具栏样式规范
所有表格组件必须包含批量操作工具栏，使用以下标准结构：

```vue
<!-- 批量操作工具栏标准结构 -->
<div v-if="selectedRows.length > 0" class="batch-toolbar">
  <div class="batch-info">
    <el-icon><Check /></el-icon>
    <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
  </div>
  <div class="batch-actions">
    <el-button type="warning" size="small" @click="handleBatchStatus(0)">
      批量禁用
    </el-button>
    <el-button type="success" size="small" @click="handleBatchStatus(1)">
      批量启用
    </el-button>
    <el-button type="danger" size="small" @click="handleBatchDelete">
      批量删除
    </el-button>
  </div>
</div>
```

### 表格列设计标准

1. **信息展示列规范**
   - 主要信息列使用复合结构（头像+文本信息）
   - 状态列使用 `el-tag` 组件统一展示
   - 时间列使用图标+格式化文本
   - 数值列居中对齐，带单位图标

2. **操作列设计标准**
   - 操作列宽度固定为 220px
   - 使用 link 类型按钮，带图标
   - 危险操作必须使用 `el-popconfirm` 确认
   - 按钮顺序：查看、编辑、状态切换、删除

```vue
<!-- 操作列标准结构 -->
<template #actions="{ row }">
  <div class="action-buttons-group" style="min-width: 220px;">
    <el-button type="primary" link size="small" @click="handleView(row)">
      <el-icon><View /></el-icon>
      查看
    </el-button>
    <el-button type="primary" link size="small" @click="handleEdit(row)">
      <el-icon><Edit /></el-icon>
      编辑
    </el-button>
    <el-popconfirm
      :title="`确定要${row.status === 1 ? '禁用' : '启用'}${entityName} ${row.name} 吗？`"
      @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
    >
      <template #reference>
        <el-button 
          :type="row.status === 1 ? 'warning' : 'success'" 
          link 
          size="small"
        >
          <el-icon>
            <component :is="row.status === 1 ? Lock : Unlock" />
          </el-icon>
          {{ row.status === 1 ? '禁用' : '启用' }}
        </el-button>
      </template>
    </el-popconfirm>
    <el-popconfirm
      :title="`确定要删除${entityName} ${row.name} 吗？此操作不可恢复！`"
      @confirm="handleDelete(row)"
    >
      <template #reference>
        <el-button type="danger" link size="small">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </template>
    </el-popconfirm>
  </div>
</template>
```

### SlinkyTable 配置标准
```vue
<SlinkyTable
  :data="{entity}List"
  :loading="loading"
  show-selection
  :show-table-header="true"
  show-index
  show-actions
  :border="true"
  :stripe="true"
  @selection-change="handleSelectionChange"
  @view="handleView"
  @edit="handleEdit"
  @delete="handleDelete"
  action-width="220"
  view-text="查看"
  edit-text="编辑"
  delete-text="删除"
  empty-text="暂无{实体}数据"
  class="{entity}-data-table"
>
```

## 组件开发规范模板

### 主页面组件模板 (index.vue)
```vue
<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>{模块名称}管理</h2>
            <div class="buttons">
              <el-button type="primary" :icon="Plus" @click="handleAdd">添加{实体}</el-button>
              <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
              <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 使用{实体}搜索组件 -->
      <{Entity}SearchBar
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
        @refresh="refreshList"
        class="mb-4"
      />

      <!-- 使用{实体}表格组件 -->
      <{Entity}Table
        :{entity}-list="{entity}List"
        :loading="loading"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @batch-status="handleBatchStatus"
        @batch-delete="handleBatchDelete"
      />

      <!-- 对话框组件 -->
      <{Entity}FormDialog
        :visible="dialogVisible"
        :type="dialogType"
        :{entity}-data="current{Entity}"
        @update:visible="(val: boolean) => dialogVisible = val"
        @success="handleDialogSuccess"
      />

      <{Entity}DetailDialog
        :visible="detailDialogVisible"
        :{entity}-data="current{Entity}"
        @update:visible="(val: boolean) => detailDialogVisible = val"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { get{Entity}List, update{Entity}Status, delete{Entity}, batchUpdate{Entity}Status, batchDelete{Entity} } from '@/service/api/{module-name}/{module-name}';
import type { {Entity}Item } from '@/types/{module-name}';
import { handleApiError } from '@/utils/errorHandler';
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

// 导入{实体}管理组件
import {Entity}DetailDialog from './components/{Entity}DetailDialog.vue';
import {Entity}FormDialog from './components/{Entity}FormDialog.vue';
import {Entity}SearchBar from './components/{Entity}SearchBar.vue';
import {Entity}Table from './components/{Entity}Table.vue';

// 响应式数据
const loading = ref(false);
const {entity}List = ref<{Entity}Item[]>([]);
const selectedRows = ref<{Entity}Item[]>([]);
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const current{Entity} = ref<{Entity}Item | null>(null);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as number | undefined,
  category_id: undefined as string | undefined,
  creator_id: undefined as string | undefined,
  is_featured: undefined as number | undefined,
  start_date: '',
  end_date: ''
});

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 获取{实体}列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      data: {
        keyword: searchForm.keyword,
        status: searchForm.status,
        category_id: searchForm.category_id,
        creator_id: searchForm.creator_id,
        is_featured: searchForm.is_featured,
        start_date: searchForm.start_date,
        end_date: searchForm.end_date
      },
      page: {
        pageNo: pagination.page,
        pageSize: pagination.pageSize
      }
    };

    const {response, data} = await get{Entity}List(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      {entity}List.value = data.list || [];
      pagination.total = data.total || 0;
    } else {
      handleApiError(response.message || '获取{实体}列表失败');
    }
  } catch (error) {
    handleApiError(error, '获取{实体}列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (searchParams?: any) => {
  if (searchParams) {
    Object.assign(searchForm, searchParams);
  }
  pagination.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    category_id: undefined,
    creator_id: undefined,
    is_featured: undefined,
    start_date: '',
    end_date: ''
  });
  pagination.page = 1;
  getList();
};

// 刷新列表
const refreshList = () => {
  getList();
};

// 表格选择变化
const handleSelectionChange = (selection: {Entity}Item[]) => {
  selectedRows.value = selection;
};

// 分页处理
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getList();
};

// 添加{实体}
const handleAdd = () => {
  dialogType.value = 'add';
  current{Entity}.value = null;
  dialogVisible.value = true;
};

// 查看{实体}
const handleView = (row: {Entity}Item) => {
  current{Entity}.value = row;
  detailDialogVisible.value = true;
};

// 编辑{实体}
const handleEdit = (row: {Entity}Item) => {
  dialogType.value = 'edit';
  current{Entity}.value = { ...row };
  dialogVisible.value = true;
};

// 删除{实体}
const handleDelete = async (row: {Entity}Item) => {
  try {
    const {response} = await delete{Entity}(row.id) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('删除{实体}成功');
      getList();
    } else {
      handleApiError(response.message || '删除{实体}失败');
    }
  } catch (error) {
    handleApiError(error, '删除{实体}失败');
  }
};

// 更改{实体}状态
const handleChangeStatus = async (id: string, status: number) => {
  try {
    const params = { id, status };
    const {response, data} = await update{Entity}Status(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}{实体}成功`);
      getList();
    } else {
      handleApiError(response.message || '更新{实体}状态失败');
    }
  } catch (error) {
    handleApiError(error, '更新{实体}状态失败');
  }
};

// 导出{实体}数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

// 批量更新{实体}状态
const handleBatchStatus = async (status: number, {entity}s: {Entity}Item[]) => {
  try {
    const params = { ids: {entity}s.map({entity} => {entity}.id), status };
    const {response, data} = await batchUpdate{Entity}Status(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}{实体}成功`);
      getList();
    } else {
      handleApiError(response.message || '更新{实体}状态失败');
    }
  } catch (error) {
    handleApiError(error, '更新{实体}状态失败');
  }
};

// 批量删除{实体}
const handleBatchDelete = async ({entity}s: {Entity}Item[]) => {
  try {
    const params = { ids: {entity}s.map({entity} => {entity}.id) };
    const {response, data} = await batchDelete{Entity}(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('批量删除{实体}成功');
      getList();  
      selectedRows.value = [];
    } else {
      handleApiError(response.message || '批量删除{实体}失败');
    }
  } catch (error) {
    handleApiError(error, '批量删除{实体}失败');
  }
};

// 对话框操作成功
const handleDialogSuccess = () => {
  getList();
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.title-container h2 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .title-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    justify-content: center;
  }
}
</style>
```

### 表格组件模板 ({Entity}Table.vue)
```vue
<template>
  <div class="{entity}-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量禁用
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量启用
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 - 使用SlinkyTable -->
    <div class="table-content">
      <SlinkyTable
        :data="{entity}List"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="编辑"
        delete-text="删除"
        empty-text="暂无{实体}数据"
        class="{entity}-data-table"
      >
        <!-- 主要信息列 -->
        <el-table-column prop="name" label="{实体}信息" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="{entity}-info">
              <el-avatar
                :size="36"
                :src="row.cover"
                :alt="row.name"
                class="{entity}-avatar"
              >
                <el-icon><{EntityIcon} /></el-icon>
              </el-avatar>
              <div class="{entity}-details">
                <div class="{entity}-name">{{ row.name }}</div>
                <div class="{entity}-description">{{ row.description || '未设置描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 分类列 -->
        <el-table-column prop="category_name" label="分类" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.category_name" type="info" size="small" effect="light">
              {{ row.category_name }}
            </el-tag>
            <span v-else class="placeholder-text">未分类</span>
          </template>
        </el-table-column>

        <!-- 统计信息列 -->
        <el-table-column label="统计" min-width="120" align="center">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon class="stat-icon"><View /></el-icon>
                <span class="stat-value">{{ row.view_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><StarFilled /></el-icon>
                <span class="stat-value">{{ row.like_count || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <{Entity}StatusTag :status="row.status" />
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ formatDate(row.created_at) }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-popconfirm
              :title="`确定要${row.status === 1 ? '禁用' : '启用'}{实体} ${row.name} 吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  link 
                  size="small"
                >
                  <el-icon>
                    <component :is="row.status === 1 ? Lock : Unlock" />
                  </el-icon>
                  {{ row.status === 1 ? '禁用' : row.status === 0 ? '启用' : '已删除' }}
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              :title="`确定要删除{实体} ${row.name} 吗？此操作不可恢复！`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import SlinkyTable from '@/components/themes/slinky/tables/SlinkyTable.vue';
import {Entity}StatusTag from '@/components/common/{Entity}StatusTag.vue';
import { 
  Check, Calendar, Delete, Edit, Lock, Unlock, View, 
  StarFilled, {EntityIcon} 
} from '@element-plus/icons-vue';
import { formatDate } from '@/utils/date';
import type { {Entity}Item } from '@/types/{module-name}';

// Props定义
interface Props {
  {entity}List: {Entity}Item[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'selection-change': [selection: {Entity}Item[]];
  'view': [row: {Entity}Item];
  'edit': [row: {Entity}Item];
  'delete': [row: {Entity}Item];
  'change-status': [id: string, status: number];
  'current-change': [page: number];
  'size-change': [size: number];
  'batch-status': [status: number];
  'batch-delete': [];
}

const emit = defineEmits<Emits>();

// 选中的行
const selectedRows = ref<{Entity}Item[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: {Entity}Item[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

// 查看
const handleView = (row: {Entity}Item) => {
  emit('view', row);
};

// 编辑
const handleEdit = (row: {Entity}Item) => {
  emit('edit', row);
};

// 删除
const handleDelete = (row: {Entity}Item) => {
  emit('delete', row);
};

// 状态变化
const handleChangeStatus = (id: string, status: number) => {
  emit('change-status', id, status);
};

// 分页变化
const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 批量状态更新
const handleBatchStatus = (status: number) => {
  emit('batch-status', status);
};

// 批量删除
const handleBatchDelete = () => {
  emit('batch-delete');
};
</script>

<style scoped>
.{entity}-table-container {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-weight: 500;
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-content {
    background: white;
    border-radius: 4px;
    overflow: hidden;
  }

  .{entity}-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .{entity}-details {
      min-width: 0;
      flex: 1;

      .{entity}-name {
        font-weight: 500;
        color: #333;
      }

      .{entity}-description {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .stats-info {
    display: flex;
    gap: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .stat-icon {
        color: #999;
        font-size: 14px;
      }

      .stat-value {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .time-icon {
      color: #999;
      font-size: 14px;
    }

    .time-text {
      font-size: 12px;
      color: #666;
    }
  }

  .action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
  }

  .placeholder-text {
    color: #999;
    font-size: 12px;
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
```

### 搜索栏组件模板 ({Entity}SearchBar.vue)
```vue
<template>
  <div class="{entity}-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item label="名称/标题">
        <el-input 
          v-model="searchForm.keyword" 
          placeholder="请输入{实体}名称或标题" 
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="状态">
        <el-select 
          v-model="searchForm.status" 
          placeholder="请选择状态" 
          clearable 
          style="width: 180px;"
        >
          <el-option label="正常" :value="1" />
          <el-option label="禁用" :value="0" />
          <el-option label="已删除" :value="-4" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="分类">
        <el-select 
          v-model="searchForm.category_id" 
          placeholder="请选择分类" 
          clearable 
          style="width: 180px;"
        >
          <el-option 
            v-for="category in categoryOptions"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="是否推荐">
        <el-select 
          v-model="searchForm.is_featured" 
          placeholder="请选择" 
          clearable 
          style="width: 150px;"
        >
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="开始时间">
        <el-date-picker
          v-model="searchForm.start_date"
          type="date"
          placeholder="开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 150px;"
        />
      </el-form-item>
      
      <el-form-item label="结束时间">
        <el-date-picker
          v-model="searchForm.end_date"
          type="date"
          placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 150px;"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><RefreshRight /></el-icon>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive, ref, onMounted, watch } from 'vue';

// 搜索表单类型定义
export interface {Entity}SearchForm {
  keyword: string;
  status?: number;
  category_id?: string;
  creator_id?: string;
  is_featured?: number;
  start_date: string;
  end_date: string;
}

// Props
interface Props {
  modelValue?: {Entity}SearchForm;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    keyword: '',
    status: undefined,
    category_id: undefined,
    creator_id: undefined,
    is_featured: undefined,
    start_date: '',
    end_date: '',
  })
});

// Emits
interface Emits {
  search: [params: {Entity}SearchForm];
  reset: [];
  refresh: [];
  'update:modelValue': [value: {Entity}SearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<{Entity}SearchForm>({ ...props.modelValue });

// 分类选项（根据实际需要获取）
const categoryOptions = ref([]);

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    category_id: undefined,
    creator_id: undefined,
    is_featured: undefined,
    start_date: '',
    end_date: '',
  });
  emit('reset');
};

// 刷新
const handleRefresh = () => {
  emit('refresh');
};

// 获取分类选项
const getCategoryOptions = async () => {
  // 根据实际需要实现
  // const categories = await get{Entity}CategoryList();
  // categoryOptions.value = categories.data.list || [];
};

// 初始化
onMounted(() => {
  getCategoryOptions();
});
</script>

<style scoped>
.{entity}-search-bar {
  margin-bottom: 16px;
}

.{entity}-search-bar .el-form {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.{entity}-search-bar .el-form-item {
  margin-bottom: 8px;
}
</style>
```

## 状态管理规范模板

### 通用状态定义
```typescript
// 通用状态
export enum CommonStatus {
  DISABLED = 0,     // 禁用
  ENABLED = 1,      // 启用/正常
  DELETED = -4      // 已删除（软删除）
}

// 发布状态
export enum PublishStatus {
  DRAFT = 0,        // 草稿
  PUBLISHED = 1,    // 已发布
  SCHEDULED = 2     // 定时发布
}

// 审核状态
export enum ReviewStatus {
  PENDING = 0,      // 待审核
  APPROVED = 1,     // 已通过
  REJECTED = -1     // 已拒绝
}

// 推荐状态
export enum FeaturedStatus {
  NORMAL = 0,       // 普通
  FEATURED = 1      // 推荐
}
```

## 错误处理规范

### 统一错误处理
```typescript
import { handleApiError } from '@/utils/errorHandler';

// 在catch块中使用
catch (error) {
  handleApiError(error, '操作失败');
}
```

### 响应状态检查
```typescript
// 检查响应状态
if (response.status === 200 && response.data.code === 2000) {
  // 成功处理
} else {
  // 错误处理
  handleApiError(response.message || '操作失败');
}
```

## 分页处理规范

### 分页数据结构
```typescript
const pagination = reactive({
  page: 1,        // 当前页码
  pageSize: 10,   // 每页数量
  total: 0        // 总数量
});
```

### 分页事件处理
```typescript
// 页码变化
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getList();
};

// 每页数量变化
const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getList();
};
```

## 组件通信规范

### Props定义模板
```typescript
interface Props {
  {entity}List: {Entity}Item[];     // {实体}列表
  loading: boolean;                 // 加载状态
  pagination: PaginationData;       // 分页数据
}
```

### Emits定义模板
```typescript
interface Emits {
  'selection-change': [selection: {Entity}Item[]];      // 选择变化
  'view': [row: {Entity}Item];                          // 查看
  'edit': [row: {Entity}Item];                          // 编辑
  'delete': [row: {Entity}Item];                        // 删除
  'change-status': [id: string, status: number];       // 状态变化
  'current-change': [page: number];                     // 页码变化
  'size-change': [size: number];                        // 每页数量变化
  'batch-status': [status: number, {entity}s: {Entity}Item[]];  // 批量状态
  'batch-delete': [{entity}s: {Entity}Item[]];         // 批量删除
}
```

## 样式规范模板

### 布局样式标准
```scss
.app-container {
  padding: 20px;
  min-height: calc(100vh - 84px);
}

.filter-container {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px 0;
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 搜索栏样式标准 */
.{entity}-search-bar {
  margin-bottom: 16px;

  .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}

/* 批量工具栏样式标准 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  margin-bottom: 16px;

  .batch-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1976d2;
    font-weight: 500;

    strong {
      color: #1565c0;
    }
  }

  .batch-actions {
    display: flex;
    gap: 8px;
  }
}

/* 操作按钮样式标准 */
.action-buttons-group {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;

  .el-button {
    padding: 4px 8px;
    height: auto;
    
    .el-icon {
      margin-right: 2px;
    }
  }
}

/* 信息展示列样式标准 */
.{entity}-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .{entity}-details {
    min-width: 0;
    flex: 1;

    .{entity}-name {
      font-weight: 500;
      color: #333;
      line-height: 1.4;
    }

    .{entity}-description {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

/* 占位文本样式标准 */
.placeholder-text {
  color: #999;
  font-size: 12px;
  font-style: italic;
}
```

### 响应式设计标准
```scss
@media (max-width: 1200px) {
  .action-buttons-group {
    flex-direction: column;
    gap: 4px;
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .title-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .buttons {
    justify-content: center;
  }

  .batch-toolbar {
    flex-direction: column;
    gap: 12px;
    text-align: center;

    .batch-actions {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: 8px;
  }
}
```

## 开发注意事项

1. **模块命名**: 使用统一的命名规范，如 `{module-name}` 和 `{Entity}`
2. **数据一致性**: 确保前端状态与后端数据保持一致
3. **错误处理**: 所有API调用都必须进行错误处理
4. **加载状态**: 在数据请求期间显示加载状态
5. **用户体验**: 操作成功后给予用户明确的反馈
6. **权限控制**: 根据用户权限显示/隐藏操作按钮
7. **数据验证**: 在提交前进行必要的数据验证
8. **响应式设计**: 确保在不同设备上的良好显示效果
9. **性能优化**: 合理使用分页，避免一次性加载大量数据

## 使用模板说明

### 核心设计风格强制要求

使用此模板时，以下设计风格要素为**强制性要求**，必须严格遵循：

1. **SlinkyTable 组件使用**
   - 所有表格必须使用 `SlinkyTable` 而非原生 `el-table`
   - 导入路径：`@/components/themes/slinky/tables/SlinkyTable.vue`
   - 必须配置标准属性：`show-selection`, `show-index`, `show-actions`, `border`, `stripe`

2. **批量操作工具栏**
   - 必须包含批量操作功能
   - 工具栏背景色：`#f0f9ff`，边框色：`#e1f5fe`
   - 选择信息格式：`已选择 <strong>{{ count }}</strong> 项`
   - 标准操作：批量禁用、批量启用、批量删除

3. **操作列标准布局**
   - 操作列固定宽度：220px
   - 按钮顺序：查看、编辑、状态切换、删除
   - 使用 `link` 类型按钮带图标
   - 危险操作必须使用 `el-popconfirm` 确认

4. **页面结构规范**
   - 最外层容器：`<div class="app-container">`
   - 内容包装器：`<el-card>`
   - 标题区域：`<template #header>` with `filter-container` 和 `title-container` 类

5. **搜索栏样式标准**
   - 背景色：`#f8f9fa`，边框：`1px solid #e4e7ed`
   - 内边距：16px，圆角：4px
   - 表单项间距：8px

### 替换占位符

在使用此模板时，需要替换以下占位符：

- `{module-name}`: 模块名称（如：users, videos, posts）
- `{Entity}`: 实体名称（首字母大写，如：User, Video, Post）
- `{entity}`: 实体名称（首字母小写，如：user, video, post）
- `{实体}`: 中文实体名称（如：用户, 视频, 帖子）
- `{模块名称}`: 中文模块名称（如：用户管理, 视频管理, 帖子管理）
- `{EntityIcon}`: 实体对应图标（如：User, Video, Document）

### 示例替换

如果要创建视频管理模块：
- `{module-name}` → `videos`
- `{Entity}` → `Video`
- `{entity}` → `video`
- `{实体}` → `视频`
- `{模块名称}` → `视频管理`
- `{EntityIcon}` → `VideoCamera`

### 生成新模块的步骤

1. **准备阶段**
   - 复制此模板文档
   - 确定模块名称和实体名称
   - 准备占位符替换对照表

2. **创建文件结构**
   ```
   backend/src/views/{module-name}/{sub-module-name}/
   ├── index.vue                        # 主页面组件
   ├── components/
   │   ├── {Entity}SearchBar.vue        # 搜索栏组件
   │   ├── {Entity}Table.vue           # 表格组件
   │   ├── {Entity}FormDialog.vue      # 表单对话框
   │   └── {Entity}DetailDialog.vue    # 详情对话框
   ```

3. **替换和定制**
   - 批量替换所有占位符
   - 根据实体特点调整表格列配置
   - 添加特定的搜索字段
   - 配置对应的API接口

4. **验证风格一致性**
   - 检查是否使用了 SlinkyTable 组件
   - 确认批量操作工具栏样式正确
   - 验证操作列布局符合标准
   - 测试响应式布局效果

5. **完善功能**
   - 实现具体的业务逻辑
   - 添加数据验证
   - 配置权限控制
   - 编写单元测试

### 设计风格检查清单

在完成模块开发后，请使用以下清单验证设计风格一致性：

- [ ] 使用了 SlinkyTable 组件
- [ ] 批量操作工具栏样式正确（背景色 #f0f9ff）
- [ ] 操作列宽度为 220px，包含标准操作按钮
- [ ] 页面使用 app-container 和 el-card 结构
- [ ] 搜索栏使用标准背景色和样式
- [ ] 所有按钮使用合适的类型和图标
- [ ] 信息展示列使用复合结构（头像+详情）
- [ ] 时间和统计信息使用图标+文本格式
- [ ] 状态列使用 el-tag 组件
- [ ] 响应式设计适配移动端

### 注意事项

1. **严格遵循设计风格**：不允许随意修改批量工具栏、操作列等标准组件的样式
2. **保持组件一致性**：所有模块的同类组件应使用相同的结构和样式
3. **响应式考虑**：确保在不同屏幕尺寸下都有良好的显示效果
4. **性能优化**：合理使用分页，避免一次性加载大量数据
5. **用户体验**：操作反馈及时，加载状态明确

这个模板为backend模块开发提供了标准化的结构和严格的设计风格规范，确保所有模块的界面风格完全一致。


### 前端Vue3代码规范
- 使用组件化开发方式，每个组件有单一职责
- 使用TypeScript进行类型检查
- 视图与业务逻辑分离，使用Pinia/Vuex管理状态
- API接口统一在api目录下定义
- 使用Vue Router管理路由
- 遵循一致的CSS命名规范(如BEM)

## 开发流程

### 功能开发流程
1. 在models层定义数据模型
2. 在repository层实现数据访问方法
3. 在service层实现业务逻辑
4. 在admin层实现HTTP接口
5. 在routes层注册路由
6. 在前端api中定义接口
7. 在前端视图中实现界面

### 代码审查要点
- 代码是否符合项目架构规范
- 是否存在安全隐患
- 是否处理了所有可能的错误
- 是否有足够的测试覆盖
- 性能是否满足要求
- 是否遵循了既定的代码风格

## 通用开发准则
- 保持简单，避免过度设计
- 编写可测试的代码
- 关注性能和安全性
- 代码应该自文档化
- 遵循最小权限原则
- 定期进行代码重构