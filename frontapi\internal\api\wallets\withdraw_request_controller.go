package wallets

import (
	service "frontapi/internal/service/wallets"
)

// WithdrawRequestController 提现申请控制器
type WithdrawRequestController struct {
	withdrawRequestService service.WithdrawRequestService
}

// NewWithdrawRequestController 创建提现申请控制器实例
func NewWithdrawRequestController(withdrawRequestService service.WithdrawRequestService) *WithdrawRequestController {
	return &WithdrawRequestController{withdrawRequestService: withdrawRequestService}
}
