<template>
    <div>
      <div class="drawer-container">
        <Sidebar v-model:visible="showDrawer" :position="drawerPosition" class="app-drawer">
          <template #header>
            <div class="drawer-header">
              <div class="logo-section">
                <img src="@/assets/icons/logo.svg" alt="MyFirm Logo" class="logo-image" />
                <span class="site-name">{{ $t('common.siteName') }}</span>
              </div>
            </div>
          </template>
          
          <div class="drawer-content">
            <!-- 搜索框 - 已重构为全圆角样式 -->
            <div class="drawer-search-section">
              <div class="search-input-wrapper">
                <InputText
                  v-model="searchQuery"
                  :placeholder="$t('common.search')"
                  class="drawer-search-input"
                  @keyup.enter="handleSearch"
                />
                <Button
                  class="drawer-search-btn"
                  @click="handleSearch"
                  text
                  rounded
                >
                  <i class="pi pi-search"></i>
                </Button>
              </div>
            </div>
            
            <!-- 导航菜单 -->
            <div class="drawer-menu-items">
              <div
                v-for="item in menuItems"
                :key="item.label"
                class="drawer-menu-item"
                :class="{ 'has-submenu': item.items }"
                @click="handleMenuItemClick(item)"
                @mouseenter="handleMenuItemHover(item)"
                @mouseleave="handleMenuItemLeave(item)"
              >
                <div class="menu-item-content" :class="{ 'active': isActiveRoute(item.route) }">
                  <span class="menu-icon">
                    <i :class="item.icon"></i>
                  </span>
                  <span class="menu-label">{{ t(item.label as string) }}</span>
                  <i v-if="item.items" class="pi pi-angle-right submenu-indicator"></i>
                </div>

                <!-- 弹窗子菜单 -->
                <div
                  v-if="item.items && hoveredItem === item.label"
                  class="drawer-submenu-popover"
                  :class="[
                    `position-${drawerPosition}`,
                    { 'show': hoveredItem === item.label }
                  ]"
                  @mouseenter="keepSubmenuOpen(item)"
                  @mouseleave="handleSubmenuLeave(item)"
                >
                  <div class="submenu-content">
                    <div class="submenu-header">
                      <span class="submenu-title">{{ t(item.label as string) }}</span>
                    </div>
                    <div class="submenu-items">
                      <div
                        v-for="subItem in item.items"
                        :key="subItem.label"
                        class="drawer-submenu-item"
                        :class="{ 'active': isActiveRoute(subItem.route) }"
                        @click.stop="handleSubMenuItemClick(subItem)"
                      >
                        <span class="submenu-icon">
                          <i :class="subItem.icon"></i>
                        </span>
                        <span class="submenu-label">{{ t(subItem.label as string) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 工具栏 -->
            <div class="drawer-tools">
              <div class="tool-section">
                <h4>{{ $t('common.settings') }}</h4>
                <div class="tool-items">
                  <NavThemeSelector class="drawer-tool-item" />
                  <NavLanguageSelector class="drawer-tool-item" />
                </div>
              </div>
            </div>
          </div>
        </Sidebar>
      </div>
    </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Sidebar from 'primevue/sidebar';
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import MegaMenu from 'primevue/megamenu';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    position: {
        type: String,
        default: 'left',
        validator: (value: string) => ['left', 'right'].includes(value)
    }
});

const emit = defineEmits(['update:visible', 'close']);

const { t } = useTranslation();
const route = useRoute();
const router = useRouter();

// 响应式数据
const showDrawer = ref(props.visible);
const drawerPosition = ref(props.position || 'left');
const searchQuery = ref('');
const hoveredItem = ref('');
const submenuTimer = ref<NodeJS.Timeout | null>(null);

// 监听props变化
watch(() => props.visible, (newValue) => {
    showDrawer.value = newValue;
});

watch(() => props.position, (newValue) => {
    if (newValue && ['left', 'right'].includes(newValue)) {
        drawerPosition.value = newValue;
    }
});

// 监听抽屉状态变化
watch(() => showDrawer.value, (newValue) => {
    emit('update:visible', newValue);
});

// 关闭抽屉
const closeDrawer = () => {
    showDrawer.value = false;
    emit('close');
};

// 切换抽屉位置
const toggleDrawerPosition = () => {
    drawerPosition.value = drawerPosition.value === 'left' ? 'right' : 'left';
};

// 搜索处理
const handleSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        closeDrawer();
    }
};

// 菜单项点击处理
const handleMenuItemClick = (item: any) => {
    if (item.items) {
        // 对于有子菜单的项目，点击时切换悬停状态
        hoveredItem.value = hoveredItem.value === item.label ? '' : item.label;
    } else if (item.route) {
        // 导航到路由
        router.push(item.route);
        closeDrawer();
    }
};

// 菜单项悬停处理
const handleMenuItemHover = (item: any) => {
    if (item.items) {
        // 清除之前的定时器
        if (submenuTimer.value) {
            clearTimeout(submenuTimer.value);
            submenuTimer.value = null;
        }
        hoveredItem.value = item.label;
    }
};

// 菜单项离开处理
const handleMenuItemLeave = (item: any) => {
    if (item.items) {
        // 延迟隐藏子菜单，给用户时间移动到子菜单
        submenuTimer.value = setTimeout(() => {
            if (hoveredItem.value === item.label) {
                hoveredItem.value = '';
            }
        }, 300);
    }
};

// 保持子菜单打开
const keepSubmenuOpen = (item: any) => {
    if (submenuTimer.value) {
        clearTimeout(submenuTimer.value);
        submenuTimer.value = null;
    }
    hoveredItem.value = item.label;
};

// 子菜单离开处理
const handleSubmenuLeave = (item: any) => {
    submenuTimer.value = setTimeout(() => {
        if (hoveredItem.value === item.label) {
            hoveredItem.value = '';
        }
    }, 200);
};

// 子菜单项点击处理
const handleSubMenuItemClick = (item: any) => {
    if (item.route) {
        router.push(item.route);
        closeDrawer();
    }
};

// 判断路由是否激活
const isActiveRoute = (routePath: string | undefined) => {
    if (!routePath) return false;
    return route.path === routePath || route.path.startsWith(routePath + '/');
};

// 暴露方法给父组件
defineExpose({
    toggleDrawer: (position?: 'left' | 'right') => {
        if (position) {
            drawerPosition.value = position;
        }
        showDrawer.value = !showDrawer.value;
    },
    closeDrawer,
    toggleDrawerPosition
});
</script>

<style scoped lang="scss">
.drawer-container {
  .app-drawer {
    width: 280px;
    
    :deep(.p-sidebar-header) {
      padding: 0;
      border-bottom: 1px solid var(--surface-border);
    }
    
    :deep(.p-sidebar-content) {
      padding: 0;
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  
  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .logo-image {
      width: 32px;
      height: 32px;
    }
    
    .site-name {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-color);
    }
  }
  
  .close-button {
    width: 2rem;
    height: 2rem;
  }
}

.drawer-content {
  .drawer-search-section {
    padding:0.5rem 1px;
    .search-input-wrapper {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 25px;
      border: 1px solid var(--surface-border);
      box-shadow: none;
      &:hover{
        border: 1px solid var(--primary-color);
        background: var(--surface-overlay);
      }

      .drawer-search-input {
        width: 100%;
        padding: 0.75rem 3.5rem 0.75rem 1.25rem;
        border:0;
        outline: none;
        background: transparent;
        box-shadow: none;
        &:focus{
          border: 0;
          background: transparent;
        }
        &:hover{
          border: 0;
          background: transparent;
        }
      }
      .drawer-search-btn {
        margin-right: 4px;
      }
    }
  }
  
  .drawer-menu-items {
    .drawer-menu-item {
      position: relative;
      border-bottom: 1px solid var(--surface-border);

      &.has-submenu {
        .menu-item-content {
          &:hover {
            background: var(--surface-hover);
          }
        }
      }

      .menu-item-content {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--surface-hover);
        }

        &.active {
          background: var(--primary-50);
          color: var(--primary-color);
          font-weight: 500;

          .menu-icon i {
            color: var(--primary-color);
          }
        }

        .menu-icon {
          width: 24px;
          margin-right: 0.75rem;
          color: var(--text-color-secondary);

          i {
            font-size: 1.1rem;
          }
        }

        .menu-label {
          flex: 1;
          font-weight: 500;
          color: var(--text-color);
        }

        .submenu-indicator {
          color: var(--text-color-secondary);
          font-size: 0.8rem;
          transition: all 0.2s ease;
        }
      }

      // 弹窗子菜单样式
      .drawer-submenu-popover {
        position: absolute;
        top: 0;
        z-index: 9999;
        min-width: 220px;
        max-width: 280px;
        background: var(--surface-overlay);
        border: 1px solid var(--surface-border);
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        opacity: 0;
        visibility: hidden;
        transform: translateX(-10px);
        transition: all 0.2s ease;

        &.show {
          opacity: 1;
          visibility: visible;
          transform: translateX(0);
        }

        // 左侧抽屉时，子菜单显示在右侧
        &.position-left {
          left: calc(100% + 8px);
          transform: translateX(-10px);

          &.show {
            transform: translateX(0);
          }
        }

        // 右侧抽屉时，子菜单显示在左侧
        &.position-right {
          right: calc(100% + 8px);
          transform: translateX(10px);

          &.show {
            transform: translateX(0);
          }
        }

        .submenu-content {
          padding: 0.5rem 0;

          .submenu-header {
            padding: 0.75rem 1rem 0.5rem;
            border-bottom: 1px solid var(--surface-border);
            margin-bottom: 0.25rem;

            .submenu-title {
              font-size: 0.85rem;
              font-weight: 600;
              color: var(--text-color);
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
          }

          .submenu-items {
            .drawer-submenu-item {
              display: flex;
              align-items: center;
              padding: 0.75rem 1rem;
              cursor: pointer;
              transition: background 0.2s ease;

              &:hover {
                background: var(--surface-hover);
              }

              &.active {
                background: var(--primary-50);
                color: var(--primary-color);

                .submenu-icon i {
                  color: var(--primary-color);
                }
              }

              .submenu-icon {
                width: 20px;
                margin-right: 0.75rem;
                color: var(--text-color-secondary);

                i {
                  font-size: 0.9rem;
                }
              }

              .submenu-label {
                color: var(--text-color);
                font-size: 0.9rem;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
  
  .drawer-tools {
    padding: 1rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 1rem;
    
    .tool-section {
      h4 {
        margin: 0 0 1rem 0;
        color: var(--text-color);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .tool-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }
    }
  }
}

// 深色模式样式
.sof-dark {
  .drawer-menu-item {
    .menu-item-content.active {
      background: var(--surface-600) !important;
    }
  }
  
  .drawer-submenu-item.active {
    background: var(--surface-600) !important;
  }
}
</style>