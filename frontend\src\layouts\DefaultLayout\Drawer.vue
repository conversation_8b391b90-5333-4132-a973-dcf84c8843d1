<template>
    <div>
      <div class="drawer-container">
        <Sidebar v-model:visible="showDrawer" :position="drawerPosition" class="app-drawer">
          <template #header>
            <div class="drawer-header">
              <div class="logo-section">
                <img src="@/assets/icons/logo.svg" alt="MyFirm Logo" class="logo-image" />
                <span class="site-name">{{ $t('common.siteName') }}</span>
              </div>
            </div>
          </template>
          
          <div class="drawer-content">
            <!-- 搜索框 -->
            <div class="drawer-search-section">
                <InputText 
                  v-model="searchQuery" 
                  :placeholder="$t('common.search')" 
                  class="drawer-search-input"
                  @keyup.enter="handleSearch"
                />
                <Button class="drawer-search-btn" @click="handleSearch">
                  <i class="pi pi-search"></i>
                </Button>
            </div>
            
            <!-- 导航菜单 -->
            <div class="drawer-menu-items">
              <div 
                v-for="item in menuItems" 
                :key="item.label"
                class="drawer-menu-item"
                @click="handleMenuItemClick(item)"
              >
                <div class="menu-item-content" :class="{ 'active': isActiveRoute(item.route) }">
                  <span class="menu-icon">
                    <i :class="item.icon"></i>
                  </span>
                  <span class="menu-label">{{ t(item.label as string) }}</span>
                  <i v-if="item.items" class="pi pi-angle-down submenu-indicator" :class="{ 'expanded': expandedItem === item.label }"></i>
                </div>
                
                <!-- 子菜单 -->
                <div v-if="item.items && expandedItem === item.label" class="drawer-submenu">
                  <div 
                    v-for="subItem in item.items" 
                    :key="subItem.label"
                    class="drawer-submenu-item"
                    :class="{ 'active': isActiveRoute(subItem.route) }"
                    @click.stop="handleSubMenuItemClick(subItem)"
                  >
                    <span class="submenu-icon">
                      <i :class="subItem.icon"></i>
                    </span>
                    <span class="submenu-label">{{ t(subItem.label as string) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 工具栏 -->
            <div class="drawer-tools">
              <div class="tool-section">
                <h4>{{ $t('common.settings') }}</h4>
                <div class="tool-items">
                  <NavThemeSelector class="drawer-tool-item" />
                  <NavLanguageSelector class="drawer-tool-item" />
                </div>
              </div>
            </div>
          </div>
        </Sidebar>
      </div>
    </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import InputText from 'primevue/inputtext';
import Sidebar from 'primevue/sidebar';
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    position: {
        type: String,
        default: 'left',
        validator: (value: string) => ['left', 'right'].includes(value)
    }
});

const emit = defineEmits(['update:visible', 'close']);

const { t } = useTranslation();
const route = useRoute();
const router = useRouter();

// 响应式数据
const showDrawer = ref(props.visible);
const drawerPosition = ref(props.position || 'left');
const searchQuery = ref('');
const expandedItem = ref('');

// 监听props变化
watch(() => props.visible, (newValue) => {
    showDrawer.value = newValue;
});

watch(() => props.position, (newValue) => {
    if (newValue && ['left', 'right'].includes(newValue)) {
        drawerPosition.value = newValue;
    }
});

// 监听抽屉状态变化
watch(() => showDrawer.value, (newValue) => {
    emit('update:visible', newValue);
});

// 关闭抽屉
const closeDrawer = () => {
    showDrawer.value = false;
    emit('close');
};

// 切换抽屉位置
const toggleDrawerPosition = () => {
    drawerPosition.value = drawerPosition.value === 'left' ? 'right' : 'left';
};

// 搜索处理
const handleSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        closeDrawer();
    }
};

// 菜单项点击处理
const handleMenuItemClick = (item: any) => {
    if (item.items) {
        // 切换子菜单展开状态
        expandedItem.value = expandedItem.value === item.label ? '' : item.label;
    } else if (item.route) {
        // 导航到路由
        router.push(item.route);
        closeDrawer();
    }
};

// 子菜单项点击处理
const handleSubMenuItemClick = (item: any) => {
    if (item.route) {
        router.push(item.route);
        closeDrawer();
    }
};

// 判断路由是否激活
const isActiveRoute = (routePath: string | undefined) => {
    if (!routePath) return false;
    return route.path === routePath || route.path.startsWith(routePath + '/');
};

// 暴露方法给父组件
defineExpose({
    toggleDrawer: (position?: 'left' | 'right') => {
        if (position) {
            drawerPosition.value = position;
        }
        showDrawer.value = !showDrawer.value;
    },
    closeDrawer,
    toggleDrawerPosition
});
</script>

<style scoped lang="scss">
.drawer-container {
  .app-drawer {
    width: 280px;
    
    :deep(.p-sidebar-header) {
      padding: 0;
      border-bottom: 1px solid var(--surface-border);
    }
    
    :deep(.p-sidebar-content) {
      padding: 0;
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  
  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .logo-image {
      width: 32px;
      height: 32px;
    }
    
    .site-name {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-color);
    }
  }
  
  .close-button {
    width: 2rem;
    height: 2rem;
  }
}

.drawer-content {
  .drawer-search-section {
    display: flex;
    position: relative;
    padding: 0.1rem 0;
    .drawer-search-input {
      border-radius: 20px;
      padding-left: 1.5rem;
      width: 100%;
    }
    .drawer-search-btn{
        border-radius: 20px;
        position: relative;
        left: -40px;
     }
  }
  
  .drawer-menu-items {
    .drawer-menu-item {
      border-bottom: 1px solid var(--surface-border);
      
      .menu-item-content {
        display: flex;
        align-items: center;
        padding: 1rem;
        cursor: pointer;
        transition: background 0.2s ease;
        
        &:hover {
          background: var(--surface-hover);
        }
        
        &.active {
          background: var(--primary-50);
          color: var(--primary-color);
          font-weight: 500;
          
          .menu-icon i {
            color: var(--primary-color);
          }
        }
        
        .menu-icon {
          width: 24px;
          margin-right: 0.75rem;
          color: var(--text-color-secondary);
          
          i {
            font-size: 1.1rem;
          }
        }
        
        .menu-label {
          flex: 1;
          font-weight: 500;
          color: var(--text-color);
        }
        
        .submenu-indicator {
          color: var(--text-color-secondary);
          font-size: 0.9rem;
          transition: transform 0.2s ease;
          
          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
      
      .drawer-submenu {
        background: var(--surface-ground);
        
        .drawer-submenu-item {
          display: flex;
          align-items: center;
          padding: 0.75rem 1rem 0.75rem 3rem;
          cursor: pointer;
          transition: background 0.2s ease;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            
            .submenu-icon i {
              color: var(--primary-color);
            }
          }
          
          .submenu-icon {
            width: 20px;
            margin-right: 0.5rem;
            color: var(--text-color-secondary);
            
            i {
              font-size: 0.9rem;
            }
          }
          
          .submenu-label {
            color: var(--text-color);
            font-size: 0.9rem;
          }
        }
      }
    }
  }
  
  .drawer-tools {
    padding: 1rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 1rem;
    
    .tool-section {
      h4 {
        margin: 0 0 1rem 0;
        color: var(--text-color);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .tool-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }
    }
  }
}

// 深色模式样式
.sof-dark {
  .drawer-menu-item {
    .menu-item-content.active {
      background: var(--surface-600) !important;
    }
  }
  
  .drawer-submenu-item.active {
    background: var(--surface-600) !important;
  }
}
</style>