<template>
  <n-drawer
    v-model:show="showDrawer"
    :placement="drawerPosition"
    :width="320"
    :mask-closable="true"
    :show-mask="true"
    class="app-drawer"
  >
    <n-drawer-content
      :title="t('common.siteName')"
      closable style="padding: 0;"
      class="drawer-content-wrapper"
    >
      <template #header>
        <div class="drawer-header">
          <div class="logo-section">
            <img src="@/assets/icons/logo.svg" alt="MyFirm Logo" class="logo-image" />
            <span class="site-name">{{ t('common.siteName') }}</span>
          </div>
        </div>
      </template>

      <div class="drawer-content">
      <!-- 搜索框 -->
      <div class="drawer-search-section">
        <div class="search-input-wrapper">
          <InputText
            v-model="searchQuery"
            :placeholder="t('common.search')"
            class="drawer-search-input"
            @keyup.enter="handleSearch"
          />
          <Button
            class="drawer-search-btn"
            @click="handleSearch"
            text
            rounded
          >
            <i class="pi pi-search"></i>
          </Button>
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="drawer-menu-container">
        <ul class="menu-list">
          <li v-for="item in menuItems" :key="item.label" class="menu-item-wrapper">
            <template v-if="item.items">
              <n-popover :placement="position" trigger="click" class="menu-item" to="body">
                <template #trigger>
                  <div class="menu-item">
                    <i :class="item.icon" class="menu-icon"></i>
                    <span class="menu-label">{{ t(item.label) }}</span>
                  </div>
                </template>
                <div class="large-text">
                  <ul class="menu-list">
                    <li v-for="subItem in item.items" :key="subItem.label">
                      <router-link
                        :to="subItem.route!"
                        class="menu-item"
                        :class="{ 'active': isActiveRoute(subItem.route) }"
                        @click="closeDrawer"
                      >
                        <i :class="subItem.icon" class="menu-icon"></i>
                        <span class="menu-label">{{ t(subItem.label) }}</span>
                      </router-link>
                    </li>
                  </ul>
                </div>
              </n-popover>
            </template>
            <template v-else>
              <router-link
                :to="item.route!"
                class="menu-item"
                :class="{ 'active': isActiveRoute(item.route) }"
                @click="closeDrawer"
              >
                <i :class="item.icon" class="menu-icon"></i>
                <span class="menu-label">{{ t(item.label) }}</span>
              </router-link>
            </template>
          </li>
        </ul>
      </div>

      <!-- 工具栏 -->
      <div class="drawer-tools">
        <div class="tool-section">
          <h4>{{ t('common.settings') }}</h4>
          <div class="tool-items">
            <NavThemeSelector class="drawer-tool-item" />
            <NavLanguageSelector class="drawer-tool-item" />
          </div>
        </div>
      </div>
    </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    position: {
        type: String,
        default: 'left',
        validator: (value: string) => ['left', 'right'].includes(value)
    }
});

const emit = defineEmits(['update:visible', 'close']);

const router = useRouter();
const route = useRoute();
const { t } = useTranslation();

// 响应式数据
const showDrawer = ref(props.visible);
const drawerPosition = ref(props.position || 'left');
const searchQuery = ref('');

// 判断路由是否激活
const isActiveRoute = (routePath?: string) => {
    if (!routePath) return false;
    return route.path === routePath || route.path.startsWith(routePath + '/');
};

// 监听props变化
watch(() => props.visible, (newValue) => {
    showDrawer.value = newValue;
});

watch(() => props.position, (newValue) => {
    if (newValue && ['left', 'right'].includes(newValue)) {
        drawerPosition.value = newValue;
    }
});

// 监听抽屉状态变化
watch(() => showDrawer.value, (newValue) => {
    emit('update:visible', newValue);
});

// 关闭抽屉
const closeDrawer = () => {
    showDrawer.value = false;
    emit('close');
};

// 切换抽屉位置
const toggleDrawerPosition = () => {
    drawerPosition.value = drawerPosition.value === 'left' ? 'right' : 'left';
};

// 搜索处理
const handleSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        closeDrawer();
    }
};

// VerticalMenu 组件已经内置了路由激活判断和其他功能

// 暴露方法给父组件
defineExpose({
    toggleDrawer: (position?: 'left' | 'right') => {
        if (position) {
            drawerPosition.value = position;
        }
        showDrawer.value = !showDrawer.value;
    },
    closeDrawer,
    toggleDrawerPosition
});
</script>

<style scoped lang="scss">
.app-drawer {
  :deep(.n-drawer) {
    .n-drawer-content {
      padding: 0 !important;
      background: var(--surface-card);
    }

    .n-drawer-header {
      padding: 0 !important;
      border-bottom: 1px solid var(--surface-border);
      background: var(--surface-card);
    }

    .n-drawer-body {
      padding: 0 !important;
    }

    .n-drawer-body-content-wrapper {
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}

// 全局样式覆盖
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}

:deep(.n-drawer-body) {
  padding: 0 !important;
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;

  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .logo-image {
      width: 32px;
      height: 32px;
    }

    .site-name {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-color);
    }
  }
}

.drawer-content {
  padding: 0.5rem 0;

  .drawer-search-section {
    padding: 0.5rem 1rem;

    .search-input-wrapper {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 25px;
      border: 1px solid var(--surface-border);
      box-shadow: none;

      &:hover {
        border: 1px solid var(--primary-color);
        background: var(--surface-overlay);
      }

      .drawer-search-input {
        width: 100%;
        padding: 0.75rem 3.5rem 0.75rem 1.25rem;
        border: 0;
        outline: none;
        background: transparent;
        box-shadow: none;

        &:focus {
          border: 0;
          background: transparent;
        }

        &:hover {
          border: 0;
          background: transparent;
        }
      }

      .drawer-search-btn {
        margin-right: 4px;
      }
    }
  }

  .drawer-menu-container {
    margin-top: 1rem;
    padding-left: 2rem;
    .menu-list {
      padding: 0;
      margin: 0;
      list-style: none;
      display: flex;
      flex-direction: column;

      .menu-item-wrapper {
        width: 100%;
        list-style: none;
        border-bottom: 1px solid var(--surface-border);

        &:last-child {
          border-bottom: none;
        }

        .menu-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 1rem 0;
          width: 100%;
          color: var(--pimary-text-color);
          text-decoration: none;
          transition: all 0.2s ease;
          cursor: pointer;

          &:hover {
            background: var(--surface-hover);
          }

          &.active {
            background: var(--primary-50);
            color: var(--primary-color);

            .menu-icon {
              color: var(--primary-color);
            }
          }

          .menu-icon {
            font-size: 1.2rem;
            color: var(--text-color-secondary);
            width: 20px;
            text-align: center;
          }

          .menu-label {
            flex: 1;
            font-size: 0.9rem;
            font-weight: 500;
          }

          .menu-arrow {
            font-size: 0.8rem;
            color: var(--text-color-secondary);
          }
        }

        // n-popover 样式通过全局样式处理
        .large-text {
          .menu-list {
            padding: 0;
            margin: 0;
            list-style: none;
            min-width: 200px;

            li {
              border-bottom: 1px solid var(--surface-border);

              &:last-child {
                border-bottom: none;
              }

              .menu-item {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                padding: 0.75rem 1rem;
                color: var(--pimary-text-color);
                text-decoration: none;
                transition: background 0.2s ease;

                &:hover {
                  background: var(--surface-hover);
                }

                &.active {
                  background: var(--primary-50);
                  color: var(--primary-color);

                  .menu-icon {
                    color: var(--primary-color);
                  }
                }

                .menu-icon {
                  font-size: 1rem;
                  color: var(--text-color-secondary);
                  width: 16px;
                  text-align: center;
                }

                .menu-label {
                  font-size: 0.85rem;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }
  }
  

  
  .drawer-tools {
    padding: 1.5rem 1rem 1.5rem 2rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 1rem;
    .tool-section {
      h4 {
        margin: 0 0 1rem 0;
        color: var(--pimary-text-color);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .tool-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }
    }
  }
}


</style>