<template>
    <div>
      <div class="drawer-container">
        <Sidebar v-model:visible="showDrawer" :position="drawerPosition" class="app-drawer">
          <template #header>
            <div class="drawer-header">
              <div class="logo-section">
                <img src="@/assets/icons/logo.svg" alt="MyFirm Logo" class="logo-image" />
                <span class="site-name">{{ $t('common.siteName') }}</span>
              </div>
            </div>
          </template>
          
          <div class="drawer-content">
            <!-- 搜索框 - 已重构为全圆角样式 -->
            <div class="drawer-search-section">
              <div class="search-input-wrapper">
                <InputText
                  v-model="searchQuery"
                  :placeholder="$t('common.search')"
                  class="drawer-search-input"
                  @keyup.enter="handleSearch"
                />
                <Button
                  class="drawer-search-btn"
                  @click="handleSearch"
                  text
                  rounded
                >
                  <i class="pi pi-search"></i>
                </Button>
              </div>
            </div>
            
            <!-- 导航菜单 - 使用 VerticalMenu 组件 -->
            <div class="drawer-menu-container">
              <VerticalMenu
                :collapsed="false"
                :hover-mode="true"
                @item-click="handleMenuItemClick"
              />
            </div>
            
            <!-- 工具栏 -->
            <div class="drawer-tools">
              <div class="tool-section">
                <h4>{{ $t('common.settings') }}</h4>
                <div class="tool-items">
                  <NavThemeSelector class="drawer-tool-item" />
                  <NavLanguageSelector class="drawer-tool-item" />
                </div>
              </div>
            </div>
          </div>
        </Sidebar>
      </div>
    </div>
</template>

<script setup lang="ts">
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Sidebar from 'primevue/sidebar';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import VerticalMenu from './VerticalMenu.vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    position: {
        type: String,
        default: 'left',
        validator: (value: string) => ['left', 'right'].includes(value)
    }
});

const emit = defineEmits(['update:visible', 'close']);

const router = useRouter();

// 导入菜单配置

// 响应式数据
const showDrawer = ref(props.visible);
const drawerPosition = ref(props.position || 'left');
const searchQuery = ref('');
// 菜单项点击处理 (用于 VerticalMenu 的事件)
const handleMenuItemClick = (item: any) => {
    if (item.route) {
        router.push(item.route);
        closeDrawer();
    }
};

// 监听props变化
watch(() => props.visible, (newValue) => {
    showDrawer.value = newValue;
});

watch(() => props.position, (newValue) => {
    if (newValue && ['left', 'right'].includes(newValue)) {
        drawerPosition.value = newValue;
    }
});

// 监听抽屉状态变化
watch(() => showDrawer.value, (newValue) => {
    emit('update:visible', newValue);
});

// 关闭抽屉
const closeDrawer = () => {
    showDrawer.value = false;
    emit('close');
};

// 切换抽屉位置
const toggleDrawerPosition = () => {
    drawerPosition.value = drawerPosition.value === 'left' ? 'right' : 'left';
};

// 搜索处理
const handleSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        closeDrawer();
    }
};

// VerticalMenu 组件已经内置了路由激活判断和其他功能

// 暴露方法给父组件
defineExpose({
    toggleDrawer: (position?: 'left' | 'right') => {
        if (position) {
            drawerPosition.value = position;
        }
        showDrawer.value = !showDrawer.value;
    },
    closeDrawer,
    toggleDrawerPosition
});
</script>

<style scoped lang="scss">
.drawer-container {
  .app-drawer {
    width: 280px;
    
    :deep(.p-sidebar-header) {
      padding: 0;
      border-bottom: 1px solid var(--surface-border);
    }
    
    :deep(.p-sidebar-content) {
      padding: 0;
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  
  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .logo-image {
      width: 32px;
      height: 32px;
    }
    
    .site-name {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-color);
    }
  }
  
  .close-button {
    width: 2rem;
    height: 2rem;
  }
}

.drawer-content {
  .drawer-search-section {
    padding:0.5rem 1px;
    .search-input-wrapper {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 25px;
      border: 1px solid var(--surface-border);
      box-shadow: none;
      &:hover{
        border: 1px solid var(--primary-color);
        background: var(--surface-overlay);
      }

      .drawer-search-input {
        width: 100%;
        padding: 0.75rem 3.5rem 0.75rem 1.25rem;
        border:0;
        outline: none;
        background: transparent;
        box-shadow: none;
        &:focus{
          border: 0;
          background: transparent;
        }
        &:hover{
          border: 0;
          background: transparent;
        }
      }
      .drawer-search-btn {
        margin-right: 4px;
      }
    }
  }
  
  .drawer-menu-container {
    .drawer-custom-menu {
      .menu-item {
        border-bottom: 1px solid var(--surface-border);
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        .menu-item-content {
          .menu-item-link {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;

            &:hover {
              background: var(--surface-hover);
              color: var(--text-color);
            }

            &.active {
              background: var(--primary-50);
              color: var(--primary-color);
              font-weight: 500;

              .menu-icon i {
                color: var(--primary-color);
              }
            }

            .menu-icon {
              width: 24px;
              margin-right: 0.75rem;
              color: var(--text-color-secondary);

              i {
                font-size: 1.1rem;
              }
            }

            .menu-label {
              flex: 1;
              font-weight: 500;
            }

            .menu-submenu-icon {
              color: var(--text-color-secondary);
              font-size: 0.8rem;
              transition: transform 0.2s ease;
            }

            &.has-submenu:hover .menu-submenu-icon {
              transform: translateX(2px);
            }
          }
        }
      }
    }

    // Popover 子菜单样式
    :deep(.submenu-popover) {
      .p-popover-content {
        padding: 0;
        min-width: 280px;

        .submenu-content {
          .submenu-header {
            padding: 0.75rem 1rem 0.5rem;
            border-bottom: 1px solid var(--surface-border);
            margin-bottom: 0.25rem;

            .submenu-title {
              font-size: 0.85rem;
              font-weight: 600;
              color: var(--text-color);
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
          }

          .submenu-items {
            padding: 0.25rem 0;

            .submenu-item-link {
              display: flex;
              align-items: center;
              padding: 0.75rem 1rem;
              color: var(--text-color);
              text-decoration: none;
              transition: background 0.2s ease;

              &:hover {
                background: var(--surface-hover);
              }

              &.active {
                background: var(--primary-50);
                color: var(--primary-color);

                .submenu-icon i {
                  color: var(--primary-color);
                }
              }

              .submenu-icon {
                width: 20px;
                margin-right: 0.75rem;
                color: var(--text-color-secondary);

                i {
                  font-size: 0.9rem;
                }
              }

              .submenu-label {
                font-size: 0.9rem;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
  
  .drawer-tools {
    padding: 1rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 1rem;
    
    .tool-section {
      h4 {
        margin: 0 0 1rem 0;
        color: var(--text-color);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .tool-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }
    }
  }
}

// 深色模式样式
.sof-dark {
  .drawer-menu-item {
    .menu-item-content.active {
      background: var(--surface-600) !important;
    }
  }
  
  .drawer-submenu-item.active {
    background: var(--surface-600) !important;
  }
}
</style>