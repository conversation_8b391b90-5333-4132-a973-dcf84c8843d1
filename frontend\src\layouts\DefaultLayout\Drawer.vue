<template>
    <div>
      <div class="drawer-container">
        <Sidebar v-model:visible="showDrawer" :position="drawerPosition" class="app-drawer">
          <template #header>
            <div class="drawer-header">
              <div class="logo-section">
                <img src="@/assets/icons/logo.svg" alt="MyFirm Logo" class="logo-image" />
                <span class="site-name">{{ $t('common.siteName') }}</span>
              </div>
            </div>
          </template>
          
          <div class="drawer-content">
            <!-- 搜索框 - 已重构为全圆角样式 -->
            <div class="drawer-search-section">
              <div class="search-input-wrapper">
                <InputText
                  v-model="searchQuery"
                  :placeholder="$t('common.search')"
                  class="drawer-search-input"
                  @keyup.enter="handleSearch"
                />
                <Button
                  class="drawer-search-btn"
                  @click="handleSearch"
                  text
                  rounded
                >
                  <i class="pi pi-search"></i>
                </Button>
              </div>
            </div>
            
            <!-- 导航菜单 - 使用 Naive UI Menu 组件 -->
            <div class="drawer-menu-container">
                <ul class="menu-list">
                    <li v-for="item in menuItems" :key="item.label" class="menu-item-wrapper">    
                    
                    <template v-if="item.items">
                        <n-popover placement="right" trigger="click|hover" class="menu-item" to="body">
                            <template #trigger>
                                <div class="menu-item">
                                    <i :class="item.icon"></i>
                                    <span class="menu-label">{{ t(item.label) }}</span>
                                </div>
                            </template>
                            <div class="large-text">
                                <ul class="menu-list">
                                    <li v-for="subItem in item.items" :key="subItem.label">
                                        <router-link :to="subItem.route!" class="menu-item">
                                            <i :class="subItem.icon"></i>
                                            <span class="menu-label">{{ t(subItem.label) }}</span>
                                        </router-link>
                                    </li>
                                </ul>
                            </div>
                        </n-popover>
                    </template>
                    <template v-else>
                        <router-link :to="item.route!" class="menu-item">
                            <i :class="item.icon"></i>
                            <span class="menu-label">{{ t(item.label) }}</span>
                        </router-link>
                    </template>
                    </li>
                </ul>
            </div>
            
            <!-- 工具栏 -->
            <div class="drawer-tools">
              <div class="tool-section">
                <h4>{{ $t('common.settings') }}</h4>
                <div class="tool-items">
                  <NavThemeSelector class="drawer-tool-item" />
                  <NavLanguageSelector class="drawer-tool-item" />
                </div>
              </div>
            </div>
          </div>
        </Sidebar>
      </div>
    </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import { NIcon } from 'naive-ui';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Sidebar from 'primevue/sidebar';
import { computed, h, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    position: {
        type: String,
        default: 'left',
        validator: (value: string) => ['left', 'right'].includes(value)
    }
});

const emit = defineEmits(['update:visible', 'close']);

const router = useRouter();
const route = useRoute();
const { t } = useTranslation();

// 响应式数据
const showDrawer = ref(props.visible);
const drawerPosition = ref(props.position || 'left');
const searchQuery = ref('');

// 当前激活的菜单项
const activeMenuKey = computed(() => {
    return route.path;
});

// 渲染图标的函数
const renderIcon = (icon: string) => {
    return () => h(NIcon, null, { default: () => h('i', { class: icon }) });
};

// 转换菜单数据为 Naive UI 格式
const naiveMenuOptions = computed(() => {
    const convertMenuItem = (item: any): any => {
        const option: any = {
            label: t(item.label),
            key: item.route || item.label,
        };

        if (item.icon) {
            option.icon = renderIcon(item.icon);
        }

        if (item.items && item.items.length > 0) {
            option.children = item.items.map(convertMenuItem);
        }

        return option;
    };

    return menuItems.map(convertMenuItem);
});

// 菜单选择处理
const handleMenuSelect = (key: string) => {
    // 查找对应的菜单项
    const findMenuItem = (items: any[], targetKey: string): any => {
        for (const item of items) {
            if (item.route === targetKey) {
                return item;
            }
            if (item.items) {
                const found = findMenuItem(item.items, targetKey);
                if (found) return found;
            }
        }
        return null;
    };

    const menuItem = findMenuItem(menuItems, key);
    if (menuItem && menuItem.route) {
        router.push(menuItem.route);
        closeDrawer();
    }
};

// 监听props变化
watch(() => props.visible, (newValue) => {
    showDrawer.value = newValue;
});

watch(() => props.position, (newValue) => {
    if (newValue && ['left', 'right'].includes(newValue)) {
        drawerPosition.value = newValue;
    }
});

// 监听抽屉状态变化
watch(() => showDrawer.value, (newValue) => {
    emit('update:visible', newValue);
});

// 关闭抽屉
const closeDrawer = () => {
    showDrawer.value = false;
    emit('close');
};

// 切换抽屉位置
const toggleDrawerPosition = () => {
    drawerPosition.value = drawerPosition.value === 'left' ? 'right' : 'left';
};

// 搜索处理
const handleSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        closeDrawer();
    }
};

// VerticalMenu 组件已经内置了路由激活判断和其他功能

// 暴露方法给父组件
defineExpose({
    toggleDrawer: (position?: 'left' | 'right') => {
        if (position) {
            drawerPosition.value = position;
        }
        showDrawer.value = !showDrawer.value;
    },
    closeDrawer,
    toggleDrawerPosition
});
</script>

<style scoped lang="scss">
.drawer-container {
  .app-drawer {
    width: 280px;
    
    :deep(.p-sidebar-header) {
      padding: 0;
      border-bottom: 1px solid var(--surface-border);
    }
    
    :deep(.p-sidebar-content) {
      padding: 0;
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  
  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    
    .logo-image {
      width: 32px;
      height: 32px;
    }
    
    .site-name {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--primary-color);
    }
  }
  
  .close-button {
    width: 2rem;
    height: 2rem;
  }
}


.drawer-content {
    padding: 0.5rem 0;
  .drawer-search-section {
    padding:0.5rem 1px;
    .search-input-wrapper {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 25px;
      border: 1px solid var(--surface-border);
      box-shadow: none;
      &:hover{
        border: 1px solid var(--primary-color);
        background: var(--surface-overlay);
      }

      .drawer-search-input {
        width: 100%;
        padding: 0.75rem 3.5rem 0.75rem 1.25rem;
        border:0;
        outline: none;
        background: transparent;
        box-shadow: none;
        &:focus{
          border: 0;
          background: transparent;
        }
        &:hover{
          border: 0;
          background: transparent;
        }
      }
      .drawer-search-btn {
        margin-right: 4px;
      }
    }
 
  }
  .menu-list{
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    .menu-item-wrapper{
        width: 100%;
        list-style: none;
        border-top: 1px solid var(--surface-border);
        padding: 0.5rem 0;
        display: flex;
        align-items: center;
        &:first-child{
            border-top: none;
        }
        &:hover{
            background: var(--surface-100);
        }
       
        .menu-item{
            display: flex;
            align-items: center;
            gap: 0.8rem;
            padding: 0.5rem 1rem;
            width: 100%;
            .menu-icon{
                font-size: 1.5rem;
            }
            .menu-label{
                font-size: 0.9rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
     }
    }

    }
  
  // 菜单样式已移至 Menu 组件中
  
  .drawer-tools {
    padding: 1rem;
    border-top: 1px solid var(--surface-border);
    margin-top: 1rem;
    
    .tool-section {
      h4 {
        margin: 0 0 1rem 0;
        color: var(--text-color);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .tool-items {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }
    }
  }
}

// 深色模式样式
.sof-dark {
  .drawer-menu-item {
    .menu-item-content.active {
      background: var(--surface-600) !important;
    }
  }
  
  .drawer-submenu-item.active {
    background: var(--surface-600) !important;
  }
}
</style>