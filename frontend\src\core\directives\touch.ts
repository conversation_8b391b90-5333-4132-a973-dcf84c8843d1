/**
 * 触摸手势指令
 * 用于处理移动端的触摸交互
 */

import type { Directive, DirectiveBinding } from 'vue'

interface TouchPoint {
  x: number
  y: number
  time: number
}

interface TouchGestureOptions {
  // 滑动手势
  onSwipeLeft?: (event: TouchEvent) => void
  onSwipeRight?: (event: TouchEvent) => void
  onSwipeUp?: (event: TouchEvent) => void
  onSwipeDown?: (event: TouchEvent) => void
  
  // 点击手势
  onTap?: (event: TouchEvent) => void
  onDoubleTap?: (event: TouchEvent) => void
  onLongPress?: (event: TouchEvent) => void
  
  // 缩放手势
  onPinchStart?: (event: TouchEvent, scale: number) => void
  onPinchMove?: (event: TouchEvent, scale: number) => void
  onPinchEnd?: (event: TouchEvent, scale: number) => void
  
  // 旋转手势
  onRotateStart?: (event: TouchEvent, angle: number) => void
  onRotateMove?: (event: TouchEvent, angle: number) => void
  onRotateEnd?: (event: TouchEvent, angle: number) => void
  
  // 拖拽手势
  onPanStart?: (event: TouchEvent, point: TouchPoint) => void
  onPanMove?: (event: TouchEvent, point: TouchPoint, delta: TouchPoint) => void
  onPanEnd?: (event: TouchEvent, point: TouchPoint) => void
  
  // 配置选项
  swipeThreshold?: number      // 滑动阈值（像素）
  swipeVelocity?: number       // 滑动速度阈值（像素/毫秒）
  tapTimeout?: number          // 点击超时时间（毫秒）
  doubleTapTimeout?: number    // 双击超时时间（毫秒）
  longPressTimeout?: number    // 长按超时时间（毫秒）
  pinchThreshold?: number      // 缩放阈值
  rotateThreshold?: number     // 旋转阈值（度）
  preventDefault?: boolean     // 是否阻止默认行为
  stopPropagation?: boolean    // 是否阻止事件冒泡
}

interface TouchGestureElement extends HTMLElement {
  _touchGesture?: {
    options: TouchGestureOptions
    startPoints: TouchPoint[]
    lastPoints: TouchPoint[]
    tapCount: number
    tapTimer: number | null
    longPressTimer: number | null
    isLongPress: boolean
    isPanning: boolean
    initialDistance: number
    initialAngle: number
    lastScale: number
    lastRotation: number
  }
}

// 默认配置
const defaultOptions: Partial<TouchGestureOptions> = {
  swipeThreshold: 50,
  swipeVelocity: 0.3,
  tapTimeout: 300,
  doubleTapTimeout: 300,
  longPressTimeout: 500,
  pinchThreshold: 0.1,
  rotateThreshold: 5,
  preventDefault: false,
  stopPropagation: false
}

// 计算两点距离
function getDistance(point1: TouchPoint, point2: TouchPoint): number {
  const dx = point2.x - point1.x
  const dy = point2.y - point1.y
  return Math.sqrt(dx * dx + dy * dy)
}

// 计算两点角度
function getAngle(point1: TouchPoint, point2: TouchPoint): number {
  const dx = point2.x - point1.x
  const dy = point2.y - point1.y
  return Math.atan2(dy, dx) * 180 / Math.PI
}

// 计算多点中心
function getCenter(points: TouchPoint[]): TouchPoint {
  let x = 0
  let y = 0
  
  for (const point of points) {
    x += point.x
    y += point.y
  }
  
  return {
    x: x / points.length,
    y: y / points.length,
    time: Date.now()
  }
}

// 从触摸事件获取触摸点
function getTouchPoints(event: TouchEvent): TouchPoint[] {
  const points: TouchPoint[] = []
  const time = Date.now()
  
  for (let i = 0; i < event.touches.length; i++) {
    const touch = event.touches[i]
    points.push({
      x: touch.clientX,
      y: touch.clientY,
      time
    })
  }
  
  return points
}

// 判断滑动方向
function getSwipeDirection(
  startPoint: TouchPoint,
  endPoint: TouchPoint,
  threshold: number
): 'left' | 'right' | 'up' | 'down' | null {
  const dx = endPoint.x - startPoint.x
  const dy = endPoint.y - startPoint.y
  const absDx = Math.abs(dx)
  const absDy = Math.abs(dy)
  
  // 检查是否达到阈值
  if (Math.max(absDx, absDy) < threshold) {
    return null
  }
  
  // 判断主要方向
  if (absDx > absDy) {
    return dx > 0 ? 'right' : 'left'
  } else {
    return dy > 0 ? 'down' : 'up'
  }
}

// 处理触摸开始
function handleTouchStart(el: TouchGestureElement, event: TouchEvent): void {
  if (!el._touchGesture) return
  
  const { options } = el._touchGesture
  const points = getTouchPoints(event)
  
  // 保存初始触摸点
  el._touchGesture.startPoints = [...points]
  el._touchGesture.lastPoints = [...points]
  el._touchGesture.isPanning = false
  el._touchGesture.isLongPress = false
  
  // 处理多点触摸
  if (points.length >= 2) {
    el._touchGesture.initialDistance = getDistance(points[0], points[1])
    el._touchGesture.initialAngle = getAngle(points[0], points[1])
    el._touchGesture.lastScale = 1
    el._touchGesture.lastRotation = 0
    
    // 触发缩放开始事件
    if (options.onPinchStart) {
      options.onPinchStart(event, 1)
    }
    
    // 触发旋转开始事件
    if (options.onRotateStart) {
      options.onRotateStart(event, 0)
    }
  } else if (points.length === 1) {
    // 单点触摸
    
    // 设置长按定时器
    if (options.onLongPress) {
      el._touchGesture.longPressTimer = window.setTimeout(() => {
        if (!el._touchGesture?.isPanning) {
          el._touchGesture!.isLongPress = true
          options.onLongPress!(event)
        }
      }, options.longPressTimeout!)
    }
    
    // 触发拖拽开始事件
    if (options.onPanStart) {
      options.onPanStart(event, points[0])
    }
  }
  
  // 阻止默认行为
  if (options.preventDefault) {
    event.preventDefault()
  }
  
  // 阻止事件冒泡
  if (options.stopPropagation) {
    event.stopPropagation()
  }
}

// 处理触摸移动
function handleTouchMove(el: TouchGestureElement, event: TouchEvent): void {
  if (!el._touchGesture) return
  
  const { options, startPoints, lastPoints } = el._touchGesture
  const points = getTouchPoints(event)
  
  // 更新最后触摸点
  el._touchGesture.lastPoints = [...points]
  
  // 检查是否开始拖拽
  if (!el._touchGesture.isPanning && points.length === 1 && startPoints.length === 1) {
    const distance = getDistance(startPoints[0], points[0])
    if (distance > 10) { // 移动超过10像素认为是拖拽
      el._touchGesture.isPanning = true
      
      // 清除长按定时器
      if (el._touchGesture.longPressTimer) {
        clearTimeout(el._touchGesture.longPressTimer)
        el._touchGesture.longPressTimer = null
      }
    }
  }
  
  // 处理多点触摸
  if (points.length >= 2 && startPoints.length >= 2) {
    const currentDistance = getDistance(points[0], points[1])
    const currentAngle = getAngle(points[0], points[1])
    
    // 计算缩放比例
    const scale = currentDistance / el._touchGesture.initialDistance
    
    // 计算旋转角度
    let rotation = currentAngle - el._touchGesture.initialAngle
    
    // 标准化角度到 -180 到 180 度
    while (rotation > 180) rotation -= 360
    while (rotation < -180) rotation += 360
    
    // 触发缩放事件
    if (options.onPinchMove && Math.abs(scale - el._touchGesture.lastScale) > options.pinchThreshold!) {
      options.onPinchMove(event, scale)
      el._touchGesture.lastScale = scale
    }
    
    // 触发旋转事件
    if (options.onRotateMove && Math.abs(rotation - el._touchGesture.lastRotation) > options.rotateThreshold!) {
      options.onRotateMove(event, rotation)
      el._touchGesture.lastRotation = rotation
    }
  } else if (points.length === 1 && el._touchGesture.isPanning) {
    // 单点拖拽
    if (options.onPanMove && lastPoints.length === 1) {
      const delta = {
        x: points[0].x - lastPoints[0].x,
        y: points[0].y - lastPoints[0].y,
        time: points[0].time - lastPoints[0].time
      }
      options.onPanMove(event, points[0], delta)
    }
  }
  
  // 阻止默认行为
  if (options.preventDefault) {
    event.preventDefault()
  }
  
  // 阻止事件冒泡
  if (options.stopPropagation) {
    event.stopPropagation()
  }
}

// 处理触摸结束
function handleTouchEnd(el: TouchGestureElement, event: TouchEvent): void {
  if (!el._touchGesture) return
  
  const { options, startPoints, lastPoints } = el._touchGesture
  const points = getTouchPoints(event)
  
  // 清除定时器
  if (el._touchGesture.longPressTimer) {
    clearTimeout(el._touchGesture.longPressTimer)
    el._touchGesture.longPressTimer = null
  }
  
  // 处理多点触摸结束
  if (lastPoints.length >= 2) {
    const currentDistance = getDistance(lastPoints[0], lastPoints[1])
    const scale = currentDistance / el._touchGesture.initialDistance
    
    const currentAngle = getAngle(lastPoints[0], lastPoints[1])
    let rotation = currentAngle - el._touchGesture.initialAngle
    
    // 标准化角度
    while (rotation > 180) rotation -= 360
    while (rotation < -180) rotation += 360
    
    // 触发缩放结束事件
    if (options.onPinchEnd) {
      options.onPinchEnd(event, scale)
    }
    
    // 触发旋转结束事件
    if (options.onRotateEnd) {
      options.onRotateEnd(event, rotation)
    }
  } else if (startPoints.length === 1 && lastPoints.length >= 1) {
    // 单点触摸结束
    const startPoint = startPoints[0]
    const endPoint = lastPoints[0]
    const duration = endPoint.time - startPoint.time
    const distance = getDistance(startPoint, endPoint)
    const velocity = distance / duration
    
    if (!el._touchGesture.isLongPress && !el._touchGesture.isPanning) {
      // 处理点击事件
      el._touchGesture.tapCount++
      
      if (el._touchGesture.tapCount === 1) {
        // 第一次点击
        el._touchGesture.tapTimer = window.setTimeout(() => {
          if (el._touchGesture?.tapCount === 1 && options.onTap) {
            options.onTap(event)
          }
          el._touchGesture!.tapCount = 0
        }, options.doubleTapTimeout!)
      } else if (el._touchGesture.tapCount === 2) {
        // 双击
        if (el._touchGesture.tapTimer) {
          clearTimeout(el._touchGesture.tapTimer)
          el._touchGesture.tapTimer = null
        }
        
        if (options.onDoubleTap) {
          options.onDoubleTap(event)
        }
        
        el._touchGesture.tapCount = 0
      }
    } else if (el._touchGesture.isPanning) {
      // 处理滑动事件
      if (velocity > options.swipeVelocity!) {
        const direction = getSwipeDirection(startPoint, endPoint, options.swipeThreshold!)
        
        switch (direction) {
          case 'left':
            if (options.onSwipeLeft) options.onSwipeLeft(event)
            break
          case 'right':
            if (options.onSwipeRight) options.onSwipeRight(event)
            break
          case 'up':
            if (options.onSwipeUp) options.onSwipeUp(event)
            break
          case 'down':
            if (options.onSwipeDown) options.onSwipeDown(event)
            break
        }
      }
      
      // 触发拖拽结束事件
      if (options.onPanEnd) {
        options.onPanEnd(event, endPoint)
      }
    }
  }
  
  // 重置状态
  if (points.length === 0) {
    el._touchGesture.isPanning = false
    el._touchGesture.isLongPress = false
  }
  
  // 阻止默认行为
  if (options.preventDefault) {
    event.preventDefault()
  }
  
  // 阻止事件冒泡
  if (options.stopPropagation) {
    event.stopPropagation()
  }
}

// 触摸手势指令
export const vTouch: Directive<TouchGestureElement, TouchGestureOptions> = {
  mounted(el: TouchGestureElement, binding: DirectiveBinding<TouchGestureOptions>) {
    const options = {
      ...defaultOptions,
      ...binding.value
    }
    
    // 初始化状态
    el._touchGesture = {
      options,
      startPoints: [],
      lastPoints: [],
      tapCount: 0,
      tapTimer: null,
      longPressTimer: null,
      isLongPress: false,
      isPanning: false,
      initialDistance: 0,
      initialAngle: 0,
      lastScale: 1,
      lastRotation: 0
    }
    
    // 绑定事件监听器
    el.addEventListener('touchstart', (e) => handleTouchStart(el, e), { passive: !options.preventDefault })
    el.addEventListener('touchmove', (e) => handleTouchMove(el, e), { passive: !options.preventDefault })
    el.addEventListener('touchend', (e) => handleTouchEnd(el, e), { passive: !options.preventDefault })
    el.addEventListener('touchcancel', (e) => handleTouchEnd(el, e), { passive: !options.preventDefault })
  },
  
  updated(el: TouchGestureElement, binding: DirectiveBinding<TouchGestureOptions>) {
    if (el._touchGesture) {
      el._touchGesture.options = {
        ...defaultOptions,
        ...binding.value
      }
    }
  },
  
  unmounted(el: TouchGestureElement) {
    if (el._touchGesture) {
      // 清除定时器
      if (el._touchGesture.tapTimer) {
        clearTimeout(el._touchGesture.tapTimer)
      }
      if (el._touchGesture.longPressTimer) {
        clearTimeout(el._touchGesture.longPressTimer)
      }
      
      // 移除事件监听器
      el.removeEventListener('touchstart', (e) => handleTouchStart(el, e))
      el.removeEventListener('touchmove', (e) => handleTouchMove(el, e))
      el.removeEventListener('touchend', (e) => handleTouchEnd(el, e))
      el.removeEventListener('touchcancel', (e) => handleTouchEnd(el, e))
      
      delete el._touchGesture
    }
  }
}

// 工具函数
export const touchUtils = {
  // 检查是否支持触摸
  isTouchSupported(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  },
  
  // 获取触摸点数量
  getTouchCount(el: TouchGestureElement): number {
    return el._touchGesture?.lastPoints.length || 0
  },
  
  // 获取当前触摸点
  getCurrentTouchPoints(el: TouchGestureElement): TouchPoint[] {
    return el._touchGesture?.lastPoints || []
  },
  
  // 检查是否正在拖拽
  isPanning(el: TouchGestureElement): boolean {
    return el._touchGesture?.isPanning || false
  },
  
  // 检查是否长按
  isLongPress(el: TouchGestureElement): boolean {
    return el._touchGesture?.isLongPress || false
  },
  
  // 重置手势状态
  reset(el: TouchGestureElement): void {
    if (el._touchGesture) {
      el._touchGesture.startPoints = []
      el._touchGesture.lastPoints = []
      el._touchGesture.tapCount = 0
      el._touchGesture.isPanning = false
      el._touchGesture.isLongPress = false
      
      if (el._touchGesture.tapTimer) {
        clearTimeout(el._touchGesture.tapTimer)
        el._touchGesture.tapTimer = null
      }
      
      if (el._touchGesture.longPressTimer) {
        clearTimeout(el._touchGesture.longPressTimer)
        el._touchGesture.longPressTimer = null
      }
    }
  },
  
  // 模拟触摸事件
  simulateTouch(el: TouchGestureElement, type: 'start' | 'move' | 'end', points: TouchPoint[]): void {
    const event = new TouchEvent(`touch${type}`, {
      touches: points.map(point => ({
        clientX: point.x,
        clientY: point.y,
        identifier: 0,
        target: el
      } as any)),
      bubbles: true,
      cancelable: true
    })
    
    el.dispatchEvent(event)
  }
}

// 导出类型
export type { TouchGestureOptions, TouchPoint }