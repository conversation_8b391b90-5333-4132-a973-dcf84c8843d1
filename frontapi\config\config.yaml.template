# FrontAPI 配置模板文件
# 此文件包含了所有可配置的选项及其默认值和说明

# ============ 服务器配置 ============
server:
  host: "0.0.0.0"          # 服务器监听地址
  port: 8080               # 主服务端口
  admin_port: 8081         # 管理后台端口
  media_port: 8082         # 媒体服务端口

# ============ 数据库配置 ============
database:
  host: "localhost"        # 数据库主机地址
  port: 3306               # 数据库端口
  user: "root"             # 数据库用户名
  password: "password"     # 数据库密码
  name: "lyvideos"         # 数据库名称
  max_idle_conns: 10       # 最大空闲连接数
  max_open_conns: 100      # 最大打开连接数
  conn_max_lifetime: "1h"  # 连接最大生存时间

# ============ JWT安全配置 ============
jwt:
  secret: "your-jwt-secret-key"  # JWT密钥（生产环境请使用强密钥）
  expiry: 168                     # JWT过期时间（小时），默认7天

# ============ 缓存配置 ============
cache:
  default: "memory"        # 默认缓存适配器: memory, redis, file, bigcache
  default_ttl: "1h"        # 默认缓存过期时间
  
  # Redis缓存配置
  redis:
    host: "localhost"      # Redis主机地址
    port: 6379             # Redis端口
    password: ""           # Redis密码
    db: 0                  # Redis数据库编号
    ttl: 3600              # Redis缓存过期时间（秒）
    pool_size: 10          # 连接池大小
    min_idle_conns: 5      # 最小空闲连接数
    dial_timeout: "5s"     # 连接超时时间
    read_timeout: "3s"     # 读取超时时间
    write_timeout: "3s"    # 写入超时时间
    
    # Redis集群配置
    cluster:
      enable: false        # 是否启用集群模式
      nodes: []            # 集群节点列表
      
  # 文件缓存配置
  file:
    path: "./cache"        # 缓存文件存储路径
    ttl: 3600              # 文件缓存过期时间（秒）
    cleanup_interval: "10m" # 清理间隔
    file_mode: 0644        # 文件权限
    dir_mode: 0755         # 目录权限
    enable_compression: false # 是否启用压缩
    max_file_size: 10485760   # 最大文件大小（字节）
    
  # 内存缓存配置
  memory:
    size: 10000            # 内存缓存大小（条目数）
    ttl: 3600              # 内存缓存过期时间（秒）
    cleanup_interval: "5m" # 清理间隔
    
  # BigCache配置
  bigcache:
    size: 10000            # BigCache大小（条目数）
    ttl: 3600              # BigCache过期时间（秒）
    shards: 1024           # 分片数量
    max_entry_size: 500    # 最大条目大小
    hard_max_cache_size: 0 # 硬性最大缓存大小

# ============ Redis数据库配置 ============
redis:
  host: "localhost"        # Redis主机地址
  port: 6379               # Redis端口
  password: ""             # Redis密码
  db: 0                    # 默认数据库编号
  expiry: 168              # 默认过期时间（小时）
  
  # 管理后台Redis配置
  admin:
    db: 1                  # 管理后台数据库编号
    prefix: "admin:"       # 键前缀
    
  # 点赞服务Redis配置
  like:
    db: 1                  # 点赞服务数据库编号
    prefix: "ly_"          # 键前缀
    
  # 收藏服务Redis配置
  collect:
    db: 1                  # 收藏服务数据库编号
    prefix: "ly_"          # 键前缀
    
  # 关注服务Redis配置
  follow:
    db: 1                  # 关注服务数据库编号
    prefix: "ly_"          # 键前缀

# ============ 静态文件配置 ============
static:
  url: "/static"           # 静态文件访问URL前缀

# ============ 运行时配置 ============
runtime:
  temp_dir: "./temp"       # 临时文件目录

# ============ CORS跨域配置 ============
cors:
  allow_credentials: true  # 是否允许携带凭证
  allow_origins:           # 允许的源地址列表
    - "http://localhost:8080"
    - "http://localhost:8081"
    - "http://localhost:3000"
  allow_methods:           # 允许的HTTP方法
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
    - "PATCH"
  allow_headers:           # 允许的请求头
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
    - "X-Requested-With"

# ============ 媒体存储配置 ============
media:
  storage_path: "./storage/media"  # 媒体文件存储路径
  storage_url: "/media"            # 媒体文件访问URL前缀

# ============ 上传配置 ============
upload:
  # 小图片上传配置
  small_image:
    path: "./storage/pictures/small"  # 小图片存储路径
    url: "/upload/small"              # 小图片访问URL前缀
    
  # 图片上传配置
  picture:
    path: "./storage/pictures"        # 图片存储路径
    url: "/upload/pictures"           # 图片访问URL前缀
    
  # 视频上传配置
  video:
    path: "./storage/videos"          # 视频存储路径
    url: "/upload/videos"             # 视频访问URL前缀

# ============ Casbin权限配置 ============
casbin:
  model_path: "./config/rbac_model.conf"   # RBAC模型文件路径
  policy_file: "./config/rbac_policy.csv"  # 权限策略文件路径

# ============ 环境变量映射说明 ============
# 以下配置项可以通过环境变量覆盖：
#
# 服务器配置：
# - SERVER_HOST
# - SERVER_PORT
# - SERVER_ADMIN_PORT
# - SERVER_MEDIA_PORT
#
# 数据库配置：
# - DB_HOST
# - DB_PORT
# - DB_USER
# - DB_PASSWORD
# - DB_NAME
#
# JWT配置：
# - JWT_SECRET
# - JWT_EXPIRY
#
# Redis配置：
# - REDIS_HOST
# - REDIS_PORT
# - REDIS_PASSWORD
# - REDIS_DB
#
# 缓存配置：
# - CACHE_DEFAULT
# - CACHE_REDIS_HOST
# - CACHE_REDIS_PORT
# - CACHE_REDIS_PASSWORD
# - CACHE_REDIS_DB
# - CACHE_FILE_PATH
# - CACHE_DEFAULT_TTL

# ============ 使用说明 ============
# 1. 复制此文件为 config.yaml
# 2. 根据实际环境修改相应配置项
# 3. 生产环境请务必修改JWT密钥和数据库密码
# 4. 确保所有路径目录存在且有相应权限
# 5. Redis和数据库服务需要先启动

# ============ 性能调优建议 ============
# 1. 数据库连接池：根据并发量调整max_open_conns和max_idle_conns
# 2. Redis连接池：根据访问量调整pool_size和min_idle_conns
# 3. 缓存策略：高频访问数据使用Redis，临时数据使用Memory
# 4. 文件上传：大文件建议使用对象存储服务
# 5. 静态资源：生产环境建议使用CDN

# ============ 安全注意事项 ============
# 1. JWT密钥必须使用强随机字符串
# 2. 数据库密码应使用复杂密码
# 3. Redis如果暴露在公网，必须设置密码
# 4. 文件上传路径不应在Web根目录下
# 5. CORS配置应限制可信域名
# 6. 生产环境应禁用调试模式