<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建文件夹" width="30vw"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="submitForm"
    >
      <el-form-item label="文件夹名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入文件夹名称"
          autofocus
          @keyup.enter="submitForm"
        >
          <template #append>
            <el-button type="primary" @click="generateName">生成</el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { createDirectory } from '@/service/api/system/files';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  fileType: {
    type: String,
    default: 'image'
  },
  parentPath: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'folder-created']);

// Refs
const formRef = ref<FormInstance>();
const dialogVisible = ref(props.modelValue);
const loading = ref(false);

// Form data
const form = ref({
  name: ''
});

// Form validation rules
const rules = ref<FormRules>({
  name: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { min: 1, max: 50, message: '文件夹名称长度在 1 到 50 个字符之间', trigger: 'blur' },
    {
      pattern: /^[^\/\\:<>"|?*]+$/,
      message: '文件夹名称不能包含以下字符: / \\ : < > " | ? *',
      trigger: 'blur'
    }
  ]
});
const generateName = () => {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  const millisecond = date.getMilliseconds();
  const name = `${year}${month}${day}${hour}${minute}${second}${millisecond}`;
  form.value.name = name;
}

// Watch for prop changes
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
});

// Watch for dialog visibility changes
watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val);
  if (!val) {
    resetForm();
  }
});

// Methods
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (!valid) return;

    loading.value = true;

    try {
      const {response,data,err} = await createDirectory({
        data: {
          file_type: props.fileType,
          parent_path: props.parentPath,
          dir_name: form.value.name
        }
      }) as any;

      if (response.data.code === 2000) {
        ElMessage.success('文件夹创建成功');
        dialogVisible.value = false;
        emit('folder-created', response.data.data);
      } else {
        ElMessage.error(response.data.message || '创建文件夹失败');
      }
    } catch (error: any) {
      ElMessage.error(error.message || '创建文件夹失败');
    } finally {
      loading.value = false;
    }
  });
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.value.name = '';
};
</script>

<style scoped lang="scss">
.el-dialog{
  width: 35vw;
  height: 60vh;
  min-height: 250px;
  .el-form{
    .el-form-item{
      margin-bottom: 25px;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
