package content_creator

// CreatorApplyRequest 创作者申请请求验证模型
type CreatorApplyRequest struct {
	RealName     string `json:"realName" validate:"required|minLen:2|maxLen:50"`
	IDCardNumber string `json:"idCardNumber" validate:"required|idcard"`
	IDCardFront  string `json:"idCardFront" validate:"required|url"`
	IDCardBack   string `json:"idCardBack" validate:"required|url"`
	Phone        string `json:"phone" validate:"required|mobile"`
	Email        string `json:"email" validate:"required|email"`
	Introduction string `json:"introduction" validate:"required|minLen:10|maxLen:500"`
	Category     string `json:"category" validate:"required|in:video,music,picture,article,comic"`
	SampleWorks  string `json:"sampleWorks" validate:"required|url"`
}

// CreatorProfileUpdateRequest 创作者资料更新请求验证模型
type CreatorProfileUpdateRequest struct {
	Introduction string   `json:"introduction" validate:"minLen:10|maxLen:500"`
	Category     []string `json:"category" validate:"each:in:video,music,picture,article,comic"`
	SampleWorks  []string `json:"sampleWorks" validate:"each:url"`
	Social       struct {
		Weibo    string `json:"weibo" validate:"url"`
		Wechat   string `json:"wechat"`
		Bilibili string `json:"bilibili" validate:"url"`
		Youtube  string `json:"youtube" validate:"url"`
		Website  string `json:"website" validate:"url"`
	} `json:"social"`
}