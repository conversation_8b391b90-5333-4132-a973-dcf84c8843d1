# 工具函数使用示例

本文档展示如何使用 `@/utils` 中导出的各种工具函数。

## 基本导入方式

### 1. 导入单个函数

```typescript
// 导入格式化函数
import { formatTime, formatNumber, formatCount, formatDuration } from '@/utils'

// 导入头像函数
import { getAvatarUrl, getSafeAvatarUrl } from '@/utils'

// 导入章节函数
import { formatChapter, formatChapterList } from '@/utils'

// 导入请求函数
import { request, get, post, put, del } from '@/utils'
```

### 2. 导入分组函数

```typescript
// 按功能分组导入
import { FormatUtils, AvatarUtils, ChapterUtils, RequestUtils } from '@/utils'

// 使用分组函数
const formattedTime = FormatUtils.formatTime('2024-01-01T00:00:00Z')
const avatarUrl = AvatarUtils.getAvatarUrl('/avatar.jpg', 'user123')
const chapterTitle = ChapterUtils.formatChapter(1, 'chinese')
```

### 3. 导入所有函数

```typescript
// 导入所有工具函数
import * as Utils from '@/utils'

// 使用
const time = Utils.formatTime(new Date().toISOString())
const avatar = Utils.getAvatarUrl('/test.jpg', 'user')
```

### 4. 导入类型定义

```typescript
// 导入类型
import type { ApiResponse, PageResponse, RequestParams } from '@/utils'

// 使用类型
const response: ApiResponse<User> = await get('/api/user')
const pageData: PageResponse<Post> = await getPage('/api/posts')
```

## 具体使用示例

### 格式化函数

```typescript
import { formatTime, formatNumber, formatCount, formatDuration } from '@/utils'

// 时间格式化
const timeAgo = formatTime('2024-01-01T00:00:00Z') // "3天前"

// 数字格式化
const shortNumber = formatNumber(12345) // "12.3k"
const count = formatCount(9999) // "9.9k"

// 时长格式化
const duration = formatDuration(3661) // "1:01:01"
```

### 头像函数

```typescript
import { getAvatarUrl, getSafeAvatarUrl, preloadAvatar } from '@/utils'

// 获取头像URL
const avatarUrl = getAvatarUrl('/uploads/avatar.jpg', 'user123')

// 获取安全头像URL（带默认头像）
const safeUrl = getSafeAvatarUrl('/uploads/avatar.jpg', 'user123')

// 预加载头像
await preloadAvatar('/uploads/avatar.jpg')
```

### 章节格式化

```typescript
import { formatChapter, formatChapterList, CHAPTER_FORMATS } from '@/utils'

// 格式化单个章节
const chapterTitle = formatChapter(1, 'chinese') // "第一章"
const romanChapter = formatChapter(5, 'roman') // "第V章"

// 格式化章节列表
const chapters = [1, 2, 3, 4, 5]
const formattedList = formatChapterList(chapters, 'chinese')
// ["第一章", "第二章", "第三章", "第四章", "第五章"]

// 查看可用格式
console.log(CHAPTER_FORMATS) // 所有可用的章节格式
```

### HTTP请求

```typescript
import { request, get, post, put, del, http } from '@/utils'
import type { ApiResponse } from '@/utils'

// 基本请求
const response: ApiResponse<User> = await get('/api/user/123')

// POST请求
const createResult = await post('/api/users', {
  name: 'John',
  email: '<EMAIL>'
})

// 使用http实例
const data = await http.get('/api/data')

// 自定义请求
const customResponse = await request({
  url: '/api/custom',
  method: 'POST',
  data: { key: 'value' }
})
```

### 分页请求

```typescript
import { getPage, postPageList } from '@/utils'
import type { PageResponse } from '@/utils'

// GET分页
const pageData: PageResponse<Post> = await getPage('/api/posts', {
  page: 1,
  size: 10,
  keyword: 'search'
})

// POST分页
const postPageData = await postPageList('/api/posts/search', {
  page: 1,
  size: 20,
  filters: { category: 'tech' }
})
```

## Vue组件中的使用

```vue
<template>
  <div>
    <img :src="avatarUrl" :alt="user.name" />
    <h3>{{ chapterTitle }}</h3>
    <p>发布于 {{ publishTime }}</p>
    <span>{{ viewCount }} 次浏览</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  getAvatarUrl, 
  formatChapter, 
  formatTime, 
  formatCount 
} from '@/utils'

interface User {
  id: string
  name: string
  avatar: string
}

interface Post {
  id: string
  title: string
  chapter: number
  publishedAt: string
  views: number
  author: User
}

const props = defineProps<{
  post: Post
}>()

// 计算属性
const avatarUrl = computed(() => 
  getAvatarUrl(props.post.author.avatar, props.post.author.id)
)

const chapterTitle = computed(() => 
  formatChapter(props.post.chapter, 'chinese')
)

const publishTime = computed(() => 
  formatTime(props.post.publishedAt)
)

const viewCount = computed(() => 
  formatCount(props.post.views)
)
</script>
```

## 注意事项

1. **路径别名**: 确保使用 `@/utils` 而不是相对路径
2. **类型安全**: 导入类型定义以获得更好的TypeScript支持
3. **按需导入**: 只导入需要的函数以减少打包体积
4. **分组导入**: 对于相关功能，可以使用分组导入保持代码整洁
5. **默认导出**: 某些函数有默认导出，可以根据需要选择导入方式

## 扩展

如果需要添加新的工具函数：

1. 在对应的文件中添加函数（如 `format.ts`、`avatar.ts` 等）
2. 在 `index.ts` 中添加导出
3. 更新此文档的使用示例