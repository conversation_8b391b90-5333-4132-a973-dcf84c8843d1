package content_creator

import "frontapi/pkg/types"

type CreateContentRevenueRequest struct {
	ContentType    string         `json:"content_type"`
	ContentID      string         `json:"content_id"`
	UserID         string         `json:"user_id"`
	OrderID        string         `json:"order_id"`
	Amount         float64        `json:"amount"`
	PlatformAmount float64        `json:"platform_amount"`
	ReferrerAmount float64        `json:"referrer_amount"`
	Status         string         `json:"status"`
	SettleTime     types.JSONTime `json:"settle_time"`
}

type UpdateContentRevenueRequest struct {
	Amount         float64        `json:"amount"`
	PlatformAmount float64        `json:"platform_amount"`
	ReferrerAmount float64        `json:"referrer_amount"`
	Status         string         `json:"status"`
	SettleTime     types.JSONTime `json:"settle_time"`
}
