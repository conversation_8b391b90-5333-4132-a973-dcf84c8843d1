package books

import (
	"context"
	"errors"

	"frontapi/internal/models/books"
	bookRepo "frontapi/internal/repository/books"
	"frontapi/internal/service/base"
	bookValidator "frontapi/internal/validation/books"
)

// BookBookmarkService 电子书书签服务接口
type BookmarkService interface {
	CreateBookmark(ctx context.Context, req *bookValidator.CreateBookmarkRequest) (string, error)
	UpdateBookmark(ctx context.Context, id string, req *bookValidator.UpdateBookmarkRequest) error
	GetBookmark(ctx context.Context, id string) (*books.Bookmark, error)
	DeleteBookmark(ctx context.Context, id string) error
	ListUserBookmarks(ctx context.Context, userID string, page, pageSize int) ([]*books.Bookmark, int64, error)
	ListBookBookmarks(ctx context.Context, userID, bookID string, page, pageSize int) ([]*books.Bookmark, int64, error)
}

type bookmarkService struct {
	*base.BaseService[books.Bookmark] // 嵌入BaseService，自动获得所有方法
	bookmarkRepo                      bookRepo.BookmarkRepository
	bookRepo                          bookRepo.BookRepository
	chapterRepo                       bookRepo.ChapterRepository
}

// NewBookmarkService 创建电子书书签服务实例
func NewBookmarkService(
	bookmarkRepo bookRepo.BookmarkRepository,
	bookRepo bookRepo.BookRepository,
	chapterRepo bookRepo.ChapterRepository,
) BookmarkService {
	return &bookmarkService{
		BaseService:  base.NewBaseService[books.Bookmark](bookmarkRepo, "bookmark"),
		bookmarkRepo: bookmarkRepo,
		bookRepo:     bookRepo,
		chapterRepo:  chapterRepo,
	}
}

// CreateBookmark 创建电子书书签
func (s *bookmarkService) CreateBookmark(ctx context.Context, req *bookValidator.CreateBookmarkRequest) (string, error) {
	// 检查电子书是否存在
	book, err := s.bookRepo.FindByID(ctx, req.BookID)
	if err != nil {
		return "", err
	}
	if book == nil {
		return "", errors.New("电子书不存在")
	}

	// 检查章节是否存在
	chapter, err := s.chapterRepo.FindByID(ctx, req.ChapterID)
	if err != nil {
		return "", err
	}
	if chapter == nil {
		return "", errors.New("章节不存在")
	}

	// 如果未提供标题，则使用章节标题
	if req.Title == "" {
		req.Title = chapter.Title
	}

	bookmark := &books.Bookmark{
		UserID:    req.UserID,
		BookID:    req.BookID,
		ChapterID: req.ChapterID,
		Position:  req.Position,
		Title:     req.Title,
		Content:   req.Content,
	}

	err = s.bookmarkRepo.Create(ctx, bookmark)
	if err != nil {
		return "", err
	}

	return bookmark.GetID(), nil
}

// UpdateBookmark 更新电子书书签
func (s *bookmarkService) UpdateBookmark(ctx context.Context, id string, req *bookValidator.UpdateBookmarkRequest) error {
	bookmark, err := s.bookmarkRepo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if bookmark == nil {
		return errors.New("书签不存在")
	}

	// 更新字段
	if req.Title != "" {
		bookmark.Title = req.Title
	}
	if req.Content != "" {
		bookmark.Content = req.Content
	}
	if req.Position > 0 {
		bookmark.Position = req.Position
	}

	return s.bookmarkRepo.Update(ctx, bookmark)
}

// GetBookmark 获取电子书书签详情
func (s *bookmarkService) GetBookmark(ctx context.Context, id string) (*books.Bookmark, error) {
	return s.GetByID(ctx, id, true)
}

// DeleteBookmark 删除电子书书签
func (s *bookmarkService) DeleteBookmark(ctx context.Context, id string) error {
	return s.Delete(ctx, id)
}

// ListUserBookmarks 获取用户的书签列表
func (s *bookmarkService) ListUserBookmarks(ctx context.Context, userID string, page, pageSize int) ([]*books.Bookmark, int64, error) {
	return s.bookmarkRepo.ListByUser(ctx, userID, page, pageSize)
}

// ListBookBookmarks 获取用户在某本书的书签列表
func (s *bookmarkService) ListBookBookmarks(ctx context.Context, userID, bookID string, page, pageSize int) ([]*books.Bookmark, int64, error) {
	return s.bookmarkRepo.ListByBook(ctx, userID, bookID, page, pageSize)
}
