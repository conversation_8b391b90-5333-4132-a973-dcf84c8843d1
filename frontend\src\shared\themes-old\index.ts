/**
 * 主题配置索引
 * 导出所有可用主题
 */
import { ThemeConfig } from './theme-manager';

// 导入所有主题的light和dark版本
import { blueDarkTheme } from './blue';
import { greenDarkTheme, greenLightTheme } from './green';
import { orangeDarkTheme, orangeLightTheme } from './orange';
import { blueLightTheme, charmDarkTheme } from './pink';
import { mysteriousDarkTheme, mysteriousLightTheme } from './purple';

// 旧版主题管理器 - 兼容旧代码
export const themeManager = {
    // 旧方法的空实现
    initTheme: () => { },
    setTheme: (theme: string) => { }
};

// 导出所有主题列表 - 包含light和dark版本
export const themes: ThemeConfig[] = [
    // Modern主题
    blueLightTheme,
    blueDarkTheme,
    // orange主题
    orangeLightTheme,
    orangeDarkTheme,
    // Dark主题
    darkLightTheme,
    darkDarkTheme,
    // green主题
    greenLightTheme,
    greenDarkTheme,
    // Charm主题
    blueLightTheme,
    charmDarkTheme,
    // Mysterious主题 (默认)
    mysteriousLightTheme,
    mysteriousDarkTheme
];

// 主题映射表 - 便于查找
export const themeMap: Record<string, ThemeConfig> = {
    'modern-light': blueLightTheme,
    'modern-dark': blueDarkTheme,
    'orange-light': orangeLightTheme,
    'orange-dark': orangeDarkTheme,
    'noir-light': darkLightTheme,
    'dark-dark': darkDarkTheme,
    'green-light': greenLightTheme,
    'green-dark': greenDarkTheme,
    'charm-light': blueLightTheme,
    'charm-dark': charmDarkTheme,
    'mysterious-light': mysteriousLightTheme,
    'mysterious-dark': mysteriousDarkTheme
};

// 默认导出主题列表
export default themes;