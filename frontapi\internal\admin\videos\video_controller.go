package videos

import (
	"frontapi/internal/admin"
	videoModel "frontapi/internal/models/videos"
	userSrv "frontapi/internal/service/users"
	videoSrv "frontapi/internal/service/videos"
	"frontapi/internal/validation/videos"
	videoValidator "frontapi/internal/validation/videos"
	"frontapi/pkg/types"
	"frontapi/pkg/validator"

	"github.com/guregu/null/v6"

	"fmt"

	"github.com/gofiber/fiber/v2"
)

// VideoController 视频控制器结构体
type VideoController struct {
	admin.BaseController
	videoService    videoSrv.VideoService
	categoryService videoSrv.VideoCategoryService
	commentService  videoSrv.VideoCommentService
	channelService  videoSrv.VideoChannelService
	userService     userSrv.UserService
}

// NewVideoController 创建视频控制器
func NewVideoController(
	videoService videoSrv.VideoService,
	categoryService videoSrv.VideoCategoryService,
	commentService videoSrv.VideoCommentService,
	channelService videoSrv.VideoChannelService,
	userService userSrv.UserService,
) *VideoController {
	return &VideoController{
		videoService:    videoService,
		categoryService: categoryService,
		commentService:  commentService,
		channelService:  channelService,
		userService:     userService,
	}
}

type VideoRequest struct {
	ID string `json:"id"`
}

// GetVideo 获取视频详情
func (h *VideoController) GetVideo(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	id, _ := h.GetId(c)
	// 获取视频详情
	video, err := h.videoService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "视频不存在")
	}

	//// 异步增加观看次数
	//go h.videoService.ViewVideo(c.Context(), id)

	return h.Success(c, video)
}

// ListVideos 获取视频列表
func (h *VideoController) ListVideos(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	categoryID := reqInfo.Get("categoryID").GetString()
	channelID := reqInfo.Get("channelID").GetString()
	keyword := reqInfo.Get("keyword").GetString()
	status := -999
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return err
	}
	condition := map[string]interface{}{
		"categoryID": categoryID,
		"channelID":  channelID,
		"keyword":    keyword,
		"status":     status,
	}
	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 获取视频列表
	movies, total, err := h.videoService.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取视频列表失败")
	}

	return h.SuccessList(c, movies, int64(total), page, pageSize)
}

// CreateVideo 创建视频
func (h *VideoController) CreateVideo(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 创建请求结构
	var req videoValidator.CreateVideoRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 创建视频对象
	video := &videoModel.Video{
		Title:       req.Title,
		Cover:       req.Cover,
		URL:         req.URL,
		Duration:    req.Duration,
		Resolution:  req.Resolution,
		Quality:     req.Quality,
		Format:      req.Format,
		IsPrivate:   req.IsPrivate,
		IsVIP:       req.IsVIP,
		IsPaid:      req.IsPaid,
		Price:       req.Price,
		Celebrities: types.StringArray(req.Celebrities),
		Tags:        types.StringArray(req.Tags),
	}

	// 处理描述字段
	video.SetDescription(req.Description)

	// 处理分类ID和分类名称
	if req.CategoryID != nil && *req.CategoryID != "" {
		// 查询分类是否存在
		category, err := h.categoryService.GetByID(c.Context(), *req.CategoryID, false)
		if err != nil {
			return h.NotFound(c, "分类不存在")
		}
		video.CategoryID = null.StringFrom(*req.CategoryID)
		video.CategoryName = category.Name.String
	}

	// 处理频道ID和频道名称
	if req.ChannelID != nil && *req.ChannelID != "" {
		// 查询频道是否存在
		channel, err := h.channelService.GetByID(c.Context(), *req.ChannelID, false)
		if err != nil {
			return h.NotFound(c, "频道不存在")
		}
		video.ChannelID = null.StringFrom(*req.ChannelID)
		video.ChannelName = channel.Name
	}

	// 处理专辑ID
	if req.AlbumID != nil && *req.AlbumID != "" {
		video.AlbumID = null.StringFrom(*req.AlbumID)
	}

	// 处理创建者ID和创建者信息
	if req.CreatorID != nil && *req.CreatorID != "" {
		// 查询用户是否存在
		user, err := h.userService.GetByID(c.Context(), *req.CreatorID, false)
		if err != nil {
			return h.NotFound(c, "用户不存在")
		}
		video.CreatorID = null.StringFrom(*req.CreatorID)
		video.CreatorName = user.Nickname.String
		video.CreatorAvatar = user.Avatar.String
	}

	// 创建视频
	videoID, err := h.videoService.Create(c.Context(), video)
	if err != nil {
		return h.InternalServerError(c, "创建视频失败: "+err.Error())
	}

	// 返回成功响应
	return h.Success(c, fiber.Map{
		"id":      videoID,
		"message": "创建视频成功",
	})
}

// UpdateVideo 更新视频
func (h *VideoController) UpdateVideo(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "视频ID不能为空", nil)
	}

	// 创建请求结构
	var req videos.UpdateVideoRequest

	// 使用ValidateDataWrapper验证请求
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err // 验证器已经返回了错误响应
	}

	// 获取当前视频
	video, err := h.videoService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "视频不存在")
	}

	// 更新基本字段
	if req.Title != "" {
		video.Title = req.Title
	}

	// 处理描述字段
	if req.Description != "" {
		video.SetDescription(req.Description)
	} else {
		video.Description = null.StringFrom("")
	}

	// 处理封面字段
	if req.Cover != nil {
		video.Cover = *req.Cover
	}

	// 处理分类ID，支持清空操作
	if req.CategoryID != nil {
		if *req.CategoryID != "" {
			// 查询分类是否存在
			category, err := h.categoryService.GetByID(c.Context(), *req.CategoryID, false)
			if err != nil {
				return h.NotFound(c, "分类不存在")
			}
			video.CategoryID = null.StringFrom(*req.CategoryID)
			video.CategoryName = category.Name.String
		} else {
			// 明确设置为空
			video.CategoryID = null.String{} // 设置为NULL，直接使用空结构体
			video.CategoryName = ""
		}
	} else {
		video.CategoryID = null.String{}
		video.CategoryName = ""
	}

	// 处理频道ID，支持清空操作
	if req.ChannelID != nil {
		if *req.ChannelID != "" {
			// 查询频道是否存在
			channel, err := h.channelService.GetByID(c.Context(), *req.ChannelID, false)
			if err != nil {
				return h.NotFound(c, "频道不存在")
			}
			video.ChannelID = null.StringFrom(*req.ChannelID)
			video.ChannelName = channel.Name
		} else {
			// 明确设置为空
			video.ChannelID = null.String{} // 设置为NULL，直接使用空结构体
			video.ChannelName = ""
		}
	} else {
		video.ChannelID = null.String{}
		video.ChannelName = ""
	}

	// 处理专辑ID，支持清空操作
	if req.AlbumID != nil {
		if *req.AlbumID != "" {
			video.AlbumID = null.StringFrom(*req.AlbumID)
		} else {
			// 明确设置为空
			video.AlbumID = null.String{} // 设置为NULL，直接使用空结构体
		}
	} else {
		video.AlbumID = null.String{}
		video.AlbumTitle = ""
	}

	// 处理创建者ID，支持清空操作
	if req.CreatorID != nil {
		if *req.CreatorID != "" {
			// 查询用户是否存在
			user, err := h.userService.GetByID(c.Context(), *req.CreatorID, false)
			if err != nil {
				return h.NotFound(c, "用户不存在")
			}
			video.CreatorID = null.StringFrom(*req.CreatorID)
			video.CreatorName = user.Nickname.String
			video.CreatorAvatar = user.Avatar.String
		} else {
			// 明确设置为空
			video.CreatorID = null.String{} // 设置为NULL，直接使用空结构体
			video.CreatorName = ""
			video.CreatorAvatar = ""
		}
	} else {
		video.CreatorID = null.String{}
		video.CreatorName = ""
		video.CreatorAvatar = ""
	}

	// 处理标签字段
	if req.Tags != nil {
		video.Tags = types.StringArray(req.Tags)
	}

	// 处理明星字段
	if req.Celebrities != nil {
		video.Celebrities = types.StringArray(req.Celebrities)
	}

	// 处理状态字段
	if req.Status != 0 {
		video.Status = req.Status
	}

	// 处理其他字段
	if req.IsPrivate != 0 {
		video.IsPrivate = req.IsPrivate
	}

	if req.IsVIP != 0 {
		video.IsVIP = req.IsVIP
	}

	if req.Price != 0 {
		video.Price = req.Price
	}

	// 更新视频
	err = h.videoService.UpdateById(c.Context(), id, video)
	if err != nil {
		return h.InternalServerError(c, "更新视频失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "更新视频成功")
}

// DeleteVideo 删除视频
func (h *VideoController) DeleteVideo(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "视频ID不能为空", nil)
	}

	// 删除视频
	err := h.videoService.SoftDelete(c.Context(), id)
	if err != nil {
		return h.InternalServerError(c, "删除视频失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "删除视频成功")
}

// ReviewVideo 审核视频
func (h *VideoController) ReviewVideo(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "视频ID不能为空", nil)
	}

	// 获取请求信息
	reqInfo := h.GetRequestInfo(c)
	status := -999
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return h.BadRequest(c, "状态参数错误", nil)
	}

	if status != 2 && status != -2 {
		return h.BadRequest(c, "状态参数无效，必须是2(通过)或-2(拒绝)", nil)
	}

	reason := reqInfo.Get("reason").GetString()
	if status == -2 && reason == "" {
		return h.BadRequest(c, "拒绝原因不能为空", nil)
	}

	// 获取视频
	video, err := h.videoService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "视频不存在")
	}

	// 更新视频状态
	video.Status = int8(status)
	if status == -2 {
		video.Reason = reason
	}

	// 保存更新
	err = h.videoService.Update(c.Context(), video)
	if err != nil {
		return h.InternalServerError(c, "审核视频失败: "+err.Error())
	}

	// 返回成功响应
	return h.SuccessWithMessage(c, "审核视频成功")
}

// BatchReviewVideo 批量审核视频
func (h *VideoController) BatchReviewVideo(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 解析请求
	var req struct {
		IDs    []string `json:"ids"`
		Status int      `json:"status"`
		Reason string   `json:"reason"`
	}

	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "请求参数错误", nil)
	}

	if len(req.IDs) == 0 {
		return h.BadRequest(c, "视频ID列表不能为空", nil)
	}

	if req.Status != 2 && req.Status != -2 {
		return h.BadRequest(c, "状态参数无效，必须是2(通过)或-2(拒绝)", nil)
	}

	if req.Status == -2 && req.Reason == "" {
		return h.BadRequest(c, "拒绝原因不能为空", nil)
	}

	// 批量处理
	success := 0
	failed := 0
	for _, id := range req.IDs {
		// 获取视频
		video, err := h.videoService.GetByID(c.Context(), id, false)
		if err != nil {
			failed++
			continue
		}

		// 更新视频状态
		video.Status = int8(req.Status)
		if req.Status == -2 {
			video.Reason = req.Reason
		}

		// 保存更新
		err = h.videoService.Update(c.Context(), video)
		if err != nil {
			failed++
		} else {
			success++
		}
	}

	// 返回成功响应
	return h.Success(c, fiber.Map{
		"message": "批量审核处理完成",
		"success": success,
		"failed":  failed,
	})
}

// UpdateVideoStatus 更新视频状态（上架/下架）
func (h *VideoController) UpdateVideoStatus(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "视频ID不能为空", nil)
	}

	// 获取请求信息
	var status int
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return h.BadRequest(c, "状态参数错误", nil)
	}

	if status != 1 && status != 2 {
		return h.BadRequest(c, "状态参数无效，必须是1(下架)或2(上架)", nil)
	}

	// 获取视频
	video, err := h.videoService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "视频不存在")
	}

	// 更新视频状态
	video.Status = int8(status)

	// 保存更新
	err = h.videoService.Update(c.Context(), video)
	if err != nil {
		return h.InternalServerError(c, "更新视频状态失败: "+err.Error())
	}

	// 返回成功响应
	statusText := "已上架"
	if status == 1 {
		statusText = "已下架"
	}

	return h.Success(c, fiber.Map{
		"message": "视频" + statusText,
	})
}

// BatchUpdateVideoStatus 批量更新视频状态
func (h *VideoController) BatchUpdateVideoStatus(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 解析请求
	var req struct {
		IDs    []string `json:"ids"`
		Status int      `json:"status"`
	}

	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "请求参数错误", nil)
	}

	if len(req.IDs) == 0 {
		return h.BadRequest(c, "视频ID列表不能为空", nil)
	}

	if req.Status != 1 && req.Status != 2 {
		return h.BadRequest(c, "状态参数无效，必须是1(下架)或2(上架)", nil)
	}

	// 批量处理
	success := 0
	failed := 0
	for _, id := range req.IDs {
		// 获取视频
		video, err := h.videoService.GetByID(c.Context(), id, false)
		if err != nil {
			failed++
			continue
		}

		// 更新视频状态
		video.Status = int8(req.Status)

		// 保存更新
		err = h.videoService.Update(c.Context(), video)
		if err != nil {
			failed++
		} else {
			success++
		}
	}

	// 返回成功响应
	statusText := "已上架"
	if req.Status == 1 {
		statusText = "已下架"
	}

	return h.Success(c, fiber.Map{
		"message": "批量处理完成，" + fmt.Sprintf("%d", success) + "个视频" + statusText,
		"success": success,
		"failed":  failed,
	})
}

// BatchDeleteVideo 批量删除视频
func (h *VideoController) BatchDeleteVideo(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 解析请求
	var req struct {
		IDs []string `json:"ids" validate:"required|min_len:1"`
	}

	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "请求参数错误", nil)
	}

	if len(req.IDs) == 0 {
		return h.BadRequest(c, "视频ID列表不能为空", nil)
	}
	err := h.videoService.BatchSoftDelete(c.Context(), req.IDs)
	if err != nil {
		return h.InternalServerError(c, "批量删除失败: "+err.Error())
	}
	// 返回成功响应
	return h.Success(c, fiber.Map{
		"message": "批量删除完成",
	})
}
