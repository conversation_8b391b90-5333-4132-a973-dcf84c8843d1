package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
	"github.com/guregu/null/v6"
)

// UserVideoCollection 用户视频收藏模型
type UserVideoCollection struct {
	models.BaseModel
	UserID          string         `json:"userId" gorm:"type:string;index;not null;comment:用户ID"`  //用户ID
	VideoID         null.String    `json:"videoId" gorm:"type:string;index;not null;comment:视频ID"` //视频ID
	VideoTitle      string         `json:"videoTitle" gorm:"type:string;not null;comment:视频标题"`  //视频标题
	VideoCover      string         `json:"videoCover" gorm:"type:string;comment:视频封面"`           //视频封面
	VideoDuration   int            `json:"videoDuration" gorm:"type:int;comment:视频时长(秒)"`       //视频时长(秒)
	CreatorID       null.String    `json:"creatorId" gorm:"type:string;comment:创作者ID"`            //创作者ID
	CreatorName     string         `json:"creatorName" gorm:"type:string;comment:创作者名称"`        //创作者名称
	CreatorAvatar   string         `json:"creatorAvatar" gorm:"type:string;comment:创作者头像"`      //创作者头像
	CategoryID      null.String    `json:"categoryId" gorm:"type:string;comment:分类ID"`             //分类ID
	CategoryName    string         `json:"categoryName" gorm:"type:string;comment:分类名称"`         //分类名称
	CollectionTime  types.JSONTime `json:"collectionTime" gorm:"type:datetime;comment:收藏时间"`     //收藏时间
	CollectionGroup string         `json:"collectionGroup" gorm:"type:string;comment:收藏分组"`      //收藏分组
	Note            string         `json:"note" gorm:"type:string;comment:收藏备注"`                 //收藏备注
}

// TableName 返回表名
func (UserVideoCollection) TableName() string {
	return "ly_user_video_collections"
}
