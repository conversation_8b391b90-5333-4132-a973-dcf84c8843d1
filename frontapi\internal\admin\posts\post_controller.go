package posts

import (
	"frontapi/internal/admin"
	"frontapi/internal/models"
	postModel "frontapi/internal/models/posts"
	service "frontapi/internal/service/posts"
	postValidator "frontapi/internal/validation/posts"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// PostController 帖子控制器
type PostController struct {
	PostService          service.PostService
	admin.BaseController // 继承Response
}

// NewPostController 创建帖子控制器实例
func NewPostController(postService service.PostService) *PostController {
	return &PostController{
		PostService: postService,
	}
}

// ListPosts 获取帖子列表
func (c *PostController) ListPosts(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	title := reqInfo.Get("title").GetString()
	content := reqInfo.Get("content").GetString()
	categoryID := reqInfo.Get("category_id").GetString()
	authorID := reqInfo.Get("author_id").GetString()
	status := -999
	isFeatured := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	_ = c.GetIntegerValueWithDataWrapper(ctx, "is_featured", &isFeatured)

	condition := map[string]interface{}{
		"title":       title,
		"content":     content,
		"category_id": categoryID,
		"author_id":   authorID,
		"status":      status,
	}
	// 查询帖子列表
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	postList, total, err := c.PostService.List(ctx.Context(), condition, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取帖子列表失败: "+err.Error())
	}

	// 返回帖子列表
	return c.SuccessList(ctx, postList, total, page, pageSize)
}

// GetPost 获取帖子详情
func (c *PostController) GetPost(ctx *fiber.Ctx) error {
	// 获取帖子ID
	id, _ := c.GetId(ctx)
	if id == "" {
		return c.BadRequest(ctx, "帖子ID不能为空", nil)
	}

	// 查询帖子
	post, err := c.PostService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取帖子详情失败: "+err.Error())
	}

	if post == nil {
		return c.NotFound(ctx, "帖子不存在")
	}

	// 返回帖子详情
	return c.Success(ctx, post)
}

// CreatePost 创建帖子
func (c *PostController) CreatePost(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req postValidator.CreatePostRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	post := &postModel.Post{
		ContentBaseModel: &models.ContentBaseModel{},
	}
	if err := utils.SmartCopy(req, post); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 保存帖子
	postId, err := c.PostService.Create(ctx.Context(), post)
	if err != nil {
		return c.InternalServerError(ctx, "创建帖子失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      postId,
		"message": "创建帖子成功",
	})
}

// UpdatePost 更新帖子
func (c *PostController) UpdatePost(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req postValidator.UpdatePostRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	id, _ := c.GetId(ctx)
	if id == "" {
		return c.BadRequest(ctx, "帖子ID不能为空", nil)
	}
	post := &postModel.Post{
		ContentBaseModel: &models.ContentBaseModel{},
	}
	if err := utils.SmartCopy(req, post); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 更新帖子
	err := c.PostService.UpdateById(ctx.Context(), id, post)
	if err != nil {
		return c.InternalServerError(ctx, "更新帖子失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新帖子成功")
}

// UpdatePostStatus 更新帖子状态
func (c *PostController) UpdatePostStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		ID     string `json:"id" validate:"required"`
		Status int    `json:"status" validate:"int|min:-5|max:5"`
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 检查帖子是否存在
	post, err := c.PostService.GetByID(ctx.Context(), req.ID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取帖子详情失败: "+err.Error())
	}

	if post == nil {
		return c.NotFound(ctx, "帖子不存在")
	}

	// 更新状态
	err = c.PostService.UpdateStatus(ctx.Context(), req.ID, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新帖子状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新帖子状态成功")
}

// DeletePost 删除帖子
func (c *PostController) DeletePost(ctx *fiber.Ctx) error {
	// 获取帖子ID
	id, err := c.GetId(ctx)

	if err != nil {
		return err
	}

	// 查询帖子
	post, err := c.PostService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取帖子详情失败: "+err.Error())
	}

	if post == nil {
		return c.NotFound(ctx, "帖子不存在")
	}

	// 删除帖子
	err = c.PostService.SoftDelete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除帖子失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除帖子成功")
}

// BatchUpdatePostStatus 批量更新帖子状态
func (c *PostController) BatchUpdatePostStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Ids    []string `json:"ids" validate:"required,min=1"`
		Status int      `json:"status" validate:"int|min:-5|max:5"`
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量更新状态
	err := c.PostService.BatchUpdateStatus(ctx.Context(), req.Ids, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "批量更新帖子状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量更新帖子状态成功")
}

// BatchDeletePost 批量删除帖子
func (c *PostController) BatchDeletePost(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Ids []string `json:"ids" validate:"required,min=1"`
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量删除帖子
	err := c.PostService.BatchSoftDelete(ctx.Context(), req.Ids)
	if err != nil {
		return c.InternalServerError(ctx, "批量删除帖子失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量删除帖子成功")
}

// BatchReviewPost 批量审核帖子
func (c *PostController) BatchReviewPost(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Ids    []string `json:"ids" validate:"required|min_len:1"`
		Status int      `json:"status" validate:"int|min:-5|max:5"` // 1=通过, 2=拒绝
		Reason string   `json:"reason"`
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 如果是拒绝，需要提供拒绝原因
	if req.Status == -2 && req.Reason == "" {
		return c.BadRequest(ctx, "拒绝时需要提供拒绝原因", nil)
	}

	// 批量审核帖子
	err := c.PostService.BatchUpdateColumn(ctx.Context(), req.Ids, map[string]interface{}{
		"status": req.Status,
		"reason": req.Reason,
	})
	if err != nil {
		return c.InternalServerError(ctx, "审核帖子失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "审核帖子成功")
}

// ToggleFeaturedPost 设置/取消推荐帖子
func (c *PostController) TogglePostStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		ID     string `json:"id" validate:"required"`
		Status int    `json:"is_show" validate:"required,oneof=0 1 2 -2 -4"` // 状态：0-待审核,1-影藏,2-显示,-2-已拒绝,-4-已删除
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 检查帖子是否存在
	post, err := c.PostService.GetByID(ctx.Context(), req.ID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取帖子详情失败: "+err.Error())
	}

	if post == nil {
		return c.NotFound(ctx, "帖子不存在")
	}

	// 更新状态
	err = c.PostService.UpdateStatus(ctx.Context(), req.ID, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新帖子状态失败: "+err.Error())
	}

	// 使用if-else替代三元运算符
	message := "取消推荐成功"
	if req.Status == 1 {
		message = "设置推荐成功"
	}
	return c.SuccessWithMessage(ctx, message)
}

// BatchToggleFeaturedPost 批量设置/取消推荐帖子
func (c *PostController) BatchTogglePostStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Ids    []string `json:"ids" validate:"required,min=1"`
		Status int      `json:"is_show" validate:"required,oneof=0 1 2 -2 -4"` // 状态：0-待审核,1-影藏,2-显示,-2-已拒绝,-4-已删除
	}

	if err := c.ParseRequestData(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量更新推荐状态
	err := c.PostService.BatchUpdateStatus(ctx.Context(), req.Ids, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "批量更新帖子状态失败: "+err.Error())
	}

	// 使用if-else替代三元运算符
	message := "批量取消推荐成功"
	if req.Status == 1 {
		message = "批量设置推荐成功"
	}
	return c.SuccessWithMessage(ctx, message)
}
