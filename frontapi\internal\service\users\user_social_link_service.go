package users

import (
	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreateSocialLinkRequest 创建社交链接请求
type CreateSocialLinkRequest struct {
	UserID     string `json:"userId" validate:"required"`
	Platform   string `json:"platform" validate:"required"` // 平台名称
	URL        string `json:"url" validate:"required"`      // 链接URL
	IsVerified bool   `json:"isVerified"`                   // 是否已验证
	Visibility string `json:"visibility"`                   // 可见性:public-公开,private-私密,friends-好友可见
}

// UpdateSocialLinkRequest 更新社交链接请求
type UpdateSocialLinkRequest struct {
	Platform   string `json:"platform"`
	URL        string `json:"url"`
	IsVerified bool   `json:"isVerified"`
	Visibility string `json:"visibility"`
}

// UserSocialLinkService 用户社交链接服务接口
type UserSocialLinkService interface {
	base.IExtendedService[users.UserSocialLink]
}

// userSocialLinkService 用户社交链接服务实现
type userSocialLinkService struct {
	*base.ExtendedService[users.UserSocialLink]
	socialLinkRepo repo.UserSocialLinkRepository
	userRepo       repo.UserRepository
}

// NewUserSocialLinkService 创建用户社交链接服务实例
func NewUserSocialLinkService(
	socialLinkRepo repo.UserSocialLinkRepository,
	userRepo repo.UserRepository,
) UserSocialLinkService {
	return &userSocialLinkService{
		ExtendedService: base.NewExtendedService[users.UserSocialLink](socialLinkRepo, "user_social_link"),
		socialLinkRepo:  socialLinkRepo,
		userRepo:        userRepo,
	}
}
