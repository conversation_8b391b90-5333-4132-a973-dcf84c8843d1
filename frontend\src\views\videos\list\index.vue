<template>
    <div class="videos-list-view">
        <h1 class="text-3xl font-bold mb-6">{{ $t('common.videos') }}</h1>

        <!-- 过滤和分类选项 -->
        <div class="filters mb-6 p-4 bg-surface rounded-lg shadow-sm">
            <div class="flex flex-wrap gap-4 items-center">
                <!-- 分类筛选 -->
                <div class="flex-grow min-w-[200px] sm:max-w-[200px]">
                    <Dropdown v-model="selectedCategory" :options="categories" optionLabel="name" placeholder="选择分类"
                        class="w-full" />
                </div>

                <!-- 排序方式 -->
                <div class="flex-grow min-w-[200px] sm:max-w-[200px]">
                    <Dropdown v-model="selectedSort" :options="sortOptions" optionLabel="label" placeholder="排序方式"
                        class="w-full" />
                </div>

                <!-- 时间筛选 -->
                <div class="flex-grow min-w-[200px] sm:max-w-[200px]">
                    <Dropdown v-model="selectedTimeFilter" :options="timeFilters" optionLabel="label" placeholder="时间筛选"
                        class="w-full" />
                </div>

                <!-- 付费筛选 -->
                <div class="flex items-center gap-2">
                    <label class="flex items-center cursor-pointer">
                        <Checkbox v-model="showFreeOnly" :binary="true" />
                        <span class="ml-2">仅显示免费</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- 视频列表 -->
        <div class="video-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <div v-for="video in videos" :key="video.id" class="video-card">
                <VideosListItem :video="video" @click="goToVideoDetail(video.id)" />
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex justify-center mt-8">
            <Paginator v-model:first="first" :rows="pageSize" :totalRecords="totalVideos" @page="onPageChange($event)"
                :rowsPerPageOptions="[12, 24, 48]" />
        </div>
    </div>
</template>

<script setup lang="ts">
import Checkbox from 'primevue/checkbox';
import Dropdown from 'primevue/dropdown';
import Paginator from 'primevue/paginator';
import { onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import VideosListItem from './components/VideosListItem.vue';

// 路由
const router = useRouter();

// 分类数据
const categories = ref([
    { id: 0, name: '全部分类' },
    { id: 1, name: '电影' },
    { id: 2, name: '电视剧' },
    { id: 3, name: '动画' },
    { id: 4, name: '纪录片' },
    { id: 5, name: '教育' },
    { id: 6, name: '体育' },
    { id: 7, name: '音乐' }
]);
const selectedCategory = ref(categories.value[0]);

// 排序选项
const sortOptions = ref([
    { value: 'popular', label: '最热门' },
    { value: 'latest', label: '最新上传' },
    { value: 'rating', label: '最高评分' },
    { value: 'trending', label: '近期流行' }
]);
const selectedSort = ref(sortOptions.value[0]);

// 时间筛选
const timeFilters = ref([
    { value: 'all', label: '全部时间' },
    { value: 'today', label: '今天' },
    { value: 'week', label: '本周' },
    { value: 'month', label: '本月' },
    { value: 'year', label: '今年' }
]);
const selectedTimeFilter = ref(timeFilters.value[0]);

// 免费筛选
const showFreeOnly = ref(false);

// 分页
const pageSize = ref(12);
const first = ref(0);
const currentPage = ref(1);
const totalVideos = ref(120);

// 模拟视频数据
const generateMockVideos = (count: number, offset: number = 0) => {
    return Array.from({ length: count }, (_, i) => ({
        id: offset + i + 1,
        title: `视频标题 ${offset + i + 1}`,
        thumbnail: `https://picsum.photos/seed/${offset + i + 1}/400/225`,
        duration: Math.floor(Math.random() * 3600) + 60, // 1分钟到1小时
        views: Math.floor(Math.random() * 1000000),
        uploadDate: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
        author: `创作者 ${Math.floor(Math.random() * 100) + 1}`,
        isPremium: Math.random() > 0.7
    }));
};

// 当前视频列表
const videos = ref(generateMockVideos(pageSize.value));

// 页面变更处理
const onPageChange = (event: any) => {
    first.value = event.first;
    currentPage.value = event.page + 1;
    pageSize.value = event.rows;

    // 在实际应用中，这里应该调用API加载新数据
    videos.value = generateMockVideos(pageSize.value, first.value);
};

// 监听筛选条件变化
watch([selectedCategory, selectedSort, selectedTimeFilter, showFreeOnly], () => {
    currentPage.value = 1;
    first.value = 0;

    // 在实际应用中，这里应该调用API重新加载数据
    videos.value = generateMockVideos(pageSize.value);

    // 如果只显示免费内容，过滤掉付费内容
    if (showFreeOnly.value) {
        videos.value = videos.value.filter(video => !video.isPremium);
    }
});

// 跳转到视频详情页
const goToVideoDetail = (id: number) => {
    router.push(`/videos/${id}`);
};

// 页面加载时获取数据
onMounted(() => {
    // 在实际应用中，这里应该调用API加载初始数据
    videos.value = generateMockVideos(pageSize.value);
});
</script>

<style scoped>
/* 视频卡片效果 */
.video-card {
    transition: transform 0.2s ease-in-out;
}

.video-card:hover {
    transform: translateY(-5px);
}
</style>