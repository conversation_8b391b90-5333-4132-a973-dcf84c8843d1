<template>
  <div class="modern-layout">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="gradient-orb orb-1"></div>
      <div class="gradient-orb orb-2"></div>
      <div class="gradient-orb orb-3"></div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="layout-container">
      <!-- 顶部导航 -->
      <Header v-show="layoutStore.headerVisible" class="modern-header" />
      
      <!-- 主内容区 -->
      <main class="main-content">
        <div class="content-wrapper">
          <RouterView v-slot="{ Component }">
            <Transition name="page" mode="out-in">
              <component :is="Component" />
            </Transition>
          </RouterView>
        </div>
      </main>
      
      <!-- 底部 -->
      <Footer v-show="layoutStore.headerVisible" class="modern-footer" />
    </div>
    
    <!-- 返回顶部按钮 -->
    <Transition name="fade">
      <div 
        v-show="showBackToTop" 
        class="back-to-top"
        @click="scrollToTop"
      >
        <el-icon><ArrowUp /></el-icon>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref, onMounted, onUnmounted } from 'vue'
import { ArrowUp } from '@element-plus/icons-vue'
import Header from './Header.vue'
import Footer from './Footer.vue'
import { useLayoutStore } from '@/stores/layout'

const layoutStore = useLayoutStore()
const showBackToTop = ref(false)

// 滚动监听
const handleScroll = () => {
  showBackToTop.value = window.scrollY > 300
}

// 返回顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

onMounted(() => {
  layoutStore.showHeader()
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  layoutStore.hideHeader()
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.modern-layout {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF0F5 0%, #F3E5F5 50%, #E8F5E8 100%);
  overflow-x: hidden;
}

// 背景装饰
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  
  .gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
    
    &.orb-1 {
      width: 300px;
      height: 300px;
      background: linear-gradient(135deg, #FF6B9D, #9C27B0);
      top: 10%;
      left: -10%;
      animation-delay: 0s;
    }
    
    &.orb-2 {
      width: 200px;
      height: 200px;
      background: linear-gradient(135deg, #BA68C8, #FF8A80);
      top: 60%;
      right: -5%;
      animation-delay: 2s;
    }
    
    &.orb-3 {
      width: 250px;
      height: 250px;
      background: linear-gradient(135deg, #FF4081, #CE93D8);
      bottom: 20%;
      left: 50%;
      animation-delay: 4s;
    }
  }
}

.layout-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.modern-header {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(255, 107, 157, 0.1);
  box-shadow: 0 4px 20px rgba(255, 107, 157, 0.1);
}

.main-content {
  flex: 1;
  position: relative;
  
  .content-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg) var(--spacing-md);
    
    @media (max-width: 768px) {
      padding: var(--spacing-md) var(--spacing-sm);
    }
  }
}

.modern-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 107, 157, 0.1);
}

// 返回顶部按钮
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #FF6B9D, #9C27B0);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(255, 107, 157, 0.4);
  }
  
  .el-icon {
    color: white;
    font-size: 20px;
  }
}

// 页面切换动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 浮动动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(1deg);
  }
  66% {
    transform: translateY(10px) rotate(-1deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .back-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }
  
  .background-decoration .gradient-orb {
    &.orb-1 {
      width: 200px;
      height: 200px;
    }
    
    &.orb-2 {
      width: 150px;
      height: 150px;
    }
    
    &.orb-3 {
      width: 180px;
      height: 180px;
    }
  }
}
</style>