import { postPage } from '@/shared/composables'
import { corePost, corePostPageList } from '@/shared/composables'


// 获取图片列表
export const getPictureList = (params: any) => {
    return corePostPageList('/pictures/images/getPictureList', params)
}

// 获取专辑列表
export const getAlbumList = (params: any) => {
    return corePostPageList('/pictures/albums/getAlbumList', params)
}

// 获取专辑详情
export const getAlbumDetail = (params: any) => {
    return corePost('/pictures/albums/getAlbumDetail', params)
}

// 获取专辑内的图片
export const getAlbumPictures = (params: any) => {
    return corePostPageList('/pictures/albums/getAlbumPictures', params)
}

// 获取图片详情
export const getPictureDetail = (pictureId: string) => {
    return corePost('/pictures/images/getPictureDetail', { pictureId })
}

// 获取类别列表
export const getCategoryList = () => {
    return corePostPageList('/pictures/categories/getCategoryList', {
        data: {},
        page: { page: 1, pageSize: 100 }
    })
}

// 获取推荐专辑
export const getRecommendedAlbums = (params: any) => {
    return corePost('/pictures/albums/getRecommendedAlbums', params)
}
