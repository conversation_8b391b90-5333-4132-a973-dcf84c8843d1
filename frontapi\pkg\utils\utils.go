package utils

import (
	"errors"
	"fmt"
	"github.com/guregu/null/v6"
	"strconv"
)

// ParseID 解析ID参数
func ParseID(idParam string) (int, error) {
	id, err := strconv.Atoi(idParam)
	if err != nil {
		return 0, errors.New("无效的ID参数")
	}
	return id, nil
}

// ValidateStruct 验证结构体

// ToString 将任意类型转换为字符串
func ToString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}
func ToNullString(s *string) null.String {
	if s != nil && *s != "" {
		return null.StringFrom(*s)
	}
	return null.String{}
}
