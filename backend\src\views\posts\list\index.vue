<template>
    <div class="app-container">
        <el-card>
            <template #header>
                <div class="filter-container">
                    <div class="title-container">
                        <h2>帖子管理</h2>
                        <div class="buttons">
                            <el-button type="primary" :icon="Plus" @click="handleAdd">添加帖子</el-button>
                            <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
                            <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 搜索表单组件 -->
            <PostSearchBar v-model="searchForm" @search="handleSearch" @reset="handleReset" @refresh="refreshList"
                class="mb-4" />

            <!-- 帖子表格组件 -->
            <PostTable :loading="loading" :post-list="postList" :pagination="pagination"
                @selection-change="handleSelectionChange" @view="handleView" @edit="handleEdit" @review="handleReview"
                @delete="handleDelete" @current-change="handleCurrentChange" @size-change="handleSizeChange"
                @batch-review="handleBatchReview" @batch-delete="handleBatchDelete" @batch-status="handleBatchStatus"
                @change-status="handleChangeStatus" />

            <!-- 帖子详情对话框 -->
            <PostDetailDialog v-model:visible="detailDialogVisible" :post-data="currentPost" @review="handleReview" />

            <!-- 帖子表单对话框 -->
            <PostFormDialog v-model:visible="formDialogVisible" :type="dialogType" :post-data="currentPost"
                @success="handleDialogSuccess" />

            <!-- 审核对话框 -->
            <ReviewDialog v-model:visible="reviewDialogVisible" :post-ids="reviewPostIds" :status="reviewStatus"
                :is-batch="reviewIsBatch" @confirm="handleReviewConfirm" />

            <!-- 批量操作对话框 -->
            <BatchOperationDialog v-model:visible="batchDialogVisible" :operation-type="batchOperationType"
                :selected-ids="selectedIds" @review="handleBatchReviewConfirm" @status="handleBatchStatusConfirm"
                @featured="handleBatchFeaturedConfirm" @delete="handleBatchDeleteConfirm" />
        </el-card>
    </div>
</template>

<script setup lang="ts">
import {
    batchDeletePost,
    batchReviewPost,
    batchUpdatePostStatus,
    deletePost,
    getPostDetail,
    getPostList,
    reviewPost,
    updatePostStatus
} from '@/service/api/posts/post';
import { PostItem } from '@/types/posts';
import { handleApiError } from '@/utils/errorHandler';
import { exportToExcel } from '@/utils/export';
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import {
    BatchOperationDialog,
    PostDetailDialog,
    PostFormDialog,
    PostSearchBar,
    PostTable,
    ReviewDialog
} from './components';
import type { PostSearchForm } from './components/PostSearchBar.vue';

// 搜索表单
const searchForm = reactive<PostSearchForm>({
    keyword: '',
    user_id: '',
    category_id: undefined,
    status: undefined,
    date_range: [],
});

// 分页参数
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
});

// 数据列表
const postList = ref<PostItem[]>([]);
const selectedRows = ref<PostItem[]>([]);

// 当前选中的帖子
const currentPost = ref<PostItem | null>(null);

// 加载状态
const loading = ref(false);

// 对话框状态
const detailDialogVisible = ref(false);
const formDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');

// 审核对话框状态
const reviewDialogVisible = ref(false);
const reviewPostIds = ref<string[]>([]);
const reviewStatus = ref(1);
const reviewIsBatch = ref(false);

// 批量操作对话框状态
const batchDialogVisible = ref(false);
const batchOperationType = ref<'review' | 'status' | 'featured' | 'delete'>('review');
const selectedIds = ref<string[]>([]);

// 初始化加载
onMounted(() => {
    fetchPostList();
});

// 获取帖子列表
const fetchPostList = async () => {
    loading.value = true;
    try {
        // 构建查询参数
        const params = {
            page: {
                pageNo: pagination.page,
                pageSize: pagination.pageSize
            },
            data: {
                keyword: searchForm.keyword || undefined,
                user_id: searchForm.user_id || undefined,
                category_id: searchForm.category_id || undefined,
                status: searchForm.status,
                created_at_start: searchForm.date_range[0],
                created_at_end: searchForm.date_range[1]
            }
        };

        // 添加日期范围
        if (searchForm.date_range && searchForm.date_range.length === 2 && params.data) {
            params.data = {
                ...params.data,
                created_at_start: searchForm.date_range[0],
                created_at_end: searchForm.date_range[1]
            };
        }

        const { response, data } = await getPostList(params) as any;

        if (response.status === 200 && response.data.code === 2000) {
            postList.value = Array.isArray(data.list) ? data.list : [];
            pagination.total = typeof data.total === 'number' ? data.total : 0;
        } else {
            handleApiError(response.message || '获取帖子列表失败');
        }
    } catch (error) {
        handleApiError(error, '获取帖子列表失败');
    } finally {
        loading.value = false;
    }
};

// 搜索
const handleSearch = (params: PostSearchForm) => {
    Object.assign(searchForm, params);
    pagination.page = 1;
    fetchPostList();
};

// 重置
const handleReset = () => {
    pagination.page = 1;
    fetchPostList();
};

// 刷新
const refreshList = () => {
    fetchPostList();
};

// 导出
const handleExport = () => {
    const headers = [
        { header: 'ID', key: 'id' },
        { header: '标题', key: 'title' },
        { header: '作者ID', key: 'author_id' },
        { header: '分类', key: 'category_name' },
        { header: '状态', key: 'status_text' },
        { header: '浏览量', key: 'view_count' },
        { header: '点赞数', key: 'like_count' },
        { header: '评论数', key: 'comment_count' },
        { header: '创建时间', key: 'created_at' }
    ];

    // 处理导出数据
    const exportData = postList.value.map(item => {
        let statusText = '未知';
        switch (item.status) {
            case 0: statusText = '审核中'; break;
            case 1: statusText = '已发布'; break;
            case 2: statusText = '已拒绝'; break;
            case 3: statusText = '已删除'; break;
        }

        return {
            ...item,
            status_text: statusText
        };
    });

    exportToExcel(headers, exportData, '帖子列表');
};

// 选择变化
const handleSelectionChange = (selection: PostItem[]) => {
    selectedRows.value = selection;
    selectedIds.value = selection.map(item => item.id);
};

// 添加帖子
const handleAdd = () => {
    dialogType.value = 'add';
    currentPost.value = null;
    formDialogVisible.value = true;
};

// 编辑帖子
const handleEdit = (row: PostItem) => {
    dialogType.value = 'edit';
    currentPost.value = { ...row };
    formDialogVisible.value = true;
};

// 查看帖子详情
const handleView = async (row: PostItem) => {
    try {
        loading.value = true;
        const { response, data } = await getPostDetail(row.id) as any;

        if (response.status === 200 && response.data.code === 2000) {
            currentPost.value = data;
            detailDialogVisible.value = true;
        } else {
            handleApiError(response.message || '获取帖子详情失败');
        }
    } catch (error) {
        handleApiError(error, '获取帖子详情失败');
    } finally {
        loading.value = false;
    }
};

// 更新帖子状态
const handleChangeStatus = async (id: string, status: number) => {
    try {
        loading.value = true;
        const { response } = await updatePostStatus({ id, status }) as any;

        if (response.status === 200 && response.data.code === 2000) {
            ElMessage.success(`${status === 1 ? '隐藏' : '显示'}帖子成功`);
            fetchPostList();
        } else {
            handleApiError(response.message || '更新帖子状态失败');
        }
    } catch (error) {
        handleApiError(error, '更新帖子状态失败');
    } finally {
        loading.value = false;
    }
};

// 打开审核对话框
const handleReview = (id: string, status: number, rejectReason?: string) => {
    reviewPostIds.value = [id];
    reviewStatus.value = status;
    reviewIsBatch.value = false;
    reviewDialogVisible.value = true;
};

// 审核确认
const handleReviewConfirm = async (status: number, ids: string[], reason?: string) => {
    if (ids.length === 0) return;

    try {
        loading.value = true;
        if (reviewIsBatch.value) {
            // 批量审核
            const { response } = await batchReviewPost({
                ids,
                status,
                reject_reason: reason || ''
            }) as any;

            if (response.status === 200 && response.data.code === 2000) {
                ElMessage.success(status === 1 ? '批量审核通过成功' : '批量拒绝成功');
                fetchPostList();
            } else {
                handleApiError(response.message || '批量审核失败');
            }
        } else {
            // 单个审核
            const { response } = await reviewPost({
                id: ids[0],
                status,
                reject_reason: reason || ''
            }) as any;

            if (response.status === 200 && response.data.code === 2000) {
                ElMessage.success(status === 1 ? '审核通过成功' : '拒绝成功');
                fetchPostList();
            } else {
                handleApiError(response.message || '审核失败');
            }
        }
    } catch (error) {
        handleApiError(error, reviewIsBatch.value ? '批量审核失败' : '审核失败');
    } finally {
        loading.value = false;
    }
};

// 打开批量审核对话框
const handleBatchReview = async (ids: string[], status: number, rejectReason: string) => {
    if (ids.length === 0) {
        ElMessage.warning('请先选择帖子');
        return;
    }
    if (status === -2 && rejectReason === '') {
        ElMessage.warning('请输入拒绝原因');
        return;
    }
    try {
        loading.value = true;
        const { response } = await batchReviewPost({
            ids,
            status,
            reason: rejectReason || ''
        }) as any;
        if (response.status === 200 && response.data.code === 2000) {
            ElMessage.success('操作成功');
            fetchPostList();
        } else {
            handleApiError(response.message || '操作失败');
        }
    } catch (error) {
        handleApiError(error, '操作失败');
    } finally {
        loading.value = false;
    }
};

// 批量审核确认
const handleBatchReviewConfirm = async (status: number, ids: string[], reason?: string) => {
    try {
        loading.value = true;
        const { response } = await batchReviewPost({
            ids,
            status,
            reject_reason: reason || ''
        }) as any;

        if (response.status === 200 && response.data.code === 2000) {
            ElMessage.success(status === 1 ? '批量审核通过成功' : '批量拒绝成功');
            fetchPostList();
        } else {
            handleApiError(response.message || '批量审核失败');
        }
    } catch (error) {
        handleApiError(error, '批量审核失败');
    } finally {
        loading.value = false;
    }
};

// 批量状态更新
const handleBatchStatus = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择帖子');
        return;
    }

    batchOperationType.value = 'status';
    batchDialogVisible.value = true;
};

// 批量状态确认
const handleBatchStatusConfirm = async (status: number, ids: string[]) => {
    try {
        loading.value = true;
        const { response } = await batchUpdatePostStatus({
            ids,
            status
        }) as any;

        if (response.status === 200 && response.data.code === 2000) {
            ElMessage.success(`批量${status === 1 ? '隐藏' : '显示'}帖子成功`);
            fetchPostList();
        } else {
            handleApiError(response.message || '批量更新状态失败');
        }
    } catch (error) {
        handleApiError(error, '批量更新状态失败');
    } finally {
        loading.value = false;
    }
};

// 批量推荐
const handleBatchFeatured = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择帖子');
        return;
    }

    batchOperationType.value = 'featured';
    batchDialogVisible.value = true;
};

// 批量推荐确认
const handleBatchFeaturedConfirm = async (isFeatured: number, ids: string[]) => {
    try {
        loading.value = true;
        // 这里需要根据实际API调整
        const promises = ids.map(id => toggleFeaturedPost(id, isFeatured));
        await Promise.all(promises);

        ElMessage.success(`批量${isFeatured === 1 ? '设置' : '取消'}推荐成功`);
        fetchPostList();
    } catch (error) {
        handleApiError(error, '批量更新推荐状态失败');
    } finally {
        loading.value = false;
    }
};

// 删除帖子
const handleDelete = async (row: PostItem) => {
    try {
        loading.value = true;
        const { response } = await deletePost(row.id) as any;

        if (response.status === 200 && response.data.code === 2000) {
            ElMessage.success('删除成功');
            fetchPostList();
        } else {
            handleApiError(response.message || '删除失败');
        }
    } catch (error) {
        handleApiError(error, '删除失败');
    } finally {
        loading.value = false;
    }
};

// 批量删除对话框
const handleBatchDelete = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择帖子');
        return;
    }

    batchOperationType.value = 'delete';
    batchDialogVisible.value = true;
};

// 批量删除确认
const handleBatchDeleteConfirm = async (ids: string[]) => {
    try {
        loading.value = true;
        const { response } = await batchDeletePost({ ids }) as any;

        if (response.status === 200 && response.data.code === 2000) {
            ElMessage.success('批量删除成功');
            fetchPostList();
        } else {
            handleApiError(response.message || '批量删除失败');
        }
    } catch (error) {
        handleApiError(error, '批量删除失败');
    } finally {
        loading.value = false;
    }
};

// 分页变化
const handleCurrentChange = (page: number) => {
    pagination.page = page;
    fetchPostList();
};

const handleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.page = 1;
    fetchPostList();
};

// 对话框操作成功
const handleDialogSuccess = () => {
    fetchPostList();
};
</script>

<style scoped lang="scss">
.app-container {
    padding: 20px;
}

.filter-container {
    margin-bottom: 20px;
}

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;

    h2 {
        margin: 0;
        color: #303133;
        font-weight: 600;
    }

    .buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        padding: 10px;
    }

    .title-container {
        flex-direction: column;
        align-items: stretch;
    }

    .buttons {
        justify-content: center;
    }
}
</style>

justify-content: center;