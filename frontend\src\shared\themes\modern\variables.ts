/**
 * 明快现代主题 - CSS变量
 * 蓝色主调 + 白色 + 灰色
 * 设计参考: Tailwind CSS + PrimeVue蓝色主题
 */

// CSS变量配置
const variables = {
    // 主要颜色
    'color-primary': '#1d4ed8',
    'color-primary-light': '#3b82f6',
    'color-primary-dark': '#1e40af',
    'color-primary-contrast': '#ffffff',
    'color-primary-secondary': '#60a5fa', // 第二配色 - 比主色浅的相同色系

    // 强调色
    'color-accent': '#0284c7',
    'color-accent-light': '#0ea5e9',
    'color-accent-dark': '#0369a1',
    'color-accent-contrast': '#ffffff',

    // 中性色调 - Tailwind蓝灰色系列
    'color-neutral-50': '#f8fafc',
    'color-neutral-100': '#f1f5f9',
    'color-neutral-200': '#e2e8f0',
    'color-neutral-300': '#cbd5e1',
    'color-neutral-400': '#94a3b8',
    'color-neutral-500': '#64748b',
    'color-neutral-600': '#475569',
    'color-neutral-700': '#334155',
    'color-neutral-800': '#1e293b',
    'color-neutral-900': '#0f172a',

    // 成功/错误/警告/信息色
    'color-success': '#059669',
    'color-error': '#dc2626',
    'color-warning': '#d97706',
    'color-info': '#2563eb',

    // 背景颜色
    'color-background': '#ebf8ff',
    'color-background-alt': '#e0f2fe',
    'color-background-hover': '#bae6fd',
    'color-background-card': 'linear-gradient(145deg, #f0f9ff, #e0f2fe)',
    'color-background-gradient': 'linear-gradient(135deg, #ebf8ff, #bae6fd, #7dd3fc)',
    // 主页面背景和文字色
    'color-page-background': '#f8fafc',
    'color-page-text': '#0f172a',

    // 文本颜色
    'color-text': '#0f172a',
    'color-text-light': '#1e293b',
    'color-text-lighter': '#334155',
    'color-text-contrast': '#ffffff',

    // 边框颜色
    'color-border': '#93c5fd',
    'color-border-light': '#bfdbfe',
    'color-border-dark': '#60a5fa',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    'shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',

    // 特定区域颜色
    'header-gradient': 'linear-gradient(135deg, #1d4ed8, #3b82f6)',
    'footer-gradient': 'linear-gradient(135deg, #e2e8f0, #f1f5f9)',
    'color-nav-gradient': '#ffffff',
    'color-footer-gradient': '#475569',
    'color-footer-border': '#cbd5e1',
    'button-gradient': 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
    'card-gradient': 'linear-gradient(145deg, #ffffff, #f1f5f9)',
    'accent-gradient': 'linear-gradient(135deg, #0ea5e9, #0284c7)',

    // PrimeVue集成
    'primary-color': '#1d4ed8',
    'primary-color-text': '#ffffff',
    'surface-ground': '#f8fafc',
    'surface-section': '#f8fafc',
    'surface-card': 'linear-gradient(145deg, #ffffff, #f8fafc)',
    'surface-overlay': '#ffffff',
    'surface-border': '#cbd5e1',
    'surface-hover': '#e2e8f0',
    // 主页面内容区域
    'content-bg': '#f8fafc',
    'content-text': '#0f172a',

};

export default variables;