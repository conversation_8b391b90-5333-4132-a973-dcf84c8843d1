# API 接口文档

本目录包含了 frontapi 项目中所有 API 接口的详细文档。

## 接口模块文档

- [用户管理接口](./users_api.md)
- [视频管理接口](./video_api.md)
- [帖子管理接口](./post_api.md)
- [短视频管理接口](./shortvideo_api.md)
- [图片管理接口](./picture_api.md)
- [书籍管理接口](./book_api.md)
- [漫画管理接口](./comic_api.md)
- [金币套餐接口](./coin_package_api.md) - 预留接口

## 接口规范

### 请求格式
所有接口均使用 POST 方法，请求体格式为 JSON：

```json
{
  "data": {
    // 请求参数
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 响应格式
所有接口响应格式统一：

```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    // 响应数据
  }
}
```

### 状态码说明
- `2000`: 请求成功
- `4001`: 请求参数错误
- `4003`: 权限不足
- `5000`: 服务器内部错误

## 认证说明
部分接口需要用户认证，请在请求头中携带认证信息：

```
Authorization: Bearer <token>
```