<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>短视频管理</h2>
            <div class="buttons">
              <el-button type="primary" :icon="Plus" @click="handleAdd">添加短视频</el-button>
              <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
              <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <ShortVideoSearchBar
        @search="handleSearch"
        @reset="handleReset"
        @refresh="refreshList"
      />

      <!-- 表格 -->
      <ShortVideoTable
        :shortVideoList="shortVideoList"
        :loading="loading"
        :total="total"
        v-model:currentPage="currentPage"
        v-model:pageSize="pageSize"
        @refresh="refreshList"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        />
    </el-card>

    <!-- 表单对话框 -->
    <ShortVideoFormDialog
      v-model:visible="formDialogVisible"
      :type="dialogType"
      :shortVideoData="currentShortVideo"
      @success="refreshList"
    />

    <!-- 详情对话框 -->
    <ShortVideoDetailDialog
      v-model:visible="detailDialogVisible"
      :shortVideoData="currentShortVideo"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { deleteShortVideo, getShortVideoList, updateShortVideoStatus } from '@/service/api/shortvideos/shortvideos';
import type { ShortVideo } from '@/types/shortvideos';
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import ShortVideoDetailDialog from './components/ShortVideoDetailDialog.vue';
import ShortVideoFormDialog from './components/ShortVideoFormDialog.vue';
import ShortVideoSearchBar from './components/ShortVideoSearchBar.vue';
import ShortVideoTable from './components/ShortVideoTable.vue';

// 数据列表
const shortVideoList = ref<ShortVideo[]>([]);

// 加载状态
const loading = ref(false);

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 搜索参数
const searchParams = reactive({
  keyword: '',
  category_id: '',
  creator_id: '',
  status: undefined as number | string | undefined,
  start_date: '',
  end_date: ''
});

// 对话框状态
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const currentShortVideo = ref<ShortVideo | null>(null);

// 生命周期钩子
onMounted(() => {
  refreshList();
});

// 获取数据列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      page: {
        pageNo: currentPage.value,
        pageSize: pageSize.value
      },
      data: { ...searchParams }
    };

    const { data, response } = await getShortVideoList(params) as any;

    if (response.status === 200 && response.data.code === 2000) {
      shortVideoList.value = data.list || [];
      total.value = data.total || 0;
    } else {
      ElMessage.error('获取短视频列表失败');
      shortVideoList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取短视频列表失败:', error);
    ElMessage.error('获取短视频列表失败');
    shortVideoList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 刷新列表
const refreshList = () => {
  currentPage.value = 1;
  getList();
};

// 处理搜索
const handleSearch = (params: any) => {
  Object.assign(searchParams, params);
  refreshList();
};

// 处理重置
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key as keyof typeof searchParams] = '';
  });
  refreshList();
};

// 处理添加
const handleAdd = () => {
  dialogType.value = 'add';
  currentShortVideo.value = null;
  formDialogVisible.value = true;
};

// 处理查看
const handleView = (row: ShortVideo) => {
  currentShortVideo.value = row;
  detailDialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: ShortVideo) => {
  dialogType.value = 'edit';
  currentShortVideo.value = row;
  formDialogVisible.value = true;
};

// 从详情页处理编辑
const handleEditFromDetail = () => {
  dialogType.value = 'edit';
  formDialogVisible.value = true;
};

// 处理删除
const handleDelete = async (row: ShortVideo) => {
  try {
    await deleteShortVideo(row.id);
    ElMessage.success('删除短视频成功');
    if (shortVideoList.value.length === 1 && currentPage.value > 1) {
      currentPage.value--;
    }
    getList();
  } catch (error) {
    console.error('删除短视频失败:', error);
    ElMessage.error('删除短视频失败');
  }
};

// 处理状态变更
const handleChangeStatus = async (params: { id: string; status: number }) => {
  try {
    await updateShortVideoStatus(params.id, params.status);
    ElMessage.success('更新状态成功');
    getList();
  } catch (error) {
    console.error('更新状态失败:', error);
    ElMessage.error('更新状态失败');
  }
};

// 处理导出
const handleExport = () => {
  ElMessageBox.confirm('确定要导出当前筛选条件下的短视频数据吗？', '导出确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('导出功能开发中');
    // TODO: 实现导出功能
  }).catch(() => {
    // 取消导出
  });
};
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
  
  .filter-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
}

      .buttons {
        display: flex;
        gap: 8px;
}
    }
  }
}
</style>
