# 模型结构和数据类型综合修复

## 问题描述

遇到以下错误：
1. `failed to parse field: Tags, error: unsupported data type: &[]`
2. `Table not set, please set it like: db.Model(&user) or db.Table("users")`

## 根本原因

### 1. 数据类型错误
多个模型中的`types.StringArray`字段使用了错误的GORM类型标签：
- 错误：`gorm:"type:string"` 或 `gorm:"type:text"` 或 `gorm:"type:longtext"`
- 正确：`gorm:"type:json"`

### 2. 嵌入方式不一致
- 视频模型正确使用：`*models.ContentBaseModel` (指针嵌入)
- 其他模型错误使用：`models.ContentBaseModel` (值嵌入)

## 修复内容

### 已修复的模型文件

#### 1. 短视频模型 (`frontapi/internal/models/shortvideos/shortvideo.go`)
- ✅ 修复：`models.ContentBaseModel` → `*models.ContentBaseModel`
- ✅ 修复：`Tags gorm:"type:string"` → `Tags gorm:"type:json"`

#### 2. 电子书模型 (`frontapi/internal/models/books/book.go`)
- ✅ 修复：`models.ContentBaseModel` → `*models.ContentBaseModel`
- ✅ 修复：`Tags gorm:"type:string"` → `Tags gorm:"type:json"`

#### 3. 帖子模型 (`frontapi/internal/models/posts/post.go`)
- ✅ 修复：`models.ContentBaseModel` → `*models.ContentBaseModel`
- ✅ 修复：`Images gorm:"type:text"` → `Images gorm:"type:json"`

#### 4. 帖子评论模型 (`frontapi/internal/models/posts/post_comment.go`)
- ✅ 修复：`Images gorm:"type:text"` → `Images gorm:"type:json"`

#### 5. 图片模型 (`frontapi/internal/models/pictures/picture.go`)
- ✅ 修复：`models.ContentBaseModel` → `*models.ContentBaseModel`

#### 6. 图片专辑模型 (`frontapi/internal/models/pictures/picture_album.go`)
- ✅ 修复：`models.ContentBaseModel` → `*models.ContentBaseModel`
- ✅ 修复：`TagsJSON gorm:"type:longtext"` → `TagsJSON gorm:"type:json"`

#### 7. 漫画模型 (`frontapi/internal/models/comics/comic.go`)
- ✅ 修复：`models.ContentBaseModel` → `*models.ContentBaseModel`

#### 8. 视频专辑模型 (`frontapi/internal/models/videos/video_album.go`)
- ✅ 修复：`Tags gorm:"comment:标签"` → `Tags gorm:"type:json;comment:标签"`

#### 9. 视频评论模型 (`frontapi/internal/models/videos/video_comment.go`)
- ✅ 修复：`Images gorm:"type:text"` → `Images gorm:"type:json"`

### 数据库迁移

#### 新的迁移文件
- `frontapi/internal/migrations/20250628_fix_all_tags_fields_type.go`

#### 需要执行的SQL语句
```sql
-- 修复短视频表
ALTER TABLE ly_shorts MODIFY COLUMN tags JSON COMMENT '标签';

-- 修复电子书表
ALTER TABLE ly_books MODIFY COLUMN tags JSON COMMENT '标签';

-- 修复帖子表
ALTER TABLE ly_posts MODIFY COLUMN images JSON COMMENT '帖子图片,与视频只能存在一个';

-- 修复帖子评论表
ALTER TABLE ly_posts_comments MODIFY COLUMN images JSON COMMENT '图片';

-- 修复图片专辑表
ALTER TABLE ly_picture_albums MODIFY COLUMN tags_json JSON COMMENT '标签JSON数据';

-- 修复视频专辑表
ALTER TABLE ly_video_albums MODIFY COLUMN tags JSON COMMENT '标签';

-- 修复视频评论表
ALTER TABLE ly_video_comments MODIFY COLUMN images JSON COMMENT '图片';
```

## 修复后的统一规范

### 1. ContentBaseModel 嵌入规范
所有使用ContentBaseModel的模型都应该使用指针嵌入：
```go
type ModelName struct {
    *models.ContentBaseModel  // 指针嵌入
    // 其他字段...
}
```

### 2. StringArray 字段类型规范
所有使用`types.StringArray`的字段都应该使用JSON类型：
```go
FieldName types.StringArray `json:"field_name" gorm:"type:json;comment:字段说明"`
```

## 影响和效果

- ✅ 解决了"unsupported data type: &[]"错误
- ✅ 解决了"Table not set"错误
- ✅ 统一了ContentBaseModel的嵌入方式
- ✅ 统一了StringArray字段的数据类型
- ✅ 提高了代码的一致性和可维护性

## 执行步骤

1. **代码修复**：已完成所有模型的修复
2. **数据库迁移**：执行SQL语句更新表结构
3. **测试验证**：重新启动应用验证修复效果 