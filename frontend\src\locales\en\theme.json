{"light": "Light", "dark": "Dark", "blue": "Blue", "purple": "Purple", "green": "Green", "pink": "Pink", "selectTheme": "Select Theme", "followSystem": "Follow System Theme", "themeSettings": "Theme Settings", "themeSystemDemo": "Theme System Demo", "themeSystemDescription": "This page demonstrates the new theme system based on PrimeVue and CSS variables.", "themeSelector": "Theme Selector", "themeSelectorDescription": "The theme selector component allows users to choose between different themes and toggle dark mode.", "themeVariables": "Theme Variables", "componentPreview": "Component Preview", "buttons": "Buttons", "inputs": "Input Fields", "cards": "Cards", "cardTitle": "Card Title", "cardContent": "This is a sample card component showing theme styling.", "apiUsage": "API Usage"}