{"light": "Light", "dark": "Dark", "blue": "Blue", "purple": "Purple", "green": "Green", "pink": "Pink", "modern": "Modern", "warm": "Warm", "fresh": "Fresh", "charm": "Charm", "mysterious": "Mysterious", "mysterious-light": "Mysterious Light", "mysterious-dark": "Mysterious Dark", "modern-light": "Modern Light", "modern-dark": "Modern Dark", "warm-light": "Warm Light", "warm-dark": "Warm Dark", "dark-light": "Dark Light", "dark-dark": "Dark Dark", "fresh-light": "Fresh Light", "fresh-dark": "Fresh Dark", "charm-light": "Charm Light", "charm-dark": "Charm Dark", "selectTheme": "Select Theme", "followSystem": "Follow System Theme", "themeSettings": "Theme Settings", "darkMode": "Dark Mode", "changeTheme": "Change Theme", "themeSystemDemo": "Theme System Demo", "themeSystemDescription": "This page demonstrates the new theme system based on PrimeVue and CSS variables.", "themeSelector": "Theme Selector", "themeSelectorDescription": "The theme selector component allows users to choose between different themes and toggle dark mode.", "themeVariables": "Theme Variables", "componentPreview": "Component Preview", "buttons": "Buttons", "inputs": "Input Fields", "cards": "Cards", "cardTitle": "Card Title", "cardContent": "This is a sample card component showing theme styling.", "apiUsage": "API Usage"}