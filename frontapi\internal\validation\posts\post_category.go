package posts

// PostCategoryCreateRequest 帖子分类创建请求验证模型
type PostCategoryCreateRequest struct {
	Name        string `json:"name" validate:"required|minLen:2|maxLen:50"`
	Description string `json:"description" validate:"required|minLen:5|maxLen:200"`
	Icon        string `json:"icon" validate:"url"`
	SortOrder   int    `json:"sortOrder" validate:"min:0"`
	ParentID    uint   `json:"parentId" validate:"min:0"`
}

// PostCategoryUpdateRequest 帖子分类更新请求验证模型
type PostCategoryUpdateRequest struct {
	Name        string `json:"name" validate:"minLen:2|maxLen:50"`
	Description string `json:"description" validate:"minLen:5|maxLen:200"`
	Icon        string `json:"icon" validate:"url"`
	SortOrder   int    `json:"sortOrder" validate:"min:0"`
	Status      int    `json:"status" validate:"in:0,1"` // 0:正常 1:禁用
}
