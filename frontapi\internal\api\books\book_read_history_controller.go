package books

import (
	"frontapi/internal/api"
	bookService "frontapi/internal/service/books"
)

// BookReadHistoryController 电子书阅读历史控制器
type BookReadHistoryController struct {
	api.BaseController
	readHistoryService bookService.BookReadHistoryService
}

// NewBookReadHistoryController 创建电子书阅读历史控制器
func NewBookReadHistoryController(readHistoryService bookService.BookReadHistoryService) *BookReadHistoryController {
	return &BookReadHistoryController{
		readHistoryService: readHistoryService,
	}
}
