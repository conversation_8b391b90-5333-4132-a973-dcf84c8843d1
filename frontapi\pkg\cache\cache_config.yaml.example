# 缓存系统配置示例
# 缓存系统支持Redis、文件缓存和BigCache三种缓存方式
# 可以启用本地内存缓存作为二级缓存，显著提高性能

cache:
  # 默认适配器: redis, file, bigcache
  default: "redis"
  
  # 默认缓存过期时间(秒)
  default_ttl: 3600
  
  # 默认键前缀，用于区分不同应用或环境
  default_prefix: "frontapi:"
  
  # 是否启用本地内存缓存(作为二级缓存)
  enable_local_cache: true
  
  # 本地缓存大小(MB)
  local_cache_size: 100
  
  # 本地缓存过期时间(秒)
  local_cache_ttl: 300
  
  # 是否启用压缩
  enable_compression: true
  
  # 压缩阈值(字节)，超过该值才会压缩
  compression_threshold: 1024
  
  # 是否启用分片(提高性能和并发)
  enable_sharding: true
  
  # 分片数量(建议为CPU核心数的4倍)
  shard_count: 32
  
  # 是否启用集群模式
  enable_cluster: false
  
  # Redis配置
  redis:
    # 主机地址
    host: "localhost"
    
    # 端口
    port: 6379
    
    # 密码(没有则留空)
    password: ""
    
    # 数据库索引
    db: 0
    
    # Redis键前缀
    prefix: "cache:"
    
    # 连接池大小(建议为CPU核心数的10倍)
    pool_size: 50
    
    # 最小空闲连接数
    min_idle_conns: 10
    
    # 空闲连接超时时间(秒)
    idle_timeout: 300
    
    # 最大重试次数
    max_retries: 3
    
    # 拨号超时时间(秒)
    dial_timeout: 5
    
    # 读取超时时间(秒)
    read_timeout: 3
    
    # 写入超时时间(秒)
    write_timeout: 3
    
    # 是否使用集群模式
    cluster: false
    
    # 集群节点地址列表
    addrs:
      - "localhost:7000"
      - "localhost:7001"
      - "localhost:7002"
  
  # 文件缓存配置
  file:
    # 缓存文件路径
    path: "./storage/cache"
    
    # 清理间隔(秒)
    cleanup_interval: 600
    
    # 文件权限
    file_mode: 0644
    
    # 目录权限
    dir_mode: 0755
    
    # 是否启用压缩
    enable_compression: true
  
  # BigCache配置
  bigcache:
    # 分片数(建议为2的幂次方)
    shards: 1024
    
    # 生命周期窗口(秒)
    life_window: 600
    
    # 清理窗口(秒)
    clean_window: 900
    
    # 窗口中最大条目数
    max_entries_in_window: 1000000
    
    # 最大条目大小(字节)
    max_entry_size: 500
    
    # 硬缓存最大大小(MB)
    hard_max_cache_size: 512 