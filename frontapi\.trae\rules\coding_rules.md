# 多媒体内容管理平台编码规则

## 🎯 核心原则

你是一个专业的Go语言开发助手，专门为多媒体内容管理平台项目提供代码支持。请严格遵循以下规则：

## 📝 代码风格规范

### 1. 注释和消息规范
- **所有错误消息使用英文**: `error.New("user not found")`
- **所有代码注释使用简体中文**: `// 查找用户信息`
- **长英文消息必须添加中文注释**: `return errors.New("authentication failed") // 认证失败`

### 2. 命名规范
```go
// ✅ 正确的命名
type UserService struct {}        // 服务结构体
func (s *UserService) GetUser()   // 方法名动词开头
var userCount int                 // 变量名驼峰
const MAX_RETRY_COUNT = 3         // 常量大写下划线

// ❌ 错误的命名
type userservice struct {}        // 应该大写开头
func (s *UserService) user()      // 方法名应该动词开头
var UserCount int                 // 变量名不应该大写开头
```

### 3. 函数结构
```go
// ✅ 标准函数结构
func (s *UserService) CreateUser(req *CreateUserRequest) (*User, error) {
    // 1. 参数验证
    if err := s.validator.Validate(req); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err) // 验证失败
    }
    
    // 2. 业务逻辑处理
    user := &User{
        Username: req.Username,
        Email:    req.Email,
    }
    
    // 3. 数据持久化
    if err := s.repository.Create(user); err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err) // 创建用户失败
    }
    
    // 4. 返回结果
    return user, nil
}
```

## 🏗️ 架构模式

### 1. 分层架构严格遵循
```go
// Controller层 - 只处理HTTP请求响应
func (c *UserController) CreateUser(ctx *fiber.Ctx) error {
    // 解析请求参数
    var req CreateUserRequest
    if err := ctx.BodyParser(&req); err != nil {
        return ctx.Status(400).JSON(fiber.Map{
            "code": 4000,
            "message": "invalid request body", // 请求体格式错误
        })
    }
    
    // 调用服务层
    user, err := c.userService.CreateUser(&req)
    if err != nil {
        return ctx.Status(500).JSON(fiber.Map{
            "code": 5000,
            "message": "internal server error", // 服务器内部错误
        })
    }
    
    // 返回响应
    return ctx.JSON(fiber.Map{
        "code": 2000,
        "message": "user created successfully", // 用户创建成功
        "data": user,
    })
}

// Service层 - 处理业务逻辑
func (s *UserService) CreateUser(req *CreateUserRequest) (*User, error) {
    // 业务逻辑处理
    // ...
    return s.repository.Create(user)
}

// Repository层 - 数据访问
func (r *UserRepository) Create(user *User) error {
    return r.db.Create(user).Error
}
```

### 2. 依赖注入模式
```go
// ✅ 使用构造函数注入
type UserService struct {
    repository UserRepositoryInterface // 依赖接口而非具体实现
    validator  ValidatorInterface
    logger     LoggerInterface
}

func NewUserService(
    repo UserRepositoryInterface,
    validator ValidatorInterface,
    logger LoggerInterface,
) *UserService {
    return &UserService{
        repository: repo,
        validator:  validator,
        logger:     logger,
    }
}

// ❌ 避免全局变量
var globalUserRepo *UserRepository // 不要这样做
```

## 📊 数据模型规范

### 1. 模型定义
```go
// ✅ 标准模型定义
type User struct {
    BaseModel                                                                                    // 继承基础模型
    Username  string    `json:"username" gorm:"uniqueIndex;size:50;not null" validate:"required,min=3,max=50"` // 用户名
    Email     string    `json:"email" gorm:"uniqueIndex;size:100;not null" validate:"required,email"`           // 邮箱
    Password  string    `json:"-" gorm:"size:255;not null" validate:"required,min=6"`                          // 密码（不返回给前端）
    Avatar    *string   `json:"avatar" gorm:"size:255"`                                                        // 头像URL（可为空）
    Status    int       `json:"status" gorm:"default:1;comment:用户状态 1:正常 2:禁用"`                              // 用户状态
    LastLogin *time.Time `json:"last_login"`                                                                    // 最后登录时间
}

// 表名定义
func (User) TableName() string {
    return "users"
}
```

### 2. 请求响应结构
```go
// 请求结构
type CreateUserRequest struct {
    Username string `json:"username" validate:"required,min=3,max=50"` // 用户名
    Email    string `json:"email" validate:"required,email"`           // 邮箱
    Password string `json:"password" validate:"required,min=6"`         // 密码
}

// 响应结构
type UserResponse struct {
    ID        uint      `json:"id"`         // 用户ID
    Username  string    `json:"username"`   // 用户名
    Email     string    `json:"email"`      // 邮箱
    Avatar    *string   `json:"avatar"`     // 头像
    Status    int       `json:"status"`     // 状态
    CreatedAt time.Time `json:"created_at"` // 创建时间
}
```

## 🔐 安全最佳实践

### 1. 密码处理
```go
// ✅ 密码加密
func (s *UserService) hashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    if err != nil {
        return "", fmt.Errorf("failed to hash password: %w", err) // 密码加密失败
    }
    return string(bytes), nil
}

// ✅ 密码验证
func (s *UserService) checkPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

### 2. JWT Token处理
```go
// ✅ Token生成
func (s *AuthService) GenerateToken(userID uint) (string, error) {
    claims := jwt.MapClaims{
        "user_id": userID,
        "exp":     time.Now().Add(time.Hour * 24).Unix(), // 24小时过期
        "iat":     time.Now().Unix(),
    }
    
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(s.jwtSecret))
}
```

## 🌐 API响应规范

### 1. 统一响应格式
```go
// 成功响应
func SuccessResponse(c *fiber.Ctx, data interface{}) error {
    return c.JSON(fiber.Map{
        "code":      2000,
        "message":   "success", // 操作成功
        "data":      data,
        "timestamp": time.Now().Format(time.RFC3339),
    })
}

// 错误响应
func ErrorResponse(c *fiber.Ctx, code int, message string) error {
    return c.Status(getHTTPStatus(code)).JSON(fiber.Map{
        "code":      code,
        "message":   message,
        "timestamp": time.Now().Format(time.RFC3339),
    })
}

// 分页响应
func PaginationResponse(c *fiber.Ctx, items interface{}, pagination *Pagination) error {
    return c.JSON(fiber.Map{
        "code":    2000,
        "message": "success", // 获取成功
        "data": fiber.Map{
            "items":      items,
            "pagination": pagination,
        },
        "timestamp": time.Now().Format(time.RFC3339),
    })
}
```

### 2. 状态码映射
```go
func getHTTPStatus(code int) int {
    switch {
    case code >= 2000 && code < 3000:
        return 200 // 成功
    case code >= 4000 && code < 5000:
        return 400 // 客户端错误
    case code >= 5000:
        return 500 // 服务器错误
    default:
        return 500
    }
}
```

## 🧪 测试规范

### 1. 单元测试模板
```go
func TestUserService_CreateUser(t *testing.T) {
    // 准备测试环境
    mockRepo := &MockUserRepository{}
    mockValidator := &MockValidator{}
    service := NewUserService(mockRepo, mockValidator, nil)
    
    // 测试用例
    tests := []struct {
        name    string                // 测试名称
        request *CreateUserRequest   // 输入参数
        setup   func()              // 测试准备
        want    *User               // 期望结果
        wantErr bool                // 是否期望错误
    }{
        {
            name: "successful creation", // 成功创建用户
            request: &CreateUserRequest{
                Username: "testuser",
                Email:    "<EMAIL>",
                Password: "password123",
            },
            setup: func() {
                mockValidator.On("Validate", mock.Anything).Return(nil)
                mockRepo.On("Create", mock.Anything).Return(nil)
            },
            want: &User{
                Username: "testuser",
                Email:    "<EMAIL>",
            },
            wantErr: false,
        },
    }
    
    // 执行测试
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            if tt.setup != nil {
                tt.setup()
            }
            
            got, err := service.CreateUser(tt.request)
            
            if tt.wantErr {
                assert.Error(t, err)
                return
            }
            
            assert.NoError(t, err)
            assert.Equal(t, tt.want.Username, got.Username)
            assert.Equal(t, tt.want.Email, got.Email)
        })
    }
}
```

## 🔧 错误处理规范

### 1. 错误包装
```go
// ✅ 使用fmt.Errorf包装错误
func (s *UserService) GetUser(id uint) (*User, error) {
    user, err := s.repository.FindByID(id)
    if err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, fmt.Errorf("user not found: %w", err) // 用户不存在
        }
        return nil, fmt.Errorf("failed to get user: %w", err) // 获取用户失败
    }
    return user, nil
}
```

### 2. 自定义错误类型
```go
// 业务错误类型
type BusinessError struct {
    Code    int    `json:"code"`    // 错误码
    Message string `json:"message"` // 错误消息
}

func (e *BusinessError) Error() string {
    return e.Message
}

// 常用业务错误
var (
    ErrUserNotFound     = &BusinessError{Code: 4004, Message: "user not found"}     // 用户不存在
    ErrInvalidPassword  = &BusinessError{Code: 4001, Message: "invalid password"}  // 密码错误
    ErrUserAlreadyExists = &BusinessError{Code: 4009, Message: "user already exists"} // 用户已存在
)
```

## 📚 文档注释规范

```go
// UserService 用户服务，提供用户相关的业务逻辑处理
type UserService struct {
    repository UserRepositoryInterface // 用户数据仓库
    validator  ValidatorInterface      // 数据验证器
}

// CreateUser 创建新用户
// 参数:
//   - req: 创建用户请求参数
// 返回:
//   - *User: 创建的用户信息
//   - error: 错误信息，如果创建成功则为nil
func (s *UserService) CreateUser(req *CreateUserRequest) (*User, error) {
    // 实现逻辑...
}
```

## 🚀 性能优化规范

### 1. 数据库查询优化
```go
// ✅ 使用预加载避免N+1问题
func (r *UserRepository) GetUsersWithPosts() ([]User, error) {
    var users []User
    err := r.db.Preload("Posts").Find(&users).Error
    return users, err
}

// ✅ 使用索引字段查询
func (r *UserRepository) FindByEmail(email string) (*User, error) {
    var user User
    err := r.db.Where("email = ?", email).First(&user).Error // email字段有索引
    return &user, err
}
```

### 2. 缓存使用
```go
// ✅ Redis缓存模式
func (s *UserService) GetUser(id uint) (*User, error) {
    // 先从缓存获取
    cacheKey := fmt.Sprintf("user:%d", id)
    if cached, err := s.redis.Get(cacheKey); err == nil {
        var user User
        if err := json.Unmarshal([]byte(cached), &user); err == nil {
            return &user, nil
        }
    }
    
    // 缓存未命中，从数据库获取
    user, err := s.repository.FindByID(id)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    if data, err := json.Marshal(user); err == nil {
        s.redis.Set(cacheKey, data, time.Hour) // 缓存1小时
    }
    
    return user, nil
}
```

---

**重要提醒**: 
1. 所有代码必须遵循以上规范
2. 错误消息使用英文，注释使用中文
3. 严格遵循分层架构
4. 优先使用依赖注入而非全局变量
5. 所有公共函数必须有详细注释
6. 编写对应的单元测试