<template>
  <el-dialog
    v-model="visible"
    :show-close="false"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    width="90vw"
    class="media-preview-dialog"
    append-to-body
  >
    <div class="media-preview-container">
      <!-- 关闭按钮 -->
      <button class="close-btn" @click="handleClose">
        <i class="icon-close"></i>
      </button>

      <!-- 媒体内容 -->
      <div class="media-content">
        <div
          v-for="(media, index) in mediaList"
          :key="media.id"
          v-show="index === currentIndex"
          class="media-item"
        >
          <img
            v-if="media.type === 'image'"
            :src="media.url"
            :alt="`图片 ${index + 1}`"
            class="preview-image"
          />
          <video
            v-else
            :src="media.url"
            :poster="media.thumbnail"
            controls
            class="preview-video"
          ></video>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div v-if="mediaList.length > 1" class="navigation">
        <button
          class="nav-btn prev-btn"
          :disabled="currentIndex === 0"
          @click="goToPrevious"
        >
          <i class="icon-prev"></i>
        </button>
        <button
          class="nav-btn next-btn"
          :disabled="currentIndex === mediaList.length - 1"
          @click="goToNext"
        >
          <i class="icon-next"></i>
        </button>
      </div>

      <!-- 指示器 -->
      <div v-if="mediaList.length > 1" class="indicators">
        <span class="current-indicator">
          {{ currentIndex + 1 }} / {{ mediaList.length }}
        </span>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { MediaFile } from '../../types/comment'

// 组件属性
interface Props {
  modelValue: boolean
  mediaList: MediaFile[]
  currentIndex: number
}

// 组件事件
interface Emits {
  'update:modelValue': [value: boolean]
  'update:currentIndex': [index: number]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const currentIndex = ref(0)

// 监听器
watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

watch(() => props.currentIndex, (val) => {
  currentIndex.value = val
})

// 方法
const handleClose = () => {
  visible.value = false
}

const goToPrevious = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
    emit('update:currentIndex', currentIndex.value)
  }
}

const goToNext = () => {
  if (currentIndex.value < props.mediaList.length - 1) {
    currentIndex.value++
    emit('update:currentIndex', currentIndex.value)
  }
}
</script>

<style scoped lang="scss">
:deep(.media-preview-dialog) {
  .el-dialog {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 0;
    margin: 0;
    max-width: none;
    max-height: none;
    height: 100vh;
    width: 100vw;
  }

  .el-dialog__body {
    padding: 0;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.media-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .icon-close {
      width: 20px;
      height: 20px;
      background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 18L18 6M6 6l12 12'/%3E%3C/svg%3E") no-repeat center;
      background-size: contain;
    }
  }

  .media-content {
    max-width: 90%;
    max-height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;

    .media-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 8px;
      }

      .preview-video {
        max-width: 100%;
        max-height: 100%;
        border-radius: 8px;
      }
    }
  }

  .navigation {
    .nav-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      border: none;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-50%) scale(1.1);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      i {
        width: 24px;
        height: 24px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;

        &.icon-prev {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 19l-7-7 7-7'/%3E%3C/svg%3E");
        }

        &.icon-next {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'/%3E%3C/svg%3E");
        }
      }
    }

    .prev-btn {
      left: 20px;
    }

    .next-btn {
      right: 20px;
    }
  }

  .indicators {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);

    .current-indicator {
      background: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .media-preview-container {
    .close-btn {
      top: 10px;
      right: 10px;
      width: 36px;
      height: 36px;

      .icon-close {
        width: 18px;
        height: 18px;
      }
    }

    .navigation .nav-btn {
      width: 44px;
      height: 44px;

      i {
        width: 20px;
        height: 20px;
      }
    }

    .indicators .current-indicator {
      padding: 6px 12px;
      font-size: 13px;
    }
  }
}
</style> 