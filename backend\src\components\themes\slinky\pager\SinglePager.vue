<template>
  <div class="slinky-pager">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="true"
      :small="small"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @prev-click="handlePrevClick"
      @next-click="handleNextClick"
    />
    
    <!-- 自定义跳转信息 -->
    <div v-if="showJumpInfo" class="jump-info">
      <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页，{{ total }} 条数据</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  total: number
  currentPage?: number
  pageSize?: number
  pageSizes?: number[]
  layout?: string
  background?: boolean
  small?: boolean
  disabled?: boolean
  hideOnSinglePage?: boolean
  showJumpInfo?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  pageSize: 10,
  pageSizes: () => [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true,
  small: false,
  disabled: false,
  hideOnSinglePage: false,
  showJumpInfo: false
})

interface Emits {
  (e: 'update:currentPage', page: number): void
  (e: 'update:pageSize', size: number): void
  (e: 'size-change', size: number): void
  (e: 'current-change', page: number): void
  (e: 'prev-click', page: number): void
  (e: 'next-click', page: number): void
}

const emit = defineEmits<Emits>()

// 计算属性
const currentPage = computed({
  get: () => props.currentPage,
  set: (val) => emit('update:currentPage', val)
})

const pageSize = computed({
  get: () => props.pageSize,
  set: (val) => emit('update:pageSize', val)
})

const totalPages = computed(() => {
  return Math.ceil(props.total / props.pageSize)
})

// 事件处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('size-change', size)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('current-change', page)
}

const handlePrevClick = (page: number) => {
  emit('prev-click', page)
}

const handleNextClick = (page: number) => {
  emit('next-click', page)
}
</script>

<style scoped lang="scss">
.slinky-pager {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  
  .el-pagination {
    :deep(.el-pager) {
      li {
        border-radius: var(--el-card-border-radius);
        margin: 0 2px;
        transition: all 0.3s ease;
        
        &.is-active {
          background: linear-gradient(45deg, #667eea, #764ba2);
          border-color: transparent;
          color: #fff;
        }
      }
    }
    
    :deep(.btn-prev),
    :deep(.btn-next) {
      border-radius: var(--el-card-border-radius);
      transition: all 0.3s ease;
    }
    
    :deep(.el-select) {
      .el-select__wrapper {
        border-radius: var(--el-card-border-radius);
        transition: all 0.3s ease;
        
        &.is-focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
      }
    }
    
    :deep(.el-input) {
      .el-input__wrapper {
        border-radius: var(--el-card-border-radius);
        transition: all 0.3s ease;
        
        &.is-focus {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
      }
    }
    
    :deep(.el-pagination__total),
    :deep(.el-pagination__jump) {
      color: #606266;
      font-size: 14px;
    }
  }
  
  .jump-info {
    color: #909399;
    font-size: 14px;
    white-space: nowrap;
  }
}

// 响应式布局
@media (max-width: 768px) {
  .slinky-pager {
    flex-direction: column;
    gap: 8px;
    
    .jump-info {
      order: -1;
    }
  }
}
</style> 