package comics

import (
	"frontapi/internal/models"
)

// ComicChapter 漫画章节模型
type ComicChapter struct {
	models.BaseModelStruct
	ComicID       string  `json:"comic_id" gorm:"column:comic_id;not null;index:idx_comic_id;uniqueIndex:uk_comic_chapter,priority:1" comment:"漫画ID"`
	Title         string  `json:"title" gorm:"column:title;not null" comment:"章节标题"`
	ChapterNumber int     `json:"chapter_number" gorm:"column:chapter_number;not null;uniqueIndex:uk_comic_chapter,priority:2" comment:"章节序号"`
	ReadCount     uint64  `json:"read_count" gorm:"column:read_count;default:0" comment:"阅读次数"`
	IsLocked      int8    `json:"is_locked" gorm:"column:is_locked;default:0" comment:"是否锁定：0-免费，1-付费"`
	Price         float64 `json:"price" gorm:"column:price;default:0.00" comment:"单章价格"`
	PageCount     int     `json:"page_count" gorm:"column:page_count;default:0" comment:"页数"`
	Status        int8    `json:"status" gorm:"column:status;default:1" comment:"状态：0-禁用，1-正常"`
}

// TableName 指定表名
func (ComicChapter) TableName() string {
	return "ly_comic_chapters"
}
