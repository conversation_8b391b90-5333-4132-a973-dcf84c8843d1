import { ref, onMounted } from 'vue';
import { getPictureCategoryList } from '@/service/api/pictures/pictures';
import type { PictureCategory,PictureCategoryParams } from '@/types/pictures';

export function useCategories() {
  const categoryOptions = ref<PictureCategory[]>([]);
  const loading = ref(false);

  const fetchCategories = async () => {
    loading.value = true;
    try {
      const {response,data,err} = await getPictureCategoryList({data:{}} as PictureCategoryParams) as any;
      if(response.data.code==2000){
        categoryOptions.value =data.list;
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchCategories();
  });

  return {
    categoryOptions,
    loading,
    fetchCategories
  };
}
