/**
 * 事件工具函数
 */

/**
 * 事件监听器类型
 */
export type EventListener<T = any> = (data: T) => void

/**
 * 事件监听器配置
 */
export interface EventListenerConfig {
  once?: boolean // 是否只执行一次
  passive?: boolean // 是否为被动监听器
  capture?: boolean // 是否在捕获阶段执行
  priority?: number // 优先级（数字越大优先级越高）
}

/**
 * 事件监听器信息
 */
interface EventListenerInfo<T = any> {
  id: string
  listener: EventListener<T>
  config: EventListenerConfig
  context?: any
}

/**
 * 自定义事件接口
 */
export interface CustomEvent<T = any> {
  type: string
  data: T
  timestamp: number
  target?: any
  preventDefault?: () => void
  stopPropagation?: () => void
  defaultPrevented?: boolean
  propagationStopped?: boolean
}

/**
 * 事件总线类
 */
export class EventBus {
  private listeners = new Map<string, EventListenerInfo[]>()
  private onceListeners = new Set<string>()
  private maxListeners = 100
  private debug = false

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 设置最大监听器数量
   */
  setMaxListeners(max: number): void {
    this.maxListeners = max
  }

  /**
   * 启用/禁用调试模式
   */
  setDebug(enabled: boolean): void {
    this.debug = enabled
  }

  /**
   * 添加事件监听器
   */
  on<T = any>(
    event: string,
    listener: EventListener<T>,
    config: EventListenerConfig = {},
    context?: any
  ): string {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }

    const eventListeners = this.listeners.get(event)!
    
    // 检查监听器数量限制
    if (eventListeners.length >= this.maxListeners) {
      console.warn(`Event '${event}' has reached maximum listeners (${this.maxListeners})`)
    }

    const id = this.generateId()
    const listenerInfo: EventListenerInfo<T> = {
      id,
      listener,
      config,
      context
    }

    // 根据优先级插入
    const priority = config.priority || 0
    let insertIndex = eventListeners.length
    
    for (let i = 0; i < eventListeners.length; i++) {
      if ((eventListeners[i].config.priority || 0) < priority) {
        insertIndex = i
        break
      }
    }

    eventListeners.splice(insertIndex, 0, listenerInfo)

    if (config.once) {
      this.onceListeners.add(id)
    }

    if (this.debug) {
      console.log(`Added listener for event '${event}', ID: ${id}`)
    }

    return id
  }

  /**
   * 添加一次性事件监听器
   */
  once<T = any>(
    event: string,
    listener: EventListener<T>,
    config: EventListenerConfig = {},
    context?: any
  ): string {
    return this.on(event, listener, { ...config, once: true }, context)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listenerId?: string): boolean {
    const eventListeners = this.listeners.get(event)
    if (!eventListeners) return false

    if (listenerId) {
      // 移除特定监听器
      const index = eventListeners.findIndex(info => info.id === listenerId)
      if (index !== -1) {
        eventListeners.splice(index, 1)
        this.onceListeners.delete(listenerId)
        
        if (this.debug) {
          console.log(`Removed listener ${listenerId} for event '${event}'`)
        }
        
        return true
      }
    } else {
      // 移除所有监听器
      eventListeners.forEach(info => this.onceListeners.delete(info.id))
      this.listeners.delete(event)
      
      if (this.debug) {
        console.log(`Removed all listeners for event '${event}'`)
      }
      
      return true
    }

    return false
  }

  /**
   * 移除监听器（通过监听器函数）
   */
  removeListener<T = any>(event: string, listener: EventListener<T>): boolean {
    const eventListeners = this.listeners.get(event)
    if (!eventListeners) return false

    const index = eventListeners.findIndex(info => info.listener === listener)
    if (index !== -1) {
      const info = eventListeners[index]
      eventListeners.splice(index, 1)
      this.onceListeners.delete(info.id)
      
      if (this.debug) {
        console.log(`Removed listener ${info.id} for event '${event}'`)
      }
      
      return true
    }

    return false
  }

  /**
   * 触发事件
   */
  emit<T = any>(event: string, data?: T, target?: any): boolean {
    const eventListeners = this.listeners.get(event)
    if (!eventListeners || eventListeners.length === 0) {
      if (this.debug) {
        console.log(`No listeners for event '${event}'`)
      }
      return false
    }

    const customEvent: CustomEvent<T> = {
      type: event,
      data: data as T,
      timestamp: Date.now(),
      target,
      defaultPrevented: false,
      propagationStopped: false,
      preventDefault: function() {
        this.defaultPrevented = true
      },
      stopPropagation: function() {
        this.propagationStopped = true
      }
    }

    const listenersToRemove: string[] = []
    let hasError = false

    // 复制监听器数组，避免在执行过程中被修改
    const listeners = [...eventListeners]

    for (const info of listeners) {
      if (customEvent.propagationStopped) {
        break
      }

      try {
        if (info.context) {
          info.listener.call(info.context, customEvent.data)
        } else {
          info.listener(customEvent.data)
        }

        // 如果是一次性监听器，标记为待移除
        if (this.onceListeners.has(info.id)) {
          listenersToRemove.push(info.id)
        }
      } catch (error) {
        hasError = true
        console.error(`Error in event listener for '${event}':`, error)
      }
    }

    // 移除一次性监听器
    listenersToRemove.forEach(id => {
      const index = eventListeners.findIndex(info => info.id === id)
      if (index !== -1) {
        eventListeners.splice(index, 1)
        this.onceListeners.delete(id)
      }
    })

    if (this.debug) {
      console.log(`Emitted event '${event}' to ${listeners.length} listeners`)
    }

    return !hasError
  }

  /**
   * 获取事件的监听器数量
   */
  listenerCount(event: string): number {
    const eventListeners = this.listeners.get(event)
    return eventListeners ? eventListeners.length : 0
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): string[] {
    return Array.from(this.listeners.keys())
  }

  /**
   * 获取事件的所有监听器
   */
  getListeners(event: string): EventListener[] {
    const eventListeners = this.listeners.get(event)
    return eventListeners ? eventListeners.map(info => info.listener) : []
  }

  /**
   * 清空所有监听器
   */
  clear(): void {
    this.listeners.clear()
    this.onceListeners.clear()
    
    if (this.debug) {
      console.log('Cleared all event listeners')
    }
  }

  /**
   * 检查是否有监听器
   */
  hasListeners(event?: string): boolean {
    if (event) {
      return this.listenerCount(event) > 0
    }
    return this.listeners.size > 0
  }

  /**
   * 等待事件触发
   */
  waitFor<T = any>(event: string, timeout?: number): Promise<T> {
    return new Promise((resolve, reject) => {
      let timeoutId: number | undefined
      
      const listenerId = this.once(event, (data: T) => {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        resolve(data)
      })

      if (timeout && timeout > 0) {
        timeoutId = window.setTimeout(() => {
          this.off(event, listenerId)
          reject(new Error(`Event '${event}' timeout after ${timeout}ms`))
        }, timeout)
      }
    })
  }

  /**
   * 创建事件代理
   */
  proxy(sourceEvent: string, targetEvent: string, transform?: (data: any) => any): string {
    return this.on(sourceEvent, (data) => {
      const transformedData = transform ? transform(data) : data
      this.emit(targetEvent, transformedData)
    })
  }

  /**
   * 批量添加监听器
   */
  addListeners(listeners: Record<string, EventListener>): string[] {
    const ids: string[] = []
    Object.entries(listeners).forEach(([event, listener]) => {
      ids.push(this.on(event, listener))
    })
    return ids
  }

  /**
   * 批量移除监听器
   */
  removeListeners(ids: string[]): void {
    ids.forEach(id => {
      // 查找并移除监听器
      for (const [event, listeners] of this.listeners.entries()) {
        const index = listeners.findIndex(info => info.id === id)
        if (index !== -1) {
          listeners.splice(index, 1)
          this.onceListeners.delete(id)
          break
        }
      }
    })
  }
}

/**
 * DOM事件管理器
 */
export class DOMEventManager {
  private listeners = new Map<string, {
    element: Element | Window | Document
    event: string
    listener: EventListener
    options?: AddEventListenerOptions
  }>()
  private delegatedListeners = new Map<string, {
    container: Element
    event: string
    selector: string
    listener: EventListener
    options?: AddEventListenerOptions
  }>()

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `dom_listener_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 添加DOM事件监听器
   */
  on(
    element: Element | Window | Document,
    event: string,
    listener: EventListener,
    options?: AddEventListenerOptions
  ): string {
    const id = this.generateId()
    
    element.addEventListener(event, listener, options)
    
    this.listeners.set(id, {
      element,
      event,
      listener,
      options
    })

    return id
  }

  /**
   * 移除DOM事件监听器
   */
  off(id: string): boolean {
    const listenerInfo = this.listeners.get(id)
    if (listenerInfo) {
      listenerInfo.element.removeEventListener(
        listenerInfo.event,
        listenerInfo.listener,
        listenerInfo.options
      )
      this.listeners.delete(id)
      return true
    }
    return false
  }

  /**
   * 添加事件委托监听器
   */
  delegate(
    container: Element,
    event: string,
    selector: string,
    listener: EventListener,
    options?: AddEventListenerOptions
  ): string {
    const id = this.generateId()
    
    const delegatedListener = (e: Event) => {
      const target = e.target as Element
      if (target && target.matches && target.matches(selector)) {
        listener.call(target, e)
      } else {
        // 检查父元素
        const matchedElement = target.closest(selector)
        if (matchedElement && container.contains(matchedElement)) {
          listener.call(matchedElement, e)
        }
      }
    }
    
    container.addEventListener(event, delegatedListener, options)
    
    this.delegatedListeners.set(id, {
      container,
      event,
      selector,
      listener: delegatedListener,
      options
    })

    return id
  }

  /**
   * 移除事件委托监听器
   */
  undelegate(id: string): boolean {
    const listenerInfo = this.delegatedListeners.get(id)
    if (listenerInfo) {
      listenerInfo.container.removeEventListener(
        listenerInfo.event,
        listenerInfo.listener,
        listenerInfo.options
      )
      this.delegatedListeners.delete(id)
      return true
    }
    return false
  }

  /**
   * 一次性事件监听器
   */
  once(
    element: Element | Window | Document,
    event: string,
    listener: EventListener,
    options?: AddEventListenerOptions
  ): string {
    const onceOptions = { ...options, once: true }
    return this.on(element, event, listener, onceOptions)
  }

  /**
   * 清空所有监听器
   */
  clear(): void {
    // 移除普通监听器
    this.listeners.forEach((info, id) => {
      info.element.removeEventListener(info.event, info.listener, info.options)
    })
    this.listeners.clear()

    // 移除委托监听器
    this.delegatedListeners.forEach((info, id) => {
      info.container.removeEventListener(info.event, info.listener, info.options)
    })
    this.delegatedListeners.clear()
  }

  /**
   * 获取监听器数量
   */
  getListenerCount(): { normal: number; delegated: number } {
    return {
      normal: this.listeners.size,
      delegated: this.delegatedListeners.size
    }
  }
}

/**
 * 事件工具类
 */
export class EventUtils {
  /**
   * 防抖函数
   */
  static debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number,
    immediate = false
  ): T {
    let timeoutId: number | undefined
    let lastCallTime: number
    
    return ((...args: Parameters<T>) => {
      const now = Date.now()
      const callNow = immediate && !timeoutId
      
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = window.setTimeout(() => {
        timeoutId = undefined
        if (!immediate) {
          func.apply(null, args)
        }
      }, delay)
      
      if (callNow) {
        func.apply(null, args)
      }
      
      lastCallTime = now
    }) as T
  }

  /**
   * 节流函数
   */
  static throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number,
    options: { leading?: boolean; trailing?: boolean } = {}
  ): T {
    let timeoutId: number | undefined
    let lastCallTime = 0
    let lastArgs: Parameters<T>
    
    const { leading = true, trailing = true } = options
    
    return ((...args: Parameters<T>) => {
      const now = Date.now()
      
      if (!lastCallTime && !leading) {
        lastCallTime = now
      }
      
      const remaining = delay - (now - lastCallTime)
      lastArgs = args
      
      if (remaining <= 0 || remaining > delay) {
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = undefined
        }
        lastCallTime = now
        func.apply(null, args)
      } else if (!timeoutId && trailing) {
        timeoutId = window.setTimeout(() => {
          lastCallTime = leading ? Date.now() : 0
          timeoutId = undefined
          func.apply(null, lastArgs)
        }, remaining)
      }
    }) as T
  }

  /**
   * 创建自定义事件
   */
  static createCustomEvent<T = any>(
    type: string,
    data?: T,
    options: CustomEventInit = {}
  ): globalThis.CustomEvent {
    return new globalThis.CustomEvent(type, {
      detail: data,
      bubbles: true,
      cancelable: true,
      ...options
    })
  }

  /**
   * 触发DOM事件
   */
  static trigger(
    element: Element,
    event: string | Event,
    data?: any
  ): boolean {
    let eventObj: Event
    
    if (typeof event === 'string') {
      eventObj = this.createCustomEvent(event, data)
    } else {
      eventObj = event
    }
    
    return element.dispatchEvent(eventObj)
  }

  /**
   * 等待DOM事件
   */
  static waitForEvent(
    element: Element | Window | Document,
    event: string,
    timeout?: number
  ): Promise<Event> {
    return new Promise((resolve, reject) => {
      let timeoutId: number | undefined
      
      const listener = (e: Event) => {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        element.removeEventListener(event, listener)
        resolve(e)
      }
      
      element.addEventListener(event, listener, { once: true })
      
      if (timeout && timeout > 0) {
        timeoutId = window.setTimeout(() => {
          element.removeEventListener(event, listener)
          reject(new Error(`Event '${event}' timeout after ${timeout}ms`))
        }, timeout)
      }
    })
  }

  /**
   * 检查事件支持
   */
  static isEventSupported(event: string, element?: Element): boolean {
    const testElement = element || document.createElement('div')
    const eventName = `on${event.toLowerCase()}`
    
    if (eventName in testElement) {
      return true
    }
    
    testElement.setAttribute(eventName, 'return;')
    const isSupported = typeof (testElement as any)[eventName] === 'function'
    
    if (!element) {
      testElement.remove()
    }
    
    return isSupported
  }

  /**
   * 获取事件路径
   */
  static getEventPath(event: Event): EventTarget[] {
    if ('composedPath' in event && typeof event.composedPath === 'function') {
      return event.composedPath()
    }
    
    // 回退方案
    const path: EventTarget[] = []
    let target = event.target as Element
    
    while (target) {
      path.push(target)
      target = target.parentElement as Element
    }
    
    path.push(document, window)
    return path
  }

  /**
   * 阻止事件冒泡和默认行为
   */
  static stopEvent(event: Event): void {
    event.preventDefault()
    event.stopPropagation()
    event.stopImmediatePropagation()
  }

  /**
   * 获取鼠标/触摸位置
   */
  static getPointerPosition(event: MouseEvent | TouchEvent): { x: number; y: number } {
    if ('touches' in event && event.touches.length > 0) {
      const touch = event.touches[0]
      return { x: touch.clientX, y: touch.clientY }
    }
    
    if ('clientX' in event) {
      return { x: event.clientX, y: event.clientY }
    }
    
    return { x: 0, y: 0 }
  }

  /**
   * 检查是否为移动设备触摸事件
   */
  static isTouchEvent(event: Event): event is TouchEvent {
    return 'touches' in event
  }

  /**
   * 检查是否为键盘事件
   */
  static isKeyboardEvent(event: Event): event is KeyboardEvent {
    return 'key' in event
  }

  /**
   * 检查是否为鼠标事件
   */
  static isMouseEvent(event: Event): event is MouseEvent {
    return 'button' in event && 'clientX' in event
  }

  /**
   * 获取按键组合
   */
  static getKeyCombo(event: KeyboardEvent): string {
    const keys: string[] = []
    
    if (event.ctrlKey) keys.push('Ctrl')
    if (event.altKey) keys.push('Alt')
    if (event.shiftKey) keys.push('Shift')
    if (event.metaKey) keys.push('Meta')
    
    if (event.key && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
      keys.push(event.key)
    }
    
    return keys.join('+')
  }
}

/**
 * 默认事件总线实例
 */
export const eventBus = new EventBus()

/**
 * 默认DOM事件管理器实例
 */
export const domEventManager = new DOMEventManager()

/**
 * 创建事件总线
 */
export function createEventBus(): EventBus {
  return new EventBus()
}

/**
 * 创建DOM事件管理器
 */
export function createDOMEventManager(): DOMEventManager {
  return new DOMEventManager()
}

// 导出工具实例
export const eventUtils = EventUtils

// 导出快捷方法
export const {
  debounce,
  throttle,
  createCustomEvent,
  trigger,
  waitForEvent,
  isEventSupported,
  getEventPath,
  stopEvent,
  getPointerPosition,
  isTouchEvent,
  isKeyboardEvent,
  isMouseEvent,
  getKeyCombo
} = EventUtils

// 全局事件总线快捷方法
export const on = eventBus.on.bind(eventBus)
export const once = eventBus.once.bind(eventBus)
export const off = eventBus.off.bind(eventBus)
export const emit = eventBus.emit.bind(eventBus)
export const waitFor = eventBus.waitFor.bind(eventBus)