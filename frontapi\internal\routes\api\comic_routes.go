package api

import (
	"frontapi/internal/api/comics"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterComicRoutes 注册漫画相关路由
func RegisterComicRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	//漫画分类相关路由
	category := apiGroup.Group("/comics/category")
	{
		categoryController := comics.NewComicCategoryController(services.ComicCategoryService, services.ComicService)
		category.Post("/getComicCategoryList", categoryController.GetCategoryList)
		category.Post("/getComicCategoryDetail", categoryController.GetCategoryDetail)
	}

	comicController := comics.NewComicController(services.ComicService, services.ComicChapterService, services.ComicFavoriteService, services.ComicCommentService, services.ComicReadHistoryService)
	// 漫画相关路由组
	comicsApp := apiGroup.Group("/comics")
	{
		// 公开接口
		comicsApp.Post("/getComicList", comicController.GetComicList)
		comicsApp.Post("/getComicDetail", comicController.GetComicDetail)
		comicsApp.Post("/getComicChapterList", comicController.GetChapterList)
		comicsApp.Post("/getComicChapterDetail", comicController.GetChapterDetail)
		comicsApp.Post("/getChapterContent", comicController.GetChapterContent)
		comicsApp.Post("/getRelatedComics", comicController.GetRelatedComics)
	}

	//漫画页面相关路由
	page := apiGroup.Group("/comics/page")
	{
		pageController := comics.NewComicPageController(services.ComicService, services.ComicChapterService, services.ComicFavoriteService, services.ComicCommentService, services.ComicReadHistoryService)
		page.Post("/getComicPageList", pageController.GetPageList)
		page.Post("/getComicPageDetail", pageController.GetPageDetail)
	}

	// 漫画收藏相关路由
	//下面是预留接口
	// 漫画评论相关路由组
	//comments := app.Group("/api/proadm/comic-comments")
	//{
	//	// 公开接口
	//	//comments.Post("/list", comicController.ListComments)
	//	//comments.Get("/replies", comicController.ListReplies)
	//}
	//
	//// 漫画阅读历史相关路由
	//history := app.Group("/api/proadm/comic-history")
	//{
	//	//history.Get("/", middleware.AuthRequired(), comicController.ListReadHistories)
	//}
}
