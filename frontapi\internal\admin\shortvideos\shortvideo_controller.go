package shortvideos

import (
	"frontapi/internal/admin"
	"frontapi/internal/models"
	shortModel "frontapi/internal/models/shortvideos"
	"frontapi/internal/service/shortvideos"
	userSrv "frontapi/internal/service/users"
	shortValidator "frontapi/internal/validation/shortvideos"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
	"github.com/guregu/null/v6"
)

// ShortVideoController 短视频控制器
type ShortVideoController struct {
	ShortVideoService         shortvideos.ShortVideoService
	ShortVideoCategoryService shortvideos.ShortVideoCategoryService
	ShortVideoCommentService  shortvideos.ShortVideoCommentService
	userService               userSrv.UserService
	admin.BaseController      // 继承BaseController
}

// NewShortVideoController 创建短视频控制器实例
func NewShortVideoController(
	shortVideoService shortvideos.ShortVideoService,
	shortVideoCategoryService shortvideos.ShortVideoCategoryService,
	shortVideoCommentService shortvideos.ShortVideoCommentService,
	userService userSrv.UserService,
) *ShortVideoController {
	return &ShortVideoController{
		ShortVideoService:         shortVideoService,
		ShortVideoCategoryService: shortVideoCategoryService,
		ShortVideoCommentService:  shortVideoCommentService,
		userService:               userService,
	}
}

// ListShortVideos 获取短视频列表
func (c *ShortVideoController) ListShortVideos(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	keyword := reqInfo.Get("keyword").GetString()
	categoryId := reqInfo.Get("category_id").GetString()
	status := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	condition := map[string]interface{}{
		"keyword":     keyword,
		"category_id": categoryId,
		"status":      status,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询短视频列表
	shortVideoList, total, err := c.ShortVideoService.List(ctx.Context(), condition, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取短视频列表失败: "+err.Error())
	}

	// 返回短视频列表
	return c.SuccessList(ctx, shortVideoList, total, page, pageSize)
}

// GetShortVideo 获取短视频详情
func (c *ShortVideoController) GetShortVideo(ctx *fiber.Ctx) error {
	// 获取短视频ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询短视频
	shortVideo, err := c.ShortVideoService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取短视频详情失败: "+err.Error())
	}

	if shortVideo == nil {
		return c.NotFound(ctx, "短视频不存在")
	}

	// 返回短视频详情
	return c.Success(ctx, shortVideo)
}

// CreateShortVideo 创建短视频
func (c *ShortVideoController) CreateShortVideo(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req shortValidator.CreateShortVideoRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	if err := ctx.BodyParser(&req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	var shortVideo shortModel.ShortVideo
	shortVideo.ContentBaseModel = &models.ContentBaseModel{}

	// 使用SmartCopy进行基础拷贝
	utils.SmartCopy(req, shortVideo)

	// 手动设置null.String字段
	if req.CreatorID != nil && *req.CreatorID != "" {
		shortVideo.CreatorID = null.StringFrom(*req.CreatorID)
	}
	if req.CategoryID != nil && *req.CategoryID != "" {
		shortVideo.CategoryID = null.StringFrom(*req.CategoryID)
	}

	// 如果提供了用户ID，查询用户信息并填充
	if req.CreatorID != nil && *req.CreatorID != "" {
		user, err := c.userService.GetByID(ctx.Context(), *req.CreatorID, false)
		if err != nil {
			return c.BadRequest(ctx, "指定的创作者不存在", nil)
		}
		if user != nil {
			if user.Nickname.Valid {
				shortVideo.ContentBaseModel.CreatorID = null.StringFrom(*req.CreatorID)
			}
		}
	}

	// 如果提供了分类ID，查询分类信息并填充
	if req.CategoryID != nil && *req.CategoryID != "" {
		category, err := c.ShortVideoCategoryService.GetByID(ctx.Context(), *req.CategoryID, false)
		if err != nil {
			return c.BadRequest(ctx, "指定的分类不存在", nil)
		}
		if category != nil {
			if category.Name.Valid {
				shortVideo.ContentBaseModel.CategoryName = null.StringFrom(category.Name.String)
			}
		}
	}

	// 创建短视频
	id, err := c.ShortVideoService.Create(ctx.Context(), &shortVideo)
	if err != nil {
		return c.InternalServerError(ctx, "创建短视频失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建短视频成功",
	})
}

// UpdateShortVideo 更新短视频
func (c *ShortVideoController) UpdateShortVideo(ctx *fiber.Ctx) error {
	// 获取短视频ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req shortValidator.UpdateShortVideoRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数:"+err.Error(), nil)
	}

	// 先查询现有的短视频信息
	shortVideo, err := c.ShortVideoService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.NotFound(ctx, "短视频不存在")
	}

	// 检查是否有相同标题的短视频（排除当前短视频）
	if req.Title != "" && req.Title != shortVideo.Title {
		existingVideo, err := c.ShortVideoService.FindOne(ctx.Context(), "id != ? AND title = ?", id, req.Title)
		if err != nil {
			return c.InternalServerError(ctx, "检查标题重复失败: "+err.Error())
		}
		if existingVideo != nil {
			return c.BadRequest(ctx, "已有相同标题的短视频", nil)
		}
	}

	// 使用SmartCopy更新指定字段，保留现有的统计数据
	utils.SmartCopy(req, shortVideo)

	// 手动设置null.String字段
	if req.CreatorID != nil && *req.CreatorID != "" {
		shortVideo.CreatorID = null.StringFrom(*req.CreatorID)
	}
	if req.CategoryID != nil && *req.CategoryID != "" {
		shortVideo.CategoryID = null.StringFrom(*req.CategoryID)
	}

	// 如果提供了用户ID，查询用户信息并填充
	if req.CreatorID != nil && *req.CreatorID != "" {
		user, err := c.userService.GetByID(ctx.Context(), *req.CreatorID, false)
		if err != nil {
			return c.BadRequest(ctx, "指定的创作者不存在", nil)
		}
		if user != nil {
			if user.Nickname.Valid {
				shortVideo.ContentBaseModel.CreatorID = null.StringFrom(*req.CreatorID)
			}
		}
	}

	// 如果提供了分类ID，查询分类信息并填充
	if req.CategoryID != nil && *req.CategoryID != "" {
		category, err := c.ShortVideoCategoryService.GetByID(ctx.Context(), *req.CategoryID, false)
		if err != nil {
			return c.BadRequest(ctx, "指定的分类不存在", nil)
		}
		if category != nil {
			if category.Name.Valid {
				shortVideo.ContentBaseModel.CategoryName = null.StringFrom(category.Name.String)
			}
		}
	}

	// 更新短视频
	err = c.ShortVideoService.Update(ctx.Context(), shortVideo)
	if err != nil {
		return c.InternalServerError(ctx, "更新短视频失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新短视频成功")
}

func (c *ShortVideoController) UpdateShortVideoStatus(ctx *fiber.Ctx) error {
	// 获取短视频ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}
	status := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	reqInfo := c.GetRequestInfo(ctx)
	reason := reqInfo.Get("reason").GetString()

	// 更新短视频
	err = c.ShortVideoService.ReviewShorts(ctx.Context(), id, status, reason)
	return c.SuccessWithMessage(ctx, "更新短视频状态成功")
}

// DeleteShortVideo 删除短视频
func (c *ShortVideoController) DeleteShortVideo(ctx *fiber.Ctx) error {
	// 获取短视频ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除短视频
	err = c.ShortVideoService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除短视频失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除短视频成功")
}

// BatchUpdateShortVideoStatus 批量更新短视频状态
func (c *ShortVideoController) BatchUpdateShortVideoStatus(ctx *fiber.Ctx) error {
	var req struct {
		Ids    []string `json:"ids" validate:"required"`
		Status int      `json:"status" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量更新状态
	for _, id := range req.Ids {
		video, err := c.ShortVideoService.GetByID(ctx.Context(), id, false)
		if err != nil {
			continue // 跳过不存在的视频
		}

		video.Status = int8(req.Status)
		if err := c.ShortVideoService.Update(ctx.Context(), video); err != nil {
			return c.InternalServerError(ctx, "批量更新短视频状态失败: "+err.Error())
		}
	}

	return c.SuccessWithMessage(ctx, "批量更新短视频状态成功")
}

// BatchDeleteShortVideo 批量删除短视频
func (c *ShortVideoController) BatchDeleteShortVideo(ctx *fiber.Ctx) error {
	var req struct {
		Ids []string `json:"ids" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量删除视频
	for _, id := range req.Ids {
		if err := c.ShortVideoService.Delete(ctx.Context(), id); err != nil {
			return c.InternalServerError(ctx, "批量删除短视频失败: "+err.Error())
		}
	}

	return c.SuccessWithMessage(ctx, "批量删除短视频成功")
}

// GetCommentReplies 获取评论回复列表
func (c *ShortVideoController) GetCommentReplies(ctx *fiber.Ctx) error {
	// 获取评论ID
	commentID := ctx.Params("id")
	if commentID == "" {
		return c.BadRequest(ctx, "评论ID不能为空", nil)
	}

	// 获取分页参数
	reqInfo := c.GetRequestInfo(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询评论回复
	replies, total, err := c.ShortVideoCommentService.GetReplies(ctx.Context(), commentID, page, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, "获取评论回复失败: "+err.Error())
	}

	// 返回评论回复列表
	return c.SuccessList(ctx, replies, total, page, pageSize)
}
