package videos

import (
	"frontapi/internal/models/videos"
	repo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

// VideoChannelService 接口定义
type VideoChannelService interface {
	base.IExtendedService[videos.VideoChannel]
}

// videoChannelService 实现
type videoChannelService struct {
	*base.ExtendedService[videos.VideoChannel]
	videoChannelRepo  repo.VideoChannelRepository
	videoCategoryRepo repo.VideoCategoryRepository
	videoAlbumRepo    repo.VideosAlbumsRepository
	videoRepo         repo.VideoRepository
}

// NewVideoChannelService 创建服务实例
func NewVideoChannelService(videoChannelRepo repo.VideoChannelRepository, videoCategoryRepo repo.VideoCategoryRepository, videoAlbumRepo repo.VideosAlbumsRepository, videoRepo repo.VideoRepository) VideoChannelService {
	return &videoChannelService{
		ExtendedService:   base.NewExtendedService[videos.VideoChannel](videoChannelRepo, "video_channel"),
		videoChannelRepo:  videoChannelRepo,
		videoCategoryRepo: videoCategoryRepo,
		videoAlbumRepo:    videoAlbumRepo,
		videoRepo:         videoRepo,
	}
}
