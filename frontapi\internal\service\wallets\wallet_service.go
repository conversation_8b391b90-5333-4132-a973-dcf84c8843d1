package wallets

import (
	"frontapi/internal/models/wallets"
	repo "frontapi/internal/repository/wallets"
	"frontapi/internal/service/base"
)

// WalletService 钱包服务接口
type WalletService interface {
	base.IExtendedService[wallets.Wallet]
}

// walletService 钱包服务实现
type walletService struct {
	*base.ExtendedService[wallets.Wallet]
	walletRepo repo.WalletRepository
}

// NewWalletService 创建钱包服务实例
func NewWalletService(walletRepo repo.WalletRepository) WalletService {
	return &walletService{
		ExtendedService: base.NewExtendedService[wallets.Wallet](walletRepo, "wallet"),
		walletRepo:      walletRepo,
	}
}
