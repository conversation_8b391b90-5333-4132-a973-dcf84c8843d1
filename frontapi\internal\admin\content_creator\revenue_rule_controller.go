package content_creator

import (
	"frontapi/internal/admin"
	contentCreatorModel "frontapi/internal/models/content_creator"
	contentCreatorSrv "frontapi/internal/service/content_creator"
	contentCreatorValidator "frontapi/internal/validation/content_creator"

	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

type RevenueRuleController struct {
	admin.BaseController
	RevenueRuleService contentCreatorSrv.RevenueRuleService
}

func NewRevenueRuleController(service contentCreatorSrv.RevenueRuleService) *RevenueRuleController {
	return &RevenueRuleController{RevenueRuleService: service}
}

// ListRevenueRules 获取分成规则列表
func (c *RevenueRuleController) ListRevenueRules(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	status := reqInfo.Get("status").GetString()
	condition := map[string]interface{}{
		"content_type": contentType,
		"status":       status,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询分成规则列表
	list, total, err := c.RevenueRuleService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取分成规则列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// GetRevenueRule 获取分成规则详情
func (c *RevenueRuleController) GetRevenueRule(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询分成规则
	revenueRule, err := c.RevenueRuleService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取分成规则详情失败: "+err.Error())
	}

	if revenueRule == nil {
		return c.NotFound(ctx, "分成规则不存在")
	}

	// 返回分成规则详情
	return c.Success(ctx, revenueRule)
}

// CreateRevenueRule 创建分成规则
func (c *RevenueRuleController) CreateRevenueRule(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.CreateContentRevenueRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 创建分成规则
	revenueRule := &contentCreatorModel.RevenueRule{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, revenueRule); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	id, err := c.RevenueRuleService.Create(ctx.Context(), revenueRule)
	if err != nil {
		return c.InternalServerError(ctx, "创建分成规则失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建分成规则成功",
	})
}

// UpdateRevenueRule 更新分成规则
func (c *RevenueRuleController) UpdateRevenueRule(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.UpdateContentRevenueRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新分成规则
	var revenueRule = &contentCreatorModel.RevenueRule{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, revenueRule); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	err := c.RevenueRuleService.Update(ctx.Context(), revenueRule)
	if err != nil {
		return c.InternalServerError(ctx, "更新分成规则失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新分成规则成功")
}

// DeleteRevenueRule 删除分成规则
func (c *RevenueRuleController) DeleteRevenueRule(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除分成规则
	err = c.RevenueRuleService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除分成规则失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除分成规则成功")
}
