package advs

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type Advertisement struct {
	models.BaseModelStruct
	PositionID string         `gorm:"column:position_id;type:string;not null;comment:广告位ID" json:"position_id"`                          // 广告位ID
	Title      string         `gorm:"column:title;type:varchar(100);not null;comment:广告标题" json:"title"`                                // 广告标题
	Image      string         `gorm:"column:image;type:varchar(255);not null;comment:广告图片" json:"image"`                               // 广告图片
	URL        string         `gorm:"column:url;type:varchar(255);comment:跳转链接" json:"url"`                                           // 跳转链接
	StartTime  types.JSONTime `gorm:"column:start_time;type:datetime;not null;comment:开始时间" json:"start_time"`                       // 开始时间
	EndTime    types.JSONTime `gorm:"column:end_time;type:datetime;not null;comment:结束时间" json:"end_time"`                           // 结束时间
	SortOrder  int            `gorm:"column:sort_order;type:int;not null;default:0;comment:排序" json:"sort_order"`                     // 排序
	ViewCount  int64          `gorm:"column:view_count;type:bigint;not null;default:0;comment:浏览次数" json:"view_count"`               // 浏览次数
	ClickCount int64          `gorm:"column:click_count;type:bigint;not null;default:0;comment:点击次数" json:"click_count"`             // 点击次数
}

func (Advertisement) TableName() string {
	return "ly_advertisements"
}

// 实现BaseModel接口的方法，确保Advertisement满足BaseModel约束
func (a *Advertisement) SetCreatedAt(time types.JSONTime) {
	a.BaseModelStruct.SetCreatedAt(time)
}

func (a *Advertisement) SetUpdatedAt(time types.JSONTime) {
	a.BaseModelStruct.SetUpdatedAt(time)
}

func (a *Advertisement) SetID(id string) {
	a.BaseModelStruct.SetID(id)
}

func (a *Advertisement) SetStatus(status int8) {
	a.BaseModelStruct.SetStatus(status)
}
