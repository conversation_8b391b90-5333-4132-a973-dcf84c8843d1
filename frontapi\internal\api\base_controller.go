package api

import (
	"frontapi/internal/bootstrap"
	"frontapi/internal/bootstrap/controller"
	"frontapi/internal/models/users"

	"github.com/gofiber/fiber/v2"
)

// Controller API控制器基础结构
// 采用组合设计模式，组合bootstrap.BaseController的功能
type BaseController struct {
	*bootstrap.ControllerCore
	controller.ResponseTrait
	controller.RequestTrait
}

// NewController 创建API控制器
func NewBaseController() *BaseController {
	return &BaseController{
		ControllerCore: bootstrap.NewControllerCore(),
		ResponseTrait:  controller.ResponseTrait{},
	}
}

// 获取登录的用户
func (b *BaseController) GetLoginUser(ctx *fiber.Ctx) *users.User {
	//从登录后保存的token中获取用户信息
	token := ctx.Locals("token")
	if token == nil {
		return nil
	}
	user, err := b.GetUserService().GetByID(ctx.Context(), token.(string), false)
	if err != nil {
		return nil
	}
	return user
}

// //获取登录用户ID
// func (b *BaseController) GetUserId(ctx *fiber.Ctx) (string, error) {
// 	userId, err := b.GetUserId(ctx)
// 	if err != nil {
// 		return "", err
// 	}
// 	return userId, nil
// }
