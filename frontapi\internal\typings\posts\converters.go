package posts

import (
	"frontapi/internal/models/posts"
	"frontapi/internal/models/videos"
	"frontapi/internal/typings"
	"time"
)

// formatTime 格式化时间
func formatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}

func ConvertPostInfo(post *posts.Post) PostInfo {
	postInfo := PostInfo{
		ID:           post.ID,
		AuthorID:     post.AuthorID,
		AuthorName:   post.AuthorName,
		AuthorAvatar: post.AuthorAvatar,
		Content:      post.Content,
		Images:       post.Images,
		Video:        post.Video,
		AuthorType:   int8(post.AuthorType),
		LikeCount:    int64(post.LikeCount),
		CommentCount: int64(post.CommentCount),
		ViewCount:    int64(post.ViewCount),
		Heat:         int64(post.Heat),
		CreatedAt:    formatTime(time.Time(post.CreatedAt)),
		Status:       int8(post.Status),
		IsLiked:      post.IsLiked,
	}
	if post.Author != nil {
		postInfo.Author = typings.ConvertBase<PERSON><PERSON>or(post.Author)
	}
	return postInfo
}
func ConvertPostResponse(post *posts.Post) PostResponse {
	postInfo := ConvertPostInfo(post)
	return PostResponse{
		BaseDetailResponse: typings.BaseDetailResponse{
			Code: 0,
			Msg:  "success",
		},
		Data: postInfo,
	}
}

func ConvertPostList(posts []*posts.Post) []PostInfo {
	result := make([]PostInfo, len(posts))
	for i, post := range posts {
		result[i] = ConvertPostInfo(post)
	}
	return result
}
func ConvertPostListResponse(posts []*posts.Post, total int64, pageNo int, pageSize int) PostListResponse {
	return PostListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertPostList(posts),
	}
}

func ConvertPostCommentInfo(comment *posts.PostComment) PostCommentInfo {
	postCommentInfo := PostCommentInfo{
		UserID:       comment.UserID.ValueOrZero(),
		Content:      comment.Content,
		Images:       comment.Images,
		Video:        comment.Video.ValueOrZero(),
		UserNickname: "",
		UserAvatar:   "",
		EntityID:     comment.GetID(),
		EntityType:   3,
		ParentID:     comment.ParentID.ValueOrZero(),
		RelationID:   comment.PostID.ValueOrZero(),
		Heat:         comment.Heat,
		LikeCount:    comment.LikeCount,
		ReplyCount:   comment.ReplyCount,
		CreatedAt:    formatTime(time.Time(comment.GetCreatedAt())),
		UpdatedAt:    formatTime(time.Time(comment.GetUpdatedAt())),
		Status:       int8(comment.Status),
		IsLiked:      comment.IsLiked,
	}

	if comment.Author != nil {
		postCommentInfo.Author = typings.ConvertBaseAuthor(comment.Author)
	}
	return postCommentInfo
}

func ConvertPostCommentList(comments []*posts.PostComment) []PostCommentInfo {
	result := make([]PostCommentInfo, len(comments))
	for i, comment := range comments {
		result[i] = ConvertPostCommentInfo(comment)
	}
	return result
}

func ConvertPostCommentListResponse(comments []*posts.PostComment, total int64, pageNo int, pageSize int) PostCommentListResponse {
	return PostCommentListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertPostCommentList(comments),
	}
}
func ConvertPostVideoInfo(video *videos.Video) PostVideoInfo {
	postVideoInfo := PostVideoInfo{
		ID:          video.GetID(),
		Title:       video.Title,
		Description: video.Description.String,
		Cover:       video.Thumbnail, // 使用Thumbnail作为Cover
		Duration:    video.Duration,
		Resolution:  video.Resolution,
		Quality:     "", // Quality字段不存在，设置为空字符串
		Size:        int64(video.Size),
		ViewCount:   int64(video.ViewCount),
		LikeCount:   int64(video.LikeCount),
		Heat:        0, // Heat字段不存在，设置为0
		CreatedAt:   formatTime(time.Time(video.GetCreatedAt())),
		IsLiked:     video.IsLiked,
	}
	if video.Author != nil {
		postVideoInfo.Author = typings.ConvertBaseAuthor(video.Author)
	}
	return postVideoInfo
}
func ConvertPostVideoList(videos []*videos.Video) []PostVideoInfo {
	result := make([]PostVideoInfo, len(videos))
	for i, video := range videos {
		result[i] = ConvertPostVideoInfo(video)
	}
	return result
}

func ConvertPostVideoListResponse(videos []*videos.Video, total int64, pageNo int, pageSize int) PostVideoListResponse {
	return PostVideoListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertPostVideoList(videos),
	}
}
