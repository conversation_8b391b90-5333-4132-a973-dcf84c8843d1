# v-model 错误修复文档

## 问题描述
```
[plugin:vite:vue] v-model cannot be used on a prop, because local prop bindings are not writable.
Use a v-bind binding combined with a v-on listener that emits update:x event instead.
```

## 错误原因
在Vue 3中，组件的props是只读的，不能直接使用v-model绑定到props上。这会导致编译错误。

## 修复方案

### 1. 主题分页组件修复
**文件**: `backend/src/components/themes/slinky/pager/SinglePager.vue`

**错误代码**:
```vue
<el-pagination
  v-model:current-page="currentPage"
  v-model:page-size="pageSize"
  ...
/>
```

**修复后**:
```vue
<el-pagination
  :current-page="currentPage"
  :page-size="pageSize"
  ...
/>
```

### 2. 用户分页组件修复
**文件**: `backend/src/views/users/list/components/UserPagination.vue`

**错误代码**:
```vue
<el-pagination
  v-model:current-page="currentPage"
  v-model:page-size="pageSize"
  ...
/>
```

**修复后**:
```vue
<el-pagination
  :current-page="currentPage"
  :page-size="pageSize"
  ...
/>
```

## 技术说明

### v-model 与 props 的关系
1. **props是只读的**: 在Vue 3中，子组件不能直接修改props的值
2. **v-model的本质**: v-model实际上是`:value`和`@input`事件的语法糖
3. **正确的做法**: 使用`:prop-name`绑定值，通过`@event-name`监听事件

### 正确的双向绑定模式
```vue
<!-- 父组件 -->
<ChildComponent
  v-model:current-page="page"
  v-model:page-size="size"
/>

<!-- 等价于 -->
<ChildComponent
  :current-page="page"
  :page-size="size"
  @update:current-page="page = $event"
  @update:page-size="size = $event"
/>

<!-- 子组件模板 -->
<el-pagination
  :current-page="currentPage"
  :page-size="pageSize"
  @current-change="$emit('update:currentPage', $event)"
  @size-change="$emit('update:pageSize', $event)"
/>
```

### emit 定义
```typescript
interface Emits {
  'update:currentPage': [value: number];
  'update:pageSize': [value: number];
}

const emit = defineEmits<Emits>();
```

## 最佳实践

### 1. 组件设计原则
- props用于接收数据（只读）
- emit用于向父组件发送事件
- 不要在子组件中直接修改props

### 2. 分页组件模式
```vue
<template>
  <el-pagination
    :current-page="currentPage"
    :page-size="pageSize"
    :total="total"
    @current-change="handleCurrentChange"
    @size-change="handleSizeChange"
  />
</template>

<script setup lang="ts">
interface Props {
  currentPage: number;
  pageSize: number;
  total: number;
}

interface Emits {
  'update:currentPage': [page: number];
  'update:pageSize': [size: number];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const handleCurrentChange = (page: number) => {
  emit('update:currentPage', page);
};

const handleSizeChange = (size: number) => {
  emit('update:pageSize', size);
};
</script>
```

## 修复状态
- [x] 修复 SinglePager.vue 中的v-model错误
- [x] 修复 UserPagination.vue 中的v-model错误
- [x] 创建修复文档

## 注意事项
1. 所有使用了类似模式的组件都需要检查并修复
2. 在使用Element Plus组件时，要注意哪些属性支持v-model，哪些不支持
3. 自定义组件的双向绑定要正确实现emit事件

## 相关文档
- [Vue 3 组件 Props](https://cn.vuejs.org/guide/components/props.html)
- [Vue 3 组件事件](https://cn.vuejs.org/guide/components/events.html)
- [Element Plus Pagination](https://element-plus.org/zh-CN/component/pagination.html) 