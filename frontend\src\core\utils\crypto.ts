/**
 * 加密工具函数
 */

/**
 * 哈希算法类型
 */
export type HashAlgorithm = 'SHA-1' | 'SHA-256' | 'SHA-384' | 'SHA-512'

/**
 * 对称加密算法类型
 */
export type SymmetricAlgorithm = 'AES-GCM' | 'AES-CBC' | 'AES-CTR'

/**
 * 非对称加密算法类型
 */
export type AsymmetricAlgorithm = 'RSA-OAEP' | 'RSA-PSS' | 'ECDSA' | 'ECDH'

/**
 * 密钥用途
 */
export type KeyUsage = 'encrypt' | 'decrypt' | 'sign' | 'verify' | 'deriveKey' | 'deriveBits' | 'wrapKey' | 'unwrapKey'

/**
 * 加密选项接口
 */
export interface EncryptionOptions {
  algorithm: SymmetricAlgorithm
  keyLength?: number
  iv?: Uint8Array
  additionalData?: Uint8Array
  tagLength?: number
}

/**
 * 签名选项接口
 */
export interface SignatureOptions {
  algorithm: AsymmetricAlgorithm
  hash?: HashAlgorithm
  saltLength?: number
}

/**
 * 密钥对接口
 */
export interface KeyPair {
  publicKey: CryptoKey
  privateKey: CryptoKey
}

/**
 * 加密结果接口
 */
export interface EncryptionResult {
  data: ArrayBuffer
  iv: Uint8Array
  tag?: Uint8Array
}

/**
 * 简单哈希函数（基于字符串）
 * @param str 输入字符串
 * @returns 哈希值
 */
export function simpleHash(str: string): number {
  let hash = 0
  if (str.length === 0) return hash
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return Math.abs(hash)
}

/**
 * 生成随机字符串
 * @param length 长度
 * @param charset 字符集
 * @returns 随机字符串
 */
export function generateRandomString(
  length: number,
  charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = ''
  const array = new Uint8Array(length)
  crypto.getRandomValues(array)
  
  for (let i = 0; i < length; i++) {
    result += charset[array[i] % charset.length]
  }
  
  return result
}

/**
 * 生成随机字节
 * @param length 字节长度
 * @returns 随机字节数组
 */
export function generateRandomBytes(length: number): Uint8Array {
  const array = new Uint8Array(length)
  crypto.getRandomValues(array)
  return array
}

/**
 * 生成UUID v4
 * @returns UUID字符串
 */
export function generateUUID(): string {
  const array = new Uint8Array(16)
  crypto.getRandomValues(array)
  
  // 设置版本号 (4) 和变体位
  array[6] = (array[6] & 0x0f) | 0x40
  array[8] = (array[8] & 0x3f) | 0x80
  
  const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  
  return [
    hex.slice(0, 8),
    hex.slice(8, 12),
    hex.slice(12, 16),
    hex.slice(16, 20),
    hex.slice(20, 32)
  ].join('-')
}

/**
 * 字符串转ArrayBuffer
 * @param str 字符串
 * @returns ArrayBuffer
 */
export function stringToArrayBuffer(str: string): ArrayBuffer {
  const encoder = new TextEncoder()
  return encoder.encode(str).buffer
}

/**
 * ArrayBuffer转字符串
 * @param buffer ArrayBuffer
 * @returns 字符串
 */
export function arrayBufferToString(buffer: ArrayBuffer): string {
  const decoder = new TextDecoder()
  return decoder.decode(buffer)
}

/**
 * ArrayBuffer转Base64
 * @param buffer ArrayBuffer
 * @returns Base64字符串
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

/**
 * Base64转ArrayBuffer
 * @param base64 Base64字符串
 * @returns ArrayBuffer
 */
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binary = atob(base64)
  const bytes = new Uint8Array(binary.length)
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i)
  }
  return bytes.buffer
}

/**
 * 十六进制字符串转ArrayBuffer
 * @param hex 十六进制字符串
 * @returns ArrayBuffer
 */
export function hexToArrayBuffer(hex: string): ArrayBuffer {
  const bytes = new Uint8Array(hex.length / 2)
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substr(i, 2), 16)
  }
  return bytes.buffer
}

/**
 * ArrayBuffer转十六进制字符串
 * @param buffer ArrayBuffer
 * @returns 十六进制字符串
 */
export function arrayBufferToHex(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * 计算哈希值
 * @param data 数据
 * @param algorithm 哈希算法
 * @returns Promise<ArrayBuffer>
 */
export async function hash(data: ArrayBuffer | string, algorithm: HashAlgorithm = 'SHA-256'): Promise<ArrayBuffer> {
  const buffer = typeof data === 'string' ? stringToArrayBuffer(data) : data
  return await crypto.subtle.digest(algorithm, buffer)
}

/**
 * 计算哈希值并返回十六进制字符串
 * @param data 数据
 * @param algorithm 哈希算法
 * @returns Promise<string>
 */
export async function hashToHex(data: ArrayBuffer | string, algorithm: HashAlgorithm = 'SHA-256'): Promise<string> {
  const hashBuffer = await hash(data, algorithm)
  return arrayBufferToHex(hashBuffer)
}

/**
 * 计算HMAC
 * @param key 密钥
 * @param data 数据
 * @param algorithm 哈希算法
 * @returns Promise<ArrayBuffer>
 */
export async function hmac(key: ArrayBuffer | string, data: ArrayBuffer | string, algorithm: HashAlgorithm = 'SHA-256'): Promise<ArrayBuffer> {
  const keyBuffer = typeof key === 'string' ? stringToArrayBuffer(key) : key
  const dataBuffer = typeof data === 'string' ? stringToArrayBuffer(data) : data
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBuffer,
    { name: 'HMAC', hash: algorithm },
    false,
    ['sign']
  )
  
  return await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer)
}

/**
 * 计算HMAC并返回十六进制字符串
 * @param key 密钥
 * @param data 数据
 * @param algorithm 哈希算法
 * @returns Promise<string>
 */
export async function hmacToHex(key: ArrayBuffer | string, data: ArrayBuffer | string, algorithm: HashAlgorithm = 'SHA-256'): Promise<string> {
  const hmacBuffer = await hmac(key, data, algorithm)
  return arrayBufferToHex(hmacBuffer)
}

/**
 * 生成对称密钥
 * @param algorithm 算法
 * @param keyLength 密钥长度
 * @param usages 密钥用途
 * @returns Promise<CryptoKey>
 */
export async function generateSymmetricKey(
  algorithm: SymmetricAlgorithm = 'AES-GCM',
  keyLength = 256,
  usages: KeyUsage[] = ['encrypt', 'decrypt']
): Promise<CryptoKey> {
  return await crypto.subtle.generateKey(
    { name: algorithm, length: keyLength },
    true,
    usages
  )
}

/**
 * 生成非对称密钥对
 * @param algorithm 算法
 * @param keyLength 密钥长度（RSA）或曲线名称（ECDSA/ECDH）
 * @param usages 密钥用途
 * @returns Promise<KeyPair>
 */
export async function generateAsymmetricKeyPair(
  algorithm: AsymmetricAlgorithm = 'RSA-OAEP',
  keyLength: number | string = 2048,
  usages: KeyUsage[] = ['encrypt', 'decrypt']
): Promise<KeyPair> {
  let algorithmParams: any
  
  if (algorithm.startsWith('RSA')) {
    algorithmParams = {
      name: algorithm,
      modulusLength: keyLength as number,
      publicExponent: new Uint8Array([1, 0, 1]),
      hash: 'SHA-256'
    }
  } else if (algorithm === 'ECDSA' || algorithm === 'ECDH') {
    algorithmParams = {
      name: algorithm,
      namedCurve: keyLength as string || 'P-256'
    }
  }
  
  return await crypto.subtle.generateKey(algorithmParams, true, usages) as KeyPair
}

/**
 * 导入密钥
 * @param keyData 密钥数据
 * @param algorithm 算法
 * @param format 格式
 * @param usages 用途
 * @returns Promise<CryptoKey>
 */
export async function importKey(
  keyData: ArrayBuffer | JsonWebKey,
  algorithm: string | object,
  format: KeyFormat = 'raw',
  usages: KeyUsage[] = ['encrypt', 'decrypt']
): Promise<CryptoKey> {
  return await crypto.subtle.importKey(format, keyData, algorithm, true, usages)
}

/**
 * 导出密钥
 * @param key 密钥
 * @param format 格式
 * @returns Promise<ArrayBuffer | JsonWebKey>
 */
export async function exportKey(key: CryptoKey, format: KeyFormat = 'raw'): Promise<ArrayBuffer | JsonWebKey> {
  return await crypto.subtle.exportKey(format, key)
}

/**
 * 对称加密
 * @param data 数据
 * @param key 密钥
 * @param options 选项
 * @returns Promise<EncryptionResult>
 */
export async function encrypt(
  data: ArrayBuffer | string,
  key: CryptoKey,
  options: EncryptionOptions = { algorithm: 'AES-GCM' }
): Promise<EncryptionResult> {
  const dataBuffer = typeof data === 'string' ? stringToArrayBuffer(data) : data
  const iv = options.iv || generateRandomBytes(12) // GCM推荐12字节IV
  
  let algorithmParams: any = { name: options.algorithm, iv }
  
  if (options.algorithm === 'AES-GCM') {
    if (options.additionalData) {
      algorithmParams.additionalData = options.additionalData
    }
    if (options.tagLength) {
      algorithmParams.tagLength = options.tagLength
    }
  }
  
  const encrypted = await crypto.subtle.encrypt(algorithmParams, key, dataBuffer)
  
  return {
    data: encrypted,
    iv,
    tag: options.algorithm === 'AES-GCM' ? new Uint8Array(encrypted.slice(-16)) : undefined
  }
}

/**
 * 对称解密
 * @param encryptedData 加密数据
 * @param key 密钥
 * @param iv 初始化向量
 * @param options 选项
 * @returns Promise<ArrayBuffer>
 */
export async function decrypt(
  encryptedData: ArrayBuffer,
  key: CryptoKey,
  iv: Uint8Array,
  options: Partial<EncryptionOptions> = { algorithm: 'AES-GCM' }
): Promise<ArrayBuffer> {
  let algorithmParams: any = { name: options.algorithm, iv }
  
  if (options.algorithm === 'AES-GCM') {
    if (options.additionalData) {
      algorithmParams.additionalData = options.additionalData
    }
    if (options.tagLength) {
      algorithmParams.tagLength = options.tagLength
    }
  }
  
  return await crypto.subtle.decrypt(algorithmParams, key, encryptedData)
}

/**
 * 数字签名
 * @param data 数据
 * @param privateKey 私钥
 * @param options 选项
 * @returns Promise<ArrayBuffer>
 */
export async function sign(
  data: ArrayBuffer | string,
  privateKey: CryptoKey,
  options: SignatureOptions = { algorithm: 'RSA-PSS', hash: 'SHA-256' }
): Promise<ArrayBuffer> {
  const dataBuffer = typeof data === 'string' ? stringToArrayBuffer(data) : data
  
  let algorithmParams: any = { name: options.algorithm }
  
  if (options.algorithm === 'RSA-PSS') {
    algorithmParams.saltLength = options.saltLength || 32
  }
  
  if (options.hash) {
    algorithmParams.hash = options.hash
  }
  
  return await crypto.subtle.sign(algorithmParams, privateKey, dataBuffer)
}

/**
 * 验证签名
 * @param signature 签名
 * @param data 原始数据
 * @param publicKey 公钥
 * @param options 选项
 * @returns Promise<boolean>
 */
export async function verify(
  signature: ArrayBuffer,
  data: ArrayBuffer | string,
  publicKey: CryptoKey,
  options: SignatureOptions = { algorithm: 'RSA-PSS', hash: 'SHA-256' }
): Promise<boolean> {
  const dataBuffer = typeof data === 'string' ? stringToArrayBuffer(data) : data
  
  let algorithmParams: any = { name: options.algorithm }
  
  if (options.algorithm === 'RSA-PSS') {
    algorithmParams.saltLength = options.saltLength || 32
  }
  
  if (options.hash) {
    algorithmParams.hash = options.hash
  }
  
  return await crypto.subtle.verify(algorithmParams, publicKey, signature, dataBuffer)
}

/**
 * 密钥派生（PBKDF2）
 * @param password 密码
 * @param salt 盐值
 * @param iterations 迭代次数
 * @param keyLength 密钥长度
 * @param hash 哈希算法
 * @returns Promise<ArrayBuffer>
 */
export async function deriveKey(
  password: string,
  salt: ArrayBuffer | Uint8Array,
  iterations = 100000,
  keyLength = 256,
  hash: HashAlgorithm = 'SHA-256'
): Promise<ArrayBuffer> {
  const passwordBuffer = stringToArrayBuffer(password)
  const saltBuffer = salt instanceof Uint8Array ? salt.buffer : salt
  
  const baseKey = await crypto.subtle.importKey(
    'raw',
    passwordBuffer,
    'PBKDF2',
    false,
    ['deriveKey']
  )
  
  const derivedKey = await crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: saltBuffer,
      iterations,
      hash
    },
    baseKey,
    { name: 'AES-GCM', length: keyLength },
    true,
    ['encrypt', 'decrypt']
  )
  
  return await crypto.subtle.exportKey('raw', derivedKey)
}

/**
 * 简单的密码加密（基于密码的加密）
 * @param data 数据
 * @param password 密码
 * @param iterations 迭代次数
 * @returns Promise<{encrypted: string, salt: string, iv: string}>
 */
export async function encryptWithPassword(
  data: string,
  password: string,
  iterations = 100000
): Promise<{ encrypted: string; salt: string; iv: string }> {
  const salt = generateRandomBytes(16)
  const iv = generateRandomBytes(12)
  
  const keyMaterial = await deriveKey(password, salt, iterations)
  const key = await crypto.subtle.importKey(
    'raw',
    keyMaterial,
    'AES-GCM',
    false,
    ['encrypt']
  )
  
  const result = await encrypt(data, key, { algorithm: 'AES-GCM', iv })
  
  return {
    encrypted: arrayBufferToBase64(result.data),
    salt: arrayBufferToBase64(salt.buffer),
    iv: arrayBufferToBase64(iv.buffer)
  }
}

/**
 * 简单的密码解密
 * @param encrypted 加密数据
 * @param salt 盐值
 * @param iv 初始化向量
 * @param password 密码
 * @param iterations 迭代次数
 * @returns Promise<string>
 */
export async function decryptWithPassword(
  encrypted: string,
  salt: string,
  iv: string,
  password: string,
  iterations = 100000
): Promise<string> {
  const encryptedBuffer = base64ToArrayBuffer(encrypted)
  const saltBuffer = base64ToArrayBuffer(salt)
  const ivBuffer = new Uint8Array(base64ToArrayBuffer(iv))
  
  const keyMaterial = await deriveKey(password, saltBuffer, iterations)
  const key = await crypto.subtle.importKey(
    'raw',
    keyMaterial,
    'AES-GCM',
    false,
    ['decrypt']
  )
  
  const decrypted = await decrypt(encryptedBuffer, key, ivBuffer, { algorithm: 'AES-GCM' })
  return arrayBufferToString(decrypted)
}

/**
 * 生成密码哈希（用于存储）
 * @param password 密码
 * @param salt 盐值（可选）
 * @param iterations 迭代次数
 * @returns Promise<{hash: string, salt: string}>
 */
export async function hashPassword(
  password: string,
  salt?: string,
  iterations = 100000
): Promise<{ hash: string; salt: string }> {
  const saltBytes = salt ? base64ToArrayBuffer(salt) : generateRandomBytes(16).buffer
  const hashBuffer = await deriveKey(password, saltBytes, iterations, 256)
  
  return {
    hash: arrayBufferToBase64(hashBuffer),
    salt: arrayBufferToBase64(saltBytes)
  }
}

/**
 * 验证密码
 * @param password 密码
 * @param hash 存储的哈希
 * @param salt 盐值
 * @param iterations 迭代次数
 * @returns Promise<boolean>
 */
export async function verifyPassword(
  password: string,
  hash: string,
  salt: string,
  iterations = 100000
): Promise<boolean> {
  const result = await hashPassword(password, salt, iterations)
  return result.hash === hash
}

/**
 * 安全比较两个字符串（防止时序攻击）
 * @param a 字符串A
 * @param b 字符串B
 * @returns 是否相等
 */
export function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false
  }
  
  let result = 0
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i)
  }
  
  return result === 0
}

/**
 * 生成安全的随机密码
 * @param length 长度
 * @param includeSymbols 是否包含符号
 * @returns 随机密码
 */
export function generateSecurePassword(length = 16, includeSymbols = true): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const numbers = '0123456789'
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
  
  let charset = lowercase + uppercase + numbers
  if (includeSymbols) {
    charset += symbols
  }
  
  return generateRandomString(length, charset)
}

/**
 * 检查密码强度
 * @param password 密码
 * @returns 强度分数（0-100）
 */
export function checkPasswordStrength(password: string): number {
  let score = 0
  
  // 长度检查
  if (password.length >= 8) score += 25
  if (password.length >= 12) score += 25
  
  // 字符类型检查
  if (/[a-z]/.test(password)) score += 10
  if (/[A-Z]/.test(password)) score += 10
  if (/[0-9]/.test(password)) score += 10
  if (/[^A-Za-z0-9]/.test(password)) score += 20
  
  return Math.min(score, 100)
}