<template>
  <el-dialog
    :model-value="visible"
    :title="form.id ? '编辑章节' : '新增章节'"
    width="80vw"
    @close="$emit('close')"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入章节标题"></el-input>
      </el-form-item>

      <el-form-item label="内容" prop="content">
        <WangEditor
          v-model="form.content"
          :height="450"
          :placeholder="'请输入章节内容...'"
          :upload-img-server="'/api/common/upload'"
          :mode="editorMode"
          :editor-config="editorConfig"
          @ready="handleEditorReady"
          @change="handleEditorChange"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="章节序号" prop="chapter_number">
            <el-input-number v-model="form.chapter_number" :min="props.bookId?1:props.maxChapterNumber+1" :max="999"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="付费" prop="is_locked">
            <el-switch
              v-model="form.is_locked"
              :active-value="1"
              :inactive-value="0"
              active-text="付费章节"
              inactive-text="免费章节"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="价格" prop="price" v-if="form.is_locked === 1">
            <el-input-number v-model="form.price" :min="0" :precision="2" :step="0.1" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="editor-toolbar">
        <el-button-group>
          <el-button size="small" @click="toggleEditorMode">
            {{ editorMode === 'simple' ? '切换到完整模式' : '切换到简洁模式' }}
          </el-button>
          <el-button size="small" @click="insertTemplate('章节标题')">插入章节标题</el-button>
          <el-button size="small" @click="insertTemplate('分隔线')">插入分隔线</el-button>
          <el-button size="small" @click="insertTemplate('注释')">插入注释</el-button>
        </el-button-group>
        <span class="word-count">当前字数: {{ wordCount }}</span>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { WangEditor } from '@/components/wEditor';

const props = defineProps<{
  visible: boolean;
  chapter?: any;
  bookId: string;
  bookName: string;
  maxChapterNumber: number;
}>();

console.log("props",props);

const emit = defineEmits(['submit', 'close']);
const formRef = ref<FormInstance>();
const submitLoading = ref(false);
const editorMode = ref('default');
const editorInstance = ref<any>(null);
const wordCount = ref(0);

// 富文本编辑器配置
const editorConfig = reactive({
  placeholder: '请输入章节内容...',
  MENU_CONF: {
    uploadImage: {
      server: '/api/common/upload',
      fieldName: 'file',
      maxFileSize: 5 * 1024 * 1024, // 5MB
      maxNumberOfFiles: 10,
      customInsert(res: any, insertFn: Function) {
        if (res.code === 2000 && res.data && res.data.url) {
          insertFn(res.data.url, res.data.alt || '', res.data.href || '');
        } else {
          ElMessage.error('图片上传失败');
        }
      }
    }
  }
});

// 表单数据
const form = ref({
  id: '',
  book_id: props.bookId,
  book_name: props.bookName,
  title: '',
  content: '',
  chapter_number: 1,
  is_locked: 0,
  price: 0,
  status: 1,
  word_count: 0
});

// 表单验证规则
const rules = reactive<FormRules>({
  title: [{ required: true, message: '请输入章节标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入章节内容', trigger: 'blur' }],
  sort_order: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
});

// 监听chapter变化，更新表单数据
watch(() => props.chapter, (val) => {
  if (val) {
    form.value = { 
      ...val,
      // 确保is_locked和price字段为数字类型
      is_locked: typeof val.is_locked === 'number' ? val.is_locked : 0,
      price: typeof val.price === 'number' ? val.price : 0
    };
    
    // 计算字数
    calculateWordCount(form.value.content || '');
  } else {
    resetForm();
  }
}, { immediate: true });

// 富文本编辑器就绪事件
const handleEditorReady = (editor: any) => {
  editorInstance.value = editor;
  calculateWordCount(form.value.content || '');
};

// 编辑器内容变更处理
const handleEditorChange = (html: string) => {
  form.value.content = html;
  calculateWordCount(html);
};

// 计算字数
const calculateWordCount = (html: string) => {
  if (!html) {
    wordCount.value = 0;
    return;
  }
  
  // 创建临时元素解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  const text = tempDiv.textContent || tempDiv.innerText || '';
  
  // 计算字数（中英文都算一个字）
  wordCount.value = text.replace(/\s+/g, '').length;
};

// 切换编辑器模式
const toggleEditorMode = () => {
  editorMode.value = editorMode.value === 'default' ? 'simple' : 'default';
};

// 插入模板内容
const insertTemplate = (type: string) => {
  if (!editorInstance.value) return;
  
  let content = '';
  switch (type) {
    case '章节标题':
      content = '<h2 style="text-align:center;margin:20px 0;">第x章 章节标题</h2>';
      break;
    case '分隔线':
      content = '<hr style="border:none;border-top:1px dashed #ccc;margin:20px 0;">';
      break;
    case '注释':
      content = '<p style="color:#888;font-size:14px;background:#f8f8f8;padding:10px;border-left:4px solid #ddd;"><i>注释：这里是注释内容</i></p>';
      break;
    default:
      return;
  }
  
  editorInstance.value.setHtml(editorInstance.value.getHtml() + content);
};

// 重置表单
function resetForm() {
  form.value = {
    id: '',
    book_id: props.bookId,
    book_name: props.bookName,
    title: '',
    content: '',
    chapter_number: 1,
    is_locked: 0,
    price: 0,
    status: 1,
    word_count: 0
  };
  wordCount.value = 0;
}

// 提交表单前处理
function beforeSubmit() {
  // 如果不是付费章节，价格设为0
  if (form.value.is_locked !== 1) {
    form.value.price = 0;
  }
  
  // 更新章节字数
  form.value.word_count = wordCount.value;
  
  return { ...form.value,book_name:props.bookName,book_id:props.bookId };
}

// 提交表单
async function submitForm() {
  if (!formRef.value) return;
  
  await formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true;
      try {
        const submitData = beforeSubmit();
        console.log("submitData",submitData);
        emit('submit', submitData);
      } finally {
        submitLoading.value = false;
      }
    } else {
      ElMessage.warning('请完善表单信息');
    }
  });
}
</script>

<style scoped>
.el-form-item__content {
  line-height: normal;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  margin-bottom: 20px;
}

.word-count {
  font-size: 14px;
  color: #606266;
}
</style>