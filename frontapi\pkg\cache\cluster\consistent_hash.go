package cluster

import (
	"hash/crc32"
	"sort"
	"strconv"
	"sync"
)

// ConsistentHash 一致性哈希实现
type ConsistentHash struct {
	ring       map[uint32]string
	sortedKeys []uint32
	replicas   int
	mu         sync.RWMutex
}

// NewConsistentHash 创建一致性哈希环
func NewConsistentHash(replicas int) *ConsistentHash {
	if replicas <= 0 {
		replicas = 100
	}
	return &ConsistentHash{
		ring:       make(map[uint32]string),
		sortedKeys: make([]uint32, 0),
		replicas:   replicas,
	}
}

// Add 添加节点到哈希环
func (h *ConsistentHash) Add(nodeID string) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 为每个节点创建多个虚拟节点
	for i := 0; i < h.replicas; i++ {
		key := h.hashKey(nodeID + ":" + strconv.Itoa(i))
		h.ring[key] = nodeID
		h.sortedKeys = append(h.sortedKeys, key)
	}

	// 保持有序
	sort.Slice(h.sortedKeys, func(i, j int) bool {
		return h.sortedKeys[i] < h.sortedKeys[j]
	})
}

// Remove 从哈希环移除节点
func (h *ConsistentHash) Remove(nodeID string) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// 移除所有虚拟节点
	for i := 0; i < h.replicas; i++ {
		key := h.hashKey(nodeID + ":" + strconv.Itoa(i))
		delete(h.ring, key)
	}

	// 重建有序键列表
	h.sortedKeys = make([]uint32, 0, len(h.ring))
	for k := range h.ring {
		h.sortedKeys = append(h.sortedKeys, k)
	}
	sort.Slice(h.sortedKeys, func(i, j int) bool {
		return h.sortedKeys[i] < h.sortedKeys[j]
	})
}

// Get 根据键获取节点
func (h *ConsistentHash) Get(key string) string {
	if len(h.ring) == 0 {
		return ""
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	// 计算键的哈希值
	hash := h.hashKey(key)

	// 二分查找找到第一个大于等于hash的索引
	idx := sort.Search(len(h.sortedKeys), func(i int) bool {
		return h.sortedKeys[i] >= hash
	})

	// 如果找不到，则回到环的开始位置
	if idx == len(h.sortedKeys) {
		idx = 0
	}

	// 返回对应节点
	return h.ring[h.sortedKeys[idx]]
}

// GetN 获取N个不同的节点
func (h *ConsistentHash) GetN(key string, n int) []string {
	if len(h.ring) == 0 {
		return []string{}
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	// 如果请求的节点数大于实际节点数，则调整n
	uniqueNodes := make(map[string]struct{})
	for _, node := range h.ring {
		uniqueNodes[node] = struct{}{}
	}

	if n > len(uniqueNodes) {
		n = len(uniqueNodes)
	}

	// 计算键的哈希值
	hash := h.hashKey(key)

	// 二分查找找到第一个大于等于hash的索引
	idx := sort.Search(len(h.sortedKeys), func(i int) bool {
		return h.sortedKeys[i] >= hash
	})

	// 如果找不到，则回到环的开始位置
	if idx == len(h.sortedKeys) {
		idx = 0
	}

	// 收集n个不同的节点
	result := make([]string, 0, n)
	seen := make(map[string]struct{})

	for len(result) < n {
		nodeID := h.ring[h.sortedKeys[idx]]

		// 确保节点唯一
		if _, exists := seen[nodeID]; !exists {
			seen[nodeID] = struct{}{}
			result = append(result, nodeID)
		}

		// 移动到下一个位置
		idx = (idx + 1) % len(h.sortedKeys)
	}

	return result
}

// GetNodes 获取所有节点
func (h *ConsistentHash) GetNodes() []string {
	h.mu.RLock()
	defer h.mu.RUnlock()

	uniqueNodes := make(map[string]struct{})
	for _, node := range h.ring {
		uniqueNodes[node] = struct{}{}
	}

	nodes := make([]string, 0, len(uniqueNodes))
	for node := range uniqueNodes {
		nodes = append(nodes, node)
	}

	return nodes
}

// hashKey 计算键的哈希值
func (h *ConsistentHash) hashKey(key string) uint32 {
	return crc32.ChecksumIEEE([]byte(key))
}

// GetReplicas 获取副本数
func (h *ConsistentHash) GetReplicas() int {
	return h.replicas
}

// GetSize 获取哈希环大小
func (h *ConsistentHash) GetSize() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.sortedKeys)
}

// GetDistribution 获取节点分布情况
func (h *ConsistentHash) GetDistribution() map[string]int {
	h.mu.RLock()
	defer h.mu.RUnlock()

	distribution := make(map[string]int)
	for _, nodeID := range h.ring {
		distribution[nodeID]++
	}

	return distribution
}

// Clear 清空哈希环
func (h *ConsistentHash) Clear() {
	h.mu.Lock()
	defer h.mu.Unlock()

	h.ring = make(map[uint32]string)
	h.sortedKeys = make([]uint32, 0)
}
