import { ref, onMounted, onUnmounted, type Ref } from 'vue'

export interface IntersectionObserverOptions {
  threshold?: number | number[]
  rootMargin?: string
  root?: Element | null
}

export function useIntersectionObserver(
  target: Ref<HTMLElement | undefined>,
  options: IntersectionObserverOptions = {}
) {
  const isIntersecting = ref(false)
  const intersectionRatio = ref(0)
  
  let observer: IntersectionObserver | null = null

  const {
    threshold = 0,
    rootMargin = '0px',
    root = null
  } = options

  const startObserver = () => {
    if (!target.value || observer) return

    observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0]
        if (entry) {
          isIntersecting.value = entry.isIntersecting
          intersectionRatio.value = entry.intersectionRatio
        }
      },
      {
        threshold,
        rootMargin,
        root
      }
    )

    observer.observe(target.value)
  }

  const stopObserver = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  onMounted(() => {
    startObserver()
  })

  onUnmounted(() => {
    stopObserver()
  })

  return {
    isIntersecting,
    intersectionRatio,
    startObserver,
    stopObserver
  }
} 