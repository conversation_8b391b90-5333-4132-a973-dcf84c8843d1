<template>
    <div class="post-table-container">
        <!-- 批量操作工具栏 -->
        <div v-if="selectedRows.length > 0" class="batch-toolbar">
            <div class="batch-info">
                <el-icon>
                    <Check />
                </el-icon>
                <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
            </div>
            <div class="batch-actions">
                <el-button type="success" size="small" @click="handleBatchStatus(1)">
                    批量影藏
                </el-button>
                <el-button type="warning" size="small" @click="handleBatchStatus(2)">
                    批量显示
                </el-button>
                <el-button type="primary" size="small" @click="handleBatchReview">
                    批量审核
                </el-button>
                <el-button type="danger" size="small" @click="handleBatchDelete">
                    批量删除
                </el-button>
            </div>
        </div>

        <!-- 表格容器 -->
        <div class="table-container" v-if="postList">
            <SlinkyTable :data="postList" :loading="loading" show-selection show-index
                show-actions :border="true" :stripe="true" @selection-change="handleSelectionChange" @view="handleView"
                @edit="handleEdit" @delete="handleDelete" action-width="220" view-text="查看" edit-text="编辑"
                delete-text="删除" empty-text="暂无帖子数据" class="post-data-table">
                <!-- 帖子信息列 -->
                <el-table-column prop="title" label="帖子信息" align="left" min-width="200">
                    <template #default="{ row }">
                        <div class="post-info">
                            <el-avatar :size="36" :src="row.cover_url || ''" :alt="row.title" class="post-avatar">
                                <el-icon>
                                    <Document />
                                </el-icon>
                            </el-avatar>
                            <div class="post-details">
                                <div class="post-title">{{ row.title }}</div>
                                <div class="post-description">{{ row.content || '无内容' }}</div>
                                <div class="post-tags">
                                    <el-tag v-if="row.category_name" type="info" size="small" effect="light"
                                        class="mr-1">
                                        {{ row.category_name }}
                                    </el-tag>
                                    <span v-for="tag in row.tags" :key="tag" class="mr-1">
                                        <el-tag size="small" type="info">{{ tag }}</el-tag>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <!-- 作者列 -->
                <el-table-column label="作者" min-width="220">
                    <template #default="{ row }">
                        <div class="user-info">
                            <el-avatar :size="30" :src="row.author_avatar || ''" class="mr-2">
                                <el-icon>
                                    <User />
                                </el-icon>
                            </el-avatar>
                            <span>{{ row.author_name || row.author_id }}</span>
                        </div>
                    </template>
                </el-table-column>

                <!-- 统计信息列 -->
                <el-table-column prop="view_count" label="查看数据" width="150"></el-table-column>
                <el-table-column prop="heat" label="热度" width="150"></el-table-column>
                <el-table-column prop="like_count" label="点赞数" width="150"></el-table-column>
                <el-table-column prop="comment_count" label="评论数" width="150"></el-table-column>


                <!-- 状态列 -->
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                        <!-- 状态：0-待审核,1-影藏,2-显示,-2-已拒绝,-4-已删除 -->
                        <el-tag v-if="row.status === 0" type="info">待审核</el-tag>
                        <el-tag v-else-if="row.status === 1" type="warning">隐藏</el-tag>
                        <el-tag v-else-if="row.status === 2" type="success">显示</el-tag>
                        <el-tag v-else-if="row.status === -2" type="danger">已拒绝</el-tag>
                        <el-tag v-else-if="row.status === -4" type="danger">已删除</el-tag>
                        <el-tag v-else type="primary">未知</el-tag>
                    </template>
                </el-table-column>

                <!-- 创建时间列 -->
                <el-table-column prop="created_at" label="创建时间" width="160" align="center">
                    <template #default="{ row }">
                        <div class="time-info">
                            <el-icon class="time-icon">
                                <Calendar />
                            </el-icon>
                            <span class="time-text">{{ formatDate(row.created_at) }}</span>
                        </div>
                    </template>
                </el-table-column>

                <!-- 自定义操作列 -->
                <template #actions="{ row }">
                    <div class="action-buttons-group" style="min-width: 220px;">
                        <el-button type="primary" link size="small" @click="handleView(row)">
                            <el-icon>
                                <View />
                            </el-icon>
                            查看
                        </el-button>
                        <el-button type="primary" link size="small" @click="openReviewDialog(row)">
                            <el-icon>
                                <Edit />
                            </el-icon>
                            审核
                        </el-button>
                        <el-popconfirm v-if="row.status == 1" :title="`确定要显示帖子 ${row.title} 吗？`"
                            @confirm="$emit('change-status', row.id, 2)">
                            <template #reference>
                                <el-button type="success" link size="small">
                                    <el-icon>
                                        <Check />
                                    </el-icon>
                                    显示
                                </el-button>
                            </template>
                        </el-popconfirm>

                        <el-popconfirm v-if="row.status == 2" :title="`确定要影藏帖子 ${row.title} 吗？`"
                            @confirm="$emit('change-status', row.id, 1)">
                            <template #reference>
                                <el-button type="warning" link size="small">
                                    <el-icon>
                                        <Close />
                                    </el-icon>
                                    影藏
                                </el-button>
                            </template>
                        </el-popconfirm>

                        <el-popconfirm :title="`确定要删除帖子 ${row.title} 吗？此操作不可恢复！`" @confirm="handleDelete(row)">
                            <template #reference>
                                <el-button type="danger" link size="small">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                    删除
                                </el-button>
                            </template>
                        </el-popconfirm>
                    </div>
                </template>
            </SlinkyTable>
        </div>

        <!-- 分页器 -->
        <div class="table-footer">
            <SinglePager :current-page="pagination.page" :page-size="pagination.pageSize" :total="pagination.total"
                @current-change="handleCurrentChange" @size-change="handleSizeChange" :background="true" show-jump-info
                class="pagination-wrapper" />
        </div>
        <ReviewDialog v-model:visible="reviewDialogVisible" :post-ids="reviewPostIds" :status="reviewStatus"
            :is-batch="reviewIsBatch" @confirm="handleReviewConfirm" />
    </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import { PostItem } from '@/types/posts';
import { formatDate } from '@/utils/date';
import {
    Calendar,
    Check, Close, Delete, Document, Edit,
    User, View
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import ReviewDialog from './ReviewDialog.vue';

// Props定义
interface Props {
    postList: PostItem[];
    loading: boolean;
    pagination: {
        page: number;
        pageSize: number;
        total: number;
    };
}

const props = defineProps<Props>();
const reviewPostIds = ref<string[]>([]);
const reviewStatus = ref(2);
const reviewIsBatch = ref(false);

// Emits定义
interface Emits {
    'selection-change': [selection: PostItem[]];
    'view': [row: PostItem];
    'edit': [row: PostItem];
    'review': [id: string, status: number, rejectReason?: string];
    'delete': [row: PostItem];
    'current-change': [page: number];
    'size-change': [size: number];
    'batch-review': [ids: string[], status: number, rejectReason?: string];
    'batch-delete': [rows: PostItem[]];
    'batch-status': [status: number];
    'change-status': [id: string, status: number];
}

const emit = defineEmits<Emits>();

// 选中的行
const selectedRows = ref<PostItem[]>([]);
const tableRef = ref();

// 拒绝对话框
const reviewDialogVisible = ref(false);
const reviewForm = ref({
    reason: '',
    id: '',
    isBatch: false,
    ids: [] as string[]
});

// 打开审核对话框
const openReviewDialog = (row: PostItem) => {
    reviewPostIds.value = [row.id];
    reviewIsBatch.value = false;
    reviewDialogVisible.value = true;
};

// 处理选择变化
const handleSelectionChange = (selection: PostItem[]) => {
    selectedRows.value = selection;
    emit('selection-change', selection);
};

// 查看
const handleView = (row: PostItem) => {
    emit('view', row);
};

// 编辑
const handleEdit = (row: PostItem) => {
    emit('edit', row);
};

// 审核
const handleReview = (row: PostItem) => {
    emit('review', row.id, 0, '');
};


// 删除
const handleDelete = (row: PostItem) => {
    emit('delete', row);
};

// 分页变化
const handleCurrentChange = (page: number) => {
    emit('current-change', page);
};

const handleSizeChange = (size: number) => {
    emit('size-change', size);
};

// 批量状态更新
const handleBatchStatus = (status: number) => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择帖子');
        return;
    }

    emit('batch-status', status);
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择帖子');
        return;
    }
    emit('batch-delete', selectedRows.value);
};

// 批量审核
const handleBatchReview = () => {
    reviewPostIds.value = selectedRows.value.map(row => row.id);
    reviewIsBatch.value = true;
    reviewDialogVisible.value = true;
};

// 确认审核
const handleReviewConfirm = (status: number, ids: string[], rejectReason?: string) => {
    console.log("status:", status);
    console.log("ids:", ids);
    console.log("rejectReason:", rejectReason);
    emit('batch-review', ids, status, rejectReason);
};
</script>

<style scoped lang="scss">
.post-table-container {
    .batch-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f0f9ff;
        border: 1px solid #e1f5fe;
        border-radius: 4px;
        margin-bottom: 16px;

        .batch-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1976d2;
            font-weight: 500;

            strong {
                color: #1565c0;
            }
        }

        .batch-actions {
            display: flex;
            gap: 8px;
        }
    }

    .table-content {
        background: white;
        border-radius: 4px;
        overflow: hidden;
    }

    .post-info {
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .post-avatar {
            flex-shrink: 0;
        }

        .post-details {
            min-width: 0;
            flex: 1;

            .post-title {
                font-weight: 500;
                color: #333;
                margin-bottom: 4px;
            }

            .post-description {
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }

            .post-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin-top: 4px;
            }
        }
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .stats-info {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .stat-icon {
                color: #999;
                font-size: 14px;
            }

            .stat-value {
                font-size: 12px;
                color: #666;
            }
        }
    }

    .time-info {
        display: flex;
        align-items: center;
        gap: 4px;

        .time-icon {
            color: #999;
            font-size: 14px;
        }

        .time-text {
            font-size: 12px;
            color: #666;
        }
    }

    .action-buttons-group {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
    }

    .table-footer {
        padding: 16px 24px;
        background: var(--el-bg-color-page);
        border-top: 1px solid var(--el-border-color-lighter);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .post-table-container {
        .action-buttons-group {
            flex-direction: column;
            gap: 4px;
            align-items: stretch;
        }
    }
}

/* 平板适配 */
@media (max-width: 768px) {
    .post-table-container {
        .batch-toolbar {
            flex-direction: column;
            gap: 12px;
            text-align: center;

            .batch-actions {
                justify-content: center;
            }
        }

        .pagination-container {
            justify-content: center;
        }
    }
}
</style>