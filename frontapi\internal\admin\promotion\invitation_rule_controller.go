package promotion

import (
	"frontapi/internal/admin"
	service "frontapi/internal/service/promotion"

	"github.com/gofiber/fiber/v2"
)

type InvitationRuleController struct {
	admin.BaseController
	service service.InvitationRuleService
}

func NewInvitationRuleController(service service.InvitationRuleService) *InvitationRuleController {
	return &InvitationRuleController{service: service}
}
func (h *InvitationRuleController) ListInvitationRules(c *fiber.Ctx) error {
	return h.Success(c, "")
}
