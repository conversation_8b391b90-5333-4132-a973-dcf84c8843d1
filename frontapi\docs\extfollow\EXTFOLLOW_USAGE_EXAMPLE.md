# ExtFollow 关注服务使用指南

## 概述

ExtFollow 是一个高性能的用户关注操作缓存服务，专为处理大量关注/取关操作而设计。它提供了完整的关注功能，包括基础操作、批量操作、查询操作、统计分析等。

## 特性

- **高性能缓存**: 基于 Redis 的高性能缓存存储
- **批量操作**: 支持批量关注/取关操作，减少网络开销
- **统计分析**: 提供关注趋势、影响力排名等统计功能
- **模块化设计**: 采用适配器模式，支持多种存储后端
- **可扩展性**: 支持扩展到 MongoDB 等其他存储系统

## 快速开始

### 1. 创建服务实例

```go
package main

import (
    "context"
    "log"
    
    "frontapi/internal/service/base/extfollow"
    "github.com/go-redis/redis/v8"
)

func main() {
    // 创建 Redis 客户端
    redisClient := redis.NewClient(&redis.Options{
        Addr:     "localhost:6379",
        Password: "",
        DB:       0,
    })
    
    // 创建关注服务
    followService := extfollow.CreateFollowService(redisClient, "redis")
    defer followService.Close()
    
    ctx := context.Background()
    
    // 使用服务进行关注操作
    err := followService.Follow(ctx, "user123", "user456")
    if err != nil {
        log.Printf("关注失败: %v", err)
        return
    }
    
    log.Println("关注成功!")
}
```

### 2. 基础关注操作

```go
// 关注用户
err := followService.Follow(ctx, "user123", "user456")
if err != nil {
    log.Printf("关注失败: %v", err)
}

// 取消关注
err = followService.Unfollow(ctx, "user123", "user456")
if err != nil {
    log.Printf("取关失败: %v", err)
}

// 检查是否关注
isFollowing, err := followService.IsFollowing(ctx, "user123", "user456")
if err != nil {
    log.Printf("检查关注状态失败: %v", err)
} else {
    log.Printf("关注状态: %v", isFollowing)
}
```

### 3. 获取统计数据

```go
// 获取粉丝数
followerCount, err := followService.GetFollowerCount(ctx, "user456")
if err != nil {
    log.Printf("获取粉丝数失败: %v", err)
} else {
    log.Printf("粉丝数: %d", followerCount)
}

// 获取关注数
followingCount, err := followService.GetFollowingCount(ctx, "user123")
if err != nil {
    log.Printf("获取关注数失败: %v", err)
} else {
    log.Printf("关注数: %d", followingCount)
}
```

### 4. 批量操作

```go
import (
    "frontapi/internal/service/base/extfollow/types"
    "time"
)

// 批量关注操作
operations := []*types.FollowOperation{
    {
        FollowerID: "user123",
        FolloweeID: "user456",
        Timestamp:  time.Now(),
        Source:     "api",
    },
    {
        FollowerID: "user123",
        FolloweeID: "user789",
        Timestamp:  time.Now(),
        Source:     "api",
    },
}

err := followService.BatchFollow(ctx, operations)
if err != nil {
    log.Printf("批量关注失败: %v", err)
}

// 批量获取关注状态
userIDs := []string{"user456", "user789", "user999"}
statusMap, err := followService.BatchGetFollowStatus(ctx, "user123", userIDs)
if err != nil {
    log.Printf("批量获取关注状态失败: %v", err)
} else {
    for userID, isFollowing := range statusMap {
        log.Printf("用户 %s 关注状态: %v", userID, isFollowing)
    }
}
```

### 5. 查询操作

```go
// 获取用户的粉丝列表
followers, err := followService.GetUserFollowers(ctx, "user456", 20, 0)
if err != nil {
    log.Printf("获取粉丝列表失败: %v", err)
} else {
    log.Printf("获取到 %d 个粉丝", len(followers))
    for _, follower := range followers {
        log.Printf("粉丝: %s", follower.FollowerID)
    }
}

// 获取用户的关注列表
following, err := followService.GetUserFollowing(ctx, "user123", 20, 0)
if err != nil {
    log.Printf("获取关注列表失败: %v", err)
} else {
    log.Printf("获取到 %d 个关注", len(following))
    for _, follow := range following {
        log.Printf("关注: %s", follow.FolloweeID)
    }
}
```

### 6. 获取关注历史

```go
import (
    "frontapi/internal/service/base/extfollow/types"
    "time"
)

// 获取指定时间范围的关注历史
timeRange := &types.TimeRange{
    Start: time.Now().AddDate(0, 0, -7), // 7天前
    End:   time.Now(),
}

history, err := followService.GetFollowHistory(ctx, "user123", timeRange)
if err != nil {
    log.Printf("获取关注历史失败: %v", err)
} else {
    log.Printf("获取到 %d 条历史记录", len(history))
    for _, record := range history {
        log.Printf("操作: %s -> %s (状态: %s, 时间: %s)", 
            record.FollowerID, record.FolloweeID, 
            record.Status, record.Timestamp.Format("2006-01-02 15:04:05"))
    }
}
```

### 7. 影响力排名

```go
// 更新用户影响力分数
err := followService.UpdateInfluenceRank(ctx, "user456", 95.5)
if err != nil {
    log.Printf("更新影响力排名失败: %v", err)
}

// 获取影响力排行榜
ranking, err := followService.GetInfluenceRanking(ctx, 10)
if err != nil {
    log.Printf("获取影响力排行榜失败: %v", err)
} else {
    log.Printf("影响力排行榜 TOP %d:", len(ranking))
    for i, userID := range ranking {
        log.Printf("%d. %s", i+1, userID)
    }
}

// 获取带分数的影响力排行榜
rankingWithScores, err := followService.GetInfluenceRankingWithScores(ctx, 10)
if err != nil {
    log.Printf("获取影响力排行榜失败: %v", err)
} else {
    for userID, score := range rankingWithScores {
        log.Printf("用户 %s: 影响力分数 %.2f", userID, score)
    }
}
```

### 8. 获取用户统计信息

```go
// 获取用户的详细统计信息
stats, err := followService.GetUserFollowStats(ctx, "user456")
if err != nil {
    log.Printf("获取用户统计失败: %v", err)
} else {
    log.Printf("用户 %s 统计信息:", stats.UserID)
    log.Printf("  粉丝数: %d", stats.FollowerCount)
    log.Printf("  关注数: %d", stats.FollowingCount)
    log.Printf("  互关数: %d", stats.MutualCount)
    log.Printf("  影响力分数: %.2f", stats.InfluenceScore)
    log.Printf("  最后更新: %s", stats.LastUpdated.Format("2006-01-02 15:04:05"))
}
```

### 9. 查找互相关注

```go
// 查找两个用户的互相关注
mutualFollows, err := followService.GetMutualFollows(ctx, "user123", "user456")
if err != nil {
    log.Printf("获取互关信息失败: %v", err)
} else {
    log.Printf("互相关注的用户数: %d", len(mutualFollows))
    for _, mutual := range mutualFollows {
        log.Printf("互关用户: %s", mutual.FolloweeID)
    }
}
```

### 10. 健康检查

```go
// 检查服务健康状态
err := followService.HealthCheck(ctx)
if err != nil {
    log.Printf("服务健康检查失败: %v", err)
} else {
    log.Println("服务状态正常")
}
```

## 错误处理

服务定义了以下错误类型：

```go
import "frontapi/internal/service/base/extfollow/types"

// 检查具体错误类型
err := followService.Follow(ctx, "user123", "user123")
if err != nil {
    switch err {
    case types.ErrSelfFollow:
        log.Println("不能关注自己")
    case types.ErrInvalidFollowerID:
        log.Println("无效的关注者ID")
    case types.ErrInvalidFolloweeID:
        log.Println("无效的被关注者ID")
    case types.ErrAlreadyFollowing:
        log.Println("已经关注了该用户")
    case types.ErrNotFollowing:
        log.Println("没有关注该用户")
    default:
        log.Printf("其他错误: %v", err)
    }
}
```

## 最佳实践

### 1. 批量操作优化

```go
// 好的做法：使用批量操作
operations := make([]*types.FollowOperation, 0, 100)
for _, targetUser := range targetUsers {
    operations = append(operations, &types.FollowOperation{
        FollowerID: currentUserID,
        FolloweeID: targetUser,
        Timestamp:  time.Now(),
        Source:     "batch_import",
    })
}
err := followService.BatchFollow(ctx, operations)

// 避免：逐个执行单独操作
// for _, targetUser := range targetUsers {
//     followService.Follow(ctx, currentUserID, targetUser)
// }
```

### 2. 错误重试机制

```go
import "time"

func followWithRetry(followService extfollow.FollowService, ctx context.Context, followerID, followeeID string, maxRetries int) error {
    for i := 0; i < maxRetries; i++ {
        err := followService.Follow(ctx, followerID, followeeID)
        if err == nil {
            return nil
        }
        
        // 对于某些错误类型不需要重试
        if err == types.ErrSelfFollow || err == types.ErrInvalidFollowerID {
            return err
        }
        
        if i < maxRetries-1 {
            time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
        }
    }
    
    return fmt.Errorf("关注操作在 %d 次重试后仍然失败", maxRetries)
}
```

### 3. 分页查询

```go
// 分页获取粉丝列表
func getAllFollowers(followService extfollow.FollowService, ctx context.Context, userID string) ([]*types.FollowRecord, error) {
    var allFollowers []*types.FollowRecord
    limit := 100
    offset := 0
    
    for {
        followers, err := followService.GetUserFollowers(ctx, userID, limit, offset)
        if err != nil {
            return nil, err
        }
        
        if len(followers) == 0 {
            break
        }
        
        allFollowers = append(allFollowers, followers...)
        
        if len(followers) < limit {
            break
        }
        
        offset += limit
    }
    
    return allFollowers, nil
}
```

## 性能考虑

1. **批量操作**: 优先使用批量操作接口减少网络开销
2. **分页查询**: 大量数据查询时使用分页避免内存溢出
3. **缓存策略**: 合理设置缓存过期时间平衡性能和数据一致性
4. **并发控制**: 高并发场景下注意Redis连接池配置

## 监控和调试

```go
// 获取缓存统计信息（用于监控）
stats, err := followService.GetCacheStats(ctx)
if err != nil {
    log.Printf("获取缓存统计失败: %v", err)
} else {
    log.Printf("缓存统计: %+v", stats)
}
```

这个服务提供了完整的用户关注操作缓存功能，能够有效降低MySQL服务器压力，提高系统性能。 