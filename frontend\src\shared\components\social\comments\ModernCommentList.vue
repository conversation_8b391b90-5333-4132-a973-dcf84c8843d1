<!--
  现代化评论列表组件
  提供优雅的评论展示和交互功能
-->
<template>
  <div class="modern-comment-list">
    <!-- 评论列表 -->
    <div v-if="comments.length > 0" class="comment-list">
      <div
        v-for="comment in comments"
        :key="comment.id"
        class="comment-item"
      >
        <ModernCommentItem
          :comment="comment"
          @like="$emit('like', $event)"
          @reply="handleReply"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-state">
      <div class="empty-icon">
        <el-icon :size="48"><ChatDotRound /></el-icon>
      </div>
      <p class="empty-text">还没有评论，来说点什么吧~</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-skeleton">
        <div v-for="i in 3" :key="i" class="skeleton-item">
          <el-skeleton animated>
            <template #template>
              <div class="skeleton-comment">
                <el-skeleton-item variant="circle" style="width: 40px; height: 40px;" />
                <div class="skeleton-content">
                  <el-skeleton-item variant="text" style="width: 120px; height: 16px;" />
                  <el-skeleton-item variant="text" style="width: 100%; height: 14px;" />
                  <el-skeleton-item variant="text" style="width: 80%; height: 14px;" />
                  <el-skeleton-item variant="text" style="width: 150px; height: 12px;" />
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
      </div>
    </div>

    <!-- 加载更多 -->
    <div v-if="hasMore && !loading" class="load-more">
      <el-button
        text
        @click="$emit('load-more')"
        class="load-more-btn"
      >
        <el-icon><ArrowDown /></el-icon>
        加载更多评论
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits } from 'vue'
import { ElButton, ElIcon, ElSkeleton, ElSkeletonItem } from 'element-plus'
import { ChatDotRound, ArrowDown } from '@element-plus/icons-vue'
import ModernCommentItem from './ModernCommentItem.vue'
import type { Comment } from '@/types/comment'

// 组件属性
interface Props {
  comments: Comment[]
  loading?: boolean
  hasMore?: boolean
}

// 组件事件
interface Emits {
  'like': [commentId: string]
  'reply': [commentId: string, content: string]
  'load-more': []
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// 处理回复
const handleReply = (commentId: string, content: string) => {
  emit('reply', commentId, content)
}
</script>

<style scoped lang="scss">
.modern-comment-list {
  width: 100%;
  min-height: 100px;
}

.comment-list {
  display: flex;
  flex-direction: column;
}

.comment-item {
  position: relative;
  
  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  color: #c0c4cc;
  opacity: 0.6;
}

.empty-text {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.loading-state {
  padding: 16px 0;
}

.loading-skeleton {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-item {
  padding: 16px;
}

.skeleton-comment {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.skeleton-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

.load-more-btn {
  color: #1890ff;
  font-size: 14px;
  
  &:hover {
    background: #f0f8ff;
  }
  
  .el-icon {
    margin-right: 4px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .comment-item {
    padding: 0 8px;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
  
  .skeleton-item {
    padding: 12px 8px;
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .comment-item {
    border-bottom-color: #333;
  }
  
  .empty-icon {
    color: #666;
  }
  
  .empty-text {
    color: #ccc;
  }
  
  .load-more {
    border-top-color: #333;
  }
  
  .load-more-btn {
    color: #409eff;
    
    &:hover {
      background: #1a2332;
    }
  }
}
</style> 