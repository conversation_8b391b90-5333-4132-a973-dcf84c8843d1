<template>
  <div class="file-manager-demo">
    <h2>文件管理器示例</h2>
    
    <div class="control-panel">
      <el-form :inline="true">
        <el-form-item label="组件模式">
          <el-radio-group v-model="componentMode">
            <el-radio-button label="normal">完整模式</el-radio-button>
            <el-radio-button label="selector">选择器模式</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="多选" v-if="componentMode === 'selector'">
          <el-switch v-model="allowMultiple" />
        </el-form-item>

        <el-form-item label="文件类型">
          <el-select v-model="fileType">
            <el-option label="全部" value="all" />
            <el-option label="图片" value="image" />
            <el-option label="视频" value="video" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="file-manager-container">
      <file-manager
        ref="fileManagerRef"
        :mode="componentMode"
        :initial-file-type="fileType"
        :multiple="allowMultiple"
        @select="handleSelectFiles"
        @cancel="handleCancel"
      />
    </div>

    <div class="selected-files" v-if="componentMode === 'selector' && selectedFiles.length > 0">
      <h3>已选择的文件</h3>
      <el-table :data="selectedFiles" style="width: 100%">
        <el-table-column label="预览" width="100">
          <template #default="{ row }">
            <div class="preview-cell">
              <el-image 
                v-if="row.type === 'image'" 
                :src="row.url"
                fit="cover"
                :preview-src-list="[row.url]"
                style="width: 50px; height: 50px;"
              />
              <el-icon v-else-if="row.type === 'video'"><VideoPlay /></el-icon>
              <el-icon v-else><Document /></el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="文件名" />
        <el-table-column prop="type" label="类型" width="80" />
        <el-table-column label="大小" width="100">
          <template #default="{ row }">
            {{ formatSize(row.size) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ row, $index }">
            <el-button type="danger" link @click="removeSelected($index)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Document, VideoPlay } from '@element-plus/icons-vue';
import FileManager from './index.vue';
import type { FileItem } from '@/service/api/files';

// 组件模式
const componentMode = ref<'normal' | 'selector'>('normal');
// 文件类型
const fileType = ref<'all' | 'image' | 'video'>('all');
// 是否允许多选
const allowMultiple = ref(false);
// 已选择的文件
const selectedFiles = ref<FileItem[]>([]);
// 文件管理器引用
const fileManagerRef = ref<any>(null);

// 监听组件模式变化
watch(() => componentMode.value, () => {
  // 切换模式时清空选择
  selectedFiles.value = [];
});

// 处理选择文件
const handleSelectFiles = (files: FileItem[]) => {
  if (allowMultiple.value) {
    // 多选模式：合并已选择的文件
    selectedFiles.value = [...selectedFiles.value];
    
    // 添加不重复的文件
    files.forEach(file => {
      if (!selectedFiles.value.some(f => f.path === file.path)) {
        selectedFiles.value.push(file);
      }
    });
  } else {
    // 单选模式：替换已选择的文件
    selectedFiles.value = [...files];
  }
};

// 处理取消选择
const handleCancel = () => {
  console.log('取消选择');
};

// 移除已选择的文件
const removeSelected = (index: number) => {
  selectedFiles.value.splice(index, 1);
};

// 格式化文件大小
const formatSize = (size: number) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  }
};
</script>

<style scoped>
.file-manager-demo {
  padding: 20px;
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
}

.control-panel {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.file-manager-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 600px;
  overflow: hidden;
}

.selected-files {
  margin-top: 20px;
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.selected-files h3 {
  margin-top: 0;
  margin-bottom: 16px;
}

.preview-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}

.preview-cell .el-icon {
  font-size: 24px;
}
</style> 