<template>
    <el-dialog v-model="dialogVisible" title="图片详情" width="700px" :close-on-click-modal="false" destroy-on-close>
        <div v-if="picture" class="picture-detail">
            <div class="picture-header">
                <h2 class="picture-title">{{ picture.title }}</h2>
                <el-tag :type="picture.status === 1 ? 'success' : 'danger'" class="status-tag">
                    {{ picture.status === 1 ? '正常' : '禁用' }}
                </el-tag>
            </div>

            <div class="picture-content">
                <div class="picture-image-container">
                    <el-image :src="picture.url" fit="contain" class="picture-image" :preview-src-list="[picture.url]">
                        <template #error>
                            <div class="image-placeholder">
                                <el-icon :size="32">
                                    <PictureIcon />
                                </el-icon>
                                <span>图片加载失败</span>
                            </div>
                        </template>
                    </el-image>
                </div>

                <div class="picture-info">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="图片ID">{{ picture.id }}</el-descriptions-item>
                        <el-descriptions-item label="分类">{{ picture.category_name || '未分类' }}</el-descriptions-item>
                        <el-descriptions-item label="专辑">{{ picture.album_title || '未分配' }}</el-descriptions-item>
                        <el-descriptions-item label="尺寸">{{ picture.width }}x{{ picture.height
                            }}px</el-descriptions-item>
                        <el-descriptions-item label="文件大小">{{ formatSize(picture.size) }}</el-descriptions-item>
                        <el-descriptions-item label="创建时间">{{ formatDate(picture.created_at) }}</el-descriptions-item>
                        <el-descriptions-item label="更新时间">{{ formatDate(picture.updated_at) }}</el-descriptions-item>
                    </el-descriptions>

                    <div class="stats-container">
                        <div class="stats-title">数据统计</div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <el-icon>
                                    <View />
                                </el-icon>
                                <div class="stat-content">
                                    <div class="stat-value">{{ formatNumber(picture.view_count) }}</div>
                                    <div class="stat-label">查看</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <el-icon>
                                    <Star />
                                </el-icon>
                                <div class="stat-content">
                                    <div class="stat-value">{{ formatNumber(picture.like_count) }}</div>
                                    <div class="stat-label">点赞</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="picture-description" v-if="picture.description">
                <div class="description-title">描述</div>
                <div class="description-content">{{ picture.description }}</div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleEdit">编辑</el-button>
                <el-button @click="dialogVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import type { Picture } from '@/types/pictures';
import { Picture as PictureIcon, Star, View } from '@element-plus/icons-vue';
import { computed } from 'vue';

const props = defineProps<{
    visible: boolean;
    picture: Picture | null;
}>();

const emit = defineEmits<{
    'update:visible': [value: boolean];
    'edit': [];
}>();

const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 处理编辑按钮点击
const handleEdit = () => {
    dialogVisible.value = false;
    emit('edit');
};

// 格式化日期
const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleString();
};

// 格式化文件大小
const formatSize = (bytes: number) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化数字
const formatNumber = (num: number) => {
    if (!num) return '0';
    if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
};
</script>

<style scoped lang="scss">
.picture-detail {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .picture-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        border-bottom: 1px solid #ebeef5;

        .picture-title {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #303133;
        }

        .status-tag {
            font-size: 14px;
        }
    }

    .picture-content {
        display: flex;
        gap: 24px;

        @media (max-width: 768px) {
            flex-direction: column;
        }

        .picture-image-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f5f7fa;
            border-radius: 4px;
            overflow: hidden;
            max-height: 400px;

            .picture-image {
                max-width: 100%;
                max-height: 400px;
                object-fit: contain;
            }

            .image-placeholder {
                width: 100%;
                height: 200px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                color: #909399;
                gap: 8px;
            }
        }

        .picture-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 16px;

            .stats-container {
                margin-top: 16px;

                .stats-title {
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 12px;
                    color: #303133;
                }

                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 16px;

                    .stat-item {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        padding: 16px;
                        border-radius: 4px;
                        background-color: #f5f7fa;

                        .el-icon {
                            font-size: 24px;
                            color: #409eff;
                        }

                        .stat-content {
                            display: flex;
                            flex-direction: column;
                            gap: 4px;

                            .stat-value {
                                font-size: 20px;
                                font-weight: 600;
                                color: #303133;
                            }

                            .stat-label {
                                font-size: 12px;
                                color: #909399;
                            }
                        }
                    }
                }
            }
        }
    }

    .picture-description {
        padding-top: 16px;
        border-top: 1px solid #ebeef5;

        .description-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #303133;
        }

        .description-content {
            font-size: 14px;
            color: #606266;
            line-height: 1.6;
            white-space: pre-wrap;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
}
</style>
