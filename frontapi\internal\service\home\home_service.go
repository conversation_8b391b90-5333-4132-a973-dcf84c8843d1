package home

import (
	"context"
	"encoding/json"
	"frontapi/internal/models/system"
	"frontapi/internal/models/users"
	"frontapi/internal/models/videos"
	homeRepo "frontapi/internal/repository/home"
	"frontapi/internal/repository/tags"
	"frontapi/pkg/redis"
	"time"
)

// HomeService 首页服务接口
type HomeService interface {
	GetTagList(ctx context.Context) ([]*system.Tag, error)
	GetHotVideos(ctx context.Context) ([]*videos.Video, error)
	GetRecommendedVideos(ctx context.Context) ([]*videos.Video, error)
	GetRecommendStarList(ctx context.Context) ([]*users.User, error)
	GetCategories(ctx context.Context) ([]*videos.VideoCategory, error)
}

// homeService 首页服务实现
type homeService struct {
	homeRepo homeRepo.HomeRepository
	tagRepo  tags.TagRepository
}

// NewHomeService 创建首页服务实例
func NewHomeService(homeRepo homeRepo.HomeRepository, tagRepo tags.TagRepository) HomeService {
	return &homeService{
		homeRepo: homeRepo,
		tagRepo:  tagRepo,
	}
}

// GetTagList 获取首页标签列表
func (s *homeService) GetTagList(ctx context.Context) ([]*system.Tag, error) {
	// 尝试从缓存获取
	cacheKey := "home:tags"
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var tags []*system.Tag
		if err := json.Unmarshal([]byte(cachedData), &tags); err == nil {
			return tags, nil
		}
	}

	// 从数据库获取
	tags, err := s.tagRepo.FindAll(ctx, map[string]interface{}{
		"type": 1,
	}, "count DESC")
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if tagsData, err := json.Marshal(tags); err == nil {
		redis.Set(cacheKey, string(tagsData), 30*time.Minute)
	}

	return tags, nil
}

// GetHotVideos 获取热门视频列表
func (s *homeService) GetHotVideos(ctx context.Context) ([]*videos.Video, error) {
	// 尝试从缓存获取
	cacheKey := "home:hot_videos"
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var videos []*videos.Video
		if err := json.Unmarshal([]byte(cachedData), &videos); err == nil {
			return videos, nil
		}
	}

	// 从数据库获取，默认限制10个
	videos, err := s.homeRepo.GetHotVideos(ctx, 10)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if videosData, err := json.Marshal(videos); err == nil {
		redis.Set(cacheKey, string(videosData), 15*time.Minute)
	}

	return videos, nil
}

// GetRecommendedVideos 获取推荐视频列表
func (s *homeService) GetRecommendedVideos(ctx context.Context) ([]*videos.Video, error) {
	// 尝试从缓存获取
	cacheKey := "home:recommended_videos"
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var videos []*videos.Video
		if err := json.Unmarshal([]byte(cachedData), &videos); err == nil {
			return videos, nil
		}
	}

	// 从数据库获取，默认限制10个
	videos, err := s.homeRepo.GetRecommendedVideos(ctx, 10)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if videosData, err := json.Marshal(videos); err == nil {
		redis.Set(cacheKey, string(videosData), 15*time.Minute)
	}

	return videos, nil
}

// GetRecommendStarList 获取推荐明星列表
func (s *homeService) GetRecommendStarList(ctx context.Context) ([]*users.User, error) {
	// 尝试从缓存获取
	cacheKey := "home:recommend_stars"
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var stars []*users.User
		if err := json.Unmarshal([]byte(cachedData), &stars); err == nil {
			return stars, nil
		}
	}

	// 从数据库获取，默认限制8个
	stars, err := s.homeRepo.GetRecommendStarList(ctx, 8)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if starsData, err := json.Marshal(stars); err == nil {
		redis.Set(cacheKey, string(starsData), 1*time.Hour)
	}

	return stars, nil
}

// GetCategories 获取分类列表
func (s *homeService) GetCategories(ctx context.Context) ([]*videos.VideoCategory, error) {
	// 尝试从缓存获取
	cacheKey := "categories"
	cachedData, err := redis.Get(cacheKey)

	// 如果缓存存在且没有错误，则解析并返回
	if err == nil && cachedData != "" {
		var categories []*videos.VideoCategory
		if err := json.Unmarshal([]byte(cachedData), &categories); err == nil {
			return categories, nil
		}
	}

	// 从数据库获取
	categories, err := s.homeRepo.GetCategories(ctx)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if categoriesData, err := json.Marshal(categories); err == nil {
		redis.Set(cacheKey, string(categoriesData), 2*time.Hour)
	}

	return categories, nil
}
