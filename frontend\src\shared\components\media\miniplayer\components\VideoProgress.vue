<template>
  <div class="video-progress">
    <div 
      ref="progressBar"
      class="progress-bar"
      @click="handleProgressClick"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseleave="handleMouseLeave"
    >
      <!-- 进度条背景 -->
      <div class="progress-track">
        <!-- 已播放进度 -->
        <div 
          class="progress-played"
          :style="{ width: playedPercentage + '%' }"
        ></div>
        
        <!-- 缓冲进度 -->
        <div 
          class="progress-buffered"
          :style="{ width: bufferedPercentage + '%' }"
        ></div>
        
        <!-- 进度滑块 -->
        <div 
          class="progress-thumb"
          :style="{ left: playedPercentage + '%' }"
          :class="{ 'is-dragging': isDragging }"
        ></div>
        
        <!-- 悬停时间提示 -->
        <div 
          v-if="showHoverTime"
          class="hover-time"
          :style="{ left: hoverPercentage + '%' }"
        >
          {{ formatTime(hoverTime) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 定义属性
interface Props {
  currentTime: number
  duration: number
  bufferedTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  bufferedTime: 0
})

// 定义事件
const emit = defineEmits<{
  'seek': [time: number]
}>()

// 模板引用
const progressBar = ref<HTMLElement>()

// 状态
const isDragging = ref(false)
const showHoverTime = ref(false)
const hoverTime = ref(0)
const hoverPercentage = ref(0)

// 计算属性
const playedPercentage = computed(() => {
  if (props.duration === 0) return 0
  return Math.min((props.currentTime / props.duration) * 100, 100)
})

const bufferedPercentage = computed(() => {
  if (props.duration === 0) return 0
  return Math.min((props.bufferedTime / props.duration) * 100, 100)
})

// 获取鼠标位置对应的时间
const getTimeFromPosition = (clientX: number): number => {
  if (!progressBar.value) return 0
  
  const rect = progressBar.value.getBoundingClientRect()
  const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
  return percentage * props.duration
}

// 获取鼠标位置对应的百分比
const getPercentageFromPosition = (clientX: number): number => {
  if (!progressBar.value) return 0
  
  const rect = progressBar.value.getBoundingClientRect()
  return Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100))
}

// 处理进度条点击
const handleProgressClick = (event: MouseEvent) => {
  if (isDragging.value) return
  
  const time = getTimeFromPosition(event.clientX)
  emit('seek', time)
}

// 处理鼠标按下
const handleMouseDown = (event: MouseEvent) => {
  isDragging.value = true
  const time = getTimeFromPosition(event.clientX)
  emit('seek', time)
  
  // 添加全局鼠标事件监听
  document.addEventListener('mousemove', handleGlobalMouseMove)
  document.addEventListener('mouseup', handleGlobalMouseUp)
}

// 处理鼠标移动（悬停）
const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) return
  
  showHoverTime.value = true
  hoverTime.value = getTimeFromPosition(event.clientX)
  hoverPercentage.value = getPercentageFromPosition(event.clientX)
}

// 处理鼠标离开
const handleMouseLeave = () => {
  if (!isDragging.value) {
    showHoverTime.value = false
  }
}

// 处理全局鼠标移动（拖拽）
const handleGlobalMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const time = getTimeFromPosition(event.clientX)
  emit('seek', time)
}

// 处理全局鼠标释放
const handleGlobalMouseUp = () => {
  isDragging.value = false
  showHoverTime.value = false
  
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleGlobalMouseMove)
  document.removeEventListener('mouseup', handleGlobalMouseUp)
}

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleGlobalMouseMove)
  document.removeEventListener('mouseup', handleGlobalMouseUp)
})
</script>

<style scoped lang="scss">
.video-progress {
  padding: 8px 12px 4px;

  .progress-bar {
    position: relative;
    height: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;

    .progress-track {
      position: relative;
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
      overflow: visible;

      .progress-buffered {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 2px;
        transition: width 0.1s ease;
      }

      .progress-played {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        background: #409eff;
        border-radius: 2px;
        transition: width 0.1s ease;
        z-index: 1;
      }

      .progress-thumb {
        position: absolute;
        top: 50%;
        width: 12px;
        height: 12px;
        background: #409eff;
        border: 2px solid white;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        transition: all 0.2s ease;
        z-index: 2;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

        &.is-dragging {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1.2);
        }
      }

      .hover-time {
        position: absolute;
        bottom: 100%;
        transform: translateX(-50%);
        margin-bottom: 8px;
        padding: 4px 8px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 12px;
        border-radius: 4px;
        white-space: nowrap;
        pointer-events: none;
        z-index: 3;

        &::after {
          content: '';
          position: absolute;
          top: 100%;
          left: 50%;
          transform: translateX(-50%);
          border: 4px solid transparent;
          border-top-color: rgba(0, 0, 0, 0.8);
        }
      }
    }

    &:hover {
      .progress-track {
        height: 6px;

        .progress-thumb {
          opacity: 1;
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    padding: 6px 8px 3px;

    .progress-bar {
      height: 24px;

      .progress-track {
        height: 6px;

        .progress-thumb {
          width: 16px;
          height: 16px;
          opacity: 1;
        }
      }

      &:hover {
        .progress-track {
          height: 6px;
        }
      }
    }
  }
}
</style> 