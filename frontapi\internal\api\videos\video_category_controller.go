package videos

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/videos"
	videoTypings "frontapi/internal/typings/videos"

	"github.com/gofiber/fiber/v2"
)

type VideoCategoryController struct {
	admin.BaseController
	videoService    videos.VideoService
	categoryService videos.VideoCategoryService
}

func NewVideoCategoryController(
	videoService videos.VideoService,
	categoryService videos.VideoCategoryService,
) *VideoCategoryController {
	return &VideoCategoryController{
		videoService:    videoService,
		categoryService: categoryService,
	}
}

// GetVideoCategoryList 获取视频分类列表
func (c *VideoCategoryController) GetVideoCategoryList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()

	condition := map[string]interface{}{
		"keyword": keyword,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "featured_order ASC,sort_order ASC"
	}

	categoryList, total, err := c.categoryService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	categoryListResponse := videoTypings.ConvertVideoCategoryListResponse(categoryList, total, pageNo, pageSize)

	return c.Success(ctx, categoryListResponse)

}
