package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoCommentLike 评论点赞表
type VideoCommentLike struct {
	*models.BaseModelStruct
	UserID    string `gorm:"column:user_id;index" json:"user_id"`       // 用户ID
	CommentID string `gorm:"column:comment_id;index" json:"comment_id"` // 评论ID
}

// TableName 设置表名
func (VideoCommentLike) TableName() string {
	return "ly_video_comment_likes"
}

// 实现BaseModel接口的方法
func (v *VideoCommentLike) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoCommentLike) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoCommentLike) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoCommentLike) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
