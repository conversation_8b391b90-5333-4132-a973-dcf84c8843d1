<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    class="auth-dialog"
    @close="handleClose"
  >
    <div class="auth-container">
      <!-- 登录表单 -->
      <component
        :is="currentComponent"
        @login-success="handleLoginSuccess"
        @switch-component="handleSwitchComponent"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue'
import { useUserStore } from '@/stores/user'

// 异步加载组件
const LoginForm = defineAsyncComponent(() => import('@/shared/components/auth/LoginForm.vue'))
const RegisterForm = defineAsyncComponent(() => import('@/shared/components/auth/RegisterForm.vue'))

// 简单的忘记密码组件
const ForgetPasswordForm = {
  template: `
    <div class="forget-password-form">
      <el-form :model="form" :rules="rules" ref="formRef">
        <el-form-item prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
          />
        </el-form-item>
        <el-button type="primary" class="submit-button" @click="handleSubmit" :loading="loading">
          发送重置邮件
        </el-button>
        <div class="back-link">
          <el-link type="primary" @click="$emit('switch-component', 'login')">返回登录</el-link>
        </div>
      </el-form>
    </div>
  `,
  emits: ['switch-component'],
  setup(props: any, { emit }: any) {
    const form = { email: '' }
    const loading = false
    const rules = {
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    }
    
    const handleSubmit = () => {
      // TODO: 实现忘记密码逻辑
      console.log('发送重置邮件')
    }
    
    return { form, loading, rules, handleSubmit }
  }
}

const userStore = useUserStore()

// 计算属性
const dialogVisible = computed({
  get: () => userStore.authDialogVisible,
  set: (value: boolean) => {
    if (!value) {
      userStore.hideAuthDialog()
    }
  }
})

const currentComponent = computed(() => {
  switch (userStore.authDialogComponent) {
    case 'login':
      return LoginForm
    case 'register':
      return RegisterForm
    case 'forget':
      return ForgetPasswordForm
    default:
      return LoginForm
  }
})

const dialogTitle = computed(() => {
  switch (userStore.authDialogComponent) {
    case 'login':
      return '用户登录'
    case 'register':
      return '用户注册'
    case 'forget':
      return '忘记密码'
    default:
      return '用户登录'
  }
})

// 事件处理
const handleLoginSuccess = () => {
  userStore.hideAuthDialog()
}

const handleSwitchComponent = (component: string) => {
  userStore.authDialogComponent = component
}

const handleClose = () => {
  userStore.hideAuthDialog()
}
</script>

<style lang="scss" scoped>
.auth-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    color: white;
    padding: 20px 24px;
    margin: 0;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }
}

.auth-container {
  min-height: 200px;
}

// 忘记密码表单样式
:deep(.forget-password-form) {
  .el-input {
    .el-input__wrapper {
      background: #f5f7fa;
      border-radius: 8px;
      padding: 8px 15px;
      box-shadow: none;
      transition: all 0.3s ease;

      &.is-focus, &:hover {
        background: #fff;
        box-shadow: 0 0 0 1px var(--el-color-primary) !important;
      }

      .el-input__inner {
        height: 36px;
        font-size: 14px;
      }
    }
  }

  .submit-button {
    width: 100%;
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border: none;
    transition: transform 0.3s ease;
    margin-bottom: 16px;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .back-link {
    text-align: center;
  }
}

// 深色主题支持
:deep([data-theme='dark']) {
  .auth-dialog {
    .el-dialog {
      background: var(--el-bg-color-page);
    }

    .el-dialog__header {
      background: linear-gradient(135deg, var(--el-color-primary-dark-2), var(--el-color-primary));
    }
  }

  .forget-password-form {
    .el-input {
      .el-input__wrapper {
        background: var(--el-fill-color-light);

        &.is-focus, &:hover {
          background: var(--el-bg-color);
        }
      }
    }
  }
}
</style>