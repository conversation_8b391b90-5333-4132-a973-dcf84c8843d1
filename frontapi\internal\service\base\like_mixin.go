package base

import (
	"context"
	"fmt"
	"time"

	"frontapi/internal/service/base/extlike"
	"frontapi/internal/service/base/extlike/types"
)

// ILikeMixin 点赞功能混入接口
type ILikeMixin interface {
	// 点赞相关功能
	Like(ctx context.Context, userID string, itemID string) error
	Unlike(ctx context.Context, userID string, itemID string) error
	IsLiked(ctx context.Context, userID string, itemID string) (bool, error)
	GetLikeCount(ctx context.Context, itemID string) (int64, error)

	// 批量操作
	BatchLike(ctx context.Context, userID string, itemIDs []string) error
	BatchUnlike(ctx context.Context, userID string, itemIDs []string) error
	BatchGetLikeStatus(ctx context.Context, userID string, itemIDs []string) (map[string]bool, error)
	BatchGetLikeCounts(ctx context.Context, itemIDs []string) (map[string]int64, error)

	// 排行榜功能
	GetHotRanking(ctx context.Context, limit int) ([]string, error)
	GetTrendingItems(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error)

	// 统计功能
	GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error)

	// 获取类型名称
	GetItemType() string
}

// LikeMixin 点赞功能混入实现
type LikeMixin struct {
	likeService extlike.ExtendedLikeService
	itemType    string
}

// NewLikeMixin 创建点赞功能混入实例
func NewLikeMixin(likeService extlike.ExtendedLikeService, itemType string) *LikeMixin {
	return &LikeMixin{
		likeService: likeService,
		itemType:    itemType,
	}
}

// GetItemType 获取项目类型
func (m *LikeMixin) GetItemType() string {
	return m.itemType
}

// Like 点赞操作
func (m *LikeMixin) Like(ctx context.Context, userID string, itemID string) error {
	return m.likeService.Like(ctx, userID, itemID, m.itemType)
}

// Unlike 取消点赞操作
func (m *LikeMixin) Unlike(ctx context.Context, userID string, itemID string) error {
	return m.likeService.Unlike(ctx, userID, itemID, m.itemType)
}

// IsLiked 检查是否已点赞
func (m *LikeMixin) IsLiked(ctx context.Context, userID string, itemID string) (bool, error) {
	return m.likeService.IsLiked(ctx, userID, itemID, m.itemType)
}

// GetLikeCount 获取点赞数量
func (m *LikeMixin) GetLikeCount(ctx context.Context, itemID string) (int64, error) {
	return m.likeService.GetLikeCount(ctx, itemID, m.itemType)
}

// BatchLike 批量点赞
func (m *LikeMixin) BatchLike(ctx context.Context, userID string, itemIDs []string) error {
	operations := make([]*types.LikeOperation, len(itemIDs))
	for i, itemID := range itemIDs {
		operations[i] = &types.LikeOperation{
			UserID:    userID,
			ItemID:    itemID,
			ItemType:  m.itemType,
			Action:    "like",
			Timestamp: time.Now(),
		}
	}
	return m.likeService.BatchLike(ctx, operations)
}

// BatchUnlike 批量取消点赞
func (m *LikeMixin) BatchUnlike(ctx context.Context, userID string, itemIDs []string) error {
	operations := make([]*types.LikeOperation, len(itemIDs))
	for i, itemID := range itemIDs {
		operations[i] = &types.LikeOperation{
			UserID:    userID,
			ItemID:    itemID,
			ItemType:  m.itemType,
			Action:    "unlike",
			Timestamp: time.Now(),
		}
	}
	return m.likeService.BatchUnlike(ctx, operations)
}

// BatchGetLikeStatus 批量获取点赞状态
func (m *LikeMixin) BatchGetLikeStatus(ctx context.Context, userID string, itemIDs []string) (map[string]bool, error) {
	items := make(map[string]string)
	for _, itemID := range itemIDs {
		items[itemID] = m.itemType
	}
	return m.likeService.BatchGetLikeStatus(ctx, userID, items)
}

// BatchGetLikeCounts 批量获取点赞数量
func (m *LikeMixin) BatchGetLikeCounts(ctx context.Context, itemIDs []string) (map[string]int64, error) {
	items := make(map[string]string)
	for _, itemID := range itemIDs {
		items[itemID] = m.itemType
	}
	return m.likeService.BatchGetLikeCounts(ctx, items)
}

// GetHotRanking 获取热门排行榜
func (m *LikeMixin) GetHotRanking(ctx context.Context, limit int) ([]string, error) {
	return m.likeService.GetHotRanking(ctx, m.itemType, limit)
}

// GetTrendingItems 获取趋势项目
func (m *LikeMixin) GetTrendingItems(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error) {
	return m.likeService.GetTrendingItems(ctx, m.itemType, timeRange, limit)
}

// GetUserLikeStats 获取用户点赞统计
func (m *LikeMixin) GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error) {
	return m.likeService.GetUserLikeStats(ctx, userID)
}

// LikeMixinBuilder 点赞功能混入建造者
type LikeMixinBuilder struct {
	config       *extlike.Config
	configMgr    *extlike.ConfigManager
	itemType     string
	customConfig *extlike.LikeServiceConfig
}

// NewLikeMixinBuilder 创建点赞功能混入建造者
func NewLikeMixinBuilder(itemType string) *LikeMixinBuilder {
	return &LikeMixinBuilder{
		itemType:  itemType,
		configMgr: extlike.NewConfigManager(),
	}
}

// WithConfig 设置配置
func (b *LikeMixinBuilder) WithConfig(config *extlike.Config) *LikeMixinBuilder {
	b.config = config
	return b
}

// WithCustomConfig 设置自定义配置
func (b *LikeMixinBuilder) WithCustomConfig(config *extlike.LikeServiceConfig) *LikeMixinBuilder {
	b.customConfig = config
	return b
}

// WithRedisConfig 设置Redis配置
func (b *LikeMixinBuilder) WithRedisConfig(config *extlike.RedisConfig) *LikeMixinBuilder {
	if b.customConfig == nil {
		b.customConfig = extlike.DefaultLikeServiceConfig()
	}
	b.customConfig.RedisConfig = config
	return b
}

// WithMongoConfig 设置MongoDB配置
func (b *LikeMixinBuilder) WithMongoConfig(config *extlike.MongoConfig) *LikeMixinBuilder {
	if b.customConfig == nil {
		b.customConfig = extlike.DefaultLikeServiceConfig()
	}
	b.customConfig.MongoConfig = config
	return b
}

// Build 构建点赞功能混入
func (b *LikeMixinBuilder) Build() (ILikeMixin, error) {
	// 设置自定义配置
	if b.customConfig != nil {
		b.configMgr.SetLikeConfig(b.customConfig)
	}

	// 验证配置
	if err := b.configMgr.ValidateConfig(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 如果没有设置config，使用默认配置
	if b.config == nil {
		b.config = extlike.DefaultConfig()
	}

	// 获取Redis客户端
	redisClient, redisConfig, err := b.configMgr.GetRedisClient()
	if err != nil {
		return nil, fmt.Errorf("获取Redis客户端失败: %w", err)
	}

	// 更新config中的Redis配置
	b.config.Redis.Client = redisClient
	b.config.Redis.Config = redisConfig

	// 获取MongoDB客户端（如果需要）
	if b.config.MongoDB.Enabled {
		mongoClient, mongoDatabase, _, err := b.configMgr.GetMongoClient()
		if err != nil {
			return nil, fmt.Errorf("获取MongoDB客户端失败: %w", err)
		}

		b.config.MongoDB.Client = mongoClient
		b.config.MongoDB.Database = mongoDatabase
	}

	// 创建点赞服务
	likeService, err := extlike.NewServiceBuilder().
		WithConfig(b.config).
		Build()
	if err != nil {
		return nil, fmt.Errorf("创建点赞服务失败: %w", err)
	}

	// 创建点赞功能混入
	mixin := NewLikeMixin(likeService, b.itemType)

	return mixin, nil
}

// BuildDefault 构建默认点赞功能混入
func (b *LikeMixinBuilder) BuildDefault() (ILikeMixin, error) {
	return b.WithConfig(extlike.DefaultConfig()).Build()
}

// 便捷函数

// NewLikeMixin 创建点赞功能混入的便捷函数
func NewLikeMixinFromType(itemType string) (ILikeMixin, error) {
	return NewLikeMixinBuilder(itemType).BuildDefault()
}

// NewLikeMixinWithConfig 使用配置创建点赞功能混入的便捷函数
func NewLikeMixinWithConfig(itemType string, config *extlike.Config) (ILikeMixin, error) {
	return NewLikeMixinBuilder(itemType).WithConfig(config).Build()
}
