# 轻量级控制器架构设计文档

## 1. 架构概述

轻量级控制器架构是一种高性能、低内存占用的控制器设计模式，旨在提高API服务的响应速度和资源利用率。该架构采用了以下几个核心设计原则：

1. **最小化内存占用**：通过精简数据结构和减少冗余字段，降低每个请求的内存占用
2. **最大化性能**：使用对象池、sync.Map等高性能组件，减少GC压力和锁竞争
3. **简化初始化流程**：提供一站式初始化函数，使main.go保持简洁
4. **类型安全**：通过类型断言提供类型安全的服务访问
5. **向后兼容**：与现有代码结构兼容，支持平滑迁移

## 2. 核心组件

### 2.1 轻量级服务注册表

使用`sync.Map`实现的高性能服务注册表，替代传统的全局服务容器，具有以下优势：

- 读操作无锁，适合读多写少的场景
- 内存占用更低，只存储必要的服务
- 并发安全，无需额外的锁保护

```go
var (
    serviceRegistry   sync.Map
    serviceRegistryMu sync.RWMutex
    initialized       bool
)
```

### 2.2 轻量级控制器

简化的控制器基础结构，只保留必要的字段和方法：

```go
type LiteController struct {
    ctx *fiber.Ctx
}
```

### 2.3 对象池化响应构建器

使用`sync.Pool`实现的响应构建器对象池，减少GC压力：

```go
var liteResponseBuilderPool = sync.Pool{
    New: func() interface{} {
        return &LiteResponseBuilder{
            code: 2000,
        }
    },
}
```

## 3. 性能优化策略

### 3.1 对象池化

通过对象池重用常用对象，减少内存分配和GC压力：

- 响应构建器对象池
- 请求上下文对象池（可扩展）

### 3.2 减少锁竞争

- 使用`sync.Map`减少读操作的锁竞争
- 初始化时使用写锁，运行时主要是无锁读操作

### 3.3 内存优化

- 只注册必要的服务
- 减少中间对象的创建
- 避免不必要的类型转换和接口调用

### 3.4 减少抽象层次

- 直接从服务注册表获取服务，减少间接调用
- 使用类型断言直接获取具体类型，避免接口调用开销

## 4. 使用方法

### 4.1 初始化

在`main.go`中只需一行代码完成初始化：

```go
bootstrap.InitApp(app, services)
```

### 4.2 创建轻量级控制器

```go
type LitePictureAlbumController struct {
    api.LiteBaseController
}

func NewLitePictureAlbumController() *LitePictureAlbumController {
    return &LitePictureAlbumController{
        LiteBaseController: api.NewLiteBaseController(),
    }
}
```

### 4.3 访问服务

通过类型断言直接访问具体服务类型：

```go
albumService := c.GetPictureAlbumService().(pictures.PictureAlbumService)
```

### 4.4 注册路由

使用简化的路由注册方式：

```go
func registerLitePictureRoutes(apiGroup fiber.Router) {
    pictureAlbumController := pictures.NewLitePictureAlbumController()
    albums := apiGroup.Group("/pictures/albums")
    albums.Post("/getAlbumList", pictureAlbumController.GetAlbumList)
    // ...
}
```

## 5. 与传统架构的对比

| 特性 | 轻量级架构 | 传统架构 |
|------|------------|----------|
| 内存占用 | 低 | 中-高 |
| 响应速度 | 快 | 中 |
| GC压力 | 低 | 中-高 |
| 初始化复杂度 | 简单（一行代码） | 中等 |
| 代码复杂度 | 低 | 中 |
| 可扩展性 | 中-高 | 高 |

## 6. 迁移指南

### 6.1 从传统控制器迁移

1. 创建对应的轻量级控制器，继承`LiteBaseController`
2. 修改服务访问方式，使用类型断言
3. 更新路由注册代码

### 6.2 新项目使用建议

1. 直接使用轻量级控制器架构
2. 在`main.go`中使用`bootstrap.InitApp()`初始化
3. 按模块组织路由和控制器

## 7. 最佳实践

1. 尽量减少每个请求的内存分配
2. 使用对象池管理频繁创建的对象
3. 避免不必要的类型转换和接口调用
4. 保持控制器简洁，业务逻辑放在服务层
5. 使用类型断言时注意安全检查

## 8. 未来扩展

1. 请求上下文对象池化
2. 更细粒度的服务注册
3. 集成监控和性能指标
4. 自动化性能测试和基准测试 