import { RouteRecordRaw } from 'vue-router';

const shortsRoutes: Array<RouteRecordRaw> = [
    {
        path: '/shorts',
        name: 'Shorts',
        component: () => import('@/views/shorts/list/index.vue'),
        meta: {
            title: 'shorts',
            icon: 'play-circle'
        }
    },
    {
        path: '/shorts/:id',
        name: 'ShortDetail',
        component: () => import('@/views/shorts/detail/indev.vue'),
        meta: {
            title: 'shortDetail',
            hideInMenu: true
        }
    }
];

export default shortsRoutes; 