/**
 * Modern 主题暗色版本变量定义
 * 基于现代设计理念的暗色主题
 */

export const modernDarkVariables = {
    // 主色调 - 蓝色系
    'color-primary': '#4A90E2',
    'color-primary-secondary': '#357ABD', // 第二配色
    'color-primary-light': '#6BA3E8',
    'color-primary-dark': '#3A7BC8',
    'color-primary-50': 'rgba(74, 144, 226, 0.05)',
    'color-primary-100': 'rgba(74, 144, 226, 0.1)',
    'color-primary-200': 'rgba(74, 144, 226, 0.2)',
    'color-primary-300': 'rgba(74, 144, 226, 0.3)',
    'color-primary-400': 'rgba(74, 144, 226, 0.4)',
    'color-primary-500': 'rgba(74, 144, 226, 0.5)',
    'color-primary-600': 'rgba(74, 144, 226, 0.6)',
    'color-primary-700': 'rgba(74, 144, 226, 0.7)',
    'color-primary-800': 'rgba(74, 144, 226, 0.8)',
    'color-primary-900': 'rgba(74, 144, 226, 0.9)',

    // 强调色
    'color-accent': '#FF6B6B',
    'color-accent-light': '#FF8E8E',
    'color-accent-dark': '#E55555',

    // 中性色 - 暗色主题
    'color-neutral-50': '#1A1A1A',
    'color-neutral-100': '#2D2D2D',
    'color-neutral-200': '#404040',
    'color-neutral-300': '#525252',
    'color-neutral-400': '#737373',
    'color-neutral-500': '#A3A3A3',
    'color-neutral-600': '#D4D4D4',
    'color-neutral-700': '#E5E5E5',
    'color-neutral-800': '#F5F5F5',
    'color-neutral-900': '#FFFFFF',

    // 背景色 - 暗色主题
    'color-background': '#121212',
    'color-background-secondary': '#1E1E1E',
    'color-background-tertiary': '#2A2A2A',
    'color-surface': '#1E1E1E',
    'color-surface-secondary': '#2A2A2A',

    // 主页面背景和文字色
    'color-page-background': '#0F0F0F',
    'color-page-text': '#E0E0E0',

    // 文本色 - 暗色主题
    'color-text': '#E0E0E0',
    'color-text-secondary': '#B0B0B0',
    'color-text-tertiary': '#808080',
    'color-text-inverse': '#1A1A1A',

    // 边框色
    'color-border': '#404040',
    'color-border-light': '#525252',
    'color-border-dark': '#2D2D2D',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    'shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    'shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3)',
    'shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',
    'shadow-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2)',

    // PrimeVue 集成
    'primary-color': '#4A90E2',
    'primary-color-text': '#FFFFFF',
    'surface-0': '#121212',
    'surface-50': '#1A1A1A',
    'surface-100': '#2D2D2D',
    'surface-200': '#404040',
    'surface-300': '#525252',
    'surface-400': '#737373',
    'surface-500': '#A3A3A3',
    'surface-600': '#D4D4D4',
    'surface-700': '#E5E5E5',
    'surface-800': '#F5F5F5',
    'surface-900': '#FFFFFF',
    'surface-ground': '#0F0F0F',
    'surface-section': '#1E1E1E',
    'surface-card': '#2A2A2A',
    'surface-overlay': '#2A2A2A',
    'surface-border': '#404040',
    'surface-hover': '#525252',
    'text-color': '#E0E0E0',
    'text-color-secondary': '#B0B0B0',
    'mask-bg': 'rgba(0, 0, 0, 0.6)',
    'highlight-bg': 'rgba(74, 144, 226, 0.1)',
    'highlight-text-color': '#4A90E2',
    'content-bg': '#0F0F0F',
    'content-text': '#E0E0E0',

    'header-gradient': 'linear-gradient(135deg, #1d4ed8, #3b82f6)',
    'footer-gradient': 'linear-gradient(135deg, #1E1E1E, #2D2D2D)',
    'footer-text': '#E0E0E0',
    'nav-text': '#FFFFFF',
};

export default modernDarkVariables;