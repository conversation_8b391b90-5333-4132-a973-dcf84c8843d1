package system

import (
	"frontapi/internal/models"
)

type Tag struct {
	models.BaseModelStruct
	Name      string `gorm:"column:name;type:string;not null;comment:标签名称" json:"name"`                  // 标签名称
	Count     int64  `gorm:"column:count;type:bigint;default:0;comment:使用次数" json:"count"`               // 使用次数
	SortOrder int    `gorm:"column:sort_order;type:int;default:0;comment:排序" json:"sort_order"`          // 排序
	Type      int8   `gorm:"column:type;type:tinyint;not null;comment:标签类型:0通用,1内容,2用户,3系统" json:"type"` // 标签类型
	//Status      int       `gorm:"column:status;type:tinyint;default:1;comment:状态:0禁用,1启用" json:"status"`                    // 状态:0禁用,1启用
}

func (Tag) TableName() string {
	return "ly_tags"
}
