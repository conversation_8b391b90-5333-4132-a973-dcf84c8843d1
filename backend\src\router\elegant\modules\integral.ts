import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from "@/locales";

const routes: GeneratedRoute[] = [
  {
    name: 'integral_list',
    path: '/integral',
    component: 'layout.base',
    meta: {
      title: 'integral',
      i18nKey: 'route.integral',
      icon: 'lucide:book-open',
      order: 12
    },
    // children: [
    //   // 积分管理
    //   {
    //     name: 'integral_list',
    //     path: '/integral_list',
    //     component: 'layout.base$view.integral',
    //     meta: {
    //       title: 'integral',
    //       i18nKey: 'route.integral'
    //     }
    //   },
    // ]
  }
];

export default routes;
