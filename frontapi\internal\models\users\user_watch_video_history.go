package users

import (
	"frontapi/internal/models"
)

// UserWatchVideoHistory 用户观看视频历史表
type UserWatchVideoHistory struct {
	models.BaseModel
	UserID       string `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                // 用户ID
	VideoID      string `gorm:"column:video_id;type:string;not null;comment:视频ID" json:"video_id"`              // 视频ID
	WatchTime    int    `gorm:"column:watch_time;type:int;default:0;comment:观看时间(秒)" json:"watch_time"`         // 观看时间(秒)
	LastPosition int    `gorm:"column:last_position;type:int;default:0;comment:上次观看位置(秒)" json:"last_position"` // 上次观看位置(秒)
	IsFinished   uint8  `gorm:"column:is_finished;default:0" json:"is_finished"`                                // 是否看完：0-未看完，1-已看完

}

// TableName 表名
func (UserWatchVideoHistory) TableName() string {
	return "ly_user_watch_video_history"
}
