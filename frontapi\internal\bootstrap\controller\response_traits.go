package controller

import "github.com/gofiber/fiber/v2"

type ResponseInterface interface {
	WithCode(code int) *ResponseBuilder
	WithMessage(message string) *ResponseBuilder
	WithData(data interface{}) *ResponseBuilder
	Build() fiber.Map
	Success(ctx *fiber.Ctx, data interface{}) error
	SuccessWithMessage(ctx *fiber.Ctx, message string) error
	SuccessList(ctx *fiber.Ctx, list interface{}, total int64, page, pageSize int) error
	BadRequest(ctx *fiber.Ctx, message string, data interface{}) error
	Unauthorized(ctx *fiber.Ctx, message string) error
	Forbidden(ctx *fiber.Ctx, message string) error
	NotFound(ctx *fiber.Ctx, message string) error
	InternalServerError(ctx *fiber.Ctx, message string) error
}

type ResponseTrait struct {
	ResponseInterface
}

// Success 返回成功响应
func (b ResponseTrait) Success(ctx *fiber.Ctx, data interface{}) error {
	return NewResponseBuilder().
		WithData(data).
		Success(ctx)
}

// SuccessWithMessage 返回带消息的成功响应
func (b ResponseTrait) SuccessWithMessage(ctx *fiber.Ctx, message string) error {
	return NewResponseBuilder().
		WithMessage(message).
		Success(ctx)
}

// SuccessList 返回列表数据
func (b ResponseTrait) SuccessList(ctx *fiber.Ctx, list interface{}, total int64, page, pageSize int) error {
	return NewResponseBuilder().
		WithData(fiber.Map{
			"list":     list,
			"total":    total,
			"page":     page,
			"pageSize": pageSize,
		}).
		Success(ctx)
}

// BadRequest 返回错误请求响应
func (b ResponseTrait) BadRequest(ctx *fiber.Ctx, message string, data interface{}) error {
	return NewResponseBuilder().
		WithCode(4000).
		WithMessage(message).
		WithData(data).
		Error(ctx, fiber.StatusBadRequest)
}

// Unauthorized 返回未授权响应
func (b ResponseTrait) Unauthorized(ctx *fiber.Ctx, message string) error {
	return NewResponseBuilder().
		WithCode(4001).
		WithMessage(message).
		Error(ctx, fiber.StatusUnauthorized)
}

// Forbidden 返回禁止访问响应
func (b ResponseTrait) Forbidden(ctx *fiber.Ctx, message string) error {
	return NewResponseBuilder().
		WithCode(4003).
		WithMessage(message).
		Error(ctx, fiber.StatusForbidden)
}

// NotFound 返回资源不存在响应
func (b ResponseTrait) NotFound(ctx *fiber.Ctx, message string) error {
	return NewResponseBuilder().
		WithCode(4004).
		WithMessage(message).
		Error(ctx, fiber.StatusNotFound)
}

// InternalServerError 返回服务器错误响应
func (b ResponseTrait) InternalServerError(ctx *fiber.Ctx, message string) error {
	return NewResponseBuilder().
		WithCode(5000).
		WithMessage(message).
		Error(ctx, fiber.StatusInternalServerError)
}
