package comics

import (
	"context"
	"errors"
	"frontapi/pkg/types"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"frontapi/internal/models/comics"
	repo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
	comicsValidator "frontapi/internal/validation/comics"
)

// ComicPageService 漫画页面服务接口
type ComicPageService interface {
	base.IExtendedService[comics.ComicPage]
	CreatePage(ctx context.Context, req *comicsValidator.CreatePageRequest) (string, error)
	BatchCreatePages(ctx context.Context, req *comicsValidator.BatchCreatePagesRequest) error
	UpdatePage(ctx context.Context, id string, req *comicsValidator.UpdatePageRequest) error
	DeletePage(ctx context.Context, id string) error
	DeleteChapterPages(ctx context.Context, chapterID string) error
	UpdatePageOrder(ctx context.Context, pageID string, newOrder int) error
	BatchUpdatePageOrder(ctx context.Context, chapterID string, comicID string, pages []comicsValidator.PageOrderItem) error
}

// comicPageService 漫画页面服务实现
type comicPageService struct {
	*base.ExtendedService[comics.ComicPage]
	pageRepo    repo.ComicPageRepository
	chapterRepo repo.ComicChapterRepository
}

// NewComicPageService 创建漫画页面服务实例
func NewComicPageService(
	pageRepo repo.ComicPageRepository,
	chapterRepo repo.ComicChapterRepository,
) ComicPageService {
	return &comicPageService{
		ExtendedService: base.NewExtendedService[comics.ComicPage](pageRepo, "comic_page"),
		pageRepo:        pageRepo,
		chapterRepo:     chapterRepo,
	}
}

// CreatePage 创建页面
func (s *comicPageService) CreatePage(ctx context.Context, req *comicsValidator.CreatePageRequest) (string, error) {
	// 检查章节是否存在
	chapter, err := s.chapterRepo.FindByID(ctx, req.ChapterID)
	if err != nil {
		return "", err
	}
	if chapter == nil {
		return "", errors.New("章节不存在")
	}

	// 检查页码是否已存在

	existCount, err := s.Count(ctx, map[string]interface{}{
		"chapter_id":  req.ChapterID,
		"page_number": req.PageNumber,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if existCount != 0 {
		return "", errors.New("页码已存在")
	}

	// 生成唯一ID
	pageID := uuid.New().String()

	// 创建页面
	page := &comics.ComicPage{
		ChapterID:  req.ChapterID,
		ComicId:    req.ComicId,
		PageNumber: req.PageNumber,
		ImageURL:   req.ImageURL,
		Width:      req.Width,
		Height:     req.Height,
	}
	page.SetID(pageID)
	page.SetStatus(1)
	page.SetCreatedAt(types.JSONTime(time.Now()))
	page.SetUpdatedAt(types.JSONTime(time.Now()))

	// 保存页面
	if _, err := s.Create(ctx, page); err != nil {
		return "", err
	}

	// 更新章节页数
	pageCount, err := s.Count(ctx, map[string]interface{}{
		"chapter_id": req.ChapterID,
	})
	if err != nil {
		return "", err
	}

	if err := s.chapterRepo.UpdateColumn(ctx, map[string]interface{}{
		"id": req.ChapterID,
	}, "page_count", pageCount); err != nil {
		return "", err
	}

	return pageID, nil
}

// BatchCreatePages 批量创建页面
func (s *comicPageService) BatchCreatePages(ctx context.Context, req *comicsValidator.BatchCreatePagesRequest) error {
	if len(req.Pages) == 0 {
		return errors.New("页面列表为空")
	}

	// 检查章节是否存在
	_, err := s.chapterRepo.FindByID(ctx, req.ChapterID)
	if err != nil {
		return err
	}
	//获取最大页码
	//maxPageNumber, err := s.pageRepo.GetMaxPageNumber(ctx, req.ChapterID, req.ComicId)
	//if err != nil {
	//	return err
	//}

	// 创建页面列表
	pages := make([]*comics.ComicPage, len(req.Pages))
	for i, pageDetail := range req.Pages {
		// 检查页码是否已存在
		count, err := s.pageRepo.Count(ctx, map[string]interface{}{
			"chapter_id":  req.ChapterID,
			"page_number": pageDetail.PageNumber,
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if count != 0 {
			return errors.New("页码已存在")
		}

		page := &comics.ComicPage{
			ChapterID:  req.ChapterID,
			ComicId:    req.ComicId,
			PageNumber: pageDetail.PageNumber,
			ImageURL:   pageDetail.ImageURL,
			Width:      pageDetail.Width,
			Height:     pageDetail.Height,
		}
		page.SetID(uuid.New().String())
		page.SetStatus(1)
		page.SetCreatedAt(types.JSONTime(time.Now()))
		page.SetUpdatedAt(types.JSONTime(time.Now()))
		pages[i] = page
	}

	// 批量保存页面
	if _, err := s.pageRepo.BatchCreate(ctx, pages); err != nil {
		return err
	}

	// 更新章节页数
	pageCount, err := s.Count(ctx, map[string]interface{}{
		"chapter_id": req.ChapterID,
	})
	if err != nil {
		return err
	}

	return s.chapterRepo.UpdateColumn(ctx, map[string]interface{}{
		"id": req.ChapterID,
	}, "page_count", pageCount)
}

// UpdatePage 更新页面
func (s *comicPageService) UpdatePage(ctx context.Context, id string, req *comicsValidator.UpdatePageRequest) error {
	// 获取现有页面
	page, err := s.BaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}

	// 如果要更改页码，检查新页码是否已存在
	if req.PageNumber != nil && *req.PageNumber != page.PageNumber {
		count, err := s.Count(ctx, map[string]interface{}{
			"chapter_id":  page.ChapterID,
			"page_number": *req.PageNumber,
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if count != 0 {
			return errors.New("页码已存在")
		}
		page.PageNumber = *req.PageNumber
	}

	// 更新其他字段
	if req.ImageURL != "" {
		page.ImageURL = req.ImageURL
	}
	if req.Width != nil {
		page.Width = *req.Width
	}
	if req.Height != nil {
		page.Height = *req.Height
	}

	page.SetUpdatedAt(types.JSONTime(time.Now()))

	// 保存更新
	return s.BaseService.Update(ctx, page)
}

// DeletePage 删除页面
func (s *comicPageService) DeletePage(ctx context.Context, id string) error {
	// 获取页面信息
	page, err := s.BaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}

	// 删除页面
	if err := s.BaseService.Delete(ctx, id); err != nil {
		return err
	}

	// 更新章节页数
	pageCount, err := s.pageRepo.Count(ctx, map[string]interface{}{
		"chapter_id": page.ChapterID,
	})
	if err != nil {
		return err
	}

	return s.chapterRepo.UpdateColumn(ctx, map[string]interface{}{
		"id": page.ChapterID,
	}, "page_count", pageCount-1)
}

// DeleteChapterPages 删除章节的所有页面
func (s *comicPageService) DeleteChapterPages(ctx context.Context, chapterID string) error {
	// 检查章节是否存在
	if _, err := s.chapterRepo.FindByID(ctx, chapterID); err != nil {
		return err
	}

	// 删除页面
	if err := s.Delete(ctx, chapterID); err != nil {
		return err
	}

	// 更新章节页数
	return s.UpdateColumn(ctx, map[string]interface{}{
		"id": chapterID,
	}, "page_count", 0)
}

// UpdatePageOrder 更新页面顺序
func (s *comicPageService) UpdatePageOrder(ctx context.Context, pageID string, newOrder int) error {
	return s.pageRepo.UpdateOrder(ctx, pageID, newOrder)
}

// BatchUpdatePageOrder 批量更新页面顺序
func (s *comicPageService) BatchUpdatePageOrder(ctx context.Context, chapterID string, comicID string, pages []comicsValidator.PageOrderItem) error {
	// 验证章节是否存在
	chapter, err := s.chapterRepo.FindByID(ctx, chapterID)
	if err != nil {
		return err
	}
	if chapter == nil {
		return errors.New("章节不存在")
	}

	// 构建页面ID到序号的映射
	pageOrders := make(map[string]int)
	for _, item := range pages {
		pageOrders[item.ID] = item.PageNumber
	}

	// 调用仓库的批量更新方法
	return s.pageRepo.BatchUpdateOrder(ctx, chapterID, pageOrders)
}
