import type { BaseRequest, BaseResponse, PageResponse } from '../../core/utils/request'
import { post, corePost } from '@/shared/composables'

export interface ChannelsRequest {
  keyword?: string
}

export interface ChannelsResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

export const fetchCreatorList = (formData: BaseRequest<ChannelsRequest>)=>{
  return corePost('/creator/getCreatorList', formData)
}

export const fetchAlbumList = (formData: BaseRequest<ChannelsRequest>) => {
  return corePost('/video/albums/getAlbumList', formData)
}


export const getCreatorDetail = (formData: { id: number }) => {
  return corePost('/creator/getCreatorDetail', formData)
}
export const getAlbumDetail = (formData: { id: number }) => {
  return corePost('/video/albums/getAlbumDetail', formData)
}
export const getCreatorVideos = (formData: { id: number }) => {
  return corePost('/creator/getCreatorVideos', formData)
}
export const getAlbumVideos = (formData: { id: number }) => {
  return corePost('/video/albums/getAlbumVideoList', formData)
}