package main

import (
	"frontapi/pkg/database"
	"frontapi/pkg/redis"
	"log"
	"os"
	"os/signal"
	"strconv"
	"syscall"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"

	"frontapi/config"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"
	"frontapi/pkg/utils"
)

func main() {
	// 加载配置
	config.LoadConfig()

	// 初始化JWT密钥
	utils.InitJWTSecret(config.AppConfig.JWT.Secret)

	// 初始化MySQL数据库
	database.InitMySQL()

	// 初始化Redis
	if err := redis.Init(); err != nil {
		log.Printf("警告: Redis初始化失败: %v", err)
	} else {
		log.Println("Redis连接成功")
	}

	// 初始化所有服务
	services := bootstrap.InitServices(database.DB)

	// 创建 Fiber 应用
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			// 默认错误处理
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}
			return c.Status(code).JSON(fiber.Map{
				"code":    code,
				"message": err.Error(),
				"data":    nil,
			})
		},
	})

	// 添加中间件
	app.Use(recover.New())
	app.Use(logger.New())
	app.Use(cors.New(cors.Config{
		AllowOrigins:     "*",
		AllowMethods:     "GET,POST,PUT,DELETE,OPTIONS",
		AllowHeaders:     "Origin,Content-Type,Accept,Authorization",
		AllowCredentials: true,
	}))

	// 应用可选认证中间件到所有路由
	app.Use(middleware.OptionalAuth())

	// 初始化轻量级控制器系统并注册中间件（一行代码完成所有初始化）
	bootstrap.InitApp(app, services)

	// 注册轻量级路由
	// routes.RegisterLiteRoutes(app)

	// 设置优雅关闭
	// 捕获中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-quit
		log.Println("正在优雅关闭服务...")

		// 关闭Redis连接
		if err := redis.Close(); err != nil {
			log.Printf("关闭Redis连接失败: %v", err)
		}

		// 关闭数据库连接
		database.Close()

		// 关闭Fiber应用
		if err := app.Shutdown(); err != nil {
			log.Fatalf("关闭服务失败: %v", err)
		}
	}()

	// 启动服务器
	listenAddr := config.AppConfig.Server.Host + ":" + strconv.Itoa(config.AppConfig.Server.Port)
	log.Printf("服务器启动在 %s", listenAddr)
	if err := app.Listen(listenAddr); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}
