# Cache 包

这是一个简化版的缓存系统，提供了统一的缓存接口和多种缓存实现。

## 主要功能

- 统一的缓存接口
- 支持Redis缓存
- 支持BigCache内存缓存
- 支持文件缓存
- 支持集群和分片功能
- 支持JSON序列化和反序列化
- 支持泛型操作

## 目录结构

```
cache/
├── interfaces.go      # 接口定义
├── manager.go         # 缓存管理器实现
├── json_utils.go      # JSON工具函数
├── global.go          # 全局缓存管理器
├── init.go            # 初始化函数
├── types/             # 类型定义
├── redis/             # Redis适配器
├── bigcache/          # BigCache适配器
├── file/              # 文件缓存适配器
├── cluster/           # 集群支持
└── sharding/          # 分片支持
```

## 使用示例

### 基本用法 - Redis

```go
import (
    "time"
    "frontapi/pkg/cache"
    "frontapi/pkg/cache/redis"
)

// 初始化Redis适配器
redisAdapter, err := redis.New(&redis.Config{
    Host:     "localhost",
    Port:     6379,
    Password: "",
    DB:       0,
})
if err != nil {
    panic(err)
}

// 创建缓存管理器
manager, err := cache.NewManager(nil)
if err != nil {
    panic(err)
}

// 添加适配器
err = manager.AddAdapter("redis", redisAdapter)
if err != nil {
    panic(err)
}

// 设置默认适配器
err = manager.SetDefaultAdapter("redis")
if err != nil {
    panic(err)
}

// 获取默认适配器
adapter, err := manager.GetDefaultAdapter()
if err != nil {
    panic(err)
}

// 设置缓存
err = adapter.Set(context.Background(), "key", []byte("value"), time.Hour)
if err != nil {
    panic(err)
}

// 获取缓存
value, err := adapter.Get(context.Background(), "key")
if err != nil {
    panic(err)
}
```

### JSON序列化和反序列化

```go
import (
    "time"
    "frontapi/pkg/cache"
)

// 使用JSON工具函数
type User struct {
    ID   int    `json:"id"`
    Name string `json:"name"`
}

user := User{ID: 1, Name: "张三"}

// 设置缓存（使用管理器）
err := cache.ManagerSetJSON("user:1", user, time.Hour)
if err != nil {
    panic(err)
}

// 获取缓存（使用管理器）
var retrievedUser User
err = cache.ManagerGetJSON("user:1", &retrievedUser)
if err != nil {
    panic(err)
}

// 使用泛型版本
err = cache.ManagerSetJSONGeneric("user:2", user, time.Hour)
if err != nil {
    panic(err)
}

// 获取缓存（泛型版本）
retrievedUser2, err := cache.ManagerGetJSONGeneric[User]("user:2")
if err != nil {
    panic(err)
}
```

### 集群和分片

```go
import (
    "frontapi/pkg/cache"
    "frontapi/pkg/cache/cluster"
    "frontapi/pkg/cache/sharding"
)

// 创建集群适配器
clusterAdapter, err := cluster.New(&cluster.Config{
    Nodes: []cache.CacheAdapter{
        redisAdapter1,
        redisAdapter2,
        redisAdapter3,
    },
})
if err != nil {
    panic(err)
}

// 创建分片适配器
shardingAdapter, err := sharding.New(&sharding.Config{
    Shards: []cache.CacheAdapter{
        redisAdapter1,
        redisAdapter2,
        redisAdapter3,
    },
    ShardingStrategy: "hash",
})
if err != nil {
    panic(err)
}
```

## 全局缓存管理器

```go
import (
    "frontapi/pkg/cache"
)

// 获取全局缓存管理器
manager := cache.GetGlobalManager()

// 获取默认适配器
adapter, err := manager.GetDefaultAdapter()
if err != nil {
    panic(err)
}

// 使用适配器
err = adapter.Set(context.Background(), "key", []byte("value"), time.Hour)
if err != nil {
    panic(err)
}
```

## 初始化缓存系统

在应用程序启动时，通常会调用以下函数来初始化缓存系统：

```go
import (
    "frontapi/pkg/cache"
    "frontapi/config"
)

// 初始化缓存系统
err := cache.InitCacheManager(&config.AppConfig)
if err != nil {
    panic(err)
}
``` 