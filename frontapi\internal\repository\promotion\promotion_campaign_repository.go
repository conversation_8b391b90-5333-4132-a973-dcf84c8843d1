package promotion

import (
	"frontapi/internal/repository/base"

	"gorm.io/gorm"

	model "frontapi/internal/models/promotion"
)

// PromotionCampaignRepository 推广活动仓库接口
type PromotionCampaignRepository interface {
	base.ExtendedRepository[model.PromotionCampaign]
}

// promotionCampaignRepository 推广活动仓库实现
type promotionCampaignRepository struct {
	base.ExtendedRepository[model.PromotionCampaign]
}

// NewPromotionCampaignRepository 创建推广活动仓库实例
func NewPromotionCampaignRepository(db *gorm.DB) PromotionCampaignRepository {
	return &promotionCampaignRepository{
		ExtendedRepository: base.NewExtendedRepository[model.PromotionCampaign](db),
	}
}
