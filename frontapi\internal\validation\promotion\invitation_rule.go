package promotion

// CreateInvitationRuleRequest 创建规则请求
type CreateInvitationRulesRequest struct {
	Name                  string  `json:"name"`
	InviteRewardPoints    int     `json:"invite_reward_points"`
	InvitedRewardPoints   int     `json:"invited_reward_points"`
	CommissionRate        float64 `json:"commission_rate"`
	CommissionPeriod      int     `json:"commission_period"`
	IsPermanentCommission int8    `json:"is_permanent_commission"`
	Description           string  `json:"description"`
	Status                int8    `json:"status"`
}

type UpdateInvitationRulesRequest struct {
	Name                  string  `json:"name"`
	InviteRewardPoints    int     `json:"invite_reward_points"`
	InvitedRewardPoints   int     `json:"invited_reward_points"`
	CommissionRate        float64 `json:"commission_rate"`
	CommissionPeriod      int     `json:"commission_period"`
	IsPermanentCommission int8    `json:"is_permanent_commission"`
	Description           string  `json:"description"`
	Status                int8    `json:"status"`
}
