package middleware

import (
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
)

// WebSocketUpgrade 升级HTTP连接到WebSocket连接的中间件
//func WebSocketUpgrade(handler websocket.Handler) fiber.Handler {
//	return websocket.New(handler, websocket.Config{
//		// 检查是否是 WebSocket 升级请求
//		Filter: func(c *fiber.Ctx) bool {
//			return websocket.IsWebSocketUpgrade(c)
//		},
//	})
//}

func WebSocketUpgrade() fiber.Handler {
	return websocket.New(func(c *websocket.Conn) {
		// 此处不做实际处理，只升级协议
	}, websocket.Config{
		// 检查是否是 WebSocket 升级请求
		Filter: func(c *fiber.Ctx) bool {
			return websocket.IsWebSocketUpgrade(c)
		},
	})
}
