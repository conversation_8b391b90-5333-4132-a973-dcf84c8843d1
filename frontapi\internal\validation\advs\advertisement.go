package advs

import "time"

type CreateAdvertisementRequest struct {
	PositionID string    `json:"position_id" validate:"required"`
	Title      string    `json:"title" validate:"required"`
	Image      string    `json:"image" validate:"required"`
	URL        string    `json:"url"`
	StartTime  time.Time `json:"start_time" validate:"required"`
	EndTime    time.Time `json:"end_time" validate:"required"`
	SortOrder  int       `json:"sort_order" validate:"required"`
	ViewCount  int64     `json:"view_count"`
	ClickCount int64     `json:"click_count"`
	Status     int8      `json:"status" validate:"required"`
}

type UpdateAdvertisementRequest struct {
	PositionID string    `json:"position_id"`
	Title      string    `json:"title"`
	Image      string    `json:"image"`
	URL        string    `json:"url"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	SortOrder  int       `json:"sort_order"`
	ViewCount  int64     `json:"view_count"`
	ClickCount int64     `json:"click_count"`
	Status     int8      `json:"status"`
}
