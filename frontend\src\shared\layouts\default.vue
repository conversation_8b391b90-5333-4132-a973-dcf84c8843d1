<template>
  <div class="layout">
    <div class="header-wrapper" v-show="layoutStore.headerVisible">
      <!-- 原有的 header 内容 -->
    </div>
    <!-- 其他布局内容 -->
  </div>
</template>

<script setup lang="ts">
import { useLayoutStore } from '@/stores/layout'
import { onMounted, onUnmounted } from 'vue'

const layoutStore = useLayoutStore()

// 添加全局鼠标事件监听
onMounted(() => {
  document.addEventListener('mouseup', layoutStore.hideHeader)
})

onUnmounted(() => {
  document.removeEventListener('mouseup', layoutStore.hideHeader)
})
</script> 