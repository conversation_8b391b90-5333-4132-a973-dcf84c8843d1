package content_creator

import (
	"frontapi/internal/models/content_creator"
	contentRepo "frontapi/internal/repository/content_creator"
	"frontapi/internal/service/base"
)

type RevenueRuleService interface {
	base.IExtendedService[content_creator.RevenueRule]
}

type revenueRuleService struct {
	*base.ExtendedService[content_creator.RevenueRule]
	repo contentRepo.RevenueRuleRepository
}

func NewRevenueRuleService(repo contentRepo.RevenueRuleRepository) RevenueRuleService {
	return &revenueRuleService{
		ExtendedService: base.NewExtendedService[content_creator.RevenueRule](repo, "revenue_rule"),
		repo:            repo,
	}
}
