import { request } from '@/service/request';
import { ApiResponse } from "@/types/https";
import { AxiosProgressEvent, AxiosRequestConfig, CancelTokenSource } from 'axios';
import axios from 'axios';

export interface FileItem {
  name: string;
  path: string;
  url: string;
  type: string; // 'image' | 'video' | 'directory'
  size: number;
  is_dir: boolean;
  modified_at: string;
  full_path?: string; // 完整路径，包括基础路径
}

export interface FileListRequest {
  data: {
    file_type: string; // 'image' | 'video' | 'all'
    path: string;
    search?: string;
    limit?: number;
  };
}

export interface FileListResponse {
  files: FileItem[];
  total: number;
}

export interface CreateDirectoryRequest {
  data: {
    file_type: string; // 'image' | 'video'
    parent_path: string;
    dir_name: string;
  };
}

export interface RenameFileRequest {
  data: {
    file_type: string; // 'image' | 'video'
    old_path: string;
    new_name: string;
  };
}

export interface DeleteFileRequest {
  data: {
    file_type: string; // 'image' | 'video'
    path: string;
    full_path?: string; // 完整路径，包括基础路径
  };
}

// Upload progress callbacks
export interface UploadCallbacks {
  onProgress?: (progress: number, speed: string, remainingTime: string) => void;
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
  onComplete?: () => void;
}

// Upload options
export interface UploadOptions {
  file: File;
  fileType: string; // 'image' | 'video'
  path?: string;
  callbacks?: UploadCallbacks;
  cancelToken?: CancelTokenSource;
}

/**
 * Get file list
 * @param params Query parameters
 * @returns Promise with file list response
 */
export function getFileList(params: FileListRequest): Promise<ApiResponse<FileListResponse>> {
  return request({
    url: '/files/list',
    method: 'post',
    data: params,
  });
}

/**
 * Upload file with advanced options and progress tracking
 * @param options Upload options
 * @returns Cancel function to abort the upload
 */
export function uploadFileWithProgress(options: UploadOptions): () => void {
  const { file, fileType, path = '', callbacks } = options;
  const cancelToken = options.cancelToken || axios.CancelToken.source();
  
  // Create form data
  const formData = new FormData();
  formData.append('file', file);
  formData.append('file_type', fileType);
  formData.append('path', path);

  // Track upload stats
  let startTime = Date.now();
  let lastLoaded = 0;
  let lastTime = startTime;
  let speedHistory: number[] = []; // Store recent speeds for averaging
  
  // Initialize progress state in case it takes time to start
  if (callbacks?.onProgress) {
    callbacks.onProgress(0, '正在准备上传...', '计算中...');
  }
  
  // Calculate upload speed and remaining time
  const handleProgress = (event: AxiosProgressEvent) => {
    if (!callbacks?.onProgress) return;
    
    const progress = Math.round((event.loaded / (event.total || file.size)) * 100);
    
    // Calculate speed - update every 500ms for stability
    const now = Date.now();
    const timeDiff = now - lastTime;
    
    if (timeDiff > 500) {
      const loadedDiff = event.loaded - lastLoaded;
      const speed = loadedDiff / (timeDiff / 1000); // bytes per second
      
      // Add to speed history (keep last 5 for average)
      speedHistory.push(speed);
      if (speedHistory.length > 5) {
        speedHistory.shift();
      }
      
      // Calculate average speed for more stability
      const avgSpeed = speedHistory.reduce((sum, s) => sum + s, 0) / speedHistory.length;
      
      // Format speed
      let speedText = '';
      if (avgSpeed < 1024) {
        speedText = `${avgSpeed.toFixed(2)} B/s`;
      } else if (avgSpeed < 1024 * 1024) {
        speedText = `${(avgSpeed / 1024).toFixed(2)} KB/s`;
      } else {
        speedText = `${(avgSpeed / (1024 * 1024)).toFixed(2)} MB/s`;
      }
      
      // Calculate remaining time
      const remainingBytes = (event.total || file.size) - event.loaded;
      const remainingSecs = avgSpeed > 0 ? remainingBytes / avgSpeed : 0;
      
      // Format remaining time
      let remainingTimeText = '';
      if (remainingSecs < 1) {
        remainingTimeText = '不到1秒';
      } else if (remainingSecs < 60) {
        remainingTimeText = `${Math.round(remainingSecs)}秒`;
      } else if (remainingSecs < 3600) {
        const minutes = Math.floor(remainingSecs / 60);
        const secs = Math.round(remainingSecs % 60);
        remainingTimeText = `${minutes}分${secs}秒`;
      } else {
        const hours = Math.floor(remainingSecs / 3600);
        const minutes = Math.floor((remainingSecs % 3600) / 60);
        remainingTimeText = `${hours}小时${minutes}分钟`;
      }
      
      // Update last values
      lastTime = now;
      lastLoaded = event.loaded;
      
      callbacks.onProgress(progress, speedText, remainingTimeText);
    } else {
      // Just update progress without recalculating speed
      callbacks.onProgress(progress, '', '');
    }
  };

  // Send request with proper error handling
  request({
    url: '/files/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: handleProgress,
    cancelToken: cancelToken.token,
    timeout: 3600000, // 1 hour timeout for large files
  })
    .then(response => {
      if (callbacks?.onSuccess) {
        callbacks.onSuccess(response);
      }
    })
    .catch(error => {
      if (axios.isCancel(error)) {
        console.log('Upload canceled by user');
      } else {
        console.error('Upload error:', error);
        if (callbacks?.onError) {
          callbacks.onError(error);
        }
      }
    })
    .finally(() => {
      // Clean up resources
      speedHistory = [];
      
      if (callbacks?.onComplete) {
        callbacks.onComplete();
      }
    });

  // Return cancel function
  return () => {
    cancelToken.cancel('Upload canceled by user');
  };
}

/**
 * Upload file (simple version)
 * @param fileData Form data
 * @param onProgress Progress callback
 * @returns Promise with upload response
 */
export function uploadFile(
  fileData: FormData,
  onProgress?: (progressEvent: AxiosProgressEvent) => void
): Promise<ApiResponse<any>> {
  return request({
    url: '/files/upload',
    method: 'post',
    data: fileData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: onProgress,
    timeout: 3600000, // 1 hour timeout for large files
  });
}

/**
 * Create directory
 * @param params Directory data
 * @returns Promise with creation response
 */
export function createDirectory(params: CreateDirectoryRequest): Promise<ApiResponse<any>> {
  return request({
    url: '/files/create-directory',
    method: 'post',
    data: params,
  });
}

/**
 * Rename file or directory
 * @param params Rename data
 * @returns Promise with rename response
 */
export function renameFile(params: RenameFileRequest): Promise<ApiResponse<any>> {
  return request({
    url: '/files/rename',
    method: 'post',
    data: params,
  });
}

/**
 * Delete file or directory
 * @param params Delete data
 * @returns Promise with delete response
 */
export function deleteFile(params: DeleteFileRequest): Promise<ApiResponse<any>> {
  return request({
    url: '/files/delete',
    method: 'post',
    data: params,
  });
}