package comics

import (
	"frontapi/internal/admin"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	comicModel "frontapi/internal/models/comics"
	comicSrv "frontapi/internal/service/comics"
	comicValidator "frontapi/internal/validation/comics"

	"github.com/gofiber/fiber/v2"
)

// ComicChapterController 漫画章节控制器
type ComicChapterController struct {
	admin.BaseController
	service comicSrv.ComicChapterService
}

// NewComicChapterController 创建漫画章节控制器实例
func NewComicChapterController(service comicSrv.ComicChapterService) *ComicChapterController {
	return &ComicChapterController{
		service: service,
	}
}

// List 获取漫画章节列表
func (c *ComicChapterController) List(ctx *fiber.Ctx) error {
	// 获取分页参数
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	comicID := reqInfo.Get("comic_id").GetString()
	title := reqInfo.Get("title").GetString()
	status := -999
	err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	condition := map[string]interface{}{
		"comic_id": comicID,
		"title":    title,
		"status":   status,
	}
	// 获取章节列表
	chapters, total, err := c.service.List(ctx.Context(), condition, "chapter_number ASC", pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取章节列表失败: "+err.Error())
	}

	return c.SuccessList(ctx, chapters, total, pageNo, pageSize)
}

// Create 创建漫画章节
func (c *ComicChapterController) Create(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req comicValidator.CreateChapterRequest
	err := validator.ValidateDataWrapper(ctx, &req)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败"+err.Error(), nil)
	}

	// 创建章节
	var chapter comicModel.ComicChapter
	if err := utils.SmartCopy(req, &chapter); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	chapterID, err := c.service.Create(ctx.Context(), &chapter)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.Success(ctx, map[string]interface{}{"id": chapterID})
}

// Update 更新漫画章节
func (c *ComicChapterController) Update(ctx *fiber.Ctx) error {
	// 获取章节ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "章节ID不能为空", nil)
	}

	// 解析请求参数
	var req comicValidator.UpdateChapterRequest
	err = c.ParseRequestData(ctx, &req)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	var chapter comicModel.ComicChapter
	if err := utils.SmartCopy(req, &chapter); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 更新章节
	if err := c.service.UpdateById(ctx.Context(), id, &chapter); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新章节成功")
}

// Delete 删除漫画章节
func (c *ComicChapterController) Delete(ctx *fiber.Ctx) error {
	// 获取章节ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "章节ID不能为空", nil)
	}

	// 删除章节
	if err := c.service.Delete(ctx.Context(), id); err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除章节成功")
}

// GetByID 获取漫画章节详情
func (c *ComicChapterController) GetByID(ctx *fiber.Ctx) error {
	// 获取章节ID
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	if id == "" {
		return c.BadRequest(ctx, "章节ID不能为空", nil)
	}

	// 获取章节详情
	chapter, err := c.service.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取章节详情失败: "+err.Error())
	}

	return c.Success(ctx, chapter)
}

// 添加批量更新章节顺序请求结构
type BatchUpdateChapterOrderRequest struct {
	ComicID  string                      `json:"comic_id" validate:"required"`
	Chapters []comicSrv.ChapterOrderItem `json:"chapters" validate:"required|minLen:1"`
}

// BatchUpdateOrder 批量更新章节顺序
func (c *ComicChapterController) BatchUpdateOrder(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req BatchUpdateChapterOrderRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "参数解析失败: "+err.Error(), nil)
	}

	// 批量更新章节顺序
	if err := c.service.BatchUpdateChapterOrder(ctx.Context(), req.ComicID, req.Chapters); err != nil {
		return c.InternalServerError(ctx, "更新章节顺序失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "章节顺序更新成功")
}
