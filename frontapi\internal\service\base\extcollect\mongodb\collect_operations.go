package mongodb

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extcollect/types"
)

// CollectOperations MongoDB收藏操作处理器
type CollectOperations struct {
	collection *mongo.Collection
}

// NewCollectOperations 创建收藏操作处理器
func NewCollectOperations(collection *mongo.Collection) *CollectOperations {
	return &CollectOperations{
		collection: collection,
	}
}

// Collect 收藏项目
func (c *CollectOperations) Collect(ctx context.Context, userID, itemID, itemType string) error {
	record := &types.CollectRecord{
		UserID:    userID,
		ItemID:    itemID,
		ItemType:  itemType,
		Timestamp: time.Now(),
		Status:    "collected",
		Version:   1,
	}

	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": record,
		"$inc": bson.M{"version": 1},
	}

	opts := options.Update().SetUpsert(true)
	_, err := c.collection.UpdateOne(ctx, filter, update, opts)
	return err
}

// Uncollect 取消收藏
func (c *CollectOperations) Uncollect(ctx context.Context, userID, itemID, itemType string) error {
	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": bson.M{
			"status":    "uncollected",
			"timestamp": time.Now(),
		},
		"$inc": bson.M{"version": 1},
	}

	_, err := c.collection.UpdateOne(ctx, filter, update)
	return err
}

// IsCollected 检查是否已收藏
func (c *CollectOperations) IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
		"status":    "collected",
	}

	count, err := c.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetCollectCount 获取收藏数量
func (c *CollectOperations) GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error) {
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
		"status":    "collected",
	}

	return c.collection.CountDocuments(ctx, filter)
}

// BatchCollect 批量收藏
func (c *CollectOperations) BatchCollect(ctx context.Context, operations []*types.CollectOperation) error {
	var models []mongo.WriteModel

	for _, op := range operations {
		if op.Action == "collect" {
			filter := bson.M{
				"user_id":   op.UserID,
				"item_id":   op.ItemID,
				"item_type": op.ItemType,
			}

			update := bson.M{
				"$set": bson.M{
					"user_id":   op.UserID,
					"item_id":   op.ItemID,
					"item_type": op.ItemType,
					"timestamp": op.Timestamp,
					"status":    "collected",
					"metadata":  op.Metadata,
				},
				"$inc": bson.M{"version": 1},
			}

			model := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update).
				SetUpsert(true)

			models = append(models, model)
		}
	}

	if len(models) == 0 {
		return nil
	}

	_, err := c.collection.BulkWrite(ctx, models)
	return err
}

// BatchUncollect 批量取消收藏
func (c *CollectOperations) BatchUncollect(ctx context.Context, operations []*types.CollectOperation) error {
	var models []mongo.WriteModel

	for _, op := range operations {
		if op.Action == "uncollect" {
			filter := bson.M{
				"user_id":   op.UserID,
				"item_id":   op.ItemID,
				"item_type": op.ItemType,
			}

			update := bson.M{
				"$set": bson.M{
					"status":    "uncollected",
					"timestamp": op.Timestamp,
				},
				"$inc": bson.M{"version": 1},
			}

			model := mongo.NewUpdateOneModel().
				SetFilter(filter).
				SetUpdate(update)

			models = append(models, model)
		}
	}

	if len(models) == 0 {
		return nil
	}

	_, err := c.collection.BulkWrite(ctx, models)
	return err
}
