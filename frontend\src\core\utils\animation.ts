/**
 * 动画工具函数
 */

/**
 * 缓动函数类型
 */
export type EasingFunction = (t: number) => number

/**
 * 动画配置接口
 */
export interface AnimationConfig {
  duration?: number // 持续时间（毫秒）
  delay?: number // 延迟时间（毫秒）
  easing?: EasingFunction | string // 缓动函数
  iterations?: number // 迭代次数
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse'
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both'
  playState?: 'running' | 'paused'
}

/**
 * 关键帧接口
 */
export interface Keyframe {
  offset?: number // 0-1之间的偏移量
  [property: string]: any // CSS属性
}

/**
 * 动画实例接口
 */
export interface AnimationInstance {
  id: string
  element: HTMLElement
  animation: Animation | null
  config: AnimationConfig
  onComplete?: () => void
  onUpdate?: (progress: number) => void
  play(): void
  pause(): void
  stop(): void
  reverse(): void
  restart(): void
  setProgress(progress: number): void
  getProgress(): number
  isRunning(): boolean
}

/**
 * 预定义缓动函数
 */
export const easingFunctions: Record<string, EasingFunction> = {
  // 线性
  linear: (t: number) => t,
  
  // 二次方
  easeInQuad: (t: number) => t * t,
  easeOutQuad: (t: number) => t * (2 - t),
  easeInOutQuad: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  
  // 三次方
  easeInCubic: (t: number) => t * t * t,
  easeOutCubic: (t: number) => (--t) * t * t + 1,
  easeInOutCubic: (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
  
  // 四次方
  easeInQuart: (t: number) => t * t * t * t,
  easeOutQuart: (t: number) => 1 - (--t) * t * t * t,
  easeInOutQuart: (t: number) => t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t,
  
  // 五次方
  easeInQuint: (t: number) => t * t * t * t * t,
  easeOutQuint: (t: number) => 1 + (--t) * t * t * t * t,
  easeInOutQuint: (t: number) => t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t,
  
  // 正弦
  easeInSine: (t: number) => 1 - Math.cos(t * Math.PI / 2),
  easeOutSine: (t: number) => Math.sin(t * Math.PI / 2),
  easeInOutSine: (t: number) => -(Math.cos(Math.PI * t) - 1) / 2,
  
  // 指数
  easeInExpo: (t: number) => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),
  easeOutExpo: (t: number) => t === 1 ? 1 : 1 - Math.pow(2, -10 * t),
  easeInOutExpo: (t: number) => {
    if (t === 0) return 0
    if (t === 1) return 1
    if (t < 0.5) return Math.pow(2, 20 * t - 10) / 2
    return (2 - Math.pow(2, -20 * t + 10)) / 2
  },
  
  // 圆形
  easeInCirc: (t: number) => 1 - Math.sqrt(1 - t * t),
  easeOutCirc: (t: number) => Math.sqrt(1 - (--t) * t),
  easeInOutCirc: (t: number) => {
    if (t < 0.5) return (1 - Math.sqrt(1 - 4 * t * t)) / 2
    return (Math.sqrt(1 - (-2 * t + 2) * (-2 * t + 2)) + 1) / 2
  },
  
  // 弹性
  easeInElastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3
    return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4)
  },
  easeOutElastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1
  },
  easeInOutElastic: (t: number) => {
    const c5 = (2 * Math.PI) / 4.5
    if (t === 0) return 0
    if (t === 1) return 1
    if (t < 0.5) return -(Math.pow(2, 20 * t - 10) * Math.sin((20 * t - 11.125) * c5)) / 2
    return (Math.pow(2, -20 * t + 10) * Math.sin((20 * t - 11.125) * c5)) / 2 + 1
  },
  
  // 回弹
  easeInBounce: (t: number) => 1 - easingFunctions.easeOutBounce(1 - t),
  easeOutBounce: (t: number) => {
    const n1 = 7.5625
    const d1 = 2.75
    if (t < 1 / d1) {
      return n1 * t * t
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375
    }
  },
  easeInOutBounce: (t: number) => {
    if (t < 0.5) return (1 - easingFunctions.easeOutBounce(1 - 2 * t)) / 2
    return (1 + easingFunctions.easeOutBounce(2 * t - 1)) / 2
  },
  
  // 超出
  easeInBack: (t: number) => {
    const c1 = 1.70158
    const c3 = c1 + 1
    return c3 * t * t * t - c1 * t * t
  },
  easeOutBack: (t: number) => {
    const c1 = 1.70158
    const c3 = c1 + 1
    return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2)
  },
  easeInOutBack: (t: number) => {
    const c1 = 1.70158
    const c2 = c1 * 1.525
    if (t < 0.5) {
      return (Math.pow(2 * t, 2) * ((c2 + 1) * 2 * t - c2)) / 2
    }
    return (Math.pow(2 * t - 2, 2) * ((c2 + 1) * (t * 2 - 2) + c2) + 2) / 2
  }
}

/**
 * 动画管理器
 */
export class AnimationManager {
  private animations = new Map<string, AnimationInstance>()
  private rafId: number | null = null
  private isRunning = false

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `anim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取缓动函数
   */
  private getEasingFunction(easing: EasingFunction | string): EasingFunction {
    if (typeof easing === 'function') {
      return easing
    }
    return easingFunctions[easing] || easingFunctions.linear
  }

  /**
   * 创建CSS动画
   */
  createCSSAnimation(
    element: HTMLElement,
    keyframes: Keyframe[],
    config: AnimationConfig = {}
  ): AnimationInstance {
    const id = this.generateId()
    const defaultConfig: Required<AnimationConfig> = {
      duration: 1000,
      delay: 0,
      easing: 'linear',
      iterations: 1,
      direction: 'normal',
      fillMode: 'both',
      playState: 'running'
    }
    
    const mergedConfig = { ...defaultConfig, ...config }
    
    // 转换缓动函数为CSS值
    let easingValue: string
    if (typeof mergedConfig.easing === 'string') {
      // 将自定义缓动函数转换为cubic-bezier
      const easingMap: Record<string, string> = {
        linear: 'linear',
        easeInQuad: 'cubic-bezier(0.55, 0.085, 0.68, 0.53)',
        easeOutQuad: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        easeInOutQuad: 'cubic-bezier(0.455, 0.03, 0.515, 0.955)',
        easeInCubic: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
        easeOutCubic: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
        easeInOutCubic: 'cubic-bezier(0.645, 0.045, 0.355, 1)'
      }
      easingValue = easingMap[mergedConfig.easing] || 'linear'
    } else {
      easingValue = 'linear'
    }
    
    const animation = element.animate(keyframes, {
      duration: mergedConfig.duration,
      delay: mergedConfig.delay,
      easing: easingValue,
      iterations: mergedConfig.iterations,
      direction: mergedConfig.direction,
      fill: mergedConfig.fillMode
    })
    
    if (mergedConfig.playState === 'paused') {
      animation.pause()
    }
    
    const instance: AnimationInstance = {
      id,
      element,
      animation,
      config: mergedConfig,
      
      play() {
        animation.play()
      },
      
      pause() {
        animation.pause()
      },
      
      stop() {
        animation.cancel()
      },
      
      reverse() {
        animation.reverse()
      },
      
      restart() {
        animation.cancel()
        const newAnimation = element.animate(keyframes, {
          duration: mergedConfig.duration,
          delay: mergedConfig.delay,
          easing: easingValue,
          iterations: mergedConfig.iterations,
          direction: mergedConfig.direction,
          fill: mergedConfig.fillMode
        })
        instance.animation = newAnimation
      },
      
      setProgress(progress: number) {
        const time = progress * mergedConfig.duration
        animation.currentTime = time
      },
      
      getProgress(): number {
        const currentTime = animation.currentTime || 0
        return Math.min(1, Math.max(0, currentTime / mergedConfig.duration))
      },
      
      isRunning(): boolean {
        return animation.playState === 'running'
      }
    }
    
    // 添加事件监听
    animation.addEventListener('finish', () => {
      instance.onComplete?.()
      this.animations.delete(id)
    })
    
    this.animations.set(id, instance)
    return instance
  }

  /**
   * 创建JavaScript动画
   */
  createJSAnimation(
    callback: (progress: number, value: number) => void,
    config: AnimationConfig & {
      from?: number
      to?: number
    } = {}
  ): AnimationInstance {
    const id = this.generateId()
    const defaultConfig = {
      duration: 1000,
      delay: 0,
      easing: 'linear',
      from: 0,
      to: 1,
      iterations: 1,
      direction: 'normal',
      fillMode: 'both',
      playState: 'running'
    }
    
    const mergedConfig = { ...defaultConfig, ...config }
    const easingFn = this.getEasingFunction(mergedConfig.easing)
    
    let startTime: number | null = null
    let isPlaying = mergedConfig.playState === 'running'
    let isPaused = false
    let pausedTime = 0
    let currentIteration = 0
    let isReversed = false
    
    const animate = (timestamp: number) => {
      if (!isPlaying) return
      
      if (!startTime) {
        startTime = timestamp + mergedConfig.delay
      }
      
      if (timestamp < startTime) {
        requestAnimationFrame(animate)
        return
      }
      
      const elapsed = timestamp - startTime - pausedTime
      let progress = Math.min(1, elapsed / mergedConfig.duration)
      
      // 应用缓动函数
      const easedProgress = easingFn(progress)
      
      // 计算当前值
      const range = mergedConfig.to - mergedConfig.from
      const currentValue = mergedConfig.from + range * easedProgress
      
      // 处理方向
      let finalProgress = easedProgress
      let finalValue = currentValue
      
      if (mergedConfig.direction === 'reverse' || 
          (mergedConfig.direction === 'alternate' && currentIteration % 2 === 1) ||
          (mergedConfig.direction === 'alternate-reverse' && currentIteration % 2 === 0)) {
        finalProgress = 1 - easedProgress
        finalValue = mergedConfig.to - range * easedProgress
      }
      
      callback(finalProgress, finalValue)
      instance.onUpdate?.(finalProgress)
      
      if (progress >= 1) {
        currentIteration++
        
        if (mergedConfig.iterations === Infinity || currentIteration < mergedConfig.iterations) {
          startTime = timestamp
          pausedTime = 0
        } else {
          isPlaying = false
          instance.onComplete?.()
          this.animations.delete(id)
          return
        }
      }
      
      if (isPlaying) {
        requestAnimationFrame(animate)
      }
    }
    
    const instance: AnimationInstance = {
      id,
      element: document.createElement('div'), // 占位元素
      animation: null,
      config: mergedConfig,
      
      play() {
        if (!isPlaying) {
          isPlaying = true
          isPaused = false
          requestAnimationFrame(animate)
        }
      },
      
      pause() {
        if (isPlaying) {
          isPlaying = false
          isPaused = true
        }
      },
      
      stop() {
        isPlaying = false
        isPaused = false
        startTime = null
        pausedTime = 0
        currentIteration = 0
      },
      
      reverse() {
        isReversed = !isReversed
        // 重新计算方向
      },
      
      restart() {
        this.stop()
        this.play()
      },
      
      setProgress(progress: number) {
        const targetTime = progress * mergedConfig.duration
        if (startTime) {
          pausedTime = performance.now() - startTime - targetTime
        }
      },
      
      getProgress(): number {
        if (!startTime) return 0
        const elapsed = performance.now() - startTime - pausedTime
        return Math.min(1, Math.max(0, elapsed / mergedConfig.duration))
      },
      
      isRunning(): boolean {
        return isPlaying
      }
    }
    
    this.animations.set(id, instance)
    
    if (mergedConfig.playState === 'running') {
      instance.play()
    }
    
    return instance
  }

  /**
   * 获取动画实例
   */
  getAnimation(id: string): AnimationInstance | undefined {
    return this.animations.get(id)
  }

  /**
   * 停止动画
   */
  stopAnimation(id: string): void {
    const animation = this.animations.get(id)
    if (animation) {
      animation.stop()
      this.animations.delete(id)
    }
  }

  /**
   * 停止所有动画
   */
  stopAllAnimations(): void {
    this.animations.forEach(animation => animation.stop())
    this.animations.clear()
  }

  /**
   * 暂停所有动画
   */
  pauseAllAnimations(): void {
    this.animations.forEach(animation => animation.pause())
  }

  /**
   * 恢复所有动画
   */
  resumeAllAnimations(): void {
    this.animations.forEach(animation => animation.play())
  }

  /**
   * 获取运行中的动画数量
   */
  getRunningCount(): number {
    let count = 0
    this.animations.forEach(animation => {
      if (animation.isRunning()) count++
    })
    return count
  }

  /**
   * 清理已完成的动画
   */
  cleanup(): void {
    const toDelete: string[] = []
    this.animations.forEach((animation, id) => {
      if (!animation.isRunning()) {
        toDelete.push(id)
      }
    })
    toDelete.forEach(id => this.animations.delete(id))
  }
}

/**
 * 动画序列管理器
 */
export class AnimationSequence {
  private animations: Array<{
    animation: () => AnimationInstance
    delay?: number
  }> = []
  private currentIndex = 0
  private isPlaying = false
  private onComplete?: () => void
  private onStep?: (index: number) => void

  /**
   * 添加动画到序列
   */
  add(animation: () => AnimationInstance, delay = 0): this {
    this.animations.push({ animation, delay })
    return this
  }

  /**
   * 播放序列
   */
  play(): void {
    if (this.isPlaying || this.animations.length === 0) return
    
    this.isPlaying = true
    this.currentIndex = 0
    this.playNext()
  }

  /**
   * 播放下一个动画
   */
  private playNext(): void {
    if (this.currentIndex >= this.animations.length) {
      this.isPlaying = false
      this.onComplete?.()
      return
    }
    
    const { animation, delay } = this.animations[this.currentIndex]
    
    const playAnimation = () => {
      const instance = animation()
      instance.onComplete = () => {
        this.onStep?.(this.currentIndex)
        this.currentIndex++
        this.playNext()
      }
    }
    
    if (delay && delay > 0) {
      setTimeout(playAnimation, delay)
    } else {
      playAnimation()
    }
  }

  /**
   * 停止序列
   */
  stop(): void {
    this.isPlaying = false
    this.currentIndex = 0
  }

  /**
   * 设置完成回调
   */
  setOnComplete(callback: () => void): this {
    this.onComplete = callback
    return this
  }

  /**
   * 设置步骤回调
   */
  setOnStep(callback: (index: number) => void): this {
    this.onStep = callback
    return this
  }

  /**
   * 清空序列
   */
  clear(): void {
    this.animations = []
    this.currentIndex = 0
    this.isPlaying = false
  }
}

/**
 * 动画工具类
 */
export class AnimationUtils {
  /**
   * 淡入动画
   */
  static fadeIn(
    element: HTMLElement,
    duration = 300,
    easing: EasingFunction | string = 'easeOutQuad'
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { opacity: 0 },
        { opacity: 1 }
      ],
      { duration, easing }
    )
  }

  /**
   * 淡出动画
   */
  static fadeOut(
    element: HTMLElement,
    duration = 300,
    easing: EasingFunction | string = 'easeOutQuad'
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { opacity: 1 },
        { opacity: 0 }
      ],
      { duration, easing }
    )
  }

  /**
   * 滑入动画
   */
  static slideIn(
    element: HTMLElement,
    direction: 'up' | 'down' | 'left' | 'right' = 'up',
    duration = 300,
    easing: EasingFunction | string = 'easeOutQuad'
  ): AnimationInstance {
    const transforms: Record<string, string> = {
      up: 'translateY(100%)',
      down: 'translateY(-100%)',
      left: 'translateX(100%)',
      right: 'translateX(-100%)'
    }

    return animationManager.createCSSAnimation(
      element,
      [
        { transform: transforms[direction], opacity: 0 },
        { transform: 'translate(0, 0)', opacity: 1 }
      ],
      { duration, easing }
    )
  }

  /**
   * 滑出动画
   */
  static slideOut(
    element: HTMLElement,
    direction: 'up' | 'down' | 'left' | 'right' = 'up',
    duration = 300,
    easing: EasingFunction | string = 'easeOutQuad'
  ): AnimationInstance {
    const transforms: Record<string, string> = {
      up: 'translateY(-100%)',
      down: 'translateY(100%)',
      left: 'translateX(-100%)',
      right: 'translateX(100%)'
    }

    return animationManager.createCSSAnimation(
      element,
      [
        { transform: 'translate(0, 0)', opacity: 1 },
        { transform: transforms[direction], opacity: 0 }
      ],
      { duration, easing }
    )
  }

  /**
   * 缩放动画
   */
  static scale(
    element: HTMLElement,
    from = 0,
    to = 1,
    duration = 300,
    easing: EasingFunction | string = 'easeOutBack'
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { transform: `scale(${from})`, opacity: from === 0 ? 0 : 1 },
        { transform: `scale(${to})`, opacity: to === 0 ? 0 : 1 }
      ],
      { duration, easing }
    )
  }

  /**
   * 旋转动画
   */
  static rotate(
    element: HTMLElement,
    from = 0,
    to = 360,
    duration = 1000,
    easing: EasingFunction | string = 'linear'
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { transform: `rotate(${from}deg)` },
        { transform: `rotate(${to}deg)` }
      ],
      { duration, easing }
    )
  }

  /**
   * 弹跳动画
   */
  static bounce(
    element: HTMLElement,
    intensity = 20,
    duration = 600,
    iterations = 1
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { transform: 'translateY(0)' },
        { transform: `translateY(-${intensity}px)`, offset: 0.25 },
        { transform: 'translateY(0)', offset: 0.5 },
        { transform: `translateY(-${intensity / 2}px)`, offset: 0.75 },
        { transform: 'translateY(0)' }
      ],
      { duration, easing: 'easeOutBounce', iterations }
    )
  }

  /**
   * 摇摆动画
   */
  static shake(
    element: HTMLElement,
    intensity = 10,
    duration = 600,
    iterations = 1
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { transform: 'translateX(0)' },
        { transform: `translateX(-${intensity}px)`, offset: 0.1 },
        { transform: `translateX(${intensity}px)`, offset: 0.2 },
        { transform: `translateX(-${intensity}px)`, offset: 0.3 },
        { transform: `translateX(${intensity}px)`, offset: 0.4 },
        { transform: `translateX(-${intensity}px)`, offset: 0.5 },
        { transform: `translateX(${intensity}px)`, offset: 0.6 },
        { transform: `translateX(-${intensity}px)`, offset: 0.7 },
        { transform: `translateX(${intensity}px)`, offset: 0.8 },
        { transform: `translateX(-${intensity}px)`, offset: 0.9 },
        { transform: 'translateX(0)' }
      ],
      { duration, iterations }
    )
  }

  /**
   * 脉冲动画
   */
  static pulse(
    element: HTMLElement,
    scale = 1.1,
    duration = 1000,
    iterations = Infinity
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { transform: 'scale(1)' },
        { transform: `scale(${scale})` },
        { transform: 'scale(1)' }
      ],
      { duration, easing: 'easeInOutSine', iterations }
    )
  }

  /**
   * 闪烁动画
   */
  static blink(
    element: HTMLElement,
    duration = 1000,
    iterations = Infinity
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { opacity: 1 },
        { opacity: 0 },
        { opacity: 1 }
      ],
      { duration, iterations }
    )
  }

  /**
   * 数值动画
   */
  static animateNumber(
    from: number,
    to: number,
    duration: number,
    callback: (value: number) => void,
    easing: EasingFunction | string = 'easeOutQuad'
  ): AnimationInstance {
    return animationManager.createJSAnimation(
      (progress, value) => {
        callback(Math.round(value))
      },
      { from, to, duration, easing }
    )
  }

  /**
   * 颜色动画
   */
  static animateColor(
    element: HTMLElement,
    property: string,
    from: string,
    to: string,
    duration = 300,
    easing: EasingFunction | string = 'linear'
  ): AnimationInstance {
    return animationManager.createCSSAnimation(
      element,
      [
        { [property]: from },
        { [property]: to }
      ],
      { duration, easing }
    )
  }

  /**
   * 路径动画
   */
  static animatePath(
    element: HTMLElement,
    path: Array<{ x: number; y: number }>,
    duration = 1000,
    easing: EasingFunction | string = 'linear'
  ): AnimationInstance {
    const keyframes = path.map((point, index) => ({
      transform: `translate(${point.x}px, ${point.y}px)`,
      offset: index / (path.length - 1)
    }))

    return animationManager.createCSSAnimation(
      element,
      keyframes,
      { duration, easing }
    )
  }

  /**
   * 创建动画序列
   */
  static createSequence(): AnimationSequence {
    return new AnimationSequence()
  }

  /**
   * 并行执行多个动画
   */
  static parallel(
    animations: Array<() => AnimationInstance>
  ): Promise<void> {
    const promises = animations.map(animation => {
      return new Promise<void>(resolve => {
        const instance = animation()
        instance.onComplete = resolve
      })
    })

    return Promise.all(promises).then(() => {})
  }

  /**
   * 延迟执行
   */
  static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * 默认动画管理器实例
 */
export const animationManager = new AnimationManager()

/**
 * 创建动画管理器
 */
export function createAnimationManager(): AnimationManager {
  return new AnimationManager()
}

/**
 * 创建动画序列
 */
export function createAnimationSequence(): AnimationSequence {
  return new AnimationSequence()
}

// 导出工具实例
export const animationUtils = AnimationUtils

// 导出快捷方法
export const {
  fadeIn,
  fadeOut,
  slideIn,
  slideOut,
  scale,
  rotate,
  bounce,
  shake,
  pulse,
  blink,
  animateNumber,
  animateColor,
  animatePath,
  createSequence,
  parallel,
  delay
} = AnimationUtils