# Redis缓存配置与使用说明

## 概述

本项目使用Redis作为缓存系统，主要用于以下用途：
1. 缓存用户登录令牌（Token）
2. 缓存热门视频数据
3. 缓存视频列表等查询结果

## 安装Redis

### Windows系统
1. 下载Redis Windows版本：https://github.com/microsoftarchive/redis/releases
2. 安装并启动Redis服务
3. 默认情况下，Redis服务运行在127.0.0.1:6379

### Linux系统
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
```

## 配置

在`.env`文件中配置Redis连接信息：

```
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_EXPIRY=168 # Redis缓存过期时间（小时）
```

## 项目中的Redis使用

### 初始化Redis连接

Redis连接在应用启动时自动初始化：

```go
// 初始化Redis
if err := redis.Init(); err != nil {
    log.Printf("警告: Redis初始化失败: %v", err)
} else {
    log.Println("Redis连接成功")
}
```

### 基本缓存操作

```go
// 设置缓存（带过期时间）
redis.Set("key", "value", 1*time.Hour)

// 获取缓存
value, err := redis.Get("key")

// 删除缓存
redis.Del("key")

// 检查键是否存在
exists, err := redis.Exists("key")
```

### 用户Token缓存

```go
// 存储用户Token
userID := uint(1)
token := "jwt-token-string"
expiration := 24 * time.Hour
redis.SetToken(userID, token, expiration)

// 获取用户Token
token, err := redis.GetToken(userID)

// 删除用户Token（用户退出登录）
redis.DeleteToken(userID)
```

### 视频数据缓存

```go
// 缓存热门视频
videoID := "video-123"
videoData := `{"id":"video-123", "title":"视频标题", ...}`
redis.SetHotVideo(videoID, videoData, 1*time.Hour)

// 获取热门视频缓存
videoData, err := redis.GetHotVideo(videoID)
```

## 缓存策略

1. **用户Token**：
   - 过期时间：与JWT令牌相同，默认7天
   - 存储格式：`user:token:{userID}` = `{token}`

2. **视频数据**：
   - 过期时间：常规视频7天，热门视频（超过1000次观看）1小时
   - 存储格式：`video:{videoID}` = `{json数据}`

3. **视频列表**：
   - 过期时间：30分钟
   - 存储格式：`videos:list:{page}:{pageSize}` = `{json数组}`

4. **分类视频列表**：
   - 过期时间：30分钟
   - 存储格式：`videos:category:{categoryID}:{page}:{pageSize}` = `{json数组}`

5. **用户点赞记录**：
   - 过期时间：7天
   - 存储格式：`video:like:{videoID}:{userID}` = `1`

## 缓存失效策略

1. **主动失效**：当资源更新时，主动删除相关缓存
   - 视频更新/删除时，删除对应视频缓存
   - 点赞/取消点赞时，删除对应视频缓存
   - 视频观看时，删除对应视频缓存以更新观看计数

2. **被动失效**：通过设置过期时间，自动失效
   - 对热门数据设置较短的过期时间
   - 对不常更新的数据设置较长的过期时间

## 测试Redis连接

可以使用以下命令测试Redis连接：

```bash
# 运行测试脚本
go run scripts/test_redis.go
```

或者访问API端点：

```
GET /api/redis/status
```

响应示例：

```json
{
  "code": 2000,
  "message": "Redis服务正常",
  "data": {
    "status": "ok",
    "hits": 10,
    "misses": 2,
    "timeouts": 0,
    "totalConns": 5,
    "idleConns": 3,
    "staleConns": 0
  }
}
``` 