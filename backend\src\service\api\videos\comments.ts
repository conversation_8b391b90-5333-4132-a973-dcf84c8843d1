import { request } from "../../request";

// 评论参数接口
export interface CommentParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        videoId?: string;
        keyword?: string;
        userId?: string;
        status?: number;
        created_at_start?: string;
        created_at_end?: string;
    }
}

// 评论数据结构接口
export interface CommentItem {
    id: string;
    content: string;
    user_id: string;
    user_nickname: string;
    user_avatar: string;
    parent_id?: string;
    video_id: string;
    heat?: number;
    like_count: number;
    reply_count?: number;
    reply_to_id?: string;
    reply_to_user?: string;
    status: number;
    created_at: string;
    updated_at: string;
    ip_address?: string;
    [key: string]: any;
}

/**
 * 获取视频评论列表
 */
export function getVideoCommentList(params?: CommentParams) {
    return request({
        url: '/video-comments/list',
        method: 'post',
        data: {
            data: params?.data,
            page: params?.page
        }
    });
}

/**
 * 获取评论详情
 */
export function getCommentDetail(id: string) {
    return request({
        url: `/video-comments/detail/${id}`,
        method: 'post',
        data: {
            data: {
                id: id
            }
        }
    });
}

/**
 * 获取评论回复列表
 */
export function getCommentReplies(commentId: string, page = 1, pageSize = 10) {
    return request({
        url: '/video-comments/replies',
        method: 'post',
        data: {
            data: {
                comment_id: commentId,
            },
            page: {
                page_no: page,
                page_size: pageSize
            }
        }
    });
}

/**
 * 删除评论
 */
export function deleteComment(id: string) {
    return request({
        url: `/video-comments/delete/${id}`,
        method: 'post'
    });
}

/**
 * 更新评论状态
 */
export function updateCommentStatus(id: string, status: number) {
    return request({
        url: '/video-comments/update-status',
        method: 'post',
        data: {
            data: {
                id,
                status
            }
        }
    });
}

/**
 * 批量更新评论状态
 */
export function batchUpdateCommentStatus(data: { ids: string[], status: number }) {
    return request({
        url: '/video-comments/batch-update-status',
        method: 'post',
        data: {
            data: {
                ids: data.ids,
                status: data.status
            }
        }
    });
}

/**
 * 批量删除评论
 */
export function batchDeleteComment(data: { ids: string[] }) {
    return request({
        url: '/video-comments/batch-delete',
        method: 'post',
        data: {
            data: {
                ids: data.ids
            }
        }
    });
}
