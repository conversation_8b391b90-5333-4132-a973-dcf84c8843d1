/**
 * Aura主题配置
 */
import { THEME_COLORS, ThemeConfig } from '@/config/theme.config';

// Aura主题列表
export const auraThemes: ThemeConfig[] = [
    // Indigo
    {
        name: 'aura-light-indigo',
        code: 'auraIndigo',
        displayName: 'Indigo',
        isDark: false,
        primary: THEME_COLORS.indigo.light,
        themeFamily: 'aura',
        colorScheme: 'indigo'
    },
    {
        name: 'aura-dark-indigo',
        code: 'auraIndigo',
        displayName: 'Indigo',
        isDark: true,
        primary: THEME_COLORS.indigo.dark,
        themeFamily: 'aura',
        colorScheme: 'indigo'
    },

    // Blue
    {
        name: 'aura-light-blue',
        code: 'auraBlue',
        displayName: 'Blue',
        isDark: false,
        primary: THEME_COLORS.blue.light,
        themeFamily: 'aura',
        colorScheme: 'blue'
    },
    {
        name: 'aura-dark-blue',
        code: 'auraBlue',
        displayName: 'Blue',
        isDark: true,
        primary: THEME_COLORS.blue.dark,
        themeFamily: 'aura',
        colorScheme: 'blue'
    },

    // Green
    {
        name: 'aura-light-green',
        code: 'auraGreen',
        displayName: 'Green',
        isDark: false,
        primary: THEME_COLORS.green.light,
        themeFamily: 'aura',
        colorScheme: 'green'
    },
    {
        name: 'aura-dark-green',
        code: 'auraGreen',
        displayName: 'Green',
        isDark: true,
        primary: THEME_COLORS.green.dark,
        themeFamily: 'aura',
        colorScheme: 'green'
    },

    // Purple
    {
        name: 'aura-light-purple',
        code: 'auraPurple',
        displayName: 'Purple',
        isDark: false,
        primary: THEME_COLORS.purple.light,
        themeFamily: 'aura',
        colorScheme: 'purple'
    },
    {
        name: 'aura-dark-purple',
        code: 'auraPurple',
        displayName: 'Purple',
        isDark: true,
        primary: THEME_COLORS.purple.dark,
        themeFamily: 'aura',
        colorScheme: 'purple'
    },

    // Orange
    {
        name: 'aura-light-orange',
        code: 'auraOrange',
        displayName: 'Orange',
        isDark: false,
        primary: THEME_COLORS.orange.light,
        themeFamily: 'aura',
        colorScheme: 'orange'
    },
    {
        name: 'aura-dark-orange',
        code: 'auraOrange',
        displayName: 'Orange',
        isDark: true,
        primary: THEME_COLORS.orange.dark,
        themeFamily: 'aura',
        colorScheme: 'orange'
    },

    // Rose
    {
        name: 'aura-light-rose',
        code: 'auraRose',
        displayName: 'Rose',
        isDark: false,
        primary: THEME_COLORS.rose.light,
        themeFamily: 'aura',
        colorScheme: 'rose'
    },
    {
        name: 'aura-dark-rose',
        code: 'auraRose',
        displayName: 'Rose',
        isDark: true,
        primary: THEME_COLORS.rose.dark,
        themeFamily: 'aura',
        colorScheme: 'rose'
    },

    // Fuchsia
    {
        name: 'aura-light-fuchsia',
        code: 'auraFuchsia',
        displayName: 'Fuchsia',
        isDark: false,
        primary: THEME_COLORS.fuchsia.light,
        themeFamily: 'aura',
        colorScheme: 'fuchsia'
    },
    {
        name: 'aura-dark-fuchsia',
        code: 'auraFuchsia',
        displayName: 'Fuchsia',
        isDark: true,
        primary: THEME_COLORS.fuchsia.dark,
        themeFamily: 'aura',
        colorScheme: 'fuchsia'
    },

    // Noir
    {
        name: 'aura-light-noir',
        code: 'auraNoir',
        displayName: 'Noir',
        isDark: false,
        primary: THEME_COLORS.noir.light,
        themeFamily: 'aura',
        colorScheme: 'noir'
    },
    {
        name: 'aura-dark-noir',
        code: 'auraNoir',
        displayName: 'Noir',
        isDark: true,
        primary: THEME_COLORS.noir.dark,
        themeFamily: 'aura',
        colorScheme: 'noir'
    }
]; 