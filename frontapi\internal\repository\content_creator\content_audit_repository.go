package content_creator

import (
	"frontapi/internal/models/content_creator"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type ContentAuditRepository interface {
	base.ExtendedRepository[content_creator.ContentAudit]
}

type contentAuditRepository struct {
	base.ExtendedRepository[content_creator.ContentAudit]
}

func NewContentAuditRepository(db *gorm.DB) ContentAuditRepository {
	return &contentAuditRepository{
		ExtendedRepository: base.NewExtendedRepository[content_creator.ContentAudit](db),
	}
}
