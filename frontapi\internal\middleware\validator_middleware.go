package middleware

import (
	"frontapi/pkg/validator"
	"reflect"

	"github.com/gofiber/fiber/v2"
	"github.com/gookit/validate"
)

// ValidateRequest 创建请求验证中间件
func ValidateRequest(schema interface{}) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 创建新的结构体实例
		val := reflect.ValueOf(schema)
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}

		// 创建新的相同类型的实例
		requestData := reflect.New(val.Type()).Interface()

		// 解析请求体到结构体
		if err := c.BodyParser(requestData); err != nil {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "无法解析请求数据",
			})
		}

		// 注册自定义验证规则
		if err := validator.RegisterCustomValidations(validator.New()); err != nil {
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"success": false,
				"message": "验证器初始化失败",
			})
		}

		// 使用gookit/validate验证
		v := validate.Struct(requestData)
		if !v.Validate() {
			errorMessages := make([]fiber.Map, 0)
			for field, fieldErrors := range v.Errors.All() {
				for _, errMsg := range fieldErrors {
					errorMessages = append(errorMessages, fiber.Map{
						"field": field,
						"error": errMsg,
					})
				}
			}

			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
				"success": false,
				"message": "请求数据验证失败",
				"errors":  errorMessages,
			})
		}

		// 将验证通过的数据存储在上下文中，供后续处理器使用
		c.Locals("validatedData", requestData)

		return c.Next()
	}
}
