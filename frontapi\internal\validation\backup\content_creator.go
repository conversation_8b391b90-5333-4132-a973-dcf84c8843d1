package validation

// CreatorApplyRequest 创作者申请请求验证模型
type CreatorApplyRequest struct {
	RealName     string `json:"realName" validate:"required|minLen:2|maxLen:50"`
	IDCardNumber string `json:"idCardNumber" validate:"required|idcard"`
	IDCardFront  string `json:"idCardFront" validate:"required|url"`
	IDCardBack   string `json:"idCardBack" validate:"required|url"`
	Phone        string `json:"phone" validate:"required|mobile"`
	Email        string `json:"email" validate:"required|email"`
	Introduction string `json:"introduction" validate:"required|minLen:10|maxLen:500"`
	Category     string `json:"category" validate:"required|in:video,music,picture,article,comic"`
	SampleWorks  string `json:"sampleWorks" validate:"required|url"`
}

// CreatorProfileUpdateRequest 创作者资料更新请求验证模型
type CreatorProfileUpdateRequest struct {
	Introduction string   `json:"introduction" validate:"minLen:10|maxLen:500"`
	Category     []string `json:"category" validate:"each:in:video,music,picture,article,comic"`
	SampleWorks  []string `json:"sampleWorks" validate:"each:url"`
	Social       struct {
		Weibo    string `json:"weibo" validate:"url"`
		Wechat   string `json:"wechat"`
		Bilibili string `json:"bilibili" validate:"url"`
		Youtube  string `json:"youtube" validate:"url"`
		Website  string `json:"website" validate:"url"`
	} `json:"social"`
}

// CreatorIncomeWithdrawRequest 创作者收益提现请求验证模型
type CreatorIncomeWithdrawRequest struct {
	Amount      float64 `json:"amount" validate:"required|gt:0"`
	Account     string  `json:"account" validate:"required"`
	AccountType string  `json:"accountType" validate:"required|in:alipay,wechat,bank"`
	RealName    string  `json:"realName" validate:"requiredIf:AccountType,bank"`
	BankName    string  `json:"bankName" validate:"requiredIf:AccountType,bank"`
}

// CreatorChannelCreateRequest 创作者频道创建请求验证模型
type CreatorChannelCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"required,min=10,max=500"`
	Avatar      string `json:"avatar" validate:"required,url"`
	Banner      string `json:"banner" validate:"required,url"`
	Category    string `json:"category" validate:"required,oneof=video music picture article comic"`
}

// CreatorChannelUpdateRequest 创作者频道更新请求验证模型
type CreatorChannelUpdateRequest struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=50"`
	Description string `json:"description" validate:"omitempty,min=10,max=500"`
	Avatar      string `json:"avatar" validate:"omitempty,url"`
	Banner      string `json:"banner" validate:"omitempty,url"`
	Status      int    `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:正常 1:隐藏 2:封禁
}

// CreatorIncomeListRequest 创作者收益列表请求验证模型
type CreatorIncomeListRequest struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"pageSize" validate:"min=1,max=100"`
	StartDate string `json:"startDate" validate:"omitempty,datetime=2006-01-02"`
	EndDate   string `json:"endDate" validate:"omitempty,datetime=2006-01-02,gtfield=StartDate"`
	Type      string `json:"type" validate:"omitempty,oneof=all video music picture article comic ad"`
}
