package permission

// CasbinRule Casbin权限规则模型
type CasbinRule struct {
	ID    int    `json:"id" gorm:"column:id;primaryKey;autoIncrement;comment:规则ID"`
	PType string `json:"ptype" gorm:"column:ptype;type:varchar(100);not null;comment:策略类型"`
	V0    string `json:"v0" gorm:"column:v0;type:varchar(100);comment:参数0"`
	V1    string `json:"v1" gorm:"column:v1;type:varchar(100);comment:参数1"`
	V2    string `json:"v2" gorm:"column:v2;type:varchar(100);comment:参数2"`
	V3    string `json:"v3" gorm:"column:v3;type:varchar(100);comment:参数3"`
	V4    string `json:"v4" gorm:"column:v4;type:varchar(100);comment:参数4"`
	V5    string `json:"v5" gorm:"column:v5;type:varchar(100);comment:参数5"`
}

// TableName 指定表名
func (CasbinRule) TableName() string {
	return "ly_casbin_rule"
}

// RuleType 规则类型常量
const (
	// 策略规则类型
	PolicyTypeP  = "p"  // 权限策略 (角色, 资源, 操作)
	PolicyTypeG  = "g"  // 角色继承 (用户, 角色)
	PolicyTypeG2 = "g2" // 角色层级继承 (角色, 父角色)
)

// GetRuleTypeDesc 获取规则类型描述
func GetRuleTypeDesc(ptype string) string {
	switch ptype {
	case PolicyTypeP:
		return "权限策略"
	case PolicyTypeG:
		return "用户角色"
	case PolicyTypeG2:
		return "角色继承"
	default:
		return "未知类型"
	}
}

// PermissionRule 权限规则结构体
type PermissionRule struct {
	Role     string `json:"role"`     // 角色
	Resource string `json:"resource"` // 资源
	Action   string `json:"action"`   // 操作
}

// UserRoleRule 用户角色规则结构体
type UserRoleRule struct {
	UserID string `json:"user_id"` // 用户ID
	Role   string `json:"role"`    // 角色
}

// RoleInheritRule 角色继承规则结构体
type RoleInheritRule struct {
	Role       string `json:"role"`        // 子角色
	ParentRole string `json:"parent_role"` // 父角色
}

// ToPermissionRule 转换为权限规则
func (r *CasbinRule) ToPermissionRule() *PermissionRule {
	if r.PType != PolicyTypeP {
		return nil
	}
	return &PermissionRule{
		Role:     r.V0,
		Resource: r.V1,
		Action:   r.V2,
	}
}

// ToUserRoleRule 转换为用户角色规则
func (r *CasbinRule) ToUserRoleRule() *UserRoleRule {
	if r.PType != PolicyTypeG {
		return nil
	}
	return &UserRoleRule{
		UserID: r.V0,
		Role:   r.V1,
	}
}

// ToRoleInheritRule 转换为角色继承规则
func (r *CasbinRule) ToRoleInheritRule() *RoleInheritRule {
	if r.PType != PolicyTypeG2 {
		return nil
	}
	return &RoleInheritRule{
		Role:       r.V0,
		ParentRole: r.V1,
	}
}

// FromPermissionRule 从权限规则创建
func FromPermissionRule(rule *PermissionRule) *CasbinRule {
	return &CasbinRule{
		PType: PolicyTypeP,
		V0:    rule.Role,
		V1:    rule.Resource,
		V2:    rule.Action,
	}
}

// FromUserRoleRule 从用户角色规则创建
func FromUserRoleRule(rule *UserRoleRule) *CasbinRule {
	return &CasbinRule{
		PType: PolicyTypeG,
		V0:    rule.UserID,
		V1:    rule.Role,
	}
}

// FromRoleInheritRule 从角色继承规则创建
func FromRoleInheritRule(rule *RoleInheritRule) *CasbinRule {
	return &CasbinRule{
		PType: PolicyTypeG2,
		V0:    rule.Role,
		V1:    rule.ParentRole,
	}
}

// String 返回规则的字符串表示
func (r *CasbinRule) String() string {
	return r.PType + ", " + r.V0 + ", " + r.V1 + ", " + r.V2 + ", " + r.V3 + ", " + r.V4 + ", " + r.V5
}
