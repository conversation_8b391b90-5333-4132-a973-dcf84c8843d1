package permission

// CreateAdminMenuRequest 创建菜单请求
type CreateAdminMenuRequest struct {
	ParentID   int    `json:"parent_id"`
	Title      string `json:"title" validate:"required|minLen:2|maxLen:100"`
	Name       string `json:"name" validate:"required|minLen:2|maxLen:100"`
	Path       string `json:"path"`
	Component  string `json:"component"`
	Icon       string `json:"icon"`
	Permission string `json:"permission"`
	Type       int8   `json:"type" validate:"required|min:1|max:3"`
	Sort       int    `json:"sort"`
	IsHidden   int8   `json:"is_hidden"`
	IsCache    int8   `json:"is_cache"`
	IsFrame    int8   `json:"is_frame"`
	Remark     string `json:"remark"`
}

// UpdateAdminMenuRequest 更新菜单请求
type UpdateAdminMenuRequest struct {
	ParentID   int    `json:"parent_id"`
	Title      string `json:"title"`
	Name       string `json:"name"`
	Path       string `json:"path"`
	Component  string `json:"component"`
	Icon       string `json:"icon"`
	Permission string `json:"permission"`
	Type       int8   `json:"type"`
	Sort       int    `json:"sort"`
	IsHidden   int8   `json:"is_hidden"`
	IsCache    int8   `json:"is_cache"`
	IsFrame    int8   `json:"is_frame"`
	Status     int8   `json:"status"`
	Remark     string `json:"remark"`
}

// AdminMenuQuery 管理员菜单查询参数
type AdminMenuQuery struct {
	Title      string `json:"title"`
	Permission string `json:"permission"`
	Type       *int8  `json:"type"`
	Status     *int8  `json:"status"`
	ParentID   int    `json:"parent_id"`
}

// AdminMenuBatchUpdateRequest 批量更新菜单状态请求
type AdminMenuBatchUpdateRequest struct {
	IDs    []int `json:"ids" validate:"required"`
	Status int8  `json:"status" validate:"required|min:0|max:1"`
}

// MenuSortItem 菜单排序项
type MenuSortItem struct {
	ID   int `json:"id"`
	Sort int `json:"sort"`
}

// AdminMenuBatchSortUpdateRequest 批量更新菜单排序请求
type AdminMenuBatchSortUpdateRequest struct {
	SortData []MenuSortItem `json:"sort_data" validate:"required"`
}
