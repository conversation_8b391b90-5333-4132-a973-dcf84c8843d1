<template>
  <div class="table-selector">
    <el-form :inline="true" class="search-form">
      <el-form-item label="选择表">
        <el-select 
          v-model="selectedTable" 
          placeholder="请选择或输入表名进行搜索" 
          clearable 
          filterable
          remote
          :remote-method="handleRemoteSearch"
          :loading="loading"
          style="width: 350px;"
          @change="handleTableChange"
        >
          <el-option
            v-for="table in filteredTableList"
            :key="table?.table_name || ''"
            :label="`${table?.table_name || '未知表'} (${table?.table_comment || '无注释'})`"
            :value="table?.table_name || ''"
            :disabled="!table?.table_name"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="success" @click="refreshTableList" :loading="loading">
          刷新表列表
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表统计信息 -->
    <div v-if="tableList.length > 0" class="table-stats">
      <el-tag type="info" size="small">
        共 {{ tableList.length }} 张表
        <span v-if="filteredTableList.length !== tableList.length">
          ，筛选后 {{ filteredTableList.length }} 张
        </span>
      </el-tag>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { getTableList } from '@/service/api/database/database';
import type { TableInfo } from '@/types/database';

// Props
interface Props {
  modelValue: string;
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'table-changed', tableName: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const tableList = ref<TableInfo[]>([]);
const filteredTableList = ref<TableInfo[]>([]);
const searchKeyword = ref('');

// 计算属性
const selectedTable = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
});

// 过滤表列表
const filterTableList = (keyword: string = '') => {
  if (!keyword.trim()) {
    filteredTableList.value = tableList.value;
    return;
  }

  const lowerKeyword = keyword.toLowerCase();
  filteredTableList.value = tableList.value.filter(table => {
    const tableName = (table?.table_name || '').toLowerCase();
    const tableComment = (table?.table_comment || '').toLowerCase();
    return tableName.includes(lowerKeyword) || tableComment.includes(lowerKeyword);
  });
};

// 处理远程搜索
const handleRemoteSearch = (query: string) => {
  searchKeyword.value = query;
  filterTableList(query);
};

// 获取表列表
const getTableListData = async () => {
  try {
    loading.value = true;
    console.log('开始请求表列表...');
    const response = await getTableList();
    console.log('API响应:', response);
    
    // 检查响应是否成功
    if (response && Array.isArray(response)) {
      // 如果响应直接是数组
      const validTables = response.filter(
        (table: any) => table && table.table_name && typeof table.table_name === 'string'
      );
      console.log('过滤后的表列表:', validTables);
      tableList.value = validTables;
      filteredTableList.value = validTables; // 初始化过滤列表
    } else if (response && response.data && Array.isArray(response.data)) {
      // 如果响应有data字段，使用data字段的内容
      const validTables = response.data.filter(
        (table: any) => table && table.table_name && typeof table.table_name === 'string'
      );
      console.log('过滤后的表列表:', validTables);
      tableList.value = validTables;
      filteredTableList.value = validTables; // 初始化过滤列表
    } else {
      console.error('API响应格式不正确:', response);
      ElMessage.error('API响应格式不正确');
      tableList.value = [];
      filteredTableList.value = []; // 清空过滤列表
    }
  } catch (error: any) {
    console.error('获取表列表错误:', error);
    console.error('错误详情:', {
      message: error?.message,
      status: error?.response?.status,
      statusText: error?.response?.statusText,
      data: error?.response?.data,
      config: error?.config
    });
    
    // 检查是否是认证错误
    if (error?.response?.status === 401 || error?.code === 4001) {
      ElMessage.error('请先登录后再使用此功能');
    } else if (error?.response?.status === 403 || error?.code === 4003) {
      ElMessage.error('没有权限访问此功能');
    } else if (error?.response?.status === 405) {
      ElMessage.error('请求方法不允许，请检查API配置');
    } else {
      ElMessage.error('获取表列表失败: ' + (error?.message || '未知错误'));
    }
    tableList.value = [];
    filteredTableList.value = []; // 清空过滤列表
  } finally {
    loading.value = false;
  }
};

// 刷新表列表
const refreshTableList = () => {
  getTableListData();
};

// 处理表选择变化
const handleTableChange = (tableName: string) => {
  console.log('表选择变化:', tableName);
  selectedTable.value = tableName;
  emit('table-changed', tableName);
};

// 初始化
onMounted(() => {
  getTableListData();
});

// 暴露方法给父组件
defineExpose({
  refreshTableList
});
</script>

<style scoped>
.table-selector {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.table-stats {
  margin-top: 10px;
  text-align: right;
}
</style> 