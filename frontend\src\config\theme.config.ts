/**
 * 主题配置文件
 */

// 导入主题数据
import { availableThemes, primaryColorsMapping, surfacesMapping } from '@/shared/themes';

// 主题类型定义
export type ThemeFamily = 'aura' | 'lara';
export type ThemeMode = 'light' | 'dark';
export type ThemeStyle = string; // 可以是任何颜色代码，如'purple', 'blue'等

// 主题状态类 - 单例模式
export class ThemeState {
    // 主题数据
    public primaryColors: any = primaryColorsMapping;
    public surfaces: any = surfacesMapping;
    public themePresets: any = availableThemes;

    // 主题配置
    public themeFamily: ThemeFamily = 'aura';
    public themeStyle: ThemeStyle = 'purple';
    public themeMode: ThemeMode = 'light';
    public themeName: string = 'aura-purple-light';

    // 颜色配置
    public primaryColor: string = 'purple';
    public surface: string = "purple";

    // 单例实现
    private static instance: ThemeState;

    private constructor() {
        // 私有构造函数，防止外部实例化
    }

    public static getInstance(): ThemeState {
        if (!ThemeState.instance) {
            ThemeState.instance = new ThemeState();
        }
        return ThemeState.instance;
    }
}

// 主题配置接口
export interface ThemeConfig {
    name: string;
    code: string;
    displayName: string;
    darkName: string;
    lightName: string;
    lightBgColor: string;
    lightTextColor: string;
    darkBgColor: string;
    darkTextColor: string;
}

// 默认主题设置
export const DEFAULT_THEME = 'aura-purple-light';
export const DEFAULT_THEME_FAMILY: ThemeFamily = 'aura';
export const DEFAULT_THEME_STYLE: ThemeStyle = 'purple';
export const DEFAULT_THEME_MODE: ThemeMode = 'light';
export const DEFAULT_FOLLOW_SYSTEM = false;

// 暗色模式 CSS 类名
export const APP_DARK_CLASS = 'sof-dark';

// 本地存储键名
export const STORAGE_THEME_KEY = 'app-theme';
export const STORAGE_THEME_FAMILY_KEY = 'app-theme-family';
export const STORAGE_THEME_STYLE_KEY = 'app-theme-style';
export const STORAGE_THEME_MODE_KEY = 'app-theme-mode';
export const STORAGE_FOLLOW_SYSTEM_KEY = 'app-theme-follow-system';

// 主题变更事件名称
export const THEME_CHANGED_EVENT = 'theme-changed';

// 可用主题颜色配置
export const themeStyles: ThemeConfig[] = [
    { name: 'purple', code: 'purple', displayName: 'Purple', darkName: 'dark-purple', lightName: 'light-purple', lightBgColor: '#673AB7', lightTextColor: '#FFFFFF', darkBgColor: '#673AB7', darkTextColor: '#FFFFFF', },
    { name: 'noir', code: 'noir', displayName: 'Noir', darkName: 'dark-noir', lightName: 'light-noir', lightBgColor: '#334155', lightTextColor: '#FFFFFF', darkBgColor: '#334155', darkTextColor: '#FFFFFF', },
    { name: 'blue', code: 'blue', displayName: 'Blue', darkName: 'dark-blue', lightName: 'light-blue', lightBgColor: '#3B82F6', lightTextColor: '#FFFFFF', darkBgColor: '#3B82F6', darkTextColor: '#FFFFFF', },
    { name: 'green', code: 'green', displayName: 'Green', darkName: 'dark-green', lightName: 'light-green', lightBgColor: '#22C55E', lightTextColor: '#FFFFFF', darkBgColor: '#22C55E', darkTextColor: '#FFFFFF', },
    { name: 'orange', code: 'orange', displayName: 'Orange', darkName: 'dark-orange', lightName: 'light-orange', lightBgColor: '#F97316', lightTextColor: '#FFFFFF', darkBgColor: '#F97316', darkTextColor: '#FFFFFF', },
    { name: 'red', code: 'red', displayName: 'Red', darkName: 'dark-red', lightName: 'light-red', lightBgColor: '#F43F5E', lightTextColor: '#FFFFFF', darkBgColor: '#F43F5E', darkTextColor: '#FFFFFF', },
    { name: 'yellow', code: 'yellow', displayName: 'Yellow', darkName: 'dark-yellow', lightName: 'light-yellow', lightBgColor: '#F59E0B', lightTextColor: '#FFFFFF', darkBgColor: '#F59E0B', darkTextColor: '#FFFFFF', },
    // { name: 'gray', code: 'gray', displayName: 'Gray', darkName: 'dark-gray', lightName: 'light-gray', lightBgColor: '#6B7280', lightTextColor: '#000000', darkBgColor: '#000000', darkTextColor: '#6B7280', },
    { name: 'indigo', code: 'indigo', displayName: 'Indigo', darkName: 'dark-indigo', lightName: 'light-indigo', lightBgColor: '#6366F1', lightTextColor: '#FFFFFF', darkBgColor: '#6366F1', darkTextColor: '#FFFFFF', },
    { name: 'teal', code: 'teal', displayName: 'Teal', darkName: 'dark-teal', lightName: 'light-teal', lightBgColor: '#14B8A6', lightTextColor: '#FFFFFF', darkBgColor: '#14B8A6', darkTextColor: '#FFFFFF', },
    { name: 'pink', code: 'pink', displayName: 'Pink', darkName: 'dark-pink', lightName: 'light-pink', lightBgColor: '#EC4899', lightTextColor: '#FFFFFF', darkBgColor: '#EC4899', darkTextColor: '#FFFFFF', },
    { name: 'fuchsia', code: 'fuchsia', displayName: 'Fuchsia', darkName: 'dark-fuchsia', lightName: 'light-fuchsia', lightBgColor: '#D946EF', lightTextColor: '#FFFFFF', darkBgColor: '#D946EF', darkTextColor: '#FFFFFF', },
    { name: 'rose', code: 'rose', displayName: 'Rose', darkName: 'dark-rose', lightName: 'light-rose', lightBgColor: '#F43F5E', lightTextColor: '#FFFFFF', darkBgColor: '#F43F5E', darkTextColor: '#FFFFFF', }
]