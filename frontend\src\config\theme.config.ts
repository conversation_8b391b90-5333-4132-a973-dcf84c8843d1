/**
 * 主题配置文件
 */

// 主题类型定义
export type ThemeMode = 'light' | 'dark';
export type ThemeFamily = 'aura' | 'lara' | 'md';
export type ThemeColorScheme = 'indigo' | 'blue' | 'green' | 'purple' | 'teal' | 'deeppurple' | 'orange' | 'rose' | 'fuchsia' | 'noir';

// 主题配置接口
export interface ThemeConfig {
    name: string;
    code: string;
    displayName: string;
    isDark: boolean;
    primary: string;
    themeFamily: ThemeFamily;
    colorScheme: ThemeColorScheme;
}

// 默认主题设置
export const DEFAULT_THEME = 'auraLightIndigo';
export const DEFAULT_THEME_FAMILY: ThemeFamily = 'aura';
export const DEFAULT_FOLLOW_SYSTEM = false;

// CDN 路径配置
export const THEME_CDN_PATH = 'https://cdn.jsdelivr.net/npm/primevue@4.3.6/resources/themes/';

// 主题加载配置
export const THEME_LINK_ID = 'primevue-theme-link';

// 暗色模式 CSS 类名
export const DARK_THEME_CLASS = 'dark-theme';
export const APP_DARK_CLASS = 'sof-app-dark';

// 本地存储键名
export const STORAGE_THEME_KEY = 'app-theme';
export const STORAGE_FOLLOW_SYSTEM_KEY = 'app-theme-follow-system';

// 主题变更事件名称
export const THEME_CHANGED_EVENT = 'theme-changed';

// 主题颜色配置
export const THEME_COLORS = {
    indigo: {
        light: '#6366F1',
        dark: '#4F46E5',
        header: 'linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(99, 102, 241, 0.1)',
        footerText: '#4F46E5'
    },
    blue: {
        light: '#3B82F6',
        dark: '#2563EB',
        header: 'linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(59, 130, 246, 0.1)',
        footerText: '#2563EB'
    },
    green: {
        light: '#22C55E',
        dark: '#16A34A',
        header: 'linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(34, 197, 94, 0.1)',
        footerText: '#16A34A'
    },
    purple: {
        light: '#A855F7',
        dark: '#9333EA',
        header: 'linear-gradient(135deg, rgba(168, 85, 247, 0.9) 0%, rgba(147, 51, 234, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(168, 85, 247, 0.1)',
        footerText: '#9333EA'
    },
    teal: {
        light: '#14B8A6',
        dark: '#0D9488',
        header: 'linear-gradient(135deg, rgba(20, 184, 166, 0.9) 0%, rgba(13, 148, 136, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(20, 184, 166, 0.1)',
        footerText: '#0D9488'
    },
    deeppurple: {
        light: '#673AB7',
        dark: '#5E35B1',
        header: 'linear-gradient(135deg, rgba(103, 58, 183, 0.9) 0%, rgba(94, 53, 177, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(103, 58, 183, 0.1)',
        footerText: '#5E35B1'
    },
    orange: {
        light: '#F97316',
        dark: '#EA580C',
        header: 'linear-gradient(135deg, rgba(249, 115, 22, 0.9) 0%, rgba(234, 88, 12, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(249, 115, 22, 0.1)',
        footerText: '#EA580C'
    },
    rose: {
        light: '#F43F5E',
        dark: '#E11D48',
        header: 'linear-gradient(135deg, rgba(244, 63, 94, 0.9) 0%, rgba(225, 29, 72, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(244, 63, 94, 0.1)',
        footerText: '#E11D48'
    },
    fuchsia: {
        light: '#D946EF',
        dark: '#C026D3',
        header: 'linear-gradient(135deg, rgba(217, 70, 239, 0.9) 0%, rgba(192, 38, 211, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(217, 70, 239, 0.1)',
        footerText: '#C026D3'
    },
    noir: {
        light: '#334155',
        dark: '#1E293B',
        header: 'linear-gradient(135deg, rgba(51, 65, 85, 0.9) 0%, rgba(30, 41, 59, 0.9) 100%)',
        headerText: '#ffffff',
        footer: 'rgba(51, 65, 85, 0.1)',
        footerText: '#334155'
    }
}; 