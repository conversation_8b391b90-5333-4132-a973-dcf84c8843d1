<template>
    <el-dialog :title="title" v-model="dialogVisible" width="550px" height="300px" style="padding: 2px;"
        :close-on-click-modal="false" @closed="resetForm" @update:model-value="$emit('update:visible', $event)"
        destroy-on-close>
        <div v-if="selectedCount === 0" class="empty-selection">
            <el-empty description="请先选择要操作的帖子" />
        </div>
        <div v-else class="selection-info">
            <el-alert type="info" :closable="false">
                <template #title>
                    <div class="alert-content">
                        <el-icon class="alert-icon">
                            <InfoFilled />
                        </el-icon>
                        <span>已选择 <strong>{{ selectedCount }}</strong> 个帖子进行{{ operationTypeText }}操作</span>
                    </div>
                </template>
            </el-alert>
        </div>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right"
            v-if="selectedCount > 0">
            <!-- 批量审核 -->
            <template v-if="operationType === 'review'">
                <el-form-item label="审核状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">通过</el-radio>
                        <el-radio :label="2">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>

                <el-form-item v-if="form.status === 2" label="拒绝原因" prop="rejectReason">
                    <el-input v-model="form.rejectReason" type="textarea" :rows="4" placeholder="请输入拒绝原因"></el-input>
                </el-form-item>

                <el-form-item v-if="form.status === 1" label="审核备注" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入审核备注（可选）"></el-input>
                </el-form-item>
            </template>

            <!-- 批量状态更新 -->
            <template v-if="operationType === 'status'">
                <el-form-item label="设置状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="1">影藏</el-radio>
                        <el-radio :label="2">显示</el-radio>
                    </el-radio-group>
                </el-form-item>
            </template>

            <!-- 批量推荐 -->
            <template v-if="operationType === 'featured'">
                <el-form-item label="推荐设置" prop="isFeatured">
                    <el-radio-group v-model="form.isFeatured">
                        <el-radio :label="1">设为推荐</el-radio>
                        <el-radio :label="0">取消推荐</el-radio>
                    </el-radio-group>
                </el-form-item>
            </template>

            <!-- 批量删除 -->
            <template v-if="operationType === 'delete'">
                <div class="delete-warning">
                    <el-alert type="warning" :closable="false" show-icon>
                        <p>确定要删除选中的 {{ selectedCount }} 个帖子吗？</p>
                        <p>此操作不可恢复，删除后数据将无法找回！</p>
                    </el-alert>
                    <el-form-item label="确认删除" prop="confirmDelete">
                        <el-checkbox v-model="form.confirmDelete">我已了解删除操作的风险，并确认执行删除</el-checkbox>
                    </el-form-item>
                </div>
            </template>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm" :loading="submitting"
                    :disabled="operationType === 'delete' && !form.confirmDelete">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { InfoFilled } from '@element-plus/icons-vue';
import { ElMessage, FormInstance } from 'element-plus';
import { computed, reactive, ref } from 'vue';

// 操作类型定义
type OperationType = 'review' | 'status' | 'featured' | 'delete';

// Props
interface Props {
    visible: boolean;
    operationType: OperationType;
    selectedIds: string[];
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    operationType: 'review',
    selectedIds: () => []
});

// Emits
interface Emits {
    'update:visible': [value: boolean];
    'review': [status: number, ids: string[], rejectReason?: string];
    'status': [status: number, ids: string[]];
    'featured': [isFeatured: number, ids: string[]];
    'delete': [ids: string[]];
}

const emit = defineEmits<Emits>();

// 表单引用
const formRef = ref<FormInstance>();

// 对话框可见状态
const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

// 选中数量
const selectedCount = computed(() => props.selectedIds.length);

// 操作类型文本
const operationTypeText = computed(() => {
    switch (props.operationType) {
        case 'review': return '审核';
        case 'status': return '状态更新';
        case 'featured': return '推荐';
        case 'delete': return '删除';
        default: return '';
    }
});

// 对话框标题
const title = computed(() => {
    return `批量${operationTypeText.value}`;
});

// 表单数据
const form = reactive({
    status: 1,
    rejectReason: '',
    remark: '',
    isFeatured: 1,
    confirmDelete: false
});

// 验证规则
const rules = {
    rejectReason: [
        { required: true, message: '请输入拒绝原因', trigger: 'blur' },
        { min: 2, max: 200, message: '拒绝原因长度在 2 到 200 个字符', trigger: 'blur' }
    ],
    confirmDelete: [
        { required: true, message: '请确认删除操作', trigger: 'change' }
    ]
};

// 提交状态
const submitting = ref(false);

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.status = 1;
    form.rejectReason = '';
    form.remark = '';
    form.isFeatured = 1;
    form.confirmDelete = false;
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    // 如果是拒绝状态，需要验证表单
    if (props.operationType === 'review' && form.status === 2) {
        await formRef.value.validate((valid, fields) => {
            if (valid) {
                confirmSubmit();
            } else {
                console.log('表单验证失败', fields);
            }
        });
    } else if (props.operationType === 'delete' && !form.confirmDelete) {
        ElMessage.warning('请先确认删除操作');
        return;
    } else {
        // 其他情况直接提交
        confirmSubmit();
    }
};

// 确认提交
const confirmSubmit = () => {
    if (selectedCount.value === 0) {
        ElMessage.warning('请先选择要操作的帖子');
        return;
    }

    submitting.value = true;
    try {
        switch (props.operationType) {
            case 'review':
                emit('review', form.status, props.selectedIds, form.status === 2 ? form.rejectReason : form.remark);
                break;
            case 'status':
                emit('status', form.status, props.selectedIds);
                break;
            case 'featured':
                emit('featured', form.isFeatured, props.selectedIds);
                break;
            case 'delete':
                emit('delete', props.selectedIds);
                break;
        }

        dialogVisible.value = false;
        resetForm();
    } catch (error) {
        console.error('提交失败', error);
        ElMessage.error('提交失败');
    } finally {
        submitting.value = false;
    }
};
</script>

<style scoped lang="scss">
.el-dialog {
    padding: 2px;

    .el-form {
        min-height: 150px;
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.el-form-item {
    margin-bottom: 10px;
}

.el-textarea {
    width: 100%;
}

.empty-selection {
    padding: 20px 0;
}

.selection-info {
    margin-bottom: 20px;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .alert-icon {
        font-size: 16px;
    }

    strong {
        font-weight: 600;
        margin: 0 4px;
    }
}

.delete-warning {
    margin: 20px 0;

    p {
        margin: 8px 0;
    }
}
</style>