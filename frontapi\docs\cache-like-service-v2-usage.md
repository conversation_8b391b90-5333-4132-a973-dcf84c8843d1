# 优化版缓存点赞服务使用指南

## 概述

本文档介绍如何使用优化版的缓存点赞服务，该服务采用Redis Hash数据结构和可选的MongoDB支持，提供更好的内存管理和数据分析能力。

## 架构对比

### V1版本（简单键值对）
```
Redis存储结构：
- user_likes:shortvideo_comment:user123 = "item1,item2,item3"
- item_likes_count:shortvideo_comment:item1 = "100"
```

### V2版本（Hash优化）
```
Redis存储结构：
- user_likes:shortvideo_comment:user123 = Hash {
    "item1": "1672531200",  // 点赞时间戳
    "item2": "1672531300",
    "item3": "1672531400"
  }
- item_likes:shortvideo_comment:item1 = Hash {
    "count": "100",
    "updated_at": "1672531500",
    "growth_24h": "5"
  }
- hot_items:shortvideo_comment = ZSet {
    "item1": 100.5,  // 分数=点赞数+增长率权重
    "item2": 95.2,
    "item3": 88.7
  }
```

## 配置和初始化

### 1. Redis配置

```go
// config/redis.go
type RedisConfig struct {
    Host     string `yaml:"host"`
    Port     int    `yaml:"port"`
    Password string `yaml:"password"`
    DB       int    `yaml:"db"`
    PoolSize int    `yaml:"pool_size"`
}

// 初始化Redis客户端
func NewRedisClient(config *RedisConfig) *redis.Client {
    return redis.NewClient(&redis.Options{
        Addr:     fmt.Sprintf("%s:%d", config.Host, config.Port),
        Password: config.Password,
        DB:       config.DB,
        PoolSize: config.PoolSize,
    })
}
```

### 2. MongoDB配置（可选）

```go
// config/mongo.go
type MongoConfig struct {
    URI      string `yaml:"uri"`
    Database string `yaml:"database"`
    Timeout  int    `yaml:"timeout"`
}

// 初始化MongoDB客户端
func NewMongoClient(config *MongoConfig) (*mongo.Client, error) {
    ctx, cancel := context.WithTimeout(context.Background(), 
        time.Duration(config.Timeout)*time.Second)
    defer cancel()
    
    return mongo.Connect(ctx, options.Client().ApplyURI(config.URI))
}
```

### 3. 服务初始化

```go
// main.go 或 service初始化文件
func initLikeServices() {
    // Redis客户端
    redisClient := NewRedisClient(&RedisConfig{
        Host:     "localhost",
        Port:     6379,
        Password: "",
        DB:       0,
        PoolSize: 10,
    })
    
    // MongoDB客户端（可选）
    mongoClient, err := NewMongoClient(&MongoConfig{
        URI:      "mongodb://localhost:27017",
        Database: "like_analytics",
        Timeout:  10,
    })
    if err != nil {
        log.Printf("MongoDB连接失败: %v", err)
        mongoClient = nil
    }
    
    // 创建缓存点赞服务
    cacheLikeService := base.NewCacheLikeServiceV2(redisClient, base.CacheLikeConfigV2{
        UserLikesExpiration:    24 * time.Hour,
        ItemLikesExpiration:    12 * time.Hour,
        HotItemsExpiration:     6 * time.Hour,
        BatchSize:              100,
        HotItemsLimit:          1000,
        GrowthRateWindow:       24 * time.Hour,
        EnableHotRanking:       true,
        EnableGrowthTracking:   true,
    })
    
    // 创建MongoDB服务（如果可用）
    var mongoLikeService base.MongoLikeService
    if mongoClient != nil {
        mongoLikeService = base.NewMongoLikeService(mongoClient, "like_analytics")
    }
    
    // 创建评论服务
    commentService := shortvideos.NewShortVideoCommentServiceV2(
        commentRepo,
        commentLikeRepo,
        mongoLikeService,
    )
    
    // 注册到依赖注入容器
    container.Register("commentServiceV2", commentService)
}
```

## 基本使用

### 1. 控制器中的使用

```go
// controllers/shortvideo_comment_controller.go
type ShortVideoCommentController struct {
    commentService shortvideos.ShortVideoCommentServiceV2
}

// 点赞评论
func (c *ShortVideoCommentController) LikeComment(ctx *gin.Context) {
    userID := ctx.GetString("user_id")
    commentID := ctx.Param("comment_id")
    
    err := c.commentService.LikeItem(ctx, userID, commentID)
    if err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{"message": "点赞成功"})
}

// 取消点赞
func (c *ShortVideoCommentController) UnlikeComment(ctx *gin.Context) {
    userID := ctx.GetString("user_id")
    commentID := ctx.Param("comment_id")
    
    err := c.commentService.UnlikeItem(ctx, userID, commentID)
    if err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{"message": "取消点赞成功"})
}

// 获取评论详情（包含点赞信息）
func (c *ShortVideoCommentController) GetComment(ctx *gin.Context) {
    userID := ctx.GetString("user_id")
    commentID := ctx.Param("comment_id")
    
    comment, err := c.commentService.GetCommentWithLikeInfo(ctx, commentID, userID)
    if err != nil {
        ctx.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, comment)
}

// 批量获取评论（包含点赞信息）
func (c *ShortVideoCommentController) GetComments(ctx *gin.Context) {
    userID := ctx.GetString("user_id")
    videoID := ctx.Param("video_id")
    
    // 获取评论ID列表（这里简化处理）
    commentIDs := []string{"comment1", "comment2", "comment3"}
    
    comments, err := c.commentService.GetCommentsWithBatchLikeInfo(ctx, commentIDs, userID)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{"comments": comments})
}

// 获取热门评论
func (c *ShortVideoCommentController) GetHotComments(ctx *gin.Context) {
    videoID := ctx.Param("video_id")
    limit := 20
    
    hotComments, err := c.commentService.GetHotComments(ctx, videoID, limit)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{"hot_comments": hotComments})
}

// 获取用户统计信息
func (c *ShortVideoCommentController) GetUserStats(ctx *gin.Context) {
    userID := ctx.GetString("user_id")
    
    stats, err := c.commentService.GetUserCommentStats(ctx, userID)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, stats)
}
```

### 2. 路由配置

```go
// routes/api.go
func SetupCommentRoutes(router *gin.Engine, controller *ShortVideoCommentController) {
    api := router.Group("/api/v2")
    {
        comments := api.Group("/comments")
        {
            comments.POST("/:comment_id/like", controller.LikeComment)
            comments.DELETE("/:comment_id/like", controller.UnlikeComment)
            comments.GET("/:comment_id", controller.GetComment)
            comments.GET("/video/:video_id", controller.GetComments)
            comments.GET("/video/:video_id/hot", controller.GetHotComments)
        }
        
        users := api.Group("/users")
        {
            users.GET("/me/comment-stats", controller.GetUserStats)
        }
    }
}
```

## 高级功能

### 1. 数据同步任务

```go
// tasks/sync_like_data.go
type LikeDataSyncTask struct {
    commentService shortvideos.ShortVideoCommentServiceV2
    mongoService   base.MongoLikeService
}

// 定时同步缓存数据到数据库
func (t *LikeDataSyncTask) SyncCacheToDatabase() error {
    ctx := context.Background()
    
    // 获取缓存统计信息
    stats, err := t.commentService.GetCacheStats(ctx)
    if err != nil {
        return fmt.Errorf("获取缓存统计失败: %w", err)
    }
    
    log.Printf("缓存统计: %+v", stats)
    
    // 刷新数据到数据库
    err = t.commentService.FlushToDatabase(ctx)
    if err != nil {
        return fmt.Errorf("刷新数据到数据库失败: %w", err)
    }
    
    // 同步到MongoDB（如果配置了）
    if t.mongoService != nil {
        err = t.mongoService.SyncFromRedis(ctx, "shortvideo_comment")
        if err != nil {
            log.Printf("同步到MongoDB失败: %v", err)
        }
    }
    
    return nil
}

// 定时更新热门排行榜
func (t *LikeDataSyncTask) UpdateHotRanking() error {
    ctx := context.Background()
    
    err := t.commentService.UpdateHotRanking(ctx)
    if err != nil {
        return fmt.Errorf("更新热门排行榜失败: %w", err)
    }
    
    return nil
}

// 启动定时任务
func StartSyncTasks(commentService shortvideos.ShortVideoCommentServiceV2, mongoService base.MongoLikeService) {
    task := &LikeDataSyncTask{
        commentService: commentService,
        mongoService:   mongoService,
    }
    
    // 每5分钟同步一次数据
    go func() {
        ticker := time.NewTicker(5 * time.Minute)
        defer ticker.Stop()
        
        for range ticker.C {
            if err := task.SyncCacheToDatabase(); err != nil {
                log.Printf("数据同步失败: %v", err)
            }
        }
    }()
    
    // 每小时更新一次热门排行榜
    go func() {
        ticker := time.NewTicker(1 * time.Hour)
        defer ticker.Stop()
        
        for range ticker.C {
            if err := task.UpdateHotRanking(); err != nil {
                log.Printf("更新热门排行榜失败: %v", err)
            }
        }
    }()
}
```

### 2. 监控和管理接口

```go
// controllers/admin_controller.go
type AdminController struct {
    commentService shortvideos.ShortVideoCommentServiceV2
    mongoService   base.MongoLikeService
}

// 获取缓存统计信息
func (c *AdminController) GetCacheStats(ctx *gin.Context) {
    stats, err := c.commentService.GetCacheStats(ctx)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, stats)
}

// 手动刷新缓存到数据库
func (c *AdminController) FlushCache(ctx *gin.Context) {
    err := c.commentService.FlushToDatabase(ctx)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{"message": "缓存刷新成功"})
}

// 清理过期缓存
func (c *AdminController) CleanExpiredCache(ctx *gin.Context) {
    err := c.commentService.CleanExpiredCache(ctx)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{"message": "过期缓存清理成功"})
}

// 获取MongoDB分析数据
func (c *AdminController) GetAnalytics(ctx *gin.Context) {
    if c.mongoService == nil {
        ctx.JSON(http.StatusServiceUnavailable, gin.H{"error": "MongoDB服务未配置"})
        return
    }
    
    // 获取点赞趋势
    trends, err := c.mongoService.GetLikeTrends(ctx, "shortvideo_comment", 7)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    // 获取用户排行榜
    leaderboard, err := c.mongoService.GetUserLeaderboard(ctx, "shortvideo_comment", 50)
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    ctx.JSON(http.StatusOK, gin.H{
        "trends":      trends,
        "leaderboard": leaderboard,
    })
}
```

## 性能优化建议

### 1. Redis配置优化

```yaml
# redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
```

### 2. 连接池配置

```go
// Redis连接池优化
redisClient := redis.NewClient(&redis.Options{
    Addr:         "localhost:6379",
    PoolSize:     20,              // 连接池大小
    MinIdleConns: 5,               // 最小空闲连接
    MaxRetries:   3,               // 最大重试次数
    DialTimeout:  5 * time.Second, // 连接超时
    ReadTimeout:  3 * time.Second, // 读取超时
    WriteTimeout: 3 * time.Second, // 写入超时
    PoolTimeout:  4 * time.Second, // 连接池超时
})
```

### 3. 批量操作优化

```go
// 使用Pipeline进行批量操作
func (s *cacheLikeServiceV2) BatchOperationOptimized(ctx context.Context, operations []Operation) error {
    pipe := s.redisClient.Pipeline()
    
    for _, op := range operations {
        switch op.Type {
        case "like":
            pipe.HSet(ctx, s.getUserLikesKey(op.UserID), op.ItemID, time.Now().Unix())
            pipe.HIncrBy(ctx, s.getItemLikesKey(op.ItemID), "count", 1)
        case "unlike":
            pipe.HDel(ctx, s.getUserLikesKey(op.UserID), op.ItemID)
            pipe.HIncrBy(ctx, s.getItemLikesKey(op.ItemID), "count", -1)
        }
    }
    
    _, err := pipe.Exec(ctx)
    return err
}
```

## 监控指标

### 1. 关键指标

- **缓存命中率**: 应保持在95%以上
- **响应时间**: 平均响应时间应小于10ms
- **内存使用率**: Redis内存使用率应控制在80%以下
- **数据同步延迟**: 缓存到数据库的同步延迟应小于5分钟

### 2. 告警配置

```yaml
# prometheus告警规则
groups:
  - name: like_service_alerts
    rules:
      - alert: LikeServiceCacheHitRateLow
        expr: like_service_cache_hit_rate < 0.95
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "点赞服务缓存命中率过低"
          
      - alert: LikeServiceResponseTimeLow
        expr: like_service_response_time_p99 > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "点赞服务响应时间过长"
```

## 故障排查

### 1. 常见问题

**问题1**: 缓存命中率低
- 检查缓存过期时间配置
- 检查数据访问模式
- 考虑增加缓存预热

**问题2**: 内存使用过高
- 检查Hash结构是否过大
- 考虑使用数据压缩
- 调整过期策略

**问题3**: 数据不一致
- 检查同步任务是否正常运行
- 检查Redis和数据库的数据差异
- 考虑增加数据校验机制

### 2. 调试工具

```bash
# Redis调试命令
redis-cli --latency-history -i 1
redis-cli info memory
redis-cli --bigkeys
redis-cli monitor

# 查看特定键的信息
redis-cli hgetall user_likes:shortvideo_comment:user123
redis-cli zrange hot_items:shortvideo_comment 0 -1 withscores
```

## 总结

优化版的缓存点赞服务通过使用Redis Hash和ZSet数据结构，提供了更好的内存管理和查询性能。结合可选的MongoDB支持，可以实现复杂的数据分析和统计功能。通过合理的配置和监控，可以构建一个高性能、高可用的点赞服务系统。