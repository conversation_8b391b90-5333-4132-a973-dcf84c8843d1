package books

// BookCreateRequest 书籍创建请求验证模型
type BookCreateRequest struct {
	Title       string   `json:"title" validate:"required,min=2,max=100"`
	Description string   `json:"description" validate:"required,min=10,max=2000"`
	Cover       string   `json:"cover" validate:"required,url"`
	Author      string   `json:"author" validate:"required"`
	CategoryID  uint     `json:"categoryId" validate:"required,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
	ISBN        string   `json:"isbn" validate:"omitempty"`
	Publisher   string   `json:"publisher" validate:"omitempty"`
	PublishDate string   `json:"publishDate" validate:"omitempty,datetime=2006-01-02"`
}

// BookUpdateRequest 书籍更新请求验证模型
type BookUpdateRequest struct {
	Title       string   `json:"title" validate:"omitempty,min=2,max=100"`
	Description string   `json:"description" validate:"omitempty,min=10,max=2000"`
	Cover       string   `json:"cover" validate:"omitempty,url"`
	Author      string   `json:"author" validate:"omitempty"`
	CategoryID  uint     `json:"categoryId" validate:"omitempty,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
	ISBN        string   `json:"isbn" validate:"omitempty"`
	Publisher   string   `json:"publisher" validate:"omitempty"`
	PublishDate string   `json:"publishDate" validate:"omitempty,datetime=2006-01-02"`
}

// BookUpdateStatusRequest 书籍更新状态请求验证模型
type BookUpdateStatusRequest struct {
	ID     string `json:"id" validate:"required,min=1"`
	Status int    `json:"status" validate:"required,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
}

// BookListRequest 书籍列表请求验证模型
type BookListRequest struct {
	Page       int      `json:"page" validate:"min=1"`
	PageSize   int      `json:"pageSize" validate:"min=1,max=100"`
	CategoryID uint     `json:"categoryId" validate:"omitempty,min=1"`
	Status     int      `json:"status" validate:"omitempty,oneof=0 1 2"`
	Tags       []string `json:"tags" validate:"omitempty"`
	SortBy     string   `json:"sortBy" validate:"omitempty,oneof=latest popular favorite update"`
	IsVIP      bool     `json:"isVip" validate:"omitempty"`
	IsAdult    bool     `json:"isAdult" validate:"omitempty"`
}
