package controller

import (
	"errors"
	"frontapi/pkg/utils"

	"github.com/gofiber/fiber/v2"
)

type RequestInterface interface {
	GetRequestInfo(ctx *fiber.Ctx) *utils.RequestInfo
	GetUserID(ctx *fiber.Ctx) string
	RequireLogin(ctx *fiber.Ctx) (string, bool)
	GetBaseRequest(ctx *fiber.Ctx) (*utils.BaseRequest, error)
	GetId(ctx *fiber.Ctx) (string, error)
	GetIntegerValueWithDataWrapper(ctx *fiber.Ctx, key string, target *int) error
}

type RequestTrait struct {
	RequestInterface
}

// GetRequestInfo 获取请求信息，支持链式调用
func (b RequestTrait) GetRequestInfo(c *fiber.Ctx) *utils.RequestInfo {
	return utils.GetRequestInfo(c)
}

// GetUserID 获取当前登录用户ID
func (b RequestTrait) GetUserID(c *fiber.Ctx) string {
	userID := c.Locals("user_id")
	if userID == nil {
		return ""
	}

	return userID.(string)
}

// RequireLogin 检查用户是否已登录，若未登录返回错误
func (b RequestTrait) RequireLogin(c *fiber.Ctx) (string, bool) {
	userID := b.GetUserID(c)
	if userID == "" {
		utils.Unauthorized(c)
		return "", false
	}

	return userID, true
}

// GetBaseRequest 获取基础请求结构
func (b RequestTrait) GetBaseRequest(c *fiber.Ctx) (*utils.BaseRequest, error) {
	// 创建一个新的基础请求对象
	baseReq := utils.NewBaseRequest()

	// 解析请求体到BaseRequest
	if err := c.BodyParser(baseReq); err != nil {
		return nil, err
	}

	// 获取用户信息
	userID := c.Locals("user_id")
	if userID != nil {
		baseReq.SetUserID(userID.(string))
	}

	// 获取用户角色
	role := c.Locals("user_role")
	if role != nil && role.(string) == "admin" {
		baseReq.SetIsAdmin(true)
	}

	// 处理查询参数中的分页信息
	pageNo := c.QueryInt("pageNo", 0)
	pageSize := c.QueryInt("pageSize", 0)

	if pageNo > 0 {
		baseReq.GetPage().PageNo = pageNo
	}
	if pageSize > 0 {
		baseReq.GetPage().PageSize = pageSize
	}

	return baseReq, nil
}

// GetId 从URL参数获取ID
func (b RequestTrait) GetId(ctx *fiber.Ctx) (string, error) {
	id := ctx.Params("id")
	if id == "" {
		return "", errors.New("ID参数缺失")
	}
	return id, nil
}

// GetIntegerValueWithDataWrapper 从请求中获取整数值
func (b RequestTrait) GetIntegerValueWithDataWrapper(ctx *fiber.Ctx, key string, target *int) error {
	reqInfo := b.GetRequestInfo(ctx)
	value := reqInfo.Get(key).GetInt()
	if value == 0 {
		return nil
	}
	*target = value
	return nil
}
