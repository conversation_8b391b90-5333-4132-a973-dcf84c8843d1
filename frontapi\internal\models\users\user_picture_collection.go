package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// PictureCollection 用户图片收藏表
type UserPictureCollection struct {
	models.BaseModel
	UserID          string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                  //用户ID
	PictureID       string         `gorm:"column:picture_id;type:string;not null;comment:图片ID" json:"picture_id"`                            //图片ID
	PictureURL      string         `gorm:"column:picture_url;type:string;size:255;comment:图片URL" json:"picture_url"`                         //图片URL
	PictureTitle    string         `gorm:"column:picture_title;type:string;size:255;comment:图片标题" json:"picture_title"`                      //图片标题
	Width           int            `gorm:"column:width;type:int;comment:宽度" json:"width"`                                                    //宽度
	Height          int            `gorm:"column:height;type:int;comment:高度" json:"height"`                                                  //高度
	AlbumID         string         `gorm:"column:album_id;type:string;comment:专辑ID" json:"album_id"`                                         //专辑ID
	AlbumTitle      string         `gorm:"column:album_title;type:string;size:255;comment:专辑标题" json:"album_title"`                          //专辑标题
	CreatorID       string         `gorm:"column:creator_id;type:string;comment:创作者ID" json:"creator_id"`                                    //创作者ID
	CreatorName     string         `gorm:"column:creator_name;type:string;size:50;comment:创作者名称" json:"creator_name"`                        //创作者名称
	CreatorAvatar   string         `gorm:"column:creator_avatar;type:string;size:255;comment:创作者头像" json:"creator_avatar"`                   //创作者头像
	CategoryID      string         `gorm:"column:category_id;type:string;comment:分类ID" json:"category_id"`                                   //分类ID
	CategoryName    string         `gorm:"column:category_name;type:string;size:50;comment:分类名称" json:"category_name"`                       //分类名称
	CollectionTime  types.JSONTime `gorm:"column:collection_time;type:time;default:current_timestamp;comment:收藏时间" json:"collection_time"`   //收藏时间
	CollectionGroup string         `gorm:"column:collection_group;type:string;size:50;default:'默认收藏夹';comment:收藏分组" json:"collection_group"` //收藏分组
	Note            string         `gorm:"column:note;type:string;size:255;comment:收藏备注" json:"note"`                                        //收藏备注

}

// TableName 表名
func (UserPictureCollection) TableName() string {
	return "ly_user_picture_collections"
}
