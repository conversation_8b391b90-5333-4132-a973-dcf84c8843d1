package comics

// ComicChapterCreateRequest 漫画章节创建请求验证模型
// type ComicChapterCreateRequest struct {
// 	ComicID   uint     `json:"comicId" validate:"required,min=1"`
// 	Title     string   `json:"title" validate:"required,min=2,max=100"`
// 	ChapterNo float64  `json:"chapterNo" validate:"required"` // 支持小数，如 1.5 表示番外
// 	Images    []string `json:"images" validate:"required,dive,url"`
// 	IsVIP     bool     `json:"isVip"`
// 	Price     float64  `json:"price" validate:"omitempty,min=0"`
// }

// // ComicChapterUpdateRequest 漫画章节更新请求验证模型
// type ComicChapterUpdateRequest struct {
// 	Title     string   `json:"title" validate:"omitempty,min=2,max=100"`
// 	ChapterNo float64  `json:"chapterNo" validate:"omitempty"`
// 	Images    []string `json:"images" validate:"omitempty,dive,url"`
// 	IsVIP     bool     `json:"isVip"`
// 	Price     float64  `json:"price" validate:"omitempty,min=0"`
// }

// CreateChapterRequest 创建章节请求
type CreateChapterRequest struct {
	ComicID       uint    `json:"comicId" validate:"required,min=1"`
	Title         string  `json:"title" validate:"required,min=2,max=100"`
	ChapterNumber int     `json:"chapter_number" validate:"required"`
	IsLocked      bool    `json:"is_locked"`
	Price         float64 `json:"price"`
}

// UpdateChapterRequest 更新章节请求
type UpdateChapterRequest struct {
	Title         string   `json:"title"`
	ChapterNumber *int     `json:"chapter_number"`
	IsLocked      *int8    `json:"is_locked"`
	Price         *float64 `json:"price"`
	Status        *int8    `json:"status"`
}
