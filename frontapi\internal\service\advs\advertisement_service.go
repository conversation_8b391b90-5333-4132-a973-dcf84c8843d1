package advs

import (
	"context"
	"fmt"
	"time"

	"frontapi/internal/models"
	"frontapi/internal/models/advs"
	repo "frontapi/internal/repository/advs"
	"frontapi/internal/service/base"
	advValidate "frontapi/internal/validation/advs"
	"frontapi/pkg/types"
)

// AdvertisementService 广告服务接口
type AdvertisementService interface {
	CreateAdvertisement(ctx context.Context, req *advValidate.CreateAdvertisementRequest) (string, error)
	GetAdvertisement(ctx context.Context, id string) (*advs.Advertisement, error)
	UpdateAdvertisement(ctx context.Context, id string, req *advValidate.UpdateAdvertisementRequest) error
	DeleteAdvertisement(ctx context.Context, id string) error
	ListAdvertisements(ctx context.Context, page, pageSize int) ([]*advs.Advertisement, int64, error)
}

// advertisementService 广告服务实现
type advertisementService struct {
	*base.BaseService[advs.Advertisement]
}

// NewAdvertisementService 创建广告服务实例
func NewAdvertisementService(repo repo.AdvertisementRepository) AdvertisementService {
	return &advertisementService{
		BaseService: base.NewBaseService[advs.Advertisement](repo, "advertisement"),
	}
}

// CreateAdvertisement 创建广告
func (s *advertisementService) CreateAdvertisement(ctx context.Context, req *advValidate.CreateAdvertisementRequest) (string, error) {
	adv := &advs.Advertisement{
		BaseModelStruct: models.BaseModelStruct{
			Status: req.Status,
		},
		PositionID: req.PositionID,
		Title:      req.Title,
		Image:      req.Image,
		URL:        req.URL,
		StartTime:  types.JSONTime(req.StartTime),
		EndTime:    types.JSONTime(req.EndTime),
		SortOrder:  req.SortOrder,
		ViewCount:  req.ViewCount,
		ClickCount: req.ClickCount,
	}
	return s.BaseService.Create(ctx, adv)
}

// GetAdvertisement 获取广告详情
func (s *advertisementService) GetAdvertisement(ctx context.Context, id string) (*advs.Advertisement, error) {
	return s.BaseService.GetByID(ctx, id, false)
}

// UpdateAdvertisement 更新广告
func (s *advertisementService) UpdateAdvertisement(ctx context.Context, id string, req *advValidate.UpdateAdvertisementRequest) error {
	adv, err := s.BaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}
	if adv == nil {
		return fmt.Errorf("广告不存在")
	}
	if req.PositionID != "" {
		adv.PositionID = req.PositionID
	}
	if req.Title != "" {
		adv.Title = req.Title
	}
	if req.Image != "" {
		adv.Image = req.Image
	}
	if req.URL != "" {
		adv.URL = req.URL
	}
	if !req.StartTime.IsZero() {
		adv.StartTime = types.JSONTime(req.StartTime)
	}
	if !req.EndTime.IsZero() {
		adv.EndTime = types.JSONTime(req.EndTime)
	}
	if req.SortOrder != 0 {
		adv.SortOrder = req.SortOrder
	}
	if req.ViewCount != 0 {
		adv.ViewCount = req.ViewCount
	}
	if req.ClickCount != 0 {
		adv.ClickCount = req.ClickCount
	}
	if req.Status != 0 {
		adv.Status = req.Status
	}
	adv.UpdatedAt = types.JSONTime(time.Now())
	return s.BaseService.Update(ctx, adv)
}

// DeleteAdvertisement 删除广告
func (s *advertisementService) DeleteAdvertisement(ctx context.Context, id string) error {
	return s.BaseService.Delete(ctx, id)
}

// ListAdvertisements 获取广告列表
func (s *advertisementService) ListAdvertisements(ctx context.Context, page, pageSize int) ([]*advs.Advertisement, int64, error) {
	return s.BaseService.List(ctx, map[string]interface{}{}, "", page, pageSize, false)
}
