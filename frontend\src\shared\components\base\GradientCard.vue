<template>
    <div class="gradient-card" :class="[
        `gradient-card--${type}`,
        { 'gradient-card--hoverable': hoverable },
        { 'gradient-card--bordered': bordered },
        { 'gradient-card--shadow': shadow },
        sizeClass
    ]" @click="handleClick">
        <!-- 卡片头部 -->
        <div v-if="$slots.header || title" class="gradient-card__header">
            <slot name="header">
                <h3 class="gradient-card__title">{{ title }}</h3>
            </slot>
        </div>

        <!-- 卡片内容 -->
        <div class="gradient-card__content">
            <slot></slot>
        </div>

        <!-- 卡片底部 -->
        <div v-if="$slots.footer" class="gradient-card__footer">
            <slot name="footer"></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    // 卡片类型：primary, secondary, accent, success, warning, error, info, default
    type: {
        type: String,
        default: 'default',
        validator: (value: string) => [
            'primary',
            'secondary',
            'accent',
            'success',
            'warning',
            'error',
            'info',
            'default'
        ].includes(value)
    },
    // 卡片尺寸：small, medium, large
    size: {
        type: String,
        default: 'medium',
        validator: (value: string) => ['small', 'medium', 'large'].includes(value)
    },
    // 是否可悬停
    hoverable: {
        type: Boolean,
        default: false
    },
    // 是否有边框
    bordered: {
        type: Boolean,
        default: true
    },
    // 是否有阴影
    shadow: {
        type: Boolean,
        default: true
    },
    // 卡片标题
    title: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['click']);

// 计算尺寸类名
const sizeClass = computed(() => {
    return `gradient-card--${props.size}`;
});

// 处理点击事件
const handleClick = (event: MouseEvent) => {
    emit('click', event);
};
</script>

<style lang="scss" scoped>
.gradient-card {
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;

    // 尺寸变体
    &--small {
        .gradient-card__header {
            padding: 8px 12px;
        }

        .gradient-card__content {
            padding: 12px;
        }

        .gradient-card__footer {
            padding: 8px 12px;
        }

        .gradient-card__title {
            font-size: 1rem;
        }
    }

    &--medium {
        .gradient-card__header {
            padding: 12px 16px;
        }

        .gradient-card__content {
            padding: 16px;
        }

        .gradient-card__footer {
            padding: 12px 16px;
        }

        .gradient-card__title {
            font-size: 1.125rem;
        }
    }

    &--large {
        .gradient-card__header {
            padding: 16px 20px;
        }

        .gradient-card__content {
            padding: 20px;
        }

        .gradient-card__footer {
            padding: 16px 20px;
        }

        .gradient-card__title {
            font-size: 1.25rem;
        }
    }

    // 边框样式
    &--bordered {
        border: 1px solid var(--color-border);
    }

    // 阴影样式
    &--shadow {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // 悬停效果
    &--hoverable {
        cursor: pointer;

        &:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
    }

    // 卡片头部
    &__header {
        border-bottom: 1px solid var(--color-border);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    // 卡片标题
    &__title {
        margin: 0;
        font-weight: 500;
        color: var(--color-text);
    }

    // 卡片底部
    &__footer {
        border-top: 1px solid var(--color-border);
    }

    // 颜色变体
    &--default {
        background: var(--gradient-card, linear-gradient(120deg, rgba(142, 45, 226, 0.1), rgba(122, 37, 201, 0.08), rgba(74, 0, 224, 0.05)));

        &.gradient-card--hoverable:hover {
            background: var(--gradient-card-hover, linear-gradient(120deg, rgba(142, 45, 226, 0.15), rgba(122, 37, 201, 0.12), rgba(74, 0, 224, 0.1)));
        }
    }

    &--primary {
        background: linear-gradient(120deg, rgba(142, 45, 226, 0.15), rgba(122, 37, 201, 0.12), rgba(74, 0, 224, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(142, 45, 226, 0.2), rgba(122, 37, 201, 0.17), rgba(74, 0, 224, 0.15));
        }
    }

    &--secondary {
        background: linear-gradient(120deg, rgba(74, 0, 224, 0.15), rgba(59, 0, 179, 0.12), rgba(44, 0, 134, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(74, 0, 224, 0.2), rgba(59, 0, 179, 0.17), rgba(44, 0, 134, 0.15));
        }
    }

    &--accent {
        background: linear-gradient(120deg, rgba(255, 152, 0, 0.15), rgba(230, 137, 0, 0.12), rgba(204, 122, 0, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(255, 152, 0, 0.2), rgba(230, 137, 0, 0.17), rgba(204, 122, 0, 0.15));
        }
    }

    &--success {
        background: linear-gradient(120deg, rgba(76, 175, 80, 0.15), rgba(67, 160, 71, 0.12), rgba(56, 142, 60, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(76, 175, 80, 0.2), rgba(67, 160, 71, 0.17), rgba(56, 142, 60, 0.15));
        }
    }

    &--warning {
        background: linear-gradient(120deg, rgba(255, 152, 0, 0.15), rgba(230, 137, 0, 0.12), rgba(204, 122, 0, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(255, 152, 0, 0.2), rgba(230, 137, 0, 0.17), rgba(204, 122, 0, 0.15));
        }
    }

    &--error {
        background: linear-gradient(120deg, rgba(244, 67, 54, 0.15), rgba(229, 57, 53, 0.12), rgba(211, 47, 47, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(244, 67, 54, 0.2), rgba(229, 57, 53, 0.17), rgba(211, 47, 47, 0.15));
        }
    }

    &--info {
        background: linear-gradient(120deg, rgba(33, 150, 243, 0.15), rgba(30, 136, 229, 0.12), rgba(25, 118, 210, 0.1));

        &.gradient-card--hoverable:hover {
            background: linear-gradient(120deg, rgba(33, 150, 243, 0.2), rgba(30, 136, 229, 0.17), rgba(25, 118, 210, 0.15));
        }
    }
}
</style>