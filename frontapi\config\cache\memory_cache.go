package cache

import "time"

// MemoryConfig 内存缓存配置
type MemoryConfig struct {
	Name                    string        `mapstructure:"name"`
	Prefix                  string        `mapstructure:"prefix"`
	DefaultTTL              time.Duration `mapstructure:"default_ttl"`
	BufferItems             int64         `mapstructure:"buffer_items"`
	MaxCost                 int64         `mapstructure:"max_cost"`
	NumCounters             int64         `mapstructure:"num_counters"`
	ShardCount              int           `mapstructure:"shard_count"`
	CleanupInterval         time.Duration `mapstructure:"cleanup_interval"`
	EnableBackgroundCleanup bool          `mapstructure:"enable_background_cleanup"`

	// 最大项数
	MaxEntries int `mapstructure:"max_entries"`
	// 缓存大小
	Size int `mapstructure:"size"`
	// 缓存过期时间
	TTL time.Duration `mapstructure:"ttl"`
}
