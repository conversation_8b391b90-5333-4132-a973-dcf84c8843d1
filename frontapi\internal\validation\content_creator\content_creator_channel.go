package content_creator

// CreatorChannelCreateRequest 创作者频道创建请求验证模型
type CreatorChannelCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"required,min=10,max=500"`
	Avatar      string `json:"avatar" validate:"required,url"`
	Banner      string `json:"banner" validate:"required,url"`
	Category    string `json:"category" validate:"required,oneof=video music picture article comic"`
}

// CreatorChannelUpdateRequest 创作者频道更新请求验证模型
type CreatorChannelUpdateRequest struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=50"`
	Description string `json:"description" validate:"omitempty,min=10,max=500"`
	Avatar      string `json:"avatar" validate:"omitempty,url"`
	Banner      string `json:"banner" validate:"omitempty,url"`
	Status      int    `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:正常 1:隐藏 2:封禁
}