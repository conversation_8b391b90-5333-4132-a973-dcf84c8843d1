# 缓存系统架构文档

## 概述

缓存系统是一个高性能、可扩展的Go缓存库，支持多种缓存实现和企业级功能。该系统采用分层架构设计，支持多种缓存适配器，包括Redis、内存、文件、Memcached和BigCache等。

## 架构设计

缓存系统采用分层架构设计，主要包括以下几个层次：

1. **接口层**：定义统一的缓存操作接口
2. **管理层**：实现缓存管理、多级缓存、适配器注册等功能
3. **适配层**：实现各种缓存适配器，如Redis、内存、文件等
4. **增强层**：提供类型安全、分片、集群等增强功能

### 架构图

```
+---------------------------+
|      应用层 (App Layer)    |
+---------------------------+
              |
+---------------------------+
|    缓存管理器 (Manager)     |
+---------------------------+
              |
+--------+--------+--------+
|        |        |        |
v        v        v        v
+----+  +----+  +----+  +----+
|Redis| |File| |Mem.| |Memc.|
+----+  +----+  +----+  +----+
```

## 目录结构

```
frontapi/pkg/cache/
├── v2/                       # v2版本核心实现
│   ├── interfaces.go         # 接口定义
│   ├── types.go              # 类型定义
│   ├── errors.go             # 错误定义
│   ├── manager.go            # 缓存管理器实现
│   ├── redis_adapter.go      # Redis适配器
│   ├── file_adapter.go       # 文件适配器
│   ├── memory_adapter.go     # 内存适配器
│   ├── memcached_adapter.go  # Memcached适配器
│   ├── bigcache_adapter.go   # BigCache适配器
│   ├── typed_adapter.go      # 类型安全适配器
│   ├── cache.go              # 主入口
│   └── README.md             # 文档
├── redis/                    # Redis实现 (v1)
├── file/                     # 文件缓存实现 (v1)
├── memory/                   # 内存缓存实现 (v1)
├── memcached/                # Memcached实现 (v1)
├── bigcache/                 # BigCache实现 (v1)
├── adapters/                 # 适配器增强 (v1)
├── types/                    # 共享类型定义 (v1)
├── sharding/                 # 分片支持 (v1)
├── cluster/                  # 集群支持 (v1)
├── generic/                  # 泛型支持 (v1)
├── factory/                  # 工厂方法 (v1)
├── example/                  # 示例代码
├── cache.go                  # 主入口 (v1)
└── README.md                 # 文档
```

## 核心组件

### 接口定义

#### CacheManager

缓存管理器接口，提供高级缓存操作：

```go
type CacheManager interface {
    // 获取缓存
    Get(key string) (interface{}, error)
    // 设置缓存
    Set(key string, value interface{}, ttl time.Duration) error
    // 删除缓存
    Delete(key string) error
    // 清空缓存
    Clear() error
    // 关闭缓存连接
    Close() error
    // 创建类型安全的缓存适配器
    CreateTypedAdapter(t interface{}) interface{}
    // 获取特定类型的适配器
    GetAdapter(adapterType string) (CacheAdapter, bool)
    // 注册自定义适配器
    RegisterAdapter(name string, adapter CacheAdapter)
}
```

#### CacheAdapter

缓存适配器接口，提供底层缓存操作：

```go
type CacheAdapter interface {
    // 基本操作
    Get(ctx context.Context, key string) ([]byte, error)
    Set(ctx context.Context, key string, value []byte, expiration time.Duration) error
    Delete(ctx context.Context, key string) error
    Exists(ctx context.Context, key string) (bool, error)
    Clear(ctx context.Context) error

    // 连接管理
    Close() error

    // 统计和元信息
    Stats() *CacheStats
    Name() string
    Type() string
}
```

#### 扩展接口

```go
// BatchCacheAdapter 批量操作缓存适配器接口
type BatchCacheAdapter interface {
    CacheAdapter
    MGet(ctx context.Context, keys []string) (map[string][]byte, error)
    MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error
}

// CounterCacheAdapter 计数器缓存适配器接口
type CounterCacheAdapter interface {
    CacheAdapter
    Increment(ctx context.Context, key string, delta int64) (int64, error)
    Decrement(ctx context.Context, key string, delta int64) (int64, error)
}

// ExpirationCacheAdapter 过期时间管理缓存适配器接口
type ExpirationCacheAdapter interface {
    CacheAdapter
    Expire(ctx context.Context, key string, expiration time.Duration) error
    TTL(ctx context.Context, key string) (time.Duration, error)
}

// HealthCheckCacheAdapter 健康检查缓存适配器接口
type HealthCheckCacheAdapter interface {
    CacheAdapter
    Ping(ctx context.Context) error
}
```

### 缓存管理器

缓存管理器是缓存系统的核心组件，负责管理多个缓存适配器，提供统一的缓存操作接口。它支持多级缓存，可以同时使用多种缓存实现，如内存缓存、Redis缓存、文件缓存等。

主要功能：

- 管理多个缓存适配器
- 提供统一的缓存操作接口
- 支持多级缓存读写策略
- 提供类型安全的缓存操作
- 支持自定义适配器注册

### 缓存适配器

缓存适配器是对具体缓存实现的封装，提供统一的操作接口。系统内置了多种适配器实现：

1. **RedisAdapter**：Redis缓存适配器
2. **FileAdapter**：文件缓存适配器
3. **MemoryAdapter**：内存缓存适配器
4. **MemcachedAdapter**：Memcached缓存适配器
5. **BigCacheAdapter**：BigCache缓存适配器

每个适配器都实现了 `CacheAdapter` 接口，并可能实现了其他扩展接口，如 `BatchCacheAdapter`、`CounterCacheAdapter` 等。

### 类型安全适配器

类型安全适配器是一个泛型包装器，提供类型安全的缓存操作。它使用Go的泛型特性，在编译时进行类型检查，避免运行时类型错误。

```go
type TypedAdapter[T any] struct {
    manager *Manager
    ctx     context.Context
}
```

主要方法：

- `Get(key string) (T, error)`：获取指定类型的缓存值
- `Set(key string, value T, ttl time.Duration) error`：设置指定类型的缓存值
- `GetOrSet(key string, setter func() (T, error), ttl time.Duration) (T, error)`：获取缓存，如果不存在则设置

## 版本兼容性

v2版本与v1版本并存，提供了更简洁、更强大的API，同时保持向后兼容。v2版本的主要改进包括：

1. 更简洁的API设计
2. 完整的泛型支持
3. 更好的多级缓存支持
4. 更完善的错误处理
5. 更灵活的配置选项

## 使用场景

1. **高性能Web应用**：使用内存+Redis多级缓存，减少数据库负载
2. **分布式系统**：使用Redis或Memcached缓存，共享数据
3. **离线处理**：使用文件缓存，持久化数据
4. **大容量缓存**：使用BigCache，处理大量数据
5. **类型安全应用**：使用TypedAdapter，确保类型安全

## 扩展性

缓存系统设计为高度可扩展的，可以通过以下方式进行扩展：

1. **自定义适配器**：实现 `CacheAdapter` 接口，创建自定义缓存实现
2. **注册适配器**：使用 `RegisterAdapter` 方法注册自定义适配器
3. **扩展接口**：实现扩展接口，如 `BatchCacheAdapter`、`CounterCacheAdapter` 等
4. **中间件**：使用装饰器模式，为适配器添加中间件功能

## 性能考虑

1. **多级缓存**：热点数据使用内存缓存，冷数据使用Redis或文件缓存
2. **批量操作**：使用 `MGet`、`MSet` 等批量操作方法，减少网络往返
3. **连接池**：Redis和Memcached适配器使用连接池，复用连接
4. **压缩**：可选择对大数据进行压缩，减少存储空间和网络传输
5. **序列化优化**：使用高效的序列化方法，如MessagePack、ProtoBuf等

## 最佳实践

1. **使用类型安全的适配器**：尽可能使用 `GetTyped[T]` 创建类型安全的适配器
2. **合理设置过期时间**：根据数据更新频率设置合适的TTL
3. **使用多级缓存**：热点数据使用内存缓存，其他数据使用Redis或文件缓存
4. **监控缓存统计**：定期检查命中率、未命中率等指标
5. **优雅关闭**：程序退出前调用 `Close()` 方法释放资源 