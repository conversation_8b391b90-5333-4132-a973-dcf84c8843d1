package pictures

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"frontapi/internal/models/pictures"
	"frontapi/internal/repository/base"
)

// UserPictureCollectionRepository 用户图片收藏数据访问接口
type UserPictureCollectionRepository interface {
	base.ExtendedRepository[pictures.UserPictureCollection]
	FindByUserAndPicture(ctx context.Context, userID, pictureID string) (*pictures.UserPictureCollection, error)
	ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*pictures.UserPictureCollection, int64, error)
	ListByUserAndGroup(ctx context.Context, userID, group string, page, pageSize int) ([]*pictures.UserPictureCollection, int64, error)
	ListByPicture(ctx context.Context, pictureID string, page, pageSize int) ([]*pictures.UserPictureCollection, int64, error)
	GetGroups(ctx context.Context, userID string) ([]string, error)
	CountByUser(ctx context.Context, userID string) (int, error)
	CountByPicture(ctx context.Context, pictureID string) (int, error)
}

// userPictureCollectionRepository 用户图片收藏数据访问实现
type userPictureCollectionRepository struct {
	base.ExtendedRepository[pictures.UserPictureCollection]
}

// NewUserPictureCollectionRepository 创建用户图片收藏仓库实例
func NewUserPictureCollectionRepository(db *gorm.DB) UserPictureCollectionRepository {
	return &userPictureCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[pictures.UserPictureCollection](db),
	}
}

// FindByUserAndPicture 根据用户ID和图片ID查找收藏记录
func (r *userPictureCollectionRepository) FindByUserAndPicture(ctx context.Context, userID, pictureID string) (*pictures.UserPictureCollection, error) {
	var collection pictures.UserPictureCollection
	err := r.GetDBWithContext(ctx).
		Where("user_id = ? AND picture_id = ?", userID, pictureID).
		First(&collection).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &collection, nil
}

// ListByUser 根据用户ID获取收藏列表
func (r *userPictureCollectionRepository) ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*pictures.UserPictureCollection, int64, error) {
	var (
		collections []*pictures.UserPictureCollection
		total       int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&pictures.UserPictureCollection{}).
		Where("user_id = ?", userID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&collections).Error
	if err != nil {
		return nil, 0, err
	}

	return collections, total, nil
}

// ListByUserAndGroup 根据用户ID和收藏分组获取收藏列表
func (r *userPictureCollectionRepository) ListByUserAndGroup(ctx context.Context, userID, group string, page, pageSize int) ([]*pictures.UserPictureCollection, int64, error) {
	var (
		collections []*pictures.UserPictureCollection
		total       int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&pictures.UserPictureCollection{}).
		Where("user_id = ? AND collection_group = ?", userID, group).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("user_id = ? AND collection_group = ?", userID, group).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&collections).Error
	if err != nil {
		return nil, 0, err
	}

	return collections, total, nil
}

// ListByPicture 根据图片ID获取收藏列表
func (r *userPictureCollectionRepository) ListByPicture(ctx context.Context, pictureID string, page, pageSize int) ([]*pictures.UserPictureCollection, int64, error) {
	var (
		collections []*pictures.UserPictureCollection
		total       int64
	)

	offset := (page - 1) * pageSize
	err := r.GetDBWithContext(ctx).Model(&pictures.UserPictureCollection{}).
		Where("picture_id = ?", pictureID).
		Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	err = r.GetDBWithContext(ctx).
		Where("picture_id = ?", pictureID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&collections).Error
	if err != nil {
		return nil, 0, err
	}

	return collections, total, nil
}

// GetGroups 获取用户的收藏分组列表
func (r *userPictureCollectionRepository) GetGroups(ctx context.Context, userID string) ([]string, error) {
	var groups []string
	err := r.GetDBWithContext(ctx).Model(&pictures.UserPictureCollection{}).
		Where("user_id = ?", userID).
		Distinct("collection_group").
		Pluck("collection_group", &groups).Error
	return groups, err
}

// CountByUser 统计用户收藏数
func (r *userPictureCollectionRepository) CountByUser(ctx context.Context, userID string) (int, error) {
	var count int64
	err := r.GetDBWithContext(ctx).Model(&pictures.UserPictureCollection{}).
		Where("user_id = ?", userID).
		Count(&count).Error
	return int(count), err
}

// CountByPicture 统计图片被收藏数
func (r *userPictureCollectionRepository) CountByPicture(ctx context.Context, pictureID string) (int, error) {
	var count int64
	err := r.GetDBWithContext(ctx).Model(&pictures.UserPictureCollection{}).
		Where("picture_id = ?", pictureID).
		Count(&count).Error
	return int(count), err
}
