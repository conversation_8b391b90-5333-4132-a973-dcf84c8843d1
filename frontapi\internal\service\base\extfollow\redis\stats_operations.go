package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"frontapi/internal/service/base/extfollow/types"

	goredis "github.com/go-redis/redis/v8"
)

// StatsOperations 统计操作处理器
type StatsOperations struct {
	client   *goredis.Client
	cacheKey *types.CacheKey
	config   *Config
}

// NewStatsOperations 创建统计操作处理器
func NewStatsOperations(client *goredis.Client, cacheKey *types.CacheKey, config *Config) *StatsOperations {
	return &StatsOperations{
		client:   client,
		cacheKey: cacheKey,
		config:   config,
	}
}

// GetUserFollowStats 获取用户关注统计
func (s *StatsOperations) GetUserFollowStats(ctx context.Context, userID string) (*types.UserFollowStats, error) {
	statsKey := s.cacheKey.UserStatsKey(userID)

	// 尝试从缓存获取
	result, err := s.client.Get(ctx, statsKey).Result()
	if err == nil {
		var stats types.UserFollowStats
		if err := json.Unmarshal([]byte(result), &stats); err == nil {
			return &stats, nil
		}
	}

	// 如果缓存中没有，计算统计数据
	stats, err := s.calculateUserStats(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 缓存结果
	if data, err := json.Marshal(stats); err == nil {
		s.client.Set(ctx, statsKey, string(data), s.config.DefaultTTL)
	}

	return stats, nil
}

// calculateUserStats 计算用户统计数据
func (s *StatsOperations) calculateUserStats(ctx context.Context, userID string) (*types.UserFollowStats, error) {
	// 获取关注数和粉丝数
	followingCountKey := s.cacheKey.FollowingCountKey(userID)
	followerCountKey := s.cacheKey.FollowerCountKey(userID)

	pipe := s.client.Pipeline()
	followingCmd := pipe.Get(ctx, followingCountKey)
	followerCmd := pipe.Get(ctx, followerCountKey)

	_, err := pipe.Exec(ctx)
	if err != nil && err != goredis.Nil {
		return nil, fmt.Errorf("获取用户统计数据失败: %w", err)
	}

	var followingCount, followerCount int64

	if val, err := followingCmd.Result(); err == nil {
		fmt.Sscanf(val, "%d", &followingCount)
	}

	if val, err := followerCmd.Result(); err == nil {
		fmt.Sscanf(val, "%d", &followerCount)
	}

	stats := &types.UserFollowStats{
		UserID:         userID,
		FollowerCount:  followerCount,
		FollowingCount: followingCount,
		MutualCount:    0,                            // 需要单独计算
		InfluenceScore: float64(followerCount) * 1.0, // 简单的影响力计算
		LastUpdated:    time.Now(),
	}

	return stats, nil
}

// GetFollowTrends 获取关注趋势
func (s *StatsOperations) GetFollowTrends(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.FollowTrend, error) {
	// 这里简化实现，实际项目中可能需要更复杂的统计逻辑
	trends := []*types.FollowTrend{
		{
			Date:         time.Now().AddDate(0, 0, -1),
			NewFollows:   100,
			NewUnfollows: 10,
			NetFollows:   90,
			ActiveUsers:  50,
		},
	}

	return trends, nil
}
