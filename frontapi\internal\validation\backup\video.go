package validation

// VideoCreateRequest 视频创建请求验证模型
type VideoCreateRequest struct {
	Title       string   `json:"title" validate:"required|minLen:2|maxLen:100"`
	Description string   `json:"description" validate:"required|minLen:10|maxLen:2000"`
	CategoryID  uint     `json:"categoryId" validate:"required|min:1"`
	CoverURL    string   `json:"coverUrl" validate:"required|url"`
	VideoURL    string   `json:"videoUrl" validate:"required|url"`
	Duration    int      `json:"duration" validate:"required|min:1"`
	Tags        []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
	IsPrivate   bool     `json:"isPrivate"`
}

// VideoUpdateRequest 视频更新请求验证模型
type VideoUpdateRequest struct {
	Title       string   `json:"title" validate:"minLen:2|maxLen:100"`
	Description string   `json:"description" validate:"minLen:10|maxLen:2000"`
	CategoryID  uint     `json:"categoryId" validate:"min:1"`
	CoverURL    string   `json:"coverUrl" validate:"url"`
	Tags        []string `json:"tags" validate:"each:minLen:1|each:maxLen:20"`
	IsPrivate   bool     `json:"isPrivate"`
}

// VideoCommentRequest 视频评论请求验证模型
type VideoCommentRequest struct {
	VideoID  uint   `json:"videoId" validate:"required|min:1"`
	Content  string `json:"content" validate:"required|minLen:1|maxLen:500"`
	ParentID uint   `json:"parentId"` // 父评论ID，0表示顶级评论
}

// VideoListRequest 视频列表请求验证模型
type VideoListRequest struct {
	Page     int    `json:"page" validate:"min:1"`
	PageSize int    `json:"pageSize" validate:"min:1|max:100"`
	Category uint   `json:"category" validate:"min:1"`
	Keyword  string `json:"keyword" validate:"maxLen:50"`
	SortBy   string `json:"sortBy" validate:"in:latest,popular,favorite"`
}

// VideoSearchRequest 视频搜索请求验证模型
type VideoSearchRequest struct {
	Keyword  string `json:"keyword" validate:"required|minLen:1|maxLen:50"`
	Page     int    `json:"page" validate:"min:1"`
	PageSize int    `json:"pageSize" validate:"min:1|max:100"`
	SortBy   string `json:"sortBy" validate:"in:latest,popular,relevant"`
}
