/**
 * 通用工具函数
 * 包含防抖、节流、深拷贝、ID生成等常用功能
 */

/**
 * 防抖选项
 */
export interface DebounceOptions {
  leading?: boolean
  trailing?: boolean
  maxWait?: number
}

/**
 * 节流选项
 */
export interface ThrottleOptions {
  leading?: boolean
  trailing?: boolean
}

/**
 * 重试选项
 */
export interface RetryOptions {
  maxAttempts?: number
  delay?: number
  backoff?: 'linear' | 'exponential'
  maxDelay?: number
  shouldRetry?: (error: any, attempt: number) => boolean
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @param options 选项
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: DebounceOptions = {}
): T & { cancel: () => void; flush: () => ReturnType<T> | undefined } {
  const { leading = false, trailing = true, maxWait } = options
  
  let timeoutId: number | null = null
  let maxTimeoutId: number | null = null
  let lastCallTime: number | undefined
  let lastInvokeTime = 0
  let lastArgs: Parameters<T> | undefined
  let lastThis: any
  let result: ReturnType<T> | undefined
  
  function invokeFunc(time: number): ReturnType<T> {
    const args = lastArgs!
    const thisArg = lastThis
    
    lastArgs = undefined
    lastThis = undefined
    lastInvokeTime = time
    result = func.apply(thisArg, args)
    return result as any
  }
  
  function leadingEdge(time: number): ReturnType<T> | undefined {
    lastInvokeTime = time
    timeoutId = setTimeout(timerExpired, wait)
    return leading ? invokeFunc(time) : result
  }
  
  function remainingWait(time: number): number {
    const timeSinceLastCall = time - lastCallTime!
    const timeSinceLastInvoke = time - lastInvokeTime
    const timeWaiting = wait - timeSinceLastCall
    
    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting
  }
  
  function shouldInvoke(time: number): boolean {
    const timeSinceLastCall = time - lastCallTime!
    const timeSinceLastInvoke = time - lastInvokeTime
    
    return (
      lastCallTime === undefined ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    )
  }
  
  function timerExpired(): ReturnType<T> | undefined {
    const time = Date.now()
    if (shouldInvoke(time)) {
      return trailingEdge(time)
    }
    timeoutId = setTimeout(timerExpired, remainingWait(time))
    return result
  }
  
  function trailingEdge(time: number): ReturnType<T> | undefined {
    timeoutId = null
    
    if (trailing && lastArgs) {
      return invokeFunc(time)
    }
    lastArgs = undefined
    lastThis = undefined
    return result
  }
  
  function cancel(): void {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
    if (maxTimeoutId !== null) {
      clearTimeout(maxTimeoutId)
      maxTimeoutId = null
    }
    lastInvokeTime = 0
    lastArgs = undefined
    lastCallTime = undefined
    lastThis = undefined
  }
  
  function flush(): ReturnType<T> | undefined {
    return timeoutId === null ? result : trailingEdge(Date.now())
  }
  
  function debounced(this: any, ...args: Parameters<T>): ReturnType<T> | undefined {
    const time = Date.now()
    const isInvoking = shouldInvoke(time)
    
    lastArgs = args
    lastThis = this
    lastCallTime = time
    
    if (isInvoking) {
      if (timeoutId === null) {
        return leadingEdge(lastCallTime)
      }
      if (maxWait !== undefined) {
        timeoutId = setTimeout(timerExpired, wait)
        return invokeFunc(lastCallTime)
      }
    }
    if (timeoutId === null) {
      timeoutId = setTimeout(timerExpired, wait)
    }
    return result
  }
  
  debounced.cancel = cancel
  debounced.flush = flush
  
  return debounced as T & { cancel: () => void; flush: () => ReturnType<T> | undefined }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @param options 选项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: ThrottleOptions = {}
): T & { cancel: () => void; flush: () => ReturnType<T> | undefined } {
  const { leading = true, trailing = true } = options
  
  return debounce(func, wait, {
    leading,
    trailing,
    maxWait: wait
  })
}

/**
 * 深拷贝函数
 * @param obj 要拷贝的对象
 * @returns 深拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }
  
  if (obj instanceof RegExp) {
    return new RegExp(obj.source, obj.flags) as T
  }
  
  if (obj instanceof Map) {
    const clonedMap = new Map()
    for (const [key, value] of obj) {
      clonedMap.set(deepClone(key), deepClone(value))
    }
    return clonedMap as T
  }
  
  if (obj instanceof Set) {
    const clonedSet = new Set()
    for (const value of obj) {
      clonedSet.add(deepClone(value))
    }
    return clonedSet as T
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        ;(clonedObj as any)[key] = deepClone((obj as any)[key])
      }
    }
    return clonedObj
  }
  
  return obj
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID字符串
 */
export function generateId(prefix = 'id'): string {
  const timestamp = Date.now().toString(36)
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `${prefix}_${timestamp}_${randomStr}`
}

/**
 * 生成UUID
 * @returns UUID字符串
 */
export function generateUUID(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }
  
  // 降级实现
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 睡眠函数
 * @param ms 毫秒数
 * @returns Promise
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 重试函数
 * @param fn 要重试的函数
 * @param options 重试选项
 * @returns Promise
 */
export async function retry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxAttempts = 3,
    delay = 1000,
    backoff = 'linear',
    maxDelay = 30000,
    shouldRetry = () => true
  } = options
  
  let lastError: any
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error
      
      if (attempt === maxAttempts || !shouldRetry(error, attempt)) {
        throw error
      }
      
      let waitTime = delay
      if (backoff === 'exponential') {
        waitTime = Math.min(delay * Math.pow(2, attempt - 1), maxDelay)
      }
      
      await sleep(waitTime)
    }
  }
  
  throw lastError
}

/**
 * 获取数据类型
 * @param value 任意值
 * @returns 类型字符串
 */
export function getType(value: any): string {
  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase()
}

/**
 * 检查值是否为空
 * @param value 任意值
 * @returns 是否为空
 */
export function isEmpty(value: any): boolean {
  if (value == null) {
    return true
  }
  
  if (typeof value === 'string' || Array.isArray(value)) {
    return value.length === 0
  }
  
  if (value instanceof Map || value instanceof Set) {
    return value.size === 0
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0
  }
  
  return false
}

/**
 * 扁平化数组
 * @param arr 多维数组
 * @param depth 扁平化深度
 * @returns 扁平化后的数组
 */
export function flatten<T>(arr: any[], depth = Infinity): T[] {
  const result: T[] = []
  
  function flattenHelper(items: any[], currentDepth: number): void {
    for (const item of items) {
      if (Array.isArray(item) && currentDepth > 0) {
        flattenHelper(item, currentDepth - 1)
      } else {
        result.push(item)
      }
    }
  }
  
  flattenHelper(arr, depth)
  return result
}

/**
 * 数组去重
 * @param arr 数组
 * @param key 对象数组的去重键
 * @returns 去重后的数组
 */
export function unique<T>(arr: T[], key?: keyof T): T[] {
  if (!key) {
    return [...new Set(arr)]
  }
  
  const seen = new Set()
  return arr.filter(item => {
    const value = item[key]
    if (seen.has(value)) {
      return false
    }
    seen.add(value)
    return true
  })
}

/**
 * 检查是否为移动设备
 * @returns 是否为移动设备
 */
export function isMobile(): boolean {
  if (typeof navigator === 'undefined') {
    return false
  }
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

/**
 * 解析URL参数
 * @param url URL字符串
 * @returns 参数对象
 */
export function parseUrlParams(url?: string): Record<string, string> {
  const urlStr = url || (typeof window !== 'undefined' ? window.location.search : '')
  const params = new URLSearchParams(urlStr)
  const result: Record<string, string> = {}
  
  for (const [key, value] of params) {
    result[key] = value
  }
  
  return result
}

/**
 * 构建URL参数
 * @param params 参数对象
 * @returns URL参数字符串
 */
export function buildUrlParams(params: Record<string, any>): string {
  const searchParams = new URLSearchParams()
  
  for (const [key, value] of Object.entries(params)) {
    if (value != null) {
      searchParams.append(key, String(value))
    }
  }
  
  return searchParams.toString()
}

/**
 * 空函数
 */
export function noop(): void {}

/**
 * 恒等函数
 * @param value 任意值
 * @returns 原值
 */
export function identity<T>(value: T): T {
  return value
}

/**
 * 常量函数
 * @param value 常量值
 * @returns 返回常量值的函数
 */
export function constant<T>(value: T): () => T {
  return () => value
}

/**
 * 记忆化函数
 * @param fn 要记忆化的函数
 * @param resolver 键解析器
 * @returns 记忆化后的函数
 */
export function memoize<TFunc extends (...args: any[]) => any>(
  fn: TFunc,
  resolver?: (...args: Parameters<TFunc>) => string
): TFunc & { cache: Map<string, ReturnType<TFunc>>; clear: () => void } {
  const cache = new Map<string, ReturnType<TFunc>>()
  
  function memoized(this: any, ...args: Parameters<TFunc>): ReturnType<TFunc> {
    const key = resolver ? resolver(...args) : JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)!
    }
    
    const result = fn.apply(this, args)
    cache.set(key, result)
    return result
  }
  
  memoized.cache = cache
  memoized.clear = () => cache.clear()
  
  return memoized as TFunc & { cache: Map<string, ReturnType<TFunc>>; clear: () => void }
}