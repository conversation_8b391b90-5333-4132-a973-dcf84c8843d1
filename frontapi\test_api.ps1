$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMSIsInVzZXJuYW1lIjoiYWRtaW4iLCJleHAiOjE3NTE4MjIzNDAsIm5iZiI6MTc1MTIxNzU0MCwiaWF0IjoxNzUxMjE3NTQwfQ.f7jZ5f2TFeniNx3OKAwH_VbFNpYjzr4IkiUX51-a3-c"
}

$body = @{
    data = @{
        id = "d4FebF8c-385e-356b-76Be-c2d8BDA35cF6"
        status = 1
    }
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/proadm/users/update-status" -Method POST -Headers $headers -Body $body
    Write-Host "Success: $($response | ConvertTo-Json -Depth 3)"
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
} 