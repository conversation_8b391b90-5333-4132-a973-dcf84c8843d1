<template>
  <el-dialog
    :model-value="visible"
    :title="form.id ? '编辑标签' : '新增标签'"
    width="600px"
    @close="$emit('update:visible', false)"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="标签名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入标签名称"></el-input>
      </el-form-item>

      <el-form-item label="标签类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择标签类型">
          <el-option :label="'视频标签'" :value="1"></el-option>
          <el-option :label="'短视频标签'" :value="2"></el-option>
          <el-option :label="'帖子标签'" :value="3"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="排序" prop="sort_order">
        <el-input-number v-model="form.sort_order" :min="0" :max="9999"></el-input-number>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import type { Tag, CreateTagRequest, UpdateTagRequest } from "@/types/tags";

const props = defineProps<{
  visible: boolean;
  tag?: Tag | null;
}>();

const emit = defineEmits(["update:visible", "success"]);
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
const form = reactive<CreateTagRequest & { id?: string }>({
  id: '',
  name: '',
  type: 1,
  sort_order: 0
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择标签类型', trigger: 'change' }
  ]
});

// 监听tag变化，更新表单数据
watch(() => props.tag, val => {
  if (val) {
    Object.assign(form, val);
  } else {
    resetForm();
  }
}, { immediate: true });

// 监听visible变化
watch(() => props.visible, val => {
  if (!val) {
    resetForm();
  }
});

// 重置表单
function resetForm() {
  Object.assign(form, {
    id: '',
    name: '',
    type: 1,
    sort_order: 0
  });
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

// 提交表单
function submitForm() {
  if (!formRef.value) return;

  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;

      // 构建提交数据
      // console.log("提交数据",form,form.id&&form.id.length>0);
      const submitData = form.id&&form.id.length>0
        ? { id: form.id, ...form } as UpdateTagRequest
        : { ...form } as CreateTagRequest;

      emit("success", submitData);
      loading.value = false;
    }
  });
}
</script>
