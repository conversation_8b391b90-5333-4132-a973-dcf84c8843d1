package validation

// bookCategory
type CreateCategoryRequest struct {
	Name        string `json:"name" validate:"required"`
	Code        string `json:"code" validate:"required|minLen:3|maxLen:50"`
	Description string `json:"description"`
	SortOrder   int    `json:"sort_order"`
	Status      int    `json:"status"`
}

// bookCategory
type UpdateCategoryRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	SortOrder   *int   `json:"sort_order"`
	Status      *int   `json:"status"`
}

// bookCategory
type UpdateCategoryStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status" validate:"required|min:-1|max:3"`
}

// BookCreateRequest 书籍创建请求验证模型
type BookCreateRequest struct {
	Title       string   `json:"title" validate:"required,min=2,max=100"`
	Description string   `json:"description" validate:"required,min=10,max=2000"`
	Cover       string   `json:"cover" validate:"required,url"`
	Author      string   `json:"author" validate:"required"`
	CategoryID  uint     `json:"categoryId" validate:"required,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
	ISBN        string   `json:"isbn" validate:"omitempty"`
	Publisher   string   `json:"publisher" validate:"omitempty"`
	PublishDate string   `json:"publishDate" validate:"omitempty,datetime=2006-01-02"`
}

// BookUpdateRequest 书籍更新请求验证模型
type BookUpdateRequest struct {
	Title       string   `json:"title" validate:"omitempty,min=2,max=100"`
	Description string   `json:"description" validate:"omitempty,min=10,max=2000"`
	Cover       string   `json:"cover" validate:"omitempty,url"`
	Author      string   `json:"author" validate:"omitempty"`
	CategoryID  uint     `json:"categoryId" validate:"omitempty,min=1"`
	Tags        []string `json:"tags" validate:"omitempty,dive,min=1,max=20"`
	Status      int      `json:"status" validate:"omitempty,oneof=0 1 2"` // 0:连载中 1:已完结 2:暂停更新
	IsVIP       bool     `json:"isVip"`
	IsAdult     bool     `json:"isAdult"`
	ISBN        string   `json:"isbn" validate:"omitempty"`
	Publisher   string   `json:"publisher" validate:"omitempty"`
	PublishDate string   `json:"publishDate" validate:"omitempty,datetime=2006-01-02"`
}

// BookChapterCreateRequest 书籍章节创建请求验证模型
type BookChapterCreateRequest struct {
	BookID    uint    `json:"bookId" validate:"required,min=1"`
	Title     string  `json:"title" validate:"required,min=2,max=100"`
	ChapterNo float64 `json:"chapterNo" validate:"required"` // 支持小数，如 1.5 表示番外
	Content   string  `json:"content" validate:"required,min=10"`
	WordCount int     `json:"wordCount" validate:"omitempty,min=0"`
	IsVIP     bool    `json:"isVip"`
	Price     float64 `json:"price" validate:"omitempty,min=0"`
}

// BookChapterUpdateRequest 书籍章节更新请求验证模型
type BookChapterUpdateRequest struct {
	Title     string  `json:"title" validate:"omitempty,min=2,max=100"`
	ChapterNo float64 `json:"chapterNo" validate:"omitempty"`
	Content   string  `json:"content" validate:"omitempty,min=10"`
	WordCount int     `json:"wordCount" validate:"omitempty,min=0"`
	IsVIP     bool    `json:"isVip"`
	Price     float64 `json:"price" validate:"omitempty,min=0"`
}

// BookListRequest 书籍列表请求验证模型
type BookListRequest struct {
	Page       int      `json:"page" validate:"min=1"`
	PageSize   int      `json:"pageSize" validate:"min=1,max=100"`
	CategoryID uint     `json:"categoryId" validate:"omitempty,min=1"`
	Status     int      `json:"status" validate:"omitempty,oneof=0 1 2"`
	Tags       []string `json:"tags" validate:"omitempty"`
	SortBy     string   `json:"sortBy" validate:"omitempty,oneof=latest popular favorite update"`
	IsVIP      bool     `json:"isVip" validate:"omitempty"`
	IsAdult    bool     `json:"isAdult" validate:"omitempty"`
}

// BookCommentRequest 书籍评论请求验证模型
type BookCommentRequest struct {
	BookID      uint   `json:"bookId" validate:"required,min=1"`
	ChapterID   uint   `json:"chapterId" validate:"omitempty,min=1"`
	Content     string `json:"content" validate:"required,min=1,max=500"`
	ParentID    uint   `json:"parentId"`    // 父评论ID，0表示顶级评论
	SpoilerFlag bool   `json:"spoilerFlag"` // 是否包含剧透
}

// 请求和响应结构体
type CreateChapterRequest struct {
	BookID        string  `json:"book_id" validate:"required"`
	BookName      string  `json:"book_name" validate:"required"`
	Title         string  `json:"title" validate:"required"`
	ChapterNumber int     `json:"chapter_number"`
	Content       string  `json:"content" validate:"required"`
	IsLocked      uint8   `json:"is_locked"`
	Price         float64 `json:"price"`
}

type UpdateChapterRequest struct {
	Title    string  `json:"title"`
	BookName string  `json:"book_name"`
	Content  string  `json:"content"`
	IsLocked *uint8  `json:"is_locked"`
	Price    float64 `json:"price"`
	Status   *uint8  `json:"status"`
}
type BatchUploadChaptersRequest struct {
	BookID      string                  `json:"book_id" validate:"required"`
	BookName    string                  `json:"book_name" validate:"required"`
	Chapters    []*CreateChapterRequest `json:"chapters" validate:"required"`
	IsOverwrite bool                    `json:"isOverwrite"`
}
type ReorderChapterRequest struct {
	BookID        string `json:"bookId" validate:"required"`
	OldChapterID  string `json:"oldChapterId" validate:"required"`
	NewChapterID  string `json:"newChapterId" validate:"required"`
	NewChapterNum int    `json:"newChapterNum" validate:"required"`
}

// 添加新的章节排序项结构体
type ChapterOrderItem struct {
	ID            string `json:"id"`
	ChapterNumber int    `json:"chapter_number"`
}

// 添加批量更新章节排序请求结构体
type BatchUpdateChapterOrderRequest struct {
	BookID   string             `json:"book_id" validate:"required"`
	Chapters []ChapterOrderItem `json:"chapters" validate:"required"`
}
