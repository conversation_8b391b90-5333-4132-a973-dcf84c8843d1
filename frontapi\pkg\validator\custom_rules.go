package validator

import (
	"github.com/gookit/validate"
	"regexp"
)

// RegisterCustomValidations 注册所有自定义验证规则
func RegisterCustomValidations(v *Validator) error {

	// 用户名验证（字母、数字、下划线，3-20个字符）
	validate.AddValidator("username", func(val interface{}) bool {
		if str, ok := val.(string); ok {
			match, _ := regexp.MatchString("^[a-zA-Z0-9_]{3,20}$", str)
			return match
		}
		return false
	})

	// 强密码验证（至少8个字符，包含大小写字母、数字和特殊字符）
	validate.AddValidator("strongpassword", func(val interface{}) bool {
		if str, ok := val.(string); ok {
			return validateStrongPasswordRule(str)
		}
		return false
	})

	// 手机号验证（中国大陆手机号格式）
	validate.AddValidator("mobile", func(val interface{}) bool {
		if str, ok := val.(string); ok {
			match, _ := regexp.MatchString("^1[3-9]\\d{9}$", str)
			return match
		}
		return false
	})

	// 日期格式验证
	validate.AddValidator("dateformat", func(val interface{}) bool {
		if str, ok := val.(string); ok {
			match, _ := regexp.MatchString("^\\d{4}-\\d{2}-\\d{2}$", str)
			return match
		}
		return false
	})

	// 身份证号验证
	validate.AddValidator("idcard", func(val interface{}) bool {
		if str, ok := val.(string); ok {
			match, _ := regexp.MatchString("^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9X]$", str)
			return match
		}
		return false
	})

	return nil
}

// validateStrongPasswordRule 验证强密码（至少8个字符，包含大小写字母、数字和特殊字符）
func validateStrongPasswordRule(password string) bool {
	// 至少8个字符
	if len(password) < 8 {
		return false
	}

	// 检查是否包含大小写字母、数字和特殊字符
	hasUpperCase := regexp.MustCompile(`[A-Z]`).MatchString(password)
	hasLowerCase := regexp.MustCompile(`[a-z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`[0-9]`).MatchString(password)
	hasSpecial := regexp.MustCompile(`[!@#$%^&*(),.?":{}|<>]`).MatchString(password)

	// 简化版：至少满足3种类型
	typeCount := 0
	if hasUpperCase {
		typeCount++
	}
	if hasLowerCase {
		typeCount++
	}
	if hasNumber {
		typeCount++
	}
	if hasSpecial {
		typeCount++
	}

	return typeCount >= 3
}
