<template>
  <el-dialog
    :model-value="visible"
    :title="type === 'edit' ? '编辑频道' : '添加频道'"
    width="50%"
    max-width="600px"
    @update:model-value="(val) => emit('update:visible', val)"
    @closed="onClosed"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 频道名称 -->
      <el-form-item label="频道名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入频道名称" />
      </el-form-item>

      <!-- 频道编码 -->
      <el-form-item label="频道编码" prop="code">
        <el-input 
          v-model="form.code" 
          placeholder="请输入频道编码"
          :readonly="type === 'edit'"
        >
          <template #append v-if="type === 'add'">
            <el-button @click="generateCode" :icon="RefreshRight" type="primary">
              随机生成
            </el-button>
          </template>
        </el-input>
        <div v-if="type === 'edit'" class="form-tip">
          编辑模式下频道编码不可修改
        </div>
      </el-form-item>

      <!-- 频道描述 -->
      <el-form-item label="频道描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入频道描述"
        />
      </el-form-item>

      <!-- 频道图标 -->
      <el-form-item label="频道图标" prop="icon">
        <UrlOrFileInput
          v-model="form.icon"
          :accept="'.jpg,.jpeg,.png,.gif,.webp'"
          :max-size="2"
          :upload-dir="'icons'"
          placeholder="请选择或上传频道图标"
          show-preview
        />
      </el-form-item>

      <!-- 频道封面 -->
      <el-form-item label="频道封面" prop="cover">
        <UrlOrFileInput
          v-model="form.cover"
          :accept="'.jpg,.jpeg,.png,.gif,.webp'"
          :max-size="5"
          :upload-dir="'covers'"
          placeholder="请选择或上传频道封面"
          show-preview
        />
      </el-form-item>

      <!-- 频道横幅 -->
      <el-form-item label="频道横幅" prop="banner">
        <UrlOrFileInput
          v-model="form.banner"
          :accept="'.jpg,.jpeg,.png,.gif,.webp'"
          :max-size="5"
          :upload-dir="'banners'"
          placeholder="请选择或上传频道横幅"
          show-preview
        />
      </el-form-item>

      <!-- 主题颜色 -->
      <el-form-item label="主题颜色" prop="color">
        <el-color-picker v-model="form.color" show-alpha />
      </el-form-item>

      <!-- 更新频率 -->
      <el-form-item label="更新频率" prop="update_frequency">
        <el-select v-model="form.update_frequency" placeholder="请选择更新频率" style="width: 100%;">
          <el-option label="每日" value="daily" />
          <el-option label="每周" value="weekly" />
          <el-option label="每月" value="monthly" />
          <el-option label="不定期" value="irregular" />
        </el-select>
      </el-form-item>

      <!-- 频道URI -->
      <el-form-item label="频道URI" prop="uri">
        <el-input v-model="form.uri" placeholder="频道访问地址" />
      </el-form-item>

      <!-- 排序权重 -->
      <el-form-item label="排序权重" prop="sort_order">
        <el-input-number 
          v-model="form.sort_order" 
          :min="0" 
          :max="9999"
          placeholder="排序权重"
          style="width: 100%;"
        />
      </el-form-item>

      <!-- 状态 -->
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 是否推荐 -->
      <el-form-item label="是否推荐" prop="is_featured">
        <el-switch
          v-model="form.is_featured"
          :active-value="1"
          :inactive-value="0"
          active-text="推荐"
          inactive-text="普通"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="emit('update:visible', false)">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ type === 'edit' ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import type { VideoChannelItem } from '@/types/videos';
import { RefreshRight } from '@element-plus/icons-vue';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { nextTick, reactive, ref, watch } from 'vue';

// Props
interface Props {
  visible: boolean;
  type: 'add' | 'edit';
  channelData?: VideoChannelItem | null;
}

const props = withDefaults(defineProps<Props>(), {
  channelData: null
});

// Emits
interface Emits {
  'update:visible': [value: boolean];
  submit: [data: any, type: 'add' | 'edit'];
}

const emit = defineEmits<Emits>();

// Form相关
const formRef = ref<FormInstance>();
const submitting = ref(false);

// 表单数据
const form = reactive({
  id: '',
  name: '',
  code: '',
  description: '',
  icon: '',
  cover: '',
  banner: '',
  color: '',
  update_frequency: '',
  uri: '',
  sort_order: 0,
  status: 1,
  is_featured: 0,
});

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入频道名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入频道编码', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '编码必须以字母开头，只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述不能超过500个字符', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, max: 9999, message: '排序权重必须在0-9999之间', trigger: 'blur' }
  ]
};

// 生成频道编码
const generateCode = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  form.code = `ch_${timestamp}_${random}`;
};

// 监听props变化，更新表单数据
watch(() => props.channelData, (newData) => {
  if (newData && props.type === 'edit') {
    // 编辑模式，填充数据
    Object.assign(form, {
      id: newData.id,
      name: newData.name,
      code: newData.code || '',
      description: newData.description || '',
      icon: newData.icon || '',
      cover: newData.cover || '',
      banner: newData.banner || '',
      color: newData.color || '',
      update_frequency: newData.update_frequency || '',
      uri: newData.uri || '',
      sort_order: Number(newData.sort_order) || 0,
      status: Number(newData.status),
      is_featured: Number(newData.is_featured),
    });
  }
}, { immediate: true });

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible && props.type === 'add') {
    // 添加模式，重置表单
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    name: '',
    code: '',
    description: '',
    icon: '',
    cover: '',
    banner: '',
    color: '',
    update_frequency: '',
    uri: '',
    sort_order: 0,
    status: 1,
    is_featured: 0,
  });
  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    // 准备提交数据
    const submitData = { ...form };
    
    // 数据类型转换和处理
    submitData.sort_order = Number(submitData.sort_order) || 0;
    submitData.status = Number(submitData.status);
    submitData.is_featured = Number(submitData.is_featured);

    emit('submit', submitData, props.type);
    
  } catch (error) {
    console.error('表单验证失败:', error);
    ElMessage.error('请检查表单填写是否正确');
  } finally {
    submitting.value = false;
  }
};

// 关闭弹窗
const onClosed = () => {
  resetForm();
};
</script>

<style scoped lang="scss">
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.2;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;

  .el-button {
    margin-left: 8px;
  }
}

// 表单项样式
:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }
  
  .el-form-item__content {
    .el-input,
    .el-select,
    .el-textarea {
      width: 100%;
    }
  }
}

:deep(.el-color-picker) {
  width: 100%;
  
  .el-color-picker__trigger {
    width: 100%;
    min-width: 200px;
    border: 1px solid var(--el-border-color);
    
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-textarea) {
  .el-textarea__inner {
    resize: vertical;
    min-height: 80px;
  }
}

:deep(.el-radio-group) {
  display: flex;
  gap: 16px;
}

:deep(.el-switch) {
  .el-switch__label {
    color: #666;
    font-weight: normal;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    margin: 5vh auto;
    
    .el-dialog__header {
      padding: 15px 20px;
    }
    
    .el-dialog__body {
      padding: 20px;
    }
  }
  
  :deep(.el-form) {
    .el-form-item {
      margin-bottom: 16px;
      
      .el-form-item__label {
        margin-bottom: 5px;
        display: block;
        text-align: left;
      }
    }
  }
  
  .dialog-footer {
    text-align: center;
    
    .el-button {
      margin: 0 5px;
      min-width: 80px;
    }
  }
}

@media (max-width: 480px) {
  :deep(.el-dialog) {
    margin: 0;
    width: 100% !important;
    max-width: none !important;
    border-radius: 0;
    height: 100vh;
    
    .el-dialog__header {
      padding: 12px 16px;
    }
    
    .el-dialog__body {
      padding: 16px;
      height: calc(100vh - 120px);
      overflow-y: auto;
    }
  }
}
</style> 