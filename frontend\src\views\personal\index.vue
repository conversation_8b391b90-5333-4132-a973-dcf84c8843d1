<template>
    <div class="personal-center">
        <div class="container mx-auto p-4">
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 侧边导航 -->
                <div class="w-full md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 rounded-full overflow-hidden">
                            <img src="https://via.placeholder.com/150" alt="用户头像" class="w-full h-full object-cover" />
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium">{{ $t('personal.welcome') }}</h3>
                            <p class="text-gray-600 dark:text-gray-400">用户名称</p>
                        </div>
                    </div>

                    <nav>
                        <router-link to="/user/profile"
                            class="block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 mb-1"
                            active-class="bg-primary-100 text-primary-700 dark:bg-gray-700">
                            {{ $t('personal.profile') }}
                        </router-link>
                        <router-link to="/user/settings"
                            class="block px-4 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 mb-1"
                            active-class="bg-primary-100 text-primary-700 dark:bg-gray-700">
                            {{ $t('personal.settings') }}
                        </router-link>
                    </nav>
                </div>

                <!-- 内容区 -->
                <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-md">
                    <router-view />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';

const route = useRoute();
</script>

<style scoped lang="scss">
.personal-center {
    min-height: calc(100vh - 64px);
    padding: 1.5rem 0;
}
</style>