<template>
  <div class="horizontal-menu">
    <div class="menu-items">
      <div
        v-for="item in menuItems"
        :key="item.label"
        class="menu-item"
        :class="{ 
          'has-submenu': item.items,
          'active': isActiveRoute(item.route)
        }"
      >
        <!-- 主菜单项 -->
        <div
          class="menu-item-content"
          @click="handleMenuItemClick(item, $event)"
          @mouseenter="handleMenuItemHover(item, $event)"
          @mouseleave="handleMenuItemLeave(item)"
        >
          <router-link
            v-if="item.route && !item.items"
            :to="item.route"
            class="menu-link"
            :class="{ 'active': isActiveRoute(item.route) }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span class="menu-label">{{ t(item.label) }}</span>
          </router-link>
          <div
            v-else
            class="menu-link"
            :class="{ 'has-submenu': item.items }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span class="menu-label">{{ t(item.label) }}</span>
            <i v-if="item.items" class="pi pi-angle-down submenu-indicator"></i>
          </div>
        </div>

        <!-- 子菜单 Popover -->
        <Popover
          v-if="item.items"
          :ref="el => setPopoverRef(item.label, el)"
          class="submenu-popover"
          position="bottom"
        >
          <div class="submenu-content">
            <!-- 单列子菜单 -->
            <div v-if="item.items.length <= 6" class="submenu-single-column">
              <div class="submenu-header">
                <span class="submenu-title">{{ t(item.label) }}</span>
              </div>
              <div class="submenu-items">
                <router-link
                  v-for="subItem in item.items"
                  :key="subItem.label"
                  :to="subItem.route!"
                  class="submenu-item-link"
                  :class="{ 'active': isActiveRoute(subItem.route) }"
                  @click="$emit('item-click', subItem)"
                >
                  <span v-if="subItem.icon" class="submenu-icon">
                    <i :class="subItem.icon"></i>
                  </span>
                  <span class="submenu-label">{{ t(subItem.label) }}</span>
                </router-link>
              </div>
            </div>

            <!-- 多列子菜单 (MegaMenu 风格) -->
            <div v-else class="submenu-mega">
              <div class="submenu-header">
                <span class="submenu-title">{{ t(item.label) }}</span>
              </div>
              <div class="submenu-columns">
                <div
                  v-for="(group, groupIndex) in getMenuGroups(item.items)"
                  :key="groupIndex"
                  class="submenu-column"
                >
                  <router-link
                    v-for="subItem in group"
                    :key="subItem.label"
                    :to="subItem.route!"
                    class="submenu-item-link"
                    :class="{ 'active': isActiveRoute(subItem.route) }"
                    @click="$emit('item-click', subItem)"
                  >
                    <span v-if="subItem.icon" class="submenu-icon">
                      <i :class="subItem.icon"></i>
                    </span>
                    <span class="submenu-label">{{ t(subItem.label) }}</span>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </Popover>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import Popover from 'primevue/popover';
import { ref } from 'vue';
import { useRoute } from 'vue-router';

// Props
interface Props {
  megaMenuThreshold?: number; // 超过多少个子菜单项时使用 MegaMenu 风格
}

const props = withDefaults(defineProps<Props>(), {
  megaMenuThreshold: 6
});

// Emits
interface Emits {
  'item-click': [item: any];
}

const emit = defineEmits<Emits>();

// Composables
const { t } = useTranslation();
const route = useRoute();

// State
const hoverTimer = ref<NodeJS.Timeout | null>(null);
const popoverRefs = ref<Record<string, any>>({});

// Methods
const setPopoverRef = (label: string, el: any) => {
  if (el) {
    popoverRefs.value[label] = el;
  }
};

const isActiveRoute = (routePath?: string) => {
  if (!routePath) return false;
  return route.path === routePath || route.path.startsWith(routePath + '/');
};

const getMenuGroups = (items: any[]) => {
  const groupSize = Math.ceil(items.length / 3); // 分成3列
  const groups = [];
  for (let i = 0; i < items.length; i += groupSize) {
    groups.push(items.slice(i, i + groupSize));
  }
  return groups;
};

const handleMenuItemClick = (item: any, event: Event) => {
  if (item.items) {
    // 有子菜单的项目：显示 Popover
    const popover = popoverRefs.value[item.label];
    if (popover) {
      popover.toggle(event);
    }
  } else if (item.route) {
    emit('item-click', item);
  }
};

const handleMenuItemHover = (item: any, event: Event) => {
  if (item.items) {
    // 清除之前的定时器
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }
    
    // 显示 Popover
    const popover = popoverRefs.value[item.label];
    if (popover) {
      popover.show(event);
    }
  }
};

const handleMenuItemLeave = (item: any) => {
  if (item.items) {
    // 延迟隐藏 Popover
    hoverTimer.value = setTimeout(() => {
      const popover = popoverRefs.value[item.label];
      if (popover) {
        popover.hide();
      }
    }, 300);
  }
};
</script>

<style lang="scss" scoped>
.horizontal-menu {
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  
  .menu-items {
    display: flex;
    align-items: center;
    
    .menu-item {
      position: relative;
      
      .menu-item-content {
        .menu-link {
          display: flex;
          align-items: center;
          padding: 1rem 1.5rem;
          color: var(--text-color);
          text-decoration: none;
          transition: all 0.2s ease;
          cursor: pointer;
          white-space: nowrap;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            font-weight: 500;
            
            .menu-icon i {
              color: var(--primary-color);
            }
          }
          
          .menu-icon {
            margin-right: 0.5rem;
            color: var(--text-color-secondary);
            
            i {
              font-size: 1rem;
            }
          }
          
          .menu-label {
            font-weight: 500;
          }
          
          .submenu-indicator {
            margin-left: 0.5rem;
            color: var(--text-color-secondary);
            font-size: 0.7rem;
            transition: transform 0.2s ease;
          }
          
          &.has-submenu:hover .submenu-indicator {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}

// Popover 子菜单样式
:deep(.submenu-popover) {
  .p-popover-content {
    padding: 0;
    
    .submenu-content {
      // 单列子菜单
      .submenu-single-column {
        min-width: 280px;
        
        .submenu-header {
          padding: 0.75rem 1rem 0.5rem;
          border-bottom: 1px solid var(--surface-border);
          margin-bottom: 0.25rem;
          
          .submenu-title {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--text-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
        
        .submenu-items {
          padding: 0.25rem 0;
          
          .submenu-item-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background 0.2s ease;
            
            &:hover {
              background: var(--surface-hover);
            }
            
            &.active {
              background: var(--primary-50);
              color: var(--primary-color);
              
              .submenu-icon i {
                color: var(--primary-color);
              }
            }
            
            .submenu-icon {
              width: 20px;
              margin-right: 0.75rem;
              color: var(--text-color-secondary);
              
              i {
                font-size: 0.9rem;
              }
            }
            
            .submenu-label {
              font-size: 0.9rem;
              font-weight: 500;
            }
          }
        }
      }
      
      // 多列子菜单 (MegaMenu 风格)
      .submenu-mega {
        min-width: 600px;
        
        .submenu-header {
          padding: 1rem 1.5rem 0.75rem;
          border-bottom: 1px solid var(--surface-border);
          margin-bottom: 0.5rem;
          
          .submenu-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
        
        .submenu-columns {
          display: flex;
          padding: 0.5rem 1rem 1rem;
          gap: 2rem;
          
          .submenu-column {
            flex: 1;
            
            .submenu-item-link {
              display: flex;
              align-items: center;
              padding: 0.75rem 1rem;
              color: var(--text-color);
              text-decoration: none;
              transition: background 0.2s ease;
              border-radius: 6px;
              margin-bottom: 0.25rem;
              
              &:hover {
                background: var(--surface-hover);
              }
              
              &.active {
                background: var(--primary-50);
                color: var(--primary-color);
                
                .submenu-icon i {
                  color: var(--primary-color);
                }
              }
              
              .submenu-icon {
                width: 24px;
                margin-right: 0.75rem;
                color: var(--text-color-secondary);
                
                i {
                  font-size: 1rem;
                }
              }
              
              .submenu-label {
                font-size: 0.9rem;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}
</style>
