<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '添加用户' : '编辑用户'"
     @close="$emit('close')"
    width="45vw"
  >
    <el-form
      ref="userFormRef"
      :model="userForm"
      :rules="userRules"
      label-width="100px"
    >
      <el-form-item label="用户名" prop="username">
        <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="dialogType === 'edit'"></el-input>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="userForm.nickname" placeholder="请输入昵称"></el-input>
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <UrlOrFileInput
          v-model="userForm.avatar"
          fileType="image"
          subDir="avatar"
          placeholder="请输入头像图片URL或选择图片"
        />
      </el-form-item>
      <el-form-item v-if="dialogType === 'add'" label="密码" prop="password">
        <el-input v-model="userForm.password" type="password" placeholder="请输入密码"></el-input>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="userForm.gender">
          <el-radio :label="0">未知</el-radio>
          <el-radio :label="1">男</el-radio>
          <el-radio :label="2">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="用户等级" prop="user_level">
        <el-select v-model="userForm.user_level" placeholder="请选择用户等级">
          <el-option label="普通用户" :value="1"></el-option>
          <el-option label="VIP用户" :value="2"></el-option>
          <el-option label="蓝标用户" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="会员等级" prop="member_level">
        <el-select v-model="userForm.member_level" placeholder="请选择会员等级">
          <el-option label="非会员" :value="0"></el-option>
          <el-option label="会员" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="内容创作者" prop="is_content_creator">
        <el-switch
          v-model="userForm.is_content_creator"
          :active-value="1"
          :inactive-value="0"
          active-text="是"
          inactive-text="否"
        ></el-switch>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="userForm.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="个人简介" prop="bio">
        <el-input v-model="userForm.bio" type="textarea" :rows="3" placeholder="请输入个人简介"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { uploadImage } from "@/service/api/upload";
import { addUser, updateUser } from '@/service/api/users/users';
import type { UserItem } from '@/types/users';
import { ElLoading, ElMessage } from 'element-plus';
import { reactive, ref, watch } from 'vue';

const props = defineProps<{
  visible: boolean;
  type: 'add' | 'edit';
  userData?: UserItem | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
  (e: 'close'): void;
}>();

// 弹窗相关
const dialogVisible = ref(props.visible);
const dialogType = ref(props.type);
const userFormRef = ref();
const userForm = reactive({
  id: '',
  username: '',
  nickname: '',
  password: '',
  avatar: '',
  gender: 0,
  email: '',
  phone: '',
  bio: '',
  user_level: 1,
  member_level: 0,
  is_content_creator: 0,
  status: 1,
});

// 表单校验规则
const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' },
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 18, message: '长度在 6 到 18 个字符', trigger: 'blur' },
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  email: [
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
};

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal && props.type === 'edit' && props.userData) {
      Object.assign(userForm, props.userData);
    }
  }
);
watch(()=>props.type,
  (newVal) => {
    if (newVal === 'add') {
      resetUserForm();
      dialogType.value = 'add';
    }else{
      dialogType.value = 'edit';
    }
  }
)
// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (newVal) => {
    emit('update:visible', newVal);
    if (!newVal) {
      resetUserForm();
    }
  }
);

// 重置表单
const resetUserForm = () => {
  userForm.id = '';
  userForm.username = '';
  userForm.nickname = '';
  userForm.password = '';
  userForm.avatar = '';
  userForm.gender = 0;
  userForm.email = '';
  userForm.phone = '';
  userForm.bio = '';
  userForm.user_level = 1;
  userForm.member_level = 0;
  userForm.is_content_creator = 0;
  userForm.status = 1;
  if (userFormRef.value) {
    userFormRef.value.resetFields();
  }
};

// 头像上传成功
const handleAvatarSuccess = (res: any) => {
  if (res.code === 200) {
    userForm.avatar = res.data.url;
  } else {
    ElMessage.error(res.message || '上传失败');
  }
};

// 头像上传前校验
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg';
  const isPNG = file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG && !isPNG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!');
    return false;
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!');
    return false;
  }
  return true;
};
// 上传头像图
function uploadImageHandler(options: any): Promise<XMLHttpRequest> {
  const loading = ElLoading.service({
    lock: true,
    text: '上传中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  const formData = new FormData();
  formData.append('file', options.file);
  formData.append('sub_dir', "avatar");

  return new Promise<XMLHttpRequest>((resolve, reject) => {
    uploadImage(formData)
      .then((res: any) => {
        if (res.code === 2000) {
          // 尝试从多种可能的数据结构中获取URL
          const url = res.data?.url || (typeof res.data === 'string' ? res.data : '');
          userForm.avatar = url;
          ElMessage.success('头像图上传成功');
          resolve(new XMLHttpRequest());
        } else {
          ElMessage.error(res.message || '头像图上传失败');
          reject(new Error(res.message || '头像图上传失败'));
        }
      })
      .catch((error) => {
        console.error('上传出错:', error);
        ElMessage.error('头像图上传失败');
        reject(error);
      })
      .finally(() => {
        loading.close();
      });
  });
}

// 提交表单
const handleSubmit = async () => {
  if (!userFormRef.value) return;

  await userFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const api = dialogType.value === 'add' ? addUser : updateUser;
        const res = await api(userForm) as any;
        if (res.response.data.code === 2000) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功');
          dialogVisible.value = false;
          emit('success');
        } else {
          ElMessage.error(res.response.data.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'));
        }
      } catch (error) {
        console.error(error);
        ElMessage.error(dialogType.value === 'add' ? '添加失败' : '更新失败');
      }
    }
  });
};
</script>

<style scoped lang="scss">
.avatar-uploader {
  display: flex;
  justify-content: center;

  .avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
  }

  .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  border: 1px dashed var(--el-border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
