# 扩展点赞服务架构设计

本文档详细介绍扩展点赞服务 (ExtendedLikeService) 的架构设计、核心组件和设计理念。

## 🎉 实现状态

### ✅ 已完成功能

1. **核心服务架构**
   - ✅ 基础接口定义 (`interfaces.go`)
   - ✅ 配置管理系统 (`config.go`)  
   - ✅ 主服务实现 (`service.go`)
   - ✅ 工厂构建器 (`factory.go`)

2. **Redis v2 适配器**
   - ✅ 完整的Redis适配器实现
   - ✅ 使用高级数据结构（Hash, Set, ZSet, Stream）
   - ✅ 支持所有核心点赞操作
   - ✅ 缓存管理和性能优化

3. **MongoDB 适配器**
   - ✅ 基础适配器框架
   - ⚠️ 具体实现待完善（标记为"待实现"）

4. **设计模式实现**
   - ✅ 策略模式：支持多种存储策略
   - ✅ 适配器模式：统一存储接口
   - ✅ 工厂模式：简化服务创建
   - ✅ 建造者模式：灵活配置

5. **存储策略**
   - ✅ RedisOnly：仅Redis存储
   - ✅ MongoOnly：仅MongoDB存储
   - ✅ RedisFirst：Redis优先，故障转移
   - ✅ MongoFirst：MongoDB优先
   - ✅ DualWrite：双写模式

### 📊 编译状态

```bash
✅ go build ./internal/service/base/extlike/...
# 编译成功，无错误
```

### 📋 使用文档

详细使用指南请参考：[使用指南](./usage.md)

## 目录

- [架构概览](#架构概览)
- [核心组件](#核心组件)
- [设计理念](#设计理念)
- [数据流程](#数据流程)
- [存储策略](#存储策略)
- [性能优化](#性能优化)
- [扩展性设计](#扩展性设计)
- [容错机制](#容错机制)

## 架构概览

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────────┤
│  Controller  │  Service  │  Repository  │  Middleware  │  Utils  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                   扩展点赞服务层 (Extended Like Service)          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌──────────────┐ │
│  │  Service Manager │    │ Extended Service │    │ Sync Service │ │
│  │                 │    │                 │    │              │ │
│  │ • 服务注册      │    │ • 统一接口      │    │ • 数据同步   │ │
│  │ • 服务发现      │    │ • 策略管理      │    │ • 增量同步   │ │
│  │ • 生命周期管理   │    │ • 错误处理      │    │ • 定时任务   │ │
│  └─────────────────┘    └─────────────────┘    └──────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                     存储适配器层 (Storage Adapter Layer)          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌──────────────┐ │
│  │ Redis Adapter   │    │ MongoDB Adapter │    │ Dual Adapter │ │
│  │                 │    │                 │    │              │ │
│  │ • 缓存操作      │    │ • 持久化存储    │    │ • 双写模式   │ │
│  │ • 热门排行      │    │ • 历史记录      │    │ • 一致性保证 │ │
│  │ • 实时统计      │    │ • 复杂查询      │    │ • 故障转移   │ │
│  └─────────────────┘    └─────────────────┘    └──────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      存储层 (Storage Layer)                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐              ┌─────────────────┐           │
│  │      Redis      │              │     MongoDB     │           │
│  │                 │              │                 │           │
│  │ • 高性能缓存    │              │ • 持久化存储    │           │
│  │ • 原子操作      │              │ • 复杂查询      │           │
│  │ • 过期策略      │              │ • 事务支持      │           │
│  │ • 集群支持      │              │ • 副本集       │           │
│  └─────────────────┘              └─────────────────┘           │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 分层架构说明

1. **应用层**: 处理HTTP请求，业务逻辑协调
2. **扩展点赞服务层**: 核心业务逻辑，策略管理，数据同步
3. **存储适配器层**: 统一存储接口，适配不同存储引擎
4. **存储层**: 底层数据存储，Redis缓存和MongoDB持久化

## 核心组件

### 1. ExtendedLikeService (扩展点赞服务)

```go
type ExtendedLikeService interface {
    // 基础操作
    LikeItem(ctx context.Context, userID, itemType, itemID string) error
    UnlikeItem(ctx context.Context, userID, itemType, itemID string) error
    IsLiked(ctx context.Context, userID, itemType, itemID string) (bool, error)
    
    // 查询操作
    GetLikeCount(ctx context.Context, itemType, itemID string) (int64, error)
    GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]string, error)
    GetItemLikedUsers(ctx context.Context, itemType, itemID string, limit, offset int) ([]string, error)
    
    // 批量操作
    BatchLike(ctx context.Context, operations []*LikeOperation) ([]bool, error)
    BatchGetLikeStatus(ctx context.Context, userID, itemType string, itemIDs []string) (map[string]bool, error)
    BatchGetLikeCounts(ctx context.Context, itemType string, itemIDs []string) (map[string]int64, error)
    
    // 热门排行
    GetHotItems(ctx context.Context, itemType string, limit int) ([]*HotItem, error)
    UpdateHotRank(ctx context.Context, itemType, itemID string, likeCount int64) error
    
    // 历史和统计
    GetUserLikeHistory(ctx context.Context, userID, itemType string, limit, offset int) ([]*LikeRecord, error)
    GetItemLikeHistory(ctx context.Context, itemType, itemID string, limit, offset int) ([]*LikeRecord, error)
    GetLikeTrends(ctx context.Context, itemType, itemID string, timeRange TimeRange) ([]*LikeTrend, error)
    
    // 统计分析
    GetUserLikeStats(ctx context.Context, userID string, timeRange TimeRange) (*UserLikeStats, error)
    GetItemLikeStats(ctx context.Context, itemType, itemID string, timeRange TimeRange) (*ItemLikeStats, error)
    GetGlobalLikeStats(ctx context.Context, itemType string, timeRange TimeRange) (*GlobalLikeStats, error)
    
    // 排行榜
    GetTopLikedItems(ctx context.Context, itemType string, timeRange TimeRange, limit int) ([]*TopLikedItem, error)
    GetMostActiveLikers(ctx context.Context, itemType string, timeRange TimeRange, limit int) ([]*ActiveLiker, error)
    
    // 缓存管理
    FlushCache(ctx context.Context, itemType string) error
    GetCacheStats(ctx context.Context, itemType string) (*CacheStats, error)
    
    // 数据同步
    SyncFromRedis(ctx context.Context, itemType string) error
    SyncToRedis(ctx context.Context, itemType string) error
    SyncToMongoDB(ctx context.Context, itemType string) error
    SyncFromMongoDB(ctx context.Context, itemType string) error
    SyncLikeCountsToDatabase(ctx context.Context, itemType string) error
    
    // 策略管理
    GetStrategy() StorageStrategy
    SwitchStrategy(strategy StorageStrategy) error
}
```

**职责**:
- 提供统一的点赞服务接口
- 管理不同的存储策略
- 协调Redis和MongoDB的数据操作
- 处理复杂的业务逻辑

### 2. LikeStorage (存储接口)

```go
type LikeStorage interface {
    // 基础操作
    LikeItem(ctx context.Context, userID, itemType, itemID string) error
    UnlikeItem(ctx context.Context, userID, itemType, itemID string) error
    IsLiked(ctx context.Context, userID, itemType, itemID string) (bool, error)
    GetLikeCount(ctx context.Context, itemType, itemID string) (int64, error)
    
    // 查询操作
    GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]string, error)
    GetItemLikedUsers(ctx context.Context, itemType, itemID string, limit, offset int) ([]string, error)
    
    // 批量操作
    BatchLike(ctx context.Context, operations []*LikeOperation) ([]bool, error)
    BatchGetLikeStatus(ctx context.Context, userID, itemType string, itemIDs []string) (map[string]bool, error)
    BatchGetLikeCounts(ctx context.Context, itemType string, itemIDs []string) (map[string]int64, error)
}
```

**职责**:
- 定义统一的存储操作接口
- 屏蔽底层存储实现细节
- 支持多种存储引擎适配

### 3. Storage Adapters (存储适配器)

#### Redis Storage Adapter
```go
type redisStorageAdapter struct {
    service v2.LikeService
}
```

**特点**:
- 高性能缓存操作
- 支持原子操作
- 实时热门排行
- 自动过期机制

#### MongoDB Storage Adapter
```go
type mongoStorageAdapter struct {
    service mongodb.LikeService
}
```

**特点**:
- 持久化存储
- 复杂查询支持
- 历史记录管理
- 事务一致性

#### Dual Write Storage Adapter
```go
type dualWriteStorageAdapter struct {
    redisService  v2.LikeService
    mongoService  mongodb.LikeService
}
```

**特点**:
- 双写保证数据冗余
- 读操作优先Redis
- 写操作同时写入两个存储
- 故障转移支持

### 4. Sync Service (同步服务)

```go
type SyncService interface {
    SyncLikeCountToTable(ctx context.Context, itemType, itemID string, likeCount int64) error
    BatchSyncLikeCounts(ctx context.Context, itemType string, likeCounts map[string]int64) error
    GetTableLikeCount(ctx context.Context, itemType, itemID string) (int64, error)
    IncrementTableLikeCount(ctx context.Context, itemType, itemID string, delta int64) error
}

type DeltaSyncService interface {
    RecordLikeDelta(ctx context.Context, itemType, itemID string, delta int64) error
    FlushDeltas(ctx context.Context) error
    GetPendingDeltas(ctx context.Context, itemType string) ([]LikeDelta, error)
}
```

**职责**:
- 数据同步到业务表
- 增量同步管理
- 定时任务调度
- 数据一致性保证

### 5. Service Manager (服务管理器)

```go
type LikeServiceManager struct {
    services map[string]ExtendedLikeService
    mu       sync.RWMutex
}
```

**职责**:
- 服务实例管理
- 服务注册和发现
- 生命周期管理
- 统一操作接口

## 设计理念

### 1. 分层架构

**优势**:
- **职责分离**: 每层专注于特定功能
- **松耦合**: 层间通过接口交互
- **可测试性**: 每层可独立测试
- **可维护性**: 修改影响范围有限

### 2. 适配器模式

**应用场景**:
- 统一不同存储引擎的接口
- 支持存储引擎的热插拔
- 简化上层业务逻辑

**实现方式**:
```go
// 统一接口
type LikeStorage interface {
    LikeItem(ctx context.Context, userID, itemType, itemID string) error
    // 其他方法...
}

// Redis适配器
type redisStorageAdapter struct {
    service v2.LikeService
}

func (r *redisStorageAdapter) LikeItem(ctx context.Context, userID, itemType, itemID string) error {
    return r.service.LikeItem(ctx, userID, itemType, itemID)
}

// MongoDB适配器
type mongoStorageAdapter struct {
    service mongodb.LikeService
}

func (m *mongoStorageAdapter) LikeItem(ctx context.Context, userID, itemType, itemID string) error {
    return m.service.LikeItem(ctx, userID, itemType, itemID)
}
```

### 3. 策略模式

**存储策略**:
- **MongoOnly**: 仅使用MongoDB
- **RedisFirst**: Redis优先，MongoDB备份
- **MongoFirst**: MongoDB优先，Redis缓存
- **DualWrite**: 双写模式，保证数据冗余

**策略切换**:
```go
func (s *extendedLikeService) SwitchStrategy(strategy StorageStrategy) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    // 重新初始化存储适配器
    return s.initializeStorageStrategy(s.redisService, s.mongoService)
}
```

### 4. 观察者模式

**事件驱动**:
- 点赞事件触发缓存更新
- 数据变更触发同步任务
- 性能指标触发告警

### 5. 工厂模式

**服务创建**:
```go
func NewExtendedLikeService(config *LikeServiceConfig, db *gorm.DB) (ExtendedLikeService, error) {
    // 创建Redis服务
    redisService := v2.NewLikeService(config.RedisConfig)
    
    // 创建MongoDB服务
    mongoService := mongodb.NewLikeService(config.MongoConfig)
    
    // 创建同步服务
    syncService := NewSyncService(db, nil)
    
    // 创建扩展服务
    service := &extendedLikeService{
        config:      config,
        strategy:    config.Strategy,
        syncService: syncService,
    }
    
    // 初始化存储策略
    if err := service.initializeStorageStrategy(redisService, mongoService); err != nil {
        return nil, err
    }
    
    return service, nil
}
```

## 数据流程

### 1. 点赞操作流程

```
用户点赞请求
      │
      ▼
┌─────────────┐
│ Controller  │ ──── 参数验证、权限检查
└─────────────┘
      │
      ▼
┌─────────────┐
│ Service     │ ──── 业务逻辑处理
└─────────────┘
      │
      ▼
┌─────────────┐
│ Extended    │ ──── 策略选择、存储操作
│ Like Service│
└─────────────┘
      │
      ▼
┌─────────────┐    ┌─────────────┐
│ Redis       │    │ MongoDB     │
│ Adapter     │    │ Adapter     │
└─────────────┘    └─────────────┘
      │                    │
      ▼                    ▼
┌─────────────┐    ┌─────────────┐
│ Redis       │    │ MongoDB     │
│ Storage     │    │ Storage     │
└─────────────┘    └─────────────┘
      │                    │
      └────────┬───────────┘
               │
               ▼
      ┌─────────────┐
      │ Sync        │ ──── 异步同步到业务表
      │ Service     │
      └─────────────┘
```

### 2. 查询操作流程

```
查询请求
    │
    ▼
┌─────────────┐
│ 缓存检查    │ ──── Redis优先
└─────────────┘
    │
    ▼
┌─────────────┐
│ 缓存命中?   │
└─────────────┘
    │         │
   是│        │否
    │         ▼
    │   ┌─────────────┐
    │   │ 数据库查询  │ ──── MongoDB查询
    │   └─────────────┘
    │         │
    │         ▼
    │   ┌─────────────┐
    │   │ 更新缓存    │ ──── 回写Redis
    │   └─────────────┘
    │         │
    └─────────┼─────────
              │
              ▼
        ┌─────────────┐
        │ 返回结果    │
        └─────────────┘
```

### 3. 数据同步流程

```
定时任务触发
      │
      ▼
┌─────────────┐
│ 获取增量    │ ──── 从Redis获取变更数据
│ 数据        │
└─────────────┘
      │
      ▼
┌─────────────┐
│ 批量处理    │ ──── 批量更新业务表
└─────────────┘
      │
      ▼
┌─────────────┐
│ 清理增量    │ ──── 清空已处理的增量数据
│ 数据        │
└─────────────┘
      │
      ▼
┌─────────────┐
│ 记录日志    │ ──── 记录同步结果
└─────────────┘
```

## 存储策略

### 1. MongoOnly 策略

**适用场景**:
- 数据一致性要求极高
- 并发量不大
- 成本敏感

**数据流**:
```
请求 → MongoDB → 响应
```

**优缺点**:
- ✅ 数据一致性好
- ✅ 实现简单
- ✅ 成本低
- ❌ 性能较低
- ❌ 扩展性有限

### 2. RedisFirst 策略

**适用场景**:
- 高并发读写
- 对性能要求高
- 可接受少量数据丢失

**数据流**:
```
写入: 请求 → Redis → 异步同步 → MongoDB
读取: 请求 → Redis → 响应
```

**优缺点**:
- ✅ 性能最高
- ✅ 响应速度快
- ❌ 数据可能丢失
- ❌ 一致性较弱

### 3. MongoFirst 策略

**适用场景**:
- 平衡性能和一致性
- 中等并发量
- 数据重要性较高

**数据流**:
```
写入: 请求 → MongoDB → 更新缓存 → Redis
读取: 请求 → Redis(缓存) → MongoDB(缓存未命中) → 响应
```

**优缺点**:
- ✅ 数据安全性好
- ✅ 性能适中
- ✅ 一致性较好
- ❌ 复杂度较高

### 4. DualWrite 策略

**适用场景**:
- 高可用性要求
- 零数据丢失
- 资源充足

**数据流**:
```
写入: 请求 → Redis + MongoDB (同时写入)
读取: 请求 → Redis → MongoDB(Redis故障) → 响应
```

**优缺点**:
- ✅ 高可用性
- ✅ 数据冗余
- ✅ 故障转移
- ❌ 资源消耗大
- ❌ 复杂度最高

## 性能优化

### 1. 缓存优化

**多级缓存**:
```
L1 Cache (本地) → L2 Cache (Redis) → L3 Storage (MongoDB)
```

**缓存策略**:
- **热点数据**: 长期缓存
- **冷数据**: 短期缓存或不缓存
- **批量预加载**: 预测性加载

### 2. 批量操作

**批量写入**:
```go
func (s *extendedLikeService) BatchLike(ctx context.Context, operations []*LikeOperation) ([]bool, error) {
    // 按存储类型分组
    redisOps, mongoOps := s.groupOperations(operations)
    
    // 并行执行
    var wg sync.WaitGroup
    var redisResults, mongoResults []bool
    var redisErr, mongoErr error
    
    wg.Add(2)
    go func() {
        defer wg.Done()
        redisResults, redisErr = s.primaryStorage.BatchLike(ctx, redisOps)
    }()
    
    go func() {
        defer wg.Done()
        mongoResults, mongoErr = s.secondaryStorage.BatchLike(ctx, mongoOps)
    }()
    
    wg.Wait()
    
    // 合并结果
    return s.mergeResults(redisResults, mongoResults), s.combineErrors(redisErr, mongoErr)
}
```

### 3. 连接池优化

**Redis连接池**:
```go
redisClient := redis.NewClient(&redis.Options{
    PoolSize:     20,        // 连接池大小
    MinIdleConns: 10,        // 最小空闲连接
    MaxRetries:   3,         // 最大重试次数
    DialTimeout:  5 * time.Second,
    ReadTimeout:  3 * time.Second,
    WriteTimeout: 3 * time.Second,
})
```

**MongoDB连接池**:
```go
clientOptions := options.Client().ApplyURI(uri)
clientOptions.SetMaxPoolSize(100)
clientOptions.SetMinPoolSize(10)
clientOptions.SetMaxConnIdleTime(30 * time.Minute)
```

### 4. 异步处理

**异步同步**:
```go
func (s *extendedLikeService) asyncSync(ctx context.Context, itemType string) {
    go func() {
        if err := s.SyncLikeCountsToDatabase(ctx, itemType); err != nil {
            log.Printf("异步同步失败: %v", err)
        }
    }()
}
```

## 扩展性设计

### 1. 水平扩展

**Redis集群**:
```go
redisClusterClient := redis.NewClusterClient(&redis.ClusterOptions{
    Addrs: []string{
        "redis-node1:7000",
        "redis-node2:7000",
        "redis-node3:7000",
    },
})
```

**MongoDB分片**:
```go
// 按itemType分片
type ShardRouter struct {
    shards map[string]*mongo.Client
}

func (r *ShardRouter) GetShard(itemType string) *mongo.Client {
    return r.shards[itemType]
}
```

### 2. 垂直扩展

**服务拆分**:
- 点赞服务
- 统计服务
- 排行榜服务
- 同步服务

**微服务架构**:
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Like        │    │ Statistics  │    │ Ranking     │
│ Service     │    │ Service     │    │ Service     │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                  ┌─────────────┐
                  │ Message     │
                  │ Queue       │
                  └─────────────┘
```

### 3. 插件化扩展

**存储插件**:
```go
type StoragePlugin interface {
    Name() string
    Initialize(config map[string]interface{}) error
    LikeStorage
}

type PluginManager struct {
    plugins map[string]StoragePlugin
}

func (pm *PluginManager) RegisterPlugin(plugin StoragePlugin) {
    pm.plugins[plugin.Name()] = plugin
}
```

## 容错机制

### 1. 故障检测

**健康检查**:
```go
func (s *extendedLikeService) HealthCheck(ctx context.Context) error {
    // Redis健康检查
    if err := s.checkRedisHealth(ctx); err != nil {
        return fmt.Errorf("Redis不可用: %w", err)
    }
    
    // MongoDB健康检查
    if err := s.checkMongoHealth(ctx); err != nil {
        return fmt.Errorf("MongoDB不可用: %w", err)
    }
    
    return nil
}
```

### 2. 故障转移

**自动切换**:
```go
func (s *extendedLikeService) LikeItem(ctx context.Context, userID, itemType, itemID string) error {
    // 尝试主存储
    if err := s.primaryStorage.LikeItem(ctx, userID, itemType, itemID); err != nil {
        log.Printf("主存储失败，切换到备用存储: %v", err)
        
        // 切换到备用存储
        return s.secondaryStorage.LikeItem(ctx, userID, itemType, itemID)
    }
    
    return nil
}
```

### 3. 数据恢复

**增量恢复**:
```go
func (s *extendedLikeService) RecoverFromFailure(ctx context.Context, startTime, endTime time.Time) error {
    // 从MongoDB恢复到Redis
    records, err := s.getFailedRecords(ctx, startTime, endTime)
    if err != nil {
        return err
    }
    
    // 批量恢复
    return s.batchRecover(ctx, records)
}
```

### 4. 降级策略

**功能降级**:
```go
func (s *extendedLikeService) degradedLikeItem(ctx context.Context, userID, itemType, itemID string) error {
    // 降级模式：只记录到消息队列，异步处理
    return s.messageQueue.Publish(ctx, &LikeMessage{
        UserID:   userID,
        ItemType: itemType,
        ItemID:   itemID,
        Action:   "like",
        Timestamp: time.Now(),
    })
}
```

通过以上架构设计，扩展点赞服务能够提供高性能、高可用、可扩展的点赞功能，满足各种业务场景的需求。