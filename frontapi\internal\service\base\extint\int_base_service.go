package base

import (
	"context"
	"errors"
	"fmt"
	"frontapi/pkg/redis"
	"frontapi/pkg/types"
	"reflect"
	"time"

	"gorm.io/gorm"

	"frontapi/internal/hooks"
	"frontapi/internal/models"
	"frontapi/internal/repository/base/extint"
	baseService "frontapi/internal/service/base"
)

// IntBaseService 针对int类型ID的基础服务结构
type IntBaseService[T models.IntBaseModelConstraint] struct {
	repo        extint.IntBaseRepository[T]
	cacheTTL    time.Duration
	hookManager *hooks.ServiceHookManager
	db          *gorm.DB
	entityType  string
}

// IIntBaseService 针对int类型ID的基础服务接口
type IIntBaseService[T models.IntBaseModelConstraint] interface {
	// 基础CRUD操作
	Create(ctx context.Context, entity *T) (int, error)
	GetByID(ctx context.Context, id int, useCache bool) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id int) error
	List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, entities []*T) (int, error)
	BatchUpdate(ctx context.Context, entities []*T) error
	BatchDelete(ctx context.Context, ids []int) (int, error)
	BatchUpdateStatus(ctx context.Context, ids []int, status int) error
	Count(ctx context.Context, condition map[string]interface{}) (int64, error)

	// 查询操作
	FindByIDs(ctx context.Context, ids []int) ([]*T, error)
	FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error)
	FindOneByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) (*T, error)

	// 计数操作
	UpdateCount(ctx context.Context, id int, field string, count int64) error
	UpdateCountByIDs(ctx context.Context, ids []int, field string, count int64) error

	// 状态操作
	UpdateStatus(ctx context.Context, id int, status int) error

	// 工具方法
	SetEntityDefaults(entity *T)
	SetCacheTTL(ttl time.Duration)
	GetRepo() extint.IntBaseRepository[T]
}

// NewIntBaseService 创建针对int类型ID的基础服务实例
// NewIntBaseService 创建整型ID基础服务实例
// 参数:
//   - repo: 整型ID基础仓储接口实例
//   - entityType: 实体类型标识
//
// 返回:
//   - *IntBaseService[T]: 整型ID基础服务实例
func NewIntBaseService[T models.IntBaseModelConstraint](repo extint.IntBaseRepository[T], entityType string) *IntBaseService[T] {
	db := repo.GetDB()
	service := &IntBaseService[T]{
		repo:       repo,
		cacheTTL:   24 * time.Hour, // 默认缓存24小时
		db:         db,
		entityType: entityType,
	}

	// 初始化hooks管理器，使用repo中的DB连接
	service.hookManager = hooks.NewServiceHookManager(db, entityType)

	return service
}

// SetEntityDefaults 设置实体默认值
func (s *IntBaseService[T]) SetEntityDefaults(entity *T) {
	now := types.JSONTime(time.Now())

	// 使用反射设置创建时间和更新时间
	val := reflect.ValueOf(entity).Elem()
	typ := val.Type()

	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		if !field.CanSet() {
			continue
		}

		switch fieldType.Name {
		case "CreatedAt":
			if field.Type() == reflect.TypeOf(types.JSONTime{}) && field.IsZero() {
				field.Set(reflect.ValueOf(now))
			}
		case "UpdatedAt":
			if field.Type() == reflect.TypeOf(types.JSONTime{}) {
				field.Set(reflect.ValueOf(now))
			}
		case "Status":
			if field.Kind() == reflect.Int8 && field.Int() == 0 {
				field.SetInt(1) // 默认状态为启用
			}
		}
	}
}

// Create 创建实体
func (s *IntBaseService[T]) Create(ctx context.Context, entity *T) (int, error) {
	// 执行创建前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, entity); err != nil {
		return 0, fmt.Errorf("创建前钩子执行失败: %w", err)
	}

	s.SetEntityDefaults(entity)
	err := s.repo.Create(ctx, entity)
	if err != nil {
		return 0, fmt.Errorf("创建失败: %w", err)
	}

	// 执行创建后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterCreate, entity); err != nil {
		// 创建后钩子失败只记录日志，不影响创建结果
		// 这里可以添加日志记录
	}

	return (*entity).GetID(), nil
}

// GetByID 根据ID获取实体（带缓存控制）
func (s *IntBaseService[T]) GetByID(ctx context.Context, id int, useCache bool) (*T, error) {
	if id <= 0 {
		return nil, errors.New("ID必须大于0")
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := s.getCacheKey(id)
		var entity T
		if redis.GetJSON(cacheKey, &entity) == nil {
			return &entity, nil
		}
	}

	// 从数据库获取
	entity, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := s.getCacheKey(id)
		redis.SetJSON(cacheKey, *entity, s.cacheTTL)
	}

	return entity, nil
}

// Update 更新实体
func (s *IntBaseService[T]) Update(ctx context.Context, entity *T) error {
	// 执行更新前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, entity); err != nil {
		return fmt.Errorf("更新前钩子执行失败: %w", err)
	}

	// 设置更新时间
	val := reflect.ValueOf(entity).Elem()
	updatedAtField := val.FieldByName("UpdatedAt")
	if updatedAtField.IsValid() && updatedAtField.CanSet() {
		updatedAtField.Set(reflect.ValueOf(types.JSONTime(time.Now())))
	}

	err := s.repo.Update(ctx, entity)
	if err != nil {
		return fmt.Errorf("更新失败: %w", err)
	}

	// 执行更新后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, entity); err != nil {
		// 更新后钩子失败只记录日志，不影响更新结果
		// 这里可以添加日志记录
	}

	// 删除缓存
	s.deleteCacheByID((*entity).GetID())

	return nil
}

// Delete 删除实体
func (s *IntBaseService[T]) Delete(ctx context.Context, id int) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	// 获取实体用于钩子执行
	entity, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取实体失败: %w", err)
	}

	// 执行删除前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeDelete, entity); err != nil {
		return fmt.Errorf("删除前钩子执行失败: %w", err)
	}

	err = s.repo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("删除失败: %w", err)
	}

	// 执行删除后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterDelete, entity); err != nil {
		// 删除后钩子失败只记录日志，不影响删除结果
		// 这里可以添加日志记录
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

// List 获取实体列表（带缓存控制）
func (s *IntBaseService[T]) List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := baseService.BuildListCacheKey("entity", condition, page, pageSize)
		var result struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}
		if redis.GetJSON(cacheKey, &result) == nil {
			return result.Items, result.Total, nil
		}
	}

	// 从数据库获取
	items, total, err := s.repo.List(ctx, condition, orderBy, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取列表失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := baseService.BuildListCacheKey("entity", condition, page, pageSize)
		result := struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}{
			Items: items,
			Total: total,
		}
		redis.SetJSON(cacheKey, result, s.cacheTTL)
	}

	return items, total, nil
}

// BatchCreate 批量创建实体
func (s *IntBaseService[T]) BatchCreate(ctx context.Context, entities []*T) (int, error) {
	if len(entities) == 0 {
		return 0, errors.New("实体列表不能为空")
	}

	// 执行批量创建前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchCreate, entities); err != nil {
		return 0, fmt.Errorf("批量创建前钩子执行失败: %w", err)
	}

	// 设置默认值
	for _, entity := range entities {
		s.SetEntityDefaults(entity)
	}

	count, err := s.repo.BatchCreate(ctx, entities)
	if err != nil {
		return 0, fmt.Errorf("批量创建失败: %w", err)
	}

	// 执行批量创建后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchCreate, entities); err != nil {
		// 创建后钩子失败只记录日志，不影响创建结果
		// 这里可以添加日志记录
	}

	return count, nil
}

// BatchUpdate 批量更新实体
func (s *IntBaseService[T]) BatchUpdate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return errors.New("实体列表不能为空")
	}

	// 执行批量更新前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchUpdate, entities); err != nil {
		return fmt.Errorf("批量更新前钩子执行失败: %w", err)
	}

	// 设置更新时间
	now := types.JSONTime(time.Now())
	for _, entity := range entities {
		val := reflect.ValueOf(entity).Elem()
		updatedAtField := val.FieldByName("UpdatedAt")
		if updatedAtField.IsValid() && updatedAtField.CanSet() {
			updatedAtField.Set(reflect.ValueOf(now))
		}
	}

	err := s.repo.BatchUpdate(ctx, entities)
	if err != nil {
		return fmt.Errorf("批量更新失败: %w", err)
	}

	// 执行批量更新后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchUpdate, entities); err != nil {
		// 更新后钩子失败只记录日志，不影响更新结果
		// 这里可以添加日志记录
	}

	// 删除相关缓存
	for _, entity := range entities {
		s.deleteCacheByID((*entity).GetID())
	}

	return nil
}

// BatchDelete 批量删除实体
func (s *IntBaseService[T]) BatchDelete(ctx context.Context, ids []int) (int, error) {
	if len(ids) == 0 {
		return 0, errors.New("ID列表不能为空")
	}

	// 获取实体用于钩子执行
	entities := make([]*T, 0, len(ids))
	for _, id := range ids {
		entity, err := s.repo.FindByID(ctx, id)
		if err != nil {
			// 如果实体不存在，跳过
			continue
		}
		entities = append(entities, entity)
	}

	// 执行批量删除前钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchDelete, entities); err != nil {
		return 0, fmt.Errorf("批量删除前钩子执行失败: %w", err)
	}

	err := s.repo.BatchDelete(ctx, ids)
	if err != nil {
		return 0, fmt.Errorf("批量删除失败: %w", err)
	}

	// 执行批量删除后钩子
	if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchDelete, entities); err != nil {
		// 删除后钩子失败只记录日志，不影响删除结果
		// 这里可以添加日志记录
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return len(ids), nil
}

// BatchUpdateStatus 批量更新状态
func (s *IntBaseService[T]) BatchUpdateStatus(ctx context.Context, ids []int, status int) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	// 这里需要根据具体的仓库实现来调用
	// 如果仓库有BatchUpdateStatus方法，直接调用
	if batchRepo, ok := s.repo.(interface {
		BatchUpdateStatus(ctx context.Context, ids []int, status int) error
	}); ok {
		err := batchRepo.BatchUpdateStatus(ctx, ids, status)
		if err != nil {
			return fmt.Errorf("批量更新状态失败: %w", err)
		}
	} else {
		return errors.New("仓库不支持批量更新状态操作")
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return nil
}

// Count 计数操作
func (s *IntBaseService[T]) Count(ctx context.Context, condition map[string]interface{}) (int64, error) {
	return s.repo.Count(ctx, condition)
}

// FindByIDs 根据ID列表查找实体
func (s *IntBaseService[T]) FindByIDs(ctx context.Context, ids []int) ([]*T, error) {
	if len(ids) == 0 {
		return []*T{}, nil
	}

	// 构建条件
	condition := map[string]interface{}{
		"id": ids,
	}

	return s.repo.FindByCondition(ctx, condition, "")
}

// FindByCondition 根据条件查找实体
func (s *IntBaseService[T]) FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error) {
	return s.repo.FindByCondition(ctx, condition, orderBy)
}

// FindOneByCondition 根据条件查找单个实体
func (s *IntBaseService[T]) FindOneByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) (*T, error) {
	return s.repo.FindOneByCondition(ctx, condition, orderBy)
}

// UpdateCount 更新计数字段
func (s *IntBaseService[T]) UpdateCount(ctx context.Context, id int, field string, count int64) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	// 如果仓库支持UpdateCount方法，直接调用
	if countRepo, ok := s.repo.(interface {
		UpdateCount(ctx context.Context, id int, field string, increment int64) error
	}); ok {
		err := countRepo.UpdateCount(ctx, id, field, count)
		if err != nil {
			return fmt.Errorf("更新计数失败: %w", err)
		}
	} else {
		return errors.New("仓库不支持更新计数操作")
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

// UpdateCountByIDs 批量更新计数字段
func (s *IntBaseService[T]) UpdateCountByIDs(ctx context.Context, ids []int, field string, count int64) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	err := s.repo.UpdateCountByIDs(ctx, ids, field, count)
	if err != nil {
		return fmt.Errorf("批量更新计数失败: %w", err)
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return nil
}

// UpdateStatus 更新状态
func (s *IntBaseService[T]) UpdateStatus(ctx context.Context, id int, status int) error {
	if id <= 0 {
		return errors.New("ID必须大于0")
	}

	// 如果仓库支持UpdateStatus方法，直接调用
	if statusRepo, ok := s.repo.(interface {
		UpdateStatus(ctx context.Context, id int, status int) error
	}); ok {
		err := statusRepo.UpdateStatus(ctx, id, status)
		if err != nil {
			return fmt.Errorf("更新状态失败: %w", err)
		}
	} else {
		return errors.New("仓库不支持更新状态操作")
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

// SetCacheTTL 设置缓存TTL
func (s *IntBaseService[T]) SetCacheTTL(ttl time.Duration) {
	s.cacheTTL = ttl
}

// GetRepo 获取仓库实例
func (s *IntBaseService[T]) GetRepo() extint.IntBaseRepository[T] {
	return s.repo
}

// GetHookManager 获取钩子管理器
func (s *IntBaseService[T]) GetHookManager() *hooks.ServiceHookManager {
	return s.hookManager
}

// RegisterDuplicateCheck 注册重复性检查钩子
func (s *IntBaseService[T]) RegisterDuplicateCheck(tableName string, fields []string, message string) {
	s.hookManager.RegisterDuplicateCheck(tableName, fields, message)
}

// RegisterDataCleaning 注册数据清洗钩子
func (s *IntBaseService[T]) RegisterDataCleaning(trimFields, lowerFields, upperFields []string, defaultValues map[string]interface{}) {
	s.hookManager.RegisterDataCleaning(trimFields, lowerFields, upperFields, defaultValues)
}

// RegisterAuditHook 注册审计钩子
func (s *IntBaseService[T]) RegisterAuditHook(userID string) {
	s.hookManager.RegisterAuditHook(userID)
}

// RegisterTimestampHook 注册时间戳钩子
func (s *IntBaseService[T]) RegisterTimestampHook(createField, updateField string) {
	s.hookManager.RegisterTimestampHook(createField, updateField)
}

// RegisterValidationHook 注册验证钩子
func (s *IntBaseService[T]) RegisterValidationHook(rules map[string]interface{}) {
	s.hookManager.RegisterValidationHook(rules)
}

// SetupCommonHooks 设置通用钩子
func (s *IntBaseService[T]) SetupCommonHooks(setup *hooks.CommonHooksSetup) {
	integrator := hooks.NewServiceIntegrator(s.db)
	s.hookManager = integrator.IntegrateHooks(s.entityType, setup.TableName, func(manager *hooks.ServiceHookManager) {
		// 注册重复检查钩子
		if len(setup.DuplicateFields) > 0 {
			manager.RegisterDuplicateCheck(setup.TableName, setup.DuplicateFields, "数据重复")
		}

		// 注册数据清洗钩子
		if len(setup.TrimFields) > 0 || len(setup.LowerFields) > 0 || len(setup.UpperFields) > 0 || len(setup.DefaultValues) > 0 {
			manager.RegisterDataCleaning(setup.TrimFields, setup.LowerFields, setup.UpperFields, setup.DefaultValues)
		}

		// 注册审计钩子
		if setup.EnableAudit && setup.UserID != "" {
			manager.RegisterAuditHook(setup.UserID)
		}

		// 注册时间戳钩子
		if setup.EnableTimestamp {
			createField := setup.CreateTimeField
			updateField := setup.UpdateTimeField
			if createField == "" {
				createField = "created_at"
			}
			if updateField == "" {
				updateField = "updated_at"
			}
			manager.RegisterTimestampHook(createField, updateField)
		}

		// 注册验证钩子
		if len(setup.ValidationRules) > 0 {
			rules := make(map[string]interface{})
			for field, validationRules := range setup.ValidationRules {
				rules[field] = validationRules
			}
			manager.RegisterValidationHook(rules)
		}
	})
}

// getCacheKey 生成缓存键
func (s *IntBaseService[T]) getCacheKey(id int) string {
	entityType := reflect.TypeOf((*T)(nil)).Elem().Name()
	return fmt.Sprintf("%s:%d", entityType, id)
}

// deleteCacheByID 根据ID删除缓存
func (s *IntBaseService[T]) deleteCacheByID(id int) {
	cacheKey := s.getCacheKey(id)
	redis.Del(cacheKey)
}
