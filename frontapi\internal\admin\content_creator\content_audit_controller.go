package content_creator

import (
	"frontapi/internal/admin"
	contentCreatorModel "frontapi/internal/models/content_creator"
	contentCreatorSrv "frontapi/internal/service/content_creator"
	contentCreatorValidator "frontapi/internal/validation/content_creator"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ContentAuditController 内容审核控制器
type ContentAuditController struct {
	admin.BaseController // 继承BaseController
	ContentAuditService  contentCreatorSrv.ContentAuditService
}

// NewContentAuditController 创建内容审核控制器实例
func NewContentAuditController(contentAuditService contentCreatorSrv.ContentAuditService) *ContentAuditController {
	return &ContentAuditController{
		ContentAuditService: contentAuditService,
	}
}

// ListContentAudits 获取内容审核列表
func (c *ContentAuditController) ListContentAudits(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	status := reqInfo.Get("status").GetString()
	creatorID := reqInfo.Get("creator_id").GetString()
	condition := map[string]interface{}{
		"content_type": contentType,
		"status":       status,
		"creator_id":   creatorID,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询内容审核列表
	list, total, err := c.ContentAuditService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容审核列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// GetContentAudit 获取内容审核详情
func (c *ContentAuditController) GetContentAudit(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询内容审核
	contentAudit, err := c.ContentAuditService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容审核详情失败: "+err.Error())
	}

	if contentAudit == nil {
		return c.NotFound(ctx, "内容审核不存在")
	}

	// 返回内容审核详情
	return c.Success(ctx, contentAudit)
}

// CreateContentAudit 创建内容审核
func (c *ContentAuditController) CreateContentAudit(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.CreateContentAuditRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 创建内容审核
	contentAudit := &contentCreatorModel.ContentAudit{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentAudit); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	id, err := c.ContentAuditService.Create(ctx.Context(), contentAudit)
	if err != nil {
		return c.InternalServerError(ctx, "创建内容审核失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建内容审核成功",
	})
}

// UpdateContentAudit 更新内容审核
func (c *ContentAuditController) UpdateContentAudit(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.UpdateContentAuditRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新内容审核
	contentAudit := &contentCreatorModel.ContentAudit{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentAudit); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	err := c.ContentAuditService.Update(ctx.Context(), contentAudit)
	if err != nil {
		return c.InternalServerError(ctx, "更新内容审核失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新内容审核成功")
}

// DeleteContentAudit 删除内容审核
func (c *ContentAuditController) DeleteContentAudit(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除内容审核
	err = c.ContentAuditService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除内容审核失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除内容审核成功")
}

// UpdateContentAuditStatus 更新内容审核状态
func (c *ContentAuditController) UpdateContentAuditStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.UpdateContentAuditRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新内容审核状态
	contentAudit := &contentCreatorModel.ContentAudit{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentAudit); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	err := c.ContentAuditService.Update(ctx.Context(), contentAudit)
	if err != nil {
		return c.InternalServerError(ctx, "更新内容审核状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新内容审核状态成功")
}
