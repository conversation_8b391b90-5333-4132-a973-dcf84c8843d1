import { request } from "../../request";

// 视频查询参数接口
interface VideoData {
    id?: number | string;
    title: string;
    description?: string;
    url: string;
    cover?: string;
    duration?: number;
    categoryId?: number | string;
    categoryName?: string;
    channelId?: number | string;
    channelName?: string;
    creatorId?: string;
    creatorName?: string;
    tags?: string[];
    resolution?: string;
    status?: number;
    views?: number;
    likes?: number;
    shares?: number;
    featured?: boolean;
    uploadTime?: string;
    [key: string]: any;
}

interface VideoParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        title?: string;
        keyword?: string;
        categoryId?: string;
        channelId?: string;
        status?: number;
    };
}

// 视频分类接口
interface VideoCategoryData {
    id?: number | string;
    name: string;
    description?: string;
    coverUrl?: string;
    parentId?: number | string;
    sort?: number;
    status?: number;
    [key: string]: any;
}

// 用户搜索接口
interface UserSearchParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        status?: number;
    };
}

// 分类搜索接口
interface CategorySearchParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        status?: number;
    };
}

// 视频 API

/**
 * 获取视频列表
 */
export function getVideoList(params?: VideoParams) {
    return request({
        url: '/videos/list',
        method: 'post',
        data: params
    })
}

/**
 * 获取视频详情
 */
export function getVideoDetail(id: string | number) {
    return request({
        url: `/videos/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 创建视频
 */
export function createVideo(data: VideoData) {
    return request({
        url: '/videos/add',
        method: 'post',
        data: { data }
    })
}

/**
 * 更新视频
 */
export function updateVideo(id: string | number, data: VideoData) {
    return request({
        url: `/videos/update/${id}`,
        method: 'post',
        data: { data }
    })
}

/**
 * 删除视频
 */
export function deleteVideo(id: string | number) {
    return request({
        url: `/videos/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

// ============ 视频分类管理API ============ 

/**
 * 获取视频分类列表
 */
export function getVideoCategoryList(params?: any) {
    return request({
        url: "/video-categories/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取视频分类详情
 */
export function getVideoCategoryDetail(id: string) {
    return request({
        url: `/video-categories/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 新增视频分类
 */
export function addVideoCategory(data: Partial<any>) {
    return request({
        url: "/video-categories/add",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新视频分类
 */
export function updateVideoCategory(data: Partial<any>) {
    return request({
        url: "/video-categories/update",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新视频分类状态
 */
export function updateVideoCategoryStatus(data: any) {
    return request({
        url: `/video-categories/update-status/${data.id}`,
        method: "post",
        data: { data: { status: data.status, id: data.id } }
    })
}

/**
 * 批量更新视频分类状态
 */
export function batchUpdateVideoCategoryStatus(data: any) {
    return request({
        url: "/video-categories/batch-update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 删除视频分类
 */
export function deleteVideoCategory(id: string) {
    return request({
        url: `/video-categories/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 批量删除视频分类
 */
export function batchDeleteVideoCategory(data: any) {
    return request({
        url: "/video-categories/batch-delete",
        method: "post",
        data: { data: data }
    })
}

/**
 * 搜索视频分类 - 用于下拉选择器
 */
export function searchVideoCategories(params?: any) {
    return request({
        url: '/video-categories/list',
        method: 'post',
        data: params
    })
}

/**
 * 搜索用户 - 用于创建者选择器
 */
export function searchUsers(params?: UserSearchParams) {
    return request({
        url: '/users/list',
        method: 'post',
        data: params
    })
}

// ============ 视频频道管理API ============

/**
 * 获取视频频道列表
 */
export function getChannelList(params?: any) {
    return request({
        url: '/video-channels/list',
        method: 'post',
        data: params
    })
}

/**
 * 获取视频频道详情
 */
export function getChannelDetail(id: string | number) {
    return request({
        url: `/video-channels/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 新增视频频道
 */
export function addChannel(data: any) {
    return request({
        url: '/video-channels/add',
        method: 'post',
        data: { data }
    })
}

/**
 * 更新视频频道
 */
export function updateChannel(data: any) {
    return request({
        url: '/video-channels/update',
        method: 'post',
        data: { data }
    })
}

/**
 * 更新视频频道状态
 */
export function updateChannelStatus(data: any) {
    return request({
        url: `/video-channels/update-status/${data.id}`,
        method: 'post',
        data: { data: { status: data.status } }
    })
}

/**
 * 批量更新视频频道状态
 */
export function batchUpdateChannelStatus(data: any) {
    return request({
        url: '/video-channels/batch-update-status',
        method: 'post',
        data: { data: data }
    })
}

/**
 * 删除视频频道
 */
export function deleteChannel(id: string) {
    return request({
        url: `/video-channels/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 批量删除视频频道
 */
export function batchDeleteChannel(data: any) {
    return request({
        url: '/video-channels/batch-delete',
        method: 'post',
        data: { data: data }
    })
}

/**
 * 搜索视频频道 - 用于下拉选择器
 */
export function searchChannels(params?: any) {
    return request({
        url: '/video-channels/list',
        method: 'post',
        data: params
    })
}

// ============ 视频标签管理API ============

/**
 * 搜索标签 - 用于下拉选择器
 */
export function searchTags(params?: any) {
    return request({
        url: '/tags/list',
        method: 'post',
        data: params
    })
}

// ============ 视频专辑管理API ============

/**
 * 搜索专辑 - 用于下拉选择器
 */
export function searchAlbums(params?: any) {
    return request({
        url: '/video-albums/list',
        method: 'post',
        data: params
    })
}

// ============ 视频状态更新API ============

/**
 * 更新视频状态
 */
export function updateVideoStatus(id: string | number, status: number) {
    return request({
        url: `/videos/update-status/${id}`,
        method: 'post',
        data: { data: { id, status } }
    });
}

/**
 * 批量更新视频状态
 */
export function batchUpdateVideoStatus(ids: (string | number)[], status: number) {
    return request({
        url: '/videos/batch-update-status',
        method: 'post',
        data: { data: { ids, status } }
    });
}

/**
 * 批量删除视频
 */
export function batchDeleteVideos(ids: (string | number)[]) {
    return request({
        url: '/videos/batch-delete',
        method: 'post',
        data: { data: { ids } }
    });
}

/**
 * 审核视频
 */
export function reviewVideo(id: string | number, data: { status: number; reason: string }) {
    return request({
        url: `/videos/review/${id}`,
        method: 'post',
        data: { data: { ...data, id } }
    });
}

/**
 * 批量审核视频
 */
export function batchReviewVideos(data: { ids: (string | number)[]; status: number; reason: string }) {
    return request({
        url: '/videos/batch-review',
        method: 'post',
        data: { data }
    });
}
