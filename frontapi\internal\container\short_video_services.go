package container

import (
	shortVideoRepo "frontapi/internal/repository/shortvideos"
	userRepo "frontapi/internal/repository/users"
	"frontapi/internal/service/base/extcollect"
	"frontapi/internal/service/base/extlike"
	shortvideoServices "frontapi/internal/service/shortvideos"
)

// InitShortVideoServices 初始化短视频相关服务
func InitShortVideoServices(b *ServiceBuilder) {
	// 初始化短视频仓库
	shortVideoRepoImpl := shortVideoRepo.NewShortVideoRepository(b.DB())
	shortVideoCategoryRepo := shortVideoRepo.NewShortVideoCategoryRepository(b.DB())
	shortVideoCommentRepo := shortVideoRepo.NewShortVideoCommentRepository(b.DB())
	shortVideoLikeRepo := shortVideoRepo.NewShortVideoLikeRepository(b.DB())
	shortVideoCommentLikeRepo := shortVideoRepo.NewShortVideoCommentLikeRepository(b.DB())
	userShortCollectionRepo := userRepo.NewUserShortsCollectionRepository(b.DB())

	// 获取扩展服务（从容器中获取）
	container := b.Services()
	var likeService extlike.LikeService
	var collectService extcollect.CollectService

	// 类型断言获取扩展服务
	if container.LikeService != nil {
		if ls, ok := container.LikeService.(extlike.LikeService); ok {
			likeService = ls
		}
	}

	if container.CollectService != nil {
		if cs, ok := container.CollectService.(extcollect.CollectService); ok {
			collectService = cs
		}
	}

	// 初始化短视频服务 - 传入扩展服务
	container.ShortVideoService = shortvideoServices.NewShortVideoService(
		shortVideoRepoImpl,
		shortVideoLikeRepo,
		userShortCollectionRepo,
		likeService,    // 新增：扩展点赞服务
		collectService, // 新增：扩展收藏服务
	)

	container.ShortVideoCategoryService = shortvideoServices.NewShortVideoCategoryService(shortVideoCategoryRepo)
	container.ShortVideoCommentService = shortvideoServices.NewShortVideoCommentService(
		shortVideoCommentRepo,
		shortVideoRepoImpl,
		shortVideoCommentLikeRepo,
	)
}
