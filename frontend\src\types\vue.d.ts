/**
 * Vue全局类型增强
 */
import { ThemeConfig } from '@/shared/themes'
import { I18n } from 'vue-i18n'
import { RouteLocationNormalizedLoaded, Router } from 'vue-router'

declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $t: Function
        $i18n: I18n
        $theme: {
            currentThemeName: {
                value: string
            }
            currentTheme: {
                value: ThemeConfig
            }
            setTheme: (name: string) => void
        }
        $route: RouteLocationNormalizedLoaded
        $router: Router
    }
}

declare global {
    interface Window {
        __i18n_messages?: Record<string, Record<string, any>>
        i18n?: any
    }
} 