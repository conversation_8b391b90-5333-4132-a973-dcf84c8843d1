import { ApiResponse } from "@/types/https";
import { BatchDeletePostRequest, BatchReviewPostRequest, CreatePostRequest, PostItem, PostListResponse, PostParams, ReviewPostRequest, UpdatePostRequest } from "@/types/posts";
import { request } from "../../request";

/**
 * 获取帖子列表
 * @param params 查询参数
 */
export function getPostList(params: PostParams) {
    console.log("params:", params);
    return request<ApiResponse<PostListResponse>>({
        url: "/posts/list",
        method: "post",
        data: params
    });
}

/**
 * 获取帖子详情
 * @param id 帖子ID
 */
export function getPostDetail(id: string) {
    return request<ApiResponse<PostItem>>({
        url: `/posts/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    });
}

/**
 * 创建帖子
 * @param data 帖子数据
 */
export function createPost(data: CreatePostRequest) {
    return request<ApiResponse<null>>({
        url: "/posts/add",
        method: "post",
        data: { data }
    });
}

/**
 * 更新帖子
 * @param data 帖子数据
 */
export function updatePost(data: UpdatePostRequest) {
    return request<ApiResponse<null>>({
        url: "/posts/update",
        method: "post",
        data: { data }
    });
}

/**
 * 更新帖子状态
 * @param data 状态数据 {id: string, status: number}
 */
export function updatePostStatus(data: { id: string, status: number }) {
    return request<ApiResponse<null>>({
        url: "/posts/update-status",
        method: "post",
        data: { data }
    });
}

/**
 * 批量更新帖子状态
 * @param data 批量状态数据 {ids: string[], status: number}
 */
export function batchUpdatePostStatus(data: { ids: string[], status: number }) {
    return request<ApiResponse<null>>({
        url: "/posts/batch-update-status",
        method: "post",
        data: { data }
    });
}

/**
 * 删除帖子（软删除）
 * @param id 帖子ID
 */
export function deletePost(id: string) {
    return request<ApiResponse<null>>({
        url: `/posts/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    });
}

/**
 * 批量删除帖子
 * @param data 批量删除数据 {ids: string[]}
 */
export function batchDeletePost(data: BatchDeletePostRequest) {
    return request<ApiResponse<null>>({
        url: "/posts/batch-delete",
        method: "post",
        data: { data }
    });
}

/**
 * 审核帖子
 * @param data 审核数据 {id: string, status: number, reject_reason?: string}
 */
export function reviewPost(data: ReviewPostRequest) {
    return request<ApiResponse<null>>({
        url: "/posts/review",
        method: "post",
        data: { data }
    });
}

/**
 * 批量审核帖子
 * @param data 批量审核数据 {ids: string[], status: number, reject_reason?: string}
 */
export function batchReviewPost(data: BatchReviewPostRequest) {
    return request<ApiResponse<null>>({
        url: "/posts/batch-review",
        method: "post",
        data: { data }
    });
}

// 设置/取消推荐帖子
export function toggleFeaturedPost(id: string, is_featured: number) {
    return request<ApiResponse<null>>({
        url: "/posts/toggle-status",
        method: "post",
        data: { data: { "id": id, "is_show": is_featured } }
    });
} 