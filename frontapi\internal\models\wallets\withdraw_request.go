package wallets

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// WithdrawRequest 提现申请模型
type WithdrawRequest struct {
	models.BaseModelStruct
	UserID       string         `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	OrderNo      string         `json:"order_no" gorm:"type:string;size:50;not null;comment:提现单号"`
	Amount       float64        `json:"amount" gorm:"type:decimal(10,2);comment:提现金额"`
	Fee          float64        `json:"fee" gorm:"type:decimal(10,2);default:0.00;comment:手续费"`
	ActualAmount float64        `json:"actual_amount" gorm:"type:decimal(10,2);comment:实际到账金额"`
	AccountType  string         `json:"account_type" gorm:"type:string;size:20;not null;comment:账户类型"`
	AccountName  string         `json:"account_name" gorm:"type:string;size:50;not null;comment:账户姓名"`
	AccountNo    string         `json:"account_no" gorm:"type:string;size:100;not null;comment:账户号码"`
	BankName     string         `json:"bank_name" gorm:"type:string;size:100;comment:银行名称"`
	BankBranch   string         `json:"bank_branch" gorm:"type:string;size:100;comment:银行支行"`
	Status       string         `json:"status" gorm:"type:string;size:20;default:'pending';comment:状态"`
	Remark       string         `json:"remark" gorm:"type:string;size:255;comment:备注"`
	AdminID      string         `json:"admin_id" gorm:"type:string;size:36;comment:处理人ID"`
	ProcessTime  types.JSONTime `json:"process_time" gorm:"comment:处理时间"`
	PaymentTime  types.JSONTime `json:"payment_time" gorm:"comment:打款时间"`
	PaymentNo    string         `json:"payment_no" gorm:"type:string;size:100;comment:打款单号"`
	IP           string         `json:"ip" gorm:"type:string;size:50;comment:申请IP"`
}

// TableName 指定表名
func (WithdrawRequest) TableName() string {
	return "ly_withdraw_requests"
}
