package books

// BookChapterCreateRequest 书籍章节创建请求验证模型
type BookChapterCreateRequest struct {
	BookID    uint    `json:"bookId" validate:"required,min=1"`
	Title     string  `json:"title" validate:"required,min=2,max=100"`
	ChapterNo float64 `json:"chapterNo" validate:"required"` // 支持小数，如 1.5 表示番外
	Content   string  `json:"content" validate:"required,min=10"`
	WordCount int     `json:"wordCount" validate:"omitempty,min=0"`
	IsVIP     bool    `json:"isVip"`
	Price     float64 `json:"price" validate:"omitempty,min=0"`
}

// BookChapterUpdateRequest 书籍章节更新请求验证模型
type BookChapterUpdateRequest struct {
	Title     string  `json:"title" validate:"omitempty,min=2,max=100"`
	ChapterNo float64 `json:"chapterNo" validate:"omitempty"`
	Content   string  `json:"content" validate:"omitempty,min=10"`
	WordCount int     `json:"wordCount" validate:"omitempty,min=0"`
	IsVIP     bool    `json:"isVip"`
	Price     float64 `json:"price" validate:"omitempty,min=0"`
}

// CreateChapterRequest 创建章节请求验证模型
type CreateChapterRequest struct {
	BookID        string  `json:"book_id" validate:"required"`
	BookName      string  `json:"book_name" validate:"required"`
	Title         string  `json:"title" validate:"required"`
	ChapterNumber int     `json:"chapter_number"`
	Content       string  `json:"content" validate:"required"`
	IsLocked      uint8   `json:"is_locked"`
	Price         float64 `json:"price"`
}

// UpdateChapterRequest 更新章节请求验证模型
type UpdateChapterRequest struct {
	Title    string  `json:"title"`
	BookName string  `json:"book_name"`
	Content  string  `json:"content"`
	IsLocked *uint8  `json:"is_locked"`
	Price    float64 `json:"price"`
	Status   *uint8  `json:"status"`
}

// BatchUploadChaptersRequest 批量上传章节请求验证模型
type BatchUploadChaptersRequest struct {
	BookID      string                  `json:"book_id" validate:"required"`
	BookName    string                  `json:"book_name" validate:"required"`
	Chapters    []*CreateChapterRequest `json:"chapters" validate:"required"`
	IsOverwrite bool                    `json:"isOverwrite"`
}

// ReorderChapterRequest 重新排序章节请求验证模型
type ReorderChapterRequest struct {
	BookID        string `json:"bookId" validate:"required"`
	OldChapterID  string `json:"oldChapterId" validate:"required"`
	NewChapterID  string `json:"newChapterId" validate:"required"`
	NewChapterNum int    `json:"newChapterNum" validate:"required"`
}

// ChapterOrderItem 章节排序项结构体
type ChapterOrderItem struct {
	ID            string `json:"id"`
	ChapterNumber int    `json:"chapter_number"`
}

// BatchUpdateChapterOrderRequest 批量更新章节排序请求验证模型
type BatchUpdateChapterOrderRequest struct {
	BookID   string             `json:"book_id" validate:"required"`
	Chapters []ChapterOrderItem `json:"chapters" validate:"required"`
}
