<template>
  <div class="search-form">
    <el-form :inline="true" :model="modelValue">
      <el-form-item label="页码">
        <el-input 
          v-model="searchParams.page_number" 
          placeholder="请输入页码" 
          clearable
          @keyup.enter="handleSearch"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleReset">刷新</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue', 'search', 'reset']);

// 本地维护一个搜索参数对象，避免直接修改props
const searchParams = ref({ ...props.modelValue });

// 监听 props 变化更新本地搜索参数
watch(() => props.modelValue, (newVal) => {
  searchParams.value = { ...newVal };
}, { deep: true });

// 搜索处理
const handleSearch = () => {
  emit('update:modelValue', { ...searchParams.value });
  emit('search');
};

// 重置处理
const handleReset = () => {
  searchParams.value = { page_number: '' };
  emit('update:modelValue', { ...searchParams.value });
  emit('reset');
};
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style> 