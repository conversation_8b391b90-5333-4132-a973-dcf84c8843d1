package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type VideoSubtitleRepository interface {
	base.ExtendedRepository[videos.VideoSubtitle]
	FindByVideoID(ctx context.Context, videoID string) (*videos.VideoSubtitle, error)
}

type videoSubtitleRepository struct {
	base.ExtendedRepository[videos.VideoSubtitle]
}

// NewVideoSubtitleRepository 创建视频字幕仓库实例
func NewVideoSubtitleRepository(db *gorm.DB) VideoSubtitleRepository {
	return &videoSubtitleRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoSubtitle](db),
	}
}

// FindByVideoID 根据视频ID查找视频字幕
func (r *videoSubtitleRepository) FindByVideoID(ctx context.Context, videoID string) (*videos.VideoSubtitle, error) {
	conditions := map[string]interface{}{
		"video_id": videoID,
	}
	return r.FindOneByCondition(ctx, conditions, "")
}
