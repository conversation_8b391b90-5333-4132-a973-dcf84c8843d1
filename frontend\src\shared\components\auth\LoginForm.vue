<template>
  <el-form
    :model="loginForm"
    :rules="rules"
    ref="loginFormRef"
    class="login-form"
  >
    <el-form-item prop="username">
      <el-input
        v-model="loginForm.username"
        placeholder="用户名/邮箱"
        prefix-icon="User"
        :class="{ 'is-focused': activeInput === 'username' }"
        @focus="activeInput = 'username'"
        @blur="activeInput = ''"
      />
    </el-form-item>
    <el-form-item prop="password">
      <el-input
        v-model="loginForm.password"
        type="password"
        placeholder="密码"
        prefix-icon="Lock"
        show-password
        :class="{ 'is-focused': activeInput === 'password' }"
        @focus="activeInput = 'password'"
        @blur="activeInput = ''"
      />
    </el-form-item>
    <el-form-item prop="captcha">
      <div class="captcha-container">
        <el-input
          v-model="loginForm.captcha"
          placeholder="验证码"
          prefix-icon="Key"
          :class="{ 'is-focused': activeInput === 'captcha' }"
          @focus="activeInput = 'captcha'"
          @blur="activeInput = ''"
        >
        </el-input>
        <div class="captcha-image" @click="refreshCaptcha">
          <img :src="captchaUrl" alt="验证码" />
        </div>
      </div>
    </el-form-item>
    <div class="remember-forgot">
      <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
      <el-link type="primary" underline="never" @click="$emit('switch-component', 'forget')">忘记密码？</el-link>
    </div>
    <el-button type="primary" class="login-button" @click="handleLogin" :loading="loading">
      登录
    </el-button>
    <div class="register-link">
      还没有账号？<el-link type="primary" @click="$emit('switch-component', 'register')">立即注册</el-link>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { User, Lock, Key } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const emit = defineEmits<{
  (e: 'login-success'): void
  (e: 'switch-component', component: string): void
}>()

const loginFormRef = ref()
const loading = ref(false)
const activeInput = ref('')
const captchaUrl = ref('/api/captcha')

const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false
})

const rules = {
  username: [
    { required: true, message: '请输入用户名/邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' },
    {
      pattern: /^[\w-]+([\.[\w-]+)*@[\w-]+([\.[\w-]+)+$|^[\w]{3,20}$/,
      message: '请输入有效的用户名或邮箱地址',
      trigger: 'blur'
    }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 6, message: '请输入正确的验证码', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    loading.value = true
    await loginFormRef.value.validate()
    
    // TODO: 调用登录API
    // const response = await login(loginForm)
    // 模拟登录成功
    const mockUser = {
      id: 1,
      nickname: '测试用户',
      username: loginForm.username,
      avatar: 'https://via.placeholder.com/100',
      email: loginForm.username.includes('@') ? loginForm.username : undefined
    }
    const mockToken = 'mock-jwt-token-' + Date.now()
    
    // 调用store的登录成功方法
    const { useUserStore } = await import('@/stores/user')
    const userStore = useUserStore()
    userStore.loginSuccess(mockUser, mockToken)
    
    ElMessage.success('登录成功')
    emit('login-success')
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error('登录失败，请检查输入信息')
    refreshCaptcha()
  } finally {
    loading.value = false
  }
}

const refreshCaptcha = () => {
  captchaUrl.value = `/api/captcha?t=${new Date().getTime()}`
  loginForm.captcha = ''
}
</script>

<style lang="scss" scoped>
.login-form {
  :deep(.el-input) {
    .el-input__wrapper {
      background: #f5f7fa;
      border-radius: 8px;
      padding: 8px 15px;
      box-shadow: none;
      transition: all 0.3s ease;

      &.is-focus, &:hover {
        background: #fff;
        box-shadow: 0 0 0 1px var(--el-color-primary) !important;
      }

      .el-input__inner {
        height: 36px;
        font-size: 14px;
      }
    }
  }

  .captcha-container {
    display: flex;
    gap: 12px;

    .el-input {
      flex: 1;
    }

    .captcha-image {
      width: 100px;
      height: 36px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .login-button {
    width: 100%;
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border: none;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .register-link {
    text-align: center;
    margin-top: 16px;
  }
}

:deep([data-theme='dark']) {
  .login-form {
    :deep(.el-input) {
      .el-input__wrapper {
        background: #2c2c2c;
        
        &.is-focus, &:hover {
          background: #363636;
        }
      }
    }
  }
}
</style>