package redis

import (
	"context"
	"time"

	"frontapi/internal/service/base/extfollow"
	"frontapi/internal/service/base/extfollow/types"
	"frontapi/pkg/redis"
)

// RedisAdapter Redis 关注适配器
type RedisAdapter struct {
	client   *redis.Client
	cacheKey *types.CacheKey
	config   *Config

	// 操作处理器
	followOps  *FollowOperations
	queryOps   *QueryOperations
	rankingOps *RankingOperations
	statsOps   *StatsOperations
	cacheOps   *CacheOperations
}

// Config Redis适配器配置
type Config struct {
	// Redis配置
	RedisAddr     string `json:"redis_addr"`
	RedisPassword string `json:"redis_password"`
	RedisDB       int    `json:"redis_db"`

	// 缓存配置
	CachePrefix string        `json:"cache_prefix"`
	DefaultTTL  time.Duration `json:"default_ttl"`

	// 性能配置
	BatchSize  int           `json:"batch_size"`
	MaxRetries int           `json:"max_retries"`
	RetryDelay time.Duration `json:"retry_delay"`

	// 超时配置
	ConnectTimeout time.Duration `json:"connect_timeout"`
	ReadTimeout    time.Duration `json:"read_timeout"`
	WriteTimeout   time.Duration `json:"write_timeout"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		CachePrefix:    "follow:",
		DefaultTTL:     24 * time.Hour,
		BatchSize:      100,
		MaxRetries:     3,
		RetryDelay:     100 * time.Millisecond,
		ConnectTimeout: 5 * time.Second,
		ReadTimeout:    3 * time.Second,
		WriteTimeout:   3 * time.Second,
	}
}

// NewRedisAdapter 创建Redis适配器
func NewRedisAdapter(client *redis.Client, config *Config) extfollow.FollowAdapter {
	if config == nil {
		config = DefaultConfig()
	}

	cacheKey := types.NewCacheKey(config.CachePrefix)

	adapter := &RedisAdapter{
		client:   client,
		cacheKey: cacheKey,
		config:   config,
	}

	// 初始化操作处理器
	adapter.followOps = NewFollowOperations(client, cacheKey, config)
	adapter.queryOps = NewQueryOperations(client, cacheKey, config)
	adapter.rankingOps = NewRankingOperations(client, cacheKey, config)
	adapter.statsOps = NewStatsOperations(client, cacheKey, config)
	adapter.cacheOps = NewCacheOperations(client, cacheKey, config)

	return adapter
}

// Follow 关注用户
func (a *RedisAdapter) Follow(ctx context.Context, followerID, followeeID string) error {
	return a.followOps.Follow(ctx, followerID, followeeID)
}

// Unfollow 取消关注
func (a *RedisAdapter) Unfollow(ctx context.Context, followerID, followeeID string) error {
	return a.followOps.Unfollow(ctx, followerID, followeeID)
}

// IsFollowing 检查是否关注
func (a *RedisAdapter) IsFollowing(ctx context.Context, followerID, followeeID string) (bool, error) {
	return a.followOps.IsFollowing(ctx, followerID, followeeID)
}

// GetFollowerCount 获取粉丝数
func (a *RedisAdapter) GetFollowerCount(ctx context.Context, userID string) (int64, error) {
	return a.followOps.GetFollowerCount(ctx, userID)
}

// GetFollowingCount 获取关注数
func (a *RedisAdapter) GetFollowingCount(ctx context.Context, userID string) (int64, error) {
	return a.followOps.GetFollowingCount(ctx, userID)
}

// BatchFollow 批量关注
func (a *RedisAdapter) BatchFollow(ctx context.Context, operations []*types.FollowOperation) error {
	return a.followOps.BatchFollow(ctx, operations)
}

// BatchUnfollow 批量取消关注
func (a *RedisAdapter) BatchUnfollow(ctx context.Context, operations []*types.FollowOperation) error {
	return a.followOps.BatchUnfollow(ctx, operations)
}

// BatchGetFollowStatus 批量获取关注状态
func (a *RedisAdapter) BatchGetFollowStatus(ctx context.Context, followerID string, userIDs []string) (map[string]bool, error) {
	return a.followOps.BatchGetFollowStatus(ctx, followerID, userIDs)
}

// BatchGetFollowerCounts 批量获取粉丝数
func (a *RedisAdapter) BatchGetFollowerCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	return a.followOps.BatchGetFollowerCounts(ctx, userIDs)
}

// BatchGetFollowingCounts 批量获取关注数
func (a *RedisAdapter) BatchGetFollowingCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	return a.followOps.BatchGetFollowingCounts(ctx, userIDs)
}

// GetUserFollowers 获取用户粉丝列表
func (a *RedisAdapter) GetUserFollowers(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	return a.queryOps.GetUserFollowers(ctx, userID, limit, offset)
}

// GetUserFollowing 获取用户关注列表
func (a *RedisAdapter) GetUserFollowing(ctx context.Context, userID string, limit, offset int) ([]*types.FollowRecord, error) {
	return a.queryOps.GetUserFollowing(ctx, userID, limit, offset)
}

// GetFollowHistory 获取关注历史
func (a *RedisAdapter) GetFollowHistory(ctx context.Context, userID string, timeRange *types.TimeRange) ([]*types.FollowRecord, error) {
	return a.queryOps.GetFollowHistory(ctx, userID, timeRange)
}

// GetMutualFollows 获取互相关注
func (a *RedisAdapter) GetMutualFollows(ctx context.Context, userID1, userID2 string) ([]*types.FollowRecord, error) {
	return a.queryOps.GetMutualFollows(ctx, userID1, userID2)
}

// UpdateInfluenceRank 更新影响力排名
func (a *RedisAdapter) UpdateInfluenceRank(ctx context.Context, userID string, score float64) error {
	return a.rankingOps.UpdateInfluenceRank(ctx, userID, score)
}

// GetInfluenceRanking 获取影响力排行榜
func (a *RedisAdapter) GetInfluenceRanking(ctx context.Context, limit int) ([]string, error) {
	return a.rankingOps.GetInfluenceRanking(ctx, limit)
}

// GetInfluenceRankingWithScores 获取带分数的影响力排行榜
func (a *RedisAdapter) GetInfluenceRankingWithScores(ctx context.Context, limit int) (map[string]float64, error) {
	return a.rankingOps.GetInfluenceRankingWithScores(ctx, limit)
}

// GetUserFollowStats 获取用户关注统计
func (a *RedisAdapter) GetUserFollowStats(ctx context.Context, userID string) (*types.UserFollowStats, error) {
	return a.statsOps.GetUserFollowStats(ctx, userID)
}

// GetFollowTrends 获取关注趋势
func (a *RedisAdapter) GetFollowTrends(ctx context.Context, timeRange *types.TimeRange, limit int) ([]*types.FollowTrend, error) {
	return a.statsOps.GetFollowTrends(ctx, timeRange, limit)
}

// InvalidateCache 清除缓存
func (a *RedisAdapter) InvalidateCache(ctx context.Context, keys ...string) error {
	return a.cacheOps.InvalidateCache(ctx, keys...)
}

// WarmupCache 预热缓存
func (a *RedisAdapter) WarmupCache(ctx context.Context, userIDs []string) error {
	return a.cacheOps.WarmupCache(ctx, userIDs)
}

// GetCacheStats 获取缓存统计
func (a *RedisAdapter) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	return a.cacheOps.GetCacheStats(ctx)
}

// CleanupExpiredData 清理过期数据
func (a *RedisAdapter) CleanupExpiredData(ctx context.Context, before time.Time) error {
	return a.cacheOps.CleanupExpiredData(ctx, before)
}

// ExportData 导出数据
func (a *RedisAdapter) ExportData(ctx context.Context, userID, format string) ([]byte, error) {
	return a.cacheOps.ExportData(ctx, userID, format)
}

// ImportData 导入数据
func (a *RedisAdapter) ImportData(ctx context.Context, data []byte, format string) error {
	return a.cacheOps.ImportData(ctx, data, format)
}

// HealthCheck 健康检查
func (a *RedisAdapter) HealthCheck(ctx context.Context) error {
	return a.cacheOps.HealthCheck(ctx)
}

// Close 关闭适配器
func (a *RedisAdapter) Close() error {
	return a.cacheOps.Close()
}
