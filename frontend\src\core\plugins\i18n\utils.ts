/**
 * 国际化插件工具函数
 */

import { DEFAULT_LOCALE, LOCALE_STORAGE, getBrowserLocaleCode } from '@/config/locales.config';
import { isLocaleSupported, type LocaleType } from '@/locales';

/**
 * 获取存储的语言设置
 */
export function getStoredLocale(): LocaleType | null {
    const { KEY, STORAGE } = LOCALE_STORAGE;
    const storage = STORAGE === 'sessionStorage' ? sessionStorage : localStorage;
    const locale = storage.getItem(KEY);

    if (locale && isLocaleSupported(locale)) {
        return locale as LocaleType;
    }

    return null;
}

/**
 * 保存语言设置到存储
 */
export function saveLocaleToStorage(locale: LocaleType): void {
    const { KEY, STORAGE } = LOCALE_STORAGE;
    const storage = STORAGE === 'sessionStorage' ? sessionStorage : localStorage;
    storage.setItem(KEY, locale);
}

/**
 * 获取浏览器语言设置
 */
export function detectBrowserLocale(): LocaleType | null {
    try {
        const browserLang = navigator.language || (navigator as any).userLanguage || '';
        const localeCode = getBrowserLocaleCode(browserLang);

        if (localeCode && isLocaleSupported(localeCode)) {
            return localeCode as LocaleType;
        }
    } catch (error) {
        console.error('Failed to detect browser locale:', error);
    }

    return null;
}

/**
 * 检测最佳匹配语言
 * 优先级：存储中的语言 > 浏览器语言 > 默认语言
 */
export function detectLocale(useAutoDetect = true): LocaleType {
    // 1. 检查存储
    const storedLocale = getStoredLocale();
    if (storedLocale) return storedLocale;

    // 2. 检查浏览器语言
    if (useAutoDetect) {
        const browserLocale = detectBrowserLocale();
        if (browserLocale) return browserLocale;
    }

    // 3. 使用默认语言
    return DEFAULT_LOCALE as LocaleType;
} 