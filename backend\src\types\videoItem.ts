export interface VideoItem {
    id: string;
    title: string;
    description: string;
    cover: string;
    url: string;
    duration: number;
    resolution: string;
    quality: string;
    size: number;
    format: string;
    view_count: number;
    like_count: number;
    dislike_count: number;
    download_count: number;
    share_count: number;
    comment_count: number;
    favorite_count: number;
    score: number;
    category_id: string | null;
    category_name: string;
    channel_id: string | null;
    creator_id: string | null;
    creator_name: string;
    creator_avatar: string;
    celebrities: string[];
    vip_level: number;
    tags: string[];
    is_private: boolean;
    is_vip: boolean;
    price: number;
    src_type: number;
    src: string;
    upload_time: string;
    is_paid: number;
    is_featured: number;
    status: number;
    created_at: string;
    updated_at: string;
    
    // 兼容前端旧字段
    viewCount?: number;
    likeCount?: number;
    favoriteCount?: number;
    commentCount?: number;
    categoryName?: string;
    channelName?: string;
    coverUrl?: string;
    videoUrl?: string;
    stars?: string[];
    director?: string;
    releaseDate?: string;
    rating?: number;
}