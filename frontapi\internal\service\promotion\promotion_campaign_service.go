package promotion

import (
	"frontapi/pkg/types"

	model "frontapi/internal/models/promotion"
	repo "frontapi/internal/repository/promotion"
	"frontapi/internal/service/base"
)

// CreatePromotionCampaignRequest 创建活动请求
type CreatePromotionCampaignRequest struct {
	Name         string         `json:"name"`
	Code         string         `json:"code"`
	Description  string         `json:"description"`
	StartTime    types.JSONTime `json:"start_time"`
	EndTime      types.JSONTime `json:"end_time"`
	RewardType   string         `json:"reward_type"`
	RewardValue  float64        `json:"reward_value"`
	LimitPerUser *int           `json:"limit_per_user"`
	TotalLimit   *int           `json:"total_limit"`
	Conditions   string         `json:"conditions"`
	BannerImage  string         `json:"banner_image"`
	Status       string         `json:"status"`
}

type UpdatePromotionCampaignRequest struct {
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	StartTime    types.JSONTime `json:"start_time"`
	EndTime      types.JSONTime `json:"end_time"`
	RewardType   string         `json:"reward_type"`
	RewardValue  float64        `json:"reward_value"`
	LimitPerUser *int           `json:"limit_per_user"`
	TotalLimit   *int           `json:"total_limit"`
	Conditions   string         `json:"conditions"`
	BannerImage  string         `json:"banner_image"`
	Status       string         `json:"status"`
}

// PromotionCampaignService 推广活动服务接口
type PromotionCampaignService interface {
	base.IExtendedService[model.PromotionCampaign]
}

type promotionCampaignService struct {
	*base.ExtendedService[model.PromotionCampaign]
	repo repo.PromotionCampaignRepository
}

func NewPromotionCampaignService(repo repo.PromotionCampaignRepository) PromotionCampaignService {
	return &promotionCampaignService{
		ExtendedService: base.NewExtendedService[model.PromotionCampaign](repo, "promotion_campaign"),
		repo:            repo,
	}
}
