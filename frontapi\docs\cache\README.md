# 缓存系统文档

## 简介

缓存系统是一个灵活、高效的组件，用于在应用程序中缓存数据，减少数据库查询和计算开销。系统支持多种缓存存储方式，包括Redis、文件、内存、Memcached和BigCache等，并可以同时使用多种缓存策略。

## 主要特性

- **多适配器支持**：支持Redis、文件、内存、Memcached和BigCache等多种缓存存储方式
- **混合缓存**：可以同时使用多种缓存存储方式，提高可靠性和性能
- **类型安全**：提供泛型支持，确保类型安全
- **简单易用**：提供简洁的API，易于集成和使用
- **灵活配置**：支持通过环境变量、配置文件或代码配置
- **统计信息**：提供缓存使用统计，方便监控和调优

## 快速开始

### 安装

缓存系统已经集成到项目中，无需额外安装。

### 基本使用

```go
// 创建Redis缓存
redisCache, err := v2.NewRedis("localhost", 6379, "", 0)
if err != nil {
    log.Fatalf("创建Redis缓存失败: %v", err)
}
defer redisCache.Close()

// 设置缓存
err = redisCache.Set("key", "value", 10*time.Minute)
if err != nil {
    log.Printf("设置缓存失败: %v", err)
}

// 获取缓存
value, err := redisCache.Get("key")
if err != nil {
    log.Printf("获取缓存失败: %v", err)
}
fmt.Printf("缓存值: %v\n", value)
```

### 类型安全的缓存

```go
// 创建类型安全的缓存适配器
typedCache, err := v2.GetTyped[string](redisCache)
if err != nil {
    log.Fatalf("创建类型安全的缓存适配器失败: %v", err)
}

// 设置缓存
err = typedCache.Set("key", "value", 10*time.Minute)
if err != nil {
    log.Printf("设置缓存失败: %v", err)
}

// 获取缓存
value, err := typedCache.Get("key")
if err != nil {
    log.Printf("获取缓存失败: %v", err)
}
fmt.Printf("缓存值: %s\n", value)
```

### 混合缓存

```go
// 创建混合缓存（Redis + 文件）
hybridCache, err := v2.NewHybrid("localhost", 6379, "", 0, "storage/cache")
if err != nil {
    log.Fatalf("创建混合缓存失败: %v", err)
}
defer hybridCache.Close()

// 使用混合缓存
err = hybridCache.Set("key", "value", 10*time.Minute)
value, err := hybridCache.Get("key")
```

## 配置

缓存系统支持通过环境变量进行配置：

```
# Redis配置
CACHE_REDIS_HOST=localhost
CACHE_REDIS_PORT=6379
CACHE_REDIS_PASSWORD=
CACHE_REDIS_DB=0

# 文件缓存配置
CACHE_FILE_PATH=storage/cache

# 默认TTL（秒）
CACHE_DEFAULT_TTL=3600
```

## 接口

### CacheManager 接口

```go
type CacheManager interface {
    Get(key string) (interface{}, error)
    Set(key string, value interface{}, ttl time.Duration) error
    Delete(key string) error
    Clear() error
    Close() error
    CreateTypedAdapter(t interface{}) interface{}
    GetAdapter(adapterType string) (CacheAdapter, bool)
}
```

### CacheAdapter 接口

```go
type CacheAdapter interface {
    Get(ctx context.Context, key string) ([]byte, error)
    Set(ctx context.Context, key string, value []byte, expiration time.Duration) error
    Delete(ctx context.Context, key string) error
    Exists(ctx context.Context, key string) (bool, error)
    Clear(ctx context.Context) error
    Close() error
    Stats() *CacheStats
    Name() string
    Type() string
}
```

## 示例

详细示例可以参考：

- `examples/cache_integration_example.go`：基本使用示例
- `examples/cache_usage_example2.go`：高级使用示例

## 文档

更多文档请参考：

- [架构文档](ARCHITECTURE.md)：缓存系统的架构设计
- [优化总结](OPTIMIZATION_SUMMARY.md)：缓存系统的优化总结
- [目录结构](DIRECTORY_STRUCTURE.md)：缓存系统的目录结构
- [安装指南](INSTALLATION.md)：缓存系统的安装指南 