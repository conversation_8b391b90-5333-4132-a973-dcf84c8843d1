package comics

import (
	"frontapi/internal/admin"
	comic_service "frontapi/internal/service/comics"
)

// ComicFavoriteHandler 漫画收藏处理器
type ComicFavoriteHandler struct {
	admin.BaseController
	favoriteService comic_service.ComicFavoriteService
}

// NewComicFavoriteHandler 创建漫画收藏处理器
func NewComicFavoriteHandler(favoriteService comic_service.ComicFavoriteService) *ComicFavoriteHandler {
	return &ComicFavoriteHandler{
		favoriteService: favoriteService,
	}
}
