package sys

import (
	"context"
	"fmt"
	"frontapi/internal/models/sys"

	"gorm.io/gorm"
)

// DatabaseRepository 数据库查询接口
type DatabaseRepository interface {
	// GetTableList 获取数据库表列表
	GetTableList(ctx context.Context, dbName string) ([]sys.TableInfo, error)

	// GetTableInfo 获取单个表的详细信息
	GetTableInfo(ctx context.Context, dbName, tableName string) (*sys.TableInfo, error)

	// GetTableColumns 获取表字段信息
	GetTableColumns(ctx context.Context, dbName, tableName string) ([]sys.ColumnInfo, error)

	// GetTableForeignKeys 获取表外键信息
	GetTableForeignKeys(ctx context.Context, dbName, tableName string) ([]sys.ForeignKeyInfo, error)

	// GetCreateTableSQL 获取建表语句
	GetCreateTableSQL(ctx context.Context, tableName string) (string, error)

	// GetForeignKeyData 获取外键关联的真实数据
	GetForeignKeyData(ctx context.Context, tableName, columnName string, limit int) ([]interface{}, error)

	// InsertMockData 插入Mock数据到数据库
	InsertMockData(ctx context.Context, tableName string, data []map[string]interface{}) error

	// ExecuteSQL 执行SQL语句
	ExecuteSQL(ctx context.Context, sql string) error
}

// databaseRepository 数据库查询实现
type databaseRepository struct {
	db *gorm.DB
}

// NewDatabaseRepository 创建数据库查询仓库实例
func NewDatabaseRepository(db *gorm.DB) DatabaseRepository {
	return &databaseRepository{db: db}
}

// GetTableList 获取数据库表列表
func (r *databaseRepository) GetTableList(ctx context.Context, dbName string) ([]sys.TableInfo, error) {
	var tables []sys.TableInfo

	sql := `
		SELECT 
			TABLE_NAME,
			TABLE_COMMENT,
			ENGINE,
			TABLE_ROWS,
			CREATE_TIME
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = ? 
		AND TABLE_TYPE = 'BASE TABLE'
		ORDER BY TABLE_NAME
	`

	err := r.db.WithContext(ctx).Raw(sql, dbName).Scan(&tables).Error
	return tables, err
}

// GetTableColumns 获取表字段信息
func (r *databaseRepository) GetTableColumns(ctx context.Context, dbName, tableName string) ([]sys.ColumnInfo, error) {
	var columns []sys.ColumnInfo

	sql := `
		SELECT 
			COLUMN_NAME,
			DATA_TYPE,
			COLUMN_TYPE,
			IS_NULLABLE,
			COLUMN_DEFAULT,
			COLUMN_COMMENT,
			COLUMN_KEY,
			EXTRA,
			ORDINAL_POSITION
		FROM information_schema.COLUMNS 
		WHERE TABLE_SCHEMA = ? 
		AND TABLE_NAME = ?
		ORDER BY ORDINAL_POSITION
	`

	err := r.db.WithContext(ctx).Raw(sql, dbName, tableName).Scan(&columns).Error
	return columns, err
}

// GetTableForeignKeys 获取表外键信息
func (r *databaseRepository) GetTableForeignKeys(ctx context.Context, dbName, tableName string) ([]sys.ForeignKeyInfo, error) {
	var foreignKeys []sys.ForeignKeyInfo

	sql := `
		SELECT 
			kcu.CONSTRAINT_NAME,
			kcu.TABLE_NAME,
			kcu.COLUMN_NAME,
			kcu.REFERENCED_TABLE_NAME,
			kcu.REFERENCED_COLUMN_NAME,
			rc.UPDATE_RULE,
			rc.DELETE_RULE
		FROM information_schema.KEY_COLUMN_USAGE kcu
		JOIN information_schema.REFERENTIAL_CONSTRAINTS rc 
			ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
			AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
		WHERE kcu.TABLE_SCHEMA = ? 
		AND kcu.TABLE_NAME = ?
		AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
		ORDER BY kcu.ORDINAL_POSITION
	`

	err := r.db.WithContext(ctx).Raw(sql, dbName, tableName).Scan(&foreignKeys).Error
	return foreignKeys, err
}

// GetCreateTableSQL 获取建表语句
func (r *databaseRepository) GetCreateTableSQL(ctx context.Context, tableName string) (string, error) {
	var result sys.CreateTableResult

	sql := fmt.Sprintf("SHOW CREATE TABLE `%s`", tableName)
	err := r.db.WithContext(ctx).Raw(sql).Scan(&result).Error
	if err != nil {
		return "", err
	}

	return result.CreateTable, nil
}

// GetForeignKeyData 获取外键关联的真实数据
func (r *databaseRepository) GetForeignKeyData(ctx context.Context, tableName, columnName string, limit int) ([]interface{}, error) {
	var results []interface{}

	// 构建查询SQL，获取不重复的外键值
	sql := fmt.Sprintf("SELECT DISTINCT `%s` FROM `%s` WHERE `%s` IS NOT NULL LIMIT ?", columnName, tableName, columnName)

	rows, err := r.db.WithContext(ctx).Raw(sql, limit).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var value string
		if err := rows.Scan(&value); err != nil {
			return nil, err
		}
		results = append(results, value)
	}

	return results, nil
}

// InsertMockData 插入Mock数据到数据库
func (r *databaseRepository) InsertMockData(ctx context.Context, tableName string, data []map[string]interface{}) error {
	if len(data) == 0 {
		return nil
	}

	// 开启事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量插入数据
	for _, row := range data {
		if err := tx.Table(tableName).Create(row).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

// ExecuteSQL 执行SQL语句
func (r *databaseRepository) ExecuteSQL(ctx context.Context, sql string) error {
	return r.db.WithContext(ctx).Exec(sql).Error
}

// GetTableInfo 获取单个表的详细信息
func (r *databaseRepository) GetTableInfo(ctx context.Context, dbName, tableName string) (*sys.TableInfo, error) {
	var table sys.TableInfo

	sql := `
		SELECT 
			TABLE_NAME,
			TABLE_COMMENT,
			ENGINE,
			TABLE_ROWS,
			CREATE_TIME
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = ? 
		AND TABLE_NAME = ?
		AND TABLE_TYPE = 'BASE TABLE'
	`

	err := r.db.WithContext(ctx).Raw(sql, dbName, tableName).Scan(&table).Error
	if err != nil {
		return nil, err
	}

	// 如果没有找到表，返回nil
	if table.TableName == "" {
		return nil, nil
	}

	return &table, nil
}
