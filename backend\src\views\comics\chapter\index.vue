<template>
  <div class="chapter-list">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <div class="card-title">漫画章节</div>
          <div>
            <el-button v-if="isSortMode" type="success" @click="saveSortOrder">保存排序</el-button>
            <el-button v-if="isSortMode" @click="cancelSortMode">取消排序</el-button>
            <el-button v-if="!isSortMode" type="primary" @click="toggleSortMode">排序章节</el-button>
            <el-button v-if="!isSortMode" type="primary" @click="onAdd">添加章节</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form v-if="!isSortMode" :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="章节标题">
          <el-input v-model="searchForm.title" placeholder="请输入章节标题" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="所有状态" clearable style="width: 200px;">
            <el-option label="正常" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">搜索</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 章节列表组件 -->
      <ChapterList
        :loading="loading"
        :chapter-list="chapterList"
        :sortable-chapter-list="sortableChapterList"
        :comics-id="comicsId"
        :pagination="pagination"
        :is-sort-mode="isSortMode"
        @update:sortable-chapter-list="sortableChapterList = $event"
        @size-change="onSizeChange"
        @page-change="onPageChange"
        @order-change="isOrderChanged = $event"
        @edit="onEdit"
        @refresh="fetchList"
      />

      <!-- 章节表单对话框 -->
      <ChapterForm
        :comics-id="comicsId"
        :visible="dialogVisible"
        :dialog-type="dialogType"
        :edit-data="currentEditData"
        @update:visible="dialogVisible = $event"
        @success="onFormSuccess"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import { getComicsChapterList, updateComicsChapter, batchUpdateChaptersOrder } from '@/service/api/comics/comics';
import type { ComicsChapter, ComicsParams } from '@/types/comics';
import ChapterList from './components/ChapterList.vue';
import ChapterForm from './components/ChapterForm.vue';

// 获取漫画ID
const route = useRoute();
const comicsId = route.params.comic_id as string;
console.log("comicsId:",comicsId)
// 加载状态
const loading = ref(false);

// 章节列表
const chapterList = ref<ComicsChapter[]>([]);
const sortableChapterList = ref<ComicsChapter[]>([]);

// 分页
const pagination = reactive({ page: 1, pageSize: 10, total: 0 });

// 搜索表单
const searchForm = reactive({ title: '', status: undefined });

// 对话框状态
const dialogVisible = ref(false);
const dialogType = ref<'add'|'edit'>('add');
const currentEditData = ref<ComicsChapter | undefined>(undefined);

// 排序模式
const isSortMode = ref(false);
const isOrderChanged = ref(false);

// 获取章节列表数据
const fetchList = async () => {
  loading.value = true;
  try {
    const params: ComicsParams = {
      page: { pageNo: pagination.page, pageSize: pagination.pageSize },
      data: { keyword: searchForm.title, status: searchForm.status }
    };
    const res = await getComicsChapterList(comicsId, params) as any;
    const {response, data} = res as any;
    if (response.data.code === 2000) {
      chapterList.value = data.list;
      pagination.total = data.total;
    }
  } finally {
    loading.value = false;
  }
};

// 改进排序模式切换函数
const toggleSortMode = async () => {
  loading.value = true;
  try {
    // 先获取数据，成功后再切换模式
    const params: ComicsParams = {
      page: { pageNo: 1, pageSize: 999 }, // 获取所有章节
      data: { status: 1 } // 只排序启用的章节
    };
    const res = await getComicsChapterList(comicsId, params) as any;
    const {response, data} = res as any;
    if (response.data.code === 2000) {
      // 按章节序号排序
      sortableChapterList.value = [...data.list].sort((a, b) => a.chapter_number - b.chapter_number);
      isOrderChanged.value = false;

      // 切换到排序模式
      isSortMode.value = true;
    } else {
      ElMessage.error('获取章节数据失败');
    }
  } catch (error) {
    console.error('获取章节数据出错:', error);
    ElMessage.error('获取章节数据时出错');
  } finally {
    loading.value = false;
  }
};

// 取消排序模式
const cancelSortMode = () => {
  if (isOrderChanged.value) {
    ElMessageBox.confirm('您已经修改了章节顺序，确定要放弃这些更改吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      isSortMode.value = false;
      fetchList();
    }).catch(() => {});
  } else {
    isSortMode.value = false;
    fetchList();
  }
};

// 修改保存排序函数
const saveSortOrder = async () => {
  if (!isOrderChanged.value) {
    ElMessage.info('章节顺序未发生变化');
    isSortMode.value = false;
    return;
  }

  loading.value = true;
  try {
    // 准备章节排序数据
    const chaptersData = sortableChapterList.value.map((chapter, index) => ({
      id: chapter.id,
      chapter_number: index + 1
    }));

    // 使用批量更新API
    const res = await batchUpdateChaptersOrder(comicsId, chaptersData) as any;
    const { response } = res as any;

    if (response.data.code === 2000) {
      ElMessage.success('章节排序已保存');
      isSortMode.value = false;
      fetchList();
    } else {
      ElMessage.error(response?.data?.message || '保存排序失败');
    }
  } catch (error) {
    ElMessage.error('保存排序时出错');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 搜索操作
const onSearch = () => {
  pagination.page = 1;
  fetchList();
};

// 重置搜索
const onReset = () => {
  searchForm.title = '';
  searchForm.status = undefined;
  onSearch();
};

// 分页操作
const onPageChange = (page: number) => {
  pagination.page = page;
  fetchList();
};

const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchList();
};

// 添加章节
const onAdd = () => {
  dialogType.value = 'add';
  currentEditData.value = undefined;
  dialogVisible.value = true;
};

// 编辑章节
const onEdit = (row: ComicsChapter) => {
  dialogType.value = 'edit';
  currentEditData.value = row;
  dialogVisible.value = true;
};

// 表单提交成功处理
const onFormSuccess = () => {
  fetchList();
};

// 初始化
onMounted(() => {
  fetchList();
});
</script>

<style scoped lang="scss">
.chapter-list {
  width: 100%;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.search-form {
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
}
</style>
