/**
 * 格式化工具函数
 * 统一所有格式化相关功能
 */

/**
 * 数字格式化选项
 */
export interface NumberFormatOptions {
  locale?: string
  style?: 'decimal' | 'currency' | 'percent' | 'unit'
  currency?: string
  unit?: string
  minimumFractionDigits?: number
  maximumFractionDigits?: number
  minimumIntegerDigits?: number
  useGrouping?: boolean
  notation?: 'standard' | 'scientific' | 'engineering' | 'compact'
  compactDisplay?: 'short' | 'long'
  signDisplay?: 'auto' | 'never' | 'always' | 'exceptZero'
}

/**
 * 日期格式化选项
 */
export interface DateFormatOptions {
  format?: string
  locale?: string
  timezone?: string
}

/**
 * 文件大小单位
 */
export type FileSizeUnit = 'B' | 'KB' | 'MB' | 'GB' | 'TB' | 'PB'

/**
 * 格式化数字
 * @param num 数字
 * @param options 格式化选项
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number, options: NumberFormatOptions = {}): string {
  if (!isFinite(num)) {
    return 'NaN'
  }

  const {
    locale = 'zh-CN',
    style = 'decimal',
    ...formatOptions
  } = options

  try {
    return new Intl.NumberFormat(locale, {
      style,
      ...formatOptions
    }).format(num)
  } catch (error) {
    return num.toString()
  }
}

/**
 * 格式化计数（简化版数字格式化）
 * @param count 计数
 * @returns 格式化后的字符串
 */
export function formatCount(count: number): string {
  if (count < 1000) return count.toString()
  if (count < 10000) return `${(count / 1000).toFixed(1)}k`
  if (count < 1000000) return `${(count / 10000).toFixed(1)}w`
  return `${(count / 1000000).toFixed(1)}M`
}

/**
 * 视频时长格式化选项
 */
export interface DurationFormatOptions {
  format?: 'short' | 'long' | 'chinese' // 短格式(1:23)、长格式(1分23秒)、中文格式
  showHours?: boolean // 是否强制显示小时
  locale?: string // 语言环境
}

/**
 * 格式化视频时长
 * @param seconds 秒数
 * @param options 格式化选项
 * @returns 格式化后的时长字符串
 */
export function formatDuration(seconds: number, options: DurationFormatOptions = {}): string {
  if (!isFinite(seconds) || seconds < 0) {
    return options.format === 'chinese' ? '0秒' : '00:00'
  }

  const {
    format = 'short',
    showHours = false,
    locale = 'zh-CN'
  } = options

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  switch (format) {
    case 'chinese':
      if (hours > 0) {
        return `${hours}小时${minutes}分${secs}秒`
      }
      if (minutes > 0) {
        return `${minutes}分${secs}秒`
      }
      return `${secs}秒`
      
    case 'long':
      if (locale.startsWith('zh')) {
        if (hours > 0) {
          return `${hours}小时${minutes}分钟${secs}秒`
        }
        if (minutes > 0) {
          return `${minutes}分钟${secs}秒`
        }
        return `${secs}秒`
      } else {
        if (hours > 0) {
          return `${hours}h ${minutes}m ${secs}s`
        }
        if (minutes > 0) {
          return `${minutes}m ${secs}s`
        }
        return `${secs}s`
      }
      
    case 'short':
    default:
      if (hours > 0 || showHours) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

/**
 * 格式化日期
 * @param date 日期
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | string | number, options: DateFormatOptions = {}): string {
  const {
    format = 'YYYY-MM-DD HH:mm:ss',
    locale = 'zh-CN',
    timezone
  } = options
  
  const d = new Date(date)
  
  if (isNaN(d.getTime())) {
    return 'Invalid Date'
  }
  
  // 使用Intl.DateTimeFormat进行本地化格式化
  if (format === 'locale') {
    return new Intl.DateTimeFormat(locale, {
      timeZone: timezone
    }).format(d)
  }
  
  // 自定义格式化
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  const milliseconds = String(d.getMilliseconds()).padStart(3, '0')
  
  return format
    .replace(/YYYY/g, String(year))
    .replace(/YY/g, String(year).slice(-2))
    .replace(/MM/g, month)
    .replace(/M/g, String(d.getMonth() + 1))
    .replace(/DD/g, day)
    .replace(/D/g, String(d.getDate()))
    .replace(/HH/g, hours)
    .replace(/H/g, String(d.getHours()))
    .replace(/mm/g, minutes)
    .replace(/m/g, String(d.getMinutes()))
    .replace(/ss/g, seconds)
    .replace(/s/g, String(d.getSeconds()))
    .replace(/SSS/g, milliseconds)
}

/**
 * 发布时间格式化选项
 */
export interface PublishTimeFormatOptions {
  locale?: string
  showFullDateAfter?: number // 超过多少天后显示完整日期（默认30天）
  includeYear?: boolean // 是否包含年份
}

/**
 * 格式化发布时间（超过一个月显示具体时间）
 * @param dateString 日期字符串
 * @param options 格式化选项
 * @returns 发布时间字符串
 */
export function formatPublishTime(dateString: string | Date, options: PublishTimeFormatOptions = {}): string {
  const {
    locale = 'zh-CN',
    showFullDateAfter = 30, // 30天后显示完整日期
    includeYear = true
  } = options
  
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (isNaN(date.getTime())) {
    return 'Invalid Date'
  }
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  // 超过指定天数后显示具体日期
  if (days > showFullDateAfter) {
    const currentYear = now.getFullYear()
    const dateYear = date.getFullYear()
    
    if (includeYear || currentYear !== dateYear) {
      return date.toLocaleDateString(locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } else {
      return date.toLocaleDateString(locale, {
        month: 'short',
        day: 'numeric'
      })
    }
  }
  
  // 相对时间显示
  if (minutes < 1) return locale.startsWith('zh') ? '刚刚' : 'just now'
  if (minutes < 60) return locale.startsWith('zh') ? `${minutes}分钟前` : `${minutes} minutes ago`
  if (hours < 24) return locale.startsWith('zh') ? `${hours}小时前` : `${hours} hours ago`
  if (days < 7) return locale.startsWith('zh') ? `${days}天前` : `${days} days ago`
  if (days < 30) {
    const weeks = Math.floor(days / 7)
    return locale.startsWith('zh') ? `${weeks}周前` : `${weeks} week${weeks > 1 ? 's' : ''} ago`
  }
  
  return date.toLocaleDateString(locale, {
    month: 'short',
    day: 'numeric'
  })
}

/**
 * 格式化相对时间（兼容旧版本）
 * @param dateString 日期字符串
 * @param locale 语言环境
 * @returns 相对时间字符串
 */
export function formatTime(dateString: string, locale = 'zh-CN'): string {
  return formatPublishTime(dateString, { locale })
}

/**
 * 格式化相对时间（更完整的版本）
 * @param date 日期
 * @param baseDate 基准日期
 * @param locale 语言环境
 * @returns 相对时间字符串
 */
export function formatTimeAgo(
  date: Date | string | number,
  baseDate: Date | string | number = new Date(),
  locale = 'zh-CN'
): string {
  const d = new Date(date)
  const base = new Date(baseDate)
  
  if (isNaN(d.getTime()) || isNaN(base.getTime())) {
    return 'Invalid Date'
  }
  
  const diff = base.getTime() - d.getTime()
  const absDiff = Math.abs(diff)
  const isPast = diff > 0
  
  const units = [
    { unit: 'year', ms: 365 * 24 * 60 * 60 * 1000 },
    { unit: 'month', ms: 30 * 24 * 60 * 60 * 1000 },
    { unit: 'week', ms: 7 * 24 * 60 * 60 * 1000 },
    { unit: 'day', ms: 24 * 60 * 60 * 1000 },
    { unit: 'hour', ms: 60 * 60 * 1000 },
    { unit: 'minute', ms: 60 * 1000 },
    { unit: 'second', ms: 1000 }
  ]
  
  for (const { unit, ms } of units) {
    const value = Math.floor(absDiff / ms)
    if (value >= 1) {
      if (Intl.RelativeTimeFormat) {
        const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
        return rtf.format(isPast ? -value : value, unit as Intl.RelativeTimeFormatUnit)
      }
      // 降级处理
      return isPast 
        ? (locale.startsWith('zh') ? `${value}${unit === 'year' ? '年' : unit === 'month' ? '月' : unit === 'day' ? '天' : unit === 'hour' ? '小时' : unit === 'minute' ? '分钟' : '秒'}前` : `${value} ${unit}${value > 1 ? 's' : ''} ago`)
        : (locale.startsWith('zh') ? `${value}${unit === 'year' ? '年' : unit === 'month' ? '月' : unit === 'day' ? '天' : unit === 'hour' ? '小时' : unit === 'minute' ? '分钟' : '秒'}后` : `in ${value} ${unit}${value > 1 ? 's' : ''}`)
    }
  }
  
  return locale.startsWith('zh') ? '刚刚' : 'just now'
}

/**
 * 文章章节格式化选项
 */
export interface ChapterFormatOptions {
  locale?: string
  style?: 'number' | 'chinese' | 'roman' | 'letter' // 数字、中文、罗马数字、字母
  prefix?: string // 前缀
  suffix?: string // 后缀
  showTotal?: boolean // 是否显示总数
}

/**
 * 格式化文章章节（多语言支持）
 * @param chapterNumber 章节号
 * @param options 格式化选项或语言代码（兼容旧版本）
 * @param totalChapters 总章节数
 * @returns 格式化后的章节字符串
 */
export function formatChapter(
  chapterNumber: number,
  options: ChapterFormatOptions | string = {},
  totalChapters?: number
): string {
  // 兼容旧版本：如果第二个参数是字符串，则作为locale处理
  let actualOptions: ChapterFormatOptions
  let actualTotalChapters: number | undefined
  
  if (typeof options === 'string') {
    actualOptions = { locale: options }
    actualTotalChapters = totalChapters
  } else {
    actualOptions = options
    actualTotalChapters = totalChapters
  }
  
  const {
    locale = 'zh-CN',
    style = 'number',
    prefix = '',
    suffix = '',
    showTotal = false
  } = actualOptions
  
  if (!isFinite(chapterNumber) || chapterNumber < 1) {
    return 'Invalid Chapter'
  }
  
  let formattedNumber: string
  
  switch (style) {
    case 'chinese':
      formattedNumber = convertToChineseNumber(chapterNumber)
      break
    case 'roman':
      formattedNumber = convertToRomanNumber(chapterNumber)
      break
    case 'letter':
      formattedNumber = convertToLetterNumber(chapterNumber)
      break
    case 'number':
    default:
      formattedNumber = chapterNumber.toString()
      break
  }
  
  // 构建章节字符串
  let result = `${prefix}${formattedNumber}${suffix}`
  
  // 添加本地化前缀
  if (locale.startsWith('zh')) {
    result = `第${result}章`
  } else if (locale.startsWith('ja')) {
    result = `第${result}章`
  } else if (locale.startsWith('ko')) {
    result = `제${result}장`
  } else {
    result = `Chapter ${result}`
  }
  
  // 添加总数信息
  if (showTotal && actualTotalChapters && actualTotalChapters > 0) {
    if (locale.startsWith('zh')) {
      result += ` (共${actualTotalChapters}章)`
    } else if (locale.startsWith('ja')) {
      result += ` (全${actualTotalChapters}章)`
    } else if (locale.startsWith('ko')) {
      result += ` (총 ${actualTotalChapters}장)`
    } else {
      result += ` (${chapterNumber}/${actualTotalChapters})`
    }
  }
  
  return result
}

/**
 * 转换为中文数字
 * @param num 数字
 * @returns 中文数字字符串
 */
function convertToChineseNumber(num: number): string {
  const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const units = ['', '十', '百', '千', '万']
  
  if (num === 0) return chineseNumbers[0]
  if (num < 10) return chineseNumbers[num]
  if (num < 20) return num === 10 ? '十' : `十${chineseNumbers[num % 10]}`
  if (num < 100) {
    const tens = Math.floor(num / 10)
    const ones = num % 10
    return ones === 0 ? `${chineseNumbers[tens]}十` : `${chineseNumbers[tens]}十${chineseNumbers[ones]}`
  }
  
  // 简化处理，仅支持到999
  return num.toString()
}

/**
 * 转换为罗马数字
 * @param num 数字
 * @returns 罗马数字字符串
 */
function convertToRomanNumber(num: number): string {
  const romanNumerals = [
    { value: 1000, symbol: 'M' },
    { value: 900, symbol: 'CM' },
    { value: 500, symbol: 'D' },
    { value: 400, symbol: 'CD' },
    { value: 100, symbol: 'C' },
    { value: 90, symbol: 'XC' },
    { value: 50, symbol: 'L' },
    { value: 40, symbol: 'XL' },
    { value: 10, symbol: 'X' },
    { value: 9, symbol: 'IX' },
    { value: 5, symbol: 'V' },
    { value: 4, symbol: 'IV' },
    { value: 1, symbol: 'I' }
  ]
  
  let result = ''
  for (const { value, symbol } of romanNumerals) {
    while (num >= value) {
      result += symbol
      num -= value
    }
  }
  return result
}

/**
 * 转换为字母编号
 * @param num 数字
 * @returns 字母编号字符串
 */
function convertToLetterNumber(num: number): string {
  let result = ''
  while (num > 0) {
    num--
    result = String.fromCharCode(65 + (num % 26)) + result
    num = Math.floor(num / 26)
  }
  return result
}

// formatFileSize函数已移至file.ts，请从那里导入使用

/**
 * 格式化货币
 * @param value 数值
 * @param currency 货币代码
 * @param locale 语言环境
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(value: number, currency = 'CNY', locale = 'zh-CN'): string {
  return formatNumber(value, {
    style: 'currency',
    currency,
    locale
  })
}

/**
 * 格式化百分比
 * @param value 数值（0-1之间）
 * @param decimals 小数位数
 * @param locale 语言环境
 * @returns 格式化后的百分比字符串
 */
export function formatPercent(value: number, decimals = 2, locale = 'zh-CN'): string {
  return formatNumber(value, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    locale
  })
}

/**
 * 计算热度等级（1-5级）
 * @param totalEngagement 总互动数
 * @param views 观看数
 * @returns 热度等级
 */
export function getHeatLevel(totalEngagement: number, views: number): number {
  const engagementRate = views > 0 ? (totalEngagement / views) * 100 : 0
  
  if (engagementRate >= 20) return 5
  if (engagementRate >= 15) return 4
  if (engagementRate >= 10) return 3
  if (engagementRate >= 5) return 2
  return 1
}

/**
 * 获取热度颜色
 * @param totalEngagement 总互动数
 * @param views 观看数
 * @param level 等级
 * @returns 颜色值
 */
export function getHeatColor(totalEngagement: number, views: number, level: number): string {
  const currentLevel = getHeatLevel(totalEngagement, views)
  if (level > currentLevel) return '#e2e8f0' // 灰色
  
  const colors = [
    '#3b82f6', // 蓝色 - 1级
    '#06b6d4', // 青色 - 2级  
    '#10b981', // 绿色 - 3级
    '#f59e0b', // 橙色 - 4级
    '#ef4444'  // 红色 - 5级
  ]
  return colors[level - 1] || colors[0]
}

/**
 * 格式化播放速度
 * @param speed 播放速度
 * @param locale 语言环境
 * @returns 格式化后的播放速度字符串
 */
export function formatPlaybackSpeed(speed: number, locale = 'zh-CN'): string {
  if (speed === 1) {
    return locale.startsWith('zh') ? '正常' : 'Normal'
  }
  return `${speed}x`
}

/**
 * 格式化视频质量
 * @param quality 视频质量
 * @param locale 语言环境
 * @returns 格式化后的视频质量字符串
 */
export function formatVideoQuality(quality: string | number, locale = 'zh-CN'): string {
  const qualityMap: Record<string, { zh: string; en: string }> = {
    '144': { zh: '流畅', en: 'Smooth' },
    '240': { zh: '标清', en: 'SD' },
    '360': { zh: '清晰', en: 'Clear' },
    '480': { zh: '高清', en: 'HD' },
    '720': { zh: '超清', en: 'HD' },
    '1080': { zh: '蓝光', en: 'Full HD' },
    '1440': { zh: '2K', en: '2K' },
    '2160': { zh: '4K', en: '4K' },
    'auto': { zh: '自动', en: 'Auto' }
  }
  
  const key = quality.toString()
  const mapping = qualityMap[key]
  
  if (mapping) {
    return locale.startsWith('zh') ? mapping.zh : mapping.en
  }
  
  return `${quality}p`
}