<template>
  <div class="channel-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量禁用
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量启用
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 - 使用SlinkyTable -->
    <div class="table-content">
      <SlinkyTable
        :data="channelList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="编辑"
        delete-text="删除"
        empty-text="暂无频道数据"
        class="channel-data-table"
      >
        <!-- 频道信息列 -->
        <el-table-column prop="name" label="频道信息" align="left" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="channel-info">
              <el-avatar
                :size="36"
                :src="row.icon"
                :alt="row.name"
                class="channel-avatar"
              >
                <el-icon><VideoCamera /></el-icon>
              </el-avatar>
              <div class="channel-details">
                <div class="channel-name">{{ row.name }}</div>
                <div class="channel-description">{{ row.description || '未设置描述' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 频道编码列 -->
        <el-table-column prop="code" label="频道编码" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.code" type="info" size="small" effect="light">
              {{ row.code }}
            </el-tag>
            <span v-else class="placeholder-text">未设置</span>
          </template>
        </el-table-column>

        <!-- 更新频率列 -->
        <el-table-column prop="update_frequency" label="更新频率" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              v-if="row.update_frequency" 
              :type="getFrequencyTagType(row.update_frequency)" 
              size="small" 
              effect="light"
            >
              {{ getFrequencyText(row.update_frequency) }}
            </el-tag>
            <span v-else class="placeholder-text">未设置</span>
          </template>
        </el-table-column>

        <!-- 统计信息列 -->
        <el-table-column label="统计" min-width="140" align="center">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon class="stat-icon"><VideoCamera /></el-icon>
                <span class="stat-value">{{ row.video_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><View /></el-icon>
                <span class="stat-value">{{ formatNumber(row.view_count || 0) }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><User /></el-icon>
                <span class="stat-value">{{ formatNumber(row.subscriber_count || 0) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 推荐状态列 -->
        <el-table-column prop="is_featured" label="推荐" width="80" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.is_featured === 1 ? 'success' : 'info'" 
              size="small" 
              effect="light"
            >
              {{ row.is_featured === 1 ? '推荐' : '普通' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <ChannelStatusTag :status="row.status" />
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ formatDate(row.created_at) }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-popconfirm
              :title="`确定要${row.status === 1 ? '禁用' : '启用'}频道 ${row.name} 吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  link 
                  size="small"
                >
                  <el-icon>
                    <component :is="row.status === 1 ? Lock : Unlock" />
                  </el-icon>
                  {{ row.status === 1 ? '禁用' : row.status === 0 ? '启用' : '已删除' }}
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              :title="`确定要删除频道 ${row.name} 吗？此操作不可恢复！`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页组件 -->
    <div class="table-footer">
      <SinglePager
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import type { VideoChannelItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import {
    Calendar,
    Check,
    Delete, Edit, Lock, Unlock,
    User,
    VideoCamera,
    View
} from '@element-plus/icons-vue';
import { ref } from 'vue';
import ChannelStatusTag from './ChannelStatusTag.vue';

// Props定义
interface Props {
  channelList: VideoChannelItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'selection-change': [selection: VideoChannelItem[]];
  'view': [row: VideoChannelItem];
  'edit': [row: VideoChannelItem];
  'delete': [row: VideoChannelItem];
  'change-status': [id: string, status: number];
  'current-change': [page: number];
  'size-change': [size: number];
  'batch-status': [status: number, channels: VideoChannelItem[]];
  'batch-delete': [channels: VideoChannelItem[]];
}

const emit = defineEmits<Emits>();

// 选中的行
const selectedRows = ref<VideoChannelItem[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: VideoChannelItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

// 查看
const handleView = (row: VideoChannelItem) => {
  emit('view', row);
};

// 编辑
const handleEdit = (row: VideoChannelItem) => {
  emit('edit', row);
};

// 删除
const handleDelete = (row: VideoChannelItem) => {
  emit('delete', row);
};

// 状态变化
const handleChangeStatus = (id: string, status: number) => {
  emit('change-status', id, status);
};

// 分页变化
const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 批量状态更新
const handleBatchStatus = (status: number) => {
  emit('batch-status', status, selectedRows.value);
};

// 批量删除
const handleBatchDelete = () => {
  emit('batch-delete', selectedRows.value);
};

// 工具函数
const getFrequencyTagType = (frequency: string): 'danger' | 'warning' | 'success' | 'info' => {
  const typeMap: Record<string, 'danger' | 'warning' | 'success' | 'info'> = {
    'daily': 'danger',
    'weekly': 'warning', 
    'monthly': 'success',
    'irregular': 'info'
  };
  return typeMap[frequency] || 'info';
};

const getFrequencyText = (frequency: string) => {
  const textMap: Record<string, string> = {
    'daily': '每日',
    'weekly': '每周',
    'monthly': '每月',
    'irregular': '不定期'
  };
  return textMap[frequency] || frequency;
};

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};
</script>

<style scoped lang="scss">
.channel-table-container {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-weight: 500;

      strong {
        color: #1565c0;
      }
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-content {
    background: white;
    border-radius: 4px;
    overflow: hidden;
  }

  .channel-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 4px 0;

    .channel-avatar {
      flex-shrink: 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .channel-details {
      min-width: 0;
      flex: 1;

      .channel-name {
        font-weight: 500;
        color: #333;
        line-height: 1.4;
        margin-bottom: 2px;
      }

      .channel-description {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .stats-info {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-direction: column;

      .stat-icon {
        color: #999;
        font-size: 14px;
      }

      .stat-value {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .time-icon {
      color: #999;
      font-size: 14px;
    }

    .time-text {
      font-size: 12px;
      color: #666;
    }
  }

  .action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      
      .el-icon {
        margin-right: 2px;
      }
    }
  }

  .placeholder-text {
    color: #999;
    font-size: 12px;
    font-style: italic;
  }

  .table-footer {
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-lighter);
  } 
}

// 响应式设计
@media (max-width: 768px) {
  .channel-table-container {
    .batch-toolbar {
      flex-direction: column;
      gap: 12px;
      text-align: center;

      .batch-actions {
        justify-content: center;
      }
    }

    .stats-info {
      gap: 8px;

      .stat-item {
        font-size: 11px;
      }
    }
  }
}
</style> 