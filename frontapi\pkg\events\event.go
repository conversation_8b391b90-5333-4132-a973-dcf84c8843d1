package events

import (
	"sync"
)

// Event 事件类型
type Event struct {
	Type    string                 // 事件类型
	Payload map[string]interface{} // 事件数据
}

// EventHandler 事件处理函数
type EventHandler func(Event)

// EventBus 事件总线
type EventBus struct {
	subscribers map[string][]EventHandler
	mutex       sync.RWMutex
}

// NewEventBus 创建新的事件总线
func NewEventBus() *EventBus {
	return &EventBus{
		subscribers: make(map[string][]EventHandler),
	}
}

// Subscribe 订阅事件
func (bus *EventBus) Subscribe(eventType string, handler EventHandler) {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	bus.subscribers[eventType] = append(bus.subscribers[eventType], handler)
}

// Publish 发布事件
func (bus *EventBus) Publish(event Event) {
	bus.mutex.RLock()
	defer bus.mutex.RUnlock()

	if handlers, exists := bus.subscribers[event.Type]; exists {
		for _, handler := range handlers {
			go handler(event)
		}
	}
}

// Unsubscribe 取消订阅
func (bus *EventBus) Unsubscribe(eventType string, handler EventHandler) {
	bus.mutex.Lock()
	defer bus.mutex.Unlock()

	if handlers, exists := bus.subscribers[eventType]; exists {
		for i, h := range handlers {
			if &h == &handler {
				bus.subscribers[eventType] = append(handlers[:i], handlers[i+1:]...)
				break
			}
		}
	}
}

// 预定义事件类型常量
const (
	// 帖子相关事件
	EventPostCreated = "post.created"
	EventPostUpdated = "post.updated"
	EventPostDeleted = "post.deleted"
	EventPostLiked   = "post.liked"

	// 评论相关事件
	EventCommentCreated = "comment.created"
	EventCommentDeleted = "comment.deleted"
	EventCommentLiked   = "comment.liked"

	// 视频相关事件
	EventVideoViewed = "video.viewed"
	EventVideoLiked  = "video.liked"

	// 用户相关事件
	EventUserRegistered = "user.registered"
	EventUserLoggedIn   = "user.logged_in"
	EventUserLoggedOut  = "user.logged_out"
)

// GlobalEventBus 全局事件总线
var GlobalEventBus = NewEventBus()
