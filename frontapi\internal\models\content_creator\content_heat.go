package content_creator

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type ContentHeat struct {
	models.BaseModel
	ContentType     string         `gorm:"column:content_type;type:string;not null;comment:内容类型" json:"content_type"`                                // 内容类型
	ContentID       string         `gorm:"column:content_id;type:string;not null;comment:内容ID" json:"content_id"`                                    // 内容ID
	PeriodType      string         `gorm:"column:period_type;type:string;not null;default:'all';comment:周期类型" json:"period_type"`                    // 周期类型
	PeriodDate      types.JSONTime `gorm:"column:period_date;type:date;comment:周期日期" json:"period_date"`                                             // 周期日期
	HeatScore       float64        `gorm:"column:heat_score;type:decimal(10,2);not null;default:0.00;comment:热度分数" json:"heat_score"`                // 热度分数
	ViewWeight      float64        `gorm:"column:view_weight;type:decimal(5,2);not null;default:1.00;comment:观看权重" json:"view_weight"`               // 观看权重
	LikeWeight      float64        `gorm:"column:like_weight;type:decimal(5,2);not null;default:3.00;comment:点赞权重" json:"like_weight"`               // 点赞权重
	CommentWeight   float64        `gorm:"column:comment_weight;type:decimal(5,2);not null;default:5.00;comment:评论权重" json:"comment_weight"`         // 评论权重
	ShareWeight     float64        `gorm:"column:share_weight;type:decimal(5,2);not null;default:8.00;comment:分享权重" json:"share_weight"`             // 分享权重
	FavoriteWeight  float64        `gorm:"column:favorite_weight;type:decimal(5,2);not null;default:3.00;comment:收藏权重" json:"favorite_weight"`       // 收藏权重
	WatchTimeWeight float64        `gorm:"column:watch_time_weight;type:decimal(5,2);not null;default:2.00;comment:观看时长权重" json:"watch_time_weight"` // 观看时长权重
	Rank            *int           `gorm:"column:rank;type:int;comment:排名" json:"rank"`                                                              // 排名
}

func (ContentHeat) TableName() string {
	return "ly_content_heat"
}
