package base

import (
	"context"
	"errors"
	"fmt"
	"time"

	"frontapi/internal/hooks"
	"frontapi/internal/models"
	base "frontapi/internal/repository/base"
	"frontapi/pkg/redis"
	"frontapi/pkg/types"
)

// 使用repository包中的ExtendedRepository接口，不再重复定义

// IExtendedService 扩展服务接口
type IExtendedService[T models.BaseModelConstraint] interface {
	// 继承基础服务接口
	IBaseService[T]

	// 扩展功能
	UpdateViewCount(ctx context.Context, id string) error
	UpdateLikeCount(ctx context.Context, id string, increment int) error
	UpdateCommentCount(ctx context.Context, id string, increment int) error
	SoftDelete(ctx context.Context, id string) error
	BatchSoftDelete(ctx context.Context, ids []string) error
	Restore(ctx context.Context, id string) error
	UpdateSortOrder(ctx context.Context, id string, sortOrder int) error
	ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)

	BatchUpdateColumn(ctx context.Context, ids []string, columns map[string]interface{}) error
	GetEntityType() string
}

// ExtendedService 扩展服务结构
// 使用嵌入（embedding）而不是组合，自动获得BaseService的所有方法
type ExtendedService[T models.BaseModelConstraint] struct {
	*BaseService[T]                            // 嵌入BaseService，自动获得所有方法
	extendedRepo    base.ExtendedRepository[T] // 扩展仓库
	entityType      string                     // 实体类型，用于缓存键前缀
}

// NewExtendedService 创建扩展服务实例
// NewExtendedService 创建扩展服务实例
// 参数:
//   - repo: 扩展仓储接口实例
//   - entityType: 实体类型标识
//
// 返回:
//   - *ExtendedService[T]: 扩展服务实例
func NewExtendedService[T models.BaseModelConstraint](
	repo base.ExtendedRepository[T],
	entityType string,
) *ExtendedService[T] {
	return &ExtendedService[T]{
		BaseService:  NewBaseService[T](repo, entityType),
		extendedRepo: repo,
		entityType:   entityType,
	}
}

// getCacheKey 重写缓存键生成方法
func (s *ExtendedService[T]) getCacheKey(id string) string {
	return BuildCacheKey(s.entityType, id)
}

// ClearCacheByPattern 根据模式清除缓存
func (s *ExtendedService[T]) ClearCacheByPattern(pattern string) {
	// 这里需要实现根据模式删除缓存的逻辑
	// 暂时留空，具体实现依赖于Redis的SCAN命令
	_ = pattern
}

// GetEntityType 获取实体类型
func (s *ExtendedService[T]) GetEntityType() string {
	return s.entityType
}

// GetHookManager 获取钩子管理器
func (s *ExtendedService[T]) GetHookManager() *hooks.ServiceHookManager {
	return s.BaseService.hookManager
}

// RegisterHook 注册钩子
func (s *ExtendedService[T]) RegisterHook(hookType hooks.HookType, hookFunc hooks.HookFunc) {
	s.BaseService.hookManager.RegisterCustomHook(hookType, "custom", "custom hook", 50, hookFunc)
}

// 通过嵌入BaseService，其他BaseService的方法都自动可用，无需手动委托

// ExtendedService特有的方法，直接调用ExtendedRepository
func (s *ExtendedService[T]) UpdateViewCount(ctx context.Context, id string) error {
	return s.extendedRepo.UpdateViewCount(ctx, id)
}

func (s *ExtendedService[T]) UpdateLikeCount(ctx context.Context, id string, increment int) error {
	return s.extendedRepo.UpdateLikeCount(ctx, id, increment)
}

func (s *ExtendedService[T]) UpdateCommentCount(ctx context.Context, id string, increment int) error {
	return s.extendedRepo.UpdateCommentCount(ctx, id, increment)
}

// SoftDelete 软删除实体
func (s *ExtendedService[T]) SoftDelete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 先获取实体用于钩子
	entity, err := s.extendedRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取实体失败: %w", err)
	}

	// 执行删除前钩子
	if err := s.BaseService.hookManager.ExecuteHooks(ctx, hooks.BeforeDelete, entity); err != nil {
		return fmt.Errorf("软删除前钩子执行失败: %w", err)
	}

	// 设置删除时间和删除状态
	now := types.JSONTime(time.Now())
	if ptr, ok := any(entity).(interface{ SetDeletedAt(*types.JSONTime) }); ok {
		ptr.SetDeletedAt(&now)
	}

	// 更新实体
	err = s.extendedRepo.SoftDelete(ctx, id)
	if err != nil {
		return fmt.Errorf("软删除失败: %w", err)
	}

	// 删除缓存
	s.deleteCacheByID(id)

	// 执行删除后钩子
	if err := s.BaseService.hookManager.ExecuteHooks(ctx, hooks.AfterDelete, entity); err != nil {
		// 删除后钩子失败只记录日志，不影响删除结果
		// 这里可以添加日志记录
	}

	return nil
}

func (s *ExtendedService[T]) BatchSoftDelete(ctx context.Context, ids []string) error {
	return s.extendedRepo.BatchSoftDelete(ctx, ids)
}

func (s *ExtendedService[T]) Restore(ctx context.Context, id string) error {
	return s.extendedRepo.Restore(ctx, id)
}

func (s *ExtendedService[T]) UpdateSortOrder(ctx context.Context, id string, sortOrder int) error {
	return s.extendedRepo.UpdateSortOrder(ctx, id, sortOrder)
}

func (s *ExtendedService[T]) BatchUpdateColumn(ctx context.Context, ids []string, columns map[string]interface{}) error {
	return s.extendedRepo.BatchUpdateColumn(ctx, ids, columns)
}

// List 重写基础服务的List方法，使用扩展仓库以支持自定义条件应用
func (s *ExtendedService[T]) List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := BuildCacheKey("entity", fmt.Sprintf("list_%v_%s_%d_%d", condition, orderBy, page, pageSize))
		var result struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}
		if redis.GetJSON(cacheKey, &result) == nil {
			return result.Items, result.Total, nil
		}
	}

	// 使用扩展仓库的List方法，确保能够使用自定义的条件应用逻辑
	items, total, err := s.extendedRepo.List(ctx, condition, orderBy, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取列表失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := BuildCacheKey("entity", fmt.Sprintf("list_%v_%s_%d_%d", condition, orderBy, page, pageSize))
		result := struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}{
			Items: items,
			Total: total,
		}
		redis.SetJSON(cacheKey, result, s.BaseService.cacheTTL)
	}

	return items, total, nil
}

func (s *ExtendedService[T]) ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error) {
	return s.extendedRepo.ListWithPreload(ctx, preloads, condition, orderBy, page, pageSize)
}
