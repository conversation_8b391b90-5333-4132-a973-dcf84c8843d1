# 随机图片生成功能测试说明

## 功能概述

已在媒体服务器中添加了随机图片生成功能，从真实的图片API服务中获取高质量的随机图片，支持动态生成指定尺寸的测试图片。

## 路由格式

```
GET /image/random/:width/:height/:filename
```

## 参数说明

- `width`: 图片宽度 (1-2000像素)
- `height`: 图片高度 (1-2000像素)  
- `filename`: 文件名 (支持.png, .jpg, .jpeg格式)

## 测试URL示例

```
http://localhost:8082/image/random/800/600/test.jpg
http://localhost:8082/image/random/400/300/sample.png
http://localhost:8082/image/random/1200/800/demo.jpeg
http://localhost:8082/image/random/500/500/square.jpg
http://localhost:8082/image/random/1920/1080/wallpaper.jpg
```

## 启动媒体服务器

```bash
# 仅启动媒体服务器
./bin/frontapi.exe -api=false -admin=false -media=true

# 或者启动所有服务
./bin/frontapi.exe
```

## 图片来源API

系统会随机从以下高质量图片API中获取图片：

1. **Picsum Photos** (https://picsum.photos)
   - 提供高质量的摄影作品
   - 支持任意尺寸
   - 每次请求都会返回不同的图片

2. **Lorem Flickr** (https://loremflickr.com)
   - 基于Flickr的真实照片
   - 支持主题分类（自然、城市、科技、食物、动物等）
   - 图片质量优秀

3. **Via Placeholder** (https://via.placeholder.com)
   - 彩色占位符图片
   - 显示图片尺寸信息
   - 多种颜色主题

## 功能特性

1. **真实图片**: 从知名图片API获取真实、高质量的图片
2. **智能备用**: 如果一个API失败，自动尝试其他API服务
3. **多格式支持**: 支持PNG、JPG、JPEG格式
4. **缓存策略**: 相同参数的图片会缓存1小时，每小时内容会有所变化
5. **响应头优化**: 正确设置Content-Type、Content-Length和缓存控制头
6. **错误处理**: 完善的错误处理和日志记录
7. **超时控制**: 15秒主请求超时，10秒备用请求超时

## 随机策略

- 使用文件名作为随机种子，确保相同参数在同一小时内返回相同图片
- 每小时图片内容会发生变化
- 随机选择API服务和图片主题

## 错误处理

- 宽度或高度超出范围(1-2000)会返回400错误
- 不支持的文件格式会返回400错误
- 缺少必要参数会返回400错误
- 所有API都无法访问时返回500错误

## 实现文件

- `frontapi/internal/media/image/generator.go` - 随机图片获取器
- `frontapi/cmd/main.go` - 添加了路由配置

## 使用场景

- 前端开发时的占位图片
- 测试图片上传功能
- 原型设计的临时图片
- API测试和演示
- 网站内容填充
- 设计稿预览

## 性能说明

- 首次请求可能需要几秒钟（从外部API获取）
- 后续相同参数请求会被缓存，响应更快
- 支持并发请求
- 自动重试机制确保高可用性 