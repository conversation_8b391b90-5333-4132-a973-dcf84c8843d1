/**
 * 认证状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from './user'

export interface AuthState {
  token: string | null
  refreshToken: string | null
  user: User | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const user = ref<User | null>(null)
  const isAuthenticated = ref<boolean>(false)
  const loading = ref<boolean>(false)
  const error = ref<string | null>(null)

  // 计算属性
  const authState = computed((): AuthState => ({
    token: token.value,
    refreshToken: refreshToken.value,
    user: user.value,
    isAuthenticated: isAuthenticated.value,
    loading: loading.value,
    error: error.value
  }))

  const hasValidToken = computed(() => {
    return !!token.value && !isTokenExpired(token.value)
  })

  // 方法
  const setTokens = (accessToken: string, refreshTokenValue?: string) => {
    token.value = accessToken
    if (refreshTokenValue) {
      refreshToken.value = refreshTokenValue
    }
    isAuthenticated.value = true
    error.value = null
  }

  const setUser = (userData: User) => {
    user.value = userData
  }

  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const logout = () => {
    token.value = null
    refreshToken.value = null
    user.value = null
    isAuthenticated.value = false
    error.value = null
  }

  const clearError = () => {
    error.value = null
  }

  // 辅助函数
  const isTokenExpired = (tokenValue: string): boolean => {
    try {
      const payload = JSON.parse(atob(tokenValue.split('.')[1]))
      const currentTime = Date.now() / 1000
      return payload.exp < currentTime
    } catch {
      return true
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    isAuthenticated,
    loading,
    error,
    
    // 计算属性
    authState,
    hasValidToken,
    
    // 方法
    setTokens,
    setUser,
    setLoading,
    setError,
    logout,
    clearError
  }
})

export default useAuthStore