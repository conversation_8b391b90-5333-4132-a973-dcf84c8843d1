<template>
    <div class="status-tag">
        <el-tag :type="tagType" :effect="effect" size="small" class="status-tag-item">
            {{ statusText }}
        </el-tag>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    status: {
        type: Number,
        required: true
    },
    effect: {
        type: String as () => 'light' | 'dark' | 'plain',
        default: 'light'
    }
});

// 状态文本
const statusText = computed(() => {
    switch (props.status) {
        case 1:
            return '启用';
        case 0:
            return '禁用';
        case -1:
            return '已删除';
        default:
            return '未知';
    }
});

// 标签类型
const tagType = computed(() => {
    switch (props.status) {
        case 1:
            return 'success';
        case 0:
            return 'warning';
        case -1:
            return 'danger';
        default:
            return 'info';
    }
});
</script>

<style scoped lang="scss">
.status-tag {
    display: inline-flex;
    align-items: center;

    .status-tag-item {
        text-align: center;
        min-width: 50px;
    }
}
</style>