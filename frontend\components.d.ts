/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Button: typeof import('primevue/button')['default']
    Divider: typeof import('primevue/divider')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Megamenu: typeof import('primevue/megamenu')['default']
    MegaMenu: typeof import('primevue/megamenu')['default']
    Menu: typeof import('primevue/menu')['default']
    Popover: typeof import('primevue/popover')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('primevue/sidebar')['default']
  }
  export interface GlobalDirectives {
    Ripple: typeof import('primevue/ripple')['default']
  }
}
