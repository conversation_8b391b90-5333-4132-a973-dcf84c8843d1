# 完整认证流程测试
Write-Host "🧪 开始完整认证流程测试..." -ForegroundColor Green

# 第一步：测试登录
Write-Host "`n1️⃣ 测试登录接口..." -ForegroundColor Yellow
$loginData = @{
    data = @{
        username = "admin"
        password = "admin123"
        rememberMe = $true
    }
} | ConvertTo-Json -Depth 3

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/proadm/auth/login" `
        -Method POST `
        -ContentType "application/json" `
        -Body $loginData `
        -TimeoutSec 10

    Write-Host "✅ 登录成功!" -ForegroundColor Green
    Write-Host "登录响应: $($loginResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    
    # 提取access token
    $accessToken = $loginResponse.data.accessToken
    if ($accessToken) {
        Write-Host "🔑 获取到AccessToken: $accessToken" -ForegroundColor Cyan
        
        # 第二步：测试获取用户信息
        Write-Host "`n2️⃣ 测试获取用户信息接口..." -ForegroundColor Yellow
        
        $headers = @{
            "Authorization" = "Bearer $accessToken"
            "Content-Type" = "application/json"
        }
        
        $userInfoData = @{
            data = @{}
        } | ConvertTo-Json -Depth 3
        
        try {
            $userInfoResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/proadm/auth/getUserInfo" `
                -Method POST `
                -Headers $headers `
                -Body $userInfoData `
                -TimeoutSec 10

            Write-Host "✅ 获取用户信息成功!" -ForegroundColor Green
            Write-Host "用户信息响应: $($userInfoResponse | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
            
            Write-Host "`n🎉 完整认证流程测试成功!" -ForegroundColor Green
        }
        catch {
            $statusCode = $_.Exception.Response.StatusCode.value__
            Write-Host "❌ 获取用户信息失败! 状态码: $statusCode" -ForegroundColor Red
            
            try {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorContent = $reader.ReadToEnd()
                Write-Host "错误响应: $errorContent" -ForegroundColor Red
            }
            catch {
                Write-Host "无法读取错误响应" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "❌ 登录响应中没有找到accessToken" -ForegroundColor Red
    }
}
catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "❌ 登录失败! 状态码: $statusCode" -ForegroundColor Red
    
    try {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorContent = $reader.ReadToEnd()
        Write-Host "错误响应: $errorContent" -ForegroundColor Red
    }
    catch {
        Write-Host "无法读取错误响应" -ForegroundColor Red
    }
}

Write-Host "`n测试完成。" -ForegroundColor Green 