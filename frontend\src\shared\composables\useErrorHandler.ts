import { ref, onErrorCaptured, onUnmounted, readonly, h } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  API = 'api',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  RUNTIME = 'runtime',
  UNKNOWN = 'unknown'
}

// 错误级别
export enum ErrorLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 错误信息接口
export interface AppError {
  id: string
  type: ErrorType
  level: ErrorLevel
  message: string
  details?: any
  stack?: string
  timestamp: number
  url?: string
  userAgent?: string
  userId?: string
  component?: string
  action?: string
}

// 错误处理配置
export interface ErrorHandlerConfig {
  enableLogging?: boolean
  enableReporting?: boolean
  enableNotification?: boolean
  maxErrors?: number
  reportUrl?: string
  ignoredErrors?: string[]
  retryAttempts?: number
  retryDelay?: number
}

// 错误恢复策略
export interface RecoveryStrategy {
  canRecover: (error: AppError) => boolean
  recover: (error: AppError) => Promise<boolean>
  fallback?: () => void
}

/**
 * 错误处理 Composable
 * 提供统一的错误处理、记录、报告和恢复机制
 */
export function useErrorHandler(config: ErrorHandlerConfig = {}) {
  const {
    enableLogging = true,
    enableReporting = true,
    enableNotification = true,
    maxErrors = 100,
    reportUrl = '/api/errors',
    ignoredErrors = [],
    retryAttempts = 3,
    retryDelay = 1000
  } = config

  const errors = ref<AppError[]>([])
  const isReporting = ref(false)
  const recoveryStrategies = ref<Map<ErrorType, RecoveryStrategy>>(new Map())

  // 生成错误ID
  const generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 创建错误对象
  const createError = (
    type: ErrorType,
    level: ErrorLevel,
    message: string,
    details?: any,
    component?: string,
    action?: string
  ): AppError => {
    return {
      id: generateErrorId(),
      type,
      level,
      message,
      details,
      stack: new Error().stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      component,
      action
    }
  }

  // 检查是否应该忽略错误
  const shouldIgnoreError = (error: AppError): boolean => {
    return ignoredErrors.some(pattern => {
      if (typeof pattern === 'string') {
        return error.message.includes(pattern)
      }
      return false
    })
  }

  // 记录错误
  const logError = (error: AppError) => {
    if (!enableLogging || shouldIgnoreError(error)) return

    // 添加到错误列表
    errors.value.unshift(error)
    
    // 限制错误数量
    if (errors.value.length > maxErrors) {
      errors.value = errors.value.slice(0, maxErrors)
    }

    // 控制台输出
    const logMethod = error.level === ErrorLevel.CRITICAL ? 'error' : 
                     error.level === ErrorLevel.HIGH ? 'error' :
                     error.level === ErrorLevel.MEDIUM ? 'warn' : 'log'
    
    console[logMethod](`[${error.type.toUpperCase()}] ${error.message}`, {
      id: error.id,
      details: error.details,
      stack: error.stack,
      component: error.component,
      action: error.action
    })
  }

  // 显示错误通知
  const showErrorNotification = (error: AppError) => {
    if (!enableNotification || shouldIgnoreError(error)) return

    const notificationConfig = {
      title: '系统错误',
      message: error.message,
      type: 'error' as const,
      duration: error.level === ErrorLevel.CRITICAL ? 0 : 5000,
      showClose: true
    }

    switch (error.level) {
      case ErrorLevel.CRITICAL:
        ElNotification({
          ...notificationConfig,
          title: '严重错误',
          type: 'error'
        })
        break
      case ErrorLevel.HIGH:
        ElNotification({
          ...notificationConfig,
          title: '错误',
          type: 'error'
        })
        break
      case ErrorLevel.MEDIUM:
        ElMessage({
          message: error.message,
          type: 'warning',
          duration: 3000
        })
        break
      case ErrorLevel.LOW:
        ElMessage({
          message: error.message,
          type: 'info',
          duration: 2000
        })
        break
    }
  }

  // 报告错误到服务器
  const reportError = async (error: AppError): Promise<boolean> => {
    if (!enableReporting || shouldIgnoreError(error) || isReporting.value) {
      return false
    }

    isReporting.value = true

    try {
      const response = await fetch(reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          error: {
            ...error,
            // 移除敏感信息
            stack: error.level === ErrorLevel.CRITICAL ? error.stack : undefined
          },
          context: {
            timestamp: Date.now(),
            sessionId: sessionStorage.getItem('sessionId'),
            buildVersion: process.env.VUE_APP_VERSION
          }
        })
      })

      return response.ok
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
      return false
    } finally {
      isReporting.value = false
    }
  }

  // 尝试错误恢复
  const attemptRecovery = async (error: AppError): Promise<boolean> => {
    const strategy = recoveryStrategies.value.get(error.type)
    
    if (!strategy || !strategy.canRecover(error)) {
      return false
    }

    try {
      const recovered = await strategy.recover(error)
      if (recovered) {
        console.log(`Successfully recovered from error: ${error.id}`)
        return true
      }
    } catch (recoveryError) {
      console.error('Error recovery failed:', recoveryError)
    }

    // 执行回退策略
    if (strategy.fallback) {
      strategy.fallback()
    }

    return false
  }

  // 处理错误的主要方法
  const handleError = async (
    type: ErrorType,
    level: ErrorLevel,
    message: string,
    details?: any,
    component?: string,
    action?: string
  ): Promise<void> => {
    const error = createError(type, level, message, details, component, action)

    // 记录错误
    logError(error)

    // 显示通知
    showErrorNotification(error)

    // 尝试恢复
    const recovered = await attemptRecovery(error)
    
    if (!recovered) {
      // 报告错误
      await reportError(error)
    }
  }

  // 处理网络错误
  const handleNetworkError = (error: any, action?: string) => {
    let message = '网络连接失败'
    let level = ErrorLevel.MEDIUM

    if (error.code === 'NETWORK_ERROR') {
      message = '网络连接中断，请检查网络设置'
      level = ErrorLevel.HIGH
    } else if (error.status === 0) {
      message = '无法连接到服务器'
      level = ErrorLevel.HIGH
    } else if (error.status >= 500) {
      message = '服务器内部错误'
      level = ErrorLevel.HIGH
    } else if (error.status === 404) {
      message = '请求的资源不存在'
      level = ErrorLevel.MEDIUM
    } else if (error.status === 403) {
      message = '没有权限访问该资源'
      level = ErrorLevel.MEDIUM
    } else if (error.status === 401) {
      message = '身份验证失败，请重新登录'
      level = ErrorLevel.HIGH
    }

    return handleError(ErrorType.NETWORK, level, message, error, undefined, action)
  }

  // 处理API错误
  const handleApiError = (error: any, endpoint?: string) => {
    const message = error.message || error.data?.message || 'API请求失败'
    const level = error.status >= 500 ? ErrorLevel.HIGH : ErrorLevel.MEDIUM
    
    return handleError(ErrorType.API, level, message, {
      status: error.status,
      endpoint,
      response: error.data
    }, undefined, `API: ${endpoint}`)
  }

  // 处理验证错误
  const handleValidationError = (errors: Record<string, string[]>, component?: string) => {
    const message = '表单验证失败'
    
    return handleError(ErrorType.VALIDATION, ErrorLevel.LOW, message, errors, component, 'validation')
  }

  // 处理权限错误
  const handlePermissionError = (action: string, resource?: string) => {
    const message = `没有权限执行操作: ${action}`
    
    return handleError(ErrorType.PERMISSION, ErrorLevel.MEDIUM, message, { action, resource })
  }

  // 处理运行时错误
  const handleRuntimeError = (error: Error, component?: string) => {
    const message = error.message || '运行时错误'
    
    return handleError(ErrorType.RUNTIME, ErrorLevel.HIGH, message, {
      name: error.name,
      stack: error.stack
    }, component, 'runtime')
  }

  // 注册恢复策略
  const registerRecoveryStrategy = (type: ErrorType, strategy: RecoveryStrategy) => {
    recoveryStrategies.value.set(type, strategy)
  }

  // 重试机制
  const withRetry = async <T>(
    operation: () => Promise<T>,
    attempts: number = retryAttempts,
    delay: number = retryDelay
  ): Promise<T> => {
    let lastError: any

    for (let i = 0; i < attempts; i++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (i < attempts - 1) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
        }
      }
    }

    throw lastError
  }

  // 清除错误
  const clearErrors = () => {
    errors.value = []
  }

  // 获取错误统计
  const getErrorStats = () => {
    const stats = {
      total: errors.value.length,
      byType: {} as Record<ErrorType, number>,
      byLevel: {} as Record<ErrorLevel, number>,
      recent: errors.value.slice(0, 10)
    }

    errors.value.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1
    })

    return stats
  }

  // Vue错误捕获
  onErrorCaptured((error: Error, instance, info) => {
    const componentName = instance?.$options.name || instance?.$options.__name || 'Unknown'
    
    handleRuntimeError(error, componentName)
    
    // 返回false阻止错误继续传播
    return false
  })

  // 全局错误监听
  const setupGlobalErrorHandling = () => {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      handleRuntimeError(new Error(event.reason), 'Global')
      event.preventDefault()
    })

    // 捕获全局JavaScript错误
    window.addEventListener('error', (event) => {
      handleRuntimeError(event.error || new Error(event.message), 'Global')
    })
  }

  // 清理全局错误监听
  const cleanupGlobalErrorHandling = () => {
    window.removeEventListener('unhandledrejection', () => {})
    window.removeEventListener('error', () => {})
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanupGlobalErrorHandling()
  })

  // 初始化全局错误处理
  setupGlobalErrorHandling()

  return {
    // 响应式数据
    errors: readonly(errors),
    isReporting: readonly(isReporting),
    
    // 错误处理方法
    handleError,
    handleNetworkError,
    handleApiError,
    handleValidationError,
    handlePermissionError,
    handleRuntimeError,
    
    // 工具方法
    withRetry,
    registerRecoveryStrategy,
    clearErrors,
    getErrorStats,
    
    // 配置方法
    setupGlobalErrorHandling,
    cleanupGlobalErrorHandling
  }
}

/**
 * 简化的错误处理 Hook
 * 用于组件级别的错误处理
 */
export function useComponentErrorHandler(componentName: string) {
  const { handleError, handleRuntimeError } = useErrorHandler()

  const handleComponentError = (error: Error, action?: string) => {
    return handleRuntimeError(error, componentName)
  }

  const handleAsyncError = async <T>(operation: () => Promise<T>, action?: string): Promise<T | null> => {
    try {
      return await operation()
    } catch (error) {
      await handleComponentError(error as Error, action)
      return null
    }
  }

  return {
    handleComponentError,
    handleAsyncError
  }
}

// 错误边界组件工厂
export function createErrorBoundary(fallbackComponent?: any) {
  return {
    name: 'ErrorBoundary',
    props: {
      fallback: {
        type: Object,
        default: () => fallbackComponent
      }
    },
    data() {
      return {
        hasError: false,
        error: null as Error | null
      }
    },
    errorCaptured(error: Error, instance: any, info: string) {
      this.hasError = true
      this.error = error
      
      const { handleRuntimeError } = useErrorHandler()
      handleRuntimeError(error, instance?.$options.name || 'ErrorBoundary')
      
      return false
    },
    render() {
      if (this.hasError) {
        return this.fallback ? h(this.fallback, { error: this.error }) : h('div', {
          class: 'error-boundary',
          style: {
            padding: '20px',
            border: '1px solid #ff4757',
            borderRadius: '4px',
            backgroundColor: '#fff5f5',
            color: '#ff4757',
            textAlign: 'center'
          }
        }, [
          h('h3', '出现了一些问题'),
          h('p', this.error?.message || '未知错误'),
          h('button', {
            onClick: () => {
              this.hasError = false
              this.error = null
            },
            style: {
              padding: '8px 16px',
              backgroundColor: '#ff4757',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }
          }, '重试')
        ])
      }
      
      return this.$slots.default?.()
    }
  }
}