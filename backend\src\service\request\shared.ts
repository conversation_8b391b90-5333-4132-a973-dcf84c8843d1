import { useAuthStore } from '@/store/modules/auth';
import { errorHandler } from '@/utils/errorHandler';
import { localStg } from '@/utils/storage';
import { fetchRefreshToken } from '../api';
import type { RequestInstanceState } from './type';

export function getAuthorization() {
    const token = localStg.get('token');
    const Authorization = token ? `Bearer ${token}` : null;

    // 添加调试日志
    console.log('Debug - Token from localStorage:', token);
    console.log('Debug - Generated Authorization header:', Authorization);
    console.log('Debug - localStorage contents:', {
        token: localStg.get('token'),
        refreshToken: localStg.get('refreshToken'),
        allKeys: Object.keys(localStorage).filter(key => key.includes('SOY_'))
    });

    return Authorization;
}

/** refresh token */
async function handleRefreshToken() {
    const { resetStore } = useAuthStore();

    const rToken = localStg.get('refreshToken') || '';
    const { error, data } = await fetchRefreshToken(rToken);
    if (!error && data) {
        console.log('Debug - Raw refresh token response:', data);

        // 转换后端返回的数据格式
        const token = data.accessToken || data.token;
        const refreshToken = data.refreshToken;

        console.log('Debug - Converted refresh tokens:', { token, refreshToken });

        localStg.set('token', token);
        localStg.set('refreshToken', refreshToken);
        return true;
    }

    resetStore();

    return false;
}

export async function handleExpiredRequest(state: RequestInstanceState) {
    if (!state.refreshTokenFn) {
        state.refreshTokenFn = handleRefreshToken();
    }

    const success = await state.refreshTokenFn;

    setTimeout(() => {
        state.refreshTokenFn = null;
    }, 1000);

    return success;
}

export function showErrorMsg(state: RequestInstanceState, message: string) {
    // 使用统一的错误处理器，自动防重复
    errorHandler.showError(message);
}
