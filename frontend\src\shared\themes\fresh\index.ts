/**
 * Fresh 主题配置
 * 基于清新自然的主题
 */
import { ThemeConfig } from '../theme-manager';
import freshVariables from './variables';
import freshDarkVariables from './variables-dark';

// Fresh 亮色主题
export const freshLightTheme: ThemeConfig = {
  name: 'fresh-light',
  displayName: 'Fresh Light',
  code: 'fresh',
  primary: '#4CAF50',
  isDark: false,
  variables: freshVariables
};

// Fresh 暗色主题
export const freshDarkTheme: ThemeConfig = {
  name: 'fresh-dark',
  displayName: 'Fresh Dark',
  code: 'fresh',
  primary: '#66BB6A',
  isDark: true,
  variables: freshDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default freshLightTheme;