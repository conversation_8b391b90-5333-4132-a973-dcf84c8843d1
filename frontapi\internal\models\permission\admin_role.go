package permission

import (
	"frontapi/internal/models"
)

// AdminRole 管理员角色模型
type AdminRole struct {
	*models.IntBaseModelStruct
	Code        string `json:"code" gorm:"column:code;type:varchar(50);not null;uniqueIndex;comment:角色编码"`
	Name        string `json:"name" gorm:"column:name;type:varchar(100);not null;comment:角色名称"`
	Description string `json:"description" gorm:"column:description;type:varchar(500);comment:角色描述"`
	Level       int    `json:"level" gorm:"column:level;default:1;comment:角色等级(1-4,数字越小权限越大)"`
	IsDefault   int8   `json:"is_default" gorm:"column:is_default;default:0;comment:是否默认角色(0否,1是)"`

	// 关联字段 (不存储到数据库)
	UserCount   int      `json:"user_count" gorm:"-"`  // 该角色下的用户数量
	Permissions []string `json:"permissions" gorm:"-"` // 角色权限列表
	MenuIDs     []int    `json:"menu_ids" gorm:"-"`    // 菜单ID列表
}

// TableName 指定表名
func (AdminRole) TableName() string {
	return "ly_admin_sys_role"
}

// IsActive 检查角色是否激活
func (r *AdminRole) IsActive() bool {
	return r.Status == 1
}

// IsDefaultRole 检查是否为默认角色
func (r *AdminRole) IsDefaultRole() bool {
	return r.IsDefault == 1
}

// GetRoleCode 获取角色编码
func (r *AdminRole) GetRoleCode() string {
	return r.Code
}

// GetRoleLevel 获取角色等级
func (r *AdminRole) GetRoleLevel() int {
	return r.Level
}

// CanManageRole 检查是否可以管理指定角色(只能管理等级更低的角色)
func (r *AdminRole) CanManageRole(targetRole *AdminRole) bool {
	return r.Level < targetRole.Level
}

// RoleLevel 角色等级常量
const (
	RoleLevelAdmin   = 1 // 超级管理员
	RoleLevelManager = 2 // 管理员
	RoleLevelEditor  = 3 // 编辑者
	RoleLevelViewer  = 4 // 查看者
)

// GetRoleLevelName 获取角色等级名称
func GetRoleLevelName(level int) string {
	switch level {
	case RoleLevelAdmin:
		return "超级管理员"
	case RoleLevelManager:
		return "管理员"
	case RoleLevelEditor:
		return "编辑者"
	case RoleLevelViewer:
		return "查看者"
	default:
		return "未知"
	}
}

// GetRoleLevelNames 获取所有角色等级名称
func GetRoleLevelNames() map[int]string {
	return map[int]string{
		RoleLevelAdmin:   "超级管理员",
		RoleLevelManager: "管理员",
		RoleLevelEditor:  "编辑者",
		RoleLevelViewer:  "查看者",
	}
}
