<template>
  <div class="user-stats">
    <div class="stats-grid">
      <div class="stat-item" v-if="followCount !== undefined">
        <div class="stat-number">{{ formatCount(followCount) }}</div>
        <div class="stat-label">关注者</div>
      </div>
      
      <div class="stat-item" v-if="totalVideos !== undefined">
        <div class="stat-number">{{ formatCount(totalVideos) }}</div>
        <div class="stat-label">视频</div>
      </div>
      
      <div class="stat-item" v-if="totalPosts !== undefined">
        <div class="stat-number">{{ formatCount(totalPosts) }}</div>
        <div class="stat-label">动态</div>
      </div>
      
      <div class="stat-item" v-if="totalShorts !== undefined">
        <div class="stat-number">{{ formatCount(totalShorts) }}</div>
        <div class="stat-label">短视频</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatCount } from '@/utils/format'

interface Props {
  followCount?: number
  totalVideos?: number
  totalPosts?: number
  totalShorts?: number
}

defineProps<Props>()
</script>

<style scoped lang="scss">
.user-stats {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 16px;
    padding: 12px 0;
  }

  .stat-item {
    text-align: center;
    
    .stat-number {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      line-height: 1.2;
      margin-bottom: 2px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #6b7280;
      line-height: 1;
    }
  }
}
</style> 