package books

import (
	"context"
	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// BookmarkRepository 电子书书签仓库接口
type BookmarkRepository interface {
	base.ExtendedRepository[books.Bookmark]
	ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*books.Bookmark, int64, error)
	ListByBook(ctx context.Context, userID, bookID string, page, pageSize int) ([]*books.Bookmark, int64, error)
}

type bookmarkRepository struct {
	base.ExtendedRepository[books.Bookmark]
}

// NewBookmarkRepository 创建电子书书签仓库实例
func NewBookmarkRepository(db *gorm.DB) BookmarkRepository {
	return &bookmarkRepository{
		ExtendedRepository: base.NewExtendedRepository[books.Bookmark](db),
	}
}



// ListByUser 获取用户的书签列表
func (r *bookmarkRepository) ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*books.Bookmark, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "created_at DESC", page, pageSize, "user_id = ?", userID)
}

// ListByBook 获取用户在某本书的书签列表
func (r *bookmarkRepository) ListByBook(ctx context.Context, userID, bookID string, page, pageSize int) ([]*books.Bookmark, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "created_at DESC", page, pageSize, "user_id = ? AND book_id = ?", userID, bookID)
}
