package picture

import "frontapi/internal/typings"

type PictureCategoryInfo struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Code      string `json:"code"`
	SortOrder int64  `json:"sortOrder"`
}
type PictureAlbumDetail struct {
	ID           string   `json:"id"`
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	CoverURL     string   `json:"coverUrl"`
	PictureCount int64    `json:"pictureCount"`
	ViewCount    int64    `json:"viewCount"`
	LikeCount    int64    `json:"likeCount"`
	CommentCount int64    `json:"commentCount"`
	CategoryID   string   `json:"categoryId"`
	CategoryName string   `json:"categoryName"`
	Tags         []string `json:"tags"`
}

type PictureCategoryListResponse struct {
	typings.BaseListResponse
	List []PictureCategoryInfo `json:"list"`
}
type PictureAlbumListResponse struct {
	typings.BaseListResponse
	List []PictureAlbumDetail `json:"list"`
}

type PictureInfo struct {
	ID           string `json:"id"`
	URL          string `json:"url"`
	Title        string `json:"title"`
	Description  string `json:"description"`
	Width        int64  `json:"width"`
	Height       int64  `json:"height"`
	Size         int64  `json:"size"`
	CategoryID   string `json:"categoryId"`
	CategoryName string `json:"categoryName"`
	AlbumID      string `json:"albumId"`
	AlbumTitle   string `json:"albumTitle"`
	CreatorID    string `json:"creatorId"`
	ViewCount    int64  `json:"viewCount"`
	LikeCount    int64  `json:"likeCount"`
	UploadTime   string `json:"uploadTime"`
	Status       int8   `json:"status"`
	CreatedAt    string `json:"createdAt"`
	UpdatedAt    string `json:"updatedAt"`
	IsLiked      bool   `json:"isLiked"`
	IsFavorite   bool   `json:"isFavorite"`
}

type PictureListResponse struct {
	typings.BaseListResponse
	List []PictureInfo `json:"list"`
}
