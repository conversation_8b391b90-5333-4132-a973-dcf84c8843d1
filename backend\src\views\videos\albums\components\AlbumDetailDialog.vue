<template>
  <el-dialog
    :model-value="visible"
    title="专辑详情"
    width="700px"
    @close="handleClose"
  >
    <div class="album-detail">
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>专辑ID：</label>
              <span>{{ album.id }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>专辑标题：</label>
              <span>{{ album.title }}</span>
            </div>
          </el-col>
        </el-row>
        
        <div class="detail-item">
          <label>专辑描述：</label>
          <p class="description">{{ album.description || '暂无描述' }}</p>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>专辑封面：</label>
              <div v-if="album.cover" class="cover-display">
                <el-image
                  :src="album.cover"
                  :preview-src-list="[album.cover]"
                  fit="cover"
                  style="width: 150px; height: 90px; border-radius: 8px;"
                />
              </div>
              <span v-else class="text-gray">暂无封面</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>分类信息：</label>
              <div>
                <el-tag v-if="album.category_name" type="info">
                  {{ album.category_name }}
                </el-tag>
                <span v-else class="text-gray">未分类</span>
                <div v-if="album.category_id" class="category-id">
                  ID: {{ album.category_id }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="detail-section">
        <h3 class="section-title">创建者信息</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>创建者头像：</label>
              <div v-if="album.user_avatar" class="avatar-display">
                <el-avatar :src="album.user_avatar" :size="60" />
              </div>
              <span v-else class="text-gray">暂无头像</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建者ID：</label>
              <span>{{ album.user_id }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建者昵称：</label>
              <span>{{ album.user_nickname || '暂无昵称' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="detail-section">
        <h3 class="section-title">统计信息</h3>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ album.video_count || 0 }}</div>
              <div class="stat-label">视频数量</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formatNumber(album.view_count) }}</div>
              <div class="stat-label">观看次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formatNumber(album.heat) }}</div>
              <div class="stat-label">热度</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">
                <el-tag type="success">
                  免费
                </el-tag>
              </div>
              <div class="stat-label">付费类型</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="detail-section">
        <h3 class="section-title">其他信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>标签：</label>
              <div class="tags-display">
                <el-tag
                  v-for="tag in album.tags"
                  :key="tag"
                  size="small"
                  style="margin-right: 8px; margin-bottom: 4px;"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="!album.tags || album.tags.length === 0" class="text-gray">
                  暂无标签
                </span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>价格：</label>
              <span class="text-gray">免费</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="album.status === 1 ? 'success' : 'danger'">
                {{ album.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(album.created_at) }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item">
          <label>更新时间：</label>
          <span>{{ formatDateTime(album.updated_at) }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { VideoAlbum } from '@/types/videoAlbum';
import { computed } from 'vue';

// Props
interface Props {
  visible: boolean;
  albumData: VideoAlbum | null;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  'update:visible': [value: boolean];
}

const emit = defineEmits<Emits>();

// 获取专辑数据，如果为null则创建空对象
const album = computed(() => props.albumData || {} as VideoAlbum);

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
};

// 格式化数字
const formatNumber = (num: number) => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};
</script>

<style scoped>
.album-detail {
  padding: 20px 0;
}

.detail-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

.description {
  margin: 8px 0;
  line-height: 1.6;
  color: #606266;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.cover-display {
  margin-top: 8px;
}

.avatar-display {
  margin-top: 8px;
}

.category-id {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.tags-display {
  margin-top: 8px;
  line-height: 1.8;
}

.text-gray {
  color: #c0c4cc;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 