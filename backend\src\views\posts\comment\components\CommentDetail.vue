<template>
    <el-dialog title="评论详情" :model-value="visible" @update:model-value="handleClose" width="60%" destroy-on-close
        class="comment-detail-dialog">
        <div v-if="comment" class="comment-detail-container">
            <!-- 左右布局容器 -->
            <div class="layout-container">
                <!-- 左侧：主评论卡片 -->
                <div class="left-section">
                    <el-card class="main-comment-card" shadow="hover">
                        <template #header>
                            <div class="comment-header">
                                <div class="user-info">
                                    <el-avatar :size="40" :src="comment.user_avatar" class="user-avatar">
                                        {{ (comment.user_nickname || comment.user_id || '')[0] }}
                                    </el-avatar>
                                    <div class="user-details">
                                        <div class="username">
                                            {{ comment.user_nickname || '用户' + comment.user_id }}
                                            <el-tag v-if="comment.user_type === 2" type="warning" size="small"
                                                class="star-tag">
                                                明星
                                            </el-tag>
                                        </div>
                                        <div class="user-meta">
                                            <span class="user-id">ID: {{ comment.user_id }}</span>
                                            <span class="create-time">
                                                <el-icon>
                                                    <Clock />
                                                </el-icon>
                                                {{ formatDateTime(comment.created_at) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="comment-status">
                                    <el-tag :type="getStatusType(comment.status)" size="large">
                                        {{ getStatusText(comment.status) }}
                                    </el-tag>
                                </div>
                            </div>
                        </template>

                        <!-- 评论内容 -->
                        <div class="comment-content">
                            <div class="content-text">{{ comment.content }}</div>

                            <!-- 图片展示 -->
                            <div v-if="comment.images && comment.images.length > 0" class="comment-images">
                                <el-image v-for="(image, index) in comment.images" :key="index" :src="image"
                                    :preview-src-list="comment.images" :initial-index="index" class="comment-image"
                                    fit="cover" />
                            </div>

                            <!-- 视频展示 -->
                            <div v-if="comment.video" class="comment-video">
                                <video :src="comment.video" controls class="video-player"></video>
                            </div>
                        </div>

                        <!-- 评论统计信息 -->
                        <div class="comment-stats">
                            <div class="stat-item">
                                <el-icon class="stat-icon">
                                    <Star />
                                </el-icon>
                                <span class="stat-label">点赞</span>
                                <span class="stat-value">{{ comment.like_count || 0 }}</span>
                            </div>
                            <div class="stat-item">
                                <el-icon class="stat-icon">
                                    <ChatLineRound />
                                </el-icon>
                                <span class="stat-label">回复</span>
                                <span class="stat-value">{{ comment.reply_count || 0 }}</span>
                            </div>
                            <div class="stat-item">
                                <el-icon class="stat-icon">
                                    <TrendCharts />
                                </el-icon>
                                <span class="stat-label">热度</span>
                                <span class="stat-value">{{ comment.heat || 0 }}</span>
                            </div>
                        </div>
                    </el-card>
                </div>

                <!-- 右侧：详情信息卡片 -->
                <div class="right-section">
                    <el-card class="detail-info-card" shadow="hover">
                        <template #header>
                            <h3>详细信息</h3>
                        </template>

                        <el-descriptions :column="1" border>
                            <el-descriptions-item label="评论ID">
                                <el-text type="info" class="comment-id">{{ comment.id }}</el-text>
                            </el-descriptions-item>
                            <el-descriptions-item label="帖子ID">
                                <el-link type="primary" @click="viewPost(comment.post_id)">
                                    {{ comment.post_id }}
                                </el-link>
                            </el-descriptions-item>
                            <el-descriptions-item label="父评论ID">
                                <el-link v-if="comment.parent_id" type="primary"
                                    @click="viewParentComment(comment.parent_id)">
                                    {{ comment.parent_id }}
                                </el-link>
                                <span v-else class="text-muted">无</span>
                            </el-descriptions-item>
                            <el-descriptions-item label="审核时间">
                                {{ comment.audit_time ? formatDateTime(comment.audit_time) : '未审核' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="审核人ID">
                                {{ comment.auditor_id || '无' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="拒绝原因">
                                <div v-if="comment.reason" class="reject-reason">
                                    {{ comment.reason }}
                                </div>
                                <span v-else class="text-muted">无</span>
                            </el-descriptions-item>
                            <el-descriptions-item label="更新时间">
                                {{ formatDateTime(comment.updated_at) }}
                            </el-descriptions-item>
                            <el-descriptions-item label="用户类型">
                                <el-tag :type="comment.user_type === 2 ? 'warning' : 'info'" size="small">
                                    {{ comment.user_type === 2 ? '明星用户' : '普通用户' }}
                                </el-tag>
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-card>
                </div>
            </div>
            <!-- 回复列表 -->
            <el-card v-if="comment.reply_count > 0" class="replies-card" shadow="hover">
                <template #header>
                    <div class="replies-header">
                        <h3>回复列表 ({{ comment.reply_count }})</h3>
                        <el-button type="primary" size="small" @click="loadReplies" :loading="repliesLoading">
                            <el-icon>
                                <Refresh />
                            </el-icon>
                            刷新回复
                        </el-button>
                    </div>
                </template>

                <div v-loading="repliesLoading" class="replies-container">
                    <div v-if="replyList.length > 0" class="reply-list">
                        <div v-for="reply in replyList" :key="reply.id" class="reply-item">
                            <div class="reply-header">
                                <el-avatar :size="32" :src="reply.user_avatar" class="reply-avatar">
                                    {{ (reply.user_nickname || reply.user_id || '')[0] }}
                                </el-avatar>
                                <div class="reply-user-info">
                                    <div class="reply-username">
                                        {{ reply.user_nickname || '用户' + reply.user_id }}
                                        <el-tag v-if="reply.user_type === 2" type="warning" size="small">
                                            明星
                                        </el-tag>
                                    </div>
                                    <div class="reply-meta">
                                        <span v-if="reply.reply_to_user" class="reply-to">
                                            回复 @{{ reply.reply_to_user }}
                                        </span>
                                        <span class="reply-time">
                                            {{ formatDateTime(reply.created_at) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="reply-actions">
                                    <el-tag :type="getStatusType(reply.status)" size="small">
                                        {{ getStatusText(reply.status) }}
                                    </el-tag>
                                </div>
                            </div>
                            <div class="reply-content">{{ reply.content }}</div>
                            <div class="reply-stats">
                                <span class="reply-likes">
                                    <el-icon>
                                        <Star />
                                    </el-icon>
                                    {{ reply.like_count || 0 }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <el-empty v-else description="暂无回复" />
                </div>

                <!-- 回复分页 -->
                <div v-if="replyPagination.total > replyPagination.pageSize" class="replies-pagination">
                    <el-pagination v-model:current-page="replyPagination.page"
                        v-model:page-size="replyPagination.pageSize" :page-sizes="[5, 10, 20, 50]"
                        :total="replyPagination.total" background layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleReplyPageChange(1)" @current-change="handleReplyPageChange" small />
                </div>
            </el-card>

        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { getCommentReplies } from '@/service/api/posts/comment';
import { PostComment } from '@/types/posts';
import {
    ChatLineRound,
    Clock,
    Refresh,
    Star,
    TrendCharts
} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { PropType, ref, watch } from 'vue';

// Props定义
interface Props {
    visible: boolean;
    comment: PostComment | null;
}

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    comment: {
        type: Object as PropType<PostComment | null>,
        default: null
    }
});

// Emits定义
interface Emits {
    'update:visible': [value: boolean];
    'edit': [comment: PostComment];
    'view-post': [postId: string];
    'view-parent': [parentId: string];
}

const emit = defineEmits<Emits>();

// 回复相关状态
const replyList = ref<PostComment[]>([]);
const repliesLoading = ref(false);
const replyPagination = ref({
    page: 1,
    pageSize: 10,
    total: 0
});

// 监听对话框显示状态，自动加载回复
watch(() => props.visible, (newVal) => {
    if (newVal && props.comment && props.comment.reply_count > 0) {
        loadReplies();
    }
});

// 加载回复列表
const loadReplies = async () => {
    if (!props.comment?.id) return;

    repliesLoading.value = true;
    try {
        const { response, data } = await getCommentReplies(
            props.comment.id,
            replyPagination.value.page,
            replyPagination.value.pageSize
        ) as any;

        if (response.status === 200 && response.data.code == 2000) {
            replyList.value = data.list || [];
            replyPagination.value.total = data.total || 0;
        } else {
            ElMessage.error(response.data.message || '加载回复失败');
        }
    } catch (error) {
        console.error('加载回复失败:', error);
        ElMessage.error('加载回复失败');
    } finally {
        repliesLoading.value = false;
    }
};

// 处理回复分页变化
const handleReplyPageChange = (page: number) => {
    replyPagination.value.page = page;
    loadReplies();
};

// 关闭对话框
const handleClose = () => {
    emit('update:visible', false);
    // 重置回复数据
    replyList.value = [];
    replyPagination.value = { page: 1, pageSize: 10, total: 0 };
};

// 编辑评论
const handleEdit = () => {
    if (props.comment) {
        emit('edit', props.comment);
        handleClose();
    }
};

// 查看帖子
const viewPost = (postId: string) => {
    emit('view-post', postId);
    handleClose();
};

// 查看父评论
const viewParentComment = (parentId: string) => {
    emit('view-parent', parentId);
    handleClose();
};

// 获取状态类型
const getStatusType = (status: number) => {
    switch (status) {
        case 0: return 'info';      // 草稿
        case 1: return 'warning';   // 待审核
        case 2: return 'success';   // 审核通过
        case -2: return 'danger';   // 审核拒绝
        case -4: return 'info';     // 已删除
        default: return 'info';
    }
};

// 获取状态文本
const getStatusText = (status: number) => {
    switch (status) {
        case 0: return '草稿';
        case 1: return '待审核';
        case 2: return '审核通过';
        case -2: return '审核拒绝';
        case -4: return '已删除';
        default: return '未知状态';
    }
};

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};
</script>

<style scoped lang="scss">
.comment-detail-dialog {
    .el-dialog__body {
        padding: 20px;
    }
}

.comment-detail-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 80vh;
    overflow-y: auto;
}

// 左右布局容器
.layout-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: stretch;

    .left-section {
        flex: 1;
    }

    .right-section {
        flex: 1;
    }

    // 响应式设计
    @media (max-width: 1200px) {
        flex-direction: column;

        .right-section {
            width: 100%;
        }
    }
}

// 主评论卡片样式
.main-comment-card {
    height: 100%;

    .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .user-info {
            display: flex;
            align-items: flex-start;
            gap: 12px;

            .user-avatar {
                flex-shrink: 0;
            }

            .user-details {
                .username {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .star-tag {
                        font-size: 12px;
                    }
                }

                .user-meta {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    margin-top: 4px;
                    font-size: 12px;
                    color: #909399;

                    .user-id {
                        color: #909399;
                    }

                    .create-time {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                    }
                }
            }
        }

        .comment-status {
            flex-shrink: 0;
        }
    }

    .comment-content {
        margin: 10px 0;
        height: calc(100% - 60px);

        .content-text {
            font-size: 15px;
            line-height: 1.6;
            color: #303133;
            white-space: pre-wrap;
            word-break: break-word;
            margin-bottom: 12px;
        }

        .comment-images {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 12px 0;

            .comment-image {
                width: 80px;
                height: 80px;
                border-radius: 6px;
                cursor: pointer;
                transition: transform 0.2s;

                &:hover {
                    transform: scale(1.05);
                }
            }
        }

        .comment-video {
            margin: 12px 0;

            .video-player {
                width: 100%;
                max-width: 400px;
                border-radius: 6px;
            }
        }
    }

    .comment-stats {
        display: flex;
        gap: 24px;
        padding: 12px 0;
        border-top: 1px solid #f0f0f0;

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #606266;

            .stat-icon {
                color: #909399;
            }

            .stat-label {
                font-size: 14px;
            }

            .stat-value {
                font-weight: 600;
                color: #303133;
            }
        }
    }
}

// 回复卡片样式
.replies-card {
    .replies-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            margin: 0;
            color: #303133;
            font-size: 16px;
        }
    }

    .replies-container {
        max-height: 400px;
        overflow-y: auto;
    }

    .reply-list {
        .reply-item {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;

            &:hover {
                background-color: #fafafa;
            }

            &:last-child {
                border-bottom: none;
            }

            .reply-header {
                display: flex;
                align-items: flex-start;
                gap: 10px;
                margin-bottom: 8px;

                .reply-avatar {
                    flex-shrink: 0;
                }

                .reply-user-info {
                    flex: 1;

                    .reply-username {
                        font-weight: 600;
                        color: #303133;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                    }

                    .reply-meta {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        margin-top: 2px;
                        font-size: 12px;
                        color: #909399;

                        .reply-to {
                            color: #409eff;
                        }
                    }
                }

                .reply-actions {
                    flex-shrink: 0;
                }
            }

            .reply-content {
                margin-left: 42px;
                color: #606266;
                line-height: 1.5;
                white-space: pre-wrap;
                word-break: break-word;
            }

            .reply-stats {
                margin-left: 42px;
                margin-top: 8px;

                .reply-likes {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    color: #909399;
                    font-size: 12px;
                }
            }
        }
    }

    .replies-pagination {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
    }
}

// 详细信息卡片样式
.detail-info-card {
    h3 {
        margin: 0;
        color: #303133;
        font-size: 16px;
    }

    .comment-id {
        font-family: 'Courier New', monospace;
        font-size: 12px;
    }

    .text-muted {
        color: #c0c4cc;
    }

    .reject-reason {
        color: #f56c6c;
        background-color: #fef0f0;
        padding: 8px;
        border-radius: 4px;
        border-left: 3px solid #f56c6c;
    }
}

// 对话框底部样式
.dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

// 响应式设计
@media (max-width: 768px) {
    .comment-detail-dialog {
        .el-dialog {
            width: 95% !important;
            margin: 5vh auto;
        }
    }

    .comment-detail-container {
        max-height: 60vh;
    }

    .main-comment-card .comment-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .comment-stats {
        flex-wrap: wrap;
        gap: 16px !important;
    }

    .replies-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start !important;
    }
}
</style>