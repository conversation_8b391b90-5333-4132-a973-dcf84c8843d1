<template>
  <div class="slinky-table">
    <!-- 表格头部操作栏 -->
    <div class="table-header" v-if="showHeader">
      <div class="header-left">
        <h3 v-if="title" class="table-title">{{ title }}</h3>
        <slot name="header-left"></slot>
      </div>
      <div class="header-right">
        <el-button 
          v-if="showAdd" 
          type="primary" 
          @click="handleAdd"
          :icon="Plus"
        >
          {{ addText }}
        </el-button>
        <el-button 
          v-if="showRefresh"
          @click="handleRefresh"
          :icon="Refresh"
        >
          刷新
        </el-button>
        <slot name="header-right"></slot>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="table-search" v-if="showSearch">
      <slot name="search"></slot>
    </div>

    <!-- 表格主体 -->
    <el-table
      v-loading="loading"
      :data="data"
      :border="border"
      :stripe="stripe"
      :size="size"
      :height="height"
      :max-height="maxHeight"
      :show-header="showTableHeader"
      :row-key="rowKey"
      :tree-props="treeProps"
      :row-class-name="(getRowClassName as any)"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @row-dblclick="handleRowDblClick"
      ref="tableRef"
    >
      <!-- 选择列 -->
      <el-table-column 
        v-if="showSelection"
        type="selection" 
        width="50" 
        align="center"
        :selectable="(selectable as any)"
      />
      
      <!-- 序号列 -->
      <el-table-column 
        v-if="showIndex"
        type="index" 
        label="序号" 
        width="60" 
        align="center"
        :index="getIndex"
      />
      
      <!-- 动态列 -->
      <template v-for="column in columns" :key="column.prop || column.key">
        <!-- 普通列 -->
        <el-table-column
          v-if="!column.children"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :align="column.align || 'left'"
          :header-align="column.headerAlign || 'center'"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showTooltip !== false"
          :formatter="(column.formatter as any)"
        >
          <template #default="scope" v-if="column.slot">
            <slot :name="column.slot" :row="scope.row" :column="column" :index="scope.$index"></slot>
          </template>
          <template #default="scope" v-else-if="column.render">
            <component :is="column.render" :row="scope.row" :column="column" :index="scope.$index"></component>
          </template>
          <template #default="scope" v-else-if="column.type === 'tag'">
            <el-tag 
              :type="getTagType(scope.row, column)" 
              class="slinky-tag"
            >
              {{ getTagText(scope.row, column) }}
            </el-tag>
          </template>
          <template #default="scope" v-else-if="column.type === 'image'">
            <el-image
              :src="getValue(scope.row, column.prop)"
              :style="{ width: column.imageWidth || '60px', height: column.imageHeight || '60px' }"
              fit="cover"
              :preview-src-list="[getValue(scope.row, column.prop)]"
              preview-teleported
            />
          </template>
          <template #default="scope" v-else-if="column.type === 'date'">
            {{ formatDate(getValue(scope.row, column.prop), column.dateFormat) }}
          </template>
        </el-table-column>
        
        <!-- 多级表头 -->
        <el-table-column
          v-else
          :label="column.label"
          :align="column.align || 'center'"
          :header-align="column.headerAlign || 'center'"
        >
          <template v-for="child in column.children" :key="child.prop">
            <el-table-column
              :prop="child.prop"
              :label="child.label"
              :width="child.width"
              :min-width="child.minWidth"
              :align="child.align || 'left'"
              :sortable="child.sortable"
              :show-overflow-tooltip="child.showTooltip !== false"
            >
              <template #default="scope" v-if="child.slot">
                <slot :name="child.slot" :row="scope.row" :column="child" :index="scope.$index"></slot>
              </template>
            </el-table-column>
          </template>
        </el-table-column>
      </template>
      
      <!-- 操作列 -->
      <el-table-column 
        v-if="showActions"
        label="操作" 
        :width="actionWidth"
        :fixed="actionFixed"
        align="center"
      >
        <template #default="scope">
          <div class="slinky-actions">
            <el-button
              v-if="showView"
              type="primary"
              link
              @click="handleView(scope.row, scope.$index)"
              :disabled="isActionDisabled('view', scope.row)"
            >
              {{ viewText }}
            </el-button>
            <el-button
              v-if="showEdit"
              type="primary"
              link
              @click="handleEdit(scope.row, scope.$index)"
              :disabled="isActionDisabled('edit', scope.row)"
            >
              {{ editText }}
            </el-button>
            <el-popconfirm
              v-if="showDelete"
              :title="getDeleteTitle(scope.row)"
              @confirm="handleDelete(scope.row, scope.$index)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  link
                  :disabled="isActionDisabled('delete', scope.row)"
                >
                  {{ deleteText }}
                </el-button>
              </template>
            </el-popconfirm>
            <slot name="actions" :row="scope.row" :index="scope.$index"></slot>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="table-pagination" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatDate as utilFormatDate } from '@/utils/date'
import { Plus, Refresh } from '@element-plus/icons-vue'
import type { TableColumnCtx } from 'element-plus'
import { ElTable } from 'element-plus'
import { computed, ref } from 'vue'

// 定义类型
interface TableColumn {
  prop?: string
  key?: string
  label: string
  width?: string | number
  minWidth?: string | number
  fixed?: boolean | string
  align?: 'left' | 'center' | 'right'
  headerAlign?: 'left' | 'center' | 'right'
  sortable?: boolean | string
  showTooltip?: boolean
  formatter?: Function
  slot?: string
  render?: any
  type?: 'text' | 'tag' | 'image' | 'date'
  tagOptions?: { value: any; label: string; type: string }[]
  imageWidth?: string
  imageHeight?: string
  dateFormat?: string
  children?: TableColumn[]
}

interface Props {
  // 数据相关
  data: any[]
  loading?: boolean
  
  // 表格配置
  columns: TableColumn[]
  border?: boolean
  stripe?: boolean
  size?: 'large' | 'default' | 'small'
  height?: string | number
  maxHeight?: string | number
  showTableHeader?: boolean
  rowKey?: string | Function
  treeProps?: any
  rowClassName?: string | Function
  
  // 表头配置
  title?: string
  showHeader?: boolean
  showAdd?: boolean
  addText?: string
  showRefresh?: boolean
  
  // 搜索配置
  showSearch?: boolean
  
  // 选择配置
  showSelection?: boolean
  selectable?: Function
  
  // 序号配置
  showIndex?: boolean
  indexMethod?: Function
  
  // 操作配置
  showActions?: boolean
  actionWidth?: string | number
  actionFixed?: boolean | string
  showView?: boolean
  viewText?: string
  showEdit?: boolean
  editText?: string
  showDelete?: boolean
  deleteText?: string
  actionDisabled?: Function
  
  // 分页配置
  showPagination?: boolean
  total?: number
  currentPage?: number
  pageSize?: number
  pageSizes?: number[]
  paginationLayout?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  border: true,
  stripe: true,
  size: 'default',
  showTableHeader: true,
  showHeader: true,
  showAdd: true,
  addText: '新增',
  showRefresh: true,
  showSearch: false,
  showSelection: false,
  showIndex: true,
  showActions: true,
  actionWidth: 180,
  actionFixed: 'right',
  showView: true,
  viewText: '详情',
  showEdit: true,
  editText: '编辑',
  showDelete: true,
  deleteText: '删除',
  showPagination: true,
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: () => [10, 20, 50, 100],
  paginationLayout: 'total, sizes, prev, pager, next, jumper'
})

// 事件定义
interface Emits {
  (e: 'add'): void
  (e: 'refresh'): void
  (e: 'view', row: any, index: number): void
  (e: 'edit', row: any, index: number): void
  (e: 'delete', row: any, index: number): void
  (e: 'selection-change', selection: any[]): void
  (e: 'sort-change', data: any): void
  (e: 'row-click', row: any, column: TableColumnCtx<any>, event: Event): void
  (e: 'row-dblclick', row: any, column: TableColumnCtx<any>, event: Event): void
  (e: 'size-change', size: number): void
  (e: 'current-change', page: number): void
  (e: 'update:currentPage', page: number): void
  (e: 'update:pageSize', size: number): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const tableRef = ref<InstanceType<typeof ElTable>>()
const currentPage = computed({
  get: () => props.currentPage,
  set: (val) => emit('update:currentPage', val)
})
const pageSize = computed({
  get: () => props.pageSize,
  set: (val) => emit('update:pageSize', val)
})

// 工具方法
const getValue = (row: any, prop: string) => {
  if (!prop) return ''
  return prop.split('.').reduce((obj, key) => obj?.[key], row)
}

const getTagType = (row: any, column: TableColumn) => {
  if (!column.tagOptions) return 'primary'
  const value = getValue(row, column.prop!)
  const option = column.tagOptions.find(opt => opt.value === value)
  return option?.type || 'primary'
}

const getTagText = (row: any, column: TableColumn) => {
  if (!column.tagOptions) return getValue(row, column.prop!)
  const value = getValue(row, column.prop!)
  const option = column.tagOptions.find(opt => opt.value === value)
  return option?.label || value
}

const formatDate = (date: any, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) return ''
  return utilFormatDate(date, format)
}

const getIndex = (index: number) => {
  if (props.indexMethod) {
    return props.indexMethod(index)
  }
  return (currentPage.value - 1) * pageSize.value + index + 1
}

const getRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
  if (typeof props.rowClassName === 'function') {
    return props.rowClassName({ row, rowIndex })
  }
  return props.rowClassName || ''
}

const isActionDisabled = (action: string, row: any) => {
  if (props.actionDisabled) {
    return props.actionDisabled(action, row)
  }
  return false
}

const getDeleteTitle = (row: any) => {
  return `确定删除"${row.name || row.title || '此项'}"吗？`
}

// 事件处理
const handleAdd = () => {
  emit('add')
}

const handleRefresh = () => {
  emit('refresh')
}

const handleView = (row: any, index: number) => {
  emit('view', row, index)
}

const handleEdit = (row: any, index: number) => {
  emit('edit', row, index)
}

const handleDelete = (row: any, index: number) => {
  emit('delete', row, index)
}

const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

const handleSortChange = (data: any) => {
  emit('sort-change', data)
}

const handleRowClick = (row: any, column: TableColumnCtx<any>, event: Event) => {
  emit('row-click', row, column, event)
}

const handleRowDblClick = (row: any, column: TableColumnCtx<any>, event: Event) => {
  emit('row-dblclick', row, column, event)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('size-change', size)
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  emit('current-change', page)
}

// 公开方法
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const toggleRowSelection = (row: any, selected?: boolean) => {
  tableRef.value?.toggleRowSelection(row, selected)
}

const toggleAllSelection = () => {
  tableRef.value?.toggleAllSelection()
}

const setCurrentRow = (row: any) => {
  tableRef.value?.setCurrentRow(row)
}

const scrollTo = (options: any) => {
  tableRef.value?.scrollTo(options)
}

defineExpose({
  clearSelection,
  toggleRowSelection,
  toggleAllSelection,
  setCurrentRow,
  scrollTo,
  tableRef
})
</script>

<style scoped lang="scss">
@import '../styles/table.scss';

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 0;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .table-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .header-right {
    display: flex;
    gap: 12px;
  }
}

.table-search {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: var(--el-card-border-radius);
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding: 16px 0;
}
</style> 