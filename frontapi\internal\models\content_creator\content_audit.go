package content_creator

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type ContentAudit struct {
	models.BaseModel
	ContentType string         `gorm:"column:content_type;type:string;not null;comment:内容类型" json:"content_type"`                         // 内容类型
	ContentID   string         `gorm:"column:content_id;type:string;not null;comment:内容ID" json:"content_id"`                             // 内容ID
	UserID      string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                   // 用户ID
	AdminID     string         `gorm:"column:admin_id;type:string;comment:审核管理员ID" json:"admin_id"`                                       // 审核管理员ID
	Status      string         `gorm:"column:status;type:string;not null;default:'pending';comment:状态" json:"status"`                     // 状态
	Reason      string         `gorm:"column:reason;type:string;comment:拒绝理由" json:"reason"`                                              // 拒绝理由
	AuditTime   types.JSONTime `gorm:"column:audit_time;type:datetime;comment:审核时间" json:"audit_time"`                                    // 审核时间
}

func (ContentAudit) TableName() string {
	return "ly_content_audits"
}
