<template>
  <div class="demo-container">
    <div class="video-container">
      <ShortPlayer
        :video="currentVideo"
        :autoplay="true"
        :muted="false"
        :can-go-previous="currentIndex > 0"
        :can-go-next="currentIndex < videoList.length - 1"
        :is-user-logged-in="true"
        :is-danmu-open="true"
        @like="handleLike"
        @comment="handleComment"
        @favorite="handleFavorite"
        @share="handleShare"
        @follow="handleFollow"
        @previous="handlePrevious"
        @next="handleNext"
        @video-end="handleVideoEnd"
      />
    </div>
    
    <!-- 视频列表指示器 -->
    <div class="video-indicator">
      <span>{{ currentIndex + 1 }} / {{ videoList.length }}</span>
      <div class="video-title">{{ currentVideo?.title }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ShortPlayer from './index.vue'
import type { ShortVideo } from '../../types/shorts'
import { useUserStore } from '../../stores/user'

// 模拟视频数据
const videoList = ref<ShortVideo[]>([
  {
    id: '1',
    title: '第一个精彩视频',
    description: '这是第一个很棒的短视频',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    cover: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg',
    creator_id: 'creator-a',
    creator_name: '创作者A',
    creator_avatar: 'https://via.placeholder.com/60x60/ff6b6b/ffffff?text=A',
    like_count: 1200,
    comment_count: 89,
    favorite_count: 156,
    share_count: 45,
    view_count: 5600,
    duration: 120,
    tags: ['精彩', '推荐'],
    tags_json: '["精彩", "推荐"]',
    src_type: 1,
    is_paid: 0,
    is_featured: 1,
    price: 0,
    upload_time: '2024-01-01T00:00:00Z',
    status: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    title: '第二个精彩视频',
    description: '这是第二个很棒的短视频',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    cover: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ElephantsDream.jpg',
    creator_id: 'creator-b',
    creator_name: '创作者B',
    creator_avatar: 'https://via.placeholder.com/60x60/4ecdc4/ffffff?text=B',
    like_count: 890,
    comment_count: 67,
    favorite_count: 123,
    share_count: 34,
    view_count: 3400,
    duration: 90,
    tags: ['有趣', '娱乐'],
    tags_json: '["有趣", "娱乐"]',
    src_type: 1,
    is_paid: 0,
    is_featured: 0,
    price: 0,
    upload_time: '2024-01-02T00:00:00Z',
    status: 1,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z'
  },
  {
    id: '3',
    title: '第三个精彩视频',
    description: '这是第三个很棒的短视频',
    url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    cover: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerBlazes.jpg',
    creator_id: 'creator-c',
    creator_name: '创作者C',
    creator_avatar: 'https://via.placeholder.com/60x60/45b7d1/ffffff?text=C',
    like_count: 2100,
    comment_count: 145,
    favorite_count: 289,
    share_count: 78,
    view_count: 8900,
    duration: 150,
    tags: ['热门', '推荐'],
    tags_json: '["热门", "推荐"]',
    src_type: 1,
    is_paid: 0,
    is_featured: 1,
    price: 0,
    upload_time: '2024-01-03T00:00:00Z',
    status: 1,
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z'
  }
])

const currentIndex = ref(0)
const currentVideo = computed(() => videoList.value[currentIndex.value])

const userStore = useUserStore()
const isLiked = ref(false)
const likeCount = ref(0)
const isFavorited = ref(false)
const favoriteCount = ref(0)
const showComments = ref(false)

// 事件处理
const handleLike = () => {
  if (!userStore.requireAuth()) {
    return
  }
  isLiked.value = !isLiked.value
  if (isLiked.value) {
    likeCount.value++
  } else {
    likeCount.value--
  }
}

const handleComment = () => {
  if (!userStore.requireAuth()) {
    return
  }
  showComments.value = !showComments.value
}

const handleFavorite = () => {
  if (!userStore.requireAuth()) {
    return
  }
  isFavorited.value = !isFavorited.value
  if (isFavorited.value) {
    favoriteCount.value++
  } else {
    favoriteCount.value--
  }
}

const handleShare = () => {
  console.log('分享视频:', currentVideo.value?.title)
  // 这里可以添加分享逻辑
}

const handleFollow = () => {
  console.log('关注创作者:', currentVideo.value?.creator_name)
  // 这里可以添加关注逻辑
}

const handlePrevious = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
    console.log('切换到上一个视频:', currentVideo.value?.title)
  }
}

const handleNext = () => {
  if (currentIndex.value < videoList.value.length - 1) {
    currentIndex.value++
    console.log('切换到下一个视频:', currentVideo.value?.title)
  }
}

const handleVideoEnd = () => {
  console.log('视频播放结束')
  // 自动播放下一个视频
  if (currentIndex.value < videoList.value.length - 1) {
    handleNext()
  }
}
</script>

<style scoped lang="scss">
.demo-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
}

.video-container {
  width: 100%;
  height: 100%;
}

.video-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  
  span {
    font-size: 14px;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
  }
  
  .video-title {
    font-size: 12px;
    opacity: 0.8;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .video-indicator {
    top: 15px;
    left: 15px;
    padding: 8px 12px;
    
    span {
      font-size: 12px;
    }
    
    .video-title {
      font-size: 11px;
      max-width: 150px;
    }
  }
}
</style> 