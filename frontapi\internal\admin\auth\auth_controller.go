package auth

import (
	"log"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"

	"frontapi/internal/admin"
	permSrv "frontapi/internal/service/permission"
	jwtAuth "frontapi/pkg/auth"
	"frontapi/pkg/redis"
)

type AuthController struct {
	admin.BaseController
	adminUserService permSrv.AdminUserService
	casbinService    permSrv.CasbinService
}

// NewAuthController 创建认证控制器
func NewAuthController(adminUserService permSrv.AdminUserService) *AuthController {
	return &AuthController{
		adminUserService: adminUserService,
	}
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username   string `json:"username" validate:"required"`
	Password   string `json:"password" validate:"required"`
	RememberMe bool   `json:"rememberMe"`
}

// LoginResponse 登录响应结构
type LoginResponse struct {
	ID           int    `json:"id"`
	Username     string `json:"username"`
	Nickname     string `json:"nickname"`
	Avatar       string `json:"avatar"`
	AccessToken  string `json:"accessToken"`
	RefreshToken string `json:"refreshToken"`
}

// Login 处理登录请求
func (u *AuthController) Login(c *fiber.Ctx) error {
	log.Printf("[Auth] 收到登录请求: %s", string(c.Body()))

	// 获取请求信息（使用DataWrapper格式）
	reqInfo := u.GetRequestInfo(c)

	// 获取登录参数
	username := reqInfo.Get("username").GetString()
	password := reqInfo.Get("password").GetString()
	rememberMe := reqInfo.Get("rememberMe").GetBool()

	log.Printf("[Auth] 解析后的参数: username=%s, password=*****, rememberMe=%v", username, rememberMe)

	if username == "" || password == "" {
		return u.BadRequest(c, "用户名或密码不能为空", nil)
	}

	// 使用AdminUserService验证用户
	user, err := u.adminUserService.Login(c.Context(), username, password)
	if err != nil {
		return u.BadRequest(c, "用户名或密码错误", nil)
	}

	// 生成JWT token
	tokenExpiration := 24 * time.Hour // 默认24小时
	if rememberMe {
		tokenExpiration = 7 * 24 * time.Hour // 记住我：7天
	}

	accessToken, err := jwtAuth.GenerateToken(
		strconv.Itoa(user.ID),
		user.Username,
		tokenExpiration,
	)
	if err != nil {
		return u.InternalServerError(c, "生成访问令牌失败")
	}

	refreshToken, err := jwtAuth.GenerateToken(
		strconv.Itoa(user.ID),
		user.Username,
		30*24*time.Hour, // 刷新令牌30天
	)
	if err != nil {
		return u.InternalServerError(c, "生成刷新令牌失败")
	}

	// 准备用户信息
	userInfo := &redis.AdminUserInfo{
		UserID:      user.ID,
		Username:    user.Username,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Email:       user.Email,
		Phone:       user.Phone,
		Roles:       []string{}, // TODO: 从Casbin获取角色
		Permissions: []string{}, // TODO: 从Casbin获取权限
		LoginTime:   time.Now().Unix(),
		LoginIP:     c.IP(),
	}

	// 存储会话到Redis
	if err := redis.SetAdminUserSession(user.ID, accessToken, userInfo, tokenExpiration); err != nil {
		log.Printf("存储用户会话失败: %v", err)
		return u.InternalServerError(c, "存储用户会话失败")
	}

	// 更新用户登录信息
	if err := u.adminUserService.UpdateLoginInfo(c.Context(), user.ID, c.IP()); err != nil {
		log.Printf("更新用户登录信息失败: %v", err)
		// 不影响登录流程，只记录日志
	}

	// 设置上下文
	c.Locals("userId", strconv.Itoa(user.ID))
	c.Locals("user_id", strconv.Itoa(user.ID))
	c.Locals("username", user.Username)

	// 构造响应
	loginData := LoginResponse{
		ID:           user.ID,
		Username:     user.Username,
		Nickname:     user.Nickname,
		Avatar:       user.Avatar,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	}

	return c.Status(200).JSON(fiber.Map{
		"data":    loginData,
		"success": true,
		"code":    2000,
		"msg":     "登录成功",
		"message": "登录成功",
	})
}

// Logout 处理退出登录请求
func (u *AuthController) Logout(c *fiber.Ctx) error {
	userID := c.Locals("userId")
	if userID == nil {
		return u.SuccessWithMessage(c, "退出登录成功")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return u.SuccessWithMessage(c, "退出登录成功")
	}

	userIDInt, err := strconv.Atoi(userIDStr)
	if err != nil {
		return u.SuccessWithMessage(c, "退出登录成功")
	}

	// 从Redis中删除用户会话
	if err := redis.DeleteAdminUserSession(userIDInt); err != nil {
		log.Printf("从Redis删除用户会话失败: %v", err)
		// 不影响退出流程，只记录日志
	}

	return u.SuccessWithMessage(c, "退出登录成功")
}

// GetUserInfo 获取用户信息
func (u *AuthController) GetUserInfo(c *fiber.Ctx) error {
	userID := c.Locals("userId")
	if userID == nil {
		return u.Unauthorized(c, "用户未登录")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return u.BadRequest(c, "用户信息格式错误", nil)
	}

	userIDInt, err := strconv.Atoi(userIDStr)
	if err != nil {
		return u.BadRequest(c, "用户ID格式错误", nil)
	}

	// 从Redis获取用户信息
	userInfo, err := redis.GetAdminUserInfo(userIDInt)
	if err != nil {
		// 如果Redis中没有用户信息，从数据库重新获取
		log.Printf("从Redis获取用户信息失败: %v", err)

		user, err := u.adminUserService.GetByID(c.Context(), userIDInt, false)
		if err != nil {
			return u.Unauthorized(c, "用户未登录")
		}

		// 重新构造用户信息
		userInfo = &redis.AdminUserInfo{
			UserID:      user.ID,
			Username:    user.Username,
			Nickname:    user.Nickname,
			Avatar:      user.Avatar,
			Email:       user.Email,
			Phone:       user.Phone,
			Roles:       []string{}, // TODO: 从Casbin获取角色
			Permissions: []string{}, // TODO: 从Casbin获取权限
			LoginTime:   time.Now().Unix(),
			LoginIP:     c.IP(),
		}

		// 重新存储到Redis
		if err := redis.SetAdminUserInfo(userIDInt, userInfo, 24*time.Hour); err != nil {
			log.Printf("重新存储用户信息到Redis失败: %v", err)
		}
	}

	// 延长会话时间
	if err := redis.ExtendAdminSession(userIDInt, 24*time.Hour); err != nil {
		log.Printf("延长用户会话失败: %v", err)
	}

	return c.JSON(fiber.Map{
		"code":    2000,
		"message": "获取用户信息成功",
		"data": fiber.Map{
			"userId":      userInfo.UserID,
			"userName":    userInfo.Username,
			"nickname":    userInfo.Nickname,
			"avatar":      userInfo.Avatar,
			"email":       userInfo.Email,
			"phone":       userInfo.Phone,
			"roles":       userInfo.Roles,
			"permissions": userInfo.Permissions,
		},
	})
}

// RefreshToken 刷新访问令牌
func (u *AuthController) RefreshToken(c *fiber.Ctx) error {
	var req struct {
		RefreshToken string `json:"refreshToken" validate:"required"`
	}

	if err := c.BodyParser(&req); err != nil {
		return u.BadRequest(c, "请求参数错误", nil)
	}

	// 解析刷新令牌
	claims, err := jwtAuth.ParseToken(req.RefreshToken)
	if err != nil {
		return u.Unauthorized(c, "用户未登录")
	}

	userIDInt, err := strconv.Atoi(claims.UserID)
	if err != nil {
		return u.BadRequest(c, "用户ID格式错误", nil)
	}

	// 检查用户是否存在
	user, err := u.adminUserService.GetByID(c.Context(), userIDInt, false)
	if err != nil {
		return u.Unauthorized(c, "用户未登录")
	}

	// 生成新的访问令牌
	newAccessToken, err := jwtAuth.GenerateToken(
		strconv.Itoa(user.ID),
		user.Username,
		24*time.Hour,
	)
	if err != nil {
		return u.InternalServerError(c, "生成新访问令牌失败")
	}

	// 更新Redis中的令牌
	if err := redis.SetAdminToken(userIDInt, newAccessToken, 24*time.Hour); err != nil {
		log.Printf("更新Redis令牌失败: %v", err)
	}

	return u.Success(c, fiber.Map{
		"accessToken": newAccessToken,
	})
}

// Test 测试认证状态
func (u *AuthController) Test(c *fiber.Ctx) error {
	userID := c.Locals("userId")
	username := c.Locals("username")

	if userID == nil || username == nil {
		return u.Unauthorized(c, "用户未登录")
	}

	return u.Success(c, fiber.Map{
		"userId":   userID,
		"username": username,
	})
}

// GetCodes 获取用户权限代码
func (u *AuthController) GetCodes(c *fiber.Ctx) error {
	userID := c.Locals("userId")
	if userID == nil {
		return u.Unauthorized(c, "用户未登录")
	}

	// TODO: 从Casbin获取用户权限代码
	codes := []string{"AC_100100", "AC_100110", "AC_100120", "AC_100010"}

	return c.JSON(fiber.Map{
		"code":    0,
		"message": "获取用户权限代码成功",
		"data": fiber.Map{
			"codes":    codes,
			"username": c.Locals("username"),
		},
	})
}
