<template>
  <div class="chapter-table-container">
    <!-- 常规模式下的表格 -->
    <el-table
      v-loading="loading"
      :data="chapterList"
      border
      style="width: 100%"
      row-key="id"
    >
      <el-table-column type="index" width="55" label="#"></el-table-column>
      <el-table-column prop="title" label="章节标题" min-width="150" />
      <el-table-column prop="book_name" label="所属电子书" min-width="120" />
      <el-table-column prop="chapter_number" label="章节序号" width="100" align="center">
        <template #default="{ row }">
          {{ row.chapter_number || row.sort_order }}
        </template>
      </el-table-column>
      <el-table-column prop="word_count" label="字数" width="80" align="center" />
      <el-table-column prop="is_locked" label="付费" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_locked === 1 ? 'danger' : 'success'">
            {{ row.is_locked === 1 ? '付费' : '免费' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="price" label="价格" width="100" align="center">
        <template #default="{ row }">
          {{ row.is_locked === 1 ? `¥${row.price}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="170" align="center">
        <template #default="{ row }">
          {{ formatDate(row.create_time || row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="$emit('edit', row)">编辑</el-button>
          <el-button type="danger" link @click="$emit('delete', row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="showPagination" class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onSizeChange"
        @current-change="onPageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue';
import type { BookChapter } from '@/types/books';

interface Props {
  loading: boolean;
  chapterList: BookChapter[];
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
  };
  showPagination?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  pagination: () => ({ page: 1, pageSize: 10, total: 0 }),
  showPagination: true
});

const emit = defineEmits([
  'edit', 
  'delete', 
  'page-change',
  'size-change',
  'refresh'
]);

// 分页处理
const onSizeChange = (size: number) => {
  emit('size-change', size);
};

const onPageChange = (page: number) => {
  emit('page-change', page);
};

// 日期格式化
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 8);
};
</script>

<style scoped>
.chapter-table-container {
  width: 100%;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}
</style>