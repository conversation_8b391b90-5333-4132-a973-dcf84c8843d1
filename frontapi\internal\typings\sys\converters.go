package sys

import (
	"frontapi/internal/models/sys"
)

// ConvertTableInfo 转换表信息
func ConvertTableInfo(sysTableInfo sys.TableInfo) TableInfo {
	return TableInfo{
		TableName:    sysTableInfo.TableName,
		TableComment: sysTableInfo.TableComment,
		Engine:       sysTableInfo.Engine,
		TableRows:    sysTableInfo.TableRows,
		CreateTime:   sysTableInfo.CreateTime,
	}
}

// ConvertTableInfoList 转换表信息列表
func ConvertTableInfoList(sysTableInfos []sys.TableInfo) []TableInfo {
	result := make([]TableInfo, len(sysTableInfos))
	for i, sysTableInfo := range sysTableInfos {
		result[i] = ConvertTableInfo(sysTableInfo)
	}
	return result
}

// ConvertColumnInfo 转换列信息
func ConvertColumnInfo(sysColumnInfo sys.ColumnInfo) ColumnInfo {
	return ColumnInfo{
		ColumnName:      sysColumnInfo.ColumnName,
		DataType:        sysColumnInfo.DataType,
		ColumnType:      sysColumnInfo.ColumnType,
		IsNullable:      sysColumnInfo.IsNullable,
		ColumnDefault:   sysColumnInfo.ColumnDefault,
		ColumnComment:   sysColumnInfo.ColumnComment,
		ColumnKey:       sysColumnInfo.ColumnKey,
		Extra:           sysColumnInfo.Extra,
		OrdinalPosition: sysColumnInfo.OrdinalPosition,
	}
}

// ConvertColumnInfoList 转换列信息列表
func ConvertColumnInfoList(sysColumnInfos []sys.ColumnInfo) []ColumnInfo {
	result := make([]ColumnInfo, len(sysColumnInfos))
	for i, sysColumnInfo := range sysColumnInfos {
		result[i] = ConvertColumnInfo(sysColumnInfo)
	}
	return result
}

// ConvertForeignKeyInfo 转换外键信息
func ConvertForeignKeyInfo(sysForeignKeyInfo sys.ForeignKeyInfo) ForeignKeyInfo {
	return ForeignKeyInfo{
		ConstraintName:   sysForeignKeyInfo.ConstraintName,
		TableName:        sysForeignKeyInfo.TableName,
		ColumnName:       sysForeignKeyInfo.ColumnName,
		ReferencedTable:  sysForeignKeyInfo.ReferencedTableName,
		ReferencedColumn: sysForeignKeyInfo.ReferencedColumnName,
		UpdateRule:       sysForeignKeyInfo.UpdateRule,
		DeleteRule:       sysForeignKeyInfo.DeleteRule,
	}
}

// ConvertForeignKeyInfoList 转换外键信息列表
func ConvertForeignKeyInfoList(sysForeignKeyInfos []sys.ForeignKeyInfo) []ForeignKeyInfo {
	result := make([]ForeignKeyInfo, len(sysForeignKeyInfos))
	for i, sysForeignKeyInfo := range sysForeignKeyInfos {
		result[i] = ConvertForeignKeyInfo(sysForeignKeyInfo)
	}
	return result
}

// ConvertTableDetailResponse 转换表详情响应
func ConvertTableDetailResponse(sysTableDetail *sys.TableDetailResponse) TableDetailResponse {
	return TableDetailResponse{
		TableInfo:   ConvertTableInfo(sysTableDetail.TableInfo),
		Columns:     ConvertColumnInfoList(sysTableDetail.Columns),
		ForeignKeys: ConvertForeignKeyInfoList(sysTableDetail.ForeignKeys),
	}
}

// ConvertMockDataRequest 转换Mock数据请求
func ConvertMockDataRequest(req MockDataRequest) sys.MockDataRequest {
	return sys.MockDataRequest{
		TableName: req.TableName,
		Count:     req.Count,
		MockRules: convertRulesToStringMap(req.Rules),
	}
}

// convertRulesToStringMap 将interface{}规则转换为string规则
func convertRulesToStringMap(rules map[string]interface{}) map[string]string {
	if rules == nil {
		return nil
	}
	result := make(map[string]string)
	for k, v := range rules {
		if str, ok := v.(string); ok {
			result[k] = str
		}
	}
	return result
}

// ConvertMockDataResponse 转换Mock数据响应
func ConvertMockDataResponse(sysResponse *sys.MockDataResponse) MockDataResponse {
	return MockDataResponse{
		TableName: sysResponse.TableName,
		Count:     sysResponse.Count,
		Data:      sysResponse.Data,
		Success:   true, // 默认成功
		Message:   "数据生成成功",
	}
}

// ConvertForeignKeyDataResponse 转换外键数据响应
func ConvertForeignKeyDataResponse(values []string) ForeignKeyDataResponse {
	return ForeignKeyDataResponse{
		Values: values,
	}
}
