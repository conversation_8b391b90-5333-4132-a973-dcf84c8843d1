package pictures

import (
	"context"
	"errors"
	"frontapi/pkg/types"
	"time"

	"github.com/google/uuid"

	"frontapi/internal/models/pictures"
	repo "frontapi/internal/repository/pictures"
	"frontapi/internal/service/base"
	pictureValidator "frontapi/internal/validation/pictures"
)

// PictureCategoryService 图片分类服务接口
type PictureCategoryService interface {
	base.IExtendedService[pictures.PictureCategory]
	CreateCategory(ctx context.Context, req *pictureValidator.PictureCategoryCreateRequest) (string, error)
	UpdateCategory(ctx context.Context, id string, req *pictureValidator.PictureCategoryUpdateRequest) error
	GetCategoryList(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureCategory, int64, error)
}

// pictureCategoryService 图片分类服务实现
type pictureCategoryService struct {
	*base.ExtendedService[pictures.PictureCategory]
	categoryRepo repo.PictureCategoryRepository
}

// NewPictureCategoryService 创建图片分类服务实例
func NewPictureCategoryService(categoryRepo repo.PictureCategoryRepository) PictureCategoryService {
	return &pictureCategoryService{
		ExtendedService: base.NewExtendedService[pictures.PictureCategory](categoryRepo, "picture_category"),
		categoryRepo:    categoryRepo,
	}
}

// CreateCategory 创建图片分类
func (s *pictureCategoryService) CreateCategory(ctx context.Context, req *pictureValidator.PictureCategoryCreateRequest) (string, error) {
	// 检查名称是否已存在
	existingCategory, err := s.categoryRepo.FindByName(ctx, req.Name)
	if err == nil && existingCategory != nil {
		return "", errors.New("分类名称已存在")
	}

	// 生成唯一ID
	categoryID := uuid.New().String()

	// 创建分类记录
	category := &pictures.PictureCategory{
		Code: req.Code,
	}

	// 设置继承的字段
	category.SetID(categoryID)
	category.SetName(req.Name)
	category.SortOrder = req.SortOrder
	category.SetDescription(req.Description)
	category.SetStatus(1)
	category.SetCreatedAt(types.JSONTime(time.Now()))
	category.SetUpdatedAt(types.JSONTime(time.Now()))

	// 保存分类
	if err := s.categoryRepo.Create(ctx, category); err != nil {
		return "", err
	}

	return categoryID, nil
}

// UpdateCategory 更新图片分类
func (s *pictureCategoryService) UpdateCategory(ctx context.Context, id string, req *pictureValidator.PictureCategoryUpdateRequest) error {
	// 获取现有分类
	category, err := s.BaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}

	// 更新字段
	if req.Name != nil {
		category.SetName(*req.Name)
	}
	if req.Description != nil {
		category.SetDescription(*req.Description)
	}
	if req.SortOrder != nil {
		category.SortOrder = *req.SortOrder
	}
	if req.Status != nil {
		category.Status = *req.Status
	}

	category.UpdatedAt = types.JSONTime(time.Now())

	// 保存更新
	return s.BaseService.Update(ctx, category)
}

// GetCategoryList 根据条件获取分类列表，用于管理后台
func (s *pictureCategoryService) GetCategoryList(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureCategory, int64, error) {
	return s.categoryRepo.List(ctx, condition, orderBy, page, pageSize)
}
