# 前后端字段匹配总结

## 概述

本文档总结了用户列表功能中前端与后端字段的完整匹配情况，确保数据传递的一致性。

## 字段映射表

| 前端字段 (Vue) | 后端字段 (Go) | 数据类型 | 说明 |
|---------------|-------------|---------|------|
| `keyword` | `keyword` | string | 关键字搜索，支持用户名、昵称、邮箱、手机号、简介 |
| `status` | `status` | number | 用户状态：1-正常，0-禁用，-999-不过滤 |
| `user_type` | `user_type` | number | 用户类型：1-普通用户，2-VIP用户，3-管理员，-999-不过滤 |
| `is_content_creator` | `is_content_creator` | number | 是否为内容创作者：1-是，0-否，-999-不过滤 |
| `start_date` | `reg_time_start` | string | 注册开始时间，格式：YYYY-MM-DD |
| `end_date` | `reg_time_end` | string | 注册结束时间，格式：YYYY-MM-DD |

## 前端实现 (Vue)

### 搜索表单定义

```typescript
const searchForm = reactive({
  keyword: '',
  status: undefined as number | undefined,
  user_type: undefined as number | undefined,
  is_content_creator: undefined as number | undefined,
  start_date: '',
  end_date: ''
});
```

### 搜索字段配置

```typescript
const searchFields = ref([
  {
    prop: 'keyword',
    label: '关键词',
    type: 'input',
    placeholder: '请输入用户名或昵称'
  },
  {
    prop: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '正常', value: 1 },
      { label: '禁用', value: 0 }
    ]
  },
  {
    prop: 'user_type',
    label: '用户类型',
    type: 'select',
    options: [
      { label: '普通用户', value: 1 },
      { label: 'VIP用户', value: 2 },
      { label: '管理员', value: 3 }
    ]
  },
  {
    prop: 'is_content_creator',
    label: '内容创作者',
    type: 'select',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 0 }
    ]
  },
  {
    prop: 'start_date',
    label: '开始时间',
    type: 'date',
    valueFormat: 'YYYY-MM-DD'
  },
  {
    prop: 'end_date',
    label: '结束时间',
    type: 'date',
    valueFormat: 'YYYY-MM-DD'
  }
]);
```

### API 请求参数

```typescript
const params = {
  data: {
    keyword: searchForm.keyword,
    status: searchForm.status,
    user_type: searchForm.user_type,
    is_content_creator: searchForm.is_content_creator,
    start_date: searchForm.start_date,
    end_date: searchForm.end_date
  },
  page: {
    pageNo: pagination.page,
    pageSize: pagination.pageSize
  }
};
```

## 后端实现 (Go)

### 控制器参数获取

```go
// 查询参数
keyword := reqInfo.Get("keyword").GetString()
status := -999
err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)

// 获取用户类型过滤条件
userType := -999
_ = c.GetIntegerValueWithDataWrapper(ctx, "user_type", &userType)

// 获取内容创作者过滤条件
isContentCreator := -999
_ = c.GetIntegerValueWithDataWrapper(ctx, "is_content_creator", &isContentCreator)

// 获取时间范围参数 - 前端发送的是start_date和end_date
startDate := reqInfo.Get("start_date").GetString()
endDate := reqInfo.Get("end_date").GetString()

// 构建查询条件，映射到用户仓库中ApplyConditions期望的字段名
condition := map[string]interface{}{
    "keyword":            keyword,
    "status":             status,
    "user_type":          userType,
    "is_content_creator": isContentCreator,
    "reg_time_start":     startDate, // 映射前端的start_date到reg_time_start
    "reg_time_end":       endDate,   // 映射前端的end_date到reg_time_end
}
```

### 仓库条件应用

```go
func (r *userRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    // 关键字搜索
    if condition["keyword"] != nil && condition["keyword"] != "" {
        keyword := condition["keyword"].(string)
        query = query.Where("username LIKE ? OR nickname LIKE ? OR email LIKE ? OR phone LIKE ? OR bio LIKE ?",
            "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
    }

    // 状态过滤
    if condition["status"] != nil && condition["status"] != "" {
        if status, ok := condition["status"].(int); ok && status > -999 {
            query = query.Where("status = ?", status)
        }
    }

    // 用户类型过滤
    if condition["user_type"] != nil && condition["user_type"] != "" {
        if userType, ok := condition["user_type"].(int); ok && userType > -999 {
            query = query.Where("user_type = ?", userType)
        }
    }

    // 内容创作者过滤
    if condition["is_content_creator"] != nil && condition["is_content_creator"] != "" {
        if isContentCreator, ok := condition["is_content_creator"].(int); ok && isContentCreator > -999 {
            query = query.Where("is_content_creator = ?", isContentCreator)
        }
    }

    // 注册时间范围
    if condition["reg_time_start"] != nil && condition["reg_time_start"] != "" {
        regTimeStart := condition["reg_time_start"].(string)
        query = query.Where("reg_time >= ?", regTimeStart)
    }
    if condition["reg_time_end"] != nil && condition["reg_time_end"] != "" {
        regTimeEnd := condition["reg_time_end"].(string)
        query = query.Where("reg_time <= ?", regTimeEnd)
    }

    return query
}
```

## 数据流转过程

1. **前端用户操作**: 用户在界面上输入搜索条件
2. **前端数据收集**: Vue组件收集表单数据到 `searchForm`
3. **API请求发送**: 将 `searchForm` 数据打包发送到后端API
4. **后端参数解析**: Go控制器解析请求参数
5. **条件映射**: 控制器将前端字段映射到后端查询条件
6. **数据库查询**: 仓库的 `ApplyConditions` 方法构建SQL查询
7. **结果返回**: 查询结果返回给前端显示

## 注意事项

1. **时间字段映射**: 前端的 `start_date`/`end_date` 映射到后端的 `reg_time_start`/`reg_time_end`
2. **默认值处理**: 使用 -999 作为"不过滤"的标识值
3. **类型安全**: 后端使用类型断言确保数据类型正确
4. **空值处理**: 前端空值（undefined）和后端空字符串都会被忽略
5. **调试支持**: 后端仓库中有调试日志可以查看接收到的条件

## 验证方法

1. **前端调试**: 在浏览器开发者工具中查看网络请求参数
2. **后端日志**: 查看控制台输出的调试信息
3. **SQL查询**: 检查生成的SQL语句是否包含正确的WHERE条件
4. **功能测试**: 逐一测试每个筛选条件的效果 