package permission

import (
	"context"
	"testing"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"frontapi/internal/models/permission"
	permRepo "frontapi/internal/repository/permission"
	"frontapi/pkg/types"
)

// 测试配置
const testDSN = "root:password@tcp(localhost:3306)/test_db?charset=utf8mb4&parseTime=True&loc=Local"

// setupTestDB 设置测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(mysql.Open(testDSN), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		t.Skip("跳过测试: 无法连接数据库", err)
	}

	// 自动迁移测试表
	err = db.AutoMigrate(
		&permission.AdminUser{},
		&permission.AdminRole{},
		&permission.AdminMenu{},
		&permission.CasbinRule{},
	)
	if err != nil {
		t.Fatal("数据库迁移失败:", err)
	}

	return db
}

// cleanupTestDB 清理测试数据
func cleanupTestDB(db *gorm.DB) {
	db.Exec("DELETE FROM ly_admin_users")
	db.Exec("DELETE FROM ly_admin_roles")
	db.Exec("DELETE FROM ly_admin_sys_menus")
	db.Exec("DELETE FROM ly_casbin_rule")
}

// TestAdminUserRepository 测试用户Repository
func TestAdminUserRepository(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(db)

	userRepo := permRepo.NewAdminUserRepository(db)
	ctx := context.Background()

	// 测试创建用户
	user := &permission.AdminUser{
		Username:     "testuser",
		Password:     "hashedpassword",
		Nickname:     "测试用户",
		Email:        "<EMAIL>",
		Phone:        "13800138000",
		Psalt:        "randomsalt",
		IsSuperAdmin: 0,
		Status:       1,
		CreatedAt:    types.JSONTime(time.Now()),
		UpdatedAt:    types.JSONTime(time.Now()),
	}

	err := userRepo.Create(ctx, user)
	if err != nil {
		t.Fatal("创建用户失败:", err)
	}
	t.Logf("创建用户成功, ID: %d", user.ID)

	// 测试根据用户名获取用户
	foundUser, err := userRepo.GetByUsername(ctx, "testuser")
	if err != nil {
		t.Fatal("获取用户失败:", err)
	}
	if foundUser == nil {
		t.Fatal("用户不存在")
	}
	if foundUser.Username != "testuser" {
		t.Errorf("期望用户名: testuser, 实际: %s", foundUser.Username)
	}

	// 测试检查用户名是否存在
	exists, err := userRepo.ExistsUsername(ctx, "testuser")
	if err != nil {
		t.Fatal("检查用户名存在性失败:", err)
	}
	if !exists {
		t.Error("用户名应该存在")
	}

	t.Log("✅ 用户Repository测试通过")
}

// TestAdminRoleRepository 测试角色Repository
func TestAdminRoleRepository(t *testing.T) {
	db := setupTestDB(t)
	defer cleanupTestDB(db)

	roleRepo := permRepo.NewAdminRoleRepository(db)
	ctx := context.Background()

	// 测试创建角色
	role := &permission.AdminRole{
		Code:        "admin",
		Name:        "管理员",
		Description: "系统管理员角色",
		Level:       1,
		IsDefault:   0,
		Status:      1,
		CreatedAt:   types.JSONTime(time.Now()),
		UpdatedAt:   types.JSONTime(time.Now()),
	}

	err := roleRepo.Create(ctx, role)
	if err != nil {
		t.Fatal("创建角色失败:", err)
	}
	t.Logf("创建角色成功, ID: %d", role.ID)

	// 测试根据编码获取角色
	foundRole, err := roleRepo.GetByCode(ctx, "admin")
	if err != nil {
		t.Fatal("获取角色失败:", err)
	}
	if foundRole == nil {
		t.Fatal("角色不存在")
	}
	if foundRole.Code != "admin" {
		t.Errorf("期望角色编码: admin, 实际: %s", foundRole.Code)
	}

	// 测试检查角色编码是否存在
	exists, err := roleRepo.ExistsCode(ctx, "admin")
	if err != nil {
		t.Fatal("检查角色编码存在性失败:", err)
	}
	if !exists {
		t.Error("角色编码应该存在")
	}

	// 测试获取所有活跃角色
	activeRoles, err := roleRepo.GetActive(ctx)
	if err != nil {
		t.Fatal("获取活跃角色失败:", err)
	}
	if len(activeRoles) != 1 {
		t.Errorf("期望活跃角色数量: 1, 实际: %d", len(activeRoles))
	}

	t.Log("✅ 角色Repository测试通过")
}

// TestCasbinRuleModel 测试Casbin规则模型
func TestCasbinRuleModel(t *testing.T) {
	// 测试权限规则转换
	permRule := &permission.PermissionRule{
		Role:     "admin",
		Resource: "user",
		Action:   "read",
	}

	casbinRule := permission.FromPermissionRule(permRule)
	if casbinRule.PType != "p" {
		t.Errorf("期望策略类型: p, 实际: %s", casbinRule.PType)
	}
	if casbinRule.V0 != "admin" {
		t.Errorf("期望V0: admin, 实际: %s", casbinRule.V0)
	}
	if casbinRule.V1 != "user" {
		t.Errorf("期望V1: user, 实际: %s", casbinRule.V1)
	}
	if casbinRule.V2 != "read" {
		t.Errorf("期望V2: read, 实际: %s", casbinRule.V2)
	}

	// 测试反向转换
	convertedRule := casbinRule.ToPermissionRule()
	if convertedRule.Role != permRule.Role {
		t.Errorf("期望角色: %s, 实际: %s", permRule.Role, convertedRule.Role)
	}

	t.Log("✅ Casbin规则模型测试通过")
}
