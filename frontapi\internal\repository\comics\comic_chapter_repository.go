package comics

import (
	"context"
	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ComicChapterRepository 漫画章节数据访问接口
type ComicChapterRepository interface {
	base.ExtendedRepository[comics.ComicChapter]
	FindByComicAndNumber(ctx context.Context, comicID string, chapterNumber int) (*comics.ComicChapter, error)
	BatchUpdateOrder(ctx context.Context, comicID string, chapters map[string]int) error
}

// comicChapterRepository 漫画章节数据访问实现
type comicChapterRepository struct {
	base.ExtendedRepository[comics.ComicChapter]
}

// NewComicChapterRepository 创建漫画章节仓库实例
func NewComicChapterRepository(db *gorm.DB) ComicChapterRepository {
	return &comicChapterRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.ComicChapter](db),
	}
}

// FindByComicAndNumber 根据漫画ID和章节序号查找章节
func (r *comicChapterRepository) FindByComicAndNumber(ctx context.Context, comicID string, chapterNumber int) (*comics.ComicChapter, error) {
	condition := map[string]interface{}{
		"comic_id":       comicID,
		"chapter_number": chapterNumber,
	}
	return r.FindOneByCondition(ctx, condition, "")
}

// BatchUpdateOrder 批量更新章节序号
func (r *comicChapterRepository) BatchUpdateOrder(ctx context.Context, comicID string, chapters map[string]int) error {
	// 开启事务
	tx := r.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 遍历更新每个章节
	for chapterID, orderNumber := range chapters {
		// 更新章节序号
		err := tx.WithContext(ctx).
			Model(&comics.ComicChapter{}).
			Where("id = ? AND comic_id = ?", chapterID, comicID).
			Update("chapter_number", orderNumber).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}

	// 提交事务
	return tx.Commit().Error
}
