package models

import (
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"
)

// BaseModel 基础模型接口，定义所有数据库模型必须实现的方法
type BaseModel interface {
	GetID() string
	SetID(id string)
	GetStatus() int8
	SetStatus(status int8)
	GetCreatedAt() types.JSONTime
	GetUpdatedAt() types.JSONTime
	SetCreatedAt(time types.JSONTime)
	SetUpdatedAt(time types.JSONTime)
	GetStatusField() string
}

// BaseModelConstraint 用于泛型约束的接口，包含所有BaseModel方法
type BaseModelConstraint interface {
	GetID() string
	GetStatus() int8
	GetCreatedAt() types.JSONTime
	GetUpdatedAt() types.JSONTime
	TableName() string
	GetStatusField() string
}

// IntBaseModel 基础模型接口，定义所有使用int类型ID的数据库模型必须实现的方法
type IntBaseModel interface {
	GetID() int
	SetID(id int)
	GetStatus() int8
	SetStatus(status int8)
	GetCreatedAt() types.JSONTime
	GetUpdatedAt() types.JSONTime
	SetCreatedAt(time types.JSONTime)
	SetUpdatedAt(time types.JSONTime)
	GetStatusField() string
}

// IntBaseModelConstraint 用于泛型约束的接口，包含所有IntBaseModel方法
type IntBaseModelConstraint interface {
	GetID() int
	GetStatus() int8
	GetCreatedAt() types.JSONTime
	GetUpdatedAt() types.JSONTime
	TableName() string
	GetStatusField() string
}

// IntBaseModelPointer 指针方法接口
type IntBaseModelPointer interface {
	SetID(id int)
	SetStatus(status int8)
	SetCreatedAt(time types.JSONTime)
	SetUpdatedAt(time types.JSONTime)
	GetStatusField() string
}

// BaseModelPointer 指针方法接口
type BaseModelPointer interface {
	SetID(id string)
	SetStatus(status int8)
	SetCreatedAt(time types.JSONTime)
	SetUpdatedAt(time types.JSONTime)
	GetStatusField() string
}

// BaseModelStruct 基础模型结构体，包含所有数据库模型的公共字段
type BaseModelStruct struct {
	ID        string         `json:"id" gorm:"column:id;primaryKey;type:string;size:36" comment:"主键ID" deepcopier:"skip"`
	Status    int8           `json:"status" gorm:"column:status;default:1" comment:"状态：0-禁用，1-正常"`
	CreatedAt types.JSONTime `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP;->;<-:create" comment:"创建时间" deepcopier:"skip"`
	UpdatedAt types.JSONTime `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP;ON UPDATE CURRENT_TIMESTAMP" comment:"更新时间" deepcopier:"skip"`
}

// IntBaseModelStruct 基础模型结构体(int类型ID)，适用于使用自增ID的表
type IntBaseModelStruct struct {
	ID        int            `json:"id" gorm:"column:id;primaryKey;autoIncrement" comment:"主键ID"`
	Status    int8           `json:"status" gorm:"column:status;default:1" comment:"状态：0-禁用，1-正常"`
	CreatedAt types.JSONTime `json:"created_at" gorm:"column:created_at;default:CURRENT_TIMESTAMP;->;<-:create" comment:"创建时间"`
	UpdatedAt types.JSONTime `json:"updated_at" gorm:"column:updated_at;default:CURRENT_TIMESTAMP;ON UPDATE CURRENT_TIMESTAMP" comment:"更新时间"`
}

// GetID 获取ID
func (b BaseModelStruct) GetID() string { return b.ID }

// SetID 设置ID
func (b *BaseModelStruct) SetID(id string) {
	b.ID = id
}

// GetStatus 获取状态
func (b BaseModelStruct) GetStatus() int8 {
	return b.Status
}

// SetStatus 设置状态
func (b *BaseModelStruct) SetStatus(status int8) {
	b.Status = status
}

// GetStatusField 获取状态字段
func (b IntBaseModelStruct) GetStatusField() string {
	return "status"
}

// GetStatusField 获取状态字段
func (b BaseModelStruct) GetStatusField() string {
	return "status"
}

// GetCreatedAt 获取创建时间
func (b BaseModelStruct) GetCreatedAt() types.JSONTime {
	return b.CreatedAt
}

// GetUpdatedAt 获取更新时间
func (b BaseModelStruct) GetUpdatedAt() types.JSONTime {
	return b.UpdatedAt
}

// SetCreatedAt 设置创建时间
func (b *BaseModelStruct) SetCreatedAt(time types.JSONTime) {
	b.CreatedAt = time
}

// SetUpdatedAt 设置更新时间
func (b *BaseModelStruct) SetUpdatedAt(time types.JSONTime) {
	b.UpdatedAt = time
}

// IntBaseModelStruct 方法实现
// GetID 获取ID
func (b IntBaseModelStruct) GetID() int { return b.ID }

// SetID 设置ID
func (b *IntBaseModelStruct) SetID(id int) {
	b.ID = id
}

// GetStatus 获取状态
func (b IntBaseModelStruct) GetStatus() int8 {
	return b.Status
}

// SetStatus 设置状态
func (b *IntBaseModelStruct) SetStatus(status int8) {
	b.Status = status
}

// GetCreatedAt 获取创建时间
func (b IntBaseModelStruct) GetCreatedAt() types.JSONTime {
	return b.CreatedAt
}

// GetUpdatedAt 获取更新时间
func (b IntBaseModelStruct) GetUpdatedAt() types.JSONTime {
	return b.UpdatedAt
}

// SetCreatedAt 设置创建时间
func (b *IntBaseModelStruct) SetCreatedAt(time types.JSONTime) {
	b.CreatedAt = time
}

// SetUpdatedAt 设置更新时间
func (b *IntBaseModelStruct) SetUpdatedAt(time types.JSONTime) {
	b.UpdatedAt = time
}

// ExtendedBaseModel 扩展基础模型，包含更多常用字段
type ExtendedBaseModel struct {
	BaseModelStruct
	ViewCount    uint64 `json:"view_count" gorm:"column:view_count;default:0" comment:"查看次数" deepcopier:"skip"`
	LikeCount    uint64 `json:"like_count" gorm:"column:like_count;default:0" comment:"点赞数" deepcopier:"skip"`
	CommentCount uint64 `json:"comment_count" gorm:"column:comment_count;default:0" comment:"评论数" deepcopier:"skip"`
	ShareCount   uint64 `json:"share_count" gorm:"column:share_count;default:0" comment:"分享次数" deepcopier:"skip"`
	//IsFeatured   int8   `json:"is_featured" gorm:"column:is_featured;default:0" comment:"是否推荐"`
	IsLiked    bool `json:"is_liked" gorm:"-" comment:"是否点赞"`
	IsFavorite bool `json:"is_favorite" gorm:"-" comment:"是否收藏"`
}

func (e *ExtendedBaseModel) SetViewCount(count uint64) {
	e.ViewCount = count
}
func (e *ExtendedBaseModel) SetLikeCount(count uint64) {
	e.LikeCount = count
}
func (e *ExtendedBaseModel) SetCommentCount(count uint64) {
	e.CommentCount = count
}
func (e *ExtendedBaseModel) SetShareCount(count uint64) {
	e.ShareCount = count
}

// ContentBaseModel 内容基础模型，适用于图片、视频、文章等内容类型
type ContentBaseModel struct {
	ExtendedBaseModel
	Title        string      `json:"title" gorm:"column:title" comment:"标题"`
	Description  null.String `json:"description" gorm:"column:description;type:text" comment:"描述"`
	CreatorID    null.String `json:"creator_id" gorm:"column:creator_id" comment:"创作者ID"`
	CategoryID   null.String `json:"category_id" gorm:"column:category_id" comment:"分类ID"`
	CategoryName null.String `json:"category_name" gorm:"column:category_name" comment:"分类名称"`
}

func (c *ContentBaseModel) SetTitle(title string) {
	c.Title = title
}
func (c *ContentBaseModel) SetDescription(description interface{}) {
	//判断是否是string,如果是,则赋值
	if description != nil {
		switch v := description.(type) {
		case string:
			c.Description = null.StringFrom(v)
		case null.String:
			c.Description = v
		}
	}
}
func (c *ContentBaseModel) SetCreatorID(creatorID interface{}) {
	//判断是否是string,如果是,则赋值
	if creatorID != nil {
		switch v := creatorID.(type) {
		case string:
			c.CreatorID = null.StringFrom(v)
		case null.String:
			c.CreatorID = v
		}
	}
}
func (c *ContentBaseModel) SetCategoryID(categoryID interface{}) {
	//判断是否是string,如果是,则赋值
	if categoryID != nil {
		switch v := categoryID.(type) {
		case string:
			c.CategoryID = null.StringFrom(v)
		case null.String:
			c.CategoryID = v
		}
	}
}
func (c *ContentBaseModel) SetCategoryName(categoryName interface{}) {
	//判断是否是string,如果是,则赋值
	if categoryName != nil {
		switch v := categoryName.(type) {
		case string:
			c.CategoryName = null.StringFrom(v)
		case null.String:
			c.CategoryName = v
		}
	}
}

// CategoryBaseModel 分类基础模型
type CategoryBaseModel struct {
	BaseModelStruct
	Name        null.String `json:"name" gorm:"column:name;not null" comment:"分类名称"`
	Code        string      `json:"code" gorm:"column:code;uniqueIndex" comment:"分类编码"`
	Description null.String `json:"description" gorm:"column:description;type:text" comment:"分类描述"`
	SortOrder   int         `json:"sort_order" gorm:"column:sort_order;default:0" comment:"排序序号"`
}

func (c *CategoryBaseModel) SetName(name interface{}) {
	//判断是否是string,如果是,则赋值
	if name != nil {
		switch v := name.(type) {
		case string:
			c.Name = null.StringFrom(v)
		case null.String:
			c.Name = v
		}
	}
}
func (c *CategoryBaseModel) SetCode(code interface{}) {
	//判断是否是string,如果是,则赋值
	if code != nil {
		switch v := code.(type) {
		case string:
			c.Code = v
		case null.String:
			c.Code = v.String
		}
	}
}
func (c *CategoryBaseModel) SetDescription(description interface{}) {
	//判断是否是string,如果是,则赋值
	if description != nil {
		switch v := description.(type) {
		case string:
			c.Description = null.StringFrom(v)
		case null.String:
			c.Description = v
		}
	}
}
