/**
 * 国际化语言配置
 * 配置多语言相关选项，独立于核心模块
 */

export interface LocaleInfo {
    /** 语言代码 */
    code: string;
    /** 语言简码 (用于显示在UI上) */
    shortCode: string;
    /** 本地语言名称 */
    nativeName: string;
    /** 英文名称 */
    englishName: string;
    /** 国旗图标 */
    flag: string;
    /** 是否启用 */
    enabled: boolean;
    /** 浏览器语言匹配模式 (用于匹配浏览器语言设置) */
    browserMatches: string[];
}

/**
 * 支持的语言列表
 */
export const SUPPORTED_LOCALES: LocaleInfo[] = [
    {
        code: 'en',
        shortCode: 'EN',
        nativeName: 'English',
        englishName: 'English',
        flag: '🇺🇸',
        enabled: true,
        browserMatches: [
            'en', 'en-us', 'en-gb', 'en-au', 'en-ca',
            'en-nz', 'en-ie', 'en-za', 'en-in'
        ]
    },
    {
        code: 'zh-CN',
        shortCode: 'SC',
        nativeName: '简体中文',
        englishName: 'Simplified Chinese',
        flag: '🇨🇳',
        enabled: true,
        browserMatches: ['zh', 'zh-cn', 'zh-hans', 'zh-sg', 'zh-hans-cn', 'cn']
    },
    {
        code: 'zh-TW',
        shortCode: 'TC',
        nativeName: '繁體中文',
        englishName: 'Traditional Chinese',
        flag: '🇹🇼',
        enabled: true,
        browserMatches: ['zh-tw', 'zh-hk', 'zh-hant', 'zh-mo', 'zh-hant-tw', 'zh-hant-hk']
    },
    {
        code: 'ko',
        shortCode: 'KO',
        nativeName: '한국어',
        englishName: 'Korean',
        flag: '🇰🇷',
        enabled: true,
        browserMatches: ['ko', 'ko-kr']
    }
];

/**
 * 默认语言
 */
export const DEFAULT_LOCALE = 'en';

/**
 * 获取启用的语言代码列表
 */
export function getEnabledLocaleCodes(): string[] {
    return SUPPORTED_LOCALES
        .filter(locale => locale.enabled)
        .map(locale => locale.code);
}

/**
 * 根据语言代码获取语言信息
 */
export function getLocaleInfo(code: string): LocaleInfo | undefined {
    return SUPPORTED_LOCALES.find(locale => locale.code === code);
}

/**
 * 获取浏览器语言对应的系统语言代码
 */
export function getBrowserLocaleCode(browserLang: string): string | undefined {
    const normalizedBrowserLang = browserLang.toLowerCase();

    // 查找匹配的语言
    const matchedLocale = SUPPORTED_LOCALES.find(locale =>
        locale.enabled && locale.browserMatches.some(pattern =>
            normalizedBrowserLang === pattern || normalizedBrowserLang.startsWith(`${pattern}-`)
        )
    );

    return matchedLocale?.code;
}

/**
 * 国际化缓存设置
 */
export const LOCALE_STORAGE = {
    /** 存储键名 */
    KEY: 'app-locale',
    /** 存储位置 - localStorage 或 sessionStorage */
    STORAGE: 'localStorage'
}; 