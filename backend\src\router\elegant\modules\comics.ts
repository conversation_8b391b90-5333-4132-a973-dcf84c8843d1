import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from "@/locales";

const routes: GeneratedRoute[] = [
  {
    name: 'comics',
    path: '/comics',
    component: 'layout.base',
    meta: {
      title: 'comics',
      i18nKey: 'route.comics',
      icon: 'lucide:book-marked',
      order: 7
    },
    children: [
      // 漫画分类管理
      {
        name: 'comics_category',
        path: '/comics/category',
        component: 'view.comics_category',
        meta: {
          title: 'comics_category',
          i18nKey: 'route.comics_category',
          icon: 'lucide:folder',
          order: 1
        }
      },
      // 漫画列表页面
      {
        name: 'comics_list',
        path: '/comics/list',
        component: 'view.comics_list',
        meta: {
          title: 'comics_list',
          i18nKey: 'route.comics_list',
          icon: 'lucide:list',

          order: 2

        }
      },
      // 漫画章节管理
      {
        name: 'comics_chapter',
        path: '/comics/chapter/:comic_id',
        component: 'view.comics_chapter',
        meta: {
          title: 'comics_chapter',
          i18nKey: 'route.comics_chapter',
          icon: 'lucide:file-text',
          hideInMenu: true,
          order: 3,
          params: {
            comic_id: {
              type: 'string',
              required: true
            }
          }
        }
      },


      // 漫画页面管理
      {
        name: 'comics_page',
        path: '/comics/:comic_id/chapter/:chapter_id/page',
        component: 'view.comics_page',
        meta: {
          title: 'comics_page',
          i18nKey: 'route.comics_page',
          icon: 'lucide:file-image',
          hideInMenu: true,
          order:4,
          params: {
            comic_id: {
              type: 'string',
              required: true
            },
            chapter_id: {
              type: 'string',
              required: true
            }
          }
        }
      }
    ]
  }
];

export default routes;
