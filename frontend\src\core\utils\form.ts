/**
 * 表单工具函数
 */

/**
 * 表单字段类型
 */
export type FormFieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'tel' 
  | 'url' 
  | 'search'
  | 'textarea'
  | 'select'
  | 'checkbox'
  | 'radio'
  | 'date'
  | 'time'
  | 'datetime-local'
  | 'file'
  | 'range'
  | 'color'
  | 'hidden'

/**
 * 表单字段配置
 */
export interface FormFieldConfig {
  name: string
  type: FormFieldType
  label?: string
  placeholder?: string
  defaultValue?: any
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  min?: number | string
  max?: number | string
  step?: number
  pattern?: string
  options?: FormSelectOption[]
  multiple?: boolean
  accept?: string
  rows?: number
  cols?: number
  validation?: FormFieldValidation
  dependencies?: FormFieldDependency[]
  className?: string
  style?: Record<string, any>
  attributes?: Record<string, any>
}

/**
 * 表单选项
 */
export interface FormSelectOption {
  label: string
  value: any
  disabled?: boolean
  group?: string
}

/**
 * 表单字段验证
 */
export interface FormFieldValidation {
  required?: boolean | string
  minLength?: number | string
  maxLength?: number | string
  min?: number | string
  max?: number | string
  pattern?: RegExp | string
  email?: boolean | string
  url?: boolean | string
  custom?: (value: any, formData: Record<string, any>) => string | null
}

/**
 * 表单字段依赖
 */
export interface FormFieldDependency {
  field: string
  condition: (value: any) => boolean
  action: 'show' | 'hide' | 'enable' | 'disable' | 'require'
}

/**
 * 表单配置
 */
export interface FormConfig {
  fields: FormFieldConfig[]
  layout?: 'vertical' | 'horizontal' | 'inline'
  submitText?: string
  resetText?: string
  showReset?: boolean
  autoComplete?: boolean
  noValidate?: boolean
  enctype?: 'application/x-www-form-urlencoded' | 'multipart/form-data' | 'text/plain'
  method?: 'GET' | 'POST'
  action?: string
  className?: string
  style?: Record<string, any>
  onSubmit?: (data: Record<string, any>) => void | Promise<void>
  onReset?: () => void
  onChange?: (field: string, value: any, formData: Record<string, any>) => void
  onValidate?: (errors: Record<string, string[]>) => void
}

/**
 * 表单数据
 */
export interface FormData {
  [key: string]: any
}

/**
 * 表单错误
 */
export interface FormErrors {
  [key: string]: string[]
}

/**
 * 表单状态
 */
export interface FormState {
  data: FormData
  errors: FormErrors
  touched: Record<string, boolean>
  dirty: Record<string, boolean>
  submitting: boolean
  submitted: boolean
  valid: boolean
}

/**
 * 表单管理器
 */
export class FormManager {
  private config: FormConfig
  private state: FormState
  private listeners: Set<(state: FormState) => void> = new Set()
  private element?: HTMLFormElement

  constructor(config: FormConfig) {
    this.config = config
    this.state = {
      data: this.getInitialData(),
      errors: {},
      touched: {},
      dirty: {},
      submitting: false,
      submitted: false,
      valid: true
    }
  }

  /**
   * 获取初始数据
   */
  private getInitialData(): FormData {
    const data: FormData = {}
    
    this.config.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        data[field.name] = field.defaultValue
      } else {
        switch (field.type) {
          case 'checkbox':
            data[field.name] = false
            break
          case 'select':
            if (field.multiple) {
              data[field.name] = []
            } else {
              data[field.name] = ''
            }
            break
          case 'number':
          case 'range':
            data[field.name] = 0
            break
          case 'file':
            data[field.name] = field.multiple ? [] : null
            break
          default:
            data[field.name] = ''
        }
      }
    })
    
    return data
  }

  /**
   * 获取表单状态
   */
  getState(): FormState {
    return { ...this.state }
  }

  /**
   * 设置字段值
   */
  setValue(name: string, value: any): void {
    const oldValue = this.state.data[name]
    
    this.state.data[name] = value
    this.state.dirty[name] = true
    
    // 清除该字段的错误
    if (this.state.errors[name]) {
      delete this.state.errors[name]
    }
    
    // 验证字段
    this.validateField(name)
    
    // 处理字段依赖
    this.processDependencies()
    
    // 更新整体验证状态
    this.updateValidState()
    
    // 触发变更回调
    if (this.config.onChange && oldValue !== value) {
      this.config.onChange(name, value, this.state.data)
    }
    
    // 通知监听器
    this.notifyListeners()
  }

  /**
   * 获取字段值
   */
  getValue(name: string): any {
    return this.state.data[name]
  }

  /**
   * 设置字段为已触摸
   */
  setTouched(name: string, touched = true): void {
    this.state.touched[name] = touched
    this.notifyListeners()
  }

  /**
   * 验证字段
   */
  private validateField(name: string): void {
    const field = this.config.fields.find(f => f.name === name)
    if (!field || !field.validation) return

    const value = this.state.data[name]
    const errors: string[] = []
    const validation = field.validation

    // 必填验证
    if (validation.required) {
      const isEmpty = value === null || value === undefined || value === '' || 
                     (Array.isArray(value) && value.length === 0)
      
      if (isEmpty) {
        const message = typeof validation.required === 'string' 
          ? validation.required 
          : `${field.label || field.name}是必填项`
        errors.push(message)
      }
    }

    // 如果值为空且不是必填，跳过其他验证
    if (!value && !validation.required) {
      this.state.errors[name] = errors
      return
    }

    // 最小长度验证
    if (validation.minLength && typeof value === 'string') {
      if (value.length < validation.minLength) {
        const message = typeof validation.minLength === 'string'
          ? validation.minLength
          : `${field.label || field.name}最少需要${validation.minLength}个字符`
        errors.push(message)
      }
    }

    // 最大长度验证
    if (validation.maxLength && typeof value === 'string') {
      if (value.length > validation.maxLength) {
        const message = typeof validation.maxLength === 'string'
          ? validation.maxLength
          : `${field.label || field.name}最多允许${validation.maxLength}个字符`
        errors.push(message)
      }
    }

    // 最小值验证
    if (validation.min !== undefined && typeof value === 'number') {
      if (value < validation.min) {
        const message = typeof validation.min === 'string'
          ? validation.min
          : `${field.label || field.name}不能小于${validation.min}`
        errors.push(message)
      }
    }

    // 最大值验证
    if (validation.max !== undefined && typeof value === 'number') {
      if (value > validation.max) {
        const message = typeof validation.max === 'string'
          ? validation.max
          : `${field.label || field.name}不能大于${validation.max}`
        errors.push(message)
      }
    }

    // 正则表达式验证
    if (validation.pattern && typeof value === 'string') {
      const pattern = validation.pattern instanceof RegExp 
        ? validation.pattern 
        : new RegExp(validation.pattern)
      
      if (!pattern.test(value)) {
        const message = typeof validation.pattern === 'string'
          ? `${field.label || field.name}格式不正确`
          : `${field.label || field.name}格式不正确`
        errors.push(message)
      }
    }

    // 邮箱验证
    if (validation.email && typeof value === 'string') {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailPattern.test(value)) {
        const message = typeof validation.email === 'string'
          ? validation.email
          : `${field.label || field.name}格式不正确`
        errors.push(message)
      }
    }

    // URL验证
    if (validation.url && typeof value === 'string') {
      try {
        new URL(value)
      } catch {
        const message = typeof validation.url === 'string'
          ? validation.url
          : `${field.label || field.name}格式不正确`
        errors.push(message)
      }
    }

    // 自定义验证
    if (validation.custom) {
      const customError = validation.custom(value, this.state.data)
      if (customError) {
        errors.push(customError)
      }
    }

    this.state.errors[name] = errors
  }

  /**
   * 验证所有字段
   */
  validateAll(): boolean {
    this.config.fields.forEach(field => {
      this.validateField(field.name)
    })
    
    this.updateValidState()
    
    if (this.config.onValidate) {
      this.config.onValidate(this.state.errors)
    }
    
    this.notifyListeners()
    
    return this.state.valid
  }

  /**
   * 更新验证状态
   */
  private updateValidState(): void {
    this.state.valid = Object.values(this.state.errors).every(errors => errors.length === 0)
  }

  /**
   * 处理字段依赖
   */
  private processDependencies(): void {
    this.config.fields.forEach(field => {
      if (field.dependencies) {
        field.dependencies.forEach(dep => {
          const depValue = this.state.data[dep.field]
          const shouldApply = dep.condition(depValue)
          
          // 这里可以根据action类型来处理字段状态
          // 实际实现需要与UI框架结合
        })
      }
    })
  }

  /**
   * 提交表单
   */
  async submit(): Promise<boolean> {
    this.state.submitting = true
    this.notifyListeners()

    // 标记所有字段为已触摸
    this.config.fields.forEach(field => {
      this.state.touched[field.name] = true
    })

    // 验证所有字段
    const isValid = this.validateAll()

    if (!isValid) {
      this.state.submitting = false
      this.notifyListeners()
      return false
    }

    try {
      if (this.config.onSubmit) {
        await this.config.onSubmit(this.state.data)
      }
      
      this.state.submitted = true
      return true
    } catch (error) {
      console.error('Form submission error:', error)
      return false
    } finally {
      this.state.submitting = false
      this.notifyListeners()
    }
  }

  /**
   * 重置表单
   */
  reset(): void {
    this.state.data = this.getInitialData()
    this.state.errors = {}
    this.state.touched = {}
    this.state.dirty = {}
    this.state.submitting = false
    this.state.submitted = false
    this.state.valid = true

    if (this.config.onReset) {
      this.config.onReset()
    }

    this.notifyListeners()
  }

  /**
   * 添加状态监听器
   */
  subscribe(listener: (state: FormState) => void): () => void {
    this.listeners.add(listener)
    
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      listener(this.getState())
    })
  }

  /**
   * 绑定DOM表单元素
   */
  bindElement(element: HTMLFormElement): void {
    this.element = element
    
    // 绑定表单提交事件
    element.addEventListener('submit', (e) => {
      e.preventDefault()
      this.submit()
    })
    
    // 绑定表单重置事件
    element.addEventListener('reset', (e) => {
      e.preventDefault()
      this.reset()
    })
    
    // 绑定字段变更事件
    this.config.fields.forEach(field => {
      const fieldElement = element.querySelector(`[name="${field.name}"]`) as HTMLInputElement
      if (fieldElement) {
        fieldElement.addEventListener('input', () => {
          this.setValue(field.name, this.getFieldValue(fieldElement, field))
        })
        
        fieldElement.addEventListener('blur', () => {
          this.setTouched(field.name, true)
        })
      }
    })
  }

  /**
   * 获取字段值
   */
  private getFieldValue(element: HTMLInputElement, field: FormFieldConfig): any {
    switch (field.type) {
      case 'checkbox':
        return element.checked
      case 'number':
      case 'range':
        return parseFloat(element.value) || 0
      case 'file':
        return field.multiple ? Array.from(element.files || []) : element.files?.[0] || null
      case 'select':
        if (field.multiple) {
          const select = element as HTMLSelectElement
          return Array.from(select.selectedOptions).map(option => option.value)
        }
        return element.value
      default:
        return element.value
    }
  }

  /**
   * 销毁表单管理器
   */
  destroy(): void {
    this.listeners.clear()
    this.element = undefined
  }
}

/**
 * 创建表单管理器
 * @param config 表单配置
 * @returns 表单管理器实例
 */
export function createFormManager(config: FormConfig): FormManager {
  return new FormManager(config)
}

/**
 * 表单验证器
 */
export class FormValidator {
  /**
   * 验证邮箱
   */
  static email(value: string): boolean {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return pattern.test(value)
  }

  /**
   * 验证手机号
   */
  static phone(value: string): boolean {
    const pattern = /^1[3-9]\d{9}$/
    return pattern.test(value)
  }

  /**
   * 验证身份证号
   */
  static idCard(value: string): boolean {
    const pattern = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return pattern.test(value)
  }

  /**
   * 验证URL
   */
  static url(value: string): boolean {
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  }

  /**
   * 验证密码强度
   */
  static passwordStrength(value: string): 'weak' | 'medium' | 'strong' {
    let score = 0
    
    // 长度检查
    if (value.length >= 8) score++
    if (value.length >= 12) score++
    
    // 字符类型检查
    if (/[a-z]/.test(value)) score++
    if (/[A-Z]/.test(value)) score++
    if (/\d/.test(value)) score++
    if (/[^\w\s]/.test(value)) score++
    
    if (score < 3) return 'weak'
    if (score < 5) return 'medium'
    return 'strong'
  }

  /**
   * 验证信用卡号
   */
  static creditCard(value: string): boolean {
    // Luhn算法验证
    const digits = value.replace(/\D/g, '')
    let sum = 0
    let isEven = false
    
    for (let i = digits.length - 1; i >= 0; i--) {
      let digit = parseInt(digits[i], 10)
      
      if (isEven) {
        digit *= 2
        if (digit > 9) {
          digit -= 9
        }
      }
      
      sum += digit
      isEven = !isEven
    }
    
    return sum % 10 === 0
  }
}

/**
 * 表单工具类
 */
export class FormUtils {
  /**
   * 序列化表单数据
   */
  static serialize(form: HTMLFormElement): Record<string, any> {
    const formData = new FormData(form)
    const data: Record<string, any> = {}
    
    for (const [key, value] of formData.entries()) {
      if (data[key]) {
        // 处理多值字段
        if (Array.isArray(data[key])) {
          data[key].push(value)
        } else {
          data[key] = [data[key], value]
        }
      } else {
        data[key] = value
      }
    }
    
    return data
  }

  /**
   * 填充表单数据
   */
  static populate(form: HTMLFormElement, data: Record<string, any>): void {
    Object.entries(data).forEach(([key, value]) => {
      const elements = form.querySelectorAll(`[name="${key}"]`) as NodeListOf<HTMLInputElement>
      
      elements.forEach(element => {
        switch (element.type) {
          case 'checkbox':
          case 'radio':
            element.checked = Array.isArray(value) 
              ? value.includes(element.value)
              : element.value === String(value)
            break
          case 'select-multiple':
            const select = element as HTMLSelectElement
            Array.from(select.options).forEach(option => {
              option.selected = Array.isArray(value) 
                ? value.includes(option.value)
                : option.value === String(value)
            })
            break
          default:
            element.value = String(value || '')
        }
      })
    })
  }

  /**
   * 清空表单
   */
  static clear(form: HTMLFormElement): void {
    const elements = form.querySelectorAll('input, select, textarea') as NodeListOf<HTMLInputElement>
    
    elements.forEach(element => {
      switch (element.type) {
        case 'checkbox':
        case 'radio':
          element.checked = false
          break
        case 'select-multiple':
          const select = element as HTMLSelectElement
          Array.from(select.options).forEach(option => {
            option.selected = false
          })
          break
        default:
          element.value = ''
      }
    })
  }

  /**
   * 禁用表单
   */
  static disable(form: HTMLFormElement): void {
    const elements = form.querySelectorAll('input, select, textarea, button') as NodeListOf<HTMLInputElement>
    elements.forEach(element => {
      element.disabled = true
    })
  }

  /**
   * 启用表单
   */
  static enable(form: HTMLFormElement): void {
    const elements = form.querySelectorAll('input, select, textarea, button') as NodeListOf<HTMLInputElement>
    elements.forEach(element => {
      element.disabled = false
    })
  }

  /**
   * 获取表单字段
   */
  static getFields(form: HTMLFormElement): HTMLInputElement[] {
    return Array.from(form.querySelectorAll('input, select, textarea'))
  }

  /**
   * 获取表单字段值
   */
  static getFieldValue(element: HTMLInputElement): any {
    switch (element.type) {
      case 'checkbox':
        return element.checked
      case 'radio':
        return element.checked ? element.value : null
      case 'number':
      case 'range':
        return parseFloat(element.value) || 0
      case 'file':
        return element.multiple ? Array.from(element.files || []) : element.files?.[0] || null
      case 'select-multiple':
        const select = element as HTMLSelectElement
        return Array.from(select.selectedOptions).map(option => option.value)
      default:
        return element.value
    }
  }

  /**
   * 设置表单字段值
   */
  static setFieldValue(element: HTMLInputElement, value: any): void {
    switch (element.type) {
      case 'checkbox':
        element.checked = Boolean(value)
        break
      case 'radio':
        element.checked = element.value === String(value)
        break
      case 'select-multiple':
        const select = element as HTMLSelectElement
        Array.from(select.options).forEach(option => {
          option.selected = Array.isArray(value) 
            ? value.includes(option.value)
            : option.value === String(value)
        })
        break
      default:
        element.value = String(value || '')
    }
  }

  /**
   * 验证表单
   */
  static validate(form: HTMLFormElement): boolean {
    return form.checkValidity()
  }

  /**
   * 获取表单验证错误
   */
  static getValidationErrors(form: HTMLFormElement): Record<string, string> {
    const errors: Record<string, string> = {}
    const elements = this.getFields(form)
    
    elements.forEach(element => {
      if (!element.checkValidity()) {
        errors[element.name] = element.validationMessage
      }
    })
    
    return errors
  }
}

// 导出默认实例
export const formValidator = FormValidator
export const formUtils = FormUtils

// 导出快捷方法
export {
  createFormManager as createForm,
  FormValidator as validator,
  FormUtils as utils
}