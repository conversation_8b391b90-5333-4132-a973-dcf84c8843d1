<template>
  <div class="horizontal-menu">
    <div class="menu-items">
      <div
        v-for="item in menuItems"
        :key="item.label"
        class="menu-item"
        :class="{ 
          'has-submenu': item.items,
          'active': isActiveRoute(item.route)
        }"
      >
        <!-- 主菜单项 -->
        <div
          :ref="el => setMenuItemRef(item.label, el)"
          class="menu-item-content"
          @click="() => handleMenuItemClick(item)"
          @mouseenter="() => handleMenuItemHover(item)"
          @mouseleave="() => handleMenuItemLeave(item)"
        >
          <router-link
            v-if="item.route && !item.items"
            :to="item.route"
            class="menu-link"
            :class="{ 'active': isActiveRoute(item.route) }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span class="menu-label">{{ t(item.label) }}</span>
          </router-link>
          <div
            v-else
            class="menu-link"
            :class="{ 'has-submenu': item.items }"
          >
            <span v-if="item.icon" class="menu-icon">
              <i :class="item.icon"></i>
            </span>
            <span class="menu-label">{{ t(item.label) }}</span>
            <i v-if="item.items" class="pi pi-angle-down submenu-indicator"></i>
          </div>
        </div>

        <!-- 子菜单 CustomPopover -->
        <CustomPopover
          v-if="item.items"
          :ref="el => setPopoverRef(item.label, el)"
          placement="bottom-start"
          trigger="manual"
          :show-arrow="true"
          :offset="8"
          class="submenu-popover"
        >
          <template #trigger>
            <div class="popover-trigger-placeholder"></div>
          </template>

          <template #header>
            <span class="submenu-title">{{ t(item.label) }}</span>
          </template>

          <template #body>
            <!-- 单列子菜单 -->
            <div v-if="item.items.length <= props.megaMenuThreshold" class="submenu-single-column">
              <div class="submenu-items">
                <router-link
                  v-for="subItem in item.items"
                  :key="subItem.label"
                  :to="subItem.route!"
                  class="submenu-item-link"
                  :class="{ 'active': isActiveRoute(subItem.route) }"
                  @click="$emit('item-click', subItem)"
                >
                  <span v-if="subItem.icon" class="submenu-icon">
                    <i :class="subItem.icon"></i>
                  </span>
                  <span class="submenu-label">{{ t(subItem.label) }}</span>
                </router-link>
              </div>
            </div>

            <!-- 多列子菜单 (MegaMenu 风格) -->
            <div v-else class="submenu-mega">
              <div class="submenu-columns">
                <div
                  v-for="(group, groupIndex) in getMenuGroups(item.items)"
                  :key="groupIndex"
                  class="submenu-column"
                >
                  <router-link
                    v-for="subItem in group"
                    :key="subItem.label"
                    :to="subItem.route!"
                    class="submenu-item-link"
                    :class="{ 'active': isActiveRoute(subItem.route) }"
                    @click="$emit('item-click', subItem)"
                  >
                    <span v-if="subItem.icon" class="submenu-icon">
                      <i :class="subItem.icon"></i>
                    </span>
                    <span class="submenu-label">{{ t(subItem.label) }}</span>
                  </router-link>
                </div>
              </div>
            </div>
          </template>
        </CustomPopover>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from '@/core/plugins/i18n/composables';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import CustomPopover from '../CustomPopover/index.vue';

// Props
interface Props {
  megaMenuThreshold?: number;
}

const props = withDefaults(defineProps<Props>(), {
  megaMenuThreshold: 6
});

// Emits
interface Emits {
  'item-click': [item: any];
}

const emit = defineEmits<Emits>();

// Composables
const { t } = useTranslation();
const route = useRoute();

// State
const hoverTimer = ref<NodeJS.Timeout | null>(null);
const popoverRefs = ref<Record<string, any>>({});
const menuItemRefs = ref<Record<string, HTMLElement>>({});

// Methods
const setPopoverRef = (label: string, el: any) => {
  if (el) {
    popoverRefs.value[label] = el;
  }
};

const setMenuItemRef = (label: string, el: any) => {
  if (el && el instanceof HTMLElement) {
    menuItemRefs.value[label] = el;
  }
};

const isActiveRoute = (routePath?: string) => {
  if (!routePath) return false;
  return route.path === routePath || route.path.startsWith(routePath + '/');
};

const getMenuGroups = (items: any[]) => {
  const groupSize = Math.ceil(items.length / 3);
  const groups = [];
  for (let i = 0; i < items.length; i += groupSize) {
    groups.push(items.slice(i, i + groupSize));
  }
  return groups;
};

const handleMenuItemClick = (item: any) => {
  if (item.items) {
    const popover = popoverRefs.value[item.label];
    const menuItem = menuItemRefs.value[item.label];
    if (popover && menuItem) {
      popover.showWithTrigger(menuItem);
    }
  } else if (item.route) {
    emit('item-click', item);
  }
};

const handleMenuItemHover = (item: any) => {
  if (item.items) {
    if (hoverTimer.value) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }
    
    const popover = popoverRefs.value[item.label];
    const menuItem = menuItemRefs.value[item.label];
    if (popover && menuItem) {
      popover.showWithTrigger(menuItem);
    }
  }
};

const handleMenuItemLeave = (item: any) => {
  if (item.items) {
    hoverTimer.value = setTimeout(() => {
      const popover = popoverRefs.value[item.label];
      if (popover) {
        popover.hide();
      }
    }, 300);
  }
};
</script>

<style lang="scss" scoped>
.horizontal-menu {
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  
  .menu-items {
    display: flex;
    align-items: center;
    
    .menu-item {
      position: relative;
      
      .menu-item-content {
        .menu-link {
          display: flex;
          align-items: center;
          padding: 1rem 1.5rem;
          color: var(--text-color);
          text-decoration: none;
          transition: all 0.2s ease;
          cursor: pointer;
          white-space: nowrap;
          
          &:hover {
            background: var(--surface-hover);
          }
          
          &.active {
            background: var(--primary-50);
            color: var(--primary-color);
            font-weight: 500;
            
            .menu-icon i {
              color: var(--primary-color);
            }
          }
          
          .menu-icon {
            margin-right: 0.5rem;
            color: var(--text-color-secondary);
            
            i {
              font-size: 1rem;
            }
          }
          
          .menu-label {
            font-weight: 500;
          }
          
          .submenu-indicator {
            margin-left: 0.5rem;
            color: var(--text-color-secondary);
            font-size: 0.7rem;
            transition: transform 0.2s ease;
          }
          
          &.has-submenu:hover .submenu-indicator {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}
</style>
