package videos

// VideoCommentRequest 视频评论请求验证模型
type VideoCommentRequest struct {
	VideoID  uint   `json:"videoId" validate:"required|min:1"`
	Content  string `json:"content" validate:"required|minLen:1|maxLen:500"`
	ParentID uint   `json:"parentId"` // 父评论ID，0表示顶级评论
}

type VideoCommentReplyRequest struct {
	VideoID  uint   `json:"videoId" validate:"required|min:1"`
	Content  string `json:"content" validate:"required|minLen:1|maxLen:500"`
	ParentID uint   `json:"parentId"` // 父评论ID，0表示顶级评论
}

type UpdateVideoCommentStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status" validate:"int|min:-5|max:5"`
}
