package vips

import (
	"frontapi/internal/models"
)

type CoinPackage struct {
	models.BaseModel
	Name        string  `gorm:"column:name;type:string;not null;comment:套餐名称" json:"name"`                                // 套餐名称
	Code        string  `gorm:"column:code;type:string;not null;unique;comment:套餐编码" json:"code"`                         // 套餐编码
	CoinAmount  int     `gorm:"column:coin_amount;type:int;not null;comment:平台币数量" json:"coin_amount"`                    // 平台币数量
	Price       float64 `gorm:"column:price;type:decimal(10,2);not null;comment:价格" json:"price"`                         // 价格
	BonusCoin   int     `gorm:"column:bonus_coin;type:int;not null;default:0;comment:赠送平台币" json:"bonus_coin"`            // 赠送平台币
	Description string  `gorm:"column:description;type:string;comment:描述" json:"description"`                             // 描述
	Icon        string  `gorm:"column:icon;type:string;comment:图标" json:"icon"`                                           // 图标
	SortOrder   int     `gorm:"column:sort_order;type:int;not null;default:0;comment:排序序号" json:"sort_order"`             // 排序序号
	Status      int8    `gorm:"column:status;type:tinyint;not null;default:1;comment:状态:0-禁用,1-启用" json:"status"`         // 状态：0-禁用，1-启用
}

func (CoinPackage) TableName() string {
	return "ly_coin_packages"
}
