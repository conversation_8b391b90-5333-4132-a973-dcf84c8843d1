package v1

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// LikeTrait Redis点赞功能trait
type LikeTrait struct {
	client   *redis.Client
	itemType string
}

// NewLikeTrait 创建Redis点赞trait
func NewLikeTrait(client *redis.Client, itemType string) *LikeTrait {
	return &LikeTrait{
		client:   client,
		itemType: itemType,
	}
}

// LikeItem 点赞操作
func (t *LikeTrait) LikeItem(ctx context.Context, userID, itemID string) error {
	userLikesKey := fmt.Sprintf("user_likes:%s:%s", t.itemType, userID)
	itemLikesKey := fmt.Sprintf("item_likes:%s:%s", t.itemType, itemID)
	likeRecordKey := fmt.Sprintf("like_record:%s:%s:%s", t.itemType, userID, itemID)

	// 使用事务确保原子性
	txf := func(tx *redis.Tx) error {
		// 检查是否已经点赞
		exists, err := tx.Exists(ctx, likeRecordKey).Result()
		if err != nil {
			return err
		}
		if exists > 0 {
			return fmt.Errorf("already liked")
		}

		// 执行点赞操作
		_, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
			pipe.SAdd(ctx, userLikesKey, itemID)
			pipe.Incr(ctx, itemLikesKey)
			pipe.Set(ctx, likeRecordKey, time.Now().Unix(), 0)
			return nil
		})
		return err
	}

	return t.client.Watch(ctx, txf, likeRecordKey)
}

// UnlikeItem 取消点赞操作
func (t *LikeTrait) UnlikeItem(ctx context.Context, userID, itemID string) error {
	userLikesKey := fmt.Sprintf("user_likes:%s:%s", t.itemType, userID)
	itemLikesKey := fmt.Sprintf("item_likes:%s:%s", t.itemType, itemID)
	likeRecordKey := fmt.Sprintf("like_record:%s:%s:%s", t.itemType, userID, itemID)

	// 使用事务确保原子性
	txf := func(tx *redis.Tx) error {
		// 检查是否已经点赞
		exists, err := tx.Exists(ctx, likeRecordKey).Result()
		if err != nil {
			return err
		}
		if exists == 0 {
			return fmt.Errorf("not liked yet")
		}

		// 执行取消点赞操作
		_, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
			pipe.SRem(ctx, userLikesKey, itemID)
			pipe.Decr(ctx, itemLikesKey)
			pipe.Del(ctx, likeRecordKey)
			return nil
		})
		return err
	}

	return t.client.Watch(ctx, txf, likeRecordKey)
}

// IsLiked 检查是否已点赞
func (t *LikeTrait) IsLiked(ctx context.Context, userID, itemID string) (bool, error) {
	likeRecordKey := fmt.Sprintf("like_record:%s:%s:%s", t.itemType, userID, itemID)
	exists, err := t.client.Exists(ctx, likeRecordKey).Result()
	if err != nil {
		return false, err
	}
	return exists > 0, nil
}

// GetLikeCount 获取点赞数量
func (t *LikeTrait) GetLikeCount(ctx context.Context, itemID string) (int64, error) {
	itemLikesKey := fmt.Sprintf("item_likes:%s:%s", t.itemType, itemID)
	count, err := t.client.Get(ctx, itemLikesKey).Int64()
	if err == redis.Nil {
		return 0, nil
	}
	return count, err
}

// GetUserLikes 获取用户的所有点赞
func (t *LikeTrait) GetUserLikes(ctx context.Context, userID string) ([]string, error) {
	userLikesKey := fmt.Sprintf("user_likes:%s:%s", t.itemType, userID)
	return t.client.SMembers(ctx, userLikesKey).Result()
}

// FlushUserLikes 清空用户点赞缓存
func (t *LikeTrait) FlushUserLikes(ctx context.Context, userID string) error {
	userLikesKey := fmt.Sprintf("user_likes:%s:%s", t.itemType, userID)
	return t.client.Del(ctx, userLikesKey).Err()
}

// FlushAllLikes 清空所有点赞缓存
func (t *LikeTrait) FlushAllLikes(ctx context.Context) error {
	pattern := fmt.Sprintf("*:%s:*", t.itemType)
	keys, err := t.client.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}
	if len(keys) > 0 {
		return t.client.Del(ctx, keys...).Err()
	}
	return nil
}
