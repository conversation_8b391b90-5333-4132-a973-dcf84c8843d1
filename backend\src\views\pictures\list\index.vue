<template>
    <div class="app-container">
        <el-card>
            <template #header>
                <div class="filter-container">
                    <div class="title-container">
                        <h2>图片管理</h2>
                        <div class="buttons">
                            <el-button type="primary" :icon="Plus" @click="handleAdd">上传图片</el-button>
                            <el-button type="primary" :icon="Upload" @click="handleBatchAdd">批量添加图片</el-button>
                            <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- 搜索栏 -->
            <PictureSearchBar :albumOptions="albumOptions" @search="handleSearch" @reset="handleReset"
                @refresh="refreshList" />

            <!-- 表格 -->
            <PictureTable :pictures="pictureList" :loading="loading" :total="pagination.total"
                v-model:currentPage="pagination.page" v-model:pageSize="pagination.pageSize" @refresh="refreshList"
                @view="handleView" @edit="handleEdit" @delete="handleDelete" @change-status="handleChangeStatus" />
        </el-card>

        <!-- 表单对话框 -->
        <PictureFormDialog v-model:visible="dialogVisible" :picture="currentPicture" :albumOptions="albumOptions"
            @success="handleDialogSuccess" />

        <!-- 详情对话框 -->
        <PictureDetailDialog v-model:visible="detailVisible" :picture="currentPicture" @edit="handleEditFromDetail" />

        <!-- 批量上传对话框 -->
        <BatchUploadDialog v-model="batchUploadVisible" :albumOptions="albumOptions"
            @update:modelValue="handleBatchUploadVisible" @success="handleBatchUploadSuccess" />
    </div>
</template>

<script setup lang="ts">
import {
    deletePicture,
    getPictureAlbumList,
    getPictureList,
    updatePictureStatus
} from '@/service/api/pictures/pictures';
import type { Picture, PictureParams } from '@/types/pictures';
import { Plus, Refresh, Upload } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { computed, onMounted, reactive, ref } from 'vue';
import BatchUploadDialog from './components/BatchUploadDialog.vue';
import PictureDetailDialog from './components/PictureDetailDialog.vue';
import PictureFormDialog from './components/PictureFormDialog.vue';
import PictureSearchBar from './components/PictureSearchBar.vue';
import PictureTable from './components/PictureTable.vue';
import { useAlbums } from './composables/useAlbums';
import { useCategories } from './composables/useCategories';

// 定义列表响应接口
interface ListResponse {
    list: Picture[];
    total: number;
    page: number;
    pageSize: number;
}

// 搜索表单
const searchForm = reactive({
    title: '',
    category_id: '',
    album_id: '',
    status: '',
    start_date: '',
    end_date: ''
});

// 分页数据
const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0
});

// 图片列表数据
const pictureList = ref<Picture[]>([]);
const loading = ref(false);

// 对话框控制
const dialogVisible = ref(false);
const batchUploadVisible = ref(false);
const detailVisible = ref(false);
const currentPicture = ref<Picture | null>(null);

// 获取分类和专辑数据
const { categoryOptions: categories } = useCategories();
const { albumOptions: albums } = useAlbums();
const allAlbumOptions = ref<Array<{ value: string, label: string }>>([]);
const albumOptions = ref<Array<{ value: string, label: string }>>([]);
const albumsLoading = ref(false);

// 构建请求参数
const buildRequestParams = computed((): PictureParams => {
    return {
        page: {
            pageNo: pagination.page,
            pageSize: pagination.pageSize,
        },
        data: {
            title: searchForm.title || undefined,
            category_id: searchForm.category_id || undefined,
            album_id: searchForm.album_id || undefined,
            status: searchForm.status === '' ? undefined : searchForm.status,
            start_date: searchForm.start_date || undefined,
            end_date: searchForm.end_date || undefined
        }
    };
});

// 获取图片列表
const fetchPictureList = async () => {
    loading.value = true;
    try {
        const res = await getPictureList(buildRequestParams.value);
        const listData = res.data as unknown as ListResponse;

        pictureList.value = listData.list || [];
        pagination.total = listData.total || 0;
    } catch (error) {
        console.error('获取图片列表失败:', error);
        ElMessage.error('获取图片列表失败');
    } finally {
        loading.value = false;
    }
};

// 刷新列表
const refreshList = () => {
    pagination.page = 1;
    fetchPictureList();
};

// 搜索
const handleSearch = (params: any) => {
    Object.assign(searchForm, params);
    refreshList();
};

// 重置搜索
const handleReset = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key as keyof typeof searchForm] = '';
    });
    refreshList();
};

// 新增图片
const handleAdd = () => {
    currentPicture.value = null;
    dialogVisible.value = true;
};

// 查看图片
const handleView = (picture: Picture) => {
    currentPicture.value = { ...picture };
    detailVisible.value = true;
};

// 编辑图片
const handleEdit = (picture: Picture) => {
    currentPicture.value = { ...picture };
    dialogVisible.value = true;
};

// 从详情页编辑
const handleEditFromDetail = () => {
    dialogVisible.value = true;
};

// 删除图片
const handleDelete = async (picture: Picture) => {
    try {
        await deletePicture(picture.id);
        ElMessage.success('删除图片成功');
        if (pictureList.value.length === 1 && pagination.page > 1) {
            pagination.page--;
        }
        fetchPictureList();
    } catch (error) {
        console.error('删除图片失败:', error);
        ElMessage.error('删除图片失败');
    }
};

// 更新图片状态
const handleChangeStatus = async (params: { id: string; status: number }) => {
    try {
        await updatePictureStatus(params.id, params.status);
        ElMessage.success('更新状态成功');
        fetchPictureList();
    } catch (error) {
        console.error('更新状态失败:', error);
        ElMessage.error('更新状态失败');
    }
};
// 加载专辑选项
const loadAlbumOptions = async () => {
    albumsLoading.value = true;
    try {
        const { response, data } = await getPictureAlbumList({
            page: { pageNo: 1, pageSize: 100 },
            data: { status: 1 }
        }) as any;
        if (response.status == 200 && response.data.code == 2000) {
            const list = data.list;
            const albums = list.map((item: any) => ({
                value: item.id,
                label: item.title
            }));
            allAlbumOptions.value = albums;
            albumOptions.value = albums;
        } else {
            allAlbumOptions.value = [];
            albumOptions.value = [];
        }
    } catch (error) {
        console.error('加载专辑列表失败:', error);
        ElMessage.error('加载专辑列表失败');
        allAlbumOptions.value = [];
        albumOptions.value = [];
    } finally {
        albumsLoading.value = false;
    }
};
// 批量添加图片
const handleBatchAdd = () => {
    batchUploadVisible.value = true;
};

const handleBatchUploadVisible = (value: boolean) => {
    batchUploadVisible.value = value;
};

const handleBatchUploadSuccess = () => {
    batchUploadVisible.value = false;
    fetchPictureList();
};

// 对话框操作成功
const handleDialogSuccess = () => {
    dialogVisible.value = false;
    fetchPictureList();
};

// 初始化
onMounted(() => {
    fetchPictureList();
    loadAlbumOptions();
});
</script>

<style scoped lang="scss">
.app-container {
    padding: 20px;

    .filter-container {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;

            h2 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
            }

            .buttons {
                display: flex;
                gap: 8px;
            }
        }
    }
}
</style>
