package videos

import "frontapi/pkg/types"

// CreateVideoAlbumRequest 创建视频专辑请求
type CreateVideoAlbumRequest struct {
	Title        string            `json:"title" validate:"required|minLen:1|maxLen:255"`
	Description  string            `json:"description"`
	UserID       string            `json:"user_id" validate:"required"`
	UserNickname string            `json:"user_nickname"`
	UserAvatar   string            `json:"user_avatar"`
	Cover        string            `json:"cover"`
	CategoryID   string            `json:"category_id"`
	CategoryName string            `json:"category_name"`
	Tags         types.StringArray `json:"tags"`
	IsPaid       int8              `json:"is_paid"`
	Price        float64           `json:"price"`
}

// UpdateVideoAlbumRequest 更新视频专辑请求
type UpdateVideoAlbumRequest struct {
	Title        string            `json:"title"`
	Description  string            `json:"description"`
	UserID       string            `json:"user_id"`
	UserNickname string            `json:"user_nickname"`
	UserAvatar   string            `json:"user_avatar"`
	Cover        string            `json:"cover"`
	CategoryID   string            `json:"category_id"`
	CategoryName string            `json:"category_name"`
	Tags         types.StringArray `json:"tags"`
	IsPaid       int8              `json:"is_paid"`
	Price        float64           `json:"price"`
	Status       int8              `json:"status"`
}

// VideoAlbumListRequest 视频专辑列表请求
type VideoAlbumListRequest struct {
	Keyword    string `json:"keyword"`
	CreatorID  string `json:"creator_id"`
	CategoryID string `json:"category_id"`
	Status     int    `json:"status"`
	IsPaid     int    `json:"is_paid"`
	SortBy     string `json:"sort_by"`
}

type VideosAlbumsListParams struct {
	Keyword    string `json:"keyword"`
	UserID     string `json:"user_id"`
	CategoryID string `json:"category_id"`
	Status     int8   `json:"status"`
}

type VideosAlbumParams struct {
	ID           string            `json:"id"`
	Title        string            `json:"title"`
	Description  string            `json:"description"`
	UserID       *string           `json:"user_id"`
	UserNickname string            `json:"user_nickname"`
	UserAvatar   string            `json:"user_avatar"`
	Cover        string            `json:"cover"`
	CategoryID   *string           `json:"category_id"`
	CategoryName string            `json:"category_name"`
	Tags         types.StringArray `json:"tags"`
	IsPaid       int8              `json:"is_paid"`
	Price        float64           `json:"price"`
	Status       int8              `json:"status"`
}
