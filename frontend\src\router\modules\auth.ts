import { RouteRecordRaw } from 'vue-router';

const authRoutes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/auth/login/index.vue'),
        meta: {
            title: 'login',
            hideInMenu: true
        }
    },
    {
        path: '/register',
        name: 'Register',
        component: () => import('@/views/auth/register/index.vue'),
        meta: {
            title: 'register',
            hideInMenu: true
        }
    }
];

export default authRoutes; 