package books

import (
	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// CategoryRepository 电子书分类仓库接口
type CategoryRepository interface {
	base.ExtendedRepository[books.BookCategory]
}

type categoryRepository struct {
	base.ExtendedRepository[books.BookCategory]
}

// NewCategoryRepository 创建电子书分类仓库实例
func NewCategoryRepository(db *gorm.DB) CategoryRepository {
	return &categoryRepository{
		ExtendedRepository: base.NewExtendedRepository[books.BookCategory](db),
	}
}
