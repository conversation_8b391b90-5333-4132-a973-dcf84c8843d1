import type { ColumnInfo, TableDetailResponse } from '@/types/database';
import Mock from 'mockjs';

export interface MockDataGeneratorOptions {
    count: number;
    mockRules?: Record<string, string>;
    excludedColumns?: string[];
}

export interface GeneratedMockData {
    data: Record<string, any>[];
    sql: string;
    count: number;
}

export class MockDataGenerator {
    private tableDetail: TableDetailResponse;
    private tableName: string;

    constructor(tableDetail: TableDetailResponse, tableName: string) {
        this.tableDetail = tableDetail;
        this.tableName = tableName;
    }

    /**
     * 生成Mock数据
     */
    generate(options: MockDataGeneratorOptions): GeneratedMockData {
        const { count, mockRules = {}, excludedColumns = [] } = options;

        // 过滤掉排除的列和自增列
        const validColumns = this.tableDetail.columns.filter(column =>
            !excludedColumns.includes(column.column_name) &&
            !column.extra?.includes('auto_increment')
        );

        // 构建Mock模板
        const mockTemplate = this.buildMockTemplate(validColumns, mockRules);

        // 生成数据
        const mockData = Mock.mock({
            [`list|${count}`]: [mockTemplate]
        });

        const data = mockData.list;
        const sql = this.generateInsertSQL(data);

        return {
            data,
            sql,
            count: data.length
        };
    }

    /**
     * 构建Mock模板
     */
    private buildMockTemplate(columns: ColumnInfo[], mockRules: Record<string, string>): Record<string, any> {
        const template: Record<string, any> = {};

        columns.forEach(column => {
            const columnName = column.column_name;

            // 优先使用自定义规则
            if (mockRules[columnName]) {
                template[columnName] = this.parseMockRule(mockRules[columnName], column);
                return;
            }

            // 根据字段类型和名称自动生成规则
            template[columnName] = this.generateMockRule(column);
        });

        return template;
    }

    /**
     * 解析自定义Mock规则
     */
    private parseMockRule(rule: string, column: ColumnInfo): any {
        // 处理特殊标识
        if (rule === 'GENERATE_IMAGE_ARRAY') {
            return () => this.generateImageArray();
        }

        // 处理函数调用字符串
        if (rule.includes('generateImageArray()')) {
            return () => this.generateImageArray();
        }

        // 处理@开头的Mock.js规则
        if (rule.startsWith('@')) {
            const ruleName = rule.substring(1);

            // 处理带参数的规则，如 @integer(1,100)
            if (ruleName.includes('(')) {
                const [name, params] = ruleName.split('(');
                const paramStr = params.replace(')', '');

                switch (name) {
                    case 'integer':
                    case 'natural':
                    case 'float':
                        return rule; // 直接返回原规则
                    case 'string':
                        return rule;
                    case 'ctitle':
                    case 'title':
                    case 'cparagraph':
                    case 'sentence':
                    case 'cword':
                        return rule;
                    case 'datetime':
                    case 'date':
                    case 'time':
                        return rule;
                    case 'image':
                        return rule;
                    case 'pick':
                        return rule;
                    case 'county':
                        return rule;
                    default:
                        return rule;
                }
            }

            // 处理简单规则
            switch (ruleName) {
                case 'cname':
                    return '@cname';
                case 'name':
                    return '@name';
                case 'email':
                    return '@email';
                case 'phone':
                    return /^1[3-9]\d{9}$/;
                case 'id':
                    return '@id';
                case 'uuid':
                    return '@guid';
                case 'guid':
                    return '@guid';
                case 'url':
                    return '@url';
                case 'domain':
                    return '@domain';
                case 'ip':
                    return '@ip';
                case 'datetime':
                    return '@datetime("yyyy-MM-dd HH:mm:ss")';
                case 'date':
                    return '@date("yyyy-MM-dd")';
                case 'time':
                    return '@time("HH:mm:ss")';
                case 'province':
                    return '@province';
                case 'city':
                    return '@city';
                case 'county':
                    return '@county';
                case 'region':
                    return '@region';
                case 'company':
                    return '@company';
                case 'color':
                    return '@color';
                case 'hex':
                    return '@hex';
                case 'rgb':
                    return '@rgb';

                // 自定义规则
                case 'username':
                    return () => this.generateUsername();
                case 'simple_username':
                    return () => this.generateSimpleUsername();
                case 'recommend_code':
                    return () => this.generateRecommendCode();
                case 'invite_code':
                    return () => this.generateInviteCode();
                case 'salt':
                    return () => this.generateSalt();
                case 'token':
                    return () => this.generateToken();
                case 'device_id':
                    return () => this.generateDeviceId();
                case 'mac_address':
                    return () => this.generateMacAddress();

                default:
                    return rule;
            }
        }

        return rule;
    }

    /**
     * 生成英文字母数字组合的用户名
     */
    private generateUsername(): string {
        const letters = 'abcdefghijklmnopqrstuvwxyz';
        const numbers = '0123456789';

        let username = '';

        // 3-5个字母开头
        const letterCount = Math.floor(Math.random() * 3) + 3;
        for (let i = 0; i < letterCount; i++) {
            username += letters[Math.floor(Math.random() * letters.length)];
        }

        // 1-4个数字结尾
        const numberCount = Math.floor(Math.random() * 4) + 1;
        for (let i = 0; i < numberCount; i++) {
            username += numbers[Math.floor(Math.random() * numbers.length)];
        }

        return username;
    }

    /**
     * 生成简单用户名
     */
    private generateSimpleUsername(): string {
        const prefixes = ['user', 'admin', 'test', 'demo', 'guest', 'member', 'player', 'dev'];
        const suffixes = ['123', '456', '789', '2023', '2024', 'pro', 'vip', 'new'];

        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];

        // 随机决定是否添加数字
        const addNumbers = Math.random() > 0.5;
        if (addNumbers) {
            const randomNum = Math.floor(Math.random() * 9999) + 1;
            return `${prefix}${randomNum}`;
        } else {
            return `${prefix}${suffix}`;
        }
    }

    /**
     * 生成推荐码
     */
    private generateRecommendCode(): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        return result;
    }

    /**
     * 生成邀请码
     */
    private generateInviteCode(): string {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        return result;
    }

    /**
     * 生成密码盐值
     */
    private generateSalt(): string {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 16; i++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        return result;
    }

    /**
     * 生成Token
     */
    private generateToken(): string {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 32; i++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        return result;
    }

    /**
     * 生成设备ID
     */
    private generateDeviceId(): string {
        const chars = 'abcdef0123456789';
        let result = '';
        for (let i = 0; i < 16; i++) {
            result += chars[Math.floor(Math.random() * chars.length)];
        }
        return result.toUpperCase();
    }

    /**
     * 生成MAC地址
     */
    private generateMacAddress(): string {
        const hexChars = '0123456789ABCDEF';
        const macParts = [];
        for (let i = 0; i < 6; i++) {
            let part = '';
            for (let j = 0; j < 2; j++) {
                part += hexChars.charAt(Math.floor(Math.random() * hexChars.length));
            }
            macParts.push(part);
        }
        return macParts.join(':');
    }

    /**
     * 生成图片数组JSON字符串
     */
    private generateImageArray(): string {
        // 随机生成1-5张图片
        const imageCount = Math.floor(Math.random() * 5) + 1;
        const images: string[] = [];

        for (let i = 0; i < imageCount; i++) {
            // 随机尺寸
            const widths = [400, 600, 800, 1200];
            const heights = [300, 400, 600, 800];
            const width = widths[Math.floor(Math.random() * widths.length)];
            const height = heights[Math.floor(Math.random() * heights.length)];

            // 生成随机图片URL
            const randomId = Math.floor(Math.random() * 1000) + 1;
            images.push(`https://picsum.photos/${width}/${height}?random=${randomId}`);
        }

        return JSON.stringify(images);
    }

    /**
     * 根据字段信息自动生成Mock规则
     */
    private generateMockRule(column: ColumnInfo): any {
        const columnName = column.column_name.toLowerCase();
        const dataType = column.data_type.toLowerCase();
        const columnType = column.column_type.toLowerCase();

        // 优先根据字段名称推断（保持原有的智能推断）
        if (columnName.includes('email')) {
            return '@email';
        }
        if (columnName.includes('phone') || columnName.includes('mobile')) {
            return /^1[3-9]\d{9}$/;
        }

        // 特殊处理：images字段且为json类型时，生成图片数组
        if (columnName === 'images' && (dataType === 'json' || dataType === 'text' || dataType === 'longtext')) {
            return () => this.generateImageArray();
        }

        // 用户名字段识别
        if (columnName === 'username' || columnName.includes('user_name')) {
            return () => this.generateUsername();
        }

        // 推荐码、邀请码等系统字段
        if (columnName.includes('recommend_code') || columnName.includes('referral_code')) {
            return () => this.generateRecommendCode();
        }
        if (columnName.includes('invite_code') || columnName.includes('invitation_code')) {
            return () => this.generateInviteCode();
        }
        if (columnName.includes('salt')) {
            return () => this.generateSalt();
        }
        if (columnName.includes('token') && !columnName.includes('_token_')) {
            return () => this.generateToken();
        }
        if (columnName.includes('device_id') || columnName.includes('deviceid')) {
            return () => this.generateDeviceId();
        }
        if (columnName.includes('mac_address') || columnName.includes('mac')) {
            return () => this.generateMacAddress();
        }

        if (columnName.includes('name') && !columnName.includes('_name')) {
            return '@cname';
        }
        if (columnName.includes('title')) {
            const length = this.extractLength(columnType);
            if (length && length > 0) {
                const maxLen = Math.min(length, 50); // 标题不超过50个字符
                return `@ctitle(3, ${maxLen})`;
            }
            return '@ctitle(5, 20)';
        }
        if (columnName.includes('content') || columnName.includes('description')) {
            const length = this.extractLength(columnType);
            if (length && length > 100) {
                return '@cparagraph(1, 3)';
            } else if (length && length > 0) {
                const maxLen = Math.min(length, 100);
                return `@string(5, ${maxLen})`;
            }
            return '@cparagraph(1, 3)';
        }
        if (columnName.includes('url') || columnName.includes('link')) {
            return '@url';
        }
        if (columnName.includes('ip')) {
            return '@ip';
        }
        if (columnName.includes('uuid') || columnName.includes('guid')) {
            return '@guid';
        }
        if (columnName.includes('address')) {
            return '@county(true)';
        }
        if (columnName.includes('company')) {
            return '@company';
        }
        if (columnName.includes('image') || columnName.includes('avatar') || columnName.includes('photo')) {
            // return '@image("200x200")';
            return 'https://picsum.photos/200';
        }

        // 根据数据类型和长度生成精确的规则
        switch (dataType) {
            case 'varchar':
            case 'char':
                const varcharLength = this.extractLength(columnType);
                if (varcharLength && varcharLength > 0) {
                    if (varcharLength <= 10) {
                        // 短字符串，生成简单字符串
                        return `@string(1, ${varcharLength})`;
                    } else if (varcharLength <= 50) {
                        // 中等长度，生成词汇或短句
                        return `@cword(2, ${Math.min(varcharLength, 20)})`;
                    } else if (varcharLength <= 255) {
                        // 长字符串，生成句子
                        const maxWords = Math.min(Math.floor(varcharLength / 5), 20);
                        return `@ctitle(3, ${maxWords})`;
                    } else {
                        // 超长字符串，生成段落
                        return '@cparagraph(1, 2)';
                    }
                }
                return '@string(5, 20)';

            case 'text':
            case 'longtext':
            case 'mediumtext':
                // 文本类型，生成段落
                return '@cparagraph(1, 3)';

            case 'int':
            case 'integer':
                const intLength = this.extractLength(columnType);
                if (columnName.includes('status')) {
                    return '@pick([0, 1])';
                }
                if (columnName.includes('type') || columnName.includes('category')) {
                    return '@integer(1, 10)';
                }

                // 根据int长度限制生成范围
                if (intLength) {
                    const maxValue = Math.pow(10, intLength) - 1;
                    return `@integer(1, ${Math.min(maxValue, 999999)})`;
                }
                return '@integer(1, 100000)';

            case 'bigint':
                if (columnName.includes('status')) {
                    return '@pick([0, 1])';
                }
                return '@integer(1, 999999999)';

            case 'tinyint':
                const tinyintLength = this.extractLength(columnType);
                const comment = column.column_comment?.toLowerCase() || '';

                // 根据注释判断具体类型 - 使用增强的枚举解析
                if (comment) {
                    const enumValues = this.parseEnhancedCommentEnumValues(comment);
                    if (enumValues && enumValues.length > 0) {
                        return this.generateEnhancedEnumMockRule(enumValues);
                    }

                    // 检查是否是明确的布尔值字段
                    const booleanPattern = /(是否|启用|禁用|开启|关闭|验证|确认|true|false|boolean|bool)/;
                    if (booleanPattern.test(comment)) {
                        return '@pick([0, 1])';
                    }
                }

                // 常见的布尔值字段名
                if (columnName.includes('status') || columnName.includes('enabled') ||
                    columnName.includes('verified') || columnName.includes('deleted') ||
                    columnName.includes('is_') || columnName.includes('has_') ||
                    columnName.includes('can_') || columnName.includes('active')) {
                    return '@pick([0, 1])';
                }

                // 性别字段
                if (columnName.includes('gender') || columnName.includes('sex')) {
                    return '@pick([0, 1, 2])';
                }

                // 根据tinyint长度生成范围
                if (tinyintLength) {
                    if (tinyintLength === 1) {
                        return '@pick([0, 1])'; // 通常是布尔值
                    } else if (tinyintLength === 2) {
                        return '@pick([0, 1, 2, 3])'; // 小范围枚举
                    } else if (tinyintLength === 3) {
                        return '@pick([0, 1, 2, 3, -1, -2])'; // 包含负数的枚举
                    } else {
                        return '@integer(0, 127)'; // tinyint最大值
                    }
                }

                // 默认为布尔值
                return '@pick([0, 1])';

            case 'smallint':
                const smallintLength = this.extractLength(columnType);
                if (smallintLength) {
                    const maxValue = Math.pow(10, smallintLength) - 1;
                    return `@integer(0, ${Math.min(maxValue, 32767)})`;
                }
                return '@integer(0, 32767)';

            case 'mediumint':
                const mediumintLength = this.extractLength(columnType);
                if (mediumintLength) {
                    const maxValue = Math.pow(10, mediumintLength) - 1;
                    return `@integer(0, ${Math.min(maxValue, 8388607)})`;
                }
                return '@integer(0, 8388607)';

            case 'decimal':
            case 'numeric':
                const decimalInfo = this.extractDecimalInfo(columnType);
                if (decimalInfo) {
                    const { precision, scale } = decimalInfo;
                    const maxIntegerPart = precision - scale;
                    const maxValue = Math.pow(10, maxIntegerPart) - 1;

                    if (columnName.includes('price') || columnName.includes('amount') || columnName.includes('money')) {
                        return `@float(0, ${Math.min(maxValue, 10000)}, ${scale}, ${scale})`;
                    }
                    return `@float(0, ${Math.min(maxValue, 1000)}, ${scale}, ${scale})`;
                }

                if (columnName.includes('price') || columnName.includes('amount') || columnName.includes('money')) {
                    return '@float(0, 10000, 2, 2)';
                }
                return '@float(0, 100, 2, 2)';

            case 'float':
            case 'double':
                const floatLength = this.extractLength(columnType);
                if (columnName.includes('price') || columnName.includes('amount') || columnName.includes('money')) {
                    return '@float(0, 10000, 2, 2)';
                }

                if (floatLength) {
                    const maxValue = Math.pow(10, floatLength - 2);
                    return `@float(0, ${Math.min(maxValue, 10000)}, 2, 2)`;
                }
                return '@float(0, 100, 2, 2)';

            case 'datetime':
            case 'timestamp':
                return '@datetime("yyyy-MM-dd HH:mm:ss")';

            case 'date':
                return '@date("yyyy-MM-dd")';

            case 'time':
                return '@time("HH:mm:ss")';

            case 'year':
                return '@integer(1970, 2030)';

            case 'json':
                return '{"key": "@string(5, 10)", "value": "@integer(1, 100)"}';

            case 'enum':
                const enumValues = this.extractEnumValues(columnType);
                if (enumValues && enumValues.length > 0) {
                    return `@pick([${enumValues.map(v => `"${v}"`).join(', ')}])`;
                }
                return '@string(5, 10)';

            case 'set':
                const setValues = this.extractEnumValues(columnType);
                if (setValues && setValues.length > 0) {
                    return `@pick([${setValues.map(v => `"${v}"`).join(', ')}])`;
                }
                return '@string(5, 10)';

            case 'binary':
            case 'varbinary':
                const binaryLength = this.extractLength(columnType);
                if (binaryLength && binaryLength > 0) {
                    return `@string("lower", ${binaryLength})`;
                }
                return '@string("lower", 16)';

            case 'blob':
            case 'longblob':
            case 'mediumblob':
            case 'tinyblob':
                return '@string("lower", 32)';

            default:
                // 未知类型，根据是否允许为空决定
                if (column.is_nullable === 'YES') {
                    return '@pick([null, "@string(5, 20)"])';
                }
                return '@string(5, 20)';
        }
    }

    /**
     * 从字段类型中提取长度
     */
    private extractLength(columnType: string): number | null {
        const match = columnType.match(/\((\d+)\)/);
        return match ? parseInt(match[1]) : null;
    }

    /**
     * 从decimal类型中提取精度和标度信息
     */
    private extractDecimalInfo(columnType: string): { precision: number; scale: number } | null {
        const match = columnType.match(/\((\d+),\s*(\d+)\)/);
        if (match) {
            return {
                precision: parseInt(match[1]),
                scale: parseInt(match[2])
            };
        }
        return null;
    }

    /**
     * 从enum/set类型中提取可选值
     */
    private extractEnumValues(columnType: string): string[] | null {
        const match = columnType.match(/\((.*)\)/);
        if (match) {
            const valuesStr = match[1];
            // 解析枚举值，处理单引号包围的值
            const values = valuesStr.split(',').map(v => v.trim().replace(/^'|'$/g, ''));
            return values;
        }
        return null;
    }

    /**
     * 增强的枚举值解析函数
     */
    private parseEnhancedCommentEnumValues(comment: string): Array<{ value: number, label: string }> | null {
        if (!comment) return null;

        // 清理注释，移除多余的空格和标点
        const cleanComment = comment.trim().replace(/\s+/g, ' ');

        // 多种枚举格式的正则表达式
        const patterns = [
            // 格式1: "0-未知，1-男，2-女" 或 "0-未知,1-男,2-女"
            {
                regex: /(\d+)[-－]([^,，;；]+)(?:[,，;；]|$)/g,
                name: 'dash_format'
            },
            // 格式2: "1普通用户，2VIP用户，3蓝标用户" 或 "1普通用户,2VIP用户,3蓝标用户"
            {
                regex: /(\d+)([^,，;；\d]+?)(?=\d|$|[,，;；])/g,
                name: 'number_text_format'
            },
            // 格式3: "0=未知,1=男,2=女" 或 "0＝未知，1＝男，2＝女"
            {
                regex: /(\d+)[=＝]([^,，;；]+)(?:[,，;；]|$)/g,
                name: 'equals_format'
            },
            // 格式4: "0:未知,1:男,2:女" 或 "0：未知，1：男，2：女"
            {
                regex: /(\d+)[:：]([^,，;；]+)(?:[,，;；]|$)/g,
                name: 'colon_format'
            },
            // 格式5: "状态：0-禁用，1-正常" (带前缀的格式)
            {
                regex: /[^：:]*[:：]\s*(\d+)[-－]([^,，;；]+)(?:[,，;；]|$)/g,
                name: 'prefix_dash_format'
            }
        ];

        let matches: RegExpMatchArray[] = [];

        // 尝试所有模式
        for (const pattern of patterns) {
            const patternMatches = [...cleanComment.matchAll(pattern.regex)];
            if (patternMatches.length > 0) {
                matches = patternMatches;
                break;
            }
        }

        if (matches.length === 0) {
            // 尝试提取纯数字列表 "0,1,2,3" 或 "0 1 2 3"
            const numberPattern = /\b(\d+)\b/g;
            const numberMatches = [...cleanComment.matchAll(numberPattern)];
            if (numberMatches.length >= 2) {
                return numberMatches.map((match) => ({
                    value: parseInt(match[1], 10),
                    label: `选项${parseInt(match[1], 10)}`
                }));
            }
            return null;
        }

        // 解析匹配结果
        const enumValues = matches.map(match => {
            const value = parseInt(match[1], 10);
            let label = match[2]?.trim() || `选项${value}`;

            // 清理标签中的特殊字符和多余空格
            label = label.replace(/[,，。.；;：:]/g, '').trim();

            // 如果标签为空，使用默认标签
            if (!label) {
                label = `选项${value}`;
            }

            return {
                value,
                label
            };
        });

        // 去重并排序
        const uniqueValues = enumValues.filter((item, index, self) =>
            index === self.findIndex(t => t.value === item.value)
        ).sort((a, b) => a.value - b.value);

        return uniqueValues.length > 0 ? uniqueValues : null;
    }

    /**
     * 生成枚举字段的Mock规则
     */
    private generateEnhancedEnumMockRule(enumValues: Array<{ value: number, label: string }>): string {
        if (!enumValues || enumValues.length === 0) return '@pick([0, 1])';

        // 只获取数值，用于Mock生成
        const values = enumValues.map(ev => ev.value).filter(v => !isNaN(v));

        if (values.length === 0) return '@pick([0, 1])';

        // 对于只有两个值(0,1)的情况，用pick随机生成
        if (values.length === 2 && values.includes(0) && values.includes(1)) {
            return '@pick([0, 1])';
        }

        // 检查是否是连续的数字序列
        const sortedValues = [...values].sort((a, b) => a - b);
        const min = sortedValues[0];
        const max = sortedValues[sortedValues.length - 1];

        // 如果值是连续的，使用范围随机
        if (max - min + 1 === values.length) {
            return `@integer(${min}, ${max})`;
        }

        // 如果值不连续，使用pick从具体值中选择
        return `@pick([${values.join(', ')}])`;
    }

    /**
     * 生成INSERT SQL语句
     */
    private generateInsertSQL(data: Record<string, any>[]): string {
        if (!data.length) return '';

        const columns = Object.keys(data[0]);
        const columnNames = columns.map(col => `\`${col}\``).join(', ');

        const values = data.map(row => {
            const rowValues = columns.map(col => {
                const value = row[col];
                if (value === null || value === undefined) {
                    return 'NULL';
                }
                if (typeof value === 'string') {
                    return `'${value.replace(/'/g, "''")}'`;
                }
                if (typeof value === 'boolean') {
                    return value ? '1' : '0';
                }
                return value.toString();
            });
            return `(${rowValues.join(', ')})`;
        }).join(',\n  ');

        return `INSERT INTO \`${this.tableName}\` (${columnNames}) VALUES\n  ${values};`;
    }
}

/**
 * 创建Mock数据生成器实例
 */
export function createMockDataGenerator(tableDetail: TableDetailResponse, tableName: string): MockDataGenerator {
    return new MockDataGenerator(tableDetail, tableName);
}
