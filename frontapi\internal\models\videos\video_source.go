package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoSource 视频源表
type VideoSource struct {
	*models.BaseModelStruct
	VideoID string `gorm:"column:video_id;index" json:"video_id"` // 视频ID
	Quality string `gorm:"column:quality" json:"quality"`         // 画质
	URL     string `gorm:"column:url" json:"url"`                 // 视频URL
	Size    uint64 `gorm:"column:size" json:"size"`               // 文件大小(字节)
	Format  string `gorm:"column:format" json:"format"`           // 视频格式
}

// TableName 设置表名
func (VideoSource) TableName() string {
	return "ly_video_sources"
}

// 实现BaseModel接口的方法
func (v *VideoSource) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoSource) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoSource) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoSource) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
