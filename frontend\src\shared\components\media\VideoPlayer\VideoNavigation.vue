<template>
  <div class="video-navigation">
    <el-button type="primary" @click="goPrevious">
      <el-icon><ArrowLeft /></el-icon>
      <span>上一集</span>
    </el-button>
    <el-button type="primary" @click="goNext">
      <el-icon><ArrowRight /></el-icon>
      <span>下一集</span>
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const goPrevious = () => {
  console.log('Go to previous video')
  // 在这里添加导航到上一集的逻辑
}

const goNext = () => {
  console.log('Go to next video')
  // 在这里添加导航到下一集的逻辑
}
</script>

<style scoped lang="scss">
.video-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;

  .el-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    padding: 0 20px;
    
    .el-icon {
      font-size: 16px;
      margin-right: 4px;
      vertical-align: middle;
    }

    span {
      font-size: 14px;
      line-height: 1;
      vertical-align: middle;
    }
  }
}
</style> 