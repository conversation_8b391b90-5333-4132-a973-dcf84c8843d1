package promotion

import (
	model "frontapi/internal/models/promotion"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// InvitationRuleRepository 邀请奖励规则数据访问接口
type InvitationRuleRepository interface {
	base.ExtendedRepository[model.InvitationRule]
}

type invitationRuleRepository struct {
	base.ExtendedRepository[model.InvitationRule]
}

func NewInvitationRuleRepository(db *gorm.DB) InvitationRuleRepository {
	return &invitationRuleRepository{
		ExtendedRepository: base.NewExtendedRepository[model.InvitationRule](db),
	}
}
