package utils

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/gofiber/fiber/v2"
)

// PageRequest 分页请求参数
type PageRequest struct {
	PageNo   int `json:"pageNo"`
	PageSize int `json:"pageSize"`
}

// RequestInfo 请求信息，用于链式调用
type RequestInfo struct {
	Data       *DataNode    // 动态数据，可通过键访问
	Page       *PageRequest // 分页信息
	Ctx        *fiber.Ctx   // Fiber上下文
	IsValid    bool         // 请求是否有效
	IsAuthUser bool         // 是否已认证用户
	UserID     string       // 用户ID，若已认证
	RawData    interface{}  // 原始数据
}

// GetRequestInfo 获取请求信息，支持链式调用
func GetRequestInfo(c *fiber.Ctx) *RequestInfo {
	// 创建请求信息对象
	reqInfo := &RequestInfo{
		Ctx:     c,
		IsValid: true,
		Data:    &DataNode{value: make(map[string]interface{})},
		Page: &PageRequest{
			PageNo:   1,
			PageSize: 10,
		},
	}

	// 尝试获取用户信息
	userID, err := GetUserID(c)
	if err == nil && userID != "" {
		reqInfo.IsAuthUser = true
		reqInfo.UserID = userID
	}

	// 根据 content-type 处理请求体
	contentType := c.Get("Content-Type")

	if strings.Contains(contentType, "form") {
		// 处理表单数据
		form := make(map[string]interface{})
		if err := c.BodyParser(&form); err == nil {

			// 解析 data 字段
			if dataVal, ok := form["data"]; ok {
				switch v := dataVal.(type) {
				case string:
					if v != "" {
						// 尝试将字符串解析为 JSON
						var jsonData interface{}
						if err := json.Unmarshal([]byte(v), &jsonData); err != nil {
							// 不是有效的 JSON，直接存储字符串
							reqInfo.Data = &DataNode{value: v}
						} else {
							reqInfo.Data = &DataNode{value: jsonData}
						}
					}
				case map[string]interface{}:
					reqInfo.Data = &DataNode{value: v}
				default:
					// 尝试转换为 JSON
					jsonBytes, _ := json.Marshal(v)
					var jsonData interface{}
					if err := json.Unmarshal(jsonBytes, &jsonData); err == nil {
						reqInfo.Data = &DataNode{value: jsonData}
					}
				}
			} else {
				// 没有 data 字段，直接使用整个表单
				reqInfo.Data = &DataNode{value: form}
			}

			// 解析分页信息
			if pageVal, ok := form["page"]; ok {
				var page PageRequest
				jsonBytes, _ := json.Marshal(pageVal)
				if err := json.Unmarshal(jsonBytes, &page); err == nil {
					reqInfo.Page = &page
				}
			}
		} else {
			fmt.Printf("form err: %v", err)
		}

	} else {
		// 处理 JSON 数据
		var standardReq struct {
			Data interface{}  `json:"data"`
			Page *PageRequest `json:"page"`
		}

		if err := c.BodyParser(&standardReq); err == nil {
			// 处理 data 字段
			if standardReq.Data != nil {
				reqInfo.Data = &DataNode{value: standardReq.Data}
			}

			// 处理 page 字段
			if standardReq.Page != nil {
				reqInfo.Page = standardReq.Page
			}
		} else {
			// 尝试直接解析整个请求体
			var directData map[string]interface{}
			if err := c.BodyParser(&directData); err == nil {
				reqInfo.Data = &DataNode{value: directData}
			}
		}
	}

	// 处理查询参数
	if reqInfo.Page != nil {
		// 从查询参数更新分页信息
		pageNoQuery := c.QueryInt("pageNo", 0)
		pageSizeQuery := c.QueryInt("pageSize", 0)

		if pageNoQuery > 0 {
			reqInfo.Page.PageNo = pageNoQuery
		}
		if pageSizeQuery > 0 {
			reqInfo.Page.PageSize = pageSizeQuery
		}
	}

	// 验证和修正分页参数
	if reqInfo.Page.PageNo < 1 {
		reqInfo.Page.PageNo = 1
	}
	if reqInfo.Page.PageSize < 1 || reqInfo.Page.PageSize > 100 {
		reqInfo.Page.PageSize = 10
	}

	return reqInfo
}

// ParseObject 将数据解析到结构体
func (r *RequestInfo) ParseObject(obj interface{}) error {
	if r.Data == nil || r.Data.value == nil {
		return errors.New("没有可用数据")
	}

	jsonBytes, err := json.Marshal(r.Data.value)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonBytes, obj)
}

// 为RequestInfo添加动态属性访问器
func (r *RequestInfo) Get(key string) *DataNode {
	if r.Data == nil {
		return &DataNode{value: nil}
	}
	return r.Data.Get(key)
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *fiber.Ctx) (string, error) {
	userID := c.Locals("userId")
	if userID == nil {
		userID = c.Locals("user_id")
	}
	if userID == nil {
		return "", errors.New("没有用户ID")
	}
	return fmt.Sprintf("%v", userID), nil
}
