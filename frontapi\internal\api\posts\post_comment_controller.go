package posts

import (
	"frontapi/config/constant"
	"frontapi/internal/api"
	postModel "frontapi/internal/models/posts"
	service "frontapi/internal/service/posts"
	userSrv "frontapi/internal/service/users"
	postTypings "frontapi/internal/typings/posts"
	postValidator "frontapi/internal/validation/posts"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
	"github.com/guregu/null/v6"
)

// PostCommentController 帖子评论控制器
type PostCommentController struct {
	api.BaseController // 继承Response
	PostCommentService service.PostCommentService
	UserService        userSrv.UserService
}

// NewPostCommentController 创建帖子评论控制器实例
func NewPostCommentController(postCommentService service.PostCommentService, userService userSrv.UserService) *PostCommentController {
	return &PostCommentController{
		PostCommentService: postCommentService,
		UserService:        userService,
	}
}

// GetPostCommentList 获取帖子评论列表
func (c *PostCommentController) GetPostCommentList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	postID := reqInfo.Get("postId").GetString()
	if postID == "" {
		return c.BadRequest(ctx, "postId is required", nil)
	}
	userId := c.GetUserID(ctx)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at desc"
	}
	condition := map[string]interface{}{
		"post_id": postID,
		"status":  2,
	}
	comments, total, err := c.PostCommentService.List(ctx.Context(), condition, orderBy, page, pageSize, true)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	for _, comment := range comments {
		if userId == "" {
			comment.IsLiked, err = c.PostCommentService.IsLikeComment(ctx.Context(), comment.GetID(), userId)
			if err != nil {
				return c.InternalServerError(ctx, err.Error())
			}
		}
		//获取评论者信息
		if comment.UserID.ValueOrZero() == "" {
			continue
		}
		user, err := c.UserService.GetByID(ctx.Context(), comment.UserID.ValueOrZero(), true)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		if user != nil {
			comment.Author = user
		}
	}

	postCommentResponse := postTypings.ConvertPostCommentListResponse(comments, total, page, pageSize)
	return c.Success(ctx, postCommentResponse)
}

// GetCommentDetail 获取评论详情
func (c *PostCommentController) GetCommentDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	commentID := reqInfo.Get("comment_id").GetString()
	comment, err := c.PostCommentService.GetByID(ctx.Context(), commentID, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.Success(ctx, comment)
}

// AddComment 添加评论
func (c *PostCommentController) AddComment(ctx *fiber.Ctx) error {
	// reqInfo := c.GetRequestInfo(ctx)
	var req postValidator.CreateCommentRequest
	// 验证请求参数
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, err.Error(), nil)
	}
	userId := null.StringFrom(c.GetUserID(ctx))

	postComment := &postModel.PostComment{
		PostID:  req.PostID,
		UserID:  userId,
		Content: req.Content,
		Status:  constant.CommentState.CommentStatusNormal,
	}
	comment, err := c.PostCommentService.Create(ctx.Context(), postComment)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.Success(ctx, comment)
}

// DeleteComment 删除评论
func (c *PostCommentController) DeleteComment(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	commentID := reqInfo.Get("comment_id").GetString()
	userID := c.GetUserID(ctx)
	err := c.PostCommentService.DeleteByCondition(ctx.Context(), map[string]interface{}{
		"id":      commentID,
		"user_id": userID,
	})
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	return c.Success(ctx, "success")
}
