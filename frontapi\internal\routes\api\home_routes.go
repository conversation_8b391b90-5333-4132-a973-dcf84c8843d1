package api

import (
	"frontapi/internal/api/home"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

func RegisterHomeRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 首页相关路由组
	homeController := home.NewHomeController(
		services.HomeService, services.VideoService,
		services.TagService, services.UserService,
		services.VideoCategoryService,
		services.VideoChannelService,
		services.VideoAlbumService)
	homeGroup := apiGroup.Group("/home")
	{
		homeGroup.Post("/getTagList", homeController.GetTags)
		//homeGroup.Post("/getTopCategories", homeController)
		homeGroup.Post("/getHotVideos", homeController.GetHotVideos)
		homeGroup.Post("/getRecommendedVideos", homeController.GetRecommendVideos)
		homeGroup.Post("/getRecommendStarList", homeController.GetRecommendStarList)
		homeGroup.Post("/getHotCreators", homeController.GetHotCreators)
		homeGroup.Post("/GetRecommendChannels", homeController.GetRecommendChannels) //推荐频道
		homeGroup.Post("/GetRecommendAlbums", homeController.GetRecommendAlbums)     //推荐视频专辑
	}

	// 分类路由3816873_10745.svg
	homeGroup.Post("/getTopCategoryNav", homeController.GetTopCategoryNav)
}
