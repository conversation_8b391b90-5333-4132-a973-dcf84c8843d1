package utils

import (
	"encoding/json"
)

// BaseRequest 精简版标准请求结构
type BaseRequest struct {
	UserID   string       `json:"userId,omitempty"`
	UserName string       `json:"userName,omitempty"`
	Token    string       `json:"token,omitempty"`
	Page     *PageRequest `json:"page,omitempty"`
	IsAdmin  bool         `json:"isAdmin,omitempty"`
	Data     interface{}  `json:"data"`
}

// NewBaseRequest 创建新的BaseRequest对象
func NewBaseRequest() *BaseRequest {
	return &BaseRequest{
		Page: &PageRequest{
			PageNo:   1,
			PageSize: 10,
		},
	}
}

// GetUserID 获取用户ID
func (r *BaseRequest) GetUserID() string {
	return r.UserID
}

// GetUserName 获取用户名
func (r *BaseRequest) GetUserName() string {
	return r.UserName
}

// GetPage 获取分页信息
func (r *BaseRequest) GetPage() *PageRequest {
	if r.Page == nil {
		r.Page = &PageRequest{
			PageNo:   1,
			PageSize: 10,
		}
	}
	return r.Page
}

// GetIsAdmin 获取是否为管理员
func (r *BaseRequest) GetIsAdmin() bool {
	return r.IsAdmin
}

// GetData 获取数据
func (r *BaseRequest) GetData() interface{} {
	return r.Data
}

// SetUserID 设置用户ID
func (r *BaseRequest) SetUserID(userID string) {
	r.UserID = userID
}

// SetUserName 设置用户名
func (r *BaseRequest) SetUserName(userName string) {
	r.UserName = userName
}

// SetPage 设置分页信息
func (r *BaseRequest) SetPage(page *PageRequest) {
	r.Page = page
}

// SetIsAdmin 设置是否为管理员
func (r *BaseRequest) SetIsAdmin(isAdmin bool) {
	r.IsAdmin = isAdmin
}

// SetData 设置数据
func (r *BaseRequest) SetData(data interface{}) {
	r.Data = data
}

// ParseDataToStruct 将Data解析为特定结构体
func (r *BaseRequest) ParseDataToStruct(v interface{}) error {
	if r.Data == nil {
		return nil
	}

	jsonData, err := json.Marshal(r.Data)
	if err != nil {
		return err
	}

	return json.Unmarshal(jsonData, v)
}
