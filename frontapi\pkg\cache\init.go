package cache

import (
	"fmt"
	"log"
	"time"

	cacheConfig "frontapi/config/cache"
	"frontapi/pkg/cache/bigcache"
	"frontapi/pkg/cache/file"
	"frontapi/pkg/cache/local"
	"frontapi/pkg/cache/redis"
	"frontapi/pkg/cache/sharding"
	"frontapi/pkg/cache/types"
)

// 初始化本地缓存适配器
func InitLocalCache(config *cacheConfig.CacheConfig) (*local.Adapter, error) {
	size := config.Memory.Size
	if size <= 0 {
		size = 100 // 默认100MB
	}

	ttl := time.Duration(config.Memory.TTL) * time.Second
	if ttl <= 0 {
		ttl = 5 * time.Minute // 默认5分钟
	}

	// 创建本地缓存配置
	localConfig := &local.AdapterConfig{
		Name:                    "local",
		Prefix:                  config.Memory.Prefix,
		DefaultTTL:              ttl,
		MaxCost:                 int64(size * 1024 * 1024), // 转换为字节
		BufferItems:             64,                        // 默认值
		NumCounters:             1000000,                   // 默认值
		ShardCount:              256,                       // 默认值
		CleanupInterval:         10 * time.Minute,          // 默认值
		EnableBackgroundCleanup: true,
	}

	// 创建本地缓存适配器
	return local.NewAdapter(localConfig)
}

// 初始化Redis适配器
func InitRedisAdapter(config *cacheConfig.CacheConfig) (*redis.Adapter, error) {
	redisConfig := &cacheConfig.RedisConfig{
		Host:         config.Redis.Host,
		Port:         config.Redis.Port,
		Password:     config.Redis.Password,
		DB:           config.Redis.DB,
		Prefix:       config.Redis.Prefix,
		Name:         "redis",
		DefaultTTL:   config.DefaultTTL,
		PoolSize:     config.Redis.PoolSize,
		MinIdleConns: config.Redis.MinIdleConns,
		MaxRetries:   config.Redis.MaxRetries,
		DialTimeout:  config.Redis.DialTimeout,
		ReadTimeout:  config.Redis.ReadTimeout,
		WriteTimeout: config.Redis.WriteTimeout,
		Cluster:      config.Redis.Cluster,
		Addrs:        config.Redis.Addrs,
	}

	return redis.NewAdapter(redisConfig)
}

// 初始化文件缓存适配器
func InitFileAdapter(config *cacheConfig.CacheConfig) (*file.Adapter, error) {
	fileConfig := &file.Config{
		BasePath:          config.File.Path,
		DefaultTTL:        config.Memory.TTL,
		CleanupInterval:   config.File.CleanupInterval,
		FileMode:          0644,
		DirMode:           0755,
		EnableCompression: config.File.EnableCompression,
	}

	return file.NewAdapter(fileConfig)
}

// 初始化BigCache适配器
func InitBigCacheAdapter(config *cacheConfig.CacheConfig) (*bigcache.Adapter, error) {
	bigCacheConfig := &bigcache.Config{
		Shards:             config.BigCache.Shards,
		LifeWindow:         config.BigCache.LifeWindow,
		CleanWindow:        config.BigCache.CleanWindow,
		MaxEntriesInWindow: config.BigCache.MaxEntriesInWindow,
		MaxEntrySize:       config.BigCache.MaxEntrySize,
		HardMaxCacheSize:   config.BigCache.HardMaxCacheSize,
		Prefix:             config.BigCache.Prefix,
	}

	return bigcache.NewAdapter(bigCacheConfig)
}

// 初始化分片适配器
func InitShardAdapter(config *cacheConfig.CacheConfig, baseAdapter types.CacheAdapter) (*sharding.Adapter, error) {
	shardConfig := &sharding.Config{
		ShardCount:       config.ShardCount,
		ShardingStrategy: "hash",
		Prefix:           config.Sharding.Prefix,
		BaseAdapter:      baseAdapter,
	}

	return sharding.NewAdapter(shardConfig)
}

// InitCacheWithConfig 使用给定配置初始化缓存
func InitCacheWithConfig(config *cacheConfig.CacheConfig) error {
	if config == nil {
		config = &cacheConfig.CacheConfig{
			DefaultAdapter:   "memory",
			DefaultTTL:       time.Hour,
			EnableLocalCache: true,
			LocalCacheSize:   100, // 100MB
			LocalCacheTTL:    300, // 5分钟
			Memory: &cacheConfig.MemoryConfig{
				Size: 100,
				TTL:  300,
			},
		}
	}
	// 创建缓存管理器
	manager, err := NewManager(config)
	if err != nil {
		return fmt.Errorf("初始化缓存管理器失败: %w", err)
	}

	// 初始化本地缓存（如果启用）
	if config.EnableLocalCache && config.LocalCache != nil && config.LocalCache.Size > 0 {
		localAdapter, err := InitLocalCache(config)
		if err != nil {
			log.Printf("初始化本地缓存失败: %v", err)
		} else {
			manager.SetLocalCache(localAdapter)
		}
	}

	// 初始化Redis适配器（如果配置了）
	if config.Redis != nil && config.Redis.Host != "" {
		redisAdapter, err := InitRedisAdapter(config)
		if err != nil {
			log.Printf("初始化Redis缓存失败: %v", err)
		} else {
			if err := manager.AddAdapter("redis", redisAdapter); err != nil {
				log.Printf("添加Redis适配器失败: %v", err)
			}
		}
	}

	// 初始化文件缓存适配器（如果配置了）
	if config.File != nil && config.File.Path != "" {
		fileAdapter, err := InitFileAdapter(config)
		if err != nil {
			log.Printf("初始化文件缓存失败: %v", err)
		} else {
			if err := manager.AddAdapter("file", fileAdapter); err != nil {
				log.Printf("添加文件适配器失败: %v", err)
			}
		}
	}

	// 初始化BigCache适配器（如果配置了）
	if config.BigCache != nil && config.BigCache.LifeWindow > 0 && config.BigCache.HardMaxCacheSize > 0 {
		bigCacheAdapter, err := InitBigCacheAdapter(config)
		if err != nil {
			log.Printf("初始化BigCache失败: %v", err)
		} else {
			if err := manager.AddAdapter("bigcache", bigCacheAdapter); err != nil {
				log.Printf("添加BigCache适配器失败: %v", err)
			}
		}
	}

	// 启用分片
	if config.EnableSharding && config.Sharding != nil && config.Sharding.ShardCount > 0 {
		// 获取默认适配器，并在其基础上创建分片适配器
		baseAdapter, err := manager.GetDefaultAdapter()
		if err == nil {

			shardAdapter, err := InitShardAdapter(config, baseAdapter)
			if err != nil {
				log.Printf("初始化分片缓存失败: %v", err)
			} else {
				if err := manager.AddAdapter("shard", shardAdapter); err != nil {
					log.Printf("添加分片适配器失败: %v", err)
				}
				// 将分片适配器设为默认
				manager.SetDefaultAdapter("shard")
			}
		}
	}

	// 设置全局管理器
	SetGlobalManager(manager)

	return nil
}

// GetAdapter 获取指定适配器
func GetAdapter(adapterType string) types.CacheAdapter {
	manager := GetGlobalManager()
	if manager == nil {
		panic(types.ErrAdapterNotInitialized)
	}

	adapter, err := manager.GetAdapter(adapterType)
	if err != nil {
		panic(types.ErrAdapterNotFound)
	}
	return adapter
}
