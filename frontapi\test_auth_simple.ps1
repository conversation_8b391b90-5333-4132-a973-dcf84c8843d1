# Auth flow test
Write-Host "Starting auth test..." -ForegroundColor Green

# Test login
Write-Host "Testing login..." -ForegroundColor Yellow
$loginData = @{
    data = @{
        username = "admin"
        password = "admin123"
        rememberMe = $true
    }
} | ConvertTo-Json -Depth 3

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/proadm/auth/login" `
        -Method POST `
        -ContentType "application/json" `
        -Body $loginData `
        -TimeoutSec 10

    Write-Host "Login success!" -ForegroundColor Green
    Write-Host "Login response:" $loginResponse.data -ForegroundColor Cyan
    
    $accessToken = $loginResponse.data.accessToken
    if ($accessToken) {
        Write-Host "AccessToken:" $accessToken -ForegroundColor Cyan
        
        # Test getUserInfo
        Write-Host "Testing getUserInfo..." -ForegroundColor Yellow
        
        $headers = @{
            "Authorization" = "Bearer $accessToken"
            "Content-Type" = "application/json"
        }
        
        $userInfoData = @{
            data = @{}
        } | ConvertTo-Json -Depth 3
        
        try {
            $userInfoResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/proadm/auth/getUserInfo" `
                -Method POST `
                -Headers $headers `
                -Body $userInfoData `
                -TimeoutSec 10

            Write-Host "getUserInfo success!" -ForegroundColor Green
            Write-Host "UserInfo response:" $userInfoResponse.data -ForegroundColor Cyan
            
            Write-Host "Auth test completed successfully!" -ForegroundColor Green
        }
        catch {
            $statusCode = $_.Exception.Response.StatusCode.value__
            Write-Host "getUserInfo failed! Status:" $statusCode -ForegroundColor Red
        }
    } else {
        Write-Host "No accessToken found in login response" -ForegroundColor Red
    }
}
catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "Login failed! Status:" $statusCode -ForegroundColor Red
}

Write-Host "Test completed." -ForegroundColor Green 