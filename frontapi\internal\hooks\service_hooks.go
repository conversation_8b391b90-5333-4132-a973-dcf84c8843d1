package hooks

import (
	"context"
	"fmt"
	"reflect"

	"frontapi/internal/hooks/common"
	"gorm.io/gorm"
)

// ServiceHookManager 服务层钩子管理器
type ServiceHookManager struct {
	manager    *LayeredHookManager
	db         *gorm.DB
	entityType string
}

// NewServiceHookManager 创建服务层钩子管理器
// 参数:
//   - db: 数据库连接实例
//   - entityType: 实体类型标识
// 返回:
//   - *ServiceHookManager: 服务层钩子管理器实例
func NewServiceHookManager(db *gorm.DB, entityType string) *ServiceHookManager {
	return &ServiceHookManager{
		manager:    GlobalHookManager,
		db:         db,
		entityType: entityType,
	}
}

// RegisterDuplicateCheck 注册重复检查钩子
func (s *ServiceHookManager) RegisterDuplicateCheck(tableName string, fields []string, message string) {
	hook := Hook{
		Name:        fmt.Sprintf("%s_duplicate_check", s.entityType),
		Description: fmt.Sprintf("检查%s的重复数据", s.entityType),
		Priority:    10, // 高优先级
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			dupHook := &common.DuplicateCheckHook{
				DB:        s.db,
				TableName: tableName,
				Fields:    fields,
				Message:   message,
			}
			return dupHook.Execute(ctx, data)
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeCreate, hook)
	s.manager.RegisterHook(s.entityType, BeforeUpdate, hook)
}

// RegisterDataCleaning 注册数据清洗钩子
func (s *ServiceHookManager) RegisterDataCleaning(trimFields, lowerFields, upperFields []string, defaultValues map[string]interface{}) {
	hook := Hook{
		Name:        fmt.Sprintf("%s_data_cleaning", s.entityType),
		Description: fmt.Sprintf("清洗%s的数据", s.entityType),
		Priority:    5, // 最高优先级
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			cleanHook := &common.DataCleaningHook{
				TrimFields:    trimFields,
				LowerFields:   lowerFields,
				UpperFields:   upperFields,
				DefaultValues: defaultValues,
			}
			return cleanHook.Execute(ctx, data)
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeCreate, hook)
	s.manager.RegisterHook(s.entityType, BeforeUpdate, hook)
}

// RegisterValidation 注册数据验证钩子
func (s *ServiceHookManager) RegisterValidation(rules map[string][]common.ValidationRule) {
	hook := Hook{
		Name:        fmt.Sprintf("%s_validation", s.entityType),
		Description: fmt.Sprintf("验证%s的数据", s.entityType),
		Priority:    15, // 中等优先级
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			valHook := &common.ValidationHook{
				Rules: rules,
			}
			return valHook.Execute(ctx, data)
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeCreate, hook)
	s.manager.RegisterHook(s.entityType, BeforeUpdate, hook)
}

// RegisterAudit 注册审计钩子
func (s *ServiceHookManager) RegisterAudit(tableName, userID string) {
	// 创建后审计
	createHook := Hook{
		Name:        fmt.Sprintf("%s_audit_create", s.entityType),
		Description: fmt.Sprintf("审计%s的创建操作", s.entityType),
		Priority:    100, // 低优先级
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			auditHook := &common.AuditHook{
				DB:        s.db,
				TableName: tableName,
				UserID:    userID,
				Action:    "CREATE",
			}
			return auditHook.Execute(ctx, data)
		},
	}
	s.manager.RegisterHook(s.entityType, AfterCreate, createHook)

	// 更新后审计
	updateHook := Hook{
		Name:        fmt.Sprintf("%s_audit_update", s.entityType),
		Description: fmt.Sprintf("审计%s的更新操作", s.entityType),
		Priority:    100, // 低优先级
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			auditHook := &common.AuditHook{
				DB:        s.db,
				TableName: tableName,
				UserID:    userID,
				Action:    "UPDATE",
			}
			return auditHook.Execute(ctx, data)
		},
	}
	s.manager.RegisterHook(s.entityType, AfterUpdate, updateHook)
}

// RegisterTimestamp 注册时间戳钩子
func (s *ServiceHookManager) RegisterTimestamp(createTimeField, updateTimeField string) {
	hook := Hook{
		Name:        fmt.Sprintf("%s_timestamp", s.entityType),
		Description: fmt.Sprintf("设置%s的时间戳", s.entityType),
		Priority:    8, // 高优先级
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			timestampHook := &common.TimestampHook{
				CreateTimeField: createTimeField,
				UpdateTimeField: updateTimeField,
			}
			return timestampHook.Execute(ctx, data)
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeCreate, hook)
	s.manager.RegisterHook(s.entityType, BeforeUpdate, hook)
}

// RegisterCustomHook 注册自定义钩子
func (s *ServiceHookManager) RegisterCustomHook(hookType HookType, name, description string, priority int, hookFunc HookFunc) {
	hook := Hook{
		Name:        name,
		Description: description,
		Priority:    priority,
		Enabled:     true,
		Func:        hookFunc,
	}
	s.manager.RegisterHook(s.entityType, hookType, hook)
}

// ExecuteHooks 执行钩子
func (s *ServiceHookManager) ExecuteHooks(ctx context.Context, hookType HookType, data interface{}) error {
	return s.manager.ExecuteHooks(ctx, s.entityType, hookType, data)
}

// HookableService 可钩子化的服务接口
type HookableService interface {
	GetHookManager() *ServiceHookManager
	SetupHooks() // 设置钩子
}

// BaseHookableService 基础可钩子化服务
type BaseHookableService struct {
	hookManager *ServiceHookManager
}

// GetHookManager 获取钩子管理器
func (s *BaseHookableService) GetHookManager() *ServiceHookManager {
	return s.hookManager
}

// InitHooks 初始化钩子管理器
func (s *BaseHookableService) InitHooks(db *gorm.DB, entityType string) {
	s.hookManager = NewServiceHookManager(db, entityType)
}

// CreateHookBuilder 创建钩子构建器
type CreateHookBuilder struct {
	manager *ServiceHookManager
}

// NewCreateHookBuilder 创建新的钩子构建器
func NewCreateHookBuilder(manager *ServiceHookManager) *CreateHookBuilder {
	return &CreateHookBuilder{manager: manager}
}

// WithDuplicateCheck 添加重复检查
func (b *CreateHookBuilder) WithDuplicateCheck(tableName string, fields []string, message string) *CreateHookBuilder {
	b.manager.RegisterDuplicateCheck(tableName, fields, message)
	return b
}

// WithDataCleaning 添加数据清洗
func (b *CreateHookBuilder) WithDataCleaning(trimFields, lowerFields, upperFields []string, defaultValues map[string]interface{}) *CreateHookBuilder {
	b.manager.RegisterDataCleaning(trimFields, lowerFields, upperFields, defaultValues)
	return b
}

// WithValidation 添加数据验证
func (b *CreateHookBuilder) WithValidation(rules map[string][]common.ValidationRule) *CreateHookBuilder {
	b.manager.RegisterValidation(rules)
	return b
}

// WithAudit 添加审计
func (b *CreateHookBuilder) WithAudit(tableName, userID string) *CreateHookBuilder {
	b.manager.RegisterAudit(tableName, userID)
	return b
}

// WithTimestamp 添加时间戳
func (b *CreateHookBuilder) WithTimestamp(createTimeField, updateTimeField string) *CreateHookBuilder {
	b.manager.RegisterTimestamp(createTimeField, updateTimeField)
	return b
}

// WithCustomHook 添加自定义钩子
func (b *CreateHookBuilder) WithCustomHook(hookType HookType, name, description string, priority int, hookFunc HookFunc) *CreateHookBuilder {
	b.manager.RegisterCustomHook(hookType, name, description, priority, hookFunc)
	return b
}

// Build 构建完成
func (b *CreateHookBuilder) Build() *ServiceHookManager {
	return b.manager
}

// RegisterAuditHook 注册审计钩子（兼容方法）
func (s *ServiceHookManager) RegisterAuditHook(userID string) {
	s.RegisterAudit(s.entityType, userID)
}

// RegisterTimestampHook 注册时间戳钩子（兼容方法）
func (s *ServiceHookManager) RegisterTimestampHook(createField, updateField string) {
	s.RegisterTimestamp(createField, updateField)
}

// RegisterValidationHook 注册验证钩子（兼容方法）
func (s *ServiceHookManager) RegisterValidationHook(rules map[string]interface{}) {
	// 转换规则格式
	validationRules := make(map[string][]common.ValidationRule)
	for field, rule := range rules {
		if ruleSlice, ok := rule.([]common.ValidationRule); ok {
			validationRules[field] = ruleSlice
		}
	}
	s.RegisterValidation(validationRules)
}

// RegisterBatchCreateHooks 注册批量创建钩子
func (s *ServiceHookManager) RegisterBatchCreateHooks() {
	hook := Hook{
		Name:        fmt.Sprintf("%s_batch_create", s.entityType),
		Description: fmt.Sprintf("批量创建%s的钩子", s.entityType),
		Priority:    50,
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			// 批量创建的特殊处理逻辑
			return nil
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeBatchCreate, hook)
	s.manager.RegisterHook(s.entityType, AfterBatchCreate, hook)
}

// RegisterBatchUpdateHooks 注册批量更新钩子
func (s *ServiceHookManager) RegisterBatchUpdateHooks() {
	hook := Hook{
		Name:        fmt.Sprintf("%s_batch_update", s.entityType),
		Description: fmt.Sprintf("批量更新%s的钩子", s.entityType),
		Priority:    50,
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			// 批量更新的特殊处理逻辑
			return nil
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeBatchUpdate, hook)
	s.manager.RegisterHook(s.entityType, AfterBatchUpdate, hook)
}

// RegisterBatchDeleteHooks 注册批量删除钩子
func (s *ServiceHookManager) RegisterBatchDeleteHooks() {
	hook := Hook{
		Name:        fmt.Sprintf("%s_batch_delete", s.entityType),
		Description: fmt.Sprintf("批量删除%s的钩子", s.entityType),
		Priority:    50,
		Enabled:     true,
		Func: func(ctx context.Context, data interface{}) error {
			// 批量删除的特殊处理逻辑
			return nil
		},
	}
	s.manager.RegisterHook(s.entityType, BeforeBatchDelete, hook)
	s.manager.RegisterHook(s.entityType, AfterBatchDelete, hook)
}

// GetEntityType 获取实体类型名称
func GetEntityType(entity interface{}) string {
	t := reflect.TypeOf(entity)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.Name()
}