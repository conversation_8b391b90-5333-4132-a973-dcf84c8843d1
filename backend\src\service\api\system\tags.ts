import type {
    CreateTagRequest,
    TagParams,
    UpdateTagRequest
} from "@/types/tags";
import { request } from "../../request";

/**
 * 获取标签列表
 * @param params 查询参数
 */
export function getTagList(params: TagParams) {
    return request({
        url: '/tags/list',
        method: 'post',
        data: params
    })
}

/**
 * 获取标签详情
 * @param id 标签ID
 */
export function getTagDetail(id: string) {
    return request({
        url: `/tags/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 创建标签
 * @param data 标签数据
 */
export function createTag(params: { data: CreateTagRequest }) {
    return request({
        url: '/tags/add',
        method: 'post',
        data: params
    })
}

/**
 * 更新标签
 * @param data 标签数据
 */
export function updateTag(params: { data: UpdateTagRequest }) {
    return request({
        url: '/tags/update',
        method: 'post',
        data: params
    })
}

/**
 * 删除标签
 * @param id 标签ID
 */
export function deleteTag(id: string) {
    return request({
        url: `/tags/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 搜索标签 - 用于下拉选择器
 * @param params 查询参数
 */
export function searchTags(params: any) {
    return request({
        url: '/tags/list',
        method: 'post',
        data: params
    })
} 