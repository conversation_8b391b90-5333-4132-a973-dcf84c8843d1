import { request } from '../../request';
import { ApiResponse, ApiRequest } from '@/types/https';
import { Menu, MenuParams, CreateMenuRequest, UpdateMenuRequest } from '@/types/menus';

/**
 * 获取菜单列表
 */
export function getMenuList(params?: MenuParams) {
  return request<ApiResponse<{ list: Menu[], total: number }>>({
    url: '/permission/menus/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取菜单树形结构
 */
export function getMenuTree() {
  return request<ApiResponse<Menu[]>>({
    url: '/permission/menus/tree',
    method: 'post'
  });
}

/**
 * 获取菜单详情
 */
export function getMenuDetail(id: number) {
  return request<ApiResponse<Menu>>({
    url: `/permission/menus/detail/${id}`,
    method: 'post',
    data: { data: { id } }
  });
}

/**
 * 创建菜单
 */
export function createMenu(data: CreateMenuRequest) {
  return request<ApiResponse<any>>({
    url: '/permission/menus/add',
    method: 'post',
    data: { data }
  });
}

/**
 * 更新菜单
 */
export function updateMenu(data: UpdateMenuRequest) {
  return request<ApiResponse<any>>({
    url: '/permission/menus/update',
    method: 'post',
    data: { data }
  });
}

/**
 * 删除菜单
 */
export function deleteMenu(id: number) {
  return request<ApiResponse<any>>({
    url: `/permission/menus/delete/${id}`,
    method: 'post',
    data: { data: { id } }
  });
}

/**
 * 更新菜单状态
 */
export function updateMenuStatus(id: number, status: number) {
  return request<ApiResponse<any>>({
    url: '/permission/menus/update-status',
    method: 'post',
    data: { data: { id, status } }
  });
} 