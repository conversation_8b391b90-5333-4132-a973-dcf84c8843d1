<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>视频分类管理</h2>
            <div class="buttons">
              <el-button type="primary" :icon="Plus" @click="handleAdd">添加分类</el-button>
              <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
              <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 使用分类搜索组件 -->
      <CategorySearchBar
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
        @refresh="refreshList"
        class="mb-4"
      />

      <!-- 使用分类表格组件 -->
      <CategoryTable
        :category-list="categoryList"
        :loading="loading"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @batch-status="handleBatchStatus"
        @batch-delete="handleBatchDelete"
      />

      <!-- 对话框组件 -->
      <CategoryFormDialog
        :visible="dialogVisible"
        :type="dialogType"
        :category-data="currentCategory"
        @update:visible="(val: boolean) => dialogVisible = val"
        @success="handleDialogSuccess"
      />

      <CategoryDetailDialog
        :visible="detailDialogVisible"
        :category-data="currentCategory"
        @update:visible="(val: boolean) => detailDialogVisible = val"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { batchDeleteVideoCategory, batchUpdateVideoCategoryStatus, deleteVideoCategory, getVideoCategoryList, updateVideoCategoryStatus } from '@/service/api/videos/videos';
import type { VideoCategoryItem } from '@/types/videos';
import { handleApiError } from '@/utils/errorHandler';
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入分类管理组件
import CategoryDetailDialog from './components/CategoryDetailDialog.vue';
import CategoryFormDialog from './components/CategoryFormDialog.vue';
import CategorySearchBar from './components/CategorySearchBar.vue';
import CategoryTable from './components/CategoryTable.vue';

// 响应式数据
const loading = ref(false);
const categoryList = ref<VideoCategoryItem[]>([]);
const selectedRows = ref<VideoCategoryItem[]>([]);
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const currentCategory = ref<VideoCategoryItem | null>(null);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as number | undefined,
  parent_id: undefined as string | undefined,
  is_featured: undefined as number | undefined,
  start_date: '',
  end_date: ''
});

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 获取分类列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      data: {
        keyword: searchForm.keyword,
        status: searchForm.status,
        parent_id: searchForm.parent_id,
        is_featured: searchForm.is_featured,
        created_at_start: searchForm.start_date,
        created_at_end: searchForm.end_date
      },
      page: {
        pageNo: pagination.page,
        pageSize: pagination.pageSize
      }
    };

    const {response, data} = await getVideoCategoryList(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      categoryList.value = data.list || [];
      pagination.total = data.total || 0;
    } else {
      handleApiError(response.message || '获取分类列表失败');
    }
  } catch (error) {
    handleApiError(error, '获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (searchParams?: any) => {
  if (searchParams) {
    Object.assign(searchForm, searchParams);
  }
  pagination.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    parent_id: undefined,
    is_featured: undefined,
    start_date: '',
    end_date: ''
  });
  pagination.page = 1;
  getList();
};

// 刷新列表
const refreshList = () => {
  getList();
};

// 表格选择变化
const handleSelectionChange = (selection: VideoCategoryItem[]) => {
  selectedRows.value = selection;
};

// 分页处理
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getList();
};

// 添加分类
const handleAdd = () => {
  dialogType.value = 'add';
  currentCategory.value = null;
  dialogVisible.value = true;
};

// 查看分类
const handleView = (row: VideoCategoryItem) => {
  currentCategory.value = row;
  detailDialogVisible.value = true;
};

// 编辑分类
const handleEdit = (row: VideoCategoryItem) => {
  dialogType.value = 'edit';
  currentCategory.value = { ...row };
  dialogVisible.value = true;
};

// 删除分类
const handleDelete = async (row: VideoCategoryItem) => {
  try {
    const {response} = await deleteVideoCategory(row.id) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('删除分类成功');
      getList();
    } else {
      handleApiError(response.message || '删除分类失败');
    }
  } catch (error) {
    handleApiError(error, '删除分类失败');
  }
};

// 更改分类状态
const handleChangeStatus = async (id: string, status: number) => {
  try {
    const params = { id, status };
    const {response, data} = await updateVideoCategoryStatus(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}分类成功`);
      getList();
    } else {
      handleApiError(response.message || '更新分类状态失败');
    }
  } catch (error) {
    handleApiError(error, '更新分类状态失败');
  }
};

// 导出分类数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

// 批量更新分类状态
const handleBatchStatus = async (status: number) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要操作的分类');
    return;
  }
  
  try {
    const params = { ids: selectedRows.value.map(category => category.id), status };
    const {response, data} = await batchUpdateVideoCategoryStatus(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}分类成功`);
      selectedRows.value = [];
      getList();
    } else {
      handleApiError(response.message || '更新分类状态失败');
    }
  } catch (error) {
    handleApiError(error, '更新分类状态失败');
  }
};

// 批量删除分类
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的分类');
    return;
  }
  
  try {
    const params = { ids: selectedRows.value.map(category => category.id) };
    const {response, data} = await batchDeleteVideoCategory(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('批量删除分类成功');
      selectedRows.value = [];
      getList();  
    } else {
      handleApiError(response.message || '批量删除分类失败');
    }
  } catch (error) {
    handleApiError(error, '批量删除分类失败');
  }
};

// 对话框操作成功
const handleDialogSuccess = () => {
  getList();
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.title-container h2 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .title-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    justify-content: center;
  }
}
</style>