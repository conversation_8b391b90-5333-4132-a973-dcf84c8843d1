package admin

import (
	"frontapi/internal/admin/permission"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterPermissionRoutes 注册权限相关路由
func RegisterPermissionRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建RBAC权限控制器
	// 用户管理路由组
	userGroup := apiGroup.Group("/permission/users")
	userGroup.Use(middleware.OptionalAuth())
	{
		userController := permission.NewAdminUserController()
		// 用户管理路由
		userGroup.Post("/list", middleware.AuthRequired(), userController.ListUsers)
		userGroup.Post("/add", userController.CreateUser)
		userGroup.Post("/update/:id", userController.UpdateUser)
		userGroup.Post("/delete/:id", userController.DeleteUser)
		userGroup.Post("/detail/:id", userController.GetUser)
		userGroup.Post("/assign-roles/:id", userController.UpdateUserRoles)
		userGroup.Post("/reset-password/:id", userController.ResetPassword)
		userGroup.Post("/update-password/:id", userController.UpdatePassword)

		// 获取当前用户权限码
		app.Post("/api/proadm/permission/get-user-codes", userController.GetUserCodes)
		// 获取用户动态路由 - 用于 Pure Admin 前端框架
		app.Get("/api/proadm/get-async-routes", middleware.AuthRequired(), userController.GetAsyncRoutes)

	}

	// 角色管理路由组
	roleGroup := apiGroup.Group("/permission/roles")
	roleGroup.Use(middleware.OptionalAuth())
	{
		roleController := permission.NewRoleController()
		// 角色管理路由
		roleGroup.Post("/list", roleController.ListRoles)
		roleGroup.Post("/add", roleController.CreateRole)
		roleGroup.Post("/update/:id", roleController.UpdateRole)
		roleGroup.Post("/delete/:id", roleController.DeleteRole)
		roleGroup.Post("/detail/:id", roleController.GetRole)
		roleGroup.Post("/assign-menus/:id", roleController.UpdateRoleMenus)
		roleGroup.Post("/assign-permissions/:id", roleController.UpdateRolePermissions)
	}

	// 权限管理路由组
	permGroup := apiGroup.Group("/permission/permissions")
	permGroup.Use(middleware.OptionalAuth())
	{
		permController := permission.NewPermissionController()
		// 权限管理路由
		permGroup.Post("/list", middleware.AuthRequired(), permController.ListPermissions)
		permGroup.Post("/add", middleware.AuthRequired(), permController.CreatePermission)
		permGroup.Post("/update/:id", middleware.AuthRequired(), permController.UpdatePermission)
		permGroup.Post("/delete/:id", middleware.AuthRequired(), permController.DeletePermission)
		permGroup.Post("/detail/:id", middleware.AuthRequired(), permController.GetPermission)

	}

	menuGroup := apiGroup.Group("/permission/menus")
	{
		menuController := permission.NewMenuController(services.AdminMenuService)
		// 添加认证中间件
		menuGroup.Use(middleware.OptionalAuth())
		// 注册菜单路由
		menuGroup.Post("/list", menuController.GetMenuList)
		menuGroup.Post("/add", menuController.CreateMenu)
		menuGroup.Post("/update/:id", menuController.UpdateMenu)
		menuGroup.Post("/delete/:id", menuController.DeleteMenu)
		menuGroup.Post("/detail/:id", menuController.GetMenuDetail)
		menuGroup.Post("/name-exists", menuController.CheckMenuNameExists)
		menuGroup.Post("/path-exists", menuController.CheckMenuPathExists)
	}
	deptGroup := apiGroup.Group("/system/dept")
	{
		deptController := permission.NewDeptController()
		// 添加认证中间件
		deptGroup.Use(middleware.OptionalAuth())

		// 注册部门路由
		deptGroup.Get("/", deptController.GetDeptList)
		deptGroup.Post("/add", deptController.CreateDept)
		deptGroup.Post("/update/:id", deptController.UpdateDept)
		deptGroup.Post("/delete/:id", deptController.DeleteDept)
		deptGroup.Get("/:id", deptController.GetDeptInfo)
	}

}
