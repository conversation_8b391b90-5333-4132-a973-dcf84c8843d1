package comics

import (
	"frontapi/internal/models"
)

// ComicComment 漫画评论模型
type ComicComment struct {
	models.BaseModelStruct
	ComicID     string `json:"comic_id" gorm:"column:comic_id;not null;index:idx_comic_id" comment:"漫画ID"`
	UserID      string `json:"user_id" gorm:"column:user_id;not null;index:idx_user_id" comment:"用户ID"`
	Content     string `json:"content" gorm:"column:content;not null;type:text" comment:"评论内容"`
	ParentID    string `json:"parent_id" gorm:"column:parent_id;index:idx_parent_id" comment:"父评论ID，用于楼中楼回复"`
	ReplyID     string `json:"reply_id" gorm:"column:reply_id" comment:"回复的评论ID"`
	ReplyUserID string `json:"reply_user_id" gorm:"column:reply_user_id" comment:"回复的用户ID"`
	LikeCount   int    `json:"like_count" gorm:"column:like_count;default:0" comment:"点赞数"`
	Status      int8   `json:"status" gorm:"column:status;default:1" comment:"状态：1-正常，0-删除"`
}

// TableName 指定表名
func (ComicComment) TableName() string {
	return "ly_comic_comments"
}
