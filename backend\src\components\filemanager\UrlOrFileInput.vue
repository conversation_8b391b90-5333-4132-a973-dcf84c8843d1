<template>
  <div class="url-file-input">
    <el-input
      v-model="inputValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :size="size"
      @change="handleChange"
    >
      <template #prefix>
         <!-- 是图片就显示图片 -->
         <el-image  v-if="fileType === 'image'||fileType === 'static'||fileType === 'pictures'" :src="inputValue" alt="" style="width: 20px; height: 20px;">
            <template #error>
              <el-icon><InfoFilled /></el-icon>
            </template>
         </el-image>
         <el-icon v-else-if="fileType === 'video'||fileType === 'videos'">
          <Film />
         </el-icon>
         <el-icon v-else>
          <link />
         </el-icon>
      </template>
      <template #append>
        <el-button-group class="input-actions">
          <el-button @click="openFileManager">
            <el-icon><folder /></el-icon>
          </el-button>
          <el-button v-if="showUpload" @click="openUpload">
            <el-icon><upload /></el-icon>
          </el-button>
          <el-button v-if="showPreview && hasValue" @click="openPreview">
            <el-icon><View /></el-icon>
          </el-button>
        </el-button-group>
      </template>
    </el-input>

    <!-- 文件管理器弹窗 -->
    <el-dialog
      v-model="fileManagerVisible"
      title="选择文件" style="padding: 0;"
      width="80%"
      top="5vh"
      class="file-manager-dialog url-file-input-dialog"
      destroy-on-close
      :before-close="closeFileManager"
      :append-to-body="true"
    >
      <div style="height: 70vh;">
        <file-manager
          mode="selector"
          :initial-file-type="fileType"
          :multiple="false"
          @select="handleFileSelected"
          @cancel="closeFileManager"
        />
      </div>
    </el-dialog>

    <!-- 上传对话框 -->
    <upload-dialog
      v-model="uploadDialogVisible"
      :file-type="fileType"
      :current-path="subDir" @uploaded="handleUploadSuccess"
      @upload-success="handleUploadSuccess"
    />

    <!-- 预览对话框 -->
    <preview-dialog
      v-model="previewDialogVisible"
      :file="getPreviewFile()"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Link, Folder, Upload, View, Film,InfoFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import FileManager from './FileManager.vue';
import { UploadDialog, PreviewDialog } from './components';

interface FileItem {
  name: string;
  path: string;
  url: string;
  size: number;
  type: string;
  is_dir: boolean;
}

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入URL或选择文件'
  },
  fileType: {
    type: String,
    default: 'pictures'
  },
  subDir: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  showPreview: {
    type: Boolean,
    default: true
  },
  showUpload: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change']);

// Internal state
const inputValue = ref(props.modelValue);
const fileManagerVisible = ref(false);
const uploadDialogVisible = ref(false);
const previewDialogVisible = ref(false);

// Computed properties
const hasValue = computed(() => {
  return !!inputValue.value;
});

// Watch for changes in modelValue prop
watch(() => props.modelValue, (newVal) => {
  inputValue.value = newVal;
});

// Watch for changes in inputValue
watch(() => inputValue.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// Methods
const handleChange = (value: string) => {
  emit('change', value);
};

const openFileManager = () => {
  fileManagerVisible.value = true;
};

const closeFileManager = () => {
  fileManagerVisible.value = false;
};

const openUpload = () => {
  uploadDialogVisible.value = true;
};

const openPreview = () => {
  if (inputValue.value) {
    previewDialogVisible.value = true;
  }
};

const handleFileSelected = (files: FileItem[]) => {
  if (files.length > 0) {
    // 确保选择的是文件而不是目录
    const selectedFile = files[0];
    if (selectedFile.is_dir) {
      ElMessage.warning('请选择文件而不是文件夹');
      return;
    }

    // 验证文件类型是否匹配预期
    const isImageFile = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(
      selectedFile.name.split('.').pop()?.toLowerCase() || ''
    );

    const isVideoFile = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v'].includes(
      selectedFile.name.split('.').pop()?.toLowerCase() || ''
    );

    // 图片类型验证
    if (
      ['image', 'static', 'pictures', 'images'].includes(props.fileType) &&
      !isImageFile
    ) {
      ElMessage.warning('请选择图片文件（jpg、jpeg、png、gif、webp等）');
      return;
    }

    // 视频类型验证
    if (
      ['video', 'videos'].includes(props.fileType) &&
      !isVideoFile
    ) {
      ElMessage.warning('请选择视频文件（mp4、avi、mov、mkv等）');
      return;
    }

    inputValue.value = selectedFile.url;
    emit('update:modelValue', selectedFile.url);
    emit('change', selectedFile.url);
    closeFileManager();
  }
};

const handleUploadSuccess = (fileData: any) => {
  console.log("上传文件：",fileData)
  if (fileData&&fileData.url) {
    inputValue.value = fileData.url;

    emit('change', fileData.url);
    ElMessage.success('文件上传成功');
  }
};

const getPreviewFile = (): FileItem | null => {
  if (!inputValue.value) return null;

  return {
    name: inputValue.value.split('/').pop() || 'file',
    path: '',
    url: inputValue.value,
    size: 0,
    type: props.fileType,
    is_dir: false
  };
};
</script>

<style scoped lang="scss">
.el-dialog__body {
  padding: 0 !important;
}
.url-file-input {
  width: 100%;

  .input-actions {
    display: flex;
  }
}
:deep(.file-manager-dialog){
  .el-dialog__header{
    padding-top: 6px;
    padding-bottom: 6px;
  }
  padding:0;
  .el-dialog__body{
    margin:0;
    padding:0 !important;
  }
  .el-dialog__footer{
    padding: 6px 0;
  }

}


</style>
