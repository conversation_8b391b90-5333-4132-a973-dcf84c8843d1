package permission

import (
	"strconv"

	"frontapi/internal/admin"
	permissionService "frontapi/internal/service/permission"
	adminTypes "frontapi/internal/typings/admin"
	validationPerm "frontapi/internal/validation/permission"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// MenuController 菜单控制器
type MenuController struct {
	admin.BaseController
	menuService permissionService.AdminMenuService
}

// NewMenuController 创建菜单控制器实例
func NewMenuController(menuService permissionService.AdminMenuService) *MenuController {
	return &MenuController{
		menuService: menuService,
	}
}

// GetMenuList 获取菜单列表
func (c *MenuController) GetMenuList(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询条件
	condition := make(map[string]interface{})

	// 获取查询参数
	if keyword := reqInfo.Get("keyword"); !keyword.IsEmpty() {
		condition["title LIKE"] = "%" + keyword.GetString() + "%"
	}
	if typeVal := reqInfo.Get("type"); !typeVal.IsEmpty() {
		if t := typeVal.GetInt(); t > 0 {
			condition["type"] = int8(t)
		}
	}
	if statusVal := reqInfo.Get("status"); !statusVal.IsEmpty() {
		if s := statusVal.GetInt(); s >= 0 {
			condition["status"] = int8(s)
		}
	}
	if parentID := reqInfo.Get("parent_id"); !parentID.IsEmpty() {
		if p := parentID.GetInt(); p >= 0 {
			condition["parent_id"] = p
		}
	}

	// 获取菜单列表
	menus, total, err := c.menuService.List(ctx.Context(), condition, "sort ASC, id ASC", page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取菜单列表失败: "+err.Error())
	}

	// 转换为响应格式
	menuList := make([]*adminTypes.MenuListResponse, 0, len(menus))
	for _, menu := range menus {
		menuList = append(menuList, adminTypes.ConvertToMenuListResponse(menu))
	}

	// 返回列表数据
	return c.SuccessList(ctx, menuList, total, page, pageSize)
}

// GetMenuTree 获取菜单树
func (c *MenuController) GetMenuTree(ctx *fiber.Ctx) error {
	// 获取查询条件
	reqInfo := c.GetRequestInfo(ctx)
	condition := make(map[string]interface{})

	// 默认只获取启用的菜单
	if statusVal := reqInfo.Get("status"); !statusVal.IsEmpty() {
		if s := statusVal.GetInt(); s >= 0 {
			condition["status"] = int8(s)
		}
	} else {
		condition["status"] = int8(1) // 默认只获取启用的菜单
	}

	// 获取菜单树
	menuTree, err := c.menuService.GetMenuTree(ctx.Context(), condition)
	if err != nil {
		return c.InternalServerError(ctx, "获取菜单树失败: "+err.Error())
	}

	// 转换为响应格式
	treeList := adminTypes.ConvertMenuTreeToResponse(menuTree)

	return c.Success(ctx, treeList)
}

// GetMenuDetail 获取菜单详情
func (c *MenuController) GetMenuDetail(ctx *fiber.Ctx) error {
	// 获取菜单ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 转换为int类型
	menuID, err := strconv.Atoi(id)
	if err != nil {
		return c.BadRequest(ctx, "无效的菜单ID", nil)
	}

	// 获取菜单详情
	menu, err := c.menuService.GetByID(ctx.Context(), menuID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取菜单详情失败: "+err.Error())
	}

	if menu == nil {
		return c.NotFound(ctx, "菜单不存在")
	}

	// 转换为响应格式
	menuDetail := adminTypes.ConvertToMenuDetailResponse(menu)

	return c.Success(ctx, menuDetail)
}

// CreateMenu 创建菜单
func (c *MenuController) CreateMenu(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req validationPerm.CreateAdminMenuRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 创建菜单
	menuID, err := c.menuService.CreateMenu(ctx.Context(), &req)
	if err != nil {
		return c.InternalServerError(ctx, "创建菜单失败: "+err.Error())
	}

	// 返回创建结果
	result := fiber.Map{
		"id":      menuID,
		"message": "创建菜单成功",
	}

	return c.Success(ctx, result)
}

// UpdateMenu 更新菜单
func (c *MenuController) UpdateMenu(ctx *fiber.Ctx) error {
	// 获取菜单ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 转换为int类型
	menuID, err := strconv.Atoi(id)
	if err != nil {
		return c.BadRequest(ctx, "无效的菜单ID", nil)
	}

	// 解析请求参数
	var req validationPerm.UpdateAdminMenuRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 更新菜单
	err = c.menuService.UpdateMenu(ctx.Context(), menuID, &req)
	if err != nil {
		return c.InternalServerError(ctx, "更新菜单失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新菜单成功")
}

// DeleteMenu 删除菜单
func (c *MenuController) DeleteMenu(ctx *fiber.Ctx) error {
	// 获取菜单ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 转换为int类型
	menuID, err := strconv.Atoi(id)
	if err != nil {
		return c.BadRequest(ctx, "无效的菜单ID", nil)
	}

	// 检查是否存在子菜单
	children, err := c.menuService.GetMenusByParent(ctx.Context(), menuID)
	if err != nil {
		return c.InternalServerError(ctx, "检查子菜单失败: "+err.Error())
	}
	if len(children) > 0 {
		return c.BadRequest(ctx, "存在子菜单，无法删除", nil)
	}

	// 删除菜单
	err = c.menuService.Delete(ctx.Context(), menuID)
	if err != nil {
		return c.InternalServerError(ctx, "删除菜单失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除菜单成功")
}

// UpdateMenuStatus 更新菜单状态
func (c *MenuController) UpdateMenuStatus(ctx *fiber.Ctx) error {
	// 获取菜单ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 转换为int类型
	menuID, err := strconv.Atoi(id)
	if err != nil {
		return c.BadRequest(ctx, "无效的菜单ID", nil)
	}

	// 获取状态参数
	reqInfo := c.GetRequestInfo(ctx)
	status := int8(reqInfo.Get("status").GetInt())

	// 更新菜单状态
	err = c.menuService.UpdateStatus(ctx.Context(), menuID, int(status))
	if err != nil {
		return c.InternalServerError(ctx, "更新菜单状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新菜单状态成功")
}

// BatchUpdateMenuStatus 批量更新菜单状态
func (c *MenuController) BatchUpdateMenuStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req validationPerm.AdminMenuBatchUpdateRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量更新菜单状态
	err := c.menuService.BatchUpdateStatus(ctx.Context(), req.IDs, int(req.Status))
	if err != nil {
		return c.InternalServerError(ctx, "批量更新菜单状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量更新菜单状态成功")
}

// UpdateMenuSort 更新菜单排序
func (c *MenuController) UpdateMenuSort(ctx *fiber.Ctx) error {
	// 获取菜单ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 转换为int类型
	menuID, err := strconv.Atoi(id)
	if err != nil {
		return c.BadRequest(ctx, "无效的菜单ID", nil)
	}

	// 获取排序参数
	reqInfo := c.GetRequestInfo(ctx)
	sort := reqInfo.Get("sort").GetInt()

	// 获取菜单并更新排序
	menu, err := c.menuService.GetByID(ctx.Context(), menuID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取菜单失败: "+err.Error())
	}
	if menu == nil {
		return c.NotFound(ctx, "菜单不存在")
	}

	menu.Sort = sort
	err = c.menuService.Update(ctx.Context(), menu)
	if err != nil {
		return c.InternalServerError(ctx, "更新菜单排序失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新菜单排序成功")
}

// BatchUpdateMenuSort 批量更新菜单排序
func (c *MenuController) BatchUpdateMenuSort(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req validationPerm.AdminMenuBatchSortUpdateRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 转换为服务层需要的格式
	sortData := make([]map[string]interface{}, 0, len(req.SortData))
	for _, item := range req.SortData {
		sortData = append(sortData, map[string]interface{}{
			"id":   item.ID,
			"sort": item.Sort,
		})
	}

	// 批量更新菜单排序
	err := c.menuService.BatchUpdateMenuSort(ctx.Context(), sortData)
	if err != nil {
		return c.InternalServerError(ctx, "批量更新菜单排序失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量更新菜单排序成功")
}

// CheckMenuNameExists 检查菜单名称是否存在
func (c *MenuController) CheckMenuNameExists(ctx *fiber.Ctx) error {
	name := ctx.Query("name")
	if name == "" {
		return c.BadRequest(ctx, "菜单名称不能为空", nil)
	}

	// 获取排除的ID（用于编辑时检查）
	excludeID := -1
	if id := ctx.Query("exclude_id"); id != "" {
		if parsedID, err := strconv.Atoi(id); err == nil {
			excludeID = parsedID
		}
	}

	// 检查名称是否存在
	var exists bool
	var err error
	if excludeID > 0 {
		exists, err = c.menuService.CheckMenuNameExists(ctx.Context(), name, excludeID)
	} else {
		exists, err = c.menuService.CheckMenuNameExists(ctx.Context(), name)
	}

	if err != nil {
		return c.InternalServerError(ctx, "检查菜单名称失败: "+err.Error())
	}

	return c.Success(ctx, adminTypes.MenuCheckResponse{
		Exists: exists,
	})
}

// CheckMenuPathExists 检查菜单路径是否存在
func (c *MenuController) CheckMenuPathExists(ctx *fiber.Ctx) error {
	path := ctx.Query("path")
	if path == "" {
		return c.BadRequest(ctx, "菜单路径不能为空", nil)
	}

	// 获取排除的ID（用于编辑时检查）
	excludeID := -1
	if id := ctx.Query("exclude_id"); id != "" {
		if parsedID, err := strconv.Atoi(id); err == nil {
			excludeID = parsedID
		}
	}

	// 检查路径是否存在
	var exists bool
	var err error
	if excludeID > 0 {
		exists, err = c.menuService.CheckMenuPathExists(ctx.Context(), path, excludeID)
	} else {
		exists, err = c.menuService.CheckMenuPathExists(ctx.Context(), path)
	}

	if err != nil {
		return c.InternalServerError(ctx, "检查菜单路径失败: "+err.Error())
	}

	return c.Success(ctx, adminTypes.MenuCheckResponse{
		Exists: exists,
	})
}

// CheckMenuPermissionExists 检查权限标识是否存在
func (c *MenuController) CheckMenuPermissionExists(ctx *fiber.Ctx) error {
	permission := ctx.Query("permission")
	if permission == "" {
		return c.BadRequest(ctx, "权限标识不能为空", nil)
	}

	// 获取排除的ID（用于编辑时检查）
	excludeID := -1
	if id := ctx.Query("exclude_id"); id != "" {
		if parsedID, err := strconv.Atoi(id); err == nil {
			excludeID = parsedID
		}
	}

	// 检查权限标识是否存在
	var exists bool
	var err error
	if excludeID > 0 {
		exists, err = c.menuService.CheckMenuPermissionExists(ctx.Context(), permission, excludeID)
	} else {
		exists, err = c.menuService.CheckMenuPermissionExists(ctx.Context(), permission)
	}

	if err != nil {
		return c.InternalServerError(ctx, "检查权限标识失败: "+err.Error())
	}

	return c.Success(ctx, adminTypes.MenuCheckResponse{
		Exists: exists,
	})
}

// GetMenusByParent 获取指定父级的菜单
func (c *MenuController) GetMenusByParent(ctx *fiber.Ctx) error {
	parentIDStr := ctx.Query("parent_id", "0")
	parentID, err := strconv.Atoi(parentIDStr)
	if err != nil {
		return c.BadRequest(ctx, "无效的父级菜单ID", nil)
	}

	// 获取子菜单
	menus, err := c.menuService.GetMenusByParent(ctx.Context(), parentID)
	if err != nil {
		return c.InternalServerError(ctx, "获取子菜单失败: "+err.Error())
	}

	// 转换为响应格式
	menuList := make([]*adminTypes.MenuListResponse, 0, len(menus))
	for _, menu := range menus {
		menuList = append(menuList, adminTypes.ConvertToMenuListResponse(menu))
	}

	return c.Success(ctx, menuList)
}
