package home

// BaseListResponse 基础列表响应结构
type BaseListResponse struct {
	Total    int64 `json:"total"`
	PageNo   int   `json:"pageNo"`
	PageSize int   `json:"pageSize"`
}

// HomeTag 首页标签 - 前端响应类型
type HomeTag struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Icon   string `json:"icon"`
	Sort   int    `json:"sort"`
	Status int8   `json:"status"`
}

// HomeVideo 首页视频 - 前端响应类型
type HomeVideo struct {
	ID            string        `json:"id"`
	Title         string        `json:"title"`
	Description   string        `json:"description"`
	Cover         string        `json:"cover"`
	Image         string        `json:"image"`     // 兼容前端字段
	Thumbnail     string        `json:"thumbnail"` // 兼容前端字段
	URL           string        `json:"url"`
	Duration      string        `json:"duration"` // 格式化后的时长，如 "05:30"
	ViewCount     uint64        `json:"viewCount"`
	LikeCount     uint64        `json:"likeCount"`
	Hot           uint64        `json:"hot"`  // 兼容前端字段
	Heat          uint64        `json:"heat"` // 热度值
	CommentCount  uint64        `json:"commentCount"`
	ShareCount    uint64        `json:"shareCount"`
	CategoryID    string        `json:"categoryID"`
	CategoryName  string        `json:"categoryName"`
	CreatorID     string        `json:"creatorID"`
	CreatorName   string        `json:"creatorName"`
	CreatorAvatar string        `json:"creatorAvatar"`
	IsFeatured    int8          `json:"isFeatured"`
	Status        int8          `json:"status"`
	CreatedAt     string        `json:"createdAt"`
	UpdatedAt     string        `json:"updatedAt"`
	Author        *HomeBaseUser `json:"author"`
}
type HomeBaseUser struct {
	ID               string `json:"id"`
	Username         string `json:"username"`
	Nickname         string `json:"nickname"`
	Avatar           string `json:"avatar"`
	LikeCount        uint64 `json:"likeCount"`
	IsContentCreator int8   `json:"isContentCreator"` // 是否内容创作者
	ViewCount        uint64 `json:"viewCount"`
	FollowCount      uint64 `json:"followCount"`
	TotalVideos      uint64 `json:"totalVideos"`
	TotalShorts      uint64 `json:"totalShorts"`
	TotalPosts       uint64 `json:"totalPosts"`
	IsFollowed       bool   `json:"isFollowed"` // 是否关注
}

// HomeStar 首页明星 - 前端响应类型
type HomeStar struct {
	HomeBaseUser
}

// HomeUser 首页用户 - 前端响应类型
type HomeUser struct {
	HomeBaseUser
}

type HomeCreator struct {
	HomeBaseUser
}

// HomeCategory 首页分类 - 前端响应类型
type HomeCategory struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Code       string `json:"code"`
	Icon       string `json:"icon"`
	Cover      string `json:"cover"`
	Sort       int    `json:"sort"`
	ViewCount  uint64 `json:"viewCount"`
	VideoCount uint64 `json:"videoCount"`
	IsFeatured int8   `json:"isFeatured"`
}

type HomeChannel struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Code       string `json:"code"`
	Icon       string `json:"icon"`
	Image      string `json:"image"`
	Sort       int    `json:"sort"`
	ViewCount  uint64 `json:"viewCount"`
	VideoCount uint64 `json:"videoCount"`
}
type HomeAlbum struct {
	ID           string        `json:"id"`
	Title        string        `json:"title"`
	Description  string        `json:"description"`
	CategoryID   string        `json:"categoryId"`
	CategoryName string        `json:"categoryName"`
	Tags         []string      `json:"tags"`
	Cover        string        `json:"cover"`
	Heat         int           `json:"heat"`
	ViewCount    int           `json:"viewCount"`
	VideoCount   int           `json:"videoCount"`
	IsPaid       int8          `json:"isPaid"`
	Price        float64       `json:"price"`
	CreatedAt    string        `json:"createdAt"`
	UpdatedAt    string        `json:"updatedAt"`
	Status       int8          `json:"status"`
	Author       *HomeBaseUser `json:"author"`
}

// HomeTagListResponse 标签列表响应 - 前端响应类型
type HomeTagListResponse struct {
	BaseListResponse
	List []HomeTag `json:"list"`
}

// HomeVideoListResponse 视频列表响应 - 前端响应类型
type HomeVideoListResponse struct {
	BaseListResponse
	List []HomeVideo `json:"list"`
}

// HomeStarListResponse 明星列表响应 - 前端响应类型
type HomeStarListResponse struct {
	BaseListResponse
	List []HomeStar `json:"list"`
}

// HomeUserListResponse 用户列表响应 - 前端响应类型
type HomeUserListResponse struct {
	BaseListResponse
	List []HomeUser `json:"list"`
}

// HomeCreatorListResponse 创作者列表响应 - 前端响应类型
type HomeCreatorListResponse struct {
	BaseListResponse
	List []HomeCreator `json:"list"`
}

// HomeCategoryListResponse 分类列表响应 - 前端响应类型
type HomeCategoryListResponse struct {
	BaseListResponse
	List []HomeCategory `json:"list"`
}

// 视频频道列表
type HomeChannelListResponse struct {
	BaseListResponse
	List []HomeChannel `json:"list"`
}

// 视频专辑列表
type HomeAlbumListResponse struct {
	BaseListResponse
	List []HomeAlbum `json:"list"`
}
