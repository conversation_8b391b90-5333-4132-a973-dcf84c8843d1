package posts

import (
	"context"
	"frontapi/internal/models/posts"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// PostRepository 帖子仓库接口
type PostRepository interface {
	base.ExtendedRepository[posts.Post]
	GetRecommendPosts(ctx context.Context, condition map[string]interface{}, userID string, orderBy string, pageNo, pageSize int) ([]*posts.Post, int64, error)
}

// postRepository 帖子仓库实现
type postRepository struct {
	base.ExtendedRepository[posts.Post]
}

// NewPostRepository 创建帖子仓库实例
func NewPostRepository(db *gorm.DB) PostRepository {
	return &postRepository{
		ExtendedRepository: base.NewExtendedRepository[posts.Post](db),
	}
}

// GetRecommendPosts 获取推荐帖子
// 基于用户行为数据进行个性化推荐：
// 1. 根据用户收藏的帖子推荐相似作者的帖子
// 2. 根据用户关注的用户推荐其帖子
// 3. 根据用户点赞的帖子推荐相似分类的帖子
// 4. 如果用户无行为数据，则推荐热门帖子
func (r *postRepository) GetRecommendPosts(ctx context.Context, condition map[string]interface{}, userID string, orderBy string, pageNo, pageSize int) ([]*posts.Post, int64, error) {
	db := r.GetDB().WithContext(ctx)

	// 计算分页偏移量
	offset := (pageNo - 1) * pageSize

	// 构建基础查询
	query := db.Model(&posts.Post{})

	// 应用基础条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	// 确保只查询正常状态的帖子
	query = query.Where("status = ?", 1)

	var recommendedPosts []*posts.Post
	var total int64

	// 如果提供了用户ID，进行个性化推荐
	if userID != "" {
		// 构建个性化推荐查询
		recommendQuery := r.buildPersonalizedRecommendQuery(db, userID)

		// 应用条件过滤
		for key, value := range condition {
			recommendQuery = recommendQuery.Where(key, value)
		}

		// 排除用户已收藏的帖子
		recommendQuery = recommendQuery.Where(`
			id NOT IN (
				SELECT post_id FROM ly_user_post_collections 
				WHERE user_id = ? AND status = 1
			)
		`, userID)

		// 排除用户自己发布的帖子
		recommendQuery = recommendQuery.Where("author_id != ?", userID)

		// 获取总数
		recommendQuery.Count(&total)

		// 应用排序
		if orderBy != "" {
			recommendQuery = recommendQuery.Order(orderBy)
		} else {
			// 默认按推荐权重排序
			recommendQuery = recommendQuery.Order("recommend_score DESC, created_at DESC")
		}

		// 分页查询
		err := recommendQuery.Offset(offset).Limit(pageSize).Find(&recommendedPosts).Error
		if err != nil {
			return nil, 0, err
		}

		// 如果个性化推荐结果不足，补充热门帖子
		if len(recommendedPosts) < pageSize {
			remaining := pageSize - len(recommendedPosts)
			hotPosts, _, _ := r.getHotPosts(db, condition, userID, remaining, len(recommendedPosts))
			recommendedPosts = append(recommendedPosts, hotPosts...)
		}
	} else {
		// 无用户ID时，返回热门帖子
		hotPosts, hotTotal, err := r.getHotPosts(db, condition, "", pageSize, offset)
		if err != nil {
			return nil, 0, err
		}
		recommendedPosts = hotPosts
		total = hotTotal
	}

	return recommendedPosts, total, nil
}

// buildPersonalizedRecommendQuery 构建个性化推荐查询
func (r *postRepository) buildPersonalizedRecommendQuery(db *gorm.DB, userID string) *gorm.DB {
	return db.Select(`
		ly_posts.*,
		(
			-- 基于用户收藏的帖子作者权重 (权重: 3)
			CASE WHEN ly_posts.author_id IN (
				SELECT DISTINCT p.author_id 
				FROM ly_user_post_collections upc
				JOIN ly_posts p ON upc.post_id = p.id
				WHERE upc.user_id = ? AND upc.status = 1
			) THEN 3 ELSE 0 END +
			
			-- 基于用户关注的作者权重 (权重: 4)
			CASE WHEN ly_posts.author_id IN (
				SELECT followed_user_id FROM ly_user_follows 
				WHERE follower_user_id = ? AND status = 1
			) THEN 4 ELSE 0 END +
			
			-- 基于用户点赞的帖子分类权重 (权重: 2)
			CASE WHEN ly_posts.category_id IN (
				SELECT DISTINCT p.category_id 
				FROM ly_post_likes pl
				JOIN ly_posts p ON pl.post_id = p.id
				WHERE pl.user_id = ? AND p.category_id IS NOT NULL
			) THEN 2 ELSE 0 END +
			
			-- 基于帖子热度的权重 (标准化到0-1)
			(ly_posts.like_count * 0.4 + ly_posts.comment_count * 0.3 + ly_posts.view_count * 0.2 + ly_posts.share_count * 0.1) / 1000 +
			
			-- 基于发布时间的权重 (越新权重越高)
			(CASE 
				WHEN ly_posts.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1.5
				WHEN ly_posts.created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1.2
				WHEN ly_posts.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1.0
				WHEN ly_posts.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 0.8
				ELSE 0.5
			END)
		) as recommend_score
	`).Where("ly_posts.status = ?", 1).Having("recommend_score > 0", userID, userID, userID)
}

// getHotPosts 获取热门帖子
func (r *postRepository) getHotPosts(db *gorm.DB, condition map[string]interface{}, excludeUserID string, limit, offset int) ([]*posts.Post, int64, error) {
	query := db.Model(&posts.Post{})

	// 应用条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	// 确保只查询正常状态的帖子
	query = query.Where("status = ?", 1)

	// 排除指定用户的帖子
	if excludeUserID != "" {
		query = query.Where("author_id != ?", excludeUserID)

		// 排除用户已收藏的帖子
		query = query.Where(`
			id NOT IN (
				SELECT post_id FROM ly_user_post_collections 
				WHERE user_id = ? AND status = 1
			)
		`, excludeUserID)
	}

	var total int64
	query.Count(&total)

	// 按热度排序：综合考虑点赞数、评论数、浏览数、分享数和发布时间
	query = query.Select(`
		*,
		(
			like_count * 0.4 + 
			comment_count * 0.3 + 
			view_count * 0.2 + 
			share_count * 0.1 +
			(CASE 
				WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 100
				WHEN created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 50
				WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 20
				ELSE 0
			END)
		) as hot_score
	`).Order("hot_score DESC, created_at DESC")

	var hotPosts []*posts.Post
	err := query.Offset(offset).Limit(limit).Find(&hotPosts).Error

	return hotPosts, total, err
}
