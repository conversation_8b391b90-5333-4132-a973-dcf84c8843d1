package comics

import (
	"frontapi/internal/api"
	comics_service "frontapi/internal/service/comics"

	"github.com/gofiber/fiber/v2"
)

// ComicController 漫画处理器
type ComicPageController struct {
	api.BaseController
	comicService       comics_service.ComicService
	chapterService     comics_service.ComicChapterService
	favoriteService    comics_service.ComicFavoriteService
	commentService     comics_service.ComicCommentService
	readHistoryService comics_service.ComicReadHistoryService
}

// NewComicController 创建漫画处理器
func NewComicPageController(
	comicService comics_service.ComicService,
	chapterService comics_service.ComicChapterService,
	favoriteService comics_service.ComicFavoriteService,
	commentService comics_service.ComicCommentService,
	readHistoryService comics_service.ComicReadHistoryService,
) *ComicPageController {
	return &ComicPageController{
		comicService:       comicService,
		chapterService:     chapterService,
		favoriteService:    favoriteService,
		commentService:     commentService,
		readHistoryService: readHistoryService,
	}
}
func (c *ComicPageController) GetPageList(ctx *fiber.Ctx) error {
	return c.Success(ctx, "success")
}

func (c *ComicPageController) GetPageDetail(ctx *fiber.Ctx) error {
	return c.Success(ctx, "success")
}
