package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// UserSession 用户会话表
type UserSession struct {
	models.BaseModel
	UserID     string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                    //用户ID
	Token      string         `gorm:"column:token;type:string;size:255;not null;comment:会话token" json:"token"`                            //会话token
	DeviceType string         `gorm:"column:device_type;type:string;size:50;comment:设备类型" json:"device_type"`                             //设备类型
	DeviceInfo string         `gorm:"column:device_info;type:string;size:255;comment:设备信息" json:"device_info"`                            //设备信息
	IPAddress  string         `gorm:"column:ip_address;type:string;size:50;comment:IP地址" json:"ip_address"`                               //IP地址
	Location   string         `gorm:"column:location;type:string;size:100;comment:位置信息" json:"location"`                                  //位置信息
	IsActive   bool           `gorm:"column:is_active;type:bool;default:true;comment:是否激活" json:"is_active"`                              //是否激活
	LoginTime  types.JSONTime `gorm:"column:login_time;type:time;default:current_timestamp;comment:登录时间" json:"login_time"`               //登录时间
	ExpireTime types.JSONTime `gorm:"column:expire_time;type:time;comment:过期时间" json:"expire_time"`                                       //过期时间
	LastActive types.JSONTime `gorm:"column:last_active_time;type:time;default:current_timestamp;comment:最后活跃时间" json:"last_active_time"` //最后活跃时间

}

// TableName 表名
func (UserSession) TableName() string {
	return "ly_user_sessions"
}
