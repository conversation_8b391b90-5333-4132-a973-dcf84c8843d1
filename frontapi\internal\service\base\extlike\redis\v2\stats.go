package v2

import (
	"context"
	"time"
)

// GetStats 获取统计信息
func (s *AdapterStats) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"hit_count":    s.HitCount,
		"miss_count":   s.MissCount,
		"error_count":  s.<PERSON>r<PERSON>ount,
		"hit_rate":     s.<PERSON>Rate,
		"last_updated": s.LastUpdated,
	}
}

// Reset 重置统计信息
func (s *AdapterStats) Reset() {
	s.HitCount = 0
	s.MissCount = 0
	s.ErrorCount = 0
	s.HitRate = 0
	s.LastUpdated = time.Now()
}

// StatsOperations 统计操作处理器
type StatsOperations struct {
	client *RedisClient
	stats  *AdapterStats
}

// NewStatsOperations 创建统计操作处理器
func NewStatsOperations(client *RedisClient, stats *AdapterStats) *StatsOperations {
	return &StatsOperations{
		client: client,
		stats:  stats,
	}
}

// GetCacheStats 获取缓存统计信息
func (s *StatsOperations) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 获取适配器统计信息
	result["adapter"] = s.stats.GetStats()

	// 获取Redis连接池统计信息
	if poolStats := s.client.client.PoolStats(); poolStats != nil {
		result["pool"] = map[string]interface{}{
			"hits":        poolStats.Hits,
			"misses":      poolStats.Misses,
			"timeouts":    poolStats.Timeouts,
			"total_conns": poolStats.TotalConns,
			"idle_conns":  poolStats.IdleConns,
			"stale_conns": poolStats.StaleConns,
		}
	}

	return result, nil
}
