# Typings 目录说明

本目录包含前端响应类型定义，按模块分类组织，确保与前端类型定义保持一致。

## 目录结构

```
typings/
├── sys/           # 系统模块类型定义
│   ├── database.go    # 数据库相关类型
│   └── converters.go  # 数据转换函数
├── home/          # 首页模块类型定义
│   ├── types.go       # 首页相关类型
│   └── converters.go  # 数据转换函数
├── videos/        # 视频模块类型定义
│   └── types.go       # 视频相关类型
├── users/         # 用户模块类型定义
│   └── types.go       # 用户相关类型
└── README.md      # 本说明文档
```

## 设计原则

1. **模块化组织**: 按业务模块分类，便于维护和扩展
2. **类型一致性**: 与前端类型定义保持一致，确保数据传输的准确性
3. **数据安全**: 通过类型转换过滤敏感信息，只返回前端需要的字段
4. **性能优化**: 减少不必要的数据传输，提升接口响应速度
5. **兼容性**: 提供多个字段名支持，兼容前端的不同命名习惯

## 使用方式

### 1. 在Controller中使用

```go
import (
    homeTypings "frontapi/internal/typings/home"
    sysTypings "frontapi/internal/typings/sys"
)

// 转换数据并返回
response := homeTypings.ConvertVideoListResponse(videos, total, page, pageSize)
return c.Success(ctx, response)
```

### 2. 添加新的类型定义

1. 在对应模块目录下的 `types.go` 文件中添加类型定义
2. 在对应模块目录下的 `converters.go` 文件中添加转换函数
3. 在Controller中使用新的类型和转换函数

### 3. 字段命名规范

- 使用 `json` 标签定义JSON字段名
- 保持与前端类型定义一致
- 提供兼容字段支持多种命名方式

## 注意事项

1. 所有对外接口都应该使用typings中定义的类型
2. 不要直接返回内部模型，必须通过转换函数处理
3. 新增字段时要考虑前端兼容性
4. 敏感信息（如密码、token等）不应该包含在响应类型中 