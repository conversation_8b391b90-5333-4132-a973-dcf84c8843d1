<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>用户登录日志管理</h2>
            <div class="buttons">
              <el-button type="warning" :icon="Delete" @click="handleClearLogs">
                清空日志
              </el-button>
              <el-button type="success" :icon="Refresh" @click="refreshList">
                刷新
              </el-button>
              <el-button type="info" :icon="Download" @click="handleExport">
                导出
              </el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 使用登录日志搜索组件 -->
      <LoginLogSearchBar
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
        @refresh="refreshList"
        class="mb-4"
      />

      <!-- 使用登录日志表格组件 -->
      <LoginLogTable
        :login-log-list="loginLogList"
        :loading="loading"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @delete="handleDelete"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @batch-delete="handleBatchDelete"
      />

      <!-- 对话框组件 -->
      <LoginLogDetailDialog
        :visible="detailDialogVisible"
        :login-log-data="currentLoginLog"
        @update:visible="(val: boolean) => detailDialogVisible = val"
      />

      <!-- 清空日志确认对话框 -->
      <LoginLogClearDialog
        :visible="clearDialogVisible"
        @update:visible="(val: boolean) => clearDialogVisible = val"
        @success="handleClearSuccess"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { batchDeleteLoginLogs, deleteLoginLog, getLoginLogs } from '@/service/api/users/users';
import type { UserLoginLog } from '@/types/users';
import { handleApiError } from '@/utils/errorHandler';
import { Delete, Download, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入登录日志管理组件
import LoginLogClearDialog from './components/LoginLogClearDialog.vue';
import LoginLogDetailDialog from './components/LoginLogDetailDialog.vue';
import LoginLogSearchBar from './components/LoginLogSearchBar.vue';
import LoginLogTable from './components/LoginLogTable.vue';

// 响应式数据
const loading = ref(false);
const loginLogList = ref<UserLoginLog[]>([]);
const selectedRows = ref<UserLoginLog[]>([]);
const detailDialogVisible = ref(false);
const clearDialogVisible = ref(false);
const currentLoginLog = ref<UserLoginLog | null>(null);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  user_id: '',
  username: '',
  type: undefined as number | undefined,
  login_status: '',
  ip_address: '',
  device_type: '',
  location: '',
  start_date: '',
  end_date: ''
});

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 获取登录日志列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      data: {
        keyword: searchForm.keyword,
        user_id: searchForm.user_id,
        username: searchForm.username,
        type: searchForm.type,
        login_status: searchForm.login_status,
        ip_address: searchForm.ip_address,
        device_type: searchForm.device_type,
        location: searchForm.location,
        start_date: searchForm.start_date,
        end_date: searchForm.end_date
      },
      page: {
        pageNo: pagination.page,
        pageSize: pagination.pageSize
      }
    };

    const {response, data} = await getLoginLogs(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      loginLogList.value = data.list || [];
      pagination.total = data.total || 0;
    } else {
      handleApiError(response.message || '获取登录日志列表失败');
    }
  } catch (error) {
    handleApiError(error, '获取登录日志列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (searchParams?: any) => {
  if (searchParams) {
    Object.assign(searchForm, searchParams);
  }
  pagination.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    user_id: '',
    username: '',
    type: undefined,
    login_status: '',
    ip_address: '',
    device_type: '',
    location: '',
    start_date: '',
    end_date: ''
  });
  pagination.page = 1;
  getList();
};

// 刷新列表
const refreshList = () => {
  getList();
};

// 表格选择变化
const handleSelectionChange = (selection: UserLoginLog[]) => {
  selectedRows.value = selection;
};

// 分页处理
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getList();
};

// 查看登录日志
const handleView = (row: UserLoginLog) => {
  currentLoginLog.value = row;
  detailDialogVisible.value = true;
};

// 删除登录日志
const handleDelete = async (row: UserLoginLog) => {
  try {
    const {response} = await deleteLoginLog(row.id) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('删除登录日志成功');
      getList();
    } else {
      handleApiError(response.message || '删除登录日志失败');
    }
  } catch (error) {
    handleApiError(error, '删除登录日志失败');
  }
};

// 导出登录日志数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

// 批量删除登录日志
const handleBatchDelete = async (loginLogs: UserLoginLog[]) => {
  if (loginLogs.length === 0) {
    ElMessage.warning('请选择要删除的登录日志');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${loginLogs.length} 条登录日志吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 调用批量删除API
    const ids = loginLogs.map(log => log.id);
    const {response} = await batchDeleteLoginLogs(ids) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success(`成功删除 ${loginLogs.length} 条登录日志`);
      getList();
      selectedRows.value = [];
    } else {
      handleApiError(response.message || '批量删除登录日志失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      handleApiError(error, '批量删除登录日志失败');
    }
  }
};

// 清空日志
const handleClearLogs = () => {
  clearDialogVisible.value = true;
};

// 清空成功回调
const handleClearSuccess = () => {
  getList();
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.title-container h2 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .title-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    justify-content: center;
  }
}
</style>
