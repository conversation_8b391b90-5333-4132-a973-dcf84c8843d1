package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// 用户登录日志表
type UserLoginLogs struct {
	models.BaseModelStruct
	UserID         string         `gorm:"column:user_id;type:string;not null;comment:用户ID" json:"user_id"`                                 //用户ID
	Username       string         `gorm:"-:all" json:"username"`                                                                           // 用户名（不存储在数据库中，用于前端展示）
	LoginTime      types.JSONTime `gorm:"column:login_time;type:datetime;comment:登录时间" json:"login_time"`                                  //登录时间
	Type           int8           `gorm:"column:type;type:tinyint;default:0;comment:登录类型，0登录，1退出" json:"type"`                             //登录类型，0登录，1退出
	LoginType      string         `gorm:"column:login_type;type:varchar(50);comment:登录类型：password, token, oauth2, etc." json:"login_type"` //登录类型：password, token, oauth2, etc.
	IPAddress      string         `gorm:"column:ip_address;type:varchar(50);comment:登录IP地址" json:"ip_address"`                             //登录IP地址
	DeviceType     string         `gorm:"column:device_type;type:varchar(20);comment:设备类型" json:"device_type"`                             //设备类型
	OSName         string         `gorm:"column:os_name;type:varchar(50);comment:操作系统名称" json:"os_name"`                                   //操作系统名称
	OSVersion      string         `gorm:"column:os_version;type:varchar(20);comment:操作系统版本号" json:"os_version"`                            //操作系统版本号
	BrowserName    string         `gorm:"column:browser_name;type:varchar(50);comment:浏览器名称" json:"browser_name"`                          //浏览器名称
	BrowserVersion string         `gorm:"column:browser_version;type:varchar(20);comment:浏览器版本号" json:"browser_version"`                   //浏览器版本号
	UserAgent      string         `gorm:"column:user_agent;type:varchar(500);comment:完整的User-Agent字符串" json:"user_agent"`                  //完整的User-Agent字符串
	Location       string         `gorm:"column:location;type:varchar(100);comment:地理位置" json:"location"`                                  //地理位置
	LoginStatus    string         `gorm:"column:login_status;type:varchar(20);not null;comment:登录状态：成功或失败" json:"login_status"`            //登录状态：成功或失败
	FailureReason  string         `gorm:"column:failure_reason;type:varchar(100);comment:失败原因，若登录失败时记录" json:"failure_reason"`             //失败原因，若登录失败时记录
}

func (UserLoginLogs) TableName() string {
	return "ly_user_login_logs"
}

// UserLoginLogsRepository 用户登录日志数据访问接口
