/**
 * URL工具函数
 */

/**
 * URL参数类型
 */
export type URLParams = Record<string, string | number | boolean | null | undefined>

/**
 * URL解析结果接口
 */
export interface ParsedURL {
  protocol: string
  hostname: string
  port: string
  pathname: string
  search: string
  hash: string
  origin: string
  params: URLParams
}

/**
 * 路由参数接口
 */
export interface RouteParams {
  path: string
  params: Record<string, string>
  query: URLParams
  hash: string
}

/**
 * URL构建选项
 */
export interface URLBuildOptions {
  base?: string
  params?: URLParams
  hash?: string
  encode?: boolean
}

/**
 * 解析URL
 * @param url URL字符串
 * @returns 解析结果
 */
export function parseURL(url: string): ParsedURL {
  try {
    const urlObj = new URL(url, window?.location?.origin)
    const params: URLParams = {}
    
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value
    })
    
    return {
      protocol: urlObj.protocol,
      hostname: urlObj.hostname,
      port: urlObj.port,
      pathname: urlObj.pathname,
      search: urlObj.search,
      hash: urlObj.hash,
      origin: urlObj.origin,
      params
    }
  } catch (error) {
    throw new Error(`Invalid URL: ${url}`)
  }
}

/**
 * 构建URL
 * @param path 路径
 * @param options 选项
 * @returns URL字符串
 */
export function buildURL(path: string, options: URLBuildOptions = {}): string {
  const { base = '', params = {}, hash = '', encode = true } = options
  
  let url = base ? `${base.replace(/\/$/, '')}/${path.replace(/^\//, '')}` : path
  
  // 添加查询参数
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  if (queryString) {
    url += (url.includes('?') ? '&' : '?') + queryString
  }
  
  // 添加hash
  if (hash) {
    url += '#' + (encode ? encodeURIComponent(hash) : hash)
  }
  
  return url
}

/**
 * 获取URL参数
 * @param url URL字符串，默认为当前页面URL
 * @returns 参数对象
 */
export function getURLParams(url?: string): URLParams {
  const targetURL = url || (typeof window !== 'undefined' ? window.location.href : '')
  if (!targetURL) return {}
  
  try {
    const urlObj = new URL(targetURL)
    const params: URLParams = {}
    
    urlObj.searchParams.forEach((value, key) => {
      // 尝试转换为合适的类型
      if (value === 'true') {
        params[key] = true
      } else if (value === 'false') {
        params[key] = false
      } else if (value === 'null') {
        params[key] = null
      } else if (value === 'undefined') {
        params[key] = undefined
      } else if (/^\d+$/.test(value)) {
        params[key] = parseInt(value, 10)
      } else if (/^\d*\.\d+$/.test(value)) {
        params[key] = parseFloat(value)
      } else {
        params[key] = value
      }
    })
    
    return params
  } catch {
    return {}
  }
}

/**
 * 获取单个URL参数
 * @param key 参数名
 * @param url URL字符串，默认为当前页面URL
 * @returns 参数值
 */
export function getURLParam(key: string, url?: string): string | null {
  const params = getURLParams(url)
  const value = params[key]
  return value !== null && value !== undefined ? String(value) : null
}

/**
 * 设置URL参数
 * @param params 参数对象
 * @param url URL字符串，默认为当前页面URL
 * @returns 新的URL字符串
 */
export function setURLParams(params: URLParams, url?: string): string {
  const targetURL = url || (typeof window !== 'undefined' ? window.location.href : '')
  if (!targetURL) return ''
  
  try {
    const urlObj = new URL(targetURL)
    
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        urlObj.searchParams.delete(key)
      } else {
        urlObj.searchParams.set(key, String(value))
      }
    })
    
    return urlObj.toString()
  } catch {
    return targetURL
  }
}

/**
 * 设置单个URL参数
 * @param key 参数名
 * @param value 参数值
 * @param url URL字符串，默认为当前页面URL
 * @returns 新的URL字符串
 */
export function setURLParam(key: string, value: string | number | boolean | null, url?: string): string {
  return setURLParams({ [key]: value }, url)
}

/**
 * 移除URL参数
 * @param keys 参数名数组
 * @param url URL字符串，默认为当前页面URL
 * @returns 新的URL字符串
 */
export function removeURLParams(keys: string[], url?: string): string {
  const params: URLParams = {}
  keys.forEach(key => {
    params[key] = null
  })
  return setURLParams(params, url)
}

/**
 * 移除单个URL参数
 * @param key 参数名
 * @param url URL字符串，默认为当前页面URL
 * @returns 新的URL字符串
 */
export function removeURLParam(key: string, url?: string): string {
  return removeURLParams([key], url)
}

/**
 * 清空所有URL参数
 * @param url URL字符串，默认为当前页面URL
 * @returns 新的URL字符串
 */
export function clearURLParams(url?: string): string {
  const targetURL = url || (typeof window !== 'undefined' ? window.location.href : '')
  if (!targetURL) return ''
  
  try {
    const urlObj = new URL(targetURL)
    urlObj.search = ''
    return urlObj.toString()
  } catch {
    return targetURL
  }
}

/**
 * 获取URL的hash部分
 * @param url URL字符串，默认为当前页面URL
 * @returns hash字符串（不包含#）
 */
export function getURLHash(url?: string): string {
  const targetURL = url || (typeof window !== 'undefined' ? window.location.href : '')
  if (!targetURL) return ''
  
  try {
    const urlObj = new URL(targetURL)
    return urlObj.hash.slice(1) // 移除#
  } catch {
    return ''
  }
}

/**
 * 设置URL的hash部分
 * @param hash hash字符串
 * @param url URL字符串，默认为当前页面URL
 * @returns 新的URL字符串
 */
export function setURLHash(hash: string, url?: string): string {
  const targetURL = url || (typeof window !== 'undefined' ? window.location.href : '')
  if (!targetURL) return ''
  
  try {
    const urlObj = new URL(targetURL)
    urlObj.hash = hash ? `#${hash}` : ''
    return urlObj.toString()
  } catch {
    return targetURL
  }
}

/**
 * 检查URL是否有效
 * @param url URL字符串
 * @returns 是否有效
 */
export function isValidURL(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 检查是否为绝对URL
 * @param url URL字符串
 * @returns 是否为绝对URL
 */
export function isAbsoluteURL(url: string): boolean {
  return /^https?:\/\//i.test(url)
}

/**
 * 检查是否为相对URL
 * @param url URL字符串
 * @returns 是否为相对URL
 */
export function isRelativeURL(url: string): boolean {
  return !isAbsoluteURL(url) && !url.startsWith('//')
}

/**
 * 检查是否为外部URL
 * @param url URL字符串
 * @param currentOrigin 当前域名，默认为window.location.origin
 * @returns 是否为外部URL
 */
export function isExternalURL(url: string, currentOrigin?: string): boolean {
  if (!isAbsoluteURL(url)) return false
  
  const origin = currentOrigin || (typeof window !== 'undefined' ? window.location.origin : '')
  if (!origin) return true
  
  try {
    const urlObj = new URL(url)
    return urlObj.origin !== origin
  } catch {
    return true
  }
}

/**
 * 规范化URL
 * @param url URL字符串
 * @param base 基础URL
 * @returns 规范化后的URL
 */
export function normalizeURL(url: string, base?: string): string {
  try {
    const baseURL = base || (typeof window !== 'undefined' ? window.location.origin : '')
    const urlObj = new URL(url, baseURL)
    return urlObj.toString()
  } catch {
    return url
  }
}

/**
 * 连接URL路径
 * @param paths 路径数组
 * @returns 连接后的路径
 */
export function joinURLPaths(...paths: string[]): string {
  return paths
    .map((path, index) => {
      if (index === 0) {
        return path.replace(/\/$/, '')
      }
      return path.replace(/^\//, '').replace(/\/$/, '')
    })
    .filter(Boolean)
    .join('/')
}

/**
 * 获取URL的文件名
 * @param url URL字符串
 * @returns 文件名
 */
export function getURLFilename(url: string): string {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    return pathname.split('/').pop() || ''
  } catch {
    return ''
  }
}

/**
 * 获取URL的文件扩展名
 * @param url URL字符串
 * @returns 扩展名（包含.）
 */
export function getURLExtension(url: string): string {
  const filename = getURLFilename(url)
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex > 0 ? filename.slice(lastDotIndex) : ''
}

/**
 * 获取URL的目录路径
 * @param url URL字符串
 * @returns 目录路径
 */
export function getURLDirectory(url: string): string {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    const parts = pathname.split('/')
    parts.pop() // 移除文件名
    return parts.join('/') || '/'
  } catch {
    return ''
  }
}

/**
 * 编码URL组件
 * @param str 字符串
 * @returns 编码后的字符串
 */
export function encodeURLComponent(str: string): string {
  return encodeURIComponent(str)
    .replace(/[!'()*]/g, (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`)
}

/**
 * 解码URL组件
 * @param str 编码的字符串
 * @returns 解码后的字符串
 */
export function decodeURLComponent(str: string): string {
  try {
    return decodeURIComponent(str)
  } catch {
    return str
  }
}

/**
 * 转换对象为查询字符串
 * @param params 参数对象
 * @param options 选项
 * @returns 查询字符串
 */
export function objectToQueryString(
  params: URLParams,
  options: { encode?: boolean; arrayFormat?: 'brackets' | 'comma' | 'repeat' } = {}
): string {
  const { encode = true, arrayFormat = 'repeat' } = options
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value === null || value === undefined) return
    
    if (Array.isArray(value)) {
      switch (arrayFormat) {
        case 'brackets':
          value.forEach(item => searchParams.append(`${key}[]`, String(item)))
          break
        case 'comma':
          searchParams.append(key, value.join(','))
          break
        case 'repeat':
        default:
          value.forEach(item => searchParams.append(key, String(item)))
          break
      }
    } else {
      searchParams.append(key, String(value))
    }
  })
  
  let queryString = searchParams.toString()
  
  if (!encode) {
    queryString = decodeURIComponent(queryString)
  }
  
  return queryString
}

/**
 * 转换查询字符串为对象
 * @param queryString 查询字符串
 * @param options 选项
 * @returns 参数对象
 */
export function queryStringToObject(
  queryString: string,
  options: { decode?: boolean; parseArrays?: boolean } = {}
): URLParams {
  const { decode = true, parseArrays = true } = options
  const params: URLParams = {}
  
  if (!queryString) return params
  
  const searchParams = new URLSearchParams(queryString)
  
  if (parseArrays) {
    const arrayKeys = new Set<string>()
    
    searchParams.forEach((value, key) => {
      const cleanKey = key.replace(/\[\]$/, '')
      
      if (key.endsWith('[]') || arrayKeys.has(cleanKey)) {
        arrayKeys.add(cleanKey)
        if (!params[cleanKey]) {
          params[cleanKey]=[]
        }
        ;(params[cleanKey] as any[]).push(decode ? decodeURLComponent(value) : value)
      } else if (params[cleanKey]) {
        // 如果已存在同名参数，转换为数组
        if (!Array.isArray(params[cleanKey])) {
          params[cleanKey] = [params[cleanKey] as string]
        }
        ;(params[cleanKey] as any[]).push(decode ? decodeURLComponent(value) : value)
        arrayKeys.add(cleanKey)
      } else {
        params[cleanKey] = decode ? decodeURLComponent(value) : value
      }
    })
  } else {
    searchParams.forEach((value, key) => {
      params[key] = decode ? decodeURLComponent(value) : value
    })
  }
  
  return params
}

/**
 * URL路由匹配器
 */
export class URLMatcher {
  private pattern: RegExp
  private paramNames: string[]
  
  constructor(pattern: string) {
    this.paramNames = []
    
    // 转换路由模式为正则表达式
    const regexPattern = pattern
      .replace(/\//g, '\\/')
      .replace(/:([^/]+)/g, (match, paramName) => {
        this.paramNames.push(paramName)
        return '([^/]+)'
      })
      .replace(/\*/g, '(.*)')
    
    this.pattern = new RegExp(`^${regexPattern}$`)
  }
  
  /**
   * 匹配URL路径
   * @param path 路径
   * @returns 匹配结果
   */
  match(path: string): { params: Record<string, string>; matched: boolean } {
    const match = this.pattern.exec(path)
    
    if (!match) {
      return { params: {}, matched: false }
    }
    
    const params: Record<string, string> = {}
    this.paramNames.forEach((name, index) => {
      params[name] = match[index + 1]
    })
    
    return { params, matched: true }
  }
}

/**
 * 创建URL匹配器
 * @param pattern 路由模式
 * @returns 匹配器实例
 */
export function createURLMatcher(pattern: string): URLMatcher {
  return new URLMatcher(pattern)
}

/**
 * 解析路由
 * @param url URL字符串
 * @param pattern 路由模式
 * @returns 路由参数
 */
export function parseRoute(url: string, pattern: string): RouteParams | null {
  try {
    const urlObj = new URL(url, typeof window !== 'undefined' ? window.location.origin : 'http://localhost')
    const matcher = createURLMatcher(pattern)
    const matchResult = matcher.match(urlObj.pathname)
    
    if (!matchResult.matched) {
      return null
    }
    
    return {
      path: urlObj.pathname,
      params: matchResult.params,
      query: getURLParams(url),
      hash: getURLHash(url)
    }
  } catch {
    return null
  }
}

/**
 * URL历史管理器
 */
export class URLHistory {
  private history: string[] = []
  private currentIndex = -1
  private maxSize: number
  
  constructor(maxSize = 50) {
    this.maxSize = maxSize
    
    // 初始化当前URL
    if (typeof window !== 'undefined') {
      this.push(window.location.href)
    }
  }
  
  /**
   * 添加URL到历史
   * @param url URL字符串
   */
  push(url: string): void {
    // 移除当前位置之后的历史
    this.history = this.history.slice(0, this.currentIndex + 1)
    
    // 添加新URL
    this.history.push(url)
    this.currentIndex++
    
    // 限制历史大小
    if (this.history.length > this.maxSize) {
      this.history.shift()
      this.currentIndex--
    }
  }
  
  /**
   * 后退
   * @returns 前一个URL
   */
  back(): string | null {
    if (this.currentIndex > 0) {
      this.currentIndex--
      return this.history[this.currentIndex]
    }
    return null
  }
  
  /**
   * 前进
   * @returns 后一个URL
   */
  forward(): string | null {
    if (this.currentIndex < this.history.length - 1) {
      this.currentIndex++
      return this.history[this.currentIndex]
    }
    return null
  }
  
  /**
   * 获取当前URL
   * @returns 当前URL
   */
  current(): string | null {
    return this.history[this.currentIndex] || null
  }
  
  /**
   * 获取历史列表
   * @returns 历史URL数组
   */
  getHistory(): string[] {
    return [...this.history]
  }
  
  /**
   * 清空历史
   */
  clear(): void {
    this.history = []
    this.currentIndex = -1
  }
  
  /**
   * 检查是否可以后退
   * @returns 是否可以后退
   */
  canGoBack(): boolean {
    return this.currentIndex > 0
  }
  
  /**
   * 检查是否可以前进
   * @returns 是否可以前进
   */
  canGoForward(): boolean {
    return this.currentIndex < this.history.length - 1
  }
}

/**
 * 创建URL历史管理器
 * @param maxSize 最大历史大小
 * @returns 历史管理器实例
 */
export function createURLHistory(maxSize?: number): URLHistory {
  return new URLHistory(maxSize)
}

/**
 * 获取当前页面的完整URL信息
 * @returns URL信息对象
 */
export function getCurrentURLInfo(): ParsedURL | null {
  if (typeof window === 'undefined') return null
  
  try {
    return parseURL(window.location.href)
  } catch {
    return null
  }
}

/**
 * 更新当前页面URL（不刷新页面）
 * @param url 新的URL
 * @param title 页面标题
 */
export function updateCurrentURL(url: string, title?: string): void {
  if (typeof window === 'undefined' || !window.history) return
  
  try {
    window.history.pushState({}, title || document.title, url)
    if (title) {
      document.title = title
    }
  } catch (error) {
    console.error('Failed to update URL:', error)
  }
}

/**
 * 替换当前页面URL（不刷新页面）
 * @param url 新的URL
 * @param title 页面标题
 */
export function replaceCurrentURL(url: string, title?: string): void {
  if (typeof window === 'undefined' || !window.history) return
  
  try {
    window.history.replaceState({}, title || document.title, url)
    if (title) {
      document.title = title
    }
  } catch (error) {
    console.error('Failed to replace URL:', error)
  }
}