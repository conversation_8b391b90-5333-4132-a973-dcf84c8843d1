package base

import (
	"context"
	"errors"
	"fmt"

	"frontapi/internal/models"

	"gorm.io/gorm"
)

// ExtendedRepository 扩展仓库接口，包含更多业务方法
type ExtendedRepository[T models.BaseModelConstraint] interface {
	BaseRepository[T]

	// 设置调用者实例，用于支持条件应用的自定义
	SetCaller(caller interface{})

	// 计数更新操作
	UpdateCount(ctx context.Context, id string, field string, increment int64) error
	UpdateViewCount(ctx context.Context, id string) error
	UpdateLikeCount(ctx context.Context, id string, increment int) error
	UpdateCommentCount(ctx context.Context, id string, increment int) error

	// 状态操作
	UpdateStatus(ctx context.Context, id string, status int) error
	BatchUpdateStatus(ctx context.Context, ids []string, status int) error

	// 软删除操作
	SoftDelete(ctx context.Context, id string) error
	BatchSoftDelete(ctx context.Context, ids []string) error
	Restore(ctx context.Context, id string) error

	// 排序操作
	UpdateSortOrder(ctx context.Context, id string, sortOrder int) error

	// 分页查询增强
	ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)

	// 条件查询方法
	ListWithCondition(ctx context.Context, orderBy string, whereClause string, args ...interface{}) ([]*T, error)
	ListWithConditionAndPagination(ctx context.Context, orderBy string, page, pageSize int, whereClause string, args ...interface{}) ([]*T, int64, error)

	// 原生SQL支持
	ExecuteRaw(ctx context.Context, sql string, values ...interface{}) error
	QueryRaw(ctx context.Context, dest interface{}, sql string, values ...interface{}) error

	// 批量更新字段
	BatchUpdateColumn(ctx context.Context, ids []string, columns map[string]interface{}) error
}

// extendedRepository 扩展仓库实现
type extendedRepository[T models.BaseModelConstraint] struct {
	*baseRepository[T]
	caller interface{} // 用于存储具体的仓库实例，支持条件应用的自定义
}

// NewExtendedRepository 创建扩展仓库实例
func NewExtendedRepository[T models.BaseModelConstraint](db *gorm.DB) ExtendedRepository[T] {
	return &extendedRepository[T]{
		baseRepository: &baseRepository[T]{db: db},
		caller:         nil,
	}
}

// SetCaller 设置调用者实例，用于支持条件应用的自定义
func (r *extendedRepository[T]) SetCaller(caller interface{}) {
	r.caller = caller
	r.baseRepository.caller = caller
}

// applyConditions 重写基础仓库的applyConditions方法，支持传递caller
func (r *extendedRepository[T]) applyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	return r.baseRepository.applyConditionsWithCaller(query, condition, r.caller)
}

// UpdateCount 更新计数字段
func (r *extendedRepository[T]) UpdateCount(ctx context.Context, id string, field string, increment int64) error {
	if id == "" {
		return errors.New("id cannot be empty")
	}
	if field == "" {
		return errors.New("field cannot be empty")
	}
	//这里判断是否是数据库的字段
	var entity T
	stmt := &gorm.Statement{DB: r.db}
	err := stmt.Parse(&entity)
	if err != nil {
		return err
	}
	// 检查字段是否存在
	if _, ok := stmt.Schema.FieldsByDBName[field]; !ok {
		return fmt.Errorf("field %s does not exist in table %s", field, stmt.Table)
	}

	return r.db.WithContext(ctx).Model(&entity).Where("id = ?", id).Update(field, gorm.Expr(field+" + ?", increment)).Error
}

// UpdateViewCount 更新浏览次数
func (r *extendedRepository[T]) UpdateViewCount(ctx context.Context, id string) error {
	return r.UpdateCount(ctx, id, "view_count", 1)
}

// UpdateLikeCount 更新点赞数
func (r *extendedRepository[T]) UpdateLikeCount(ctx context.Context, id string, increment int) error {
	return r.UpdateCount(ctx, id, "like_count", int64(increment))
}

// UpdateCommentCount 更新评论数
func (r *extendedRepository[T]) UpdateCommentCount(ctx context.Context, id string, increment int) error {
	return r.UpdateCount(ctx, id, "comment_count", int64(increment))
}

// UpdateStatus 更新状态
func (r *extendedRepository[T]) UpdateStatus(ctx context.Context, id string, status int) error {
	if id == "" {
		return errors.New("id cannot be empty")
	}

	var entity T
	return r.db.WithContext(ctx).Model(&entity).Where("id = ?", id).Update("status", status).Error
}

// BatchUpdateStatus 批量更新状态
func (r *extendedRepository[T]) BatchUpdateStatus(ctx context.Context, ids []string, status int) error {
	if len(ids) == 0 {
		return errors.New("ids cannot be empty")
	}

	// 使用事务确保原子性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var entity T
		return tx.Model(&entity).Where("id IN ?", ids).Update("status", status).Error
	})
}

// SoftDelete 软删除
func (r *extendedRepository[T]) SoftDelete(ctx context.Context, id string) error {
	return r.UpdateStatus(ctx, id, -4) // -4 表示已删除
}

// BatchSoftDelete 批量软删除
func (r *extendedRepository[T]) BatchSoftDelete(ctx context.Context, ids []string) error {
	// 通过BatchUpdateStatus方法确保事务原子性
	return r.BatchUpdateStatus(ctx, ids, -4)
}

// Restore 恢复软删除
func (r *extendedRepository[T]) Restore(ctx context.Context, id string) error {
	return r.UpdateStatus(ctx, id, 1) // 1 表示正常状态
}

// UpdateSortOrder 更新排序
func (r *extendedRepository[T]) UpdateSortOrder(ctx context.Context, id string, sortOrder int) error {
	if id == "" {
		return errors.New("id cannot be empty")
	}

	var entity T
	return r.db.WithContext(ctx).Model(&entity).Where("id = ?", id).Update("sort_order", sortOrder).Error
}

// ListWithPreload 带预加载的列表查询
func (r *extendedRepository[T]) ListWithPreload(ctx context.Context, preloads []string, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error) {
	var entities []*T
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(new(T))

	// 应用预加载
	for _, preload := range preloads {
		query = query.Preload(preload)
	}

	// 应用条件
	query = r.applyConditions(query, condition)

	// 获取总数（不包含预加载，提高性能）
	countQuery := r.db.WithContext(ctx).Model(new(T))
	countQuery = r.applyConditions(countQuery, condition)
	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	err = query.Find(&entities).Error
	if err != nil {
		return nil, 0, err
	}

	return entities, total, nil
}

// ExecuteRaw 执行原生SQL
func (r *extendedRepository[T]) ExecuteRaw(ctx context.Context, sql string, values ...interface{}) error {
	return r.db.WithContext(ctx).Exec(sql, values...).Error
}

// QueryRaw 查询原生SQL
func (r *extendedRepository[T]) QueryRaw(ctx context.Context, dest interface{}, sql string, values ...interface{}) error {
	return r.db.WithContext(ctx).Raw(sql, values...).Scan(dest).Error
}

// ListWithCondition 根据条件查询列表
func (r *extendedRepository[T]) ListWithCondition(ctx context.Context, orderBy string, whereClause string, args ...interface{}) ([]*T, error) {
	var entities []*T

	// 构建查询
	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	if whereClause != "" {
		query = query.Where(whereClause, args...)
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 执行查询
	err := query.Find(&entities).Error
	return entities, err
}

// ListWithConditionAndPagination 根据条件查询分页列表
func (r *extendedRepository[T]) ListWithConditionAndPagination(ctx context.Context, orderBy string, page, pageSize int, whereClause string, args ...interface{}) ([]*T, int64, error) {
	var entities []*T
	var total int64

	// 构建查询
	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	if whereClause != "" {
		query = query.Where(whereClause, args...)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	// 执行查询
	err = query.Find(&entities).Error
	return entities, total, err
}

// BatchUpdateColumn 批量更新字段
func (r *extendedRepository[T]) BatchUpdateColumn(ctx context.Context, ids []string, columns map[string]interface{}) error {

	if len(ids) == 0 {
		return errors.New("ids cannot be empty")
	}
	var entity T
	err := r.db.WithContext(ctx).Model(&entity).Where("id IN ?", ids).Updates(columns).Error
	if err != nil {
		return err
	}
	return nil
}
