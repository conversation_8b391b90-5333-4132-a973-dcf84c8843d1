package comics

import (
	"context"
	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ComicRepository 漫画数据访问接口
type ComicRepository interface {
	base.ExtendedRepository[comics.Comic]
	GetRelatedComics(ctx context.Context, condition map[string]interface{}, orderBy string, pageNo int, pageSize int) ([]*comics.Comic, int64, error)
}

// comicRepository 漫画数据访问实现
type comicRepository struct {
	base.ExtendedRepository[comics.Comic]
}

// NewComicRepository 创建漫画仓库实例
func NewComicRepository(db *gorm.DB) ComicRepository {
	return &comicRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.Comic](db),
	}
}

// 获取相关漫画推荐
// 基于分类、标签、作者、评分等进行高效推荐
func (r *comicRepository) GetRelatedComics(ctx context.Context, condition map[string]interface{}, orderBy string, pageNo int, pageSize int) ([]*comics.Comic, int64, error) {
	db := r.GetDB().WithContext(ctx)

	// 获取当前漫画信息用于推荐计算
	currentComicID, _ := condition["comic_id"].(string)
	// userID, _ := condition["user_id"].(string)

	// 构建推荐查询
	query := db.Model(&comics.Comic{}).Where("status = ?", 1)

	// 排除当前漫画
	if currentComicID != "" {
		query = query.Where("id != ?", currentComicID)
	}

	// 如果有当前漫画ID，基于内容相似性推荐
	if currentComicID != "" {
		var currentComic comics.Comic
		if err := db.Where("id = ? AND status = ?", currentComicID, 1).First(&currentComic).Error; err == nil {
			// 构建相似性评分的复杂查询
			// 使用CASE WHEN语句计算相似度得分
			similarityQuery := `(
					CASE WHEN category_id = ? THEN 30 ELSE 0 END +
					CASE WHEN author = ? THEN 25 ELSE 0 END +
					CASE 
						WHEN ABS(rating - ?) <= 0.5 THEN 20
						WHEN ABS(rating - ?) <= 1.0 THEN 15
						WHEN ABS(rating - ?) <= 1.5 THEN 10
						ELSE 0
					END +
					CASE WHEN progress = ? THEN 10 ELSE 0 END +
					CASE 
						WHEN popularity > 0 AND ? > 0 THEN
							CASE 
								WHEN (
									CASE 
										WHEN popularity >= ? THEN (popularity - ?) 
										ELSE (? - popularity) 
									END
								) / GREATEST(popularity, ?) <= 0.3 THEN 10
								WHEN (
									CASE 
										WHEN popularity >= ? THEN (popularity - ?) 
										ELSE (? - popularity) 
									END
								) / GREATEST(popularity, ?) <= 0.5 THEN 5
								ELSE 0
							END
						ELSE 0
					END +
					CASE WHEN is_paid = ? THEN 5 ELSE 0 END
				) AS similarity_score`

			// 重新构建查询，添加相似度评分字段
			query = query.Select("ly_comics.*, " + similarityQuery,
				currentComic.CategoryID.String,
				currentComic.Author,
				currentComic.Rating, currentComic.Rating, currentComic.Rating,
				currentComic.Progress,
				currentComic.Popularity,
				currentComic.Popularity, currentComic.Popularity, currentComic.Popularity,
				currentComic.Popularity,
				currentComic.Popularity, currentComic.Popularity, currentComic.Popularity,
				currentComic.Popularity,
				currentComic.IsPaid,
			)
		}
	}

	// 应用其他筛选条件
	if categoryID, ok := condition["category_id"]; ok && categoryID != "" {
		query = query.Where("category_id = ?", categoryID)
	}

	if author, ok := condition["author"]; ok && author != "" {
		query = query.Where("author LIKE ?", "%"+author.(string)+"%")
	}

	if isAdult, ok := condition["is_adult"]; ok {
		query = query.Where("is_adult = ?", isAdult)
	}

	if isPaid, ok := condition["is_paid"]; ok {
		query = query.Where("is_paid = ?", isPaid)
	}

	if minRating, ok := condition["min_rating"]; ok {
		query = query.Where("rating >= ?", minRating)
	}

	// 排序逻辑
	if orderBy == "" {
		if currentComicID != "" {
			// 有当前漫画时，按相似度排序，然后按人气和评分
			orderBy = "similarity_score DESC, popularity DESC, rating DESC, updated_at DESC"
		} else {
			// 没有当前漫画时，按综合指标排序
			orderBy = "is_featured DESC, popularity DESC, rating DESC, updated_at DESC"
		}
	}
	query = query.Order(orderBy)

	// 计算总数 - 需要单独构建计数查询
	var total int64
	countQuery := db.Model(&comics.Comic{}).Where("status = ?", 1)
	if currentComicID != "" {
		countQuery = countQuery.Where("id != ?", currentComicID)
	}
	
	// 应用相同的筛选条件到计数查询
	if categoryID, ok := condition["category_id"]; ok && categoryID != "" {
		countQuery = countQuery.Where("category_id = ?", categoryID)
	}
	if author, ok := condition["author"]; ok && author != "" {
		countQuery = countQuery.Where("author LIKE ?", "%"+author.(string)+"%")
	}
	if isAdult, ok := condition["is_adult"]; ok {
		countQuery = countQuery.Where("is_adult = ?", isAdult)
	}
	if isPaid, ok := condition["is_paid"]; ok {
		countQuery = countQuery.Where("is_paid = ?", isPaid)
	}
	if minRating, ok := condition["min_rating"]; ok {
		countQuery = countQuery.Where("rating >= ?", minRating)
	}
	
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (pageNo - 1) * pageSize
	var results []*comics.Comic
	if err := query.Offset(offset).Limit(pageSize).Find(&results).Error; err != nil {
		return nil, 0, err
	}

	return results, total, nil
}
