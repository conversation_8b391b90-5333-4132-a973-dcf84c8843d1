package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// VideoSourceRepository 视频源数据访问接口
type VideoSourceRepository interface {
	base.ExtendedRepository[videos.VideoSource]
	ListByVideoID(ctx context.Context, videoID string) ([]*videos.VideoSource, error)
	DeleteByVideoID(ctx context.Context, videoID string) error
}

// videoSourceRepository 视频源数据访问实现
type videoSourceRepository struct {
	base.ExtendedRepository[videos.VideoSource]
}

// NewVideoSourceRepository 创建视频源仓库实例
func NewVideoSourceRepository(db *gorm.DB) VideoSourceRepository {
	return &videoSourceRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoSource](db),
	}
}

// ListByVideoID 获取视频的所有视频源
func (r *videoSourceRepository) ListByVideoID(ctx context.Context, videoID string) ([]*videos.VideoSource, error) {
	conditions := map[string]interface{}{
		"video_id": videoID,
	}
	return r.FindByCondition(ctx, conditions, "")
}

// DeleteByVideoID 删除视频的所有视频源
func (r *videoSourceRepository) DeleteByVideoID(ctx context.Context, videoID string) error {
	return r.GetDBWithContext(ctx).Where("video_id = ?", videoID).Delete(&videos.VideoSource{}).Error
}
