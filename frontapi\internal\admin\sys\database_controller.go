package sys

import (
	"frontapi/internal/admin"
	sysService "frontapi/internal/service/sys"
	sysTypings "frontapi/internal/typings/sys"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// DatabaseController 数据库控制器
type DatabaseController struct {
	DatabaseService      sysService.DatabaseService
	admin.BaseController // 继承BaseController
}

// NewDatabaseController 创建数据库控制器实例
func NewDatabaseController(databaseService sysService.DatabaseService) *DatabaseController {
	return &DatabaseController{
		DatabaseService: databaseService,
	}
}

// GetTableList 获取数据库表列表
func (c *DatabaseController) GetTableList(ctx *fiber.Ctx) error {
	// 获取表列表
	tables, err := c.DatabaseService.GetTableList(ctx.Context())
	if err != nil {
		return c.InternalServerError(ctx, "获取数据库表列表失败: "+err.Error())
	}

	// 转换为前端响应类型
	frontendTables := sysTypings.ConvertTableInfoList(tables)

	return c.Success(ctx, frontendTables)
}

// GetTableDetail 获取表详情
func (c *DatabaseController) GetTableDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	// 获取表名
	tableName := reqInfo.Get("table_name").GetString()
	if tableName == "" {
		return c.BadRequest(ctx, "表名不能为空", nil)
	}

	// 获取表详情
	detail, err := c.DatabaseService.GetTableDetail(ctx.Context(), tableName)
	if err != nil {
		return c.InternalServerError(ctx, "获取表详情失败: "+err.Error())
	}

	// 转换为前端响应类型
	frontendDetail := sysTypings.ConvertTableDetailResponse(detail)

	return c.Success(ctx, frontendDetail)
}

// GenerateMockData 生成Mock数据
func (c *DatabaseController) GenerateMockData(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req sysTypings.MockDataRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 转换为内部类型
	internalReq := sysTypings.ConvertMockDataRequest(req)

	// 生成Mock数据
	response, err := c.DatabaseService.GenerateMockData(ctx.Context(), &internalReq)
	if err != nil {
		return c.InternalServerError(ctx, "生成Mock数据失败: "+err.Error())
	}

	// 转换为前端响应类型
	frontendResponse := sysTypings.ConvertMockDataResponse(response)

	return c.Success(ctx, frontendResponse)
}

// InsertMockData 插入Mock数据到数据库
func (c *DatabaseController) InsertMockData(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		TableName string                   `json:"table_name" validate:"required"`
		Data      []map[string]interface{} `json:"data" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 插入数据到数据库
	err := c.DatabaseService.InsertMockDataToDB(ctx.Context(), req.TableName, req.Data)
	if err != nil {
		return c.InternalServerError(ctx, "插入数据失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "数据插入成功")
}

// GetTableDetailByPost 通过POST方式获取表详情
func (c *DatabaseController) GetTableDetailByPost(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		TableName string `json:"table_name" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 获取表详情
	detail, err := c.DatabaseService.GetTableDetail(ctx.Context(), req.TableName)
	if err != nil {
		return c.InternalServerError(ctx, "获取表详情失败: "+err.Error())
	}

	// 转换为前端响应类型
	frontendDetail := sysTypings.ConvertTableDetailResponse(detail)

	return c.Success(ctx, frontendDetail)
}

// GetForeignKeyData 获取外键关联表的数据
func (c *DatabaseController) GetForeignKeyData(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req sysTypings.ForeignKeyDataRequest

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 设置默认限制
	if req.Limit <= 0 {
		req.Limit = 50
	}

	// 获取外键关联数据
	values, err := c.DatabaseService.GetForeignKeyData(ctx.Context(), req.TableName, req.ColumnName, req.Limit)
	if err != nil {
		return c.InternalServerError(ctx, "获取外键关联数据失败: "+err.Error())
	}

	// 转换为前端响应类型
	frontendResponse := sysTypings.ConvertForeignKeyDataResponse(values)

	return c.Success(ctx, frontendResponse)
}
