import { ref, computed, onUnmounted, watch, type UnwrapRef } from 'vue'

// 缓存项接口
export interface CacheItem<T = any> {
  key: string
  value: UnwrapRef<T>
  timestamp: number
  ttl?: number // 生存时间（毫秒）
  accessCount: number
  lastAccessed: number
  size?: number // 数据大小（字节）
  tags?: string[] // 缓存标签
}

// 缓存配置
export interface CacheConfig {
  maxSize?: number // 最大缓存项数量
  maxMemory?: number // 最大内存使用（字节）
  defaultTTL?: number // 默认TTL（毫秒）
  cleanupInterval?: number // 清理间隔（毫秒）
  enablePersistence?: boolean // 是否持久化
  storageKey?: string // 本地存储键名
  enableCompression?: boolean // 是否启用压缩
}

// 缓存策略
export enum CacheStrategy {
  LRU = 'lru', // 最近最少使用
  LFU = 'lfu', // 最少使用频率
  FIFO = 'fifo', // 先进先出
  TTL = 'ttl' // 基于时间
}

// 缓存统计
export interface CacheStats {
  hits: number
  misses: number
  hitRate: number
  totalItems: number
  totalMemory: number
  oldestItem?: number
  newestItem?: number
}

/**
 * 内存缓存 Composable
 * 提供高性能的内存缓存功能
 */
export function useCache<T = any>(config: CacheConfig = {}) {
  const {
    maxSize = 1000,
    maxMemory = 50 * 1024 * 1024, // 50MB
    defaultTTL = 5 * 60 * 1000, // 5分钟
    cleanupInterval = 60 * 1000, // 1分钟
    enablePersistence = false,
    storageKey = 'app_cache',
    enableCompression = false
  } = config

  const cache = ref<Map<string, CacheItem<T>>>(new Map())
  const stats = ref({
    hits: 0,
    misses: 0,
    totalMemory: 0
  })
  
  let cleanupTimer: number | null = null

  // 计算缓存统计
  const cacheStats = computed<CacheStats>(() => {
    const items = Array.from(cache.value.values())
    const totalHits = stats.value.hits
    const totalMisses = stats.value.misses
    const total = totalHits + totalMisses
    
    return {
      hits: totalHits,
      misses: totalMisses,
      hitRate: total > 0 ? totalHits / total : 0,
      totalItems: cache.value.size,
      totalMemory: stats.value.totalMemory,
      oldestItem: items.length > 0 ? Math.min(...items.map(item => item.timestamp)) : undefined,
      newestItem: items.length > 0 ? Math.max(...items.map(item => item.timestamp)) : undefined
    }
  })

  // 计算数据大小（简单估算）
  const calculateSize = (value: UnwrapRef<T>): number => {
    if (typeof value === 'string') {
      return value.length * 2 // Unicode字符
    }
    if (typeof value === 'number') {
      return 8
    }
    if (typeof value === 'boolean') {
      return 4
    }
    if (value === null || value === undefined) {
      return 0
    }
    
    try {
      return JSON.stringify(value).length * 2
    } catch {
      return 1024 // 默认1KB
    }
  }

  // 检查缓存项是否过期
  const isExpired = (item: CacheItem<T>): boolean => {
    if (!item.ttl) return false
    return Date.now() - item.timestamp > item.ttl
  }

  // 清理过期项
  const cleanupExpired = () => {
    const now = Date.now()
    const expiredKeys: string[] = []
    
    cache.value.forEach((item, key) => {
      if (isExpired(item)) {
        expiredKeys.push(key)
      }
    })
    
    expiredKeys.forEach(key => {
      const item = cache.value.get(key)
      if (item) {
        stats.value.totalMemory -= item.size || 0
        cache.value.delete(key)
      }
    })
    
    return expiredKeys.length
  }

  // LRU清理策略
  const evictLRU = (count: number = 1) => {
    const items = Array.from(cache.value.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
    
    for (let i = 0; i < Math.min(count, items.length); i++) {
      const [key, item] = items[i]
      stats.value.totalMemory -= item.size || 0
      cache.value.delete(key)
    }
  }

  // LFU清理策略
  const evictLFU = (count: number = 1) => {
    const items = Array.from(cache.value.entries())
      .sort(([, a], [, b]) => a.accessCount - b.accessCount)
    
    for (let i = 0; i < Math.min(count, items.length); i++) {
      const [key, item] = items[i]
      stats.value.totalMemory -= item.size || 0
      cache.value.delete(key)
    }
  }

  // FIFO清理策略
  const evictFIFO = (count: number = 1) => {
    const items = Array.from(cache.value.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)
    
    for (let i = 0; i < Math.min(count, items.length); i++) {
      const [key, item] = items[i]
      stats.value.totalMemory -= item.size || 0
      cache.value.delete(key)
    }
  }

  // 执行清理策略
  const evict = (strategy: CacheStrategy = CacheStrategy.LRU, count: number = 1) => {
    switch (strategy) {
      case CacheStrategy.LRU:
        evictLRU(count)
        break
      case CacheStrategy.LFU:
        evictLFU(count)
        break
      case CacheStrategy.FIFO:
        evictFIFO(count)
        break
      case CacheStrategy.TTL:
        cleanupExpired()
        break
    }
  }

  // 检查是否需要清理
  const needsEviction = (): boolean => {
    return cache.value.size >= maxSize || stats.value.totalMemory >= maxMemory
  }

  // 设置缓存
  const set = (key: string, value: UnwrapRef<T>, ttl?: number, tags?: string[]): void => {
    const now = Date.now()
    const size = calculateSize(value)
    
    // 如果已存在，先删除旧的
    if (cache.value.has(key)) {
      const oldItem = cache.value.get(key)!
      stats.value.totalMemory -= oldItem.size || 0
    }
    
    // 检查是否需要清理空间
    while (needsEviction() && cache.value.size > 0) {
      evict(CacheStrategy.LRU, Math.ceil(cache.value.size * 0.1))
    }
    
    const item: CacheItem<T> = {
      key,
      value,
      timestamp: now,
      ttl: ttl || defaultTTL,
      accessCount: 0,
      lastAccessed: now,
      size,
      tags
    }
    
    cache.value.set(key, item)
    stats.value.totalMemory += size
    
    // 持久化
    if (enablePersistence) {
      persistToStorage()
    }
  }

  // 获取缓存
  const get = (key: string): UnwrapRef<T> | null => {
    const item = cache.value.get(key)
    
    if (!item) {
      stats.value.misses++
      return null
    }
    
    // 检查是否过期
    if (isExpired(item)) {
      stats.value.totalMemory -= item.size || 0
      cache.value.delete(key)
      stats.value.misses++
      return null
    }
    
    // 更新访问信息
    item.accessCount++
    item.lastAccessed = Date.now()
    stats.value.hits++
    
    return item.value
  }

  // 检查是否存在
  const has = (key: string): boolean => {
    const item = cache.value.get(key)
    if (!item) return false
    
    if (isExpired(item)) {
      stats.value.totalMemory -= item.size || 0
      cache.value.delete(key)
      return false
    }
    
    return true
  }

  // 删除缓存
  const remove = (key: string): boolean => {
    const item = cache.value.get(key)
    if (item) {
      stats.value.totalMemory -= item.size || 0
      cache.value.delete(key)
      
      if (enablePersistence) {
        persistToStorage()
      }
      
      return true
    }
    return false
  }

  // 根据标签删除
  const removeByTag = (tag: string): number => {
    let count = 0
    const keysToRemove: string[] = []
    
    cache.value.forEach((item, key) => {
      if (item.tags && item.tags.includes(tag)) {
        keysToRemove.push(key)
      }
    })
    
    keysToRemove.forEach(key => {
      if (remove(key)) {
        count++
      }
    })
    
    return count
  }

  // 清空缓存
  const clear = (): void => {
    cache.value.clear()
    stats.value.totalMemory = 0
    stats.value.hits = 0
    stats.value.misses = 0
    
    if (enablePersistence) {
      localStorage.removeItem(storageKey)
    }
  }

  // 获取所有键
  const keys = (): string[] => {
    return Array.from(cache.value.keys())
  }

  // 获取所有值
  const values = (): UnwrapRef<T>[] => {
    return Array.from(cache.value.values()).map(item => item.value)
  }

  // 持久化到本地存储
  const persistToStorage = () => {
    if (!enablePersistence) return
    
    try {
      const data = {
        cache: Array.from(cache.value.entries()),
        stats: stats.value,
        timestamp: Date.now()
      }
      
      const serialized = JSON.stringify(data)
      localStorage.setItem(storageKey, serialized)
    } catch (error) {
      console.warn('Failed to persist cache to storage:', error)
    }
  }

  // 从本地存储恢复
  const restoreFromStorage = () => {
    if (!enablePersistence) return
    
    try {
      const stored = localStorage.getItem(storageKey)
      if (!stored) return
      
      const data = JSON.parse(stored)
      const now = Date.now()
      
      // 检查数据是否太旧（超过1天）
      if (now - data.timestamp > 24 * 60 * 60 * 1000) {
        localStorage.removeItem(storageKey)
        return
      }
      
      // 恢复缓存项
      data.cache.forEach(([key, item]: [string, CacheItem<T>]) => {
        if (!isExpired(item)) {
          cache.value.set(key, item)
        }
      })
      
      // 恢复统计
      stats.value = { ...stats.value, ...data.stats }
      
      // 重新计算内存使用
      stats.value.totalMemory = Array.from(cache.value.values())
        .reduce((total, item) => total + (item.size || 0), 0)
        
    } catch (error) {
      console.warn('Failed to restore cache from storage:', error)
      localStorage.removeItem(storageKey)
    }
  }

  // 缓存装饰器
  const cached = <Args extends any[], Return>(
    fn: (...args: Args) => Promise<Return>,
    keyGenerator?: (...args: Args) => string,
    ttl?: number
  ) => {
    return async (...args: Args): Promise<Return> => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)
      
      // 尝试从缓存获取
      const cached = get(key)
      if (cached !== null) {
        return cached as Return
      }
      
      // 执行函数并缓存结果
      const result = await fn(...args)
      set(key, result as UnwrapRef<T>, ttl)
      
      return result
    }
  }

  // 启动清理定时器
  const startCleanup = () => {
    if (cleanupTimer) return
    
    cleanupTimer = window.setInterval(() => {
      const cleaned = cleanupExpired()
      if (cleaned > 0) {
        console.log(`Cleaned up ${cleaned} expired cache items`)
      }
    }, cleanupInterval)
  }

  // 停止清理定时器
  const stopCleanup = () => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
      cleanupTimer = null
    }
  }

  // 初始化
  const init = () => {
    restoreFromStorage()
    startCleanup()
  }

  // 清理
  const cleanup = () => {
    stopCleanup()
    if (enablePersistence) {
      persistToStorage()
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  // 初始化缓存
  init()

  return {
    // 响应式数据
    cacheStats,
    
    // 基本操作
    set,
    get,
    has,
    remove,
    clear,
    keys,
    values,
    
    // 高级操作
    removeByTag,
    cached,
    evict,
    cleanupExpired,
    
    // 持久化
    persistToStorage,
    restoreFromStorage,
    
    // 生命周期
    init,
    cleanup,
    startCleanup,
    stopCleanup
  }
}

/**
 * HTTP缓存 Composable
 * 专门用于HTTP请求的缓存
 */
export function useHttpCache(config: CacheConfig = {}) {
  const cache = useCache<any>({
    ...config,
    defaultTTL: config.defaultTTL || 5 * 60 * 1000, // 5分钟
    storageKey: config.storageKey || 'http_cache'
  })

  // 生成请求缓存键
  const generateRequestKey = (url: string, options?: RequestInit): string => {
    const method = options?.method || 'GET'
    const headers = options?.headers || {}
    const body = options?.body
    
    return `${method}:${url}:${JSON.stringify({ headers, body })}`
  }

  // 缓存HTTP请求
  const cachedFetch = async (
    url: string,
    options?: RequestInit,
    ttl?: number
  ): Promise<Response> => {
    const key = generateRequestKey(url, options)
    
    // 只缓存GET请求
    if (!options?.method || options.method.toUpperCase() === 'GET') {
      const cached = cache.get(key)
      if (cached) {
        return new Response(JSON.stringify(cached.data), {
          status: cached.status,
          statusText: cached.statusText,
          headers: cached.headers
        })
      }
    }
    
    // 发起请求
    const response = await fetch(url, options)
    
    // 缓存成功的GET请求
    if (response.ok && (!options?.method || options.method.toUpperCase() === 'GET')) {
      const data = await response.clone().json()
      cache.set(key, {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      }, ttl)
    }
    
    return response
  }

  // 清除URL相关的缓存
  const clearUrlCache = (urlPattern: string | RegExp) => {
    const keys = cache.keys()
    const pattern = typeof urlPattern === 'string' ? new RegExp(urlPattern) : urlPattern
    
    let cleared = 0
    keys.forEach(key => {
      const [, url] = key.split(':', 2)
      if (pattern.test(url)) {
        cache.remove(key)
        cleared++
      }
    })
    
    return cleared
  }

  return {
    ...cache,
    cachedFetch,
    generateRequestKey,
    clearUrlCache
  }
}

/**
 * 图片缓存 Composable
 * 专门用于图片资源的缓存
 */
export function useImageCache(config: CacheConfig = {}) {
  const cache = useCache<string>({
    ...config,
    defaultTTL: config.defaultTTL || 30 * 60 * 1000, // 30分钟
    storageKey: config.storageKey || 'image_cache'
  })

  // 预加载图片
  const preloadImage = (url: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        cache.set(url, url)
        resolve(url)
      }
      img.onerror = reject
      img.src = url
    })
  }

  // 批量预加载
  const preloadImages = async (urls: string[]): Promise<string[]> => {
    const results = await Promise.allSettled(
      urls.map(url => preloadImage(url))
    )
    
    return results
      .filter((result): result is PromiseFulfilledResult<string> => result.status === 'fulfilled')
      .map(result => result.value)
  }

  // 检查图片是否已缓存
  const isImageCached = (url: string): boolean => {
    return cache.has(url)
  }

  return {
    ...cache,
    preloadImage,
    preloadImages,
    isImageCached
  }
}