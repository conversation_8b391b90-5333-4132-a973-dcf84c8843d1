<template>
    <nav class="nav-bar bg-surface-50 dark:bg-surface-800 border-b border-surface-200 dark:border-surface-700 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
         
            <!-- 桌面端导航 -->
            <div class="hidden md:flex h-14 items-center">
                <MegaMenu :model="mainNavItems" class="bg-transparent border-none" style="border-radius: 3rem">
                    <template #item="{ item }">
                        <router-link v-if="item.route" v-slot="{ href, navigate }" :to="item.route" custom>
                            <a v-ripple :href="href" @click="navigate" class="flex items-center p-3 text-surface-700 dark:text-surface-200 hover:text-primary dark:hover:text-primary transition-colors duration-200">
                                <i :class="[item.icon, 'mr-2']" />
                                <span>{{ item.label }}</span>
                            </a>
                        </router-link>
                        <a v-else v-ripple :href="item.url" :target="item.target" class="flex items-center p-3 text-surface-700 dark:text-surface-200 hover:text-primary dark:hover:text-primary transition-colors duration-200">
                            <i :class="[item.icon, 'mr-2']" />
                            <span>{{ item.label }}</span>
                        </a>
                    </template>
                </MegaMenu>
            </div>
            
            <!-- 移动端导航抽屉遮罩 -->
            <div 
                class="md:hidden fixed inset-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300"
                :class="{ 'opacity-100': mobileMenuOpen, 'opacity-0 pointer-events-none': !mobileMenuOpen }"
                @click="closeMobileMenu"
            ></div>
            
            <!-- 移动端导航抽屉 -->
            <div 
                class="md:hidden fixed inset-y-0 left-0 z-50 w-72 bg-surface-0 dark:bg-surface-900 shadow-lg transform transition-transform duration-300 ease-in-out"
                :class="{ 'translate-x-0': mobileMenuOpen, '-translate-x-full': !mobileMenuOpen }"
            >
                <div class="p-4 border-b border-surface-200 dark:border-surface-700 flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="h-8 w-8" viewBox="0 0 32 32" fill="none">
                            <defs>
                                <linearGradient id="drawerLogoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="var(--primary-500)" />
                                    <stop offset="100%" stop-color="var(--primary-700)" />
                                </linearGradient>
                            </defs>
                            <circle cx="16" cy="16" r="14" fill="url(#drawerLogoGradient)" />
                            <path d="M12 10l8 6-8 6V10z" fill="white" />
                        </svg>
                        <span class="ml-2 text-lg font-semibold text-surface-900 dark:text-surface-0">
                            {{ t('common.siteName') }}
                        </span>
                    </div>
                    <Button
                        icon="pi pi-times"
                        class="p-button-text p-button-rounded"
                        @click="closeMobileMenu"
                        aria-label="Close menu"
                    />
                </div>
                
                <!-- 移动端导航内容 -->
                <div class="p-3">
                    <template v-for="(item, index) in mainNavItems" :key="index">
                        <!-- 一级导航项 -->
                        <router-link 
                            v-if="item.route"    
                            :to="item.route" 
                            class="flex items-center px-4 py-3 text-base font-medium rounded-lg mb-1"
                            :class="[
                                isActiveRoute(item.route) 
                                    ? 'bg-primary-50 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300' 
                                    : 'text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-800'
                            ]"
                            @click="closeMobileMenu"
                        >
                            <i v-if="item.icon" :class="[item.icon, 'mr-3 text-lg']"></i>
                            {{ item.label }}
                        </router-link>
                        
                        <!-- 带子菜单的导航项 -->
                        <div v-else class="mb-1">
                            <div 
                                class="flex items-center justify-between px-4 py-3 text-base font-medium rounded-lg cursor-pointer text-surface-700 dark:text-surface-200 hover:bg-surface-100 dark:hover:bg-surface-800"
                                @click="toggleMobileSubmenu(index)"
                            >
                                <div class="flex items-center">
                                    <i v-if="item.icon" :class="[item.icon, 'mr-3 text-lg']"></i>
                                    {{ item.label }}
                                </div>
                                <i :class="['pi', expandedMobileMenus.includes(index) ? 'pi-chevron-down' : 'pi-chevron-right', 'transition-transform duration-200']"></i>
                            </div>
                            
                            <!-- 子菜单 -->
                            <div 
                                v-if="expandedMobileMenus.includes(index)" 
                                class="pl-4 ml-3 border-l border-surface-200 dark:border-surface-700 mt-1 space-y-1"
                            >
                                <template v-for="(subItem, subIndex) in item.items" :key="subIndex">
                                    <router-link 
                                        v-if="!subItem.separator" 
                                        :to="subItem.route || '/'" 
                                        class="flex items-center px-4 py-2 text-sm rounded-lg mb-1 text-surface-700 dark:text-surface-300 hover:bg-surface-100 dark:hover:bg-surface-800"
                                        @click="closeMobileMenu"
                                    >
                                        <i v-if="subItem.icon" :class="[subItem.icon, 'mr-2 text-surface-500 dark:text-surface-400']"></i>
                                        {{ subItem.label }}
                                    </router-link>
                                    <hr v-else class="my-2 border-surface-200 dark:border-surface-700" />
                                </template>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- 移动端设置区域 -->
                <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-surface-200 dark:border-surface-700">
                    <div class="flex items-center justify-around">
                        <Button icon="pi pi-user" class="p-button-rounded p-button-text" />
                        <NavThemeSelector />
                        <NavLanguageSelector />
                        <Button icon="pi pi-cog" class="p-button-rounded p-button-text" />
                    </div>
                </div>
            </div>
        </div>
    </nav>
</template> 

<script setup lang="ts">
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import Button from 'primevue/button';
import MegaMenu from 'primevue/megamenu';

import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

// i18n
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// 响应式数据
const mobileMenuOpen = ref(false);
const expandedMobileMenus = ref<number[]>([]);

// 主导航菜单项
const mainNavItems = [
    {
        label: t('common.home'),
        icon: 'pi pi-home',
        route: '/'
    },
    {
        label: t('common.videos'),
        icon: 'pi pi-video',
        route: '/videos'
    },
    {
        label: t('common.pictures'),
        icon: 'pi pi-images',
        route: '/pictures'
    },
    {
        label: t('common.comics'),
        icon: 'pi pi-book',
        route: '/comics'
    },
    {
        label: t('common.community'),
        icon: 'pi pi-users',
        route: '/community'
    },
    {
        label: t('common.more'),
        icon: 'pi pi-ellipsis-h',
        items: [
            {
                label: t('common.about'),
                icon: 'pi pi-info-circle',
                route: '/about'
            },
            {
                separator: true
            },
            {
                label: t('common.ebooks'),
                icon: 'pi pi-file-pdf',
                route: '/ebooks'
            }
        ]
    }
];

// 判断路由是否激活
const isActiveRoute = (path: string): boolean => {
    if (path === '/') {
        return route.path === '/';
    }
    return route.path.startsWith(path);
};

// 移动端子菜单切换
const toggleMobileSubmenu = (index: number) => {
    const position = expandedMobileMenus.value.indexOf(index);
    if (position === -1) {
        expandedMobileMenus.value.push(index);
    } else {
        expandedMobileMenus.value.splice(position, 1);
    }
};

// 关闭移动端菜单
const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
};

// 监听移动端菜单切换事件
const handleMobileMenuToggle = (event: CustomEvent) => {
    mobileMenuOpen.value = event.detail.isOpen;
};

onMounted(() => {
    window.addEventListener('mobile-menu-toggle', handleMobileMenuToggle as EventListener);
});

onBeforeUnmount(() => {
    window.removeEventListener('mobile-menu-toggle', handleMobileMenuToggle as EventListener);
});
</script>

<style scoped lang="scss">
.nav-bar {
    position: relative;
    z-index: 10;
}

:deep(.p-megamenu) {
    background: transparent;
    border: none;
    padding: 0;
    
    .p-megamenu-root-list {
        gap: 0.5rem;
        
        > .p-menuitem {
            > .p-menuitem-link {
                padding: 0.75rem 1rem;
                border-radius: 0.5rem;
                transition: all 0.2s ease;
                
                &:hover {
                    background-color: var(--surface-100);
                    
                    .dark & {
                        background-color: var(--surface-700);
                    }
                }
                
                .p-menuitem-icon {
                    color: inherit;
                }
                
                .p-menuitem-text {
                    color: inherit;
                }
            }
        }
    }
}
</style> 