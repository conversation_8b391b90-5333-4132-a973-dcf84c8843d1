import {request} from '../../request';
import type { ApiResponse, ApiRequest } from '@/types/https';
import type { BookItem, BookCategory, BookChapter } from '@/types/books';
import { pa } from 'element-plus/es/locale';

/**
 * 电子书参数接口
 */
interface BookParams {
  page: {
    pageNo: number;
    pageSize: number;
  };
  data?: {
    title?: string;
    author?: string;
    category_id?: string;
    status?: string | number;
    is_featured?: number;
    [key: string]: any;
  };
}

/**
 * 获取电子书列表
 * @param params 查询参数
 */
export function getBookList(params: BookParams) {
  return request({
    url: '/books/list',
    method: 'post',
    data: params
  })
}

/**
 * 获取电子书详情
 * @param id 电子书ID
 */
export function getBookDetail(id: string) {
  return request({
    url: `/books/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  })
}

/**
 * 创建电子书
 * @param data 电子书数据
 */
export function createBook(params: { data: any }) {
  return request({
    url: '/books/add',
    method: 'post',
    data: params
  })
}

/**
 * 更新电子书
 * @param data 电子书数据
 */
export function updateBook(params: { data: any }) {
  return request({
    url: '/books/update',
    method: 'post',
    data: params
  })
}

/**
 * 删除电子书
 * @param id 电子书ID
 */
export function deleteBook(id: string) {
  return request({
    url: `/books/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  })
}

/**
 * 更新电子书状态
 * @param id 电子书ID
 * @param status 状态
 */
export function updateBookStatus(id: string, status: string) {
  return request({
    url: '/books/update-status',
    method: 'post',
    data: { data: { "id": id, "status": status } }
  })
}

/**
 * 获取电子书章节列表
 * @param bookId 电子书ID
 * @param params 分页参数
 */
export function getBookChapterList(bookId: string, params: BookParams) {
  return request({
    url: '/books/chapters/list',
    method: 'post',
    data: {
      data: {
        ...params.data,
        book_id: bookId
      }
    }
  })
}

/**
 * 获取电子书章节详情
 * @param id 章节ID
 */
export function getBookChapterDetail(id: string) {
  return request({
    url: `/books/chapters/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  })
}

/**
 * 创建电子书章节
 * @param data 章节数据
 */
export function createBookChapter(params: { data: any }) {
  return request({
    url: '/books/chapters/add',
    method: 'post',
    data: params
  })
 
}

/**
 * 更新电子书章节
 * @param data 章节数据
 */
export function updateBookChapter(params: { data: any }) {
  return request({
    url: '/books/chapters/update',
    method: 'post',
    data: params
  })
}

/**
 * 删除电子书章节
 * @param id 章节ID
 */
export function deleteBookChapter(id: string) {
  return request({
    url: `/books/chapters/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  })
}

/**
 * 批量上传章节
 * @param bookId 电子书ID
 * @param chapters 章节数据
 */
export function batchUploadChapters(bookId: string, chapters: any[]) {
  return request({
    url: '/books/chapters/batch-upload',
    method: 'post',
    data: {
      data: {
        book_id: bookId,
        chapters: chapters
      }
    }
  })
}

/**
 * 调整章节顺序
 * @param chapterId 章节ID
 * @param newOrder 新序号
 */
export function reorderBookChapter(chapterId: string, newOrder: number) {
  return request({
    url: '/books/chapters/reorder',
    method: 'post',
    data: { data: { "id": chapterId, "chapter_number": newOrder } }
  })
}
