<template>
  <div class="interaction-buttons">
    <!-- 上一个视频下一个视频按钮 -->
    <div class="video-navigation">
      <!-- 上一个视频 -->
      <div class="action-item">
        <button 
          class="action-btn nav-btn"
          :disabled="!canGoPrevious"
          @click="$emit('previous')"
          title="上一个视频"
        >
          <el-icon><ArrowUp /></el-icon>
        </button>
      </div>
      
      <!-- 下一个视频 -->
      <div class="action-item">
        <button 
          class="action-btn nav-btn"
          :disabled="!canGoNext"
          @click="$emit('next')"
          title="下一个视频"
        >
         <el-icon><ArrowDown /></el-icon>
        </button>
      </div>
    </div>

    <!-- 创作者头像 -->
    <div class="action-item creator-avatar-item">
      <div class="creator-avatar-wrapper">
        <img 
          :src="getAvatarUrl(getCreatorAvatar())" 
          :alt="getCreatorName()"
          class="creator-avatar"
          @error="handleAvatarError"
        >
        <button 
          v-if="!isFollowing" 
          class="follow-btn"
          @click="$emit('follow')"
        >
          +
        </button>
      </div>
    </div>
    
    <!-- 点赞 -->
    <div class="action-item">
      <button 
        class="action-btn"
        :class="{ active: isLiked }"
        @click="$emit('like')"
      >
        <img src="@/assets/icons/like.svg" alt="点赞" />
      </button>
      <span class="action-count">{{ formatCount(getLikeCount()) }}</span>
    </div>

    <!-- 评论 -->
    <div class="action-item">
      <button class="action-btn" @click="$emit('comment')">
        <img src="@/assets/icons/comment.svg" alt="评论" />
      </button>
      <span class="action-count">{{ formatCount(getCommentCount()) }}</span>
    </div>

    <!-- 收藏 -->
    <div class="action-item">
      <button 
        class="action-btn"
        :class="{ active: isFavorited }"
        @click="$emit('favorite')"
      >
        <img src="@/assets/icons/favorite.svg" alt="收藏" />
      </button>
      <span class="action-count">{{ formatCount(getFavoriteCount()) }}</span>
    </div>

    <!-- 分享 -->
    <div class="action-item">
      <button class="action-btn" @click="$emit('share')">
        <img src="@/assets/icons/share.svg" alt="分享" />
      </button>
      <span class="action-count">{{ formatCount(getShareCount()) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ShortVideo } from '@/types/shorts'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { ref } from 'vue'

// 导入默认头像
import defaultAvatar from '@/assets/image/avatar_1.png'

// Props
interface Props {
  video: ShortVideo
  isLiked: boolean
  isFavorited: boolean
  isFollowing: boolean
  canGoPrevious: boolean
  canGoNext: boolean
}

const props = defineProps<Props>()

// Emits
defineEmits<{
  like: []
  comment: []
  favorite: []
  share: []
  follow: []
  previous: []
  next: []
}>()

// Methods
const formatCount = (count: number): string => {
  if (!count || isNaN(count)) return '0'
  if (count < 1000) return count.toString()
  if (count < 10000) return (count / 1000).toFixed(1) + 'k'
  if (count < 1000000) return Math.floor(count / 1000) + 'k'
  return (count / 1000000).toFixed(1) + 'm'
}

const getAvatarUrl = (avatar: string | null | undefined): string => {
  if (!avatar) return defaultAvatar
  // 如果是相对路径，直接返回
  if (avatar.startsWith('/') || avatar.startsWith('http')) {
    return avatar
  }
  // 如果是其他格式，返回默认头像
  return defaultAvatar
}

const handleAvatarError = (event: Event) => {
  const target = event.target as HTMLImageElement
  if (target) {
    target.src = defaultAvatar
  }
}

// 兼容驼峰和下划线命名的获取方法
const getLikeCount = (): number => {
  return props.video.likeCount || props.video.like_count || 0
}

const getCommentCount = (): number => {
  return props.video.commentCount || props.video.comment_count || 0
}

const getFavoriteCount = (): number => {
  return props.video.favoriteCount || props.video.favorite_count || 0
}

const getShareCount = (): number => {
  return props.video.shareCount || props.video.share_count || 0
}

const getCreatorAvatar = (): string => {
  return props.video.creatorAvatar || props.video.creator_avatar || ''
}

const getCreatorName = (): string => {
  return props.video.creatorName || props.video.creator_name || ''
}
</script>

<style scoped lang="scss">
.interaction-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  padding: 20px 0;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.action-btn {
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  padding: 12px;
  background: linear-gradient(180deg, #e0eafc, #cfdef3, #12c2e9, #cb8eea, #ed9da3, #e1d1a9);
}

.action-btn:hover {
  background: linear-gradient(180deg, #e0eafc, #cfdef3, #12c2e9, #cb8eea, #ed9da3, #e1d1a9);
  transform: scale(1.1);
}

.action-btn.active {
  background: #ff6b6b;
  color: white;
}

.action-btn img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
}

.action-btn.active img {
  filter: brightness(0) invert(1);
}

.action-count {
  font-size: 12px;
  color: white;
  font-weight: 600;
  text-align: center;
  min-width: 30px;
  padding: 0.2em;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

/* 创作者头像特殊样式 */
.creator-avatar-item {
  margin-top: 10px;
}

.creator-avatar-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.creator-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.follow-btn {
  position: absolute;
  bottom: 0px;
  right: 0px;
  width: 20px;
  height: 20px;
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 0.8em;
  border-radius: 50%;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.follow-btn:hover {
  background: #ff5252;
  transform: scale(1.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .interaction-buttons {
    gap: 16px;
    padding: 15px 0;
  }

  .video-navigation {
    gap: 10px;
    margin-bottom: 16px;
    padding-bottom: 16px;
  }

  .action-btn {
    width: 44px;
    height: 44px;
    padding: 10px;
  }

  .action-btn img {
    width: 22px;
    height: 22px;
  }

  .nav-btn img {
    width: 18px;
    height: 18px;
  }

  .action-count {
    font-size: 11px;
  }

  .creator-avatar {
    width: 44px;
    height: 44px;
  }

  .follow-btn {
    width: 18px;
    height: 18px;
    font-size: 12px;
  }
}

/* 视频导航按钮样式 */
.video-navigation {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-btn {
  background: linear-gradient(180deg, #e0eafc, #cfdef3, #12c2e9, #cb8eea, #ed9da3, #e1d1a9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  
  &:hover:not(:disabled) {
    background: linear-gradient(180deg, #e0eafc, #cfdef3, #12c2e9, #cb8eea, #ed9da3, #e1d1a9);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.15);
  }
  
  &:disabled {
    background: linear-gradient(180deg, rgba(85, 224, 234, 0.68), rgba(85, 94, 14, 0.68) c, rgba(85, 94, 14, 0.68) c, rgba(85, 94, 14, 0.68) c, rgba(85, 94, 14, 0.68) c, rgba(85, 94, 14, 0.68) c);
    border-color: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    opacity: 0.4;
    
    img {
      filter: brightness(0) invert(1) opacity(0.4);
    }
    
    &:hover {
      transform: none;
    }
  }
  
  img {
    width: 20px;
    height: 20px;
  }
}
</style> 