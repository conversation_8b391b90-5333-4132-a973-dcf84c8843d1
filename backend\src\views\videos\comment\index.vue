<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>视频评论管理</h2>
            <div class="buttons">
              <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
              <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 搜索栏组件 -->
      <CommentSearchBar
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
        @refresh="refreshList"
        class="mb-4"
      />

      <!-- 评论表格组件 -->
      <CommentTable
        :loading="loading"
        :comment-list="commentList"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @view-detail="handleViewDetail"
        @view-replies="handleViewReplies"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @batch-status="handleBatchStatus"
        @batch-delete="handleBatchDelete"
        @selection-change="handleSelectionChange"
      />
    </el-card>

    <!-- 评论详情对话框组件 -->
    <CommentDetailDialog
      v-model="dialogVisible"
      :comment="currentComment"
      @delete="handleDelete"
      @status-change="handleChangeStatus"
    />

    <!-- 回复列表对话框组件 -->
    <CommentRepliesDialog
      v-model="repliesDialogVisible"
      :loading="repliesLoading"
      :parent-comment="parentComment"
      :replies="replies"
      :current-page="repliesCurrentPage"
      :page-size="repliesPageSize"
      :total="repliesTotal"
      @delete-reply="handleDelete"
      @change-status="handleChangeStatus"
      @page-change="handleRepliesPageChange"
    />
  </div>
</template>

<script setup lang="ts">
import {
    batchDeleteComment,
    batchUpdateCommentStatus,
    deleteComment,
    getCommentReplies,
    getVideoCommentList,
    updateCommentStatus,
    type CommentItem,
    type CommentParams
} from '@/service/api/videos/comments';
import { handleApiError } from '@/utils/errorHandler';
import { Download, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入组件
import CommentDetailDialog from './components/CommentDetailDialog.vue';
import CommentRepliesDialog from './components/CommentRepliesDialog.vue';
import CommentSearchBar, { type SearchForm } from './components/CommentSearchBar.vue';
import CommentTable from './components/CommentTable.vue';

// 搜索表单
const searchForm = reactive<SearchForm>({
  videoId: '',
  userId: '',
  content: '',
  status: undefined,
  dateRange: undefined,
  created_at_start: undefined,
  created_at_end: undefined
});

// 状态变量
const loading = ref(false);
const commentList = ref<CommentItem[]>([]);
const selectedComments = ref<CommentItem[]>([]);
const dialogVisible = ref(false);
const currentComment = ref<CommentItem | null>(null);

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 回复相关变量
const repliesDialogVisible = ref(false);
const repliesLoading = ref(false);
const parentComment = ref<CommentItem | null>(null);
const replies = ref<CommentItem[]>([]);
const repliesCurrentPage = ref(1);
const repliesPageSize = ref(10);
const repliesTotal = ref(0);

// 生命周期钩子
onMounted(() => {
  fetchCommentList();
});

// 加载评论列表
const fetchCommentList = async () => {
  loading.value = true;
  try {
    const params: CommentParams = {
      page: {
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
      },
      data: {} as Record<string, any>
    };

    // 添加搜索条件
    if (searchForm.videoId) params.data!.videoId = searchForm.videoId;
    if (searchForm.userId) params.data!.userId = searchForm.userId;
    if (searchForm.content) params.data!.keyword = searchForm.content;
    if (searchForm.status !== undefined) params.data!.status = searchForm.status;
    if (searchForm.created_at_start) params.data!.created_at_start = searchForm.created_at_start;
    if (searchForm.created_at_end) params.data!.created_at_end = searchForm.created_at_end;
    console.log("params:",params);
    const { data, response } = await getVideoCommentList(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      commentList.value = data.list || [];
      pagination.total = data.total || 0;
    } else {
      handleApiError(response.message || '获取评论列表失败');
    }
  } catch (error) {
    handleApiError(error, '获取评论列表失败');
  } finally {
    loading.value = false;
  }
};

// 查看评论回复
const handleViewReplies = async (row: CommentItem) => {
  parentComment.value = row;
  repliesDialogVisible.value = true;
  repliesCurrentPage.value = 1;
  await fetchReplies();
};

// 加载回复列表
const fetchReplies = async () => {
  if (!parentComment.value) return;

  repliesLoading.value = true;
  try {
    const { data, response } = await getCommentReplies(
      parentComment.value.id,
      repliesCurrentPage.value,
      repliesPageSize.value
    ) as any;

    if (response.status === 200 && response.data.code === 2000) {
      replies.value = data.list || [];
      repliesTotal.value = data.total || 0;
    } else {
      handleApiError(response.message || '获取回复列表失败');
    }
  } catch (error) {
    handleApiError(error, '获取回复列表失败');
  } finally {
    repliesLoading.value = false;
  }
};

// 处理回复分页变化
const handleRepliesPageChange = (page: number) => {
  repliesCurrentPage.value = page;
  fetchReplies();
};

// 搜索评论
const handleSearch = (params: any) => {
  Object.assign(searchForm, params);
  pagination.page = 1;
  fetchCommentList();
};

// 重置搜索表单
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
        if (key === 'dateRange') {
            searchForm[key as keyof SearchForm] = undefined as never;
        } else {
            searchForm[key as keyof SearchForm] = undefined as never;
        }
    });
  pagination.page = 1;
  fetchCommentList();
};

// 刷新列表
const refreshList = () => {
  fetchCommentList();
};

// 处理选择变化
const handleSelectionChange = (selection: CommentItem[]) => {
  selectedComments.value = selection;
};

// 处理分页大小变化
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.page = 1;
  fetchCommentList();
};

// 处理分页当前页变化
const handleCurrentChange = (val: number) => {
  pagination.page = val;
  fetchCommentList();
};

// 查看评论详情
const handleViewDetail = (row: CommentItem) => {
  currentComment.value = row;
  dialogVisible.value = true;
};

// 更改评论状态
const handleChangeStatus = async (id: string, status: number) => {
  try {
    const { response } = await updateCommentStatus(id, status) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('状态更新成功');
      
      // 更新当前评论列表
      fetchCommentList();
      
      // 如果是在回复对话框中操作，也更新回复列表
      if (repliesDialogVisible.value) {
        fetchReplies();
      }
    } else {
      handleApiError(response.message || '状态更新失败');
    }
  } catch (error) {
    handleApiError(error, '状态更新失败');
  }
};

// 批量更新状态
const handleBatchStatus = async (status: number, comments: CommentItem[]) => {
  if (!comments || comments.length === 0) {
    ElMessage.warning('请先选择要操作的评论');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要${status === 1 ? '显示' : '隐藏'}选中的 ${comments.length} 条评论吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const { response } = await batchUpdateCommentStatus({
      ids: comments.map(comment => comment.id),
      status
    }) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success(`批量${status === 1 ? '显示' : '隐藏'}成功`);
      fetchCommentList();
    } else {
      handleApiError(response.message || '批量操作失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      handleApiError(error, '批量操作失败');
    }
  }
};

// 批量删除
const handleBatchDelete = async (comments: CommentItem[]) => {
  if (!comments || comments.length === 0) {
    ElMessage.warning('请先选择要删除的评论');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${comments.length} 条评论吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const { response } = await batchDeleteComment({
      ids: comments.map(comment => comment.id)
    }) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('批量删除成功');
      fetchCommentList();
    } else {
      handleApiError(response.message || '批量删除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      handleApiError(error, '批量删除失败');
    }
  }
};

// 删除评论
const handleDelete = async (row: CommentItem | null) => {
  if (!row) return;

  try {
    await ElMessageBox.confirm(
      '确定要删除此评论吗？此操作不可恢复！',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const { response } = await deleteComment(row.id) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      ElMessage.success('评论已删除');

      // 关闭相关对话框
      if (dialogVisible.value && currentComment.value?.id === row.id) {
        dialogVisible.value = false;
      }

      if (repliesDialogVisible.value) {
        // 如果删除的是父评论，关闭回复对话框
        if (parentComment.value?.id === row.id) {
          repliesDialogVisible.value = false;
        } else {
          // 如果删除的是回复，重新加载回复列表
          fetchReplies();
        }
      }

      // 重新加载评论列表
      fetchCommentList();
    } else {
      handleApiError(response.message || '删除评论失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      handleApiError(error, '删除评论失败');
    }
  }
};

// 导出评论
const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.title-container h2 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.mb-4 {
  margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .title-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    justify-content: center;
  }
}
</style>
