package social

import (
	"errors"
	"time"

	collectTypes "frontapi/internal/service/base/extcollect/types"
	followTypes "frontapi/internal/service/base/extfollow/types"
	"frontapi/internal/service/base/extlike/types"
)

// 错误定义
var (
	ErrAlreadyRunning   = errors.New("social service manager is already running")
	ErrNotRunning       = errors.New("social service manager is not running")
	ErrMetricsDisabled  = errors.New("metrics collection is disabled")
	ErrSyncDisabled     = errors.New("sync is disabled")
	ErrEventBusDisabled = errors.New("event bus is disabled")
	ErrServiceNotFound  = errors.New("service not found")
	ErrInvalidConfig    = errors.New("invalid configuration")
)

// SocialMetrics 社交服务总体指标
type SocialMetrics struct {
	Timestamp time.Time `json:"timestamp"`

	// 各服务指标
	LikeMetrics    *types.ServiceMetrics        `json:"like_metrics,omitempty"`
	CollectMetrics *collectTypes.ServiceMetrics `json:"collect_metrics,omitempty"`
	FollowMetrics  *followTypes.ServiceMetrics  `json:"follow_metrics,omitempty"`

	// 总体统计
	TotalOperations     int64   `json:"total_operations"`
	TotalErrors         int64   `json:"total_errors"`
	AverageLatency      float64 `json:"average_latency_ms"`
	OperationsPerSecond float64 `json:"operations_per_second"`
	ErrorRate           float64 `json:"error_rate"`

	// 资源使用情况
	MemoryUsage  int64   `json:"memory_usage_bytes"`
	CPUUsage     float64 `json:"cpu_usage_percent"`
	CacheHitRate float64 `json:"cache_hit_rate"`

	// 业务指标
	ActiveUsers  int64         `json:"active_users"`
	PopularItems []PopularItem `json:"popular_items"`
	TrendingTags []string      `json:"trending_tags"`
}

// PopularItem 热门项目
type PopularItem struct {
	ItemID   string  `json:"item_id"`
	ItemType string  `json:"item_type"`
	Score    float64 `json:"score"`
	Title    string  `json:"title,omitempty"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Timestamp     time.Time                `json:"timestamp"`
	OverallStatus string                   `json:"overall_status"` // healthy, degraded, unhealthy
	Services      map[string]ServiceHealth `json:"services"`
}

// ServiceHealth 单个服务健康状态
type ServiceHealth struct {
	Status    string    `json:"status"` // healthy, unhealthy
	Error     string    `json:"error,omitempty"`
	LastCheck time.Time `json:"last_check"`
	Uptime    string    `json:"uptime,omitempty"`
}

// Event 事件基础接口
type Event interface {
	GetEventType() string
	GetEventTime() time.Time
	GetEventData() map[string]interface{}
	ToJSON() ([]byte, error)
}

// SocialEvent 社交事件
type SocialEvent struct {
	Type     string                 `json:"type"`
	Time     time.Time              `json:"time"`
	UserID   string                 `json:"user_id"`
	ItemID   string                 `json:"item_id"`
	ItemType string                 `json:"item_type"`
	Action   string                 `json:"action"`
	Data     map[string]interface{} `json:"data,omitempty"`
}

func (e *SocialEvent) GetEventType() string {
	return e.Type
}

func (e *SocialEvent) GetEventTime() time.Time {
	return e.Time
}

func (e *SocialEvent) GetEventData() map[string]interface{} {
	return e.Data
}

func (e *SocialEvent) ToJSON() ([]byte, error) {
	// Implementation here
	return nil, nil
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	TotalCount   int              `json:"total_count"`
	SuccessCount int              `json:"success_count"`
	FailureCount int              `json:"failure_count"`
	Errors       []OperationError `json:"errors,omitempty"`
	ProcessedAt  time.Time        `json:"processed_at"`
	Duration     time.Duration    `json:"duration"`
}

// OperationError 操作错误
type OperationError struct {
	ItemID    string `json:"item_id"`
	ItemType  string `json:"item_type,omitempty"`
	UserID    string `json:"user_id,omitempty"`
	Error     string `json:"error"`
	ErrorCode string `json:"error_code,omitempty"`
}

// SyncStatus 同步状态
type SyncStatus struct {
	LastSyncTime time.Time `json:"last_sync_time"`
	IsRunning    bool      `json:"is_running"`
	SyncedItems  int64     `json:"synced_items"`
	FailedItems  int64     `json:"failed_items"`
	NextSyncTime time.Time `json:"next_sync_time"`
	Enabled      bool      `json:"enabled"`
}

// CrossServiceData 跨服务数据
type CrossServiceData struct {
	UserID       string                 `json:"user_id"`
	ItemID       string                 `json:"item_id"`
	ItemType     string                 `json:"item_type"`
	IsLiked      bool                   `json:"is_liked"`
	IsCollected  bool                   `json:"is_collected"`
	IsFollowed   bool                   `json:"is_followed"`
	LikeCount    int64                  `json:"like_count"`
	CollectCount int64                  `json:"collect_count"`
	FollowCount  int64                  `json:"follow_count"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// RecommendationData 推荐数据
type RecommendationData struct {
	UserID           string            `json:"user_id"`
	RecommendedItems []RecommendedItem `json:"recommended_items"`
	Algorithm        string            `json:"algorithm"`
	Confidence       float64           `json:"confidence"`
	GeneratedAt      time.Time         `json:"generated_at"`
}

// RecommendedItem 推荐项目
type RecommendedItem struct {
	ItemID    string  `json:"item_id"`
	ItemType  string  `json:"item_type"`
	Score     float64 `json:"score"`
	Reason    string  `json:"reason"`
	Title     string  `json:"title,omitempty"`
	Thumbnail string  `json:"thumbnail,omitempty"`
}

// AnalyticsData 分析数据
type AnalyticsData struct {
	Period    string                 `json:"period"` // daily, weekly, monthly
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
	Metrics   map[string]interface{} `json:"metrics"`
}

// UserBehaviorPattern 用户行为模式
type UserBehaviorPattern struct {
	UserID          string                 `json:"user_id"`
	PreferredTypes  []string               `json:"preferred_types"`
	ActiveHours     []int                  `json:"active_hours"`
	EngagementScore float64                `json:"engagement_score"`
	LastActivity    time.Time              `json:"last_activity"`
	Patterns        map[string]interface{} `json:"patterns"`
}

// ContentPopularity 内容热度
type ContentPopularity struct {
	ItemID       string    `json:"item_id"`
	ItemType     string    `json:"item_type"`
	LikeScore    float64   `json:"like_score"`
	CollectScore float64   `json:"collect_score"`
	ShareScore   float64   `json:"share_score"`
	TotalScore   float64   `json:"total_score"`
	Trending     bool      `json:"trending"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Timestamp        time.Time `json:"timestamp"`
	ThroughputQPS    float64   `json:"throughput_qps"`
	AverageLatencyMs float64   `json:"average_latency_ms"`
	P95LatencyMs     float64   `json:"p95_latency_ms"`
	P99LatencyMs     float64   `json:"p99_latency_ms"`
	ErrorRate        float64   `json:"error_rate"`
	CacheHitRate     float64   `json:"cache_hit_rate"`
	MemoryUsageMB    float64   `json:"memory_usage_mb"`
	CPUUsagePercent  float64   `json:"cpu_usage_percent"`
}
