package pictures

import (
	"context"
	"errors"

	"frontapi/internal/models/pictures"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// PictureCategoryRepository 图片分类数据访问接口
type PictureCategoryRepository interface {
	base.ExtendedRepository[pictures.PictureCategory]

	// 扩展方法
	FindByName(ctx context.Context, name string) (*pictures.PictureCategory, error)
	GetCategoryList(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureCategory, int64, error)
}

// pictureCategoryRepository 图片分类数据访问实现
type pictureCategoryRepository struct {
	base.ExtendedRepository[pictures.PictureCategory]
}

// NewPictureCategoryRepository 创建图片分类仓库实例
func NewPictureCategoryRepository(db *gorm.DB) PictureCategoryRepository {
	return &pictureCategoryRepository{
		ExtendedRepository: base.NewExtendedRepository[pictures.PictureCategory](db),
	}
}

// FindByName 根据名称查找图片分类
func (r *pictureCategoryRepository) FindByName(ctx context.Context, name string) (*pictures.PictureCategory, error) {
	var category pictures.PictureCategory
	err := r.GetDBWithContext(ctx).Where("name = ?", name).First(&category).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("图片分类不存在")
		}
		return nil, err
	}
	return &category, nil
}

// Delete 删除图片分类
func (r *pictureCategoryRepository) Delete(ctx context.Context, id string) error {
	// 先检查是否有关联的图片
	var count int64
	err := r.GetDBWithContext(ctx).Model(&pictures.Picture{}).Where("category_id = ?", id).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该分类下有图片，无法删除")
	}

	return r.GetDBWithContext(ctx).Where("id = ?", id).Delete(&pictures.PictureCategory{}).Error
}

// GetCategoryList 根据条件获取分类列表
func (r *pictureCategoryRepository) GetCategoryList(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*pictures.PictureCategory, int64, error) {
	var (
		categories []*pictures.PictureCategory
		total      int64
	)

	query := r.GetDBWithContext(ctx).Model(&pictures.PictureCategory{})

	// 应用查询条件
	if name, ok := condition["name"].(string); ok && name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	if status, ok := condition["status"].(int); ok && status > -999 {
		query = query.Where("status = ?", status)
	}

	// 统计总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&categories).Error
	if err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}
