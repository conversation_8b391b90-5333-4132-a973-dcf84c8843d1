package pictures

import (
	"frontapi/internal/models/pictures"
	repo "frontapi/internal/repository/pictures"
	"frontapi/internal/service/base"
)

// CreateCollectionRequest 创建收藏请求
type CreateCollectionRequest struct {
	UserID          string `json:"user_id" validate:"required"`
	PictureID       string `json:"picture_id" validate:"required"`
	CollectionGroup string `json:"collection_group"`
	Note            string `json:"note"`
}

// UpdateCollectionRequest 更新收藏请求
type UpdateCollectionRequest struct {
	CollectionGroup string `json:"collection_group"`
	Note            string `json:"note"`
}

// PictureCollectionService 图片收藏服务接口
type PictureCollectionService interface {
	base.IExtendedService[pictures.UserPictureCollection]
}

// pictureCollectionService 图片收藏服务实现
type pictureCollectionService struct {
	*base.ExtendedService[pictures.UserPictureCollection]
	collectionRepo repo.UserPictureCollectionRepository
	pictureRepo    repo.PictureRepository
	albumRepo      repo.PictureAlbumRepository
}

// NewPictureCollectionService 创建图片收藏服务实例
func NewPictureCollectionService(
	collectionRepo repo.UserPictureCollectionRepository,
	pictureRepo repo.PictureRepository,
	albumRepo repo.PictureAlbumRepository,
) PictureCollectionService {
	return &pictureCollectionService{
		ExtendedService: base.NewExtendedService[pictures.UserPictureCollection](collectionRepo, "user_picture_collection"),
		collectionRepo:  collectionRepo,
		pictureRepo:     pictureRepo,
		albumRepo:       albumRepo,
	}
}
