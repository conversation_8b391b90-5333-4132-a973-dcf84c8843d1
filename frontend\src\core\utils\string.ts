/**
 * 字符串工具函数
 */

/**
 * 字符串格式化选项
 */
export interface StringFormatOptions {
  trim?: boolean // 是否去除首尾空格
  lowercase?: boolean // 是否转换为小写
  uppercase?: boolean // 是否转换为大写
  capitalize?: boolean // 是否首字母大写
  removeExtraSpaces?: boolean // 是否移除多余空格
  removeSpecialChars?: boolean // 是否移除特殊字符
  maxLength?: number // 最大长度
  ellipsis?: string // 超长省略符
}

/**
 * 字符串验证规则
 */
export interface StringValidationRule {
  required?: boolean // 是否必填
  minLength?: number // 最小长度
  maxLength?: number // 最大长度
  pattern?: RegExp // 正则表达式
  custom?: (value: string) => boolean // 自定义验证函数
  message?: string // 错误消息
}

/**
 * 字符串验证结果
 */
export interface StringValidationResult {
  valid: boolean
  errors: string[]
}

/**
 * 字符串相似度选项
 */
export interface SimilarityOptions {
  caseSensitive?: boolean // 是否区分大小写
  ignoreSpaces?: boolean // 是否忽略空格
  algorithm?: 'levenshtein' | 'jaro' | 'jaccard' // 算法类型
}

/**
 * 字符串工具类
 */
export class StringUtils {
  /**
   * 检查是否为字符串
   */
  static isString(value: any): value is string {
    return typeof value === 'string'
  }

  /**
   * 检查是否为空字符串
   */
  static isEmpty(value: any): boolean {
    return !this.isString(value) || value.length === 0
  }

  /**
   * 检查是否为空白字符串
   */
  static isBlank(value: any): boolean {
    return !this.isString(value) || value.trim().length === 0
  }

  /**
   * 检查是否不为空
   */
  static isNotEmpty(value: any): value is string {
    return this.isString(value) && value.length > 0
  }

  /**
   * 检查是否不为空白
   */
  static isNotBlank(value: any): value is string {
    return this.isString(value) && value.trim().length > 0
  }

  /**
   * 安全转换为字符串
   */
  static toString(value: any, defaultValue = ''): string {
    if (this.isString(value)) {
      return value
    }
    
    if (value === null || value === undefined) {
      return defaultValue
    }
    
    if (typeof value === 'object') {
      try {
        return JSON.stringify(value)
      } catch {
        return defaultValue
      }
    }
    
    return String(value)
  }

  /**
   * 格式化字符串
   */
  static format(value: string, options: StringFormatOptions = {}): string {
    if (!this.isString(value)) {
      return ''
    }

    let result = value

    // 去除首尾空格
    if (options.trim !== false) {
      result = result.trim()
    }

    // 移除多余空格
    if (options.removeExtraSpaces) {
      result = result.replace(/\s+/g, ' ')
    }

    // 移除特殊字符
    if (options.removeSpecialChars) {
      result = result.replace(/[^\w\s\u4e00-\u9fff]/g, '')
    }

    // 大小写转换
    if (options.lowercase) {
      result = result.toLowerCase()
    } else if (options.uppercase) {
      result = result.toUpperCase()
    } else if (options.capitalize) {
      result = this.capitalize(result)
    }

    // 长度限制
    if (options.maxLength && result.length > options.maxLength) {
      const ellipsis = options.ellipsis || '...'
      result = result.substring(0, options.maxLength - ellipsis.length) + ellipsis
    }

    return result
  }

  /**
   * 首字母大写
   */
  static capitalize(value: string): string {
    if (!this.isString(value) || value.length === 0) {
      return ''
    }
    
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase()
  }

  /**
   * 每个单词首字母大写
   */
  static capitalizeWords(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value.replace(/\b\w/g, char => char.toUpperCase())
  }

  /**
   * 驼峰命名转换
   */
  static toCamelCase(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value
      .replace(/[^a-zA-Z0-9\u4e00-\u9fff]+(.)/g, (_, char) => char.toUpperCase())
      .replace(/^[A-Z]/, char => char.toLowerCase())
  }

  /**
   * 帕斯卡命名转换
   */
  static toPascalCase(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    const camelCase = this.toCamelCase(value)
    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1)
  }

  /**
   * 蛇形命名转换
   */
  static toSnakeCase(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value
      .replace(/([A-Z])/g, '_$1')
      .replace(/[^a-zA-Z0-9\u4e00-\u9fff]+/g, '_')
      .replace(/^_+|_+$/g, '')
      .toLowerCase()
  }

  /**
   * 短横线命名转换
   */
  static toKebabCase(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value
      .replace(/([A-Z])/g, '-$1')
      .replace(/[^a-zA-Z0-9\u4e00-\u9fff]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .toLowerCase()
  }

  /**
   * 截断字符串
   */
  static truncate(value: string, length: number, ellipsis = '...'): string {
    if (!this.isString(value)) {
      return ''
    }
    
    if (value.length <= length) {
      return value
    }
    
    return value.substring(0, length - ellipsis.length) + ellipsis
  }

  /**
   * 按单词截断
   */
  static truncateWords(value: string, wordCount: number, ellipsis = '...'): string {
    if (!this.isString(value)) {
      return ''
    }
    
    const words = value.split(/\s+/)
    
    if (words.length <= wordCount) {
      return value
    }
    
    return words.slice(0, wordCount).join(' ') + ellipsis
  }

  /**
   * 填充字符串
   */
  static pad(value: string, length: number, padString = ' ', direction: 'left' | 'right' | 'both' = 'left'): string {
    if (!this.isString(value)) {
      return ''
    }
    
    if (value.length >= length) {
      return value
    }
    
    const padLength = length - value.length
    
    switch (direction) {
      case 'left':
        return padString.repeat(Math.ceil(padLength / padString.length)).substring(0, padLength) + value
      case 'right':
        return value + padString.repeat(Math.ceil(padLength / padString.length)).substring(0, padLength)
      case 'both':
        const leftPad = Math.floor(padLength / 2)
        const rightPad = padLength - leftPad
        return padString.repeat(Math.ceil(leftPad / padString.length)).substring(0, leftPad) +
               value +
               padString.repeat(Math.ceil(rightPad / padString.length)).substring(0, rightPad)
      default:
        return value
    }
  }

  /**
   * 左填充
   */
  static padLeft(value: string, length: number, padString = ' '): string {
    return this.pad(value, length, padString, 'left')
  }

  /**
   * 右填充
   */
  static padRight(value: string, length: number, padString = ' '): string {
    return this.pad(value, length, padString, 'right')
  }

  /**
   * 重复字符串
   */
  static repeat(value: string, count: number): string {
    if (!this.isString(value) || count < 0) {
      return ''
    }
    
    return value.repeat(count)
  }

  /**
   * 反转字符串
   */
  static reverse(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value.split('').reverse().join('')
  }

  /**
   * 随机打乱字符串
   */
  static shuffle(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    const chars = value.split('')
    
    for (let i = chars.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[chars[i], chars[j]] = [chars[j], chars[i]]
    }
    
    return chars.join('')
  }

  /**
   * 计算字符串长度（支持中文）
   */
  static length(value: string, countChinese = true): number {
    if (!this.isString(value)) {
      return 0
    }
    
    if (!countChinese) {
      return value.length
    }
    
    // 中文字符按2个字符计算
    return value.replace(/[\u4e00-\u9fff]/g, '**').length
  }

  /**
   * 计算字节长度
   */
  static byteLength(value: string): number {
    if (!this.isString(value)) {
      return 0
    }
    
    return new Blob([value]).size
  }

  /**
   * 统计字符出现次数
   */
  static countChar(value: string, char: string): number {
    if (!this.isString(value) || !this.isString(char) || char.length === 0) {
      return 0
    }
    
    return (value.match(new RegExp(this.escapeRegExp(char), 'g')) || []).length
  }

  /**
   * 统计单词数量
   */
  static countWords(value: string): number {
    if (!this.isString(value)) {
      return 0
    }
    
    return value.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  /**
   * 统计行数
   */
  static countLines(value: string): number {
    if (!this.isString(value)) {
      return 0
    }
    
    return value.split(/\r\n|\r|\n/).length
  }

  /**
   * 检查是否包含子字符串
   */
  static contains(value: string, searchString: string, caseSensitive = true): boolean {
    if (!this.isString(value) || !this.isString(searchString)) {
      return false
    }
    
    if (!caseSensitive) {
      value = value.toLowerCase()
      searchString = searchString.toLowerCase()
    }
    
    return value.includes(searchString)
  }

  /**
   * 检查是否以指定字符串开始
   */
  static startsWith(value: string, searchString: string, caseSensitive = true): boolean {
    if (!this.isString(value) || !this.isString(searchString)) {
      return false
    }
    
    if (!caseSensitive) {
      value = value.toLowerCase()
      searchString = searchString.toLowerCase()
    }
    
    return value.startsWith(searchString)
  }

  /**
   * 检查是否以指定字符串结束
   */
  static endsWith(value: string, searchString: string, caseSensitive = true): boolean {
    if (!this.isString(value) || !this.isString(searchString)) {
      return false
    }
    
    if (!caseSensitive) {
      value = value.toLowerCase()
      searchString = searchString.toLowerCase()
    }
    
    return value.endsWith(searchString)
  }

  /**
   * 查找所有匹配位置
   */
  static findAll(value: string, searchString: string, caseSensitive = true): number[] {
    if (!this.isString(value) || !this.isString(searchString) || searchString.length === 0) {
      return []
    }
    
    const positions: number[] = []
    let searchValue = value
    let searchTarget = searchString
    
    if (!caseSensitive) {
      searchValue = value.toLowerCase()
      searchTarget = searchString.toLowerCase()
    }
    
    let index = searchValue.indexOf(searchTarget)
    
    while (index !== -1) {
      positions.push(index)
      index = searchValue.indexOf(searchTarget, index + 1)
    }
    
    return positions
  }

  /**
   * 替换所有匹配项
   */
  static replaceAll(value: string, searchValue: string | RegExp, replaceValue: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    if (typeof searchValue === 'string') {
      return value.split(searchValue).join(replaceValue)
    }
    
    return value.replace(searchValue, replaceValue)
  }

  /**
   * 移除指定字符串
   */
  static remove(value: string, removeValue: string | RegExp): string {
    return this.replaceAll(value, removeValue, '')
  }

  /**
   * 移除HTML标签
   */
  static removeHtml(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value.replace(/<[^>]*>/g, '')
  }

  /**
   * 转义HTML字符
   */
  static escapeHtml(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    const htmlEscapes: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#39;'
    }
    
    return value.replace(/[&<>"']/g, char => htmlEscapes[char])
  }

  /**
   * 反转义HTML字符
   */
  static unescapeHtml(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    const htmlUnescapes: Record<string, string> = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'"
    }
    
    return value.replace(/&(?:amp|lt|gt|quot|#39);/g, entity => htmlUnescapes[entity])
  }

  /**
   * 转义正则表达式字符
   */
  static escapeRegExp(value: string): string {
    if (!this.isString(value)) {
      return ''
    }
    
    return value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  /**
   * 生成随机字符串
   */
  static random(length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
    if (length <= 0 || !this.isString(charset) || charset.length === 0) {
      return ''
    }
    
    let result = ''
    
    for (let i = 0; i < length; i++) {
      result += charset.charAt(Math.floor(Math.random() * charset.length))
    }
    
    return result
  }

  /**
   * 生成随机字母字符串
   */
  static randomAlpha(length = 8, uppercase = false): string {
    const charset = uppercase ? 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' : 'abcdefghijklmnopqrstuvwxyz'
    return this.random(length, charset)
  }

  /**
   * 生成随机数字字符串
   */
  static randomNumeric(length = 8): string {
    return this.random(length, '0123456789')
  }

  /**
   * 生成随机字母数字字符串
   */
  static randomAlphanumeric(length = 8): string {
    return this.random(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789')
  }

  /**
   * 生成UUID
   */
  static uuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }

  /**
   * 生成短ID
   */
  static shortId(length = 8): string {
    return this.random(length, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789')
  }

  /**
   * 字符串模板替换
   */
  static template(template: string, data: Record<string, any>, options: { prefix?: string; suffix?: string } = {}): string {
    if (!this.isString(template) || !data || typeof data !== 'object') {
      return template || ''
    }
    
    const { prefix = '{{', suffix = '}}' } = options
    const pattern = new RegExp(`${this.escapeRegExp(prefix)}\\s*([^${this.escapeRegExp(suffix)}]+)\\s*${this.escapeRegExp(suffix)}`, 'g')
    
    return template.replace(pattern, (match, key) => {
      const value = data[key.trim()]
      return value !== undefined ? this.toString(value) : match
    })
  }

  /**
   * 字符串插值
   */
  static interpolate(template: string, ...args: any[]): string {
    if (!this.isString(template)) {
      return ''
    }
    
    return template.replace(/{(\d+)}/g, (match, index) => {
      const argIndex = parseInt(index, 10)
      return argIndex < args.length ? this.toString(args[argIndex]) : match
    })
  }

  /**
   * 字符串验证
   */
  static validate(value: string, rules: StringValidationRule[]): StringValidationResult {
    const errors: string[] = []
    
    for (const rule of rules) {
      // 必填验证
      if (rule.required && this.isBlank(value)) {
        errors.push(rule.message || '此字段为必填项')
        continue
      }
      
      // 如果值为空且不是必填，跳过其他验证
      if (this.isBlank(value) && !rule.required) {
        continue
      }
      
      // 最小长度验证
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        errors.push(rule.message || `最小长度为 ${rule.minLength} 个字符`)
      }
      
      // 最大长度验证
      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        errors.push(rule.message || `最大长度为 ${rule.maxLength} 个字符`)
      }
      
      // 正则表达式验证
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(rule.message || '格式不正确')
      }
      
      // 自定义验证
      if (rule.custom && !rule.custom(value)) {
        errors.push(rule.message || '验证失败')
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 计算字符串相似度（Levenshtein距离）
   */
  static similarity(str1: string, str2: string, options: SimilarityOptions = {}): number {
    if (!this.isString(str1) || !this.isString(str2)) {
      return 0
    }
    
    const {
      caseSensitive = true,
      ignoreSpaces = false,
      algorithm = 'levenshtein'
    } = options
    
    let s1 = str1
    let s2 = str2
    
    if (!caseSensitive) {
      s1 = s1.toLowerCase()
      s2 = s2.toLowerCase()
    }
    
    if (ignoreSpaces) {
      s1 = s1.replace(/\s/g, '')
      s2 = s2.replace(/\s/g, '')
    }
    
    switch (algorithm) {
      case 'levenshtein':
        return this.levenshteinSimilarity(s1, s2)
      case 'jaro':
        return this.jaroSimilarity(s1, s2)
      case 'jaccard':
        return this.jaccardSimilarity(s1, s2)
      default:
        return this.levenshteinSimilarity(s1, s2)
    }
  }

  /**
   * Levenshtein相似度
   */
  private static levenshteinSimilarity(str1: string, str2: string): number {
    const len1 = str1.length
    const len2 = str2.length
    
    if (len1 === 0) return len2 === 0 ? 1 : 0
    if (len2 === 0) return 0
    
    const matrix: number[][] = []
    
    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j
    }
    
    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // 删除
          matrix[i][j - 1] + 1,     // 插入
          matrix[i - 1][j - 1] + cost // 替换
        )
      }
    }
    
    const maxLen = Math.max(len1, len2)
    return (maxLen - matrix[len1][len2]) / maxLen
  }

  /**
   * Jaro相似度
   */
  private static jaroSimilarity(str1: string, str2: string): number {
    const len1 = str1.length
    const len2 = str2.length
    
    if (len1 === 0 && len2 === 0) return 1
    if (len1 === 0 || len2 === 0) return 0
    
    const matchWindow = Math.floor(Math.max(len1, len2) / 2) - 1
    const str1Matches = new Array(len1).fill(false)
    const str2Matches = new Array(len2).fill(false)
    
    let matches = 0
    let transpositions = 0
    
    // 查找匹配字符
    for (let i = 0; i < len1; i++) {
      const start = Math.max(0, i - matchWindow)
      const end = Math.min(i + matchWindow + 1, len2)
      
      for (let j = start; j < end; j++) {
        if (str2Matches[j] || str1[i] !== str2[j]) continue
        str1Matches[i] = true
        str2Matches[j] = true
        matches++
        break
      }
    }
    
    if (matches === 0) return 0
    
    // 计算转置
    let k = 0
    for (let i = 0; i < len1; i++) {
      if (!str1Matches[i]) continue
      while (!str2Matches[k]) k++
      if (str1[i] !== str2[k]) transpositions++
      k++
    }
    
    return (matches / len1 + matches / len2 + (matches - transpositions / 2) / matches) / 3
  }

  /**
   * Jaccard相似度
   */
  private static jaccardSimilarity(str1: string, str2: string): number {
    const set1 = new Set(str1.split(''))
    const set2 = new Set(str2.split(''))
    
    const intersection = new Set([...set1].filter(x => set2.has(x)))
    const union = new Set([...set1, ...set2])
    
    return union.size === 0 ? 1 : intersection.size / union.size
  }

  /**
   * 字符串差异比较
   */
  static diff(str1: string, str2: string): Array<{ type: 'equal' | 'insert' | 'delete'; value: string }> {
    if (!this.isString(str1) || !this.isString(str2)) {
      return []
    }
    
    const len1 = str1.length
    const len2 = str2.length
    const matrix: number[][] = []
    
    // 初始化矩阵
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j
    }
    
    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        )
      }
    }
    
    // 回溯生成差异
    const result: Array<{ type: 'equal' | 'insert' | 'delete'; value: string }> = []
    let i = len1
    let j = len2
    
    while (i > 0 || j > 0) {
      if (i > 0 && j > 0 && str1[i - 1] === str2[j - 1]) {
        result.unshift({ type: 'equal', value: str1[i - 1] })
        i--
        j--
      } else if (j > 0 && (i === 0 || matrix[i][j - 1] <= matrix[i - 1][j])) {
        result.unshift({ type: 'insert', value: str2[j - 1] })
        j--
      } else if (i > 0) {
        result.unshift({ type: 'delete', value: str1[i - 1] })
        i--
      }
    }
    
    return result
  }

  /**
   * 字符串压缩
   */
  static compress(value: string): string {
    if (!this.isString(value) || value.length === 0) {
      return ''
    }
    
    let compressed = ''
    let count = 1
    
    for (let i = 1; i < value.length; i++) {
      if (value[i] === value[i - 1]) {
        count++
      } else {
        compressed += value[i - 1] + (count > 1 ? count : '')
        count = 1
      }
    }
    
    compressed += value[value.length - 1] + (count > 1 ? count : '')
    
    return compressed.length < value.length ? compressed : value
  }

  /**
   * 字符串解压缩
   */
  static decompress(value: string): string {
    if (!this.isString(value) || value.length === 0) {
      return ''
    }
    
    let decompressed = ''
    let i = 0
    
    while (i < value.length) {
      const char = value[i]
      let count = ''
      
      i++
      while (i < value.length && /\d/.test(value[i])) {
        count += value[i]
        i++
      }
      
      const repeatCount = count ? parseInt(count, 10) : 1
      decompressed += char.repeat(repeatCount)
    }
    
    return decompressed
  }
}

/**
 * 字符串格式化器类
 */
export class StringFormatter {
  private defaultOptions: StringFormatOptions

  constructor(defaultOptions: StringFormatOptions = {}) {
    this.defaultOptions = defaultOptions
  }

  /**
   * 格式化字符串
   */
  format(value: string, options: StringFormatOptions = {}): string {
    const mergedOptions = { ...this.defaultOptions, ...options }
    return StringUtils.format(value, mergedOptions)
  }

  /**
   * 批量格式化
   */
  formatBatch(values: string[], options: StringFormatOptions = {}): string[] {
    return values.map(value => this.format(value, options))
  }
}

/**
 * 字符串验证器类
 */
export class StringValidator {
  private rules: StringValidationRule[]

  constructor(rules: StringValidationRule[] = []) {
    this.rules = rules
  }

  /**
   * 添加验证规则
   */
  addRule(rule: StringValidationRule): this {
    this.rules.push(rule)
    return this
  }

  /**
   * 验证字符串
   */
  validate(value: string): StringValidationResult {
    return StringUtils.validate(value, this.rules)
  }

  /**
   * 批量验证
   */
  validateBatch(values: string[]): StringValidationResult[] {
    return values.map(value => this.validate(value))
  }
}

/**
 * 默认字符串格式化器实例
 */
export const stringFormatter = new StringFormatter()

/**
 * 默认字符串验证器实例
 */
export const stringValidator = new StringValidator()

/**
 * 创建字符串格式化器
 */
export function createStringFormatter(defaultOptions?: StringFormatOptions): StringFormatter {
  return new StringFormatter(defaultOptions)
}

/**
 * 创建字符串验证器
 */
export function createStringValidator(rules?: StringValidationRule[]): StringValidator {
  return new StringValidator(rules)
}

// 导出工具实例
export const stringUtils = StringUtils

// 导出快捷方法
export const {
  isString,
  isEmpty,
  isBlank,
  isNotEmpty,
  isNotBlank,
  toString,
  format,
  capitalize,
  capitalizeWords,
  toCamelCase,
  toPascalCase,
  toSnakeCase,
  toKebabCase,
  truncate,
  truncateWords,
  pad,
  padLeft,
  padRight,
  repeat,
  reverse,
  shuffle,
  length,
  byteLength,
  countChar,
  countWords,
  countLines,
  contains,
  startsWith,
  endsWith,
  findAll,
  replaceAll,
  remove,
  removeHtml,
  escapeHtml,
  unescapeHtml,
  escapeRegExp,
  random,
  randomAlpha,
  randomNumeric,
  randomAlphanumeric,
  uuid,
  shortId,
  template,
  interpolate,
  validate,
  similarity,
  diff,
  compress,
  decompress
} = StringUtils