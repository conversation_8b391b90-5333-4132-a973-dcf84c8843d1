package mongodb

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
)

// Factory MongoDB适配器工厂
type Factory struct{}

// NewFactory 创建工厂实例
func NewFactory() *Factory {
	return &Factory{}
}

// CreateAdapter 创建MongoDB适配器
func (f *Factory) CreateAdapter(config *MongoConfig, itemType string) (*MongoAdapter, error) {
	if config == nil {
		config = DefaultMongoConfig()
	}

	// 创建MongoDB连接
	client, database, err := f.createMongoConnection(config)
	if err != nil {
		return nil, fmt.Errorf("创建MongoDB连接失败: %w", err)
	}

	// 创建MongoDB客户端包装器
	mongoClient, err := NewMongoClient(client, database, config, itemType)
	if err != nil {
		return nil, fmt.Errorf("创建MongoDB客户端包装器失败: %w", err)
	}

	// 创建适配器
	adapter := &MongoAdapter{
		client:    mongoClient,
		likeOps:   NewLikeOperations(mongoClient),
		queryOps:  NewQueryOperations(mongoClient),
		rankOps:   NewRankingOperations(mongoClient),
		stats:     NewStats(mongoClient),
		connected: true,
	}

	return adapter, nil
}

// CreateAdapterWithConnection 使用现有连接创建适配器
func (f *Factory) CreateAdapterWithConnection(client *mongo.Client, database *mongo.Database, config *MongoConfig, itemType string) (*MongoAdapter, error) {
	if config == nil {
		config = DefaultMongoConfig()
	}

	// 创建MongoDB客户端包装器
	mongoClient, err := NewMongoClient(client, database, config, itemType)
	if err != nil {
		return nil, fmt.Errorf("创建MongoDB客户端包装器失败: %w", err)
	}

	// 创建适配器
	adapter := &MongoAdapter{
		client:    mongoClient,
		likeOps:   NewLikeOperations(mongoClient),
		queryOps:  NewQueryOperations(mongoClient),
		rankOps:   NewRankingOperations(mongoClient),
		stats:     NewStats(mongoClient),
		connected: true,
	}

	return adapter, nil
}

// createMongoConnection 创建MongoDB连接
func (f *Factory) createMongoConnection(config *MongoConfig) (*mongo.Client, *mongo.Database, error) {
	// 设置客户端选项
	clientOpts := options.Client().ApplyURI(config.URI)

	// 连接池设置
	clientOpts.SetMaxPoolSize(config.MaxPoolSize)
	clientOpts.SetMinPoolSize(config.MinPoolSize)
	clientOpts.SetMaxConnIdleTime(config.MaxIdleTime)
	clientOpts.SetConnectTimeout(config.ConnectTimeout)

	// 认证设置
	if config.Username != "" && config.Password != "" {
		credential := options.Credential{
			Username:   config.Username,
			Password:   config.Password,
			AuthSource: config.AuthSource,
		}
		clientOpts.SetAuth(credential)
	}

	// 读写偏好设置
	if config.ReadPreference != "" {
		readPref, err := f.parseReadPreference(config.ReadPreference)
		if err == nil {
			clientOpts.SetReadPreference(readPref)
		}
	}

	// 写关注设置
	if config.WriteConcern != "" {
		writeConcern, err := f.parseWriteConcern(config.WriteConcern)
		if err == nil {
			clientOpts.SetWriteConcern(writeConcern)
		}
	}

	// 读关注设置
	if config.ReadConcern != "" {
		readConcern, err := f.parseReadConcern(config.ReadConcern)
		if err == nil {
			clientOpts.SetReadConcern(readConcern)
		}
	}

	// 重试设置
	clientOpts.SetRetryWrites(config.RetryWrites)
	clientOpts.SetRetryReads(config.RetryReads)

	// 创建连接
	ctx, cancel := context.WithTimeout(context.Background(), config.ConnectTimeout)
	defer cancel()

	client, err := mongo.Connect(ctx, clientOpts)
	if err != nil {
		return nil, nil, fmt.Errorf("连接MongoDB失败: %w", err)
	}

	// 测试连接
	ctx, cancel = context.WithTimeout(context.Background(), config.Timeout)
	defer cancel()

	if err := client.Ping(ctx, nil); err != nil {
		client.Disconnect(context.Background())
		return nil, nil, fmt.Errorf("MongoDB连接测试失败: %w", err)
	}

	// 获取数据库
	database := client.Database(config.Database)

	return client, database, nil
}

// parseReadPreference 解析读偏好
func (f *Factory) parseReadPreference(pref string) (*readpref.ReadPref, error) {
	switch pref {
	case "primary":
		return readpref.Primary(), nil
	case "primaryPreferred":
		return readpref.PrimaryPreferred(), nil
	case "secondary":
		return readpref.Secondary(), nil
	case "secondaryPreferred":
		return readpref.SecondaryPreferred(), nil
	case "nearest":
		return readpref.Nearest(), nil
	default:
		return nil, fmt.Errorf("不支持的读偏好: %s", pref)
	}
}

// parseWriteConcern 解析写关注
func (f *Factory) parseWriteConcern(concern string) (*writeconcern.WriteConcern, error) {
	switch concern {
	case "majority":
		return writeconcern.New(writeconcern.WMajority()), nil
	case "1":
		return writeconcern.New(writeconcern.W(1)), nil
	case "0":
		return writeconcern.New(writeconcern.W(0)), nil
	default:
		return nil, fmt.Errorf("不支持的写关注: %s", concern)
	}
}

// parseReadConcern 解析读关注
func (f *Factory) parseReadConcern(concern string) (*readconcern.ReadConcern, error) {
	switch concern {
	case "local":
		return readconcern.Local(), nil
	case "available":
		return readconcern.Available(), nil
	case "majority":
		return readconcern.Majority(), nil
	case "linearizable":
		return readconcern.Linearizable(), nil
	case "snapshot":
		return readconcern.Snapshot(), nil
	default:
		return nil, fmt.Errorf("不支持的读关注: %s", concern)
	}
}

// CreateDefaultAdapter 创建默认配置的MongoDB适配器
func CreateDefaultAdapter(itemType string) (*MongoAdapter, error) {
	factory := NewFactory()
	config := DefaultMongoConfig()
	return factory.CreateAdapter(config, itemType)
}

// CreateAdapterFromURI 从URI创建MongoDB适配器
func CreateAdapterFromURI(uri, database, itemType string) (*MongoAdapter, error) {
	factory := NewFactory()
	config := DefaultMongoConfig()
	config.URI = uri
	config.Database = database
	return factory.CreateAdapter(config, itemType)
}

// NewMongoAdapter 创建MongoDB适配器 - 兼容旧接口
func NewMongoAdapter(client *mongo.Client, database *mongo.Database, config *MongoConfig) *MongoAdapter {
	factory := NewFactory()
	adapter, err := factory.CreateAdapterWithConnection(client, database, config, "default")
	if err != nil {
		// 如果出错，返回一个基本的适配器
		mongoClient, _ := NewMongoClient(client, database, config, "default")
		return &MongoAdapter{
			client:    mongoClient,
			connected: false,
		}
	}
	return adapter
}
