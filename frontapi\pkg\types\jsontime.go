package types

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"
)

type JSONTime time.Time

// 实现 driver.Valuer 接口
func (t JSONTime) Value() (driver.Value, error) {
	return time.Time(t), nil
}

// 实现 sql.Scanner 接口
func (t *JSONTime) Scan(value interface{}) error {
	if value == nil {
		*t = JSONTime(time.Time{})
		return nil
	}
	switch v := value.(type) {
	case time.Time:
		*t = JSONTime(v)
		return nil
	case []byte:
		parsedTime, err := time.Parse("2006-01-02 15:04:05", string(v))
		if err != nil {
			return err
		}
		*t = JSONTime(parsedTime)
		return nil
	case string:
		parsedTime, err := time.Parse("2006-01-02 15:04:05", v)
		if err != nil {
			return err
		}
		*t = JSONTime(parsedTime)
		return nil
	default:
		return fmt.Errorf("cannot scan type %T into JSONTime", value)
	}
}

// 实现 json.Marshaler 接口
func (t JSONTime) MarshalJSON() ([]byte, error) {
	formatted := time.Time(t).Format("2006-01-02 15:04:05")
	return json.Marshal(formatted)
}

// 实现 json.Unmarshaler 接口
func (t *JSONTime) UnmarshalJSON(data []byte) error {
	var str string
	if err := json.Unmarshal(data, &str); err != nil {
		return err
	}
	if str == "" {
		*t = JSONTime(time.Time{}) // 空字符串 → 空时间
		return nil
	}
	parsedTime, err := time.Parse("2006-01-02 15:04:05", str)
	if err != nil {
		return err
	}
	*t = JSONTime(parsedTime)
	return nil
}
