package comics

import (
	"frontapi/internal/models/comics"
	"frontapi/internal/typings"
	"strings"
)

func ConvertComicCategoryInfo(comic *comics.ComicCategory) ComicCategoryInfo {
	return ComicCategoryInfo{
		ID:          comic.ID,
		Name:        comic.Name.ValueOrZero(),
		Description: comic.Description.ValueOrZero(),
		Cover:       comic.Cover.ValueOrZero(),
		SortOrder:   comic.SortOrder,
		Status:      int(comic.Status),
	}
}

func ConvertComicCategoryList(comics []*comics.ComicCategory) []ComicCategoryInfo {
	result := make([]ComicCategoryInfo, 0)
	for _, comic := range comics {
		result = append(result, ConvertComicCategoryInfo(comic))
	}
	return result
}

func ConvertComicCategoryListResponse(comics []*comics.ComicCategory, total int64, pageNo int, pageSize int) ComicCategoryListResponse {
	return ComicCategoryListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		}, List: ConvertComicCategoryList(comics),
	}
}

func ConvertComicInfo(comic *comics.Comic) ComicInfo {
	return ComicInfo{
		ID:            comic.ID,
		Title:         comic.Title,
		Cover:         comic.Cover,
		Rating:        comic.Rating,
		Description:   comic.Description.ValueOrZero(),
		Author:        comic.Author,
		CategoryID:    comic.CategoryID.ValueOrZero(),
		CategoryName:  comic.CategoryName.ValueOrZero(),
		Progress:      comic.Progress,
		Tags:          strings.Join(comic.Tags, ","),
		Popularity:    int64(comic.Popularity),
		ChapterCount:  comic.ChapterCount,
		ReadCount:     int64(comic.ReadCount),
		LikeCount:     int64(comic.LikeCount),
		Score:         comic.Score,
		FavoriteCount: int64(comic.FavoriteCount),
		ShareCount:    int64(comic.ShareCount),
		CommentCount:  int64(comic.CommentCount),
		IsPaid:        comic.IsPaid == 1,
		IsFeatured:    comic.IsFeatured == 1,
		Price:         float64(comic.Price),
		Status:        int(comic.Status),
	}
}

func ConvertComicList(comics []*comics.Comic) []ComicInfo {
	result := make([]ComicInfo, 0)
	for _, comic := range comics {
		result = append(result, ConvertComicInfo(comic))
	}
	return result
}
func ConvertComicListResponse(comics []*comics.Comic, total int64, pageNo int, pageSize int) ComicListResponse {
	return ComicListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertComicList(comics),
	}
}

func ConvertComicChapterInfo(chapter *comics.ComicChapter) ComicChapterInfo {
	return ComicChapterInfo{
		ID:            chapter.GetID(),
		ComicID:       chapter.ComicID,
		Title:         chapter.Title,
		ChapterNumber: chapter.ChapterNumber,
		ReadCount:     int64(chapter.ReadCount),
		IsLocked:      chapter.IsLocked == 1,
		Price:         float64(chapter.Price),
		PageCount:     chapter.PageCount,
		Status:        int(chapter.Status),
	}
}
func ConvertComicChapterList(chapters []*comics.ComicChapter) []ComicChapterInfo {
	result := make([]ComicChapterInfo, 0)
	for _, chapter := range chapters {
		result = append(result, ConvertComicChapterInfo(chapter))
	}
	return result
}

func ConvertComicChapterListResponse(chapters []*comics.ComicChapter, total int64, pageNo int, pageSize int) ComicChapterListResponse {
	return ComicChapterListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		List: ConvertComicChapterList(chapters),
	}
}
func ConvertComicDetail(comic *comics.Comic) ComicDetail {
	return ComicDetail{
		ComicInfo: ConvertComicInfo(comic),
		Chapters:  ConvertComicChapterList(comic.Chapters),
	}
}
