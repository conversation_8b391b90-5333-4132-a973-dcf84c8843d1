package videos

import (
	"fmt"
	"frontapi/internal/models/users"
	"frontapi/internal/models/videos"
	"frontapi/internal/typings"
	"time"
)

// formatDuration 格式化时长（秒转换为 MM:SS 格式）
func formatDuration(seconds int) string {
	if seconds <= 0 {
		return "00:00"
	}
	minutes := seconds / 60
	secs := seconds % 60
	return fmt.Sprintf("%02d:%02d", minutes, secs)
}

// formatTime 格式化时间
func formatTime(t time.Time) string {
	return t.Format("2006-01-02 15:04:05")
}
func ConvertVideoInfo(video *videos.Video) VideoInfo {
	var creatorID, categoryID string
	if video.CreatorID.Valid {
		creatorID = video.CreatorID.String
	}
	if video.CategoryID.Valid {
		categoryID = video.CategoryID.String
	}

	var description string
	if video.Description.Valid {
		description = video.Description.String
	}

	return VideoInfo{
		ID:            video.ID,
		Title:         video.Title,
		Cover:         video.Cover,
		Description:   description,
		CategoryID:    categoryID,
		CategoryName:  video.CategoryName,
		CreatorID:     creatorID,
		CreatorName:   getCreatorName(video.Author),
		CreatorAvatar: get<PERSON><PERSON><PERSON><PERSON><PERSON>(video.Author),
		Duration:      int64(video.Duration),
		ViewCount:     video.ViewCount,
		LikeCount:     video.LikeCount,
		CommentCount:  video.CommentCount,
		Status:        video.Status,
		CreatedAt:     formatTime(time.Time(video.CreatedAt)),
		UpdatedAt:     formatTime(time.Time(video.UpdatedAt)),
		IsLiked:       video.IsLiked,
		IsFavorite:    video.IsFavorite,
	}
}

// getCreatorName 获取创作者名称
func getCreatorName(author *users.User) string {
	if author != nil && author.Nickname.Valid {
		return author.Nickname.String
	}
	return ""
}

// getCreatorAvatar 获取创作者头像
func getCreatorAvatar(author *users.User) string {
	if author != nil && author.Avatar.Valid {
		return author.Avatar.String
	}
	return ""
}

func ConvertVideoList(videos []*videos.Video) []VideoInfo {
	result := make([]VideoInfo, len(videos))
	for i, video := range videos {
		result[i] = ConvertVideoInfo(video)
	}
	return result
}

func ConvertVideoListResponse(videos []*videos.Video, total int64, page, size int) VideoListResponse {
	return VideoListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: ConvertVideoList(videos),
	}
}

func ConvertVideoCategoryInfo(category *videos.VideoCategory) VideoCategoryInfo {
	return VideoCategoryInfo{
		ID:          category.ID,
		Name:        category.Name.ValueOrZero(),
		Code:        category.Code,
		Icon:        category.Icon,
		Cover:       category.Image,
		Description: category.Description.ValueOrZero(),
		Sort:        category.SortOrder,
		// ViewCount:   category.ViewCount,
		// VideoCount:  category.VideoCount,
		IsFeatured: int8(category.IsFeatured),
		Status:     int8(category.Status),
		CreatedAt:  formatTime(time.Time(category.CreatedAt)),
	}
}

func ConvertVideoCategoryList(categories []*videos.VideoCategory) []VideoCategoryInfo {
	result := make([]VideoCategoryInfo, len(categories))
	for i, category := range categories {
		result[i] = ConvertVideoCategoryInfo(category)
	}
	return result
}

func ConvertVideoCategoryListResponse(categories []*videos.VideoCategory, total int64, page, size int) VideoCategoryListResponse {
	return VideoCategoryListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: ConvertVideoCategoryList(categories),
	}
}

func ConvertVideoAlbumInfo(album *videos.VideoAlbum) VideoAlbumInfo {
	var userID, cover, userNickname, userAvatar string

	if album.UserID.Valid {
		userID = album.UserID.String
	}
	if album.Cover.Valid {
		cover = album.Cover.String
	}
	if album.UserNickname.Valid {
		userNickname = album.UserNickname.String
	}
	if album.UserAvatar.Valid {
		userAvatar = album.UserAvatar.String
	}

	return VideoAlbumInfo{
		ID:            album.ID,
		Title:         album.Title,
		Description:   album.Description,
		Cover:         cover,
		CreatorID:     userID,
		CreatorName:   userNickname,
		CreatorAvatar: userAvatar,
		ViewCount:     album.ViewCount,
		VideoCount:    album.VideoCount,
		Heat:          album.Heat,
	}
}

func ConvertVideoAlbumList(albums []*videos.VideoAlbum) []VideoAlbumInfo {
	result := make([]VideoAlbumInfo, len(albums))
	for i, album := range albums {
		result[i] = ConvertVideoAlbumInfo(album)
	}
	return result
}

func ConvertVideoAlbumListResponse(albums []*videos.VideoAlbum, total int64, page, size int) VideoAlbumListResponse {
	return VideoAlbumListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: ConvertVideoAlbumList(albums),
	}
}
func ConvertVideoCommentListResponse(comments []*videos.VideoComment, total int64, page, size int) VideoCommentListResponse {
	return VideoCommentListResponse{
		BaseListResponse: typings.BaseListResponse{
			Total:    total,
			PageNo:   page,
			PageSize: size,
		},
		List: ConvertVideoCommentList(comments),
	}
}

func ConvertVideoAlbumVideoList(videos []*videos.Video) []VideoInfo {
	result := make([]VideoInfo, len(videos))
	for i, video := range videos {
		result[i] = ConvertVideoInfo(video)
	}
	return result
}

func ConvertVideoCommentList(comments []*videos.VideoComment) []VideoCommentInfo {
	result := make([]VideoCommentInfo, len(comments))
	for i, comment := range comments {
		result[i] = ConvertVideoCommentInfo(comment)
	}
	return result
}

func ConvertVideoCommentInfo(comment *videos.VideoComment) VideoCommentInfo {
	return VideoCommentInfo{
		Content:      comment.Content,
		UserNickname: comment.UserNickname,
		UserAvatar:   comment.UserAvatar,
		ParentID:     comment.ParentID.ValueOrZero(),
		EntityID:     comment.ID,
		EntityType:   1,
		RelationID:   comment.VideoID,
		Heat:         int64(*comment.Heat),
		LikeCount:    int64(*comment.LikeCount),
		ReplyCount:   int64(*comment.ReplyCount),
		CreatedAt:    formatTime(time.Time(comment.CreatedAt)),
		UpdatedAt:    formatTime(time.Time(comment.UpdatedAt)),
		Status:       comment.Status,
	}
}
