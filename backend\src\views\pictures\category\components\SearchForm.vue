<template>
    <div class="category-search-bar">
        <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
            <el-form-item label="名称/关键词">
                <el-input v-model="searchForm.keyword" placeholder="请输入分类名称或关键词" clearable
                    @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 180px;">
                    <el-option label="正常" :value="1" />
                    <el-option label="禁用" :value="0" />
                    <el-option label="已删除" :value="-4" />
                </el-select>
            </el-form-item>

            <el-form-item label="开始时间">
                <el-date-picker v-model="searchForm.start_date" type="date" placeholder="开始日期" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" style="width: 150px;" />
            </el-form-item>

            <el-form-item label="结束时间">
                <el-date-picker v-model="searchForm.end_date" type="date" placeholder="结束日期" format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" style="width: 150px;" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">
                    <el-icon>
                        <Search />
                    </el-icon>
                    搜索
                </el-button>
                <el-button @click="handleReset">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    重置
                </el-button>
                <el-button @click="handleRefresh">
                    <el-icon>
                        <RefreshRight />
                    </el-icon>
                    刷新
                </el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive, watch } from 'vue';

// 搜索表单类型定义
export interface CategorySearchForm {
    keyword: string;
    status?: number;
    start_date: string;
    end_date: string;
}

// Props
interface Props {
    modelValue?: CategorySearchForm;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
        keyword: '',
        status: undefined,
        start_date: '',
        end_date: '',
    })
});

// Emits
interface Emits {
    search: [params: CategorySearchForm];
    reset: [];
    refresh: [];
    'update:modelValue': [value: CategorySearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<CategorySearchForm>({ ...props.modelValue });

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
    emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
    emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
    Object.assign(searchForm, {
        keyword: '',
        status: undefined,
        start_date: '',
        end_date: '',
    });
    emit('reset');
};

// 刷新
const handleRefresh = () => {
    emit('refresh');
};
</script>

<style scoped lang="scss">
.category-search-bar {
    margin-bottom: 16px;
}

.category-search-bar .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
}

.category-search-bar .el-form-item {
    margin-bottom: 8px;
}
</style>
