package advs

import (
	"frontapi/internal/models/advs"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type AdvertisementRepository interface {
	base.ExtendedRepository[advs.Advertisement]
}

type advertisementRepository struct {
	base.ExtendedRepository[advs.Advertisement]
}

func NewAdvertisementRepository(db *gorm.DB) AdvertisementRepository {
	return &advertisementRepository{
		ExtendedRepository: base.NewExtendedRepository[advs.Advertisement](db),
	}
}
