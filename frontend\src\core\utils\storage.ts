/**
 * 本地存储工具
 */

/**
 * 存储接口
 */
export interface StorageInterface {
  get<T = any>(key: string): T | null
  set(key: string, value: any): void
  remove(key: string): void
  clear(): void
  has(key: string): boolean
  keys(): string[]
  size(): number
}

/**
 * 存储配置
 */
export interface StorageConfig {
  prefix?: string
  encrypt?: boolean
  compress?: boolean
  ttl?: number // 过期时间（毫秒）
}

/**
 * 存储项
 */
interface StorageItem {
  value: any
  timestamp: number
  ttl?: number
}

/**
 * 本地存储实现类
 */
class StorageImpl implements StorageInterface {
  private storage: Storage
  private config: Required<StorageConfig>

  constructor(storage: Storage, config: StorageConfig = {}) {
    this.storage = storage
    this.config = {
      prefix: 'app_',
      encrypt: false,
      compress: false,
      ttl: 0,
      ...config
    }
  }

  /**
   * 生成完整的key
   */
  private getFullKey(key: string): string {
    return `${this.config.prefix}${key}`
  }

  /**
   * 序列化数据
   */
  private serialize(value: any, ttl?: number): string {
    const item: StorageItem = {
      value,
      timestamp: Date.now(),
      ttl: ttl || this.config.ttl || undefined
    }

    let serialized = JSON.stringify(item)

    // 压缩（简单实现）
    if (this.config.compress) {
      // 这里可以集成压缩库，如 lz-string
      // serialized = LZString.compress(serialized)
    }

    // 加密（简单实现）
    if (this.config.encrypt) {
      // 这里可以集成加密库
      // serialized = CryptoJS.AES.encrypt(serialized, 'secret-key').toString()
    }

    return serialized
  }

  /**
   * 反序列化数据
   */
  private deserialize(data: string): any {
    try {
      let processed = data

      // 解密
      if (this.config.encrypt) {
        // processed = CryptoJS.AES.decrypt(processed, 'secret-key').toString(CryptoJS.enc.Utf8)
      }

      // 解压缩
      if (this.config.compress) {
        // processed = LZString.decompress(processed)
      }

      const item: StorageItem = JSON.parse(processed)

      // 检查是否过期
      if (item.ttl && Date.now() - item.timestamp > item.ttl) {
        return null
      }

      return item.value
    } catch (error) {
      console.error('Failed to deserialize storage data:', error)
      return null
    }
  }

  /**
   * 获取数据
   */
  get<T = any>(key: string): T | null {
    try {
      const fullKey = this.getFullKey(key)
      const data = this.storage.getItem(fullKey)
      
      if (data === null) {
        return null
      }

      const value = this.deserialize(data)
      
      // 如果数据已过期，删除并返回null
      if (value === null) {
        this.remove(key)
        return null
      }

      return value
    } catch (error) {
      console.error('Failed to get storage item:', error)
      return null
    }
  }

  /**
   * 设置数据
   */
  set(key: string, value: any, ttl?: number): void {
    try {
      const fullKey = this.getFullKey(key)
      const serialized = this.serialize(value, ttl)
      this.storage.setItem(fullKey, serialized)
    } catch (error) {
      console.error('Failed to set storage item:', error)
      
      // 如果存储空间不足，尝试清理过期数据
      if (error instanceof DOMException && error.code === 22) {
        this.cleanup()
        try {
          const serialized = this.serialize(value, ttl)
          this.storage.setItem(this.getFullKey(key), serialized)
        } catch (retryError) {
          console.error('Failed to set storage item after cleanup:', retryError)
        }
      }
    }
  }

  /**
   * 删除数据
   */
  remove(key: string): void {
    try {
      const fullKey = this.getFullKey(key)
      this.storage.removeItem(fullKey)
    } catch (error) {
      console.error('Failed to remove storage item:', error)
    }
  }

  /**
   * 清空所有数据
   */
  clear(): void {
    try {
      const keys = this.keys()
      keys.forEach(key => {
        this.storage.removeItem(this.getFullKey(key))
      })
    } catch (error) {
      console.error('Failed to clear storage:', error)
    }
  }

  /**
   * 检查是否存在
   */
  has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * 获取所有key
   */
  keys(): string[] {
    try {
      const keys: string[] = []
      const prefix = this.config.prefix
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i)
        if (key && key.startsWith(prefix)) {
          keys.push(key.substring(prefix.length))
        }
      }
      
      return keys
    } catch (error) {
      console.error('Failed to get storage keys:', error)
      return []
    }
  }

  /**
   * 获取存储项数量
   */
  size(): number {
    return this.keys().length
  }

  /**
   * 清理过期数据
   */
  cleanup(): void {
    try {
      const keys = this.keys()
      keys.forEach(key => {
        // 通过get方法检查是否过期，过期的会自动删除
        this.get(key)
      })
    } catch (error) {
      console.error('Failed to cleanup storage:', error)
    }
  }

  /**
   * 获取存储使用情况
   */
  getUsage(): { used: number; total: number; percentage: number } {
    try {
      let used = 0
      const keys = this.keys()
      
      keys.forEach(key => {
        const fullKey = this.getFullKey(key)
        const data = this.storage.getItem(fullKey)
        if (data) {
          used += data.length
        }
      })

      // 估算总容量（通常为5MB）
      const total = 5 * 1024 * 1024
      const percentage = (used / total) * 100

      return { used, total, percentage }
    } catch (error) {
      console.error('Failed to get storage usage:', error)
      return { used: 0, total: 0, percentage: 0 }
    }
  }
}

/**
 * 创建存储实例
 */
export function createStorage(storage: Storage, config?: StorageConfig): StorageInterface {
  return new StorageImpl(storage, config)
}

/**
 * 默认localStorage实例
 */
export const storage = createStorage(localStorage, {
  prefix: 'myfirm_',
  ttl: 7 * 24 * 60 * 60 * 1000 // 7天
})

/**
 * sessionStorage实例
 */
export const sessionStorage = createStorage(window.sessionStorage, {
  prefix: 'myfirm_session_'
})

/**
 * 内存存储实现（用于测试或不支持localStorage的环境）
 */
class MemoryStorage implements StorageInterface {
  private data: Map<string, any> = new Map()

  get<T = any>(key: string): T | null {
    return this.data.get(key) || null
  }

  set(key: string, value: any): void {
    this.data.set(key, value)
  }

  remove(key: string): void {
    this.data.delete(key)
  }

  clear(): void {
    this.data.clear()
  }

  has(key: string): boolean {
    return this.data.has(key)
  }

  keys(): string[] {
    return Array.from(this.data.keys())
  }

  size(): number {
    return this.data.size
  }
}

/**
 * 内存存储实例
 */
export const memoryStorage = new MemoryStorage()

/**
 * 存储工具函数
 */
export const StorageUtils = {
  /**
   * 检查存储是否可用
   */
  isStorageAvailable(type: 'localStorage' | 'sessionStorage'): boolean {
    try {
      const storage = window[type]
      const testKey = '__storage_test__'
      storage.setItem(testKey, 'test')
      storage.removeItem(testKey)
      return true
    } catch (error) {
      return false
    }
  },

  /**
   * 获取最佳存储方案
   */
  getBestStorage(): StorageInterface {
    if (this.isStorageAvailable('localStorage')) {
      return storage
    } else if (this.isStorageAvailable('sessionStorage')) {
      return sessionStorage
    } else {
      return memoryStorage
    }
  },

  /**
   * 迁移数据
   */
  migrate(from: StorageInterface, to: StorageInterface, keys?: string[]): void {
    try {
      const keysToMigrate = keys || from.keys()
      
      keysToMigrate.forEach(key => {
        const value = from.get(key)
        if (value !== null) {
          to.set(key, value)
        }
      })
    } catch (error) {
      console.error('Failed to migrate storage data:', error)
    }
  },

  /**
   * 备份数据
   */
  backup(storage: StorageInterface): Record<string, any> {
    try {
      const backup: Record<string, any> = {}
      const keys = storage.keys()
      
      keys.forEach(key => {
        const value = storage.get(key)
        if (value !== null) {
          backup[key] = value
        }
      })
      
      return backup
    } catch (error) {
      console.error('Failed to backup storage data:', error)
      return {}
    }
  },

  /**
   * 恢复数据
   */
  restore(storage: StorageInterface, backup: Record<string, any>): void {
    try {
      Object.entries(backup).forEach(([key, value]) => {
        storage.set(key, value)
      })
    } catch (error) {
      console.error('Failed to restore storage data:', error)
    }
  }
}

/**
 * 默认导出
 */
export default storage