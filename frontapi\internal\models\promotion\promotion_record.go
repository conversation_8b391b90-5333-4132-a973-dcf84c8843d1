package promotion

import (
	"frontapi/internal/models"
)

type PromotionRecord struct {
	models.BaseModel
	CampaignID    string  `json:"campaign_id" gorm:"type:string;size:36;not null;comment:活动ID"`
	UserID        string  `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	RewardType    string  `json:"reward_type" gorm:"type:string;size:20;not null;comment:奖励类型"`
	RewardValue   float64 `json:"reward_value" gorm:"type:decimal(10,2);not null;comment:奖励值"`
	TransactionID string  `json:"transaction_id" gorm:"type:string;size:36;comment:交易ID"`
	OrderID       string  `json:"order_id" gorm:"type:string;size:36;comment:订单ID"`
	ReferenceID   string  `json:"reference_id" gorm:"type:string;size:100;comment:关联ID"`
	Status        string  `json:"status" gorm:"type:string;size:20;not null;default:'success';comment:状态:pending-处理中,success-成功,failed-失败"`
	IP            string  `json:"ip" gorm:"type:string;size:50;comment:操作IP"`
	Device        string  `json:"device" gorm:"type:string;size:255;comment:设备信息"`
}

func (PromotionRecord) TableName() string {
	return "ly_promotion_records"
}
