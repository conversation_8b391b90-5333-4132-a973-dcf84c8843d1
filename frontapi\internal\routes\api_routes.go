package routes

import (
	"frontapi/internal/api/auth"
	"frontapi/internal/api/home"
	"frontapi/internal/bootstrap"
	apiRoute "frontapi/internal/routes/api"

	"github.com/gofiber/fiber/v2"
)

// RegisterAPIRoutes 注册前端API路由（不使用服务容器）
func RegisterBaseAPIRoutes(app *fiber.App, apiGroup fiber.Router) {
	// 根路由
	homeController := &home.HomeController{}
	apiGroup.Post("/", homeController.Index)

	// 认证相关路由组
	authGroup := apiGroup.Group("/auth")
	authController := &auth.AuthController{}
	{
		authGroup.Post("/login", authController.Login)
		authGroup.Post("/logout", authController.Logout)
		authGroup.Post("/test", authController.Test)
	}

}

// RegisterAPIRoutesWithContainer 使用服务容器注册前端API路由
func RegisterAPIRoutesWithContainer(app *fiber.App, services *bootstrap.ServiceContainer) {
	apiGroup := app.Group("/api/v1")

	// 注册基础API路由
	RegisterBaseAPIRoutes(app, apiGroup)

	// 注册首页相关路由
	apiRoute.RegisterHomeRoutes(app, apiGroup, services)

	// 注册用户相关路由
	apiRoute.RegisterUserRoutes(app, apiGroup, services)

	// 注册视频相关路由
	apiRoute.RegisterVideoRoutes(app, apiGroup, services)

	// 注册帖子相关路由
	apiRoute.RegisterPostRoutes(app, apiGroup, services)

	// 注册短视频相关路由
	apiRoute.RegisterShortVideoRoutes(app, apiGroup, services)

	// 注册图片相关路由
	apiRoute.RegisterPictureRoutes(app, apiGroup, services)

	// 注册漫画相关路由
	apiRoute.RegisterComicRoutes(app, apiGroup, services)

}
