package users

import (
	"context"
	"fmt"

	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
	userValidator "frontapi/internal/validation/users"
	"frontapi/pkg/utils"
)

// UserService 用户服务接口
type UserService interface {
	base.IExtendedService[users.User]
	GetUserByFollow(ctx context.Context, userID, followeeID string) (*users.User, error)
	GetRecommendUsers(ctx context.Context, condition map[string]interface{}, userID string, orderBy string, page, pageSize int) ([]*users.User, int64, error)
	ChangePassword(ctx context.Context, id string, req *userValidator.ChangePasswordRequest) error
	CheckLoginUser(ctx context.Context, username, password string) (*users.User, error)
	GetAllCelebrity(ctx context.Context, condition map[string]interface{}, sortBy string, page, pageSize int) ([]*users.User, int64, error)
	ListUserAllComments(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*users.UserComment, int64, error)
}

// userService 用户服务实现
type userService struct {
	*base.ExtendedService[users.User]
	userRepo        repo.UserRepository
	userFollowsRepo repo.UserFollowsRepository
}

func NewUserService(userRepo repo.UserRepository, userFollowsRepo repo.UserFollowsRepository) UserService {
	return &userService{
		ExtendedService: base.NewExtendedService[users.User](userRepo, "user"),
		userRepo:        userRepo,
		userFollowsRepo: userFollowsRepo,
	}
}

func (s *userService) GetUserByFollow(ctx context.Context, userID, followeeID string) (*users.User, error) {
	user, err := s.userFollowsRepo.GetUserFollow(ctx, userID, followeeID)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// GetRecommendUsers 获取推荐用户列表
func (s *userService) GetRecommendUsers(ctx context.Context, condition map[string]interface{}, userID string, orderBy string, page, pageSize int) ([]*users.User, int64, error) {
	//获取已经关注过的用户和已经推荐过的用户
	followList, err := s.userFollowsRepo.FindAll(ctx, map[string]interface{}{
		"follower_id": userID,
	}, "created_at DESC")
	if err != nil {
		return nil, 0, err
	}
	followIds := make([]string, len(followList))
	for i, follow := range followList {
		followIds[i] = follow.FollowedID
	}
	condition["not_in"] = followIds
	//已经推荐过的用户过滤，二期再做
	return s.userRepo.List(ctx, condition, orderBy, page, pageSize)
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(ctx context.Context, id string, req *userValidator.ChangePasswordRequest) error {
	// 获取用户
	user, err := s.BaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}
	if user == nil {
		return fmt.Errorf("用户不存在")
	}

	// 处理null类型的Salt字段
	saltStr := ""
	if user.Salt.Valid {
		saltStr = user.Salt.String
	}

	// 验证旧密码
	if !utils.VerifyPassword(req.OldPassword, saltStr, user.Password) {
		return fmt.Errorf("旧密码错误")
	}

	// 生成新盐值
	salt, err := utils.GenerateSalt()
	if err != nil {
		return err
	}

	// 哈希新密码
	hashedPassword := utils.HashPassword(req.NewPassword, salt)

	// 更新用户密码
	user.Password = hashedPassword
	user.Salt.String = salt
	user.Salt.Valid = true

	return s.BaseService.Update(ctx, user)
}

// CheckLoginUser 检查用户登录
func (s *userService) CheckLoginUser(ctx context.Context, username, password string) (*users.User, error) {
	// 查找用户
	user, err := s.userRepo.FindByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}

	// 处理null类型的Salt字段
	saltStr := ""
	if user.Salt.Valid {
		saltStr = user.Salt.String
	}

	// 验证密码
	if !utils.VerifyPassword(password, saltStr, user.Password) {
		return nil, fmt.Errorf("密码错误")
	}

	return user, nil
}

// GetAllCelebrity 获取所有明星
func (s *userService) GetAllCelebrity(ctx context.Context, condition map[string]interface{}, sortBy string, page, pageSize int) ([]*users.User, int64, error) {
	return s.userRepo.List(ctx, condition, sortBy, page, pageSize)
}

// ListUserAllComments 获取用户全部评论
func (s *userService) ListUserAllComments(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*users.UserComment, int64, error) {
	return s.userRepo.ListUserAllComments(ctx, condition, orderBy, page, pageSize)
}
