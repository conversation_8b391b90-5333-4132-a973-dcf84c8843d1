/**
 * Dialog z-index fixes
 * This file contains fixes for overlapping dialog issues in the application
 */

// Ensure the file manager dialog is always on top of other dialogs
.file-manager-dialog {
  z-index: 3500 !important;
}

// Apply to dialog wrapper
.el-dialog__wrapper.file-manager-dialog {
  z-index: 3500 !important;
}

// Fix for URL or file input dialog
.url-file-input-dialog {
  z-index: 3500 !important;
}

// Fix overlay stacking
body > .el-overlay {
  &:nth-last-of-type(1) {
    z-index: 3499 !important;
  }
}

// For nested dialogs, ensure each level has appropriate z-index
.el-dialog__wrapper {
  z-index: 2001 !important;
  
  .el-dialog__wrapper {
    z-index: 2500 !important;
    
    .el-dialog__wrapper {
      z-index: 3000 !important;
    }
  }
}

// Fix overlays at each level
.el-overlay {
  z-index: 2000 !important;
  
  & + .el-overlay {
    z-index: 2500 !important;
    
    & + .el-overlay {
      z-index: 3000 !important;
    }
  }
}

// Make sure poppers are always on top
.el-popper {
  z-index: 4000 !important;
} 