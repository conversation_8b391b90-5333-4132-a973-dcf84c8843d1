# 自定义条件应用功能示例

## 概述

本文档展示了如何使用和测试自定义条件应用功能。这个功能允许仓库子类覆盖基础仓库的查询条件应用逻辑。

## 工作原理

1. **基础仓库**检查caller是否实现了`ConditionApplier`接口
2. 如果实现了，使用子类的`ApplyConditions`方法
3. 如果没有实现，使用默认的条件应用逻辑

## 测试示例

### 1. 用户仓库测试

```go
// 在实际的控制器或服务中测试
func TestUserCustomConditions() {
    // 创建用户仓库实例
    userRepo := users.NewuserRepository(db)
    
    // 准备查询条件
    condition := map[string]interface{}{
        "keyword": "john",              // 会在用户名、昵称、邮箱、手机号、简介中搜索
        "status": 1,                    // 活跃用户
        "is_content_creator": 1,        // 内容创作者
        "reg_time_start": "2024-01-01", // 注册时间范围
    }
    
    // 调用查询方法 - 会自动使用自定义的ApplyConditions
    users, total, err := userRepo.List(ctx, condition, "created_at DESC", 1, 10)
    
    // 检查结果
    if err == nil {
        fmt.Printf("Found %d users matching custom conditions\n", total)
    }
}
```

### 2. 短视频仓库测试

```go
func TestShortVideoCustomConditions() {
    // 创建短视频仓库实例
    shortVideoRepo := shortvideos.NewShortVideoRepository(db)
    
    // 准备查询条件
    condition := map[string]interface{}{
        "keyword": "funny",          // 会在标题和描述中搜索
        "category_id": "cat123",     // 特定分类
        "creator_name": "creator1",   // 创作者名称模糊搜索
        "duration_min": 60,          // 最小时长60秒
        "duration_max": 300,         // 最大时长5分钟
        "is_featured": 1,            // 推荐视频
        "view_count_min": 1000,      // 最少观看次数
        "status": 1,                 // 已发布状态
    }
    
    // 调用查询方法
    videos, total, err := shortVideoRepo.List(ctx, condition, "view_count DESC", 1, 20)
    
    if err == nil {
        fmt.Printf("Found %d videos matching custom conditions\n", total)
    }
}
```

### 3. 验证接口实现

```go
func TestConditionApplierInterface() {
    userRepo := users.NewuserRepository(db)
    
    // 类型断言检查是否实现了ConditionApplier接口
    if applier, ok := userRepo.(base.ConditionApplier); ok {
        fmt.Println("✓ User repository implements ConditionApplier")
        
        // 可以直接调用ApplyConditions方法测试
        query := db.Model(&users.User{})
        condition := map[string]interface{}{
            "keyword": "test",
            "status": 1,
        }
        
        resultQuery := applier.ApplyConditions(query, condition)
        // resultQuery现在包含了应用条件后的查询
        
    } else {
        fmt.Println("✗ User repository does not implement ConditionApplier")
    }
}
```

## 实际使用场景

### 在控制器中使用

```go
func (c *UserController) ListUsers(ctx *fiber.Ctx) error {
    // 获取请求参数
    reqInfo := c.GetRequestInfo(ctx)
    
    // 构建查询条件
    condition := map[string]interface{}{
        "keyword": reqInfo.Get("keyword").GetString(),
        "status": reqInfo.Get("status").GetInt(),
        "is_content_creator": reqInfo.Get("is_content_creator").GetInt(),
        "creator_level": reqInfo.Get("creator_level").GetInt(),
        "reg_time_start": reqInfo.Get("reg_time_start").GetString(),
        "reg_time_end": reqInfo.Get("reg_time_end").GetString(),
    }
    
    // 调用仓库方法 - 自动使用自定义条件应用
    users, total, err := c.UserService.GetUserList(
        ctx.Context(), 
        reqInfo.Page.PageNo, 
        reqInfo.Page.PageSize, 
        condition,
    )
    
    if err != nil {
        return c.InternalServerError(ctx, "获取用户列表失败")
    }
    
    return c.SuccessList(ctx, users, total, reqInfo.Page.PageNo, reqInfo.Page.PageSize)
}
```

### 在服务层中使用

```go
func (s *UserService) GetUserList(ctx context.Context, page, pageSize int, condition map[string]interface{}) ([]*users.User, int64, error) {
    // 直接调用仓库的List方法
    // 仓库会自动检测并使用自定义的ApplyConditions方法
    return s.userRepo.List(ctx, condition, "created_at DESC", page, pageSize)
}
```

## 调试和验证

### 1. 检查SetCaller是否被调用

```go
func NewUserRepository(db *gorm.DB) UserRepository {
    repo := &userRepository{
        ExtendedRepository: base.NewExtendedRepository[users.User](db),
    }
    
    // 确保这行代码存在且被执行
    repo.SetCaller(repo)
    
    return repo
}
```

### 2. 验证ApplyConditions方法签名

```go
// 方法签名必须完全匹配接口定义
func (r *userRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    // 实现细节...
}
```

### 3. 添加日志进行调试

```go
func (r *userRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    log.Printf("User repository ApplyConditions called with conditions: %+v", condition)
    
    if condition == nil {
        return query
    }
    
    // 处理各种条件...
    
    return query
}
```

## 性能建议

1. **索引优化**: 为常用的查询字段创建数据库索引
2. **条件顺序**: 将选择性高的条件放在前面
3. **缓存策略**: 对于频繁查询的结果考虑使用缓存
4. **分页优化**: 使用游标分页替代偏移分页处理大数据集

## 常见问题

### Q: 自定义条件没有生效？
A: 检查以下几点：
- 是否调用了`SetCaller(repo)`
- `ApplyConditions`方法名是否正确（注意大小写）
- 方法签名是否匹配接口定义

### Q: 如何处理复杂的查询条件？
A: 可以在`ApplyConditions`方法中调用其他辅助方法：

```go
func (r *userRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    if condition == nil {
        return query
    }
    
    query = r.applyKeywordSearch(query, condition)
    query = r.applyStatusFilter(query, condition)
    query = r.applyTimeRangeFilter(query, condition)
    
    return query
}

func (r *userRepository) applyKeywordSearch(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    if keyword, ok := condition["keyword"].(string); ok && keyword != "" {
        query = query.Where("username LIKE ? OR nickname LIKE ? OR email LIKE ?", 
            "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
    }
    return query
}
```

### Q: 如何确保向后兼容？
A: 基础仓库提供了回退机制，如果子类没有实现`ConditionApplier`接口，会自动使用默认的条件应用逻辑。 