<template>
  <div class="umo-editor-wrapper" :style="{ height: `${height}px` }">
    <div v-if="!loadError" id="umo-editor-container"></div>
    <div v-else class="fallback-editor">
      <div class="fallback-toolbar">
        <button @click="execCommand('bold')" title="加粗"><b>B</b></button>
        <button @click="execCommand('italic')" title="斜体"><i>I</i></button>
        <button @click="execCommand('underline')" title="下划线"><u>U</u></button>
        <button @click="execCommand('insertOrderedList')" title="有序列表">1.</button>
        <button @click="execCommand('insertUnorderedList')" title="无序列表">•</button>
        <button @click="execCommand('createLink')" title="插入链接">🔗</button>
        <button @click="execCommand('insertImage')" title="插入图片">🖼️</button>
      </div>
      <div
        ref="fallbackEditor"
        class="fallback-content"
        contenteditable="true"
        @input="handleFallbackInput"
        v-html="innerValue"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import './editor-styles.css';

// Declare global UmoEditor type
declare global {
  interface Window {
    UmoEditor: any;
  }
}

// Flag to track whether UmoEditor loaded successfully
const loadError = ref(false);
let editorInstance: any = null;

// Component props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 500
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  uploadUrl: {
    type: String,
    default: '/api/common/upload'
  },
  simpleMode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

// 内部值用于同步外部传入的值
const innerValue = ref(props.modelValue);
const fallbackEditor = ref<HTMLElement | null>(null);

// 监听外部传入的值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== undefined && newValue !== innerValue.value) {
    innerValue.value = newValue;
    
    // Update editor content if editor instance exists
    if (editorInstance && !loadError.value) {
      editorInstance.setValue(newValue);
    }
  }
});

// 工具栏配置
const toolbarItems = computed(() => props.simpleMode 
  ? ['heading', 'bold', 'italic', 'underline', 'bulletList', 'orderedList', 'link', 'image']
  : [
      'undo', 'redo', 'clear', 'divider',
      'heading', 'bold', 'italic', 'underline', 'strike', 'divider',
      'bulletList', 'orderedList', 'indent', 'outdent', 'divider',
      'table', 'link', 'image', 'video', 'formula', 'divider',
      'code', 'blockquote', 'horizontalRule', 'divider',
      'alignment', 'lineHeight', 'fontSize', 'fontColor', 'backgroundColor', 'divider',
      'print', 'exportWord', 'exportPdf', 'fullScreen'
    ]);

// Initialize editor with provided configuration
const initEditor = async () => {
  try {
    // Use the global UmoEditor from window if it exists
    if (typeof window !== 'undefined' && window.UmoEditor) {
      const container = document.getElementById('umo-editor-container');
      if (!container) return;
      
      // Create editor instance
      editorInstance = new window.UmoEditor({
        el: container,
        value: innerValue.value,
        placeholder: props.placeholder,
        height: props.height,
        toolbar: {
          items: toolbarItems.value,
          floating: false
        },
        page: {
          enabled: true,
          margin: {
            top: 72,
            bottom: 72,
            left: 72,
            right: 72
          },
          paperSize: 'A4'
        },
        upload: {
          image: {
            action: props.uploadUrl,
            headers: {},
            data: { type: 'image' },
            name: 'file',
            response: (res: any) => {
              try {
                const parsedRes = typeof res === 'string' ? JSON.parse(res) : res;
                if (parsedRes.code === 2000) {
                  return parsedRes.data.url;
                }
                return '';
              } catch (error) {
                console.error('Upload response parsing error:', error);
                return '';
              }
            }
          }
        },
        extensions: {
          table: { resizable: true },
          imageResize: { enabled: true },
          heading: { levels: [1, 2, 3] }
        }
      });
      
      // Add change event listener
      editorInstance.on('change', () => {
        const value = editorInstance.getValue();
        innerValue.value = value;
        emit('update:modelValue', value);
        emit('change', value);
      });
      
      // Editor initialized successfully
      loadError.value = false;
      console.log('UmoEditor initialized successfully');
    } else {
      // UmoEditor not found in window
      console.error('UmoEditor not found in window object');
      loadError.value = true;
    }
  } catch (error) {
    console.error('Failed to initialize UmoEditor:', error);
    loadError.value = true;
  }
};

// Import the UmoEditor script and initialize the editor
onMounted(async () => {
  try {
    // Try to load from CDN first with fallback to local
    // Dynamically load CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://unpkg.com/@umoteam/editor@6.1.1/dist/style.css';
    link.onerror = () => {
      console.warn('Failed to load UmoEditor CSS from CDN, trying local path');
      link.href = 'node_modules/@umoteam/editor/dist/style.css';
    };
    document.head.appendChild(link);
    
    // Create a script element to load UmoEditor
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/@umoteam/editor@6.1.1/dist/umo-editor.js';
    script.async = true;
    
    script.onload = () => {
      console.log('UmoEditor script loaded');
      initEditor();
    };
    
    script.onerror = (error) => {
      console.warn('Failed to load UmoEditor script from CDN, trying local path', error);
      
      // Try local path as fallback
      const localScript = document.createElement('script');
      localScript.src = 'node_modules/@umoteam/editor/dist/umo-editor.js';
      localScript.async = true;
      
      localScript.onload = () => {
        console.log('UmoEditor script loaded from local path');
        initEditor();
      };
      
      localScript.onerror = (localError) => {
        console.error('Failed to load UmoEditor script from local path:', localError);
        loadError.value = true;
      };
      
      document.head.appendChild(localScript);
    };
    
    document.head.appendChild(script);
  } catch (error) {
    console.error('Failed to load UmoEditor:', error);
    loadError.value = true;
  }
  
  // If using fallback editor, set initial content
  if (loadError.value && fallbackEditor.value) {
    fallbackEditor.value.innerHTML = innerValue.value;
  }
});

// 备用编辑器命令执行
const execCommand = (command: string, value?: string) => {
  if (command === 'createLink') {
    const url = prompt('请输入链接地址:');
    if (url) document.execCommand('createLink', false, url);
  } else if (command === 'insertImage') {
    const url = prompt('请输入图片地址:');
    if (url) document.execCommand('insertImage', false, url);
  } else {
    document.execCommand(command, false, value);
  }
  if (fallbackEditor.value) {
    fallbackEditor.value.focus();
  }
};

// 备用编辑器内容变更
const handleFallbackInput = () => {
  if (fallbackEditor.value) {
    innerValue.value = fallbackEditor.value.innerHTML;
    emit('update:modelValue', innerValue.value);
    emit('change', innerValue.value);
  }
};

onBeforeUnmount(() => {
  // Cleanup editor instance when component is unmounted
  if (editorInstance) {
    editorInstance.destroy();
    editorInstance = null;
  }
  console.log('UmoEditor component unmounted');
});
</script>

<style>
.umo-editor-wrapper {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

#umo-editor-container {
  width: 100%;
  height: 100%;
}

/* Override some UmoEditor styles to match the application theme */
.umo-editor-toolbar {
  background-color: #f5f7fa !important;
  border-bottom: 1px solid #dcdfe6 !important;
}

.umo-editor-content {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
  background-color: #fff !important;
}

/* Ensure proper layout for the editor */
.umo-editor {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

/* Fallback editor styles */
.fallback-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.fallback-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 8px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.fallback-toolbar button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.fallback-toolbar button:hover {
  background-color: #ecf5ff;
}

.fallback-content {
  flex: 1;
  padding: 12px 15px;
  overflow-y: auto;
  outline: none;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 14px;
  line-height: 1.5;
}
</style> 