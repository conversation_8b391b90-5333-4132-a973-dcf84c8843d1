package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoSubtitle 视频字幕表
type VideoSubtitle struct {
	*models.BaseModelStruct
	VideoID  string `gorm:"column:video_id;index" json:"video_id"` // 视频ID
	Language string `gorm:"column:language" json:"language"`       // 语言
	URL      string `gorm:"column:url" json:"url"`                 // 字幕文件URL
}

// TableName 设置表名
func (VideoSubtitle) TableName() string {
	return "ly_video_subtitles"
}

// 实现BaseModel接口的方法
func (v *VideoSubtitle) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoSubtitle) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoSubtitle) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoSubtitle) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
