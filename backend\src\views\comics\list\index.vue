<template>
  <el-scrollbar>
    <div class="comics-list">
      <el-card>
        <template #header>
          <div class="flex justify-between items-center">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item>漫画管理</el-breadcrumb-item>
                <el-breadcrumb-item>漫画列表</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <el-button type="primary" @click="onAdd">添加漫画</el-button>
          </div>
        </template>

        <!-- 搜索栏组件 -->
        <SearchBar
          @search="onSearch"
          @reset="onReset"
          :categoryOptions="categoryOptions"
          @refresh="fetchComicsList"
        />

        <!-- 漫画表格组件 -->
        <ComicsTable
          :loading="loading"
          :comicsList="comicsList"
          @edit="onEdit"
          @detail="onDetail"
          @delete="onDelete"
          @chapter="onManageChapter"
        />

        <!-- 分页组件 -->
        <Pagination
          :total="total"
          :current="currentPage"
          :size="pageSize"
          @pagination="onPageChange"
        />
      </el-card>

      <!-- 漫画表单对话框 -->
      <ComicsDialog
        :visible="dialogVisible"
        :comics="currentComics"
        @submit="onDialogSubmit"
        :categoryOptions="categoryOptions"
        @update:visible="dialogVisible = $event"
      />

      <!-- 漫画详情弹窗 -->
      <ComicsDetailDialog
        :visible="detailDialogVisible"
        :comics="currentComics"
        @update:visible="detailDialogVisible = $event"
      />
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getComicsList, deleteComics, createComics, updateComics } from '@/service/api/comics/comics';
import SearchBar from './components/SearchBar.vue';
import ComicsTable from './components/ComicsTable.vue';
import Pagination from './components/Pagination.vue';
import ComicsDialog from './components/ComicsDialog.vue';
import ComicsDetailDialog from './components/ComicsDetailDialog.vue';
import { ComicsItem } from '@/types/comics';
import { getAllComicsCategories } from '@/service/api/comics/category';
const router = useRouter();

// 初始化标签页
onMounted(() => {

  // 初始化数据
  fetchComicsList();
  loadCategories();
});

// 数据状态
const loading = ref(false);
const comicsList = ref<ComicsItem[]>([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const searchParams = ref<Record<string, any>>({});
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const currentComics = ref<ComicsItem>({} as ComicsItem);
// 分类选项
const categoryOptions = ref<Array<{id: string; name: string}>>([]);
// 获取漫画列表
const fetchComicsList = async () => {
  loading.value = true;
  try {
    const params = {
      page: {
        pageNo: currentPage.value,
        pageSize: pageSize.value,
      },
      data: {
        keyword: searchParams.value.title || '',
        category_id: searchParams.value.category_id || '',
        progress: searchParams.value.progress !== undefined ? parseInt(searchParams.value.progress) : undefined,
      }
    };

    const {response,data} = await getComicsList(params) as any;
    if (response.data.code === 2000) {
      comicsList.value = data.list;
      total.value = data.total || 0;
    } else {
      ElMessage.error(response.data.message || '获取漫画列表失败');
    }
  } catch (error) {
    console.error('获取漫画列表出错:', error);
    ElMessage.error('获取漫画列表失败');
  } finally {
    loading.value = false;
  }
};
// 加载分类选项
const loadCategories = async () => {
  try {
    const {response,data} = await getAllComicsCategories() as any;
    if (response?.data?.code === 2000) {
      categoryOptions.value = data.list.map(item => ({
        id: item.id || '',
        name: item.name || ''
      }));
    } else {
      console.error('获取分类失败:', response?.data?.message);
      categoryOptions.value = [];
    }
  } catch (error) {
    console.error('获取分类出错:', error);
    categoryOptions.value = [];
  }
};
// 处理搜索
const onSearch = (formData: Record<string, any>) => {
  searchParams.value = formData;
  currentPage.value = 1;
  fetchComicsList();
};

// 处理重置
const onReset = () => {
  searchParams.value = {};
  currentPage.value = 1;
  fetchComicsList();
};

// 处理分页
const onPageChange = (params: { page: number; pageSize: number }) => {
  currentPage.value = params.page;
  pageSize.value = params.pageSize;
  fetchComicsList();
};

// 处理添加漫画
const onAdd = () => {
  currentComics.value = {} as ComicsItem;
  dialogVisible.value = true;
};

// 处理编辑漫画
const onEdit = (row: ComicsItem) => {
  currentComics.value = {...row};
  dialogVisible.value = true;
};

// 处理查看详情
const onDetail = (row: ComicsItem) => {
  currentComics.value = {...row};
  detailDialogVisible.value = true;
};

// 处理管理章节
const onManageChapter = (row: ComicsItem) => {
  router.push(`/comics/chapter/${row.id}`);
};

// 处理删除漫画
const onDelete = async (row: ComicsItem) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个漫画吗？删除后无法恢复。',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const {response,data} = await deleteComics(row.id) as any;
    if (response.data.code === 2000) {
      ElMessage.success('删除成功');
      fetchComicsList();
    } else {
      ElMessage.error(response.data.message || '删除失败');
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除漫画出错:', error);
      ElMessage.error('删除失败');
    }
  }
};

// 处理对话框提交
const onDialogSubmit = async (formData: any) => {
  try {
    const isUpdate = !!formData.id;
    const params = { data: formData };
    let res;

    if (isUpdate) {
      res = await updateComics(formData.id, params) as any;
    } else {
      res = await createComics(params) as any;
    }
    const {response,data}=res as any;
    if (response.data.code === 2000) {
      ElMessage.success(isUpdate ? '更新成功' : '添加成功');
      dialogVisible.value = false;
      fetchComicsList();
    } else {
      ElMessage.error(response?.data.message || (isUpdate ? '更新失败' : '添加失败'));
    }
  } catch (error) {
    console.error('操作失败:', error);
    ElMessage.error('操作失败');
  }
};
</script>

<style scoped>
.comics-list {
  width: 100%;
}
</style>
