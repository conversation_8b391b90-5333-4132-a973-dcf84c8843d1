package videos

import (
	"frontapi/internal/models"

	"github.com/guregu/null/v6"
)

// VideoCategory 视频分类表
type VideoCategory struct {
	models.CategoryBaseModel
	Icon          string           `gorm:"column:icon" json:"icon"`                               // 分类图标
	Color         string           `gorm:"column:color" json:"color"`                             // 分类颜色
	Code          string           `gorm:"column:code;uniqueIndex" json:"code"`                   // 分类编码
	ParentID      null.String      `gorm:"column:parent_id" json:"parent_id"`                     // 父分类ID
	Uri           string           `gorm:"column:uri" json:"uri"`                                 // URI标识
	Image         string           `gorm:"column:image" json:"image"`                             // 分类图片
	FeaturedOrder int              `gorm:"column:featured_order;default:0" json:"featured_order"` // 推荐排序
	IsFeatured    int              `gorm:"column:is_featured;default:0" json:"is_featured"`       // 是否推荐
	Children      []*VideoCategory `json:"children" gorm:"-"`
}

// TableName 设置表名
func (VideoCategory) TableName() string {
	return "ly_video_categories"
}
