# 项目目录结构说明

## 概述

本项目采用标准Go项目结构，将代码按照功能和层次进行组织，主要包括API处理、路由管理、业务逻辑、数据访问等部分。

## 主要目录结构

```
frontapi/
├── cmd/                 # 主程序入口
│   └── main.go          # 主程序
├── config/              # 配置相关
│   └── config.go        # 配置加载和管理
├── internal/            # 内部包，不对外暴露
│   ├── api/             # API处理函数
│   │   ├── home.go      # 首页相关
│   │   ├── auth.go      # 认证相关
│   │   └── redis.go     # Redis状态相关
│   ├── handlers/        # 请求处理器
│   ├── middleware/      # 中间件
│   │   └── auth.go      # 认证中间件
│   ├── models/          # 数据模型
│   ├── repository/      # 数据仓库层
│   ├── routes/          # 路由管理
│   │   └── routes.go    # 路由注册
│   └── service/         # 业务服务层
├── pkg/                 # 可被外部引用的包
│   ├── database/        # 数据库相关
│   │   └── mysql.go     # MySQL连接管理
│   ├── redis/           # Redis相关
│   │   ├── redis.go     # Redis操作函数
│   │   └── init.go      # Redis初始化
│   └── utils/           # 工具函数
├── scripts/             # 脚本文件
│   └── test_redis.go    # Redis测试脚本
└── docs/                # 文档
    ├── redis.md         # Redis使用文档
    └── directory-structure.md # 目录结构说明
```

## 代码组织方式

### 分层结构

项目采用经典的分层架构设计：

1. **表示层**：API处理函数和路由
   - `internal/api/`: 包含各类API处理函数
   - `internal/routes/`: 负责路由注册和管理

2. **业务逻辑层**：处理具体业务逻辑
   - `internal/service/`: 实现业务逻辑
   - `internal/handlers/`: 协调请求和服务

3. **数据访问层**：数据操作和持久化
   - `internal/repository/`: 数据访问接口和实现
   - `internal/models/`: 数据模型定义

4. **基础设施层**：提供基础服务
   - `pkg/database/`: 数据库连接管理
   - `pkg/redis/`: Redis连接和操作
   - `pkg/utils/`: 通用工具函数

### 依赖关系

各层之间的依赖关系如下：

1. API层依赖服务层和工具层
2. 服务层依赖仓库层和模型层
3. 仓库层依赖基础设施层和模型层
4. 所有层都可能使用工具层和配置层

## 设计原则

1. **关注点分离**：将不同的功能放在不同的目录和文件中
2. **依赖倒置**：通过接口实现依赖倒置，确保高层不依赖低层实现
3. **单一职责**：每个文件和函数只负责单一功能
4. **开闭原则**：通过扩展而非修改来增加新功能
5. **内聚性高**：相关功能放在一起，不相关功能分开 