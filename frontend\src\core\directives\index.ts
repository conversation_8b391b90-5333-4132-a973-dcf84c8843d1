/**
 * 指令统一导出
 * 整合所有自定义指令
 */

import type { App } from 'vue'

// 导入所有指令
import { clickOutsideUtils, vClickOutside } from './clickOutside'
import { infiniteScrollUtils, vInfiniteScroll } from './infiniteScroll'
import { lazyUtils, vLazy } from './lazy'
import { permissionUtils, vPermission } from './permission'
import { tooltipUtils, vTooltip } from './tooltip'
import { touchUtils, vTouch } from './touch'

// 导出所有指令
export {
    vClickOutside,
    vInfiniteScroll, vLazy,
    vPermission, vTooltip, vTouch
}

// 导出所有工具函数
export {
    clickOutsideUtils,
    infiniteScrollUtils, lazyUtils,
    permissionUtils, tooltipUtils, touchUtils
}

// 导出类型
export type { ClickOutsideOptions } from './clickOutside'
export type { InfiniteScrollOptions } from './infiniteScroll'
export type { LazyOptions } from './lazy'
export type { PermissionOptions } from './permission'
export type { TooltipOptions } from './tooltip'
export type { TouchGestureOptions, TouchPoint } from './touch'

// 指令管理器
export class DirectiveManager {
    private static instance: DirectiveManager
    private registeredDirectives = new Map<string, any>()

    static getInstance(): DirectiveManager {
        if (!DirectiveManager.instance) {
            DirectiveManager.instance = new DirectiveManager()
        }
        return DirectiveManager.instance
    }

    // 注册指令
    register(name: string, directive: any): void {
        this.registeredDirectives.set(name, directive)
    }

    // 获取指令
    get(name: string): any {
        return this.registeredDirectives.get(name)
    }

    // 检查指令是否已注册
    has(name: string): boolean {
        return this.registeredDirectives.has(name)
    }

    // 移除指令
    remove(name: string): boolean {
        return this.registeredDirectives.delete(name)
    }

    // 获取所有已注册的指令
    getAll(): Map<string, any> {
        return new Map(this.registeredDirectives)
    }

    // 清空所有指令
    clear(): void {
        this.registeredDirectives.clear()
    }

    // 批量注册指令到 Vue 应用
    installAll(app: App): void {
        this.registeredDirectives.forEach((directive, name) => {
            app.directive(name, directive)
        })
    }
}

// 默认指令配置
const defaultDirectives = {
    lazy: vLazy,
    permission: vPermission,
    'click-outside': vClickOutside,
    'infinite-scroll': vInfiniteScroll,
    touch: vTouch,
    tooltip: vTooltip
}

// 安装所有指令的插件
export const directivesPlugin = {
    install(app: App, options: { directives?: Record<string, any> } = {}) {
        const manager = DirectiveManager.getInstance()

        // 注册默认指令
        Object.entries(defaultDirectives).forEach(([name, directive]) => {
            manager.register(name, directive)
            app.directive(name, directive)
        })

        // 注册自定义指令
        if (options.directives) {
            Object.entries(options.directives).forEach(([name, directive]) => {
                manager.register(name, directive)
                app.directive(name, directive)
            })
        }

        // 将管理器挂载到全局属性
        app.config.globalProperties.$directiveManager = manager
    }
}

// 工具函数集合
export const directiveUtils = {
    lazy: lazyUtils,
    permission: permissionUtils,
    clickOutside: clickOutsideUtils,
    infiniteScroll: infiniteScrollUtils,
    touch: touchUtils,
    tooltip: tooltipUtils
}

// 常用指令组合
export const directivePresets = {
    // 移动端预设
    mobile: {
        touch: vTouch,
        lazy: vLazy,
        'infinite-scroll': vInfiniteScroll,
        tooltip: vTooltip
    },

    // 桌面端预设
    desktop: {
        'click-outside': vClickOutside,
        permission: vPermission,
        lazy: vLazy,
        'infinite-scroll': vInfiniteScroll,
        tooltip: vTooltip
    },

    // 完整预设
    full: defaultDirectives
}

// 按需安装指令
export function installDirectives(app: App, preset: keyof typeof directivePresets | string[] = 'full') {
    const manager = DirectiveManager.getInstance()

    let directivesToInstall: Record<string, any>

    if (typeof preset === 'string' && preset in directivePresets) {
        directivesToInstall = directivePresets[preset as keyof typeof directivePresets]
    } else if (Array.isArray(preset)) {
        directivesToInstall = {}
        preset.forEach(name => {
            if (name in defaultDirectives) {
                directivesToInstall[name] = defaultDirectives[name as keyof typeof defaultDirectives]
            }
        })
    } else {
        directivesToInstall = defaultDirectives
    }

    Object.entries(directivesToInstall).forEach(([name, directive]) => {
        manager.register(name, directive)
        app.directive(name, directive)
    })

    app.config.globalProperties.$directiveManager = manager
}

// 指令性能监控
export class DirectivePerformanceMonitor {
    private static instance: DirectivePerformanceMonitor
    private metrics = new Map<string, {
        mountCount: number
        updateCount: number
        unmountCount: number
        totalMountTime: number
        totalUpdateTime: number
        averageMountTime: number
        averageUpdateTime: number
    }>()

    static getInstance(): DirectivePerformanceMonitor {
        if (!DirectivePerformanceMonitor.instance) {
            DirectivePerformanceMonitor.instance = new DirectivePerformanceMonitor()
        }
        return DirectivePerformanceMonitor.instance
    }

    // 记录指令挂载性能
    recordMount(directiveName: string, duration: number): void {
        const metric = this.getOrCreateMetric(directiveName)
        metric.mountCount++
        metric.totalMountTime += duration
        metric.averageMountTime = metric.totalMountTime / metric.mountCount
    }

    // 记录指令更新性能
    recordUpdate(directiveName: string, duration: number): void {
        const metric = this.getOrCreateMetric(directiveName)
        metric.updateCount++
        metric.totalUpdateTime += duration
        metric.averageUpdateTime = metric.totalUpdateTime / metric.updateCount
    }

    // 记录指令卸载
    recordUnmount(directiveName: string): void {
        const metric = this.getOrCreateMetric(directiveName)
        metric.unmountCount++
    }

    // 获取指令性能指标
    getMetrics(directiveName?: string) {
        if (directiveName) {
            return this.metrics.get(directiveName)
        }
        return Object.fromEntries(this.metrics)
    }

    // 重置性能指标
    reset(directiveName?: string): void {
        if (directiveName) {
            this.metrics.delete(directiveName)
        } else {
            this.metrics.clear()
        }
    }

    // 获取性能报告
    getReport(): string {
        const report: string[] = ['指令性能报告:', '=']

        this.metrics.forEach((metric, name) => {
            report.push(`\n指令: ${name}`)
            report.push(`  挂载次数: ${metric.mountCount}`)
            report.push(`  更新次数: ${metric.updateCount}`)
            report.push(`  卸载次数: ${metric.unmountCount}`)
            report.push(`  平均挂载时间: ${metric.averageMountTime.toFixed(2)}ms`)
            report.push(`  平均更新时间: ${metric.averageUpdateTime.toFixed(2)}ms`)
        })

        return report.join('\n')
    }

    private getOrCreateMetric(directiveName: string) {
        if (!this.metrics.has(directiveName)) {
            this.metrics.set(directiveName, {
                mountCount: 0,
                updateCount: 0,
                unmountCount: 0,
                totalMountTime: 0,
                totalUpdateTime: 0,
                averageMountTime: 0,
                averageUpdateTime: 0
            })
        }
        return this.metrics.get(directiveName)!
    }
}

// 创建性能监控装饰器
export function withPerformanceMonitoring(directiveName: string, directive: any) {
    const monitor = DirectivePerformanceMonitor.getInstance()

    return {
        ...directive,
        mounted(el: any, binding: any, vnode: any, prevVnode: any) {
            const start = performance.now()

            if (directive.mounted) {
                directive.mounted(el, binding, vnode, prevVnode)
            }

            const end = performance.now()
            monitor.recordMount(directiveName, end - start)
        },

        updated(el: any, binding: any, vnode: any, prevVnode: any) {
            const start = performance.now()

            if (directive.updated) {
                directive.updated(el, binding, vnode, prevVnode)
            }

            const end = performance.now()
            monitor.recordUpdate(directiveName, end - start)
        },

        unmounted(el: any, binding: any, vnode: any, prevVnode: any) {
            if (directive.unmounted) {
                directive.unmounted(el, binding, vnode, prevVnode)
            }

            monitor.recordUnmount(directiveName)
        }
    }
}

// 默认导出
export default {
    install: directivesPlugin.install,
    DirectiveManager,
    DirectivePerformanceMonitor,
    directiveUtils,
    directivePresets,
    installDirectives,
    withPerformanceMonitoring
}