package validator

import (
	"encoding/json"
	sysErrors "errors"
	"fmt"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/gookit/validate"
)

// 自定义错误响应结构
type ErrorResponse struct {
	Success     bool   `json:"success"`
	Message     string `json:"message"`
	FailedField string `json:"failedField,omitempty"`
	Tag         string `json:"tag,omitempty"`
	Value       any    `json:"value,omitempty"`
}

// 详细的验证错误信息
type ValidationError struct {
	FailedField string
	Tag         string
	Value       any
}

// Validator 结构体
type Validator struct {
	validator *validate.Validation
}

// New 创建一个新的验证器实例
func New() *Validator {
	// 使用gookit/validate创建验证器
	return &Validator{}
}

// RegisterValidation 注册自定义验证规则
func (v *Validator) RegisterValidation(tag string, fn func(val interface{}) bool) error {
	// 使用gookit/validate的自定义验证规则
	validate.AddValidator(tag, fn)
	return nil
}

// Validate 验证结构体数据
func (v *Validator) Validate(data interface{}) []ValidationError {
	// 使用gookit/validate创建验证实例
	validator := validate.Struct(data)

	// 运行验证
	if !validator.Validate() {
		// 获取所有错误
		validationErrors := make([]ValidationError, 0)
		errors := validator.Errors
		for field, fieldErrors := range errors.All() {
			for _, errMsg := range fieldErrors {
				validationErrors = append(validationErrors, ValidationError{
					FailedField: field,
					Tag:         "validation_failed",
					Value:       errMsg,
				})
			}
		}
		return validationErrors
	}

	return nil
}

// ValidateRequest 验证请求并返回错误
func ValidateRequest(c *fiber.Ctx, data interface{}) error {
	v := New()

	// 注册自定义验证规则
	if err := RegisterCustomValidations(v); err != nil {
		return c.Status(fiber.StatusInternalServerError).JSON(ErrorResponse{
			Success: false,
			Message: "验证器初始化失败",
		})
	}

	// 只使用一种方式解析请求数据
	if err := c.BodyParser(data); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Success: false,
			Message: "无法解析请求数据: " + err.Error(),
		})
	}

	// 使用gookit/validate验证
	validator := validate.Struct(data)
	if !validator.Validate() {
		errMsgs := make([]string, 0)
		for field, fieldErrors := range validator.Errors.All() {
			for _, errMsg := range fieldErrors {
				errMsgs = append(errMsgs, fmt.Sprintf(
					"字段 [%s]: %s",
					field,
					errMsg,
				))
			}
		}

		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Success: false,
			Message: strings.Join(errMsgs, "; "),
		})
	}

	return nil
}

// ValidateQuery 验证Query参数
func ValidateQuery(c *fiber.Ctx, data interface{}) error {
	if err := c.QueryParser(data); err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Success: false,
			Message: "无法解析查询参数",
		})
	}

	// 使用gookit/validate验证
	validator := validate.Struct(data)
	if !validator.Validate() {
		errMsgs := make([]string, 0)
		for field, fieldErrors := range validator.Errors.All() {
			for _, errMsg := range fieldErrors {
				errMsgs = append(errMsgs, fmt.Sprintf(
					"查询参数 [%s]: %s",
					field,
					errMsg,
				))
			}
		}

		return c.Status(fiber.StatusBadRequest).JSON(ErrorResponse{
			Success: false,
			Message: strings.Join(errMsgs, "; "),
		})
	}

	return nil
}

// ValidateDataWrapper 处理带有data字段包装的请求参数
func ValidateDataWrapper(c *fiber.Ctx, modelPtr interface{}) error {
	// 注册自定义验证规则
	if err := RegisterCustomValidations(New()); err != nil {
		return sysErrors.New("验证器初始化失败:" + err.Error())
	}
	// 1. 获取原始请求体
	body := c.Body()
	fmt.Printf("原始请求体: %s\n", string(body))

	// 2. 创建一个临时结构体来解析带有data字段的请求
	var tempRequest struct {
		Data json.RawMessage `json:"data"`
		Page json.RawMessage `json:"page"`
	}

	// 3. 解析请求体到临时结构体
	if err := json.Unmarshal(body, &tempRequest); err != nil {
		return sysErrors.New("无法解析请求数据:" + err.Error())

	}

	// 4. 检查data字段是否存在
	if len(tempRequest.Data) == 0 {
		return sysErrors.New("请求缺少data字段或data字段为空")

	}

	// 5. 将data字段数据解析到目标模型
	if err := json.Unmarshal(tempRequest.Data, modelPtr); err != nil {
		return sysErrors.New("无法将data字段解析到模型:" + err.Error())
	}
	fmt.Printf("解析的data字段: %s\n", string(tempRequest.Data))
	fmt.Printf("模型类型: %s\n", modelPtr)

	// 6. 准备验证选项
	validator := validate.Struct(modelPtr)
	fmt.Printf("验证规则: %+v\n", validator.Validate())

	// 7. 执行验证
	if !validator.Validate() {
		errors := validator.Errors
		fmt.Printf("验证错误: %+v\n", errors.All())

		errMsgs := make([]string, 0)
		for field, fieldErrors := range errors.All() {
			for _, errMsg := range fieldErrors {
				errMsgs = append(errMsgs, fmt.Sprintf(
					"字段 [%s]: %s",
					field,
					errMsg,
				))
			}
		}
		return sysErrors.New(strings.Join(errMsgs, "; "))
		// 直接返回错误响应并终止执行
	}

	// 8. 处理page字段
	if len(tempRequest.Page) > 0 {
		var pageData map[string]interface{}
		if err := json.Unmarshal(tempRequest.Page, &pageData); err == nil {
			c.Locals("page", pageData)
		}
	}

	return nil
}
