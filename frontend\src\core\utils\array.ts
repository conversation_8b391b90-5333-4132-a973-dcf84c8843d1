/**
 * 数组工具函数
 */

/**
 * 排序选项
 */
export interface SortOptions<T> {
  key?: keyof T | ((item: T) => any) // 排序键或函数
  order?: 'asc' | 'desc' // 排序顺序
  locale?: string // 语言环境（用于字符串排序）
  numeric?: boolean // 数字排序
}

/**
 * 分组选项
 */
export interface GroupOptions<T> {
  key: keyof T | ((item: T) => any) // 分组键或函数
  preserveOrder?: boolean // 是否保持原始顺序
}

/**
 * 分页选项
 */
export interface PaginationOptions {
  page: number // 页码（从1开始）
  pageSize: number // 每页大小
}

/**
 * 分页结果
 */
export interface PaginationResult<T> {
  data: T[] // 当前页数据
  total: number // 总数量
  page: number // 当前页码
  pageSize: number // 每页大小
  totalPages: number // 总页数
  hasNext: boolean // 是否有下一页
  hasPrev: boolean // 是否有上一页
}

/**
 * 统计信息
 */
export interface ArrayStatistics {
  count: number // 总数
  unique: number // 唯一值数量
  duplicates: number // 重复值数量
  empty: number // 空值数量
  nonEmpty: number // 非空值数量
}

/**
 * 数组工具类
 */
export class ArrayUtils {
  /**
   * 检查是否为数组
   */
  static isArray<T>(value: any): value is T[] {
    return Array.isArray(value)
  }

  /**
   * 检查是否为空数组
   */
  static isEmpty<T>(array: T[]): boolean {
    return !this.isArray(array) || array.length === 0
  }

  /**
   * 检查是否不为空数组
   */
  static isNotEmpty<T>(array: T[]): boolean {
    return this.isArray(array) && array.length > 0
  }

  /**
   * 安全获取数组长度
   */
  static length<T>(array: T[]): number {
    return this.isArray(array) ? array.length : 0
  }

  /**
   * 安全获取数组元素
   */
  static get<T>(array: T[], index: number, defaultValue?: T): T | undefined {
    if (!this.isArray(array) || index < 0 || index >= array.length) {
      return defaultValue
    }
    return array[index]
  }

  /**
   * 获取第一个元素
   */
  static first<T>(array: T[], defaultValue?: T): T | undefined {
    return this.get(array, 0, defaultValue)
  }

  /**
   * 获取最后一个元素
   */
  static last<T>(array: T[], defaultValue?: T): T | undefined {
    return this.get(array, array.length - 1, defaultValue)
  }

  /**
   * 获取随机元素
   */
  static random<T>(array: T[]): T | undefined {
    if (this.isEmpty(array)) {
      return undefined
    }
    const index = Math.floor(Math.random() * array.length)
    return array[index]
  }

  /**
   * 获取多个随机元素
   */
  static randomSample<T>(array: T[], count: number): T[] {
    if (this.isEmpty(array) || count <= 0) {
      return []
    }
    
    const shuffled = this.shuffle([...array])
    return shuffled.slice(0, Math.min(count, array.length))
  }

  /**
   * 创建指定长度的数组
   */
  static create<T>(length: number, value?: T | ((index: number) => T)): T[] {
    if (length <= 0) {
      return []
    }
    
    return Array.from({ length }, (_, index) => {
      if (typeof value === 'function') {
        return (value as (index: number) => T)(index)
      }
      return value as T
    })
  }

  /**
   * 创建数字范围数组
   */
  static range(start: number, end?: number, step = 1): number[] {
    if (end === undefined) {
      end = start
      start = 0
    }
    
    if (step === 0) {
      throw new Error('Step cannot be zero')
    }
    
    const result: number[] = []
    
    if (step > 0) {
      for (let i = start; i < end; i += step) {
        result.push(i)
      }
    } else {
      for (let i = start; i > end; i += step) {
        result.push(i)
      }
    }
    
    return result
  }

  /**
   * 数组去重
   */
  static unique<T>(array: T[], key?: keyof T | ((item: T) => any)): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    if (!key) {
      return [...new Set(array)]
    }
    
    const seen = new Set()
    const getKey = typeof key === 'function' ? key : (item: T) => item[key]
    
    return array.filter(item => {
      const keyValue = getKey(item)
      if (seen.has(keyValue)) {
        return false
      }
      seen.add(keyValue)
      return true
    })
  }

  /**
   * 数组去重（保留最后出现的）
   */
  static uniqueLast<T>(array: T[], key?: keyof T | ((item: T) => any)): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return this.unique([...array].reverse(), key).reverse()
  }

  /**
   * 获取重复元素
   */
  static duplicates<T>(array: T[], key?: keyof T | ((item: T) => any)): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    const counts = new Map()
    const getKey = key ? (typeof key === 'function' ? key : (item: T) => item[key]) : (item: T) => item
    
    // 统计出现次数
    array.forEach(item => {
      const keyValue = getKey(item)
      counts.set(keyValue, (counts.get(keyValue) || 0) + 1)
    })
    
    // 返回出现次数大于1的元素
    return array.filter(item => counts.get(getKey(item)) > 1)
  }

  /**
   * 数组扁平化
   */
  static flatten<T>(array: any[], depth = Infinity): T[] {
    if (!this.isArray(array)) {
      return []
    }
    
    return array.flat(depth) as T[]
  }

  /**
   * 深度扁平化
   */
  static flattenDeep<T>(array: any[]): T[] {
    if (!this.isArray(array)) {
      return []
    }
    
    const result: T[] = []
    
    const flatten = (arr: any[]) => {
      arr.forEach(item => {
        if (this.isArray(item)) {
          flatten(item)
        } else {
          result.push(item)
        }
      })
    }
    
    flatten(array)
    return result
  }

  /**
   * 数组分块
   */
  static chunk<T>(array: T[], size: number): T[][] {
    if (this.isEmpty(array) || size <= 0) {
      return []
    }
    
    const result: T[][] = []
    
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size))
    }
    
    return result
  }

  /**
   * 数组分组
   */
  static groupBy<T>(array: T[], options: GroupOptions<T>): Record<string, T[]> {
    if (this.isEmpty(array)) {
      return {}
    }
    
    const { key, preserveOrder = false } = options
    const getKey = typeof key === 'function' ? key : (item: T) => item[key]
    const result: Record<string, T[]> = {}
    const keyOrder: string[] = []
    
    array.forEach(item => {
      const keyValue = String(getKey(item))
      
      if (!result[keyValue]) {
        result[keyValue] = []
        if (preserveOrder) {
          keyOrder.push(keyValue)
        }
      }
      
      result[keyValue].push(item)
    })
    
    if (preserveOrder) {
      const orderedResult: Record<string, T[]> = {}
      keyOrder.forEach(key => {
        orderedResult[key] = result[key]
      })
      return orderedResult
    }
    
    return result
  }

  /**
   * 数组排序
   */
  static sort<T>(array: T[], options: SortOptions<T> = {}): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    const { key, order = 'asc', locale = 'zh-CN', numeric = false } = options
    const sorted = [...array]
    
    if (!key) {
      return sorted.sort((a, b) => {
        if (order === 'desc') {
          [a, b] = [b, a]
        }
        
        if (typeof a === 'string' && typeof b === 'string') {
          return a.localeCompare(b, locale, { numeric })
        }
        
        if (a < b) return -1
        if (a > b) return 1
        return 0
      })
    }
    
    const getKey = typeof key === 'function' ? key : (item: T) => item[key]
    
    return sorted.sort((a, b) => {
      let aValue = getKey(a)
      let bValue = getKey(b)
      
      if (order === 'desc') {
        [aValue, bValue] = [bValue, aValue]
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return aValue.localeCompare(bValue, locale, { numeric })
      }
      
      if (aValue < bValue) return -1
      if (aValue > bValue) return 1
      return 0
    })
  }

  /**
   * 多字段排序
   */
  static sortBy<T>(array: T[], ...sortOptions: SortOptions<T>[]): T[] {
    if (this.isEmpty(array) || sortOptions.length === 0) {
      return [...array]
    }
    
    return [...array].sort((a, b) => {
      for (const options of sortOptions) {
        const { key, order = 'asc', locale = 'zh-CN', numeric = false } = options
        
        if (!key) continue
        
        const getKey = typeof key === 'function' ? key : (item: T) => item[key]
        let aValue = getKey(a)
        let bValue = getKey(b)
        
        if (order === 'desc') {
          [aValue, bValue] = [bValue, aValue]
        }
        
        let result = 0
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          result = aValue.localeCompare(bValue, locale, { numeric })
        } else {
          if (aValue < bValue) result = -1
          else if (aValue > bValue) result = 1
        }
        
        if (result !== 0) {
          return result
        }
      }
      
      return 0
    })
  }

  /**
   * 数组洗牌
   */
  static shuffle<T>(array: T[]): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    const shuffled = [...array]
    
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    
    return shuffled
  }

  /**
   * 数组反转
   */
  static reverse<T>(array: T[]): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return [...array].reverse()
  }

  /**
   * 数组交集
   */
  static intersection<T>(...arrays: T[][]): T[] {
    if (arrays.length === 0) {
      return []
    }
    
    if (arrays.length === 1) {
      return this.unique(arrays[0])
    }
    
    const [first, ...rest] = arrays.filter(arr => this.isNotEmpty(arr))
    
    if (!first) {
      return []
    }
    
    return this.unique(first).filter(item => 
      rest.every(arr => arr.includes(item))
    )
  }

  /**
   * 数组并集
   */
  static union<T>(...arrays: T[][]): T[] {
    const combined = arrays.flat()
    return this.unique(combined)
  }

  /**
   * 数组差集
   */
  static difference<T>(array: T[], ...excludeArrays: T[][]): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    const excludeSet = new Set(excludeArrays.flat())
    return array.filter(item => !excludeSet.has(item))
  }

  /**
   * 数组对称差集
   */
  static symmetricDifference<T>(...arrays: T[][]): T[] {
    if (arrays.length === 0) {
      return []
    }
    
    if (arrays.length === 1) {
      return this.unique(arrays[0])
    }
    
    const allItems = arrays.flat()
    const counts = new Map<T, number>()
    
    allItems.forEach(item => {
      counts.set(item, (counts.get(item) || 0) + 1)
    })
    
    return Array.from(counts.entries())
      .filter(([, count]) => count === 1)
      .map(([item]) => item)
  }

  /**
   * 数组分页
   */
  static paginate<T>(array: T[], options: PaginationOptions): PaginationResult<T> {
    const { page, pageSize } = options
    const total = this.length(array)
    const totalPages = Math.ceil(total / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const data = this.isEmpty(array) ? [] : array.slice(startIndex, endIndex)
    
    return {
      data,
      total,
      page,
      pageSize,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }

  /**
   * 数组压缩（zip）
   */
  static zip<T>(...arrays: T[][]): T[][] {
    if (arrays.length === 0) {
      return []
    }
    
    const maxLength = Math.max(...arrays.map(arr => this.length(arr)))
    const result: T[][] = []
    
    for (let i = 0; i < maxLength; i++) {
      result.push(arrays.map(arr => arr[i]))
    }
    
    return result
  }

  /**
   * 数组解压缩（unzip）
   */
  static unzip<T>(array: T[][]): T[][] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return this.zip(...array)
  }

  /**
   * 数组转对象
   */
  static toObject<T>(array: T[], keySelector: keyof T | ((item: T) => string)): Record<string, T> {
    if (this.isEmpty(array)) {
      return {}
    }
    
    const getKey = typeof keySelector === 'function' ? keySelector : (item: T) => String(item[keySelector])
    const result: Record<string, T> = {}
    
    array.forEach(item => {
      const key = getKey(item)
      result[key] = item
    })
    
    return result
  }

  /**
   * 数组转Map
   */
  static toMap<T, K>(array: T[], keySelector: (item: T) => K): Map<K, T> {
    const map = new Map<K, T>()
    
    if (this.isEmpty(array)) {
      return map
    }
    
    array.forEach(item => {
      const key = keySelector(item)
      map.set(key, item)
    })
    
    return map
  }

  /**
   * 数组统计
   */
  static statistics<T>(array: T[]): ArrayStatistics {
    const count = this.length(array)
    
    if (count === 0) {
      return {
        count: 0,
        unique: 0,
        duplicates: 0,
        empty: 0,
        nonEmpty: 0
      }
    }
    
    const unique = this.unique(array).length
    const duplicates = count - unique
    const empty = array.filter(item => 
      item === null || item === undefined || item === '' || 
      (this.isArray(item) && item.length === 0) ||
      (typeof item === 'object' && Object.keys(item).length === 0)
    ).length
    const nonEmpty = count - empty
    
    return {
      count,
      unique,
      duplicates,
      empty,
      nonEmpty
    }
  }

  /**
   * 数组求和
   */
  static sum(array: number[]): number {
    if (this.isEmpty(array)) {
      return 0
    }
    
    return array.filter(num => typeof num === 'number' && !isNaN(num))
                .reduce((sum, num) => sum + num, 0)
  }

  /**
   * 数组平均值
   */
  static average(array: number[]): number {
    if (this.isEmpty(array)) {
      return 0
    }
    
    const validNumbers = array.filter(num => typeof num === 'number' && !isNaN(num))
    
    if (validNumbers.length === 0) {
      return 0
    }
    
    return this.sum(validNumbers) / validNumbers.length
  }

  /**
   * 数组最小值
   */
  static min(array: number[]): number {
    if (this.isEmpty(array)) {
      return 0
    }
    
    const validNumbers = array.filter(num => typeof num === 'number' && !isNaN(num))
    
    if (validNumbers.length === 0) {
      return 0
    }
    
    return Math.min(...validNumbers)
  }

  /**
   * 数组最大值
   */
  static max(array: number[]): number {
    if (this.isEmpty(array)) {
      return 0
    }
    
    const validNumbers = array.filter(num => typeof num === 'number' && !isNaN(num))
    
    if (validNumbers.length === 0) {
      return 0
    }
    
    return Math.max(...validNumbers)
  }

  /**
   * 数组中位数
   */
  static median(array: number[]): number {
    if (this.isEmpty(array)) {
      return 0
    }
    
    const validNumbers = array.filter(num => typeof num === 'number' && !isNaN(num))
                              .sort((a, b) => a - b)
    
    if (validNumbers.length === 0) {
      return 0
    }
    
    const middle = Math.floor(validNumbers.length / 2)
    
    if (validNumbers.length % 2 === 0) {
      return (validNumbers[middle - 1] + validNumbers[middle]) / 2
    } else {
      return validNumbers[middle]
    }
  }

  /**
   * 数组众数
   */
  static mode<T>(array: T[]): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    const frequency = new Map<T, number>()
    
    array.forEach(item => {
      frequency.set(item, (frequency.get(item) || 0) + 1)
    })
    
    const maxFreq = Math.max(...frequency.values())
    
    return Array.from(frequency.entries())
      .filter(([, freq]) => freq === maxFreq)
      .map(([item]) => item)
  }

  /**
   * 数组计数
   */
  static count<T>(array: T[], predicate?: (item: T, index: number) => boolean): number {
    if (this.isEmpty(array)) {
      return 0
    }
    
    if (!predicate) {
      return array.length
    }
    
    return array.filter(predicate).length
  }

  /**
   * 数组查找
   */
  static find<T>(array: T[], predicate: (item: T, index: number) => boolean): T | undefined {
    if (this.isEmpty(array)) {
      return undefined
    }
    
    return array.find(predicate)
  }

  /**
   * 数组查找索引
   */
  static findIndex<T>(array: T[], predicate: (item: T, index: number) => boolean): number {
    if (this.isEmpty(array)) {
      return -1
    }
    
    return array.findIndex(predicate)
  }

  /**
   * 数组查找最后一个
   */
  static findLast<T>(array: T[], predicate: (item: T, index: number) => boolean): T | undefined {
    if (this.isEmpty(array)) {
      return undefined
    }
    
    for (let i = array.length - 1; i >= 0; i--) {
      if (predicate(array[i], i)) {
        return array[i]
      }
    }
    
    return undefined
  }

  /**
   * 数组查找最后一个索引
   */
  static findLastIndex<T>(array: T[], predicate: (item: T, index: number) => boolean): number {
    if (this.isEmpty(array)) {
      return -1
    }
    
    for (let i = array.length - 1; i >= 0; i--) {
      if (predicate(array[i], i)) {
        return i
      }
    }
    
    return -1
  }

  /**
   * 数组过滤
   */
  static filter<T>(array: T[], predicate: (item: T, index: number) => boolean): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return array.filter(predicate)
  }

  /**
   * 数组映射
   */
  static map<T, U>(array: T[], mapper: (item: T, index: number) => U): U[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return array.map(mapper)
  }

  /**
   * 数组归约
   */
  static reduce<T, U>(array: T[], reducer: (acc: U, item: T, index: number) => U, initialValue: U): U {
    if (this.isEmpty(array)) {
      return initialValue
    }
    
    return array.reduce(reducer, initialValue)
  }

  /**
   * 数组检查是否所有元素都满足条件
   */
  static every<T>(array: T[], predicate: (item: T, index: number) => boolean): boolean {
    if (this.isEmpty(array)) {
      return true
    }
    
    return array.every(predicate)
  }

  /**
   * 数组检查是否有元素满足条件
   */
  static some<T>(array: T[], predicate: (item: T, index: number) => boolean): boolean {
    if (this.isEmpty(array)) {
      return false
    }
    
    return array.some(predicate)
  }

  /**
   * 数组包含检查
   */
  static includes<T>(array: T[], searchElement: T, fromIndex?: number): boolean {
    if (this.isEmpty(array)) {
      return false
    }
    
    return array.includes(searchElement, fromIndex)
  }

  /**
   * 数组索引查找
   */
  static indexOf<T>(array: T[], searchElement: T, fromIndex?: number): number {
    if (this.isEmpty(array)) {
      return -1
    }
    
    return array.indexOf(searchElement, fromIndex)
  }

  /**
   * 数组最后索引查找
   */
  static lastIndexOf<T>(array: T[], searchElement: T, fromIndex?: number): number {
    if (this.isEmpty(array)) {
      return -1
    }
    
    return array.lastIndexOf(searchElement, fromIndex)
  }

  /**
   * 数组切片
   */
  static slice<T>(array: T[], start?: number, end?: number): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return array.slice(start, end)
  }

  /**
   * 数组拼接
   */
  static concat<T>(...arrays: (T | T[])[]): T[] {
    const result: T[] = []
    
    arrays.forEach(item => {
      if (this.isArray(item)) {
        result.push(...item)
      } else {
        result.push(item)
      }
    })
    
    return result
  }

  /**
   * 数组连接为字符串
   */
  static join<T>(array: T[], separator = ','): string {
    if (this.isEmpty(array)) {
      return ''
    }
    
    return array.join(separator)
  }

  /**
   * 数组深拷贝
   */
  static clone<T>(array: T[]): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return JSON.parse(JSON.stringify(array))
  }

  /**
   * 数组浅拷贝
   */
  static copy<T>(array: T[]): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    return [...array]
  }

  /**
   * 数组相等比较
   */
  static equals<T>(array1: T[], array2: T[], deep = false): boolean {
    if (!this.isArray(array1) || !this.isArray(array2)) {
      return false
    }
    
    if (array1.length !== array2.length) {
      return false
    }
    
    for (let i = 0; i < array1.length; i++) {
      if (deep) {
        if (JSON.stringify(array1[i]) !== JSON.stringify(array2[i])) {
          return false
        }
      } else {
        if (array1[i] !== array2[i]) {
          return false
        }
      }
    }
    
    return true
  }

  /**
   * 数组移动元素
   */
  static move<T>(array: T[], fromIndex: number, toIndex: number): T[] {
    if (this.isEmpty(array) || fromIndex === toIndex) {
      return [...array]
    }
    
    const result = [...array]
    const [movedItem] = result.splice(fromIndex, 1)
    result.splice(toIndex, 0, movedItem)
    
    return result
  }

  /**
   * 数组插入元素
   */
  static insert<T>(array: T[], index: number, ...items: T[]): T[] {
    if (this.isEmpty(array) && index === 0) {
      return [...items]
    }
    
    const result = [...array]
    result.splice(index, 0, ...items)
    
    return result
  }

  /**
   * 数组删除元素
   */
  static remove<T>(array: T[], index: number, count = 1): T[] {
    if (this.isEmpty(array) || index < 0 || index >= array.length) {
      return [...array]
    }
    
    const result = [...array]
    result.splice(index, count)
    
    return result
  }

  /**
   * 数组替换元素
   */
  static replace<T>(array: T[], index: number, item: T): T[] {
    if (this.isEmpty(array) || index < 0 || index >= array.length) {
      return [...array]
    }
    
    const result = [...array]
    result[index] = item
    
    return result
  }

  /**
   * 数组清空
   */
  static clear<T>(): T[] {
    return []
  }

  /**
   * 数组填充
   */
  static fill<T>(array: T[], value: T, start?: number, end?: number): T[] {
    if (this.isEmpty(array)) {
      return []
    }
    
    const result = [...array]
    result.fill(value, start, end)
    
    return result
  }
}

/**
 * 数组管理器类
 */
export class ArrayManager<T> {
  private data: T[]

  constructor(initialData: T[] = []) {
    this.data = [...initialData]
  }

  /**
   * 获取数据
   */
  getData(): T[] {
    return [...this.data]
  }

  /**
   * 设置数据
   */
  setData(data: T[]): this {
    this.data = [...data]
    return this
  }

  /**
   * 添加元素
   */
  add(...items: T[]): this {
    this.data.push(...items)
    return this
  }

  /**
   * 插入元素
   */
  insert(index: number, ...items: T[]): this {
    this.data.splice(index, 0, ...items)
    return this
  }

  /**
   * 删除元素
   */
  remove(index: number, count = 1): this {
    this.data.splice(index, count)
    return this
  }

  /**
   * 替换元素
   */
  replace(index: number, item: T): this {
    if (index >= 0 && index < this.data.length) {
      this.data[index] = item
    }
    return this
  }

  /**
   * 清空数据
   */
  clear(): this {
    this.data = []
    return this
  }

  /**
   * 获取长度
   */
  length(): number {
    return this.data.length
  }

  /**
   * 检查是否为空
   */
  isEmpty(): boolean {
    return this.data.length === 0
  }

  /**
   * 获取元素
   */
  get(index: number): T | undefined {
    return this.data[index]
  }

  /**
   * 查找元素
   */
  find(predicate: (item: T, index: number) => boolean): T | undefined {
    return this.data.find(predicate)
  }

  /**
   * 过滤元素
   */
  filter(predicate: (item: T, index: number) => boolean): ArrayManager<T> {
    return new ArrayManager(this.data.filter(predicate))
  }

  /**
   * 映射元素
   */
  map<U>(mapper: (item: T, index: number) => U): ArrayManager<U> {
    return new ArrayManager(this.data.map(mapper))
  }

  /**
   * 排序
   */
  sort(options: SortOptions<T> = {}): this {
    this.data = ArrayUtils.sort(this.data, options)
    return this
  }

  /**
   * 去重
   */
  unique(key?: keyof T | ((item: T) => any)): this {
    this.data = ArrayUtils.unique(this.data, key)
    return this
  }

  /**
   * 洗牌
   */
  shuffle(): this {
    this.data = ArrayUtils.shuffle(this.data)
    return this
  }

  /**
   * 反转
   */
  reverse(): this {
    this.data = ArrayUtils.reverse(this.data)
    return this
  }

  /**
   * 分页
   */
  paginate(options: PaginationOptions): PaginationResult<T> {
    return ArrayUtils.paginate(this.data, options)
  }

  /**
   * 分组
   */
  groupBy(options: GroupOptions<T>): Record<string, T[]> {
    return ArrayUtils.groupBy(this.data, options)
  }

  /**
   * 统计
   */
  statistics(): ArrayStatistics {
    return ArrayUtils.statistics(this.data)
  }

  /**
   * 转换为数组
   */
  toArray(): T[] {
    return [...this.data]
  }

  /**
   * 转换为JSON字符串
   */
  toJSON(): string {
    return JSON.stringify(this.data)
  }

  /**
   * 克隆
   */
  clone(): ArrayManager<T> {
    return new ArrayManager(ArrayUtils.clone(this.data))
  }
}

/**
 * 创建数组管理器
 */
export function createArrayManager<T>(initialData?: T[]): ArrayManager<T> {
  return new ArrayManager(initialData)
}

// 导出工具实例
export const arrayUtils = ArrayUtils

// 导出快捷方法
export const {
  isArray,
  isEmpty,
  isNotEmpty,
  length,
  get,
  first,
  last,
  random,
  randomSample,
  create,
  range,
  unique,
  uniqueLast,
  duplicates,
  flatten,
  flattenDeep,
  chunk,
  groupBy,
  sort,
  sortBy,
  shuffle,
  reverse,
  intersection,
  union,
  difference,
  symmetricDifference,
  paginate,
  zip,
  unzip,
  toObject,
  toMap,
  statistics,
  sum,
  average,
  min,
  max,
  median,
  mode,
  count,
  find,
  findIndex,
  findLast,
  findLastIndex,
  filter,
  map,
  reduce,
  every,
  some,
  includes,
  indexOf,
  lastIndexOf,
  slice,
  concat,
  join,
  clone,
  copy,
  equals,
  move,
  insert,
  remove,
  replace,
  clear,
  fill
} = ArrayUtils