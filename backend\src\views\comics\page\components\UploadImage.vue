<template>
  <div class="upload-image">
    <el-upload
      :http-request="handleUpload"
      :show-file-list="false"
      accept="image/*"
      :multiple="allowMultiple"
      :before-upload="beforeUpload"
      class="upload-area"
      :disabled="disabled"
    >
      <div v-if="!modelValue" class="upload-placeholder">
        <el-button type="primary" :disabled="disabled">{{ buttonText }}</el-button>
        <div class="text-sm text-gray-500 mt-2">{{ tipText }}</div>
      </div>
      <el-image
        v-else
        :src="modelValue"
        :style="imageStyle"
        :preview-src-list="previewImages"
      />
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { uploadPictureWithProgress } from '@/service/api/upload';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  buttonText: {
    type: String,
    default: '点击上传图片'
  },
  tipText: {
    type: String,
    default: '支持多选，建议尺寸: 800x1200px'
  },
  subDir: {
    type: String,
    default: 'comics/pages'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  imageWidth: {
    type: [String, Number],
    default: '80px'
  },
  imageHeight: {
    type: [String, Number],
    default: '120px'
  },
  allowMultiple: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error']);

const imageStyle = computed(() => ({
  // width: typeof props.imageWidth === 'number' ? `${props.imageWidth}px` : props.imageWidth,
  height: typeof props.imageHeight === 'number' ? `${props.imageHeight}px` : props.imageHeight,
  objectFit: 'cover'
}));

const previewImages = computed(() => {
  return props.modelValue ? [props.modelValue] : [];
});

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }
  return true;
};

// 获取图片尺寸
const getImageDimensions = (url: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      });
    };
    img.onerror = reject;
    img.src = url;
  });
};

// 上传处理
const handleUpload = async (options: any) => {
  try {
    // 处理单个或多个文件
    let files = [options.file];

    // 如果是通过input[type=file]多选的文件，需要特殊处理
    if (options.file.name === undefined && options.file.length > 0) {
      files = Array.from(options.file);
    }

    if (!props.allowMultiple && files.length > 1) {
      files = [files[0]]; // 如果不允许多选，只取第一个文件
    }

    const uploadPromises = files.map(async (file: File) => {
      // 先检查文件类型
      if (!beforeUpload(file)) {
        throw new Error('文件格式或大小不符合要求');
      }

      const res = await uploadPictureWithProgress({ file, subDir: props.subDir });
      if (res.code === 2000 && res.data) {
        const imageUrl = res.data.url || (typeof res.data === 'string' ? res.data : '');
        const dimensions = await getImageDimensions(imageUrl);
        return {
          url: imageUrl,
          width: dimensions.width,
          height: dimensions.height
        };
      }
      throw new Error(res.message || '上传失败');
    });

    const results = await Promise.all(uploadPromises);

    if (results.length === 1) {
      emit('update:modelValue', results[0].url);
      emit('upload-success', results[0]);
    } else if (results.length > 1) {
      emit('update:modelValue', results[0].url);
      emit('upload-success', results);
    }

    ElMessage.success('上传成功');
  } catch (error: any) {
    emit('upload-error', error);
    ElMessage.error(error.message || '上传失败');
  }
};
</script>

<style scoped>
.upload-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.upload-area {
  cursor: pointer;
}
</style>
