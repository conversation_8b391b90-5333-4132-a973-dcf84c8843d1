<template>
  <div class="file-manager">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="left-controls">
        <!-- 返回上一级目录 -->
        <el-button
          v-if="currentPath !== ''"

          @click="goToParentDirectory"
        >
          <el-icon><back /></el-icon> 返回上级
        </el-button>

        <!-- 当前路径导航 -->
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item @click="navigateTo('')">根目录</el-breadcrumb-item>
            <template v-if="pathSegments&&pathSegments.length > 0">
              <el-breadcrumb-item
                v-for="(segment, index) in pathSegments"
                :key="index"
                @click="navigateToSegment(index)"
              >
                {{ segment }}
              </el-breadcrumb-item>
            </template>
          </el-breadcrumb>
        </div>
      </div>

      <div class="right-controls">
        <!-- 搜索框 -->
        <el-input
          v-model="searchQuery"
          placeholder="搜索文件..."
          clearable
          @input="debounceSearch"
          style="width: 200px"
        >
          <template #prefix>
            <el-icon><search /></el-icon>
          </template>
        </el-input>

        <!-- 文件类型选择 -->
        <el-select v-model="fileType" style="width: 100px" @change="loadFiles">
          <el-option label="大图片" value="pictures"></el-option>
          <el-option label="小图片" value="static"></el-option>
          <el-option label="视频" value="video"></el-option>
          <el-option label="全部" value="all"></el-option>
        </el-select>

        <!-- 操作按钮 -->
        <el-button-group>
          <el-button  type="primary" @click="openUploadDialog">
            <el-icon><upload-filled /></el-icon> 上传
          </el-button>
          <el-button  @click="openCreateFolderDialog">
            <el-icon><folder-add /></el-icon> 新建文件夹
          </el-button>
          <el-button  @click="refreshFiles">
            <el-icon><refresh /></el-icon> 刷新
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧目录树 -->
      <div class="directory-tree">
        <el-tree
          ref="directoryTreeRef"
          :data="directoryTree"
          :props="{ label: 'name', children: 'children' }"
          node-key="path"
          size="large"
          :expand-on-click-node="false"
          :default-expanded-keys="['']"
          :current-node-key="currentPath"
          highlight-current
          @node-click="handleTreeNodeClick"
          :load="loadNode"
          lazy
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <el-icon v-if="node.isLeaf"><folder /></el-icon>
              <el-icon v-else-if="node.expanded"><folder-opened /></el-icon>
              <el-icon v-else><folder /></el-icon>
              <span class="node-label">{{ node.label }}</span>
            </div>
          </template>
        </el-tree>
      </div>

      <!-- 右侧文件列表 -->
      <div class="file-container" v-loading="loading">
        <el-empty v-if="files.length === 0" description="没有找到文件"></el-empty>

        <div v-else class="file-grid">
          <div
            v-for="file in files"
            :key="file.path"
            class="file-item"
            :class="{
              'is-dir': file.is_dir,
              'selected': selectedFiles.some(f => f.path === file.path)
            }"
            @click="handleFileClick(file, $event)"
            @dblclick="handleFileDblClick(file)"
            @contextmenu.prevent="handleContextMenu($event, file)"
            @mouseenter="handleMouseEnter(file)"
            @mouseleave="handleMouseLeave"
          >
            <!-- 文件图标或预览 -->
            <div class="file-thumbnail">
              <!-- 目录 -->
              <el-icon v-if="file.is_dir" class="dir-icon"><folder /></el-icon>

              <!-- 图片预览 -->
              <el-image
                v-else-if="(file.type === 'pictures'||file.type === 'static')||['jpg','jpeg','png','gif','webp'].indexOf(file.extension)>=0"
                :src="file.url"
                fit="cover"
                class="image-thumbnail"
                loading="lazy"
              >
                <template #error>
                  <el-icon><picture /></el-icon>
                </template>
              </el-image>

              <!-- 视频预览 -->
              <div v-else-if="file.type === 'video'||file.type==='videos'" class="video-thumbnail">
                <video :src="file.url" class="video-src" controls></video>
              </div>

              <!-- 其他文件 -->
              <el-icon v-else><document /></el-icon>
            </div>

            <!-- 文件名 -->
            <div class="file-name" :title="file.name">{{ file.name }}</div>

            <!-- 文件信息 -->
            <div class="file-info">
              <span v-if="!file.is_dir">{{ formatFileSize(file.size) }}</span>
              <span v-if="file.is_dir">文件夹</span>
            </div>

            <!-- 悬浮操作按钮 -->
            <div class="file-actions" v-if="hoveredFile && hoveredFile.path === file.path && !isRootLevelDirectory(file)">
              <el-button
                circle
                size="small"
                @click.stop="renameFileDialog(file)"
                type="primary"
                :icon="Edit"
              ></el-button>
              <el-button
                circle
                size="small"
                @click.stop="confirmDeleteFile(file)"
                type="danger"
                :icon="Delete"
              ></el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 - 选择模式下的确认区域 -->
    <div class="footer" v-if="mode === 'selector'">
      <div class="selection-info">
        已选择: {{ selectedFiles.length }} 个文件
      </div>
      <div class="action-buttons">
        <el-button @click="cancelSelect">取消</el-button>
        <el-button type="primary" :disabled="!canConfirmSelection" @click="confirmSelection">确认选择</el-button>
      </div>
    </div>

    <!-- 上下文菜单 -->
    <div v-show="contextMenuVisible" class="context-menu" :style="contextMenuStyle" v-if="(isRootLevelDirectory(contextMenuTarget)&&!contextMenuTarget.is_dir) || (!isRootLevelDirectory(contextMenuTarget))">
      <ul>
        <li v-if="contextMenuTarget && !contextMenuTarget.is_dir" @click="previewFile(contextMenuTarget)">预览</li>
        <li v-if="!isRootLevelDirectory(contextMenuTarget)" @click="renameFileDialog(contextMenuTarget)">重命名</li>
        <li v-if="!isRootLevelDirectory(contextMenuTarget)" @click="confirmDeleteFile(contextMenuTarget)">删除</li>
      </ul>
    </div>

    <!-- 上传文件对话框 -->
    <upload-dialog
      v-model="uploadDialogVisible"
      :file-type="dirType" @uploaded="handleUploadSuccess"
      :current-path="currentPath"
      @upload-success="handleUploadSuccess"
    />

    <!-- 新建文件夹对话框 -->
    <create-folder-dialog
      v-model="createFolderDialogVisible"
      :file-type="dirType"
      :parent-path="currentPath"
      @folder-created="handleCreateFolderSuccess"
      @create-success="handleCreateFolderSuccess"
    />

    <!-- 重命名对话框 -->
    <rename-dialog
      v-model="renameDialogVisible"
      :file-type="fileType"
      :file="fileToRename"
      @rename-success="handleRenameSuccess"
    />

    <!-- 预览对话框 -->
    <preview-dialog
      v-model="previewDialogVisible"
      :file="fileToPreview"
    />
  </div>
</template>

<script setup lang="ts">
import { deleteFile, getFileList } from '@/service/api/system/files';
import {
    Back,
    Delete,
    Document,
    Edit,
    Folder,
    FolderAdd,
    FolderOpened,
    Refresh,
    Search,
    UploadFilled
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { CreateFolderDialog, PreviewDialog, RenameDialog, UploadDialog } from './components';

// 文件项类型定义
interface FileItem {
  name: string;
  url: string;
  path: string;
  size?: number;
  width?: number;
  height?: number;
  type?: string;
  extension?: string;
  is_dir?: boolean;
  [key: string]: any; // 允许其他属性
}

// 接收的属性
const props = defineProps({
  // 组件模式: 'manager' - 文件管理模式, 'selector' - 文件选择模式
  mode: {
    type: String,
    default: 'manager',
    validator: (value: string) => ['manager', 'selector'].includes(value)
  },
  // 初始文件类型
  initialFileType: {
    type: String,
    default: 'all'
  },
  // 是否允许多选（仅在选择模式下有效）
  multiple: {
    type: Boolean,
    default: false
  }
});

// 发出的事件
const emit = defineEmits(['select', 'cancel']);

// 状态变量
const loading = ref(false);
const files = ref<FileItem[]>([]);
const currentPath = ref('');
const dirType = ref('all');
const searchQuery = ref('');
const fileType = ref(props.initialFileType);
const selectedFiles = ref<FileItem[]>([]);
const hoveredFile = ref<FileItem | null>(null);

// 目录树相关
const directoryTreeRef = ref<any>(null);
const directoryTree = ref<any[]>([
  {
    name: '根目录',
    path: '',
    children: []
  }
]);

// 对话框控制
const uploadDialogVisible = ref(false);
const createFolderDialogVisible = ref(false);
const renameDialogVisible = ref(false);
const previewDialogVisible = ref(false);

// 操作目标文件
const fileToRename = ref<FileItem | null>(null);
const fileToPreview = ref<FileItem | null>(null);

// 上下文菜单相关
const contextMenuVisible = ref(false);
const contextMenuStyle = ref({
  top: '0px',
  left: '0px'
});
const contextMenuTarget = ref<FileItem | null>(null);

// 计算路径段
const pathSegments = computed(() => {
  return currentPath.value ? currentPath.value.split('/').filter(Boolean) : [];
});

// 是否可以确认选择
const canConfirmSelection = computed(() => {
  if (props.mode !== 'selector') return false;

  if (props.multiple) {
    // 多选模式：至少选择一个文件（不是目录）
    return selectedFiles.value.length > 0 && selectedFiles.value.every(file => !file.is_dir);
  } else {
    // 单选模式：选择了一个文件（不是目录）
    return selectedFiles.value.length === 1 && !selectedFiles.value[0].is_dir;
  }
});

// 加载文件列表
const loadFiles = async () => {
  // 更新 dirType 基于当前路径
  updateDirType();

  loading.value = true;
  try {
    // 根据fileType转换为后端期望的类型值
    let backendFileType = fileType.value;
    if (fileType.value === 'pictures') {
      backendFileType = 'image';
    } else if (fileType.value === 'static') {
      backendFileType = 'small_image';
    } else if (fileType.value === 'videos') {
      backendFileType = 'video';
    }

    const {response,data} = await getFileList({
      data: {
        file_type: backendFileType,
        path: currentPath.value,
        search: searchQuery.value
      }
    });

    if (response.data.code === 2000) {
      files.value = data.files || [];

      // 如果在查看子目录，找到当前目录的类型
      if (files.value.length > 0 && currentPath.value !== '') {
        // 所有文件应该有相同的类型，取第一个
        const fileType = files.value[0].type;
        if (fileType) {
          dirType.value = fileType;
        }
      }
    } else {
      files.value = [];
      ElMessage.error('获取文件列表失败');
    }
  } catch (error: any) {
    console.error('获取文件列表失败:', error);
    files.value = [];
    ElMessage.error('获取文件列表失败');
  } finally {
    loading.value = false;
  }
};

// 更新 dirType 基于当前路径
const updateDirType = () => {
  if (currentPath.value === '') {
    dirType.value = fileType.value === 'all' ? 'all' : fileType.value;
    return;
  }

  // 获取路径的第一个部分作为目录类型
  const pathParts = currentPath.value.split('/');
  if (pathParts.length > 0) {
    const firstSegment = pathParts[0];
    dirType.value = firstSegment;
  }
};

// 加载目录树节点
const loadNode = async (node: any, resolve: (data: any[]) => void) => {
  // 根节点直接返回预设的根目录
  if (node.level === 0) {
    return resolve(directoryTree.value);
  }

  try {
    const {response,data} = await getFileList({
      data: {
        file_type: "all",
        path: node.data.path || '',
        search: ''
      }
    });

    if (response.data.code === 2000) {
      // 只过滤出目录
      const directories = (data.files || [])
        .filter((file: FileItem) => file.is_dir)
        .map((dir: FileItem) => ({
          name: dir.name,
          path: dir.path,
          fileType: dir.type,
          children: [] // 子节点默认为空，懒加载
        }));
      resolve(directories);
    } else {
      resolve([]);
    }
  } catch (error) {
    console.error('加载目录树节点失败:', error);
    resolve([]);
  }
};

// 处理目录树节点点击
const handleTreeNodeClick = (data: any) => {
  // 如果节点具有指定的 fileType 属性，直接使用它更新 dirType
  if (data.type) {
    dirType.value = data.type;
  }

  navigateTo(data.path);
};

// 鼠标悬浮处理
const handleMouseEnter = (file: FileItem) => {
  hoveredFile.value = file;
};

const handleMouseLeave = () => {
  hoveredFile.value = null;
};

// 延迟搜索实现
let searchTimeout: any = null;
const debounceSearch = () => {
  if (searchTimeout) clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    loadFiles();
  }, 500);
};

// 刷新文件列表
const refreshFiles = () => {
  loadFiles();
};

// 导航到指定路径
const navigateTo = (path: string) => {
  currentPath.value = path;
  selectedFiles.value = [];
  loadFiles(); // 这会更新 dirType
};

// 导航到指定路径段
const navigateToSegment = (index: number) => {
  const segments = pathSegments.value.slice(0, index + 1);
  navigateTo(segments.join('/'));
};

// 返回上一级目录
const goToParentDirectory = () => {
  if (pathSegments.value.length > 0) {
    const newPath = pathSegments.value.slice(0, -1).join('/');
    navigateTo(newPath);
  }
};

// 处理文件点击
const handleFileClick = (file: FileItem, event: MouseEvent) => {
  if (props.mode === 'selector') {
    if (props.multiple) {
      // 多选模式
      const index = selectedFiles.value.findIndex(f => f.path === file.path);

      if (event.ctrlKey || event.metaKey) {
        // Ctrl/Cmd + 点击：切换选择状态
        if (index > -1) {
          selectedFiles.value.splice(index, 1);
        } else if (!file.is_dir) { // 只选择文件，不选择目录
          selectedFiles.value.push(file);
        }
      } else if (event.shiftKey && selectedFiles.value.length > 0 && !file.is_dir) {
        // Shift + 点击：选择范围
        const allFiles = files.value.filter(f => !f.is_dir);
        const lastSelected = selectedFiles.value[selectedFiles.value.length - 1];
        const lastIndex = allFiles.findIndex(f => f.path === lastSelected.path);
        const currentIndex = allFiles.findIndex(f => f.path === file.path);

        if (lastIndex !== -1 && currentIndex !== -1) {
          const start = Math.min(lastIndex, currentIndex);
          const end = Math.max(lastIndex, currentIndex);

          // 获取范围内的所有文件
          const filesToSelect = allFiles.slice(start, end + 1);

          // 将新选择的文件添加到选择列表中（避免重复）
          for (const f of filesToSelect) {
            if (!selectedFiles.value.some(selected => selected.path === f.path)) {
              selectedFiles.value.push(f);
            }
          }
        }
      } else {
        // 普通点击：在多选模式下切换选择状态
        if (!file.is_dir) { // 只选择文件，不选择目录
          if (index > -1) {
            // 如果已经选中，则取消选择
            selectedFiles.value.splice(index, 1);
          } else {
            // 如果未选中，则添加到选择列表
            selectedFiles.value.push(file);
          }
        } else {
          // 如果是目录，导航到该目录
          navigateTo(file.path);
        }
      }
    } else {
      // 单选模式
      if (!file.is_dir) {
        selectedFiles.value = [file];
      } else {
        // 如果是目录，导航到该目录
        navigateTo(file.path);
        selectedFiles.value = [];
      }
    }
  } else {
    // 非选择模式，如果是目录，导航到该目录
    if (file.is_dir) {
      navigateTo(file.path);
    }
  }
};

// 处理文件双击
const handleFileDblClick = (file: FileItem) => {
  if (file.is_dir) {
    // 导航到目录
    navigateTo(file.path);
  } else if (props.mode === 'selector') {
    // 选择模式下，双击文件直接确认选择
    if (props.multiple) {
      // 多选模式下，如果文件未被选中，则添加到选择列表
      if (!selectedFiles.value.some(f => f.path === file.path)) {
        selectedFiles.value.push(file);
      }
    } else {
      // 单选模式下，直接选择当前文件
      selectedFiles.value = [file];
    }
    confirmSelection();
  } else {
    // 管理模式下，双击文件预览
    previewFile(file);
  }
};

// 处理上下文菜单
const handleContextMenu = (event: MouseEvent, file: FileItem) => {
  // 设置上下文菜单位置
  contextMenuStyle.value = {
    top: `${event.clientY}px`,
    left: `${event.clientX}px`
  };

  // 设置目标文件并显示菜单
  contextMenuTarget.value = file;
  contextMenuVisible.value = true;

  // 阻止默认右键菜单
  event.preventDefault();
};

// 关闭上下文菜单
const closeContextMenu = () => {
  contextMenuVisible.value = false;
};

// 预览文件
const previewFile = (file: FileItem) => {
  if (file && !file.is_dir) {
    fileToPreview.value = file;
    previewDialogVisible.value = true;
    closeContextMenu();
  }
};

// 重命名文件对话框
const renameFileDialog = (file: FileItem) => {
  if (file) {
    // 检查是否是根目录下的一级目录
    if (isRootLevelDirectory(file)) {
      ElMessage.warning('不允许重命名根目录下的文件夹');
      return;
    }

    fileToRename.value = file;
    renameDialogVisible.value = true;
    closeContextMenu();
  }
};

// 确认删除文件
const confirmDeleteFile = (file: FileItem) => {
  if (!file) return;

  // 检查是否是根目录下的一级目录
  if (isRootLevelDirectory(file)) {
    ElMessage.warning('不允许删除根目录下的文件夹');
    return;
  }

  closeContextMenu();

  ElMessageBox.confirm(
    `确定要删除${file.is_dir ? '文件夹' : '文件'} "${file.name}" 吗？${file.is_dir ? '文件夹必须为空才能删除。' : ''}`,
    '确认删除',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deleteSelectedFile(file);
  }).catch(() => {
    // 取消删除
  });
};

// 删除选定的文件
const deleteSelectedFile = async (file: FileItem) => {
  loading.value = true;
  try {
    const {response,data} = await deleteFile({
      data: {
        file_type: fileType.value,
        path: file.path
      }
    });

    if (response.data.code === 2000) {
      ElMessage.success(`${file.is_dir ? '文件夹' : '文件'}已成功删除`);

      // 如果删除的是目录，刷新目录树和文件列表
      if (file.is_dir) {
        refreshAll();
      } else {
        // 只是删除文件，仅刷新文件列表
        refreshFiles();
      }
    } else {
      ElMessage.error(response.data.message || '删除失败');
    }
  } catch (error: any) {
    ElMessage.error(error.message || '删除失败');
  } finally {
    loading.value = false;
  }
};

// 打开上传对话框
const openUploadDialog = () => {
  updateDirType();
  uploadDialogVisible.value = true;
};

// 打开创建文件夹对话框
const openCreateFolderDialog = () => {
  // 检查是否在根目录，不允许在根目录创建文件夹
  if (currentPath.value === '') {
    ElMessage.warning('不允许在根目录创建文件夹');
    return;
  }
  createFolderDialogVisible.value = true;
};

// 处理上传成功
const handleUploadSuccess = (fileData: any) => {
  // 刷新文件列表
  refreshFiles();
};

// 处理创建文件夹成功
const handleCreateFolderSuccess = (folderData: any) => {
  // 刷新文件列表和目录树
  refreshAll();
};

// 处理重命名成功
const handleRenameSuccess = (fileData: any) => {
  // 检查是否是目录，如果是目录则刷新树
  if (fileData && fileData.is_dir) {
    refreshAll();
  } else {
    refreshFiles();
  }
};

// 确认选择（选择模式）
const confirmSelection = () => {
  // 确保只选择了文件而不是目录
  const fileOnly = selectedFiles.value.filter(file => !file.is_dir);

  if (fileOnly.length === 0) {
    ElMessage.warning('请选择至少一个文件');
    return;
  }

  // 为每个文件加载尺寸信息
  const loadFileInfo = async () => {
    const promises = fileOnly.map(file => {
      return new Promise<void>(resolve => {
        // 如果是图片，获取尺寸信息
        if (file.extension && ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(file.extension.toLowerCase())) {
          const img = new Image();
          img.onload = () => {
            file.width = img.width;
            file.height = img.height;
            resolve();
          };
          img.onerror = () => resolve();
          img.src = file.url;
        } else {
          resolve();
        }
      });
    });

    await Promise.all(promises);
    emit('select', fileOnly);
  };

  loadFileInfo();
};

// 取消选择（选择模式）
const cancelSelect = () => {
  emit('cancel');
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
};

// 检查是否是根目录下的一级目录
const isRootLevelDirectory = (file: FileItem | null): boolean => {
  if (!file || !file.is_dir) return false;

  // 检查路径是否只有一级，例如 "images" 而不是 "images/subfolder"
  const pathParts = file.path.split('/').filter(Boolean);
  return pathParts.length === 1;
};

// 添加全局点击事件监听器关闭上下文菜单
const handleGlobalClick = (event: MouseEvent) => {
  const contextMenu = document.querySelector('.context-menu');
  if (contextMenuVisible.value && contextMenu && !contextMenu.contains(event.target as Node)) {
    closeContextMenu();
  }
};

// 监听 fileType 变化
watch(fileType, () => {
  // 重置当前路径
  currentPath.value = '';
  selectedFiles.value = [];
  dirType.value = 'all'; // Reset dirType when fileType changes

  // 重新加载文件列表
  loadFiles();

  // 刷新目录树
  nextTick(() => {
    if (directoryTreeRef.value) {
      directoryTreeRef.value.setCurrentKey('');
      directoryTreeRef.value.store.setCurrentNode(directoryTreeRef.value.store.root.childNodes[0]);
    }
  });
});

// 监听 currentPath 变化，更新目录树选中状态
watch(currentPath, (newPath) => {
  nextTick(() => {
    if (directoryTreeRef.value) {
      directoryTreeRef.value.setCurrentKey(newPath);
    }
  });
});

// 加载目录树
const loadDirectoryTree = async () => {
  try {
    // 需要异步加载树控件中的数据
    directoryTree.value = [
      {
        name: '根目录',
        path: '',
        children: [] // 子节点默认为空，懒加载
      }
    ];

    // 在树控件引用可用时展开根节点
    nextTick(() => {
      if (directoryTreeRef.value) {
        // 选中当前路径
        directoryTreeRef.value.setCurrentKey(currentPath.value);
        // 展开根节点
        directoryTreeRef.value.store.root.childNodes[0].expand();
      }
    });
  } catch (error) {
    console.error('加载目录树失败:', error);
  }
};

// 重新加载树节点
const reloadTreeNode = (node: any) => {
  if (!directoryTreeRef.value) return;

  // 如果是根节点或没有指定节点，则重载整个树
  if (!node || node.level === 0) {
    loadDirectoryTree();
    return;
  }

  // 刷新特定节点
  if (node.loaded !== undefined) {
    node.loaded = false;
    if (node.childNodes) {
      node.childNodes = [];
    }
    if (typeof node.expand === 'function') {
      node.expand();
    }
  }
};

// 刷新文件列表和目录树
const refreshAll = () => {
  loadFiles(); // 刷新文件列表

  // 找到当前路径对应的节点
  if (directoryTreeRef.value) {
    const currentNode = findTreeNodeByPath(currentPath.value);
    if (currentNode) {
      // 如果找到当前节点，刷新它
      reloadTreeNode(currentNode);
    } else {
      // 如果找不到节点，刷新整个树
      loadDirectoryTree();
    }
  }
};

// 通过路径查找树节点
const findTreeNodeByPath = (path: string) => {
  if (!directoryTreeRef.value) return null;

  // 如果是根路径，返回根节点
  if (path === '') {
    return directoryTreeRef.value.store.root.childNodes[0];
  }

  // 获取路径的所有部分
  const pathParts = path.split('/');
  const rootNode = directoryTreeRef.value.store.root.childNodes[0];

  // 如果只有一级，直接查找根节点的子节点
  if (pathParts.length === 1) {
    return rootNode.childNodes.find((node: any) => node.data && node.data.path === path);
  }

  // 获取路径的第一部分（类型目录）
  const dirTypeValue = pathParts[0];

  // 查找第一级节点
  const typeNode = rootNode.childNodes.find((node: any) => node.data && node.data.path === dirTypeValue);
  if (!typeNode) return null;

  // 如果节点未加载，返回父节点
  if (!typeNode.loaded) return typeNode;

  // 依次找到路径对应的节点
  let currentNode = typeNode;
  for (let i = 1; i < pathParts.length; i++) {
    // 构建当前级别的路径
    const currentPath = pathParts.slice(0, i+1).join('/');

    // 在当前节点的子节点中查找
    const childNode = currentNode.childNodes.find((node: any) => node.data && node.data.path === currentPath);
    if (!childNode) return currentNode;

    // 如果子节点未加载，返回当前节点
    if (!childNode.loaded) return currentNode;

    currentNode = childNode;
  }

  return currentNode;
};

// 组件挂载后加载文件列表和目录树
onMounted(() => {
  // Initialize dirType based on the current path
  updateDirType();

  // 加载文件列表和目录树
  loadFiles();
  loadDirectoryTree();

  document.addEventListener('click', handleGlobalClick);
});

// 组件卸载前移除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('click', handleGlobalClick);
});
</script>

<style lang="scss">
.file-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e6e6e6;
}

.left-controls, .right-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 10px;
}

.breadcrumb {
  display: flex;
  align-items: center;
}

:deep(.el-breadcrumb__item) {
  cursor: pointer;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.directory-tree {
  width: 250px;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
  padding: 0;
  background-color: #f5f7fa;
  .el-tree{
    height:100%;
    overflow-y: auto;
    padding: 15px;
    .tree-node{
      font-size:18px;
      .el-icon{
      font-size: 18px;
    }
    :deep(.el-tree-node__label) {
      font-size: 18px; /* 根据需要调整字体大小 */
    }
    :deep(.el-tree-node__expand-icon) {
      font-size: 20px; /* 根据需要调整图标大小 */
    }
    }

  }
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 5px;
}

.node-label {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-container {
  flex: 1;
  overflow: auto;
  padding: 16px;
  background-color: #f5f7fa;
}

.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px; /* 减小间隔，从16px改为8px */
}

.file-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  aspect-ratio:1/1.1;
  min-height: 160px;
  padding: 6px;
  border-radius: 8px; /* More rounded corners like Windows 11 */
  cursor: pointer;
  transition: all 0.2s;
  overflow: hidden;
  background-color: #fff; /* Ensure background is white */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Subtle shadow for Windows 11 look */

  &:hover {
    background-color: #fff; /* Keep background white on hover */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover instead of background change */
  }

  &.selected {
    background-color: #ecf5ff;
    border: 1px solid #409EFF;
  }

  &.is-dir {
    .dir-icon {
      color: #0078d7; /* Windows 11 folder color */
      font-size: 48px; /* Increased folder icon size */
    }
  }

  .file-thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;

    height:60%;
    margin-bottom: 5px;
    padding-top:6px;

    .el-icon {
      font-size: 60px;
      color: #ffe318;
    }

    .image-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }

    .video-thumbnail {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: #f0f0f0;
      border-radius: 4px;

      .el-icon {
        font-size: 32px;
        color: #409EFF;
      }
    }
  }

  .file-name {
    width: 100%;
    text-align: center;
    font-size: 12px;
    margin: 2px 0;
    white-space: normal; /* Changed from nowrap to normal to allow wrapping */
    overflow: visible; /* Changed from hidden to visible */
    text-overflow: initial; /* Removed ellipsis */
    vertical-align: middle;
    line-height: 1.2; /* Added line height for better text display */
    display: -webkit-box; /* For multi-line ellipsis */
    -webkit-line-clamp: 2; /* Limit to 2 lines */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .file-info {
    font-size: 12px;
    color: #909399;
  }

  // 悬浮操作按钮
  .file-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 10;
  }
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-top: 1px solid #e6e6e6;
}

.selection-info {
  font-size: 14px;
  color: #606266;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.context-menu {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 5px 0;
  min-width: 120px;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      padding: 8px 16px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-size: 14px;

      &:hover {
        background-color: #f5f7fa;
        color: var(--el-color-primary);
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .directory-tree {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid #e6e6e6;
  }

  .file-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}
</style>
