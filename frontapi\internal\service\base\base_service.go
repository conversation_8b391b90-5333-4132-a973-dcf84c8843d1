package base

import (
	"context"
	"errors"
	"fmt"
	"frontapi/pkg/redis"
	"frontapi/pkg/types"
	"reflect"
	"time"

	"frontapi/internal/hooks"
	"frontapi/internal/models"
	baseRepo "frontapi/internal/repository/base"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseService 通用基础服务结构
type BaseService[T models.BaseModelConstraint] struct {
	repo        baseRepo.BaseRepository[T]
	cacheTTL    time.Duration
	hookManager *hooks.ServiceHookManager
	db          *gorm.DB
	entityType  string
}

// IBaseService 基础服务接口
type IBaseService[T models.BaseModelConstraint] interface {
	AbstractService
	// 基础CRUD操作
	Create(ctx context.Context, entity *T) (string, error)
	GetByID(ctx context.Context, id string, useCache bool) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error)
	UpdateById(ctx context.Context, id string, entity *T) error

	FindOne(ctx context.Context, query interface{}, args ...interface{}) (*T, error)

	FindAll(ctx context.Context, condition map[string]interface{}, orderBy string, useCache bool) ([]*T, error)

	UpdateColumn(ctx context.Context, condition map[string]interface{}, column string, value interface{}) error
	DeleteByCondition(ctx context.Context, condition map[string]interface{}) error
	// 批量操作
	BatchCreate(ctx context.Context, entities []*T) (int, error)
	BatchUpdate(ctx context.Context, entities []*T) (int, error)
	BatchDelete(ctx context.Context, ids []string) (int, error)
	BatchUpdateStatus(ctx context.Context, ids []string, status int) error
	Count(ctx context.Context, condition map[string]interface{}) (int64, error)

	// 查询操作
	FindByIDs(ctx context.Context, ids []string) ([]*T, error)

	// 计数操作
	UpdateCount(ctx context.Context, id string, field string, count int64) error
	UpdateCountByIDs(ctx context.Context, ids []string, field string, count int64) error

	// 状态操作
	UpdateStatus(ctx context.Context, id string, status int) error

	// 工具方法
	GenerateID() string
	SetEntityDefaults(entity *T)
	SetCacheTTL(ttl time.Duration)
}

// NewBaseService 创建通用基础服务实例
// 参数:
//   - repo: 基础仓储接口实例
//   - entityType: 实体类型标识
//
// 返回:
//   - *BaseService[T]: 基础服务实例
func NewBaseService[T models.BaseModelConstraint](repo baseRepo.BaseRepository[T], entityType string) *BaseService[T] {
	db := repo.GetDB()
	service := &BaseService[T]{
		repo:       repo,
		cacheTTL:   24 * time.Hour, // 默认缓存24小时
		db:         db,
		entityType: entityType,
	}

	// 初始化hooks管理器，使用repo中的DB连接
	service.hookManager = hooks.NewServiceHookManager(db, entityType)

	return service
}

// GenerateID 生成唯一ID
func (s *BaseService[T]) GenerateID() string {
	return uuid.New().String()
}

// SetEntityDefaults 设置实体默认值
func (s *BaseService[T]) SetEntityDefaults(entity *T) {
	now := types.JSONTime(time.Now())
	if (*entity).GetID() == "" {
		if ptr, ok := any(entity).(interface{ SetID(string) }); ok {
			ptr.SetID(s.GenerateID())
		}
	}
	if ptr, ok := any(entity).(interface{ SetCreatedAt(types.JSONTime) }); ok {
		ptr.SetCreatedAt(now)
	}
	if ptr, ok := any(entity).(interface{ SetUpdatedAt(types.JSONTime) }); ok {
		ptr.SetUpdatedAt(now)
	}
}

// Create 创建实体
func (s *BaseService[T]) Create(ctx context.Context, entity *T) (string, error) {
	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行创建前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, entity); err != nil {
			return "", fmt.Errorf("创建前钩子执行失败: %w", err)
		}
	}

	s.SetEntityDefaults(entity)
	err := s.repo.Create(ctx, entity)
	if err != nil {
		return "", fmt.Errorf("创建失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行创建后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterCreate, entity); err != nil {
			// 创建后钩子失败只记录日志，不影响创建结果
			// 这里可以添加日志记录
		}
	}

	return (*entity).GetID(), nil
}

// GetByID 根据ID获取实体（带缓存控制）
func (s *BaseService[T]) GetByID(ctx context.Context, id string, useCache bool) (*T, error) {
	if id == "" {
		return nil, errors.New("ID不能为空")
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := s.getCacheKey(id)
		var entity T
		if redis.GetJSON(cacheKey, &entity) == nil {
			return &entity, nil
		}
	}

	// 从数据库获取
	entity, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := s.getCacheKey(id)
		redis.SetJSON(cacheKey, *entity, s.cacheTTL)
	}

	return entity, nil
}

// FindOne 查找单个实体
func (s *BaseService[T]) FindOne(ctx context.Context, query interface{}, args ...interface{}) (*T, error) {
	return s.repo.FindOne(ctx, query, args...)
}

// Count 计数操作
func (s *BaseService[T]) Count(ctx context.Context, condition map[string]interface{}) (int64, error) {
	return s.repo.Count(ctx, condition)
}

// Update 更新实体
func (s *BaseService[T]) Update(ctx context.Context, entity *T) error {
	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, entity); err != nil {
			return fmt.Errorf("更新前钩子执行失败: %w", err)
		}
	}

	// 设置更新时间
	if ptr, ok := any(entity).(interface{ SetUpdatedAt(types.JSONTime) }); ok {
		ptr.SetUpdatedAt(types.JSONTime(time.Now()))
	}

	err := s.repo.Update(ctx, entity)
	if err != nil {
		return fmt.Errorf("更新失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, entity); err != nil {
			// 更新后钩子失败只记录日志，不影响更新结果
			// 这里可以添加日志记录
		}
	}

	// 删除缓存
	s.deleteCacheByID((*entity).GetID())

	return nil
}

// UpdateById 更新实体
func (s *BaseService[T]) UpdateById(ctx context.Context, id string, entity *T) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, entity); err != nil {
			return fmt.Errorf("更新前钩子执行失败: %w", err)
		}
	}

	// 设置更新时间
	if ptr, ok := any(entity).(interface{ SetUpdatedAt(types.JSONTime) }); ok {
		ptr.SetUpdatedAt(types.JSONTime(time.Now()))
	}

	err := s.repo.UpdateById(ctx, id, entity)
	if err != nil {
		return fmt.Errorf("更新失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, entity); err != nil {
			// 更新后钩子失败只记录日志，不影响更新结果
			// 这里可以添加日志记录
		}
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

func (s *BaseService[T]) UpdateColumn(ctx context.Context, condition map[string]interface{}, column string, value interface{}) error {
	// 创建更新数据对象用于钩子
	updateData := map[string]interface{}{
		"condition": condition,
		"column":    column,
		"value":     value,
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, updateData); err != nil {
			return fmt.Errorf("更新前钩子执行失败: %w", err)
		}
	}

	err := s.repo.UpdateColumn(ctx, condition, column, value)
	if err != nil {
		return fmt.Errorf("更新列失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, updateData); err != nil {
			// 更新后钩子失败只记录日志，不影响更新结果
		}
	}

	return nil
}
func (s *BaseService[T]) DeleteByCondition(ctx context.Context, condition map[string]interface{}) error {
	return s.repo.DeleteByCondition(ctx, condition)
}

// Delete 删除实体
func (s *BaseService[T]) Delete(ctx context.Context, id string) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 获取实体用于钩子执行
	entity, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("获取实体失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行删除前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeDelete, entity); err != nil {
			return fmt.Errorf("删除前钩子执行失败: %w", err)
		}
	}

	err = s.repo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("删除失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行删除后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterDelete, entity); err != nil {
			// 删除后钩子失败只记录日志，不影响删除结果
			// 这里可以添加日志记录
		}
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

// List 获取实体列表（带缓存控制）
func (s *BaseService[T]) List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := BuildListCacheKey("entity", condition, page, pageSize)
		var result struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}
		if redis.GetJSON(cacheKey, &result) == nil {
			return result.Items, result.Total, nil
		}
	}

	// 从数据库获取
	items, total, err := s.repo.List(ctx, condition, orderBy, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取列表失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := BuildListCacheKey("entity", condition, page, pageSize)
		result := struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}{
			Items: items,
			Total: total,
		}
		redis.SetJSON(cacheKey, result, s.cacheTTL)
	}

	return items, total, nil
}

func (s *BaseService[T]) FindAll(ctx context.Context, condition map[string]interface{}, orderBy string, useCache bool) ([]*T, error) {
	if useCache {
		cacheKey := BuildConditionCacheKey("entity", condition)
		var result []*T
		if redis.GetJSON(cacheKey, &result) == nil {
			return result, nil
		}
	}
	return s.repo.FindAll(ctx, condition, orderBy)
}

// ListWithModel 获取实体列表（带缓存控制）
func (s *BaseService[T]) ListWithModel(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// 如果启用缓存，尝试从缓存获取
	if useCache {
		cacheKey := BuildListCacheKey("entity", condition, page, pageSize)
		var result struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}
		if redis.GetJSON(cacheKey, &result) == nil {
			return result.Items, result.Total, nil
		}
	}

	// 从数据库获取
	items, total, err := s.repo.List(ctx, condition, orderBy, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("获取列表失败: %w", err)
	}

	// 如果启用缓存，缓存结果
	if useCache {
		cacheKey := BuildListCacheKey("entity", condition, page, pageSize)
		result := struct {
			Items []*T  `json:"items"`
			Total int64 `json:"total"`
		}{
			Items: items,
			Total: total,
		}
		redis.SetJSON(cacheKey, result, s.cacheTTL)
	}

	return items, total, nil
}

// BatchCreate 批量创建实体
func (s *BaseService[T]) BatchCreate(ctx context.Context, entities []*T) (int, error) {
	if len(entities) == 0 {
		return 0, errors.New("实体列表不能为空")
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量创建前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchCreate, entities); err != nil {
			return 0, fmt.Errorf("批量创建前钩子执行失败: %w", err)
		}
	}

	// 设置默认值
	for _, entity := range entities {
		s.SetEntityDefaults(entity)
	}

	successCount, err := s.repo.BatchCreate(ctx, entities)
	if err != nil {
		return 0, fmt.Errorf("批量创建失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量创建后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchCreate, entities); err != nil {
			// 创建后钩子失败只记录日志，不影响创建结果
			// 这里可以添加日志记录
		}
	}

	return successCount, nil
}

// BatchUpdate 批量更新实体
func (s *BaseService[T]) BatchUpdate(ctx context.Context, entities []*T) (int, error) {
	if len(entities) == 0 {
		return 0, errors.New("实体列表不能为空")
	}

	// 验证实体ID
	for _, entity := range entities {
		if (*entity).GetID() == "" {
			return 0, errors.New("实体ID不能为空")
		}
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量更新前钩子
		for _, entity := range entities {
			if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, entity); err != nil {
				return 0, fmt.Errorf("批量更新前钩子执行失败: %w", err)
			}
		}
	}

	// 设置更新时间
	for _, entity := range entities {
		if ptr, ok := any(entity).(interface{ SetUpdatedAt(types.JSONTime) }); ok {
			ptr.SetUpdatedAt(types.JSONTime(time.Now()))
		}
	}

	err := s.repo.BatchUpdate(ctx, entities)
	if err != nil {
		return 0, fmt.Errorf("批量更新失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量更新后钩子
		for _, entity := range entities {
			if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, entity); err != nil {
				// 更新后钩子失败只记录日志，不影响更新结果
				// 这里可以添加日志记录
			}
		}
	}

	// 删除相关缓存
	for _, entity := range entities {
		s.deleteCacheByID((*entity).GetID())
	}

	return len(entities), nil
}

// BatchDelete 批量删除实体
func (s *BaseService[T]) BatchDelete(ctx context.Context, ids []string) (int, error) {
	if len(ids) == 0 {
		return 0, errors.New("ID列表不能为空")
	}

	// 验证ID
	for _, id := range ids {
		if id == "" {
			return 0, errors.New("ID不能为空")
		}
	}

	// 获取实体用于钩子执行
	entities := make([]*T, 0, len(ids))
	for _, id := range ids {
		entity, err := s.repo.FindByID(ctx, id)
		if err != nil {
			return 0, fmt.Errorf("获取实体失败: %w", err)
		}
		if entity != nil {
			entities = append(entities, entity)
		}
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量删除前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchDelete, entities); err != nil {
			return 0, fmt.Errorf("批量删除前钩子执行失败: %w", err)
		}
	}

	err := s.repo.BatchDelete(ctx, ids)
	if err != nil {
		return 0, fmt.Errorf("批量删除失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量删除后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchDelete, entities); err != nil {
			// 删除后钩子失败只记录日志，不影响删除结果
			// 这里可以添加日志记录
		}
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return len(ids), nil
}

// BatchUpdateStatus 批量更新状态
// 注意：此方法需要实体支持status字段，如果base repository不支持，需要逐个更新
func (s *BaseService[T]) BatchUpdateStatus(ctx context.Context, ids []string, status int) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}

	// 验证ID
	for _, id := range ids {
		if id == "" {
			return errors.New("ID不能为空")
		}
	}

	// 创建批量更新数据对象用于钩子
	batchUpdateData := map[string]interface{}{
		"ids":    ids,
		"status": status,
		"type":   "batch_update_status",
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量更新前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchUpdate, batchUpdateData); err != nil {
			return fmt.Errorf("批量更新前钩子执行失败: %w", err)
		}
	}

	// 直接使用仓库层的BatchUpdateStatus方法
	err := s.repo.BatchUpdateStatus(ctx, ids, status)
	if err != nil {
		return fmt.Errorf("批量更新状态失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchUpdate, batchUpdateData); err != nil {
			// 批量更新后钩子失败只记录日志，不影响更新结果
		}
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return nil
}

// FindByIDs 根据ID列表查找实体
func (s *BaseService[T]) FindByIDs(ctx context.Context, ids []string) ([]*T, error) {
	if len(ids) == 0 {
		return nil, errors.New("ID列表不能为空")
	}

	// 验证ID
	for _, id := range ids {
		if id == "" {
			return nil, errors.New("ID不能为空")
		}
	}

	entities, err := s.repo.FindByIDs(ctx, ids)
	if err != nil {
		return nil, fmt.Errorf("根据ID列表查找实体失败: %w", err)
	}

	return entities, nil
}

// UpdateCountByIDs 批量更新计数字段
func (s *BaseService[T]) UpdateCountByIDs(ctx context.Context, ids []string, field string, count int64) error {
	if len(ids) == 0 {
		return errors.New("ID列表不能为空")
	}
	if field == "" {
		return errors.New("字段名不能为空")
	}

	// 验证ID
	for _, id := range ids {
		if id == "" {
			return errors.New("ID不能为空")
		}
	}

	// 创建批量更新数据对象用于钩子
	batchUpdateData := map[string]interface{}{
		"ids":   ids,
		"field": field,
		"count": count,
		"type":  "batch_update_count",
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量更新前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeBatchUpdate, batchUpdateData); err != nil {
			return fmt.Errorf("批量更新前钩子执行失败: %w", err)
		}
	}

	err := s.repo.UpdateCountByIDs(ctx, ids, field, count)
	if err != nil {
		return fmt.Errorf("批量更新计数失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行批量更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterBatchUpdate, batchUpdateData); err != nil {
			// 批量更新后钩子失败只记录日志，不影响更新结果
		}
	}

	// 删除相关缓存
	for _, id := range ids {
		s.deleteCacheByID(id)
	}

	return nil
}

// UpdateCount 更新计数字段
func (s *BaseService[T]) UpdateCount(ctx context.Context, id string, field string, count int64) error {
	if id == "" {
		return errors.New("ID不能为空")
	}
	if field == "" {
		return errors.New("字段名不能为空")
	}

	// 创建更新数据对象用于钩子
	updateData := map[string]interface{}{
		"id":    id,
		"field": field,
		"count": count,
		"type":  "update_count",
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新前钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, updateData); err != nil {
			return fmt.Errorf("更新前钩子执行失败: %w", err)
		}
	}

	// 使用UpdateCountByIDs方法
	err := s.repo.UpdateCountByIDs(ctx, []string{id}, field, count)
	if err != nil {
		return fmt.Errorf("更新计数失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, updateData); err != nil {
			// 更新后钩子失败只记录日志，不影响更新结果
		}
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

// UpdateStatus 更新状态
func (s *BaseService[T]) UpdateStatus(ctx context.Context, id string, status int) error {
	if id == "" {
		return errors.New("ID不能为空")
	}

	// 执行更新前钩子
	updateData := map[string]interface{}{
		"id":     id,
		"status": status,
		"type":   "update_status",
	}
	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeUpdate, updateData); err != nil {
			return fmt.Errorf("更新前钩子执行失败: %w", err)
		}
	}

	// 直接使用仓库层的UpdateStatus方法
	err := s.repo.UpdateStatus(ctx, id, status)
	if err != nil {
		return fmt.Errorf("更新状态失败: %w", err)
	}

	// 检查钩子管理器是否为空
	if s.hookManager != nil {
		// 执行更新后钩子
		if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterUpdate, updateData); err != nil {
			// 更新后钩子失败只记录日志，不影响更新结果
		}
	}

	// 删除缓存
	s.deleteCacheByID(id)

	return nil
}

// getCacheKey 获取缓存键
func (s *BaseService[T]) getCacheKey(id string) string {
	// 这里需要根据实体类型生成缓存键
	// 暂时使用通用格式
	return fmt.Sprintf("entity:%s", id)
}

// deleteCacheByID 根据ID删除缓存
func (s *BaseService[T]) deleteCacheByID(id string) {
	cacheKey := s.getCacheKey(id)
	redis.Del(cacheKey)
}

// SetCacheTTL 设置缓存过期时间
func (s *BaseService[T]) SetCacheTTL(ttl time.Duration) {
	s.cacheTTL = ttl
}

// ValidatePageParams 验证分页参数
func ValidatePageParams(page, pageSize int) (int, int) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}
	return page, pageSize
}

// BuildCacheKey 构建缓存键
func BuildCacheKey(prefix, id string) string {
	return fmt.Sprintf("%s:%s", prefix, id)
}

func BuildConditionCacheKey(prefix string, condition map[string]interface{}) string {
	return fmt.Sprintf("%s:%v", prefix, condition)
}

// BuildListCacheKey 构建列表缓存键
func BuildListCacheKey(prefix string, condition map[string]interface{}, page, pageSize int) string {
	return fmt.Sprintf("%s:list:%v:%d:%d", prefix, condition, page, pageSize)
}

// GetRepo 获取仓库实例
func (s *BaseService[T]) GetRepo() baseRepo.BaseRepository[T] {
	return s.repo
}

// GetHookManager 获取钩子管理器
func (s *BaseService[T]) GetHookManager() *hooks.ServiceHookManager {
	return s.hookManager
}

// RegisterDuplicateCheck 注册重复性检查钩子
func (s *BaseService[T]) RegisterDuplicateCheck(tableName string, fields []string, message string) {
	s.hookManager.RegisterDuplicateCheck(tableName, fields, message)
}

// RegisterDataCleaning 注册数据清洗钩子
func (s *BaseService[T]) RegisterDataCleaning(trimFields, lowerFields, upperFields []string, defaultValues map[string]interface{}) {
	s.hookManager.RegisterDataCleaning(trimFields, lowerFields, upperFields, defaultValues)
}

// RegisterAuditHook 注册审计钩子
func (s *BaseService[T]) RegisterAuditHook(userID string) {
	s.hookManager.RegisterAuditHook(userID)
}

// RegisterTimestampHook 注册时间戳钩子
func (s *BaseService[T]) RegisterTimestampHook(createField, updateField string) {
	s.hookManager.RegisterTimestampHook(createField, updateField)
}

// RegisterValidationHook 注册验证钩子
func (s *BaseService[T]) RegisterValidationHook(rules map[string]interface{}) {
	s.hookManager.RegisterValidationHook(rules)
}

// SetupCommonHooks 设置通用钩子
func (s *BaseService[T]) SetupCommonHooks(setup *hooks.CommonHooksSetup) {
	integrator := hooks.NewServiceIntegrator(s.db)
	s.hookManager = integrator.IntegrateHooks(s.entityType, setup.TableName, func(manager *hooks.ServiceHookManager) {
		// 注册重复检查钩子
		if len(setup.DuplicateFields) > 0 {
			manager.RegisterDuplicateCheck(setup.TableName, setup.DuplicateFields, "数据重复")
		}

		// 注册数据清洗钩子
		if len(setup.TrimFields) > 0 || len(setup.LowerFields) > 0 || len(setup.UpperFields) > 0 || len(setup.DefaultValues) > 0 {
			manager.RegisterDataCleaning(setup.TrimFields, setup.LowerFields, setup.UpperFields, setup.DefaultValues)
		}

		// 注册审计钩子
		if setup.EnableAudit && setup.UserID != "" {
			manager.RegisterAuditHook(setup.UserID)
		}

		// 注册时间戳钩子
		if setup.EnableTimestamp {
			createField := setup.CreateTimeField
			updateField := setup.UpdateTimeField
			if createField == "" {
				createField = "created_at"
			}
			if updateField == "" {
				updateField = "updated_at"
			}
			manager.RegisterTimestampHook(createField, updateField)
		}

		// 注册验证钩子
		if len(setup.ValidationRules) > 0 {
			rules := make(map[string]interface{})
			for field, validationRules := range setup.ValidationRules {
				rules[field] = validationRules
			}
			manager.RegisterValidationHook(rules)
		}
	})
}

// setEntityStatus 通过反射设置实体的status字段
func (s *BaseService[T]) setEntityStatus(entity *T, status int) error {
	if entity == nil {
		return errors.New("实体不能为空")
	}

	v := reflect.ValueOf(entity).Elem()
	if !v.IsValid() {
		return errors.New("无效的实体")
	}

	// 查找Status字段
	statusField := v.FieldByName("Status")
	if !statusField.IsValid() {
		return errors.New("实体没有Status字段")
	}

	if !statusField.CanSet() {
		return errors.New("Status字段不可设置")
	}

	// 设置status值
	switch statusField.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		statusField.SetInt(int64(status))
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		statusField.SetUint(uint64(status))
	default:
		return fmt.Errorf("不支持的Status字段类型: %v", statusField.Kind())
	}

	return nil
}

// UpdateViewCount 更新浏览次数
func (s *BaseService[T]) UpdateViewCount(ctx context.Context, id string, count int64) error {
	return s.UpdateCount(ctx, id, "view_count", count)
}

// UpdateLikeCount 更新点赞次数
func (s *BaseService[T]) UpdateLikeCount(ctx context.Context, id string, count int64) error {
	return s.UpdateCount(ctx, id, "like_count", count)
}

// UpdateCommentCount 更新评论次数
func (s *BaseService[T]) UpdateCommentCount(ctx context.Context, id string, count int64) error {
	return s.UpdateCount(ctx, id, "comment_count", count)
}

// UpdateShareCount 更新分享次数
func (s *BaseService[T]) UpdateShareCount(ctx context.Context, id string, count int64) error {
	return s.UpdateCount(ctx, id, "share_count", count)
}

// 导出extlike包中的类型，以便其他包使用
type (
	// MongoLikeService MongoDB点赞服务接口
	MongoLikeService = interface{}

	// LikeableService 可点赞服务接口
	LikeableService = interface{}

	// LikeableServiceV2 可点赞服务V2接口
	LikeableServiceV2 = interface{}

	// SimpleLikeService 简化的点赞服务接口
	SimpleLikeService = interface{}

	// LikeableServiceImpl 可点赞服务实现
	LikeableServiceImpl struct {
		// 嵌入字段，实际实现由具体的extlike包提供
		Impl interface{}
	}

	// LikeableServiceV2Impl 可点赞服务V2实现
	LikeableServiceV2Impl struct {
		// 嵌入字段，实际实现由具体的extlike包提供
		Impl interface{}
	}

	// SimpleLikeServiceImpl 简化的点赞服务实现
	SimpleLikeServiceImpl struct {
		// 嵌入字段，实际实现由具体的extlike包提供
		Impl interface{}
	}
)

// NewLikeableServiceImpl 创建可点赞服务实现（占位符）
func NewLikeableServiceImpl(args ...interface{}) *LikeableServiceImpl {
	return &LikeableServiceImpl{Impl: nil}
}

// NewLikeableServiceV2Impl 创建可点赞服务V2实现（占位符）
func NewLikeableServiceV2Impl(args ...interface{}) *LikeableServiceV2Impl {
	return &LikeableServiceV2Impl{Impl: nil}
}

// NewSimpleLikeServiceImpl 创建简化的点赞服务实现（占位符）
func NewSimpleLikeServiceImpl(args ...interface{}) *SimpleLikeServiceImpl {
	return &SimpleLikeServiceImpl{Impl: nil}
}
