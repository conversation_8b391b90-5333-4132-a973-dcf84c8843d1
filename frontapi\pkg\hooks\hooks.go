package hooks

import (
	"context"
	"sort"
)

// HookFunc 钩子函数类型
type HookFunc func(ctx context.Context, data interface{}) error

// Hook 钩子结构
type Hook struct {
	Name     string   // 钩子名称
	Priority int      // 优先级（数字越小优先级越高）
	Func     HookFunc // 钩子函数
}

// HookManager 钩子管理器
type HookManager struct {
	hooks map[string][]Hook // 按钩子点组织的钩子列表
}

// NewHookManager 创建新的钩子管理器
func NewHookManager() *HookManager {
	return &HookManager{
		hooks: make(map[string][]Hook),
	}
}

// RegisterHook 注册钩子
func (m *HookManager) RegisterHook(hookPoint string, hook Hook) {
	if _, exists := m.hooks[hookPoint]; !exists {
		m.hooks[hookPoint] = []Hook{}
	}
	m.hooks[hookPoint] = append(m.hooks[hookPoint], hook)

	// 按优先级排序
	sort.Slice(m.hooks[hookPoint], func(i, j int) bool {
		return m.hooks[hookPoint][i].Priority < m.hooks[hookPoint][j].Priority
	})
}

// ExecuteHooks 执行指定钩子点的所有钩子
func (m *HookManager) ExecuteHooks(ctx context.Context, hookPoint string, data interface{}) error {
	if hooks, exists := m.hooks[hookPoint]; exists {
		for _, hook := range hooks {
			if err := hook.Func(ctx, data); err != nil {
				return err
			}
		}
	}
	return nil
}

// 预定义钩子点常量
const (
	// 帖子相关钩子点
	HookBeforePostCreate = "before_post_create"
	HookAfterPostCreate  = "after_post_create"
	HookBeforePostUpdate = "before_post_update"
	HookAfterPostUpdate  = "after_post_update"
	HookBeforePostDelete = "before_post_delete"
	HookAfterPostDelete  = "after_post_delete"

	// 评论相关钩子点
	HookBeforeCommentCreate = "before_comment_create"
	HookAfterCommentCreate  = "after_comment_create"
	HookBeforeCommentDelete = "before_comment_delete"
	HookAfterCommentDelete  = "after_comment_delete"

	// 视频相关钩子点
	HookBeforeVideoCreate = "before_video_create"
	HookAfterVideoCreate  = "after_video_create"
	HookBeforeVideoUpdate = "before_video_update"
	HookAfterVideoUpdate  = "after_video_update"

	// 用户相关钩子点
	HookBeforeUserRegister = "before_user_register"
	HookAfterUserRegister  = "after_user_register"
	HookBeforeUserLogin    = "before_user_login"
	HookAfterUserLogin     = "after_user_login"
)

// GlobalHookManager 全局钩子管理器
var GlobalHookManager = NewHookManager()
