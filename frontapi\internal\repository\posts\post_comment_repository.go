package posts

import (
	"frontapi/internal/models/posts"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// Comment 评论实体

// CommentRepository 评论仓库接口
type CommentRepository interface {
	base.ExtendedRepository[posts.PostComment]
}

// CommentRepositoryImpl 评论仓库实现
type CommentRepositoryImpl struct {
	base.ExtendedRepository[posts.PostComment]
}

// NewCommentRepository 创建评论仓库
func NewCommentRepository(db *gorm.DB) CommentRepository {
	return &CommentRepositoryImpl{
		ExtendedRepository: base.NewExtendedRepository[posts.PostComment](db),
	}
}
