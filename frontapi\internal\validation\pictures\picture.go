package pictures

// PictureCreateRequest 图片创建请求验证模型
// CreatePictureRequest 创建图片请求
type CreatePictureRequest struct {
	URL         string `json:"url" validate:"required"`
	Title       string `json:"title" validate:"required|minLen:2|maxLen:100"`
	Description string `json:"description"`
	Width       int    `json:"width"`
	Height      int    `json:"height"`
	Size        int    `json:"size"`
	CategoryID  string `json:"category_id"`
	AlbumID     string `json:"album_id"`
	SortOrder   int    `json:"sort_order"`
	Status      int    `json:"status"`
	CreatorID   string `json:"-"` // 从登录信息中获取
}

// UpdatePictureRequest 更新图片请求
type UpdatePictureRequest struct {
	Title       string `json:"title" validate:"minLen:2|maxLen:100"`
	Description string `json:"description"`
	CategoryID  string `json:"category_id"`
	AlbumID     string `json:"album_id"`
	SortOrder   int    `json:"sort_order"`
	Status      int    `json:"status"`
}

// BatchCreatePicturesRequest 批量创建图片请求
type BatchCreatePicturesRequest struct {
	Pictures []CreatePictureRequest `json:"pictures" validate:"required|minLen:1"`
}

// UpdatePictureStatusRequest 更新图片状态请求
type UpdatePictureStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status" validate:"int|min:-5|max:5"`
	Reason string `json:"reason"`
}

// BatchUpdatePictureStatusRequest 批量更新图片状态请求
type BatchUpdatePictureStatusRequest struct {
	IDs    []string `json:"ids" validate:"required"`
	Status int      `json:"status" validate:"int|min:-5|max:5"`
	Reason string   `json:"reason"`
}

// BatchDeletePictureRequest 批量删除图片请求
type BatchDeletePictureRequest struct {
	IDs []string `json:"ids" validate:"required"`
}
