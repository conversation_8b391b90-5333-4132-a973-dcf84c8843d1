package videos

import (
	"fmt"
	"frontapi/internal/admin"
	"frontapi/internal/service/videos"
	videoValidator "frontapi/internal/validation/videos"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ==============================
// 视频评论相关方法
// ==============================
type VideoCommentController struct {
	admin.BaseController
	commentService videos.VideoCommentService
}

func NewVideoCommentController(
	commentService videos.VideoCommentService,
) *VideoCommentController {
	return &VideoCommentController{
		commentService: commentService,
	}
}

// GetComment 获取评论详情
func (h *VideoCommentController) GetComment(c *fiber.Ctx) error {
	commentID := c.Params("id")
	if commentID == "" {
		return h.BadRequest(c, "评论ID不能为空", nil)
	}

	comment, err := h.commentService.GetByID(c.Context(), commentID, false)
	if err != nil {
		return h.NotFound(c, "评论不存在")
	}

	return h.Success(c, comment)
}

// DeleteComment 删除评论
func (h *VideoCommentController) DeleteComment(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := utils.GetRequestInfo(c)

	// 检查用户登录状态
	if !reqInfo.IsAuthUser {
		return h.Unauthorized(c, "用户未登录")
	}

	commentID := c.Params("id")
	if commentID == "" {
		return h.BadRequest(c, "评论ID不能为空", nil)
	}

	// 检查评论是否属于当前用户
	comment, err := h.commentService.GetByID(c.Context(), commentID, false)
	if err != nil {
		return h.InternalServerError(c, "获取评论详情失败: "+err.Error())
	}

	if comment == nil {
		return h.NotFound(c, "评论不存在")
	}

	// 检查用户权限，只有评论作者或管理员可以删除评论
	// isAdmin := h.IsAdmin(c)
	// if comment.UserID != reqInfo.UserID && !isAdmin {
	// 	return h.Forbidden(c, "无权操作该评论")
	// }

	// 删除评论
	err = h.commentService.Delete(c.Context(), commentID)
	if err != nil {
		return h.InternalServerError(c, "删除评论失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "删除评论成功")
}

// ListComments 获取视频评论列表
func (h *VideoCommentController) ListComments(c *fiber.Ctx) error {
	// 使用链式调用获取数据
	reqInfo := utils.GetRequestInfo(c)

	// 获取videoId，支持从路径参数或请求体中获取
	videoID := c.Params("videoId")
	if videoID == "" {
		// 如果路径参数中没有，尝试从请求体获取
		videoID = reqInfo.Get("videoId").GetString()
	}
	userId := reqInfo.Get("user_id").GetString()
	userName := reqInfo.Get("user_name").GetString()
	keyword := reqInfo.Get("keyword").GetString()
	status := -999
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return h.BadRequest(c, "参数解析失败", nil)
	}

	// 获取创建时间范围
	createdAtStart := reqInfo.Get("created_at_start").GetString()
	createdAtEnd := reqInfo.Get("created_at_end").GetString()

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"video_id":         videoID,
		"status":           status,
		"user_id":          userId,
		"user_nickname":    userName,
		"keyword":          keyword,
		"created_at_start": createdAtStart,
		"created_at_end":   createdAtEnd,
	}
	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 获取评论列表
	comments, total, err := h.commentService.List(c.Context(), condition, "created_at desc", page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取评论列表失败: "+err.Error())
	}

	return h.SuccessList(c, comments, int64(total), page, pageSize)
}

// ListReplies 获取评论回复列表
func (h *VideoCommentController) ListReplies(c *fiber.Ctx) error {
	reqInfo := utils.GetRequestInfo(c)
	commentID := reqInfo.Get("comment_id").GetString()
	if commentID == "" {
		return h.BadRequest(c, "评论ID不能为空", nil)
	}

	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{
		"parent_id": commentID,
	}
	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	replies, total, err := h.commentService.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取回复列表失败")
	}

	return h.SuccessList(c, replies, int64(total), page, pageSize)
}

// UpdateCommentStatus 更新评论状态
func (h *VideoCommentController) UpdateCommentStatus(c *fiber.Ctx) error {
	var req videoValidator.UpdateVideoCommentStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "参数验证失败", err)
	}

	err := h.commentService.UpdateStatus(c.Context(), req.ID, req.Status)
	if err != nil {
		return h.InternalServerError(c, "更新评论状态失败: "+err.Error())
	}
	return h.SuccessWithMessage(c, "更新评论状态成功")
}

// BatchUpdateCommentStatus 批量更新评论状态
func (h *VideoCommentController) BatchUpdateCommentStatus(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 定义批量更新请求结构
	type BatchUpdateRequest struct {
		IDs    []string `json:"ids" validate:"required"`
		Status int      `json:"status" validate:"required"`
	}

	// 解析请求参数
	var req BatchUpdateRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "参数验证失败", err)
	}

	// 批量更新状态
	err := h.commentService.BatchUpdateStatus(c.Context(), req.IDs, req.Status)
	if err != nil {
		return h.InternalServerError(c, "批量更新评论状态失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "批量更新评论状态成功")
}

// BatchDeleteComment 批量删除评论
func (h *VideoCommentController) BatchDeleteComment(c *fiber.Ctx) error {
	// 权限检查
	_, ok := h.RequireLogin(c)
	if !ok {
		return nil // 已经返回了错误响应
	}

	// 定义批量删除请求结构
	type BatchDeleteRequest struct {
		IDs []string `json:"ids" validate:"required"`
	}

	// 解析请求参数
	var req BatchDeleteRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "参数验证失败", err)
	}

	// 批量删除评论
	count, err := h.commentService.BatchDelete(c.Context(), req.IDs)
	if err != nil {
		return h.InternalServerError(c, "批量删除评论失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, fmt.Sprintf("成功删除 %d 条评论", count))
}
