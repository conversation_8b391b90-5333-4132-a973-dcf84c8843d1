package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoChannel 频道表
type VideoChannel struct {
	models.BaseModelStruct        // 使用值嵌入而不是指针嵌入
	Name                   string `gorm:"column:name;uniqueIndex" json:"name"`             // 频道名称
	Icon                   string `gorm:"column:icon" json:"icon"`                         // 频道图标
	Image                  string `gorm:"column:image" json:"image"`                       // 频道封面图
	Description            string `gorm:"column:description" json:"description"`           // 频道描述
	UpdateFrequency        string `gorm:"column:update_frequency" json:"update_frequency"` // 更新频率
	Uri                    string `gorm:"column:uri" json:"uri"`                           // URI标识
	SortOrder              uint8  `gorm:"column:sort_order" json:"sort_order"`             // 排序序号
	Status                 uint8  `gorm:"column:status;default:1" json:"status"`           // 状态：0-禁用，1-正常
}

// TableName 设置表名
func (VideoChannel) TableName() string {
	return "ly_video_channels"
}

// 重写方法以确保正确调用
func (v *VideoChannel) SetCreatedAt(time types.JSONTime) {
	v.BaseModelStruct.SetCreatedAt(time)
}

func (v *VideoChannel) SetUpdatedAt(time types.JSONTime) {
	v.BaseModelStruct.SetUpdatedAt(time)
}

func (v *VideoChannel) SetID(id string) {
	v.BaseModelStruct.SetID(id)
}

func (v *VideoChannel) SetStatus(status int8) {
	v.BaseModelStruct.SetStatus(status)
}

func (v VideoChannel) GetID() string {
	return v.BaseModelStruct.GetID()
}

func (v VideoChannel) GetStatus() int8 {
	return v.BaseModelStruct.GetStatus()
}

func (v VideoChannel) GetCreatedAt() types.JSONTime {
	return v.BaseModelStruct.GetCreatedAt()
}

func (v VideoChannel) GetUpdatedAt() types.JSONTime {
	return v.BaseModelStruct.GetUpdatedAt()
}
