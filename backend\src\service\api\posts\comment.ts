import { ApiResponse } from "@/types/https";
import { CommentListResponse, CommentParams, PostComment } from "@/types/posts";
import { request } from "../../request";

// 获取评论列表
export const getCommentList = (params: CommentParams) => {
    return request<ApiResponse<CommentListResponse>>({
        url: "/posts/comments/list",
        method: "post",
        data: params,
    });
};

// 获取评论详情
export const getCommentDetail = (id: string) => {
    return request<ApiResponse<PostComment>>({
        url: `/posts/comments/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    });
};


// 删除评论
export const deleteComment = (id: string) => {
    return request<ApiResponse<null>>({
        url: `/posts/comments/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
};

// 更新评论状态
export const updateCommentStatus = (id: string, status: number, reason?: string) => {
    return request<ApiResponse<null>>({
        url: `/posts/comments/update-status/${id}`,
        method: "post",
        data: { data: { "id": id, "status": status, "reason": reason } }
    });
};

// 批量删除评论
export const batchDeleteComment = (data: { ids: string[] }) => {
    return request<ApiResponse<null>>({
        url: "/posts/comments/batch-delete",
        method: "post",
        data: { data }
    });
};

// 获取评论回复列表
export const getCommentReplies = (commentId: string, pageNo: number = 1, pageSize: number = 10) => {
    return request<ApiResponse<CommentListResponse>>({
        url: `/posts/comments/replies/${commentId}`,
        method: "post",
        data: {
            page: {
                pageNo,
                pageSize
            },
            data: {
                parent_id: commentId
            }
        }
    });
};
export const batchUpdateCommentStatus = (data: { ids: string[], status: number }) => {
    return request<ApiResponse<null>>({
        url: "/posts/comments/batch-update-status",
        method: "post",
        data: { data }
    });
};
