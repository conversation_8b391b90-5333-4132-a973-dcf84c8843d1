/**
 * PrimeVue 4 definePreset 主题预设配置
 */
import { definePreset } from '@primeuix/themes';
import Aura from '@primeuix/themes/aura';
import Lara from '@primeuix/themes/lara';
import Material from '@primeuix/themes/material';
import { THEME_COLORS } from '@/config/theme.config';

// 创建主题预设的工厂函数
function createThemePreset(baseTheme: any, colorScheme: string, isDark: boolean = false) {
    const colorConfig = THEME_COLORS[colorScheme as keyof typeof THEME_COLORS];
    
    if (!colorConfig) {
        console.warn(`Color scheme '${colorScheme}' not found, using default indigo`);
        return definePreset(baseTheme, {});
    }

    const primaryColor = isDark ? colorConfig.dark : colorConfig.light;
    
    return definePreset(baseTheme, {
        semantic: {
            primary: {
                50: isDark ? '#f0f9ff' : '#eff6ff',
                100: isDark ? '#e0f2fe' : '#dbeafe', 
                200: isDark ? '#bae6fd' : '#bfdbfe',
                300: isDark ? '#7dd3fc' : '#93c5fd',
                400: isDark ? '#38bdf8' : '#60a5fa',
                500: primaryColor,
                600: isDark ? '#0284c7' : '#2563eb',
                700: isDark ? '#0369a1' : '#1d4ed8',
                800: isDark ? '#075985' : '#1e40af',
                900: isDark ? '#0c4a6e' : '#1e3a8a',
                950: isDark ? '#082f49' : '#172554'
            }
        },
        components: {
            // 按钮组件自定义
            button: {
                primary: {
                    background: primaryColor,
                    hoverBackground: isDark ? colorConfig.light : colorConfig.dark,
                    activeBackground: isDark ? colorConfig.light : colorConfig.dark,
                    borderColor: primaryColor,
                    hoverBorderColor: isDark ? colorConfig.light : colorConfig.dark,
                    activeBorderColor: isDark ? colorConfig.light : colorConfig.dark,
                    color: '#ffffff',
                    hoverColor: '#ffffff',
                    activeColor: '#ffffff'
                }
            },
            // 面板组件自定义
            panel: {
                header: {
                    background: isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.95)',
                    borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                    color: isDark ? '#ffffff' : '#1f2937'
                },
                content: {
                    background: isDark ? 'rgba(0, 0, 0, 0.9)' : 'rgba(255, 255, 255, 0.98)',
                    color: isDark ? '#e5e7eb' : '#374151'
                }
            },
            // 输入框组件自定义
            inputtext: {
                background: isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(255, 255, 255, 0.9)',
                borderColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                color: isDark ? '#ffffff' : '#1f2937',
                focusBorderColor: primaryColor
            },
            // 菜单组件自定义
            menu: {
                background: isDark ? 'rgba(0, 0, 0, 0.9)' : 'rgba(255, 255, 255, 0.95)',
                borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                color: isDark ? '#e5e7eb' : '#374151'
            }
        }
    });
}

// 创建所有主题预设
export const auraPresets = {
    // Aura Light 主题
    auraLightIndigo: createThemePreset(Aura, 'indigo', false),
    auraLightBlue: createThemePreset(Aura, 'blue', false),
    auraLightGreen: createThemePreset(Aura, 'green', false),
    auraLightPurple: createThemePreset(Aura, 'purple', false),
    auraLightTeal: createThemePreset(Aura, 'teal', false),
    auraLightDeeppurple: createThemePreset(Aura, 'deeppurple', false),
    auraLightOrange: createThemePreset(Aura, 'orange', false),
    auraLightRose: createThemePreset(Aura, 'rose', false),
    auraLightFuchsia: createThemePreset(Aura, 'fuchsia', false),
    auraLightNoir: createThemePreset(Aura, 'noir', false),
    
    // Aura Dark 主题
    auraDarkIndigo: createThemePreset(Aura, 'indigo', true),
    auraDarkBlue: createThemePreset(Aura, 'blue', true),
    auraDarkGreen: createThemePreset(Aura, 'green', true),
    auraDarkPurple: createThemePreset(Aura, 'purple', true),
    auraDarkTeal: createThemePreset(Aura, 'teal', true),
    auraDarkDeeppurple: createThemePreset(Aura, 'deeppurple', true),
    auraDarkOrange: createThemePreset(Aura, 'orange', true),
    auraDarkRose: createThemePreset(Aura, 'rose', true),
    auraDarkFuchsia: createThemePreset(Aura, 'fuchsia', true),
    auraDarkNoir: createThemePreset(Aura, 'noir', true)
};

export const laraPresets = {
    // Lara Light 主题
    laraLightIndigo: createThemePreset(Lara, 'indigo', false),
    laraLightBlue: createThemePreset(Lara, 'blue', false),
    laraLightGreen: createThemePreset(Lara, 'green', false),
    laraLightPurple: createThemePreset(Lara, 'purple', false),
    laraLightTeal: createThemePreset(Lara, 'teal', false),
    laraLightDeeppurple: createThemePreset(Lara, 'deeppurple', false),
    laraLightOrange: createThemePreset(Lara, 'orange', false),
    laraLightRose: createThemePreset(Lara, 'rose', false),
    laraLightFuchsia: createThemePreset(Lara, 'fuchsia', false),
    laraLightNoir: createThemePreset(Lara, 'noir', false),
    
    // Lara Dark 主题
    laraDarkIndigo: createThemePreset(Lara, 'indigo', true),
    laraDarkBlue: createThemePreset(Lara, 'blue', true),
    laraDarkGreen: createThemePreset(Lara, 'green', true),
    laraDarkPurple: createThemePreset(Lara, 'purple', true),
    laraDarkTeal: createThemePreset(Lara, 'teal', true),
    laraDarkDeeppurple: createThemePreset(Lara, 'deeppurple', true),
    laraDarkOrange: createThemePreset(Lara, 'orange', true),
    laraDarkRose: createThemePreset(Lara, 'rose', true),
    laraDarkFuchsia: createThemePreset(Lara, 'fuchsia', true),
    laraDarkNoir: createThemePreset(Lara, 'noir', true)
};

export const materialPresets = {
    // Material Light 主题
    mdLightIndigo: createThemePreset(Material, 'indigo', false),
    mdLightBlue: createThemePreset(Material, 'blue', false),
    mdLightGreen: createThemePreset(Material, 'green', false),
    mdLightPurple: createThemePreset(Material, 'purple', false),
    mdLightTeal: createThemePreset(Material, 'teal', false),
    mdLightDeeppurple: createThemePreset(Material, 'deeppurple', false),
    mdLightOrange: createThemePreset(Material, 'orange', false),
    mdLightRose: createThemePreset(Material, 'rose', false),
    mdLightFuchsia: createThemePreset(Material, 'fuchsia', false),
    mdLightNoir: createThemePreset(Material, 'noir', false),
    
    // Material Dark 主题
    mdDarkIndigo: createThemePreset(Material, 'indigo', true),
    mdDarkBlue: createThemePreset(Material, 'blue', true),
    mdDarkGreen: createThemePreset(Material, 'green', true),
    mdDarkPurple: createThemePreset(Material, 'purple', true),
    mdDarkTeal: createThemePreset(Material, 'teal', true),
    mdDarkDeeppurple: createThemePreset(Material, 'deeppurple', true),
    mdDarkOrange: createThemePreset(Material, 'orange', true),
    mdDarkRose: createThemePreset(Material, 'rose', true),
    mdDarkFuchsia: createThemePreset(Material, 'fuchsia', true),
    mdDarkNoir: createThemePreset(Material, 'noir', true)
};

// 合并所有预设
export const allPresets = {
    ...auraPresets,
    ...laraPresets,
    ...materialPresets
};

// 根据主题代码获取预设
export function getPresetByThemeCode(themeCode: string) {
    return allPresets[themeCode as keyof typeof allPresets] || auraPresets.auraLightIndigo;
}

// 导出默认预设
export default allPresets;