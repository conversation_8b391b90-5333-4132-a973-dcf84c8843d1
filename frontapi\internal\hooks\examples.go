package hooks

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"
	"frontapi/internal/hooks/common"
)

// CategoryExample 分类示例
type CategoryExample struct {
	ID          string `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"column:name"`
	Description string `json:"description" gorm:"column:description"`
	Status      int    `json:"status" gorm:"column:status"`
	CreatedAt   string `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   string `json:"updated_at" gorm:"column:updated_at"`
}

// CategoryService 分类服务示例
type CategoryService struct {
	db          *gorm.DB
	hookManager *ServiceHookManager
}

// NewCategoryService 创建分类服务
func NewCategoryService(db *gorm.DB) *CategoryService {
	service := &CategoryService{
		db:          db,
		hookManager: NewServiceHookManager(db, "category"),
	}
	
	// 设置钩子
	service.setupHooks()
	return service
}

// setupHooks 设置钩子
func (s *CategoryService) setupHooks() {
	// 使用构建器模式设置钩子
	NewCreateHookBuilder(s.hookManager).
		// 数据清洗：去除空格，名称转换为小写
		WithDataCleaning(
			[]string{"Name", "Description"}, // 去除空格的字段
			[]string{"Name"},                // 转换为小写的字段
			[]string{},                      // 转换为大写的字段
			map[string]interface{}{
				"Status": 1, // 默认状态为启用
			},
		).
		// 数据验证
		WithValidation(map[string][]common.ValidationRule{
			"Name": {
				{Type: "required", Message: "分类名称不能为空"},
				{Type: "min_length", Value: 2, Message: "分类名称至少2个字符"},
				{Type: "max_length", Value: 50, Message: "分类名称不能超过50个字符"},
			},
			"Description": {
				{Type: "max_length", Value: 200, Message: "描述不能超过200个字符"},
			},
		}).
		// 重复检查
		WithDuplicateCheck(
			"categories",
			[]string{"Name"},
			"分类名称已存在",
		).
		// 时间戳
		WithTimestamp("CreatedAt", "UpdatedAt").
		// 审计
		WithAudit("categories", "system").
		// 自定义钩子：名称格式化
		WithCustomHook(
			BeforeCreate,
			"category_name_format",
			"格式化分类名称",
			12,
			func(ctx context.Context, data interface{}) error {
				if category, ok := data.(*CategoryExample); ok {
					// 移除特殊字符，只保留字母、数字、中文和空格
					category.Name = strings.TrimSpace(category.Name)
					if category.Name == "" {
						return errors.New("分类名称不能为空")
					}
				}
				return nil
			},
		).
		Build()
}

// Create 创建分类
func (s *CategoryService) Create(ctx context.Context, category *CategoryExample) error {
	// 执行创建前钩子
	if err := s.hookManager.ExecuteHooks(ctx, BeforeCreate, category); err != nil {
		return fmt.Errorf("创建前钩子执行失败: %w", err)
	}

	// 执行数据库创建
	if err := s.db.WithContext(ctx).Create(category).Error; err != nil {
		return fmt.Errorf("创建分类失败: %w", err)
	}

	// 执行创建后钩子
	if err := s.hookManager.ExecuteHooks(ctx, AfterCreate, category); err != nil {
		// 创建后钩子失败不影响主操作，只记录日志
		fmt.Printf("创建后钩子执行失败: %v\n", err)
	}

	return nil
}

// Update 更新分类
func (s *CategoryService) Update(ctx context.Context, category *CategoryExample) error {
	// 执行更新前钩子
	if err := s.hookManager.ExecuteHooks(ctx, BeforeUpdate, category); err != nil {
		return fmt.Errorf("更新前钩子执行失败: %w", err)
	}

	// 执行数据库更新
	if err := s.db.WithContext(ctx).Save(category).Error; err != nil {
		return fmt.Errorf("更新分类失败: %w", err)
	}

	// 执行更新后钩子
	if err := s.hookManager.ExecuteHooks(ctx, AfterUpdate, category); err != nil {
		// 更新后钩子失败不影响主操作，只记录日志
		fmt.Printf("更新后钩子执行失败: %v\n", err)
	}

	return nil
}

// BookExample 电子书示例
type BookExample struct {
	ID          string `json:"id" gorm:"primaryKey"`
	Title       string `json:"title" gorm:"column:title"`
	Author      string `json:"author" gorm:"column:author"`
	ISBN        string `json:"isbn" gorm:"column:isbn"`
	Description string `json:"description" gorm:"column:description"`
	Status      int    `json:"status" gorm:"column:status"`
	CreatedAt   string `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   string `json:"updated_at" gorm:"column:updated_at"`
}

// BookService 电子书服务示例
type BookService struct {
	db          *gorm.DB
	hookManager *ServiceHookManager
}

// NewBookService 创建电子书服务
func NewBookService(db *gorm.DB) *BookService {
	service := &BookService{
		db:          db,
		hookManager: NewServiceHookManager(db, "book"),
	}
	
	// 设置钩子
	service.setupHooks()
	return service
}

// setupHooks 设置钩子
func (s *BookService) setupHooks() {
	// 数据清洗
	s.hookManager.RegisterDataCleaning(
		[]string{"Title", "Author", "Description"}, // 去除空格
		[]string{},                                   // 转换为小写
		[]string{"ISBN"},                            // ISBN转换为大写
		map[string]interface{}{
			"Status": 1, // 默认状态
		},
	)

	// 数据验证
	s.hookManager.RegisterValidation(map[string][]common.ValidationRule{
		"Title": {
			{Type: "required", Message: "书名不能为空"},
			{Type: "min_length", Value: 1, Message: "书名至少1个字符"},
			{Type: "max_length", Value: 100, Message: "书名不能超过100个字符"},
		},
		"Author": {
			{Type: "required", Message: "作者不能为空"},
			{Type: "max_length", Value: 50, Message: "作者名称不能超过50个字符"},
		},
		"ISBN": {
			{Type: "regex", Value: `^[0-9]{10}([0-9]{3})?$`, Message: "ISBN格式不正确"},
		},
	})

	// 重复检查：检查ISBN是否重复
	s.hookManager.RegisterDuplicateCheck(
		"books",
		[]string{"ISBN"},
		"该ISBN已存在",
	)

	// 时间戳
	s.hookManager.RegisterTimestamp("CreatedAt", "UpdatedAt")

	// 审计
	s.hookManager.RegisterAudit("books", "system")

	// 自定义钩子：生成书籍编号
	s.hookManager.RegisterCustomHook(
		BeforeCreate,
		"book_code_generator",
		"生成书籍编号",
		20,
		func(ctx context.Context, data interface{}) error {
			if book, ok := data.(*BookExample); ok {
				if book.ID == "" {
					// 这里可以实现自定义的ID生成逻辑
					book.ID = fmt.Sprintf("BOOK_%d", len(book.Title))
				}
			}
			return nil
		},
	)
}

// Create 创建电子书
func (s *BookService) Create(ctx context.Context, book *BookExample) error {
	// 执行创建前钩子
	if err := s.hookManager.ExecuteHooks(ctx, BeforeCreate, book); err != nil {
		return fmt.Errorf("创建前钩子执行失败: %w", err)
	}

	// 执行数据库创建
	if err := s.db.WithContext(ctx).Create(book).Error; err != nil {
		return fmt.Errorf("创建电子书失败: %w", err)
	}

	// 执行创建后钩子
	if err := s.hookManager.ExecuteHooks(ctx, AfterCreate, book); err != nil {
		// 创建后钩子失败不影响主操作，只记录日志
		fmt.Printf("创建后钩子执行失败: %v\n", err)
	}

	return nil
}

// 使用示例函数
func ExampleUsage() {
	// 假设已经有数据库连接
	var db *gorm.DB

	// 创建分类服务
	categoryService := NewCategoryService(db)

	// 创建分类
	category := &CategoryExample{
		Name:        "  科技  ", // 包含空格，会被清洗
		Description: "科技相关的内容",
	}

	ctx := context.Background()
	if err := categoryService.Create(ctx, category); err != nil {
		fmt.Printf("创建分类失败: %v\n", err)
	} else {
		fmt.Printf("创建分类成功: %+v\n", category)
	}

	// 创建电子书服务
	bookService := NewBookService(db)

	// 创建电子书
	book := &BookExample{
		Title:       "Go语言编程",
		Author:      "张三",
		ISBN:        "9787111111111",
		Description: "Go语言学习指南",
	}

	if err := bookService.Create(ctx, book); err != nil {
		fmt.Printf("创建电子书失败: %v\n", err)
	} else {
		fmt.Printf("创建电子书成功: %+v\n", book)
	}
}