import type { App, Directive } from 'vue';

// This is provided for backward compatibility if any components
// are still using the misspelled directive name 'v-dragable'

interface DragableElement extends HTMLElement {
  _dragStartHandler?: EventListener;
  _dragOverHandler?: EventListener;
  _dragEndHandler?: EventListener;
  _dropHandler?: EventListener;
}

const dragable: Directive = {
  mounted(el: DragableElement, binding) {
    console.warn('Deprecated: Use v-draggable-columns instead of v-dragable');
    
    // Redirect to the correctly spelled directive
    // This ensures backward compatibility
    const dragableColumns = document.querySelector('[v-draggable-columns]');
    if (dragableColumns) {
      console.info('Redirecting to draggable-columns directive');
    } else {
      console.error('No draggable-columns directive found to redirect to');
    }
  },
  
  unmounted(el: DragableElement) {
    // Clean up (empty for compatibility)
  }
};

// Export the directive
export default {
  install(app: App) {
    app.directive('dragable', dragable);
  }
}; 