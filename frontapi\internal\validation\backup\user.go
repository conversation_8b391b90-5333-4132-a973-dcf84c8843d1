package validation

// LoginRequest 登录请求验证模型
type LoginRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Password string `json:"password" validate:"required,min=6"`
}

// RegisterRequest 注册请求验证模型
type RegisterRequest struct {
	Username   string `json:"username" validate:"required,username"`
	Password   string `json:"password" validate:"required,strongpassword"`
	Email      string `json:"email" validate:"required,email"`
	Mobile     string `json:"mobile" validate:"required,mobile"`
	Nickname   string `json:"nickname" validate:"required,min=2,max=50"`
	Gender     int    `json:"gender" validate:"oneof=0 1 2"` // 0:未知, 1:男, 2:女
	BirthDate  string `json:"birthDate" validate:"omitempty,dateformat"`
	AvatarURL  string `json:"avatarUrl" validate:"omitempty,url"`
	Invitation string `json:"invitation" validate:"omitempty"`
}

// UpdateProfileRequest 更新个人资料请求验证模型
type UpdateProfileRequest struct {
	Nickname  string `json:"nickname" validate:"omitempty,min=2,max=50"`
	Gender    int    `json:"gender" validate:"omitempty,oneof=0 1 2"`
	BirthDate string `json:"birthDate" validate:"omitempty,dateformat"`
	AvatarURL string `json:"avatarUrl" validate:"omitempty,url"`
	Bio       string `json:"bio" validate:"omitempty,max=200"`
	Website   string `json:"website" validate:"omitempty,url"`
}

// ChangePasswordRequest 修改密码请求验证模型
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" validate:"required,min=6"`
	NewPassword string `json:"newPassword" validate:"required,strongpassword,nefield=OldPassword"`
	Confirm     string `json:"confirm" validate:"required,eqfield=NewPassword"`
}
