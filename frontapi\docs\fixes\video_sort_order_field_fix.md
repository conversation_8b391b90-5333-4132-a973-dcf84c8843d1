# 视频模型 sort_order 字段修复总结

## 问题描述

在调用 `http://localhost:8081/api/proadm/videos/add` 创建视频时，出现数据库错误：

```
创建视频失败: 创建失败: Error 1054 (42S22): Unknown column 'sort_order' in 'field list'
```

## 问题原因

1. **模型层次结构问题**：
   - `Video` 模型使用指针嵌入 `*models.ContentBaseModel`
   - `ContentBaseModel` 嵌入了 `ExtendedBaseModel`
   - `ExtendedBaseModel` 包含了 `SortOrder` 字段
   - 但数据库表 `ly_videos` 中没有 `sort_order` 字段

2. **字段不匹配**：
   - 代码中的模型包含 `sort_order` 字段
   - 数据库表结构中缺少该字段
   - 用户明确表示不需要该字段，使用其他排序方式

## 修复方案

### 1. 重构 Video 模型

将 `Video` 模型从嵌入 `ContentBaseModel` 改为直接嵌入 `BaseModelStruct`，并手动添加所需字段：

```go
// Video 视频模型
type Video struct {
	models.BaseModelStruct
	// 基本信息
	Title        string      `json:"title" gorm:"column:title" comment:"标题"`
	Description  null.String `json:"description" gorm:"column:description;type:text" comment:"描述"`
	Cover        string      `json:"cover" gorm:"column:cover" comment:"封面URL"`
	// ... 其他字段，但不包含 SortOrder
}
```

**优点**：
- 完全控制字段定义
- 避免不需要的字段
- 与数据库表结构完全匹配

### 2. 更新控制器逻辑

修改 `VideoController` 中的 `CreateVideo` 和 `UpdateVideo` 方法：

```go
// CreateVideo 创建视频
func (h *VideoController) CreateVideo(c *fiber.Ctx) error {
	// ... 验证逻辑
	
	// 手动映射字段，替代 SmartCopy
	video := &videoModel.Video{}
	video.Title = req.Title
	video.SetDescription(req.Description)
	video.Cover = req.Cover
	// ... 其他字段映射
	
	// 创建视频
	videoID, err := h.videoService.Create(c.Context(), video)
	// ...
}
```

### 3. 修复类型转换器

更新 `typings/videos/converters.go` 和 `typings/home/<USER>

```go
// 修复前
Description:   video.Description.ValueOrZero(),
CategoryName:  video.CategoryName.ValueOrZero(),

// 修复后
var description string
if video.Description.Valid {
    description = video.Description.String
}
CategoryName:  video.CategoryName,  // 现在是普通string
```

## 修复文件列表

1. `frontapi/internal/models/videos/video.go` - 重构Video模型
2. `frontapi/internal/admin/videos/video_controller.go` - 更新控制器逻辑
3. `frontapi/internal/typings/videos/converters.go` - 修复类型转换
4. `frontapi/internal/typings/home/<USER>

## 测试验证

- ✅ 编译成功：`go build ./cmd/admin`
- ✅ 程序正常运行在8081端口
- ✅ 移除了不需要的 `sort_order` 字段
- ✅ 保持了所有必需的视频字段
- ✅ 修复了所有相关的类型转换错误

## 注意事项

1. **字段映射一致性**：确保模型字段与数据库表结构完全匹配
2. **null.String 处理**：正确处理可为空的字段类型
3. **手动映射优于 SmartCopy**：当模型结构复杂时，手动映射更安全可控
4. **类型转换器同步**：模型字段变更时需要同步更新所有转换器

## 预防措施

1. **模型设计原则**：
   - 优先使用最小的基础模型
   - 避免过度嵌入包含不需要字段的模型
   - 明确区分业务字段和基础字段

2. **字段验证**：
   - 开发新功能时先检查数据库表结构
   - 确保模型字段与表字段一致
   - 使用数据库迁移管理字段变更

3. **测试策略**：
   - 在修改模型后立即编译测试
   - 检查所有使用该模型的转换器和服务
   - 进行端到端的API测试

## 相关问题

这个修复解决了类似的模型字段不匹配问题：
- [视频频道nil指针修复](./video_channel_nil_pointer_fix.md)
- [用户模型字段修复](./user_model_basemodel_fix.md)
- [登录日志BaseModel接口错误修复](./user_login_logs_basemodel_fix.md)

## 总结

通过重构 `Video` 模型结构，移除了不需要的 `sort_order` 字段，修复了数据库字段不匹配的问题。同时更新了相关的控制器和类型转换器，确保系统的一致性和稳定性。这个修复遵循了最小化依赖和明确字段控制的设计原则。 