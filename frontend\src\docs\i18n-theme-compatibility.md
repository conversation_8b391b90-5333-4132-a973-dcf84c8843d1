# 国际化(i18n)组件与主题系统兼容性

为了确保我们的国际化(i18n)组件能够与主题系统无缝兼容，我们进行了以下优化设计：

## 设计原则

1. **使用变量而非硬编码颜色**：所有UI组件样式都使用PrimeVue和系统提供的CSS变量
2. **主题感知**：组件能够检测并响应主题变化
3. **一致性**：确保在所有主题下文本和UI元素保持可见和美观
4. **无闪烁**：主题切换时避免视觉闪烁或不必要的重绘

## CSS变量系统

我们使用了以下PrimeVue提供的CSS变量来确保主题兼容性：

### 文本颜色变量

- `--text-color`: 主要文本颜色
- `--text-color-secondary`: 次要文本颜色
- `--primary-color-text`: 主题色上的文本颜色

### 背景颜色变量

- `--surface-card`: 卡片背景
- `--surface-section`: 区域背景
- `--surface-ground`: 基础背景
- `--surface-hover`: 悬停状态背景
- `--surface-overlay`: 覆盖层背景
- `--primary-50`: 主色轻淡版本（高亮项使用）

### 边框颜色变量

- `--surface-border`: 边框颜色
- `--surface-400`: 中等强调边框
- `--surface-500`: 高强调边框

## 组件实现细节

### 语言选择器基础组件 (`LanguageSelector/index.vue`)

基础组件通过以下方式支持主题：

```scss
.language-panel {
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
}

.language-panel-header {
  border-bottom: 1px solid var(--surface-border);
  background: var(--surface-section);
}

.language-panel-title {
  color: var(--text-color);
}

.language-item {
  color: var(--text-color);
  
  &:hover {
    background-color: var(--surface-hover);
  }
  
  &.active {
    background-color: var(--primary-50);
  }
}
```

### 导航栏语言选择器 (`NavLanguageSelector.vue`)

针对导航栏位置的特殊处理：

```scss
.language-toggle-btn {
  color: var(--text-color);
  
  &:hover {
    color: var(--primary-color);
  }
}

.language-panel {
  background: var(--surface-overlay);
  border-color: var(--surface-border);
}
```

### 页脚语言选择器 (`FooterLanguageSelector.vue`)

使用PrimeVue的Dropdown组件，并应用主题变量：

```scss
.p-dropdown-label {
  color: var(--text-color);
  background-color: var(--surface-card);
  border-color: var(--surface-border);
}

.p-dropdown-panel {
  background: var(--surface-card);
  
  .p-dropdown-item {
    color: var(--text-color);
    
    &:hover {
      background: var(--surface-hover);
    }
    
    &.p-highlight {
      background: var(--primary-50);
      color: var(--primary-color);
    }
  }
}
```

## 主题检测与响应

我们通过以下方式实现主题变化的检测和响应：

1. **CSS变量自动应用**：PrimeVue的主题变化会自动更新CSS变量
2. **无需JavaScript处理**：组件样式完全依赖CSS变量，无需JS监听主题变化
3. **即时反馈**：主题切换后立即应用新的样式，无需刷新页面

## 最佳实践

在创建或修改国际化相关的UI组件时，请遵循以下最佳实践：

1. 始终使用CSS变量而非硬编码颜色
2. 测试组件在浅色和深色主题下的显示效果
3. 确保文本和背景之间有足够的对比度
4. 使用`:deep()`选择器修改PrimeVue组件内部样式
5. 考虑不同主题下的视觉一致性

## 测试清单

- [x] 浅色主题下的文本可读性
- [x] 深色主题下的文本可读性
- [x] 主题切换时的平滑过渡
- [x] 下拉面板的背景与文本颜色兼容
- [x] 活跃/选中状态在所有主题下可辨识
- [x] 悬停效果在所有主题下可见 