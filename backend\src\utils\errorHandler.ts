import { ElMessage } from 'element-plus';

/**
 * 统一错误处理工具类
 * 用于避免重复的错误提示
 */
class ErrorHandler {
    private errorMessages: Set<string> = new Set();
    private clearTimeouts: Map<string, NodeJS.Timeout> = new Map();

    /**
     * 显示错误消息（防重复）
     * @param message 错误消息
     * @param duration 显示时长（毫秒）
     */
    showError(message: string, duration = 3000) {
        if (this.errorMessages.has(message)) {
            return; // 已存在相同错误消息，不重复显示
        }

        this.errorMessages.add(message);

        ElMessage.error({
            message,
            duration,
            onClose: () => {
                this.removeError(message);
            }
        });

        // 设置自动清理定时器
        const timeout = setTimeout(() => {
            this.removeError(message);
        }, duration + 100); // 比消息持续时间稍长一点

        this.clearTimeouts.set(message, timeout);
    }

    /**
     * 显示成功消息
     * @param message 成功消息
     * @param duration 显示时长（毫秒）
     */
    showSuccess(message: string, duration = 3000) {
        ElMessage.success({
            message,
            duration
        });
    }

    /**
     * 显示警告消息
     * @param message 警告消息
     * @param duration 显示时长（毫秒）
     */
    showWarning(message: string, duration = 3000) {
        ElMessage.warning({
            message,
            duration
        });
    }

    /**
     * 移除错误消息记录
     * @param message 错误消息
     */
    private removeError(message: string) {
        this.errorMessages.delete(message);

        const timeout = this.clearTimeouts.get(message);
        if (timeout) {
            clearTimeout(timeout);
            this.clearTimeouts.delete(message);
        }
    }

    /**
     * 清空所有错误消息记录
     */
    clearAll() {
        this.errorMessages.clear();

        this.clearTimeouts.forEach(timeout => clearTimeout(timeout));
        this.clearTimeouts.clear();
    }

    /**
     * 处理API错误响应
     * @param error 错误对象
     * @param defaultMessage 默认错误消息
     */
    handleApiError(error: any, defaultMessage = '操作失败') {
        let message = defaultMessage;

        if (error?.response?.data?.message) {
            message = error.response.data.message;
        } else if (error?.message) {
            message = error.message;
        }

        this.showError(message);
    }

    /**
     * 处理删除操作的错误
     * @param error 错误对象
     * @param itemName 删除项目名称
     */
    handleDeleteError(error: any, itemName = '项目') {
        this.handleApiError(error, `删除${itemName}失败`);
    }

    /**
     * 处理创建操作的错误
     * @param error 错误对象
     * @param itemName 创建项目名称
     */
    handleCreateError(error: any, itemName = '项目') {
        this.handleApiError(error, `创建${itemName}失败`);
    }

    /**
     * 处理更新操作的错误
     * @param error 错误对象
     * @param itemName 更新项目名称
     */
    handleUpdateError(error: any, itemName = '项目') {
        this.handleApiError(error, `更新${itemName}失败`);
    }
}

// 导出单例实例
export const errorHandler = new ErrorHandler();

// 导出常用方法的简写
export const showError = (message: string, duration?: number) => errorHandler.showError(message, duration);
export const showSuccess = (message: string, duration?: number) => errorHandler.showSuccess(message, duration);
export const showWarning = (message: string, duration?: number) => errorHandler.showWarning(message, duration);
export const handleApiError = (error: any, defaultMessage?: string) => errorHandler.handleApiError(error, defaultMessage); 