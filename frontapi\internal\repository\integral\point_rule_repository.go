package integral

import (
	"context"

	"gorm.io/gorm"

	model "frontapi/internal/models/integral"
	"frontapi/internal/repository/base"
)

// PointRuleRepository 积分规则数据访问接口
type PointRuleRepository interface {
	base.ExtendedRepository[model.PointRule]
	FindByType(ctx context.Context, ruleType string) ([]*model.PointRule, error)
}

type pointRuleRepository struct {
	base.ExtendedRepository[model.PointRule]
}

func NewPointRuleRepository(db *gorm.DB) PointRuleRepository {
	return &pointRuleRepository{
		ExtendedRepository: base.NewExtendedRepository[model.PointRule](db),
	}
}

// FindByType 根据类型查找积分规则
func (r *pointRuleRepository) FindByType(ctx context.Context, ruleType string) ([]*model.PointRule, error) {
	return r.FindAll(ctx, map[string]interface{}{
		"type": ruleType,
	}, "Points DESC")
}
