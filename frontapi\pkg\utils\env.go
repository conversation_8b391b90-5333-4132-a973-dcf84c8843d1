package utils

import (
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"

	"github.com/joho/godotenv"
)

var (
	envLoaded bool
	envMutex  sync.Once
	envCache  map[string]string
)

// init 初始化环境变量缓存
func init() {
	envCache = make(map[string]string)
}

// loadEnvFile 智能加载.env文件
func loadEnvFile() {
	envMutex.Do(func() {
		// 获取可能的.env文件路径
		envPaths := getEnvFilePaths()

		// 尝试加载.env文件
		for _, envPath := range envPaths {
			if _, err := os.Stat(envPath); err == nil {
				if err := godotenv.Load(envPath); err == nil {
					envLoaded = true
					break
				}
			}
		}

		// 如果没有找到.env文件，记录警告但不报错
		if !envLoaded {
			// 可以选择性地输出警告信息
			// log.Println("警告: 未找到.env文件，将使用系统环境变量和默认值")
		}
	})
}

// getEnvFilePaths 获取可能的.env文件路径
func getEnvFilePaths() []string {
	var paths []string

	// 1. 当前工作目录
	if wd, err := os.Getwd(); err == nil {
		paths = append(paths, filepath.Join(wd, ".env"))
	}

	// 2. 可执行文件所在目录
	if ex, err := os.Executable(); err == nil {
		exDir := filepath.Dir(ex)
		paths = append(paths, filepath.Join(exDir, ".env"))
	}

	// 3. 通过runtime获取当前文件路径，推断项目根目录
	_, filename, _, ok := runtime.Caller(0)
	if ok {
		// 当前文件是 frontapi/pkg/utils/env.go
		// 项目根目录应该是 frontapi/
		currentDir := filepath.Dir(filename)
		projectRoot := filepath.Join(currentDir, "..", "..")
		if absRoot, err := filepath.Abs(projectRoot); err == nil {
			paths = append(paths, filepath.Join(absRoot, ".env"))
		}
	}

	// 4. 查找包含go.mod的目录（项目根目录）
	if wd, err := os.Getwd(); err == nil {
		projectRoot := findProjectRoot(wd)
		if projectRoot != "" {
			paths = append(paths, filepath.Join(projectRoot, ".env"))
		}
	}

	// 5. 一些常见的相对路径
	commonPaths := []string{
		".env",
		"../.env",
		"../../.env",
		"../../../.env",
	}

	for _, commonPath := range commonPaths {
		if absPath, err := filepath.Abs(commonPath); err == nil {
			paths = append(paths, absPath)
		}
	}

	// 去重
	return removeDuplicates(paths)
}

// findProjectRoot 向上查找包含go.mod的目录
func findProjectRoot(startDir string) string {
	dir := startDir
	for {
		goModPath := filepath.Join(dir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return dir
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			// 已经到达根目录
			break
		}
		dir = parent
	}
	return ""
}

// removeDuplicates 去除重复路径
func removeDuplicates(paths []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, path := range paths {
		if !seen[path] {
			seen[path] = true
			result = append(result, path)
		}
	}

	return result
}

// GetEnv 获取环境变量，如果不存在则返回默认值
// 优先级：系统环境变量 > .env文件 > 默认值
func GetEnv(key, defaultValue string) string {
	// 确保.env文件已加载
	loadEnvFile()

	// 1. 首先检查系统环境变量
	if value := os.Getenv(key); value != "" {
		return value
	}

	// 2. 检查缓存中的值（来自.env文件）
	if value, exists := envCache[key]; exists {
		return value
	}

	// 3. 如果.env文件已加载，再次尝试从环境变量获取
	if envLoaded {
		if value := os.Getenv(key); value != "" {
			envCache[key] = value
			return value
		}
	}

	// 4. 返回默认值
	return defaultValue
}

// SetEnv 设置环境变量（用于测试或动态配置）
func SetEnv(key, value string) error {
	envCache[key] = value
	return os.Setenv(key, value)
}

// GetEnvBool 获取布尔类型的环境变量
func GetEnvBool(key string, defaultValue bool) bool {
	value := GetEnv(key, "")
	if value == "" {
		return defaultValue
	}

	value = strings.ToLower(value)
	return value == "true" || value == "1" || value == "yes" || value == "on"
}

// GetEnvInt 获取整数类型的环境变量
func GetEnvInt(key string, defaultValue int) int {
	value := GetEnv(key, "")
	if value == "" {
		return defaultValue
	}

	if intValue, err := strconv.Atoi(value); err == nil {
		return intValue
	}

	return defaultValue
}

// IsEnvLoaded 检查.env文件是否已成功加载
func IsEnvLoaded() bool {
	loadEnvFile()
	return envLoaded
}

// GetLoadedEnvPath 获取已加载的.env文件路径（用于调试）
func GetLoadedEnvPath() string {
	if !envLoaded {
		return ""
	}

	envPaths := getEnvFilePaths()
	for _, envPath := range envPaths {
		if _, err := os.Stat(envPath); err == nil {
			return envPath
		}
	}

	return ""
}

// GetEnvAsInt 获取环境变量并转换为整数，如果不存在或转换失败则返回默认值
func GetEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}
