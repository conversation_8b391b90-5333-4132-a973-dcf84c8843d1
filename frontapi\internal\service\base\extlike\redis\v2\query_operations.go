package v2

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"

	"frontapi/internal/service/base/extlike/types"
)

// QueryOperations 查询操作处理器
type QueryOperations struct {
	client *RedisClient
	stats  *AdapterStats
}

// NewQueryOperations 创建查询操作处理器
func NewQueryOperations(client *RedisClient, stats *AdapterStats) *QueryOperations {
	return &QueryOperations{
		client: client,
		stats:  stats,
	}
}

// BatchGetLikeStatus 批量获取点赞状态
func (q *QueryOperations) BatchGetLikeStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	if len(items) == 0 {
		return make(map[string]bool), nil
	}

	pipe := q.client.Pipeline()
	cmdMap := make(map[string]*redis.BoolCmd)

	// 批量检查点赞状态
	for itemID, itemType := range items {
		likeKey := q.client.likeKey(itemType, itemID)
		cmdMap[itemID] = pipe.SIsMember(ctx, likeKey, userID)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		q.stats.ErrorCount++
		return nil, fmt.Errorf("批量获取点赞状态失败: %w", err)
	}

	// 解析结果
	result := make(map[string]bool)
	for itemID, cmd := range cmdMap {
		liked, err := cmd.Result()
		if err != nil && err != redis.Nil {
			q.stats.MissCount++
			result[itemID] = false
		} else {
			q.stats.HitCount++
			result[itemID] = liked
		}
	}

	q.stats.updateHitRate()
	return result, nil
}

// BatchGetLikeCounts 批量获取点赞数量
func (q *QueryOperations) BatchGetLikeCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	if len(items) == 0 {
		return make(map[string]int64), nil
	}

	pipe := q.client.Pipeline()
	cmdMap := make(map[string]*redis.StringCmd)

	// 批量获取点赞数量
	for itemID, itemType := range items {
		countKey := q.client.countKey(itemType, itemID)
		cmdMap[itemID] = pipe.Get(ctx, countKey)
	}

	_, err := pipe.Exec(ctx)
	if err != nil {
		q.stats.ErrorCount++
		return nil, fmt.Errorf("批量获取点赞数量失败: %w", err)
	}

	// 解析结果
	result := make(map[string]int64)
	for itemID, cmd := range cmdMap {
		count, err := cmd.Int64()
		if err != nil {
			if err == redis.Nil {
				q.stats.MissCount++
				result[itemID] = 0
			} else {
				q.stats.ErrorCount++
				result[itemID] = 0
			}
		} else {
			q.stats.HitCount++
			result[itemID] = count
		}
	}

	q.stats.updateHitRate()
	return result, nil
}

// GetUserLikes 获取用户点赞的内容列表
func (q *QueryOperations) GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	historyKey := q.client.historyKey(userID, itemType)

	// 从Stream获取历史记录
	messages, err := q.client.client.XRange(ctx, historyKey, "-", "+").Result()
	if err != nil {
		if err == redis.Nil {
			q.stats.MissCount++
			q.stats.updateHitRate()
			return []*types.LikeRecord{}, nil
		}
		q.stats.ErrorCount++
		return nil, fmt.Errorf("获取用户点赞历史失败: %w", err)
	}

	// 转换结果
	var records []*types.LikeRecord
	for _, msg := range messages {
		action := getStringValue(msg.Values, "action")
		// 只返回点赞操作的记录
		if action == "like" {
			record := &types.LikeRecord{
				ID:       msg.ID,
				UserID:   getStringValue(msg.Values, "user_id"),
				ItemID:   getStringValue(msg.Values, "item_id"),
				ItemType: getStringValue(msg.Values, "item_type"),
				Status:   "liked",
			}

			if timestampStr := getStringValue(msg.Values, "timestamp"); timestampStr != "" {
				if timestamp, err := strconv.ParseInt(timestampStr, 10, 64); err == nil {
					record.Timestamp = time.Unix(timestamp, 0)
				}
			}

			records = append(records, record)
		}
	}

	// 分页处理
	start := offset
	end := offset + limit
	if start >= len(records) {
		start = len(records)
	}
	if end > len(records) {
		end = len(records)
	}

	q.stats.HitCount++
	q.stats.updateHitRate()
	return records[start:end], nil
}

// GetItemLikers 获取点赞某个内容的用户列表
func (q *QueryOperations) GetItemLikers(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	itemLikersKey := q.client.itemLikersKey(itemType, itemID)

	// 从Set获取点赞用户列表
	userIDs, err := q.client.client.SMembers(ctx, itemLikersKey).Result()
	if err != nil {
		if err == redis.Nil {
			q.stats.MissCount++
			q.stats.updateHitRate()
			return []*types.LikeRecord{}, nil
		}
		q.stats.ErrorCount++
		return nil, fmt.Errorf("获取点赞用户列表失败: %w", err)
	}

	// 分页处理
	start := offset
	end := offset + limit
	if start >= len(userIDs) {
		start = len(userIDs)
	}
	if end > len(userIDs) {
		end = len(userIDs)
	}

	var records []*types.LikeRecord
	for i := start; i < end; i++ {
		records = append(records, &types.LikeRecord{
			UserID:    userIDs[i],
			ItemID:    itemID,
			ItemType:  itemType,
			Status:    "liked",
			Timestamp: time.Now(), // 简化处理，实际应该从历史记录中获取
		})
	}

	q.stats.HitCount++
	q.stats.updateHitRate()
	return records, nil
}

// GetLikeHistory 获取点赞历史
func (q *QueryOperations) GetLikeHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.LikeRecord, error) {
	historyKey := q.client.historyKey(userID, itemType)

	// 构建时间范围查询
	start := "-"
	stop := "+"

	if timeRange != nil {
		if !timeRange.Start.IsZero() {
			start = fmt.Sprintf("%d-0", timeRange.Start.UnixMilli())
		}
		if !timeRange.End.IsZero() {
			stop = fmt.Sprintf("%d-0", timeRange.End.UnixMilli())
		}
	}

	messages, err := q.client.client.XRange(ctx, historyKey, start, stop).Result()
	if err != nil {
		if err == redis.Nil {
			q.stats.MissCount++
			q.stats.updateHitRate()
			return []*types.LikeRecord{}, nil
		}
		q.stats.ErrorCount++
		return nil, fmt.Errorf("获取点赞历史失败: %w", err)
	}

	// 转换结果
	var records []*types.LikeRecord
	for _, msg := range messages {
		action := getStringValue(msg.Values, "action")
		status := "liked"
		if action == "unlike" {
			status = "unliked"
		}

		record := &types.LikeRecord{
			ID:       msg.ID,
			UserID:   getStringValue(msg.Values, "user_id"),
			ItemID:   getStringValue(msg.Values, "item_id"),
			ItemType: getStringValue(msg.Values, "item_type"),
			Status:   status,
		}

		if timestampStr := getStringValue(msg.Values, "timestamp"); timestampStr != "" {
			if timestamp, err := strconv.ParseInt(timestampStr, 10, 64); err == nil {
				record.Timestamp = time.Unix(timestamp, 0)
			}
		}

		records = append(records, record)
	}

	q.stats.HitCount++
	q.stats.updateHitRate()
	return records, nil
}

// GetUserLikeStats 获取用户点赞统计
func (q *QueryOperations) GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error) {
	userStatsKey := q.client.userStatsKey(userID)

	// 从Hash获取用户统计信息
	statsMap, err := q.client.client.HGetAll(ctx, userStatsKey).Result()
	if err != nil {
		if err == redis.Nil {
			q.stats.MissCount++
			q.stats.updateHitRate()
			return &types.UserLikeStats{UserID: userID}, nil
		}
		q.stats.ErrorCount++
		return nil, fmt.Errorf("获取用户统计信息失败: %w", err)
	}

	stats := &types.UserLikeStats{
		UserID: userID,
	}

	if totalLikesStr, ok := statsMap["total_likes"]; ok {
		if totalLikes, err := strconv.ParseInt(totalLikesStr, 10, 64); err == nil {
			stats.TotalLikes = totalLikes
		}
	}

	if lastLikeTimeStr, ok := statsMap["last_like_time"]; ok {
		if lastLikeTime, err := strconv.ParseInt(lastLikeTimeStr, 10, 64); err == nil {
			lastTime := time.Unix(lastLikeTime, 0)
			stats.LastLikeAt = &lastTime
		}
	}

	q.stats.HitCount++
	q.stats.updateHitRate()
	return stats, nil
}

// GetItemLikeStats 获取内容点赞统计
func (q *QueryOperations) GetItemLikeStats(ctx context.Context, itemID, itemType string) (*types.ItemLikeStats, error) {
	statsKey := q.client.statsKey(itemType, itemID)
	countKey := q.client.countKey(itemType, itemID)

	pipe := q.client.Pipeline()
	statsCmd := pipe.HGetAll(ctx, statsKey)
	countCmd := pipe.Get(ctx, countKey)

	_, err := pipe.Exec(ctx)
	if err != nil {
		q.stats.ErrorCount++
		return nil, fmt.Errorf("获取内容统计信息失败: %w", err)
	}

	stats := &types.ItemLikeStats{
		ItemID:   itemID,
		ItemType: itemType,
	}

	// 获取当前点赞数
	if count, err := countCmd.Int64(); err == nil {
		stats.TotalLikes = count
	}

	// 获取详细统计
	statsMap, err := statsCmd.Result()
	if err == nil {
		if totalLikesStr, ok := statsMap["total_likes"]; ok {
			if totalLikes, err := strconv.ParseInt(totalLikesStr, 10, 64); err == nil {
				stats.TotalLikes = totalLikes
			}
		}

		if lastLikeTimeStr, ok := statsMap["last_like_time"]; ok {
			if lastLikeTime, err := strconv.ParseInt(lastLikeTimeStr, 10, 64); err == nil {
				lastTime := time.Unix(lastLikeTime, 0)
				stats.LastLikeAt = &lastTime
			}
		}
	}

	q.stats.HitCount++
	q.stats.updateHitRate()
	return stats, nil
}

// 辅助函数：从Redis消息中获取字符串值
func getStringValue(values map[string]interface{}, key string) string {
	if value, ok := values[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}
