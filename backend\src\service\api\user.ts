import { request } from '../request';

export type UserResult = {
  success: boolean;
  data: {
    /** 头像 */
    avatar: string;
    /** 用户名 */
    username: string;
    /** 昵称 */
    nickname: string;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** 按钮级别权限 */
    permissions: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/**
 * Login
 *
 * @param data Login data
 */
export function fetchLogin(data?: object) {
  return request<UserResult>({
    url: '/login',
    method: 'post',
    data
  });
}

/**
 * Refresh token
 *
 * @param data Refresh token data
 */
export function fetchRefreshToken(data?: object) {
  return request<RefreshTokenResult>({
    url: '/refresh-token',
    method: 'post',
    data
  });
}
