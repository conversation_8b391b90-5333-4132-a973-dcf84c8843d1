<template>
    <div class="picture-table">
        <!-- 批量操作工具栏 -->
        <div v-if="selectedRows.length > 0" class="batch-toolbar">
            <div class="batch-info">
                <el-icon>
                    <Check />
                </el-icon>
                <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
            </div>
            <div class="batch-actions">
                <el-button type="success" size="small" @click="handleBatchStatus(1)">
                    批量启用
                </el-button>
                <el-button type="warning" size="small" @click="handleBatchStatus(0)">
                    批量禁用
                </el-button>
                <el-button type="danger" size="small" @click="handleBatchDelete">
                    批量删除
                </el-button>
            </div>
        </div>

        <!-- 使用SlinkyTable组件 -->
        <SlinkyTable ref="tableRef" :data="pictures" :loading="loading" row-key="id"
            @selection-change="handleSelectionChange" show-selection show-index index-label="序号" :show-header="true"
            :show-actions="true" action-label="操作" action-width="200" :show-view="true" :show-edit="true"
            :show-delete="true" @view="handleView" @edit="handleEdit" @delete="handleDelete"
            @refresh="$emit('refresh')">
            <!-- 标题列 -->
            <el-table-column prop="title" label="基本信息" align="left" min-width="180">
                <template #default="{ row }">
                    <div class="picture-info">
                        <div class="picture-cover-wrapper">
                            <el-image :src="row.url" fit="cover" class="picture-cover" :preview-src-list="[row.url]"
                                preview-teleported :initial-index="0">
                                <template #error>
                                    <div class="image-placeholder">
                                        <el-icon>
                                            <PictureIcon />
                                        </el-icon>
                                    </div>
                                </template>
                            </el-image>
                        </div>
                        <div class="picture-detail">
                            <div class="picture-title" :title="row.title">{{ row.title }}</div>
                            <div class="picture-meta">
                                <span class="resolution">{{ row.width }}x{{ row.height }}</span>
                                <span class="size">{{ formatSize(row.size) }}</span>
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 分类列 -->
            <el-table-column prop="category_name" label="分类" min-width="100">
                <template #default="{ row }">
                    <el-tag size="small" type="info" effect="plain">
                        {{ row.category_name || '未分类' }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 专辑列 -->
            <el-table-column prop="album_title" label="专辑" min-width="120">
                <template #default="{ row }">
                    <el-tag size="small" type="success" effect="plain" v-if="row.album_title">
                        {{ row.album_title }}
                    </el-tag>
                    <span v-else>未分配</span>
                </template>
            </el-table-column>

            <!-- 状态列 -->
            <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'danger'"
                        :effect="row.status === 1 ? 'light' : 'plain'" size="small">
                        {{ row.status === 1 ? '正常' : '禁用' }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 数据统计列 -->
            <el-table-column label="数据统计" width="180">
                <template #default="{ row }">
                    <div class="stats">
                        <el-tooltip content="查看量" placement="top">
                            <span class="stat-item">
                                <el-icon>
                                    <View />
                                </el-icon>
                                {{ formatNumber(row.view_count) }}
                            </span>
                        </el-tooltip>
                        <el-tooltip content="点赞数" placement="top">
                            <span class="stat-item">
                                <el-icon>
                                    <Star />
                                </el-icon>
                                {{ formatNumber(row.like_count) }}
                            </span>
                        </el-tooltip>
                    </div>
                </template>
            </el-table-column>

            <!-- 创建时间列 -->
            <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                    {{ formatDate(row.created_at) }}
                </template>
            </el-table-column>

            <!-- 自定义操作列 -->
            <template #actions="{ row }">
                <el-button type="primary" link size="small" @click="handleView(row)">
                    查看
                </el-button>
                <el-button type="primary" link size="small" @click="handleEdit(row)">
                    编辑
                </el-button>
                <!-- 根据状态显示启用或禁用按钮 -->
                <el-button v-if="row.status === 1" type="warning" link size="small" @click="handleChangeStatus(row, 0)">
                    禁用
                </el-button>
                <el-button v-else type="success" link size="small" @click="handleChangeStatus(row, 1)">
                    启用
                </el-button>
                <el-button type="danger" link size="small" @click="handleDelete(row)">
                    删除
                </el-button>
            </template>
        </SlinkyTable>

        <!-- 分页器 -->
        <div class="table-footer">
            <SinglePager :current-page="currentPage" :page-size="pageSize" :total="total"
                @current-change="handlePageChange" @size-change="handleSizeChange" show-jump-info />
        </div>
    </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import { batchDeletePicture, batchUpdatePictureStatus } from '@/service/api/pictures/pictures';
import type { Picture } from '@/types/pictures';
import {
    Check,
    Picture as PictureIcon,
    Star,
    View
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { defineEmits, defineProps, ref } from 'vue';

const props = defineProps({
    pictures: {
        type: Array as () => Picture[],
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    total: {
        type: Number,
        default: 0
    },
    currentPage: {
        type: Number,
        default: 1
    },
    pageSize: {
        type: Number,
        default: 10
    }
});

const emit = defineEmits([
    'update:currentPage',
    'update:pageSize',
    'refresh',
    'view',
    'edit',
    'delete',
    'change-status'
]);

// 表格引用
const tableRef = ref();

// 选中的行
const selectedRows = ref<Picture[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: Picture[]) => {
    selectedRows.value = selection;
};

// 处理查看
const handleView = (row: Picture) => {
    emit('view', row);
};

// 处理编辑
const handleEdit = (row: Picture) => {
    emit('edit', row);
};

// 处理删除
const handleDelete = (row: Picture) => {
    ElMessageBox.confirm(`确定要删除图片"${row.title}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        emit('delete', row);
    }).catch(() => {
        // 取消删除
    });
};

// 处理状态变更
const handleChangeStatus = (row: Picture, status: number) => {
    const statusText = status === 1 ? '启用' : '禁用';
    ElMessageBox.confirm(`确定要${statusText}图片"${row.title}"吗？`, '状态变更确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        emit('change-status', { id: row.id, status });
    }).catch(() => {
        // 取消操作
    });
};

// 批量更新状态
const handleBatchStatus = (status: number) => {
    const ids = selectedRows.value.map(item => item.id);
    const statusText = status === 1 ? '启用' : '禁用';

    ElMessageBox.confirm(`确定要批量${statusText}选中的${selectedRows.value.length}张图片吗？`, '批量状态变更确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await batchUpdatePictureStatus(ids, status);
            ElMessage.success(`批量${statusText}成功`);
            emit('refresh');
            // 清除选择
            if (tableRef.value) {
                tableRef.value.clearSelection();
            }
        } catch (error) {
            console.error('批量更新状态失败:', error);
            ElMessage.error(`批量${statusText}失败`);
        }
    }).catch(() => {
        // 取消操作
    });
};

// 批量删除
const handleBatchDelete = () => {
    const ids = selectedRows.value.map(item => item.id);

    ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}张图片吗？`, '批量删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await batchDeletePicture(ids);
            ElMessage.success('批量删除成功');
            emit('refresh');
            // 清除选择
            if (tableRef.value) {
                tableRef.value.clearSelection();
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            ElMessage.error('批量删除失败');
        }
    }).catch(() => {
        // 取消操作
    });
};

// 处理页码变化
const handlePageChange = (page: number) => {
    emit('update:currentPage', page);
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
    emit('update:pageSize', size);
};

// 格式化文件大小
const formatSize = (bytes: number) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化数字
const formatNumber = (num: number) => {
    if (!num) return '0';
    if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
};

// 格式化日期
const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toLocaleString();
};
</script>

<style scoped lang="scss">
.picture-table {
    .batch-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        margin-bottom: 16px;
        background-color: #f0f9eb;
        border: 1px solid #e1f3d8;
        border-radius: 4px;

        .batch-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #67c23a;

            strong {
                font-weight: bold;
                margin: 0 4px;
            }
        }

        .batch-actions {
            display: flex;
            gap: 8px;
        }
    }

    .picture-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .picture-cover-wrapper {
            width: 80px;
            height: 80px;
            border-radius: 4px;
            overflow: hidden;
            flex-shrink: 0;
            border: 1px solid #ebeef5;
        }

        .picture-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f7fa;
            color: #909399;
        }

        .picture-detail {
            display: flex;
            flex-direction: column;
            gap: 4px;
            overflow: hidden;

            .picture-title {
                font-weight: 500;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .picture-meta {
                display: flex;
                gap: 8px;
                color: #909399;
                font-size: 12px;
            }
        }
    }

    .stats {
        display: flex;
        gap: 12px;

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #606266;
        }
    }

    .table-footer {
        padding: 16px 24px;
        background: var(--el-bg-color-page);
        border-top: 1px solid var(--el-border-color-lighter);
    }
}
</style>
