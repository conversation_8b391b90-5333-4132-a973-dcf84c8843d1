package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserVipRepository 用户VIP数据访问接口
type UserVipRepository interface {
	base.ExtendedRepository[users.UserVip]
}

// userVipRepository 用户VIP数据访问实现
type userVipRepository struct {
	base.ExtendedRepository[users.UserVip]
}

// NewUserVipRepository 创建用户VIP仓库实例
func NewUserVipRepository(db *gorm.DB) UserVipRepository {
	return &userVipRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserVip](db),
	}
}
