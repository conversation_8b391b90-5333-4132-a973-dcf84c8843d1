package system

import (
	"fmt"
	"math/rand"
	"mime/multipart"
	"net/url"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"frontapi/internal/admin"
	imageUtils "frontapi/pkg/image_utils"
	"frontapi/pkg/utils"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
	"github.com/google/uuid"
	"github.com/joho/godotenv"
)

// 文件上传类型常量
const (
	UploadTypeSmallImage = "small_image" // 小图片，如分类图标、头像等
	UploadTypePicture    = "picture"     // 图片模块的图片
	UploadTypeVideo      = "video"       // 视频
)

// UploadProgress 存储上传进度信息
type UploadProgress struct {
	ID           string  `json:"id"`              // 上传ID
	FileName     string  `json:"fileName"`        // 文件名
	TotalSize    int64   `json:"totalSize"`       // 总大小
	UploadedSize int64   `json:"uploadedSize"`    // 已上传大小
	Percentage   float64 `json:"percentage"`      // 百分比
	Status       string  `json:"status"`          // 状态：uploading, completed, failed
	Error        string  `json:"error,omitempty"` // 错误信息，如果有
}

// UploadController 系统上传控制器
type UploadController struct {
	admin.BaseController
	// 上传路径配置
	uploadPaths map[string]string
	// 访问URL前缀配置
	urlPrefixes map[string]string
	// 文件大小限制(MB)
	sizeLimits map[string]int
	// 允许的文件类型
	allowedTypes map[string][]string
	// 上传进度存储
	progressStore map[string]*UploadProgress
}

// NewUploadController 创建一个新的上传控制器
func NewUploadController() *UploadController {
	controller := &UploadController{
		uploadPaths: make(map[string]string),
		urlPrefixes: make(map[string]string),
		sizeLimits: map[string]int{
			UploadTypeSmallImage: 5,    // 小图片限制5MB
			UploadTypePicture:    10,   // 图片限制10MB
			UploadTypeVideo:      2048, // 视频限制2048MB
		},
		allowedTypes: map[string][]string{
			UploadTypeSmallImage: {"image/jpeg", "image/png", "image/gif", "image/webp"},
			UploadTypePicture:    {"image/jpeg", "image/png", "image/gif", "image/webp"},
			UploadTypeVideo:      {"video/mp4", "video/mpeg", "video/quicktime", "video/x-msvideo", "video/x-matroska"},
		},
		progressStore: make(map[string]*UploadProgress),
	}

	// 加载环境变量
	controller.loadConfig()

	// 确保上传目录存在
	for _, path := range controller.uploadPaths {
		controller.ensureDirectoryExists(path)
	}

	return controller
}

// ensureDirectoryExists 确保目录存在
func (c *UploadController) ensureDirectoryExists(dir string) {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		os.MkdirAll(dir, 0755)
	}
}

// loadConfig 从.env文件加载配置
func (c *UploadController) loadConfig() {
	// 尝试加载.env文件
	godotenv.Load(".env")

	// 设置默认路径，如果环境变量不存在
	defaultPaths := map[string]string{
		UploadTypeSmallImage: "./storage/static/images",
		UploadTypePicture:    "./storage/pictures",
		UploadTypeVideo:      "./storage/videos",
	}

	defaultURLs := map[string]string{
		UploadTypeSmallImage: "/static/images",
		UploadTypePicture:    "/pictures",
		UploadTypeVideo:      "/videos",
	}

	// 从环境变量读取上传路径配置
	c.uploadPaths[UploadTypeSmallImage] = utils.GetEnv("UPLOAD_SMALL_IMAGE_PATH", defaultPaths[UploadTypeSmallImage])
	c.uploadPaths[UploadTypePicture] = utils.GetEnv("UPLOAD_PICTURE_PATH", defaultPaths[UploadTypePicture])
	c.uploadPaths[UploadTypeVideo] = utils.GetEnv("UPLOAD_VIDEO_PATH", defaultPaths[UploadTypeVideo])

	// 从环境变量读取URL前缀配置
	c.urlPrefixes[UploadTypeSmallImage] = utils.GetEnv("UPLOAD_SMALL_IMAGE_URL", defaultURLs[UploadTypeSmallImage])
	c.urlPrefixes[UploadTypePicture] = utils.GetEnv("UPLOAD_PICTURE_URL", defaultURLs[UploadTypePicture])
	c.urlPrefixes[UploadTypeVideo] = utils.GetEnv("UPLOAD_VIDEO_URL", defaultURLs[UploadTypeVideo])
}

// UploadImage 上传小图片(分类等)接口
func (c *UploadController) UploadImage(ctx *fiber.Ctx) error {
	return c.handleUpload(ctx, UploadTypeSmallImage)
}

// UploadPicture 上传图片(图片模块)接口
func (c *UploadController) UploadPicture(ctx *fiber.Ctx) error {
	return c.handleUpload(ctx, UploadTypePicture)
}

// UploadVideo 上传视频接口
func (c *UploadController) UploadVideo(ctx *fiber.Ctx) error {
	return c.handleUpload(ctx, UploadTypeVideo)
}

// UploadVideoWithProgress 处理带进度的视频上传
// 支持前端通过websocket实时获取上传进度
func (c *UploadController) UploadVideoWithProgress(ctx *fiber.Ctx) error {
	// 生成唯一的上传ID
	uploadID := uuid.New().String()

	// 获取上传的文件信息
	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		return utils.BadRequest(ctx, "文件上传失败: "+err.Error(), nil)
	}

	// 初始化进度信息
	c.progressStore[uploadID] = &UploadProgress{
		ID:           uploadID,
		FileName:     fileHeader.Filename,
		TotalSize:    fileHeader.Size,
		UploadedSize: 0,
		Percentage:   0,
		Status:       "uploading",
	}

	// 检查文件大小
	maxSize := c.sizeLimits[UploadTypeVideo] * 1024 * 1024
	if fileHeader.Size > int64(maxSize) {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = fmt.Sprintf("文件大小不能超过 %dMB", c.sizeLimits[UploadTypeVideo])
		return utils.BadRequest(ctx, c.progressStore[uploadID].Error, nil)
	}

	// 对视频类型进行MIME检查
	if !c.isAllowedVideoType(fileHeader) {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "不支持的视频格式，请上传MP4/AVI/MOV/MKV格式的视频"
		return utils.BadRequest(ctx, c.progressStore[uploadID].Error, nil)
	}

	// 获取子目录（可选）
	subDir := ctx.FormValue("sub_dir", "")
	if subDir != "" {
		// 简单清理子目录名称，防止路径遍历攻击
		subDir = filepath.Clean(subDir)
		subDir = strings.TrimPrefix(subDir, "/")
		subDir = strings.TrimPrefix(subDir, "\\")
		if subDir != "" {
			// 确保子目录存在
			fullDir := filepath.Join(c.uploadPaths[UploadTypeVideo], subDir)
			c.ensureDirectoryExists(fullDir)
		}
	}

	// 生成唯一文件名
	filename := c.generateUniqueFilename(fileHeader.Filename)

	// 确定保存路径
	savePath := c.uploadPaths[UploadTypeVideo]
	if subDir != "" {
		savePath = filepath.Join(savePath, subDir)
	}

	// 保存文件路径
	uploadPath := filepath.Join(savePath, filename)

	// 打开源文件
	src, err := fileHeader.Open()
	if err != nil {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "打开上传文件失败: " + err.Error()
		return utils.InternalServerError(ctx, c.progressStore[uploadID].Error)
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(uploadPath)
	if err != nil {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "创建目标文件失败: " + err.Error()
		return utils.InternalServerError(ctx, c.progressStore[uploadID].Error)
	}
	defer dst.Close()

	// 创建带进度的写入器
	progressWriter := &ProgressWriter{
		dst:      dst,
		progress: c.progressStore[uploadID],
	}

	// 使用io.Copy写入文件，自动触发进度更新
	_, err = progressWriter.ReadFrom(src)
	if err != nil {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "保存文件失败: " + err.Error()
		return utils.InternalServerError(ctx, c.progressStore[uploadID].Error)
	}

	// 更新进度为完成
	c.progressStore[uploadID].Status = "completed"
	c.progressStore[uploadID].Percentage = 100

	// 构建访问URL
	urlPrefix := c.urlPrefixes[UploadTypeVideo]
	fileURL := fmt.Sprintf("%s/%s", urlPrefix, filename)
	baseURL := fmt.Sprintf("%s", filename)
	if subDir != "" {
		fileURL = fmt.Sprintf("%s/%s/%s", urlPrefix, subDir, filename)
		baseURL = fmt.Sprintf("%s/%s", subDir, filename)
	}

	// 返回上传成功的响应
	return utils.Success(ctx, fiber.Map{
		"url":          fileURL,
		"name":         filename,
		"originalUrl":  urlPrefix,
		"file":         baseURL,
		"originalName": fileHeader.Filename,
		"size":         fileHeader.Size,
		"type":         UploadTypeVideo,
		"uploadId":     uploadID,
	}, "上传成功")
}

// GetUploadProgress 获取上传进度
func (c *UploadController) GetUploadProgress(ctx *fiber.Ctx) error {
	uploadID := ctx.Params("id")
	if uploadID == "" {
		return utils.BadRequest(ctx, "缺少上传ID", nil)
	}

	progress, exists := c.progressStore[uploadID]
	if !exists {
		return utils.NotFound(ctx, "未找到上传记录")
	}

	return utils.Success(ctx, progress, "获取进度成功")
}

// UploadProgressWebSocket 通过WebSocket提供实时上传进度
func (c *UploadController) UploadProgressWebSocket(conn *websocket.Conn) {
	// 获取上传ID
	uploadID := conn.Params("id")
	if uploadID == "" {
		conn.Close()
		return
	}

	// 定时发送进度更新
	ticker := time.NewTicker(500 * time.Millisecond) // 每500ms发送一次
	defer ticker.Stop()

	// 首次发送当前进度
	progress, exists := c.progressStore[uploadID]
	if exists {
		conn.WriteJSON(progress)
	} else {
		conn.WriteJSON(fiber.Map{"error": "未找到上传记录"})
		conn.Close()
		return
	}

	// 持续发送进度更新
	for range ticker.C {
		progress, exists := c.progressStore[uploadID]
		if !exists {
			conn.WriteJSON(fiber.Map{"error": "上传记录已失效"})
			conn.Close()
			return
		}

		if err := conn.WriteJSON(progress); err != nil {
			break
		}

		// 如果上传已完成或失败，结束WebSocket连接
		if progress.Status == "completed" || progress.Status == "failed" {
			// 再发送一次最终状态
			conn.WriteJSON(progress)
			break
		}
	}

	conn.Close()
}

// ProgressWriter 用于跟踪上传进度的写入器
type ProgressWriter struct {
	dst      *os.File
	progress *UploadProgress
}

// ReadFrom 实现io.ReaderFrom接口，用于从源读取并更新进度
func (pw *ProgressWriter) ReadFrom(src multipart.File) (int64, error) {
	buf := make([]byte, 32*1024) // 32KB的缓冲区
	var total int64 = 0

	for {
		n, err := src.Read(buf)
		if n > 0 {
			// 写入文件
			if _, err := pw.dst.Write(buf[:n]); err != nil {
				return total, err
			}

			total += int64(n)

			// 更新进度
			pw.progress.UploadedSize = total
			if pw.progress.TotalSize > 0 {
				pw.progress.Percentage = float64(total) / float64(pw.progress.TotalSize) * 100
			}
		}

		if err != nil {
			// 到达文件末尾是正常的
			if err.Error() == "EOF" {
				break
			}
			return total, err
		}
	}

	return total, nil
}

// handleUpload 处理文件上传的通用方法
func (c *UploadController) handleUpload(ctx *fiber.Ctx, uploadType string) error {
	// 获取上传的文件
	file, err := ctx.FormFile("file")
	if err != nil {
		return utils.BadRequest(ctx, "文件上传失败: "+err.Error(), nil)
	}

	// 检查文件大小
	maxSize := c.sizeLimits[uploadType] * 1024 * 1024
	if file.Size > int64(maxSize) {
		return utils.BadRequest(ctx, fmt.Sprintf("文件大小不能超过 %dMB", c.sizeLimits[uploadType]), nil)
	}

	// 对图片类型进行MIME检查
	if uploadType == UploadTypeSmallImage || uploadType == UploadTypePicture {
		if !c.isAllowedImageType(file, uploadType) {
			return utils.BadRequest(ctx, "不支持的图片格式，请上传JPG/PNG/GIF/WEBP格式的图片", nil)
		}
	}

	// 对视频类型进行MIME检查
	if uploadType == UploadTypeVideo {
		if !c.isAllowedVideoType(file) {
			return utils.BadRequest(ctx, "不支持的视频格式，请上传MP4/AVI/MOV/MKV格式的视频", nil)
		}
	}

	// 获取子目录（可选）
	subDir := ctx.FormValue("sub_dir", "")
	if subDir != "" {
		// 简单清理子目录名称，防止路径遍历攻击
		subDir = filepath.Clean(subDir)
		subDir = strings.TrimPrefix(subDir, "/")
		subDir = strings.TrimPrefix(subDir, "\\")
		if subDir != "" {
			// 确保子目录存在
			fullDir := filepath.Join(c.uploadPaths[uploadType], subDir)
			c.ensureDirectoryExists(fullDir)
		}
	}

	// 生成唯一文件名
	filename := c.generateUniqueFilename(file.Filename)

	// 确定保存路径
	savePath := c.uploadPaths[uploadType]
	if subDir != "" {
		savePath = filepath.Join(savePath, subDir)
	}

	// 保存文件路径
	uploadPath := filepath.Join(savePath, filename)

	// 保存文件
	if err := ctx.SaveFile(file, uploadPath); err != nil {
		return utils.InternalServerError(ctx, "保存文件失败: "+err.Error())
	}

	// 如果是图片文件，异步压缩图片
	if uploadType == UploadTypeSmallImage || uploadType == UploadTypePicture {
		fileExt := strings.ToLower(filepath.Ext(filename))
		if fileExt == ".jpg" || fileExt == ".jpeg" || fileExt == ".png" {
			// 使用新的图片处理包进行异步压缩
			imageUtils.CompressImageAsync(uploadPath)
		}
	}

	// 构建访问URL
	urlPrefix := c.urlPrefixes[uploadType]
	fileURL := fmt.Sprintf("%s/%s", urlPrefix, filename)
	if subDir != "" {
		fileURL = fmt.Sprintf("%s/%s/%s", urlPrefix, subDir, filename)
	}

	// 返回上传成功的响应
	return utils.Success(ctx, fiber.Map{
		"url":          fileURL,
		"name":         filename,
		"originalName": file.Filename,
		"size":         file.Size,
		"type":         uploadType,
	}, "上传成功")
}

// isAllowedImageType 检查是否是允许的图片类型
func (c *UploadController) isAllowedImageType(file *multipart.FileHeader, uploadType string) bool {
	// 打开文件以检查其内容类型
	src, err := file.Open()
	if err != nil {
		return false
	}
	defer src.Close()

	// 读取文件头部来判断类型
	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil {
		return false
	}

	// 获取文件类型
	contentType := utils.GetFileContentType(buffer)

	// 检查是否是允许的类型
	for _, allowedType := range c.allowedTypes[uploadType] {
		if contentType == allowedType {
			return true
		}
	}

	return false
}

// isAllowedVideoType 检查是否是允许的视频类型
func (c *UploadController) isAllowedVideoType(file *multipart.FileHeader) bool {
	// 对视频文件，可以根据扩展名来初步判断
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := []string{".mp4", ".avi", ".mov", ".mkv", ".mpeg", ".mpg"}

	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return true
		}
	}

	// 也可以检查MIME类型
	src, err := file.Open()
	if err != nil {
		return false
	}
	defer src.Close()

	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil {
		return false
	}

	contentType := utils.GetFileContentType(buffer)
	for _, allowedType := range c.allowedTypes[UploadTypeVideo] {
		if strings.HasPrefix(contentType, "video/") || contentType == allowedType {
			return true
		}
	}

	return false
}

// generateUniqueFilename 生成唯一的文件名
func (c *UploadController) generateUniqueFilename(originalFilename string) string {
	// 获取文件扩展名
	ext := filepath.Ext(originalFilename)

	// 使用时间戳和随机字符串生成唯一文件名
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	randomStr := utils.RandomString(8)

	return fmt.Sprintf("%d_%s%s", timestamp, randomStr, ext)
}

// DeleteFile 删除上传的文件
func (c *UploadController) DeleteFile(ctx *fiber.Ctx) error {
	var request struct {
		Path string `json:"path"`
	}

	if err := ctx.BodyParser(&request); err != nil {
		return utils.BadRequest(ctx, "无效的请求数据: "+err.Error(), nil)
	}

	if request.Path == "" {
		return utils.BadRequest(ctx, "文件路径不能为空", nil)
	}

	// 解析 URL 或路径
	u, err := url.Parse(request.Path)
	if err != nil {
		return utils.BadRequest(ctx, "无效的路径或URL: "+err.Error(), nil)
	}

	var cleanPath string
	if u.Scheme == "http" || u.Scheme == "https" {
		cleanPath = filepath.Clean(u.Path) // /pictures/pic/xxx.png
	} else {
		cleanPath = filepath.Clean(request.Path)
	}

	cleanPath = strings.ReplaceAll(cleanPath, "\\", "/") // Windows 路径统一成 /
	isAllowed := false
	filePath := ""

	for key, storagePath := range c.uploadPaths {
		urlPrefix := c.urlPrefixes[key]

		// 检查完整URL匹配
		if strings.HasPrefix(request.Path, urlPrefix) || strings.HasPrefix(cleanPath, "/"+key) {
			topDir := utils.GetFirstPathSegment(cleanPath, string(os.PathSeparator))
			if topDir == cleanPath {
				topDir = utils.GetFirstPathSegment(cleanPath, "/")
			}
			// 构造相对路径
			relativePath := strings.TrimPrefix(cleanPath, "/"+topDir)
			relativePath = strings.TrimPrefix(relativePath, "/")
			filePath = filepath.Join(storagePath, relativePath)
			isAllowed = true
			break
		}
	}

	if !isAllowed {
		return utils.BadRequest(ctx, "无法删除指定路径的文件，只允许 static/images, pictures, videos 目录", nil)
	}

	filePath = filepath.Clean(filePath)

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return utils.NotFound(ctx, "文件不存在: "+filePath)
	}

	// 缓解 Windows 占用问题
	if f, err := os.OpenFile(filePath, os.O_RDWR, 0666); err == nil {
		f.Close()
	}
	runtime.GC()

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		return utils.InternalServerError(ctx, "删除文件失败: "+err.Error())
	}

	return utils.Success(ctx, fiber.Map{
		"path":    request.Path,
		"deleted": true,
	}, "文件删除成功")
}

// UploadPicture 上传图片（带进度条）
func (c *UploadController) UploadPictureWithProgress(ctx *fiber.Ctx) error {
	// 获取文件
	fileHeader, err := ctx.FormFile("file")
	if err != nil {
		return c.BadRequest(ctx, "获取上传文件失败", nil)
	}
	// 生成唯一的上传ID
	uploadID := uuid.New().String()

	// 文件大小限制 (10MB)
	maxSize := int64(10 * 1024 * 1024)
	if fileHeader.Size > maxSize {
		return c.BadRequest(ctx, "图片大小超过限制，最大允许：10MB", nil)
	}

	// 检查文件后缀名
	extension := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !c.isAllowedImageType(fileHeader, UploadTypePicture) {
		return c.BadRequest(ctx, "不支持的图片格式，仅支持 jpg、jpeg、png、gif、webp", nil)
	}
	// 初始化进度信息
	c.progressStore[uploadID] = &UploadProgress{
		ID:           uploadID,
		FileName:     fileHeader.Filename,
		TotalSize:    fileHeader.Size,
		UploadedSize: 0,
		Percentage:   0,
		Status:       "uploading",
	}
	// 生成文件名和保存路径
	// 获取子目录（可选）
	subDir := ctx.FormValue("sub_dir", "")
	if subDir != "" {
		// 简单清理子目录名称，防止路径遍历攻击
		subDir = filepath.Clean(subDir)
		subDir = strings.TrimPrefix(subDir, "/")
		subDir = strings.TrimPrefix(subDir, "\\")
		if subDir != "" {
			// 确保子目录存在
			fullDir := filepath.Join(c.uploadPaths[UploadTypePicture], subDir)
			c.ensureDirectoryExists(fullDir)
		}
	}

	// 生成唯一文件名
	filename := c.generatePictureFileName(extension)

	// 确定保存路径
	savePath := c.uploadPaths[UploadTypePicture]
	if subDir != "" {
		savePath = filepath.Join(savePath, subDir)
	}

	// 保存文件路径
	uploadPath := filepath.Join(savePath, filename)

	// 打开源文件
	src, err := fileHeader.Open()
	if err != nil {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "打开上传文件失败: " + err.Error()
		return utils.InternalServerError(ctx, c.progressStore[uploadID].Error)
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(uploadPath)
	if err != nil {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "创建目标文件失败: " + err.Error()
		return utils.InternalServerError(ctx, c.progressStore[uploadID].Error)
	}
	defer dst.Close()

	// 创建带进度的写入器
	progressWriter := &ProgressWriter{
		dst:      dst,
		progress: c.progressStore[uploadID],
	}

	// 使用io.Copy写入文件，自动触发进度更新
	_, err = progressWriter.ReadFrom(src)
	if err != nil {
		c.progressStore[uploadID].Status = "failed"
		c.progressStore[uploadID].Error = "保存文件失败: " + err.Error()
		return utils.InternalServerError(ctx, c.progressStore[uploadID].Error)
	}

	// 更新进度为完成
	c.progressStore[uploadID].Status = "completed"
	c.progressStore[uploadID].Percentage = 100

	// 构建访问URL
	urlPrefix := c.urlPrefixes[UploadTypePicture]
	fileURL := fmt.Sprintf("%s/%s", urlPrefix, filename)
	baseURL := fmt.Sprintf("%s", filename)
	if subDir != "" {
		fileURL = fmt.Sprintf("%s/%s/%s", urlPrefix, subDir, filename)
		baseURL = fmt.Sprintf("%s/%s", subDir, filename)
	}

	// 返回上传成功的响应
	return utils.Success(ctx, fiber.Map{
		"url":          fileURL,
		"name":         filename,
		"originalUrl":  urlPrefix,
		"file":         baseURL,
		"originalName": fileHeader.Filename,
		"size":         fileHeader.Size,
		"type":         UploadTypeVideo,
		"uploadId":     uploadID,
	}, "上传成功")
}

// 生成图片文件名
func (c *UploadController) generatePictureFileName(extension string) string {
	// 使用 UUID + 时间戳 + 随机数生成唯一文件名
	timestamp := time.Now().UnixNano() / 1000000
	randomNum := rand.Intn(10000)
	fileName := fmt.Sprintf("%s_%d_%d%s",
		strings.ReplaceAll(uuid.New().String(), "-", ""),
		timestamp,
		randomNum,
		extension)
	return fileName
}
