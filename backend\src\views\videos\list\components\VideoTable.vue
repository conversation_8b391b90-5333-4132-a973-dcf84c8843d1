<template>
  <div class="video-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span> 
        <el-button type="primary" link @click="clearSelection">取消选择</el-button>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量禁用
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量启用
        </el-button>
        <el-button type="warning" size="small" @click="handleBatchStatus(1)">
          批量下架
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(2)">
          批量上架
        </el-button>
        <el-button type="warning" size="small" @click="handleBatchAudit">
          批量审核
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 - 使用SlinkyTable -->
    <div class="table-content">
      <SlinkyTable
        :data="videoList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="编辑"
        delete-text="删除"
        empty-text="暂无视频数据"
        class="video-data-table"
      >
        <!-- 视频信息列 -->
        <el-table-column prop="title" label="视频信息" align="left" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="video-info">
              <el-image
                style="width: 80px; height: 45px"
                :src="row.cover"
                :preview-src-list="[row.cover]"
                fit="cover"
                lazy
                class="video-thumbnail"
              ></el-image>
              <div class="video-details">
                <div class="video-title">{{ row.title }}</div>
                <div class="video-description">{{ formatDuration(row.duration) }} | {{ row.celebrities?.join("、") || '-' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 分类列 -->
        <el-table-column prop="category_name" label="分类" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.category_name" type="info" size="small" effect="light">
              {{ row.category_name }}
            </el-tag>
            <span v-else class="placeholder-text">未分类</span>
          </template>
        </el-table-column>

        <!-- 标签列 -->
        <el-table-column prop="tags" label="标签" min-width="150" show-overflow-tooltip>
          <template #default="scope">
            <el-tag 
              v-for="tag in scope.row.tags" 
              :key="tag" 
              size="small" 
              type="info"
              effect="plain"
              style="margin-right: 5px; margin-bottom: 2px;"
            >
              {{ tag }}
            </el-tag>
            <span v-if="!scope.row.tags || scope.row.tags.length === 0" class="placeholder-text">-</span>
          </template>
        </el-table-column>

        <!-- 统计信息列 -->
        <el-table-column label="统计" min-width="140" align="center">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon class="stat-icon"><View /></el-icon>
                <span class="stat-value">{{ formatNumber(row.view_count || 0) }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><Star /></el-icon>
                <span class="stat-value">{{ formatNumber(row.like_count || 0) }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><ChatDotRound /></el-icon>
                <span class="stat-value">{{ formatNumber(row.comment_count || 0) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 评分列 -->
        <el-table-column prop="score" label="评分" width="100" align="center">
          <template #default="{ row }">
            <el-rate
              v-model="row.score"
              disabled
              text-color="#ff9900"
              show-score
              score-template="{value}"
              :max="5"
              :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
            />
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <!-- 状态：0-待审核,1-已下架,2-已发布,-2-已拒绝,-4-已删除 -->
            <el-tag v-if="row.status === 1" type="danger">{{ $t('common.status.unpublished') }}</el-tag>
            <el-tag v-else-if="row.status === 0" type="info">{{ $t('common.status.pending') }}</el-tag>
            <el-tag v-else-if="row.status === 2" type="success">{{ $t('common.status.published') }}</el-tag>
            <el-tag v-else-if="row.status === -2" type="danger">{{ $t('common.status.rejected') }}</el-tag>
            <el-tag v-else-if="row.status === -4" type="danger">{{ $t('common.status.deleted') }}</el-tag>
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="upload_time" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ formatDate(row.upload_time || row.created_at) }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button 
              type="warning" 
              link 
              size="small" 
              v-if="row.status === 0"
              @click="handleAudit(row)"
            >
              <el-icon><Check /></el-icon>
              审核
            </el-button>
            <el-popconfirm
        
              :title="`确定要${row.status === 2 ? '下架' : '上架'}视频 ${row.title} 吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 2 ? 1 : 2)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 2 ? 'warning' : 'success'" 
                  link 
                  size="small"
                  v-if="row.status !== -2"
                >
                  <el-icon>
                    <component :is="row.status === 2 ? Lock : Unlock" />
                  </el-icon>
                  {{ row.status === 2 ? '下架' : '上架' }}
                </el-button>
                <el-button 
                  type="success" 
                  link 
                  size="small"
                  v-else
                >
                  <el-icon><RefreshRight /></el-icon>
                  重审
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
             v-if="row.status!=-4"
              :title="`确定要删除视频 ${row.title} 吗？此操作不可恢复！`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页组件 -->
    <div class="table-footer">
      <SinglePager
        v-if="pagination"
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import { VideoItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import {
    Calendar,
    ChatDotRound,
    Check,
    Delete,
    Edit,
    Lock,
    RefreshRight,
    Star,
    Unlock,
    View
} from '@element-plus/icons-vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Props定义
interface Props {
  loading: boolean,
  videoList: VideoItem[],
  pagination?: {
    page: number,
    pageSize: number,
    total: number
  }
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'edit': [row: VideoItem];
  'detail': [row: VideoItem];
  'view': [row: VideoItem];
  'delete': [row: VideoItem];
  'change-status': [id:string,status:number];
  'audit': [row: VideoItem];
  'current-change': [params: {page: number,pageSize: number}];
  'size-change': [size: number];
  'batch-status': [status: number, videos: VideoItem[]];
  'batch-delete': [videos: VideoItem[]];
  'selection-change': [selection: VideoItem[]];
  'batch-audit': [videos: VideoItem[]];
}

const emit = defineEmits<Emits>();

// 选中行数据
const selectedRows = ref<VideoItem[]>([]);

// 格式化视频时长
const formatDuration = (seconds: number = 0) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;

  return [
    h > 0 ? h.toString().padStart(2, '0') : '',
    m.toString().padStart(2, '0'),
    s.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
};

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};

// 处理选择变化
const handleSelectionChange = (selection: VideoItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

// 处理编辑
const handleEdit = (row: VideoItem) => {
  emit('edit', row);
};

// 处理查看
const handleView = (row: VideoItem) => {
  emit('detail', row);
};

// 处理审核
const handleAudit = (row: VideoItem) => {
  emit('audit', row);
};

// 处理删除
const handleDelete = (row: VideoItem) => {
  emit('delete', row);
};

// 处理状态变化
const handleChangeStatus = (id: string, status: number) => {
  emit('change-status', id, status);
};

// 处理分页变化
const handleCurrentChange = (page: number) => {
  emit('current-change', {
    page: page,
    pageSize: props.pagination?.pageSize || 10
  });
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 处理批量审核
const handleBatchAudit = () => {
  emit('batch-audit', selectedRows.value);
};

// 处理批量状态
const handleBatchStatus = (status: number) => {
  emit('batch-status', status, selectedRows.value);
};

// 清除选择
const clearSelection = () => {
  // 获取表格实例并清除选中项
  const table = document.querySelector('.el-table__body-wrapper table');
  if (table) {
    const checkboxes = table.querySelectorAll('.el-checkbox__input');
    checkboxes.forEach((checkbox: any) => {
      if (checkbox.classList.contains('is-checked')) {
        checkbox.click();
      }
    });
  }
  selectedRows.value = [];
};

const handleBatchDelete = () => {
  emit('batch-delete', selectedRows.value);
};
</script>

<style scoped lang="scss">
.video-table-container {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-weight: 500;
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-content {
    background: white;
    border-radius: 4px;
    overflow: hidden;
  }

  .video-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .video-thumbnail {
      border-radius: 4px;
      overflow: hidden;
      flex-shrink: 0;
    }

    .video-details {
      min-width: 0;
      flex: 1;

      .video-title {
        font-weight: 500;
        color: #333;
      }

      .video-description {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .stats-info {
    display: flex;
    gap: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .stat-icon {
        color: #999;
        font-size: 14px;
      }

      .stat-value {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .time-icon {
      color: #999;
      font-size: 14px;
    }

    .time-text {
      font-size: 12px;
      color: #666;
    }
  }

  .action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
  }

  .placeholder-text {
    color: #999;
    font-size: 12px;
  }

  .table-footer {
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .video-table-container {
    .action-buttons-group {
      flex-direction: column;
      gap: 4px;
      align-items: stretch;
    }
  }
}

@media (max-width: 768px) {
  .video-table-container {
    .batch-toolbar {
      flex-direction: column;
      gap: 12px;
      text-align: center;

      .batch-actions {
        justify-content: center;
      }
    }

    .table-footer {
      justify-content: center;
    }
  }
}
</style> 