package redis

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/go-redis/redis/v8"

	"frontapi/config"
)

// Init 初始化Redis，使用配置文件中的参数
func Init() error {
	log.Println("正在初始化Redis连接...")

	// 构建Redis连接配置
	redisAddr := fmt.Sprintf("%s:%d", config.AppConfig.Redis.Host, config.AppConfig.Redis.Port)

	// 初始化普通Redis客户端
	Client = redis.NewClient(&redis.Options{
		Addr:         redisAddr,
		Password:     config.AppConfig.Redis.Password,
		DB:           config.AppConfig.Redis.DB,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10, // 连接池大小
		MinIdleConns: 5,  // 最小空闲连接数
	})

	// 验证普通Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := Client.Ping(ctx).Err(); err != nil {
		log.Printf("普通Redis连接失败: %v", err)
		return err
	}

	log.Println("普通Redis连接成功!")

	// 初始化管理后台Redis连接
	if err := InitAdminRedis(); err != nil {
		log.Printf("管理后台Redis初始化失败: %v", err)
		return err
	}

	log.Println("管理后台Redis连接成功!")
	log.Println("Redis初始化完成!")
	return nil
}

// Close 优雅地关闭Redis连接
func Close() error {
	log.Println("关闭Redis连接...")

	var errors []error

	// 关闭普通Redis连接
	if Client != nil {
		if err := Client.Close(); err != nil {
			log.Printf("关闭普通Redis连接失败: %v", err)
			errors = append(errors, fmt.Errorf("关闭普通Redis连接失败: %v", err))
		} else {
			log.Println("普通Redis连接已关闭")
		}
	}

	// 关闭管理后台Redis连接
	if err := CloseAdminRedis(); err != nil {
		log.Printf("关闭管理后台Redis连接失败: %v", err)
		errors = append(errors, fmt.Errorf("关闭管理后台Redis连接失败: %v", err))
	} else {
		log.Println("管理后台Redis连接已关闭")
	}

	log.Println("Redis连接关闭完成")

	if len(errors) > 0 {
		return fmt.Errorf("关闭Redis连接时出现错误: %v", errors)
	}

	return nil
}

// Ping 测试Redis连接
func Ping() error {
	if Client == nil {
		return fmt.Errorf("Redis客户端未初始化")
	}

	err := Client.Ping(Ctx).Err()
	if err != nil {
		return fmt.Errorf("Redis连接测试失败: %v", err)
	}

	return nil
}

// Stats 获取Redis统计信息
func Stats() *redis.PoolStats {
	if Client == nil {
		return nil
	}

	return Client.PoolStats()
}
