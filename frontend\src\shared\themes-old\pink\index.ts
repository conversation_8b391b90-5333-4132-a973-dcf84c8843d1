/**
 * Charm 主题配置
 * 基于魅力粉色的主题
 */
import { ThemeConfig } from '../theme-manager';
import charmVariables from './variables';
import charmDarkVariables from './variables-dark';

// Charm 亮色主题
export const blueLightTheme: ThemeConfig = {
    name: 'blue-light',
    displayName: 'Blue Light',
    shortName: 'Blue',
    code: 'blue',
    primary: '#E91E63',
    isDark: false,
    variables: charmVariables
};

// Charm 暗色主题
export const charmDarkTheme: ThemeConfig = {
    name: 'blue-dark',
    displayName: 'Blue Dark',
    shortName: 'Blue',
    code: 'blue',
    primary: '#F06292',
    isDark: true,
    variables: charmDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default blueLightTheme;