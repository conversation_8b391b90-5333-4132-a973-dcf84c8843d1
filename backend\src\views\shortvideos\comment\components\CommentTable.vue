<template>
    <div class="comment-table-container">
        <!-- 批量操作工具栏 -->
        <div v-if="selectedRows.length > 0" class="batch-toolbar">
            <div class="batch-info">
                <el-icon>
                    <Check />
                </el-icon>
                <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
            </div>
            <div class="batch-actions">
                <el-button type="danger" size="small" @click="handleBatchDelete">
                    批量删除
                </el-button>
            </div>
        </div>

        <!-- 主表格 - 使用SlinkyTable -->
        <div class="table-content">
            <SlinkyTable ref="tableRef" :data="commentList" :loading="loading" row-key="id"
                @selection-change="handleSelectionChange" show-selection show-index index-label="序号"
                :empty-text="'暂无评论数据'" v-bind="$attrs" :default-sort="{ prop: 'created_at', order: 'descending' }"
                class="comment-data-table">
                <!-- 评论内容列 -->
                <el-table-column prop="content" label="评论内容" min-width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                        <div class="comment-content">{{ row.content }}</div>
                    </template>
                </el-table-column>

                <!-- 视频ID列 -->
                <el-table-column prop="video_id" label="视频ID" width="120" show-overflow-tooltip />

                <!-- 用户信息列 -->
                <el-table-column label="用户信息" width="150">
                    <template #default="{ row }">
                        <div class="user-info">
                            <el-avatar :size="24" :src="row.user_avatar || ''" class="user-avatar">
                                {{ (row.user_name || row.user_id || '')[0] }}
                            </el-avatar>
                            <span class="username">{{ row.user_name || row.user_id }}</span>
                        </div>
                    </template>
                </el-table-column>

                <!-- 状态列 -->
                <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                        <el-tag :type="row.status === 0 ? 'success' : 'danger'">
                            {{ row.status === 0 ? '正常' : '隐藏' }}
                        </el-tag>
                    </template>
                </el-table-column>

                <!-- 点赞数列 -->
                <el-table-column prop="likes" label="点赞数" width="80" align="center" />

                <!-- 回复数列 -->
                <el-table-column prop="reply_count" label="回复数" width="80" align="center">
                    <template #default="{ row }">
                        <el-button v-if="row.reply_count > 0" type="primary" link @click="handleViewReplies(row)">
                            {{ row.reply_count }}
                        </el-button>
                        <span v-else>{{ row.reply_count }}</span>
                    </template>
                </el-table-column>

                <!-- 创建时间列 -->
                <el-table-column prop="created_at" label="创建时间" width="160" align="center" sortable>
                    <template #default="{ row }">
                        {{ formatDateTime(row.created_at) }}
                    </template>
                </el-table-column>

                <!-- 操作列 -->
                <el-table-column label="操作" width="200" fixed="right" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" link size="small" @click="handleViewDetail(row)">
                            <el-icon>
                                <View />
                            </el-icon>
                            详情
                        </el-button>
                        <el-button :type="row.status === 0 ? 'warning' : 'success'" link size="small"
                            @click="handleToggleStatus(row)">
                            <el-icon>
                                <Hide v-if="row.status === 0" />
                                <View v-else />
                            </el-icon>
                            {{ row.status === 0 ? '隐藏' : '显示' }}
                        </el-button>
                        <el-popconfirm title="确定要删除该评论吗？此操作不可恢复！" @confirm="handleDelete(row)">
                            <template #reference>
                                <el-button type="danger" link size="small">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                    删除
                                </el-button>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>

                <!-- 自定义空状态插槽，覆盖el-table的默认空状态 -->
                <template #empty>
                    <div style="display: none;"></div>
                </template>
            </SlinkyTable>
        </div>

        <!-- 分页器 -->
        <div class="table-footer">
            <SinglePager :current-page="pagination.page" :page-size="pagination.pageSize" :total="pagination.total"
                @current-change="handleCurrentChange" @size-change="handleSizeChange" show-jump-info />
        </div>
    </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import { ShortVideoComment } from '@/types/shortvideos';
import {
    Check,
    Delete,
    Hide,
    View
} from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { ElMessageBox } from 'element-plus';
import { defineEmits, defineProps, ref } from 'vue';

// Props定义
interface Props {
    loading: boolean;
    commentList: ShortVideoComment[];
    pagination: {
        page: number;
        pageSize: number;
        total: number;
    };
}

const props = defineProps<Props>();

// Emits定义
const emit = defineEmits([
    'selection-change',
    'view-detail',
    'view-replies',
    'change-status',
    'delete',
    'current-change',
    'size-change',
    'batch-delete'
]);

// 表格引用
const tableRef = ref();

// 选中的行
const selectedRows = ref<ShortVideoComment[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: ShortVideoComment[]) => {
    selectedRows.value = selection;
    emit('selection-change', selection);
};

// 格式化时间
const formatDateTime = (datetime?: string) => {
    if (!datetime) return '';
    return dayjs(datetime).format('YYYY-MM-DD HH:mm:ss');
};

// 查看详情
const handleViewDetail = (row: ShortVideoComment) => {
    emit('view-detail', row);
};

// 查看回复
const handleViewReplies = (row: ShortVideoComment) => {
    emit('view-replies', row);
};

// 修改状态
const handleToggleStatus = (row: ShortVideoComment) => {
    // 状态取反：0->1, 1->0
    const newStatus = row.status === 0 ? 1 : 0;
    emit('change-status', row, newStatus);
};

// 删除评论
const handleDelete = (row: ShortVideoComment) => {
    emit('delete', row);
};

// 批量删除
const handleBatchDelete = () => {
    if (selectedRows.value.length === 0) return;

    ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.value.length} 条评论吗？此操作不可恢复！`,
        '警告',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        emit('batch-delete', selectedRows.value);
    });
};

// 页码变化
const handleCurrentChange = (page: number) => {
    emit('current-change', page);
};

// 每页条数变化
const handleSizeChange = (size: number) => {
    emit('size-change', size);
};
</script>

<style scoped lang="scss">
.comment-table-container {
    .batch-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f0f9ff;
        border: 1px solid #e1f5fe;
        border-radius: 4px;
        margin-bottom: 16px;

        .batch-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #1976d2;
            font-weight: 500;
        }

        .batch-actions {
            display: flex;
            gap: 8px;
        }
    }

    .table-content {
        background: white;
        border-radius: 4px;
        overflow: hidden;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .user-details {
            min-width: 0;
            flex: 1;

            .user-name {
                font-weight: 500;
                color: #333;
            }

            .user-id {
                font-size: 12px;
                color: #666;
                margin-top: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .comment-content {
        display: flex;
        flex-direction: column;
        word-break: break-all;
        white-space: pre-wrap;

        .reply-info {
            margin-top: 5px;
        }
    }

    .stats-info {
        display: flex;
        justify-content: center;
        gap: 16px;

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .stat-icon {
                color: #999;
                font-size: 14px;
            }

            .stat-value {
                font-size: 12px;
                color: #666;
            }
        }
    }

    .time-info {
        display: flex;
        align-items: center;
        gap: 4px;

        .time-icon {
            color: #999;
            font-size: 14px;
        }

        .time-text {
            font-size: 12px;
            color: #666;
        }
    }

    .action-buttons-group {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
    }

    .table-footer {
        padding: 16px 24px;
        background: var(--el-bg-color-page);
        border-top: 1px solid var(--el-border-color-lighter);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .comment-table-container {
        .action-buttons-group {
            flex-direction: column;
            gap: 4px;
            align-items: flex-start;
        }
    }
}
</style>
