package api

import (
	apiShortVideos "frontapi/internal/api/shortvideos"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterShortVideoRoutes 注册短视频相关路由
func RegisterShortVideoRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 创建短视频API控制器
	shortController := apiShortVideos.NewShortVideoController(
		services.ShortVideoService,
		services.ShortVideoCategoryService,
		services.ShortVideoCommentService,
	)

	commentController := apiShortVideos.NewShortVideoCommentController(
		services.ShortVideoCommentService,
		services.ShortVideoService,
	)
	categoryController := apiShortVideos.NewShortVideoCategoryController(
		services.ShortVideoCategoryService,
		services.ShortVideoService,
	)

	// 短视频路由组
	shortVideoGroup := apiGroup.Group("/shorts")
	{
		// 短视频基础接口
		shortVideoGroup.Post("/getShortVideoList", shortController.GetShortVideoList)
		shortVideoGroup.Post("/getShortVideoDetail", shortController.GetShortVideoDetail)
		shortVideoGroup.Post("/viewShortVideo", shortController.ViewShortVideo)
		shortVideoGroup.Post("/searchShortVideos", shortController.SearchShortVideos)
		shortVideoGroup.Post("/getTrendingShortVideos", shortController.GetTrendingShortVideos)
		shortVideoGroup.Post("/getRelatedShortVideos", shortController.GetRelatedShortVideos)

		// 短视频互动接口
		shortVideoGroup.Post("/likeShortVideo", shortController.LikeShortVideo)
		shortVideoGroup.Post("/cancelLikeShortVideo", shortController.CancelLikeShortVideo)
		shortVideoGroup.Post("/checkUserLiked", shortController.CheckUserLiked)

		// 短视频收藏接口
		shortVideoGroup.Post("/collectShortVideo", shortController.CollectShortVideo)
		shortVideoGroup.Post("/cancelCollectShortVideo", shortController.CancelCollectShortVideo)
		shortVideoGroup.Post("/checkUserCollected", shortController.CheckUserCollected)
		shortVideoGroup.Post("/getUserCollections", shortController.GetUserCollections)

		// 短视频评论接口
		shortVideoGroup.Post("/getShortVideoComments", commentController.GetShortVideoComments)
		shortVideoGroup.Post("/addShortVideoComment", commentController.AddShortVideoComment)

		// 短视频评论点赞接口
		shortVideoGroup.Post("/likeShortVideoComment", commentController.LikeShortVideoComment)
		shortVideoGroup.Post("/cancelLikeShortVideoComment", commentController.CancelLikeShortVideoComment)
		shortVideoGroup.Post("/checkUserLikedShortVideoComment", commentController.CheckUserLikedShortVideoComment)

		// 短视频分类接口
		shortVideoGroup.Post("/getShortVideoCategories", categoryController.GetShortVideoCategories)
	}

}
