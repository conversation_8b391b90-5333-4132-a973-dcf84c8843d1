<template>
  <component 
    :is="MenuComponent" 
    v-bind="$attrs"
    @item-click="$emit('item-click', $event)"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import HorizontalMenu from './HorizontalMenu.vue';
import VerticalMenu from './VerticalMenu.vue';

// Props
interface Props {
  orientation?: 'horizontal' | 'vertical';
}

const props = withDefaults(defineProps<Props>(), {
  orientation: 'vertical'
});

// Emits
interface Emits {
  'item-click': [item: any];
}

const emit = defineEmits<Emits>();

// 根据 orientation 选择对应的组件
const MenuComponent = computed(() => {
  return props.orientation === 'horizontal' ? HorizontalMenu : VerticalMenu;
});
</script>

<style scoped>
/* 统一菜单组件样式 */
</style>
