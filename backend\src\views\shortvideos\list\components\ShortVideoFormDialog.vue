<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @closed="handleDialogClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="right"
      size="default"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入短视频标题" maxlength="100" show-word-limit />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入短视频描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="视频URL" prop="url">
        <UrlOrFileInput
          v-model="formData.url"
          fileType="video"
          subDir="videos"
          placeholder="请上传视频或输入视频URL"
        />
        <div class="form-tip">支持MP4、WebM格式，建议使用H.264编码</div>
      </el-form-item>

      <el-form-item label="时长(秒)" prop="duration">
        <el-input-number v-model="formData.duration" :min="0" :precision="0" :step="1" style="width: 100%" />
      </el-form-item>

      <el-form-item label="分类" prop="category_id">
        <el-select
          v-model="formData.category_id"
          filterable
          remote
          reserve-keyword
          placeholder="请选择分类"
          :remote-method="handleSearchCategories"
          :loading="categoriesLoading"
          clearable
          style="width: 100%"
          @focus="loadCategoriesIfEmpty"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="创作者" prop="creator_id">
        <el-select
          v-model="formData.creator_id"
          filterable
          remote
          reserve-keyword
          placeholder="请搜索创作者"
          :remote-method="handleSearchCreators"
          :loading="creatorsLoading"
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="creator in creatorsOptions"
            :key="creator.id"
            :label="creator.nickname || creator.username"
            :value="creator.id"
          >
            <div style="display: flex; align-items: center;">
              <el-avatar :size="20" :src="creator.avatar" style="margin-right: 6px;">
                {{ (creator.nickname || creator.username).charAt(0) }}
              </el-avatar>
              <span>{{ creator.nickname || creator.username }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入标签，回车确认"
          style="width: 100%"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
          <el-option label="待审核" :value="0" />
          <el-option label="已下架" :value="1" />
          <el-option label="已发布" :value="2" />
          <el-option label="已拒绝" :value="-2" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 封面预览 -->
  <el-dialog v-model="previewVisible" title="封面预览" width="500px" append-to-body center>
    <div class="cover-preview">
      <el-image
        :src="formData.cover_url"
        fit="contain"
        style="width: 100%"
      >
        <template #error>
          <div class="image-error">
            <el-icon><Picture /></el-icon>
            <span>图片加载失败</span>
          </div>
        </template>
      </el-image>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import { createShortVideo, getCategoryList, updateShortVideo } from '@/service/api/shortvideos/shortvideos';
import { searchUsers } from '@/service/api/videos/videos';
import type { CreateShortVideoRequest, ShortVideo, UpdateShortVideoRequest } from '@/types/shortvideos';
import { Picture } from '@element-plus/icons-vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { computed, onMounted, reactive, ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String as () => 'add' | 'edit',
    default: 'add'
  },
  shortVideoData: {
    type: Object as () => ShortVideo | null,
    default: null
  }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref<FormInstance>();

// 对话框标题
const dialogTitle = computed(() => props.type === 'add' ? '添加短视频' : '编辑短视频');

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  description: '',
  url: '',
  duration: 0,
  width: 0, // 保留字段，后端需要
  height: 0, // 保留字段，后端需要
  category_id: '',
  category_name: '',
  creator_id: '',
  creator_name: '',
  creator_avatar: '',
  tags: [] as string[],
  status: 0,
  is_featured: 0
});

// 表单验证规则
const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { max: 100, message: '标题不能超过100个字符', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请上传或输入视频URL', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入视频时长', trigger: 'blur' }
  ]
});

// 分类选项
const categoryOptions = ref<{ value: string; label: string }[]>([]);
const categoriesLoading = ref(false);

// 创作者选项
const creatorsOptions = ref<any[]>([]);
const creatorsLoading = ref(false);

// 标签选项
const tagOptions = ref<{ value: string; label: string }[]>([]);

// 提交状态
const submitting = ref(false);

// 封面预览
const previewVisible = ref(false);

// 监听shortVideoData变化
watch(() => props.shortVideoData, (newVal) => {
  if (newVal) {
    // 填充表单数据
    formData.id = newVal.id || '';
    formData.title = newVal.title || '';
    formData.description = newVal.description || '';
    formData.url = newVal.url || '';
    formData.duration = newVal.duration || 0;
    formData.width = newVal.width || 0;
    formData.height = newVal.height || 0;
    formData.category_id = newVal.category_id || '';
    formData.category_name = newVal.category_name || '';
    formData.creator_id = newVal.creator_id || '';
    formData.creator_name = newVal.creator_name || '';
    formData.creator_avatar = newVal.creator_avatar || '';
    formData.tags = newVal.tags || [];
    formData.status = newVal.status ?? 0;
  }
}, { immediate: true });

// 组件挂载时获取分类列表
onMounted(() => {
  loadCategoriesIfEmpty();
});

// 如果分类列表为空，则加载分类列表
const loadCategoriesIfEmpty = async () => {
  if (categoryOptions.value.length === 0) {
    categoriesLoading.value = true;
    try {
      const res = await getCategoryList({ 
        page: { 
          pageNo: 1, 
          pageSize: 100 
        } 
      });
      if (res.data) {
        categoryOptions.value = res.data.list.map((item: any) => ({
          value: item.id,
          label: item.name
        }));
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
    } finally {
      categoriesLoading.value = false;
    }
  }
};

// 搜索分类
const handleSearchCategories = async (query: string) => {
  if (query) {
    categoriesLoading.value = true;
    try {
      const res = await getCategoryList({ 
        page: { 
          pageNo: 1, 
          pageSize: 100 
        },
        data: { 
          keyword: query 
        }
      });
      if (res.data) {
        categoryOptions.value = res.data.list.map((item: any) => ({
          value: item.id,
          label: item.name
        }));
        
        // 如果选中了分类，保存分类名称
        if (formData.category_id) {
          const selectedCategory = res.data.list.find((item: any) => item.id === formData.category_id);
          if (selectedCategory) {
            formData.category_name = selectedCategory.name;
          }
        }
      }
    } catch (error) {
      console.error('搜索分类失败:', error);
    } finally {
      categoriesLoading.value = false;
    }
  } else {
    loadCategoriesIfEmpty();
  }
};

// 搜索创作者
const handleSearchCreators = async (query: string) => {
  if (query) {
    creatorsLoading.value = true;
    try {
      const res = await searchUsers({ 
        page: { 
          pageNo: 1, 
          pageSize: 20 
        },
        data: { 
          keyword: query 
        }
      });
      if (res.data) {
        creatorsOptions.value = res.data.list || [];
        
        // 如果选中了创作者，保存创作者信息
        if (formData.creator_id) {
          const selectedCreator = creatorsOptions.value.find(item => item.id === formData.creator_id);
          if (selectedCreator) {
            formData.creator_name = selectedCreator.nickname || selectedCreator.username;
            formData.creator_avatar = selectedCreator.avatar || '';
          }
        }
      }
    } catch (error) {
      console.error('搜索创作者失败:', error);
    } finally {
      creatorsLoading.value = false;
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid, fields) => {
    if (!valid) {
      console.error('表单验证失败:', fields);
      return;
    }
    
    submitting.value = true;
    
    try {
      // 确保分类名称和创作者信息已设置
      if (formData.category_id && !formData.category_name) {
        const category = categoryOptions.value.find(item => item.value === formData.category_id);
        if (category) {
          formData.category_name = category.label;
        }
      }
      
      if (formData.creator_id && !formData.creator_name) {
        const creator = creatorsOptions.value.find(item => item.id === formData.creator_id);
        if (creator) {
          formData.creator_name = creator.nickname || creator.username;
          formData.creator_avatar = creator.avatar || '';
        }
      }
      
      // 构建提交数据
      const submitData = props.type === 'add' 
        ? formData as CreateShortVideoRequest
        : formData as UpdateShortVideoRequest;
      
      if (props.type === 'add') {
        await createShortVideo(submitData);
        ElMessage.success('添加短视频成功');
      } else {
        await updateShortVideo(submitData);
        ElMessage.success('更新短视频成功');
      }
      
      dialogVisible.value = false;
      emit('success');
    } catch (error: any) {
      console.error('提交失败:', error);
      ElMessage.error(`提交失败: ${error.message || '未知错误'}`);
    } finally {
      submitting.value = false;
    }
  });
};

// 对话框关闭时重置表单
const handleDialogClosed = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  // 重置表单数据
  formData.id = '';
  formData.title = '';
  formData.description = '';
  formData.cover = '';
  formData.url = '';
  formData.duration = 0;
  formData.width = 0;
  formData.height = 0;
  formData.category_id = '';
  formData.category_name = '';
  formData.creator_id = '';
  formData.creator_name = '';
  formData.creator_avatar = '';
  formData.tags = [];
  formData.status = 0;
  formData.is_featured = 0;
};
</script>

<style scoped lang="scss">
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.cover-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #909399;
  
  .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
}
</style> 