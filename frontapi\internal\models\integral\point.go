package integral

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type Point struct {
	models.BaseModelStruct
	UserID      string         `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	Amount      int            `json:"amount" gorm:"not null;comment:积分数量"`
	Balance     int            `json:"balance" gorm:"not null;comment:积分余额"`
	Type        string         `json:"type" gorm:"type:string;size:50;not null;comment:类型：comment-评论，post-发帖，login-登录，register-注册，invite-邀请，exchange-兑换，other-其他"`
	RuleID      string         `json:"rule_id" gorm:"type:string;size:36;comment:积分规则ID"`
	ContentType string         `json:"content_type" gorm:"type:string;size:20;comment:关联内容类型"`
	ContentID   string         `json:"content_id" gorm:"type:string;size:36;comment:关联内容ID"`
	Description string         `json:"description" gorm:"type:string;size:255;comment:描述"`
	IP          string         `json:"ip" gorm:"type:string;size:50;comment:操作IP"`
	Device      string         `json:"device" gorm:"type:string;size:255;comment:设备信息"`
	ExpiredAt   types.JSONTime `json:"expired_at" gorm:"comment:过期时间"`
	IsExpired   int8           `json:"is_expired" gorm:"not null;default:0;comment:是否已过期"`
}

func (Point) TableName() string {
	return "ly_points"
}
