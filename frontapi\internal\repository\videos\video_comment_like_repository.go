package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// VideoCommentLikeRepository 视频评论点赞数据访问接口
type VideoCommentLikeRepository interface {
	base.ExtendedRepository[videos.VideoCommentLike]
	FindByUserIDAndCommentID(ctx context.Context, userID, commentID string) (*videos.VideoCommentLike, error)
}

// videoCommentLikeRepository 视频评论点赞数据访问实现
type videoCommentLikeRepository struct {
	base.ExtendedRepository[videos.VideoCommentLike]
}

// NewVideoCommentLikeRepository 创建视频评论点赞仓库实例
func NewVideoCommentLikeRepository(db *gorm.DB) VideoCommentLikeRepository {
	return &videoCommentLikeRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoCommentLike](db),
	}
}

// FindByUserIDAndCommentID 根据用户ID和评论ID查找点赞记录
func (r *videoCommentLikeRepository) FindByUserIDAndCommentID(ctx context.Context, userID, commentID string) (*videos.VideoCommentLike, error) {
	return r.FindOneByCondition(ctx, map[string]interface{}{
		"user_id":    userID,
		"comment_id": commentID,
	}, "")
}
