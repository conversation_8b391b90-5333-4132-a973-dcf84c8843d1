package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// VideoCategoryRepository 视频分类数据访问接口
type VideoCategoryRepository interface {
	base.ExtendedRepository[videos.VideoCategory]
	// 业务特定方法
	FindByCode(ctx context.Context, code string) (*videos.VideoCategory, error)
	ListByParentID(ctx context.Context, parentID string) ([]*videos.VideoCategory, error)
}

// videoCategoryRepository 视频分类数据访问实现
type videoCategoryRepository struct {
	base.ExtendedRepository[videos.VideoCategory]
}

// NewVideoCategoryRepository 创建视频分类仓库实例
func NewVideoCategoryRepository(db *gorm.DB) VideoCategoryRepository {
	return &videoCategoryRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoCategory](db),
	}
}

// FindByCode 根据编码查找视频分类
func (r *videoCategoryRepository) FindByCode(ctx context.Context, code string) (*videos.VideoCategory, error) {
	condition := map[string]interface{}{"code": code}
	return r.FindOneByCondition(ctx, condition, "")
}

// ListByParentID 获取指定父分类的子分类
func (r *videoCategoryRepository) ListByParentID(ctx context.Context, parentID string) ([]*videos.VideoCategory, error) {
	condition := map[string]interface{}{"parent_id": parentID}
	return r.FindAll(ctx, condition, "sort_order ASC")
}
