package books

// CreateCategoryRequest 创建书籍分类请求验证模型
type CreateCategoryRequest struct {
	Name        string `json:"name" validate:"required"`
	Code        string `json:"code" validate:"required|minLen:3|maxLen:50"`
	Description string `json:"description"`
	SortOrder   int    `json:"sort_order"`
	Status      int    `json:"status"`
}

// UpdateCategoryRequest 更新书籍分类请求验证模型
type UpdateCategoryRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
	SortOrder   *int   `json:"sort_order"`
	Status      *int   `json:"status"`
}

// UpdateCategoryStatusRequest 更新书籍分类状态请求验证模型
type UpdateCategoryStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status" validate:"required|min:-1|max:3"`
}
