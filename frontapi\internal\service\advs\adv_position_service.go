package advs

import (
	"context"
	"fmt"
	"frontapi/internal/models"
	"frontapi/internal/models/advs"
	repo "frontapi/internal/repository/advs"
	advValidate "frontapi/internal/validation/advs"
	"frontapi/pkg/types"
	"time"

	"frontapi/internal/service/base"
)

// AdvPositionService 广告位服务接口
type AdvPositionService interface {
	CreateAdvPosition(ctx context.Context, req *advValidate.CreateAdvPositionRequest) (string, error)
	GetAdvPosition(ctx context.Context, id string) (*advs.AdvPosition, error)
	UpdateAdvPosition(ctx context.Context, id string, req *advValidate.UpdateAdvPositionRequest) error
	DeleteAdvPosition(ctx context.Context, id string) error
	ListAdvPositions(ctx context.Context, page, pageSize int) ([]*advs.AdvPosition, int64, error)
}

// advPositionService 广告位服务实现
type advPositionService struct {
	*base.BaseService[advs.AdvPosition]
	repo repo.AdvPositionRepository // 保留repo用于特殊查询方法
}

// NewAdvPositionService 创建广告位服务实例
func NewAdvPositionService(repo repo.AdvPositionRepository) AdvPositionService {
	return &advPositionService{
		BaseService: base.NewBaseService[advs.AdvPosition](repo, "adv_position"),
		repo:        repo,
	}
}

// CreateAdvPosition 创建广告位
func (s *advPositionService) CreateAdvPosition(ctx context.Context, req *advValidate.CreateAdvPositionRequest) (string, error) {
	// 检查广告位编码是否已存在
	existing, err := s.repo.FindByCode(ctx, req.Code)
	if err != nil {
		return "", err
	}
	if existing != nil {
		return "", fmt.Errorf("广告位编码已存在")
	}

	pos := &advs.AdvPosition{
		BaseModelStruct: &models.BaseModelStruct{
			Status: req.Status,
		},
		Name:        req.Name,
		Code:        req.Code,
		Description: req.Description,
		Width:       req.Width,
		Height:      req.Height,
	}
	return s.BaseService.Create(ctx, pos)
}

// GetAdvPosition 获取广告位详情
func (s *advPositionService) GetAdvPosition(ctx context.Context, id string) (*advs.AdvPosition, error) {
	return s.BaseService.GetByID(ctx, id, false)
}

// UpdateAdvPosition 更新广告位
func (s *advPositionService) UpdateAdvPosition(ctx context.Context, id string, req *advValidate.UpdateAdvPositionRequest) error {
	pos, err := s.BaseService.GetByID(ctx, id, false)
	if err != nil {
		return err
	}
	if pos == nil {
		return fmt.Errorf("广告位不存在")
	}
	if req.Name != "" {
		pos.Name = req.Name
	}
	if req.Code != "" {
		pos.Code = req.Code
	}
	if req.Description != "" {
		pos.Description = req.Description
	}
	if req.Width != 0 {
		pos.Width = req.Width
	}
	if req.Height != 0 {
		pos.Height = req.Height
	}
	if req.Status != 0 {
		pos.Status = req.Status
	}
	pos.UpdatedAt = types.JSONTime(time.Now())
	return s.BaseService.Update(ctx, pos)
}

// DeleteAdvPosition 删除广告位
func (s *advPositionService) DeleteAdvPosition(ctx context.Context, id string) error {
	return s.BaseService.Delete(ctx, id)
}

// ListAdvPositions 获取广告位列表
func (s *advPositionService) ListAdvPositions(ctx context.Context, page, pageSize int) ([]*advs.AdvPosition, int64, error) {
	return s.BaseService.List(ctx, map[string]interface{}{}, "", page, pageSize, false)
}
