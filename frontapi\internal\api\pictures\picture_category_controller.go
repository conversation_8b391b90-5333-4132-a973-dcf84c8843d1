package pictures

import (
	"fmt"
	"frontapi/internal/api"
	"frontapi/internal/service/pictures"
	pictureTypings "frontapi/internal/typings/picture"
	"frontapi/pkg/cache"
	"frontapi/pkg/utils"
	"time"

	"github.com/gofiber/fiber/v2"
)

// PictureCategoryController 图片分类控制器
type PictureCategoryController struct {
	api.BaseController
	categoryService pictures.PictureCategoryService
	pictureService  pictures.PictureService
	albumService    pictures.PictureAlbumService
}

// NewPictureCategoryController 创建图片分类控制器
func NewPictureCategoryController(
	categoryService pictures.PictureCategoryService,
	pictureService pictures.PictureService,
	albumService pictures.PictureAlbumService,
) *PictureCategoryController {
	return &PictureCategoryController{
		categoryService: categoryService,
		pictureService:  pictureService,
		albumService:    albumService,
	}
}

func (c *PictureCategoryController) GetCategoryList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()

	categoryIdsBytes, err := cache.GetAdapter("redis").Get(ctx.Context(), "picture_album_active_id")

	categoryIds := []string{}
	if categoryIdsBytes != nil {
		err = utils.Unmarshal(categoryIdsBytes, &categoryIds)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
	}
	fmt.Println("categoryIds", categoryIds, err)
	if err != nil || len(categoryIds) == 0 {
		// 如果Redis获取失败，尝试从数据库中获取并缓存
		albums, _ := c.albumService.FindAll(ctx.Context(), map[string]interface{}{"status": 1, "group_by": "category_id"}, "sort_order DESC,created_at DESC", false)
		categoryIds = make([]string, 0, len(albums))
		for _, album := range albums {
			if album.CategoryID.Valid {
				categoryIds = append(categoryIds, album.CategoryID.String)
			}
		}
		categoryIdsBytes, err := utils.Marshal(categoryIds)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		if err := cache.GetAdapter("redis").Set(ctx.Context(), "picture_album_active_id", categoryIdsBytes, time.Hour*24); err != nil {
			fmt.Printf("缓存管理器缓存专辑ID列表失败: %v\n", err)
		}
	}

	condition := map[string]interface{}{
		"status":  2,
		"keyword": keyword,
		"id":      categoryIds,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "sort_order DESC,created_at DESC"
	}
	categories, total, err := c.categoryService.GetCategoryList(ctx.Context(), condition, orderBy, pageNo, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := pictureTypings.ConvertPictureCategoryListResponse(categories, total, pageNo, pageSize)
	return c.Success(ctx, response)
}
