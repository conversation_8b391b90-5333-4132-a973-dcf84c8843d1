package users

import (
	"frontapi/internal/models"
)

type UserFollows struct {
	models.BaseModelStruct
	UserID           string `gorm:"column:user_id;type:string;not null;index;comment:用户ID" json:"user_id"`                 //用户ID
	UserNickname     string `gorm:"column:user_nickname;type:string;size:50;comment:用户昵称" json:"user_nickname"`            //用户昵称
	UserAvatar       string `gorm:"column:user_avatar;type:string;size:255;comment:用户头像" json:"user_avatar"`               //用户头像
	FollowedID       string `gorm:"column:followed_id;type:string;not null;index;comment:被关注者ID" json:"followed_id"`       //被关注者ID
	FollowedUsername string `gorm:"column:followed_username;type:string;size:50;comment:被关注用户名" json:"followed_username"`  //被关注用户名
	FollowedNickname string `gorm:"column:followed_nickname;type:string;size:50;comment:被关注用户昵称" json:"followed_nickname"` //被关注用户昵称
	FollowedAvatar   string `gorm:"column:followed_avatar;type:string;size:255;comment:被关注用户头像" json:"followed_avatar"`    //被关注用户头像
	FollowedBio      string `gorm:"column:followed_bio;type:text;comment:被关注用户简介" json:"followed_bio"`                     //被关注用户简介
	IsMutual         bool   `gorm:"column:is_mutual;type:bool;default:false;comment:是否互相关注" json:"is_mutual"`              //是否互相关注
	GroupName        string `gorm:"column:group_name;type:string;size:50;comment:关注分组" json:"group_name"`                  //关注分组
	RemarkName       string `gorm:"column:remark_name;type:string;size:50;comment:备注名称" json:"remark_name"`                //备注名称
}

func (UserFollows) TableName() string {
	return "ly_user_follows"
}

func (UserFollows) GetStatusField() string {
	return "status"
}

// 粉丝增长的model
type FollowGrowth struct {
	Time  string `json:"time"`
	Count int64  `json:"count"`
}

// FollowersGrowthStat 粉丝增长统计数据结构
type FollowersGrowthStat struct {
	TimePeriod string `json:"time_period" gorm:"column:time_period"` // 时间周期
	Count      int64  `json:"count" gorm:"column:count"`             // 粉丝数量
}

// FollowersStatsRequest 粉丝统计请求参数
type FollowersStatsRequest struct {
	UserID    string `json:"user_id" validate:"required"`                             // 用户ID
	TimeType  string `json:"time_type" validate:"required|in:day,week,month,quarter"` // 时间类型：day-日，week-周，month-月，quarter-季度
	StartTime string `json:"start_time" validate:"required"`                          // 开始时间 格式：2024-01-01
	EndTime   string `json:"end_time" validate:"required"`                            // 结束时间 格式：2024-12-31
}
