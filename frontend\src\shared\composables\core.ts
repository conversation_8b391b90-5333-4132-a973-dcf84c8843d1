/**
 * 核心组合式函数
 */

import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import type { Ref, ComputedRef, WatchStopHandle } from 'vue'
import type { MaybeRef, MaybeRefOrGetter, Nullable } from './types'
import { toValue, isFunction, debounce, throttle } from './utils'

/**
 * 响应式状态管理
 */
export function useReactiveState<T>(initialValue: T) {
  const state = ref(initialValue)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)
  
  const setState = (newState: T | ((prev: T) => T)) => {
    if (isFunction(newState)) {
      state.value = newState(state.value)
    } else {
      state.value = newState
    }
  }
  
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }
  
  const setError = (err: Error | null) => {
    error.value = err
  }
  
  const reset = () => {
    state.value = initialValue
    isLoading.value = false
    error.value = null
  }
  
  return {
    state: state as Ref<T>,
    isLoading: isLoading as Ref<boolean>,
    error: error as Ref<Error | null>,
    setState,
    setLoading,
    setError,
    reset
  }
}

/**
 * 异步操作管理
 */
export function useAsyncOperation<T = any, P extends any[] = any[]>(
  asyncFn: (...args: P) => Promise<T>,
  options: {
    immediate?: boolean
    initialData?: T
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
    resetOnExecute?: boolean
  } = {}
) {
  const {
    immediate = false,
    initialData,
    onSuccess,
    onError,
    resetOnExecute = true
  } = options
  
  const data = ref<T | undefined>(initialData)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)
  const isFinished = ref(false)
  
  const execute = async (...args: P): Promise<T | undefined> => {
    if (resetOnExecute) {
      data.value = undefined
      error.value = null
    }
    
    isLoading.value = true
    isFinished.value = false
    
    try {
      const result = await asyncFn(...args)
      data.value = result
      onSuccess?.(result)
      return result
    } catch (err) {
      const errorObj = err instanceof Error ? err : new Error(String(err))
      error.value = errorObj
      onError?.(errorObj)
      throw errorObj
    } finally {
      isLoading.value = false
      isFinished.value = true
    }
  }
  
  const reset = () => {
    data.value = initialData
    isLoading.value = false
    error.value = null
    isFinished.value = false
  }
  
  if (immediate) {
    execute()
  }
  
  return {
    data: data as Ref<T | undefined>,
    isLoading: isLoading as Ref<boolean>,
    error: error as Ref<Error | null>,
    isFinished: isFinished as Ref<boolean>,
    execute,
    reset
  }
}

/**
 * 计数器
 */
export function useCounter(
  initialValue = 0,
  options: {
    min?: number
    max?: number
    step?: number
  } = {}
) {
  const { min = -Infinity, max = Infinity, step = 1 } = options
  
  const count = ref(initialValue)
  
  const increment = (delta = step) => {
    count.value = Math.min(max, count.value + delta)
  }
  
  const decrement = (delta = step) => {
    count.value = Math.max(min, count.value - delta)
  }
  
  const set = (value: number) => {
    count.value = Math.max(min, Math.min(max, value))
  }
  
  const reset = () => {
    count.value = initialValue
  }
  
  return {
    count: count as Ref<number>,
    increment,
    decrement,
    set,
    reset
  }
}

/**
 * 切换状态
 */
export function useToggle(
  initialValue = false
): [Ref<boolean>, (value?: boolean) => boolean] {
  const state = ref(initialValue)
  
  const toggle = (value?: boolean) => {
    state.value = typeof value === 'boolean' ? value : !state.value
    return state.value
  }
  
  return [state, toggle]
}

/**
 * 布尔状态管理
 */
export function useBoolean(initialValue = false) {
  const [state, toggle] = useToggle(initialValue)
  
  const setTrue = () => toggle(true)
  const setFalse = () => toggle(false)
  
  return {
    value: state,
    toggle,
    setTrue,
    setFalse
  }
}

/**
 * 防抖
 */
export function useDebounce<T>(
  value: MaybeRefOrGetter<T>,
  delay: MaybeRefOrGetter<number> = 300
): Ref<T> {
  const debouncedValue = ref(toValue(value))
  
  watch(
    () => toValue(value),
    debounce((newValue: T) => {
      debouncedValue.value = newValue
    }, toValue(delay)),
    { immediate: true }
  )
  
  return debouncedValue as Ref<T>
}

/**
 * 防抖函数
 */
export function useDebounceFn<T extends (...args: any[]) => any>(
  fn: T,
  delay: MaybeRefOrGetter<number> = 300,
  options: {
    immediate?: boolean
  } = {}
) {
  const { immediate = false } = options
  
  return debounce(fn, toValue(delay), immediate)
}

/**
 * 节流
 */
export function useThrottle<T>(
  value: MaybeRefOrGetter<T>,
  delay: MaybeRefOrGetter<number> = 100
): Ref<T> {
  const throttledValue = ref(toValue(value))
  
  watch(
    () => toValue(value),
    throttle((newValue: T) => {
      throttledValue.value = newValue
    }, toValue(delay)),
    { immediate: true }
  )
  
  return throttledValue as Ref<T>
}

/**
 * 节流函数
 */
export function useThrottleFn<T extends (...args: any[]) => any>(
  fn: T,
  delay: MaybeRefOrGetter<number> = 100,
  options: {
    leading?: boolean
    trailing?: boolean
  } = {}
) {
  const { leading = true, trailing = true } = options
  
  return throttle(fn, toValue(delay), leading, trailing)
}

/**
 * 间隔执行
 */
export function useInterval(
  callback: () => void,
  delay: MaybeRefOrGetter<Nullable<number>>,
  options: {
    immediate?: boolean
    immediateCallback?: boolean
  } = {}
) {
  const { immediate = true, immediateCallback = false } = options
  
  const isActive = ref(false)
  let timer: NodeJS.Timeout | null = null
  
  const clean = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }
  
  const pause = () => {
    isActive.value = false
    clean()
  }
  
  const resume = () => {
    const delayValue = toValue(delay)
    if (delayValue == null || delayValue <= 0) return
    
    isActive.value = true
    
    if (immediateCallback) {
      callback()
    }
    
    clean()
    timer = setInterval(callback, delayValue)
  }
  
  if (immediate) {
    resume()
  }
  
  onUnmounted(pause)
  
  return {
    isActive: isActive as Ref<boolean>,
    pause,
    resume
  }
}

/**
 * 超时执行
 */
export function useTimeout(
  callback: () => void,
  delay: MaybeRefOrGetter<number>,
  options: {
    immediate?: boolean
  } = {}
) {
  const { immediate = true } = options
  
  const isPending = ref(false)
  let timer: NodeJS.Timeout | null = null
  
  const clear = () => {
    if (timer) {
      clearTimeout(timer)
      timer = null
      isPending.value = false
    }
  }
  
  const start = () => {
    clear()
    isPending.value = true
    timer = setTimeout(() => {
      isPending.value = false
      timer = null
      callback()
    }, toValue(delay))
  }
  
  if (immediate) {
    start()
  }
  
  onUnmounted(clear)
  
  return {
    isPending: isPending as Ref<boolean>,
    start,
    clear
  }
}

/**
 * 时间戳
 */
export function useTimestamp(
  options: {
    immediate?: boolean
    interval?: number
    callback?: (timestamp: number) => void
  } = {}
) {
  const { immediate = true, interval = 1000, callback } = options
  
  const timestamp = ref(Date.now())
  
  const update = () => {
    timestamp.value = Date.now()
    callback?.(timestamp.value)
  }
  
  const { pause, resume } = useInterval(update, interval, { immediate })
  
  return {
    timestamp: timestamp as Ref<number>,
    pause,
    resume
  }
}

/**
 * 上一个值
 */
export function usePrevious<T>(
  value: MaybeRefOrGetter<T>
): Ref<T | undefined> {
  const previous = ref<T | undefined>()
  
  watch(
    () => toValue(value),
    (_, oldValue) => {
      previous.value = oldValue
    },
    { flush: 'sync' }
  )
  
  return previous
}

/**
 * 数组管理
 */
export function useArray<T>(initialValue: T[] = []) {
  const array = ref([...initialValue])
  
  const push = (...items: T[]) => {
    array.value.push(...items)
  }
  
  const pop = () => {
    return array.value.pop()
  }
  
  const shift = () => {
    return array.value.shift()
  }
  
  const unshift = (...items: T[]) => {
    array.value.unshift(...items)
  }
  
  const splice = (start: number, deleteCount?: number, ...items: T[]) => {
    return array.value.splice(start, deleteCount ?? 0, ...items)
  }
  
  const remove = (index: number) => {
    return array.value.splice(index, 1)[0]
  }
  
  const removeWhere = (predicate: (item: T, index: number) => boolean) => {
    const index = array.value.findIndex(predicate)
    if (index > -1) {
      return remove(index)
    }
  }
  
  const clear = () => {
    array.value.length = 0
  }
  
  const reset = () => {
    array.value = [...initialValue]
  }
  
  const filter = (predicate: (item: T, index: number) => boolean) => {
    array.value = array.value.filter(predicate)
  }
  
  const map = <U>(mapper: (item: T, index: number) => U) => {
    return array.value.map(mapper)
  }
  
  const find = (predicate: (item: T, index: number) => boolean) => {
    return array.value.find(predicate)
  }
  
  const findIndex = (predicate: (item: T, index: number) => boolean) => {
    return array.value.findIndex(predicate)
  }
  
  const includes = (item: T) => {
    return array.value.includes(item)
  }
  
  const indexOf = (item: T) => {
    return array.value.indexOf(item)
  }
  
  const sort = (compareFn?: (a: T, b: T) => number) => {
    array.value.sort(compareFn)
  }
  
  const reverse = () => {
    array.value.reverse()
  }
  
  return {
    array: array as Ref<T[]>,
    push,
    pop,
    shift,
    unshift,
    splice,
    remove,
    removeWhere,
    clear,
    reset,
    filter,
    map,
    find,
    findIndex,
    includes,
    indexOf,
    sort,
    reverse,
    // 计算属性
    length: computed(() => array.value.length),
    isEmpty: computed(() => array.value.length === 0),
    first: computed(() => array.value[0]),
    last: computed(() => array.value[array.value.length - 1])
  }
}

/**
 * 对象管理
 */
export function useObject<T extends Record<string, any>>(initialValue: T) {
  const object = ref({ ...initialValue })
  
  const set = <K extends keyof T>(key: K, value: T[K]) => {
    object.value[key] = value
  }
  
  const get = <K extends keyof T>(key: K): T[K] => {
    return object.value[key]
  }
  
  const has = (key: keyof T) => {
    return key in object.value
  }
  
  const remove = (key: keyof T) => {
    const value = object.value[key]
    delete object.value[key]
    return value
  }
  
  const clear = () => {
    for (const key in object.value) {
      delete object.value[key]
    }
  }
  
  const reset = () => {
    object.value = { ...initialValue }
  }
  
  const merge = (source: Partial<T>) => {
    Object.assign(object.value, source)
  }
  
  const clone = () => {
    return { ...object.value }
  }
  
  return {
    object: object as Ref<T>,
    set,
    get,
    has,
    remove,
    clear,
    reset,
    merge,
    clone,
    // 计算属性
    keys: computed(() => Object.keys(object.value)),
    values: computed(() => Object.values(object.value)),
    entries: computed(() => Object.entries(object.value)),
    size: computed(() => Object.keys(object.value).length),
    isEmpty: computed(() => Object.keys(object.value).length === 0)
  }
}

/**
 * 集合管理
 */
export function useSet<T>(initialValue: T[] = []) {
  const set = ref(new Set(initialValue))
  
  const add = (value: T) => {
    set.value.add(value)
  }
  
  const remove = (value: T) => {
    return set.value.delete(value)
  }
  
  const has = (value: T) => {
    return set.value.has(value)
  }
  
  const clear = () => {
    set.value.clear()
  }
  
  const reset = () => {
    set.value = new Set(initialValue)
  }
  
  const toggle = (value: T) => {
    if (has(value)) {
      remove(value)
    } else {
      add(value)
    }
  }
  
  return {
    set: set as Ref<Set<T>>,
    add,
    remove,
    has,
    clear,
    reset,
    toggle,
    // 计算属性
    size: computed(() => set.value.size),
    isEmpty: computed(() => set.value.size === 0),
    values: computed(() => Array.from(set.value))
  }
}

/**
 * Map管理
 */
export function useMap<K, V>(initialValue: [K, V][] = []) {
  const map = ref(new Map(initialValue))
  
  const set = (key: K, value: V) => {
    map.value.set(key, value)
  }
  
  const get = (key: K) => {
    return map.value.get(key)
  }
  
  const has = (key: K) => {
    return map.value.has(key)
  }
  
  const remove = (key: K) => {
    return map.value.delete(key)
  }
  
  const clear = () => {
    map.value.clear()
  }
  
  const reset = () => {
    map.value = new Map(initialValue)
  }
  
  return {
    map: map as Ref<Map<K, V>>,
    set,
    get,
    has,
    remove,
    clear,
    reset,
    // 计算属性
    size: computed(() => map.value.size),
    isEmpty: computed(() => map.value.size === 0),
    keys: computed(() => Array.from(map.value.keys())),
    values: computed(() => Array.from(map.value.values())),
    entries: computed(() => Array.from(map.value.entries()))
  }
}