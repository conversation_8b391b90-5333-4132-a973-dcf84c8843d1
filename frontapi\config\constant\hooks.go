package constant

// HookType 钩子类型
type HookType string

// 预定义钩子类型常量
const (
	// Service层钩子
	BeforeCreate HookType = "before_create"
	AfterCreate  HookType = "after_create"
	BeforeUpdate HookType = "before_update"
	AfterUpdate  HookType = "after_update"
	BeforeDelete HookType = "before_delete"
	AfterDelete  HookType = "after_delete"
	BeforeFind   HookType = "before_find"
	AfterFind    HookType = "after_find"

	// 批量操作钩子
	BeforeBatchCreate HookType = "before_batch_create"
	AfterBatchCreate  HookType = "after_batch_create"
	BeforeBatchUpdate HookType = "before_batch_update"
	AfterBatchUpdate  HookType = "after_batch_update"
	BeforeBatchDelete HookType = "before_batch_delete"
	AfterBatchDelete  HookType = "after_batch_delete"

	// Repository层钩子
	BeforeDBCreate HookType = "before_db_create"
	AfterDBCreate  HookType = "after_db_create"
	BeforeDBUpdate HookType = "before_db_update"
	AfterDBUpdate  HookType = "after_db_update"
	BeforeDBDelete HookType = "before_db_delete"
	AfterDBDelete  HookType = "after_db_delete"

	// Controller层钩子
	BeforeRequest  HookType = "before_request"
	AfterRequest   HookType = "after_request"
	BeforeResponse HookType = "before_response"
	AfterResponse  HookType = "after_response"
)