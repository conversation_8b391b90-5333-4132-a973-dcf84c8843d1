<template>
  <div class="book-table">
    <el-table
      v-loading="loading"
      :data="bookList"
      border
      stripe
      style="width: 100%"
      row-key="id"
    >
      <el-table-column type="index" width="50" align="center" />

      <el-table-column prop="cover" label="封面" width="120" align="center">
        <template #default="{ row }">
          <el-image
            :src="row.cover"
            fit="cover"
            style="width: 80px; height: 120px"
            :preview-src-list="[row.cover]"
          >
            <template #error>
              <div class="image-placeholder">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="书名" min-width="180" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="book-title">
            <span>{{ row.title }}</span>
            <el-tag v-if="row.is_featured === 1" type="warning" size="small" effect="dark">推荐</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="author" label="作者" width="120" show-overflow-tooltip />

      <el-table-column prop="category_name" label="分类" width="120" show-overflow-tooltip />

      <el-table-column prop="word_count" label="字数" width="100" align="center">
        <template #default="{ row }">
          {{ formatWordCount(row.word_count) }}
        </template>
      </el-table-column>

      <el-table-column prop="chapter_count" label="章节数" width="100" align="center" />

      <el-table-column prop="read_count" label="阅读量" width="100" align="center">
        <template #default="{ row }">
          {{ formatNumber(row.read_count) }}
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 'completed' ? 'success' : 'info'">
            {{ row.status === 'completed' ? '已完结' : '连载中' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="is_paid" label="付费" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_paid === 1 ? 'danger' : 'success'">
            {{ row.is_paid === 1 ? '付费' : '免费' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="180" align="center" />

      <el-table-column label="操作" width="280" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="success" link @click="handleDetail(row)">详情</el-button>
          <el-button type="warning" link @click="handleManageChapters(row)">章节管理</el-button>
          <el-popconfirm
            :title="$t('books.info.confirmDelete')"
            @confirm="handleDelete(row)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">

import { Picture } from '@element-plus/icons-vue';
import type { BookItem } from '@/types/books';

// 定义接收的属性
const props = defineProps({
  bookList: {
    type: Array as () => BookItem[],
    required: true,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['edit', 'detail', 'delete', 'manage-chapters']);

// 处理编辑
const handleEdit = (row: BookItem) => {
  emit('edit', row);
};

// 处理查看详情
const handleDetail = (row: BookItem) => {
  emit('detail', row);
};

// 处理删除
const handleDelete = (row: BookItem) => {
  emit('delete', row);
};

// 处理章节管理
const handleManageChapters = (row: BookItem) => {
  emit('manage-chapters', row);
};

// 格式化字数
const formatWordCount = (wordCount: number): string => {
  if (wordCount >= 10000) {
    return (wordCount / 10000).toFixed(1) + '万字';
  }
  if(!wordCount) wordCount="0";
  return wordCount + '字';
};

// 格式化数字（添加千分位）
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万';
  }
  if(!num) return "0";
  return num.toString();
};
</script>

<style scoped>
.book-table {
  margin-bottom: 20px;
}

.book-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 10px;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 120px;
  background-color: #f0f0f0;
  color: #909399;
  font-size: 24px;
}
</style>
