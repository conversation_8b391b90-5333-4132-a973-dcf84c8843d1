@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局变量 */
:root {
    /* 主题颜色变量 */
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4cc9f0;
    --success-color: #4caf50;
    --info-color: #2196f3;
    --warning-color: #ff9800;
    --danger-color: #f44336;

    /* 文本颜色变量 */
    --text-color: #333333;
    --text-color-light: #666666;
    --text-color-lighter: #999999;

    /* 背景颜色变量 */
    --bg-color: #ffffff;
    --bg-color-light: #f5f5f5;
    --bg-color-dark: #eeeeee;

    /* 边框颜色变量 */
    --border-color: #e0e0e0;
    --border-color-light: #eeeeee;

    /* 阴影变量 */
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --box-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
    --box-shadow-large: 0 8px 16px rgba(0, 0, 0, 0.1);

    /* 过渡变量 */
    --transition-speed: 0.3s;
}

/* 暗黑主题变量 */
.theme-dark {
    --primary-color: #4cc9f0;
    --secondary-color: #3f37c9;
    --accent-color: #4361ee;
    --success-color: #81c784;
    --info-color: #64b5f6;
    --warning-color: #ffb74d;
    --danger-color: #e57373;

    --text-color: #f5f5f5;
    --text-color-light: #e0e0e0;
    --text-color-lighter: #bdbdbd;

    --bg-color: #121212;
    --bg-color-light: #1e1e1e;
    --bg-color-dark: #272727;

    --border-color: #333333;
    --border-color-light: #454545;

    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    --box-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.5);
    --box-shadow-large: 0 8px 16px rgba(0, 0, 0, 0.5);
}

/* 紫色主题变量 */
.theme-purple {
    --primary-color: #9c27b0;
    --secondary-color: #7b1fa2;
    --accent-color: #e1bee7;
}

/* 粉色主题变量 */
.theme-pink {
    --primary-color: #e91e63;
    --secondary-color: #c2185b;
    --accent-color: #f8bbd0;
}

/* 渐变主题变量 */
.theme-gradient {
    --primary-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    --accent-color: #b2a8d9;
}

/* 基础样式 */
html,
body,
#app {
    height: 100%;
    margin: 0;
    padding: 0;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
}

/* 通用过渡效果 */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color-light);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-color-lighter);
}

/* PrimeVue 水波纹效果控制 */
.p-button {
    .p-ink {
        height: 100% !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
        transform: scale(0.8);
    }
}