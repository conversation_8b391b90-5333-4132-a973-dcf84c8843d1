/**
 * 主样式入口文件
 */

/* PrimeVue 主题样式会通过主题系统自动加载 */

/* Tailwind CSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */

html {
    font-size: 14px;
}

:root {
    --main-width: 1200px;
}

body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: var(--font-family);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    // background: linear-gradient(135deg, var(--surface-0), var(--surface-100), var(--surface-100), var(--surface-50));
    color: var(--pimary-text-color);
}

.p-popover:before {
    border-bottom-color: var(--p-popover-border-color, var(--surface-border, var(--border-color, var(--border-color-primary, inherit))));
}


//重置primevue 4 radius变量