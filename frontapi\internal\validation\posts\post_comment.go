package posts

import "github.com/guregu/null/v6"

// PostCommentRequest 帖子评论请求验证模型
type PostCommentRequest struct {
	PostID      uint     `json:"postId" validate:"required|min:1"`
	Content     string   `json:"content" validate:"required|minLen:1|maxLen:500"`
	Images      []string `json:"images" validate:"each:url"`
	ParentID    uint     `json:"parentId"` // 父评论ID，0表示顶级评论
	IsAnonymous bool     `json:"isAnonymous"`
}

// CreateCommentRequest 创建评论请求
type CreateCommentRequest struct {
	Content     string      `json:"content" validate:"required"`
	UserID      null.String `json:"user_id" validate:"required"`
	PostID      null.String `json:"post_id" validate:"required"`
	ParentID    null.String `json:"parent_id"`
	ReplyID     null.String `json:"reply_id"`
	ReplyUserID null.String `json:"reply_user_id"`
}

// UpdateCommentRequest 更新评论请求
type UpdateCommentRequest struct {
	Content string `json:"content" validate:"required"`
	Status  int    `json:"status"`
}
