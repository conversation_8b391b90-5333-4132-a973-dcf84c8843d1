package wallets

import (
	"frontapi/internal/models/wallets"
	repo "frontapi/internal/repository/wallets"
	"frontapi/internal/service/base"
)

// WithdrawRequestService 提现申请服务接口
type WithdrawRequestService interface {
	base.IExtendedService[wallets.WithdrawRequest]
}

// withdrawRequestService 提现申请服务实现
type withdrawRequestService struct {
	*base.ExtendedService[wallets.WithdrawRequest]
	withdrawRequestRepo repo.WithdrawRequestRepository
	walletRepo          repo.WalletRepository
}

// NewWithdrawRequestService 创建提现申请服务实例
func NewWithdrawRequestService(
	withdrawRequestRepo repo.WithdrawRequestRepository,
	walletRepo repo.WalletRepository,
) WithdrawRequestService {
	return &withdrawRequestService{
		ExtendedService:     base.NewExtendedService[wallets.WithdrawRequest](withdrawRequestRepo, "withdraw_request"),
		withdrawRequestRepo: withdrawRequestRepo,
		walletRepo:          walletRepo,
	}
}
