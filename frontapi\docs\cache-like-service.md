# 缓存点赞服务使用指南

## 概述

缓存点赞服务是一个通用的 Redis 缓存点赞解决方案，支持各种类型的内容点赞功能，包括评论、视频、帖子、图片、书籍、漫画等。该服务优先使用 Redis 缓存进行点赞操作，提高性能，并支持定时同步到数据库。

## 架构设计

### 核心组件

1. **CacheLikeService**: 核心缓存点赞服务接口
2. **LikeTrait**: 点赞功能 trait 接口
3. **LikeableService**: 可点赞服务接口
4. **LikeableServiceImpl**: 可点赞服务实现

### 缓存键设计

- `user_likes:{itemType}:{userID}`: 用户点赞记录集合
- `item_likes_count:{itemType}:{itemID}`: 项目点赞数量
- `like:{itemType}:{userID}:{itemID}`: 具体点赞记录

## 使用方法

### 1. 在服务中集成点赞功能

```go
package yourservice

import (
    "context"
    "fmt"
    "frontapi/internal/service/base"
    // 其他导入...
)

// YourService 你的服务接口
type YourService interface {
    base.IExtendedService[YourModel]
    base.LikeableService  // 集成点赞功能
}

// yourService 你的服务实现
type yourService struct {
    *base.ExtendedService[YourModel]
    *base.LikeableServiceImpl  // 嵌入点赞功能实现
    yourRepo     YourRepository
    yourLikeRepo YourLikeRepository
}

// NewYourService 创建服务实例
func NewYourService(
    yourRepo YourRepository,
    yourLikeRepo YourLikeRepository,
) YourService {
    service := &yourService{
        ExtendedService: base.NewExtendedService[YourModel](yourRepo, "your_table"),
        yourRepo:        yourRepo,
        yourLikeRepo:    yourLikeRepo,
    }
    
    // 初始化点赞功能，传入项目类型和自身作为数据库操作服务
    service.LikeableServiceImpl = base.NewLikeableServiceImpl("your_item_type", service)
    
    return service
}
```

### 2. 实现数据库操作方法

```go
// DBLikeItem 数据库点赞操作实现
func (s *yourService) DBLikeItem(ctx context.Context, userID, itemID string) error {
    // 检查是否已经点赞
    exists, err := s.yourLikeRepo.ExistsByCondition(ctx, map[string]interface{}{
        "user_id": userID,
        "item_id": itemID,
    })
    if err != nil {
        return fmt.Errorf("检查点赞记录失败: %w", err)
    }
    if exists {
        return fmt.Errorf("用户已经点赞过此项目")
    }

    // 创建点赞记录
    like := &YourLikeModel{
        UserID: userID,
        ItemID: itemID,
    }
    _, err = s.yourLikeRepo.Create(ctx, like)
    if err != nil {
        return fmt.Errorf("创建点赞记录失败: %w", err)
    }

    return nil
}

// DBUnlikeItem 数据库取消点赞操作实现
func (s *yourService) DBUnlikeItem(ctx context.Context, userID, itemID string) error {
    err := s.yourLikeRepo.DeleteByCondition(ctx, map[string]interface{}{
        "user_id": userID,
        "item_id": itemID,
    })
    if err != nil {
        return fmt.Errorf("删除点赞记录失败: %w", err)
    }
    return nil
}

// DBIsLiked 数据库检查点赞状态实现
func (s *yourService) DBIsLiked(ctx context.Context, userID, itemID string) (bool, error) {
    exists, err := s.yourLikeRepo.ExistsByCondition(ctx, map[string]interface{}{
        "user_id": userID,
        "item_id": itemID,
    })
    if err != nil {
        return false, fmt.Errorf("检查点赞状态失败: %w", err)
    }
    return exists, nil
}

// DBGetLikeCount 数据库获取点赞数量实现
func (s *yourService) DBGetLikeCount(ctx context.Context, itemID string) (int64, error) {
    count, err := s.yourLikeRepo.Count(ctx, map[string]interface{}{
        "item_id": itemID,
    })
    if err != nil {
        return 0, fmt.Errorf("获取点赞数量失败: %w", err)
    }
    return count, nil
}
```

### 3. 在控制器中使用

```go
package controller

import (
    "github.com/gofiber/fiber/v2"
    // 其他导入...
)

// LikeItem 点赞操作
func (c *YourController) LikeItem(ctx *fiber.Ctx) error {
    userID := c.GetUserID(ctx)
    itemID := ctx.Params("id")
    
    err := c.YourService.LikeItem(ctx.Context(), userID, itemID)
    if err != nil {
        return c.BadRequest(ctx, err.Error())
    }
    
    return c.Success(ctx, "点赞成功")
}

// UnlikeItem 取消点赞操作
func (c *YourController) UnlikeItem(ctx *fiber.Ctx) error {
    userID := c.GetUserID(ctx)
    itemID := ctx.Params("id")
    
    err := c.YourService.UnlikeItem(ctx.Context(), userID, itemID)
    if err != nil {
        return c.BadRequest(ctx, err.Error())
    }
    
    return c.Success(ctx, "取消点赞成功")
}

// IsLiked 检查点赞状态
func (c *YourController) IsLiked(ctx *fiber.Ctx) error {
    userID := c.GetUserID(ctx)
    itemID := ctx.Params("id")
    
    isLiked, err := c.YourService.IsLiked(ctx.Context(), userID, itemID)
    if err != nil {
        return c.InternalServerError(ctx, err.Error())
    }
    
    return c.Success(ctx, map[string]bool{"is_liked": isLiked})
}

// GetLikeCount 获取点赞数量
func (c *YourController) GetLikeCount(ctx *fiber.Ctx) error {
    itemID := ctx.Params("id")
    
    count, err := c.YourService.GetLikeCount(ctx.Context(), itemID)
    if err != nil {
        return c.InternalServerError(ctx, err.Error())
    }
    
    return c.Success(ctx, map[string]int64{"like_count": count})
}
```

## 支持的项目类型

该缓存点赞服务支持以下项目类型：

- `video`: 视频点赞
- `shortvideo`: 短视频点赞
- `post`: 帖子点赞
- `picture`: 图片点赞
- `book`: 书籍点赞
- `comic`: 漫画点赞
- `video_comment`: 视频评论点赞
- `shortvideo_comment`: 短视频评论点赞
- `post_comment`: 帖子评论点赞
- `user`: 用户关注

## 定时任务同步

### 创建定时任务

```go
package tasks

import (
    "context"
    "log"
    "time"
    "frontapi/internal/service/base"
)

// LikeSyncTask 点赞数据同步任务
type LikeSyncTask struct {
    cacheLikeService base.CacheLikeService
}

// NewLikeSyncTask 创建点赞同步任务
func NewLikeSyncTask() *LikeSyncTask {
    return &LikeSyncTask{
        cacheLikeService: base.NewCacheLikeService(),
    }
}

// SyncAllLikes 同步所有点赞数据
func (t *LikeSyncTask) SyncAllLikes() {
    ctx := context.Background()
    itemTypes := []string{
        "video", "shortvideo", "post", "picture", "book", "comic",
        "video_comment", "shortvideo_comment", "post_comment", "user",
    }
    
    for _, itemType := range itemTypes {
        err := t.cacheLikeService.FlushAllLikes(ctx, itemType)
        if err != nil {
            log.Printf("同步 %s 点赞数据失败: %v", itemType, err)
        } else {
            log.Printf("同步 %s 点赞数据成功", itemType)
        }
    }
}

// StartSyncScheduler 启动定时同步任务
func (t *LikeSyncTask) StartSyncScheduler() {
    ticker := time.NewTicker(1 * time.Hour) // 每小时同步一次
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            t.SyncAllLikes()
        }
    }
}
```

## 性能优化建议

1. **缓存过期时间**: 根据业务需求调整缓存过期时间，默认24小时
2. **批量操作**: 对于大量数据的同步，考虑使用批量操作
3. **错误处理**: 实现完善的错误处理和重试机制
4. **监控**: 添加缓存命中率和同步成功率的监控

## 注意事项

1. **数据一致性**: 缓存和数据库之间可能存在短暂的数据不一致
2. **Redis 可用性**: 确保 Redis 服务的高可用性
3. **内存使用**: 监控 Redis 内存使用情况，避免内存溢出
4. **并发安全**: Redis 操作本身是原子的，但复合操作需要注意并发安全

## 示例项目

参考 `internal/service/shortvideos/shortvideo_comment_service.go` 中的实现示例。