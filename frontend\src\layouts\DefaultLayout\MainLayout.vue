<template>
    <div class="main-layout min-h-screen flex flex-col" :class="{'dark-theme': isDarkMode}">
        <!-- 顶部导航栏 -->
        <header class="shadow-sm bg-header-gradient">
            <div class="container mx-auto px-4 flex items-center justify-between h-16">
                <!-- 左侧Logo和导航 -->
                <div class="flex items-center gap-8">
                    <!-- Logo -->
                    <div class="logo-container">
                        <router-link to="/" class="flex items-center">
                            <img src="@/assets/icons/logo.svg" alt="Logo" class="h-10 w-auto"
                                onerror="this.src='/logo-placeholder.svg'" />
                            <span class="ml-2 font-bold text-xl text-white">SoFans</span>
                        </router-link>
                    </div>

                    <!-- 主导航菜单 - 桌面版 -->
                    <nav class="hidden md:flex">
                        <ul class="flex gap-6">
                            <li v-for="(item, index) in mainNavItems" :key="index">
                                <router-link :to="item.path"
                                    class="nav-link text-white hover:text-primary-contrast transition-colors"
                                    active-class="text-primary-contrast font-medium">
                                    {{ t(item.title) }}
                                </router-link>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- 右侧用户区域和工具 -->
                <div class="flex items-center gap-4">
                    <!-- 搜索按钮 -->
                    <Button icon="pi pi-search" class="p-button-text p-button-rounded nav-button" @click="showSearch" />

                    <!-- 主题和语言切换器 -->
                    <div class="hidden md:flex items-center gap-2">
                        <NavThemeSelector />
                        <NavLanguageSelector />
                    </div>

                    <!-- 用户菜单 -->
                    <Menu ref="userMenu" :model="userMenuItems" :popup="true" />

                    <!-- 用户头像/登录按钮 -->
                    <div>
                        <Button v-if="isLoggedIn" @click="toggleUserMenu" class="p-button-rounded p-button-text nav-button">
                            <Avatar :image="userAvatar" shape="circle" />
                        </Button>
                        <Button v-else :label="t('user.login')" class="p-button-outlined nav-button" @click="goToLogin" />

                    </div>

                    <!-- 移动端菜单按钮 -->
                    <Button icon="pi pi-bars" class="p-button-text p-button-rounded nav-button md:hidden"
                        @click="showMobileMenu = true" />
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="flex-grow main-content">
            <div class="container mx-auto px-4 py-6">
                <router-view v-slot="{ Component }">
                    <transition name="fade" mode="out-in">
                        <component :is="Component" />
                    </transition>
                </router-view>
            </div>
        </main>

        <!-- 页脚 -->
        <footer class="bg-footer-gradient shadow-inner py-8">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <!-- 关于我们 -->
                    <div>
                        <h3 class="text-lg font-bold mb-4 text-footer-title">关于我们</h3>
                        <ul class="space-y-2">
                            <li><router-link to="/about"
                                    class="text-footer-link hover:text-primary transition-colors">公司简介</router-link>
                            </li>
                            <li><router-link to="/contact"
                                    class="text-footer-link hover:text-primary transition-colors">联系我们</router-link>
                            </li>
                            <li><router-link to="/jobs"
                                    class="text-footer-link hover:text-primary transition-colors">加入我们</router-link>
                            </li>
                        </ul>
                    </div>

                    <!-- 帮助中心 -->
                    <div>
                        <h3 class="text-lg font-bold mb-4 text-footer-title">帮助中心</h3>
                        <ul class="space-y-2">
                            <li><router-link to="/faq"
                                    class="text-footer-link hover:text-primary transition-colors">常见问题</router-link>
                            </li>
                            <li><router-link to="/feedback"
                                    class="text-footer-link hover:text-primary transition-colors">意见反馈</router-link>
                            </li>
                            <li><router-link to="/report"
                                    class="text-footer-link hover:text-primary transition-colors">侵权举报</router-link>
                            </li>
                        </ul>
                    </div>

                    <!-- 商务合作 -->
                    <div>
                        <h3 class="text-lg font-bold mb-4 text-footer-title">商务合作</h3>
                        <ul class="space-y-2">
                            <li><router-link to="/business"
                                    class="text-footer-link hover:text-primary transition-colors">广告投放</router-link>
                            </li>
                            <li><router-link to="/cooperation"
                                    class="text-footer-link hover:text-primary transition-colors">商务合作</router-link>
                            </li>
                            <li><router-link to="/creator"
                                    class="text-footer-link hover:text-primary transition-colors">内容创作者</router-link>
                            </li>
                        </ul>
                    </div>

                    <!-- 下载我们的应用 -->
                    <div>
                        <h3 class="text-lg font-bold mb-4 text-footer-title">下载应用</h3>
                        <div class="flex gap-4">
                            <a href="#" class="text-footer-link hover:text-primary transition-colors">
                                <i class="pi pi-apple text-2xl"></i>
                            </a>
                            <a href="#" class="text-footer-link hover:text-primary transition-colors">
                                <i class="pi pi-android text-2xl"></i>
                            </a>
                        </div>

                        <!-- 主题和语言切换 -->
                        <div class="mt-6">
                            <h3 class="text-lg font-bold mb-4 text-footer-title">设置</h3>
                            <div class="flex flex-col gap-3">
                                <FooterThemeSelector />
                                <FooterLanguageSelector />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 版权信息 -->
                <div class="mt-8 pt-4 border-t border-footer-border text-center">
                    <p class="text-footer-link">&copy; {{ new Date().getFullYear() }} MyFirm. All Rights Reserved.
                    </p>
                </div>
            </div>
        </footer>

        <!-- 移动端菜单 -->
        <Sidebar v-model:visible="showMobileMenu" position="right" class="p-sidebar-md">
            <div class="p-4">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">菜单</h2>
                    <Button icon="pi pi-times" class="p-button-text p-button-rounded" @click="showMobileMenu = false" />
                </div>

                <div class="mb-6 space-y-3">
                    <FooterThemeSelector />
                    <NavLanguageSelector />
                </div>

                <!-- 导航链接 -->
                <ul class="space-y-4">
                    <li v-for="(item, index) in mainNavItems" :key="index">
                        <router-link :to="item.path" class="block py-2 text-lg border-b border-border"
                            active-class="text-primary font-medium" @click="showMobileMenu = false">
                            <i :class="item.icon" class="mr-2"></i>
                            {{ t(item.title) }}
                        </router-link>
                    </li>
                </ul>

                <!-- 登录/用户区域 -->
                <div class="mt-6 pt-4 border-t border-border">
                    <Button v-if="!isLoggedIn" label="登录" class="p-button-primary w-full" @click="goToLogin" />
                    <div v-else class="flex flex-col gap-4">
                        <div class="flex items-center gap-2">
                            <Avatar :image="userAvatar" shape="circle" />
                            <div>
                                <div class="font-medium">{{ userName }}</div>
                                <div class="text-sm text-text-light">{{ userEmail }}</div>
                            </div>
                        </div>
                        <Button label="个人中心" class="p-button-outlined w-full" @click="goToUserProfile" />
                        <Button label="退出登录" class="p-button-danger p-button-text w-full" @click="logout" />
                    </div>
                </div>
            </div>
        </Sidebar>

        <!-- 搜索对话框 -->
        <Dialog v-model:visible="searchVisible" header="搜索" :modal="true" :style="{ width: '90%', maxWidth: '600px' }">
            <div class="flex flex-col gap-4">
                <div class="flex gap-2">
                    <InputText v-model="searchQuery" placeholder="搜索..." class="flex-grow"
                        @keyup.enter="performSearch" />
                    <Button label="搜索" @click="performSearch" />
                </div>
                <div>
                    <div class="flex gap-2 mb-2">
                        <div v-for="(type, index) in searchTypes" :key="index">
                            <Chip :label="type.label"
                                :class="{ 'bg-primary text-white': currentSearchType === type.value }"
                                @click="currentSearchType = type.value" />
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
import FooterLanguageSelector from '@/shared/components/LanguageSelector/FooterLanguageSelector.vue';
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import FooterThemeSelector from '@/shared/components/ThemeSelector/FooterThemeSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import { useLocaleStore } from '@/store/modules/locale';
import { useThemeStore } from '@/store/modules/theme';
import Avatar from 'primevue/avatar';
import Button from 'primevue/button';
import Chip from 'primevue/chip';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Menu from 'primevue/menu';
import Sidebar from 'primevue/sidebar';
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

// i18n和主题管理
const { t } = useI18n({ useScope: 'global' });
const localeStore = useLocaleStore();
const themeStore = useThemeStore();
const currentThemeCode = computed(() => themeStore.currentThemeCode);
const isDarkMode = computed(() => themeStore.isDark);

// 路由和响应式引用
const router = useRouter();

// 导航菜单项
const mainNavItems = [
    { title: 'common.home', path: '/', icon: 'pi pi-home' },
    { title: 'common.videos', path: '/videos', icon: 'pi pi-video' },
    { title: 'common.shorts', path: '/shorts', icon: 'pi pi-mobile' },
    { title: 'common.community', path: '/community', icon: 'pi pi-users' },
    { title: 'common.pictures', path: '/pictures', icon: 'pi pi-images' },
    { title: 'common.manga', path: '/manga', icon: 'pi pi-book' },
    { title: 'common.ebook', path: '/ebook', icon: 'pi pi-file' }
];



// 用户菜单
const userMenu = ref();
const toggleUserMenu = (event: MouseEvent) => {
    userMenu.value.toggle(event);
};

// 用户状态模拟 (实际项目中应该从用户存储中获取)
const isLoggedIn = ref(false);
const userName = ref('用户名');
const userEmail = ref('<EMAIL>');
const userAvatar = ref('https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp');

// 用户菜单项
const userMenuItems = ref([
    {
        label: '个人中心',
        icon: 'pi pi-user',
        command: () => goToUserProfile()
    },
    {
        label: '我的收藏',
        icon: 'pi pi-heart',
        command: () => router.push('/favorites')
    },
    {
        label: '设置',
        icon: 'pi pi-cog',
        command: () => router.push('/settings')
    },
    { separator: true },
    {
        label: '退出登录',
        icon: 'pi pi-power-off',
        command: () => logout()
    }
]);

// 移动端菜单
const showMobileMenu = ref(false);

// 搜索
const searchVisible = ref(false);
const searchQuery = ref('');
const currentSearchType = ref('all');
const searchTypes = ref([
    { label: '全部', value: 'all' },
    { label: '视频', value: 'videos' },
    { label: '短视频', value: 'shorts' },
    { label: '图片', value: 'pictures' },
    { label: '文章', value: 'posts' }
]);

// 功能函数
const showSearch = () => {
    searchVisible.value = true;
};

const performSearch = () => {
    if (searchQuery.value.trim()) {
        // 实现搜索逻辑
        router.push({
            path: '/search',
            query: {
                q: searchQuery.value,
                type: currentSearchType.value
            }
        });
        searchVisible.value = false;
    }
};

const goToLogin = () => {
    router.push('/login');
};

const goToUserProfile = () => {
    router.push('/user/profile');
};

const logout = () => {
    // 实现登出逻辑
    isLoggedIn.value = false;
    router.push('/');
};
</script>

<style scoped lang="scss">
.main-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.bg-header-gradient {
    background: var(--header-bg, var(--surface-card));
    color: var(--header-text, var(--text-color));
    transition: background-color 0.3s ease, color 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.bg-footer-gradient {
    background: var(--footer-bg, var(--surface-section));
    color: var(--footer-text, var(--text-color-secondary));
    transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-button {
    color: var(--header-text, var(--text-color)) !important;
    transition: background-color 0.2s ease, color 0.2s ease;
    
    &:hover {
        background-color: var(--surface-hover) !important;
    }
}

.text-footer-title {
    color: var(--footer-text, var(--text-color));
    font-weight: bold;
}

.text-footer-link {
    color: var(--footer-text, var(--text-color-secondary));

    &:hover {
        color: var(--primary-color);
    }
}

.main-content {
    background: var(--surface-ground);
    color: var(--text-color);
    min-height: calc(100vh - 120px);
    position: relative;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 导航链接样式 */
.nav-link {
    padding: 0.5rem 0;
    position: relative;
    color: var(--header-text, var(--text-color));
    transition: color 0.2s ease;

    &.router-link-active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--header-text, var(--primary-color));
        transition: all 0.3s ease;
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
</style>