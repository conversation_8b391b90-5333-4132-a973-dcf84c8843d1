<template>
  <div class="modern-header-wrapper">
    <header class="modern-header">
      <!-- 主导航栏 -->
      <div class="header-main">
        <div class="header-container">
          <!-- 左侧区域 -->
          <div class="header-left">
            <div class="logo-section">
              <router-link to="/" class="logo-link">
                <div class="logo-icon">
                  <div class="logo-gradient"></div>
                  <span class="logo-text">MyFirm</span>
                </div>
              </router-link>
            </div>
            
            <!-- 桌面端导航菜单 -->
            <nav class="desktop-nav">
              <div class="nav-item" :class="{ active: $route.path === '/' }">
                <router-link to="/">
                  <el-icon><House /></el-icon>
                  <span>首页</span>
                </router-link>
              </div>
              
              <div class="nav-item nav-dropdown" @mouseenter="showCategoryDropdown = true" @mouseleave="showCategoryDropdown = false">
                <div class="nav-link">
                  <el-icon><Grid /></el-icon>
                  <span>分类</span>
                  <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                </div>
                <Transition name="dropdown">
                  <div v-show="showCategoryDropdown" class="category-dropdown">
                    <div v-if="categoriesLoading" class="dropdown-loading">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>加载中...</span>
                    </div>
                    <div v-else class="category-grid">
                      <div 
                        v-for="category in categories.slice(0, 8)" 
                        :key="category.id"
                        class="category-card"
                        @click="handleCategoryClick(category)"
                      >
                        <div class="category-icon">
                          <el-icon><Grid /></el-icon>
                        </div>
                        <span class="category-name">{{ category.name }}</span>
                      </div>
                    </div>
                    <div class="dropdown-footer">
                      <router-link to="/category" class="view-all-btn">
                        查看全部分类 <el-icon><ArrowRight /></el-icon>
                      </router-link>
                    </div>
                  </div>
                </Transition>
              </div>
              
              <div class="nav-item" :class="{ active: $route.path.includes('/channels') }">
                <router-link to="/channels">
                  <el-icon><VideoPlay /></el-icon>
                  <span>频道</span>
                </router-link>
              </div>
              
              <div class="nav-item" :class="{ active: $route.path.includes('/shorts') }">
                <router-link to="/shorts">
                  <el-icon><Film /></el-icon>
                  <span>短视频</span>
                </router-link>
              </div>
              
              <div class="nav-item" :class="{ active: $route.path.includes('/community') }">
                <router-link to="/community">
                  <el-icon><ChatLineRound /></el-icon>
                  <span>社区</span>
                </router-link>
              </div>
            </nav>
          </div>
          
          <!-- 中间搜索区域 -->
          <div class="header-center">
            <div class="search-container">
              <div class="search-input-wrapper">
                <el-input
                  v-model="searchQuery"
                  placeholder="搜索你感兴趣的内容..."
                  class="modern-search"
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <el-icon class="search-icon"><Search /></el-icon>
                  </template>
                </el-input>
                <button class="search-btn" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 右侧区域 -->
          <div class="header-right">
            <!-- 语言和地区选择 -->
            <div class="region-selector">
              <el-dropdown trigger="hover" class="language-dropdown">
                <div class="dropdown-trigger">
                  <el-icon><Globe /></el-icon>
                  <span class="current-lang">{{ getCurrentLanguageDisplay() }}</span>
                  <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="selectedLanguage = 'zh-CN'">🇨🇳 简体中文</el-dropdown-item>
                    <el-dropdown-item @click="selectedLanguage = 'en-US'">🇺🇸 English</el-dropdown-item>
                    <el-dropdown-item @click="selectedLanguage = 'ja-JP'">🇯🇵 日本語</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <!-- 主题切换 -->
            <div class="theme-toggle">
              <button class="theme-btn" @click="toggleTheme">
                <Transition name="theme-icon" mode="out-in">
                  <el-icon v-if="isDarkMode" key="moon"><Moon /></el-icon>
                  <el-icon v-else key="sun"><Sunny /></el-icon>
                </Transition>
              </button>
            </div>
            
            <!-- 用户操作 -->
            <div class="user-section">
              <div v-if="!isLoggedIn" class="auth-buttons">
                <button class="login-btn" @click="showAuthDialog('login')">
                  登录
                </button>
                <button class="register-btn" @click="showAuthDialog('register')">
                  注册
                </button>
              </div>
              <div v-else class="user-menu">
                <el-dropdown trigger="hover">
                  <div class="user-avatar">
                    <img src="/api/placeholder/32/32" alt="用户头像" />
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="$router.push('/space/my')">
                        <el-icon><User /></el-icon>
                        个人中心
                      </el-dropdown-item>
                      <el-dropdown-item @click="$router.push('/space/settings')">
                        <el-icon><Setting /></el-icon>
                        设置
                      </el-dropdown-item>
                      <el-dropdown-item divided @click="handleLogout">
                        <el-icon><SwitchButton /></el-icon>
                        退出登录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            
            <!-- 移动端菜单按钮 -->
            <button class="mobile-menu-btn" @click="toggleMobileMenu">
              <Transition name="menu-icon" mode="out-in">
                <el-icon v-if="!mobileMenuOpen" key="menu"><Menu /></el-icon>
                <el-icon v-else key="close"><Close /></el-icon>
              </Transition>
            </button>
          </div>
        </div>
      </div>
    <div class="header-menu" :class="{ 'is-hidden': !menuVisible && isMobile }">
      <el-menu mode="horizontal" :ellipsis="false" class="main-menu" router>
        <el-menu-item index="/">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-menu-item>
        <el-dropdown 
          trigger="hover" 
          placement="bottom-start"
          :show-timeout="100"
          :hide-timeout="200"
          class="category-dropdown"
        >
          <template #default>
            <el-menu-item index="/category" class="category-menu-item">
              <el-icon><Grid /></el-icon>
              <span>分类</span>
              <el-icon class="dropdown-arrow"><ArrowDown /></el-icon>
            </el-menu-item>
          </template>
          <template #dropdown>
            <el-dropdown-menu class="category-dropdown-menu">
              <div v-if="categoriesLoading" class="loading-state">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
              <div v-else-if="categories.length === 0" class="empty-state">
                <span>暂无分类数据</span>
              </div>
              <div v-else class="categories-grid">
                <el-dropdown-item 
                  v-for="category in categories" 
                  :key="category.id"
                  class="category-item"
                  @click="handleCategoryClick(category)"
                >
                  <div 
                    class="category-content"
                    :style="getCategoryBackgroundStyle(category)"
                  >
                    <div class="category-overlay"></div>
                    <div class="category-info">
                      <div class="category-icon">
                        <el-icon class="category-icon-default">
                          <Grid />
                        </el-icon>
                      </div>
                      <div class="category-name">{{ category.name }}</div>
                    </div>
                  </div>
                </el-dropdown-item>
              </div>
              <div class="dropdown-footer">
                <el-button 
                  type="primary" 
                  text 
                  size="small"
                  @click="$router.push('/category')"
                >
                  查看全部分类 <el-icon><ArrowRight /></el-icon>
                </el-button>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-menu-item index="/channels">
          <el-icon><VideoPlay /></el-icon>
          <span>频道</span>
        </el-menu-item>
        <el-menu-item index="/shorts">
          <el-icon><Film /></el-icon>
          <span>短视频</span>
        </el-menu-item>
        <el-menu-item index="/celebrity">
          <el-icon><Star /></el-icon>
          <span>明星</span>
        </el-menu-item>
        <el-menu-item index="/community">
          <el-icon><ChatLineRound /></el-icon>
          <span>社区</span>
        </el-menu-item>
        <el-menu-item index="/pictures">
          <el-icon><Picture /></el-icon>
          <span>图片</span>
        </el-menu-item>
        <el-menu-item index="/comics">
          <el-icon><VideoPlay /></el-icon>
          <span>漫画</span>
        </el-menu-item>
        <el-menu-item index="/books">
          <el-icon><Film /></el-icon>
          <span>点子书</span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  House, 
  Grid, 
  VideoPlay, 
  Film,
  Search,
  Moon,
  Sunny,
  Star,
  Picture,
  Menu,
  Close,
  ChatLineRound,
  ArrowDown,
  ArrowRight,
  Loading,
  Globe,
  User,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import AuthDialog from '@/shared/components/auth/AuthDialog.vue'
import { getNavigationCategories } from '../api/category'

// 分类类型定义
interface VideoCategory {
  id: string
  name: string
  code?: string
  icon?: string
  cover?: string
  description?: string
  parent_id?: string
  sort?: number
  sort_order?: number
  status?: number
  is_featured?: number
  video_count?: number
  view_count?: number
  created_at?: string
  updated_at?: string
}

const searchQuery = ref('')
const selectedLanguage = ref('zh-CN')
const selectedRegion = ref('CN')
const isDarkMode = ref(false)
const menuVisible = ref(true)
const mobileMenuOpen = ref(false)
const authDialogVisible = ref(false)
const currentAuthComponent = ref('login')
const isLoggedIn = ref(false)
const showCategoryDropdown = ref(false)

// 分类相关状态
const categories = ref<VideoCategory[]>([])
const categoriesLoading = ref(false)

// 认证相关方法
const showAuthDialog = (component: string) => {
  currentAuthComponent.value = component
  authDialogVisible.value = true
}

const handleLoginSuccess = () => {
  authDialogVisible.value = false
  isLoggedIn.value = true
  ElMessage.success('登录成功')
}

const handleLogout = () => {
  isLoggedIn.value = false
  ElMessage.success('已退出登录')
  // TODO: 清除用户数据
}

// 搜索相关方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // TODO: 实现搜索功能
    console.log('搜索:', searchQuery.value)
  }
}

// 移动端菜单
const isMobile = computed(() => window.innerWidth <= 768)

const toggleMenu = () => {
  menuVisible.value = !menuVisible.value
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// 主题切换
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  handleThemeChange(isDarkMode.value)
}

const handleThemeChange = (value: boolean) => {
  const theme = value ? 'dark' : 'light'
  document.documentElement.setAttribute('data-theme', theme)
  // 应用主题CSS变量
  if (value) {
    document.documentElement.style.setProperty('--color-background', '#1a1a1a')
    document.documentElement.style.setProperty('--color-surface', '#2d2d2d')
    document.documentElement.style.setProperty('--color-text', '#ffffff')
  } else {
    document.documentElement.style.setProperty('--color-background', '#ffffff')
    document.documentElement.style.setProperty('--color-surface', '#f8fafc')
    document.documentElement.style.setProperty('--color-text', '#1e293b')
  }
}

// 语言显示
const getCurrentLanguageDisplay = () => {
  const langMap: Record<string, string> = {
    'zh-CN': '中文',
    'en-US': 'EN',
    'ja-JP': '日本語'
  }
  return langMap[selectedLanguage.value] || '中文'
}

const initTheme = () => {
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  isDarkMode.value = prefersDark
  handleThemeChange(prefersDark)
}

// 加载导航分类
const loadNavigationCategories = async () => {
  if (categoriesLoading.value) return
  
  categoriesLoading.value = true
  try {
    const result = await getNavigationCategories({}) as any
    console.log('原始分类数据:', result)
    
    // 处理不同的响应格式
    let categoryList: VideoCategory[] = []
    
    if (Array.isArray(result)) {
      categoryList = result
    } else if (result && result.list) {
      if (Array.isArray(result.list)) {
        categoryList = result.list
      }
    } else if (result && result.data) {
      if (Array.isArray(result.data)) {
        categoryList = result.data
      } else if (result.data.list) {
        categoryList = result.data.list
      }
    }
    
    // 确保每个分类都有必要的字段
    categories.value = categoryList.map(category => ({
      id: category.id || '',
      name: category.name || '未知分类',
      code: category.code || '',
      icon: category.icon || category.cover || '',
      description: category.description || '',
      sortorder : category.sortorder || 0,
      status: category.status || 1,
      isFeatured: category.isFeatured || 0,
      videoCount: category.videoCount || 0,
      viewCount: category.viewCount || 0
    }))
    
  } catch (error) {
    console.error('Failed to load navigation categories:', error)
    ElMessage.error('加载分类失败: ' + (error as Error).message)
    categories.value = []
  } finally {
    categoriesLoading.value = false
  }
}

// 处理分类点击
const handleCategoryClick = (category: VideoCategory) => {
  console.log('点击分类:', category)
  
  // 构建分类页面路由
  let route = '/category'
  if (category.code) {
    route += `/${category.code}`
  } else if (category.id) {
    route += `?id=${category.id}`
  }
  
  // 导航到分类页面
  window.location.href = route
}

const getCategoryBackgroundStyle = (category: VideoCategory) => {
  if (category.icon && category.icon.startsWith('http')) {
    return {
      backgroundImage: `url(${category.icon})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  }
  return {}
}

initTheme()

onMounted(() => {
  loadNavigationCategories()
})
</script>

<style scoped lang="scss">
.modern-header-wrapper {
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(20px);
  background: var(--nav-bg, var(--bg-header));
  border-bottom: 1px solid var(--nav-border, rgba(255, 107, 157, 0.1));
  box-shadow: 0 4px 20px rgba(255, 107, 157, 0.1);
}

.modern-header {
  .header-main {
    .header-container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 var(--spacing-md);
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 70px;
      
      @media (max-width: 768px) {
        padding: 0 var(--spacing-sm);
        height: 60px;
      }
    }
  }
}

// 左侧区域
.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  
  .logo-section {
    .logo-link {
      text-decoration: none;
      
      .logo-icon {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        position: relative;
        
        .logo-gradient {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #FF6B9D, #9C27B0);
          border-radius: var(--radius-lg);
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            inset: 2px;
            background: white;
            border-radius: var(--radius-md);
          }
          
          &::after {
            content: 'M';
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            background: linear-gradient(135deg, #FF6B9D, #9C27B0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            z-index: 1;
          }
        }
        
        .logo-text {
          font-size: 24px;
          font-weight: 700;
          background: linear-gradient(135deg, #FF6B9D, #9C27B0);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          letter-spacing: -0.5px;
        }
      }
    }
  }
  
  .desktop-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    @media (max-width: 1024px) {
      display: none;
    }
    
    .nav-item {
      position: relative;
      
      a, .nav-link {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-lg);
        text-decoration: none;
        color: var(--nav-text-secondary, var(--color-text-secondary));
        font-weight: 500;
        transition: all var(--transition-normal);
        cursor: pointer;
        
        &:hover {
          background: rgba(255, 107, 157, 0.1);
          color: var(--color-primary);
          transform: translateY(-1px);
        }
        
        .el-icon {
          font-size: 18px;
        }
      }
      
      &.active a {
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        color: white;
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
      }
      
      &.nav-dropdown {
        .dropdown-icon {
          transition: transform var(--transition-fast);
        }
        
        &:hover .dropdown-icon {
          transform: rotate(180deg);
        }
      }
    }
  }
}

// 分类下拉菜单
.category-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 400px;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 40px rgba(255, 107, 157, 0.2);
  border: 1px solid rgba(255, 107, 157, 0.1);
  padding: var(--spacing-md);
  z-index: 1000;
  
  .dropdown-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    color: var(--nav-text-secondary, var(--color-text-secondary));
  }
  
  .category-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    
    .category-card {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all var(--transition-fast);
      
      &:hover {
        background: rgba(255, 107, 157, 0.1);
        transform: translateX(4px);
      }
      
      .category-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        border-radius: var(--radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
      
      .category-name {
        font-weight: 500;
        color: var(--nav-text, var(--color-text));
      }
    }
  }
  
  .dropdown-footer {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 107, 157, 0.1);
    
    .view-all-btn {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--color-primary);
      text-decoration: none;
      font-weight: 500;
      transition: all var(--transition-fast);
      
      &:hover {
        transform: translateX(4px);
      }
    }
  }
}

// 中间搜索区域
.header-center {
  flex: 1;
  max-width: 600px;
  margin: 0 var(--spacing-lg);
  
  @media (max-width: 768px) {
    display: none;
  }
  
  .search-container {
    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      
      .modern-search {
        flex: 1;
        
        :deep(.el-input__wrapper) {
          background: rgba(255, 255, 255, 0.9);
          border: 2px solid rgba(255, 107, 157, 0.2);
          border-radius: var(--radius-full);
          padding: 0 var(--spacing-md);
          height: 48px;
          transition: all var(--transition-normal);
          
          &:hover {
            border-color: rgba(255, 107, 157, 0.4);
            box-shadow: 0 4px 20px rgba(255, 107, 157, 0.1);
          }
          
          &.is-focus {
            border-color: var(--color-primary);
            box-shadow: 0 4px 20px rgba(255, 107, 157, 0.2);
          }
        }
        
        :deep(.el-input__inner) {
          border: none;
          background: transparent;
          font-size: 16px;
          
          &::placeholder {
            color: var(--color-text-secondary);
          }
        }
        
        :deep(.el-input__prefix) {
          .search-icon {
            color: var(--color-text-secondary);
            font-size: 18px;
          }
        }
      }
      
      .search-btn {
        position: absolute;
        right: 4px;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #FF6B9D, #9C27B0);
        border: none;
        border-radius: var(--radius-full);
        color: white;
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
        }
      }
    }
  }
}

// 右侧区域
.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  
  .region-selector {
    @media (max-width: 768px) {
      display: none;
    }
    
    .language-dropdown {
      .dropdown-trigger {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--transition-fast);
        color: var(--nav-text-secondary, var(--color-text-secondary));
        
        &:hover {
          background: rgba(255, 107, 157, 0.1);
          color: var(--color-primary);
        }
        
        .current-lang {
          font-weight: 500;
          font-size: 14px;
        }
        
        .dropdown-arrow {
          font-size: 12px;
          transition: transform var(--transition-fast);
        }
      }
    }
  }
  
  .theme-toggle {
    .theme-btn {
      width: 44px;
      height: 44px;
      border: none;
      background: rgba(255, 107, 157, 0.1);
      border-radius: var(--radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all var(--transition-normal);
      
      &:hover {
        background: rgba(255, 107, 157, 0.2);
        transform: scale(1.05);
      }
      
      .el-icon {
        font-size: 20px;
        color: var(--color-primary);
      }
    }
  }
  
  .user-section {
    .auth-buttons {
      display: flex;
      gap: var(--spacing-sm);
      
      .login-btn, .register-btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-full);
        font-weight: 500;
        cursor: pointer;
        transition: all var(--transition-normal);
        border: none;
        
        &.login-btn {
          background: transparent;
          color: var(--color-primary);
          border: 2px solid var(--color-primary);
          
          &:hover {
            background: var(--color-primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
          }
        }
        
        &.register-btn {
          background: linear-gradient(135deg, #FF6B9D, #9C27B0);
          color: white;
          
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.4);
          }
        }
      }
    }
    
    .user-menu {
      .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        overflow: hidden;
        cursor: pointer;
        border: 2px solid var(--color-primary);
        transition: all var(--transition-fast);
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
  
  .mobile-menu-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: rgba(255, 107, 157, 0.1);
    border-radius: var(--radius-lg);
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    
    @media (max-width: 1024px) {
      display: flex;
    }
    
    &:hover {
      background: rgba(255, 107, 157, 0.2);
      transform: scale(1.05);
    }
    
    .el-icon {
      font-size: 20px;
      color: var(--color-primary);
    }
  }
}

// 动画效果
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all var(--transition-normal);
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.theme-icon-enter-active,
.theme-icon-leave-active {
  transition: all var(--transition-fast);
}

.theme-icon-enter-from,
.theme-icon-leave-to {
  opacity: 0;
  transform: rotate(90deg) scale(0.8);
}

.menu-icon-enter-active,
.menu-icon-leave-active {
  transition: all var(--transition-fast);
}

.menu-icon-enter-from,
.menu-icon-leave-to {
  opacity: 0;
  transform: rotate(90deg);
}

        .search-input {
          flex: 1;
        }
      }

      .right-section {
        display: flex;
        align-items: center;
        gap: 20px;

        .theme-switch {
          margin-right: 10px;
        }

        .user-actions {
          display: flex;
          gap: 10px;
        }
      }
    }
  }
}

.header-menu {
  margin: 70px auto 10px auto;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.08);
  .main-menu {
    width: 1280px;
    margin: 0 auto;
    border: none;
    background-color: transparent;
    height: 40px;
    line-height: 40px;

    :deep(.el-menu-item) {
      padding: 0 16px;
      height: 40px;
      line-height: 40px;
      background-color: transparent !important;
      display: flex;
      align-items: center;
      font-size: 14px;

      .el-icon {
        margin-right: 4px;
        height: 14px;
        width: 14px;
      }

      &:hover {
        background-color: #f5f7fa !important;
      }
    }

    // 分类下拉菜单特殊样式
    .category-dropdown {
      :deep(.el-menu-item.category-menu-item) {
        position: relative;
        
        .dropdown-arrow {
          margin-left: 4px;
          transition: transform 0.3s ease;
        }
        
        &:hover .dropdown-arrow {
          transform: rotate(180deg);
        }
      }
    }
  }

  &.is-hidden {
    display: none;
  }
}

.hamburger-menu {
  display: none;
  @media (max-width: 768px) {
    display: block;
  }
}

// 分类下拉菜单样式
.category-dropdown-menu {
  min-width: 800px;
  max-width: 900px;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid #e4e7ed;
  background: #fff;

  .loading-state,
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: #909399;
    font-size: 14px;
    
    .el-icon {
      margin-right: 8px;
    }
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);

    :deep(.el-dropdown-menu__item) {
      padding: 0;
    }
    .category-item {
      padding: 0;
      margin: 0;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;
      height: 120px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

        .category-content {
          .category-overlay {
            background: rgba(0, 0, 0, 0.2);
          }
        }
      }

      .category-content {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
  
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-align: center;
        overflow: hidden;

        .category-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.3);
          transition: all 0.3s ease;
          z-index: 1;
        }

        .category-info {
          position: relative;
          z-index: 2;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .category-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            backdrop-filter: blur(10px);

            .category-icon-default {
              font-size: 18px;
              color: white;
            }
          }

          .category-name {
            font-size: 14px;
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 140px;
          }
        }
      }
    }
  }

  .dropdown-footer {
    border-top: 1px solid #ebeef5;
    padding-top: 16px;
    text-align: center;

    .el-button {
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
    }
  }
}

:deep(.el-dropdown-item) {
  padding: 0;
  
  &:hover {
    background-color: transparent;
  }
}

// 深色模式样式
:deep([data-theme='dark']) {
  .header {
    background-color: #1a1a1a;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.25);
    
    .header-top {
      background-color: #1a1a1a;
      border-bottom-color: #2c2c2c;
    }
    
    .el-input {
      --el-input-bg-color: #2c2c2c;
      --el-input-text-color: #fff;
      --el-input-border-color: #4c4c4c;
    }
    
    .el-button:not(.el-button--primary) {
      --el-button-bg-color: #2c2c2c;
      --el-button-text-color: #fff;
      --el-button-border-color: #4c4c4c;
    }
  }

  .header-menu {
    background-color: #1a1a1a;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.15);
  }

  .category-dropdown-menu {
    background-color: #1a1a1a;
    border-color: #4c4c4c;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3);

    .loading-state,
    .empty-state {
      color: #a0aec0;
    }

    .category-item {
      .category-content {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        
        .category-overlay {
          background: rgba(0, 0, 0, 0.4);
        }

        .category-info {
          .category-icon {
            background: rgba(255, 255, 255, 0.15);
          }

          .category-name {
            color: #e2e8f0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }
        }
      }

      &:hover {
        .category-content {
          .category-overlay {
            background: rgba(0, 0, 0, 0.2);
          }
        }
      }
    }

    .dropdown-footer {
      border-top-color: #4c4c4c;
    }
  }
}
</style>