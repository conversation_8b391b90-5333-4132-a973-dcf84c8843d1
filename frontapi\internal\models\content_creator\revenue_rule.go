package content_creator

import (
	"frontapi/internal/models"
)

type RevenueRule struct {
	models.BaseModel
	ContentType   string  `gorm:"column:content_type;type:string;size:20;not null;comment:内容类型：video-视频，book-电子书，comic-漫画，picture-图片" json:"content_type"` // 内容类型
	CreatorRatio  float64 `gorm:"column:creator_ratio;type:decimal(5,2);comment:创作者分成比例" json:"creator_ratio"`                                             // 创作者分成比例
	PlatformRatio float64 `gorm:"column:platform_ratio;type:decimal(5,2);comment:平台分成比例" json:"platform_ratio"`                                            // 平台分成比例
	ReferrerRatio float64 `gorm:"column:referrer_ratio;type:decimal(5,2);comment:推荐人分成比例" json:"referrer_ratio"`                                           // 推荐人分成比例
	MinAmount     float64 `gorm:"column:min_amount;type:decimal(10,2);comment:最低提现金额" json:"min_amount"`                                                   // 最低提现金额
	Description   string  `gorm:"column:description;type:string;size:255;comment:规则描述" json:"description"`                                                 // 规则描述
	Status        int8    `gorm:"column:status;type:tinyint;comment:状态：0-禁用，1-启用" json:"status"`                                                           // 状态
}

func (RevenueRule) TableName() string {
	return "ly_revenue_rules"
}
