<template>
  <el-dialog
    :model-value="visible"
    :title="type === 'edit' ? '编辑视频' : '添加视频'"
    width="800px"
    :close-on-click-modal="!isUploading"
    :close-on-press-escape="!isUploading"
    :show-close="!isUploading"
    @update:model-value="$emit('update:visible', $event)"
    class="video-form-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="视频标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入视频标题" maxlength="255" show-word-limit></el-input>
      </el-form-item>

      <el-form-item label="分类" prop="categoryOrChannel">
        <el-select 
          v-model="form.category_id" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请选择分类或输入关键词搜索" 
          :remote-method="handleSearchCategories" 
          :loading="categoriesLoading" 
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
          <template #empty>
            <div class="empty-search-result">
              <p v-if="categoriesLoading">正在搜索中...</p>
              <p v-else>暂无符合条件的分类，请尝试其他关键词</p>
            </div>
          </template>
        </el-select>
        <div class="form-tip">输入关键词可快速搜索相关分类</div>
      </el-form-item>

      <el-form-item label="频道" prop="categoryOrChannel">
        <el-select 
          v-model="form.channel_id" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请选择频道或输入关键词搜索" 
          :remote-method="handleSearchChannels" 
          :loading="channelsLoading" 
          style="width: 100%" 
          clearable
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
          <template #empty>
            <div class="empty-search-result">
              <p v-if="channelsLoading">正在搜索中...</p>
              <p v-else>暂无符合条件的频道，请尝试其他关键词</p>
            </div>
          </template>
        </el-select>
        <div class="form-tip">输入关键词可快速搜索相关频道</div>
      </el-form-item>

      <el-form-item label="所属专辑" prop="album_id">
        <el-select 
          v-model="form.album_id" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请选择专辑或输入关键词搜索" 
          :remote-method="handleSearchAlbums" 
          :loading="albumsLoading" 
          style="width: 100%" 
          clearable
        >
          <el-option
            v-for="item in albumOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
          <template #empty>
            <div class="empty-search-result">
              <p v-if="albumsLoading">正在搜索中...</p>
              <p v-else>暂无符合条件的专辑，请尝试其他关键词</p>
            </div>
          </template>
        </el-select>
        <div class="form-tip">输入关键词可快速搜索相关专辑</div>
      </el-form-item>
      
      <el-form-item label="创建人" prop="creator_id">
        <el-select 
          v-model="form.creator_id" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请选择创建人或输入关键词搜索" 
          :remote-method="handleSearchUsers" 
          :loading="usersLoading" 
          style="width: 100%" 
          clearable
        >
          <el-option
            v-for="item in userOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <div class="flex items-center">
              <el-avatar 
                v-if="item.avatar" 
                :src="item.avatar" 
                :size="24"
                class="mr-2"
              />
              <span v-else class="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full mr-2">
                <i class="el-icon-user"></i>
              </span>
              <span>{{ item.label }}</span>
            </div>
          </el-option>
          <template #empty>
            <div class="empty-search-result">
              <p v-if="usersLoading">正在搜索中...</p>
              <p v-else>暂无符合条件的用户，请尝试其他关键词</p>
            </div>
          </template>
        </el-select>
        <div class="form-tip">输入关键词可快速搜索相关创建人</div>
      </el-form-item>

      <el-form-item label="视频封面" prop="cover">
        <UrlOrFileInput
          v-model="form.cover"
          fileType="image"
          subDir="covers"
          placeholder="请上传封面或输入封面URL"
        />
        <div class="form-tip">推荐尺寸: 1920x1080像素，支持JPG、PNG格式</div>
      </el-form-item>

      <el-form-item label="视频文件" prop="url">
        <UrlOrFileInput
          v-model="form.url"
          fileType="video"
          subDir="videos"
          placeholder="请上传视频或输入视频URL"
        />
        <div class="form-tip">支持MP4、WebM格式，建议使用H.264编码</div>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入视频描述"
          maxlength="1024"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="演员/明星" prop="celebrities">
        <el-select
          v-model="form.celebrities"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入演员/明星名称"
          style="width: 100%"
          clearable
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="item in celebrityOptions"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
        <div class="form-tip">支持多选，可直接输入演员名称后回车添加</div>
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          remote
          allow-create
          :remote-method="remoteSearchTags"
          :loading="isLoadingTags"
          default-first-option
          placeholder="请输入或选择标签"
          style="width: 100%"
          clearable
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          ></el-option>
          <template #empty>
            <div class="empty-search-result">
              <p v-if="isLoadingTags">正在搜索中...</p>
              <p v-else>暂无符合条件的标签，可直接输入创建新标签</p>
            </div>
          </template>
        </el-select>
        <div class="form-tip">支持多选，可输入关键词搜索或直接输入新标签</div>
      </el-form-item>

      <el-form-item label="是否付费" prop="is_paid">
        <el-radio-group v-model="form.is_paid">
          <el-radio :label="0">免费</el-radio>
          <el-radio :label="1">付费</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.is_paid === 1" label="价格" prop="price">
        <el-input-number
          v-model="form.price"
          :precision="2"
          :min="0"
          :max="999.99"
          class="w-32"
        ></el-input-number>
        <span class="ml-2">元</span>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="0">待审核</el-radio>
          <el-radio :label="1">已下架</el-radio>
          <el-radio :label="2">已发布</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)" :disabled="isUploading">取消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="isUploading || submitting" :loading="submitting">
          {{ submitting ? '提交中...' : '提交' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import { searchTags } from '@/service/api/system/tags';
import { createVideo, searchAlbums, searchChannels, searchUsers, searchVideoCategories, updateVideo } from '@/service/api/videos/videos';
import { VideoItem } from '@/types/videos';
import { ElMessage, FormInstance } from 'element-plus';
import { onMounted, ref, watch } from 'vue';

// Props 定义
interface Props {
  visible: boolean;
  type: 'add' | 'edit';
  videoData?: VideoItem | null;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'add',
  videoData: null
});

// Emits 定义
const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref<FormInstance>();
const isUploading = ref(false);
const submitting = ref(false);

// 表单数据
const form = ref({
  id: '',
  title: '',
  description: '',
  cover: '',
  url: '',
  category_id: '',
  channel_id: '',
  album_id: '',
  creator_id: '',
  celebrities: [] as string[],
  tags: [] as string[],
  is_paid: 0,
  price: 0,
  status: 0, // 默认为待审核状态
});

// 加载状态
const categoriesLoading = ref(false);
const channelsLoading = ref(false);
const albumsLoading = ref(false);
const isLoadingTags = ref(false);
const usersLoading = ref(false);

// 选项数据
const categoryOptions = ref<{value: string, label: string}[]>([]);
const channelOptions = ref<{value: string, label: string}[]>([]);
const albumOptions = ref<{value: string, label: string}[]>([]);
const celebrityOptions = ref<string[]>([
  '张三', '李四', '王五', '赵六', '周杰伦', '刘德华', '成龙', '李连杰'
]);
const tagOptions = ref<{id: string, name: string}[]>([]);
const userOptions = ref<{value: string, label: string, avatar?: string}[]>([]);

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入视频标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度为2-100个字符', trigger: 'blur' }
  ],
  categoryOrChannel: [
    { 
      validator: (_rule: any, _value: any, callback: Function) => {
        if (!form.value.category_id && !form.value.channel_id) {
          callback(new Error('请至少选择一个分类或频道'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  url: [
    { required: true, message: '请上传视频或输入视频URL', trigger: 'blur' }
  ],
  cover: [
    { required: true, message: '请上传封面或输入封面URL', trigger: 'blur' }
  ],
  price: [
    { 
      validator: (_rule: any, value: number, callback: Function) => {
        if (form.value.is_paid === 1 && (!value || value <= 0)) {
          callback(new Error('付费视频的价格必须大于0'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
};

// 初始化
onMounted(() => {
  loadDefaultOptions();
  
  // 如果是编辑模式，设置表单数据
  if (props.type === 'edit' && props.videoData) {
    setFormData(props.videoData);
  }
});

// 监视props变化
watch(() => props.videoData, (newVal) => {
  if (newVal && props.type === 'edit') {
    setFormData(newVal);
  } else if (props.type === 'add') {
    resetForm();
  }
}, { deep: true });

// 设置表单数据
const setFormData = (video: VideoItem) => {
  form.value = {
    id: video.id || '',
    title: video.title || '',
    description: video.description || '',
    cover: video.cover || '',
    url: video.url || '',
    category_id: video.category_id || '',
    channel_id: video.channel_id || '',
    album_id: (video as any).album_id || '',
    creator_id: video.creator_id || '',
    celebrities: (video as any).celebrities || [],
    tags: Array.isArray(video.tags) ? video.tags : [],
    is_paid: (video as any).is_paid || 0,
    price: (video as any).price || 0,
    status: video.status,
  };
};

// 重置表单
const resetForm = () => {
  form.value = {
    id: '',
    title: '',
    description: '',
    cover: '',
    url: '',
    category_id: '',
    channel_id: '',
    album_id: '',
    creator_id: '',
    celebrities: [],
    tags: [],
    is_paid: 0,
    price: 0,
    status: 0, // 默认为待审核状态
  };
};

// 加载默认选项
const loadDefaultOptions = async () => {
  try {
    // 加载默认分类
    const categoryResult = await searchVideoCategories({
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    });
    
    if (categoryResult?.data?.list) {
      categoryOptions.value = categoryResult.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }

    // 加载默认频道
    const channelResult = await searchChannels({
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    });
    
    if (channelResult?.data?.list) {
      channelOptions.value = channelResult.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }

    // 加载默认标签
    const tagResult = await searchTags({
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    });
    
    if (tagResult?.data?.list) {
      tagOptions.value = tagResult.data.list;
    }

    // 加载默认专辑
    const albumResult = await searchAlbums({
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    });
    
    if (albumResult?.data?.list) {
      albumOptions.value = albumResult.data.list.map((item: any) => ({
        value: item.id,
        label: item.title
      }));
    }

    // 加载默认创建人
    const userResult = await searchUsers({
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    });
    
    if (userResult?.data?.list) {
      userOptions.value = userResult.data.list.map((item: any) => ({
        value: item.id,
        label: item.username || item.nickname || item.id,
        avatar: item.avatar
      }));
    }
  } catch (error) {
    console.error('加载默认选项失败:', error);
  }
};

// 搜索分类
const handleSearchCategories = async (query: string) => {
  if (!query) return;
  categoriesLoading.value = true;
  try {
    const result = await searchVideoCategories({
      page: { pageNo: 1, pageSize: 20 },
      data: { keyword: query, status: 1 }
    });
    
    if (result?.data?.list) {
      categoryOptions.value = result.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }
  } catch (error) {
    console.error('搜索分类失败:', error);
    ElMessage.error('搜索分类失败');
  } finally {
    categoriesLoading.value = false;
  }
};

// 搜索频道
const handleSearchChannels = async (query: string) => {
  if (!query) return;
  channelsLoading.value = true;
  try {
    const result = await searchChannels({
      page: { pageNo: 1, pageSize: 20 },
      data: { keyword: query, status: 1 }
    });
    
    if (result?.data?.list) {
      channelOptions.value = result.data.list.map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }
  } catch (error) {
    console.error('搜索频道失败:', error);
    ElMessage.error('搜索频道失败');
  } finally {
    channelsLoading.value = false;
  }
};

// 搜索创建人
const handleSearchUsers = async (query: string) => {
  if (!query) return;
  usersLoading.value = true;
  try {
    const result = await searchUsers({
      page: { pageNo: 1, pageSize: 20 },
      data: { keyword: query, status: 1 }
    });
    
    if (result?.data?.list) {
      userOptions.value = result.data.list.map((item: any) => ({
        value: item.id,
        label: item.username || item.nickname || item.id,
        avatar: item.avatar
      }));
    }
  } catch (error) {
    console.error('搜索创建人失败:', error);
    ElMessage.error('搜索创建人失败');
  } finally {
    usersLoading.value = false;
  }
};

// 搜索专辑
const handleSearchAlbums = async (query: string) => {
  if (!query) return;
  albumsLoading.value = true;
  try {
    const result = await searchAlbums({
      page: { pageNo: 1, pageSize: 20 },
      data: { keyword: query, status: 1 }
    });
    
    if (result?.data?.list) {
      albumOptions.value = result.data.list.map((item: any) => ({
        value: item.id,
        label: item.title
      }));
    }
  } catch (error) {
    console.error('搜索专辑失败:', error);
    ElMessage.error('搜索专辑失败');
  } finally {
    albumsLoading.value = false;
  }
};

// 远程搜索标签
const remoteSearchTags = async (query: string) => {
  if (query.length < 1) return;
  isLoadingTags.value = true;
  try {
    const result = await searchTags({
      page: { pageNo: 1, pageSize: 20 },
      data: { keyword: query }
    });
    
    if (result?.data?.list) {
      tagOptions.value = result.data.list;
    }
  } catch (error) {
    console.error('搜索标签失败:', error);
  } finally {
    isLoadingTags.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    let res;

    // 准备提交的数据
    const videoData = {
      ...form.value,
      view_count: props.videoData?.view_count || 0,
      like_count: props.videoData?.like_count || 0,
      comment_count: props.videoData?.comment_count || 0,
      share_count: props.videoData?.share_count || 0,
    };

    if (props.type === 'edit' && form.value.id) {
      // 更新视频
      res = await updateVideo(form.value.id, videoData);
    } else {
      // 创建视频
      res = await createVideo(videoData);
    }

    const { response, data } = res as any;
    console.log("视频更新",response,data)
    if (response?.data?.code === 2000) {
      ElMessage.success(props.type === 'edit' ? '视频更新成功' : '视频添加成功');
      emit('success');
      emit('update:visible', false);
      if (props.type === 'add') {
        resetForm();
      }
    } else {
      ElMessage.error(response?.data?.message || '操作失败');
    }
  } catch (error) {
    console.error('表单提交错误:', error);
    ElMessage.error('表单验证失败，请检查输入');
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped lang="scss">
.video-form-dialog {
  .form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  :deep(.el-upload) {
    width: 100%;
  }
  
  :deep(.el-form-item__content) {
    flex-wrap: wrap;
  }
}
</style> 