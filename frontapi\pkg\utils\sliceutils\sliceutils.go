// 此包包含用于处理和操作切片的实用函数。
// 它从JavaScript和Python中汲取灵感，并以Go泛型为基础。

package sliceutils

import (
	"golang.org/x/exp/constraints"
)

// Filter - 给定类型T的切片，对切片中的每个元素执行给定的谓词函数。
// 谓词函数接收当前元素、当前索引和切片本身作为函数参数。
// 如果谓词返回true，则该值包含在结果中，否则被过滤掉。
func Filter[T any](slice []T, predicate func(value T, index int, slice []T) bool) (filtered []T) {
	for i, el := range slice {
		if ok := predicate(el, i, slice); ok {
			filtered = append(filtered, el)
		}
	}
	return filtered
}

// ForEach - 给定类型T的切片，对切片中的每个元素执行传入的函数。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func ForEach[T any](slice []T, function func(value T, index int, slice []T)) {
	for i, el := range slice {
		function(el, i, slice)
	}
}

// Map - 给定类型T的切片，对切片中的每个元素执行传入的映射函数，返回类型R的切片。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func Map[T any, R any](slice []T, mapper func(value T, index int, slice []T) R) (mapped []R) {
	if len(slice) > 0 {
		mapped = make([]R, len(slice))
		for i, el := range slice {
			mapped[i] = mapper(el, i, slice)
		}
	}
	return mapped
}

// Reduce - 给定类型T的切片，对切片中的每个元素执行传入的归约函数，返回类型R的结果。
// 该函数接收累加器、当前元素、当前索引和切片本身作为函数参数。
// reduce的第三个参数是要使用的类型R的初始值。
func Reduce[T any, R any](
	slice []T,
	reducer func(acc R, value T, index int, slice []T) R,
	initial R,
) R {
	acc := initial
	for i, el := range slice {
		acc = reducer(acc, el, i, slice)
	}
	return acc
}

// Find - 给定类型T的切片，对切片中的每个元素执行传入的谓词函数。
// 如果谓词返回true，则返回指向该元素的指针。如果未找到元素，则返回nil。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func Find[T any](slice []T, predicate func(value T, index int, slice []T) bool) *T {
	for i, el := range slice {
		if ok := predicate(el, i, slice); ok {
			return &el
		}
	}
	return nil
}

// FindIndex - 给定类型T的切片，对切片中的每个元素执行传入的谓词函数。
// 如果谓词返回true，则返回该元素的索引。如果未找到元素，则返回-1。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func FindIndex[T any](slice []T, predicate func(value T, index int, slice []T) bool) int {
	for i, el := range slice {
		if ok := predicate(el, i, slice); ok {
			return i
		}
	}
	return -1
}

// FindIndexOf - 给定类型T的切片和类型T的值，返回等于该值的元素的第一个索引。
// 如果未找到元素，则返回-1。
func FindIndexOf[T comparable](slice []T, value T) int {
	for i, el := range slice {
		if el == value {
			return i
		}
	}
	return -1
}

// FindLastIndex - 给定类型T的切片，从切片末尾开始对每个元素执行传入的谓词函数。
// 如果未找到元素，则返回-1。该函数接收当前元素、当前索引和切片本身作为函数参数。
func FindLastIndex[T any](slice []T, predicate func(value T, index int, slice []T) bool) int {
	for i := len(slice) - 1; i >= 0; i-- {
		el := slice[i]
		if ok := predicate(el, i, slice); ok {
			return i
		}
	}
	return -1
}

// FindLastIndexOf - 给定类型T的切片和类型T的值，返回匹配给定值的最后一个索引。
// 如果未找到元素，则返回-1。
func FindLastIndexOf[T comparable](slice []T, value T) int {
	for i := len(slice) - 1; i > 0; i-- {
		el := slice[i]
		if el == value {
			return i
		}
	}
	return -1
}

// FindIndexes - 给定类型T的切片，对切片中的每个元素执行传入的谓词函数。
// 返回包含谓词返回true的所有元素索引的切片。如果未找到元素，则返回nil int切片。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func FindIndexes[T any](slice []T, predicate func(value T, index int, slice []T) bool) []int {
	var indexes []int
	for i, el := range slice {
		if ok := predicate(el, i, slice); ok {
			indexes = append(indexes, i)
		}
	}
	return indexes
}

// FindIndexesOf - 给定类型T的切片和类型T的值，返回包含匹配给定值的所有索引的切片。
// 如果未找到元素，则返回nil int切片。
func FindIndexesOf[T comparable](slice []T, value T) []int {
	var indexes []int
	for i, el := range slice {
		if el == value {
			indexes = append(indexes, i)
		}
	}
	return indexes
}

// Includes - 给定类型T的切片和类型T的值，确定切片是否包含该值。
// 注意：T仅限于可比较类型，比较使用相等运算符确定。
func Includes[T comparable](slice []T, value T) bool {
	for _, el := range slice {
		if el == value {
			return true
		}
	}
	return false
}

// Some - 给定类型T的切片，对切片的每个元素执行给定的谓词。
// 如果谓词对任何元素返回true，则返回true，否则返回false。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func Some[T any](slice []T, predicate func(value T, index int, slice []T) bool) bool {
	for i, el := range slice {
		if ok := predicate(el, i, slice); ok {
			return true
		}
	}
	return false
}

// Every - 给定类型T的切片，对切片的每个元素执行给定的谓词。
// 如果谓词对所有元素都返回true，则返回true，否则返回false。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func Every[T any](slice []T, predicate func(value T, index int, slice []T) bool) bool {
	for i, el := range slice {
		if ok := predicate(el, i, slice); !ok {
			return false
		}
	}
	return true
}

// Merge - 接收类型T的切片并将它们合并为单个类型T的切片。
// 注意：元素按照它们在切片中的顺序合并，
// 即首先是第一个切片的元素，然后是第二个切片的元素，依此类推。
func Merge[T any](slices ...[]T) (mergedSlice []T) {
	if len(slices) > 0 {
		mergedSliceCap := 0

		for _, slice := range slices {
			mergedSliceCap += len(slice)
		}

		if mergedSliceCap > 0 {
			mergedSlice = make([]T, 0, mergedSliceCap)

			for _, slice := range slices {
				mergedSlice = append(mergedSlice, slice...)
			}
		}
	}
	return mergedSlice
}

// Sum - 接收类型T的切片并返回数字总和的值T。
// 注意：T被约束为数字类型。
func Sum[T constraints.Complex | constraints.Integer | constraints.Float](slice []T) (result T) {
	for _, el := range slice {
		result += el
	}
	return result
}

// Remove - 接收类型T的切片和一个索引，移除给定索引处的元素。
// 注意：此函数不修改输入切片。
func Remove[T any](slice []T, i int) []T {
	if len(slice) == 0 || i > len(slice)-1 {
		return slice
	}
	copied := Copy(slice)
	if i == 0 {
		return copied[1:]
	}
	if i != len(copied)-1 {
		return append(copied[:i], copied[i+1:]...)
	}
	return copied[:i]
}

// Insert - 接收类型T的切片、一个索引和一个值。
// 该值插入到给定索引处。如果该索引处存在现有值，则将其移动到下一个索引。
// 注意：此函数不修改输入切片。
func Insert[T any](slice []T, i int, value T) []T {
	if len(slice) == i {
		return append(slice, value)
	}
	slice = append(slice[:i+1], slice[i:]...)
	slice[i] = value
	return slice
}

// Copy - 接收类型T的切片并复制它。
func Copy[T any](slice []T) []T {
	duplicate := make([]T, len(slice), cap(slice))
	copy(duplicate, slice)
	return duplicate
}

// Intersection - 接收可变数量的类型T切片，返回包含所有切片中都存在的值的类型T切片。
// 例如，给定[]int{1, 2, 3}，[]int{1, 7, 3}，交集将是[]int{1, 3}。
func Intersection[T comparable](slices ...[]T) []T {
	possibleIntersections := map[T]int{}
	for i, slice := range slices {
		for _, el := range slice {
			if i == 0 {
				possibleIntersections[el] = 0
			} else if _, elementExists := possibleIntersections[el]; elementExists {
				possibleIntersections[el] = i
			}
		}
	}

	intersected := make([]T, 0)
	for _, el := range slices[0] {
		if lastVisitorIndex, exists := possibleIntersections[el]; exists &&
			lastVisitorIndex == len(slices)-1 {
			intersected = append(intersected, el)
			delete(possibleIntersections, el)
		}
	}

	return intersected
}

// Difference - 接收可变数量的类型T切片，返回包含切片之间不同元素的类型T切片。
// 例如，给定[]int{1, 2, 3}，[]int{2, 3, 4}，[]int{3, 4, 5}，差集将是[]int{1, 5}。
func Difference[T comparable](slices ...[]T) []T {
	possibleDifferences := map[T]int{}
	nonDifferentElements := map[T]int{}

	for i, slice := range slices {
		for _, el := range slice {
			if lastVisitorIndex, elementExists := possibleDifferences[el]; elementExists &&
				lastVisitorIndex != i {
				nonDifferentElements[el] = i
			} else if !elementExists {
				possibleDifferences[el] = i
			}
		}
	}

	differentElements := make([]T, 0)

	for _, slice := range slices {
		for _, el := range slice {
			if _, exists := nonDifferentElements[el]; !exists {
				differentElements = append(differentElements, el)
			}
		}
	}

	return differentElements
}

// Union - 接收可变数量的类型T切片，返回包含不同切片中唯一元素的类型T切片。
// 例如，给定[]int{1, 2, 3}，[]int{2, 3, 4}，[]int{3, 4, 5}，并集将是[]int{1, 2, 3, 4, 5}。
func Union[T comparable](slices ...[]T) []T {
	return Unique(Merge(slices...))
}

// Reverse - 接收类型T的切片，返回元素顺序相反的类型T切片。
func Reverse[T any](slice []T) []T {
	result := make([]T, len(slice))

	itemCount := len(slice)
	middle := itemCount / 2
	result[middle] = slice[middle]

	for i := 0; i < middle; i++ {
		mirrorIdx := itemCount - i - 1
		result[i], result[mirrorIdx] = slice[mirrorIdx], slice[i]
	}
	return result
}

// Unique - 接收类型T的切片，返回包含所有唯一元素的类型T切片。
func Unique[T comparable](slice []T) []T {
	unique := make([]T, 0)
	visited := map[T]bool{}

	for _, value := range slice {
		if exists := visited[value]; !exists {
			unique = append(unique, value)
			visited[value] = true
		}
	}
	return unique
}

// Chunk - 接收类型T的切片和大小N，返回大小为N的类型T切片的切片。
func Chunk[T any](input []T, size int) [][]T {
	var chunks [][]T

	for i := 0; i < len(input); i += size {
		end := i + size
		if end > len(input) {
			end = len(input)
		}
		chunks = append(chunks, input[i:end])
	}
	return chunks
}

// Pluck - 接收类型I的切片和一个字段的getter函数，
// 返回包含切片中每个项目的请求字段值的切片。
func Pluck[I any, O any](input []I, getter func(I) *O) []O {
	var output []O

	for _, item := range input {
		field := getter(item)

		if field != nil {
			output = append(output, *field)
		}
	}

	return output
}

// Flatten - 接收类型I的切片的切片，将其展平为类型I的切片。
func Flatten[I any](input [][]I) (output []I) {
	if len(input) > 0 {
		var outputSize int

		for _, item := range input {
			outputSize += len(item)
		}

		if outputSize > 0 {
			output = make([]I, 0, outputSize)

			for _, item := range input {
				output = append(output, item...)
			}
		}
	}
	return output
}

// EnsureUniqueAndAppend - 如果项目不存在，则将其追加到切片中。
func EnsureUniqueAndAppend[T comparable](slice []T, item T) []T {
	if Includes(slice, item) {
		return slice
	}

	// 项目不存在，追加它
	return append(slice, item)
}

// FlatMap - 给定类型T的切片，对切片中的每个元素执行传入的切片映射函数，
// 返回包含所有映射切片中所有元素的展平切片。
// 该函数接收当前元素、当前索引和切片本身作为函数参数。
func FlatMap[T any, R any](slice []T, mapper func(value T, index int, slice []T) []R) []R {
	return Flatten(Map(slice, mapper))
}

// ToMap - 给定类型T的切片和一个键提取函数，返回以键为索引的map。
// 键提取函数接收当前元素、当前索引和切片本身作为函数参数，返回类型K的键。
// 如果多个元素产生相同的键，后面的元素将覆盖前面的元素。
func ToMap[T any, K comparable](slice []T, keyExtractor func(value T, index int, slice []T) K) map[K]T {
	result := make(map[K]T, len(slice))
	for i, el := range slice {
		key := keyExtractor(el, i, slice)
		result[key] = el
	}
	return result
}

// ToMapWithValue - 给定类型T的切片、键提取函数和值提取函数，返回以键为索引、值为指定类型的map。
// 键提取函数和值提取函数都接收当前元素、当前索引和切片本身作为函数参数。
// 如果多个元素产生相同的键，后面的元素将覆盖前面的元素。
func ToMapWithValue[T any, K comparable, V any](slice []T, keyExtractor func(value T, index int, slice []T) K, valueExtractor func(value T, index int, slice []T) V) map[K]V {
	result := make(map[K]V, len(slice))
	for i, el := range slice {
		key := keyExtractor(el, i, slice)
		value := valueExtractor(el, i, slice)
		result[key] = value
	}
	return result
}
