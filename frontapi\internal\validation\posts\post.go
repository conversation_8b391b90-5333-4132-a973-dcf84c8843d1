package posts

// PostListRequest 帖子列表请求验证模型
type PostListRequest struct {
	Page       int      `json:"page" validate:"min:1"`
	PageSize   int      `json:"pageSize" validate:"min:1|max:100"`
	CategoryID uint     `json:"categoryId" validate:"min:1"`
	UserID     uint     `json:"userId" validate:"min:1"`
	Tags       []string `json:"tags"`
	SortBy     string   `json:"sortBy" validate:"in:latest,popular,comment,hot"`
	IsTop      bool     `json:"isTop"`
	IsEssence  bool     `json:"isEssence"`
	Keyword    string   `json:"keyword" validate:"maxLen:50"`
}

// CreatePostRequest 创建帖子请求
type CreatePostRequest struct {
	Title       string `json:"title" binding:"required"`
	Content     string `json:"content" binding:"required"`
	AuthorID    string `json:"author_id" binding:"required"`
	CategoryID  string `json:"category_id"`
	Tags        string `json:"tags"`
	IsPublished bool   `json:"is_published"`
}

// UpdatePostRequest 更新帖子请求
type UpdatePostRequest struct {
	Title       *string `json:"title"`
	Content     *string `json:"content"`
	CategoryID  *string `json:"category_id"`
	Tags        *string `json:"tags"`
	IsPublished *bool   `json:"is_published"`
}
