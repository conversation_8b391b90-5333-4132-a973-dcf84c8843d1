# 评论组件使用示例

本文档提供了评论组件在各种实际场景中的详细使用示例。

## 🎯 基础使用示例

### 1. 最简单的使用方式

```vue
<template>
  <div class="simple-example">
    <h2>文章标题</h2>
    <p>文章内容...</p>
    
    <!-- 最简单的评论组件 -->
    <CommentBox
      target-id="article-123"
      target-type="post"
    />
  </div>
</template>

<script setup>
import CommentBox from '@/shared/components/social/comments/Index.vue'
</script>
```

### 2. 带事件处理的基础用法

```vue
<template>
  <div class="basic-example">
    <div class="content">
      <h2>{{ post.title }}</h2>
      <p>{{ post.content }}</p>
    </div>
    
    <CommentBox
      :target-id="post.id"
      target-type="post"
      @comment-added="handleNewComment"
      @comment-liked="handleCommentLike"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const post = ref({
  id: 'post-123',
  title: '精彩文章标题',
  content: '这是一篇很有趣的文章内容...',
  commentCount: 0
})

const handleNewComment = (comment) => {
  console.log('新评论:', comment)
  post.value.commentCount++
  // 可以在这里更新UI或发送通知
}

const handleCommentLike = (commentId, liked) => {
  console.log(`评论 ${commentId} ${liked ? '被点赞' : '取消点赞'}`)
}
</script>
```

## 🎨 不同显示模式示例

### 1. 内嵌模式 (Inline Mode)

```vue
<template>
  <div class="inline-example">
    <article class="article">
      <header>
        <h1>{{ article.title }}</h1>
        <div class="meta">
          <span>作者: {{ article.author }}</span>
          <span>发布时间: {{ article.publishTime }}</span>
        </div>
      </header>
      
      <div class="content" v-html="article.content"></div>
      
      <!-- 内嵌评论区域 -->
      <section class="comments-section">
        <h3>评论区</h3>
        <CommentBox
          :target-id="article.id"
          target-type="post"
          mode="inline"
          :support-image="true"
          :support-video="false"
          placeholder="分享你的想法..."
          :max-comments="20"
        />
      </section>
    </article>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const article = ref({
  id: 'article-456',
  title: '深度解析Vue 3组合式API',
  author: '技术专家',
  publishTime: '2024-01-15',
  content: '<p>Vue 3的组合式API为我们带来了...</p>'
})
</script>

<style scoped>
.article {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.meta {
  display: flex;
  gap: 20px;
  color: #666;
  margin-bottom: 20px;
}

.comments-section {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}
</style>
```

### 2. 紧凑模式 (Compact Mode)

```vue
<template>
  <div class="compact-example">
    <div class="post-list">
      <div v-for="post in posts" :key="post.id" class="post-card">
        <div class="post-header">
          <img :src="post.author.avatar" :alt="post.author.name" class="avatar" />
          <div class="author-info">
            <h4>{{ post.author.name }}</h4>
            <span class="time">{{ formatTime(post.createdAt) }}</span>
          </div>
        </div>
        
        <div class="post-content">
          <p>{{ post.content }}</p>
          <div v-if="post.images" class="images">
            <img v-for="img in post.images" :key="img" :src="img" />
          </div>
        </div>
        
        <div class="post-actions">
          <button @click="toggleLike(post)" class="action-btn">
            <i :class="['icon-heart', { liked: post.isLiked }]"></i>
            {{ post.likeCount }}
          </button>
          <button class="action-btn">
            <i class="icon-share"></i>
            分享
          </button>
        </div>
        
        <!-- 紧凑模式评论 -->
        <CommentBox
          :target-id="post.id"
          target-type="post"
          mode="compact"
          :support-video="false"
          placeholder="说点什么..."
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const posts = ref([
  {
    id: 'post-1',
    content: '今天天气真好，分享一些美丽的风景！',
    images: ['/images/landscape1.jpg', '/images/landscape2.jpg'],
    author: {
      name: '摄影爱好者',
      avatar: '/avatars/user1.jpg'
    },
    likeCount: 15,
    isLiked: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 30)
  },
  // 更多帖子...
])

const toggleLike = (post) => {
  post.isLiked = !post.isLiked
  post.likeCount += post.isLiked ? 1 : -1
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return `${Math.floor(minutes / 1440)}天前`
}
</script>

<style scoped>
.post-list {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.post-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.post-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 12px;
}

.author-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
}

.time {
  font-size: 12px;
  color: #666;
}

.post-content {
  padding: 0 20px 16px;
}

.images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 12px;
}

.images img {
  width: 100%;
  border-radius: 8px;
}

.post-actions {
  display: flex;
  gap: 20px;
  padding: 12px 20px;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  cursor: pointer;
  font-size: 14px;
}

.icon-heart.liked {
  color: #ff4757;
}
</style>
```

### 3. 对话框模式 (Dialog Mode)

```vue
<template>
  <div class="dialog-example">
    <div class="video-player">
      <video :src="video.url" :poster="video.poster" controls></video>
      
      <div class="video-info">
        <h2>{{ video.title }}</h2>
        <div class="video-meta">
          <span>{{ video.viewCount }} 次观看</span>
          <span>{{ video.likeCount }} 个赞</span>
          <button @click="showComments = true" class="comment-btn">
            <i class="icon-comment"></i>
            {{ video.commentCount }} 条评论
          </button>
        </div>
        <p>{{ video.description }}</p>
      </div>
    </div>
    
    <!-- 对话框模式评论 -->
    <CommentBox
      v-if="showComments"
      :target-id="video.id"
      target-type="video"
      mode="dialog"
      :support-image="true"
      :support-video="false"
      placeholder="说说你对这个视频的看法..."
      @dialog-closed="showComments = false"
      @comment-added="handleCommentAdded"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const showComments = ref(false)

const video = ref({
  id: 'video-789',
  title: '精彩视频：Vue 3 实战教程',
  url: '/videos/vue3-tutorial.mp4',
  poster: '/images/video-poster.jpg',
  viewCount: 1250,
  likeCount: 89,
  commentCount: 23,
  description: '这是一个关于Vue 3实战开发的详细教程...'
})

const handleCommentAdded = (comment) => {
  video.value.commentCount++
  console.log('视频收到新评论:', comment)
}
</script>

<style scoped>
.video-player {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

video {
  width: 100%;
  border-radius: 12px;
}

.video-info {
  margin-top: 20px;
}

.video-meta {
  display: flex;
  gap: 20px;
  align-items: center;
  margin: 10px 0;
  color: #666;
}

.comment-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
```

## 🎨 主题和样式定制示例

### 1. 暗色主题

```vue
<template>
  <div class="dark-theme-example" data-theme="dark">
    <div class="content">
      <h2>暗色主题文章</h2>
      <p>这是一篇使用暗色主题的文章...</p>
    </div>
    
    <CommentBox
      target-id="dark-post-123"
      target-type="post"
      theme="dark"
      placeholder="在暗色模式下发表评论..."
    />
  </div>
</template>

<style scoped>
.dark-theme-example {
  background: #1a1a1a;
  color: #ffffff;
  padding: 20px;
  border-radius: 12px;
}

.content {
  margin-bottom: 30px;
}
</style>
```

### 2. 自定义样式

```vue
<template>
  <div class="custom-style-example">
    <h2>自定义样式评论</h2>
    <p>这个评论组件使用了自定义的颜色和样式</p>
    
    <CommentBox
      target-id="custom-post-456"
      target-type="post"
      class="custom-comment-box"
      border-radius="20px"
    />
  </div>
</template>

<style scoped>
.custom-comment-box {
  --comment-primary-color: #10b981;
  --comment-border-radius: 20px;
  --comment-bg-color: #f0fdf4;
  --comment-border-color: #bbf7d0;
}

.custom-comment-box :deep(.comment-box-inline) {
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.15);
}

.custom-comment-box :deep(.btn-expand),
.custom-comment-box :deep(.btn-popup) {
  background: linear-gradient(135deg, #10b981, #059669);
}
</style>
```

## 📱 移动端优化示例

### 1. 响应式布局

```vue
<template>
  <div class="mobile-example">
    <div class="mobile-post">
      <div class="post-header">
        <img :src="post.author.avatar" class="avatar" />
        <div class="author-info">
          <h4>{{ post.author.name }}</h4>
          <span>{{ post.timeAgo }}</span>
        </div>
      </div>
      
      <div class="post-content">
        <p>{{ post.content }}</p>
        <img v-if="post.image" :src="post.image" class="post-image" />
      </div>
      
      <!-- 移动端优化的评论组件 -->
      <CommentBox
        :target-id="post.id"
        target-type="post"
        mode="compact"
        class="mobile-comments"
        placeholder="写评论..."
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const post = ref({
  id: 'mobile-post-1',
  content: '移动端的精彩内容分享！',
  image: '/images/mobile-post.jpg',
  author: {
    name: '移动用户',
    avatar: '/avatars/mobile-user.jpg'
  },
  timeAgo: '2小时前'
})
</script>

<style scoped>
.mobile-example {
  max-width: 375px;
  margin: 0 auto;
  background: #f5f5f5;
  min-height: 100vh;
}

.mobile-post {
  background: white;
  margin-bottom: 1px;
}

.post-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
}

.author-info h4 {
  margin: 0 0 2px 0;
  font-size: 14px;
  font-weight: 600;
}

.author-info span {
  font-size: 12px;
  color: #666;
}

.post-content {
  padding: 0 16px 12px;
}

.post-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.4;
}

.post-image {
  width: 100%;
  border-radius: 8px;
}

.mobile-comments {
  border-top: 1px solid #f0f0f0;
}

/* 移动端特定样式 */
@media (max-width: 768px) {
  .mobile-comments :deep(.comment-header) {
    padding: 8px 16px;
  }
  
  .mobile-comments :deep(.btn-popup) {
    padding: 6px 12px;
    font-size: 13px;
  }
}
</style>
```

## 🔧 高级功能示例

### 1. 权限控制

```vue
<template>
  <div class="permission-example">
    <div class="user-status">
      <p>当前用户状态: {{ userStatus }}</p>
      <button @click="toggleLogin">{{ isLoggedIn ? '退出登录' : '登录' }}</button>
    </div>
    
    <!-- 根据用户状态动态调整权限 -->
    <CommentBox
      target-id="permission-post-123"
      target-type="post"
      :require-login="!isLoggedIn"
      :allow-anonymous="!requireStrictAuth"
      :placeholder="commentPlaceholder"
      @comment-added="handleCommentAdded"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const isLoggedIn = ref(false)
const requireStrictAuth = ref(true)

const userStatus = computed(() => {
  if (isLoggedIn.value) return '已登录'
  if (requireStrictAuth.value) return '需要登录'
  return '可匿名评论'
})

const commentPlaceholder = computed(() => {
  if (!isLoggedIn.value && requireStrictAuth.value) {
    return '请先登录后再评论'
  }
  return '写下你的评论...'
})

const toggleLogin = () => {
  isLoggedIn.value = !isLoggedIn.value
}

const handleCommentAdded = (comment) => {
  console.log('权限验证通过，评论已添加:', comment)
}
</script>

<style scoped>
.user-status {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
```

### 2. 实时数据更新

```vue
<template>
  <div class="realtime-example">
    <div class="stats">
      <span>总评论数: {{ totalComments }}</span>
      <span>在线用户: {{ onlineUsers }}</span>
    </div>
    
    <CommentBox
      ref="commentBoxRef"
      target-id="realtime-post-456"
      target-type="post"
      @comment-added="handleCommentAdded"
      @comment-liked="handleCommentLiked"
    />
    
    <div class="actions">
      <button @click="refreshComments">刷新评论</button>
      <button @click="simulateNewComment">模拟新评论</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const commentBoxRef = ref()
const totalComments = ref(0)
const onlineUsers = ref(12)

// 模拟实时数据更新
let updateInterval

const handleCommentAdded = (comment) => {
  totalComments.value++
  console.log('实时评论更新:', comment)
}

const handleCommentLiked = (commentId, liked) => {
  console.log('实时点赞更新:', commentId, liked)
}

const refreshComments = () => {
  commentBoxRef.value?.refresh()
}

const simulateNewComment = () => {
  // 模拟其他用户的新评论
  totalComments.value++
  // 可以在这里触发评论列表的更新
}

onMounted(() => {
  // 模拟在线用户数的实时更新
  updateInterval = setInterval(() => {
    onlineUsers.value = Math.floor(Math.random() * 20) + 10
  }, 5000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.stats {
  display: flex;
  gap: 20px;
  padding: 16px;
  background: #e3f2fd;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  color: #1565c0;
}

.actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.actions button {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
```

### 3. 媒体上传示例

```vue
<template>
  <div class="media-example">
    <h3>支持媒体上传的评论</h3>
    <p>用户可以在评论中上传图片和视频</p>
    
    <CommentBox
      target-id="media-post-789"
      target-type="post"
      :support-image="true"
      :support-video="true"
      placeholder="分享图片或视频，让评论更生动..."
      @comment-added="handleMediaComment"
    />
    
    <div class="upload-tips">
      <h4>上传提示:</h4>
      <ul>
        <li>支持 JPG、PNG、GIF 格式图片</li>
        <li>支持 MP4、WebM 格式视频</li>
        <li>单个文件最大 10MB</li>
        <li>最多可上传 5 个媒体文件</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import CommentBox from '@/shared/components/social/comments/Index.vue'

const handleMediaComment = (comment) => {
  console.log('收到媒体评论:', comment)
  if (comment.media && comment.media.length > 0) {
    console.log('包含媒体文件:', comment.media)
  }
}
</script>

<style scoped>
.upload-tips {
  margin-top: 20px;
  padding: 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.upload-tips h4 {
  margin: 0 0 12px 0;
  color: #856404;
}

.upload-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #856404;
}

.upload-tips li {
  margin-bottom: 4px;
}
</style>
```

## 🎯 特殊场景示例

### 1. 短视频应用

```vue
<template>
  <div class="shorts-app">
    <div class="shorts-container">
      <div v-for="short in shorts" :key="short.id" class="short-item">
        <video 
          :src="short.url" 
          :poster="short.poster"
          loop 
          muted 
          autoplay
          @click="togglePlay"
        ></video>
        
        <div class="short-overlay">
          <div class="short-info">
            <h4>{{ short.title }}</h4>
            <p>@{{ short.author }}</p>
          </div>
          
          <div class="short-actions">
            <button @click="toggleLike(short)" class="action-btn">
              <i :class="['icon-heart', { liked: short.isLiked }]"></i>
              <span>{{ short.likeCount }}</span>
            </button>
            
            <button @click="openComments(short)" class="action-btn">
              <i class="icon-comment"></i>
              <span>{{ short.commentCount }}</span>
            </button>
            
            <button @click="shareShort(short)" class="action-btn">
              <i class="icon-share"></i>
              <span>分享</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 短视频评论弹窗 -->
    <CommentBox
      v-if="activeShort"
      :target-id="activeShort.id"
      target-type="shortvideo"
      mode="dialog"
      theme="dark"
      :support-image="false"
      :support-video="false"
      placeholder="说点什么..."
      @dialog-closed="closeComments"
      @comment-added="handleShortComment"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const activeShort = ref(null)

const shorts = ref([
  {
    id: 'short-1',
    title: '精彩短视频1',
    author: 'creator1',
    url: '/videos/short1.mp4',
    poster: '/images/short1-poster.jpg',
    likeCount: 1250,
    commentCount: 89,
    isLiked: false
  },
  // 更多短视频...
])

const togglePlay = (event) => {
  const video = event.target
  if (video.paused) {
    video.play()
  } else {
    video.pause()
  }
}

const toggleLike = (short) => {
  short.isLiked = !short.isLiked
  short.likeCount += short.isLiked ? 1 : -1
}

const openComments = (short) => {
  activeShort.value = short
}

const closeComments = () => {
  activeShort.value = null
}

const handleShortComment = (comment) => {
  if (activeShort.value) {
    activeShort.value.commentCount++
  }
  console.log('短视频评论:', comment)
}

const shareShort = (short) => {
  console.log('分享短视频:', short.id)
}
</script>

<style scoped>
.shorts-app {
  height: 100vh;
  overflow: hidden;
  background: #000;
}

.shorts-container {
  height: 100%;
  overflow-y: auto;
  scroll-snap-type: y mandatory;
}

.short-item {
  height: 100vh;
  position: relative;
  scroll-snap-align: start;
  display: flex;
  align-items: center;
  justify-content: center;
}

.short-item video {
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
}

.short-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.short-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.short-info p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.short-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-btn {
  background: rgba(255,255,255,0.2);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  color: white;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  backdrop-filter: blur(10px);
}

.action-btn i {
  font-size: 20px;
  margin-bottom: 2px;
}

.icon-heart.liked {
  color: #ff4757;
}
</style>
```

### 2. 图片社区

```vue
<template>
  <div class="photo-community">
    <div class="photo-grid">
      <div v-for="photo in photos" :key="photo.id" class="photo-item">
        <div class="photo-container">
          <img :src="photo.url" :alt="photo.title" @click="openPhotoDetail(photo)" />
          <div class="photo-overlay">
            <div class="photo-stats">
              <span><i class="icon-heart"></i> {{ photo.likes }}</span>
              <span><i class="icon-comment"></i> {{ photo.comments }}</span>
            </div>
          </div>
        </div>
        
        <!-- 图片下方的紧凑评论 -->
        <CommentBox
          :target-id="photo.id"
          target-type="image"
          mode="compact"
          :support-video="false"
          placeholder="夸夸这张照片..."
        />
      </div>
    </div>
    
    <!-- 图片详情弹窗 -->
    <div v-if="selectedPhoto" class="photo-detail-modal" @click="closePhotoDetail">
      <div class="photo-detail" @click.stop>
        <img :src="selectedPhoto.url" :alt="selectedPhoto.title" />
        <div class="photo-detail-sidebar">
          <div class="photo-info">
            <h3>{{ selectedPhoto.title }}</h3>
            <p>{{ selectedPhoto.description }}</p>
            <div class="photo-meta">
              <span>摄影师: {{ selectedPhoto.photographer }}</span>
              <span>拍摄时间: {{ selectedPhoto.date }}</span>
            </div>
          </div>
          
          <!-- 详情页评论 -->
          <CommentBox
            :target-id="selectedPhoto.id"
            target-type="image"
            mode="inline"
            :support-image="true"
            :support-video="false"
            placeholder="分享你对这张照片的看法..."
            class="photo-comments"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CommentBox from '@/shared/components/social/comments/Index.vue'

const selectedPhoto = ref(null)

const photos = ref([
  {
    id: 'photo-1',
    title: '日落时分',
    description: '在海边拍摄的美丽日落',
    url: '/images/sunset.jpg',
    photographer: '风景摄影师',
    date: '2024-01-15',
    likes: 234,
    comments: 45
  },
  // 更多照片...
])

const openPhotoDetail = (photo) => {
  selectedPhoto.value = photo
}

const closePhotoDetail = () => {
  selectedPhoto.value = null
}
</script>

<style scoped>
.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.photo-item {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.photo-container {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
}

.photo-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.photo-container:hover img {
  transform: scale(1.05);
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-end;
  padding: 16px;
}

.photo-container:hover .photo-overlay {
  opacity: 1;
}

.photo-stats {
  display: flex;
  gap: 16px;
  color: white;
  font-size: 14px;
}

.photo-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.photo-detail {
  display: flex;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.photo-detail img {
  max-width: 60vw;
  max-height: 90vh;
  object-fit: contain;
}

.photo-detail-sidebar {
  width: 400px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.photo-info {
  margin-bottom: 20px;
}

.photo-info h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
}

.photo-info p {
  margin: 0 0 16px 0;
  color: #666;
  line-height: 1.5;
}

.photo-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #888;
}

.photo-comments {
  flex: 1;
  overflow: hidden;
}
</style>
```

这些示例展示了评论组件在各种实际场景中的灵活应用，从简单的博客评论到复杂的社交媒体应用，都能很好地满足需求。每个示例都包含了完整的代码和样式，可以直接在项目中使用或作为参考进行定制。