package sync

import (
	"context"
	"fmt"
	"sync"
	"time"

	"frontapi/internal/service/base/extlike"
)

// SyncTask 同步任务接口
type SyncTask interface {
	// Execute 执行同步任务
	Execute(ctx context.Context) error

	// GetName 获取任务名称
	GetName() string

	// GetInterval 获取执行间隔
	GetInterval() time.Duration

	// IsEnabled 是否启用
	IsEnabled() bool
}

// DataSyncTask 数据同步任务
type DataSyncTask struct {
	name         string
	interval     time.Duration
	enabled      bool
	likeService  extlike.ExtendedLikeService
	itemType     string
	batchSize    int
	lastSyncTime time.Time
	mutex        sync.RWMutex
}

// NewDataSyncTask 创建数据同步任务
func NewDataSyncTask(
	name string,
	interval time.Duration,
	likeService extlike.ExtendedLikeService,
	itemType string,
	batchSize int,
) *DataSyncTask {
	return &DataSyncTask{
		name:        name,
		interval:    interval,
		enabled:     true,
		likeService: likeService,
		itemType:    itemType,
		batchSize:   batchSize,
	}
}

// GetName 获取任务名称
func (t *DataSyncTask) GetName() string {
	return t.name
}

// GetInterval 获取执行间隔
func (t *DataSyncTask) GetInterval() time.Duration {
	return t.interval
}

// IsEnabled 是否启用
func (t *DataSyncTask) IsEnabled() bool {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	return t.enabled
}

// SetEnabled 设置启用状态
func (t *DataSyncTask) SetEnabled(enabled bool) {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	t.enabled = enabled
}

// GetLastSyncTime 获取最后同步时间
func (t *DataSyncTask) GetLastSyncTime() time.Time {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	return t.lastSyncTime
}

// Execute 执行同步任务
func (t *DataSyncTask) Execute(ctx context.Context) error {
	if !t.IsEnabled() {
		return fmt.Errorf("任务 %s 未启用", t.name)
	}

	t.mutex.Lock()
	defer t.mutex.Unlock()

	// 更新最后同步时间
	t.lastSyncTime = time.Now()

	// 这里实现具体的同步逻辑
	// 由于具体的同步逻辑依赖于业务实现，这里提供一个框架
	fmt.Printf("[%s] 开始同步 %s 类型的点赞数据...\n", t.name, t.itemType)

	// 示例：获取待同步的数据
	// 实际实现中，可能需要从缓存中获取变更的数据
	// 然后批量同步到数据库

	fmt.Printf("[%s] 同步完成\n", t.name)
	return nil
}

// Scheduler 任务调度器
type Scheduler struct {
	tasks   map[string]SyncTask
	running bool
	done    chan struct{}
	mutex   sync.RWMutex
}

// NewScheduler 创建任务调度器
func NewScheduler() *Scheduler {
	return &Scheduler{
		tasks: make(map[string]SyncTask),
		done:  make(chan struct{}),
	}
}

// AddTask 添加任务
func (s *Scheduler) AddTask(task SyncTask) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.tasks[task.GetName()] = task
}

// RemoveTask 移除任务
func (s *Scheduler) RemoveTask(name string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	delete(s.tasks, name)
}

// GetTask 获取任务
func (s *Scheduler) GetTask(name string) (SyncTask, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	task, exists := s.tasks[name]
	return task, exists
}

// GetAllTasks 获取所有任务
func (s *Scheduler) GetAllTasks() map[string]SyncTask {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make(map[string]SyncTask)
	for name, task := range s.tasks {
		result[name] = task
	}
	return result
}

// Start 启动调度器
func (s *Scheduler) Start(ctx context.Context) error {
	s.mutex.Lock()
	if s.running {
		s.mutex.Unlock()
		return fmt.Errorf("调度器已经在运行")
	}
	s.running = true
	s.mutex.Unlock()

	fmt.Println("点赞数据同步调度器启动")

	// 为每个任务启动一个协程
	for _, task := range s.GetAllTasks() {
		if task.IsEnabled() {
			go s.runTask(ctx, task)
		}
	}

	// 等待停止信号
	select {
	case <-ctx.Done():
		fmt.Println("收到停止信号，调度器停止")
	case <-s.done:
		fmt.Println("调度器已停止")
	}

	s.mutex.Lock()
	s.running = false
	s.mutex.Unlock()

	return nil
}

// Stop 停止调度器
func (s *Scheduler) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		close(s.done)
		s.running = false
	}
}

// IsRunning 检查是否正在运行
func (s *Scheduler) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.running
}

// runTask 运行单个任务
func (s *Scheduler) runTask(ctx context.Context, task SyncTask) {
	ticker := time.NewTicker(task.GetInterval())
	defer ticker.Stop()

	fmt.Printf("任务 %s 开始运行，间隔: %v\n", task.GetName(), task.GetInterval())

	for {
		select {
		case <-ctx.Done():
			fmt.Printf("任务 %s 收到停止信号\n", task.GetName())
			return
		case <-s.done:
			fmt.Printf("任务 %s 停止\n", task.GetName())
			return
		case <-ticker.C:
			if task.IsEnabled() {
				if err := task.Execute(ctx); err != nil {
					fmt.Printf("任务 %s 执行失败: %v\n", task.GetName(), err)
				}
			}
		}
	}
}

// GetStatus 获取调度器状态
func (s *Scheduler) GetStatus() *SchedulerStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	status := &SchedulerStatus{
		IsRunning:  s.running,
		TaskCount:  len(s.tasks),
		TaskStatus: make(map[string]*TaskStatus),
	}

	for name, task := range s.tasks {
		var lastSyncTime time.Time
		if dataTask, ok := task.(*DataSyncTask); ok {
			lastSyncTime = dataTask.GetLastSyncTime()
		}

		status.TaskStatus[name] = &TaskStatus{
			Name:         task.GetName(),
			Enabled:      task.IsEnabled(),
			Interval:     task.GetInterval(),
			LastSyncTime: lastSyncTime,
		}
	}

	return status
}

// SchedulerStatus 调度器状态
type SchedulerStatus struct {
	IsRunning  bool                   `json:"is_running"`
	TaskCount  int                    `json:"task_count"`
	TaskStatus map[string]*TaskStatus `json:"task_status"`
}

// TaskStatus 任务状态
type TaskStatus struct {
	Name         string        `json:"name"`
	Enabled      bool          `json:"enabled"`
	Interval     time.Duration `json:"interval"`
	LastSyncTime time.Time     `json:"last_sync_time"`
}

// SyncManager 同步管理器
type SyncManager struct {
	scheduler   *Scheduler
	likeService extlike.ExtendedLikeService
	config      *SyncConfig
}

// SyncConfig 同步配置
type SyncConfig struct {
	// 全局配置
	Enabled         bool          `json:"enabled" yaml:"enabled"`
	BatchSize       int           `json:"batch_size" yaml:"batch_size"`
	DefaultInterval time.Duration `json:"default_interval" yaml:"default_interval"`

	// 各种内容类型的同步配置
	ItemTypes map[string]*ItemTypeSyncConfig `json:"item_types" yaml:"item_types"`
}

// ItemTypeSyncConfig 内容类型同步配置
type ItemTypeSyncConfig struct {
	Enabled   bool          `json:"enabled" yaml:"enabled"`
	Interval  time.Duration `json:"interval" yaml:"interval"`
	BatchSize int           `json:"batch_size" yaml:"batch_size"`
}

// DefaultSyncConfig 默认同步配置
func DefaultSyncConfig() *SyncConfig {
	return &SyncConfig{
		Enabled:         true,
		BatchSize:       100,
		DefaultInterval: 5 * time.Minute,
		ItemTypes: map[string]*ItemTypeSyncConfig{
			"shortvideo_comment": {
				Enabled:   true,
				Interval:  5 * time.Minute,
				BatchSize: 100,
			},
			"shortvideo": {
				Enabled:   true,
				Interval:  10 * time.Minute,
				BatchSize: 200,
			},
			"post": {
				Enabled:   true,
				Interval:  5 * time.Minute,
				BatchSize: 100,
			},
			"video": {
				Enabled:   true,
				Interval:  10 * time.Minute,
				BatchSize: 200,
			},
		},
	}
}

// NewSyncManager 创建同步管理器
func NewSyncManager(likeService extlike.ExtendedLikeService, config *SyncConfig) *SyncManager {
	if config == nil {
		config = DefaultSyncConfig()
	}

	return &SyncManager{
		scheduler:   NewScheduler(),
		likeService: likeService,
		config:      config,
	}
}

// Initialize 初始化同步管理器
func (m *SyncManager) Initialize() error {
	if !m.config.Enabled {
		return fmt.Errorf("同步功能未启用")
	}

	// 为每种内容类型创建同步任务
	for itemType, typeConfig := range m.config.ItemTypes {
		if typeConfig.Enabled {
			taskName := fmt.Sprintf("sync_%s", itemType)
			interval := typeConfig.Interval
			if interval == 0 {
				interval = m.config.DefaultInterval
			}

			batchSize := typeConfig.BatchSize
			if batchSize == 0 {
				batchSize = m.config.BatchSize
			}

			task := NewDataSyncTask(
				taskName,
				interval,
				m.likeService,
				itemType,
				batchSize,
			)

			m.scheduler.AddTask(task)
			fmt.Printf("已添加同步任务: %s, 间隔: %v, 批量大小: %d\n",
				taskName, interval, batchSize)
		}
	}

	return nil
}

// Start 启动同步管理器
func (m *SyncManager) Start(ctx context.Context) error {
	return m.scheduler.Start(ctx)
}

// Stop 停止同步管理器
func (m *SyncManager) Stop() {
	m.scheduler.Stop()
}

// GetStatus 获取状态
func (m *SyncManager) GetStatus() *SchedulerStatus {
	return m.scheduler.GetStatus()
}

// EnableTask 启用任务
func (m *SyncManager) EnableTask(taskName string) error {
	task, exists := m.scheduler.GetTask(taskName)
	if !exists {
		return fmt.Errorf("任务 %s 不存在", taskName)
	}

	if dataTask, ok := task.(*DataSyncTask); ok {
		dataTask.SetEnabled(true)
		return nil
	}

	return fmt.Errorf("任务 %s 类型不支持", taskName)
}

// DisableTask 禁用任务
func (m *SyncManager) DisableTask(taskName string) error {
	task, exists := m.scheduler.GetTask(taskName)
	if !exists {
		return fmt.Errorf("任务 %s 不存在", taskName)
	}

	if dataTask, ok := task.(*DataSyncTask); ok {
		dataTask.SetEnabled(false)
		return nil
	}

	return fmt.Errorf("任务 %s 类型不支持", taskName)
}

// ExecuteTaskNow 立即执行任务
func (m *SyncManager) ExecuteTaskNow(ctx context.Context, taskName string) error {
	task, exists := m.scheduler.GetTask(taskName)
	if !exists {
		return fmt.Errorf("任务 %s 不存在", taskName)
	}

	return task.Execute(ctx)
}
