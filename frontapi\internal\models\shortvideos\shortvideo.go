package shortvideos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// ShortVideo 短视频模型
type ShortVideo struct {
	*models.ContentBaseModel
	Cover         string            `json:"cover" gorm:"not null;comment:封面图URL"`
	URL           string            `json:"url" gorm:"not null;comment:视频URL"`
	Duration      int               `json:"duration" gorm:"default:0;comment:时长(秒)"`
	Resolution    string            `json:"resolution" gorm:"comment:分辨率"`
	FavoriteCount uint64            `json:"favorite_count" gorm:"default:0;comment:收藏次数"`
	Tags          types.StringArray `json:"tags" gorm:"type:json;comment:标签"`
	SrcType       int8              `json:"src_type" gorm:"comment:1.用户上传，2系统上传，3.系统采集"`
	Src           string            `json:"src" gorm:"comment:采集地址"`
	IsPaid        int8              `json:"is_paid" gorm:"default:0;comment:是否付费：0-免费，1-付费"`
	IsFeatured    int8              `json:"is_featured" gorm:"default:0;comment:是否推荐"`
	Price         float64           `json:"price" gorm:"type:decimal(10,2);default:0.00;comment:价格"`
	UploadTime    types.JSONTime    `json:"upload_time" gorm:"default:CURRENT_TIMESTAMP;comment:上传时间"`
	Reason        string            `json:"reason" gorm:"type:string;size:255;comment:禁用原因"`
	IsLiked       bool              `json:"is_liked" gorm:"-"`
	IsCommented   bool              `json:"is_commented" gorm:"-"`
	IsCollected   bool              `json:"is_collected" gorm:"-"`
}

// TableName 指定表名
func (ShortVideo) TableName() string {
	return "ly_shorts"
}
