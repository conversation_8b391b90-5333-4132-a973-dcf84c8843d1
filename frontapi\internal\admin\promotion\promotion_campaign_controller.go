package promotion

import (
	"frontapi/internal/admin"
	service "frontapi/internal/service/promotion"

	"github.com/gofiber/fiber/v2"
)

type PromotionCampaignController struct {
	admin.BaseController
	service service.PromotionCampaignService
}

func NewPromotionCampaignController(service service.PromotionCampaignService) *PromotionCampaignController {
	return &PromotionCampaignController{service: service}
}

// ListCampaigns 获取活动列表
func (h *PromotionCampaignController) ListCampaigns(c *fiber.Ctx) error {
	return h.Success(c, "")
}
