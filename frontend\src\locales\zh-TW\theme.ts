import enTheme from '../en/theme';

export default {
    ...enTheme,
    modern: "明快現代",
    warm: "溫暖友善",
    dark: "時尚前衛",
    fresh: "清新自然",
    charm: "魅力誘惑",
    mysterious: "神秘優雅",
    light: "淺色主題",

    blue: "藍色主題",
    purple: "紫色主題",
    green: "綠色主題",
    pink: "粉紅色主題",
    mysteriousLight: "神秘優雅 淺色",
    mysteriousDark: "神秘優雅 深色",
    modernLight: "明快現代 淺色",
    modernDark: "明快現代 深色",
    warmLight: "溫暖友善 淺色",
    warmDark: "溫暖友善 深色",
    darkLight: "時尚前衛 淺色",
    darkDark: "時尚前衛 深色",
    freshLight: "清新自然 淺色",
    freshDark: "清新自然 深色",
    charmLight: "魅力誘惑 淺色",
    charmDark: "魅力誘惑 深色",
    themeSystemDemo: "主題系統示範",
    themeSystemDescription: "此頁面展示了基於PrimeVue和CSS變數的新主題系統。",
    themeSelector: "主題選擇器",
    themeSelectorDescription: "主題選擇器元件允許使用者選擇不同的主題並切換暗黑模式。",
    themeVariables: "主題變數",
    componentPreview: "元件預覽",
    buttons: "按鈕",
    inputs: "輸入框",
    cards: "卡片",
    cardTitle: "卡片標題",
    cardContent: "這是一個展示主題樣式的範例卡片元件。",
    apiUsage: "API使用方法",
    // 設定介面
    title: "選擇主題",
    selectTheme: "選擇主題",
    themeSettings: "主題設定",
    changeTheme: "更換主題",
    followSystem: "跟隨系統主題",
    darkMode: "暗黑模式",

    // 描述
    modernDesc: "科技感與信任，適合視訊平台或社交主頁",
    warmDesc: "讓使用者感覺親切，適合社區互動類",
    darkDesc: "提升潮酷感，適合年輕用戶群",
    freshDesc: "營造舒適體驗，適合興趣社群或生活分享平台",
    charmDesc: "從淺粉到玫紅，帶來溫柔又活力的氛圍",
    mysteriousDesc: "奢華感、成熟、時尚，溫柔、夢幻，活力、前衛",

    // 其他設定
    settings: "主題設定",
    customization: "個人化設定",
    apply: "應用程式主題",
    reset: "重置設定"
} as const;