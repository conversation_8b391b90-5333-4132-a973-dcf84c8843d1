import { RouteRecordRaw } from 'vue-router';

const videoRoutes: Array<RouteRecordRaw> = [
    {
        path: '/videos',
        name: 'Videos',
        component: () => import('@/views/videos/list/index.vue'),
        meta: {
            title: 'videos',
            icon: 'video'
        }
    },
    {
        path: '/videos/:id',
        name: 'VideoDetail',
        component: () => import('@/views/videos/detail/index.vue'),
        meta: {
            title: 'videoDetail',
            hideInMenu: true
        }
    }
];

export default videoRoutes; 