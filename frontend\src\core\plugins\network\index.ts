/**
 * 网络插件 - 提供网络状态监控和请求管理功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'

/**
 * 网络状态
 */
export interface NetworkState {
  isOnline: boolean
  connectionType: string
  effectiveType: string
  downlink: number
  rtt: number
  saveData: boolean
}

/**
 * 请求配置
 */
export interface RequestConfig {
  url: string
  method?: string
  headers?: Record<string, string>
  body?: any
  timeout?: number
  retries?: number
  retryDelay?: number
  cache?: boolean
  cacheTTL?: number
  priority?: 'low' | 'normal' | 'high'
  abortSignal?: AbortSignal
}

/**
 * 请求响应
 */
export interface RequestResponse<T = any> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
  config: RequestConfig
  duration: number
}

/**
 * 网络配置
 */
export interface NetworkConfig {
  /** 是否启用网络监控 */
  enableMonitoring: boolean
  /** 是否启用请求拦截 */
  enableInterceptors: boolean
  /** 默认超时时间 */
  defaultTimeout: number
  /** 默认重试次数 */
  defaultRetries: number
  /** 默认重试延迟 */
  defaultRetryDelay: number
  /** 是否启用缓存 */
  enableCache: boolean
  /** 默认缓存TTL */
  defaultCacheTTL: number
  /** 最大并发请求数 */
  maxConcurrentRequests: number
  /** 是否启用请求队列 */
  enableQueue: boolean
  /** 基础URL */
  baseURL?: string
  /** 默认请求头 */
  defaultHeaders: Record<string, string>
}

/**
 * 请求统计
 */
export interface RequestStats {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  requestsByMethod: Record<string, number>
  requestsByStatus: Record<number, number>
  cacheHits: number
  cacheMisses: number
}

/**
 * 网络管理器类
 */
export class NetworkManager {
  private config: NetworkConfig
  private state: NetworkState
  private listeners: Array<(state: NetworkState) => void> = []
  private requestQueue: RequestConfig[] = []
  private activeRequests = new Set<Promise<any>>()
  private requestCache = new Map<string, { data: any; expiresAt: number }>()
  private stats: RequestStats
  private requestInterceptors: Array<(config: RequestConfig) => RequestConfig | Promise<RequestConfig>> = []
  private responseInterceptors: Array<(response: RequestResponse) => RequestResponse | Promise<RequestResponse>> = []
  private errorInterceptors: Array<(error: Error) => Error | Promise<Error>> = []

  constructor(config: NetworkConfig) {
    this.config = config
    this.state = {
      isOnline: navigator.onLine,
      connectionType: 'unknown',
      effectiveType: 'unknown',
      downlink: 0,
      rtt: 0,
      saveData: false
    }
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      requestsByMethod: {},
      requestsByStatus: {},
      cacheHits: 0,
      cacheMisses: 0
    }
    this.init()
  }

  /**
   * 初始化网络管理器
   */
  private init() {
    if (this.config.enableMonitoring) {
      this.setupNetworkMonitoring()
    }
    
    this.updateNetworkState()
    console.log('Network manager initialized')
  }

  /**
   * 设置网络监控
   */
  private setupNetworkMonitoring() {
    // 监听在线/离线状态
    window.addEventListener('online', () => {
      this.state.isOnline = true
      this.notifyListeners()
      this.processQueue()
    })

    window.addEventListener('offline', () => {
      this.state.isOnline = false
      this.notifyListeners()
    })

    // 监听网络连接变化
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      const updateConnection = () => {
        this.state.connectionType = connection.type || 'unknown'
        this.state.effectiveType = connection.effectiveType || 'unknown'
        this.state.downlink = connection.downlink || 0
        this.state.rtt = connection.rtt || 0
        this.state.saveData = connection.saveData || false
        this.notifyListeners()
      }

      connection.addEventListener('change', updateConnection)
      updateConnection()
    }
  }

  /**
   * 更新网络状态
   */
  private updateNetworkState() {
    this.state.isOnline = navigator.onLine
    
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      this.state.connectionType = connection?.type || 'unknown'
      this.state.effectiveType = connection?.effectiveType || 'unknown'
      this.state.downlink = connection?.downlink || 0
      this.state.rtt = connection?.rtt || 0
      this.state.saveData = connection?.saveData || false
    }
  }

  /**
   * 发送请求
   */
  async request<T = any>(config: RequestConfig): Promise<RequestResponse<T>> {
    // 应用默认配置
    const finalConfig: RequestConfig = {
      method: 'GET',
      timeout: this.config.defaultTimeout,
      retries: this.config.defaultRetries,
      retryDelay: this.config.defaultRetryDelay,
      cache: this.config.enableCache,
      cacheTTL: this.config.defaultCacheTTL,
      priority: 'normal',
      ...config,
      headers: {
        ...this.config.defaultHeaders,
        ...config.headers
      }
    }

    // 处理基础URL
    if (this.config.baseURL && !finalConfig.url.startsWith('http')) {
      finalConfig.url = this.config.baseURL + finalConfig.url
    }

    // 应用请求拦截器
    if (this.config.enableInterceptors) {
      for (const interceptor of this.requestInterceptors) {
        try {
          const result = await interceptor(finalConfig)
          Object.assign(finalConfig, result)
        } catch (error) {
          console.error('Request interceptor error:', error)
        }
      }
    }

    // 检查缓存
    if (finalConfig.cache && finalConfig.method?.toUpperCase() === 'GET') {
      const cached = this.getFromCache(finalConfig.url)
      if (cached) {
        this.stats.cacheHits++
        return {
          data: cached,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: finalConfig,
          duration: 0
        }
      }
      this.stats.cacheMisses++
    }

    // 检查网络状态
    if (!this.state.isOnline) {
      if (this.config.enableQueue) {
        return this.queueRequest(finalConfig)
      }
      throw new Error('Network is offline')
    }

    // 检查并发限制
    if (this.activeRequests.size >= this.config.maxConcurrentRequests) {
      if (this.config.enableQueue) {
        return this.queueRequest(finalConfig)
      }
      throw new Error('Too many concurrent requests')
    }

    return this.executeRequest(finalConfig)
  }

  /**
   * 执行请求
   */
  private async executeRequest<T>(config: RequestConfig): Promise<RequestResponse<T>> {
    const startTime = Date.now()
    let attempt = 0
    let lastError: Error
    let requestPromise: Promise<RequestResponse<T>> | null = null

    while (attempt <= (config.retries || 0)) {
      try {
        requestPromise = this.performRequest<T>(config)
        this.activeRequests.add(requestPromise)

        const response = await requestPromise
        this.activeRequests.delete(requestPromise)

        // 更新统计
        this.updateStats(config, response, Date.now() - startTime)

        // 缓存响应
        if (config.cache && config.method?.toUpperCase() === 'GET' && response.status === 200) {
          this.setCache(config.url, response.data, config.cacheTTL!)
        }

        // 应用响应拦截器
        if (this.config.enableInterceptors) {
          for (const interceptor of this.responseInterceptors) {
            try {
              const result = await interceptor(response)
              Object.assign(response, result)
            } catch (error) {
              console.error('Response interceptor error:', error)
            }
          }
        }

        return response
      } catch (error) {
        if (requestPromise) {
          this.activeRequests.delete(requestPromise)
        }
        lastError = error as Error

        // 应用错误拦截器
        if (this.config.enableInterceptors) {
          for (const interceptor of this.errorInterceptors) {
            try {
              lastError = await interceptor(lastError)
            } catch (interceptorError) {
              console.error('Error interceptor error:', interceptorError)
            }
          }
        }

        attempt++
        
        if (attempt <= (config.retries || 0)) {
          await new Promise(resolve => 
            setTimeout(resolve, (config.retryDelay || 0) * attempt)
          )
        }
      }
    }

    // 更新失败统计
    this.stats.totalRequests++
    this.stats.failedRequests++
    this.stats.requestsByMethod[config.method!] = (this.stats.requestsByMethod[config.method!] || 0) + 1

    throw lastError!
  }

  /**
   * 执行实际的HTTP请求
   */
  private async performRequest<T>(config: RequestConfig): Promise<RequestResponse<T>> {
    const controller = new AbortController()
    const timeoutId = config.timeout ? setTimeout(() => {
      controller.abort()
    }, config.timeout) : null

    try {
      const response = await fetch(config.url, {
        method: config.method,
        headers: config.headers,
        body: config.body ? JSON.stringify(config.body) : undefined,
        signal: config.abortSignal || controller.signal
      })

      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      const data = await response.json()
      const headers: Record<string, string> = {}
      response.headers.forEach((value, key) => {
        headers[key] = value
      })

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers,
        config,
        duration: 0 // 将在调用处计算
      }
    } catch (error) {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      throw error
    }
  }

  /**
   * 队列请求
   */
  private async queueRequest<T>(config: RequestConfig): Promise<RequestResponse<T>> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        ...config,
        _resolve: resolve,
        _reject: reject
      } as any)
    })
  }

  /**
   * 处理队列
   */
  private async processQueue() {
    while (this.requestQueue.length > 0 && this.state.isOnline) {
      const config = this.requestQueue.shift()!
      const { _resolve, _reject, ...requestConfig } = config as any
      
      try {
        const response = await this.executeRequest(requestConfig)
        _resolve(response)
      } catch (error) {
        _reject(error)
      }
    }
  }

  /**
   * 获取缓存
   */
  private getFromCache(key: string): any {
    const cached = this.requestCache.get(key)
    if (cached && Date.now() < cached.expiresAt) {
      return cached.data
    }
    if (cached) {
      this.requestCache.delete(key)
    }
    return null
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any, ttl: number): void {
    this.requestCache.set(key, {
      data,
      expiresAt: Date.now() + ttl
    })
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.requestCache.clear()
  }

  /**
   * 更新统计
   */
  private updateStats(config: RequestConfig, response: RequestResponse, duration: number): void {
    this.stats.totalRequests++
    this.stats.successfulRequests++
    this.stats.requestsByMethod[config.method!] = (this.stats.requestsByMethod[config.method!] || 0) + 1
    this.stats.requestsByStatus[response.status] = (this.stats.requestsByStatus[response.status] || 0) + 1
    
    // 更新平均响应时间
    const totalTime = this.stats.averageResponseTime * (this.stats.totalRequests - 1) + duration
    this.stats.averageResponseTime = totalTime / this.stats.totalRequests
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>): void {
    this.requestInterceptors.push(interceptor)
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: (response: RequestResponse) => RequestResponse | Promise<RequestResponse>): void {
    this.responseInterceptors.push(interceptor)
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor: (error: Error) => Error | Promise<Error>): void {
    this.errorInterceptors.push(interceptor)
  }

  /**
   * GET请求
   */
  get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<RequestResponse<T>> {
    return this.request<T>({ ...config, url, method: 'GET' })
  }

  /**
   * POST请求
   */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<RequestResponse<T>> {
    return this.request<T>({ ...config, url, method: 'POST', body: data })
  }

  /**
   * PUT请求
   */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<RequestResponse<T>> {
    return this.request<T>({ ...config, url, method: 'PUT', body: data })
  }

  /**
   * DELETE请求
   */
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<RequestResponse<T>> {
    return this.request<T>({ ...config, url, method: 'DELETE' })
  }

  /**
   * 获取网络状态
   */
  getNetworkState(): NetworkState {
    return { ...this.state }
  }

  /**
   * 获取请求统计
   */
  getStats(): RequestStats {
    return { ...this.stats }
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      requestsByMethod: {},
      requestsByStatus: {},
      cacheHits: 0,
      cacheMisses: 0
    }
  }

  /**
   * 添加网络状态监听器
   */
  addListener(listener: (state: NetworkState) => void): void {
    this.listeners.push(listener)
  }

  /**
   * 移除网络状态监听器
   */
  removeListener(listener: (state: NetworkState) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.state)
      } catch (error) {
        console.error('Network listener error:', error)
      }
    })
  }

  /**
   * 取消所有请求
   */
  cancelAllRequests(): void {
    this.activeRequests.clear()
    this.requestQueue = []
  }

  /**
   * 销毁网络管理器
   */
  destroy(): void {
    this.cancelAllRequests()
    this.clearCache()
    this.listeners = []
    this.requestInterceptors = []
    this.responseInterceptors = []
    this.errorInterceptors = []
  }
}

// 默认配置
const defaultConfig: NetworkConfig = {
  enableMonitoring: true,
  enableInterceptors: true,
  defaultTimeout: 10000,
  defaultRetries: 2,
  defaultRetryDelay: 1000,
  enableCache: true,
  defaultCacheTTL: 5 * 60 * 1000, // 5分钟
  maxConcurrentRequests: 6,
  enableQueue: true,
  defaultHeaders: {
    'Content-Type': 'application/json'
  }
}

// 全局网络管理器实例
export const networkManager = new NetworkManager(defaultConfig)

/**
 * 网络插件
 */
export const networkPlugin: Plugin = createPlugin({
  meta: {
    name: 'network',
    version: '1.0.0',
    description: '网络状态监控和请求管理插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 2
  },
  install(context: PluginContext) {
    // 将网络管理器添加到全局属性
    context.app.config.globalProperties.$network = networkManager
    
    // 提供网络管理器
    context.app.provide('network', networkManager)
    
    console.log('Network plugin installed')
  },
  uninstall() {
    networkManager.destroy()
    console.log('Network plugin uninstalled')
  }
})

/**
 * 使用网络的组合式函数
 */
export function useNetwork() {
  return {
    network: networkManager,
    request: networkManager.request.bind(networkManager),
    get: networkManager.get.bind(networkManager),
    post: networkManager.post.bind(networkManager),
    put: networkManager.put.bind(networkManager),
    delete: networkManager.delete.bind(networkManager),
    getNetworkState: networkManager.getNetworkState.bind(networkManager),
    getStats: networkManager.getStats.bind(networkManager),
    resetStats: networkManager.resetStats.bind(networkManager),
    clearCache: networkManager.clearCache.bind(networkManager),
    addListener: networkManager.addListener.bind(networkManager),
    removeListener: networkManager.removeListener.bind(networkManager),
    addRequestInterceptor: networkManager.addRequestInterceptor.bind(networkManager),
    addResponseInterceptor: networkManager.addResponseInterceptor.bind(networkManager),
    addErrorInterceptor: networkManager.addErrorInterceptor.bind(networkManager)
  }
}

export default networkPlugin