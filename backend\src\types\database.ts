// 表信息接口
export interface TableInfo {
  table_name: string;
  table_comment: string;
  engine: string;
  table_rows: number;
  create_time: string;
}

// 字段信息接口
export interface ColumnInfo {
  column_name: string;
  data_type: string;
  column_type: string;
  is_nullable: string;
  column_default: string;
  column_comment: string;
  column_key: string;
  extra: string;
  ordinal_position: number;
}

// 外键信息接口
export interface ForeignKeyInfo {
  constraint_name: string;
  table_name: string;
  column_name: string;
  referenced_table: string;
  referenced_column: string;
  update_rule: string;
  delete_rule: string;
}

// 表详情响应接口
export interface TableDetailResponse {
  table_info: TableInfo;
  columns: ColumnInfo[];
  foreign_keys: ForeignKeyInfo[];
  create_sql: string;
}

// Mock数据请求接口
export interface MockDataRequest {
  table_name: string;
  count: number;
  excluded_fks?: string[];
  mock_rules?: Record<string, string>;
  insert_to_db?: boolean;
}

// Mock数据响应接口
export interface MockDataResponse {
  table_name: string;
  count: number;
  data: Record<string, any>[];
  sql: string;
  inserted_to_db: boolean;
}

// 分页参数
export interface PageParams {
  pageNo: number;
  pageSize: number;
}

// 请求参数包装
export interface RequestWrapper<T> {
  data: T;
  page?: PageParams;
} 