package comics

import (
	"context"
	"errors"

	"frontapi/internal/models/comics"
	repo "frontapi/internal/repository/comics"
	"frontapi/internal/service/base"
)

// ComicChapterService 漫画章节服务接口
type ComicChapterService interface {
	base.IExtendedService[comics.ComicChapter]
	BatchUpdateChapterOrder(ctx context.Context, comicID string, chapters []ChapterOrderItem) error
}

// 添加章节顺序项结构
type ChapterOrderItem struct {
	ID            string `json:"id"`
	ChapterNumber int    `json:"chapter_number"`
}

// comicChapterService 漫画章节服务实现
type comicChapterService struct {
	*base.ExtendedService[comics.ComicChapter]
	chapterRepo repo.ComicChapterRepository
	comicRepo   repo.ComicRepository
	pageRepo    repo.ComicPageRepository
}

// NewComicChapterService 创建漫画章节服务实例
func NewComicChapterService(
	chapterRepo repo.ComicChapterRepository,
	comicRepo repo.ComicRepository,
	pageRepo repo.ComicPageRepository,
) ComicChapterService {
	return &comicChapterService{
		ExtendedService: base.NewExtendedService[comics.ComicChapter](chapterRepo, "comic_chapter"),
		chapterRepo:     chapterRepo,
		comicRepo:       comicRepo,
		pageRepo:        pageRepo,
	}
}

// 修改BatchUpdateChapterOrder方法实现
func (s *comicChapterService) BatchUpdateChapterOrder(ctx context.Context, comicID string, chapters []ChapterOrderItem) error {
	// 验证漫画是否存在
	comic, err := s.comicRepo.FindByID(ctx, comicID)
	if err != nil {
		return err
	}
	if comic == nil {
		return errors.New("漫画不存在")
	}

	// 构建章节ID到序号的映射
	chapterOrders := make(map[string]int)
	for _, item := range chapters {
		chapterOrders[item.ID] = item.ChapterNumber
	}

	// 调用仓库的批量更新方法
	return s.chapterRepo.BatchUpdateOrder(ctx, comicID, chapterOrders)
}
