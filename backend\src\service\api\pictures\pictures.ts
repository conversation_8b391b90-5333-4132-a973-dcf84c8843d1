import { ApiResponse } from "@/types/https";
import type {
    BatchCreatePicturesRequest,
    CreatePictureAlbumRequest,
    CreatePictureCategoryRequest,
    CreatePictureRequest,
    Picture,
    PictureAlbum,
    PictureAlbumParams,
    PictureCategory,
    PictureCategoryParams,
    PictureParams,
    UpdatePictureAlbumRequest,
    UpdatePictureCategoryRequest,
    UpdatePictureRequest
} from '@/types/pictures';
import { request } from "../../request";

// 图片相关API

/**
 * 获取图片列表
 * @param params 查询参数
 */
export function getPictureList(params: PictureParams) {
    return request<ApiResponse<Picture>>({
        url: '/pictures/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取图片详情
 * @param id 图片ID
 */
export function getPictureDetail(id: string) {
    return request<ApiResponse<Picture>>({
        url: `/pictures/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    });
}

/**
 * 创建图片
 * @param data 图片数据
 */
export function createPicture(params: { data: CreatePictureRequest }) {
    return request<ApiResponse<any>>({
        url: '/pictures/add',
        method: 'post',
        data: params
    });
}

/**
 * 更新图片
 * @param id 图片ID
 * @param data 图片数据
 */
export function updatePicture(id: string, params: { data: UpdatePictureRequest }) {
    return request<ApiResponse<any>>({
        url: `/pictures/update/${id}`,
        method: 'post',
        data: params
    });
}

/**
 * 删除图片
 * @param id 图片ID
 */
export function deletePicture(id: string) {
    return request<ApiResponse<any>>({
        url: `/pictures/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    });

}

/**
 * 更新图片状态
 * @param id 图片ID
 * @param status 状态值
 * @param reason 原因（可选）
 */
export function updatePictureStatus(id: string, status: number, reason?: string) {
    return request<ApiResponse<any>>({
        url: '/pictures/update-status',
        method: 'post',
        data: {
            data: {
                id,
                status,
                reason
            }
        }
    });
}

/**
 * 批量更新图片状态
 * @param ids 图片ID数组
 * @param status 状态值
 * @param reason 原因（可选）
 */
export function batchUpdatePictureStatus(ids: string[], status: number, reason?: string) {
    return request<ApiResponse<any>>({
        url: '/pictures/batch-update-status',
        method: 'post',
        data: {
            data: {
                ids,
                status,
                reason
            }
        }
    });
}

/**
 * 批量删除图片
 * @param ids 图片ID数组
 */
export function batchDeletePicture(ids: string[]) {
    return request<ApiResponse<any>>({
        url: '/pictures/batch-delete',
        method: 'post',
        data: {
            data: {
                ids
            }
        }
    });
}

// 图片分类相关API

/**
 * 获取图片分类列表
 * @param params 查询参数
 */
export function getPictureCategoryList(params: PictureCategoryParams) {
    return request<ApiResponse<PictureCategory>>({
        url: '/pictures/categories/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取所有图片分类
 */
export function getAllPictureCategories() {
    return request<ApiResponse<PictureCategory>>({
        url: '/pictures/categories/all',
        method: 'post'
    });
}

/**
 * 获取图片分类详情
 * @param id 分类ID
 */
export function getPictureCategoryDetail(id: string) {
    return request<ApiResponse<PictureCategory>>({
        url: `/pictures/categories/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 创建图片分类
 * @param data 分类数据
 */
export function createPictureCategory(params: { data: CreatePictureCategoryRequest }) {
    return request<ApiResponse<any>>({
        url: '/pictures/categories/add',
        method: 'post',
        data: params
    });
}

/**
 * 更新图片分类
 * @param id 分类ID
 * @param data 分类数据
 */
export function updatePictureCategory(params: { data: UpdatePictureCategoryRequest }) {
    return request<ApiResponse<any>>({
        url: `/pictures/categories/update`,
        method: 'post',
        data: params
    })
}

/**
 * 删除图片分类
 * @param id 分类ID
 */
export function deletePictureCategory(id: string) {
    return request<ApiResponse<any>>({
        url: `/pictures/categories/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 更新图片分类状态
 * @param data 状态数据 {id: string, status: number}
 */
export function updatePictureCategoryStatus(data: { id: string, status: number }) {
    return request<ApiResponse<any>>({
        url: '/pictures/categories/update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量更新图片分类状态
 * @param data 批量状态数据 {ids: string[], status: number}
 */
export function batchUpdatePictureCategoryStatus(data: { ids: string[], status: number }) {
    return request<ApiResponse<any>>({
        url: '/pictures/categories/batch-update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量删除图片分类
 * @param data 批量删除数据 {ids: string[]}
 */
export function batchDeletePictureCategory(data: { ids: string[] }) {
    return request<ApiResponse<any>>({
        url: '/pictures/categories/batch-delete',
        method: 'post',
        data: { data }
    });
}

// 图片专辑相关API

/**
 * 获取图片专辑列表
 * @param params 查询参数
 */
export function getPictureAlbumList(params: PictureAlbumParams) {
    return request<ApiResponse<PictureAlbum>>({
        url: '/pictures/albums/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取图片专辑详情
 * @param id 专辑ID
 */
export function getPictureAlbumDetail(id: string) {
    return request<ApiResponse<PictureAlbum>>({
        url: `/pictures/albums/detail/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 创建图片专辑
 * @param data 专辑数据
 */
export function createPictureAlbum(params: { data: CreatePictureAlbumRequest }) {
    return request<ApiResponse<any>>({
        url: '/pictures/albums/add',
        method: 'post',
        data: params
    })
}

/**
 * 更新图片专辑
 * @param id 专辑ID
 * @param data 专辑数据
 */
export function updatePictureAlbum(params: { data: UpdatePictureAlbumRequest }) {
    return request<ApiResponse<any>>({
        url: `/pictures/albums/update`,
        method: 'post',
        data: params
    })
}

/**
 * 删除图片专辑
 * @param id 专辑ID
 */
export function deletePictureAlbum(id: string) {
    return request<ApiResponse<any>>({
        url: `/pictures/albums/delete/${id}`,
        method: 'post',
        data: { data: { "id": id } }
    })
}

/**
 * 更新图片专辑状态
 * @param data 状态数据 {id: string, status: number}
 */
export function updatePictureAlbumStatus(data: { id: string, status: number }) {
    return request<ApiResponse<any>>({
        url: '/pictures/albums/update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量更新图片专辑状态
 * @param data 批量状态数据 {ids: string[], status: number}
 */
export function batchUpdatePictureAlbumStatus(data: { ids: string[], status: number }) {
    return request<ApiResponse<any>>({
        url: '/pictures/albums/batch-update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量删除图片专辑
 * @param data 批量删除数据 {ids: string[]}
 */
export function batchDeletePictureAlbum(data: { ids: string[] }) {
    return request<ApiResponse<any>>({
        url: '/pictures/albums/batch-delete',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量创建图片
 * @param data 批量图片数据
 */
export function batchCreatePictures(params: { data: BatchCreatePicturesRequest }) {
    return request<ApiResponse<any>>({
        url: '/pictures/batch-add',
        method: 'post',
        data: params
    });
}
