# 缓存系统设计与使用文档

## 1. 概述

缓存系统是一个简化版的缓存实现，提供了统一的缓存接口和多种缓存实现。它支持多种缓存适配器，包括Redis、文件缓存和BigCache内存缓存，同时支持集群和分片功能。

## 2. 系统架构

### 2.1 目录结构

```
cache/
├── interfaces.go      # 接口定义
├── manager.go         # 缓存管理器实现
├── json_utils.go      # JSON工具函数
├── global.go          # 全局缓存管理器
├── init.go            # 初始化函数
├── adapter_factory.go # 适配器工厂
├── types/             # 类型定义
├── redis/             # Redis适配器
├── bigcache/          # BigCache适配器
├── file/              # 文件缓存适配器
├── cluster/           # 集群支持
└── sharding/          # 分片支持
```

### 2.2 核心组件

1. **缓存接口（CacheAdapter）**：定义了缓存操作的基本方法，如Get、Set、Delete等。
2. **缓存管理器（Manager）**：管理多个缓存适配器，提供统一的接口。
3. **适配器工厂**：负责创建各种类型的缓存适配器。
4. **全局缓存管理器**：提供全局访问点，方便在应用程序中使用缓存。
5. **JSON工具**：提供JSON序列化和反序列化功能，支持泛型操作。

### 2.3 适配器类型

1. **Redis适配器**：使用Redis作为缓存存储。
2. **文件适配器**：使用文件系统作为缓存存储。
3. **BigCache适配器**：使用内存作为缓存存储，基于BigCache库实现。
4. **集群适配器**：支持多节点集群，提高可用性。
5. **分片适配器**：支持数据分片，提高性能和扩展性。

## 3. 配置说明

### 3.1 配置结构

缓存系统的配置在`config.go`文件中定义，主要包括以下部分：

```go
// 缓存配置
Cache struct {
    Default string `mapstructure:"default"`
    Redis   struct {
        Host     string `mapstructure:"host"`
        Port     int    `mapstructure:"port"`
        Password string `mapstructure:"password"`
        DB       int    `mapstructure:"db"`
        TTL      int    `mapstructure:"ttl"` // 秒
        // ... 其他配置
    } `mapstructure:"redis"`
    File struct {
        Path     string `mapstructure:"path"`
        TTL      int    `mapstructure:"ttl"` // 秒
        // ... 其他配置
    } `mapstructure:"file"`
    BigCache struct {
        Size     int `mapstructure:"size"`
        TTL      int `mapstructure:"ttl"` // 秒
        // ... 其他配置
    } `mapstructure:"bigcache"`
    Sharding struct {
        Enable       bool   `mapstructure:"enable"`
        ShardCount   int    `mapstructure:"shard_count"`
        HashFunction string `mapstructure:"hash_function"`
    } `mapstructure:"sharding"`
    Cluster struct {
        Enable bool `mapstructure:"enable"`
        Nodes  []struct {
            Name   string `mapstructure:"name"`
            Host   string `mapstructure:"host"`
            Port   int    `mapstructure:"port"`
            Weight int    `mapstructure:"weight"`
            Enable bool   `mapstructure:"enable"`
        } `mapstructure:"nodes"`
    } `mapstructure:"cluster"`
}
```

### 3.2 配置示例

```yaml
cache:
  default: redis
  redis:
    host: localhost
    port: 6379
    password: ""
    db: 0
    ttl: 3600
    pool:
      max_idle: 10
      max_active: 100
      idle_timeout: 300
  file:
    path: ./storage/cache
    ttl: 3600
    advanced:
      cleanup_interval: 600
  bigcache:
    size: 1024
    ttl: 3600
    advanced:
      shards: 1024
      life_window: 600
      clean_window: 300
  sharding:
    enable: false
    shard_count: 4
    hash_function: fnv
  cluster:
    enable: false
    nodes:
      - name: node1
        host: localhost
        port: 6379
        weight: 1
        enable: true
```

## 4. 使用指南

### 4.1 基本用法

#### 4.1.1 初始化缓存系统

在应用程序启动时，通常会调用以下函数来初始化缓存系统：

```go
import (
    "frontapi/pkg/cache"
    "frontapi/config"
)

// 初始化缓存系统
err := cache.InitCacheManager(&config.AppConfig)
if err != nil {
    panic(err)
}
```

#### 4.1.2 使用全局缓存管理器

```go
import (
    "frontapi/pkg/cache"
)

// 获取全局缓存管理器
manager := cache.GetGlobalManager()

// 获取默认适配器
adapter, err := manager.GetDefaultAdapter()
if err != nil {
    panic(err)
}

// 使用适配器
err = adapter.Set(context.Background(), "key", []byte("value"), time.Hour)
if err != nil {
    panic(err)
}
```

#### 4.1.3 JSON序列化和反序列化

```go
import (
    "time"
    "frontapi/pkg/cache"
)

// 使用JSON工具函数
type User struct {
    ID   int    `json:"id"`
    Name string `json:"name"`
}

user := User{ID: 1, Name: "张三"}

// 设置缓存（使用管理器）
err := cache.ManagerSetJSON("user:1", user, time.Hour)
if err != nil {
    panic(err)
}

// 获取缓存（使用管理器）
var retrievedUser User
err = cache.ManagerGetJSON("user:1", &retrievedUser)
if err != nil {
    panic(err)
}

// 使用泛型版本
err = cache.ManagerSetJSONGeneric("user:2", user, time.Hour)
if err != nil {
    panic(err)
}

// 获取缓存（泛型版本）
retrievedUser2, err := cache.ManagerGetJSONGeneric[User]("user:2")
if err != nil {
    panic(err)
}
```

### 4.2 高级用法

#### 4.2.1 创建自定义缓存管理器

```go
import (
    "frontapi/pkg/cache"
    "frontapi/pkg/cache/redis"
)

// 创建缓存管理器
manager, err := cache.NewManager(nil)
if err != nil {
    panic(err)
}

// 创建Redis适配器
redisAdapter, err := redis.NewAdapter(&redis.Config{
    Host:     "localhost",
    Port:     6379,
    Password: "",
    DB:       0,
})
if err != nil {
    panic(err)
}

// 添加适配器
err = manager.AddAdapter("redis", redisAdapter)
if err != nil {
    panic(err)
}

// 设置默认适配器
err = manager.SetDefaultAdapter("redis")
if err != nil {
    panic(err)
}
```

#### 4.2.2 使用分片功能

分片功能可以将缓存数据分散到多个缓存节点，提高性能和可扩展性。

```go
import (
    "frontapi/pkg/cache"
    "frontapi/pkg/cache/sharding"
)

// 创建分片适配器
shardingAdapter, err := sharding.NewAdapter(&sharding.Config{
    Shards: []types.CacheAdapter{
        redisAdapter1,
        redisAdapter2,
        redisAdapter3,
    },
    ShardCount:       4,
    ShardingStrategy: "hash",
})
if err != nil {
    panic(err)
}

// 添加到管理器
manager.AddAdapter("sharding", shardingAdapter)
```

#### 4.2.3 使用集群功能

集群功能可以提高缓存系统的可用性，当某个节点故障时，其他节点可以继续提供服务。

```go
import (
    "frontapi/pkg/cache"
    "frontapi/pkg/cache/cluster"
)

// 创建集群适配器
clusterAdapter, err := cluster.NewAdapter(&cluster.Config{
    Nodes: []types.CacheAdapter{
        redisAdapter1,
        redisAdapter2,
        redisAdapter3,
    },
})
if err != nil {
    panic(err)
}

// 添加到管理器
manager.AddAdapter("cluster", clusterAdapter)
```

## 5. 性能考虑

### 5.1 适配器选择

- **Redis适配器**：适合需要高性能、分布式缓存的场景，支持复杂的数据结构。
- **BigCache适配器**：适合需要高性能、本地内存缓存的场景，不支持分布式。
- **文件适配器**：适合需要持久化缓存的场景，性能相对较低。

### 5.2 性能优化建议

1. **合理设置TTL**：根据数据的实际使用情况设置合理的过期时间。
2. **使用批量操作**：尽量使用MGet、MSet等批量操作，减少网络开销。
3. **使用分片功能**：对于大规模缓存，使用分片功能可以提高性能。
4. **监控缓存统计**：定期检查缓存的命中率、使用率等统计信息，及时调整缓存策略。

## 6. 扩展性

缓存系统设计具有良好的扩展性，可以方便地添加新的缓存适配器。要添加新的适配器，需要实现`CacheAdapter`接口，并在适配器工厂中添加相应的创建方法。

## 7. 故障排除

### 7.1 常见问题

1. **连接失败**：检查缓存服务器的连接配置是否正确。
2. **缓存未命中**：检查键名是否正确，是否已过期。
3. **内存溢出**：检查缓存大小限制，考虑使用分片或增加清理频率。

### 7.2 日志和监控

缓存系统提供了统计信息功能，可以通过`Stats()`方法获取缓存的命中率、使用率等信息，有助于排查问题和优化性能。

## 8. 最佳实践

1. **合理使用缓存**：不是所有数据都适合缓存，应根据访问频率和数据变化频率决定。
2. **设置合理的过期时间**：避免缓存过期时间过长导致数据不一致，或过短导致缓存效果不佳。
3. **处理缓存穿透**：对于不存在的数据，也可以缓存空值，避免频繁查询数据库。
4. **处理缓存雪崩**：避免大量缓存同时过期，可以在过期时间上添加随机值。
5. **定期清理过期数据**：特别是对于文件缓存，定期清理可以释放磁盘空间。

## 9. 总结

缓存系统提供了统一的缓存接口和多种缓存实现，支持Redis、文件和内存缓存，以及集群和分片功能。通过合理配置和使用，可以显著提高应用程序的性能和可扩展性。 