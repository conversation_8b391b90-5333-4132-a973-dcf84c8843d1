/**
 * 工具函数统一导出入口
 * 提供项目中所有常用的工具函数
 */

// 格式化相关
export {
  formatNumber,
  formatCount,
  formatDuration,
  formatDate,
  formatTime,
  formatTimeAgo,
  formatPublishTime,
  formatChapter,
  formatPlaybackSpeed,
  formatVideoQuality,
  formatCurrency,
  formatPercent,
  getHeatLevel,
  getHeatColor,
  type NumberFormatOptions,
  type DateFormatOptions,
  type DurationFormatOptions,
  type PublishTimeFormatOptions,
  type ChapterFormatOptions,
  type FileSizeUnit
} from './format'

// 通用工具函数
export {
  debounce,
  throttle,
  deepClone,
  generateId,
  generateUUID,
  sleep,
  retry,
  getType,
  isEmpty,
  flatten,
  unique,
  isMobile,
  parseUrlParams,
  buildUrlParams,
  noop,
  identity,
  constant,
  memoize,
  type DebounceOptions,
  type ThrottleOptions,
  type RetryOptions
} from './common'

// 日期时间工具
export {
  parseDate,
  getRelativeTime,
  getDiff,
  addTime
} from './date'

// 字符串工具
export {
  StringUtils
} from './string'

// 数字工具
export {
  NumberUtils
} from './number'

// 验证工具
export {
  BaseValidator,
  RegexPatterns,
  Validator
} from './validate'

// 数组工具
export {
  ArrayUtils
} from './array'

// 对象工具
export {
  ObjectUtils
} from './object'

// 认证工具
export {
  AuthUtils,
  TokenManager,
  type AuthConfig,
  type TokenInfo,
  type UserInfo
} from './auth'

// 头像工具
export {
  AvatarUtils,
  type AvatarOptions
} from './avatar'

// 动画工具
export {
  AnimationUtils,
  type AnimationConfig,
  type EasingFunction
} from './animation'

// 图表工具
export {
  ChartUtils,
  type ChartData,
  type ChartOptions
} from './chart'

// 转换工具
export {
  ConvertUtils
} from './convert'

// 加密工具
export {
  CryptoUtils,
  type HashOptions,
  type EncryptOptions
} from './crypto'

// 设备检测
export {
  DeviceUtils,
  type DeviceInfo,
  type BrowserInfo
} from './device'

// DOM 工具
export {
  DOMUtils,
  type ElementPosition,
  type ScrollOptions
} from './dom'

// 事件工具
export {
  EventUtils,
  type EventOptions
} from './event'

// 文件工具
export {
  FileUtils,
  type FileInfo,
  type UploadOptions,
  formatFileSize
} from './file'

// 表单工具
export {
  FormUtils,
  type FormData,
  type ValidationRule
} from './form'

// HTTP 工具
export {
  http,
  createHttpClient,
  request as httpRequest,
  type RequestConfig,
  type ResponseData
} from './http'

// 数学工具
export {
  MathUtils
} from './math'

// 请求工具
export {
  request as apiRequest,
  get,
  post,
  put,
  del,
  getPage,
  postPageList,
  postPageListSimple,
  isSuccess,
  getData,
  getErrorMessage,
  type ApiResponse,
  type RequestOptions
} from './request'

// 存储工具
export {
  StorageUtils,
  storage as storageUtils,
  sessionStorage as sessionStorageUtils,
  memoryStorage as memoryStorageUtils,
  createStorage,
  type StorageInterface,
  type StorageConfig
} from './storage'

// URL 工具
export {
  UrlUtils,
  type UrlParts,
  type QueryParams
} from './url'

// WebSocket 工具
export {
  WebSocketUtils,
  type WebSocketConfig,
  type MessageHandler
} from './websocket'

// Worker 工具
export {
  WorkerUtils,
  type WorkerConfig,
  type WorkerMessage
} from './worker'

// 使用示例和文档（注释掉，因为markdown文件不能直接导出）
// export { default as UsageExamples } from './usage-examples.md'

/**
 * 工具函数版本信息
 */
export const UTILS_VERSION = '2.0.0'

/**
 * 工具函数分类
 */
export const UTILS_CATEGORIES = {
  FORMAT: 'format',
  COMMON: 'common',
  DATE: 'date',
  STRING: 'string',
  NUMBER: 'number',
  VALIDATION: 'validation',
  ARRAY: 'array',
  OBJECT: 'object',
  AUTH: 'auth',
  AVATAR: 'avatar',
  ANIMATION: 'animation',
  CHART: 'chart',
  CONVERT: 'convert',
  CRYPTO: 'crypto',
  DEVICE: 'device',
  DOM: 'dom',
  EVENT: 'event',
  FILE: 'file',
  FORM: 'form',
  HTTP: 'http',
  MATH: 'math',
  REQUEST: 'request',
  STORAGE: 'storage',
  URL: 'url',
  WEBSOCKET: 'websocket',
  WORKER: 'worker'
} as const

/**
 * 获取所有可用的工具函数列表
 * @returns 工具函数列表
 */
export function getAvailableUtils(): string[] {
  return Object.values(UTILS_CATEGORIES)
}

/**
 * 检查工具函数是否可用
 * @param utilName 工具函数名称
 * @returns 是否可用
 */
export function isUtilAvailable(utilName: string): boolean {
  return Object.values(UTILS_CATEGORIES).includes(utilName as any)
}