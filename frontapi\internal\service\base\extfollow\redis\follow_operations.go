package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"frontapi/internal/service/base/extfollow/types"
	"frontapi/pkg/redis"
)

// FollowOperations 关注操作处理器
type FollowOperations struct {
	client   *redis.Client
	cacheKey *types.CacheKey
	config   *Config
}

// NewFollowOperations 创建关注操作处理器
func NewFollowOperations(client *redis.Client, cacheKey *types.CacheKey, config *Config) *FollowOperations {
	return &FollowOperations{
		client:   client,
		cacheKey: cacheKey,
		config:   config,
	}
}

// Follow 关注用户
func (f *FollowOperations) Follow(ctx context.Context, followerID, followeeID string) error {
	if followerID == followeeID {
		return types.ErrSelfFollow
	}

	// 使用 Redis 事务确保操作的原子性
	pipe := f.client.TxPipeline()

	// 1. 设置关注关系
	followKey := f.cacheKey.FollowKey(followerID, followeeID)
	pipe.Set(ctx, followKey, "1", f.config.DefaultTTL)

	// 2. 更新关注者的关注列表
	followingKey := f.cacheKey.FollowingKey(followerID)
	pipe.SAdd(ctx, followingKey, followeeID)
	pipe.Expire(ctx, followingKey, f.config.DefaultTTL)

	// 3. 更新被关注者的粉丝列表
	followersKey := f.cacheKey.FollowersKey(followeeID)
	pipe.SAdd(ctx, followersKey, followerID)
	pipe.Expire(ctx, followersKey, f.config.DefaultTTL)

	// 4. 增加关注数和粉丝数
	followingCountKey := f.cacheKey.FollowingCountKey(followerID)
	followerCountKey := f.cacheKey.FollowerCountKey(followeeID)
	pipe.Incr(ctx, followingCountKey)
	pipe.Incr(ctx, followerCountKey)
	pipe.Expire(ctx, followingCountKey, f.config.DefaultTTL)
	pipe.Expire(ctx, followerCountKey, f.config.DefaultTTL)

	// 5. 记录关注历史
	historyKey := "follow_history:" + followerID
	followRecord := &types.FollowRecord{
		FollowerID: followerID,
		FolloweeID: followeeID,
		Status:     "following",
		Timestamp:  time.Now(),
		Source:     "api",
		Version:    1,
	}

	recordData, err := json.Marshal(followRecord)
	if err != nil {
		return fmt.Errorf("序列化关注记录失败: %w", err)
	}

	pipe.ZAdd(ctx, historyKey, &redis.Z{
		Score:  float64(time.Now().Unix()),
		Member: string(recordData),
	})
	pipe.Expire(ctx, historyKey, 30*24*time.Hour) // 保留30天历史

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("执行关注操作失败: %w", err)
	}

	return nil
}

// Unfollow 取消关注
func (f *FollowOperations) Unfollow(ctx context.Context, followerID, followeeID string) error {
	if followerID == followeeID {
		return types.ErrSelfFollow
	}

	// 检查是否已关注
	isFollowing, err := f.IsFollowing(ctx, followerID, followeeID)
	if err != nil {
		return fmt.Errorf("检查关注状态失败: %w", err)
	}
	if !isFollowing {
		return types.ErrNotFollowing
	}

	// 使用 Redis 事务确保操作的原子性
	pipe := f.client.TxPipeline()

	// 1. 删除关注关系
	followKey := f.cacheKey.FollowKey(followerID, followeeID)
	pipe.Del(ctx, followKey)

	// 2. 从关注者的关注列表中移除
	followingKey := f.cacheKey.FollowingKey(followerID)
	pipe.SRem(ctx, followingKey, followeeID)

	// 3. 从被关注者的粉丝列表中移除
	followersKey := f.cacheKey.FollowersKey(followeeID)
	pipe.SRem(ctx, followersKey, followerID)

	// 4. 减少关注数和粉丝数
	followingCountKey := f.cacheKey.FollowingCountKey(followerID)
	followerCountKey := f.cacheKey.FollowerCountKey(followeeID)
	pipe.Decr(ctx, followingCountKey)
	pipe.Decr(ctx, followerCountKey)

	// 5. 记录取关历史
	historyKey := "follow_history:" + followerID
	unfollowRecord := &types.FollowRecord{
		FollowerID: followerID,
		FolloweeID: followeeID,
		Status:     "unfollowed",
		Timestamp:  time.Now(),
		Source:     "api",
		Version:    1,
	}

	recordData, err := json.Marshal(unfollowRecord)
	if err != nil {
		return fmt.Errorf("序列化取关记录失败: %w", err)
	}

	pipe.ZAdd(ctx, historyKey, &redis.Z{
		Score:  float64(time.Now().Unix()),
		Member: string(recordData),
	})

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("执行取关操作失败: %w", err)
	}

	return nil
}

// IsFollowing 检查是否关注
func (f *FollowOperations) IsFollowing(ctx context.Context, followerID, followeeID string) (bool, error) {
	if followerID == followeeID {
		return false, nil
	}

	followKey := f.cacheKey.FollowKey(followerID, followeeID)
	result, err := f.client.Get(ctx, followKey).Result()
	if err != nil {
		if err == redis.Nil {
			return false, nil
		}
		return false, fmt.Errorf("检查关注状态失败: %w", err)
	}

	return result == "1", nil
}

// GetFollowerCount 获取粉丝数
func (f *FollowOperations) GetFollowerCount(ctx context.Context, userID string) (int64, error) {
	followerCountKey := f.cacheKey.FollowerCountKey(userID)
	result, err := f.client.Get(ctx, followerCountKey).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		return 0, fmt.Errorf("获取粉丝数失败: %w", err)
	}

	count, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("解析粉丝数失败: %w", err)
	}

	return count, nil
}

// GetFollowingCount 获取关注数
func (f *FollowOperations) GetFollowingCount(ctx context.Context, userID string) (int64, error) {
	followingCountKey := f.cacheKey.FollowingCountKey(userID)
	result, err := f.client.Get(ctx, followingCountKey).Result()
	if err != nil {
		if err == redis.Nil {
			return 0, nil
		}
		return 0, fmt.Errorf("获取关注数失败: %w", err)
	}

	count, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("解析关注数失败: %w", err)
	}

	return count, nil
}

// BatchFollow 批量关注
func (f *FollowOperations) BatchFollow(ctx context.Context, operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	// 验证操作
	if err := types.ValidateBatchOperations(operations); err != nil {
		return err
	}

	// 分批处理
	batchSize := f.config.BatchSize
	for i := 0; i < len(operations); i += batchSize {
		end := i + batchSize
		if end > len(operations) {
			end = len(operations)
		}

		batch := operations[i:end]
		if err := f.processBatchFollow(ctx, batch); err != nil {
			return fmt.Errorf("批量关注处理失败 (批次 %d-%d): %w", i, end-1, err)
		}
	}

	return nil
}

// processBatchFollow 处理批量关注
func (f *FollowOperations) processBatchFollow(ctx context.Context, operations []*types.FollowOperation) error {
	pipe := f.client.TxPipeline()

	for _, op := range operations {
		// 设置关注关系
		followKey := f.cacheKey.FollowKey(op.FollowerID, op.FolloweeID)
		pipe.Set(ctx, followKey, "1", f.config.DefaultTTL)

		// 更新关注列表
		followingKey := f.cacheKey.FollowingKey(op.FollowerID)
		pipe.SAdd(ctx, followingKey, op.FolloweeID)
		pipe.Expire(ctx, followingKey, f.config.DefaultTTL)

		// 更新粉丝列表
		followersKey := f.cacheKey.FollowersKey(op.FolloweeID)
		pipe.SAdd(ctx, followersKey, op.FollowerID)
		pipe.Expire(ctx, followersKey, f.config.DefaultTTL)

		// 更新计数器
		followingCountKey := f.cacheKey.FollowingCountKey(op.FollowerID)
		followerCountKey := f.cacheKey.FollowerCountKey(op.FolloweeID)
		pipe.Incr(ctx, followingCountKey)
		pipe.Incr(ctx, followerCountKey)
		pipe.Expire(ctx, followingCountKey, f.config.DefaultTTL)
		pipe.Expire(ctx, followerCountKey, f.config.DefaultTTL)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// BatchUnfollow 批量取消关注
func (f *FollowOperations) BatchUnfollow(ctx context.Context, operations []*types.FollowOperation) error {
	if len(operations) == 0 {
		return nil
	}

	// 验证操作
	if err := types.ValidateBatchOperations(operations); err != nil {
		return err
	}

	// 分批处理
	batchSize := f.config.BatchSize
	for i := 0; i < len(operations); i += batchSize {
		end := i + batchSize
		if end > len(operations) {
			end = len(operations)
		}

		batch := operations[i:end]
		if err := f.processBatchUnfollow(ctx, batch); err != nil {
			return fmt.Errorf("批量取关处理失败 (批次 %d-%d): %w", i, end-1, err)
		}
	}

	return nil
}

// processBatchUnfollow 处理批量取关
func (f *FollowOperations) processBatchUnfollow(ctx context.Context, operations []*types.FollowOperation) error {
	pipe := f.client.TxPipeline()

	for _, op := range operations {
		// 删除关注关系
		followKey := f.cacheKey.FollowKey(op.FollowerID, op.FolloweeID)
		pipe.Del(ctx, followKey)

		// 从关注列表中移除
		followingKey := f.cacheKey.FollowingKey(op.FollowerID)
		pipe.SRem(ctx, followingKey, op.FolloweeID)

		// 从粉丝列表中移除
		followersKey := f.cacheKey.FollowersKey(op.FolloweeID)
		pipe.SRem(ctx, followersKey, op.FollowerID)

		// 更新计数器
		followingCountKey := f.cacheKey.FollowingCountKey(op.FollowerID)
		followerCountKey := f.cacheKey.FollowerCountKey(op.FolloweeID)
		pipe.Decr(ctx, followingCountKey)
		pipe.Decr(ctx, followerCountKey)
	}

	_, err := pipe.Exec(ctx)
	return err
}

// BatchGetFollowStatus 批量获取关注状态
func (f *FollowOperations) BatchGetFollowStatus(ctx context.Context, followerID string, userIDs []string) (map[string]bool, error) {
	if len(userIDs) == 0 {
		return make(map[string]bool), nil
	}

	result := make(map[string]bool)

	// 构建批量查询
	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = f.cacheKey.FollowKey(followerID, userID)
	}

	// 批量获取
	pipe := f.client.Pipeline()
	cmds := make([]*redis.StringCmd, len(keys))
	for i, key := range keys {
		cmds[i] = pipe.Get(ctx, key)
	}

	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("批量获取关注状态失败: %w", err)
	}

	// 解析结果
	for i, cmd := range cmds {
		userID := userIDs[i]
		val, err := cmd.Result()
		if err != nil {
			if err == redis.Nil {
				result[userID] = false
			} else {
				return nil, fmt.Errorf("获取用户 %s 关注状态失败: %w", userID, err)
			}
		} else {
			result[userID] = val == "1"
		}
	}

	return result, nil
}

// BatchGetFollowerCounts 批量获取粉丝数
func (f *FollowOperations) BatchGetFollowerCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	return f.batchGetCounts(ctx, userIDs, f.cacheKey.FollowerCountKey)
}

// BatchGetFollowingCounts 批量获取关注数
func (f *FollowOperations) BatchGetFollowingCounts(ctx context.Context, userIDs []string) (map[string]int64, error) {
	return f.batchGetCounts(ctx, userIDs, f.cacheKey.FollowingCountKey)
}

// batchGetCounts 批量获取计数
func (f *FollowOperations) batchGetCounts(ctx context.Context, userIDs []string, keyFunc func(string) string) (map[string]int64, error) {
	if len(userIDs) == 0 {
		return make(map[string]int64), nil
	}

	result := make(map[string]int64)

	// 构建批量查询
	keys := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keys[i] = keyFunc(userID)
	}

	// 批量获取
	pipe := f.client.Pipeline()
	cmds := make([]*redis.StringCmd, len(keys))
	for i, key := range keys {
		cmds[i] = pipe.Get(ctx, key)
	}

	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, fmt.Errorf("批量获取计数失败: %w", err)
	}

	// 解析结果
	for i, cmd := range cmds {
		userID := userIDs[i]
		val, err := cmd.Result()
		if err != nil {
			if err == redis.Nil {
				result[userID] = 0
			} else {
				return nil, fmt.Errorf("获取用户 %s 计数失败: %w", userID, err)
			}
		} else {
			count, parseErr := strconv.ParseInt(val, 10, 64)
			if parseErr != nil {
				result[userID] = 0
			} else {
				result[userID] = count
			}
		}
	}

	return result, nil
}
