import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点定义
export const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
} as const

export type Breakpoint = keyof typeof breakpoints

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// 屏幕尺寸信息
export interface ScreenInfo {
  width: number
  height: number
  breakpoint: Breakpoint
  device: DeviceType
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  orientation: 'portrait' | 'landscape'
  pixelRatio: number
}

// 响应式配置
export interface ResponsiveConfig {
  mobile: any
  tablet?: any
  desktop?: any
}

/**
 * 响应式设计 Composable
 * 提供屏幕尺寸检测、断点判断、设备类型识别等功能
 */
export function useResponsive() {
  const windowWidth = ref(0)
  const windowHeight = ref(0)
  const pixelRatio = ref(1)

  // 当前断点
  const currentBreakpoint = computed<Breakpoint>(() => {
    const width = windowWidth.value
    if (width >= breakpoints.xxl) return 'xxl'
    if (width >= breakpoints.xl) return 'xl'
    if (width >= breakpoints.lg) return 'lg'
    if (width >= breakpoints.md) return 'md'
    if (width >= breakpoints.sm) return 'sm'
    return 'xs'
  })

  // 设备类型
  const deviceType = computed<DeviceType>(() => {
    const width = windowWidth.value
    if (width < breakpoints.md) return 'mobile'
    if (width < breakpoints.lg) return 'tablet'
    return 'desktop'
  })

  // 设备判断
  const isMobile = computed(() => deviceType.value === 'mobile')
  const isTablet = computed(() => deviceType.value === 'tablet')
  const isDesktop = computed(() => deviceType.value === 'desktop')

  // 屏幕方向
  const orientation = computed(() => {
    return windowWidth.value > windowHeight.value ? 'landscape' : 'portrait'
  })

  // 断点判断函数
  const isBreakpoint = (bp: Breakpoint) => {
    return currentBreakpoint.value === bp
  }

  const isBreakpointUp = (bp: Breakpoint) => {
    return windowWidth.value >= breakpoints[bp]
  }

  const isBreakpointDown = (bp: Breakpoint) => {
    return windowWidth.value < breakpoints[bp]
  }

  const isBreakpointBetween = (min: Breakpoint, max: Breakpoint) => {
    return windowWidth.value >= breakpoints[min] && windowWidth.value < breakpoints[max]
  }

  // 屏幕信息
  const screenInfo = computed<ScreenInfo>(() => ({
    width: windowWidth.value,
    height: windowHeight.value,
    breakpoint: currentBreakpoint.value,
    device: deviceType.value,
    isMobile: isMobile.value,
    isTablet: isTablet.value,
    isDesktop: isDesktop.value,
    orientation: orientation.value,
    pixelRatio: pixelRatio.value
  }))

  // 响应式值获取
  const getResponsiveValue = <T>(config: ResponsiveConfig | T): T => {
    if (typeof config !== 'object' || config === null) {
      return config as T
    }

    const responsiveConfig = config as ResponsiveConfig
    
    if (isMobile.value && responsiveConfig.mobile !== undefined) {
      return responsiveConfig.mobile
    }
    
    if (isTablet.value && responsiveConfig.tablet !== undefined) {
      return responsiveConfig.tablet
    }
    
    if (isDesktop.value && responsiveConfig.desktop !== undefined) {
      return responsiveConfig.desktop
    }
    
    // 回退到 mobile 值
    return responsiveConfig.mobile
  }

  // 更新窗口尺寸
  const updateWindowSize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    pixelRatio.value = window.devicePixelRatio || 1
  }

  // 监听窗口大小变化
  let resizeObserver: ResizeObserver | null = null
  
  const startListening = () => {
    updateWindowSize()
    
    // 使用 ResizeObserver 监听窗口变化（更精确）
    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(() => {
        updateWindowSize()
      })
      resizeObserver.observe(document.documentElement)
    } else {
      // 回退到 resize 事件
      window.addEventListener('resize', updateWindowSize)
    }
    
    // 监听屏幕方向变化
    window.addEventListener('orientationchange', updateWindowSize)
  }

  const stopListening = () => {
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    } else {
      window.removeEventListener('resize', updateWindowSize)
    }
    window.removeEventListener('orientationchange', updateWindowSize)
  }

  onMounted(() => {
    startListening()
  })

  onUnmounted(() => {
    stopListening()
  })

  return {
    // 响应式数据
    windowWidth: readonly(windowWidth),
    windowHeight: readonly(windowHeight),
    pixelRatio: readonly(pixelRatio),
    currentBreakpoint: readonly(currentBreakpoint),
    deviceType: readonly(deviceType),
    orientation: readonly(orientation),
    screenInfo: readonly(screenInfo),
    
    // 设备判断
    isMobile: readonly(isMobile),
    isTablet: readonly(isTablet),
    isDesktop: readonly(isDesktop),
    
    // 断点判断
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    isBreakpointBetween,
    
    // 工具函数
    getResponsiveValue,
    updateWindowSize,
    startListening,
    stopListening
  }
}

/**
 * 窗口尺寸 Composable
 * 简化版本，只提供窗口尺寸信息
 */
export function useWindowSize() {
  const { windowWidth, windowHeight, updateWindowSize } = useResponsive()
  
  return {
    width: windowWidth,
    height: windowHeight,
    updateSize: updateWindowSize
  }
}

/**
 * 媒体查询 Composable
 * 提供 CSS 媒体查询的 JavaScript 实现
 */
export function useMediaQuery(query: string) {
  const matches = ref(false)
  let mediaQuery: MediaQueryList | null = null

  const updateMatches = () => {
    if (mediaQuery) {
      matches.value = mediaQuery.matches
    }
  }

  onMounted(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      mediaQuery = window.matchMedia(query)
      matches.value = mediaQuery.matches
      
      // 监听媒体查询变化
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', updateMatches)
      } else {
        // 兼容旧版本浏览器
        mediaQuery.addListener(updateMatches)
      }
    }
  })

  onUnmounted(() => {
    if (mediaQuery) {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', updateMatches)
      } else {
        mediaQuery.removeListener(updateMatches)
      }
    }
  })

  return readonly(matches)
}

/**
 * 设备检测 Composable
 * 检测设备类型和特性
 */
export function useDeviceDetection() {
  const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : ''
  
  const isIOS = /iPad|iPhone|iPod/.test(userAgent)
  const isAndroid = /Android/.test(userAgent)
  const isMacOS = /Mac/.test(userAgent)
  const isWindows = /Win/.test(userAgent)
  const isLinux = /Linux/.test(userAgent)
  
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent)
  const isChrome = /Chrome/.test(userAgent)
  const isFirefox = /Firefox/.test(userAgent)
  const isEdge = /Edge/.test(userAgent)
  
  const isTouchDevice = typeof window !== 'undefined' && 'ontouchstart' in window
  const hasHover = typeof window !== 'undefined' && window.matchMedia('(hover: hover)').matches
  
  return {
    // 操作系统
    isIOS,
    isAndroid,
    isMacOS,
    isWindows,
    isLinux,
    
    // 浏览器
    isSafari,
    isChrome,
    isFirefox,
    isEdge,
    
    // 设备特性
    isTouchDevice,
    hasHover,
    userAgent
  }
}

// 导出常用的响应式工具函数
export const isMobile = () => {
  if (typeof window === 'undefined') return false
  return window.innerWidth < breakpoints.md
}

export const isTablet = () => {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= breakpoints.md && window.innerWidth < breakpoints.lg
}

export const isDesktop = () => {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= breakpoints.lg
}

export const getCurrentBreakpoint = (): Breakpoint => {
  if (typeof window === 'undefined') return 'lg'
  
  const width = window.innerWidth
  if (width >= breakpoints.xxl) return 'xxl'
  if (width >= breakpoints.xl) return 'xl'
  if (width >= breakpoints.lg) return 'lg'
  if (width >= breakpoints.md) return 'md'
  if (width >= breakpoints.sm) return 'sm'
  return 'xs'
}