package admin

import (
	"frontapi/internal/admin/users"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterUserRoutes 注册用户管理相关路由
func RegisterUserRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建用户控制器
	userController := users.NewUserController(services.UserService)

	// 用户管理路由组
	userRoutes := apiGroup.Group("/users", middleware.AuthRequired())
	{
		// 用户管理接口
		userRoutes.Post("/list", userController.ListUsers)
		userRoutes.Post("/detail/:id?", userController.GetUser)
		userRoutes.Post("/add", userController.CreateUser)
		userRoutes.Post("/update", userController.UpdateUser)
		userRoutes.Post("/update-status", userController.UpdateUserStatus)
		userRoutes.Post("/delete/:id?", userController.DeleteUser)
		userRoutes.Post("/batch-update-status", userController.BatchUpdateUserStatus)
		userRoutes.Post("/batch-delete", userController.BatchDeleteUser)
	}

	// 用户登录日志路由组
	loginLogController := users.NewUserLoginLogController(services.UserLoginLogsService, services.UserService)
	loginLogRoutes := apiGroup.Group("/users/login-logs", middleware.AuthRequired())
	{
		// 登录日志管理接口
		loginLogRoutes.Post("/list", loginLogController.ListLoginLogs)
		loginLogRoutes.Post("/clear", loginLogController.ClearLoginLogs)
	}

}
