package users

import (
	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// UserWatchVideoHistoryRepository 用户观看视频历史数据访问接口
type UserWatchVideoHistoryRepository interface {
	base.ExtendedRepository[users.UserWatchVideoHistory]
}

// userWatchVideoHistoryRepository 用户观看视频历史数据访问实现
type userWatchVideoHistoryRepository struct {
	base.ExtendedRepository[users.UserWatchVideoHistory]
}

func NewUserWatchVideoHistoryRepository(db *gorm.DB) UserWatchVideoHistoryRepository {
	return &userWatchVideoHistoryRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserWatchVideoHistory](db),
	}
}
