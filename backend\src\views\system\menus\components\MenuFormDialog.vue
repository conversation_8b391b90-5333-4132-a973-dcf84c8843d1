<template>
  <el-dialog
    :title="getDialogTitle"
    v-model="dialogVisible"
    width="650px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      size="default"
    >
      <el-form-item label="上级菜单" prop="parent">
        <el-cascader
          v-model="form.parent"
          :options="menuOptions"
          :props="{ 
            expandTrigger: 'hover',
            value: 'id',
            label: 'name',
            emitPath: false,
            checkStrictly: true
          }"
          placeholder="顶级菜单"
          clearable
          style="width: 100%"
          :disabled="dialogType === 'addChild'"
        ></el-cascader>
      </el-form-item>

      <el-form-item label="菜单类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio :label="0">目录</el-radio>
          <el-radio :label="1">菜单</el-radio>
          <el-radio :label="2">按钮</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="菜单名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入菜单名称"></el-input>
      </el-form-item>

      <el-form-item label="路由地址" prop="path" v-if="form.type !== 2">
        <el-input v-model="form.path" placeholder="请输入路由地址"></el-input>
      </el-form-item>

      <el-form-item label="权限标识" prop="permission" v-if="form.type === 2">
        <el-input v-model="form.permission" placeholder="请输入权限标识"></el-input>
      </el-form-item>

      <el-form-item label="组件路径" prop="component" v-if="form.type === 1">
        <el-input v-model="form.component" placeholder="请输入组件路径"></el-input>
      </el-form-item>

      <el-form-item label="图标" prop="icon" v-if="form.type !== 2">
        <el-input v-model="form.icon" placeholder="请输入图标类名">
          <template #prefix v-if="form.icon">
            <i :class="form.icon" style="margin-right: 5px;"></i>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="排序" prop="orderNo">
        <el-input-number v-model="form.orderNo" :min="0" :max="9999" style="width: 150px;"></el-input-number>
      </el-form-item>

      <el-form-item label="显示状态" prop="show" v-if="form.type !== 2">
        <el-radio-group v-model="form.show">
          <el-radio :label="1">显示</el-radio>
          <el-radio :label="0">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="页面缓存" prop="keepalive" v-if="form.type === 1">
        <el-radio-group v-model="form.keepalive">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="外部链接" prop="external" v-if="form.type === 1">
        <el-radio-group v-model="form.external">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, ElForm } from 'element-plus';
import { createMenu, updateMenu, getMenuTree } from '@/service/api/permission/menus';
import type { Menu, CreateMenuRequest, UpdateMenuRequest } from '@/types/menus';

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'add'
  },
  menuData: {
    type: Object as () => Menu | null,
    default: null
  },
  parentMenu: {
    type: Object as () => Menu | null,
    default: null
  }
});

// 定义emits
const emit = defineEmits(['close', 'success']);

// 状态变量
const loading = ref(false);
const dialogVisible = computed(() => props.visible);
const dialogType = computed(() => props.type);
const formRef = ref<InstanceType<typeof ElForm> | null>(null);
const menuOptions = ref<Menu[]>([]);

// 表单数据
const form = reactive<CreateMenuRequest & UpdateMenuRequest>({
  id: 0,
  parent: null,
  path: '',
  name: '',
  permission: '',
  type: 1,
  icon: '',
  orderNo: 0,
  component: '',
  keepalive: 0,
  show: 1,
  status: 1,
  external: 0
});

// 验证规则
const rules = {
  name: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
    { max: 50, message: '菜单名称长度不能超过50个字符', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请输入路由地址', trigger: 'blur' },
    { max: 200, message: '路由地址长度不能超过200个字符', trigger: 'blur' }
  ],
  permission: [
    { max: 100, message: '权限标识长度不能超过100个字符', trigger: 'blur' }
  ],
  component: [
    { max: 255, message: '组件路径长度不能超过255个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择菜单类型', trigger: 'change' }
  ]
};

// 计算属性 - 对话框标题
const getDialogTitle = computed(() => {
  if (dialogType.value === 'add') {
    return '添加菜单';
  } else if (dialogType.value === 'edit') {
    return '编辑菜单';
  } else if (dialogType.value === 'addChild') {
    return `添加子菜单 - ${props.parentMenu?.name || ''}`;
  }
  return '';
});

// 监听菜单类型变化
watch(() => form.type, (val) => {
  if (val === 2) { // 按钮类型
    form.show = 0;
    form.path = '';
    form.component = '';
    form.icon = '';
    form.keepalive = 0;
    form.external = 0;
  }
});

// 监听props变化，初始化表单数据
watch(() => props.visible, (val) => {
  if (val) {
    resetForm();
    fetchMenuTreeData();
    
    if (dialogType.value === 'edit' && props.menuData) {
      initFormData(props.menuData);
    } else if (dialogType.value === 'addChild' && props.parentMenu) {
      form.parent = props.parentMenu.id;
      form.type = props.parentMenu.type === 0 ? 1 : 2; // 如果父级是目录，默认为菜单，否则为按钮
    }
  }
});

// 获取菜单树形数据
const fetchMenuTreeData = async () => {
  try {
    const res = await getMenuTree();
    if (res && res.data) {
      menuOptions.value = res.data;
    }
  } catch (error) {
    console.error('获取菜单树形数据失败:', error);
  }
};

// 初始化表单数据
const initFormData = (menu: Menu) => {
  form.id = menu.id;
  form.parent = menu.parent || null;
  form.path = menu.path;
  form.name = menu.name;
  form.permission = menu.permission;
  form.type = menu.type;
  form.icon = menu.icon;
  form.orderNo = menu.orderNo;
  form.component = menu.component;
  form.keepalive = menu.keepalive;
  form.show = menu.show;
  form.status = menu.status;
  form.external = menu.external;
};

// 重置表单
const resetForm = () => {
  form.id = 0;
  form.parent = null;
  form.path = '';
  form.name = '';
  form.permission = '';
  form.type = 1;
  form.icon = '';
  form.orderNo = 0;
  form.component = '';
  form.keepalive = 0;
  form.show = 1;
  form.status = 1;
  form.external = 0;
  formRef.value?.resetFields();
};

// 关闭对话框
const handleClose = () => {
  emit('close');
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    
    loading.value = true;
    try {
      let res;
      
      if (dialogType.value === 'edit') {
        // 编辑菜单
        const data: UpdateMenuRequest = {
          id: form.id,
          parent: form.parent,
          path: form.path,
          name: form.name,
          permission: form.permission,
          type: form.type,
          icon: form.icon,
          orderNo: form.orderNo,
          component: form.component,
          keepalive: form.keepalive,
          show: form.show,
          status: form.status,
          external: form.external
        };
        res = await updateMenu(data);
      } else {
        // 添加菜单（包括子菜单）
        const data: CreateMenuRequest = {
          parent: form.parent,
          path: form.path,
          name: form.name,
          permission: form.permission,
          type: form.type,
          icon: form.icon,
          orderNo: form.orderNo,
          component: form.component,
          keepalive: form.keepalive,
          show: form.show,
          status: form.status,
          external: form.external
        };
        res = await createMenu(data);
      }
      
      if (res && res.code === 2000) {
        ElMessage.success(dialogType.value === 'edit' ? '更新菜单成功' : '添加菜单成功');
        emit('success');
      } else {
        ElMessage.error((res && res.message) || (dialogType.value === 'edit' ? '更新菜单失败' : '添加菜单失败'));
      }
    } catch (error) {
      console.error(dialogType.value === 'edit' ? '更新菜单出错:' : '添加菜单出错:', error);
      ElMessage.error(dialogType.value === 'edit' ? '更新菜单失败' : '添加菜单失败');
    } finally {
      loading.value = false;
    }
  });
};

// 生命周期钩子
onMounted(() => {
  // 如果对话框可见，初始化数据
  if (dialogVisible.value) {
    fetchMenuTreeData();
  }
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 