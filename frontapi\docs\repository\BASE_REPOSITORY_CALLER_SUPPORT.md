# BaseRepository Caller 支持功能总结

## 概述

本文档总结了在 `BaseRepository` 中添加的 caller 支持功能，使得直接使用 `BaseRepository` 的仓库也能够实现自定义条件应用逻辑。

## 修改内容

### 1. BaseRepository 接口修改

在 `BaseRepository` 接口中添加了 `SetCaller` 方法：

```go
type BaseRepository[T models.BaseModelConstraint] interface {
    // ... 其他方法
    
    // 设置调用者实例，用于支持条件应用的自定义
    SetCaller(caller interface{})
}
```

### 2. baseRepository 结构体修改

在 `baseRepository` 结构体中添加了 `caller` 字段：

```go
type baseRepository[T models.BaseModelConstraint] struct {
    db     *gorm.DB
    caller interface{} // 用于存储具体的仓库实例，支持条件应用的自定义
}
```

### 3. SetCaller 方法实现

```go
// SetCaller 设置调用者实例，用于支持条件应用的自定义
func (r *baseRepository[T]) SetCaller(caller interface{}) {
    r.caller = caller
}
```

### 4. applyConditions 方法修改

修改了 `applyConditions` 方法，使其使用 `r.caller` 而不是 `nil`：

```go
func (r *baseRepository[T]) applyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    return r.applyConditionsWithCaller(query, condition, r.caller)
}
```

### 5. 辅助函数

添加了 `NewBaseRepositoryWithCaller` 辅助函数，用于简化创建过程：

```go
// NewBaseRepositoryWithCaller 创建带有调用者设置的基础仓库实例
func NewBaseRepositoryWithCaller[T models.BaseModelConstraint](db *gorm.DB, caller interface{}) BaseRepository[T] {
    repo := &baseRepository[T]{
        db:     db,
        caller: caller,
    }
    return repo
}
```

## 使用示例

### 对于直接使用 BaseRepository 的仓库

**方式一：使用辅助函数**
```go
func NewYourRepository(db *gorm.DB) YourRepository {
    repo := &yourRepository{}
    repo.BaseRepository = base.NewBaseRepositoryWithCaller[YourModel](db, repo)
    return repo
}

// 实现自定义条件应用
func (r *yourRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    // 自定义条件逻辑
    return query
}
```

**方式二：先创建再设置**
```go
func NewYourRepository(db *gorm.DB) YourRepository {
    repo := &yourRepository{
        BaseRepository: base.NewBaseRepository[YourModel](db),
    }
    repo.SetCaller(repo)
    return repo
}

// 实现自定义条件应用
func (r *yourRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
    // 自定义条件逻辑
    return query
}
```

## 工作流程

1. **创建仓库实例**：使用 `NewBaseRepository` 或 `NewBaseRepositoryWithCaller` 创建仓库
2. **设置Caller**：通过 `SetCaller` 方法或创建时设置 caller
3. **实现自定义条件**：在具体仓库中实现 `ApplyConditions` 方法
4. **自动应用**：当调用 `List`、`FindAll` 等方法时，基础仓库会自动检测并使用自定义的条件应用逻辑

## 兼容性

- **向后兼容**：现有的仓库无需修改即可继续使用
- **可选功能**：只有需要自定义条件应用的仓库才需要设置 caller
- **默认行为**：如果没有设置 caller 或没有实现 `ApplyConditions`，会使用默认的条件应用逻辑

## 注意事项

1. **循环引用**：确保在设置 caller 时不会产生循环引用
2. **类型安全**：使用接口检查确保类型安全
3. **性能影响**：接口检查会有轻微的性能开销，但在实际应用中可以忽略
4. **调试**：可以通过检查 caller 是否为 nil 来调试条件应用是否正常工作

## 测试验证

可以通过以下方式验证功能是否正常工作：

1. 在自定义的 `ApplyConditions` 方法中添加日志输出
2. 使用特殊的查询条件来验证是否使用了自定义逻辑
3. 对比设置 caller 前后的查询结果

## 相关文件

- `frontapi/internal/repository/base/base_repository.go` - 基础仓库实现
- `frontapi/internal/repository/base/extended_repository.go` - 扩展仓库实现
- `frontapi/internal/service/base/extended_service.go` - 扩展服务实现
- `frontapi/docs/repository/CUSTOM_CONDITIONS_GUIDE.md` - 自定义条件应用指南 