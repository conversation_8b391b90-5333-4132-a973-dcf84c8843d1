// 评论相关类型定义
// 基于 base.ts 中的基础类型，避免重复定义

import type { 
  BaseContent, 
  ContentStats, 
  ContentInteraction, 
  CompactUser, 
  MediaFile,
  QueryParams
} from './base'

// ==================== 评论类型 ====================

// 评论基础信息
export interface PostComment extends BaseContent, ContentStats, ContentInteraction {
  // 继承 BaseContent 的基础字段：id, content, authorId, createdAt, updatedAt, status
  // 继承 ContentStats 的统计字段：likeCount, commentCount 等
  // 继承 ContentInteraction 的互动状态：isLiked 等
  
  // 评论特有字段
  userId: string // 用户ID（与authorId相同，保持兼容性）
  userNickname: string // 用户昵称
  userAvatar: string // 用户头像
  parentId?: string // 父评论ID
  entityId: string // 关联实体ID（帖子、视频等）
  entityType: number // 关联实体类型：1-帖子 2-视频 3-短视频
  relationId?: string // 关联ID
  heat?: number // 热度
  replyCount?: number // 回复数
  
  // 媒体内容
  images?: string[]
  video?: string
  
  // 关联数据
  author?: CompactUser // 作者信息
  replies?: Comment[] // 回复列表
}

// 评论提交数据
export interface CommentSubmitData {
  content: string
  media?: MediaFile[]
  parentId?: string
  replyTo?: Comment | null
  targetId: string
  targetType: 'post' | 'video' | 'image' | 'shortvideo'
}

// 创建评论数据
export interface CreateCommentData {
  content: string
  replyTo?: Comment | null
  parentId?: string
  images?: string[]
  media?: MediaFile[]
  targetId: string
  targetType: 'post' | 'video' | 'image' | 'shortvideo'
}

// 评论响应数据
export interface CommentResponse {
  comments: Comment[]
  totalComments: number
  hasMore: boolean
  nextCursor?: string | number
}

// ==================== 评论组件相关类型 ====================

// 评论组件属性
export interface CommentBoxProps {
  // 基础配置
  targetId: string
  targetType: 'post' | 'video' | 'image' | 'shortvideo'
  
  // 数据传递
  comments?: Comment[] // 评论列表数据
  totalComments?: number // 总评论数
  loading?: boolean // 加载状态
  hasMore?: boolean // 是否有更多数据
  
  // 显示配置
  mode?: 'inline' | 'dialog' | 'compact'
  autoExpand?: boolean
  supportHeaderCollapse?: boolean
  maxHeight?: string
  disableDialog?: boolean // 是否禁用弹窗模式，默认false
  
  // 功能配置
  supportImage?: boolean
  supportVideo?: boolean
  placeholder?: string
  maxComments?: number
  
  // 样式配置
  theme?: 'light' | 'dark' | 'auto'
  
  // 权限配置
  allowAnonymous?: boolean
  requireLogin?: boolean
}

// 评论组件事件
export interface CommentBoxEmits {
  'comment-added': [comment: CreateCommentData]
  'comment-liked': [commentId: string, liked: boolean]
  'comment-replied': [parentId: string, reply: CreateCommentData]
  'dialog-opened': []
  'dialog-closed': []
  'expanded-changed': [expanded: boolean]
  'load-comments': [page: number] // 加载评论事件
  'load-more': [] // 加载更多事件
  'refresh': [] // 刷新事件
}

// ==================== 查询参数类型 ====================

// 评论查询参数
export interface CommentQueryParams extends QueryParams {
  data: {
    post_id?: string
    video_id?: string
    entity_id?: string
    entity_type?: number
    parent_id?: string
    keyword?: string
    user_id?: string
    status?: number
  }
}

// ==================== 兼容性类型 ====================

// 保持与旧版本的兼容性
export interface LegacyComment {
  id: string
  content: string
  createdAt: string | number
  likes: number
  isLiked: boolean
  author: {
    id: string
    username: string
    nickname?: string
    avatar?: string
    isVerified?: boolean
  }
  targetId: string
  targetType: 'post' | 'video' | 'image' | 'shortvideo'
  replyTo?: {
    id: string
    author: {
      id: string
      username: string
      nickname?: string
      avatar?: string
    }
  }
  replies?: LegacyComment[]
  images?: string[]
  parentId?: string
  media?: MediaFile[]
}