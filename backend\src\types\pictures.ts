import type { BaseModel, BaseParams } from '@/types/base';

// 图片类型
export interface Picture extends BaseModel {
    title: string;
    url: string;
    description?: string;
    category_id?: string;
    category_name?: string;
    album_id?: string;
    album_title?: string;
    width: number;
    height: number;
    size: number;
    view_count: number;
    like_count: number;
    share_count: number;
    status: number;
    reason?: string;
    created_at: string;
    updated_at: string;
}

// 分类类型
export interface PictureCategory {
    id: string;
    name: string;
    code: string;
    description?: string;
    status: number;
    sort_order: number;
    created_at: string;
    updated_at: string;
}

// 专辑类型
export interface PictureAlbum {
    id: string;
    title: string;
    description?: string;
    cover_url?: string;
    category_id?: string;
    category_name?: string;
    creator_name?: string;
    picture_count?: number;
    view_count?: number;
    like_count?: number;
    share_count?: number;
    comment_count?: number;
    is_paid?: number;
    price?: number;
    upload_time?: string;
    tags_json?: string;
    sort_order?: number;
    status: number;
    created_at: string;
    updated_at: string;
}

// 分页参数
export interface PageParams {
    pageNo: number;
    pageSize: number;
}

// 图片请求参数
export interface PictureParams extends BaseParams {
    data?: {
        title?: string;
        category_id?: string;
        album_id?: string;
        status?: string | number;
        start_date?: string;
        end_date?: string;
    };
}

// 图片分类请求参数
export interface PictureCategoryParams {
    page: PageParams;
    data?: {
        name?: string;
        status?: string | number;
    };
}

// 图片专辑请求参数
export interface PictureAlbumParams {
    page: PageParams;
    data?: {
        title?: string;
        category_id?: string;
        status?: string | number;
    };
}

// 创建图片请求
export interface CreatePictureRequest {
    url: string;
    title: string;
    description?: string;
    width?: number;
    height?: number;
    size?: number;
    category_id?: string;
    album_id?: string;
    status?: number;
}

// 更新图片请求
export interface UpdatePictureRequest {
    id: string;
    title?: string;
    description?: string;
    category_id?: string;
    album_id?: string;
    status?: number;
}

// 创建图片分类请求
export interface CreatePictureCategoryRequest {
    name: string;
    description?: string;
    icon?: string;
    sort_order?: number;
    status?: number;
}

// 更新图片分类请求
export interface UpdatePictureCategoryRequest {
    id: string;
    name?: string;
    description?: string;
    icon?: string;
    sort_order?: number;
    status?: number;
}

// 创建图片专辑请求
export interface CreatePictureAlbumRequest {
    title: string;
    description?: string;
    cover_url?: string;
    category_id?: string;
    sort_order?: number;
    status?: number;
}

// 更新图片专辑请求
export interface UpdatePictureAlbumRequest {
    id: string;
    title?: string;
    description?: string;
    cover_url?: string;
    category_id?: string;
    sort_order?: number;
    status?: number;
}

// 批量创建图片请求
export interface BatchCreatePicturesRequest {
    pictures: CreatePictureRequest[];
}
