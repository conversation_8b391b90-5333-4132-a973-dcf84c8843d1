package mongodb

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"frontapi/internal/service/base/extcollect/types"
)

// StatsOperations MongoDB统计操作处理器
type StatsOperations struct {
	collection *mongo.Collection
}

// NewStatsOperations 创建统计操作处理器
func NewStatsOperations(collection *mongo.Collection) *StatsOperations {
	return &StatsOperations{
		collection: collection,
	}
}

// GetUserCollectStats 获取用户收藏统计
func (s *StatsOperations) GetUserCollectStats(ctx context.Context, userID string) (*types.UserCollectStats, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"user_id": userID,
				"status":  "collected",
			},
		},
		{
			"$group": bson.M{
				"_id":            "$user_id",
				"total_collects": bson.M{"$sum": 1},
				"total_items":    bson.M{"$addToSet": "$item_id"},
				"collects_by_type": bson.M{
					"$push": bson.M{
						"type":  "$item_type",
						"count": 1,
					},
				},
				"first_collect_at": bson.M{"$min": "$timestamp"},
				"last_collect_at":  bson.M{"$max": "$timestamp"},
			},
		},
		{
			"$addFields": bson.M{
				"total_items": bson.M{"$size": "$total_items"},
			},
		},
	}

	cursor, err := s.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result struct {
			UserID         string                   `bson:"_id"`
			TotalCollects  int64                    `bson:"total_collects"`
			TotalItems     int64                    `bson:"total_items"`
			CollectsByType []map[string]interface{} `bson:"collects_by_type"`
			FirstCollectAt *interface{}             `bson:"first_collect_at"`
			LastCollectAt  *interface{}             `bson:"last_collect_at"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		stats := &types.UserCollectStats{
			UserID:        result.UserID,
			TotalCollects: result.TotalCollects,
			TotalItems:    result.TotalItems,
		}

		return stats, nil
	}

	// 如果没有找到数据，返回空统计
	return &types.UserCollectStats{
		UserID:        userID,
		TotalCollects: 0,
		TotalItems:    0,
	}, nil
}

// GetItemCollectStats 获取项目收藏统计
func (s *StatsOperations) GetItemCollectStats(ctx context.Context, itemID, itemType string) (*types.ItemCollectStats, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"item_id":   itemID,
				"item_type": itemType,
				"status":    "collected",
			},
		},
		{
			"$group": bson.M{
				"_id": bson.M{
					"item_id":   "$item_id",
					"item_type": "$item_type",
				},
				"total_collects":   bson.M{"$sum": 1},
				"unique_users":     bson.M{"$addToSet": "$user_id"},
				"first_collect_at": bson.M{"$min": "$timestamp"},
				"last_collect_at":  bson.M{"$max": "$timestamp"},
			},
		},
		{
			"$addFields": bson.M{
				"unique_users": bson.M{"$size": "$unique_users"},
			},
		},
	}

	cursor, err := s.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	if cursor.Next(ctx) {
		var result struct {
			ID             map[string]string `bson:"_id"`
			TotalCollects  int64             `bson:"total_collects"`
			UniqueUsers    int64             `bson:"unique_users"`
			FirstCollectAt *interface{}      `bson:"first_collect_at"`
			LastCollectAt  *interface{}      `bson:"last_collect_at"`
		}

		if err := cursor.Decode(&result); err != nil {
			return nil, err
		}

		stats := &types.ItemCollectStats{
			ItemID:        result.ID["item_id"],
			ItemType:      result.ID["item_type"],
			TotalCollects: result.TotalCollects,
			UniqueUsers:   result.UniqueUsers,
		}

		return stats, nil
	}

	// 如果没有找到数据，返回空统计
	return &types.ItemCollectStats{
		ItemID:        itemID,
		ItemType:      itemType,
		TotalCollects: 0,
		UniqueUsers:   0,
	}, nil
}

// GetCacheStats 获取缓存统计（MongoDB没有缓存，返回集合统计）
func (s *StatsOperations) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	// 获取集合统计信息
	stats := s.collection.Database().RunCommand(ctx, bson.M{
		"collStats": s.collection.Name(),
	})

	var result map[string]interface{}
	if err := stats.Decode(&result); err != nil {
		return nil, err
	}

	// 包装成标准格式
	cacheStats := map[string]interface{}{
		"type":             "mongodb_collection",
		"collection_stats": result,
		"hit_count":        0, // MongoDB没有缓存命中概念
		"miss_count":       0,
		"hit_rate":         0.0,
	}

	return cacheStats, nil
}
