package content_creator

import (
	"frontapi/internal/admin"
	contentCreatorModel "frontapi/internal/models/content_creator"
	contentCreatorSrv "frontapi/internal/service/content_creator"
	contentCreatorValidator "frontapi/internal/validation/content_creator"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ContentHeatController 内容热度控制器
type ContentHeatController struct {
	ContentHeatService   contentCreatorSrv.ContentHeatService
	admin.BaseController // 继承BaseController
}

// NewContentHeatController 创建内容热度控制器实例
func NewContentHeatController(contentHeatService contentCreatorSrv.ContentHeatService) *ContentHeatController {
	return &ContentHeatController{
		ContentHeatService: contentHeatService,
	}
}

// ListContentHeats 获取内容热度列表
func (c *ContentHeatController) ListContentHeats(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()
	periodType := reqInfo.Get("period_type").GetString()
	condition := map[string]interface{}{
		"content_type": contentType,
		"content_id":   contentID,
		"period_type":  periodType,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "count desc"
	}
	// 查询内容热度列表
	list, total, err := c.ContentHeatService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容热度列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// GetContentHeat 获取内容热度详情
func (c *ContentHeatController) GetContentHeat(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询内容热度
	contentHeat, err := c.ContentHeatService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容热度详情失败: "+err.Error())
	}

	if contentHeat == nil {
		return c.NotFound(ctx, "内容热度不存在")
	}

	// 返回内容热度详情
	return c.Success(ctx, contentHeat)
}

// CreateContentHeat 创建内容热度
func (c *ContentHeatController) CreateContentHeat(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.CreateContentHeatRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 创建内容热度
	contentHeat := &contentCreatorModel.ContentHeat{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentHeat); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	id, err := c.ContentHeatService.Create(ctx.Context(), contentHeat)
	if err != nil {
		return c.InternalServerError(ctx, "创建内容热度失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建内容热度成功",
	})
}

// UpdateContentHeat 更新内容热度
func (c *ContentHeatController) UpdateContentHeat(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.UpdateContentHeatRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新内容热度
	contentHeat := &contentCreatorModel.ContentHeat{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentHeat); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	err := c.ContentHeatService.Update(ctx.Context(), contentHeat)
	if err != nil {
		return c.InternalServerError(ctx, "更新内容热度失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新内容热度成功")
}

// DeleteContentHeat 删除内容热度
func (c *ContentHeatController) DeleteContentHeat(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除内容热度
	err = c.ContentHeatService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除内容热度失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除内容热度成功")
}

// ListContentHeatsByContent 根据内容获取热度列表
func (c *ContentHeatController) ListContentHeatsByContent(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()

	// 查询内容热度列表
	condition := map[string]interface{}{
		"content_type": contentType,
		"content_id":   contentID,
	}
	list, err := c.ContentHeatService.FindAll(ctx.Context(), condition, "count desc", false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容热度列表失败: "+err.Error())
	}

	// 返回列表
	return c.Success(ctx, list)
}

// ListContentHeatsByPeriod 根据时期获取热度列表
func (c *ContentHeatController) ListContentHeatsByPeriod(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 查询参数
	periodType := reqInfo.Get("period_type").GetString()
	periodDate := reqInfo.Get("period_date").GetString()
	condition := map[string]interface{}{
		"period_type": periodType,
		"period_date": periodDate,
	}
	// 查询内容热度列表
	list, err := c.ContentHeatService.FindAll(ctx.Context(), condition, "count desc", false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容热度列表失败: "+err.Error())
	}

	// 返回列表
	return c.Success(ctx, list)
}
