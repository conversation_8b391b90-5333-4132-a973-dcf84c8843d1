package v2

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// RedisClient Redis客户端包装器
type RedisClient struct {
	client    redis.UniversalClient
	config    *RedisConfig
	keyPrefix string
}

// NewRedisClient 创建Redis客户端包装器
func NewRedisClient(client redis.UniversalClient, config *RedisConfig) *RedisClient {
	if config == nil {
		config = DefaultRedisConfig()
	}

	return &RedisClient{
		client:    client,
		config:    config,
		keyPrefix: config.KeyPrefix,
	}
}

// 键生成函数
func (r *RedisClient) likeKey(itemType, itemID string) string {
	return fmt.Sprintf("%slike:%s:%s", r.keyPrefix, itemType, itemID)
}

func (r *RedisClient) userLikesKey(userID, itemType string) string {
	return fmt.Sprintf("%suser:%s:likes:%s", r.keyPrefix, userID, itemType)
}

func (r *RedisClient) itemLikersKey(itemType, itemID string) string {
	return fmt.Sprintf("%sitem:%s:%s:likers", r.keyPrefix, itemType, itemID)
}

func (r *RedisClient) countKey(itemType, itemID string) string {
	return fmt.Sprintf("%scount:%s:%s", r.keyPrefix, itemType, itemID)
}

func (r *RedisClient) hotRankKey(itemType string) string {
	return fmt.Sprintf("%shot:%s", r.keyPrefix, itemType)
}

func (r *RedisClient) statsKey(itemType, itemID string) string {
	return fmt.Sprintf("%sstats:%s:%s", r.keyPrefix, itemType, itemID)
}

func (r *RedisClient) historyKey(userID, itemType string) string {
	return fmt.Sprintf("%shistory:%s:%s", r.keyPrefix, userID, itemType)
}

func (r *RedisClient) userStatsKey(userID string) string {
	return fmt.Sprintf("%suserstats:%s", r.keyPrefix, userID)
}

func (r *RedisClient) trendKey(itemType string) string {
	return fmt.Sprintf("%strend:%s", r.keyPrefix, itemType)
}

// GetClient 获取原始Redis客户端
func (r *RedisClient) GetClient() redis.UniversalClient {
	return r.client
}

// GetConfig 获取Redis配置
func (r *RedisClient) GetConfig() *RedisConfig {
	return r.config
}

// HealthCheck 健康检查
func (r *RedisClient) HealthCheck(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}

// Close 关闭连接
func (r *RedisClient) Close() error {
	if closer, ok := r.client.(interface{ Close() error }); ok {
		return closer.Close()
	}
	return nil
}

// Pipeline 创建Pipeline
func (r *RedisClient) Pipeline() redis.Pipeliner {
	return r.client.TxPipeline()
}

// SetExpiration 设置键过期时间
func (r *RedisClient) SetExpiration(ctx context.Context, key string, expiration time.Duration) error {
	return r.client.Expire(ctx, key, expiration).Err()
}
