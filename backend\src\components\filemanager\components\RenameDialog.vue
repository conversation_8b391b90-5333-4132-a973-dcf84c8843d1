<template>
  <el-dialog
    v-model="dialogVisible"
    title="重命名"
    width="450px"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent="submitForm"
    >
      <el-form-item label="原名称">
        <el-input v-model="originalName" disabled></el-input>
      </el-form-item>
      <el-form-item label="新名称" prop="newName">
        <el-input
          v-model="form.newName"
          placeholder="请输入新名称"
          autofocus
          @keyup.enter="submitForm"
          :suffix="fileExtension"
        ></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { renameFile } from '@/service/api/system/files';
import path from 'path-browserify';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  fileType: {
    type: String,
    default: 'image'
  },
  file: {
    type: Object,
    default: () => null
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'rename-success']);

// Refs
const formRef = ref<FormInstance>();
const dialogVisible = ref(props.modelValue);
const loading = ref(false);

// Form data
const form = ref({
  newName: ''
});

// Computed
const originalName = computed(() => {
  return props.file?.name || '';
});

// Get file type from file
const getFileTypeFromPath = (file) => {
  if (!file || !file.type) return props.fileType;
  
  // Use the file's type property if available
  return file.type;
};

// Get file extension
const getFileExtension = (filename: string): string => {
  const ext = path.extname(filename);
  return ext;
};

// File extension for display
const fileExtension = computed(() => {
  if (!props.file || props.file.is_dir) return '';
  return getFileExtension(props.file.name);
});

// Get filename without extension
const getFilenameWithoutExtension = (filename: string): string => {
  const ext = getFileExtension(filename);
  if (ext) {
    return filename.slice(0, filename.length - ext.length);
  }
  return filename;
};

// Form validation rules
const rules = ref<FormRules>({
  newName: [
    { required: true, message: '请输入新名称', trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度在 1 到 100 个字符之间', trigger: 'blur' },
    { 
      pattern: /^[^\/\\:<>"|?*]+$/,
      message: '名称不能包含以下字符: / \\ : < > " | ? *',
      trigger: 'blur'
    }
  ]
});

// Initialize form when file changes
watch(() => props.file, (newFile) => {
  if (newFile) {
    // If it's a file (not a directory), only use the name without extension
    if (newFile.is_dir) {
      form.value.newName = newFile.name || '';
    } else {
      form.value.newName = getFilenameWithoutExtension(newFile.name);
    }
  }
}, { immediate: true });

// Watch for prop changes
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
});

// Watch for dialog visibility changes
watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val);
  if (!val) {
    resetForm();
  }
});

// Methods
const submitForm = async () => {
  if (!formRef.value || !props.file) return;
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return;
    
    loading.value = true;
    
    try {
      // Append the extension for files (not directories)
      let newName = form.value.newName;
      if (!props.file.is_dir) {
        newName = form.value.newName + fileExtension.value;
      }
      
      const {response,data,err} = await renameFile({
        data: {
          file_type: getFileTypeFromPath(props.file),
          old_path: props.file.path,
          new_name: newName
        }
      });
      
      if (response.data.code === 2000) {
        ElMessage.success('重命名成功');
        dialogVisible.value = false;
        emit('rename-success', response.data.data);
      } else {
        ElMessage.error(response.data.message || '重命名失败');
      }
    } catch (error: any) {
      ElMessage.error(error.message || '重命名失败');
    } finally {
      loading.value = false;
    }
  });
};

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
};
</script>

<style scoped lang="scss">
.el-dialog {
  .el-form{
    .el-form-item{
      margin-bottom: 20px;
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style> 