# 工具函数使用指南

## 格式化工具 (format.ts)

### 数字格式化
- `formatNumber(num, options?)` - 格式化数字
- `formatCount(count)` - 格式化计数（k/w/M）
- `formatCurrency(value, currency?, locale?)` - 格式化货币
- `formatPercent(value, decimals?, locale?)` - 格式化百分比

### 时间格式化
- `formatDate(date, options?)` - 格式化日期
- `formatTime(dateString, locale?)` - 格式化时间
- `formatTimeAgo(date, baseDate?, locale?)` - 格式化相对时间
- `formatPublishTime(dateString, options?)` - 格式化发布时间
- `formatDuration(seconds, options?)` - 格式化时长

### 其他格式化
- `formatChapter(chapterNumber, options?, totalChapters?)` - 格式化章节
- `formatFileSize(bytes, decimals?)` - 格式化文件大小
- `formatPlaybackSpeed(speed, locale?)` - 格式化播放速度
- `formatVideoQuality(quality, locale?)` - 格式化视频质量
- `getHeatLevel(totalEngagement, views)` - 计算热度等级
- `getHeatColor(totalEngagement, views, level)` - 获取热度颜色

## 通用工具 (common.ts)

### 函数工具
- `debounce(func, wait, options?)` - 防抖函数
- `throttle(func, wait, options?)` - 节流函数
- `memoize(fn, resolver?)` - 记忆化函数
- `retry(fn, options?)` - 重试函数

### 数据处理
- `deepClone(obj)` - 深拷贝
- `flatten(arr, depth?)` - 扁平化数组
- `unique(arr, key?)` - 数组去重
- `getType(value)` - 获取数据类型
- `isEmpty(value)` - 检查是否为空

### ID生成
- `generateId(prefix?)` - 生成唯一ID
- `generateUUID()` - 生成UUID

### 工具函数
- `sleep(ms)` - 睡眠函数
- `noop()` - 空函数
- `identity(value)` - 恒等函数
- `constant(value)` - 常量函数
- `isMobile()` - 检查是否为移动设备
- `parseUrlParams(url?)` - 解析URL参数
- `buildUrlParams(params)` - 构建URL参数

## 验证工具 (validate.ts)

### 基础验证器
- `BaseValidator.isEmpty(value)` - 检查是否为空
- `BaseValidator.isNumber(value)` - 检查是否为数字
- `BaseValidator.isInteger(value)` - 检查是否为整数
- `BaseValidator.isPositive(value)` - 检查是否为正数
- `BaseValidator.inRange(value, min, max)` - 检查数值范围
- `BaseValidator.lengthInRange(value, min, max)` - 检查字符串长度
- `BaseValidator.matchPattern(value, pattern)` - 检查正则匹配

### 字符串验证器
- `StringValidator.email(email)` - 验证邮箱
- `StringValidator.phone(phone)` - 验证手机号
- `StringValidator.idCard(idCard)` - 验证身份证号
- `StringValidator.bankCard(cardNumber)` - 验证银行卡号
- `StringValidator.url(url)` - 验证URL
- `StringValidator.password(password, level?)` - 验证密码强度
- `StringValidator.username(username)` - 验证用户名

### 数字验证器
- `NumberValidator.integer(value, min?, max?)` - 验证整数
- `NumberValidator.float(value, min?, max?, decimals?)` - 验证浮点数

### 正则表达式
- `RegexPatterns.email` - 邮箱正则
- `RegexPatterns.phone` - 手机号正则
- `RegexPatterns.idCard` - 身份证号正则
- `RegexPatterns.url` - URL正则
- `RegexPatterns.password` - 密码正则
- `RegexPatterns.strongPassword` - 强密码正则
- 更多正则表达式...

## 字符串工具 (string.ts)

### 字符串检查
- `StringUtils.isString(value)` - 检查是否为字符串
- `StringUtils.isEmpty(value)` - 检查是否为空字符串
- `StringUtils.isBlank(value)` - 检查是否为空白字符串
- `StringUtils.isNotEmpty(value)` - 检查是否不为空
- `StringUtils.isNotBlank(value)` - 检查是否不为空白

### 字符串转换
- `StringUtils.toString(value, defaultValue?)` - 安全转换为字符串
- `StringUtils.format(value, options?)` - 格式化字符串
- `StringUtils.capitalize(value)` - 首字母大写
- `StringUtils.capitalizeWords(value)` - 每个单词首字母大写
- `StringUtils.toCamelCase(value)` - 转换为驼峰命名
- `StringUtils.toPascalCase(value)` - 转换为帕斯卡命名

## 数组工具 (array.ts)

### 数组检查
- `ArrayUtils.isArray(value)` - 检查是否为数组
- `ArrayUtils.isEmpty(array)` - 检查是否为空数组
- `ArrayUtils.isNotEmpty(array)` - 检查是否不为空数组
- `ArrayUtils.length(array)` - 安全获取数组长度

### 数组访问
- `ArrayUtils.get(array, index, defaultValue?)` - 安全获取数组元素
- `ArrayUtils.first(array, defaultValue?)` - 获取第一个元素
- `ArrayUtils.last(array, defaultValue?)` - 获取最后一个元素
- `ArrayUtils.random(array)` - 获取随机元素
- `ArrayUtils.randomSample(array, count)` - 获取多个随机元素

### 数组创建
- `ArrayUtils.create(length, value?)` - 创建指定长度的数组
- `ArrayUtils.range(start, end?, step?)` - 创建数字范围数组

### 数组处理
- `ArrayUtils.unique(array, key?)` - 数组去重

## 其他工具类

### 对象工具 (object.ts)
- `ObjectUtils` - 对象操作工具类

### 数字工具 (number.ts)
- `NumberUtils` - 数字操作工具类

### 认证工具 (auth.ts)
- `AuthUtils` - 认证相关工具
- `TokenManager` - Token管理器

### 设备检测 (device.ts)
- `DeviceUtils` - 设备信息检测

### DOM工具 (dom.ts)
- `DOMUtils` - DOM操作工具

### 文件工具 (file.ts)
- `FileUtils` - 文件处理工具

### 存储工具 (storage.ts)
- `StorageUtils` - 本地存储工具

### 网络工具
- `HttpUtils` (http.ts) - HTTP请求工具
- `RequestUtils` (request.ts) - 请求工具
- `WebSocketUtils` (websocket.ts) - WebSocket工具

### 其他工具
- `AnimationUtils` (animation.ts) - 动画工具
- `ChartUtils` (chart.ts) - 图表工具
- `ConvertUtils` (convert.ts) - 转换工具
- `CryptoUtils` (crypto.ts) - 加密工具
- `EventUtils` (event.ts) - 事件工具
- `FormUtils` (form.ts) - 表单工具
- `MathUtils` (math.ts) - 数学工具
- `UrlUtils` (url.ts) - URL工具
- `WorkerUtils` (worker.ts) - Worker工具

## 使用示例

```typescript
import { formatNumber, formatDate, debounce, StringUtils } from '@/shared/utils'

// 格式化数字
const price = formatNumber(1234.56, { style: 'currency', currency: 'CNY' })

// 格式化日期
const date = formatDate(new Date(), { format: 'YYYY-MM-DD' })

// 防抖函数
const debouncedSearch = debounce((query: string) => {
  // 搜索逻辑
}, 300)

// 字符串工具
const camelCase = StringUtils.toCamelCase('hello-world')
```

## 版本信息

当前版本：2.0.0

所有工具函数都经过TypeScript类型检查，提供完整的类型支持和智能提示。