import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

/**
 * 导出数据到Excel
 * @param headers 表头配置 [{ header: '显示名称', key: '字段名' }]
 * @param data 数据数组
 * @param filename 文件名（不含扩展名）
 */
export function exportToExcel(
    headers: { header: string; key: string }[],
    data: Record<string, any>[],
    filename: string = 'export'
): void {
    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 准备表头行
    const headerRow = headers.map(h => h.header);

    // 准备数据行
    const rows = data.map(item => {
        return headers.map(h => item[h.key] ?? '');
    });

    // 合并表头和数据
    const worksheet = XLSX.utils.aoa_to_sheet([headerRow, ...rows]);

    // 将工作表添加到工作簿
    XLSX.utils.book_append_sheet(wb, worksheet, 'Sheet1');

    // 生成Excel二进制数据
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

    // 创建Blob对象
    const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

    // 保存文件
    saveAs(blob, `${filename}.xlsx`);
}

/**
 * 导出数据到CSV
 * @param headers 表头配置 [{ header: '显示名称', key: '字段名' }]
 * @param data 数据数组
 * @param filename 文件名（不含扩展名）
 */
export function exportToCSV(
    headers: { header: string; key: string }[],
    data: Record<string, any>[],
    filename: string = 'export'
): void {
    // 准备表头行
    const headerRow = headers.map(h => h.header).join(',');

    // 准备数据行
    const rows = data.map(item => {
        return headers.map(h => {
            const value = item[h.key] ?? '';
            // 如果值包含逗号、引号或换行符，需要用引号包裹并处理引号转义
            if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
                return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
        }).join(',');
    });

    // 合并表头和数据
    const csvContent = [headerRow, ...rows].join('\n');

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // 保存文件
    saveAs(blob, `${filename}.csv`);
} 