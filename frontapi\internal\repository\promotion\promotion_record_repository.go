package promotion

import (
	"frontapi/internal/repository/base"

	"gorm.io/gorm"

	model "frontapi/internal/models/promotion"
)

// PromotionRecordRepository 推广活动记录仓库接口
type PromotionRecordRepository interface {
	base.ExtendedRepository[model.PromotionRecord]
}

// promotionRecordRepository 推广活动记录仓库实现
type promotionRecordRepository struct {
	base.ExtendedRepository[model.PromotionRecord]
}

// NewPromotionRecordRepository 创建推广活动记录仓库实例
func NewPromotionRecordRepository(db *gorm.DB) PromotionRecordRepository {
	return &promotionRecordRepository{
		ExtendedRepository: base.NewExtendedRepository[model.PromotionRecord](db),
	}
}
