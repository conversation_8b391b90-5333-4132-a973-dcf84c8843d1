// 视频分类相关类型定义

// 视频分类项
export interface VideoCategoryItem {
    id: string;                    // 分类ID
    name: string;                  // 分类名称
    code: string;                  // 分类编码
    description?: string;          // 分类描述
    parent_id?: string;            // 父分类ID
    parent_name?: string;          // 父分类名称
    icon?: string;                 // 图标URL
    cover?: string;                // 封面图URL
    color?: string;                // 分类颜色
    sort_order: number;            // 排序权重
    status: number;                // 状态：0-禁用，1-启用
    is_featured: number;           // 是否推荐：0-否，1-是
    video_count?: number;          // 视频数量
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
    children?: VideoCategoryItem[]; // 子分类
}

// 视频分类搜索表单
export interface VideoCategorySearchForm {
    keyword: string;               // 关键词搜索（名称/编码）
    status?: number;               // 状态筛选
    parent_id?: string;            // 父分类筛选
    is_featured?: number;          // 是否推荐筛选
    start_date: string;            // 开始时间
    end_date: string;              // 结束时间
}

// 视频频道项
export interface VideoChannelItem {
    id: string;                    // 频道ID
    name: string;                  // 频道名称
    code?: string;                 // 频道编码
    description?: string;          // 频道描述
    icon?: string;                 // 图标URL
    cover?: string;                // 封面图URL
    banner?: string;               // 横幅图URL
    color?: string;                // 频道颜色
    sort_order: number;            // 排序权重
    status: number;                // 状态：0-禁用，1-启用
    is_featured: number;           // 是否推荐：0-否，1-是
    video_count?: number;          // 视频数量
    subscriber_count?: number;     // 订阅数量
    view_count?: number;           // 总观看数
    update_frequency?: string;     // 更新频率
    uri?: string;                  // 频道URI

    // 创建者信息
    creator_id?: string;           // 创建者ID
    creator_name?: string;         // 创建者名称
    creator_avatar?: string;       // 创建者头像

    // 时间字段
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
    last_video_at?: string;        // 最后发布视频时间
}

// 视频频道搜索表单
export interface VideoChannelSearchForm {
    keyword: string;               // 关键词搜索（名称/描述）
    status?: number;               // 状态筛选
    creator_id?: string;           // 创建者筛选
    created_at_start: string;      // 创建时间开始
    created_at_end: string;        // 创建时间结束
}

// 视频项
export interface VideoItem {
    id: string;                    // 视频ID
    title: string;                 // 视频标题
    description?: string;          // 视频描述
    cover?: string;                // 封面图URL
    url: string;                   // 视频URL
    duration?: number;             // 视频时长（秒）

    // 分类和频道
    category_id?: string;          // 分类ID
    category_name?: string;        // 分类名称
    channel_id?: string;           // 频道ID
    channel_name?: string;         // 频道名称

    // 创建者信息
    creator_id?: string;           // 创建者ID
    creator_name?: string;         // 创建者名称
    creator_avatar?: string;       // 创建者头像

    // 状态相关字段
    status: number;                // 状态：0-禁用，1-正常，-4-已删除
    is_featured?: number;          // 是否推荐：0-否，1-是
    is_published?: number;         // 是否发布：0-否，1-是

    // 标签和属性
    tags?: string[];               // 标签数组
    resolution?: string;           // 分辨率
    file_size?: number;            // 文件大小（字节）

    // 统计字段
    view_count?: number;           // 浏览次数
    like_count?: number;           // 点赞数
    comment_count?: number;        // 评论数
    share_count?: number;          // 分享数

    // 时间字段
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
    published_at?: string;         // 发布时间

    // 排序字段
    sort_order?: number;           // 排序权重

    // 临时字段，不在数据库中存储
    is_liked?: boolean;            // 是否点赞
    is_collected?: boolean;        // 是否收藏
}

// 视频搜索表单
export interface VideoSearchForm {
    keyword: string;               // 关键词搜索（标题/描述）
    status?: number;               // 状态筛选
    category_id?: string;          // 分类筛选
    channel_id?: string;           // 频道筛选
    creator_id?: string;           // 创建者筛选
    is_featured?: number;          // 是否推荐筛选
    is_published?: number;         // 是否发布筛选
    start_date: string;            // 开始时间
    end_date: string;              // 结束时间
}

// 视频评论
export interface VideoComment {
    id: string;                    // 评论ID
    video_id: string;              // 视频ID
    video_title?: string;          // 视频标题
    user_id: string;               // 用户ID
    username?: string;             // 用户名
    user_avatar?: string;          // 用户头像
    content: string;               // 评论内容
    parent_id?: string;            // 父评论ID
    reply_to_user_id?: string;     // 回复用户ID
    reply_to_username?: string;    // 回复用户名
    like_count: number;            // 点赞数
    reply_count: number;           // 回复数
    status: number;                // 状态：0-待审核，1-已通过，-1-已拒绝
    ip_address?: string;           // IP地址
    user_agent?: string;           // 用户代理
    created_at?: string;           // 创建时间
    updated_at?: string;           // 更新时间
}