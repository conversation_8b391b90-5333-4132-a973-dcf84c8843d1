/**
 * Dark 主题亮色版本变量定义
 * 基于深色调的亮色主题
 */

export const blackLightVariables = {
    // 主色调 - 深灰色系
    'color-primary': '#424242',
    'color-primary-secondary': '#212121', // 第二配色
    'color-primary-light': '#616161',
    'color-primary-dark': '#212121',
    'color-primary-50': 'rgba(66, 66, 66, 0.05)',
    'color-primary-100': 'rgba(66, 66, 66, 0.1)',
    'color-primary-200': 'rgba(66, 66, 66, 0.2)',
    'color-primary-300': 'rgba(66, 66, 66, 0.3)',
    'color-primary-400': 'rgba(66, 66, 66, 0.4)',
    'color-primary-500': 'rgba(66, 66, 66, 0.5)',
    'color-primary-600': 'rgba(66, 66, 66, 0.6)',
    'color-primary-700': 'rgba(66, 66, 66, 0.7)',
    'color-primary-800': 'rgba(66, 66, 66, 0.8)',
    'color-primary-900': 'rgba(66, 66, 66, 0.9)',

    // 强调色
    'color-accent': '#FF5722',
    'color-accent-light': '#FF7043',
    'color-accent-dark': '#D84315',

    // 中性色 - 亮色主题
    'color-neutral-50': '#FAFAFA',
    'color-neutral-100': '#F5F5F5',
    'color-neutral-200': '#EEEEEE',
    'color-neutral-300': '#E0E0E0',
    'color-neutral-400': '#BDBDBD',
    'color-neutral-500': '#9E9E9E',
    'color-neutral-600': '#757575',
    'color-neutral-700': '#616161',
    'color-neutral-800': '#424242',
    'color-neutral-900': '#212121',

    // 背景色 - 亮色主题
    'color-background': '#FFFFFF',
    'color-background-secondary': '#FAFAFA',
    'color-background-tertiary': '#F5F5F5',
    'color-surface': '#FFFFFF',
    'color-surface-secondary': '#FAFAFA',

    // 主页面背景和文字色
    'color-page-background': '#F8F8F8',
    'color-page-text': '#212121',

    // 文本色 - 亮色主题
    'color-text': '#212121',
    'color-text-secondary': '#616161',
    'color-text-tertiary': '#9E9E9E',
    'color-text-inverse': '#FFFFFF',

    // 边框色
    'color-border': '#E0E0E0',
    'color-border-light': '#EEEEEE',
    'color-border-dark': '#BDBDBD',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    'shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    'shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    'shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    'shadow-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',

    // PrimeVue 集成
    'primary-color': '#424242',
    'primary-color-text': '#FFFFFF',
    'surface-0': '#FFFFFF',
    'surface-50': '#FAFAFA',
    'surface-100': '#F5F5F5',
    'surface-200': '#EEEEEE',
    'surface-300': '#E0E0E0',
    'surface-400': '#BDBDBD',
    'surface-500': '#9E9E9E',
    'surface-600': '#757575',
    'surface-700': '#616161',
    'surface-800': '#424242',
    'surface-900': '#212121',
    'surface-ground': '#F8F8F8',
    'surface-section': '#FFFFFF',
    'surface-card': '#FFFFFF',
    'surface-overlay': '#FFFFFF',
    'surface-border': '#E0E0E0',
    'surface-hover': '#F5F5F5',
    'text-color': '#212121',
    'text-color-secondary': '#616161',
    'mask-bg': 'rgba(0, 0, 0, 0.4)',
    'highlight-bg': 'rgba(66, 66, 66, 0.1)',
    'highlight-text-color': '#424242',
    'content-bg': '#F8F8F8',
    'content-text': '#212121'
};

export default blackLightVariables;