<template>
  <el-button
    :type="isFollowed ? 'default' : 'primary'"
    :class="['follow-button', { 'is-followed': isFollowed }]"
    :loading="loading"
    @click="handleClick"
    size="small"
  >
    <template v-if="!loading">
      <el-icon v-if="isFollowed" class="button-icon">
        <Check />
      </el-icon>
      <el-icon v-else class="button-icon">
        <Plus />
      </el-icon>
      {{ isFollowed ? '已关注' : '关注' }}
    </template>
  </el-button>
</template>

<script setup lang="ts">
import { Check, Plus } from '@element-plus/icons-vue'

interface Props {
  isFollowed: boolean
  loading?: boolean
}

interface Emits {
  (e: 'follow'): void
  (e: 'unfollow'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const handleClick = () => {
  if (props.isFollowed) {
    emit('unfollow')
  } else {
    emit('follow')
  }
}
</script>

<style scoped lang="scss">
.follow-button {
  width: 100%;
  height: 36px;
  border-radius: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;

  .button-icon {
    font-size: 14px;
  }

  &:not(.is-followed) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: #fff;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
  }

  &.is-followed {
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    color: #6b7280;

    &:hover {
      background: #fee2e2;
      border-color: #fecaca;
      color: #dc2626;
    }
  }
}
</style> 