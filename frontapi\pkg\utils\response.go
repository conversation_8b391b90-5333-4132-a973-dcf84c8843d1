package utils

import (
	"github.com/gofiber/fiber/v2"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// PageData 分页数据结构
type PageData struct {
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
}

// Success 返回成功响应
func Success(c *fiber.Ctx, data interface{}, message string) error {
	if message == "" {
		message = "操作成功"
	}
	return c.Status(fiber.StatusOK).JSON(Response{
		Code:    2000,
		Data:    data,
		Message: message,
	})
}
func SendOk(c *fiber.Ctx, data interface{}, code int, message string) error {
	if message == "" {
		message = "操作成功"
	}
	return c.Status(fiber.StatusOK).JSON(Response{
		Code:    code,
		Data:    data,
		Message: message,
	})
}

// SuccessWithMessage 返回带消息的成功响应
func SuccessWithMessage(c *fiber.Ctx, message string) error {
	return c.Status(fiber.StatusOK).JSON(Response{
		Code:    2000,
		Message: message,
	})
}

// SuccessList 返回分页列表数据
func SuccessList(c *fiber.Ctx, list interface{}, total int64, page, pageSize int) error {
	return c.Status(fiber.StatusOK).JSON(Response{
		Code: 2000,
		Data: PageData{
			List:     list,
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		},
	})
}

// BadRequest 返回400错误
func BadRequest(c *fiber.Ctx, message string, data interface{}) error {
	return c.Status(fiber.StatusBadRequest).JSON(Response{
		Code:    4000,
		Message: message,
		Data:    data,
	})
}

// Unauthorized 返回401错误
func Unauthorized(c *fiber.Ctx) error {
	return c.Status(fiber.StatusUnauthorized).JSON(Response{
		Code:    4001,
		Message: "未登录",
	})
}
func AuthFail(c *fiber.Ctx, message string) error {
	if message == "" {
		message = "认证失败"
	}
	return c.Status(fiber.StatusUnauthorized).JSON(Response{
		Code:    4001,
		Message: message,
	})
}

// Forbidden 返回403错误
func Forbidden(c *fiber.Ctx, message string) error {
	if message == "" {
		message = "没有权限"
	}

	return c.Status(fiber.StatusForbidden).JSON(Response{
		Code:    4003,
		Message: message,
	})
}

// NotFound 返回404错误
func NotFound(c *fiber.Ctx, message string) error {
	if message == "" {
		message = "资源不存在"
	}

	return c.Status(fiber.StatusNotFound).JSON(Response{
		Code:    4004,
		Message: message,
	})
}

// InternalServerError 返回500错误
func InternalServerError(c *fiber.Ctx, message string) error {
	if message == "" {
		message = "服务器内部错误"
	}

	return c.Status(fiber.StatusInternalServerError).JSON(Response{
		Code:    5000,
		Message: message,
	})
}
func Error(c *fiber.Ctx, code int, message string) error {
	return c.Status(code).JSON(Response{
		Code:    code,
		Message: message,
	})
}
