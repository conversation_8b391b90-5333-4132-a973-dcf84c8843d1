package permission

import (
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
	"github.com/casbin/casbin/v2/persist"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/gorm"

	"frontapi/internal/models/permission"
	permRepo "frontapi/internal/repository/permission"
)

// CacheItem 缓存项
type CacheItem struct {
	Value     interface{}
	ExpiresAt time.Time
}

// CasbinService Casbin权限服务接口
type CasbinService interface {
	// 权限检查
	Enforce(ctx context.Context, userID, obj, act string) (bool, error)
	EnforceWithCache(ctx context.Context, userID, obj, act string) (bool, error)

	// 角色管理
	AddRoleForUser(ctx context.Context, userID, role string) error
	DeleteRoleForUser(ctx context.Context, userID, role string) error
	GetRolesForUser(ctx context.Context, userID string) ([]string, error)
	GetUsersForRole(ctx context.Context, role string) ([]string, error)

	// 权限管理
	AddPermission(ctx context.Context, role, obj, act string) error
	DeletePermission(ctx context.Context, role, obj, act string) error
	GetPermissionsForUser(ctx context.Context, userID string) ([][]string, error)

	// 策略管理
	AddPolicy(ctx context.Context, params ...interface{}) error
	RemovePolicy(ctx context.Context, params ...interface{}) error
	GetPolicy(ctx context.Context) ([][]string, error)

	// 系统管理
	LoadPolicy(ctx context.Context) error
	SavePolicy(ctx context.Context) error
	SyncFromDatabase(ctx context.Context) error
	ClearCache(ctx context.Context) error

	// 菜单权限
	GetMenuPermissions(ctx context.Context, roleCodes []string) ([]*permission.AdminMenu, error)
	GetMenuPermissionsWithCache(ctx context.Context, roleCodes []string) ([]*permission.AdminMenu, error)

	// 批量操作
	BatchAddPolicies(ctx context.Context, policies [][]string) error
	BatchRemovePolicies(ctx context.Context, policies [][]string) error

	// 工具方法
	GetEnforcer() *casbin.Enforcer
	GetCacheStats() map[string]interface{}
}

// casbinService Casbin服务实现
type casbinService struct {
	enforcer *casbin.Enforcer
	adapter  persist.Adapter
	menuRepo permRepo.AdminMenuRepository
	mu       sync.RWMutex
	// 缓存相关
	cache       map[string]*CacheItem
	cacheMu     sync.RWMutex
	cacheTTL    time.Duration
	cacheHits   int64
	cacheMisses int64
}

// NewCasbinService 创建新的Casbin服务
func NewCasbinService(modelPath string, db *gorm.DB,
	menuRepo permRepo.AdminMenuRepository) (CasbinService, error) {

	// 加载模型
	m, err := model.NewModelFromFile(modelPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load model: %w", err)
	}

	// 创建GORM适配器，使用ly_前缀
	adapter, err := gormadapter.NewAdapterByDBWithCustomTable(db, &permission.CasbinRule{}, "ly_casbin_rule")
	if err != nil {
		return nil, fmt.Errorf("failed to create gorm adapter: %w", err)
	}

	// 创建执行器
	enforcer, err := casbin.NewEnforcer(m, adapter)
	if err != nil {
		return nil, fmt.Errorf("failed to create enforcer: %w", err)
	}

	// 启用日志
	enforcer.EnableLog(true)

	// 创建服务实例
	service := &casbinService{
		enforcer:    enforcer,
		adapter:     adapter,
		menuRepo:    menuRepo,
		cache:       make(map[string]*CacheItem),
		cacheTTL:    5 * time.Minute, // 默认5分钟缓存
		cacheHits:   0,
		cacheMisses: 0,
	}

	// 从数据库同步权限数据
	err = service.SyncFromDatabase(context.Background())
	if err != nil {
		log.Printf("Warning: failed to sync from database: %v", err)
	}

	return service, nil
}

// Enforce 权限检查
func (s *casbinService) Enforce(ctx context.Context, userID, obj, act string) (bool, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	result, err := s.enforcer.Enforce(userID, obj, act)
	if err != nil {
		return false, fmt.Errorf("权限检查失败: %w", err)
	}

	return result, nil
}

// AddRoleForUser 为用户添加角色
func (s *casbinService) AddRoleForUser(ctx context.Context, userID, role string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.AddRoleForUser(userID, role)
	if err != nil {
		return fmt.Errorf("添加用户角色失败: %w", err)
	}

	if !success {
		return fmt.Errorf("用户 %s 已经拥有角色 %s", userID, role)
	}

	return nil
}

// DeleteRoleForUser 删除用户角色
func (s *casbinService) DeleteRoleForUser(ctx context.Context, userID, role string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.DeleteRoleForUser(userID, role)
	if err != nil {
		return fmt.Errorf("删除用户角色失败: %w", err)
	}

	if !success {
		return fmt.Errorf("用户 %s 没有角色 %s", userID, role)
	}

	return nil
}

// GetRolesForUser 获取用户角色
func (s *casbinService) GetRolesForUser(ctx context.Context, userID string) ([]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	roles, err := s.enforcer.GetRolesForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户角色失败: %w", err)
	}

	return roles, nil
}

// GetUsersForRole 获取角色下的用户
func (s *casbinService) GetUsersForRole(ctx context.Context, role string) ([]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	users, err := s.enforcer.GetUsersForRole(role)
	if err != nil {
		return nil, fmt.Errorf("获取角色用户失败: %w", err)
	}

	return users, nil
}

// AddPermission 添加权限
func (s *casbinService) AddPermission(ctx context.Context, role, obj, act string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.AddPermissionForUser(role, obj, act)
	if err != nil {
		return fmt.Errorf("添加权限失败: %w", err)
	}

	if !success {
		return fmt.Errorf("权限 %s:%s:%s 已存在", role, obj, act)
	}

	return nil
}

// DeletePermission 删除权限
func (s *casbinService) DeletePermission(ctx context.Context, role, obj, act string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.DeletePermissionForUser(role, obj, act)
	if err != nil {
		return fmt.Errorf("删除权限失败: %w", err)
	}

	if !success {
		return fmt.Errorf("权限 %s:%s:%s 不存在", role, obj, act)
	}

	return nil
}

// GetPermissionsForUser 获取用户权限
func (s *casbinService) GetPermissionsForUser(ctx context.Context, userID string) ([][]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	permissions, err := s.enforcer.GetPermissionsForUser(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %w", err)
	}

	return permissions, nil
}

// AddPolicy 添加策略
func (s *casbinService) AddPolicy(ctx context.Context, params ...interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.AddPolicy(params...)
	if err != nil {
		return fmt.Errorf("添加策略失败: %w", err)
	}

	if !success {
		return fmt.Errorf("策略已存在")
	}

	return nil
}

// RemovePolicy 删除策略
func (s *casbinService) RemovePolicy(ctx context.Context, params ...interface{}) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.RemovePolicy(params...)
	if err != nil {
		return fmt.Errorf("删除策略失败: %w", err)
	}

	if !success {
		return fmt.Errorf("策略不存在")
	}

	return nil
}

// GetPolicy 获取策略
func (s *casbinService) GetPolicy(ctx context.Context) ([][]string, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	policies, err := s.enforcer.GetPolicy()
	if err != nil {
		return nil, fmt.Errorf("获取策略失败: %w", err)
	}
	return policies, nil
}

// LoadPolicy 加载策略
func (s *casbinService) LoadPolicy(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	err := s.enforcer.LoadPolicy()
	if err != nil {
		return fmt.Errorf("加载策略失败: %w", err)
	}

	return nil
}

// SavePolicy 保存策略
func (s *casbinService) SavePolicy(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	err := s.enforcer.SavePolicy()
	if err != nil {
		return fmt.Errorf("保存策略失败: %w", err)
	}

	return nil
}

// SyncFromDatabase 从数据库同步权限数据
func (s *casbinService) SyncFromDatabase(ctx context.Context) error {
	// 加载基础策略
	if err := s.LoadPolicy(ctx); err != nil {
		return fmt.Errorf("加载基础策略失败: %w", err)
	}

	// 可以在这里添加其他同步逻辑
	// 例如：同步用户角色、角色权限等

	return nil
}

// GetMenuPermissions 获取菜单权限
func (s *casbinService) GetMenuPermissions(ctx context.Context, roleCodes []string) ([]*permission.AdminMenu, error) {
	// 这里可以根据角色代码查询菜单权限
	// 简化实现，直接查询所有菜单
	menus, _, err := s.menuRepo.List(ctx, map[string]interface{}{"status": 1}, "sort ASC", 1, 1000)
	if err != nil {
		return nil, fmt.Errorf("查询菜单权限失败: %w", err)
	}

	return menus, nil
}

// GetMenuPermissionsWithCache 带缓存的获取菜单权限
func (s *casbinService) GetMenuPermissionsWithCache(ctx context.Context, roleCodes []string) ([]*permission.AdminMenu, error) {
	cacheKey := fmt.Sprintf("menu_permissions:%s", strings.Join(roleCodes, ","))

	// 尝试从缓存获取
	if cached, found := s.getFromCache(cacheKey); found {
		if menus, ok := cached.([]*permission.AdminMenu); ok {
			s.cacheMu.Lock()
			s.cacheHits++
			s.cacheMu.Unlock()
			return menus, nil
		}
	}

	// 缓存未命中，从数据库获取
	s.cacheMu.Lock()
	s.cacheMisses++
	s.cacheMu.Unlock()

	menus, err := s.GetMenuPermissions(ctx, roleCodes)
	if err != nil {
		return nil, err
	}

	// 设置缓存
	s.setCache(cacheKey, menus)

	return menus, nil
}

// EnforceWithCache 带缓存的权限检查
func (s *casbinService) EnforceWithCache(ctx context.Context, userID, obj, act string) (bool, error) {
	cacheKey := fmt.Sprintf("enforce:%s:%s:%s", userID, obj, act)

	// 尝试从缓存获取
	if cached, found := s.getFromCache(cacheKey); found {
		if result, ok := cached.(bool); ok {
			s.cacheMu.Lock()
			s.cacheHits++
			s.cacheMu.Unlock()
			return result, nil
		}
	}

	// 缓存未命中，执行权限检查
	s.cacheMu.Lock()
	s.cacheMisses++
	s.cacheMu.Unlock()

	result, err := s.Enforce(ctx, userID, obj, act)
	if err != nil {
		return false, err
	}

	// 设置缓存（权限检查结果缓存时间较短）
	s.setCache(cacheKey, result)

	return result, nil
}

// BatchAddPolicies 批量添加策略
func (s *casbinService) BatchAddPolicies(ctx context.Context, policies [][]string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.AddPolicies(policies)
	if err != nil {
		return fmt.Errorf("批量添加策略失败: %w", err)
	}

	if !success {
		return fmt.Errorf("部分策略添加失败")
	}

	return nil
}

// BatchRemovePolicies 批量删除策略
func (s *casbinService) BatchRemovePolicies(ctx context.Context, policies [][]string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	success, err := s.enforcer.RemovePolicies(policies)
	if err != nil {
		return fmt.Errorf("批量删除策略失败: %w", err)
	}

	if !success {
		return fmt.Errorf("部分策略删除失败")
	}

	return nil
}

// ClearCache 清除缓存
func (s *casbinService) ClearCache(ctx context.Context) error {
	s.cacheMu.Lock()
	defer s.cacheMu.Unlock()

	s.cache = make(map[string]*CacheItem)
	return nil
}

// GetCacheStats 获取缓存统计
func (s *casbinService) GetCacheStats() map[string]interface{} {
	s.cacheMu.RLock()
	defer s.cacheMu.RUnlock()

	total := s.cacheHits + s.cacheMisses
	hitRate := float64(0)
	if total > 0 {
		hitRate = float64(s.cacheHits) / float64(total)
	}

	return map[string]interface{}{
		"cache_size":   len(s.cache),
		"cache_hits":   s.cacheHits,
		"cache_misses": s.cacheMisses,
		"hit_rate":     hitRate,
		"ttl_seconds":  int(s.cacheTTL.Seconds()),
	}
}

// getFromCache 从缓存获取数据
func (s *casbinService) getFromCache(key string) (interface{}, bool) {
	s.cacheMu.RLock()
	defer s.cacheMu.RUnlock()

	item, exists := s.cache[key]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.ExpiresAt) {
		delete(s.cache, key)
		return nil, false
	}

	return item.Value, true
}

// setCache 设置缓存
func (s *casbinService) setCache(key string, value interface{}) {
	s.cacheMu.Lock()
	defer s.cacheMu.Unlock()

	s.cache[key] = &CacheItem{
		Value:     value,
		ExpiresAt: time.Now().Add(s.cacheTTL),
	}
}

// clearCacheByPattern 根据模式清除缓存
func (s *casbinService) clearCacheByPattern(pattern string) {
	s.cacheMu.Lock()
	defer s.cacheMu.Unlock()

	for key := range s.cache {
		if strings.Contains(key, pattern) {
			delete(s.cache, key)
		}
	}
}

// GetEnforcer 获取执行器
func (s *casbinService) GetEnforcer() *casbin.Enforcer {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return s.enforcer
}
