<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :background="true"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

// 定义接收的属性
const props = defineProps({
  total: {
    type: Number,
    required: true,
    default: 0
  },
  current: {
    type: Number,
    default: 1
  },
  size: {
    type: Number,
    default: 10
  }
});

// 定义事件
const emit = defineEmits(['pagination']);

// 当前页码和每页条数
const currentPage = ref(props.current);
const pageSize = ref(props.size);

// 监听属性变化
watch(() => props.current, (val) => {
  currentPage.value = val;
});

watch(() => props.size, (val) => {
  pageSize.value = val;
});

// 处理页数改变
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  emit('pagination', {
    page: currentPage.value,
    pageSize: val
  });
};

// 处理页码改变
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
  emit('pagination', {
    page: val,
    pageSize: pageSize.value
  });
};
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style> 