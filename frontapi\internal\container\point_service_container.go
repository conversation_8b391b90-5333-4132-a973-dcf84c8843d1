package container

import (
	integralRepo "frontapi/internal/repository/integral"
	integralSvc "frontapi/internal/service/integral"
)

// InitPointServices 初始化积分相关服务
func InitPointServices(b *ServiceBuilder) {
	// 初始化积分相关仓库和服务
	pointRepo := integralRepo.NewPointRepository(b.DB())
	pointExchangeRepo := integralRepo.NewPointExchangeRepository(b.DB())
	pointLevelRepo := integralRepo.NewPointLevelRepository(b.DB())
	pointRuleRepo := integralRepo.NewPointRuleRepository(b.DB())

	container := b.Services()
	container.PointService = integralSvc.NewPointService(pointRepo)
	container.PointExchangeService = integralSvc.NewPointExchangeService(pointExchangeRepo)
	container.PointLevelService = integralSvc.NewPointLevelService(pointLevelRepo)
	container.PointRuleService = integralSvc.NewPointRuleService(pointRuleRepo)
}
