<template>
  <div class="media-grid" :class="gridClass">
    <div 
      v-for="(item, index) in mediaItems" 
      :key="index"
      class="media-item"
      @click="handleMediaClick(item, index, $event)"
    >
      <!-- 图片 -->
      <div v-if="item.type === 'image'" class="image-container">
        <img 
          :src="item.thumbnail || item.url" 
          :alt="`图片 ${index + 1}`"
          class="media-image"
          loading="lazy"
        />
        <div class="image-overlay">
          <el-icon class="preview-icon"><ZoomIn /></el-icon>
        </div>
        <!-- 多图片数量指示器 -->
        <div v-if="mediaItems.length > 1 && index === 0" class="image-count-badge">
          <el-icon><Picture /></el-icon>
          <span>{{ mediaItems.length }}</span>
        </div>
      </div>

      <!-- 视频 - 使用MiniVideoPlayer -->
      <div v-else-if="item.type === 'video'" class="video-container" @click.stop>
        <MiniVideoPlayer
          :src="item.url"
          :poster="item.thumbnail"
          :duration="item.duration"
          :width="'100%'"
          :height="'100%'"
          :is-compact="true"
          :enable-auto-play="enableAutoPlay"
          :show-info="false"
          :aspect-ratio="'16:9'"
          @play="handleVideoPlay(item)"
          @pause="handleVideoPause(item)"
          @error="handleVideoError"
          @click="handleVideoClick(item)"
        />
      </div>

      <!-- 音频 -->
      <div v-else-if="item.type === 'audio'" class="audio-container">
        <div class="audio-placeholder">
          <el-icon class="audio-icon"><Microphone /></el-icon>
          <span class="audio-text">音频</span>
          <div v-if="item.duration" class="audio-duration">
            {{ formatDuration(item.duration) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ZoomIn, Microphone, Picture } from '@element-plus/icons-vue'
import MiniVideoPlayer from '@/shared/components/media/miniplayer/Index.vue'

// 定义媒体项接口
export interface MediaItem {
  type: 'image' | 'video' | 'audio'
  url: string
  thumbnail?: string
  duration?: number
  width?: number
  height?: number
  size?: number
}

// 定义属性
interface Props {
  media?: (MediaItem | string)[]
  images?: string[]
  video?: string
  enableAutoPlay?: boolean
  maxDisplay?: number
}

const props = withDefaults(defineProps<Props>(), {
  enableAutoPlay: true,
  maxDisplay: 9
})

// 定义事件
const emit = defineEmits<{
  'media-click': [media: MediaItem, index: number]
  'video-play': [media: MediaItem]
  'video-pause': [media: MediaItem]
  'video-error': [error: Error]
  'video-click': [media: MediaItem]
}>()

// 计算媒体项列表
const mediaItems = computed(() => {
  const items: MediaItem[] = []
  
  // 处理images属性
  if (props.images && props.images.length > 0) {
    const imagesToShow = props.images.slice(0, props.maxDisplay)
    items.push(...imagesToShow.map(url => ({
      type: 'image' as const,
      url
    })))
  }
  
  // 处理video属性
  if (props.video) {
    items.push({
      type: 'video' as const,
      url: props.video
    })
  }
  
  // 处理media属性
  if (props.media && props.media.length > 0) {
    const mediaToShow = props.media.slice(0, props.maxDisplay)
    items.push(...mediaToShow.map(item => {
      if (typeof item === 'string') {
        return {
          type: 'image' as const,
          url: item
        }
      }
      return item
    }))
  }
  
  return items
})

// 计算网格类名
const gridClass = computed(() => {
  const count = mediaItems.value.length
  if (count === 1) return 'grid-single'
  if (count === 2) return 'grid-double'
  if (count === 3) return 'grid-triple'
  return 'grid-multiple'
})

// 处理媒体点击
const handleMediaClick = (media: MediaItem, index: number, event: Event) => {
  if (media.type === 'image') {
    event.stopPropagation()
    emit('media-click', media, index)
  }
}

// 处理视频播放
const handleVideoPlay = (media: MediaItem) => {
  emit('video-play', media)
}

// 处理视频暂停
const handleVideoPause = (media: MediaItem) => {
  emit('video-pause', media)
}

// 处理视频错误
const handleVideoError = (error: Error) => {
  emit('video-error', error)
}

// 处理视频点击
const handleVideoClick = (media: MediaItem) => {
  emit('video-click', media)
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}
</script>

<style scoped lang="scss">
.media-grid {
  border-radius: 12px;
  overflow: hidden;
  background: #f8fafc;
  
  &.grid-single {
    .media-item {
      aspect-ratio: 16/9;
  
      width: 100%;
    }
  }
  
  &.grid-double {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4px;
    aspect-ratio: 2/1;
    height: 240px;
    
    .media-item {
      height: 100%;
      
      .media-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  &.grid-triple {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 4px;
    height: 240px;
    
    .media-item:first-child {
      grid-row: 1 / 3;
    }
    
    .media-item {
      .media-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
  
  &.grid-multiple {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    
    .media-item {
      aspect-ratio: 1/1;
      max-height: 140px;
      
      .media-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .media-item {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    background: #e2e8f0;
    transition: all 0.3s ease;
    border-radius: 8px;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
    
    .media-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.02);
      }
    }
    
    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      .preview-icon {
        font-size: 24px;
        color: white;
      }
    }
    
    .image-count-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;
      
      .el-icon {
        font-size: 12px;
      }
    }
    
    &:hover .image-overlay {
      opacity: 1;
    }
  }

  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    z-index: 10;
  }

  .audio-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    
    .audio-placeholder {
      text-align: center;
      
      .audio-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
      
      .audio-text {
        display: block;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .audio-duration {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .media-grid {
    &.grid-single {
      .media-item {
        max-height: 300px;
      }
    }
    
    &.grid-double,
    &.grid-triple {
      height: 200px;
    }
    
    &.grid-multiple {
      .media-item {
        max-height: 120px;
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .media-grid {
    background: #2c2c2c;
    
    .media-item {
      background: #404040;
    }
    
    .image-count-badge {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}
</style>