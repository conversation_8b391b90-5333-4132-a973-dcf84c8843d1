/**
 * Lara主题配置
 */
import { THEME_COLORS, ThemeConfig } from '@/config/theme.config';

// Lara主题列表
export const laraThemes: ThemeConfig[] = [
    // Indigo
    {
        name: 'lara-light-indigo',
        code: 'laraIndigo',
        displayName: 'Indigo',
        isDark: false,
        primary: THEME_COLORS.indigo.light,
        themeFamily: 'lara',
        colorScheme: 'indigo'
    },
    {
        name: 'lara-dark-indigo',
        code: 'laraIndigo',
        displayName: 'Indigo',
        isDark: true,
        primary: THEME_COLORS.indigo.dark,
        themeFamily: 'lara',
        colorScheme: 'indigo'
    },

    // Blue
    {
        name: 'lara-light-blue',
        code: 'laraBlue',
        displayName: 'Blue',
        isDark: false,
        primary: THEME_COLORS.blue.light,
        themeFamily: 'lara',
        colorScheme: 'blue'
    },
    {
        name: 'lara-dark-blue',
        code: 'laraBlue',
        displayName: 'Blue',
        isDark: true,
        primary: THEME_COLORS.blue.dark,
        themeFamily: 'lara',
        colorScheme: 'blue'
    },

    // Purple
    {
        name: 'lara-light-purple',
        code: 'laraPurple',
        displayName: 'Purple',
        isDark: false,
        primary: THEME_COLORS.purple.light,
        themeFamily: 'lara',
        colorScheme: 'purple'
    },
    {
        name: 'lara-dark-purple',
        code: 'laraPurple',
        displayName: 'Purple',
        isDark: true,
        primary: THEME_COLORS.purple.dark,
        themeFamily: 'lara',
        colorScheme: 'purple'
    },

    // Teal
    {
        name: 'lara-light-teal',
        code: 'laraTeal',
        displayName: 'Teal',
        isDark: false,
        primary: THEME_COLORS.teal.light,
        themeFamily: 'lara',
        colorScheme: 'teal'
    },
    {
        name: 'lara-dark-teal',
        code: 'laraTeal',
        displayName: 'Teal',
        isDark: true,
        primary: THEME_COLORS.teal.dark,
        themeFamily: 'lara',
        colorScheme: 'teal'
    },

    // Orange
    {
        name: 'lara-light-orange',
        code: 'laraOrange',
        displayName: 'Orange',
        isDark: false,
        primary: THEME_COLORS.orange.light,
        themeFamily: 'lara',
        colorScheme: 'orange'
    },
    {
        name: 'lara-dark-orange',
        code: 'laraOrange',
        displayName: 'Orange',
        isDark: true,
        primary: THEME_COLORS.orange.dark,
        themeFamily: 'lara',
        colorScheme: 'orange'
    },

    // Rose
    {
        name: 'lara-light-rose',
        code: 'laraRose',
        displayName: 'Rose',
        isDark: false,
        primary: THEME_COLORS.rose.light,
        themeFamily: 'lara',
        colorScheme: 'rose'
    },
    {
        name: 'lara-dark-rose',
        code: 'laraRose',
        displayName: 'Rose',
        isDark: true,
        primary: THEME_COLORS.rose.dark,
        themeFamily: 'lara',
        colorScheme: 'rose'
    },

    // Fuchsia
    {
        name: 'lara-light-fuchsia',
        code: 'laraFuchsia',
        displayName: 'Fuchsia',
        isDark: false,
        primary: THEME_COLORS.fuchsia.light,
        themeFamily: 'lara',
        colorScheme: 'fuchsia'
    },
    {
        name: 'lara-dark-fuchsia',
        code: 'laraFuchsia',
        displayName: 'Fuchsia',
        isDark: true,
        primary: THEME_COLORS.fuchsia.dark,
        themeFamily: 'lara',
        colorScheme: 'fuchsia'
    },

    // Noir
    {
        name: 'lara-light-noir',
        code: 'laraNoir',
        displayName: 'Noir',
        isDark: false,
        primary: THEME_COLORS.noir.light,
        themeFamily: 'lara',
        colorScheme: 'noir'
    },
    {
        name: 'lara-dark-noir',
        code: 'laraNoir',
        displayName: 'Noir',
        isDark: true,
        primary: THEME_COLORS.noir.dark,
        themeFamily: 'lara',
        colorScheme: 'noir'
    }
]; 