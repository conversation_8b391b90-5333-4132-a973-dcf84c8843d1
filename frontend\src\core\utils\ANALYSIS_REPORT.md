# Utils 目录重复函数分析报告

## 发现的问题

### 1. 重复函数定义

#### 格式化函数重复
- `formatNumber`: 存在于 `index.ts`, `format.ts`
- `formatDuration`: 存在于 `index.ts`, `format.ts`
- `formatTime`/`formatDate`: 在 `format.ts`, `date.ts` 中有类似功能
- `formatCount`: 在 `format.ts` 中与 `formatNumber` 功能重复

#### 工具函数重复
- `debounce`: 存在于 `index.ts`, `common.ts`, `function.ts`
- `throttle`: 存在于 `index.ts`, `common.ts`, `function.ts`
- `deepClone`: 存在于 `index.ts`, `common.ts`
- `generateId`: 存在于 `index.ts`, `common.ts`
- `isEmpty`: 存在于 `common.ts`, `validate.ts`, `validation.ts`

#### 验证函数重复
- 数字验证函数在 `validate.ts`, `validation.ts`, `number.ts` 中重复
- 字符串验证在 `validate.ts`, `validation.ts`, `string.ts` 中重复
- 正则表达式模式在多个文件中重复定义

### 2. 文件组织问题

#### 功能重叠的文件
- `validate.ts` 和 `validation.ts` - 两个文件都做表单验证
- `format.ts` 和 `date.ts` - 日期格式化功能重叠
- `common.ts`, `function.ts`, `index.ts` - 基础工具函数重复

#### 命名不一致
- 有些文件使用类导出 (如 `StringUtils`, `NumberUtils`)
- 有些文件使用函数导出
- 有些使用对象导出 (如 `BaseValidator`)

### 3. 代码质量问题

#### 实现不一致
- 同一功能的不同实现版本，功能和参数不统一
- 有些实现更完整，有些较简单
- 错误处理方式不统一

#### 文档不完整
- 部分函数缺少 JSDoc 注释
- 类型定义不够完善
- 使用示例缺失

## 优化建议

### 1. 文件重组方案

#### 保留的核心文件
- `index.ts` - 统一导出入口
- `format.ts` - 所有格式化功能 (合并日期格式化)
- `validation.ts` - 统一验证功能 (删除 validate.ts)
- `function.ts` - 函数工具 (合并 common.ts 的功能)
- `string.ts` - 字符串工具
- `number.ts` - 数字工具
- `date.ts` - 日期工具 (移除格式化功能到 format.ts)
- 其他专用工具文件保持不变

#### 删除的重复文件
- `validate.ts` - 功能合并到 `validation.ts`
- `common.ts` - 功能合并到 `function.ts`

### 2. 函数整理方案

#### 格式化函数 (format.ts)
```typescript
// 保留最完整的实现
export const formatNumber = (num: number, options?: NumberFormatOptions) => { ... }
export const formatDuration = (seconds: number) => { ... }
export const formatDate = (date: Date | string | number, format?: string) => { ... }
export const formatTime = (dateString: string) => { ... } // 相对时间
export const formatFileSize = (bytes: number) => { ... }
export const formatCurrency = (value: number, currency?: string) => { ... }
```

#### 工具函数 (function.ts)
```typescript
// 保留最完整的实现
export const debounce = <T extends (...args: any[]) => any>(...) => { ... }
export const throttle = <T extends (...args: any[]) => any>(...) => { ... }
export const deepClone = <T>(obj: T): T => { ... }
export const generateId = (prefix?: string): string => { ... }
export const sleep = (ms: number): Promise<void> => { ... }
export const retry = async <T>(...) => { ... }
```

#### 验证函数 (validation.ts)
```typescript
// 统一验证接口和实现
export class Validator { ... }
export const RegexPatterns = { ... }
export const validateEmail = (email: string) => { ... }
export const validatePhone = (phone: string) => { ... }
// 删除重复的验证逻辑
```

### 3. 统一导出 (index.ts)
```typescript
// 重新组织导出，避免重复
export * from './format'
export * from './validation'
export * from './function'
export * from './string'
export * from './number'
export * from './date'
// ... 其他文件

// 提供便捷的默认导出
export {
  formatNumber,
  formatDuration,
  formatDate,
  debounce,
  throttle,
  deepClone,
  generateId,
  validateEmail,
  validatePhone
} from './format'
```

## 实施步骤

1. **备份当前代码** - 确保可以回滚
2. **创建新的整合文件** - 合并最佳实现
3. **更新 index.ts** - 重新组织导出
4. **删除重复文件** - 移除 validate.ts, common.ts
5. **更新导入引用** - 修改项目中的导入路径
6. **添加完整文档** - JSDoc 注释和使用示例
7. **编写单元测试** - 确保功能正确性
8. **更新 README.md** - 文档化工具函数列表

## 预期收益

- **减少代码重复** - 预计减少 30-40% 的重复代码
- **提高维护性** - 统一的实现和接口
- **改善开发体验** - 清晰的文档和类型定义
- **减少包大小** - 移除重复代码
- **提高代码质量** - 统一的错误处理和边界情况处理