/**
 * 组合式函数通用类型定义
 */

// 基础类型
export type Nullable<T> = T | null
export type Optional<T> = T | undefined
export type MaybeRef<T> = T | Ref<T>
export type MaybeRefOrGetter<T> = T | Ref<T> | (() => T)

// 响应式类型
export interface ReactiveState<T = any> {
  value: T
  loading: boolean
  error: Error | null
}

// 异步操作类型
export interface AsyncOperation<T = any> {
  data: Ref<T | null>
  loading: Ref<boolean>
  error: Ref<Error | null>
  execute: (...args: any[]) => Promise<T>
  reset: () => void
}

// API相关类型
export interface ApiResponse<T = any> {
  data: T
  message?: string
  code?: number
  success?: boolean
}

export interface ApiError {
  message: string
  code?: number
  status?: number
  details?: any
}

export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  timeout?: number
  retries?: number
}

// 认证相关类型
export interface User {
  id: string | number
  username?: string
  email?: string
  name?: string
  avatar?: string
  roles?: string[]
  permissions?: string[]
  [key: string]: any
}

export interface LoginCredentials {
  email?: string
  username?: string
  password: string
  remember?: boolean
}

export interface RegisterData {
  username?: string
  email: string
  password: string
  confirmPassword?: string
  [key: string]: any
}

export interface AuthTokens {
  accessToken: string
  refreshToken?: string
  tokenType?: string
  expiresIn?: number
}

// 存储相关类型
export interface StorageOptions {
  prefix?: string
  encryption?: boolean
  compression?: boolean
  serializer?: {
    read: (value: string) => any
    write: (value: any) => string
  }
}

export interface CookieOptions {
  expires?: Date | number
  maxAge?: number
  domain?: string
  path?: string
  secure?: boolean
  httpOnly?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
}

// 主题相关类型
export type ThemeMode = 'light' | 'dark' | 'auto'

export interface ThemeConfig {
  mode: ThemeMode
  colors?: Record<string, string>
  fonts?: Record<string, string>
  spacing?: Record<string, string>
  breakpoints?: Record<string, string>
}

// 语言相关类型
export type Locale = string

export interface LanguageConfig {
  locale: Locale
  fallback?: Locale
  messages?: Record<string, Record<string, string>>
}

// 设备相关类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'
export type Orientation = 'portrait' | 'landscape'
export type OSType = 'Windows' | 'macOS' | 'Linux' | 'iOS' | 'Android' | 'Unknown'

export interface DeviceInfo {
  type: DeviceType
  orientation: Orientation
  width: number
  height: number
  pixelRatio: number
  touchSupport: boolean
  os: OSType
  browser: string
  version: string
}

// 设备相关组合式函数返回类型
export interface UseDeviceReturn {
  deviceInfo: Ref<DeviceInfo>
  networkInfo: Ref<NetworkInfo>
  isMobile: Ref<boolean>
  isTablet: Ref<boolean>
  isDesktop: Ref<boolean>
  isOnline: Ref<boolean>
  isOffline: Ref<boolean>
  orientation: Ref<Orientation>
  screenSize: Ref<{ width: number; height: number }>
  pixelRatio: Ref<number>
  touchSupport: Ref<boolean>
  isIOS: Ref<boolean>
  isAndroid: Ref<boolean>
  isWindows: Ref<boolean>
  isMacOS: Ref<boolean>
  isLinux: Ref<boolean>
  browser: Ref<string>
  browserVersion: Ref<string>
  updateDeviceInfo: () => void
  updateNetworkInfo: () => void
}

export interface UseMediaQueryReturn {
  matches: Ref<boolean>
  media: string
  update: () => void
}

export interface UseElementSizeReturn {
  width: Ref<number>
  height: Ref<number>
  update: () => void
}

export interface UseElementVisibilityReturn {
  isVisible: Ref<boolean>
  update: () => void
}

export interface UseScrollReturn {
  x: Ref<number>
  y: Ref<number>
  isScrolling: Ref<boolean>
  arrivedState: Ref<{
    left: boolean
    right: boolean
    top: boolean
    bottom: boolean
  }>
  directions: Ref<{
    left: boolean
    right: boolean
    top: boolean
    bottom: boolean
  }>
}

export interface UseMouseReturn {
  x: Ref<number>
  y: Ref<number>
  sourceType: Ref<'mouse' | 'touch' | null>
}

export interface UseKeyboardReturn {
  pressed: Ref<Set<string>>
  isPressed: (key: string) => boolean
}

export interface UseNetworkReturn {
  online: Ref<boolean>
  offline: Ref<boolean>
  downlink: Ref<number | undefined>
  downlinkMax: Ref<number>
  effectiveType: Ref<string | undefined>
  rtt: Ref<number | undefined>
  saveData: Ref<boolean | undefined>
  type: Ref<string>
  updateNetworkInfo: () => void
}

// 网络相关类型
export type NetworkType = 'wifi' | 'cellular' | 'ethernet' | 'unknown'
export type ConnectionType = 'slow-2g' | '2g' | '3g' | '4g' | '5g'

export interface NetworkInfo {
  online: boolean
  type?: NetworkType
  effectiveType?: ConnectionType
  downlink?: number
  rtt?: number
  saveData?: boolean
}

// 媒体相关类型
export interface MediaConstraints {
  video?: boolean | MediaTrackConstraints
  audio?: boolean | MediaTrackConstraints
}

export interface UploadOptions {
  url: string
  method?: string
  headers?: Record<string, string>
  data?: Record<string, any>
  maxSize?: number
  allowedTypes?: string[]
  multiple?: boolean
  chunkSize?: number
  concurrent?: number
  onProgress?: (progress: number) => void
  onSuccess?: (response: any) => void
  onError?: (error: Error) => void
}

export interface UploadFile {
  file: File
  id: string
  name: string
  size: number
  type: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  progress: number
  response?: any
  error?: Error
}

// 通知相关类型
export type NotificationType = 'info' | 'success' | 'warning' | 'error'

export interface NotificationOptions {
  title?: string
  message: string
  type?: NotificationType
  duration?: number
  closable?: boolean
  icon?: string
  actions?: NotificationAction[]
}

export interface NotificationAction {
  text: string
  action: () => void
}

// 地理位置相关类型
export interface GeolocationOptions {
  enableHighAccuracy?: boolean
  timeout?: number
  maximumAge?: number
}

export interface Position {
  latitude: number
  longitude: number
  accuracy?: number
  altitude?: number
  altitudeAccuracy?: number
  heading?: number
  speed?: number
  timestamp?: number
}

// WebSocket相关类型
export type WebSocketStatus = 'connecting' | 'connected' | 'disconnected' | 'error'

export interface WebSocketOptions {
  protocols?: string | string[]
  reconnect?: boolean
  maxReconnects?: number
  reconnectInterval?: number
  heartbeat?: {
    message: string
    interval: number
  }
}

// 分页相关类型
export interface PaginationOptions {
  page?: number
  pageSize?: number
  total?: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

export interface PaginationData<T = any> {
  items: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 搜索相关类型
export interface SearchOptions {
  query: string
  filters?: Record<string, any>
  sort?: {
    field: string
    order: 'asc' | 'desc'
  }
  pagination?: PaginationOptions
}

export interface SearchResult<T = any> {
  items: T[]
  total: number
  query: string
  took?: number
}

// 表单相关类型
export interface FormField {
  name: string
  label?: string
  type?: string
  value?: any
  rules?: ValidationRule[]
  required?: boolean
  disabled?: boolean
  placeholder?: string
  options?: { label: string; value: any }[]
}

export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

export interface FormState {
  values: Record<string, any>
  errors: Record<string, string[]>
  touched: Record<string, boolean>
  dirty: boolean
  valid: boolean
  submitting: boolean
}

// 表格相关类型
export interface TableColumn {
  key: string
  title: string
  dataIndex?: string
  width?: number
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, record: any, index: number) => any
}

export interface TableOptions {
  columns: TableColumn[]
  data: any[]
  loading?: boolean
  pagination?: PaginationOptions
  selection?: {
    type: 'checkbox' | 'radio'
    selectedKeys: (string | number)[]
    onChange: (keys: (string | number)[]) => void
  }
  sorting?: {
    field: string
    order: 'asc' | 'desc'
    onChange: (field: string, order: 'asc' | 'desc') => void
  }
  filtering?: {
    filters: Record<string, any>
    onChange: (filters: Record<string, any>) => void
  }
}

// 缓存相关类型
export interface CacheOptions {
  ttl?: number
  maxSize?: number
  strategy?: 'lru' | 'fifo' | 'lfu'
  serialize?: boolean
}

export interface CacheItem<T = any> {
  key: string
  value: T
  timestamp: number
  ttl?: number
  hits?: number
}

// 性能相关类型
export interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  memoryUsage?: number
  networkLatency?: number
  fps?: number
}

export interface DebounceOptions {
  delay: number
  immediate?: boolean
  maxWait?: number
}

export interface ThrottleOptions {
  delay: number
  leading?: boolean
  trailing?: boolean
}

// 交互相关类型
export interface DragOptions {
  disabled?: boolean
  handle?: string
  cancel?: string
  axis?: 'x' | 'y' | 'both'
  grid?: [number, number]
  bounds?: string | { left?: number; top?: number; right?: number; bottom?: number }
  onStart?: (event: MouseEvent | TouchEvent) => void
  onDrag?: (event: MouseEvent | TouchEvent, data: DragData) => void
  onStop?: (event: MouseEvent | TouchEvent, data: DragData) => void
}

export interface DragData {
  x: number
  y: number
  deltaX: number
  deltaY: number
  node: HTMLElement
}

export interface DropOptions {
  accept?: string | string[]
  disabled?: boolean
  onDrop?: (item: any, monitor: any) => void
  onDragEnter?: (item: any, monitor: any) => void
  onDragLeave?: (item: any, monitor: any) => void
  onDragOver?: (item: any, monitor: any) => void
}

// 输入相关类型
export interface KeyboardOptions {
  target?: MaybeRef<EventTarget | null>
  eventName?: 'keydown' | 'keyup' | 'keypress'
  passive?: boolean
  capture?: boolean
}

export interface MouseOptions {
  target?: MaybeRef<EventTarget | null>
  eventName?: 'click' | 'mousedown' | 'mouseup' | 'mousemove' | 'mouseenter' | 'mouseleave'
  passive?: boolean
  capture?: boolean
}

export interface TouchOptions {
  target?: MaybeRef<EventTarget | null>
  passive?: boolean
  capture?: boolean
}

export interface SwipeOptions {
  threshold?: number
  timeout?: number
  onSwipeStart?: (event: TouchEvent) => void
  onSwipeMove?: (event: TouchEvent) => void
  onSwipeEnd?: (event: TouchEvent) => void
}

export interface LongPressOptions {
  delay?: number
  onStart?: (event: Event) => void
  onCancel?: (event: Event) => void
}

// 权限相关类型
export type PermissionName = 
  | 'camera'
  | 'microphone'
  | 'geolocation'
  | 'notifications'
  | 'persistent-storage'
  | 'push'
  | 'screen-wake-lock'
  | 'xr-spatial-tracking'

export type PermissionState = 'granted' | 'denied' | 'prompt'

export interface PermissionStatus {
  name: PermissionName
  state: PermissionState
}

// 分享相关类型
export interface ShareData {
  title?: string
  text?: string
  url?: string
  files?: File[]
}

export interface ShareOptions {
  fallback?: boolean
  platforms?: string[]
}

// 硬件相关类型
export interface CameraOptions {
  facingMode?: 'user' | 'environment'
  width?: number
  height?: number
  frameRate?: number
}

export interface MicrophoneOptions {
  sampleRate?: number
  channelCount?: number
  echoCancellation?: boolean
  noiseSuppression?: boolean
}

export interface BatteryInfo {
  charging: boolean
  chargingTime: number
  dischargingTime: number
  level: number
}

// 高级功能类型
export interface WebRTCOptions {
  iceServers?: RTCIceServer[]
  iceTransportPolicy?: RTCIceTransportPolicy
  bundlePolicy?: RTCBundlePolicy
}

export interface IndexedDBOptions {
  name: string
  version?: number
  stores: {
    name: string
    keyPath?: string
    autoIncrement?: boolean
    indexes?: {
      name: string
      keyPath: string
      unique?: boolean
    }[]
  }[]
}

export interface WorkerOptions {
  type?: 'classic' | 'module'
  credentials?: 'omit' | 'same-origin' | 'include'
  name?: string
}

// 分析相关类型
export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: number
  userId?: string
  sessionId?: string
}

export interface LogLevel {
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  data?: any
  timestamp?: number
}

// 实验相关类型
export interface Experiment {
  id: string
  name: string
  variants: ExperimentVariant[]
  traffic?: number
  enabled?: boolean
}

export interface ExperimentVariant {
  id: string
  name: string
  weight: number
  config?: Record<string, any>
}

export interface FeatureFlag {
  key: string
  enabled: boolean
  variants?: Record<string, any>
  conditions?: {
    user?: string[]
    group?: string[]
    percentage?: number
  }
}

// 国际化类型
export interface I18nOptions {
  locale: string
  fallbackLocale?: string
  messages: Record<string, Record<string, string>>
  dateTimeFormats?: Record<string, any>
  numberFormats?: Record<string, any>
}

// 时间相关类型
export interface DateTimeOptions {
  locale?: string
  timezone?: string
  format?: string
}

export interface TimerOptions {
  interval?: number
  immediate?: boolean
  callback?: () => void
}

// 调试相关类型
export interface DebugOptions {
  enabled?: boolean
  verbose?: boolean
  namespace?: string
}

export interface TestOptions {
  mock?: boolean
  fixtures?: boolean
  snapshots?: boolean
}

// 状态管理类型
export interface StateOptions<T = any> {
  initialState?: T
  persistence?: boolean
  history?: boolean
  devtools?: boolean
}

export interface Action<T = any> {
  type: string
  payload?: T
}

export interface Reducer<T = any> {
  (state: T, action: Action): T
}

// 插件系统类型
export interface Plugin {
  name: string
  version?: string
  install: (app: any, options?: any) => void
  uninstall?: (app: any) => void
}

export interface Middleware<T = any> {
  (context: T, next: () => void): void
}

export interface Interceptor<T = any> {
  request?: (config: T) => T | Promise<T>
  response?: (response: T) => T | Promise<T>
  error?: (error: Error) => Error | Promise<Error>
}

// 工具类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

export type Prettify<T> = {
  [K in keyof T]: T[K]
} & {}

export type UnionToIntersection<U> = 
  (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never

export type IsEqual<T, U> = 
  (<G>() => G extends T ? 1 : 2) extends (<G>() => G extends U ? 1 : 2) ? true : false

export type If<C extends boolean, T, F> = C extends true ? T : F

// 事件类型
export interface EventMap {
  [key: string]: (...args: any[]) => void
}

export interface EventEmitter<T extends EventMap = EventMap> {
  on<K extends keyof T>(event: K, listener: T[K]): void
  off<K extends keyof T>(event: K, listener: T[K]): void
  emit<K extends keyof T>(event: K, ...args: Parameters<T[K]>): void
  once<K extends keyof T>(event: K, listener: T[K]): void
  removeAllListeners<K extends keyof T>(event?: K): void
}

// 响应式工具类型
export type ToRefs<T> = {
  [K in keyof T]: Ref<T[K]>
}

export type ToReactive<T> = {
  [K in keyof T]: UnwrapRef<T[K]>
}

// 组合式函数返回类型
export interface ComposableReturn<T = any> {
  [key: string]: T
}

// 全局类型声明
declare global {
  interface Window {
    // 扩展 Window 接口
    __COMPOSABLES_CONFIG__?: any
    __COMPOSABLES_DEBUG__?: boolean
  }
}

// 导出所有类型
export * from 'vue'