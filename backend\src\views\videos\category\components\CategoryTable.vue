<template>
  <div class="category-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="warning" size="small" @click="handleBatchStatus(0)">
          批量禁用
        </el-button>
        <el-button type="success" size="small" @click="handleBatchStatus(1)">
          批量启用
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 - 使用SlinkyTable -->
    <div class="table-content">
      <SlinkyTable
        :data="categoryList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        action-width="220"
        view-text="查看"
        edit-text="编辑"
        delete-text="删除"
        empty-text="暂无分类数据"
        class="category-data-table"
      >
        <!-- 分类信息列 -->
        <el-table-column prop="name" label="分类信息" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="category-info">
              <el-avatar
                :size="36"
                :src="row.cover"
                :alt="row.name"
                class="category-avatar"
              >
                <el-icon><Folder /></el-icon>
              </el-avatar>
              <el-image
                :src="row.icon"
                :alt="row.name"
                class="category-icon"
              />
              <div class="category-details">
                <div class="category-name">{{ row.name }}</div>
                <div class="category-code">{{ row.code || '未设置编码' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 父分类列 -->
        <el-table-column prop="parent_name" label="父分类" width="120" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.parent_name" type="info" size="small" effect="light">
              {{ row.parent_name }}
            </el-tag>
            <span v-else class="placeholder-text">顶级分类</span>
          </template>
        </el-table-column>

        <!-- 统计信息列 -->
        <el-table-column label="统计" min-width="120" align="center">
          <template #default="{ row }">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon class="stat-icon"><VideoCamera /></el-icon>
                <span class="stat-value">{{ row.video_count || 0 }}</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><Sort /></el-icon>
                <span class="stat-value">{{ row.sort_order || 0 }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 推荐状态列 -->
        <el-table-column prop="is_featured" label="推荐" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.is_featured === 1" type="warning" size="small">推荐</el-tag>
            <span v-else class="placeholder-text">普通</span>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <CategoryStatusTag :status="row.status" />
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column prop="created_at" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ row.created_at ? formatDate(row.created_at) : '未知' }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 220px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-popconfirm
              :title="`确定要${row.status === 1 ? '禁用' : '启用'}分类 ${row.name} 吗？`"
              @confirm="handleChangeStatus(row.id, row.status === 1 ? 0 : 1)"
            >
              <template #reference>
                <el-button 
                  :type="row.status === 1 ? 'warning' : 'success'" 
                  link 
                  size="small"
                >
                  <el-icon>
                    <component :is="row.status === 1 ? Lock : Unlock" />
                  </el-icon>
                  {{ row.status === 1 ? '禁用' : row.status === 0 ? '启用' : '已删除' }}
                </el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
              :title="`确定要删除分类 ${row.name} 吗？此操作不可恢复！`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页组件 -->
    <div class="table-footer">
        <SinglePager
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import type { VideoCategoryItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import {
    Calendar,
    Check,
    Delete, Edit,
    Folder,
    Lock,
    Sort,
    Unlock,
    VideoCamera,
    View
} from '@element-plus/icons-vue';
import { ref } from 'vue';
import CategoryStatusTag from './CategoryStatusTag.vue';

// Props定义
interface Props {
  categoryList: VideoCategoryItem[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'selection-change': [selection: VideoCategoryItem[]];
  'view': [row: VideoCategoryItem];
  'edit': [row: VideoCategoryItem];
  'delete': [row: VideoCategoryItem];
  'change-status': [id: string, status: number];
  'current-change': [page: number];
  'size-change': [size: number];
  'batch-status': [status: number];
  'batch-delete': [];
}

const emit = defineEmits<Emits>();

// 选中的行
const selectedRows = ref<VideoCategoryItem[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: VideoCategoryItem[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

// 查看
const handleView = (row: VideoCategoryItem) => {
  emit('view', row);
};

// 编辑
const handleEdit = (row: VideoCategoryItem) => {
  emit('edit', row);
};

// 删除
const handleDelete = (row: VideoCategoryItem) => {
  emit('delete', row);
};

// 状态变化
const handleChangeStatus = (id: string, status: number) => {
  emit('change-status', id, status);
};

// 分页变化
const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 批量状态更新
const handleBatchStatus = (status: number) => {
  emit('batch-status', status);
};

// 批量删除
const handleBatchDelete = () => {
  emit('batch-delete');
};
</script>

<style scoped lang="scss">
.category-table-container {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #e1f5fe;
    border-radius: 4px;
    margin-bottom: 16px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #1976d2;
      font-weight: 500;
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .table-content {
    background: white;
    border-radius: 4px;
    overflow: hidden;
  }

  .category-info {
    display: flex;
    align-items: center;
    gap: 12px;
    .category-icon{
        width: 36px;
        height: 36px;
    }
    .category-details {
      min-width: 0;
      flex: 1;

      .category-name {
        font-weight: 500;
        color: #333;
      }

      .category-code {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .stats-info {
    display: flex;
    gap: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;

      .stat-icon {
        color: #999;
        font-size: 14px;
      }

      .stat-value {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .time-info {
    display: flex;
    align-items: center;
    gap: 4px;

    .time-icon {
      color: #999;
      font-size: 14px;
    }

    .time-text {
      font-size: 12px;
      color: #666;
    }
  }

  .action-buttons-group {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
  }

  .placeholder-text {
    color: #999;
    font-size: 12px;
  }
    .table-footer{
        padding: 16px 24px;
        background: var(--el-bg-color-page);
        border-top: 1px solid var(--el-border-color-lighter);
    }   
}
</style> 