# 视频管理接口文档

## 视频接口 (/videos)

### 1. 获取视频列表

**接口地址**: `POST /api/videos/getVideoList`

**接口描述**: 获取视频列表，支持分页和筛选

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "channel_id": "频道ID",
    "status": 1,
    "sort_by": "created_at",
    "sort_order": "desc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "video_id",
        "title": "视频标题",
        "description": "视频描述",
        "cover_url": "封面图片URL",
        "video_url": "视频文件URL",
        "duration": 120,
        "views_count": 1000,
        "likes_count": 50,
        "comments_count": 10,
        "category_id": "分类ID",
        "channel_id": "频道ID",
        "creator_id": "创作者ID",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取视频详情

**接口地址**: `POST /api/videos/getVideoDetail`

**接口描述**: 获取指定视频的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "video_id"
  }
}
```

### 3. 获取推荐视频

**接口地址**: `POST /api/videos/getRecommendedVideos`

**接口描述**: 获取推荐视频列表

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "exclude_video_id": "排除的视频ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 4. 获取热门视频

**接口地址**: `POST /api/videos/getHotVideos`

**接口描述**: 获取热门视频列表

**请求参数**:
```json
{
  "data": {
    "time_range": "week",
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 5. 搜索视频

**接口地址**: `POST /api/videos/searchVideos`

**接口描述**: 根据关键词搜索视频

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "duration_filter": "short",
    "upload_time": "week"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 视频分类接口 (/video/categories)

### 1. 获取视频分类列表

**接口地址**: `POST /api/video/categories/getVideoCategoryList`

**接口描述**: 获取所有视频分类

**请求参数**:
```json
{
  "data": {
    "parent_id": "父分类ID",
    "status": 1
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "category_id",
        "name": "分类名称",
        "description": "分类描述",
        "icon_url": "图标URL",
        "parent_id": "父分类ID",
        "sort_order": 1,
        "video_count": 100,
        "status": 1
      }
    ]
  }
}
```

### 2. 获取分类详情

**接口地址**: `POST /api/video/categories/getCategoryDetail`

**接口描述**: 获取指定分类的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "category_id"
  }
}
```

### 3. 获取分类下的视频

**接口地址**: `POST /api/video/categories/getCategoryVideos`

**接口描述**: 获取指定分类下的视频列表

**请求参数**:
```json
{
  "data": {
    "category_id": "category_id",
    "sort_by": "created_at"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 视频频道接口 (/video/channels)

### 1. 获取视频频道列表

**接口地址**: `POST /api/video/channels/getVideoChannelList`

**接口描述**: 获取所有视频频道

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "status": 1
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "channel_id",
        "name": "频道名称",
        "description": "频道描述",
        "cover_url": "封面图片URL",
        "creator_id": "创建者ID",
        "subscribers_count": 1000,
        "videos_count": 50,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取频道详情

**接口地址**: `POST /api/video/channels/getChannelDetail`

**接口描述**: 获取指定频道的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "channel_id"
  }
}
```

### 3. 获取频道视频

**接口地址**: `POST /api/video/channels/getChannelVideos`

**接口描述**: 获取指定频道下的视频列表

**请求参数**:
```json
{
  "data": {
    "channel_id": "channel_id",
    "sort_by": "created_at"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 4. 订阅频道

**接口地址**: `POST /api/video/channels/subscribeChannel`

**接口描述**: 订阅指定频道

**请求参数**:
```json
{
  "data": {
    "channel_id": "channel_id"
  }
}
```

### 5. 取消订阅频道

**接口地址**: `POST /api/video/channels/unsubscribeChannel`

**接口描述**: 取消订阅指定频道

**请求参数**:
```json
{
  "data": {
    "channel_id": "channel_id"
  }
}
```

## 视频专辑接口 (/video/albums)

### 1. 获取视频专辑列表

**接口地址**: `POST /api/video/albums/getVideoAlbumList`

**接口描述**: 获取视频专辑列表

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "creator_id": "创作者ID",
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "album_id",
        "title": "专辑标题",
        "description": "专辑描述",
        "cover_url": "封面图片URL",
        "creator_id": "创作者ID",
        "videos_count": 10,
        "views_count": 5000,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 20,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取专辑详情

**接口地址**: `POST /api/video/albums/getAlbumDetail`

**接口描述**: 获取指定专辑的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "album_id"
  }
}
```

### 3. 获取专辑视频

**接口地址**: `POST /api/video/albums/getAlbumVideos`

**接口描述**: 获取指定专辑下的视频列表

**请求参数**:
```json
{
  "data": {
    "album_id": "album_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 视频评论接口 (/video/comments)

### 1. 获取视频评论列表

**接口地址**: `POST /api/video/comments/getVideoCommentList`

**接口描述**: 获取指定视频的评论列表

**请求参数**:
```json
{
  "data": {
    "video_id": "video_id",
    "parent_id": "父评论ID",
    "sort_by": "created_at"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "comment_id",
        "content": "评论内容",
        "user_id": "用户ID",
        "username": "用户名",
        "avatar": "用户头像",
        "video_id": "视频ID",
        "parent_id": "父评论ID",
        "likes_count": 10,
        "replies_count": 5,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取评论详情

**接口地址**: `POST /api/video/comments/getCommentDetail`

**接口描述**: 获取指定评论的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "comment_id"
  }
}
```

### 3. 添加评论

**接口地址**: `POST /api/video/comments/addComment`

**接口描述**: 为视频添加评论

**请求参数**:
```json
{
  "data": {
    "video_id": "video_id",
    "content": "评论内容",
    "parent_id": "父评论ID"
  }
}
```

### 4. 删除评论

**接口地址**: `POST /api/video/comments/deleteComment`

**接口描述**: 删除指定评论

**请求参数**:
```json
{
  "data": {
    "id": "comment_id"
  }
}
```

### 5. 点赞评论

**接口地址**: `POST /api/video/comments/likeComment`

**接口描述**: 为评论点赞

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id"
  }
}
```

### 6. 取消点赞评论

**接口地址**: `POST /api/video/comments/unlikeComment`

**接口描述**: 取消评论点赞

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id"
  }
}
```

### 7. 举报评论

**接口地址**: `POST /api/video/comments/reportComment`

**接口描述**: 举报不当评论

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "reason": "举报原因",
    "description": "详细描述"
  }
}
```