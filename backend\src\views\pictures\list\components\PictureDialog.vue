<template>
  <el-dialog
    :title="dialogTitle"
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    :close-on-press-escape="false"
    width="600px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入图片标题" />
      </el-form-item>


      <el-form-item label="专辑" prop="album_id">
        <el-select v-model="formData.album_id" placeholder="请选择专辑" clearable @change="handleAlbumChange">
          <el-option
            v-for="item in albumOptions"
            :key="item.id"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="图片" prop="url">
        <UrlOrFileInput
          v-model="formData.url"
          fileType="image"
          subDir="pic"
          placeholder="请输入图片URL或选择图片"
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入图片描述"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="emit('update:visible',false)">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onBeforeUnmount } from 'vue';
import { ElMessage,ElMessageBox } from 'element-plus';

import type { Picture,CreatePictureRequest,PictureAlbum } from '@/types/pictures';
import { uploadPictureWithProgress,deleteUploadedFile} from '@/service/api/upload';
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';

// 从 @/service/api/pictures/pictures 导入实际的函数
import { getPictureAlbumList,createPicture,updatePicture } from '@/service/api/pictures/pictures';

const props = defineProps<{
  visible: boolean;
  picture: Picture | null;
  albumOptions: PictureAlbum[];
}>();

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [data: any];
}>();


// 对话框标题
const dialogTitle = computed(() => props.picture ? '编辑图片' : '上传图片');

// 表单引用
const formRef = ref();
// 表单数据
const formData = reactive({
  id: '',
  title: '',
  url: '',
  category_name: '',
  album_title: '',
  height: 0,
  width: 0,
  size: 0,
  description: '',
  category_id: '',
  album_id: '',
  status: 1
});

// 上传状态
const uploadState = reactive({
  uploading: false,
  uploadId: '',
  percentage: 0,
  uploadedSize: 0,
  totalSize: 0,
  status: '' as '' | 'success' | 'warning' | 'exception',
  timer: null as number | null
});

// 取消上传控制器
const uploadCancelToken = reactive<{ cancel: () => void }>({
  cancel: () => {}
});

// 表单校验规则
const rules = {
  title: [{ required: true, message: '请输入图片标题', trigger: 'blur' }],
  album_id: [{ required: true, message: '请选择专辑', trigger: 'change' }],
  url: [{ required: true, message: '请上传图片', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
};

// 专辑选项
// const albumOptions = ref<any[]>([]);


// 获取专辑列表
// const fetchAlbums = async () => {
//   try {
//     const {response,data,err} = await getPictureAlbumList({ page: { pageNo: 1, pageSize: 100 }}) as any;
//     if (response.data.code === 2000) {
//       albumOptions.value = data.list;
//     }
//   } catch (error) {
//     console.error('获取专辑列表失败', error);
//   }
// };

// 监听图片数据变化
watch(() => props.picture, (newVal) => {
  if (newVal) {
    Object.assign(formData, newVal);
  } else {
    Object.assign(formData, {
      id: '',
      title: '',
      url: '',
      category_name: '',
      album_title: '',
      height: 0,
      width: 0,
      size: 0,
      description: '',
      category_id: '',
      album_id: '',
      status: 1
    });
  }
}, { immediate: true });

// 组件挂载时加载数据
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // fetchAlbums();
    }
  },
  { immediate: true }
);
const handleDeletePicture = async () => {
  if (!formData.url) return;

  try {
    await ElMessageBox.confirm(
     '确定要删除当前封面吗？',
     '警告',
      {
        confirmButtonText:  '确定',
        cancelButtonText:  '取消',
        type: 'warning'
      }
    );

    // 提取文件路径
    const filePath = extractFilePathFromUrl(formData.url);
    if (filePath) {
      // 调用删除API
      const res = await deleteUploadedFile(filePath);
      console.log('删除封面结果:', res);
    }

    // 无论服务器删除是否成功，都清空表单中的封面
    formData.url = '';
    ElMessage.success('封面已删除');
  } catch (err) {
    // 用户取消删除
    console.log('用户取消删除封面');
  }
};
const handleAlbumChange=(album:any)=>{
  console.log(album)

}
// 从URL中提取文件路径
const extractFilePathFromUrl = (url: string): string | null => {
  if (!url) return null;

  try {
    // 去除域名和协议，只保留路径部分
    // 例如 http://example.com/videos/123.mp4 -> /videos/123.mp4
    const urlObj = new URL(url, window.location.origin);
    return urlObj.pathname;
  } catch (e) {
    // 如果URL不是完整URL，假设它已经是路径
    // 确保路径以/开头
    return url.startsWith('/') ? url : `/${url}`;
  }
};
// 格式化文件大小
const formatSize = (size: number) => {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
};
// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error("图片格式不支持");
    return false;
  }

  if (!isLt2M) {
    ElMessage.error("图片打小超过限制");
    return false;
  }

  return true;
};


// 停止进度轮询
const stopProgressPolling = () => {
  if (uploadState.timer) {
    window.clearInterval(uploadState.timer);
    uploadState.timer = null;
  }
};

// 组件销毁前清理定时器
onBeforeUnmount(() => {
  stopProgressPolling();
});


// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  let res:any;
  try {
    await formRef.value.validate(async(valid: boolean) => {
      if (valid) {
        formData.category_id = props.albumOptions.find((item:any) => item.id === formData.album_id)?.category_id || '';
        formData.category_name = props.albumOptions.find((item:any) => item.id === formData.album_id)?.category_name || '';
        formData.album_title = props.albumOptions.find((item:any) => item.id === formData.album_id)?.title || '';
        const img = new Image();
        img.src = formData.url;
        await new Promise(resolve => {
          img.onload = resolve;
          img.onerror = resolve;
        });
        if (formData.id) {
          // 更新图片
          const updateData = {
            id: formData.id,
            title: formData.title,
            description: formData.description,
            category_id: formData.category_id,
            url: formData.url,
            width: img.naturalWidth || 0,
            height: img.naturalHeight || 0,
            album_id: formData.album_id,
            status: formData.status
          };
          res = await updatePicture(formData.id, { data: updateData });
        } else {
          // 添加图片
          // 获取图片宽高


          const createData: CreatePictureRequest = {
            title: formData.title,
            description: formData.description,
            url: formData.url,
            width: img.naturalWidth || 0,
            height: img.naturalHeight || 0,
            size: 0, // 这里无法获取实际文件大小，可以改为可选或设置默认值
            category_id: formData.category_id,
            album_id: formData.album_id,
            status: formData.status
          };

          res= await createPicture({ data: createData });
        }
        const {response,data}=res as any;
        if (response.data.code === 2000) {
          ElMessage.success(formData.id ? "修改成功" : "添加成功");
          emit('update:visible', false);
          emit('success', { ...formData });
        } else {
          ElMessage.error(response.data?.message || (formData.id ? "更新失败" : "添加失败"));
        }
      } else {
        ElMessage.error("验证失败");
      }
    });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};


</script>

<style scoped>
.upload-container {
  position: relative;
  width: 178px;
  min-height: 178px;
}

.upload-trigger {
  width: 100%;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: border-color 0.3s;
}

.upload-trigger:hover {
  border-color: #409eff;
}
.preview-container {
  position: relative;
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 5px;
  transition: all 0.3s;
}

.preview-container:hover .action-buttons {
  opacity: 1;
}

.action-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 2px;
}

.upload-trigger.has-image {
  border: none;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  text-align: center;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-top: 8px;
}

.file-input {
  display: none;
}

/* 内嵌进度条样式 */
.inline-upload-progress {
  margin-top: 8px;
  width: 100%;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
  color: #606266;
}

.progress-action {
  margin-top: 5px;
  display: flex;
  justify-content: flex-end;
}
</style>
