package utils

import (
	"os"
	"path/filepath"
	"testing"
)

func TestGetEnv(t *testing.T) {
	// 测试默认值
	value := GetEnv("NON_EXISTENT_KEY", "default_value")
	if value != "default_value" {
		t.<PERSON><PERSON><PERSON>("Expected 'default_value', got '%s'", value)
	}

	// 测试系统环境变量
	os.Setenv("TEST_KEY", "test_value")
	value = GetEnv("TEST_KEY", "default_value")
	if value != "test_value" {
		t.<PERSON><PERSON><PERSON>("Expected 'test_value', got '%s'", value)
	}

	// 清理
	os.Unsetenv("TEST_KEY")
}

func TestGetEnvBool(t *testing.T) {
	tests := []struct {
		envValue     string
		defaultValue bool
		expected     bool
	}{
		{"true", false, true},
		{"1", false, true},
		{"yes", false, true},
		{"on", false, true},
		{"false", true, false},
		{"0", true, false},
		{"no", true, false},
		{"off", true, false},
		{"", true, true}, // 空值使用默认值
	}

	for _, test := range tests {
		if test.envValue != "" {
			os.Setenv("TEST_BOOL_KEY", test.envValue)
		} else {
			os.Unsetenv("TEST_BOOL_KEY")
		}

		result := GetEnvBool("TEST_BOOL_KEY", test.defaultValue)
		if result != test.expected {
			t.Errorf("For envValue='%s', defaultValue=%v, expected %v, got %v",
				test.envValue, test.defaultValue, test.expected, result)
		}
	}

	// 清理
	os.Unsetenv("TEST_BOOL_KEY")
}

func TestGetEnvInt(t *testing.T) {
	tests := []struct {
		envValue     string
		defaultValue int
		expected     int
	}{
		{"123", 0, 123},
		{"0", 999, 0},
		{"invalid", 456, 456}, // 无效值使用默认值
		{"", 789, 789},        // 空值使用默认值
	}

	for _, test := range tests {
		if test.envValue != "" {
			os.Setenv("TEST_INT_KEY", test.envValue)
		} else {
			os.Unsetenv("TEST_INT_KEY")
		}

		result := GetEnvInt("TEST_INT_KEY", test.defaultValue)
		if result != test.expected {
			t.Errorf("For envValue='%s', defaultValue=%d, expected %d, got %d",
				test.envValue, test.defaultValue, test.expected, result)
		}
	}

	// 清理
	os.Unsetenv("TEST_INT_KEY")
}

func TestFindProjectRoot(t *testing.T) {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get working directory: %v", err)
	}

	// 从当前目录开始查找项目根目录
	projectRoot := findProjectRoot(wd)
	if projectRoot == "" {
		t.Error("Expected to find project root, but got empty string")
		return
	}

	// 验证找到的目录确实包含go.mod文件
	goModPath := filepath.Join(projectRoot, "go.mod")
	if _, err := os.Stat(goModPath); os.IsNotExist(err) {
		t.Errorf("Project root '%s' does not contain go.mod file", projectRoot)
	}
}

func TestGetEnvFilePaths(t *testing.T) {
	paths := getEnvFilePaths()
	if len(paths) == 0 {
		t.Error("Expected at least one .env file path, got none")
	}

	// 验证路径去重功能
	seen := make(map[string]bool)
	for _, path := range paths {
		if seen[path] {
			t.Errorf("Duplicate path found: %s", path)
		}
		seen[path] = true
	}
}

func TestRemoveDuplicates(t *testing.T) {
	input := []string{"a", "b", "a", "c", "b", "d"}
	expected := []string{"a", "b", "c", "d"}
	result := removeDuplicates(input)

	if len(result) != len(expected) {
		t.Errorf("Expected length %d, got %d", len(expected), len(result))
	}

	// 验证所有期望的元素都存在
	resultMap := make(map[string]bool)
	for _, item := range result {
		resultMap[item] = true
	}

	for _, item := range expected {
		if !resultMap[item] {
			t.Errorf("Expected item '%s' not found in result", item)
		}
	}
}
