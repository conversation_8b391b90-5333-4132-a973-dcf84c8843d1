package videos

// CreateCategoryRequest 创建分类请求
type CreateCategoryRequest struct {
	Name          string  `json:"name" validate:"required|minLen:2|maxLen:50"`
	Icon          string  `json:"icon"`
	Color         string  `json:"color"`
	Code          string  `json:"code" validate:"required|minLen:3|maxLen:50"`
	Description   string  `json:"description"`
	ParentID      *string `json:"parentID"`
	Uri           string  `json:"uri"`
	Image         string  `json:"image"`
	SortOrder     int     `json:"sortOrder"`
	FeaturedOrder int     `json:"featuredOrder"`
	IsFeatured    int     `json:"isFeatured"`
	Status        int     `json:"status"`
}

// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
	Name          string  `json:"name" validate:"required|minLen:2|maxLen:50"`
	Icon          string  `json:"icon"`
	Color         string  `json:"color"`
	Code          string  `json:"code" validate:"required|minLen:3|maxLen:50"`
	Description   string  `json:"description"`
	ParentID      *string `json:"parentID"`
	Uri           string  `json:"uri"`
	Image         string  `json:"image"`
	SortOrder     int     `json:"sortOrder"`
	FeaturedOrder int     `json:"featuredOrder"`
	IsFeatured    int     `json:"isFeatured"`
	Status        int     `json:"status"`
}
type UpdateCategoryStatusRequest struct {
	Id     string `json:"id" validate:"required|string"`
	Status int    `json:"status" validate:"int|min:-5|max:5"`
}

// BatchUpdateCategoryStatusRequest 批量更新分类状态请求
type BatchUpdateCategoryStatusRequest struct {
	Ids    []string `json:"ids" validate:"required|min_len:1"`
	Status int      `json:"status" validate:"int|min:-5|max:5"`
}
