<template>
  <div 
    ref="containerRef" 
    class="virtual-list"
    @scroll="handleScroll"
  >
    <div 
      class="virtual-list-phantom" 
      :style="{ height: totalHeight + 'px' }"
    ></div>
    
    <div 
      class="virtual-list-content"
      :style="{
        transform: `translateY(${offsetY}px)`
      }"
    >
      <div
        v-for="(item, index) in visibleItems"
        :key="getItemKey(item, startIndex + index)"
        class="virtual-list-item"
        :style="getItemStyle(startIndex + index)"
        :data-index="startIndex + index"
      >
        <slot :item="item" :index="startIndex + index">
          {{ item }}
        </slot>
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div v-if="loading" class="virtual-list-loading">
      <el-icon class="loading-icon">
        <Loading />
      </el-icon>
      <span>{{ loadingText }}</span>
    </div>
    
    <!-- 空状态 -->
    <div v-if="!loading && items.length === 0" class="virtual-list-empty">
      <slot name="empty">
        <el-icon class="empty-icon">
          <Box />
        </el-icon>
        <p>{{ emptyText }}</p>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { Loading, Box } from '@element-plus/icons-vue'
import { throttle } from '@/core/utils'

interface Props {
  items: any[]
  itemHeight: number | ((index: number) => number)
  containerHeight?: number
  buffer?: number
  loading?: boolean
  keyField?: string
  loadingText?: string
  emptyText?: string
  estimatedItemHeight?: number
  overscan?: number
}

const props = withDefaults(defineProps<Props>(), {
  containerHeight: 400,
  buffer: 5,
  loading: false,
  keyField: 'id',
  loadingText: '加载中...',
  emptyText: '暂无数据',
  estimatedItemHeight: 100,
  overscan: 5
})

const emit = defineEmits<{
  scrollEnd: []
  scroll: [{ scrollTop: number, scrollLeft: number }]
  itemResize: [{ index: number, height: number }]
}>()

const containerRef = ref<HTMLElement>()
const scrollTop = ref(0)
const itemHeights = ref<Map<number, number>>(new Map())
const measuredItems = ref<Set<number>>(new Set())

// 获取项目高度
const getItemHeight = (index: number): number => {
  if (typeof props.itemHeight === 'function') {
    return props.itemHeight(index)
  }
  return itemHeights.value.get(index) || props.itemHeight || props.estimatedItemHeight
}

// 计算总高度
const totalHeight = computed(() => {
  if (typeof props.itemHeight === 'number') {
    return props.items.length * props.itemHeight
  }
  
  let height = 0
  for (let i = 0; i < props.items.length; i++) {
    height += getItemHeight(i)
  }
  return height
})

// 计算可见区域的开始和结束索引
const { startIndex, endIndex, offsetY } = computed(() => {
  if (typeof props.itemHeight === 'number') {
    const start = Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.buffer)
    const visibleCount = Math.ceil(props.containerHeight / props.itemHeight)
    const end = Math.min(
      props.items.length - 1,
      start + visibleCount + props.buffer * 2
    )
    const offset = start * props.itemHeight
    
    return { startIndex: start, endIndex: end, offsetY: offset }
  }
  
  // 动态高度计算
  let start = 0
  let end = 0
  let offset = 0
  let currentHeight = 0
  
  // 找到开始索引
  for (let i = 0; i < props.items.length; i++) {
    const itemHeight = getItemHeight(i)
    if (currentHeight + itemHeight > scrollTop.value - props.buffer * props.estimatedItemHeight) {
      start = Math.max(0, i - props.overscan)
      offset = currentHeight - (i - start) * props.estimatedItemHeight
      break
    }
    currentHeight += itemHeight
  }
  
  // 找到结束索引
  currentHeight = offset
  for (let i = start; i < props.items.length; i++) {
    if (currentHeight > scrollTop.value + props.containerHeight + props.buffer * props.estimatedItemHeight) {
      end = Math.min(props.items.length - 1, i + props.overscan)
      break
    }
    currentHeight += getItemHeight(i)
  }
  
  if (end === 0) end = props.items.length - 1
  
  return { startIndex: start, endIndex: end, offsetY: offset }
}).value

// 可见项目
const visibleItems = computed(() => {
  return props.items.slice(startIndex, endIndex + 1)
})

// 获取项目样式
const getItemStyle = (index: number) => {
  if (typeof props.itemHeight === 'number') {
    return { height: props.itemHeight + 'px' }
  }
  
  const height = getItemHeight(index)
  return { height: height + 'px' }
}

// 获取项目key
const getItemKey = (item: any, index: number) => {
  return item[props.keyField] || item.id || index
}

// 测量项目高度
const measureItem = (index: number, element: HTMLElement) => {
  const height = element.offsetHeight
  if (height !== getItemHeight(index)) {
    itemHeights.value.set(index, height)
    measuredItems.value.add(index)
    emit('itemResize', { index, height })
  }
}

// 滚动处理（节流）
const handleScroll = throttle((event: Event) => {
  const target = event.target as HTMLElement
  scrollTop.value = target.scrollTop
  
  emit('scroll', {
    scrollTop: target.scrollTop,
    scrollLeft: target.scrollLeft
  })
  
  // 检查是否滚动到底部
  if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
    emit('scrollEnd')
  }
}, 16)

// 滚动到指定位置
const scrollTo = (index: number) => {
  if (!containerRef.value) return
  
  let targetScrollTop = 0
  
  if (typeof props.itemHeight === 'number') {
    targetScrollTop = index * props.itemHeight
  } else {
    for (let i = 0; i < index; i++) {
      targetScrollTop += getItemHeight(i)
    }
  }
  
  containerRef.value.scrollTop = targetScrollTop
  scrollTop.value = targetScrollTop
}

// 滚动到顶部
const scrollToTop = () => {
  scrollTo(0)
}

// 滚动到底部
const scrollToBottom = () => {
  scrollTo(props.items.length - 1)
}

// 重新计算高度
const recalculateHeights = () => {
  itemHeights.value.clear()
  measuredItems.value.clear()
  
  nextTick(() => {
    const items = containerRef.value?.querySelectorAll('.virtual-list-item')
    items?.forEach((item, index) => {
      const actualIndex = startIndex + index
      measureItem(actualIndex, item as HTMLElement)
    })
  })
}

// 监听容器高度变化
const updateContainerHeight = () => {
  if (containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect()
    // 可以在这里更新容器高度
  }
}

// 监听items变化
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength === 0) {
    scrollTop.value = 0
    itemHeights.value.clear()
    measuredItems.value.clear()
  } else if (newLength !== oldLength) {
    // 如果是动态高度，重新计算
    if (typeof props.itemHeight !== 'number') {
      recalculateHeights()
    }
  }
})

// 监听项目高度变化
watch(() => props.itemHeight, () => {
  if (typeof props.itemHeight === 'number') {
    itemHeights.value.clear()
    measuredItems.value.clear()
  }
})

onMounted(() => {
  nextTick(() => {
    updateContainerHeight()
    
    // 如果是动态高度，设置观察器
    if (typeof props.itemHeight !== 'number') {
      const observer = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const element = entry.target as HTMLElement
          const index = parseInt(element.dataset.index || '0')
          measureItem(index, element)
        })
      })
      
      // 观察所有项目
      const items = containerRef.value?.querySelectorAll('.virtual-list-item')
      items?.forEach(item => observer.observe(item))
      
      onUnmounted(() => {
        observer.disconnect()
      })
    }
  })
  
  window.addEventListener('resize', updateContainerHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerHeight)
})

// 暴露方法
defineExpose({
  scrollTo,
  scrollToTop,
  scrollToBottom,
  recalculateHeights,
  containerRef
})
</script>

<style scoped>
.virtual-list {
  @apply relative overflow-auto;
}

.virtual-list__spacer {
  @apply w-full;
}

.virtual-list__content {
  @apply w-full;
}

.virtual-list__item {
  @apply w-full;
}

.virtual-list__loading {
  @apply absolute bottom-0 left-0 right-0 flex items-center justify-center py-4;
}

.virtual-list__loading-default {
  @apply flex items-center gap-2 text-gray-500;
}

.is-loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义滚动条样式 */
.virtual-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-list::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded;
}

.virtual-list::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded hover:bg-gray-500;
}

.virtual-list::-webkit-scrollbar-thumb:active {
  @apply bg-gray-600;
}
</style>