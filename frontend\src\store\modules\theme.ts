import {
    DEFAULT_FOLLOW_SYSTEM,
    DEFAULT_THEME_FAMILY,
    DEFAULT_THEME_MODE,
    DEFAULT_THEME_STYLE,
    STORAGE_FOLLOW_SYSTEM_KEY,
    STORAGE_THEME_FAMILY_KEY,
    STORAGE_THEME_KEY,
    STORAGE_THEME_MODE_KEY,
    STORAGE_THEME_STYLE_KEY,
    ThemeFamily,
    ThemeMode,
    ThemeStyle,
    themeStyles
} from '@/config/theme.config';
import { ThemeManager } from '@/core/plugins/theme';

import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';

// 主题存储
export const useThemeStore = defineStore('theme', () => {
    const themeManager = ThemeManager.getInstance();

    // 状态
    const themeFamily = ref<ThemeFamily>(DEFAULT_THEME_FAMILY);
    const themeStyle = ref<ThemeStyle>(DEFAULT_THEME_STYLE);
    const themeMode = ref<ThemeMode>(DEFAULT_THEME_MODE);
    const followSystem = ref<boolean>(DEFAULT_FOLLOW_SYSTEM);

    // 计算属性
    const isDark = computed(() => themeMode.value === 'dark');
    const isFollowingSystem = computed(() => followSystem.value);

    // 获取当前主题名称
    const currentThemeName = computed(() => {
        return themeManager.getThemeName(themeFamily.value, themeStyle.value, themeMode.value);
    });

    // 获取当前主题家族
    const currentThemeFamily = computed(() => themeFamily.value);

    // 获取当前主题样式
    const currentThemeStyle = computed(() => themeStyle.value);


    /**
     * 获取可用的主题家族列表
     */
    function getAvailableThemes(): string[] {
        return ['aura', 'lara'];
    }

    /**
     * 获取主题样式列表
     */
    function getThemeStyles() {
        return themeStyles;
    }

    /**
     * 设置主题家族
     */
    function setThemeFamily(family: ThemeFamily) {
        themeFamily.value = family;
        themeManager.updateThemePresets(family);
        saveThemeSettings();
    }

    /**
     * 设置主题样式（颜色）
     */
    function setThemeStyle(style: ThemeStyle) {
        themeStyle.value = style;
        themeManager.updateColors('primary', style);
        themeManager.updateColors('surface', style);
        saveThemeSettings();
    }

    /**
     * 设置主题模式（亮/暗）
     */
    function setThemeMode(mode: ThemeMode) {
        themeMode.value = mode;
        themeManager.applyDarkModeClasses(mode === 'dark');
        saveThemeSettings();
    }

    /**
     * 设置完整主题
     */
    function setTheme(themeName: string) {
        const parts = themeName.split('-');
        if (parts.length < 3) return;

        const family = parts[0] as ThemeFamily;
        const style = parts[1] as ThemeStyle;
        const mode = parts[2] as ThemeMode;

        // 更新状态
        themeFamily.value = family;
        themeStyle.value = style;
        themeMode.value = mode;

        // 应用主题
        themeManager.setCurrentTheme(themeName);

        // 如果之前是跟随系统，现在手动设置了主题，则关闭跟随系统
        if (followSystem.value) {
            followSystem.value = false;
            localStorage.setItem(STORAGE_FOLLOW_SYSTEM_KEY, 'false');
        }

        // 保存主题设置
        saveThemeSettings();

        console.log(`[Theme Store] Theme set to: ${themeName}, isDark: ${isDark.value}`);
    }

    /**
     * 切换暗黑模式
     */
    function toggleDarkMode() {
        const newMode = themeMode.value === 'dark' ? 'light' : 'dark';
        setThemeMode(newMode);
        themeManager.toggleDarkMode();
    }

    /**
     * 切换跟随系统主题
     */
    function toggleFollowSystem(value?: boolean) {
        followSystem.value = value !== undefined ? value : !followSystem.value;
        localStorage.setItem(STORAGE_FOLLOW_SYSTEM_KEY, String(followSystem.value));

        if (followSystem.value) {
            // 应用系统主题
            applySystemTheme();
        }

        console.log(`[Theme Store] Follow system toggled: ${followSystem.value}`);
    }

    /**
     * 应用系统主题
     */
    function applySystemTheme() {
        if (!followSystem.value) return;

        const systemDarkMode = themeManager.prefersDark.value ? 'dark' : 'light';
        if (themeMode.value !== systemDarkMode) {
            setThemeMode(systemDarkMode);
        }
    }

    /**
     * 保存主题设置到本地存储
     */
    function saveThemeSettings() {
        const themeName = themeManager.getThemeName(themeFamily.value, themeStyle.value, themeMode.value);
        localStorage.setItem(STORAGE_THEME_KEY, themeName);
        localStorage.setItem(STORAGE_THEME_FAMILY_KEY, themeFamily.value);
        localStorage.setItem(STORAGE_THEME_STYLE_KEY, themeStyle.value);
        localStorage.setItem(STORAGE_THEME_MODE_KEY, themeMode.value);
    }

    /**
     * 初始化主题
     */
    function initializeTheme() {
        // 读取本地存储的跟随系统设置
        const savedFollowSystem = localStorage.getItem(STORAGE_FOLLOW_SYSTEM_KEY);
        if (savedFollowSystem !== null) {
            followSystem.value = savedFollowSystem === 'true';
        }

        // 读取本地存储的主题设置
        const savedThemeFamily = localStorage.getItem(STORAGE_THEME_FAMILY_KEY) as ThemeFamily || DEFAULT_THEME_FAMILY;
        const savedThemeStyle = localStorage.getItem(STORAGE_THEME_STYLE_KEY) as ThemeStyle || DEFAULT_THEME_STYLE;
        const savedThemeMode = localStorage.getItem(STORAGE_THEME_MODE_KEY) as ThemeMode || DEFAULT_THEME_MODE;

        // 设置主题状态
        themeFamily.value = savedThemeFamily;
        themeStyle.value = savedThemeStyle;
        themeMode.value = savedThemeMode;

        // 如果跟随系统，则应用系统主题
        if (followSystem.value) {
            themeMode.value = themeManager.prefersDark.value ? 'dark' : 'light';
        }

        console.log(`[Theme Store] Initialized - family: ${themeFamily.value}, style: ${themeStyle.value}, mode: ${themeMode.value}, followSystem: ${followSystem.value}`);

        // 应用主题
        const themeName = themeManager.getThemeName(themeFamily.value, themeStyle.value, themeMode.value);

        // 先应用主题预设
        themeManager.updateThemePresets(themeFamily.value);

        // 再应用颜色
        themeManager.updateColors('primary', themeStyle.value);
        themeManager.updateColors('surface', themeStyle.value);

        // 最后应用模式
        themeManager.applyDarkModeClasses(themeMode.value === 'dark');

        // 确保PrimeVue 4按钮变量被设置
        // ensurePrimeVue4Variables();

        // 监听系统主题变化
        watch(themeManager.prefersDark, () => {
            if (followSystem.value) {
                applySystemTheme();
            }
        });
    }

    /**
     * 确保PrimeVue 4变量被正确设置
     */
    function ensurePrimeVue4Variables() {
        // 获取主题颜色
        const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim() || '#a855f7';
        const primaryTextColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color-text').trim() || '#ffffff';
        const primaryHoverColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-600').trim() || '#9333ea';
        const primaryActiveColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-700').trim() || '#7e22ce';

        // 设置PrimeVue 4按钮变量
        document.documentElement.style.setProperty('--p-button-primary-color', primaryTextColor);
        document.documentElement.style.setProperty('--p-button-primary-background', primaryColor);
        document.documentElement.style.setProperty('--p-button-primary-border-color', primaryColor);
        document.documentElement.style.setProperty('--p-button-primary-hover-background', primaryHoverColor);
        document.documentElement.style.setProperty('--p-button-primary-hover-border-color', primaryHoverColor);
        document.documentElement.style.setProperty('--p-button-primary-active-background', primaryActiveColor);
        document.documentElement.style.setProperty('--p-button-primary-active-border-color', primaryActiveColor);

        // 设置PrimeVue 4输入框变量
        document.documentElement.style.setProperty('--p-input-filled-bg', 'var(--surface-50)');
        document.documentElement.style.setProperty('--p-input-filled-hover-bg', 'var(--surface-100)');
        document.documentElement.style.setProperty('--p-input-filled-focus-bg', 'var(--surface-50)');
        document.documentElement.style.setProperty('--p-input-border-color', 'var(--surface-300)');

        // 设置PrimeVue 4复选框和单选框变量
        document.documentElement.style.setProperty('--p-checkbox-box-border', 'var(--surface-300)');
        document.documentElement.style.setProperty('--p-checkbox-active-box-bg', primaryColor);
        document.documentElement.style.setProperty('--p-checkbox-active-box-border', primaryColor);

        document.documentElement.style.setProperty('--p-radiobutton-box-border', 'var(--surface-300)');
        document.documentElement.style.setProperty('--p-radiobutton-active-box-bg', primaryColor);
        document.documentElement.style.setProperty('--p-radiobutton-active-box-border', primaryColor);

        console.log('[Theme Store] PrimeVue 4 variables set');
    }

    return {
        // 状态
        themeFamily,
        themeStyle,
        themeMode,
        followSystem,

        // 计算属性
        isDark,
        isFollowingSystem,
        currentThemeName,
        currentThemeFamily,
        currentThemeStyle,

        // 方法
        getAvailableThemes,
        getThemeStyles,
        setThemeFamily,
        setThemeStyle,
        setThemeMode,
        setTheme,
        toggleDarkMode,
        toggleFollowSystem,
        applySystemTheme,
        initializeTheme
    };
});
