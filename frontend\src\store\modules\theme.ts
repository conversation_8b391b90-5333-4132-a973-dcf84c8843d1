import {
    DEFAULT_FOLLOW_SYSTEM,
    DEFAULT_THEME,
    STORAGE_FOLLOW_SYSTEM_KEY,
    STORAGE_THEME_KEY,
    ThemeConfig
} from '@/config/theme.config';
import {
    applyDarkModeClasses,
    applyHeaderFooterStyles,
    dispatchThemeChangedEvent,
    loadThemeCSS,
    prefersDark
} from '@/core/plugins/theme';
import { switchThemePreset } from '@/core/plugins/theme/preset-manager';
import {
    availableThemes,
    getThemeByCode,
    getThemeByName,
    getUniqueColorSchemes,
    themeColorGroups,
    themeGroups
} from '@/shared/themes';
import { defineStore } from 'pinia';
import { computed, ref, watch } from 'vue';

// 主题存储
export const useThemeStore = defineStore('theme', () => {
    // 状态
    const themeCode = ref<string>(DEFAULT_THEME);
    const followSystem = ref<boolean>(DEFAULT_FOLLOW_SYSTEM);
    const isDarkMode = ref<boolean>(false);

    // 计算属性
    const currentThemeCode = computed(() => themeCode.value);
    const isFollowingSystem = computed(() => followSystem.value);
    const isDark = computed(() => isDarkMode.value);

    // 获取当前主题配置
    const currentThemeConfig = computed((): ThemeConfig => {
        const theme = getThemeByCode(themeCode.value, isDarkMode.value);
        return theme || getThemeByName('aura-light-indigo')!;
    });

    // 获取当前主题名称
    const currentThemeName = computed(() => currentThemeConfig.value.name);

    // 获取当前主题家族
    const currentThemeFamily = computed(() => {
        return currentThemeConfig.value.themeFamily;
    });

    // 获取当前主题颜色方案
    const currentColorScheme = computed(() => {
        return currentThemeConfig.value.colorScheme;
    });

    // 获取当前主题家族的唯一颜色方案
    const currentFamilyColorSchemes = computed(() => {
        return getUniqueColorSchemes(currentThemeFamily.value);
    });

    // 获取当前主题家族的颜色方案分组
    const currentFamilyColorGroups = computed(() => {
        return themeColorGroups[currentThemeFamily.value as keyof typeof themeColorGroups] || {};
    });

    // 方法
    // 设置主题
    function setTheme(code: string) {
        const theme = getThemeByCode(code, isDarkMode.value);
        if (!theme) return;

        themeCode.value = code;

        // 使用新的预设管理器切换主题
        const success = switchThemePreset(code, isDarkMode.value);
        
        if (!success) {
            // 如果预设管理器失败，回退到旧方式
            console.warn('[Theme Store] Preset manager failed, falling back to CSS loading');
            
            // 应用暗色模式类
            applyDarkModeClasses(isDarkMode.value);

            // 加载主题CSS
            loadThemeCSS(theme.name);

            // 应用header和footer样式
            applyHeaderFooterStyles(theme.colorScheme, isDarkMode.value);

            // 触发主题变更事件
            dispatchThemeChangedEvent(theme.name, isDarkMode.value);
        }

        // 如果之前是跟随系统，现在手动设置了主题，则关闭跟随系统
        if (followSystem.value) {
            followSystem.value = false;
            localStorage.setItem(STORAGE_FOLLOW_SYSTEM_KEY, 'false');
        }

        // 保存主题设置
        localStorage.setItem(STORAGE_THEME_KEY, code);

        console.log(`[Theme Store] Theme set to: ${code}, isDark: ${isDarkMode.value}`);
    }

    // 设置暗黑模式
    function setDarkMode(value: boolean) {
        isDarkMode.value = value;

        // 获取当前主题配置
        const theme = getThemeByCode(themeCode.value, isDarkMode.value);
        if (!theme) return;

        // 使用新的预设管理器切换主题
        const success = switchThemePreset(themeCode.value, isDarkMode.value);
        
        if (!success) {
            // 如果预设管理器失败，回退到旧方式
            console.warn('[Theme Store] Preset manager failed, falling back to CSS loading');
            
            // 应用暗色模式类
            applyDarkModeClasses(isDarkMode.value);

            // 加载主题CSS
            loadThemeCSS(theme.name);

            // 应用header和footer样式
            applyHeaderFooterStyles(theme.colorScheme, isDarkMode.value);

            // 触发主题变更事件
            dispatchThemeChangedEvent(theme.name, isDarkMode.value);
        }
    }

    // 切换暗黑模式
    function toggleDarkMode() {
        setDarkMode(!isDarkMode.value);
    }

    // 设置主题家族
    function setThemeFamily(family: string) {
        // 构建新的主题代码
        const newThemeCode = `${family}${isDarkMode.value ? 'Dark' : 'Light'}${formatColorScheme(currentColorScheme.value)}`;
        setTheme(newThemeCode);
    }

    // 设置颜色方案
    function setColorScheme(scheme: string) {
        // 构建新的主题代码
        const newThemeCode = `${currentThemeFamily.value}${isDarkMode.value ? 'Dark' : 'Light'}${formatColorScheme(scheme)}`;
        setTheme(newThemeCode);
    }

    // 格式化颜色方案名称（首字母大写）
    function formatColorScheme(scheme: string): string {
        return scheme.charAt(0).toUpperCase() + scheme.slice(1);
    }

    // 切换跟随系统主题
    function toggleFollowSystem(value?: boolean) {
        followSystem.value = value !== undefined ? value : !followSystem.value;

        if (followSystem.value) {
            // 应用系统主题
            applySystemTheme();
        }

        localStorage.setItem(STORAGE_FOLLOW_SYSTEM_KEY, String(followSystem.value));
        console.log(`[Theme Store] Follow system toggled: ${followSystem.value}`);
    }

    // 应用系统主题
    function applySystemTheme() {
        const isDark = prefersDark.value;

        // 如果当前主题的明暗模式与系统不匹配，则切换明暗模式
        if (isDarkMode.value !== isDark) {
            isDarkMode.value = isDark;

            // 获取当前主题配置
            const theme = getThemeByCode(themeCode.value, isDarkMode.value);
            if (!theme) return;

            // 应用暗色模式类
            applyDarkModeClasses(isDarkMode.value);

            // 加载主题CSS
            loadThemeCSS(theme.name);

            // 应用header和footer样式
            applyHeaderFooterStyles(theme.colorScheme, isDarkMode.value);
        }
    }

    // 初始化主题
    function initializeTheme() {
        // 读取本地存储的跟随系统设置
        const savedFollowSystem = localStorage.getItem(STORAGE_FOLLOW_SYSTEM_KEY);
        if (savedFollowSystem !== null) {
            followSystem.value = savedFollowSystem === 'true';
        }

        // 读取本地存储的主题
        const savedThemeCode = localStorage.getItem(STORAGE_THEME_KEY);
        if (savedThemeCode && getThemeByCode(savedThemeCode, false)) {
            themeCode.value = savedThemeCode;
        } else {
            themeCode.value = DEFAULT_THEME;
        }

        // 设置初始暗色模式
        if (followSystem.value) {
            isDarkMode.value = prefersDark.value;
        } else {
            // 从存储中恢复暗色模式设置
            const savedDarkMode = localStorage.getItem('app-theme-dark-mode');
            isDarkMode.value = savedDarkMode === 'true';
        }

        console.log(`[Theme Store] Initialized - theme: ${themeCode.value}, followSystem: ${followSystem.value}, isDark: ${isDarkMode.value}`);

        // 应用主题
        const theme = getThemeByCode(themeCode.value, isDarkMode.value);
        if (theme) {
            // 应用暗色模式类
            applyDarkModeClasses(isDarkMode.value);

            // 加载主题CSS
            loadThemeCSS(theme.name);

            // 应用header和footer样式
            applyHeaderFooterStyles(theme.colorScheme, isDarkMode.value);
        }

        // 监听系统主题变化
        watch(prefersDark, () => {
            if (followSystem.value) {
                applySystemTheme();
            }
        });

        // 监听暗色模式变化，保存到本地存储
        watch(isDarkMode, (newValue) => {
            localStorage.setItem('app-theme-dark-mode', String(newValue));
        });
    }

    // 初始化
    initializeTheme();

    return {
        // 状态
        themeCode,
        followSystem,
        isDarkMode,

        // 计算属性
        currentThemeCode,
        currentThemeName,
        isFollowingSystem,
        isDark,
        currentThemeConfig,
        currentThemeFamily,
        currentColorScheme,
        currentFamilyColorSchemes,
        currentFamilyColorGroups,
        themeGroups,
        availableThemes,

        // 方法
        setTheme,
        setDarkMode,
        toggleDarkMode,
        setThemeFamily,
        setColorScheme,
        toggleFollowSystem,
        applySystemTheme,
        initializeTheme
    };
});
