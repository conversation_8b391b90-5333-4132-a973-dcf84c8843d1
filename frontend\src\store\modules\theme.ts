/**
 * 主题状态管理
 * 基于PrimeVue和CSS变量
 */
import type { ThemeConfig } from '@/shared/themes/theme-manager';
import { themeManager } from '@/shared/themes/theme-manager';
import { defineStore } from 'pinia';

// 默认主题
const DEFAULT_THEME = 'mysterious-light';

// 支持的主题类型
export type ThemeType = string;

// 主题状态接口
interface ThemeState {
    theme: ThemeType;
    followSystem: boolean;
    isDarkMode: boolean;
}

/**
 * 主题状态存储
 */
export const useThemeStore = defineStore('theme', {
    state: (): ThemeState => ({
        theme: DEFAULT_THEME, // 默认主题
        followSystem: false, // 是否跟随系统主题
        isDarkMode: false, // 是否为暗黑模式
    }),

    getters: {
        // 当前主题
        currentTheme: (state) => state.theme,

        // 是否跟随系统主题
        isFollowingSystem: (state) => state.followSystem,

        // 是否为暗黑模式
        isDark(): boolean {
            return this.isDarkMode;
        }
    },

    actions: {
        /**
         * 设置主题
         */
        setTheme(newTheme: ThemeType) {
            // 更新状态
            this.theme = newTheme;

            // 应用主题
            themeManager.setTheme(newTheme);

            // 检查并设置暗黑模式状态
            const theme = themeManager.availableThemes.value.find((t: ThemeConfig) => t.name === newTheme);
            if (theme) {
                this.isDarkMode = !!theme.isDark;

                // 添加或移除暗色主题类
                if (this.isDarkMode) {
                    document.documentElement.classList.add('dark-theme');
                    document.body.classList.add('dark-theme');
                } else {
                    document.documentElement.classList.remove('dark-theme');
                    document.body.classList.remove('dark-theme');
                }
            }

            // 如果之前是跟随系统，现在手动设置了主题，则关闭跟随系统
            if (this.followSystem) {
                this.followSystem = false;
                localStorage.setItem('app-theme-follow-system', 'false');
            }

            // 保存主题设置
            localStorage.setItem('app-theme', newTheme);

            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('theme-changed', { detail: { theme: newTheme, isDark: this.isDarkMode } }));

            console.log(`[Theme Store] Theme set to: ${newTheme}, isDark: ${this.isDarkMode}`);
        },

        /**
         * 切换暗黑模式
         */
        toggleDarkMode() {
            // 获取当前主题
            const currentTheme = themeManager.availableThemes.value.find((t: ThemeConfig) => t.name === this.theme);
            if (!currentTheme) return;

            // 查找对应的暗/亮主题
            const targetTheme = themeManager.availableThemes.value.find((t: ThemeConfig) =>
                t.code === currentTheme.code && t.isDark !== currentTheme.isDark
            );

            if (targetTheme) {
                this.setTheme(targetTheme.name);
                console.log(`[Theme Store] Dark mode toggled to: ${targetTheme.isDark}, theme: ${targetTheme.name}`);
            } else {
                // 如果没有找到对应的主题，使用默认的亮/暗主题
                const defaultTheme = this.isDarkMode ? 'modern-light' : 'dark-dark';
                this.setTheme(defaultTheme);
                console.log(`[Theme Store] No matching theme found, using default: ${defaultTheme}`);
            }
        },

        /**
         * 切换跟随系统主题
         */
        toggleFollowSystem(value?: boolean) {
            this.followSystem = value !== undefined ? value : !this.followSystem;

            if (this.followSystem) {
                themeManager.enableSystemTheme();
            } else {
                themeManager.disableSystemTheme();
            }

            localStorage.setItem('app-theme-follow-system', String(this.followSystem));

            console.log(`[Theme Store] Follow system toggled: ${this.followSystem}`);
        },

        /**
         * 初始化主题
         */
        initializeTheme() {
            console.log(`[Theme Store] Starting initialization...`);

            // 初始化主题管理器（这会加载localStorage中的设置）
            themeManager.init();

            // 同步状态（从themeManager获取已加载的设置）
            this.syncWithThemeManager();

            console.log(`[Theme Store] Initialized - theme: ${this.theme}, followSystem: ${this.followSystem}`);

            // 检查并设置暗黑模式状态
            const theme = themeManager.availableThemes.value.find((t: ThemeConfig) => t.name === this.theme);
            if (theme) {
                this.isDarkMode = !!theme.isDark;

                // 添加或移除暗色主题类
                if (this.isDarkMode) {
                    document.documentElement.classList.add('dark-theme');
                    document.body.classList.add('dark-theme');
                } else {
                    document.documentElement.classList.remove('dark-theme');
                    document.body.classList.remove('dark-theme');
                }
            }

            // 监听主题管理器变化
            document.addEventListener('themechange', () => {
                this.syncWithThemeManager();
            });
        },

        /**
         * 同步方法 - 当外部修改主题时调用此方法同步状态
         */
        syncWithThemeManager() {
            // 从主题管理器获取状态
            if (themeManager.currentTheme && themeManager.currentTheme.value) {
                this.theme = themeManager.currentTheme.value as ThemeType;
            }

            this.followSystem = themeManager.followSystem.value;
            this.isDarkMode = themeManager.isDark.value;

            console.log(`[Theme Store] Synced with theme manager - theme: ${this.theme}, darkMode: ${this.isDarkMode}`);
        }
    },
}); 