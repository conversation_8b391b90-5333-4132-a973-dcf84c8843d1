package utils

import (
	"github.com/ulule/deepcopier"
)

// SmartCopy 智能拷贝，基于 github.com/ulule/deepcopier 优化性能
// 支持字段映射、类型转换和嵌套结构体，性能比自定义反射实现更优
func SmartCopy(src, dest interface{}) error {
	// 使用 deepcopier 进行高效拷贝
	// deepcopier 内部使用了缓存和优化的反射机制，性能更好
	return deepcopier.Copy(src).To(dest)
}

// SmartCopyWithContext 带上下文的智能拷贝
// 支持在拷贝过程中传递上下文信息给目标结构体的方法
func SmartCopyWithContext(src, dest interface{}, ctx map[string]interface{}) error {
	return deepcopier.Copy(src).WithContext(ctx).To(dest)
}

// CopyFrom 反向拷贝，从目标拷贝到源
// 在某些场景下可能需要反向拷贝数据
func CopyFrom(dest, src interface{}) error {
	return deepcopier.Copy(dest).From(src)
}

// CopyFromWithContext 带上下文的反向拷贝
func CopyFromWithContext(dest, src interface{}, ctx map[string]interface{}) error {
	return deepcopier.Copy(dest).WithContext(ctx).From(src)
}
