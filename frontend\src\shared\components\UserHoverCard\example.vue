<template>
  <div class="user-hover-card-example">
    <h2>UserHoverCard 使用示例</h2>
    
    <div class="example-section">
      <h3>基础用法</h3>
      <p>鼠标悬停在头像上查看用户信息卡片：</p>
      
      <div class="user-list">
        <div v-for="user in users" :key="user.id" class="user-item">
          <UserHoverCard
            :user-info="user"
            @click="handleUserClick"
            @follow="handleFollow"
            @unfollow="handleUnfollow"
          />
          <span class="user-label">{{ user.nickname || user.username }}</span>
        </div>
      </div>
    </div>

    <div class="example-section">
      <h3>不同尺寸</h3>
      <div class="size-examples">
        <UserHoverCard
          :user-info="users[0]"
          :avatar-size="32"
          @click="handleUserClick"
          @follow="handleFollow"
          @unfollow="handleUnfollow"
        />
        <span>32px</span>

        <UserHoverCard
          :user-info="users[0]"
          :avatar-size="48"
          @click="handleUserClick"
          @follow="handleFollow"
          @unfollow="handleUnfollow"
        />
        <span>48px (默认)</span>

        <UserHoverCard
          :user-info="users[0]"
          :avatar-size="64"
          @click="handleUserClick"
          @follow="handleFollow"
          @unfollow="handleUnfollow"
        />
        <span>64px</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { UserHoverCard } from '@/shared/components/UserHoverCard'
import type { Author } from '@/types/community'

// 模拟用户数据
const users = ref<Author[]>([
  {
    id: '1',
    username: 'johndoe',
    nickname: '约翰·多伊',
    avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
    bio: '热爱生活的摄影师，专注于记录美好瞬间。喜欢旅行和美食，分享生活中的点点滴滴。',
    followCount: 1234,
    totalVideos: 56,
    totalPosts: 89,
    totalShorts: 123,
    heat: 9876,
    userType: 2, // 创作者
    creatorLevel: 5,
    isLiked: false,
    isFollowed: false
  },
  {
    id: '2',
    username: 'janedoe',
    nickname: '简·多伊',
    avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png',
    bio: '美食博主，探索世界各地的美味。',
    followCount: 5678,
    totalVideos: 234,
    totalPosts: 156,
    totalShorts: 89,
    heat: 12345,
    userType: 2, // 创作者
    creatorLevel: 3,
    isLiked: true,
    isFollowed: true
  },
  {
    id: '3',
    username: 'bobsmith',
    nickname: '鲍勃·史密斯',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    bio: '科技爱好者，分享最新的科技资讯。',
    followCount: 890,
    totalVideos: 45,
    totalPosts: 67,
    totalShorts: 23,
    heat: 3456,
    userType: 1, // 普通用户
    creatorLevel: 0,
    isLiked: false,
    isFollowed: false
  }
])

const handleUserClick = (user: User) => {
  ElMessage.success(`点击了用户: ${user.nickname || user.username}`)
  console.log('用户点击:', user)
}

const handleFollow = (user: User) => {
  // 更新用户关注状态
  const targetUser = users.value.find(u => u.id === user.id)
  if (targetUser) {
    targetUser.isFollowed = true
    targetUser.followCount += 1
  }
  ElMessage.success(`已关注用户: ${user.nickname || user.username}`)
}

const handleUnfollow = (user: User) => {
  // 更新用户关注状态
  const targetUser = users.value.find(u => u.id === user.id)
  if (targetUser) {
    targetUser.isFollowed = false
    targetUser.followCount -= 1
  }
  ElMessage.success(`已取消关注用户: ${user.nickname || user.username}`)
}
</script>

<style scoped lang="scss">
.user-hover-card-example {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #1f2937;
    margin-bottom: 24px;
  }

  .example-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f9fafb;
    border-radius: 8px;

    h3 {
      color: #374151;
      margin-bottom: 12px;
    }

    p {
      color: #6b7280;
      margin-bottom: 16px;
    }
  }

  .user-list {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;

    .user-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .user-label {
        font-size: 14px;
        color: #6b7280;
        text-align: center;
      }
    }
  }

  .size-examples {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;

    span {
      font-size: 14px;
      color: #6b7280;
    }
  }
}
</style>