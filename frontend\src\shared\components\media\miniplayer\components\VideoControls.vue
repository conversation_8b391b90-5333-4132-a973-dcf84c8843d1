<template>
  <div class="video-controls" :class="{ 'is-visible': isVisible }">
    <!-- 进度条 -->
    <VideoProgress
      :current-time="currentTime"
      :duration="duration"
      @seek="handleSeek"
    />
    
    <!-- 控制按钮 -->
    <div class="controls-bar">
      <!-- 播放/暂停按钮 -->
      <div class="control-group left">
        <button 
          class="control-btn play-btn"
          @click="handlePlayPause"
          :title="isPlaying ? '暂停' : '播放'"
        >
          <el-icon>
            <VideoPlay v-if="!isPlaying" />
            <VideoPause v-else />
          </el-icon>
        </button>

        <!-- 时间显示 -->
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="separator">/</span>
          <span class="total-time">{{ formatTime(duration) }}</span>
        </div>
      </div>

      <!-- 右侧控制组 -->
      <div class="control-group right">
        <!-- 音量控制 -->
        <div class="volume-control">
          <button 
            class="control-btn volume-btn"
            @click="handleMuteToggle"
            :title="isMuted ? '取消静音' : '静音'"
          >
            <el-icon>
              <Mute v-if="isMuted" />
              <VideoCamera v-else-if="volume > 0.5" />
              <VideoCamera v-else />
            </el-icon>
          </button>
          
          <div class="volume-slider" @mouseenter="showVolumeSlider = true" @mouseleave="showVolumeSlider = false">
            <el-slider
              v-show="showVolumeSlider"
              v-model="volumeValue"
              :min="0"
              :max="100"
              :show-tooltip="false"
              size="small"
              @input="handleVolumeChange"
              class="volume-range"
              vertical
              height="60px"
            />
          </div>
        </div>

        <!-- 全屏按钮 -->
        <button 
          class="control-btn fullscreen-btn"
          @click="handleFullscreen"
          :title="isFullscreen ? '退出全屏' : '全屏'"
        >
          <el-icon>
            <FullScreen v-if="!isFullscreen" />
            <Aim v-else />
          </el-icon>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { VideoPlay, VideoPause, Mute, VideoCamera, FullScreen, Aim } from '@element-plus/icons-vue'
import VideoProgress from './VideoProgress.vue'

// 定义属性
interface Props {
  isPlaying: boolean
  isMuted: boolean
  volume: number
  currentTime: number
  duration: number
  isFullscreen: boolean
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits<{
  'play': []
  'pause': []
  'mute': []
  'volume-change': [volume: number]
  'seek': [time: number]
  'fullscreen': []
}>()

// 控制栏可见性
const isVisible = ref(true)
const showVolumeSlider = ref(false)

// 音量值（0-100）
const volumeValue = computed({
  get: () => Math.round(props.volume * 100),
  set: (value: number) => {
    emit('volume-change', value / 100)
  }
})

// 监听音量变化
watch(() => props.volume, (newVolume) => {
  // 同步音量值
})

// 播放/暂停切换
const handlePlayPause = () => {
  if (props.isPlaying) {
    emit('pause')
  } else {
    emit('play')
  }
}

// 静音切换
const handleMuteToggle = () => {
  emit('mute')
}

// 音量变化
const handleVolumeChange = (value: number) => {
  emit('volume-change', value / 100)
}

// 进度跳转
const handleSeek = (time: number) => {
  emit('seek', time)
}

// 全屏切换
const handleFullscreen = () => {
  emit('fullscreen')
}

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// 控制栏自动隐藏逻辑
let hideTimer: number | null = null

const showControls = () => {
  isVisible.value = true
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
  hideTimer = window.setTimeout(() => {
    if (props.isPlaying) {
      isVisible.value = false
    }
  }, 3000)
}

const hideControls = () => {
  if (!props.isPlaying) return
  isVisible.value = false
}

// 暴露方法
defineExpose({
  showControls,
  hideControls
})
</script>

<style scoped lang="scss">
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  transition: opacity 0.3s ease;
  z-index: 10;

  &:not(.is-visible) {
    opacity: 0;
    pointer-events: none;
  }

  .controls-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    height: 48px;

    .control-group {
      display: flex;
      align-items: center;
      gap: 8px;

      &.left {
        flex: 1;
      }

      &.right {
        flex-shrink: 0;
      }
    }

    .control-btn {
      width: 32px;
      height: 32px;
      border: none;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);

      .el-icon {
        font-size: 16px;
      }
    }

    .time-display {
      font-size: 13px;
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      margin-left: 4px;

      .separator {
        margin: 0 4px;
        opacity: 0.6;
      }
    }

    .volume-control {
      position: relative;
      display: flex;
      align-items: center;

      .volume-slider {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 8px;
        background: rgba(0, 0, 0, 0.8);
        padding: 8px;
        border-radius: 6px;
        backdrop-filter: blur(10px);

        .volume-range {
          :deep(.el-slider__runway) {
            background-color: rgba(255, 255, 255, 0.3);
          }

          :deep(.el-slider__bar) {
            background-color: #409eff;
          }

          :deep(.el-slider__button) {
            border-color: #409eff;
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .controls-bar {
      padding: 6px 8px;
      height: 40px;

      .control-btn {
        width: 28px;
        height: 28px;

        .el-icon {
          font-size: 14px;
        }
      }

      .time-display {
        font-size: 12px;
      }
    }
  }
}
</style> 