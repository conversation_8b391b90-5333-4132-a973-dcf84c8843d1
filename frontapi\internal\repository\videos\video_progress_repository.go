package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type VideoProgressRepository interface {
	base.ExtendedRepository[videos.VideoProgress]
	FindByUserIDAndVideoID(ctx context.Context, userID, videoID string) (*videos.VideoProgress, error)
	UpdateProgress(ctx context.Context, userID, videoID string, progress uint) error
	UpdateDuration(ctx context.Context, userID, videoID string, duration uint) error
	UpdateIsFinished(ctx context.Context, userID, videoID string, isFinished uint8) error
}
type videoProgressRepository struct {
	base.ExtendedRepository[videos.VideoProgress]
}

// NewVideoProgressRepository creates a new instance of VideoProgressRepository.
func NewVideoProgressRepository(db *gorm.DB) VideoProgressRepository {
	return &videoProgressRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoProgress](db),
	}
}

func (r *videoProgressRepository) FindByUserIDAndVideoID(ctx context.Context, userID, videoID string) (*videos.VideoProgress, error) {
	conditions := map[string]interface{}{
		"user_id":  userID,
		"video_id": videoID,
	}
	return r.FindOneByCondition(ctx, conditions, "")
}

func (r *videoProgressRepository) UpdateProgress(ctx context.Context, userID, videoID string, position uint) error {
	var progress videos.VideoProgress
	err := r.GetDBWithContext(ctx).Where("user_id = ? AND video_id = ?", userID, videoID).First(&progress).Error
	if err != nil {
		return err
	}
	return r.GetDBWithContext(ctx).Model(&videos.VideoProgress{}).Where("user_id = ? AND video_id = ?", userID, videoID).Update("progress", position).Error
}

func (r *videoProgressRepository) UpdateDuration(ctx context.Context, userID, videoID string, duration uint) error {
	return r.GetDBWithContext(ctx).Model(&videos.VideoProgress{}).Where("user_id = ? AND video_id = ?", userID, videoID).Update("duration", duration).Error
}

func (r *videoProgressRepository) UpdateIsFinished(ctx context.Context, userID, videoID string, isFinished uint8) error {
	return r.GetDBWithContext(ctx).Model(&videos.VideoProgress{}).Where("user_id = ? AND video_id = ?", userID, videoID).Update("is_finished", isFinished).Error
}
