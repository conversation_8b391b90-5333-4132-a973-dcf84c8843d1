<template>
  <el-form :inline="true" :model="searchForm" class="search-form">
    <el-form-item label="章节标题">
      <el-input v-model="searchForm.title" placeholder="请输入章节标题" clearable></el-input>
    </el-form-item>
    <el-form-item label="状态">
      <el-select v-model="searchForm.status" placeholder="所有状态" clearable style="width: 200px;">
        <el-option label="启用" :value="1"></el-option>
        <el-option label="禁用" :value="0"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="onSearch">搜索</el-button>
      <el-button @click="onReset">重置</el-button>
      <el-button type="success" @click="emit('refresh')">刷新</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive } from 'vue';

const emit = defineEmits(['search', 'reset', 'refresh']);

const searchForm = reactive({
  title: '',
  status: undefined
});

const onSearch = () => {
  emit('search', { ...searchForm });
};

const onReset = () => {
  searchForm.title = '';
  searchForm.status = undefined;
  emit('reset');
};
</script>