package bootstrap

import (
	userSrv "frontapi/internal/service/users"

	"github.com/gofiber/fiber/v2"
)

// ===== 请求上下文部分 =====

// ContextKey 用于fiber.Ctx.Locals的键
type ContextKey string

const (
	CtxUserID     ContextKey = "user_id"
	CtxUserRole   ContextKey = "user_role"
	CtxRequestID  ContextKey = "request_id"
	CtxController ContextKey = "controller"
)

// ControllerMiddleware 控制器中间件
// 在请求处理前设置必要的上下文信息
func ControllerMiddleware() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// 存储请求ID用于跟踪
		c.Locals(string(CtxRequestID), c.Get("X-Request-ID", ""))
		return c.Next()
	}
}

// ===== 控制器基础部分 =====

// ControllerCore 控制器基础结构
// 采用组合优于继承的设计原则
type ControllerCore struct {
	// 不存储任何状态，使其成为无状态组件
	// 这样可以安全地在多个请求间共享同一个控制器实例
}

// NewControllerCore 创建基础控制器
func NewControllerCore() *ControllerCore {
	return &ControllerCore{}
}

// 服务访问方法 - 使用类型断言提供类型安全

// GetUserService 获取用户服务
func (c *ControllerCore) GetUserService() userSrv.UserService {
	return GetService("UserService").(userSrv.UserService)
}

// GetUserService 获取用户服务
func (c *ControllerCore) GetUserFollowService() userSrv.UserFollowsService {
	return GetService("UserFollowsService").(userSrv.UserFollowsService)
}

// // GetPictureService 获取图片服务
// func (c *ControllerCore) GetPictureService() interface{} {
// 	return GetService("PictureService")
// }

// // GetPictureAlbumService 获取图片专辑服务
// func (c *ControllerCore) GetPictureAlbumService() interface{} {
// 	return GetService("PictureAlbumService")
// }

// // GetPictureCategoryService 获取图片分类服务
// func (c *ControllerCore) GetPictureCategoryService() interface{} {
// 	return GetService("PictureCategoryService")
// }

// // GetVideoService 获取视频服务
// func (c *ControllerCore) GetVideoService() interface{} {
// 	return GetService("VideoService")
// }

// // GetShortVideoService 获取短视频服务
// func (c *ControllerCore) GetShortVideoService() interface{} {
// 	return GetService("ShortVideoService")
// }

// // GetPostService 获取帖子服务
// func (c *ControllerCore) GetPostService() interface{} {
// 	return GetService("PostService")
// }

// // GetPostCommentService 获取帖子评论服务
// func (c *ControllerCore) GetPostCommentService() interface{} {
// 	return GetService("PostCommentService")
// }
