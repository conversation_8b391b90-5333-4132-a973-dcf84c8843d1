package container

import (
	sysRepo "frontapi/internal/repository/system"
	vipRepo "frontapi/internal/repository/vips"
	sysService "frontapi/internal/service/system"
	vipService "frontapi/internal/service/vips"
)

// InitSystemServices 初始化系统相关服务
func InitSystemServices(b *ServiceBuilder) {
	// 平台币充值套餐相关仓库和服务
	coinPackageRepoImpl := vipRepo.NewCoinPackageRepository(b.DB())
	b.Services().CoinPackageService = vipService.NewCoinPackageService(coinPackageRepoImpl)

	// 国家/地区相关仓库和服务
	countryRepo := sysRepo.NewCountryRepository(b.DB())
	b.Services().CountryService = sysService.NewCountryService(countryRepo)
}
