import * as XLSX from 'xlsx'

// 导出配置接口
export interface ExportConfig {
    filename?: string
    sheetName?: string
    columns: ExportColumn[]
    data: any[]
    dateFormat?: string
    numberFormat?: string
}

export interface ExportColumn {
    prop: string
    label: string
    width?: number
    type?: 'text' | 'number' | 'date' | 'boolean' | 'image'
    formatter?: (value: any, row: any) => string
}

// Excel导出器类
export class ExcelExporter {
    private config: ExportConfig

    constructor(config: ExportConfig) {
        this.config = {
            filename: 'export',
            sheetName: 'Sheet1',
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
            numberFormat: '0.00',
            ...config
        }
    }

    /**
     * 导出Excel文件
     */
    export(): void {
        const workbook = XLSX.utils.book_new()
        const worksheet = this.createWorksheet()

        XLSX.utils.book_append_sheet(workbook, worksheet, this.config.sheetName)

        const filename = `${this.config.filename}_${this.formatDate(new Date(), 'YYYYMMDD_HHmmss')}.xlsx`
        XLSX.writeFile(workbook, filename)
    }

    /**
     * 创建工作表
     */
    private createWorksheet(): XLSX.WorkSheet {
        // 准备表头
        const headers = this.config.columns.map(col => col.label)

        // 准备数据
        const rows = this.config.data.map(row =>
            this.config.columns.map(col => this.formatCellValue(row[col.prop], col, row))
        )

        // 合并表头和数据
        const sheetData = [headers, ...rows]

        // 创建工作表
        const worksheet = XLSX.utils.aoa_to_sheet(sheetData)

        // 设置列宽
        this.setColumnWidths(worksheet)

        return worksheet
    }

    /**
     * 格式化单元格值
     */
    private formatCellValue(value: any, column: ExportColumn, row: any): string {
        // 如果有自定义格式化函数，优先使用
        if (column.formatter) {
            return column.formatter(value, row)
        }

        // 根据类型进行格式化
        switch (column.type) {
            case 'date':
                return this.formatDate(value, this.config.dateFormat!)
            case 'number':
                return this.formatNumber(value)
            case 'boolean':
                return value ? '是' : '否'
            case 'image':
                return value ? '有图片' : '无图片'
            default:
                return value != null ? String(value) : ''
        }
    }

    /**
     * 格式化日期
     */
    private formatDate(date: any, format: string): string {
        if (!date) return ''

        const d = new Date(date)
        if (isNaN(d.getTime())) return ''

        const year = d.getFullYear()
        const month = String(d.getMonth() + 1).padStart(2, '0')
        const day = String(d.getDate()).padStart(2, '0')
        const hours = String(d.getHours()).padStart(2, '0')
        const minutes = String(d.getMinutes()).padStart(2, '0')
        const seconds = String(d.getSeconds()).padStart(2, '0')

        return format
            .replace('YYYY', year.toString())
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds)
    }

    /**
     * 格式化数字
     */
    private formatNumber(value: any): string {
        if (value == null || value === '') return ''

        const num = Number(value)
        if (isNaN(num)) return String(value)

        return num.toFixed(2)
    }

    /**
     * 设置列宽
     */
    private setColumnWidths(worksheet: XLSX.WorkSheet): void {
        const colWidths = this.config.columns.map(col => ({
            wch: col.width || 15
        }))

        worksheet['!cols'] = colWidths
    }

    /**
     * 静态方法：快速导出
     */
    static quickExport(data: any[], columns: ExportColumn[], filename?: string): void {
        const exporter = new ExcelExporter({
            data,
            columns,
            filename: filename || 'export'
        })
        exporter.export()
    }

    /**
     * 静态方法：从表格配置导出
     */
    static exportFromTable(
        data: any[],
        tableColumns: any[],
        filename?: string
    ): void {
        const exportColumns: ExportColumn[] = tableColumns
            .filter(col => col.prop && col.label)
            .map(col => ({
                prop: col.prop,
                label: col.label,
                type: col.type || 'text',
                formatter: col.formatter,
                width: col.width ? col.width / 8 : 15 // 转换为Excel列宽
            }))

        this.quickExport(data, exportColumns, filename)
    }
}

// 常用的格式化函数
export const commonFormatters = {
    // 状态格式化
    status: (value: any, options: { value: any; label: string }[]) => {
        const option = options.find(opt => opt.value === value)
        return option?.label || '未知'
    },

    // 文件大小格式化
    fileSize: (bytes: number) => {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 货币格式化
    currency: (value: number, symbol = '¥') => {
        if (value == null) return ''
        return symbol + value.toFixed(2)
    },

    // 百分比格式化
    percent: (value: number) => {
        if (value == null) return ''
        return (value * 100).toFixed(2) + '%'
    }
}

// 预设的导出配置
export const presetConfigs = {
    // 用户数据导出
    users: {
        columns: [
            { prop: 'id', label: 'ID', width: 8 },
            { prop: 'username', label: '用户名', width: 15 },
            { prop: 'email', label: '邮箱', width: 25 },
            {
                prop: 'status',
                label: '状态',
                width: 10,
                formatter: (value: any) => commonFormatters.status(value, [
                    { value: 1, label: '启用' },
                    { value: 0, label: '禁用' }
                ])
            },
            { prop: 'created_at', label: '创建时间', type: 'date', width: 20 }
        ]
    },

    // 订单数据导出
    orders: {
        columns: [
            { prop: 'order_no', label: '订单号', width: 20 },
            { prop: 'customer_name', label: '客户姓名', width: 15 },
            { prop: 'amount', label: '金额', type: 'number', width: 12 },
            {
                prop: 'status',
                label: '状态',
                width: 12,
                formatter: (value: any) => commonFormatters.status(value, [
                    { value: 'pending', label: '待处理' },
                    { value: 'paid', label: '已支付' },
                    { value: 'shipped', label: '已发货' },
                    { value: 'completed', label: '已完成' },
                    { value: 'cancelled', label: '已取消' }
                ])
            },
            { prop: 'created_at', label: '下单时间', type: 'date', width: 20 }
        ]
    }
}

export default ExcelExporter 