package books

import (
	"frontapi/internal/models"
)

// Bookmark 电子书书签模型
type Bookmark struct {
	models.BaseModel
	UserID    string `json:"userId" gorm:"type:string;size:36;not null;index:idx_user_id"`
	BookID    string `json:"bookId" gorm:"type:string;size:36;not null;index:idx_book_id"`
	ChapterID string `json:"chapterId" gorm:"type:string;size:36;not null;index:idx_chapter_id"`
	Position  int    `json:"position" gorm:"type:int;default:0"`
	Title     string `json:"title" gorm:"type:string"`
	Content   string `json:"content" gorm:"type:text"`
}

// TableName 设置表名
func (Bookmark) TableName() string {
	return "ly_book_bookmarks"
}
