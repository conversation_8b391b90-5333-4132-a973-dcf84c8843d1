# Mock系统 - Images字段数组规则功能

## 功能概述

在Mock数据生成系统中，新增了对`images`字段的智能识别功能。当数据库表中有名为`images`的字段，且该字段类型为`json`、`text`或`longtext`时，系统会自动识别并生成图片URL数组的JSON格式数据。

## 近期修复

### 问题修复 (2024-12-19)
修复了生成Mock数据时显示函数字符串而非实际数据的问题：
- **问题**：生成的数据显示为 `"() => generateImageArray()"` 而不是实际的图片数组
- **解决方案**：使用特殊标识 `GENERATE_IMAGE_ARRAY` 替代函数字符串，在解析时正确转换为函数调用
- **影响**：现在能正确生成 `["https://picsum.photos/800/600?random=123", ...]` 格式的数据

## 功能特点

### 1. 智能识别
- 自动识别字段名为`images`的字段
- 支持`json`、`text`、`longtext`数据类型
- 基于Go语言中的`[]string`类型设计

### 2. 数据格式
生成的数据为JSON格式的字符串数组：
```json
["https://picsum.photos/800/600?random=123", "https://picsum.photos/1200/800?random=456", "https://picsum.photos/400/300?random=789"]
```

### 3. 随机特性
- 随机生成1-5张图片
- 图片尺寸随机选择：
  - 宽度：400px, 600px, 800px, 1200px
  - 高度：300px, 400px, 600px, 800px
- 使用Picsum作为图片源，确保图片真实可用

## 使用场景

### 1. 数据库表设计
适用于以下表结构：
```sql
CREATE TABLE posts (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255),
    content TEXT,
    images JSON COMMENT '帖子图片,与视频只能存在一个'
);

CREATE TABLE comments (
    id VARCHAR(36) PRIMARY KEY,
    content TEXT,
    images TEXT COMMENT '评论图片'
);
```

### 2. Go模型定义
对应的Go结构体：
```go
type Post struct {
    ID     string            `json:"id"`
    Title  string            `json:"title"`
    Images types.StringArray `json:"images" gorm:"type:json;comment:帖子图片"`
}

type Comment struct {
    ID     string      `json:"id"`
    Images null.String `json:"images" gorm:"type:text;comment:评论图片"`
}
```

## 功能实现

### 1. 后端Mock生成器 (mockDataGenerator.ts)
```typescript
// 解析规则时处理特殊标识
private parseMockRule(rule: string, column: ColumnInfo): any {
  // 处理特殊标识
  if (rule === 'GENERATE_IMAGE_ARRAY') {
    return () => this.generateImageArray();
  }
  
  // 处理函数调用字符串（向后兼容）
  if (rule.includes('generateImageArray()')) {
    return () => this.generateImageArray();
  }
  // ...其他规则处理
}

// 智能识别images字段
if (columnName === 'images' && (dataType === 'json' || dataType === 'text' || dataType === 'longtext')) {
  return () => this.generateImageArray();
}

private generateImageArray(): string {
  // 随机生成1-5张图片
  const imageCount = Math.floor(Math.random() * 5) + 1;
  const images: string[] = [];
  
  for (let i = 0; i < imageCount; i++) {
    // 随机尺寸和URL生成
    const widths = [400, 600, 800, 1200];
    const heights = [300, 400, 600, 800];
    const width = widths[Math.floor(Math.random() * widths.length)];
    const height = heights[Math.floor(Math.random() * heights.length)];
    
    const randomId = Math.floor(Math.random() * 1000) + 1;
    images.push(`https://picsum.photos/${width}/${height}?random=${randomId}`);
  }
  
  return JSON.stringify(images);
}
```

### 2. 前端规则推断 (MockRules.ts)
```typescript
// 特殊处理：images字段（JSON数组）
if (lowerName === 'images') {
  return 'images_array';
}

// Mock规则模板（使用特殊标识）
mockRuleTemplates: {
  images_array: 'GENERATE_IMAGE_ARRAY',
  // ...其他规则
}
```

### 3. 手动选择选项
在Mock规则配置界面中，用户可以手动选择：
- **网络相关** > **图片数组 (JSON)**

## 生成示例

### 输入表结构
```sql
CREATE TABLE ly_posts (
    id VARCHAR(36) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    images JSON COMMENT '帖子图片'
);
```

### 生成的Mock数据
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "title": "精彩内容分享",
  "content": "这是一段精彩的内容描述...",
  "images": "[\"https://picsum.photos/800/600?random=234\", \"https://picsum.photos/1200/400?random=567\", \"https://picsum.photos/600/800?random=890\"]"
}
```

### 对应SQL语句
```sql
INSERT INTO ly_posts (id, title, content, images) VALUES 
('550e8400-e29b-41d4-a716-446655440000', '精彩内容分享', '这是一段精彩的内容描述...', '[\"https://picsum.photos/800/600?random=234\", \"https://picsum.photos/1200/400?random=567\", \"https://picsum.photos/600/800?random=890\"]');
```

## 技术细节

### 规则处理流程
1. **智能识别**：系统检测到`images`字段时自动应用规则
2. **规则标识**：使用`GENERATE_IMAGE_ARRAY`作为特殊标识
3. **函数转换**：在`parseMockRule`中将标识转换为实际的函数调用
4. **Mock执行**：Mock.js库执行函数并生成真实数据
5. **JSON序列化**：函数返回已序列化的JSON字符串

### 兼容性处理
- 支持旧版本的函数字符串格式（向后兼容）
- 新版本使用特殊标识避免字符串解析问题
- 确保在各种使用场景下都能正确生成数据

## 注意事项

1. **字段名匹配**：只有确切名为`images`的字段才会触发此规则
2. **数据类型限制**：仅支持`json`、`text`、`longtext`类型
3. **JSON格式**：生成的数据为JSON字符串格式，可直接存储到数据库
4. **图片可用性**：使用Picsum服务，确保生成的图片链接真实有效
5. **数组长度**：每次生成1-5张图片，模拟真实使用场景
6. **函数执行**：确保使用特殊标识而非函数字符串，避免生成错误数据

## 扩展建议

如需支持其他类似字段（如`photos`、`pictures`等），可以在代码中添加相应的判断条件：

```typescript
if ((columnName === 'images' || columnName === 'photos' || columnName === 'pictures') && 
    (dataType === 'json' || dataType === 'text' || dataType === 'longtext')) {
  return () => this.generateImageArray();
}
``` 