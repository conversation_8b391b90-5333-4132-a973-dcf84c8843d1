/* Basic editor styles for UmoEditor */
.umo-editor {
  position: relative;
  width: 100%;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.umo-editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 8px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #f5f7fa;
}

.umo-editor-toolbar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.umo-editor-toolbar-item:hover {
  background-color: #ecf5ff;
}

.umo-editor-toolbar-item.is-active {
  color: #409eff;
  background-color: #ecf5ff;
}

.umo-editor-content {
  min-height: 300px;
  padding: 12px 15px;
  outline: none;
  overflow-y: auto;
}

.umo-editor-content h1,
.umo-editor-content h2,
.umo-editor-content h3,
.umo-editor-content h4,
.umo-editor-content h5,
.umo-editor-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 500;
  line-height: 1.2;
}

.umo-editor-content h1 {
  font-size: 2em;
}

.umo-editor-content h2 {
  font-size: 1.5em;
}

.umo-editor-content h3 {
  font-size: 1.17em;
}

.umo-editor-content p {
  margin: 1em 0;
}

.umo-editor-content ul,
.umo-editor-content ol {
  padding-left: 2em;
  margin: 1em 0;
}

.umo-editor-content blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #dcdfe6;
  background-color: #f8f8f8;
  color: #606266;
}

.umo-editor-content img {
  max-width: 100%;
}

.umo-editor-content a {
  color: #409eff;
  text-decoration: none;
}

.umo-editor-content a:hover {
  text-decoration: underline;
}

.umo-editor-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.umo-editor-content th,
.umo-editor-content td {
  border: 1px solid #dcdfe6;
  padding: 0.5em;
}

.umo-editor-content th {
  background-color: #f5f7fa;
  font-weight: bold;
}

/* Additional styles for UmoEditor */

/* Container styles */
.umo-editor-wrapper {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

/* Toolbar styles */
.umo-editor-toolbar {
  border-bottom: 1px solid #dcdfe6 !important;
  background-color: #f5f7fa !important;
  padding: 4px !important;
}

.umo-editor-toolbar-item {
  margin: 0 2px !important;
}

.umo-editor-toolbar-item.is-active {
  color: #409eff !important;
  background-color: #ecf5ff !important;
}

.umo-editor-toolbar-item:hover {
  background-color: #e6f1fc !important;
}

/* Content area styles */
.umo-editor-content {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  color: #303133 !important;
  padding: 12px 15px !important;
}

/* Typography styles */
.umo-editor-content h1 {
  font-size: 24px !important;
  margin-top: 16px !important;
  margin-bottom: 12px !important;
}

.umo-editor-content h2 {
  font-size: 20px !important;
  margin-top: 14px !important;
  margin-bottom: 10px !important;
}

.umo-editor-content h3 {
  font-size: 18px !important;
  margin-top: 12px !important;
  margin-bottom: 8px !important;
}

.umo-editor-content p {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.umo-editor-content ul,
.umo-editor-content ol {
  padding-left: 24px !important;
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.umo-editor-content blockquote {
  border-left: 4px solid #dcdfe6 !important;
  padding-left: 12px !important;
  color: #606266 !important;
  font-style: italic !important;
  margin: 8px 0 !important;
}

.umo-editor-content code {
  background-color: #f5f7fa !important;
  padding: 2px 4px !important;
  border-radius: 2px !important;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace !important;
}

.umo-editor-content pre {
  background-color: #f5f7fa !important;
  padding: 12px !important;
  border-radius: 4px !important;
  overflow-x: auto !important;
}

.umo-editor-content table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 12px 0 !important;
}

.umo-editor-content th,
.umo-editor-content td {
  border: 1px solid #dcdfe6 !important;
  padding: 8px !important;
}

.umo-editor-content th {
  background-color: #f5f7fa !important;
  font-weight: bold !important;
}

/* Link styles */
.umo-editor-content a {
  color: #409eff !important;
  text-decoration: none !important;
}

.umo-editor-content a:hover {
  text-decoration: underline !important;
}

/* Image styles */
.umo-editor-content img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  margin: 12px 0 !important;
}

/* Placeholder styles */
.umo-editor-content .is-empty::before {
  color: #909399 !important;
  font-style: italic !important;
}

/* Focus styles */
.umo-editor-content:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
} 