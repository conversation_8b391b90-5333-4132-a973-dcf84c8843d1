/**
 * 时尚前卫主题 - CSS变量
 * 黑色主调 + 紫色 + 电光蓝
 * 设计参考: Tailwind Dark + PrimeVue暗色主题
 */

// CSS变量配置
const variables = {
    // 主要颜色
    'color-primary': '#6366f1',
    'color-primary-light': '#818cf8',
    'color-primary-dark': '#4f46e5',
    'color-primary-contrast': '#ffffff',
    'color-primary-secondary': '#a5b4fc', // 第二配色 - 比主色浅的相同色系

    // 强调色
    'color-accent': '#0ea5e9',
    'color-accent-light': '#38bdf8',
    'color-accent-dark': '#0284c7',
    'color-accent-contrast': '#ffffff',

    // 中性色调 - 暗色主题
    'color-neutral-50': '#f9fafb',
    'color-neutral-100': '#f3f4f6',
    'color-neutral-200': '#e5e7eb',
    'color-neutral-300': '#d1d5db',
    'color-neutral-400': '#9ca3af',
    'color-neutral-500': '#6b7280',
    'color-neutral-600': '#4b5563',
    'color-neutral-700': '#374151',
    'color-neutral-800': '#1f2937',
    'color-neutral-900': '#111827',

    // 成功/错误/警告/信息色
    'color-success': '#34d399',
    'color-error': '#f87171',
    'color-warning': '#fbbf24',
    'color-info': '#60a5fa',

    // 背景颜色 - 暗色背景
    'color-background': '#0f172a',
    'color-background-alt': '#1e293b',
    'color-background-hover': '#334155',
    'color-background-card': 'linear-gradient(145deg, #1e293b, #0f172a)',
    // 主页面背景和文字色
    'color-page-background': '#0f172a',
    'color-page-text': '#f1f5f9',

    // 文本颜色 - 亮色文本
    'color-text': '#f9fafb',
    'color-text-light': '#e5e7eb',
    'color-text-lighter': '#d1d5db',
    'color-text-contrast': '#111827',

    // 边框颜色
    'color-border': '#334155',
    'color-border-light': '#475569',
    'color-border-dark': '#1e293b',

    // 阴影
    'shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
    'shadow': '0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.24)',
    'shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.24)',
    'shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',

    // 特定区域颜色
    'header-gradient': 'linear-gradient(135deg, #0f172a, #1e293b)',
    'footer-gradient': 'linear-gradient(135deg, #1e293b, #0f172a)',
    'footer-title': '#f9fafb',
    'footer-link': '#e5e7eb',
    'nav-text': '#FFFFFF',
    'footer-text': '#f9fafb',
    'color-nav-gradient': '#f9fafb',
    'color-footer-gradient': '#f9fafb',
    'color-footer-border': '#334155',
    'button-gradient': 'linear-gradient(135deg, #818cf8, #6366f1)',
    'card-gradient': 'linear-gradient(145deg, #1e293b, #0f172a)',
    'accent-gradient': 'linear-gradient(135deg, #38bdf8, #0ea5e9)',

    // PrimeVue集成
    'primary-color': '#6366f1',
    'primary-color-text': '#ffffff',
    'surface-ground': '#0f172a',
    'surface-section': '#0f172a',
    'surface-card': '#1e293b',
    'surface-overlay': '#1e293b',
    'surface-border': '#334155',
    'surface-hover': '#334155',
    'text-color': '#f1f5f9',
    'text-color-secondary': '#d1d5db',
    // 主页面内容区域
    'content-bg': '#0f172a',
    'content-text': '#f1f5f9',
};

export default variables;