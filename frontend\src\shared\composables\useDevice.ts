/**
 * 设备和网络相关组合式函数
 */

import { ref, computed, onMounted, onUnmounted, watch, reactive } from 'vue'
import type { Ref } from 'vue'
import {
  UseDeviceReturn,
  UseNetworkReturn,
  UseMediaQueryReturn,
  UseElementSizeReturn,
  UseElementVisibilityReturn,
  UseScrollReturn,
  UseMouseReturn,
  UseKeyboardReturn
} from './types'
import { detectDevice, detectNetwork } from '../../core/utils/device'

/**
 * 网络状态组合式函数
 */
export function useNetwork(): UseNetworkReturn {
  const online = ref(navigator.onLine)
  const offlineAt = ref<Date | undefined>()
  const onlineAt = ref<Date | undefined>()
  const downlink = ref<number | undefined>()
  const downlinkMax = ref<number | undefined>()
  const effectiveType = ref<string | undefined>()
  const rtt = ref<number | undefined>()
  const saveData = ref<boolean | undefined>()
  const type = ref<string | undefined>()

  const updateNetworkInfo = () => {
    const info = detectNetwork()
    downlink.value = info.downlink
    downlinkMax.value = info.downlink || 0
    effectiveType.value = info.effectiveType
    rtt.value = info.rtt
    saveData.value = info.saveData
    type.value = info.type
  }

  const onOnline = () => {
    online.value = true
    onlineAt.value = new Date()
    updateNetworkInfo()
  }

  const onOffline = () => {
    online.value = false
    offlineAt.value = new Date()
  }

  onMounted(() => {
    window.addEventListener('online', onOnline)
    window.addEventListener('offline', onOffline)
    updateNetworkInfo()

    // 监听网络变化
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connection?.addEventListener('change', updateNetworkInfo)
    }
  })

  onUnmounted(() => {
    window.removeEventListener('online', onOnline)
    window.removeEventListener('offline', onOffline)

    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connection?.removeEventListener('change', updateNetworkInfo)
    }
  })

  return {
    online,
    offlineAt,
    onlineAt,
    downlink,
    downlinkMax,
    effectiveType,
    rtt,
    saveData,
    type
  }
}

/**
 * 设备信息组合式函数
 */
export function useDevice(): UseDeviceReturn {
  const deviceInfo = ref(detectDevice())

  const isMobile = computed(() => deviceInfo.value.isMobile)
  const isTablet = computed(() => deviceInfo.value.isTablet)
  const isDesktop = computed(() => deviceInfo.value.isDesktop)
  const isIOS = computed(() => deviceInfo.value.os === 'ios')
  const isAndroid = computed(() => deviceInfo.value.os === 'android')
  // 浏览器检测（如果需要可以取消注释）
  // const isSafari = computed(() => deviceInfo.value.browser === 'safari')
  // const isChrome = computed(() => deviceInfo.value.browser === 'chrome')
  // const isFirefox = computed(() => deviceInfo.value.browser === 'firefox')
  // const isEdge = computed(() => deviceInfo.value.browser === 'edge')

  const orientation = computed(() => 
    deviceInfo.value.viewport.width > deviceInfo.value.viewport.height ? 'landscape' : 'portrait'
  )
  const screenSize = computed(() => ({
    width: deviceInfo.value.viewport.width,
    height: deviceInfo.value.viewport.height
  }))
  const pixelRatio = computed(() => window.devicePixelRatio || 1)
  const touchSupport = computed(() => deviceInfo.value.isTouchDevice)

  // 更新设备信息
  const updateDeviceInfo = () => {
    deviceInfo.value = detectDevice()
  }

  onMounted(() => {
    window.addEventListener('resize', updateDeviceInfo)
    window.addEventListener('orientationchange', updateDeviceInfo)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceInfo)
    window.removeEventListener('orientationchange', updateDeviceInfo)
  })

  return {
    deviceInfo,
    networkInfo: ref(detectNetwork()),
    isMobile,
    isTablet,
    isDesktop,
    isOnline: computed(() => navigator.onLine),
    isOffline: computed(() => !navigator.onLine),
    orientation,
    screenSize,
    pixelRatio,
    touchSupport,
    isIOS,
    isAndroid,
    isWindows: computed(() => deviceInfo.value.os === 'windows'),
    isMacOS: computed(() => deviceInfo.value.os === 'macos'),
    isLinux: computed(() => deviceInfo.value.os === 'linux'),
    browser: computed(() => deviceInfo.value.browser),
    browserVersion: computed(() => deviceInfo.value.userAgent),
    updateDeviceInfo,
    updateNetworkInfo: () => {}
  }
}

/**
 * 媒体查询组合式函数
 */
export function useMediaQuery(query: string): UseMediaQueryReturn {
  const matches = ref(false)
  let mediaQuery: MediaQueryList | null = null

  const updateMatches = () => {
    if (mediaQuery) {
      matches.value = mediaQuery.matches
    }
  }

  onMounted(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      mediaQuery = window.matchMedia(query)
      matches.value = mediaQuery.matches
      
      // 使用新的 addEventListener API
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', updateMatches)
      } else {
        // 兼容旧版本
        mediaQuery.addListener(updateMatches)
      }
    }
  })

  onUnmounted(() => {
    if (mediaQuery) {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', updateMatches)
      } else {
        // 兼容旧版本
        mediaQuery.removeListener(updateMatches)
      }
    }
  })

  return {
    matches
  }
}

/**
 * 元素尺寸组合式函数
 */
export function useElementSize(target?: Ref<HTMLElement | null>): UseElementSizeReturn {
  const width = ref(0)
  const height = ref(0)
  let resizeObserver: ResizeObserver | null = null

  const updateSize = (entries: ResizeObserverEntry[]) => {
    const entry = entries[0]
    if (entry) {
      const { width: w, height: h } = entry.contentRect
      width.value = w
      height.value = h
    }
  }

  const observe = (element: HTMLElement) => {
    if (resizeObserver) {
      resizeObserver.disconnect()
    }

    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(updateSize)
      resizeObserver.observe(element)
    }
  }

  const stop = () => {
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    }
  }

  onMounted(() => {
    if (target?.value) {
      observe(target.value)
    }
  })

  onUnmounted(() => {
    stop()
  })

  // 监听目标元素变化
  if (target) {
    watch(target, (newElement) => {
      if (newElement) {
        observe(newElement)
      } else {
        stop()
      }
    })
  }

  return {
    width,
    height,
    observe,
    stop
  }
}

/**
 * 元素可见性组合式函数
 */
export function useElementVisibility(target?: Ref<HTMLElement | null>): UseElementVisibilityReturn {
  const isVisible = ref(false)
  let intersectionObserver: IntersectionObserver | null = null

  const updateVisibility = (entries: IntersectionObserverEntry[]) => {
    const entry = entries[0]
    if (entry) {
      isVisible.value = entry.isIntersecting
    }
  }

  const observe = (element: HTMLElement) => {
    if (intersectionObserver) {
      intersectionObserver.disconnect()
    }

    if (typeof IntersectionObserver !== 'undefined') {
      intersectionObserver = new IntersectionObserver(updateVisibility, {
        threshold: 0.1
      })
      intersectionObserver.observe(element)
    }
  }

  const stop = () => {
    if (intersectionObserver) {
      intersectionObserver.disconnect()
      intersectionObserver = null
    }
  }

  onMounted(() => {
    if (target?.value) {
      observe(target.value)
    }
  })

  onUnmounted(() => {
    stop()
  })

  // 监听目标元素变化
  if (target) {
    watch(target, (newElement) => {
      if (newElement) {
        observe(newElement)
      } else {
        stop()
      }
    })
  }

  return {
    isVisible,
    observe,
    stop
  }
}

/**
 * 滚动组合式函数
 */
export function useScroll(target?: Ref<HTMLElement | Window | null>): UseScrollReturn {
  const x = ref(0)
  const y = ref(0)
  const isScrolling = ref(false)
  const arrivedState = reactive({
    left: true,
    right: false,
    top: true,
    bottom: false
  })
  const directions = reactive({
    left: false,
    right: false,
    top: false,
    bottom: false
  })

  let scrollTimer: NodeJS.Timeout | null = null

  const updateScroll = () => {
    const element = target?.value || window
    
    if (element === window) {
      x.value = window.scrollX || window.pageXOffset
      y.value = window.scrollY || window.pageYOffset
    } else if (element instanceof HTMLElement) {
      x.value = element.scrollLeft
      y.value = element.scrollTop
    }

    // 更新到达状态
    if (element === window) {
      const { innerWidth, innerHeight } = window
      const { scrollWidth, scrollHeight } = document.documentElement
      
      arrivedState.left = x.value <= 0
      arrivedState.right = x.value >= scrollWidth - innerWidth
      arrivedState.top = y.value <= 0
      arrivedState.bottom = y.value >= scrollHeight - innerHeight
    } else if (element instanceof HTMLElement) {
      arrivedState.left = x.value <= 0
      arrivedState.right = x.value >= element.scrollWidth - element.clientWidth
      arrivedState.top = y.value <= 0
      arrivedState.bottom = y.value >= element.scrollHeight - element.clientHeight
    }

    isScrolling.value = true
    
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    scrollTimer = setTimeout(() => {
      isScrolling.value = false
    }, 150)
  }

  const onScroll = () => {
    updateScroll()
  }

  onMounted(() => {
    const element = target?.value || window
    element.addEventListener('scroll', onScroll, { passive: true })
    updateScroll()
  })

  onUnmounted(() => {
    const element = target?.value || window
    element.removeEventListener('scroll', onScroll)
    
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
  })

  // 监听目标元素变化
  if (target) {
    watch(target, (newElement, oldElement) => {
      if (oldElement) {
        oldElement.removeEventListener('scroll', onScroll)
      }
      if (newElement) {
        newElement.addEventListener('scroll', onScroll, { passive: true })
        updateScroll()
      }
    })
  }

  return {
    x,
    y,
    isScrolling,
    arrivedState,
    directions
  }
}

/**
 * 鼠标位置组合式函数
 */
export function useMouse(): UseMouseReturn {
  const x = ref(0)
  const y = ref(0)
  const sourceType = ref<'mouse' | 'touch'>('mouse')

  const onMouseMove = (event: MouseEvent) => {
    x.value = event.clientX
    y.value = event.clientY
    sourceType.value = 'mouse'
  }

  const onTouchMove = (event: TouchEvent) => {
    if (event.touches.length > 0) {
      const touch = event.touches[0]
      x.value = touch.clientX
      y.value = touch.clientY
      sourceType.value = 'touch'
    }
  }

  onMounted(() => {
    window.addEventListener('mousemove', onMouseMove, { passive: true })
    window.addEventListener('touchmove', onTouchMove, { passive: true })
  })

  onUnmounted(() => {
    window.removeEventListener('mousemove', onMouseMove)
    window.removeEventListener('touchmove', onTouchMove)
  })

  return {
    x,
    y,
    sourceType
  }
}

/**
 * 键盘事件组合式函数
 */
export function useKeyboard(): UseKeyboardReturn {
  const pressed = ref(new Set<string>())
  const ctrl = ref(false)
  const shift = ref(false)
  const alt = ref(false)
  const meta = ref(false)

  const updateModifiers = (event: KeyboardEvent) => {
    ctrl.value = event.ctrlKey
    shift.value = event.shiftKey
    alt.value = event.altKey
    meta.value = event.metaKey
  }

  const onKeyDown = (event: KeyboardEvent) => {
    pressed.value.add(event.key)
    updateModifiers(event)
  }

  const onKeyUp = (event: KeyboardEvent) => {
    pressed.value.delete(event.key)
    updateModifiers(event)
  }

  const onBlur = () => {
    pressed.value.clear()
    ctrl.value = false
    shift.value = false
    alt.value = false
    meta.value = false
  }

  onMounted(() => {
    window.addEventListener('keydown', onKeyDown, { passive: true })
    window.addEventListener('keyup', onKeyUp, { passive: true })
    window.addEventListener('blur', onBlur, { passive: true })
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', onKeyDown)
    window.removeEventListener('keyup', onKeyUp)
    window.removeEventListener('blur', onBlur)
  })

  const isPressed = (key: string) => {
    return computed(() => pressed.value.has(key))
  }

  return {
    pressed,
    ctrl,
    shift,
    alt,
    meta,
    isPressed
  }
}