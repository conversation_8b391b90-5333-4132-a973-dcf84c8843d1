<template>
  <div v-if="mockData.length > 0" class="mock-data-preview">
    <h3>Mock数据预览</h3>
    
    <div class="action-buttons">
      <el-button type="info" @click="copySQL">复制SQL</el-button>
      <el-button type="warning" @click="exportData">导出数据</el-button>
    </div>
    
    <!-- 数据表格预览 -->
    <el-table 
      :data="mockData.slice(0, 10)" 
      border 
      style="width: 100%; margin-bottom: 20px;" 
      max-height="400"
    >
      <el-table-column
        v-for="column in Object.keys(mockData[0] || {})"
        :key="column"
        :prop="column"
        :label="column"
        min-width="120"
        show-overflow-tooltip
      />
    </el-table>

    <!-- 分页信息 -->
    <div v-if="mockData.length > 10" class="pagination-info">
      <el-alert
        :title="`显示前10条数据，共生成 ${mockData.length} 条数据`"
        type="info"
        :closable="false"
      />
    </div>

    <!-- SQL预览 -->
    <div class="sql-preview">
      <h4>生成的SQL语句</h4>
      <el-input
        :value="generatedSQL"
        type="textarea"
        :rows="8"
        readonly
        style="font-family: 'Courier New', monospace;"
        placeholder="SQL语句将在这里显示..."
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';

// Props
interface Props {
  mockData: Record<string, any>[];
  generatedSQL: string;
  selectedTable: string;
}

const props = defineProps<Props>();

// 复制SQL
const copySQL = async () => {
  if (!props.generatedSQL) {
    ElMessage.warning('没有可复制的SQL');
    return;
  }

  try {
    await navigator.clipboard.writeText(props.generatedSQL);
    ElMessage.success('SQL已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
};

// 导出数据
const exportData = () => {
  if (!props.mockData.length) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  try {
    const jsonStr = JSON.stringify(props.mockData, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${props.selectedTable}_mock_data.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    ElMessage.success('数据导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};
</script>

<style scoped>
.mock-data-preview {
  margin-top: 20px;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.mock-data-preview h3,
.mock-data-preview h4 {
  margin-top: 0;
  color: #303133;
}

.action-buttons {
  margin-bottom: 15px;
}

.pagination-info {
  margin-top: 10px;
  margin-bottom: 20px;
}

.sql-preview {
  margin-top: 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style> 