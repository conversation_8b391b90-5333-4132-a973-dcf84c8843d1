package shortvideos

import (
	"frontapi/internal/models"
)

// ShortVideoCommentLike 短视频评论点赞模型
type ShortVideoCommentLike struct {
	models.BaseModel
	UserID    string `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	CommentID string `json:"comment_id" gorm:"type:string;size:36;not null;comment:评论ID"`
}

// TableName 指定表名
func (ShortVideoCommentLike) TableName() string {
	return "ly_shorts_comment_likes"
}
