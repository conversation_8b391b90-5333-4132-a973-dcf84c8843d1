package shortvideos

import (
	"frontapi/internal/models"

	"github.com/guregu/null/v6"
)

// ShortVideoCategory 短视频分类模型
type ShortVideoCategory struct {
	models.CategoryBaseModel
	Code     string      `json:"code" gorm:"not null;unique;comment:分类编码"`
	ParentID null.String `json:"parent_id" gorm:"type:string;size:36;comment:父分类ID"`
	URI      string      `json:"uri" gorm:"comment:URI标识"`
	Icon     string      `json:"icon" gorm:"comment:分类图标"`
	Image    string      `json:"image" gorm:"comment:分类图片"`
}

// TableName 指定表名
func (ShortVideoCategory) TableName() string {
	return "ly_shorts_categories"
}
