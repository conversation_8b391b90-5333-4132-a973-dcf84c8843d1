package hooks

import (
	"context"
	"fmt"
	"sort"
	"sync"

	"frontapi/config/constant"
)

// HookType 钩子类型别名
type HookType = constant.HookType

// 钩子类型常量别名
const (
	// Service层钩子
	BeforeCreate = constant.BeforeCreate
	AfterCreate  = constant.AfterCreate
	BeforeUpdate = constant.BeforeUpdate
	AfterUpdate  = constant.AfterUpdate
	BeforeDelete = constant.BeforeDelete
	AfterDelete  = constant.AfterDelete
	BeforeFind   = constant.BeforeFind
	AfterFind    = constant.AfterFind

	// 批量操作钩子
	BeforeBatchCreate = constant.BeforeBatchCreate
	AfterBatchCreate  = constant.AfterBatchCreate
	BeforeBatchUpdate = constant.BeforeBatchUpdate
	AfterBatchUpdate  = constant.AfterBatchUpdate
	BeforeBatchDelete = constant.BeforeBatchDelete
	AfterBatchDelete  = constant.AfterBatchDelete

	// Repository层钩子
	BeforeDBCreate = constant.BeforeDBCreate
	AfterDBCreate  = constant.AfterDBCreate
	BeforeDBUpdate = constant.BeforeDBUpdate
	AfterDBUpdate  = constant.AfterDBUpdate
	BeforeDBDelete = constant.BeforeDBDelete
	AfterDBDelete  = constant.AfterDBDelete

	// Controller层钩子
	BeforeRequest  = constant.BeforeRequest
	AfterRequest   = constant.AfterRequest
	BeforeResponse = constant.BeforeResponse
	AfterResponse  = constant.AfterResponse
)

// HookFunc 钩子函数类型
type HookFunc func(ctx context.Context, data interface{}) error

// Hook 钩子结构
type Hook struct {
	Name        string   // 钩子名称
	Description string   // 钩子描述
	Priority    int      // 优先级（数字越小优先级越高）
	Func        HookFunc // 钩子函数
	Enabled     bool     // 是否启用
}

// HookContext 钩子上下文
type HookContext struct {
	HookType   HookType
	EntityType string
	Operation  string
	Metadata   map[string]interface{}
}

// LayeredHookManager 分层钩子管理器
type LayeredHookManager struct {
	mu    sync.RWMutex
	hooks map[string]map[HookType][]Hook // [entityType][hookType][]Hook
}

// NewLayeredHookManager 创建新的分层钩子管理器
func NewLayeredHookManager() *LayeredHookManager {
	return &LayeredHookManager{
		hooks: make(map[string]map[HookType][]Hook),
	}
}

// RegisterHook 注册钩子
// entityType: 实体类型，如 "post", "user", "category" 等
// hookType: 钩子类型
// hook: 钩子实例
func (m *LayeredHookManager) RegisterHook(entityType string, hookType HookType, hook Hook) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.hooks[entityType]; !exists {
		m.hooks[entityType] = make(map[HookType][]Hook)
	}

	if _, exists := m.hooks[entityType][hookType]; !exists {
		m.hooks[entityType][hookType] = []Hook{}
	}

	// 设置默认值
	if hook.Priority == 0 {
		hook.Priority = 100 // 默认优先级
	}
	if !hook.Enabled {
		hook.Enabled = true // 默认启用
	}

	m.hooks[entityType][hookType] = append(m.hooks[entityType][hookType], hook)

	// 按优先级排序
	sort.Slice(m.hooks[entityType][hookType], func(i, j int) bool {
		return m.hooks[entityType][hookType][i].Priority < m.hooks[entityType][hookType][j].Priority
	})
}

// ExecuteHooks 执行指定实体类型和钩子类型的所有钩子
func (m *LayeredHookManager) ExecuteHooks(ctx context.Context, entityType string, hookType HookType, data interface{}) error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 创建钩子上下文
	hookCtx := &HookContext{
		HookType:   hookType,
		EntityType: entityType,
		Metadata:   make(map[string]interface{}),
	}

	// 将钩子上下文添加到context中
	ctx = context.WithValue(ctx, "hookContext", hookCtx)

	if entityHooks, exists := m.hooks[entityType]; exists {
		if hooks, exists := entityHooks[hookType]; exists {
			for _, hook := range hooks {
				if !hook.Enabled {
					continue
				}

				if err := hook.Func(ctx, data); err != nil {
					return fmt.Errorf("钩子 '%s' 执行失败: %w", hook.Name, err)
				}
			}
		}
	}
	return nil
}

// ExecuteGlobalHooks 执行全局钩子（不区分实体类型）
func (m *LayeredHookManager) ExecuteGlobalHooks(ctx context.Context, hookType HookType, data interface{}) error {
	return m.ExecuteHooks(ctx, "*", hookType, data)
}

// DisableHook 禁用指定钩子
func (m *LayeredHookManager) DisableHook(entityType string, hookType HookType, hookName string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if entityHooks, exists := m.hooks[entityType]; exists {
		if hooks, exists := entityHooks[hookType]; exists {
			for i := range hooks {
				if hooks[i].Name == hookName {
					hooks[i].Enabled = false
					break
				}
			}
		}
	}
}

// EnableHook 启用指定钩子
func (m *LayeredHookManager) EnableHook(entityType string, hookType HookType, hookName string) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if entityHooks, exists := m.hooks[entityType]; exists {
		if hooks, exists := entityHooks[hookType]; exists {
			for i := range hooks {
				if hooks[i].Name == hookName {
					hooks[i].Enabled = true
					break
				}
			}
		}
	}
}

// GetHooks 获取指定实体类型和钩子类型的所有钩子
func (m *LayeredHookManager) GetHooks(entityType string, hookType HookType) []Hook {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if entityHooks, exists := m.hooks[entityType]; exists {
		if hooks, exists := entityHooks[hookType]; exists {
			return append([]Hook{}, hooks...) // 返回副本
		}
	}
	return []Hook{}
}

// ListAllHooks 列出所有注册的钩子
func (m *LayeredHookManager) ListAllHooks() map[string]map[HookType][]Hook {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]map[HookType][]Hook)
	for entityType, entityHooks := range m.hooks {
		result[entityType] = make(map[HookType][]Hook)
		for hookType, hooks := range entityHooks {
			result[entityType][hookType] = append([]Hook{}, hooks...) // 返回副本
		}
	}
	return result
}

// 全局钩子管理器实例
var GlobalHookManager = NewLayeredHookManager()
