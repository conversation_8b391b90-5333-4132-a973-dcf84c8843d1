<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <h2>Mock数据生成器</h2>
          <p>选择数据库表，自动生成测试数据</p>
        </div>
      </template>

      <!-- 表选择器 -->
      <TableSelector 
        :model-value="selectedTable"
        @update:model-value="selectedTable = $event"
        @table-changed="handleTableChanged"
      />

      <!-- 表结构信息 -->
      <TableStructure 
        v-if="tableDetail"
        :table-detail="tableDetail"
        :mock-rules="mockRules"
        :excluded-f-ks="excludedFKs"
        :excluded-columns="excludedColumns"
        @mock-rule-changed="handleMockRuleChanged"
        @foreign-key-toggled="handleForeignKeyToggled"
        @column-excluded="handleColumnExcluded"
      />

      <!-- Mock数据生成器 -->
      <MockDataGenerator
        v-if="selectedTable && tableDetail"
        :selected-table="selectedTable"
        :mock-data="mockData"
        :mock-rules="mockRules"
        :excluded-f-ks="excludedFKs"
        :excluded-columns="excludedColumns"
        :table-detail="tableDetail"
        @mock-data-generated="handleMockDataGenerated"
        @data-inserted="handleDataInserted"
      />

      <!-- Mock数据预览 -->
      <MockDataPreview
        v-if="mockData.length > 0"
        :mock-data="mockData"
        :generated-s-q-l="generatedSQL"
        :selected-table="selectedTable"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 组件导入
import TableSelector from './components/TableSelector.vue';
import TableStructure from './components/TableStructure.vue';
import MockDataGenerator from './components/MockDataGenerator.vue';
import MockDataPreview from './components/MockDataPreview.vue';

// 组合式函数导入
import { useTableDetail } from './composables/useTableDetail';
import { useMockRules } from './composables/useMockRules';

// 使用组合式函数
const { tableDetail, getTableDetail, clearTableDetail } = useTableDetail();
const { 
  mockRules, 
  excludedFKs, 
  excludedColumns,
  autoSetMockRules, 
  updateMockRules, 
  toggleForeignKey, 
  toggleColumnExclusion,
  resetRules 
} = useMockRules();

// 响应式数据
const selectedTable = ref('');
const mockData = ref<Record<string, any>[]>([]);
const generatedSQL = ref('');

// 监听表选择变化
watch(selectedTable, (newTable) => {
  if (!newTable) {
    clearTableDetail();
    resetRules();
    mockData.value = [];
    generatedSQL.value = '';
  }
});

// 处理表选择变化
const handleTableChanged = async (tableName: string) => {
  console.log('表选择变化:', tableName);
  
  if (!tableName) {
    return;
  }

  try {
    // 获取表详情
    const detail = await getTableDetail(tableName);
    
    console.log('获取到的表详情:', detail);
    console.log('tableDetail.value:', tableDetail.value);
    
    if (detail) {
      // 自动设置Mock规则
      autoSetMockRules(detail);
      
      // 清空之前的Mock数据
      mockData.value = [];
      generatedSQL.value = '';
      
      console.log('Mock规则设置完成:', mockRules.value);
      console.log('排除的外键:', excludedFKs.value);
      
      ElMessage.success('表信息加载成功');
    } else {
      console.error('获取表详情失败，detail为null');
    }
  } catch (error) {
    console.error('处理表选择变化失败:', error);
  }
};

// 处理Mock规则变化
const handleMockRuleChanged = (columnName: string, rule: string) => {
  const newRules = { ...mockRules.value };
  if (rule) {
    newRules[columnName] = rule;
  } else {
    delete newRules[columnName];
  }
  updateMockRules(newRules);
};

// 处理外键切换
const handleForeignKeyToggled = (constraintName: string) => {
  toggleForeignKey(constraintName);
};

// 处理列排除切换
const handleColumnExcluded = (columnName: string) => {
  toggleColumnExclusion(columnName);
};

// 处理Mock数据生成完成
const handleMockDataGenerated = (data: { mockData: Record<string, any>[], sql: string }) => {
  mockData.value = data.mockData;
  generatedSQL.value = data.sql;
  console.log('Mock数据生成完成:', data);
};

// 处理数据插入完成
const handleDataInserted = () => {
  console.log('数据插入完成');
  // 可以在这里添加其他逻辑，比如刷新统计信息等
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}
</style> 