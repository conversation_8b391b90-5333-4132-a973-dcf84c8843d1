// 基础类型
export * from './base'

// 用户相关类型
export * from './user'

// 社区相关类型
export * from './community'

// 短视频相关类型
export * from './shorts'

// 评论相关类型（兼容旧版本）
export interface CommentBase {
  id: string
  content: string
  userId: string
  userName: string
  userAvatar: string
  likesCount: number
  repliesCount: number
  isLiked?: boolean
  createdAt: string
  updatedAt: string
}

// 国家/地区类型（兼容旧版本）
export interface Country {
  code: string
  name: string
  nameEn: string
  flag: string
  dialCode: string
  continent: string
}

// 漫画相关类型（兼容旧版本）
export interface Comic {
  id: string
  title: string
  description: string
  cover: string
  author: string
  status: 'ongoing' | 'completed' | 'hiatus'
  tags: string[]
  category: string
  rating: number
  viewsCount: number
  likesCount: number
  chaptersCount: number
  lastChapter?: {
    id: string
    title: string
    number: number
    publishedAt: string
  }
  createdAt: string
  updatedAt: string
}

export interface ComicChapter {
  id: string
  comicId: string
  title: string
  number: number
  pages: {
    id: string
    url: string
    width: number
    height: number
    order: number
  }[]
  publishedAt: string
  createdAt: string
}

// 图片相关类型（兼容旧版本）
export interface Picture {
  id: string
  title: string
  description?: string
  url: string
  thumbnail: string
  width: number
  height: number
  size: number
  format: string
  tags: string[]
  category: string
  uploaderId: string
  uploader: {
    id: string
    username: string
    nickname: string
    avatar: string
  }
  viewsCount: number
  likesCount: number
  downloadsCount: number
  isLiked?: boolean
  createdAt: string
}

// 直播相关类型
export interface LiveStream {
  id: string
  title: string
  description: string
  cover: string
  streamUrl: string
  playbackUrl?: string
  status: 'preparing' | 'live' | 'ended' | 'error'
  category: string
  tags: string[]
  streamerId: string
  streamerName: string
  streamerAvatar: string
  streamerVerified: boolean
  viewersCount: number
  likesCount: number
  sharesCount: number
  maxViewers: number
  duration: number
  quality: 'SD' | 'HD' | 'FHD' | '4K'
  bitrate: number
  fps: number
  allowChat: boolean
  allowGifts: boolean
  isFollowing?: boolean
  startedAt?: string
  endedAt?: string
  createdAt: string
}

export interface LiveMessage {
  id: string
  type: 'text' | 'gift' | 'system' | 'emoji'
  content: string
  userId: string
  userName: string
  userAvatar: string
  userLevel: number
  userVerified: boolean
  gift?: {
    id: string
    name: string
    icon: string
    value: number
    count: number
  }
  timestamp: number
  createdAt: string
}

// 频道相关类型
export interface Channel {
  id: string
  name: string
  description: string
  avatar: string
  banner: string
  category: string
  tags: string[]
  ownerId: string
  ownerName: string
  ownerAvatar: string
  subscribersCount: number
  videosCount: number
  totalViews: number
  isSubscribed?: boolean
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

export interface ChannelVideo {
  id: string
  title: string
  description: string
  cover: string
  url: string
  duration: number
  channelId: string
  viewsCount: number
  likesCount: number
  commentsCount: number
  publishedAt: string
  createdAt: string
}

// 书籍相关类型
export interface Book {
  id: string
  title: string
  description: string
  cover: string
  author: string
  publisher?: string
  isbn?: string
  category: string
  tags: string[]
  language: string
  pages: number
  fileSize: number
  format: 'pdf' | 'epub' | 'mobi' | 'txt'
  downloadUrl?: string
  readUrl?: string
  rating: number
  ratingsCount: number
  downloadsCount: number
  viewsCount: number
  price?: number
  isFree: boolean
  publishedAt: string
  createdAt: string
}

// 空间/个人主页相关类型
export interface UserSpace {
  userId: string
  user: {
    id: string
    username: string
    nickname: string
    avatar: string
    verified: boolean
    level: number
    signature?: string
  }
  stats: {
    followersCount: number
    followingCount: number
    videosCount: number
    articlesCount: number
    likesCount: number
    viewsCount: number
  }
  isFollowing?: boolean
  recentVideos: ChannelVideo[]
  recentPosts: any[]
  achievements: {
    id: string
    name: string
    icon: string
    description: string
    unlockedAt: string
  }[]
}

// 错误页面类型
export interface ErrorPageInfo {
  code: number
  title: string
  message: string
  description?: string
  showBackButton?: boolean
  showHomeButton?: boolean
  customActions?: {
    text: string
    action: () => void
  }[]
}
// 常用类型别名
export type ID = string
export type Timestamp = string
export type URL = string
export type Email = string
export type Phone = string
export type Color = string
export type Size = 'small' | 'medium' | 'large'
export type Status = 'active' | 'inactive' | 'pending' | 'disabled'
export type Theme = 'light' | 'dark' | 'auto'
export type Language = 'zh-CN' | 'zh-TW' | 'en-US' | 'ja-JP' | 'ko-KR'
export type Gender = 'male' | 'female' | 'unknown'
export type SortOrder = 'asc' | 'desc'
export type TimeRange = 'day' | 'week' | 'month' | 'year' | 'all'
export type MediaType = 'image' | 'video' | 'audio'
export type FileFormat = 'jpg' | 'png' | 'gif' | 'webp' | 'mp4' | 'webm' | 'mp3' | 'wav'
export type Quality = 'SD' | 'HD' | 'FHD' | '4K'
export type Visibility = 'public' | 'followers' | 'private'
export type ContentType = 'text' | 'image' | 'video' | 'audio' | 'file'
export type NotificationType = 'info' | 'success' | 'warning' | 'error'
export type UserRole = 'admin' | 'moderator' | 'creator' | 'user' | 'guest'
export type VerificationType = 'personal' | 'enterprise' | 'media' | 'government'
export type DeviceType = 'desktop' | 'tablet' | 'mobile'
export type Platform = 'web' | 'ios' | 'android' | 'windows' | 'macos' | 'linux'