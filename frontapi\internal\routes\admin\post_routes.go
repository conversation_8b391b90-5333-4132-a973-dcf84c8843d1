package admin

import (
	"frontapi/internal/admin/posts"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"

	"github.com/gofiber/fiber/v2"
)

// RegisterPostRoutes 注册帖子管理相关路由
func RegisterPostRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 创建帖子控制器
	postController := posts.NewPostController(services.PostService)

	// 帖子管理路由组
	postRoutes := apiGroup.Group("/posts", middleware.AuthRequired())

	{ // 帖子管理接口
		postRoutes.Post("/list", postController.ListPosts)
		postRoutes.Post("/detail/:id?", postController.GetPost)
		postRoutes.Post("/add", postController.CreatePost)
		postRoutes.Post("/update", postController.UpdatePost)
		postRoutes.Post("/update-status", postController.UpdatePostStatus)
		postRoutes.Post("/delete/:id?", postController.DeletePost)

		// 添加批量操作路由
		postRoutes.Post("/batch-update-status", postController.BatchUpdatePostStatus)
		postRoutes.Post("/batch-delete", postController.BatchDeletePost)

		// 添加审核路由
		// postRoutes.Post("/review", postController.ReviewPost)
		postRoutes.Post("/batch-review", postController.BatchReviewPost)

		// 添加推荐路由
		postRoutes.Post("/toggle-status", postController.TogglePostStatus)
		postRoutes.Post("/batch-toggle-status", postController.BatchTogglePostStatus)
	}

	// 评论管理控制器
	commentController := posts.NewPostCommentController(services.PostCommentService)
	{
		// 评论管理路由组
		commentRoutes := apiGroup.Group("/posts/comments", middleware.AuthRequired())

		// 评论管理接口
		commentRoutes.Post("/list", commentController.ListComments)
		commentRoutes.Post("/detail/:id?", commentController.GetComment)
		commentRoutes.Post("/update-status/:id?", commentController.UpdateCommentStatus)
		commentRoutes.Post("/replies/:id?", commentController.GetCommentReplies)

		// commentRoutes.Post("/batch-update-status", commentController.BatchUpdateCommentStatus)
		commentRoutes.Post("/delete/:id?", commentController.DeleteComment)
		commentRoutes.Post("/batch-update-status", commentController.BatchUpdateCommentStatus)
		commentRoutes.Post("/batch-delete", commentController.BatchDeleteComment)
	}

}
