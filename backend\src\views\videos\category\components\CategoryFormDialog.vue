<template>
  <el-dialog
    :model-value="visible"
    :title="type === 'edit' ? '编辑分类' : '添加分类'"
    width="600px"
    @update:model-value="(val) => emit('update:visible', val)"
    @closed="onClosed"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入分类名称" />
      </el-form-item>
      
      <el-form-item label="分类编码" prop="code">
        <el-input 
          v-model="form.code" 
          placeholder="请输入分类编码"
          :disabled="type === 'edit'"
          :readonly="type === 'edit'"
        >
          <template #append v-if="type === 'add'">
            <el-button @click="generateCode" :icon="RefreshRight" type="primary">
              {{ $t('common.generateRandomCode') }}
            </el-button>
          </template>
        </el-input>
        <div v-if="type === 'edit'" class="form-tip">
          编辑模式下分类编码不可修改
        </div>
      </el-form-item>
      
      <el-form-item label="父分类" prop="parent_id">
        <el-cascader
          v-model="form.parent_id"
          :options="parentCategoryOptions"
          :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children', emitPath: false }"
          clearable
          placeholder="请选择父分类（可留空为顶级分类）"
          style="width: 100%"
          @clear="clearParentCategory"
        >
          <template #default="{ node, data }">
            <span>{{ data.name }}</span>
            <span v-if="data.code" class="category-code">（{{ data.code }}）</span>
          </template>
        </el-cascader>
        <div class="form-tip">
          可以选择父分类，也可以清空设置为顶级分类
        </div>
      </el-form-item>
      
      <el-form-item label="分类图标" prop="icon">
        <url-or-file-input
          v-model="form.icon"
          placeholder="请输入图标URL或选择文件"
          file-type="pictures"
          sub-dir="icons"
          :show-preview="true"
          :show-upload="true"
        />
      </el-form-item>
      
      <el-form-item label="分类封面" prop="cover">
        <url-or-file-input
          v-model="form.cover"
          placeholder="请输入封面URL或选择文件"
          file-type="pictures"
          sub-dir="covers"
          :show-preview="true"
          :show-upload="true"
        />
      </el-form-item>
      
      <el-form-item label="颜色" prop="color">
        <el-color-picker v-model="form.color" show-alpha />
      </el-form-item>
      
      <el-form-item label="排序" prop="sort_order">
        <el-input-number 
          v-model="form.sort_order" 
          :min="0" 
          placeholder="排序权重" 
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="推荐" prop="is_featured">
        <el-switch 
          v-model="form.is_featured" 
          :active-value="1" 
          :inactive-value="0"
          active-text="推荐"
          inactive-text="普通"
        />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入分类描述"
          show-word-limit
          maxlength="500"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="emit('update:visible', false)">取消</el-button>
      <el-button type="primary" @click="onSubmit" :loading="submitting">
        {{ type === 'edit' ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
import { $t } from '@/locales';
import { addVideoCategory, getVideoCategoryDetail, getVideoCategoryList, updateVideoCategory } from '@/service/api/videos/videos';
import type { VideoCategoryItem } from '@/types/videos';
import { handleApiError } from '@/utils/errorHandler';
import { RefreshRight } from '@element-plus/icons-vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { onMounted, reactive, ref, watch } from 'vue';

// Props定义
interface Props {
  visible: boolean;
  type: 'add' | 'edit';
  categoryData: VideoCategoryItem | null;
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'update:visible': [value: boolean];
  'success': [];
}

const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const parentCategoryOptions = ref<VideoCategoryItem[]>([]);
const submitting = ref(false);

// 表单数据
const form = reactive<Partial<VideoCategoryItem>>({
  name: '',
  code: '',
  parent_id: undefined,
  sort_order: 0,
  status: 1,
  is_featured: 0,
  color: '',
  icon: '',
  cover: '',
  description: ''
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { min: 3, max: 50, message: '分类编码长度在 3 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/, message: '编码必须以字母开头，只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  sort_order: [
    { type: 'number', min: 0, message: '排序必须大于等于0', trigger: 'blur' }
  ]
});

// 生成分类编码
const generateCode = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  form.code = `cat_${timestamp}_${random}`;
};

// 清空父分类
const clearParentCategory = () => {
  form.parent_id = undefined;
};

// 获取分类详情
const fetchCategoryDetails = async (id: string) => {
  try {
    const res = await getVideoCategoryDetail(id);
    if (res.data) {
      Object.assign(form, res.data);
      // 确保数字类型字段的正确性
      form.status = Number(res.data.status);
      form.is_featured = Number(res.data.is_featured);
      form.sort_order = Number(res.data.sort_order) || 0;
    }
  } catch (error) {
    handleApiError(error, '获取分类详情失败');
  }
};

// 获取父分类选项
const fetchParentCategories = async () => {
  try {
    const res = await getVideoCategoryList({ 
      page: { pageNo: 1, pageSize: 1000 },
      data: { status: 1 } // 只获取启用的分类
    });
    
    if (res.data && res.data.list) {
      // 如果是编辑模式，过滤掉当前分类及其子分类（防止循环引用）
      let categories = res.data.list;
      if (props.type === 'edit' && props.categoryData) {
        categories = categories.filter((cat: VideoCategoryItem) => 
          cat.id !== props.categoryData!.id && 
          cat.parent_id !== props.categoryData!.id
        );
      }
      parentCategoryOptions.value = buildCategoryTree(categories);
    }
  } catch (error) {
    handleApiError(error, '获取父分类列表失败');
  }
};

// 构建分类树
const buildCategoryTree = (categories: VideoCategoryItem[]): VideoCategoryItem[] => {
  const map: Record<string, VideoCategoryItem & { children: VideoCategoryItem[] }> = {};
  const roots: VideoCategoryItem[] = [];
  
  // 首先创建所有节点的映射
  categories.forEach(cat => {
    map[cat.id] = { ...cat, children: [] };
  });

  // 然后构建父子关系
  categories.forEach(cat => {
    if (cat.parent_id && map[cat.parent_id]) {
      map[cat.parent_id].children.push(map[cat.id]);
    } else {
      roots.push(map[cat.id]);
    }
  });
  
  return roots;
};

// 提交表单
const onSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    // 处理数据格式
    const submitData = { ...form };
    
    // 确保parent_id的正确格式
    if (submitData.parent_id === undefined || submitData.parent_id === '') {
      delete submitData.parent_id;
    }
    
    if (props.type === 'edit') {
      if (!props.categoryData?.id) {
        ElMessage.error('分类ID不存在');
        return;
      }
      await updateVideoCategory({ ...submitData, id: props.categoryData.id });
      ElMessage.success('更新分类成功');
    } else {
      await addVideoCategory(submitData);
      ElMessage.success('添加分类成功');
    }
    
    emit('success');
    emit('update:visible', false);
  } catch (error) {
    if (typeof error === 'string') {
      // 表单验证错误
      return;
    }
    handleApiError(error, `${props.type === 'edit' ? '更新' : '添加'}分类失败`);
  } finally {
    submitting.value = false;
  }
};

// 对话框关闭时重置表单
const onClosed = () => {
  formRef.value?.resetFields();
  submitting.value = false;
  Object.assign(form, {
    name: '',
    code: '',
    parent_id: undefined,
    sort_order: 0,
    status: 1,
    is_featured: 0,
    color: '',
    icon: '',
    cover: '',
    description: ''
  });
};

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  if (val) {
    fetchParentCategories();
    if (props.type === 'edit' && props.categoryData) {
      // 编辑模式：填充现有数据
      fetchCategoryDetails(props.categoryData.id);
    } else if (props.type === 'add') {
      // 创建模式：生成默认编码
      generateCode();
    }
  }
});

// 初始化
onMounted(() => {
  fetchParentCategories();
});
</script>

<style scoped lang="scss">
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.category-code {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

:deep(.el-cascader-panel) {
  .el-cascader-node__label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}

:deep(.el-input-group__append) {
  .el-button {
    margin: 0;
    border-radius: 0;
    
    &:hover {
      z-index: 1;
    }
  }
}

:deep(.el-color-picker) {
  .el-color-picker__trigger {
    width: 40px;
    height: 32px;
  }
}

:deep(.el-switch) {
  .el-switch__label {
    font-size: 12px;
    
    &.is-active {
      color: var(--el-color-primary);
    }
  }
}
</style>