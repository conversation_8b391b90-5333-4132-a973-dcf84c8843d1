import type { App, Directive } from 'vue';

interface DraggableElement extends HTMLElement {
  _dragStartHandler?: EventListener;
  _dragOverHandler?: EventListener;
  _dragLeaveHandler?: EventListener;
  _dropHandler?: EventListener;
  _dragEndHandler?: EventListener;
}

const draggableColumns: Directive = {
  mounted(el: DraggableElement) {
    // Find all table headers
    const headers = el.querySelectorAll('th');
    let draggedColumn: HTMLElement | null = null;
    let fromIndex = -1;

    // Setup drag events for each header
    headers.forEach((header, index) => {
      header.setAttribute('draggable', 'true');
      
      const onDragStart = (e: Event) => {
        const dragEvent = e as DragEvent;
        draggedColumn = header;
        fromIndex = index;
        if (dragEvent.dataTransfer) {
          dragEvent.dataTransfer.effectAllowed = 'move';
        }
        header.classList.add('column-dragging');
      };
      
      const onDragOver = (e: Event) => {
        e.preventDefault();
        header.classList.add('column-over');
      };
      
      const onDragLeave = () => {
        header.classList.remove('column-over');
      };
      
      const onDrop = (e: Event) => {
        e.preventDefault();
        if (draggedColumn && draggedColumn !== header) {
          // Get the index of the current column
          const toIndex = Array.from(headers).indexOf(header);
          
          // Create a custom event with details
          const event = new CustomEvent('column-drop', {
            detail: { fromIndex, toIndex }
          });
          
          // Dispatch the event
          el.dispatchEvent(event);
        }
        
        // Clean up
        headers.forEach(h => {
          h.classList.remove('column-over');
        });
        
        if (draggedColumn) {
          draggedColumn.classList.remove('column-dragging');
        }
        
        draggedColumn = null;
        fromIndex = -1;
      };
      
      const onDragEnd = () => {
        try {
          // Clean up
          headers.forEach(h => {
            h.classList.remove('column-over');
          });
          
          if (draggedColumn) {
            draggedColumn.classList.remove('column-dragging');
          }
          
          draggedColumn = null;
          fromIndex = -1;
        } catch (error) {
          console.error('Error in drag end handler:', error);
        }
      };
      
      // Add event listeners
      header.addEventListener('dragstart', onDragStart);
      header.addEventListener('dragover', onDragOver);
      header.addEventListener('dragleave', onDragLeave);
      header.addEventListener('drop', onDrop);
      header.addEventListener('dragend', onDragEnd);
      
      // Store event handlers for cleanup on a header element
      const headerElement = header as unknown as DraggableElement;
      headerElement._dragStartHandler = onDragStart;
      headerElement._dragOverHandler = onDragOver;
      headerElement._dragLeaveHandler = onDragLeave;
      headerElement._dropHandler = onDrop;
      headerElement._dragEndHandler = onDragEnd;
    });
    
    // Setup for row dragging
    const rows = el.querySelectorAll('tbody tr');
    let draggedRow: HTMLElement | null = null;
    let rowFromIndex = -1;
    
    rows.forEach((row, index) => {
      row.setAttribute('draggable', 'true');
      
      const onRowDragStart = (e: Event) => {
        const dragEvent = e as DragEvent;
        draggedRow = row as HTMLElement;
        rowFromIndex = index;
        if (dragEvent.dataTransfer) {
          dragEvent.dataTransfer.effectAllowed = 'move';
        }
        row.classList.add('row-dragging');
      };
      
      const onRowDragOver = (e: Event) => {
        e.preventDefault();
        row.classList.add('row-over');
      };
      
      const onRowDragLeave = () => {
        row.classList.remove('row-over');
      };
      
      const onRowDrop = (e: Event) => {
        e.preventDefault();
        if (draggedRow && draggedRow !== row) {
          // Get the index of the current row
          const rowToIndex = Array.from(rows).indexOf(row);
          
          // Dispatch custom event
          el.dispatchEvent(new CustomEvent('row-drop', {
            detail: { oldIndex: rowFromIndex, newIndex: rowToIndex }
          }));
        }
        
        // Clean up
        rows.forEach(r => {
          r.classList.remove('row-over');
        });
        
        if (draggedRow) {
          draggedRow.classList.remove('row-dragging');
        }
        
        draggedRow = null;
        rowFromIndex = -1;
      };
      
      const onRowDragEnd = () => {
        try {
          // Clean up
          rows.forEach(r => {
            r.classList.remove('row-over');
          });
          
          if (draggedRow) {
            draggedRow.classList.remove('row-dragging');
          }
          
          draggedRow = null;
          rowFromIndex = -1;
        } catch (error) {
          console.error('Error in row drag end handler:', error);
        }
      };
      
      // Add event listeners
      row.addEventListener('dragstart', onRowDragStart);
      row.addEventListener('dragover', onRowDragOver);
      row.addEventListener('dragleave', onRowDragLeave);
      row.addEventListener('drop', onRowDrop);
      row.addEventListener('dragend', onRowDragEnd);
      
      // Store event handlers for cleanup on a row element
      const rowElement = row as unknown as DraggableElement;
      rowElement._dragStartHandler = onRowDragStart;
      rowElement._dragOverHandler = onRowDragOver;
      rowElement._dragLeaveHandler = onRowDragLeave;
      rowElement._dropHandler = onRowDrop;
      rowElement._dragEndHandler = onRowDragEnd;
    });
  },
  
  unmounted(el: DraggableElement) {
    // Cleanup column drag listeners
    const headers = el.querySelectorAll('th');
    headers.forEach((header) => {
      const headerElement = header as unknown as DraggableElement;
      if (headerElement._dragStartHandler) {
        header.removeEventListener('dragstart', headerElement._dragStartHandler);
      }
      if (headerElement._dragOverHandler) {
        header.removeEventListener('dragover', headerElement._dragOverHandler);
      }
      if (headerElement._dragLeaveHandler) {
        header.removeEventListener('dragleave', headerElement._dragLeaveHandler);
      }
      if (headerElement._dropHandler) {
        header.removeEventListener('drop', headerElement._dropHandler);
      }
      if (headerElement._dragEndHandler) {
        header.removeEventListener('dragend', headerElement._dragEndHandler);
      }
    });
    
    // Cleanup row drag listeners
    const rows = el.querySelectorAll('tbody tr');
    rows.forEach((row) => {
      const rowElement = row as unknown as DraggableElement;
      if (rowElement._dragStartHandler) {
        row.removeEventListener('dragstart', rowElement._dragStartHandler);
      }
      if (rowElement._dragOverHandler) {
        row.removeEventListener('dragover', rowElement._dragOverHandler);
      }
      if (rowElement._dragLeaveHandler) {
        row.removeEventListener('dragleave', rowElement._dragLeaveHandler);
      }
      if (rowElement._dropHandler) {
        row.removeEventListener('drop', rowElement._dropHandler);
      }
      if (rowElement._dragEndHandler) {
        row.removeEventListener('dragend', rowElement._dragEndHandler);
      }
    });
  }
};

// Export the directive
export default {
  install(app: App) {
    app.directive('draggable-columns', draggableColumns);
  }
}; 