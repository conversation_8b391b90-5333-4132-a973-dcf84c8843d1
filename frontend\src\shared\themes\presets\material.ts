/**
 * Material主题预设
 */
import { THEME_COLORS } from '@/config/theme.config';

// PrimeVue Pass-through (PT) 属性
const materialPreset = {
  // 全局样式
  global: {
    css: `
      :root {
        /* 自定义变量 */
        --header-bg: ${THEME_COLORS.indigo.header};
        --header-text: ${THEME_COLORS.indigo.headerText};
        --footer-bg: ${THEME_COLORS.indigo.footer};
        --footer-text: ${THEME_COLORS.indigo.footerText};
        
        /* PrimeVue Panel组件变量 */
        --p-panel-header-bg: var(--surface-0);
        --p-panel-header-text: var(--surface-900);
        --p-panel-content-bg: var(--surface-0);
        --p-panel-content-text: var(--surface-700);
        --p-panel-border-color: transparent;
        --p-panel-border-radius: 0.375rem;
        --p-panel-background: var(--surface-card);
        --p-panel-color: var(--text-color);
      }
      
      .dark {
        --p-panel-header-bg: var(--surface-900);
        --p-panel-header-text: #ffffff;
        --p-panel-content-bg: var(--surface-900);
        --p-panel-content-text: rgba(255, 255, 255, 0.8);
        --p-panel-border-color: transparent;
      }
    `
  },
  
  // Panel组件样式
  panel: {
    header: {
      class: [
        // 基础样式
        'flex items-center justify-between',
        'border-0',
        'bg-surface-0 text-surface-900',
        'py-4 px-6',
        
        // 状态样式
        'dark:bg-surface-900 dark:text-white'
      ]
    },
    content: {
      class: [
        // 基础样式
        'p-6',
        'border-0',
        'bg-surface-0 text-surface-700',
        'shadow-md rounded-md',
        
        // 状态样式
        'dark:bg-surface-900 dark:text-white/80'
      ]
    },
    footer: {
      class: [
        // 基础样式
        'p-6',
        'border-0',
        'bg-surface-0 text-surface-700',
        
        // 状态样式
        'dark:bg-surface-900 dark:text-white/80'
      ]
    },
    transition: {
      enterFromClass: 'opacity-0 scale-75',
      enterActiveClass: 'transition-all duration-150 ease-in-out',
      leaveActiveClass: 'transition-all duration-150 ease-in',
      leaveToClass: 'opacity-0 scale-75'
    }
  },
  
  // Button组件样式
  button: {
    root: ({ props }) => ({
      class: [
        // 基础样式
        'relative',
        'items-center inline-flex text-center align-bottom justify-center',
        'rounded-full',
        'text-sm',
        'transition-shadow duration-200',
        'cursor-pointer overflow-hidden select-none',
        'shadow-sm',
        
        // 尺寸
        {
          'px-3 py-1.5 min-w-[2rem]': props.size === 'small',
          'px-4 py-2 min-w-[2.5rem]': props.size === null || props.size === 'medium',
          'px-5 py-2.5 min-w-[3rem]': props.size === 'large'
        },
        
        // 图标按钮
        { 'p-0 w-10 h-10': props.label == null && props.icon !== null },
        
        // 主题样式
        {
          'text-white bg-primary-500 hover:bg-primary-600 hover:shadow-md':
            !props.link && props.severity === null && !props.text && !props.outlined && !props.plain,
          'text-primary-500 hover:text-primary-600 hover:bg-primary-300/20': props.link
        },
        
        // 禁用状态
        {
          'opacity-60 pointer-events-none cursor-default': props.disabled
        }
      ]
    })
  },
  
  // 其他组件样式...
};

export default materialPreset; 