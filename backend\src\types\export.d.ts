declare module '@/utils/export' {
    /**
     * 导出数据到Excel
     * @param headers 表头配置 [{ header: '显示名称', key: '字段名' }]
     * @param data 数据数组
     * @param filename 文件名（不含扩展名）
     */
    export function exportToExcel(
        headers: { header: string; key: string }[],
        data: Record<string, any>[],
        filename?: string
    ): void;

    /**
     * 导出数据到CSV
     * @param headers 表头配置 [{ header: '显示名称', key: '字段名' }]
     * @param data 数据数组
     * @param filename 文件名（不含扩展名）
     */
    export function exportToCSV(
        headers: { header: string; key: string }[],
        data: Record<string, any>[],
        filename?: string
    ): void;
} 