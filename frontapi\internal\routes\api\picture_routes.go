package api

import (
	"frontapi/internal/api/pictures"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterPictureRoutes 注册图片相关路由
func RegisterPictureRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 图片分类相关路由组
	categories := apiGroup.Group("/pictures/categories")
	{
		// 创建图片API控制器
		pictureCategoryController := pictures.NewPictureCategoryController(
			services.PictureCategoryService,
			services.PictureService,
			services.PictureAlbumService,
		)
		categories.Post("/getCategoryList", pictureCategoryController.GetCategoryList)

	}

	// 图片专辑相关路由组
	albums := apiGroup.Group("/pictures/albums")
	{
		pictureAlbumController := pictures.NewPictureAlbumController(
			services.PictureAlbumService,
			services.PictureService,
		)
		albums.Post("/getAlbumList", pictureAlbumController.GetAlbumList)
		albums.Post("/getAlbumDetail", pictureAlbumController.GetAlbumDetail)
		albums.Post("/getAlbumPictures", pictureAlbumController.GetAlbumPictures)
		albums.Post("/getRecommendedAlbums", pictureAlbumController.GetRecommendedAlbums)
	}
	// 图片相关路由组
	picturesApi := apiGroup.Group("/pictures/images")
	{
		// 创建图片API控制器
		pictureController := pictures.NewPictureController(
			services.PictureService,
			services.PictureCategoryService,
			services.PictureAlbumService,
			services.PictureCollectionService,
		)
		picturesApi.Post("/getPictureList", pictureController.GetPictureList)
	}

	// 图片通用路由组
	picturesGeneral := apiGroup.Group("/pictures")
	{
		pictureController := pictures.NewPictureController(
			services.PictureService,
			services.PictureCategoryService,
			services.PictureAlbumService,
			services.PictureCollectionService,
		)
		picturesGeneral.Post("/getPictureDetail", pictureController.GetPictureDetail)
		picturesGeneral.Post("/getRecommendedAlbums", pictureController.GetRecommendedAlbums)
	}
}
