package promotion

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

type PromotionCampaign struct {
	models.BaseModel
	Name         string         `json:"name" gorm:"type:string;size:100;not null;comment:活动名称"`
	Code         string         `json:"code" gorm:"type:string;size:50;not null;uniqueIndex;comment:活动代码"`
	Description  string         `json:"description" gorm:"type:string;comment:活动描述"`
	StartTime    types.JSONTime `json:"start_time" gorm:"not null;comment:开始时间"`
	EndTime      types.JSONTime `json:"end_time" gorm:"not null;comment:结束时间"`
	RewardType   string         `json:"reward_type" gorm:"type:string;size:20;not null;comment:奖励类型:points-积分,cash-现金,coin-平台币,discount-折扣"`
	RewardValue  float64        `json:"reward_value" gorm:"type:decimal(10,2);not null;comment:奖励值"`
	LimitPerUser *int           `json:"limit_per_user" gorm:"comment:每用户限制次数"`
	TotalLimit   *int           `json:"total_limit" gorm:"comment:总限制次数"`
	CurrentCount int            `json:"current_count" gorm:"not null;default:0;comment:当前使用次数"`
	Conditions   string         `json:"conditions" gorm:"type:string;comment:参与条件"`
	BannerImage  string         `json:"banner_image" gorm:"type:string;size:255;comment:横幅图片"`
	Status       string         `json:"status" gorm:"type:string;size:20;not null;default:'active';comment:状态:pending-未开始,active-进行中,ended-已结束,canceled-已取消"`
}

func (PromotionCampaign) TableName() string {
	return "ly_promotion_campaigns"
}
