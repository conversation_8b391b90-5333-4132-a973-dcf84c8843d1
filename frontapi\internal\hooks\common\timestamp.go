package common

import (
	"context"
	"reflect"
	"time"

	"frontapi/config/constant"
)

// TimestampHook 时间戳钩子
type TimestampHook struct {
	CreateTimeField string // 创建时间字段名，默认为 "CreatedAt"
	UpdateTimeField string // 更新时间字段名，默认为 "UpdatedAt"
}

// HookContext 钩子上下文（用于获取钩子类型）
type HookContext struct {
	HookType   constant.HookType
	EntityType string
	Operation  string
	Metadata   map[string]interface{}
}

// Execute 执行时间戳设置
func (h *TimestampHook) Execute(ctx context.Context, data interface{}) error {
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return nil // 不是结构体，跳过
	}

	now := time.Now()

	// 获取钩子上下文
	hookCtx, ok := ctx.Value("hookContext").(*HookContext)
	if !ok {
		return nil
	}

	// 设置创建时间
	if hookCtx.HookType == constant.BeforeCreate {
		createField := h.CreateTimeField
		if createField == "" {
			createField = "CreatedAt"
		}
		if fieldValue := v.FieldByName(createField); fieldValue.IsValid() && fieldValue.CanSet() {
			fieldValue.Set(reflect.ValueOf(now))
		}
	}

	// 设置更新时间
	if hookCtx.HookType == constant.BeforeCreate || hookCtx.HookType == constant.BeforeUpdate {
		updateField := h.UpdateTimeField
		if updateField == "" {
			updateField = "UpdatedAt"
		}
		if fieldValue := v.FieldByName(updateField); fieldValue.IsValid() && fieldValue.CanSet() {
			fieldValue.Set(reflect.ValueOf(now))
		}
	}

	return nil
}

// NewTimestampHook 创建时间戳钩子
func NewTimestampHook() *TimestampHook {
	return &TimestampHook{
		CreateTimeField: "CreatedAt",
		UpdateTimeField: "UpdatedAt",
	}
}

// WithCreateTimeField 设置创建时间字段名
func (h *TimestampHook) WithCreateTimeField(field string) *TimestampHook {
	h.CreateTimeField = field
	return h
}

// WithUpdateTimeField 设置更新时间字段名
func (h *TimestampHook) WithUpdateTimeField(field string) *TimestampHook {
	h.UpdateTimeField = field
	return h
}
