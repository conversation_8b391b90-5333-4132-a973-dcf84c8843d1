// 漫画章节类型
export interface ComicChapter {
    id: number | string
    title: string
    readCount: number
    chapterNumber: number
    wordCount?: number
    isLocked: boolean
    publishTime?: string
    order?: number
}

// 漫画详情类型
export interface ComicDetail {
    id: number | string
    title: string
    cover: string
    author: string
    categoryId: number | string
    categoryName: string
    status: 'ongoing' | 'completed'
    description: string
    tags: string[]
    readCount: number
    favoriteCount: number
    likeCount: number
    rating: number
    updateTime: string
    publishTime?: string
    chapters: ComicChapter[]
}

// 相关推荐漫画类型
export interface RelatedComic {
    id: number | string
    title: string
    cover: string
    author: string
    rating: number
    categoryName?: string
}

// 漫画列表项类型
export interface ComicListItem {
    id: number | string
    title: string
    cover: string
    author: string
    categoryName: string
    status: 'ongoing' | 'completed'
    updateTime: string
    readCount: number
    favoriteCount?: number
    rating?: number
    description?: string
}

// 漫画页面类型
export interface ComicPage {
    id: number | string
    comicId: number | string
    chapterId: number | string
    url: string
    order: number
}

// 漫画分类类型
export interface ComicCategory {
    id: number | string
    name: string
    description?: string
    cover?: string
    comicCount?: number
} 