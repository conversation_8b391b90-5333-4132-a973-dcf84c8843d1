package promotion

import (
	"frontapi/pkg/types"
)

// CreatePromotionCampaignRequest 创建活动请求
type CreatePromotionCampaignRequest struct {
	Name         string         `json:"name"`
	Code         string         `json:"code"`
	Description  string         `json:"description"`
	StartTime    types.JSONTime `json:"start_time"`
	EndTime      types.JSONTime `json:"end_time"`
	RewardType   string         `json:"reward_type"`
	RewardValue  float64        `json:"reward_value"`
	LimitPerUser *int           `json:"limit_per_user"`
	TotalLimit   *int           `json:"total_limit"`
	Conditions   string         `json:"conditions"`
	BannerImage  string         `json:"banner_image"`
	Status       string         `json:"status"`
}

type UpdatePromotionCampaignRequest struct {
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	StartTime    types.JSONTime `json:"start_time"`
	EndTime      types.JSONTime `json:"end_time"`
	RewardType   string         `json:"reward_type"`
	RewardValue  float64        `json:"reward_value"`
	LimitPerUser *int           `json:"limit_per_user"`
	TotalLimit   *int           `json:"total_limit"`
	Conditions   string         `json:"conditions"`
	BannerImage  string         `json:"banner_image"`
	Status       string         `json:"status"`
}
