# 多媒体内容管理平台开发规则

## 🎯 核心开发原则

你是一个专业的Go语言开发助手，专门为多媒体内容管理平台项目提供代码支持。

### 项目特点
- **技术栈**: Go + Fiber + MySQL + Redis + MongoDB
- **架构**: 分层架构 + 依赖注入 + 钩子系统
- **双端服务**: 前台API(8080) + 管理后台(8081)
- **多媒体支持**: 视频、小说、图片、短视频管理

## 📝 代码规范

### 1. 语言规范
```go
// ✅ 错误消息使用英文
return errors.New("user not found")

// ✅ 代码注释使用中文
// 查找用户信息
func (s *UserService) GetUser(id uint) (*User, error) {
    // 从缓存获取用户信息
    if user := s.getFromCache(id); user != nil {
        return user, nil
    }
    // 从数据库查询
    return s.repository.FindByID(id)
}

// ✅ 长英文消息添加中文注释
return fmt.Errorf("authentication failed: %w", err) // 认证失败
```

### 2. 命名规范
```go
// ✅ 结构体和接口
type UserService struct {}        // 服务结构体
type UserServiceInterface interface {} // 服务接口

// ✅ 方法命名
func (s *UserService) CreateUser()  // 动词开头
func (s *UserService) GetUserList() // Get/Set/Create/Update/Delete

// ✅ 变量命名
var userCount int                   // 驼峰命名
const MAX_RETRY_COUNT = 3          // 常量大写下划线
```

### 3. 项目结构
```
frontapi/
├── cmd/                    # 应用入口
│   ├── admin/             # 管理后台服务
│   └── api/               # 前台API服务
├── internal/              # 内部业务逻辑
│   ├── handlers/          # HTTP处理器 (Controller层)
│   ├── service/           # 业务逻辑 (Service层)
│   ├── repository/        # 数据访问 (Repository层)
│   ├── models/            # 数据模型
│   ├── middleware/        # 中间件
│   ├── routes/            # 路由配置
│   ├── container/         # 依赖注入容器
│   └── hooks/             # 钩子系统
├── pkg/                   # 公共包
│   ├── database/          # 数据库连接
│   ├── redis/             # Redis连接
│   ├── auth/              # 认证相关
│   └── utils/             # 工具函数
└── config/                # 配置管理
```

## 🏗️ 架构规范

### 1. 分层架构
```go
// Controller层 - 只处理HTTP请求响应
func (c *UserController) CreateUser(ctx *fiber.Ctx) error {
    var req CreateUserRequest
    if err := ctx.BodyParser(&req); err != nil {
        return response.Error(ctx, codes.CodeBadRequest, "invalid request body") // 请求体格式错误
    }
    
    user, err := c.userService.CreateUser(&req) // 调用服务层
    if err != nil {
        return response.Error(ctx, codes.CodeInternalError, err.Error())
    }
    
    return response.Success(ctx, user)
}

// Service层 - 处理业务逻辑
func (s *UserService) CreateUser(req *CreateUserRequest) (*User, error) {
    // 1. 数据验证
    if err := s.validator.Validate(req); err != nil {
        return nil, fmt.Errorf("validation failed: %w", err) // 验证失败
    }
    
    // 2. 业务逻辑处理
    user := &User{
        Username: req.Username,
        Email:    req.Email,
    }
    
    // 3. 调用仓库层
    return s.repository.Create(user)
}

// Repository层 - 数据访问
func (r *UserRepository) Create(user *User) (*User, error) {
    if err := r.db.Create(user).Error; err != nil {
        return nil, fmt.Errorf("failed to create user: %w", err) // 创建用户失败
    }
    return user, nil
}
```

### 2. 依赖注入
```go
// ✅ 使用接口依赖
type UserService struct {
    repository UserRepositoryInterface // 依赖接口
    validator  ValidatorInterface
    redis      RedisInterface
}

// ✅ 构造函数注入
func NewUserService(
    repo UserRepositoryInterface,
    validator ValidatorInterface,
    redis RedisInterface,
) *UserService {
    return &UserService{
        repository: repo,
        validator:  validator,
        redis:     redis,
    }
}

// ❌ 避免全局变量
var globalDB *gorm.DB // 不要这样做
```

## 📊 数据模型规范

### 1. 基础模型
```go
type BaseModel struct {
    ID        uint           `json:"id" gorm:"primarykey"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}
```

### 2. 模型定义
```go
type User struct {
    BaseModel
    Username string  `json:"username" gorm:"uniqueIndex;size:50;not null" validate:"required,min=3,max=50"` // 用户名
    Email    string  `json:"email" gorm:"uniqueIndex;size:100;not null" validate:"required,email"`        // 邮箱
    Password string  `json:"-" gorm:"size:255;not null" validate:"required,min=6"`                       // 密码（不返回）
    Status   int     `json:"status" gorm:"default:1;comment:状态 1:正常 2:禁用"`                            // 状态
}

// 表名定义
func (User) TableName() string {
    return "users"
}
```

## 🌐 API规范

### 1. 响应格式
```go
// 成功响应
{
    "code": 2000,
    "message": "success",
    "data": {...},
    "timestamp": "2024-01-01T12:00:00Z"
}

// 分页响应
{
    "code": 2000,
    "message": "success",
    "data": {
        "items": [...],
        "pagination": {
            "page": 1,
            "page_size": 20,
            "total": 100,
            "pages": 5
        }
    },
    "timestamp": "2024-01-01T12:00:00Z"
}

// 错误响应
{
    "code": 4004,
    "message": "user not found",
    "data": null,
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. 状态码规范
```go
const (
    // 成功 2xxx
    CodeSuccess = 2000
    
    // 客户端错误 4xxx
    CodeBadRequest     = 4000 // 请求参数错误
    CodeUnauthorized   = 4001 // 未授权
    CodeForbidden      = 4003 // 禁止访问
    CodeNotFound       = 4004 // 资源不存在
    CodeConflict       = 4009 // 资源冲突
    
    // 业务错误 4xxx
    CodeUserNotFound      = 4104 // 用户不存在
    CodeUserAlreadyExists = 4109 // 用户已存在
    CodeInvalidPassword   = 4101 // 密码错误
    
    // 服务器错误 5xxx
    CodeInternalError = 5000 // 服务器内部错误
    CodeDatabaseError = 5001 // 数据库错误
)
```

### 3. 路由设计
```go
// RESTful API设计
GET    /api/v1/users          // 获取用户列表
POST   /api/v1/users          // 创建用户
GET    /api/v1/users/:id      // 获取用户详情
PUT    /api/v1/users/:id      // 更新用户
DELETE /api/v1/users/:id      // 删除用户

// 认证相关
POST   /api/v1/auth/login     // 用户登录
POST   /api/v1/auth/register  // 用户注册
POST   /api/v1/auth/logout    // 用户登出
```

## 🔐 安全规范

### 1. 密码处理
```go
// ✅ 密码加密
func hashPassword(password string) (string, error) {
    bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    if err != nil {
        return "", fmt.Errorf("failed to hash password: %w", err) // 密码加密失败
    }
    return string(bytes), nil
}

// ✅ 密码验证
func checkPassword(password, hash string) bool {
    err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
    return err == nil
}
```

### 2. JWT处理
```go
// ✅ Token生成
func GenerateToken(userID uint) (string, error) {
    claims := jwt.MapClaims{
        "user_id": userID,
        "exp":     time.Now().Add(time.Hour * 24).Unix(),
        "iat":     time.Now().Unix(),
    }
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(jwtSecret))
}
```

## 🧪 测试规范

### 1. 测试文件命名
```
user_service.go      -> user_service_test.go
user_handler.go      -> user_handler_test.go
user_repository.go   -> user_repository_test.go
```

### 2. 测试结构
```go
func TestUserService_CreateUser(t *testing.T) {
    // 准备测试环境
    mockRepo := &MockUserRepository{}
    service := NewUserService(mockRepo, nil, nil)
    
    // 测试用例
    tests := []struct {
        name    string
        request *CreateUserRequest
        want    *User
        wantErr bool
    }{
        {
            name: "successful creation", // 成功创建
            request: &CreateUserRequest{
                Username: "testuser",
                Email:    "<EMAIL>",
            },
            want: &User{
                Username: "testuser",
                Email:    "<EMAIL>",
            },
            wantErr: false,
        },
    }
    
    // 执行测试
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := service.CreateUser(tt.request)
            if tt.wantErr {
                assert.Error(t, err)
                return
            }
            assert.NoError(t, err)
            assert.Equal(t, tt.want.Username, got.Username)
        })
    }
}
```

## 🔧 错误处理

### 1. 错误包装
```go
// ✅ 使用fmt.Errorf包装错误
if err != nil {
    return fmt.Errorf("failed to create user: %w", err) // 创建用户失败
}

// ✅ 检查特定错误类型
if errors.Is(err, gorm.ErrRecordNotFound) {
    return nil, fmt.Errorf("user not found: %w", err) // 用户不存在
}
```

### 2. 自定义错误
```go
type BusinessError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
}

func (e *BusinessError) Error() string {
    return e.Message
}

var (
    ErrUserNotFound = &BusinessError{Code: 4104, Message: "user not found"}
    ErrInvalidPassword = &BusinessError{Code: 4101, Message: "invalid password"}
)
```

## 📚 开发工作流

### 1. 功能开发步骤
1. 创建数据模型 (`internal/models/`)
2. 实现仓库层 (`internal/repository/`)
3. 实现服务层 (`internal/service/`)
4. 实现控制器 (`internal/handlers/`)
5. 配置路由 (`internal/routes/`)
6. 更新容器 (`internal/container/`)
7. 编写测试

### 2. Git提交规范
```bash
feat(user): add user registration API      # 新功能
fix(auth): fix JWT token validation        # 修复bug
docs(api): update API documentation        # 文档更新
refactor(service): optimize user service   # 重构
test(user): add user service tests         # 测试
```

## 🚀 性能优化

### 1. 数据库优化
```go
// ✅ 使用预加载避免N+1问题
db.Preload("Posts").Find(&users)

// ✅ 使用索引字段查询
db.Where("email = ?", email).First(&user)

// ✅ 分页查询
db.Offset((page-1)*pageSize).Limit(pageSize).Find(&users)
```

### 2. 缓存使用
```go
// ✅ Redis缓存模式
func (s *UserService) GetUser(id uint) (*User, error) {
    // 先查缓存
    cacheKey := fmt.Sprintf("user:%d", id)
    if cached := s.redis.Get(cacheKey); cached != "" {
        var user User
        json.Unmarshal([]byte(cached), &user)
        return &user, nil
    }
    
    // 查数据库
    user, err := s.repository.FindByID(id)
    if err != nil {
        return nil, err
    }
    
    // 写入缓存
    data, _ := json.Marshal(user)
    s.redis.Set(cacheKey, string(data), time.Hour)
    
    return user, nil
}
```

---

**重要提醒**:
1. 错误消息用英文，注释用中文
2. 严格遵循分层架构
3. 使用依赖注入而非全局变量
4. 所有公共函数必须有注释
5. 编写对应的单元测试
6. 遵循RESTful API设计
7. 注意安全性和性能优化
