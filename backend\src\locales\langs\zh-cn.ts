
const local: App.I18n.Schema = {
    system: {
        title: 'Soybean 管理系统',
        updateTitle: '系统版本更新通知',
        updateContent: '检测到系统有新版本发布，是否立即刷新页面？',
        updateConfirm: '立即刷新',
        updateCancel: '稍后再说'
    },
    buttons: {
        add: '新增',
        delete: '删除',
        edit: '编辑',
        detail: '详情',
        save: '保存',
        reset: '重置',
        cancel: '取消',
        search: '搜索',
        refresh: '刷新',
        close: '关闭',
        more: '更多',
        submit: '提交',
        confirm: "确认"
    },
    common: {
        action: '操作',
        add: '新增',
        addSuccess: '添加成功',
        backToHome: '返回首页',
        batchDelete: '批量删除',
        cancel: '取消',
        close: '关闭',
        check: '勾选',
        expandColumn: '展开列',
        columnSetting: '列设置',
        config: '配置',
        confirm: '确认',
        delete: '删除',
        deleteSuccess: '删除成功',
        confirmDelete: '确认删除吗？',
        edit: '编辑',
        warning: '警告',
        error: '错误',
        index: '序号',
        keywordSearch: '请输入关键词搜索',
        logout: '退出登录',
        logoutConfirm: '确认退出登录吗？',
        lookForward: '敬请期待',
        modify: '修改',
        modifySuccess: '修改成功',
        noData: '无数据',
        operate: '操作',
        pleaseCheckValue: '请检查输入的值是否合法',
        refresh: '刷新',
        reset: '重置',
        search: '搜索',
        switch: '切换',
        tip: '提示',
        trigger: '触发',
        update: '更新',
        updateSuccess: '更新成功',
        userCenter: '个人中心',
        yesOrNo: {
            yes: '是',
            no: '否'
        },
        status: {
            label: '状态',
            all: '全部',
            pending: '待审核',
            published: '已发布',
            unpublished: '已下架',
            rejected: '已拒绝',
            deleted: '已删除'
        }
    },
    request: {
        logout: '请求失败后登出用户',
        logoutMsg: '用户状态失效，请重新登录',
        logoutWithModal: '请求失败后弹出模态框再登出用户',
        logoutWithModalMsg: '用户状态失效，请重新登录',
        refreshToken: '请求的token已过期，刷新token',
        tokenExpired: 'token已过期'
    },
    theme: {
        themeSchema: {
            title: '主题模式',
            light: '亮色模式',
            dark: '暗黑模式',
            auto: '跟随系统'
        },
        grayscale: '灰色模式',
        colourWeakness: '色弱模式',
        layoutMode: {
            title: '布局模式',
            vertical: '左侧菜单模式',
            'vertical-mix': '左侧菜单混合模式',
            horizontal: '顶部菜单模式',
            'horizontal-mix': '顶部菜单混合模式',
            reverseHorizontalMix: '一级菜单与子级菜单位置反转'
        },
        recommendColor: '应用推荐算法的颜色',
        recommendColorDesc: '推荐颜色的算法参照',
        themeColor: {
            title: '主题颜色',
            primary: '主色',
            info: '信息色',
            success: '成功色',
            warning: '警告色',
            error: '错误色',
            followPrimary: '跟随主色'
        },
        scrollMode: {
            title: '滚动模式',
            wrapper: '外层滚动',
            content: '主体滚动'
        },
        page: {
            animate: '页面切换动画',
            mode: {
                title: '页面切换动画类型',
                'fade-slide': '滑动',
                fade: '淡入淡出',
                'fade-bottom': '底部消退',
                'fade-scale': '缩放消退',
                'zoom-fade': '渐变',
                'zoom-out': '闪现',
                none: '无'
            }
        },
        fixedHeaderAndTab: '固定头部和标签栏',
        header: {
            height: '头部高度',
            breadcrumb: {
                visible: '显示面包屑',
                showIcon: '显示面包屑图标'
            },
            multilingual: {
                visible: '显示多语言按钮'
            }
        },
        tab: {
            visible: '显示标签栏',
            cache: '标签栏信息缓存',
            height: '标签栏高度',
            mode: {
                title: '标签栏风格',
                chrome: '谷歌风格',
                button: '按钮风格'
            }
        },
        sider: {
            inverted: '深色侧边栏',
            width: '侧边栏宽度',
            collapsedWidth: '侧边栏折叠宽度',
            mixWidth: '混合布局侧边栏宽度',
            mixCollapsedWidth: '混合布局侧边栏折叠宽度',
            mixChildMenuWidth: '混合布局子菜单宽度'
        },
        footer: {
            visible: '显示底部',
            fixed: '固定底部',
            height: '底部高度',
            right: '底部局右'
        },
        watermark: {
            visible: '显示全屏水印',
            text: '水印文本'
        },
        themeDrawerTitle: '主题配置',
        pageFunTitle: '页面功能',
        resetCacheStrategy: {
            title: '重置缓存策略',
            close: '关闭页面',
            refresh: '刷新页面'
        },
        configOperation: {
            copyConfig: '复制配置',
            copySuccessMsg: '复制成功，请替换 src/theme/settings.ts 中的变量 themeSettings',
            resetConfig: '重置配置',
            resetSuccessMsg: '重置成功'
        }
    },
    route: {
        system_mock: '数据库Mock生成器',
        system_tags: '标签管理',
        system_menus: '菜单管理',
        videos: {
            title: '视频管理',
            channel: {
                title: '频道管理',
                name: '频道名称',
                uri: 'URI',
                icon: '频道图标',
                cover: '频道封面',
                description: '描述',
                updateFrequency: '更新频率',
                sort: '排序',
                status: '状态',
                createTime: '创建时间',
                actions: '操作',
                addChannel: '添加频道',
                editChannel: '编辑频道',
                iconTip: '图标尺寸: 100x100 像素, 小于 2MB',
                coverTip: '建议尺寸: 1200x300 像素, 小于 5MB',
                pleaseEnterName: '请输入频道名称',
                pleaseEnterUri: '请输入URI, 如movie-channel',
                pleaseEnterDescription: '请输入频道描述',
                pleaseSelectFrequency: '请选择更新频率',
                sortTip: '数字越小排序越靠前',
                normal: '正常',
                disabled: '禁用',
                noImage: '无图片',
                confirmDelete: '确定要删除此频道吗？',
                uploading: '上传中...',
                uploadSuccess: '上传成功',
                uploadFailed: '上传失败'
            },


            comment: {
                title: '评论管理',
                videoId: '视频ID',
                userId: '用户ID',
                content: '评论内容',
                status: '状态',
                replyCount: '回复数',
                likeCount: '点赞数',
                commentTime: '评论时间',
                actions: '操作',
                detail: '详情',
                normal: '正常',
                blocked: '屏蔽',
                unknown: '未知',
                pleaseEnterVideoId: '请输入视频ID',
                pleaseEnterUserId: '请输入用户ID',
                pleaseEnterContent: '请输入评论内容',
                pleaseSelectStatus: '请选择状态',
                confirmDelete: '确定删除此评论吗？',
                replyList: '评论回复列表',
                parentComment: '父评论',
                replies: '回复列表',
                noReplies: '暂无回复',
                replyTo: '回复',
                confirmDeleteReply: '确定删除此回复吗？'
            },
            category: {
                title: '分类管理',
                name: '分类名称',
                code: '分类编码',
                icon: '图标',
                cover: '封面图',
                color: '颜色',
                description: '描述',
                sort: '排序',
                status: '状态',
                featured: '是否推荐',
                createTime: '创建时间',
                actions: '操作',
                addCategory: '添加分类',
                editCategory: '编辑分类',
                addSubCategory: '添加子分类',
                parentCategory: '父级分类',
                uri: 'URI标识',
                uploadIcon: '上传图标',
                uploadCover: '上传封面图',
                normal: '正常',
                disabled: '禁用',
                yes: '是',
                no: '否',
                confirmDelete: '确定删除此分类吗？',
                pleaseEnterName: '请输入分类名称',
                pleaseEnterCode: '请输入分类编码',
                pleaseEnterDescription: '请输入分类描述',
                pleaseEnterUri: '请输入URI标识',
                selectParentCategory: '请选择父级分类',
                iconFormatError: '图标必须是图片格式!',
                iconSizeError: '图标大小不能超过 2MB!',
                coverFormatError: '封面必须是图片格式!',
                coverSizeError: '封面大小不能超过 5MB!',
                generateRandomCode: '随机生成'
            },

        },
        videos_manage: '视频管理',
        system: '系统管理', // 系统相关路由国际化占位，需根据实际情况补充
        system_database: '数据库Mock生成器',
        books: '电子书管理', // 书籍相关路由国际化占位，需根据实际情况补充
        books_category: '电子书分类', // 书籍分类相关路由国际化占位，需根据实际情况补充
        books_chapter: '电子书章节', // 书籍章节相关路由国际化占位，需根据实际情况补充
        books_list: '电子书列表', // 书籍列表相关路由国际化占位，需根据实际情况补充
        comics: '漫画管理', // 漫画相关路由国际化占位，需根据实际情况补充
        comics_category: '漫画分类', // 漫画分类相关路由国际化占位，需根据实际情况补充
        comics_chapter: '漫画章节', // 漫画章节相关路由国际化占位，需根据实际情况补充
        comics_list: '漫画列表', // 漫画列表相关路由国际化占位，需根据实际情况补充
        comics_page: '漫画页面', // 漫画页面相关路由国际化占位，需根据实际情况补充
        pictures: '图片管理', // 图片相关路由国际化占位，需根据实际情况补充
        pictures_list: '图片列表', // 图片列表相关路由国际化占位，需根据实际情况补充
        pictures_category: '图片分类',
        pictures_album: '图片专辑',
        posts: '帖子管理', // 文章相关路由国际化占位，需根据实际情况补充
        posts_comment: '文章评论', // 文章评论相关路由国际化占位，需根据实际情况补充
        shortvideos: '短视频管理', // 短视频相关路由国际化占位，需根据实际情况补充
        shortvideos_category: '短视频分类', // 短视频分类相关路由国际化占位，需根据实际情况补充
        shortvideos_list: '短视频列表', // 短视频列表相关路由国际化占位，需根据实际情况补充
        videos_list: '视频列表', // 视频列表相关路由国际化占位，需根据实际情况补充
        videos_albums: '视频专辑', // 视频专辑相关路由国际化占位，需根据实际情况补充
        videos_comment: '视频评论', // 视频评论相关路由国际化占位，需根据实际情况补充
        videos_channel: '视频频道',
        videos_category: '视频分类',
        users: '用户管理', // 用户相关路由国际化占位，需根据实际情况补充
        users_list: '用户列表', // 用户列表相关路由国际化占位，需根据实际情况补充
        users_role: '用户角色', // 用户角色相关路由国际化占位，需根据实际情况补充
        users_permission: '用户权限', // 用户权限相关路由国际化占位，需根据实际情况补充
        users_menu: '用户菜单', // 用户菜单相关路由国际化占位，需根据实际情况补充
        users_login: '用户登录', // 用户登录相关路由国际化占位，需根据实际情况补充
        "users_login-logs": '用户登录日志', // 用户登录日志相关路由国际化占位，需根据实际情况补充
        integral: '积分管理',
        posts_list: '帖子列表',
        shortvideos_comment: '短视频评论',
        integral_list: '积分列表',
        validate: '验证',
        profile: '',
        system_tags: '标签管理',
        login: '登录',
        403: '无权限',
        404: '页面不存在',
        500: '服务器错误',
        'iframe-page': '外链页面',
        home: '首页',
        document: '文档',
        document_project: '项目文档',
        'document_project-link': '项目文档(外链)',
        document_vue: 'Vue文档',
        document_vite: 'Vite文档',
        document_unocss: 'UnoCSS文档',
        document_naive: 'Naive UI文档',
        document_antd: 'Ant Design Vue文档',
        'document_element-plus': 'Element Plus文档',
        document_alova: 'Alova文档',
        'user-center': '个人中心',
        about: '关于',
        function: '系统功能',
        alova: 'alova示例',
        alova_request: 'alova请求',
        alova_user: '用户列表',
        alova_scenes: '场景化请求',
        function_tab: '标签页',
        'function_multi-tab': '多标签页',
        'function_hide-child': '隐藏子菜单',
        'function_hide-child_one': '隐藏子菜单',
        'function_hide-child_two': '菜单二',
        'function_hide-child_three': '菜单三',
        function_request: '请求',
        'function_toggle-auth': '切换权限',
        'function_super-page': '超级管理员可见',
        manage: '系统管理',
        manage_user: '用户管理',
        'manage_user-detail': '用户详情',
        manage_role: '角色管理',
        manage_menu: '菜单管理',
        'multi-menu': '多级菜单',
        'multi-menu_first': '菜单一',
        'multi-menu_first_child': '菜单一子菜单',
        'multi-menu_second': '菜单二',
        'multi-menu_second_child': '菜单二子菜单',
        'multi-menu_second_child_home': '菜单二子菜单首页',
        exception: '异常页',
        exception_403: '403',
        exception_404: '404',
        exception_500: '500',
        plugin: '插件示例',
        plugin_copy: '剪贴板',
        plugin_charts: '图表',
        plugin_charts_echarts: 'ECharts',
        plugin_charts_antv: 'AntV',
        plugin_charts_vchart: 'VChart',
        plugin_editor: '编辑器',
        plugin_editor_quill: '富文本编辑器',
        plugin_editor_markdown: 'MD 编辑器',
        plugin_icon: '图标',
        plugin_map: '地图',
        plugin_print: '打印',
        plugin_swiper: 'Swiper',
        plugin_video: '视频',
        plugin_barcode: '条形码',
        plugin_pinyin: '拼音',
        plugin_excel: 'Excel',
        plugin_pdf: 'PDF 预览',
        plugin_gantt: '甘特图',
        plugin_gantt_dhtmlx: 'dhtmlxGantt',
        plugin_gantt_vtable: 'VTableGantt',
        plugin_typeit: '打字机',
        plugin_tables: '表格',
        plugin_tables_vtable: 'VTable'
    },
    page: {
        login: {
            common: {
                loginOrRegister: '登录 / 注册',
                userNamePlaceholder: '请输入用户名',
                phonePlaceholder: '请输入手机号',
                codePlaceholder: '请输入验证码',
                passwordPlaceholder: '请输入密码',
                confirmPasswordPlaceholder: '请再次输入密码',
                codeLogin: '验证码登录',
                confirm: '确定',
                back: '返回',
                validateSuccess: '验证成功',
                loginSuccess: '登录成功',
                welcomeBack: '欢迎回来，{userName} ！'
            },
            pwdLogin: {
                title: '密码登录',
                rememberMe: '记住我',
                forgetPassword: '忘记密码？',
                register: '注册账号',
                otherAccountLogin: '其他账号登录',
                otherLoginMode: '其他登录方式',
                superAdmin: '超级管理员',
                admin: '管理员',
                user: '普通用户'
            },
            codeLogin: {
                title: '验证码登录',
                getCode: '获取验证码',
                reGetCode: '{time}秒后重新获取',
                sendCodeSuccess: '验证码发送成功',
                imageCodePlaceholder: '请输入图片验证码'
            },
            register: {
                title: '注册账号',
                agreement: '我已经仔细阅读并接受',
                protocol: '《用户协议》',
                policy: '《隐私权政策》'
            },
            resetPwd: {
                title: '重置密码'
            },
            bindWeChat: {
                title: '绑定微信'
            }
        },
        about: {
            title: '关于',
            introduction: `SoybeanAdmin 是一个优雅且功能强大的后台管理模板，基于最新的前端技术栈，包括 Vue3, Vite5, TypeScript, Pinia 和 UnoCSS。它内置了丰富的主题配置和组件，代码规范严谨，实现了自动化的文件路由系统。此外，它还采用了基于 ApiFox 的在线Mock数据方案。SoybeanAdmin 为您提供了一站式的后台管理解决方案，无需额外配置，开箱即用。同样是一个快速学习前沿技术的最佳实践。`,
            projectInfo: {
                title: '项目信息',
                version: '版本',
                latestBuildTime: '最新构建时间',
                githubLink: 'Github 地址',
                previewLink: '预览地址'
            },
            prdDep: '生产依赖',
            devDep: '开发依赖'
        },
        home: {
            branchDesc:
                '为了方便大家开发和更新合并，我们对main分支的代码进行了精简，只保留了首页菜单，其余内容已移至example分支进行维护。预览地址显示的内容即为example分支的内容。',
            greeting: '早安，{userName}, 今天又是充满活力的一天!',
            weatherDesc: '今日多云转晴，20℃ - 25℃!',
            projectCount: '项目数',
            todo: '待办',
            message: '消息',
            downloadCount: '下载量',
            registerCount: '注册量',
            schedule: '作息安排',
            study: '学习',
            work: '工作',
            rest: '休息',
            entertainment: '娱乐',
            visitCount: '访问量',
            turnover: '成交额',
            dealCount: '成交量',
            projectNews: {
                title: '项目动态',
                moreNews: '更多动态',
                desc1: 'Soybean 在2021年5月28日创建了开源项目 soybean-admin!',
                desc2: 'Yanbowe 向 soybean-admin 提交了一个bug，多标签栏不会自适应。',
                desc3: 'Soybean 准备为 soybean-admin 的发布做充分的准备工作!',
                desc4: 'Soybean 正在忙于为soybean-admin写项目说明文档！',
                desc5: 'Soybean 刚才把工作台页面随便写了一些，凑合能看了！'
            },
            creativity: '创意'
        },
        function: {
            tab: {
                tabOperate: {
                    title: '标签页操作',
                    addTab: '添加标签页',
                    addTabDesc: '跳转到关于页面',
                    closeTab: '关闭标签页',
                    closeCurrentTab: '关闭当前标签页',
                    closeAboutTab: '关闭"关于"标签页',
                    addMultiTab: '添加多标签页',
                    addMultiTabDesc1: '跳转到多标签页页面',
                    addMultiTabDesc2: '跳转到多标签页页面(带有查询参数)'
                },
                tabTitle: {
                    title: '标签页标题',
                    changeTitle: '修改标题',
                    change: '修改',
                    resetTitle: '重置标题',
                    reset: '重置'
                }
            },
            multiTab: {
                routeParam: '路由参数',
                backTab: '返回 function_tab'
            },
            toggleAuth: {
                toggleAccount: '切换账号',
                authHook: '权限钩子函数 `hasAuth`',
                superAdminVisible: '超级管理员可见',
                adminVisible: '管理员可见',
                adminOrUserVisible: '管理员和用户可见'
            },
            request: {
                repeatedErrorOccurOnce: '重复请求错误只出现一次',
                repeatedError: '重复请求错误',
                repeatedErrorMsg1: '自定义请求错误 1',
                repeatedErrorMsg2: '自定义请求错误 2'
            }
        },
        alova: {
            scenes: {
                captchaSend: '发送验证码',
                autoRequest: '自动请求',
                visibilityRequestTips: '浏览器窗口切换自动请求数据',
                pollingRequestTips: '每3秒自动请求一次',
                networkRequestTips: '网络重连后自动请求',
                refreshTime: '更新时间',
                startRequest: '开始请求',
                stopRequest: '停止请求',
                requestCrossComponent: '跨组件触发请求',
                triggerAllRequest: '手动触发所有自动请求'
            }
        },
        manage: {
            common: {
                status: {
                    enable: '启用',
                    disable: '禁用'
                }
            },
            role: {
                title: '角色列表',
                roleName: '角色名称',
                roleCode: '角色编码',
                roleStatus: '角色状态',
                roleDesc: '角色描述',
                menuAuth: '菜单权限',
                buttonAuth: '按钮权限',
                form: {
                    roleName: '请输入角色名称',
                    roleCode: '请输入角色编码',
                    roleStatus: '请选择角色状态',
                    roleDesc: '请输入角色描述'
                },
                addRole: '新增角色',
                editRole: '编辑角色'
            },
            user: {
                title: '用户列表',
                userName: '用户名',
                userGender: '性别',
                nickName: '昵称',
                userPhone: '手机号',
                userEmail: '邮箱',
                userStatus: '用户状态',
                userRole: '用户角色',
                form: {
                    userName: '请输入用户名',
                    userGender: '请选择性别',
                    nickName: '请输入昵称',
                    userPhone: '请输入手机号',
                    userEmail: '请输入邮箱',
                    userStatus: '请选择用户状态',
                    userRole: '请选择用户角色'
                },
                addUser: '新增用户',
                editUser: '编辑用户',
                gender: {
                    male: '男',
                    female: '女'
                }
            },
            menu: {
                home: '首页',
                title: '菜单列表',
                id: 'ID',
                parentId: '父级菜单ID',
                menuType: '菜单类型',
                menuName: '菜单名称',
                routeName: '路由名称',
                routePath: '路由路径',
                pathParam: '路径参数',
                layout: '布局',
                page: '页面组件',
                i18nKey: '国际化key',
                icon: '图标',
                localIcon: '本地图标',
                iconTypeTitle: '图标类型',
                order: '排序',
                constant: '常量路由',
                keepAlive: '缓存路由',
                href: '外链',
                hideInMenu: '隐藏菜单',
                activeMenu: '高亮的菜单',
                multiTab: '支持多页签',
                fixedIndexInTab: '固定在页签中的序号',
                query: '路由参数',
                button: '按钮',
                buttonCode: '按钮编码',
                buttonDesc: '按钮描述',
                menuStatus: '菜单状态',
                form: {
                    home: '请选择首页',
                    menuType: '请选择菜单类型',
                    menuName: '请输入菜单名称',
                    routeName: '请输入路由名称',
                    routePath: '请输入路由路径',
                    pathParam: '请输入路径参数',
                    page: '请选择页面组件',
                    layout: '请选择布局组件',
                    i18nKey: '请输入国际化key',
                    icon: '请输入图标',
                    localIcon: '请选择本地图标',
                    order: '请输入排序',
                    keepAlive: '请选择是否缓存路由',
                    href: '请输入外链',
                    hideInMenu: '请选择是否隐藏菜单',
                    activeMenu: '请选择高亮的菜单的路由名称',
                    multiTab: '请选择是否支持多标签',
                    fixedInTab: '请选择是否固定在页签中',
                    fixedIndexInTab: '请输入固定在页签中的序号',
                    queryKey: '请输入路由参数Key',
                    queryValue: '请输入路由参数Value',
                    button: '请选择是否按钮',
                    buttonCode: '请输入按钮编码',
                    buttonDesc: '请输入按钮描述',
                    menuStatus: '请选择菜单状态'
                },
                addMenu: '新增菜单',
                editMenu: '编辑菜单',
                addChildMenu: '新增子菜单',
                type: {
                    directory: '目录',
                    menu: '菜单'
                },
                iconType: {
                    iconify: 'iconify图标',
                    local: '本地图标'
                }
            }
        }
    },
    form: {
        required: '不能为空',
        userName: {
            required: '请输入用户名',
            invalid: '用户名格式不正确'
        },
        phone: {
            required: '请输入手机号',
            invalid: '手机号格式不正确'
        },
        pwd: {
            required: '请输入密码',
            invalid: '密码格式不正确，6-18位字符，包含字母、数字、下划线'
        },
        confirmPwd: {
            required: '请输入确认密码',
            invalid: '两次输入密码不一致'
        },
        code: {
            required: '请输入验证码',
            invalid: '验证码格式不正确'
        },
        email: {
            required: '请输入邮箱',
            invalid: '邮箱格式不正确'
        }
    },
    dropdown: {
        closeCurrent: '关闭',
        closeOther: '关闭其它',
        closeLeft: '关闭左侧',
        closeRight: '关闭右侧',
        closeAll: '关闭所有'
    },
    icon: {
        themeConfig: '主题配置',
        themeSchema: '主题模式',
        lang: '切换语言',
        fullscreen: '全屏',
        fullscreenExit: '退出全屏',
        reload: '刷新页面',
        collapse: '折叠菜单',
        expand: '展开菜单',
        pin: '固定',
        unpin: '取消固定'
    },
    datatable: {
        itemCount: '共 {total} 条'
    },
    videos: {
        info: {
            title: "视频管理",
            name: "视频名称",
            category: "视频分类",
            cover: "视频封面",
            channel: "视频频道",
            status: "视频状态",
            url: "视频地址",
            celebrityName: "关联明星",
            duration: "时长",
            categoryName: "视频分类",
            likeCount: "点赞数",
            commentCount: "评论数",
            viewCount: "观看数",
            favoriteCount: "收藏数",
            quality: "视频质量",
            format: "视频格式",
            downloadCount: "下载数",
            channelName: "视频频道",
            tags: "标签",
            isVip: "是否VIP可见",
            srcType: "视频来源",
            srcUrl: "视频来源地址",
            resolution: "分辨率",
            size: "视频大小",
            video: "视频",
            isPaid: "是否付费",
            isFeatured: "是否精选",
            free: "免费",
            paid: "付费",
            description: "视频描述",
            dislikeCount: "不喜欢数",
            shareCount: "分享数",
            creatorName: "创建者",
            vipLevel: "VIP等级",
            isPrivate: "是否私密",
            price: "价格",
            uploadTime: "上传时间",
            score: "评分",
            createTime: "创建时间",
            updateTime: "更新时间",
            add: "添加视频",
            edit: "编辑视频",
            delete: "删除视频",
            deleteConfirm: "确定删除该视频吗？",
            deleteSuccess: "删除成功",
            deleteFail: "删除失败",
            addSuccess: "添加成功",
            addFail: "添加失败",
            editSuccess: "编辑成功",
            editFail: "编辑失败",
            deleteError: "删除错误",
            addError: "添加错误",
            editError: "编辑错误",
            nameRequired: "视频名称不能为空",
            addNew: "添加新视频",
            titlePlaceholder: "请输入视频名称",
            titleRequired: "视频名称不能为空",
            categoryRequired: "视频分类不能为空",
            categoryPlaceholder: "请选择视频分类",
            channelRequired: "视频频道不能为空",
            channelPlaceholder: "请选择视频频道",
            uploadCover: "上传封面",
            uploadVideo: "上传视频",
            coverTip: "支持jpg/png，最大2MB",
            coverRequired: "封面不能为空",
            onlyImageAllowed: "仅支持图片格式",
            imageSizeLimit: "图片大小不能超过2MB",
            coverUploadSuccess: "封面上传成功",
            coverUploadFailed: "封面上传失败",
            videoTip: "支持mp4，最大2000MB",
            videoRequired: "视频不能为空",
            onlyVideoAllowed: "仅支持视频格式",
            videoSizeLimit: "视频大小不能超过2000MB",
            videoUploadSuccess: "视频上传成功",
            videoUploadFailed: "视频上传失败",
            descriptionPlaceholder: "请输入视频简介",
            descriptionRequired: "简介不能为空",
            updateFailed: "更新失败",
            coverPlaceholder: "请上传封面",
            videoPlaceholder: "请上传视频",
            videoUrlPlaceholder: "请输入视频地址",
            tagsPlaceholder: "请输入标签",
            tagsRequired: "标签不能为空",
            tagsMaxLength: "标签不能超过10个字符",
            titleLength: "视频名称不能超过50个字符",
            updateSuccess: "更新成功",
            categoryOrChannelRequired: "视频分类或视频频道不能为空",
            deleteConfirmContent: "确定要删除吗？",
            addFailed: "添加失败"
        },
        channel: {
            recommended: "频道推荐",
            hot: "热门",
            movie: "电影",
        },
        dialog: {
            editTitle: '编辑视频',
            addTitle: '添加视频',
        },
        messages: {
            updateSuccess: '更新成功',

        },
        status: {
            published: "已发布",
            draft: "草稿",
            deleted: "已删除",
            rejected: "已拒绝"
        }
    },
    books: {
        info: {
            manage: "电子书管理",
            title: "标题",
            edit: "编辑",
            author: "作者",
            category: "分类",
            cover: "封面",
            status: "状态",
            price: "价格",
            description: "描述",
            sort_order: "排序",
            tags: "标签",
            code: "编码",
            yes: "是",
            no: "否",
            search: "搜索",
            reset: "重置",
            refresh: "刷新",
            addNew: "新增",
            load: "加载",
            coverTip: "图书封面支持jpg/png，最大2MB",
            wordCount: "字数",
            isPaid: "是否付费",
            isFeatured: "是否精选",
            publishDate: "发布日期",
            dataLoadFailed: "数据加载失败",
            loadFailed: "加载失败",
            featured: "是否精选",
            enterTitle: "请输入标题",
            enterAuthor: "请输入作者",
            enterCategory: "请输入分类",
            enterCover: "请输入封面",
            enterStatus: "请输入状态",
            enterPrice: "请输入价格",
            enterDescription: "请输入描述",
            enterSortOrder: "请输入排序",
            enterCode: "请输入编码",
            enterTags: "请输入标签",
            selectCategory: "请选择分类",
            selectPublishDate: "请选择发布日期",
            selectStatus: "请选择状态",
            selectFeatured: "请选择是否精选",
            confirmDelete: "确定删除吗？",
            processing: "进度",
            progress: "进度",
            statistics: "统计",
            readCount: "阅读数",
            favoriteCount: "收藏数",
            likeCount: "点赞数",
            shareCount: "分享数",
            commentCount: "评论数",
            viewCount: "观看数",
            createdAt: "创建时间",
            updatedAt: "更新时间",
            chapterCount: '章节统计',
            basicinfo: "基础信息",
            publishInfo: "发布信息",
            manageChapters: "管理章节",
            updateFailed: "更新失败",
            updateSuccess: "更新成功",

        },
        dialog: {
            add: "新增图书",
            edit: "编辑图书"
        }

    }

};

export default local;
