package api

import (
	"frontapi/internal/bootstrap"
	//content_service "frontapi/internal/service/content_creator"

	"github.com/gofiber/fiber/v2"
)

// RegisterContentCreatorRoutes 注册内容创作相关路由
func RegisterContentCreatorRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// 创建内容创作相关控制器
	// auditApi := content_creator.NewContentAuditController(services.ContentAuditService)
	// heatApi := content_creator.NewContentHeatController(services.ContentHeatService)
	// ratingApi := content_creator.NewContentRatingController(services.ContentRatingService)
	// revenueApi := content_creator.NewContentRevenueController(services.ContentRevenueService)
	// revenueRuleApi := content_creator.NewRevenueRuleController(services.RevenueRuleService)

}
