import type { UserItem } from "@/types/users";
import { request } from "../../request";

// 用户查询参数接口
interface UserParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        reg_time?: string;
        last_login_time?: string;
        last_activity_time?: string;
        score?: number;
        status?: number;
    }
}

// 登录日志查询参数
interface LoginLogParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        user_id?: string;
        username?: string;
        type?: number;
        login_status?: string;
        ip_address?: string;
        device_type?: string;
        location?: string;
        start_date?: string;
        end_date?: string;
    }
}

// 用户 API

/**
 * 获取用户列表
 */
export function getUserList(params?: UserParams) {
    return request({
        url: "/users/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取用户详情
 */
export function getUserDetail(id: string) {
    return request({
        url: `/users/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })

}

/**
 * 新增用户
 */
export function addUser(data: Partial<UserItem>) {
    return request({
        url: "/users/add",
        method: "post",
        data: { data: data }
    })
}

/**
 * 更新用户
 */
export function updateUser(data: Partial<UserItem>) {
    return request({
        url: "/users/update",
        method: "post",
        data: { data: data }
    })
}

export function updateUserStatus(data: any) {
    return request({
        url: "/users/update-status",
        method: "post",
        data: { data: data }
    })
}

/**
 * 批量更新用户状态
 */
export function batchUpdateUserStatus(data: any) {
    return request({
        url: "/users/batch-update-status",
        method: "post",
        data: { data: data }
    })
}
/**
 * 删除用户
 */
export function deleteUser(id: string) {
    return request({
        url: `/users/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}
/**
 * 批量更新用户
 */
export function batchDeleteUser(data: any) {
    return request({
        url: "/users/batch-delete",
        method: "post",
        data: { data: data }
    })
}


// 登录日志 API

/**
 * 获取用户登录日志列表
 */
export function getLoginLogs(params?: LoginLogParams) {
    return request({
        url: "/users/login-logs/list",
        method: "post",
        data: params,
    })
}

/**
 * 获取登录日志详情
 */
export function getLoginLogDetail(id: string) {
    return request({
        url: `/users/login-logs/detail/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 删除登录日志
 */
export function deleteLoginLog(id: string) {
    return request({
        url: `/users/login-logs/delete/${id}`,
        method: "post",
        data: { data: { "id": id } }
    })
}

/**
 * 批量删除登录日志
 */
export function batchDeleteLoginLogs(ids: string[]) {
    return request({
        url: "/users/login-logs/batch-delete",
        method: "post",
        data: { data: { ids } }
    })
}

/**
 * 清空登录日志
 */
export function clearLoginLogs(params: any) {
    return request({
        url: "/users/login-logs/clear",
        method: "post",
        data: { data: params }
    })
}
