/**
 * Warm 主题配置
 * 基于温暖色调的主题
 */
import { ThemeConfig } from '../theme-manager';
import warmVariables from './variables';
import warmDarkVariables from './variables-dark';

// Warm 亮色主题
export const warmLightTheme: ThemeConfig = {
    name: 'warm-light',
    displayName: 'Warm Light',
    shortName: 'Warm Light',
    code: 'warm',
    primary: '#FF7043',
    isDark: false,
    variables: warmVariables
};

// Warm 暗色主题
export const warmDarkTheme: ThemeConfig = {
    name: 'warm-dark',
    displayName: 'Warm Dark',
    shortName: 'Warm Dark',
    code: 'warm',
    primary: '#FF8A65',
    isDark: true,
    variables: warmDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default warmLightTheme;