package admin

import (
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

func RegisterHotKeywordRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	//group := app.Group("/api/proadm/hot_keywords")
	//group.Post("/add", middleware.AuthRequired(), middleware.RequireRole("admin"), api.Create)
	//group.Post("/update/:id?", middleware.AuthRequired(), middleware.RequireRole("admin"), api.Update)
	//group.Post("/delete/:id?", middleware.AuthRequired(), middleware.RequireRole("admin"), api.Delete)
	//group.Post("/detail/:id?", api.Get)
	//group.Post("/list", api.List)
}
