package integral

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/integral"

	"github.com/gofiber/fiber/v2"
)

type PointController struct {
	admin.BaseController
	service integral.PointService
}

func NewPointController(service integral.PointService) *PointController {
	return &PointController{service: service}
}

// GetPoint 获取积分详情
func (h *PointController) GetPoint(c *fiber.Ctx) error {
	id := c.Params("id")
	if id == "" {
		return h.BadRequest(c, "积分ID不能为空", nil)
	}
	point, err := h.service.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "积分记录不存在")
	}
	return h.Success(c, point)
}

// ListPoints 获取积分列表
func (h *PointController) ListPoints(c *fiber.Ctx) error {
	reqInfo := h.GetRequestInfo(c)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	condition := map[string]interface{}{}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	points, total, err := h.service.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取积分列表失败")
	}
	return h.SuccessList(c, points, total, page, pageSize)
}
