/**
 * 数字工具函数
 */

/**
 * 数字格式化选项
 */
export interface NumberFormatOptions {
  locale?: string // 语言环境
  style?: 'decimal' | 'currency' | 'percent' | 'unit' // 格式样式
  currency?: string // 货币代码
  unit?: string // 单位
  minimumFractionDigits?: number // 最小小数位数
  maximumFractionDigits?: number // 最大小数位数
  minimumIntegerDigits?: number // 最小整数位数
  useGrouping?: boolean // 是否使用千分位分隔符
  notation?: 'standard' | 'scientific' | 'engineering' | 'compact' // 记数法
  compactDisplay?: 'short' | 'long' // 紧凑显示方式
  signDisplay?: 'auto' | 'never' | 'always' | 'exceptZero' // 符号显示
}

/**
 * 数字范围接口
 */
export interface NumberRange {
  min: number
  max: number
}

/**
 * 统计信息接口
 */
export interface Statistics {
  count: number
  sum: number
  mean: number
  median: number
  mode: number[]
  min: number
  max: number
  range: number
  variance: number
  standardDeviation: number
  skewness: number
  kurtosis: number
}

/**
 * 数字工具类
 */
export class NumberUtils {
  /**
   * 检查是否为数字
   */
  static isNumber(value: any): value is number {
    return typeof value === 'number' && !isNaN(value) && isFinite(value)
  }

  /**
   * 检查是否为整数
   */
  static isInteger(value: any): value is number {
    return this.isNumber(value) && Number.isInteger(value)
  }

  /**
   * 检查是否为正数
   */
  static isPositive(value: any): value is number {
    return this.isNumber(value) && value > 0
  }

  /**
   * 检查是否为负数
   */
  static isNegative(value: any): value is number {
    return this.isNumber(value) && value < 0
  }

  /**
   * 检查是否为零
   */
  static isZero(value: any): value is number {
    return this.isNumber(value) && value === 0
  }

  /**
   * 检查是否为偶数
   */
  static isEven(value: any): value is number {
    return this.isInteger(value) && value % 2 === 0
  }

  /**
   * 检查是否为奇数
   */
  static isOdd(value: any): value is number {
    return this.isInteger(value) && value % 2 !== 0
  }

  /**
   * 检查是否为质数
   */
  static isPrime(value: any): value is number {
    if (!this.isInteger(value) || value < 2) {
      return false
    }
    
    if (value === 2) {
      return true
    }
    
    if (value % 2 === 0) {
      return false
    }
    
    const sqrt = Math.sqrt(value)
    for (let i = 3; i <= sqrt; i += 2) {
      if (value % i === 0) {
        return false
      }
    }
    
    return true
  }

  /**
   * 检查是否在范围内
   */
  static isInRange(value: number, range: NumberRange, inclusive = true): boolean {
    if (!this.isNumber(value)) {
      return false
    }
    
    if (inclusive) {
      return value >= range.min && value <= range.max
    } else {
      return value > range.min && value < range.max
    }
  }

  /**
   * 转换为数字
   */
  static toNumber(value: any, defaultValue = 0): number {
    if (this.isNumber(value)) {
      return value
    }
    
    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return isNaN(parsed) ? defaultValue : parsed
    }
    
    if (typeof value === 'boolean') {
      return value ? 1 : 0
    }
    
    return defaultValue
  }

  /**
   * 安全转换为整数
   */
  static toInteger(value: any, defaultValue = 0): number {
    const num = this.toNumber(value, defaultValue)
    return Math.trunc(num)
  }

  /**
   * 格式化数字
   */
  static format(value: number, options: NumberFormatOptions = {}): string {
    if (!this.isNumber(value)) {
      return 'NaN'
    }

    const {
      locale = 'zh-CN',
      style = 'decimal',
      ...formatOptions
    } = options

    try {
      return new Intl.NumberFormat(locale, {
        style,
        ...formatOptions
      }).format(value)
    } catch (error) {
      return value.toString()
    }
  }

  /**
   * 格式化为货币
   */
  static formatCurrency(value: number, currency = 'CNY', locale = 'zh-CN'): string {
    return this.format(value, {
      style: 'currency',
      currency,
      locale
    })
  }

  /**
   * 格式化为百分比
   */
  static formatPercent(value: number, locale = 'zh-CN', fractionDigits = 2): string {
    return this.format(value, {
      style: 'percent',
      locale,
      minimumFractionDigits: fractionDigits,
      maximumFractionDigits: fractionDigits
    })
  }

  /**
   * 格式化为紧凑形式
   */
  static formatCompact(value: number, locale = 'zh-CN', notation: 'compact' = 'compact'): string {
    return this.format(value, {
      notation,
      locale
    })
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number, locale = 'zh-CN', binary = true): string {
    if (!this.isNumber(bytes) || bytes < 0) {
      return '0 B'
    }

    const base = binary ? 1024 : 1000
    const units = binary
      ? ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB']
      : ['B', 'KB', 'MB', 'GB', 'TB', 'PB']

    if (bytes === 0) {
      return '0 B'
    }

    const exponent = Math.floor(Math.log(bytes) / Math.log(base))
    const value = bytes / Math.pow(base, exponent)
    const unit = units[Math.min(exponent, units.length - 1)]

    return `${this.format(value, {
      locale,
      minimumFractionDigits: 0,
      maximumFractionDigits: exponent === 0 ? 0 : 2
    })} ${unit}`
  }

  /**
   * 四舍五入到指定小数位
   */
  static round(value: number, precision = 0): number {
    if (!this.isNumber(value)) {
      return 0
    }
    
    const factor = Math.pow(10, precision)
    return Math.round(value * factor) / factor
  }

  /**
   * 向上取整到指定小数位
   */
  static ceil(value: number, precision = 0): number {
    if (!this.isNumber(value)) {
      return 0
    }
    
    const factor = Math.pow(10, precision)
    return Math.ceil(value * factor) / factor
  }

  /**
   * 向下取整到指定小数位
   */
  static floor(value: number, precision = 0): number {
    if (!this.isNumber(value)) {
      return 0
    }
    
    const factor = Math.pow(10, precision)
    return Math.floor(value * factor) / factor
  }

  /**
   * 截断到指定小数位
   */
  static trunc(value: number, precision = 0): number {
    if (!this.isNumber(value)) {
      return 0
    }
    
    const factor = Math.pow(10, precision)
    return Math.trunc(value * factor) / factor
  }

  /**
   * 限制数字在指定范围内
   */
  static clamp(value: number, min: number, max: number): number {
    if (!this.isNumber(value)) {
      return min
    }
    
    return Math.min(Math.max(value, min), max)
  }

  /**
   * 线性插值
   */
  static lerp(start: number, end: number, t: number): number {
    if (!this.isNumber(start) || !this.isNumber(end) || !this.isNumber(t)) {
      return start
    }
    
    return start + (end - start) * t
  }

  /**
   * 反向线性插值
   */
  static inverseLerp(start: number, end: number, value: number): number {
    if (!this.isNumber(start) || !this.isNumber(end) || !this.isNumber(value)) {
      return 0
    }
    
    if (start === end) {
      return 0
    }
    
    return (value - start) / (end - start)
  }

  /**
   * 映射数字从一个范围到另一个范围
   */
  static map(
    value: number,
    fromMin: number,
    fromMax: number,
    toMin: number,
    toMax: number
  ): number {
    if (!this.isNumber(value)) {
      return toMin
    }
    
    const t = this.inverseLerp(fromMin, fromMax, value)
    return this.lerp(toMin, toMax, t)
  }

  /**
   * 计算百分比
   */
  static percentage(value: number, total: number): number {
    if (!this.isNumber(value) || !this.isNumber(total) || total === 0) {
      return 0
    }
    
    return (value / total) * 100
  }

  /**
   * 从百分比计算值
   */
  static fromPercentage(percentage: number, total: number): number {
    if (!this.isNumber(percentage) || !this.isNumber(total)) {
      return 0
    }
    
    return (percentage / 100) * total
  }

  /**
   * 计算平均值
   */
  static average(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return 0
    }
    
    return this.sum(validNumbers) / validNumbers.length
  }

  /**
   * 计算总和
   */
  static sum(numbers: number[]): number {
    if (!Array.isArray(numbers)) {
      return 0
    }
    
    return numbers.filter(this.isNumber).reduce((sum, num) => sum + num, 0)
  }

  /**
   * 计算乘积
   */
  static product(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return 0
    }
    
    return validNumbers.reduce((product, num) => product * num, 1)
  }

  /**
   * 获取最小值
   */
  static min(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return 0
    }
    
    return Math.min(...validNumbers)
  }

  /**
   * 获取最大值
   */
  static max(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return 0
    }
    
    return Math.max(...validNumbers)
  }

  /**
   * 计算中位数
   */
  static median(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber).sort((a, b) => a - b)
    if (validNumbers.length === 0) {
      return 0
    }
    
    const middle = Math.floor(validNumbers.length / 2)
    
    if (validNumbers.length % 2 === 0) {
      return (validNumbers[middle - 1] + validNumbers[middle]) / 2
    } else {
      return validNumbers[middle]
    }
  }

  /**
   * 计算众数
   */
  static mode(numbers: number[]): number[] {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return []
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return []
    }
    
    const frequency = new Map<number, number>()
    
    validNumbers.forEach(num => {
      frequency.set(num, (frequency.get(num) || 0) + 1)
    })
    
    const maxFreq = Math.max(...frequency.values())
    
    return Array.from(frequency.entries())
      .filter(([, freq]) => freq === maxFreq)
      .map(([num]) => num)
  }

  /**
   * 计算方差
   */
  static variance(numbers: number[], sample = false): number {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length === 0) {
      return 0
    }
    
    const mean = this.average(validNumbers)
    const squaredDiffs = validNumbers.map(num => Math.pow(num - mean, 2))
    const divisor = sample ? validNumbers.length - 1 : validNumbers.length
    
    return this.sum(squaredDiffs) / divisor
  }

  /**
   * 计算标准差
   */
  static standardDeviation(numbers: number[], sample = false): number {
    return Math.sqrt(this.variance(numbers, sample))
  }

  /**
   * 计算偏度
   */
  static skewness(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length < 3) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length < 3) {
      return 0
    }
    
    const mean = this.average(validNumbers)
    const std = this.standardDeviation(validNumbers)
    
    if (std === 0) {
      return 0
    }
    
    const n = validNumbers.length
    const cubedDiffs = validNumbers.map(num => Math.pow((num - mean) / std, 3))
    
    return (n / ((n - 1) * (n - 2))) * this.sum(cubedDiffs)
  }

  /**
   * 计算峰度
   */
  static kurtosis(numbers: number[]): number {
    if (!Array.isArray(numbers) || numbers.length < 4) {
      return 0
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    if (validNumbers.length < 4) {
      return 0
    }
    
    const mean = this.average(validNumbers)
    const std = this.standardDeviation(validNumbers)
    
    if (std === 0) {
      return 0
    }
    
    const n = validNumbers.length
    const fourthPowerDiffs = validNumbers.map(num => Math.pow((num - mean) / std, 4))
    
    const kurtosis = (n * (n + 1) / ((n - 1) * (n - 2) * (n - 3))) * this.sum(fourthPowerDiffs)
    const correction = 3 * Math.pow(n - 1, 2) / ((n - 2) * (n - 3))
    
    return kurtosis - correction
  }

  /**
   * 计算统计信息
   */
  static getStatistics(numbers: number[]): Statistics {
    if (!Array.isArray(numbers) || numbers.length === 0) {
      return {
        count: 0,
        sum: 0,
        mean: 0,
        median: 0,
        mode: [],
        min: 0,
        max: 0,
        range: 0,
        variance: 0,
        standardDeviation: 0,
        skewness: 0,
        kurtosis: 0
      }
    }
    
    const validNumbers = numbers.filter(this.isNumber)
    
    if (validNumbers.length === 0) {
      return {
        count: 0,
        sum: 0,
        mean: 0,
        median: 0,
        mode: [],
        min: 0,
        max: 0,
        range: 0,
        variance: 0,
        standardDeviation: 0,
        skewness: 0,
        kurtosis: 0
      }
    }
    
    const sum = this.sum(validNumbers)
    const mean = this.average(validNumbers)
    const median = this.median(validNumbers)
    const mode = this.mode(validNumbers)
    const min = this.min(validNumbers)
    const max = this.max(validNumbers)
    const range = max - min
    const variance = this.variance(validNumbers)
    const standardDeviation = this.standardDeviation(validNumbers)
    const skewness = this.skewness(validNumbers)
    const kurtosis = this.kurtosis(validNumbers)
    
    return {
      count: validNumbers.length,
      sum,
      mean,
      median,
      mode,
      min,
      max,
      range,
      variance,
      standardDeviation,
      skewness,
      kurtosis
    }
  }

  /**
   * 生成随机数
   */
  static random(min = 0, max = 1): number {
    if (!this.isNumber(min) || !this.isNumber(max)) {
      return 0
    }
    
    return Math.random() * (max - min) + min
  }

  /**
   * 生成随机整数
   */
  static randomInt(min = 0, max = 100): number {
    if (!this.isNumber(min) || !this.isNumber(max)) {
      return 0
    }
    
    return Math.floor(this.random(min, max + 1))
  }

  /**
   * 生成随机数组
   */
  static randomArray(length: number, min = 0, max = 1): number[] {
    if (!this.isInteger(length) || length < 0) {
      return []
    }
    
    return Array.from({ length }, () => this.random(min, max))
  }

  /**
   * 生成随机整数数组
   */
  static randomIntArray(length: number, min = 0, max = 100): number[] {
    if (!this.isInteger(length) || length < 0) {
      return []
    }
    
    return Array.from({ length }, () => this.randomInt(min, max))
  }

  /**
   * 计算阶乘
   */
  static factorial(n: number): number {
    if (!this.isInteger(n) || n < 0) {
      return 0
    }
    
    if (n === 0 || n === 1) {
      return 1
    }
    
    let result = 1
    for (let i = 2; i <= n; i++) {
      result *= i
    }
    
    return result
  }

  /**
   * 计算组合数
   */
  static combination(n: number, r: number): number {
    if (!this.isInteger(n) || !this.isInteger(r) || n < 0 || r < 0 || r > n) {
      return 0
    }
    
    if (r === 0 || r === n) {
      return 1
    }
    
    // 优化计算，选择较小的r
    r = Math.min(r, n - r)
    
    let result = 1
    for (let i = 0; i < r; i++) {
      result = result * (n - i) / (i + 1)
    }
    
    return Math.round(result)
  }

  /**
   * 计算排列数
   */
  static permutation(n: number, r: number): number {
    if (!this.isInteger(n) || !this.isInteger(r) || n < 0 || r < 0 || r > n) {
      return 0
    }
    
    if (r === 0) {
      return 1
    }
    
    let result = 1
    for (let i = 0; i < r; i++) {
      result *= (n - i)
    }
    
    return result
  }

  /**
   * 计算最大公约数
   */
  static gcd(a: number, b: number): number {
    if (!this.isInteger(a) || !this.isInteger(b)) {
      return 0
    }
    
    a = Math.abs(a)
    b = Math.abs(b)
    
    while (b !== 0) {
      const temp = b
      b = a % b
      a = temp
    }
    
    return a
  }

  /**
   * 计算最小公倍数
   */
  static lcm(a: number, b: number): number {
    if (!this.isInteger(a) || !this.isInteger(b)) {
      return 0
    }
    
    if (a === 0 || b === 0) {
      return 0
    }
    
    return Math.abs(a * b) / this.gcd(a, b)
  }

  /**
   * 计算斐波那契数
   */
  static fibonacci(n: number): number {
    if (!this.isInteger(n) || n < 0) {
      return 0
    }
    
    if (n === 0) {
      return 0
    }
    
    if (n === 1) {
      return 1
    }
    
    let a = 0
    let b = 1
    
    for (let i = 2; i <= n; i++) {
      const temp = a + b
      a = b
      b = temp
    }
    
    return b
  }

  /**
   * 生成斐波那契数列
   */
  static fibonacciSequence(length: number): number[] {
    if (!this.isInteger(length) || length < 0) {
      return []
    }
    
    if (length === 0) {
      return []
    }
    
    if (length === 1) {
      return [0]
    }
    
    const sequence = [0, 1]
    
    for (let i = 2; i < length; i++) {
      sequence.push(sequence[i - 1] + sequence[i - 2])
    }
    
    return sequence
  }

  /**
   * 转换进制
   */
  static convertBase(value: string | number, fromBase: number, toBase: number): string {
    if (!this.isInteger(fromBase) || !this.isInteger(toBase) || 
        fromBase < 2 || fromBase > 36 || toBase < 2 || toBase > 36) {
      return '0'
    }
    
    let num: number
    
    if (typeof value === 'string') {
      num = parseInt(value, fromBase)
    } else {
      num = value
    }
    
    if (isNaN(num)) {
      return '0'
    }
    
    return num.toString(toBase).toUpperCase()
  }

  /**
   * 转换为二进制
   */
  static toBinary(value: number): string {
    return this.convertBase(value, 10, 2)
  }

  /**
   * 转换为八进制
   */
  static toOctal(value: number): string {
    return this.convertBase(value, 10, 8)
  }

  /**
   * 转换为十六进制
   */
  static toHex(value: number): string {
    return this.convertBase(value, 10, 16)
  }

  /**
   * 从二进制转换
   */
  static fromBinary(value: string): number {
    return parseInt(this.convertBase(value, 2, 10), 10)
  }

  /**
   * 从八进制转换
   */
  static fromOctal(value: string): number {
    return parseInt(this.convertBase(value, 8, 10), 10)
  }

  /**
   * 从十六进制转换
   */
  static fromHex(value: string): number {
    return parseInt(this.convertBase(value, 16, 10), 10)
  }
}

/**
 * 数字格式化器类
 */
export class NumberFormatter {
  private locale: string
  private defaultOptions: NumberFormatOptions

  constructor(locale = 'zh-CN', defaultOptions: NumberFormatOptions = {}) {
    this.locale = locale
    this.defaultOptions = defaultOptions
  }

  /**
   * 格式化数字
   */
  format(value: number, options: NumberFormatOptions = {}): string {
    const mergedOptions = { ...this.defaultOptions, ...options, locale: this.locale }
    return NumberUtils.format(value, mergedOptions)
  }

  /**
   * 格式化货币
   */
  formatCurrency(value: number, currency = 'CNY'): string {
    return NumberUtils.formatCurrency(value, currency, this.locale)
  }

  /**
   * 格式化百分比
   */
  formatPercent(value: number, fractionDigits = 2): string {
    return NumberUtils.formatPercent(value, this.locale, fractionDigits)
  }

  /**
   * 格式化紧凑形式
   */
  formatCompact(value: number): string {
    return NumberUtils.formatCompact(value, this.locale)
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number, binary = true): string {
    return NumberUtils.formatFileSize(bytes, this.locale, binary)
  }
}

/**
 * 默认数字格式化器实例
 */
export const numberFormatter = new NumberFormatter()

/**
 * 创建数字格式化器
 */
export function createNumberFormatter(locale?: string, defaultOptions?: NumberFormatOptions): NumberFormatter {
  return new NumberFormatter(locale, defaultOptions)
}

// 导出工具实例
export const numberUtils = NumberUtils

// 导出快捷方法
export const {
  isNumber,
  isInteger,
  isPositive,
  isNegative,
  isZero,
  isEven,
  isOdd,
  isPrime,
  isInRange,
  toNumber,
  toInteger,
  format,
  formatCurrency,
  formatPercent,
  formatCompact,
  formatFileSize,
  round,
  ceil,
  floor,
  trunc,
  clamp,
  lerp,
  inverseLerp,
  map,
  percentage,
  fromPercentage,
  average,
  sum,
  product,
  min,
  max,
  median,
  mode,
  variance,
  standardDeviation,
  skewness,
  kurtosis,
  getStatistics,
  random,
  randomInt,
  randomArray,
  randomIntArray,
  factorial,
  combination,
  permutation,
  gcd,
  lcm,
  fibonacci,
  fibonacciSequence,
  convertBase,
  toBinary,
  toOctal,
  toHex,
  fromBinary,
  fromOctal,
  fromHex
} = NumberUtils