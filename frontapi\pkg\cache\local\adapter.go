// Package local 提供本地内存缓存实现
package local

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"frontapi/pkg/cache/types"

	"github.com/dgraph-io/ristretto"
	"github.com/dgraph-io/ristretto/z"
)

// KeyToHash 将键转换为哈希值（模仿ristretto内部函数）
func KeyToHash(key interface{}) (uint64, uint64) {
	if s, ok := key.(string); ok {
		return z.MemHashString(s), 0
	}
	if b, ok := key.([]byte); ok {
		return z.MemHash(b), 0
	}
	return z.MemHashString(fmt.Sprintf("%v", key)), 0
}

// 常量定义
const (
	defaultTTL           = 5 * time.Minute
	defaultCleanInterval = 10 * time.Minute
	defaultShardCount    = 256
)

// Adapter 本地内存缓存适配器，基于ristretto实现
type Adapter struct {
	name           string
	prefix         string
	defaultTTL     time.Duration
	cache          *ristretto.Cache
	stats          *types.CacheStats
	statsMutex     sync.RWMutex
	cleanupTicker  *time.Ticker
	stopCleanup    chan bool
	bufferItems    int64
	numCounters    int64
	costMaximum    int64
	cleanupEnabled bool
}

// AdapterConfig 本地内存缓存适配器配置
type AdapterConfig struct {
	Name                    string
	Prefix                  string
	DefaultTTL              time.Duration
	BufferItems             int64
	MaxCost                 int64 // 最大内存使用量（以字节为单位）
	NumCounters             int64 // 计数器数量，通常是最大元素数量的10倍
	ShardCount              int   // 分片数量，默认256
	CleanupInterval         time.Duration
	EnableBackgroundCleanup bool
}

// NewAdapter 创建新的本地内存缓存适配器
func NewAdapter(config *AdapterConfig) (*Adapter, error) {
	if config == nil {
		config = &AdapterConfig{}
	}

	// 设置默认值
	if config.Name == "" {
		config.Name = "local"
	}
	if config.DefaultTTL <= 0 {
		config.DefaultTTL = defaultTTL
	}
	if config.CleanupInterval <= 0 {
		config.CleanupInterval = defaultCleanInterval
	}
	if config.BufferItems <= 0 {
		config.BufferItems = 64
	}
	if config.MaxCost <= 0 {
		config.MaxCost = 100 * 1024 * 1024 // 默认100MB
	}
	if config.NumCounters <= 0 {
		config.NumCounters = 1000000 // 默认100万个计数器
	}
	if config.ShardCount <= 0 {
		config.ShardCount = defaultShardCount
	}

	// 创建ristretto缓存
	ristrettoConfig := &ristretto.Config{
		NumCounters: config.NumCounters, // 计数器数量
		MaxCost:     config.MaxCost,     // 最大内存使用量
		BufferItems: config.BufferItems, // 写入缓冲区大小
		Metrics:     true,               // 启用指标
		KeyToHash: func(key interface{}) (uint64, uint64) {
			// 使用我们自己的KeyToHash函数
			return KeyToHash(key)
		},
		Cost: func(value interface{}) int64 {
			// 默认以字节长度为成本，对于[]byte类型特别处理
			if bytes, ok := value.([]byte); ok {
				return int64(len(bytes))
			}
			return 1
		},
	}

	cache, err := ristretto.NewCache(ristrettoConfig)
	if err != nil {
		return nil, err
	}

	adapter := &Adapter{
		name:           config.Name,
		prefix:         config.Prefix,
		defaultTTL:     config.DefaultTTL,
		cache:          cache,
		stats:          &types.CacheStats{StartTime: time.Now()},
		bufferItems:    config.BufferItems,
		numCounters:    config.NumCounters,
		costMaximum:    config.MaxCost,
		cleanupEnabled: config.EnableBackgroundCleanup,
	}

	// 启动后台清理任务
	if config.EnableBackgroundCleanup {
		adapter.startCleanup(config.CleanupInterval)
	}

	return adapter, nil
}

// Get 从缓存中获取值
func (a *Adapter) Get(ctx context.Context, key string) ([]byte, error) {
	prefixedKey := a.KeyWithPrefix(key)

	value, found := a.cache.Get(prefixedKey)
	if !found {
		atomic.AddInt64(&a.stats.Misses, 1)
		return nil, types.ErrNotFound
	}

	// 类型断言
	data, ok := value.([]byte)
	if !ok {
		atomic.AddInt64(&a.stats.Misses, 1)
		return nil, types.ErrInvalidValue
	}

	atomic.AddInt64(&a.stats.Hits, 1)
	atomic.AddInt64(&a.stats.BytesRead, int64(len(data)))

	return data, nil
}

// Set 设置缓存值
func (a *Adapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	prefixedKey := a.KeyWithPrefix(key)

	if expiration <= 0 {
		expiration = a.defaultTTL
	}

	// 设置值，带TTL
	success := a.cache.SetWithTTL(prefixedKey, value, int64(len(value)), expiration)
	if !success {
		return types.ErrInvalidValue
	}

	// 确保立即写入而不是缓冲
	a.cache.Wait()

	atomic.AddInt64(&a.stats.Sets, 1)
	atomic.AddInt64(&a.stats.BytesWritten, int64(len(value)))

	return nil
}

// Delete 删除缓存值
func (a *Adapter) Delete(ctx context.Context, key string) error {
	prefixedKey := a.KeyWithPrefix(key)
	a.cache.Del(prefixedKey)
	atomic.AddInt64(&a.stats.Deletes, 1)
	return nil
}

// Clear 清空缓存
func (a *Adapter) Clear(ctx context.Context) error {
	// Ristretto没有直接的Clear方法，创建一个新的缓存实例
	ristrettoConfig := &ristretto.Config{
		NumCounters: a.numCounters,
		MaxCost:     a.costMaximum,
		BufferItems: a.bufferItems,
		Metrics:     true,
	}

	cache, err := ristretto.NewCache(ristrettoConfig)
	if err != nil {
		return err
	}

	// 替换缓存实例
	oldCache := a.cache
	a.cache = cache

	// 关闭旧缓存
	oldCache.Close()

	atomic.AddInt64(&a.stats.Clears, 1)
	return nil
}

// Close 关闭缓存
func (a *Adapter) Close() error {
	// 停止清理任务
	if a.cleanupEnabled && a.stopCleanup != nil {
		a.stopCleanup <- true
		close(a.stopCleanup)
	}

	// 关闭缓存
	a.cache.Close()
	return nil
}

// Stats 获取缓存统计信息
func (a *Adapter) Stats() *types.CacheStats {
	a.statsMutex.RLock()
	defer a.statsMutex.RUnlock()

	// 克隆统计信息，避免并发修改
	stats := &types.CacheStats{
		Hits:         atomic.LoadInt64(&a.stats.Hits),
		Misses:       atomic.LoadInt64(&a.stats.Misses),
		BytesRead:    atomic.LoadInt64(&a.stats.BytesRead),
		BytesWritten: atomic.LoadInt64(&a.stats.BytesWritten),
		Sets:         atomic.LoadInt64(&a.stats.Sets),
		Deletes:      atomic.LoadInt64(&a.stats.Deletes),
		Clears:       atomic.LoadInt64(&a.stats.Clears),
		StartTime:    a.stats.StartTime,
		Uptime:       time.Since(a.stats.StartTime),
	}

	// 计算命中率
	totalOps := stats.Hits + stats.Misses
	if totalOps > 0 {
		stats.HitRate = float64(stats.Hits) / float64(totalOps)
	}

	// 从Ristretto获取额外指标
	metrics := a.cache.Metrics
	stats.EvictionCount = int64(metrics.KeysEvicted())
	stats.ItemCount = int64(metrics.KeysAdded() - metrics.KeysEvicted())
	stats.Size = int64(metrics.CostAdded() - metrics.CostEvicted())

	return stats
}

// Name 获取适配器名称
func (a *Adapter) Name() string {
	return a.name
}

// Type 获取适配器类型
func (a *Adapter) Type() string {
	return "local"
}

// KeyWithPrefix 为键添加前缀
func (a *Adapter) KeyWithPrefix(key string) string {
	if a.prefix == "" {
		return key
	}
	return a.prefix + ":" + key
}

// Exists 检查键是否存在
func (a *Adapter) Exists(ctx context.Context, key string) (bool, error) {
	prefixedKey := a.KeyWithPrefix(key)
	_, found := a.cache.Get(prefixedKey)
	return found, nil
}

// MGet 批量获取多个键的值
func (a *Adapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	result := make(map[string][]byte, len(keys))
	for _, key := range keys {
		value, err := a.Get(ctx, key)
		if err == nil {
			result[key] = value
		}
	}
	return result, nil
}

// MSet 批量设置多个键值对
func (a *Adapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	for key, value := range items {
		err := a.Set(ctx, key, value, expiration)
		if err != nil {
			return err
		}
	}
	return nil
}

// Increment 递增计数器
func (a *Adapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	prefixedKey := a.KeyWithPrefix(key)

	// 尝试获取当前值
	currentVal, found := a.cache.Get(prefixedKey)

	var newValue int64
	if !found {
		// 键不存在，使用delta作为初始值
		newValue = delta
	} else {
		// 键存在，进行类型转换
		var currentInt int64
		if byteVal, ok := currentVal.([]byte); ok {
			// 尝试从字节数组转换
			if len(byteVal) == 8 {
				currentInt = int64(byteVal[0]) | int64(byteVal[1])<<8 | int64(byteVal[2])<<16 | int64(byteVal[3])<<24 |
					int64(byteVal[4])<<32 | int64(byteVal[5])<<40 | int64(byteVal[6])<<48 | int64(byteVal[7])<<56
			} else {
				currentInt = 0
			}
		} else if intVal, ok := currentVal.(int64); ok {
			// 直接使用int64值
			currentInt = intVal
		} else {
			// 未知类型，从0开始
			currentInt = 0
		}

		// 计算新值
		newValue = currentInt + delta
	}

	// 将int64转换为字节数组
	byteVal := make([]byte, 8)
	byteVal[0] = byte(newValue)
	byteVal[1] = byte(newValue >> 8)
	byteVal[2] = byte(newValue >> 16)
	byteVal[3] = byte(newValue >> 24)
	byteVal[4] = byte(newValue >> 32)
	byteVal[5] = byte(newValue >> 40)
	byteVal[6] = byte(newValue >> 48)
	byteVal[7] = byte(newValue >> 56)

	// 设置新值
	success := a.cache.SetWithTTL(prefixedKey, byteVal, 8, a.defaultTTL)
	if !success {
		return 0, types.ErrInvalidValue
	}

	// 确保立即写入
	a.cache.Wait()

	atomic.AddInt64(&a.stats.Sets, 1)
	return newValue, nil
}

// Decrement 递减计数器
func (a *Adapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return a.Increment(ctx, key, -delta)
}

// Expire 设置过期时间
func (a *Adapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	prefixedKey := a.KeyWithPrefix(key)

	// 获取当前值
	value, found := a.cache.Get(prefixedKey)
	if !found {
		return types.ErrNotFound
	}

	// 重新设置值和TTL
	success := a.cache.SetWithTTL(prefixedKey, value, 0, expiration)
	if !success {
		return types.ErrInvalidValue
	}

	// 确保立即写入
	a.cache.Wait()

	return nil
}

// TTL 获取键的剩余生存时间
func (a *Adapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	// Ristretto不支持获取TTL
	return -1, nil
}

// Ping 检查缓存可用性
func (a *Adapter) Ping(ctx context.Context) error {
	return nil // 本地缓存总是可用
}

// 启动后台清理任务
func (a *Adapter) startCleanup(interval time.Duration) {
	a.cleanupTicker = time.NewTicker(interval)
	a.stopCleanup = make(chan bool)

	go func() {
		for {
			select {
			case <-a.cleanupTicker.C:
				// Ristretto自动处理过期，这里不需要额外操作
			case <-a.stopCleanup:
				a.cleanupTicker.Stop()
				return
			}
		}
	}()
}
