package users

import (
	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreateSearchHistoryRequest 创建搜索历史请求
type CreateSearchHistoryRequest struct {
	UserID      string `json:"userId"`
	Query       string `json:"query" validate:"required"`
	SearchType  string `json:"searchType"`
	ResultCount int    `json:"resultCount"`
	Device      string `json:"device"`
	IP          string `json:"ip"`
}

// UpdateSearchClickRequest 更新搜索点击请求
type UpdateSearchClickRequest struct {
	HasClick         bool   `json:"hasClick"`
	ClickContentType string `json:"clickContentType"`
	ClickContentID   string `json:"clickContentId"`
	ClickPosition    int    `json:"clickPosition"`
}

// UserSearchHistoryService 用户搜索历史服务接口
type UserSearchHistoryService interface {
	base.IExtendedService[users.UserSearchHistory]
}

// userSearchHistoryService 用户搜索历史服务实现
type userSearchHistoryService struct {
	*base.ExtendedService[users.UserSearchHistory]
	searchHistoryRepo repo.UserSearchHistoryRepository
}

// NewUserSearchHistoryService 创建用户搜索历史服务实例
func NewUserSearchHistoryService(
	searchHistoryRepo repo.UserSearchHistoryRepository,
) UserSearchHistoryService {
	return &userSearchHistoryService{
		ExtendedService:   base.NewExtendedService[users.UserSearchHistory](searchHistoryRepo, "user_search_history"),
		searchHistoryRepo: searchHistoryRepo,
	}
}
