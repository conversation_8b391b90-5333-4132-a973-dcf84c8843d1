package videos

import (
	"github.com/guregu/null/v6"

	"frontapi/internal/models/videos"
	repo "frontapi/internal/repository/videos"
	"frontapi/internal/service/base"
)

// CreateCommentRequest 创建评论请求
type CreateCommentRequest struct {
	Content      string      `json:"content" validate:"required"`
	UserID       string      `json:"userID" validate:"required"`
	UserNickname string      `json:"userNickname"`
	UserAvatar   string      `json:"userAvatar"`
	UserType     uint8       `json:"userType"`
	ParentID     null.String `json:"parentID"`
	ReplyToID    null.String `json:"replyToID"`
	ReplyToUser  null.String `json:"replyToUser"`
	VideoID      string      `json:"videoID" validate:"required"`
}

// UpdateCommentRequest 更新评论请求
type UpdateCommentRequest struct {
	Content string `json:"content" validate:"required"`
	Status  int8   `json:"status"`
}

// VideoCommentService 视频评论服务接口
type VideoCommentService interface {
	base.IExtendedService[videos.VideoComment]
}

// videoCommentService 视频评论服务实现
type videoCommentService struct {
	*base.ExtendedService[videos.VideoComment]
	commentRepo repo.VideoCommentRepository
	videoRepo   repo.VideoRepository
}

// NewVideoCommentService 创建视频评论服务实例
func NewVideoCommentService(commentRepo repo.VideoCommentRepository, videoRepo repo.VideoRepository) VideoCommentService {
	return &videoCommentService{
		ExtendedService: base.NewExtendedService[videos.VideoComment](commentRepo, "video_comment"),
		commentRepo:     commentRepo,
		videoRepo:       videoRepo,
	}
}
