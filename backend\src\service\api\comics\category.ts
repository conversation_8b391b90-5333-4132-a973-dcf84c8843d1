import { request } from "../../request";
import type {
  ComicsCategory,
  ComicsParams,
  CreateComicsCategoryRequest,
  UpdateComicsCategoryRequest
} from '@/types/comics';
import type { ApiResponse } from '@/types/https';

/**
 * 获取漫画分类列表
 * @param params 查询参数
 */
export function getComicsCategoryList(params: ComicsParams) {
  return request<ApiResponse<ComicsCategory[]>>({
    url: '/comics/category/all',
    method: 'post',
    data: params
  });
}

/**
 * 获取所有漫画分类（不分页）
 */
export function getAllComicsCategories() {
  return request<ApiResponse<ComicsCategory[]>>({
    url: '/comics/category/all',
    method: 'post',
    data: { data: { status: 1 } } // 假设 status 为 1 表示可用
  });

}

/**
 * 获取漫画分类详情
 * @param id 分类ID
 */
export function getComicsCategoryDetail(id: string) {
  return request<ApiResponse<ComicsCategory>>({
    url: `/comics/category/detail/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });

}

/**
 * 创建漫画分类
 * @param data 分类数据
 */
export function createComicsCategory(params: { data: CreateComicsCategoryRequest }) {
  return request<ApiResponse<any>>({
    url: '/comics/category/add',
    method: 'post',
    data: params
  })
}

/**
 * 更新漫画分类
 * @param id 分类ID
 * @param data 分类数据
 */
export function updateComicsCategory(id: string, params: { data: UpdateComicsCategoryRequest }) {
  return request<ApiResponse<any>>({
    url: `/comics/category/update/${id}`,
    method: 'post',
    data: params
  });
}

/**
 * 删除漫画分类
 * @param id 分类ID
 */
export function deleteComicsCategory(id: string) {
  return request<ApiResponse<any>>({
    url: `/comics/category/delete/${id}`,
    method: 'post',
    data: { data: { "id": id } }
  });
}

/**
 * 更新漫画分类状态
 * @param id 分类ID
 * @param status 状态
 */
export function updateComicsCategoryStatus(id: string, status: number) {
  return request<ApiResponse<any>>({
    url: `/comics/category/update-status`,
    method: 'post',
    data: { data: { "id": id, "status": status } }
  });
}
