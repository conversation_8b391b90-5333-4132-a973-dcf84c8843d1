package books

import (
	"context"
	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// ReadHistoryRepository 电子书阅读历史仓库接口
type ReadHistoryRepository interface {
	base.ExtendedRepository[books.BookReadHistory]
	FindByUserAndBookAndChapter(ctx context.Context, userID, bookID, chapterID string) (*books.BookReadHistory, error)
	ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*books.BookReadHistory, int64, error)
	UpdatePosition(ctx context.Context, userID, bookID, chapterID string, position int) error
}

type readHistoryRepository struct {
	base.ExtendedRepository[books.BookReadHistory]
}

// NewReadHistoryRepository 创建电子书阅读历史仓库实例
func NewReadHistoryRepository(db *gorm.DB) ReadHistoryRepository {
	return &readHistoryRepository{
		ExtendedRepository: base.NewExtendedRepository[books.BookReadHistory](db),
	}
}

// FindByUserAndBookAndChapter 根据用户ID、电子书ID和章节ID查找阅读历史
func (r *readHistoryRepository) FindByUserAndBookAndChapter(ctx context.Context, userID, bookID, chapterID string) (*books.BookReadHistory, error) {
	conditions := map[string]interface{}{
		"user_id":    userID,
		"book_id":    bookID,
		"chapter_id": chapterID,
	}
	return r.FindOneByCondition(ctx, conditions, "")
}

// ListByUser 获取用户阅读历史列表
func (r *readHistoryRepository) ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*books.BookReadHistory, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "updated_at DESC", page, pageSize, "user_id = ?", userID)
}

// UpdatePosition 更新阅读位置
func (r *readHistoryRepository) UpdatePosition(ctx context.Context, userID, bookID, chapterID string, position int) error {
	return r.GetDBWithContext(ctx).Model(&books.BookReadHistory{}).
		Where("user_id = ? AND book_id = ? AND chapter_id = ?", userID, bookID, chapterID).
		Update("position", position).Error
}
