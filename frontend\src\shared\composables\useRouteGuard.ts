import { ref, computed, watch } from 'vue'
import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useUserStore } from '@/core/stores/user'
import { useAuthStore } from '@/core/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useErrorHandler } from './useErrorHandler'

// 守卫类型
export enum GuardType {
  AUTH = 'auth', // 认证守卫
  PERMISSION = 'permission', // 权限守卫
  ROLE = 'role', // 角色守卫
  SUBSCRIPTION = 'subscription', // 订阅守卫
  MAINTENANCE = 'maintenance', // 维护模式守卫
  RATE_LIMIT = 'rate_limit', // 频率限制守卫
  GEO = 'geo', // 地理位置守卫
  DEVICE = 'device', // 设备类型守卫
  TIME = 'time', // 时间段守卫
  CUSTOM = 'custom' // 自定义守卫
}

// 守卫结果
export interface GuardResult {
  allowed: boolean
  redirect?: string
  message?: string
  data?: any
}

// 守卫配置
export interface GuardConfig {
  type: GuardType
  name: string
  description?: string
  enabled: boolean
  priority: number // 执行优先级，数字越小优先级越高
  routes?: string[] // 应用的路由，为空表示全局
  excludeRoutes?: string[] // 排除的路由
  condition?: (to: RouteLocationNormalized, from: RouteLocationNormalized) => boolean
  handler: (to: RouteLocationNormalized, from: RouteLocationNormalized) => Promise<GuardResult> | GuardResult
  onSuccess?: (to: RouteLocationNormalized, from: RouteLocationNormalized, result: GuardResult) => void
  onFailure?: (to: RouteLocationNormalized, from: RouteLocationNormalized, result: GuardResult) => void
}

// 守卫执行结果
export interface GuardExecutionResult {
  guard: GuardConfig
  result: GuardResult
  executionTime: number
  error?: Error
}

// 守卫统计
export interface GuardStats {
  totalExecutions: number
  successCount: number
  failureCount: number
  averageExecutionTime: number
  lastExecution?: Date
  errorCount: number
}

/**
 * 路由守卫 Composable
 * 提供灵活的路由守卫系统
 */
export function useRouteGuard(router: Router) {
  const userStore = useUserStore()
  const authStore = useAuthStore()
  const { logError } = useErrorHandler()
  
  const guards = ref<Map<string, GuardConfig>>(new Map())
  const guardStats = ref<Map<string, GuardStats>>(new Map())
  const isGuardEnabled = ref(true)
  const debugMode = ref(false)
  
  // 当前执行的守卫
  const currentGuards = ref<GuardExecutionResult[]>([])
  
  // 守卫执行历史
  const executionHistory = ref<{
    timestamp: Date
    route: string
    guards: GuardExecutionResult[]
    totalTime: number
  }[]>([])

  // 计算属性
  const enabledGuards = computed(() => {
    return Array.from(guards.value.values())
      .filter(guard => guard.enabled)
      .sort((a, b) => a.priority - b.priority)
  })

  const guardCount = computed(() => guards.value.size)
  const enabledGuardCount = computed(() => enabledGuards.value.length)

  // 内置守卫
  const builtInGuards: GuardConfig[] = [
    {
      type: GuardType.AUTH,
      name: 'authentication',
      description: '用户认证守卫',
      enabled: true,
      priority: 1,
      condition: (to) => to.meta?.requiresAuth === true,
      handler: async (to) => {
        if (!authStore.isAuthenticated) {
          return {
            allowed: false,
            redirect: `/login?redirect=${encodeURIComponent(to.fullPath)}`,
            message: '请先登录'
          }
        }
        return { allowed: true }
      }
    },
    {
      type: GuardType.PERMISSION,
      name: 'permission',
      description: '权限检查守卫',
      enabled: true,
      priority: 2,
      condition: (to) => !!to.meta?.permissions,
      handler: async (to) => {
        const requiredPermissions = to.meta?.permissions as string[]
        if (!requiredPermissions || requiredPermissions.length === 0) {
          return { allowed: true }
        }
        
        const hasPermission = requiredPermissions.every(permission => 
          userStore.hasPermission(permission)
        )
        
        if (!hasPermission) {
          return {
            allowed: false,
            redirect: '/403',
            message: '权限不足'
          }
        }
        
        return { allowed: true }
      }
    },
    {
      type: GuardType.ROLE,
      name: 'role',
      description: '角色检查守卫',
      enabled: true,
      priority: 3,
      condition: (to) => !!to.meta?.roles,
      handler: async (to) => {
        const requiredRoles = to.meta?.roles as string[]
        if (!requiredRoles || requiredRoles.length === 0) {
          return { allowed: true }
        }
        
        const hasRole = requiredRoles.some(role => 
          userStore.hasRole(role)
        )
        
        if (!hasRole) {
          return {
            allowed: false,
            redirect: '/403',
            message: '角色权限不足'
          }
        }
        
        return { allowed: true }
      }
    },
    {
      type: GuardType.SUBSCRIPTION,
      name: 'subscription',
      description: '订阅状态守卫',
      enabled: true,
      priority: 4,
      condition: (to) => to.meta?.requiresSubscription === true,
      handler: async (to) => {
        if (!userStore.hasActiveSubscription) {
          return {
            allowed: false,
            redirect: '/subscription',
            message: '需要有效订阅'
          }
        }
        return { allowed: true }
      }
    },
    {
      type: GuardType.MAINTENANCE,
      name: 'maintenance',
      description: '维护模式守卫',
      enabled: true,
      priority: 0,
      excludeRoutes: ['/maintenance', '/login', '/logout'],
      handler: async (to) => {
        // 检查系统是否处于维护模式
        const isMaintenanceMode = await checkMaintenanceMode()
        if (isMaintenanceMode && !userStore.isAdmin) {
          return {
            allowed: false,
            redirect: '/maintenance',
            message: '系统正在维护中'
          }
        }
        return { allowed: true }
      }
    }
  ]

  // 检查维护模式
  const checkMaintenanceMode = async (): Promise<boolean> => {
    try {
      // 这里可以调用API检查维护状态
      // const response = await api.system.getMaintenanceStatus()
      // return response.data.isMaintenanceMode
      return false // 默认不在维护模式
    } catch {
      return false
    }
  }

  // 注册守卫
  const registerGuard = (guard: GuardConfig): void => {
    guards.value.set(guard.name, guard)
    guardStats.value.set(guard.name, {
      totalExecutions: 0,
      successCount: 0,
      failureCount: 0,
      averageExecutionTime: 0,
      errorCount: 0
    })
    
    if (debugMode.value) {
      console.log(`[RouteGuard] Registered guard: ${guard.name}`, guard)
    }
  }

  // 注销守卫
  const unregisterGuard = (name: string): boolean => {
    const removed = guards.value.delete(name)
    guardStats.value.delete(name)
    
    if (debugMode.value && removed) {
      console.log(`[RouteGuard] Unregistered guard: ${name}`)
    }
    
    return removed
  }

  // 启用/禁用守卫
  const toggleGuard = (name: string, enabled?: boolean): boolean => {
    const guard = guards.value.get(name)
    if (!guard) return false
    
    guard.enabled = enabled !== undefined ? enabled : !guard.enabled
    
    if (debugMode.value) {
      console.log(`[RouteGuard] Guard ${name} ${guard.enabled ? 'enabled' : 'disabled'}`)
    }
    
    return true
  }

  // 更新守卫统计
  const updateGuardStats = (name: string, result: GuardExecutionResult): void => {
    const stats = guardStats.value.get(name)
    if (!stats) return
    
    stats.totalExecutions++
    stats.lastExecution = new Date()
    
    if (result.error) {
      stats.errorCount++
    } else if (result.result.allowed) {
      stats.successCount++
    } else {
      stats.failureCount++
    }
    
    // 更新平均执行时间
    stats.averageExecutionTime = (
      (stats.averageExecutionTime * (stats.totalExecutions - 1) + result.executionTime) / 
      stats.totalExecutions
    )
  }

  // 执行单个守卫
  const executeGuard = async (
    guard: GuardConfig,
    to: RouteLocationNormalized,
    from: RouteLocationNormalized
  ): Promise<GuardExecutionResult> => {
    const startTime = performance.now()
    
    try {
      // 检查条件
      if (guard.condition && !guard.condition(to, from)) {
        return {
          guard,
          result: { allowed: true },
          executionTime: performance.now() - startTime
        }
      }
      
      // 检查路由匹配
      if (guard.routes && !guard.routes.includes(to.name as string)) {
        return {
          guard,
          result: { allowed: true },
          executionTime: performance.now() - startTime
        }
      }
      
      // 检查排除路由
      if (guard.excludeRoutes && guard.excludeRoutes.includes(to.name as string)) {
        return {
          guard,
          result: { allowed: true },
          executionTime: performance.now() - startTime
        }
      }
      
      // 执行守卫处理器
      const result = await guard.handler(to, from)
      const executionTime = performance.now() - startTime
      
      const executionResult: GuardExecutionResult = {
        guard,
        result,
        executionTime
      }
      
      // 调用成功/失败回调
      if (result.allowed && guard.onSuccess) {
        guard.onSuccess(to, from, result)
      } else if (!result.allowed && guard.onFailure) {
        guard.onFailure(to, from, result)
      }
      
      return executionResult
      
    } catch (error) {
      const executionTime = performance.now() - startTime
      const executionResult: GuardExecutionResult = {
        guard,
        result: { allowed: false, message: '守卫执行错误' },
        executionTime,
        error: error as Error
      }
      
      logError(error as Error, {
        context: 'RouteGuard',
        guard: guard.name,
        route: to.path
      })
      
      return executionResult
    }
  }

  // 执行所有守卫
  const executeGuards = async (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized
  ): Promise<GuardExecutionResult[]> => {
    if (!isGuardEnabled.value) {
      return []
    }
    
    const results: GuardExecutionResult[] = []
    const startTime = performance.now()
    
    for (const guard of enabledGuards.value) {
      const result = await executeGuard(guard, to, from)
      results.push(result)
      
      // 更新统计
      updateGuardStats(guard.name, result)
      
      // 如果守卫不允许通过，停止执行后续守卫
      if (!result.result.allowed) {
        break
      }
    }
    
    const totalTime = performance.now() - startTime
    
    // 记录执行历史
    executionHistory.value.unshift({
      timestamp: new Date(),
      route: to.path,
      guards: results,
      totalTime
    })
    
    // 限制历史记录数量
    if (executionHistory.value.length > 100) {
      executionHistory.value = executionHistory.value.slice(0, 100)
    }
    
    if (debugMode.value) {
      console.log(`[RouteGuard] Executed ${results.length} guards in ${totalTime.toFixed(2)}ms`, results)
    }
    
    return results
  }

  // 安装路由守卫
  const installGuards = (): void => {
    // 注册内置守卫
    builtInGuards.forEach(guard => registerGuard(guard))
    
    // 安装全局前置守卫
    router.beforeEach(async (to, from, next) => {
      try {
        const results = await executeGuards(to, from)
        currentGuards.value = results
        
        // 检查是否有守卫阻止导航
        const failedGuard = results.find(result => !result.result.allowed)
        
        if (failedGuard) {
          const { result } = failedGuard
          
          // 显示消息
          if (result.message) {
            ElMessage.warning(result.message)
          }
          
          // 重定向
          if (result.redirect) {
            next(result.redirect)
          } else {
            next(false)
          }
        } else {
          next()
        }
        
      } catch (error) {
        logError(error as Error, {
          context: 'RouteGuard',
          route: to.path
        })
        
        ElMessage.error('路由守卫执行失败')
        next(false)
      }
    })
    
    // 安装全局后置钩子
    router.afterEach((to, from) => {
      // 清理当前守卫状态
      setTimeout(() => {
        currentGuards.value = []
      }, 100)
    })
  }

  // 获取守卫信息
  const getGuard = (name: string): GuardConfig | undefined => {
    return guards.value.get(name)
  }

  // 获取守卫统计
  const getGuardStats = (name: string): GuardStats | undefined => {
    return guardStats.value.get(name)
  }

  // 获取所有守卫
  const getAllGuards = (): GuardConfig[] => {
    return Array.from(guards.value.values())
  }

  // 获取所有统计
  const getAllStats = (): Map<string, GuardStats> => {
    return new Map(guardStats.value)
  }

  // 清理统计
  const clearStats = (): void => {
    guardStats.value.forEach(stats => {
      stats.totalExecutions = 0
      stats.successCount = 0
      stats.failureCount = 0
      stats.averageExecutionTime = 0
      stats.errorCount = 0
      stats.lastExecution = undefined
    })
    
    executionHistory.value = []
  }

  // 导出守卫配置
  const exportGuardConfig = (): string => {
    const config = {
      guards: Array.from(guards.value.entries()),
      stats: Array.from(guardStats.value.entries()),
      settings: {
        enabled: isGuardEnabled.value,
        debugMode: debugMode.value
      }
    }
    
    return JSON.stringify(config, null, 2)
  }

  // 导入守卫配置
  const importGuardConfig = (configJson: string): boolean => {
    try {
      const config = JSON.parse(configJson)
      
      // 清空现有守卫
      guards.value.clear()
      guardStats.value.clear()
      
      // 导入守卫
      config.guards.forEach(([name, guard]: [string, GuardConfig]) => {
        guards.value.set(name, guard)
      })
      
      // 导入统计
      config.stats.forEach(([name, stats]: [string, GuardStats]) => {
        guardStats.value.set(name, stats)
      })
      
      // 导入设置
      if (config.settings) {
        isGuardEnabled.value = config.settings.enabled
        debugMode.value = config.settings.debugMode
      }
      
      return true
    } catch (error) {
      logError(error as Error, { context: 'RouteGuard', action: 'importConfig' })
      return false
    }
  }

  return {
    // 响应式数据
    guards: readonly(guards),
    guardStats: readonly(guardStats),
    currentGuards: readonly(currentGuards),
    executionHistory: readonly(executionHistory),
    isGuardEnabled,
    debugMode,
    
    // 计算属性
    enabledGuards,
    guardCount,
    enabledGuardCount,
    
    // 守卫管理
    registerGuard,
    unregisterGuard,
    toggleGuard,
    getGuard,
    getAllGuards,
    
    // 统计管理
    getGuardStats,
    getAllStats,
    clearStats,
    
    // 执行控制
    executeGuard,
    executeGuards,
    installGuards,
    
    // 配置管理
    exportGuardConfig,
    importGuardConfig
  }
}

/**
 * 权限守卫 Composable
 * 专门用于权限检查的简化版本
 */
export function usePermissionGuard() {
  const userStore = useUserStore()
  const authStore = useAuthStore()
  
  // 检查是否已登录
  const checkAuth = (): boolean => {
    return authStore.isAuthenticated
  }
  
  // 检查权限
  const checkPermission = (permission: string | string[]): boolean => {
    if (!authStore.isAuthenticated) return false
    
    if (Array.isArray(permission)) {
      return permission.every(p => userStore.hasPermission(p))
    }
    
    return userStore.hasPermission(permission)
  }
  
  // 检查角色
  const checkRole = (role: string | string[]): boolean => {
    if (!authStore.isAuthenticated) return false
    
    if (Array.isArray(role)) {
      return role.some(r => userStore.hasRole(r))
    }
    
    return userStore.hasRole(role)
  }
  
  // 检查订阅状态
  const checkSubscription = (): boolean => {
    return userStore.hasActiveSubscription
  }
  
  // 权限守卫装饰器
  const withPermission = <T extends (...args: any[]) => any>(
    fn: T,
    permission: string | string[],
    fallback?: () => void
  ): T => {
    return ((...args: any[]) => {
      if (checkPermission(permission)) {
        return fn(...args)
      } else {
        if (fallback) {
          fallback()
        } else {
          ElMessage.warning('权限不足')
        }
      }
    }) as T
  }
  
  // 角色守卫装饰器
  const withRole = <T extends (...args: any[]) => any>(
    fn: T,
    role: string | string[],
    fallback?: () => void
  ): T => {
    return ((...args: any[]) => {
      if (checkRole(role)) {
        return fn(...args)
      } else {
        if (fallback) {
          fallback()
        } else {
          ElMessage.warning('角色权限不足')
        }
      }
    }) as T
  }
  
  return {
    checkAuth,
    checkPermission,
    checkRole,
    checkSubscription,
    withPermission,
    withRole
  }
}