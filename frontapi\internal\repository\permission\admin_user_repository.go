package permission

import (
	"context"
	"errors"

	"gorm.io/gorm"

	permissionModels "frontapi/internal/models/permission"
	"frontapi/internal/repository/base/extint"
)

// AdminUserRepository 管理员用户仓库接口
type AdminUserRepository interface {
	extint.IntBaseRepository[permissionModels.AdminUser] // 继承基础仓库接口

	// 特有的业务方法
	GetByUsername(ctx context.Context, username string) (*permissionModels.AdminUser, error)
	GetByEmail(ctx context.Context, email string) (*permissionModels.AdminUser, error)
	ExistsUsername(ctx context.Context, username string) (bool, error)
	ExistsEmail(ctx context.Context, email string) (bool, error)
	ExistsPhone(ctx context.Context, phone string) (bool, error)
	UpdatePassword(ctx context.Context, id int, password, salt string) error
	UpdateLastLogin(ctx context.Context, id int, ip string) error
	GetByDept(ctx context.Context, deptID int) ([]*permissionModels.AdminUser, error)
	GetSuperAdmins(ctx context.Context) ([]*permissionModels.AdminUser, error)
	SearchUsers(ctx context.Context, keyword string, page, pageSize int) ([]*permissionModels.AdminUser, int64, error)
}

// adminUserRepository 管理员用户仓库实现
type adminUserRepository struct {
	extint.IntBaseRepository[permissionModels.AdminUser] // 嵌入基础仓库
	db                                                   *gorm.DB
}

// NewAdminUserRepository 创建管理员用户仓库实例
func NewAdminUserRepository(db *gorm.DB) AdminUserRepository {
	baseRepo := extint.NewIntBaseRepository[permissionModels.AdminUser](db)
	return &adminUserRepository{
		IntBaseRepository: baseRepo,
		db:                db,
	}
}

// GetByUsername 根据用户名获取用户
func (r *adminUserRepository) GetByUsername(ctx context.Context, username string) (*permissionModels.AdminUser, error) {
	if username == "" {
		return nil, errors.New("用户名不能为空")
	}

	var user permissionModels.AdminUser
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// GetByEmail 根据邮箱获取用户
func (r *adminUserRepository) GetByEmail(ctx context.Context, email string) (*permissionModels.AdminUser, error) {
	if email == "" {
		return nil, errors.New("邮箱不能为空")
	}

	var user permissionModels.AdminUser
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// ExistsUsername 检查用户名是否存在
func (r *adminUserRepository) ExistsUsername(ctx context.Context, username string) (bool, error) {
	if username == "" {
		return false, errors.New("用户名不能为空")
	}

	var count int64
	err := r.db.WithContext(ctx).Model(&permissionModels.AdminUser{}).
		Where("username = ?", username).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// ExistsEmail 检查邮箱是否存在
func (r *adminUserRepository) ExistsEmail(ctx context.Context, email string) (bool, error) {
	if email == "" {
		return false, nil
	}

	var count int64
	err := r.db.WithContext(ctx).Model(&permissionModels.AdminUser{}).
		Where("email = ?", email).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// ExistsPhone 检查手机号是否存在
func (r *adminUserRepository) ExistsPhone(ctx context.Context, phone string) (bool, error) {
	if phone == "" {
		return false, nil
	}

	var count int64
	err := r.db.WithContext(ctx).Model(&permissionModels.AdminUser{}).
		Where("phone = ?", phone).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdatePassword 更新用户密码
func (r *adminUserRepository) UpdatePassword(ctx context.Context, id int, password, salt string) error {
	if id <= 0 {
		return errors.New("用户ID无效")
	}
	if password == "" {
		return errors.New("密码不能为空")
	}

	updates := map[string]interface{}{
		"password": password,
	}
	if salt != "" {
		updates["psalt"] = salt
	}

	return r.db.WithContext(ctx).Model(&permissionModels.AdminUser{}).
		Where("id = ?", id).Updates(updates).Error
}

// UpdateLastLogin 更新最后登录信息
func (r *adminUserRepository) UpdateLastLogin(ctx context.Context, id int, ip string) error {
	if id <= 0 {
		return errors.New("用户ID无效")
	}

	updates := map[string]interface{}{
		"last_login_ip": ip,
		"last_login_at": gorm.Expr("NOW()"),
	}

	return r.db.WithContext(ctx).Model(&permissionModels.AdminUser{}).
		Where("id = ?", id).Updates(updates).Error
}

// GetByDept 根据部门ID获取用户列表
func (r *adminUserRepository) GetByDept(ctx context.Context, deptID int) ([]*permissionModels.AdminUser, error) {
	var users []*permissionModels.AdminUser
	err := r.db.WithContext(ctx).Where("dept_id = ? AND status = 1", deptID).
		Find(&users).Error
	return users, err
}

// GetSuperAdmins 获取所有超级管理员
func (r *adminUserRepository) GetSuperAdmins(ctx context.Context) ([]*permissionModels.AdminUser, error) {
	var users []*permissionModels.AdminUser
	err := r.db.WithContext(ctx).Where("is_super_admin = 1 AND status = 1").
		Find(&users).Error
	return users, err
}

// SearchUsers 搜索用户
func (r *adminUserRepository) SearchUsers(ctx context.Context, keyword string, page, pageSize int) ([]*permissionModels.AdminUser, int64, error) {
	var users []*permissionModels.AdminUser
	var total int64

	query := r.db.WithContext(ctx).Model(&permissionModels.AdminUser{})

	if keyword != "" {
		query = query.Where("username LIKE ? OR nickname LIKE ? OR email LIKE ? OR phone LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}
