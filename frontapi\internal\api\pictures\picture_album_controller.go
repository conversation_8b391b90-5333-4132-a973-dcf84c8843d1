package pictures

import (
	"frontapi/internal/api"
	"frontapi/internal/service/pictures"
	pictureTypings "frontapi/internal/typings/picture"

	"github.com/gofiber/fiber/v2"
)

// PictureAlbumController 图片专辑控制器
type PictureAlbumController struct {
	api.BaseController
	albumService   pictures.PictureAlbumService
	pictureService pictures.PictureService
}

// NewPictureAlbumController 创建图片专辑控制器
func NewPictureAlbumController(
	albumService pictures.PictureAlbumService,
	pictureService pictures.PictureService,
) *PictureAlbumController {
	return &PictureAlbumController{
		albumService:   albumService,
		pictureService: pictureService,
	}
}

// GetAlbumList 获取专辑列表
func (c *PictureAlbumController) GetAlbumList(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	categoryID := reqInfo.Get("category_id").GetString()

	// 构建查询条件
	condition := map[string]interface{}{
		"status":      1,
		"keyword":     keyword,
		"category_id": categoryID,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "view_count DESC,like_count DESC,picture_count DESC,sort_order ASC"
	}
	albums, total, err := c.albumService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := pictureTypings.ConvertPictureAlbumListResponse(albums, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// GetAlbumDetail 获取专辑详情
func (c *PictureAlbumController) GetAlbumDetail(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	albumID := reqInfo.Get("albumId").GetString()
	if albumID == "" {
		return c.BadRequest(ctx, "album ID is required", nil) // 专辑ID必填
	}

	album, err := c.albumService.GetByID(ctx.Context(), albumID, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	if album == nil || album.Status == 0 {
		return c.NotFound(ctx, "album not found") // 专辑不存在
	}

	response := pictureTypings.ConvertPictureAlbumDetail(album)
	return c.Success(ctx, response)
}

// GetAlbumPictures 获取专辑内的图片列表
func (c *PictureAlbumController) GetAlbumPictures(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	albumID := reqInfo.Get("albumId").GetString()
	if albumID == "" {
		return c.BadRequest(ctx, "album ID is required", nil) // 专辑ID必填
	}

	condition := map[string]interface{}{
		"status":   1,
		"album_id": albumID,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	pictures, total, err := c.pictureService.List(ctx.Context(), condition, orderBy, pageNo, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}

	response := pictureTypings.ConvertPictureListResponse(pictures, total, pageNo, pageSize)
	return c.Success(ctx, response)
}

// GetRecommendedAlbums 获取推荐专辑
func (c *PictureAlbumController) GetRecommendedAlbums(ctx *fiber.Ctx) error {
	reqInfo := c.GetRequestInfo(ctx)
	pageNo := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	userID := c.GetUserID(ctx)
	keyword := reqInfo.Get("keyword").GetString()
	albumId := reqInfo.Get("albumId").GetString()

	condition := map[string]interface{}{
		"status":  1,
		"keyword": keyword,
		"id <>":   albumId,
	}
	if userID != "" {
		condition["user_id"] = userID
		userService := c.GetUserService()
		user, err := userService.GetByID(ctx.Context(), userID, false)
		if err != nil {
			return c.InternalServerError(ctx, err.Error())
		}
		condition["nation"] = user.Nation
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "view_count DESC,like_count DESC,picture_count DESC,sort_order ASC"
	}

	albums, total, err := c.albumService.GetRecommendedAlbums(ctx.Context(), condition, orderBy, pageNo, pageSize)
	if err != nil {
		return c.InternalServerError(ctx, err.Error())
	}
	response := pictureTypings.ConvertPictureAlbumListResponse(albums, total, pageNo, pageSize)
	return c.Success(ctx, response)
}
