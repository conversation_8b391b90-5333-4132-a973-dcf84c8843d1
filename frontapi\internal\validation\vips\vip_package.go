package vips

type CreateVipPackageRequest struct {
	Name          string  `json:"name" validate:"required"`
	Code          string  `json:"code" validate:"required"`
	Price         float64 `json:"price" validate:"required"`
	OriginalPrice float64 `json:"original_price"`
	Duration      int     `json:"duration" validate:"required"`
	Description   string  `json:"description"`
	Benefits      string  `json:"benefits"`
	Icon          string  `json:"icon"`
	SortOrder     int     `json:"sort_order"`
	Status        int8    `json:"status"`
}

type UpdateVipPackageRequest struct {
	Name          string  `json:"name"`
	Code          string  `json:"code"`
	Price         float64 `json:"price"`
	OriginalPrice float64 `json:"original_price"`
	Duration      int     `json:"duration"`
	Description   string  `json:"description"`
	Benefits      string  `json:"benefits"`
	Icon          string  `json:"icon"`
	SortOrder     int     `json:"sort_order"`
	Status        int8    `json:"status"`
}
