# 扩展点赞服务使用示例

本文档展示如何使用扩展点赞服务 (ExtendedLikeService) 进行各种点赞操作。

## 基本配置

### 1. 创建服务实例

```go
package main

import (
    "context"
    "log"
    
    "github.com/go-redis/redis/v8"
    "gorm.io/gorm"
    
    "frontapi/internal/service/base/extlike"
    "frontapi/internal/service/base/extlike/mongodb"
    v2 "frontapi/internal/service/base/extlike/redis/v2"
)

func main() {
    // 初始化Redis客户端
    redisClient := redis.NewClient(&redis.Options{
        Addr:     "localhost:6379",
        Password: "",
        DB:       0,
    })
    
    // 初始化数据库连接
    var db *gorm.DB // 假设已经初始化
    
    // 创建服务配置
    config := &extlike.LikeServiceConfig{
        RedisConfig: &v2.LikeConfig{
            RedisClient: redisClient,
            KeyPrefix:   "video_like",
            Expiration:  3600, // 1小时
        },
        MongoConfig: &mongodb.LikeConfig{
            DB:             db,
            CollectionName: "video_likes",
        },
        Strategy: extlike.DualWrite, // 双写策略
    }
    
    // 创建扩展点赞服务
    likeService, err := extlike.NewExtendedLikeService(config, db)
    if err != nil {
        log.Fatal("Failed to create like service:", err)
    }
    
    ctx := context.Background()
    
    // 使用服务进行点赞操作
    err = likeService.LikeItem(ctx, "123", "video", "456")
    if err != nil {
        log.Printf("Failed to like item: %v", err)
    }
}
```

### 2. 使用服务管理器

```go
package main

import (
    "context"
    "log"
    
    "frontapi/internal/service/base/extlike"
)

func main() {
    // 创建服务管理器
    manager := extlike.NewLikeServiceManager()
    
    // 注册不同类型的服务
    videoService, _ := createVideoLikeService() // 假设已实现
    commentService, _ := createCommentLikeService() // 假设已实现
    
    manager.RegisterService("video", videoService)
    manager.RegisterService("comment", commentService)
    
    ctx := context.Background()
    
    // 获取并使用服务
    videoLikeService := manager.GetService("video")
    if videoLikeService != nil {
        err := videoLikeService.LikeItem(ctx, "user123", "video", "video456")
        if err != nil {
            log.Printf("Failed to like video: %v", err)
        }
    }
}
```

## 基本操作示例

### 1. 点赞和取消点赞

```go
func likeOperations(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    userID := "user123"
    itemType := "video"
    itemID := "video456"
    
    // 点赞
    err := service.LikeItem(ctx, userID, itemType, itemID)
    if err != nil {
        log.Printf("点赞失败: %v", err)
        return
    }
    log.Println("点赞成功")
    
    // 检查是否已点赞
    isLiked, err := service.IsLiked(ctx, userID, itemType, itemID)
    if err != nil {
        log.Printf("检查点赞状态失败: %v", err)
        return
    }
    log.Printf("点赞状态: %t", isLiked)
    
    // 获取点赞数
    count, err := service.GetLikeCount(ctx, itemType, itemID)
    if err != nil {
        log.Printf("获取点赞数失败: %v", err)
        return
    }
    log.Printf("点赞数: %d", count)
    
    // 取消点赞
    err = service.UnlikeItem(ctx, userID, itemType, itemID)
    if err != nil {
        log.Printf("取消点赞失败: %v", err)
        return
    }
    log.Println("取消点赞成功")
}
```

### 2. 批量操作

```go
func batchOperations(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    
    // 批量点赞操作
    operations := []*extlike.LikeOperation{
        {
            UserID:   "user123",
            ItemType: "video",
            ItemID:   "video1",
            Action:   "like",
        },
        {
            UserID:   "user123",
            ItemType: "video",
            ItemID:   "video2",
            Action:   "like",
        },
        {
            UserID:   "user123",
            ItemType: "video",
            ItemID:   "video3",
            Action:   "unlike",
        },
    }
    
    results, err := service.BatchLike(ctx, operations)
    if err != nil {
        log.Printf("批量操作失败: %v", err)
        return
    }
    
    for i, result := range results {
        log.Printf("操作 %d 结果: %t", i, result)
    }
    
    // 批量获取点赞状态
    items := []string{"video1", "video2", "video3"}
    statuses, err := service.BatchGetLikeStatus(ctx, "user123", "video", items)
    if err != nil {
        log.Printf("批量获取状态失败: %v", err)
        return
    }
    
    for itemID, status := range statuses {
        log.Printf("视频 %s 点赞状态: %t", itemID, status)
    }
    
    // 批量获取点赞数
    counts, err := service.BatchGetLikeCounts(ctx, "video", items)
    if err != nil {
        log.Printf("批量获取点赞数失败: %v", err)
        return
    }
    
    for itemID, count := range counts {
        log.Printf("视频 %s 点赞数: %d", itemID, count)
    }
}
```

### 3. 热门排行

```go
func hotRankingOperations(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    
    // 更新热门排行
    err := service.UpdateHotRank(ctx, "video", "video123", 100)
    if err != nil {
        log.Printf("更新热门排行失败: %v", err)
        return
    }
    
    // 获取热门项目
    hotItems, err := service.GetHotItems(ctx, "video", 10)
    if err != nil {
        log.Printf("获取热门项目失败: %v", err)
        return
    }
    
    log.Println("热门视频排行:")
    for i, item := range hotItems {
        log.Printf("%d. 视频ID: %s, 分数: %f, 更新时间: %v", 
            i+1, item.ItemID, item.Score, item.UpdatedAt)
    }
}
```

### 4. 查询操作

```go
func queryOperations(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    userID := "user123"
    itemType := "video"
    itemID := "video456"
    
    // 获取用户点赞的项目
    userLikes, err := service.GetUserLikes(ctx, userID, itemType, 20, 0)
    if err != nil {
        log.Printf("获取用户点赞失败: %v", err)
        return
    }
    
    log.Printf("用户 %s 点赞的视频: %v", userID, userLikes)
    
    // 获取项目的点赞用户
    likedUsers, err := service.GetItemLikedUsers(ctx, itemType, itemID, 20, 0)
    if err != nil {
        log.Printf("获取点赞用户失败: %v", err)
        return
    }
    
    log.Printf("视频 %s 的点赞用户: %v", itemID, likedUsers)
}
```

### 5. 历史和统计

```go
func historyAndStats(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    userID := "user123"
    itemType := "video"
    itemID := "video456"
    
    // 时间范围
    timeRange := extlike.TimeRange{
        Start: time.Now().AddDate(0, -1, 0), // 一个月前
        End:   time.Now(),
    }
    
    // 获取用户点赞历史
    userHistory, err := service.GetUserLikeHistory(ctx, userID, itemType, 50, 0)
    if err != nil {
        log.Printf("获取用户历史失败: %v", err)
        return
    }
    
    log.Printf("用户 %s 的点赞历史记录数: %d", userID, len(userHistory))
    
    // 获取项目点赞历史
    itemHistory, err := service.GetItemLikeHistory(ctx, itemType, itemID, 50, 0)
    if err != nil {
        log.Printf("获取项目历史失败: %v", err)
        return
    }
    
    log.Printf("视频 %s 的点赞历史记录数: %d", itemID, len(itemHistory))
    
    // 获取用户统计
    userStats, err := service.GetUserLikeStats(ctx, userID, timeRange)
    if err != nil {
        log.Printf("获取用户统计失败: %v", err)
        return
    }
    
    log.Printf("用户 %s 统计: 总点赞数=%d, 今日点赞数=%d", 
        userID, userStats.TotalLikes, userStats.TodayLikes)
    
    // 获取全局统计
    globalStats, err := service.GetGlobalLikeStats(ctx, itemType, timeRange)
    if err != nil {
        log.Printf("获取全局统计失败: %v", err)
        return
    }
    
    log.Printf("全局统计: 总点赞数=%d, 活跃用户数=%d", 
        globalStats.TotalLikes, globalStats.ActiveUsers)
}
```

### 6. 数据同步

```go
func syncOperations(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    itemType := "video"
    
    // 同步点赞数到数据库
    err := service.SyncLikeCountsToDatabase(ctx, itemType)
    if err != nil {
        log.Printf("同步到数据库失败: %v", err)
        return
    }
    log.Println("同步到数据库成功")
    
    // 从MongoDB同步到Redis
    err = service.SyncToRedis(ctx, itemType)
    if err != nil {
        log.Printf("同步到Redis失败: %v", err)
        return
    }
    log.Println("同步到Redis成功")
    
    // 从Redis同步到MongoDB
    err = service.SyncToMongoDB(ctx, itemType)
    if err != nil {
        log.Printf("同步到MongoDB失败: %v", err)
        return
    }
    log.Println("同步到MongoDB成功")
}
```

### 7. 缓存管理

```go
func cacheOperations(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    itemType := "video"
    
    // 获取缓存统计
    stats, err := service.GetCacheStats(ctx, itemType)
    if err != nil {
        log.Printf("获取缓存统计失败: %v", err)
        return
    }
    
    log.Printf("缓存统计: 命中次数=%d, 未命中次数=%d", 
        stats.HitCount, stats.MissCount)
    
    // 清空缓存
    err = service.FlushCache(ctx, itemType)
    if err != nil {
        log.Printf("清空缓存失败: %v", err)
        return
    }
    log.Println("缓存清空成功")
}
```

### 8. 策略管理

```go
func strategyOperations(service extlike.ExtendedLikeService) {
    // 获取当前策略
    currentStrategy := service.GetStrategy()
    log.Printf("当前存储策略: %v", currentStrategy)
    
    // 切换策略
    err := service.SwitchStrategy(extlike.RedisFirst)
    if err != nil {
        log.Printf("切换策略失败: %v", err)
        return
    }
    log.Println("策略切换成功")
    
    // 验证新策略
    newStrategy := service.GetStrategy()
    log.Printf("新的存储策略: %v", newStrategy)
}
```

## 错误处理

```go
func handleErrors(service extlike.ExtendedLikeService) {
    ctx := context.Background()
    
    err := service.LikeItem(ctx, "user123", "video", "video456")
    if err != nil {
        switch {
        case errors.Is(err, extlike.ErrAlreadyLiked):
            log.Println("用户已经点赞过了")
        case errors.Is(err, extlike.ErrUserNotFound):
            log.Println("用户不存在")
        case errors.Is(err, extlike.ErrItemNotFound):
            log.Println("项目不存在")
        default:
            log.Printf("未知错误: %v", err)
        }
    }
}
```

## 性能优化建议

1. **批量操作**: 尽量使用批量操作接口来减少网络开销
2. **缓存策略**: 根据业务需求选择合适的存储策略
3. **定期同步**: 设置定时任务定期同步数据
4. **监控统计**: 定期检查缓存统计信息，优化缓存配置

## 注意事项

1. 确保Redis和MongoDB连接正常
2. 合理设置缓存过期时间
3. 在高并发场景下注意数据一致性
4. 定期备份重要数据
5. 监控服务性能和错误率