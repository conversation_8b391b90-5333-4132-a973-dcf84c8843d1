<template>
  <el-dialog
    v-model="dialogVisible"
    :title="file ? file.name : '预览'"
    width="70%"
    destroy-on-close
    class="preview-dialog"
    :fullscreen="isMobile"
  >
    <div class="preview-container">
      <!-- 图片预览 -->
      <div v-if="isImage" class="image-preview">
        <img :src="file?.url" :alt="file?.name" @load="imageLoaded = true" />
        <div class="image-info" v-if="imageLoaded">
          <p>分辨率: {{ imageWidth }} x {{ imageHeight }}</p>
          <p>大小: {{ formatFileSize(file?.size || 0) }}</p>
        </div>
      </div>

      <!-- 视频预览 -->
      <div v-else-if="isVideo" class="video-preview">
        <video 
          ref="videoRef"
          controls 
          controlsList="nodownload" 
          :src="file?.url"
          @loadedmetadata="videoLoaded"
        ></video>
        <div class="video-info" v-if="videoInfo.loaded">
          <p>分辨率: {{ videoInfo.width }} x {{ videoInfo.height }}</p>
          <p>时长: {{ formatDuration(videoInfo.duration) }}</p>
          <p>大小: {{ formatFileSize(file?.size || 0) }}</p>
        </div>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <el-icon class="unsupported-icon"><document /></el-icon>
        <p>无法预览该文件类型</p>
        <p>文件名: {{ file?.name }}</p>
        <p>类型: {{ file?.type || '未知' }}</p>
        <p>大小: {{ formatFileSize(file?.size || 0) }}</p>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="downloadFile" v-if="file?.url">下载</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { Document } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: () => null
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Refs
const dialogVisible = ref(props.modelValue);
const imageLoaded = ref(false);
const imageWidth = ref(0);
const imageHeight = ref(0);
const videoInfo = ref({
  loaded: false,
  width: 0,
  height: 0,
  duration: 0
});
const videoRef = ref<HTMLVideoElement | null>(null);

// Computed
const isImage = computed(() => {
  return props.file?.type === 'image'||props.file?.type === 'static'||props.file?.type === 'images'||props.file?.type === 'pictures';
});

const isVideo = computed(() => {
  return props.file?.type === 'video'||props.file?.type === 'videos';
});

const isMobile = computed(() => {
  return window.innerWidth < 768;
});

// Methods
const resetState = () => {
  imageLoaded.value = false;
  imageWidth.value = 0;
  imageHeight.value = 0;
  videoInfo.value = {
    loaded: false,
    width: 0,
    height: 0,
    duration: 0
  };
};

// Watch for prop changes
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val;
});

// Watch for dialog visibility changes
watch(() => dialogVisible.value, (val) => {
  emit('update:modelValue', val);
  if (!val) {
    resetState();
  }
});

// Watch for file changes
watch(() => props.file, () => {
  resetState();
}, { immediate: true });

const videoLoaded = (event: Event) => {
  const video = videoRef.value;
  if (video) {
    videoInfo.value = {
      loaded: true,
      width: video.videoWidth,
      height: video.videoHeight,
      duration: video.duration
    };
  }
};

const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(2)} KB`;
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  } else {
    return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  }
};

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  } else {
    return `${minutes}:${String(secs).padStart(2, '0')}`;
  }
};

const downloadFile = () => {
  if (!props.file?.url) return;
  
  const a = document.createElement('a');
  a.href = props.file.url;
  a.download = props.file.name;
  a.target = '_blank';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

// Get image dimensions when loaded
onMounted(() => {
  watch(() => [props.file?.url, isImage.value, dialogVisible.value], ([url, isImg, visible]) => {
    if (url && isImg && visible) {
      const img = new Image();
      img.onload = () => {
        imageWidth.value = img.width;
        imageHeight.value = img.height;
        imageLoaded.value = true;
      };
      img.src = url;
    }
  }, { immediate: true });
});
</script>

<style scoped lang="scss">
.preview-dialog{
  padding: 0;
  width: 60%;
}
.preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  margin:0;
  overflow: auto;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 0;
  margin:0;
}

.image-preview, .video-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.image-preview img {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.video-preview video {
  max-width: 100%;

}

.image-info, .video-info {
  margin-top: 5px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  width: 100%;
  text-align: center;
}

.unsupported-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  text-align: center;
}

.unsupported-icon {
  font-size: 64px;
  color: #909399;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .preview-dialog :deep(.el-dialog__body) {
    max-height: calc(100vh - 120px);
  }
}
</style> 