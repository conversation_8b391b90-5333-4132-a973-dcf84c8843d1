package container

import (
	walletRepo "frontapi/internal/repository/wallets"
	walletSrv "frontapi/internal/service/wallets"
)

// InitWalletServices 初始化钱包相关服务
func InitWalletServices(b *ServiceBuilder) {
	//注册钱包和提现
	walletRepoImpl := walletRepo.NewWalletRepository(b.DB())
	withdrawRepoImpl := walletRepo.NewWithdrawRequestRepository(b.DB())

	container := b.Services()
	container.WalletService = walletSrv.NewWalletService(walletRepoImpl)
	container.WithdrawRequestService = walletSrv.NewWithdrawRequestService(withdrawRepoImpl, walletRepoImpl)
}
