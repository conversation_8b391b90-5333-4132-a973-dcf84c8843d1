<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="650px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <!-- 基本信息 -->
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入漫画标题" />
      </el-form-item>

      <el-form-item label="作者" prop="author">
        <el-input v-model="formData.author" placeholder="请输入作者名称" />
      </el-form-item>

      <el-form-item label="分类" prop="category_id">
        <el-select v-model="formData.category_id" placeholder="请选择分类" clearable style="width: 100%;">
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入漫画描述"
        />
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-tag
          v-for="tag in formData.tags"
          :key="tag"
          closable
          @close="handleRemoveTag(tag)"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
        <el-input
          v-if="inputTagVisible"
          ref="tagInputRef"
          v-model="tagInputValue"
          class="tag-input"
          size="small"
          @keyup.enter="handleAddTag"
          @blur="handleAddTag"
        />
        <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
      </el-form-item>

      <el-form-item label="封面" prop="cover">
       <UrlOrFileInput v-model="formData.cover" fileType="image"
          subDir="comics/covers"
          placeholder="请输入封面地址或上传封面" />
      </el-form-item>

      <!-- 销售设置 -->
      <el-divider>销售设置</el-divider>

      <el-form-item label="是否免费" prop="is_paid">
        <el-radio-group v-model="formData.is_paid">
          <el-radio :label="0">免费</el-radio>
          <el-radio :label="1">收费</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="价格" prop="price" v-if="formData.is_paid === 1">
        <el-input-number v-model="formData.price" :precision="2" :step="0.01" :min="0" style="width: 180px;" />
      </el-form-item>

      <el-form-item label="推荐" prop="is_featured">
        <el-switch v-model="isFeatured" />
      </el-form-item>

      <el-form-item label="进度" prop="progress">
        <el-radio-group v-model="formData.progress">
          <el-radio value="ongoing">连载中</el-radio>
          <el-radio value="completed">已完结</el-radio>
        </el-radio-group>
      </el-form-item>
    <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :value="2">上架中</el-radio>
          <el-radio :value="1">下架中</el-radio>
          <el-radio :value="0">暂时不可用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watchEffect, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { uploadPictureWithProgress, deleteUploadedFile } from '@/service/api/upload';
import { ComicsItem } from '@/types/comics';
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  comics: {
    type: Object as () => ComicsItem,
    default: () => ({} as ComicsItem)
  },
  categoryOptions: {
    type: Array as () => { id: string; name: string }[],
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'submit']);

// 对话框是否可见
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 对话框标题
const dialogTitle = computed(() => props.comics && props.comics.id ? '编辑漫画' : '添加漫画');

// 表单引用
const formRef = ref();
const submitLoading = ref(false);



// 上传相关
const isUploading = ref(false);
const coverUploadProgress = ref(0);

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  description: '',
  cover: '',
  category_id: '',
  author: '',
  progress: "completed",  // 设置默认值
  is_featured: 0,
  is_paid: 0,
  price: 0,
  tags: [] as string[],
  status: 2
});

// 推荐开关
const isFeatured = computed({
  get: () => formData.is_featured === 1,
  set: (val) => {
    formData.is_featured = val ? 1 : 0;
  }
});

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入漫画标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者名称', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  cover: [
    { required: true, message: '请上传封面', trigger: 'change' }
  ],
  progress: [
    { required: true, message: '请选择进度', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  price: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (formData.is_paid === 1 && (value === null || value === undefined || value <= 0)) {
          callback(new Error('收费漫画需要设置价格'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
};

// 标签输入
const tagInputRef = ref();
const inputTagVisible = ref(false);
const tagInputValue = ref('');

// 显示标签输入框
const showTagInput = () => {
  inputTagVisible.value = true;
  nextTick(() => {
    tagInputRef.value?.focus();
  });
};

// 添加标签
const handleAddTag = () => {
  if (tagInputValue.value) {
    if (!formData.tags) {
      formData.tags = [];
    }
    if (!formData.tags.includes(tagInputValue.value)) {
      formData.tags.push(tagInputValue.value);
    }
  }
  inputTagVisible.value = false;
  tagInputValue.value = '';
};

// 删除标签
const handleRemoveTag = (tag: string) => {
  // 确保tags是数组
  if (!Array.isArray(formData.tags)) {
    formData.tags = [];
  }
  // 使用splice方法删除标签
  const index = formData.tags.indexOf(tag);
  if (index > -1) {
    formData.tags.splice(index, 1);
  }
  // 强制更新视图
  nextTick(() => {
    formRef.value?.validateField('tags');
  });
};

// 从数据同步表单
watchEffect(() => {
  if (props.comics && props.comics.id) {
    Object.keys(formData).forEach(key => {
      if (key in props.comics) {
        // @ts-ignore
        formData[key] = props.comics[key];
      }
    });

    // 确保标签是数组
    if (!formData.tags || !Array.isArray(formData.tags)) {
      formData.tags = [];
    }
  }
});


// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }

  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!');
    return false;
  }

  return true;
};

// 删除封面
const handleDeleteCover = async () => {
  if (!formData.cover) return;

  try {
    await ElMessageBox.confirm(
      '确定要删除当前封面吗？',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 提取文件路径
    const filePath = extractFilePathFromUrl(formData.cover);
    if (filePath) {
      // 调用删除API
      const res = await deleteUploadedFile(filePath);
      console.log('删除封面结果:', res);
    }

    // 无论服务器删除是否成功，都清空表单中的封面
    formData.cover = '';
    ElMessage.success('封面已删除');
  } catch (err) {
    // 用户取消删除
    console.log('用户取消删除封面');
  }
};

// 从URL中提取文件路径
const extractFilePathFromUrl = (url: string): string | null => {
  if (!url) return null;

  try {
    // 去除域名和协议，只保留路径部分
    const urlObj = new URL(url);
    return urlObj.pathname;
  } catch (e) {
    // 如果URL不是完整URL，假设它已经是路径
    return url.startsWith('/') ? url : `/${url}`;
  }
};

// 封面自定义上传
const uploadCoverHandler = async (options: any) => {
  // 设置上传状态
  isUploading.value = true;
  coverUploadProgress.value = 0;

  // 使用带进度的上传函数
  try {
    const response = await uploadPictureWithProgress({
      file: options.file,
      subDir: 'comics/covers',
      callbacks: {
        onProgress: (percent) => {
          coverUploadProgress.value = percent;
        },
        onSuccess: (res) => {
          if (res.code === 2000 && res.data) {
            formData.cover = res.data.url || (typeof res.data === 'string' ? res.data : '');
            ElMessage.success('封面上传成功');
          } else {
            const errMsg = res.message || '上传失败';
            ElMessage.error(errMsg);
          }
        },
        onError: (err) => {
          ElMessage.error(err.message || '上传失败');
        },
        onComplete: () => {
          setTimeout(() => {
            coverUploadProgress.value = 0;
            isUploading.value = false;
          }, 500);
        }
      }
    });

    return response;
  } catch (error: any) {
    ElMessage.error(error.message || '上传失败');
    isUploading.value = false;
    throw error;
  }
};

// 表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    submitLoading.value = true;

    // 准备提交数据
    const submitData = { ...formData };

    // 发送提交事件
    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 取消
const handleCancel = () => {
  dialogVisible.value = false;
};

// 组件挂载时初始化
onMounted(() => {
});
</script>

<style scoped>
.upload-container {
  width: 200px;
}

.preview-container {
  position: relative;
  width: 150px;
  margin-bottom: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.preview-container:hover .action-buttons {
  opacity: 1;
}

.cover-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  display: block;
}

.action-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  opacity: 0;
  transition: opacity 0.3s;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  padding: 2px;
}

.progress-container {
  margin-top: 10px;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
  vertical-align: bottom;
}
</style>
