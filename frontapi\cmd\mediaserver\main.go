package main

import (
	"fmt"
	"frontapi/pkg/utils"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/joho/godotenv"
)

// 路径和URL配置
type MediaConfig struct {
	// 路径和URL映射
	Paths        map[string]string
	URLs         map[string]string
	Port         string
	AllowOrigins string
}

func main() {
	// 加载环境变量
	loadEnv()

	// 获取配置
	config := getConfig()

	// 确保路径存在
	ensureDirectories(config)

	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		// 启用流式传输视频等大文件
		StreamRequestBody: true,
		// 设置较大的Body限制
		BodyLimit: 1024 * 1024 * 1024, // 1GB
	})

	// 添加中间件
	configureMiddleware(app, config)

	// 配置静态文件服务
	configureStaticRoutes(app, config)

	// 添加健康检查
	app.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status": "ok",
			"paths":  config.Paths,
			"urls":   config.URLs,
		})
	})

	// 启动服务器
	port := config.Port
	log.Printf("📷 媒体服务器开始运行在端口 %s\n", port)
	log.Printf("📁 静态文件映射:\n")
	for url, path := range config.Paths {
		log.Printf("   %s -> %s\n", url, path)
	}

	if err := app.Listen(":" + port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// 加载环境变量
func loadEnv() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("警告: 未找到.env文件，将使用默认配置或环境变量")
	}
}

// 获取配置
func getConfig() MediaConfig {
	config := MediaConfig{
		Paths: make(map[string]string),
		URLs:  make(map[string]string),
	}

	// 获取图片和视频路径
	smallImagePath := utils.GetEnv("UPLOAD_SMALL_IMAGE_PATH", "../storage/static/images")
	picturePath := utils.GetEnv("UPLOAD_PICTURE_PATH", "../storage/pictures")
	videoPath := utils.GetEnv("UPLOAD_VIDEO_PATH", "../storage/videos")

	// 获取URL映射
	smallImageURL := strings.TrimPrefix(utils.GetEnv("UPLOAD_SMALL_IMAGE_URL", "/static/images"), "http://localhost:8082")
	pictureURL := strings.TrimPrefix(utils.GetEnv("UPLOAD_PICTURE_URL", "/pictures"), "http://localhost:8082")
	videoURL := strings.TrimPrefix(utils.GetEnv("UPLOAD_VIDEO_URL", "/videos"), "http://localhost:8082")

	// 清理URL路径，确保以/开头但不以/结尾
	smallImageURL = cleanURLPath(smallImageURL)
	pictureURL = cleanURLPath(pictureURL)
	videoURL = cleanURLPath(videoURL)

	// 设置路径映射
	config.Paths[smallImageURL] = smallImagePath
	config.Paths[pictureURL] = picturePath
	config.Paths[videoURL] = videoPath

	// 存储原始URL配置，用于输出信息
	config.URLs[smallImageURL] = utils.GetEnv("UPLOAD_SMALL_IMAGE_URL", "/static/images")
	config.URLs[pictureURL] = utils.GetEnv("UPLOAD_PICTURE_URL", "/pictures")
	config.URLs[videoURL] = utils.GetEnv("UPLOAD_VIDEO_URL", "/videos")

	// 获取服务器端口
	config.Port = utils.GetEnv("MEDIA_SERVER_PORT", "8082")

	// 获取CORS配置
	config.AllowOrigins = utils.GetEnv("CORS_ALLOW_ORIGINS", "*")

	return config
}

// 确保所有目录存在
func ensureDirectories(config MediaConfig) {
	for _, path := range config.Paths {
		ensureDirectoryExists(path)
	}
}

// 确保目录存在
func ensureDirectoryExists(dir string) {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		log.Printf("创建目录: %s\n", dir)
		err := os.MkdirAll(dir, 0755)
		if err != nil {
			log.Printf("无法创建目录 %s: %v\n", dir, err)
		}
	}
}

// 配置中间件
func configureMiddleware(app *fiber.App, config MediaConfig) {
	// 添加恢复中间件
	app.Use(recover.New())

	// 添加日志中间件
	app.Use(logger.New(logger.Config{
		Format: "[${time}] ${status} - ${latency} ${method} ${path}\n",
	}))

	// 添加CORS中间件
	app.Use(cors.New(cors.Config{
		AllowOrigins: config.AllowOrigins,
		AllowHeaders: "Origin, Content-Type, Accept, Authorization",
		AllowMethods: "GET, HEAD, OPTIONS",
	}))

	// 添加缓存控制中间件
	app.Use(func(c *fiber.Ctx) error {
		path := c.Path()

		// 为图片和视频设置不同的缓存策略
		if strings.HasSuffix(path, ".jpg") ||
			strings.HasSuffix(path, ".jpeg") ||
			strings.HasSuffix(path, ".png") ||
			strings.HasSuffix(path, ".gif") ||
			strings.HasSuffix(path, ".webp") {
			// 图片缓存1天
			c.Set("Cache-Control", "public, max-age=86400")
		} else if strings.HasSuffix(path, ".mp4") ||
			strings.HasSuffix(path, ".avi") ||
			strings.HasSuffix(path, ".mov") ||
			strings.HasSuffix(path, ".mkv") {
			// 视频缓存7天
			c.Set("Cache-Control", "public, max-age=604800")
		}

		return c.Next()
	})
}

// 配置静态文件路由
func configureStaticRoutes(app *fiber.App, config MediaConfig) {
	// 视频流处理
	for urlPrefix, dirPath := range config.Paths {
		localDirPath := dirPath // 创建本地变量以便在闭包中使用

		// 处理视频流请求
		if strings.Contains(urlPrefix, "video") {
			app.Get(fmt.Sprintf("%s/*", urlPrefix), func(c *fiber.Ctx) error {
				filename := c.Params("*")
				if filename == "" {
					return c.Status(fiber.StatusNotFound).SendString("文件不存在")
				}

				filePath := filepath.Join(localDirPath, filename)

				// 确保文件存在
				if _, err := os.Stat(filePath); os.IsNotExist(err) {
					return c.Status(fiber.StatusNotFound).SendString("文件不存在")
				}

				// 设置适当的内容类型
				contentType := getContentType(filePath)
				c.Set("Content-Type", contentType)

				// 获取范围请求头
				rangeHeader := c.Get("Range")
				if rangeHeader != "" {
					return handleVideoStream(c, filePath, rangeHeader)
				}

				// 如果不是范围请求，直接发送文件
				return c.SendFile(filePath)
			})
		} else {
			// 为图片和其他媒体类型提供静态文件服务
			app.Static(urlPrefix, localDirPath, fiber.Static{
				Compress:      true,
				ByteRange:     true,
				Browse:        false,
				CacheDuration: 24 * 60 * 60 * time.Second, // 24小时
				MaxAge:        86400,                      // 1天
			})
		}
	}
}

// 处理视频流
func handleVideoStream(c *fiber.Ctx, filePath, rangeHeader string) error {
	// 获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).SendString("无法获取文件信息")
	}
	fileSize := fileInfo.Size()

	// 解析Range头
	rangeValue := strings.TrimPrefix(rangeHeader, "bytes=")
	ranges := strings.Split(rangeValue, "-")
	if len(ranges) != 2 {
		return c.Status(fiber.StatusBadRequest).SendString("无效的Range头")
	}

	// 解析起始位置
	var start int64 = 0
	if ranges[0] != "" {
		fmt.Sscanf(ranges[0], "%d", &start)
	}

	// 解析结束位置
	var end int64 = fileSize - 1
	if ranges[1] != "" {
		fmt.Sscanf(ranges[1], "%d", &end)
	}

	// 确保范围有效
	if start >= fileSize || end >= fileSize || start > end {
		return c.Status(fiber.StatusRequestedRangeNotSatisfiable).SendString("无效的范围")
	}

	// 计算内容长度
	contentLength := end - start + 1

	// 设置响应头
	c.Set("Content-Range", fmt.Sprintf("bytes %d-%d/%d", start, end, fileSize))
	c.Set("Accept-Ranges", "bytes")
	c.Set("Content-Length", fmt.Sprintf("%d", contentLength))

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).SendString("无法打开文件")
	}
	defer file.Close()

	// 移动到起始位置
	_, err = file.Seek(start, 0)
	if err != nil {
		return c.Status(fiber.StatusInternalServerError).SendString("无法定位文件")
	}

	// 设置206部分内容状态码
	c.Status(fiber.StatusPartialContent)

	// 创建一个有限的读取器，只读取指定的字节范围
	limitedReader := io.LimitReader(file, contentLength)

	// 将内容复制到响应中
	_, err = io.Copy(c, limitedReader)
	if err != nil {
		return err
	}

	return nil
}

// 获取内容类型
func getContentType(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".mp4":
		return "video/mp4"
	case ".avi":
		return "video/x-msvideo"
	case ".mov":
		return "video/quicktime"
	case ".mkv":
		return "video/x-matroska"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

// 清理URL路径
func cleanURLPath(path string) string {
	// 确保以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	// 确保不以/结尾
	if strings.HasSuffix(path, "/") {
		path = path[:len(path)-1]
	}

	return path
}
