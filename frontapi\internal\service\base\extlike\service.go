package extlike

import (
	"context"
	"fmt"
	"sync"
	"time"

	goredis "github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"

	redisv2 "frontapi/internal/service/base/extlike/redis/v2"
	"frontapi/internal/service/base/extlike/types"
)

// ExtendedLikeServiceImpl 扩展点赞服务实现
type ExtendedLikeServiceImpl struct {
	config         *Config
	redisAdapter   LikeAdapter
	mongoAdapter   LikeAdapter
	currentAdapter LikeAdapter
	metrics        *ServiceMetrics
	mu             sync.RWMutex

	// 内部组件
	cacheManager     CacheManager
	metricsCollector MetricsCollector
	syncService      SyncService
	healthChecker    HealthChecker

	// 状态管�?
	started bool
	closed  bool
	closeCh chan struct{}
}

// ServiceMetrics 服务指标
type ServiceMetrics struct {
	TotalRequests   int64         `json:"total_requests"`
	SuccessRequests int64         `json:"success_requests"`
	ErrorRequests   int64         `json:"error_requests"`
	AvgLatency      time.Duration `json:"avg_latency"`
	StartTime       time.Time     `json:"start_time"`
	LastOperation   time.Time     `json:"last_operation"`
	mu              sync.RWMutex
}

// NewExtendedLikeService 创建扩展点赞服务实例
func NewExtendedLikeService(config *Config) (*ExtendedLikeServiceImpl, error) {
	if config == nil {
		config = DefaultConfig()
	}

	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	service := &ExtendedLikeServiceImpl{
		config: config,
		metrics: &ServiceMetrics{
			StartTime: time.Now(),
		},
		closeCh: make(chan struct{}),
	}

	// 初始化适配�?
	if err := service.initAdapters(); err != nil {
		return nil, fmt.Errorf("初始化适配器失�? %w", err)
	}

	// 根据策略选择主适配�?
	if err := service.selectAdapter(); err != nil {
		return nil, fmt.Errorf("选择适配器失�? %w", err)
	}

	return service, nil
}

// initAdapters 初始化适配�?
func (s *ExtendedLikeServiceImpl) initAdapters() error {
	// 初始化Redis适配�?
	if s.config.Redis.Enabled {
		redisClient := s.createRedisClient()
		// 使用v2版本的Redis适配�?
		s.redisAdapter = redisv2.NewRedisAdapter(redisClient, redisv2.DefaultRedisConfig())
	}

	// 初始化MongoDB适配器
	if s.config.MongoDB.Enabled {
		// 暂时禁用MongoDB适配器，待后续实现
		// TODO: 实现MongoDB适配器初始化
		s.mongoAdapter = nil
	}

	return nil
}

// createRedisClient 创建Redis客户�?
func (s *ExtendedLikeServiceImpl) createRedisClient() goredis.UniversalClient {
	config := s.config.Redis.Config

	opts := &goredis.UniversalOptions{
		Addrs:           config.Addrs,
		Password:        config.Password,
		DB:              config.DB,
		Username:        config.Username,
		PoolSize:        config.PoolSize,
		MinIdleConns:    config.MinIdleConns,
		MaxConnAge:      config.MaxConnAge,
		PoolTimeout:     config.PoolTimeout,
		IdleTimeout:     config.IdleTimeout,
		DialTimeout:     config.DialTimeout,
		ReadTimeout:     config.ReadTimeout,
		WriteTimeout:    config.WriteTimeout,
		MaxRetries:      config.MaxRetries,
		MinRetryBackoff: config.MinRetryBackoff,
		MaxRetryBackoff: config.MaxRetryBackoff,
		MasterName:      config.MasterName,
	}

	return goredis.NewUniversalClient(opts)
}

// createMongoClient 创建MongoDB客户�?
func (s *ExtendedLikeServiceImpl) createMongoClient() (*mongo.Client, *mongo.Database, error) {
	// 这里简化实现，实际应该根据配置创建MongoDB客户�?
	// 由于MongoDB驱动比较复杂，这里返回nil，在实际使用时需要补充完�?
	return nil, nil, fmt.Errorf("MongoDB客户端创建功能待实现")
}

// selectAdapter 根据策略选择适配�?
func (s *ExtendedLikeServiceImpl) selectAdapter() error {
	switch s.config.Strategy {
	case RedisOnly:
		if s.redisAdapter == nil {
			return fmt.Errorf("Redis适配器未初始化")
		}
		s.currentAdapter = s.redisAdapter

	case MongoOnly:
		if s.mongoAdapter == nil {
			return fmt.Errorf("MongoDB适配器未初始化")
		}
		s.currentAdapter = s.mongoAdapter

	case RedisFirst:
		if s.redisAdapter == nil {
			return fmt.Errorf("Redis适配器未初始化")
		}
		s.currentAdapter = s.redisAdapter

	case MongoFirst:
		if s.mongoAdapter == nil {
			return fmt.Errorf("MongoDB适配器未初始化")
		}
		s.currentAdapter = s.mongoAdapter

	case DualWrite:
		// 双写模式使用Redis作为主适配�?
		if s.redisAdapter == nil || s.mongoAdapter == nil {
			return fmt.Errorf("双写模式需要Redis和MongoDB适配器都可用")
		}
		s.currentAdapter = s.redisAdapter

	default:
		return fmt.Errorf("不支持的存储策略: %v", s.config.Strategy)
	}

	return nil
}

// updateMetrics 更新指标
func (s *ExtendedLikeServiceImpl) updateMetrics(success bool, latency time.Duration) {
	s.metrics.mu.Lock()
	defer s.metrics.mu.Unlock()

	s.metrics.TotalRequests++
	s.metrics.LastOperation = time.Now()

	if success {
		s.metrics.SuccessRequests++
	} else {
		s.metrics.ErrorRequests++
	}

	// 计算平均延迟
	totalLatency := s.metrics.AvgLatency * time.Duration(s.metrics.TotalRequests-1)
	s.metrics.AvgLatency = (totalLatency + latency) / time.Duration(s.metrics.TotalRequests)
}

// executeWithFallback 执行操作，支持故障转�?
func (s *ExtendedLikeServiceImpl) executeWithFallback(ctx context.Context, operation func(adapter LikeAdapter) error) error {
	start := time.Now()
	var err error

	defer func() {
		latency := time.Since(start)
		s.updateMetrics(err == nil, latency)
	}()

	// 执行主适配器操�?
	err = operation(s.currentAdapter)
	if err == nil {
		// 如果是双写模式，同时写入备份适配�?
		if s.config.Strategy == DualWrite {
			go s.executeBackgroundOperation(ctx, operation)
		}
		return nil
	}

	// 主适配器失败，尝试故障转移
	if s.config.Strategy == RedisFirst && s.mongoAdapter != nil {
		err = operation(s.mongoAdapter)
	} else if s.config.Strategy == MongoFirst && s.redisAdapter != nil {
		err = operation(s.redisAdapter)
	}

	return err
}

// executeBackgroundOperation 后台执行操作（用于双写模式）
func (s *ExtendedLikeServiceImpl) executeBackgroundOperation(ctx context.Context, operation func(adapter LikeAdapter) error) {
	var backupAdapter LikeAdapter

	// 选择备份适配�?
	if s.currentAdapter == s.redisAdapter {
		backupAdapter = s.mongoAdapter
	} else {
		backupAdapter = s.redisAdapter
	}

	if backupAdapter != nil {
		operation(backupAdapter)
	}
}

// 实现 LikeService 接口

// Like 点赞操作
func (s *ExtendedLikeServiceImpl) Like(ctx context.Context, userID, itemID, itemType string) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.Like(ctx, userID, itemID, itemType)
	})
}

// Unlike 取消点赞操作
func (s *ExtendedLikeServiceImpl) Unlike(ctx context.Context, userID, itemID, itemType string) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.Unlike(ctx, userID, itemID, itemType)
	})
}

// IsLiked 检查是否已点赞
func (s *ExtendedLikeServiceImpl) IsLiked(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	var result bool
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.IsLiked(ctx, userID, itemID, itemType)
		return err
	})
	return result, err
}

// GetLikeCount 获取点赞�?
func (s *ExtendedLikeServiceImpl) GetLikeCount(ctx context.Context, itemID, itemType string) (int64, error) {
	var result int64
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetLikeCount(ctx, itemID, itemType)
		return err
	})
	return result, err
}

// BatchLike 批量点赞
func (s *ExtendedLikeServiceImpl) BatchLike(ctx context.Context, operations []*types.LikeOperation) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.BatchLike(ctx, operations)
	})
}

// BatchUnlike 批量取消点赞
func (s *ExtendedLikeServiceImpl) BatchUnlike(ctx context.Context, operations []*types.LikeOperation) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.BatchUnlike(ctx, operations)
	})
}

// BatchGetLikeStatus 批量获取点赞状�?
func (s *ExtendedLikeServiceImpl) BatchGetLikeStatus(ctx context.Context, userID string, items map[string]string) (map[string]bool, error) {
	var result map[string]bool
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.BatchGetLikeStatus(ctx, userID, items)
		return err
	})
	return result, err
}

// BatchGetLikeCounts 批量获取点赞�?
func (s *ExtendedLikeServiceImpl) BatchGetLikeCounts(ctx context.Context, items map[string]string) (map[string]int64, error) {
	var result map[string]int64
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.BatchGetLikeCounts(ctx, items)
		return err
	})
	return result, err
}

// GetUserLikes 获取用户点赞列表
func (s *ExtendedLikeServiceImpl) GetUserLikes(ctx context.Context, userID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	var result []*types.LikeRecord
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetUserLikes(ctx, userID, itemType, limit, offset)
		return err
	})
	return result, err
}

// GetItemLikers 获取物品点赞用户列表
func (s *ExtendedLikeServiceImpl) GetItemLikers(ctx context.Context, itemID, itemType string, limit, offset int) ([]*types.LikeRecord, error) {
	var result []*types.LikeRecord
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetItemLikers(ctx, itemID, itemType, limit, offset)
		return err
	})
	return result, err
}

// GetLikeHistory 获取点赞历史
func (s *ExtendedLikeServiceImpl) GetLikeHistory(ctx context.Context, userID, itemType string, timeRange *types.TimeRange) ([]*types.LikeRecord, error) {
	var result []*types.LikeRecord
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetLikeHistory(ctx, userID, itemType, timeRange)
		return err
	})
	return result, err
}

// UpdateHotRank 更新热门排行
func (s *ExtendedLikeServiceImpl) UpdateHotRank(ctx context.Context, itemID, itemType string, score float64) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.UpdateHotRank(ctx, itemID, itemType, score)
	})
}

// GetHotRanking 获取热门排行
func (s *ExtendedLikeServiceImpl) GetHotRanking(ctx context.Context, itemType string, limit int) ([]string, error) {
	var result []string
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetHotRanking(ctx, itemType, limit)
		return err
	})
	return result, err
}

// GetHotRankingWithScores 获取热门排行（带分数�?
func (s *ExtendedLikeServiceImpl) GetHotRankingWithScores(ctx context.Context, itemType string, limit int) (map[string]float64, error) {
	var result map[string]float64
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetHotRankingWithScores(ctx, itemType, limit)
		return err
	})
	return result, err
}

// GetUserLikeStats 获取用户点赞统计
func (s *ExtendedLikeServiceImpl) GetUserLikeStats(ctx context.Context, userID string) (*types.UserLikeStats, error) {
	var result *types.UserLikeStats
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetUserLikeStats(ctx, userID)
		return err
	})
	return result, err
}

// GetItemLikeStats 获取物品点赞统计
func (s *ExtendedLikeServiceImpl) GetItemLikeStats(ctx context.Context, itemID, itemType string) (*types.ItemLikeStats, error) {
	var result *types.ItemLikeStats
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetItemLikeStats(ctx, itemID, itemType)
		return err
	})
	return result, err
}

// GetTrendingItems 获取趋势物品
func (s *ExtendedLikeServiceImpl) GetTrendingItems(ctx context.Context, itemType string, timeRange *types.TimeRange, limit int) ([]*types.LikeTrend, error) {
	var result []*types.LikeTrend
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetTrendingItems(ctx, itemType, timeRange, limit)
		return err
	})
	return result, err
}

// HealthCheck 健康检�?
func (s *ExtendedLikeServiceImpl) HealthCheck(ctx context.Context) error {
	return s.currentAdapter.HealthCheck(ctx)
}

// Close 关闭服务
func (s *ExtendedLikeServiceImpl) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.closed {
		return nil
	}

	s.closed = true
	close(s.closeCh)

	var errs []error

	if s.redisAdapter != nil {
		if err := s.redisAdapter.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭Redis适配器失�? %w", err))
		}
	}

	if s.mongoAdapter != nil {
		if err := s.mongoAdapter.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭MongoDB适配器失�? %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("关闭服务时发生错�? %v", errs)
	}

	return nil
}

// 实现 ExtendedLikeService 接口

// InvalidateCache 失效缓存
func (s *ExtendedLikeServiceImpl) InvalidateCache(ctx context.Context, keys ...string) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.InvalidateCache(ctx, keys...)
	})
}

// WarmupCache 预热缓存
func (s *ExtendedLikeServiceImpl) WarmupCache(ctx context.Context, itemType string, itemIDs []string) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.WarmupCache(ctx, itemType, itemIDs)
	})
}

// GetCacheStats 获取缓存统计
func (s *ExtendedLikeServiceImpl) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	var result map[string]interface{}
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.GetCacheStats(ctx)
		return err
	})
	return result, err
}

// CleanupExpiredData 清理过期数据
func (s *ExtendedLikeServiceImpl) CleanupExpiredData(ctx context.Context, itemType string, before time.Time) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.CleanupExpiredData(ctx, itemType, before)
	})
}

// ExportData 导出数据
func (s *ExtendedLikeServiceImpl) ExportData(ctx context.Context, userID, itemType, format string) ([]byte, error) {
	var result []byte
	err := s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		var err error
		result, err = adapter.ExportData(ctx, userID, itemType, format)
		return err
	})
	return result, err
}

// ImportData 导入数据
func (s *ExtendedLikeServiceImpl) ImportData(ctx context.Context, data []byte, itemType, format string) error {
	return s.executeWithFallback(ctx, func(adapter LikeAdapter) error {
		return adapter.ImportData(ctx, data, itemType, format)
	})
}

// SyncData 数据同步
func (s *ExtendedLikeServiceImpl) SyncData(ctx context.Context, itemType string, startTime, endTime time.Time) error {
	// 简化实现，实际需要完整的同步逻辑
	return nil
}

// GetSyncStatus 获取同步状�?
func (s *ExtendedLikeServiceImpl) GetSyncStatus(ctx context.Context) (*types.SyncStatus, error) {
	return &types.SyncStatus{
		LastSyncTime:   time.Now(),
		SyncEnabled:    true,
		SyncInProgress: false,
	}, nil
}

// GetMetrics 获取服务指标
func (s *ExtendedLikeServiceImpl) GetMetrics(ctx context.Context) (*types.ServiceMetrics, error) {
	s.metrics.mu.RLock()
	defer s.metrics.mu.RUnlock()

	return &types.ServiceMetrics{
		ServiceName:       s.config.ServiceName,
		Version:           "1.0.0",
		Timestamp:         time.Now(),
		Uptime:            time.Since(s.metrics.StartTime),
		TotalRequests:     s.metrics.TotalRequests,
		SuccessRequests:   s.metrics.SuccessRequests,
		ErrorRequests:     s.metrics.ErrorRequests,
		AvgResponseTime:   s.metrics.AvgLatency,
		RequestsPerSecond: float64(s.metrics.TotalRequests) / time.Since(s.metrics.StartTime).Seconds(),
	}, nil
}

// GetErrorStats 获取错误统计
func (s *ExtendedLikeServiceImpl) GetErrorStats(ctx context.Context) (map[string]interface{}, error) {
	s.metrics.mu.RLock()
	defer s.metrics.mu.RUnlock()

	totalRequests := s.metrics.TotalRequests
	errorRate := 0.0
	if totalRequests > 0 {
		errorRate = float64(s.metrics.ErrorRequests) / float64(totalRequests)
	}

	return map[string]interface{}{
		"total_requests":   totalRequests,
		"error_requests":   s.metrics.ErrorRequests,
		"success_requests": s.metrics.SuccessRequests,
		"error_rate":       errorRate,
		"last_operation":   s.metrics.LastOperation,
	}, nil
}

// Shutdown 优雅关闭
func (s *ExtendedLikeServiceImpl) Shutdown(ctx context.Context) error {
	return s.Close()
}
