<template>
  <div class="comment-modal-overlay" @click="$emit('close')">
    <div class="comment-modal" @click.stop>
      <div class="modal-header">
        <h3>评论 ({{ comments.length }})</h3>
        <button class="close-btn" @click="$emit('close')">
          <span>×</span>
        </button>
      </div>
      
      <div class="comments-container" ref="commentsContainer">
        <div v-if="loading" class="loading-state">
          <div class="spinner"></div>
          <p>加载评论中...</p>
        </div>
        
        <div v-else-if="comments.length === 0" class="empty-state">
          <div class="empty-icon">💬</div>
          <p>还没有评论，快来抢沙发吧！</p>
        </div>
        
        <div v-else class="comments-list">
          <div 
            v-for="comment in comments" 
            :key="comment.id" 
            class="comment-item"
          >
            <div class="comment-avatar">
              <img v-if="comment.avatar" :src="comment.avatar" :alt="comment.username" />
              <div v-else class="avatar-placeholder">{{ comment.username[0] }}</div>
            </div>
            
            <div class="comment-content">
              <div class="comment-header">
                <span class="username">{{ comment.username }}</span>
                <span class="timestamp">{{ formatTime(comment.created_at) }}</span>
              </div>
              
              <div class="comment-text">{{ comment.content }}</div>
              
              <div class="comment-actions">
                <button 
                  class="action-btn like-btn" 
                  :class="{ 'liked': comment.isLiked }"
                  @click="toggleLike(comment)"
                >
                  <span class="icon">{{ comment.isLiked ? '❤️' : '🤍' }}</span>
                  <span v-if="comment.like_count > 0">{{ comment.like_count }}</span>
                </button>
                
                <button class="action-btn reply-btn" @click="replyTo(comment)">
                  <span class="icon">💬</span>
                  <span>回复</span>
                </button>
              </div>
              
              <!-- 回复列表 -->
              <div v-if="comment.replies && comment.replies.length > 0" class="replies-list">
                <div 
                  v-for="reply in comment.replies" 
                  :key="reply.id" 
                  class="reply-item"
                >
                  <div class="reply-avatar">
                    <img v-if="reply.avatar" :src="reply.avatar" :alt="reply.username" />
                    <div v-else class="avatar-placeholder">{{ reply.username[0] }}</div>
                  </div>
                  
                  <div class="reply-content">
                    <div class="reply-header">
                      <span class="username">{{ reply.username }}</span>
                      <span class="timestamp">{{ formatTime(reply.created_at) }}</span>
                    </div>
                    <div class="reply-text">
                      <span v-if="reply.reply_to" class="reply-target">@{{ reply.reply_to }}</span>
                      {{ reply.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="comment-input-section">
        <div v-if="replyingTo" class="replying-indicator">
          <span>回复 @{{ replyingTo.username }}</span>
          <button @click="cancelReply" class="cancel-reply">×</button>
        </div>
        
        <div class="input-container">
          <div class="user-avatar">
            <img v-if="currentUser.avatar" :src="currentUser.avatar" :alt="currentUser.username" />
            <div v-else class="avatar-placeholder">{{ currentUser.username[0] }}</div>
          </div>
          
          <div class="input-wrapper">
            <textarea
              v-model="newComment"
              ref="commentInput"
              :placeholder="replyingTo ? `回复 @${replyingTo.username}` : '说点什么...'"
              @keydown="handleKeydown"
              rows="1"
              class="comment-input"
            ></textarea>
            
            <div class="input-actions">
              <button class="emoji-btn" @click="showEmojiPicker = !showEmojiPicker">
                😊
              </button>
              
              <button 
                class="send-btn" 
                :disabled="!newComment.trim() || submitting"
                @click="submitComment"
              >
                <span v-if="submitting" class="loading">⏳</span>
                <span v-else>发送</span>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 表情选择器 -->
        <div v-if="showEmojiPicker" class="emoji-picker">
          <div class="emoji-grid">
            <button 
              v-for="emoji in commonEmojis" 
              :key="emoji" 
              class="emoji-item"
              @click="insertEmoji(emoji)"
            >
              {{ emoji }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'

interface Comment {
  id: string
  username: string
  avatar?: string
  content: string
  created_at: string
  like_count: number
  isLiked: boolean
  replies?: Reply[]
}

interface Reply {
  id: string
  username: string
  avatar?: string
  content: string
  created_at: string
  reply_to?: string
}

interface Props {
  videoId?: string
  videoTitle?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  'comment-added': []
}>()

// State
const loading = ref(true)
const submitting = ref(false)
const comments = ref<Comment[]>([])
const newComment = ref('')
const replyingTo = ref<Comment | null>(null)
const showEmojiPicker = ref(false)

// Refs
const commentsContainer = ref<HTMLElement>()
const commentInput = ref<HTMLTextAreaElement>()

// Current user (模拟数据)
const currentUser = reactive({
  username: '当前用户',
  avatar: ''
})

// 常用表情
const commonEmojis = [
  '😊', '😂', '❤️', '👍', '👎', '😍', '😢', '😮', '😡', '🤔',
  '👏', '🙏', '💪', '🔥', '✨', '🎉', '😎', '🤗', '😘', '🥰'
]

// Methods
const loadComments = async () => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟评论数据
    comments.value = [
      {
        id: '1',
        username: '用户1',
        avatar: '',
        content: '这个视频太棒了！👍',
        created_at: '2024-01-15T10:30:00Z',
        like_count: 5,
        isLiked: false,
        replies: [
          {
            id: '1-1',
            username: '用户2',
            avatar: '',
            content: '同意！',
            created_at: '2024-01-15T10:35:00Z',
            reply_to: '用户1'
          }
        ]
      },
      {
        id: '2',
        username: '用户3',
        avatar: '',
        content: '非常有趣的内容，学到了很多东西',
        created_at: '2024-01-15T09:20:00Z',
        like_count: 3,
        isLiked: true
      }
    ]
  } catch (error) {
    console.error('加载评论失败:', error)
  } finally {
    loading.value = false
  }
}

const submitComment = async () => {
  if (!newComment.value.trim() || submitting.value) return
  
  try {
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const comment: Comment = {
      id: Date.now().toString(),
      username: currentUser.username,
      avatar: currentUser.avatar,
      content: newComment.value.trim(),
      created_at: new Date().toISOString(),
      like_count: 0,
      isLiked: false
    }
    
    if (replyingTo.value) {
      // 添加回复
      if (!replyingTo.value.replies) {
        replyingTo.value.replies = []
      }
      replyingTo.value.replies.push({
        id: Date.now().toString(),
        username: currentUser.username,
        avatar: currentUser.avatar,
        content: newComment.value.trim(),
        created_at: new Date().toISOString(),
        reply_to: replyingTo.value.username
      })
    } else {
      // 添加新评论
      comments.value.unshift(comment)
    }
    
    newComment.value = ''
    replyingTo.value = null
    showEmojiPicker.value = false
    
    emit('comment-added')
    
    // 滚动到顶部显示新评论
    nextTick(() => {
      if (commentsContainer.value) {
        commentsContainer.value.scrollTop = 0
      }
    })
  } catch (error) {
    console.error('发送评论失败:', error)
  } finally {
    submitting.value = false
  }
}

const toggleLike = (comment: Comment) => {
  comment.isLiked = !comment.isLiked
  comment.like_count += comment.isLiked ? 1 : -1
}

const replyTo = (comment: Comment) => {
  replyingTo.value = comment
  nextTick(() => {
    commentInput.value?.focus()
  })
}

const cancelReply = () => {
  replyingTo.value = null
}

const insertEmoji = (emoji: string) => {
  newComment.value += emoji
  showEmojiPicker.value = false
  nextTick(() => {
    commentInput.value?.focus()
  })
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    submitComment()
  }
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 30) return `${days}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// Lifecycle
onMounted(() => {
  loadComments()
})
</script>

<style scoped>
.comment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.comment-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
  background: #fafafa;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 24px;
  color: #666;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
}

.comments-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.comments-list {
  padding: 0 24px;
}

.comment-item {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.comment-avatar,
.reply-avatar,
.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.comment-avatar img,
.reply-avatar img,
.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header,
.reply-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.username {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.timestamp {
  font-size: 12px;
  color: #999;
}

.comment-text,
.reply-text {
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.reply-target {
  color: #ff6b6b;
  font-weight: 500;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #666;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 16px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.like-btn.liked {
  color: #ff6b6b;
}

.replies-list {
  margin-top: 12px;
  padding-left: 16px;
  border-left: 2px solid #f0f0f0;
}

.reply-item {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.reply-item:last-child {
  margin-bottom: 0;
}

.reply-avatar {
  width: 28px;
  height: 28px;
}

.reply-content {
  flex: 1;
  min-width: 0;
}

.comment-input-section {
  border-top: 1px solid #eee;
  padding: 16px 24px;
  background: #fafafa;
}

.replying-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 107, 107, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #ff6b6b;
}

.cancel-reply {
  background: none;
  border: none;
  color: #ff6b6b;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.cancel-reply:hover {
  background: rgba(255, 107, 107, 0.1);
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.input-wrapper {
  flex: 1;
  background: white;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 12px 16px;
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.comment-input {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
  max-height: 100px;
  min-height: 20px;
  font-family: inherit;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.emoji-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.emoji-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.send-btn {
  background: #ff6b6b;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
}

.send-btn:hover:not(:disabled) {
  background: #ff5252;
  transform: translateY(-1px);
}

.send-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.emoji-picker {
  margin-top: 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 8px;
}

.emoji-item {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.emoji-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .comment-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .comments-list {
    padding: 0 20px;
  }
  
  .comment-input-section {
    padding: 12px 20px;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}
</style> 