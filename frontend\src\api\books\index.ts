import { get, post } from '@/shared/composables'
import { coreGet, corePost } from '@/shared/composables'
import type { Book, BookCategory, BookChapter, BookDetail, ChapterContent, RelatedBook, Bookmark } from '@/types/Book'

// 通用响应接口
interface ListResponse<T> {
  list: T[]
  total: number
  currentPage: number
  pageSize: number
}

// 获取电子书分类
export const getBookCategories = () => {
  return coreGet('/api/books/categories', {})
}

// 获取电子书列表
export interface BookListParams {
  page: {
    pageNo: number
    pageSize: number
  }
  data: {
    categoryId?: number
    keyword?: string
    sortBy?: 'popularity' | 'latest' | 'rating'
  }
}

export const getBookList = (params: BookListParams) => {
  return corePost('/api/books/list', params)
}

// 获取电子书详情
export interface BookDetailParams {
  bookId: number
}

export const getBookDetail = (params: BookDetailParams) => {
  return corePost('/api/books/detail', params)
}

// 获取章节内容
export interface ChapterParams {
  chapterId: number
}

export const getChapterContent = (params: ChapterParams) => {
  return corePost('/api/books/chapter', params)
}

// 获取相关推荐
export const getRelatedBooks = (params: BookDetailParams) => {
  return corePost('/api/books/related', params)
}

// 获取书签列表
export const getBookmarks = (params: BookDetailParams) => {
  return corePost('/api/books/bookmarks', params)
}

// 添加书签
export interface AddBookmarkParams {
  bookId: number
  chapterId: number
  position: number
  title: string
  content: string
}

export const addBookmark = (params: AddBookmarkParams) => {
  return corePost('/api/books/bookmark/add', params)
}

// 删除书签
export interface DeleteBookmarkParams {
  bookId: number
  bookmarkId: number
}

export const deleteBookmark = (params: DeleteBookmarkParams) => {
  return corePost('/api/books/bookmark/delete', params)
}
