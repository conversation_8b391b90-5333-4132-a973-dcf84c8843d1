package posts

import (
	"context"
	"frontapi/internal/models/posts"
	postValidator "frontapi/internal/validation/posts"
	"frontapi/pkg/events"
	"frontapi/pkg/hooks"
)

// 帖子服务扩展示例，展示如何使用事件和钩子

// 在这个示例中，我们扩展 PostService 的 CreatePost 方法，
// 添加事件发布和钩子执行功能

// CreatePostWithEvents 创建帖子并触发事件
func (s *postService) CreatePostWithEvents(ctx context.Context, req *postValidator.CreatePostRequest) (string, error) {
	// 执行前置钩子
	if err := hooks.GlobalHookManager.ExecuteHooks(ctx, hooks.HookBeforePostCreate, req); err != nil {
		return "", err
	}

	// 调用原始方法创建帖子
	post := &posts.Post{
		AuthorID: req.AuthorID,
		Content:  req.Content,
		// Title:    req.Title,
		// Status:   posts.PostStatusNormal,
		// Category: req.Category,
	}
	postID, err := s.Create(ctx, post)
	if err != nil {
		return "", err
	}

	// 创建事件数据
	eventData := map[string]interface{}{
		"post_id":   postID,
		"author_id": req.AuthorID,
		"content":   req.Content,
	}

	// 发布帖子创建事件
	events.GlobalEventBus.Publish(events.Event{
		Type:    events.EventPostCreated,
		Payload: eventData,
	})

	// 执行后置钩子
	if err := hooks.GlobalHookManager.ExecuteHooks(ctx, hooks.HookAfterPostCreate, map[string]interface{}{
		"post_id":   postID,
		"author_id": req.AuthorID,
	}); err != nil {
		// 这里我们选择记录错误但不影响主流程
		// log.Printf("执行后置钩子失败: %v", err)
	}

	return postID, nil
}

// DeletePostWithEvents 删除帖子并触发事件
func (s *postService) DeletePostWithEvents(ctx context.Context, postID string, userID string) error {
	// 执行前置钩子
	if err := hooks.GlobalHookManager.ExecuteHooks(ctx, hooks.HookBeforePostDelete, map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	}); err != nil {
		return err
	}

	// 调用原始方法删除帖子
	if err := s.DeleteByCondition(ctx, map[string]interface{}{
		"id":        postID,
		"author_id": userID,
	}); err != nil {
		return err
	}

	// 发布帖子删除事件
	events.GlobalEventBus.Publish(events.Event{
		Type: events.EventPostDeleted,
		Payload: map[string]interface{}{
			"post_id": postID,
			"user_id": userID,
		},
	})

	// 执行后置钩子
	hooks.GlobalHookManager.ExecuteHooks(ctx, hooks.HookAfterPostDelete, map[string]interface{}{
		"post_id": postID,
		"user_id": userID,
	})

	return nil
}

// 示例：如何注册事件处理器和钩子

// RegisterPostEventHandlers 注册帖子相关事件处理器
func RegisterPostEventHandlers() {
	// 注册帖子创建事件处理器
	events.GlobalEventBus.Subscribe(events.EventPostCreated, func(e events.Event) {
		// 这里可以实现事件处理逻辑，比如：
		// 1. 更新统计数据
		// 2. 发送通知
		// 3. 同步到搜索引擎
		// 4. 触发缓存失效
		// ...
	})

	// 注册帖子删除事件处理器
	events.GlobalEventBus.Subscribe(events.EventPostDeleted, func(e events.Event) {
		// 处理帖子删除事件
	})
}

// RegisterPostHooks 注册帖子相关钩子
func RegisterPostHooks() {
	// 注册帖子创建前钩子
	hooks.GlobalHookManager.RegisterHook(hooks.HookBeforePostCreate, hooks.Hook{
		Name:     "validate_post_content",
		Priority: 10,
		Func: func(ctx context.Context, data interface{}) error {
			// 帖子创建前的内容验证逻辑
			// ...
			return nil
		},
	})

	// 注册帖子创建后钩子
	hooks.GlobalHookManager.RegisterHook(hooks.HookAfterPostCreate, hooks.Hook{
		Name:     "index_post_content",
		Priority: 20,
		Func: func(ctx context.Context, data interface{}) error {
			// 帖子创建后的索引逻辑
			// ...
			return nil
		},
	})
}

// 在应用启动时，可以调用这些函数来注册事件处理器和钩子
func init() {
	RegisterPostEventHandlers()
	RegisterPostHooks()
}
