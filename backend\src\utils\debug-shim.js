// debug-shim.js - Fixes module format issues with debug package

// Determine the environment
const isESM = typeof import.meta !== 'undefined';
const isCJS = typeof module !== 'undefined' && module.exports;

// Our debug implementation - fallback if the real one fails
const createDebugFallback = (namespace) => {
  return function(...args) {
    console.log(`[${namespace}]`, ...args);
  };
};

// Initialize with fallback implementations
let debugImpl = createDebugFallback;
let formattersImpl = {};
let logImpl = console.log.bind(console);

// Function to load the real debug module
async function loadRealDebugModule() {
  if (isESM) {
    try {
      const debugModule = await import('debug');
      return {
        debug: debugModule.default || debugModule,
        formatters: debugModule.formatters || {},
        log: debugModule.log || console.log.bind(console)
      };
    } catch (error) {
      console.warn('Failed to import debug module (ESM), using fallback implementation');
      return { debug: createDebugFallback, formatters: {}, log: console.log.bind(console) };
    }
  } else if (isCJS) {
    try {
      const debugModule = require('debug');
      return {
        debug: debugModule,
        formatters: debugModule.formatters || {},
        log: debugModule.log || console.log.bind(console)
      };
    } catch (error) {
      console.warn('Failed to import debug module (CJS), using fallback implementation');
      return { debug: createDebugFallback, formatters: {}, log: console.log.bind(console) };
    }
  }
  return { debug: createDebugFallback, formatters: {}, log: console.log.bind(console) };
}

// Try to load the real module immediately
loadRealDebugModule().then(({ debug, formatters, log }) => {
  debugImpl = debug;
  formattersImpl = formatters;
  logImpl = log;
}).catch(error => {
  console.error('Error loading debug module:', error);
});

// The main debug function
const debug = (namespace) => {
  // This will use the latest implementation (real or fallback)
  return debugImpl(namespace);
};

// Attach properties to match the original debug module
debug.formatters = formattersImpl;
debug.log = logImpl;

// Export for ESM
export { debug as debug };
export const formatters = formattersImpl;
export const log = logImpl;

// Export for CJS
if (isCJS) {
  module.exports = debug;
  module.exports.formatters = formattersImpl;
  module.exports.log = logImpl;
}

// Default export
export default debug; 