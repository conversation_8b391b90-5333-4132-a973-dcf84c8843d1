<template>
  <div class="shortvideo-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item label="标题">
        <el-input 
          v-model="searchForm.keyword" 
          placeholder="请输入短视频标题" 
          clearable 
          style="width: 200px"
          @keyup.enter="handleSearch"
        ></el-input>
      </el-form-item>
      
      <el-form-item label="分类">
        <el-select 
          v-model="searchForm.category_id" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请选择分类" 
          :remote-method="handleSearchCategories" 
          :loading="categoriesLoading" 
          clearable 
          style="width: 200px;"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="创建者">
        <el-select 
          v-model="searchForm.creator_id" 
          filterable 
          remote 
          reserve-keyword 
          placeholder="请搜索创建者" 
          :remote-method="handleSearchCreators" 
          :loading="creatorsLoading" 
          clearable 
          style="width: 200px"
        >
          <el-option 
            v-for="creator in creatorsOptions" 
            :key="creator.id"
            :label="creator.nickname || creator.username" 
            :value="creator.id"
          >
            <div style="display: flex; align-items: center;">
              <el-avatar :size="20" :src="creator.avatar" style="margin-right: 6px;">
                {{ (creator.nickname || creator.username).charAt(0) }}
              </el-avatar>
              <span>{{ creator.nickname || creator.username }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="状态">
        <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width:200px">
          <!-- 状态：0-待审核,1-已下架,2-已发布,-2-已拒绝,-4-已删除 -->
          <el-option label="全部" value=""></el-option>
          <el-option label="待审核" :value="0"></el-option>
          <el-option label="已下架" :value="1"></el-option>
          <el-option label="已发布" :value="2"></el-option>
          <el-option label="已拒绝" :value="-2"></el-option>
          <el-option label="已删除" :value="-4"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="日期范围">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 260px"
          @change="handleDateRangeChange"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { getCategoryList } from '@/service/api/shortvideos/shortvideos';
import { searchUsers } from '@/service/api/videos/videos';
import { Refresh, Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

const emit = defineEmits(['search', 'reset', 'refresh']);

// 搜索表单数据
const searchForm = reactive({
  keyword: '',
  category_id: '',
  creator_id: '',
  status: '',
  start_date: '',
  end_date: ''
});

// 日期范围
const dateRange = ref<string[]>([]);

// 下拉选项
const categoryOptions = ref<Array<{value: string, label: string}>>([]);
const creatorsOptions = ref<any[]>([]);

// 加载状态
const categoriesLoading = ref(false);
const creatorsLoading = ref(false);

// 生命周期钩子
onMounted(() => {
  loadDefaultOptions();
});

// 加载默认选项
const loadDefaultOptions = async () => {
  // 加载默认分类选项
  try {
    const { data, response } = await getCategoryList({
      page: { pageNo: 1, pageSize: 10 },
      data: { status: 1 }
    }) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      categoryOptions.value = (data.list || []).map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    }
  } catch (error) {
    console.error('加载默认分类失败:', error);
  }
};

// 搜索分类
const handleSearchCategories = async (query: string) => {
  categoriesLoading.value = true;
  try {
    const params: any = {
      page: { pageNo: 1, pageSize: 20 },
      data: { status: 1 }
    };
    
    if (query) {
      params.data.keyword = query;
    }
    
    const { data, response } = await getCategoryList(params) as any;
    
    if (response.status === 200 && response.data.code === 2000) {
      categoryOptions.value = (data.list || []).map((item: any) => ({
        value: item.id,
        label: item.name
      }));
    } else {
      categoryOptions.value = [];
    }
  } catch (error) {
    console.error('搜索分类失败:', error);
    ElMessage.error('搜索分类失败');
    categoryOptions.value = [];
  } finally {
    categoriesLoading.value = false;
  }
};

// 搜索创建者
const handleSearchCreators = async (query: string) => {
  if (query) {
    creatorsLoading.value = true;
    try {
      const { data, response } = await searchUsers({
        page: { pageNo: 1, pageSize: 20 },
        data: { keyword: query, status: 1 }
      }) as any;

      if (response.status === 200 && response.data.code === 2000) {
        creatorsOptions.value = data.list || [];
      } else {
        creatorsOptions.value = [];
      }
    } catch (error) {
      console.error('搜索创建者失败:', error);
      ElMessage.error('搜索创建者失败');
      creatorsOptions.value = [];
    } finally {
      creatorsLoading.value = false;
    }
  } else {
    creatorsOptions.value = [];
  }
};

// 处理日期范围变化
const handleDateRangeChange = (val: string[]) => {
  if (val && val.length === 2) {
    searchForm.start_date = val[0];
    searchForm.end_date = val[1];
  } else {
    searchForm.start_date = '';
    searchForm.end_date = '';
  }
};

// 处理搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 处理重置
const handleReset = () => {
  // 重置表单数据
  Object.keys(searchForm).forEach(key => {
    searchForm[key as keyof typeof searchForm] = '';
  });
  dateRange.value = [];
  emit('reset');
};

// 处理刷新
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped lang="scss">
.shortvideo-search-bar {
  margin-bottom: 16px;
  
  .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .el-form-item {
    margin-bottom: 8px;
  }
}
</style> 