package users

//UserLoginLogsRepository 用户登录日志数据访问接口

import (
	"fmt"
	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// UserLoginLogsRepository 用户登录日志数据访问接口
type UserLoginLogsRepository interface {
	base.ExtendedRepository[users.UserLoginLogs]
}
type userLoginLogsRepository struct {
	base.ExtendedRepository[users.UserLoginLogs]
}

func NewUserLoginLogsRepository(db *gorm.DB) UserLoginLogsRepository {
	repo := &userLoginLogsRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserLoginLogs](db),
	}
	// 设置调用者实例，这样基础仓库就能识别并使用子类的ApplyConditions方法
	repo.ExtendedRepository.SetCaller(repo)
	return repo
}

// ApplyConditions 实现ConditionApplier接口，自定义用户登录日志查询条件应用逻辑
func (r *userLoginLogsRepository) ApplyConditions(query *gorm.DB, condition map[string]interface{}) *gorm.DB {
	if condition == nil {
		return query
	}

	// 调试日志：打印接收到的查询条件
	fmt.Printf("用户登录日志仓库自定义条件应用 - 接收到的条件: %+v\n", condition)

	// 处理关键字搜索 - 可以搜索用户名、IP地址、位置等
	if condition["keyword"] != nil && condition["keyword"] != "" {
		keyword := condition["keyword"].(string)
		query = query.Where("user_id IN (SELECT id FROM ly_users WHERE username LIKE ? OR nickname LIKE ?) OR ip_address LIKE ? OR location LIKE ? OR user_agent LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 处理用户ID过滤
	if condition["user_id"] != nil && condition["user_id"] != "" {
		userId := condition["user_id"].(string)
		query = query.Where("user_id = ?", userId)
	}

	// 处理用户名过滤 - 通过关联查询
	if condition["username"] != nil && condition["username"] != "" {
		username := condition["username"].(string)
		query = query.Where("user_id IN (SELECT id FROM ly_users WHERE username LIKE ?)", "%"+username+"%")
	}

	// 处理登录状态过滤
	if condition["login_status"] != nil && condition["login_status"] != "" {
		loginStatus := condition["login_status"].(string)
		query = query.Where("login_status = ?", loginStatus)
	}

	// 处理IP地址过滤
	if condition["ip_address"] != nil && condition["ip_address"] != "" {
		ipAddress := condition["ip_address"].(string)
		query = query.Where("ip_address LIKE ?", "%"+ipAddress+"%")
	}

	// 处理设备类型过滤
	if condition["device_type"] != nil && condition["device_type"] != "" {
		deviceType := condition["device_type"].(string)
		query = query.Where("device_type LIKE ?", "%"+deviceType+"%")
	}

	// 处理地理位置过滤
	if condition["location"] != nil && condition["location"] != "" {
		location := condition["location"].(string)
		query = query.Where("location LIKE ?", "%"+location+"%")
	}

	// 处理登录时间范围
	if condition["start_date"] != nil && condition["start_date"] != "" {
		startDate := condition["start_date"].(string)
		query = query.Where("login_time >= ?", startDate)
	}
	if condition["end_date"] != nil && condition["end_date"] != "" {
		endDate := condition["end_date"].(string)
		query = query.Where("login_time <= ?", endDate)
	}

	// 处理登录类型过滤（如果需要的话）
	if condition["type"] != nil && condition["type"] != "" {
		if loginType, ok := condition["type"].(int); ok && loginType > -999 {
			query = query.Where("type = ?", loginType)
		}
	}

	// 处理登录方式过滤
	if condition["login_type"] != nil && condition["login_type"] != "" {
		loginTypeStr := condition["login_type"].(string)
		query = query.Where("login_type = ?", loginTypeStr)
	}

	// 处理通用时间范围条件
	if condition["created_at_start"] != nil && condition["created_at_start"] != "" {
		query = query.Where("created_at >= ?", condition["created_at_start"])
	}
	if condition["created_at_end"] != nil && condition["created_at_end"] != "" {
		query = query.Where("created_at <= ?", condition["created_at_end"])
	}
	if condition["updated_at_start"] != nil && condition["updated_at_start"] != "" {
		query = query.Where("updated_at >= ?", condition["updated_at_start"])
	}
	if condition["updated_at_end"] != nil && condition["updated_at_end"] != "" {
		query = query.Where("updated_at <= ?", condition["updated_at_end"])
	}

	return query
}
