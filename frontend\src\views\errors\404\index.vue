<template>
    <div class="h-screen flex flex-col items-center justify-center p-4 text-center">
        <h1 class="text-6xl font-bold text-primary mb-4">404</h1>
        <h2 class="text-2xl mb-6">页面未找到</h2>
        <p class="mb-8">您访问的页面不存在或已被移除</p>
        <router-link to="/" class="px-6 py-2 bg-primary text-white rounded-full hover:bg-opacity-90 transition-all">
            返回首页
        </router-link>
    </div>
</template>

<script setup lang="ts">
// 404 页面逻辑
</script>

<style scoped>
.text-primary {
    color: var(--primary-color);
}

.bg-primary {
    background: var(--primary-color);
}
</style>