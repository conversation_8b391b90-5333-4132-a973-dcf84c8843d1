package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

//

type VideoLikeRepository interface {
	base.ExtendedRepository[videos.VideoLike]
	IsLiked(ctx context.Context, userID, videoID string) (bool, error)
	GetLikeVideoList(ctx context.Context, condition map[string]interface{}, orderBy string, pageNo, pageSize int) ([]*videos.VideoLike, int64, error)
}
type videoLikeRepository struct {
	base.ExtendedRepository[videos.VideoLike]
}

func NewVideoLikeRepository(db *gorm.DB) VideoLikeRepository {
	return &videoLikeRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoLike](db),
	}
}
func (r *videoLikeRepository) IsLiked(ctx context.Context, userID, videoID string) (bool, error) {
	like, err := r.FindOneByCondition(ctx, map[string]interface{}{
		"user_id":  userID,
		"video_id": videoID,
	}, "")
	if err != nil {
		return false, err
	}
	return like != nil && like.ID != "", nil
}
func (r *videoLikeRepository) GetLikeVideoList(ctx context.Context, condition map[string]interface{}, orderBy string, pageNo, pageSize int) ([]*videos.VideoLike, int64, error) {
	var likes []*videos.VideoLike
	var total int64
	query := r.GetDBWithContext(ctx).Model(&videos.VideoLike{})
	if condition != nil {
		if condition["user_id"] != nil && condition["user_id"] != "" {
			userID := condition["user_id"].(string)
			query = query.Where("user_id = ?", userID)
		}
		if condition["video_id"] != nil && condition["video_id"] != "" {
			videoID := condition["video_id"].(string)
			query = query.Where("video_id = ?", videoID)
		}
	}
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = query.Order(orderBy).Offset((pageNo - 1) * pageSize).Limit(pageSize).Find(&likes).Error
	if err != nil {
		return nil, 0, err
	}
	return likes, total, nil
}
