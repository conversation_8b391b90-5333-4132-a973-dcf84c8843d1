package books

import (
	"frontapi/internal/models"
)

// BookLike 电子书点赞模型
type BookLike struct {
	models.BaseModel
	BookID string `json:"book_id" gorm:"type:string;size:36;not null;comment:电子书ID"`
	UserID string `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	Status int8   `json:"status" gorm:"default:1;comment:状态：0-取消点赞，1-已点赞"`
}

// TableName 指定表名
func (BookLike) TableName() string {
	return "ly_book_likes"
}