package migrations

import (
	"fmt"

	"gorm.io/gorm"
)

// Migration20241201FixUserLoginLogsTable 修复用户登录日志表结构
type Migration20241201FixUserLoginLogsTable struct{}

// Name 返回迁移名称
func (m *Migration20241201FixUserLoginLogsTable) Name() string {
	return "20241201_fix_user_login_logs_table"
}

// Up 执行迁移
func (m *Migration20241201FixUserLoginLogsTable) Up(db *gorm.DB) error {
	// 检查并添加缺失的 status 字段
	var statusColumnExists bool
	err := db.Raw("SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_name = 'ly_user_login_logs' AND column_name = 'status'").Scan(&statusColumnExists).Error
	if err != nil {
		return fmt.Errorf("检查status字段失败: %w", err)
	}

	if !statusColumnExists {
		err = db.Exec("ALTER TABLE ly_user_login_logs ADD COLUMN status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常'").Error
		if err != nil {
			return fmt.Errorf("添加status字段失败: %w", err)
		}

		// 添加status字段的索引
		err = db.Exec("ALTER TABLE ly_user_login_logs ADD INDEX idx_status (status)").Error
		if err != nil {
			return fmt.Errorf("添加status索引失败: %w", err)
		}
	}

	// 检查并添加缺失的 updated_at 字段
	var updatedAtColumnExists bool
	err = db.Raw("SELECT COUNT(*) > 0 FROM information_schema.columns WHERE table_name = 'ly_user_login_logs' AND column_name = 'updated_at'").Scan(&updatedAtColumnExists).Error
	if err != nil {
		return fmt.Errorf("检查updated_at字段失败: %w", err)
	}

	if !updatedAtColumnExists {
		err = db.Exec("ALTER TABLE ly_user_login_logs ADD COLUMN updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'").Error
		if err != nil {
			return fmt.Errorf("添加updated_at字段失败: %w", err)
		}
	}

	return nil
}

// Down 回滚迁移
func (m *Migration20241201FixUserLoginLogsTable) Down(db *gorm.DB) error {
	// 删除添加的字段
	err := db.Exec("ALTER TABLE ly_user_login_logs DROP COLUMN IF EXISTS status").Error
	if err != nil {
		return fmt.Errorf("删除status字段失败: %w", err)
	}

	err = db.Exec("ALTER TABLE ly_user_login_logs DROP COLUMN IF EXISTS updated_at").Error
	if err != nil {
		return fmt.Errorf("删除updated_at字段失败: %w", err)
	}

	return nil
}
