<template>
    <div class="main-layout" :class="{ 'dark-mode': currentTheme.name === 'dark' }">
        <!-- 顶部导航栏 -->
        <header class="main-header">
            <div class="header-container">
                <!-- 左侧 Logo 和主导航 -->
                <div class="header-left">
                    <div class="logo-container">
                        <router-link to="/">
                            <img src="@/assets/icons/logo.svg" alt="Logo" class="logo-image" />
                        </router-link>
                    </div>
                    <nav class="main-nav">
                        <ul class="nav-list">
                            <li class="nav-item">
                                <router-link to="/" class="nav-link" active-class="active">首页</router-link>
                            </li>
                            <li class="nav-item">
                                <router-link to="/videos" class="nav-link" active-class="active">视频</router-link>
                            </li>
                            <li class="nav-item">
                                <router-link to="/shorts" class="nav-link" active-class="active">短视频</router-link>
                            </li>
                            <li class="nav-item">
                                <router-link to="/community" class="nav-link" active-class="active">社区</router-link>
                            </li>
                            <li class="nav-item">
                                <router-link to="/pictures" class="nav-link" active-class="active">图片</router-link>
                            </li>
                            <li class="nav-item">
                                <router-link to="/manga" class="nav-link" active-class="active">漫画</router-link>
                            </li>
                            <li class="nav-item">
                                <router-link to="/ebook" class="nav-link" active-class="active">电子书</router-link>
                            </li>
                        </ul>
                    </nav>
                </div>

                <!-- 中间搜索栏 -->
                <div class="search-container">
                    <el-input v-model="searchQuery" placeholder="搜索视频、短视频、帖子..." class="search-input"
                        @keyup.enter="handleSearch">
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                        <template #append>
                            <el-dropdown trigger="click" @command="handleSearchTypeChange">
                                <span class="search-type-text">{{ searchTypes[currentSearchType] }}</span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item v-for="(type, index) in searchTypes" :key="index"
                                            :command="index">
                                            {{ type }}
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                    </el-input>
                </div>

                <!-- 右侧用户区域 -->
                <div class="header-right">
                    <div class="user-actions" v-if="!userStore.isLoggedIn">
                        <el-button type="primary" @click="userStore.showAuthDialog('login')">登录</el-button>
                        <el-button @click="userStore.showAuthDialog('register')">注册</el-button>

                        <!-- 主题切换下拉菜单 -->
                        <el-dropdown trigger="click" @command="handleThemeChange">
                            <el-button type="info" plain>
                                <el-icon>
                                    <component :is="currentTheme.name === 'dark' ? 'Moon' : 'Sunny'" />
                                </el-icon>
                                <span style="margin-left: 5px;">主题</span>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-for="theme in availableThemes" :key="theme.name"
                                        :command="theme.name"
                                        :class="{ 'is-active': currentTheme.name === theme.name }">
                                        <div class="theme-item">
                                            <div class="theme-color-preview"
                                                :style="{ backgroundColor: theme.colors.primary }"></div>
                                            <span>{{ theme.displayName }}</span>
                                        </div>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>

                    <div class="user-menu" v-else>
                        <el-dropdown trigger="click" @command="handleUserCommand">
                            <div class="user-avatar-container">
                                <el-avatar :size="40" :src="userStore.userAvatar" />
                                <el-badge :value="unreadNotifications" :max="99" :hidden="!unreadNotifications">
                                    <span class="username">{{ userStore.userDisplayName }}</span>
                                </el-badge>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                                    <el-dropdown-item command="uploads">我的上传</el-dropdown-item>
                                    <el-dropdown-item command="favorites">我的收藏</el-dropdown-item>
                                    <el-dropdown-item command="history">观看历史</el-dropdown-item>
                                    <el-dropdown-item command="messages" divided>
                                        消息中心
                                        <el-badge :value="unreadNotifications" :max="99"
                                            :hidden="!unreadNotifications" />
                                    </el-dropdown-item>
                                    <el-dropdown-item command="settings">账户设置</el-dropdown-item>
                                    <el-dropdown-item command="vip" divided>
                                        <span class="vip-text">会员中心</span>
                                    </el-dropdown-item>
                                    <el-dropdown-item command="points">
                                        <span class="points-text">积分: {{ userPoints }}</span>
                                    </el-dropdown-item>
                                    <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>

                        <!-- 主题切换下拉菜单 -->
                        <el-dropdown trigger="click" @command="handleThemeChange">
                            <el-button type="info" plain>
                                <el-icon>
                                    <component :is="currentTheme.name === 'dark' ? 'Moon' : 'Sunny'" />
                                </el-icon>
                                <span style="margin-left: 5px;">主题</span>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-for="theme in availableThemes" :key="theme.name"
                                        :command="theme.name"
                                        :class="{ 'is-active': currentTheme.name === theme.name }">
                                        <div class="theme-item">
                                            <div class="theme-color-preview"
                                                :style="{ backgroundColor: theme.colors.primary }"></div>
                                            <span>{{ theme.displayName }}</span>
                                        </div>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>

                        <el-button class="upload-btn" type="primary" @click="showUploadDialog">
                            <el-icon>
                                <Upload />
                            </el-icon>
                            上传
                        </el-button>
                    </div>

                    <!-- 移动端菜单按钮 -->
                    <el-button class="mobile-menu-btn" @click="toggleMobileNav">
                        <el-icon>
                            <Menu />
                        </el-icon>
                    </el-button>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <router-view v-slot="{ Component }">
                <transition name="fade" mode="out-in">
                    <keep-alive :include="cachedViews">
                        <component :is="Component" />
                    </keep-alive>
                </transition>
            </router-view>
        </main>

        <!-- 底部区域 -->
        <footer class="main-footer">
            <div class="footer-container">
                <div class="footer-top">
                    <div class="footer-section">
                        <h3>关于我们</h3>
                        <ul>
                            <li><router-link to="/about">公司介绍</router-link></li>
                            <li><router-link to="/contact">联系我们</router-link></li>
                            <li><router-link to="/jobs">招贤纳士</router-link></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>帮助中心</h3>
                        <ul>
                            <li><router-link to="/faq">常见问题</router-link></li>
                            <li><router-link to="/feedback">意见反馈</router-link></li>
                            <li><router-link to="/report">侵权举报</router-link></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>商务合作</h3>
                        <ul>
                            <li><router-link to="/advertise">广告投放</router-link></li>
                            <li><router-link to="/business">商务合作</router-link></li>
                            <li><router-link to="/creators">创作者计划</router-link></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h3>下载APP</h3>
                        <div class="qrcode">
                            <img src="@/assets/qrcode.png" alt="App下载" />
                            <p>扫码下载APP</p>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>© 2023-{{ new Date().getFullYear() }} MyFirm. 保留所有权利</p>
                    <p>
                        <router-link to="/terms">服务条款</router-link> |
                        <router-link to="/privacy">隐私政策</router-link> |
                        <router-link to="/copyright">版权声明</router-link>
                    </p>
                </div>
            </div>
        </footer>

        <!-- 移动端导航 -->
        <MobileNav :is-open="mobileNavOpen" @close="closeMobileNav" />
    </div>
</template>

<script setup lang="ts">
import {
    applyTheme,
    themes,
    type ThemeConfig
} from '@/shared/themes'
import { useUserStore } from '@/stores/user'
import { Menu, Search, Upload } from '@element-plus/icons-vue'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import MobileNav from './MobileNav.vue'

// 路由实例
const router = useRouter()

// 用户状态
const userStore = useUserStore()
const unreadNotifications = ref(0)
const userPoints = ref(0)

// 搜索相关
const searchQuery = ref('')
const searchTypes = ['全站', '视频', '短视频', '图片', '社区', '漫画', '电子书']
const currentSearchType = ref(0)

// 主题相关
const currentTheme = ref<ThemeConfig>(themes.light)

// 将 themes 对象转换为数组，以便在模板中使用
const availableThemes = computed(() => {
    return Object.values(themes)
})

// 缓存的视图
const cachedViews = ref(['Home'])

// 移动导航状态
const mobileNavOpen = ref(false)

// 方法
const handleSearch = () => {
    if (!searchQuery.value.trim()) return

    router.push({
        path: '/search',
        query: {
            q: searchQuery.value,
            type: searchTypes[currentSearchType.value].toLowerCase()
        }
    })
}

const handleSearchTypeChange = (index: number) => {
    currentSearchType.value = index
}

const handleThemeChange = (themeName: string) => {
    const theme = themes[themeName]
    if (theme) {
        currentTheme.value = theme
        applyTheme(theme)
    }
}

const handleUserCommand = (command: string) => {
    switch (command) {
        case 'profile':
            router.push('/user/profile')
            break
        case 'uploads':
            router.push('/user/uploads')
            break
        case 'favorites':
            router.push('/user/favorites')
            break
        case 'history':
            router.push('/user/history')
            break
        case 'messages':
            router.push('/user/messages')
            break
        case 'settings':
            router.push('/user/settings')
            break
        case 'vip':
            router.push('/user/vip')
            break
        case 'points':
            router.push('/user/points')
            break
        case 'logout':
            userStore.logout()
            break
    }
}

const showUploadDialog = () => {
    // 实现上传对话框逻辑
}

const toggleMobileNav = () => {
    mobileNavOpen.value = !mobileNavOpen.value
}

const closeMobileNav = () => {
    mobileNavOpen.value = false
}

// 生命周期钩子
onMounted(() => {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme && themes[savedTheme]) {
        currentTheme.value = themes[savedTheme]
        applyTheme(themes[savedTheme])
    } else {
        applyTheme(themes.light)
    }

    // 获取未读消息数量
    if (userStore.isLoggedIn) {
        // 模拟获取未读消息数量
        unreadNotifications.value = Math.floor(Math.random() * 10)
        userPoints.value = Math.floor(Math.random() * 1000)
    }
})
</script>

<style scoped lang="scss">
.main-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--color-background);
    color: var(--color-text);
    transition: all 0.3s ease;

    // 顶部导航
    .main-header {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: var(--color-surface);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        padding: 0;

        .header-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 20px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .header-left {
                display: flex;
                align-items: center;

                .logo-container {
                    margin-right: 24px;

                    .logo-image {
                        height: 36px;
                        transition: transform 0.3s;

                        &:hover {
                            transform: scale(1.05);
                        }
                    }
                }

                .main-nav {
                    .nav-list {
                        display: flex;
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        .nav-item {
                            margin: 0 8px;

                            .nav-link {
                                display: block;
                                padding: 8px 12px;
                                color: var(--color-text);
                                text-decoration: none;
                                font-weight: 500;
                                border-radius: 4px;
                                transition: all 0.2s;

                                &:hover {
                                    color: var(--color-primary);
                                    background-color: rgba(var(--color-primary-rgb), 0.1);
                                }

                                &.active {
                                    color: var(--color-primary);
                                    font-weight: 600;
                                }
                            }
                        }
                    }
                }
            }

            .search-container {
                flex: 1;
                max-width: 400px;
                margin: 0 20px;

                .search-input {
                    :deep(.el-input__wrapper) {
                        border-radius: 25px;
                        background: var(--color-surface);
                        border: 2px solid var(--color-border);
                        box-shadow: var(--shadow-sm, 0 2px 6px rgba(0, 0, 0, 0.1));
                        transition: all 0.3s ease;

                        &:hover {
                            border-color: var(--color-primary);
                            box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.15));
                            background: var(--gradient-card, var(--color-surface));
                        }

                        &:focus-within {
                            border-color: var(--color-primary);
                            box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb, 142, 45, 226), 0.1);
                            background: var(--gradient-card, var(--color-surface));
                        }
                    }

                    :deep(.el-input__inner) {
                        color: var(--color-text);
                        font-weight: 500;
                        
                        &::placeholder {
                            color: var(--color-textSecondary);
                        }
                    }

                    :deep(.el-input-group__append) {
                        background: var(--gradient-button, var(--color-primary));
                        border: none;
                        border-radius: 0 25px 25px 0;
                        
                        .el-dropdown {
                            .search-type-text {
                                padding: 0 12px;
                                font-size: 14px;
                                color: white;
                                font-weight: 500;
                            }
                        }
                    }

                    :deep(.el-input-group__prepend) {
                        background: transparent;
                        border: none;
                        
                        .el-icon {
                            color: var(--color-primary);
                        }
                    }
                }
            }

            .header-right {
                display: flex;
                align-items: center;
                gap: 16px;

                .user-actions {
                    display: flex;
                    gap: 12px;

                    :deep(.el-button) {
                        border-radius: 20px;
                        font-weight: 500;
                        padding: 8px 20px;
                        transition: all 0.3s ease;
                        
                        &.el-button--primary {
                            background: var(--gradient-button, var(--color-primary));
                            border: none;
                            box-shadow: var(--shadow-sm, 0 2px 6px rgba(0, 0, 0, 0.1));
                            
                            &:hover {
                                background: var(--gradient-buttonHover, var(--color-secondary));
                                transform: translateY(-2px);
                                box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.15));
                            }
                        }
                        
                        &.el-button--default {
                            background: var(--color-surface);
                            border: 2px solid var(--color-border);
                            color: var(--color-text);
                            
                            &:hover {
                                border-color: var(--color-primary);
                                color: var(--color-primary);
                                background: var(--gradient-card, var(--color-surface));
                                transform: translateY(-2px);
                            }
                        }
                        
                        &.el-button--info {
                            background: var(--color-surface);
                            border: 2px solid var(--color-border);
                            color: var(--color-text);
                            
                            &:hover {
                                border-color: var(--color-primary);
                                color: var(--color-primary);
                                background: var(--gradient-card, var(--color-surface));
                                transform: translateY(-2px);
                            }
                        }
                    }
                }

                .user-menu {
                    display: flex;
                    align-items: center;
                    gap: 16px;

                    .user-avatar-container {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        cursor: pointer;
                        padding: 8px 12px;
                        border-radius: 25px;
                        background: var(--color-surface);
                        border: 2px solid var(--color-border);
                        transition: all 0.3s ease;

                        &:hover {
                            border-color: var(--color-primary);
                            background: var(--gradient-card, var(--color-surface));
                            transform: translateY(-2px);
                            box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.15));
                        }

                        .username {
                            font-size: 14px;
                            font-weight: 500;
                            max-width: 80px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            color: var(--color-text);
                        }
                    }

                    .upload-btn {
                        border-radius: 25px;
                        background: var(--gradient-button, var(--color-primary));
                        border: none;
                        font-weight: 600;
                        padding: 10px 24px;
                        box-shadow: var(--shadow-sm, 0 2px 6px rgba(0, 0, 0, 0.1));
                        
                        &:hover {
                            background: var(--gradient-buttonHover, var(--color-secondary));
                            transform: translateY(-2px);
                            box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.15));
                        }
                    }

                    :deep(.el-button) {
                        border-radius: 20px;
                        font-weight: 500;
                        padding: 8px 16px;
                        transition: all 0.3s ease;
                        
                        &.el-button--info {
                            background: var(--color-surface);
                            border: 2px solid var(--color-border);
                            color: var(--color-text);
                            
                            &:hover {
                                border-color: var(--color-primary);
                                color: var(--color-primary);
                                background: var(--gradient-card, var(--color-surface));
                                transform: translateY(-2px);
                            }
                        }
                    }
                }

                :deep(.el-dropdown-menu) {
                    background: var(--color-surface);
                    border: 1px solid var(--color-border);
                    border-radius: 12px;
                    box-shadow: var(--shadow-lg, 0 8px 24px rgba(0, 0, 0, 0.15));
                    
                    .theme-item {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        padding: 8px 12px;
                        transition: all 0.2s ease;

                        .theme-color-preview {
                            width: 16px;
                            height: 16px;
                            border-radius: 50%;
                            border: 2px solid var(--color-border);
                        }
                        
                        &:hover {
                            background: var(--gradient-card, var(--color-surface));
                        }
                    }

                    .is-active {
                        color: var(--color-primary);
                        font-weight: bold;
                        background: var(--gradient-card, rgba(var(--color-primary-rgb, 142, 45, 226), 0.1));
                    }
                }

                .mobile-menu-btn {
                    display: none;
                    padding: 8px;
                    border-radius: 50%;
                    background: var(--color-surface);
                    border: 2px solid var(--color-border);
                    
                    &:hover {
                        border-color: var(--color-primary);
                        background: var(--gradient-card, var(--color-surface));
                    }

                    @media (max-width: 992px) {
                        display: flex;
                    }
                }
            }
        }
    }

    // 主内容区域
    .main-content {
        flex: 1;
        padding: 24px 0;
        max-width: 1440px;
        width: 100%;
        margin: 0 auto;

        // 页面过渡动画
        .fade-enter-active,
        .fade-leave-active {
            transition: opacity 0.3s ease;
        }

        .fade-enter-from,
        .fade-leave-to {
            opacity: 0;
        }
    }

    // 底部区域
    .main-footer {
        background: var(--gradient-footer, linear-gradient(to right, rgba(74, 0, 224, 0.1), rgba(122, 37, 201, 0.08), rgba(142, 45, 226, 0.05)));
        padding: 40px 0 20px;
        margin-top: auto;
        border-top: 1px solid var(--color-border);
        position: relative;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-footer, linear-gradient(135deg, rgba(142, 45, 226, 0.05), rgba(122, 37, 201, 0.03), rgba(74, 0, 224, 0.02)));
            z-index: 0;
        }

        .footer-container {
            max-width: 1440px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 1;

            .footer-top {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                margin-bottom: 40px;

                .footer-section {
                    flex: 1;
                    min-width: 200px;
                    margin-bottom: 20px;
                    padding: 20px;
                    background: var(--gradient-card, rgba(255, 255, 255, 0.05));
                    border-radius: 12px;
                    border: 1px solid var(--color-border);
                    backdrop-filter: blur(10px);
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-5px);
                        box-shadow: var(--shadow-lg, 0 8px 24px rgba(0, 0, 0, 0.15));
                        background: var(--gradient-cardHover, rgba(255, 255, 255, 0.1));
                    }

                    h3 {
                        font-size: 18px;
                        font-weight: 600;
                        margin-bottom: 16px;
                        color: var(--color-text);
                        background: var(--gradient-primary, var(--color-primary));
                        background-clip: text;
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        text-align: center;
                        padding-bottom: 8px;
                        border-bottom: 2px solid var(--color-border);
                    }

                    ul {
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        li {
                            margin-bottom: 12px;

                            a {
                                color: var(--color-textSecondary);
                                text-decoration: none;
                                transition: all 0.3s ease;
                                padding: 8px 12px;
                                border-radius: 8px;
                                display: block;
                                font-weight: 500;

                                &:hover {
                                    color: var(--color-primary);
                                    background: var(--gradient-card, rgba(var(--color-primary-rgb, 142, 45, 226), 0.1));
                                    transform: translateX(8px);
                                }
                            }
                        }
                    }

                    .qrcode {
                        text-align: center;
                        padding: 16px;
                        background: var(--gradient-card, rgba(255, 255, 255, 0.1));
                        border-radius: 12px;
                        border: 1px solid var(--color-border);

                        img {
                            width: 120px;
                            height: 120px;
                            margin-bottom: 12px;
                            border-radius: 12px;
                            border: 2px solid var(--color-border);
                            transition: all 0.3s ease;
                            
                            &:hover {
                                transform: scale(1.05);
                                border-color: var(--color-primary);
                            }
                        }

                        p {
                            font-size: 14px;
                            color: var(--color-textSecondary);
                            font-weight: 500;
                            margin: 0;
                        }
                    }
                }
            }

            .footer-bottom {
                border-top: 2px solid var(--color-border);
                padding: 24px 20px 20px;
                text-align: center;
                margin-top: 20px;
                background: var(--gradient-card, rgba(255, 255, 255, 0.05));
                border-radius: 12px;
                backdrop-filter: blur(10px);

                p {
                    margin: 12px 0;
                    font-size: 14px;
                    color: var(--color-textSecondary);
                    font-weight: 500;
                    line-height: 1.6;

                    a {
                        color: var(--color-textSecondary);
                        text-decoration: none;
                        transition: all 0.3s ease;
                        padding: 4px 8px;
                        border-radius: 6px;
                        margin: 0 4px;

                        &:hover {
                            color: var(--color-primary);
                            background: var(--gradient-card, rgba(var(--color-primary-rgb, 142, 45, 226), 0.1));
                            transform: translateY(-2px);
                        }
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .main-layout {
        .main-header .header-container {
            .header-left .main-nav .nav-list .nav-item {
                margin: 0 4px;

                .nav-link {
                    padding: 8px;
                }
            }

            .search-container {
                max-width: 300px;
            }
        }
    }
}

@media (max-width: 992px) {
    .main-layout {
        .main-header .header-container {
            .header-left .main-nav {
                display: none;
            }

            .search-container {
                max-width: 400px;
            }
        }
    }
}

@media (max-width: 768px) {
    .main-layout {
        .main-header .header-container {
            .search-container {
                max-width: 200px;
                margin: 0 10px;
            }

            .header-right {
                .theme-selector {
                    display: flex;
                    /* 确保在移动端也显示主题切换按钮 */
                }

                .user-actions {
                    display: flex;
                }

                .user-menu {
                    .user-avatar-container .username {
                        display: none;
                    }

                    .upload-btn {
                        display: none;
                    }
                }
            }
        }

        .main-footer .footer-container .footer-top {
            flex-direction: column;

            .footer-section {
                margin-bottom: 30px;
            }
        }
    }
}

@media (max-width: 576px) {
    .main-layout {
        .main-header .header-container {
            padding: 0 10px;

            .search-container {
                display: none;
            }
        }

        .main-content {
            padding: 16px 10px;
        }
    }
}

// 深色模式特定样式
.dark-mode {
    .main-header {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .vip-text {
        color: #FFD700;
    }

    .points-text {
        color: #FF6B6B;
    }
}
</style>