# 用户列表条件映射修复

## 问题描述

前端发送的查询参数与后端接收的条件映射不匹配，导致自定义的 `ApplyConditions` 方法无法正确处理查询条件。

## 前端发送的参数结构

```javascript
const params = {
  data: {
    keyword: searchForm.keyword,
    status: searchForm.status,
    user_type: searchForm.user_type,
    is_content_creator: searchForm.is_content_creator,
    start_date: searchForm.start_date,
    end_date: searchForm.end_date
  },
  page: {
    pageNo: pagination.page,
    pageSize: pagination.pageSize
  }
};
```

## 后端原有的条件映射

```go
condition := map[string]interface{}{
    "keyword":        keyword,
    "status":         status,
    "reg_time_start": regTimeStart,
    "reg_time_end":   regTimeEnd,
}
```

## 修复后的条件映射

```go
// 查询参数
keyword := reqInfo.Get("keyword").GetString()
status := -999
err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)

// 获取内容创作者过滤条件
isContentCreator := -999
_ = c.GetIntegerValueWithDataWrapper(ctx, "is_content_creator", &isContentCreator)

// 获取时间范围参数 - 前端发送的是start_date和end_date
startDate := reqInfo.Get("start_date").GetString()
endDate := reqInfo.Get("end_date").GetString()

// 构建查询条件，映射到用户仓库中ApplyConditions期望的字段名
condition := map[string]interface{}{
    "keyword":            keyword,
    "status":             status,
    "user_type":          userType,
    "is_content_creator": isContentCreator,
    "reg_time_start":     startDate, // 映射前端的start_date到reg_time_start
    "reg_time_end":       endDate,   // 映射前端的end_date到reg_time_end
}
```

## 用户仓库中的ApplyConditions处理

用户仓库中的 `ApplyConditions` 方法已经正确处理了以下条件：

1. **关键字搜索** (`keyword`): 在用户名、昵称、邮箱、手机号、简介中搜索
2. **状态过滤** (`status`): 过滤用户状态，-999表示不过滤
3. **用户类型过滤** (`user_type`): 过滤用户类型（普通用户、VIP用户、管理员等），-999表示不过滤
4. **内容创作者过滤** (`is_content_creator`): 过滤是否为内容创作者，-999表示不过滤
5. **注册时间范围** (`reg_time_start`, `reg_time_end`): 按注册时间范围过滤

## 修复的关键点

1. **字段映射**: 前端的 `start_date` 和 `end_date` 映射到后端的 `reg_time_start` 和 `reg_time_end`
2. **新增字段**: 添加了对 `user_type` 和 `is_content_creator` 字段的处理
3. **类型安全**: 使用类型断言和范围检查确保数据安全
4. **默认值处理**: 使用 -999 作为"不过滤"的标识

## 验证方法

1. **调试日志**: 在 `ApplyConditions` 方法中添加了调试日志，可以查看接收到的条件
2. **前端测试**: 在前端页面中输入不同的筛选条件，观察返回结果
3. **SQL查询**: 检查生成的SQL查询是否包含正确的WHERE条件

## 测试场景

1. **关键字搜索**: 输入用户名、邮箱等关键字进行搜索
2. **状态筛选**: 选择不同的用户状态进行过滤
3. **用户类型筛选**: 选择普通用户、VIP用户、管理员等类型进行过滤
4. **创作者筛选**: 筛选内容创作者或非创作者
5. **时间范围**: 选择注册时间范围进行过滤
6. **组合条件**: 多个条件同时使用

## 相关文件

- `frontapi/internal/admin/users/user_controller.go` - 用户控制器，条件映射逻辑
- `frontapi/internal/repository/users/user_repository.go` - 用户仓库，自定义条件应用逻辑
- `backend/src/views/users/list/index.vue` - 前端用户列表页面

## 注意事项

1. 确保前后端的字段命名保持一致
2. 处理空值和默认值的情况
3. 注意类型转换的安全性
4. 记得移除调试日志（生产环境中） 