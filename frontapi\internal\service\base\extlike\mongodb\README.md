# MongoDB点赞适配器

MongoDB点赞适配器是一个完整的基于MongoDB的点赞服务实现，提供了高性能、可扩展的点赞功能。

## 功能特性

### 🚀 核心功能
- **基础点赞操作**: Like、Unlike、IsLiked、GetLikeCount
- **批量操作**: 支持批量点赞和批量查询，提高性能
- **事务支持**: 使用MongoDB事务确保数据一致性
- **聚合查询**: 利用MongoDB聚合管道进行复杂统计
- **索引优化**: 自动创建优化的复合索引

### 📊 高级功能
- **排行榜**: 实时计算和维护热门内容排行
- **统计分析**: 用户点赞统计、项目点赞统计、全局统计
- **趋势分析**: 基于时间窗口的趋势计算
- **历史查询**: 支持时间范围的历史数据查询

### ⚙️ 配置特性
- **灵活配置**: 支持多种MongoDB配置选项
- **连接池管理**: 高效的连接池配置
- **读写偏好**: 可配置的读写偏好和一致性级别
- **数据保留**: 支持TTL和数据自动过期
- **重试机制**: 内置重试和错误处理

## 文件结构

```
mongodb/
├── adapter.go              # 主适配器，组合所有功能模块
├── factory.go              # 工厂方法，处理连接和初始化
├── mongo_client.go         # MongoDB客户端包装器
├── like_operations.go      # 点赞基础操作
├── query_operations.go     # 查询操作
├── ranking_operations.go   # 排行榜操作
├── stats.go               # 统计功能
└── README.md              # 说明文档
```

## 快速开始

### 1. 基本使用

```go
package main

import (
    "context"
    "log"
    
    "frontapi/internal/service/base/extlike/mongodb"
)

func main() {
    // 创建适配器
    adapter, err := mongodb.CreateAdapterFromURI(
        "mongodb://localhost:27017",
        "like_service",
        "videos", // 项目类型
    )
    if err != nil {
        log.Fatal(err)
    }
    defer adapter.Cleanup(context.Background())

    ctx := context.Background()
    
    // 点赞操作
    err = adapter.Like(ctx, "user123", "video", "video456")
    if err != nil {
        log.Printf("点赞失败: %v", err)
    }

    // 检查点赞状态
    liked, _ := adapter.IsLiked(ctx, "user123", "video", "video456")
    log.Printf("点赞状态: %v", liked)

    // 获取点赞数
    count, _ := adapter.GetLikeCount(ctx, "video", "video456")
    log.Printf("点赞数: %d", count)
}
```

### 2. 批量操作

```go
// 批量点赞
operations := []*types.LikeOperation{
    {
        UserID:    "user1",
        ItemID:    "video1",
        ItemType:  "video",
        Action:    "like",
        Timestamp: time.Now(),
    },
    // ... 更多操作
}

err := adapter.BatchLike(ctx, operations)

// 批量查询点赞状态
items := map[string]string{
    "video1": "video",
    "video2": "video",
}

statuses, err := adapter.BatchGetLikeStatus(ctx, "user123", items)
counts, err := adapter.BatchGetLikeCounts(ctx, items)
```

### 3. 自定义配置

```go
config := &mongodb.MongoConfig{
    URI:             "mongodb://localhost:27017",
    Database:        "my_like_service",
    MaxPoolSize:     100,
    MinPoolSize:     10,
    ConnectTimeout:  10 * time.Second,
    Timeout:         5 * time.Second,
    ReadPreference:  "primary",
    WriteConcern:    "majority",
    ReadConcern:     "majority",
    LikeCollection:  "likes",
    StatsCollection: "like_stats",
    EnableIndexes:   true,
    CreateIndexes:   true,
    BatchSize:       1000,
    EnablePipeline:  true,
    DataRetention:   365 * 24 * time.Hour,
    EnableTTL:       false,
    ConsistencyLevel: "strong",
    RetryWrites:     true,
    RetryReads:      true,
}

factory := mongodb.NewFactory()
adapter, err := factory.CreateAdapter(config, "articles")
```

## 数据模型

### 点赞记录 (LikeRecord)
```go
type LikeRecord struct {
    ID        string                 `bson:"_id,omitempty"`
    UserID    string                 `bson:"user_id"`
    ItemType  string                 `bson:"item_type"`
    ItemID    string                 `bson:"item_id"`
    Timestamp time.Time              `bson:"timestamp"`
    Status    string                 `bson:"status"`      // "liked" or "unliked"
    Source    string                 `bson:"source,omitempty"`
    Metadata  map[string]interface{} `bson:"metadata,omitempty"`
    Version   int64                  `bson:"version"`
}
```

### 数据库集合

- **likes**: 点赞记录集合
- **like_stats**: 统计数据集合
- **like_trends**: 趋势数据集合
- **like_rankings**: 排行榜集合
- **like_operations**: 操作记录集合（可选）

### 索引策略

自动创建的索引：

```javascript
// 点赞记录索引
db.likes.createIndex({ "user_id": 1, "item_id": 1, "item_type": 1 }, { unique: true })
db.likes.createIndex({ "item_id": 1, "item_type": 1 })
db.likes.createIndex({ "user_id": 1, "timestamp": -1 })
db.likes.createIndex({ "timestamp": -1 })

// 统计数据索引
db.like_stats.createIndex({ "item_id": 1, "item_type": 1 }, { unique: true })
db.like_stats.createIndex({ "like_count": -1 })

// 排行榜索引
db.like_rankings.createIndex({ "item_type": 1, "score": -1 })
db.like_rankings.createIndex({ "item_type": 1, "rank": 1 })
```

## 性能特性

### 🔥 高性能设计
- **批量操作**: 支持批量插入和更新，减少网络往返
- **聚合管道**: 使用MongoDB聚合进行复杂查询
- **索引优化**: 根据查询模式创建优化索引
- **连接池**: 高效的连接池管理

### 📈 可扩展性
- **水平分片**: 支持MongoDB分片集群
- **读写分离**: 支持读写偏好配置
- **异步处理**: 支持异步更新和后台任务

### 🛡️ 可靠性
- **事务支持**: ACID事务保证数据一致性
- **错误重试**: 内置重试机制
- **连接恢复**: 自动连接恢复
- **数据验证**: 完整的数据验证

## 监控指标

适配器提供丰富的监控指标：

```go
// 服务指标
metrics, err := adapter.GetServiceMetrics(ctx)
// 性能指标
perfMetrics, err := adapter.GetPerformanceMetrics(ctx, timeRange)
// 缓存统计
cacheStats, err := adapter.GetCacheStats(ctx)
```

## 配置选项详解

### 连接配置
- `URI`: MongoDB连接字符串
- `Database`: 数据库名称
- `Username`/`Password`: 认证信息
- `AuthSource`: 认证数据库

### 性能配置
- `MaxPoolSize`/`MinPoolSize`: 连接池大小
- `ConnectTimeout`/`Timeout`: 超时设置
- `BatchSize`: 批量操作大小
- `EnablePipeline`: 是否启用管道

### 一致性配置
- `ReadPreference`: 读偏好 (primary, secondary等)
- `WriteConcern`: 写关注 (majority, 1等)
- `ReadConcern`: 读关注 (local, majority等)
- `ConsistencyLevel`: 应用级一致性 (strong, soft)

### 数据管理
- `DataRetention`: 数据保留时间
- `EnableTTL`: 是否启用TTL
- `TTLField`: TTL字段名

## 最佳实践

### 1. 索引优化
- 根据查询模式创建复合索引
- 定期分析查询性能
- 避免过多的单字段索引

### 2. 批量操作
- 优先使用批量操作提高性能
- 控制批量大小避免内存问题
- 合理设置批量超时时间

### 3. 事务使用
- 只在必要时使用事务
- 保持事务操作简短
- 合理设置事务超时

### 4. 监控告警
- 监控连接池使用率
- 关注查询响应时间
- 设置错误率告警

## 故障排除

### 常见问题

1. **连接超时**
   - 检查MongoDB服务状态
   - 调整连接超时设置
   - 验证网络连通性

2. **性能问题**
   - 检查索引使用情况
   - 分析慢查询日志
   - 优化聚合管道

3. **数据不一致**
   - 检查一致性级别设置
   - 验证事务使用
   - 确认重试机制

### 日志级别
```go
// 启用详细日志
config.LogLevel = "debug"
```

## 版本兼容性

- **MongoDB**: 4.4+
- **Go**: 1.19+
- **Driver**: go.mongodb.org/mongo-driver v1.11+

## 贡献指南

欢迎贡献代码和问题报告！请遵循以下指南：

1. 提交前运行所有测试
2. 遵循代码规范
3. 添加适当的文档
4. 提供测试用例

## 许可证

本项目采用 MIT 许可证。 