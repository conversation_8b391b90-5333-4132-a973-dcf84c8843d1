# 缓存系统目录结构

本文档介绍 frontapi 项目中缓存系统的目录结构和各个文件的功能。

## 目录结构

```
frontapi/pkg/cache/v2/
├── cache.go            # 主入口文件，包含所有核心接口和类型定义
├── file_adapter.go     # 文件缓存适配器实现
└── examples/
    └── cache_integration_example.go  # 使用示例
```

## 文件说明

### cache.go

主入口文件，包含以下内容：

- 接口定义：`CacheManager`、`CacheAdapter` 等
- 类型定义：`CacheConfig`、`RedisConfig`、`FileConfig`、`CacheStats` 等
- 管理器实现：`Manager` 结构体及其方法
- Redis适配器实现：`RedisAdapter` 结构体及其方法
- 泛型适配器实现：`TypedAdapter` 结构体及其方法
- 工具函数：环境变量读取、配置加载等
- 便捷创建函数：`New`、`NewRedis`、`NewFile`、`NewHybrid` 等

### file_adapter.go

文件缓存适配器实现，包含以下内容：

- `FileAdapter` 结构体及其方法
- 文件缓存的读写、删除、清空等操作
- 文件路径生成和管理
- 统计信息收集

## 核心接口

### CacheManager

缓存管理器接口，提供高级缓存操作：

```go
type CacheManager interface {
    // 获取缓存
    Get(key string) (interface{}, error)
    // 设置缓存
    Set(key string, value interface{}, ttl time.Duration) error
    // 删除缓存
    Delete(key string) error
    // 清空缓存
    Clear() error
    // 关闭缓存连接
    Close() error
    // 创建类型安全的缓存适配器
    CreateTypedAdapter(t interface{}) interface{}
}
```

### CacheAdapter

缓存适配器接口，提供底层缓存操作：

```go
type CacheAdapter interface {
    // 基本操作
    Get(ctx context.Context, key string) ([]byte, error)
    Set(ctx context.Context, key string, value []byte, expiration time.Duration) error
    Delete(ctx context.Context, key string) error
    Exists(ctx context.Context, key string) (bool, error)
    Clear(ctx context.Context) error
    
    // 连接管理
    Close() error
    
    // 统计和元信息
    Stats() *CacheStats
    Name() string
    Type() string
}
```

### TypedAdapter

类型安全的泛型适配器：

```go
type TypedAdapter[T any] struct {
    manager *Manager
    ctx     context.Context
}

// 方法包括：
// Get(key string) (T, error)
// Set(key string, value T, ttl time.Duration) error
// Delete(key string) error
// Clear() error
// GetOrSet(key string, setter func() (T, error), ttl time.Duration) (T, error)
```

## 配置结构

### CacheConfig

缓存配置结构体：

```go
type CacheConfig struct {
    // DefaultTTL 默认缓存时间
    DefaultTTL time.Duration
    // Redis配置
    Redis *RedisConfig
    // File配置
    File *FileConfig
}
```

### RedisConfig

Redis配置结构体：

```go
type RedisConfig struct {
    Host     string
    Port     int
    Password string
    DB       int
}
```

### FileConfig

文件缓存配置结构体：

```go
type FileConfig struct {
    Path string
}
```

## 统计信息

### CacheStats

缓存统计信息结构体：

```go
type CacheStats struct {
    Hits      int64         // 命中次数
    Misses    int64         // 未命中次数
    Sets      int64         // 设置次数
    Deletes   int64         // 删除次数
    Clears    int64         // 清空次数
    Keys      int64         // 键数量
    Size      int64         // 占用空间
    Uptime    time.Duration // 运行时间
    StartTime time.Time     // 启动时间
}
```

## 实现细节

### Manager

缓存管理器实现，支持多种缓存后端：

- 可以同时使用 Redis 和文件缓存
- 优先从 Redis 获取数据，如果未命中则尝试从文件缓存获取
- 设置数据时同时写入 Redis 和文件缓存（如果已配置）
- 支持创建类型安全的泛型适配器

### RedisAdapter

Redis 缓存适配器实现：

- 使用 go-redis/redis/v8 包与 Redis 服务器通信
- 支持连接测试、统计信息收集
- 实现了所有 CacheAdapter 接口方法

### FileAdapter

文件缓存适配器实现：

- 使用文件系统存储缓存数据
- 支持过期时间（通过文件内容前缀存储）
- 使用 Base64 编码键名，避免文件名冲突和无效字符
- 实现了所有 CacheAdapter 接口方法

### TypedAdapter

类型安全的泛型适配器实现：

- 使用 Go 1.18+ 泛型特性
- 支持任意类型的缓存操作，避免类型断言
- 提供 GetOrSet 方法简化缓存逻辑 