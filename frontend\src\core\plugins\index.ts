/**
 * 核心插件管理器
 */

import type { App } from 'vue';
import { setupI18n } from './i18n';
import createThemePlugin from './theme';

// 插件管理器
class PluginManager {
    private plugins: Array<{
        name: string;
        plugin: any;
        options?: any;
    }> = [];

    /**
     * 注册插件
     */
    register(name: string, plugin: any, options?: any) {
        this.plugins.push({ name, plugin, options });
        return this;
    }

    /**
     * 应用所有插件
     */
    async apply(app: App) {
        for (const { name, plugin, options } of this.plugins) {
            try {
                // 如果是函数，直接调用
                if (typeof plugin === 'function') {
                    await plugin(app, options);
                }
                // 如果有install方法，使用app.use
                else if (plugin && typeof plugin.install === 'function') {
                    app.use(plugin, options);
                }
                // 其他情况，尝试直接使用
                else {
                    app.use(plugin, options);
                }
            } catch (error) {
                console.error(`Failed to apply plugin ${name}:`, error);
            }
        }
    }

    /**
     * 获取已注册的插件
     */
    getPlugins() {
        return [...this.plugins];
    }
}

// 创建插件管理器实例
export const pluginManager = new PluginManager();

// 默认插件注册
pluginManager
    .register('i18n', setupI18n)
    .register('theme', createThemePlugin());

// 初始化应用插件
export async function setupPlugins(app: App) {
    await pluginManager.apply(app);
}
