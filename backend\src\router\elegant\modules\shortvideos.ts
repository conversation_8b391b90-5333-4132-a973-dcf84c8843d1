import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from '@/locales';

const routes: GeneratedRoute[] = [
  {
    name: 'shortvideos',
    path: '/shortvideos',
    component: 'layout.base',
    meta: {
      title: 'shortvideos',
      i18nKey: 'route.shortvideos',
      icon: 'lucide:film',
      order: 4
    },
    children: [
      // 短视频分类页面
      {
        name: 'shortvideos_category',
        path: '/shortvideos/category',
        component: 'view.shortvideos_category',
        meta: {
          title: 'shortvideos_category',
          i18nKey: 'route.shortvideos_category',
          icon: 'lucide:folder',
          order: 1
        }
      },
      // 短视频列表页面
      {
        name: 'shortvideos_list',
        path: '/shortvideos/list',
        component: 'view.shortvideos_list',
        meta: {
          title: 'shortvideos_list',
          i18nKey: 'route.shortvideos_list',
          icon: 'lucide:list',
          order: 2
        }
      },
      // 短视频评论页面
      {
        name: 'shortvideos_comment',
        path: '/shortvideos/comment',
        component: 'view.shortvideos_comment',
        meta: {
          title: 'shortvideos_comment',
          i18nKey: 'route.shortvideos_comment',
          icon: 'lucide:message-circle',
          order: 3
        }
      }
    ]
  }
];

export default routes;
