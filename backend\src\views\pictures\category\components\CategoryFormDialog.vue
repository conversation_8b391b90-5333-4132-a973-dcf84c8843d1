<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" :close-on-click-modal="false"
        :close-on-press-escape="true" destroy-on-close>
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-position="right" status-icon>
            <el-form-item label="分类名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入分类名称" />
            </el-form-item>
            <el-form-item label="分类编码" prop="code">
                <el-input v-model="form.code" placeholder="请输入分类编码">
                    <template #append>
                        <el-button @click="handleGenerateCode">生成编码</el-button>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item label="状态" prop="status" v-if="type === 'edit'">
                <el-radio-group v-model="form.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="描述" prop="description">
                <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入分类描述" />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" :loading="loading" @click="handleSubmit">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { createPictureCategory, updatePictureCategory } from '@/service/api/pictures/pictures';
import type { CreatePictureCategoryRequest, PictureCategory, UpdatePictureCategoryRequest } from '@/types/pictures';
import { handleApiError } from '@/utils/errorHandler';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    type: {
        type: String as () => 'add' | 'edit',
        default: 'add'
    },
    categoryData: {
        type: Object as () => PictureCategory | null,
        default: null
    }
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref<FormInstance>();
// 加载状态
const loading = ref(false);

// 计算属性：对话框是否可见
const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

// 计算属性：对话框标题
const dialogTitle = computed(() => props.type === 'add' ? '添加分类' : '编辑分类');

// 表单数据
const form = reactive({
    id: '',
    name: '',
    code: '',
    status: 1,
    description: ''
});

// 表单验证规则
const rules = reactive<FormRules>({
    name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 2, max: 50, message: '分类名称长度应在2-50个字符之间', trigger: 'blur' }
    ]
});

// 监听对话框可见性变化
watch(
    () => props.visible,
    (visible) => {
        if (visible) {
            if (props.type === 'edit' && props.categoryData) {
                // 编辑模式，填充表单数据
                const data = props.categoryData;
                form.id = data.id;
                form.name = data.name;
                form.code = data.code;
                form.status = data.status;
                form.description = data.description || '';
            } else {
                // 添加模式，重置表单
                form.id = '';
                form.name = '';
                form.code = '';
                form.status = 1;
                form.description = '';
            }
        }
    },
    { immediate: true }
);
const handleGenerateCode = () => {
    form.code = Math.random().toString(36).substring(2, 15);
};
// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false;
};

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (!valid) {
            ElMessage.warning('请完善表单信息');
            return;
        }

        loading.value = true;
        try {
            if (props.type === 'add') {
                // 创建分类
                const createData: CreatePictureCategoryRequest = {
                    name: form.name,
                    code: form.code,
                    description: form.description
                };

                await createPictureCategory({
                    data: createData
                });
                ElMessage.success('创建分类成功');
            } else {
                // 更新分类
                const updateData: UpdatePictureCategoryRequest & { id: string } = {
                    name: form.name,
                    code: form.code,
                    description: form.description,
                    status: form.status,
                    id: form.id
                };

                await updatePictureCategory({
                    data: updateData
                });
                ElMessage.success('更新分类成功');
            }
            // 关闭对话框并通知父组件成功
            dialogVisible.value = false;
            emit('success');
        } catch (error) {
            handleApiError(error, props.type === 'add' ? '创建分类失败' : '更新分类失败');
        } finally {
            loading.value = false;
        }
    });
};
</script>

<style scoped lang="scss">
.el-dialog {
    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        gap: 12px;
    }

    :deep(.el-dialog__body) {
        padding: 0 !important;
    }
}
</style>