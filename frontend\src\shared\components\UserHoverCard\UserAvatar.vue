<template>
  <div class="user-avatar-wrapper">
    <el-avatar 
      :size="size" 
      :src="avatar" 
      class="user-avatar"
      :class="{ 'has-border': showBorder }"
    >
      {{ fallbackText }}
    </el-avatar>
    
    <!-- 在线状态指示器 -->
    <div v-if="isOnline" class="online-indicator"></div>
    
    <!-- 认证标识 -->
    <div v-if="isVerified" class="verified-badge">
      <el-icon><CircleCheckFilled /></el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { CircleCheckFilled } from '@element-plus/icons-vue'

interface Props {
  avatar: string
  username?: string
  nickname?: string
  size?: number
  isOnline?: boolean
  isVerified?: boolean
  showBorder?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 48,
  isOnline: false,
  isVerified: false,
  showBorder: true
})

const fallbackText = computed(() => {
  const name = props.nickname || props.username || ''
  return name.charAt(0).toUpperCase()
})
</script>

<style scoped lang="scss">
.user-avatar-wrapper {
  position: relative;
  display: inline-block;

  .user-avatar {
    transition: all 0.3s ease;
    
    &.has-border {
      border: 2px solid #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #52c41a;
    border: 2px solid #fff;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .verified-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: #1890ff;
    border: 2px solid #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

    .el-icon {
      font-size: 10px;
      color: #fff;
    }
  }
}
</style> 