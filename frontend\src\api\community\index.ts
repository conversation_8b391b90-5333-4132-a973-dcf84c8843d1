import { post, page, postPage } from '@/shared/composables'
import { corePost, coreGetPage, corePostPageList } from '@/shared/composables'
import type { PageResponse, RequestParams, PageRequestParams } from '@/shared/composables'

// ==================== 帖子相关API ====================

// 获取帖子详情
export const getPostDetail = (params: any) => {
  return corePost('/community/posts/getPostDetail', params)
}

// 获取帖子列表
export const getPosts = (params: any) => {
  return corePostPageList('/community/posts/getPostList', params)
}

// 点赞/取消点赞帖子
export const togglePostLike = (params: any) => {
  return corePost('/community/posts/toggleLike', params)
}

// ==================== 评论相关API ====================

// 获取帖子评论列表
export const getPostComments = (params: any) => {
  return corePostPageList('/community/post/comments/getPostCommentList', params)
}

// 添加评论
export const addPostComment = (params: any) => {
  return corePost('/community/post/comments/addComment', params)
}

// 删除评论
export const deleteComment = (params: any) => {
  return corePost('/community/post/comments/deleteComment', params)
}

// 点赞/取消点赞评论
export const toggleCommentLike = (params: any) => {
  return corePost('/community/post/comments/toggleLike', params)
}

// 回复评论
export const replyComment = (params: any) => {
  return corePost('/community/post/comments/replyComment', params)
}

// 获取评论详情
export const getCommentDetail = (params: any) => {
  return corePost('/community/post/comments/getCommentDetail', params)
}

// ==================== 用户相关API ====================

// 获取推荐创作者用户
export const getRecommendedCreatorUsers = (params: any) => {
  return corePost('/community/posts/getRecommendedCreatorUsers', params)
}

// 获取推荐关注用户
export const getRecommendedFollowUsers = (params: any) => {
  return corePost('/community/posts/getRecommendedFollowUsers', params)
}

// 获取帖子的关注用户列表
export const getPostFollowingUsers = (params: any) => {
  return corePost('/community/posts/getPostFollowingUsers', params)
}


// 获取热门用户列表
export const getTopHotUserList = (params: any) => {
  return corePost('/community/posts/getTopHotUserList', params)
}

// ==================== 推荐内容API ====================

// 获取推荐视频
export const getRecommendedVideos = (params: any) => {
  return corePost('/community/posts/getRecommendedVideos', params)
}

// 获取推荐用户列表
export const getRecommendedUsers = (params: any) => {
  return corePost('/community/posts/getRecommendedUsers', params)
}

// 获取关注用户列表
export const getFollowingUsers = (params: any) => {
  return corePost('/community/posts/getFollowingUsers', params)
}
