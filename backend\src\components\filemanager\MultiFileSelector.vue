<template>
  <div class="multi-file-selector">
    <!-- 已选择的文件显示区域 -->
    <div class="selected-files" v-if="modelValue && modelValue.length > 0">
      <div class="selected-files-grid" ref="sortableContainer">
        <div 
          v-for="(file, index) in modelValue" 
          :key="index" 
          class="file-item"
          :data-index="index"
          @click="previewImage(file)"
        >
          <div class="file-thumbnail">
            <el-image 
              :src="file.url" 
              fit="cover"
              loading="lazy"
              :preview-src-list="[file.url]"
              :initial-index="0"
              hide-on-click-modal
            >
              <template #error>
                <el-icon><picture /></el-icon>
              </template>
            </el-image>
            <div class="file-actions">
              <el-button
                type="danger"
                circle
                size="small"
                @click.stop="removeFile(index)"
                :icon="Delete"
              ></el-button>
            </div>
          </div>
          <div class="file-name" :title="file.name">{{ file.name }}</div>
        </div>
      </div>
    </div>

    <!-- 提示和选择按钮 -->
    <div class="file-selector-actions">
      <div class="empty-tip" v-if="!modelValue || modelValue.length === 0">
        <el-icon><picture /></el-icon>
        <span>请选择{{ limit > 1 ? '多个' : '' }}图片文件</span>
      </div>
      
      <div class="action-buttons">
        <el-button type="primary" @click="openFileManager">
          <el-icon><folder-opened /></el-icon>
          选择{{ limit > 1 ? '多个' : '' }}图片
        </el-button>
        <el-button v-if="modelValue && modelValue.length > 0" @click="clearSelection">
          <el-icon><delete /></el-icon>
          清空选择
        </el-button>
      </div>
      
      <div class="help-text" v-if="limit > 1">
        已选择 {{ modelValue ? modelValue.length : 0 }}/{{ limit > 0 ? limit : '∞' }} 个文件
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="图片预览"
      width="50%"
      center
    >
      <div class="preview-container" v-if="previewFile">
        <el-image 
          :src="previewFile.url" 
          fit="contain"
          style="width: 100%; max-height: 60vh;"
        />
        <div class="preview-info">
          <div class="info-item">
            <span class="label">文件名:</span>
            <span class="value">{{ previewFile.name }}</span>
          </div>
          <div class="info-item" v-if="previewFile.size">
            <span class="label">大小:</span>
            <span class="value">{{ formatFileSize(previewFile.size) }}</span>
          </div>
          <div class="info-item" v-if="previewFile.width && previewFile.height">
            <span class="label">尺寸:</span>
            <span class="value">{{ previewFile.width }} x {{ previewFile.height }} px</span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 文件选择器对话框 -->
    <el-dialog
      v-model="selectorVisible"
      title="选择文件"
      width="80%"  class="file-selector-dialog"
      destroy-on-close
    >
      <file-manager
        :mode="'selector'"
        :multiple="limit !== 1"
        :initial-file-type="fileType || 'pictures'"
        @select="handleFilesSelected"
        @cancel="selectorVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Picture, Delete, FolderOpened } from '@element-plus/icons-vue';
import FileManager from './FileManager.vue';
import Sortable from 'sortablejs';

// 文件项接口定义
interface FileItem {
  name: string;
  url: string;
  path: string;
  size?: number;
  width?: number;
  height?: number;
  type?: string;
  extension?: string;
  is_dir?: boolean;
  [key: string]: any; // 允许其他属性
}

// 定义属性
const props = defineProps({
  modelValue: {
    type: Array as () => FileItem[],
    default: () => []
  },
  fileType: {
    type: String,
    default: 'pictures'
  },
  limit: {
    type: Number,
    default: 0 // 0 表示无限制
  },
  accept: {
    type: String,
    default: 'image/*' // 默认只接受图片
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'change']);

// 状态变量
const selectorVisible = ref(false);
const previewVisible = ref(false);
const previewFile = ref<FileItem | null>(null);
const sortableContainer = ref<HTMLElement | null>(null);
let sortableInstance: Sortable | null = null;

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
};

// 打开文件管理器
const openFileManager = () => {
  selectorVisible.value = true;
};

// 处理文件选择
const handleFilesSelected = (files: FileItem[]) => {
  // 检查是否超过限制
  if (props.limit > 0 && files.length > props.limit) {
    ElMessage.warning(`最多只能选择 ${props.limit} 个文件`);
    // 只取限制数量的文件
    files = files.slice(0, props.limit);
  }

  // 为每个文件添加尺寸信息（如果是图片）
  const updatedFiles = files.map(file => {
    // 对于图片文件，获取尺寸信息
    if (file.extension && ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(file.extension.toLowerCase())) {
      // 创建一个返回图片尺寸的 Promise
      const promise = new Promise(resolve => {
        const img = new Image();
        img.onload = () => {
          file.width = img.width;
          file.height = img.height;
          resolve(file);
        };
        img.onerror = () => resolve(file);
        img.src = file.url;
      });
      
      // 开始加载图片以获取尺寸
      promise.catch(() => {
        // 忽略加载错误
      });
    }
    return file;
  });

  // 更新选中的文件列表
  emit('update:modelValue', updatedFiles);
  emit('change', updatedFiles);
  
  // 关闭选择器
  selectorVisible.value = false;
};

// 移除文件
const removeFile = (index: number) => {
  const newFiles = [...props.modelValue];
  newFiles.splice(index, 1);
  emit('update:modelValue', newFiles);
  emit('change', newFiles);
};

// 清空选择
const clearSelection = () => {
  emit('update:modelValue', []);
  emit('change', []);
};

// 预览图片
const previewImage = (file: FileItem) => {
  previewFile.value = file;
  previewVisible.value = true;
};

// 初始化拖拽排序
const initSortable = () => {
  if (!sortableContainer.value || !props.modelValue || props.modelValue.length <= 1) return;
  
  // 销毁之前的实例（如果存在）
  if (sortableInstance) {
    sortableInstance.destroy();
  }
  
  // 创建新的Sortable实例
  sortableInstance = Sortable.create(sortableContainer.value, {
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    dragClass: 'sortable-drag',
    handle: '.file-thumbnail', // 使用缩略图作为拖拽手柄
    // 关闭自动排序，完全依赖我们的手动排序逻辑
    sort: true,
    // 使用原生HTML5拖拽，更稳定
    forceFallback: false,
    // 在拖拽开始时记录原始索引
    onStart: function(evt) {
      const itemElem = evt.item;
      const index = Array.from(itemElem.parentNode.children).indexOf(itemElem);
      // 将索引存储在元素的dataset中
      itemElem.dataset.originalIndex = index.toString();
    },
    onEnd: function(evt) {
      // 获取拖拽前后的索引
      const itemElem = evt.item;
      const oldIndex = parseInt(itemElem.dataset.originalIndex);
      const newIndex = Array.from(itemElem.parentNode.children).indexOf(itemElem);
      
      // 验证索引有效性
      if (isNaN(oldIndex) || oldIndex < 0 || oldIndex >= props.modelValue.length || 
          newIndex < 0 || newIndex >= props.modelValue.length || oldIndex === newIndex) {
        return;
      }
      
      // 创建新的文件数组并更新顺序
      const newFiles = [...props.modelValue];
      const movedItem = newFiles.splice(oldIndex, 1)[0];
      newFiles.splice(newIndex, 0, movedItem);
      
      // 更新文件列表
      emit('update:modelValue', newFiles);
      emit('change', newFiles);
      
      // 重新初始化排序，确保下一次拖拽正常
      nextTick(() => {
        initSortable();
      });
    }
  });
};

// 监听文件列表变化，重新初始化拖拽
watch(() => props.modelValue, () => {
  nextTick(() => {
    initSortable();
  });
}, { deep: true });

// 组件挂载后初始化拖拽
onMounted(() => {
  nextTick(() => {
    initSortable();
  });
});
</script>

<style lang="scss" scoped>
.multi-file-selector {
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
  transition: border-color 0.3s;

  &:hover {
    border-color: var(--el-color-primary);
  }

  .selected-files {
    margin-bottom: 16px;

    .selected-files-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 12px;
      
      /* 拖拽相关样式 */
      .sortable-ghost {
        opacity: 0.5;
        background: var(--el-color-primary-light-9);
        border: 1px dashed var(--el-color-primary);
      }
      
      .sortable-chosen {
        cursor: move;
      }
      
      .sortable-drag {
        opacity: 0.8;
        z-index: 10;
      }
    }

    .file-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;

      .file-thumbnail {
        position: relative;
        width: 100%;
        height: 120px;
        border-radius: 4px;
        overflow: hidden;
        cursor: move; /* 指示可拖动 */

        .el-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .file-actions {
          position: absolute;
          top: 4px;
          right: 4px;
          opacity: 0;
          transition: opacity 0.3s;
        }

        &:hover .file-actions {
          opacity: 1;
        }
      }

      .file-name {
        margin-top: 4px;
        font-size: 12px;
        width: 100%;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .file-selector-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;

    .empty-tip {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #909399;
      
      .el-icon {
        font-size: 36px;
        margin-bottom: 8px;
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
    }

    .help-text {
      font-size: 12px;
      color: #909399;
    }
  }
}
:deep(.file-selector-dialog) {
  height: 70vh;
  .el-dialog__header{
    padding-top: 6px;
    padding-bottom: 6px;
  }
  padding:0;
  .el-dialog__body{
    margin:0;
    padding:0;
    height: 100%;
  }
  .el-dialog__footer{
    padding: 6px 0;
  }
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .preview-info {
    margin-top: 16px;
    width: 100%;
    
    .info-item {
      display: flex;
      margin-bottom: 8px;
      
      .label {
        font-weight: bold;
        width: 80px;
      }
      
      .value {
        flex: 1;
      }
    }
  }
}
</style>