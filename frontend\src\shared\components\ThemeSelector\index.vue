<template>
    <div class="theme-selector">
        <!-- 主题切换按钮 -->
        <div @click="toggleThemePanel" type="button"
            class="p-button-rounded p-button-text p-button-sm theme-toggle-btn" :class="{ 'active': showThemePanel }"
            aria-haspopup="true" :aria-expanded="showThemePanel" :title="t(`theme.${currentTheme}`)">
            <template #icon>
                <div class="current-theme-preview" :style="{ backgroundColor: currentThemeColor }"></div>
            </template>
        </div>

        <!-- 主题选择面板 -->
        <div v-show="showThemePanel" class="theme-panel" @click.stop>
            <div class="theme-panel-header">
                <h3 class="theme-panel-title">
                    <i class="pi pi-palette mr-2"></i>
                    {{ t('theme.themeSettings') }}
                </h3>
                <Button @click="showThemePanel = false" icon="pi pi-times"
                    class="p-button-rounded p-button-text p-button-sm" />
            </div>

            <div class="theme-panel-content">
                <!-- 主题颜色圆形按钮列表 -->
                <div class="theme-colors">
                    <div v-for="theme in availableThemes" :key="theme.name" class="theme-color-item"
                        :class="{ 'active': currentTheme === theme.name }" @click="selectTheme(theme.name)"
                        :title="t(`theme.${theme.code}`)">
                        <div class="theme-color-circle" :style="{ backgroundColor: theme.primary || '#4361ee' }">
                            <i v-if="currentTheme === theme.name" class="pi pi-check"></i>
                        </div>
                        <span class="theme-color-name">{{ t(`theme.${theme.code}`) }}</span>
                    </div>
                </div>

                <!-- 主题设置选项 -->
                <div class="theme-options">
                    <div class="option-item">
                        <Checkbox v-model="darkMode" binary inputId="dark-mode" />
                        <label for="dark-mode" class="option-label ml-2">
                            {{ t('theme.darkMode') }}
                        </label>
                    </div>

                    <div class="option-item">
                        <Checkbox v-model="followSystem" binary inputId="follow-system" />
                        <label for="follow-system" class="option-label ml-2">
                            <i class="pi pi-desktop mr-2"></i>
                            {{ t('theme.followSystem') }}
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 遮罩层 -->
        <div v-show="showThemePanel" class="theme-overlay" @click="showThemePanel = false"></div>
    </div>
</template>

<script setup lang="ts">
import type { ThemeConfig } from '@/shared/themes/theme-manager';
import { themeManager } from '@/shared/themes/theme-manager';
import { useThemeStore } from '@/store/modules/theme';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

// i18n
const { t } = useI18n();



// 使用Pinia主题存储
const themeStore = useThemeStore();

// 面板显示状态
const showThemePanel = ref(false);

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme);

// 可用主题列表

const availableThemes = computed(() => {
    return themeManager.availableThemes.value as ThemeConfig[];
});
// 是否为深色模式
const darkMode = computed({
    get: () => themeStore.isDark,
    set: () => themeStore.toggleDarkMode()
});

// 是否跟随系统主题
const followSystem = computed({
    get: () => themeStore.isFollowingSystem,
    set: (value) => themeStore.toggleFollowSystem(value)
});

// 获取当前主题颜色
const currentThemeColor = computed(() => {
    const theme = availableThemes.value.find((t: any) => t.name === currentTheme.value);
    return theme?.primary || '#4361ee';
});

// 切换主题面板
const toggleThemePanel = () => {
    showThemePanel.value = !showThemePanel.value;
};

// 选择主题
const selectTheme = (themeName: string) => {
    themeStore.setTheme(themeName as any); // 类型转换解决TypeScript问题
    showThemePanel.value = false;
};

// 点击外部关闭面板
const handleClickOutside = (event: MouseEvent) => {
    const themeSelector = document.querySelector('.theme-selector');
    if (themeSelector && !themeSelector.contains(event.target as Node)) {
        showThemePanel.value = false;
    }
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        showThemePanel.value = false;
    }
};

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeydown);
});

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside);
    document.removeEventListener('keydown', handleKeydown);
});
</script>

<style scoped lang="scss">
.theme-selector {
    position: relative;
    display: inline-block;
}

.theme-toggle-btn {
    transition: all 0.3s ease;

    &:hover {
        background-color: var(--surface-hover) !important;
        transform: scale(1.05);
    }

    &.active {
        background-color: var(--color-primary) !important;
        color: white !important;
    }
}

.theme-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.theme-panel {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 280px;
    background: var(--surface-card, var(--color-background, #ffffff));
    border: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    border-radius: 0.25rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    overflow: hidden;
    animation: slideDown 0.3s ease;
}

.theme-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    background: var(--surface-section, var(--surface-ground, var(--surface-100)));
}

.theme-panel-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color, var(--color-text, #111827));
    display: flex;
    align-items: center;
}

.theme-panel-content {
    padding: 1rem;
}

.current-theme-preview {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid var(--surface-border, var(--color-border, #e2e8f0));
}

.theme-colors {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.theme-color-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: translateY(-2px);
    }

    &.active .theme-color-circle {
        box-shadow: 0 0 0 2px var(--color-primary, #3b82f6);
    }
}

.theme-color-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.theme-color-name {
    font-size: 0.75rem;
    text-align: center;
}

.theme-options {
    margin-top: 1.5rem;
    border-top: 1px solid var(--surface-border, var(--color-border, #e2e8f0));
    padding-top: 1rem;
}

.option-item {
    padding: 0.5rem 0;

    .option-label {
        cursor: pointer;
        display: flex;
        align-items: center;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>