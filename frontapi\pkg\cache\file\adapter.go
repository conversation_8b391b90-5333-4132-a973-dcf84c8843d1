// Package file 提供基于文件的缓存适配器
package file

import (
	"context"
	"encoding/json"
	"fmt"
	"io/fs"
	"io/ioutil"
	"os"
	"path/filepath"
	"sync"
	"sync/atomic"
	"time"

	"frontapi/pkg/cache/types"
)

// Config 文件缓存配置
type Config struct {
	BasePath          string        // 缓存文件基础路径
	Prefix            string        // 键前缀
	DefaultTTL        time.Duration // 默认过期时间
	CleanupInterval   time.Duration // 清理过期文件的间隔
	FileMode          fs.FileMode   // 文件权限
	DirMode           fs.FileMode   // 目录权限
	EnableCompression bool          // 是否启用压缩
}

// Adapter 文件缓存适配器
type Adapter struct {
	basePath          string
	prefix            string
	defaultTTL        time.Duration
	cleanupInterval   time.Duration
	cleanupTicker     *time.Ticker
	stopCleanup       chan bool
	fileMode          fs.FileMode
	dirMode           fs.FileMode
	enableCompression bool
	stats             *types.CacheStats
	mu                sync.RWMutex
}

// 缓存项元数据
type cacheItem struct {
	Value      []byte    `json:"value"`
	Expiration time.Time `json:"expiration"`
	Compressed bool      `json:"compressed,omitempty"`
}

// NewAdapter 创建文件缓存适配器
func NewAdapter(config *Config) (*Adapter, error) {
	if config == nil {
		return nil, types.ErrInvalidConfig
	}

	if config.BasePath == "" {
		return nil, fmt.Errorf("缓存路径不能为空")
	}

	// 确保目录存在
	if err := os.MkdirAll(config.BasePath, 0755); err != nil {
		return nil, fmt.Errorf("创建缓存目录失败: %w", err)
	}

	// 设置默认值
	fileMode := config.FileMode
	if fileMode == 0 {
		fileMode = 0644
	}

	dirMode := config.DirMode
	if dirMode == 0 {
		dirMode = 0755
	}

	cleanupInterval := config.CleanupInterval
	if cleanupInterval <= 0 {
		cleanupInterval = 10 * time.Minute
	}

	defaultTTL := config.DefaultTTL
	if defaultTTL <= 0 {
		defaultTTL = time.Hour
	}

	adapter := &Adapter{
		basePath:          config.BasePath,
		prefix:            config.Prefix,
		defaultTTL:        defaultTTL,
		cleanupInterval:   cleanupInterval,
		fileMode:          fileMode,
		dirMode:           dirMode,
		enableCompression: config.EnableCompression,
		stats:             &types.CacheStats{StartTime: time.Now()},
		stopCleanup:       make(chan bool),
	}

	// 启动后台清理任务
	adapter.startCleanup()

	return adapter, nil
}

// Get 从缓存获取值
func (a *Adapter) Get(ctx context.Context, key string) ([]byte, error) {
	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	a.mu.RLock()
	defer a.mu.RUnlock()

	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			atomic.AddInt64(&a.stats.Misses, 1)
			return nil, types.ErrNotFound
		}
		return nil, fmt.Errorf("读取缓存文件失败: %w", err)
	}

	// 解析缓存项
	var item cacheItem
	if err := json.Unmarshal(data, &item); err != nil {
		atomic.AddInt64(&a.stats.Misses, 1)
		return nil, fmt.Errorf("解析缓存项失败: %w", err)
	}

	// 检查是否过期
	if !item.Expiration.IsZero() && item.Expiration.Before(time.Now()) {
		// 异步删除过期文件
		go func() {
			_ = os.Remove(filePath)
		}()
		atomic.AddInt64(&a.stats.Misses, 1)
		return nil, types.ErrNotFound
	}

	// 解压缓存值（如果启用了压缩）
	value := item.Value
	if item.Compressed {
		// 这里应该实现解压逻辑，现在先简单返回
		// TODO: 实现解压缩
	}

	atomic.AddInt64(&a.stats.Hits, 1)
	atomic.AddInt64(&a.stats.BytesRead, int64(len(value)))

	return value, nil
}

// Set 设置缓存值
func (a *Adapter) Set(ctx context.Context, key string, value []byte, expiration time.Duration) error {
	if value == nil {
		return types.ErrInvalidValue
	}

	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, a.dirMode); err != nil {
		return fmt.Errorf("创建缓存目录失败: %w", err)
	}

	// 设置过期时间
	var expireTime time.Time
	if expiration > 0 {
		expireTime = time.Now().Add(expiration)
	} else if a.defaultTTL > 0 {
		expireTime = time.Now().Add(a.defaultTTL)
	}

	// 压缩值（如果启用）
	compressedValue := value
	isCompressed := false
	if a.enableCompression && len(value) > 1024 { // 大于1KB才压缩
		// 这里应该实现压缩逻辑，现在先简单返回
		// TODO: 实现压缩
		isCompressed = a.enableCompression
	}

	// 创建缓存项
	item := cacheItem{
		Value:      compressedValue,
		Expiration: expireTime,
		Compressed: isCompressed,
	}

	// 序列化缓存项
	data, err := json.Marshal(item)
	if err != nil {
		return fmt.Errorf("序列化缓存项失败: %w", err)
	}

	// 写入文件
	a.mu.Lock()
	defer a.mu.Unlock()

	if err := ioutil.WriteFile(filePath, data, a.fileMode); err != nil {
		return fmt.Errorf("写入缓存文件失败: %w", err)
	}

	atomic.AddInt64(&a.stats.Sets, 1)
	atomic.AddInt64(&a.stats.BytesWritten, int64(len(data)))

	return nil
}

// Delete 删除缓存值
func (a *Adapter) Delete(ctx context.Context, key string) error {
	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	a.mu.Lock()
	defer a.mu.Unlock()

	// 删除文件
	err := os.Remove(filePath)
	if err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除缓存文件失败: %w", err)
	}

	atomic.AddInt64(&a.stats.Deletes, 1)
	return nil
}

// Clear 清空缓存
func (a *Adapter) Clear(ctx context.Context) error {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 遍历删除所有文件
	err := filepath.Walk(a.basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 删除文件
		return os.Remove(path)
	})

	if err != nil {
		return fmt.Errorf("清空缓存失败: %w", err)
	}

	atomic.AddInt64(&a.stats.Clears, 1)
	return nil
}

// Close 关闭缓存
func (a *Adapter) Close() error {
	// 停止清理任务
	if a.cleanupTicker != nil {
		a.cleanupTicker.Stop()
		a.stopCleanup <- true
		close(a.stopCleanup)
	}
	return nil
}

// Stats 获取缓存统计信息
func (a *Adapter) Stats() *types.CacheStats {
	stats := &types.CacheStats{
		Hits:         atomic.LoadInt64(&a.stats.Hits),
		Misses:       atomic.LoadInt64(&a.stats.Misses),
		Sets:         atomic.LoadInt64(&a.stats.Sets),
		Deletes:      atomic.LoadInt64(&a.stats.Deletes),
		Clears:       atomic.LoadInt64(&a.stats.Clears),
		StartTime:    a.stats.StartTime,
		Uptime:       time.Since(a.stats.StartTime),
		BytesRead:    atomic.LoadInt64(&a.stats.BytesRead),
		BytesWritten: atomic.LoadInt64(&a.stats.BytesWritten),
	}

	// 计算命中率
	totalOps := stats.Hits + stats.Misses
	if totalOps > 0 {
		stats.HitRate = float64(stats.Hits) / float64(totalOps)
	}

	return stats
}

// Name 获取适配器名称
func (a *Adapter) Name() string {
	return "file"
}

// Type 获取适配器类型
func (a *Adapter) Type() string {
	return "file"
}

// KeyWithPrefix 为缓存键添加前缀
func (a *Adapter) KeyWithPrefix(key string) string {
	if a.prefix == "" {
		return key
	}
	return a.prefix + ":" + key
}

// 获取缓存文件路径
func (a *Adapter) getFilePath(key string) string {
	// 将键转换为安全的文件名
	fileName := safeFileName(key)

	// 使用子目录来减少单个目录中的文件数
	// 使用键的前2个字符作为子目录，减少目录深度
	if len(fileName) > 2 {
		subDir := fileName[:2]
		return filepath.Join(a.basePath, subDir, fileName)
	}

	return filepath.Join(a.basePath, fileName)
}

// 将键转换为安全的文件名
func safeFileName(key string) string {
	// 替换不安全的字符
	replacer := map[rune]rune{
		'\\': '_',
		'/':  '_',
		':':  '_',
		'*':  '_',
		'?':  '_',
		'"':  '_',
		'<':  '_',
		'>':  '_',
		'|':  '_',
	}

	result := []rune{}
	for _, r := range key {
		if replacement, ok := replacer[r]; ok {
			result = append(result, replacement)
		} else {
			result = append(result, r)
		}
	}

	return string(result)
}

// 启动后台清理任务
func (a *Adapter) startCleanup() {
	a.cleanupTicker = time.NewTicker(a.cleanupInterval)
	go func() {
		for {
			select {
			case <-a.cleanupTicker.C:
				a.cleanupExpired()
			case <-a.stopCleanup:
				return
			}
		}
	}()
}

// 清理过期缓存
func (a *Adapter) cleanupExpired() {
	// 遍历所有缓存文件
	err := filepath.Walk(a.basePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil // 忽略错误，继续遍历
		}

		// 跳过目录
		if info.IsDir() {
			return nil
		}

		// 读取文件
		data, err := ioutil.ReadFile(path)
		if err != nil {
			// 忽略错误，继续遍历
			return nil
		}

		// 解析缓存项
		var item cacheItem
		if err := json.Unmarshal(data, &item); err != nil {
			// 无法解析，可能是损坏的缓存文件，删除它
			_ = os.Remove(path)
			return nil
		}

		// 检查是否过期
		if !item.Expiration.IsZero() && item.Expiration.Before(time.Now()) {
			// 删除过期文件
			_ = os.Remove(path)
		}

		return nil
	})

	if err != nil {
		// 记录错误但不处理
		// 在生产环境中应该使用适当的日志记录
	}
}

// Exists 检查键是否存在
func (a *Adapter) Exists(ctx context.Context, key string) (bool, error) {
	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	// 检查文件是否存在
	_, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}

	// 文件存在，但还需要检查是否过期
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return false, err
	}

	var item cacheItem
	if err := json.Unmarshal(data, &item); err != nil {
		return false, err
	}

	// 检查是否过期
	if !item.Expiration.IsZero() && item.Expiration.Before(time.Now()) {
		// 异步删除过期文件
		go func() {
			_ = os.Remove(filePath)
		}()
		return false, nil
	}

	return true, nil
}

// MGet 批量获取多个键的值
func (a *Adapter) MGet(ctx context.Context, keys []string) (map[string][]byte, error) {
	result := make(map[string][]byte, len(keys))

	for _, key := range keys {
		value, err := a.Get(ctx, key)
		if err == nil {
			result[key] = value
		}
	}

	return result, nil
}

// MSet 批量设置多个键值对
func (a *Adapter) MSet(ctx context.Context, items map[string][]byte, expiration time.Duration) error {
	for key, value := range items {
		if err := a.Set(ctx, key, value, expiration); err != nil {
			return err
		}
	}
	return nil
}

// Increment 递增计数器
func (a *Adapter) Increment(ctx context.Context, key string, delta int64) (int64, error) {
	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	a.mu.Lock()
	defer a.mu.Unlock()

	// 尝试读取现有值
	var currentValue int64 = 0
	data, err := ioutil.ReadFile(filePath)
	if err == nil {
		// 文件存在，解析值
		var item cacheItem
		if err := json.Unmarshal(data, &item); err == nil {
			// 尝试将字节数组转换为整数
			if len(item.Value) == 8 {
				currentValue = int64(item.Value[0]) | int64(item.Value[1])<<8 | int64(item.Value[2])<<16 | int64(item.Value[3])<<24 |
					int64(item.Value[4])<<32 | int64(item.Value[5])<<40 | int64(item.Value[6])<<48 | int64(item.Value[7])<<56
			}
		}
	}

	// 递增值
	newValue := currentValue + delta

	// 将int64转换为字节数组
	byteValue := make([]byte, 8)
	byteValue[0] = byte(newValue)
	byteValue[1] = byte(newValue >> 8)
	byteValue[2] = byte(newValue >> 16)
	byteValue[3] = byte(newValue >> 24)
	byteValue[4] = byte(newValue >> 32)
	byteValue[5] = byte(newValue >> 40)
	byteValue[6] = byte(newValue >> 48)
	byteValue[7] = byte(newValue >> 56)

	// 创建缓存项
	item := cacheItem{
		Value:      byteValue,
		Expiration: time.Now().Add(a.defaultTTL),
	}

	// 序列化并保存
	itemData, err := json.Marshal(item)
	if err != nil {
		return 0, err
	}

	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, a.dirMode); err != nil {
		return 0, err
	}

	// 写入文件
	if err := ioutil.WriteFile(filePath, itemData, a.fileMode); err != nil {
		return 0, err
	}

	atomic.AddInt64(&a.stats.Sets, 1)
	return newValue, nil
}

// Decrement 递减计数器
func (a *Adapter) Decrement(ctx context.Context, key string, delta int64) (int64, error) {
	return a.Increment(ctx, key, -delta)
}

// Expire 设置过期时间
func (a *Adapter) Expire(ctx context.Context, key string, expiration time.Duration) error {
	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	a.mu.Lock()
	defer a.mu.Unlock()

	// 读取现有缓存项
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return err
	}

	var item cacheItem
	if err := json.Unmarshal(data, &item); err != nil {
		return err
	}

	// 更新过期时间
	item.Expiration = time.Now().Add(expiration)

	// 重新序列化
	updatedData, err := json.Marshal(item)
	if err != nil {
		return err
	}

	// 写回文件
	return ioutil.WriteFile(filePath, updatedData, a.fileMode)
}

// TTL 获取键的剩余生存时间
func (a *Adapter) TTL(ctx context.Context, key string) (time.Duration, error) {
	prefixedKey := a.KeyWithPrefix(key)
	filePath := a.getFilePath(prefixedKey)

	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return 0, types.ErrNotFound
		}
		return 0, err
	}

	var item cacheItem
	if err := json.Unmarshal(data, &item); err != nil {
		return 0, err
	}

	// 计算剩余时间
	if item.Expiration.IsZero() {
		return -1, nil // 表示永不过期
	}

	ttl := time.Until(item.Expiration)
	if ttl <= 0 {
		// 已过期，异步删除
		go func() {
			_ = os.Remove(filePath)
		}()
		return 0, types.ErrNotFound
	}

	return ttl, nil
}

// Ping 检查缓存是否可用
func (a *Adapter) Ping(ctx context.Context) error {
	// 检查缓存目录是否存在且可写
	testFile := filepath.Join(a.basePath, ".ping")
	if err := ioutil.WriteFile(testFile, []byte("ping"), a.fileMode); err != nil {
		return err
	}
	defer os.Remove(testFile)
	return nil
}
