package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"frontapi/internal/service/base/extlike/types"
)

// LikeOperations MongoDB点赞操作处理器
type LikeOperations struct {
	client *MongoClient
}

// NewLikeOperations 创建点赞操作处理器
func NewLikeOperations(client *MongoClient) *LikeOperations {
	return &LikeOperations{
		client: client,
	}
}

// Like 执行点赞操作
func (ops *LikeOperations) Like(ctx context.Context, userID, itemID, itemType string) error {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	// 检查是否已经点赞
	exists, err := ops.IsLiked(ctx, userID, itemID, itemType)
	if err != nil {
		return fmt.Errorf("检查点赞状态失败: %w", err)
	}
	if exists {
		return nil // 已经点赞，直接返回
	}

	// 开始事务
	session, err := ops.client.GetClient().StartSession()
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 插入点赞记录
		likeRecord := &types.LikeRecord{
			UserID:    userID,
			ItemID:    itemID,
			ItemType:  itemType,
			Timestamp: time.Now(),
			Status:    "liked",
			Source:    "mongodb",
			Version:   1,
		}

		likeCollection := ops.client.getLikeCollection()
		_, err := likeCollection.InsertOne(sessCtx, likeRecord)
		if err != nil {
			return nil, fmt.Errorf("插入点赞记录失败: %w", err)
		}

		// 更新统计数据
		err = ops.updateLikeCount(sessCtx, itemID, itemType, 1)
		if err != nil {
			return nil, fmt.Errorf("更新点赞统计失败: %w", err)
		}

		// 记录操作日志
		err = ops.recordOperation(sessCtx, userID, itemID, itemType, "like")
		if err != nil {
			return nil, fmt.Errorf("记录操作日志失败: %w", err)
		}

		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		return fmt.Errorf("点赞事务执行失败: %w", err)
	}

	return nil
}

// Unlike 执行取消点赞操作
func (ops *LikeOperations) Unlike(ctx context.Context, userID, itemID, itemType string) error {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	// 检查是否已经点赞
	exists, err := ops.IsLiked(ctx, userID, itemID, itemType)
	if err != nil {
		return fmt.Errorf("检查点赞状态失败: %w", err)
	}
	if !exists {
		return nil // 没有点赞，直接返回
	}

	// 开始事务
	session, err := ops.client.GetClient().StartSession()
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 删除点赞记录（或更新状态为unliked）
		likeCollection := ops.client.getLikeCollection()
		filter := bson.M{
			"user_id":   userID,
			"item_id":   itemID,
			"item_type": itemType,
		}

		if ops.client.config.ConsistencyLevel == "soft" {
			// 软删除：更新状态
			update := bson.M{
				"$set": bson.M{
					"status":     "unliked",
					"updated_at": time.Now(),
					"version":    bson.M{"$inc": 1},
				},
			}
			_, err := likeCollection.UpdateOne(sessCtx, filter, update)
			if err != nil {
				return nil, fmt.Errorf("更新点赞记录失败: %w", err)
			}
		} else {
			// 硬删除：直接删除记录
			_, err := likeCollection.DeleteOne(sessCtx, filter)
			if err != nil {
				return nil, fmt.Errorf("删除点赞记录失败: %w", err)
			}
		}

		// 更新统计数据
		err = ops.updateLikeCount(sessCtx, itemID, itemType, -1)
		if err != nil {
			return nil, fmt.Errorf("更新点赞统计失败: %w", err)
		}

		// 记录操作日志
		err = ops.recordOperation(sessCtx, userID, itemID, itemType, "unlike")
		if err != nil {
			return nil, fmt.Errorf("记录操作日志失败: %w", err)
		}

		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		return fmt.Errorf("取消点赞事务执行失败: %w", err)
	}

	return nil
}

// IsLiked 检查是否已点赞
func (ops *LikeOperations) IsLiked(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	likeCollection := ops.client.getLikeCollection()
	filter := bson.M{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		// 软删除模式下，检查状态
		filter["status"] = "liked"
	}

	count, err := likeCollection.CountDocuments(ctx, filter)
	if err != nil {
		return false, fmt.Errorf("检查点赞状态失败: %w", err)
	}

	return count > 0, nil
}

// GetLikeCount 获取点赞数量
func (ops *LikeOperations) GetLikeCount(ctx context.Context, itemID, itemType string) (int64, error) {
	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	// 优先从统计表获取
	statsCollection := ops.client.getStatsCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	var stats struct {
		LikeCount int64 `bson:"like_count"`
	}

	err := statsCollection.FindOne(ctx, filter).Decode(&stats)
	if err == nil {
		return stats.LikeCount, nil
	}

	if err != mongo.ErrNoDocuments {
		return 0, fmt.Errorf("查询统计数据失败: %w", err)
	}

	// 统计表没有数据，实时计算
	likeCollection := ops.client.getLikeCollection()
	likeFilter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	if ops.client.config.ConsistencyLevel == "soft" {
		likeFilter["status"] = "liked"
	}

	count, err := likeCollection.CountDocuments(ctx, likeFilter)
	if err != nil {
		return 0, fmt.Errorf("实时计算点赞数量失败: %w", err)
	}

	// 异步更新统计表
	go ops.updateStatsAsync(itemID, itemType, count)

	return count, nil
}

// BatchLike 批量点赞
func (ops *LikeOperations) BatchLike(ctx context.Context, operations []*types.LikeOperation) error {
	if len(operations) == 0 {
		return nil
	}

	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	// 开始事务
	session, err := ops.client.GetClient().StartSession()
	if err != nil {
		return fmt.Errorf("开始批量点赞事务失败: %w", err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		likeCollection := ops.client.getLikeCollection()

		// 准备批量插入的文档
		var documents []interface{}
		statsUpdates := make(map[string]int64) // itemID_itemType -> count

		for _, op := range operations {
			// 检查是否已经点赞
			exists, err := ops.IsLiked(sessCtx, op.UserID, op.ItemID, op.ItemType)
			if err != nil {
				return nil, fmt.Errorf("检查点赞状态失败: %w", err)
			}
			if exists {
				continue // 已经点赞，跳过
			}

			likeRecord := &types.LikeRecord{
				UserID:    op.UserID,
				ItemID:    op.ItemID,
				ItemType:  op.ItemType,
				Timestamp: time.Now(),
				Status:    "liked",
				Source:    "mongodb",
				Metadata:  op.Metadata,
				Version:   1,
			}

			documents = append(documents, likeRecord)

			// 统计每个项目的点赞增量
			key := fmt.Sprintf("%s_%s", op.ItemID, op.ItemType)
			statsUpdates[key]++
		}

		// 批量插入点赞记录
		if len(documents) > 0 {
			_, err := likeCollection.InsertMany(sessCtx, documents)
			if err != nil {
				return nil, fmt.Errorf("批量插入点赞记录失败: %w", err)
			}
		}

		// 批量更新统计数据
		for key, increment := range statsUpdates {
			parts := splitKey(key)
			if len(parts) == 2 {
				err = ops.updateLikeCount(sessCtx, parts[0], parts[1], increment)
				if err != nil {
					return nil, fmt.Errorf("更新统计数据失败: %w", err)
				}
			}
		}

		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		return fmt.Errorf("批量点赞事务执行失败: %w", err)
	}

	return nil
}

// BatchUnlike 批量取消点赞
func (ops *LikeOperations) BatchUnlike(ctx context.Context, operations []*types.LikeOperation) error {
	if len(operations) == 0 {
		return nil
	}

	ctx, cancel := ops.client.CreateContext(ctx)
	defer cancel()

	// 开始事务
	session, err := ops.client.GetClient().StartSession()
	if err != nil {
		return fmt.Errorf("开始批量取消点赞事务失败: %w", err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (interface{}, error) {
		likeCollection := ops.client.getLikeCollection()
		statsUpdates := make(map[string]int64) // itemID_itemType -> count

		for _, op := range operations {
			// 检查是否已经点赞
			exists, err := ops.IsLiked(sessCtx, op.UserID, op.ItemID, op.ItemType)
			if err != nil {
				return nil, fmt.Errorf("检查点赞状态失败: %w", err)
			}
			if !exists {
				continue // 没有点赞，跳过
			}

			filter := bson.M{
				"user_id":   op.UserID,
				"item_id":   op.ItemID,
				"item_type": op.ItemType,
			}

			if ops.client.config.ConsistencyLevel == "soft" {
				// 软删除：更新状态
				update := bson.M{
					"$set": bson.M{
						"status":     "unliked",
						"updated_at": time.Now(),
					},
					"$inc": bson.M{"version": 1},
				}
				_, err := likeCollection.UpdateOne(sessCtx, filter, update)
				if err != nil {
					return nil, fmt.Errorf("更新点赞记录失败: %w", err)
				}
			} else {
				// 硬删除：直接删除记录
				_, err := likeCollection.DeleteOne(sessCtx, filter)
				if err != nil {
					return nil, fmt.Errorf("删除点赞记录失败: %w", err)
				}
			}

			// 统计每个项目的取消点赞增量
			key := fmt.Sprintf("%s_%s", op.ItemID, op.ItemType)
			statsUpdates[key]--
		}

		// 批量更新统计数据
		for key, decrement := range statsUpdates {
			parts := splitKey(key)
			if len(parts) == 2 {
				err = ops.updateLikeCount(sessCtx, parts[0], parts[1], decrement)
				if err != nil {
					return nil, fmt.Errorf("更新统计数据失败: %w", err)
				}
			}
		}

		return nil, nil
	}

	_, err = session.WithTransaction(ctx, callback)
	if err != nil {
		return fmt.Errorf("批量取消点赞事务执行失败: %w", err)
	}

	return nil
}

// updateLikeCount 更新点赞统计
func (ops *LikeOperations) updateLikeCount(ctx context.Context, itemID, itemType string, increment int64) error {
	statsCollection := ops.client.getStatsCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$inc": bson.M{
			"like_count": increment,
		},
		"$set": bson.M{
			"updated_at": time.Now(),
		},
		"$setOnInsert": bson.M{
			"item_id":    itemID,
			"item_type":  itemType,
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, err := statsCollection.UpdateOne(ctx, filter, update, opts)
	return err
}

// recordOperation 记录操作日志
func (ops *LikeOperations) recordOperation(ctx context.Context, userID, itemID, itemType, action string) error {
	if !ops.client.config.EnablePipeline {
		return nil // 未启用操作记录
	}

	operationCollection := ops.client.getOperationCollection()
	operation := &types.LikeOperation{
		UserID:    userID,
		ItemID:    itemID,
		ItemType:  itemType,
		Action:    action,
		Timestamp: time.Now(),
	}

	_, err := operationCollection.InsertOne(ctx, operation)
	return err
}

// updateStatsAsync 异步更新统计数据
func (ops *LikeOperations) updateStatsAsync(itemID, itemType string, count int64) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	statsCollection := ops.client.getStatsCollection()
	filter := bson.M{
		"item_id":   itemID,
		"item_type": itemType,
	}

	update := bson.M{
		"$set": bson.M{
			"like_count": count,
			"updated_at": time.Now(),
		},
		"$setOnInsert": bson.M{
			"item_id":    itemID,
			"item_type":  itemType,
			"created_at": time.Now(),
		},
	}

	opts := options.Update().SetUpsert(true)
	_, _ = statsCollection.UpdateOne(ctx, filter, update, opts)
}

// splitKey 分割键
func splitKey(key string) []string {
	// 简单的分割实现，实际可能需要更复杂的逻辑
	for i := len(key) - 1; i >= 0; i-- {
		if key[i] == '_' {
			return []string{key[:i], key[i+1:]}
		}
	}
	return []string{key}
}
