package mongodb

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Config MongoDB配置结构
type Config struct {
	URI            string        `json:"uri" yaml:"uri"`                         // MongoDB连接URI
	Database       string        `json:"database" yaml:"database"`               // 数据库名称
	ConnectTimeout time.Duration `json:"connect_timeout" yaml:"connect_timeout"` // 连接超时时间
	MaxPoolSize    uint64        `json:"max_pool_size" yaml:"max_pool_size"`     // 最大连接池大小
	MinPoolSize    uint64        `json:"min_pool_size" yaml:"min_pool_size"`     // 最小连接池大小
	MaxIdleTime    time.Duration `json:"max_idle_time" yaml:"max_idle_time"`     // 最大空闲时间
}

// DefaultConfig 返回默认MongoDB配置
func DefaultConfig() *Config {
	return &Config{
		URI:            "mongodb://localhost:27017",
		Database:       "frontapi",
		ConnectTimeout: 10 * time.Second,
		MaxPoolSize:    100,
		MinPoolSize:    5,
		MaxIdleTime:    5 * time.Minute,
	}
}

// NewClient 创建MongoDB客户端
func NewClient(config *Config) (*mongo.Client, error) {
	if config == nil {
		config = DefaultConfig()
	}

	clientOptions := options.Client().
		ApplyURI(config.URI).
		SetConnectTimeout(config.ConnectTimeout).
		SetMaxPoolSize(config.MaxPoolSize).
		SetMinPoolSize(config.MinPoolSize).
		SetMaxConnIdleTime(config.MaxIdleTime)

	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx, nil); err != nil {
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	return client, nil
}

// NewDatabase 创建数据库实例
func NewDatabase(client *mongo.Client, databaseName string) *mongo.Database {
	return client.Database(databaseName)
}

// Close 关闭MongoDB连接
func Close(client *mongo.Client) error {
	if client == nil {
		return nil
	}
	return client.Disconnect(context.Background())
}