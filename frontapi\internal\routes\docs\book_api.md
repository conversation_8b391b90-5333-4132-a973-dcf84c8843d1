# 书籍管理接口文档

## 电子书管理接口 (/books)

**注意**: 所有书籍管理接口都需要用户认证，请在请求头中携带认证信息。

### 1. 获取电子书列表

**接口地址**: `POST /api/books/list`

**接口描述**: 获取电子书列表，支持分页和筛选

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "author": "作者名称",
    "status": 1,
    "sort_by": "created_at",
    "sort_order": "desc",
    "publish_status": "published"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "book_id",
        "title": "书籍标题",
        "subtitle": "副标题",
        "author": "作者名称",
        "description": "书籍描述",
        "cover_url": "封面图片URL",
        "category_id": "分类ID",
        "category_name": "分类名称",
        "isbn": "978-7-111-12345-6",
        "publisher": "出版社",
        "publish_date": "2024-01-01",
        "page_count": 300,
        "word_count": 150000,
        "chapters_count": 20,
        "price": 29.99,
        "discount_price": 19.99,
        "views_count": 1000,
        "downloads_count": 100,
        "rating": 4.5,
        "reviews_count": 50,
        "status": 1,
        "publish_status": "published",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 200,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取电子书详情

**接口地址**: `POST /api/books/detail/:id`

**接口描述**: 获取指定电子书的详细信息

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "include_chapters": true,
    "include_reviews": true
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "book_id",
    "title": "书籍标题",
    "subtitle": "副标题",
    "author": "作者名称",
    "description": "书籍详细描述",
    "cover_url": "封面图片URL",
    "category_id": "分类ID",
    "category_name": "分类名称",
    "isbn": "978-7-111-12345-6",
    "publisher": "出版社",
    "publish_date": "2024-01-01",
    "page_count": 300,
    "word_count": 150000,
    "chapters_count": 20,
    "price": 29.99,
    "discount_price": 19.99,
    "views_count": 1000,
    "downloads_count": 100,
    "rating": 4.5,
    "reviews_count": 50,
    "tags": ["小说", "科幻"],
    "language": "zh-CN",
    "file_format": ["pdf", "epub", "mobi"],
    "file_size": 10485760,
    "status": 1,
    "publish_status": "published",
    "chapters": [
      {
        "id": "chapter_id",
        "title": "第一章 开始",
        "order": 1,
        "word_count": 5000,
        "is_free": true
      }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 添加电子书

**接口地址**: `POST /api/books/add`

**接口描述**: 创建新的电子书

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "title": "书籍标题",
    "subtitle": "副标题",
    "author": "作者名称",
    "description": "书籍描述",
    "cover_url": "封面图片URL",
    "category_id": "分类ID",
    "isbn": "978-7-111-12345-6",
    "publisher": "出版社",
    "publish_date": "2024-01-01",
    "page_count": 300,
    "word_count": 150000,
    "price": 29.99,
    "discount_price": 19.99,
    "tags": ["小说", "科幻"],
    "language": "zh-CN",
    "file_format": ["pdf", "epub"],
    "status": 1
  }
}
```

### 4. 更新电子书

**接口地址**: `POST /api/books/update`

**接口描述**: 更新电子书信息

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "id": "book_id",
    "title": "更新后的书籍标题",
    "description": "更新后的描述",
    "price": 39.99,
    "discount_price": 29.99
  }
}
```

### 5. 更新电子书状态

**接口地址**: `POST /api/books/update-status`

**接口描述**: 更新电子书的状态

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "id": "book_id",
    "status": 1,
    "publish_status": "published"
  }
}
```

### 6. 删除电子书

**接口地址**: `POST /api/books/delete/:id`

**接口描述**: 删除指定的电子书

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "confirm": true
  }
}
```

## 电子书分类管理接口 (/books/categories)

### 1. 获取分类列表

**接口地址**: `POST /api/books/categories/list`

**接口描述**: 获取电子书分类列表

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "parent_id": "父分类ID",
    "status": 1,
    "include_book_count": true
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "category_id",
        "name": "分类名称",
        "description": "分类描述",
        "icon_url": "图标URL",
        "parent_id": "父分类ID",
        "sort_order": 1,
        "book_count": 50,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 20,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 获取分类详情

**接口地址**: `POST /api/books/categories/detail/:id`

**接口描述**: 获取指定分类的详细信息

**认证要求**: 需要用户认证

### 3. 添加分类

**接口地址**: `POST /api/books/categories/add`

**接口描述**: 创建新的电子书分类

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "name": "分类名称",
    "description": "分类描述",
    "icon_url": "图标URL",
    "parent_id": "父分类ID",
    "sort_order": 1,
    "status": 1
  }
}
```

### 4. 更新分类

**接口地址**: `POST /api/books/categories/update`

**接口描述**: 更新电子书分类信息

**认证要求**: 需要用户认证

### 5. 更新分类状态

**接口地址**: `POST /api/books/categories/update-status`

**接口描述**: 更新分类状态

**认证要求**: 需要用户认证

### 6. 删除分类

**接口地址**: `POST /api/books/categories/delete/:id`

**接口描述**: 删除指定分类

**认证要求**: 需要用户认证

## 电子书章节管理接口 (/books/chapters)

### 1. 获取章节列表

**接口地址**: `POST /api/books/chapters/list`

**接口描述**: 获取电子书章节列表

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "book_id": "book_id",
    "keyword": "搜索关键词",
    "status": 1,
    "sort_by": "order",
    "sort_order": "asc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 20
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "chapter_id",
        "book_id": "book_id",
        "title": "第一章 开始",
        "content": "章节内容",
        "summary": "章节摘要",
        "order": 1,
        "word_count": 5000,
        "is_free": true,
        "price": 0,
        "views_count": 100,
        "status": 1,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 20,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 获取章节详情

**接口地址**: `POST /api/books/chapters/detail/:id`

**接口描述**: 获取指定章节的详细信息

**认证要求**: 需要用户认证

### 3. 添加章节

**接口地址**: `POST /api/books/chapters/add`

**接口描述**: 为电子书添加新章节

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "book_id": "book_id",
    "title": "章节标题",
    "content": "章节内容",
    "summary": "章节摘要",
    "order": 1,
    "is_free": true,
    "price": 0,
    "status": 1
  }
}
```

### 4. 更新章节

**接口地址**: `POST /api/books/chapters/update`

**接口描述**: 更新章节信息

**认证要求**: 需要用户认证

### 5. 删除章节

**接口地址**: `POST /api/books/chapters/delete/:id`

**接口描述**: 删除指定章节

**认证要求**: 需要用户认证

### 6. 批量上传章节

**接口地址**: `POST /api/books/chapters/batch-upload`

**接口描述**: 批量上传多个章节

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "book_id": "book_id",
    "chapters": [
      {
        "title": "第一章",
        "content": "章节内容1",
        "order": 1
      },
      {
        "title": "第二章",
        "content": "章节内容2",
        "order": 2
      }
    ]
  }
}
```

### 7. 重新排序章节

**接口地址**: `POST /api/books/chapters/reorder`

**接口描述**: 重新排序单个章节

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "chapter_id": "chapter_id",
    "new_order": 5
  }
}
```

### 8. 批量更新章节顺序

**接口地址**: `POST /api/books/chapters/batch-update-order`

**接口描述**: 批量更新多个章节的顺序

**认证要求**: 需要用户认证

**请求参数**:
```json
{
  "data": {
    "chapters": [
      {
        "id": "chapter_id_1",
        "order": 1
      },
      {
        "id": "chapter_id_2",
        "order": 2
      }
    ]
  }
}
```

## 电子书阅读接口

### 9. 开始阅读

**接口地址**: `POST /api/books/start-reading`

**接口描述**: 开始阅读电子书，记录阅读进度

**请求参数**:
```json
{
  "data": {
    "book_id": "book_id",
    "chapter_id": "chapter_id",
    "user_id": "用户ID"
  }
}
```

### 10. 更新阅读进度

**接口地址**: `POST /api/books/update-progress`

**接口描述**: 更新用户的阅读进度

**请求参数**:
```json
{
  "data": {
    "book_id": "book_id",
    "chapter_id": "chapter_id",
    "user_id": "用户ID",
    "progress_percentage": 75.5,
    "reading_time": 1800
  }
}
```

### 11. 获取阅读历史

**接口地址**: `POST /api/books/reading-history`

**接口描述**: 获取用户的阅读历史记录

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```