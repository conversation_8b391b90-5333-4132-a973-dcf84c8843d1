package integral

import (
	"frontapi/internal/models/integral"
	repo "frontapi/internal/repository/integral"
	"frontapi/internal/service/base"
)

// CreatePointExchangeRequest 创建兑换记录请求
type CreatePointExchangeRequest struct {
	UserID        string `json:"user_id"`
	Points        int    `json:"points"`
	ExchangeType  string `json:"exchange_type"`
	ExchangeValue int    `json:"exchange_value"`
	Status        string `json:"status"`
}

// UpdatePointExchangeRequest 更新兑换记录请求
type UpdatePointExchangeRequest struct {
	Points        int    `json:"points"`
	ExchangeType  string `json:"exchange_type"`
	ExchangeValue int    `json:"exchange_value"`
	Status        string `json:"status"`
}

// PointExchangeService 积分兑换服务接口
type PointExchangeService interface {
	base.IExtendedService[integral.PointExchange]
}

type pointExchangeService struct {
	*base.ExtendedService[integral.PointExchange]
	repo repo.PointExchangeRepository
}

func NewPointExchangeService(repo repo.PointExchangeRepository) PointExchangeService {
	return &pointExchangeService{
		ExtendedService: base.NewExtendedService[integral.PointExchange](repo, "point_exchange"),
		repo:            repo,
	}
}
