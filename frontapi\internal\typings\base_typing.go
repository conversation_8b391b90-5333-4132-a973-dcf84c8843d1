package typings

import "frontapi/internal/models/users"

type BaseAuthor struct {
	ID               string `json:"id"`               // 作者ID
	Username         string `json:"username"`         // 作者昵称
	Nickname         string `json:"nickname"`         // 作者昵称
	Avatar           string `json:"avatar"`           // 作者头像
	UserType         int8   `json:"userType"`         // 1.普通用户，2,Vip用户,3蓝标用户,11,明星
	IsFollowed       bool   `json:"isFollowed"`       // 是否关注
	IsLiked          bool   `json:"isLiked"`          // 是否点赞
	Level            int8   `json:"level"`            // 等级
	IsVerified       bool   `json:"isVerified"`       // 是否认证
	IsContentCreator bool   `json:"isContentCreator"` // 是否内容创作者
	CreatorLevel     int8   `json:"creatorLevel"`     // 创作者等级
	TotalPosts       int64  `json:"totalPosts"`       // 总帖子数
	TotalShorts      int64  `json:"totalShorts"`      // 总短视频数
	TotalVideos      int64  `json:"totalVideos"`      // 总视频数
	FollowCount      int64  `json:"followCount"`      // 关注数
	LikeCount        int64  `json:"likeCount"`        // 点赞数
	Bio              string `json:"bio"`              // 个人简介
	Online           int8   `json:"online"`           // 是否在线
}

func ConvertBaseAuthor(author *users.User) BaseAuthor {
	// 处理null类型字段
	avatarStr := ""
	if author.Avatar.Valid {
		avatarStr = author.Avatar.String
	}

	bioStr := ""
	if author.Bio.Valid {
		bioStr = author.Bio.String
	}

	nicknameStr := ""
	if author.Nickname.Valid {
		nicknameStr = author.Nickname.String
	}

	usernameStr := ""
	if author.Username.Valid {
		usernameStr = author.Username.String
	}

	return BaseAuthor{
		ID:               author.ID,
		Username:         usernameStr,
		Nickname:         nicknameStr,
		Avatar:           avatarStr,
		UserType:         author.UserType,
		IsFollowed:       author.IsFollowed,
		IsLiked:          author.IsLiked,
		Level:            int8(author.CreatorLevel),
		IsVerified:       author.IsVerified == 1,
		IsContentCreator: author.IsContentCreator == 1,
		CreatorLevel:     int8(author.CreatorLevel),
		TotalPosts:       0, // 这些字段在新模型中不存在，设为默认值
		TotalShorts:      0,
		TotalVideos:      0,
		FollowCount:      0,
		LikeCount:        0,
		Bio:              bioStr,
		Online:           0, // 新模型中不存在此字段，设为默认值
	}
}
