/.cursor
/.gradio
/.idea
/.vite
/.vscode
node_modules/
**/node_modules/
/frontend/node_modules
/frontend/dist
/frontend/build
/frontend/.idea
/frontend/.vscode
/frontend/.env.local
/frontend/.env.development
/frontend/.env.production
/frontend/.env.test
/backend/node_modules
/backend/dist
/backend/build
/backend/pkg/*
/backend/.idea
/backend/.vscode
/backend/.env.local
/backend/.env.development
/frontapi/db
/frontapi/*.txt
/frontapi/*.json
/frontapi/*.sql
/frontapi/**/*.txt
/frontapi/**/*.json
/frontapi/**/*.sql
/frontapi/**/**/*.txt
/frontapi/**/**/*.json
/frontapi/**/**/*.sql
/frontapi/**/**/**/*.txt
/frontapi/**/**/**/*.json
/frontapi/**/**/**/*.sql
/frontapi/backend/node_modules
/frontapi/backend/**/node_modules
/frontapi/backend/**/**/node_modules
/frontapi/backend/**/**/**/node_modules
/frontapi/backend/**/**/**/**/node_modules
/tmp/
/tmp/**
/**/tmp/
/**/tmp/**
/**/**/tmp/**
/storage/pictures/comics
/storage/pictures/pic
/storage/pictures/pictures
/storage/pictures/video-cover
/storage/static/images/avatar
/storage/static/images/banner
/storage/static/images/covers
/storage/static/images/icons
/storage/static/images/video-cover
/storage/videos
/storage/shortvideos
/*.exe
/**/*.exe
/**/**/*.exe
/**/**/**/*.exe
/**/**/**/**/*.exe
/*.txt
/**/*.txt
/**/**/*.txt
/**/**/**/*.txt



