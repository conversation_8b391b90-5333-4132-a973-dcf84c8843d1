import process from 'node:process';
import { URL, fileURLToPath } from 'node:url';
import { defineConfig, loadEnv } from 'vite';
import { createViteProxy, getBuildTime } from './build/config';
import { setupVitePlugins } from './build/plugins';

export default defineConfig(configEnv => {
    const viteEnv = loadEnv(configEnv.mode, process.cwd()) as unknown as Env.ImportMeta;

    const buildTime = getBuildTime();

    const enableProxy = configEnv.command === 'serve' && !configEnv.isPreview;
    console.log("-------------------");
    console.log(viteEnv.VITE_BASE_URL);
    return {
        base: viteEnv.VITE_BASE_URL,
        resolve: {
            alias: {
                '~': fileURLToPath(new URL('./', import.meta.url)),
                '@': fileURLToPath(new URL('./src', import.meta.url)),
                'debug': fileURLToPath(new URL('./src/utils/debug-shim.js', import.meta.url))
            },
            dedupe: ['vue', 'vue-router', 'pinia'],
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
        },
        css: {
            preprocessorOptions: {
                scss: {
                    api: 'modern-compiler',
                    additionalData: `@use "@/styles/scss/global.scss" as *;`
                }
            }
        },
        plugins: setupVitePlugins(viteEnv, buildTime),
        define: {
            BUILD_TIME: JSON.stringify(buildTime)
        },
        server: {
            host: '0.0.0.0',
            port: 9527,
            open: true,
            proxy: createViteProxy(viteEnv, enableProxy)
        },
        preview: {
            port: 9725
        },
        build: {
            reportCompressedSize: false,
            sourcemap: viteEnv.VITE_SOURCE_MAP === 'Y',
            commonjsOptions: {
                ignoreTryCatch: false,
                transformMixedEsModules: true,
                include: [
                    /node_modules\/debug/,
                    /node_modules\/.*\.cjs/
                ]
            },
            rollupOptions: {
                external: ['node:path', 'node:fs', 'node:process'],
                output: {
                    globals: {
                        'node:path': 'path',
                        'node:fs': 'fs',
                        'node:process': 'process'
                    }
                }
            }
        },
        optimizeDeps: {
            esbuildOptions: {
                define: {
                    global: 'globalThis'
                }
            },
            include: ['debug', 'vue', 'vue-router', 'pinia'],
            exclude: []
        }
    };
});
