<template>
  <div class="chapter-sort-table">
    <div class="sort-mode-tip">
      <el-alert
        title="章节排序模式"
        type="info"
        description="您可以通过拖动行或使用上移/下移按钮来调整章节顺序，完成排序后点击'保存排序'按钮。"
        show-icon
        :closable="false"
      />
    </div>
    
    <el-table 
      v-loading="loading" 
      :data="chapters" 
      border 
      style="width: 100%"
      row-key="id"
      ref="tableRef"
    >
      <el-table-column type="index" width="55" label="#" />
      <el-table-column prop="title" label="章节标题" min-width="150" />
      <el-table-column prop="chapter_number" label="章节序号" width="100" align="center">
        <template #default="{ row, $index }">
          <div class="chapter-drag-handle" @mousedown="startDrag($event, $index)">
            <el-icon class="drag-icon"><Rank /></el-icon>
            <span>{{ $index + 1 }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="word_count" label="字数" width="80" align="center" />
      <el-table-column prop="is_locked" label="付费" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_locked === 1 ? 'danger' : 'success'">
            {{ row.is_locked === 1 ? '付费' : '免费' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template #default="{ row, $index }">
          <el-button-group>
            <el-button 
              type="primary" 
              link 
              :disabled="$index === 0" 
              @click="moveChapter($index, $index - 1)"
            >上移</el-button>
            <el-button 
              type="primary" 
              link 
              :disabled="$index === chapters.length - 1" 
              @click="moveChapter($index, $index + 1)"
            >下移</el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 拖拽提示 -->
    <div v-if="isDragging" class="drag-indicator" :style="dragIndicatorStyle">
      <div class="drag-indicator-line"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import { Rank } from '@element-plus/icons-vue';
import type { BookChapter } from '@/types/books';

const props = defineProps<{
  loading: boolean,
  chapters: BookChapter[]
}>();

const emit = defineEmits(['update:chapters', 'change']);
const tableRef = ref<any>(null);

// 拖拽状态
const isDragging = ref(false);
const dragStartIndex = ref(-1);
const dragCurrentIndex = ref(-1);
const dragStartY = ref(0);
const dragCurrentY = ref(0);

// 拖拽指示器样式
const dragIndicatorStyle = computed(() => {
  if (!isDragging.value) return {};
  
  // 计算拖拽指示器的位置
  const tableEl = tableRef.value?.$el;
  if (!tableEl) return {};
  
  const rows = tableEl.querySelectorAll('.el-table__body tr');
  if (!rows.length) return {};
  
  let top = 0;
  
  if (dragCurrentIndex.value < rows.length) {
    const targetRow = rows[dragCurrentIndex.value];
    const rect = targetRow.getBoundingClientRect();
    const tableRect = tableEl.getBoundingClientRect();
    top = rect.top - tableRect.top;
  }
  
  return {
    top: `${top}px`
  };
});

// 开始拖拽
const startDrag = (e: MouseEvent, index: number) => {
  e.preventDefault();
  
  dragStartIndex.value = index;
  dragCurrentIndex.value = index;
  dragStartY.value = e.clientY;
  dragCurrentY.value = e.clientY;
  isDragging.value = true;
  
  // 添加事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', endDrag);
  
  // 添加拖拽样式
  document.body.classList.add('dragging');
  
  // 获取表格元素
  const tableEl = tableRef.value?.$el;
  if (tableEl) {
    const rows = tableEl.querySelectorAll('.el-table__body tr');
    if (rows[index]) {
      rows[index].classList.add('row-dragging');
    }
  }
};

// 拖拽中
const onDrag = (e: MouseEvent) => {
  if (!isDragging.value) return;
  
  dragCurrentY.value = e.clientY;
  
  // 获取表格元素
  const tableEl = tableRef.value?.$el;
  if (!tableEl) return;
  
  const rows = tableEl.querySelectorAll('.el-table__body tr');
  if (!rows.length) return;
  
  // 计算当前拖拽位置对应的行索引
  for (let i = 0; i < rows.length; i++) {
    const rect = rows[i].getBoundingClientRect();
    // 如果鼠标位置在此行中间以上，则放在此行之前
    if (dragCurrentY.value < rect.top + rect.height / 2) {
      if (dragCurrentIndex.value !== i) {
        dragCurrentIndex.value = i;
      }
      break;
    }
    
    // 如果是最后一行且鼠标在最后一行下方
    if (i === rows.length - 1) {
      dragCurrentIndex.value = rows.length;
    }
  }
};

// 结束拖拽
const endDrag = () => {
  if (!isDragging.value) return;
  
  // 移除事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', endDrag);
  
  // 移除拖拽样式
  document.body.classList.remove('dragging');
  
  // 获取表格元素
  const tableEl = tableRef.value?.$el;
  if (tableEl) {
    const rows = tableEl.querySelectorAll('.el-table__body tr');
    if (rows[dragStartIndex.value]) {
      rows[dragStartIndex.value].classList.remove('row-dragging');
    }
  }
  
  // 如果位置发生变化，则移动章节
  if (dragStartIndex.value !== dragCurrentIndex.value && dragCurrentIndex.value <= props.chapters.length) {
    const targetIndex = dragCurrentIndex.value > dragStartIndex.value 
      ? dragCurrentIndex.value - 1 // 向下拖动
      : dragCurrentIndex.value;    // 向上拖动
    
    moveChapter(dragStartIndex.value, targetIndex);
  }
  
  // 重置拖拽状态
  isDragging.value = false;
  dragStartIndex.value = -1;
  dragCurrentIndex.value = -1;
};

// 移动章节位置
const moveChapter = (fromIndex: number, toIndex: number) => {
  if (fromIndex === toIndex) return;
  if (fromIndex < 0 || toIndex < 0) return;
  if (fromIndex >= props.chapters.length || toIndex >= props.chapters.length) return;
  
  // 创建一个新的数组，避免直接修改props
  const newList = [...props.chapters];
  
  // 移动元素
  const movedItem = newList.splice(fromIndex, 1)[0];
  newList.splice(toIndex, 0, movedItem);
  
  // 更新章节序号
  const updatedList = newList.map((chapter, index) => ({
    ...chapter,
    chapter_number: index + 1,
    sort_order: index + 1
  }));
  
  // 发出更新事件
  emit('update:chapters', updatedList);
  emit('change', true);
};

// 监听数据变化
watch(() => props.chapters, (newChapters) => {
  console.log(`章节排序表格数据更新，共 ${newChapters.length} 条记录`);
}, { immediate: true });

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', endDrag);
  document.body.classList.remove('dragging');
});
</script>

<style scoped>
.chapter-sort-table {
  width: 100%;
  position: relative;
}

.sort-mode-tip {
  margin-bottom: 20px;
}

.chapter-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f9ff;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: grab;
}

.drag-icon {
  margin-right: 5px;
  color: #409eff;
}

/* 拖拽样式 */
:deep(.row-dragging) {
  background-color: var(--el-color-primary-light-9) !important;
  opacity: 0.6;
}

:deep(.el-table__body tr) {
  cursor: default;
}

:deep(.el-table__body tr.row-dragging) {
  cursor: grabbing;
}

.drag-indicator {
  position: absolute;
  width: 100%;
  pointer-events: none;
  z-index: 100;
}

.drag-indicator-line {
  width: 100%;
  height: 2px;
  background-color: var(--el-color-primary);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
}

/* 全局拖拽样式 */
:global(body.dragging) {
  cursor: grabbing !important;
  user-select: none;
}
</style> 