<template>
  <div class="comment-item">
    <div class="comment-main">
      <!-- 用户头像 -->
      <div class="comment-avatar">
        <el-avatar
          :size="40"
          :src="comment.author.avatar"
          :icon="UserFilled"
        />
      </div>
      
      <!-- 评论内容 -->
      <div class="comment-content">
        <!-- 用户信息 -->
        <div class="comment-header">
          <div class="user-info">
            <span class="username">{{ comment.author.username }}</span>
            <el-icon
              v-if="comment.author.isVerified"
              class="verified-icon"
            >
              <Select />
            </el-icon>
            <span class="comment-time">{{ formatTimeAgo(comment.createdAt) }}</span>
          </div>
          
          <!-- 更多操作 -->
          <el-dropdown
            trigger="click"
            @command="handleCommand"
          >
            <el-button
              type="text"
              :icon="MoreFilled"
              class="more-btn"
            />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-if="canDelete"
                  command="delete"
                  class="danger-item"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-dropdown-item>
                <el-dropdown-item command="report">
                  <el-icon><Warning /></el-icon>
                  举报
                </el-dropdown-item>
                <el-dropdown-item command="copy">
                  <el-icon><CopyDocument /></el-icon>
                  复制
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        
        <!-- 回复目标 -->
        <div v-if="comment.replyTo" class="reply-target">
          回复 <span class="reply-username">@{{ comment.replyTo.username }}</span>
        </div>
        
        <!-- 评论文本 -->
        <div class="comment-text">
          {{ comment.content }}
        </div>
        
        <!-- 评论图片/视频 -->
        <div v-if="comment.media" class="comment-media">
          <img
            v-if="comment.media.type === 'image'"
            :src="comment.media.url"
            :alt="comment.media.alt"
            class="comment-image"
            @click="previewImage(comment.media.url)"
          />
          <video
            v-else-if="comment.media.type === 'video'"
            :src="comment.media.url"
            class="comment-video"
            controls
            preload="metadata"
          />
        </div>
        
        <!-- 操作按钮 -->
        <div class="comment-actions">
          <button
            class="action-btn"
            :class="{ 'action-btn--active': comment.isLiked }"
            @click="handleLike"
          >
            <el-icon>
              <Like v-if="comment.isLiked" />
              <Like v-else />
            </el-icon>
            <span v-if="comment.likes > 0">{{ formatNumber(comment.likes) }}</span>
          </button>
          
          <button
            class="action-btn"
            @click="toggleReply"
          >
            <el-icon><ChatDotRound /></el-icon>
            回复
          </button>
          
          <button
            v-if="comment.replies && comment.replies.length > 0"
            class="action-btn"
            @click="toggleReplies"
          >
            <el-icon>
              <ArrowDown v-if="!showReplies" />
              <ArrowUp v-else />
            </el-icon>
            {{ showReplies ? '收起' : '展开' }}回复 ({{ comment.replies.length }})
          </button>
        </div>
        
        <!-- 回复输入框 -->
        <div v-if="showReplyInput" class="reply-input">
          <div class="reply-input-container">
            <el-input
              v-model="replyContent"
              type="textarea"
              :rows="2"
              :placeholder="`回复 @${comment.author.username}`"
              resize="none"
              @keydown.ctrl.enter="submitReply"
            />
            <div class="reply-actions">
              <el-button
                size="small"
                @click="cancelReply"
              >
                取消
              </el-button>
              <el-button
                type="primary"
                size="small"
                :loading="submittingReply"
                :disabled="!replyContent.trim()"
                @click="submitReply"
              >
                回复
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 回复列表 -->
    <div v-if="showReplies && comment.replies && comment.replies.length > 0" class="replies-list">
      <CommentItem
        v-for="reply in comment.replies"
        :key="reply.id"
        :comment="reply"
        :current-user="currentUser"
        :is-reply="true"
        @reply="handleReplyToReply"
        @like="$emit('like', reply.id)"
        @delete="$emit('delete', reply.id)"
        @report="$emit('report', reply.id)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  UserFilled,
  Select,
  MoreFilled,
  Delete,
  Warning,
  CopyDocument,
  Like,
  ChatDotRound,
  ArrowDown,
  ArrowUp
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatTimeAgo, formatNumber } from '@/core/utils'
import type { Comment, User } from '@/types'

export interface CommentItemProps {
  comment: Comment
  currentUser?: User | null
  isReply?: boolean
}

const props = withDefaults(defineProps<CommentItemProps>(), {
  isReply: false
})

const emit = defineEmits<{
  reply: [commentId: string, content: string]
  like: [commentId: string]
  delete: [commentId: string]
  report: [commentId: string]
}>()

// 回复相关状态
const showReplyInput = ref(false)
const replyContent = ref('')
const submittingReply = ref(false)
const showReplies = ref(false)

// 权限检查
const canDelete = computed(() => {
  return props.currentUser && (
    props.currentUser.id === props.comment.author.id ||
    props.currentUser.role === 'admin' ||
    props.currentUser.role === 'moderator'
  )
})

// 切换回复输入框
const toggleReply = () => {
  if (!props.currentUser) {
    ElMessage.warning('请先登录')
    return
  }
  showReplyInput.value = !showReplyInput.value
  if (showReplyInput.value) {
    replyContent.value = ''
  }
}

// 取消回复
const cancelReply = () => {
  showReplyInput.value = false
  replyContent.value = ''
}

// 提交回复
const submitReply = async () => {
  if (!replyContent.value.trim()) {
    return
  }
  
  submittingReply.value = true
  
  try {
    emit('reply', props.comment.id, replyContent.value.trim())
    
    replyContent.value = ''
    showReplyInput.value = false
    
    ElMessage.success('回复成功')
    
  } catch (error) {
    ElMessage.error('回复失败')
  } finally {
    submittingReply.value = false
  }
}

// 切换回复列表显示
const toggleReplies = () => {
  showReplies.value = !showReplies.value
}

// 点赞
const handleLike = () => {
  if (!props.currentUser) {
    ElMessage.warning('请先登录')
    return
  }
  emit('like', props.comment.id)
}

// 回复的回复
const handleReplyToReply = (replyId: string, content: string) => {
  emit('reply', replyId, content)
}

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'delete':
      await handleDelete()
      break
    case 'report':
      emit('report', props.comment.id)
      break
    case 'copy':
      await copyToClipboard()
      break
  }
}

// 删除评论
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条评论吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    emit('delete', props.comment.id)
    
  } catch {
    // 用户取消删除
  }
}

// 复制评论内容
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(props.comment.content)
    ElMessage.success('已复制到剪贴板')
  } catch {
    ElMessage.error('复制失败')
  }
}

// 预览图片
const previewImage = (url: string) => {
  // 这里可以集成图片预览组件
  window.open(url, '_blank')
}
</script>

<style scoped>
.comment-item {
  @apply space-y-3;
}

.comment-main {
  @apply flex gap-3;
}

.comment-avatar {
  @apply flex-shrink-0;
}

.comment-content {
  @apply flex-1 min-w-0;
}

.comment-header {
  @apply flex items-start justify-between mb-2;
}

.user-info {
  @apply flex items-center gap-2 flex-wrap;
}

.username {
  @apply font-medium text-gray-900;
}

.verified-icon {
  @apply text-blue-500 text-sm;
}

.comment-time {
  @apply text-sm text-gray-500;
}

.more-btn {
  @apply text-gray-400 hover:text-gray-600 p-1;
}

.reply-target {
  @apply text-sm text-gray-600 mb-2;
}

.reply-username {
  @apply text-blue-600 font-medium;
}

.comment-text {
  @apply text-gray-900 leading-relaxed mb-3 whitespace-pre-wrap;
}

.comment-media {
  @apply mb-3;
}

.comment-image {
  @apply max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity;
}

.comment-video {
  @apply max-w-sm rounded-lg;
}

.comment-actions {
  @apply flex items-center gap-4;
}

.action-btn {
  @apply flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 transition-colors;
}

.action-btn--active {
  @apply text-red-500 hover:text-red-600;
}

.reply-input {
  @apply mt-3 pl-4 border-l-2 border-gray-200;
}

.reply-input-container {
  @apply space-y-2;
}

.reply-actions {
  @apply flex items-center justify-end gap-2;
}

.replies-list {
  @apply ml-12 space-y-3 border-l-2 border-gray-100 pl-4;
}

.danger-item {
  @apply text-red-600;
}

:deep(.el-textarea__inner) {
  @apply border-gray-300 focus:border-blue-500;
}

:deep(.el-dropdown-menu__item.danger-item:hover) {
  @apply bg-red-50 text-red-700;
}
</style>