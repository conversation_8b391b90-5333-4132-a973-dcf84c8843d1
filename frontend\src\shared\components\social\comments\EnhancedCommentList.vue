<template>
  <div class="enhanced-comment-list">
    <!-- 加载状态 -->
    <div v-if="loading && comments.length === 0" class="loading-state">
      <div class="loading-skeleton">
        <div v-for="i in 3" :key="i" class="skeleton-item">
          <div class="skeleton-avatar"></div>
          <div class="skeleton-content">
            <div class="skeleton-line short"></div>
            <div class="skeleton-line long"></div>
            <div class="skeleton-line medium"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论列表 -->
    <div v-else-if="comments.length > 0" class="comments-container">
      <div
        v-for="comment in comments"
        :key="comment.id"
        class="comment-item"
        :class="{ 'highlighted': highlightedCommentId === comment.id }"
      >
        <!-- 主评论 -->
        <div class="comment-main">
          <div class="comment-avatar">
            <img :src="comment.author.avatar || defaultAvatar" :alt="comment.author.username" />
            <div v-if="comment.author.isVerified" class="verified-badge">
              <i class="icon-verified"></i>
            </div>
          </div>

          <div class="comment-content">
            <!-- 评论头部 -->
            <div class="comment-header">
              <span class="author-name">{{ comment.author.username }}</span>
              <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
              <div v-if="showActions" class="comment-menu">
                <button class="menu-btn" @click="toggleMenu(comment.id)">
                  <i class="icon-more"></i>
                </button>
                <div v-if="activeMenuId === comment.id" class="menu-dropdown">
                  <button @click="reportComment(comment.id)">举报</button>
                  <button v-if="canDelete(comment)" @click="deleteComment(comment.id)">删除</button>
                </div>
              </div>
            </div>

            <!-- 评论文本 -->
            <div class="comment-text">
              <p>{{ comment.content }}</p>
            </div>

            <!-- 媒体内容 -->
            <div v-if="comment.media && comment.media.length > 0" class="comment-media">
              <div
                v-for="(media, index) in comment.media"
                :key="media.id"
                class="media-item"
                :class="{ 'video': media.type === 'video' }"
                @click="previewMedia(comment.media, index)"
              >
                <img v-if="media.type === 'image'" :src="media.url" :alt="`图片 ${index + 1}`" />
                <div v-else class="video-thumbnail">
                  <img :src="media.thumbnail || media.url" :alt="`视频 ${index + 1}`" />
                  <div class="video-play-overlay">
                    <i class="icon-play"></i>
                    <span v-if="media.duration" class="duration">{{ formatDuration(media.duration) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 评论操作 -->
            <div class="comment-actions">
              <button
                class="action-btn like-btn"
                :class="{ 'liked': comment.isLiked }"
                @click="toggleLike(comment.id)"
              >
                <i class="icon-like"></i>
                <span>{{ comment.likes > 0 ? comment.likes : '点赞' }}</span>
              </button>
              <button class="action-btn reply-btn" @click="startReply(comment)">
                <i class="icon-reply"></i>
                <span>回复</span>
              </button>
              <button v-if="showShare" class="action-btn share-btn" @click="shareComment(comment)">
                <i class="icon-share"></i>
                <span>分享</span>
              </button>
            </div>

            <!-- 回复输入框 -->
            <div v-if="replyingTo === comment.id" class="reply-input-container">
              <EnhancedCommentInput
                :placeholder="`回复 @${comment.author.username}`"
                :reply-to="comment"
                :support-image="supportImage"
                :support-video="supportVideo"
                :current-user-avatar="currentUserAvatar"
                :current-user-name="currentUserName"
                submit-text="回复"
                auto-focus
                @submit="handleReply"
                @cancel-reply="cancelReply"
              />
            </div>

            <!-- 回复列表 -->
            <div v-if="comment.replies && comment.replies.length > 0" class="replies-container">
              <div class="replies-header">
                <span class="replies-count">{{ comment.replies.length }} 条回复</span>
                <button
                  v-if="comment.replies.length > maxVisibleReplies"
                  class="toggle-replies-btn"
                  @click="toggleReplies(comment.id)"
                >
                  {{ expandedReplies.has(comment.id) ? '收起回复' : `查看全部 ${comment.replies.length} 条回复` }}
                </button>
              </div>

              <div class="replies-list">
                <div
                  v-for="(reply, index) in getVisibleReplies(comment)"
                  :key="reply.id"
                  class="reply-item"
                  :class="{ 'highlighted': highlightedCommentId === reply.id }"
                >
                  <div class="reply-avatar">
                    <img :src="reply.author.avatar || defaultAvatar" :alt="reply.author.username" />
                  </div>

                  <div class="reply-content">
                    <div class="reply-header">
                      <span class="reply-author">{{ reply.author.username }}</span>
                      <span v-if="reply.replyTo" class="reply-target">
                        回复 @{{ reply.replyTo.author.username }}
                      </span>
                      <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
                    </div>

                    <div class="reply-text">
                      <p>{{ reply.content }}</p>
                    </div>

                    <!-- 回复媒体 -->
                    <div v-if="reply.media && reply.media.length > 0" class="reply-media">
                      <div
                        v-for="(media, mediaIndex) in reply.media"
                        :key="media.id"
                        class="media-item small"
                        @click="previewMedia(reply.media, mediaIndex)"
                      >
                        <img v-if="media.type === 'image'" :src="media.url" :alt="`图片 ${mediaIndex + 1}`" />
                        <div v-else class="video-thumbnail">
                          <img :src="media.thumbnail || media.url" :alt="`视频 ${mediaIndex + 1}`" />
                          <div class="video-play-overlay">
                            <i class="icon-play"></i>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="reply-actions">
                      <button
                        class="action-btn like-btn small"
                        :class="{ 'liked': reply.isLiked }"
                        @click="toggleLike(reply.id)"
                      >
                        <i class="icon-like"></i>
                        <span>{{ reply.likes > 0 ? reply.likes : '' }}</span>
                      </button>
                      <button class="action-btn reply-btn small" @click="startReply(reply, comment)">
                        <i class="icon-reply"></i>
                        <span>回复</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more-container">
        <button
          v-if="!loading"
          class="load-more-btn"
          @click="$emit('load-more')"
        >
          加载更多评论
        </button>
        <div v-else class="loading-more">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">
        <i class="icon-comment-empty"></i>
      </div>
      <h3>暂无评论</h3>
      <p>快来发表第一条评论吧！</p>
    </div>

    <!-- 媒体预览弹窗 -->
    <MediaPreviewDialog
      v-model="showMediaPreview"
      :media-list="previewMediaList"
      :current-index="previewCurrentIndex"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import EnhancedCommentInput from './EnhancedCommentInput.vue'
import MediaPreviewDialog from './MediaPreviewDialog.vue'
import type { Comment, MediaFile, CommentSubmitData } from '../../types/comment'

// 组件属性
interface Props {
  comments: Comment[]
  loading?: boolean
  hasMore?: boolean
  
  // 功能配置
  supportImage?: boolean
  supportVideo?: boolean
  showActions?: boolean
  showShare?: boolean
  maxVisibleReplies?: number
  
  // 用户信息
  currentUserAvatar?: string
  currentUserName?: string
  currentUserId?: string
  
  // 高亮评论
  highlightedCommentId?: string
}

// 组件事件
interface Emits {
  'like': [commentId: string]
  'reply': [parentId: string, replyData: CommentSubmitData]
  'delete': [commentId: string]
  'report': [commentId: string]
  'share': [comment: Comment]
  'load-more': []
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasMore: false,
  supportImage: true,
  supportVideo: true,
  showActions: true,
  showShare: true,
  maxVisibleReplies: 3
})

const emit = defineEmits<Emits>()

// 响应式数据
const replyingTo = ref<string | null>(null)
const expandedReplies = ref(new Set<string>())
const activeMenuId = ref<string | null>(null)
const showMediaPreview = ref(false)
const previewMediaList = ref<MediaFile[]>([])
const previewCurrentIndex = ref(0)

// 默认头像
const defaultAvatar = 'https://api.dicebear.com/7.x/avataaars/svg?seed=default'

// 计算属性
const canDelete = (comment: Comment) => {
  return comment.author.id === props.currentUserId
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 方法
const formatTime = (dateString: string | number) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString()
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const toggleLike = (commentId: string) => {
  emit('like', commentId)
}

const startReply = (comment: Comment, parentComment?: Comment) => {
  replyingTo.value = comment.id
}

const cancelReply = () => {
  replyingTo.value = null
}

const handleReply = (replyData: CommentSubmitData) => {
  if (!replyingTo.value) return
  
  emit('reply', replyingTo.value, replyData)
  replyingTo.value = null
}

const toggleReplies = (commentId: string) => {
  if (expandedReplies.value.has(commentId)) {
    expandedReplies.value.delete(commentId)
  } else {
    expandedReplies.value.add(commentId)
  }
}

const getVisibleReplies = (comment: Comment) => {
  if (!comment.replies) return []
  
  if (expandedReplies.value.has(comment.id)) {
    return comment.replies
  }
  
  return comment.replies.slice(0, props.maxVisibleReplies)
}

const toggleMenu = (commentId: string) => {
  activeMenuId.value = activeMenuId.value === commentId ? null : commentId
}

const deleteComment = (commentId: string) => {
  emit('delete', commentId)
  activeMenuId.value = null
}

const reportComment = (commentId: string) => {
  emit('report', commentId)
  activeMenuId.value = null
  ElMessage.success('举报已提交')
}

const shareComment = (comment: Comment) => {
  emit('share', comment)
}

const previewMedia = (mediaList: MediaFile[], currentIndex: number) => {
  previewMediaList.value = mediaList
  previewCurrentIndex.value = currentIndex
  showMediaPreview.value = true
}

const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.comment-menu')) {
    activeMenuId.value = null
  }
}
</script>

<style scoped lang="scss">
.enhanced-comment-list {
  .loading-state {
    padding: 20px 0;

    .loading-skeleton {
      .skeleton-item {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;

        .skeleton-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
        }

        .skeleton-content {
          flex: 1;

          .skeleton-line {
            height: 12px;
            border-radius: 6px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            margin-bottom: 8px;

            &.short { width: 30%; }
            &.medium { width: 60%; }
            &.long { width: 80%; }
          }
        }
      }
    }
  }

  .comments-container {
    .comment-item {
      padding: 16px 0;
      border-bottom: 1px solid #f3f4f6;
      transition: background-color 0.2s ease;

      &.highlighted {
        background: #fef3c7;
        border-radius: 8px;
        padding: 16px;
        margin: 8px 0;
      }

      &:last-child {
        border-bottom: none;
      }

      .comment-main {
        display: flex;
        gap: 12px;

        .comment-avatar {
          position: relative;
          flex-shrink: 0;

          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
          }

          .verified-badge {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .icon-verified {
              width: 10px;
              height: 10px;
              background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'/%3E%3C/svg%3E") no-repeat center;
              background-size: contain;
            }
          }
        }

        .comment-content {
          flex: 1;
          min-width: 0;

          .comment-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;

            .author-name {
              font-weight: 600;
              color: #1f2937;
              font-size: 14px;
            }

            .comment-time {
              font-size: 12px;
              color: #6b7280;
            }

            .comment-menu {
              position: relative;
              margin-left: auto;

              .menu-btn {
                width: 24px;
                height: 24px;
                border: none;
                background: transparent;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s ease;

                &:hover {
                  background: #f3f4f6;
                }

                .icon-more {
                  width: 16px;
                  height: 16px;
                  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%236b7280' viewBox='0 0 24 24'%3E%3Cpath d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'/%3E%3C/svg%3E") no-repeat center;
                  background-size: contain;
                }
              }

              .menu-dropdown {
                position: absolute;
                top: 100%;
                right: 0;
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                z-index: 100;
                min-width: 80px;

                button {
                  display: block;
                  width: 100%;
                  padding: 8px 12px;
                  border: none;
                  background: transparent;
                  text-align: left;
                  font-size: 13px;
                  cursor: pointer;
                  transition: background-color 0.2s ease;

                  &:hover {
                    background: #f3f4f6;
                  }

                  &:first-child {
                    border-radius: 6px 6px 0 0;
                  }

                  &:last-child {
                    border-radius: 0 0 6px 6px;
                  }
                }
              }
            }
          }

          .comment-text {
            margin-bottom: 8px;
            color: #374151;
            line-height: 1.5;
            font-size: 14px;

            p {
              margin: 0;
              word-wrap: break-word;
            }
          }

          .comment-media {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 8px;
            margin-bottom: 12px;
            max-width: 400px;

            .media-item {
              position: relative;
              aspect-ratio: 1;
              border-radius: 8px;
              overflow: hidden;
              cursor: pointer;
              transition: transform 0.2s ease;

              &:hover {
                transform: scale(1.02);
              }

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .video-thumbnail {
                position: relative;
                width: 100%;
                height: 100%;

                .video-play-overlay {
                  position: absolute;
                  inset: 0;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  background: rgba(0, 0, 0, 0.3);
                  color: white;

                  .icon-play {
                    width: 24px;
                    height: 24px;
                    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E") no-repeat center;
                    background-size: contain;
                    margin-bottom: 4px;
                  }

                  .duration {
                    font-size: 11px;
                    background: rgba(0, 0, 0, 0.6);
                    padding: 2px 4px;
                    border-radius: 3px;
                  }
                }
              }
            }
          }

          .comment-actions {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 12px;

            .action-btn {
              display: flex;
              align-items: center;
              gap: 4px;
              padding: 4px 8px;
              border: none;
              background: transparent;
              border-radius: 4px;
              font-size: 13px;
              color: #6b7280;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                background: #f3f4f6;
                color: #374151;
              }

              &.liked {
                color: #ef4444;

                .icon-like {
                  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ef4444' viewBox='0 0 24 24'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'/%3E%3C/svg%3E");
                }
              }

              i {
                width: 16px;
                height: 16px;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;

                &.icon-like {
                  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'/%3E%3C/svg%3E");
                }

                &.icon-reply {
                  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6'/%3E%3C/svg%3E");
                }

                &.icon-share {
                  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z'/%3E%3C/svg%3E");
                }
              }
            }
          }

          .reply-input-container {
            margin-bottom: 16px;
          }

          .replies-container {
            .replies-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 12px;

              .replies-count {
                font-size: 13px;
                color: #6b7280;
                font-weight: 500;
              }

              .toggle-replies-btn {
                border: none;
                background: transparent;
                color: #3b82f6;
                font-size: 13px;
                cursor: pointer;
                padding: 4px 0;

                &:hover {
                  text-decoration: underline;
                }
              }
            }

            .replies-list {
              .reply-item {
                display: flex;
                gap: 8px;
                margin-bottom: 12px;
                padding: 8px;
                border-radius: 6px;
                transition: background-color 0.2s ease;

                &.highlighted {
                  background: #fef3c7;
                }

                &:hover {
                  background: #f9fafb;
                }

                .reply-avatar {
                  flex-shrink: 0;

                  img {
                    width: 28px;
                    height: 28px;
                    border-radius: 50%;
                    object-fit: cover;
                  }
                }

                .reply-content {
                  flex: 1;
                  min-width: 0;

                  .reply-header {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    margin-bottom: 2px;

                    .reply-author {
                      font-weight: 500;
                      color: #1f2937;
                      font-size: 13px;
                    }

                    .reply-target {
                      color: #6b7280;
                      font-size: 12px;
                    }

                    .reply-time {
                      color: #9ca3af;
                      font-size: 11px;
                      margin-left: auto;
                    }
                  }

                  .reply-text {
                    color: #374151;
                    font-size: 13px;
                    line-height: 1.4;
                    margin-bottom: 6px;

                    p {
                      margin: 0;
                      word-wrap: break-word;
                    }
                  }

                  .reply-media {
                    display: flex;
                    gap: 4px;
                    margin-bottom: 6px;

                    .media-item.small {
                      width: 60px;
                      height: 60px;
                      border-radius: 4px;
                      overflow: hidden;
                      cursor: pointer;

                      img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                      }

                      .video-thumbnail .video-play-overlay {
                        .icon-play {
                          width: 16px;
                          height: 16px;
                        }
                      }
                    }
                  }

                  .reply-actions {
                    display: flex;
                    align-items: center;
                    gap: 12px;

                    .action-btn.small {
                      padding: 2px 6px;
                      font-size: 12px;

                      i {
                        width: 14px;
                        height: 14px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .load-more-container {
      padding: 20px 0;
      text-align: center;

      .load-more-btn {
        padding: 8px 24px;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }
      }

      .loading-more {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #6b7280;
        font-size: 14px;

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;

    .empty-icon {
      margin-bottom: 16px;

      .icon-comment-empty {
        width: 64px;
        height: 64px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='1' d='M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        display: inline-block;
      }
    }

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #374151;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

// 动画
@keyframes skeleton-loading {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .enhanced-comment-list {
    .comments-container .comment-item {
      padding: 12px 0;

      .comment-main {
        gap: 8px;

        .comment-avatar img {
          width: 32px;
          height: 32px;
        }

        .comment-content {
          .comment-media {
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            max-width: 300px;
          }

          .comment-actions {
            gap: 12px;

            .action-btn {
              padding: 3px 6px;
              font-size: 12px;

              i {
                width: 14px;
                height: 14px;
              }
            }
          }

          .replies-container .replies-list .reply-item {
            gap: 6px;

            .reply-avatar img {
              width: 24px;
              height: 24px;
            }

            .reply-content .reply-media .media-item.small {
              width: 50px;
              height: 50px;
            }
          }
        }
      }
    }

    .empty-state {
      padding: 40px 16px;

      .empty-icon .icon-comment-empty {
        width: 48px;
        height: 48px;
      }

      h3 {
        font-size: 16px;
      }

      p {
        font-size: 13px;
      }
    }
  }
}
</style> 