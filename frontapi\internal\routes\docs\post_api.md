# 帖子管理接口文档

## 帖子接口 (/community/posts)

### 1. 获取帖子列表

**接口地址**: `POST /api/community/posts/getPostList`

**接口描述**: 获取社区帖子列表，支持分页和筛选

**请求参数**:
```json
{
  "data": {
    "keyword": "搜索关键词",
    "category_id": "分类ID",
    "user_id": "用户ID",
    "status": 1,
    "sort_by": "created_at",
    "sort_order": "desc",
    "post_type": "text"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "post_id",
        "title": "帖子标题",
        "content": "帖子内容",
        "images": ["图片URL1", "图片URL2"],
        "video_url": "视频URL",
        "user_id": "用户ID",
        "username": "用户名",
        "avatar": "用户头像",
        "likes_count": 100,
        "comments_count": 20,
        "shares_count": 5,
        "views_count": 1000,
        "is_liked": false,
        "is_followed": false,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 500,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取推荐创作者用户

**接口地址**: `POST /api/community/posts/getRecommendedCreatorUsers`

**接口描述**: 获取推荐的创作者用户列表

**请求参数**:
```json
{
  "data": {
    "user_id": "当前用户ID",
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "user_id",
        "username": "用户名",
        "nickname": "昵称",
        "avatar": "头像URL",
        "bio": "个人简介",
        "followers_count": 1000,
        "posts_count": 50,
        "videos_count": 20,
        "is_followed": false,
        "is_verified": true
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 10
  }
}
```

### 3. 获取推荐关注用户

**接口地址**: `POST /api/community/posts/getRecommendedFollowUsers`

**接口描述**: 获取推荐关注的用户列表

**请求参数**:
```json
{
  "data": {
    "user_id": "当前用户ID",
    "exclude_followed": true
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 4. 获取关注用户的帖子

**接口地址**: `POST /api/community/posts/getPostFollowingUsers`

**接口描述**: 获取当前用户关注的用户发布的帖子

**请求参数**:
```json
{
  "data": {
    "user_id": "当前用户ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 5. 获取热门用户排行榜

**接口地址**: `POST /api/community/posts/getTopHotUserList`

**接口描述**: 获取热门用户排行榜

**请求参数**:
```json
{
  "data": {
    "time_range": "week",
    "category_id": "分类ID"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "rank": 1,
        "user_id": "user_id",
        "username": "用户名",
        "avatar": "头像URL",
        "followers_count": 10000,
        "likes_count": 50000,
        "posts_count": 100,
        "hot_score": 95.5
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 6. 获取帖子详情

**接口地址**: `POST /api/community/posts/getPostDetail`

**接口描述**: 获取指定帖子的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "post_id",
    "user_id": "当前用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "post_id",
    "title": "帖子标题",
    "content": "帖子详细内容",
    "images": ["图片URL1", "图片URL2"],
    "video_url": "视频URL",
    "user_id": "用户ID",
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "用户头像",
    "bio": "用户简介",
    "likes_count": 100,
    "comments_count": 20,
    "shares_count": 5,
    "views_count": 1000,
    "is_liked": false,
    "is_followed": false,
    "is_collected": false,
    "tags": ["标签1", "标签2"],
    "location": "发布地点",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 7. 获取推荐视频

**接口地址**: `POST /api/community/posts/getRecommendedVideos`

**接口描述**: 获取推荐视频列表

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "category_id": "分类ID",
    "exclude_video_ids": ["video_id1", "video_id2"]
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 8. 获取推荐帖子列表

**接口地址**: `POST /api/community/posts/getRecommendedPostList`

**接口描述**: 获取个性化推荐的帖子列表

**请求参数**:
```json
{
  "data": {
    "user_id": "用户ID",
    "exclude_post_ids": ["post_id1", "post_id2"]
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

### 9. 获取推荐用户

**接口地址**: `POST /api/community/posts/getRecommendedUsers`

**接口描述**: 获取推荐用户列表

**请求参数**:
```json
{
  "data": {
    "user_id": "当前用户ID",
    "recommendation_type": "similar_interests"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

## 帖子评论接口 (/community/post/comments)

### 1. 获取帖子评论列表

**接口地址**: `POST /api/community/post/comments/getPostCommentList`

**接口描述**: 获取指定帖子的评论列表

**请求参数**:
```json
{
  "data": {
    "post_id": "post_id",
    "parent_id": "父评论ID",
    "sort_by": "created_at",
    "sort_order": "desc"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "list": [
      {
        "id": "comment_id",
        "content": "评论内容",
        "user_id": "用户ID",
        "username": "用户名",
        "nickname": "昵称",
        "avatar": "用户头像",
        "post_id": "帖子ID",
        "parent_id": "父评论ID",
        "likes_count": 10,
        "replies_count": 5,
        "is_liked": false,
        "images": ["图片URL"],
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取评论详情

**接口地址**: `POST /api/community/post/comments/getCommentDetail`

**接口描述**: 获取指定评论的详细信息

**请求参数**:
```json
{
  "data": {
    "id": "comment_id",
    "user_id": "当前用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "请求成功",
  "data": {
    "id": "comment_id",
    "content": "评论内容",
    "user_id": "用户ID",
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "用户头像",
    "post_id": "帖子ID",
    "parent_id": "父评论ID",
    "likes_count": 10,
    "replies_count": 5,
    "is_liked": false,
    "images": ["图片URL"],
    "replies": [
      {
        "id": "reply_id",
        "content": "回复内容",
        "user_id": "回复用户ID",
        "username": "回复用户名",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 添加评论

**接口地址**: `POST /api/community/post/comments/addComment`

**接口描述**: 为帖子添加评论

**请求参数**:
```json
{
  "data": {
    "post_id": "post_id",
    "content": "评论内容",
    "parent_id": "父评论ID",
    "images": ["图片URL1", "图片URL2"],
    "mentioned_users": ["@用户名1", "@用户名2"]
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "评论添加成功",
  "data": {
    "id": "comment_id",
    "content": "评论内容",
    "user_id": "用户ID",
    "post_id": "帖子ID",
    "parent_id": "父评论ID",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 删除评论

**接口地址**: `POST /api/community/post/comments/deleteComment`

**接口描述**: 删除指定评论

**请求参数**:
```json
{
  "data": {
    "id": "comment_id",
    "user_id": "当前用户ID"
  }
}
```

**响应示例**:
```json
{
  "code": 2000,
  "message": "评论删除成功",
  "data": {
    "deleted_id": "comment_id"
  }
}
```

### 5. 点赞评论

**接口地址**: `POST /api/community/post/comments/likeComment`

**接口描述**: 为评论点赞

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "user_id": "用户ID"
  }
}
```

### 6. 取消点赞评论

**接口地址**: `POST /api/community/post/comments/unlikeComment`

**接口描述**: 取消评论点赞

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "user_id": "用户ID"
  }
}
```

### 7. 举报评论

**接口地址**: `POST /api/community/post/comments/reportComment`

**接口描述**: 举报不当评论

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id",
    "user_id": "举报用户ID",
    "reason": "举报原因",
    "description": "详细描述",
    "evidence_images": ["证据图片URL"]
  }
}
```

### 8. 获取评论回复

**接口地址**: `POST /api/community/post/comments/getCommentReplies`

**接口描述**: 获取指定评论的回复列表

**请求参数**:
```json
{
  "data": {
    "comment_id": "comment_id"
  },
  "page": {
    "pageNo": 1,
    "pageSize": 10
  }
}
```