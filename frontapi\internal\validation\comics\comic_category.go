package comics

// ComicCategoryCreateRequest 漫画分类创建请求验证模型
type ComicCategoryCreateRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	SortOrder   int    `json:"sortOrder"`
	Status      int    `json:"status" validate:"required,min=0"`
	// CreatedAt time.Time `json:"createdAt" validate:"required"`
	// UpdatedAt time.Time `json:"updatedAt" validate:"required"`
}

// ComicCategoryUpdateRequest 漫画分类更新请求验证模型
type ComicCategoryUpdateRequest struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=100"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	SortOrder   int    `json:"sortOrder"`
	Status      int    `json:"status" validate:"omitempty,min=0"`
	// CreatedAt time.Time `json:"createdAt" validate:"required"`
	// UpdatedAt time.Time `json:"updatedAt" validate:"required"`
}
