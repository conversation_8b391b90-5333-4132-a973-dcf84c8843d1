// API基础配置
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
  VERSION: 'v1'
} as const

// HTTP状态码
export const HTTP_STATUS = {
  // 成功
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  
  // 重定向
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  NOT_MODIFIED: 304,
  
  // 客户端错误
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  PAYLOAD_TOO_LARGE: 413,
  URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  
  // 服务器错误
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505
} as const

// 业务状态码
export const BUSINESS_CODE = {
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  
  // 客户端错误
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  TOO_MANY_REQUESTS: 429,
  
  // 服务器错误
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
  
  // 业务错误
  USER_NOT_FOUND: 1001,
  USER_ALREADY_EXISTS: 1002,
  INVALID_CREDENTIALS: 1003,
  TOKEN_EXPIRED: 1004,
  TOKEN_INVALID: 1005,
  PERMISSION_DENIED: 1006,
  ACCOUNT_LOCKED: 1007,
  ACCOUNT_DISABLED: 1008,
  EMAIL_NOT_VERIFIED: 1009,
  PHONE_NOT_VERIFIED: 1010,
  
  VIDEO_NOT_FOUND: 2001,
  VIDEO_PROCESSING: 2002,
  VIDEO_UPLOAD_FAILED: 2003,
  VIDEO_TOO_LARGE: 2004,
  VIDEO_FORMAT_UNSUPPORTED: 2005,
  VIDEO_DURATION_EXCEEDED: 2006,
  VIDEO_ALREADY_EXISTS: 2007,
  VIDEO_DELETED: 2008,
  VIDEO_PRIVATE: 2009,
  VIDEO_BLOCKED: 2010,
  
  COMMENT_NOT_FOUND: 3001,
  COMMENT_DELETED: 3002,
  COMMENT_TOO_LONG: 3003,
  COMMENT_SPAM: 3004,
  COMMENT_BLOCKED: 3005,
  
  LIVE_ROOM_NOT_FOUND: 4001,
  LIVE_ROOM_FULL: 4002,
  LIVE_ROOM_ENDED: 4003,
  LIVE_STREAM_FAILED: 4004,
  LIVE_PERMISSION_DENIED: 4005,
  
  UPLOAD_FAILED: 5001,
  UPLOAD_TOO_LARGE: 5002,
  UPLOAD_FORMAT_UNSUPPORTED: 5003,
  UPLOAD_QUOTA_EXCEEDED: 5004,
  
  SEARCH_QUERY_TOO_SHORT: 6001,
  SEARCH_QUERY_TOO_LONG: 6002,
  SEARCH_NO_RESULTS: 6003,
  
  NOTIFICATION_NOT_FOUND: 7001,
  NOTIFICATION_ALREADY_READ: 7002,
  
  PAYMENT_FAILED: 8001,
  PAYMENT_CANCELLED: 8002,
  PAYMENT_EXPIRED: 8003,
  INSUFFICIENT_BALANCE: 8004,
  
  RATE_LIMIT_EXCEEDED: 9001,
  MAINTENANCE_MODE: 9002,
  FEATURE_DISABLED: 9003
} as const

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    VERIFY_EMAIL: '/auth/verify-email',
    VERIFY_PHONE: '/auth/verify-phone',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    CHANGE_PASSWORD: '/auth/change-password',
    SOCIAL_LOGIN: '/auth/social',
    TWO_FACTOR: '/auth/2fa'
  },
  
  // 用户相关
  USER: {
    PROFILE: '/user/profile',
    UPDATE_PROFILE: '/user/profile',
    AVATAR: '/user/avatar',
    COVER: '/user/cover',
    SETTINGS: '/user/settings',
    PREFERENCES: '/user/preferences',
    PRIVACY: '/user/privacy',
    SECURITY: '/user/security',
    NOTIFICATIONS: '/user/notifications',
    FOLLOWERS: '/user/followers',
    FOLLOWING: '/user/following',
    BLOCKED: '/user/blocked',
    FAVORITES: '/user/favorites',
    HISTORY: '/user/history',
    PLAYLISTS: '/user/playlists',
    SUBSCRIPTIONS: '/user/subscriptions',
    ANALYTICS: '/user/analytics',
    EARNINGS: '/user/earnings',
    VERIFICATION: '/user/verification',
    DELETE_ACCOUNT: '/user/delete'
  },
  
  // 视频相关
  VIDEO: {
    LIST: '/videos',
    DETAIL: '/videos/:id',
    CREATE: '/videos',
    UPDATE: '/videos/:id',
    DELETE: '/videos/:id',
    UPLOAD: '/videos/upload',
    THUMBNAIL: '/videos/:id/thumbnail',
    SUBTITLES: '/videos/:id/subtitles',
    CHAPTERS: '/videos/:id/chapters',
    TRANSCODING: '/videos/:id/transcoding',
    ANALYTICS: '/videos/:id/analytics',
    LIKE: '/videos/:id/like',
    UNLIKE: '/videos/:id/unlike',
    SHARE: '/videos/:id/share',
    REPORT: '/videos/:id/report',
    DOWNLOAD: '/videos/:id/download',
    EMBED: '/videos/:id/embed',
    RELATED: '/videos/:id/related',
    RECOMMENDATIONS: '/videos/recommendations',
    TRENDING: '/videos/trending',
    POPULAR: '/videos/popular',
    RECENT: '/videos/recent',
    CATEGORIES: '/videos/categories',
    TAGS: '/videos/tags',
    SEARCH: '/videos/search'
  },
  
  // 短视频相关
  SHORTS: {
    LIST: '/shorts',
    DETAIL: '/shorts/:id',
    CREATE: '/shorts',
    UPDATE: '/shorts/:id',
    DELETE: '/shorts/:id',
    UPLOAD: '/shorts/upload',
    FEED: '/shorts/feed',
    TRENDING: '/shorts/trending',
    CHALLENGES: '/shorts/challenges',
    EFFECTS: '/shorts/effects',
    MUSIC: '/shorts/music',
    DUET: '/shorts/:id/duet',
    REMIX: '/shorts/:id/remix'
  },
  
  // 评论相关
  COMMENT: {
    LIST: '/comments',
    DETAIL: '/comments/:id',
    CREATE: '/comments',
    UPDATE: '/comments/:id',
    DELETE: '/comments/:id',
    LIKE: '/comments/:id/like',
    UNLIKE: '/comments/:id/unlike',
    REPLY: '/comments/:id/reply',
    REPLIES: '/comments/:id/replies',
    REPORT: '/comments/:id/report',
    PIN: '/comments/:id/pin',
    UNPIN: '/comments/:id/unpin'
  },
  
  // 直播相关
  LIVE: {
    ROOMS: '/live/rooms',
    ROOM_DETAIL: '/live/rooms/:id',
    CREATE_ROOM: '/live/rooms',
    UPDATE_ROOM: '/live/rooms/:id',
    DELETE_ROOM: '/live/rooms/:id',
    JOIN_ROOM: '/live/rooms/:id/join',
    LEAVE_ROOM: '/live/rooms/:id/leave',
    START_STREAM: '/live/stream/start',
    END_STREAM: '/live/stream/:id/end',
    STREAM_KEY: '/live/stream/key',
    MESSAGES: '/live/rooms/:id/messages',
    VIEWERS: '/live/rooms/:id/viewers',
    GIFTS: '/live/gifts',
    SEND_GIFT: '/live/rooms/:id/gift',
    MODERATORS: '/live/rooms/:id/moderators',
    BANNED_USERS: '/live/rooms/:id/banned',
    ANALYTICS: '/live/rooms/:id/analytics'
  },
  
  // 搜索相关
  SEARCH: {
    GLOBAL: '/search',
    VIDEOS: '/search/videos',
    USERS: '/search/users',
    CHANNELS: '/search/channels',
    PLAYLISTS: '/search/playlists',
    LIVE: '/search/live',
    SUGGESTIONS: '/search/suggestions',
    TRENDING: '/search/trending',
    HISTORY: '/search/history',
    FILTERS: '/search/filters'
  },
  
  // 上传相关
  UPLOAD: {
    INIT: '/upload/init',
    CHUNK: '/upload/chunk',
    MERGE: '/upload/merge',
    CANCEL: '/upload/cancel',
    RESUME: '/upload/resume',
    PROGRESS: '/upload/progress',
    PRESIGNED_URL: '/upload/presigned-url',
    DIRECT: '/upload/direct'
  },
  
  // 通知相关
  NOTIFICATION: {
    LIST: '/notifications',
    UNREAD_COUNT: '/notifications/unread-count',
    MARK_READ: '/notifications/:id/read',
    MARK_ALL_READ: '/notifications/read-all',
    DELETE: '/notifications/:id',
    DELETE_ALL: '/notifications/delete-all',
    SETTINGS: '/notifications/settings',
    SUBSCRIBE: '/notifications/subscribe',
    UNSUBSCRIBE: '/notifications/unsubscribe'
  },
  
  // 社交相关
  SOCIAL: {
    FOLLOW: '/social/follow',
    UNFOLLOW: '/social/unfollow',
    BLOCK: '/social/block',
    UNBLOCK: '/social/unblock',
    REPORT: '/social/report',
    SHARE: '/social/share',
    INVITE: '/social/invite',
    CONTACTS: '/social/contacts',
    SUGGESTIONS: '/social/suggestions'
  },
  
  // 内容管理
  CONTENT: {
    CATEGORIES: '/content/categories',
    TAGS: '/content/tags',
    PLAYLISTS: '/content/playlists',
    COLLECTIONS: '/content/collections',
    BOOKMARKS: '/content/bookmarks',
    WATCH_LATER: '/content/watch-later',
    HISTORY: '/content/history',
    RECOMMENDATIONS: '/content/recommendations',
    TRENDING: '/content/trending',
    FEATURED: '/content/featured'
  },
  
  // 分析统计
  ANALYTICS: {
    OVERVIEW: '/analytics/overview',
    VIDEOS: '/analytics/videos',
    AUDIENCE: '/analytics/audience',
    REVENUE: '/analytics/revenue',
    ENGAGEMENT: '/analytics/engagement',
    TRAFFIC: '/analytics/traffic',
    DEMOGRAPHICS: '/analytics/demographics',
    DEVICES: '/analytics/devices',
    GEOGRAPHY: '/analytics/geography',
    REAL_TIME: '/analytics/real-time'
  },
  
  // 支付相关
  PAYMENT: {
    METHODS: '/payment/methods',
    ADD_METHOD: '/payment/methods',
    DELETE_METHOD: '/payment/methods/:id',
    CHARGE: '/payment/charge',
    REFUND: '/payment/refund',
    HISTORY: '/payment/history',
    INVOICES: '/payment/invoices',
    SUBSCRIPTIONS: '/payment/subscriptions',
    BILLING: '/payment/billing',
    WALLET: '/payment/wallet'
  },
  
  // 管理相关
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    VIDEOS: '/admin/videos',
    COMMENTS: '/admin/comments',
    REPORTS: '/admin/reports',
    ANALYTICS: '/admin/analytics',
    SETTINGS: '/admin/settings',
    LOGS: '/admin/logs',
    MODERATION: '/admin/moderation',
    CONTENT_REVIEW: '/admin/content-review'
  },
  
  // 系统相关
  SYSTEM: {
    HEALTH: '/system/health',
    STATUS: '/system/status',
    VERSION: '/system/version',
    CONFIG: '/system/config',
    MAINTENANCE: '/system/maintenance',
    ANNOUNCEMENTS: '/system/announcements',
    FEEDBACK: '/system/feedback',
    SUPPORT: '/system/support'
  }
} as const

// WebSocket事件
export const WS_EVENTS = {
  // 连接事件
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
  RECONNECT: 'reconnect',
  
  // 用户事件
  USER_ONLINE: 'user:online',
  USER_OFFLINE: 'user:offline',
  USER_TYPING: 'user:typing',
  USER_STOP_TYPING: 'user:stop_typing',
  
  // 直播事件
  LIVE_JOIN: 'live:join',
  LIVE_LEAVE: 'live:leave',
  LIVE_MESSAGE: 'live:message',
  LIVE_GIFT: 'live:gift',
  LIVE_LIKE: 'live:like',
  LIVE_VIEWER_COUNT: 'live:viewer_count',
  LIVE_STREAM_START: 'live:stream_start',
  LIVE_STREAM_END: 'live:stream_end',
  LIVE_ROOM_UPDATE: 'live:room_update',
  
  // 通知事件
  NOTIFICATION_NEW: 'notification:new',
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_DELETE: 'notification:delete',
  
  // 视频事件
  VIDEO_UPLOAD_PROGRESS: 'video:upload_progress',
  VIDEO_PROCESSING: 'video:processing',
  VIDEO_READY: 'video:ready',
  VIDEO_ERROR: 'video:error',
  
  // 评论事件
  COMMENT_NEW: 'comment:new',
  COMMENT_UPDATE: 'comment:update',
  COMMENT_DELETE: 'comment:delete',
  COMMENT_LIKE: 'comment:like',
  
  // 系统事件
  SYSTEM_MAINTENANCE: 'system:maintenance',
  SYSTEM_ANNOUNCEMENT: 'system:announcement',
  SYSTEM_UPDATE: 'system:update'
} as const

// 请求头
export const REQUEST_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
  X_REQUESTED_WITH: 'X-Requested-With',
  X_CSRF_TOKEN: 'X-CSRF-Token',
  X_REQUEST_ID: 'X-Request-ID',
  X_TIMESTAMP: 'X-Timestamp',
  X_CLIENT_VERSION: 'X-Client-Version',
  X_DEVICE_ID: 'X-Device-ID',
  X_PLATFORM: 'X-Platform',
  X_LANGUAGE: 'X-Language',
  X_TIMEZONE: 'X-Timezone'
} as const

// 内容类型
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  FORM_URLENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
  HTML: 'text/html',
  XML: 'application/xml',
  BINARY: 'application/octet-stream'
} as const

// 缓存策略
export const CACHE_STRATEGIES = {
  NO_CACHE: 'no-cache',
  NO_STORE: 'no-store',
  MUST_REVALIDATE: 'must-revalidate',
  PUBLIC: 'public',
  PRIVATE: 'private',
  MAX_AGE: 'max-age',
  S_MAXAGE: 's-maxage',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  STALE_IF_ERROR: 'stale-if-error'
} as const

// 请求方法
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
  HEAD: 'HEAD',
  OPTIONS: 'OPTIONS'
} as const

// 错误类型
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  PARSE_ERROR: 'PARSE_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const

// 重试策略
export const RETRY_STRATEGIES = {
  NONE: 'none',
  FIXED: 'fixed',
  EXPONENTIAL: 'exponential',
  LINEAR: 'linear',
  CUSTOM: 'custom'
} as const

// API版本
export const API_VERSIONS = {
  V1: 'v1',
  V2: 'v2',
  BETA: 'beta',
  ALPHA: 'alpha'
} as const

// 环境类型
export const ENVIRONMENT_TYPES = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  TEST: 'test'
} as const

// 默认导出
export default {
  API_CONFIG,
  HTTP_STATUS,
  BUSINESS_CODE,
  API_ENDPOINTS,
  WS_EVENTS,
  REQUEST_HEADERS,
  CONTENT_TYPES,
  CACHE_STRATEGIES,
  HTTP_METHODS,
  ERROR_TYPES,
  RETRY_STRATEGIES,
  API_VERSIONS,
  ENVIRONMENT_TYPES
}