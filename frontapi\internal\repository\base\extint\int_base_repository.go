package extint

import (
	"context"
	"errors"
	"reflect"

	"frontapi/internal/models"

	"gorm.io/gorm"
)

// IntBaseRepository 针对int类型ID的通用仓库接口
// T 表示模型类型，必须继承自IntBaseModel
type IntBaseRepository[T models.IntBaseModelConstraint] interface {
	// 基础CRUD操作
	Create(ctx context.Context, entity *T) error
	FindByID(ctx context.Context, id int) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id int) error
	BatchDelete(ctx context.Context, ids []int) error

	// 列表查询
	List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error)
	FindAll(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error)

	// 条件查询
	FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error)
	FindOneByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) (*T, error)

	// 使用函数式条件查询
	FindOneByConditionFunc(ctx context.Context, conditionFunc func(*gorm.DB) *gorm.DB) (*T, error)
	FindByConditionFunc(ctx context.Context, conditionFunc func(*gorm.DB) *gorm.DB) ([]*T, error)
	ListWithConditionFunc(ctx context.Context, orderBy string, page, pageSize int, conditionFunc func(*gorm.DB) *gorm.DB) ([]*T, int64, error)

	// 计数操作
	Count(ctx context.Context, condition map[string]interface{}) (int64, error)
	CountByCondition(ctx context.Context, conditionFunc func(*gorm.DB) *gorm.DB) (int64, error)
	Exists(ctx context.Context, condition map[string]interface{}) (bool, error)

	// 批量操作
	BatchCreate(ctx context.Context, entities []*T) (int, error)
	BatchUpdate(ctx context.Context, entities []*T) error
	UpdateCountByIDs(ctx context.Context, ids []int, field string, count int64) error

	// 获取数据库实例
	GetDB() *gorm.DB
	GetDBWithContext(ctx context.Context) *gorm.DB
}

// intBaseRepository 针对int类型ID的通用仓库实现
type intBaseRepository[T models.IntBaseModelConstraint] struct {
	db *gorm.DB
}

// NewIntBaseRepository 创建针对int类型ID的通用仓库实例
func NewIntBaseRepository[T models.IntBaseModelConstraint](db *gorm.DB) IntBaseRepository[T] {
	return &intBaseRepository[T]{
		db: db,
	}
}

// Create 创建实体
func (r *intBaseRepository[T]) Create(ctx context.Context, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}

	return r.db.WithContext(ctx).Create(entity).Error
}

// FindByID 根据ID查找实体
func (r *intBaseRepository[T]) FindByID(ctx context.Context, id int) (*T, error) {
	var entity T
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &entity, nil
}

// Update 更新实体
func (r *intBaseRepository[T]) Update(ctx context.Context, entity *T) error {
	if entity == nil {
		return errors.New("entity cannot be nil")
	}

	// 使用反射获取ID值
	v := reflect.ValueOf(entity).Elem()
	idField := v.FieldByName("ID")
	if !idField.IsValid() || idField.Int() == 0 {
		return errors.New("entity ID is required for update")
	}

	return r.db.WithContext(ctx).Save(entity).Error
}

// Delete 根据ID删除实体
func (r *intBaseRepository[T]) Delete(ctx context.Context, id int) error {
	var entity T
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&entity).Error
}

// BatchDelete 批量删除
func (r *intBaseRepository[T]) BatchDelete(ctx context.Context, ids []int) error {
	if len(ids) == 0 {
		return nil
	}

	var entity T
	return r.db.WithContext(ctx).Where("id IN ?", ids).Delete(&entity).Error
}

// List 分页查询列表
func (r *intBaseRepository[T]) List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Find(&entities).Error
	return entities, total, err
}

// FindAll 查询所有记录
func (r *intBaseRepository[T]) FindAll(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error) {
	var entities []*T

	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	err := query.Find(&entities).Error
	return entities, err
}

// FindByCondition 根据条件查询
func (r *intBaseRepository[T]) FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error) {
	return r.FindAll(ctx, condition, orderBy)
}

// FindOneByCondition 根据条件查询单个记录
func (r *intBaseRepository[T]) FindOneByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) (*T, error) {
	var entity T

	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	err := query.First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &entity, nil
}

// FindOneByConditionFunc 使用函数式条件查询单个记录
func (r *intBaseRepository[T]) FindOneByConditionFunc(ctx context.Context, conditionFunc func(*gorm.DB) *gorm.DB) (*T, error) {
	var entity T

	query := r.db.WithContext(ctx).Model(new(T))
	if conditionFunc != nil {
		query = conditionFunc(query)
	}

	err := query.First(&entity).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &entity, nil
}

// FindByConditionFunc 使用函数式条件查询
func (r *intBaseRepository[T]) FindByConditionFunc(ctx context.Context, conditionFunc func(*gorm.DB) *gorm.DB) ([]*T, error) {
	var entities []*T

	query := r.db.WithContext(ctx).Model(new(T))
	if conditionFunc != nil {
		query = conditionFunc(query)
	}

	err := query.Find(&entities).Error
	return entities, err
}

// ListWithConditionFunc 使用函数式条件分页查询
func (r *intBaseRepository[T]) ListWithConditionFunc(ctx context.Context, orderBy string, page, pageSize int, conditionFunc func(*gorm.DB) *gorm.DB) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.db.WithContext(ctx).Model(new(T))
	if conditionFunc != nil {
		query = conditionFunc(query)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 应用排序
	if orderBy != "" {
		query = query.Order(orderBy)
	}

	// 应用分页
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	err := query.Find(&entities).Error
	return entities, total, err
}

// Count 计算记录数
func (r *intBaseRepository[T]) Count(ctx context.Context, condition map[string]interface{}) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(new(T))

	// 应用条件
	for key, value := range condition {
		query = query.Where(key, value)
	}

	err := query.Count(&count).Error
	return count, err
}

// CountByCondition 使用函数式条件计算记录数
func (r *intBaseRepository[T]) CountByCondition(ctx context.Context, conditionFunc func(*gorm.DB) *gorm.DB) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(new(T))
	if conditionFunc != nil {
		query = conditionFunc(query)
	}

	err := query.Count(&count).Error
	return count, err
}

// Exists 检查记录是否存在
func (r *intBaseRepository[T]) Exists(ctx context.Context, condition map[string]interface{}) (bool, error) {
	count, err := r.Count(ctx, condition)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// BatchCreate 批量创建
func (r *intBaseRepository[T]) BatchCreate(ctx context.Context, entities []*T) (int, error) {
	if len(entities) == 0 {
		return 0, nil
	}

	err := r.db.WithContext(ctx).Create(entities).Error
	if err != nil {
		return 0, err
	}
	return len(entities), nil
}

// BatchUpdate 批量更新
func (r *intBaseRepository[T]) BatchUpdate(ctx context.Context, entities []*T) error {
	if len(entities) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, entity := range entities {
			if err := tx.Save(entity).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// UpdateCountByIDs 根据ID列表更新计数字段
func (r *intBaseRepository[T]) UpdateCountByIDs(ctx context.Context, ids []int, field string, count int64) error {
	if len(ids) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Model(new(T)).Where("id IN ?", ids).Update(field, count).Error
}

// GetDB 获取数据库实例
func (r *intBaseRepository[T]) GetDB() *gorm.DB {
	return r.db
}

// GetDBWithContext 获取带上下文的数据库实例
func (r *intBaseRepository[T]) GetDBWithContext(ctx context.Context) *gorm.DB {
	return r.db.WithContext(ctx)
}
