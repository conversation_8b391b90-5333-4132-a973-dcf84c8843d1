package system

import (
	"context"
	model "frontapi/internal/models/system"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type CountryRepository interface {
	base.ExtendedRepository[model.Country]
	FindByCode(ctx context.Context, code string) (*model.Country, error)
}

type countryRepository struct {
	base.ExtendedRepository[model.Country]
}

func NewCountryRepository(db *gorm.DB) CountryRepository {
	return &countryRepository{
		ExtendedRepository: base.NewExtendedRepository[model.Country](db),
	}
}

func (r *countryRepository) FindByCode(ctx context.Context, code string) (*model.Country, error) {
	condition := map[string]interface{}{"code": code}
	return r.FindOneByCondition(ctx, condition, "")
}
