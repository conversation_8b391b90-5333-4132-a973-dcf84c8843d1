package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

var (
	// Redis客户端
	Client *redis.Client
	// 上下文
	Ctx = context.Background()
)

// Set 设置缓存，并设置过期时间
func Set(key string, value interface{}, expiration time.Duration) error {
	return Client.Set(Ctx, key, value, expiration).Err()
}

// Get 获取缓存
func Get(key string) (string, error) {
	return Client.Get(Ctx, key).Result()
}

// Del 删除缓存
func Del(key string) error {
	return Client.Del(Ctx, key).Err()
}

// Exists 检查键是否存在
func Exists(key string) (bool, error) {
	result, err := Client.Exists(Ctx, key).Result()
	return result > 0, err
}

// SetToken 设置用户Token
func SetToken(userID string, token string, expiration time.Duration) error {
	key := fmt.Sprintf("user:token:%s", userID)
	return Set(key, token, expiration)
}

// GetToken 获取用户Token
func GetToken(userID string) (string, error) {
	key := fmt.Sprintf("user:token:%s", userID)
	return Get(key)
}

// DeleteToken 删除用户Token
func DeleteToken(userID string) error {
	key := fmt.Sprintf("user:token:%s", userID)
	return Del(key)
}

// SetHotVideo 设置热门视频缓存
func SetHotVideo(videoID string, data interface{}, expiration time.Duration) error {
	key := fmt.Sprintf("hot:video:%s", videoID)
	return Set(key, data, expiration)
}

// GetHotVideo 获取热门视频缓存
func GetHotVideo(videoID string) (string, error) {
	key := fmt.Sprintf("hot:video:%s", videoID)
	return Get(key)
}

// SetHotVideos 设置热门视频列表缓存
func SetHotVideos(key string, data interface{}, expiration time.Duration) error {
	return Set(key, data, expiration)
}

// GetHotVideos 获取热门视频列表缓存
func GetHotVideos(key string) (string, error) {
	return Get(key)
}

// SetJSON 设置JSON格式的缓存数据
// @param key 缓存键
// @param value 要缓存的数据（将被序列化为JSON）
// @param expiration 过期时间
// @return error 错误信息
func SetJSON(key string, value interface{}, expiration time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %w", err)
	}
	return Client.Set(Ctx, key, jsonData, expiration).Err()
}

// GetJSON 获取JSON格式的缓存数据并反序列化到指定结构
// @param key 缓存键
// @param dest 目标结构体指针，用于接收反序列化后的数据
// @return error 错误信息，如果键不存在或反序列化失败
func GetJSON(key string, dest interface{}) error {
	data, err := Client.Get(Ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return fmt.Errorf("缓存键不存在: %s", key)
		}
		return fmt.Errorf("获取缓存失败: %w", err)
	}

	err = json.Unmarshal([]byte(data), dest)
	if err != nil {
		return fmt.Errorf("JSON反序列化失败: %w", err)
	}

	return nil
}

// SetJSONGeneric 设置JSON格式的缓存数据（泛型版本）
// @param key 缓存键
// @param value 要缓存的数据（将被序列化为JSON）
// @param expiration 过期时间
// @return error 错误信息
func SetJSONGeneric[T any](key string, value T, expiration time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %w", err)
	}
	return Client.Set(Ctx, key, string(jsonData), expiration).Err()
}

// GetJSONGeneric 获取JSON格式的缓存数据并反序列化到指定结构（泛型版本）
// @param key 缓存键
// @return T 反序列化后的数据
// @return error 错误信息，如果键不存在或反序列化失败
func GetJSONGeneric[T any](key string) (T, error) {
	var result T
	data, err := Client.Get(Ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return result, fmt.Errorf("缓存键不存在: %s", key)
		}
		return result, fmt.Errorf("获取缓存失败: %w", err)
	}

	err = json.Unmarshal([]byte(data), &result)
	if err != nil {
		return result, fmt.Errorf("JSON反序列化失败: %w", err)
	}

	return result, nil
}

// DelByPattern 根据模式删除缓存键
// @param pattern 匹配模式，支持通配符 * 和 ?
// @return error 错误信息
func DelByPattern(pattern string) error {
	keys, err := Client.Keys(Ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("获取匹配键失败: %w", err)
	}

	if len(keys) == 0 {
		return nil // 没有匹配的键
	}

	err = Client.Del(Ctx, keys...).Err()
	if err != nil {
		return fmt.Errorf("删除缓存键失败: %w", err)
	}

	return nil
}

// HSet 设置哈希表字段
// @param key 哈希表键
// @param field 字段名
// @param value 字段值
// @return error 错误信息
func HSet(key, field string, value interface{}) error {
	return Client.HSet(Ctx, key, field, value).Err()
}

// HGet 获取哈希表字段值
// @param key 哈希表键
// @param field 字段名
// @return string 字段值
// @return error 错误信息
func HGet(key, field string) (string, error) {
	return Client.HGet(Ctx, key, field).Result()
}

// HDel 删除哈希表字段
// @param key 哈希表键
// @param fields 字段名列表
// @return error 错误信息
func HDel(key string, fields ...string) error {
	return Client.HDel(Ctx, key, fields...).Err()
}

// Incr 递增计数器
// @param key 计数器键
// @return int64 递增后的值
// @return error 错误信息
func Incr(key string) (int64, error) {
	return Client.Incr(Ctx, key).Result()
}

// IncrBy 按指定值递增计数器
// @param key 计数器键
// @param value 递增值
// @return int64 递增后的值
// @return error 错误信息
func IncrBy(key string, value int64) (int64, error) {
	return Client.IncrBy(Ctx, key, value).Result()
}

// Expire 设置键的过期时间
// @param key 键名
// @param expiration 过期时间
// @return error 错误信息
func Expire(key string, expiration time.Duration) error {
	return Client.Expire(Ctx, key, expiration).Err()
}

// TTL 获取键的剩余生存时间
// @param key 键名
// @return time.Duration 剩余时间
// @return error 错误信息
func TTL(key string) (time.Duration, error) {
	return Client.TTL(Ctx, key).Result()
}
