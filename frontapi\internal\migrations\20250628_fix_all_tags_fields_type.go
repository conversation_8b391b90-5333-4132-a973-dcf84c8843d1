package migrations

import (
	"gorm.io/gorm"
)

// FixAllTagsFieldsType 修复所有表中tags相关字段的数据类型
func FixAllTagsFieldsType(db *gorm.DB) error {
	// 修复短视频表的tags字段
	err := db.Exec(`
		ALTER TABLE ly_shorts 
		MODIFY COLUMN tags JSON COMMENT '标签'
	`).Error
	if err != nil {
		return err
	}

	// 修复电子书表的tags字段
	err = db.Exec(`
		ALTER TABLE ly_books 
		MODIFY COLUMN tags JSON COMMENT '标签'
	`).Error
	if err != nil {
		return err
	}

	// 修复帖子表的images字段
	err = db.Exec(`
		ALTER TABLE ly_posts 
		MODIFY COLUMN images JSON COMMENT '帖子图片,与视频只能存在一个'
	`).Error
	if err != nil {
		return err
	}

	// 修复帖子评论表的images字段
	err = db.Exec(`
		ALTER TABLE ly_posts_comments 
		MODIFY COLUMN images JSON COMMENT '图片'
	`).Error
	if err != nil {
		return err
	}

	// 修复图片专辑表的tags_json字段
	err = db.Exec(`
		ALTER TABLE ly_picture_albums 
		MODIFY COLUMN tags_json JSON COMMENT '标签JSON数据'
	`).Error
	if err != nil {
		return err
	}

	// 修复视频专辑表的tags字段
	err = db.Exec(`
		ALTER TABLE ly_video_albums 
		MODIFY COLUMN tags JSON COMMENT '标签'
	`).Error
	if err != nil {
		return err
	}

	// 修复视频评论表的images字段
	err = db.Exec(`
		ALTER TABLE ly_video_comments 
		MODIFY COLUMN images JSON COMMENT '图片'
	`).Error
	if err != nil {
		return err
	}

	return nil
}

// RollbackFixAllTagsFieldsType 回滚所有表中tags相关字段的数据类型修改
func RollbackFixAllTagsFieldsType(db *gorm.DB) error {
	// 回滚短视频表
	err := db.Exec(`
		ALTER TABLE ly_shorts 
		MODIFY COLUMN tags VARCHAR(255) COMMENT '标签'
	`).Error
	if err != nil {
		return err
	}

	// 回滚电子书表
	err = db.Exec(`
		ALTER TABLE ly_books 
		MODIFY COLUMN tags VARCHAR(255) COMMENT '标签'
	`).Error
	if err != nil {
		return err
	}

	// 回滚帖子表
	err = db.Exec(`
		ALTER TABLE ly_posts 
		MODIFY COLUMN images TEXT COMMENT '帖子图片,与视频只能存在一个'
	`).Error
	if err != nil {
		return err
	}

	// 回滚帖子评论表
	err = db.Exec(`
		ALTER TABLE ly_posts_comments 
		MODIFY COLUMN images TEXT COMMENT '图片'
	`).Error
	if err != nil {
		return err
	}

	// 回滚图片专辑表
	err = db.Exec(`
		ALTER TABLE ly_picture_albums 
		MODIFY COLUMN tags_json LONGTEXT COMMENT '标签JSON数据'
	`).Error
	if err != nil {
		return err
	}

	// 回滚视频专辑表
	err = db.Exec(`
		ALTER TABLE ly_video_albums 
		MODIFY COLUMN tags VARCHAR(255) COMMENT '标签'
	`).Error
	if err != nil {
		return err
	}

	// 回滚视频评论表
	err = db.Exec(`
		ALTER TABLE ly_video_comments 
		MODIFY COLUMN images TEXT COMMENT '图片'
	`).Error
	if err != nil {
		return err
	}

	return nil
}
