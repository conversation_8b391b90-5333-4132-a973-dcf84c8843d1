package utils

import (
	"encoding/json"
	"reflect"
	"strconv"
)

// DataNode 数据节点，用于链式调用
type DataNode struct {
	value interface{}
}

// GetString 获取字符串值
func (d *DataNode) GetString() string {
	if d == nil || d.value == nil {
		return ""
	}
	switch v := d.value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		str, _ := json.Marshal(v)
		return string(str)
	}
}

// GetInt 获取整数值
func (d *DataNode) GetInt() int {
	if d == nil || d.value == nil {
		return 0
	}
	switch v := d.value.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		i, _ := strconv.Atoi(v)
		return i
	case bool:
		if v {
			return 1
		}
		return 0
	default:
		return 0
	}
}

// GetFloat 获取浮点数值
func (d *DataNode) GetFloat() float64 {
	if d == nil || d.value == nil {
		return 0
	}
	switch v := d.value.(type) {
	case float64:
		return v
	case int:
		return float64(v)
	case string:
		f, _ := strconv.ParseFloat(v, 64)
		return f
	case bool:
		if v {
			return 1.0
		}
		return 0.0
	default:
		return 0
	}
}

// GetBool 获取布尔值
func (d *DataNode) GetBool() bool {
	if d == nil || d.value == nil {
		return false
	}
	switch v := d.value.(type) {
	case bool:
		return v
	case string:
		return v == "true" || v == "1" || v == "yes"
	case int:
		return v > 0
	case float64:
		return v > 0
	default:
		return false
	}
}

// Get 获取子节点
func (d *DataNode) Get(key string) *DataNode {
	if d == nil || d.value == nil {
		return &DataNode{value: nil}
	}

	// 如果是map，直接获取子项
	if m, ok := d.value.(map[string]interface{}); ok {
		if val, exists := m[key]; exists {
			return &DataNode{value: val}
		}
	}

	// 如果是结构体，尝试通过反射获取字段
	if reflect.TypeOf(d.value).Kind() == reflect.Struct {
		val := reflect.ValueOf(d.value).FieldByName(key)
		if val.IsValid() {
			return &DataNode{value: val.Interface()}
		}
	}

	return &DataNode{value: nil}
}

// IsEmpty 检查节点是否为空
func (d *DataNode) IsEmpty() bool {
	return d == nil || d.value == nil
}
