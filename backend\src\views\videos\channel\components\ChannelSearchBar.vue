<template>
  <div class="channel-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item label="频道名称">
        <el-input 
          v-model="searchForm.keyword" 
          placeholder="请输入频道名称或描述" 
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="状态">
        <el-select 
          v-model="searchForm.status" 
          placeholder="请选择状态" 
          clearable 
          style="width: 180px;"
        >
          <el-option label="正常" :value="1" />
          <el-option label="禁用" :value="0" />
          <el-option label="已删除" :value="-4" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 280px;"
          @change="handleDateRangeChange"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><RefreshRight /></el-icon>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import type { VideoChannelSearchForm } from '@/types/videos';
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive, ref, watch } from 'vue';

// Props
interface Props {
  modelValue?: VideoChannelSearchForm;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    keyword: '',
    status: undefined,
    creator_id: undefined,
    created_at_start: '',
    created_at_end: '',
  })
});

// Emits
interface Emits {
  search: [params: VideoChannelSearchForm];
  reset: [];
  refresh: [];
  'update:modelValue': [value: VideoChannelSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<VideoChannelSearchForm>({ ...props.modelValue });

// 时间区间选择器的值
const dateRange = ref<string[]>([]);

// 初始化时间区间
if (searchForm.created_at_start && searchForm.created_at_end) {
  dateRange.value = [searchForm.created_at_start, searchForm.created_at_end];
}

// 处理时间区间变化
const handleDateRangeChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    searchForm.created_at_start = dates[0];
    searchForm.created_at_end = dates[1];
  } else {
    searchForm.created_at_start = '';
    searchForm.created_at_end = '';
  }
};

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    creator_id: undefined,
    created_at_start: '',
    created_at_end: '',
  });
  dateRange.value = [];
  emit('reset');
};

// 刷新
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped lang="scss">
.channel-search-bar {
  margin-bottom: 16px;

  .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .el-form-item {
      margin-bottom: 8px;
    }
  }
}
</style> 