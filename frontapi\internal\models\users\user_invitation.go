package users

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// UserInvitation 用户邀请关系表
type UserInvitation struct {
	models.BaseModel
	InviterID         string         `gorm:"column:inviter_id;type:string;not null;comment:邀请人ID" json:"inviter_id"`                                                     //邀请人ID
	InvitedID         string         `gorm:"column:invited_id;type:string;not null;comment:被邀请人ID" json:"invited_id"`                                                    //被邀请人ID
	InviterUsername   string         `gorm:"column:inviter_username;type:string;comment:邀请人用户名" json:"inviter_username"`                                                 //邀请人用户名
	InviterNickname   string         `gorm:"column:inviter_nickname;type:string;comment:邀请人昵称" json:"inviter_nickname"`                                                  //邀请人昵称
	InviterAvatar     string         `gorm:"column:inviter_avatar;type:string;comment:邀请人头像" json:"inviter_avatar"`                                                      //邀请人头像
	InvitedTime       types.JSONTime `gorm:"column:invited_time;type:time;comment:被邀请时间" json:"invited_time"`                                                            //被邀请时间
	InvitedStatus     string         `gorm:"column:invited_status;type:string;comment:邀请状态，0邀请码已填写，1.邀请被后台审核通过" json:"invited_status"`                                   //邀请状态，0邀请码已填写，1.邀请被后台审核通过
	InviteCode        string         `gorm:"column:invite_code;type:string;size:20;comment:邀请码" json:"invite_code"`                                                      //邀请码
	InvitationMethod  string         `gorm:"column:invitation_method;type:string;size:20;comment:邀请方式:link-链接,code-邀请码,qrcode-二维码,social-社交分享" json:"invitation_method"` //邀请方式:link-链接,code-邀请码,qrcode-二维码,social-社交分享
	InvitationChannel string         `gorm:"column:invitation_channel;type:string;size:50;comment:邀请渠道" json:"invitation_channel"`                                       //邀请渠道
	RewardStatus      bool           `gorm:"column:reward_status;type:bool;default:false;comment:奖励状态：false-未奖励，true-已奖励" json:"reward_status"`                          //奖励状态：0-未奖励，1-已奖励
	RewardTime        types.JSONTime `gorm:"column:reward_time;type:time;comment:奖励时间" json:"reward_time"`                                                               //奖励时间
	PointsRewarded    int            `gorm:"column:points_rewarded;type:int;default:0;comment:已奖励积分" json:"points_rewarded"`                                             //已奖励积分
	CoinsRewarded     int            `gorm:"column:coins_rewarded;type:int;default:0;comment:已奖励平台币" json:"coins_rewarded"`                                              //已奖励平台币
	CashbackRewarded  float64        `gorm:"column:cashback_rewarded;type:float;default:0.00;comment:已奖励现金返现" json:"cashback_rewarded"`                                  //已奖励现金返现
	ExpiryDate        types.JSONTime `gorm:"column:expiry_date;type:time;comment:邀请关系过期时间" json:"expiry_date"`                                                           //邀请关系过期时间
	IsExpired         bool           `gorm:"column:is_expired;type:bool;default:false;comment:是否已过期" json:"is_expired"`                                                  //是否已过期
}

// TableName 表名
func (UserInvitation) TableName() string {
	return "ly_user_invitations"
}
