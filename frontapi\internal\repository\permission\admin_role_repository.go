package permission

import (
	"context"

	"gorm.io/gorm"

	permissionModels "frontapi/internal/models/permission"
	baseRepo "frontapi/internal/repository/base/extint"
)

// AdminRoleRepository 管理员角色数据访问接口
type AdminRoleRepository interface {
	baseRepo.IntBaseRepository[permissionModels.AdminRole] // 继承基础仓库接口

	// 特有的业务逻辑方法
	GetByCode(ctx context.Context, code string) (*permissionModels.AdminRole, error)
	Search(ctx context.Context, keyword string, page, pageSize int) ([]*permissionModels.AdminRole, int64, error)
	GetAll(ctx context.Context) ([]*permissionModels.AdminRole, error)
	GetActive(ctx context.Context) ([]*permissionModels.AdminRole, error)
	GetByLevel(ctx context.Context, level int) ([]*permissionModels.AdminRole, error)

	// 验证操作
	ExistsCode(ctx context.Context, code string, excludeID ...int) (bool, error)
	ExistsName(ctx context.Context, name string, excludeID ...int) (bool, error)
}

// adminRoleRepository 管理员角色数据访问实现
type adminRoleRepository struct {
	baseRepo.IntBaseRepository[permissionModels.AdminRole] // 嵌入基础仓库
	db                                                     *gorm.DB
}

// NewAdminRoleRepository 创建管理员角色仓库实例
func NewAdminRoleRepository(db *gorm.DB) AdminRoleRepository {
	baseRepository := baseRepo.NewIntBaseRepository[permissionModels.AdminRole](db)
	return &adminRoleRepository{
		IntBaseRepository: baseRepository,
		db:                db,
	}
}

// GetByCode 根据角色编码获取管理员角色
func (r *adminRoleRepository) GetByCode(ctx context.Context, code string) (*permissionModels.AdminRole, error) {
	condition := map[string]interface{}{
		"code": code,
	}
	return r.IntBaseRepository.FindOneByCondition(ctx, condition, "")
}

// Search 搜索管理员角色
func (r *adminRoleRepository) Search(ctx context.Context, keyword string, page, pageSize int) ([]*permissionModels.AdminRole, int64, error) {
	// 使用函数式条件查询进行搜索
	conditionFunc := func(db *gorm.DB) *gorm.DB {
		if keyword != "" {
			searchCondition := "code LIKE ? OR name LIKE ? OR description LIKE ?"
			searchKeyword := "%" + keyword + "%"
			return db.Where(searchCondition, searchKeyword, searchKeyword, searchKeyword)
		}
		return db
	}

	return r.IntBaseRepository.ListWithConditionFunc(ctx, "level ASC, created_at DESC", page, pageSize, conditionFunc)
}

// GetAll 获取所有角色
func (r *adminRoleRepository) GetAll(ctx context.Context) ([]*permissionModels.AdminRole, error) {
	return r.IntBaseRepository.FindAll(ctx, map[string]interface{}{}, "level ASC, created_at DESC")
}

// GetActive 获取所有活跃角色
func (r *adminRoleRepository) GetActive(ctx context.Context) ([]*permissionModels.AdminRole, error) {
	condition := map[string]interface{}{
		"status": 1,
	}
	return r.IntBaseRepository.FindAll(ctx, condition, "level ASC, created_at DESC")
}

// GetByLevel 根据等级获取角色
func (r *adminRoleRepository) GetByLevel(ctx context.Context, level int) ([]*permissionModels.AdminRole, error) {
	condition := map[string]interface{}{
		"level": level,
	}
	return r.IntBaseRepository.FindByCondition(ctx, condition, "created_at DESC")
}

// ExistsCode 检查角色编码是否存在
func (r *adminRoleRepository) ExistsCode(ctx context.Context, code string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"code": code,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	return r.IntBaseRepository.Exists(ctx, condition)
}

// ExistsName 检查角色名称是否存在
func (r *adminRoleRepository) ExistsName(ctx context.Context, name string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"name": name,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	return r.IntBaseRepository.Exists(ctx, condition)
}
