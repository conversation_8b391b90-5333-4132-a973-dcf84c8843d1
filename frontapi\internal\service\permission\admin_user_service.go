package permission

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"time"

	"frontapi/internal/models/permission"
	permRepo "frontapi/internal/repository/permission"
	base "frontapi/internal/service/base/extint"
	permValidator "frontapi/internal/validation/permission"
	"frontapi/pkg/types"
)

// UserProfile 用户资料
type UserProfile struct {
	*permission.AdminUser
	LastLoginTime string   `json:"last_login_time"`
	RoleNames     []string `json:"role_names"`
	Permissions   []string `json:"permissions"`
}

// AdminUserService 管理员用户服务接口
type AdminUserService interface {
	base.IIntBaseService[permission.AdminUser] // 继承基础服务接口

	// 认证相关
	Login(ctx context.Context, username, password string) (*permission.AdminUser, error)
	VerifyPassword(ctx context.Context, user *permission.AdminUser, password string) bool
	ChangePassword(ctx context.Context, userID int, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, userID int, newPassword string) error

	// 用户管理
	CreateUser(ctx context.Context, req *permValidator.CreateUserRequest) (int, error)
	UpdateUser(ctx context.Context, userID int, req *permValidator.UpdateUserRequest) error
	GetUserByUsername(ctx context.Context, username string) (*permission.AdminUser, error)
	GetUserProfile(ctx context.Context, userID int) (*UserProfile, error)
	SearchUsers(ctx context.Context, keyword string, page, pageSize int) ([]*permission.AdminUser, int64, error)

	// 状态管理
	EnableUser(ctx context.Context, userID int) error
	DisableUser(ctx context.Context, userID int) error
	UpdateLoginInfo(ctx context.Context, userID int, ip string) error

	// 验证方法
	ValidateUserData(ctx context.Context, req *permValidator.CreateUserRequest, excludeID ...int) error
}

// adminUserService 管理员用户服务实现
type adminUserService struct {
	base.IIntBaseService[permission.AdminUser] // 嵌入基础服务
	userRepo                                   permRepo.AdminUserRepository
}

// NewAdminUserService 创建管理员用户服务实例
func NewAdminUserService(userRepo permRepo.AdminUserRepository) AdminUserService {
	baseService := base.NewIntBaseService[permission.AdminUser](userRepo, "admin_user")
	return &adminUserService{
		IIntBaseService: baseService,
		userRepo:        userRepo,
	}
}

// Login 用户登录
func (s *adminUserService) Login(ctx context.Context, username, password string) (*permission.AdminUser, error) {
	if username == "" || password == "" {
		return nil, errors.New("用户名和密码不能为空")
	}

	// 查找用户
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, errors.New("用户已被禁用")
	}

	// 验证密码
	if !s.VerifyPassword(ctx, user, password) {
		return nil, errors.New("密码错误")
	}

	return user, nil
}

// VerifyPassword 验证密码
func (s *adminUserService) VerifyPassword(ctx context.Context, user *permission.AdminUser, password string) bool {
	// 使用MD5+盐值验证密码
	hashedPassword := s.hashPassword(password, user.Psalt)
	fmt.Print("密码:", hashedPassword)
	return hashedPassword == user.Password
}

// ChangePassword 修改密码
func (s *adminUserService) ChangePassword(ctx context.Context, userID int, oldPassword, newPassword string) error {
	// 获取用户信息
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if !s.VerifyPassword(ctx, user, oldPassword) {
		return errors.New("原密码错误")
	}

	// 生成新密码
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(newPassword, salt)

	// 更新密码
	return s.userRepo.UpdatePassword(ctx, userID, hashedPassword, salt)
}

// ResetPassword 重置密码
func (s *adminUserService) ResetPassword(ctx context.Context, userID int, newPassword string) error {
	// 生成新密码
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(newPassword, salt)

	// 更新密码
	return s.userRepo.UpdatePassword(ctx, userID, hashedPassword, salt)
}

// CreateUser 创建用户
func (s *adminUserService) CreateUser(ctx context.Context, req *permValidator.CreateUserRequest) (int, error) {
	// 验证数据
	if err := s.ValidateUserData(ctx, req); err != nil {
		return 0, err
	}

	// 生成密码盐和哈希密码
	salt := s.generateSalt()
	hashedPassword := s.hashPassword(req.Password, salt)

	// 创建用户实例
	user := &permission.AdminUser{
		Username:     req.Username,
		Password:     hashedPassword,
		Nickname:     req.Nickname,
		Email:        req.Email,
		Phone:        req.Phone,
		Remark:       req.Remark,
		Psalt:        salt,
		DeptID:       req.DeptID,
		IsSuperAdmin: boolToInt8(req.IsSuperAdmin),
	}

	// 使用基础服务创建
	id, err := s.IIntBaseService.Create(ctx, user)
	if err != nil {
		return 0, err
	}

	return id, nil
}

// UpdateUser 更新用户
func (s *adminUserService) UpdateUser(ctx context.Context, userID int, req *permValidator.UpdateUserRequest) error {
	// 获取现有用户
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 更新字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Remark != "" {
		user.Remark = req.Remark
	}
	if req.DeptID > 0 {
		user.DeptID = req.DeptID
	}
	if req.Status >= 0 {
		user.Status = req.Status
	}
	user.IsSuperAdmin = boolToInt8(req.IsSuperAdmin)
	user.UpdatedAt = types.JSONTime(time.Now())

	// 使用基础服务更新
	return s.IIntBaseService.Update(ctx, user)
}

// GetUserByUsername 根据用户名获取用户
func (s *adminUserService) GetUserByUsername(ctx context.Context, username string) (*permission.AdminUser, error) {
	return s.userRepo.GetByUsername(ctx, username)
}

// GetUserProfile 获取用户资料
func (s *adminUserService) GetUserProfile(ctx context.Context, userID int) (*UserProfile, error) {
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("用户不存在")
	}

	profile := &UserProfile{
		AdminUser: user,
	}

	// 格式化最后登录时间
	if user.LastLoginAt != nil {
		profile.LastLoginTime = time.Time(*user.LastLoginAt).Format("2006-01-02 15:04:05")
	}

	// TODO: 这里可以添加角色和权限信息
	// profile.RoleNames = ...
	// profile.Permissions = ...

	return profile, nil
}

// SearchUsers 搜索用户
func (s *adminUserService) SearchUsers(ctx context.Context, keyword string, page, pageSize int) ([]*permission.AdminUser, int64, error) {
	return s.userRepo.SearchUsers(ctx, keyword, page, pageSize)
}

// EnableUser 启用用户
func (s *adminUserService) EnableUser(ctx context.Context, userID int) error {
	// 使用基础服务的UpdateStatus方法
	return s.IIntBaseService.UpdateStatus(ctx, userID, 1)
}

// DisableUser 禁用用户
func (s *adminUserService) DisableUser(ctx context.Context, userID int) error {
	// 使用基础服务的UpdateStatus方法
	return s.IIntBaseService.UpdateStatus(ctx, userID, 0)
}

// UpdateLoginInfo 更新登录信息
func (s *adminUserService) UpdateLoginInfo(ctx context.Context, userID int, ip string) error {
	return s.userRepo.UpdateLastLogin(ctx, userID, ip)
}

// ValidateUserData 验证用户数据
func (s *adminUserService) ValidateUserData(ctx context.Context, req *permValidator.CreateUserRequest, excludeID ...int) error {
	// 检查用户名是否存在
	exists, err := s.userRepo.ExistsUsername(ctx, req.Username)
	if err != nil {
		return err
	}
	if exists {
		return errors.New("用户名已存在")
	}

	// 检查邮箱是否存在
	if req.Email != "" {
		exists, err := s.userRepo.ExistsEmail(ctx, req.Email)
		if err != nil {
			return err
		}
		if exists {
			return errors.New("邮箱已存在")
		}
	}

	// 检查手机号是否存在
	if req.Phone != "" {
		exists, err := s.userRepo.ExistsPhone(ctx, req.Phone)
		if err != nil {
			return err
		}
		if exists {
			return errors.New("手机号已存在")
		}
	}

	return nil
}

// 工具方法

// hashPassword 生成密码哈希
func (s *adminUserService) hashPassword(password, salt string) string {
	data := []byte(password + salt)
	hash := md5.Sum(data)
	return fmt.Sprintf("%x", hash)
}

// generateSalt 生成密码盐
func (s *adminUserService) generateSalt() string {
	data := []byte(fmt.Sprintf("%d", time.Now().UnixNano()))
	hash := md5.Sum(data)
	return fmt.Sprintf("%x", hash)[:8]
}

// boolToInt8 将布尔值转换为int8
func boolToInt8(b bool) int8 {
	if b {
		return 1
	}
	return 0
}
