import { post, corePost } from '@/shared/composables'
import type { BaseRequest, BaseResponse } from '../../utils/http/baseRequest'
import type { VideoItem } from '../../types/videoItem'
import type { CategoryQuery, CategoryListResponse } from '@/types/category'


// 获取顶部导航分类列表
export function getNavigationCategories(params: any) {
  return corePost('/home/<USER>', params)
}

// 获取分类页面详细列表
export function getCategoryList(params: BaseRequest<CategoryQuery>) {
  return corePost('/video/categories/getCategoryList', params)
}

// 视频查询参数接口
export interface VideoParams {
  categoryCode?: string
  sortBy?: string
  uploadTime?: string
  duration?: string
  quality?: string
}

// 视频响应接口
export interface VideoResponse {
  list: Array<{
    id: number
    title: string
    cover: string
    resolution: string
    views: number
    likes: number
    duration: string
    uploadTime: string
  }>
  total: number
  page: number
  pageSize: number
}

// 获取分类下的视频列表
export const getCategoryVideos = (params: BaseRequest<VideoParams>) => {
  return corePost<BaseResponse<VideoResponse>>('/video/categories/videos', params)
}
