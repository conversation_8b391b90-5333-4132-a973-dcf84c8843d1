package books

import (
	"frontapi/internal/admin"
	bookService "frontapi/internal/service/books"
	bookValidator "frontapi/internal/validation/books"

	"github.com/gofiber/fiber/v2"
)

// BookmarkController 电子书书签控制器
type BookmarkController struct {
	admin.BaseController
	bookmarkService bookService.BookmarkService
}

// NewBookmarkController 创建电子书书签控制器
func NewBookmarkController(bookmarkService bookService.BookmarkService) *BookmarkController {
	return &BookmarkController{
		bookmarkService: bookmarkService,
	}
}

// CreateBookmark 创建书签
func (h *BookmarkController) CreateBookmark(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 获取请求参数
	bookID := reqInfo.Get("bookId").GetString()
	chapterID := reqInfo.Get("chapterId").GetString()
	position := reqInfo.Get("position").GetInt()
	content := reqInfo.Get("content").GetString()
	bookCover := reqInfo.Get("bookCover").GetString()
	author := reqInfo.Get("author").GetString()

	// 验证必填参数
	if bookID == "" {
		return h.BadRequest(c, "电子书ID不能为空", nil)
	}
	if chapterID == "" {
		return h.BadRequest(c, "章节ID不能为空", nil)
	}

	// 创建书签请求
	createRequest := &bookValidator.CreateBookmarkRequest{
		UserID:    userID,
		BookID:    bookID,
		ChapterID: chapterID,
		Position:  position,
		Content:   content,
		BookCover: bookCover,
		Author:    author,
	}

	// 创建书签
	bookmarkID, err := h.bookmarkService.CreateBookmark(c.Context(), createRequest)
	if err != nil {
		return h.InternalServerError(c, "创建书签失败: "+err.Error())
	}

	return h.Success(c, fiber.Map{"bookmarkId": bookmarkID})
}

// UpdateBookmark 更新书签
func (h *BookmarkController) UpdateBookmark(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 获取请求参数
	bookmarkID := reqInfo.Get("bookmarkId").GetString()
	position := reqInfo.Get("position").GetInt()
	content := reqInfo.Get("content").GetString()

	// 验证必填参数
	if bookmarkID == "" {
		return h.BadRequest(c, "书签ID不能为空", nil)
	}

	// 检查书签是否属于当前用户
	bookmark, err := h.bookmarkService.GetBookmark(c.Context(), bookmarkID)
	if err != nil {
		return h.InternalServerError(c, "获取书签详情失败: "+err.Error())
	}

	if bookmark == nil {
		return h.NotFound(c, "书签不存在")
	}

	if bookmark.UserID != userID {
		return h.Forbidden(c, "无权操作该书签")
	}

	// 更新书签请求
	updateRequest := &bookValidator.UpdateBookmarkRequest{
		Position: position,
		Content:  content,
	}

	// 更新书签
	err = h.bookmarkService.UpdateBookmark(c.Context(), bookmarkID, updateRequest)
	if err != nil {
		return h.InternalServerError(c, "更新书签失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新书签成功")
}

// DeleteBookmark 删除书签
func (h *BookmarkController) DeleteBookmark(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 获取书签ID参数
	bookmarkID := reqInfo.Get("bookmarkId").GetString()

	// 如果请求体中没有，检查路径参数
	if bookmarkID == "" {
		bookmarkID = c.Params("bookmarkId")
		if bookmarkID == "" {
			return h.BadRequest(c, "书签ID不能为空", nil)
		}
	}

	// 检查书签是否属于当前用户
	bookmark, err := h.bookmarkService.GetBookmark(c.Context(), bookmarkID)
	if err != nil {
		return h.InternalServerError(c, "获取书签详情失败: "+err.Error())
	}

	if bookmark == nil {
		return h.NotFound(c, "书签不存在")
	}

	if bookmark.UserID != userID {
		return h.Forbidden(c, "无权操作该书签")
	}

	// 删除书签
	err = h.bookmarkService.DeleteBookmark(c.Context(), bookmarkID)
	if err != nil {
		return h.InternalServerError(c, "删除书签失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "删除书签成功")
}

// ListBookmarks 获取用户书签列表
func (h *BookmarkController) ListBookmarks(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 获取书签列表
	bookmarks, total, err := h.bookmarkService.ListUserBookmarks(c.Context(), userID, page, pageSize)
	if err != nil {
		return h.InternalServerError(c, "获取书签列表失败: "+err.Error())
	}

	return h.SuccessList(c, bookmarks, int64(total), page, pageSize)
}

// ListBookBookmarks 获取特定电子书的书签列表
func (h *BookmarkController) ListBookBookmarks(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 获取电子书ID参数
	bookID := reqInfo.Get("bookId").GetString()

	// 如果请求体中没有，检查路径参数
	if bookID == "" {
		bookID = c.Params("bookId")
		if bookID == "" {
			return h.BadRequest(c, "电子书ID不能为空", nil)
		}
	}

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 获取书签列表
	bookmarks, total, err := h.bookmarkService.ListBookBookmarks(c.Context(), userID, bookID, page, pageSize)
	if err != nil {
		return h.InternalServerError(c, "获取书签列表失败: "+err.Error())
	}

	return h.SuccessList(c, bookmarks, int64(total), page, pageSize)
}

// GetBookmark 获取书签详情
func (h *BookmarkController) GetBookmark(c *fiber.Ctx) error {
	// 使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 获取书签ID参数
	bookmarkID := reqInfo.Get("bookmarkId").GetString()

	// 如果请求体中没有，检查路径参数
	if bookmarkID == "" {
		bookmarkID = c.Params("bookmarkId")
		if bookmarkID == "" {
			return h.BadRequest(c, "书签ID不能为空", nil)
		}
	}

	// 获取书签详情
	bookmark, err := h.bookmarkService.GetBookmark(c.Context(), bookmarkID)
	if err != nil {
		return h.InternalServerError(c, "获取书签详情失败: "+err.Error())
	}

	if bookmark == nil {
		return h.NotFound(c, "书签不存在")
	}

	// 检查书签是否属于当前用户
	if bookmark.UserID != userID {
		return h.Forbidden(c, "无权查看该书签")
	}

	return h.Success(c, bookmark)
}
