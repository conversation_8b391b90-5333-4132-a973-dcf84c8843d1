/**
 * 用户相关类型定义
 */

import type { ID } from '@/shared/types/base'

/**
 * 用户基本信息
 */
export interface User {
    id: ID
    username: string
    email: string
    displayName?: string
    avatar?: string
    phone?: string
    status: UserStatus
    verified: boolean
    createdAt: string
    updatedAt: string
    [key: string]: any
}

/**
 * 用户状态
 */
export type UserStatus = 'active' | 'inactive' | 'banned' | 'pending'

/**
 * 用户角色
 */
export type UserRole = 'user' | 'admin' | 'moderator' | 'creator' | 'vip'

/**
 * 用户资料
 */
export interface UserProfile {
    userId: ID
    nickname?: string
    firstName?: string
    lastName?: string
    gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
    birthday?: string
    bio?: string
    website?: string
    location?: string
    company?: string
    position?: string
    education?: string
    socialLinks?: {
        twitter?: string
        facebook?: string
        instagram?: string
        linkedin?: string
        github?: string
        [key: string]: string | undefined
    }
    [key: string]: any
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
    language: string
    theme: string
    timezone: string
    dateFormat: string
    timeFormat: string
    currency: string
    autoPlay: boolean
    quality: string
    volume: number
    playbackSpeed: number
    subtitles: boolean
    notifications: boolean
    [key: string]: any
}

/**
 * 用户统计信息
 */
export interface UserStats {
    followersCount: number
    followingCount: number
    postsCount: number
    videosCount: number
    likesCount: number
    commentsCount: number
    viewsCount: number
    experience: number
    level: number
    reputation: number
    [key: string]: any
}

/**
 * 用户认证信息
 */
export interface UserAuth {
    token: string
    refreshToken: string
    expiresAt: number
    tokenType: string
}

/**
 * 登录请求参数
 */
export interface LoginParams {
    username?: string
    email?: string
    password: string
    remember?: boolean
}

/**
 * 注册请求参数
 */
export interface RegisterParams {
    username: string
    email: string
    password: string
    confirmPassword: string
    agreeTerms: boolean
}

/**
 * 找回密码请求参数
 */
export interface ForgotPasswordParams {
    email: string
}

/**
 * 重置密码请求参数
 */
export interface ResetPasswordParams {
    token: string
    password: string
    confirmPassword: string
}

/**
 * 修改密码请求参数
 */
export interface ChangePasswordParams {
    oldPassword: string
    newPassword: string
    confirmPassword: string
}

/**
 * 用户关系类型
 */
export type UserRelationType = 'following' | 'follower' | 'friend' | 'blocked' 