# Hooks 系统实现文档

## 概述

本文档描述了为 frontapi 项目实现的分层 Hooks 系统。该系统允许在 Controller、Service、Repository 各层级执行自定义逻辑，提供了数据验证、重复检查、数据清洗、审计记录等常用功能。

## 系统架构

### 核心组件

```
internal/hooks/
├── manager.go              # 核心钩子管理器
├── common_hooks.go         # 内置常用钩子实现
├── service_hooks.go        # 服务层钩子管理器
├── integration.go          # 与现有系统的集成工具
├── examples.go             # 使用示例
├── book_service_example.go # BookService 集成示例
├── hooks_test.go           # 测试文件
└── README.md               # 详细使用文档
```

### 设计原则

1. **分层管理**: 支持不同层级的钩子管理
2. **类型安全**: 基于 Go 泛型的类型安全设计
3. **优先级控制**: 支持钩子执行优先级设置
4. **错误处理**: Before 钩子失败阻止操作，After 钩子失败不影响主流程
5. **可扩展性**: 支持自定义钩子和内置钩子
6. **性能优化**: 使用读写锁保证并发安全

## 核心功能

### 1. 钩子类型

#### Service 层钩子
- `BeforeCreate`: 创建前执行
- `AfterCreate`: 创建后执行
- `BeforeUpdate`: 更新前执行
- `AfterUpdate`: 更新后执行
- `BeforeDelete`: 删除前执行
- `AfterDelete`: 删除后执行
- `BeforeFind`: 查询前执行
- `AfterFind`: 查询后执行

#### Repository 层钩子
- `BeforeDBCreate`: 数据库创建前
- `AfterDBCreate`: 数据库创建后
- `BeforeDBUpdate`: 数据库更新前
- `AfterDBUpdate`: 数据库更新后
- `BeforeDBDelete`: 数据库删除前
- `AfterDBDelete`: 数据库删除后

#### Controller 层钩子
- `BeforeRequest`: 请求处理前
- `AfterRequest`: 请求处理后
- `BeforeResponse`: 响应发送前
- `AfterResponse`: 响应发送后

### 2. 内置钩子

#### 重复检查钩子 (DuplicateCheckHook)
```go
// 检查指定字段是否在数据库中重复
hookManager.RegisterDuplicateCheck(
    "categories",           // 表名
    []string{"Name"},       // 检查字段
    "分类名称已存在",        // 错误消息
)
```

#### 数据清洗钩子 (DataCleaningHook)
```go
// 清洗和格式化数据
hookManager.RegisterDataCleaning(
    []string{"Name", "Description"}, // 去除空格的字段
    []string{"Email"},               // 转换为小写的字段
    []string{"Code"},                // 转换为大写的字段
    map[string]interface{}{
        "Status": 1,                   // 默认值
    },
)
```

#### 数据验证钩子 (ValidationHook)
```go
// 验证数据格式和内容
hookManager.RegisterValidation(map[string][]ValidationRule{
    "Name": {
        {Type: "required", Message: "名称不能为空"},
        {Type: "min_length", Value: 2, Message: "名称至少2个字符"},
        {Type: "max_length", Value: 50, Message: "名称不能超过50个字符"},
        {Type: "regex", Value: `^[a-zA-Z0-9_]+$`, Message: "只能包含字母数字下划线"},
    },
})
```

#### 审计钩子 (AuditHook)
```go
// 记录操作审计日志
hookManager.RegisterAudit(
    "categories",  // 表名
    "user123",     // 用户ID
)
```

#### 时间戳钩子 (TimestampHook)
```go
// 自动设置创建时间和更新时间
hookManager.RegisterTimestamp(
    "CreatedAt",  // 创建时间字段
    "UpdatedAt",  // 更新时间字段
)
```

### 3. 构建器模式

提供链式调用的钩子配置方式：

```go
NewCreateHookBuilder(hookManager).
    WithDataCleaning(
        []string{"Name", "Description"}, // 去除空格
        []string{"Email"},               // 转小写
        []string{"Code"},                // 转大写
        map[string]interface{}{"Status": 1}, // 默认值
    ).
    WithValidation(map[string][]ValidationRule{
        "Name": {
            {Type: "required", Message: "名称不能为空"},
        },
    }).
    WithDuplicateCheck("categories", []string{"Name"}, "名称已存在").
    WithTimestamp("CreatedAt", "UpdatedAt").
    WithAudit("categories", "system").
    WithCustomHook(
        BeforeCreate,
        "custom_hook",
        "自定义钩子",
        20,
        func(ctx context.Context, data interface{}) error {
            // 自定义逻辑
            return nil
        },
    ).
    Build()
```

## 使用方法

### 1. 基本使用

#### 创建服务并设置钩子

```go
type CategoryService struct {
    db          *gorm.DB
    hookManager *hooks.ServiceHookManager
}

func NewCategoryService(db *gorm.DB) *CategoryService {
    service := &CategoryService{
        db:          db,
        hookManager: hooks.NewServiceHookManager(db, "category"),
    }
    service.setupHooks()
    return service
}

func (s *CategoryService) setupHooks() {
    // 使用预定义配置
    integrator := hooks.NewServiceIntegrator(s.db)
    s.hookManager = integrator.SetupCommonHooks("category", hooks.CategoryHooksConfig())
}
```

#### 在业务方法中执行钩子

```go
func (s *CategoryService) Create(ctx context.Context, category *Category) error {
    // 执行创建前钩子
    if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, category); err != nil {
        return fmt.Errorf("创建前钩子执行失败: %w", err)
    }

    // 执行数据库创建
    if err := s.db.WithContext(ctx).Create(category).Error; err != nil {
        return fmt.Errorf("创建分类失败: %w", err)
    }

    // 执行创建后钩子
    if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterCreate, category); err != nil {
        // 创建后钩子失败不影响主操作，只记录日志
        fmt.Printf("创建后钩子执行失败: %v\n", err)
    }

    return nil
}
```

### 2. 集成到现有 BaseService

#### 方法一：使用混入 (Mixin)

```go
type EnhancedBaseService[T any] struct {
    *base.BaseService[T]           // 原有的BaseService
    hooks.BaseServiceHooksMixin    // 钩子混入
}

func NewEnhancedBaseService[T any](repo Repository[T], db *gorm.DB, entityType string) *EnhancedBaseService[T] {
    service := &EnhancedBaseService[T]{
        BaseService: base.NewBaseService[T](repo),
    }
    
    // 初始化钩子
    hookManager := hooks.NewServiceHookManager(db, entityType)
    service.InitHooksMixin(hookManager)
    
    return service
}

func (s *EnhancedBaseService[T]) Create(ctx context.Context, entity *T) (string, error) {
    // 执行创建前钩子
    if err := s.ExecuteBeforeCreate(ctx, entity); err != nil {
        return "", err
    }

    // 调用原有创建逻辑
    id, err := s.BaseService.Create(ctx, entity)
    if err != nil {
        return "", err
    }

    // 执行创建后钩子
    s.ExecuteAfterCreate(ctx, entity)

    return id, nil
}
```

#### 方法二：直接修改现有 BaseService

```go
// 在现有的 BaseService 中添加钩子管理器
type BaseService[T models.BaseModelConstraint] struct {
    repo        baseRepo.BaseRepository[T]
    cacheTTL    time.Duration
    hookManager *HookManager[T]           // 原有的钩子管理器
    newHookManager *hooks.ServiceHookManager // 新的钩子管理器
}

// 修改 Create 方法
func (s *BaseService[T]) Create(ctx context.Context, entity *T) (string, error) {
    // 执行新的钩子系统
    if s.newHookManager != nil {
        if err := s.newHookManager.ExecuteHooks(ctx, hooks.BeforeCreate, entity); err != nil {
            return "", fmt.Errorf("创建前钩子执行失败: %w", err)
        }
    }

    // 执行原有的钩子（保持兼容性）
    if err := s.hookManager.ExecuteHooks(BeforeCreate, ctx, entity); err != nil {
        return "", fmt.Errorf("创建前钩子执行失败: %w", err)
    }

    // 原有的创建逻辑
    s.SetEntityDefaults(entity)
    err := s.repo.Create(ctx, entity)
    if err != nil {
        return "", fmt.Errorf("创建失败: %w", err)
    }

    // 执行创建后钩子
    if s.newHookManager != nil {
        s.newHookManager.ExecuteHooks(ctx, hooks.AfterCreate, entity)
    }
    s.hookManager.ExecuteHooks(AfterCreate, ctx, entity)

    return s.GetEntityID(entity), nil
}
```

### 3. 预定义配置

系统提供了常用实体的预定义钩子配置：

```go
// 分类钩子配置
config := hooks.CategoryHooksConfig()
hookManager := integrator.SetupCommonHooks("category", config)

// 电子书钩子配置
config := hooks.BookHooksConfig()
hookManager := integrator.SetupCommonHooks("book", config)

// 帖子钩子配置
config := hooks.PostHooksConfig()
hookManager := integrator.SetupCommonHooks("post", config)

// 用户钩子配置
config := hooks.UserHooksConfig()
hookManager := integrator.SetupCommonHooks("user", config)
```

### 4. 自定义钩子

```go
// 注册自定义钩子
hookManager.RegisterCustomHook(
    hooks.BeforeCreate,
    "slug_generator",
    "生成URL友好的slug",
    15, // 优先级
    func(ctx context.Context, data interface{}) error {
        if entity, ok := data.(*YourEntity); ok {
            entity.Slug = generateSlug(entity.Title)
        }
        return nil
    },
)
```

## 最佳实践

### 1. 钩子优先级设置

建议的优先级范围：
- **1-10**: 数据清洗和格式化
- **11-20**: 数据验证
- **21-50**: 业务逻辑处理
- **51-100**: 审计和日志记录

### 2. 错误处理策略

- **Before 钩子**: 执行失败应该阻止主操作
- **After 钩子**: 执行失败不应该影响主操作，只记录日志

### 3. 性能考虑

- 避免在钩子中执行耗时操作
- 重复检查钩子会执行数据库查询，注意优化
- 合理设置钩子数量，避免过多钩子影响性能

### 4. 测试建议

```go
func TestServiceWithHooks(t *testing.T) {
    db := setupTestDB()
    service := NewServiceWithHooks(db)

    // 测试正常流程
    entity := &Entity{Name: "  test  "} // 测试数据清洗
    err := service.Create(context.Background(), entity)
    assert.NoError(t, err)
    assert.Equal(t, "test", entity.Name) // 验证清洗效果

    // 测试验证失败
    invalidEntity := &Entity{Name: ""}
    err = service.Create(context.Background(), invalidEntity)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "名称不能为空")

    // 测试重复检查
    duplicateEntity := &Entity{Name: "test"}
    err = service.Create(context.Background(), duplicateEntity)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "已存在")
}
```

## 迁移指南

### 从现有系统迁移

1. **保持兼容性**: 新系统与现有的 BaseService 钩子系统并存
2. **逐步迁移**: 可以逐个服务迁移到新的钩子系统
3. **测试验证**: 确保迁移后功能正常

### 迁移步骤

1. **创建钩子管理器**
   ```go
   hookManager := hooks.NewServiceHookManager(db, "entity_type")
   ```

2. **设置钩子配置**
   ```go
   integrator := hooks.NewServiceIntegrator(db)
   hookManager = integrator.SetupCommonHooks("entity_type", config)
   ```

3. **修改业务方法**
   ```go
   // 在 Create/Update/Delete 方法中添加钩子执行
   if err := hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, entity); err != nil {
       return err
   }
   ```

4. **测试验证**
   - 单元测试验证钩子逻辑
   - 集成测试验证完整流程
   - 性能测试确保无性能回归

## 故障排除

### 常见问题

1. **钩子不执行**
   - 检查钩子是否正确注册
   - 检查实体类型是否匹配
   - 检查钩子是否被禁用

2. **重复检查失败**
   - 检查表名是否正确
   - 检查字段名是否与数据库列名匹配
   - 检查数据库连接是否正常

3. **验证规则不生效**
   - 检查字段名是否与结构体字段名匹配
   - 检查验证规则语法是否正确
   - 检查正则表达式是否有效

4. **性能问题**
   - 检查钩子数量是否过多
   - 优化数据库查询
   - 使用性能分析工具检查耗时

### 调试技巧

1. **启用详细日志**
2. **使用单元测试验证钩子逻辑**
3. **检查钩子执行顺序和优先级**
4. **使用性能分析工具**

## 总结

Hooks 系统为 frontapi 项目提供了一个强大、灵活的数据处理框架。通过合理使用内置钩子和自定义钩子，可以：

- **简化业务代码**: 将通用逻辑抽取到钩子中
- **提高代码复用**: 钩子可以在多个服务中复用
- **增强可维护性**: 钩子逻辑独立，易于测试和维护
- **保证数据质量**: 通过验证和清洗钩子确保数据质量
- **支持审计需求**: 通过审计钩子记录操作历史

该系统设计遵循了 Go 语言的最佳实践，具有良好的性能和可扩展性，可以满足项目的长期发展需求。