import { RouteRecordRaw } from 'vue-router';

const userRoutes: Array<RouteRecordRaw> = [
    {
        path: '/user',
        name: 'User',
        component: () => import('@/views/personal/index.vue'),
        meta: {
            title: 'user',
            icon: 'user'
        },
        children: [
            {
                path: 'profile',
                name: 'UserProfile',
                component: () => import('@/views/personal/profile/index.vue'),
                meta: {
                    title: 'userProfile'
                }
            },
            {
                path: 'settings',
                name: 'UserSettings',
                component: () => import('@/views/personal/settings/index.vue'),
                meta: {
                    title: 'userSettings'
                }
            }
        ]
    }
];

export default userRoutes; 