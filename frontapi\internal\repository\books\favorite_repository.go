package books

import (
	"context"
	"frontapi/internal/models/books"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// FavoriteRepository 电子书收藏仓库接口
type FavoriteRepository interface {
	base.ExtendedRepository[books.BookFavorite]
	FindByUserAndBook(ctx context.Context, userID, bookID string) (*books.BookFavorite, error)
	DeleteByUserAndBook(ctx context.Context, userID, bookID string) error
	ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*books.BookFavorite, int64, error)
	CountByBook(ctx context.Context, bookID string) (int64, error)
}

type favoriteRepository struct {
	base.ExtendedRepository[books.BookFavorite]
}

// NewFavoriteRepository 创建电子书收藏仓库实例
func NewFavoriteRepository(db *gorm.DB) FavoriteRepository {
	return &favoriteRepository{
		ExtendedRepository: base.NewExtendedRepository[books.BookFavorite](db),
	}
}

// FindByUserAndBook 根据用户ID和电子书ID查找收藏
func (r *favoriteRepository) FindByUserAndBook(ctx context.Context, userID, bookID string) (*books.BookFavorite, error) {
	conditions := map[string]interface{}{
		"user_id": userID,
		"book_id": bookID,
	}
	return r.FindOneByCondition(ctx, conditions, "")
}

// DeleteByUserAndBook 根据用户ID和电子书ID删除收藏
func (r *favoriteRepository) DeleteByUserAndBook(ctx context.Context, userID, bookID string) error {
	return r.GetDBWithContext(ctx).Where("user_id = ? AND book_id = ?", userID, bookID).Delete(&books.BookFavorite{}).Error
}

// ListByUser 获取用户收藏的电子书列表
func (r *favoriteRepository) ListByUser(ctx context.Context, userID string, page, pageSize int) ([]*books.BookFavorite, int64, error) {
	return r.ListWithConditionAndPagination(ctx, "created_at DESC", page, pageSize, "user_id = ?", userID)
}

// CountByBook 统计电子书的收藏数量
func (r *favoriteRepository) CountByBook(ctx context.Context, bookID string) (int64, error) {
	condition := map[string]interface{}{"book_id": bookID}
	return r.Count(ctx, condition)
}
