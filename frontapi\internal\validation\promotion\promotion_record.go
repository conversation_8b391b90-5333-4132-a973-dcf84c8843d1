package promotion

// CreatePromotionRecordRequest 创建记录请求
type CreatePromotionRecordRequest struct {
	CampaignID    string  `json:"campaign_id"`
	UserID        string  `json:"user_id"`
	RewardType    string  `json:"reward_type"`
	RewardValue   float64 `json:"reward_value"`
	TransactionID string  `json:"transaction_id"`
	OrderID       string  `json:"order_id"`
	ReferenceID   string  `json:"reference_id"`
	Status        string  `json:"status"`
	IP            string  `json:"ip"`
	Device        string  `json:"device"`
}

type UpdatePromotionRecordRequest struct {
	RewardType    string  `json:"reward_type"`
	RewardValue   float64 `json:"reward_value"`
	TransactionID string  `json:"transaction_id"`
	OrderID       string  `json:"order_id"`
	ReferenceID   string  `json:"reference_id"`
	Status        string  `json:"status"`
	IP            string  `json:"ip"`
	Device        string  `json:"device"`
}
