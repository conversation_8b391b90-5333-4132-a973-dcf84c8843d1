<template>
  <div class="comment-section">
    <!-- 评论输入框 -->
    <div class="comment-input-section">
      <div class="comment-input-header">
        <span class="comment-count">{{ totalComments }} 条评论</span>
        <el-dropdown @command="handleSortChange">
          <span class="sort-trigger">
            {{ sortOptions.find(opt => opt.value === currentSort)?.label }}
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="option in sortOptions"
                :key="option.value"
                :command="option.value"
                :class="{ 'is-active': currentSort === option.value }"
              >
                {{ option.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      
      <div class="comment-input-container">
        <div class="user-avatar">
          <el-avatar
            :size="40"
            :src="currentUser?.avatar"
            :icon="UserFilled"
          />
        </div>
        <div class="comment-input-wrapper">
          <el-input
            v-model="newComment"
            type="textarea"
            :rows="3"
            :placeholder="currentUser ? '添加评论...' : '请先登录后评论'"
            :disabled="!currentUser || submitting"
            resize="none"
            @focus="showInputActions = true"
          />
          <div v-if="showInputActions" class="comment-input-actions">
            <div class="input-actions-left">
              <el-button
                type="text"
                :icon="Picture"
                @click="handleAddImage"
              >
                图片
              </el-button>
              <el-button
                type="text"
                :icon="VideoCamera"
                @click="handleAddVideo"
              >
                视频
              </el-button>
            </div>
            <div class="input-actions-right">
              <el-button
                @click="cancelComment"
                :disabled="submitting"
              >
                取消
              </el-button>
              <el-button
                type="primary"
                :loading="submitting"
                :disabled="!newComment.trim()"
                @click="submitComment"
              >
                评论
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 评论列表 -->
    <div class="comment-list">
      <div v-if="loading" class="comment-loading">
        <el-skeleton
          v-for="i in 3"
          :key="i"
          :rows="3"
          animated
          class="comment-skeleton"
        />
      </div>
      
      <div v-else-if="comments.length === 0" class="empty-comments">
        <el-empty description="暂无评论，快来抢沙发吧！" />
      </div>
      
      <div v-else>
        <CommentItem
          v-for="comment in comments"
          :key="comment.id"
          :comment="comment"
          :current-user="currentUser"
          @reply="handleReply"
          @like="handleLike"
          @delete="handleDelete"
          @report="handleReport"
        />
        
        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <el-button
            v-if="!loadingMore"
            type="text"
            @click="loadMoreComments"
          >
            查看更多评论
          </el-button>
          <el-skeleton v-else :rows="2" animated />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import {
  ArrowDown,
  UserFilled,
  Picture,
  VideoCamera
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import CommentItem from './CommentItem.vue'
import type { Comment, User } from '@/types'

export interface CommentSectionProps {
  targetId: string
  targetType: 'video' | 'post' | 'live'
  currentUser?: User | null
}

const props = defineProps<CommentSectionProps>()

const emit = defineEmits<{
  commentAdded: [comment: Comment]
  commentDeleted: [commentId: string]
  commentLiked: [commentId: string, liked: boolean]
}>()

// 评论数据
const comments = ref<Comment[]>([])
const totalComments = ref(0)
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = 20

// 排序选项
const sortOptions = [
  { label: '最新', value: 'newest' },
  { label: '最热', value: 'hottest' },
  { label: '最早', value: 'oldest' }
]
const currentSort = ref('newest')

// 评论输入
const newComment = ref('')
const showInputActions = ref(false)
const submitting = ref(false)

// 加载评论列表
const loadComments = async (reset = false) => {
  if (reset) {
    loading.value = true
    currentPage.value = 1
    comments.value = []
  } else {
    loadingMore.value = true
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockComments: Comment[] = [
      {
        id: '1',
        content: '这个视频太棒了！学到了很多东西。',
        author: {
          id: 'user1',
          username: '用户1',
          avatar: 'https://via.placeholder.com/40',
          isVerified: true
        },
        createdAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(),
        likes: 15,
        isLiked: false,
        replies: [
          {
            id: '1-1',
            content: '同感！作者讲得很清楚。',
            author: {
              id: 'user2',
              username: '用户2',
              avatar: 'https://via.placeholder.com/40'
            },
            createdAt: new Date(Date.now() - 1000 * 60 * 20).toISOString(),
            likes: 3,
            isLiked: true,
            replyTo: {
              id: 'user1',
              username: '用户1'
            }
          }
        ]
      },
      {
        id: '2',
        content: '请问这个技术在实际项目中怎么应用？',
        author: {
          id: 'user3',
          username: '开发者小王',
          avatar: 'https://via.placeholder.com/40'
        },
        createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
        likes: 8,
        isLiked: false,
        replies: []
      }
    ]
    
    if (reset) {
      comments.value = mockComments
      totalComments.value = 25
    } else {
      comments.value.push(...mockComments)
    }
    
    hasMore.value = comments.value.length < totalComments.value
    currentPage.value++
    
  } catch (error) {
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 加载更多评论
const loadMoreComments = () => {
  if (!loadingMore.value && hasMore.value) {
    loadComments(false)
  }
}

// 排序变更
const handleSortChange = (sortType: string) => {
  currentSort.value = sortType
  loadComments(true)
}

// 提交评论
const submitComment = async () => {
  if (!newComment.value.trim() || !props.currentUser) {
    return
  }
  
  submitting.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const comment: Comment = {
      id: Date.now().toString(),
      content: newComment.value.trim(),
      author: props.currentUser,
      createdAt: new Date().toISOString(),
      likes: 0,
      isLiked: false,
      replies: []
    }
    
    comments.value.unshift(comment)
    totalComments.value++
    
    emit('commentAdded', comment)
    
    newComment.value = ''
    showInputActions.value = false
    
    ElMessage.success('评论发布成功')
    
  } catch (error) {
    ElMessage.error('评论发布失败')
  } finally {
    submitting.value = false
  }
}

// 取消评论
const cancelComment = () => {
  newComment.value = ''
  showInputActions.value = false
}

// 回复评论
const handleReply = (commentId: string, replyContent: string) => {
  // 处理回复逻辑
  console.log('回复评论:', commentId, replyContent)
}

// 点赞评论
const handleLike = async (commentId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const comment = findCommentById(commentId)
    if (comment) {
      comment.isLiked = !comment.isLiked
      comment.likes += comment.isLiked ? 1 : -1
      
      emit('commentLiked', commentId, comment.isLiked)
    }
    
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

// 删除评论
const handleDelete = async (commentId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    comments.value = comments.value.filter(comment => comment.id !== commentId)
    totalComments.value--
    
    emit('commentDeleted', commentId)
    
    ElMessage.success('评论已删除')
    
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 举报评论
const handleReport = (commentId: string) => {
  ElMessage.info('举报功能开发中')
}

// 添加图片
const handleAddImage = () => {
  ElMessage.info('图片上传功能开发中')
}

// 添加视频
const handleAddVideo = () => {
  ElMessage.info('视频上传功能开发中')
}

// 查找评论
const findCommentById = (id: string): Comment | null => {
  for (const comment of comments.value) {
    if (comment.id === id) {
      return comment
    }
    for (const reply of comment.replies || []) {
      if (reply.id === id) {
        return reply
      }
    }
  }
  return null
}

// 监听目标变化
watch(
  () => [props.targetId, props.targetType],
  () => {
    loadComments(true)
  },
  { immediate: true }
)

// 监听排序变化
watch(currentSort, () => {
  loadComments(true)
})

onMounted(() => {
  loadComments(true)
})
</script>

<style scoped>
.comment-section {
  @apply space-y-6;
}

.comment-input-section {
  @apply space-y-4;
}

.comment-input-header {
  @apply flex items-center justify-between;
}

.comment-count {
  @apply text-lg font-semibold text-gray-900;
}

.sort-trigger {
  @apply flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900 cursor-pointer;
}

.comment-input-container {
  @apply flex gap-3;
}

.user-avatar {
  @apply flex-shrink-0;
}

.comment-input-wrapper {
  @apply flex-1 space-y-3;
}

.comment-input-actions {
  @apply flex items-center justify-between;
}

.input-actions-left {
  @apply flex items-center gap-2;
}

.input-actions-right {
  @apply flex items-center gap-2;
}

.comment-list {
  @apply space-y-4;
}

.comment-loading {
  @apply space-y-4;
}

.comment-skeleton {
  @apply p-4 border border-gray-200 rounded-lg;
}

.empty-comments {
  @apply py-12;
}

.load-more {
  @apply text-center py-4;
}

:deep(.el-textarea__inner) {
  @apply border-gray-300 focus:border-blue-500;
}

:deep(.el-dropdown-menu__item.is-active) {
  @apply text-blue-600 bg-blue-50;
}
</style>