package admin

import (
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

func RegisterIntegralRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// apiGroup := apiGroup.Group("/integral")

	// // 积分记录
	// pointCtrl := integral2.NewPointController(services.PointService)
	// {

	// }

	// // 积分兑换
	// exchangeCtrl := integral2.NewPointExchangeController(services.PointExchangeService)
	// {

	// }

	// // 积分等级
	// levelCtrl := integral2.NewPointLevelController(services.PointLevelService)
	// {

	// }

	// // 积分规则
	// ruleCtrl := integral2.NewPointRuleController(services.PointRuleService)
	// {

	// }

}
