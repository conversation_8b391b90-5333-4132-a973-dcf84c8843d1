# Vue 3 组合式函数库

这是一个全面的 Vue 3 组合式函数库，提供了丰富的功能模块来简化开发工作。

## 📦 安装

```bash
npm install
```

## 🚀 快速开始

### 基本使用

```typescript
import { useAuth, useApi } from '@/shared/composables'
import { useTheme } from '@/core/plugins/theme'

// 在组件中使用
export default defineComponent({
  setup() {
    const { login, logout, user } = useAuth()
    const { get, post } = useApi()
    const { currentTheme, setTheme } = useTheme()
    
    return {
      login,
      logout,
      user,
      get,
      post,
      currentTheme,
      setTheme
    }
  }
})
```

### 动态导入

```typescript
import { composables } from '@/shared/composables'

// 按需加载
const { useAuth } = await composables.auth()
const { useApi } = await composables.api()
```

### 配置

```typescript
import { configComposables } from '@/shared/composables'

// 全局配置
configComposables({
  auth: {
    tokenKey: 'access_token',
    loginUrl: '/api/auth/login'
  },
  api: {
    baseUrl: 'https://api.example.com',
    timeout: 10000
  },
  theme: {
    default: 'light',
    storage: true
  }
})
```

## 📚 功能模块

### 🔐 认证相关
- `useAuth` - 用户认证管理
- `useUser` - 用户信息管理

### 🌐 API相关
- `request` - 统一HTTP请求封装
- `get` - GET请求
- `post` - POST请求
- `put` - PUT请求
- `del` - DELETE请求
- `page` - 分页请求
- `postPage` - POST分页请求
- `useApi` - HTTP请求封装（兼容旧版本）
- `useRequest`、`useGet`、`usePost`、`usePut`、`useDelete`、`usePage`、`usePostPage` - 兼容旧版本命名

### 💾 存储相关
- `useStorage` - 通用存储
- `useLocalStorage` - 本地存储
- `useSessionStorage` - 会话存储
- `useCookie` - Cookie管理

### 🎨 UI相关
- `useTheme` - 主题管理
- `useLanguage` - 语言切换
- `useModal` - 模态框管理
- `useToast` - 消息提示
- `useLoading` - 加载状态

### 📱 设备相关
- `useDevice` - 设备检测
- `useNetwork` - 网络状态

### 🎬 媒体相关
- `useMedia` - 媒体查询
- `useUpload` - 文件上传
- `useDownload` - 文件下载

### 🛠️ 工具相关
- `useClipboard` - 剪贴板操作
- `useFullscreen` - 全屏控制
- `useNotification` - 通知管理
- `useGeolocation` - 地理位置

### 📡 通信相关
- `useWebSocket` - WebSocket连接
- `useEventBus` - 事件总线

### 📊 数据相关
- `usePagination` - 分页管理
- `useSearch` - 搜索功能
- `useForm` - 表单处理
- `useTable` - 表格管理

### ⚡ 性能相关
- `useDebounce` - 防抖处理
- `useThrottle` - 节流处理
- `useInterval` - 定时器
- `useTimeout` - 延时器

### 🖱️ 交互相关
- `useDrag` - 拖拽功能
- `useDrop` - 拖放功能
- `useResize` - 尺寸变化
- `useScroll` - 滚动监听
- `useIntersection` - 交叉观察

### ⌨️ 输入相关
- `useKeyboard` - 键盘事件
- `useMouse` - 鼠标事件
- `useTouch` - 触摸事件
- `useSwipe` - 滑动手势
- `useLongPress` - 长按事件
- `useClickOutside` - 外部点击
- `useFocus` - 焦点管理
- `useHover` - 悬停状态
- `useVisible` - 可见性检测

### 🔒 权限相关
- `usePermission` - 权限管理

### 📤 分享相关
- `useShare` - 分享功能
- `useQRCode` - 二维码生成
- `useBarcode` - 条形码处理

### 🎥 硬件相关
- `useCamera` - 摄像头控制
- `useMicrophone` - 麦克风控制
- `useScreen` - 屏幕录制
- `useBattery` - 电池状态
- `useVibration` - 震动控制
- `useSpeech` - 语音识别
- `useGamepad` - 游戏手柄

### 🚀 高级功能
- `useWebRTC` - WebRTC通信
- `useIndexedDB` - IndexedDB数据库
- `useWorker` - Web Worker
- `useServiceWorker` - Service Worker
- `usePWA` - PWA功能

### 📈 分析相关
- `useAnalytics` - 数据分析
- `useLogger` - 日志记录
- `usePerformance` - 性能监控
- `useSEO` - SEO优化
- `useMetrics` - 指标收集

### 🧪 实验相关
- `useExperiment` - A/B测试
- `useFeatureFlag` - 功能开关

### ⚙️ 配置相关
- `useConfig` - 配置管理
- `useEnvironment` - 环境变量
- `useVersion` - 版本管理
- `useBuild` - 构建信息

### 🌍 国际化
- `useInternationalization` - 国际化
- `useLocalization` - 本地化
- `useTranslation` - 翻译
- `useCurrency` - 货币格式
- `useTimezone` - 时区处理

### ⏰ 时间相关
- `useCalendar` - 日历功能
- `useDate` - 日期处理
- `useTime` - 时间处理
- `useClock` - 时钟显示
- `useTimer` - 计时器
- `useStopwatch` - 秒表
- `useCountdown` - 倒计时
- `useScheduler` - 任务调度
- `useCron` - Cron表达式

### 🔧 调试相关
- `useProfiler` - 性能分析
- `useDebugger` - 调试工具
- `useTesting` - 测试工具
- `useMocking` - 模拟数据

### 📦 状态管理
- `useHistory` - 历史记录
- `useUndo` - 撤销操作
- `useRedo` - 重做操作
- `useState` - 状态管理
- `useStore` - 存储管理
- `useReducer` - Reducer模式

### 🏗️ 设计模式
- `useService` - 服务模式
- `useRepository` - 仓储模式
- `useFactory` - 工厂模式
- `useBuilder` - 建造者模式
- `useAdapter` - 适配器模式
- `useProxy` - 代理模式
- `useDecorator` - 装饰器模式
- `useFacade` - 外观模式
- `useObserver` - 观察者模式
- `useStrategy` - 策略模式

### 🔌 插件系统
- `usePlugin` - 插件管理
- `useMiddleware` - 中间件
- `useInterceptor` - 拦截器
- `useFilter` - 过滤器
- `useTransformer` - 转换器
- `useValidator` - 验证器
- `useSerializer` - 序列化器
- `useParser` - 解析器
- `useFormatter` - 格式化器

## 🔧 API 参考

### HTTP请求工具

#### 核心函数

##### `request<T>(url: string, options?: UseRequestOptions)`
通用HTTP请求Hook

**参数:**
- `url: string` - 请求URL，支持路径参数如 `/api/users/{id}`
- `options?: UseRequestOptions` - 请求配置选项

**返回值:**
```typescript
{
  data: Ref<T | null>,           // 响应数据
  loading: Ref<boolean>,         // 加载状态
  error: Ref<ApiError | null>,   // 错误信息
  execute: (params?) => Promise<RequestResult<T>>  // 执行请求
}
```

##### `get<T>(url: string, options?: UseRequestOptions)`
GET请求Hook

##### `post<T>(url: string, options?: UseRequestOptions)`
POST请求Hook

##### `put<T>(url: string, options?: UseRequestOptions)`
PUT请求Hook

##### `del<T>(url: string, options?: UseRequestOptions)`
DELETE请求Hook

##### `page<T>(url: string, options?: UseRequestOptions)`
分页GET请求Hook

##### `postPage<T>(url: string, options?: UseRequestOptions)`
分页POST请求Hook

#### 请求配置选项

```typescript
interface UseRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  immediate?: boolean        // 是否立即执行，默认true
  showLoading?: boolean      // 是否显示loading，默认false
  showError?: boolean        // 是否自动显示错误，默认false
  cache?: boolean           // 是否缓存，默认false
  retry?: number            // 重试次数，默认0
  timeout?: number          // 超时时间，默认30000ms
  onSuccess?: (data: T) => void     // 成功回调
  onError?: (error: ApiError) => void  // 错误回调
}
```

#### 请求参数格式

```typescript
// 普通请求参数
interface RequestParams {
  data?: Record<string, any>  // 请求数据
}

// 分页请求参数
interface PageRequestParams {
  data?: Record<string, any>  // 请求数据
  page?: {
    page: number              // 页码，从1开始
    pageSize: number          // 每页大小
  }
}
```

#### 响应数据格式

```typescript
// 标准API响应
interface ApiResponse<T = any> {
  code: number              // 状态码，2000表示成功
  message: string           // 响应消息
  data: T                   // 响应数据
  timestamp?: number        // 时间戳
  traceId?: string         // 追踪ID
}

// 分页响应数据
interface PageResponse<T = any> {
  list: T[]                 // 数据列表
  total: number             // 总数
  page: number              // 当前页码
  pageSize: number          // 每页大小
}
```

#### 工具函数

##### `isSuccess(response: ApiResponse): boolean`
判断API响应是否成功

##### `getData<T>(response: ApiResponse<T>): T | null`
安全获取响应数据

##### `getErrorMessage(response: ApiResponse): string`
获取错误消息

#### 原始请求方法

```typescript
// 从 @/core/utils/request 导入
import { request, get, post, put, del, getPage, postPageList, upload, download } from '@/core/utils/request'

// 通用请求
request<T>(config: RequestConfig): Promise<ApiResponse<T>>

// 便捷方法
get<T>(url: string, params?: any, options?: RequestOptions): Promise<ApiResponse<T>>
post<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>
put<T>(url: string, data?: any, options?: RequestOptions): Promise<ApiResponse<T>>
del<T>(url: string, params?: any, options?: RequestOptions): Promise<ApiResponse<T>>

// 分页请求
getPage<T>(url: string, params?: PageRequestParams, options?: RequestOptions): Promise<ApiResponse<PageResponse<T>>>
postPageList<T>(url: string, params?: PageRequestParams, options?: RequestOptions): Promise<ApiResponse<PageResponse<T>>>
postPageListSimple<T>(url: string, params?: any, headers?: Record<string, string>): Promise<ApiResponse<PageResponse<T>>>

// 文件操作
upload<T>(url: string, file: File | FormData, options?: RequestOptions): Promise<ApiResponse<T>>
download(url: string, params?: any, filename?: string): Promise<void>
```

### 配置函数

#### `configComposables(config: ComposablesConfig)`
配置全局设置

#### `getComposablesConfig(): ComposablesConfig`
获取当前配置

#### `getModuleConfig<T>(module: T): ComposablesConfig[T]`
获取特定模块配置

#### `resetComposablesConfig(): void`
重置配置

### 工具对象

#### `composables`
包含所有组合式函数的动态导入对象

#### `features`
浏览器特性检测对象

#### `version`
当前版本号

#### `buildTime`
构建时间

#### `env`
当前环境

## 📝 使用示例

### 认证示例

```typescript
import { useAuth } from '@/shared/composables'

export default defineComponent({
  setup() {
    const { 
      user, 
      isAuthenticated, 
      login, 
      logout, 
      register 
    } = useAuth()
    
    const handleLogin = async () => {
      try {
        await login({
          email: '<EMAIL>',
          password: 'password'
        })
        console.log('登录成功')
      } catch (error) {
        console.error('登录失败:', error)
      }
    }
    
    return {
      user,
      isAuthenticated,
      handleLogin,
      logout
    }
  }
})
```

### HTTP请求示例

#### 基础请求

```typescript
import { get, post, page } from '@/shared/composables'

export default defineComponent({
  setup() {
    // GET请求
    const { data: users, loading, error, execute } = get<User[]>('/api/users')
    
    // POST请求
    const { data: newUser, loading: creating, execute: createUser } = post<User>('/api/users', {
      immediate: false,
      showLoading: true,
      showError: true
    })
    
    // 分页请求
    const { 
      data: pageData, 
      loading: pageLoading, 
      execute: loadPage 
    } = page<User>('/api/users/page')
    
    const handleCreateUser = async (userData: CreateUserData) => {
      const result = await createUser({ data: userData })
      if (result.success) {
        console.log('用户创建成功:', result.data)
        // 重新加载用户列表
        execute()
      }
    }
    
    const handleLoadPage = (page: number, pageSize: number) => {
      loadPage({
        data: { name: 'search' },
        page: { page, pageSize }
      })
    }
    
    return {
      users,
      loading,
      error,
      newUser,
      creating,
      handleCreateUser,
      pageData,
      pageLoading,
      handleLoadPage
    }
  }
})
```

#### 手动错误处理

```typescript
import { request } from '@/shared/composables'
import { ElMessage } from 'element-plus'

export default defineComponent({
  setup() {
    const { execute, loading } = request<User>('/api/users/{id}', {
      immediate: false,
      showError: false // 关闭自动错误提示
    })
    
    const fetchUser = async (id: number) => {
      const result = await execute({ data: { id } })
      
      if (result.success) {
        console.log('用户信息:', result.data)
      } else {
        // 自定义错误处理
        if (result.error?.code === 404) {
          ElMessage.warning('用户不存在')
        } else {
          ElMessage.error(result.error?.message || '获取用户信息失败')
        }
      }
    }
    
    return {
      fetchUser,
      loading
    }
  }
})
```

#### 文件上传示例

```typescript
import { upload, postPageListSimple } from '@/core/utils/request'

export default defineComponent({
  setup() {
    const uploadFile = async (file: File) => {
      try {
        const result = await upload('/api/upload', file, {
          onUploadProgress: (progress) => {
            console.log('上传进度:', progress)
          }
        })
        
        if (result.success) {
          console.log('文件上传成功:', result.data)
        } else {
          ElMessage.error(result.message || '文件上传失败')
        }
      } catch (error) {
        console.error('上传错误:', error)
        ElMessage.error('文件上传失败')
      }
    }
    
    // 使用简单的POST分页请求（兼容现有代码）
    const fetchPageData = async () => {
      try {
        const result = await postPageListSimple('/api/data/page', {
          name: 'search',
          page: 1,
          pageSize: 10
        }, {
          'Authorization': 'Bearer token'
        })
        
        if (result.code === 2000) {
          console.log('分页数据:', result.data)
        }
      } catch (error) {
        console.error('获取分页数据失败:', error)
      }
    }
    
    return {
      uploadFile,
      fetchPageData
    }
  }
})
```

#### 批量操作示例

```typescript
import { request as useRequest } from '@/shared/composables'
import { request } from '@/core/utils/request'

export default defineComponent({
  setup() {
    const { execute: deleteUser, loading: deleting } = useRequest('/api/users/{id}', {
      method: 'DELETE',
      immediate: false,
      showLoading: true
    })
    
    const batchDelete = async (userIds: number[]) => {
      try {
        // 并发删除
        const results = await request.all(
          userIds.map(id => deleteUser({ data: { id } }))
        )
        
        const successCount = results.filter(r => r.success).length
        const failCount = results.length - successCount
        
        if (failCount === 0) {
          ElMessage.success(`成功删除 ${successCount} 个用户`)
        } else {
          ElMessage.warning(`删除完成，成功 ${successCount} 个，失败 ${failCount} 个`)
        }
      } catch (error) {
        ElMessage.error('批量删除失败')
      }
    }
    
    return {
      batchDelete,
      deleting
    }
  }
})
```
```

### 主题切换示例

```typescript
import { useTheme } from '@/core/plugins/theme'

export default defineComponent({
  setup() {
    const { 
      currentTheme, 
      themes, 
      setTheme, 
      nextTheme 
    } = useTheme()
    
    return {
      currentTheme,
      themes,
      setTheme,
      nextTheme
    }
  }
})
```

### 存储示例

```typescript
import { useLocalStorage } from '@/shared/composables'

export default defineComponent({
  setup() {
    const { 
      value: userPreferences, 
      set, 
      remove 
    } = useLocalStorage('user-preferences', {
      theme: 'light',
      language: 'zh-CN'
    })
    
    const updatePreferences = (newPrefs: any) => {
      set({ ...userPreferences.value, ...newPrefs })
    }
    
    return {
      userPreferences,
      updatePreferences,
      remove
    }
  }
})
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个库。

## 📄 许可证

MIT License