package promotion

import (
	"frontapi/internal/admin"
	service "frontapi/internal/service/promotion"

	"github.com/gofiber/fiber/v2"
)

type InvitationCommissionController struct {
	admin.BaseController
	service service.InvitationCommissionService
}

func NewInvitationCommissionController(service service.InvitationCommissionService) *InvitationCommissionController {
	return &InvitationCommissionController{service: service}
}

// GetCommission 获取佣金详情
func (h *InvitationCommissionController) ListInvitationCommission(c *fiber.Ctx) error {

	return h.Success(c, "")
}
