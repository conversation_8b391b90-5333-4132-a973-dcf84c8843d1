<template>
  <div class="slinky-search-bar">
    <el-form
      ref="formRef"
      :model="searchForm"
      :inline="true"
      :label-width="labelWidth"
      :size="size"
      @submit.prevent="handleSearch"
    >
      <template v-for="field in fields" :key="field.prop">
        <el-form-item
          :label="field.label"
          :prop="field.prop"
          :rules="field.rules"
          :class="field.class"
        >
          <!-- 文本输入框 -->
          <el-input
            v-if="field.type === 'input'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder || `请输入${field.label}`"
            :clearable="field.clearable !== false"
            :disabled="field.disabled"
            :maxlength="field.maxlength"
            :show-word-limit="field.showWordLimit"
            @keyup.enter="handleSearch"
            @clear="field.onClear && field.onClear()"
          />
          
          <!-- 下拉选择 -->
          <el-select
            v-else-if="field.type === 'select'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder || `请选择${field.label}`"
            :clearable="field.clearable !== false"
            :disabled="field.disabled"
            :multiple="field.multiple"
            :filterable="field.filterable"
            :remote="field.remote"
            :remote-method="field.remoteMethod"
            :loading="field.loading"
            @change="field.onChange && field.onChange($event)"
          >
            <el-option
              v-for="option in field.options"
              :key="option.value"
              :label="option.label"
              :value="option.value"
              :disabled="option.disabled"
            />
          </el-select>
          
          <!-- 级联选择 -->
          <el-cascader
            v-else-if="field.type === 'cascader'"
            v-model="searchForm[field.prop]"
            :options="field.options"
            :placeholder="field.placeholder || `请选择${field.label}`"
            :clearable="field.clearable !== false"
            :disabled="field.disabled"
            :show-all-levels="field.showAllLevels !== false"
            :separator="field.separator || '/'"
            :props="field.cascaderProps"
            @change="field.onChange && field.onChange($event)"
          />
          
          <!-- 日期选择 -->
          <el-date-picker
            v-else-if="field.type === 'date'"
            v-model="searchForm[field.prop]"
            :type="field.dateType || 'date'"
            :placeholder="field.placeholder || `请选择${field.label}`"
            :clearable="field.clearable !== false"
            :disabled="field.disabled"
            :format="field.format"
            :value-format="field.valueFormat"
            :start-placeholder="field.startPlaceholder"
            :end-placeholder="field.endPlaceholder"
            :range-separator="field.rangeSeparator || '至'"
            @change="field.onChange && field.onChange($event)"
          />
          
          <!-- 数字输入 -->
          <el-input-number
            v-else-if="field.type === 'number'"
            v-model="searchForm[field.prop]"
            :placeholder="field.placeholder"
            :disabled="field.disabled"
            :min="field.min"
            :max="field.max"
            :step="field.step"
            :precision="field.precision"
            :controls="field.controls !== false"
            @change="field.onChange && field.onChange($event)"
          />
          
          <!-- 开关 -->
          <el-switch
            v-else-if="field.type === 'switch'"
            v-model="searchForm[field.prop]"
            :disabled="field.disabled"
            :active-text="field.activeText"
            :inactive-text="field.inactiveText"
            :active-value="field.activeValue"
            :inactive-value="field.inactiveValue"
            @change="field.onChange && field.onChange($event)"
          />
          
          <!-- 单选框组 -->
          <el-radio-group
            v-else-if="field.type === 'radio'"
            v-model="searchForm[field.prop]"
            :disabled="field.disabled"
            @change="field.onChange && field.onChange($event)"
          >
            <el-radio
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
          
          <!-- 复选框组 -->
          <el-checkbox-group
            v-else-if="field.type === 'checkbox'"
            v-model="searchForm[field.prop]"
            :disabled="field.disabled"
            @change="field.onChange && field.onChange($event)"
          >
            <el-checkbox
              v-for="option in field.options"
              :key="option.value"
              :label="option.value"
              :disabled="option.disabled"
            >
              {{ option.label }}
            </el-checkbox>
          </el-checkbox-group>
          
          <!-- 自定义插槽 -->
          <slot
            v-else-if="field.type === 'slot'"
            :name="field.slot"
            :field="field"
            :value="searchForm[field.prop]"
            :setValue="(val: any) => (searchForm[field.prop] = val)"
          />
        </el-form-item>
      </template>
      
      <!-- 操作按钮 -->
      <el-form-item class="search-actions">
        <el-button
          type="primary"
          :icon="Search"
          @click="handleSearch"
          :loading="searching"
        >
          {{ searchText }}
        </el-button>
        <el-button
          :icon="Refresh"
          @click="handleReset"
        >
          {{ resetText }}
        </el-button>
        <el-button
          v-if="showExpand && expandableFields.length > 0"
          :icon="expanded ? ArrowUp : ArrowDown"
          @click="toggleExpand"
        >
          {{ expanded ? '收起' : '展开' }}
        </el-button>
        <slot name="actions"></slot>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ArrowDown, ArrowUp, Refresh, Search } from '@element-plus/icons-vue'
import { ElForm } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'

// 字段配置接口
interface SearchField {
  prop: string
  label: string
  type: 'input' | 'select' | 'cascader' | 'date' | 'number' | 'switch' | 'radio' | 'checkbox' | 'slot'
  placeholder?: string
  clearable?: boolean
  disabled?: boolean
  rules?: any[]
  class?: string
  
  // input 特有属性
  maxlength?: number
  showWordLimit?: boolean
  onClear?: () => void
  
  // select 特有属性
  options?: { label: string; value: any; disabled?: boolean }[]
  multiple?: boolean
  filterable?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => void
  loading?: boolean
  onChange?: (value: any) => void
  
  // cascader 特有属性
  showAllLevels?: boolean
  separator?: string
  cascaderProps?: any
  
  // date 特有属性
  dateType?: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange'
  format?: string
  valueFormat?: string
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  
  // number 特有属性
  min?: number
  max?: number
  step?: number
  precision?: number
  controls?: boolean
  
  // switch 特有属性
  activeText?: string
  inactiveText?: string
  activeValue?: any
  inactiveValue?: any
  
  // 扩展相关
  expandable?: boolean
  
  // slot 特有属性
  slot?: string
}

interface Props {
  fields: SearchField[]
  modelValue?: Record<string, any>
  labelWidth?: string | number
  size?: 'large' | 'default' | 'small'
  searching?: boolean
  searchText?: string
  resetText?: string
  showExpand?: boolean
  defaultExpanded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  labelWidth: '80px',
  size: 'default',
  searching: false,
  searchText: '搜索',
  resetText: '重置',
  showExpand: true,
  defaultExpanded: false
})

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void
  (e: 'search', value: Record<string, any>): void
  (e: 'reset'): void
  (e: 'field-change', prop: string, value: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<InstanceType<typeof ElForm>>()
const expanded = ref(props.defaultExpanded)

// 初始化搜索表单
const initSearchForm = () => {
  const form: Record<string, any> = {}
  props.fields.forEach(field => {
    if (props.modelValue && props.modelValue[field.prop] !== undefined) {
      form[field.prop] = props.modelValue[field.prop]
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'checkbox':
          form[field.prop] = []
          break
        case 'switch':
          form[field.prop] = field.inactiveValue ?? false
          break
        case 'number':
          form[field.prop] = undefined
          break
        default:
          form[field.prop] = ''
      }
    }
  })
  return form
}

const searchForm = reactive(initSearchForm())

// 计算属性
const expandableFields = computed(() => 
  props.fields.filter(field => field.expandable)
)

const visibleFields = computed(() => {
  if (!props.showExpand || expandableFields.value.length === 0) {
    return props.fields
  }
  
  if (expanded.value) {
    return props.fields
  } else {
    return props.fields.filter(field => !field.expandable)
  }
})

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      Object.keys(newVal).forEach(key => {
        if (searchForm[key] !== newVal[key]) {
          searchForm[key] = newVal[key]
        }
      })
    }
  },
  { deep: true }
)

// 监听表单变化，同步到外部
watch(
  searchForm,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true }
)

// 方法
const handleSearch = async () => {
  try {
    await formRef.value?.validate()
    emit('search', { ...searchForm })
  } catch (error) {
    console.warn('搜索表单验证失败:', error)
  }
}

const handleReset = () => {
  // 重置表单
  Object.keys(searchForm).forEach(key => {
    const field = props.fields.find(f => f.prop === key)
    if (field) {
      switch (field.type) {
        case 'checkbox':
          searchForm[key] = []
          break
        case 'switch':
          searchForm[key] = field.inactiveValue ?? false
          break
        case 'number':
          searchForm[key] = undefined
          break
        default:
          searchForm[key] = ''
      }
    }
  })
  
  // 清除验证结果
  formRef.value?.clearValidate()
  
  emit('reset')
}

const toggleExpand = () => {
  expanded.value = !expanded.value
}

const validate = () => {
  return formRef.value?.validate()
}

const clearValidate = (props?: string | string[]) => {
  formRef.value?.clearValidate(props)
}

const resetFields = () => {
  formRef.value?.resetFields()
}

// 导出方法
defineExpose({
  validate,
  clearValidate,
  resetFields,
  searchForm
})
</script>

<style scoped lang="scss">
.slinky-search-bar {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
  border-radius: var(--el-card-border-radius);
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.1);
  margin-bottom: 16px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 16px;
      line-height: 50px;
      &:last-child {
        margin-right: 0;
      }
      
      .el-form-item__label {
        color: #333;
        font-weight: 500;
        font-size: 14px;
      }
      
      .el-form-item__content {
        .el-input,
        .el-select,
        .el-cascader,
        .el-date-editor {
          width: 200px;
          
          .el-input__wrapper,
          .el-select__wrapper {
            border-radius: var(--el-card-border-radius);
            transition: all 0.3s ease;
            
            &:hover {
              border-color: #667eea;
            }
            
            &.is-focus {
              border-color: #667eea;
              box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
            }
          }
        }
        
        .el-input-number {
          width: 160px;
        }
        
        .el-switch {
          margin-top: 4px;
        }
        
        .el-radio-group,
        .el-checkbox-group {
          .el-radio,
          .el-checkbox {
            margin-right: 12px;
            
            .el-radio__label,
            .el-checkbox__label {
              font-size: 14px;
            }
          }
        }
      }
      
      &.search-actions {
        margin-bottom: 0;
        
        .el-form-item__content {
          .el-button {
            border-radius: var(--el-card-border-radius);
            margin-right: 8px;
            transition: all 0.3s ease;
            
            &.el-button--primary {
              background: linear-gradient(45deg, #667eea, #764ba2);
              border: none;
            }
            
            &:not(.el-button--primary) {
              background: #fff;
              border: 1px solid #dcdfe6;
              color: #606266;
              
              &:hover {
                border-color: #c0c4cc;
                background: #f5f7fa;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .slinky-search-bar {
    padding: 16px;
    
    .el-form {
      .el-form-item {
        margin-right: 0;
        
        .el-form-item__content {
          .el-input,
          .el-select,
          .el-cascader,
          .el-date-editor {
            width: 100%;
          }
        }
      }
    }
  }
}
</style> 