# ExtLike 点赞服务实施指南\n\n## 目录\n\n1. [概述](#概述)\n2. [架构设计](#架构设计)\n3. [模块化结构](#模块化结构)\n4. [配置管理](#配置管理)\n5. [业务集成](#业务集成)\n6. [定时同步](#定时同步)\n7. [最佳实践](#最佳实践)\n8. [故障排除](#故障排除)\n\n## 概述\n\nExtLike 点赞服务是一个高性能、可扩展的点赞功能解决方案，支持Redis和MongoDB双写模式，提供完整的点赞、取消点赞、批量操作、热门排行等功能。\n\n### 核心特性\n\n- ✅ **模块化设计**: 单一职责原则，功能分离\n- ✅ **配置灵活**: 支持系统配置和自定义配置\n- ✅ **高性能**: Redis v2 高级数据结构优化\n- ✅ **可扩展**: 支持多种存储策略\n- ✅ **松耦合**: 通过混入模式集成到业务服务\n- ✅ **定时同步**: 支持缓存数据定时同步到数据库\n\n## 架构设计\n\n### 总体架构图\n\n```\n┌─────────────────────────────────────────────────────────────┐\n│                    业务服务层                                │\n├─────────────────┬─────────────────┬─────────────────────────┤\n│  短视频评论服务   │    帖子服务     │      其他业务服务        │\n├─────────────────┼─────────────────┼─────────────────────────┤\n│           点赞功能混入 (LikeMixin)                          │\n├─────────────────────────────────────────────────────────────┤\n│                ExtLike 点赞服务                             │\n├─────────────────┬─────────────────┬─────────────────────────┤\n│   配置管理器     │    服务工厂     │       定时同步          │\n├─────────────────┼─────────────────┼─────────────────────────┤\n│  Redis适配器     │  MongoDB适配器  │      策略管理器         │\n├─────────────────┼─────────────────┼─────────────────────────┤\n│     Redis       │    MongoDB      │       MySQL             │\n└─────────────────┴─────────────────┴─────────────────────────┘\n```\n\n### 模块依赖关系\n\n```\n业务服务 → LikeMixin → ExtLike服务 → 适配器 → 存储层\n                ↓\n            配置管理器\n                ↓\n            系统配置\n```\n\n## 模块化结构\n\n### 文件组织结构\n\n```\nfrontapi/internal/service/base/extlike/\n├── interfaces.go           # 核心接口定义\n├── config.go              # 配置结构定义\n├── config_manager.go      # 配置管理器\n├── service.go             # 主服务实现\n├── factory.go             # 服务工厂和建造者\n├── types/\n│   └── types.go           # 类型定义\n├── redis/v2/              # Redis v2 适配器（模块化）\n│   ├── adapter_main.go    # 主适配器\n│   ├── redis_client.go    # Redis客户端包装\n│   ├── like_operations.go # 点赞操作\n│   ├── query_operations.go# 查询操作\n│   ├── ranking_operations.go # 排行榜操作\n│   └── stats.go           # 统计功能\n├── mongodb/\n│   └── adapter.go         # MongoDB适配器\n└── sync/                  # 定时同步模块\n    └── scheduler.go       # 任务调度器\n\nfrontapi/internal/service/base/\n└── like_mixin.go          # 点赞功能混入\n\nfrontapi/examples/\n└── shortvideo_comment_with_like_integration.go # 集成示例\n```\n\n### 模块职责分离\n\n#### 1. Redis适配器模块化\n\n**原单一文件问题**:\n- `adapter.go` 文件过于臃肿（800+行）\n- 违反单一职责原则\n- 难以维护和测试\n\n**解决方案**:\n```go\n// redis_client.go - Redis客户端管理\ntype RedisClient struct {\n    client    redis.UniversalClient\n    config    *extlike.RedisConfig\n    keyPrefix string\n}\n\n// like_operations.go - 点赞操作\ntype LikeOperations struct {\n    client *RedisClient\n    stats  *AdapterStats\n}\n\n// query_operations.go - 查询操作\ntype QueryOperations struct {\n    client *RedisClient\n    stats  *AdapterStats\n}\n\n// ranking_operations.go - 排行榜操作\ntype RankingOperations struct {\n    client *RedisClient\n    stats  *AdapterStats\n}\n```\n\n#### 2. 主适配器组合模式\n\n```go\n// adapter_main.go\ntype RedisV2Adapter struct {\n    client         *RedisClient\n    likeOps        *LikeOperations\n    queryOps       *QueryOperations\n    rankingOps     *RankingOperations\n    statsOps       *StatsOperations\n    stats          *AdapterStats\n}\n\n// 接口方法委托给具体操作模块\nfunc (r *RedisV2Adapter) Like(ctx context.Context, userID, itemID, itemType string) error {\n    return r.likeOps.Like(ctx, userID, itemID, itemType)\n}\n```\n\n## 配置管理\n\n### 配置层次结构\n\n```\n系统配置 (pkg/redis, pkg/mongodb)\n     ↓\n点赞服务配置 (LikeServiceConfig)\n     ↓\n具体适配器配置 (RedisConfig, MongoConfig)\n```\n\n### 配置优先级\n\n1. **自定义点赞配置** (最高优先级)\n2. **系统Redis/MongoDB配置** (默认)\n3. **默认配置** (fallback)\n\n### 配置使用示例\n\n```go\n// 1. 使用系统配置（默认）\nlikeMixin, err := base.NewLikeMixinFromType(\"shortvideo_comment\")\n\n// 2. 使用自定义Redis配置\ncustomRedisConfig := &extlike.RedisConfig{\n    Enabled:   true,\n    UseCustom: true,\n    Host:      \"like-redis.example.com\",\n    Port:      6379,\n    Password:  \"like-redis-password\",\n    DB:        2,\n    KeyPrefix: \"shortvideo_comment_like:\",\n    LikeTTL:   24 * time.Hour,\n    CountTTL:  1 * time.Hour,\n}\n\nlikeMixin, err := base.NewLikeMixinBuilder(\"shortvideo_comment\").\n    WithRedisConfig(customRedisConfig).\n    Build()\n\n// 3. 完整的自定义配置\ncustomConfig := &extlike.LikeServiceConfig{\n    RedisConfig: customRedisConfig,\n    ServiceConfig: &extlike.ServiceConfig{\n        ServiceName: \"shortvideo-comment-like\",\n        Debug:       true,\n    },\n    PerformanceConfig: &extlike.PerformanceConfig{\n        BatchSize:        50,\n        SyncInterval:     2 * time.Minute,\n        EnablePipelining: true,\n        CircuitBreaker:   true,\n    },\n}\n\nlikeMixin, err := base.NewLikeMixinBuilder(\"shortvideo_comment\").\n    WithCustomConfig(customConfig).\n    Build()\n```\n\n## 业务集成\n\n### 集成模式选择\n\n#### 1. 混入模式（推荐）\n\n**优点**:\n- 松耦合\n- 不影响原有业务代码\n- 功能独立\n- 易于测试\n\n**使用方式**:\n```go\n// 定义集成服务接口\ntype ShortVideoCommentServiceWithLike interface {\n    extlikeService.ShortVideoCommentService  // 原有接口\n    base.ILikeMixin                         // 点赞功能\n}\n\n// 实现结构体\ntype shortVideoCommentServiceWithLike struct {\n    extlikeService.ShortVideoCommentService  // 嵌入原有服务\n    base.ILikeMixin                         // 嵌入点赞功能\n}\n```\n\n#### 2. 组合模式\n\n```go\ntype CommentService struct {\n    baseService    CommentBaseService\n    likeService    base.ILikeMixin\n    // 其他功能服务...\n}\n\nfunc (s *CommentService) GetCommentWithLikeInfo(ctx context.Context, commentID, userID string) (*CommentWithLikeInfo, error) {\n    // 获取评论基本信息\n    comment, err := s.baseService.GetByID(ctx, commentID)\n    if err != nil {\n        return nil, err\n    }\n    \n    // 获取点赞信息\n    isLiked, err := s.likeService.IsLiked(ctx, userID, commentID)\n    if err != nil {\n        return nil, err\n    }\n    \n    likeCount, err := s.likeService.GetLikeCount(ctx, commentID)\n    if err != nil {\n        return nil, err\n    }\n    \n    return &CommentWithLikeInfo{\n        Comment:   comment,\n        IsLiked:   isLiked,\n        LikeCount: likeCount,\n    }, nil\n}\n```\n\n### 业务方法扩展\n\n```go\n// 获取增强的评论信息（包含点赞数据）\nfunc (s *shortVideoCommentServiceWithLike) GetCommentWithEnhancedLikeInfo(\n    ctx context.Context, \n    commentID, userID string,\n) (*CommentWithLikeInfo, error) {\n    // 获取评论基本信息\n    comment, err := s.GetByID(ctx, commentID, true)\n    if err != nil {\n        return nil, fmt.Errorf(\"获取评论失败: %w\", err)\n    }\n\n    // 使用点赞混入获取点赞信息\n    isLiked, err := s.IsLiked(ctx, userID, commentID)\n    if err != nil {\n        return nil, fmt.Errorf(\"获取点赞状态失败: %w\", err)\n    }\n\n    likeCount, err := s.GetLikeCount(ctx, commentID)\n    if err != nil {\n        return nil, fmt.Errorf(\"获取点赞数量失败: %w\", err)\n    }\n\n    return &CommentWithLikeInfo{\n        ShortVideoComment: comment,\n        IsLiked:           isLiked,\n        LikeCount:         likeCount,\n    }, nil\n}\n\n// 批量获取增强的评论信息\nfunc (s *shortVideoCommentServiceWithLike) BatchGetCommentsWithEnhancedLikeInfo(\n    ctx context.Context, \n    commentIDs []string, \n    userID string,\n) ([]*CommentWithLikeInfo, error) {\n    if len(commentIDs) == 0 {\n        return []*CommentWithLikeInfo{}, nil\n    }\n\n    // 批量获取评论基本信息\n    comments, err := s.FindByIDs(ctx, commentIDs)\n    if err != nil {\n        return nil, fmt.Errorf(\"批量获取评论失败: %w\", err)\n    }\n\n    // 使用点赞混入批量获取点赞信息\n    likeStatuses, err := s.BatchGetLikeStatus(ctx, userID, commentIDs)\n    if err != nil {\n        return nil, fmt.Errorf(\"批量获取点赞状态失败: %w\", err)\n    }\n\n    likeCounts, err := s.BatchGetLikeCounts(ctx, commentIDs)\n    if err != nil {\n        return nil, fmt.Errorf(\"批量获取点赞数量失败: %w\", err)\n    }\n\n    // 组装结果\n    result := make([]*CommentWithLikeInfo, len(comments))\n    for i, comment := range comments {\n        commentID := comment.ID\n        result[i] = &CommentWithLikeInfo{\n            ShortVideoComment: comment,\n            IsLiked:           likeStatuses[commentID],\n            LikeCount:         likeCounts[commentID],\n        }\n    }\n\n    return result, nil\n}\n```\n\n## 定时同步\n\n### 同步架构\n\n```\n缓存层 (Redis) → 同步调度器 → 数据库层 (MySQL)\n     ↓              ↓              ↓\n  实时操作        定时任务        持久化存储\n```\n\n### 同步管理器使用\n\n```go\n// 1. 创建同步管理器\nsyncConfig := &sync.SyncConfig{\n    Enabled:         true,\n    BatchSize:       100,\n    DefaultInterval: 5 * time.Minute,\n    ItemTypes: map[string]*sync.ItemTypeSyncConfig{\n        \"shortvideo_comment\": {\n            Enabled:   true,\n            Interval:  5 * time.Minute,\n            BatchSize: 100,\n        },\n        \"shortvideo\": {\n            Enabled:   true,\n            Interval:  10 * time.Minute,\n            BatchSize: 200,\n        },\n    },\n}\n\nsyncManager := sync.NewSyncManager(likeService, syncConfig)\n\n// 2. 初始化并启动\nif err := syncManager.Initialize(); err != nil {\n    log.Fatalf(\"初始化同步管理器失败: %v\", err)\n}\n\nctx, cancel := context.WithCancel(context.Background())\ndefer cancel()\n\n// 启动同步管理器（会阻塞直到停止）\ngo func() {\n    if err := syncManager.Start(ctx); err != nil {\n        log.Printf(\"同步管理器启动失败: %v\", err)\n    }\n}()\n\n// 3. 运行时管理\n// 获取状态\nstatus := syncManager.GetStatus()\nfmt.Printf(\"同步器状态: %+v\\n\", status)\n\n// 禁用/启用任务\nsyncManager.DisableTask(\"sync_shortvideo_comment\")\nsyncManager.EnableTask(\"sync_shortvideo_comment\")\n\n// 立即执行任务\nsyncManager.ExecuteTaskNow(ctx, \"sync_shortvideo_comment\")\n```\n\n### 自定义同步任务\n\n```go\n// 自定义同步任务实现\ntype CustomSyncTask struct {\n    name         string\n    interval     time.Duration\n    enabled      bool\n    businessRepo BusinessRepository\n    likeService  extlike.ExtendedLikeService\n}\n\nfunc (t *CustomSyncTask) Execute(ctx context.Context) error {\n    fmt.Printf(\"[%s] 开始自定义同步任务...\\n\", t.name)\n    \n    // 1. 从缓存获取变更数据\n    // 2. 批量同步到数据库\n    // 3. 清理已同步缓存\n    \n    return nil\n}\n\n// 添加到调度器\nscheduler := sync.NewScheduler()\ncustomTask := &CustomSyncTask{\n    name:     \"custom_sync_task\",\n    interval: 3 * time.Minute,\n    enabled:  true,\n    // ... 其他字段\n}\nscheduler.AddTask(customTask)\n```\n\n## 最佳实践\n\n### 1. 配置管理最佳实践\n\n```go\n// ✅ 推荐：环境特定配置\nfunc createProductionLikeService() (base.ILikeMixin, error) {\n    redisConfig := &extlike.RedisConfig{\n        Enabled:   true,\n        UseCustom: true,\n        Host:      os.Getenv(\"LIKE_REDIS_HOST\"),\n        Port:      6379,\n        Password:  os.Getenv(\"LIKE_REDIS_PASSWORD\"),\n        DB:        2, // 专用数据库\n        KeyPrefix: \"prod_like:\",\n        \n        // 生产环境优化配置\n        PoolSize:     50,\n        MinIdleConns: 10,\n        LikeTTL:      7 * 24 * time.Hour,\n        CountTTL:     1 * time.Hour,\n    }\n    \n    perfConfig := &extlike.PerformanceConfig{\n        BatchSize:        200,\n        SyncInterval:     10 * time.Minute,\n        EnablePipelining: true,\n        CircuitBreaker:   true,\n        RateLimiting:     true,\n        RateLimit:        1000,\n    }\n    \n    return base.NewLikeMixinBuilder(\"shortvideo_comment\").\n        WithRedisConfig(redisConfig).\n        WithPerformanceConfig(perfConfig).\n        Build()\n}\n```\n\n### 2. 错误处理最佳实践\n\n```go\n// ✅ 推荐：优雅的错误处理\nfunc (s *CommentService) LikeComment(ctx context.Context, userID, commentID string) error {\n    // 1. 参数验证\n    if userID == \"\" || commentID == \"\" {\n        return fmt.Errorf(\"用户ID和评论ID不能为空\")\n    }\n    \n    // 2. 业务规则验证\n    if exists, err := s.baseService.Exists(ctx, commentID); err != nil {\n        return fmt.Errorf(\"验证评论存在性失败: %w\", err)\n    } else if !exists {\n        return fmt.Errorf(\"评论不存在\")\n    }\n    \n    // 3. 执行点赞操作\n    if err := s.likeService.Like(ctx, userID, commentID); err != nil {\n        // 根据错误类型决定是否重试\n        if isRetryableError(err) {\n            return s.retryLike(ctx, userID, commentID, 3)\n        }\n        return fmt.Errorf(\"点赞操作失败: %w\", err)\n    }\n    \n    // 4. 可选：发送事件通知\n    s.eventBus.Publish(\"comment.liked\", &CommentLikedEvent{\n        UserID:    userID,\n        CommentID: commentID,\n        Timestamp: time.Now(),\n    })\n    \n    return nil\n}\n```\n\n### 3. 性能优化最佳实践\n\n```go\n// ✅ 推荐：批量操作优化\nfunc (s *CommentService) GetCommentsPageWithLikeInfo(\n    ctx context.Context, \n    userID string, \n    page, pageSize int,\n) (*CommentsPageResult, error) {\n    // 1. 获取评论列表\n    comments, total, err := s.baseService.GetCommentsPage(ctx, page, pageSize)\n    if err != nil {\n        return nil, err\n    }\n    \n    if len(comments) == 0 {\n        return &CommentsPageResult{Comments: []*CommentWithLikeInfo{}, Total: total}, nil\n    }\n    \n    // 2. 提取评论ID列表\n    commentIDs := make([]string, len(comments))\n    for i, comment := range comments {\n        commentIDs[i] = comment.ID\n    }\n    \n    // 3. 批量获取点赞信息（性能优化关键）\n    var likeStatuses map[string]bool\n    var likeCounts map[string]int64\n    \n    // 并发获取点赞状态和数量\n    g, ctx := errgroup.WithContext(ctx)\n    \n    g.Go(func() error {\n        var err error\n        likeStatuses, err = s.likeService.BatchGetLikeStatus(ctx, userID, commentIDs)\n        return err\n    })\n    \n    g.Go(func() error {\n        var err error\n        likeCounts, err = s.likeService.BatchGetLikeCounts(ctx, commentIDs)\n        return err\n    })\n    \n    if err := g.Wait(); err != nil {\n        return nil, fmt.Errorf(\"批量获取点赞信息失败: %w\", err)\n    }\n    \n    // 4. 组装结果\n    result := make([]*CommentWithLikeInfo, len(comments))\n    for i, comment := range comments {\n        result[i] = &CommentWithLikeInfo{\n            Comment:   comment,\n            IsLiked:   likeStatuses[comment.ID],\n            LikeCount: likeCounts[comment.ID],\n        }\n    }\n    \n    return &CommentsPageResult{\n        Comments: result,\n        Total:    total,\n        Page:     page,\n        PageSize:  pageSize,\n    }, nil\n}\n```\n\n### 4. 监控和日志最佳实践\n\n```go\n// ✅ 推荐：结构化日志和指标\nfunc (s *CommentService) LikeCommentWithMetrics(ctx context.Context, userID, commentID string) error {\n    start := time.Now()\n    \n    // 结构化日志\n    logger := s.logger.With(\n        \"operation\", \"like_comment\",\n        \"user_id\", userID,\n        \"comment_id\", commentID,\n    )\n    \n    logger.Info(\"开始点赞操作\")\n    \n    err := s.LikeComment(ctx, userID, commentID)\n    \n    // 记录指标\n    duration := time.Since(start)\n    s.metrics.RecordLikeOperation(duration, err == nil)\n    \n    if err != nil {\n        logger.Error(\"点赞操作失败\", \"error\", err)\n        return err\n    }\n    \n    logger.Info(\"点赞操作成功\", \"duration\", duration)\n    return nil\n}\n```\n\n## 故障排除\n\n### 常见问题及解决方案\n\n#### 1. 配置问题\n\n**问题**: `获取Redis客户端失败: 系统Redis客户端未初始化`\n\n**解决方案**:\n```go\n// 确保系统Redis已初始化\nif redisClient.RDB == nil {\n    // 初始化系统Redis\n    redisClient.Init()\n}\n\n// 或者使用自定义Redis配置\ncustomConfig := &extlike.RedisConfig{\n    UseCustom: true,\n    Host:      \"localhost\",\n    Port:      6379,\n    // ... 其他配置\n}\n```\n\n#### 2. 性能问题\n\n**问题**: 点赞操作响应慢\n\n**解决方案**:\n```go\n// 1. 启用Pipeline\nperfConfig := &extlike.PerformanceConfig{\n    EnablePipelining: true,\n    PipelineSize:     100,\n}\n\n// 2. 增加连接池大小\nredisConfig := &extlike.RedisConfig{\n    PoolSize:     50,\n    MinIdleConns: 10,\n}\n\n// 3. 使用批量操作\n// 避免：\nfor _, commentID := range commentIDs {\n    service.Like(ctx, userID, commentID)\n}\n\n// 推荐：\nservice.BatchLike(ctx, userID, commentIDs)\n```\n\n#### 3. 同步问题\n\n**问题**: 定时同步任务不执行\n\n**解决方案**:\n```go\n// 1. 检查任务状态\nstatus := syncManager.GetStatus()\nfor name, taskStatus := range status.TaskStatus {\n    if !taskStatus.Enabled {\n        fmt.Printf(\"任务 %s 未启用\\n\", name)\n        syncManager.EnableTask(name)\n    }\n}\n\n// 2. 检查配置\nsyncConfig := &sync.SyncConfig{\n    Enabled: true, // 确保全局启用\n    ItemTypes: map[string]*sync.ItemTypeSyncConfig{\n        \"shortvideo_comment\": {\n            Enabled:  true, // 确保特定类型启用\n            Interval: 5 * time.Minute,\n        },\n    },\n}\n\n// 3. 手动执行测试\nerr := syncManager.ExecuteTaskNow(ctx, \"sync_shortvideo_comment\")\nif err != nil {\n    fmt.Printf(\"手动执行失败: %v\\n\", err)\n}\n```\n\n#### 4. 内存泄漏\n\n**问题**: 内存使用持续增长\n\n**解决方案**:\n```go\n// 1. 设置合理的TTL\nredisConfig := &extlike.RedisConfig{\n    LikeTTL:    7 * 24 * time.Hour,  // 点赞记录TTL\n    CountTTL:   1 * time.Hour,       // 计数缓存TTL\n    RankingTTL: 30 * time.Minute,    // 排行榜TTL\n}\n\n// 2. 定期清理过期数据\ngo func() {\n    ticker := time.NewTicker(1 * time.Hour)\n    defer ticker.Stop()\n    \n    for {\n        select {\n        case <-ticker.C:\n            adapter.CleanupExpiredData(ctx, \"shortvideo_comment\", time.Now().Add(-24*time.Hour))\n        case <-ctx.Done():\n            return\n        }\n    }\n}()\n\n// 3. 监控内存使用\nstats, _ := adapter.GetCacheStats(ctx)\nfmt.Printf(\"缓存统计: %+v\\n\", stats)\n```\n\n### 调试技巧\n\n#### 1. 启用调试日志\n\n```go\nserviceConfig := &extlike.ServiceConfig{\n    Debug:    true,\n    LogLevel: \"debug\",\n}\n```\n\n#### 2. 检查Redis键\n\n```bash\n# 连接Redis\nredis-cli\n\n# 查看所有点赞相关键\nKEYS \"like:*\"\n\n# 查看特定键的类型和内容\nTYPE \"like:shortvideo_comment:comment123\"\nSMEMBERS \"like:shortvideo_comment:comment123\"\n\n# 查看点赞计数\nGET \"count:shortvideo_comment:comment123\"\n\n# 查看排行榜\nZRANGE \"hot:shortvideo_comment\" 0 -1 WITHSCORES\n```\n\n#### 3. 性能分析\n\n```go\n// 添加性能分析\nimport _ \"net/http/pprof\"\n\ngo func() {\n    log.Println(http.ListenAndServe(\"localhost:6060\", nil))\n}()\n\n// 在浏览器中访问 http://localhost:6060/debug/pprof/\n```\n\n## 结论\n\n通过模块化设计、灵活的配置管理和松耦合的集成方式，ExtLike 点赞服务提供了高性能、可扩展的点赞功能解决方案。遵循本指南的最佳实践，可以确保服务的稳定运行和良好的性能表现。\n\n关键要点：\n\n1. **模块化**: 遵循单一职责原则，便于维护和扩展\n2. **配置灵活**: 支持多层次配置，适应不同环境需求\n3. **松耦合**: 通过混入模式实现业务集成，不影响原有代码\n4. **性能优化**: 使用批量操作、Pipeline、连接池等技术\n5. **监控完善**: 结构化日志、指标收集、健康检查\n6. **故障处理**: 优雅的错误处理和恢复机制 