package permission

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
	"strconv"
)

// AdminUser 管理员用户模型
type AdminUser struct {
	models.IntBaseModelStruct                 // 继承int类型基础模型，包含ID、Status、CreatedAt、UpdatedAt
	Username                  string          `json:"username" gorm:"column:username;type:varchar(50);not null;uniqueIndex;comment:用户名"`
	Password                  string          `json:"password" gorm:"column:password;type:varchar(255);not null;comment:密码"`
	Nickname                  string          `json:"nickname" gorm:"column:nickname;type:varchar(100);not null;comment:昵称"`
	Avatar                    string          `json:"avatar" gorm:"column:avatar;type:varchar(500);comment:头像URL"`
	Email                     string          `json:"email" gorm:"column:email;type:varchar(100);comment:邮箱"`
	Phone                     string          `json:"phone" gorm:"column:phone;type:varchar(20);comment:手机号"`
	Remark                    string          `json:"remark" gorm:"column:remark;type:varchar(500);comment:备注"`
	Psalt                     string          `json:"psalt" gorm:"column:psalt;type:varchar(32);not null;comment:密码盐"`
	IsSuperAdmin              int8            `json:"is_super_admin" gorm:"column:is_super_admin;default:0;comment:是否超级管理员(0否,1是)"`
	DeptID                    int             `json:"dept_id" gorm:"column:dept_id;default:0;comment:部门ID"`
	LastLoginAt               *types.JSONTime `json:"last_login_at" gorm:"column:last_login_at;comment:最后登录时间"`
	LastLoginIP               string          `json:"last_login_ip" gorm:"column:last_login_ip;type:varchar(45);comment:最后登录IP"`

	// 关联字段 (不存储到数据库)
	Roles       []string `json:"roles" gorm:"-"`       // 用户角色列表
	Permissions []string `json:"permissions" gorm:"-"` // 用户权限列表
	RoleNames   []string `json:"role_names" gorm:"-"`  // 角色名称列表
}

// TableName 指定表名
func (AdminUser) TableName() string {
	return "ly_admin_sys_user"
}

// IsActive 检查用户是否激活
func (u *AdminUser) IsActive() bool {
	return u.Status == 1
}

// IsSuperAdminUser 检查是否为超级管理员
func (u *AdminUser) IsSuperAdminUser() bool {
	return u.IsSuperAdmin == 1
}

// GetUserIDString 获取用户ID的字符串格式
func (u *AdminUser) GetUserIDString() string {
	return strconv.Itoa(u.ID)
}

// SetPassword 设置密码(包含加盐)
func (u *AdminUser) SetPassword(password string) {
	u.Password = password
}

// SetLastLogin 更新登录信息
func (u *AdminUser) SetLastLogin(ip string, time *types.JSONTime) {
	u.LastLoginIP = ip
	u.LastLoginAt = time
}
