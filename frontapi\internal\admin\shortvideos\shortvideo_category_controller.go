package shortvideos

import (
	"frontapi/internal/admin"
	shortModel "frontapi/internal/models/shortvideos"
	"frontapi/internal/service/shortvideos"
	shortValidator "frontapi/internal/validation/shortvideos"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
	"github.com/guregu/null/v6"
)

// ShortVideoCategoryController 短视频控制器
type ShortVideoCategoryController struct {
	ShortVideoService         shortvideos.ShortVideoService
	ShortVideoCategoryService shortvideos.ShortVideoCategoryService
	admin.BaseController      // 继承BaseController
}

// NewShortVideoCategoryController 创建短视频控制器实例
func NewShortVideoCategoryController(
	shortVideoCategoryService shortvideos.ShortVideoCategoryService,
	shortVideoService shortvideos.ShortVideoService,
) *ShortVideoCategoryController {
	return &ShortVideoCategoryController{
		ShortVideoService:         shortVideoService,
		ShortVideoCategoryService: shortVideoCategoryService,
	}
}

// ListShortVideoCategories 获取短视频分类列表
func (c *ShortVideoCategoryController) ListShortVideoCategories(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	keyword := reqInfo.Get("keyword").GetString()
	createdAtStart := reqInfo.Get("created_at_start").GetString()
	createdAtEnd := reqInfo.Get("created_at_end").GetString()
	status := -999
	if err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 修复：使用具体的字段名而不是URI，避免SQL错误
	condition := map[string]interface{}{
		"name":             keyword, // 将keyword应用到name字段，而不是uri
		"status":           status,
		"created_at_start": createdAtStart,
		"created_at_end":   createdAtEnd,
	}

	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "updated_at desc"
	}

	// 查询短视频分类列表
	categoryList, total, err := c.ShortVideoCategoryService.List(ctx.Context(), condition, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取短视频分类列表失败: "+err.Error())
	}

	// 返回短视频分类列表
	return c.SuccessList(ctx, categoryList, total, page, pageSize)
}

// GetShortVideoCategory 获取短视频分类详情
func (c *ShortVideoCategoryController) GetShortVideoCategory(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询分类
	category, err := c.ShortVideoCategoryService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取短视频分类详情失败: "+err.Error())
	}

	if category == nil {
		return c.NotFound(ctx, "短视频分类不存在")
	}

	// 返回分类详情
	return c.Success(ctx, category)
}

// CreateShortVideoCategory 创建短视频分类
func (c *ShortVideoCategoryController) CreateShortVideoCategory(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req shortValidator.CreateCategoryRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 检查是否有相同名称的分类
	if req.Name != "" {
		existingCategory, err := c.ShortVideoCategoryService.FindOne(ctx.Context(), "name = ?", req.Name)
		if err != nil {
			return c.InternalServerError(ctx, "检查名称重复失败: "+err.Error())
		}
		if existingCategory != nil {
			return c.BadRequest(ctx, "已有相同名称的分类", nil)
		}
	}

	// 验证parent_id是否存在（如果不为空）
	if req.ParentID != "" {
		// 检查父分类是否存在于短视频分类表中
		parentCategory, err := c.ShortVideoCategoryService.GetByID(ctx.Context(), req.ParentID, false)
		if err != nil || parentCategory == nil {
			return c.BadRequest(ctx, "指定的父分类不存在", nil)
		}
	}

	var category shortModel.ShortVideoCategory
	utils.SmartCopy(req, &category)

	// 创建分类
	id, err := c.ShortVideoCategoryService.Create(ctx.Context(), &category)
	if err != nil {
		return c.InternalServerError(ctx, "创建短视频分类失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建短视频分类成功",
	})
}

// UpdateShortVideoCategory 更新短视频分类
func (c *ShortVideoCategoryController) UpdateShortVideoCategory(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req shortValidator.UpdateCategoryRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 先查询现有的分类信息
	category, err := c.ShortVideoCategoryService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.NotFound(ctx, "短视频分类不存在")
	}

	// 检查是否有相同名称的分类（排除当前分类）
	if req.Name != nil && *req.Name != "" && *req.Name != category.Name.String {
		existingCategory, err := c.ShortVideoCategoryService.FindOne(ctx.Context(), "id != ? AND name = ?", id, *req.Name)
		if err != nil {
			return c.InternalServerError(ctx, "检查名称重复失败: "+err.Error())
		}
		if existingCategory != nil {
			return c.BadRequest(ctx, "已有相同名称的分类", nil)
		}
	}
	// 使用SmartCopy进行基础拷贝，保留现有的统计数据
	utils.SmartCopy(req, category)
	// 验证parent_id是否存在（如果不为空）
	if req.ParentID != nil && *req.ParentID != "" {
		// 检查父分类是否存在
		if *req.ParentID != id { // 不能选择自己作为父分类
			parentCategory, err := c.ShortVideoCategoryService.GetByID(ctx.Context(), *req.ParentID, false)
			if err != nil || parentCategory == nil {
				return c.BadRequest(ctx, "指定的父分类不存在", nil)
			}

			// 检查是否会造成循环引用（父分类不能是当前分类的子分类）
			if c.isChildCategory(ctx, *req.ParentID, id) {
				return c.BadRequest(ctx, "不能选择子分类作为父分类，会造成循环引用", nil)
			}
		} else {
			return c.BadRequest(ctx, "分类不能选择自己作为父分类", nil)
		}
		category.ParentID = null.StringFrom(*req.ParentID)
	} else {
		category.ParentID = null.String{}
	}

	// 手动处理指针类型字段的转换
	if req.Name != nil {
		category.SetName(*req.Name)
	}
	if req.Description != nil {
		category.SetDescription(*req.Description)
	}
	if req.ParentID != nil {
		// 对于ShortVideoCategory，ParentID是null.String类型
		if *req.ParentID == "" {
			// 当前端传递空字符串时，设置为null
			category.ParentID = null.String{}
		} else {
			// 验证parent_id是否在短视频分类表中存在
			parentExists := false
			categories, _, err := c.ShortVideoCategoryService.List(ctx.Context(), map[string]interface{}{}, "id", 1, 1000, false)
			if err == nil {
				for _, cat := range categories {
					if cat.ID == *req.ParentID {
						parentExists = true
						break
					}
				}
			}

			if parentExists {
				category.ParentID = null.StringFrom(*req.ParentID)
			} else {
				// 如果父分类不存在，清空parent_id
				category.ParentID = null.String{}
			}
		}
	}

	// 更新分类
	err = c.ShortVideoCategoryService.Update(ctx.Context(), category)
	if err != nil {
		return c.InternalServerError(ctx, "更新短视频分类失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新短视频分类成功")
}
func (c *ShortVideoCategoryController) UpdateShortVideoCategoryStatus(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req shortValidator.UpdateCategoryRequestStatus
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), req)
	}
	// 更新分类
	err = c.ShortVideoCategoryService.UpdateStatus(ctx.Context(), id, int(req.Status))
	if err != nil {
		return c.InternalServerError(ctx, "更新短视频分类失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新短视频分类成功")
}

// DeleteShortVideoCategory 删除短视频分类
func (c *ShortVideoCategoryController) DeleteShortVideoCategory(ctx *fiber.Ctx) error {
	// 获取分类ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除分类
	err = c.ShortVideoCategoryService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除短视频分类失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除短视频分类成功")
}

// isChildCategory 检查targetID是否是currentID的子分类（递归检查）
func (c *ShortVideoCategoryController) isChildCategory(ctx *fiber.Ctx, targetID, currentID string) bool {
	// 查询所有子分类
	categories, _, err := c.ShortVideoCategoryService.List(ctx.Context(), map[string]interface{}{}, "id", 1, 1000, false)
	if err != nil {
		return false
	}

	// 递归检查
	var checkChild func(parentID string) bool
	checkChild = func(parentID string) bool {
		for _, category := range categories {
			if category.ParentID.String == parentID {
				if category.ID == targetID {
					return true
				}
				// 递归检查子分类
				if checkChild(category.ID) {
					return true
				}
			}
		}
		return false
	}

	return checkChild(currentID)
}

// BatchUpdateShortVideoCategoryStatus 批量更新短视频分类状态
func (c *ShortVideoCategoryController) BatchUpdateShortVideoCategoryStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Ids    []string `json:"ids" validate:"required"`
		Status int      `json:"status" validate:"int|min:-5|max:5"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量更新状态
	err := c.ShortVideoCategoryService.BatchUpdateStatus(ctx.Context(), req.Ids, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "批量更新短视频分类状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量更新短视频分类状态成功")
}

// BatchDeleteShortVideoCategory 批量删除短视频分类
func (c *ShortVideoCategoryController) BatchDeleteShortVideoCategory(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		Ids []string `json:"ids" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量删除分类
	if _, err := c.ShortVideoCategoryService.BatchDelete(ctx.Context(), req.Ids); err != nil {
		return c.InternalServerError(ctx, "批量删除短视频分类失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "批量删除短视频分类成功")
}
