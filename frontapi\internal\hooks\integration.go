package hooks

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"frontapi/internal/hooks/common"
)

// ServiceIntegrator 服务集成器
// 用于将新的hooks系统集成到现有的BaseService中
type ServiceIntegrator struct {
	db *gorm.DB
}

// NewServiceIntegrator 创建服务集成器
func NewServiceIntegrator(db *gorm.DB) *ServiceIntegrator {
	return &ServiceIntegrator{db: db}
}

// IntegrateHooks 为服务集成hooks系统
// entityType: 实体类型名称，如 "book", "category", "post" 等
// tableName: 数据库表名
// setupFunc: 钩子设置函数
func (si *ServiceIntegrator) IntegrateHooks(entityType, tableName string, setupFunc func(*ServiceHookManager)) *ServiceHookManager {
	hookManager := NewServiceHookManager(si.db, entityType)
	
	// 执行自定义设置
	if setupFunc != nil {
		setupFunc(hookManager)
	}
	
	return hookManager
}

// CommonHooksSetup 通用钩子设置
// 为大多数实体提供标准的钩子配置
type CommonHooksSetup struct {
	TableName        string                 // 表名
	DuplicateFields  []string               // 重复检查字段
	TrimFields       []string               // 需要去除空格的字段
	LowerFields      []string               // 需要转小写的字段
	UpperFields      []string               // 需要转大写的字段
	DefaultValues    map[string]interface{} // 默认值
	ValidationRules  map[string][]common.ValidationRule // 验证规则
	EnableAudit      bool                   // 是否启用审计
	EnableTimestamp  bool                   // 是否启用时间戳
	UserID           string                 // 用户ID（用于审计）
	CreateTimeField  string                 // 创建时间字段名
	UpdateTimeField  string                 // 更新时间字段名
	CustomHooks      []CustomHookConfig     // 自定义钩子配置
}

// CustomHookConfig 自定义钩子配置
type CustomHookConfig struct {
	HookType    HookType
	Name        string
	Description string
	Priority    int
	HookFunc    HookFunc
}

// SetupCommonHooks 设置通用钩子
func (si *ServiceIntegrator) SetupCommonHooks(entityType string, config CommonHooksSetup) *ServiceHookManager {
	return si.IntegrateHooks(entityType, config.TableName, func(hookManager *ServiceHookManager) {
		builder := NewCreateHookBuilder(hookManager)
		
		// 数据清洗
		if len(config.TrimFields) > 0 || len(config.LowerFields) > 0 || len(config.UpperFields) > 0 || len(config.DefaultValues) > 0 {
			builder = builder.WithDataCleaning(config.TrimFields, config.LowerFields, config.UpperFields, config.DefaultValues)
		}
		
		// 数据验证
		if len(config.ValidationRules) > 0 {
			builder = builder.WithValidation(config.ValidationRules)
		}
		
		// 重复检查
		if len(config.DuplicateFields) > 0 {
			message := fmt.Sprintf("%s已存在", entityType)
			builder = builder.WithDuplicateCheck(config.TableName, config.DuplicateFields, message)
		}
		
		// 时间戳
		if config.EnableTimestamp {
			createField := config.CreateTimeField
			updateField := config.UpdateTimeField
			if createField == "" {
				createField = "CreatedAt"
			}
			if updateField == "" {
				updateField = "UpdatedAt"
			}
			builder = builder.WithTimestamp(createField, updateField)
		}
		
		// 审计
		if config.EnableAudit {
			userID := config.UserID
			if userID == "" {
				userID = "system"
			}
			builder = builder.WithAudit(config.TableName, userID)
		}
		
		// 自定义钩子
		for _, customHook := range config.CustomHooks {
			builder = builder.WithCustomHook(
				customHook.HookType,
				customHook.Name,
				customHook.Description,
				customHook.Priority,
				customHook.HookFunc,
			)
		}
		
		builder.Build()
	})
}

// BaseServiceHooksMixin 基础服务钩子混入
// 可以嵌入到现有的BaseService中
type BaseServiceHooksMixin struct {
	hookManager *ServiceHookManager
}

// InitHooksMixin 初始化钩子混入
func (m *BaseServiceHooksMixin) InitHooksMixin(hookManager *ServiceHookManager) {
	m.hookManager = hookManager
}

// ExecuteBeforeCreate 执行创建前钩子
func (m *BaseServiceHooksMixin) ExecuteBeforeCreate(ctx context.Context, entity interface{}) error {
	if m.hookManager != nil {
		return m.hookManager.ExecuteHooks(ctx, BeforeCreate, entity)
	}
	return nil
}

// ExecuteAfterCreate 执行创建后钩子
func (m *BaseServiceHooksMixin) ExecuteAfterCreate(ctx context.Context, entity interface{}) error {
	if m.hookManager != nil {
		return m.hookManager.ExecuteHooks(ctx, AfterCreate, entity)
	}
	return nil
}

// ExecuteBeforeUpdate 执行更新前钩子
func (m *BaseServiceHooksMixin) ExecuteBeforeUpdate(ctx context.Context, entity interface{}) error {
	if m.hookManager != nil {
		return m.hookManager.ExecuteHooks(ctx, BeforeUpdate, entity)
	}
	return nil
}

// ExecuteAfterUpdate 执行更新后钩子
func (m *BaseServiceHooksMixin) ExecuteAfterUpdate(ctx context.Context, entity interface{}) error {
	if m.hookManager != nil {
		return m.hookManager.ExecuteHooks(ctx, AfterUpdate, entity)
	}
	return nil
}

// ExecuteBeforeDelete 执行删除前钩子
func (m *BaseServiceHooksMixin) ExecuteBeforeDelete(ctx context.Context, entity interface{}) error {
	if m.hookManager != nil {
		return m.hookManager.ExecuteHooks(ctx, BeforeDelete, entity)
	}
	return nil
}

// ExecuteAfterDelete 执行删除后钩子
func (m *BaseServiceHooksMixin) ExecuteAfterDelete(ctx context.Context, entity interface{}) error {
	if m.hookManager != nil {
		return m.hookManager.ExecuteHooks(ctx, AfterDelete, entity)
	}
	return nil
}

// GetHookManager 获取钩子管理器
func (m *BaseServiceHooksMixin) GetHookManager() *ServiceHookManager {
	return m.hookManager
}

// 预定义的常用钩子配置

// CategoryHooksConfig 分类钩子配置
func CategoryHooksConfig() CommonHooksSetup {
	return CommonHooksSetup{
		TableName:       "categories",
		DuplicateFields: []string{"Name"},
		TrimFields:      []string{"Name", "Description"},
		LowerFields:     []string{"Name"},
		DefaultValues: map[string]interface{}{
			"Status": 1,
		},
		ValidationRules: map[string][]common.ValidationRule{
			"Name": {
				{Type: "required", Message: "分类名称不能为空"},
				{Type: "min_length", Value: 2, Message: "分类名称至少2个字符"},
				{Type: "max_length", Value: 50, Message: "分类名称不能超过50个字符"},
			},
			"Description": {
				{Type: "max_length", Value: 200, Message: "描述不能超过200个字符"},
			},
		},
		EnableAudit:     true,
		EnableTimestamp: true,
	}
}

// BookHooksConfig 电子书钩子配置
func BookHooksConfig() CommonHooksSetup {
	return CommonHooksSetup{
		TableName:       "books",
		DuplicateFields: []string{"ISBN"},
		TrimFields:      []string{"Title", "Author", "Description"},
		UpperFields:     []string{"ISBN"},
		DefaultValues: map[string]interface{}{
			"Status": 1,
		},
		ValidationRules: map[string][]common.ValidationRule{
			"Title": {
				{Type: "required", Message: "书名不能为空"},
				{Type: "min_length", Value: 1, Message: "书名至少1个字符"},
				{Type: "max_length", Value: 100, Message: "书名不能超过100个字符"},
			},
			"Author": {
				{Type: "required", Message: "作者不能为空"},
				{Type: "max_length", Value: 50, Message: "作者名称不能超过50个字符"},
			},
			"ISBN": {
				{Type: "regex", Value: `^[0-9]{10}([0-9]{3})?$`, Message: "ISBN格式不正确"},
			},
		},
		EnableAudit:     true,
		EnableTimestamp: true,
	}
}

// PostHooksConfig 帖子钩子配置
func PostHooksConfig() CommonHooksSetup {
	return CommonHooksSetup{
		TableName:   "posts",
		TrimFields:  []string{"Title", "Content", "Summary"},
		DefaultValues: map[string]interface{}{
			"Status": 1,
			"ViewCount": 0,
			"LikeCount": 0,
		},
		ValidationRules: map[string][]common.ValidationRule{
			"Title": {
				{Type: "required", Message: "标题不能为空"},
				{Type: "min_length", Value: 5, Message: "标题至少5个字符"},
				{Type: "max_length", Value: 100, Message: "标题不能超过100个字符"},
			},
			"Content": {
				{Type: "required", Message: "内容不能为空"},
				{Type: "min_length", Value: 10, Message: "内容至少10个字符"},
			},
		},
		EnableAudit:     true,
		EnableTimestamp: true,
	}
}

// UserHooksConfig 用户钩子配置
func UserHooksConfig() CommonHooksSetup {
	return CommonHooksSetup{
		TableName:       "users",
		DuplicateFields: []string{"Email", "Username"},
		TrimFields:      []string{"Username", "Email", "FirstName", "LastName"},
		LowerFields:     []string{"Email", "Username"},
		DefaultValues: map[string]interface{}{
			"Status": 1,
			"IsVerified": false,
		},
		ValidationRules: map[string][]common.ValidationRule{
			"Username": {
				{Type: "required", Message: "用户名不能为空"},
				{Type: "min_length", Value: 3, Message: "用户名至少3个字符"},
				{Type: "max_length", Value: 20, Message: "用户名不能超过20个字符"},
				{Type: "regex", Value: `^[a-zA-Z0-9_]+$`, Message: "用户名只能包含字母、数字和下划线"},
			},
			"Email": {
				{Type: "required", Message: "邮箱不能为空"},
				{Type: "regex", Value: `^[^@]+@[^@]+\.[^@]+$`, Message: "邮箱格式不正确"},
			},
		},
		EnableAudit:     true,
		EnableTimestamp: true,
	}
}