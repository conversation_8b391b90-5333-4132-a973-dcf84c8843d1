<template>
  <el-dialog
    title="视频详情"
    v-model="dialogVisible"
    @update:model-value="$emit('update:visible', $event)"
    width="60%"
    destroy-on-close
    class="video-detail-dialog"
  >
    <div v-if="videoData" class="video-detail-container">
      <!-- 顶部视频信息 -->
      <div class="video-header">
        <div class="video-title-section">
          <h2>{{ videoData.title }}</h2>
          <div class="video-meta">
            <span class="video-time">
              <el-icon><Calendar /></el-icon>
              {{ formatDate(videoData.created_at || '') }}
            </span>
            <el-tag v-if="videoData.status === 1" type="success">{{ $t('videos.status.published') }}</el-tag>
            <el-tag v-else-if="videoData.status === 0" type="info">{{ $t('videos.status.deleted') }}</el-tag>
            <el-tag v-else-if="videoData.status === -1" type="danger">{{ $t('videos.status.rejected') }}</el-tag>
          </div>
        </div>

        <!-- 视频预览 -->
        <div class="video-preview-container">
          <div class="video-thumbnail">
            <el-image 
              :src="videoData.cover || ''" 
              fit="cover" 
              class="video-cover-image"
              :preview-src-list="videoData.cover ? [videoData.cover] : []" 
            />
          </div>
          <div class="video-player" v-if="videoData.url">
            <video 
              :src="videoData.url" 
              controls 
              class="video-player-element"
              @loadedmetadata="onVideoLoaded"
              ref="videoRef"
              :style="videoPlayerStyle"
            ></video>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <el-tabs type="border-card" class="detail-tabs">
        <el-tab-pane label="基本信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="视频标题">{{ videoData.title }}</el-descriptions-item>
            <el-descriptions-item label="视频描述">{{ videoData.description || '无描述' }}</el-descriptions-item>
            <el-descriptions-item label="分类">{{ videoData.category_name || '未分类' }}</el-descriptions-item>
            <el-descriptions-item label="频道">{{ videoData.channel_name || videoData.channel_id || '无频道' }}</el-descriptions-item>
            <el-descriptions-item label="创作者">{{ videoData.creator_name || '未知作者' }}</el-descriptions-item>
            <el-descriptions-item label="主演">{{ (videoData as any).celebrities?.join('、') || '-' }}</el-descriptions-item>
            <el-descriptions-item label="标签">
              <div class="tags-container">
                <el-tag 
                  v-for="tag in videoData.tags" 
                  :key="tag" 
                  size="small"
                  type="info"
                  effect="plain"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                <span v-if="!videoData.tags || videoData.tags.length === 0" class="placeholder-text">无标签</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="上传时间">{{ formatDate(videoData.created_at || '') }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="技术参数">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="时长">{{ formatDuration(videoData.duration) }}</el-descriptions-item>
            <el-descriptions-item label="分辨率">{{ videoData.resolution || '-' }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ videoData.file_size ? `${Math.round(videoData.file_size / 1024 / 1024)}MB` : '-' }}</el-descriptions-item>
            <el-descriptions-item label="来源类型">{{ formatSourceType((videoData as any).src_type) }}</el-descriptions-item>
            <el-descriptions-item label="视频链接">
              <el-link type="primary" :href="videoData.url" target="_blank" :underline="false">{{ truncateUrl(videoData.url) }}</el-link>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        
        <el-tab-pane label="统计信息">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon"><el-icon><View /></el-icon></div>
              <div class="stat-value">{{ videoData.view_count || 0 }}</div>
              <div class="stat-label">播放</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><el-icon><Star /></el-icon></div>
              <div class="stat-value">{{ videoData.like_count || 0 }}</div>
              <div class="stat-label">点赞</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><el-icon><ChatDotRound /></el-icon></div>
              <div class="stat-value">{{ videoData.comment_count || 0 }}</div>
              <div class="stat-label">评论</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><el-icon><Collection /></el-icon></div>
              <div class="stat-value">{{ (videoData as any).favorite_count || 0 }}</div>
              <div class="stat-label">收藏</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><el-icon><Share /></el-icon></div>
              <div class="stat-value">{{ videoData.share_count || 0 }}</div>
              <div class="stat-label">分享</div>
            </div>
            <div class="stat-item">
              <div class="stat-icon"><el-icon><Opportunity /></el-icon></div>
              <div class="stat-value">{{ (videoData as any).score || 0 }}</div>
              <div class="stat-label">评分</div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <el-button @click="closeDialog">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { VideoItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import { Calendar, ChatDotRound, Collection, Opportunity, Share, Star, View } from '@element-plus/icons-vue';
import { computed, ref, watch } from 'vue';

// Props定义
interface Props {
  visible: boolean;
  videoData: VideoItem;
}

const props = defineProps<Props>();
const emit = defineEmits(['close', 'update:visible']);

// 对话框可见性状态
const dialogVisible = ref(props.visible);

// 监听props.visible变化，同步到dialogVisible
watch(() => props.visible, (val) => {
  dialogVisible.value = val;
});

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
  emit('close');
};

// 添加脚本部分
const videoRef = ref<HTMLVideoElement | null>(null);
const videoWidth = ref(0);
const videoHeight = ref(0);

// 视频加载完成事件处理
const onVideoLoaded = (e: Event) => {
  const video = e.target as HTMLVideoElement;
  if (video) {
    videoWidth.value = video.videoWidth;
    videoHeight.value = video.videoHeight;
    
    // 如果需要可以在这里对视频播放器进行自动播放设置
    // video.muted = true;
    // video.play();
  }
};

// 计算视频播放器样式
const videoPlayerStyle = computed(() => {
  // 如果没有宽高信息，返回默认样式
  if (!videoWidth.value || !videoHeight.value) {
    return {};
  }
  
  // 计算视频宽高比
  const aspectRatio = videoWidth.value / videoHeight.value;
  
  // 根据宽高比设置不同的样式
  if (aspectRatio > 1.7) {
    // 宽屏视频
    return {
      width: '100%',
      maxHeight: '500px'
    };
  } else if (aspectRatio < 0.8) {
    // 竖屏视频
    return {
      height: '400px',
      maxWidth: '320px',
      margin: '0 auto'
    };
  }
  
  // 标准视频
  return {
    width: '100%',
    maxHeight: '400px'
  };
});

// 格式化视频时长
const formatDuration = (seconds: number = 0) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;

  return [
    h > 0 ? h.toString().padStart(2, '0') : '',
    m.toString().padStart(2, '0'),
    s.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
};

// 截断URL显示
const truncateUrl = (url: string) => {
  if (!url) return '-';
  if (url.length > 50) {
    return url.substring(0, 47) + '...';
  }
  return url;
};

// 格式化来源类型
const formatSourceType = (srcType: number) => {
  switch(srcType) {
    case 1: return '用户上传';
    case 2: return '系统上传';
    case 3: return '系统采集';
    default: return '未知';
  }
};
</script>

<style scoped lang="scss">
.video-detail-dialog {
  .video-detail-container {
    padding: 0;
    
    .video-header {
      margin-bottom: 20px;
      
      .video-title-section {
        margin-bottom: 16px;
        
        h2 {
          margin: 0 0 8px 0;
          color: #303133;
          font-weight: 600;
        }
        
        .video-meta {
          display: flex;
          gap: 16px;
          align-items: center;
          color: #606266;
          font-size: 14px;
          
          .video-time {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }
      
      .video-preview-container {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
        
        .video-thumbnail {
          flex: 0 0 200px;
          
          .video-cover-image {
            width: 100%;
            height: 120px;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }
        }
        
        .video-player {
          flex: 1;
          min-width: 300px;
          
          .video-player-element {
            width: 100%;
            max-height: 300px;
            border-radius: 4px;
            background: #000;
          }
        }
      }
    }
    
    .detail-tabs {
      margin-top: 20px;
      
      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        
        .tag-item {
          margin-bottom: 5px;
        }
      }
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 20px;
        margin-top: 16px;
        
        .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          transition: all 0.3s;
          
          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }
          
          .stat-icon {
            font-size: 24px;
            color: var(--el-color-primary);
            margin-bottom: 8px;
          }
          
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: #606266;
          }
        }
      }
    }
  }
}

.placeholder-text {
  color: #909399;
  font-style: italic;
}

// 响应式设计
@media (max-width: 768px) {
  .video-detail-dialog {
    width: 95% !important;
    
    .video-header {
      .video-preview-container {
        flex-direction: column;
        
        .video-thumbnail,
        .video-player {
          width: 100%;
          flex-basis: auto;
        }
      }
    }
    
    .stats-grid {
      grid-template-columns: repeat(2, 1fr) !important;
    }
  }
}
</style> 