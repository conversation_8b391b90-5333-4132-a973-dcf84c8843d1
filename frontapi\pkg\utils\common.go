package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
)

// JWTClaims 自定义JWT声明结构
type JWTClaims struct {
	UserID   string `json:"userId"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// JWTSecret JWT密钥
var JWTSecret = []byte("your_jwt_secret_key") // 实际应用中应该从配置文件中读取

// InitJWTSecret 初始化JWT密钥
func InitJWTSecret(secret string) {
	if secret != "" {
		JWTSecret = []byte(secret)
	}
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID string, username string, expiration time.Duration) (string, error) {
	// 设置JWT声明
	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expiration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "frontapi",
			Subject:   userID,
			ID:        uuid.New().String(),
		},
	}

	// 创建令牌
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 使用密钥签名令牌并获得完整的编码令牌作为字符串
	return token.SignedString(JWTSecret)
}

// refreshToken
func RefreshToken(tokenString string) (string, error) {
	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return JWTSecret, nil
	})
	if err != nil {
		return "", err
	}
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		// 刷新令牌的过期时间
		claims.ExpiresAt = jwt.NewNumericDate(time.Now().Add(time.Hour * 24))
		// 重新签名并返回刷新后的令牌
		return jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString(JWTSecret)
	}
	return "", fmt.Errorf("invalid token")
}

// ParseToken 解析JWT令牌
func ParseToken(tokenString string) (*JWTClaims, error) {
	// 解析令牌
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return JWTSecret, nil
	})

	if err != nil {
		return nil, err
	}

	// 验证令牌
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("无效的令牌")
}

// GenerateRandomToken 生成随机令牌
func GenerateRandomToken() string {
	// 使用当前时间和随机数生成随机令牌
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(10000)
	timestamp := time.Now().Unix()
	uniqueID := uuid.New().String()

	// 组合成令牌并进行MD5哈希
	tokenStr := fmt.Sprintf("%d_%d_%s", timestamp, randomNum, uniqueID)
	hash := md5.New()
	hash.Write([]byte(tokenStr))
	return hex.EncodeToString(hash.Sum(nil))
}

// GenerateOrderNo 生成订单号
func GenerateOrderNo(prefix string) string {
	// 基于时间和随机数生成订单号
	now := time.Now()
	dateStr := now.Format("20060102150405")
	randNum := rand.Intn(1000)

	if prefix == "" {
		prefix = "ORD"
	}

	return strings.ToUpper(prefix) + dateStr + strconv.Itoa(randNum)
}

// ComparePasswords 比较密码是否匹配
func ComparePasswords(rawPassword, salt, encryptedPassword string) bool {
	// 使用MD5加密原始密码和盐值
	hash := md5.New()
	hash.Write([]byte(rawPassword + salt))
	calculatedPassword := hex.EncodeToString(hash.Sum(nil))

	return calculatedPassword == encryptedPassword
}
