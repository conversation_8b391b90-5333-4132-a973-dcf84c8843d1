package examples

import (
	"context"
	"fmt"
	"log"
	"time"

	"frontapi/internal/service/base/extlike/mongodb"
	"frontapi/internal/service/base/extlike/types"
)

// MongoDBExample MongoDB适配器使用示例
func MongoDBExample() {
	fmt.Println("=== MongoDB点赞适配器示例 ===")

	// 1. 创建MongoDB适配器
	adapter, err := mongodb.CreateAdapterFromURI(
		"mongodb://localhost:27017",
		"like_service_test",
		"videos", // 视频点赞
	)
	if err != nil {
		log.Fatalf("创建MongoDB适配器失败: %v", err)
	}
	defer adapter.Cleanup(context.Background())

	fmt.Println("✓ MongoDB适配器创建成功")

	ctx := context.Background()

	// 2. 基础点赞操作
	fmt.Println("\n--- 基础点赞操作 ---")

	userID := "user123"
	itemType := "video"
	itemID := "video456"

	// 点赞
	err = adapter.Like(ctx, userID, itemType, itemID)
	if err != nil {
		log.Printf("点赞失败: %v", err)
	} else {
		fmt.Printf("✓ 用户%s成功点赞%s:%s\n", userID, itemType, itemID)
	}

	// 检查点赞状态
	liked, err := adapter.IsLiked(ctx, userID, itemType, itemID)
	if err != nil {
		log.Printf("检查点赞状态失败: %v", err)
	} else {
		fmt.Printf("✓ 点赞状态: %v\n", liked)
	}

	// 获取点赞数
	count, err := adapter.GetLikeCount(ctx, itemType, itemID)
	if err != nil {
		log.Printf("获取点赞数失败: %v", err)
	} else {
		fmt.Printf("✓ 点赞数: %d\n", count)
	}

	// 3. 批量操作
	fmt.Println("\n--- 批量操作 ---")

	operations := []*types.LikeOperation{
		{
			UserID:    "user123",
			ItemID:    "video789",
			ItemType:  "video",
			Action:    "like",
			Timestamp: time.Now(),
		},
		{
			UserID:    "user456",
			ItemID:    "video456",
			ItemType:  "video",
			Action:    "like",
			Timestamp: time.Now(),
		},
	}

	err = adapter.BatchLike(ctx, operations)
	if err != nil {
		log.Printf("批量点赞失败: %v", err)
	} else {
		fmt.Printf("✓ 批量点赞成功: %d个操作\n", len(operations))
	}

	// 4. 查询操作
	fmt.Println("\n--- 查询操作 ---")

	// 批量查询点赞状态
	items := map[string]string{
		"video456": "video",
		"video789": "video",
	}

	statuses, err := adapter.BatchGetLikeStatus(ctx, "user123", items)
	if err != nil {
		log.Printf("批量查询点赞状态失败: %v", err)
	} else {
		fmt.Printf("✓ 点赞状态查询结果: %+v\n", statuses)
	}

	// 批量查询点赞数
	counts, err := adapter.BatchGetLikeCounts(ctx, items)
	if err != nil {
		log.Printf("批量查询点赞数失败: %v", err)
	} else {
		fmt.Printf("✓ 点赞数查询结果: %+v\n", counts)
	}

	// 5. 用户点赞列表
	fmt.Println("\n--- 用户数据查询 ---")

	userLikes, err := adapter.GetUserLikes(ctx, "user123", "video", 10, 0)
	if err != nil {
		log.Printf("获取用户点赞列表失败: %v", err)
	} else {
		fmt.Printf("✓ 用户点赞列表: %d条记录\n", len(userLikes))
		for i, like := range userLikes {
			if i < 3 { // 只显示前3条
				fmt.Printf("  - %s:%s 于 %v\n", like.ItemType, like.ItemID, like.Timestamp.Format("2006-01-02 15:04:05"))
			}
		}
	}

	// 6. 排行榜功能
	fmt.Println("\n--- 排行榜功能 ---")

	// 更新热门排行
	err = adapter.UpdateHotRank(ctx, "video", "score", 0.0)
	if err != nil {
		log.Printf("更新热门排行失败: %v", err)
	} else {
		fmt.Println("✓ 热门排行更新成功")
	}

	// 获取热门排行
	hotItems, err := adapter.GetHotRanking(ctx, "video", 5)
	if err != nil {
		log.Printf("获取热门排行失败: %v", err)
	} else {
		fmt.Printf("✓ 热门排行: %+v\n", hotItems)
	}

	// 7. 连接状态检查
	fmt.Println("\n--- 连接状态 ---")

	status := adapter.GetStatus()
	fmt.Printf("✓ 连接状态: %s\n", status)

	err = adapter.Ping(ctx)
	if err != nil {
		log.Printf("连接测试失败: %v", err)
	} else {
		fmt.Println("✓ 连接测试成功")
	}

	fmt.Println("\n=== MongoDB适配器示例完成 ===")
}

// MongoDBConfigExample MongoDB配置示例
func MongoDBConfigExample() {
	fmt.Println("=== MongoDB配置示例 ===")

	// 1. 默认配置
	defaultConfig := mongodb.DefaultMongoConfig()
	fmt.Printf("默认配置:\n")
	fmt.Printf("  - URI: %s\n", defaultConfig.URI)
	fmt.Printf("  - 数据库: %s\n", defaultConfig.Database)
	fmt.Printf("  - 连接池大小: %d-%d\n", defaultConfig.MinPoolSize, defaultConfig.MaxPoolSize)
	fmt.Printf("  - 超时时间: %v\n", defaultConfig.Timeout)

	// 2. 自定义配置
	customConfig := &mongodb.MongoConfig{
		Enabled:          true,
		UseSystem:        false,
		UseCustom:        true,
		URI:              "***********************************",
		Database:         "my_like_service",
		Username:         "user",
		Password:         "pass",
		AuthSource:       "admin",
		MaxPoolSize:      50,
		MinPoolSize:      2,
		MaxIdleTime:      15 * time.Minute,
		ConnectTimeout:   5 * time.Second,
		Timeout:          3 * time.Second,
		ReadPreference:   "primary",
		WriteConcern:     "majority",
		ReadConcern:      "majority",
		LikeCollection:   "user_likes",
		StatsCollection:  "like_statistics",
		EnableIndexes:    true,
		CreateIndexes:    true,
		BatchSize:        500,
		EnablePipeline:   true,
		DataRetention:    180 * 24 * time.Hour, // 180天
		EnableTTL:        true,
		TTLField:         "created_at",
		ConsistencyLevel: "strong",
		RetryWrites:      true,
		RetryReads:       true,
	}

	fmt.Printf("\n自定义配置:\n")
	fmt.Printf("  - 数据库: %s\n", customConfig.Database)
	fmt.Printf("  - 认证: %s@%s\n", customConfig.Username, customConfig.AuthSource)
	fmt.Printf("  - 数据保留: %v\n", customConfig.DataRetention)
	fmt.Printf("  - 一致性级别: %s\n", customConfig.ConsistencyLevel)

	// 3. 使用自定义配置创建适配器
	factory := mongodb.NewFactory()
	adapter, err := factory.CreateAdapter(customConfig, "articles")
	if err != nil {
		log.Printf("使用自定义配置创建适配器失败: %v", err)
	} else {
		fmt.Println("✓ 使用自定义配置创建适配器成功")
		adapter.Cleanup(context.Background())
	}

	fmt.Println("\n=== MongoDB配置示例完成 ===")
}

// MongoDBPerformanceExample MongoDB性能测试示例
func MongoDBPerformanceExample() {
	fmt.Println("=== MongoDB性能测试示例 ===")

	adapter, err := mongodb.CreateDefaultAdapter("performance_test")
	if err != nil {
		log.Fatalf("创建适配器失败: %v", err)
	}
	defer adapter.Cleanup(context.Background())

	ctx := context.Background()

	// 批量点赞性能测试
	fmt.Println("\n--- 批量操作性能测试 ---")

	batchSize := 100
	operations := make([]*types.LikeOperation, batchSize)

	start := time.Now()
	for i := 0; i < batchSize; i++ {
		operations[i] = &types.LikeOperation{
			UserID:    fmt.Sprintf("user%d", i),
			ItemID:    fmt.Sprintf("item%d", i%10), // 10个不同的项目
			ItemType:  "test",
			Action:    "like",
			Timestamp: time.Now(),
		}
	}

	err = adapter.BatchLike(ctx, operations)
	duration := time.Since(start)

	if err != nil {
		log.Printf("批量点赞失败: %v", err)
	} else {
		fmt.Printf("✓ 批量点赞%d条记录耗时: %v\n", batchSize, duration)
		fmt.Printf("✓ 平均每条记录: %v\n", duration/time.Duration(batchSize))
	}

	// 查询性能测试
	fmt.Println("\n--- 查询性能测试 ---")

	queryItems := make(map[string]string)
	for i := 0; i < 10; i++ {
		queryItems[fmt.Sprintf("item%d", i)] = "test"
	}

	start = time.Now()
	counts, err := adapter.BatchGetLikeCounts(ctx, queryItems)
	duration = time.Since(start)

	if err != nil {
		log.Printf("批量查询失败: %v", err)
	} else {
		fmt.Printf("✓ 批量查询%d个项目耗时: %v\n", len(queryItems), duration)
		fmt.Printf("✓ 查询结果: %+v\n", counts)
	}

	fmt.Println("\n=== MongoDB性能测试完成 ===")
}
