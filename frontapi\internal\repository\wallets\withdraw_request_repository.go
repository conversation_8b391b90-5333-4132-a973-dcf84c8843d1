package wallets

import (
	"context"
	"frontapi/internal/models/wallets"
	"frontapi/internal/repository/base"
	"time"

	"gorm.io/gorm"
)

// WithdrawRequestRepository 提现申请数据访问接口
type WithdrawRequestRepository interface {
	base.ExtendedRepository[wallets.WithdrawRequest]
	// 业务特定方法
	FindByOrderNo(ctx context.Context, orderNo string) (*wallets.WithdrawRequest, error)
	FindByUserID(ctx context.Context, userID string, page, pageSize int) ([]*wallets.WithdrawRequest, int64, error)
	AuditWallet(ctx context.Context, id string, status string, adminID string, remark string) error
	UpdatePaymentInfo(ctx context.Context, id string, paymentNo string, paymentTime time.Time) error
}

// withdrawRequestRepository 提现申请数据访问实现
type withdrawRequestRepository struct {
	base.ExtendedRepository[wallets.WithdrawRequest]
}

// NewWithdrawRequestRepository 创建提现申请仓库实例
func NewWithdrawRequestRepository(db *gorm.DB) WithdrawRequestRepository {
	return &withdrawRequestRepository{
		ExtendedRepository: base.NewExtendedRepository[wallets.WithdrawRequest](db),
	}
}

// FindByOrderNo 根据订单号查找提现申请
func (r *withdrawRequestRepository) FindByOrderNo(ctx context.Context, orderNo string) (*wallets.WithdrawRequest, error) {
	condition := map[string]interface{}{"order_no": orderNo}
	return r.FindOneByCondition(ctx, condition, "")
}

// FindByUserID 根据用户ID查找提现申请列表
func (r *withdrawRequestRepository) FindByUserID(ctx context.Context, userID string, page, pageSize int) ([]*wallets.WithdrawRequest, int64, error) {
	condition := map[string]interface{}{"user_id": userID}
	return r.List(ctx, condition, "created_at DESC", page, pageSize)
}

// UpdateStatus 更新提现申请状态
func (r *withdrawRequestRepository) AuditWallet(ctx context.Context, id string, status string, adminID string, remark string) error {
	return r.GetDBWithContext(ctx).
		Model(&wallets.WithdrawRequest{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       status,
			"admin_id":     adminID,
			"process_time": time.Now(),
			"remark":       remark,
		}).Error
}

// UpdatePaymentInfo 更新支付信息
func (r *withdrawRequestRepository) UpdatePaymentInfo(ctx context.Context, id string, paymentNo string, paymentTime time.Time) error {
	return r.GetDBWithContext(ctx).
		Model(&wallets.WithdrawRequest{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"payment_no":   paymentNo,
			"payment_time": paymentTime,
			"status":       "paid",
		}).Error
}
