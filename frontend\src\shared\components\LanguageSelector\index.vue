<template>
    <div :key="componentKey" class="language-selector" :class="customClass">
        <!-- 语言切换按钮 -->
        <Button 
            ref="languageToggleRef"
            class="language-toggle-btn bg-gradient-to-r from-primary-400 to-primary-500 dark:from-primary-700 dark:to-primary-800" 
            @click="toggleLanguagePanel"
            :aria-label="t('language.selectLanguage')" 
            :title="t('language.selectLanguage')">
            <div class="language-button-content">
                <div class="language-flag">
                    <i class="pi pi-globe"></i>
                </div>
                <span class="language-name">{{ localeName }}</span>
            </div>
        </Button>
        <!-- 语言选择面板 -->
         <Popover ref="popoverRef" class="language-popover bg-surface-0 dark:bg-surface-800 border border-surface-200 dark:border-surface-700 shadow-md" :style="{width: '18rem'}" append-to="body">
            <div class="language-panel-header bg-gradient-to-r from-surface-50 to-surface-100 dark:from-surface-700 dark:to-surface-800 text-color dark:text-surface-50">
                <div class="language-panel-title">
                    <i class="pi pi-globe mr-2 text-primary-500 dark:text-primary-400"></i>
                    {{ t('language.languageSettings') }}
                </div>
                <Button @click="popoverRef.hide()" icon="pi pi-times"
                class="p-button-rounded p-button-text p-button-sm text-surface-700 dark:text-surface-100" />
            </div>
            <div class="language-panel-content">
                <div class="language-list">
                    <div v-for="language in supportedLanguages" :key="language.code" class="language-item hover:bg-surface-100 dark:hover:bg-surface-700 transition-colors duration-200"
                        :class="{ 'active bg-surface-200 dark:bg-surface-600': currentLocale === language.code }" @click="selectLanguage(language.code)">
                        <div class="language-flag">{{ language.flag }}</div>
                        <div class="language-info">
                            <div class="language-name text-color dark:text-surface-50">{{ language.nativeName }}</div>
                            <div class="language-english text-surface-600 dark:text-surface-300">{{ language.englishName }}</div>
                        </div>
                        <div v-if="currentLocale === language.code" class="language-check text-primary-500 dark:text-primary-400">
                            <i class="pi pi-check"></i>
                        </div>
                    </div>
                </div>
            </div>
         </Popover>
    
    </div>
</template>

<script setup lang="ts">
import { useTranslation } from '@/core/plugins/i18n/composables'
import { useLocaleStore } from '@/store/modules/locale'
import Button from 'primevue/button'
import { computed, nextTick, onBeforeUnmount, onMounted, ref } from 'vue'
const { t } = useTranslation()
// 组件属性
interface Props {
    customClass?: string
    useRounded?: boolean
    direction?: 'up' | 'down'
    panelPosition?: 'default' | 'top-center' | 'bottom-center'
    showText?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    customClass: '',
    useRounded: true,
    direction: 'down',
    panelPosition: 'default',
    showText: false
})

// 强制重新渲染的key
const componentKey = ref(0)
const popoverRef = ref()
const forceRerender = () => {
    componentKey.value += 1
}

// 国际化
const localeStore = useLocaleStore()

// 面板显示状态
const showLanguagePanel = ref(false)

// 当前语言
const currentLocale = computed(() => localeStore.locale)

// 支持的语言列表
const supportedLanguages = computed(() => localeStore.getAllLocales())

// 当前语言国旗
const currentLanguageFlag = computed(() => {
    const currentLang = supportedLanguages.value.find(lang => lang.code === currentLocale.value)
    return currentLang?.flag || '🌐'
})

// 当前语言名称
const localeName = computed(() => {
    const currentLang = supportedLanguages.value.find(lang => lang.code === currentLocale.value)
    return currentLang?.shortCode || currentLocale.value
})

// 切换语言面板
const toggleLanguagePanel = (event: MouseEvent) => {
    popoverRef.value.toggle(event)
}

// 选择语言
const selectLanguage = async (languageCode: string) => {
    console.log(`Changing language to: ${languageCode}`)
    try {
        // 调用全局i18n实例的setLanguage方法
        const success = await window.$setLanguage?.(languageCode)

        if (success) {
            console.log('Language changed successfully, forcing rerender')
            // 强制重新渲染
            await nextTick()
            forceRerender()
        } else {
            console.error(`Failed to set language to ${languageCode}`)
        }
    } catch (error) {
        console.error('Error setting language:', error)
        // 作为备选方案，使用store方法
        localeStore.setLocale(languageCode)
        forceRerender()
    }
    showLanguagePanel.value = false
}

// 语言变化时自动更新UI
const handleLocaleChanged = async (event: CustomEvent) => {
    const { locale: newLocale } = event.detail || {}
    if (newLocale && newLocale !== localeStore.locale) {
        console.log(`Locale changed event received: ${newLocale}`)
        localeStore.setLocale(newLocale)
        await nextTick()
        forceRerender()
    }
}

// 点击外部关闭面板
const handleClickOutside = (event: MouseEvent) => {
    const languageSelector = document.querySelector('.language-selector')
    if (languageSelector && !languageSelector.contains(event.target as Node)) {
        showLanguagePanel.value = false
    }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        showLanguagePanel.value = false
    }
}

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeydown)
    window.addEventListener('i18n:locale-changed', handleLocaleChanged as unknown as EventListener)
})

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('keydown', handleKeydown)
    window.removeEventListener('i18n:locale-changed', handleLocaleChanged as unknown as EventListener)
})

// 声明全局函数类型
declare global {
    interface Window {
        $setLanguage?: (locale: string) => Promise<boolean>;
    }
}
</script>

<style scoped lang="scss">
//清理不在html存在的类
.language-selector {
    position: relative;
    display: inline-block;
    .p-button.language-toggle-btn {
        padding: 0.5rem 0.8rem;
        transition: all 0.3s ease;
        border-radius: 50px;
        border: 1px solid transparent;
        color: white;
        &:hover{
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        &:active {
            transform: translateY(0);
        }
        position: relative;
        overflow: hidden;  /* 添加溢出隐藏 */
        .language-button-content{
            display: flex;
            align-items: center;
            .language-flag{
                font-size: 1.25rem;
                margin-right: 0.75rem;
            }
            .language-name{
                font-weight: 500;
                font-size: 0.875rem;
            }
        }
    }
}

.language-popover{
   border-radius: 0.5rem;
   overflow: hidden;
   color: var(--text-color);
   transition: all 0.3s ease;
   
   .language-panel-header{
       display: flex;
       justify-content: space-between; 
       align-items: center;
       padding: 0.75rem 1rem;
       border-bottom: 1px solid var(--surface-border);
       
       .language-panel-title{
           font-size: 1rem;
           font-weight: 600;
       }
   }
   
   .language-panel-content{
       max-height: 250px;
       overflow-y: auto;
       
       .language-list{
           .language-item{
               display: flex;
               align-items: center;
               padding: 0.75rem 1rem;
               cursor: pointer;
               border-bottom: 1px solid var(--surface-border);
               
               &.active{
                   font-weight: 500;
               }
               
               .language-flag{
                   font-size: 1.25rem;
                   margin-right: 0.75rem;
               }
               
               .language-info{
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 0.25rem;
                    
                    .language-name{
                        font-size: 0.9rem;
                        font-weight: 500;
                    }
                    
                    .language-english{
                        font-size: 0.75rem;
                    }
               }
               
               .language-check{
                    font-size: 1rem;
                    margin-left: auto;
               }
           }
       }
   }
}
</style>