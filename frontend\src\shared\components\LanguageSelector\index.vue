<template>
    <div :key="componentKey" class="language-selector" :class="customClass">
        <!-- 语言切换按钮 -->
        <Button @click="toggleLanguagePanel" type="button"
            class="p-button-rounded p-button-text p-button-sm language-toggle-btn"
            :class="{ 'active': showLanguagePanel, 'rounded': useRounded }" aria-haspopup="true"
            :aria-expanded="showLanguagePanel" :title="t('language.changeLanguage')">
            <template #icon>
                <span class="current-language-flag"> <i class="pi pi-globe"></i>{{ currentLanguageFlag }}</span>
               
            </template>
            <span v-if="showText" class="current-language-name">{{ localeName }}</span>
        </Button>

        <!-- 语言选择面板 -->
        <div v-show="showLanguagePanel" class="language-panel" :class="{
            'direction-up': direction === 'up',
            'position-top-center': panelPosition === 'top-center',
            'position-bottom-center': panelPosition === 'bottom-center'
        }" @click.stop>
            <div class="language-panel-header">
                <h3 class="language-panel-title">
                    <i class="pi pi-globe mr-2"></i>
                    {{ t('language.languageSettings') }}
                </h3>
                <Button @click="showLanguagePanel = false" icon="pi pi-times"
                    class="p-button-rounded p-button-text p-button-sm" />
            </div>

            <div class="language-panel-content">
                <!-- 语言列表 -->
                <div class="language-list">
                    <div v-for="language in supportedLanguages" :key="language.code" class="language-item"
                        :class="{ 'active': currentLocale === language.code }" @click="selectLanguage(language.code)">
                        <div class="language-flag">{{ language.flag }}</div>
                        <div class="language-info">
                            <div class="language-name">{{ language.nativeName }}</div>
                            <div class="language-english">{{ language.englishName }}</div>
                        </div>
                        <div v-if="currentLocale === language.code" class="language-check">
                            <i class="pi pi-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 遮罩层 -->
        <div v-show="showLanguagePanel" class="language-overlay" @click="showLanguagePanel = false"></div>
    </div>
</template>

<script setup lang="ts">
import { useLocaleStore } from '@/store/modules/locale'
import Button from 'primevue/button'
import { computed, nextTick, onBeforeUnmount, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

// 组件属性
interface Props {
    customClass?: string
    useRounded?: boolean
    direction?: 'up' | 'down'
    panelPosition?: 'default' | 'top-center' | 'bottom-center'
    showText?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    customClass: '',
    useRounded: true,
    direction: 'down',
    panelPosition: 'default',
    showText: false
})

// 强制重新渲染的key
const componentKey = ref(0)
const forceRerender = () => {
    componentKey.value += 1
}

// 国际化
const { t, locale } = useI18n({ useScope: 'global' })
const localeStore = useLocaleStore()

// 面板显示状态
const showLanguagePanel = ref(false)

// 当前语言
const currentLocale = computed(() => localeStore.locale)

// 支持的语言列表
const supportedLanguages = computed(() => localeStore.getAllLocales())

// 当前语言国旗
const currentLanguageFlag = computed(() => {
    const currentLang = supportedLanguages.value.find(lang => lang.code === currentLocale.value)
    return currentLang?.flag || '🌐'
})

// 当前语言名称
const localeName = computed(() => {
    const currentLang = supportedLanguages.value.find(lang => lang.code === currentLocale.value)
    return currentLang?.shortCode || currentLocale.value
})

// 切换语言面板
const toggleLanguagePanel = () => {
    showLanguagePanel.value = !showLanguagePanel.value
}

// 选择语言
const selectLanguage = async (languageCode: string) => {
    console.log(`Changing language to: ${languageCode}`)
    try {
        // 调用全局i18n实例的setLanguage方法
        const success = await window.$setLanguage?.(languageCode)

        if (success) {
            console.log('Language changed successfully, forcing rerender')
            // 强制重新渲染
            await nextTick()
            forceRerender()
        } else {
            console.error(`Failed to set language to ${languageCode}`)
        }
    } catch (error) {
        console.error('Error setting language:', error)
        // 作为备选方案，使用store方法
        localeStore.setLocale(languageCode)
        forceRerender()
    }
    showLanguagePanel.value = false
}

// 语言变化时自动更新UI
const handleLocaleChanged = async (event: CustomEvent) => {
    const { locale: newLocale } = event.detail || {}
    if (newLocale && newLocale !== localeStore.locale) {
        console.log(`Locale changed event received: ${newLocale}`)
        localeStore.setLocale(newLocale)
        await nextTick()
        forceRerender()
    }
}

// 点击外部关闭面板
const handleClickOutside = (event: MouseEvent) => {
    const languageSelector = document.querySelector('.language-selector')
    if (languageSelector && !languageSelector.contains(event.target as Node)) {
        showLanguagePanel.value = false
    }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
        showLanguagePanel.value = false
    }
}

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeydown)
    window.addEventListener('i18n:locale-changed', handleLocaleChanged as unknown as EventListener)
})

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
    document.removeEventListener('keydown', handleKeydown)
    window.removeEventListener('i18n:locale-changed', handleLocaleChanged as unknown as EventListener)
})

// 声明全局函数类型
declare global {
    interface Window {
        $setLanguage?: (locale: string) => Promise<boolean>;
    }
}
</script>

<style scoped lang="scss">
.language-selector {
    position: relative;
    display: inline-block;
}

.language-toggle-btn {
    transition: background-color 0.3s ease;
    position: relative;
    overflow: hidden;  /* 添加溢出隐藏 */
    
    /* 限制水波纹大小 */
    :deep(.p-ink) {
        height: 100% !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
        transform: scale(0.8);
    }
    
    .current-language-flag {
        font-size: 1.25rem;
        display: flex;
        gap: 6px;
        align-items: center;
        color: white;
    }
    &:hover {
        background-color: var(--surface-hover) !important;
    }

    &.active {
        background-color: var(--primary-color) !important;
        color: var(--primary-color-text) !important;
    }

    &.rounded {
        border-radius: 50px;
    }

    &:not(.rounded) {
        border-radius: 0.25rem;
    }

    .current-language-name {
        margin-left: 0.3rem;
        font-weight: 500;
    }
}



.language-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 999;
    backdrop-filter: blur(2px);
}

.language-panel {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 240px;
    background: var(--surface-overlay, var(--surface-card));
    border: 1px solid var(--surface-border);
    border-radius: 0.25rem;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    overflow: hidden;
    animation: slideDown 0.2s ease;

    &.direction-up {
        top: auto;
        bottom: calc(100% + 8px);
        animation: slideUp 0.2s ease;
    }

    &.position-top-center,
    &.position-bottom-center {
        right: auto;
        left: 50%;
        transform: translateX(-50%);
    }
}

.language-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-bottom: 1px solid var(--surface-border);
    background: var(--surface-section, var(--surface-ground, var(--surface-100)));
}

.language-panel-title {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color, var(--text-color-primary, inherit));
    display: flex;
    align-items: center;
}

.language-panel-content {
    padding: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
}

.language-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.language-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    cursor: pointer;
    color: var(--text-color, var(--text-color-primary, inherit));

    &:hover {
        background-color: var(--surface-hover, var(--surface-200));
        color: var(--text-color, var(--text-color-primary, inherit));
    }

    &.active {
        background-color: var(--primary-50, rgba(var(--primary-color-rgb), 0.1));
        color: var(--primary-700, var(--primary-color, inherit));
    }

    .language-flag {
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }

    .language-info {
        flex: 1;

        .language-name {
            font-weight: 500;
            font-size: 0.875rem;
            color: var(--text-color, var(--text-color-primary, inherit));
        }

        .language-english {
            font-size: 0.75rem;
            color: var(--text-color-secondary, var(--text-600, inherit));
        }
    }

    .language-check {
        color: var(--primary-color);
        font-size: 0.875rem;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 滚动条样式
.language-list::-webkit-scrollbar {
    width: 4px;
}

.language-list::-webkit-scrollbar-track {
    background: var(--surface-ground, var(--surface-200));
}

.language-list::-webkit-scrollbar-thumb {
    background: var(--surface-400);
    border-radius: 2px;
}

.language-list::-webkit-scrollbar-thumb:hover {
    background: var(--surface-500);
}

// 响应式设计
@media (max-width: 768px) {
    .language-panel {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        transform: translate(-50%, -50%);
        width: 90vw;
        max-width: 280px;
        max-height: 80vh;

        &.direction-up {
            top: 50%;
            bottom: auto;
            transform: translate(-50%, -50%);
        }

        &.position-top-center,
        &.position-bottom-center {
            transform: translate(-50%, -50%);
        }
    }
}
</style>