/**
 * Tooltip 指令
 * 提供简单的提示框功能
 */

import type { DirectiveBinding, ObjectDirective } from 'vue'

// 提示框配置选项
export interface TooltipOptions {
    position?: 'top' | 'right' | 'bottom' | 'left'
    content?: string
    showDelay?: number
    hideDelay?: number
    theme?: 'light' | 'dark' | 'primary'
    maxWidth?: string | number
    showOnHover?: boolean
    showOnFocus?: boolean
    showOnClick?: boolean
    allowHtml?: boolean
}

// 元素类型
interface TooltipElement extends HTMLElement {
    _tooltip?: {
        tooltipElement: HTMLElement | null
        options: TooltipOptions
        showTimeout: number | null
        hideTimeout: number | null
        isVisible: boolean
        eventListeners: {
            mouseenter?: (e: MouseEvent) => void
            mouseleave?: (e: MouseEvent) => void
            focus?: (e: FocusEvent) => void
            blur?: (e: FocusEvent) => void
            click?: (e: MouseEvent) => void
        }
    }
}

// 默认配置
const defaultOptions: TooltipOptions = {
    position: 'top',
    content: '',
    showDelay: 300,
    hideDelay: 200,
    theme: 'dark',
    maxWidth: 250,
    showOnHover: true,
    showOnFocus: true,
    showOnClick: false,
    allowHtml: false,
}

// 创建提示框元素
function createTooltipElement(content: string, options: TooltipOptions): HTMLElement {
    const tooltipElement = document.createElement('div')
    tooltipElement.className = `v-tooltip v-tooltip-${options.theme} v-tooltip-${options.position}`

    if (options.allowHtml) {
        tooltipElement.innerHTML = content
    } else {
        tooltipElement.textContent = content
    }

    // 应用样式
    tooltipElement.style.position = 'fixed'
    tooltipElement.style.zIndex = '9999'
    tooltipElement.style.maxWidth = `${options.maxWidth}px`
    tooltipElement.style.padding = '0.5rem 0.75rem'
    tooltipElement.style.borderRadius = '0.25rem'
    tooltipElement.style.fontSize = '0.875rem'
    tooltipElement.style.lineHeight = '1.25rem'
    tooltipElement.style.opacity = '0'
    tooltipElement.style.pointerEvents = 'none'
    tooltipElement.style.transition = 'opacity 0.2s ease-in-out'

    // 应用主题样式
    switch (options.theme) {
        case 'light':
            tooltipElement.style.backgroundColor = '#fff'
            tooltipElement.style.color = '#212121'
            tooltipElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)'
            tooltipElement.style.border = '1px solid rgba(0,0,0,0.1)'
            break
        case 'primary':
            tooltipElement.style.backgroundColor = 'var(--primary-color, #1976D2)'
            tooltipElement.style.color = 'white'
            break
        case 'dark':
        default:
            tooltipElement.style.backgroundColor = '#212121'
            tooltipElement.style.color = 'white'
            break
    }

    return tooltipElement
}

// 定位提示框
function positionTooltip(tooltipEl: HTMLElement, targetEl: HTMLElement, position: string): void {
    const targetRect = targetEl.getBoundingClientRect()
    const tooltipRect = tooltipEl.getBoundingClientRect()

    let left = 0
    let top = 0

    // 基于位置设置定位
    switch (position) {
        case 'top':
            left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2)
            top = targetRect.top - tooltipRect.height - 5
            break
        case 'bottom':
            left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2)
            top = targetRect.bottom + 5
            break
        case 'left':
            left = targetRect.left - tooltipRect.width - 5
            top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2)
            break
        case 'right':
            left = targetRect.right + 5
            top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2)
            break
    }

    // 确保提示框在视口内
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    // 避免左侧溢出
    if (left < 5) {
        left = 5
    }

    // 避免右侧溢出
    if (left + tooltipRect.width > viewportWidth - 5) {
        left = viewportWidth - tooltipRect.width - 5
    }

    // 避免顶部溢出
    if (top < 5) {
        top = 5
    }

    // 避免底部溢出
    if (top + tooltipRect.height > viewportHeight - 5) {
        top = viewportHeight - tooltipRect.height - 5
    }

    tooltipEl.style.left = `${left}px`
    tooltipEl.style.top = `${top}px`
}

// 显示提示框
function showTooltip(el: TooltipElement): void {
    if (!el._tooltip) return

    // 清除现有超时
    if (el._tooltip.hideTimeout) {
        clearTimeout(el._tooltip.hideTimeout)
        el._tooltip.hideTimeout = null
    }

    // 如果提示框元素已存在，则直接显示
    if (el._tooltip.tooltipElement) {
        el._tooltip.tooltipElement.style.opacity = '1'
        el._tooltip.isVisible = true
        return
    }

    // 创建提示框元素
    const tooltipElement = createTooltipElement(
        el._tooltip.options.content || el.getAttribute('title') || '',
        el._tooltip.options
    )

    // 添加到DOM
    document.body.appendChild(tooltipElement)
    el._tooltip.tooltipElement = tooltipElement

    // 定位提示框
    positionTooltip(tooltipElement, el, el._tooltip.options.position || 'top')

    // 显示提示框
    setTimeout(() => {
        if (tooltipElement) {
            tooltipElement.style.opacity = '1'
        }
    }, 20)

    el._tooltip.isVisible = true

    // 如果元素有title属性，暂时隐藏它以避免原生提示框
    if (el.getAttribute('title')) {
        el.dataset.originalTitle = el.getAttribute('title') || ''
        el.removeAttribute('title')
    }
}

// 隐藏提示框
function hideTooltip(el: TooltipElement): void {
    if (!el._tooltip || !el._tooltip.tooltipElement) return

    // 清除现有超时
    if (el._tooltip.showTimeout) {
        clearTimeout(el._tooltip.showTimeout)
        el._tooltip.showTimeout = null
    }

    // 隐藏提示框
    if (el._tooltip.tooltipElement) {
        el._tooltip.tooltipElement.style.opacity = '0'

        // 延迟后移除元素
        el._tooltip.hideTimeout = window.setTimeout(() => {
            if (el._tooltip && el._tooltip.tooltipElement) {
                if (el._tooltip.tooltipElement.parentNode) {
                    el._tooltip.tooltipElement.parentNode.removeChild(el._tooltip.tooltipElement)
                }
                el._tooltip.tooltipElement = null
                el._tooltip.isVisible = false
            }

            // 恢复title属性
            if (el.dataset.originalTitle) {
                el.setAttribute('title', el.dataset.originalTitle)
                delete el.dataset.originalTitle
            }
        }, 200) as unknown as number
    }
}

// 添加事件监听器
function addEventListeners(el: TooltipElement): void {
    if (!el._tooltip) return

    const options = el._tooltip.options

    // 鼠标悬停事件
    if (options.showOnHover) {
        el._tooltip.eventListeners.mouseenter = () => {
            if (el._tooltip?.showTimeout) {
                clearTimeout(el._tooltip.showTimeout)
            }
            el._tooltip!.showTimeout = window.setTimeout(() => {
                showTooltip(el)
            }, options.showDelay) as unknown as number
        }

        el._tooltip.eventListeners.mouseleave = () => {
            if (el._tooltip?.showTimeout) {
                clearTimeout(el._tooltip.showTimeout)
                el._tooltip.showTimeout = null
            }

            el._tooltip!.hideTimeout = window.setTimeout(() => {
                hideTooltip(el)
            }, options.hideDelay) as unknown as number
        }

        el.addEventListener('mouseenter', el._tooltip.eventListeners.mouseenter)
        el.addEventListener('mouseleave', el._tooltip.eventListeners.mouseleave)
    }

    // 焦点事件
    if (options.showOnFocus) {
        el._tooltip.eventListeners.focus = () => {
            if (el._tooltip?.showTimeout) {
                clearTimeout(el._tooltip.showTimeout)
            }
            el._tooltip!.showTimeout = window.setTimeout(() => {
                showTooltip(el)
            }, options.showDelay) as unknown as number
        }

        el._tooltip.eventListeners.blur = () => {
            if (el._tooltip?.showTimeout) {
                clearTimeout(el._tooltip.showTimeout)
                el._tooltip.showTimeout = null
            }

            el._tooltip!.hideTimeout = window.setTimeout(() => {
                hideTooltip(el)
            }, options.hideDelay) as unknown as number
        }

        el.addEventListener('focus', el._tooltip.eventListeners.focus)
        el.addEventListener('blur', el._tooltip.eventListeners.blur)
    }

    // 点击事件
    if (options.showOnClick) {
        el._tooltip.eventListeners.click = () => {
            if (el._tooltip?.isVisible) {
                hideTooltip(el)
            } else {
                showTooltip(el)
            }
        }

        el.addEventListener('click', el._tooltip.eventListeners.click)
    }
}

// 移除事件监听器
function removeEventListeners(el: TooltipElement): void {
    if (!el._tooltip || !el._tooltip.eventListeners) return

    const listeners = el._tooltip.eventListeners

    if (listeners.mouseenter) {
        el.removeEventListener('mouseenter', listeners.mouseenter)
    }

    if (listeners.mouseleave) {
        el.removeEventListener('mouseleave', listeners.mouseleave)
    }

    if (listeners.focus) {
        el.removeEventListener('focus', listeners.focus)
    }

    if (listeners.blur) {
        el.removeEventListener('blur', listeners.blur)
    }

    if (listeners.click) {
        el.removeEventListener('click', listeners.click)
    }

    el._tooltip.eventListeners = {}
}

// 导出 Tooltip 指令
export const vTooltip: ObjectDirective<TooltipElement, string | TooltipOptions> = {
    mounted(el: TooltipElement, binding: DirectiveBinding<string | TooltipOptions>): void {
        // 解析选项
        let options: TooltipOptions = { ...defaultOptions }

        if (typeof binding.value === 'string') {
            options.content = binding.value
        } else if (binding.value) {
            options = { ...options, ...binding.value }
        }

        // 从修饰符中获取位置
        if (binding.modifiers.top) options.position = 'top'
        if (binding.modifiers.right) options.position = 'right'
        if (binding.modifiers.bottom) options.position = 'bottom'
        if (binding.modifiers.left) options.position = 'left'

        // 初始化tooltip状态
        el._tooltip = {
            tooltipElement: null,
            options,
            showTimeout: null,
            hideTimeout: null,
            isVisible: false,
            eventListeners: {}
        }

        // 添加事件监听器
        addEventListeners(el)
    },

    updated(el: TooltipElement, binding: DirectiveBinding<string | TooltipOptions>): void {
        if (!el._tooltip) return

        // 更新选项
        let options: TooltipOptions = { ...defaultOptions }

        if (typeof binding.value === 'string') {
            options.content = binding.value
        } else if (binding.value) {
            options = { ...options, ...binding.value }
        }

        // 从修饰符中获取位置
        if (binding.modifiers.top) options.position = 'top'
        if (binding.modifiers.right) options.position = 'right'
        if (binding.modifiers.bottom) options.position = 'bottom'
        if (binding.modifiers.left) options.position = 'left'

        // 更新选项
        el._tooltip.options = options

        // 如果提示框正在显示，则更新它
        if (el._tooltip.isVisible && el._tooltip.tooltipElement) {
            // 更新内容
            if (options.allowHtml) {
                el._tooltip.tooltipElement.innerHTML = options.content || ''
            } else {
                el._tooltip.tooltipElement.textContent = options.content || ''
            }

            // 重新定位
            positionTooltip(el._tooltip.tooltipElement, el, options.position || 'top')
        }
    },

    beforeUnmount(el: TooltipElement): void {
        if (!el._tooltip) return

        // 移除事件监听器
        removeEventListeners(el)

        // 隐藏并清理提示框
        if (el._tooltip.tooltipElement) {
            if (el._tooltip.tooltipElement.parentNode) {
                el._tooltip.tooltipElement.parentNode.removeChild(el._tooltip.tooltipElement)
            }
        }

        // 清除超时
        if (el._tooltip.showTimeout) {
            clearTimeout(el._tooltip.showTimeout)
        }

        if (el._tooltip.hideTimeout) {
            clearTimeout(el._tooltip.hideTimeout)
        }

        // 恢复title属性
        if (el.dataset.originalTitle) {
            el.setAttribute('title', el.dataset.originalTitle)
            delete el.dataset.originalTitle
        }

        // 删除tooltip数据
        delete el._tooltip
    }
}

// 工具函数
export const tooltipUtils = {
    // 手动显示提示框
    show(el: TooltipElement): void {
        if (el && el._tooltip) {
            showTooltip(el)
        }
    },

    // 手动隐藏提示框
    hide(el: TooltipElement): void {
        if (el && el._tooltip) {
            hideTooltip(el)
        }
    },

    // 更新提示框内容
    updateContent(el: TooltipElement, content: string): void {
        if (el && el._tooltip) {
            el._tooltip.options.content = content

            if (el._tooltip.isVisible && el._tooltip.tooltipElement) {
                if (el._tooltip.options.allowHtml) {
                    el._tooltip.tooltipElement.innerHTML = content
                } else {
                    el._tooltip.tooltipElement.textContent = content
                }
            }
        }
    },

    // 更新提示框位置
    updatePosition(el: TooltipElement, position: 'top' | 'right' | 'bottom' | 'left'): void {
        if (el && el._tooltip) {
            el._tooltip.options.position = position

            if (el._tooltip.isVisible && el._tooltip.tooltipElement) {
                positionTooltip(el._tooltip.tooltipElement, el, position)
            }
        }
    }
}

// 默认导出
export default vTooltip 