package posts

import (
	"frontapi/internal/admin"
	service "frontapi/internal/service/posts"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// PostCommentController 帖子评论控制器
type PostCommentController struct {
	CommentService       service.PostCommentService
	admin.BaseController // 继承Response
}

// NewPostCommentController 创建帖子评论控制器实例
func NewPostCommentController(commentService service.PostCommentService) *PostCommentController {
	return &PostCommentController{
		CommentService: commentService,
	}
}

// ListComments 获取评论列表
func (c *PostCommentController) ListComments(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)
	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	post_id := reqInfo.Get("post_id").GetString()
	content := reqInfo.Get("content").GetString()
	user_id := reqInfo.Get("user_id").GetString()
	status := -999
	err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	if err != nil {
		status = -999
	}

	condition := map[string]interface{}{
		"post_id": post_id,
		"content": content,
		"user_id": user_id,
		"status":  status,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询评论列表
	commentList, total, err := c.CommentService.List(ctx.Context(), condition, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取评论列表失败: "+err.Error())
	}

	// 返回评论列表
	return c.SuccessList(ctx, commentList, total, page, pageSize)
}

// GetComment 获取评论详情
func (c *PostCommentController) GetComment(ctx *fiber.Ctx) error {
	// 获取评论ID
	id, _ := c.GetId(ctx)
	if id == "" {
		return c.BadRequest(ctx, "评论ID不能为空", nil)
	}

	// 查询评论
	comment, err := c.CommentService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取评论详情失败: "+err.Error())
	}

	if comment == nil {
		return c.NotFound(ctx, "评论不存在")
	}

	// 返回评论详情
	return c.Success(ctx, comment)
}

// UpdateCommentStatus 更新评论状态
func (c *PostCommentController) UpdateCommentStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req struct {
		ID     string `json:"id" validate:"required"`
		Status int    `json:"status" validate:"int|min:-5|max:5"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 检查评论是否存在
	comment, err := c.CommentService.GetByID(ctx.Context(), req.ID, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取评论详情失败: "+err.Error())
	}

	if comment == nil {
		return c.NotFound(ctx, "评论不存在")
	}

	// 更新状态
	err = c.CommentService.UpdateStatus(ctx.Context(), req.ID, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新评论状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新评论状态成功")
}

// DeleteComment 删除评论
func (c *PostCommentController) DeleteComment(ctx *fiber.Ctx) error {
	// 获取评论ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询评论
	comment, err := c.CommentService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取评论详情失败: "+err.Error())
	}

	if comment == nil {
		return c.NotFound(ctx, "评论不存在")
	}

	// 删除评论
	err = c.CommentService.SoftDelete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除评论失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除评论成功")
}

// GetCommentReplies 获取评论回复
func (c *PostCommentController) GetCommentReplies(ctx *fiber.Ctx) error {
	// 获取评论ID
	var req struct {
		ParentID string `json:"parent_id" validate:"required"`
	}
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	condition := map[string]interface{}{
		"parent_id": req.ParentID,
	}
	replies, total, err := c.CommentService.List(ctx.Context(), condition, "created_at DESC", 1, 10, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取评论回复失败: "+err.Error())
	}
	return c.SuccessList(ctx, replies, total, 1, 10)
}

// BatchUpdateCommentStatus 批量更新评论状态
func (c *PostCommentController) BatchUpdateCommentStatus(ctx *fiber.Ctx) error {
	var req struct {
		IDs    []string `json:"ids" validate:"required"`
		Status int      `json:"status" validate:"int|min:-5|max:5"`
	}
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	err := c.CommentService.BatchUpdateStatus(ctx.Context(), req.IDs, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新评论状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新评论状态成功")
}

// BatchDeleteComment 批量删除评论
func (c *PostCommentController) BatchDeleteComment(ctx *fiber.Ctx) error {
	var req struct {
		IDs []string `json:"ids" validate:"required"`
	}
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}
	err := c.CommentService.BatchSoftDelete(ctx.Context(), req.IDs)
	if err != nil {
		return c.InternalServerError(ctx, "删除评论失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除评论成功")
}
