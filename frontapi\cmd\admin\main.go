package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"frontapi/config"
	"frontapi/internal/bootstrap"
	"frontapi/internal/routes"
	"frontapi/pkg/database"
	"frontapi/pkg/redis"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
)

func main() {
	// 初始化配置
	config.LoadConfig()
	fmt.Println("config", config.AppConfig)

	// 初始化数据库
	database.InitMySQL()

	// 初始化Redis（包括管理后台Redis）
	if err := redis.Init(); err != nil {
		log.Fatalf("Redis初始化失败: %v", err)
	}
	defer redis.Close()

	// 初始化服务容器
	services := bootstrap.InitServices(database.DB)

	// 创建Fiber应用
	app := fiber.New(fiber.Config{
		AppName:   "Admin Backend API",
		BodyLimit: 100 * 1024 * 1024, // 100MB
	})

	// 添加中间件
	app.Use(recover.New())
	app.Use(logger.New())

	// CORS配置
	app.Use(cors.New(cors.Config{
		AllowOrigins:     "http://localhost:9527,http://localhost:8080,http://localhost:8081,http://localhost:3000,http://127.0.0.1:9527",
		AllowMethods:     "GET, POST, PUT, DELETE, OPTIONS, PATCH",
		AllowHeaders:     "Origin, Content-Type, Accept, Authorization, X-Admin-Token, X-Requested-With, x-client-info",
		AllowCredentials: true,
	}))

	// 注册管理后台路由
	routes.RegisterAdminRoutes(app, services)

	// 设置优雅关闭
	go func() {
		sigterm := make(chan os.Signal, 1)
		signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)
		<-sigterm

		log.Println("正在关闭管理后台服务器...")
		if err := app.Shutdown(); err != nil {
			log.Printf("服务器关闭失败: %v", err)
		}
	}()

	// 输出管理后台端口配置
	log.Printf("管理后台端口配置: %d", config.AppConfig.Server.AdminPort)

	// 启动管理后台服务器
	serverPort := ":" + fmt.Sprintf("%d", config.AppConfig.Server.AdminPort)
	log.Printf("管理后台服务器启动在端口 %s", serverPort)
	if err := app.Listen(serverPort); err != nil {
		log.Fatalf("启动管理后台服务器失败: %v", err)
	}
}
