package comics

import (
	"gorm.io/gorm"

	"frontapi/internal/models/comics"
	"frontapi/internal/repository/base"
)

// ComicReadHistoryRepository 漫画阅读历史数据访问接口
type ComicReadHistoryRepository interface {
	base.ExtendedRepository[comics.ComicReadHistory]
}

// comicReadHistoryRepository 漫画阅读历史数据访问实现
type comicReadHistoryRepository struct {
	base.ExtendedRepository[comics.ComicReadHistory]
}

// NewComicReadHistoryRepository 创建漫画阅读历史仓库实例
func NewComicReadHistoryRepository(db *gorm.DB) ComicReadHistoryRepository {
	return &comicReadHistoryRepository{
		ExtendedRepository: base.NewExtendedRepository[comics.ComicReadHistory](db),
	}
}
