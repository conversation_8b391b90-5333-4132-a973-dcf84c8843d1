<template>
  <div class="chapter-list">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="card-title">章节列表</span>
          <div class="button-group">
            <el-switch
              v-model="sortMode"
              active-text="排序模式"
              inactive-text="普通模式"
              style="margin-right: 15px;"
              @change="toggleSortMode"
            />
            <el-button v-if="sortMode" type="success" :disabled="!orderChanged" @click="saveSortOrder">保存排序</el-button>
        
            <el-button v-else type="primary" @click="onAdd">添加章节</el-button>
            <el-button type="warning" @click="router.back()">返回电子书列表</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索栏组件，排序模式下不显示 -->
      <SearchBar v-if="!sortMode" @search="onSearch" @reset="onReset" />
      
      <!-- 排序模式下的调试信息 -->
      <div v-if="sortMode && debugMode" class="debug-info">
        <p>章节数量: {{ sortableChapterList.length }}</p>
        <p v-if="sortableChapterList.length > 0">首个章节: {{ sortableChapterList[0].title }}</p>
      </div>
      
      <!-- 排序模式下的表格 -->
      <ChapterSortTable 
        v-if="sortMode"
        :loading="loading"
        :chapters="sortableChapterList"
        @update:chapters="updateSortableList"
        @change="onOrderChange"
      />
      
      <!-- 常规模式下的表格 -->
      <ChapterTable
        v-else
        :loading="loading"
        :chapter-list="chapterList"
        :pagination="pagination"
        @edit="onEdit"
        @delete="onDelete"
        @page-change="onPageChange"
        @size-change="onSizeChange"
        @refresh="fetchList"
      />

      <!-- 分页组件，排序模式下不显示 -->
      <div v-if="!sortMode" class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSizeChange"
          @current-change="onPageChange"
        />
      </div>

      <!-- 章节表单对话框组件 -->
      <ChapterDialog
        :visible="dialogVisible"
        :chapter="currentChapter"
        :book-id="bookId"
        :bookName="bookName"
        :maxChapterNumber="bookMaxChapterNumber"
        @submit="submitForm"
        @close="dialogVisible = false"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { 
  getBookChapterList, 
  createBookChapter, 
  updateBookChapter, 
  deleteBookChapter,
  updateChapterOrder
} from '@/service/api/books/chapter';
import { BookChapter, CreateBookChapterRequest, UpdateBookChapterRequest, BookParams } from '@/types/books';
import SearchBar from './components/SearchBar.vue';
import ChapterTable from './components/ChapterTable.vue';
import ChapterSortTable from './components/ChapterSortTable.vue';
import ChapterDialog from './components/ChapterDialog.vue';
import { ApiResponse } from "@/types/https";
import { useRoute, useRouter } from 'vue-router';

// 调试模式开关
const debugMode = ref(true);

const loading = ref(false);
const chapterList = ref<BookChapter[]>([]);
const sortableChapterList = ref<BookChapter[]>([]);
const pagination = reactive({ page: 1, pageSize: 10, total: 0 });
const dialogVisible = ref(false);
const currentChapter = ref<BookChapter>();
const sortMode = ref(false);
const orderChanged = ref(false);
const bookMaxChapterNumber = ref(0);

const route = useRoute();
const router = useRouter();
const bookId = route.query.bookId as string;
const bookName = route.query.bookName as string;
console.log("bookId", bookId, bookName);

// 监听排序模式变化
watch(() => sortMode.value, (newVal) => {
  if (newVal) {
    console.log('排序模式已启用');
  } else {
    console.log('退出排序模式');
  }
});

// 切换排序模式
const toggleSortMode = async (val: unknown) => {
  const isSort = Boolean(val);
  sortMode.value = isSort;
  if (isSort) {
    console.log('正在进入排序模式...');
    // 进入排序模式，加载所有章节用于排序
    await loadAllChaptersForSort();
  } else {
    // 退出排序模式，如果有未保存的排序，提示用户
    if (orderChanged.value) {
      ElMessageBox.confirm('您有未保存的排序更改，确定要退出排序模式吗？', '提示', {
        confirmButtonText: '退出',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        orderChanged.value = false;
        fetchList(); // 恢复原始列表数据
      }).catch(() => {
        sortMode.value = true; // 取消退出，保持排序模式
      });
    } else {
      fetchList(); // 恢复原始列表数据
    }
  }
};

// 加载所有章节用于排序
const loadAllChaptersForSort = async () => {
  console.log('开始加载章节数据供排序...');
  loading.value = true;
  try {
    // 获取当前筛选条件下的所有章节
    const queryParams: BookParams = {
      page: { pageNo: 1, pageSize: 1000 }, // 加载足够多的章节用于排序
      data: { book_id: bookId }
    };
    
    console.log('排序模式请求参数:', JSON.stringify(queryParams));
    
    try {
      const response = await getBookChapterList(queryParams);
      console.log('章节数据API响应:', response);
      
      // 提取数据
      let chapters: BookChapter[] = [];
      
      // 用安全的方式解包数据
      try {
        // 尝试获取不同格式的响应数据
        if (response && response.data && Array.isArray(response.data)) {
          chapters = response.data;
        } else if (response && response.data && response.data.list && Array.isArray(response.data.list)) {
          chapters = response.data.list;
        } else if (response && response.data && response.data.data && Array.isArray(response.data.data)) {
          chapters = response.data.data;
        } else {
          console.warn('未识别的API响应格式，将使用空数组');
        }
      } catch(e) {
        console.error('解析API响应出错:', e);
      }
      
      console.log(`获取到 ${chapters.length} 个章节`);
      
      if (chapters.length === 0) {
        ElMessage.warning('没有可供排序的章节');
        return;
      }
      
      // 按章节序号排序
      const sorted = [...chapters].sort((a, b) => {
        const aNum = a.chapter_number || a.sort_order || 0;
        const bNum = b.chapter_number || b.sort_order || 0;
        return aNum - bNum;
      });
      
      sortableChapterList.value = sorted;
      console.log('排序后章节列表:', sortableChapterList.value.length, '个章节');
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      ElMessage.error('获取章节数据失败: API错误');
    }
  } catch (error) {
    console.error('加载章节数据出错:', error);
    ElMessage.error('加载章节数据出错');
  } finally {
    loading.value = false;
  }
};

// 更新排序后的章节列表
const updateSortableList = (list: BookChapter[]) => {
  console.log('更新排序列表:', list.length);
  sortableChapterList.value = list;
};

// 排序变更标记
const onOrderChange = (changed: boolean) => {
  console.log('排序状态变更:', changed);
  orderChanged.value = changed;
};

// 保存排序
const saveSortOrder = async () => {
  if (!orderChanged.value) return;
  
  loading.value = true;
  try {
    // 准备要提交的排序数据
    const orderData = sortableChapterList.value.map((chapter, index) => ({
      id: chapter.id,
      sort_order: index + 1,
      chapter_number: index + 1
    }));
    
    console.log('保存排序数据:', orderData);
    
    // 调用API保存排序
    const res = await updateChapterOrder({ 
      data: { 
        book_id: bookId, 
        chapters: orderData 
      } 
    });
    console.log('保存排序API响应:', res);
    
    // 简化处理响应，不做类型判断
    ElMessage.success('章节排序保存成功');
    orderChanged.value = false;
    // 刷新数据
    fetchList();
    sortMode.value = false;
  } catch (error) {
    console.error('保存章节排序失败:', error);
    ElMessage.error('保存排序失败');
  } finally {
    loading.value = false;
  }
};

const fetchList = async (params?: any) => {
  console.log('获取章节列表, 参数:', params);
  loading.value = true;
  try {
    const queryParams: BookParams = {
      page: { pageNo: pagination.page, pageSize: pagination.pageSize },
      data: { ...params, book_id: bookId }
    };
    
    console.log('获取章节列表请求参数:', JSON.stringify(queryParams));
    
    try {
      const response = await getBookChapterList(queryParams);
      console.log('获取章节列表响应:', response);
      
      // 提取数据 - 简化处理，不进行类型检查
      let chapters: BookChapter[] = [];
      let total = 0;
      
      // 解包数据安全
      try {
        if (response && response.data) {
          if (Array.isArray(response.data)) {
            chapters = response.data;
            total = chapters.length;
          } else if (response.data.list && Array.isArray(response.data.list)) {
            chapters = response.data.list;
            total = response.data.total || chapters.length;
          } else if (response.data.data && Array.isArray(response.data.data)) {
            chapters = response.data.data;
            total = response.data.total || chapters.length;
          }
        }
      } catch(e) {
        console.error('解析API响应出错:', e);
      }
      
      chapterList.value = chapters;
      pagination.total = total;
      
      console.log(`加载了 ${chapterList.value.length} / ${pagination.total} 章节`);
      getBookMaxChapterNumber();
    } catch (apiError) {
      console.error('API调用失败:', apiError);
      ElMessage.error('获取章节列表失败: API错误');
    }
  } finally {
    loading.value = false;
  }
};

const getBookMaxChapterNumber = () => {
  if(chapterList.value.length > 0) { //找出最大chapter_number
    bookMaxChapterNumber.value = chapterList.value.reduce((max, chapter) => 
      Math.max(max, chapter.chapter_number || 0), 0);
    console.log('最大章节序号:', bookMaxChapterNumber.value);
  }
};

const onSearch = (params: any) => {
  pagination.page = 1;
  fetchList(params);
};

const onReset = () => {
  pagination.page = 1;
  fetchList();
};

const onPageChange = (page: number) => {
  pagination.page = page;
  fetchList();
};

const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  fetchList();
};

const onAdd = () => {
  currentChapter.value = undefined;
  dialogVisible.value = true;
};

const onEdit = (row: BookChapter) => {
  currentChapter.value = row;
  dialogVisible.value = true;
};

const onDelete = (row: BookChapter) => {
  ElMessageBox.confirm('确认删除该章节吗？', '提示', {
    type: 'warning',
  }).then(async () => {
    try {
      await deleteBookChapter(row.id);
      ElMessage.success('删除成功');
      fetchList();
    } catch (error) {
      console.error('删除章节失败:', error);
      ElMessage.error('删除失败');
    }
  });
};

const submitForm = async (formData: any) => {
  const isEdit = formData.id;
  console.log(`${isEdit ? '更新' : '创建'}章节, 数据:`, formData);
  
  try {
    const res = isEdit
      ? await updateBookChapter({ data: formData })
      : await createBookChapter({ data: formData });
      
    console.log(`${isEdit ? '更新' : '创建'}章节响应:`, res);
    
    ElMessage.success(isEdit ? '更新成功' : '创建成功');
    dialogVisible.value = false;
    fetchList();
  } catch (error) {
    console.error('提交章节表单失败:', error);
    ElMessage.error('操作失败');
  }
};

onMounted(() => {
  console.log('章节列表页面已挂载');
  fetchList();
});
</script>

<style scoped>
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.button-group {
  display: flex;
  align-items: center;
}

.debug-info {
  background-color: #f8f9fa;
  border: 1px dashed #ccc;
  padding: 10px;
  margin-bottom: 15px;
  font-family: monospace;
}
</style>
