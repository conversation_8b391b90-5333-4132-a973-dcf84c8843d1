package permission

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	permissionModels "frontapi/internal/models/permission"
	baseRepo "frontapi/internal/repository/base/extint"
)

// AdminMenuRepository 管理员菜单数据访问接口
type AdminMenuRepository interface {
	baseRepo.IntBaseRepository[permissionModels.AdminMenu] // 继承基础仓库接口

	// 特有的业务逻辑方法
	GetAll(ctx context.Context) ([]*permissionModels.AdminMenu, error)
	GetActive(ctx context.Context) ([]*permissionModels.AdminMenu, error)
	GetByParentID(ctx context.Context, parentID int) ([]*permissionModels.AdminMenu, error)
	GetByType(ctx context.Context, menuType int8) ([]*permissionModels.AdminMenu, error)
	GetMenuTree(ctx context.Context) ([]*permissionModels.AdminMenu, error)

	// 权限相关
	GetByPermissions(ctx context.Context, permissions []string) ([]*permissionModels.AdminMenu, error)
	GetMenusByRoleCodes(ctx context.Context, roleCodes []string) ([]*permissionModels.AdminMenu, error)

	// 验证操作
	ExistsName(ctx context.Context, name string, excludeID ...int) (bool, error)
	ExistsPath(ctx context.Context, path string, excludeID ...int) (bool, error)
	ExistsPermission(ctx context.Context, permission string, excludeID ...int) (bool, error)
}

// adminMenuRepository 管理员菜单数据访问实现
type adminMenuRepository struct {
	baseRepo.IntBaseRepository[permissionModels.AdminMenu] // 嵌入基础仓库
	db                                                     *gorm.DB
}

// NewAdminMenuRepository 创建管理员菜单仓库实例
func NewAdminMenuRepository(db *gorm.DB) AdminMenuRepository {
	baseRepository := baseRepo.NewIntBaseRepository[permissionModels.AdminMenu](db)
	return &adminMenuRepository{
		IntBaseRepository: baseRepository,
		db:                db,
	}
}

// GetAll 获取所有菜单
func (r *adminMenuRepository) GetAll(ctx context.Context) ([]*permissionModels.AdminMenu, error) {
	return r.IntBaseRepository.FindAll(ctx, map[string]interface{}{}, "sort ASC, created_at DESC")
}

// GetActive 获取所有活跃菜单
func (r *adminMenuRepository) GetActive(ctx context.Context) ([]*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"status": 1,
	}
	return r.IntBaseRepository.FindAll(ctx, condition, "sort ASC, created_at DESC")
}

// GetByParentID 根据父级ID获取菜单
func (r *adminMenuRepository) GetByParentID(ctx context.Context, parentID int) ([]*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"parent_id": parentID,
		"status":    1,
	}
	return r.IntBaseRepository.FindByCondition(ctx, condition, "sort ASC")
}

// GetByType 根据类型获取菜单
func (r *adminMenuRepository) GetByType(ctx context.Context, menuType int8) ([]*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"type":   menuType,
		"status": 1,
	}
	return r.IntBaseRepository.FindByCondition(ctx, condition, "sort ASC")
}

// GetMenuTree 获取菜单树
func (r *adminMenuRepository) GetMenuTree(ctx context.Context) ([]*permissionModels.AdminMenu, error) {
	condition := map[string]interface{}{
		"status": 1,
	}
	menus, err := r.IntBaseRepository.FindAll(ctx, condition, "sort ASC")
	if err != nil {
		return nil, fmt.Errorf("获取菜单树失败: %w", err)
	}

	// 构建树形结构
	return permissionModels.BuildMenuTree(menus), nil
}

// GetByPermissions 根据权限标识获取菜单
func (r *adminMenuRepository) GetByPermissions(ctx context.Context, permissions []string) ([]*permissionModels.AdminMenu, error) {
	var menus []*permissionModels.AdminMenu
	if err := r.db.WithContext(ctx).Where("permission IN (?) AND status = ?", permissions, 1).Order("sort ASC").Find(&menus).Error; err != nil {
		return nil, fmt.Errorf("根据权限标识获取菜单失败: %w", err)
	}
	return menus, nil
}

// GetMenusByRoleCodes 根据角色编码获取菜单(通过Casbin规则)
func (r *adminMenuRepository) GetMenusByRoleCodes(ctx context.Context, roleCodes []string) ([]*permissionModels.AdminMenu, error) {
	var menus []*permissionModels.AdminMenu

	// 通过Casbin规则表关联查询有权限的菜单
	subQuery := r.db.Select("DISTINCT v1").
		Table("casbin_rule").
		Where("ptype = ? AND v0 IN (?)", "p", roleCodes)

	if err := r.db.WithContext(ctx).
		Where("permission IN (?) AND status = ?", subQuery, 1).
		Order("sort ASC").
		Find(&menus).Error; err != nil {
		return nil, fmt.Errorf("根据角色编码获取菜单失败: %w", err)
	}

	return menus, nil
}

// ExistsName 检查菜单名称是否存在
func (r *adminMenuRepository) ExistsName(ctx context.Context, name string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"name": name,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	return r.IntBaseRepository.Exists(ctx, condition)
}

// ExistsPath 检查菜单路径是否存在
func (r *adminMenuRepository) ExistsPath(ctx context.Context, path string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"path": path,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	return r.IntBaseRepository.Exists(ctx, condition)
}

// ExistsPermission 检查权限标识是否存在
func (r *adminMenuRepository) ExistsPermission(ctx context.Context, permission string, excludeID ...int) (bool, error) {
	condition := map[string]interface{}{
		"permission": permission,
	}

	// 如果有排除ID，添加排除条件
	if len(excludeID) > 0 && excludeID[0] > 0 {
		condition["id !="] = excludeID[0]
	}

	return r.IntBaseRepository.Exists(ctx, condition)
}
