<template>
  <el-dialog
    :model-value="visible"
    title="分类详情"
    width="700px"
    @update:model-value="(val) => emit('update:visible', val)"
  >
    <div v-if="category" class="detail-container">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="分类名称">{{ category.name }}</el-descriptions-item>
        <el-descriptions-item label="分类编码">{{ category.code }}</el-descriptions-item>
        <el-descriptions-item label="父分类">{{ getParentCategoryName(category.parent_id) }}</el-descriptions-item>
        <el-descriptions-item label="排序">{{ category.sort_order }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="category.status === 1 ? 'success' : 'danger'">
            {{ category.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="推荐">
          <el-tag :type="category.is_featured === 1 ? 'success' : 'info'">
            {{ category.is_featured === 1 ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="颜色">
          <el-tag :color="category.color" style="color: white;">{{ category.color || '未设置' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="视频数量">{{ category.video_count || 0 }}</el-descriptions-item>
        <el-descriptions-item label="图标" :span="2">
          <el-image v-if="category.icon" :src="category.icon" style="width: 60px; height: 60px;" fit="cover" />
          <span v-else class="placeholder-text">未设置图标</span>
        </el-descriptions-item>
        <el-descriptions-item label="封面" :span="2">
          <el-image v-if="category.cover" :src="category.cover" style="width: 120px; height: 67px;" fit="cover" />
          <span v-else class="placeholder-text">未设置封面</span>
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ category.description || '未设置描述' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ category.created_at ? formatDate(category.created_at) : '未知' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ category.updated_at ? formatDate(category.updated_at) : '未知' }}</el-descriptions-item>
      </el-descriptions>

      <h3 v-if="subCategories.length > 0">子分类</h3>
      <el-table v-if="subCategories.length > 0" :data="subCategories" stripe>
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="code" label="分类编码" />
        <el-table-column prop="video_count" label="视频数量" />
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <el-button @click="emit('update:visible', false)">关闭</el-button>
      <el-button type="primary" @click="onEdit">编辑分类</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getVideoCategoryDetail, getVideoCategoryList } from '@/service/api/videos/videos';
import type { VideoCategoryItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import { computed, onMounted, ref, watch } from 'vue';

// Props定义
interface Props {
  visible: boolean;
  categoryData: VideoCategoryItem | null;
}

const props = defineProps<Props>();

// Emits定义
interface Emits {
  'update:visible': [value: boolean];
  'edit': [row: VideoCategoryItem];
}

const emit = defineEmits<Emits>();

// 响应式数据
const category = ref<VideoCategoryItem | null>(null);
const allCategories = ref<VideoCategoryItem[]>([]);
const subCategories = ref<VideoCategoryItem[]>([]);

// 计算属性
const categoryId = computed(() => props.categoryData?.id);

// 获取分类详情
const fetchCategoryDetails = async (id: string) => {
  try {
    const res = await getVideoCategoryDetail(id);
    category.value = res.data;
    findSubCategories(id);
  } catch (error) {
    console.error('获取分类详情失败:', error);
  }
};

// 获取所有分类
const fetchAllCategories = async () => {
  try {
    const res = await getVideoCategoryList({ page: { pageNo: 1, pageSize: 1000 } });
    allCategories.value = res.data.list;
  } catch (error) {
    console.error('获取分类列表失败:', error);
  }
};

// 获取父分类名称
const getParentCategoryName = (parentId: string | undefined) => {
  if (!parentId) return '顶级分类';
  const parent = allCategories.value.find(c => c.id === parentId);
  return parent ? parent.name : '未知';
};

// 查找子分类
const findSubCategories = (parentId: string) => {
  subCategories.value = allCategories.value.filter(c => c.parent_id === parentId);
};

// 编辑分类
const onEdit = () => {
  if (category.value) {
    emit('edit', category.value);
    emit('update:visible', false);
  }
};

// 监听对话框显示状态
watch(() => props.visible, (val) => {
  if (val && categoryId.value) {
    fetchAllCategories().then(() => {
      fetchCategoryDetails(categoryId.value!);
    });
  }
});

// 监听分类数据变化
watch(() => props.categoryData, (val) => {
  if (val) {
    category.value = val;
    if (props.visible) {
      findSubCategories(val.id);
    }
  }
});

// 初始化
onMounted(() => {
  fetchAllCategories();
});
</script>

<style scoped>
.detail-container {
  padding: 10px;
}

h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.placeholder-text {
  color: #999;
  font-style: italic;
}
</style> 