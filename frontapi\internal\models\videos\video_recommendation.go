package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// VideoRecommendation 视频推荐表
type VideoRecommendation struct {
	*models.BaseModelStruct
	UserID       string         `gorm:"column:user_id;index" json:"user_id"`                           // 用户ID
	VideoID      string         `gorm:"column:video_id;index" json:"video_id"`                         // 视频ID
	Score        float64        `gorm:"column:score;default:0.0000" json:"score"`                      // 推荐分数
	ReasonType   string         `gorm:"column:reason_type" json:"reason_type"`                         // 推荐原因类型:similar-相似内容,popular-热门,history-历史相关,tag-标签相关
	ReasonDetail string         `gorm:"column:reason_detail" json:"reason_detail"`                     // 推荐原因详情
	IsClicked    uint8          `gorm:"column:is_clicked;default:0" json:"is_clicked"`                 // 是否已点击
	ClickTime    types.JSONTime `gorm:"column:click_time" json:"click_time"`                           // 点击时间
}

// TableName 设置表名
func (VideoRecommendation) TableName() string {
	return "ly_video_recommendations"
}

// 实现BaseModel接口的方法
func (v *VideoRecommendation) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoRecommendation) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoRecommendation) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoRecommendation) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
