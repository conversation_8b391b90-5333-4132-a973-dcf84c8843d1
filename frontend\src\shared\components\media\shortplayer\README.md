# 短视频播放器组件

## 最新修复 (2024)

### 视频导航功能
1. **上一个/下一个视频按钮**：
   - 在右侧交互按钮区域顶部添加视频导航按钮
   - 支持点击切换到上一个或下一个视频
   - 按钮状态根据是否有上一个/下一个视频动态启用/禁用
   - 提供视觉反馈，禁用状态下按钮变灰且不可点击

2. **视频切换逻辑**：
   - 通过 `canGoPrevious` 和 `canGoNext` props 控制按钮状态
   - 点击按钮触发 `previous` 和 `next` 事件
   - 支持视频播放结束后自动播放下一个视频

### 音量控制修复
1. **修复音量按钮逻辑**：
   - 点击音量按钮时，先判断是否有音量控制条显示
   - 如果有音量控制条：
     - 静音状态：开启声音并恢复到之前的音量
     - 非静音状态：切换为静音
   - 如果没有音量控制条：打开音量控制条
   - 双击音量按钮：切换音量控制条显示/隐藏

2. **音量调整功能**：
   - 音量滑块真实有效，能够实时调整播放器音量
   - 自动保存非零音量值，用于静音恢复
   - 音量为0时自动设置为静音状态

### 视频播放器居中和背景填充
1. **视频居中显示**：
   - 播放区域水平和垂直居中
   - 根据视频宽高比自动调整显示方式
   - 竖屏视频：垂直居中，横向等比例缩放
   - 横屏视频：水平居中，纵向等比例缩放

2. **背景渐变填充**：
   - 自动检测视频主要颜色
   - 生成基于视频颜色的径向渐变背景
   - 余下空白区域使用渐变色填充
   - 背景颜色会根据视频内容动态更新

## 使用方法

```vue
<template>
  <ShortPlayer
    :video="currentVideo"
    :autoplay="true"
    :muted="false"
    :can-go-previous="true"
    :can-go-next="true"
    :is-user-logged-in="true"
    :is-danmu-open="true"
    @like="handleLike"
    @comment="handleComment"
    @favorite="handleFavorite"
    @share="handleShare"
    @follow="handleFollow"
    @previous="handlePrevious"
    @next="handleNext"
    @video-end="handleVideoEnd"
  />
</template>
```

## 功能特性

- ✅ 音量控制逻辑修复
- ✅ 真实有效的音量调整
- ✅ 视频居中显示
- ✅ 动态背景渐变填充
- ✅ 弹幕系统
- ✅ 交互按钮（点赞、评论、收藏、分享、关注）
- ✅ 播放控制（播放/暂停、进度条、倍速、清晰度）
- ✅ 全屏支持
- ✅ 移动端适配

## 技术实现

- 基于 xgplayer 播放器
- Vue 3 Composition API
- TypeScript 支持
- SCSS 样式
- Canvas 颜色提取技术

## 组件结构

```
shortPlayer/
├── index.vue                    # 主播放器组件
├── components/
│   ├── PlayerControls.vue       # 播放控制组件
│   └── VideoInfo.vue           # 视频信息组件
└── README.md                   # 说明文档
```

## 使用方法

### 基本用法

```vue
<template>
  <ShortPlayer
    :video="currentVideo"
    :is-liked="false"
    :is-favorited="false"
    :is-following="false"
    :can-go-previous="true"
    :can-go-next="true"
    @like="handleLike"
    @comment="handleComment"
    @favorite="handleFavorite"
    @share="handleShare"
    @follow="handleFollow"
    @previous="handlePrevious"
    @next="handleNext"
    @video-end="handleVideoEnd"
  />
</template>

<script setup>
import ShortPlayer from '@/shared/components/media/shortplayer/index.vue'

const currentVideo = {
  id: '1',
  title: '精彩短视频',
  description: '这是一个很棒的短视频',
  videoUrl: 'https://example.com/video.mp4',
  cover: 'https://example.com/cover.jpg',
  creator: {
    id: '1',
    name: '创作者',
    avatar: 'https://example.com/avatar.jpg'
  },
  like_count: 100,
  comment_count: 50,
  favorite_count: 25,
  share_count: 10
}
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| video | ShortVideo | - | 视频数据对象 |
| autoplay | boolean | true | 是否自动播放 |
| muted | boolean | true | 是否静音 |
| canGoPrevious | boolean | false | 是否可以播放上一个视频 |
| canGoNext | boolean | false | 是否可以播放下一个视频 |
| isUserLoggedIn | boolean | false | 用户是否已登录 |
| isDanmuOpen | boolean | true | 是否开启弹幕 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| like | - | 点赞事件 |
| comment | - | 评论事件 |
| favorite | - | 收藏事件 |
| share | - | 分享事件 |
| follow | - | 关注事件 |
| previous | - | 上一个视频事件 |
| next | - | 下一个视频事件 |
| video-end | - | 视频播放结束事件 |

### 视频数据格式

```typescript
interface ShortVideo {
  id: string
  title: string
  description: string
  videoUrl: string
  cover: string
  creator: {
    id: string
    name: string
    avatar: string
  }
  like_count: number
  comment_count: number
  favorite_count: number
  share_count: number
  view_count: number
  duration?: number
  tags?: string[]
}
```

## 子组件说明

### PlayerControls

播放控制组件，包含：
- 中央播放/暂停按钮
- 静音切换按钮
- 上一个/下一个视频按钮

### VideoInfo

视频信息组件，包含：
- 创作者信息和头像
- 视频标题和描述
- 交互按钮（点赞、评论、收藏、分享）
- 关注按钮

## 自定义样式

组件使用 scoped CSS，可以通过以下方式自定义样式：

```vue
<style>
/* 覆盖播放器容器样式 */
.short-player {
  /* 自定义样式 */
}

/* 覆盖控制按钮样式 */
.player-controls {
  /* 自定义样式 */
}
</style>
```

## 注意事项

1. 确保已安装 xgplayer 依赖：`npm install xgplayer@^3.0.22`
2. 视频 URL 需要支持 CORS 跨域访问
3. 移动端建议使用 HTTPS 协议以支持自动播放
4. 组件会自动处理播放器的创建和销毁，无需手动管理

## 扩展功能

### 添加新的交互按钮

1. 在 `VideoInfo.vue` 中添加新按钮
2. 在主组件中添加对应的事件处理
3. 通过 props 传递状态数据

### 自定义播放器配置

可以在 `index.vue` 中修改 xgplayer 的配置选项：

```javascript
const playerConfig = {
  autoplay: true,
  controls: false,
  fluid: true,
  // 添加更多配置...
}
```

## 性能优化

- 使用 `v-if` 条件渲染减少不必要的组件创建
- 播放器实例会在组件销毁时自动清理
- 事件监听器会在适当时机移除
- 支持视频预加载和缓存策略

## 使用示例

### 基本使用
```vue
<template>
  <ShortPlayer
    :video="currentVideo"
    :autoplay="true"
    :muted="false"
    :can-go-previous="currentIndex > 0"
    :can-go-next="currentIndex < videoList.length - 1"
    :is-user-logged-in="true"
    :is-danmu-open="true"
    @like="handleLike"
    @comment="handleComment"
    @favorite="handleFavorite"
    @share="handleShare"
    @follow="handleFollow"
    @previous="handlePrevious"
    @next="handleNext"
    @video-end="handleVideoEnd"
  />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ShortPlayer from '@/shared/components/media/shortplayer/index.vue'

const videoList = ref([...]) // 视频列表
const currentIndex = ref(0)
const currentVideo = computed(() => videoList.value[currentIndex.value])

const handlePrevious = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}

const handleNext = () => {
  if (currentIndex.value < videoList.value.length - 1) {
    currentIndex.value++
  }
}

const handleVideoEnd = () => {
  // 自动播放下一个视频
  if (currentIndex.value < videoList.value.length - 1) {
    handleNext()
  }
}
</script>
```

### 演示页面
项目中包含了一个完整的演示页面 `demo.vue`，展示了如何使用视频导航功能：
- 包含3个示例视频
- 显示当前视频索引和总数
- 支持手动切换和自动播放下一个视频