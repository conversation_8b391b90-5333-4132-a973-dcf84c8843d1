/**
 * 错误处理插件 - 提供全局错误处理和日志记录功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'
import { storage } from '../../utils/storage'

// 扩展Window接口
declare global {
  interface Window {
    __errorContext?: Record<string, any>
    __errorTags?: string[]
  }
}

/**
 * 错误级别
 */
export type ErrorLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

/**
 * 错误类型
 */
export type ErrorType = 'javascript' | 'promise' | 'resource' | 'network' | 'vue' | 'custom'

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  id: string
  type: ErrorType
  level: ErrorLevel
  message: string
  stack?: string
  source?: string
  line?: number
  column?: number
  timestamp: number
  url: string
  userAgent: string
  userId?: string
  sessionId: string
  context?: Record<string, any>
  tags?: string[]
  fingerprint?: string
}

/**
 * 错误处理配置
 */
export interface ErrorConfig {
  /** 是否启用错误处理 */
  enabled: boolean
  /** 是否启用自动上报 */
  autoReport: boolean
  /** 上报端点 */
  reportEndpoint?: string
  /** 最大错误数量 */
  maxErrors: number
  /** 是否启用本地存储 */
  enableStorage: boolean
  /** 存储键前缀 */
  storagePrefix: string
  /** 是否启用控制台输出 */
  enableConsole: boolean
  /** 过滤规则 */
  filters: Array<(error: ErrorInfo) => boolean>
  /** 采样率 */
  sampleRate: number
  /** 是否启用用户反馈 */
  enableUserFeedback: boolean
  /** 是否启用性能监控 */
  enablePerformance: boolean
  /** 重试配置 */
  retry: {
    enabled: boolean
    maxRetries: number
    retryDelay: number
  }
}

/**
 * 错误统计
 */
export interface ErrorStats {
  totalErrors: number
  errorsByType: Record<ErrorType, number>
  errorsByLevel: Record<ErrorLevel, number>
  recentErrors: ErrorInfo[]
  topErrors: Array<{ fingerprint: string; count: number; lastSeen: number }>
}

/**
 * 错误处理管理器类
 */
export class ErrorManager {
  private config: ErrorConfig
  private errors: ErrorInfo[] = []
  private errorCounts = new Map<string, number>()
  private sessionId: string
  private userId?: string
  private listeners: Array<(error: ErrorInfo) => void> = []

  constructor(config: ErrorConfig) {
    this.config = config
    this.sessionId = this.generateSessionId()
    this.init()
  }

  /**
   * 初始化错误管理器
   */
  private init() {
    if (!this.config.enabled) {
      return
    }

    this.loadFromStorage()
    this.setupGlobalHandlers()
    
    console.log('Error manager initialized')
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalHandlers() {
    // JavaScript错误
    window.addEventListener('error', (event) => {
      this.handleError({
        type: 'javascript',
        level: 'error',
        message: event.message,
        stack: event.error?.stack,
        source: event.filename,
        line: event.lineno,
        column: event.colno
      })
    })

    // Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: 'promise',
        level: 'error',
        message: event.reason?.message || String(event.reason),
        stack: event.reason?.stack
      })
    })

    // 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleError({
          type: 'resource',
          level: 'error',
          message: `Failed to load resource: ${(event.target as any)?.src || (event.target as any)?.href}`,
          source: (event.target as any)?.src || (event.target as any)?.href
        })
      }
    }, true)
  }

  /**
   * 处理错误
   */
  handleError(errorData: Partial<ErrorInfo>): void {
    const error: ErrorInfo = {
      id: this.generateErrorId(),
      type: errorData.type || 'custom',
      level: errorData.level || 'error',
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      source: errorData.source,
      line: errorData.line,
      column: errorData.column,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      userId: this.userId,
      sessionId: this.sessionId,
      context: errorData.context,
      tags: errorData.tags,
      fingerprint: this.generateFingerprint(errorData)
    }

    // 应用过滤器
    if (!this.shouldReport(error)) {
      return
    }

    // 采样
    if (Math.random() > this.config.sampleRate) {
      return
    }

    this.addError(error)
    this.notifyListeners(error)

    if (this.config.enableConsole) {
      this.logToConsole(error)
    }

    if (this.config.autoReport) {
      this.reportError(error)
    }
  }

  /**
   * 添加错误到列表
   */
  private addError(error: ErrorInfo): void {
    this.errors.push(error)
    
    // 更新错误计数
    const count = this.errorCounts.get(error.fingerprint) || 0
    this.errorCounts.set(error.fingerprint, count + 1)

    // 限制错误数量
    if (this.errors.length > this.config.maxErrors) {
      this.errors.shift()
    }

    if (this.config.enableStorage) {
      this.saveToStorage()
    }
  }

  /**
   * 检查是否应该上报错误
   */
  private shouldReport(error: ErrorInfo): boolean {
    return this.config.filters.every(filter => filter(error))
  }

  /**
   * 生成错误指纹
   */
  private generateFingerprint(errorData: Partial<ErrorInfo>): string {
    const parts = [
      errorData.type,
      errorData.message,
      errorData.source,
      errorData.line?.toString(),
      errorData.column?.toString()
    ].filter(Boolean)
    
    return btoa(parts.join('|')).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16)
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 输出到控制台
   */
  private logToConsole(error: ErrorInfo): void {
    const method = error.level === 'error' || error.level === 'fatal' ? 'error' :
                  error.level === 'warn' ? 'warn' : 'log'
    
    console[method](`[${error.level.toUpperCase()}] ${error.message}`, {
      type: error.type,
      stack: error.stack,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString()
    })
  }

  /**
   * 上报错误
   */
  private async reportError(error: ErrorInfo): Promise<void> {
    if (!this.config.reportEndpoint) {
      return
    }

    try {
      await this.sendWithRetry(error)
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  /**
   * 带重试的发送
   */
  private async sendWithRetry(error: ErrorInfo, attempt = 1): Promise<void> {
    try {
      const response = await fetch(this.config.reportEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(error)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (sendError) {
      if (this.config.retry.enabled && attempt < this.config.retry.maxRetries) {
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retry.retryDelay * attempt)
        )
        return this.sendWithRetry(error, attempt + 1)
      }
      throw sendError
    }
  }

  /**
   * 从存储中加载
   */
  private loadFromStorage(): void {
    if (!this.config.enableStorage) {
      return
    }

    try {
      const data = storage.get(this.config.storagePrefix + 'errors')
      if (data) {
        this.errors = JSON.parse(data)
      }

      const countsData = storage.get(this.config.storagePrefix + 'counts')
      if (countsData) {
        const counts = JSON.parse(countsData)
        this.errorCounts = new Map(Object.entries(counts))
      }
    } catch (error) {
      console.error('Failed to load errors from storage:', error)
    }
  }

  /**
   * 保存到存储
   */
  private saveToStorage(): void {
    if (!this.config.enableStorage) {
      return
    }

    try {
      storage.set(
        this.config.storagePrefix + 'errors',
        JSON.stringify(this.errors.slice(-100)) // 只保存最近100个错误
      )

      const countsObj = Object.fromEntries(this.errorCounts.entries())
      storage.set(
        this.config.storagePrefix + 'counts',
        JSON.stringify(countsObj)
      )
    } catch (error) {
      console.error('Failed to save errors to storage:', error)
    }
  }

  /**
   * 手动记录错误
   */
  captureError(error: Error, context?: Record<string, any>, tags?: string[]): void {
    this.handleError({
      type: 'custom',
      level: 'error',
      message: error.message,
      stack: error.stack,
      context,
      tags
    })
  }

  /**
   * 记录消息
   */
  captureMessage(message: string, level: ErrorLevel = 'info', context?: Record<string, any>): void {
    this.handleError({
      type: 'custom',
      level,
      message,
      context
    })
  }

  /**
   * 设置用户ID
   */
  setUserId(userId: string): void {
    this.userId = userId
  }

  /**
   * 设置上下文
   */
  setContext(key: string, value: any): void {
    // 为后续错误设置全局上下文
    if (!window.__errorContext) {
      window.__errorContext = {}
    }
    window.__errorContext[key] = value
  }

  /**
   * 添加标签
   */
  addTag(tag: string): void {
    if (!window.__errorTags) {
      window.__errorTags = []
    }
    if (!window.__errorTags.includes(tag)) {
      window.__errorTags.push(tag)
    }
  }

  /**
   * 获取错误列表
   */
  getErrors(): ErrorInfo[] {
    return [...this.errors]
  }

  /**
   * 获取错误统计
   */
  getStats(): ErrorStats {
    const errorsByType: Record<ErrorType, number> = {
      javascript: 0,
      promise: 0,
      resource: 0,
      network: 0,
      vue: 0,
      custom: 0
    }

    const errorsByLevel: Record<ErrorLevel, number> = {
      debug: 0,
      info: 0,
      warn: 0,
      error: 0,
      fatal: 0
    }

    this.errors.forEach(error => {
      errorsByType[error.type]++
      errorsByLevel[error.level]++
    })

    const topErrors = Array.from(this.errorCounts.entries())
      .map(([fingerprint, count]) => {
        const lastError = this.errors.findLast(e => e.fingerprint === fingerprint)
        return {
          fingerprint,
          count,
          lastSeen: lastError?.timestamp || 0
        }
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    return {
      totalErrors: this.errors.length,
      errorsByType,
      errorsByLevel,
      recentErrors: this.errors.slice(-10),
      topErrors
    }
  }

  /**
   * 清除错误
   */
  clearErrors(): void {
    this.errors = []
    this.errorCounts.clear()
    
    if (this.config.enableStorage) {
      storageUtils.removeItem(this.config.storagePrefix + 'errors')
      storageUtils.removeItem(this.config.storagePrefix + 'counts')
    }
  }

  /**
   * 添加监听器
   */
  addListener(listener: (error: ErrorInfo) => void): void {
    this.listeners.push(listener)
  }

  /**
   * 移除监听器
   */
  removeListener(listener: (error: ErrorInfo) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(error: ErrorInfo): void {
    this.listeners.forEach(listener => {
      try {
        listener(error)
      } catch (listenerError) {
        console.error('Error listener failed:', listenerError)
      }
    })
  }

  /**
   * 销毁错误管理器
   */
  destroy(): void {
    this.listeners = []
    this.clearErrors()
  }
}

// 默认配置
const defaultConfig: ErrorConfig = {
  enabled: true,
  autoReport: false,
  maxErrors: 100,
  enableStorage: true,
  storagePrefix: 'error_',
  enableConsole: true,
  filters: [
    // 过滤掉一些常见的无用错误
    (error) => !error.message.includes('Script error'),
    (error) => !error.message.includes('Non-Error promise rejection captured'),
    (error) => error.source !== 'chrome-extension://'
  ],
  sampleRate: 1.0,
  enableUserFeedback: false,
  enablePerformance: false,
  retry: {
    enabled: true,
    maxRetries: 3,
    retryDelay: 1000
  }
}

// 全局错误管理器实例
export const errorManager = new ErrorManager(defaultConfig)

/**
 * 错误处理插件
 */
export const errorPlugin: Plugin = createPlugin({
  meta: {
    name: 'error',
    version: '1.0.0',
    description: '全局错误处理和日志记录插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 1
  },
  install(context: PluginContext) {
    // 将错误管理器添加到全局属性
    context.app.config.globalProperties.$error = errorManager
    
    // 提供错误管理器
    context.app.provide('error', errorManager)
    
    // Vue错误处理
    context.app.config.errorHandler = (error, instance, info) => {
      errorManager.handleError({
        type: 'vue',
        level: 'error',
        message: error.message,
        stack: error.stack,
        context: {
          componentName: instance?.$options.name || instance?.$options.__name,
          errorInfo: info
        }
      })
    }
    
    console.log('Error plugin installed')
  },
  uninstall() {
    errorManager.destroy()
    console.log('Error plugin uninstalled')
  }
})

/**
 * 使用错误处理的组合式函数
 */
export function useError() {
  return {
    error: errorManager,
    captureError: errorManager.captureError.bind(errorManager),
    captureMessage: errorManager.captureMessage.bind(errorManager),
    setUserId: errorManager.setUserId.bind(errorManager),
    setContext: errorManager.setContext.bind(errorManager),
    addTag: errorManager.addTag.bind(errorManager),
    getErrors: errorManager.getErrors.bind(errorManager),
    getStats: errorManager.getStats.bind(errorManager),
    clearErrors: errorManager.clearErrors.bind(errorManager),
    addListener: errorManager.addListener.bind(errorManager),
    removeListener: errorManager.removeListener.bind(errorManager)
  }
}

export default errorPlugin