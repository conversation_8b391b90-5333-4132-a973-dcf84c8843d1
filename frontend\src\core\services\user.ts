/**
 * 用户服务
 * 处理用户相关的业务逻辑
 */

import { ref, computed } from 'vue'
import type { User, UserProfile, UserStats, UserPreferences } from '@/shared/types'
import { storageUtils } from '@/core/utils'
import { apiRequest } from '@/core/utils/request'

export interface UserService {
  // 当前用户状态
  currentUser: Ref<User | null>
  isLoggedIn: ComputedRef<boolean>
  userStats: Ref<UserStats | null>
  
  // 用户操作
  login(credentials: LoginCredentials): Promise<User>
  logout(): Promise<void>
  register(userData: RegisterData): Promise<User>
  updateProfile(profile: Partial<UserProfile>): Promise<UserProfile>
  updatePreferences(preferences: Partial<UserPreferences>): Promise<void>
  
  // 用户关系
  followUser(userId: string): Promise<void>
  unfollowUser(userId: string): Promise<void>
  getFollowers(userId: string): Promise<User[]>
  getFollowing(userId: string): Promise<User[]>
  
  // 用户数据
  getUserProfile(userId: string): Promise<UserProfile>
  getUserStats(userId: string): Promise<UserStats>
  searchUsers(query: string): Promise<User[]>
  
  // 用户验证
  verifyEmail(token: string): Promise<void>
  resetPassword(email: string): Promise<void>
  changePassword(oldPassword: string, newPassword: string): Promise<void>
}

interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

class UserServiceImpl implements UserService {
  currentUser = ref<User | null>(null)
  userStats = ref<UserStats | null>(null)
  
  isLoggedIn = computed(() => !!this.currentUser.value)
  
  constructor() {
    this.initializeUser()
  }
  
  private async initializeUser() {
    try {
      const token = storageUtils.get('auth_token')
      if (token) {
        const user = await this.getCurrentUser()
        this.currentUser.value = user
        await this.loadUserStats(user.id)
      }
    } catch (error) {
      console.error('初始化用户失败:', error)
      this.logout()
    }
  }
  
  async login(credentials: LoginCredentials): Promise<User> {
    try {
      const response = await apiRequest.post('/auth/login', credentials)
      const { user, token, refreshToken } = response.data
      
      // 存储认证信息
      storageUtils.set('auth_token', token)
      storageUtils.set('refresh_token', refreshToken)
      
      if (credentials.rememberMe) {
        storageUtils.set('remember_user', true)
      }
      
      this.currentUser.value = user
      await this.loadUserStats(user.id)
      
      return user
    } catch (error) {
      throw new Error('登录失败')
    }
  }
  
  async logout(): Promise<void> {
    try {
      await apiRequest.post('/auth/logout')
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清理本地状态
      this.currentUser.value = null
      this.userStats.value = null
      storageUtils.remove('auth_token')
      storageUtils.remove('refresh_token')
      storageUtils.remove('remember_user')
    }
  }
  
  async register(userData: RegisterData): Promise<User> {
    try {
      const response = await apiRequest.post('/auth/register', userData)
      const { user, token, refreshToken } = response.data
      
      storageUtils.set('auth_token', token)
      storageUtils.set('refresh_token', refreshToken)
      
      this.currentUser.value = user
      await this.loadUserStats(user.id)
      
      return user
    } catch (error) {
      throw new Error('注册失败')
    }
  }
  
  async updateProfile(profile: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response = await apiRequest.put('/user/profile', profile)
      const updatedProfile = response.data
      
      // 更新当前用户信息
      if (this.currentUser.value) {
        this.currentUser.value = { ...this.currentUser.value, ...updatedProfile }
      }
      
      return updatedProfile
    } catch (error) {
      throw new Error('更新个人资料失败')
    }
  }
  
  async updatePreferences(preferences: Partial<UserPreferences>): Promise<void> {
    try {
      await apiRequest.put('/user/preferences', preferences)
      
      // 更新本地偏好设置
      if (this.currentUser.value) {
        this.currentUser.value.preferences = {
          ...this.currentUser.value.preferences,
          ...preferences
        }
      }
    } catch (error) {
      throw new Error('更新偏好设置失败')
    }
  }
  
  async followUser(userId: string): Promise<void> {
    try {
      await apiRequest.post(`/user/${userId}/follow`)
      
      // 更新统计数据
      if (this.userStats.value) {
        this.userStats.value.followingCount++
      }
    } catch (error) {
      throw new Error('关注用户失败')
    }
  }
  
  async unfollowUser(userId: string): Promise<void> {
    try {
      await apiRequest.delete(`/user/${userId}/follow`)
      
      // 更新统计数据
      if (this.userStats.value) {
        this.userStats.value.followingCount--
      }
    } catch (error) {
      throw new Error('取消关注失败')
    }
  }
  
  async getFollowers(userId: string): Promise<User[]> {
    try {
      const response = await apiRequest.get(`/user/${userId}/followers`)
      return response.data
    } catch (error) {
      throw new Error('获取粉丝列表失败')
    }
  }
  
  async getFollowing(userId: string): Promise<User[]> {
    try {
      const response = await apiRequest.get(`/user/${userId}/following`)
      return response.data
    } catch (error) {
      throw new Error('获取关注列表失败')
    }
  }
  
  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const response = await apiRequest.get(`/user/${userId}/profile`)
      return response.data
    } catch (error) {
      throw new Error('获取用户资料失败')
    }
  }
  
  async getUserStats(userId: string): Promise<UserStats> {
    try {
      const response = await apiRequest.get(`/user/${userId}/stats`)
      return response.data
    } catch (error) {
      throw new Error('获取用户统计失败')
    }
  }
  
  async searchUsers(query: string): Promise<User[]> {
    try {
      const response = await apiRequest.get('/user/search', { params: { q: query } })
      return response.data
    } catch (error) {
      throw new Error('搜索用户失败')
    }
  }
  
  async verifyEmail(token: string): Promise<void> {
    try {
      await apiRequest.post('/auth/verify-email', { token })
    } catch (error) {
      throw new Error('邮箱验证失败')
    }
  }
  
  async resetPassword(email: string): Promise<void> {
    try {
      await apiRequest.post('/auth/reset-password', { email })
    } catch (error) {
      throw new Error('重置密码失败')
    }
  }
  
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    try {
      await apiRequest.post('/auth/change-password', {
        oldPassword,
        newPassword
      })
    } catch (error) {
      throw new Error('修改密码失败')
    }
  }
  
  private async getCurrentUser(): Promise<User> {
    const response = await apiRequest.get('/auth/me')
    return response.data
  }
  
  private async loadUserStats(userId: string): Promise<void> {
    try {
      this.userStats.value = await this.getUserStats(userId)
    } catch (error) {
      console.error('加载用户统计失败:', error)
    }
  }
}

// 导出单例实例
export const userService = new UserServiceImpl()

// 导出类型
export type { LoginCredentials, RegisterData }