package users

import (
	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreateNotificationRequest 创建通知请求
type CreateNotificationRequest struct {
	ReceiverID string `json:"receiverId" validate:"required"` // 接收者ID
	SenderID   string `json:"senderId"`                       // 发送者ID（可选，系统通知可为空）
	Type       string `json:"type" validate:"required"`       // 通知类型
	Title      string `json:"title" validate:"required"`      // 通知标题
	Content    string `json:"content" validate:"required"`    // 通知内容
	LinkType   string `json:"linkType"`                       // 链接类型：post, video, picture, profile等
	LinkID     string `json:"linkId"`                         // 链接ID
	LinkURL    string `json:"linkUrl"`                        // 链接URL
	IsRead     bool   `json:"isRead"`                         // 是否已读
	ExtraData  string `json:"extraData"`                      // 额外数据（JSON字符串）
}

// UserNotificationService 用户通知服务接口
type UserNotificationService interface {
	base.IExtendedService[users.UserNotification]
}

// userNotificationService 用户通知服务实现
type userNotificationService struct {
	*base.ExtendedService[users.UserNotification]
	notificationRepo repo.UserNotificationRepository
	userRepo         repo.UserRepository
}

// NewUserNotificationService 创建用户通知服务实例
func NewUserNotificationService(
	notificationRepo repo.UserNotificationRepository,
	userRepo repo.UserRepository,
) UserNotificationService {
	return &userNotificationService{
		ExtendedService:  base.NewExtendedService[users.UserNotification](notificationRepo, "user_notification"),
		notificationRepo: notificationRepo,
		userRepo:         userRepo,
	}
}
