import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';

// 导入路由模块
import authRoutes from './modules/auth';
import homeRoutes from './modules/home';
import shortsRoutes from './modules/shorts';
import systemRoutes from './modules/system';
import userRoutes from './modules/user';
import videoRoutes from './modules/video';

// 路由配置
const routes: Array<RouteRecordRaw> = [
    ...homeRoutes,
    ...videoRoutes,
    ...shortsRoutes,
    ...userRoutes,
    ...authRoutes,
    ...systemRoutes,
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import('@/views/errors/404/index.vue')
    }
];

// 创建路由实例
const router = createRouter({
    history: createWebHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            return { top: 0 };
        }
    }
});

// 全局前置守卫
router.beforeEach((to, from, next) => {
    // 可以在此处添加权限验证、页面标题设置等逻辑
    document.title = `MyFirm - ${to.meta.title || '页面'}`;
    next();
});

export default router; 