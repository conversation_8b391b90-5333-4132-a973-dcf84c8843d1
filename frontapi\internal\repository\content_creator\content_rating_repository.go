package content_creator

import (
	"frontapi/internal/models/content_creator"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type ContentRatingRepository interface {
	base.ExtendedRepository[content_creator.ContentRating]
}

type contentRatingRepository struct {
	base.ExtendedRepository[content_creator.ContentRating]
}

func NewContentRatingRepository(db *gorm.DB) ContentRatingRepository {
	return &contentRatingRepository{
		ExtendedRepository: base.NewExtendedRepository[content_creator.ContentRating](db),
	}
}
