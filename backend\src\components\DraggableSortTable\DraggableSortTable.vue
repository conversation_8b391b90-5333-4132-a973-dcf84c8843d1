<template>
  <div class="draggable-sort-table">
    <slot name="table-top"></slot>
    
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      row-key="id"
      @row-drop="onRowDrop"
      v-draggable-columns
      :draggable="draggable"
      @column-drop="onColumnDrop"
      ref="tableRef"
    >
      <slot></slot>
    </el-table>
    
    <slot name="table-bottom"></slot>
    
    <div class="pagination-container" v-if="showPagination">
      <el-pagination
        :current-page="modelValue.page"
        :page-size="modelValue.pageSize"
        :total="total" background
        :page-sizes="pageSizes"
        :layout="paginationLayout"
        @size-change="onSizeChange"
        @current-change="onCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ page: 1, pageSize: 10 })
  },
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  draggable: {
    type: Boolean,
    default: true
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  pageSizes: {
    type: Array as () => number[],
    default: () => [10, 20, 50, 100]
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  idField: {
    type: String,
    default: 'id'
  },
  orderField: {
    type: String,
    default: 'order'
  }
});

const emit = defineEmits([
  'update:modelValue', 
  'row-drop', 
  'column-drop',
  'size-change',
  'current-change',
  'refresh',
  'row-sorted'
]);

// Table reference for accessing the table component
const tableRef = ref<any>(null);

// Computed properties for pagination
const currentPage = computed(() => props.modelValue.page || 1);
const pageSize = computed(() => props.modelValue.pageSize || 10);

// Handle row drop event
const onRowDrop = (params: any) => {
  emit('row-drop', params);
  
  // Also emit row-sorted event for compatibility with old code
  const { oldIndex, newIndex } = params.detail || params;
  
  if (oldIndex !== newIndex) {
    // Create a copy of the data
    const newData = [...props.tableData];
    
    // Get the moved item
    const movedItem = newData[oldIndex];
    
    // Remove the moved item from its original position
    newData.splice(oldIndex, 1);
    
    // Insert the moved item at the new position
    newData.splice(newIndex, 0, movedItem);
    
    // Emit the row-sorted event
    emit('row-sorted', {
      oldIndex,
      newIndex,
      data: newData,
      movedItem
    });
  }
};

// Handle column drop event
const onColumnDrop = (event: any) => {
  emit('column-drop', event);
};

// Pagination handlers
const onSizeChange = (size: number) => {
  emit('update:modelValue', { ...props.modelValue, pageSize: size });
  emit('size-change', size);
};

const onCurrentChange = (page: number) => {
  emit('update:modelValue', { ...props.modelValue, page });
  emit('current-change', page);
};

// Expose methods to parent component
defineExpose({
  refresh: () => {
    emit('refresh');
  },
  getTable: () => tableRef.value,
  refreshSortable: () => {
    // Just a compatibility method, doesn't need to do anything
    // as our implementation uses directive-based dragging
    console.log('refreshSortable called - using new draggable implementation');
  }
});
</script>

<style scoped>
.draggable-sort-table {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

:deep(.el-table .el-table__row) {
  cursor: move;
}

/* Add styles for dragging columns */
:deep(.column-dragging) {
  background-color: var(--el-color-primary-light-9) !important;
  border: 2px dashed var(--el-color-primary) !important;
}

:deep(.el-table th) {
  cursor: move;
}

/* Add styles for dragging rows */
:deep(.el-table__row.row-dragging) {
  background-color: var(--el-color-primary-light-9) !important;
  border: 2px dashed var(--el-color-primary) !important;
  opacity: 0.7;
}

:deep(.el-table__row.row-dragging td) {
  padding: 8px !important;
}
</style> 