package api

import (
	"frontapi/internal/admin/system"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"
	"os"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/websocket/v2"
)

// RegisterUploadRoutes 注册上传相关路由
func RegisterUploadRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 创建上传控制器
	uploadController := system.NewUploadController()

	// 创建文件管理控制器
	fileController := system.NewFileController()

	// 上传相关路由组
	uploadGroup := app.Group("/api/proadm/upload", middleware.AuthRequired())

	// 上传文件路由
	uploadGroup.Post("/image", uploadController.UploadImage)
	uploadGroup.Post("/picture", uploadController.UploadPicture)
	uploadGroup.Post("/video", uploadController.UploadVideo)
	uploadGroup.Post("/video-progress", uploadController.UploadVideoWithProgress)
	uploadGroup.Post("/picture-progress", uploadController.UploadPictureWithProgress)
	uploadGroup.Post("/progress/:id", uploadController.GetUploadProgress)
	uploadGroup.Delete("/file", uploadController.DeleteFile)

	// 文件管理相关路由组
	fileGroup := app.Group("/api/proadm/files", middleware.AuthRequired())

	// 文件管理路由
	fileGroup.Post("/list", fileController.GetFileList)
	fileGroup.Post("/upload", fileController.UploadFile)
	fileGroup.Post("/rename", fileController.RenameFile)
	fileGroup.Post("/delete", fileController.DeleteFile)
	fileGroup.Post("/create-directory", fileController.CreateDirectory)
	fileGroup.Post("/info", fileController.FileInfo)

	// WebSocket路由 - 使用websocket中间件
	app.Use("/api/proadm/ws/upload-progress", middleware.AuthRequired())
	app.Get("/api/proadm/ws/upload-progress", websocket.New(uploadController.UploadProgressWebSocket))

	// 确保上传目录存在
	ensureUploadDirectoryExists("./storage")
	ensureUploadDirectoryExists("./storage/static")
	ensureUploadDirectoryExists("./storage/static/images")
	ensureUploadDirectoryExists("./storage/pictures")
	ensureUploadDirectoryExists("./storage/videos")

	// 静态文件服务
	app.Static("/uploads", "./uploads")
	app.Static("/static", "./storage/static")
	app.Static("/pictures", "./storage/pictures")
	app.Static("/videos", "./storage/videos")
}

// ensureUploadDirectoryExists 确保上传目录存在
func ensureUploadDirectoryExists(dir string) {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		os.MkdirAll(dir, 0755)
	}
}
