/**
 * 文件操作工具函数
 */

/**
 * 文件类型枚举
 */
export enum FileType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  ARCHIVE = 'archive',
  CODE = 'code',
  TEXT = 'text',
  OTHER = 'other'
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  name: string
  size: number
  type: string
  lastModified: number
  extension: string
  category: FileType
}

/**
 * 文件读取选项
 */
export interface ReadFileOptions {
  encoding?: 'utf-8' | 'base64' | 'binary'
  onProgress?: (progress: number) => void
}

/**
 * 文件验证规则
 */
export interface FileValidationRule {
  maxSize?: number // 最大文件大小（字节）
  minSize?: number // 最小文件大小（字节）
  allowedTypes?: string[] // 允许的MIME类型
  allowedExtensions?: string[] // 允许的文件扩展名
  maxFiles?: number // 最大文件数量
}

/**
 * 文件验证结果
 */
export interface FileValidationResult {
  valid: boolean
  errors: string[]
}

/**
 * 图片压缩选项
 */
export interface ImageCompressOptions {
  quality?: number // 压缩质量 0-1
  maxWidth?: number // 最大宽度
  maxHeight?: number // 最大高度
  format?: 'jpeg' | 'png' | 'webp' // 输出格式
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 扩展名
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex > 0 ? filename.slice(lastDotIndex + 1).toLowerCase() : ''
}

/**
 * 获取文件名（不含扩展名）
 * @param filename 文件名
 * @returns 文件名
 */
export function getFileName(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex > 0 ? filename.slice(0, lastDotIndex) : filename
}

/**
 * 获取文件类型分类
 * @param mimeType MIME类型
 * @param extension 文件扩展名
 * @returns 文件类型
 */
export function getFileCategory(mimeType: string, extension: string): FileType {
  const ext = extension.toLowerCase()
  const mime = mimeType.toLowerCase()

  // 图片
  if (mime.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico'].includes(ext)) {
    return FileType.IMAGE
  }

  // 视频
  if (mime.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'].includes(ext)) {
    return FileType.VIDEO
  }

  // 音频
  if (mime.startsWith('audio/') || ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'].includes(ext)) {
    return FileType.AUDIO
  }

  // 文档
  if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp'].includes(ext)) {
    return FileType.DOCUMENT
  }

  // 压缩包
  if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(ext)) {
    return FileType.ARCHIVE
  }

  // 代码
  if (['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'less', 'json', 'xml', 'yaml', 'yml', 'md', 'py', 'java', 'c', 'cpp', 'h', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(ext)) {
    return FileType.CODE
  }

  // 文本
  if (mime.startsWith('text/') || ['txt', 'log', 'csv', 'ini', 'cfg', 'conf'].includes(ext)) {
    return FileType.TEXT
  }

  return FileType.OTHER
}

/**
 * 获取文件信息
 * @param file 文件对象
 * @returns 文件信息
 */
export function getFileInfo(file: File): FileInfo {
  const extension = getFileExtension(file.name)
  const category = getFileCategory(file.type, extension)

  return {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified,
    extension,
    category
  }
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 * @returns 格式化后的大小
 */
export function formatFileSize(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 验证文件
 * @param file 文件对象
 * @param rules 验证规则
 * @returns 验证结果
 */
export function validateFile(file: File, rules: FileValidationRule): FileValidationResult {
  const errors: string[] = []
  const extension = getFileExtension(file.name)

  // 检查文件大小
  if (rules.maxSize && file.size > rules.maxSize) {
    errors.push(`文件大小不能超过 ${formatFileSize(rules.maxSize)}`)
  }

  if (rules.minSize && file.size < rules.minSize) {
    errors.push(`文件大小不能小于 ${formatFileSize(rules.minSize)}`)
  }

  // 检查MIME类型
  if (rules.allowedTypes && rules.allowedTypes.length > 0) {
    if (!rules.allowedTypes.includes(file.type)) {
      errors.push(`不支持的文件类型: ${file.type}`)
    }
  }

  // 检查文件扩展名
  if (rules.allowedExtensions && rules.allowedExtensions.length > 0) {
    if (!rules.allowedExtensions.includes(extension)) {
      errors.push(`不支持的文件扩展名: .${extension}`)
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 验证多个文件
 * @param files 文件数组
 * @param rules 验证规则
 * @returns 验证结果
 */
export function validateFiles(files: File[], rules: FileValidationRule): FileValidationResult {
  const errors: string[] = []

  // 检查文件数量
  if (rules.maxFiles && files.length > rules.maxFiles) {
    errors.push(`最多只能选择 ${rules.maxFiles} 个文件`)
  }

  // 验证每个文件
  files.forEach((file, index) => {
    const result = validateFile(file, rules)
    if (!result.valid) {
      errors.push(`文件 ${index + 1} (${file.name}): ${result.errors.join(', ')}`)
    }
  })

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 读取文件内容
 * @param file 文件对象
 * @param options 读取选项
 * @returns Promise<string>
 */
export function readFile(file: File, options: ReadFileOptions = {}): Promise<string> {
  const { encoding = 'utf-8', onProgress } = options

  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      resolve(reader.result as string)
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    if (onProgress) {
      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100
          onProgress(progress)
        }
      }
    }

    switch (encoding) {
      case 'base64':
        reader.readAsDataURL(file)
        break
      case 'binary':
        reader.readAsBinaryString(file)
        break
      default:
        reader.readAsText(file, 'utf-8')
    }
  })
}

/**
 * 读取文件为ArrayBuffer
 * @param file 文件对象
 * @param onProgress 进度回调
 * @returns Promise<ArrayBuffer>
 */
export function readFileAsArrayBuffer(file: File, onProgress?: (progress: number) => void): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = () => {
      resolve(reader.result as ArrayBuffer)
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    if (onProgress) {
      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100
          onProgress(progress)
        }
      }
    }

    reader.readAsArrayBuffer(file)
  })
}

/**
 * 创建文件下载
 * @param content 文件内容
 * @param filename 文件名
 * @param mimeType MIME类型
 */
export function downloadFile(content: string | Blob, filename: string, mimeType = 'application/octet-stream'): void {
  const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

/**
 * 创建文件选择器
 * @param options 选择器选项
 * @returns Promise<File[]>
 */
export function selectFiles(options: {
  multiple?: boolean
  accept?: string
  directory?: boolean
} = {}): Promise<File[]> {
  const { multiple = false, accept, directory = false } = options

  return new Promise((resolve, reject) => {
    const input = document.createElement('input')
    input.type = 'file'
    input.multiple = multiple
    
    if (accept) {
      input.accept = accept
    }
    
    if (directory) {
      input.webkitdirectory = true
    }

    input.onchange = () => {
      const files = Array.from(input.files || [])
      resolve(files)
    }

    input.oncancel = () => {
      resolve([])
    }

    input.onerror = () => {
      reject(new Error('文件选择失败'))
    }

    input.click()
  })
}

/**
 * 压缩图片
 * @param file 图片文件
 * @param options 压缩选项
 * @returns Promise<Blob>
 */
export function compressImage(file: File, options: ImageCompressOptions = {}): Promise<Blob> {
  const {
    quality = 0.8,
    maxWidth = 1920,
    maxHeight = 1080,
    format = 'jpeg'
  } = options

  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('无法获取canvas上下文'))
      return
    }

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }

      // 设置canvas尺寸
      canvas.width = width
      canvas.height = height

      // 绘制图片
      ctx.drawImage(img, 0, 0, width, height)

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('图片压缩失败'))
          }
        },
        `image/${format}`,
        quality
      )
    }

    img.onerror = () => {
      reject(new Error('图片加载失败'))
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * 获取图片尺寸
 * @param file 图片文件
 * @returns Promise<{width: number, height: number}>
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      })
      URL.revokeObjectURL(img.src)
    }
    
    img.onerror = () => {
      reject(new Error('无法获取图片尺寸'))
      URL.revokeObjectURL(img.src)
    }
    
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 生成文件缩略图
 * @param file 文件对象
 * @param size 缩略图大小
 * @returns Promise<string> base64格式的缩略图
 */
export function generateThumbnail(file: File, size = 200): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('只支持图片文件'))
      return
    }

    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('无法获取canvas上下文'))
      return
    }

    img.onload = () => {
      // 计算缩略图尺寸
      const { width, height } = img
      const ratio = Math.min(size / width, size / height)
      const newWidth = width * ratio
      const newHeight = height * ratio

      canvas.width = newWidth
      canvas.height = newHeight

      // 绘制缩略图
      ctx.drawImage(img, 0, 0, newWidth, newHeight)

      // 转换为base64
      const dataURL = canvas.toDataURL('image/jpeg', 0.8)
      resolve(dataURL)
      
      URL.revokeObjectURL(img.src)
    }

    img.onerror = () => {
      reject(new Error('缩略图生成失败'))
      URL.revokeObjectURL(img.src)
    }

    img.src = URL.createObjectURL(file)
  })
}

/**
 * 文件分片上传
 * @param file 文件对象
 * @param chunkSize 分片大小（字节）
 * @returns 文件分片数组
 */
export function sliceFile(file: File, chunkSize = 1024 * 1024): Blob[] {
  const chunks: Blob[] = []
  let start = 0

  while (start < file.size) {
    const end = Math.min(start + chunkSize, file.size)
    chunks.push(file.slice(start, end))
    start = end
  }

  return chunks
}

/**
 * 计算文件MD5哈希
 * @param file 文件对象
 * @param onProgress 进度回调
 * @returns Promise<string> MD5哈希值
 */
export async function calculateFileMD5(file: File, onProgress?: (progress: number) => void): Promise<string> {
  // 注意：这里需要引入crypto-js库或使用Web Crypto API
  // 这是一个简化版本，实际使用时需要完整的MD5实现
  
  const buffer = await readFileAsArrayBuffer(file, onProgress)
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  
  return hashHex
}

/**
 * 检查文件是否为图片
 * @param file 文件对象
 * @returns 是否为图片
 */
export function isImage(file: File): boolean {
  return file.type.startsWith('image/')
}

/**
 * 检查文件是否为视频
 * @param file 文件对象
 * @returns 是否为视频
 */
export function isVideo(file: File): boolean {
  return file.type.startsWith('video/')
}

/**
 * 检查文件是否为音频
 * @param file 文件对象
 * @returns 是否为音频
 */
export function isAudio(file: File): boolean {
  return file.type.startsWith('audio/')
}

/**
 * 检查文件是否为文档
 * @param file 文件对象
 * @returns 是否为文档
 */
export function isDocument(file: File): boolean {
  const extension = getFileExtension(file.name)
  const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']
  return documentExtensions.includes(extension)
}

/**
 * 文件拖拽处理器
 */
export class FileDragHandler {
  private element: HTMLElement
  private onFiles: (files: File[]) => void
  private onDragEnter?: () => void
  private onDragLeave?: () => void
  private dragCounter = 0

  constructor(
    element: HTMLElement,
    onFiles: (files: File[]) => void,
    options: {
      onDragEnter?: () => void
      onDragLeave?: () => void
    } = {}
  ) {
    this.element = element
    this.onFiles = onFiles
    this.onDragEnter = options.onDragEnter
    this.onDragLeave = options.onDragLeave
    
    this.bindEvents()
  }

  private bindEvents(): void {
    this.element.addEventListener('dragenter', this.handleDragEnter.bind(this))
    this.element.addEventListener('dragleave', this.handleDragLeave.bind(this))
    this.element.addEventListener('dragover', this.handleDragOver.bind(this))
    this.element.addEventListener('drop', this.handleDrop.bind(this))
  }

  private handleDragEnter(event: DragEvent): void {
    event.preventDefault()
    this.dragCounter++
    
    if (this.dragCounter === 1 && this.onDragEnter) {
      this.onDragEnter()
    }
  }

  private handleDragLeave(event: DragEvent): void {
    event.preventDefault()
    this.dragCounter--
    
    if (this.dragCounter === 0 && this.onDragLeave) {
      this.onDragLeave()
    }
  }

  private handleDragOver(event: DragEvent): void {
    event.preventDefault()
  }

  private handleDrop(event: DragEvent): void {
    event.preventDefault()
    this.dragCounter = 0
    
    if (this.onDragLeave) {
      this.onDragLeave()
    }
    
    const files = Array.from(event.dataTransfer?.files || [])
    if (files.length > 0) {
      this.onFiles(files)
    }
  }

  destroy(): void {
    this.element.removeEventListener('dragenter', this.handleDragEnter)
    this.element.removeEventListener('dragleave', this.handleDragLeave)
    this.element.removeEventListener('dragover', this.handleDragOver)
    this.element.removeEventListener('drop', this.handleDrop)
  }
}

/**
 * 创建文件拖拽处理器
 * @param element 目标元素
 * @param onFiles 文件回调
 * @param options 选项
 * @returns 拖拽处理器实例
 */
export function createFileDragHandler(
  element: HTMLElement,
  onFiles: (files: File[]) => void,
  options?: {
    onDragEnter?: () => void
    onDragLeave?: () => void
  }
): FileDragHandler {
  return new FileDragHandler(element, onFiles, options)
}