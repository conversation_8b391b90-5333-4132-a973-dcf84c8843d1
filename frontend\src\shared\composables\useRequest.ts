import {
    del,
    get,
    getData,
    getErrorMessage,
    getPage,
    isSuccess,
    post,
    postPageList,
    put,
    type ApiResponse,
    type PageResponse,
    type RequestOptions
} from '@/core/utils/request'
import { ElLoading, ElMessage } from 'element-plus'
import { computed, ref, type Ref } from 'vue'

/**
 * 请求参数类型
 */
export interface RequestParams<T = any> {
    data?: T
    page?: {
        pageNo: number
        pageSize: number
    }
}

/**
 * 分页请求参数类型
 */
export interface PageRequestParams extends RequestParams {
    page: {
        pageNo: number
        pageSize: number
    }
}

/**
 * 请求状态
 */
export interface RequestState<T = any> {
    /** 响应数据 */
    data: Ref<T | null>
    /** 加载状态 */
    loading: Ref<boolean>
    /** 错误信息 */
    error: Ref<string | null>
    /** 完整响应 */
    response: Ref<ApiResponse<T> | null>
}

/**
 * 请求配置
 */
export interface UseRequestOptions extends RequestOptions {
    /** 是否立即执行 */
    immediate?: boolean
    /** 默认数据 */
    defaultData?: any
    /** 成功回调 */
    onSuccess?: (data: any, response: ApiResponse) => void
    /** 错误回调 */
    onError?: (error: string, response: ApiResponse) => void
    /** 完成回调（无论成功失败） */
    onFinally?: () => void
}

/**
 * 通用请求Hook
 * @param requestFn 请求函数
 * @param options 配置选项
 * @returns 请求状态和执行函数
 */
export function request<T = any>(
    requestFn: (...args: any[]) => Promise<ApiResponse<T>>,
    options: UseRequestOptions = {}
) {
    const {
        immediate = false,
        defaultData = null,
        showLoading = false,
        showError = true,
        showSuccess = false,
        successMessage,
        errorMessage,
        onSuccess,
        onError,
        onFinally
    } = options

    // 响应式状态
    const data = ref<T | null>(defaultData)
    const loading = ref(false)
    const error = ref<string | null>(null)
    const response = ref<ApiResponse<T> | null>(null)

    // 计算属性
    const isLoading = computed(() => loading.value)
    const hasError = computed(() => !!error.value)
    const hasData = computed(() => !!data.value)

    let loadingInstance: any = null

    /**
     * 执行请求
     */
    const execute = async (...args: any[]): Promise<T | null> => {
        try {
            // 重置状态
            error.value = null
            loading.value = true

            // 显示loading
            if (showLoading) {
                loadingInstance = ElLoading.service({
                    lock: true,
                    text: '加载中...',
                    background: 'rgba(0, 0, 0, 0.7)'
                })
            }

            // 执行请求
            const res = await requestFn(...args)
            response.value = res

            // 检查业务状态
            if (isSuccess(res)) {
                data.value = res.data

                // 显示成功消息
                if (showSuccess) {
                    ElMessage.success(successMessage || res.message || '操作成功')
                }

                // 执行成功回调
                onSuccess?.(res.data, res)

                return res.data
            } else {
                // 业务错误
                const errMsg = errorMessage || getErrorMessage(res)
                error.value = errMsg

                // 显示错误消息
                if (showError) {
                    ElMessage.error(errMsg)
                }

                // 执行错误回调
                onError?.(errMsg, res)

                return null
            }
        } catch (err: any) {
            // 网络错误或其他异常
            const errMsg = errorMessage || err.message || '请求失败'
            error.value = errMsg

            // 显示错误消息
            if (showError) {
                ElMessage.error(errMsg)
            }

            // 执行错误回调
            onError?.(errMsg, err)

            return null
        } finally {
            loading.value = false

            // 隐藏loading
            if (loadingInstance) {
                loadingInstance.close()
                loadingInstance = null
            }

            // 执行完成回调
            onFinally?.()
        }
    }

    /**
     * 重置状态
     */
    const reset = () => {
        data.value = defaultData
        loading.value = false
        error.value = null
        response.value = null
    }

    /**
     * 刷新（重新执行上次的请求）
     */
    const refresh = async (...args: any[]) => {
        return execute(...args)
    }

    // 立即执行
    if (immediate) {
        execute()
    }

    return {
        // 状态
        data,
        loading,
        error,
        response,

        // 计算属性
        isLoading,
        hasError,
        hasData,

        // 方法
        execute,
        reset,
        refresh
    }
}

/**
 * 核心请求函数
 */
export function coreRequest<T = any>(config: any): Promise<ApiResponse<T>> {
    return request(config) as any
}

/**
 * 核心POST请求函数
 */
export function corePost<T = any>(url: string, data?: any, page?: { pageNo: number; pageSize: number }, options: RequestOptions = {}) {
    return post<T>(url, data, page, options)
}

/**
 * 核心GET请求函数
 */
export function coreGet<T = any>(url: string, params?: any, options: RequestOptions = {}) {
    return get<T>(url, params, options)
}

/**
 * 核心PUT请求函数
 */
export function corePut<T = any>(url: string, data?: any, options: RequestOptions = {}) {
    return put<T>(url, data, options)
}

/**
 * 核心DELETE请求函数
 */
export function coreDel<T = any>(url: string, data?: any, options: RequestOptions = {}) {
    return del<T>(url, data, options)
}

/**
 * 核心GET分页请求函数
 */
export function coreGetPage<T = any>(url: string, pageNo: number, pageSize: number, params?: any, options: RequestOptions = {}) {
    return getPage<T>(url, pageNo, pageSize, params, options)
}

/**
 * 核心POST分页请求函数
 */
export function corePostPageList<T = any>(url: string, params: { data?: any; page: { pageNo: number; pageSize: number } }, options: RequestOptions = {}) {
    return postPageList<T>(url, params, options)
}

/**
 * 分页请求函数 - 简单封装，用于兼容旧代码
 */
export function page<T = any>(url: string, params?: RequestParams, options: RequestOptions = {}): Promise<ApiResponse<PageResponse<T>>> {
    return getPage<T>(url, params?.page?.pageNo || 1, params?.page?.pageSize || 10, params?.data, options)
}

/**
 * 分页POST请求函数
 */
export function postPage<T = any>(url: string, pageNo: number, pageSize: number, data?: any, options: RequestOptions = {}) {
    return post<PageResponse<T>>(url, data, { pageNo, pageSize }, options)
}

/**
 * GET请求Hook
 */
export function useGet<T = any>(
    url: string,
    params?: any,
    options: UseRequestOptions = {}
) {
    return request<T>(
        () => get<T>(url, params, options),
        options
    )
}

/**
 * POST请求Hook
 */
export function usePost<T = any>(
    url: string,
    options: UseRequestOptions = {}
) {
    return request<T>(
        (data?: any, page?: { pageNo: number; pageSize: number }) =>
            post<T>(url, data, page, options),
        options
    )
}

/**
 * PUT请求Hook
 */
export function usePut<T = any>(
    url: string,
    options: UseRequestOptions = {}
) {
    return request<T>(
        (data?: any) => put<T>(url, data, options),
        options
    )
}

/**
 * DELETE请求Hook
 */
export function useDelete<T = any>(
    url: string,
    options: UseRequestOptions = {}
) {
    return request<T>(
        (data?: any) => del<T>(url, data, options),
        options
    )
}

/**
 * 分页请求Hook
 */
export function usePage<T = any>(
    url: string,
    options: UseRequestOptions = {}
) {
    // 分页状态
    const pageInfo = ref({
        pageNo: 1,
        pageSize: 10,
        total: 0
    })

    // 列表数据
    const list = ref<T[]>([])

    // 请求状态
    const {
        data,
        loading,
        error,
        response,
        isLoading,
        hasError,
        hasData,
        execute: rawExecute,
        reset
    } = request<PageResponse<T>>(
        (data?: any, page?: { pageNo: number; pageSize: number }) =>
            getPage<T>(url, page?.pageNo || pageInfo.value.pageNo, page?.pageSize || pageInfo.value.pageSize, data, options),
        options
    )

    // 执行请求
    const execute = async (data?: any, page?: { pageNo: number; pageSize: number }) => {
        const res = await rawExecute(data, page)
        if (res) {
            list.value = res.list
            pageInfo.value.total = res.total
        }
        return res
    }

    // 切换页码
    const goToPage = (page: number, data?: any) => {
        pageInfo.value.pageNo = page
        return execute(data)
    }

    // 修改每页条数
    const changePageSize = (size: number, data?: any) => {
        pageInfo.value.pageSize = size
        pageInfo.value.pageNo = 1
        return execute(data)
    }

    // 刷新当前页
    const refresh = (data?: any) => {
        return execute(data)
    }

    return {
        // 状态
        data,
        loading,
        error,
        response,
        list,
        pageInfo,

        // 计算属性
        isLoading,
        hasError,
        hasData,

        // 方法
        execute,
        reset,
        refresh,
        goToPage,
        changePageSize
    }
}

/**
 * POST分页请求Hook
 */
export function usePostPage<T = any>(
    url: string,
    options: UseRequestOptions = {}
) {
    // 分页状态
    const pageInfo = ref({
        pageNo: 1,
        pageSize: 10,
        total: 0
    })

    // 列表数据
    const list = ref<T[]>([])

    // 请求状态
    const {
        data,
        loading,
        error,
        response,
        isLoading,
        hasError,
        hasData,
        execute: rawExecute,
        reset
    } = request<PageResponse<T>>(
        (data?: any, page?: { pageNo: number; pageSize: number }) =>
            postPageList<T>(url, { data, page: page || { pageNo: pageInfo.value.pageNo, pageSize: pageInfo.value.pageSize } }, options),
        options
    )

    // 执行请求
    const execute = async (data?: any, page?: { pageNo: number; pageSize: number }) => {
        const res = await rawExecute(data, page)
        if (res) {
            list.value = res.list
            pageInfo.value.total = res.total
        }
        return res
    }

    // 切换页码
    const goToPage = (page: number, data?: any) => {
        pageInfo.value.pageNo = page
        return execute(data)
    }

    // 修改每页条数
    const changePageSize = (size: number, data?: any) => {
        pageInfo.value.pageSize = size
        pageInfo.value.pageNo = 1
        return execute(data)
    }

    // 刷新当前页
    const refresh = (data?: any) => {
        return execute(data)
    }

    return {
        // 状态
        data,
        loading,
        error,
        response,
        list,
        pageInfo,

        // 计算属性
        isLoading,
        hasError,
        hasData,

        // 方法
        execute,
        reset,
        refresh,
        goToPage,
        changePageSize
    }
}

// 导出所有基础请求方法，解决循环引用问题
export {
    del, get, getData,
    getErrorMessage, getPage, isSuccess, post, postPageList, put
}

