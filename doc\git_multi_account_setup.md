# 🔐 Git 多账号配置完成指南

## ✅ 已完成的配置

### 1. SSH 密钥已生成
- **公司账号密钥**: `C:\Users\<USER>\.ssh\id_rsa_jc`
- **个人账号密钥**: `C:\Users\<USER>\.ssh\id_rsa_zh`

### 2. SSH 配置已创建
文件位置: `C:\Users\<USER>\.ssh\config`

配置内容:
```
# 公司账号配置 - <EMAIL>
Host jc.github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_rsa_jc
    IdentitiesOnly yes
    AddKeysToAgent yes

# 个人账号配置 - <EMAIL>  
Host zh.github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_rsa_zh
    IdentitiesOnly yes
    AddKeysToAgent yes

# 默认GitHub配置（可选，指向个人账号）
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_rsa_zh
    IdentitiesOnly yes
    AddKeysToAgent yes
```

## 📋 需要添加到 GitHub 的公钥

### 公司账号 (<EMAIL>)
```
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDNUN6R8pdskZ1D7/NLjfxQl8oFnMC8aQSPdvXgPq01AxktVMF1Z/ghm3ICxdVREMW8PDQZGf2l3TdkO/aGGIt22OZ/7rfZkFWz4iuBgF77Jsoj1WQHNx4OxLThTTDzMySvpiK2bIc3PKo9O5v1ipnP8Zr2uNhyPWdDIaA1ng/XWBNuqblAQX1pmnTlh5pfUmWi9nyFf6wxc1J1YsqozxLs/dQuI1YKoJOJJ3ofiHwSwuWFBWjk9yPlLzL10OzVFRpguPxJvZRvJqc64Tu+zxamdRjzadkzIdjS8PGSPg8ilyVCCL+3wFR41Qpe9bKo6JkS/tpZnQt5DAKmYrDDl/JHAoDIEF+Ni+xnVxcINzrkc77UKfmuib4JODc+mI0XYZ9Gs2aR3L/VakgA1cCEQuXQ/F7R+CxSTHdh9N+h+LHWDcKk0OV2aCZN7jVegrqw5p2ogxUr0fixCInAGN1sMqQ52BGaYa0W7RTS70BQQfKDFpBy/iHGI4lD5Y+1picAS9NZl8zVK+J/nbFDqLJpRF+a2dNItoinr0N+WV+TTjKs1LJkILRmTqQ5rXYMmk3iMh2hY3a2EUnXbNoTS68dGACTxnqFEyBy3hCjPX36r7kKaJbfMwuKCDNydCLWvWXb3DGuKmocKJsnbQwR+8RHu0PRP7GVBCp5pFZQ3MKSvNCGaQ== <EMAIL>
```

### 个人账号 (<EMAIL>)
```
ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQDSl3bhIruVrYiZGu528ANHNtdHH9nJhAfF4JFyvBqMoUAvseV+RQoVVyRSg8uXJTvvByOLTffVHA5iHtkDGcd2U95/BItx6u3Tk6rBTMc5ci8bdGSAJaSPX5LBXiG2fVyw8nliwTDHGNyv/BR2Q1IelkmBZkNrQhXbve9h1cv0IdSx75lfSOy5hU0ISYOuA8LrQvXxByb89kwf4OOkTSUAfVv0brYr/O+jGpn7uj9jjavr1IkiREayAIuiL0uzyuxsekaBa6TpeylymnZL1AL2x4/B6VIAmDYHvw2SyvKtvG+sSIv3n9bJPe7oYNvZrQbWoBh7Y10PhFglu5MyfL+EoooSQxt5AmttBJ3HEttWfbdDpvRXTkn6glKj67YDkAEMKVUbWvVIp993l90Gfyjta1wSB3BkyImt5zXgLg8ZfckXROQeCUwq3K9XJKSuvCPbUAoaqpE+P6LGIIH1npCFwJv7DUju/Y2uM0JYIjKEN/L20d3Kb1msJ1QUeq1eIt0WuztdjlkrhA6O7oUDtvX3SYZC9ZSndbHzWA4iK/FRDOzIuVl3VyvEXmQPrNegiPUgB7CQgCFPwxyCj6cSbXY03qismKREFZKohp+rDz1axTwz/9k6L8JHPEciJVuYGzqw8J2C+WZNZbO69z0HdtapFjCUPAS/NPjoqHMNGdeVaQ== <EMAIL>
```

## 🚀 使用指南

### 1. 在 GitHub 上添加公钥

#### 公司账号 (<EMAIL>)
1. 登录公司 GitHub 账号
2. 进入 Settings > SSH and GPG keys
3. 点击 "New SSH key"
4. 标题: "Windows PC - Company Key"
5. 复制粘贴上面的公司账号公钥

#### 个人账号 (<EMAIL>)
1. 登录个人 GitHub 账号
2. 进入 Settings > SSH and GPG keys
3. 点击 "New SSH key"
4. 标题: "Windows PC - Personal Key"
5. 复制粘贴上面的个人账号公钥

### 2. 使用不同账号克隆仓库

#### 克隆公司仓库
```bash
# 使用公司账号克隆
<NAME_EMAIL>:company/repo.git

# 进入仓库设置用户信息
cd repo
git config user.name "Your Company Name"
git config user.email "<EMAIL>"
```

#### 克隆个人仓库
```bash
# 使用个人账号克隆
<NAME_EMAIL>:username/repo.git

# 进入仓库设置用户信息
cd repo
git config user.name "Your Personal Name"
git config user.email "<EMAIL>"
```

### 3. 为现有仓库更改远程URL

#### 切换到公司账号
```bash
git remote set-<NAME_EMAIL>:company/repo.git
git config user.name "Your Company Name"
git config user.email "<EMAIL>"
```

#### 切换到个人账号
```bash
git remote set-<NAME_EMAIL>:username/repo.git
git config user.name "Your Personal Name"
git config user.email "<EMAIL>"
```

### 4. 设置全局默认账号（可选）
```bash
# 设置个人账号为默认
git config --global user.name "Your Personal Name"
git config --global user.email "<EMAIL>"
```

## 🔧 故障排除

### 1. 测试 SSH 连接
```bash
# 测试公司账号连接
ssh -T *****************

# 测试个人账号连接
ssh -T *****************
```

### 2. 如果连接失败
```bash
# 检查 SSH 配置
ssh -T -v *****************

# 添加密钥到 SSH Agent（如果需要）
ssh-add ~/.ssh/id_rsa_jc
ssh-add ~/.ssh/id_rsa_zh
```

### 3. 验证当前仓库配置
```bash
# 查看当前仓库的远程URL
git remote -v

# 查看当前仓库的用户配置
git config user.name
git config user.email
```

## 🎯 最佳实践

1. **项目文件夹组织**:
   - 创建 `~/work/company/` 文件夹存放公司项目
   - 创建 `~/work/personal/` 文件夹存放个人项目

2. **自动化脚本**:
   可以创建批处理脚本来快速切换账号配置

3. **安全建议**:
   - 定期更新 SSH 密钥
   - 不要在公共场所使用私钥
   - 保持密钥文件的权限设置

## ✅ 配置完成清单

- [x] 生成公司账号 SSH 密钥
- [x] 生成个人账号 SSH 密钥  
- [x] 创建 SSH 配置文件
- [x] 获取公钥内容
- [ ] 将公钥添加到对应 GitHub 账号
- [ ] 测试 SSH 连接
- [ ] 配置 Git 用户信息

**下一步**: 请将上面的公钥分别添加到对应的 GitHub 账号中，然后测试 SSH 连接！ 