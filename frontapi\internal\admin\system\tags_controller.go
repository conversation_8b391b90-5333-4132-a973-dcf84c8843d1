package system

import (
	"frontapi/internal/admin"
	sysModel "frontapi/internal/models/system"
	sysSrv "frontapi/internal/service/system"
	sysValidator "frontapi/internal/validation/system"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// TagController 标签控制器
type TagController struct {
	admin.BaseController // 继承BaseController
	TagService           sysSrv.TagService
}

// NewTagController 创建标签控制器实例
func NewTagController(tagService sysSrv.TagService) *TagController {
	return &TagController{
		TagService: tagService,
	}
}

// ListTags 获取标签列表
func (c *TagController) ListTags(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	name := reqInfo.Get("name").GetString()
	typeVal := -1 // 默认值-1表示不过滤
	_ = c.GetIntegerValueWithDataWrapper(ctx, "type", &typeVal)
	status := -999 // 默认值-1表示不过滤
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)

	// 排序参数
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询标签列表
	tagList, total, err := c.TagService.List(ctx.Context(), map[string]interface{}{
		"name":   name,
		"type":   typeVal,
		"status": status,
	}, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取标签列表失败: "+err.Error())
	}

	// 返回标签列表
	return c.SuccessList(ctx, tagList, total, page, pageSize)
}

// GetTag 获取标签详情
func (c *TagController) GetTag(ctx *fiber.Ctx) error {
	// 获取标签ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询标签
	tag, err := c.TagService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取标签详情失败: "+err.Error())
	}

	if tag == nil {
		return c.NotFound(ctx, "标签不存在")
	}

	// 返回标签详情
	return c.Success(ctx, tag)
}

// CreateTag 创建标签
func (c *TagController) CreateTag(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req sysValidator.CreateTagRequest

	// 解析和验证data字段包装的请求
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return err // 验证器已经处理了错误响应
	}
	var tag *sysModel.Tag
	if err := utils.SmartCopy(tag, req); err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	// 创建标签
	id, err := c.TagService.Create(ctx.Context(), tag)
	if err != nil {
		return c.InternalServerError(ctx, "创建标签失败: "+err.Error())
	}

	return c.Success(ctx, map[string]interface{}{
		"id":      id,
		"message": "创建标签成功",
	})
}

// UpdateTag 更新标签
func (c *TagController) UpdateTag(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req sysValidator.UpdateTagRequest

	// 解析和验证data字段包装的请求
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return err // 验证器已经处理了错误响应
	}
	var tag *sysModel.Tag
	if err := utils.SmartCopy(tag, req); err != nil {
		return c.BadRequest(ctx, "参数解析失败", nil)
	}
	// 更新标签
	err := c.TagService.Update(ctx.Context(), tag)
	if err != nil {
		return c.InternalServerError(ctx, "更新标签失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新标签成功")
}

// UpdateTagStatus 更新标签状态
func (c *TagController) UpdateTagStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req sysValidator.UpdateTagStatusRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数: "+err.Error(), nil)
	}

	// 更新标签状态
	err := c.TagService.UpdateStatus(ctx.Context(), req.ID, req.Status)
	if err != nil {
		return c.InternalServerError(ctx, "更新标签状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新标签状态成功")
}

// DeleteTag 删除标签
func (c *TagController) DeleteTag(ctx *fiber.Ctx) error {
	// 获取标签ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}
	// 删除标签
	err = c.TagService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除标签失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除标签成功")
}
