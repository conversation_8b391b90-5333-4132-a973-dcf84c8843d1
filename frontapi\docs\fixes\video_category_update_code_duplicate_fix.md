# 视频分类更新Code字段重复错误修复

## 问题描述

调用 `http://localhost:8081/api/proadm/video-categories/update` 接口更新视频分类时报错：
```
更新分类失败: 更新失败: Error 1062 (23000): Duplicate entry '' for key 'uk_code'
```

## 问题分析

### 1. 数据库约束
视频分类表 `ly_video_categories` 中的 `code` 字段有唯一索引约束（`uk_code`）：
```go
Code string `gorm:"column:code;uniqueIndex" json:"code"`
```

### 2. 验证器结构不一致
- **CreateCategoryRequest** 包含 `Code` 字段并且是必填项：
  ```go
  Code string `json:"code" validate:"required|minLen:3|maxLen:50"`
  ```
- **UpdateCategoryRequest** 缺少 `Code` 字段：
  ```go
  // 原始结构中没有Code字段
  type UpdateCategoryRequest struct {
      Name          string  `json:"name" validate:"required|minLen:2|maxLen:50"`
      Icon          string  `json:"icon"`
      Color         string  `json:"color"`
      // 缺少Code字段
      Description   string  `json:"description"`
      // ...其他字段
  }
  ```

### 3. 控制器实现不一致
- **创建操作** 正确调用了 `SetCode` 方法：
  ```go
  category.SetName(req.Name)
  category.SetCode(req.Code)  // ✅ 正确设置
  category.SetDescription(req.Description)
  ```
- **更新操作** 缺少 `SetCode` 调用：
  ```go
  category.SetName(req.Name)
  // category.SetCode(req.Code)  // ❌ 缺少这一行
  category.SetDescription(req.Description)
  ```

### 4. 问题原因
1. 前端传递了 `code` 字段，但后端验证器没有定义该字段
2. 控制器没有调用 `SetCode` 方法设置值
3. SmartCopy 可能将 `code` 字段设置为空字符串
4. 数据库中多个记录的 `code` 为空字符串，违反唯一约束

## 解决方案

### 1. 修复验证器结构
在 `UpdateCategoryRequest` 中添加 `Code` 字段：

```go
// UpdateCategoryRequest 更新分类请求
type UpdateCategoryRequest struct {
    Name          string  `json:"name" validate:"required|minLen:2|maxLen:50"`
    Icon          string  `json:"icon"`
    Color         string  `json:"color"`
    Code          string  `json:"code" validate:"required|minLen:3|maxLen:50"` // ✅ 添加Code字段
    Description   string  `json:"description"`
    ParentID      *string `json:"parentID"`
    Uri           string  `json:"uri"`
    Image         string  `json:"image"`
    SortOrder     int     `json:"sortOrder"`
    FeaturedOrder int     `json:"featuredOrder"`
    IsFeatured    int     `json:"isFeatured"`
    Status        int     `json:"status"`
}
```

### 2. 修复控制器实现
在更新方法中添加 `SetCode` 调用：

```go
func (h *VideoCategoryController) UpdateVideoCategory(c *fiber.Ctx) error {
    // ...前面的代码

    // 处理需要调用特殊方法的字段
    category.SetName(req.Name)
    category.SetCode(req.Code)        // ✅ 添加这一行
    category.SetDescription(req.Description)
    category.SetStatus(int8(req.Status))

    // ...后面的代码
}
```

## 修改的文件

1. **验证器**: `frontapi/internal/validation/videos/video_category.go`
   - 在 `UpdateCategoryRequest` 中添加 `Code` 字段
   - 添加与创建请求一致的验证规则

2. **控制器**: `frontapi/internal/admin/videos/video_category_controller.go`
   - 在 `UpdateVideoCategory` 方法中添加 `category.SetCode(req.Code)` 调用

## 修复效果

- ✅ 前后端字段定义保持一致
- ✅ 创建和更新操作的处理逻辑保持一致
- ✅ 确保 Code 字段正确更新，避免空值重复
- ✅ 满足数据库唯一约束要求

## 测试验证

修复后，更新视频分类时：
1. 前端必须传递有效的 `code` 字段（长度3-50字符）
2. 后端正确验证并设置 `code` 字段值
3. 数据库更新不会产生空值重复错误

## 相关约束

- `code` 字段在数据库中有唯一索引约束
- `code` 字段长度必须在3-50字符之间
- 更新时必须提供有效的 `code` 值

## 预防措施

为了避免类似问题，建议：
1. 保持创建和更新请求结构的一致性
2. 控制器中的字段设置逻辑保持一致
3. 对有唯一约束的字段进行特别关注
4. 添加集成测试覆盖创建和更新场景

## 注意事项

此修复确保了数据一致性，但更新时必须提供 `code` 字段。如果前端没有传递 `code` 字段，需要相应更新前端代码以包含该字段。 