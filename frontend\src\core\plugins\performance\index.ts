/**
 * 性能监控插件 - 提供应用性能监控和优化功能
 */

import type { Plugin, PluginContext } from '../types'
import { createPlugin } from '../index'

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  /** 页面加载时间 */
  pageLoadTime: number
  /** DOM内容加载时间 */
  domContentLoadedTime: number
  /** 首次内容绘制时间 */
  firstContentfulPaint: number
  /** 最大内容绘制时间 */
  largestContentfulPaint: number
  /** 首次输入延迟 */
  firstInputDelay: number
  /** 累积布局偏移 */
  cumulativeLayoutShift: number
  /** 交互到下次绘制时间 */
  interactionToNextPaint: number
  /** 内存使用情况 */
  memoryUsage?: MemoryInfo
  /** 网络信息 */
  networkInfo?: NetworkInformation
}

/**
 * 内存信息
 */
export interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

/**
 * 网络信息
 */
export interface NetworkInformation {
  effectiveType: string
  downlink: number
  rtt: number
  saveData: boolean
}

/**
 * 性能条目
 */
export interface PerformanceEntry {
  id: string
  name: string
  type: 'navigation' | 'resource' | 'measure' | 'mark' | 'paint' | 'layout-shift' | 'largest-contentful-paint' | 'first-input'
  startTime: number
  duration: number
  timestamp: number
  details?: Record<string, any>
}

/**
 * 性能配置
 */
export interface PerformanceConfig {
  /** 是否启用性能监控 */
  enabled: boolean
  /** 是否自动收集指标 */
  autoCollect: boolean
  /** 是否启用Web Vitals监控 */
  enableWebVitals: boolean
  /** 是否启用资源监控 */
  enableResourceMonitoring: boolean
  /** 是否启用用户交互监控 */
  enableUserInteractionMonitoring: boolean
  /** 是否启用内存监控 */
  enableMemoryMonitoring: boolean
  /** 采样率 */
  sampleRate: number
  /** 最大条目数量 */
  maxEntries: number
  /** 上报端点 */
  reportEndpoint?: string
  /** 是否自动上报 */
  autoReport: boolean
  /** 上报间隔（毫秒） */
  reportInterval: number
  /** 阈值配置 */
  thresholds: {
    fcp: number // 首次内容绘制阈值
    lcp: number // 最大内容绘制阈值
    fid: number // 首次输入延迟阈值
    cls: number // 累积布局偏移阈值
    inp: number // 交互到下次绘制阈值
  }
}

/**
 * 性能统计
 */
export interface PerformanceStats {
  totalEntries: number
  entriesByType: Record<string, number>
  averageMetrics: Partial<PerformanceMetrics>
  worstMetrics: Partial<PerformanceMetrics>
  bestMetrics: Partial<PerformanceMetrics>
  recentEntries: PerformanceEntry[]
}

/**
 * 性能管理器类
 */
export class PerformanceManager {
  private config: PerformanceConfig
  private entries: PerformanceEntry[] = []
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []
  private listeners: Array<(entry: PerformanceEntry) => void> = []
  private reportTimer?: number
  private sessionId: string

  constructor(config: PerformanceConfig) {
    this.config = config
    this.sessionId = this.generateSessionId()
    this.init()
  }

  /**
   * 初始化性能管理器
   */
  private init() {
    if (!this.config.enabled) {
      return
    }

    if (this.config.autoCollect) {
      this.startAutoCollection()
    }

    if (this.config.autoReport && this.config.reportEndpoint) {
      this.startAutoReporting()
    }

    console.log('Performance manager initialized')
  }

  /**
   * 开始自动收集
   */
  private startAutoCollection() {
    // 监控导航性能
    this.observeNavigation()

    // 监控Web Vitals
    if (this.config.enableWebVitals) {
      this.observeWebVitals()
    }

    // 监控资源加载
    if (this.config.enableResourceMonitoring) {
      this.observeResources()
    }

    // 监控用户交互
    if (this.config.enableUserInteractionMonitoring) {
      this.observeUserInteractions()
    }

    // 监控内存使用
    if (this.config.enableMemoryMonitoring) {
      this.observeMemoryUsage()
    }
  }

  /**
   * 监控导航性能
   */
  private observeNavigation() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming
            this.addEntry({
              id: this.generateEntryId(),
              name: 'navigation',
              type: 'navigation',
              startTime: navEntry.startTime,
              duration: navEntry.loadEventEnd - navEntry.startTime,
              timestamp: Date.now(),
              details: {
                domContentLoadedTime: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
                loadTime: navEntry.loadEventEnd - navEntry.loadEventStart,
                redirectTime: navEntry.redirectEnd - navEntry.redirectStart,
                dnsTime: navEntry.domainLookupEnd - navEntry.domainLookupStart,
                connectTime: navEntry.connectEnd - navEntry.connectStart,
                requestTime: navEntry.responseStart - navEntry.requestStart,
                responseTime: navEntry.responseEnd - navEntry.responseStart,
                domProcessingTime: navEntry.domComplete - navEntry.domLoading
              }
            })

            // 更新指标
            this.metrics.pageLoadTime = navEntry.loadEventEnd - navEntry.startTime
            this.metrics.domContentLoadedTime = navEntry.domContentLoadedEventEnd - navEntry.startTime
          }
        }
      })

      observer.observe({ entryTypes: ['navigation'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控Web Vitals
   */
  private observeWebVitals() {
    if ('PerformanceObserver' in window) {
      // 首次内容绘制 (FCP)
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime
            this.addEntry({
              id: this.generateEntryId(),
              name: 'first-contentful-paint',
              type: 'paint',
              startTime: entry.startTime,
              duration: 0,
              timestamp: Date.now()
            })
          }
        }
      })
      paintObserver.observe({ entryTypes: ['paint'] })
      this.observers.push(paintObserver)

      // 最大内容绘制 (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.largestContentfulPaint = entry.startTime
          this.addEntry({
            id: this.generateEntryId(),
            name: 'largest-contentful-paint',
            type: 'largest-contentful-paint',
            startTime: entry.startTime,
            duration: 0,
            timestamp: Date.now(),
            details: {
              element: (entry as any).element?.tagName,
              size: (entry as any).size
            }
          })
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      this.observers.push(lcpObserver)

      // 首次输入延迟 (FID)
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime
          this.addEntry({
            id: this.generateEntryId(),
            name: 'first-input-delay',
            type: 'first-input',
            startTime: entry.startTime,
            duration: (entry as any).processingStart - entry.startTime,
            timestamp: Date.now(),
            details: {
              eventType: (entry as any).name
            }
          })
        }
      })
      fidObserver.observe({ entryTypes: ['first-input'] })
      this.observers.push(fidObserver)

      // 累积布局偏移 (CLS)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value
            this.metrics.cumulativeLayoutShift = clsValue
            this.addEntry({
              id: this.generateEntryId(),
              name: 'layout-shift',
              type: 'layout-shift',
              startTime: entry.startTime,
              duration: 0,
              timestamp: Date.now(),
              details: {
                value: (entry as any).value,
                hadRecentInput: (entry as any).hadRecentInput
              }
            })
          }
        }
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })
      this.observers.push(clsObserver)
    }
  }

  /**
   * 监控资源加载
   */
  private observeResources() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const resourceEntry = entry as PerformanceResourceTiming
          this.addEntry({
            id: this.generateEntryId(),
            name: resourceEntry.name,
            type: 'resource',
            startTime: resourceEntry.startTime,
            duration: resourceEntry.duration,
            timestamp: Date.now(),
            details: {
              initiatorType: resourceEntry.initiatorType,
              transferSize: resourceEntry.transferSize,
              encodedBodySize: resourceEntry.encodedBodySize,
              decodedBodySize: resourceEntry.decodedBodySize,
              redirectTime: resourceEntry.redirectEnd - resourceEntry.redirectStart,
              dnsTime: resourceEntry.domainLookupEnd - resourceEntry.domainLookupStart,
              connectTime: resourceEntry.connectEnd - resourceEntry.connectStart,
              requestTime: resourceEntry.responseStart - resourceEntry.requestStart,
              responseTime: resourceEntry.responseEnd - resourceEntry.responseStart
            }
          })
        }
      })

      observer.observe({ entryTypes: ['resource'] })
      this.observers.push(observer)
    }
  }

  /**
   * 监控用户交互
   */
  private observeUserInteractions() {
    // 监控点击事件
    document.addEventListener('click', (event) => {
      this.measureUserInteraction('click', event.target as Element)
    })

    // 监控键盘事件
    document.addEventListener('keydown', (event) => {
      this.measureUserInteraction('keydown', event.target as Element)
    })

    // 监控滚动事件
    let scrollTimeout: number
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout)
      scrollTimeout = window.setTimeout(() => {
        this.measureUserInteraction('scroll', document.documentElement)
      }, 100)
    })
  }

  /**
   * 测量用户交互
   */
  private measureUserInteraction(type: string, target: Element) {
    const startTime = performance.now()
    
    // 使用requestAnimationFrame来测量到下次绘制的时间
    requestAnimationFrame(() => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      this.addEntry({
        id: this.generateEntryId(),
        name: `user-${type}`,
        type: 'measure',
        startTime,
        duration,
        timestamp: Date.now(),
        details: {
          interactionType: type,
          targetElement: target.tagName.toLowerCase(),
          targetId: target.id,
          targetClass: target.className
        }
      })
    })
  }

  /**
   * 监控内存使用
   */
  private observeMemoryUsage() {
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory
        this.metrics.memoryUsage = {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit
        }

        this.addEntry({
          id: this.generateEntryId(),
          name: 'memory-usage',
          type: 'measure',
          startTime: performance.now(),
          duration: 0,
          timestamp: Date.now(),
          details: this.metrics.memoryUsage
        })
      }

      // 定期检查内存使用情况
      setInterval(checkMemory, 30000) // 每30秒检查一次
      checkMemory() // 立即检查一次
    }
  }

  /**
   * 添加性能条目
   */
  private addEntry(entry: PerformanceEntry) {
    // 采样检查
    if (Math.random() > this.config.sampleRate) {
      return
    }

    this.entries.push(entry)
    
    // 限制条目数量
    if (this.entries.length > this.config.maxEntries) {
      this.entries.shift()
    }

    // 通知监听器
    this.notifyListeners(entry)
  }

  /**
   * 手动标记
   */
  mark(name: string, detail?: Record<string, any>): void {
    const startTime = performance.now()
    performance.mark(name)
    
    this.addEntry({
      id: this.generateEntryId(),
      name,
      type: 'mark',
      startTime,
      duration: 0,
      timestamp: Date.now(),
      details: detail
    })
  }

  /**
   * 手动测量
   */
  measure(name: string, startMark?: string, endMark?: string): void {
    const startTime = performance.now()
    
    if (startMark && endMark) {
      performance.measure(name, startMark, endMark)
    } else {
      performance.measure(name)
    }
    
    const entries = performance.getEntriesByName(name, 'measure')
    const entry = entries[entries.length - 1]
    
    if (entry) {
      this.addEntry({
        id: this.generateEntryId(),
        name,
        type: 'measure',
        startTime: entry.startTime,
        duration: entry.duration,
        timestamp: Date.now()
      })
    }
  }

  /**
   * 开始自动上报
   */
  private startAutoReporting() {
    this.reportTimer = window.setInterval(() => {
      this.reportMetrics()
    }, this.config.reportInterval)
  }

  /**
   * 上报指标
   */
  async reportMetrics(): Promise<void> {
    if (!this.config.reportEndpoint) {
      return
    }

    try {
      const data = {
        sessionId: this.sessionId,
        timestamp: Date.now(),
        metrics: this.metrics,
        entries: this.entries.slice(-50), // 只发送最近50个条目
        stats: this.getStats()
      }

      await fetch(this.config.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })
    } catch (error) {
      console.error('Failed to report performance metrics:', error)
    }
  }

  /**
   * 获取当前指标
   */
  getMetrics(): Partial<PerformanceMetrics> {
    // 更新网络信息
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      this.metrics.networkInfo = {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      }
    }

    return { ...this.metrics }
  }

  /**
   * 获取性能条目
   */
  getEntries(type?: string): PerformanceEntry[] {
    if (type) {
      return this.entries.filter(entry => entry.type === type)
    }
    return [...this.entries]
  }

  /**
   * 获取统计信息
   */
  getStats(): PerformanceStats {
    const entriesByType: Record<string, number> = {}
    const metricValues: Record<string, number[]> = {}

    this.entries.forEach(entry => {
      entriesByType[entry.type] = (entriesByType[entry.type] || 0) + 1
      
      if (entry.duration > 0) {
        if (!metricValues[entry.type]) {
          metricValues[entry.type] = []
        }
        metricValues[entry.type].push(entry.duration)
      }
    })

    const averageMetrics: Partial<PerformanceMetrics> = {}
    const worstMetrics: Partial<PerformanceMetrics> = {}
    const bestMetrics: Partial<PerformanceMetrics> = {}

    Object.entries(metricValues).forEach(([type, values]) => {
      if (values.length > 0) {
        const avg = values.reduce((sum, val) => sum + val, 0) / values.length
        const max = Math.max(...values)
        const min = Math.min(...values)
        
        ;(averageMetrics as any)[type] = avg
        ;(worstMetrics as any)[type] = max
        ;(bestMetrics as any)[type] = min
      }
    })

    return {
      totalEntries: this.entries.length,
      entriesByType,
      averageMetrics,
      worstMetrics,
      bestMetrics,
      recentEntries: this.entries.slice(-10)
    }
  }

  /**
   * 清除条目
   */
  clearEntries(): void {
    this.entries = []
  }

  /**
   * 添加监听器
   */
  addListener(listener: (entry: PerformanceEntry) => void): void {
    this.listeners.push(listener)
  }

  /**
   * 移除监听器
   */
  removeListener(listener: (entry: PerformanceEntry) => void): void {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知监听器
   */
  private notifyListeners(entry: PerformanceEntry): void {
    this.listeners.forEach(listener => {
      try {
        listener(entry)
      } catch (error) {
        console.error('Performance listener error:', error)
      }
    })
  }

  /**
   * 生成条目ID
   */
  private generateEntryId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  /**
   * 销毁性能管理器
   */
  destroy(): void {
    // 停止观察器
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []

    // 停止自动上报
    if (this.reportTimer) {
      clearInterval(this.reportTimer)
      this.reportTimer = undefined
    }

    // 清除监听器
    this.listeners = []
    
    // 清除条目
    this.clearEntries()
  }
}

// 默认配置
const defaultConfig: PerformanceConfig = {
  enabled: true,
  autoCollect: true,
  enableWebVitals: true,
  enableResourceMonitoring: true,
  enableUserInteractionMonitoring: true,
  enableMemoryMonitoring: true,
  sampleRate: 1.0,
  maxEntries: 1000,
  autoReport: false,
  reportInterval: 60000, // 1分钟
  thresholds: {
    fcp: 1800, // 1.8秒
    lcp: 2500, // 2.5秒
    fid: 100,  // 100毫秒
    cls: 0.1,  // 0.1
    inp: 200   // 200毫秒
  }
}

// 全局性能管理器实例
export const performanceManager = new PerformanceManager(defaultConfig)

/**
 * 性能监控插件
 */
export const performancePlugin: Plugin = createPlugin({
  meta: {
    name: 'performance',
    version: '1.0.0',
    description: '应用性能监控和优化插件',
    author: 'MyFirm Team'
  },
  config: {
    enabled: true,
    priority: 3
  },
  install(context: PluginContext) {
    // 将性能管理器添加到全局属性
    context.app.config.globalProperties.$performance = performanceManager
    
    // 提供性能管理器
    context.app.provide('performance', performanceManager)
    
    console.log('Performance plugin installed')
  },
  uninstall() {
    performanceManager.destroy()
    console.log('Performance plugin uninstalled')
  }
})

/**
 * 使用性能监控的组合式函数
 */
export function usePerformance() {
  return {
    performance: performanceManager,
    mark: performanceManager.mark.bind(performanceManager),
    measure: performanceManager.measure.bind(performanceManager),
    getMetrics: performanceManager.getMetrics.bind(performanceManager),
    getEntries: performanceManager.getEntries.bind(performanceManager),
    getStats: performanceManager.getStats.bind(performanceManager),
    clearEntries: performanceManager.clearEntries.bind(performanceManager),
    reportMetrics: performanceManager.reportMetrics.bind(performanceManager),
    addListener: performanceManager.addListener.bind(performanceManager),
    removeListener: performanceManager.removeListener.bind(performanceManager)
  }
}

export default performancePlugin