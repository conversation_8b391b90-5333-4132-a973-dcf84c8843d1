package vips

import (
	"context"
	"frontapi/internal/models/vips"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// CoinPackageRepository 平台币套餐仓库接口
type CoinPackageRepository interface {
	base.ExtendedRepository[vips.CoinPackage]
	// 业务特定方法
	FindByCode(ctx context.Context, code string) (*vips.CoinPackage, error)
}

// coinPackageRepository 平台币套餐仓库实现
type coinPackageRepository struct {
	base.ExtendedRepository[vips.CoinPackage]
}

// NewCoinPackageRepository 创建平台币套餐仓库实例
func NewCoinPackageRepository(db *gorm.DB) CoinPackageRepository {
	return &coinPackageRepository{
		ExtendedRepository: base.NewExtendedRepository[vips.CoinPackage](db),
	}
}

// FindByCode 根据编码查找平台币套餐
func (r *coinPackageRepository) FindByCode(ctx context.Context, code string) (*vips.CoinPackage, error) {
	condition := map[string]interface{}{"code": code}
	return r.FindOneByCondition(ctx, condition, "")
}
