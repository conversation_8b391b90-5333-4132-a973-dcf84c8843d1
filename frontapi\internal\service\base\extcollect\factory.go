package extcollect

import (
	"context"
	"fmt"
	"log"
	"time"

	goredis "github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/mongo"

	"frontapi/internal/service/base/extcollect/mongodb"
	redisv2 "frontapi/internal/service/base/extcollect/redis/v2"
	mongoConfig "frontapi/pkg/mongodb"
)

// ServiceBuilder 服务构建器
type ServiceBuilder struct {
	config      *Config
	redisClient goredis.UniversalClient
	mongoClient *mongo.Client
	mongoDb     *mongo.Database
}

// NewServiceBuilder 创建服务构建器
func NewServiceBuilder() *ServiceBuilder {
	return &ServiceBuilder{
		config: DefaultConfig(),
	}
}

// WithConfig 设置配置
func (sb *ServiceBuilder) WithConfig(config *Config) *ServiceBuilder {
	sb.config = config
	return sb
}

// WithRedisClient 设置Redis客户端
func (sb *ServiceBuilder) WithRedisClient(client goredis.UniversalClient) *ServiceBuilder {
	sb.redisClient = client
	return sb
}

// WithMongoClient 设置MongoDB客户端
func (sb *ServiceBuilder) WithMongoClient(client *mongo.Client, database *mongo.Database) *ServiceBuilder {
	sb.mongoClient = client
	sb.mongoDb = database
	return sb
}

// Build 构建服务
func (sb *ServiceBuilder) Build() (ExtendedCollectService, error) {
	if err := sb.config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return sb.buildService()
}

// buildService 构建具体服务
func (sb *ServiceBuilder) buildService() (ExtendedCollectService, error) {
	var adapter CollectAdapter
	var err error

	switch sb.config.Strategy {
	case RedisOnly:
		adapter, err = sb.buildRedisAdapter()
	case MongoOnly:
		adapter, err = sb.buildMongoAdapter()
	case RedisFirst, MongoFirst, DualWrite:
		adapter, err = sb.buildDualAdapter()
	default:
		return nil, fmt.Errorf("不支持的存储策略: %s", sb.config.Strategy)
	}

	if err != nil {
		return nil, fmt.Errorf("构建适配器失败: %w", err)
	}

	// 创建主服务
	service := &collectService{
		config:  sb.config,
		adapter: adapter,
	}

	return service, nil
}

// buildRedisAdapter 构建Redis适配器
func (sb *ServiceBuilder) buildRedisAdapter() (CollectAdapter, error) {
	client, err := sb.getRedisClient()
	if err != nil {
		return nil, fmt.Errorf("获取Redis客户端失败: %w", err)
	}

	// 类型断言转换 UniversalClient 为 *redis.Client
	redisClient, ok := client.(*goredis.Client)
	if !ok {
		// 如果是ClusterClient或其他类型，创建一个包装器或使用兼容方式
		return nil, fmt.Errorf("不支持的Redis客户端类型")
	}

	config := &redisv2.Config{
		Prefix:       sb.config.Redis.Config.KeyPrefix,
		TTL:          sb.config.Redis.Config.DefaultTTL,
		BatchSize:    100,
		PipelineSize: 100,
		MaxRetries:   3,
		RetryDelay:   100 * time.Millisecond,
	}

	return redisv2.NewRedisAdapter(redisClient, config), nil
}

// buildMongoAdapter 构建MongoDB适配器
func (sb *ServiceBuilder) buildMongoAdapter() (CollectAdapter, error) {
	client, database, err := sb.getMongoClient()
	if err != nil {
		return nil, fmt.Errorf("获取MongoDB客户端失败: %w", err)
	}

	config := &mongodb.MongoConfig{
		Database:   database.Name(),
		Collection: "collects",
	}

	return mongodb.NewMongoAdapter(client, database, config)
}

// buildDualAdapter 构建双写适配器
func (sb *ServiceBuilder) buildDualAdapter() (CollectAdapter, error) {
	// 根据策略决定主从适配器
	var primary, secondary CollectAdapter
	var err error

	switch sb.config.Strategy {
	case RedisFirst:
		primary, err = sb.buildRedisAdapter()
		if err != nil {
			return nil, err
		}
		secondary, err = sb.buildMongoAdapter()
		if err != nil {
			log.Printf("MongoDB适配器构建失败，继续使用Redis: %v", err)
			secondary = nil
		}
	case MongoFirst:
		primary, err = sb.buildMongoAdapter()
		if err != nil {
			return nil, err
		}
		secondary, err = sb.buildRedisAdapter()
		if err != nil {
			log.Printf("Redis适配器构建失败，继续使用MongoDB: %v", err)
			secondary = nil
		}
	case DualWrite:
		primary, err = sb.buildRedisAdapter()
		if err != nil {
			return nil, err
		}
		secondary, err = sb.buildMongoAdapter()
		if err != nil {
			return nil, err
		}
	}

	return &dualAdapter{
		primary:   primary,
		secondary: secondary,
		strategy:  sb.config.Strategy,
	}, nil
}

// getRedisClient 获取Redis客户端
func (sb *ServiceBuilder) getRedisClient() (goredis.UniversalClient, error) {
	// 如果已设置客户端，直接使用
	if sb.redisClient != nil {
		return sb.redisClient, nil
	}

	// 如果配置为使用系统Redis
	if sb.config.Redis.UseSystem && sb.config.Redis.Client != nil {
		return sb.config.Redis.Client, nil
	}

	// 创建新的Redis客户端
	redisConfig := sb.config.Redis.Config
	if redisConfig == nil {
		redisConfig = DefaultRedisConfig()
	}

	var client goredis.UniversalClient
	if redisConfig.Cluster {
		client = goredis.NewClusterClient(&goredis.ClusterOptions{
			Addrs:    redisConfig.ClusterAddrs,
			Password: redisConfig.Password,
			Username: redisConfig.Username,
		})
	} else {
		client = goredis.NewClient(&goredis.Options{
			Addr:     fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
			Password: redisConfig.Password,
			DB:       redisConfig.DB,
			Username: redisConfig.Username,
		})
	}

	// 测试连接
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis连接测试失败: %w", err)
	}

	return client, nil
}

// getMongoClient 获取MongoDB客户端
func (sb *ServiceBuilder) getMongoClient() (*mongo.Client, *mongo.Database, error) {
	// 如果已设置客户端，直接使用
	if sb.mongoClient != nil && sb.mongoDb != nil {
		return sb.mongoClient, sb.mongoDb, nil
	}

	// 如果配置为使用系统MongoDB
	if sb.config.MongoDB.UseSystem {
		if sb.config.MongoDB.Client != nil && sb.config.MongoDB.Database != nil {
			return sb.config.MongoDB.Client, sb.config.MongoDB.Database, nil
		}
	}

	// 创建新的MongoDB客户端
	mongoConf := sb.config.MongoDB.Config
	if mongoConf == nil {
		mongoConf = DefaultMongoConfig()
	}

	client, err := mongoConfig.NewClient(mongoConf)
	if err != nil {
		return nil, nil, fmt.Errorf("创建MongoDB客户端失败: %w", err)
	}

	database := mongoConfig.NewDatabase(client, mongoConf.Database)

	return client, database, nil
}

// Factory 工厂函数

// NewCollectService 创建收藏服务
func NewCollectService(config *Config) (ExtendedCollectService, error) {
	return NewServiceBuilder().
		WithConfig(config).
		Build()
}

// NewExtendedService 创建扩展收藏服务实例
func NewExtendedService(config *Config) (ExtendedCollectService, error) {
	return NewServiceBuilder().
		WithConfig(config).
		Build()
}

// NewCollectServiceWithRedis 创建带Redis的收藏服务
func NewCollectServiceWithRedis(redisClient goredis.UniversalClient, config *Config) (ExtendedCollectService, error) {
	if config == nil {
		config = DefaultConfig()
		config.Strategy = RedisOnly
		config.Redis.Enabled = true
	}

	return NewServiceBuilder().
		WithConfig(config).
		WithRedisClient(redisClient).
		Build()
}

// NewCollectServiceWithMongo 创建带MongoDB的收藏服务
func NewCollectServiceWithMongo(mongoClient *mongo.Client, database *mongo.Database, config *Config) (ExtendedCollectService, error) {
	if config == nil {
		config = DefaultConfig()
		config.Strategy = MongoOnly
		config.MongoDB.Enabled = true
	}

	return NewServiceBuilder().
		WithConfig(config).
		WithMongoClient(mongoClient, database).
		Build()
}

// NewCollectServiceWithBoth 创建双写收藏服务
func NewCollectServiceWithBoth(
	redisClient goredis.UniversalClient,
	mongoClient *mongo.Client,
	database *mongo.Database,
	strategy StorageStrategy,
) (ExtendedCollectService, error) {
	config := DefaultConfig()
	config.Strategy = strategy
	config.Redis.Enabled = true
	config.MongoDB.Enabled = true

	return NewServiceBuilder().
		WithConfig(config).
		WithRedisClient(redisClient).
		WithMongoClient(mongoClient, database).
		Build()
}

// CreateDefaultService 创建默认配置的服务
func CreateDefaultService() (ExtendedCollectService, error) {
	return NewCollectService(DefaultConfig())
}
