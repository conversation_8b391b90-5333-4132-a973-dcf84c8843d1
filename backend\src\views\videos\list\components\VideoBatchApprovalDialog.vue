<template>
  <BatchApprovalDialog
    :visible="visible"
    :title="'批量审核视频'"
    :item-count="videos.length"
    :loading="loading"
    @update:visible="handleVisibleChange"
    @confirm="handleConfirm"
  />
</template>

<script setup lang="ts">
import { BatchApprovalDialog } from '@/components/themes';
import { batchReviewVideos } from '@/service/api/videos/videos';
import { VideoItem } from '@/types/videos';
import { handleApiError } from '@/utils/errorHandler';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';

interface Props {
  visible: boolean;
  videos: VideoItem[];
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  videos: () => []
});

const emit = defineEmits<{
  'update:visible': [value: boolean];
  'success': [];
}>();

const loading = ref(false);

// 处理可见性变更
const handleVisibleChange = (val: boolean) => {
  emit('update:visible', val);
};

// 处理提交
const handleConfirm = async (data: { status: number; reason: string }) => {
  if (props.videos.length === 0) return;
  
  loading.value = true;
  try {
    const videoIds = props.videos.map(video => video.id);
    
    const result = await batchReviewVideos({
      ids: videoIds,
      status: data.status,
      reason: data.reason
    });
    
    if (result) {
      ElMessage.success(`成功${data.status === 2 ? '通过' : '拒绝'}${videoIds.length}个视频`);
      emit('update:visible', false);
      emit('success');
    }
  } catch (error) {
    handleApiError(error, '批量审核视频');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* 批量审核对话框样式 */
</style> 