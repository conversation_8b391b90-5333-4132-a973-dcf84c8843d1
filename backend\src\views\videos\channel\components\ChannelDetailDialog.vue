<template>
  <el-dialog
    :model-value="visible"
    title="频道详情"
    width="900px"
    @update:model-value="(val) => emit('update:visible', val)"
  >
    <div v-if="channelData" class="channel-detail-content">
      <!-- 频道基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>频道名称：</label>
            <span class="info-value">{{ channelData.name }}</span>
          </div>
          <div class="info-item">
            <label>频道编码：</label>
            <span class="info-value">
              <el-tag v-if="channelData.code" type="info" size="small">{{ channelData.code }}</el-tag>
              <span v-else class="placeholder">未设置</span>
            </span>
          </div>
          <div class="info-item">
            <label>频道URI：</label>
            <span class="info-value">{{ channelData.uri || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>更新频率：</label>
            <span class="info-value">{{ getFrequencyText(channelData.update_frequency) || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label>排序权重：</label>
            <span class="info-value">{{ channelData.sort_order }}</span>
          </div>
          <div class="info-item">
            <label>主题颜色：</label>
            <span class="info-value">
              <div v-if="channelData.color" class="color-display">
                <div 
                  class="color-box" 
                  :style="{ backgroundColor: channelData.color }"
                ></div>
                <span>{{ channelData.color }}</span>
              </div>
              <span v-else class="placeholder">未设置</span>
            </span>
          </div>
        </div>
      </div>

      <!-- 频道描述 -->
      <div v-if="channelData.description" class="detail-section">
        <h3 class="section-title">频道描述</h3>
        <div class="description-content">
          {{ channelData.description }}
        </div>
      </div>

      <!-- 频道图片信息 -->
      <div class="detail-section">
        <h3 class="section-title">图片信息</h3>
        <div class="image-gallery">
          <div v-if="channelData.icon" class="image-item">
            <label>频道图标</label>
            <div class="image-preview">
              <el-image 
                :src="channelData.icon" 
                :alt="channelData.name + ' 图标'"
                fit="cover"
                class="preview-image icon-image"
                :preview-src-list="[channelData.icon]"
              />
            </div>
          </div>
          <div v-if="channelData.cover" class="image-item">
            <label>频道封面</label>
            <div class="image-preview">
              <el-image 
                :src="channelData.cover" 
                :alt="channelData.name + ' 封面'"
                fit="cover"
                class="preview-image cover-image"
                :preview-src-list="[channelData.cover]"
              />
            </div>
          </div>
          <div v-if="channelData.banner" class="image-item">
            <label>频道横幅</label>
            <div class="image-preview">
              <el-image 
                :src="channelData.banner" 
                :alt="channelData.name + ' 横幅'"
                fit="cover"
                class="preview-image banner-image"
                :preview-src-list="[channelData.banner]"
              />
            </div>
          </div>
        </div>
        <div v-if="!channelData.icon && !channelData.cover && !channelData.banner" class="no-images">
          <el-empty description="暂无图片信息" :image-size="80" />
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="detail-section">
        <h3 class="section-title">统计信息</h3>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon video-icon">
              <el-icon><VideoCamera /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-label">视频数量</div>
              <div class="stat-value">{{ channelData.video_count || 0 }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon view-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-label">总观看数</div>
              <div class="stat-value">{{ formatNumber(channelData.view_count || 0) }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-label">订阅数量</div>
              <div class="stat-value">{{ formatNumber(channelData.subscriber_count || 0) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态信息 -->
      <div class="detail-section">
        <h3 class="section-title">状态信息</h3>
        <div class="status-grid">
          <div class="status-item">
            <label>频道状态：</label>
            <ChannelStatusTag :status="channelData.status" />
          </div>
          <div class="status-item">
            <label>推荐状态：</label>
            <el-tag 
              :type="channelData.is_featured === 1 ? 'success' : 'info'" 
              size="small"
            >
              {{ channelData.is_featured === 1 ? '推荐' : '普通' }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 创建者信息 -->
      <div v-if="channelData.creator_name" class="detail-section">
        <h3 class="section-title">创建者信息</h3>
        <div class="creator-info">
          <el-avatar 
            :size="40"
            :src="channelData.creator_avatar"
            class="creator-avatar"
          >
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="creator-details">
            <div class="creator-name">{{ channelData.creator_name }}</div>
            <div class="creator-id">ID: {{ channelData.creator_id }}</div>
          </div>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section">
        <h3 class="section-title">时间信息</h3>
        <div class="time-grid">
                     <div class="time-item">
             <label>创建时间：</label>
             <span class="time-value">{{ channelData.created_at ? formatDate(channelData.created_at) : '未知' }}</span>
           </div>
           <div class="time-item">
             <label>更新时间：</label>
             <span class="time-value">{{ channelData.updated_at ? formatDate(channelData.updated_at) : '未知' }}</span>
           </div>
          <div v-if="channelData.last_video_at" class="time-item">
            <label>最后发布视频：</label>
            <span class="time-value">{{ formatDate(channelData.last_video_at) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="loading-content">
      <el-skeleton :rows="5" animated />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="emit('update:visible', false)">关闭</el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑频道
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { VideoChannelItem } from '@/types/videos';
import { formatDate } from '@/utils/date';
import { Edit, User, VideoCamera, View } from '@element-plus/icons-vue';
import ChannelStatusTag from './ChannelStatusTag.vue';

// Props
interface Props {
  visible: boolean;
  channelData?: VideoChannelItem | null;
}

const props = withDefaults(defineProps<Props>(), {
  channelData: null
});

// Emits
interface Emits {
  'update:visible': [value: boolean];
  edit: [data: VideoChannelItem];
}

const emit = defineEmits<Emits>();

// 编辑频道
const handleEdit = () => {
  if (props.channelData) {
    emit('edit', props.channelData);
    emit('update:visible', false);
  }
};

// 工具函数
const getFrequencyText = (frequency?: string) => {
  const textMap: Record<string, string> = {
    'daily': '每日',
    'weekly': '每周',
    'monthly': '每月',
    'irregular': '不定期'
  };
  return frequency ? textMap[frequency] || frequency : '';
};

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toString();
};
</script>

<style scoped lang="scss">
.channel-detail-content {
  .detail-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #f0f0f0;
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;

    .info-item {
      display: flex;
      align-items: center;
      padding: 8px 0;

      label {
        font-weight: 500;
        color: #666;
        margin-right: 8px;
        min-width: 80px;
      }

      .info-value {
        color: #333;
        flex: 1;
      }

      .placeholder {
        color: #999;
        font-style: italic;
      }

      .color-display {
        display: flex;
        align-items: center;
        gap: 8px;

        .color-box {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          border: 1px solid #ddd;
        }
      }
    }
  }

  .description-content {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #409eff;
    line-height: 1.6;
    color: #333;
  }

  .image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .image-item {
      text-align: center;

      label {
        display: block;
        font-weight: 500;
        color: #666;
        margin-bottom: 8px;
      }

      .image-preview {
        .preview-image {
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }

          &.icon-image {
            width: 80px;
            height: 80px;
          }

          &.cover-image {
            width: 160px;
            height: 120px;
          }

          &.banner-image {
            width: 200px;
            height: 80px;
          }
        }
      }
    }
  }

  .no-images {
    text-align: center;
    padding: 40px 0;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .stat-icon {
        margin-right: 12px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &.video-icon {
          background: #e3f2fd;
          color: #1976d2;
        }

        &.view-icon {
          background: #f3e5f5;
          color: #7b1fa2;
        }

        &.user-icon {
          background: #e8f5e8;
          color: #388e3c;
        }
      }

      .stat-content {
        .stat-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .status-grid {
    display: flex;
    gap: 32px;
    flex-wrap: wrap;

    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;

      label {
        font-weight: 500;
        color: #666;
      }
    }
  }

  .creator-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .creator-avatar {
      flex-shrink: 0;
    }

    .creator-details {
      .creator-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .creator-id {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .time-item {
      display: flex;
      align-items: center;
      padding: 8px 0;

      label {
        font-weight: 500;
        color: #666;
        margin-right: 8px;
        min-width: 100px;
      }

      .time-value {
        color: #333;
        font-family: monospace;
      }
    }
  }
}

.loading-content {
  padding: 20px;
}

.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .channel-detail-content {
    .info-grid {
      grid-template-columns: 1fr;
    }

    .image-gallery {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .status-grid {
      flex-direction: column;
      gap: 16px;
    }

    .time-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style> 