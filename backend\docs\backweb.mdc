---
description: 
globs: 
alwaysApply: true
---
# 前后端Web应用开发规则

## 项目架构规则

### 目录结构
- frontapi/: Go后端API服务采用fiber v2框架
  - internal/: 内部包
    - models/: 数据模型定义
     - advs/: 广告模块model
     - books/: 电子书模块model
     - comics/: 漫画模块model
     - content_creator/:创作者模块model
     - home/: 首页模块model
     - integral/: 积分模块model
     - permission/: 权限模块model
     - pictures/: 图片模块model
     - promotion/: 推广模块model
     - posts/: 帖子模块model
     - shortvideos/: 短视频模块model
     - sys/: 系统模块model
     - tags/:标签模块model
     - users/: 用户模块model
     - videos/: 视频模块model
     - vips/: vip模块model
     - wallets/: 钱包模块model
    - repository/: 数据访问层
      - advs/: 广告模块数据访问层
      - books/: 电子书模块数据访问层
      - comics/: 漫画模块数据访问层
      - content_creator/:创作者模块数据访问层
      - home/: 首页模块数据访问层
      - integral/: 积分模块数据访问层
      - permission/: 权限模块数据访问层
      - pictures/: 图片模块数据访问层
      - promotion/: 推广模块数据访问层
      - posts/: 帖子模块数据访问层
      - shortvideos/: 短视频模块数据访问层
      - sys/: 系统模块数据访问层
      - tags/:标签模块数据访问层
      - users/: 用户模块数据访问层
      - videos/: 视频模块数据访问层
      - vips/: vip模块数据访问层
      - wallets/: 钱包模块数据访问层
    - service/: 业务逻辑层
     - advs/: 广告模块业务逻辑层
     - books/: 电子书模块业务逻辑层
     - comics/: 漫画模块业务逻辑层
     - content_creator/:创作者模块业务逻辑层
     - home/: 首页模块业务逻辑层
     - integral/: 积分模块业务逻辑层
     - permission/: 权限模块业务逻辑层
     - pictures/: 图片模块业务逻辑层
     - promotion/: 推广模块业务逻辑层
     - posts/: 帖子模块业务逻辑层
     - shortvideos/: 短视频模块业务逻辑层
     - sys/: 系统模块业务逻辑层
     - tags/:标签模块业务逻辑层
     - users/: 用户模块业务逻辑层
     - videos/: 视频模块业务逻辑层
     - vips/: vip模块业务逻辑层
     - wallets/: 钱包模块业务逻辑层

    - admin/: 管理后台控制器接口
      - auth/: 管理后台认证模块controller
      - books/: 管理后台电子书模块controller
      - content_creator/:管理后台创作者模块controller
      - comics/: 管理后台漫画模块controller
      - home/: 管理后台首页模块controller
      - integral/: 管理后台积分模块controller
      - permission/:管理后台权限模块controller
      - pictures/:管理后台图片模块controller
      - posts/: 管理后台帖子模块controller
      - promotion/:管理后台推广模块controller
      - shortvideos/:管理后台短视频模块controller
      - sys/:管理后台系统模块controller
      - system/:管理后台系统模块controller
      - users/:管理后台用户模块controller
      - videos/:管理后台视频模块controller
      - vips/:管理后台会员模块controller
      - wallets/:管理后台钱包模块controller
    - routes/: 路由定义
    - middleware/: 中间件
    - bootstrap/: 应用初始化
    - validator/: 请求验证
  - cmd/: 应用入口
    - admin/: 管理后台接口入口
    - api/: 前端用户接口入口
    - main.go: 应用入口
  - config/: 配置文件
  - migrations/: 数据库迁移
  - db/: 数据库文件
  - docs/: 文档
  - pkg/: 公共包

- backend/: Vue管理后台
  - locales/: 国际化和多语言
  - src/: 源代码
    - api/: API接口定义
      - advs/: 广告模块api
      - books/: 电子书模块api
      - comics/: 漫画模块api
      - posts/: 帖子模块api
      - shortvideos/: 短视频模块api
      - system/: 系统模块api
      - users/: 用户模块api
      - videos/: 视频模块api
      -  vips/: vip模块api
    - views/: 页面视图
     - advs/: 广告模块页面
     - permission/:权限模块页面
     - posts/: 帖子模块页面
     - login/: 登录页面
     - users/: 用户模块页面
     - system/: 系统模块页面
     - shortvideos/: 短视频模块页面
     - videos/: 视频模块页面
     - welcome/: 欢迎页面
     - error/: 错误页面
    - components/: 组件
    - router/: 路由
    - store/: 状态管理
    - utils/: 工具函数
    - config/: 配置文件
    - layout/: 布局
    - plugins/: 插件
    - assets/: 静态资源
    - static/: 静态资源
    - types/: 类型定义
    - directives/: 指令
    - utils/: 工具函数
    - main.js: 应用入口
    - App.vue: 应用入口

- frontend/: Vue前端应用
  - src/: 源代码
    - api/: API接口定义
     - books/: 电子书模块api
     - category/:分类模块api
     - celebrity/: 影人模块api
     - channels/:板块模块api
     - comics/: 漫画模块api
     - community/:社区(帖子)模块api
     - home/: 首页模块api
     - live/: 直播模块api
     - pictures/: 图片模块api
     - shorts/: 短视频模块api
     - space/: 空间模块api
     - users/: 用户模块api
    - views/: 页面视图
     - account/: 账户模块视图
     - books/: 电子书模块视图
     - celebrity/: 明星模块视图
     - categories/: 视频分类模块视图
     - channels/: 频道模块视图
     - comics/: 动漫模块视图
     - community/: 社区模块视图
     - error/: 错误页面
     - home/: 首页模块视图
     - live/: 直播模块视图
     - shorts/: 短视频模块视图
     - pictures/: 图片模块视图
     - space/: 空间模块视图
     - user/: 用户模块视图
     - videos/: 视频模块视图
    - components/: 组件
    - router/: 路由
    - stores/: 状态管理
    - styles/: 样式
    - plugins/: 插件
    - layouts/:  布局
    - types/: 类型定义
    - utils/: 工具函数
    - assets/: 静态资源

## 管理后台前端(backend)开发规范

### 类型定义规范 (types)
```typescript
// 短视频类型定义示例 (types/shortvideos.ts)
export interface ShortVideo {
  id: string;
  title: string;
  description: string;
  cover_url: string;
  url: string;
  user_id: string;
  category_id: string;
  duration: number;
  width: number;
  height: number;
  tags: string[];
  status: number; // 0-审核中 1-已发布 2-已拒绝 3-已删除
  is_featured: number; // 0-普通 1-推荐
  views: number;
  likes: number;
  comments: number;
  shares: number;
  created_at: string;
  updated_at: string;
}

// 分页参数
export interface PageParams {
  pageNo: number;
  pageSize: number;
}

// 请求参数
export interface ShortVideoParams {
  page: PageParams;
  data?: ShortVideoQuery;
}

// 创建短视频请求
export interface CreateShortVideoRequest {
  title: string;
  description: string;
  url: string;
  category_id: string;
  tags: string[];
  status: number;
  is_featured: number;
}
```

### API定义规范 (api)
```typescript
// 短视频API定义示例 (api/shortvideos/shortvideos.ts)
import { http } from "@/utils/http";
import type {
  ShortVideoParams,
  ShortVideo,
  CreateShortVideoRequest,
  UpdateShortVideoRequest
} from '@/types/shortvideos';

/**
 * 获取短视频列表
 * @param params 查询参数
 */
export function getShortVideoList(params: ShortVideoParams) {
  return http.request('post', '/shortvideos/list', { data: params });
}

/**
 * 获取短视频详情
 * @param id 短视频ID
 */
export function getShortVideoDetail(id: string) {
  return http.request('post', `/shortvideos/detail/${id}`, { 
    data: { data: { "id": id } } 
  });
}

/**
 * 创建短视频
 * @param data 短视频数据
 */
export function createShortVideo(params: { data: CreateShortVideoRequest }) {
  return http.request('post', '/shortvideos/add', { data: params });
}
```

### 视图定义规范 (views)
```vue
<!-- 短视频列表页示例 (views/shortvideos/list/index.vue) -->
<template>
  <div class="app-container">
    <el-card>
      <template #header>

    <div class="filter-container">
      <div class="title-container">
        <h2>短视频分类管理</h2>
        <div class="buttons">
          <el-button type="primary" @click="handleAddCategory">添加分类</el-button>
          <el-button type="success" @click="refreshList">刷新</el-button>
        </div>
      </div>
    </div>
    </template>
    <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="分类名称">
          <el-input v-model="queryParams.name" placeholder="请输入分类名称" clearable></el-input>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="所有状态" clearable style="width: 200px;">
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    <el-table
      v-loading="loading"
      :data="categoryList"
      border
      style="width: 100%"
      row-key="id"
    >
     <el-table-column type="index" width="55"></el-table-column>
    <el-table-column prop="name" label="分类名称" min-width="120">
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="分类编码" min-width="120">
        <template #default="{ row }">
          <span>{{ row.code }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="parent_id" label="父分类" width="120">
        <template #default="{ row }">
          <span v-if="row.parent_id">{{ getParentName(row.parent_id) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="描述" min-width="150" align="center">
        <template #default="{ row }">
          <span>{{ row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sort_order" label="排序" width="80" align="center" />

      <el-table-column prop="status" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="180" align="center">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="220" align="center">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button
            type="success"
            link
            v-if="row.status === 0"
            @click="handleChangeStatus(row.id, 1)"
          >启用</el-button>
          <el-button
            type="warning"
            link
            v-else
            @click="handleChangeStatus(row.id, 0)"
          >禁用</el-button>
          <el-button type="danger" link @click="handleDelete(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.page"
       :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 分类表单对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加分类' : '编辑分类'"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" ></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入分类编码" >
            <template  #append>
            <el-button @click="form.code=generateCode()">随机生成</el-button>
          </template>
          </el-input>
        </el-form-item>
        <el-form-item label="父分类">
          <el-select v-model="form.parent_id" placeholder="选择父分类" clearable>
            <el-option
              v-for="item in parentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input v-model="form.description" placeholder="请输入分类描述" type="textarea" :rows="2"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="form.sort_order" :min="0" :max="999"></el-input-number>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { getShortVideoList, deleteShortVideo } from '@/api/shortvideos/shortvideos';
import type { ShortVideo } from '@/types/shortvideos';

// 查询参数
const queryParams = reactive({
  data: {
    title: '',
    status: undefined
  },
  page: {
    pageNo: 1,
    pageSize: 10
  }
});

// 状态选项
const statusOptions = [
  { label: '审核中', value: 0 },
  { label: '已发布', value: 1 },
  { label: '已拒绝', value: 2 },
  { label: '已删除', value: 3 }
];

// 列表数据
const shortvideoList = ref<ShortVideo[]>([]);
const total = ref(0);
const loading = ref(false);
const open = ref(false);
const title = ref('');

// 获取列表数据
const getList = async () => {
  loading.value = true;
  try {
    const res = await getShortVideoList(queryParams);
    shortvideoList.value = res.data.list;
    total.value = res.data.total;
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleQuery = () => {
  queryParams.page.pageNo = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  queryParams.data.title = '';
  queryParams.data.status = undefined;
  handleQuery();
};

// 状态格式化
const statusFormatter = (row: ShortVideo) => {
  const status = row.status;
  return statusOptions.find(item => item.value === status)?.label || '未知';
};

// 初始化
onMounted(() => {
  getList();
});
</script>
```

### 路由定义规范 (router)
```typescript
// 路由配置示例 (backend\src\router\elegant\modules\shortvideos.ts)
import type { GeneratedRoute } from '@elegant-router/types';
import { $t } from '@/locales';`

const routes: GeneratedRoute[] = [
  {
    name: 'shortvideos',
    path: '/shortvideos',
    component: 'layout.base',
    meta: {
      title: 'shortvideos',
      i18nKey: 'route.shortvideos',
      icon: 'lucide:film',
      order: 4
    },
    children: [
      // 短视频分类页面
      {
        name: 'shortvideos_category',
        path: '/shortvideos/category',
        component: 'view.shortvideos_category',
        meta: {
          title: 'shortvideos_category',
          i18nKey: 'route.shortvideos_category',
          icon: 'lucide:folder',
          order: 1
        }
      },
      // 短视频列表页面
      {
        name: 'shortvideos_list',
        path: '/shortvideos/list',
        component: 'view.shortvideos_list',
        meta: {
          title: 'shortvideos_list',
          i18nKey: 'route.shortvideos_list',
          icon: 'lucide:list',
          order: 2
        }
      },
      // 短视频评论页面
      {
        name: 'shortvideos_comment',
        path: '/shortvideos/comment',
        component: 'view.shortvideos_comment',
        meta: {
          title: 'shortvideos_comment',
          i18nKey: 'route.shortvideos_comment',
          icon: 'lucide:message-circle',
          order: 3
        }
      }
    ]
  }
];

export default routes;
```

## 接口请求响应规范

### 请求格式

#### 普通请求
```typescript
// 普通请求格式
const requestData = {
  data: {
    title: "标题",
    content: "内容",
    status: 1
  }
};

// API调用
api.createData(requestData);
```

#### 带分页请求
```typescript
// 带分页请求格式
const requestData = {
  data: {
    title: "查询标题",
    status: 1
  },
  page: {
    pageNo: 1,
    pageSize: 10
  }
};

// API调用
api.getDataList(requestData);
```

### 响应格式

#### 普通响应
```typescript
// 普通响应格式
{
  code: 2000,
  message: "请求成功",
  data: {
    id: "123456",
    title: "标题",
    content: "内容",
    // ...其他字段
  }
}
```

#### 列表响应
```typescript
// 列表响应格式
{
  code: 2000,
  message: "请求成功",
  data: {
    list: [
      {
        id: "123456",
        title: "标题1",
        // ...其他字段
      },
      {
        id: "123457",
        title: "标题2",
        // ...其他字段
      }
    ],
    total: 2,
    page: 1,
    pageSize: 10
  }
}
```

## 代码规范

### 后端Go代码规范
- 使用驼峰命名法，公开函数/结构体首字母大写
- 每个包应有明确的职责，避免循环依赖
- 使用依赖注入模式管理服务依赖
- 实现接口而非直接依赖具体类型
- 错误处理必须明确，避免忽略错误
- 注释应当解释"为什么"而非"是什么"

### 后端项目层级规范
- models层: 定义数据结构和关系
- repository层: 负责数据访问和持久化
- service层: 实现业务逻辑，调用repository层
- admin/api层: 处理HTTP请求，调用service层
- routes层: 定义API路由和中间件
- 严格遵循依赖方向: controller -> service -> repository -> models

### 前端Vue代码规范
- 使用组件化开发方式，每个组件有单一职责
- 使用TypeScript进行类型检查
- 视图与业务逻辑分离，使用Pinia/Vuex管理状态
- API接口统一在api目录下定义
- 使用Vue Router管理路由
- 遵循一致的CSS命名规范(如BEM)

## 开发流程

### 功能开发流程
1. 在models层定义数据模型
2. 在repository层实现数据访问方法
3. 在service层实现业务逻辑
4. 在admin/api层实现HTTP接口
5. 在routes层注册路由
6. 在前端api中定义接口
7. 在前端视图中实现界面

### 代码审查要点
- 代码是否符合项目架构规范
- 是否存在安全隐患
- 是否处理了所有可能的错误
- 是否有足够的测试覆盖
- 性能是否满足要求
- 是否遵循了既定的代码风格

## 通用开发准则
- 保持简单，避免过度设计
- 编写可测试的代码
- 关注性能和安全性
- 代码应该自文档化
- 遵循最小权限原则
- 定期进行代码重构


