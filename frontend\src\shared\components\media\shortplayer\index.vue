<template>
  <div class="short-player-container">
    <div class="video-player" ref="playerContainer" id="video-player"></div>
    
    <!-- 弹幕组件 -->
    <Danmu
      ref="danmuComponent"
      :is-open="isDanmuOpen"
      :show-input="showDanmuInput"
      :speed="currentSpeed"
      :video-id="currentVideo?.id"
      @close-input="showDanmuInput = false"
      @danmu-sent="handleDanmuSent"
    />
    
    <!-- 右侧交互按钮 -->
    <div class="right-side-container">
      <InteractionButtons
        v-if="currentVideo"
        :video="currentVideo"
        :is-liked="isLiked"
        :is-favorited="isFavorited"
        :is-following="isFollowing"
        :can-go-previous="props.canGoPrevious"
        :can-go-next="props.canGoNext"
        @like="$emit('like')"
        @comment="$emit('comment')"
        @favorite="$emit('favorite')"
        @share="$emit('share')"
        @follow="$emit('follow')"
        @previous="$emit('previous')"
        @next="$emit('next')"
      />
    </div>

    <!-- 底部视频信息 -->
    <div class="video-bottom-container">
      <VideoInfo
        v-if="currentVideo"
        :video="currentVideo"
      />
      <!-- 播放器控制器 -->
      <PlayerControls
        v-if="currentVideo"
        :video="currentVideo"
        :is-playing="playerState.isPlaying"
        :is-muted="playerState.isMuted"
        :current-time="playerState.currentTime"
        :duration="playerState.duration"
        :is-danmu-open="isDanmuOpen"
        :is-user-logged-in="props.isUserLoggedIn"
        :can-go-previous="props.canGoPrevious"
        :can-go-next="props.canGoNext"
        :volume="volume"
        :current-quality="currentQuality"
        :quality-options="qualityOptions"
        @play-pause="togglePlayPause"
        @mute-toggle="toggleMute"
        @previous="handlePrevious"
        @next="handleNext"
        @progress-change="handleProgressChange"
        @danmu-toggle="toggleDanmu"
        @danmu-input-click="handleDanmuInputClick"
        @danmu-input-focus="handleDanmuInputFocus"
        @volume-change="handleVolumeChange"
        @speed-change="handleSpeedChange"
        @quality-change="handleQualityChange"
        @webpage-fullscreen="handleWebpageFullscreen"
        @fullscreen="handleFullscreen"
      >
        <!-- 弹幕控制按钮 slot -->
        <template #danmu-control>
          <div class="danmu-controls">
            <button 
              class="control-btn danmu-toggle-btn" 
              @click="toggleDanmu"
              :class="{ active: isDanmuOpen }"
              title="弹幕开关"
            >
              <img 
                :src="danmuIcon" 
                alt="弹幕开关"
                style="width: 18px; height: 18px;"
              />
            </button>
            
            <!-- 弹幕输入框 -->
            <div class="danmu-input-wrapper">
              <input
                type="text"
                class="danmu-input"
                :placeholder="getDanmuInputPlaceholder()"
                :disabled="!isDanmuOpen || !props.isUserLoggedIn"
                @click="handleDanmuInputClick"
                @focus="handleDanmuInputFocus"
                readonly
              />
            </div>
          </div>
        </template>
      </PlayerControls>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'
import PlayerControls from './components/PlayerControls.vue'
import VideoInfo from './components/VideoInfo.vue'
import InteractionButtons from './components/InteractionButtons.vue'
import Danmu from './components/Danmu.vue'
import type { ShortVideo } from '../../types/shorts'

// 导入弹幕图标
import danmuIcon from '@/assets/icons/danmu.svg'

// Props
interface Props {
  video?: ShortVideo
  autoplay?: boolean
  muted?: boolean
  canGoPrevious?: boolean
  canGoNext?: boolean
  isUserLoggedIn?: boolean
  isDanmuOpen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: true,
  muted: true,
  canGoPrevious: false,
  canGoNext: false,
  isUserLoggedIn: false,
  isDanmuOpen: true
})

// Emits
const emit = defineEmits<{
  like: []
  comment: []
  favorite: []
  share: []
  follow: []
  previous: []
  next: []
  'video-end': []
}>()

// Refs
const playerContainer = ref<HTMLElement>()
const danmuComponent = ref<InstanceType<typeof Danmu>>()
const player = ref<Player>()
const isPlaying = ref(false)
const isMuted = ref(false)
const isDanmuOpen = ref(props.isDanmuOpen)
const showDanmuInput = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(100)
const currentQuality = ref('标清')
const currentSpeed = ref(1)
const isWebpageFullscreen = ref(false)
const isFullscreen = ref(false)

// 用户交互状态
const isLiked = ref(false)
const isFavorited = ref(false)
const isFollowing = ref(false)

// 清晰度选项
const qualityOptions = ref([
  { label: '标清', value: '480p' },
  { label: '高清', value: '720p' },
  { label: '超清', value: '1080p' }
])

// Computed
const currentVideo = ref(props.video)
const playerState = ref({
  isPlaying: false,
  isMuted: false,
  currentTime: 0,
  duration: 0,
  progress: 0
})

// Methods
const initPlayer = async () => {
  if (!playerContainer.value || !currentVideo.value) return

  try {
    // 销毁之前的播放器实例
    if (player.value) {
      player.value.destroy()
    }

    await nextTick()

    // 创建新的播放器实例
    player.value = new Player({
      el: playerContainer.value,
      url: currentVideo.value.url,
      width: '100%',
      height: '100%',
      autoplay: true,
      loop: false,
      muted: isMuted.value,
      poster: currentVideo.value.cover,
      playsinline: true,
      controls: false, // 使用自定义控制器
      fluid: true,
      fitVideoSize: 'fixWidth', // 修复为有效值
      videoInit: true,
      videoFillMode: 'contain', // 确保视频完整显示
      plugins: [
        {
          name: 'pip',
          install: (playerInstance: any) => {
            playerInstance.on('pip', () => {
              console.log('pip')
            })
          }
        }
      ],
    })

    // 绑定播放器事件
    bindPlayerEvents()
  } catch (error) {
    console.error('初始化播放器失败:', error)
  }
}
const getDanmuInputPlaceholder = (): string => {
  if (!props.isUserLoggedIn) {
    return '请先登录'
  }
  if (!props.isDanmuOpen) {
    return '请先开启弹幕'
  }
  return '发个弹幕吧~'
}
const bindPlayerEvents = () => {
  if (!player.value) return

  player.value.on('play', () => {
    isPlaying.value = true
    playerState.value.isPlaying = true
  })

  player.value.on('pause', () => {
    isPlaying.value = false
    playerState.value.isPlaying = false
  })

  player.value.on('ended', () => {
    isPlaying.value = false
    playerState.value.isPlaying = false
    emit('video-end')
  })

  player.value.on('volumechange', () => {
    if (player.value) {
      isMuted.value = player.value.muted
      playerState.value.isMuted = player.value.muted
    }
  })

  player.value.on('timeupdate', () => {
    if (player.value) {
      currentTime.value = player.value.currentTime
      playerState.value.currentTime = player.value.currentTime
      if (player.value.duration) {
        playerState.value.progress = (player.value.currentTime / player.value.duration) * 100
      }
    }
  })

  player.value.on('loadedmetadata', () => {
    if (player.value) {
      duration.value = player.value.duration
      playerState.value.duration = player.value.duration
      
      // 检测视频尺寸并设置背景
      handleVideoAspectRatio()
    }
  })

  player.value.on('durationchange', () => {
    if (player.value) {
      duration.value = player.value.duration
      playerState.value.duration = player.value.duration
    }
  })

  player.value.on('error', (error) => {
    console.error('播放器错误:', error)
  })
}

// 处理视频宽高比和背景
const handleVideoAspectRatio = () => {
  if (!player.value) return
  
  try {
    const video = player.value.video
    if (video && video instanceof HTMLVideoElement) {
      const aspectRatio = video.videoWidth / video.videoHeight
      
      // 设置视频宽高比属性
      if (aspectRatio < 1) {
        // 竖屏视频
        video.setAttribute('data-aspect', 'portrait')
      } else {
        // 横屏视频
        video.setAttribute('data-aspect', 'landscape')
      }
      
      // 创建背景渐变
      createVideoBackground(video)
    }
  } catch (error) {
    console.error('处理视频宽高比失败:', error)
  }
}

// 创建视频背景渐变
const createVideoBackground = (video: HTMLVideoElement) => {
  try {
    // 创建canvas来获取视频的主要颜色
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) return
    
    canvas.width = 100
    canvas.height = 100
    
    // 等待视频加载完成后获取颜色
    const updateBackground = () => {
      try {
        ctx.drawImage(video, 0, 0, 100, 100)
        const imageData = ctx.getImageData(0, 0, 100, 100)
        const data = imageData.data
        
        // 计算平均颜色
        let r = 0, g = 0, b = 0
        const pixelCount = data.length / 4
        
        for (let i = 0; i < data.length; i += 4) {
          r += data[i]
          g += data[i + 1]
          b += data[i + 2]
        }
        
        r = Math.floor(r / pixelCount)
        g = Math.floor(g / pixelCount)
        b = Math.floor(b / pixelCount)
        
        // 应用背景渐变
        const playerElement = playerContainer.value?.querySelector('.xgplayer')
        if (playerElement) {
          const bgElement = playerElement as HTMLElement
          bgElement.style.background = `
            radial-gradient(
              ellipse at center,
              rgba(${r}, ${g}, ${b}, 0.3) 0%,
              rgba(${Math.max(0, r - 30)}, ${Math.max(0, g - 30)}, ${Math.max(0, b - 30)}, 0.6) 50%,
              rgba(0, 0, 0, 0.9) 100%
            )
          `
        }
      } catch (error) {
        console.error('获取视频颜色失败:', error)
      }
    }
    
    // 监听视频播放事件来更新背景
    video.addEventListener('loadeddata', updateBackground)
    video.addEventListener('seeked', updateBackground)
    
  } catch (error) {
    console.error('创建视频背景失败:', error)
  }
}

const togglePlayPause = () => {
  if (!player.value) return

  if (playerState.value.isPlaying) {
    player.value.pause()
  } else {
    player.value.play()
  }
}

const toggleMute = () => {
  if (!player.value) return

  const newMutedState = !player.value.muted
  player.value.muted = newMutedState
  isMuted.value = newMutedState
  playerState.value.isMuted = newMutedState
  
  // 如果取消静音且当前音量为0，恢复到之前的音量
  if (!newMutedState && volume.value === 0) {
    const restoreVolume = 50 // 默认恢复到50%音量
    volume.value = restoreVolume
    player.value.volume = restoreVolume / 100
  }
}

const toggleDanmu = () => {
  isDanmuOpen.value = !isDanmuOpen.value
  if (!isDanmuOpen.value) {
    showDanmuInput.value = false
  }
}

const handlePrevious = () => {
  // 触发上一个视频事件
  emit('previous')
}

const handleNext = () => {
  // 触发下一个视频事件
  emit('next')
}

const handleProgressChange = (time: number) => {
  if (!player.value) return
  
  try {
    player.value.currentTime = time
  } catch (error) {
    console.error('跳转时间失败:', error)
  }
}

const handleDanmuInputClick = () => {
  if (props.isUserLoggedIn && isDanmuOpen.value) {
    showDanmuInput.value = true
  }
}

const handleDanmuInputFocus = () => {
  if (props.isUserLoggedIn && isDanmuOpen.value) {
    showDanmuInput.value = true
  }
}

const handleVolumeChange = (newVolume: number) => {
  if (!player.value) return
  
  try {
    volume.value = newVolume
    player.value.volume = newVolume / 100
    
    // 根据音量值设置静音状态
    if (newVolume === 0) {
      player.value.muted = true
      isMuted.value = true
      playerState.value.isMuted = true
    } else {
      player.value.muted = false
      isMuted.value = false
      playerState.value.isMuted = false
    }
  } catch (error) {
    console.error('音量调节失败:', error)
  }
}

const handleSpeedChange = (speed: number) => {
  if (!player.value) return
  
  try {
    currentSpeed.value = speed
    player.value.playbackRate = speed
  } catch (error) {
    console.error('播放速度调节失败:', error)
  }
}

const handleQualityChange = (quality: { label: string; value: string }) => {
  if (!player.value) return
  
  try {
    currentQuality.value = quality.label
    // 这里可以添加清晰度切换逻辑
    console.log('切换清晰度到:', quality)
    // 实际项目中需要根据quality.value切换视频源
  } catch (error) {
    console.error('清晰度切换失败:', error)
  }
}

const handleSettings = () => {
  // 这里可以添加设置面板逻辑
  console.log('打开设置面板')
}

const handleQuality = () => {
  // 这里可以添加清晰度切换逻辑
  console.log('切换清晰度')
}

const handleDanmuSent = (danmu: any) => {
  console.log('弹幕发送成功:', danmu)
  // 手动添加测试弹幕到显示
  if (danmuComponent.value) {
    danmuComponent.value.addDanmu('用户发送的弹幕: ' + danmu.text, danmu.color, danmu.fontSize)
  }
}

const updateVideo = async (newVideo: ShortVideo) => {
  if (!player.value || !newVideo) return

  try {
    currentVideo.value = newVideo
    player.value.src = newVideo.url
    player.value.poster = newVideo.cover
    
    // 重置时间
    currentTime.value = 0
    duration.value = 0
    
    // 自动播放新视频
    await player.value.play()
  } catch (error) {
    console.error('更新视频失败:', error)
  }
}

// 添加全屏功能
const handleWebpageFullscreen = () => {
  isWebpageFullscreen.value = !isWebpageFullscreen.value
  const container = playerContainer.value?.closest('.short-player-container') as HTMLElement
  if (container) {
    if (isWebpageFullscreen.value) {
      container.classList.add('webpage-fullscreen')
    } else {
      container.classList.remove('webpage-fullscreen')
    }
  }
}

const handleFullscreen = () => {
  if (!playerContainer.value) return
  
  try {
    if (!document.fullscreenElement) {
      playerContainer.value.requestFullscreen()
      isFullscreen.value = true
    } else {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  } catch (error) {
    console.error('全屏操作失败:', error)
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// Watchers
watch(() => props.video, (newVideo) => {
  if (newVideo && newVideo.id !== currentVideo.value?.id) {
    updateVideo(newVideo)
  }
}, { immediate: true })

watch(() => props.isDanmuOpen, (newValue) => {
  isDanmuOpen.value = newValue
}, { immediate: true })

// Lifecycle
onMounted(() => {
  initPlayer()
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  if (player.value) {
    player.value.destroy()
  }
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped lang="scss">
.short-player-container {
  position: relative;
  background: #000;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;

  .video-player {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    // 确保视频居中显示
    :deep(.xgplayer) {
      width: 100% !important;
      height: 100% !important;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #000;
      position: relative;
      
      .xgplayer-video {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
        z-index: 2;
        position: relative;
        
        // 根据视频尺寸调整显示方式
        &[data-aspect="portrait"] {
          // 竖屏视频：垂直居中，横向等比例缩放
          height: 100%;
          width: auto;
          max-width: 100%;
        }
        
        &[data-aspect="landscape"] {
          // 横屏视频：水平居中，纵向等比例缩放
          width: 100%;
          height: auto;
          max-height: 100%;
        }
      }
      
      // 背景容器
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
        background: #000;
        transition: background 0.5s ease;
      }
      
      // 确保控制器在最上层
      .xgplayer-controls,
      .xgplayer-start,
      .xgplayer-loading {
        z-index: 10;
      }
    }
  }

  .player-wrapper {
    position: relative;
    width: 100%;
    height: 100%;

    .xgplayer-container {
      width: 100%;
      height: 100%;
    }
  }

  .player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;

    > * {
      pointer-events: auto;
    }
  }
}

/* 右侧交互按钮容器 */
.right-side-container {
  position: absolute;
  right: 16px;
  bottom: 120px;
  z-index: 20;
}

/* 底部视频信息容器 */
.video-bottom-container {
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  z-index: 20;
  pointer-events: none;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  .danmu-controls {
      display: flex;
      align-items: center;
      button{
        border-radius: inherit;
      }
      .danmu-toggle-btn {
        height: 36px;
        padding: 0 8px;
        border-radius: none;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        transition: all 0.2s ease;
        color: white;
        display: flex;
        align-items: center;

        &.active {
          background: rgba(255, 107, 107, 0.8);
          border-color: rgba(255, 107, 107, 0.9);
          color: white;

          &:hover {
            background: rgba(255, 107, 107, 0.9);
            border-color: rgba(255, 107, 107, 1);
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }

        img {
          width: 18px;
          height: 18px;
          filter: brightness(0) invert(1);
        }
      }

      .danmu-input-wrapper {
        flex: 1;
        max-width: 200px;

        .danmu-input {
          width: 100%;
          padding: 10px 12px;
          border: none;
          border-top-right-radius:10px;
          border-bottom-right-radius:10px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          font-size: 14px;
          outline: none;
          cursor: pointer;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &::placeholder {
            color: rgba(255, 255, 255, 0.6);
          }

          &:not(:disabled) {
            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.3);
            }

            &:focus {
              background: rgba(255, 255, 255, 0.25);
              border-color: #ff6b6b;
              cursor: text;
            }
          }

          &:disabled {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.4);
            cursor: not-allowed;

            &::placeholder {
              color: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }

    .danmu-input-btn {
      height: 36px;
      width: 36px;
      padding: 0;
      border-top-left-radius:10px;
      border-bottom-left-radius:10px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }

      svg {
        width: 18px;
        height: 18px;
        fill: white;
      }
    }
}

/* 允许 PlayerControls 接收点击事件 */
.video-bottom-container :deep(.player-controls) {
  pointer-events: auto;
}

/* 网页全屏样式 */
.short-player-container.webpage-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #000;
}

/* 隐藏 xgplayer 默认控制栏 */
:deep(.xgplayer-controls) {
  display: none !important;
}

:deep(.xgplayer-start) {
  display: none !important;
}

:deep(.xgplayer-loading) {
  background: rgba(0, 0, 0, 0.8);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .right-side-container {
    right: 12px;
    bottom: 100px;
  }

  .video-bottom-container {
    bottom: 0px;
    left: 0px;
    right: 0px;
  }
}

@media (max-width: 480px) {
  .right-side-container {
    right: 8px;
    bottom: 90px;
  }

  .video-bottom-container {
    bottom: 0px;
    left: 0px;
    right: 0px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
  }
}
</style> 