package repository

import (
	"context"
	"fmt"
	pictureRepo "frontapi/internal/repository/pictures"
	"log"
	"testing"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 测试不等于操作符查询
func TestNotEqualOperator(t *testing.T) {
	// 连接数据库
	dsn := "root:password@tcp(127.0.0.1:3306)/lydb?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		t.Fatalf("连接数据库失败: %v", err)
	}

	// 创建图片专辑仓库
	albumRepo := pictureRepo.NewPictureAlbumRepository(db)

	// 测试场景1: 使用不等于操作符查询
	testID := "test-album-id" // 替换为实际存在的ID
	condition := map[string]interface{}{
		"id <>":  testID,
		"status": 1,
	}

	// 执行查询
	albums, total, err := albumRepo.List(context.Background(), condition, "created_at DESC", 1, 10)
	if err != nil {
		t.Fatalf("查询失败: %v", err)
	}

	// 验证结果
	fmt.Printf("查询到 %d 条记录，总数: %d\n", len(albums), total)
	for i, album := range albums {
		if album.ID == testID {
			t.Errorf("查询结果中不应该包含ID为 %s 的记录", testID)
		}
		fmt.Printf("专辑 %d: ID=%s, 标题=%s\n", i+1, album.ID, album.Title)
	}

	// 测试场景2: 使用多个操作符
	condition2 := map[string]interface{}{
		"id <>":           testID,
		"status":          1,
		"picture_count >": 0,
	}

	// 执行查询
	albums2, total2, err := albumRepo.List(context.Background(), condition2, "created_at DESC", 1, 10)
	if err != nil {
		t.Fatalf("查询失败: %v", err)
	}

	// 验证结果
	fmt.Printf("查询到 %d 条记录，总数: %d\n", len(albums2), total2)
	for i, album := range albums2 {
		if album.ID == testID {
			t.Errorf("查询结果中不应该包含ID为 %s 的记录", testID)
		}
		if album.PictureCount <= 0 {
			t.Errorf("查询结果中的图片数量应该大于0，但实际为 %d", album.PictureCount)
		}
		fmt.Printf("专辑 %d: ID=%s, 标题=%s, 图片数量=%d\n", i+1, album.ID, album.Title, album.PictureCount)
	}
}

func main() {
	// 连接数据库
	dsn := "root:password@tcp(127.0.0.1:3306)/lydb?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 创建图片专辑仓库
	albumRepo := pictureRepo.NewPictureAlbumRepository(db)

	// 使用不等于操作符查询
	testID := "test-album-id" // 替换为实际存在的ID
	condition := map[string]interface{}{
		"id <>":  testID,
		"status": 1,
	}

	// 执行查询
	albums, total, err := albumRepo.List(context.Background(), condition, "created_at DESC", 1, 10)
	if err != nil {
		log.Fatalf("查询失败: %v", err)
	}

	// 输出结果
	fmt.Printf("查询到 %d 条记录，总数: %d\n", len(albums), total)
	for i, album := range albums {
		fmt.Printf("专辑 %d: ID=%s, 标题=%s\n", i+1, album.ID, album.Title)
	}
}
