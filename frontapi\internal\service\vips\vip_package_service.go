package vips

import (
	"frontapi/internal/models/vips"
	repo "frontapi/internal/repository/vips"
	"frontapi/internal/service/base"
)

type CreateVipPackageRequest struct {
	Name          string  `json:"name" validate:"required"`
	Code          string  `json:"code" validate:"required"`
	Price         float64 `json:"price" validate:"required"`
	OriginalPrice float64 `json:"original_price"`
	Duration      int     `json:"duration" validate:"required"`
	Description   string  `json:"description"`
	Benefits      string  `json:"benefits"`
	Icon          string  `json:"icon"`
	SortOrder     int     `json:"sort_order"`
	Status        int8    `json:"status"`
}

type UpdateVipPackageRequest struct {
	Name          string  `json:"name"`
	Code          string  `json:"code"`
	Price         float64 `json:"price"`
	OriginalPrice float64 `json:"original_price"`
	Duration      int     `json:"duration"`
	Description   string  `json:"description"`
	Benefits      string  `json:"benefits"`
	Icon          string  `json:"icon"`
	SortOrder     int     `json:"sort_order"`
	Status        int8    `json:"status"`
}

type VipPackageService interface {
	base.IExtendedService[vips.VipPackage]
}

type vipPackageService struct {
	*base.ExtendedService[vips.VipPackage]
	repo repo.VipPackageRepository
}

func NewVipPackageService(repo repo.VipPackageRepository) VipPackageService {
	return &vipPackageService{
		ExtendedService: base.NewExtendedService[vips.VipPackage](repo, "vip_package"),
		repo:            repo,
	}
}
