// 主题组件统一导出
// 可以通过修改这里的导入路径来切换不同的主题

// Slinky 主题组件
export { default as ApprovalDialog } from './slinky/dialog/ApprovalDialog.vue'
export { default as BatchActionDialog } from './slinky/dialog/BatchActionDialog.vue'
export { default as BatchApprovalDialog } from './slinky/dialog/BatchApprovalDialog.vue'
export { default as CreateDialog } from './slinky/dialog/CreateDialog.vue'
export { default as DetailDialog } from './slinky/dialog/DetailDialog.vue'
export { default as EditDialog } from './slinky/dialog/EditDialog.vue'
export { default as SinglePager } from './slinky/pager/SinglePager.vue'
export { default as SearchBar } from './slinky/searchbox/SearchBar.vue'
export { default as ActionTable } from './slinky/tables/ActionTable.vue'
export { default as SlinkyTable } from './slinky/tables/SlinkyTable.vue'

// 主题样式
import './slinky/styles/dialog.scss'
import './slinky/styles/table.scss'

// 类型导出
export type {
    ActionTableAction, ActionTableColumn, ActionTableConfig
} from "./slinky/tables/ActionTable.vue"

export type {
    SearchConfig, SearchField
} from './slinky/searchbox/SearchBar.vue'

export type {
    CreateDialogConfig, CreateFormField
} from './slinky/dialog/CreateDialog.vue'

export type {
    EditDialogConfig, EditFormField
} from './slinky/dialog/EditDialog.vue'

export type {
    DetailDialogConfig, DetailField
} from './slinky/dialog/DetailDialog.vue'

export type {
    BatchActionConfig, BatchFormField
} from './slinky/dialog/BatchActionDialog.vue'

// 主题切换函数
export const switchTheme = (themeName: string) => {
    // 预留主题切换功能
    console.log(`Switching to theme: ${themeName}`)
}

// 默认配置
export const defaultConfigs = {
    table: {
        size: 'default',
        border: true,
        stripe: true,
        showHeader: true,
        highlightCurrentRow: false,
        emptyText: '暂无数据'
    },
    dialog: {
        width: '600px',
        top: '15vh',
        modal: true,
        appendToBody: true,
        lockScroll: true,
        closeOnClickModal: false,
        closeOnPressEscape: true,
        showClose: true,
        destroyOnClose: false
    },
    form: {
        labelWidth: '100px',
        labelPosition: 'left',
        size: 'default'
    },
    pagination: {
        pageSize: 10,
        pageSizes: [10, 20, 50, 100],
        layout: 'total, sizes, prev, pager, next, jumper',
        background: true
    }
}

// 工具函数
export const formatters = {
    // 日期格式化
    date: (value: any, format = 'YYYY-MM-DD HH:mm:ss') => {
        if (!value) return '暂无'
        return new Date(value).toLocaleString()
    },

    // 状态格式化
    status: (value: any, options: { value: any; label: string }[]) => {
        const option = options.find(opt => opt.value === value)
        return option?.label || '未知'
    },

    // 数字格式化
    number: (value: any, precision = 2) => {
        if (value == null || value === '') return '0'
        return Number(value).toFixed(precision)
    },

    // 文件大小格式化
    fileSize: (bytes: number) => {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
}

// 验证规则
export const validationRules = {
    required: { required: true, message: '此项为必填项', trigger: 'blur' },
    email: { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
    phone: { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
    url: { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' },
    number: { type: 'number', message: '请输入数字', trigger: 'blur' },
    integer: { type: 'integer', message: '请输入整数', trigger: 'blur' },
    minLength: (min: number) => ({ min, message: `长度不能少于${min}个字符`, trigger: 'blur' }),
    maxLength: (max: number) => ({ max, message: `长度不能超过${max}个字符`, trigger: 'blur' }),
    range: (min: number, max: number) => ({ min, max, message: `长度应在${min}-${max}个字符之间`, trigger: 'blur' })
}

// 常用选项
export const commonOptions = {
    status: [
        { value: 1, label: '启用', type: 'success' },
        { value: 0, label: '禁用', type: 'danger' }
    ],
    yesNo: [
        { value: true, label: '是', type: 'success' },
        { value: false, label: '否', type: 'info' }
    ],
    gender: [
        { value: 1, label: '男', type: 'primary' },
        { value: 2, label: '女', type: 'danger' },
        { value: 0, label: '未知', type: 'info' }
    ]
} 