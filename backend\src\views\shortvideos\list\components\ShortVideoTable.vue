<template>
  <div class="shortvideo-table">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="success" size="small" @click="handleBatchStatus(2)">
          批量发布
        </el-button>
        <el-button type="warning" size="small" @click="handleBatchStatus(1)">
          批量下架
        </el-button>
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 使用SlinkyTable组件 -->
    <SlinkyTable
      ref="tableRef"
      :data="shortVideoList"
      :loading="loading"
      row-key="id"
      @selection-change="handleSelectionChange"
      show-selection
      show-index
      index-label="序号"
      :show-header="true"
      :show-actions="true"
      action-label="操作"
      action-width="200"
      :show-view="true"
      :show-edit="true"
      :show-delete="true"
      @view="handleView"
      @edit="handleEdit"
      @delete="handleDelete"
      @refresh="$emit('refresh')"
    >
      <!-- 标题列 -->
      <el-table-column prop="title" label="基本信息" align="left" min-width="180">
        <template #default="{ row }">
          <div class="video-info">
            <el-image 
              :src="row.cover" 
              fit="cover" 
              class="video-cover"
              :preview-src-list="[row.cover]"
              preview-teleported
              :initial-index="0"
            >
              <template #error>
                <div class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
            <div class="video-detail">
              <div class="video-title" :title="row.title">{{ row.title }}</div>
              <div class="video-meta">
                <span class="duration">{{ formatDuration(row.duration) }}</span>
                <span class="resolution">{{ row.width }}x{{ row.height }}</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 分类列 -->
      <el-table-column prop="category_name" label="分类" min-width="100">
        <template #default="{ row }">
          <el-tag size="small" type="info" effect="plain">
            {{ row.category_name || '未分类' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 创建者列 -->
      <el-table-column prop="creator_name" label="创建者" min-width="120">
        <template #default="{ row }">
          <div class="creator-info" v-if="row.creator_name">
            <el-avatar :size="24" :src="row.creator_avatar">
              {{ row.creator_name.charAt(0) }}
            </el-avatar>
            <span class="creator-name">{{ row.creator_name }}</span>
          </div>
          <span v-else>未知</span>
        </template>
      </el-table-column>

      <!-- 状态列 -->
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag 
            :type="getStatusType(row.status)" 
            :effect="getStatusEffect(row.status)"
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 数据统计列 -->
      <el-table-column label="数据统计" width="180">
        <template #default="{ row }">
          <div class="stats">
            <el-tooltip content="播放量" placement="top">
              <span class="stat-item">
                <el-icon><View /></el-icon>
                {{ formatNumber(row.views) }}
              </span>
            </el-tooltip>
            <el-tooltip content="点赞数" placement="top">
              <span class="stat-item">
                <el-icon><Star /></el-icon>
                {{ formatNumber(row.likes) }}
              </span>
            </el-tooltip>
            <el-tooltip content="评论数" placement="top">
              <span class="stat-item">
                <el-icon><ChatDotRound /></el-icon>
                {{ formatNumber(row.comments) }}
              </span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>

      <!-- 创建时间列 -->
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <!-- 自定义操作列 -->
      <template #actions="{ row }">
        <el-button
          type="primary"
          link
          size="small"
          @click="handleView(row)"
        >
          查看
        </el-button>
        <el-button
          type="primary"
          link
          size="small"
          @click="handleEdit(row)"
        >
          编辑
        </el-button>
        <!-- 根据状态显示发布或下架按钮 -->
        <el-button
          v-if="row.status === 2"
          type="warning"
          link
          size="small"
          @click="handleChangeStatus(row, 1)"
        >
          下架
        </el-button>
        <el-button
          v-else
          type="success"
          link
          size="small"
          @click="handleChangeStatus(row, 2)"
        >
          发布
        </el-button>
        <el-button
          type="danger"
          link
          size="small"
          @click="handleDelete(row)"
        >
          删除
        </el-button>
      </template>
    </SlinkyTable>

    <!-- 分页器 -->
  <div class="table-footer">
    <SinglePager
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
     show-jump-info
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { SinglePager, SlinkyTable } from '@/components/themes';
import { batchDeleteShortVideo, batchUpdateShortVideoStatus } from '@/service/api/shortvideos/shortvideos';
import type { ShortVideo } from '@/types/shortvideos';
import {
    ChatDotRound,
    Check,
    Picture,
    Star,
    View
} from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { defineEmits, defineProps, ref } from 'vue';

const props = defineProps({
  shortVideoList: {
    type: Array as () => ShortVideo[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits([
  'update:currentPage', 
  'update:pageSize', 
  'refresh', 
  'view', 
  'edit', 
  'delete', 
  'change-status'
]);

// 表格引用
const tableRef = ref();

// 选中的行
const selectedRows = ref<ShortVideo[]>([]);

// 处理选择变化
const handleSelectionChange = (selection: ShortVideo[]) => {
  selectedRows.value = selection;
};

// 处理查看
const handleView = (row: ShortVideo) => {
  emit('view', row);
};

// 处理编辑
const handleEdit = (row: ShortVideo) => {
  emit('edit', row);
};

// 处理删除
const handleDelete = (row: ShortVideo) => {
  ElMessageBox.confirm(`确定要删除短视频"${row.title}"吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    emit('delete', row);
  }).catch(() => {
    // 取消删除
  });
};

// 处理状态变更
const handleChangeStatus = (row: ShortVideo, status: number) => {
  const statusText = getStatusText(status);
  ElMessageBox.confirm(`确定要将短视频"${row.title}"${statusText}吗？`, '状态变更确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    emit('change-status', { id: row.id, status });
  }).catch(() => {
    // 取消操作
  });
};

// 批量更新状态
const handleBatchStatus = (status: number) => {
  const ids = selectedRows.value.map(item => item.id);
  const statusText = getStatusText(status);
  
  ElMessageBox.confirm(`确定要将选中的${selectedRows.value.length}个短视频${statusText}吗？`, '批量状态变更确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await batchUpdateShortVideoStatus({ ids, status });
      ElMessage.success(`批量${statusText}成功`);
      emit('refresh');
      // 清除选择
      if (tableRef.value) {
        tableRef.value.clearSelection();
      }
    } catch (error) {
      console.error('批量更新状态失败:', error);
      ElMessage.error(`批量${statusText}失败`);
    }
  }).catch(() => {
    // 取消操作
  });
};

// 批量删除
const handleBatchDelete = () => {
  const ids = selectedRows.value.map(item => item.id);
  
  ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}个短视频吗？`, '批量删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await batchDeleteShortVideo({ ids });
      ElMessage.success('批量删除成功');
      emit('refresh');
      // 清除选择
      if (tableRef.value) {
        tableRef.value.clearSelection();
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }).catch(() => {
    // 取消操作
  });
};

// 处理页码变化
const handlePageChange = (page: number) => {
  emit('update:currentPage', page);
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  emit('update:pageSize', size);
};

// 格式化时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '00:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 格式化数字
const formatNumber = (num: number) => {
  if (!num) return '0';
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k';
  }
  return num.toString();
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0: return '待审核';
    case 1: return '已下架';
    case 2: return '已发布';
    case -2: return '已拒绝';
    case -4: return '已删除';
    default: return '未知状态';
  }
};

// 获取状态标签类型
const getStatusType = (status: number) => {
  switch (status) {
    case 0: return 'info';
    case 1: return 'warning';
    case 2: return 'success';
    case -2: return 'danger';
    case -4: return 'danger';
    default: return 'info';
  }
};

// 获取状态标签效果
const getStatusEffect = (status: number) => {
  switch (status) {
    case -4: return 'dark';
    default: return 'light';
  }
};
</script>

<style scoped lang="scss">
.shortvideo-table {
  .batch-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    margin-bottom: 16px;
    background-color: #f0f9eb;
    border: 1px solid #e1f3d8;
    border-radius: 4px;

    .batch-info {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #67c23a;

      strong {
        font-weight: bold;
        margin: 0 4px;
      }
    }

    .batch-actions {
      display: flex;
      gap: 8px;
    }
  }

  .video-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .video-cover {
      width: 100px;
      height: 56px;
      border-radius: 4px;
      overflow: hidden;
      flex-shrink: 0;
    }

    .image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;
      color: #909399;
    }

    .video-detail {
      display: flex;
      flex-direction: column;
      gap: 4px;
      overflow: hidden;

      .video-title {
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .video-meta {
        display: flex;
        gap: 8px;
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .creator-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .creator-name {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .stats {
    display: flex;
    gap: 12px;

    .stat-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #606266;
    }
  }
  .table-footer{
    padding: 16px 24px;
    background: var(--el-bg-color-page);
    border-top: 1px solid var(--el-border-color-lighter);
  }
}
</style> 