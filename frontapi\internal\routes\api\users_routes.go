package api

import (
	"frontapi/internal/api/users"
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

// RegisterUserRoutes 注册用户管理相关路由
func RegisterUserRoutes(app *fiber.App, apiGroup fiber.Router, service *bootstrap.ServiceContainer) {
	// 创建用户控制器
	// userController := users.NewUserController(service.UserService, service.VideoService)
	celebrityController := users.NewCelebrityController(
		service.UserService,
		service.UserFollowsService,
		service.VideoService,
		service.ShortVideoService,
		service.PostService,
		service.VideoAlbumService,
		service.VideoCommentService,
		service.PostCommentService,
		service.ShortVideoCommentService,
		service.PictureService,
	)
	// 用户管理路由组
	celebrityRoutes := apiGroup.Group("/celebrity")
	{
		// 用户管理接口
		celebrityRoutes.Post("/getAllCelebrity", celebrityController.GetAllCelebrity)
		celebrityRoutes.Post("/getCelebrityDetail", celebrityController.GetCelebrityDetail)
		celebrityRoutes.Post("/getCelebrityVideoList", celebrityController.GetCelebrityVideoList)
		celebrityRoutes.Post("/getCelebrityImageList", celebrityController.GetCelebrityImageList)
		celebrityRoutes.Post("/getCelebrityPostList", celebrityController.GetCelebrityPostList)
		celebrityRoutes.Post("/getCelebrityShortVideoList", celebrityController.GetCelebrityShortVideoList)
		celebrityRoutes.Post("/getFollowersStats", celebrityController.GetFollowersStats)
		// celebrityRoutes.Post("/getCelebrityAlbumList", celebrityController.GetCelebrityAlbumList)
		celebrityRoutes.Post("/getCelebrityComments", celebrityController.GetCelebrityComments)
		// celebrityRoutes.Post("/getCelebrityLikeList", celebrityController.GetCelebrityLikeList)
		// celebrityRoutes.Post("/getCelebrityFollowList", celebrityController.GetCelebrityFollowList)

	}
	// userRoutes := apiGroup.Group("/user")
	// {
	// 	userRoutes.Post("/getCreatorList", userController.GetCreatorList)
	// }

	// 创作者路由组
	userCreatorRoute := apiGroup.Group("/creator")
	{
		creatorController := users.NewCreatorController(
			service.UserService,
			service.VideoService,
			service.PostService,
			service.ShortVideoService,
			service.VideoAlbumService,
			service.VideoCommentService,
			service.PostCommentService,
			service.ShortVideoCommentService,
		)
		userCreatorRoute.Post("/getCreatorList", creatorController.GetCreatorList)
		userCreatorRoute.Post("/getCreatorDetail", creatorController.GetCreatorDetail)
		userCreatorRoute.Post("/getCreatorVideos", creatorController.GetCreatorVideos)
		userCreatorRoute.Post("/getCreatorPosts", creatorController.GetCreatorPosts)
		userCreatorRoute.Post("/getCreatorShortVideos", creatorController.GetCreatorShortVideos)
		userCreatorRoute.Post("/getCreatorAlbums", creatorController.GetCreatorAlbums)
		userCreatorRoute.Post("/getCreatorComments", creatorController.GetCreatorComments)
		userCreatorRoute.Post("/followCreator", creatorController.FollowCreator)
		userCreatorRoute.Post("/unfollowCreator", creatorController.UnfollowCreator)
		userCreatorRoute.Post("/checkFollowStatus", creatorController.CheckFollowStatus)
	}
}
