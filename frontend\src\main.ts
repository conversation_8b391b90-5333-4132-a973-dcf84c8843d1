
import { createApp } from 'vue';
import App from './App.vue';

// 类型声明
declare global {
    interface Window {
        __i18n_messages?: Record<string, Record<string, any>>;
        i18n?: any;
        $setLanguage?: (locale: string) => Promise<boolean>;
    }
}

// 路由
import router from '@/router';

// 状态管理
import pinia from '@/store';

// UI库
import { availableThemes } from '@/shared/themes';
import naive from 'naive-ui';
import PrimeVue from 'primevue/config';

// PrimeVue 图标
import 'primeicons/primeicons.css';

// 引入主题配置
import { APP_DARK_CLASS } from '@/config/theme.config';

// 引入核心插件
import { setupPlugins } from '@/core/plugins';

// 样式
import '@/assets/styles/index.scss';

// 创建应用实例
const app = createApp(App);

// 注册基础插件
app.use(pinia);  // 先初始化Pinia，确保store可用
app.use(router);
app.use(naive)

app.use(PrimeVue, {
    // PrimeVue配置
    ripple: true,
    inputStyle: 'filled',
    unstyled: false,
    pt: {},
    // 主题配置
    theme: {
        preset: availableThemes.aura, // 默认使用Aura预设，后续会通过ThemeManager动态更改
        options: {
            prefix: 'p',
            darkModeSelector: `.${APP_DARK_CLASS}`,
            cssLayer: false
        }
    }
});

// 添加全局重新渲染机制
app.config.globalProperties.$forceUpdate = function () {
    this.$forceUpdate();
};

// 异步初始化应用
async function initApp() {
    try {
        // 初始化插件
        await setupPlugins(app);

        // 全局错误处理
        app.config.errorHandler = (err, vm, info) => {
            console.error('Vue Error:', err, info);
        };

        // 语言切换事件处理
        window.addEventListener('i18n:locale-changed', (event: Event) => {
            // 刷新所有组件
            const root = app._instance;
            if (root) {
                setTimeout(() => {
                    root.proxy?.$forceUpdate();
                    console.log('Forced app update after language change');
                }, 0);
            }
        });

        // 挂载应用
        app.mount('#app');

        console.log('Application initialized successfully!');
    } catch (error) {
        console.error('Failed to initialize application:', error);
    }
}

// 启动应用
initApp();