import { createApp } from 'vue';
import App from './App.vue';

// 类型声明
declare global {
    interface Window {
        __i18n_messages?: Record<string, Record<string, any>>;
        i18n?: any;
        $setLanguage?: (locale: string) => Promise<boolean>;
    }
}

// 路由
import router from '@/router';

// 状态管理
import pinia from '@/store';

// UI库
import PrimeVue from 'primevue/config';
import { definePreset } from '@primeuix/themes';
import Aura from '@primeuix/themes/aura';

// PrimeVue 图标
import 'primeicons/primeicons.css';

// 引入核心插件
import { setupPlugins } from '@/core/plugins';
import { initializeThemePresetManager } from '@/core/plugins/theme/preset-manager';

// 样式
import '@/assets/styles/index.scss';

// 创建应用实例
const app = createApp(App);

// 注册基础插件
app.use(pinia);  // 先初始化Pinia，确保store可用
app.use(router);
// 创建默认主题预设
const defaultPreset = definePreset(Aura, {
    semantic: {
        primary: {
            50: '#eff6ff',
            100: '#dbeafe',
            200: '#bfdbfe',
            300: '#93c5fd',
            400: '#60a5fa',
            500: '#3b82f6',
            600: '#2563eb',
            700: '#1d4ed8',
            800: '#1e40af',
            900: '#1e3a8a',
            950: '#172554'
        }
    }
});

app.use(PrimeVue, {
    // PrimeVue配置
    ripple: true,
    inputStyle: 'filled',
    theme: {
        preset: defaultPreset,
        options: {
            prefix: 'p',
            darkModeSelector: '.app-dark',
            cssLayer: false
        }
    }
});

// 添加全局重新渲染机制
app.config.globalProperties.$forceUpdate = function () {
    this.$forceUpdate();
};

// 异步初始化应用
async function initApp() {
    try {
        // 初始化插件
        await setupPlugins(app);
        
        // 初始化主题预设管理器
        initializeThemePresetManager(app);

        // 全局错误处理
        app.config.errorHandler = (err, vm, info) => {
            console.error('Vue Error:', err, info);
        };

        // 语言切换事件处理（设置为全局处理器，确保所有组件都能接收到变更）
        window.addEventListener('i18n:locale-changed', (event: Event) => {
            // 刷新所有组件
            const root = app._instance;
            if (root) {
                // 强制重新渲染整个应用
                setTimeout(() => {
                    root.proxy?.$forceUpdate();
                    console.log('Forced app update after language change');
                }, 0);
            }
        });

        // 挂载应用
        app.mount('#app');

        console.log('Application initialized successfully!');
    } catch (error) {
        console.error('Failed to initialize application:', error);
    }
}

// 启动应用
initApp();