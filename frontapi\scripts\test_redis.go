package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"frontapi/config"
	"frontapi/pkg/redis"
	"frontapi/pkg/types"
)

// TestData 测试数据结构
type TestData struct {
	ID        int              `json:"id"`
	Name      string           `json:"name"`
	Value     float64          `json:"value"`
	CreatedAt types.JSONTime   `json:"created_at"`
}

func main() {
	// 加载配置
	config.LoadConfig()

	// 初始化Redis
	if err := redis.Init(); err != nil {
		log.Fatalf("Redis初始化失败: %v", err)
	}
	defer redis.Close()

	log.Println("测试Redis缓存功能...")

	// 测试基本的SET/GET功能
	testBasicCaching()

	// 测试JSON数据的缓存和检索
	testJSONCaching()

	// 测试键过期
	testKeyExpiration()

	// 测试热点数据缓存
	testHotData()

	log.Println("Redis测试完成!")
}

// 测试基本的SET/GET功能
func testBasicCaching() {
	log.Println("\n=== 测试基本的SET/GET功能 ===")

	// 设置一个简单的字符串
	key := "test:string"
	value := "这是一个测试值"

	err := redis.Set(key, value, 10*time.Minute)
	if err != nil {
		log.Printf("设置缓存失败: %v", err)
		return
	}
	log.Printf("设置缓存成功: %s = %s", key, value)

	// 获取值
	retrievedValue, err := redis.Get(key)
	if err != nil {
		log.Printf("获取缓存失败: %v", err)
		return
	}
	log.Printf("获取缓存成功: %s = %s", key, retrievedValue)

	// 删除值
	err = redis.Del(key)
	if err != nil {
		log.Printf("删除缓存失败: %v", err)
		return
	}
	log.Printf("删除缓存成功: %s", key)

	// 尝试获取已删除的值
	_, err = redis.Get(key)
	if err != nil {
		log.Printf("预期的错误: %v", err)
	}
}

// 测试JSON数据的缓存和检索
func testJSONCaching() {
	log.Println("\n=== 测试JSON数据的缓存和检索 ===")

	// 创建测试数据
	data := TestData{
		ID:        1,
		Name:      "测试数据",
		Value:     123.45,
		CreatedAt: types.JSONTime(time.Now()),
	}

	// 将数据序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Printf("JSON序列化失败: %v", err)
		return
	}

	// 设置JSON数据
	key := "test:json"
	err = redis.Set(key, jsonData, 10*time.Minute)
	if err != nil {
		log.Printf("设置JSON缓存失败: %v", err)
		return
	}
	log.Printf("设置JSON缓存成功: %s", key)

	// 获取JSON数据
	retrievedJSON, err := redis.Get(key)
	if err != nil {
		log.Printf("获取JSON缓存失败: %v", err)
		return
	}

	// 反序列化JSON数据
	var retrievedData TestData
	err = json.Unmarshal([]byte(retrievedJSON), &retrievedData)
	if err != nil {
		log.Printf("JSON反序列化失败: %v", err)
		return
	}

	log.Printf("获取并解析JSON缓存成功: %+v", retrievedData)

	// 清理
	redis.Del(key)
}

// 测试键过期
func testKeyExpiration() {
	log.Println("\n=== 测试键过期 ===")

	// 设置一个短期键
	key := "test:expiration"
	value := "这个键将在3秒后过期"

	err := redis.Set(key, value, 3*time.Second)
	if err != nil {
		log.Printf("设置缓存失败: %v", err)
		return
	}
	log.Printf("设置短期缓存成功: %s = %s (3秒过期时间)", key, value)

	// 立即验证键是否存在
	exists, err := redis.Exists(key)
	if err != nil {
		log.Printf("检查键存在失败: %v", err)
	} else {
		log.Printf("键是否存在: %v", exists)
	}

	// 等待过期
	log.Println("等待3秒让键过期...")
	time.Sleep(4 * time.Second)

	// 再次检查键是否存在
	exists, err = redis.Exists(key)
	if err != nil {
		log.Printf("检查键存在失败: %v", err)
	} else {
		log.Printf("3秒后键是否存在: %v", exists)
	}
}

// 测试热点数据缓存
func testHotData() {
	log.Println("\n=== 测试热点数据缓存 ===")

	// 模拟视频数据
	videoID := "test-video-1"
	videoData := map[string]interface{}{
		"id":          videoID,
		"title":       "测试视频标题",
		"description": "这是一个测试视频描述",
		"view_count":  1000,
		"like_count":  500,
		"duration":    120,
	}

	// 序列化视频数据
	jsonData, err := json.Marshal(videoData)
	if err != nil {
		log.Printf("JSON序列化失败: %v", err)
		return
	}

	// 使用视频缓存功能
	err = redis.SetHotVideo(videoID, string(jsonData), 10*time.Minute)
	if err != nil {
		log.Printf("设置热点视频缓存失败: %v", err)
		return
	}
	log.Printf("设置热点视频缓存成功: video:%s", videoID)

	// 获取视频缓存
	retrievedJSON, err := redis.GetHotVideo(videoID)
	if err != nil {
		log.Printf("获取热点视频缓存失败: %v", err)
		return
	}

	// 解析视频数据
	var retrievedVideo map[string]interface{}
	err = json.Unmarshal([]byte(retrievedJSON), &retrievedVideo)
	if err != nil {
		log.Printf("JSON反序列化失败: %v", err)
		return
	}

	log.Printf("获取热点视频缓存成功: %+v", retrievedVideo)

	// 清理
	key := fmt.Sprintf("hot:video:%s", videoID)
	redis.Del(key)
}
