package books

import (
	"frontapi/internal/hooks"
	"frontapi/internal/hooks/common"
	"frontapi/internal/models/books"
	bookRepo "frontapi/internal/repository/books"
	"frontapi/internal/service/base"
)

// BookCategoryService 电子书分类服务接口
type BookCategoryService interface {
	// 继承BaseService的所有方法
	base.IExtendedService[books.BookCategory]
}

type bookCategoryService struct {
	*base.ExtendedService[books.BookCategory] // 嵌入BaseService，自动获得所有方法
	categoryRepo                              bookRepo.CategoryRepository
}

// NewCategoryService 创建电子书分类服务实例
func NewBookCategoryService(
	categoryRepo bookRepo.CategoryRepository,
) BookCategoryService {
	service := &bookCategoryService{
		ExtendedService: base.NewExtendedService[books.BookCategory](categoryRepo, "book_category"),
		categoryRepo:    categoryRepo,
	}

	// 注册创建前钩子，检查同名分类
	duplicateCheckHook := common.NewDuplicateCheckHook(
		categoryRepo.GetDB(),
		categoryRepo.GetTableName(),
		[]string{"Name"},
		"分类名称已存在，请使用其他名称",
	)
	service.RegisterHook(hooks.BeforeCreate, duplicateCheckHook.Execute)
	service.RegisterHook(hooks.BeforeUpdate, duplicateCheckHook.Execute)

	return service
}
