package pictures

import (
	"frontapi/internal/admin"
	"frontapi/internal/models"
	pictureModel "frontapi/internal/models/pictures"
	"frontapi/internal/service/pictures"
	pictureValidator "frontapi/internal/validation/pictures"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// PictureController 图片控制器结构体
type PictureController struct {
	admin.BaseController
	pictureService    pictures.PictureService
	categoryService   pictures.PictureCategoryService
	albumService      pictures.PictureAlbumService
	collectionService pictures.PictureCollectionService
}

// NewPictureController 创建图片控制器
func NewPictureController(
	pictureService pictures.PictureService,
	categoryService pictures.PictureCategoryService,
	albumService pictures.PictureAlbumService,
	collectionService pictures.PictureCollectionService,
) *PictureController {
	return &PictureController{
		pictureService:    pictureService,
		categoryService:   categoryService,
		albumService:      albumService,
		collectionService: collectionService,
	}
}

// ==============================
// 图片基本操作
// ==============================

// GetPicture 获取图片详情
func (h *PictureController) GetPicture(c *fiber.Ctx) error {
	id := c.Params("id")
	if id == "" {
		return h.BadRequest(c, "图片ID不能为空", nil)
	}

	picture, err := h.pictureService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "图片不存在")
	}

	// 增加查看次数
	go h.pictureService.UpdateCount(c.Context(), id, "view_count", 1)

	return h.Success(c, picture)
}

// CreatePicture 创建图片
func (h *PictureController) CreatePicture(c *fiber.Ctx) error {
	// 检查用户是否已登录
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	// 解析请求参数
	var req pictureValidator.CreatePictureRequest
	if err := h.ParseRequestData(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 设置作者ID
	req.CreatorID = userID

	// 正确初始化结构体指针，包括嵌入的ContentBaseModel
	pictureCreate := &pictureModel.Picture{
		ContentBaseModel: &models.ContentBaseModel{},
	}
	if err := utils.SmartCopy(req, pictureCreate); err != nil {
		return h.InternalServerError(c, "字段映射失败: "+err.Error())
	}

	// 创建图片
	pictureID, err := h.pictureService.Create(c.Context(), pictureCreate)
	if err != nil {
		return h.InternalServerError(c, "创建图片失败: "+err.Error())
	}

	return h.Success(c, fiber.Map{"id": pictureID, "message": "创建图片成功"})
}

// UpdatePicture 更新图片
func (h *PictureController) UpdatePicture(c *fiber.Ctx) error {
	// 获取图片ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req pictureValidator.UpdatePictureRequest
	if err := h.ParseRequestData(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 获取现有图片信息
	picture, err := h.pictureService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "图片不存在: "+err.Error())
	}

	// 更新图片信息
	if err := utils.SmartCopy(req, picture); err != nil {
		return h.InternalServerError(c, "字段映射失败: "+err.Error())
	}

	// 更新图片
	err = h.pictureService.UpdateById(c.Context(), id, picture)
	if err != nil {
		return h.InternalServerError(c, "更新图片失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新图片成功")
}

// DeletePicture 删除图片
func (h *PictureController) DeletePicture(c *fiber.Ctx) error {
	// 获取图片ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	if err := h.pictureService.SoftDelete(c.Context(), id); err != nil {
		return h.InternalServerError(c, "删除图片失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "删除图片成功")
}

// ListPictures 获取图片列表
func (h *PictureController) ListPictures(c *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 提取查询参数
	title := reqInfo.Get("title").GetString()
	categoryID := reqInfo.Get("category_id").GetString()
	albumID := reqInfo.Get("album_id").GetString()
	status := -999
	_ = h.GetIntegerValueWithDataWrapper(c, "status", &status)

	// 构建查询条件
	condition := map[string]interface{}{
		"title":       title,
		"category_id": categoryID,
		"album_id":    albumID,
		"status":      status,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at desc"
	}
	// 查询图片列表
	pictures, total, err := h.pictureService.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取图片列表失败: "+err.Error())
	}

	return h.SuccessList(c, pictures, total, page, pageSize)
}

// BatchCreatePictures 批量创建图片
func (c *PictureController) BatchCreatePictures(ctx *fiber.Ctx) error {
	// 获取当前请求信息
	//reqInfo := c.GetRequestInfo(ctx)
	// 校验请求数据
	var request pictureValidator.BatchCreatePicturesRequest
	if err := validator.ValidateDataWrapper(ctx, &request); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量创建图片
	response, err := c.pictureService.BatchCreatePictures(ctx.Context(), &request)
	if err != nil {
		return c.InternalServerError(ctx, "批量创建图片失败: "+err.Error())
	}

	// 返回结果
	return c.Success(ctx, response)
}

// UpdatePictureStatus 更新图片状态
func (h *PictureController) UpdatePictureStatus(c *fiber.Ctx) error {
	// 获取图片ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	// 解析请求参数
	var req pictureValidator.UpdatePictureStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}
	// 更新图片
	err = h.pictureService.UpdateStatus(c.Context(), id, req.Status)
	if err != nil {
		return h.InternalServerError(c, "更新图片状态失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新图片状态成功")
}

// BatchUpdatePictureStatus 批量更新图片状态
func (h *PictureController) BatchUpdatePictureStatus(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidator.BatchUpdatePictureStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 批量更新状态
	err := h.pictureService.BatchUpdateStatus(c.Context(), req.IDs, req.Status)
	if err != nil {
		return h.InternalServerError(c, "批量更新图片状态失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "批量更新图片状态成功")
}

// BatchDeletePicture 批量删除图片
func (h *PictureController) BatchDeletePicture(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidator.BatchDeletePictureRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 批量删除图片
	for _, id := range req.IDs {
		if err := h.pictureService.Delete(c.Context(), id); err != nil {
			return h.InternalServerError(c, "批量删除图片失败: "+err.Error())
		}
	}

	return h.SuccessWithMessage(c, "批量删除图片成功")
}
