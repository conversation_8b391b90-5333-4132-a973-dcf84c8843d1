# Hooks 系统文档

## 概述

Hooks 系统是一个分层的钩子管理框架，用于在 Controller、Service、Repository 各层级执行自定义逻辑。它提供了数据验证、重复检查、数据清洗、审计记录等常用功能，并支持自定义钩子扩展。

## 核心特性

- **分层管理**: 支持 Controller、Service、Repository 三层钩子
- **类型安全**: 基于泛型的类型安全钩子系统
- **优先级控制**: 支持钩子执行优先级设置
- **错误处理**: 钩子执行失败可立即返回错误
- **内置钩子**: 提供常用的数据处理钩子
- **自定义扩展**: 支持自定义钩子逻辑
- **构建器模式**: 提供链式调用的钩子配置方式

## 钩子类型

### Service 层钩子

```go
const (
    BeforeCreate HookType = "before_create"  // 创建前
    AfterCreate  HookType = "after_create"   // 创建后
    BeforeUpdate HookType = "before_update"  // 更新前
    AfterUpdate  HookType = "after_update"   // 更新后
    BeforeDelete HookType = "before_delete"  // 删除前
    AfterDelete  HookType = "after_delete"   // 删除后
    BeforeFind   HookType = "before_find"    // 查询前
    AfterFind    HookType = "after_find"     // 查询后
)
```

### Repository 层钩子

```go
const (
    BeforeDBCreate HookType = "before_db_create"  // 数据库创建前
    AfterDBCreate  HookType = "after_db_create"   // 数据库创建后
    BeforeDBUpdate HookType = "before_db_update"  // 数据库更新前
    AfterDBUpdate  HookType = "after_db_update"   // 数据库更新后
    BeforeDBDelete HookType = "before_db_delete"  // 数据库删除前
    AfterDBDelete  HookType = "after_db_delete"   // 数据库删除后
)
```

### Controller 层钩子

```go
const (
    BeforeRequest  HookType = "before_request"   // 请求处理前
    AfterRequest   HookType = "after_request"    // 请求处理后
    BeforeResponse HookType = "before_response"  // 响应发送前
    AfterResponse  HookType = "after_response"   // 响应发送后
)
```

## 内置钩子

### 1. 重复检查钩子 (DuplicateCheckHook)

用于检查数据库中是否存在重复记录。

```go
// 注册重复检查钩子
hookManager.RegisterDuplicateCheck(
    "categories",           // 表名
    []string{"Name"},       // 检查字段
    "分类名称已存在",        // 错误消息
)
```

### 2. 数据清洗钩子 (DataCleaningHook)

用于清洗和格式化数据。

```go
// 注册数据清洗钩子
hookManager.RegisterDataCleaning(
    []string{"Name", "Description"}, // 去除空格的字段
    []string{"Email"},               // 转换为小写的字段
    []string{"Code"},                // 转换为大写的字段
    map[string]interface{}{
        "Status": 1,                   // 默认值
    },
)
```

### 3. 数据验证钩子 (ValidationHook)

用于验证数据格式和内容。

```go
// 注册数据验证钩子
hookManager.RegisterValidation(map[string][]ValidationRule{
    "Name": {
        {Type: "required", Message: "名称不能为空"},
        {Type: "min_length", Value: 2, Message: "名称至少2个字符"},
        {Type: "max_length", Value: 50, Message: "名称不能超过50个字符"},
    },
    "Email": {
        {Type: "regex", Value: `^[^@]+@[^@]+\.[^@]+$`, Message: "邮箱格式不正确"},
    },
})
```

支持的验证规则：
- `required`: 必填验证
- `min_length`: 最小长度验证
- `max_length`: 最大长度验证
- `regex`: 正则表达式验证
- `custom`: 自定义验证函数

### 4. 审计钩子 (AuditHook)

用于记录操作审计日志。

```go
// 注册审计钩子
hookManager.RegisterAudit(
    "categories",  // 表名
    "user123",     // 用户ID
)
```

### 5. 时间戳钩子 (TimestampHook)

用于自动设置创建时间和更新时间。

```go
// 注册时间戳钩子
hookManager.RegisterTimestamp(
    "CreatedAt",  // 创建时间字段
    "UpdatedAt",  // 更新时间字段
)
```

## 使用方法

### 1. 基本使用

```go
package main

import (
    "context"
    "fmt"
    "your-project/internal/hooks"
    "gorm.io/gorm"
)

type Category struct {
    ID          string `json:"id" gorm:"primaryKey"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Status      int    `json:"status"`
    CreatedAt   string `json:"created_at"`
    UpdatedAt   string `json:"updated_at"`
}

type CategoryService struct {
    db          *gorm.DB
    hookManager *hooks.ServiceHookManager
}

func NewCategoryService(db *gorm.DB) *CategoryService {
    service := &CategoryService{
        db:          db,
        hookManager: hooks.NewServiceHookManager(db, "category"),
    }
    service.setupHooks()
    return service
}

func (s *CategoryService) setupHooks() {
    // 使用构建器模式设置钩子
    hooks.NewCreateHookBuilder(s.hookManager).
        WithDataCleaning(
            []string{"Name", "Description"}, // 去除空格
            []string{"Name"},                // 转小写
            []string{},                      // 转大写
            map[string]interface{}{"Status": 1}, // 默认值
        ).
        WithValidation(map[string][]hooks.ValidationRule{
            "Name": {
                {Type: "required", Message: "分类名称不能为空"},
                {Type: "min_length", Value: 2, Message: "分类名称至少2个字符"},
            },
        }).
        WithDuplicateCheck("categories", []string{"Name"}, "分类名称已存在").
        WithTimestamp("CreatedAt", "UpdatedAt").
        WithAudit("categories", "system").
        Build()
}

func (s *CategoryService) Create(ctx context.Context, category *Category) error {
    // 执行创建前钩子
    if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, category); err != nil {
        return fmt.Errorf("创建前钩子执行失败: %w", err)
    }

    // 执行数据库创建
    if err := s.db.WithContext(ctx).Create(category).Error; err != nil {
        return fmt.Errorf("创建分类失败: %w", err)
    }

    // 执行创建后钩子
    if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterCreate, category); err != nil {
        // 创建后钩子失败不影响主操作，只记录日志
        fmt.Printf("创建后钩子执行失败: %v\n", err)
    }

    return nil
}
```

### 2. 自定义钩子

```go
// 注册自定义钩子
hookManager.RegisterCustomHook(
    hooks.BeforeCreate,
    "category_name_format",
    "格式化分类名称",
    12, // 优先级
    func(ctx context.Context, data interface{}) error {
        if category, ok := data.(*Category); ok {
            // 自定义处理逻辑
            category.Name = strings.TrimSpace(category.Name)
            if category.Name == "" {
                return errors.New("分类名称不能为空")
            }
        }
        return nil
    },
)
```

### 3. 集成到现有 BaseService

如果你已经有现有的 BaseService，可以这样集成：

```go
// 在你的 BaseService 中添加钩子管理器
type BaseService[T any] struct {
    repo        Repository[T]
    hookManager *hooks.ServiceHookManager
}

// 修改 Create 方法
func (s *BaseService[T]) Create(ctx context.Context, entity *T) (string, error) {
    // 执行创建前钩子
    if s.hookManager != nil {
        if err := s.hookManager.ExecuteHooks(ctx, hooks.BeforeCreate, entity); err != nil {
            return "", fmt.Errorf("创建前钩子执行失败: %w", err)
        }
    }

    // 原有的创建逻辑
    s.SetEntityDefaults(entity)
    err := s.repo.Create(ctx, entity)
    if err != nil {
        return "", fmt.Errorf("创建失败: %w", err)
    }

    // 执行创建后钩子
    if s.hookManager != nil {
        if err := s.hookManager.ExecuteHooks(ctx, hooks.AfterCreate, entity); err != nil {
            // 创建后钩子失败不影响主操作
            fmt.Printf("创建后钩子执行失败: %v\n", err)
        }
    }

    return s.GetEntityID(entity), nil
}
```

## 最佳实践

### 1. 钩子优先级设置

建议的优先级范围：
- 1-10: 数据清洗和格式化
- 11-20: 数据验证
- 21-50: 业务逻辑处理
- 51-100: 审计和日志记录

### 2. 错误处理

- **Before 钩子**: 执行失败应该阻止主操作
- **After 钩子**: 执行失败不应该影响主操作，只记录日志

### 3. 钩子设计原则

- **单一职责**: 每个钩子只负责一个特定功能
- **无副作用**: Before 钩子应该是幂等的
- **性能考虑**: 避免在钩子中执行耗时操作
- **错误信息**: 提供清晰的错误信息

### 4. 测试建议

```go
func TestCategoryServiceCreate(t *testing.T) {
    // 设置测试数据库
    db := setupTestDB()
    service := NewCategoryService(db)

    // 测试正常创建
    category := &Category{
        Name:        "  测试分类  ", // 包含空格，测试数据清洗
        Description: "测试描述",
    }

    err := service.Create(context.Background(), category)
    assert.NoError(t, err)
    assert.Equal(t, "测试分类", category.Name) // 验证空格被去除
    assert.Equal(t, 1, category.Status)       // 验证默认值设置

    // 测试重复创建
    duplicateCategory := &Category{
        Name:        "测试分类",
        Description: "重复的分类",
    }

    err = service.Create(context.Background(), duplicateCategory)
    assert.Error(t, err)
    assert.Contains(t, err.Error(), "分类名称已存在")
}
```

## 性能考虑

1. **钩子数量**: 避免注册过多钩子，影响性能
2. **数据库查询**: 重复检查钩子会执行数据库查询，注意优化
3. **内存使用**: 钩子管理器使用读写锁，注意并发性能
4. **错误处理**: Before 钩子失败会阻止操作，设计时要考虑性能影响

## 扩展功能

### 1. 条件钩子

可以扩展钩子系统支持条件执行：

```go
type ConditionalHook struct {
    Condition func(ctx context.Context, data interface{}) bool
    Hook      hooks.HookFunc
}
```

### 2. 异步钩子

对于不影响主流程的钩子，可以异步执行：

```go
type AsyncHook struct {
    Hook hooks.HookFunc
}

func (h *AsyncHook) Execute(ctx context.Context, data interface{}) error {
    go func() {
        h.Hook(ctx, data)
    }()
    return nil
}
```

### 3. 钩子链

支持钩子之间的依赖关系：

```go
type HookChain struct {
    Hooks []hooks.HookFunc
}
```

## 故障排除

### 常见问题

1. **钩子不执行**: 检查钩子是否正确注册，实体类型是否匹配
2. **重复检查失败**: 检查表名和字段名是否正确
3. **验证规则不生效**: 检查字段名是否与结构体字段名匹配
4. **性能问题**: 检查钩子数量和数据库查询优化

### 调试技巧

1. 启用钩子执行日志
2. 使用单元测试验证钩子逻辑
3. 检查钩子执行顺序和优先级
4. 使用性能分析工具检查耗时

## 总结

Hooks 系统提供了一个灵活、可扩展的方式来处理数据操作前后的逻辑。通过合理使用内置钩子和自定义钩子，可以大大简化业务代码，提高代码的可维护性和可测试性。