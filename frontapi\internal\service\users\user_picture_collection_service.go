package users

import (
	"frontapi/internal/models/users"
	"frontapi/internal/repository/pictures"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
)

// CreatePictureCollectionRequest 创建图片收藏请求
type CreatePictureCollectionRequest struct {
	UserID      string `json:"userId" validate:"required"`
	PictureID   string `json:"pictureId" validate:"required"`
	Remark      string `json:"remark"`
	Title       string `json:"title"`
	ThumbUrl    string `json:"thumbUrl"`
	OriginalUrl string `json:"originalUrl"`
	Author      string `json:"author"`
}

// UserPictureCollectionService 用户图片收藏服务接口
type UserPictureCollectionService interface {
	base.IExtendedService[users.UserPictureCollection]
}

// userPictureCollectionService 用户图片收藏服务实现
type userPictureCollectionService struct {
	*base.ExtendedService[users.UserPictureCollection]
	pictureCollectionRepo repo.UserPictureCollectionRepository
	pictureRepo           pictures.PictureRepository
	userRepo              repo.UserRepository
}

// NewUserPictureCollectionService 创建用户图片收藏服务实例
func NewUserPictureCollectionService(
	pictureCollectionRepo repo.UserPictureCollectionRepository,
	pictureRepo pictures.PictureRepository,
	userRepo repo.UserRepository,
) UserPictureCollectionService {
	return &userPictureCollectionService{
		pictureCollectionRepo: pictureCollectionRepo,
		pictureRepo:           pictureRepo,
		userRepo:              userRepo,
	}
}
