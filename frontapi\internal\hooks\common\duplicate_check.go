package common

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"

	"gorm.io/gorm"
)

// DuplicateCheckHook 重复检查钩子
type DuplicateCheckHook struct {
	DB        *gorm.DB
	TableName string
	Fields    []string // 需要检查重复的字段
	Message   string   // 自定义错误消息
}

// Execute 执行重复检查
func (h *DuplicateCheckHook) Execute(ctx context.Context, data interface{}) error {
	if h.DB == nil {
		return errors.New("数据库连接不能为空")
	}

	if len(h.Fields) == 0 {
		return errors.New("检查字段不能为空")
	}

	// 使用反射获取字段值
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	if v.Kind() != reflect.Struct {
		return errors.New("数据必须是结构体类型")
	}

	// 构建查询条件
	query := h.DB.WithContext(ctx).Table(h.TableName)
	for _, field := range h.Fields {
		fieldValue := v.FieldByName(field)
		if !fieldValue.IsValid() {
			return fmt.Errorf("字段 %s 不存在", field)
		}

		// 转换字段名为数据库列名（snake_case）
		columnName := toSnakeCase(field)
		query = query.Where(fmt.Sprintf("%s = ?", columnName), fieldValue.Interface())
	}

	// 如果是更新操作，排除当前记录
	if idField := v.FieldByName("ID"); idField.IsValid() && !idField.IsZero() {
		query = query.Where("id != ?", idField.Interface())
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("检查重复数据失败: %w", err)
	}

	if count > 0 {
		if h.Message != "" {
			return errors.New(h.Message)
		}
		return fmt.Errorf("字段 %s 的值已存在", strings.Join(h.Fields, ", "))
	}

	return nil
}

// NewDuplicateCheckHook 创建重复检查钩子
func NewDuplicateCheckHook(db *gorm.DB, tableName string, fields []string, message string) *DuplicateCheckHook {
	return &DuplicateCheckHook{
		DB:        db,
		TableName: tableName,
		Fields:    fields,
		Message:   message,
	}
}

// toSnakeCase 将驼峰命名转换为蛇形命名
func toSnakeCase(str string) string {
	var result []rune
	for i, r := range str {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result = append(result, '_')
		}
		result = append(result, r)
	}
	return strings.ToLower(string(result))
}
