package controller

import (
	"sync"

	"github.com/gofiber/fiber/v2"
)

// ===== 响应构建器部分 =====

// 使用对象池模式减少GC压力
var responseBuilderPool = sync.Pool{
	New: func() interface{} {
		return &ResponseBuilder{
			code: 2000, // 默认成功状态码
		}
	},
}

// ResponseBuilder 响应构建器
// 使用建造者模式构建HTTP响应
type ResponseBuilder struct {
	code    int
	message string
	data    interface{}
}

// NewResponseBuilder 从对象池获取响应构建器
func NewResponseBuilder() *ResponseBuilder {
	return responseBuilderPool.Get().(*ResponseBuilder)
}

// Release 释放响应构建器回对象池
func (b *ResponseBuilder) Release() {
	b.code = 2000
	b.message = ""
	b.data = nil
	responseBuilderPool.Put(b)
}

// WithCode 设置响应码
func (b *ResponseBuilder) WithCode(code int) *ResponseBuilder {
	b.code = code
	return b
}

// WithMessage 设置响应消息
func (b *ResponseBuilder) WithMessage(message string) *ResponseBuilder {
	b.message = message
	return b
}

// WithData 设置响应数据
func (b *ResponseBuilder) WithData(data interface{}) *ResponseBuilder {
	b.data = data
	return b
}

// Build 构建响应
func (b *ResponseBuilder) Build() fiber.Map {
	return fiber.Map{
		"code":    b.code,
		"message": b.message,
		"data":    b.data,
	}
}

// Success 构建成功响应
func (b *ResponseBuilder) Success(ctx *fiber.Ctx) error {
	if b.message == "" {
		b.message = "请求成功"
	}
	defer b.Release() // 使用完毕后释放回对象池
	return ctx.Status(fiber.StatusOK).JSON(b.Build())
}

// Error 构建错误响应
func (b *ResponseBuilder) Error(ctx *fiber.Ctx, statusCode int) error {
	if b.message == "" {
		b.message = "请求失败"
	}
	defer b.Release() // 使用完毕后释放回对象池
	return ctx.Status(statusCode).JSON(b.Build())
}
