package videos

import (
	"fmt"
	"frontapi/internal/admin"
	videoModel "frontapi/internal/models/videos"
	videoSrv "frontapi/internal/service/videos"
	videoValidator "frontapi/internal/validation/videos"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
	"github.com/guregu/null/v6"
)

type VideoCategoryController struct {
	admin.BaseController
	videoService    videoSrv.VideoService
	categoryService videoSrv.VideoCategoryService
}

func NewVideoCategoryController(
	videoService videoSrv.VideoService,
	categoryService videoSrv.VideoCategoryService,
) *VideoCategoryController {
	return &VideoCategoryController{
		videoService:    videoService,
		categoryService: categoryService,
	}
}

// CreateVideoCategory ==============================
// 视频分类相关方法
// ==============================
func (h *VideoCategoryController) CreateVideoCategory(c *fiber.Ctx) error {
	// 使用验证器验证请求数据
	var req videoValidator.CreateCategoryRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err
	}
	reqInfo := h.GetRequestInfo(c)
	err := reqInfo.ParseObject(&req)
	if err != nil {
		return h.BadRequest(c, "请求参数错误", nil)
	}

	// 创建分类对象并使用SmartCopy进行智能拷贝
	category := videoModel.VideoCategory{}

	// 使用SmartCopy进行基础字段拷贝
	if err := utils.SmartCopy(&req, &category); err != nil {
		return h.InternalServerError(c, "数据拷贝失败: "+err.Error())
	}

	// 处理需要调用特殊方法的字段
	category.SetName(req.Name)
	category.SetCode(req.Code)
	category.SetDescription(req.Description)
	category.SetStatus(int8(req.Status))

	// 处理父分类ID：只有当ParentID不为空且不为空字符串时才设置
	if req.ParentID != nil && *req.ParentID != "" {
		// 验证父分类是否存在
		parentCategory, err := h.categoryService.GetByID(c.Context(), *req.ParentID, false)
		if err != nil || parentCategory == nil {
			return h.BadRequest(c, "指定的父分类不存在", nil)
		}
		category.ParentID = null.StringFrom(*req.ParentID)
	} else {
		category.ParentID = null.String{} // 明确设置为nil，表示顶级分类
	}

	_, err = h.categoryService.Create(c.Context(), &category)
	if err != nil {
		return h.InternalServerError(c, fmt.Sprintf("创建分类失败: %v", err))
	}

	return h.SuccessWithMessage(c, "创建分类成功")
}

func (h *VideoCategoryController) UpdateVideoCategory(c *fiber.Ctx) error {
	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "分类ID不能为空", nil)
	}
	// 使用验证器验证请求数据
	var req videoValidator.UpdateCategoryRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err
	}

	// 创建分类对象并使用SmartCopy进行智能拷贝
	category, err := h.categoryService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.InternalServerError(c, "获取分类失败: "+err.Error())
	}

	// 使用SmartCopy进行基础字段拷贝
	if err := utils.SmartCopy(req, category); err != nil {
		return h.InternalServerError(c, "数据拷贝失败: "+err.Error())
	}

	// 处理需要调用特殊方法的字段
	category.SetName(req.Name)
	// category.SetCode(req.Code)
	category.SetDescription(req.Description)
	category.SetStatus(int8(req.Status))

	// 处理父分类ID：只有当ParentID不为空且不为空字符串时才设置
	if req.ParentID != nil && *req.ParentID != "" {
		// 防止设置自己为父分类
		if *req.ParentID == id {
			return h.BadRequest(c, "不能将自己设置为父分类", nil)
		}
		// 验证父分类是否存在
		parentCategory, err := h.categoryService.GetByID(c.Context(), *req.ParentID, false)
		if err != nil || parentCategory == nil {
			return h.BadRequest(c, "指定的父分类不存在", nil)
		}
		category.ParentID = null.StringFrom(*req.ParentID)
	} else {
		category.ParentID = null.String{} // 明确设置为nil，表示顶级分类
	}

	err = h.categoryService.UpdateById(c.Context(), id, category)
	if err != nil {
		return h.InternalServerError(c, fmt.Sprintf("更新分类失败: %v", err))
	}

	return h.SuccessWithMessage(c, "更新分类成功")
}
func (h *VideoCategoryController) DeleteVideoCategory(c *fiber.Ctx) error {
	// 获取ID参数
	id, _ := h.GetId(c)
	if id == "" {
		return h.BadRequest(c, "分类ID不能为空", nil)
	}
	err := h.categoryService.Delete(c.Context(), id)
	if err != nil {
		return h.InternalServerError(c, "删除分类失败: "+err.Error())
	}
	return h.SuccessWithMessage(c, "删除分类成功")
}

// GetCategory 获取视频分类详情
func (h *VideoCategoryController) GetCategory(c *fiber.Ctx) error {
	id, _ := h.GetId(c)
	category, err := h.categoryService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "分类不存在")
	}

	return h.Success(c, category)
}

// ListCategories 获取视频分类列表
func (h *VideoCategoryController) ListCategories(c *fiber.Ctx) error {
	reqInfo := h.GetRequestInfo(c)
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize
	keyword := reqInfo.Get("keyword").GetString()
	status := -9999
	println(keyword)
	err := h.GetIntegerValueWithDataWrapper(c, "status", &status)
	if err != nil {
		return err
	}
	condition := map[string]interface{}{
		"keyword": keyword,
		"status":  status,
	}
	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	categories, total, err := h.categoryService.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取分类列表失败")
	}

	return h.SuccessList(c, categories, total, page, pageSize)
}

func (h *VideoCategoryController) UpdateVideoCategoryStatus(c *fiber.Ctx) error {
	// 从URL路径获取ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}
	var req videoValidator.UpdateCategoryStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数", nil)
	}
	err = h.categoryService.UpdateStatus(c.Context(), id, req.Status)
	if err != nil {
		return h.InternalServerError(c, "更新分类状态失败: "+err.Error())
	}
	return h.SuccessWithMessage(c, "更新分类状态成功")
}

func (h *VideoCategoryController) BatchUpdateVideoCategoryStatus(c *fiber.Ctx) error {
	var req videoValidator.BatchUpdateCategoryStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return err
	}
	err := h.categoryService.BatchUpdateStatus(c.Context(), req.Ids, req.Status)
	if err != nil {
		return h.InternalServerError(c, "批量更新分类状态失败")
	}
	return h.SuccessWithMessage(c, "批量更新分类状态成功")
}
