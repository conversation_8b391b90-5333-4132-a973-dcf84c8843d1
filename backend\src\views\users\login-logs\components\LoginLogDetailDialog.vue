<template>
  <el-dialog
    :model-value="visible"
    title="登录日志详情"
    width="800px"
    :before-close="handleClose"
    @update:model-value="updateVisible"
  >
    <div v-if="loginLogData" class="login-log-detail">
      <div class="detail-grid">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">基本信息</h4>
          <div class="detail-row">
            <span class="label">日志ID:</span>
            <span class="value">{{ loginLogData.id }}</span>
          </div>
          <div class="detail-row">
            <span class="label">用户ID:</span>
            <span class="value">{{ loginLogData.user_id }}</span>
          </div>
          <div class="detail-row">
            <span class="label">用户名:</span>
            <span class="value">{{ loginLogData.username || '未知' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">登录类型:</span>
            <span class="value">
              <el-tag v-if="loginLogData.type === 0" type="success">登录</el-tag>
              <el-tag v-else-if="loginLogData.type === 1" type="info">退出</el-tag>
              <el-tag v-else type="warning">未知</el-tag>
            </span>
          </div>
          <div class="detail-row">
            <span class="label">认证方式:</span>
            <span class="value">{{ loginLogData.login_type || '未知' }}</span>
          </div>
          <div class="detail-row">
            <span class="label">登录时间:</span>
            <span class="value">{{ loginLogData.login_time }}</span>
          </div>
          <div class="detail-row">
            <span class="label">登录状态:</span>
            <span class="value">
              <el-tag v-if="loginLogData.login_status === 'success'" type="success">成功</el-tag>
              <el-tag v-else-if="loginLogData.login_status === 'failure'" type="danger">失败</el-tag>
              <el-tag v-else type="info">未知</el-tag>
            </span>
          </div>
        </div>

        <!-- 网络信息 -->
        <div class="detail-section">
          <h4 class="section-title">网络信息</h4>
          <div class="detail-row">
            <span class="label">IP地址:</span>
            <span class="value">{{ loginLogData.ip_address }}</span>
          </div>
          <div class="detail-row">
            <span class="label">地理位置:</span>
            <span class="value">{{ loginLogData.location || '未知' }}</span>
          </div>
        </div>

        <!-- 设备信息 -->
        <div class="detail-section">
          <h4 class="section-title">设备信息</h4>
          <div class="detail-row">
            <span class="label">设备类型:</span>
            <span class="value">{{ loginLogData.device_type }}</span>
          </div>
          <div class="detail-row">
            <span class="label">操作系统:</span>
            <span class="value">{{ loginLogData.os_name }} {{ loginLogData.os_version }}</span>
          </div>
          <div class="detail-row">
            <span class="label">浏览器:</span>
            <span class="value">{{ loginLogData.browser_name }} {{ loginLogData.browser_version }}</span>
          </div>
        </div>
      </div>

      <!-- 失败原因 -->
      <div v-if="loginLogData.failure_reason" class="failure-section mt-4">
        <h4 class="section-title">失败原因</h4>
        <div class="failure-content">
          {{ loginLogData.failure_reason }}
        </div>
      </div>

      <!-- User-Agent信息 -->
      <div class="user-agent-section mt-4">
        <h4 class="section-title">User-Agent</h4>
        <div class="user-agent-content">
          {{ loginLogData.user_agent }}
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { UserLoginLog } from '@/types/users';

// Props类型定义
interface Props {
  visible: boolean;
  loginLogData: UserLoginLog | null;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loginLogData: null
});

// Emits类型定义
interface Emits {
  'update:visible': [value: boolean];
}

const emit = defineEmits<Emits>();

// 更新可见性
const updateVisible = (value: boolean) => {
  emit('update:visible', value);
};

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false);
};
</script>

<style scoped>
.login-log-detail {
  padding: 10px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.detail-row {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
  margin-right: 12px;
  font-size: 14px;
}

.value {
  color: #303133;
  font-size: 14px;
  flex: 1;
  word-break: break-word;
}

.failure-section,
.user-agent-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.failure-content,
.user-agent-content {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}

.failure-content {
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .detail-section {
    padding: 12px;
  }
  
  .section-title {
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .label {
    min-width: 70px;
    font-size: 13px;
  }
  
  .value {
    font-size: 13px;
  }
  
  .failure-content,
  .user-agent-content {
    padding: 8px;
    font-size: 13px;
  }
}
</style> 