package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserBookCollectionRepository 用户书籍收藏数据访问接口
type UserBookCollectionRepository interface {
	base.ExtendedRepository[users.UserBookCollection]
}

// userBookCollectionRepository 用户书籍收藏数据访问实现
type userBookCollectionRepository struct {
	base.ExtendedRepository[users.UserBookCollection]
}

// NewUserBookCollectionRepository 创建用户书籍收藏仓库实例
func NewUserBookCollectionRepository(db *gorm.DB) UserBookCollectionRepository {
	return &userBookCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserBookCollection](db),
	}
}
