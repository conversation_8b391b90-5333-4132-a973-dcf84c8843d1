<template>
  <el-dialog
    title="漫画详情"
    v-model="dialogVisible"
    width="700px"
    destroy-on-close
  >
    <el-descriptions
      :column="1"
      border
      class="comics-details"
    >
      <el-descriptions-item label="封面">
        <el-image 
          v-if="comics.cover"
          :src="comics.cover" 
          :preview-src-list="[comics.cover]"
          fit="cover"
          class="cover-image"
        />
        <el-icon v-else><Picture /></el-icon>
      </el-descriptions-item>
      
      <el-descriptions-item label="标题">
        {{ comics.title }}
      </el-descriptions-item>
      
      <el-descriptions-item label="作者">
        {{ comics.author }}
      </el-descriptions-item>
      
      <el-descriptions-item label="分类">
        <el-tag>{{ comics.category_name || '-' }}</el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="标签">
        <div class="tags-container">
          <el-tag 
            v-for="tag in comics.tags" 
            :key="tag" 
            size="small"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
          <span v-if="!comics.tags || comics.tags.length === 0">-</span>
        </div>
      </el-descriptions-item>
      
      <el-descriptions-item label="描述">
        <div class="description-content">
          {{ comics.description || '-' }}
        </div>
      </el-descriptions-item>
      
      <el-descriptions-item label="免费/付费">
        <el-tag :type="comics.is_paid === 0 ? 'success' : 'danger'">
          {{ comics.is_paid === 0 ? '免费' : '付费' }}
        </el-tag>
        <span v-if="comics.is_paid !== 0" class="ml-2">
          价格: ¥{{ comics.price }}
        </span>
      </el-descriptions-item>
      
      <el-descriptions-item label="推荐状态">
        <el-tag :type="comics.is_featured === 1 ? 'warning' : 'info'">
          {{ comics.is_featured === 1 ? '推荐' : '普通' }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="状态">
        <el-tag :type="comics.progress === 'ongoing' ? 'success' : 'danger'">
          {{ comics.progress === 'ongoing' ? '连载中' : '已完结' }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="章节数">
        {{ comics.chapter_count || 0 }}
      </el-descriptions-item>
      
      <el-descriptions-item label="统计信息">
        <div class="stats-container">
          <div class="stat-item">
            <el-icon><View /></el-icon>
            <span>{{ formatNumber(comics.read_count) }} 浏览</span>
          </div>
          <div class="stat-item">
            <el-icon><Star /></el-icon>
            <span>{{ formatNumber(comics.favorite_count) }} 收藏</span>
          </div>
          <div class="stat-item">
            <el-icon><Pointer /></el-icon>
            <span>{{ formatNumber(comics.like_count) }} 点赞</span>
          </div>
        </div>
      </el-descriptions-item>
      
      <el-descriptions-item label="创建时间">
        {{ formatDate(comics.created_at) }}
      </el-descriptions-item>
      
      <el-descriptions-item label="更新时间">
        {{ formatDate(comics.updated_at) }}
      </el-descriptions-item>
    </el-descriptions>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Picture, View, Star, Pointer } from '@element-plus/icons-vue';
import { ComicsItem } from '@/types/comics';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  comics: {
    type: Object as () => ComicsItem,
    default: () => ({} as ComicsItem)
  }
});

const emit = defineEmits(['update:visible']);

// 对话框是否可见
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 格式化数字
const formatNumber = (num: number) => {
  if (!num && num !== 0) return '-';
  return num >= 10000 ? (num / 10000).toFixed(1) + '万' : num;
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-';
  const date = new Date(dateStr);
  return date.toLocaleDateString() + ' ' + date.toTimeString().slice(0, 8);
};
</script>

<style scoped>
.comics-details {
  margin-bottom: 20px;
}

.cover-image {
  width: 150px;
  height: 200px;
  border-radius: 4px;
  object-fit: cover;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag-item {
  margin-right: 5px;
  margin-bottom: 5px;
}

.description-content {
  white-space: pre-wrap;
  max-height: 150px;
  overflow-y: auto;
}

.stats-container {
  display: flex;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.ml-2 {
  margin-left: 8px;
}
</style> 