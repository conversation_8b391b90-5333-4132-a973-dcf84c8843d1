package tags

import (
	"frontapi/internal/models/system"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

// system.Tag 标签数据库模型

// TagRepository 标签数据访问接口
type TagRepository interface {
	base.ExtendedRepository[system.Tag]
}

// tagRepository 标签数据访问实现
type tagRepository struct {
	base.ExtendedRepository[system.Tag]
}

// NewTagRepository 创建标签数据访问实例
func NewTagRepository(db *gorm.DB) TagRepository {
	// 自动迁移表结构
	//_ = db.AutoMigrate(&system.Tag{})

	return &tagRepository{
		ExtendedRepository: base.NewExtendedRepository[system.Tag](db),
	}
}
