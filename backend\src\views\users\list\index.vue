<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="filter-container">
          <div class="title-container">
            <h2>用户管理</h2>
            <div class="buttons">
              <el-button type="primary" :icon="Plus" @click="handleAdd">添加用户</el-button>
              <el-button type="success" :icon="Refresh" @click="refreshList">刷新</el-button>
              <el-button type="info" :icon="Download" @click="handleExport">导出</el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 使用用户搜索组件 -->
      <UserSearchBar
        v-model="searchForm"
        @search="handleSearch"
        @reset="handleReset"
        @refresh="refreshList"
        class="mb-4"
      />

      <!-- 使用用户表格组件 -->
      <UserTable
        :user-list="userList"
        :loading="loading"
        :pagination="pagination"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @edit="handleEdit"
        @delete="handleDelete"
        @change-status="handleChangeStatus"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @add="handleAdd"
        @refresh="refreshList"
        @export="handleExport"
        @batch-status="handleBatchStatus"
        @batch-delete="handleBatchDelete"
      />

      <!-- 用户表单对话框 -->
      <UserFormDialog
        :visible="dialogVisible"
        :type="dialogType"
        :user-data="currentUser"
        @update:visible="(val: boolean) => dialogVisible = val"
        @success="handleDialogSuccess"
      />

      <!-- 用户详情对话框 -->
      <UserDetailDialog
        :visible="detailDialogVisible"
        :user-data="currentUser"
        @update:visible="(val: boolean) => detailDialogVisible = val"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { batchDeleteUser, batchUpdateUserStatus, deleteUser, getUserList, updateUserStatus } from '@/service/api/users/users';
import type { UserItem } from '@/types/users';
import { handleApiError } from '@/utils/errorHandler';
import { Download, Plus, Refresh } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

// 导入用户管理组件
import UserDetailDialog from './components/UserDetailDialog.vue';
import UserFormDialog from './components/UserFormDialog.vue';
import UserSearchBar from './components/UserSearchBar.vue';
import UserTable from './components/UserTable.vue';

// 响应式数据
const loading = ref(false);
const userList = ref<UserItem[]>([]);
const selectedRows = ref<UserItem[]>([]);
const dialogVisible = ref(false);
const detailDialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');
const currentUser = ref<UserItem | null>(null);

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as number | undefined,
  user_type: undefined as number | undefined,
  is_content_creator: undefined as number | undefined,
  reg_time_start: '',
  reg_time_end: ''
});

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});



// 获取用户列表
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      data: {
        keyword: searchForm.keyword,
        status: searchForm.status,
        user_type: searchForm.user_type,
        is_content_creator: searchForm.is_content_creator,
        reg_time_start: searchForm.reg_time_start,
        reg_time_end: searchForm.reg_time_end
      },
      page: {
        pageNo: pagination.page,
        pageSize: pagination.pageSize
      }
    };

    const {response,data} = await getUserList(params) as any;
    console.log(response,data);
    if (response.status === 200&&response.data.code===2000) {
      userList.value = data.list || [];
      pagination.total = data.total || 0;
    } else {
      handleApiError(response.message || '获取用户列表失败');
    }
  } catch (error) {
    handleApiError(error, '获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (searchParams?: any) => {
  if (searchParams) {
    // 如果有传递搜索参数，更新表单数据
    Object.assign(searchForm, searchParams);
  }
  pagination.page = 1;
  getList();
};

// 重置搜索
const handleReset = () => {
  // 重置表单数据
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    user_type: undefined,
    is_content_creator: undefined,
    reg_time_start: '',
    reg_time_end: ''
  });
  pagination.page = 1;
  getList();
};

// 刷新列表
const refreshList = () => {
  getList();
};

// 表格选择变化
const handleSelectionChange = (selection: UserItem[]) => {
  selectedRows.value = selection;
};

// 分页处理
const handleCurrentChange = (page: number) => {
  pagination.page = page;
  getList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getList();
};

// 添加用户
const handleAdd = () => {
  dialogType.value = 'add';
  currentUser.value = null;
  dialogVisible.value = true;
};

// 查看用户
const handleView = (row: UserItem) => {
  currentUser.value = row;
  detailDialogVisible.value = true;
};

// 编辑用户
const handleEdit = (row: UserItem) => {
  dialogType.value = 'edit';
  currentUser.value = { ...row };
  dialogVisible.value = true;
};

// 删除用户
const handleDelete = async (row: UserItem) => {
  try {
    const {response} = await deleteUser(row.id) as any;
    
    if (response.status === 200&&response.data.code===2000) {
      ElMessage.success('删除用户成功');
      getList();
    } else {
      handleApiError(response.message || '删除用户失败');
    }
  } catch (error) {
    handleApiError(error, '删除用户失败');
  }
};

// 更改用户状态
const handleChangeStatus = async (id: string, status: number) => {
  try {
    const params = { id, status };
    const {response,data} = await updateUserStatus(params) as any;
    
    if (response.status === 200&&response.data.code===2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}用户成功`);
      getList();
    } else {
      handleApiError(response.message || '更新用户状态失败');
    }
  } catch (error) {
    handleApiError(error, '更新用户状态失败');
  }
};

// 导出用户数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

// 批量更新用户状态
const handleBatchStatus = async (status: number, users: UserItem[]) => {
  try {
    const params = { ids: users.map(user => user.id), status };
    const {response,data} = await batchUpdateUserStatus(params) as any;
    
    if (response.status === 200&&response.data.code===2000) {
      ElMessage.success(`${status === 1 ? '启用' : '禁用'}用户成功`);
      getList();
    } else {
      handleApiError(response.message || '更新用户状态失败');
    }
  } catch (error) {
    handleApiError(error, '更新用户状态失败');
  }
};

// 批量删除用户
const handleBatchDelete = async (users: UserItem[]) => {
  try {
    const params = { ids: users.map(user => user.id) };
    const {response,data} = await batchDeleteUser(params) as any;
    if (response.status === 200&&response.data.code===2000) {
    ElMessage.success('批量删除用户成功');
    getList();  
    selectedRows.value = [];
    } else {
      handleApiError(response.message || '批量删除用户失败');
    }
  } catch (error) {
    handleApiError(error, '批量删除用户失败');
  }
};

// 对话框操作成功
const handleDialogSuccess = () => {
  getList();
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.title-container h2 {
  margin: 0;
  color: #303133;
  font-weight: 600;
}

.buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .title-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .buttons {
    justify-content: center;
  }
}
</style>

