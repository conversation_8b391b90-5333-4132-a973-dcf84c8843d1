<template>
  <div class="premium-country-selector">
    <!-- 触发器按钮 -->
    <div 
      ref="triggerRef"
      class="selector-trigger"
      :class="{ 
        'has-selections': selectedCountries.length > 0, 
        'is-open': isVisible,
        'compact': compact,
        'hide-background': hideBackground,
        [customButtonClass || '']: customButtonClass
      }"
      :style="customButtonStyle"
      @click="toggleSelector"
    >
      <!-- 已选择的国家 -->
      <div v-if="selectedCountries.length > 0" class="selected-countries-display">
        <!-- 单选模式：只显示一个国家，无背景 -->
        <div v-if="!multiple" class="single-country-display">
          <span :class="`flag-icon ${selectedCountries[0].flag}`" class="country-flag"></span>
          <span class="country-name">{{ selectedCountries[0].name }}</span>
          <el-icon class="chevron-icon" :class="{ 'rotated': isVisible }"><ArrowDown /></el-icon>
        </div>
        
        <!-- 多选模式：显示选中的国家chips -->
        <div v-else class="selected-items">
          <div 
            v-for="(country, index) in displayCountries" 
            :key="country.code"
            class="selected-country-chip"
            :style="{ animationDelay: `${index * 50}ms` }"
          >
            <span :class="`flag-icon ${country.flag}`" class="country-flag"></span>
            <span class="country-name">{{ country.name }}</span>
            <button 
              class="remove-btn"
              @click.stop="removeCountry(country)"
              :aria-label="`移除 ${country.name}`"
            >
              <el-icon><Close /></el-icon>
            </button>
          </div>
          
          <!-- 显示更多指示器 -->
          <div v-if="selectedCountries.length > maxDisplayCount" class="more-indicator">
            +{{ selectedCountries.length - maxDisplayCount }}
          </div>
          
          <!-- 添加更多按钮 -->
          <button class="add-more-btn" @click.stop="toggleSelector">
            <el-icon><Plus /></el-icon>
          </button>
        </div>
      </div>
      
      <!-- 空状态按钮 -->
      <div v-else class="empty-state-trigger">
        <el-icon class="globe-icon"><Location /></el-icon>
        <span class="trigger-text">{{ multiple ? '选择地区' : '选择地区' }}</span>
        <el-icon class="chevron-icon" :class="{ 'rotated': isVisible }"><ArrowDown /></el-icon>
      </div>
    </div>

    <!-- 选择器面板 -->
    <teleport to="body">
      <transition name="selector-fade">
        <div 
          v-if="isVisible"
          ref="panelRef"
          class="selector-panel"
          :style="panelStyle"
          @click.stop
        >
          <!-- 面板头部 -->
          <div class="panel-header">
            <div class="header-title">
              <el-icon class="title-icon"><Location /></el-icon>
              <h3>{{ multiple ? '选择地区' : '选择地区' }}</h3>
              <span class="mode-badge">{{ multiple ? '多选' : '单选' }}</span>
            </div>
            <button class="close-btn" @click="closeSelector">
              <el-icon><Close /></el-icon>
            </button>
          </div>

          <!-- 搜索栏 -->
          <div class="search-section">
            <div class="search-input-wrapper">
              <el-icon class="search-icon"><Search /></el-icon>
              <input 
                v-model="searchQuery"
                type="text"
                placeholder="搜索国家或地区..."
                class="search-input"
                @keydown.esc="closeSelector"
              />
              <button 
                v-if="searchQuery"
                class="clear-search-btn"
                @click="searchQuery = ''"
              >
                <el-icon><Close /></el-icon>
              </button>
            </div>
          </div>

          <!-- 主要内容区域 -->
          <div class="main-content">
            <!-- 左侧大洲标签 -->
            <div class="continent-sidebar">
              <div class="continent-title">大洲</div>
              <div class="continent-list">
                <button
                  v-for="continent in continents"
                  :key="continent.key"
                  class="continent-item"
                  :class="{ 'active': activeContinent === continent.key }"
                  @click="activeContinent = continent.key"
                >
                  <span class="continent-icon">{{ continent.icon }}</span>
                  <div class="continent-info">
                    <span class="continent-name">{{ continent.name }}</span>
                    <span class="country-count">{{ getContinentCountryCount(continent.key) }}个</span>
                  </div>
                </button>
              </div>
            </div>

            <!-- 右侧国家列表 -->
            <div class="countries-content">
              <!-- 快速选择（仅在无搜索时显示） -->
              <div v-if="!searchQuery && multiple" class="quick-select-section">
                <div class="section-title">
                  <el-icon><Star /></el-icon>
                  热门地区
                </div>
                <div class="quick-select-grid">
                  <button
                    v-for="country in popularCountries"
                    :key="country.code"
                    class="quick-select-item"
                    :class="{ 'selected': isSelected(country) }"
                    @click="toggleCountry(country)"
                  >
                    <span :class="`flag-icon ${country.flag}`" class="country-flag"></span>
                    <span class="country-name">{{ country.name }}</span>
                    <el-icon v-if="isSelected(country)" class="check-icon"><Check /></el-icon>
                  </button>
                </div>
              </div>

              <!-- 国家网格 -->
              <div class="countries-section">
                <div class="section-title">
                  <el-icon><Location /></el-icon>
                  {{ searchQuery ? '搜索结果' : continents.find(c => c.key === activeContinent)?.name }}
                  <span class="result-count">({{ filteredCountries.length }}个)</span>
                </div>
                
                <div v-if="filteredCountries.length === 0" class="empty-result">
                  <el-icon class="empty-icon"><Search /></el-icon>
                  <p>未找到匹配的国家</p>
                </div>
                
                <div v-else class="countries-grid">
                  <div 
                    v-for="country in filteredCountries" 
                    :key="country.code"
                    class="country-item"
                    :class="{ 'selected': isSelected(country) }"
                    @click="toggleCountry(country)"
                  >
                    <span :class="`flag-icon ${country.flag}`" class="country-flag"></span>
                    <span class="country-name">{{ country.name }}</span>
                    <transition name="check-fade">
                      <el-icon v-if="isSelected(country)" class="check-icon"><Check /></el-icon>
                    </transition>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 面板底部 -->
          <div class="panel-footer">
            <div class="selection-summary">
              已选择 <span class="count">{{ selectedCountries.length }}</span> 个地区
            </div>
            <div class="footer-actions">
              <button class="clear-all-btn" @click="clearAll" v-if="selectedCountries.length > 0">
                清空所有
              </button>
              <button class="confirm-btn" @click="confirmSelection">
                确认选择
              </button>
            </div>
          </div>
        </div>
      </transition>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import {
    ArrowDown,
    Check,
    Close,
    Location,
    Plus,
    Search,
    Star
} from '@element-plus/icons-vue'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

interface Country {
  name: string
  code: string
  flag: string
  continent: string
}

const props = defineProps<{
  modelValue?: Country[]
  maxDisplayCount?: number
  multiple?: boolean
  compact?: boolean
  customButtonClass?: string
  customButtonStyle?: Record<string, string>
  hideBackground?: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: Country[]]
  'change': [value: Country[]]
}>()

// 响应式数据
const triggerRef = ref<HTMLElement>()
const panelRef = ref<HTMLElement>()
const isVisible = ref(false)
const searchQuery = ref('')
const activeContinent = ref('asia')
const selectedCountries = ref<Country[]>(props.modelValue || [])
const maxDisplayCount = computed(() => props.maxDisplayCount || 3)
const multiple = computed(() => props.multiple !== false) // 默认多选
const justOpened = ref(false) // 防止立即关闭的标志

// 面板位置样式
const panelStyle = ref({})

// 大洲配置
const continents = [
  { key: 'asia', name: '亚洲', icon: '🌏' },
  { key: 'europe', name: '欧洲', icon: '🌍' },
  { key: 'namerica', name: '北美洲', icon: '🌎' },
  { key: 'samerica', name: '南美洲', icon: '🌎' },
  { key: 'africa', name: '非洲', icon: '🌍' },
  { key: 'oceania', name: '大洋洲', icon: '🌏' }
]

// 国家数据
const countries: Country[] = [
  // Asia
  { name: '中国', code: 'CN', flag: 'flag-icon-cn', continent: 'asia' },
  { name: '日本', code: 'JP', flag: 'flag-icon-jp', continent: 'asia' },
  { name: '韩国', code: 'KR', flag: 'flag-icon-kr', continent: 'asia' },
  { name: '印度', code: 'IN', flag: 'flag-icon-in', continent: 'asia' },
  { name: '印尼', code: 'ID', flag: 'flag-icon-id', continent: 'asia' },
  { name: '越南', code: 'VN', flag: 'flag-icon-vn', continent: 'asia' },
  { name: '泰国', code: 'TH', flag: 'flag-icon-th', continent: 'asia' },
  { name: '马来西亚', code: 'MY', flag: 'flag-icon-my', continent: 'asia' },
  { name: '新加坡', code: 'SG', flag: 'flag-icon-sg', continent: 'asia' },
  { name: '菲律宾', code: 'PH', flag: 'flag-icon-ph', continent: 'asia' },

  // Europe
  { name: '英国', code: 'GB', flag: 'flag-icon-gb', continent: 'europe' },
  { name: '法国', code: 'FR', flag: 'flag-icon-fr', continent: 'europe' },
  { name: '德国', code: 'DE', flag: 'flag-icon-de', continent: 'europe' },
  { name: '意大利', code: 'IT', flag: 'flag-icon-it', continent: 'europe' },
  { name: '西班牙', code: 'ES', flag: 'flag-icon-es', continent: 'europe' },
  { name: '荷兰', code: 'NL', flag: 'flag-icon-nl', continent: 'europe' },
  { name: '瑞典', code: 'SE', flag: 'flag-icon-se', continent: 'europe' },
  { name: '乌克兰', code: 'UA', flag: 'flag-icon-ua', continent: 'europe' },
  { name: '挪威', code: 'NO', flag: 'flag-icon-no', continent: 'europe' },
  { name: '波兰', code: 'PL', flag: 'flag-icon-pl', continent: 'europe' },
  { name: '俄罗斯', code: 'RU', flag: 'flag-icon-ru', continent: 'europe' },
  { name: '土耳其', code: 'TR', flag: 'flag-icon-tr', continent: 'europe' },
  { name: '希腊', code: 'GR', flag: 'flag-icon-gr', continent: 'europe' },
  { name: '葡萄牙', code: 'PT', flag: 'flag-icon-pt', continent: 'europe' },
  { name: '匈牙利', code: 'HU', flag: 'flag-icon-hu', continent: 'europe' },
  { name: '捷克', code: 'CZ', flag: 'flag-icon-cz', continent: 'europe' },

  // North America
  { name: '美国', code: 'US', flag: 'flag-icon-us', continent: 'namerica' },
  { name: '加拿大', code: 'CA', flag: 'flag-icon-ca', continent: 'namerica' },
  { name: '墨西哥', code: 'MX', flag: 'flag-icon-mx', continent: 'namerica' },
  { name: '古巴', code: 'CU', flag: 'flag-icon-cu', continent: 'namerica' },
  { name: '牙买加', code: 'JM', flag: 'flag-icon-jm', continent: 'namerica' },
  { name: '多米尼加', code: 'DO', flag: 'flag-icon-do', continent: 'namerica' },
  { name: '巴拿马', code: 'PA', flag: 'flag-icon-pa', continent: 'namerica' },
  { name: '哥斯达黎加', code: 'CR', flag: 'flag-icon-cr', continent: 'namerica' },
  { name: '危地马拉', code: 'GT', flag: 'flag-icon-gt', continent: 'namerica' },
  { name: '洪都拉斯', code: 'HN', flag: 'flag-icon-hn', continent: 'namerica' },

  // South America
  { name: '巴西', code: 'BR', flag: 'flag-icon-br', continent: 'samerica' },
  { name: '阿根廷', code: 'AR', flag: 'flag-icon-ar', continent: 'samerica' },
  { name: '哥伦比亚', code: 'CO', flag: 'flag-icon-co', continent: 'samerica' },
  { name: '智利', code: 'CL', flag: 'flag-icon-cl', continent: 'samerica' },
  { name: '秘鲁', code: 'PE', flag: 'flag-icon-pe', continent: 'samerica' },
  { name: '委内瑞拉', code: 'VE', flag: 'flag-icon-ve', continent: 'samerica' },
  { name: '厄瓜多尔', code: 'EC', flag: 'flag-icon-ec', continent: 'samerica' },
  { name: '玻利维亚', code: 'BO', flag: 'flag-icon-bo', continent: 'samerica' },
  { name: '巴拉圭', code: 'PY', flag: 'flag-icon-py', continent: 'samerica' },
  { name: '乌拉圭', code: 'UY', flag: 'flag-icon-uy', continent: 'samerica' },

  // Africa
  { name: '尼日利亚', code: 'NG', flag: 'flag-icon-ng', continent: 'africa' },
  { name: '南非', code: 'ZA', flag: 'flag-icon-za', continent: 'africa' },
  { name: '埃及', code: 'EG', flag: 'flag-icon-eg', continent: 'africa' },
  { name: '肯尼亚', code: 'KE', flag: 'flag-icon-ke', continent: 'africa' },
  { name: '埃塞俄比亚', code: 'ET', flag: 'flag-icon-et', continent: 'africa' },
  { name: '摩洛哥', code: 'MA', flag: 'flag-icon-ma', continent: 'africa' },
  { name: '阿尔及利亚', code: 'DZ', flag: 'flag-icon-dz', continent: 'africa' },
  { name: '加纳', code: 'GH', flag: 'flag-icon-gh', continent: 'africa' },
  { name: '坦桑尼亚', code: 'TZ', flag: 'flag-icon-tz', continent: 'africa' },
  { name: '乌干达', code: 'UG', flag: 'flag-icon-ug', continent: 'africa' },

  // Oceania
  { name: '澳大利亚', code: 'AU', flag: 'flag-icon-au', continent: 'oceania' },
  { name: '新西兰', code: 'NZ', flag: 'flag-icon-nz', continent: 'oceania' }
]

// 热门国家
const popularCountries = computed(() => [
  countries.find(c => c.code === 'CN')!,
  countries.find(c => c.code === 'US')!,
  countries.find(c => c.code === 'JP')!,
  countries.find(c => c.code === 'KR')!,
  countries.find(c => c.code === 'GB')!,
  countries.find(c => c.code === 'FR')!,
  countries.find(c => c.code === 'DE')!,
  countries.find(c => c.code === 'CA')!
])

// 显示的国家（限制数量）
const displayCountries = computed(() => 
  selectedCountries.value.slice(0, maxDisplayCount.value)
)

// 过滤后的国家
const filteredCountries = computed(() => {
  let filtered = countries.filter(country => 
    country.continent === activeContinent.value
  )
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = countries.filter(country =>
      country.name.toLowerCase().includes(query) ||
      country.code.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

// 获取大洲国家数量
const getContinentCountryCount = (continent: string) => {
  return countries.filter(c => c.continent === continent).length
}

// 检查是否已选择
const isSelected = (country: Country): boolean => {
  return selectedCountries.value.some(c => c.code === country.code)
}

// 切换国家选择
const toggleCountry = (country: Country) => {
  if (!multiple.value) {
    // 单选模式
    selectedCountries.value = [country]
    emit('update:modelValue', selectedCountries.value)
    emit('change', selectedCountries.value)
    closeSelector() // 单选时立即关闭
    return
  }
  
  // 多选模式
  const index = selectedCountries.value.findIndex(c => c.code === country.code)
  if (index === -1) {
    selectedCountries.value.push(country)
  } else {
    selectedCountries.value.splice(index, 1)
  }
  emit('update:modelValue', selectedCountries.value)
  emit('change', selectedCountries.value)
}

// 移除国家
const removeCountry = (country: Country) => {
  const index = selectedCountries.value.findIndex(c => c.code === country.code)
  if (index !== -1) {
    selectedCountries.value.splice(index, 1)
    emit('update:modelValue', selectedCountries.value)
    emit('change', selectedCountries.value)
  }
}

// 清空所有
const clearAll = () => {
  selectedCountries.value = []
  emit('update:modelValue', selectedCountries.value)
  emit('change', selectedCountries.value)
}

// 切换选择器显示
const toggleSelector = () => {
  if (isVisible.value) {
    closeSelector()
  } else {
    openSelector()
  }
}

// 打开选择器
const openSelector = () => {
  isVisible.value = true
  justOpened.value = true
  nextTick(() => {
    calculatePanelPosition()
    document.addEventListener('keydown', handleKeydown)
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside, true)
      justOpened.value = false
    }, 100)
  })
}

// 关闭选择器
const closeSelector = () => {
  isVisible.value = false
  searchQuery.value = ''
  justOpened.value = false
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('click', handleClickOutside, true)
}

// 处理点击外部
const handleClickOutside = (event: Event) => {
  if (!panelRef.value || !triggerRef.value || justOpened.value) return
  
  const target = event.target as Node
  const isClickInsidePanel = panelRef.value.contains(target)
  const isClickInsideTrigger = triggerRef.value.contains(target)
  
  if (!isClickInsidePanel && !isClickInsideTrigger) {
    closeSelector()
  }
}

// 键盘事件处理
const handleKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Escape') {
    closeSelector()
  }
}

// 监听窗口大小变化
const handleResize = () => {
  if (isVisible.value) {
    calculatePanelPosition()
  }
}

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedCountries.value = [...newValue]
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('click', handleClickOutside, true)
})

// 确认选择
const confirmSelection = () => {
  closeSelector()
}

// 计算面板位置
const calculatePanelPosition = () => {
  if (!triggerRef.value || !panelRef.value) return
  
  const triggerRect = triggerRef.value.getBoundingClientRect()
  const panelWidth = 600
  const panelHeight = 580
  const viewportHeight = window.innerHeight
  const viewportWidth = window.innerWidth
  const spaceBelow = viewportHeight - triggerRect.bottom
  const spaceAbove = triggerRect.top
  
  let top = triggerRect.bottom + 8
  let left = triggerRect.left
  
  // 如果下方空间不足，显示在上方
  if (spaceBelow < panelHeight && spaceAbove > panelHeight) {
    top = triggerRect.top - panelHeight - 8
  }
  
  // 确保不超出视口
  left = Math.max(8, Math.min(left, viewportWidth - panelWidth - 8))
  top = Math.max(8, Math.min(top, viewportHeight - panelHeight - 8))
  
  panelStyle.value = {
    position: 'fixed',
    top: `${top}px`,
    left: `${left}px`,
    width: `${panelWidth}px`,
    maxHeight: `${Math.min(panelHeight, viewportHeight - 16)}px`,
    zIndex: 9999
  }
}
</script>

<style scoped lang="scss">
.premium-country-selector {
  position: relative;
  width: 100%;
}

// 触发器样式
.selector-trigger {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
  position: relative;
  overflow: hidden;
  min-height: 36px;
  font-size: 13px;
  
  &.compact {
    padding: 5px 8px;
    min-height: 32px;
    font-size: 12px;
    border-radius: 6px;
  }
  
  &.hide-background {
    background: none;
    box-shadow: none;
    padding: 0;
    min-height: auto;
    
    &::before {
      display: none;
    }
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
    
    &::before {
      opacity: 1;
    }
  }
  
  &.is-open {
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
  
  &.has-selections {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    &.hide-background {
      background: none;
    }
  }
}

// 已选择国家显示
.selected-countries-display {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  
  .selected-items {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;
    flex: 1;
  }
}

.selected-country-chip {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 2px 6px 2px 2px;
  color: white;
  font-size: 11px;
  font-weight: 500;
  animation: chipSlideIn 0.3s ease-out;
  
  .compact & {
    padding: 1px 4px 1px 1px;
    font-size: 10px;
    border-radius: 8px;
  }
  
  .country-flag {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    object-fit: cover;
    
    .compact & {
      width: 12px;
      height: 12px;
    }
  }
  
  .remove-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 1px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }
}

.more-indicator {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 2px 6px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  
  .compact & {
    padding: 1px 4px;
    font-size: 9px;
  }
}

.add-more-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  
  .compact & {
    width: 18px;
    height: 18px;
    font-size: 10px;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
}

// 空状态触发器
.empty-state-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  color: white;
  font-weight: 500;
  
  .compact & {
    gap: 4px;
  }
  
  .globe-icon {
    font-size: 14px;
    
    .compact & {
      font-size: 12px;
    }
  }
  
  .trigger-text {
    flex: 1;
  }
  
  .chevron-icon {
    transition: transform 0.3s ease;
    font-size: 12px;
    
    .compact & {
      font-size: 10px;
    }
    
    &.rotated {
      transform: rotate(180deg);
    }
  }
}

// 选择器面板
.selector-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: panelSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e4e7ed;
}

// 面板头部
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  
  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .title-icon {
      font-size: 18px;
    }
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    .mode-badge {
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }
  }
  
  .close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    padding: 0.6em;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// 搜索区域
.search-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  
  .search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.3s ease;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    
    &:focus-within {
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .search-icon {
      color: #999;
      margin-right: 8px;
      font-size: 14px;
    }
    
    .search-input {
      flex: 1;
      border: none;
      background: none;
      outline: none;
      font-size: 13px;
      color: #333;
      
      &::placeholder {
        color: #999;
      }
    }
    
    .clear-search-btn {
      background: none;
      border: none;
      color: #999;
      cursor: pointer;
      padding: 2px;
      border-radius: 50%;
      transition: all 0.2s ease;
      
      &:hover {
        background: #f0f0f0;
        color: #666;
      }
    }
  }
}

// 主要内容区域
.main-content {
  display: flex;
  flex: 1;
  min-height: 0;
}

// 左侧大洲栏
.continent-sidebar {
  width: 140px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  
  .continent-title {
    padding: 12px 16px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    border-bottom: 1px solid #e9ecef;
  }
  
  .continent-list {
    flex: 1;
    overflow-y: auto;
  }
  
  .continent-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 10px 16px;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    border-radius: 0;
    
    &:hover {
      background: #e9ecef;
    }
    
    &.active {
      background: white;
      color: #667eea;
      font-weight: 600;
      border-right: 2px solid #667eea;
    }
    
    .continent-icon {
      font-size: 16px;
    }
    
    .continent-info {
      flex: 1;
      
      .continent-name {
        display: block;
        font-size: 12px;
      }
      
      .country-count {
        display: block;
        font-size: 10px;
        color: #999;
        margin-top: 1px;
      }
    }
  }
}

// 右侧内容区域
.countries-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 快速选择区域
.quick-select-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
  }
  
  .quick-select-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
  }
  
  .quick-select-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
    position: relative;
    
    &:hover {
      background: #e9ecef;
      border-color: #667eea;
    }
    
    &.selected {
      background: #667eea;
      border-color: #667eea;
      color: white;
      
      .check-icon {
        position: absolute;
        right: 2px;
        top: 2px;
        font-size: 10px;
      }
    }
    
    .country-flag {
      width: 14px;
      height: 14px;
      border-radius: 2px;
    }
    
    .country-name {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 国家区域
.countries-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 12px 20px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    border-bottom: 1px solid #f0f0f0;
    
    .result-count {
      color: #999;
      font-weight: normal;
    }
  }
  
  .empty-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    color: #999;
    
    .empty-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
    
    p {
      margin: 0;
      font-size: 13px;
    }
  }
  
  .countries-grid {
    flex: 1;
    padding: 12px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 8px;
    overflow-y: auto;
    align-content: start;
    
    .country-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 10px;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      font-size: 12px;
      height: 36px;
      min-width: 0;
      
      &:hover {
        background: #f8f9fa;
        border-color: #667eea;
        transform: translateY(-1px);
      }
      
      &.selected {
        background: #667eea;
        border-color: #667eea;
        color: white;
        
        .check-icon {
          position: absolute;
          right: 6px;
          color: white;
          font-size: 12px;
        }
      }
      
      .country-flag {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        flex-shrink: 0;
      }
      
      .country-name {
        flex: 1;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 面板底部
.panel-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  
  .selection-summary {
    font-size: 12px;
    color: #666;
    
    .count {
      font-weight: 600;
      color: #667eea;
    }
  }
  
  .footer-actions {
    display: flex;
    gap: 8px;
    
    .clear-all-btn {
      background: none;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 6px 12px;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 11px;
      
      &:hover {
        background: #f0f0f0;
        border-color: #d0d0d0;
      }
    }
    
    .confirm-btn {
      background: #667eea;
      border: none;
      border-radius: 4px;
      padding: 6px 16px;
      color: white;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 11px;
      font-weight: 500;
      
      &:hover {
        background: #5a67d8;
      }
    }
  }
}

// 动画
@keyframes chipSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 过渡动画
.selector-fade-enter-active,
.selector-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.selector-fade-enter-from,
.selector-fade-leave-to {
  opacity: 0;
}

.check-fade-enter-active,
.check-fade-leave-active {
  transition: all 0.2s ease;
}

.check-fade-enter-from,
.check-fade-leave-to {
  opacity: 0;
  transform: scale(0.5);
}

// 响应式设计
@media (max-width: 768px) {
  .selector-panel {
    width: calc(100vw - 16px) !important;
    max-width: none !important;
    height: calc(100vh - 32px) !important;
    max-height: none !important;
    margin: 8px;
  }
  
  .continent-sidebar {
    width: 100px;
    
    .continent-item {
      padding: 8px 12px;
      
      .continent-info {
        .continent-name {
          font-size: 11px;
        }
        
        .country-count {
          font-size: 9px;
        }
      }
    }
  }
  
  .quick-select-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .countries-grid {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    padding: 8px 16px;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .selected-countries-display {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }
  
  .panel-header {
    padding: 12px 16px;
    
    .header-title h3 {
      font-size: 14px;
    }
  }
  
  .search-section,
  .quick-select-section {
    padding: 12px 16px;
  }
  
  .continent-sidebar {
    width: 80px;
  }
}

// 单选模式显示
.single-country-display {
  display: flex;
  align-items: center;
  gap: 6px;
  color: inherit;
  
  .country-flag {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    object-fit: cover;
  }
  
  .country-name {
    font-weight: 500;
  }
  
  .chevron-icon {
    transition: transform 0.3s ease;
    font-size: 12px;
    margin-left: 4px;
    
    &.rotated {
      transform: rotate(180deg);
    }
  }
}
</style>