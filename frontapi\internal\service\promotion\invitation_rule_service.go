package promotion

import (
	model "frontapi/internal/models/promotion"
	repo "frontapi/internal/repository/promotion"
	"frontapi/internal/service/base"
)

// CreateInvitationRuleRequest 创建规则请求
type CreateInvitationRuleRequest struct {
	Name                  string  `json:"name"`
	InviteRewardPoints    int     `json:"invite_reward_points"`
	InvitedRewardPoints   int     `json:"invited_reward_points"`
	CommissionRate        float64 `json:"commission_rate"`
	CommissionPeriod      int     `json:"commission_period"`
	IsPermanentCommission int8    `json:"is_permanent_commission"`
	Description           string  `json:"description"`
	Status                int8    `json:"status"`
}

type UpdateInvitationRuleRequest struct {
	Name                  string  `json:"name"`
	InviteRewardPoints    int     `json:"invite_reward_points"`
	InvitedRewardPoints   int     `json:"invited_reward_points"`
	CommissionRate        float64 `json:"commission_rate"`
	CommissionPeriod      int     `json:"commission_period"`
	IsPermanentCommission int8    `json:"is_permanent_commission"`
	Description           string  `json:"description"`
	Status                int8    `json:"status"`
}

// InvitationRuleService 邀请奖励规则服务接口
type InvitationRuleService interface {
	base.IExtendedService[model.InvitationRule]
}

type invitationRuleService struct {
	*base.ExtendedService[model.InvitationRule]
	repo repo.InvitationRuleRepository
}

func NewInvitationRuleService(repo repo.InvitationRuleRepository) InvitationRuleService {
	return &invitationRuleService{
		ExtendedService: base.NewExtendedService[model.InvitationRule](repo, "invitation_rule"),
		repo:            repo,
	}
}
