package shortvideos

import (
	"frontapi/internal/admin"
	"frontapi/internal/service/shortvideos"
	"frontapi/pkg/validator"
	"strings"

	"github.com/gofiber/fiber/v2"
)

// ShortVideoCommentController 短视频控制器
type ShortVideoCommentController struct {
	ShortVideoCommentService shortvideos.ShortVideoCommentService
	ShortVideoService        shortvideos.ShortVideoService
	admin.BaseController     // 继承BaseController
}

// NewShortVideoCommentController 创建短视频控制器实例
func NewShortVideoCommentController(
	shortVideoCommentService shortvideos.ShortVideoCommentService,
	shortVideoService shortvideos.ShortVideoService,
) *ShortVideoCommentController {
	return &ShortVideoCommentController{
		ShortVideoCommentService: shortVideoCommentService,
		ShortVideoService:        shortVideoService,
	}
}

// ListShortVideoComments 获取短视频评论列表
func (c *ShortVideoCommentController) ListShortVideoComments(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	shortVideoId := reqInfo.Get("short_id").GetString()
	userId := reqInfo.Get("user_id").GetString()
	keyword := reqInfo.Get("keyword").GetString()
	username := reqInfo.Get("username").GetString()
	status := -999
	err := c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询短视频评论列表
	commentList, total, err := c.ShortVideoCommentService.List(ctx.Context(), map[string]interface{}{
		"short_id": shortVideoId,
		"user_id":  userId,
		"status":   status,
		"keyword":  keyword,
		"username": username,
	}, orderBy, page, pageSize, false)

	if err != nil {
		return c.InternalServerError(ctx, "获取短视频评论列表失败: "+err.Error())
	}

	// 返回短视频评论列表
	return c.SuccessList(ctx, commentList, total, page, pageSize)
}

// GetShortVideoComment 获取短视频评论详情
func (c *ShortVideoCommentController) GetShortVideoComment(ctx *fiber.Ctx) error {
	// 获取评论ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询评论
	comment, err := c.ShortVideoCommentService.GetByID(ctx.Context(), id, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取短视频评论详情失败: "+err.Error())
	}

	if comment == nil {
		return c.NotFound(ctx, "短视频评论不存在")
	}

	// 返回评论详情
	return c.Success(ctx, comment)
}

// LikeComment 点赞评论
func (c *ShortVideoCommentController) LikeComment(ctx *fiber.Ctx) error {
	// 获取评论ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 获取用户ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := reqInfo.Get("user_id").GetString()
	if userID == "" {
		return c.BadRequest(ctx, "用户ID不能为空", nil)
	}

	// 执行点赞操作
	err = c.ShortVideoCommentService.LikeComment(ctx.Context(), userID, id)
	if err != nil {
		return c.InternalServerError(ctx, "点赞评论失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "点赞成功")
}

// UnlikeComment 取消点赞评论
func (c *ShortVideoCommentController) UnlikeComment(ctx *fiber.Ctx) error {
	// 获取评论ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 获取用户ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := reqInfo.Get("user_id").GetString()
	if userID == "" {
		return c.BadRequest(ctx, "用户ID不能为空", nil)
	}

	// 执行取消点赞操作
	err = c.ShortVideoCommentService.UnlikeComment(ctx.Context(), userID, id)
	if err != nil {
		return c.InternalServerError(ctx, "取消点赞评论失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "取消点赞成功")
}

// GetCommentLikeInfo 获取评论点赞信息
func (c *ShortVideoCommentController) GetCommentLikeInfo(ctx *fiber.Ctx) error {
	// 获取评论ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 获取用户ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := reqInfo.Get("user_id").GetString()

	// 获取点赞状态
	var isLiked bool
	if userID != "" {
		isLiked, err = c.ShortVideoCommentService.IsCommentLiked(ctx.Context(), userID, id)
		if err != nil {
			return c.InternalServerError(ctx, "获取点赞状态失败: "+err.Error())
		}
	}

	// 获取点赞数量
	likeCount, err := c.ShortVideoCommentService.GetCommentLikeCount(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "获取点赞数量失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"is_liked":   isLiked,
		"like_count": likeCount,
	})
}

// BatchGetCommentLikeInfo 批量获取评论点赞信息
func (c *ShortVideoCommentController) BatchGetCommentLikeInfo(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)

	// 解析评论ID列表
	commentIDsStr := reqInfo.Get("comment_ids").GetString()
	if commentIDsStr == "" {
		return c.BadRequest(ctx, "评论ID列表不能为空", nil)
	}

	// 简单的字符串分割处理，实际项目中可能需要JSON解析
	// 这里假设是逗号分隔的字符串："id1,id2,id3"
	commentIDStrings := strings.Split(commentIDsStr, ",")

	// 清理空字符串
	var validIDs []string
	for _, id := range commentIDStrings {
		trimmed := strings.TrimSpace(id)
		if trimmed != "" {
			validIDs = append(validIDs, trimmed)
		}
	}
	commentIDStrings = validIDs

	if len(commentIDStrings) == 0 {
		return c.BadRequest(ctx, "评论ID列表不能为空", nil)
	}

	// 获取用户ID
	userID := reqInfo.Get("user_id").GetString()

	// 批量获取点赞信息
	likeStatuses, likeCounts, err := c.ShortVideoCommentService.BatchGetCommentLikeInfo(ctx.Context(), userID, commentIDStrings)
	if err != nil {
		return c.InternalServerError(ctx, "批量获取点赞信息失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"like_statuses": likeStatuses,
		"like_counts":   likeCounts,
	})
}

// GetHotComments 获取热门评论
func (c *ShortVideoCommentController) GetHotComments(ctx *fiber.Ctx) error {
	// 获取请求参数
	reqInfo := c.GetRequestInfo(ctx)

	// 获取短视频ID
	videoID := reqInfo.Get("video_id").GetString()
	if videoID == "" {
		return c.BadRequest(ctx, "短视频ID不能为空", nil)
	}

	// 获取限制数量，默认为10
	limit := reqInfo.Get("limit").GetInt()
	if limit <= 0 {
		limit = 10
	}

	// 获取热门评论
	hotComments, err := c.ShortVideoCommentService.GetHotComments(ctx.Context(), videoID, limit)
	if err != nil {
		return c.InternalServerError(ctx, "获取热门评论失败: "+err.Error())
	}

	return c.Success(ctx, hotComments)
}

// GetUserCommentStats 获取用户评论统计
func (c *ShortVideoCommentController) GetUserCommentStats(ctx *fiber.Ctx) error {
	// 获取用户ID
	reqInfo := c.GetRequestInfo(ctx)
	userID := reqInfo.Get("user_id").GetString()
	if userID == "" {
		return c.BadRequest(ctx, "用户ID不能为空", nil)
	}

	// 获取用户评论统计
	stats, err := c.ShortVideoCommentService.GetUserCommentStats(ctx.Context(), userID)
	if err != nil {
		return c.InternalServerError(ctx, "获取用户评论统计失败: "+err.Error())
	}

	return c.Success(ctx, stats)
}

// UpdateShortVideoCommentStatus 更新评论状态
func (c *ShortVideoCommentController) UpdateShortVideoCommentStatus(ctx *fiber.Ctx) error {
	var req struct {
		ID     string `json:"id" validate:"required"`
		Status int    `json:"status" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 获取评论
	comment, err := c.ShortVideoCommentService.GetByID(ctx.Context(), req.ID, false)
	if err != nil {
		return c.NotFound(ctx, "评论不存在")
	}

	// 更新状态
	comment.Status = int8(req.Status)
	if err := c.ShortVideoCommentService.Update(ctx.Context(), comment); err != nil {
		return c.InternalServerError(ctx, "更新评论状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新评论状态成功")
}

// DeleteShortVideoComment 删除评论
func (c *ShortVideoCommentController) DeleteShortVideoComment(ctx *fiber.Ctx) error {
	id, err := c.GetId(ctx)
	if err != nil {
		return c.BadRequest(ctx, "评论ID不能为空", nil)
	}

	// 删除评论
	if err := c.ShortVideoCommentService.Delete(ctx.Context(), id); err != nil {
		return c.InternalServerError(ctx, "删除评论失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除评论成功")
}

// BatchUpdateShortVideoCommentStatus 批量更新评论状态
func (c *ShortVideoCommentController) BatchUpdateShortVideoCommentStatus(ctx *fiber.Ctx) error {
	var req struct {
		Ids    []string `json:"ids" validate:"required"`
		Status int      `json:"status" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量更新状态
	for _, id := range req.Ids {
		comment, err := c.ShortVideoCommentService.GetByID(ctx.Context(), id, false)
		if err != nil {
			continue // 跳过不存在的评论
		}

		comment.Status = int8(req.Status)
		if err := c.ShortVideoCommentService.Update(ctx.Context(), comment); err != nil {
			return c.InternalServerError(ctx, "批量更新评论状态失败: "+err.Error())
		}
	}

	return c.SuccessWithMessage(ctx, "批量更新评论状态成功")
}

// BatchDeleteShortVideoComment 批量删除评论
func (c *ShortVideoCommentController) BatchDeleteShortVideoComment(ctx *fiber.Ctx) error {
	var req struct {
		Ids []string `json:"ids" validate:"required"`
	}

	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	// 批量删除评论
	for _, id := range req.Ids {
		if err := c.ShortVideoCommentService.Delete(ctx.Context(), id); err != nil {
			return c.InternalServerError(ctx, "批量删除评论失败: "+err.Error())
		}
	}

	return c.SuccessWithMessage(ctx, "批量删除评论成功")
}
