/**
 * 组合式函数工具函数
 */

import { ref, unref, watch, nextTick, isRef } from 'vue'
import type { Ref, MaybeRef } from 'vue'
import type { MaybeRefOrGetter } from './types'
import {
  debounce,
  throttle,
  deepClone,
  formatFileSize,
  formatTime,
  once,
  retry
} from '@/core/utils'

// 类型守卫
export function isFunction(val: unknown): val is Function {
  return typeof val === 'function'
}

export function isString(val: unknown): val is string {
  return typeof val === 'string'
}

export function isNumber(val: unknown): val is number {
  return typeof val === 'number'
}

export function isBoolean(val: unknown): val is boolean {
  return typeof val === 'boolean'
}

export function isObject(val: unknown): val is Record<string, any> {
  return val !== null && typeof val === 'object'
}

export function isArray(val: unknown): val is any[] {
  return Array.isArray(val)
}

export function isPromise(val: unknown): val is Promise<any> {
  return val instanceof Promise
}

export function isDate(val: unknown): val is Date {
  return val instanceof Date
}

export function isRegExp(val: unknown): val is RegExp {
  return val instanceof RegExp
}

export function isError(val: unknown): val is Error {
  return val instanceof Error
}

export function isElement(val: unknown): val is Element {
  return val instanceof Element
}

export function isWindow(val: unknown): val is Window {
  return val === window
}

export function isClient(): boolean {
  return typeof window !== 'undefined'
}

export function isServer(): boolean {
  return typeof window === 'undefined'
}

// 响应式工具
export function toValue<T>(r: MaybeRefOrGetter<T>): T {
  return typeof r === 'function'
    ? (r as any)()
    : unref(r)
}

export function toRef<T>(r: MaybeRef<T>): Ref<T> {
  return isRef(r) ? r : ref(r)
}

export function tryOnMounted(fn: () => void, sync = true) {
  if (getCurrentInstance()) {
    onMounted(fn)
  } else if (sync) {
    fn()
  } else {
    nextTick(fn)
  }
}

export function tryOnUnmounted(fn: () => void) {
  if (getCurrentInstance()) {
    onUnmounted(fn)
  }
}

export function tryOnScopeDispose(fn: () => void) {
  if (getCurrentScope()) {
    onScopeDispose(fn)
  }
}

// 使用统一的工具函数

// 重新导出统一的工具函数
export { debounce, throttle, deepClone, formatFileSize, formatTime, once, retry }

// 深度合并
export function deepMerge<T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  if (!sources.length) return target
  const source = sources.shift()

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }

  return deepMerge(target, ...sources)
}

// 对象路径获取
export function get(obj: any, path: string, defaultValue?: any): any {
  const keys = path.split('.')
  let result = obj

  for (const key of keys) {
    if (result == null || typeof result !== 'object') {
      return defaultValue
    }
    result = result[key]
  }

  return result !== undefined ? result : defaultValue
}

// 对象路径设置
export function set(obj: any, path: string, value: any): void {
  const keys = path.split('.')
  let current = obj

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current) || !isObject(current[key])) {
      current[key] = {}
    }
    current = current[key]
  }

  current[keys[keys.length - 1]] = value
}

// 数组去重
export function unique<T>(arr: T[]): T[] {
  return [...new Set(arr)]
}

// 数组分组
export function groupBy<T>(
  arr: T[],
  key: string | ((item: T) => string)
): Record<string, T[]> {
  return arr.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : get(item, key)
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {} as Record<string, T[]>)
}

// 数组排序
export function sortBy<T>(
  arr: T[],
  key: string | ((item: T) => any),
  order: 'asc' | 'desc' = 'asc'
): T[] {
  return [...arr].sort((a, b) => {
    const aVal = typeof key === 'function' ? key(a) : get(a, key)
    const bVal = typeof key === 'function' ? key(b) : get(b, key)

    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

// 随机数生成
export function random(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// UUID生成
export function uuid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

// 短ID生成
export function nanoid(size = 21): string {
  const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let id = ''
  for (let i = 0; i < size; i++) {
    id += alphabet[Math.floor(Math.random() * alphabet.length)]
  }
  return id
}

// 字符串工具
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

export function camelCase(str: string): string {
  return str.replace(/[-_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ''))
}

export function kebabCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
}

export function snakeCase(str: string): string {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase()
}

// 数字格式化
export function formatNumber(
  num: number,
  options: {
    decimals?: number
    separator?: string
    delimiter?: string
  } = {}
): string {
  const { decimals = 0, separator = '.', delimiter = ',' } = options
  const parts = num.toFixed(decimals).split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, delimiter)
  return parts.join(separator)
}

// 使用统一的工具函数

// 相对时间
export function timeAgo(time: Date | number | string): string {
  const now = Date.now()
  const past = new Date(time).getTime()
  const diff = now - past

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day

  if (diff < minute) return '刚刚'
  if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
  if (diff < day) return `${Math.floor(diff / hour)}小时前`
  if (diff < week) return `${Math.floor(diff / day)}天前`
  if (diff < month) return `${Math.floor(diff / week)}周前`
  if (diff < year) return `${Math.floor(diff / month)}个月前`
  return `${Math.floor(diff / year)}年前`
}

// URL工具
export function parseUrl(url: string): {
  protocol: string
  host: string
  pathname: string
  search: string
  hash: string
  params: Record<string, string>
} {
  const a = document.createElement('a')
  a.href = url

  const params: Record<string, string> = {}
  if (a.search) {
    a.search
      .slice(1)
      .split('&')
      .forEach(param => {
        const [key, value] = param.split('=')
        params[decodeURIComponent(key)] = decodeURIComponent(value || '')
      })
  }

  return {
    protocol: a.protocol,
    host: a.host,
    pathname: a.pathname,
    search: a.search,
    hash: a.hash,
    params
  }
}

export function buildUrl(
  base: string,
  params: Record<string, any> = {}
): string {
  const url = new URL(base)
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      url.searchParams.set(key, String(value))
    }
  })
  return url.toString()
}

// 颜色工具
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      }
    : null
}

export function rgbToHex(r: number, g: number, b: number): string {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

// 设备检测
export function isMobile(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

export function isIOS(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

export function isAndroid(): boolean {
  return /Android/.test(navigator.userAgent)
}

export function isSafari(): boolean {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
}

export function isChrome(): boolean {
  return /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor)
}

export function isFirefox(): boolean {
  return /Firefox/.test(navigator.userAgent)
}

// 存储工具
export function getStorageItem<T>(
  key: string,
  defaultValue: T,
  storage: Storage = localStorage
): T {
  try {
    const item = storage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch {
    return defaultValue
  }
}

export function setStorageItem<T>(
  key: string,
  value: T,
  storage: Storage = localStorage
): void {
  try {
    storage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('Failed to set storage item:', error)
  }
}

export function removeStorageItem(
  key: string,
  storage: Storage = localStorage
): void {
  try {
    storage.removeItem(key)
  } catch (error) {
    console.error('Failed to remove storage item:', error)
  }
}

// 事件工具
export function on(
  element: EventTarget,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions
): void {
  element.addEventListener(event, handler, options)
}

export function off(
  element: EventTarget,
  event: string,
  handler: EventListener,
  options?: boolean | EventListenerOptions
): void {
  element.removeEventListener(event, handler, options)
}

// 使用统一的工具函数

// 异步工具
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function timeout<T>(
  promise: Promise<T>,
  ms: number,
  message = 'Timeout'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(message)), ms)
    )
  ])
}

// 使用统一的工具函数

// 验证工具
export function isEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

export function isPhone(phone: string): boolean {
  return /^1[3-9]\d{9}$/.test(phone)
}

export function isUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export function isIPv4(ip: string): boolean {
  return /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ip)
}

// 性能工具
export function measure<T>(fn: () => T, label?: string): T {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  if (label) {
    console.log(`${label}: ${end - start}ms`)
  }
  return result
}

export async function measureAsync<T>(
  fn: () => Promise<T>,
  label?: string
): Promise<T> {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  if (label) {
    console.log(`${label}: ${end - start}ms`)
  }
  return result
}

// 导入Vue相关函数
import {
  getCurrentInstance,
  getCurrentScope,
  onMounted,
  onUnmounted,
  onScopeDispose
} from 'vue'