# 服务基础类重构方案

## 问题分析

当前 `internal/service/base` 目录下存在大量重复代码，主要体现在以下几个方面：

### 1. 重复的代码模式

#### 1.1 CRUD操作重复
- `BaseService` 和 `IntBaseService` 中的 Create、GetByID、Update、Delete 方法逻辑几乎相同
- 唯一区别是ID类型（string vs int）
- 钩子执行、缓存处理、错误处理逻辑完全一致

#### 1.2 缓存管理重复
- `getCacheKey()` 方法在多个类中重复实现
- `deleteCacheByID()` 方法逻辑相同
- 缓存TTL设置和管理重复

#### 1.3 钩子管理重复
- 钩子注册和执行逻辑在多个服务中重复
- `RegisterHook`、`RegisterDuplicateCheck`、`RegisterDataClean` 方法重复

#### 1.4 工具方法重复
- `BuildCacheKey`、`BuildListCacheKey` 等工具方法重复
- 参数验证逻辑重复
- 反射操作重复

### 2. 架构问题

#### 2.1 继承层次混乱
- `ExtendedService` 和 `IntExtendedService` 都重写了基础方法
- 钩子管理器在多个层级重复创建
- 职责边界不清晰

#### 2.2 类型安全问题
- ID类型转换在多处重复
- 泛型约束不够严格

## 重构方案

### 阶段一：提取公共接口和抽象类

#### 1.1 创建通用服务接口

```go
// IGenericService 通用服务接口
type IGenericService[T any, ID comparable] interface {
    // 基础CRUD操作
    Create(ctx context.Context, entity *T) (ID, error)
    GetByID(ctx context.Context, id ID, useCache bool) (*T, error)
    Update(ctx context.Context, entity *T) error
    Delete(ctx context.Context, id ID) error
    List(ctx context.Context, condition map[string]interface{}, orderBy string, page, pageSize int, useCache bool) ([]*T, int64, error)
    
    // 批量操作
    BatchCreate(ctx context.Context, entities []*T) (int, error)
    BatchUpdate(ctx context.Context, entities []*T) error
    BatchDelete(ctx context.Context, ids []ID) (int, error)
    
    // 查询操作
    FindByIDs(ctx context.Context, ids []ID) ([]*T, error)
    FindByCondition(ctx context.Context, condition map[string]interface{}, orderBy string) ([]*T, error)
    Count(ctx context.Context, condition map[string]interface{}) (int64, error)
    
    // 缓存管理
    SetCacheTTL(ttl time.Duration)
    
    // 钩子管理
    RegisterHook(hookType HookType, hook Hook[T])
}
```

#### 1.2 创建抽象基础服务

```go
// AbstractBaseService 抽象基础服务
type AbstractBaseService[T ModelConstraint[ID], ID comparable] struct {
    cacheTTL    time.Duration
    hookManager HookManager[T]
    entityType  string
}

// 实现通用的缓存、钩子、工具方法
func (s *AbstractBaseService[T, ID]) getCacheKey(id ID) string {
    return fmt.Sprintf("%s:%v", s.entityType, id)
}

func (s *AbstractBaseService[T, ID]) deleteCacheByID(id ID) {
    cacheKey := s.getCacheKey(id)
    redis.Del(cacheKey)
}

// 通用的CRUD模板方法
func (s *AbstractBaseService[T, ID]) executeCreate(ctx context.Context, entity *T, repoCreate func() (ID, error)) (ID, error) {
    // 执行创建前钩子
    if err := s.hookManager.ExecuteHooks(constant.BeforeCreate, ctx, entity); err != nil {
        var zero ID
        return zero, fmt.Errorf("创建前钩子执行失败: %w", err)
    }

    s.SetEntityDefaults(entity)
    id, err := repoCreate()
    if err != nil {
        var zero ID
        return zero, fmt.Errorf("创建失败: %w", err)
    }

    // 执行创建后钩子
    if err := s.hookManager.ExecuteHooks(constant.AfterCreate, ctx, entity); err != nil {
        // 创建后钩子失败只记录日志
    }

    return id, nil
}
```

### 阶段二：重构具体服务类

#### 2.1 重构BaseService

```go
// BaseService 字符串ID的基础服务
type BaseService[T models.BaseModelConstraint] struct {
    *AbstractBaseService[T, string]
    repo baseRepo.BaseRepository[T]
}

func NewBaseService[T models.BaseModelConstraint](repo baseRepo.BaseRepository[T]) *BaseService[T] {
    entityType := reflect.TypeOf((*T)(nil)).Elem().Name()
    return &BaseService[T]{
        AbstractBaseService: NewAbstractBaseService[T, string](entityType),
        repo:               repo,
    }
}

// Create 实现具体的创建逻辑
func (s *BaseService[T]) Create(ctx context.Context, entity *T) (string, error) {
    return s.executeCreate(ctx, entity, func() (string, error) {
        err := s.repo.Create(ctx, entity)
        if err != nil {
            return "", err
        }
        return (*entity).GetID(), nil
    })
}
```

#### 2.2 重构IntBaseService

```go
// IntBaseService 整数ID的基础服务
type IntBaseService[T models.IntBaseModelConstraint] struct {
    *AbstractBaseService[T, int]
    repo extint.IntBaseRepository[T]
}

func NewIntBaseService[T models.IntBaseModelConstraint](repo extint.IntBaseRepository[T]) *IntBaseService[T] {
    entityType := reflect.TypeOf((*T)(nil)).Elem().Name()
    return &IntBaseService[T]{
        AbstractBaseService: NewAbstractBaseService[T, int](entityType),
        repo:               repo,
    }
}

// Create 实现具体的创建逻辑
func (s *IntBaseService[T]) Create(ctx context.Context, entity *T) (int, error) {
    return s.executeCreate(ctx, entity, func() (int, error) {
        err := s.repo.Create(ctx, entity)
        if err != nil {
            return 0, err
        }
        return (*entity).GetID(), nil
    })
}
```

### 阶段三：统一扩展服务

#### 3.1 创建通用扩展接口

```go
// IExtendedService 通用扩展服务接口
type IExtendedService[T any, ID comparable] interface {
    IGenericService[T, ID]
    
    // 扩展功能
    UpdateViewCount(ctx context.Context, id ID, increment int64) error
    UpdateLikeCount(ctx context.Context, id ID, increment int64) error
    UpdateCommentCount(ctx context.Context, id ID, increment int64) error
    UpdateShareCount(ctx context.Context, id ID, increment int64) error
    
    // 软删除
    SoftDelete(ctx context.Context, id ID) error
    BatchSoftDelete(ctx context.Context, ids []ID) error
    Restore(ctx context.Context, id ID) error
    BatchRestore(ctx context.Context, ids []ID) error
    
    // 排序和特色
    UpdateSortOrder(ctx context.Context, id ID, sortOrder int) error
    SetFeatured(ctx context.Context, id ID, featured bool) error
}
```

#### 3.2 创建抽象扩展服务

```go
// AbstractExtendedService 抽象扩展服务
type AbstractExtendedService[T ModelConstraint[ID], ID comparable] struct {
    baseService IGenericService[T, ID]
    entityType  string
}

// 实现通用的扩展功能
func (s *AbstractExtendedService[T, ID]) UpdateViewCount(ctx context.Context, id ID, increment int64) error {
    return s.updateCount(ctx, id, "view_count", increment)
}

func (s *AbstractExtendedService[T, ID]) updateCount(ctx context.Context, id ID, field string, increment int64) error {
    // 通用的计数更新逻辑
    // ...
}
```

### 阶段四：创建工具包

#### 4.1 缓存工具包

```go
// pkg/cache/service_cache.go
package cache

type ServiceCache[ID comparable] struct {
    entityType string
    ttl        time.Duration
}

func NewServiceCache[ID comparable](entityType string, ttl time.Duration) *ServiceCache[ID] {
    return &ServiceCache[ID]{
        entityType: entityType,
        ttl:        ttl,
    }
}

func (c *ServiceCache[ID]) GetKey(id ID) string {
    return fmt.Sprintf("%s:%v", c.entityType, id)
}

func (c *ServiceCache[ID]) GetListKey(condition map[string]interface{}, page, pageSize int) string {
    return fmt.Sprintf("%s:list:%v:%d:%d", c.entityType, condition, page, pageSize)
}

func (c *ServiceCache[ID]) Set(key string, value interface{}) error {
    return redis.SetJSON(key, value, c.ttl)
}

func (c *ServiceCache[ID]) Get(key string, dest interface{}) error {
    return redis.GetJSON(key, dest)
}

func (c *ServiceCache[ID]) Delete(key string) error {
    return redis.Del(key)
}
```

#### 4.2 钩子工具包

```go
// pkg/hooks/service_hooks.go
package hooks

type ServiceHookManager[T any] struct {
    hooks map[HookType][]Hook[T]
    mu    sync.RWMutex
}

func NewServiceHookManager[T any]() *ServiceHookManager[T] {
    return &ServiceHookManager[T]{
        hooks: make(map[HookType][]Hook[T]),
    }
}

// 通用钩子注册和执行逻辑
```

### 阶段五：重构现有服务

#### 5.1 更新Permission服务

```go
// 使用新的基础服务
type roleService struct {
    *IntExtendedService[permission.AdminSysRole]
    repo permission.RoleRepository
}

func NewRoleService(repo permission.RoleRepository) RoleService {
    return &roleService{
        IntExtendedService: NewIntExtendedService[permission.AdminSysRole](repo),
        repo:              repo,
    }
}
```

## 重构收益

### 1. 代码减少
- 预计减少重复代码 60-70%
- 统一的接口和实现
- 更好的类型安全

### 2. 维护性提升
- 单一职责原则
- 更清晰的继承层次
- 统一的错误处理和日志记录

### 3. 扩展性增强
- 更容易添加新的ID类型支持
- 插件化的钩子系统
- 可配置的缓存策略

### 4. 性能优化
- 统一的缓存管理
- 减少反射操作
- 更好的内存使用

## 实施计划

### 第一周：基础架构
1. 创建抽象基础类和接口
2. 实现缓存和钩子工具包
3. 编写单元测试

### 第二周：重构基础服务
1. 重构 BaseService 和 IntBaseService
2. 重构扩展服务
3. 更新相关测试

### 第三周：迁移业务服务
1. 更新 Permission 模块
2. 更新其他业务模块
3. 集成测试

### 第四周：优化和文档
1. 性能优化
2. 完善文档
3. 代码审查

## 风险评估

### 高风险
- 大规模重构可能引入新的bug
- 业务服务需要同步更新

### 中风险
- 泛型使用可能增加编译时间
- 学习成本

### 低风险
- 向后兼容性问题
- 性能影响

## 建议

1. **分阶段实施**：先完成基础架构，再逐步迁移业务代码
2. **保持向后兼容**：在过渡期保留旧接口
3. **充分测试**：每个阶段都要有完整的测试覆盖
4. **文档先行**：先完善设计文档，再开始编码
5. **团队培训**：确保团队理解新的架构设计