package admin

import (
	"frontapi/internal/bootstrap"

	"github.com/gofiber/fiber/v2"
)

func RegisterPromotionRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {

	// promotionGroup := app.Group("/promotion")

	// // 推广活动
	// campaignCtrl := promotion2.NewPromotionCampaignController(services.PromotionCampaignService)

	// // 邀请佣金
	// commissionCtrl := promotion2.NewInvitationCommissionController(services.InvitationCommissionService)

	// // 邀请规则
	// ruleCtrl := promotion2.NewInvitationRuleController(services.InvitationRuleService)

}
