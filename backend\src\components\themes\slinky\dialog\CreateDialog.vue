<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :modal-class="modalClass"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :destroy-on-close="destroyOnClose"
    :class="['slinky-dialog', dialogClass]"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        :size="size"
        :disabled="submitting"
        @submit.prevent="handleSubmit"
      >
        <template v-for="field in fields" :key="field.prop">
          <el-form-item
            :label="field.label"
            :prop="field.prop"
            :rules="field.rules"
            :required="field.required"
            :class="field.class"
          >
            <!-- 文本输入框 -->
            <el-input
              v-if="field.type === 'input'"
              v-model="formData[field.prop]"
              :type="field.inputType || 'text'"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :readonly="field.readonly"
              :maxlength="field.maxlength"
              :minlength="field.minlength"
              :show-word-limit="field.showWordLimit"
              :prefix-icon="field.prefixIcon"
              :suffix-icon="field.suffixIcon"
              @change="field.onChange && field.onChange($event)"
              @blur="field.onBlur && field.onBlur($event)"
              @focus="field.onFocus && field.onFocus($event)"
            />
            
            <!-- 文本域 -->
            <el-input
              v-else-if="field.type === 'textarea'"
              v-model="formData[field.prop]"
              type="textarea"
              :placeholder="field.placeholder || `请输入${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :readonly="field.readonly"
              :maxlength="field.maxlength"
              :minlength="field.minlength"
              :show-word-limit="field.showWordLimit"
              :rows="field.rows || 3"
              :autosize="field.autosize"
              :resize="field.resize"
              @change="field.onChange && field.onChange($event)"
            />
            
            <!-- 下拉选择 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :multiple="field.multiple"
              :filterable="field.filterable"
              :allow-create="field.allowCreate"
              :remote="field.remote"
              :remote-method="field.remoteMethod"
              :loading="field.loading"
              :multiple-limit="field.multipleLimit"
              @change="field.onChange && field.onChange($event)"
              @visible-change="field.onVisibleChange && field.onVisibleChange($event)"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
            
            <!-- 级联选择 -->
            <el-cascader
              v-else-if="field.type === 'cascader'"
              v-model="formData[field.prop]"
              :options="field.options"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :show-all-levels="field.showAllLevels !== false"
              :collapse-tags="field.collapseTags"
              :separator="field.separator || '/'"
              :props="field.cascaderProps"
              @change="field.onChange && field.onChange($event)"
            />
            
            <!-- 日期选择 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="formData[field.prop]"
              :type="(field.dateType as any) || 'date'"
              :placeholder="field.placeholder || `请选择${field.label}`"
              :clearable="field.clearable !== false"
              :disabled="field.disabled"
              :readonly="field.readonly"
              :editable="field.editable !== false"
              :format="field.format"
              :value-format="field.valueFormat"
              :start-placeholder="(field.startPlaceholder as any)"
              :end-placeholder="(field.endPlaceholder as any)"
              :range-separator="field.rangeSeparator || '至'"
              :shortcuts="field.shortcuts"
              :disabled-date="field.disabledDate"
              @change="field.onChange && field.onChange($event)"
            />
            
            <!-- 数字输入 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="formData[field.prop]"
              :placeholder="field.placeholder"
              :disabled="field.disabled"
              :min="field.min"
              :max="field.max"
              :step="field.step"
              :step-strictly="field.stepStrictly"
              :precision="field.precision"
              :controls="field.controls !== false"
              :controls-position="(field.controlsPosition as any)"
              @change="field.onChange && field.onChange($event)"
            />
            
            <!-- 开关 -->
            <el-switch
              v-else-if="field.type === 'switch'"
              v-model="formData[field.prop]"
              :disabled="field.disabled"
              :width="field.width"
              :active-text="field.activeText"
              :inactive-text="field.inactiveText"
              :active-value="field.activeValue"
              :inactive-value="field.inactiveValue"
              :active-color="field.activeColor"
              :inactive-color="field.inactiveColor"
              @change="field.onChange && field.onChange($event)"
            />
            
            <!-- 单选框组 -->
            <el-radio-group
              v-else-if="field.type === 'radio'"
              v-model="formData[field.prop]"
              :disabled="field.disabled"
              :size="field.size"
              @change="field.onChange && field.onChange($event)"
            >
              <el-radio
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            
            <!-- 复选框组 -->
            <el-checkbox-group
              v-else-if="field.type === 'checkbox'"
              v-model="formData[field.prop]"
              :disabled="field.disabled"
              :min="field.min"
              :max="field.max"
              @change="field.onChange && field.onChange($event)"
            >
              <el-checkbox
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
            
            <!-- 文件上传 -->
            <el-upload
              v-else-if="field.type === 'upload'"
              :action="field.action"
              :headers="field.headers"
              :data="field.data"
              :name="field.name || 'file'"
              :with-credentials="field.withCredentials"
              :multiple="field.multiple"
              :accept="field.accept"
              :auto-upload="field.autoUpload !== false"
              :list-type="field.listType || 'text'"
              :drag="field.drag"
              :disabled="field.disabled"
              :limit="field.limit"
              :file-list="formData[field.prop] || []"
              :before-upload="field.beforeUpload"
              :on-success="(response, file, fileList) => handleUploadSuccess(field.prop, response, file, fileList, field)"
              :on-error="(error, file, fileList) => handleUploadError(field.prop, error, file, fileList, field)"
              :on-remove="(file, fileList) => handleUploadRemove(field.prop, file, fileList, field)"
              :on-exceed="field.onExceed"
            >
              <template v-if="field.listType === 'picture-card'">
                <el-icon><Plus /></el-icon>
              </template>
              <template v-else-if="field.drag">
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
              </template>
              <template v-else>
                <el-button type="primary">{{ field.uploadText || '选择文件' }}</el-button>
              </template>
            </el-upload>
            
            <!-- 自定义插槽 -->
            <slot
              v-else-if="field.type === 'slot'"
              :name="field.slot"
              :field="field"
              :value="formData[field.prop]"
              :setValue="(val: any) => (formData[field.prop] = val)"
            />
            
            <!-- 提示信息 -->
            <div v-if="field.tip" class="field-tip">
              {{ field.tip }}
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="submitting">
          {{ cancelText }}
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ submitText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Plus, UploadFilled } from '@element-plus/icons-vue'
import { ElForm, ElMessage } from 'element-plus'
import { computed, ref } from 'vue'

// 字段配置接口
interface FormField {
  prop: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'cascader' | 'date' | 'number' | 'switch' | 'radio' | 'checkbox' | 'upload' | 'slot'
  placeholder?: string
  clearable?: boolean
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  rules?: any[]
  class?: string
  tip?: string
  
  // input/textarea 特有属性
  inputType?: string
  maxlength?: number
  minlength?: number
  showWordLimit?: boolean
  prefixIcon?: any
  suffixIcon?: any
  rows?: number
  autosize?: boolean | object
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'
  
  // select 特有属性
  options?: { label: string; value: any; disabled?: boolean }[]
  multiple?: boolean
  filterable?: boolean
  allowCreate?: boolean
  remote?: boolean
  remoteMethod?: (query: string) => void
  loading?: boolean
  multipleLimit?: number
  onVisibleChange?: (visible: boolean) => void
  
  // cascader 特有属性
  showAllLevels?: boolean
  collapseTags?: boolean
  separator?: string
  cascaderProps?: any
  
  // date 特有属性
  dateType?: 'year' | 'month' | 'date' | 'dates' | 'datetime' | 'week' | 'datetimerange' | 'daterange' | 'monthrange'
  format?: string
  valueFormat?: string
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  shortcuts?: any[]
  disabledDate?: (date: Date) => boolean
  editable?: boolean
  
  // number 特有属性
  min?: number
  max?: number
  step?: number
  stepStrictly?: boolean
  precision?: number
  controls?: boolean
  controlsPosition?: string
  
  // switch 特有属性
  width?: number
  activeText?: string
  inactiveText?: string
  activeValue?: any
  inactiveValue?: any
  activeColor?: string
  inactiveColor?: string
  
  // radio/checkbox 特有属性
  size?: 'large' | 'default' | 'small'
  
  // upload 特有属性
  action?: string
  headers?: object
  data?: object
  name?: string
  withCredentials?: boolean
  accept?: string
  autoUpload?: boolean
  listType?: 'text' | 'picture' | 'picture-card'
  drag?: boolean
  limit?: number
  beforeUpload?: (file: File) => boolean | Promise<boolean>
  onSuccess?: (response: any, file: any, fileList: any[]) => void
  onError?: (error: any, file: any, fileList: any[]) => void
  onRemove?: (file: any, fileList: any[]) => void
  onExceed?: (files: File[], fileList: any[]) => void
  uploadText?: string
  
  // slot 特有属性
  slot?: string
  
  // 事件
  onChange?: (value: any) => void
  onBlur?: (event: Event) => void
  onFocus?: (event: Event) => void
}

interface Props {
  modelValue: boolean
  title?: string
  width?: string | number
  fullscreen?: boolean
  top?: string
  modal?: boolean
  modalClass?: string
  appendToBody?: boolean
  lockScroll?: boolean
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  destroyOnClose?: boolean
  dialogClass?: string
  
  // 表单配置
  fields: FormField[]
  formData: Record<string, any>
  rules?: Record<string, any>
  labelWidth?: string | number
  labelPosition?: 'left' | 'right' | 'top'
  size?: 'large' | 'default' | 'small'
  
  // 按钮配置
  submitText?: string
  cancelText?: string
  submitting?: boolean
  
  // 回调
  beforeClose?: (done: () => void) => void
}

const props = withDefaults(defineProps<Props>(), {
  title: '新增',
  width: '600px',
  fullscreen: false,
  top: '15vh',
  modal: true,
  appendToBody: false,
  lockScroll: true,
  closeOnClickModal: false,
  closeOnPressEscape: true,
  showClose: true,
  destroyOnClose: false,
  labelWidth: '120px',
  labelPosition: 'right',
  size: 'default',
  submitText: '确定',
  cancelText: '取消',
  submitting: false
})

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: Record<string, any>): void
  (e: 'cancel'): void
  (e: 'open'): void
  (e: 'opened'): void
  (e: 'close'): void
  (e: 'closed'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<InstanceType<typeof ElForm>>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 事件处理
const handleBeforeClose = (done: () => void) => {
  if (props.beforeClose) {
    props.beforeClose(done)
  } else {
    done()
  }
}

const handleOpen = () => {
  emit('open')
}

const handleOpened = () => {
  emit('opened')
}

const handleClose = () => {
  emit('close')
}

const handleClosed = () => {
  emit('closed')
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', { ...props.formData })
  } catch (error) {
    console.warn('表单验证失败:', error)
    ElMessage.warning('请检查表单输入')
  }
}

const handleCancel = () => {
  visible.value = false
  emit('cancel')
}

// 文件上传处理
const handleUploadSuccess = (prop: string, response: any, file: any, fileList: any[], field: FormField) => {
  props.formData[prop] = fileList
  field.onSuccess && field.onSuccess(response, file, fileList)
}

const handleUploadError = (prop: string, error: any, file: any, fileList: any[], field: FormField) => {
  console.error('文件上传失败:', error)
  ElMessage.error('文件上传失败')
  field.onError && field.onError(error, file, fileList)
}

const handleUploadRemove = (prop: string, file: any, fileList: any[], field: FormField) => {
  props.formData[prop] = fileList
  field.onRemove && field.onRemove(file, fileList)
}

// 公开方法
const validate = () => {
  return formRef.value?.validate()
}

const clearValidate = (props?: string | string[]) => {
  formRef.value?.clearValidate(props)
}

const resetFields = () => {
  formRef.value?.resetFields()
}

defineExpose({
  validate,
  clearValidate,
  resetFields,
  formRef
})
</script>

<style scoped lang="scss">
@import '../styles/dialog.scss';

.dialog-content {
  max-height: 60vh;
  overflow-y: auto;
  
  .el-form {
    .el-form-item {
      margin-bottom: 20px;
      
      .field-tip {
        margin-top: 4px;
        font-size: 12px;
        color: #909399;
        line-height: 1.4;
      }
      
      .el-upload {
        &.el-upload--picture-card {
          width: 100px;
          height: 100px;
          
          .el-icon {
            font-size: 28px;
            color: #8c939d;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button + .el-button {
    margin-left: 12px;
  }
}
</style> 