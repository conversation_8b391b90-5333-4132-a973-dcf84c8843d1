package users

import (
	"context"
	"fmt"
	"frontapi/internal/models/users"
	repo "frontapi/internal/repository/users"
	"frontapi/internal/service/base"
	"time"
)

// UserFollowsService 用户关注服务接口
type UserFollowsService interface {
	base.IExtendedService[users.UserFollows]
	IsFollowing(ctx context.Context, userID string, followID string) (bool, error)
	GetUserFollow(ctx context.Context, userID, followID string) (*users.User, error)
	GetMutualFollows(ctx context.Context, userID, targetID string) ([]*users.UserFollows, error)
	GetFollowersGrowthStats(ctx context.Context, userID, timeRange, startTime, endTime string) ([]*users.FollowersGrowthStat, error)
}

// userFollowsService 用户关注服务实现
type userFollowsService struct {
	*base.ExtendedService[users.UserFollows]
	followsRepo repo.UserFollowsRepository
	userRepo    repo.UserRepository
}

// NewUserFollowsService 创建用户关注服务实例
func NewUserFollowsService(
	followsRepo repo.UserFollowsRepository,
	userRepo repo.UserRepository,
) UserFollowsService {
	return &userFollowsService{
		ExtendedService: base.NewExtendedService[users.UserFollows](followsRepo, "user_follows"),
		followsRepo:     followsRepo,
		userRepo:        userRepo,
	}
}
func (s *userFollowsService) IsFollowing(ctx context.Context, userID string, followID string) (bool, error) {
	return s.followsRepo.Exists(ctx, map[string]interface{}{
		"user_id":   userID,
		"follow_id": followID,
	})
}

// GetUserFollow 获取用户关注
func (s *userFollowsService) GetUserFollow(ctx context.Context, userID, followID string) (*users.User, error) {
	return s.followsRepo.GetUserFollow(ctx, userID, followID)
}

// GetMutualFollows 获取互相关注的用户
func (s *userFollowsService) GetMutualFollows(ctx context.Context, userID, targetID string) ([]*users.UserFollows, error) {
	// 获取用户的关注列表
	userFollows, _ := s.followsRepo.FindAll(ctx, map[string]interface{}{
		"user_id": userID,
	}, "is_mutual=1,created_at DESC")
	// 获取目标用户的关注列表
	targetFollows, _ := s.followsRepo.FindAll(ctx, map[string]interface{}{
		"user_id": targetID,
	}, "is_mutual=1,created_at DESC")

	// 找出互相关注的用户
	var mutualFollows []*users.UserFollows
	for i, userFollow := range userFollows {
		for _, targetFollow := range targetFollows {
			if userFollow.FollowedID == targetID && targetFollow.FollowedID == userID {
				mutualFollows = append(mutualFollows, userFollows[i])
				break
			}
		}
	}

	return mutualFollows, nil
}

// GetFollowGrowth 统计用户粉丝数据
func (s *userFollowsService) GetFollowersGrowthStats(ctx context.Context, userID, timeRange, startTime, endTime string) ([]*users.FollowersGrowthStat, error) {

	// 获取指定时间范围内的粉丝数据
	startAt, err := time.Parse("2006-01-02", startTime)
	if err != nil {
		return nil, err
	}
	endAt, err := time.Parse("2006-01-02", endTime)
	if err != nil {
		return nil, err
	}

	// 检查时间范围是否有效
	if startAt.After(endAt) {
		return nil, fmt.Errorf("开始时间不能大于结束时间")
	}

	return s.followsRepo.GetFollowersGrowthStats(ctx, userID, timeRange, startAt, endAt)
}
