/**
 * 主题工具函数
 */
import { ThemeConfig } from '@/config/theme.config';

/**
 * 获取主题的CSS变量名
 */
export function getThemeCssVarName(theme: string): string {
    return `theme-${theme.replace(/\s+/g, '-').toLowerCase()}`;
}

/**
 * 获取主题的完整名称
 */
export function getFullThemeName(themeFamily: string, themeStyle: string, themeMode: string): string {
    return `${themeFamily}-${themeStyle}-${themeMode}`;
}

/**
 * 获取主题的短名称
 */
export function getShortThemeName(theme: ThemeConfig): string {
    return theme.name;
}

/**
 * 将十六进制颜色转换为RGB
 */
export function hexToRgb(hex: string): string {
    // 移除#号
    hex = hex.replace('#', '');

    // 解析RGB值
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
}
//rgb 转十六进制
export function rgbToHex(rgb: string): string {
    const [r, g, b] = rgb.split(',').map(Number);
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}