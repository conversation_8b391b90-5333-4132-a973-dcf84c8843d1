package books

import (
	"frontapi/internal/admin"
	"frontapi/internal/models"
	bookModel "frontapi/internal/models/books"
	bookSrv "frontapi/internal/service/books"
	bookValidator "frontapi/internal/validation/books"
	"frontapi/pkg/types"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// BookController 电子书控制器
type BookController struct {
	BookService          bookSrv.BookService
	BookCategoryService  bookSrv.BookCategoryService
	BookChapterService   bookSrv.BookChapterService
	admin.BaseController // 继承BaseController
}

// NewBookController 创建电子书控制器实例
func NewBookController(
	bookService bookSrv.BookService,
	bookCategoryService bookSrv.BookCategoryService,
	bookChapterService bookSrv.BookChapterService,
) *BookController {
	return &BookController{
		BookService:         bookService,
		BookCategoryService: bookCategoryService,
		BookChapterService:  bookChapterService,
	}
}

// ListBooks 获取电子书列表
func (c *BookController) ListBooks(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	title := reqInfo.Get("title").GetString()
	author := reqInfo.Get("author").GetString()
	categoryID := reqInfo.Get("category_id").GetString()

	isFeatured := -999
	status := -999
	_ = c.GetIntegerValueWithDataWrapper(ctx, "is_featured", &isFeatured)
	_ = c.GetIntegerValueWithDataWrapper(ctx, "status", &status)
	keyword := reqInfo.Get("keyword").GetString()
	orderBy := reqInfo.Get("orderBy").GetString()
	if orderBy == "" {
		orderBy = "updated_at DESC"
	}

	// 查询电子书列表
	bookList, total, err := c.BookService.List(ctx.Context(), map[string]interface{}{
		"title":       title,
		"author":      author,
		"category_id": categoryID,
		"status":      status,
		"is_featured": isFeatured,
		"keyword":     keyword,
	}, orderBy, page, pageSize, true)

	if err != nil {
		return c.InternalServerError(ctx, "获取电子书列表失败: "+err.Error())
	}

	// 返回电子书列表
	return c.SuccessList(ctx, bookList, total, page, pageSize)
}

// GetBook 获取电子书详情
func (c *BookController) GetBook(ctx *fiber.Ctx) error {
	// 获取电子书ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询电子书
	book, err := c.BookService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取电子书详情失败: "+err.Error())
	}

	if book == nil {
		return c.NotFound(ctx, "电子书不存在")
	}

	// 返回电子书详情
	return c.Success(ctx, book)
}

// CreateBook 创建电子书
func (c *BookController) CreateBook(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.BookCreateRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 创建电子书
	book := &bookModel.Book{
		ContentBaseModel: &models.ContentBaseModel{},
	}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, book); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 手动处理需要特殊转换的字段
	book.Tags = types.StringArray(req.Tags)

	id, err := c.BookService.Create(ctx.Context(), book)
	if err != nil {
		return c.InternalServerError(ctx, "创建电子书失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建电子书成功",
	})
}

// UpdateBook 更新电子书
func (c *BookController) UpdateBook(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.BookUpdateRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新电子书
	book := &bookModel.Book{
		ContentBaseModel: &models.ContentBaseModel{},
	}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, book); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	// 手动处理需要特殊转换的字段
	if req.Tags != nil {
		book.Tags = types.StringArray(req.Tags)
	}
	err := c.BookService.Update(ctx.Context(), book)
	if err != nil {
		return c.InternalServerError(ctx, "更新电子书失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新电子书成功")
}

// UpdateBookStatus 更新电子书状态
func (c *BookController) UpdateBookStatus(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req bookValidator.BookUpdateStatusRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数", nil)
	}

	book := &bookModel.Book{}

	// 使用通用的Update方法
	err := c.BookService.Update(ctx.Context(), book)
	if err != nil {
		return c.InternalServerError(ctx, "更新电子书状态失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新电子书状态成功")
}

// DeleteBook 删除电子书
func (c *BookController) DeleteBook(ctx *fiber.Ctx) error {
	// 获取电子书ID
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除电子书
	err = c.BookService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除电子书失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除电子书成功")
}
