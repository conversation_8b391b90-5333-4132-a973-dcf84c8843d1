<template>
  <el-dialog
    :title="book && book.id ? $t('books.dialog.edit') : $t('books.dialog.add')"
    :model-value="visible"
    width="80vw"
    class="book-dialog"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="right"
      :status-icon="true"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <!-- 基本信息 -->
          <el-form-item :label="$t('books.info.title')" prop="title">
            <el-input v-model="form.title" :placeholder="$t('books.info.enterTitle')" />
          </el-form-item>

          <el-form-item :label="$t('books.info.author')" prop="author">
            <el-input v-model="form.author" :placeholder="$t('books.info.enterAuthor')" />
          </el-form-item>

          <el-form-item :label="$t('books.info.category')" prop="category_id">
            <el-select
              v-model="form.category_id"
              :placeholder="$t('books.info.selectCategory')"
              style="width: 100%"
            >
              <el-option
                v-for="option in categoryOptions"
                :key="option.id"
                :label="option.name"
                :value="option.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('books.info.description')" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="5"
              :placeholder="$t('books.info.enterDescription')"
            />
          </el-form-item>

          <el-form-item :label="$t('books.info.progress')" prop="progress">
            <el-select v-model="form.progress" style="width: 100%">
              <el-option label="连载中" value="serializing" />
              <el-option label="已完结" value="completed" />
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('books.info.tags')" prop="tags" class="form-tags">
            <el-tag
              v-for="tag in form.tags"
              :key="tag"
              closable
              @close="handleRemoveTag(tag)"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <el-input
              v-if="inputTagVisible"
              ref="tagInputRef"
              v-model="tagInputValue"
              class="tag-input"
              size="small"
              @keyup.enter="handleAddTag"
              @blur="handleAddTag"
            />
        <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
      </el-form-item>
        </el-col>

        <el-col :span="12">
          <!-- 封面和设置 -->
          <el-form-item :label="$t('books.info.cover')" prop="cover">
            <url-or-file-input
              v-model="form.cover"
              placeholder="请选择或输入封面图片URL"
              file-type="pictures"
              sub-dir="books/covers"
              @change="handleCoverChange"
            />
            <div v-if="form.cover" class="cover-preview mt-2">
              <img :src="form.cover" alt="封面预览" />
            </div>
            <div class="cover-tip">{{ $t('books.info.coverTip') }}</div>
          </el-form-item>

          <el-form-item :label="$t('books.info.wordCount')" prop="word_count">
            <el-input-number
              v-model="form.word_count"
              :min="0"
              :precision="0"
              :step="1000"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item :label="$t('books.info.isPaid')" prop="is_paid">
            <el-switch
              v-model="form.is_paid"
              :active-value="1"
              :inactive-value="0"
              inline-prompt
              :active-text="$t('books.info.yes')"
              :inactive-text="$t('books.info.no')"
            />
          </el-form-item>

          <el-form-item v-if="form.is_paid === 1" :label="$t('books.info.price')" prop="price">
            <el-input-number
              v-model="form.price"
              :min="0"
              :precision="2"
              :step="0.1"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item :label="$t('books.info.isFeatured')" prop="is_featured">
            <el-switch
              v-model="form.is_featured"
              :active-value="1"
              :inactive-value="0"
              inline-prompt
              :active-text="$t('books.info.yes')"
              :inactive-text="$t('books.info.no')"
            />
          </el-form-item>
          <el-form-item :label="$t('books.info.status')" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :value="1" :label="$t('books.info.yes')" />
              <el-radio :value="0" :label="$t('books.info.no')" />
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="$t('books.info.publishDate')" prop="publish_date">
            <el-date-picker
              v-model="form.publish_date"
              type="datetime"
              style="width: 100%"
              :placeholder="$t('books.info.selectPublishDate')"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ $t('buttons.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ $t('buttons.confirm') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted,nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getAllBookCategories } from '@/service/api/books/category';
import type { BookItem, BookCategory } from '@/types/books';
import { formatDate } from '@/utils/date';
import UrlOrFileInput from '@/components/filemanager/UrlOrFileInput.vue';

// 定义接收的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  book: {
    type: Object as () => BookItem,
    default: () => ({})
  },
  categoryOptions: {
    type: Array as () => BookCategory[],
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['close', 'submit']);

// 表单引用
const formRef = ref(null);

// 提交状态
const submitting = ref(false);

// 分类选项
// 表单对象
const form = reactive({
  id: '',
  title: '',
  author: '',
  cover: '',
  description: '',
  category_id: '',
  category_name: '',
  progress: 'serializing',
  status:1,
  tags: [] as string[],
  tags_json: '',
  word_count: 0,
  is_paid: 0,
  is_featured: 0,
  price: 0.00,
  creator_id: '',
  creator_name: '',
  creator_avatar: '',
  publish_date: null as Date | null
});

// 表单校验规则
const rules = reactive({
  title: [
    { required: true, message: '请输入电子书标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度应在2-100个字符之间', trigger: 'blur' }
  ],
  author: [
    { required: true, message: '请输入作者名称', trigger: 'blur' }
  ],
  cover: [
    { required: true, message: '请上传封面图片', trigger: 'change' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' }
  ],
  progress: [
    { required: true, message: '请选择进度', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  price: [
    {
      validator: (rule, value, callback) => {
        if (form.is_paid === 1 && (!value || value <= 0)) {
          callback(new Error('付费电子书必须设置价格'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
});

// 标签输入
const tagInputRef = ref();
const inputTagVisible = ref(false);
const tagInputValue = ref('');

// 显示标签输入框
const showTagInput = () => {
  inputTagVisible.value = true;
  nextTick(() => {
    tagInputRef.value?.focus();
  });
};

// 添加标签
const handleAddTag = () => {
  if (tagInputValue.value) {
    if (!form.tags) {
      form.tags = [];
    }
    if (!form.tags.includes(tagInputValue.value)) {
      form.tags.push(tagInputValue.value);
    }
  }
  inputTagVisible.value = false;
  tagInputValue.value = '';
};

// 删除标签
const handleRemoveTag = (tag: string) => {
  // 确保tags是数组
  if (!Array.isArray(form.tags)) {
    form.tags = [];
  }
  // 使用splice方法删除标签
  const index = form.tags.indexOf(tag);
  if (index > -1) {
    form.tags.splice(index, 1);
  }
  // 强制更新视图
  nextTick(() => {
    formRef.value?.validateField('tags');
  });
};


// 处理封面变更
const handleCoverChange = (url: string) => {
  form.cover = url;
};

// 处理关闭
const handleClose = () => {
  emit('close');
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid) => {
    if (!valid) {
      return false;
    }

    submitting.value = true;
    try {
      // 处理标签
      if (form.tags && form.tags.length > 0) {
        form.tags_json = JSON.stringify(form.tags);
      }

      // 转换时间
      if (form.publish_date) {
        form.publish_date = formatDate(form.publish_date);
      }
      form.category_name = props.categoryOptions.find(item => item.id === form.category_id)?.name;
      // 提交表单
      emit('submit', { ...form });
    } catch (error) {
      console.error('表单提交错误:', error);
      ElMessage.error('表单提交错误');
    } finally {
      submitting.value = false;
    }
  });
};

// 监听书籍数据变化
watch(() => props.book, (val) => {
  if (val && val.id) {
    // 编辑模式，填充表单
    Object.keys(form).forEach(key => {
      if (val[key] !== undefined) {
        form[key] = val[key];
      }
    });

    // 处理标签
    if (val.tags) {
      console.log("tags:",val.tags);
      try {
        form.tags = JSON.parse(val.tags);
      } catch (e) {
        form.tags = val.tags ? val.tags : [];
      }
    } else {
      form.tags = [];
    }
  } else {
    // 新增模式，重置表单
    Object.keys(form).forEach(key => {
      if (key === 'progress') {
        form[key] = 'serializing';
      } else if (key === 'is_paid' || key === 'is_featured') {
        form[key] = 0;
      } else if (key === 'word_count') {
        form[key] = 0;
      } else if (key === 'price') {
        form[key] = 0.00;
      } else if (key === 'tags') {
        form[key] = [];
      } else if (key === 'publish_date') {
        form[key] = null;
      } else if(key === 'status'){
        form[key] = 1;
      }else {
        form[key] = '';
      }
    });
  }
}, { deep: true, immediate: true });

// 在组件挂载时获取分类列表
onMounted(() => {

});
</script>

<style  lang="scss">
.form-tags {
  .tag-item {
    margin-right: 5px;
  }
  .el-form-item__content{
    display: flex;
    gap:5px;
    flex-wrap: wrap;
  }
  .tag-input{
    width: 150px;
  }
}
.book-dialog{
  height: 80vh;
  min-height: 600px;
  overflow: hidden;
}
.cover-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  margin-left: 10px;
  text-align: center;
}

.cover-preview {
  width: 150px;
  height: 225px;
  margin-top: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
}

.cover-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mt-2 {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
