package posts

import (
	"context"

	"gorm.io/gorm"

	"frontapi/internal/models/posts"
	"frontapi/internal/repository/base"
)

// LikeRepository 帖子点赞数据访问接口
type PostLikeRepository interface {
	base.ExtendedRepository[posts.PostLike]
	CheckUserLiked(ctx context.Context, userID, postID string) (bool, error)
}

// likeRepository 帖子点赞数据访问实现
type likeRepository struct {
	base.ExtendedRepository[posts.PostLike]
}

// NewPostLikeRepository 创建帖子点赞仓库实例
func NewPostLikeRepository(db *gorm.DB) PostLikeRepository {
	return &likeRepository{
		ExtendedRepository: base.NewExtendedRepository[posts.PostLike](db),
	}
}

// CheckUserLiked 检查用户是否点赞帖子
func (r likeRepository) CheckUserLiked(ctx context.Context, userID, postID string) (bool, error) {
	var count int64
	err := r.GetDB().WithContext(ctx).
		Model(&posts.PostLike{}).
		Where("user_id = ? AND post_id = ?", userID, postID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
