<template>
  <div class="login-log-table-container">
    <!-- 批量操作工具栏 -->
    <div v-if="selectedRows.length > 0" class="batch-toolbar">
      <div class="batch-info">
        <el-icon><Check /></el-icon>
        <span>已选择 <strong>{{ selectedRows.length }}</strong> 项</span>
      </div>
      <div class="batch-actions">
        <el-button type="danger" size="small" @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 主表格 -->
    <div class="table-content">
      <SlinkyTable
        :data="loginLogList"
        :loading="loading"
        show-selection
        :show-table-header="true"
        show-index
        show-actions
        :border="true"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @view="handleView"
        @delete="handleDelete"
        action-width="160"
        view-text="详情"
        delete-text="删除"
        empty-text="暂无登录日志数据"
        class="login-log-data-table"
      >
        <!-- 用户信息列 -->
        <el-table-column label="用户信息" align="left" min-width="160" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="user-info">
              <div class="user-details">
                <div class="user-id">ID: {{ row.user_id }}</div>
                <div class="username">{{ row.username || '未知用户' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 登录类型列 -->
        <el-table-column prop="type" label="登录类型" width="90" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.type === 0 ? 'success' : row.type === 1 ? 'info' : 'warning'" 
              size="small"
              effect="light"
            >
              <el-icon>
                <component :is="row.type === 0 ? 'Login' : row.type === 1 ? 'SwitchButton' : 'Warning'" />
              </el-icon>
              {{ row.type === 0 ? '登录' : row.type === 1 ? '退出' : '未知' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 认证方式列 -->
        <el-table-column prop="login_type" label="认证方式" width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="auth-type">{{ row.login_type || '未知' }}</span>
          </template>
        </el-table-column>

        <!-- 登录时间列 -->
        <el-table-column prop="login_time" label="登录时间" width="180" align="center">
          <template #default="{ row }">
            <div class="time-info">
              <el-icon class="time-icon"><Calendar /></el-icon>
              <span class="time-text">{{ row.login_time }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 网络信息列 -->
        <el-table-column label="网络信息" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="network-info">
              <div class="network-item">
                <el-icon class="network-icon"><Monitor /></el-icon>
                <span class="network-text">{{ row.ip_address }}</span>
              </div>
              <div class="network-item" v-if="row.location">
                <el-icon class="network-icon"><Location /></el-icon>
                <span class="network-text">{{ row.location }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 设备信息列 -->
        <el-table-column label="设备信息" min-width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="device-info">
              <div class="device-item">
                <span class="device-type">{{ row.device_type }}</span>
              </div>
              <div class="device-item">
                <span class="os-info">{{ row.os_name }} {{ row.os_version }}</span>
              </div>
              <div class="device-item">
                <span class="browser-info">{{ row.browser_name }} {{ row.browser_version }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 登录状态列 -->
        <el-table-column prop="login_status" label="登录状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.login_status === 'success' ? 'success' : row.login_status === 'failure' ? 'danger' : 'info'" 
              size="small"
              effect="light"
            >
              <el-icon>
                <component :is="row.login_status === 'success' ? 'SuccessFilled' : row.login_status === 'failure' ? 'CircleCloseFilled' : 'InfoFilled'" />
              </el-icon>
              {{ row.login_status === 'success' ? '成功' : row.login_status === 'failure' ? '失败' : '未知' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 自定义操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons-group" style="min-width: 160px;">
            <el-button type="primary" link size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-popconfirm
              :title="`确定要删除此登录日志吗？`"
              @confirm="handleDelete(row)"
            >
              <template #reference>
                <el-button type="danger" link size="small">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </SlinkyTable>
    </div>

    <!-- 分页器 -->
    <div class="table-footer">
        <SinglePager
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.pageSize"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :background="true"
        show-jump-info
        class="pagination-wrapper"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SinglePager from '@/components/themes/slinky/pager/SinglePager.vue';
import SlinkyTable from '@/components/themes/slinky/tables/SlinkyTable.vue';
import type { UserLoginLog } from '@/types/users';
import { Calendar, Check, Delete, Location, Monitor, View } from '@element-plus/icons-vue';
import { ref } from 'vue';

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未知';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// Props类型定义
interface Props {
  loginLogList: UserLoginLog[];
  loading: boolean;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

const props = defineProps<Props>();

// Emits类型定义
interface Emits {
  'selection-change': [selection: UserLoginLog[]];
  'view': [row: UserLoginLog];
  'delete': [row: UserLoginLog];
  'current-change': [page: number];
  'size-change': [size: number];
  'batch-delete': [loginLogs: UserLoginLog[]];
}

const emit = defineEmits<Emits>();

// 选中的行
const selectedRows = ref<UserLoginLog[]>([]);

// 表格选择变化
const handleSelectionChange = (selection: UserLoginLog[]) => {
  selectedRows.value = selection;
  emit('selection-change', selection);
};

// 查看详情
const handleView = (row: UserLoginLog) => {
  emit('view', row);
};

// 删除
const handleDelete = (row: UserLoginLog) => {
  emit('delete', row);
};

// 分页变化
const handleCurrentChange = (page: number) => {
  emit('current-change', page);
};

const handleSizeChange = (size: number) => {
  emit('size-change', size);
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length > 0) {
    emit('batch-delete', selectedRows.value);
  }
};
</script>

<style scoped>
.login-log-table-container {
  background: white;
}

.batch-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  margin-bottom: 16px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #0284c7;
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 表格内容样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

.username {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.time-info, .network-info, .device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-icon, .network-icon {
  color: #409eff;
  margin-right: 4px;
}

.time-text, .network-text {
  font-size: 13px;
  color: #303133;
}

.network-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.device-item {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.device-type {
  font-weight: 500;
  color: #303133;
}

.auth-type {
  font-size: 13px;
  color: #303133;
}

.action-buttons-group {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
}
/* 分页器样式 */
.table-footer {
  padding: 16px 24px;
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color-lighter);
}


/* 响应式设计 */
@media (max-width: 768px) {
  .batch-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
  }
  
  .batch-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .user-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .action-buttons-group {
    flex-direction: column;
    gap: 4px;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table .el-table__cell) {
    padding: 8px 4px;
  }
}
</style> 