package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserVideoAlbumCollectionRepository 用户视频专辑收藏数据访问接口
type UserVideoAlbumCollectionRepository interface {
	base.ExtendedRepository[users.UserVideoAlbumCollection]
}

// userVideoAlbumCollectionRepository 用户视频专辑收藏数据访问实现
type userVideoAlbumCollectionRepository struct {
	base.ExtendedRepository[users.UserVideoAlbumCollection]
}

// NewUserVideoAlbumCollectionRepository 创建用户视频专辑收藏仓库实例
func NewUserVideoAlbumCollectionRepository(db *gorm.DB) UserVideoAlbumCollectionRepository {
	return &userVideoAlbumCollectionRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserVideoAlbumCollection](db),
	}
}
