<template>
    <header class="bg-surface-0 dark:bg-surface-500 border-b border-surface-200 dark:border-surface-700 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- 左侧Logo和网站名称 -->
                <div class="flex items-center">
                    <!-- 移动端菜单按钮 -->
                    <Button
                        icon="pi pi-bars"
                        class="p-button-text p-button-rounded md:hidden mr-2"
                        @click="toggleMobileMenu"
                        aria-label="Menu"
                    />
                    <!-- Logo -->
                    <router-link to="/" class="flex items-center">
                        <div class="flex-shrink-0 flex items-center">
                            <svg class="h-8 w-8" viewBox="0 0 32 32" fill="none">
                                <defs>
                                    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" stop-color="var(--primary-500)" />
                                        <stop offset="100%" stop-color="var(--primary-700)" />
                                    </linearGradient>
                                </defs>
                                <circle cx="16" cy="16" r="14" fill="url(#logoGradient)" />
                                <path d="M12 10l8 6-8 6V10z" fill="white" />
                            </svg>
                            <span class="ml-2 text-lg font-semibold text-surface-900 dark:text-surface-0">
                                {{ t('common.siteName') }}
                            </span>
                        </div>
                    </router-link>
                </div>
                
                <!-- 中间搜索框 -->
                <div class="hidden md:flex flex-1 justify-center px-8">
                    <div class="w-full max-w-lg">
                        <div class="relative">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="pi pi-search text-surface-400"></i>
                            </span>
                            <InputText
                                v-model="searchQuery"
                                class="w-full pl-10 pr-4 py-2 bg-surface-50 dark:bg-surface-800 border border-surface-200 dark:border-surface-700 rounded-full"
                                :placeholder="t('common.search')"
                                @keyup.enter="performSearch"
                            />
                        </div>
                    </div>
                </div>
                
                <!-- 右侧工具栏 -->
                <div class="flex items-center gap-1 sm:gap-2">
                    <!-- 移动端搜索按钮 -->
                    <Button
                        icon="pi pi-search"
                        class="p-button-text p-button-rounded md:hidden"
                        @click="toggleMobileSearch"
                        aria-label="Search"
                    />
                    
                    <!-- 主题选择器 -->
                    <NavThemeSelector />
                    
                    <!-- 语言选择器 -->
                    <NavLanguageSelector />
                    
                    <!-- 用户菜单 -->
                    <div class="relative ml-1">
                        <Button
                            class="p-button-text p-button-rounded flex items-center"
                            @click="toggleUserMenu"
                            aria-label="User menu"
                        >
                            <template #icon>
                                <div class="w-8 h-8 rounded-full bg-surface-200 dark:bg-surface-700 flex items-center justify-center overflow-hidden">
                                    <img v-if="userAvatar" :src="userAvatar" alt="User avatar" class="w-full h-full object-cover" />
                                    <i v-else class="pi pi-user text-surface-600 dark:text-surface-200"></i>
                                </div>
                            </template>
                        </Button>
                        
                        <!-- 用户菜单下拉 -->
                        <Menu ref="userMenuRef" :model="userMenuItems" :popup="true" />
                    </div>
                </div>
            </div>
            
            <!-- 移动端搜索栏 -->
            <div v-if="showMobileSearch" class="md:hidden py-3 border-t border-surface-200 dark:border-surface-700">
                <div class="relative">
                    <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <i class="pi pi-search text-surface-400"></i>
                    </span>
                    <InputText
                        v-model="searchQuery"
                        class="w-full pl-10 pr-4 py-2 bg-surface-50 dark:bg-surface-800 border border-surface-200 dark:border-surface-700 rounded-full"
                        :placeholder="t('common.search')"
                        @keyup.enter="performSearch"
                    />
                </div>
            </div>
        </div>
    </header>
</template>

<script setup lang="ts">
import NavLanguageSelector from '@/shared/components/LanguageSelector/NavLanguageSelector.vue';
import NavThemeSelector from '@/shared/components/ThemeSelector/NavThemeSelector.vue';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Menu from 'primevue/menu';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

// i18n
const { t } = useI18n();
const router = useRouter();

// 响应式数据
const searchQuery = ref('');
const showMobileSearch = ref(false);
const showMobileMenu = ref(false);
const userMenuRef = ref();
const userAvatar = ref(''); // 可以设置默认头像

// 用户菜单项
const userMenuItems = [
    {
        label: t('user.profile'),
        icon: 'pi pi-user',
        command: () => router.push('/profile')
    },
    {
        label: t('user.settings'),
        icon: 'pi pi-cog',
        command: () => router.push('/settings')
    },
    {
        separator: true
    },
    {
        label: t('user.logout'),
        icon: 'pi pi-sign-out',
        command: () => logout()
    }
];

// 移动端菜单控制
const toggleMobileMenu = () => {
    showMobileMenu.value = !showMobileMenu.value;
    if (showMobileMenu.value) {
        showMobileSearch.value = false;
    }
    
    // 触发自定义事件通知父组件
    window.dispatchEvent(new CustomEvent('mobile-menu-toggle', {
        detail: { isOpen: showMobileMenu.value }
    }));
};

// 移动端搜索控制
const toggleMobileSearch = () => {
    showMobileSearch.value = !showMobileSearch.value;
    if (showMobileSearch.value) {
        showMobileMenu.value = false;
    }
};

// 搜索功能
const performSearch = () => {
    if (searchQuery.value.trim()) {
        router.push({ path: '/search', query: { q: searchQuery.value } });
        showMobileSearch.value = false;
    }
};

// 通知和用户菜单
const toggleNotifications = () => {
    // TODO: 实现通知功能
    console.log('Toggle notifications');
};

const toggleUserMenu = (event: Event) => {
    userMenuRef.value.toggle(event);
};

// 登出功能
const logout = () => {
    // TODO: 实现登出功能
    console.log('Logout');
};
</script> 

<style scoped lang="scss">
.p-button {
    &.p-button-text {
        color: var(--surface-700);
        
        &:hover {
            background-color: var(--surface-100);
        }
        
        .dark & {
            color: var(--surface-200);
            
            &:hover {
                background-color: var(--surface-700);
            }
        }
    }
}
</style> 