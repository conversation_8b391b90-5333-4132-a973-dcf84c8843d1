/**
 * 共享基础类型定义
 */

/**
 * 主题配置
 */
export interface ThemeConfig {
    mode: 'light' | 'dark' | 'auto' | string;
    primaryColor: string;
    [key: string]: any;
}

/**
 * 分页参数
 */
export interface PaginationParams {
    pageNo: number;
    pageSize: number;
    total?: number;
}

/**
 * 分页结果
 */
export interface PaginationResult<T> {
    list: T[];
    total: number;
    pageNo: number;
    pageSize: number;
    hasNext?: boolean;
}

/**
 * 排序方向
 */
export type SortDirection = 'asc' | 'desc';

/**
 * 排序参数
 */
export interface SortParams {
    field: string;
    direction: SortDirection;
}

/**
 * 查询参数
 */
export interface QueryParams {
    [key: string]: any;
    pagination?: PaginationParams;
    sort?: SortParams[];
    filter?: Record<string, any>;
}

/**
 * 基础响应格式
 */
export interface ApiResponse<T = any> {
    code: number;
    data: T;
    message: string;
    success: boolean;
}

/**
 * 基础ID类型
 */
export type ID = string | number;

/**
 * 基础实体接口
 */
export interface BaseEntity {
    id: ID;
    createdAt?: string | Date;
    updatedAt?: string | Date;
}

/**
 * 基础树形节点接口
 */
export interface TreeNode<T = any> {
    id: ID;
    parentId: ID | null;
    children?: TreeNode<T>[];
    [key: string]: any;
}

/**
 * 文件类型
 */
export type FileType = 'image' | 'video' | 'audio' | 'document' | 'other';

/**
 * 文件信息
 */
export interface FileInfo {
    id: ID;
    name: string;
    url: string;
    type: FileType;
    size: number;
    extension?: string;
    mimeType?: string;
    width?: number;
    height?: number;
    duration?: number;
    thumbnail?: string;
    createdAt?: string | Date;
}

/**
 * 地理位置信息
 */
export interface GeoLocation {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    province?: string;
    country?: string;
    postalCode?: string;
}

/**
 * 语言类型
 */
export type LanguageType = 'zh-CN' | 'zh-TW' | 'en' | 'ja' | 'ko' | string;

/**
 * 语言配置
 */
export interface LanguageConfig {
    code: LanguageType;
    name: string;
    flag?: string;
    rtl?: boolean;
}

/**
 * 设备类型
 */
export type DeviceType = 'desktop' | 'tablet' | 'mobile';

/**
 * 设备信息
 */
export interface DeviceInfo {
    type: DeviceType;
    os: string;
    browser: string;
    screenWidth: number;
    screenHeight: number;
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    isIOS: boolean;
    isAndroid: boolean;
    isWindows: boolean;
    isMacOS: boolean;
    isLinux: boolean;
}

/**
 * 时间范围
 */
export interface TimeRange {
    start: Date | string;
    end: Date | string;
}

/**
 * 价格信息
 */
export interface PriceInfo {
    amount: number;
    currency: string;
    discount?: number;
    originalAmount?: number;
}

/**
 * 通用状态类型
 */
export type Status = 'active' | 'inactive' | 'pending' | 'deleted' | 'draft' | 'published' | string;

/**
 * 通用结果类型
 */
export type Result<T = any, E = Error> =
    | { success: true; data: T; error?: never }
    | { success: false; data?: never; error: E };

/**
 * 通用选项类型
 */
export interface Option {
    label: string;
    value: any;
    disabled?: boolean;
    children?: Option[];
    [key: string]: any;
}

/**
 * 通用键值对
 */
export interface KeyValue<T = any> {
    key: string;
    value: T;
}

/**
 * 通用元数据
 */
export interface Metadata {
    [key: string]: any;
}

/**
 * 通用标签
 */
export interface Tag {
    id: ID;
    name: string;
    color?: string;
}

/**
 * 通用颜色类型
 */
export type ColorType = 'primary' | 'success' | 'warning' | 'danger' | 'info' | string;

/**
 * 通用尺寸类型
 */
export type SizeType = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | string;