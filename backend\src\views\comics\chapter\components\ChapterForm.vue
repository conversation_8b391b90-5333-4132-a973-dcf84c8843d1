<template>
  <el-dialog :title="dialogType === 'add' ? '添加章节' : '编辑章节'" v-model="dialogVisible" width="500px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入章节标题"></el-input>
      </el-form-item>
      <el-form-item label="序号" prop="chapter_number">
        <el-input-number v-model="form.chapter_number" :min="1" :max="9999"></el-input-number>
      </el-form-item>
      <el-form-item label="付费" prop="is_free">
        <el-radio-group v-model="form.is_locked">
          <el-radio :label="0">免费</el-radio>
          <el-radio :label="1">付费</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="价格" prop="price" v-if="form.is_free === 1">
        <el-input-number v-model="form.price" :precision="2" :step="0.01" :min="0" style="width: 180px;" />
      </el-form-item>
    
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { createComicsChapter, updateComicsChapter } from '@/service/api/comics/comics';
import type { ComicsChapter, CreateComicsChapterRequest, UpdateComicsChapterRequest } from '@/types/comics';

interface Props {
  comicsId: string;
  visible: boolean;
  dialogType: 'add' | 'edit';
  editData?: ComicsChapter;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  dialogType: 'add',
  editData: undefined
});

const emit = defineEmits(['update:visible', 'success']);

// 表单引用
const formRef = ref();
// 表单数据
const form = reactive<any>({
  id: '',
  title: '',
  comic_id: props.comicsId,
  chapter_number: 1,
  is_free: 0,
  price: 0,
  status: 1
});

// 表单校验规则
const rules = {
  title: [{ required: true, message: '请输入章节标题', trigger: 'blur' }],
  chapter_number: [{ required: true, message: '请输入序号', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
};

// 是否正在提交
const submitLoading = ref(false);

// 对话框可见状态
const dialogVisible = ref(props.visible);

// 监听visible prop变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val && props.dialogType === 'edit' && props.editData) {
    // 编辑模式下填充表单
    Object.assign(form, props.editData);
  } else if (val && props.dialogType === 'add') {
    // 添加模式下重置表单
    resetForm();
  }
});

// 监听对话框可见状态变化
watch(dialogVisible, (val) => {
  emit('update:visible', val);
  if (!val) {
    resetForm();
  }
});

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: '',
    title: '',
    comic_id: props.comicsId,
    chapter_number: 1,
    is_free: 0,
    price: 0,
    status: 1
  });
  
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  try {
    // 表单验证
    await formRef.value.validate();
    
    submitLoading.value = true;
    let res;
    
    if (props.dialogType === 'add') {
      // 创建章节
      const params: { data: CreateComicsChapterRequest } = {
        data: {
          comic_id: props.comicsId,
          title: form.title,
          chapter_number: form.chapter_number,
          status: form.status,
          is_free: form.is_free, 
          price: form.price
        }
      };
      
      // 添加付费相关参数 - 需要更新类型定义，或者使用类型断言
      if (form.is_locked === 1) {
        (params.data as any).is_locked = form.is_locked;
        (params.data as any).price = form.price;
      }
      
      res = await createComicsChapter(params) as any;
    } else {
      // 更新章节
      const params: { data: UpdateComicsChapterRequest } = {
        data: {
          id: form.id,
          title: form.title,
          comic_id: form.comic_id,
          chapter_number: form.chapter_number,
          status: form.status,
          is_free: form.is_free,
          price: form.price
        }
      };
      
      // 添加付费相关参数 - 需要更新类型定义，或者使用类型断言
      if (form.is_locked === 1) {
        (params.data as any).is_locked = form.is_locked;
        (params.data as any).price = form.price;
      }
      
      res = await updateComicsChapter(form.id, params) as any;
    }
    
    const { response, data } = res as any;
    if (response.data.code === 2000) {
      ElMessage.success(props.dialogType === 'add' ? '添加成功' : '编辑成功');
      dialogVisible.value = false;
      emit('success');
    } else {
      ElMessage.error(response?.data?.message || '操作失败');
    }
  } catch (error) {
    console.error('表单提交错误:', error);
  } finally {
    submitLoading.value = false;
  }
};
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 