package sys

// TableInfo 数据库表信息
type TableInfo struct {
	TableName    string `json:"table_name" gorm:"column:TABLE_NAME"`
	TableComment string `json:"table_comment" gorm:"column:TABLE_COMMENT"`
	Engine       string `json:"engine" gorm:"column:ENGINE"`
	TableRows    int64  `json:"table_rows" gorm:"column:TABLE_ROWS"`
	CreateTime   string `json:"create_time" gorm:"column:CREATE_TIME"`
}

// ColumnInfo 数据库列信息
type ColumnInfo struct {
	ColumnName      string `json:"column_name" gorm:"column:COLUMN_NAME"`
	DataType        string `json:"data_type" gorm:"column:DATA_TYPE"`
	ColumnType      string `json:"column_type" gorm:"column:COLUMN_TYPE"`
	IsNullable      string `json:"is_nullable" gorm:"column:IS_NULLABLE"`
	ColumnDefault   string `json:"column_default" gorm:"column:COLUMN_DEFAULT"`
	ColumnComment   string `json:"column_comment" gorm:"column:COLUMN_COMMENT"`
	ColumnKey       string `json:"column_key" gorm:"column:COLUMN_KEY"`
	Extra           string `json:"extra" gorm:"column:EXTRA"`
	OrdinalPosition int    `json:"ordinal_position" gorm:"column:ORDINAL_POSITION"`
}

// ForeignKeyInfo 外键信息
type ForeignKeyInfo struct {
	ConstraintName       string `json:"constraint_name" gorm:"column:CONSTRAINT_NAME"`
	TableName            string `json:"table_name" gorm:"column:TABLE_NAME"`
	ColumnName           string `json:"column_name" gorm:"column:COLUMN_NAME"`
	ReferencedTableName  string `json:"referenced_table_name" gorm:"column:REFERENCED_TABLE_NAME"`
	ReferencedColumnName string `json:"referenced_column_name" gorm:"column:REFERENCED_COLUMN_NAME"`
	UpdateRule           string `json:"update_rule" gorm:"column:UPDATE_RULE"`
	DeleteRule           string `json:"delete_rule" gorm:"column:DELETE_RULE"`
}

// CreateTableResult 建表语句结果
type CreateTableResult struct {
	Table       string `json:"table" gorm:"column:Table"`
	CreateTable string `json:"create_table" gorm:"column:Create Table"`
}

// MockDataRequest Mock数据生成请求
type MockDataRequest struct {
	TableName   string            `json:"table_name" validate:"required"`
	Count       int               `json:"count" validate:"required|min:1|max:1000"`
	ExcludedFKs []string          `json:"excluded_fks"` // 排除的外键
	MockRules   map[string]string `json:"mock_rules"`   // 字段Mock规则
	InsertToDB  bool              `json:"insert_to_db"` // 是否插入数据库
}

// MockDataResponse Mock数据生成响应
type MockDataResponse struct {
	TableName    string                   `json:"table_name"`
	Count        int                      `json:"count"`
	Data         []map[string]interface{} `json:"data"`
	SQL          string                   `json:"sql"`
	InsertedToDB bool                     `json:"inserted_to_db"`
}

// TableDetailResponse 表详情响应
type TableDetailResponse struct {
	TableInfo   TableInfo        `json:"table_info"`
	Columns     []ColumnInfo     `json:"columns"`
	ForeignKeys []ForeignKeyInfo `json:"foreign_keys"`
	CreateSQL   string           `json:"create_sql"`
}
