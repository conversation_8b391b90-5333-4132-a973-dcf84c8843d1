package utils

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"golang.org/x/crypto/bcrypt"
)

// HashPassword 对密码进行哈希加密
func HashPassword(password, salt string) string {
	// 将密码和盐值组合
	combined := password + salt

	// 计算SHA-256哈希
	hash := sha256.Sum256([]byte(combined))

	// 将哈希值转换为base64字符串
	return base64.StdEncoding.EncodeToString(hash[:])
}

// HashPassword 对密码进行哈希加密
func MakePassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

func VerifyPassword(password, salt, hashedPassword string) bool {
	// 使用相同的盐值重新计算哈希
	calculatedHash := HashPassword(password, salt)

	// 比较计算出的哈希和存储的哈希
	return calculatedHash == hashedPassword
}

// CheckPasswordHash 验证密码是否匹配
func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func GenerateSalt() (string, error) {
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return "", fmt.Errorf("生成盐值失败: %v", err)
	}
	return base64.StdEncoding.EncodeToString(salt), nil
}

// HashPassword 使用SHA-256和盐值对密码进行哈希
// HashPassword 使用SHA-256和盐值对密码进行哈希
//func HashPassword(password, salt string) string {
//	// 将密码和盐值组合
//	combined := password + salt
//
//	// 计算SHA-256哈希
//	hash := sha256.Sum256([]byte(combined))
//
//	// 将哈希值转换为base64字符串
//	return base64.StdEncoding.EncodeToString(hash[:])
//}
//
//// VerifyPassword 验证密码是否匹配
