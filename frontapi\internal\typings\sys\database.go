package sys

// TableInfo 数据库表信息 - 前端响应类型
type TableInfo struct {
	TableName    string `json:"table_name"`
	TableComment string `json:"table_comment"`
	Engine       string `json:"engine"`
	TableRows    int64  `json:"table_rows"`
	CreateTime   string `json:"create_time"`
}

// ColumnInfo 数据库列信息 - 前端响应类型
type ColumnInfo struct {
	ColumnName      string `json:"column_name"`
	DataType        string `json:"data_type"`
	ColumnType      string `json:"column_type"`
	IsNullable      string `json:"is_nullable"`
	ColumnDefault   string `json:"column_default"`
	ColumnComment   string `json:"column_comment"`
	ColumnKey       string `json:"column_key"`
	Extra           string `json:"extra"`
	OrdinalPosition int    `json:"ordinal_position"`
}

// ForeignKeyInfo 外键信息 - 前端响应类型
type ForeignKeyInfo struct {
	ConstraintName   string `json:"constraint_name"`
	TableName        string `json:"table_name"`
	ColumnName       string `json:"column_name"`
	ReferencedTable  string `json:"referenced_table"`
	ReferencedColumn string `json:"referenced_column"`
	UpdateRule       string `json:"update_rule"`
	DeleteRule       string `json:"delete_rule"`
}

// TableDetailResponse 表详情响应 - 前端响应类型
type TableDetailResponse struct {
	TableInfo   TableInfo        `json:"table_info"`
	Columns     []ColumnInfo     `json:"columns"`
	ForeignKeys []ForeignKeyInfo `json:"foreign_keys"`
}

// MockDataRequest Mock数据生成请求 - 前端请求类型
type MockDataRequest struct {
	TableName string                 `json:"table_name" validate:"required"`
	Count     int                    `json:"count" validate:"required|min:1|max:1000"`
	Rules     map[string]interface{} `json:"rules"`
}

// MockDataResponse Mock数据生成响应 - 前端响应类型
type MockDataResponse struct {
	TableName string                   `json:"table_name"`
	Count     int                      `json:"count"`
	Data      []map[string]interface{} `json:"data"`
	Success   bool                     `json:"success"`
	Message   string                   `json:"message"`
}

// ForeignKeyDataRequest 外键数据请求 - 前端请求类型
type ForeignKeyDataRequest struct {
	TableName  string `json:"table_name" validate:"required"`
	ColumnName string `json:"column_name" validate:"required"`
	Limit      int    `json:"limit"`
}

// ForeignKeyDataResponse 外键数据响应 - 前端响应类型
type ForeignKeyDataResponse struct {
	Values []string `json:"values"`
}
