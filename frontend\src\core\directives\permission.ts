/**
 * 权限指令
 * 用于控制元素的显示和操作权限
 */

import type { Directive, DirectiveBinding } from 'vue'
import { userService } from '@/core/services/user'

interface PermissionOptions {
  permissions?: string[] // 需要的权限列表
  roles?: string[]       // 需要的角色列表
  mode?: 'all' | 'any'   // 权限检查模式：all-需要所有权限，any-需要任一权限
  fallback?: 'hide' | 'disable' | 'readonly' // 无权限时的处理方式
  message?: string       // 无权限时的提示信息
}

interface PermissionElement extends HTMLElement {
  _permission?: {
    originalDisplay: string
    originalDisabled: boolean
    originalReadonly: boolean
    options: PermissionOptions
    hasPermission: boolean
  }
}

// 默认配置
const defaultOptions: PermissionOptions = {
  permissions: [],
  roles: [],
  mode: 'any',
  fallback: 'hide',
  message: '您没有权限执行此操作'
}

// 权限检查函数
function checkPermission(options: PermissionOptions): boolean {
  const currentUser = userService.currentUser.value
  
  if (!currentUser) {
    return false
  }

  const userPermissions = currentUser.permissions || []
  const userRoles = currentUser.roles || []

  // 检查角色权限
  if (options.roles && options.roles.length > 0) {
    const hasRole = options.mode === 'all'
      ? options.roles.every(role => userRoles.includes(role))
      : options.roles.some(role => userRoles.includes(role))
    
    if (!hasRole) {
      return false
    }
  }

  // 检查具体权限
  if (options.permissions && options.permissions.length > 0) {
    const hasPermission = options.mode === 'all'
      ? options.permissions.every(permission => userPermissions.includes(permission))
      : options.permissions.some(permission => userPermissions.includes(permission))
    
    if (!hasPermission) {
      return false
    }
  }

  return true
}

// 应用权限控制
function applyPermissionControl(el: PermissionElement, hasPermission: boolean): void {
  if (!el._permission) return

  const { options, originalDisplay, originalDisabled, originalReadonly } = el._permission

  if (hasPermission) {
    // 有权限，恢复原始状态
    el.style.display = originalDisplay
    
    if (el instanceof HTMLInputElement || el instanceof HTMLButtonElement || el instanceof HTMLSelectElement || el instanceof HTMLTextAreaElement) {
      el.disabled = originalDisabled
      if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {
        el.readOnly = originalReadonly
      }
    }
    
    el.classList.remove('permission-denied')
    el.removeAttribute('title')
  } else {
    // 无权限，根据配置处理
    switch (options.fallback) {
      case 'hide':
        el.style.display = 'none'
        break
        
      case 'disable':
        if (el instanceof HTMLInputElement || el instanceof HTMLButtonElement || el instanceof HTMLSelectElement || el instanceof HTMLTextAreaElement) {
          el.disabled = true
        }
        el.classList.add('permission-denied')
        if (options.message) {
          el.setAttribute('title', options.message)
        }
        break
        
      case 'readonly':
        if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {
          el.readOnly = true
        } else if (el instanceof HTMLButtonElement || el instanceof HTMLSelectElement) {
          el.disabled = true
        }
        el.classList.add('permission-denied')
        if (options.message) {
          el.setAttribute('title', options.message)
        }
        break
    }
  }

  el._permission.hasPermission = hasPermission
}

// 权限指令
export const vPermission: Directive<PermissionElement, string | string[] | PermissionOptions> = {
  mounted(el: PermissionElement, binding: DirectiveBinding<string | string[] | PermissionOptions>) {
    // 保存原始状态
    const originalDisplay = el.style.display || ''
    const originalDisabled = (el as any).disabled || false
    const originalReadonly = (el as any).readOnly || false

    // 解析配置
    let options: PermissionOptions
    if (typeof binding.value === 'string') {
      options = { ...defaultOptions, permissions: [binding.value] }
    } else if (Array.isArray(binding.value)) {
      options = { ...defaultOptions, permissions: binding.value }
    } else {
      options = { ...defaultOptions, ...binding.value }
    }

    // 从修饰符中获取配置
    if (binding.modifiers.hide) {
      options.fallback = 'hide'
    } else if (binding.modifiers.disable) {
      options.fallback = 'disable'
    } else if (binding.modifiers.readonly) {
      options.fallback = 'readonly'
    }

    if (binding.modifiers.all) {
      options.mode = 'all'
    } else if (binding.modifiers.any) {
      options.mode = 'any'
    }

    // 保存配置到元素
    el._permission = {
      originalDisplay,
      originalDisabled,
      originalReadonly,
      options,
      hasPermission: false
    }

    // 检查权限并应用控制
    const hasPermission = checkPermission(options)
    applyPermissionControl(el, hasPermission)

    // 监听用户状态变化
    const unwatch = userService.watchUser((user) => {
      const newHasPermission = checkPermission(options)
      if (newHasPermission !== el._permission?.hasPermission) {
        applyPermissionControl(el, newHasPermission)
      }
    })

    // 保存取消监听函数
    ;(el as any)._permissionUnwatch = unwatch
  },

  updated(el: PermissionElement, binding: DirectiveBinding<string | string[] | PermissionOptions>) {
    if (!el._permission) return

    // 更新配置
    let options: PermissionOptions
    if (typeof binding.value === 'string') {
      options = { ...defaultOptions, permissions: [binding.value] }
    } else if (Array.isArray(binding.value)) {
      options = { ...defaultOptions, permissions: binding.value }
    } else {
      options = { ...defaultOptions, ...binding.value }
    }

    // 从修饰符中获取配置
    if (binding.modifiers.hide) {
      options.fallback = 'hide'
    } else if (binding.modifiers.disable) {
      options.fallback = 'disable'
    } else if (binding.modifiers.readonly) {
      options.fallback = 'readonly'
    }

    if (binding.modifiers.all) {
      options.mode = 'all'
    } else if (binding.modifiers.any) {
      options.mode = 'any'
    }

    el._permission.options = options

    // 重新检查权限
    const hasPermission = checkPermission(options)
    applyPermissionControl(el, hasPermission)
  },

  unmounted(el: PermissionElement) {
    // 取消监听
    if ((el as any)._permissionUnwatch) {
      (el as any)._permissionUnwatch()
      delete (el as any)._permissionUnwatch
    }

    // 清理配置
    delete el._permission
  }
}

// 权限检查工具函数
export const permissionUtils = {
  // 检查当前用户是否有指定权限
  hasPermission(permission: string): boolean {
    return checkPermission({ permissions: [permission], mode: 'any' })
  },

  // 检查当前用户是否有指定角色
  hasRole(role: string): boolean {
    return checkPermission({ roles: [role], mode: 'any' })
  },

  // 检查当前用户是否有所有指定权限
  hasAllPermissions(permissions: string[]): boolean {
    return checkPermission({ permissions, mode: 'all' })
  },

  // 检查当前用户是否有任一指定权限
  hasAnyPermission(permissions: string[]): boolean {
    return checkPermission({ permissions, mode: 'any' })
  },

  // 检查当前用户是否有所有指定角色
  hasAllRoles(roles: string[]): boolean {
    return checkPermission({ roles, mode: 'all' })
  },

  // 检查当前用户是否有任一指定角色
  hasAnyRole(roles: string[]): boolean {
    return checkPermission({ roles, mode: 'any' })
  },

  // 检查复合权限条件
  checkPermission(options: PermissionOptions): boolean {
    return checkPermission(options)
  },

  // 获取当前用户权限列表
  getUserPermissions(): string[] {
    const currentUser = userService.currentUser.value
    return currentUser?.permissions || []
  },

  // 获取当前用户角色列表
  getUserRoles(): string[] {
    const currentUser = userService.currentUser.value
    return currentUser?.roles || []
  },

  // 检查是否为管理员
  isAdmin(): boolean {
    return this.hasRole('admin') || this.hasRole('super_admin')
  },

  // 检查是否为内容创作者
  isCreator(): boolean {
    return this.hasRole('creator') || this.hasPermission('video:upload')
  },

  // 检查是否为版主
  isModerator(): boolean {
    return this.hasRole('moderator') || this.hasPermission('content:moderate')
  }
}

// 常用权限常量
export const PERMISSIONS = {
  // 视频相关权限
  VIDEO_UPLOAD: 'video:upload',
  VIDEO_EDIT: 'video:edit',
  VIDEO_DELETE: 'video:delete',
  VIDEO_PUBLISH: 'video:publish',
  VIDEO_MODERATE: 'video:moderate',
  
  // 用户相关权限
  USER_VIEW: 'user:view',
  USER_EDIT: 'user:edit',
  USER_DELETE: 'user:delete',
  USER_BAN: 'user:ban',
  
  // 评论相关权限
  COMMENT_CREATE: 'comment:create',
  COMMENT_EDIT: 'comment:edit',
  COMMENT_DELETE: 'comment:delete',
  COMMENT_MODERATE: 'comment:moderate',
  
  // 直播相关权限
  LIVE_STREAM: 'live:stream',
  LIVE_MODERATE: 'live:moderate',
  
  // 系统管理权限
  ADMIN_PANEL: 'admin:panel',
  ADMIN_USERS: 'admin:users',
  ADMIN_CONTENT: 'admin:content',
  ADMIN_SYSTEM: 'admin:system'
} as const

// 常用角色常量
export const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MODERATOR: 'moderator',
  CREATOR: 'creator',
  VIP: 'vip',
  USER: 'user',
  GUEST: 'guest'
} as const

// 导出类型
export type { PermissionOptions }