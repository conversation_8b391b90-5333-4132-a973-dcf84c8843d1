package videos

import (
	"context"
	"frontapi/internal/models/videos"
	"frontapi/internal/repository/base"

	"gorm.io/gorm"
)

type VideoRecommendationRepository interface {
	base.ExtendedRepository[videos.VideoRecommendation]
	FindByUserID(ctx context.Context, userID string) ([]*videos.VideoRecommendation, error)
	FindListByPage(ctx context.Context, page, pageSize int) ([]*videos.VideoRecommendation, error)
}

type videoRecommendationRepository struct {
	base.ExtendedRepository[videos.VideoRecommendation]
}

func NewVideoRecommendationRepository(db *gorm.DB) VideoRecommendationRepository {
	return &videoRecommendationRepository{
		ExtendedRepository: base.NewExtendedRepository[videos.VideoRecommendation](db),
	}
}

func (r *videoRecommendationRepository) FindListByPage(ctx context.Context, page, pageSize int) ([]*videos.VideoRecommendation, error) {
	var recommendations []*videos.VideoRecommendation
	err := r.GetDBWithContext(ctx).Offset((page - 1) * pageSize).Limit(pageSize).Find(&recommendations).Error
	return recommendations, err
}

func (r *videoRecommendationRepository) FindByUserID(ctx context.Context, userID string) ([]*videos.VideoRecommendation, error) {
	conditions := map[string]interface{}{
		"user_id": userID,
	}
	return r.FindByCondition(ctx, conditions, "")
}
