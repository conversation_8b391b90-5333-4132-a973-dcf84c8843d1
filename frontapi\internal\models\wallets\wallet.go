package wallets

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"
)

// Wallet 钱包模型
type Wallet struct {
	models.BaseModelStruct
	UserID               string         `json:"user_id" gorm:"type:string;size:36;not null;comment:用户ID"`
	WithdrawPassword     string         `json:"withdraw_password" gorm:"type:string;size:255;comment:提现密码"`
	WithdrawPasswordSet  types.JSONTime `json:"withdraw_password_set_time" gorm:"comment:提现密码设置时间"`
	Balance              float64        `json:"balance" gorm:"type:decimal(10,2);default:0.00;comment:余额"`
	FrozenBalance        float64        `json:"frozen_balance" gorm:"type:decimal(10,2);default:0.00;comment:冻结余额"`
	DailyWithdrawLimit   float64        `json:"daily_withdraw_limit" gorm:"type:decimal(10,2);default:5000.00;comment:每日提现限额"`
	MonthlyWithdrawLimit float64        `json:"monthly_withdraw_limit" gorm:"type:decimal(10,2);default:50000.00;comment:每月提现限额"`
	PlatformCoin         int            `json:"platform_coin" gorm:"default:0;comment:平台币数量"`
	TotalIncome          float64        `json:"total_income" gorm:"type:decimal(10,2);default:0.00;comment:总收入"`
	TotalExpense         float64        `json:"total_expense" gorm:"type:decimal(10,2);default:0.00;comment:总支出"`
	LastRechargeTime     types.JSONTime `json:"last_recharge_time" gorm:"comment:最后充值时间"`
	LastWithdrawTime     types.JSONTime `json:"last_withdraw_time" gorm:"comment:最后提现时间"`
	Status               string         `json:"status" gorm:"type:string;size:20;default:'normal';comment:状态"`
	StatusChangeReason   string         `json:"status_change_reason" gorm:"type:string;size:255;comment:状态变更原因"`
	StatusChangeTime     types.JSONTime `json:"status_change_time" gorm:"comment:状态变更时间"`
}

// TableName 指定表名
func (Wallet) TableName() string {
	return "ly_wallets"
}
