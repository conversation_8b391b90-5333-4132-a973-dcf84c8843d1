<template>
  <div class="pagination-container">
    <el-pagination
      v-model:current-page="currentPageSync"
      v-model:page-size="pageSizeSync"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      background
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  total: {
    type: Number,
    required: true,
    default: 0
  },
  current: {
    type: Number,
    default: 1
  },
  size: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['pagination']);

const currentPageSync = ref(props.current);
const pageSizeSync = ref(props.size);

// Watch for parent prop changes
watch(() => props.current, (val) => {
  currentPageSync.value = val;
});

watch(() => props.size, (val) => {
  pageSizeSync.value = val;
});

// Handle page size change
const handleSizeChange = (val: number) => {
  pageSizeSync.value = val;
  emit('pagination', { page: currentPageSync.value, pageSize: val });
};

// Handle current page change
const handleCurrentChange = (val: number) => {
  currentPageSync.value = val;
  emit('pagination', { page: val, pageSize: pageSizeSync.value });
};
</script>

<style scoped lang="scss">
.pagination-container {
  margin-top: 20px;
  padding: 10px 0;
  display: flex;
  justify-content: center;
}
</style> 