import type {
    CreateCategoryRequest,
    CreateCommentRequest,
    CreateShortVideoRequest,
    UpdateCategoryRequest,
    UpdateCommentRequest,
    UpdateShortVideoRequest
} from '@/types/shortvideos';
import { request } from "../../request";

// 短视频查询参数接口
interface ShortVideoParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        category_id?: string;
        creator_id?: string;
        status?: number | string;
        start_date?: string;
        end_date?: string;
    }
}

// 短视频分类查询参数接口
interface CategoryParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        status?: number;
        parent_id?: string;
        start_date?: string;
        end_date?: string;
    }
}

// 短视频评论查询参数接口
interface CommentParams {
    page?: {
        pageNo?: number;
        pageSize?: number;
    };
    data?: {
        keyword?: string;
        video_id?: string;
        user_id?: string;
        status?: number;
        created_at_start?: string;
        created_at_end?: string;
    }
}

// 短视频相关API

/**
 * 获取短视频列表
 */
export function getShortVideoList(params?: ShortVideoParams) {
    return request({
        url: '/shortvideos/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取短视频详情
 */
export function getShortVideoDetail(id: string) {
    return request({
        url: `/shortvideos/detail/${id}`,
        method: 'post',
        data: { data: { id } }
    });
}

/**
 * 创建短视频
 */
export function createShortVideo(data: CreateShortVideoRequest) {
    return request({
        url: '/shortvideos/add',
        method: 'post',
        data: { data }
    });
}

/**
 * 更新短视频
 */
export function updateShortVideo(data: UpdateShortVideoRequest) {
    return request({
        url: '/shortvideos/update',
        method: 'post',
        data: { data }
    });
}

/**
 * 更新短视频状态
 */
export function updateShortVideoStatus(id: string, status: number) {
    return request({
        url: '/shortvideos/change-status',
        method: 'post',
        data: { data: { id, status } }
    });
}

/**
 * 批量更新短视频状态
 */
export function batchUpdateShortVideoStatus(data: { ids: string[], status: number }) {
    return request({
        url: '/shortvideos/batch-update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 删除短视频
 */
export function deleteShortVideo(id: string) {
    return request({
        url: `/shortvideos/delete/${id}`,
        method: 'post',
        data: { data: { id } }
    });
}

/**
 * 批量删除短视频
 */
export function batchDeleteShortVideo(data: { ids: string[] }) {
    return request({
        url: '/shortvideos/batch-delete',
        method: 'post',
        data: { data }
    });
}

/**
 * 审核短视频
 */
export function reviewShortVideo(id: string, data: { status: number; reason: string }) {
    return request({
        url: `/shortvideos/review/${id}`,
        method: 'post',
        data: { data: { ...data, id } }
    });
}

/**
 * 批量审核短视频
 */
export function batchReviewShortVideo(data: { ids: string[]; status: number; reason: string }) {
    return request({
        url: '/shortvideos/batch-review',
        method: 'post',
        data: { data }
    });
}

// 短视频分类相关API

/**
 * 获取分类列表
 */
export function getCategoryList(params?: CategoryParams) {
    return request({
        url: '/shortvideos/categories/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取分类详情
 */
export function getCategoryDetail(id: string) {
    return request({
        url: `/shortvideos/categories/detail/${id}`,
        method: 'post',
        data: { data: { id } }
    });
}

/**
 * 创建分类
 */
export function createCategory(data: CreateCategoryRequest) {
    return request({
        url: '/shortvideos/categories/add',
        method: 'post',
        data: { data }
    });
}

/**
 * 更新分类
 */
export function updateCategory(data: UpdateCategoryRequest) {
    return request({
        url: '/shortvideos/categories/update',
        method: 'post',
        data: { data }
    });
}

/**
 * 更新分类状态
 */
export function updateCategoryStatus(data: { id: string; status: number }) {
    return request({
        url: '/shortvideos/categories/update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量更新分类状态
 */
export function batchUpdateCategoryStatus(data: { ids: string[]; status: number }) {
    return request({
        url: '/shortvideos/categories/batch-update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 删除分类
 */
export function deleteCategory(id: string) {
    return request({
        url: `/shortvideos/categories/delete/${id}`,
        method: 'post',
        data: { data: { id } }
    });
}

/**
 * 批量删除分类
 */
export function batchDeleteCategory(data: { ids: string[] }) {
    return request({
        url: '/shortvideos/categories/batch-delete',
        method: 'post',
        data: { data }
    });
}

// 短视频评论相关API

/**
 * 获取评论列表
 */
export function getCommentList(params?: CommentParams) {
    return request({
        url: '/shortvideos/comments/list',
        method: 'post',
        data: params
    });
}

/**
 * 获取评论详情
 */
export function getCommentDetail(id: string) {
    return request({
        url: `/shortvideos/comments/detail/${id}`,
        method: 'post',
        data: { data: { id } }
    });
}

/**
 * 创建评论
 */
export function createComment(data: CreateCommentRequest) {
    return request({
        url: '/shortvideos/comments/add',
        method: 'post',
        data: { data }
    });
}

/**
 * 更新评论
 */
export function updateComment(data: UpdateCommentRequest) {
    return request({
        url: '/shortvideos/comments/update',
        method: 'post',
        data: { data }
    });
}

/**
 * 更新评论状态
 */
export function updateCommentStatus(data: { id: string; status: number }) {
    return request({
        url: '/shortvideos/comments/update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 批量更新评论状态
 */
export function batchUpdateCommentStatus(data: { ids: string[]; status: number }) {
    return request({
        url: '/shortvideos/comments/batch-update-status',
        method: 'post',
        data: { data }
    });
}

/**
 * 删除评论
 */
export function deleteComment(id: string) {
    return request({
        url: `/shortvideos/comments/delete/${id}`,
        method: 'post',
        data: { data: { id } }
    });
}

/**
 * 批量删除评论
 */
export function batchDeleteComment(data: { ids: string[] }) {
    return request({
        url: '/shortvideos/comments/batch-delete',
        method: 'post',
        data: { data }
    });
}

/**
 * 获取评论回复列表
 */
export function getCommentReplies(commentId: string, pageNo: number = 1, pageSize: number = 10) {
    return request({
        url: `/shortvideos/comments/replies/${commentId}`,
        method: 'post',
        data: {
            page: {
                pageNo,
                pageSize
            },
            data: {
                parent_id: commentId
            }
        }
    });
}
