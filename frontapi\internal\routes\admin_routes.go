package routes

import (
	"frontapi/internal/admin/auth"
	"frontapi/internal/admin/home"
	"frontapi/internal/bootstrap"
	"frontapi/internal/middleware"
	adminRoute "frontapi/internal/routes/admin"

	"github.com/gofiber/fiber/v2"
)

// RegisterAdminRoutes 注册管理后台路由（使用服务容器）
func RegisterAdminRoutes(app *fiber.App, services *bootstrap.ServiceContainer) {
	apiGroup := app.Group("/api/proadm")

	// 注册内容创作相关路由
	adminRoute.RegisterContentCreatorRoutes(app, apiGroup, services)

	// 注册视频相关路由
	adminRoute.RegisterVideoRoutes(app, apiGroup, services)

	// 注册短视频相关路由
	adminRoute.RegisterShortVideoRoutes(app, apiGroup, services)

	// 注册帖子相关路由
	adminRoute.RegisterPostRoutes(app, apiGroup, services)

	// 注册图片相关路由
	adminRoute.RegisterPictureRoutes(app, apiGroup, services)

	// 注册漫画相关路由
	adminRoute.RegisterComicRoutes(app, apiGroup, services)

	// 注册书相关路由
	adminRoute.RegisterBookRoutes(app, apiGroup, services)

	// 平台币充值套餐路由
	adminRoute.RegisterCoinPackageRoutes(app, apiGroup, services)

	// 国家/地区路由
	adminRoute.RegisterCountryRoutes(app, apiGroup, services)

	// 热搜关键词路由
	adminRoute.RegisterHotKeywordRoutes(app, apiGroup, services)

	// 注册积分相关路由
	adminRoute.RegisterIntegralRoutes(app, apiGroup, services)

	// 注册推广相关路由
	adminRoute.RegisterPromotionRoutes(app, apiGroup, services)

	// 注册钱包和提现路由
	adminRoute.RegisterWalletRoutes(app, apiGroup, services)

	// 注册用户管理相关路由
	adminRoute.RegisterUserRoutes(app, apiGroup, services)

	// 注册标签相关路由
	adminRoute.RegisterTagRoutes(app, apiGroup, services)

	// 注册RBAC权限相关路由
	adminRoute.RegisterPermissionRoutes(app, apiGroup, services)

	// 注册上传路由
	adminRoute.RegisterUploadRoutes(app, apiGroup, services)

	// 注册数据库相关路由
	adminRoute.RegisterDatabaseRoutes(app, apiGroup, services)

	// 注册基础管理后台路由
	registerBaseAdminRoutes(app, apiGroup, services)
}

// registerBaseAdminRoutes 注册基础管理后台路由
func registerBaseAdminRoutes(app *fiber.App, apiGroup fiber.Router, services *bootstrap.ServiceContainer) {
	// 根路由
	homeController := &home.HomeController{}
	app.Post("/proadm", homeController.Index)

	// 认证相关路由组
	authController := auth.NewAuthController(services.AdminUserService)
	{
		authGroup := apiGroup.Group("/auth")

		// 登录和测试接口不需要认证
		authGroup.Post("/login", authController.Login)
		authGroup.Post("/test", authController.Test)
		authGroup.Get("/codes", authController.GetCodes)

		// 需要认证的接口
		authGroup.Post("/logout", middleware.OptionalAuth(), authController.Logout)
		authGroup.Post("/getUserInfo", middleware.LenientAuth(), authController.GetUserInfo)
	}

	// 用户信息相关路由
	userInfoController := auth.NewUserInfoController()
	app.Get("/api/proadm/user/info", middleware.OptionalAuth(), userInfoController.GetUserInfo)
}

// RegisterAdminRoutesWithContainer 注册admin路由（使用服务容器）
// 此函数保留是为了向后兼容
func RegisterAdminRoutesWithContainer(app *fiber.App, services *bootstrap.ServiceContainer) {
	RegisterAdminRoutes(app, services)
}
