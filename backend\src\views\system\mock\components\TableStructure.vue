<template>
  <div v-if="tableDetail" class="table-structure">
    <!-- 调试信息 -->
    <div v-if="false" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
      <p><strong>调试信息:</strong></p>
      <p>tableDetail: {{ tableDetail ? '有数据' : '无数据' }}</p>
      <p>columns数量: {{ tableDetail?.columns?.length || 0 }}</p>
      <p>foreign_keys数量: {{ tableDetail?.foreign_keys?.length || 0 }}</p>
      <p>mockRules: {{ Object.keys(props.mockRules).length }} 个规则</p>
      <p>excludedFKs: {{ props.excludedFKs.length }} 个排除项</p>
    </div>
    
    <h3>表结构信息</h3>
    
    <!-- 表基本信息 -->
    <el-descriptions :column="2" border class="table-basic-info">
      <el-descriptions-item label="表名">{{ tableDetail.table_info?.table_name || '未知' }}</el-descriptions-item>
      <el-descriptions-item label="表注释">{{ tableDetail.table_info?.table_comment || '无' }}</el-descriptions-item>
      <el-descriptions-item label="存储引擎">{{ tableDetail.table_info?.engine || '未知' }}</el-descriptions-item>
      <el-descriptions-item label="当前行数">{{ tableDetail.table_info?.table_rows || 0 }}</el-descriptions-item>
    </el-descriptions>

    <!-- 字段信息 -->
    <div class="section-header">
      <h4>字段信息</h4>
      <div class="header-actions">
        <el-button type="success" size="small" @click="autoSetAllRules">
          <el-icon><MagicStick /></el-icon>
          智能设置所有规则
        </el-button>
        <el-button type="warning" size="small" @click="clearAllRules">
          <el-icon><Delete /></el-icon>
          清空所有规则
        </el-button>
        <el-button type="info" size="small" @click="showMockHelp = true">
          <el-icon><QuestionFilled /></el-icon>
          Mock规则说明
        </el-button>
      </div>
    </div>
    <el-table 
      :data="tableDetail.columns || []" 
      border 
      style="width: 100%; margin-bottom: 20px;"
      :row-class-name="getRowClassName"
    >
      <el-table-column prop="column_name" label="字段名" width="150" />
      <el-table-column prop="data_type" label="数据类型" width="100" />
      <el-table-column prop="column_type" label="完整类型" width="150" />
      <el-table-column prop="is_nullable" label="允许空值" width="100">
        <template #default="{ row }">
          <el-tag :type="row?.is_nullable === 'YES' ? 'success' : 'danger'">
            {{ row?.is_nullable === 'YES' ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="column_default" label="默认值" width="120" />
      <el-table-column prop="column_comment" label="字段注释" min-width="200">
        <template #default="{ row }">
          <div class="comment-cell">
            <div class="comment-text">
              {{ row?.column_comment || '无' }}
              <!-- 外键信息显示 -->
              <div v-if="getForeignKeyInfo(row?.column_name)" class="foreign-key-info">
                <el-tag type="warning" size="small">
                  外键: {{ getForeignKeyInfo(row?.column_name)?.referenced_table }}.{{ getForeignKeyInfo(row?.column_name)?.referenced_column }}
                </el-tag>
              </div>
            </div>
            <!-- 外键删除按钮 -->
            <div v-if="getForeignKeyInfo(row?.column_name)" class="comment-actions">
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                @click="handleExcludeForeignKey(row?.column_name)"
                :disabled="isColumnExcluded(row?.column_name)"
                title="排除此外键字段"
              >
                {{ isColumnExcluded(row?.column_name) ? '已排除' : '排除' }}
              </el-button>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Mock规则" width="250">
        <template #default="{ row }">
          <el-select
            :model-value="props.mockRules[row?.column_name || '']"
            @update:model-value="handleMockRuleChange(row?.column_name || '', $event)"
            placeholder="选择或输入Mock规则"
            size="small"
            clearable
            filterable
            allow-create
            :disabled="!row?.column_name || isColumnExcluded(row?.column_name)"
            style="width: 100%;"
          >
            <el-option-group label="基础规则">
              <el-option label="中文姓名 (@cname)" value="@cname" />
              <el-option label="英文姓名 (@name)" value="@name" />
              <el-option label="邮箱地址 (@email)" value="@email" />
              <el-option label="手机号码 (@phone)" value="@phone" />
              <el-option label="身份证号 (@id)" value="@id" />
              <el-option label="UUID (@uuid)" value="@uuid" />
              <el-option label="GUID (@guid)" value="@guid" />
            </el-option-group>
            
            <el-option-group label="文本规则">
              <el-option label="中文标题 (@ctitle)" value="@ctitle(5, 20)" />
              <el-option label="中文段落 (@cparagraph)" value="@cparagraph(1, 3)" />
              <el-option label="中文词汇 (@cword)" value="@cword(2, 6)" />
              <el-option label="英文标题 (@title)" value="@title(3, 7)" />
              <el-option label="英文句子 (@sentence)" value="@sentence(3, 18)" />
              <el-option label="随机字符串 (@string)" value="@string(5, 20)" />
            </el-option-group>
            
            <el-option-group label="数字规则">
              <el-option label="整数1-100 (@integer)" value="@integer(1, 100)" />
              <el-option label="整数1-1000 (@integer)" value="@integer(1, 1000)" />
              <el-option label="浮点数 (@float)" value="@float(0, 100, 2, 2)" />
              <el-option label="价格 (@float)" value="@float(0, 10000, 2, 2)" />
              <el-option label="自然数 (@natural)" value="@natural(1, 1000)" />
              <el-option label="布尔值 (@pick)" value="@pick([0, 1])" />
              <el-option label="状态值 (@pick)" value="@pick([0, 1, 2])" />
            </el-option-group>
            
            <el-option-group label="日期时间">
              <el-option label="日期时间 (@datetime)" value="@datetime" />
              <el-option label="日期 (@date)" value="@date" />
              <el-option label="时间 (@time)" value="@time" />
              <el-option label="格式化日期时间" value='@datetime("yyyy-MM-dd HH:mm:ss")' />
              <el-option label="格式化日期" value='@date("yyyy-MM-dd")' />
              <el-option label="格式化时间" value='@time("HH:mm:ss")' />
            </el-option-group>
            
            <el-option-group label="网络相关">
              <el-option label="网址 (@url)" value="@url" />
              <el-option label="域名 (@domain)" value="@domain" />
              <el-option label="IP地址 (@ip)" value="@ip" />
              <el-option label="图片URL (@image)" value='@image("200x200")' />
              <el-option label="图片数组 (JSON)" value="GENERATE_IMAGE_ARRAY" />
            </el-option-group>
            
            <el-option-group label="地址信息">
              <el-option label="省市区 (@county)" value="@county(true)" />
              <el-option label="省份 (@province)" value="@province" />
              <el-option label="城市 (@city)" value="@city" />
              <el-option label="区县 (@county)" value="@county" />
              <el-option label="详细地址 (@region)" value="@region" />
            </el-option-group>
            
            <el-option-group label="其他">
              <el-option label="公司名称 (@company)" value="@company" />
              <el-option label="颜色 (@color)" value="@color" />
              <el-option label="十六进制颜色 (@hex)" value="@hex" />
              <el-option label="RGB颜色 (@rgb)" value="@rgb" />
            </el-option-group>
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <!-- Mock规则帮助 -->
    <MockRulesHelp v-model="showMockHelp" />
  </div>
</template>

<script setup lang="ts">
import { getForeignKeyData } from '@/service/api/database/database';
import type { TableDetailResponse } from '@/types/database';
import { Delete, MagicStick, QuestionFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import MockRulesHelp from './MockRulesHelp.vue';

// Props
interface Props {
  tableDetail: TableDetailResponse | null;
  mockRules: Record<string, string>;
  excludedFKs: string[];
  excludedColumns: string[];
}

// Emits
interface Emits {
  (e: 'mock-rule-changed', columnName: string, rule: string): void;
  (e: 'foreign-key-toggled', constraintName: string): void;
  (e: 'column-excluded', columnName: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 本地响应式数据
const showMockHelp = ref(false);

// 处理Mock规则变化
const handleMockRuleChange = (columnName: string, rule: string) => {
  emit('mock-rule-changed', columnName, rule);
};

// 处理外键切换
const handleForeignKeyToggle = (constraintName: string) => {
  emit('foreign-key-toggled', constraintName);
};

// 处理外键规则设置
async function handleForeignKeyRule(columnName: string, fkInfo: { referenced_table: string; referenced_column: string }) {
  try {
    console.log(`处理外键字段: ${columnName}, 关联表: ${fkInfo.referenced_table}.${fkInfo.referenced_column}`);
    
    // 调用API获取外键关联数据
    const response = await getForeignKeyData({
      data: {
        table_name: fkInfo.referenced_table,
        column_name: fkInfo.referenced_column,
        limit: 50 // 限制获取50个值
      }
    });
    
    if (response.data && response.data.values && response.data.values.length > 0) {
      // 生成基于真实数据的pick规则
      const values = response.data.values.map(value => `"${value}"`);
      const rule = `@pick([${values.join(', ')}])`;
      
      console.log(`外键字段 ${columnName} 设置规则: ${rule}`);
      emit('mock-rule-changed', columnName, rule);
    } else {
      console.warn(`外键字段 ${columnName} 未获取到关联数据，使用默认规则`);
      // 如果没有获取到数据，使用默认的UUID规则
      emit('mock-rule-changed', columnName, '@guid');
    }
  } catch (error) {
    console.error(`处理外键字段 ${columnName} 失败:`, error);
    ElMessage.warning(`获取外键 ${columnName} 的关联数据失败，将使用默认规则`);
    // 出错时使用默认规则
    emit('mock-rule-changed', columnName, '@guid');
  }
}

// 智能设置所有规则
const autoSetAllRules = async () => {
  if (!props.tableDetail?.columns) return;
  
  // 批量处理外键字段
  const foreignKeyPromises: Promise<void>[] = [];
  
  for (const column of props.tableDetail.columns) {
    const columnName = column.column_name.toLowerCase();
    const dataType = column.data_type.toLowerCase();
    const columnType = column.column_type.toLowerCase();
    let rule = '';
    var colLen = extractLength(columnType);
    
    // 检查是否是外键字段
    const fkInfo = getForeignKeyInfo(column.column_name);
    if (fkInfo && !isColumnExcluded(column.column_name)) {
      // 处理外键字段
      const fkPromise = handleForeignKeyRule(column.column_name, fkInfo);
      foreignKeyPromises.push(fkPromise);
      continue; // 跳过后续处理，等待外键数据获取完成
    }
    
    // 优先根据字段名称推断（保持原有的智能推断）
    if(dataType==="tinyint"){
      const tinyintLength = extractLength(columnType);
      const comment = column.column_comment || '';
      
      // 优先判断：解析注释中的枚举值
      if (comment) {
        // 使用正则表达式解析tinyint类型的枚举值
        // 支持格式：0-未知，1-男，2-女 或 1.男，2.女 或 1男，2女 等
        const enumPattern = /(\d+)[,，.\-－:：=＝]\s*([^,，\d]+)/g;
        const matches = [...comment.matchAll(enumPattern)];
        
        if (matches.length > 0) {
          // 提取数字值并生成字符串格式的pick规则
          const values = matches.map(match => `"${match[1]}"`);
          // 去重并排序
          const uniqueValues = [...new Set(values)].sort((a, b) => 
            parseInt(a.replace(/"/g, ''), 10) - parseInt(b.replace(/"/g, ''), 10)
          );
          rule = `@pick([${uniqueValues.join(', ')}])`;
        } else {
          // 检查是否是纯数字列表格式 "0,1,2,3"
          const numberPattern = /(\d+)/g;
          const numberMatches = [...comment.matchAll(numberPattern)];
          if (numberMatches.length >= 2) {
            const numberValues = numberMatches.map(match => `"${match[1]}"`);
            // 去重并排序
            const uniqueNumberValues = [...new Set(numberValues)].sort((a, b) => 
              parseInt(a.replace(/"/g, ''), 10) - parseInt(b.replace(/"/g, ''), 10)
            );
            rule = `@pick([${uniqueNumberValues.join(', ')}])`;
          } else {
            // 检查是否是明确的布尔值字段
            const booleanPattern = /(是否|启用|禁用|开启|关闭|true|false|boolean|bool)/i;
            if (booleanPattern.test(comment)) {
              rule = '@pick(["0", "1"])';
            }
          }
        }
      }
      
      // 如果注释解析没有结果，根据字段名判断
      if (!rule) {
        if (columnName.includes('status') || columnName.includes('enabled') || 
            columnName.includes('verified') || columnName.includes('deleted') ||
            columnName.includes('is_') || columnName.includes('has_') ||
            columnName.includes('can_') || columnName.includes('active')) {
          rule = '@pick(["0", "1"])';
        } else if (columnName.includes('gender') || columnName.includes('sex')) {
          // 性别字段
          rule = '@pick(["0", "1", "2"])';
        } else {
          // 最后判断：根据tinyint长度生成范围
          if (tinyintLength) {
            if (tinyintLength === 1) {
              rule = '@pick(["0", "1"])'; // 通常是布尔值
            } else if (tinyintLength === 2) {
              rule = '@pick(["0", "1", "2", "3"])'; // 小范围枚举
            } else if (tinyintLength === 3) {
              rule = '@pick(["0", "1", "2", "3", "-1", "-2"])'; // 包含负数的枚举
            } else {
              rule = '@integer(0, 127)'; // tinyint最大值
            }
          } else {
            // 默认为布尔值
            rule = '@pick(["0", "1"])';
          }
        }
      }
    }else if (columnName.includes('email')&&['tinyint','int'].indexOf(dataType)===-1) {
      rule = '@email';
    } else if ((columnName.includes('phone') || columnName.includes('mobile'))&&['tinyint','int'].indexOf(dataType)===-1) {
      rule = '@phone';
    }else if(columnName.includes('username')){ //用户名只能是英文字母和数字
  
      rule = '@string(6,12)';
    } else if (columnName.includes('name') && !columnName.includes('_name')) {
      rule = '@cname';
    } else if (columnName.includes('title')) {
      const length = extractLength(columnType);
      if (length && length > 0) {
        const maxLen = Math.min(length, 50); // 标题不超过50个字符
        rule = `@ctitle(3, ${maxLen})`;
      } else {
        rule = '@ctitle(5, 20)';
      }
    } else if ((columnName.includes('content') || columnName.includes('description'))&&['tinyint','int'].indexOf(dataType)===-1) {
      const length = extractLength(columnType);
      if (length && length > 100) {
        rule = '@cparagraph(1, 3)';
      } else if (length && length > 0) {
        const maxLen = Math.min(length, 100);
        rule = `@string(5, ${maxLen})`;
      } else {
        rule = '@cparagraph(1, 3)';
      }
    } else if (columnName.includes('url') || columnName.includes('link')) {
      rule = '@url';
    }else if(columnName.includes('password')||columnName.includes('pwd')||columnName.includes('token')) {
      rule = '@string(36, 64)';
    } else if (columnName.includes('ip')) {
      rule = '@ip';
    } else if ((columnName.includes('uuid') || columnName.includes('guid'))||(columnName.includes('id')&&dataType==='char'&&colLen==36)) {
      rule = '@guid';
    } else if (columnName.includes('address')) {
      rule = '@county(true)';
    } else if (columnName.includes('company')) {
      rule = '@company';
    } else if (columnName.includes('image') || columnName.includes('avatar') || columnName.includes('photo')) {
      rule = '@image("200x200")';
    } else if (columnName === 'images' && (dataType === 'json' || dataType === 'text' || dataType === 'longtext')) {
      // 特殊处理：images字段且为json类型时，生成图片数组
      rule = 'GENERATE_IMAGE_ARRAY';
    } else {
      // 根据数据类型和长度生成精确的规则
      switch (dataType) {
        case 'varchar':
        case 'char':
          const varcharLength = extractLength(columnType);
          if (varcharLength && varcharLength > 0) {
            if (varcharLength <= 10) {
              // 短字符串，生成简单字符串
              rule = `@string(1, ${varcharLength})`;
            } else if (varcharLength <= 50) {
              // 中等长度，生成词汇或短句
              rule = `@cword(2, ${Math.min(varcharLength, 20)})`;
            } else if (varcharLength <= 255) {
              // 长字符串，生成句子
              const maxWords = Math.min(Math.floor(varcharLength / 5), 20);
              rule = `@ctitle(3, ${maxWords})`;
            } else {
              // 超长字符串，生成段落
              rule = '@cparagraph(1, 2)';
            }
          } else {
            rule = '@string(5, 20)';
          }
          break;

        case 'text':
        case 'longtext':
        case 'mediumtext':
          // 文本类型，生成段落
          rule = '@cparagraph(1, 3)';
          break;

        case 'int':
        case 'integer':
          const intLength = extractLength(columnType);
          if (columnName.includes('status')) {
            rule = '@pick([0, 1])';
          } else if (columnName.includes('type') || columnName.includes('category')) {
            rule = '@integer(1, 10)';
          } else {
            // 根据int长度限制生成范围
            if (intLength) {
              const maxValue = Math.pow(10, intLength) - 1;
              rule = `@integer(1, ${Math.min(maxValue, 999999)})`;
            } else {
              rule = '@integer(1, 100000)';
            }
          }
          break;

        case 'bigint':
          if (columnName.includes('status')) {
            rule = '@pick([0, 1])';
          } else {
            rule = '@integer(1, 999999999)';
          }
          break;

        case 'smallint':
          const smallintLength = extractLength(columnType);
          if (smallintLength) {
            const maxValue = Math.pow(10, smallintLength) - 1;
            rule = `@integer(0, ${Math.min(maxValue, 32767)})`;
          } else {
            rule = '@integer(0, 32767)';
          }
          break;

        case 'mediumint':
          const mediumintLength = extractLength(columnType);
          if (mediumintLength) {
            const maxValue = Math.pow(10, mediumintLength) - 1;
            rule = `@integer(0, ${Math.min(maxValue, 8388607)})`;
          } else {
            rule = '@integer(0, 8388607)';
          }
          break;

        case 'decimal':
        case 'numeric':
          const decimalInfo = extractDecimalInfo(columnType);
          if (decimalInfo) {
            const { precision, scale } = decimalInfo;
            const maxIntegerPart = precision - scale;
            const maxValue = Math.pow(10, maxIntegerPart) - 1;
            
            if (columnName.includes('price') || columnName.includes('amount') || columnName.includes('money')) {
              rule = `@float(0, ${Math.min(maxValue, 10000)}, ${scale}, ${scale})`;
            } else {
              rule = `@float(0, ${Math.min(maxValue, 1000)}, ${scale}, ${scale})`;
            }
          } else {
            if (columnName.includes('price') || columnName.includes('amount') || columnName.includes('money')) {
              rule = '@float(0, 10000, 2, 2)';
            } else {
              rule = '@float(0, 100, 2, 2)';
            }
          }
          break;

        case 'float':
        case 'double':
          const floatLength = extractLength(columnType);
          if (columnName.includes('price') || columnName.includes('amount') || columnName.includes('money')) {
            rule = '@float(0, 10000, 2, 2)';
          } else if (floatLength) {
            const maxValue = Math.pow(10, floatLength - 2);
            rule = `@float(0, ${Math.min(maxValue, 10000)}, 2, 2)`;
          } else {
            rule = '@float(0, 100, 2, 2)';
          }
          break;

        case 'datetime':
        case 'timestamp':
          rule = '@datetime("yyyy-MM-dd HH:mm:ss")';
          break;

        case 'date':
          rule = '@date("yyyy-MM-dd")';
          break;

        case 'time':
          rule = '@time("HH:mm:ss")';
          break;

        case 'year':
          rule = '@integer(1970, 2030)';
          break;

        case 'json':
          rule = '{"key": "@string(5, 10)", "value": "@integer(1, 100)"}';
          break;

        case 'enum':
          const enumValues = extractEnumValues(columnType);
          if (enumValues && enumValues.length > 0) {
            rule = `@pick([${enumValues.map(v => `"${v}"`).join(', ')}])`;
          } else {
            rule = '@string(5, 10)';
          }
          break;

        case 'set':
          const setValues = extractEnumValues(columnType);
          if (setValues && setValues.length > 0) {
            rule = `@pick([${setValues.map(v => `"${v}"`).join(', ')}])`;
          } else {
            rule = '@string(5, 10)';
          }
          break;

        case 'binary':
        case 'varbinary':
          const binaryLength = extractLength(columnType);
          if (binaryLength && binaryLength > 0) {
            rule = `@string("lower", ${binaryLength})`;
          } else {
            rule = '@string("lower", 16)';
          }
          break;

        case 'blob':
        case 'longblob':
        case 'mediumblob':
        case 'tinyblob':
          rule = '@string("lower", 32)';
          break;

        default:
          // 未知类型，根据是否允许为空决定
          if (column.is_nullable === 'YES') {
            rule = '@pick([null, "@string(5, 20)"])';
          } else {
            rule = '@string(5, 20)';
          }
          break;
      }
    }

    if (rule) {
      emit('mock-rule-changed', column.column_name, rule);
    }
  }
  
  // 等待所有外键处理完成
  if (foreignKeyPromises.length > 0) {
    try {
      await Promise.all(foreignKeyPromises);
      console.log('所有外键规则设置完成');
    } catch (error) {
      console.error('设置外键规则时出错:', error);
    }
  }
};

// 辅助函数：从字段类型中提取长度
const extractLength = (columnType: string): number | null => {
  const match = columnType.match(/\((\d+)\)/);
  return match ? parseInt(match[1]) : null;
};

// 辅助函数：从decimal类型中提取精度和标度信息
const extractDecimalInfo = (columnType: string): { precision: number; scale: number } | null => {
  const match = columnType.match(/\((\d+),\s*(\d+)\)/);
  if (match) {
    return {
      precision: parseInt(match[1]),
      scale: parseInt(match[2])
    };
  }
  return null;
};

// 辅助函数：从enum/set类型中提取可选值
const extractEnumValues = (columnType: string): string[] | null => {
  const match = columnType.match(/\((.*)\)/);
  if (match) {
    const valuesStr = match[1];
    // 解析枚举值，处理单引号包围的值
    const values = valuesStr.split(',').map(v => v.trim().replace(/^'|'$/g, ''));
    return values;
  }
  return null;
};

// 清空所有规则
const clearAllRules = () => {
  if (!props.tableDetail?.columns) return;
  
  props.tableDetail.columns.forEach(column => {
    emit('mock-rule-changed', column.column_name, '');
  });
};

// 获取外键信息
const getForeignKeyInfo = (columnName: string | undefined): { referenced_table: string; referenced_column: string } | undefined => {
  if (!props.tableDetail?.foreign_keys) return undefined;
  return props.tableDetail.foreign_keys.find(fk => fk.column_name === columnName);
};

// 判断列是否被排除
const isColumnExcluded = (columnName: string | undefined): boolean => {
  if (!columnName) return false;
  return props.excludedColumns.includes(columnName);
};

// 处理外键排除
const handleExcludeForeignKey = (columnName: string | undefined) => {
  if (columnName) {
    emit('column-excluded', columnName);
  }
};

// 获取行类名
const getRowClassName = ({ row }: { row: any }) => {
  const fkInfo = getForeignKeyInfo(row.column_name);
  if (fkInfo && !isColumnExcluded(row.column_name)) {
    return 'foreign-key-row';
  }
  if (isColumnExcluded(row.column_name)) {
    return 'excluded-row';
  }
  return '';
};
</script>

<style scoped>
.table-structure {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-structure h3,
.table-structure h4 {
  margin-top: 0;
  color: #303133;
}

.table-basic-info {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0 10px 0;
}

.section-header h4 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
}

.comment-cell {
  display: flex;
  align-items: center;
}

.comment-text {
  flex: 1;
}

.foreign-key-info {
  margin-top: 5px;
}

.comment-actions {
  margin-left: 10px;
}

.excluded-row {
  background-color: #f0f0f0;
}

:deep(.foreign-key-row) {
  background-color: #fff7e6 !important;
}

:deep(.excluded-row) {
  background-color: #f5f5f5 !important;
  opacity: 0.6;
}
</style> 