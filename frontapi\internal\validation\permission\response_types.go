package permission

import "frontapi/internal/models/permission"

// AdminUserResponse 管理员用户响应
type AdminUserResponse struct {
	*permission.AdminUser
	RoleNames []string `json:"role_names"`
}

// AdminRoleResponse 管理员角色响应
type AdminRoleResponse struct {
	*permission.AdminRole
	UserCount int `json:"user_count"`
}

// MenuTreeResponse 菜单树响应
type MenuTreeResponse struct {
	*permission.AdminMenu
	Children []*MenuTreeResponse `json:"children"`
}

// PermissionResponse 权限检查响应
type PermissionResponse struct {
	UserID      string   `json:"user_id"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
	HasAccess   bool     `json:"has_access"`
}

// UserMenuResponse 用户菜单响应
type UserMenuResponse struct {
	Menus []*MenuTreeResponse `json:"menus"`
	Roles []string            `json:"roles"`
}
