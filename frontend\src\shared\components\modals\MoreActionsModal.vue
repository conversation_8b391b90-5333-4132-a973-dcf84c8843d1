<template>
  <div class="more-actions-modal-overlay" @click="handleOverlayClick">
    <div class="more-actions-modal" @click.stop>
      <!-- 头部 -->
      <div class="modal-header">
        <h3 class="modal-title">更多操作</h3>
        <button class="close-btn" @click="$emit('close')" title="关闭">
          <span class="close-icon">×</span>
        </button>
      </div>

      <!-- 视频信息 -->
      <div class="video-info" v-if="video">
        <div class="video-thumbnail">
          <img v-if="video.cover" :src="video.cover" :alt="video.title" />
          <div v-else class="thumbnail-placeholder">
            <span class="play-icon">▶</span>
          </div>
        </div>
        <div class="video-details">
          <h4 class="video-title">{{ video.title }}</h4>
          <p class="video-creator">@{{ video.creator_name }}</p>
        </div>
      </div>

      <!-- 操作列表 -->
      <div class="actions-list">
        <!-- 收藏到播放列表 -->
        <div class="action-item" @click="addToPlaylist" title="收藏到播放列表">
          <div class="action-icon-wrapper playlist">
            <span class="action-icon">📋</span>
          </div>
          <div class="action-content">
            <span class="action-title">收藏到播放列表</span>
            <span class="action-desc">保存到我的播放列表</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 下载视频 -->
        <div class="action-item" @click="downloadVideo" title="下载视频">
          <div class="action-icon-wrapper download">
            <span class="action-icon">📥</span>
          </div>
          <div class="action-content">
            <span class="action-title">下载视频</span>
            <span class="action-desc">保存到本地设备</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 复制链接 -->
        <div class="action-item" @click="copyLink" title="复制视频链接">
          <div class="action-icon-wrapper copy">
            <span class="action-icon">🔗</span>
          </div>
          <div class="action-content">
            <span class="action-title">复制链接</span>
            <span class="action-desc">分享给朋友</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 不感兴趣 -->
        <div class="action-item" @click="notInterested" title="不感兴趣">
          <div class="action-icon-wrapper not-interested">
            <span class="action-icon">👎</span>
          </div>
          <div class="action-content">
            <span class="action-title">不感兴趣</span>
            <span class="action-desc">减少此类内容推荐</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 屏蔽用户 -->
        <div class="action-item" @click="blockUser" title="屏蔽此用户">
          <div class="action-icon-wrapper block">
            <span class="action-icon">🚫</span>
          </div>
          <div class="action-content">
            <span class="action-title">屏蔽用户</span>
            <span class="action-desc">不再显示此用户的内容</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 举报内容 -->
        <div class="action-item danger" @click="reportContent" title="举报此内容">
          <div class="action-icon-wrapper report">
            <span class="action-icon">⚠️</span>
          </div>
          <div class="action-content">
            <span class="action-title">举报内容</span>
            <span class="action-desc">举报不当内容</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 查看详情 -->
        <div class="action-item" @click="viewDetails" title="查看视频详情">
          <div class="action-icon-wrapper details">
            <span class="action-icon">ℹ️</span>
          </div>
          <div class="action-content">
            <span class="action-title">视频详情</span>
            <span class="action-desc">查看完整信息</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 设置画质 -->
        <div class="action-item" @click="showQualitySettings" title="画质设置">
          <div class="action-icon-wrapper quality">
            <span class="action-icon">🎬</span>
          </div>
          <div class="action-content">
            <span class="action-title">画质设置</span>
            <span class="action-desc">调整视频清晰度</span>
          </div>
          <span class="action-arrow">›</span>
        </div>

        <!-- 播放速度 -->
        <div class="action-item" @click="showSpeedSettings" title="播放速度">
          <div class="action-icon-wrapper speed">
            <span class="action-icon">⚡</span>
          </div>
          <div class="action-content">
            <span class="action-title">播放速度</span>
            <span class="action-desc">调整播放倍速</span>
          </div>
          <span class="action-arrow">›</span>
        </div>
      </div>

      <!-- 确认对话框 -->
      <div v-if="showConfirm" class="confirm-dialog">
        <div class="confirm-content">
          <div class="confirm-icon">
            <span>{{ confirmData.icon }}</span>
          </div>
          <h4 class="confirm-title">{{ confirmData.title }}</h4>
          <p class="confirm-message">{{ confirmData.message }}</p>
          <div class="confirm-actions">
            <button class="confirm-btn cancel" @click="cancelConfirm">
              取消
            </button>
            <button class="confirm-btn confirm" @click="executeConfirm">
              确认
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// Types
interface VideoData {
  id: string
  title: string
  description?: string
  cover?: string
  creator_name?: string
  creator_avatar?: string
}

interface Props {
  video?: VideoData
}

interface ConfirmData {
  icon: string
  title: string
  message: string
  action: () => void
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  action: [type: string, data?: any]
}>()

// State
const showConfirm = ref(false)
const confirmData = reactive<ConfirmData>({
  icon: '',
  title: '',
  message: '',
  action: () => {}
})

// Methods
const handleOverlayClick = () => {
  emit('close')
}

const showConfirmDialog = (data: ConfirmData) => {
  Object.assign(confirmData, data)
  showConfirm.value = true
}

const cancelConfirm = () => {
  showConfirm.value = false
}

const executeConfirm = () => {
  confirmData.action()
  showConfirm.value = false
  emit('close')
}

// Action handlers
const addToPlaylist = () => {
  emit('action', 'add-to-playlist', { videoId: props.video?.id })
  emit('close')
}

const downloadVideo = () => {
  showConfirmDialog({
    icon: '📥',
    title: '下载视频',
    message: '确定要下载这个视频到本地吗？',
    action: () => {
      emit('action', 'download', { videoId: props.video?.id })
      showToast('开始下载视频...')
    }
  })
}

const copyLink = async () => {
  try {
    const url = window.location.href
    await navigator.clipboard.writeText(url)
    showToast('链接已复制到剪贴板')
    emit('action', 'copy-link', { url })
    emit('close')
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败，请重试')
  }
}

const notInterested = () => {
  showConfirmDialog({
    icon: '👎',
    title: '不感兴趣',
    message: '我们会减少向您推荐此类内容，确定继续吗？',
    action: () => {
      emit('action', 'not-interested', { videoId: props.video?.id })
      showToast('已标记为不感兴趣')
    }
  })
}

const blockUser = () => {
  showConfirmDialog({
    icon: '🚫',
    title: '屏蔽用户',
    message: `确定要屏蔽用户 @${props.video?.creator_name} 吗？屏蔽后将不再看到该用户的内容。`,
    action: () => {
      emit('action', 'block-user', { 
        userId: props.video?.creator_name,
        videoId: props.video?.id 
      })
      showToast('已屏蔽该用户')
    }
  })
}

const reportContent = () => {
  emit('action', 'report', { videoId: props.video?.id })
  emit('close')
}

const viewDetails = () => {
  emit('action', 'view-details', { videoId: props.video?.id })
  emit('close')
}

const showQualitySettings = () => {
  emit('action', 'quality-settings')
  emit('close')
}

const showSpeedSettings = () => {
  emit('action', 'speed-settings')
  emit('close')
}

const showToast = (message: string) => {
  // 简单的提示实现，实际项目中可以使用更完善的提示组件
  console.log(message)
}
</script>

<style scoped>
.more-actions-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

.more-actions-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 420px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
  position: relative;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e0e0e0;
  transform: scale(1.05);
}

.close-icon {
  font-size: 20px;
  color: #666;
  line-height: 1;
}

.video-info {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.video-thumbnail {
  width: 60px;
  height: 45px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
}

.play-icon {
  font-size: 16px;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-creator {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.actions-list {
  padding: 8px 0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.action-item:hover {
  background: #f8f9fa;
}

.action-item:active {
  background: #f0f0f0;
}

.action-item.danger:hover {
  background: #fff5f5;
}

.action-item.danger .action-title {
  color: #e53e3e;
}

.action-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
}

.action-icon-wrapper.playlist {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.action-icon-wrapper.download {
  background: linear-gradient(135deg, #9C27B0, #7B1FA2);
}

.action-icon-wrapper.copy {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.action-icon-wrapper.not-interested {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

.action-icon-wrapper.block {
  background: linear-gradient(135deg, #607D8B, #455A64);
}

.action-icon-wrapper.report {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.action-icon-wrapper.details {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.action-icon-wrapper.quality {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #333;
}

.action-icon-wrapper.speed {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #333;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-title {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.action-desc {
  display: block;
  font-size: 12px;
  color: #666;
  line-height: 1.3;
}

.action-arrow {
  font-size: 18px;
  color: #ccc;
  font-weight: bold;
}

.confirm-dialog {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  animation: fadeIn 0.2s ease;
}

.confirm-content {
  text-align: center;
  padding: 32px 24px;
  max-width: 300px;
}

.confirm-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.confirm-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.confirm-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0 0 24px 0;
}

.confirm-actions {
  display: flex;
  gap: 12px;
}

.confirm-btn {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn.cancel:hover {
  background: #e0e0e0;
}

.confirm-btn.confirm {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
}

.confirm-btn.confirm:hover {
  background: linear-gradient(135deg, #ff5252, #ff7979);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .more-actions-modal-overlay {
    padding: 0;
    align-items: flex-end;
  }
  
  .more-actions-modal {
    border-radius: 16px 16px 0 0;
    max-height: 85vh;
  }
  
  .action-item {
    padding: 14px 20px;
  }
  
  .action-icon-wrapper {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
  
  .action-title {
    font-size: 15px;
  }
  
  .action-desc {
    font-size: 11px;
  }
  
  .confirm-content {
    padding: 24px 20px;
  }
  
  .confirm-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
  
  .confirm-title {
    font-size: 16px;
  }
  
  .confirm-message {
    font-size: 13px;
    margin-bottom: 20px;
  }
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .more-actions-modal {
    border: 2px solid #333;
  }
  
  .action-item {
    border-bottom: 2px solid #ddd;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .more-actions-modal-overlay,
  .more-actions-modal,
  .action-item,
  .confirm-btn,
  .confirm-dialog {
    animation: none !important;
    transition: none !important;
  }
}
</style> 