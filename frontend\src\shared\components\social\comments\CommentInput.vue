<template>
    <div class="comment-input-section">
        <div class="input-area">
            <div class="reply-info" v-if="replyTo">
                <span>回复 @{{ replyTo.user.name }}：</span>
                <el-button type="text" @click="$emit('update:replyTo', null)">取消回复</el-button>
            </div>

            <div class="text-input">
                <el-input v-model="CommentText" type="textarea" :rows="2" :placeholder="replyTo ? '回复评论...' : '发表评论...'"
                    resize="none" @update:modelValue="$emit('update:modelValue', $event)" />
            </div>

            <!-- 图片预览区域 -->
            <div v-if="uploadedImages.length > 0" class="image-preview">
                <div v-for="(image, index) in uploadedImages" :key="index" class="preview-item">
                    <el-image :src="image" fit="cover" />
                    <el-button class="remove-btn" circle type="danger" :icon="Delete"
                        @click="$emit('remove-image', index)" />
                </div>
            </div>

            <div class="input-actions">
                <div class="left-actions">
                    <!-- 表情选择器 -->
                    <div class="emoji-picker-wrapper">
                        <el-button circle @click.stop="toggleEmojiPicker">
                            <SvgIcon name="emoji" color="#dcdfe6" :size="24" />
                        </el-button>
                        <div v-if="showEmojiPicker" class="emoji-picker-container">
                            <EmojiPicker @select="(emoji) => $emit('insert-emoji', emoji.i)" />
                        </div>
                    </div>
                    <!-- 图片上传 -->
                    <el-upload action="/api/upload" :show-file-list="false"
                        :on-success="(res: any) => $emit('upload-success', res.url)" accept="image/*">
                        <el-button :icon="Picture" circle />
                    </el-upload>
                </div>

                <el-button type="primary" :disabled="!canSubmit" @click="$emit('submit')">
                    发送
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Delete, Picture } from '@element-plus/icons-vue'
import type { Comment } from '@/types/comment'
import { ref, watch,onMounted,onUnmounted } from 'vue'
import SvgIcon from '../icons/SvgIcon.vue'
import EmojiPicker from 'vue3-emoji-picker';
import 'vue3-emoji-picker/css';



const props = defineProps<{
    currentUserAvatar: string
    replyTo: Comment | null
    modelValue: string
    uploadedImages: string[]
    canSubmit: boolean
}>()

const CommentText = ref(props.modelValue)

watch(() => props.modelValue, (newValue) => {
    CommentText.value = newValue
})

defineEmits<{
    'update:modelValue': [value: string]
    'update:replyTo': [value: Comment | null]
    'submit': []
    'upload-success': [url: string]
    'remove-image': [index: number]
    'insert-emoji': [emoji: string]
}>()

const showEmojiPicker = ref(false)

const toggleEmojiPicker = () => {
    showEmojiPicker.value = !showEmojiPicker.value
}

// 点击外部时关闭表情选择器
const closeEmojiPicker = (event: MouseEvent) => {
    const target = event.target as HTMLElement
    if (!target.closest('.emoji-picker-wrapper')) {
        showEmojiPicker.value = false
    }
}

onMounted(() => {
    document.addEventListener('click', closeEmojiPicker)
})

onUnmounted(() => {
    document.removeEventListener('click', closeEmojiPicker)
})
</script>

<style scoped lang="scss">
.comment-input-section {
    padding: 16px;
    border-top: 1px solid var(--el-border-color-lighter);
    display: flex;
    gap: 12px;
    background-color: var(--el-bg-color);

    .user-avatar {
        flex-shrink: 0;
    }

    .input-area {
        flex: 1;
        min-width: 0;

        .reply-info {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: space-between;
            color: var(--el-text-color-regular);
        }

        .text-input {
            margin-bottom: 8px;

            :deep(.el-textarea__inner) {
                background-color: var(--el-bg-color-overlay);
                border-color: var(--el-border-color-darker);
                color: var(--el-text-color-primary);

                &:focus {
                    border-color: var(--el-color-primary);
                }
            }
        }

        .image-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;

            .preview-item {
                position: relative;
                width: 80px;
                height: 80px;

                .el-image {
                    width: 100%;
                    height: 100%;
                    border-radius: 4px;
                }

                .remove-btn {
                    position: absolute;
                    top: -8px;
                    right: -8px;
                    transform: scale(0.8);
                }
            }
        }

        .input-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .left-actions {
                display: flex;
                gap: 8px;
            }
        }
    }
}

.emoji-picker-wrapper {
    position: relative;

    .emoji-picker-container {
        position: absolute;
        bottom: 100%;
        left: 0;
        z-index: 1000;
        background-color: var(--el-bg-color);
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        margin-bottom: 8px;
    }
}

.left-actions {
    display: flex;
    gap: 8px;
    position: relative;
}
</style>