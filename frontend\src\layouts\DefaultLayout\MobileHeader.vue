<template>
  <header class="mobile-header">
    <div class="mobile-header-container">
    <div class="mobile-header-left">
        <div class="mobile-logo-section">
            <router-link to="/" class="mobile-logo-link">
            <div class="mobile-logo-icon-wrapper">
                <img src="@/assets/icons/logo.svg" alt="logo" class="mobile-logo-icon">
            </div>
            <span class="mobile-logo-text">
                {{ $t('common.siteName') }}
            </span>
            </router-link>
        </div>
            <!-- 导航快捷菜单 -->
        <div class="mobile-nav-shortcuts">
            <div class="mobile-nav-shortcut-wrapper" v-for="item in menuItems"  :key="item.label" >   
                <router-link :to="item.route as string" class="mobile-nav-shortcut" v-if="item.showMobile&&item.route">
                    <i :class="item.icon"></i>
                    <span class="mobile-nav-shortcut-text">{{ t(item.label as string) }}</span>
                </router-link>
            </div>
        
        </div>
   </div>   
      <!-- 移动端导航按钮组 -->
      <div class="mobile-actions">
        <!-- 搜索按钮 -->
        <button class="mobile-action-btn" @click="toggleSearch">
          <i class="pi pi-search"></i>
        </button>
        
        <!-- 用户头像 -->
        <button class="mobile-action-btn" @click="toggleUserMenu">
          <i class="pi pi-user"></i>
        </button>
        
        <!-- 菜单按钮 -->
        <button class="mobile-action-btn" @click="toggleDrawer('right')">
          <i class="pi pi-bars"></i>
        </button>
      </div>
    </div>

    <!-- 搜索展开区域 -->
    <div class="mobile-search-container" v-if="showSearch">
      <div class="mobile-search-wrapper">
        <IconField class="w-full">
          <InputText v-model="searchQuery" :placeholder="$t('common.search')" class="w-full" @keyup.enter="performSearch" />
          <InputIcon class="pi pi-search" />
        </IconField>
        <Button icon="pi pi-times" class="mobile-search-close" @click="toggleSearch" />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { menuItems } from '@/config/menu.config';
import { useTranslation } from "@/core/plugins/i18n/composables";
import Button from 'primevue/button';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import InputText from 'primevue/inputtext';
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const emit = defineEmits(['toggleDrawer']);

// i18n
const { t } = useTranslation();
const router = useRouter();

// 响应式数据
const searchQuery = ref('');
const showSearch = ref(false);

// 切换搜索框显示
const toggleSearch = () => {
  showSearch.value = !showSearch.value;
  if (showSearch.value) {
    setTimeout(() => {
      const inputElement = document.querySelector('.mobile-search-wrapper input') as HTMLInputElement;
      inputElement?.focus();
    }, 100);
  }
};

// 切换抽屉菜单
const toggleDrawer = (position: 'left' | 'right') => {
  emit('toggleDrawer', position);
};

// 切换用户菜单
const toggleUserMenu = () => {
  // 实现用户菜单逻辑
};

// 执行搜索
const performSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({ path: '/search', query: { q: searchQuery.value } });
    showSearch.value = false;
  }
};
</script>

<style scoped lang="scss">
.mobile-header {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 50;
  background: linear-gradient(to bottom, var(--surface-0), var(--surface-50));
  border-bottom: 1px solid var(--surface-border);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  
  .mobile-header-container {
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .mobile-header-left{
        display: flex;
        align-items: center;
        justify-content: space-between;

        .mobile-nav-shortcuts {
            display: flex;
            overflow-x: auto;
            padding: 0.2rem 1rem;
            &::-webkit-scrollbar {
            display: none;
            }
            
            .mobile-nav-shortcut {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 0.2rem 0.5rem;
                min-width: 4rem;
                text-decoration: none;
                color: var(--pimary-text-color);
                font-size: 0.75rem;
                gap: 0.25rem;
                
                i {
                    margin-top: 0.25rem;
                    font-size: 1.25rem;
                    color: var(--primary-color);
                }
                
                span {
                    white-space: nowrap;
                }
                
                &.router-link-active {
                    color: var(--primary-color);
                    font-weight: 500;
                }
                .mobile-nav-shortcut-text{
                    margin-top: 0.25rem;
                    font-size: 0.75rem;
                    color: var(--pimary-text-color);
                }
            }
        }
    }

    .mobile-logo-section {
      .mobile-logo-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
        
        .mobile-logo-icon-wrapper {
          width: 2rem;
          height: 2rem;
          
          .mobile-logo-icon {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        
        .mobile-logo-text {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--pimary-text-color);
        }
      }
    }
    
    .mobile-actions {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      
      .mobile-action-btn {
        width: 2.25rem;
        height: 2.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background: var(--surface-card);
        border: 1px solid var(--surface-border);
        color: var(--pimary-text-color);
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          background: var(--surface-hover);
          color: var(--primary-color);
        }
        
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
  
  .mobile-search-container {
    padding: 0.5rem 1rem;
    background: var(--surface-card);
    border-top: 1px solid var(--surface-border);
    
    .mobile-search-wrapper {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      
      .mobile-search-close {
        width: 2rem;
        height: 2rem;
        padding: 0;
      }
    }
  }
}

// 深色模式样式
.sof-dark {
  .mobile-header {
    background: linear-gradient(to bottom, var(--surface-700), var(--surface-800));
    
    .mobile-action-btn {
      background: var(--surface-600);
      border-color: var(--surface-500);
      color: var(--surface-0);
      
      &:hover {
        background: var(--surface-500);
      }
    }
    
    .mobile-search-container,
    .mobile-nav-shortcuts {
      background: var(--surface-700);
      border-color: var(--surface-600);
    }
  }
}
</style>
