package users

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username    string `json:"username" validate:"required"`
	Password    string `json:"password" validate:"required"`
	Email       string `json:"email" validate:"required,email"`
	Nickname    string `json:"nickname" validate:"required"`
	Avatar      string `json:"avatar"`
	PhoneNumber string `json:"phoneNumber"`
	Gender      int8   `json:"gender"`
	Birthday    string `json:"birthday"`
	Bio         string `json:"bio"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Nickname    string `json:"nickname"`
	Username    string `json:"username"`
	Avatar      string `json:"avatar"`
	Email       string `json:"email"`
	PhoneNumber string `json:"phoneNumber"`
	Gender      int8   `json:"gender"`
	Birthday    string `json:"birthday"`
	Bio         string `json:"bio"`
	Status      int    `json:"status"`
}
type UpdateUserStatusRequest struct {
	ID     string `json:"id" validate:"required"`
	Status int    `json:"status"`
}
type BatchUpdateUserStatusRequest struct {
	Ids    []string `json:"ids" validate:"required|min_len:1"`
	Status int      `json:"status" validate:"required"`
}

// BatchDeleteUserRequest 批量删除用户请求
type BatchDeleteUserRequest struct {
	Ids []string `json:"ids" validate:"required"`
}

// UpdateProfileRequest 更新个人资料请求验证模型
type UpdateProfileRequest struct {
	Nickname  string `json:"nickname" validate:"omitempty,min=2,max=50"`
	Gender    int    `json:"gender" validate:"omitempty,oneof=0 1 2"`
	BirthDate string `json:"birthDate" validate:"omitempty,dateformat"`
	AvatarURL string `json:"avatarUrl" validate:"omitempty,url"`
	Bio       string `json:"bio" validate:"omitempty,max=200"`
	Website   string `json:"website" validate:"omitempty,url"`
}

// LoginRequest 登录请求验证模型
type LoginRequest struct {
	Username string `json:"username" validate:"required,min=3,max=50"`
	Password string `json:"password" validate:"required,min=6"`
}

// RegisterRequest 注册请求验证模型
type RegisterRequest struct {
	Username   string `json:"username" validate:"required,username"`
	Password   string `json:"password" validate:"required,strongpassword"`
	Email      string `json:"email" validate:"required,email"`
	Mobile     string `json:"mobile" validate:"required,mobile"`
	Nickname   string `json:"nickname" validate:"required,min=2,max=50"`
	Gender     int    `json:"gender" validate:"oneof=0 1 2"` // 0:未知, 1:男, 2:女
	BirthDate  string `json:"birthDate" validate:"omitempty,dateformat"`
	AvatarURL  string `json:"avatarUrl" validate:"omitempty,url"`
	Invitation string `json:"invitation" validate:"omitempty"`
}

// ChangePasswordRequest 修改密码请求验证模型
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" validate:"required,min=6"`
	NewPassword string `json:"newPassword" validate:"required,strongpassword,nefield=OldPassword"`
	Confirm     string `json:"confirm" validate:"required,eqfield=NewPassword"`
}
