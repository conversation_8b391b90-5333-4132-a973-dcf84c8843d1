package content_creator

import (
	"frontapi/internal/admin"
	contentCreatorModel "frontapi/internal/models/content_creator"
	contentCreatorSrv "frontapi/internal/service/content_creator"
	contentCreatorValidator "frontapi/internal/validation/content_creator"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

type ContentRatingController struct {
	admin.BaseController
	ContentRatingService contentCreatorSrv.ContentRatingService
}

func NewContentRatingController(service contentCreatorSrv.ContentRatingService) *ContentRatingController {
	return &ContentRatingController{ContentRatingService: service}
}

// ListContentRatings 获取内容评分列表
func (c *ContentRatingController) ListContentRatings(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	userID := reqInfo.Get("user_id").GetString()
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()
	condition := map[string]interface{}{
		"user_id":      userID,
		"content_type": contentType,
		"content_id":   contentID,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询内容评分列表
	list, total, err := c.ContentRatingService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容评分列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// GetContentRating 获取内容评分详情
func (c *ContentRatingController) GetContentRating(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 查询内容评分
	contentRating, err := c.ContentRatingService.GetByID(ctx.Context(), id, true)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容评分详情失败: "+err.Error())
	}

	if contentRating == nil {
		return c.NotFound(ctx, "内容评分不存在")
	}

	// 返回内容评分详情
	return c.Success(ctx, contentRating)
}

// CreateContentRating 创建内容评分
func (c *ContentRatingController) CreateContentRating(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.CreateContentRatingRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 创建内容评分
	var contentRating contentCreatorModel.ContentRating

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentRating); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	id, err := c.ContentRatingService.Create(ctx.Context(), &contentRating)
	if err != nil {
		return c.InternalServerError(ctx, "创建内容评分失败: "+err.Error())
	}

	return c.Success(ctx, fiber.Map{
		"id":      id,
		"message": "创建内容评分成功",
	})
}

// UpdateContentRating 更新内容评分
func (c *ContentRatingController) UpdateContentRating(ctx *fiber.Ctx) error {
	// 解析请求参数
	var req contentCreatorValidator.UpdateContentRatingRequest
	if err := validator.ValidateDataWrapper(ctx, &req); err != nil {
		return c.BadRequest(ctx, "无效的请求参数"+err.Error(), nil)
	}

	// 更新内容评分
	contentRating := &contentCreatorModel.ContentRating{}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, contentRating); err != nil {
		return c.InternalServerError(ctx, "字段映射失败: "+err.Error())
	}

	err := c.ContentRatingService.Update(ctx.Context(), contentRating)
	if err != nil {
		return c.InternalServerError(ctx, "更新内容评分失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "更新内容评分成功")
}

// DeleteContentRating 删除内容评分
func (c *ContentRatingController) DeleteContentRating(ctx *fiber.Ctx) error {
	// 获取ID参数
	id, err := c.GetId(ctx)
	if err != nil {
		return err
	}

	// 删除内容评分
	err = c.ContentRatingService.Delete(ctx.Context(), id)
	if err != nil {
		return c.InternalServerError(ctx, "删除内容评分失败: "+err.Error())
	}

	return c.SuccessWithMessage(ctx, "删除内容评分成功")
}

// ListContentRatingsByUser 根据用户获取评分列表
func (c *ContentRatingController) ListContentRatingsByUser(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	userID := reqInfo.Get("user_id").GetString()
	condition := map[string]interface{}{
		"user_id": userID,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询内容评分列表
	list, total, err := c.ContentRatingService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取用户评分列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// ListContentRatingsByContent 根据内容获取评分列表
func (c *ContentRatingController) ListContentRatingsByContent(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 分页参数
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 查询参数
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()
	condition := map[string]interface{}{
		"content_type": contentType,
		"content_id":   contentID,
	}
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}
	// 查询内容评分列表
	list, total, err := c.ContentRatingService.List(ctx.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容评分列表失败: "+err.Error())
	}

	// 返回分页列表
	return c.SuccessList(ctx, list, total, page, pageSize)
}

// GetContentRatingByUserAndContent 根据用户和内容获取评分
func (c *ContentRatingController) GetContentRatingByUserAndContent(ctx *fiber.Ctx) error {
	// 获取请求信息
	reqInfo := c.GetRequestInfo(ctx)

	// 查询参数
	userID := reqInfo.Get("user_id").GetString()
	contentType := reqInfo.Get("content_type").GetString()
	contentID := reqInfo.Get("content_id").GetString()
	condition := map[string]interface{}{
		"user_id":      userID,
		"content_type": contentType,
		"content_id":   contentID,
	}

	// 查询内容评分
	contentRating, err := c.ContentRatingService.FindAll(ctx.Context(), condition, "", false)
	if err != nil {
		return c.InternalServerError(ctx, "获取内容评分失败: "+err.Error())
	}

	if contentRating == nil {
		return c.NotFound(ctx, "内容评分不存在")
	}

	// 返回内容评分
	return c.Success(ctx, contentRating)
}
