/**
 * Modern 主题配置
 * 基于现代设计理念的主题
 */
import { ThemeConfig } from '../theme-manager';
import blueVariables from './variables';
import blueDarkVariables from './variables-dark';

// Modern 亮色主题
export const blueLightTheme: ThemeConfig = {
    name: 'blueLight',
    displayName: 'Blue Light',
    extendTheme: 'aura',
    extendName: 'aura-light-blue',
    extendThemeStyle: 'primevue/resources/themes/aura-light-blue/theme.css',
    shortName: 'blue',
    code: 'bluelight',
    primary: '#4A90E2',
    isDark: false,
    variables: blueVariables
};

// Modern 暗色主题
export const blueDarkTheme: ThemeConfig = {
    name: 'blueDark',
    displayName: 'Blue Dark',
    extendTheme: 'aura',
    extendName: 'aura-dark-blue',
    extendThemeStyle: 'primevue/resources/themes/aura-dark-blue/theme.css',
    shortName: 'blue dark',
    code: 'bluedark',
    primary: '#5BA0F2',
    isDark: true,
    variables: blueDarkVariables
};

// 默认导出亮色主题（保持向后兼容）
export default blueLightTheme;