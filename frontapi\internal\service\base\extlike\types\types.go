package types

import (
	"errors"
	"time"
)

// LikeOperation 点赞操作
type LikeOperation struct {
	UserID    string                 `json:"user_id" bson:"user_id"`
	ItemID    string                 `json:"item_id" bson:"item_id"`
	ItemType  string                 `json:"item_type" bson:"item_type"`
	Action    string                 `json:"action" bson:"action"` // "like" or "unlike"
	Metadata  map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
	Timestamp time.Time              `json:"timestamp" bson:"timestamp"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start" bson:"start"`
	End   time.Time `json:"end" bson:"end"`
}

// LikeRecord 点赞记录
type LikeRecord struct {
	ID        string                 `json:"id" bson:"_id,omitempty" gorm:"primaryKey"`
	UserID    string                 `json:"user_id" bson:"user_id" gorm:"index"`
	ItemType  string                 `json:"item_type" bson:"item_type" gorm:"index"`
	ItemID    string                 `json:"item_id" bson:"item_id" gorm:"index"`
	Timestamp time.Time              `json:"timestamp" bson:"timestamp" gorm:"index"`
	Status    string                 `json:"status" bson:"status"`                     // "liked" or "unliked"
	Source    string                 `json:"source,omitempty" bson:"source,omitempty"` // 来源标识
	Metadata  map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty" gorm:"type:json"`
	Version   int64                  `json:"version" bson:"version"` // 版本号，用于冲突解决
}

// LikeTrend 点赞趋势
type LikeTrend struct {
	ItemID     string    `json:"item_id" bson:"item_id"`
	ItemType   string    `json:"item_type" bson:"item_type"`
	Date       time.Time `json:"date" bson:"date"`
	LikeCount  int64     `json:"like_count" bson:"like_count"`
	UserCount  int64     `json:"user_count" bson:"user_count"`
	GrowthRate float64   `json:"growth_rate" bson:"growth_rate"` // 增长率
	Score      float64   `json:"score" bson:"score"`             // 热度分数
	Rank       int       `json:"rank" bson:"rank"`               // 排名
}

// UserLikeStats 用户点赞统计
type UserLikeStats struct {
	UserID          string                 `json:"user_id" bson:"user_id"`
	TotalLikes      int64                  `json:"total_likes" bson:"total_likes"`
	TotalItems      int64                  `json:"total_items" bson:"total_items"`
	LikesByType     map[string]int64       `json:"likes_by_type" bson:"likes_by_type"`
	FirstLikeAt     *time.Time             `json:"first_like_at,omitempty" bson:"first_like_at,omitempty"`
	LastLikeAt      *time.Time             `json:"last_like_at,omitempty" bson:"last_like_at,omitempty"`
	AvgLikesPerDay  float64                `json:"avg_likes_per_day" bson:"avg_likes_per_day"`
	MostLikedType   string                 `json:"most_liked_type" bson:"most_liked_type"`
	EngagementScore float64                `json:"engagement_score" bson:"engagement_score"`
	Metadata        map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// ItemLikeStats 项目点赞统计
type ItemLikeStats struct {
	ItemType      string                 `json:"item_type" bson:"item_type"`
	ItemID        string                 `json:"item_id" bson:"item_id"`
	TotalLikes    int64                  `json:"total_likes" bson:"total_likes"`
	TotalUsers    int64                  `json:"total_users" bson:"total_users"`
	UniqueUsers   int64                  `json:"unique_users" bson:"unique_users"`
	FirstLikeAt   *time.Time             `json:"first_like_at,omitempty" bson:"first_like_at,omitempty"`
	LastLikeAt    *time.Time             `json:"last_like_at,omitempty" bson:"last_like_at,omitempty"`
	PeakLikesAt   *time.Time             `json:"peak_likes_at,omitempty" bson:"peak_likes_at,omitempty"`
	LikesPerHour  map[int]int64          `json:"likes_per_hour" bson:"likes_per_hour"`
	LikesPerDay   map[string]int64       `json:"likes_per_day" bson:"likes_per_day"`
	HotScore      float64                `json:"hot_score" bson:"hot_score"`
	TrendingScore float64                `json:"trending_score" bson:"trending_score"`
	Metadata      map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// GlobalLikeStats 全局点赞统计
type GlobalLikeStats struct {
	ItemType        string                 `json:"item_type" bson:"item_type"`
	TotalLikes      int64                  `json:"total_likes" bson:"total_likes"`
	TotalUsers      int64                  `json:"total_users" bson:"total_users"`
	TotalItems      int64                  `json:"total_items" bson:"total_items"`
	ActiveUsers     int64                  `json:"active_users" bson:"active_users"`
	AvgLikesPerUser float64                `json:"avg_likes_per_user" bson:"avg_likes_per_user"`
	AvgLikesPerItem float64                `json:"avg_likes_per_item" bson:"avg_likes_per_item"`
	FirstLikeAt     *time.Time             `json:"first_like_at,omitempty" bson:"first_like_at,omitempty"`
	LastLikeAt      *time.Time             `json:"last_like_at,omitempty" bson:"last_like_at,omitempty"`
	GrowthRate      float64                `json:"growth_rate" bson:"growth_rate"`
	EngagementRate  float64                `json:"engagement_rate" bson:"engagement_rate"`
	Metadata        map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// UserEngagement 用户参与度统计
type UserEngagement struct {
	UserID           string                 `json:"user_id" bson:"user_id"`
	TimeRange        TimeRange              `json:"time_range" bson:"time_range"`
	TotalActions     int64                  `json:"total_actions" bson:"total_actions"`
	LikeActions      int64                  `json:"like_actions" bson:"like_actions"`
	UnlikeActions    int64                  `json:"unlike_actions" bson:"unlike_actions"`
	ActionsByType    map[string]int64       `json:"actions_by_type" bson:"actions_by_type"`
	ActionsByHour    map[int]int64          `json:"actions_by_hour" bson:"actions_by_hour"`
	ActionsByDay     map[string]int64       `json:"actions_by_day" bson:"actions_by_day"`
	EngagementScore  float64                `json:"engagement_score" bson:"engagement_score"`
	ConsistencyScore float64                `json:"consistency_score" bson:"consistency_score"`
	DiversityScore   float64                `json:"diversity_score" bson:"diversity_score"`
	Metadata         map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// HotItem 热门项目
type HotItem struct {
	ItemID    string                 `json:"item_id" bson:"item_id"`
	ItemType  string                 `json:"item_type" bson:"item_type"`
	LikeCount int64                  `json:"like_count" bson:"like_count"`
	Score     float64                `json:"score" bson:"score"`
	Rank      int                    `json:"rank" bson:"rank"`
	UpdatedAt time.Time              `json:"updated_at" bson:"updated_at"`
	Trending  bool                   `json:"trending" bson:"trending"`
	Metadata  map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// TopLikedItem 热门点赞项目
type TopLikedItem struct {
	ItemID     string                 `json:"item_id" bson:"item_id"`
	ItemType   string                 `json:"item_type" bson:"item_type"`
	LikeCount  int64                  `json:"like_count" bson:"like_count"`
	UserCount  int64                  `json:"user_count" bson:"user_count"`
	Rank       int                    `json:"rank" bson:"rank"`
	Score      float64                `json:"score" bson:"score"`
	TimeRange  TimeRange              `json:"time_range" bson:"time_range"`
	GrowthRate float64                `json:"growth_rate" bson:"growth_rate"`
	Metadata   map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// ActiveLiker 活跃点赞用户
type ActiveLiker struct {
	UserID         string                 `json:"user_id" bson:"user_id"`
	LikeCount      int64                  `json:"like_count" bson:"like_count"`
	ItemCount      int64                  `json:"item_count" bson:"item_count"`
	Rank           int                    `json:"rank" bson:"rank"`
	Score          float64                `json:"score" bson:"score"`
	TimeRange      TimeRange              `json:"time_range" bson:"time_range"`
	DiversityScore float64                `json:"diversity_score" bson:"diversity_score"`
	Metadata       map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// CacheStats 缓存统计信息
type CacheStats struct {
	HitCount        int64   `json:"hit_count" bson:"hit_count"`
	MissCount       int64   `json:"miss_count" bson:"miss_count"`
	HitRate         float64 `json:"hit_rate" bson:"hit_rate"`
	TotalKeys       int64   `json:"total_keys" bson:"total_keys"`
	MemoryUsed      int64   `json:"memory_used" bson:"memory_used"`
	MemoryLimit     int64   `json:"memory_limit" bson:"memory_limit"`
	EvictionCount   int64   `json:"eviction_count" bson:"eviction_count"`
	TotalUsers      int64   `json:"total_users" bson:"total_users"`
	TotalItems      int64   `json:"total_items" bson:"total_items"`
	TotalLikes      int64   `json:"total_likes" bson:"total_likes"`
	AvgLikesPerUser int64   `json:"avg_likes_per_user" bson:"avg_likes_per_user"`
	AvgLikesPerItem int64   `json:"avg_likes_per_item" bson:"avg_likes_per_item"`
	CacheEfficiency float64 `json:"cache_efficiency" bson:"cache_efficiency"`
	// 添加新字段
	Timestamp   time.Time                   `json:"timestamp" bson:"timestamp"`
	Collections map[string]*CollectionStats `json:"collections,omitempty" bson:"collections,omitempty"`
	Metadata    map[string]interface{}      `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// CollectionStats MongoDB集合统计信息
type CollectionStats struct {
	Name          string                 `json:"name" bson:"name"`
	DocumentCount int64                  `json:"document_count" bson:"document_count"`
	Size          int64                  `json:"size" bson:"size"`
	IndexCount    int                    `json:"index_count" bson:"index_count"`
	IndexSize     int64                  `json:"index_size" bson:"index_size"`
	AvgDocSize    float64                `json:"avg_doc_size" bson:"avg_doc_size"`
	Metadata      map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// OperationStats 操作统计信息
type OperationStats struct {
	Operation       string        `json:"operation" bson:"operation"`
	Count           int64         `json:"count" bson:"count"`
	AvgResponseTime time.Duration `json:"avg_response_time" bson:"avg_response_time"`
	MinResponseTime time.Duration `json:"min_response_time" bson:"min_response_time"`
	MaxResponseTime time.Duration `json:"max_response_time" bson:"max_response_time"`
	ErrorCount      int64         `json:"error_count" bson:"error_count"`
	ErrorRate       float64       `json:"error_rate" bson:"error_rate"`
	Throughput      float64       `json:"throughput" bson:"throughput"`
}

// HourStats 小时统计信息
type HourStats struct {
	Hour      int   `json:"hour" bson:"hour"`
	LikeCount int64 `json:"like_count" bson:"like_count"`
	UserCount int64 `json:"user_count" bson:"user_count"`
	ItemCount int64 `json:"item_count" bson:"item_count"`
}

// HourlyStats 按小时统计
type HourlyStats struct {
	ItemType       string                 `json:"item_type" bson:"item_type"`
	Date           time.Time              `json:"date" bson:"date"`
	HourStats      []HourStats            `json:"hour_stats" bson:"hour_stats"`
	TotalLikes     int64                  `json:"total_likes" bson:"total_likes"`
	TotalUsers     int64                  `json:"total_users" bson:"total_users"`
	TotalItems     int64                  `json:"total_items" bson:"total_items"`
	PeakHour       int                    `json:"peak_hour" bson:"peak_hour"`
	PeakLikeCount  int64                  `json:"peak_like_count" bson:"peak_like_count"`
	AvgLikesByHour float64                `json:"avg_likes_by_hour" bson:"avg_likes_by_hour"`
	Metadata       map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// SyncConflict 同步冲突
type SyncConflict struct {
	ID           string                 `json:"id" bson:"_id,omitempty"`
	ItemType     string                 `json:"item_type" bson:"item_type"`
	ItemID       string                 `json:"item_id" bson:"item_id"`
	UserID       string                 `json:"user_id" bson:"user_id"`
	ConflictType string                 `json:"conflict_type" bson:"conflict_type"` // "version", "timestamp", "data"
	RedisData    map[string]interface{} `json:"redis_data" bson:"redis_data"`
	MongoData    map[string]interface{} `json:"mongo_data" bson:"mongo_data"`
	DetectedAt   time.Time              `json:"detected_at" bson:"detected_at"`
	ResolvedAt   *time.Time             `json:"resolved_at,omitempty" bson:"resolved_at,omitempty"`
	Resolution   string                 `json:"resolution,omitempty" bson:"resolution,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// LikeDelta 点赞增量记录
type LikeDelta struct {
	ID        string                 `json:"id" bson:"_id,omitempty"`
	ItemType  string                 `json:"item_type" bson:"item_type"`
	ItemID    string                 `json:"item_id" bson:"item_id"`
	UserID    string                 `json:"user_id" bson:"user_id"`
	Delta     int64                  `json:"delta" bson:"delta"`         // 增量值 (+1 for like, -1 for unlike)
	Operation string                 `json:"operation" bson:"operation"` // "like", "unlike"
	Timestamp time.Time              `json:"timestamp" bson:"timestamp"`
	Processed bool                   `json:"processed" bson:"processed"`
	BatchID   string                 `json:"batch_id,omitempty" bson:"batch_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// TableSyncConfig 表同步配置
type TableSyncConfig struct {
	ItemType        string        `json:"item_type" bson:"item_type"`
	TableName       string        `json:"table_name" bson:"table_name"`
	IDColumn        string        `json:"id_column" bson:"id_column"`
	LikeCountColumn string        `json:"like_count_column" bson:"like_count_column"`
	Enabled         bool          `json:"enabled" bson:"enabled"`
	SyncMode        string        `json:"sync_mode" bson:"sync_mode"` // "realtime", "batch", "manual"
	BatchSize       int           `json:"batch_size" bson:"batch_size"`
	SyncInterval    time.Duration `json:"sync_interval" bson:"sync_interval"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Timestamp       time.Time     `json:"timestamp" bson:"timestamp"`
	OperationType   string        `json:"operation_type" bson:"operation_type"`
	Latency         time.Duration `json:"latency" bson:"latency"`
	Throughput      float64       `json:"throughput" bson:"throughput"`
	ErrorRate       float64       `json:"error_rate" bson:"error_rate"`
	Concurrency     int           `json:"concurrency" bson:"concurrency"`
	MemoryUsage     int64         `json:"memory_usage" bson:"memory_usage"`
	CPUUsage        float64       `json:"cpu_usage" bson:"cpu_usage"`
	ConnectionCount int           `json:"connection_count" bson:"connection_count"`
	QueueSize       int           `json:"queue_size" bson:"queue_size"`
	// 补充缺失的字段
	TotalOperations int64                      `json:"total_operations" bson:"total_operations"`
	OperationStats  map[string]*OperationStats `json:"operation_stats,omitempty" bson:"operation_stats,omitempty"`
	Metadata        map[string]interface{}     `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Service      string                 `json:"service" bson:"service"`
	Version      string                 `json:"version" bson:"version"`
	Status       string                 `json:"status" bson:"status"` // "healthy", "degraded", "unhealthy"
	Timestamp    time.Time              `json:"timestamp" bson:"timestamp"`
	Uptime       time.Duration          `json:"uptime" bson:"uptime"`
	Components   map[string]string      `json:"components" bson:"components"`
	Dependencies map[string]string      `json:"dependencies" bson:"dependencies"`
	Metrics      map[string]interface{} `json:"metrics" bson:"metrics"`
	Errors       []string               `json:"errors,omitempty" bson:"errors,omitempty"`
	Warnings     []string               `json:"warnings,omitempty" bson:"warnings,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// BatchOperation 批量操作结果
type BatchOperation struct {
	ID            string                 `json:"id" bson:"_id,omitempty"`
	OperationType string                 `json:"operation_type" bson:"operation_type"`
	TotalItems    int                    `json:"total_items" bson:"total_items"`
	SuccessCount  int                    `json:"success_count" bson:"success_count"`
	FailureCount  int                    `json:"failure_count" bson:"failure_count"`
	StartTime     time.Time              `json:"start_time" bson:"start_time"`
	EndTime       *time.Time             `json:"end_time,omitempty" bson:"end_time,omitempty"`
	Duration      time.Duration          `json:"duration" bson:"duration"`
	Errors        []string               `json:"errors,omitempty" bson:"errors,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// RateLimitInfo 限流信息
type RateLimitInfo struct {
	UserID       string        `json:"user_id" bson:"user_id"`
	ItemType     string        `json:"item_type" bson:"item_type"`
	Operation    string        `json:"operation" bson:"operation"`
	Limit        int           `json:"limit" bson:"limit"`
	Remaining    int           `json:"remaining" bson:"remaining"`
	ResetTime    time.Time     `json:"reset_time" bson:"reset_time"`
	Window       time.Duration `json:"window" bson:"window"`
	Blocked      bool          `json:"blocked" bson:"blocked"`
	BlockedUntil *time.Time    `json:"blocked_until,omitempty" bson:"blocked_until,omitempty"`
}

// 错误定义
var (
	ErrInvalidOperation = errors.New("invalid operation")
	ErrInvalidAction    = errors.New("invalid action")
	ErrNotFound         = errors.New("not found")
	ErrAlreadyExists    = errors.New("already exists")
	ErrTimeout          = errors.New("operation timeout")
	ErrServiceBusy      = errors.New("service busy")
)

// SyncStatus 同步状态
type SyncStatus struct {
	LastSyncTime       time.Time              `json:"last_sync_time" bson:"last_sync_time"`
	SyncEnabled        bool                   `json:"sync_enabled" bson:"sync_enabled"`
	SyncInProgress     bool                   `json:"sync_in_progress" bson:"sync_in_progress"`
	SyncErrors         []string               `json:"sync_errors,omitempty" bson:"sync_errors,omitempty"`
	ItemType           string                 `json:"item_type" bson:"item_type"`
	TotalSynced        int64                  `json:"total_synced" bson:"total_synced"`
	TotalErrors        int64                  `json:"total_errors" bson:"total_errors"`
	CurrentBatchSize   int                    `json:"current_batch_size" bson:"current_batch_size"`
	EstimatedRemaining int64                  `json:"estimated_remaining" bson:"estimated_remaining"`
	SyncSpeed          float64                `json:"sync_speed" bson:"sync_speed"` // items per second
	Metadata           map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// ServiceMetrics 服务指标
type ServiceMetrics struct {
	ServiceName       string        `json:"service_name" bson:"service_name"`
	Version           string        `json:"version" bson:"version"`
	Timestamp         time.Time     `json:"timestamp" bson:"timestamp"`
	Uptime            time.Duration `json:"uptime" bson:"uptime"`
	TotalRequests     int64         `json:"total_requests" bson:"total_requests"`
	SuccessRequests   int64         `json:"success_requests" bson:"success_requests"`
	ErrorRequests     int64         `json:"error_requests" bson:"error_requests"`
	AvgResponseTime   time.Duration `json:"avg_response_time" bson:"avg_response_time"`
	RequestsPerSecond float64       `json:"requests_per_second" bson:"requests_per_second"`
	MemoryUsage       int64         `json:"memory_usage" bson:"memory_usage"`
	CPUUsage          float64       `json:"cpu_usage" bson:"cpu_usage"`
	GoroutineCount    int           `json:"goroutine_count" bson:"goroutine_count"`
	// 补充缺失的字段
	TotalLikes      int64                  `json:"total_likes" bson:"total_likes"`
	ActiveLikes     int64                  `json:"active_likes" bson:"active_likes"`
	UniqueUsers     int64                  `json:"unique_users" bson:"unique_users"`
	UniqueItems     int64                  `json:"unique_items" bson:"unique_items"`
	AvgLikesPerItem float64                `json:"avg_likes_per_item" bson:"avg_likes_per_item"`
	AvgLikesPerUser float64                `json:"avg_likes_per_user" bson:"avg_likes_per_user"`
	Storage         map[string]interface{} `json:"storage" bson:"storage"`
	CacheStats      *CacheStats            `json:"cache_stats,omitempty" bson:"cache_stats,omitempty"`
	DatabaseStats   map[string]interface{} `json:"database_stats,omitempty" bson:"database_stats,omitempty"`
	Components      map[string]interface{} `json:"components" bson:"components"`
	Metadata        map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// LikeEvent 点赞事件
type LikeEvent struct {
	ID        string                 `json:"id" bson:"_id,omitempty"`
	UserID    string                 `json:"user_id" bson:"user_id"`
	ItemID    string                 `json:"item_id" bson:"item_id"`
	ItemType  string                 `json:"item_type" bson:"item_type"`
	Timestamp time.Time              `json:"timestamp" bson:"timestamp"`
	Source    string                 `json:"source,omitempty" bson:"source,omitempty"`
	UserInfo  map[string]interface{} `json:"user_info,omitempty" bson:"user_info,omitempty"`
	ItemInfo  map[string]interface{} `json:"item_info,omitempty" bson:"item_info,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// UnlikeEvent 取消点赞事件
type UnlikeEvent struct {
	ID        string                 `json:"id" bson:"_id,omitempty"`
	UserID    string                 `json:"user_id" bson:"user_id"`
	ItemID    string                 `json:"item_id" bson:"item_id"`
	ItemType  string                 `json:"item_type" bson:"item_type"`
	Timestamp time.Time              `json:"timestamp" bson:"timestamp"`
	Source    string                 `json:"source,omitempty" bson:"source,omitempty"`
	UserInfo  map[string]interface{} `json:"user_info,omitempty" bson:"user_info,omitempty"`
	ItemInfo  map[string]interface{} `json:"item_info,omitempty" bson:"item_info,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}

// BatchEvent 批量操作事件
type BatchEvent struct {
	ID            string                 `json:"id" bson:"_id,omitempty"`
	OperationType string                 `json:"operation_type" bson:"operation_type"`
	ItemType      string                 `json:"item_type" bson:"item_type"`
	UserID        string                 `json:"user_id" bson:"user_id"`
	ItemIDs       []string               `json:"item_ids" bson:"item_ids"`
	TotalItems    int                    `json:"total_items" bson:"total_items"`
	SuccessCount  int                    `json:"success_count" bson:"success_count"`
	FailureCount  int                    `json:"failure_count" bson:"failure_count"`
	StartTime     time.Time              `json:"start_time" bson:"start_time"`
	EndTime       time.Time              `json:"end_time" bson:"end_time"`
	Duration      time.Duration          `json:"duration" bson:"duration"`
	Errors        []string               `json:"errors,omitempty" bson:"errors,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty" bson:"metadata,omitempty"`
}
