package promotion

import (
	"frontapi/internal/models"
)

type InvitationRule struct {
	models.BaseModelStruct
	Name                  string  `json:"name" gorm:"type:string;size:50;not null;comment:规则名称"`
	InviteRewardPoints    int     `json:"invite_reward_points" gorm:"not null;default:0;comment:邀请奖励积分"`
	InvitedRewardPoints   int     `json:"invited_reward_points" gorm:"not null;default:0;comment:被邀请奖励积分"`
	CommissionRate        float64 `json:"commission_rate" gorm:"type:decimal(5,2);not null;default:0.00;comment:佣金比例"`
	CommissionPeriod      int     `json:"commission_period" gorm:"not null;default:0;comment:佣金周期(天)"`
	IsPermanentCommission int8    `json:"is_permanent_commission" gorm:"not null;default:0;comment:是否永久佣金"`
	Description           string  `json:"description" gorm:"type:string;size:255;comment:规则描述"`
}

func (InvitationRule) TableName() string {
	return "ly_invitation_rules"
}
