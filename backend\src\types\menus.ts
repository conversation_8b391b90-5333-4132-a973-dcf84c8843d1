// 菜单类型定义
export interface Menu {
  id: number;
  parent?: number | null;
  path: string;
  name: string;
  permission: string;
  type: number;
  icon: string;
  orderNo: number;
  component: string;
  keepalive: number;
  show: number;
  status: number;
  external: number;
  createdAt: string;
  updatedAt: string;
  children?: Menu[];
  meta?: {
    title: string;
    auths?: string[];
    icon?: string;
    iconColor?: string;
    rank?: number;
    roles?: string[];
    keepalive?: boolean;
    show?: boolean;
    noCache?: boolean;
  };
}

// 菜单查询参数
export interface MenuParams {
  page?: {
    pageNo?: number;
    pageSize?: number;
  };
  data?: {
    name?: string;
    status?: number;
    type?: number;
  };
}

// 新增菜单请求
export interface CreateMenuRequest {
  parent?: number | null;
  path: string;
  name: string;
  permission?: string;
  type: number;
  icon?: string;
  orderNo?: number;
  component?: string;
  keepalive?: number;
  show?: number;
  status?: number;
  external?: number;
}

// 更新菜单请求
export interface UpdateMenuRequest {
  id: number;
  parent?: number | null;
  path?: string;
  name?: string;
  permission?: string;
  type?: number;
  icon?: string;
  orderNo?: number;
  component?: string;
  keepalive?: number;
  show?: number;
  status?: number;
  external?: number;
} 