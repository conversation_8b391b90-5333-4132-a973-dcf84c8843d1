/**
 * 国际化语言入口文件
 */

import { DEFAULT_LOCALE, SUPPORTED_LOCALES, getEnabledLocaleCodes } from '@/config/locales.config';
import type { LocaleMessages, LocaleNameMap, LocaleType } from './types';

// 预先导入所有语言包
import * as enMessages from './en/index';
import * as koMessages from './ko/index';
import * as zhCNMessages from './zh-CN/index';
import * as zhTWMessages from './zh-TW/index';

// 导出类型
export * from './types';

// 默认语言
export const defaultLocale: LocaleType = DEFAULT_LOCALE;

// 支持的语言列表
export const supportedLocales: LocaleType[] = getEnabledLocaleCodes() as LocaleType[];

// 语言包映射
const messagesMap: Record<string, LocaleMessages> = {
    'en': enMessages.default || {},
    'zh-CN': zhCNMessages.default || {},
    'zh-TW': zhTWMessages.default || {},
    'ko': koMessages.default || {}
};

// 语言显示名称映射
export const localeNames: LocaleNameMap = Object.fromEntries(
    SUPPORTED_LOCALES.filter(locale => locale.enabled)
        .map(locale => [locale.code, locale.nativeName])
) as LocaleNameMap;

// 获取语言配置的辅助函数 - 异步加载
export async function loadLocaleMessages(locale: LocaleType): Promise<LocaleMessages> {
    try {
        // 从预加载的映射中获取
        if (messagesMap[locale]) {
            return messagesMap[locale];
        }

        // 作为后备，尝试使用默认语言
        if (locale !== defaultLocale && messagesMap[defaultLocale]) {
            console.warn(`Language ${locale} not found, falling back to ${defaultLocale}`);
            return messagesMap[defaultLocale];
        }

        return {};
    } catch (error) {
        console.error(`Failed to load locale messages for ${locale}`, error);
        return {};
    }
}

// 检查语言是否支持
export function isLocaleSupported(locale: string): locale is LocaleType {
    return supportedLocales.includes(locale as LocaleType);
}

// 导出所有语言信息
export function getAllLocales() {
    return SUPPORTED_LOCALES.filter(locale => locale.enabled);
}