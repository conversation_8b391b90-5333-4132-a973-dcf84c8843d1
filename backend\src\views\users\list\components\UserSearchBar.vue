<template>
  <div class="user-search-bar">
    <el-form :inline="true" :model="searchForm" class="flex flex-wrap gap-2">
      <el-form-item label="用户名/昵称">
        <el-input 
          v-model="searchForm.keyword" 
          placeholder="请输入用户名或昵称" 
          clearable
          @keyup.enter="handleSearch"
        />
      </el-form-item>
      
      <el-form-item label="状态">
        <el-select 
          v-model="searchForm.status" 
          placeholder="请选择状态" 
          clearable 
          style="width: 180px;"
        >
          <el-option label="正常" :value="1" />
          <el-option label="禁用" :value="0" />
          <el-option label="已删除" :value="-4" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="用户类型">
        <el-select 
          v-model="searchForm.user_type" 
          placeholder="请选择类型" 
          clearable 
          style="width: 180px;"
        >
          <el-option label="普通用户" :value="1" />
          <el-option label="VIP用户" :value="2" />
          <el-option label="管理员" :value="3" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="内容创作者">
        <el-select 
          v-model="searchForm.is_content_creator" 
          placeholder="请选择" 
          clearable 
          style="width: 150px;"
        >
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="注册开始时间">
        <el-date-picker
          v-model="searchForm.reg_time_start"
          type="date"
          placeholder="开始日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 150px;"
        />
      </el-form-item>
      
      <el-form-item label="注册结束时间">
        <el-date-picker
          v-model="searchForm.reg_time_end"
          type="date"
          placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 150px;"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button @click="handleRefresh">
          <el-icon><RefreshRight /></el-icon>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import { reactive, watch } from 'vue';

// 搜索表单类型定义
export interface UserSearchForm {
  keyword: string;
  status?: number;
  user_type?: number;
  is_content_creator?: number;
  reg_time_start: string;
  reg_time_end: string;
}

// Props
interface Props {
  modelValue?: UserSearchForm;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({
    keyword: '',
    status: undefined,
    user_type: undefined,
    is_content_creator: undefined,
    reg_time_start: '',
    reg_time_end: '',
  })
});

// Emits
interface Emits {
  search: [params: UserSearchForm];
  reset: [];
  refresh: [];
  'update:modelValue': [value: UserSearchForm];
}

const emit = defineEmits<Emits>();

// 搜索表单
const searchForm = reactive<UserSearchForm>({ ...props.modelValue });

// 监听表单变化，同步到父组件
watch(searchForm, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });

// 搜索
const handleSearch = () => {
  emit('search', { ...searchForm });
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    user_type: undefined,
    is_content_creator: undefined,
    reg_time_start: '',
    reg_time_end: '',
  });
  emit('reset');
};

// 刷新
const handleRefresh = () => {
  emit('refresh');
};
</script>

<style scoped lang="scss">
.user-search-bar {
  margin-bottom: 16px;

  .el-form {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    .el-form-item {
      margin-bottom: 8px;
    }
  }
}
</style> 