package videos

import (
	"frontapi/internal/models"
	"frontapi/pkg/types"

	"github.com/guregu/null/v6"
)

// VideoComment 评论表
type VideoComment struct {
	*models.BaseModelStruct
	Content      string            `gorm:"column:content" json:"content"` // 评论内容
	Images       types.StringArray `json:"images" gorm:"type:json;comment:图片"`
	Video        null.String       `json:"video" gorm:"type:text;comment:视频"`
	UserID       string            `gorm:"column:user_id;index" json:"user_id"`             // 作者ID
	UserNickname string            `gorm:"column:user_nickname" json:"user_nickname"`       // 用户昵称
	UserAvatar   string            `gorm:"column:user_avatar" json:"user_avatar"`           // 用户头像
	ReplyToID    null.String       `gorm:"column:reply_to_id" json:"reply_to_id"`           // 回复用户ID
	ReplyToUser  null.String       `gorm:"column:reply_to_user" json:"reply_to_user"`       // 回复的用户名字
	UserType     *uint8            `gorm:"column:user_type" json:"user_type"`               // 1普通用户，2明星
	ParentID     null.String       `gorm:"column:parent_id;index" json:"parent_id"`         // 父评论ID
	VideoID      string            `gorm:"column:video_id;index" json:"video_id"`           // 关联实体ID
	Heat         *uint             `gorm:"column:heat" json:"heat"`                         // 热度
	ReplyCount   *uint             `gorm:"column:reply_count;default:0" json:"reply_count"` // 回复数
	LikeCount    *uint             `gorm:"column:like_count;default:0" json:"like_count"`   // 点赞数
	Status       int8              `gorm:"column:status;default:1" json:"status"`           // 状态：0-禁用，1-正常
}

// TableName 设置表名
func (VideoComment) TableName() string {
	return "ly_video_comments"
}

// 实现BaseModel接口的方法
func (v *VideoComment) SetCreatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetCreatedAt(time)
	}
}

func (v *VideoComment) SetUpdatedAt(time types.JSONTime) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetUpdatedAt(time)
	}
}

func (v *VideoComment) SetID(id string) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetID(id)
	}
}

func (v *VideoComment) SetStatus(status int8) {
	if v.BaseModelStruct != nil {
		v.BaseModelStruct.SetStatus(status)
	}
}
