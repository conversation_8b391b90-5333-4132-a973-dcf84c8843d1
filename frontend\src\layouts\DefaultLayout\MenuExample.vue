<template>
  <div class="menu-example">
    <div class="example-header">
      <h2>菜单组件示例</h2>
      <div class="controls">
        <Button 
          :label="verticalCollapsed ? '展开' : '折叠'" 
          @click="verticalCollapsed = !verticalCollapsed"
          size="small"
        />
        <Button 
          :label="verticalHoverMode ? '点击模式' : '悬停模式'" 
          @click="verticalHoverMode = !verticalHoverMode"
          size="small"
        />
      </div>
    </div>

    <!-- 横向菜单示例 -->
    <div class="horizontal-example">
      <h3>横向菜单 (支持 MegaMenu)</h3>
      <HorizontalMenu 
        :mega-menu-threshold="6"
        @item-click="handleMenuItemClick"
      />
    </div>

    <!-- 竖向菜单示例 -->
    <div class="vertical-example">
      <h3>竖向菜单</h3>
      <div class="vertical-menu-container">
        <VerticalMenu 
          :collapsed="verticalCollapsed"
          :hover-mode="verticalHoverMode"
          @item-click="handleMenuItemClick"
        />
        <div class="content-area">
          <div class="content-placeholder">
            <h4>内容区域</h4>
            <p>当前路由: {{ $route.path }}</p>
            <p>菜单状态:</p>
            <ul>
              <li>折叠: {{ verticalCollapsed ? '是' : '否' }}</li>
              <li>悬停模式: {{ verticalHoverMode ? '是' : '否' }}</li>
            </ul>
            <p>最后点击的菜单项: {{ lastClickedItem?.label || '无' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能说明 -->
    <div class="feature-description">
      <h3>功能特性</h3>
      <div class="features">
        <div class="feature-card">
          <h4>横向菜单</h4>
          <ul>
            <li>支持悬停和点击触发子菜单</li>
            <li>自动检测子菜单数量，超过阈值使用 MegaMenu 风格</li>
            <li>多列布局，适合大量子菜单项</li>
            <li>响应式设计</li>
          </ul>
        </div>
        <div class="feature-card">
          <h4>竖向菜单</h4>
          <ul>
            <li>支持折叠/展开模式</li>
            <li>两种子菜单模式：悬停弹出 / 点击展开</li>
            <li>折叠时自动使用 Popover 显示子菜单</li>
            <li>平滑动画过渡</li>
            <li>活动状态高亮</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Button from 'primevue/button';
import { ref } from 'vue';
import HorizontalMenu from './HorizontalMenu.vue';
import VerticalMenu from './VerticalMenu.vue';

// State
const verticalCollapsed = ref(false);
const verticalHoverMode = ref(true);
const lastClickedItem = ref<any>(null);

// Methods
const handleMenuItemClick = (item: any) => {
  lastClickedItem.value = item;
  console.log('菜单项被点击:', item);
};
</script>

<style lang="scss" scoped>
.menu-example {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  
  .example-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    
    h2 {
      margin: 0;
      color: var(--text-color);
    }
    
    .controls {
      display: flex;
      gap: 1rem;
    }
  }
  
  .horizontal-example {
    margin-bottom: 3rem;
    
    h3 {
      margin-bottom: 1rem;
      color: var(--text-color);
    }
    
    border: 1px solid var(--surface-border);
    border-radius: 8px;
    overflow: hidden;
  }
  
  .vertical-example {
    margin-bottom: 3rem;
    
    h3 {
      margin-bottom: 1rem;
      color: var(--text-color);
    }
    
    .vertical-menu-container {
      display: flex;
      border: 1px solid var(--surface-border);
      border-radius: 8px;
      overflow: hidden;
      height: 500px;
      
      .content-area {
        flex: 1;
        background: var(--surface-ground);
        
        .content-placeholder {
          padding: 2rem;
          
          h4 {
            margin-top: 0;
            color: var(--text-color);
          }
          
          p, ul {
            color: var(--text-color-secondary);
          }
          
          ul {
            padding-left: 1.5rem;
          }
        }
      }
    }
  }
  
  .feature-description {
    .features {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-top: 1rem;
      
      .feature-card {
        background: var(--surface-card);
        border: 1px solid var(--surface-border);
        border-radius: 8px;
        padding: 1.5rem;
        
        h4 {
          margin-top: 0;
          margin-bottom: 1rem;
          color: var(--primary-color);
        }
        
        ul {
          margin: 0;
          padding-left: 1.5rem;
          color: var(--text-color-secondary);
          
          li {
            margin-bottom: 0.5rem;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .menu-example {
    padding: 1rem;
    
    .example-header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }
    
    .feature-description .features {
      grid-template-columns: 1fr;
    }
  }
}
</style>
