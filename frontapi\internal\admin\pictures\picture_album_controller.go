package pictures

import (
	"frontapi/internal/admin"
	"frontapi/internal/models"
	pictureModel "frontapi/internal/models/pictures"
	"frontapi/internal/service/pictures"
	pictureValidation "frontapi/internal/validation/pictures"
	"frontapi/pkg/utils"
	"frontapi/pkg/validator"

	"github.com/gofiber/fiber/v2"
)

// ListAlbums 获取图片专辑列表
type PictureAlbumController struct {
	admin.BaseController
	albumService   pictures.PictureAlbumService
	pictureService pictures.PictureService
}

func NewPictureAlbumController(
	albumService pictures.PictureAlbumService,
	pictureService pictures.PictureService,
) *PictureAlbumController {
	return &PictureAlbumController{
		BaseController: admin.BaseController{},
		albumService:   albumService,
		pictureService: pictureService,
	}
}

func (h *PictureAlbumController) ListAlbums(c *fiber.Ctx) error {
	// A使用链式调用获取请求信息
	reqInfo := h.GetRequestInfo(c)

	// 获取分页信息
	page := reqInfo.Page.PageNo
	pageSize := reqInfo.Page.PageSize

	// 提取查询参数
	title := reqInfo.Get("title").GetString()
	categoryID := reqInfo.Get("category_id").GetString()
	status := -999
	_ = h.GetIntegerValueWithDataWrapper(c, "status", &status)
	createdAtStart := reqInfo.Get("created_at_start").GetString()
	createdAtEnd := reqInfo.Get("created_at_end").GetString()

	// 构建查询条件
	condition := map[string]interface{}{
		"title":            title,
		"category_id":      categoryID,
		"status":           status,
		"created_at_start": createdAtStart,
		"created_at_end":   createdAtEnd,
	}

	// 提取排序参数
	orderBy := reqInfo.Get("order_by").GetString()
	if orderBy == "" {
		orderBy = "created_at DESC"
	}

	// 查询专辑列表
	albums, total, err := h.albumService.List(c.Context(), condition, orderBy, page, pageSize, false)
	if err != nil {
		return h.InternalServerError(c, "获取专辑列表失败: "+err.Error())
	}

	return h.SuccessList(c, albums, total, page, pageSize)
}

// GetAlbum 获取图片专辑详情
func (h *PictureAlbumController) GetAlbum(c *fiber.Ctx) error {
	// 获取专辑ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	album, err := h.albumService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "专辑不存在: "+err.Error())
	}

	return h.Success(c, album)
}

// CreateAlbum 创建图片专辑
func (h *PictureAlbumController) CreateAlbum(c *fiber.Ctx) error {
	userID, ok := h.RequireLogin(c)
	if !ok {
		return nil
	}

	var req pictureValidation.CreateAlbumRequest
	if err := h.ParseRequestData(c, &req); err != nil {
		return h.BadRequest(c, "请求参数错误: "+err.Error(), nil)
	}

	// 设置创建者ID
	req.CreatorID = userID
	albumData := &pictureModel.PictureAlbum{
		ContentBaseModel: &models.ContentBaseModel{},
	}

	// 使用智能拷贝进行字段映射
	if err := utils.SmartCopy(req, albumData); err != nil {
		return h.InternalServerError(c, "字段映射失败: "+err.Error())
	}

	albumID, err := h.albumService.Create(c.Context(), albumData)
	if err != nil {
		return h.InternalServerError(c, "创建专辑失败: "+err.Error())
	}

	return h.Success(c, fiber.Map{"id": albumID, "message": "创建专辑成功"})
}

// UpdateAlbum 更新图片专辑
func (h *PictureAlbumController) UpdateAlbum(c *fiber.Ctx) error {
	// 获取专辑ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	var req pictureValidation.UpdateAlbumRequest
	if err := h.ParseRequestData(c, &req); err != nil {
		return h.BadRequest(c, "请求参数错误: "+err.Error(), nil)
	}
	album, err := h.albumService.GetByID(c.Context(), id, false)
	if err != nil {
		return h.NotFound(c, "专辑不存在: "+err.Error())
	}
	if err := utils.SmartCopy(req, album); err != nil {
		return h.InternalServerError(c, "字段映射失败: "+err.Error())
	}

	if err := h.albumService.UpdateById(c.Context(), id, album); err != nil {
		return h.InternalServerError(c, "更新专辑失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新专辑成功")
}

// DeleteAlbum 删除图片专辑
func (h *PictureAlbumController) DeleteAlbum(c *fiber.Ctx) error {
	// 获取专辑ID
	id, err := h.GetId(c)
	if err != nil {
		return err
	}

	if err := h.albumService.Delete(c.Context(), id); err != nil {
		return h.InternalServerError(c, "删除专辑失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "删除专辑成功")
}

// UpdateAlbumStatus 更新图片专辑状态
func (h *PictureAlbumController) UpdateAlbumStatus(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidation.UpdateAlbumStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	if err := h.albumService.UpdateStatus(c.Context(), req.ID, req.Status); err != nil {
		return h.InternalServerError(c, "更新图片专辑状态失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "更新图片专辑状态成功")
}

// BatchUpdateAlbumStatus 批量更新图片专辑状态
func (h *PictureAlbumController) BatchUpdateAlbumStatus(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidation.BatchUpdateAlbumStatusRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	err := h.albumService.BatchUpdateStatus(c.Context(), req.IDs, req.Status)
	if err != nil {
		return h.InternalServerError(c, "批量删除图片专辑失败: "+err.Error())
	}

	return h.SuccessWithMessage(c, "批量更新图片专辑状态成功")
}

// BatchDeleteAlbum 批量删除图片专辑
func (h *PictureAlbumController) BatchDeleteAlbum(c *fiber.Ctx) error {
	// 解析请求参数
	var req pictureValidation.BatchDeleteAlbumRequest
	if err := validator.ValidateDataWrapper(c, &req); err != nil {
		return h.BadRequest(c, "无效的请求参数: "+err.Error(), nil)
	}

	// 批量删除专辑
	for _, id := range req.IDs {
		if err := h.albumService.Delete(c.Context(), id); err != nil {
			return h.InternalServerError(c, "批量删除图片专辑失败: "+err.Error())
		}
	}

	return h.SuccessWithMessage(c, "批量删除图片专辑成功")
}

// AddPicturesToAlbum 向专辑添加图片
func (h *PictureAlbumController) AddPicturesToAlbum(c *fiber.Ctx) error {
	albumID := c.Params("id")
	if albumID == "" {
		return h.BadRequest(c, "专辑ID不能为空", nil)
	}

	var req struct {
		PictureIDs []string `json:"pictureIds"`
	}

	if err := c.BodyParser(&req); err != nil {
		return h.BadRequest(c, "请求参数错误: "+err.Error(), nil)
	}

	if len(req.PictureIDs) == 0 {
		return h.BadRequest(c, "图片ID列表不能为空", nil)
	}

	// 向专辑添加图片的逻辑
	// 这里假设albumService中有批量添加图片的方法
	// 实际实现可能需要根据服务层接口调整
	for _, pictureID := range req.PictureIDs {
		// 这里需要实现向专辑添加图片的逻辑
		// 或者调用专门的服务方法
		_ = pictureID
	}

	return h.SuccessWithMessage(c, "添加图片成功")
}

// RemovePicturesFromAlbum 从专辑移除图片
func (h *PictureAlbumController) RemovePicturesFromAlbum(c *fiber.Ctx) error {
	albumID := c.Params("id")
	if albumID == "" {
		return h.BadRequest(c, "专辑ID不能为空", nil)
	}

	var req struct {
		PictureIDs []string `json:"pictureIds"`
	}

	if err := c.BodyParser(&req); err != nil {
		return h.BadRequest(c, "请求参数错误: "+err.Error(), nil)
	}

	if len(req.PictureIDs) == 0 {
		return h.BadRequest(c, "图片ID列表不能为空", nil)
	}

	// 从专辑移除图片的逻辑
	// 这里假设albumService中有批量移除图片的方法
	// 实际实现可能需要根据服务层接口调整
	for _, pictureID := range req.PictureIDs {
		// 这里需要实现从专辑移除图片的逻辑
		// 或者调用专门的服务方法
		_ = pictureID
	}

	return h.SuccessWithMessage(c, "移除图片成功")
}
