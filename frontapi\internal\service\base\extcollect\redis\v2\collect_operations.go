package v2

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
)

// CollectOperations 收藏操作处理器
type CollectOperations struct {
	client *RedisClient
}

// NewCollectOperations 创建收藏操作处理器
func NewCollectOperations(client *RedisClient) *CollectOperations {
	return &CollectOperations{
		client: client,
	}
}

// Collect 收藏操作
func (c *CollectOperations) Collect(ctx context.Context, userID, itemID, itemType string) error {
	pipe := c.client.Pipeline()

	// 生成所需的键
	collectKey := c.client.collectKey(itemType, itemID)
	userCollectsKey := c.client.userCollectsKey(userID, itemType)
	itemCollectorsKey := c.client.itemCollectorsKey(itemType, itemID)
	countKey := c.client.countKey(itemType, itemID)
	statsKey := c.client.statsKey(itemType, itemID)
	historyKey := c.client.historyKey(userID, itemType)
	userStatsKey := c.client.userStatsKey(userID)

	now := time.Now()
	config := c.client.Config()

	// 使用 Set 存储收藏关系
	pipe.SAdd(ctx, collectKey, userID)
	pipe.Expire(ctx, collectKey, config.CollectTTL)

	// 用户收藏列表
	pipe.SAdd(ctx, userCollectsKey, itemID)
	pipe.Expire(ctx, userCollectsKey, config.CollectTTL)

	// 物品收藏用户列表
	pipe.SAdd(ctx, itemCollectorsKey, userID)
	pipe.Expire(ctx, itemCollectorsKey, config.CollectTTL)

	// 收藏计数
	pipe.Incr(ctx, countKey)
	pipe.Expire(ctx, countKey, config.CountTTL)

	// 统计信息
	pipe.HSet(ctx, statsKey, map[string]interface{}{
		"last_collect_time": now.Unix(),
	})
	pipe.HIncrBy(ctx, statsKey, "total_collects", 1)
	pipe.Expire(ctx, statsKey, config.DefaultTTL)

	// 用户统计信息
	pipe.HIncrBy(ctx, userStatsKey, "total_collects", 1)
	pipe.HSet(ctx, userStatsKey, "last_collect_time", now.Unix())
	pipe.Expire(ctx, userStatsKey, config.DefaultTTL)

	// 历史记录
	collectRecord := map[string]interface{}{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
		"action":    "collect",
		"timestamp": now.Unix(),
	}
	pipe.XAdd(ctx, &redis.XAddArgs{
		Stream: historyKey,
		Values: collectRecord,
		MaxLen: config.MaxHistoryLen,
		Approx: true,
	})
	pipe.Expire(ctx, historyKey, config.HistoryTTL)

	_, err := pipe.Exec(ctx)
	if err != nil {
		c.client.UpdateStats(0, 0, 1)
		return fmt.Errorf("收藏操作失败: %w", err)
	}

	c.client.UpdateStats(1, 0, 0)
	return nil
}

// Uncollect 取消收藏操作
func (c *CollectOperations) Uncollect(ctx context.Context, userID, itemID, itemType string) error {
	pipe := c.client.Pipeline()

	collectKey := c.client.collectKey(itemType, itemID)
	userCollectsKey := c.client.userCollectsKey(userID, itemType)
	itemCollectorsKey := c.client.itemCollectorsKey(itemType, itemID)
	countKey := c.client.countKey(itemType, itemID)
	statsKey := c.client.statsKey(itemType, itemID)
	historyKey := c.client.historyKey(userID, itemType)
	userStatsKey := c.client.userStatsKey(userID)

	now := time.Now()
	config := c.client.Config()

	// 移除收藏关系
	pipe.SRem(ctx, collectKey, userID)
	pipe.SRem(ctx, userCollectsKey, itemID)
	pipe.SRem(ctx, itemCollectorsKey, userID)

	// 减少计数
	pipe.Decr(ctx, countKey)

	// 更新统计信息
	pipe.HSet(ctx, statsKey, "last_uncollect_time", now.Unix())
	pipe.HIncrBy(ctx, statsKey, "total_uncollects", 1)

	// 更新用户统计
	pipe.HIncrBy(ctx, userStatsKey, "total_uncollects", 1)
	pipe.HSet(ctx, userStatsKey, "last_uncollect_time", now.Unix())

	// 记录历史
	uncollectRecord := map[string]interface{}{
		"user_id":   userID,
		"item_id":   itemID,
		"item_type": itemType,
		"action":    "uncollect",
		"timestamp": now.Unix(),
	}
	pipe.XAdd(ctx, &redis.XAddArgs{
		Stream: historyKey,
		Values: uncollectRecord,
		MaxLen: config.MaxHistoryLen,
		Approx: true,
	})

	_, err := pipe.Exec(ctx)
	if err != nil {
		c.client.UpdateStats(0, 0, 1)
		return fmt.Errorf("取消收藏操作失败: %w", err)
	}

	c.client.UpdateStats(1, 0, 0)
	return nil
}

// IsCollected 检查是否已收藏
func (c *CollectOperations) IsCollected(ctx context.Context, userID, itemID, itemType string) (bool, error) {
	collectKey := c.client.collectKey(itemType, itemID)

	result, err := c.client.Client().SIsMember(ctx, collectKey, userID).Result()
	if err != nil {
		if err == redis.Nil {
			c.client.UpdateStats(0, 1, 0)
			return false, nil
		}
		c.client.UpdateStats(0, 0, 1)
		return false, fmt.Errorf("检查收藏状态失败: %w", err)
	}

	if result {
		c.client.UpdateStats(1, 0, 0)
	} else {
		c.client.UpdateStats(0, 1, 0)
	}
	return result, nil
}

// GetCollectCount 获取收藏数量
func (c *CollectOperations) GetCollectCount(ctx context.Context, itemID, itemType string) (int64, error) {
	countKey := c.client.countKey(itemType, itemID)

	result, err := c.client.Client().Get(ctx, countKey).Int64()
	if err != nil {
		if err == redis.Nil {
			c.client.UpdateStats(0, 1, 0)
			return 0, nil
		}
		c.client.UpdateStats(0, 0, 1)
		return 0, fmt.Errorf("获取收藏数量失败: %w", err)
	}

	c.client.UpdateStats(1, 0, 0)
	return result, nil
}
