/**
 * 社交服务
 * 处理社交相关的业务逻辑，包括关注、动态、通知等
 */

import { ref, computed } from 'vue'
import type { User, Post, Notification, Message, UserRelation } from '@/shared/types'
import { apiRequest } from '@/core/utils/request'
import { eventBus } from '@/core/utils/eventBus'

export interface SocialService {
  // 关注关系
  followUser(userId: string): Promise<void>
  unfollowUser(userId: string): Promise<void>
  getFollowers(userId: string, page?: number): Promise<User[]>
  getFollowing(userId: string, page?: number): Promise<User[]>
  getMutualFollows(userId: string): Promise<User[]>
  
  // 动态相关
  createPost(content: string, media?: File[]): Promise<Post>
  deletePost(postId: string): Promise<void>
  likePost(postId: string): Promise<void>
  unlikePost(postId: string): Promise<void>
  sharePost(postId: string, comment?: string): Promise<void>
  getTimeline(page?: number): Promise<Post[]>
  getUserPosts(userId: string, page?: number): Promise<Post[]>
  
  // 通知系统
  getNotifications(page?: number): Promise<Notification[]>
  markNotificationAsRead(notificationId: string): Promise<void>
  markAllNotificationsAsRead(): Promise<void>
  getUnreadNotificationCount(): Promise<number>
  
  // 私信系统
  sendMessage(userId: string, content: string, type?: 'text' | 'image' | 'video'): Promise<Message>
  getConversations(): Promise<Conversation[]>
  getMessages(conversationId: string, page?: number): Promise<Message[]>
  markMessagesAsRead(conversationId: string): Promise<void>
  deleteConversation(conversationId: string): Promise<void>
  
  // 社交推荐
  getRecommendedUsers(): Promise<User[]>
  searchUsers(query: string): Promise<User[]>
  getNearbyUsers(latitude: number, longitude: number): Promise<User[]>
  
  // 群组功能
  createGroup(name: string, description: string, isPrivate: boolean): Promise<Group>
  joinGroup(groupId: string): Promise<void>
  leaveGroup(groupId: string): Promise<void>
  getGroupMembers(groupId: string): Promise<User[]>
  getUserGroups(): Promise<Group[]>
}

interface Conversation {
  id: string
  participants: User[]
  lastMessage: Message
  unreadCount: number
  updatedAt: string
}

interface Group {
  id: string
  name: string
  description: string
  avatar: string
  memberCount: number
  isPrivate: boolean
  isJoined: boolean
  createdAt: string
}

class SocialServiceImpl implements SocialService {
  private notifications = ref<Notification[]>([])
  private unreadCount = ref(0)
  private conversations = ref<Conversation[]>([])
  private timeline = ref<Post[]>([])
  
  // WebSocket 连接用于实时通知
  private ws: WebSocket | null = null
  
  constructor() {
    this.initWebSocket()
  }
  
  private initWebSocket() {
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) return
      
      this.ws = new WebSocket(`ws://localhost:3000/ws?token=${token}`)
      
      this.ws.onopen = () => {
        console.log('WebSocket 连接已建立')
      }
      
      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        this.handleWebSocketMessage(data)
      }
      
      this.ws.onclose = () => {
        console.log('WebSocket 连接已关闭')
        // 重连逻辑
        setTimeout(() => this.initWebSocket(), 5000)
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket 错误:', error)
      }
    } catch (error) {
      console.error('WebSocket 初始化失败:', error)
    }
  }
  
  private handleWebSocketMessage(data: any) {
    switch (data.type) {
      case 'notification':
        this.notifications.value.unshift(data.notification)
        this.unreadCount.value++
        eventBus.emit('newNotification', data.notification)
        break
        
      case 'message':
        this.updateConversation(data.message)
        eventBus.emit('newMessage', data.message)
        break
        
      case 'follow':
        eventBus.emit('newFollower', data.user)
        break
        
      case 'like':
        eventBus.emit('postLiked', data)
        break
    }
  }
  
  async followUser(userId: string): Promise<void> {
    try {
      await apiRequest.post(`/users/${userId}/follow`)
      
      // 发送事件通知
      eventBus.emit('userFollowed', { userId })
    } catch (error) {
      throw new Error('关注用户失败')
    }
  }
  
  async unfollowUser(userId: string): Promise<void> {
    try {
      await apiRequest.delete(`/users/${userId}/follow`)
      
      // 发送事件通知
      eventBus.emit('userUnfollowed', { userId })
    } catch (error) {
      throw new Error('取消关注失败')
    }
  }
  
  async getFollowers(userId: string, page = 1): Promise<User[]> {
    try {
      const response = await apiRequest.get(`/users/${userId}/followers`, {
        params: { page, limit: 20 }
      })
      return response.data
    } catch (error) {
      throw new Error('获取粉丝列表失败')
    }
  }
  
  async getFollowing(userId: string, page = 1): Promise<User[]> {
    try {
      const response = await apiRequest.get(`/users/${userId}/following`, {
        params: { page, limit: 20 }
      })
      return response.data
    } catch (error) {
      throw new Error('获取关注列表失败')
    }
  }
  
  async getMutualFollows(userId: string): Promise<User[]> {
    try {
      const response = await apiRequest.get(`/users/${userId}/mutual-follows`)
      return response.data
    } catch (error) {
      throw new Error('获取共同关注失败')
    }
  }
  
  async createPost(content: string, media: File[] = []): Promise<Post> {
    try {
      const formData = new FormData()
      formData.append('content', content)
      
      // 添加媒体文件
      media.forEach((file, index) => {
        formData.append(`media_${index}`, file)
      })
      
      const response = await apiRequest.post('/posts', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      const newPost = response.data
      
      // 添加到时间线顶部
      this.timeline.value.unshift(newPost)
      
      return newPost
    } catch (error) {
      throw new Error('发布动态失败')
    }
  }
  
  async deletePost(postId: string): Promise<void> {
    try {
      await apiRequest.delete(`/posts/${postId}`)
      
      // 从时间线中移除
      this.timeline.value = this.timeline.value.filter(post => post.id !== postId)
    } catch (error) {
      throw new Error('删除动态失败')
    }
  }
  
  async likePost(postId: string): Promise<void> {
    try {
      await apiRequest.post(`/posts/${postId}/like`)
      
      // 更新本地状态
      this.updatePostInteraction(postId, 'liked', true)
    } catch (error) {
      throw new Error('点赞失败')
    }
  }
  
  async unlikePost(postId: string): Promise<void> {
    try {
      await apiRequest.delete(`/posts/${postId}/like`)
      
      // 更新本地状态
      this.updatePostInteraction(postId, 'liked', false)
    } catch (error) {
      throw new Error('取消点赞失败')
    }
  }
  
  async sharePost(postId: string, comment = ''): Promise<void> {
    try {
      await apiRequest.post(`/posts/${postId}/share`, { comment })
      
      // 更新分享数
      this.updatePostStats(postId, 'shareCount', 1)
    } catch (error) {
      throw new Error('分享失败')
    }
  }
  
  async getTimeline(page = 1): Promise<Post[]> {
    try {
      const response = await apiRequest.get('/posts/timeline', {
        params: { page, limit: 20 }
      })
      
      const posts = response.data
      
      if (page === 1) {
        this.timeline.value = posts
      } else {
        this.timeline.value.push(...posts)
      }
      
      return posts
    } catch (error) {
      throw new Error('获取时间线失败')
    }
  }
  
  async getUserPosts(userId: string, page = 1): Promise<Post[]> {
    try {
      const response = await apiRequest.get(`/users/${userId}/posts`, {
        params: { page, limit: 20 }
      })
      return response.data
    } catch (error) {
      throw new Error('获取用户动态失败')
    }
  }
  
  async getNotifications(page = 1): Promise<Notification[]> {
    try {
      const response = await apiRequest.get('/notifications', {
        params: { page, limit: 20 }
      })
      
      const notifications = response.data
      
      if (page === 1) {
        this.notifications.value = notifications
      } else {
        this.notifications.value.push(...notifications)
      }
      
      return notifications
    } catch (error) {
      throw new Error('获取通知失败')
    }
  }
  
  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await apiRequest.put(`/notifications/${notificationId}/read`)
      
      // 更新本地状态
      const notification = this.notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = true
        this.unreadCount.value = Math.max(0, this.unreadCount.value - 1)
      }
    } catch (error) {
      throw new Error('标记通知已读失败')
    }
  }
  
  async markAllNotificationsAsRead(): Promise<void> {
    try {
      await apiRequest.put('/notifications/read-all')
      
      // 更新本地状态
      this.notifications.value.forEach(n => n.isRead = true)
      this.unreadCount.value = 0
    } catch (error) {
      throw new Error('标记所有通知已读失败')
    }
  }
  
  async getUnreadNotificationCount(): Promise<number> {
    try {
      const response = await apiRequest.get('/notifications/unread-count')
      this.unreadCount.value = response.data.count
      return response.data.count
    } catch (error) {
      throw new Error('获取未读通知数失败')
    }
  }
  
  async sendMessage(userId: string, content: string, type: 'text' | 'image' | 'video' = 'text'): Promise<Message> {
    try {
      const response = await apiRequest.post('/messages', {
        recipientId: userId,
        content,
        type
      })
      
      const message = response.data
      
      // 更新对话列表
      this.updateConversation(message)
      
      return message
    } catch (error) {
      throw new Error('发送消息失败')
    }
  }
  
  async getConversations(): Promise<Conversation[]> {
    try {
      const response = await apiRequest.get('/conversations')
      this.conversations.value = response.data
      return response.data
    } catch (error) {
      throw new Error('获取对话列表失败')
    }
  }
  
  async getMessages(conversationId: string, page = 1): Promise<Message[]> {
    try {
      const response = await apiRequest.get(`/conversations/${conversationId}/messages`, {
        params: { page, limit: 50 }
      })
      return response.data
    } catch (error) {
      throw new Error('获取消息失败')
    }
  }
  
  async markMessagesAsRead(conversationId: string): Promise<void> {
    try {
      await apiRequest.put(`/conversations/${conversationId}/read`)
      
      // 更新本地未读数
      const conversation = this.conversations.value.find(c => c.id === conversationId)
      if (conversation) {
        conversation.unreadCount = 0
      }
    } catch (error) {
      throw new Error('标记消息已读失败')
    }
  }
  
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      await apiRequest.delete(`/conversations/${conversationId}`)
      
      // 从本地列表中移除
      this.conversations.value = this.conversations.value.filter(c => c.id !== conversationId)
    } catch (error) {
      throw new Error('删除对话失败')
    }
  }
  
  async getRecommendedUsers(): Promise<User[]> {
    try {
      const response = await apiRequest.get('/users/recommended')
      return response.data
    } catch (error) {
      throw new Error('获取推荐用户失败')
    }
  }
  
  async searchUsers(query: string): Promise<User[]> {
    try {
      const response = await apiRequest.get('/users/search', {
        params: { q: query }
      })
      return response.data
    } catch (error) {
      throw new Error('搜索用户失败')
    }
  }
  
  async getNearbyUsers(latitude: number, longitude: number): Promise<User[]> {
    try {
      const response = await apiRequest.get('/users/nearby', {
        params: { lat: latitude, lng: longitude }
      })
      return response.data
    } catch (error) {
      throw new Error('获取附近用户失败')
    }
  }
  
  async createGroup(name: string, description: string, isPrivate: boolean): Promise<Group> {
    try {
      const response = await apiRequest.post('/groups', {
        name,
        description,
        isPrivate
      })
      return response.data
    } catch (error) {
      throw new Error('创建群组失败')
    }
  }
  
  async joinGroup(groupId: string): Promise<void> {
    try {
      await apiRequest.post(`/groups/${groupId}/join`)
    } catch (error) {
      throw new Error('加入群组失败')
    }
  }
  
  async leaveGroup(groupId: string): Promise<void> {
    try {
      await apiRequest.post(`/groups/${groupId}/leave`)
    } catch (error) {
      throw new Error('退出群组失败')
    }
  }
  
  async getGroupMembers(groupId: string): Promise<User[]> {
    try {
      const response = await apiRequest.get(`/groups/${groupId}/members`)
      return response.data
    } catch (error) {
      throw new Error('获取群组成员失败')
    }
  }
  
  async getUserGroups(): Promise<Group[]> {
    try {
      const response = await apiRequest.get('/groups/my-groups')
      return response.data
    } catch (error) {
      throw new Error('获取我的群组失败')
    }
  }
  
  private updatePostInteraction(postId: string, field: string, value: boolean) {
    const post = this.timeline.value.find(p => p.id === postId)
    if (post && post.userInteraction) {
      post.userInteraction[field] = value
    }
  }
  
  private updatePostStats(postId: string, field: string, increment: number) {
    const post = this.timeline.value.find(p => p.id === postId)
    if (post && post.stats) {
      post.stats[field] = (post.stats[field] || 0) + increment
    }
  }
  
  private updateConversation(message: Message) {
    const conversationIndex = this.conversations.value.findIndex(
      c => c.id === message.conversationId
    )
    
    if (conversationIndex !== -1) {
      const conversation = this.conversations.value[conversationIndex]
      conversation.lastMessage = message
      conversation.unreadCount++
      conversation.updatedAt = message.createdAt
      
      // 移动到顶部
      this.conversations.value.splice(conversationIndex, 1)
      this.conversations.value.unshift(conversation)
    }
  }
  
  // 清理资源
  destroy() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

// 导出单例实例
export const socialService = new SocialServiceImpl()

// 导出类型
export type { Conversation, Group }