package users

import (
	"gorm.io/gorm"

	"frontapi/internal/models/users"
	"frontapi/internal/repository/base"
)

// UserSocialLinkRepository 用户社交链接数据访问接口
type UserSocialLinkRepository interface {
	base.ExtendedRepository[users.UserSocialLink]
}

// userSocialLinkRepository 用户社交链接数据访问实现
type userSocialLinkRepository struct {
	base.ExtendedRepository[users.UserSocialLink]
}

// NewUserSocialLinkRepository 创建用户社交链接仓库实例
func NewUserSocialLinkRepository(db *gorm.DB) UserSocialLinkRepository {
	return &userSocialLinkRepository{
		ExtendedRepository: base.NewExtendedRepository[users.UserSocialLink](db),
	}
}
